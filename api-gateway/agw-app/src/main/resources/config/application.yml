eureka :
  client :
    healthcheck :
      enabled : true
zuul :
  #close the global hytrix retry
  retryable : false
  host :
    maxTotalConnections : 1000
    maxPerRouteConnections : 30
  ignoredServices: '*'
  #ignoredHeaders :
  #ignored-patterns :
  sensitiveHeaders : <PERSON><PERSON>,<PERSON>-<PERSON><PERSON>,Authorization,x-lemon-secure,X-Forwarded-For
  stripPrefix : false
  routes:
    #服务名：建议 实例名+"-"+功能名
    tst-addUser :
      #api gateway请求路径
      path : /user/addUser
      #服务ID
      serviceId : tst
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    csm-addUser :
      path : /csm/addUser
      serviceId : csm
    agw-login :
      path : /security/login
      signaturedParameters : loginName,loginPwd
      signatured : false
    #  strip-prefix : false

    agw-logout :
      path : /security/logout
      signatured : false
    agw-gesture-login :
      path : /security/login/gesture
      signaturedParameters : loginName,handPwd
      signatured : false
    mkm-get :
      #api gateway请求路径
      path : /mkmTool/get
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    mkm-query :
      #api gateway请求路径
      path : /mkmTool/query
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    mkm-queryFAQList :
      #api gateway请求路径
      path : /mkm/faq/queryFAQList
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
    mkm-consumeSeaCcy :
      #api gateway请求路径
      path : /mkmTool/consumeSeaCcy
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    mkm-consumeCoupon :
      #api gateway请求路径
      path : /mkmTool/consumeCoupon
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    mkm-revoconsume :
      #api gateway请求路径
      path : /mkmTool/revoconsume
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true


    mkm-queryUserMkmTool :
      #api gateway请求路径
      path : /mkmTool/queryUserMkmTool
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    mkm-seaccyGitf :
      #海币转赠接口
      #api gateway请求路径
      path : /mkmTool/seaccyGitf
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    csh-initOrder :
      #api gateway请求路径
      path : /csh/order/cashier
      #服务ID
      serviceId : csh
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    csh-balPayment :
      #api gateway请求路径
      path : /csh/order/bal
      #服务ID
      serviceId : csh
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    csh-ppPayment :
      #api gateway请求路径
      path : /csh/order/pp
      #服务ID
      serviceId : csh
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    csh-qpPayment :
      #api gateway请求路径
      path : /csh/order/qp
      #服务ID
      serviceId : csh
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true


    csh-offlinePayment :
      #api gateway请求路径
      path : /csh/order/offline
      #服务ID
      serviceId : csh
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    csh-bsPayment :
      #api gateway请求路径
      path : /csh/order/backstage
      #服务ID
      serviceId : csh
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      
    csh-orderCashierView :
      #api gateway请求路径
      path : /csh/order/cashier/{orderNo}
      #服务ID
      serviceId : csh
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false
    
    csh-updateOrder :
      #api gateway请求路径
      path : /csh/order/detail/{orderNo}/{orderStatus}
      #服务ID
      serviceId : csh
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false  
      
    CSH-tradeFee:
      #api gateway请求路径
      path : /csh/order/tradeFee
      #服务ID
      serviceId : csh
     #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    CSH-closeOrder:
      #api gateway请求路径
      path : /csh/order/close
      #服务ID
      serviceId : csh
     #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      
    pwm-rechange :
      #api gateway请求路径
      path : /pwm/recharge/order
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true  

    pwm-rechargeHCoupon :
      #api gateway请求路径
      path : /pwm/recharge/order/sea
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false 
      #服务名：充提模块模块-海币充值接口(对外)
    pwm-createHCouponOrderOut:
      #api gateway请求路径
      path : /pwm/recharge/order/sea/out
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      
    pwm-withdraw :
      #api gateway请求路径
      path : /pwm/withdraw/order
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    pwm-offlineApplication :
      #api gateway请求路径
      path : /pwm/recharge/offline/application
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    pwm-createDmWithdrawOrder :
      #api gateway请求路径
      path : /pwm/withdraw/dm/order
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    pwm-offlinePay :
      #api gateway请求路径
      path : /pwm/recharge/offline/pay
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    pwm-addWithdrawCard :
      #api gateway请求路径
      path : /pwm/withdraw/add
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    pwm-delWithdrawCard :
      #api gateway请求路径
      path : /pwm/withdraw/del
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    pwm-withdrawBank :
      #api gateway请求路径
      path : /pwm/withdraw/bank
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    pwm-withdrawBankCard :
      #api gateway请求路径
      path : /pwm/withdraw/card
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    #服务名：建议 实例名+"-"+功能名
    cpm-telFareOrderRegistrat :
      #api gateway请求路径
      path : /cpm/telfareorder/order
      #服务ID
      serviceId : cpm
    #服务名：缴费模块-充流量下单
    cpm-flowDataOrderRegistrat :
      #api gateway请求路径
      path : /cpm/flowdataorder/order
      #服务ID
      serviceId : cpm
    #服务名：缴费模块-自定义金额查询流量
    cpm-autoflowdataquery :
      #api gateway请求路径
      path : /cpm/autoflowdataquery
      #服务ID
      serviceId : cpm
    #服务名：缴费模块-生活缴费下单
    cpm-livingPayOrderRegistrat :
      #api gateway请求路径
      path : /cpm/livingpayorder/order
      #服务ID
      serviceId : cpm
    #服务名：缴费模块-流量列表查询
    cpm-flowDataQueryService :
      #api gateway请求路径
      path : /cpm/flowdataquery
      #服务ID
      serviceId : cpm
    #服务名：缴费模块-手机号段检查
    cpm-mobileNoValidate :
      #api gateway请求路径
      path : /cpm/mobilenovalidate
      #服务ID
      serviceId : cpm
    #服务名：缴费模块-缴费单位列表查询
    cpm-payUnitQuery :
      #api gateway请求路径
      path : /cpm/payunitquery
      #服务ID
      serviceId : cpm
    #服务名：缴费模块-用户账单查询
    cpm-userBillQuery :
      #api gateway请求路径
      path : /cpm/userbillquery
      #服务ID
      serviceId : cpm

    tam-rate:
      #api gateway请求路径
      path: /tam/exchange/rate
      #服务ID
      serviceId: tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      signatured: false

    tam-getDmSkAddrType:
      #api gateway请求路径
      path: /tam/transfer/order/dm/skAddr/type
      #服务ID
      serviceId: tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      signatured: false

    tam-rateList:
      #api gateway请求路径
      path: /tam/exchange/rate/list
      #服务ID
      serviceId: tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      signatured: false

    tam-exchangeOrder:
      #api gateway请求路径
      path: /tam/exchange/order
      #服务ID
      serviceId: tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      signatured: false

    tam-calExchangeFee:
      #api gateway请求路径
      path: /tam/exchange/calfee
      #服务ID
      serviceId: tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      signatured: false

    tam-outUserOrderEncrypt:
      #api gateway请求路径
      path: /tam/transfer/order/user/encrypt/out
      #服务ID
      serviceId: tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      signatured: true

    #服务名：转账模块-查询转账账户信息接口
    tam-userInfo:
      #api gateway请求路径
      path : /tam/transfer/user/{mblNo}
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false
    #服务名：转账模块-历史转账记录接口
    tam-historyTransferOrder:
      #api gateway请求路径
      path : /tam/transfer/history/order
      #服务ID
      serviceId : tam
     #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
   
    #服务名：转账模块-转账到账户下单接口
    tam-createUserOrder:
      #api gateway请求路径
      path : /tam/transfer/order/user
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
    #服务名：转账模块-转账到账户下单接口(对外)
    tam-createOutUserOrder:
      #api gateway请求路径
      path : /tam/transfer/order/user/out
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
    #服务名：转账模块-转账到银行卡下单接口
    tam-createCardOrder:
      #api gateway请求路径
      path : /tam/transfer/order/card
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
    #服务名：转账模块-面对面收款
    tam-createFaceOrder:
      #api gateway请求路径
      path : /tam/transfer/order/face
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    tam-merchantTransferUser :
      #api gateway请求路径
      path : /tam/transfer/order/merchant/out
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

    tam-outDmOrder :
      #api gateway请求路径
      path : /tam/transfer/order/dm/encrypt/out
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    cpi-queryCardsInfo :
      #api gateway请求路径
      path : /cpi/cards/userId
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    cpi-queryCardBin :
      #api gateway请求路径
      path : /cpi/cards/head
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    cpi-verifyCard :
      #api gateway请求路径
      path : /cpi/cards/verification
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    cpi-preBindCard :
      #api gateway请求路径
      path : /cpi/fastpay/pre/cards
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    cpi-queryEffOrgInfo :
      #api gateway请求路径
      path : /cpi/route/result
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    cpi-bindCard :
      #api gateway请求路径
      path : /cpi/fastpay/cards
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    cpi-unBindCard :
      #api gateway请求路径
      path : /cpi/fastpay/cards
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    cpi-wechatNotify :
      #api gateway请求路径
      path : /cpi/wechat/placeOrderNotify
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
    cpi-alipayNotify :
      #api gateway请求路径
      path : /cpi/alipay/placeOrderNotify
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
    cpi-remittanceResult :
      #api gateway请求路径
      path : /cpi/remittance/result/query
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
    cpi-ebankPayPlaceOrder :
      #api gateway请求路径
      path : /cpi/bestpay/placeOrder
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
    cpi-ebankPayRefund :
      #api gateway请求路径
      path : /cpi/bestpay/refund
      #服务ID
      serviceId : cpi
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    # 处理Cregis回调数据
    cpi-cregisCallback:
      #api gateway请求路径
      path: /cpi/cregis/callback
      #服务ID
      serviceId: cpi
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    # Cregis转账签名提交全流程
    cpi-AddressFundFlowTransferAll:
      #api gateway请求路径
      path: /cpi/address/fund-flow/transfer/all
      #服务ID
      serviceId: cpi
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    cpo-queryEffCapOrgInfo :
      #api gateway请求路径
      path : /cpo/route/capOrg
      #服务ID
      serviceId : cpo
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：线上收单模块-订单查询接口
    onr-merchantOrderQuery :
      #api gateway请求路径
      path : /onr/merc/query/order/{orderNo}
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    #服务名：线上收单模块-退款接口
    onr-merchantOrderRefund :
      #api gateway请求路径
      path : /onr/merc/refund/order
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：线上收单模块-商户APP退款接口
    onr-merchantPorRefundOrder :
      #api gateway请求路径
      path : /onr/merc/porrefund/order
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：线上收单模块-商户APP撤单接口
    onr-merchantPorOrderCancel :
      #api gateway请求路径
      path : /onr/merc/porcancle/order
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：线上收单模块-用户付款码配置接口
    onr-usrPayCodeSet :
      #api gateway请求路径
      path : /onr/configure/usr/usr
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：线上收单模块-用户付款码查询接口
    onr-usrPayCodeQry :
      #api gateway请求路径
      path : /onr/configure/usr/query
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    #服务名：线上收单模块-商户被动下单
    onr-merc-order-unactive :
      #api gateway请求路径
      path : /onr/merc/order/unactive
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    #服务名：线上收单模块-商户主动下单
    onr-merc-order-active :
      #api gateway请求路径
      path : /onr/merc/order/active
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    #服务名：线上收单模块-商户下单并支付
    onr-merc-orderpay-order :
      #api gateway请求路径
      path : /onr/onr/merc/orderpay/order
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    #服务名：线上收单模块-用户主动下单
    onr-usr-order-order :
      #api gateway请求路径
      path : /onr/usr/order/order
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

     #服务名：线上收单模块-聚合下单
    onr-usr-order-orderH5 :
      #api gateway请求路径
      path : /onr/usr/order/orderH5
       #服务ID
      serviceId : onr
       #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : false

     #服务名：线上收单模块-聚合下单
    onr-usrThird-order-orderPay :
      #api gateway请求路径
      path : /onr/usrThird/order/orderPay
       #服务ID
      serviceId : onr
       #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

     #服务名：线上收单模块-聚合下单
    onr-merc-order-control :
      #api gateway请求路径
      path : /onr/merc/order/control
       #服务ID
      serviceId : onr
       #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

     #服务名：线上收单模块-第三方商户扫码下单
    onr-merc-order-activeT :
      #api gateway请求路径
      path : /onr/merc/order/activeT
       #服务ID
      serviceId : onr
       #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

    #服务名：理财模块-用户理财功能开启
    inv-configure-usr-open :
      #api gateway请求路径
      path : /inv/configure/usr/open
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    #服务名：线上收单模块-用户理财功能关闭
    inv-configure-usr-close :
      #api gateway请求路径
      path : /inv/configure/usr/close
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    #服务名：理财模块-用户理财资金转入、转出
    inv-usr-transfer-order :
      #api gateway请求路径
      path : /inv/usr/transfer/order
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    #服务名：理财模块-用户理财产品投资明细查询
    inv-query-usr-probal :
      #api gateway请求路径
      path : /inv/usr/probal/{proId}
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    #服务名：理财模块-用户理财产品投资明细查询
    inv-usr-bal :
      #api gateway请求路径
      path : /inv/usr/bal/{userNo}
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false



    #服务名：理财模块-查询现有有效理财产品
    inv-product :
      #api gateway请求路径
      path : /inv/product/{term}
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    #服务名：理财模块-查询预期收益时间
    inv-getInvTm :
      #api gateway请求路径
      path : /inv/getInvTm/{term}
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    #服务名：理财模块-查询产品预期收益时间
    inv-getProInvTm :
      #api gateway请求路径
      path : /inv/getProInvTm/{proId}
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    #服务名：理财模块-查询现有有效理财产品
    inv-configure-productList :
      #api gateway请求路径
      path : /inv/configure/productList
      #服务ID
      serviceId : inv
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      signatured : false

    urm-openuser :
      path : /urm/users
      serviceId : urm
      authenticated : false
      signatured : false

    urm-canceluser :
      path : /urm/users/{userId}
      serviceId : urm

    urm-queryuser :
      path : /urm/users/{userId}
      serviceId : urm
      signatured : false


    urm-queryuserbyloginid :
      path : /urm/users/loginId/{loginId}
      serviceId : urm
      signatured : false

    #服务名：邮箱验证码校验
    urm-emailCodeAuth:
      #api gateway请求路径
      path: /mcc/emailCodeAuth
      #服务ID
      serviceId: urm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：邮箱验证码发送
    urm-emailCodeSend :
      #api gateway请求路径
      path : /mcc/emailCodeSend
      #服务ID
      serviceId : urm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    urm-checkresetpwd :
      path : /urm/authentication/checkresetpwd
      serviceId : urm
      authenticated : false
      signatured : false

    urm-loginpwd-forget :
      path : /urm/loginpwd/forget
      serviceId : urm
      authenticated : false
      signatured : false

    urm-login :
      path : /urm/authentication/login
      serviceId : urm
      authenticated : false
      signatured : false

    urm-checkpaypwd :
      path : /urm/authentication/checkpaypwd
      serviceId : urm

    urm-checkloginpwd :
      path : /urm/authentication/checkloginpwd
      serviceId : urm
      authenticated : false
      signatured : false


    urm-checksafeques :
      path : /urm/authentication/checksafeques
      serviceId : urm

    urm-updateloginpwd :
      path : /urm/loginpwd/update
      serviceId : urm

    urm-resetloginpwd :
      path : /urm/loginpwd/reset
      serviceId : urm
      authenticated : false

    urm-updatepaypwd :
      path : /urm/paypwd/update
      serviceId : urm

    urm-updatepaypwd-sea :
      path : /urm/paypwd/sea/update
      serviceId : urm
      authenticated : false
      signatured : false

    urm-checkpaypwd-sea :
      path : /urm/authentication/sea/checkpaypwd
      serviceId : urm
      authenticated : false
      signatured : false

    urm-resetpaypwd :
      path : /urm/paypwd/reset
      serviceId : urm

    urm-updatepwdsafeinf :
      path : /urm/pwdsafeinf/update
      serviceId : urm

    urm-sendShortMessage :
      path : /urm/sms/sendshortmessage
      serviceId : urm
      authenticated : false

    urm-querysafeques :
      path : /urm/users/safeques/{loginId}
      serviceId : urm
      signatured : false

    urm-completerealname :
      path : /urm/users/realname
      serviceId : urm

    urm-updateloginf :
      path : /urm/users/loginf
      serviceId : urm

    urm-usertype :
      path : /urm/users/usertype/{loginId}
      serviceId : urm
      authenticated : false
      signatured : false

    urm-usertype2 :
      path : /urm/users/usertype
      serviceId : urm
      authenticated : false
      signatured : false


    urm-random :
      path : /urm/random
      serviceId : urm
      authenticated : false
      signatured : false

    urm-addmercopr :
      path : /urm/merc/opr
      serviceId : urm
      authenticated : false
      signatured : false

    urm-modifymercopr :
      path : /urm/merc/opr
      serviceId : urm
      authenticated : false
      signatured : false

    urm-deletemercopr :
      path : /urm/merc/opr
      serviceId : urm
      authenticated : false
      signatured : false

    urm-querymercopr :
      path : /urm/merc/opr/list
      serviceId : urm
      authenticated : false
      signatured : false

    urm-upgradeinformation :
      path : /urm/users/information
      serviceId : urm
      authenticated : false
      signatured : false

    urm-querycprkey :
      path : /urm/cprkey/{userId}
      serviceId : urm
      signatured : false

    urm-mercoprauthority :
      path : /urm/users/authority/{loginId}
      serviceId : urm
      authenticated : false
      signatured : false

    urm-modifymercoprauthority :
      path : /urm/users/authority
      serviceId : urm
      authenticated : false
      signatured : false

    #服务名：手势密码设置(重置)
    urm-setHandPwd :
      path : /urm/users/setHandPwd
      serviceId : urm
      authenticated : false
      signatured : false

    #服务名：手势密码校验
    urm-checkHandPwd :
      path : /urm/authentication/checkHandPwd
      serviceId : urm
      authenticated : true
      signatured : true

    urm-merchant-affiliate :
      path : /urm/merchant/affiliate/{userId}
      serviceId : urm
      authenticated : true
      signatured : true

    urm-getMercListInfo :
      path : /urm/merchant/info
      serviceId : urm
      authenticated : true
      signatured : true

    #服务名：提交kyb认证信息
    urm-submitKybInfo :
      path : /urm/submitKybInfo
      serviceId : urm
      authenticated: true
      signatured: false

    #服务名：提交kyb认证信息
    urm-queryKybInfo:
      path: /urm/queryKybInfo
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理查询团队用户列表
    urm-queryTeamOprList:
      path: /urm/team/opr/list
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理修改用户信息
    urm-modifyTeamOpr:
      path: /urm/team/opr/modify
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理删除用户
    urm-deleteTeamOpr:
      path: /urm/team/opr/delete
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理搜索用户
    urm-searchTeamOpr:
      path: /urm/team/opr/search
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理添加用户
    urm-addTeamOpr:
      path: /urm/team/opr/add
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理查询已有角色配置
    urm-queryTeamRoleList:
      path: /urm/team/role/list
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理修改角色类型权限
    urm-modifyTeamRole:
      path: /urm/team/role/modify
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理删除角色类型
    urm-deleteTeamRole:
      path: /urm/team/role/delete
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：权限管理新增角色类型
    urm-addTeamRole:
      path: /urm/team/role/add
      serviceId: urm
      authenticated: true
      signatured: false

    #服务名：用户基本信息-查询用户基本信息
    urm-queryUserBasicInf:
      path: /urm//user/log
      serviceId: urm
      authenticated: true
      signatured: false
    #服务名：用户基本信息-修改用户登录密码
    urm-modifyUserLoginPwd:
      path: /urm/users/login/pwd
      serviceId: urm
      authenticated: true
      signatured: false
    #服务名：用户基本信息-修改用户邮箱
    urm-modifyUserEmail:
      path: /urm/users/email
      serviceId: urm
      authenticated: true
      signatured: false
    #服务名：记录用户登录信息
    urm-saveUserLog:
      path: /urm/user/log/save
      serviceId: urm
      authenticated: false
      signatured: false

    #服务名：清分结算-结算信息查询
    csm-settleInformation :
      #api gateway请求路径
      path : /csm/settle/information
      #服务ID
      serviceId : csm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
      
    #服务名：清分结算-结算申请
    csm-settleaApply :
      #api gateway请求路径
      path : /csm/settle/apply
      #服务ID
      serviceId : csm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：清分结算-结算订单列表
    csm-settleOrderList :
      #api gateway请求路径
      path : /csm/settle/order/list
      #服务ID
      serviceId : csm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：清分结算-结算订单详情
    csm-settleOrderDetail :
      #api gateway请求路径
      path : /csm/settle/order/detail
      #服务ID
      serviceId : csm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：清分结算-结算订单列表protal
    csm-settleOrderList2 :
      #api gateway请求路径
      path : /csm/portal/settle/order/list
      #服务ID
      serviceId : csm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：公共服务-短信验证码
    cmm-smsCodeSend :
      #api gateway请求路径
      path : /sms/code
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    cmm-params-group :
      path : /cmm-service/params/group/{parmNm}
      serviceId : cmm
      authenticated : false
      signatured : false


    #服务名：公共服务-短信验证码
    cmm-clientpre :
      #api gateway请求路径
      path : /cmm/message/push/clientpre
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

      
    #服务名：公共服务-短信验证码校验
    cmm-smsCodeCheck :
      #api gateway请求路径
      path : /sms/code/check
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true





    #服务名：公共服务-付款码生成
    cmm-paymentCodeGenerate :
      #api gateway请求路径
      path : /qrcode/payment
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
      
    #服务名：公共服务-二维码生成
    cmm-qrCodeGenerate :
      #api gateway请求路径
      path : /qrcode/generate
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：公共服务-二维码解析
    cmm-qrCodeResolve :
      #api gateway请求路径
      path : /qrcode/resolve
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：公共服务-聚合支付二维码解析
    cmm-qrAgCodeResolve :
      #api gateway请求路径
      path : /qrcode/aggregate/resolve
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
      
    #服务名：公共服务-手机号合法性行校验
    cmm-phoneNumberCheck :
      #api gateway请求路径
      path : /cmm/phonenumber/check
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：公共服务-图片上传
    cmm-imageUpload:
      #api gateway请求路径
      path: /cmm/upload/imageUpload
      #服务ID
      serviceId: cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：公共服务-多图片上传
    cmm-imagesUpload:
      #api gateway请求路径
      path: /cmm/upload/imagesUpload
      #服务ID
      serviceId: cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：公共服务-公告列表
    cmm-noticeList :
      #api gateway请求路径
      path : /cmm/notice/list
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
      
    #服务名：公共服务-Banner列表
    cmm-bannerList :
      #api gateway请求路径
      path : /cmm/banner/list
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
      
    #服务名：公共服务-活动列表
    cmm-campaignList :
      #api gateway请求路径
      path : /cmm/campaign/list
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false
      
    #服务名：公共服务-消息列表
    cmm-messageList :
      #api gateway请求路径
      path : /cmm/message/list
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：公共服务-消息确认
    cmm-messageAcknowledge :
      #api gateway请求路径
      path : /cmm/message/acknowledge
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：公共服务-消息推送Id设置
    cmm-messageClient :
      #api gateway请求路径
      path : /cmm/message/push/client
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
      
    #服务名：公共服务-系统密钥
    cmm-systemKey :
      #api gateway请求路径
      path : /cmm/system/key
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    #服务名：公共服务-授权
    cmm-grant :
      #api gateway请求路径
      path : /cmm/grant
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    #服务名：公共服务-查询app信息
    cmm-appvnoinfo :
      #api gateway请求路径
      path : /cmm/appvnoinfo
      #服务ID
      serviceId : cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    #服务名：公共服务-获取金融新闻
    cmm-getNews :
      #api gateway请求路径
      path : /cmm/getNews
      #服务ID
      serviceId: cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：公共服务-获取公共常量参数列表
    cmm-getAllParams :
      #api gateway请求路径
      path: /cmm-service/params
      #服务ID
      serviceId: cmm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryUserBills :
      #api gateway请求路径
      path : /bil/user/bill/all
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryBillInfo :
      #api gateway请求路径
      path : /bil/user/bill
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryUserAccount :
      #api gateway请求路径
      path : /bil/user/act
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    bil-actBalByCcy:
    #api gateway请求路径
      path: /bil/user/actBalByCcy
    #服务ID
      serviceId: bil
    #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
    #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryMercBills :
      #api gateway请求路径
      path : /bil/merc/bill/all
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryMercBillsList :
      #api gateway请求路径
      path : /bil/merc/bill/list
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryThirdMercDetails :
      #api gateway请求路径
      path : /bil/merc/bill/third
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryMercCsmDetails :
      #api gateway请求路径
      path : /bil/merc/detail
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryMercDetails :
      #api gateway请求路径
      path : /bil/merc/bill
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    bil-queryMercAccount :
      #api gateway请求路径
      path : /bil/merc/act
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    bil-mercCsmDownload :
      #api gateway请求路径
      path : /bil/export/chkdetail
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    bil-mercBillUpList :
      #api gateway请求路径
      path : /bil/merc/bill/up/list
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    # g获取用户数币账户收款/充值记录列表
    bil-getDmAccountReceiptsList :
      #api gateway请求路径
      path : /bil/user/dm/receipts/list
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false



    pwm-queryRate :
      #api gateway请求路径
      path : /pwm/withdraw/rate
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true

    bil-inside-queryUserBills :
      #api gateway请求路径
      path : /bil/inside/bill/all
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : false

    bil-getBillList:
      #查询用户流水
      path: /bil/user/getBillList
      #服务ID
      serviceId: bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      signatured : false

    #服务名：计费服务-商户服务费计算
    tfm-merchanFeeCalculate :
      #api gateway请求路径
      path : /tfm/merchant/fee/calculate
      #服务ID
      serviceId : tfm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    #服务名：计费服务-交易费率查询
    tfm-queryTradeRate :
      #api gateway请求路径
      path : /tfm/rate
      #服务ID
      serviceId : tfm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true
    tfm-tradeFeeCaculate:
      #api 交易手续费预算
      path: /tfm/fee/calculate
      #服务ID
      serviceId: tfm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-计算用户净值
    acm-calNetWorth:
      #api gateway请求路径
      path: /acm/account/networth
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-获取用户币种账户列表
    acm-queryUserAccountList:
        #api gateway请求路径
        path: /acm/account/list
        #服务ID
        serviceId: acm
        #是否需要认证，默认为true；如果不需要认证，请主动设置false
        authenticated: true
        #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
        signatured: false

    #服务名：账户服务-获取用户法币账户详情
    acm-queryFmAccountDetail:
      #api gateway请求路径
      path: /acm/account/fm/{acNo}
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false


    #服务名：账户服务-获取用户数币账户列表
    acm-getAcBal:
      #api gateway请求路径
      path: /acm/account/acbal
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-开立法币账户
    acm-openFmAccount:
      #api gateway请求路径
      path: /acm/account/fm/open
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-获取用户数币账户列表
    acm-queryUserDmAccountList:
      #api gateway请求路径
      path: /acm/account/dm/list
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    acm-queryUserFmAccountList:
      #api gateway请求路径
      path: /acm/account/fm/list
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-查看数币账户详情
    acm-queryDmAccountDetail:
      #api gateway请求路径
      path: /acm/account/dm/{acNo}
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-获取平台支持币种列表
    acm-getCcyList:
      #api gateway请求路径
      path: /acm/account/ccy/list
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-开立数币账户
    acm-openDmAccount:
      #api gateway请求路径
      path: /acm/account/dm/open
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-获取当前用户收款/充值数币账户列表
    acm-getDmReceiptAcc:
      #api gateway请求路径
      path: /acm/account/dm/receipt/accounts
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-收款二维码
    acm-getDmPaymentQrcode:
      #api gateway请求路径
      path: /acm/account/dm/qrcode/payment
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：账户服务-获取用户所有数币账户信息
    acm-getDmAccountInfo:
      #api gateway请求路径
      path: /acm/account/dm/all/info
      #服务ID
      serviceId: acm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated: true
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured: false

    #服务名：演示服务-用户鉴权
    cmm1-userAuth :
      path : /cmm1/userAuth
      serviceId : cmm1
      authenticated : false
      signatured : false
    #服务名：演示服务-余额查询
    cmm1-queryHuaweiBal :
      path : /cmm1/huaweiBal
      serviceId : cmm1
      authenticated : true
      signatured : false
    #服务名：演示服务-余额支付
    csh1-payByHuaweiBal :
      path : /csh1/huawei/bal
      serviceId : csh1
      authenticated : true
      signatured : true
    #服务名：演示服务-接口回调
    ghi-callbackByHuawei :
      path : /ghi/recvPack/{originatorConversationID}
      serviceId : ghi
      authenticated : false
      signatured : false
    urm1-openuser :
      path : /urm1/users
      serviceId : urm
      authenticated : false

    #服务名：xxxx
    upg-v1-channel-createOrder :
      #api gateway请求路径
      path : /upg/v1/channel/createOrder
      #服务ID
      serviceId : upg
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：xxxx
    upg-v1-channel-preOrder :
      #api gateway请求路径
      path : /upg/v1/channel/preOrder
      #服务ID
      serviceId : upg
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：xxxx
    upg-v1-channel-queryOrder :
      #api gateway请求路径
      path : /upg/v1/channel/queryOrder
      #服务ID
      serviceId : upg
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：xxxx
    upg-v1-channel-cancelOrder :
      #api gateway请求路径
      path : /upg/v1/channel/cancelOrder
      #服务ID
      serviceId : upg
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：xxxx
    upg-v1-channel-refundOrder :
      #api gateway请求路径
      path : /upg/v1/channel/refundOrder
      #服务ID
      serviceId : upg
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：xxxx
    upg-v1-channel-prepareWeChatOrder :
      #api gateway请求路径
      path : /upg/v1/channel/prepareWeChatOrder
      #服务ID
      serviceId : upg
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : false

    #服务名：xxxx
    upg-v1-channel-downloadbill :
      #api gateway请求路径
      path : /upg/v1/channel/downloadbill
      #服务ID
      serviceId : upg
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #服务名：xxxx
    upg-v1-manage-addMeric :
      #api gateway请求路径
      path : /upg/v1/manage/addMeric
      #服务ID
      serviceId : upg
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

