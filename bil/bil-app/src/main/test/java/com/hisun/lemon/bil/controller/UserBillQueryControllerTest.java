package com.hisun.lemon.bil.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.hisun.lemon.bil.dto.UserBillOrderDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;


/**
 * <AUTHOR>
 * @date 2017/7/18
 * @time 17:50
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class UserBillQueryControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    /**
     * 测试用户账户信息查询
     * @throws Exception
     */
    @Test
    public void testQueryUserBills() throws Exception {

        UserBillOrderDTO userBillOrderDTO = new UserBillOrderDTO();
        //userBillOrderDTO.setTxTmBegin("2017-07-19 09:09:18");
        //userBillOrderDTO.setTxTmEnd("2017-07-19 09:30:18");
        userBillOrderDTO.setTxType("01");
        userBillOrderDTO.setPageSize(1);
        userBillOrderDTO.setPageNo(1);
        GenericDTO<UserBillOrderDTO> genericUserBillOrderDTO = new GenericDTO();
        genericUserBillOrderDTO.setBody(userBillOrderDTO);
        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericUserBillOrderDTO);
        RequestBuilder request = MockMvcRequestBuilders.get("/bil/user/bill/all")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        Assert.assertTrue("正确", status == 200);
        Assert.assertFalse("错误", status != 200);
    }

    /**
     * 测试用户账户账单交易明细
     * @throws Exception
     */
    @Test
    public void testQueryBillInfo() throws Exception {

        /*UserBillOrderDTO userBillOrderDTO = new UserBillOrderDTO();
        userBillOrderDTO.setOrderNo("2017071900000000001132");
        GenericDTO<UserBillOrderDTO> genericUserBillOrderDTO = new GenericDTO();
        genericUserBillOrderDTO.setBody(userBillOrderDTO);*/
        String orderNo= "2017071900000000001132";
        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(orderNo);
        RequestBuilder request = MockMvcRequestBuilders.get("/bil/user/bill/"+orderNo)
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        Assert.assertTrue("正确", status == 200);
        Assert.assertFalse("错误", status != 200);
    }

    /**
     * 测试用户账户信息
     * @throws Exception
     */
    @Test
    public void testQueryUserAccount() throws Exception {

        UserBillOrderDTO userBillOrderDTO = new UserBillOrderDTO();
        userBillOrderDTO.setUserId("userId1");
        GenericDTO<UserBillOrderDTO> genericUserBillOrderDTO = new GenericDTO();
        genericUserBillOrderDTO.setBody(userBillOrderDTO);
        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericUserBillOrderDTO);
        RequestBuilder request = MockMvcRequestBuilders.get("/bil/user/act")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        Assert.assertTrue("正确", status == 200);
        Assert.assertFalse("错误", status != 200);
    }
}
