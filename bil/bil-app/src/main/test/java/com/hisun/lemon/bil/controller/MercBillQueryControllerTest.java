package com.hisun.lemon.bil.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.hisun.lemon.bil.dto.MercBillOrderDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 * @date 2017/7/21
 * @time 11:47
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class MercBillQueryControllerTest {
    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    /**
     * 测试商户账户信息查询
     * @throws Exception
     */
    @Test
    public void testQueryMercBills() throws Exception {

        MercBillOrderDTO mercBillOrderDTO = new MercBillOrderDTO();
        //mercBillOrderDTO.setTxTmBegin("2017-07-21 15:41:01");
        //mercBillOrderDTO.setTxTmEnd("2017-07-21 15:50:41");
        mercBillOrderDTO.setOrderStatus("03");
        mercBillOrderDTO.setPageSize(1);
        mercBillOrderDTO.setPageNo(1);
        GenericDTO<MercBillOrderDTO> genericMercBillOrderDTO = new GenericDTO();
        genericMercBillOrderDTO.setBody(mercBillOrderDTO);
        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericMercBillOrderDTO);
        RequestBuilder request = MockMvcRequestBuilders.get("/bil/merc/bill/all")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        Assert.assertTrue("正确", status == 200);
        Assert.assertFalse("错误", status != 200);
    }

    /**
     * 测试商户账户订单交易详情
     * @throws Exception
     */
    @Test
    public void testQueryMercDetails() throws Exception {

        MercBillOrderDTO mercBillOrderDTO = new MercBillOrderDTO();
        //mercBillOrderDTO.setOrderNo("2017072100000000000213");
        GenericDTO<MercBillOrderDTO> genericMercBillOrderDTO = new GenericDTO();
        genericMercBillOrderDTO.setBody(mercBillOrderDTO);
        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericMercBillOrderDTO);
        RequestBuilder request = MockMvcRequestBuilders.get("/bil/merc/bill")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        Assert.assertTrue("正确", status == 200);
        Assert.assertFalse("错误", status != 200);
    }

    /**
     * 测试商户账户信息
     * @throws Exception
     */
    @Test
    public void testQueryMercAccount() throws Exception {

        MercBillOrderDTO mercBillOrderDTO = new MercBillOrderDTO();
        mercBillOrderDTO.setMercId("mercId1");
        GenericDTO<MercBillOrderDTO> genericMercBillOrderDTO = new GenericDTO();
        genericMercBillOrderDTO.setBody(mercBillOrderDTO);
        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericMercBillOrderDTO);
        RequestBuilder request = MockMvcRequestBuilders.get("/bil/merc/act")
                .contentType(MediaType.APPLICATION_JSON_UTF8);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        Assert.assertTrue("正确", status == 200);
        Assert.assertFalse("错误", status != 200);
    }
}
