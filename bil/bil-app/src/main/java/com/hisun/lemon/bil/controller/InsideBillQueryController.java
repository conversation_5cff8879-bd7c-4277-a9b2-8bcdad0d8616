package com.hisun.lemon.bil.controller;

import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.bil.service.IInsideBillOrderService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.LemonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2017/7/7
 * @time 17:20
 */
@Api(value = "用户账单查询")
@RestController
@RequestMapping(value= "/bil/inside")
public class InsideBillQueryController extends BaseController {

    @Resource
    private IInsideBillOrderService userBillOrderService;

    @ApiOperation(value = "用户账单查询", notes = "查询当前用户账户全部交易信息")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "用户账单查询结果")
    @PostMapping(value = "/bill/all")
    public GenericRspDTO<UserBillOrderListDTO> queryUserBills(@Validated @RequestBody GenericDTO<UserBillOrderDTO> genericDTO) {

        //还没过网关，先写定
        //userBillOrderDTO.setUserId("1");
        UserBillOrderDTO userBillOrderDTO = genericDTO.getBody();
//        userBillOrderDTO.setUserId(LemonUtils.getUserId());
        UserBillOrderListDTO userBillOrderListDTO = userBillOrderService.queryUserBills(userBillOrderDTO);
        return GenericRspDTO.newSuccessInstance(userBillOrderListDTO);
    }

    @ApiOperation(value = "用户账户交易明细查询", notes = "查询用户账户单条交易记录明细")
    @ApiResponse(code = 200, message = "单条交易明细查询结果")
    @GetMapping(value = "/bill")
    public GenericRspDTO<UserBillOrderListDTO> queryBillInfo(@Validated UserBillInfoDTO userBillInfoDTO) {

        UserBillOrderListDTO userBillOrderListDTO = userBillOrderService.queryBillInfo(userBillInfoDTO);
        return GenericRspDTO.newSuccessInstance(userBillOrderListDTO);
    }

    @ApiOperation(value = "用户账户信息查询", notes = "查询当前用户账户信息")
    @ApiResponse(code = 200, message = "用户账户查询结果")
    @GetMapping(value= "/act")
    public GenericRspDTO<UserAccountRspDTO> queryUserAccount(@Validated UserAccountDTO userAccountDTO) {

        userAccountDTO.setUserId(LemonUtils.getUserId());
        UserAccountRspDTO userAccountRspDTO = userBillOrderService.queryUserAccount(userAccountDTO);
        return GenericRspDTO.newSuccessInstance(userAccountRspDTO);
    }
}
