package com.hisun.lemon.bil.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.*;
import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dao.IUserBillOrderDao;
import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.bil.dto.UserAccountDTO;
import com.hisun.lemon.bil.entity.UserBillOrderDO;
import com.hisun.lemon.bil.service.IUserBillOrderService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.client.CardClient;
import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.inv.client.QueryInformationClient;
import com.hisun.lemon.mkm.client.MarketActivityClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2017/7/8
 * @time 16:45
 */
@Service
public class UserBillOrderServiceImpl extends BaseService implements IUserBillOrderService {
    private final static Logger logger = LoggerFactory.getLogger(UserBillOrderServiceImpl.class);

    @Resource
    private IUserBillOrderDao userBillOrderDao;

    @Resource
    private MarketActivityClient marketActivityClient;

    @Resource
    private AccountManagementClient accountManagementClient;

    @Resource
    private QueryInformationClient queryInformationClient;

    @Resource
    private CardClient cardClient;

    /**
     * 查询用户个人账单信息
     *
     * @param userBillOrderDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
    public UserBillOrderListDTO queryUserBills(UserBillOrderDTO userBillOrderDTO) {
        //如果交易起始时间不给空，转为之间类型
        if (JudgeUtils.isNotNull(userBillOrderDTO.getTxTmBeginStr()) && !JudgeUtils.equals("", userBillOrderDTO.getTxTmBeginStr())) {
            userBillOrderDTO.setTxTmBegin(DateTimeUtils.parseLocalDateTime(userBillOrderDTO.getTxTmBeginStr()));
        }
        //如果交易结束 时间不给空，转为之间类型
        if (JudgeUtils.isNotNull(userBillOrderDTO.getTxTmEndStr()) && !JudgeUtils.equals("", userBillOrderDTO.getTxTmEndStr())) {
            userBillOrderDTO.setTxTmEnd(DateTimeUtils.parseLocalDateTime(userBillOrderDTO.getTxTmEndStr()));
        }
        //切割交易类型txType，装list
        String txType = userBillOrderDTO.getTxType();
        if (JudgeUtils.isNotEmpty(txType)) {
            List<String> list = Arrays.asList(txType.split(","));
            if (list.size() != 0) {
                userBillOrderDTO.setTxTypeList(list);
            }
        }
        //根据用户级别，查询不同数据,用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家
        String userLevel = userBillOrderDTO.getUserLevel();
        List<UserBillOrderDO> userBillOrderListDO = null;
        PageInfo pageInfo = null;
        if (JudgeUtils.equals(userLevel, "0") || JudgeUtils.equals(userLevel, "1")) {
            //根据条件查询用户账单信息
            //userBillOrderListDO = PageUtils.pageQuery(userBillOrderDTO.getPageNo(), userBillOrderDTO.getPageSize(), () -> userBillOrderDao.queryUserBillOrder(userBillOrderDTO));
            pageInfo = PageUtils.pageQueryWithCount(userBillOrderDTO.getPageNo(), userBillOrderDTO.getPageSize(), () -> userBillOrderDao.queryUserBillOrder(userBillOrderDTO));
        }
        if (JudgeUtils.equals(userLevel, "2") || JudgeUtils.equals(userLevel, "3")) {
            //根据条件查询用户账单信息
            //userBillOrderListDO = PageUtils.pageQuery(userBillOrderDTO.getPageNo(), userBillOrderDTO.getPageSize(), () -> userBillOrderDao.queryUserBillOrder2(userBillOrderDTO));
            pageInfo = PageUtils.pageQueryWithCount(userBillOrderDTO.getPageNo(), userBillOrderDTO.getPageSize(), () -> userBillOrderDao.queryUserBillOrder2(userBillOrderDTO));
        }
        //判断是否有查到数据
        /*if (JudgeUtils.isNull(userBillOrderListDO) || userBillOrderListDO.size() <= 0){
            LemonException.throwBusinessException("BIL20001");
        }*/
        if (JudgeUtils.isNotNull(pageInfo)) {
            userBillOrderListDO = pageInfo.getList();
        }
        //DO集合转为DTO
        List<UserBillOrderListDTO.UserBillOrder> userBillOrders = new ArrayList<>();
        if (JudgeUtils.isNotNull(userBillOrderListDO)) {
            for (UserBillOrderDO userBillOrderDO : userBillOrderListDO) {
                UserBillOrderListDTO.UserBillOrder userBillOrder = new UserBillOrderListDTO.UserBillOrder();
                BeanUtils.copyProperties(userBillOrder, userBillOrderDO);
                userBillOrders.add(userBillOrder);
            }
        }
        UserBillOrderListDTO userBillOrderListDTO = new UserBillOrderListDTO();
        userBillOrderListDTO.setList(userBillOrders);
        userBillOrderListDTO.setTotalNumber(pageInfo.getTotal());
        return userBillOrderListDTO;
    }

    /**
     * 查询单条用户账单交易明细
     *
     * @param userBillInfoDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
    public UserBillOrderListDTO queryBillInfo(UserBillInfoDTO userBillInfoDTO) {

        String orderNo = userBillInfoDTO.getOrderNo();
        String userId = LemonUtils.getUserId();
        //根据账单编号查询用户账单交易明细信息
        UserBillOrderDO userBillOrderDO = userBillOrderDao.queryBillInfo(orderNo, userId);
        //判断是否有查到数据
        if (JudgeUtils.isNull(userBillOrderDO)) {
            LemonException.throwBusinessException("BIL20002");
        }
        //数据对象集合转为传输对象
        List<UserBillOrderListDTO.UserBillOrder> userBillOrders = new ArrayList<>();
        UserBillOrderListDTO.UserBillOrder userBillOrder = new UserBillOrderListDTO.UserBillOrder();
        BeanUtils.copyProperties(userBillOrder, userBillOrderDO);
        userBillOrders.add(userBillOrder);
        UserBillOrderListDTO userBillOrderListDTO = new UserBillOrderListDTO();
        userBillOrderListDTO.setList(userBillOrders);
        return userBillOrderListDTO;
    }

    /**
     * 查询用户账户信息
     *
     * @param userAccountDTO1
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
    public UserAccountRspDTO queryUserAccount(UserAccountDTO userAccountDTO1) {

        GenericRspDTO genericDTO = null;
        String userId = userAccountDTO1.getUserId();
        com.hisun.lemon.acm.dto.UserAccountDTO userAccountDTO = new com.hisun.lemon.acm.dto.UserAccountDTO();
        //初始化返回DTO
        UserAccountRspDTO userAccountRspDTO = new UserAccountRspDTO();
        userAccountDTO.setUserId(userId);

        //调用账户接口，查询账户余额
        genericDTO = accountManagementClient.queryAcBal(userAccountDTO);
        List<QueryAcBalRspDTO> queryAcBalRspDTO = (List<QueryAcBalRspDTO>) genericDTO.getBody();
        if (JudgeUtils.isNull(queryAcBalRspDTO)) {
            LemonException.throwBusinessException("BIL20012");
        }
        //判断资金类型为现金，则填充账户余额
        for (QueryAcBalRspDTO acBalRspDTO : queryAcBalRspDTO) {
            if (JudgeUtils.equals(CapTypEnum.CAP_TYP_CASH.getCapTyp(), acBalRspDTO.getCapTyp())) {
                userAccountRspDTO.setCashBalAmt(acBalRspDTO.getAcCurBal());
            }
        }

        //调用营销接口，查询海币余额
//        QueryUserMkmToolReqDTO queryUserMkmToolReqDTO = new QueryUserMkmToolReqDTO();
//        queryUserMkmToolReqDTO.setUserId(userId);
//        queryUserMkmToolReqDTO.setMkTool("02");
//        genericDTO.setBody(queryUserMkmToolReqDTO);
//        genericDTO = marketActivityClient.queryUserMkmTool(genericDTO);
//        QueryUserMkmToolRspDTO queryUserMkmToolRspDTO = (QueryUserMkmToolRspDTO) genericDTO.getBody();
//        if(JudgeUtils.isNull(queryUserMkmToolRspDTO) || JudgeUtils.isNull(queryUserMkmToolRspDTO.getSeaCcyDetal())){
//            userAccountRspDTO.setCouponAmt(0);
//        }else {
//            userAccountRspDTO.setCouponAmt(queryUserMkmToolRspDTO.getSeaCcyDetal().getCount());
//        }
//
//        //调用理财接口，查询理财余额
//        genericDTO = queryInformationClient.usrBalQuery(userId);
//        InvUserInfoDTO invUserInfoDTO = (InvUserInfoDTO) genericDTO.getBody();
//        if(JudgeUtils.isNull(invUserInfoDTO)){
//            userAccountRspDTO.setInvBalAmt(BigDecimal.ZERO);
//        }else {
//            userAccountRspDTO.setInvBalAmt(invUserInfoDTO.getTotalCurrentAmt());
//        }

        //查询绑定银行张数
        genericDTO = cardClient.queryCardsInfo(CorpBusTyp.SIGN, CorpBusSubTyp.FAST_SIGN, userId, null);
        AgrInfoRspDTO agrInfoRspDTO = (AgrInfoRspDTO) genericDTO.getBody();
        if (JudgeUtils.isNull(agrInfoRspDTO)) {
            LemonException.throwBusinessException("BIL20015");
        }
        userAccountRspDTO.setCardCount(agrInfoRspDTO.getList().size());
        return userAccountRspDTO;
    }

    /**
     * 根据币种获取用户账户余额信息
     *
     * @param userAccountDTO1
     * @return
     */
    @Override
    public List<QueryAcBalRspDTO> queryUserActBalByCcy(UserAccountDTO userAccountDTO1) {
        String userId = userAccountDTO1.getUserId();
        com.hisun.lemon.acm.dto.UserAccountDTO userAccountDTO = new com.hisun.lemon.acm.dto.UserAccountDTO();
        //初始化返回DTO
        UserAccountRspDTO userAccountRspDTO = new UserAccountRspDTO();
        userAccountDTO.setUserId(userId);
        if (JudgeUtils.isNotBlank(userAccountDTO1.getCcy())) {
            userAccountDTO.setCcy(userAccountDTO1.getCcy());
        }

        //调用账户接口，查询账户余额
        GenericRspDTO<List<QueryAcBalRspDTO>> genericDTO = accountManagementClient.queryAcBal(userAccountDTO);
        List<QueryAcBalRspDTO> queryAcBalRspDTO = genericDTO.getBody();
        if (JudgeUtils.isNull(queryAcBalRspDTO)) {
            LemonException.throwBusinessException("BIL20012");
        }
        //判断资金类型为现金，则填充账户余额
        for (QueryAcBalRspDTO acBalRspDTO : queryAcBalRspDTO) {
            if (JudgeUtils.equals(CapTypEnum.CAP_TYP_CASH.getCapTyp(), acBalRspDTO.getCapTyp())) {
                userAccountRspDTO.setCashBalAmt(acBalRspDTO.getAcCurBal());
            }
        }
        return queryAcBalRspDTO;
    }

    @Override
    public UserOrderRecordListRspDTO queryUserOrderRecords(UserOrderRecordDTO userOrderRecordDTO) {
        //如果交易起始时间不给空，转为之间类型
        if (JudgeUtils.isNotNull(userOrderRecordDTO.getTxTmBeginStr()) && !JudgeUtils.equals("", userOrderRecordDTO.getTxTmBeginStr())) {
            userOrderRecordDTO.setTxTmBegin(DateTimeUtils.parseLocalDateTime(userOrderRecordDTO.getTxTmBeginStr()));
        }
        //如果交易结束 时间不给空，转为之间类型
        if (JudgeUtils.isNotNull(userOrderRecordDTO.getTxTmEndStr()) && !JudgeUtils.equals("", userOrderRecordDTO.getTxTmEndStr())) {
            userOrderRecordDTO.setTxTmEnd(DateTimeUtils.parseLocalDateTime(userOrderRecordDTO.getTxTmEndStr()));
        }

        //切割交易类型txType，装list
        String txType = userOrderRecordDTO.getTxType();
        if (JudgeUtils.isNotEmpty(txType)) {
            List<String> list = Arrays.asList(txType.split(","));
            if (list.size() != 0) {
                userOrderRecordDTO.setTxTypeList(list);
            }
        }

        PageInfo pageInfo = null;
        List<UserBillOrderDO> userBillOrderDOList = null;

        //根据条件查询用户账单信息
        pageInfo = PageUtils.pageQueryWithCount(userOrderRecordDTO.getPageNo(), userOrderRecordDTO.getPageSize(), () -> userBillOrderDao.queryUserOrderRecord(userOrderRecordDTO));

        if (JudgeUtils.isNotNull(pageInfo)) {
            userBillOrderDOList = pageInfo.getList();
        }

        List<UserOrderRecordListRspDTO.UserOrderRecord> recordList = new ArrayList<>();
        for (UserBillOrderDO userBillOrderDO : userBillOrderDOList) {
            UserOrderRecordListRspDTO.UserOrderRecord record = new UserOrderRecordListRspDTO.UserOrderRecord();
            BeanUtils.copyProperties(record, userBillOrderDO);
            recordList.add(record);
        }

        UserOrderRecordListRspDTO userOrderRecordListRspDTO = new UserOrderRecordListRspDTO();
        userOrderRecordListRspDTO.setList(recordList);
        userOrderRecordListRspDTO.setTotalNumber(pageInfo.getTotal());
        return userOrderRecordListRspDTO;
    }

    /**
     * 获取用户数币账户收款/充值记录列表
     *
     * @param req
     * @return
     */
    @Override
    public BilDmAccountReceiptsRspDTO getDmAccountReceiptsList(BilDmAccountReceiptsReqDTO req) {
        // 校验账户编号
        if (JudgeUtils.isBlank(req.getAcNo())) {
            logger.warn("账户编号为空，不执行查询");
            BilDmAccountReceiptsRspDTO result = new BilDmAccountReceiptsRspDTO();
            result.setRecords(new ArrayList<>());
            result.setTotalRecords(0);
            result.setTotalPages(0);
            result.setPageNo(req.getPageNo());
            result.setPageSize(req.getPageSize());
            return result;
        }

        // 设置默认时间（当天）
        LocalDateTime startTime = req.getStartTime();
        LocalDateTime endTime = req.getEndTime();
        if (JudgeUtils.isNull(startTime)) {
            startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            req.setStartTime(startTime);
        }
        if (JudgeUtils.isNull(endTime)) {
            endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(*********);
            req.setEndTime(endTime);
        }

        // 查询账单编号集合(bil_user_order)
        List<String> orderNos = userBillOrderDao.findOrderNos(
                req.getAcNo(), req.getTxType(), startTime, endTime);
        // 总记录数
        long totalRecords = orderNos.size();
        if (totalRecords == 0) {
            logger.info("无符合条件的记录，acNo: {}, txType: {}", req.getAcNo(), req.getTxType());
            BilDmAccountReceiptsRspDTO result = new BilDmAccountReceiptsRspDTO();
            result.setRecords(new ArrayList<>());
            result.setTotalRecords(0);
            result.setTotalPages(0);
            result.setPageNo(req.getPageNo());
            result.setPageSize(req.getPageSize());
            return result;
        }
        // 获取分页收款/充值记录数据
        ReceiptRecordReqDTO recordReq = new ReceiptRecordReqDTO();
        recordReq.setOrderNos(orderNos);
        recordReq.setPageNo(req.getPageNo());
        recordReq.setPageSize(req.getPageSize());
        GenericRspDTO<List<ReceiptRecord>> rspDTO = accountManagementClient.getDmAccountReceiptsList(recordReq);

        // 转换为 BilReceiptRecord 列表
        List<ReceiptRecord> records = rspDTO.getBody();
        List<BilReceiptRecord> bilRecords = new ArrayList<>(records.size());
        for (ReceiptRecord receiptRecord : records) {
            BilReceiptRecord bilRecord = new BilReceiptRecord();
            BeanUtils.copyProperties(bilRecord, receiptRecord);
            bilRecords.add(bilRecord);
        }

        // 组装响应
        BilDmAccountReceiptsRspDTO result = new BilDmAccountReceiptsRspDTO();
        result.setRecords(bilRecords);
        result.setTotalRecords(totalRecords);
        result.setTotalPages((int) Math.ceil((double) totalRecords / req.getPageSize()));
        result.setPageNo(req.getPageNo());
        result.setPageSize(req.getPageSize());

        logger.info("查询用户数币账户收款/充值记录完成，adNo: {}, totalRecords: {}", req.getAcNo(), totalRecords);
        return result;
    }

    /**
     * 根据账单编号查询用户账单
     *
     * @param orderNo 账单编号
     * @return
     */
    @Override
    public UserBilOrderRspDTO getBilInfoByOrderNo(String orderNo) {
        UserBillOrderDO billOrderDO = userBillOrderDao.get(orderNo);
        UserBilOrderRspDTO rspDTO = new UserBilOrderRspDTO();
        if (JudgeUtils.isNull(billOrderDO)) {
            return rspDTO;
        }
        BeanUtils.copyProperties(rspDTO, billOrderDO);
        return rspDTO;
    }

    /**
     * 更新订单表
     *
     * @param req
     */
    @Override
    public void updateOrder(UpdateOrderReqDTO req) {
        UserBillOrderDO billOrderDO = new UserBillOrderDO();
        BeanUtils.copyProperties(billOrderDO, req);
        userBillOrderDao.update(billOrderDO);
    }

    /**
     * 新增订单
     * @param req
     */
    @Override
    public void addOrder(com.hisun.lemon.bil.dto.AddOrderReqDTO req) {
        UserBillOrderDO billOrderDO = new UserBillOrderDO();
        BeanUtils.copyProperties(billOrderDO, req);
        userBillOrderDao.insert(billOrderDO);
    }
}
