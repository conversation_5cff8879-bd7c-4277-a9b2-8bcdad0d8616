package com.hisun.lemon.bil.dao;

import com.hisun.lemon.bil.dto.MercBillOrderDTO;
import com.hisun.lemon.bil.dto.MercChkDetailExportDTO;
import com.hisun.lemon.bil.dto.MercCsmInfoDTO;
import com.hisun.lemon.bil.dto.MercUpBillOrderDTO;
import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/12
 * @time 17:30
 */
@Mapper
@Component
public interface IMercBillOrderDao extends BaseDao<MercBillOrderDO> {

    List<MercBillOrderDO> queryMercBillOrder(MercBillOrderDTO mercBillOrderDTO);

    MercBillOrderDO queryMercDetails(@Param("orderNo") String orderNo, @Param("mercId") String mercId);

    MercBillOrderDO queryMercAccount(String mercId);
    /**
     * 查询订单是否存在
     * @param orderNo
     * @return
     */
    int query(String orderNo);

    /**
     * 查询今日收入
     * @param mercId
     * @return
     */
    BigDecimal queryTodayIncome(@Param("mercId") String mercId, @Param("curTm") LocalDateTime curTm);

    /**
     * 查询今日退款
     * @param mercId
     * @return
     */
    BigDecimal queryTodayRefund(String mercId);

    /**
     * 查询商户账单对账信息
     * @param mercId
     * @return
     */
    List<MercBillOrderDO> queryData(String mercId);

    List<MercBillOrderDO> queryAndExport(String mercId, LocalDateTime txTmBegin, LocalDateTime txTmEnd, String orderStat, String rfdStat);

    /**
     * 查询商户对账明细并导出excel
     * @param mercChkDetailExportDTO
     * @return
     */
    List<MercBillOrderDO> queryDetailByDate(MercChkDetailExportDTO mercChkDetailExportDTO);

    /**
     * 查询商户清分信息
     * @param mercCsmInfoDTO
     * @return
     */
    List<MercBillOrderDO> queryMercCsmDetails(MercCsmInfoDTO mercCsmInfoDTO);

    List<MercBillOrderDO> queryMercUpBillOrder(MercUpBillOrderDTO mercBillOrderDTO);
}
