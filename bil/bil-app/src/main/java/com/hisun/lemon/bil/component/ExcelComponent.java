package com.hisun.lemon.bil.component;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.read.biff.BiffException;
import jxl.write.Label;
import jxl.write.Number;
import jxl.write.Boolean;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * <AUTHOR>
 * @date 2017/7/14
 * @time 14:30
 */
@Component
public class ExcelComponent {

    //@Value("${bil.merc.detailTemplate}")
    private String merDetailTemplate;
    //@Value("${bil.merc.detailpath}")
    private String merDetailExcelPath;

    public String createMerchantCheckDetailExcel(List<Object[]> data,String fileName, String language){

        if(JudgeUtils.isNotEmpty(language)) {
            if (JudgeUtils.equals("zh", language)) {
                merDetailTemplate = LemonUtils.getProperty("bil.merc.detailTemplate-zh");
            }
            if (JudgeUtils.equals("en", language)) {
                merDetailTemplate = LemonUtils.getProperty("bil.merc.detailTemplate-en");
            }
            if (JudgeUtils.equals("km", language)) {
                merDetailTemplate = LemonUtils.getProperty("bil.merc.detailTemplate-km");
            }
        }else{
            merDetailTemplate = LemonUtils.getProperty("bil.merc.detailTemplate-zh");
        }
        merDetailExcelPath = LemonUtils.getProperty("bil.merc.detailpath");
        String path=merDetailExcelPath+DateTimeUtils.getCurrentDateStr()+"/";
        File file=new File(path);
        if(!file.exists()){
            file.mkdirs();
        }

        String outPath=path+fileName;
        FileOutputStream is =null;
        WritableWorkbook wwb =null;
        Workbook wb=null;
        WritableSheet sheet =null;

        try{
            is=new FileOutputStream(outPath);
            String templePath =merDetailTemplate;
            // 读取模板文件
            wb= Workbook.getWorkbook(new File(templePath));

            WorkbookSettings settings = new WorkbookSettings ();
            settings.setWriteAccess(null);
            // 根据模板文件创建可写的文件，注意这里是createWorkbook(),创建而不是获取
            wwb = Workbook.createWorkbook(is, wb,settings);
            // 注意这里是getSheet(), 通过索引，获取模板文件中的sheet页第一页
            sheet = wwb.getSheet(0);

            for(int i=1; i<=data.size(); i++) {     // 从第二行开始插入，第一行表头
                Object[] lineArr= data.get(i-1);

                //0 清分日期，1 订单日期，2 订单号，3 类型，4 订单金额，5 现金，6 海币，
                // 7 电子券，8 服务费，9 支付类型，10 支付渠道，11 交易状态
                LocalDate acDt=(LocalDate)lineArr[0];
                LocalDate txDt=(LocalDate)lineArr[1];
                String orderNo=(String)lineArr[2];
                String type=(String)lineArr[3];
                BigDecimal orderAmt=(BigDecimal)lineArr[4];
                BigDecimal acAmt=(BigDecimal)lineArr[5];

                BigDecimal hcoupon=(BigDecimal)lineArr[6];
                BigDecimal coupon=(BigDecimal)lineArr[7];
                BigDecimal fee=(BigDecimal) lineArr[8];
                String payMode=(String) lineArr[9];
                String payCnl=(String) lineArr[10];
                String status=(String)lineArr[11];

                Label labelAcDt;
                if(JudgeUtils.isNull(acDt)) {
                    labelAcDt = new Label(0, i, null);
                }else {
                    labelAcDt = new Label(0, i, DateTimeUtils.formatLocalDate(acDt));
                }
                Label labelTxDt = new Label(1, i, DateTimeUtils.formatLocalDate(txDt));
                Label labelOrderNo = new Label(2, i,orderNo);
                Label labelType = new Label(3, i,type);

                Number labelOrderAmt = new Number(4, i, Double.parseDouble(orderAmt.toString()));
                Number labelAcAmt = new Number(5, i, Double.parseDouble(acAmt.toString()));

                Number labelHCoupon= new Number(6, i, Double.parseDouble(hcoupon.toString()));
                Number labelCoupon= new Number(7, i, Double.parseDouble(coupon.toString()));
                Number labelFee;
                if(JudgeUtils.isNull(fee)){
                    labelFee = new Number(8, i, 0);
                }else {
                    labelFee = new Number(8, i, Double.parseDouble(fee.toString()));
                }

                Label labelPayMode = new Label(9, i,payMode);
                Label labelPayCnl = new Label(10, i,payCnl);
                Label labelStatus = new Label(11, i,status);

                sheet.addCell(labelAcDt);
                sheet.addCell(labelTxDt);
                sheet.addCell(labelOrderNo);
                sheet.addCell(labelType);
                sheet.addCell(labelOrderAmt);
                sheet.addCell(labelAcAmt);
                sheet.addCell(labelHCoupon);
                sheet.addCell(labelCoupon);
                sheet.addCell(labelFee);
                sheet.addCell(labelPayMode);
                sheet.addCell(labelPayCnl);
                sheet.addCell(labelStatus);
            }

            wwb.write();
        }catch (IOException e) {
            e.printStackTrace();
        }catch (Exception e2) {
            e2.printStackTrace();
        } finally {
            try{
                if(JudgeUtils.isNotNull(wwb)){
                    wwb.close();
                }
            }catch (Exception es){
                es.printStackTrace();
            }

            try{
                if(JudgeUtils.isNotNull(is)){
                    is.close();
                }
            }catch (IOException e){
                e.printStackTrace();
            }

        }
        return zipFile(outPath,outPath+".zip",true);
    }

    private String zipFile(String sf,String df,boolean delSrc){
        File srcfile=new File(sf);
        if (!srcfile.exists())
            return null;

        File targetFile=new File(df);

        byte[] buf = new byte[1024];
        FileInputStream in = null;
        ZipOutputStream out= null;
        try {

            in = new FileInputStream(srcfile);
            out=new ZipOutputStream(new FileOutputStream(targetFile));
            out.putNextEntry(new ZipEntry(srcfile.getName()));

            int len;
            while ((len = in.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {

                if(JudgeUtils.isNotNull(out)){
                    try{
                        out.closeEntry();
                        out.close();
                    }catch(Exception e){

                    }
                }

                if(JudgeUtils.isNotNull(in)){
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }

        if(delSrc){
            srcfile.delete();
        }
        return df;
    }


    public static void main(String[] args) throws IOException, BiffException {
        // 表格要导出的目录
        String outPath = "/Users/<USER>/Documents/new.xls";
        FileOutputStream is = new FileOutputStream(outPath);
        // 模板文件路径
        String templePath = "/Users/<USER>/Documents/模板.xls";
        // 读取模板文件
        Workbook wb = Workbook.getWorkbook(new File(templePath));

        WorkbookSettings settings = new WorkbookSettings ();
        settings.setWriteAccess(null);

        // 根据模板文件创建可写的文件，注意这里是createWorkbook(),创建而不是获取
        WritableWorkbook wwb = Workbook.createWorkbook(is, wb,settings);
        // 注意这里是getSheet(), 通过索引，获取模板文件中的sheet页第一页
        WritableSheet sheet = wwb.getSheet(0);

        for(int i=1; i<=4; i++) {     // 从第二行开始插入，第一行表头
            // 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
            // 填充数字， import jxl.write.Number;
            Number label1 = new Number(0, i, (long) i);
            // 填充文本
            Label label2 = new Label(1, i,"AAS"+i);
            Label label3 = new Label(2, i, i*3+"");
            // 填充布尔值， import jxl.write.Boolean;
            Boolean label4 = new Boolean(3, i, java.lang.Boolean.valueOf("1"));
            // 填充日期
            Label label5 = new Label(4, i, "20127788");
            // 填充格式化的数字, 保留6位小数，不足六位的时候有几位保留几位。非小数部分用千位符隔开
            jxl.write.NumberFormat nf = new jxl.write.NumberFormat("###,###,###.######");
            jxl.write.WritableCellFormat wcf = new jxl.write.WritableCellFormat(nf);
            jxl.write.Number label6 = new jxl.write.Number(5, i, i*500, wcf);

            try {
                sheet.addCell(label1);
                sheet.addCell(label2);
                sheet.addCell(label3);
                sheet.addCell(label4);
                sheet.addCell(label5);
                sheet.addCell(label6);
            } catch (WriteException e) {
                e.printStackTrace();
            }
        }
        try {
            wwb.write();
            wwb.close();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (WriteException e) {
            e.printStackTrace();
        }
    }
}
