package com.hisun.lemon.bil.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dto.CreateUserBillDTO;
import com.hisun.lemon.bil.service.IBillService;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2017/8/16
 * @time 19:57
 */
@Component(BilConstants.SYNC_CREATE_BEAN)
public class CreateBillMessageHandler implements MessageHandler<CreateUserBillDTO> {
    private static final Logger logger = LoggerFactory.getLogger(CreateBillMessageHandler.class);
    @Resource
    private IBillService billService;

    @Resource
    ObjectMapper objectMapper;

    @Override
    public void onMessageReceive(GenericCmdDTO<CreateUserBillDTO> genericCmdDTO) {
        String data = ObjectMapperHelper.writeValueAsString(objectMapper, genericCmdDTO, true);
        logger.info("接收订单创建同步数据：" + data);
        billService.createUserBills(genericCmdDTO.getBody());
    }
}

