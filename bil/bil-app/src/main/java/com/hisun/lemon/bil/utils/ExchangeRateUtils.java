package com.hisun.lemon.bil.utils;

import cn.hutool.json.JSONUtil;
import com.hisun.lemon.bil.quartz.util.SpringContextUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.tam.client.ExchangeOrderClient;
import com.hisun.lemon.tam.dto.ExchangeRateRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 汇率工具类
 * 用于将不同币种转换为USD或USDT
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class ExchangeRateUtils {
    private static final Logger logger = LoggerFactory.getLogger(ExchangeRateUtils.class);

    // 汇率映射表（备用，当外部服务不可用时使用）
    private static final Map<String, BigDecimal> EXCHANGE_RATES = new HashMap<>();

    static {
        EXCHANGE_RATES.put("USD", BigDecimal.ONE);
        EXCHANGE_RATES.put("CNY", new BigDecimal("0.140000000000000000")); // 1 USD = 7.14 CNY
        EXCHANGE_RATES.put("HKD", new BigDecimal("0.127654321000000000")); // 1 USD = 7.83 HKD
        EXCHANGE_RATES.put("EUR", new BigDecimal("1.08")); // 1 USD = 0.93 EUR
        EXCHANGE_RATES.put("GBP", new BigDecimal("1.26")); // 1 USD = 0.79 GBP
        EXCHANGE_RATES.put("JPY", new BigDecimal("0.0067")); // 1 USD = 149 JPY
        EXCHANGE_RATES.put("KRW", new BigDecimal("0.00075")); // 1 USD = 1333 KRW
        EXCHANGE_RATES.put("BTC", new BigDecimal("42000")); // 1 BTC = 42000 USD
        EXCHANGE_RATES.put("ETH", new BigDecimal("2500")); // 1 ETH = 2500 USD
        EXCHANGE_RATES.put("USDT", BigDecimal.ONE); // USDT 1:1 USD
        EXCHANGE_RATES.put("USDC", BigDecimal.ONE); // USDC 1:1 USD
    }

    /**
     * 将指定币种金额转换为USD
     */
    public static BigDecimal convertToUSD(BigDecimal amount, String currency) {
        logger.info("开始进行币种金额转换，金额：{}，币种：{}，转换为USD", amount, currency);

        if (amount == null || currency == null) {
            return BigDecimal.ZERO;
        }

        if ("USD".equalsIgnoreCase(currency)) {
            return amount;
        }

        try {
            ExchangeOrderClient client = SpringContextUtils.getBean(ExchangeOrderClient.class);
            if (client != null) {
                GenericRspDTO<ExchangeRateRspDTO> rspDTO = client.queryExchangeRate("USD", currency);
                logger.info("获取实时汇率，响应：{}", JSONUtil.toJsonStr(rspDTO));
                if (rspDTO.getBody() != null && JudgeUtils.isSuccess(rspDTO.getMsgCd())) {
                    BigDecimal rate = rspDTO.getBody().getExchangeRate();
                    if (rate != null && rate.compareTo(BigDecimal.ZERO) > 0) {
                        return amount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("获取实时汇率失败，使用静态汇率表，错误：{}", e.getMessage());
        }

        BigDecimal rate = EXCHANGE_RATES.get(currency.toUpperCase());
        if (rate == null) {
            rate = BigDecimal.ONE;
        }

        return amount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 批量转换币种金额为USD
     */
    public static BigDecimal convertAllToUSD(Map<String, BigDecimal> amountsByCurrency) {
        BigDecimal totalUSD = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : amountsByCurrency.entrySet()) {
            String currency = entry.getKey();
            BigDecimal amount = entry.getValue();
            BigDecimal usdAmount = convertToUSD(amount, currency);
            totalUSD = totalUSD.add(usdAmount);
        }
        return totalUSD;
    }

    /**
     * 将指定币种金额转换为USDT
     */
    public static BigDecimal convertToUSDT(BigDecimal amount, String currency) {
        logger.info("开始进行币种金额转换，金额：{}，币种：{}，转换为USDT", amount, currency);

        if (amount == null || currency == null) {
            return BigDecimal.ZERO;
        }

        if ("USDT".equalsIgnoreCase(currency)) {
            return amount;
        }

        try {
            ExchangeOrderClient client = SpringContextUtils.getBean(ExchangeOrderClient.class);
            if (client != null) {
                GenericRspDTO<ExchangeRateRspDTO> rspDTO = client.queryExchangeRate("USDT", currency);
                logger.info("获取实时汇率，响应：{}", JSONUtil.toJsonStr(rspDTO));
                if (rspDTO.getBody() != null && JudgeUtils.isSuccess(rspDTO.getMsgCd())) {
                    BigDecimal rate = rspDTO.getBody().getExchangeRate();
                    if (rate != null && rate.compareTo(BigDecimal.ZERO) > 0) {
                        return amount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("获取实时汇率失败，使用静态汇率表，错误：{}", e.getMessage());
        }

        BigDecimal rate = EXCHANGE_RATES.get(currency.toUpperCase());
        if (rate == null) {
            rate = BigDecimal.ONE;
        }

        return amount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取支持的币种列表
     */
    public static String[] getSupportedCurrencies() {
        return EXCHANGE_RATES.keySet().toArray(new String[0]);
    }

    /**
     * 检查是否支持指定币种
     */
    public static boolean isSupportedCurrency(String currency) {
        return currency != null && EXCHANGE_RATES.containsKey(currency.toUpperCase());
    }

    /**
     * 使用传入的汇率Map将指定币种金额转换为USD
     */
    public static BigDecimal convertToUSD(BigDecimal currency, String ccy, Map<String, BigDecimal> rateMap) {
        if (ccy == null || currency == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal rate = rateMap.getOrDefault(ccy, BigDecimal.ONE);
        return currency.multiply(rate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 初始化默认汇率Map
     */
    public static Map<String, BigDecimal> initExchangeRate() {
        Map<String, BigDecimal> rates = new HashMap<>();
        rates.put("USD", BigDecimal.ONE);
        rates.put("CNY", new BigDecimal("0.140000000000000000"));
        rates.put("HKD", new BigDecimal("0.127654321000000000"));
        rates.put("EUR", new BigDecimal("1.08"));
        rates.put("GBP", new BigDecimal("1.26"));
        rates.put("JPY", new BigDecimal("0.0067"));
        rates.put("KRW", new BigDecimal("0.00075"));
        rates.put("BTC", new BigDecimal("42000"));
        rates.put("ETH", new BigDecimal("2500"));
        rates.put("USDT", BigDecimal.ONE);
        rates.put("USDC", BigDecimal.ONE);
        return rates;
    }
}
