package com.hisun.lemon.bil.service;

import com.hisun.lemon.bil.dto.*;


/**
 * <AUTHOR>
 * @date 2017/7/7
 * @time 17:25
 */
public interface IInsideBillOrderService {
    /**
     * 查询用户个人账单交易信息
     * @param userBillOrderDTO
     */
    public UserBillOrderListDTO queryUserBills(UserBillOrderDTO userBillOrderDTO);

    /**
     * 查询单条用户账单交易明细
     * @param userBillInfoDTO
     * @return
     */
    public UserBillOrderListDTO queryBillInfo(UserBillInfoDTO userBillInfoDTO);

    /**
     * 查询用户账户信息
     * @param userAccountDTO
     * @return
     */
    public UserAccountRspDTO queryUserAccount(UserAccountDTO userAccountDTO);
}
