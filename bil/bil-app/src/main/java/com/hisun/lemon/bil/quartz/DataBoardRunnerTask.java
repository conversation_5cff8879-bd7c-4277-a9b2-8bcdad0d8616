package com.hisun.lemon.bil.quartz;

import com.hisun.lemon.bil.quartz.job.BusinessIndicatorsJob;
import com.hisun.lemon.bil.quartz.job.TransactionDataStatisticsJob;
import groovy.util.logging.Slf4j;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 数据看板跑批
 */
@Slf4j
@Component
public class DataBoardRunnerTask implements ApplicationRunner {
    @Override
    public void run(ApplicationArguments args) throws Exception {

        StdSchedulerFactory stdSchedulerFactory = new StdSchedulerFactory();
        try {
            Scheduler scheduler = stdSchedulerFactory.getScheduler();
            scheduler.start();

            // 创建Trigger，指定开始时间和结束时间
            /*********************/
            String timeInquiryStartTriggerJobId = UUID.randomUUID().toString().replace("-", "").substring(0, 10);
            JobDetail timeInquiryStartConsumer = JobBuilder.newJob(BusinessIndicatorsJob.class)
                    .withIdentity(timeInquiryStartTriggerJobId)
                    .build();

            //触发器 预成交
            Trigger timeInquiryStartTrigger = TriggerBuilder.newTrigger()
                    .withIdentity(timeInquiryStartTriggerJobId + "T")
                    .startNow() // 立即开始
                    .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                            .withIntervalInMinutes(1) // 每30分钟执行一次
                            .repeatForever()) // 无限重复
                    .build();
            /*********************/


            // 创建Trigger，指定开始时间和结束时间
            /*********************/
            String transactionDataStatisticsJobId = UUID.randomUUID().toString().replace("-", "").substring(0, 10);
            JobDetail transactionDataStatisticsJobConsumer = JobBuilder.newJob(TransactionDataStatisticsJob.class)
                    .withIdentity(transactionDataStatisticsJobId)
                    .build();

            //触发器 预成交
            Trigger transactionDataStatisticsTrigger = TriggerBuilder.newTrigger()
                    .withIdentity(transactionDataStatisticsJobId + "T")
                    .startNow() // 立即开始
                    .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                            .withIntervalInMinutes(1) // 每30分钟执行一次
                            .repeatForever()) // 无限重复
                    .build();
            /*********************/


            //开始
            scheduler.scheduleJob(timeInquiryStartConsumer,timeInquiryStartTrigger);
            scheduler.scheduleJob(transactionDataStatisticsJobConsumer,transactionDataStatisticsTrigger);

        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }

    }

}
