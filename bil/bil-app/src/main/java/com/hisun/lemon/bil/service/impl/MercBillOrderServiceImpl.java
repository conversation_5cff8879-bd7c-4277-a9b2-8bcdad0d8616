package com.hisun.lemon.bil.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.*;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dao.IMercBillOrderDao;
import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.bil.service.IMercBillOrderService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/12
 * @time 16:22
 */
@Service
public class MercBillOrderServiceImpl extends BaseService implements IMercBillOrderService {
    private static final Logger logger = LoggerFactory.getLogger(MercBillOrderServiceImpl.class);


    @Resource
    private IMercBillOrderDao mercBillOrderDao;

    @Resource
    private AccountManagementClient accountManagementClient;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    /**
     * 查询所有用户在该商户交易记录
     * @param mercBillOrderDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
    public MercBillOrderListDTO queryMercBills(MercBillOrderDTO mercBillOrderDTO) {
        String mercId = LemonUtils.getUserId();
        if(JudgeUtils.isNotEmpty(mercId)){
            if(JudgeUtils.isEmpty(mercBillOrderDTO.getMercId())){
                mercBillOrderDTO.setMercId(mercId);
            }
        }

        //如果交易起始时间不给空，转为之间类型
        if(JudgeUtils.isNotEmpty(mercBillOrderDTO.getTxTmBeginStr())){
            mercBillOrderDTO.setTxTmBegin(DateTimeUtils.parseLocalDateTime(mercBillOrderDTO.getTxTmBeginStr()));
        }
        //如果交易结束时间不给空，转为之间类型
        if(JudgeUtils.isNotEmpty(mercBillOrderDTO.getTxTmEndStr())){
            mercBillOrderDTO.setTxTmEnd(DateTimeUtils.parseLocalDateTime(mercBillOrderDTO.getTxTmEndStr()));
        }
        //把起始时间置为00：00：00，结束时间置为23：59：59
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-ddHH:mm:ss");
        String begin = mercBillOrderDTO.getTxTmBegin().toString();
        begin = begin.substring(0,begin.indexOf('T'))+"00:00:00";
        LocalDateTime beginTm = LocalDateTime.parse(begin, dateFormat);
        mercBillOrderDTO.setTxTmBegin(beginTm);
        String end = mercBillOrderDTO.getTxTmEnd().toString();
        end = end.substring(0,end.indexOf('T'))+"23:59:59";
        LocalDateTime endTm = LocalDateTime.parse(end, dateFormat);
        mercBillOrderDTO.setTxTmEnd(endTm);
        //切割订单状态orderStatus，装list
        String orderStatus = mercBillOrderDTO.getOrderStatus();
        if(JudgeUtils.isNotEmpty(orderStatus)) {
            List<String> list = Arrays.asList(orderStatus.split(","));
            if (list.size() != 0) {
                mercBillOrderDTO.setOrderStatusList(list);
            }
        }
        //根据支付方式进行账单筛选
        if(JudgeUtils.equals(BilConstants.PAY_MPAY,mercBillOrderDTO.getPayMod())){
            //Mpay
            mercBillOrderDTO.setPayType(BilConstants.PAY_M);
        }
        if(JudgeUtils.equals(BilConstants.PAY_WECHAT_USER,mercBillOrderDTO.getPayMod())){
            //微信扫码
            mercBillOrderDTO.setPayType(BilConstants.PAY_WECHAT);
            mercBillOrderDTO.setBusType(BilConstants.BUS_TYPE_CONSUME_CODE);
        }
        if(JudgeUtils.equals(BilConstants.PAY_WECHAT_MERC,mercBillOrderDTO.getPayMod())){
            //微信条码
            mercBillOrderDTO.setPayType(BilConstants.PAY_WECHAT);
            mercBillOrderDTO.setBusType(BilConstants.BUS_TYPE_CONSUME_BARCODE);
        }
        if(JudgeUtils.equals(BilConstants.PAY_ALI_USER,mercBillOrderDTO.getPayMod())){
            //支付宝扫码
            mercBillOrderDTO.setPayType(BilConstants.PAY_ALI);
            mercBillOrderDTO.setBusType(BilConstants.BUS_TYPE_CONSUME_CODE);
        }
        if(JudgeUtils.equals(BilConstants.PAY_ALI_MERC,mercBillOrderDTO.getPayMod())){
            //支付宝条码
            mercBillOrderDTO.setPayType(BilConstants.PAY_ALI);
            mercBillOrderDTO.setBusType(BilConstants.BUS_TYPE_CONSUME_BARCODE);
        }
        if(JudgeUtils.equals(BilConstants.PAY_BEST_USER,mercBillOrderDTO.getPayMod())){
            //翼支付扫码
            mercBillOrderDTO.setPayType(BilConstants.PAY_BEST);
            mercBillOrderDTO.setBusType(BilConstants.BUS_TYPE_CONSUME_CODE);
        }
        if(JudgeUtils.equals(BilConstants.PAY_BEST_MERC,mercBillOrderDTO.getPayMod())){
            ////翼支付条码
            mercBillOrderDTO.setPayType(BilConstants.PAY_BEST);
            mercBillOrderDTO.setBusType(BilConstants.BUS_TYPE_CONSUME_BARCODE);
        }
        //根据条件查询商户账单信息，分页
        List<MercBillOrderDO> mercBillOrderListDO =null;
        //List<MercBillOrderDO> mercBillOrderListDO = PageUtils.pageQuery(mercBillOrderDTO.getPageNo(), mercBillOrderDTO.getPageSize(), ()-> mercBillOrderDao.queryMercBillOrder(mercBillOrderDTO));
        PageInfo pageInfo = PageUtils.pageQueryWithCount(mercBillOrderDTO.getPageNo(), mercBillOrderDTO.getPageSize(), ()-> mercBillOrderDao.queryMercBillOrder(mercBillOrderDTO));
        //查询所有数据，不分页
        List<MercBillOrderDO> mercBillOrderListTotalDO = mercBillOrderDao.queryMercBillOrder(mercBillOrderDTO);
        //判断是否有查到数据
        /*if (JudgeUtils.isNull(mercBillOrderListDO) || mercBillOrderListDO.size() <= 0){
            LemonException.throwBusinessException("BIL20004");
        }*/
        if(JudgeUtils.isNotNull(pageInfo)) {
            mercBillOrderListDO = pageInfo.getList();
        }
        //数据对象集合转为传输对象
        List<MercBillOrderListDTO.MercBillOrder> mercBillOrders = new ArrayList<>();
        BigDecimal totalAmt = BigDecimal.ZERO;
        if(JudgeUtils.isNotNull(mercBillOrderListDO)) {
            for (MercBillOrderDO mercBillOrderDO : mercBillOrderListDO) {
                MercBillOrderListDTO.MercBillOrder mercBillOrder = new MercBillOrderListDTO.MercBillOrder();
                BeanUtils.copyProperties(mercBillOrder, mercBillOrderDO);
                mercBillOrders.add(mercBillOrder);
            }
        }
        //计算总记录数，计算成功的汇总金额
        if(JudgeUtils.isNotNull(mercBillOrderListTotalDO)) {
            for (MercBillOrderDO mercBillOrderDO : mercBillOrderListTotalDO) {
                //订单成功则累加交易金额
                if(JudgeUtils.equals(mercBillOrderDO.getOrderStatus(),BilConstants.ORD_STS_S) ||
                        JudgeUtils.equals(mercBillOrderDO.getOrderStatus(),BilConstants.ORD_STS_S1) ||
                        JudgeUtils.equals(mercBillOrderDO.getOrderStatus(),BilConstants.ORD_STS_S2)) {
                    totalAmt = totalAmt.add(mercBillOrderDO.getOrderAmt());
                }
                //退款的交易金额要去掉
                if(JudgeUtils.equals(mercBillOrderDO.getOrderStatus(),BilConstants.ORD_STS_R1)) {
                    totalAmt = totalAmt.add(mercBillOrderDO.getOrderAmt());
                    totalAmt = totalAmt.subtract(mercBillOrderDO.getRfdAmt());
                }
            }
        }
        MercBillOrderListDTO.MercBillOrder totalMercBillOrder = new MercBillOrderListDTO.MercBillOrder();
        //设置汇总金额，汇总金额的交易类型为'00'
        totalMercBillOrder.setOrderAmt(totalAmt);
        totalMercBillOrder.setTxType("00");
        //添加汇总记录
        mercBillOrders.add(totalMercBillOrder);

        MercBillOrderListDTO mercBillOrderListDTO = new MercBillOrderListDTO();
        mercBillOrderListDTO.setList(mercBillOrders);
        //设置汇总金额
        mercBillOrderListDTO.setTotalAmt(totalAmt);
        //设置返回总记录数
        mercBillOrderListDTO.setTotalNumber(pageInfo.getTotal());
        return mercBillOrderListDTO;
    }

    /**
     * 查询用户在该商户单条订单交易记录明细
     * @param mercBillInfoDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
    public MercBillOrderListDTO queryMercDetails(MercBillInfoDTO mercBillInfoDTO) {

        String orderNo = mercBillInfoDTO.getOrderNo();
        String mercId = LemonUtils.getUserId();
        if(JudgeUtils.isNotEmpty(mercBillInfoDTO.getMercId())){
            mercId = mercBillInfoDTO.getMercId();
        }
        MercBillOrderDO mercBillOrderDO = mercBillOrderDao.queryMercDetails(orderNo, mercId);
        //判断是否有查到数据
        if (JudgeUtils.isNull(mercBillOrderDO)){
            LemonException.throwBusinessException("BIL20005");
        }
        //数据对象集合转为传输对象
        List<MercBillOrderListDTO.MercBillOrder> mercBillOrders = new ArrayList<>();
        MercBillOrderListDTO.MercBillOrder mercBillOrder = new MercBillOrderListDTO.MercBillOrder();
        BeanUtils.copyProperties(mercBillOrder, mercBillOrderDO);
        mercBillOrders.add(mercBillOrder);
        MercBillOrderListDTO mercBillOrderListDTO = new MercBillOrderListDTO();
        mercBillOrderListDTO.setList(mercBillOrders);
        return mercBillOrderListDTO;
    }

    /**
     * 查询商户账户信息
     * @param mercAccountDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
    public MercAccountRspDTO queryMercAccount(MercAccountDTO mercAccountDTO) {

        String mercId = mercAccountDTO.getMercId();
        //判断商户编号是否为空
        if(StringUtils.isEmpty(mercId)){
            LemonException.throwBusinessException("BIL10006");
        }

        //调用账户接口，查询账户余额
        com.hisun.lemon.acm.dto.UserAccountDTO userAccountDTO = new UserAccountDTO();
        userAccountDTO.setUserId(mercId);
        GenericRspDTO genericDTO = accountManagementClient.queryAcBal(userAccountDTO);
        //取出账户余额及账户不可用余额
        List<QueryAcBalRspDTO> queryAcBalRspDTO = (List<QueryAcBalRspDTO>) genericDTO.getBody();
        if(JudgeUtils.isNull(queryAcBalRspDTO)){
            LemonException.throwBusinessException("BIL20012");
        }
        MercAccountRspDTO mercAccountRspDTO = new MercAccountRspDTO();
        //判断资金类型为现金，则填充账户余额
        for(QueryAcBalRspDTO acBalRspDTO: queryAcBalRspDTO) {
            if(JudgeUtils.equals(CapTypEnum.CAP_TYP_CASH.getCapTyp(),acBalRspDTO.getCapTyp())){
                mercAccountRspDTO.setCashBalAmt(acBalRspDTO.getAcCurBal());
                mercAccountRspDTO.setUavaAmt(acBalRspDTO.getAcUavaBal());
            }
            if(JudgeUtils.equals(CapTypEnum.CAP_TYP_SETTLEMENT.getCapTyp(),acBalRspDTO.getCapTyp())){
                mercAccountRspDTO.setUnSettleAmt(acBalRspDTO.getAcCurBal());
            }
        }

        //查询今日收入
        BigDecimal todayIncome = mercBillOrderDao.queryTodayIncome(mercId,DateTimeUtils.getCurrentLocalDateTime());
        //查询今日退款
        //BigDecimal todayRefund = mercBillOrderDao.queryTodayRefund(mercId);
        //mercAccountRspDTO.setTodayIncmAmt(todayIncome.subtract(todayRefund));
        if(todayIncome.compareTo(BigDecimal.ZERO)<0 || JudgeUtils.isNull(todayIncome)){
            todayIncome = BigDecimal.ZERO;
        }
        mercAccountRspDTO.setTodayIncmAmt(todayIncome);
        return mercAccountRspDTO;
    }

    /**
     * 查询商户清分信息
     * @param mercCsmInfoDTO
     * @return
     */
    @Override
    public MercCsmInfoRspDTO queryMercCsmDetails(MercCsmInfoDTO mercCsmInfoDTO) {

        String dateType = mercCsmInfoDTO.getDateType();
        String beginDateStr = mercCsmInfoDTO.getBeginDateStr();
        String endDateStr = mercCsmInfoDTO.getEndDateStr();
        //把字符串日期改为LocalDate
        if(JudgeUtils.isNotEmpty(beginDateStr)) {
            mercCsmInfoDTO.setBeginDate(DateTimeUtils.parseLocalDate(beginDateStr));
        }
        if(JudgeUtils.isNotEmpty(endDateStr)) {
            mercCsmInfoDTO.setEndDate(DateTimeUtils.parseLocalDate(endDateStr));
        }
        //判断日期类型
        if(JudgeUtils.isNotEmpty(dateType) && JudgeUtils.equals(dateType, "T")){

            //如果日期类型为交易日期，则设置为时间
            mercCsmInfoDTO.setBeginTime(mercCsmInfoDTO.getBeginDate().atTime(0,0,0));
            mercCsmInfoDTO.setEndTime(mercCsmInfoDTO.getEndDate().atTime(23,59,59));
        }
        List<MercBillOrderDO> mercBillOrderListDO = null;
        PageInfo pageInfo = PageUtils.pageQueryWithCount(mercCsmInfoDTO.getPageNo(), mercCsmInfoDTO.getPageSize(), ()-> mercBillOrderDao.queryMercCsmDetails(mercCsmInfoDTO));

        if(JudgeUtils.isNotNull(pageInfo)) {
            mercBillOrderListDO = pageInfo.getList();
        }
        //数据对象集合转为传输对象
        List<MercCsmInfoRspDTO.MercCsmInfo> mercCsmInfos = new ArrayList<>();
        //初始化总计字段
        if(JudgeUtils.isNotNull(mercBillOrderListDO)) {
            for (MercBillOrderDO mercBillOrderDO : mercBillOrderListDO) {
                MercCsmInfoRspDTO.MercCsmInfo mercCsmInfo = new MercCsmInfoRspDTO.MercCsmInfo();
                if(JudgeUtils.isNull(mercBillOrderDO.getOrderAmt())){
                    mercBillOrderDO.setOrderAmt(BigDecimal.ZERO);
                }
                if(JudgeUtils.isNull(mercBillOrderDO.getServeFee())){
                    mercBillOrderDO.setServeFee(BigDecimal.ZERO);
                }
                BeanUtils.copyProperties(mercCsmInfo, mercBillOrderDO);
                BigDecimal actAmt1 = mercBillOrderDO.getOrderAmt().subtract(mercBillOrderDO.getServeFee());
                mercCsmInfo.setActAmt(actAmt1);
                mercCsmInfos.add(mercCsmInfo);
            }
        }
        MercCsmInfoRspDTO mercCsmInfoRspDTO = new MercCsmInfoRspDTO();
        mercCsmInfoRspDTO.setList(mercCsmInfos);
        mercCsmInfoRspDTO.setTotalNumber(pageInfo.getTotal());
        return mercCsmInfoRspDTO;
    }

    /**
     * 查询所有用户在该商户交易记录
     * @param mercBillOrderDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
    public MercUpBillOrderListDTO queryMercUpBills(MercUpBillOrderDTO mercBillOrderDTO) {

        String mercId = LemonUtils.getUserId();
        String clientMercid = mercBillOrderDTO.getMercId();
        List<MercUpBillOrderListDTO.MercBillOrder> mercBillOrders = new ArrayList<>();
        MercUpBillOrderListDTO mercBillOrderListDTO = new MercUpBillOrderListDTO();
        List<String> mercIdList = new ArrayList<>();
        if(mercBillOrderDTO.getBusOrderNo() == null || "".equals(mercBillOrderDTO.getBusOrderNo())){
            mercBillOrderDTO.setBusOrderNo(null);
        }
        if(mercBillOrderDTO.getCshOrderNo() == null || "".equals(mercBillOrderDTO.getCshOrderNo())){
            mercBillOrderDTO.setCshOrderNo(null);
        }

        //如果交易起始时间不给空，转为之间类型
        if(JudgeUtils.isNotEmpty(mercBillOrderDTO.getTxTmBeginStr())){
            mercBillOrderDTO.setTxTmBegin(DateTimeUtils.parseLocalDateTime(mercBillOrderDTO.getTxTmBeginStr() + "000000"));
        }
        //如果交易结束时间不给空，转为之间类型
        if(JudgeUtils.isNotEmpty(mercBillOrderDTO.getTxTmEndStr())){
            mercBillOrderDTO.setTxTmEnd(DateTimeUtils.parseLocalDateTime(mercBillOrderDTO.getTxTmEndStr() + "235959"));
        }

        if(JudgeUtils.isNotEmpty(clientMercid)){
            mercIdList.add(clientMercid);
            mercBillOrderDTO.setMercIdList(mercIdList);
        }else{
            GenericDTO genericDTO = GenericDTO.newInstance();
            GenericRspDTO<List<String>> genericRspDTO = new GenericRspDTO<List<String>>();
     //               userBasicInfClient.getAffiliatedList(genericDTO, mercId);
            if(JudgeUtils.isSuccess(genericRspDTO.getMsgCd())){
                mercIdList = genericRspDTO.getBody();
                logger.info("listSize:" + mercIdList.size());
                if(mercIdList != null && mercIdList.size() > 0){
                    mercBillOrderDTO.setMercIdList(mercIdList);
                }else{
                    mercIdList.add(mercId);
                    mercBillOrderDTO.setMercIdList(mercIdList);
                }
            }else{
                mercBillOrderListDTO.setTotalNum(0);
                mercBillOrderListDTO.setCurrPage(0);
                mercBillOrderListDTO.setList(mercBillOrders);
                return mercBillOrderListDTO;
            }
        }

        //根据条件查询商户账单信息，分页
        List<MercBillOrderDO> mercBillOrderListDO = null;
        //List<MercBillOrderDO> mercBillOrderListDO = PageUtils.pageQuery(mercBillOrderDTO.getPageNo(), mercBillOrderDTO.getPageSize(), ()-> mercBillOrderDao.queryMercBillOrder(mercBillOrderDTO));
        PageInfo pageInfo = PageUtils.pageQueryWithCount(mercBillOrderDTO.getPageNo(), mercBillOrderDTO.getPageSize(), ()-> mercBillOrderDao.queryMercUpBillOrder(mercBillOrderDTO));

        if(JudgeUtils.isNotNull(pageInfo)) {
            mercBillOrderListDO = pageInfo.getList();
        }
        //数据对象集合转为传输对象
        if(JudgeUtils.isNotNull(mercBillOrderListDO)) {
            for (MercBillOrderDO mercBillOrderDO : mercBillOrderListDO) {
                MercUpBillOrderListDTO.MercBillOrder mercBillOrder = new MercUpBillOrderListDTO.MercBillOrder();
                BeanUtils.copyProperties(mercBillOrder, mercBillOrderDO);
                mercBillOrders.add(mercBillOrder);
            }
        }
        mercBillOrderListDTO.setTotalNum(pageInfo.getTotal());
        mercBillOrderListDTO.setCurrPage(pageInfo.getPageNum());;
        mercBillOrderListDTO.setList(mercBillOrders);
        return mercBillOrderListDTO;
    }
}
