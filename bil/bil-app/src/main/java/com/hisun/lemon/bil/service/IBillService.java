package com.hisun.lemon.bil.service;

import com.hisun.lemon.bil.dto.CreateUserBillDTO;
import com.hisun.lemon.bil.dto.UpdateMercBillDTO;
import com.hisun.lemon.bil.dto.UpdateUserBillDTO;
import com.hisun.lemon.framework.data.GenericDTO;

/**
 * <AUTHOR>
 * @date 2017/7/26
 * @time 9:22
 */
public interface IBillService {
    /**
     * 生成用户商户账单
     * @param genericCreateUserBillListDTO
     */
    public void createUserBills(CreateUserBillDTO genericCreateUserBillListDTO);
    /**
     * 更新用户商户账单
     * @param genericUpdateUserBillListDTO
     */
    public void updateUserBill(UpdateUserBillDTO genericUpdateUserBillListDTO);
    /**
     * 更新用户商户账单清分数据
     * @param genericUpdateMercBillListDTO
     */
    public void updateMercBill(UpdateMercBillDTO genericUpdateMercBillListDTO);
}
