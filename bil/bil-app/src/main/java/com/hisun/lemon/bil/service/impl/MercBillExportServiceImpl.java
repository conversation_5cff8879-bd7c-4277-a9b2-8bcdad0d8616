package com.hisun.lemon.bil.service.impl;

import com.hisun.lemon.bil.dto.MercChkDetailExportDTO;
import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.bil.service.IMercBillExportService;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/14
 * @time 14:36
 */
@Service
public class MercBillExportServiceImpl extends BaseService implements IMercBillExportService {

    @Resource
    private MercBillExportTransactionalService mercBillExportTransactionalService;

    /**
     * 查询商户订单信息并导出excel
     * @param mercId
     * @param txTmBegin
     * @param txTmEnd
     * @param orderStat
     * @param rfdStat
     */
    @Override
    public void queryAndExport(String mercId, LocalDateTime txTmBegin, LocalDateTime txTmEnd, String orderStat, String rfdStat) {

        mercBillExportTransactionalService.queryAndExport(mercId, txTmBegin, txTmEnd, orderStat, rfdStat);
    }

    @Override
    public List<MercBillOrderDO> queryDetailByDate(MercChkDetailExportDTO mercChkDetailExportDTO){

        if(JudgeUtils.isNotEmpty(mercChkDetailExportDTO.getBeginDateStr())){
            mercChkDetailExportDTO.setBeginDate(DateTimeUtils.parseLocalDate(mercChkDetailExportDTO.getBeginDateStr()));
        }
        if(JudgeUtils.isNotEmpty(mercChkDetailExportDTO.getEndDateStr())){
            mercChkDetailExportDTO.setEndDate(DateTimeUtils.parseLocalDate(mercChkDetailExportDTO.getEndDateStr()));
        }
        String type = mercChkDetailExportDTO.getType();
        //判断日期类型
        if(JudgeUtils.isNotEmpty(type) && JudgeUtils.equals(type, "T")){
            //如果日期类型为交易日期，则设置为时间
            mercChkDetailExportDTO.setBeginTime(mercChkDetailExportDTO.getBeginDate().atTime(0,0,0));
            mercChkDetailExportDTO.setEndTime(mercChkDetailExportDTO.getEndDate().atTime(23,59,59));
        }
        return mercBillExportTransactionalService.queryDetailByDate(mercChkDetailExportDTO);
    }
}
