package com.hisun.lemon.bil.component;


import com.hisun.lemon.bil.dao.IMercBillOrderDao;
import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.lemon.jcommon.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.util.List;

/**
 * 对账文件组件
 * 
 * <AUTHOR>
 *
 */
@Component
public class ChkFileComponent {
	private static final Logger logger = LoggerFactory.getLogger(ChkFileComponent.class);
	final String yyyyMMdd="yyyyMMdd";

	final int defaultSftpTimeout=120000;

	@Resource
    private IMercBillOrderDao mercBillOrderDao;

	public List<MercBillOrderDO> queryDatas(String mercId){
        //查询商户交易成功的订单信息
		List<MercBillOrderDO> datas=mercBillOrderDao.queryData(mercId);
		return datas;
	}

	/**
	 * 获取对账数据日期
	 * @return
	 */
	public LocalDate getChkDate(){
		LocalDate today= DateTimeUtils.getCurrentLocalDate();
		return today.minusDays(1);
	}

	/**
	 * 获取对账文件名
	 * @param appCnl
	 * @param chkDate
	 * @return
	 */
	public String getChkFileName(String appCnl,LocalDate chkDate, String mercId){
		return appCnl+"_"+mercId+"_"+DateTimeUtils.formatLocalDate(chkDate,yyyyMMdd)+".ck";
	}

	/**
	 * 数据写入对账文件
	 * @param datas
	 * @param fileName
	 */
	public void writeToFile(String appCnl,List<MercBillOrderDO> datas,String fileName){
		StringBuilder contextBuilder=new StringBuilder();
		for(int i=0;i<datas.size();i++){
            MercBillOrderDO rec=datas.get(i);
            contextBuilder.append(recToLine(rec));
		}


		//写入文件
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write(contextBuilder.toString(), localPath + fileName);
		} catch (Exception e) {
			LemonException.throwBusinessException("BIL20016");
		}

	}

	/**
	 * 文件上传SFTP服务器
	 * @param chkFileName
	 * @param flagName
	 */
	public void upload(String appCnl,String chkFileName, String flagName){
		String localPath=getLocalPath(appCnl);
		String[] uploadFileNames=new String[]{localPath+chkFileName,localPath+flagName};

		String remoteIp= LemonUtils.getProperty("bil.sftp.ip");
		int remotePort=Integer.valueOf(LemonUtils.getProperty("bil.sftp.port"));
		String timeoutStr= LemonUtils.getProperty("bil.sftp.connectTimeout");
		int connectTimeout=defaultSftpTimeout;
		if(StringUtils.isNotEmpty(timeoutStr)){
			connectTimeout=Integer.valueOf(timeoutStr);
		}

		String remotePath= LemonUtils.getProperty("bil.chk.remotePath");

		String name= LemonUtils.getProperty("bil.sftp.name");
		String pwd= LemonUtils.getProperty("bil.sftp.password");

		try {
			FileSftpUtils.upload(uploadFileNames,remoteIp,remotePort,connectTimeout,remotePath,name,pwd);
		} catch (Exception e) {
			logger.error(chkFileName+"上传SFTP文件服务器失败",e);
			LemonException.throwBusinessException("BIL20018");
		}
	}


	public boolean isStart(String appCnl,String flagName){
		return new File(getLocalPath(appCnl)+flagName).exists();
	}

	public void createFlagFile(String appCnl,String flagName){
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write("flag", localPath+flagName);
		} catch (Exception e) {
			LemonException.throwBusinessException("BIL20017");
		}
	}

	/**
	 * 获取本地存放对账文件的目录，没有则创建
	 * @param appCnl
	 * @return
	 */
	private String getLocalPath(String appCnl){
		String localPath= LemonUtils.getProperty("bil.chk.localPath")+appCnl+"/";
		File localPathFile=new File(localPath);
		if(localPathFile.exists()){
			if(localPathFile.isDirectory()){
				return localPath;
			}else{
				logger.error(localPath+"已经存在，但不是目录，任务退出");
				LemonException.throwBusinessException("BIL20019");
			}
		}
		boolean success=localPathFile.mkdirs();
		if(!success){
			logger.error(localPath+"目录创建失败，任务退出");
			LemonException.throwBusinessException("BIL20019");

		}
		return localPath;
	}


	private StringBuilder recToLine(MercBillOrderDO rec){
		StringBuilder lineBuilder=new StringBuilder();
		lineBuilder.append(rec.getOrderNo());
        lineBuilder.append("|");
        lineBuilder.append(rec.getTxType());
		lineBuilder.append("|");
		lineBuilder.append(rec.getOrderAmt());
		lineBuilder.append("|");
		lineBuilder.append(rec.getTxTm());
        lineBuilder.append("|");
        lineBuilder.append(rec.getUserId());
        lineBuilder.append("|");
        lineBuilder.append(rec.getMobileNo());
        lineBuilder.append("|");
        lineBuilder.append(rec.getGoodsInfo());
        lineBuilder.append("|");
        lineBuilder.append(rec.getMercName());
        lineBuilder.append("|");
        lineBuilder.append(rec.getRfdAmt());
        lineBuilder.append("|");
        lineBuilder.append(rec.getRfdReason());
        lineBuilder.append("|");
        lineBuilder.append(rec.getOrgOrderNo());
        lineBuilder.append("|");
        lineBuilder.append(rec.getPayOrdTm());
        lineBuilder.append("|");
        lineBuilder.append(rec.getRfdOrdTm());
        lineBuilder.append("|");
        lineBuilder.append(rec.getUndoOrdTm());
        lineBuilder.append("|");
        lineBuilder.append(rec.getRemark());
		lineBuilder.append("\n");
		return lineBuilder;
	}

}
