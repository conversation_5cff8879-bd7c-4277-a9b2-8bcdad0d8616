package com.hisun.lemon.bil.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.util.Date;

public class BusinessIndicatorsDO extends BaseDO {
    /**
     * id
     */
    private Long id;

    /**
     * 交易总额
     */
    private BigDecimal amt;

    /**
     * 交易笔数
     */
    private Integer num;

    /**
     * 币种
     */
    private String ccy;

    /**
     * 交易成功率  最多5位整数和2位小数，例如100.00表示100%
     */
    private BigDecimal successRate;

    /**
     * 交易总额与昨日相比
     */
    private String amtCompareYesterday;

    /**
     * 交易笔数与昨日相比
     */
    private String numCompareYesterday;

    /**
     * 交易成功率  最多5位整数和2位小数，例如100.00表示100%
     */
    private Date trData;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getSuccessRate() {
        return successRate;
    }

    public void setSuccessRate(BigDecimal successRate) {
        this.successRate = successRate;
    }

    public Date getTrData() {
        return trData;
    }

    public void setTrData(Date trData) {
        this.trData = trData;
    }

    public String getAmtCompareYesterday() {
        return amtCompareYesterday;
    }

    public void setAmtCompareYesterday(String amtCompareYesterday) {
        this.amtCompareYesterday = amtCompareYesterday;
    }

    public String getNumCompareYesterday() {
        return numCompareYesterday;
    }

    public void setNumCompareYesterday(String numCompareYesterday) {
        this.numCompareYesterday = numCompareYesterday;
    }
}