package com.hisun.lemon.bil.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dto.UpdateMercBillDTO;
import com.hisun.lemon.bil.service.IBillService;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2017/8/16
 * @time 19:57
 */
@Component(BilConstants.SYNC_UPDATE_MERC_BEAN)
public class UpdateMercBillMessageHandler implements MessageHandler<UpdateMercBillDTO> {
    private static final Logger logger = LoggerFactory.getLogger(UpdateMercBillMessageHandler.class);
    @Resource
    private IBillService billService;

    @Resource
    ObjectMapper objectMapper;

    @Override
    public void onMessageReceive(GenericCmdDTO<UpdateMercBillDTO> genericCmdDTO) {
        String data = ObjectMapperHelper.writeValueAsString(objectMapper, genericCmdDTO, true);
        logger.info("接收订单更新同步清分数据 ：" + data);
        billService.updateMercBill(genericCmdDTO.getBody());
    }
}

