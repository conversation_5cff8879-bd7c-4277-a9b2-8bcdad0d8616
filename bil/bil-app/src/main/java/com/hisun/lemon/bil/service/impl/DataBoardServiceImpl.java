package com.hisun.lemon.bil.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.dto.DmPlatFormBalDTO;
import com.hisun.lemon.acm.dto.ItemAccountBalDTO;
import com.hisun.lemon.bil.dao.BusinessIndicatorsDao;
import com.hisun.lemon.bil.dao.ITranBillDataDao;
import com.hisun.lemon.bil.dao.IUserBillOrderDao;
import com.hisun.lemon.bil.dto.AgcyInfoListRspDTO;
import com.hisun.lemon.bil.dto.BusinessIndicatorsRspDTO;
import com.hisun.lemon.bil.dto.TransactionDataList;
import com.hisun.lemon.bil.entity.BusinessIndicatorsDO;
import com.hisun.lemon.bil.service.IDataBoardService;
import com.hisun.lemon.bil.utils.ExchangeRateUtils;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpo.client.CopAgcyBizClient;
import com.hisun.lemon.cpo.dto.CopAgcyInfoDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2017/7/26
 * @time 9:29
 */
@Service
public class DataBoardServiceImpl extends BaseService implements IDataBoardService {
    private static final Logger logger = LoggerFactory.getLogger(DataBoardServiceImpl.class);

    @Resource
    private BusinessIndicatorsDao businessIndicatorsDao;

    @Resource
    private AccountManagementClient accountManagementClient;

    @Resource
    private CopAgcyBizClient copAgcyBizClient;

    @Resource
    private IUserBillOrderDao userBillOrderDao;

    @Resource
    private ITranBillDataDao tranBillDataDao;

    @Override
    public GenericRspDTO<BusinessIndicatorsRspDTO> getBusinessIndicators(String type) {
        try {
            LocalDate startDate;
            LocalDate endDate;
            
            switch (type) {
                case "day":
                    // 获取当日的业务指标数据
                    startDate = LocalDate.now();
                    endDate = LocalDate.now();
                    break;
                case "week":
                    // 获取最近一周的业务指标数据总和
                    startDate = LocalDate.now().minusDays(7);
                    endDate = LocalDate.now();
                    break;
                case "month":
                    // 获取最近一个月的业务指标数据总和
                    startDate = LocalDate.now().minusMonths(1);
                    endDate = LocalDate.now();
                    break;
                default:
                    // 默认获取当日的业务指标数据
                    startDate = LocalDate.now();
                    endDate = LocalDate.now();
                    break;
            }
            
            // 构建返回结果
            BusinessIndicatorsRspDTO result = new BusinessIndicatorsRspDTO();
            
            if ("day".equals(type)) {
                // 查询当日的一条数据
                Date today = Date.from(LocalDate.now().atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());
                BusinessIndicatorsDO todayData = businessIndicatorsDao.findLatestByTrData(today);
                
                if (todayData != null) {
                    result.setAmt(todayData.getAmt());
                    result.setNum(todayData.getNum());
                    result.setSuccessRate(todayData.getSuccessRate());
                    result.setAmtCompareYesterday(todayData.getAmtCompareYesterday());
                    result.setNumCompareYesterday(todayData.getNumCompareYesterday());
                } else {
                    // 如果没有数据，设置默认值
                    result.setAmt(BigDecimal.ZERO);
                    result.setNum(0);
                    result.setSuccessRate(BigDecimal.ZERO);
                    result.setAmtCompareYesterday("0.00%");
                    result.setNumCompareYesterday("0.00%");
                }
            } else {
                // 查询日期范围内的数据并汇总
                Date startDateObj = Date.from(startDate.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());
                Date endDateObj = Date.from(endDate.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());
                
                List<BusinessIndicatorsDO> dataList = businessIndicatorsDao.findByDateRange(startDateObj, endDateObj);
                
                if (dataList != null && !dataList.isEmpty()) {
                    // 汇总数据
                    BigDecimal totalAmt = BigDecimal.ZERO;
                    Integer totalNum = 0;
                    BigDecimal weightedSuccessRate = BigDecimal.ZERO;
                    BigDecimal totalWeight = BigDecimal.ZERO;
                    
                    for (BusinessIndicatorsDO data : dataList) {
                        if (data.getAmt() != null) {
                            totalAmt = totalAmt.add(data.getAmt());
                        }
                        if (data.getNum() != null) {
                            totalNum += data.getNum();
                        }
                        // 计算加权成功率（以交易笔数为权重）
                        if (data.getSuccessRate() != null && data.getNum() != null && data.getNum() > 0) {
                            BigDecimal weight = new BigDecimal(data.getNum());
                            weightedSuccessRate = weightedSuccessRate.add(data.getSuccessRate().multiply(weight));
                            totalWeight = totalWeight.add(weight);
                        }
                    }
                    
                    // 设置汇总结果
                    result.setAmt(totalAmt);
                    result.setNum(totalNum);
                    
                    // 计算总体成功率（加权平均）
                    BigDecimal overallSuccessRate = BigDecimal.ZERO;
                    if (totalWeight.compareTo(BigDecimal.ZERO) > 0) {
                        overallSuccessRate = weightedSuccessRate.divide(totalWeight, 2, RoundingMode.HALF_UP);
                    }
                    result.setSuccessRate(overallSuccessRate);
                } else {
                    // 如果没有数据，设置默认值
                    result.setAmt(BigDecimal.ZERO);
                    result.setNum(0);
                    result.setSuccessRate(BigDecimal.ZERO);
                }
            }
            
            return GenericRspDTO.newSuccessInstance(result);
            
        } catch (Exception e) {
            logger.error("获取业务指标数据失败，type: {}, error: {}", type, e.getMessage(), e);
            GenericRspDTO<BusinessIndicatorsRspDTO> genericDTO = new GenericRspDTO<BusinessIndicatorsRspDTO>();
            genericDTO.setMsgCd("SYS00001");
            return genericDTO;
        }
    }

    @Override
    public GenericRspDTO<List<Map<String, String>>> getBusinessDataTrends(String startDate, String endDate) {
            // 参数验证
            if (startDate == null || endDate == null || startDate.trim().isEmpty() || endDate.trim().isEmpty()) {
                return GenericRspDTO.newSuccessInstance(new ArrayList<>());
            }

            // 解析日期 - 支持年月格式 (yyyy-MM)
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate start = LocalDate.parse(startDate + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate end = LocalDate.parse(endDate + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            // 将结束日期设置为该月的最后一天
            end = end.withDayOfMonth(end.lengthOfMonth());

            // 验证时间范围：最多12个月，最少2个月
            long monthsBetween = java.time.temporal.ChronoUnit.MONTHS.between(start, end);
            if (monthsBetween < 1 || monthsBetween > 12) {
                logger.warn("时间范围不符合要求，开始日期: {}, 结束日期: {}, 月份差: {}", startDate, endDate, monthsBetween);
                throw new LemonException("BIL00007", "时间范围不符合要求，最多12个月，最少2个月");
            }

            // 转换为Date对象
            Date startDateObj = Date.from(start.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());
            Date endDateObj = Date.from(end.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());

            // 查询月度交易总额
            List<Map<String, Object>> monthlyData = businessIndicatorsDao.findMonthlyTransactionAmount(startDateObj, endDateObj);

            // 构建返回结果
            List<Map<String, String>> result = new ArrayList<>();
            
            // 生成完整的月份列表（包含所有月份，即使没有数据）
            TreeMap<String, String> allMonths = new TreeMap<>();
            LocalDate current = start;
            while (!current.isAfter(end)) {
                String yearMonth = current.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                allMonths.put(yearMonth, "0"); // 默认金额为0
                current = current.plusMonths(1);
            }
            
            // 如果有查询到的数据，更新对应月份的金额
            if (monthlyData != null && !monthlyData.isEmpty()) {
                for (Map<String, Object> data : monthlyData) {
                    String yearMonth = (String) data.get("yearMonth");
                    Object totalAmountObj = data.get("totalAmount");
                    
                    if (yearMonth != null) {
                        // 设置交易总额
                        String totalAmount = "0";
                        if (totalAmountObj != null) {
                            if (totalAmountObj instanceof BigDecimal) {
                                BigDecimal amount = (BigDecimal) totalAmountObj;
                                totalAmount = amount.compareTo(BigDecimal.ZERO) > 0 ? amount.toString() : "0";
                            } else {
                                totalAmount = totalAmountObj.toString();
                            }
                        }
                        allMonths.put(yearMonth, totalAmount);
                    }
                }
            }
            
            // 将TreeMap转换为List，保持月份顺序
            for (Map.Entry<String, String> entry : allMonths.entrySet()) {
                Map<String, String> monthData = new HashMap<>();
                monthData.put("yearMonth", entry.getKey());
                monthData.put("totalAmount", entry.getValue());
                result.add(monthData);
            }

            return GenericRspDTO.newSuccessInstance(result);
    }

    @Override
    public GenericRspDTO<List<TransactionDataList>> getTransactionDataList() {
        try {
            logger.info("获取交易数据列表");
            LocalDate date = LocalDate.now();
            List<TransactionDataList> tranBillDataList = tranBillDataDao.queryList(date);
            GenericRspDTO<List<TransactionDataList>> genericRspDTO = GenericRspDTO.newSuccessInstance(tranBillDataList);
            return genericRspDTO;
        } catch (Exception e) {
            logger.error("获取交易数据列表失败，error: {}", e.getMessage(), e);
            GenericRspDTO<List<TransactionDataList>> genericDTO = new GenericRspDTO<>();
            genericDTO.setMsgCd("SYS00001");
            return genericDTO;
        }
    }

    @Override
    public GenericRspDTO<List<AgcyInfoListRspDTO>> getAgcyInfoList() {
        List<AgcyInfoListRspDTO> agcyInfoListRspDTOList = new ArrayList<>();

        logger.info("获取机构信息列表");
        GenericRspDTO<List<CopAgcyInfoDTO>> rspDTO = copAgcyBizClient.selectByKey();
        if(!JudgeUtils.isSuccess(rspDTO.getMsgCd())){
            logger.error("查询机构信息失败");
            return GenericRspDTO.newSuccessInstance(agcyInfoListRspDTOList);
        }
        List<CopAgcyInfoDTO> copAgcyInfoDTOList = rspDTO.getBody();
        if(JudgeUtils.isEmpty(copAgcyInfoDTOList)){
            return GenericRspDTO.newSuccessInstance(agcyInfoListRspDTOList);
        }

        // 获取数币法币总金额
        logger.info("获取财务数据");
        GenericRspDTO<Map<String, BigDecimal>> financialData = this.getFinancialData();
        if (!JudgeUtils.isSuccess(financialData.getMsgCd())) {
            logger.error("查询财务数据失败");
            return GenericRspDTO.newSuccessInstance(agcyInfoListRspDTOList);
        }
        BigDecimal fmAvailableBalance = BigDecimal.ZERO;
        BigDecimal dmAvailableBalance = BigDecimal.ZERO;
        if (financialData.getBody() != null) {
            Map<String, BigDecimal> body = financialData.getBody();
            fmAvailableBalance = body.get("USD");
            dmAvailableBalance = body.get("USDT");
            dmAvailableBalance = ExchangeRateUtils.convertToUSD(dmAvailableBalance, "USDT");
        }

        // 获取数币账户数量
        logger.info("获取数币账户数量");
        String dmCount = "0";
        GenericRspDTO<List<DmPlatFormBalDTO>> listGenericRspDTO = accountManagementClient.queryDmAccountBals();
        if (!JudgeUtils.isSuccess(listGenericRspDTO.getMsgCd())) {
            logger.error("查询数币账户数量失败");
            return GenericRspDTO.newSuccessInstance(agcyInfoListRspDTOList);
        }
        List<DmPlatFormBalDTO> body = listGenericRspDTO.getBody();
        if(JudgeUtils.isNotEmpty(body)){
            dmCount = String.valueOf(body.size());
        }

        // 获取法币账户数量
        logger.info("获取法币账户数量");
        String fmCount = "0";
        GenericRspDTO<List<ItemAccountBalDTO>> genericRspDTO = accountManagementClient.queryAllItemAccountInf();
        if (JudgeUtils.isEmpty(genericRspDTO.getBody())) {
            logger.error("查询法币账户数量失败");
            return GenericRspDTO.newSuccessInstance(agcyInfoListRspDTOList);
        }
        List<ItemAccountBalDTO> fm = genericRspDTO.getBody();
        if(JudgeUtils.isNotEmpty(fm)){
            List<ItemAccountBalDTO> fmItmBalList = fm.stream()
                    .filter(itemAccountBalDTO -> itemAccountBalDTO.getItmNo().contains("100201"))
                    .collect(Collectors.toList());
            fmCount  = String.valueOf(fmItmBalList.size());
        }

        // 初始化数据
        for (CopAgcyInfoDTO copAgcyInfoDTO : copAgcyInfoDTOList) {
            AgcyInfoListRspDTO agcyInfoListRspDTO = new AgcyInfoListRspDTO();
            agcyInfoListRspDTO.setCorpOrgNm(copAgcyInfoDTO.getCorpOrgNm());

            agcyInfoListRspDTO.setCount(fmCount);
            agcyInfoListRspDTO.setAmount(fmAvailableBalance);
            // TODO:先写死，后面要改
            if(copAgcyInfoDTO.getCorpOrgNm().contains("Cregis")){
                agcyInfoListRspDTO.setCount(dmCount);
                agcyInfoListRspDTO.setAmount(dmAvailableBalance);
            }
            agcyInfoListRspDTO.setUnAmount(BigDecimal.ZERO);
            agcyInfoListRspDTO.setAcAmount(BigDecimal.ZERO);
            agcyInfoListRspDTOList.add(agcyInfoListRspDTO);
        }
        logger.info("获取机构信息列表成功,list:{}",agcyInfoListRspDTOList);
        return GenericRspDTO.newSuccessInstance(agcyInfoListRspDTOList);
    }

    /**
     * 获取财务数据  数币可用头寸余额和法币可用头寸余额
     * @return Map<String, String>
     */
    @Override
    public GenericRspDTO<Map<String, BigDecimal>> getFinancialData() {
        Map<String, BigDecimal> map = new HashMap<>();
        // 获取数币可用头寸余额
        BigDecimal dmAvailableBalance = getDmAvailableBalance();
        // 获取法币可用头寸余额
        BigDecimal fmAvailableBalance = getFmAvailableBalance();
        map.put("USDT", dmAvailableBalance);
        map.put("USD", fmAvailableBalance);
        return GenericRspDTO.newSuccessInstance(map);
    }

    private BigDecimal getFmAvailableBalance() {
        GenericRspDTO<List<ItemAccountBalDTO>> rspDTO = accountManagementClient.queryAllItemAccountInf();
        if (JudgeUtils.isEmpty(rspDTO.getBody())) {
            return BigDecimal.ZERO;
        }

        List<ItemAccountBalDTO> body = rspDTO.getBody();
        List<ItemAccountBalDTO> fmItmBalList = body.stream()
                .filter(itemAccountBalDTO -> itemAccountBalDTO.getItmNo().contains("100201"))
                .collect(Collectors.toList());

        if (JudgeUtils.isEmpty(fmItmBalList)) {
            return BigDecimal.ZERO;
        }

        // 按币种分组，获取不同币种的余额
        Map<String, BigDecimal> currencyBalanceMap = new HashMap<>();

        for (ItemAccountBalDTO itemAccountBalDTO : fmItmBalList) {
            BigDecimal bal = itemAccountBalDTO.getTdCrBal();
            String currency = itemAccountBalDTO.getCcy();

            if (JudgeUtils.isNotNull(bal) && JudgeUtils.isNotBlank(currency)) {
                // 如果该币种已存在，累加余额
                if (currencyBalanceMap.containsKey(currency)) {
                    currencyBalanceMap.put(currency, currencyBalanceMap.get(currency).add(bal));
                } else {
                    currencyBalanceMap.put(currency, bal);
                }
            }
        }

        if (currencyBalanceMap.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 将其他币种转换为USD，计算总的法币头寸余额
        BigDecimal totalUSDBalance = BigDecimal.ZERO;

        for (Map.Entry<String, BigDecimal> entry : currencyBalanceMap.entrySet()) {
            String currency = entry.getKey();
            BigDecimal amount = entry.getValue();

            // 将其他币种转换为USD
            BigDecimal usdAmount = ExchangeRateUtils.convertToUSD(amount, currency);
            totalUSDBalance = totalUSDBalance.add(usdAmount);

            logger.info("法币头寸 - 币种: {}, 原始余额: {}, 转换为USD: {}",
                currency, amount, usdAmount);
        }

        logger.info("法币头寸总余额(USD): {}", totalUSDBalance);
        return totalUSDBalance;
    }

    private BigDecimal getDmAvailableBalance() {
        logger.info("开始计算数币头寸余额");

        GenericRspDTO<List<DmPlatFormBalDTO>> rspDTO = accountManagementClient.queryDmAccountBals();
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            logger.info("查询数币科目账户余额信息失败,msgCd: {}", rspDTO.getMsgCd());
            return BigDecimal.ZERO;
        }
        List<DmPlatFormBalDTO> body = rspDTO.getBody();
        if (JudgeUtils.isNull(body)) {
            logger.info("查询科目账户余额信息为空");
            return BigDecimal.ZERO;
        }

        // 按币种分组，获取不同币种的余额
        Map<String, BigDecimal> currencyBalanceMap = new HashMap<>();

        for (DmPlatFormBalDTO dmPlatFormDO : body) {
            try {
                // 获取platformAcNo和余额
                String platformAcNo = dmPlatFormDO.getPlatformAcNo();
                BigDecimal balance = dmPlatFormDO.getAcmBal();

                if (JudgeUtils.isNotBlank(platformAcNo) && JudgeUtils.isNotNull(balance)) {
                    // 根据platformAcNo的前4位确定币种
                    String firstFourLetters = platformAcNo.substring(0, Math.min(4, platformAcNo.length()));
                    String currency = firstFourLetters.toUpperCase();

                    // 如果该币种已存在，累加余额
                    if (currencyBalanceMap.containsKey(currency)) {
                        currencyBalanceMap.put(currency, currencyBalanceMap.get(currency).add(balance));
                    } else {
                        currencyBalanceMap.put(currency, balance);
                    }

                    logger.info("数币头寸 - 平台账户: {}, 币种: {}, 余额: {}", platformAcNo, currency, balance);
                }
            } catch (Exception e) {
                logger.warn("处理数币账户余额数据时出错: {}", e.getMessage());
            }
        }

        if (currencyBalanceMap.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 将其他币种转换为USDT，计算总的数币头寸余额
        BigDecimal totalUSDTBalance = BigDecimal.ZERO;

        for (Map.Entry<String, BigDecimal> entry : currencyBalanceMap.entrySet()) {
            String currency = entry.getKey();
            BigDecimal amount = entry.getValue();

            // 将其他币种转换为USDT
            BigDecimal usdtAmount = ExchangeRateUtils.convertToUSDT(amount, currency);
            totalUSDTBalance = totalUSDTBalance.add(usdtAmount);

            logger.info("数币头寸 - 币种: {}, 原始余额: {}, 转换为USDT: {}",
                    currency, amount, usdtAmount);
        }

        logger.info("数币头寸总余额(USDT): {}", totalUSDTBalance);
        return totalUSDTBalance;
    }
}
