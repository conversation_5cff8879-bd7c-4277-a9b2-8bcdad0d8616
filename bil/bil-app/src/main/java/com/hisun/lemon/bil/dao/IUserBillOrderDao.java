package com.hisun.lemon.bil.dao;

import com.hisun.lemon.bil.dto.BillTypeDTO;
import com.hisun.lemon.bil.dto.DhDataDTO;
import com.hisun.lemon.bil.dto.UserBillOrderDTO;
import com.hisun.lemon.bil.dto.UserOrderRecordDTO;
import com.hisun.lemon.bil.entity.UserBillOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/7/10
 * @time 9:55
 */
@Mapper
@Component
public interface IUserBillOrderDao extends BaseDao<UserBillOrderDO> {

    /**
     * 普通用户查询用户所有账单
     * @param userBillOrderDTO
     * @return
     */
    List<UserBillOrderDO> queryUserBillOrder(UserBillOrderDTO userBillOrderDTO);

    /**
     * 查询用户账单交易明细
     * @param orderNo
     * @param userId
     * @return
     */
    UserBillOrderDO queryBillInfo(@Param("orderNo") String orderNo, @Param("userId") String userId);

    /**
     * 查询用户账户信息
     * @param userId
     * @return
     */
    UserBillOrderDO queryUserAccount(String userId);

    /**
     * 商家用户查询用户所有账单
     * @param userBillOrderDTO
     * @return
     */
    List<UserBillOrderDO> queryUserBillOrder2(UserBillOrderDTO userBillOrderDTO);

    /**
     * 查询订单是否存在
     * @param orderNo
     * @return
     */
    int query(String orderNo);

    /**
     * 查询用户账户流水
     * @param userOrderRecordDTO
     * @return
     */
    List<UserBillOrderDO> queryUserOrderRecord(UserOrderRecordDTO userOrderRecordDTO);

    /**
     * 查询数币账户收款/充值订单集合
     *
     * @param acNo 账户编号
     * @param txType 交易类型
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    List<String> findOrderNos(@Param("acNo") String acNo,
                              @Param("txType") String txType,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定日期范围内的交易统计
     * 按币种分组统计交易总额、交易笔数、成功交易笔数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 按币种分组的交易统计
     */
    @MapKey("currency")
    List<Map<String, Object>> queryTransactionStatisticsByCurrency(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定日期范围内的交易统计（所有币种汇总）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 交易统计汇总
     */
    @MapKey("currency")
    Map<String, Object> queryTransactionStatisticsSummary(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 查询昨日交易统计（用于比较趋势）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 昨日交易统计
     */
    @MapKey("currency")
    Map<String, Object> queryYesterdayTransactionStatistics(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    List<DhDataDTO> DhTypeList(@Param("txType") String txType,
                               @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    List<BillTypeDTO> getBillByType(@Param("txTypeList") List<String> txTypeList,
                                    @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);
}
