package com.hisun.lemon.bil.service.impl;

import com.hisun.lemon.bil.dao.IMercBillOrderDao;
import com.hisun.lemon.bil.dao.IUserBillOrderDao;
import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.bil.entity.UserBillOrderDO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2017/7/26
 * @time 15:52
 */
@Transactional
@Service
public class BillTransactionalService extends BaseService {
    private static final Logger logger = LoggerFactory.getLogger(BillTransactionalService.class);

    @Resource
    private IUserBillOrderDao userBillOrderDao;

    @Resource
    private IMercBillOrderDao mercBillOrderDao;

    /**
     * 生成用户商户账单
     * @param userBillOrderDO
     */
    public void createUserBill(UserBillOrderDO userBillOrderDO, MercBillOrderDO mercBillOrderDO){

        if(JudgeUtils.isNotNull(userBillOrderDO)) {
            int nums = userBillOrderDao.insert(userBillOrderDO);
            if (nums != 1) {
                throw new LemonException("BIL20007");
            }
        }
        if(JudgeUtils.isNotNull(mercBillOrderDO)){
            int mercNums = mercBillOrderDao.insert(mercBillOrderDO);
            if(mercNums != 1){
                throw new LemonException("BIL20010");
            }
        }
    }

    /**
     * 查询订单编号是否存在
     * @param orderNo
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
    public String query(String orderNo, boolean flag){
        int userNums = userBillOrderDao.query(orderNo);
        int mercNums = mercBillOrderDao.query(orderNo);
        if(flag){
            if (mercNums == 1 && userNums == 1) {
                throw new LemonException("BIL20008");
            }
            return userNums+"-"+mercNums;
        }else {
            if (mercNums == 1 || userNums == 1) {
                throw new LemonException("BIL20008");
            }
            return null;
        }
    }

    /**
     * 更新用户个人账单信息
     * @param userBillOrderDO
     */
    public void updateUserBill(UserBillOrderDO userBillOrderDO, MercBillOrderDO mercBillOrderDO){
        if(JudgeUtils.isNotNull(userBillOrderDO)) {
            int nums = userBillOrderDao.update(userBillOrderDO);
            if (nums != 1) {
                throw new LemonException("BIL20009");
            }
        }
        if(JudgeUtils.isNotNull(mercBillOrderDO)){
            int mercNums = mercBillOrderDao.update(mercBillOrderDO);
            if(mercNums != 1){
                throw new LemonException("BIL20011");
            }
        }
    }

    /**
     * 查询用户账单编号是否存在
     * @param userOrderNo
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
    public UserBillOrderDO queryUserBill(String userOrderNo){

        UserBillOrderDO userBillOrderDO = userBillOrderDao.get(userOrderNo);
        return userBillOrderDO;
    }

    /**
     * 查询订单编号是否存在
     * @param mercOrderNo
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
    public MercBillOrderDO queryMercBill(String mercOrderNo){

        MercBillOrderDO mercBillOrderDO = mercBillOrderDao.get(mercOrderNo);
        return mercBillOrderDO;
    }

    /**
     * 更新商户账单清分信息
     * @param mercBillOrderDO
     */
    public void updateMercBill(MercBillOrderDO mercBillOrderDO) {
        int mercNums = mercBillOrderDao.update(mercBillOrderDO);
        if(mercNums != 1){
            throw new LemonException("BIL20011");
        }
    }
}
