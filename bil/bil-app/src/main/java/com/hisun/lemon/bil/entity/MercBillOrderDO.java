/*
 * @ClassName MercBillOrderDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-20 19:59:05
 */
package com.hisun.lemon.bil.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class MercBillOrderDO extends BaseDO {
    /**
     * @Fields orderNo 订单号
     */
    private String orderNo;
    /**
     * @Fields txTm 交易时间
     */
    private LocalDateTime txTm;
    /**
     * @Fields txType 交易类型 01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费
     */
    private String txType;
    /**
     * @Fields orderAmt 订单金额
     */
    private BigDecimal orderAmt;
    /**
     * @Fields orderStatus 'W'待支付、'P'支付待确认、'S'成功、'F'失败、'R1'部分退款、'R2'全部退款、'E'超时、'C'撤销
     */
    private String orderStatus;
    /**
     * @Fields mobileNo 手机号码
     */
    private String mobileNo;
    /**
     * @Fields mercId 商户编号
     */
    private String mercId;
    /**
     * 商户名称
     */
    private String mercName;
    /**
     * @Fields userId 付款方
     */
    private String userId;
    /**
     * @Fields goodsName 商品名称
     */
    private String goodsInfo;
    /**
     * @Fields busType 业务类型 '0201'条码支付、'0202'扫码支付、'0203'APP支付、'0204'POS支付、'0205'银行卡收单
     */
    private String busType;
    /**
     * @Fields busSubType 业务子类型
     */
    private String busSubType;
    /**
     * @Fields payMod 支付方式 01现金账户、02银行卡、03微信支付、04支付宝
     */
    private String payMod;
    /**
     * 现金账户余额
     */
    private BigDecimal cashBalAmt;
    /**
     * 优惠金额
     */
    private BigDecimal couponAmt;
    /**
     * 退款金额
     */
    private BigDecimal rfdAmt;
    /**
     * 优惠类型
     */
    private String couponType;

    /**
     * 退款原因
     */
    private String rfdReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 退款时间
     */
    private LocalDateTime rfdOrdTm;
    /**
     * 支付时间
     */
    private LocalDateTime payOrdTm;
    /**
     * 撤销时间
     */
    private LocalDateTime undoOrdTm;
    /**
     * 银行卡补款
     */
    private BigDecimal crdPayAmt;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 补款类型
     */
    private String crdPayType;
    /**
     * 原订单号
     */
    private String orgOrderNo;
    /**
     * 商户订单号
     */
    private String busOrderNo;
    /**
     * 操作员
     */
    private String oprName;
    /**
     * 已退优惠金额，通过优惠类型判断
     */
    private BigDecimal rfdSeaCoin;
    /**
     * 清分日期
     */
    private LocalDate checkDate;
    /**
     * 商户服务费
     */
    private BigDecimal serveFee;
    /**
     * 支付渠道
     */
    private String orderChannel;
    /**
     * 交易日期
     */
    private LocalDate txDate;
    /**
     * 交易总笔数
     */
    private Integer count;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getGoodsInfo() {
        return goodsInfo;
    }

    public void setGoodsInfo(String goodsInfo) {
        this.goodsInfo = goodsInfo;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getPayMod() {
        return payMod;
    }

    public void setPayMod(String payMod) {
        this.payMod = payMod;
    }

    public BigDecimal getCashBalAmt() {
        return cashBalAmt;
    }

    public void setCashBalAmt(BigDecimal cashBalAmt) {
        this.cashBalAmt = cashBalAmt;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getRfdReason() {
        return rfdReason;
    }

    public void setRfdReason(String rfdReason) {
        this.rfdReason = rfdReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getPayOrdTm() {
        return payOrdTm;
    }

    public void setPayOrdTm(LocalDateTime payOrdTm) {
        this.payOrdTm = payOrdTm;
    }

    public LocalDateTime getUndoOrdTm() {
        return undoOrdTm;
    }

    public void setUndoOrdTm(LocalDateTime undoOrdTm) {
        this.undoOrdTm = undoOrdTm;
    }

    public BigDecimal getCrdPayAmt() {
        return crdPayAmt;
    }

    public void setCrdPayAmt(BigDecimal crdPayAmt) {
        this.crdPayAmt = crdPayAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getCrdPayType() {
        return crdPayType;
    }

    public void setCrdPayType(String crdPayType) {
        this.crdPayType = crdPayType;
    }

    public String getOrgOrderNo() {
        return orgOrderNo;
    }

    public void setOrgOrderNo(String orgOrderNo) {
        this.orgOrderNo = orgOrderNo;
    }

    public String getBusSubType() {
        return busSubType;
    }

    public void setBusSubType(String busSubType) {
        this.busSubType = busSubType;
    }

    public BigDecimal getRfdAmt() {
        return rfdAmt;
    }

    public void setRfdAmt(BigDecimal rfdAmt) {
        this.rfdAmt = rfdAmt;
    }

    public LocalDateTime getRfdOrdTm() {
        return rfdOrdTm;
    }

    public void setRfdOrdTm(LocalDateTime rfdOrdTm) {
        this.rfdOrdTm = rfdOrdTm;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getOprName() {
        return oprName;
    }

    public void setOprName(String oprName) {
        this.oprName = oprName;
    }

    public BigDecimal getRfdSeaCoin() {
        return rfdSeaCoin;
    }

    public void setRfdSeaCoin(BigDecimal rfdSeaCoin) {
        this.rfdSeaCoin = rfdSeaCoin;
    }

    public LocalDate getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(LocalDate checkDate) {
        this.checkDate = checkDate;
    }

    public BigDecimal getServeFee() {
        return serveFee;
    }

    public void setServeFee(BigDecimal serveFee) {
        this.serveFee = serveFee;
    }

    public String getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(String orderChannel) {
        this.orderChannel = orderChannel;
    }

    public LocalDate getTxDate() {
        return txDate;
    }

    public void setTxDate(LocalDate txDate) {
        this.txDate = txDate;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}