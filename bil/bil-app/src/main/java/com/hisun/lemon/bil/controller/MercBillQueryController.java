package com.hisun.lemon.bil.controller;

import javax.annotation.Resource;

import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.interceptor.MercOprAccess;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hisun.lemon.bil.service.IMercBillOrderService;

import io.swagger.annotations.*;


/**
 * <AUTHOR>
 * @date 2017/7/12
 * @time 11:57
 */
@Api(value = "商户订单查询")
@RestController
@RequestMapping(value= "/bil/merc")
public class MercBillQueryController extends BaseController {

    @Resource
    private IMercBillOrderService mercBillOrderService;

    @ApiOperation(value = "商户订单查询", notes = "查询所有用户在该商户交易记录")
    @ApiResponse(code = 200, message = "商户订单查询结果")
    @GetMapping(value = "/bill/all")
    @MercOprAccess(role = 9)
    public GenericRspDTO<MercBillOrderListDTO> queryMercBills(@Validated MercBillOrderDTO mercBillOrderDTO){

        //填充用户ID
        //mercBillOrderDTO.setMercId(LemonUtils.getUserId());
        MercBillOrderListDTO mercBillOrderListDTO = mercBillOrderService.queryMercBills(mercBillOrderDTO);
        return GenericRspDTO.newSuccessInstance(mercBillOrderListDTO);
    }

    @ApiOperation(value = "商户订单查询", notes = "查询商户交易记录")
    @ApiResponse(code = 200, message = "商户订单查询结果")
    @GetMapping(value = "/bill/list")
    @MercOprAccess(role = 9)
    public GenericRspDTO<MercBillOrderListDTO> queryMercBillsList(@Validated MercBillOrderDTO mercBillOrderDTO){

        //填充用户ID
        //mercBillOrderDTO.setMercId(LemonUtils.getUserId());
        MercBillOrderListDTO mercBillOrderListDTO = mercBillOrderService.queryMercBills(mercBillOrderDTO);
        return GenericRspDTO.newSuccessInstance(mercBillOrderListDTO);
    }

    @ApiOperation(value = "商户账户订单交易详情查询", notes = "查询用户在该商户单条订单交易记录明细")
    @ApiResponse(code = 200, message = "单条订单交易详情查询结果")
    @GetMapping(value = "/bill")
    public GenericRspDTO<MercBillOrderListDTO> queryMercDetails(@Validated MercBillInfoDTO mercBillInfoDTO){

        MercBillOrderListDTO mercBillOrderListDTO = mercBillOrderService.queryMercDetails(mercBillInfoDTO);
        return GenericRspDTO.newSuccessInstance(mercBillOrderListDTO);
    }

    @ApiOperation(value = "商户账户订单交易详情查询", notes = "查询商户单条订单交易记录明细")
    @ApiResponse(code = 200, message = "单条订单交易详情查询结果")
    @GetMapping(value = "/bill/third")
    @MercOprAccess(role = 9)
    public GenericRspDTO<MercBillOrderListDTO> queryThirdMercDetails(@Validated MercBillInfoDTO mercBillInfoDTO){

        MercBillOrderListDTO mercBillOrderListDTO = mercBillOrderService.queryMercDetails(mercBillInfoDTO);
        return GenericRspDTO.newSuccessInstance(mercBillOrderListDTO);
    }

    @ApiOperation(value = "商户账户信息查询", notes = "查询商户的现金账户、专用支出账户余额")
    @ApiResponse(code = 200, message = "商户账户信息查询结果")
    @GetMapping(value = "/act")
    public GenericRspDTO<MercAccountRspDTO> queryMercAccount(@Validated MercAccountDTO mercAccountDTO){

        mercAccountDTO.setMercId(LemonUtils.getUserId());
        MercAccountRspDTO mercAccountRspDTO = mercBillOrderService.queryMercAccount(mercAccountDTO);
        return GenericRspDTO.newSuccessInstance(mercAccountRspDTO);
    }

    @ApiOperation(value = "商户账单清分数据查询", notes = "查询商户账单的情分数据")
    @ApiResponse(code = 200, message = "商户账单清分数据查询结果")
    @GetMapping(value = "/detail")
    @MercOprAccess(role = 9)
    public GenericRspDTO<MercCsmInfoRspDTO> queryMercCsmDetails(@Validated MercCsmInfoDTO mercCsmInfoDTO){

        MercCsmInfoRspDTO mercCsmInfoRspDTO = mercBillOrderService.queryMercCsmDetails(mercCsmInfoDTO);
        return GenericRspDTO.newSuccessInstance(mercCsmInfoRspDTO);
    }

    @ApiOperation(value = "上级商户查询交易订单", notes = "上级商户查询交易订单")
    @ApiResponse(code = 200, message = "商户订单查询结果")
    @GetMapping(value = "/bill/up/list")
    @MercOprAccess(role = 9)
    public GenericRspDTO<MercUpBillOrderListDTO> queryMercUpBillsList(@Validated MercUpBillOrderDTO mercUpBillOrderDTO){

        MercUpBillOrderListDTO mercBillOrderListDTO = mercBillOrderService.queryMercUpBills(mercUpBillOrderDTO);
        return GenericRspDTO.newSuccessInstance(mercBillOrderListDTO);
    }
}
