package com.hisun.lemon.bil.quartz.job;

import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dao.ITranBillDataDao;
import com.hisun.lemon.bil.dao.IUserBillOrderDao;
import com.hisun.lemon.bil.dto.BillTypeDTO;
import com.hisun.lemon.bil.dto.DhDataDTO;
import com.hisun.lemon.bil.entity.TranBillDataDO;
import com.hisun.lemon.bil.quartz.util.SpringContextUtils;
import com.hisun.lemon.bil.utils.ExchangeRateUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.tam.client.ExchangeOrderClient;
import com.hisun.lemon.tam.dto.ExchangeRateRspDTO;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * <AUTHOR>
 * @description 运营端首页-交易数据统计-定时任务
 * @title BusinessIndicatorsJob
 * @date 2025/8/5 10:56
 */
@Component
public class TransactionDataStatisticsJob implements Job {

    private static final Logger logger = LoggerFactory.getLogger(TransactionDataStatisticsJob.class);


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            logger.info("【交易数据统计】定时任务启动 - 时间: {}",
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            IUserBillOrderDao userBillOrderDao = SpringContextUtils.getBean(IUserBillOrderDao.class);
            ITranBillDataDao tranBillDataDao = SpringContextUtils.getBean(ITranBillDataDao.class);

            // 初始化汇率
            Map<String, BigDecimal> safeMap = ExchangeRateUtils.initExchangeRate();
            ExchangeOrderClient client = SpringContextUtils.getBean(ExchangeOrderClient.class);
            logger.info("获取实时汇率");
            GenericRspDTO<List<ExchangeRateRspDTO>> rspDTO = client.queryExchangeOrderList("USD");
            if (JudgeUtils.isSuccess(rspDTO.getMsgCd())) {
                List<ExchangeRateRspDTO> rateList = rspDTO.getBody();
                for (ExchangeRateRspDTO rate : rateList) {
                    safeMap.put(rate.getFromCoin(), rate.getExchangeRate());
                }
            }
            logger.info("实时汇率，rate:{}",safeMap);

            // 获取当前时间
            LocalDateTime startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            // 获取第二天的时间
            LocalDateTime endTime = startTime.plusDays(1);
            List<TranBillDataDO> tranBillDataList = new ArrayList<>();

            // 获取兑换的统计数据
            logger.info("获取兑换的统计数据start:{},end:{}", startTime, endTime);
            this.createDhData(tranBillDataList, startTime, endTime, userBillOrderDao, safeMap);

            // 获取充值的统计数据
            logger.info("获取充值的统计数据start:{},end:{}", startTime, endTime);
            this.createDcData(tranBillDataList, startTime, endTime, userBillOrderDao, safeMap);

            // 获取转账的统计数据
            logger.info("获取转账的统计数据start:{},end:{}", startTime, endTime);
            this.createDzData(tranBillDataList, startTime, endTime, userBillOrderDao, safeMap);

            // 获取提现的统计数据
            logger.info("获取提现的统计数据start:{},end:{}", startTime, endTime);
            this.createDxData(tranBillDataList, startTime, endTime, userBillOrderDao, safeMap);

            // 获取收款的统计数据
            logger.info("获取收款的统计数据start:{},end:{}", startTime, endTime);
            this.createDsData(tranBillDataList, startTime, endTime, userBillOrderDao, safeMap);

            // 获取手续费的统计数据
            logger.info("获取手续费的统计数据start:{},end:{}", startTime, endTime);
            this.createSxfData(tranBillDataList, startTime, endTime, userBillOrderDao, safeMap);

            // 批量插入获取到的统计数据
            if (!JudgeUtils.isEmpty(tranBillDataList)) {
                LocalDate localDate = startTime.toLocalDate();
                logger.info("批量删除之前的统计数据,日期:{}", localDate);
                tranBillDataDao.deleteByDate(localDate);
                logger.info("批量插入获取到的统计数据:{}", tranBillDataList);
                tranBillDataDao.batchInsert(tranBillDataList);
            }

            logger.info("【交易数据统计】任务执行成功，时间：{}",
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } catch (Exception e) {
            logger.error("交易数据统计定时任务执行失败", e);
            throw new JobExecutionException("交易数据统计定时任务执行失败", e);
        }
    }

    /**
     * 获取手续费的统计数据
     *
     *
     * 1. 今日入账 - 所有交易的手续费收入（币种金额全部转换为USD）
     *    1. 数币手续费收入
     *    2. 法币手续费收入
     * 2. 今日出账 - 调用平台的手续费支出（币种金额全部转换为USD）
     *    1. 数币手续费收入 （调用Cregis的Gas费）
     *    2. 法币手续费收入（调用法币通道需要支付的手续费）  暂无
     *
     * @param tranBillDataList
     * @param startTime
     * @param endTime
     * @param userBillOrderDao
     * @param safeMap
     */
    private void createSxfData(List<TranBillDataDO> tranBillDataList, LocalDateTime startTime, LocalDateTime endTime, IUserBillOrderDao userBillOrderDao, Map<String, BigDecimal> safeMap) {
        List<String> txTypeList = new ArrayList<>();

        logger.info("获取手续费的统计数据,txType数量:{},start:{},end:{}", txTypeList, startTime, endTime);
        List<BillTypeDTO> billList = userBillOrderDao.getBillByType(txTypeList, startTime, endTime);

        TranBillDataDO tranBillDataDO = new TranBillDataDO();
        tranBillDataDO.setInAmt(BigDecimal.ZERO);
        tranBillDataDO.setOutAmt(BigDecimal.ZERO);
        for (BillTypeDTO billTypeDTO : billList) {
            // 将手续费金额转换为USD,并设置入账
            billTypeDTO.setOrderAmt(ExchangeRateUtils.convertToUSD(billTypeDTO.getFee(), billTypeDTO.getCcy(), safeMap));
            tranBillDataDO.setInAmt(tranBillDataDO.getInAmt().add(billTypeDTO.getOrderAmt()));

            // 将手续费金额转换为USD,并设置出账
            // TODO: 后续需调用法币通道获取法币手续费收入
            billTypeDTO.setOrderAmtFee(ExchangeRateUtils.convertToUSD(billTypeDTO.getGas(), billTypeDTO.getCcy(), safeMap));
            tranBillDataDO.setOutAmt(tranBillDataDO.getOutAmt().add(billTypeDTO.getOrderAmtFee()));
        }
        tranBillDataDO.setCcy("USD");
        tranBillDataDO.setTrDate(startTime.toLocalDate());
        tranBillDataDO.setTxType("SXF");
        tranBillDataList.add(tranBillDataDO);

    }

    /**
     * 获取收款的统计数据
     *
     * 1. 今日入账 - 用户收款金额总和（币种金额全部转换为USD）
     *    1. 数币收款
     * 2. 今日出账 - 0
     *
     * @param tranBillDataList
     * @param startTime
     * @param endTime
     * @param userBillOrderDao
     * @param safeMap
     */
    private void createDsData(List<TranBillDataDO> tranBillDataList, LocalDateTime startTime, LocalDateTime endTime, IUserBillOrderDao userBillOrderDao, Map<String, BigDecimal> safeMap) {
        List<String> txTypeList = new ArrayList<>();
        txTypeList.add(BilConstants.TX_TYPE_DM_RECEIVE);

        logger.info("获取收款的统计数据,txType数量:{},start:{},end:{}", txTypeList, startTime, endTime);
        List<BillTypeDTO> billList = userBillOrderDao.getBillByType(txTypeList, startTime, endTime);

        TranBillDataDO tranBillDataDO = new TranBillDataDO();
        tranBillDataDO.setInAmt(BigDecimal.ZERO);
        tranBillDataDO.setOutAmt(BigDecimal.ZERO);
        for (BillTypeDTO billTypeDTO : billList) {
            // 扣除手续费，并转换为USD
            billTypeDTO.setOrderAmt(billTypeDTO.getOrderAmt().subtract(billTypeDTO.getFee()));
            billTypeDTO.setOrderAmt(ExchangeRateUtils.convertToUSD(billTypeDTO.getOrderAmt(), billTypeDTO.getCcy(), safeMap));

            tranBillDataDO.setInAmt(tranBillDataDO.getInAmt().add(billTypeDTO.getOrderAmt()));
        }
        tranBillDataDO.setCcy("USD");
        tranBillDataDO.setTrDate(startTime.toLocalDate());
        tranBillDataDO.setOutAmt(BigDecimal.ZERO);
        tranBillDataDO.setTxType(BilConstants.TX_TYPE_DM_RECEIVE);
        tranBillDataList.add(tranBillDataDO);
    }

    /**
     * 获取提现的统计数据
     *
     * 1. 今日入账 - 0
     * 2. 今日出账 - 用户提现金额总和（币种金额全部转换为USD）
     *    1. 数币提现和法币提现
     *
     * @param tranBillDataList
     * @param startTime
     * @param endTime
     * @param userBillOrderDao
     * @param safeMap
     */
    private void createDxData(List<TranBillDataDO> tranBillDataList, LocalDateTime startTime, LocalDateTime endTime, IUserBillOrderDao userBillOrderDao, Map<String, BigDecimal> safeMap) {
        List<String> txTypeList = new ArrayList<>();
        // 填充提现数据类型 04提现 DX提现
        txTypeList.add(BilConstants.TX_TYPE_WITHDRAW);
        txTypeList.add(BilConstants.TX_TYPE_DM_WITHDRAW);

        // 获取提现数据
        logger.info("获取转账数据,txType数量:{},start:{},end:{}", txTypeList, startTime, endTime);
        List<BillTypeDTO> billList = userBillOrderDao.getBillByType(txTypeList, startTime, endTime);

        TranBillDataDO tranBillDataDO = new TranBillDataDO();
        tranBillDataDO.setInAmt(BigDecimal.ZERO);
        tranBillDataDO.setOutAmt(BigDecimal.ZERO);
        for (BillTypeDTO billTypeDTO : billList) {
            // 将提现金额转换为USD
            billTypeDTO.setOrderAmt(ExchangeRateUtils.convertToUSD(billTypeDTO.getOrderAmt(), billTypeDTO.getCcy(), safeMap));

            tranBillDataDO.setOutAmt(tranBillDataDO.getOutAmt().add(billTypeDTO.getOrderAmt()));
        }
        tranBillDataDO.setCcy("USD");
        tranBillDataDO.setTrDate(startTime.toLocalDate());
        tranBillDataDO.setTxType(BilConstants.TX_TYPE_DM_WITHDRAW);
        tranBillDataDO.setInAmt(BigDecimal.ZERO);
        tranBillDataList.add(tranBillDataDO);
    }


    /**
     * 获取转账的统计数据
     *
     * 1.今日入账-0
     * 2.今日出账 -数币链上转账交易金额i(币种金额全部转换为USD)
     *
     * @param tranBillDataList 统计数据
     * @param userBillOrderDao
     * @param safeMap
     */
    private void createDzData(List<TranBillDataDO> tranBillDataList, LocalDateTime startTime, LocalDateTime endTime, IUserBillOrderDao userBillOrderDao, Map<String, BigDecimal> safeMap) {
        List<String> txTypeList = new ArrayList<>();
        // 填充充值数据类型 03充值 DZ数币转账
        txTypeList.add(BilConstants.TX_TYPE_TRANSFER);
        txTypeList.add(BilConstants.TX_TYPE_DM_TRANSFER);

        // 获取转账数据
        logger.info("获取转账数据,txType数量:{},start:{},end:{}", txTypeList.size(), startTime, endTime);
        List<BillTypeDTO> billList = userBillOrderDao.getBillByType(txTypeList, startTime, endTime);

        TranBillDataDO tranBillDataDO = new TranBillDataDO();
        tranBillDataDO.setInAmt(BigDecimal.ZERO);
        tranBillDataDO.setOutAmt(BigDecimal.ZERO);
        for (BillTypeDTO billTypeDTO : billList) {
            // 只统计链上转账
            if ("DZ02".equals(billTypeDTO.getBusType()) || "DZ03".equals(billTypeDTO.getBusType())) {
                billTypeDTO.setOrderAmt(ExchangeRateUtils.convertToUSD(billTypeDTO.getOrderAmt(), billTypeDTO.getCcy(), safeMap));

                tranBillDataDO.setOutAmt(tranBillDataDO.getOutAmt().add(billTypeDTO.getOrderAmt()));
            }
        }
        tranBillDataDO.setCcy("USD");
        tranBillDataDO.setTrDate(startTime.toLocalDate());
        tranBillDataDO.setTxType(BilConstants.TX_TYPE_DM_TRANSFER);
        tranBillDataDO.setInAmt(BigDecimal.ZERO);
        tranBillDataList.add(tranBillDataDO);
    }


    /**
     * 获取充值的数据
     *
     * 1. 今日入账 - 用户充值金额总和（币种金额全部转换为USD）
     *     1. 数币充值和法币线下汇款充值
     * 2. 今日出账 - 0
     *
     * @param tranBillDataList 统计数据
     * @param startTime        当天数据的开始时间
     * @param endTime          当天数据的结束时间
     * @param userBillOrderDao
     * @param safeMap
     */
    private void createDcData(List<TranBillDataDO> tranBillDataList, LocalDateTime startTime, LocalDateTime endTime, IUserBillOrderDao userBillOrderDao, Map<String, BigDecimal> safeMap) {
        List<String> txTypeList = new ArrayList<>();
        // 填充充值数据类型 01充值 DC数币充值
        txTypeList.add(BilConstants.TX_TYPE_RECHANGE);
        txTypeList.add(BilConstants.TX_TYPE_DM_RECHARGE);

        // 获取充值数据
        logger.info("获取充值数据,txType数量:{},start:{},end:{}", txTypeList.size(), startTime, endTime);
        List<BillTypeDTO> billList = userBillOrderDao.getBillByType(txTypeList, startTime, endTime);

        TranBillDataDO tranBillDataDO = new TranBillDataDO();
        tranBillDataDO.setInAmt(BigDecimal.ZERO);
        tranBillDataDO.setOutAmt(BigDecimal.ZERO);
        for (BillTypeDTO billTypeDTO : billList) {
            // 减去手续费
            billTypeDTO.setOrderAmt(billTypeDTO.getOrderAmt().subtract(billTypeDTO.getFee()));
            // 将充值金额转换为USD
            billTypeDTO.setOrderAmt(ExchangeRateUtils.convertToUSD(billTypeDTO.getOrderAmt(), billTypeDTO.getCcy(), safeMap));

            tranBillDataDO.setInAmt(tranBillDataDO.getInAmt().add(billTypeDTO.getOrderAmt()));
        }
        tranBillDataDO.setCcy("USD");
        tranBillDataDO.setTrDate(startTime.toLocalDate());
        tranBillDataDO.setTxType(BilConstants.TX_TYPE_DM_RECHARGE);
        tranBillDataDO.setOutAmt(BigDecimal.ZERO);
        tranBillDataList.add(tranBillDataDO);
    }

    /**
     * 获取兑换的数据
     *
     * 1. 今日入账 - 用户卖出币种金额总和（币种金额全部转换为USD）
     * 2. 今日出账 - 用户买入币种金额总和（币种金额全部转换为USD）
     * @param tranBillDataList 统计数据
     * @param startTime        当天数据的开始时间
     * @param endTime          当天数据的结束时间
     * @param userBillOrderDao
     * @param safeMap
     */
    private void createDhData(List<TranBillDataDO> tranBillDataList, LocalDateTime startTime, LocalDateTime endTime, IUserBillOrderDao userBillOrderDao, Map<String, BigDecimal> safeMap) {
        String txType = BilConstants.TX_TYPE_DM_EXCHANGE;
        // 获取兑换订单数据
        logger.info("获取兑换订单数据,txType:{},start:{},end:{}", txType, startTime, endTime);
        List<DhDataDTO> list = userBillOrderDao.DhTypeList(txType, startTime, endTime);

        TranBillDataDO tranBillDataDO = new TranBillDataDO();
        tranBillDataDO.setInAmt(BigDecimal.ZERO);
        tranBillDataDO.setOutAmt(BigDecimal.ZERO);
        for (DhDataDTO dhDataDTO : list) {
            // 通过汇率转换出账金额
            dhDataDTO.setFromAmount(ExchangeRateUtils.convertToUSD(dhDataDTO.getFromAmount(), dhDataDTO.getFromCoin(), safeMap));
            // 通过汇率转换入账金额
            dhDataDTO.setToAmount(ExchangeRateUtils.convertToUSD(dhDataDTO.getToAmount(), dhDataDTO.getToCoin(), safeMap));

            tranBillDataDO.setInAmt(tranBillDataDO.getInAmt().add(dhDataDTO.getFromAmount()));
            tranBillDataDO.setOutAmt(tranBillDataDO.getOutAmt().add(dhDataDTO.getToAmount()));
        }
        tranBillDataDO.setCcy("USD");
        tranBillDataDO.setTxType(txType);
        tranBillDataDO.setTrDate(startTime.toLocalDate());
        tranBillDataList.add(tranBillDataDO);
    }

}
