package com.hisun.lemon.bil.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class UserBillOrderDO extends BaseDO {
    /**
     * @Fields orderNo 账单编号
     */
    private String orderNo;
    /**
     * @Fields txTm 交易时间
     */
    private LocalDateTime txTm;
    /**
     * @Fields txType 交易类型 01.充值、02.消费、03.转账、04.提现、05.充海币
     */
    private String txType;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields amount 金额
     */
    private BigDecimal orderAmt;
    /**
     * @Fields orderStatus 账单状态 'W'待支付、'W1'系统受理中、'P'支付待确认、'S,S1'成功、'F,R2'失败、'R1'部分退款、'E'超时、'C'撤销、'W2'缴费中、'F2'缴费失败、'S2'缴费成功
     */
    private String orderStatus;
    /**
     * @Fields incmPayFlag 如理财只是现金账户和理财账户互转则无需+-标识
     */
    private String incmPayFlag;
    /**
     * @Fields mobileNo 手机号码
     */
    private String mobileNo;
    /**
     * @Fields userId 用户编号
     */
    private String userId;
    /**
     * @Fields mercId 商户编号
     */
    private String mercId;
    /**
     * 现金账户余额
     */
    private BigDecimal cashBalAmt;
    /**
     * 理财账户余额
     */
    private BigDecimal invBalAmt;
    /**
     * 优惠余额
     */
    private BigDecimal couponAmt;
    /**
     * 优惠类型 01海币 02优惠券
     */
    private String couponType;
    /**
     * 账户金额
     */
    private BigDecimal actAmt;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * gas
     */
    private BigDecimal gas;
    /**
     * 银行卡补款金额
     */
    private BigDecimal crdPayAmt;
    /**
     * 补款类型
     */
    private String crdPayType;
    /**
     * 订单渠道
     */
    private String orderChannel;
    /**
     * 商户名称
     */
    private String mercName;
    /**
     * 商品名称
     */
    private String goodsInfo;
    /**
     * 原订单号
     */
    private String orgOrderNo;
    /**
     * 备注（充值拒绝原因）
     */
    private String remark;
    /**
     * 支付方式
     */
    private String payMod;
    /**
     * 汇款信息
     */
    private String remitInfo;
    /**
     * 摘要信息
     */
    private String abstractInfo;
    /**
     * 交易币种
     */
    private String ccy;
    /**
     * 账户号码
     */
    private String acNo;
    /**
     * 收款账户号码
     */
    private String toAcNo;
    /**
     * 交易哈希（数币交易专有）
     */
    private String txHash;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getIncmPayFlag() {
        return incmPayFlag;
    }

    public void setIncmPayFlag(String incmPayFlag) {
        this.incmPayFlag = incmPayFlag;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getCashBalAmt() {
        return cashBalAmt;
    }

    public void setCashBalAmt(BigDecimal cashBalAmt) {
        this.cashBalAmt = cashBalAmt;
    }

    public BigDecimal getInvBalAmt() {
        return invBalAmt;
    }

    public void setInvBalAmt(BigDecimal invBalAmt) {
        this.invBalAmt = invBalAmt;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public String getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(String orderChannel) {
        this.orderChannel = orderChannel;
    }

    public BigDecimal getActAmt() {
        return actAmt;
    }

    public void setActAmt(BigDecimal actAmt) {
        this.actAmt = actAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getCrdPayAmt() {
        return crdPayAmt;
    }

    public void setCrdPayAmt(BigDecimal crdPayAmt) {
        this.crdPayAmt = crdPayAmt;
    }

    public String getCrdPayType() {
        return crdPayType;
    }

    public void setCrdPayType(String crdPayType) {
        this.crdPayType = crdPayType;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getGoodsInfo() {
        return goodsInfo;
    }

    public void setGoodsInfo(String goodsInfo) {
        this.goodsInfo = goodsInfo;
    }

    public String getOrgOrderNo() {
        return orgOrderNo;
    }

    public void setOrgOrderNo(String orgOrderNo) {
        this.orgOrderNo = orgOrderNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPayMod() {
        return payMod;
    }

    public void setPayMod(String payMod) {
        this.payMod = payMod;
    }

    public String getRemitInfo() {
        return remitInfo;
    }

    public void setRemitInfo(String remitInfo) {
        this.remitInfo = remitInfo;
    }

    public String getAbstractInfo() {
        return abstractInfo;
    }

    public void setAbstractInfo(String abstractInfo) {
        this.abstractInfo = abstractInfo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getToAcNo() {
        return toAcNo;
    }

    public void setToAcNo(String toAcNo) {
        this.toAcNo = toAcNo;
    }

    public String getTxHash() {
        return txHash;
    }

    public void setTxHash(String txHash) {
        this.txHash = txHash;
    }

    public BigDecimal getGas() {
        return gas;
    }

    public void setGas(BigDecimal gas) {
        this.gas = gas;
    }
}