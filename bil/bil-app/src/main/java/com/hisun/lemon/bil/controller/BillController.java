package com.hisun.lemon.bil.controller;

import com.hisun.lemon.bil.dto.CreateUserBillDTO;
import com.hisun.lemon.bil.dto.UpdateUserBillDTO;
import com.hisun.lemon.bil.service.IBillService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.LemonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2017/7/25
 * @time 20:21
 */
@Api(value = "用户个人账单处理")
@RestController
@RequestMapping(value= "/bil")
public class BillController extends BaseController{
    private static final Logger logger = LoggerFactory.getLogger(BillController.class);

    @Resource
    private IBillService billService;

    @ApiOperation(value = "用户商户订单生成", notes = "生成用户商户订单信息")
    @ApiImplicitParam(name = "genericCreateUserBillDTO", value = "用户商户订单生成详细数据", paramType="form", dataType = "GenericDTO<CreateUserBillDTO>")
    @ApiResponse(code = 200, message = "用户商户订单生成结果")
    @PostMapping(value = "/bill/order")
    public GenericRspDTO createBill(@Validated @RequestBody GenericDTO<CreateUserBillDTO> genericCreateUserBillDTO) {

        billService.createUserBills(genericCreateUserBillDTO.getBody());
        GenericRspDTO<CreateUserBillDTO> genericDTO = new GenericRspDTO<CreateUserBillDTO>();
        genericDTO.setMsgCd(LemonUtils.getSuccessMsgCd());
        return genericDTO;
    }

    @ApiOperation(value = "更新用户商户订单", notes = "更新用户商户订单信息")
    @ApiImplicitParam(name = "genericUpdateUserBillDTO", value = "更新用户商户订单详细数据", paramType="form", dataType = "GenericDTO<UpdateUserBillDTO>")
    @ApiResponse(code = 200, message = "用户商户订单更新结果")
    @PatchMapping(value = "/bill/update")
    public GenericRspDTO updateBill(@Validated @RequestBody GenericDTO<UpdateUserBillDTO> genericUpdateUserBillDTO) {

        billService.updateUserBill(genericUpdateUserBillDTO.getBody());
        GenericRspDTO<UpdateUserBillDTO> genericDTO = new GenericRspDTO<UpdateUserBillDTO>();
        genericDTO.setMsgCd(LemonUtils.getSuccessMsgCd());
        return genericDTO;
    }
}
