package com.hisun.lemon.bil.controller;

import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.bil.entity.TranBillDataDO;
import com.hisun.lemon.bil.service.IBillService;
import com.hisun.lemon.bil.service.IDataBoardService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.LemonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2017/7/25
 * @time 20:21
 */
@Api(value = "数据看板模块")
@RestController
@RequestMapping(value= "/bil/DataBoard")
public class DataBoardController extends BaseController{
    private static final Logger logger = LoggerFactory.getLogger(DataBoardController.class);

    @Resource
    private IBillService billService;

    @Resource
    private IDataBoardService dataBoardService;

    @ApiOperation(value = "获取业务核心指标", notes = "根据筛选条件查询交易总额、交易笔数、交易成功率")
    @GetMapping(value = "/business/indicators")
    public GenericRspDTO<BusinessIndicatorsRspDTO> getBusinessIndicators(@RequestParam(value = "type", required = true) String type) {
        return dataBoardService.getBusinessIndicators(type);
    }

    @ApiOperation(value = "获取业务数据趋势", notes = "根据时间范围查询交易总额列表")
    @GetMapping(value = "/business/dataTrends")
    public GenericRspDTO<List<Map<String, String>>> getBusinessDataTrends(
            @RequestParam(value = "startDate", required = true) String startDate,
            @RequestParam(value = "endDate", required = true) String endDate) {
        return dataBoardService.getBusinessDataTrends(startDate, endDate);
    }

    @ApiOperation(value = "获取财务数据", notes = "获取财务数据")
    @GetMapping(value = "/business/financialData")
    public GenericRspDTO<Map<String, BigDecimal>> getFinancialData() {
        return dataBoardService.getFinancialData();
    }

    @ApiOperation(value = "获取数据统计", notes = "获取数据统计")
    @GetMapping(value = "/dhDataStatistics")
    public GenericRspDTO<List<TransactionDataList>> getDhDataStatistics() {
        return dataBoardService.getTransactionDataList();
    }

    @ApiOperation(value = "获取合作机构列表", notes = "获取合作机构列表")
    @GetMapping(value = "/agcnInfo")
    public GenericRspDTO<List<AgcyInfoListRspDTO>> getAgcnInfo() {
        GenericRspDTO<List<AgcyInfoListRspDTO>> rspDTO = dataBoardService.getAgcyInfoList();
        return rspDTO;
    }

}
