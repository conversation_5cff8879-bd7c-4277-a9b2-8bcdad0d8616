package com.hisun.lemon.bil.service.chk;

import com.hisun.lemon.common.utils.DateTimeUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Transactional
@Service
public class MercBillChkFileServiceImpl extends AbstractChkFileService {

    public MercBillChkFileServiceImpl() {
        super();
        appCnl="BIL";
        this.lockName="BIL_CHK_FILE_LOCK"+ DateTimeUtils.getCurrentDateTimeStr();
    }

    /*@Transactional(readOnly = true)
    protected void execute() {
        //获取对账日期
        LocalDate chkDate=chkFileComponent.getChkDate();
        //对账文件名
        String  chkFileName=chkFileComponent.getChkFileName(appCnl,chkDate);
        //标志文件名
        String flagName=chkFileName+".flag";
        if(chkFileComponent.isStart(appCnl,flagName)){
            logger.info("对账文件标志文件" +flagName+"已经存在,不重复生成对账文件");
            return;
        }
        logger.info("开始生成对账文件：" +flagName);
        //生成标志文件
        chkFileComponent.createFlagFile(appCnl,flagName);
        List<MercBillOrderDO> orders = chkFileComponent.queryDatas(mercId);
        //生成文件
        chkFileComponent.writeToFile(appCnl,orders,chkFileName);

        logger.info("生成对账文件"+flagName+"完成，开始上传至SFTP");


        //上传服务器
        chkFileComponent.upload(appCnl,chkFileName,flagName);
        logger.info("对账文件"+flagName+"上传至SFTP完成");
    }*/
}
