package com.hisun.lemon.bil.service;

import com.hisun.lemon.acm.dto.DmAccountReceiptsReqDTO;
import com.hisun.lemon.acm.dto.DmAccountReceiptsRspDTO;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.bil.dto.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2017/7/7
 * @time 17:25
 */
public interface IUserBillOrderService {
    /**
     * 查询用户个人账单交易信息
     * @param userBillOrderDTO
     */
    public UserBillOrderListDTO queryUserBills(UserBillOrderDTO userBillOrderDTO);

    /**
     * 查询单条用户账单交易明细
     * @param userBillInfoDTO
     * @return
     */
    public UserBillOrderListDTO queryBillInfo(UserBillInfoDTO userBillInfoDTO);

    /**
     * 查询用户账户信息
     * @param userAccountDTO
     * @return
     */
    public UserAccountRspDTO queryUserAccount(UserAccountDTO userAccountDTO);

    /**
     * 根据币种获取用户账户余额信息
     * @param userAccountDTO
     * @return
     */
    public List<QueryAcBalRspDTO> queryUserActBalByCcy(UserAccountDTO userAccountDTO);

    /**
     * 查询用户流水信息
     * @param userOrderRecordDTO
     * @return
     */
    public UserOrderRecordListRspDTO queryUserOrderRecords(UserOrderRecordDTO userOrderRecordDTO);

    /**
     * 获取用户数币账户收款/充值记录列表
     * @param req
     * @return
     */
    BilDmAccountReceiptsRspDTO getDmAccountReceiptsList(BilDmAccountReceiptsReqDTO req);

    /**
     * 根据账单编号查询用户账单
     * @param orderNo 账单编号
     * @return
     */
    UserBilOrderRspDTO getBilInfoByOrderNo(String orderNo);

    /**
     * 更新订单信息
     *
     * @param req
     */
    void updateOrder(UpdateOrderReqDTO req);

    /**
     * 新增订单信息
     *
     * @param req
     */
    void addOrder(AddOrderReqDTO req);
}
