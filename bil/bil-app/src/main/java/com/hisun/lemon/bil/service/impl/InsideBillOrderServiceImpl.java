package com.hisun.lemon.bil.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.bil.dao.IUserBillOrderDao;
import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.bil.entity.UserBillOrderDO;
import com.hisun.lemon.bil.service.IInsideBillOrderService;
import com.hisun.lemon.bil.service.IUserBillOrderService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.client.CardClient;
import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.inv.client.QueryInformationClient;
import com.hisun.lemon.inv.dto.InvUserInfoDTO;
import com.hisun.lemon.mkm.client.MarketActivityClient;
import com.hisun.lemon.mkm.req.dto.QueryUserMkmToolReqDTO;
import com.hisun.lemon.mkm.res.dto.QueryUserMkmToolRspDTO;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2017/7/8
 * @time 16:45
 */
@Service
public class InsideBillOrderServiceImpl extends BaseService implements IInsideBillOrderService {
    private static final Logger logger = LoggerFactory.getLogger(InsideBillOrderServiceImpl.class);

    @Resource
    private IUserBillOrderDao userBillOrderDao;

    @Resource
    private MarketActivityClient marketActivityClient;

    @Resource
    private AccountManagementClient accountManagementClient;

    @Resource
    private QueryInformationClient queryInformationClient;

    @Resource
    private CardClient cardClient;

    @Resource
    private UserBasicInfClient userBasicInfClient;
    /**
     * 查询用户个人账单信息
     * @param userBillOrderDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
    public UserBillOrderListDTO queryUserBills(UserBillOrderDTO userBillOrderDTO) {
        logger.info("查询用户个人账单信息，入参：{}", userBillOrderDTO.getUserNo());
        GenericRspDTO genericRspDTO = userBasicInfClient.queryUserByLoginId(LemonUtils.getUserId());
        UserBasicInfDTO userBasicInfDTO = (UserBasicInfDTO) genericRspDTO.getBody();
        if(JudgeUtils.isNotNull(userBasicInfDTO)){
            userBillOrderDTO.setUserId(userBasicInfDTO.getUserId());
        }else{
            LemonException.throwBusinessException("BIL20020");
        }
        //如果交易起始时间不给空，转为之间类型
        if(JudgeUtils.isNotNull(userBillOrderDTO.getTxTmBeginStr()) && !JudgeUtils.equals("", userBillOrderDTO.getTxTmBeginStr())){
            userBillOrderDTO.setTxTmBegin(DateTimeUtils.parseLocalDateTime(userBillOrderDTO.getTxTmBeginStr()));
        }
        //如果交易结束 时间不给空，转为之间类型
        if(JudgeUtils.isNotNull(userBillOrderDTO.getTxTmEndStr()) && !JudgeUtils.equals("", userBillOrderDTO.getTxTmEndStr())){
            userBillOrderDTO.setTxTmEnd(DateTimeUtils.parseLocalDateTime(userBillOrderDTO.getTxTmEndStr()));
        }
        //切割交易类型txType，装list
        String txType = userBillOrderDTO.getTxType();
        if(JudgeUtils.isNotEmpty(txType)) {
            List<String> list = Arrays.asList(txType.split(","));
            if (list.size() != 0) {
                userBillOrderDTO.setTxTypeList(list);
            }
        }
        //根据用户级别，查询不同数据,用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家
        String userLevel = userBillOrderDTO.getUserLevel();
        List<UserBillOrderDO> userBillOrderListDO = null;
        if(JudgeUtils.equals(userLevel, "0") || JudgeUtils.equals(userLevel, "1")) {
            //根据条件查询用户账单信息
            userBillOrderListDO = PageUtils.pageQuery(userBillOrderDTO.getPageNo(), userBillOrderDTO.getPageSize(), () -> userBillOrderDao.queryUserBillOrder(userBillOrderDTO));
        }
        if(JudgeUtils.equals(userLevel, "2") || JudgeUtils.equals(userLevel, "3")) {
            //根据条件查询用户账单信息
            userBillOrderListDO = PageUtils.pageQuery(userBillOrderDTO.getPageNo(), userBillOrderDTO.getPageSize(), () -> userBillOrderDao.queryUserBillOrder2(userBillOrderDTO));
        }
        //判断是否有查到数据
        /*if (JudgeUtils.isNull(userBillOrderListDO) || userBillOrderListDO.size() <= 0){
            LemonException.throwBusinessException("BIL20001");
        }*/
        //DO集合转为DTO
        List<UserBillOrderListDTO.UserBillOrder> userBillOrders = new ArrayList<>();
        for(UserBillOrderDO userBillOrderDO : userBillOrderListDO){
            UserBillOrderListDTO.UserBillOrder userBillOrder = new UserBillOrderListDTO.UserBillOrder();
            BeanUtils.copyProperties(userBillOrder, userBillOrderDO);
            userBillOrders.add(userBillOrder);
        }
        UserBillOrderListDTO userBillOrderListDTO = new UserBillOrderListDTO();
        userBillOrderListDTO.setList(userBillOrders);
        return userBillOrderListDTO;
    }

    /**
     * 查询单条用户账单交易明细
     * @param userBillInfoDTO
     * @return
     */
    @Override
    public UserBillOrderListDTO queryBillInfo(UserBillInfoDTO userBillInfoDTO) {

        String orderNo = userBillInfoDTO.getOrderNo();
        String userId = LemonUtils.getUserId();
        //根据账单编号查询用户账单交易明细信息
        UserBillOrderDO userBillOrderDO = userBillOrderDao.queryBillInfo(orderNo, userId);
        //判断是否有查到数据
        if (JudgeUtils.isNull(userBillOrderDO)){
            LemonException.throwBusinessException("BIL20002");
        }
        //数据对象集合转为传输对象
        List<UserBillOrderListDTO.UserBillOrder> userBillOrders = new ArrayList<>();
        UserBillOrderListDTO.UserBillOrder userBillOrder = new UserBillOrderListDTO.UserBillOrder();
        BeanUtils.copyProperties(userBillOrder, userBillOrderDO);
        userBillOrders.add(userBillOrder);
        UserBillOrderListDTO userBillOrderListDTO = new UserBillOrderListDTO();
        userBillOrderListDTO.setList(userBillOrders);
        return userBillOrderListDTO;
    }

    /**
     * 查询用户账户信息
     * @param userAccountDTO1
     * @return
     */
    @Override
    public UserAccountRspDTO queryUserAccount(UserAccountDTO userAccountDTO1) {

        GenericRspDTO genericDTO = null;
        String userId = userAccountDTO1.getUserId();
        com.hisun.lemon.acm.dto.UserAccountDTO userAccountDTO = new com.hisun.lemon.acm.dto.UserAccountDTO();
        //初始化返回DTO
        UserAccountRspDTO userAccountRspDTO = new UserAccountRspDTO();
        userAccountDTO.setUserId(userId);

        //调用账户接口，查询账户余额
        genericDTO = accountManagementClient.queryAcBal(userAccountDTO);
        List<QueryAcBalRspDTO> queryAcBalRspDTO = (List<QueryAcBalRspDTO>) genericDTO.getBody();
        if(JudgeUtils.isNull(queryAcBalRspDTO)){
            LemonException.throwBusinessException("BIL20012");
        }
        //判断资金类型为现金，则填充账户余额
        for(QueryAcBalRspDTO acBalRspDTO: queryAcBalRspDTO) {
            if(JudgeUtils.equals(CapTypEnum.CAP_TYP_CASH.getCapTyp(),acBalRspDTO.getCapTyp())) {
                userAccountRspDTO.setCashBalAmt(acBalRspDTO.getAcCurBal());
            }
        }

        //调用营销接口，查询海币余额
        QueryUserMkmToolReqDTO queryUserMkmToolReqDTO = new QueryUserMkmToolReqDTO();
        queryUserMkmToolReqDTO.setUserId(userId);
        queryUserMkmToolReqDTO.setMkTool("02");
        genericDTO.setBody(queryUserMkmToolReqDTO);
        genericDTO = marketActivityClient.queryUserMkmTool(genericDTO);
        QueryUserMkmToolRspDTO queryUserMkmToolRspDTO = (QueryUserMkmToolRspDTO) genericDTO.getBody();
        if(JudgeUtils.isNull(queryUserMkmToolRspDTO) || JudgeUtils.isNull(queryUserMkmToolRspDTO.getSeaCcyDetal())){
            userAccountRspDTO.setCouponAmt(0);
        }else {
            userAccountRspDTO.setCouponAmt(queryUserMkmToolRspDTO.getSeaCcyDetal().getCount());
        }

        //调用理财接口，查询理财余额
        genericDTO = queryInformationClient.usrBalQuery(userId);
        InvUserInfoDTO invUserInfoDTO = (InvUserInfoDTO) genericDTO.getBody();
        if(JudgeUtils.isNull(invUserInfoDTO)){
            userAccountRspDTO.setInvBalAmt(BigDecimal.ZERO);
        }else {
            userAccountRspDTO.setInvBalAmt(invUserInfoDTO.getTotalCurrentAmt());
        }

        //查询绑定银行张数
        genericDTO = cardClient.queryCardsInfo(CorpBusTyp.SIGN, CorpBusSubTyp.FAST_SIGN, userId, null);
        AgrInfoRspDTO agrInfoRspDTO = (AgrInfoRspDTO) genericDTO.getBody();
        if(JudgeUtils.isNull(agrInfoRspDTO)){
            LemonException.throwBusinessException("BIL20015");
        }
        userAccountRspDTO.setCardCount(agrInfoRspDTO.getList().size());
        return userAccountRspDTO;
    }
}
