package com.hisun.lemon.bil.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * TODO 此处填写功能说明
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/5 14:37
 */
public class TranBillDataDO extends BaseDO {
    private Long id;

    private BigDecimal inAmt;

    private BigDecimal outAmt;

    private String ccy;

    private String txType;

    private LocalDate trDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getInAmt() {
        return inAmt;
    }

    public void setInAmt(BigDecimal inAmt) {
        this.inAmt = inAmt;
    }

    public BigDecimal getOutAmt() {
        return outAmt;
    }

    public void setOutAmt(BigDecimal outAmt) {
        this.outAmt = outAmt;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public LocalDate getTrDate() {
        return trDate;
    }

    public void setTrDate(LocalDate trDate) {
        this.trDate = trDate;
    }

    @Override
    public String toString() {
        return "TranBillDataDO{" +
                "id=" + id +
                ", inAmt=" + inAmt +
                ", outAmt=" + outAmt +
                ", ccy='" + ccy + '\'' +
                ", txType='" + txType + '\'' +
                ", trDate=" + trDate +
                '}';
    }
}
