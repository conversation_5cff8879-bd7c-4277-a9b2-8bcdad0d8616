package com.hisun.lemon.bil.service;

import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.framework.data.GenericRspDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import com.hisun.lemon.bil.entity.TranBillDataDO;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/26
 * @time 9:22
 */
public interface IDataBoardService {

    GenericRspDTO<BusinessIndicatorsRspDTO> getBusinessIndicators(String type);

    /**
     * 根据时间范围查询交易总额列表
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 每个月的交易总额列表
     */
    GenericRspDTO<List<Map<String, String>>> getBusinessDataTrends(String startDate, String endDate);

    GenericRspDTO<Map<String, BigDecimal>> getFinancialData();


    /**
     * 获取数据统计
     * @return 数据统计
     */
    GenericRspDTO<List<TransactionDataList>> getTransactionDataList();

    /**
     * 获取合作机构列表
     * @return 合作机构列表
     */
    GenericRspDTO<List<AgcyInfoListRspDTO>> getAgcyInfoList();
}
