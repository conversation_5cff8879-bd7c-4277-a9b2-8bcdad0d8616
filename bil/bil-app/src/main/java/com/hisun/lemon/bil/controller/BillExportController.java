package com.hisun.lemon.bil.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.hisun.lemon.bil.component.ExcelComponent;
import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dto.MercChkDetailExportDTO;
import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.annotation.LemonBody;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.interceptor.MercOprAccess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hisun.lemon.bil.service.IMercBillExportService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2017/7/14
 * @time 11:38
 */
@Api(value = "商户订单导出")
@RestController
@RequestMapping(value = "/bil/export")
public class BillExportController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(BillExportController.class);

    @Resource
    private IMercBillExportService mercBillExportService;

    @Resource
    private ExcelComponent excelComponent;

    @ApiOperation(value = "订单导出", notes = "商户订单详情导出excel")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mercId", value = "商户编号", paramType="form", dataType = "String"),
            @ApiImplicitParam(name = "txTmBegin", value = "开始交易时间", paramType="form", dataType = "String"),
            @ApiImplicitParam(name = "txTmEnd", value = "结束交易时间", paramType="form", dataType = "String"),
            @ApiImplicitParam(name = "orderStat", value = "订单状态", paramType="form", dataType = "String"),
            @ApiImplicitParam(name = "rfdStat", value = "退款状态", paramType="form", dataType = "String")
    })
    @ApiResponse(code = 200, message = "商户订单详情查询结果")
    @GetMapping(value = "/bill")
    public void mercBillExport(String mercId, LocalDateTime txTmBegin, LocalDateTime txTmEnd, String orderStat, String rfdStat){

        mercBillExportService.queryAndExport(mercId, txTmBegin, txTmEnd, orderStat, rfdStat);
    }

    @ApiOperation(value = "商户对账明细导出", notes = "商户对账明细导出excel")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "商户订单详情查询结果")
    @GetMapping(value="/chkdetail")
    @MercOprAccess(role = 9)
    public GenericRspDTO download(@Validated @LemonBody MercChkDetailExportDTO mercChkDetailExportDTO, HttpServletResponse response){
        String fileName=createFileName(mercChkDetailExportDTO.getUserId());
//        MercChkDetailExportDTO body=dto.getBody();
        mercChkDetailExportDTO.setMercId(mercChkDetailExportDTO.getUserId());
        //查询数据
        List<MercBillOrderDO> data=mercBillExportService.queryDetailByDate(mercChkDetailExportDTO);

        List<Object[]> arr=new ArrayList<>();
        //清分日期，订单日期，订单号，类型，订单金额，现金，海币，电子券，服务费，支付类型，支付渠道，交易状态
        for(MercBillOrderDO mercBillOrderDO: data) {
            String type = mercBillOrderDO.getCouponType();
            BigDecimal cash;
            BigDecimal couponAmt = BigDecimal.ZERO;
            BigDecimal seaCoin = BigDecimal.ZERO;
            if(JudgeUtils.equals(type, BilConstants.COUPON_TYPE_SEA_COIN)){
                seaCoin = mercBillOrderDO.getCouponAmt();
                cash = mercBillOrderDO.getOrderAmt().subtract(seaCoin);
            }else{
                couponAmt = mercBillOrderDO.getCouponAmt();
                cash = mercBillOrderDO.getOrderAmt().subtract(couponAmt);
            }
            Object[] itemArr = new Object[]{
                    mercBillOrderDO.getCheckDate(),
                    mercBillOrderDO.getTxTm().toLocalDate(),
                    mercBillOrderDO.getOrderNo(),
                    txTypeConversion(mercBillOrderDO.getTxType()),
                    mercBillOrderDO.getOrderAmt(),
                    cash,
                    seaCoin,
                    couponAmt,
                    mercBillOrderDO.getServeFee(),
                    mercBillOrderDO.getPayMod(),
                    mercBillOrderDO.getOrderChannel(),
                    orderStatusConversion(mercBillOrderDO.getOrderStatus())
            };
            arr.add(itemArr);
        }

        //生成excel
        String filepath=excelComponent.createMerchantCheckDetailExcel(arr,fileName, mercChkDetailExportDTO.getLanguage());

        File file = new File(filepath);
        InputStream inputStream = null;
        OutputStream outputStream = null;
        byte[] b= new byte[1024];
        int len = 0;
        try {
            inputStream = new FileInputStream(file);
            outputStream = response.getOutputStream();

            response.setContentType("application/force-download");
            String filename = file.getName();
//            filename = filename.substring(36, filename.length());
            response.addHeader("Content-Disposition","attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));
            response.setContentLength( (int) file.length( ) );

            while((len = inputStream.read(b)) != -1){
                outputStream.write(b, 0, len);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if(outputStream != null){
                try {
                    outputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return GenericRspDTO.newSuccessInstance();
    }

    private String createFileName(String mercId){
        long curTime=System.currentTimeMillis();
        StringBuilder builder=new StringBuilder();
        builder.append(mercId);
        builder.append("-");
        builder.append(curTime);
        builder.append(".xls");
        return builder.toString();
    }

    private String txTypeConversion(String txType){
       String desc = txType;
       if(JudgeUtils.equals(BilConstants.TX_TYPE_RECHANGE, txType)){
           desc = "Recharge";
       }
        if(JudgeUtils.equals(BilConstants.TX_TYPE_CONSUME, txType)){
            desc = "Consume";
        }
        if(JudgeUtils.equals(BilConstants.TX_TYPE_TRANSFER, txType)){
            desc = "Transfer";
        }
        if(JudgeUtils.equals(BilConstants.TX_TYPE_WITHDRAW, txType)){
            desc = "Withdraw";
        }if(JudgeUtils.equals(BilConstants.TX_TYPE_RERUND, txType)){
            desc = "Refund";
        }
        if(JudgeUtils.equals(BilConstants.TX_TYPE_UNDO, txType)){
            desc = "Cancel";
        }
       return desc;
    }

    private String orderStatusConversion(String orderStatus){
        String desc = orderStatus;
        if(JudgeUtils.equals(BilConstants.ORD_STS_W, orderStatus)){
            desc = "Wait for payment";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_W1, orderStatus)){
            desc = "System accepting";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_P, orderStatus)){
            desc = "Payment to be confirmed";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_S, orderStatus) || JudgeUtils.equals(BilConstants.ORD_STS_S1, orderStatus)){
            desc = "Success";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_F, orderStatus)){
            desc = "Failure";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_R0, orderStatus)){
            desc = "Request a refund";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_R1, orderStatus)){
            desc = "Partial refund";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_R2, orderStatus)){
            desc = "All refunds";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_R3, orderStatus)){
            desc = "Refunded";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_EP, orderStatus)){
            desc = "Time out";
        }
        if(JudgeUtils.equals(BilConstants.ORD_STS_C, orderStatus)){
            desc = "Cancel";
        }
        return desc;
    }
}
