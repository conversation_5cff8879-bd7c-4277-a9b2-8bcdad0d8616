package com.hisun.lemon.bil.controller;

import com.hisun.lemon.acm.dto.DmAccountReceiptsReqDTO;
import com.hisun.lemon.acm.dto.DmAccountReceiptsRspDTO;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.bil.service.IUserBillOrderService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import io.swagger.annotations.*;
import org.hibernate.validator.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2017/7/7
 * @time 17:20
 */
@Api(value = "用户账单查询")
@RestController
@RequestMapping(value= "/bil/user")
public class UserBillQueryController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(UserBillQueryController.class);

    @Resource
    private IUserBillOrderService userBillOrderService;

    @ApiOperation(value = "用户账单查询", notes = "查询当前用户账户全部交易信息")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "用户账单查询结果")
    @GetMapping(value = "/bill/all")
    public GenericRspDTO<UserBillOrderListDTO> queryUserBills(@Validated UserBillOrderDTO userBillOrderDTO) {

        //还没过网关，先写定
        //userBillOrderDTO.setUserId("1");
        userBillOrderDTO.setUserId(LemonUtils.getUserId());
        UserBillOrderListDTO userBillOrderListDTO = userBillOrderService.queryUserBills(userBillOrderDTO);
        return GenericRspDTO.newSuccessInstance(userBillOrderListDTO);
    }

    @ApiOperation(value = "用户账户交易明细查询", notes = "查询用户账户单条交易记录明细")
    @ApiResponse(code = 200, message = "单条交易明细查询结果")
    @GetMapping(value = "/bill")
    public GenericRspDTO<UserBillOrderListDTO> queryBillInfo(@Validated UserBillInfoDTO userBillInfoDTO) {

        UserBillOrderListDTO userBillOrderListDTO = userBillOrderService.queryBillInfo(userBillInfoDTO);
        return GenericRspDTO.newSuccessInstance(userBillOrderListDTO);
    }

    @ApiOperation(value = "根据账单编号查询用户账单", notes = "根据账单编号查询用户账单")
    @ApiResponse(code = 200, message = "根据账单编号查询用户账单成功")
    @GetMapping(value = "/{order_no}")
    public GenericRspDTO<UserBilOrderRspDTO> getBilInfoByOrderNo(@Validated @NotBlank @PathVariable("order_no") String orderNo) {

        logger.info("根据账单编号查询用户账单: {}", orderNo);
        UserBilOrderRspDTO result = userBillOrderService.getBilInfoByOrderNo(orderNo);
        return GenericRspDTO.newSuccessInstance(result);
    }

    @ApiOperation(value = "用户账户信息查询", notes = "查询当前用户账户信息")
    @ApiResponse(code = 200, message = "用户账户查询结果")
    @GetMapping(value= "/act")
    public GenericRspDTO<UserAccountRspDTO> queryUserAccount(@Validated UserAccountDTO userAccountDTO) {

        userAccountDTO.setUserId(LemonUtils.getUserId());
        UserAccountRspDTO userAccountRspDTO = userBillOrderService.queryUserAccount(userAccountDTO);
        return GenericRspDTO.newSuccessInstance(userAccountRspDTO);
    }

    @ApiOperation(value = "获取用户流水", notes = "获取用户流水")
    @ApiResponse(code = 200, message = "获取用户流水结果")
    @GetMapping(value= "/getBillList")
    public GenericRspDTO<UserOrderRecordListRspDTO> getBillList(@Validated UserOrderRecordDTO userOrderRecordDTO) {
        userOrderRecordDTO.setUserId(LemonUtils.getUserId());
        return GenericRspDTO.newSuccessInstance(userBillOrderService.queryUserOrderRecords(userOrderRecordDTO));
    }

    @ApiOperation(value = "根据币种获取用户账户余额信息", notes = "根据币种获取用户账户余额信息")
    @ApiResponse(code = 200, message = "根据币种获取用户账户余额信息")
    @GetMapping(value= "/actBalByCcy")
    public GenericRspDTO<List<QueryAcBalRspDTO>> queryUserActBalByCcy(@Validated UserAccountDTO userAccountDTO) {
        userAccountDTO.setUserId(LemonUtils.getUserId());
        List<QueryAcBalRspDTO> userBal = userBillOrderService.queryUserActBalByCcy(userAccountDTO);
        return GenericRspDTO.newSuccessInstance(userBal);
    }

    @ApiOperation(value = "获取用户数币账户收款/充值记录列表", notes = "获取用户数币账户收款/充值记录列表")
    @ApiResponse(code = 200, message = "获取用户数币账户收款/充值记录列表成功")
    @PostMapping("/dm/receipts/list")
    public GenericRspDTO<BilDmAccountReceiptsRspDTO> getDmAccountReceiptsList(@Validated @RequestBody BilDmAccountReceiptsReqDTO req) {
        logger.info("获取用户数币账户收款/充值记录列表: {}", req);
        BilDmAccountReceiptsRspDTO result = userBillOrderService.getDmAccountReceiptsList(req);
        return GenericRspDTO.newSuccessInstance(result);
    }

    @ApiOperation(value = "更新订单", notes = "更新订单")
    @ApiResponse(code = 200, message = "更新订单成功")
    @PostMapping("/update/order")
    public GenericRspDTO<NoBody> updateOrder(@Validated @RequestBody GenericDTO<UpdateOrderReqDTO> genericDTO) {
        UpdateOrderReqDTO req = genericDTO.getBody();
        logger.info("更新订单请求体: {}", req);
        userBillOrderService.updateOrder(req);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "新增订单", notes = "新增订单")
    @ApiResponse(code = 200, message = "新增订单成功")
    @PostMapping("/add/order")
    public GenericRspDTO<NoBody> addOrder(@Validated @RequestBody GenericDTO<AddOrderReqDTO> genericDTO) {
        AddOrderReqDTO req = genericDTO.getBody();
        logger.info("新增订单请求体: {}", req);
        userBillOrderService.addOrder(req);
        return GenericRspDTO.newSuccessInstance();
    }
}
