package com.hisun.lemon.bil.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dto.CreateUserBillDTO;
import com.hisun.lemon.bil.dto.UpdateMercBillDTO;
import com.hisun.lemon.bil.dto.UpdateUserBillDTO;
import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.bil.entity.UserBillOrderDO;
import com.hisun.lemon.bil.service.IBillService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/26
 * @time 9:29
 */
@Service
public class BillServiceImpl extends BaseService implements IBillService {
    private static final Logger logger = LoggerFactory.getLogger(BillServiceImpl.class);

    @Resource
    private BillTransactionalService billTransactionalService;

    @Resource
    private AccountManagementClient accountManagementClient;
    /**
     * 生成用户商户账单信息
     * @param createUserBillDTO
     * @return
     */
    @Override
    public void createUserBills(CreateUserBillDTO createUserBillDTO) {
        boolean flag = false;
        String txType = createUserBillDTO.getTxType();
        //如果交易类型是'02'消费，不登录下单，会存在补单的情况
        if(JudgeUtils.equals(txType, BilConstants.TX_TYPE_CONSUME)){
            flag = true;
        }
        //查询账单是否已存在,返回交易类型为'02'消费时，用户商户账单存在情况
        String billExist = billTransactionalService.query(createUserBillDTO.getOrderNo(),flag);
        logger.info("开始登记账单：------------DTO信息："+createUserBillDTO.toString());
        /**
         * 填充账单实体对象，生成用户账单
         */
        UserBillOrderDO userBillOrderDO = new UserBillOrderDO();
        //转DTO为DO
        BeanUtils.copyProperties(userBillOrderDO, createUserBillDTO);
        //填充命名不一致的数据
        userBillOrderDO.setCashBalAmt(createUserBillDTO.getBalAmt());
        userBillOrderDO.setCcy(JudgeUtils.isBlank(createUserBillDTO.getCcy())?"USD":createUserBillDTO.getCcy());
        userBillOrderDO.setInvBalAmt(createUserBillDTO.getInvAmt());
        userBillOrderDO.setUserId(createUserBillDTO.getPayerId());
        userBillOrderDO.setMercId(createUserBillDTO.getPayeeId());
        userBillOrderDO.setAcNo(createUserBillDTO.getPayerId());
        userBillOrderDO.setToAcNo(createUserBillDTO.getPayeeId());
        if(JudgeUtils.equals(createUserBillDTO.getTxType(), "DH")) {
            userBillOrderDO.setUserId(LemonUtils.getUserId());
            userBillOrderDO.setMercId("");
        }
        // 如果交易类型是法币充值，法币转账，法币提现，则setAcNO为账户号
        if(JudgeUtils.equals(createUserBillDTO.getTxType(), "01") || JudgeUtils.equals(createUserBillDTO.getTxType(), "03") || JudgeUtils.equals(createUserBillDTO.getTxType(), "04")) {
            setAcNoAndToAcNo(userBillOrderDO,createUserBillDTO);
        }
        //如果交易类型是数币转账或数币提现，则setAcNo为付款方地址,setToAcNo为收款方地址
        if(JudgeUtils.equals(createUserBillDTO.getTxType(), BilConstants.TX_TYPE_DM_TRANSFER) ||
                JudgeUtils.equals(createUserBillDTO.getTxType(), BilConstants.TX_TYPE_DM_WITHDRAW)) {
            userBillOrderDO.setAcNo(createUserBillDTO.getAcNo());
            userBillOrderDO.setToAcNo(createUserBillDTO.getToAcNo());
        }
        //userBillOrderDO.setTxTm(LocalDateTime.parse(createUserBillDTO.getTxTmStr(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        //如果账单没有银行卡补款，则把账户金额填上，以备零钱相关账单查询
        if(JudgeUtils.equals(BilConstants.CRD_TYPE_NONE,createUserBillDTO.getCrdPayType()) || JudgeUtils.isNull(createUserBillDTO.getCrdPayType())) {
            userBillOrderDO.setActAmt(createUserBillDTO.getOrderAmt());
        }
        //交易类型为消费、转账,且订单状态为'S'成功时，收入支出标识置'-'，特殊处理直付一步支付的情况
        if(JudgeUtils.equals(BilConstants.TX_TYPE_CONSUME,txType) ||
                JudgeUtils.equals(BilConstants.TX_TYPE_TRANSFER, txType) ||
                JudgeUtils.equals(BilConstants.TX_TYPE_DM_TRANSFER, txType) ||
                JudgeUtils.equals(BilConstants.TX_TYPE_DM_WITHDRAW, txType)){
            if(JudgeUtils.equals(createUserBillDTO.getOrderStatus(), BilConstants.ORD_STS_S)) {
                userBillOrderDO.setIncmPayFlag("-");
            }
        }
        /**
         * 如收款方存在，且交易类型不为'提现'、'充海币'、'理财'时,业务类型不为'在线充值'、'线下汇款'时，则填充商户订单实体对象，生成商户订单
         */
        MercBillOrderDO mercBillOrderDO = null;
        String busType = createUserBillDTO.getBusType();
        if(JudgeUtils.isNotEmpty(createUserBillDTO.getPayeeId()) && !JudgeUtils.equals(BilConstants.BUS_TYPE_WITHDRAW_PERSONAL,busType)
                && !JudgeUtils.equals(BilConstants.TX_TYPE_RECHANGESEACOIN,txType) && !JudgeUtils.equals(BilConstants.TX_TYPE_INV,txType)
                && !JudgeUtils.equals(BilConstants.BUS_TYPE_RECHARGE_ONLINE,busType) && !JudgeUtils.equals(BilConstants.BUS_TYPE_RECHARGE_OFFLINE,busType)){
            mercBillOrderDO = new MercBillOrderDO();
            BeanUtils.copyProperties(mercBillOrderDO, createUserBillDTO);
            mercBillOrderDO.setCashBalAmt(createUserBillDTO.getBalAmt());
            mercBillOrderDO.setUserId(createUserBillDTO.getPayerId());
            mercBillOrderDO.setMercId(createUserBillDTO.getPayeeId());
//            mercBillOrderDO.setTxTm(LocalDateTime.parse(createUserBillDTO.getTxTmStr(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            //填充收款方订单信息
            if(JudgeUtils.isNotBlank(createUserBillDTO.getExtInfo())){
                if(JudgeUtils.equals(txType, BilConstants.TX_TYPE_CONSUME) ||
                        JudgeUtils.equals(txType, BilConstants.TX_TYPE_TRANSFER)){
                    mercBillOrderDO.setGoodsInfo(createUserBillDTO.getExtInfo());
                }
            }
        }
        //消费无登录支付，以及该类型的退款，如果没有付款方，不生成用户账单
        if((JudgeUtils.equals(BilConstants.TX_TYPE_CONSUME, txType)|| JudgeUtils.equals(BilConstants.TX_TYPE_RERUND, txType))
                && JudgeUtils.isEmpty(createUserBillDTO.getPayerId())){
            userBillOrderDO = null;
        }
        if(JudgeUtils.isNotNull(billExist)){
            String[] str = billExist.split("-");
            if(JudgeUtils.equals(str[0],"1")){
                userBillOrderDO = null;
            }
            if (JudgeUtils.equals(str[1],"1")){
                mercBillOrderDO = null;
            }
        }
        billTransactionalService.createUserBill(userBillOrderDO, mercBillOrderDO);
        logger.info("登记账单成功，账单号："+createUserBillDTO.getOrderNo());
    }

    private void setAcNoAndToAcNo(UserBillOrderDO userBillOrderDO, CreateUserBillDTO createUserBillDTO) {
        // 用户id
        String payerId = createUserBillDTO.getPayerId();
        // 收款方id
        String payeeId = createUserBillDTO.getPayeeId();
        String ccy = userBillOrderDO.getCcy();
        String payerCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
        // 收款方用户id
        UserAccountDTO userDTO = new UserAccountDTO();
        userDTO.setUserId(payerId);
        userDTO.setCcy(ccy);
        userDTO.setCapTyp(payerCapType);
        GenericRspDTO<List<QueryAcBalRspDTO>> genericQueryAcBalRspDTO = accountManagementClient.queryAcBal(userDTO);
        if(JudgeUtils.isNotSuccess(genericQueryAcBalRspDTO.getMsgCd())){
            logger.error("查询账户信息失败，userId："+payerId);
            LemonException.throwBusinessException(genericQueryAcBalRspDTO.getMsgCd());
        }
        String acNo = genericQueryAcBalRspDTO.getBody().get(0).getAcNo();
        logger.info("查询账户信息成功，acNo："+acNo);
        userBillOrderDO.setAcNo(acNo);

        userDTO.setUserId(payeeId);
        GenericRspDTO<List<QueryAcBalRspDTO>> genericQueryAcBalRspDTO1 = accountManagementClient.queryAcBal(userDTO);
        if(JudgeUtils.isNotSuccess(genericQueryAcBalRspDTO1.getMsgCd())){
            logger.error("查询收款方账户信息失败，userId："+payerId);
            LemonException.throwBusinessException(genericQueryAcBalRspDTO1.getMsgCd());
        }
        String skAcNo = genericQueryAcBalRspDTO1.getBody().get(0).getAcNo();
        logger.info("查询收款方账户信息成功，acNo："+acNo);
        userBillOrderDO.setToAcNo(skAcNo);
    }

    /**
     * 更新用户商户账单信息
     * @param updateUserBillDTO
     */
    @Override
    public void updateUserBill(UpdateUserBillDTO updateUserBillDTO) {

        logger.info("开始更新账单状态：-------------DTO信息："+updateUserBillDTO.toString());
        UserBillOrderDO userBillOrderDO = new UserBillOrderDO();
        //退款时，更新原订单状态用
        UserBillOrderDO orgUserBillOrderDO = new UserBillOrderDO();
        //转DTO为DO
        BeanUtils.copyProperties(userBillOrderDO, updateUserBillDTO);
        if(JudgeUtils.equals(updateUserBillDTO.getBusType(),BilConstants.BUS_TYPE_RECHARGE_OFFLINE)) {
            if(JudgeUtils.isNotEmpty(updateUserBillDTO.getExtInfo())) {
                userBillOrderDO.setAbstractInfo(updateUserBillDTO.getExtInfo());
            }
        }
        userBillOrderDO.setCashBalAmt(updateUserBillDTO.getBalAmt());
        //收款方不为空，则更新商户订单状态
        MercBillOrderDO mercBillOrderDO = new MercBillOrderDO();
        //退款时，更新原订单状态用
        MercBillOrderDO orgMercBillOrderDO = new MercBillOrderDO();
        mercBillOrderDO.setOrderNo("");
        orgMercBillOrderDO.setOrderNo("");
        if(JudgeUtils.isNotEmpty(updateUserBillDTO.getPayeeId())){
            BeanUtils.copyProperties(mercBillOrderDO, updateUserBillDTO);
            mercBillOrderDO.setCashBalAmt(updateUserBillDTO.getBalAmt());
            if(JudgeUtils.equals(BilConstants.ORD_STS_S,mercBillOrderDO.getOrderStatus())){
                //更新订单交易时间
                if(JudgeUtils.isNotNull(updateUserBillDTO.getPayOrdTm())) {
                    mercBillOrderDO.setPayOrdTm(updateUserBillDTO.getPayOrdTm());
                }
            }
            if(JudgeUtils.equals(BilConstants.ORD_STS_C,mercBillOrderDO.getOrderStatus())){
                //更新订单撤销时间
                if(JudgeUtils.isNotNull(updateUserBillDTO.getUndoOrdTm())) {
                    mercBillOrderDO.setUndoOrdTm(updateUserBillDTO.getUndoOrdTm());
                }
            }
            //更新付款方订单信息
            if(JudgeUtils.isNotBlank(updateUserBillDTO.getExtInfo())){
                mercBillOrderDO.setGoodsInfo(updateUserBillDTO.getExtInfo());
            }
        }
        //查询用户账单或、订单是否已存在
        UserBillOrderDO userBillOrderDOTemp = billTransactionalService.queryUserBill(userBillOrderDO.getOrderNo());
        MercBillOrderDO mercBillOrderDOTemp = billTransactionalService.queryMercBill(mercBillOrderDO.getOrderNo());
        if(JudgeUtils.isNull(userBillOrderDOTemp) && JudgeUtils.isNull(mercBillOrderDOTemp)){
            LemonException.throwBusinessException("BIL20004");
        }

        //订单成功之后才设置收入支出标识
        if(JudgeUtils.isNotNull(userBillOrderDOTemp)) {
            String txType = userBillOrderDOTemp.getTxType();
            String orderStatus = updateUserBillDTO.getOrderStatus();
            //交易类型为充值、收款、退款，收入支出标识置'+'
            if (JudgeUtils.equals(BilConstants.TX_TYPE_RECHANGE, txType) ||
                    JudgeUtils.equals(BilConstants.TX_TYPE_DM_RECHARGE, txType) ||
                    JudgeUtils.equals(BilConstants.TX_TYPE_DM_RECEIVE, txType)) {
                if(JudgeUtils.equals(orderStatus, BilConstants.ORD_STS_S)) {
                    userBillOrderDO.setIncmPayFlag("+");
                }
            }
            if (JudgeUtils.equals(BilConstants.TX_TYPE_RERUND, txType)) {
                if(JudgeUtils.equals(orderStatus, BilConstants.ORD_STS_R3)) {
                    userBillOrderDO.setIncmPayFlag("+");
                }
            }
            //交易类型为消费、转账、提现、缴费、充海币,收入支出标识置'-'
            if (JudgeUtils.equals(BilConstants.TX_TYPE_CONSUME, txType) ||
                    JudgeUtils.equals(BilConstants.TX_TYPE_TRANSFER, txType) ||
                    JudgeUtils.equals(BilConstants.TX_TYPE_RECHANGESEACOIN, txType) ||
                    JudgeUtils.equals(BilConstants.TX_TYPE_DM_TRANSFER, txType)) {
                if(JudgeUtils.equals(orderStatus, BilConstants.ORD_STS_S)) {
                    userBillOrderDO.setIncmPayFlag("-");
                }
            }
            if (JudgeUtils.equals(BilConstants.TX_TYPE_WITHDRAW, txType)) {
                if(JudgeUtils.equals(orderStatus, BilConstants.ORD_STS_S1)) {
                    userBillOrderDO.setIncmPayFlag("-");
                }
            }
            //数币提现
            if (JudgeUtils.equals(BilConstants.TX_TYPE_DM_WITHDRAW, txType) &&
                    JudgeUtils.equals(orderStatus, BilConstants.ORD_STS_S)) {
                userBillOrderDO.setIncmPayFlag("-");
            }
            if (JudgeUtils.equals(BilConstants.TX_TYPE_PAY, txType)) {
                if(JudgeUtils.equals(orderStatus, BilConstants.ORD_STS_S2)) {
                    userBillOrderDO.setIncmPayFlag("-");
                }
            }
        }

        //原订单号不为空，则交易类型为退款
        if(JudgeUtils.isNotEmpty(updateUserBillDTO.getOrgOrderNo()) && JudgeUtils.isNotEmpty(updateUserBillDTO.getOrgOrderStatus())){
            //查询用户及商户原订单是否已存在
            UserBillOrderDO userBillOrderDOOrg = billTransactionalService.queryUserBill(updateUserBillDTO.getOrgOrderNo());
            MercBillOrderDO mercBillOrderDOOrg = billTransactionalService.queryMercBill(updateUserBillDTO.getOrgOrderNo());
            if(JudgeUtils.isNull(userBillOrderDOOrg) && JudgeUtils.isNull(mercBillOrderDOOrg)){
                LemonException.throwBusinessException("BIL20004");
            }
            orgUserBillOrderDO.setOrderNo(updateUserBillDTO.getOrgOrderNo());
            orgUserBillOrderDO.setOrderStatus(updateUserBillDTO.getOrgOrderStatus());
            orgMercBillOrderDO.setOrderNo(updateUserBillDTO.getOrgOrderNo());
            orgMercBillOrderDO.setOrderStatus(updateUserBillDTO.getOrgOrderStatus());
            //更新原订单已退款金额，退款时间，退款原因
            if(JudgeUtils.isNotNull(updateUserBillDTO.getRfdAmt()) && JudgeUtils.isNotNull(updateUserBillDTO.getRfdOrdTm())) {
                orgMercBillOrderDO.setRfdOrdTm(updateUserBillDTO.getRfdOrdTm());
                orgMercBillOrderDO.setRfdAmt(updateUserBillDTO.getRfdAmt());
                orgMercBillOrderDO.setRfdReason(updateUserBillDTO.getRfdReason());
                if(JudgeUtils.isNotNull(mercBillOrderDOTemp)){
                    orgMercBillOrderDO.setRfdSeaCoin(mercBillOrderDOTemp.getCouponAmt());
                }
            }
            //更新原订单状态,判断如果原订单不存在则不更新
            if(JudgeUtils.isNull(userBillOrderDOOrg)){
                orgUserBillOrderDO = null;
            }
            if(JudgeUtils.isNull(mercBillOrderDOOrg)){
                orgMercBillOrderDO = null;
            }
            billTransactionalService.updateUserBill(orgUserBillOrderDO, orgMercBillOrderDO);
        }

        /**
         * 如果交易类型为'08缴费'，状态支付成功, 且订单状态不是缴费成功S2、缴费失败F2、退款状态R*，则置状态为'W2缴费中'
         * 由于消息队列到达顺序不一致所作的状态控制
         */
        if (JudgeUtils.isNotNull(userBillOrderDOTemp)) {
            if(JudgeUtils.equals(BilConstants.TX_TYPE_PAY, userBillOrderDOTemp.getTxType())) {
                logger.info("------订单号:"+userBillOrderDOTemp.getOrderNo()+"  txType:"+userBillOrderDOTemp.getTxType()+
                        "  原订单状态:"+userBillOrderDOTemp.getOrderStatus()+"  更新订单状态:"+updateUserBillDTO.getOrderStatus());
                //如接到支付成功通知，则改状态为缴费中"W2"
                if(JudgeUtils.equals(BilConstants.ORD_STS_S, updateUserBillDTO.getOrderStatus())){
                    userBillOrderDO.setOrderStatus(BilConstants.ORD_STS_W2);
                    mercBillOrderDO.setOrderStatus(BilConstants.ORD_STS_W2);
                    //如果缴费处理结果通知先到，则不再处理支付成功通知状态
                    if (JudgeUtils.equals(BilConstants.ORD_STS_S2, userBillOrderDOTemp.getOrderStatus()) ||
                            JudgeUtils.equals(BilConstants.ORD_STS_F2, userBillOrderDOTemp.getOrderStatus()) ||
                            JudgeUtils.equals(BilConstants.ORD_STS_R1, userBillOrderDOTemp.getOrderStatus()) ||
                            JudgeUtils.equals(BilConstants.ORD_STS_R2, userBillOrderDOTemp.getOrderStatus())) {
                        userBillOrderDO.setOrderStatus(null);
                        mercBillOrderDO.setOrderStatus(null);
                    }
                }

                //如果已发起缴费退款，则不再置订单状态为缴费失败F2
                if(JudgeUtils.equals(BilConstants.ORD_STS_F2, updateUserBillDTO.getOrderStatus())) {
                    if (JudgeUtils.equals(BilConstants.ORD_STS_R1, userBillOrderDOTemp.getOrderStatus()) ||
                            JudgeUtils.equals(BilConstants.ORD_STS_R2, userBillOrderDOTemp.getOrderStatus())) {
                        userBillOrderDO.setOrderStatus(null);
                        mercBillOrderDO.setOrderStatus(null);
                    }
                }
            }
        }

        //更新订单状态,判断如果订单不存在则不更新
        if(JudgeUtils.isNull(userBillOrderDOTemp)){
            userBillOrderDO = null;
        }
        if(JudgeUtils.isNull(mercBillOrderDOTemp)){
            mercBillOrderDO = null;
        }else {
            //如果交易类型是'02'消费，存在补单的情况下，把商户订单的userId补上
            if (JudgeUtils.equals(mercBillOrderDOTemp.getTxType(),BilConstants.TX_TYPE_CONSUME)) {
                mercBillOrderDO.setUserId(updateUserBillDTO.getPayerId());
            }
        }
        billTransactionalService.updateUserBill(userBillOrderDO, mercBillOrderDO);
        logger.info("更新账单状态成功，账单号："+updateUserBillDTO.getOrderNo());
    }

    /**
     * 更新用户商户账单清分信息
     * @param updateMercBillDTO
     */
    @Override
    public void updateMercBill(UpdateMercBillDTO updateMercBillDTO) {
        MercBillOrderDO mercBillOrderDO = billTransactionalService.queryMercBill(updateMercBillDTO.getOrderNo());
        if(JudgeUtils.isNull(mercBillOrderDO)){
            LemonException.throwBusinessException("BIL20004");
        }
        MercBillOrderDO mercBillOrderDO1 = new MercBillOrderDO();
        BeanUtils.copyProperties(mercBillOrderDO1, updateMercBillDTO);
        billTransactionalService.updateMercBill(mercBillOrderDO1);
        logger.info("更新账单状态成功，账单号："+updateMercBillDTO.getOrderNo());
    }
}
