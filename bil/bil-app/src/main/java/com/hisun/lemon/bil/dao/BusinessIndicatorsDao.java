package com.hisun.lemon.bil.dao;


import com.hisun.lemon.bil.entity.BusinessIndicatorsDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/7/10
 * @time 9:55
 */
@Mapper
@Component
public interface BusinessIndicatorsDao extends BaseDao<BusinessIndicatorsDO> {


    // 根据交易日期查询最新数据
    BusinessIndicatorsDO findLatestByTrData(@Param("trData") Date trData);

    // 查询指定日期范围内的业务指标数据
    List<BusinessIndicatorsDO> findByDateRange(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // 查询指定日期范围内的月度交易总额
    @MapKey("yearMonth")
    List<Map<String, Object>> findMonthlyTransactionAmount(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // 删除方法
    int deleteById(@Param("id") Long id);

}
