package com.hisun.lemon.bil.service.chk;

import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.common.extension.ExtensionLoader;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.bil.component.ChkFileComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2017年8月5日
 * @time 下午2:54:28
 *
 */
public abstract class AbstractChkFileService implements Callable {
    protected static final Logger logger = LoggerFactory.getLogger(AbstractChkFileService.class);

    private DistributedLocker locker;


    protected ChkFileComponent chkFileComponent;

    protected String lockName;

    protected String mercId;

    protected String appCnl;

    public AbstractChkFileService() {

    }

    @Transactional(readOnly = true)
    protected void execute() {
        if(JudgeUtils.isNull(chkFileComponent)){
            chkFileComponent= (ChkFileComponent)getBean(ChkFileComponent.class);
        }
        //System.out.println("AbstractChkFileService.call()===========================================AbstractChkFileService.call()"+lockName);
        //获取对账日期
        LocalDate chkDate=chkFileComponent.getChkDate();
        //对账文件名
        String chkFileName=chkFileComponent.getChkFileName(appCnl, chkDate, mercId);
        //标志文件名
        String flagName=chkFileName+".flag";
        if(chkFileComponent.isStart(appCnl,flagName)){
            logger.info("对账文件标志文件" +flagName+"已经存在,不重复生成对账文件");
            return;
        }
        logger.info("开始生成对账文件：" +flagName);
        //生成标志文件
        chkFileComponent.createFlagFile(appCnl,flagName);
        List<MercBillOrderDO> orders = chkFileComponent.queryDatas(mercId);
        //生成文件
        chkFileComponent.writeToFile(appCnl,orders,chkFileName);

        logger.info("生成对账文件"+flagName+"完成，开始上传至SFTP");


        //上传服务器
        chkFileComponent.upload(appCnl,chkFileName,flagName);
        logger.info("对账文件"+flagName+"上传至SFTP完成");
    }

    @Override
    public Object call() throws Exception {
        logger.info("唤起生成"+appCnl+"数据对账文件任务");
        //System.out.println("AbstractChkFileService.call()===========================================AbstractChkFileService.call()"+lockName);
        if(JudgeUtils.isNull(locker)){
            locker= (DistributedLocker)getBean(DistributedLocker.class);
        }
        locker.lock(lockName, 18, 22, () -> {
            execute();
            return null;
        });
        return null;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }


    public Object getBean(Class clazz){
        Map<String,Object> map=ExtensionLoader.getSpringBeansOfType(clazz);
        Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
             while (it.hasNext()) {
                  Map.Entry<String, Object> entry = it.next();
                  return  entry.getValue();
            }
            return null;
    }
}
