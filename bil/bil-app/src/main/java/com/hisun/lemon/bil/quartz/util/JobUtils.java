package com.hisun.lemon.bil.quartz.util;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @title JobUtils
 */
public class JobUtils {

    public static boolean isWithinPublicityPeriod(Date publishTime, int publicityDays) {
        // 获取当前时间
        Date currentTime = new Date();

        // 计算截止时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(publishTime);
        calendar.add(Calendar.DAY_OF_YEAR, publicityDays);
        Date endTime = calendar.getTime();

        // 判断当前时间是否在公示期内
        return currentTime.after(publishTime) && currentTime.before(endTime);
    }

    public static boolean isAfterPeriod(Date publishTime, int publicityDays) {
        // 获取当前时间
        Date currentTime = new Date();

        // 计算截止时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(publishTime);
        calendar.add(Calendar.DAY_OF_YEAR, publicityDays);
        Date endTime = calendar.getTime();

        // 判断当前时间是否在公示期内
        return currentTime.after(endTime);
    }

    public static boolean isAfterPeriodBySec(Date publishTime, int publicitySec) {
        // 获取当前时间
        Date currentTime = new Date();

        // 计算截止时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(publishTime);
        calendar.add(Calendar.SECOND, publicitySec);
        Date endTime = calendar.getTime();

        // 判断当前时间是否在公示期内
        return currentTime.after(endTime);
    }

}
