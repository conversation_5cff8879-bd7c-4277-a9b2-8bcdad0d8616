package com.hisun.lemon.bil.service;

import com.hisun.lemon.bil.dto.MercChkDetailExportDTO;
import com.hisun.lemon.bil.entity.MercBillOrderDO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/14
 * @time 14:30
 */
public interface IMercBillExportService {

    /**
     * 查询商户订单信息并导出excel
     * @param mercId
     * @param txTmBegin
     * @param txTmEnd
     * @param orderStat
     * @param rfdStat
     */
    public void queryAndExport(String mercId, LocalDateTime txTmBegin, LocalDateTime txTmEnd, String orderStat, String rfdStat);

    /**
     * 查询商户对账明细并导出excel
     */
    public List<MercBillOrderDO> queryDetailByDate(MercChkDetailExportDTO mercChkDetailExportDTO);

}
