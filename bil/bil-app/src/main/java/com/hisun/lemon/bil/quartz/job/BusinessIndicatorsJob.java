package com.hisun.lemon.bil.quartz.job;

import com.hisun.lemon.bil.dao.BusinessIndicatorsDao;
import com.hisun.lemon.bil.dao.IMercBillOrderDao;
import com.hisun.lemon.bil.dao.IUserBillOrderDao;
import com.hisun.lemon.bil.entity.BusinessIndicatorsDO;
import com.hisun.lemon.bil.quartz.util.SpringContextUtils;
import com.hisun.lemon.bil.utils.ExchangeRateUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description 运营端首页-业务指标统计-定时任务
 * @title BusinessIndicatorsJob
 * @date 2025/8/5 10:56
 */
@Component
public class BusinessIndicatorsJob implements Job {
    
    private static final Logger logger = LoggerFactory.getLogger(BusinessIndicatorsJob.class);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            logger.info("开始执行业务指标统计定时任务");
            BusinessIndicatorsDao businessIndicatorsDao = SpringContextUtils.getBean(BusinessIndicatorsDao.class);
            IUserBillOrderDao userBillOrderDao = SpringContextUtils.getBean(IUserBillOrderDao.class);
            // 获取今天的日期范围
            LocalDate today = LocalDate.now();
            LocalDateTime startTime = today.atStartOfDay();
            LocalDateTime endTime = today.atTime(LocalTime.MAX);
            
            // 查询今日交易统计（按币种分组）
            List<Map<String, Object>> currencyStats = userBillOrderDao.queryTransactionStatisticsByCurrency(startTime, endTime);
            
            // 查询今日交易统计汇总
            Map<String, Object> summaryStats = userBillOrderDao.queryTransactionStatisticsSummary(startTime, endTime);
            
            // 查询昨日交易统计（用于比较趋势）
            LocalDate yesterday = today.minusDays(1);
            LocalDateTime yesterdayStart = yesterday.atStartOfDay();
            Date yesterdayDate = Date.from(yesterdayStart.atZone(java.time.ZoneId.systemDefault()).toInstant());
            
            // 从bil_business_indicators表中查询昨天的数据
            BusinessIndicatorsDO yesterdayRecord = businessIndicatorsDao.findLatestByTrData(yesterdayDate);
            
            // 计算所有币种汇总的USD金额和总指标
            BigDecimal totalUSDAmount = BigDecimal.ZERO;
            int totalCount = 0;
            int totalSuccessCount = 0;
            BigDecimal totalSuccessAmount = BigDecimal.ZERO;
            
            // 处理每个币种的统计数据，汇总到USD
            for (Map<String, Object> currencyStat : currencyStats) {
                String currency = (String) currencyStat.get("currency");
                BigDecimal totalAmount = (BigDecimal) currencyStat.get("totalAmount");
                Object countObj = currencyStat.get("totalCount");
                Object successCountObj = currencyStat.get("successCount");
                BigDecimal successAmount = (BigDecimal) currencyStat.get("successAmount");
                
                // 安全地转换count和successCount
                Integer count = null;
                Integer successCount = null;
                
                if (countObj instanceof Long) {
                    count = ((Long) countObj).intValue();
                } else if (countObj instanceof Integer) {
                    count = (Integer) countObj;
                } else if (countObj instanceof Number) {
                    count = ((Number) countObj).intValue();
                }
                
                if (successCountObj instanceof Long) {
                    successCount = ((Long) successCountObj).intValue();
                } else if (successCountObj instanceof Integer) {
                    successCount = (Integer) successCountObj;
                } else if (successCountObj instanceof Number) {
                    successCount = ((Number) successCountObj).intValue();
                }
                
                // 将其他币种转换为USD
                BigDecimal usdAmount = ExchangeRateUtils.convertToUSD(totalAmount, currency);
                BigDecimal usdSuccessAmount = ExchangeRateUtils.convertToUSD(successAmount, currency);
                
                totalUSDAmount = totalUSDAmount.add(usdAmount);
                totalCount += (count != null ? count : 0);
                totalSuccessCount += (successCount != null ? successCount : 0);
                totalSuccessAmount = totalSuccessAmount.add(usdSuccessAmount);
                
                logger.info("币种 {} 统计: 交易总额={}, 交易笔数={}, 成功笔数={}, 转换为USD={}", 
                    currency, totalAmount, count, successCount, usdAmount);
            }
            
            // 计算总体成功率
            BigDecimal overallSuccessRate = BigDecimal.ZERO;
            if (totalCount > 0) {
                overallSuccessRate = new BigDecimal(totalSuccessCount)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP);
            }

            // 计算与昨日的比较趋势
            String amountCompareYesterday = "0.00%";
            String countCompareYesterday = "0.00%";
            if (yesterdayRecord != null) {
                BigDecimal yesterdayAmount = yesterdayRecord.getAmt();
                Integer yesterdayCount = yesterdayRecord.getNum();
                
                if (yesterdayAmount != null && yesterdayCount != null && yesterdayAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal amountGrowth = totalUSDAmount.subtract(yesterdayAmount)
                            .divide(yesterdayAmount, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    BigDecimal yeCount = new BigDecimal(yesterdayCount);
                    BigDecimal countGrowth = new BigDecimal(totalCount).subtract(yeCount)
                            .divide(yeCount, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    amountCompareYesterday = amountGrowth.setScale(2, RoundingMode.HALF_UP).toString() + "%";
                    countCompareYesterday = countGrowth.setScale(2, RoundingMode.HALF_UP).toString() + "%";
                    logger.info("今日与昨日比较: 交易总额增长={}%, 交易笔数变化={}",
                            amountGrowth, totalCount - yesterdayCount);
                } else {
                    logger.info("昨日数据无效，无法计算比较趋势");
                }
            } else {
                // 昨天没有数据，总额为0，交易数量为0
                logger.info("昨日无数据，今日为首次统计: 交易总额={}, 交易笔数={}",
                        totalUSDAmount, totalCount);
            }

            // 检查是否已存在今日的记录，如果存在则删除
            Date todayDate = Date.from(today.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());
            BusinessIndicatorsDO existingRecord = businessIndicatorsDao.findLatestByTrData(todayDate);
            if (existingRecord != null) {
                businessIndicatorsDao.deleteById(existingRecord.getId());
                logger.info("删除已存在的今日记录，ID: {}", existingRecord.getId());
            }
            
            // 创建汇总业务指标对象（每天只存储一条记录）
            BusinessIndicatorsDO summaryDO = new BusinessIndicatorsDO();
            summaryDO.setAmt(totalUSDAmount);
            summaryDO.setNum(totalCount);
            summaryDO.setCcy("USD"); // 统一使用USD作为币种
            summaryDO.setSuccessRate(overallSuccessRate);
            summaryDO.setAmtCompareYesterday(amountCompareYesterday);
            summaryDO.setNumCompareYesterday(countCompareYesterday);
            summaryDO.setTrData(todayDate);

            // 保存到数据库
            businessIndicatorsDao.insert(summaryDO);
            
            logger.info("保存今日汇总业务指标: USD交易总额={}, 总交易笔数={}, 总成功笔数={}, 总成功率={}%", 
                totalUSDAmount, totalCount, totalSuccessCount, overallSuccessRate);
            

            
            logger.info("业务指标统计定时任务执行完成");
            
        } catch (Exception e) {
            logger.error("业务指标统计定时任务执行失败", e);
            throw new JobExecutionException("业务指标统计定时任务执行失败", e);
        }
    }
}
