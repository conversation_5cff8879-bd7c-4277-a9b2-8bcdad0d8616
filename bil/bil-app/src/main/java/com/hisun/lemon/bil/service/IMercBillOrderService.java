package com.hisun.lemon.bil.service;

import com.hisun.lemon.bil.dto.*;


/**
 * <AUTHOR>
 * @date 2017/7/12
 * @time 16:17
 */
public interface IMercBillOrderService {

    /**
     * 查询所有用户在该商户交易记录
     * @param mercBillOrderDTO
     * @return
     */
    MercBillOrderListDTO queryMercBills(MercBillOrderDTO mercBillOrderDTO);

    /**
     * 查询用户在该商户单条订单交易记录明细
     * @param mercBillInfoDTO
     * @return
     */
    MercBillOrderListDTO queryMercDetails(MercBillInfoDTO mercBillInfoDTO);

    /**
     * 查询商户账户信息
     * @param mercAccountDTO
     * @return
     */
    MercAccountRspDTO queryMercAccount(MercAccountDTO mercAccountDTO);

    /**
     * 查询商户对账清分信息
     * @param mercCsmInfoDTO
     * @return
     */
    MercCsmInfoRspDTO queryMercCsmDetails(MercCsmInfoDTO mercCsmInfoDTO);

    /**
     * 查询所有用户在该商户交易记录
     * @param mercBillOrderDTO
     * @return
     */
    MercUpBillOrderListDTO queryMercUpBills(MercUpBillOrderDTO mercBillOrderDTO);
}
