package com.hisun.lemon.bil.service.impl;

import com.hisun.lemon.bil.dao.IMercBillOrderDao;
import com.hisun.lemon.bil.dto.MercChkDetailExportDTO;
import com.hisun.lemon.bil.entity.MercBillOrderDO;
import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/14
 * @time 14:40
 */
@Transactional
@Service
public class MercBillExportTransactionalService extends BaseService {

    @Resource
    private IMercBillOrderDao mercBillOrderDao;

    public void queryAndExport(String mercId, LocalDateTime txTmBegin, LocalDateTime txTmEnd, String orderStat, String rfdStat) {

        mercBillOrderDao.queryAndExport(mercId, txTmBegin, txTmEnd, orderStat, rfdStat);
    }

    public List<MercBillOrderDO> queryDetailByDate(MercChkDetailExportDTO mercChkDetailExportDTO) {

        return mercBillOrderDao.queryDetailByDate(mercChkDetailExportDTO);
    }
}
