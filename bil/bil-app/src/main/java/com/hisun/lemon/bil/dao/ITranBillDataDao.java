package com.hisun.lemon.bil.dao;

import com.hisun.lemon.bil.dto.TransactionDataList;
import com.hisun.lemon.bil.entity.TranBillDataDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * TODO 此处填写功能说明
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/5 14:43
 */
@Mapper
@Component
public interface ITranBillDataDao  extends BaseDao<TranBillDataDO> {

    /**
     * 批量插入
     * @param tranBillDataList
     */
    void batchInsert(List<TranBillDataDO> tranBillDataList);

    /**
     * 根据交易类型分组查询列表
     * @return
     */
    List<TransactionDataList> queryList(@Param("date") LocalDate date);

    /**
     * 根据时间删除
     * @param localDate
     */
    void deleteByDate(@Param("date") LocalDate localDate);
}
