package com.hisun.lemon.bil.schedule;

import com.hisun.lemon.bil.service.chk.AbstractChkFileService;
import com.hisun.lemon.bil.service.chk.MercBillChkFileServiceImpl;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 定时任务调度器
 */
@Component
public class Scheduler {
	ExecutorService executorService = Executors.newFixedThreadPool(20);

	@Resource
	private UserBasicInfClient userBasicInfClient;

	//每天0点1分出发
    @BatchScheduled(cron="0 20 2 * * ?")
	//@Scheduled(cron = "0/90 * * * * ?") // 每90秒执行一次
	public void createChkFile(){
    	//urm ids
        GenericRspDTO genericRspDTO = userBasicInfClient.queryCrpUser(GenericDTO.newInstance());
		for(String str : (List<String>) genericRspDTO.getBody()){
			AbstractChkFileService abstractChkFileService=new MercBillChkFileServiceImpl();
			abstractChkFileService.setMercId(str);
			executorService.submit(abstractChkFileService);
		}
	}
}
