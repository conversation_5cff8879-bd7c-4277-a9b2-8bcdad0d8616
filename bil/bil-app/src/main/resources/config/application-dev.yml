redis :
  host : *************
  password : Hqpay2025

rabbitmq :
  addresses : *************:5672
  password : Rabbitmq123

#Multiple dataSources
dataSource :
  lemon :
    type : com.alibaba.druid.pool.DruidDataSource
    driverClassName : com.mysql.cj.jdbc.Driver
    url : ********************************************************************************************
    username : payment
    password : payment
    initialSize : 5
    minIdle : 5
    maxActive : 20
    # 配置获取连接等待超时的时间
    maxWait : 30000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 
    timeBetweenEvictionRunsMillis : 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒 
    minEvictableIdleTimeMillis : 300000
    validationQuery : SELECT 1
    testWhileIdle : true
    testOnBorrow : false
    testOnReturn : false
    # 打开PSCache，并且指定每个连接上PSCache的大小 
    poolPreparedStatements : true
    maxPoolPreparedStatementPerConnectionSize : 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 
    filters : stat,wall,log4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties : druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    useGlobalDataSourceStat : true
  primary :
    type : com.alibaba.druid.pool.DruidDataSource
    driverClassName : com.mysql.cj.jdbc.Driver
    url : ********************************************************************************************
    username : payment
    password : payment
    initialSize : 5
    minIdle : 5
    maxActive : 20
    # 配置获取连接等待超时的时间
    maxWait : 60000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 
    timeBetweenEvictionRunsMillis : 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒 
    minEvictableIdleTimeMillis : 300000
    validationQuery : SELECT 1
    testWhileIdle : true
    testOnBorrow : false
    testOnReturn : false
    # 打开PSCache，并且指定每个连接上PSCache的大小 
    poolPreparedStatements : true
    maxPoolPreparedStatementPerConnectionSize : 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 
    filters : stat,wall,log4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties : druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    useGlobalDataSourceStat : true


eureka :
  #  zone : http://************:9002/eureka/,http://************:9002/eureka/
  zone : http://127.0.0.1:9002/eureka/
  instance:
    ip-address: 127.0.0.1

bil :
  merc :
    #下载生成临时文件存放路径
    detailpath : /home/<USER>/data/bil/
    #下载文件模板路径
    detailTemplate-zh : /home/<USER>/data/localchk/bil/模板zh.xls
    detailTemplate-en : /home/<USER>/data/localchk/bil/模板en.xls
    detailTemplate-km : /home/<USER>/data/localchk/bil/模板km.xls
