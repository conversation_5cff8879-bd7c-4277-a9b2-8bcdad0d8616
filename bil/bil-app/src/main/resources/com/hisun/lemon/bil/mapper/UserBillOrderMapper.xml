<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.bil.dao.IUserBillOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.bil.entity.UserBillOrderDO" >
        <id column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="tx_tm" property="txTm" jdbcType="TIMESTAMP" />
        <result column="tx_type" property="txType" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="order_status" property="orderStatus" jdbcType="CHAR" />
        <result column="incm_pay_flag" property="incmPayFlag" jdbcType="CHAR" />
        <result column="mobile_no" property="mobileNo" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="merc_id" property="mercId" jdbcType="VARCHAR" />
        <result column="cash_bal_amt" property="cashBalAmt" jdbcType="DECIMAL" />
        <result column="coupon_amt" property="couponAmt" jdbcType="DECIMAL" />
        <result column="inv_bal_amt" property="invBalAmt" jdbcType="DECIMAL" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="act_amt" property="actAmt" jdbcType="DECIMAL" />
        <result column="crd_pay_amt" property="crdPayAmt" jdbcType="DECIMAL" />
        <result column="crd_pay_type" property="crdPayType" jdbcType="VARCHAR" />
        <result column="coupon_type" property="couponType" jdbcType="VARCHAR" />
        <result column="order_channel" property="orderChannel" jdbcType="VARCHAR" />
        <result column="merc_name" property="mercName" jdbcType="VARCHAR" />
        <result column="goods_info" property="goodsInfo" jdbcType="VARCHAR" />
        <result column="remit_info" property="remitInfo" jdbcType="VARCHAR" />
        <result column="abstract_info" property="abstractInfo" jdbcType="VARCHAR" />
        <result column="org_order_no" property="orgOrderNo" jdbcType="VARCHAR" />
        <result column="pay_mod" property="payMod" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="ac_no" property="acNo" jdbcType="VARCHAR" />
        <result column="to_ac_no" property="toAcNo" jdbcType="VARCHAR" />
        <result column="tx_hash" property="txHash" jdbcType="VARCHAR" />
        <result column="gas" property="gas" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        order_no, tx_tm, tx_type, bus_type, order_amt, order_status, incm_pay_flag, mobile_no, user_id, merc_id,
         order_channel, merc_name, goods_info, remit_info, abstract_info, org_order_no, remark, pay_mod, fee, coupon_amt, coupon_type
    </sql>

    <sql id="User_Bill_List" >
        order_no, tx_tm, tx_type, bus_type, order_amt, order_status, incm_pay_flag,user_id, merc_id,
         cash_bal_amt, coupon_amt, coupon_type, crd_pay_amt, crd_pay_type, ccy, fee, remark, ac_no, to_ac_no, tx_hash, order_channel
    </sql>

    <sql id="Order_Record_List" >
        tx_tm, ccy, order_amt, fee, user_id, merc_id, order_no, order_status, tx_type, remark, order_channel
    </sql>

    <sql id="User_Account_List" >
        order_no, user_id, cash_bal_amt, coupon_amt, inv_bal_amt, coupon_type
    </sql>

    <sql id="Bill_Data_List" >
        order_no, tx_tm, tx_type, order_amt, ccy, gas, fee
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from bil_user_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="query" resultType="Integer" parameterType="java.lang.String" >
        select
        count(order_no)
        from bil_user_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="queryUserBillOrder" resultMap="BaseResultMap" parameterType="com.hisun.lemon.bil.dto.UserBillOrderDTO" >
        select
        <include refid="User_Bill_List" />
        from bil_user_order where user_id = #{userId, jdbcType=VARCHAR}
        <![CDATA[and tx_type NOT IN ('09')]]>
        <if test="txTmBegin != null">
            <![CDATA[and tx_tm >= #{txTmBegin}]]>
        </if>
        <if test="txTmEnd != null">
            <![CDATA[and tx_tm <= #{txTmEnd}]]>
        </if>
        <if test="actFlag != null">
            <![CDATA[and act_amt<>0.00]]>
        </if>
        <if test="txType != null">
            and tx_type in
            <foreach item="item" index="index" collection="txTypeList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        UNION ALL
        select order_no, tx_tm, tx_type, bus_type, order_amt, order_status, '' as incm_pay_flag, user_id, merc_id,
        cash_bal_amt, coupon_amt, coupon_type, crd_pay_amt, crd_pay_type
        from bil_merc_order where merc_id = #{userId, jdbcType=VARCHAR}
        <![CDATA[and tx_type IN ('03')]]>
        <if test="txTmBegin != null">
            <![CDATA[and tx_tm >= #{txTmBegin}]]>
        </if>
        <if test="txTmEnd != null">
            <![CDATA[and tx_tm <= #{txTmEnd}]]>
        </if>
        <if test="txType != null">
            and tx_type in
            <foreach item="item" index="index" collection="txTypeList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY tx_tm DESC
    </select>

    <select id="queryUserBillOrder2" resultMap="BaseResultMap" parameterType="com.hisun.lemon.bil.dto.UserBillOrderDTO" >
        select
        <include refid="User_Bill_List" />
        from bil_user_order where user_id = #{userId, jdbcType=VARCHAR}
        <![CDATA[and tx_type NOT IN ('09')]]>
        <if test="txTmBegin != null">
            <![CDATA[and tx_tm >= #{txTmBegin}]]>
        </if>
        <if test="txTmEnd != null">
            <![CDATA[and tx_tm <= #{txTmEnd}]]>
        </if>
        <if test="actFlag != null">
            <![CDATA[and act_amt<>0.00]]>
        </if>
        <if test="txType != null">
            and tx_type in
            <foreach item="item" index="index" collection="txTypeList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        UNION ALL
        select order_no, tx_tm, tx_type, bus_type, order_amt, order_status, '' as incm_pay_flag, user_id, merc_id,
        cash_bal_amt, coupon_amt, coupon_type, crd_pay_amt, crd_pay_type
        from bil_merc_order where merc_id = #{userId, jdbcType=VARCHAR}
        <![CDATA[and tx_type NOT IN ('09')]]>
        <if test="txTmBegin != null">
            <![CDATA[and tx_tm >= #{txTmBegin}]]>
        </if>
        <if test="txTmEnd != null">
            <![CDATA[and tx_tm <= #{txTmEnd}]]>
        </if>
        <if test="txType != null">
            and tx_type in
            <foreach item="item" index="index" collection="txTypeList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY tx_tm DESC
    </select>

    <select id="queryUserOrderRecord" resultMap="BaseResultMap" parameterType="com.hisun.lemon.bil.dto.UserOrderRecordDTO" >
        select
        <include refid="User_Bill_List" />
        from bil_user_order where user_id = #{userId, jdbcType=VARCHAR}
        <if test="accountNo != null">
            and ac_no = #{accountNo,jdbcType=VARCHAR}
        </if>
        <if test="txTmBegin != null">
            <![CDATA[and tx_tm >= #{txTmBegin}]]>
        </if>
        <if test="txTmEnd != null">
            <![CDATA[and tx_tm <= #{txTmEnd}]]>
        </if>
        <if test="orderFlag != null">
            and incm_pay_flag = #{orderFlag,jdbcType=VARCHAR}
        </if>
        <if test="txType != null">
            and tx_type in
            <foreach item="item" index="index" collection="txTypeList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by tx_tm desc
    </select>

    <select id="queryBillInfo" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from bil_user_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="queryUserAccount" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="User_Account_List" />
        from bil_user_order
        where user_id = #{userId,jdbcType=VARCHAR}
        limit 0,1
    </select>

    <!--查询数币账户收款/充值订单集合-->
    <select id="findOrderNos" resultType="java.lang.String">
        SELECT order_no
        FROM bil_user_order
        <where>
            <if test="acNo != null">
                AND ac_no = #{acNo,jdbcType=BIGINT}
            </if>
            <if test="txType != null">
                AND tx_type = #{txType,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                AND tx_tm &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND tx_tm &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY tx_tm DESC
    </select>
    <select id="DhTypeList" resultType="com.hisun.lemon.bil.dto.DhDataDTO">
        SELECT t.order_no AS orderNo,
        t.from_coin AS fromCoin,
        t.from_amount AS fromAmount,
        t.to_coin AS toCoin,
        t.to_amount AS toAmount
        FROM tam_exchange_order t
        JOIN bil_user_order b ON t.order_no = b.order_no
        WHERE
        b.tx_tm &gt;= #{startTime,jdbcType=TIMESTAMP}
        AND b.tx_tm &lt; #{endTime,jdbcType=TIMESTAMP}
        AND b.tx_type = #{txType}
        AND b.order_status = 'S';
    </select>

    <select id="getBillByType" resultType="com.hisun.lemon.bil.dto.BillTypeDTO">
        select
        order_no AS orderNo,
        tx_tm AS txTm,
        tx_type AS txType,
        bus_type AS busType,
        order_amt AS orderAmt,
        ccy AS ccy,
        gas AS gas,
        fee AS fee
        from bil_user_order
        where  tx_tm &gt;= #{startTime,jdbcType=TIMESTAMP}
        AND tx_tm &lt; #{endTime,jdbcType=TIMESTAMP}
        <if test="txTypeList != null and !txTypeList.isEmpty()">
            AND tx_type IN
            <foreach item="type" collection="txTypeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        AND order_status = 'S';
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from bil_user_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.bil.entity.UserBillOrderDO" >
        insert into bil_user_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="txTm != null" >
                tx_tm,
            </if>
            <if test="txType != null" >
                tx_type,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="orderAmt != null" >
                order_amt,
            </if>
            <if test="orderStatus != null" >
                order_status,
            </if>
            <if test="incmPayFlag != null" >
                incm_pay_flag,
            </if>
            <if test="mobileNo != null" >
                mobile_no,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="mercId != null" >
                merc_id,
            </if>
            <if test="cashBalAmt != null">
                cash_bal_amt,
            </if>
            <if test="couponAmt != null">
                coupon_amt,
            </if>
            <if test="couponType != null">
                coupon_type,
            </if>
            <if test="invBalAmt != null">
                inv_bal_amt,
            </if>
            <if test="actAmt != null">
                act_amt,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="crdPayAmt != null">
                crd_pay_amt,
            </if>
            <if test="crdPayType != null">
                crd_pay_type,
            </if>
            <if test="orderChannel != null" >
                order_channel,
            </if>
            <if test="mercName != null" >
                merc_name,
            </if>
            <if test="goodsInfo != null" >
                goods_info,
            </if>
            <if test="remitInfo != null" >
                remit_info,
            </if>
            <if test="orgOrderNo != null" >
                org_order_no,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="payMod != null" >
                pay_mod,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="ccy != null" >
                ccy,
            </if>
            <if test="acNo != null" >
                ac_no,
            </if>
            <if test="toAcNo != null">
                to_ac_no,
            </if>
            <if test="txHash != null">
                tx_hash,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="txTm != null" >
                #{txTm,jdbcType=TIMESTAMP},
            </if>
            <if test="txType != null" >
                #{txType,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null" >
                #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="incmPayFlag != null" >
                #{incmPayFlag,jdbcType=CHAR},
            </if>
            <if test="mobileNo != null" >
                #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mercId != null" >
                #{mercId,jdbcType=VARCHAR},
            </if>
            <if test="cashBalAmt != null" >
                #{cashBalAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponAmt != null" >
                #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponType != null" >
                #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="invBalAmt != null" >
                #{invBalAmt,jdbcType=DECIMAL},
            </if>
            <if test="actAmt != null" >
                #{actAmt,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="crdPayAmt != null" >
                #{crdPayAmt,jdbcType=DECIMAL},
            </if>
            <if test="crdPayType != null" >
                #{crdPayType,jdbcType=VARCHAR},
            </if>
            <if test="orderChannel != null" >
                #{orderChannel,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null" >
                #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="remitInfo != null" >
                #{remitInfo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrderNo != null" >
                #{orgOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="payMod != null" >
                #{payMod,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="acNo != null" >
                #{acNo,jdbcType=VARCHAR},
            </if>
            <if test="toAcNo != null" >
                #{toAcNo,jdbcType=VARCHAR},
            </if>
            <if test="txHash != null" >
                #{txHash,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.bil.entity.UserBillOrderDO" >
        update bil_user_order
        <set >
            <if test="txTm != null" >
                tx_tm = #{txTm,jdbcType=TIMESTAMP},
            </if>
            <if test="txType != null" >
                tx_type = #{txType,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                order_amt = #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null" >
                order_status = #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="incmPayFlag != null" >
                incm_pay_flag = #{incmPayFlag,jdbcType=CHAR},
            </if>
            <if test="mobileNo != null" >
                mobile_no = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mercId != null" >
                merc_id = #{mercId,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                merc_name = #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="cashBalAmt != null" >
                cash_bal_amt = #{cashBalAmt,jdbcType=DECIMAL},
            </if>
            <if test="invBalAmt != null" >
                inv_bal_amt = #{invBalAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponAmt != null" >
                coupon_amt = #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="couponType != null" >
                coupon_type = #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="crdPayAmt != null" >
                crd_pay_amt = #{crdPayAmt,jdbcType=DECIMAL},
            </if>
            <if test="crdPayType != null" >
                crd_pay_type = #{crdPayType,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null" >
                goods_info = #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="payMod != null" >
                pay_mod = #{payMod,jdbcType=VARCHAR},
            </if>
            <if test="orderChannel != null" >
                order_channel = #{orderChannel,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="abstractInfo != null" >
                abstract_info = #{abstractInfo,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ccy != null" >
                ccy = #{txCct,jdbcType=VARCHAR},
            </if>
            <if test="acNo != null" >
                ac_no = #{acNo,jdbcType=VARCHAR},
            </if>
            <if test="toAcNo != null" >
                to_ac_no = #{toAcNo,jdbcType=VARCHAR},
            </if>
            <if test="txHash != null" >
                tx_hash = #{txHash,jdbcType=VARCHAR},
            </if>
            <if test="gas != null" >
                gas = #{gas,jdbcType=DECIMAL},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>

    <!-- 查询指定日期范围内的交易统计，按币种分组 -->
    <select id="queryTransactionStatisticsByCurrency" resultType="java.util.Map">
        <![CDATA[
        SELECT 
            ccy as currency,
            SUM(order_amt) as totalAmount,
            COUNT(*) as totalCount,
            SUM(CASE WHEN order_status IN ('S', 'S1') THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN order_status IN ('S', 'S1') THEN order_amt ELSE 0 END) as successAmount
        FROM bil_user_order 
        WHERE tx_tm >= #{startTime} 
        AND tx_tm < #{endTime}
        AND order_amt > 0
        GROUP BY ccy
        ORDER BY totalAmount DESC
        ]]>
    </select>

    <!-- 查询指定日期范围内的交易统计汇总 -->
    <select id="queryTransactionStatisticsSummary" resultType="java.util.Map">
        <![CDATA[
        SELECT 
            SUM(order_amt) as totalAmount,
            COUNT(*) as totalCount,
            SUM(CASE WHEN order_status IN ('S', 'S1') THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN order_status IN ('S', 'S1') THEN order_amt ELSE 0 END) as successAmount,
            CASE 
                WHEN COUNT(*) > 0 THEN 
                    ROUND(SUM(CASE WHEN order_status IN ('S', 'S1') THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0 
            END as successRate
        FROM bil_user_order 
        WHERE tx_tm >= #{startTime} 
        AND tx_tm < #{endTime}
        AND order_amt > 0
        ]]>
    </select>

    <!-- 查询昨日交易统计（用于比较趋势） -->
    <select id="queryYesterdayTransactionStatistics" resultType="java.util.Map">
        <![CDATA[
        SELECT 
            SUM(order_amt) as totalAmount,
            COUNT(*) as totalCount,
            SUM(CASE WHEN order_status IN ('S', 'S1') THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN order_status IN ('S', 'S1') THEN order_amt ELSE 0 END) as successAmount,
            CASE 
                WHEN COUNT(*) > 0 THEN 
                    ROUND(SUM(CASE WHEN order_status IN ('S', 'S1') THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0 
            END as successRate
        FROM bil_user_order 
        WHERE tx_tm >= #{startTime} 
        AND tx_tm < #{endTime}
        AND order_amt > 0
        ]]>
    </select>

</mapper>