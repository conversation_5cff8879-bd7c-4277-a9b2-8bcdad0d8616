<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.bil.dao.BusinessIndicatorsDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.bil.entity.BusinessIndicatorsDO" >
        <result column="id" property="id" jdbcType="BIGINT" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="num" property="num" jdbcType="BIGINT" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="success_rate" property="successRate" jdbcType="DECIMAL" />
        <result column="amt_compare_yesterday" property="amtCompareYesterday" jdbcType="VARCHAR" />
        <result column="num_compare_yesterday" property="numCompareYesterday" jdbcType="VARCHAR" />
        <result column="tr_data" property="trData" jdbcType="DATE" />
    </resultMap>
    
    <insert id="insert" parameterType="com.hisun.lemon.bil.entity.BusinessIndicatorsDO" useGeneratedKeys="true" keyProperty="id">
        <![CDATA[
        INSERT INTO bil_business_indicators
        ]]>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="amt != null" >
                amt,
            </if>
            <if test="num != null" >
                num,
            </if>
            <if test="ccy != null" >
                ccy,
            </if>
            <if test="successRate != null" >
                success_rate,
            </if>
            <if test="amtCompareYesterday != null" >
                amt_compare_yesterday,
            </if>
            <if test="numCompareYesterday != null" >
                num_compare_yesterday,
            </if>
            <if test="trData != null" >
                tr_data,
            </if>
        </trim>
        <![CDATA[
        VALUES
        ]]>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="amt != null" >
                #{amt,jdbcType=DECIMAL},
            </if>
            <if test="num != null" >
                #{num,jdbcType=BIGINT},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="successRate != null" >
                #{successRate,jdbcType=DECIMAL},
            </if>
            <if test="amtCompareYesterday != null" >
                #{amtCompareYesterday,jdbcType=VARCHAR},
            </if>
            <if test="numCompareYesterday != null" >
                #{numCompareYesterday,jdbcType=VARCHAR},
            </if>
            <if test="trData != null" >
                #{trData,jdbcType=DATE},
            </if>
        </trim>
    </insert>
    
    <select id="findLatestByTrData" parameterType="com.hisun.lemon.bil.entity.BusinessIndicatorsDO"
            resultMap="BaseResultMap">
        <![CDATA[
        SELECT * FROM bil_business_indicators
        WHERE tr_data = #{trData}
        ORDER BY tr_data DESC
            LIMIT 1
        ]]>
    </select>

    <select id="findByDateRange" parameterType="java.util.Map"
            resultMap="BaseResultMap">
        <![CDATA[
        SELECT * FROM bil_business_indicators
        WHERE tr_data >= #{startDate} AND tr_data <= #{endDate}
        ORDER BY tr_data ASC
        ]]>
    </select>

    <select id="findMonthlyTransactionAmount" parameterType="java.util.Map"
            resultType="java.util.Map">
        <![CDATA[
        SELECT 
            DATE_FORMAT(tr_data, '%Y-%m') as yearMonth,
            COALESCE(SUM(amt), 0) as totalAmount
        FROM bil_business_indicators
        WHERE tr_data >= #{startDate} AND tr_data <= #{endDate}
        GROUP BY DATE_FORMAT(tr_data, '%Y-%m')
        ORDER BY yearMonth ASC
        ]]>
    </select>

    <delete id="deleteById" parameterType="java.lang.Long" >
        <![CDATA[
        delete from bil_business_indicators
        where id = #{id,jdbcType=BIGINT}
        ]]>
    </delete>

</mapper>