<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace等于mapper接口类的全限定名,这样实现对应 -->
<mapper namespace="com.hisun.lemon.bil.dao.ITranBillDataDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.bil.entity.TranBillDataDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="in_amt" property="inAmt" jdbcType="DECIMAL" />
        <result column="out_amt" property="outAmt" jdbcType="DECIMAL" />
        <result column="ccy" property="ccy" jdbcType="CHAR" />
        <result column="tx_type" property="txType" jdbcType="VARCHAR" />
        <result column="tr_date" property="trDate" jdbcType="DATE" />
    </resultMap>

    <sql id="Base_Column_List">
        id, in_amt, out_amt, ccy, tx_type, tr_date
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into bil_transaction_data_statistics (in_amt, out_amt, ccy, tx_type, tr_data) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.inAmt},#{item.outAmt},#{item.ccy},#{item.txType},#{item.trDate})
        </foreach>
    </insert>

    <select id="queryList" resultType="com.hisun.lemon.bil.dto.TransactionDataList">
        SELECT in_amt AS inAmt,
        out_amt AS outAmt,
        tx_type AS txType,
        ccy AS ccy,
        tr_data AS trDate
        FROM bil_transaction_data_statistics
        WHERE tr_data = #{date}

    </select>

    <delete id="deleteByDate">
        delete from bil_transaction_data_statistics where tr_data = #{date}
    </delete>

</mapper>