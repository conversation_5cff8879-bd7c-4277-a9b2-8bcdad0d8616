<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.bil.dao.IMercBillOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.bil.entity.MercBillOrderDO" >
        <id column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="tx_tm" property="txTm" jdbcType="TIMESTAMP" />
        <result column="tx_type" property="txType" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="order_status" property="orderStatus" jdbcType="CHAR" />
        <result column="mobile_no" property="mobileNo" jdbcType="VARCHAR" />
        <result column="merc_id" property="mercId" jdbcType="VARCHAR" />
        <result column="merc_name" property="mercName" jdbcType="VARCHAR" />
        <result column="opr_name" property="oprName" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="goods_info" property="goodsInfo" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="CHAR" />
        <result column="pay_mod" property="payMod" jdbcType="VARCHAR" />
        <result column="cash_bal_amt" property="cashBalAmt" jdbcType="DECIMAL" />
        <result column="coupon_amt" property="couponAmt" jdbcType="DECIMAL" />
        <result column="coupon_type" property="couponType" jdbcType="VARCHAR" />
        <result column="crd_pay_amt" property="crdPayAmt" jdbcType="DECIMAL" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="rfd_amt" property="rfdAmt" jdbcType="DECIMAL" />
        <result column="rfd_sea_coin" property="rfdSeaCoin" jdbcType="DECIMAL" />
        <result column="crd_pay_type" property="crdPayType" jdbcType="VARCHAR" />
        <result column="org_order_no" property="orgOrderNo" jdbcType="VARCHAR" />
        <result column="rfd_reason" property="rfdReason" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="bus_order_no" property="busOrderNo" jdbcType="VARCHAR" />
        <result column="rfd_ord_tm" property="rfdOrdTm" jdbcType="TIMESTAMP" />
        <result column="pay_ord_tm" property="payOrdTm" jdbcType="TIMESTAMP" />
        <result column="undo_ord_tm" property="undoOrdTm" jdbcType="TIMESTAMP" />
        <result column="check_date" property="checkDate" jdbcType="DATE" />
        <result column="serve_fee" property="serveFee" jdbcType="DECIMAL" />
        <result column="order_channel" property="orderChannel" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tx_date" property="txDate" jdbcType="DATE" />
        <result column="count" property="count" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
        order_no, tx_tm, tx_type, order_amt, order_status, mobile_no, merc_id,
        user_id, goods_info, bus_type, pay_mod, merc_name, coupon_amt, coupon_type,
        rfd_reason, remark, pay_ord_tm, undo_ord_tm, crd_pay_type, fee,
        crd_pay_type, org_order_no, rfd_amt, rfd_sea_coin, rfd_ord_tm, bus_order_no, opr_name
    </sql>

    <sql id="Merc_Bill_List" >
        order_no, tx_tm, order_amt, rfd_amt, order_status, mobile_no, bus_order_no, pay_ord_tm, bus_type, user_id, tx_type, merc_id
    </sql>

    <sql id="Merc_Account_List" >
        order_no, cash_bal_amt, merc_id
    </sql>

    <sql id="Merc_Data_List" >
        order_no, tx_type, order_amt, tx_tm, user_id, mobile_no, goods_info, merc_name,
        rfd_amt, rfd_reason, org_order_no, pay_ord_tm, rfd_ord_tm, undo_ord_tm, remark
    </sql>

    <sql id="Merc_Detail_Date_List" >
        order_no, check_date, tx_tm, tx_type, order_amt, coupon_amt, coupon_type,
        serve_fee, pay_mod, order_channel, order_status
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from bil_merc_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="query" resultType="Integer" parameterType="java.lang.String" >
        select
        count(order_no)
        from bil_merc_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="queryMercBillOrder" resultMap="BaseResultMap" parameterType="com.hisun.lemon.bil.dto.MercBillOrderDTO" >
        select
        <include refid="Merc_Bill_List" />
        from bil_merc_order where merc_id = #{mercId, jdbcType=VARCHAR}
          <![CDATA[and tx_type NOT IN ('06','09') and bus_type <> '0405']]>
        <if test="txTmBegin != null">
            <![CDATA[and tx_tm >= #{txTmBegin}]]>
        </if>
        <if test="txTmEnd != null">
            <![CDATA[and tx_tm <= #{txTmEnd}]]>
        </if>
        <if test="orderStatus != null">
            and order_status in
            <foreach item="item" index="index" collection="orderStatusList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="payType == 'Seatelpay'.toString()">
            and pay_mod NOT IN ('WeChat','Alipay','BESTPAY')
        </if>
        <if test="payType != null and payType != 'Seatelpay'.toString()">
            and pay_mod = #{payType}
        </if>
        <if test="busType != null">
            and bus_type = #{busType}
        </if>
        ORDER BY tx_tm DESC
    </select>

    <select id="queryMercDetails" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from bil_merc_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
        and merc_id = #{mercId,jdbcType=VARCHAR}
    </select>

    <select id="queryMercAccount" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Merc_Account_List" />
        from bil_merc_order
        where merc_id = #{mercId,jdbcType=VARCHAR}
        limit 0,1
    </select>

    <select id="queryTodayIncome" resultType="decimal">
        select
        IFNULL(SUM(order_amt),0.00)-IFNULL(SUM(rfd_amt),0.00)
        from bil_merc_order
        where merc_id = #{mercId,jdbcType=VARCHAR}
        and order_status in ('S','R1','R2')
        <![CDATA[and (tx_type <> '06')]]>
        <![CDATA[and tx_tm>=CAST(CAST(SYSDATE()AS DATE)AS DATETIME)]]>
        <![CDATA[AND tx_tm<= #{curTm,jdbcType=TIMESTAMP}]]>
    </select>

    <select id="queryTodayRefund" resultType="decimal" parameterType="java.lang.String" >
        select
        IFNULL(SUM(rfd_amt),0.00)
        from bil_merc_order
        where merc_id = #{mercId,jdbcType=VARCHAR}
        and order_status in ('S','R1','R2')
        <![CDATA[and (tx_type <> '06')]]>
        <![CDATA[and tx_tm>=CAST(CAST(SYSDATE()AS DATE)AS DATETIME)]]>
        <![CDATA[AND tx_tm<=NOW()]]>
    </select>

    <select id="queryData" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Merc_Data_List" />
        from bil_merc_order
        where merc_id = #{mercId,jdbcType=VARCHAR}
        and order_status = 'S'
        <![CDATA[and tx_tm>=CAST((CAST(SYSDATE()AS DATE) - INTERVAL 1 DAY)AS DATETIME)]]>
        <![CDATA[and tx_tm<CAST(CAST(SYSDATE()AS DATE)AS DATETIME)]]>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from bil_merc_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.bil.entity.MercBillOrderDO" >
        insert into bil_merc_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="txTm != null" >
                tx_tm,
            </if>
            <if test="txType != null" >
                tx_type,
            </if>
            <if test="orderAmt != null" >
                order_amt,
            </if>
            <if test="orderStatus != null" >
                order_status,
            </if>
            <if test="mobileNo != null" >
                mobile_no,
            </if>
            <if test="mercId != null" >
                merc_id,
            </if>
            <if test="mercName != null" >
                merc_name,
            </if>
            <if test="oprName != null" >
                opr_name,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="goodsInfo != null" >
                goods_info,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="payMod != null" >
                pay_mod,
            </if>
            <if test="cashBalAmt != null" >
                cash_bal_amt,
            </if>
            <if test="couponAmt != null" >
                coupon_amt,
            </if>
            <if test="couponType != null" >
                coupon_type,
            </if>
            <if test="crdPayAmt != null" >
                crd_pay_amt,
            </if>
            <if test="fee != null" >
                fee,
            </if>
            <if test="crdPayType != null" >
                crd_pay_type,
            </if>
            <if test="orgOrderNo != null" >
                org_order_no,
            </if>
            <if test="rfdReason != null" >
                rfd_reason,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="busOrderNo != null" >
                bus_order_no,
            </if>
            <if test="payOrdTm != null" >
                pay_ord_tm,
            </if>
            <if test="undoOrdTm != null" >
                undo_ord_tm,
            </if>
            <if test="orderChannel != null" >
                order_channel,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="txTm != null" >
                #{txTm,jdbcType=TIMESTAMP},
            </if>
            <if test="txType != null" >
                #{txType,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null" >
                #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="mobileNo != null" >
                #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="mercId != null" >
                #{mercId,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="oprName != null" >
                #{oprName,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null" >
                #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=CHAR},
            </if>
            <if test="payMod != null" >
                #{payMod,jdbcType=VARCHAR},
            </if>
            <if test="cashBalAmt != null" >
                #{cashBalAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponAmt != null" >
                #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponType != null" >
                #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="crdPayAmt != null" >
                #{crdPayAmt,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="crdPayType != null" >
                #{crdPayType,jdbcType=VARCHAR},
            </if>
            <if test="orgOrderNo != null" >
                #{orgOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="rfdReason != null" >
                #{rfdReason,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="busOrderNo != null" >
                #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payOrdTm != null" >
                #{payOrdTm,jdbcType=TIMESTAMP},
            </if>
            <if test="undoOrdTm != null" >
                #{undoOrdTm,jdbcType=TIMESTAMP},
            </if>
            <if test="orderChannel != null" >
                #{orderChannel,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.bil.entity.MercBillOrderDO" >
        update bil_merc_order
        <set >
            <if test="txTm != null" >
                tx_tm = #{txTm,jdbcType=TIMESTAMP},
            </if>
            <if test="txType != null" >
                tx_type = #{txType,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                order_amt = #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponAmt != null" >
                coupon_amt = #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponType != null" >
                coupon_type = #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="crdPayAmt != null" >
                crd_pay_amt = #{crdPayAmt,jdbcType=DECIMAL},
            </if>
            <if test="crdPayType != null" >
                crd_pay_type = #{crdPayType,jdbcType=VARCHAR},
            </if>
            <if test="fee != null" >
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="rfdAmt != null" >
                rfd_amt = rfd_amt + #{rfdAmt,jdbcType=DECIMAL},
            </if>
            <if test="rfdSeaCoin != null" >
                rfd_sea_coin = rfd_sea_coin + #{rfdSeaCoin,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null" >
                order_status = #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="cashBalAmt != null" >
                cash_bal_amt = #{cashBalAmt,jdbcType=DECIMAL},
            </if>
            <if test="mobileNo != null" >
                mobile_no = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="mercId != null" >
                merc_id = #{mercId,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                merc_name = #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="oprName != null" >
                opr_name = #{oprName,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null" >
                goods_info = #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=CHAR},
            </if>
            <if test="payMod != null" >
                pay_mod = #{payMod,jdbcType=VARCHAR},
            </if>
            <if test="rfdReason != null" >
                rfd_reason = #{rfdReason,jdbcType=VARCHAR},
            </if>
            <if test="rfdOrdTm != null" >
                rfd_ord_tm = #{rfdOrdTm,jdbcType=TIMESTAMP},
            </if>
            <if test="payOrdTm != null" >
                pay_ord_tm = #{payOrdTm,jdbcType=TIMESTAMP},
            </if>
            <if test="undoOrdTm != null" >
                undo_ord_tm = #{undoOrdTm,jdbcType=TIMESTAMP},
            </if>
            <if test="checkDate != null" >
                check_date = #{checkDate,jdbcType=DATE},
            </if>
            <if test="serveFee != null" >
                serve_fee = #{serveFee,jdbcType=DECIMAL},
            </if>
            <if test="orderChannel != null" >
                order_channel = #{orderChannel,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>

    <select id="queryDetailByDate" resultMap="BaseResultMap" parameterType="com.hisun.lemon.bil.dto.MercChkDetailExportDTO" >
        select
        <include refid="Merc_Detail_Date_List" />
        from bil_merc_order
        where merc_id = #{mercId}
            <if test="type != null and type=='T'.toString()">
                <if test="beginTime != null">
                    <![CDATA[and tx_tm>= #{beginTime}]]>
                </if>
                <if test="endTime != null">
                    <![CDATA[and tx_tm<= #{endTime}]]>
                </if>
                ORDER BY tx_tm DESC
            </if>
            <if test="type != null and type=='C'.toString()">
                <if test="beginDate != null">
                    <![CDATA[and check_date >= #{beginDate}]]>
                </if>
                <if test="endDate != null">
                    <![CDATA[and check_date <= #{endDate}]]>
                </if>
                ORDER BY check_date DESC
            </if>
    </select>

    <select id="queryMercCsmDetails" resultMap="BaseResultMap" parameterType="com.hisun.lemon.bil.dto.MercCsmInfoDTO" >
        <if test="dateType!=null and dateType == 'C'.toString()">
            SELECT check_date, MAX(merc_id) AS merc_id, MAX(merc_name) AS merc_name,
            SUM(CASE WHEN order_status IN ('S','S1','S2','R1') THEN order_amt WHEN order_status = 'R1' THEN -rfd_amt END) AS order_amt, SUM(CASE WHEN order_status IN ('S','S1','S2','R1') THEN serve_fee END) AS serve_fee, COUNT(order_no) AS COUNT
            FROM bil_merc_order
            where merc_id = #{mercId,jdbcType=VARCHAR}
                <if test="beginDate != null">
                    <![CDATA[and check_date>= #{beginDate}]]>
                </if>
                <if test="endDate != null">
                    <![CDATA[and check_date<= #{endDate}]]>
                </if>
            GROUP BY check_date ORDER BY check_date DESC
        </if>
        <if test="dateType!=null and dateType == 'T'.toString()">
            SELECT DATE_FORMAT(tx_tm,'%Y-%m-%d') AS tx_date, MAX(merc_id) AS merc_id, MAX(merc_name) AS merc_name,
            SUM(CASE WHEN order_status IN ('S','S1','S2','R1') THEN order_amt WHEN order_status = 'R1' THEN -rfd_amt END) AS order_amt, SUM(CASE WHEN order_status IN ('S','S1','S2','R1') THEN serve_fee END) AS serve_fee, COUNT(order_no) AS COUNT
            FROM bil_merc_order
            WHERE merc_id = #{mercId,jdbcType=VARCHAR}
            <if test="beginTime != null">
                <![CDATA[and tx_tm>= #{beginTime}]]>
            </if>
            <if test="endTime != null">
                <![CDATA[and tx_tm<= #{endTime}]]>
            </if>
            GROUP BY tx_date ORDER BY tx_date DESC
        </if>
    </select>

    <select id="queryMercUpBillOrder" resultMap="BaseResultMap" parameterType="com.hisun.lemon.bil.dto.MercUpBillOrderDTO" >
        select
        <include refid="Merc_Bill_List" />
        from bil_merc_order where 1=1
        <if test="busOrderNo != null" >
            and bus_order_no = #{busOrderNo, jdbcType=VARCHAR}
        </if>
        <if test="cshOrderNo != null" >
            and order_no = #{cshOrderNo, jdbcType=VARCHAR}
        </if>
        <if test="txTmBegin != null">
            <![CDATA[and tx_tm >= #{txTmBegin}]]>
        </if>
        <if test="txTmEnd != null">
            <![CDATA[and tx_tm <= #{txTmEnd}]]>
        </if>
        <if test="orderStatus != null">
            and order_status = #{orderStatus, jdbcType=VARCHAR}
        </if>
        <if test="mercId != null">
            and merc_id in
            <foreach item="item" index="index" collection="mercIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY tx_tm DESC
    </select>


</mapper>