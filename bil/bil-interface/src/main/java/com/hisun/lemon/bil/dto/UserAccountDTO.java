package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 用户账户信息 传输对象
 * <AUTHOR>
 * @date 2017/7/31
 * @time 19:11
 */
@ClientValidated
@ApiModel("用户账户信息")
public class UserAccountDTO extends GenericDTO<NoBody>{
    /**
     *  用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    @Length(max = 20)
    private String userId;

    /**
     *  币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }
}
