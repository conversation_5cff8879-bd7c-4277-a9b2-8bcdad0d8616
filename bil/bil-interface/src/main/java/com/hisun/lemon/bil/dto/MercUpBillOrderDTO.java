package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户账单信息 传输对象
 * <AUTHOR>
 * @date 2017/7/12
 * @time 18:03
 */
@ClientValidated
@ApiModel("商户账单信息")
public class MercUpBillOrderDTO extends GenericDTO<NoBody>{

    /**
     * 交易起始（时间类型）
     */
	@ApiModelProperty(name = "txTmBeginStr", value = "交易起始（时间类型）yyyyMMdd")
    private String txTmBeginStr;
    private LocalDateTime txTmBegin;

    /**
     * 交易结束（时间类型）
     */
	@ApiModelProperty(name = "txTmEndStr", value = " 交易结束（时间类型）yyyyMMdd")
    private String txTmEndStr;

    private LocalDateTime txTmEnd;
    /**
     * @Fields orderStatus 'W'待支付、'P'支付待确认、'S'成功、'F'失败、'R1'部分退款、'R2'全部退款、'E'超时、'C'撤销
     */
	@ApiModelProperty(name = "orderStatus", value = "订单状态('W'待支付|'P'支付待确认|'S'成功|'F'失败|'R1'部分退款|'R2'全部退款|'E'超时|'C'撤销)用','隔开")
    private String orderStatus;

    /**
     * @Fields mercId 商户编号
     */
	@ApiModelProperty(name = "mercId", value = "商户编号")
    @Length(max = 20)
    private String mercId;
    /**
     * 单页记录数
     */
	@ApiModelProperty(name = "pageSize", value = "每页大小")
    @NotNull(message = "BIL10007")
    @Min(value=1, message="BIL10008")
    private Integer pageSize;
    /**
     * 页数
     */
	@ApiModelProperty(name = "pageNo", value = " 页码")
    @NotNull(message = "BIL10009")
    @Min(value=1, message="BIL10010")
    private Integer pageNo;

    /**
     * @Fields busOrderNo 业务订单号
     */
    @ApiModelProperty(name = "busOrderNo", value = "业务订单号")
    private String busOrderNo;

    /**
     * @Fields cshOrderNo 收银订单号
     */
    @ApiModelProperty(name = "cshOrderNo", value = "收银订单号")
    private String cshOrderNo;

    /**
     * 订单状态List
     */
    @ApiModelProperty(hidden = true)
    private List<String> mercIdList;

    public List<String> getMercIdList() {
        return mercIdList;
    }

    public void setMercIdList(List<String> mercIdList) {
        this.mercIdList = mercIdList;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getCshOrderNo() {
        return cshOrderNo;
    }

    public void setCshOrderNo(String cshOrderNo) {
        this.cshOrderNo = cshOrderNo;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public String getTxTmBeginStr() {
        return txTmBeginStr;
    }

    public void setTxTmBeginStr(String txTmBeginStr) {
        this.txTmBeginStr = txTmBeginStr;
    }

    public String getTxTmEndStr() {
        return txTmEndStr;
    }

    public void setTxTmEndStr(String txTmEndStr) {
        this.txTmEndStr = txTmEndStr;
    }

    public LocalDateTime getTxTmBegin() {
        return txTmBegin;
    }

    public void setTxTmBegin(LocalDateTime txTmBegin) {
        this.txTmBegin = txTmBegin;
    }

    public LocalDateTime getTxTmEnd() {
        return txTmEnd;
    }

    public void setTxTmEnd(LocalDateTime txTmEnd) {
        this.txTmEnd = txTmEnd;
    }

}
