package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 商户账单清分信息 传输对象
 * <AUTHOR>
 * @date 2017/7/12
 * @time 18:03
 */
@ClientValidated
@ApiModel("商户账单清分信息")
public class MercCsmInfoRspDTO{

    private List<MercCsmInfo> list;

    private Long totalNumber;

    public static class MercCsmInfo {
        /**
         * 清分日期
         */
        @ApiModelProperty(name = "checkDate", value = "清分日期")
        private LocalDate checkDate;
        /**
         * 交易日期
         */
        @ApiModelProperty(name = "txDate", value = "交易日期")
        private LocalDate txDate;
        /**
         * 商户编号
         */
        @ApiModelProperty(name = "mercId", value = "商户编号")
        private String mercId;
        /**
         * 商户名称
         */
        @ApiModelProperty(name = "mercName", value = "商户名称")
        private String mercName;
        /**
         * 订单金额
         */
        @ApiModelProperty(name = "orderAmt", value = "订单金额")
        private BigDecimal orderAmt;
        /**
         * 商户服务费
         */
        @ApiModelProperty(name = "serveFee", value = "商户服务费")
        private BigDecimal serveFee;
        /**
         * 实收金额
         */
        @ApiModelProperty(name = "actAmt", value = "实收金额")
        private BigDecimal actAmt;
        /**
         * 交易笔数
         */
        @ApiModelProperty(name = "count", value = "交易笔数")
        private Integer count;

        public LocalDate getCheckDate() {
            return checkDate;
        }

        public void setCheckDate(LocalDate checkDate) {
            this.checkDate = checkDate;
        }

        public LocalDate getTxDate() {
            return txDate;
        }

        public void setTxDate(LocalDate txDate) {
            this.txDate = txDate;
        }

        public String getMercId() {
            return mercId;
        }

        public void setMercId(String mercId) {
            this.mercId = mercId;
        }

        public String getMercName() {
            return mercName;
        }

        public void setMercName(String mercName) {
            this.mercName = mercName;
        }

        public BigDecimal getOrderAmt() {
            return orderAmt;
        }

        public void setOrderAmt(BigDecimal orderAmt) {
            this.orderAmt = orderAmt;
        }

        public BigDecimal getServeFee() {
            return serveFee;
        }

        public void setServeFee(BigDecimal serveFee) {
            this.serveFee = serveFee;
        }

        public BigDecimal getActAmt() {
            return actAmt;
        }

        public void setActAmt(BigDecimal actAmt) {
            this.actAmt = actAmt;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }
    }

    public List<MercCsmInfo> getList() {
        return list;
    }

    public void setList(List<MercCsmInfo> list) {
        this.list = list;
    }

    public Long getTotalNumber() {
        return totalNumber;
    }

    public void setTotalNumber(Long totalNumber) {
        this.totalNumber = totalNumber;
    }
}
