package com.hisun.lemon.bil.client;

import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.framework.data.GenericRspDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 运营端首页-数据看板
 * <AUTHOR>
 * @date 2018年01月10日
 * @time 下午5:47:24
 *
 */
@FeignClient("BIL")
public interface DataBoardClient {
    /**
     * 获取业务核心指标
     */
    @GetMapping(value = "/bil/DataBoard/business/indicators")
    public GenericRspDTO<BusinessIndicatorsRspDTO> getBusinessIndicators(@RequestParam(value = "type", required = true) String type);

    /**
     * 获取业务数据趋势
     */
    @GetMapping(value="/bil/DataBoard/business/dataTrends")
    public GenericRspDTO<List<Map<String, String>>> getBusinessDataTrends(
            @RequestParam(value = "startDate", required = true) String startDate,
            @RequestParam(value = "endDate", required = true) String endDate);


   /**
     * 获取财务数据
     */
    @GetMapping(value="/bil/DataBoard/business/financialData")
    public GenericRspDTO<Map<String, BigDecimal>> getFinancialData();


    @ApiOperation(value = "获取数据统计", notes = "获取数据统计")
    @GetMapping(value = "/bil/DataBoard/dhDataStatistics")
    public GenericRspDTO<List<TransactionDataList>> getDhDataStatistics();


    @GetMapping(value = "/bil/DataBoard/agcnInfo")
    public GenericRspDTO<List<AgcyInfoListRspDTO>> getAgcnInfo();

}
