package com.hisun.lemon.bil.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.util.List;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
 * 商户账单信息 传输对象
 * <AUTHOR>
 * @date 2017/7/12
 * @time 18:03
 */
@ClientValidated
@ApiModel("商户账单信息")
public class MercBillOrderDTO extends GenericDTO<NoBody>{

    /**
     * 交易起始（时间类型）
     */
	@ApiModelProperty(name = "txTmBeginStr", value = "交易起始（时间类型）yyyyMMddHHmmss")
    private String txTmBeginStr;
    @ApiModelProperty(hidden = true)
    private LocalDateTime txTmBegin;
    /**
     * 交易结束（时间类型）
     */
	@ApiModelProperty(name = "txTmEndStr", value = " 交易结束（时间类型）yyyyMMddHHmmss")
    private String txTmEndStr;
    @ApiModelProperty(hidden = true)
    private LocalDateTime txTmEnd;
    /**
     * @Fields orderStatus 'W'待支付、'P'支付待确认、'S'成功、'F'失败、'R1'部分退款、'R2'全部退款、'E'超时、'C'撤销
     */
	@ApiModelProperty(name = "orderStatus", value = "订单状态('W'待支付|'P'支付待确认|'S'成功|'F'失败|'R1'部分退款|'R2'全部退款|'E'超时|'C'撤销)用','隔开")
    private String orderStatus;
    /**
     * 订单状态List
     */
    @ApiModelProperty(hidden = true)
    private List<String> orderStatusList;
    /**
     * @Fields mercId 商户编号
     */
	@ApiModelProperty(name = "mercId", value = "商户编号")
    @Length(max = 20)
    private String mercId;
    /**
     * 单页记录数
     */
	@ApiModelProperty(name = "pageSize", value = "每页大小")
    @NotNull(message = "BIL10007")
    @Min(value=1, message="BIL10008")
    private Integer pageSize;
    /**
     * 页数
     */
	@ApiModelProperty(name = "pageNo", value = " 页数")
    @NotNull(message = "BIL10009")
    @Min(value=1, message="BIL10010")
    private Integer pageNo;
    /**
     * 支付方式
     */
    @ApiModelProperty(name = "payMod", value = "支付方式|1、Mpay|2、微信扫码|3、微信条码|4、支付宝扫码|5、支付宝条码|6、翼支付扫码|7、翼支付条码|")
    private String payMod;
    /**
     * 支付方式
     */
    @ApiModelProperty(hidden = true)
    private String payType;
    /**
     * 业务类型
     */
    @ApiModelProperty(hidden = true)
    private String busType;

    public LocalDateTime getTxTmBegin() {
        return txTmBegin;
    }

    public void setTxTmBegin(LocalDateTime txTmBegin) {
        this.txTmBegin = txTmBegin;
    }

    public LocalDateTime getTxTmEnd() {
        return txTmEnd;
    }

    public void setTxTmEnd(LocalDateTime txTmEnd) {
        this.txTmEnd = txTmEnd;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public List<String> getOrderStatusList() {
        return orderStatusList;
    }

    public void setOrderStatusList(List<String> orderStatusList) {
        this.orderStatusList = orderStatusList;
    }

    public String getTxTmBeginStr() {
        return txTmBeginStr;
    }

    public void setTxTmBeginStr(String txTmBeginStr) {
        this.txTmBeginStr = txTmBeginStr;
    }

    public String getTxTmEndStr() {
        return txTmEndStr;
    }

    public void setTxTmEndStr(String txTmEndStr) {
        this.txTmEndStr = txTmEndStr;
    }

    public String getPayMod() {
        return payMod;
    }

    public void setPayMod(String payMod) {
        this.payMod = payMod;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }
}
