package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 收款/充值记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21 16:59
 */
@ClientValidated
@ApiModel(value = "BilReceiptRecord", description = "收款/充值记录明细")
public class BilReceiptRecord {

    /**
     * 交易时间
     */
    @ApiModelProperty(name = "txTime", value = "交易时间")
    private LocalDateTime txTime;

    /**
     * 交易币种
     */
    @ApiModelProperty(name = "coinId", value = "交易币种")
    private String coinId;

    /**
     * 交易金额
     */
    @ApiModelProperty(name = "amount", value = "交易金额")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    /**
     * 收款地址/账户号
     */
    @ApiModelProperty(name = "receiveAddress", value = "收款地址/账户号")
    private String receiveAddress;

    /**
     * 付款地址/账户号
     */
    @ApiModelProperty(name = "payAddress", value = "付款地址/账户号")
    private String payAddress;

    /**
     * 交易流水号
     */
    @ApiModelProperty(name = "orderId", value = "交易流水号")
    private String orderId;

    /**
     * 交易哈希
     */
    @ApiModelProperty(name = "txHash", value = "交易哈希（非链上交易为空）")
    private String txHash;

    /**
     * 交易状态
     */
    @ApiModelProperty(name = "status", value = "交易状态（SUCCESS/FAILED）")
    private String status;

    /**
     * 附言
     */
    @ApiModelProperty(name = "memo", value = "附言（无则为--）")
    private String memo;

    /**
     * 交易渠道
     */
    @ApiModelProperty(name = "channel", value = "交易渠道（如SWIFT）")
    private String channel;

    public LocalDateTime getTxTime() {
        return txTime;
    }

    public void setTxTime(LocalDateTime txTime) {
        this.txTime = txTime;
    }

    public String getCoinId() {
        return coinId;
    }

    public void setCoinId(String coinId) {
        this.coinId = coinId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getReceiveAddress() {
        return receiveAddress;
    }

    public void setReceiveAddress(String receiveAddress) {
        this.receiveAddress = receiveAddress;
    }

    public String getPayAddress() {
        return payAddress;
    }

    public void setPayAddress(String payAddress) {
        this.payAddress = payAddress;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTxHash() {
        return txHash;
    }

    public void setTxHash(String txHash) {
        this.txHash = txHash;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}
