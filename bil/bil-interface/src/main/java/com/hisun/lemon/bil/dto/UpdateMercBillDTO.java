package com.hisun.lemon.bil.dto;

import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 更新订单状态 传输对象
 * <AUTHOR>
 * @date 2017/7/26
 * @time 19:56
 */
public class UpdateMercBillDTO {
    /**
     * 账单编号
     */
    @NotEmpty(message = "BIL10004")
    private String orderNo;
    /**
     * 清分日期
     */
    private LocalDate checkDate;
    /**
     * 商户服务费
     */
    private BigDecimal serveFee;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDate getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(LocalDate checkDate) {
        this.checkDate = checkDate;
    }

    public BigDecimal getServeFee() {
        return serveFee;
    }

    public void setServeFee(BigDecimal serveFee) {
        this.serveFee = serveFee;
    }

    @Override
    public String toString() {
        return "UpdateMercBillDTO{" +
                "orderNo='" + orderNo + '\'' +
                ", checkDate=" + checkDate +
                ", serveFee=" + serveFee +
                '}';
    }
}
