package com.hisun.lemon.bil.dto;

import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生成用户账单信息 传输对象
 * <AUTHOR>
 * @date 2017/7/20
 * @time 10:42
 */
public class CreateUserBillDTO {
    /**
     * 账单编号
     */
    @NotEmpty(message = "BIL10004")
    private String orderNo;
    /**
     * 交易时间
     */
    @NotNull(message = "BIL10011")
    private LocalDateTime txTm;
    private String txTmStr;
    /**
     * 交易类型（展示内容之一）01.充值、02.消费、03.转账、04.提现、05.充海币
     */
    @NotEmpty(message = "BIL10001")
    private String txType;
    /**
     * 账单金额
     */
    @NotNull(message = "BIL10012")
    @Min(value=0, message="BIL10002")
    private BigDecimal orderAmt;
    /**
     * 账单状态 'W'待支付、'P'支付待确认、'S'成功、'F'失败、'R1'部分退款、'R2'全部退款、'E'超时、'C'撤销
     */
    @NotEmpty(message = "BIL10003")
    private String orderStatus;
    /**
     * 付款方编号
     */
    private String payerId;
    /**
     * 收款方编号
     */
    private String payeeId;
    /**
     * 现金账户金额
     */
    private BigDecimal balAmt;
    /**
     * 海币账户金额
     */
    private BigDecimal couponAmt;
    /**
     * 优惠类型
     */
    private String couponType;
    /**
     * 理财账户余额
     */
    private BigDecimal invAmt;
    /**
     * @Fields mercName 商户名称
     */
    private String mercName;
    /**
     * @Fields goodsInfo 商品名称
     */
    private String goodsInfo;
    /**
     * @Fields busType 业务类型 '0201'条码支付、'0202'扫码支付、'0203'APP支付、'0204'POS支付、'0205'银行卡收单
     */
    private String busType;
    /**
     * 退款原因
     */
    private String rfdReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 订单渠道
     */
    private String orderChannel;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 银行卡补款金额
     */
    private BigDecimal crdPayAmt;
    /**
     * 补款类型 0无补款 1网银 2快捷
     */
    private String crdPayType;
    /**
     * 支付方式
     */
    private String payMod;
    /**
     * 原订单号
     */
    private String orgOrderNo;
    /**
     * 商户订单号
     */
    private String busOrderNo;
    /**
     * 拓展字段：用于国际化
     */
    private String extInfo;
    /**
     * 操作员
     */
    private String oprName;
    /**
     * 汇款信息
     */
    private String remitInfo;
    /**
     * 交易币种
     */
    private String ccy;
    /**
     * 交易账户号码
     */
    private String acNo;
    /**
     * 收款账户号码或收款地址
     */
    private String toAcNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(String payeeId) {
        this.payeeId = payeeId;
    }

    public BigDecimal getBalAmt() {
        return balAmt;
    }

    public void setBalAmt(BigDecimal balAmt) {
        this.balAmt = balAmt;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public BigDecimal getInvAmt() {
        return invAmt;
    }

    public void setInvAmt(BigDecimal invAmt) {
        this.invAmt = invAmt;
    }

    public String getTxTmStr() {
        return txTmStr;
    }

    public void setTxTmStr(String txTmStr) {
        this.txTmStr = txTmStr;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getGoodsInfo() {
        return goodsInfo;
    }

    public void setGoodsInfo(String goodsInfo) {
        this.goodsInfo = goodsInfo;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getRfdReason() {
        return rfdReason;
    }

    public void setRfdReason(String rfdReason) {
        this.rfdReason = rfdReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(String orderChannel) {
        this.orderChannel = orderChannel;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getCrdPayAmt() {
        return crdPayAmt;
    }

    public void setCrdPayAmt(BigDecimal crdPayAmt) {
        this.crdPayAmt = crdPayAmt;
    }

    public String getCrdPayType() {
        return crdPayType;
    }

    public void setCrdPayType(String crdPayType) {
        this.crdPayType = crdPayType;
    }

    public String getOrgOrderNo() {
        return orgOrderNo;
    }

    public void setOrgOrderNo(String orgOrderNo) {
        this.orgOrderNo = orgOrderNo;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getPayMod() {
        return payMod;
    }

    public void setPayMod(String payMod) {
        this.payMod = payMod;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getOprName() {
        return oprName;
    }

    public void setOprName(String oprName) {
        this.oprName = oprName;
    }

    public String getRemitInfo() {
        return remitInfo;
    }

    public void setRemitInfo(String remitInfo) {
        this.remitInfo = remitInfo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getToAcNo() {
        return toAcNo;
    }

    public void setToAcNo(String toAcNo) {
        this.toAcNo = toAcNo;
    }

    @Override
    public String toString() {
        return "CreateUserBillDTO{" +
                "orderNo='" + orderNo + '\'' +
                ", txTm=" + txTm +
                ", txTmStr='" + txTmStr + '\'' +
                ", txType='" + txType + '\'' +
                ", orderAmt=" + orderAmt +
                ", orderStatus='" + orderStatus + '\'' +
                ", payerId='" + payerId + '\'' +
                ", payeeId='" + payeeId + '\'' +
                ", balAmt=" + balAmt +
                ", couponAmt=" + couponAmt +
                ", couponType='" + couponType + '\'' +
                ", invAmt=" + invAmt +
                ", mercName='" + mercName + '\'' +
                ", goodsInfo='" + goodsInfo + '\'' +
                ", busType='" + busType + '\'' +
                ", rfdReason='" + rfdReason + '\'' +
                ", remark='" + remark + '\'' +
                ", orderChannel='" + orderChannel + '\'' +
                ", fee=" + fee +
                ", crdPayAmt=" + crdPayAmt +
                ", crdPayType='" + crdPayType + '\'' +
                ", payMod='" + payMod + '\'' +
                ", orgOrderNo='" + orgOrderNo + '\'' +
                ", busOrderNo='" + busOrderNo + '\'' +
                ", extInfo='" + extInfo + '\'' +
                ", oprName='" + oprName + '\'' +
                ", remitInfo='" + remitInfo + '\'' +
                ", ccy='" + ccy + '\'' +
                ", acNo='" + acNo + '\'' +
                ", toAcNo='" + toAcNo + '\'' +
                '}';
    }
}
