package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 商户订单交易详情 传输对象
 * <AUTHOR>
 * @date 2017/7/31
 * @time 19:11
 */
@ClientValidated
@ApiModel(value = "MercBillInfoDTO", description = "商户订单交易详情")
public class MercBillInfoDTO extends GenericDTO<NoBody>{
    /**
     * 订单编号
     */
    @ApiModelProperty(name = "orderNo", value = "账单编号")
    @Length(max = 28)
    @NotEmpty(message = "BIL10004")
    private String orderNo;
    /**
     * 商户号
     */
    @ApiModelProperty(name = "mercId", value = "商户编号")
    private String mercId;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }
}
