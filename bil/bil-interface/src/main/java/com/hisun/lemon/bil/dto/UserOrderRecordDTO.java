package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

public class UserOrderRecordDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(name = "userId", value = "用户id")
    private String userId;

    @ApiModelProperty(name = "accountNo", value = "账户号码")
    private String accountNo;

    @ApiModelProperty(name = "orderFlag", value = "账务方向（出账/入账）")
    private String orderFlag;

    @ApiModelProperty(name = "txType", value = "交易类型(01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费)用','隔开")
    private String txType;

    @ApiModelProperty(hidden = true)
    private List<String> txTypeList;

    /**
     * 交易起始（时间类型）
     */
    @ApiModelProperty(name = "txTmBeginStr", value = "交易起始（时间类型）yyyyMMddHHmmss")
    private String txTmBeginStr;
    @ApiModelProperty(hidden = true)
    private LocalDateTime txTmBegin;
    /**
     * 交易结束（时间类型）
     */
    @ApiModelProperty(name = "txTmEndStr", value = "交易结束（时间类型）yyyyMMddHHmmss")
    private String txTmEndStr;
    @ApiModelProperty(hidden = true)
    private LocalDateTime txTmEnd;

    /**
     * 单页记录数
     */
    @ApiModelProperty(name = "pageSize", value = "每页大小")
    @Min(value=1, message="BIL10008")
    @NotNull(message = "BIL10007")
    private Integer pageSize;
    /**
     * 页数
     */
    @ApiModelProperty(name = "pageNo", value = "页数")
    @Min(value=1, message="BIL10010")
    @NotNull(message = "BIL10009")
    private Integer pageNo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getOrderFlag() {
        return orderFlag;
    }

    public void setOrderFlag(String orderFlag) {
        this.orderFlag = orderFlag;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public List<String> getTxTypeList() {
        return txTypeList;
    }

    public void setTxTypeList(List<String> txTypeList) {
        this.txTypeList = txTypeList;
    }

    public String getTxTmBeginStr() {
        return txTmBeginStr;
    }

    public void setTxTmBeginStr(String txTmBeginStr) {
        this.txTmBeginStr = txTmBeginStr;
    }

    public LocalDateTime getTxTmBegin() {
        return txTmBegin;
    }

    public void setTxTmBegin(LocalDateTime txTmBegin) {
        this.txTmBegin = txTmBegin;
    }

    public String getTxTmEndStr() {
        return txTmEndStr;
    }

    public void setTxTmEndStr(String txTmEndStr) {
        this.txTmEndStr = txTmEndStr;
    }

    public LocalDateTime getTxTmEnd() {
        return txTmEnd;
    }

    public void setTxTmEnd(LocalDateTime txTmEnd) {
        this.txTmEnd = txTmEnd;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }
}
