package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 新增订单请求体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21 11:09
 */
@ClientValidated
@ApiModel(value = "AddOrderReqDTO", description = "新增订单请求传输对象")
public class AddOrderReqDTO {

    /**
     * 账单编号
     */
    @ApiModelProperty(name = "orderNo", value = "账单编号")
    @NotEmpty(message = "BIL10004")
    private String orderNo;

    /**
     * 交易时间
     */
    @ApiModelProperty(name = "txTm", value = "交易时间")
    @NotNull(message = "BIL10011")
    private LocalDateTime txTm;

    /**
     * 交易类型 01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费,DS.数币收款,DC.数币充值,DH.数币兑换,DZ.数币转账,DX.数币提现
     */
    @ApiModelProperty(name = "txType", value = "交易类型 01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费,DS.数币收款,DC.数币充值,DH.数币兑换,DZ.数币转账,DX.数币提现")
    @NotEmpty(message = "BIL10001")
    private String txType;

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "busType", value = "业务类型")
    private String busType;

    /**
     * 账单金额
     */
    @ApiModelProperty(name = "orderAmt", value = "账单金额")
    @NotNull(message = "BIL10012")
    @Min(value = 0, message = "BIL10002")
    private BigDecimal orderAmt;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "fee", value = "手续费")
    @Min(value = 0, message = "BIL10002")
    private BigDecimal fee;

    /**
     * 账单状态 'W'待支付,'W1'系统受理中,'P'支付待确认,'S,S1'成功,'F,F1,R2'失败,'R1'部分退款,'E'超时,'C'撤销,'W2'缴费中,'F2'缴费失败,'S2'缴费成功,'R0'申请退款,'R3'已退款
     */
    @ApiModelProperty(name = "orderStatus", value = "账单状态 'W'待支付,'W1'系统受理中,'P'支付待确认,'S,S1'成功,'F,F1,R2'失败,'R1'部分退款,'E'超时,'C'撤销,'W2'缴费中,'F2'缴费失败,'S2'缴费成功,'R0'申请退款,'R3'已退款")
    @NotEmpty(message = "BIL10003")
    private String orderStatus;

    /**
     * 收入支出标识
     */
    @ApiModelProperty(name = "incmPayFlag", value = "收入支出标识")
    private String incmPayFlag;

    /**
     * 手机号码
     */
    @ApiModelProperty(name = "mobileNo", value = "手机号码")
    private String mobileNo;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    @NotEmpty(message = "BIL10005")
    private String userId;

    /**
     * 商户编号
     */
    @ApiModelProperty(name = "mercId", value = "商户编号")
    private String mercId;

    /**
     * 现金账户余额
     */
    @ApiModelProperty(name = "cashBalAmt", value = "现金账户余额")
    private BigDecimal cashBalAmt;

    /**
     * 理财账户余额
     */
    @ApiModelProperty(name = "invBalAmt", value = "理财账户余额")
    private BigDecimal invBalAmt;

    /**
     * 优惠金额
     */
    @ApiModelProperty(name = "couponAmt", value = "优惠金额")
    private BigDecimal couponAmt;

    /**
     * 优惠类型 01.电子券,02.海币,03.折扣券
     */
    @ApiModelProperty(name = "couponType", value = "优惠类型 01.电子券,02.海币,03.折扣券")
    private String couponType;

    /**
     * 商户名称
     */
    @ApiModelProperty(name = "mercName", value = "商户名称")
    private String mercName;

    /**
     * 商品名称
     */
    @ApiModelProperty(name = "goodsInfo", value = "商品名称")
    private String goodsInfo;

    /**
     * 原订单号
     */
    @ApiModelProperty(name = "orgOrderNo", value = "原订单号")
    private String orgOrderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注（充值拒绝原因）")
    private String remark;

    /**
     * 支付方式
     */
    @ApiModelProperty(name = "payMod", value = "支付方式")
    private String payMod;

    /**
     * 订单渠道
     */
    @ApiModelProperty(name = "orderChannel", value = "订单渠道")
    private String orderChannel;

    /**
     * 汇款信息
     */
    @ApiModelProperty(name = "remitInfo", value = "汇款信息")
    private String remitInfo;

    /**
     * 摘要信息
     */
    @ApiModelProperty(name = "abstractInfo", value = "摘要信息")
    private String abstractInfo;

    /**
     * 账户金额
     */
    @ApiModelProperty(name = "actAmt", value = "账户金额")
    @Min(value = 0, message = "BIL10002")
    private BigDecimal actAmt;

    /**
     * 银行卡补款金额
     */
    @ApiModelProperty(name = "crdPayAmt", value = "银行卡补款金额")
    @Min(value = 0, message = "BIL10002")
    private BigDecimal crdPayAmt;

    /**
     * 补款类型
     */
    @ApiModelProperty(name = "crdPayType", value = "补款类型")
    private String crdPayType;

    /**
     * 交易币种
     */
    @ApiModelProperty(name = "ccy", value = "交易币种")
    private String ccy;

    /**
     * 账户号码
     */
    @ApiModelProperty(name = "acNo", value = "账户号码")
    private String acNo;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private LocalDateTime modifyTime;

    /**
     * 交易哈希
     */
    @ApiModelProperty(name = "txHash", value = "交易哈希")
    private String txHash;

    /**
     * gas
     */
    @ApiModelProperty(name = "gas", value = "gas")
    private BigDecimal gas;

    public BigDecimal getGas() {
        return gas;
    }

    public void setGas(BigDecimal gas) {
        this.gas = gas;
    }

    public String getTxHash() {
        return txHash;
    }

    public void setTxHash(String txHash) {
        this.txHash = txHash;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getIncmPayFlag() {
        return incmPayFlag;
    }

    public void setIncmPayFlag(String incmPayFlag) {
        this.incmPayFlag = incmPayFlag;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public BigDecimal getCashBalAmt() {
        return cashBalAmt;
    }

    public void setCashBalAmt(BigDecimal cashBalAmt) {
        this.cashBalAmt = cashBalAmt;
    }

    public BigDecimal getInvBalAmt() {
        return invBalAmt;
    }

    public void setInvBalAmt(BigDecimal invBalAmt) {
        this.invBalAmt = invBalAmt;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getGoodsInfo() {
        return goodsInfo;
    }

    public void setGoodsInfo(String goodsInfo) {
        this.goodsInfo = goodsInfo;
    }

    public String getOrgOrderNo() {
        return orgOrderNo;
    }

    public void setOrgOrderNo(String orgOrderNo) {
        this.orgOrderNo = orgOrderNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPayMod() {
        return payMod;
    }

    public void setPayMod(String payMod) {
        this.payMod = payMod;
    }

    public String getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(String orderChannel) {
        this.orderChannel = orderChannel;
    }

    public String getRemitInfo() {
        return remitInfo;
    }

    public void setRemitInfo(String remitInfo) {
        this.remitInfo = remitInfo;
    }

    public String getAbstractInfo() {
        return abstractInfo;
    }

    public void setAbstractInfo(String abstractInfo) {
        this.abstractInfo = abstractInfo;
    }

    public BigDecimal getActAmt() {
        return actAmt;
    }

    public void setActAmt(BigDecimal actAmt) {
        this.actAmt = actAmt;
    }

    public BigDecimal getCrdPayAmt() {
        return crdPayAmt;
    }

    public void setCrdPayAmt(BigDecimal crdPayAmt) {
        this.crdPayAmt = crdPayAmt;
    }

    public String getCrdPayType() {
        return crdPayType;
    }

    public void setCrdPayType(String crdPayType) {
        this.crdPayType = crdPayType;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}
