package com.hisun.lemon.bil.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2017/8/1
 * @time 19:58
 */
@ApiModel("商户账户信息返回参数")
public class MercAccountRspDTO {

    public MercAccountRspDTO(){
        cashBalAmt = BigDecimal.ZERO;
        uavaAmt = BigDecimal.ZERO;
        todayIncmAmt = BigDecimal.ZERO;
        unSettleAmt = BigDecimal.ZERO;
    }
    /**
     * 现金账户余额
     */
    @ApiModelProperty(name = "cashBalAmt", value = "现金账户金额")
    private BigDecimal cashBalAmt;
    /**
     * 现金账户不可用余额
     */
    @ApiModelProperty(name = "uavaAmt", value = "现金账户不可用金额")
    private BigDecimal uavaAmt;
    /**
     * 今日收入
     */
    @ApiModelProperty(name = "todayIncmAmt", value = "今日收入")
    private BigDecimal todayIncmAmt;
    /**
     * 待结算金额
     */
    @ApiModelProperty(name = "unSettleAmt", value = "待结算金额")
    private BigDecimal unSettleAmt;

    public BigDecimal getCashBalAmt() {
        return cashBalAmt;
    }

    public void setCashBalAmt(BigDecimal cashBalAmt) {
        this.cashBalAmt = cashBalAmt;
    }

    public BigDecimal getUavaAmt() {
        return uavaAmt;
    }

    public void setUavaAmt(BigDecimal uavaAmt) {
        this.uavaAmt = uavaAmt;
    }

    public BigDecimal getTodayIncmAmt() {
        return todayIncmAmt;
    }

    public void setTodayIncmAmt(BigDecimal todayIncmAmt) {
        this.todayIncmAmt = todayIncmAmt;
    }

    public BigDecimal getUnSettleAmt() {
        return unSettleAmt;
    }

    public void setUnSettleAmt(BigDecimal unSettleAmt) {
        this.unSettleAmt = unSettleAmt;
    }
}
