package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商户对账明细导出 传输对象
 * <AUTHOR>
 * @date 2017/7/31
 * @time 19:11
 */
@ClientValidated
@ApiModel(value = "MercChkDetailExportDTO", description = "商户对账明细导出")
public class MercChkDetailExportDTO extends GenericDTO<NoBody>{

    @ApiModelProperty(name = "beginDate", value = "开始日期",hidden = true)
    private LocalDate beginDate;


    @ApiModelProperty(name = "endDate", value = "结束日期",hidden = true)
    private LocalDate endDate;

    @ApiModelProperty(name = "beginTime", value = "开始日期",hidden = true)
    private LocalDateTime beginTime;


    @ApiModelProperty(name = "endTime", value = "结束日期",hidden = true)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "商户编号",hidden = true)
    private String mercId;

    @ApiModelProperty(name = "beginDateStr", value = "开始日期yyyyMMdd")
    @NotEmpty(message = "BIL10015")
    private String beginDateStr;


    @ApiModelProperty(name = "endDateStr", value = " 结束日期yyyyMMdd")
    @NotEmpty(message = "BIL10015")
    private String endDateStr;

    @ApiModelProperty(name = "type", value = "日期类型：'T'交易日志 'C'清分日期")
    @Length(max = 2)
    @NotBlank(message = "BIL10004")
    private String type;

    @ApiModelProperty(name = "language", value = "语言：'zh'中文 'en'英文 'km'柬埔寨文")
    private String language;

    public LocalDate getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDate beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDateTime getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(LocalDateTime beginTime) {
        this.beginTime = beginTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public String getBeginDateStr() {
        return beginDateStr;
    }

    public void setBeginDateStr(String beginDateStr) {
        this.beginDateStr = beginDateStr;
    }

    public String getEndDateStr() {
        return endDateStr;
    }

    public void setEndDateStr(String endDateStr) {
        this.endDateStr = endDateStr;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}
