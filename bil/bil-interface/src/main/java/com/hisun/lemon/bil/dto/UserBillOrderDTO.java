package com.hisun.lemon.bil.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 用户账单信息 传输对象
 * <AUTHOR>
 * @date 2017/7/12
 * @time 20:26
 */
@ClientValidated
@ApiModel("用户账单信息")
public class UserBillOrderDTO extends GenericDTO<NoBody>{

    /**
     * 交易类型（查询条件）
     */
	@ApiModelProperty(name = "txType", value = "交易类型(01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费)用','隔开")
    private String txType;
    /**
     * 交易类型List
     */
    @ApiModelProperty(hidden = true)
    private List<String> txTypeList;
    /**
     *  用户编号
     */
	@ApiModelProperty(name = "userId", value = "用户编号,取当前用户", hidden = true)
    @Length(max = 20)
    private String userId;
    /**
     *  用户编号内部用
     */
    @ApiModelProperty(name = "userNo", value = "用户编号,内部用", hidden = true)
    @Length(max = 20)
    private String userNo;
    /**
     * 交易起始（时间类型）
     */
	@ApiModelProperty(name = "txTmBeginStr", value = "交易起始（时间类型）yyyyMMddHHmmss")
    private String txTmBeginStr;
    @ApiModelProperty(hidden = true)
    private LocalDateTime txTmBegin;
    /**
     * 交易结束（时间类型）
     */
	@ApiModelProperty(name = "txTmEndStr", value = "交易结束（时间类型）yyyyMMddHHmmss")
    private String txTmEndStr;
    @ApiModelProperty(hidden = true)
    private LocalDateTime txTmEnd;
    /**
     * 单页记录数
     */
	@ApiModelProperty(name = "pageSize", value = "每页大小")
    @Min(value=1, message="BIL10008")
    @NotNull(message = "BIL10007")
    private Integer pageSize;
    /**
     * 页数
     */
	@ApiModelProperty(name = "pageNo", value = "页数")
    @Min(value=1, message="BIL10010")
    @NotNull(message = "BIL10009")
    private Integer pageNo;
    /**
     *  零钱相关标识
     */
    @ApiModelProperty(name = "actFlag", value = "零钱相关标识填1或不填")
    @Length(max = 1)
    private String actFlag;
    /**
     *  用户级别
     */
    @ApiModelProperty(name = "userLevel", value = "用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家")
    @Length(max = 1)
    @NotEmpty(message = "BIL10014")
    private String userLevel;

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public List<String> getTxTypeList() {
        return txTypeList;
    }

    public void setTxTypeList(List<String> txTypeList) {
        this.txTypeList = txTypeList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public LocalDateTime getTxTmBegin() {
        return txTmBegin;
    }

    public void setTxTmBegin(LocalDateTime txTmBegin) {
        this.txTmBegin = txTmBegin;
    }

    public LocalDateTime getTxTmEnd() {
        return txTmEnd;
    }

    public void setTxTmEnd(LocalDateTime txTmEnd) {
        this.txTmEnd = txTmEnd;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public String getActFlag() {
        return actFlag;
    }

    public void setActFlag(String actFlag) {
        this.actFlag = actFlag;
    }

    public String getTxTmBeginStr() {
        return txTmBeginStr;
    }

    public void setTxTmBeginStr(String txTmBeginStr) {
        this.txTmBeginStr = txTmBeginStr;
    }

    public String getTxTmEndStr() {
        return txTmEndStr;
    }

    public void setTxTmEndStr(String txTmEndStr) {
        this.txTmEndStr = txTmEndStr;
    }

    public String getUserLevel() {
        return userLevel;
    }

    public void setUserLevel(String userLevel) {
        this.userLevel = userLevel;
    }
}
