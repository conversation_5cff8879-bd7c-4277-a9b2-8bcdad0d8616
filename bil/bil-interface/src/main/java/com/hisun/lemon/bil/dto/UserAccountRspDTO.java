package com.hisun.lemon.bil.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2017/8/1
 * @time 20:19
 */
@ApiModel("用户账户信息")
public class UserAccountRspDTO {

    public UserAccountRspDTO(){
        cashBalAmt = BigDecimal.ZERO;
        invBalAmt = BigDecimal.ZERO;
        couponAmt = 0;
        cardCount = 0;
    }
    /**
     * 现金账户余额
     */
    @ApiModelProperty(name = "cashBalAmt", value = "现金账户金额")
    private BigDecimal cashBalAmt;
    /**
     * 理财账户余额
     */
    @ApiModelProperty(name = "invBalAmt", value = "理财账户金额")
    private BigDecimal invBalAmt;
    /**
     * 海币余额
     */
    @ApiModelProperty(name = "couponAmt", value = "海币金额")
    private Integer couponAmt;

    /**
     * 银行卡数量
     */
    @ApiModelProperty(name = "cardCount", value = "银行卡数量")
    private Integer cardCount;

    public BigDecimal getCashBalAmt() {
        return cashBalAmt;
    }

    public void setCashBalAmt(BigDecimal cashBalAmt) {
        this.cashBalAmt = cashBalAmt;
    }

    public BigDecimal getInvBalAmt() {
        return invBalAmt;
    }

    public void setInvBalAmt(BigDecimal invBalAmt) {
        this.invBalAmt = invBalAmt;
    }

    public Integer getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(Integer couponAmt) {
        this.couponAmt = couponAmt;
    }

    public Integer getCardCount() {
        return cardCount;
    }

    public void setCardCount(Integer cardCount) {
        this.cardCount = cardCount;
    }
}
