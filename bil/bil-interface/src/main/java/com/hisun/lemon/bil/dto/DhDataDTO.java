package com.hisun.lemon.bil.dto;

import java.math.BigDecimal;

/**
 * TODO 此处填写功能说明
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/5 15:20
 */
public class DhDataDTO {

    private String orderNo;

    private String fromCoin;

    private BigDecimal fromAmount;

    private String toCoin;

    private BigDecimal toAmount;

    public DhDataDTO() {
    }

    public DhDataDTO(String orderNo, String fromCoin, BigDecimal fromAmount, String toCoin, BigDecimal toAmount) {
        this.orderNo = orderNo;
        this.fromCoin = fromCoin;
        this.fromAmount = fromAmount;
        this.toCoin = toCoin;
        this.toAmount = toAmount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getFromCoin() {
        return fromCoin;
    }

    public void setFromCoin(String fromCoin) {
        this.fromCoin = fromCoin;
    }

    public BigDecimal getFromAmount() {
        return fromAmount;
    }

    public void setFromAmount(BigDecimal fromAmount) {
        this.fromAmount = fromAmount;
    }

    public String getToCoin() {
        return toCoin;
    }

    public void setToCoin(String toCoin) {
        this.toCoin = toCoin;
    }

    public BigDecimal getToAmount() {
        return toAmount;
    }

    public void setToAmount(BigDecimal toAmount) {
        this.toAmount = toAmount;
    }
}
