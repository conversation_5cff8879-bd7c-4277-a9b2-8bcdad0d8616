package com.hisun.lemon.bil.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/20
 * @time 20:16
 */
@ApiModel("商户账单信息")
public class MercUpBillOrderListDTO {

    private List<MercBillOrder> list;

    private long totalNum;

    private int currPage;



    public static class MercBillOrder{
        /**
         * @Fields orderNo 订单号
         */
        @ApiModelProperty(name = "orderNo", value = "账单编号")
        private String orderNo;
        /**
         * @Fields txTm 交易时间
         */
        @ApiModelProperty(name = "txTm", value = "交易时间")
        private LocalDateTime txTm;
        /**
         * @Fields txType 交易类型 01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费
         */
        @ApiModelProperty(name = "txType", value = "交易类型01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费")
        @NotEmpty(message = "BIL10001")
        private String txType;
        /**
         * @Fields orderAmt 订单金额
         */
        @ApiModelProperty(name = "orderAmt", value = "订单金额")
        @Min(value=0, message="BIL10002")
        private BigDecimal orderAmt;
        /**
         * @Fields orderStatus 'W'待支付、'P'支付待确认、'S'成功、'F'失败、'R1'部分退款、'R2'全部退款、'E'超时、'C'撤销
         */
        @ApiModelProperty(name = "orderStatus", value = "账单状态'W'待支付、'P'支付待确认、'S'成功、'F'失败、'R1'部分退款、'R2'全部退款、'E'超时、'C'撤销")
        @NotEmpty(message = "BIL10003")
        private String orderStatus;
        /**
         * @Fields mobileNo 手机号码
         */
        @ApiModelProperty(name = "mobileNo", value = "手机号码")
        private String mobileNo;
        /**
         * @Fields mercId 商户编号
         */
        @ApiModelProperty(name = "mercId", value = "商户编号")
        private String mercId;
        /**
         * @Fields mercName 商户名称
         */
        @ApiModelProperty(name = "mercName", value = "商户名称")
        private String mercName;
        /**
         * @Fields oprName 操作员
         */
        @ApiModelProperty(name = "oprName", value = "操作员")
        private String oprName;
        /**
         * @Fields userId 付款方
         */
        @ApiModelProperty(name = "userId", value = "付款方")
        private String userId;
        /**
         * @Fields goodsInfo 商品名称
         */
        @ApiModelProperty(name = "goodsInfo", value = "商品名称")
        private String goodsInfo;
        /**
         * @Fields busType 业务类型 '0201'条码支付、'0202'扫码支付、'0203'APP支付、'0204'POS支付、'0205'银行卡收单
         */
        @ApiModelProperty(name = "busType", value = "业务类型'0201'条码支付、'0202'扫码支付、'0203'APP支付、'0204'POS支付、'0205'银行卡收单" )
        private String busType;
        /**
         * @Fields payMod 支付方式 01现金账户、02银行卡、03微信支付、04支付宝
         */
        @ApiModelProperty(name = "payMod", value = "支付方式 01现金账户、02银行卡、03微信支付、04支付宝")
        private String payMod;
        /**
         * 现金账户金额
         */
        @ApiModelProperty(name = "cashBalAmt", value = "现金账户金额")
        private BigDecimal cashBalAmt;
        /**
         * 优惠金额
         */
        @ApiModelProperty(name = "couponAmt", value = "优惠金额")
        private BigDecimal couponAmt;
        /**
         * 优惠类型 01电子券 02海币 03折扣券
         */
        @ApiModelProperty(name = "couponType", value = "优惠类型 01电子券 02海币 03折扣券")
        private String couponType;
        /**
         * 已退款金额
         */
        @ApiModelProperty(name = "rfdAmt", value = "已退款金额")
        private BigDecimal rfdAmt;
        /**
         * 退款原因
         */
        @ApiModelProperty(name = "rfdReason", value = "退款原因")
        private String rfdReason;
        /**
         * 备注
         */
        @ApiModelProperty(name = "remark", value = "备注")
        private String remark;
        /**
         * 商户订单号
         */
        @ApiModelProperty(name = "busOrderNo", value = "商户订单号")
        private String busOrderNo;
        /**
         * 退款时间
         */
        @ApiModelProperty(name = "rfdOrdTm", value = "支付时间")
        private LocalDateTime rfdOrdTm;
        /**
         * 支付时间
         */
        @ApiModelProperty(name = "payOrdTm", value = "支付时间")
        private LocalDateTime payOrdTm;
        /**
         * 撤销时间
         */
        @ApiModelProperty(name = "undoOrdTm", value = "撤销时间")
        private LocalDateTime undoOrdTm;
        /**
         * 原订单号
         */
        @ApiModelProperty(name = "orgOrderNo", value = "原订单号")
        private String orgOrderNo;
        /**
         * @Fields rfdSeaCoin 已退海币
         */
        @ApiModelProperty(name = "rfdSeaCoin", value = "已退海币")
        private String rfdSeaCoin;
        /**
         * 手续费
         */
        @ApiModelProperty(name = "fee", value = "手续费")
        private BigDecimal fee;

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public LocalDateTime getTxTm() {
            return txTm;
        }

        public void setTxTm(LocalDateTime txTm) {
            this.txTm = txTm;
        }

        public String getTxType() {
            return txType;
        }

        public void setTxType(String txType) {
            this.txType = txType;
        }

        public BigDecimal getOrderAmt() {
            return orderAmt;
        }

        public void setOrderAmt(BigDecimal orderAmt) {
            this.orderAmt = orderAmt;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getMobileNo() {
            return mobileNo;
        }

        public void setMobileNo(String mobileNo) {
            this.mobileNo = mobileNo;
        }

        public String getMercId() {
            return mercId;
        }

        public void setMercId(String mercId) {
            this.mercId = mercId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getBusType() {
            return busType;
        }

        public void setBusType(String busType) {
            this.busType = busType;
        }

        public String getPayMod() {
            return payMod;
        }

        public void setPayMod(String payMod) {
            this.payMod = payMod;
        }

        public BigDecimal getCashBalAmt() {
            return cashBalAmt;
        }

        public void setCashBalAmt(BigDecimal cashBalAmt) {
            this.cashBalAmt = cashBalAmt;
        }

        public String getMercName() {
            return mercName;
        }

        public void setMercName(String mercName) {
            this.mercName = mercName;
        }

        public String getGoodsInfo() {
            return goodsInfo;
        }

        public void setGoodsInfo(String goodsInfo) {
            this.goodsInfo = goodsInfo;
        }

        public BigDecimal getCouponAmt() {
            return couponAmt;
        }

        public void setCouponAmt(BigDecimal couponAmt) {
            this.couponAmt = couponAmt;
        }

        public String getCouponType() {
            return couponType;
        }

        public void setCouponType(String couponType) {
            this.couponType = couponType;
        }

        public String getRfdReason() {
            return rfdReason;
        }

        public void setRfdReason(String rfdReason) {
            this.rfdReason = rfdReason;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public LocalDateTime getPayOrdTm() {
            return payOrdTm;
        }

        public void setPayOrdTm(LocalDateTime payOrdTm) {
            this.payOrdTm = payOrdTm;
        }

        public LocalDateTime getUndoOrdTm() {
            return undoOrdTm;
        }

        public void setUndoOrdTm(LocalDateTime undoOrdTm) {
            this.undoOrdTm = undoOrdTm;
        }

        public String getOrgOrderNo() {
            return orgOrderNo;
        }

        public void setOrgOrderNo(String orgOrderNo) {
            this.orgOrderNo = orgOrderNo;
        }

        public BigDecimal getRfdAmt() {
            return rfdAmt;
        }

        public void setRfdAmt(BigDecimal rfdAmt) {
            this.rfdAmt = rfdAmt;
        }

        public LocalDateTime getRfdOrdTm() {
            return rfdOrdTm;
        }

        public void setRfdOrdTm(LocalDateTime rfdOrdTm) {
            this.rfdOrdTm = rfdOrdTm;
        }

        public String getBusOrderNo() {
            return busOrderNo;
        }

        public void setBusOrderNo(String busOrderNo) {
            this.busOrderNo = busOrderNo;
        }

        public String getOprName() {
            return oprName;
        }

        public void setOprName(String oprName) {
            this.oprName = oprName;
        }

        public String getRfdSeaCoin() {
            return rfdSeaCoin;
        }

        public void setRfdSeaCoin(String rfdSeaCoin) {
            this.rfdSeaCoin = rfdSeaCoin;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }
    }

    public List<MercBillOrder> getList() {
        return list;
    }

    public void setList(List<MercBillOrder> list) {
        this.list = list;
    }

    public long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(long totalNum) {
        this.totalNum = totalNum;
    }

    public int getCurrPage() {
        return currPage;
    }

    public void setCurrPage(int currPage) {
        this.currPage = currPage;
    }

}
