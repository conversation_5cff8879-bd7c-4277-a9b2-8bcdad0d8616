package com.hisun.lemon.bil.dto;

import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 更新订单状态 传输对象
 * <AUTHOR>
 * @date 2017/7/26
 * @time 19:56
 */
public class UpdateUserBillDTO {
    /**
     * 账单编号
     */
    @NotEmpty(message = "BIL10004")
    private String orderNo;
    /**
     * 账单状态 'W'待支付、'P'支付待确认、'S'成功、'F'失败、'R1'部分退款、'R2'全部退款、'E'超时、'C'撤销、'W2'缴费中
     * 'S2'缴费成功、'F2'缴费失败
     */
    @NotEmpty(message = "BIL10003")
    private String orderStatus;
    /**
     * 付款方Id
     */
    private String payerId;
    /**
     * 收款方Id
     */
    private String payeeId;
    /**
     * 账单金额
     */
    @Min(value=0, message="BIL10002")
    private BigDecimal orderAmt;
    /**
     * 已退款金额
     */
    @Min(value=0, message="BIL10002")
    private BigDecimal rfdAmt;
    /**
     * 优惠金额
     */
    @Min(value=0, message="BIL10002")
    private BigDecimal couponAmt;
    /**
     * 优惠类型
     */
    private String couponType;
    /**
     * 银行卡补款金额
     */
    private BigDecimal crdPayAmt;
    /**
     * 补款类型 0无补款 1网银 2快捷
     */
    private String crdPayType;
    /**
     * 退款时间
     */
    private LocalDateTime rfdOrdTm;
    /**
     * 付款时间
     */
    private LocalDateTime payOrdTm;
    /**
     * 撤销时间
     */
    private LocalDateTime undoOrdTm;
    /**
     * 退款原因
     */
    private String rfdReason;
    /**
     * @Fields goodsInfo 商品名称
     */
    private String goodsInfo;
    /**
     * 支付方式
     */
    private String payMod;
    /**
     * 资金机构
     */
    private String capCorg;
    /**
     * 备注（充值拒绝原因）
     */
    private String remark;
    /**
     * 原订单号，退款用
     */
    private String orgOrderNo;
    /**
     * 原订单状态，退款用
     */
    private String orgOrderStatus;
    /**
     * 拓展字段：用于国际化
     */
    private String extInfo;
    /**
     * 操作员
     */
    private String oprName;
    /**
     * 汇款信息
     */
    private String remitInfo;
    /**
     * 现金账户金额
     */
    private BigDecimal balAmt;
    /**
     * 业务类型
     */
    private String busType;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(String payeeId) {
        this.payeeId = payeeId;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public BigDecimal getRfdAmt() {
        return rfdAmt;
    }

    public void setRfdAmt(BigDecimal rfdAmt) {
        this.rfdAmt = rfdAmt;
    }

    public LocalDateTime getRfdOrdTm() {
        return rfdOrdTm;
    }

    public void setRfdOrdTm(LocalDateTime rfdOrdTm) {
        this.rfdOrdTm = rfdOrdTm;
    }

    public LocalDateTime getPayOrdTm() {
        return payOrdTm;
    }

    public void setPayOrdTm(LocalDateTime payOrdTm) {
        this.payOrdTm = payOrdTm;
    }

    public LocalDateTime getUndoOrdTm() {
        return undoOrdTm;
    }

    public void setUndoOrdTm(LocalDateTime undoOrdTm) {
        this.undoOrdTm = undoOrdTm;
    }

    public String getRfdReason() {
        return rfdReason;
    }

    public void setRfdReason(String rfdReason) {
        this.rfdReason = rfdReason;
    }

    public String getOrgOrderNo() {
        return orgOrderNo;
    }

    public void setOrgOrderNo(String orgOrderNo) {
        this.orgOrderNo = orgOrderNo;
    }

    public String getOrgOrderStatus() {
        return orgOrderStatus;
    }

    public void setOrgOrderStatus(String orgOrderStatus) {
        this.orgOrderStatus = orgOrderStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public BigDecimal getCrdPayAmt() {
        return crdPayAmt;
    }

    public void setCrdPayAmt(BigDecimal crdPayAmt) {
        this.crdPayAmt = crdPayAmt;
    }

    public String getCrdPayType() {
        return crdPayType;
    }

    public void setCrdPayType(String crdPayType) {
        this.crdPayType = crdPayType;
    }

    public String getGoodsInfo() {
        return goodsInfo;
    }

    public void setGoodsInfo(String goodsInfo) {
        this.goodsInfo = goodsInfo;
    }

    public String getPayMod() {
        return payMod;
    }

    public void setPayMod(String payMod) {
        this.payMod = payMod;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getOprName() {
        return oprName;
    }

    public void setOprName(String oprName) {
        this.oprName = oprName;
    }

    public String getRemitInfo() {
        return remitInfo;
    }

    public void setRemitInfo(String remitInfo) {
        this.remitInfo = remitInfo;
    }

    public BigDecimal getBalAmt() {
        return balAmt;
    }

    public void setBalAmt(BigDecimal balAmt) {
        this.balAmt = balAmt;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    @Override
    public String toString() {
        return "UpdateUserBillDTO{" +
                "orderNo='" + orderNo + '\'' +
                ", orderStatus='" + orderStatus + '\'' +
                ", payerId='" + payerId + '\'' +
                ", payeeId='" + payeeId + '\'' +
                ", orderAmt=" + orderAmt +
                ", rfdAmt=" + rfdAmt +
                ", couponAmt=" + couponAmt +
                ", couponType='" + couponType + '\'' +
                ", crdPayAmt=" + crdPayAmt +
                ", crdPayType='" + crdPayType + '\'' +
                ", rfdOrdTm=" + rfdOrdTm +
                ", payOrdTm=" + payOrdTm +
                ", undoOrdTm=" + undoOrdTm +
                ", rfdReason='" + rfdReason + '\'' +
                ", goodsInfo='" + goodsInfo + '\'' +
                ", payMod='" + payMod + '\'' +
                ", capCorg='" + capCorg + '\'' +
                ", remark='" + remark + '\'' +
                ", orgOrderNo='" + orgOrderNo + '\'' +
                ", orgOrderStatus='" + orgOrderStatus + '\'' +
                ", extInfo='" + extInfo + '\'' +
                ", oprName='" + oprName + '\'' +
                ", remitInfo='" + remitInfo + '\'' +
                ", balAmt='" + balAmt + '\'' +
                ", busType='" + busType + '\'' +
                '}';
    }
}
