package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 业务核心指标 传输对象
 * <AUTHOR>
 * @date 2017/7/12
 * @time 18:03
 */
@ClientValidated
@ApiModel("业务核心指标")
public class BusinessIndicatorsRspDTO {

    @ApiModelProperty("交易总额")
    private BigDecimal amt;

    @ApiModelProperty("交易笔数")
    private Integer num;

    @ApiModelProperty("交易成功率")
    private BigDecimal successRate;

    @ApiModelProperty("交易总额与昨日相比")
    private String amtCompareYesterday;

    @ApiModelProperty("交易笔数与昨日相比")
    private String numCompareYesterday;

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public BigDecimal getSuccessRate() {
        return successRate;
    }

    public void setSuccessRate(BigDecimal successRate) {
        this.successRate = successRate;
    }

    public String getAmtCompareYesterday() {
        return amtCompareYesterday;
    }

    public void setAmtCompareYesterday(String amtCompareYesterday) {
        this.amtCompareYesterday = amtCompareYesterday;
    }

    public String getNumCompareYesterday() {
        return numCompareYesterday;
    }

    public void setNumCompareYesterday(String numCompareYesterday) {
        this.numCompareYesterday = numCompareYesterday;
    }
}
