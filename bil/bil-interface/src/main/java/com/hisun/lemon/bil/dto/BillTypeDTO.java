package com.hisun.lemon.bil.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账单统计数据返回参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/5 16:30
 */
public class BillTypeDTO {

    private String orderNo;

    private LocalDateTime txTm;

    private String txType;

    private String busType;

    private BigDecimal orderAmt;

    private BigDecimal orderAmtFee;

    private String ccy;

    private BigDecimal gas;

    private BigDecimal fee;

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public BigDecimal getOrderAmtFee() {
        return orderAmtFee;
    }

    public void setOrderAmtFee(BigDecimal orderAmtFee) {
        this.orderAmtFee = orderAmtFee;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getGas() {
        return gas;
    }

    public void setGas(BigDecimal gas) {
        this.gas = gas;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }
}
