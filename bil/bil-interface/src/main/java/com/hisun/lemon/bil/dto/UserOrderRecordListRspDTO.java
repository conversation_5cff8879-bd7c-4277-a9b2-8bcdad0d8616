package com.hisun.lemon.bil.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class UserOrderRecordListRspDTO {

    private List<UserOrderRecord> list;

    private Long totalNumber;

    public static class UserOrderRecord {

        @ApiModelProperty(name = "txTm", value = "交易时间")
        @NotEmpty(message = "BIL10011")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime txTm;

        @ApiModelProperty(name = "ccy", value = "交易币种")
        private String ccy;

        @ApiModelProperty(name = "orderAmt", value = "账单金额")
        @NotNull(message = "BIL10012")
        @Min(value=0, message="BIL10002")
        private BigDecimal orderAmt;

        @ApiModelProperty(name = "fee", value = "手续费")
        private BigDecimal fee;

        @ApiModelProperty(name = "userId", value = "用户编号")
        @NotEmpty(message = "BIL10005")
        private String userId;

        @ApiModelProperty(name = "mercId", value = "商户编号")
        private String mercId;

        @ApiModelProperty(name = "orderNo", value = "账单编号")
        @NotEmpty(message = "BIL10004")
        private String orderNo;

        @ApiModelProperty(name = "orderHash", value = "交易哈希")
        private String orderHash;

        @ApiModelProperty(name = "orderStatus", value = "账单状态 'W'待支付、'W1'系统受理中、'P'支付待确认、'S,S1'成功、" +
                "'F,F1,R2'失败、'R1'部分退款、'E'超时、'C'撤销、'W2'缴费中、'F2'缴费失败、'S2'缴费成功、'R0'申请退款、'R3'已退款")
        @NotEmpty(message = "BIL10003")
        private String orderStatus;

        @ApiModelProperty(name = "txType", value = "交易类型01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费")
        @NotEmpty(message = "BIL10001")
        private String txType;

        @ApiModelProperty(name = "remark", value = "备注")
        private String remark;

        @ApiModelProperty(name = "orderChannel", value = "订单渠道")
        private String orderChannel;

        @ApiModelProperty(name = "acNo", value = "付款方（账号）")
        private String acNo;

        @ApiModelProperty(name = "toAcNo", value = "收款方（账号）")
        private String toAcNo;

        @ApiModelProperty(name = "txHash", value = "交易哈希")
        private String txHash;

        public LocalDateTime getTxTm() {
            return txTm;
        }

        public void setTxTm(LocalDateTime txTm) {
            this.txTm = txTm;
        }

        public String getCcy() {
            return ccy;
        }

        public void setCcy(String ccy) {
            this.ccy = ccy;
        }

        public BigDecimal getOrderAmt() {
            return orderAmt;
        }

        public void setOrderAmt(BigDecimal orderAmt) {
            this.orderAmt = orderAmt;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getMercId() {
            return mercId;
        }

        public void setMercId(String mercId) {
            this.mercId = mercId;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getOrderHash() {
            return orderHash;
        }

        public void setOrderHash(String orderHash) {
            this.orderHash = orderHash;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getTxType() {
            return txType;
        }

        public void setTxType(String txType) {
            this.txType = txType;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getOrderChannel() {
            return orderChannel;
        }

        public void setOrderChannel(String orderChannel) {
            this.orderChannel = orderChannel;
        }

        public String getAcNo() {
            return acNo;
        }

        public void setAcNo(String acNo) {
            this.acNo = acNo;
        }

        public String getToAcNo() {
            return toAcNo;
        }

        public void setToAcNo(String toAcNo) {
            this.toAcNo = toAcNo;
        }

        public String getTxHash() {
            return txHash;
        }

        public void setTxHash(String txHash) {
            this.txHash = txHash;
        }
    }

    public List<UserOrderRecord> getList() {
        return list;
    }

    public void setList(List<UserOrderRecord> list) {
        this.list = list;
    }

    public Long getTotalNumber() {
        return totalNumber;
    }

    public void setTotalNumber(Long totalNumber) {
        this.totalNumber = totalNumber;
    }
}