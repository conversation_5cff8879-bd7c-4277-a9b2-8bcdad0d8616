package com.hisun.lemon.bil.client;

import com.hisun.lemon.bil.dto.MercBillInfoDTO;
import com.hisun.lemon.bil.dto.MercBillOrderDTO;
import com.hisun.lemon.bil.dto.MercBillOrderListDTO;
import com.hisun.lemon.framework.annotation.LemonBody;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;


/**
 * 商户账户订单交易详情查询
 * <AUTHOR>
 * @date 2018年01月10日
 * @time 下午5:47:24
 *
 */
@FeignClient("BIL")
public interface MercBillQueryClient {
    /**
     * 商户查询账单
     * @param
     * @return
     */
    @GetMapping(value = "/bil/merc/bill")
    public GenericRspDTO<MercBillOrderListDTO> queryMercDetails(@Validated MercBillInfoDTO mercBillInfoDTO);

    /**
     * 查询商户交易记录
     * @param mercBillOrderDTO
     * @return
     */
    @GetMapping(value="/bil/merc/bill/list")
    public GenericRspDTO<MercBillOrderListDTO> queryMercBillsList(@Validated MercBillOrderDTO mercBillOrderDTO);


}
