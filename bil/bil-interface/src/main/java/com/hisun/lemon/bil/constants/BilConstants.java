package com.hisun.lemon.bil.constants;

public class BilConstants {

	public static final String SYNC_CREATE_BEAN="createBillMessageHandler";
	public static final String SYNC_UPDATE_BEAN="updateBillMessageHandler";
	public static final String SYNC_UPDATE_MERC_BEAN="updateMercBillMessageHandler";

	/**
	 * 生成数币订单号的前缀
	 */
	public static final String ORD_DM_PRE="dmOrderNo";
	/**
	 * 交易类型：充值
	 */
	public static final String TX_TYPE_RECHANGE="01";
	/**
	 * 交易类型：消费
	 */
	public static final String TX_TYPE_CONSUME="02";
	/**
	 * 交易类型：转账
	 */
	public static final String TX_TYPE_TRANSFER="03";
	/**
	 * 交易类型：提现
	 */
	public static final String TX_TYPE_WITHDRAW="04";
	/**
	 * 业务类型：在线充值
	 */
	public static final String BUS_TYPE_WITHDRAW_PERSONAL="0401";
	/**
	 * 交易类型：充海币
	 */
	public static final String TX_TYPE_RECHANGESEACOIN="05";
	/**
	 * 交易类型：退款
	 */
	public static final String TX_TYPE_RERUND="06";
	/**
	 * 交易类型：理财
	 */
	public static final String TX_TYPE_INV="07";
	/**
	 * 交易类型：缴费
	 */
	public static final String TX_TYPE_PAY="08";
	/**
	 * 交易类型：撤销
	 */
	public static final String TX_TYPE_UNDO="09";
	/**
	 * 交易类型：数币收款
	 */
	public static final String TX_TYPE_DM_RECEIVE = "DS";

	/**
	 * 交易类型：数币充值
	 */
	public static final String TX_TYPE_DM_RECHARGE = "DC";

	/**
	 * 交易类型：数币兑换
	 */
	public static final String TX_TYPE_DM_EXCHANGE = "DH";

	/**
	 * 交易类型：数币转账
	 */
	public static final String TX_TYPE_DM_TRANSFER = "DZ";

	/**
	 * 交易类型：数币提现
	 */
	public static final String TX_TYPE_DM_WITHDRAW = "DX";
	/**
	 * 业务类型：在线充值
	 */
	public static final String BUS_TYPE_RECHARGE_ONLINE="0101";
	/**
	 * 业务类型：线下汇款
	 */
	public static final String BUS_TYPE_RECHARGE_OFFLINE="0102";
    /**
     * 业务类型：扫码
     */
    public static final String BUS_TYPE_CONSUME_CODE="0202";
    /**
     * 业务类型：条码
     */
    public static final String BUS_TYPE_CONSUME_BARCODE="0201";
	/**
	 * 优惠类型：电子券
	 */
	public static final String COUPON_TYPE_ELEC_TICKET="01";
	/**
	 * 优惠类型：海币
	 */
	public static final String COUPON_TYPE_SEA_COIN="02";
	/**
	 * 优惠类型：折扣券
	 */
	public static final String COUPON_TYPE_DISC_COUPIN="03";
	/**
	 * 待支付
	 */
	public static final String ORD_STS_W="W";
	/**
	 * 系统受理中
	 */
	public static final String ORD_STS_W1="W1";
	/**
	 * 支付待确认
	 */
	public static final String ORD_STS_P="P";
	/**
	 * 支付成功
	 */
	public static final String ORD_STS_S="S";
	/**
	 * 提现成功
	 */
	public static final String ORD_STS_S1="S1";
	/**
	 * 缴费成功
	 */
	public static final String ORD_STS_S2="S2";
	/**
	 * 失败
	 */
	public static final String ORD_STS_F="F";
	/**
	 * 缴费失败
	 */
	public static final String ORD_STS_F2="F2";
	/**
	 * 超时
	 */
	public static final String ORD_STS_EP="E";
	/**
	 * 申请退款
	 */
	public static final String ORD_STS_R0="R0";
	/**
	 * 部分退款
	 */
	public static final String ORD_STS_R1="R1";
	/**
	 * 全部退款
	 */
	public static final String ORD_STS_R2="R2";
	/**
	 * 已退款
	 */
	public static final String ORD_STS_R3="R3";
	/**
	 * 撤销
	 */
	public static final String ORD_STS_C="C";
	/**
	 * 缴费中
	 */
	public static final String ORD_STS_W2="W2";
	/**
	 * 零钱相关标识
	 */
	public static final String ACT_FLAG="1";
	/**
	 * 补款类型：无补款
	 */
	public static final String CRD_TYPE_NONE="0";
	/**
	 * 补款类型：网银
	 */
	public static final String CRD_TYPE_NB="1";
	/**
	 * 补款类型：快捷
	 */
	public static final String CRD_TYPE_QP="2";
	/**
	 * 支付方式：Mpay账户
	 */
	public static final String PAY_MPAY="1";
	/**
	 * 支付方式：微信用户扫码
	 */
	public static final String PAY_WECHAT_USER="2";
	/**
	 * 支付方式：微信商户扫码
	 */
	public static final String PAY_WECHAT_MERC="3";
	/**
	 * 支付方式：支付宝用户扫码
	 */
	public static final String PAY_ALI_USER="4";
	/**
	 * 支付方式：支付宝商户扫码
	 */
	public static final String PAY_ALI_MERC="5";
	/**
	 * 支付方式：翼支付用户扫码
	 */
	public static final String PAY_BEST_USER="6";
	/**
	 * 支付方式：翼支付商户扫码
	 */
	public static final String PAY_BEST_MERC="7";
    /**
     * 支付方式：Mpay
     */
    public static final String PAY_M="Seatelpay";
    /**
     * 支付方式：微信
     */
    public static final String PAY_WECHAT="WeChat";
    /**
     * 支付方式：支付宝
     */
    public static final String PAY_ALI="Alipay";
    /**
     * 支付方式：翼支付
     */
    public static final String PAY_BEST="BESTPAY";
}
