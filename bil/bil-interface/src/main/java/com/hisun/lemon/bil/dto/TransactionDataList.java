package com.hisun.lemon.bil.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/5 19:34
 */
@ApiModel("交易数据列表")
public class TransactionDataList {

    @ApiModelProperty("入账金额")
    private BigDecimal inAmt;

    @ApiModelProperty("出账金额")
    private BigDecimal outAmt;

    @ApiModelProperty("交易类型 DS收款,DC充值,DH兑换,DZ转账,DX提现,SXF手续费")
    private String txType;

    @ApiModelProperty("币种")
    private String ccy;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("交易日期")
    private LocalDate trDate;

    public TransactionDataList() {
    }

    public TransactionDataList(BigDecimal inAmt, BigDecimal outAmt, String txType, String ccy, LocalDate trDate) {
        this.inAmt = inAmt;
        this.outAmt = outAmt;
        this.txType = txType;
        this.ccy = ccy;
        this.trDate = trDate;
    }

    public BigDecimal getInAmt() {
        return inAmt;
    }

    public void setInAmt(BigDecimal inAmt) {
        this.inAmt = inAmt;
    }

    public BigDecimal getOutAmt() {
        return outAmt;
    }

    public void setOutAmt(BigDecimal outAmt) {
        this.outAmt = outAmt;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public LocalDate getTrDate() {
        return trDate;
    }

    public void setTrDate(LocalDate trDate) {
        this.trDate = trDate;
    }
}
