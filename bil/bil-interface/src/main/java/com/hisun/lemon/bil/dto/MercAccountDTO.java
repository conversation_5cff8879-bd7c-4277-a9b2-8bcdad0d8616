package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 商户账户信息 传输对象
 * <AUTHOR>
 * @date 2017/7/31
 * @time 19:11
 */
@ClientValidated
@ApiModel("商户账户信息")
public class MercAccountDTO extends GenericDTO<NoBody>{
    /**
     *  商户编号
     */
    @ApiModelProperty(name = "mercId", value = "商户编号")
    @Length(max = 20)
    private String mercId;

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }
}
