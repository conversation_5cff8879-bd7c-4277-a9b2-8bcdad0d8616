package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 用户账单交易明细 传输对象
 * <AUTHOR>
 * @date 2017/7/31
 * @time 19:11
 */
@ClientValidated
@ApiModel(value = "UserBillInfoDTO", description = "用户账单交易明细")
public class UserBillInfoDTO extends GenericDTO<NoBody>{
    /**
     * 账单编号
     */
    @ApiModelProperty(name = "orderNo", value = "账单编号")
    @Length(max = 28)
    @NotEmpty(message = "BIL10004")
    private String orderNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
