package com.hisun.lemon.bil.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户账单清分信息 传输对象
 * <AUTHOR>
 * @date 2017/7/12
 * @time 18:03
 */
@ClientValidated
@ApiModel("商户账单清分信息")
public class MercCsmInfoDTO extends GenericDTO<NoBody>{

    /**
     * 起始日期
     */
    @ApiModelProperty(name = "beginDateStr", value = "起始日期yyyyMMdd")
    private String beginDateStr;
    /**
     * 结束日期
     */
    @ApiModelProperty(name = "endDateStr", value = "结束日期yyyyMMdd")
    private String endDateStr;
    /**
     * 起始日期
     */
	@ApiModelProperty(name = "beginDate", value = "起始日期", hidden = true)
    private LocalDate beginDate;
    /**
     * 结束日期
     */
	@ApiModelProperty(name = "endDate", value = "结束日期", hidden = true)
    private LocalDate endDate;
    /**
     * 起始时间
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(hidden = true)
    private LocalDateTime endTime;
    /**
     * @Fields dateType 日期类型
     */
	@ApiModelProperty(name = "dateType", value = "日期类型 'T'交易日期，'C'清分日期")
    private String dateType;
    /**
     * 商户编号
     */
    @ApiModelProperty(name = "mercId", value = "商户编号")
    @NotNull(message = "BIL10006")
    private String mercId;
    /**
     * 单页记录数
     */
	@ApiModelProperty(name = "pageSize", value = "每页大小")
    @NotNull(message = "BIL10007")
    @Min(value=1, message="BIL10008")
    private Integer pageSize;
    /**
     * 页数
     */
	@ApiModelProperty(name = "pageNo", value = " 页数")
    @NotNull(message = "BIL10009")
    @Min(value=1, message="BIL10010")
    private Integer pageNo;

    public String getBeginDateStr() {
        return beginDateStr;
    }

    public void setBeginDateStr(String beginDateStr) {
        this.beginDateStr = beginDateStr;
    }

    public String getEndDateStr() {
        return endDateStr;
    }

    public void setEndDateStr(String endDateStr) {
        this.endDateStr = endDateStr;
    }

    public LocalDate getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDate beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDateTime getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(LocalDateTime beginTime) {
        this.beginTime = beginTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getDateType() {
        return dateType;
    }

    public void setDateType(String dateType) {
        this.dateType = dateType;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }
}
