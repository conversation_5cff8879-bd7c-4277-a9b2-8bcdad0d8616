package com.hisun.lemon.bil.client;

import com.hisun.lemon.bil.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 用户账单查询接口Client
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 19:49
 */
@FeignClient(value = "BIL", path = "/bil/user")
public interface UserBillQueryClient {

    /**
     * 获取用户数币账户收款/充值记录列表
     *
     * @param req
     * @return
     */
    @PostMapping("/dm/receipts/list")
    GenericRspDTO<BilDmAccountReceiptsRspDTO> getDmAccountReceiptsList(@Validated @RequestBody BilDmAccountReceiptsReqDTO req);

    /**
     * 根据账单编号查询用户账单
     *
     * @param orderNo 账单编号
     * @return
     */
    @GetMapping(value = "/{order_no}")
    GenericRspDTO<UserBilOrderRspDTO> getBilInfoByOrderNo(@Validated @NotBlank @PathVariable("order_no") String orderNo);

    /**
     * 更新订单
     *
     * @param genericDTO
     * @return
     */
    @PostMapping("/update/order")
    GenericRspDTO<NoBody> updateOrder(@Validated @RequestBody GenericDTO<UpdateOrderReqDTO> genericDTO);

    /**
     * 新增订单
     *
     * @param genericDTO
     * @return
     */
    @PostMapping("/add/order")
    GenericRspDTO<NoBody> addOrder(@Validated @RequestBody GenericDTO<AddOrderReqDTO> genericDTO);
}
