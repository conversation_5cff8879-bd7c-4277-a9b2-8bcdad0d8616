package com.hisun.lemon.bil.dto;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 合作机构资金列表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/6 17:38
 */
public class AgcyInfoListRspDTO {

    @ApiModelProperty(value = "合作机构名称")
    private String corpOrgNm;

    @ApiModelProperty(value = "账户数量")
    private String count;

    @ApiModelProperty(value = "账户总额")
    private BigDecimal amount;

    @ApiModelProperty(value = "可用余额")
    private BigDecimal acAmount;

    @ApiModelProperty(value = "冻结余额")
    private BigDecimal unAmount;

    public String getCorpOrgNm() {
        return corpOrgNm;
    }

    public void setCorpOrgNm(String corpOrgNm) {
        this.corpOrgNm = corpOrgNm;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAcAmount() {
        return acAmount;
    }

    public void setAcAmount(BigDecimal acAmount) {
        this.acAmount = acAmount;
    }

    public BigDecimal getUnAmount() {
        return unAmount;
    }

    public void setUnAmount(BigDecimal unAmount) {
        this.unAmount = unAmount;
    }

    @Override
    public String toString() {
        return "AgcyInfoListRspDTO{" +
                "corpOrgNm='" + corpOrgNm + '\'' +
                ", count='" + count + '\'' +
                ", amount=" + amount +
                ", acAmount=" + acAmount +
                ", unAmount=" + unAmount +
                '}';
    }
}
