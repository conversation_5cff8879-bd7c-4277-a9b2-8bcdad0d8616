 

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for bil_user_order
-- ----------------------------
DROP TABLE IF EXISTS `bil_user_order`;
CREATE TABLE `bil_user_order` (
  `order_no` varchar(28) NOT NULL COMMENT '账单编号',
  `tx_tm` datetime NOT NULL COMMENT '交易时间',
  `tx_type` varchar(2) NOT NULL COMMENT '交易类型 01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费',
  `bus_type` varchar(4) NOT NULL COMMENT '业务类型',
  `order_amt` decimal(13,2) NOT NULL COMMENT '订单金额',
  `order_status` char(2) NOT NULL COMMENT '账单状态 ''W''待支付、''W1''系统受理中、''P''支付待确认、''S,S1''成功、''F,R2''失败、''R1''部分退款、''E''超时、''C''撤销、''W2''缴费中、''F2''缴费失败、''S2''缴费成功',
  `incm_pay_flag` char(1) DEFAULT '' COMMENT '如理财只是现金账户和理财账户互转则无需+-标识',
  `mobile_no` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `user_id` varchar(20) DEFAULT NULL COMMENT '用户编号',
  `merc_id` varchar(20) DEFAULT '' COMMENT '商户编号',
  `merc_name` varchar(30) DEFAULT '' COMMENT '商户名称',
  `goods_info` varchar(50) DEFAULT '' COMMENT '商品名称',
  `cash_bal_amt` decimal(13,2) DEFAULT '0.00' COMMENT '现金账户余额',
  `inv_bal_amt` decimal(13,2) DEFAULT '0.00' COMMENT '理财账户余额',
  `coupon_amt` decimal(13,2) DEFAULT '0.00' COMMENT '海币账户余额',
  `coupon_type` varchar(2) DEFAULT '' COMMENT '优惠类型 01海币 02优惠券',
  `act_amt` decimal(13,2) DEFAULT '0.00' COMMENT '账户金额',
  `fee` decimal(11,2) DEFAULT '0.00' COMMENT '手续费',
  `crd_pay_amt` decimal(13,2) DEFAULT '0.00' COMMENT '银行卡补款金额',
  `crd_pay_type` varchar(2) DEFAULT '' COMMENT '补款类型 0无补款 1网银 2快捷',
  `pay_mod` varchar(20) DEFAULT '' COMMENT '支付方式',
  `order_channel` varchar(5) DEFAULT NULL COMMENT '订单渠道',
  `org_order_no` varchar(28) DEFAULT '' COMMENT '原订单号',
  `remark` varchar(200) DEFAULT '' COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `bil_merc_order`;
CREATE TABLE `bil_merc_order` (
  `order_no` varchar(28) NOT NULL COMMENT '订单号',
  `tx_tm` datetime NOT NULL COMMENT '交易时间',
  `tx_type` varchar(2) NOT NULL DEFAULT '' COMMENT '交易类型 01.充值,02.消费,03.转账,04.提现,05.充海币,06.退款,07.理财,08.缴费',
  `order_amt` decimal(13,2) NOT NULL COMMENT '订单金额',
  `order_status` char(2) NOT NULL COMMENT '订单状态 ''W''待支付、''W1''系统受理中、''P''支付待确认、''S,S1''成功、''F,R2''失败、''R1''部分退款、''E''超时、''C''撤销、''W2''缴费中、''F2''缴费失败、''S2''缴费成功',
  `mobile_no` varchar(20) DEFAULT '' COMMENT '支付手机号',
  `merc_id` varchar(20) DEFAULT '' COMMENT '商户编号',
  `merc_name` varchar(30) DEFAULT '' COMMENT '商户名称',
  `user_id` varchar(20) DEFAULT '' COMMENT '付款方',
  `goods_info` varchar(50) DEFAULT '' COMMENT '商品名称',
  `bus_type` varchar(4) DEFAULT NULL COMMENT '业务类型 ''0201''条码支付、''0202''扫码支付、''0203''APP支付、''0204''POS支付、''0205''银行卡收单',
  `pay_mod` varchar(20) DEFAULT '' COMMENT '支付方式 01现金账户、02银行卡、03微信支付、04支付宝',
  `cash_bal_amt` decimal(13,2) DEFAULT '0.00' COMMENT '现金账户金额',
  `coupon_amt` decimal(13,2) DEFAULT '0.00' COMMENT '优惠金额',
  `coupon_type` varchar(2) DEFAULT NULL COMMENT '优惠类型 01海币 02优惠券',
  `crd_pay_amt` decimal(13,2) DEFAULT '0.00' COMMENT '银行卡补款金额',
  `fee` decimal(11,2) DEFAULT '0.00' COMMENT '手续费',
  `rfd_amt` decimal(13,2) DEFAULT '0.00' COMMENT '已退款金额',
  `crd_pay_type` varchar(2) DEFAULT '' COMMENT '补款类型 0无补款 1网银 2快捷',
  `org_order_no` varchar(28) DEFAULT '' COMMENT '原订单号',
  `rfd_reason` varchar(200) DEFAULT '' COMMENT '退款原因',
  `remark` varchar(200) DEFAULT '' COMMENT '备注',
  `bus_order_no` varchar(28) DEFAULT '' COMMENT '商户订单号',
  `rfd_ord_tm` datetime DEFAULT NULL COMMENT '退款时间',
  `pay_ord_tm` datetime DEFAULT NULL COMMENT '支付时间',
  `undo_ord_tm` datetime DEFAULT NULL COMMENT '撤销时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10001','zh','交易类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10002','zh','订单金额不小于0!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10003','zh','账单状态不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10004','zh','账单编号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10005','zh','用户编号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10006','zh','商户编号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10007','zh','单页记录数不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10008','zh','单页记录数不小于1!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10009','zh','页数不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10010','zh','页数不小于1!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10011','zh','交易时间不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10012','zh','订单金额不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10013','zh','业务类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10014','zh','用户级别不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL10015','zh','查询时间不能为空!',now(),now());


insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20001','zh','用户账单信息不存在!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20002','zh','账单交易明细不存在!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20003','zh','用户账户信息查询失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20004','zh','账单信息不存在!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20005','zh','订单交易详情不存在!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20006','zh','商户账户信息查询失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20007','zh','生成用户订单不唯一!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20008','zh','账单已存在!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20009','zh','更新用户账单失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20010','zh','生成商户订单不唯一!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20011','zh','更新商户订单失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20012','zh','调用acm账户接口失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20013','zh','调用mkm营销接口失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20014','zh','调用inv理财接口失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20015','zh','调cpi资金能力接口查银行卡数失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20016','zh','生成对账文件，写文件失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20017','zh','生成对账标志文件失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20018','zh','上传对账文件失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20019','zh','获取本地路径失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('BIL20020','zh','用户ID不存在!',now(),now());
