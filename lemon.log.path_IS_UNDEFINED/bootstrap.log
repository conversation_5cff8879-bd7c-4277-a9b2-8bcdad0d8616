08/11/2025 09:31:03.171 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
08/11/2025 09:31:03.188 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@5d0a1059: startup date [Mon Aug 11 09:31:03 CST 2025]; root of context hierarchy
08/11/2025 09:31:03.390 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
08/11/2025 09:31:03.430 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$303098bf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/11/2025 11:39:05.417 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@1de76cc7: startup date [Mon Aug 11 11:39:05 CST 2025]; root of context hierarchy
08/11/2025 11:39:05.421 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
08/11/2025 11:39:05.600 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
08/11/2025 11:39:05.640 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1457d653] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/11/2025 19:20:30.211 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
08/11/2025 19:20:30.217 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@c88a337: startup date [Mon Aug 11 19:20:30 CST 2025]; root of context hierarchy
08/11/2025 19:20:30.471 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
08/11/2025 19:20:30.525 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$9438d0c1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
