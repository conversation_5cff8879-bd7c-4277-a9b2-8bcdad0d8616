08/12/2025 09:47:26 INFO  [main]o.h.j.i.u.Log<PERSON>elper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
08/12/2025 09:47:26 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
08/12/2025 09:47:26 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
08/12/2025 09:47:26 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
08/12/2025 09:47:26 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
08/12/2025 09:47:27 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08/12/2025 09:47:27 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
08/12/2025 09:47:27 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
08/12/2025 09:47:27 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
08/12/2025 09:47:34 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
08/12/2025 09:47:36 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08/12/2025 09:47:36 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
08/12/2025 09:47:36 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
08/12/2025 09:47:36 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
08/12/2025 09:47:48 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
08/12/2025 09:47:51 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08/12/2025 09:47:51 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
08/12/2025 09:47:54 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
08/12/2025 09:47:56 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08/12/2025 09:47:56 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
