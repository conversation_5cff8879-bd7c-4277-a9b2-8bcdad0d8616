08/12/2025 09:47:20.203 INFO  [main]c.h.t.Application - The following profiles are active: dev
08/12/2025 09:47:20.235 INFO  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@5eeedb60: startup date [Tue Aug 12 09:47:20 CST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@54bff557
08/12/2025 09:47:21.842 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:21.900 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:21.908 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:21.920 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:21.969 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:21.974 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:21.982 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:21.992 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.001 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.010 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.026 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.035 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.045 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.055 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.090 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.093 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.100 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
08/12/2025 09:47:22.109 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.138 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:22.189 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'bilTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/bilConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilMyBatisConfig; factoryMethodName=cpoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/BilMyBatisConfig.class]]
08/12/2025 09:47:22.195 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'invTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/InvConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invMyBatisConfig; factoryMethodName=invTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/InvMyBatisConfig.class]]
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardBinDao' and 'com.hisun.tms.cpt.dao.cpi.ICardBinDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardProtDao' and 'com.hisun.tms.cpt.dao.cpi.ICardProtDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnRouteDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IFundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IFundOrderDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IRefundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IRefundOrderDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ISettleDetailDao' and 'com.hisun.tms.cpt.dao.cpi.ISettleDetailDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnBusiDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnInfoDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnRouteDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.201 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IWithdrawOrderDao' and 'com.hisun.tms.cpt.dao.cpo.IWithdrawOrderDao' mapperInterface. Bean already defined with the same name!
08/12/2025 09:47:22.202 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tamTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TamConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamMyBatisConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TamMyBatisConfig.class]]
08/12/2025 09:47:22.203 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tfmTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TfmConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmMyBatisConfig; factoryMethodName=tfmTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TfmMyBatisConfig.class]]
08/12/2025 09:47:22.724 INFO  [main]o.s.i.c.IntegrationRegistrar - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
08/12/2025 09:47:22.800 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
08/12/2025 09:47:23.071 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.DatatablesExampleRepository.
08/12/2025 09:47:23.071 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercItfRepository.
08/12/2025 09:47:23.071 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.OfficeInfoRepository.
08/12/2025 09:47:23.071 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesRoleRepository.
08/12/2025 09:47:23.071 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcBalRepository.
08/12/2025 09:47:23.072 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleListRepository.
08/12/2025 09:47:23.072 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.WhiteListRepository.
08/12/2025 09:47:23.072 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkErrorRepository.
08/12/2025 09:47:23.072 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DataTablesMercExtInfoRepository.
08/12/2025 09:47:23.072 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.TfmFeeOrderRepository.
08/12/2025 09:47:23.072 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.CshOrderRepository.
08/12/2025 09:47:23.073 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesProInfoRepository.
08/12/2025 09:47:23.073 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesPhoneSegementInfoRepository.
08/12/2025 09:47:23.073 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.ExampleRepository.
08/12/2025 09:47:23.073 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRsRepository.
08/12/2025 09:47:23.073 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustInfRepository.
08/12/2025 09:47:23.073 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.ConstantParamsRepository.
08/12/2025 09:47:23.073 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrRfdOrderRepository.
08/12/2025 09:47:23.073 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.MercRegisterSeqRepository.
08/12/2025 09:47:23.074 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRepository.
08/12/2025 09:47:23.074 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.RoleRepository.
08/12/2025 09:47:23.074 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesCampaignInfoRepository.
08/12/2025 09:47:23.074 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesWhiteListRepository.
08/12/2025 09:47:23.074 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.CheckRuleRepository.
08/12/2025 09:47:23.075 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrRfdOrderRepository.
08/12/2025 09:47:23.075 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmCprExtInfRespository.
08/12/2025 09:47:23.075 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserBaseInfoRepository.
08/12/2025 09:47:23.075 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmPropertyRepository.
08/12/2025 09:47:23.075 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesMerchantOrderInfoRepository.
08/12/2025 09:47:23.075 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesUserHistoryRepository.
08/12/2025 09:47:23.075 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DataTableMercRegisterRepository.
08/12/2025 09:47:23.076 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.BatchFileRecRepository.
08/12/2025 09:47:23.076 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RiskRuleRepository.
08/12/2025 09:47:23.076 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeInfoRespository.
08/12/2025 09:47:23.076 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesSmsTemplateInfoRepository.
08/12/2025 09:47:23.076 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleBaseDORespository.
08/12/2025 09:47:23.076 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmFeeOrderRepository.
08/12/2025 09:47:23.076 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercSafelInfoRepository.
08/12/2025 09:47:23.076 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrOrderRepository.
08/12/2025 09:47:23.077 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkErrorRepository.
08/12/2025 09:47:23.077 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserInfoRepository.
08/12/2025 09:47:23.077 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBannerInfoRepository.
08/12/2025 09:47:23.077 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesConstantParamsRepository.
08/12/2025 09:47:23.077 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsUserOprLogRepository.
08/12/2025 09:47:23.077 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.DatatablesCpmOrderRepository.
08/12/2025 09:47:23.078 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.NoticeInfoRepository.
08/12/2025 09:47:23.078 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrOrderRepository.
08/12/2025 09:47:23.078 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.MkmAtvUserRespository.
08/12/2025 09:47:23.078 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesNoticeInfoRepository.
08/12/2025 09:47:23.078 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.SmsTemplateInfoRepository.
08/12/2025 09:47:23.078 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesCheckRuleRepository.
08/12/2025 09:47:23.078 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsOprMapperRespository.
08/12/2025 09:47:23.078 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.PhoneSegementInfoRepository.
08/12/2025 09:47:23.079 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.CampaignInfoRepository.
08/12/2025 09:47:23.079 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesBlackListRepository.
08/12/2025 09:47:23.079 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAcmAcDetailRepository.
08/12/2025 09:47:23.079 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercExtInfoRepository.
08/12/2025 09:47:23.079 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
08/12/2025 09:47:23.079 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BannerInfoRepository.
08/12/2025 09:47:23.079 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesOrderRepository.
08/12/2025 09:47:23.079 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmInfRepository.
08/12/2025 09:47:23.080 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRoleRespository.
08/12/2025 09:47:23.080 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesOrderInfoRepository.
08/12/2025 09:47:23.080 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesAreaInfoRepository.
08/12/2025 09:47:23.080 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercInfoRepository.
08/12/2025 09:47:23.080 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkControlRepository.
08/12/2025 09:47:23.080 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercSafelInfoRepository.
08/12/2025 09:47:23.080 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustDetailRepository.
08/12/2025 09:47:23.080 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpt.repository.CpiSubMercCastRepository.
08/12/2025 09:47:23.081 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.MessageTemplateInfoRepository.
08/12/2025 09:47:23.081 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercInfoRepository.
08/12/2025 09:47:23.081 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmInfRepository.
08/12/2025 09:47:23.081 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRiskRuleRepository.
08/12/2025 09:47:23.081 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesHighRiskRepository.
08/12/2025 09:47:23.081 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcInfRepository.
08/12/2025 09:47:23.081 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DateRateRepository.
08/12/2025 09:47:23.081 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.EmailTemplateInfoRepository.
08/12/2025 09:47:23.082 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.PaytypeRepository.
08/12/2025 09:47:23.082 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesOfficeInfoRepository.
08/12/2025 09:47:23.082 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybSeqRepository.
08/12/2025 09:47:23.082 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.DatatablesPayTypeRepository.
08/12/2025 09:47:23.082 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleParamRepository.
08/12/2025 09:47:23.082 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RsmUserStatusRepository.
08/12/2025 09:47:23.082 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybCertInfRepository.
08/12/2025 09:47:23.083 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeLoginRespository.
08/12/2025 09:47:23.083 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmPropertyRepository.
08/12/2025 09:47:23.083 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.BlackListRepository.
08/12/2025 09:47:23.083 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercItfInfoRepository.
08/12/2025 09:47:23.083 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.CpmOrderRepository.
08/12/2025 09:47:23.083 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesEmailTemplateInfoRepository.
08/12/2025 09:47:23.084 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBranchInfoRepository.
08/12/2025 09:47:23.084 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.AreaInfoRepository.
08/12/2025 09:47:23.084 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.ProInfoRepository.
08/12/2025 09:47:23.084 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.SeaccyDetailRepository.
08/12/2025 09:47:23.084 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.HighRiskRepository.
08/12/2025 09:47:23.084 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustInfRepository.
08/12/2025 09:47:23.084 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRsRepository.
08/12/2025 09:47:23.084 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RuleParamRepository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.OrderRepository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BranchInfoRepository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.CprResInfRespository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.InvUserInfoRepository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustDetailRepository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesUserRepository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRepository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.MerchantRateRuleRespository.
08/12/2025 09:47:23.085 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkControlRepository.
08/12/2025 09:47:23.086 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleCardRespository.
08/12/2025 09:47:23.086 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.ResourceRepository.
08/12/2025 09:47:23.086 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesMessageTemplateInfoRepository.
08/12/2025 09:47:23.086 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRepository.
08/12/2025 09:47:23.324 INFO  [main]o.s.c.c.s.GenericScope - BeanFactory id=adea5bfa-576d-3690-9794-de1760abb8aa
08/12/2025 09:47:23.349 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
08/12/2025 09:47:23.359 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
08/12/2025 09:47:23.369 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
08/12/2025 09:47:23.451 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.ExchangeOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.452 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.TransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.454 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.BankClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.454 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CardClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.455 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.455 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CregisClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.456 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.EbankpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.456 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.FastpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.457 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.PosClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.457 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.458 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RemittanceClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.458 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.460 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.bil.client.DataBoardClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.461 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.bil.client.MercBillQueryClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.461 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.bil.client.UserBillQueryClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.463 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshAuditTransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.464 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.464 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.466 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.466 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckUserStatusClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.467 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskListMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.467 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskParamMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.468 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskRuleMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.469 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.CmmServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.469 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.ConstantParamClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.470 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.GrantClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.470 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.QRCodeServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.471 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.SmsServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.472 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.CopAgcyBizClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.473 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.473 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.WithdrawClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.474 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserAuthenticationClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.475 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.475 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient1' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.475 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserPasswordClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.476 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountingTreatmentClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.478 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountManagementClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.478 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketActivityClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.479 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketingToolsMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.480 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderCancleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.480 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.480 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.ConfigureClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.481 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderQueryClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.481 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OnrCheckedHandleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.482 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRegistratClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.482 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.NotifySendClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.483 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.chk.client.ChkClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.531 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$3d9ae33f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.665 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$772f2511] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.821 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration$$EnhancerBySpringCGLIB$$fdced4b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.832 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.836 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@3cacc87' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.853 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webMvcConfig' of type [com.hisun.tms.common.config.WebMvcConfig$$EnhancerBySpringCGLIB$$9c9bbdeb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.880 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#3fe24670' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.881 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#3fe24670' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.886 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#309e3f34' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.891 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#1be52113' of type [org.springframework.data.repository.query.ExtensionAwareEvaluationContextProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:23.927 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSourceConfig' of type [com.hisun.tms.common.config.DataSourceConfig$$EnhancerBySpringCGLIB$$bd0ff18a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:24.111 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:24.137 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:24.138 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsConfig' of type [com.hisun.tms.common.config.TmsConfig$$EnhancerBySpringCGLIB$$d15caa65] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:24.159 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$$EnhancerBySpringCGLIB$$ba3201b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:24.169 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:24.177 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:24.184 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration$$EnhancerBySpringCGLIB$$e0b58931] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:26.702 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:26.708 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:26.731 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
08/12/2025 09:47:26.741 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
08/12/2025 09:47:26.809 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
08/12/2025 09:47:26.810 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
08/12/2025 09:47:26.811 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
08/12/2025 09:47:26.853 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
08/12/2025 09:47:27.070 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08/12/2025 09:47:27.271 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
08/12/2025 09:47:27.271 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
08/12/2025 09:47:27.504 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
08/12/2025 09:47:34.082 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
08/12/2025 09:47:34.084 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:34.086 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:34.099 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#61d7bb61' of type [com.sun.proxy.$Proxy163] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:34.229 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:34.235 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmConfig' of type [com.hisun.tms.common.config.AcmConfig$$EnhancerBySpringCGLIB$$2637d236] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:34.247 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'acmPersistenceUnit'
08/12/2025 09:47:34.247 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
08/12/2025 09:47:36.714 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08/12/2025 09:47:36.733 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
08/12/2025 09:47:36.733 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
08/12/2025 09:47:36.755 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#197da701' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:36.756 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#197da701' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:36.784 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
08/12/2025 09:47:48.632 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'acmPersistenceUnit'
08/12/2025 09:47:48.632 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:48.633 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:48.741 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:48.747 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilConfig' of type [com.hisun.tms.common.config.bilConfig$$EnhancerBySpringCGLIB$$4dd6a490] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:48.757 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'bilPersistenceUnit'
08/12/2025 09:47:48.758 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
08/12/2025 09:47:51.208 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08/12/2025 09:47:51.221 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
08/12/2025 09:47:53.877 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'bilPersistenceUnit'
08/12/2025 09:47:53.877 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:53.878 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:53.988 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:53.995 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkConfig' of type [com.hisun.tms.common.config.ChkConfig$$EnhancerBySpringCGLIB$$ee398671] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
08/12/2025 09:47:54.005 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'chkPersistenceUnit'
08/12/2025 09:47:54.005 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
08/12/2025 09:47:56.466 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
08/12/2025 09:47:56.493 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
