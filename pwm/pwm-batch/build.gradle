apply plugin: 'application'

dependencies {
    compile project(":pwm-interface")
    compile("com.hisun:lemon-framework")
    compile("com.hisun:csh-interface")
    compile("com.hisun:cpo-interface")
    compile("com.hisun:acm-interface")
    compile("com.hisun:urm-interface")
    compile("com.hisun:mkm-interface")
    compile("com.hisun:tfm-interface")
    compile("com.hisun:cpi-interface")
    compile("com.hisun:rsm-interface")
    compile("com.hisun:urm-interface")
    compile("com.hisun:bil-interface")
    compile("com.hisun:jcommon")
    compile("com.hisun:cmm-interface")
}

mainClassName = 'com.hisun.lemon.Application'

jar {
    manifest {
        attributes(
                   "Implementation-Title": "Gradle",
                   "Implementation-Version": "${version}",
                   "Class-Path": '. config/'
                  )
    }
//    exclude('config/')
}

bootRepackage {
    enabled = true
    mainClass = 'com.hisun.lemon.Application'
}

task clearTarget(type:Delete){  
   delete 'build/target'  
}  

task release(type: Copy,dependsOn: [clearTarget,build]) {  
    from('build/libs') {  
        include '*.jar'
        exclude '*-sources.jar'  
    }  
//    from('src/main/resources') {
//        include 'config/*'
//    }
    into ('build/target') 
    
    into('bin') {
        from 'shell'
    } 
} 

task dist(type: Zip,dependsOn: [release]) {  
    from ('build/target/') {
    } 
}
