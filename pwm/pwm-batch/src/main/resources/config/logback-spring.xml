<configuration>
  <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="appName"/>

  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${lemon.log.path}/lemon-${appName}.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${lemon.log.path}/lemon-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
      <maxFileSize>20MB</maxFileSize>
    </rollingPolicy>
    <encoder>
      <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
    </encoder>
  </appender>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <!-- 该Filter 不要配置到生产，仅仅用于调试环境，用于禁止eureka订阅服务日志干扰调试
    <filter class="com.hisun.lemon.framework.log.logback.TestFilter" />
    -->
    <encoder>
      <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
    </encoder>
  </appender>

  <appender name="ERROR-FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${lemon.log.path}/error-${appName}.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${lemon.log.path}/error-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
      <maxFileSize>20MB</maxFileSize>
    </rollingPolicy>
    <encoder>
      <pattern>%d %-5level [%-18.18thread] %-36logger{36} - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
  </appender>

  <appender name="FILE-CLIENT" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${lemon.log.path}/client-${appName}.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${lemon.log.path}/client-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
      <maxFileSize>20MB</maxFileSize>
    </rollingPolicy>
    <encoder>
      <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
    </encoder>
  </appender>

  <appender name="FILE-SERVER" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${lemon.log.path}/server-${appName}.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${lemon.log.path}/server-${appName}-%d{yyyyMMdd}.%i.log</fileNamePattern>
      <maxFileSize>20MB</maxFileSize>
    </rollingPolicy>
    <encoder>
      <pattern>%d %-5level [%-18.18thread] - [%X{requestId:-SYSTEM}]: %msg%n</pattern>
    </encoder>
  </appender>

  <logger name="com.hisun.lemon.framework.springcloud.fegin" level="INFO" additivity="false">
    <appender-ref ref="FILE-CLIENT"/>
    <appender-ref ref="STDOUT"/>
  </logger>

  <logger name="com.netflix.loadbalancer.LoadBalancerContext" level="DEBUG" additivity="false">
    <appender-ref ref="FILE-CLIENT"/>
    <appender-ref ref="STDOUT"/>
  </logger>

  <logger name="com.hisun.lemon.framework.aop.ControllerAspect$ReceiveRequestLog" level="INFO" additivity="false">
    <appender-ref ref="FILE-SERVER"/>
    <appender-ref ref="STDOUT"/>
  </logger>

  <logger name="org.springframework.amqp.rabbit.listener.BlockingQueueConsumer" level="INFO" additivity="false">
    <appender-ref ref="FILE"/>
    <appender-ref ref="STDOUT"/>
  </logger>

  <root level="DEBUG">
    <springProfile name="ci, dev, sit, uat, str, pre, prd">
      <appender-ref ref="FILE"/>
      <appender-ref ref="ERROR-FILE"/>
    </springProfile>
    <springProfile name="dev, prd">
      <appender-ref ref="STDOUT"/>
    </springProfile>
  </root>

</configuration>