<config xmlns='http://www.ehcache.org/v3'
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:jsr107="http://www.ehcache.org/v3/jsr107"
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core-3.3.xsd
                            http://www.ehcache.org/v3/jsr107 http://www.ehcache.org/schema/ehcache-107-ext-3.3.xsd">
  <service>
    <jsr107:defaults enable-management="true" enable-statistics="true"/>
  </service>

  <cache alias="lemonMsgInfo">
    <expiry>
      <ttl unit="minutes">5</ttl>
    </expiry>
    <heap unit="entries">10000</heap>
    <heap-store-settings>
      <max-object-size unit="kB">10</max-object-size>
    </heap-store-settings>
  </cache>
</config>
