package com.hisun.lemon.pwm.service;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.pwm.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月7日
 * @time 下午2:13:58
 *
 */
public interface IWithdrawOrderService {

    /**
     * 提现申请，生成提现订单
     * @param genericWithdrawDTO
     */
    public WithdrawRspDTO createOrder(GenericDTO<WithdrawDTO> genericWithdrawDTO);

    /**
     * 提现结果处理：更新订单信息
     * @param genericWithdrawResultDTO
     */
    public WithdrawRspDTO completeOrder(GenericDTO<WithdrawResultDTO> genericWithdrawResultDTO);

    /**
     * 查询交易费率
     * @param withdrawRateDTO
     */
    public GenericRspDTO<WithdrawRateResultDTO> queryRate(WithdrawRateDTO withdrawRateDTO);

    /**
     * 查询提现银行
     * @return
     */
    public List<WithdrawBankRspDTO> queryBank(GenericDTO genericDTO);

    /**
     * 添加提现银行卡
     * @param genericWithdrawCardBindDTO
     */
    public GenericRspDTO addCard(GenericDTO<WithdrawCardBindDTO> genericWithdrawCardBindDTO);

    /**
     * 查询已添加的提现银行卡
     * @param genericDTO
     * @return
     */
    public List<WithdrawCardQueryDTO> queryCard(GenericDTO genericDTO);

    /**
     * 删除提现应行卡
     * @param genericWithdrawCardDelDTO
     * @return
     */
    public GenericRspDTO delCard(GenericDTO<WithdrawCardDelDTO> genericWithdrawCardDelDTO);

    /**
     * 提现差错处理
     * @param genericWithdrawErrorHandleDTO
     * @return
     */
    public GenericRspDTO withdrawErrorHandler(GenericDTO<WithdrawErrorHandleDTO> genericWithdrawErrorHandleDTO);

    /**
     * 营业厅提现
     * @param genericWithdrawHallDTO
     * @return
     */
    public HallWithdrawResultDTO handleHallWithdraw(GenericDTO<HallWithdrawApplyDTO> genericWithdrawHallDTO);


    /**
     * 处理个人营业厅取现长款处理
     * @param genericWithdrawRevokeDTO
     * @return
     */
    public void hallWithdrawRevokeHandle(GenericDTO<HallWithdrawRevokeDTO> genericWithdrawRevokeDTO);

    /**
     * 个人提现到ACLEDA银行
     * @param genericWithdrawDTO
     * @return
     */
    public WithdrawRspDTO createAcledaOrder(GenericDTO<AcledaWithdrawDTO> genericWithdrawDTO);

    /**
     * 个人提现到ACLEDA银行前端通知
     * @param withdrawAcledaNotifyDTO
     * @return
     */
    public GenericRspDTO<NoBody> acledaCardWithdrawNotify(GenericDTO<AcledaCardWithdrawNotifyDTO> withdrawAcledaNotifyDTO);

    /**
     * 数币提现
     * @param dmWithdrawReqDTO
     * @return
     */
    DmWithdrawRspDTO createDmOrder(GenericDTO<DmWithdrawReqDTO> dmWithdrawReqDTO);

    WithdrawOrderRspDTO getByOrder(GenericDTO<GetByOrderDTO> genericDTO);

    /**
     * 更新订单
     * @param genericDTO
     * @return
     */
    WithdrawOrderRspDTO updateOrder(GenericDTO<UpdateOrderDTO> genericDTO);
}
