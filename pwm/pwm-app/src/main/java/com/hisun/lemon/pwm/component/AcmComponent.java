package com.hisun.lemon.pwm.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * 账务组件
 * 
 * 
 *
 */
@Component
public class AcmComponent {
	private static final org.slf4j.Logger logger = LoggerFactory.getLogger(AcmComponent.class);

	@Resource
	private AccountingTreatmentClient accountingTreatmentClient;

	@Resource
	private AccountManagementClient accountManagementClient;

	/**
	 *
	 * @param userId
	 *            用户ID
	 * @param acTye
	 *            账户类型 1 现金 2 待结算款
	 * @return
	 */
	public String getAcmAcNo(String userId, String acTye) {
		UserAccountDTO userDTO = new UserAccountDTO();
		userDTO.setUserId(userId);
		GenericDTO<UserAccountDTO> user=new GenericDTO<>();
		user.setBody(userDTO);
		GenericDTO<List<QueryAcBalRspDTO>> genericQueryAcBalRspDTO = accountManagementClient.queryAcBal(userDTO);
		List<QueryAcBalRspDTO> acmAcBalInfList = genericQueryAcBalRspDTO.getBody();

		if (JudgeUtils.isNull(acmAcBalInfList) || JudgeUtils.isEmpty(acmAcBalInfList)) {
			throw new LemonException("PWM40003");
		}

		for (QueryAcBalRspDTO queryAcBalRspDTO : acmAcBalInfList) {
			if (StringUtils.equals(queryAcBalRspDTO.getCapTyp(), acTye)) {
				return queryAcBalRspDTO.getAcNo();
			}
		}
		return null;
	}

	private QueryAcBalRspDTO getAcmAcInfo(String userId, String acTye, String ccy) {
		UserAccountDTO userDTO = new UserAccountDTO();
		userDTO.setUserId(userId);
		userDTO.setCcy(ccy);
		GenericRspDTO<List<QueryAcBalRspDTO>> genericQueryAcBalRspDTO = accountManagementClient.queryAcBal(userDTO);
		if(JudgeUtils.isNotSuccess(genericQueryAcBalRspDTO.getMsgCd())){
			logger.error("查询账户信息失败，userId:{}", userId);
			LemonException.throwBusinessException(genericQueryAcBalRspDTO.getMsgCd());
		}

		List<QueryAcBalRspDTO> acmAcBalInfList = genericQueryAcBalRspDTO.getBody();

		if (JudgeUtils.isNull(acmAcBalInfList) || JudgeUtils.isEmpty(acmAcBalInfList)) {
			throw new LemonException("CSH20050");
		}

		for (QueryAcBalRspDTO queryAcBalRspDTO : acmAcBalInfList) {
			if (StringUtils.equals(queryAcBalRspDTO.getCapTyp(), acTye)) {
				return queryAcBalRspDTO;
			}
		}
		return null;
	}

	/**
	 *
	 * @param userId 账户ID
	 * @param acTye 账户类型 1 现金 8 待结算款
	 * @param ccy 币种
	 * @return
	 */
	public String getAcmAcNo(String userId, String acTye, String ccy) {
		logger.info("getAcmAcNo userId:{},acTye:{},ccy:{}",userId,acTye,ccy);
		QueryAcBalRspDTO queryAcBalRspDTO=this.getAcmAcInfo(userId,acTye,ccy);
		if(JudgeUtils.isNull(queryAcBalRspDTO)){
			LemonException.throwBusinessException("CSH20050");
		}
		return queryAcBalRspDTO.getAcNo();
	}

	/**
	 *
	 * @param userId
	 *            用户ID
	 * @param acType
	 *            账户类型 1 现金 2 待结算款
	 * @return
	 */
	public BigDecimal getAccountBal(String userId,String acType){
		QueryAcBalRspDTO queryAcBalRspDTO=this.getAcmAcInfo(userId,acType);
		if(JudgeUtils.isNull(queryAcBalRspDTO)){
			LemonException.throwBusinessException("CSH20050");
		}
		return queryAcBalRspDTO.getAcCurBal();
	}

	/**
	 * 生成账务请求对象
	 * 
	 * @param orderNo
	 *            订单号
	 * @param txJrnNo
	 *            订单流水号
	 * @param txType
	 *            交易类型
	 * @param txSts
	 *            交易状态
	 * @param txAmt
	 *            交易金额
	 * @param acNo
	 *            账户号
	 * @param acType
	 *            账户类型
	 * @param capType
	 *            资金属性
	 * @param dcFlag
	 *            借贷标识
	 * @param itmNo
	 *            内部科目号
	 * @param oppAcNo
	 *            对手方账号
	 * @param oppCapType
	 *            对手方资金属性
	 * @param oppUsrId
	 *            对手方用户id
	 * @param oppUsrType
	 * @param remark
	 *            备注
	 * @return 账务请求信息对象
	 */
	public AccountingReqDTO createAccountingReqDTO(String orderNo, String txJrnNo, String txType, String txSts,
			BigDecimal txAmt, String acNo, String acType, String capType, String dcFlag, String itmNo, String oppAcNo,
			String oppCapType, String oppUsrId, String oppUsrType, String remark) {
		AccountingReqDTO accountReqDTO = new AccountingReqDTO();
		accountReqDTO.setTxTyp(txType);
		accountReqDTO.setTxSts(txSts);
		accountReqDTO.setTxAmt(txAmt);
		accountReqDTO.setTxJrnNo(txJrnNo);
		accountReqDTO.setTxOrdDt(DateTimeUtils.getCurrentLocalDate());
		accountReqDTO.setTxOrdTm(DateTimeUtils.getCurrentLocalTime());
		accountReqDTO.setTxOrdNo(orderNo);
		accountReqDTO.setAcNo(acNo);
		accountReqDTO.setAcTyp(acType);
		accountReqDTO.setCapTyp(capType);
		accountReqDTO.setDcFlg(dcFlag);
		accountReqDTO.setItmNo(itmNo);
		accountReqDTO.setOppAcNo(oppAcNo);
		accountReqDTO.setOppCapTyp(oppCapType);
		accountReqDTO.setOppUserId(oppUsrId);
		accountReqDTO.setOppUserTyp(oppUsrType);
		accountReqDTO.setRmk(remark);
		accountReqDTO.setUsrIpAdr(null);
		return accountReqDTO;
	}

	public GenericDTO<NoBody> requestAc(AccountingReqDTO... accountingReqDTOs) {
		BigDecimal dAmt = BigDecimal.ZERO;
		BigDecimal cAmt = BigDecimal.ZERO;
		List<AccountingReqDTO> accList = new ArrayList<>();
		for (AccountingReqDTO dto : accountingReqDTOs) {
			if (JudgeUtils.isNotNull(dto)) {
				accList.add(dto);
				if (JudgeUtils.equals(dto.getDcFlg(), ACMConstants.AC_D_FLG)) {
					dAmt = dAmt.add(dto.getTxAmt());
				} else {
					cAmt = cAmt.add(dto.getTxAmt());
				}
			}
		}

		// 借贷平衡校验
		if (cAmt.compareTo(dAmt) != 0) {
			LemonException.throwBusinessException("PWM40002");
		}

		GenericDTO<List<AccountingReqDTO>> userAccDto = new GenericDTO<>();
		userAccDto.setBody(accList);
		GenericRspDTO<NoBody> accountingTreatment = accountingTreatmentClient.accountingTreatment(userAccDto);
		if (JudgeUtils.isNotSuccess(accountingTreatment.getMsgCd())) {
			LemonException.throwBusinessException(accountingTreatment.getMsgCd());
		}
		return accountingTreatment;
	}

	private QueryAcBalRspDTO getAcmAcInfo(String userId, String acTye) {
		UserAccountDTO userDTO = new UserAccountDTO();
		userDTO.setUserId(userId);
		GenericDTO<UserAccountDTO> user=new GenericDTO<>();
		user.setBody(userDTO);
		GenericRspDTO<List<QueryAcBalRspDTO>> genericQueryAcBalRspDTO = accountManagementClient.queryAcBal(userDTO);
		if(JudgeUtils.isNotSuccess(genericQueryAcBalRspDTO.getMsgCd())){
			logger.error("查询账户信息失败："+userId);
			LemonException.throwBusinessException(genericQueryAcBalRspDTO.getMsgCd());
		}

		List<QueryAcBalRspDTO> acmAcBalInfList = genericQueryAcBalRspDTO.getBody();

		if (JudgeUtils.isNull(acmAcBalInfList) || JudgeUtils.isEmpty(acmAcBalInfList)) {
			throw new LemonException("CSH20050");
		}

		for (QueryAcBalRspDTO queryAcBalRspDTO : acmAcBalInfList) {
			if (StringUtils.equals(queryAcBalRspDTO.getCapTyp(), acTye)) {
				return queryAcBalRspDTO;
			}
		}
		return null;
	}

	public GenericRspDTO<NoBody> accountingTreatment(List<AccountingReqDTO> accountingReqDTOList) {
		BigDecimal dAmt = BigDecimal.ZERO;
		BigDecimal cAmt = BigDecimal.ZERO;
		for (AccountingReqDTO accountingReqDTO : accountingReqDTOList) {
			if (JudgeUtils.isNotNull(accountingReqDTO)) {
				if (JudgeUtils.equals(accountingReqDTO.getDcFlg(), ACMConstants.AC_D_FLG)) {
					dAmt = dAmt.add(accountingReqDTO.getTxAmt());
				} else {
					cAmt = cAmt.add(accountingReqDTO.getTxAmt());
				}
			}
		}
		// 借贷平衡校验
		if (cAmt.compareTo(dAmt) != 0) {
			LemonException.throwBusinessException("PWM40002");
		}
		GenericDTO<List<AccountingReqDTO>> userAccDto = new GenericDTO<>();
		userAccDto.setBody(accountingReqDTOList);
		GenericRspDTO<NoBody> genericRspDTO = accountingTreatmentClient.accountingTreatment(userAccDto);
		if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
			LemonException.throwBusinessException(genericRspDTO.getMsgCd());
		}
		return genericRspDTO;
	}

}
