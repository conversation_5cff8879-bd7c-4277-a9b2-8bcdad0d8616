package com.hisun.lemon.pwm.controller;

import javax.annotation.Resource;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.pwm.dto.*;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.pwm.service.IWithdrawOrderService;

import java.util.List;


@Api(value = "处理提现")
@RestController
@RequestMapping(value = "/pwm/withdraw")
public class WithdrawOrderController  extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(WithdrawOrderController.class);

	@Resource
	private IWithdrawOrderService withdrawOrderService;

	/**
	 * 提现申请：生成提现订单
	 * 
	 * @param genericWithdrawDTO
	 */
	@ApiOperation(value = "申请提现", notes = "生成提现订单")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "申请提现")
	@PostMapping(value = "/order")
	public GenericRspDTO createOrder(@Validated @RequestBody GenericDTO<WithdrawDTO> genericWithdrawDTO) {

		genericWithdrawDTO.getBody().setUserId(LemonUtils.getUserId());
		WithdrawRspDTO withdrawRspDTO = withdrawOrderService.createOrder(genericWithdrawDTO);
		return GenericRspDTO.newSuccessInstance(withdrawRspDTO);
	}

	/**
	 * 数币提现
	 *
	 * @param dmWithdrawReqDTO
	 */
	@ApiOperation(value = "数币提现", notes = "数币提现")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "数币提现")
	@PostMapping(value = "/dm/order")
	public GenericRspDTO<DmWithdrawRspDTO> createDmWithdrawOrder(@Validated @RequestBody GenericDTO<DmWithdrawReqDTO> dmWithdrawReqDTO) {

		dmWithdrawReqDTO.getBody().setUserId(LemonUtils.getUserId());
		DmWithdrawRspDTO withdrawRspDTO = withdrawOrderService.createDmOrder(dmWithdrawReqDTO);
		return GenericRspDTO.newSuccessInstance(withdrawRspDTO);
	}

	/**
	 * 根据订单号查询订单
	 *
	 * @param genericDTO
	 */
	@ApiOperation(value = "根据订单号查询订单", notes = "根据订单号查询订单")
	@ApiResponse(code = 200, message = "根据订单号查询订单成功")
	@PostMapping(value = "/dm/get/order")
	public GenericRspDTO<WithdrawOrderRspDTO> getByOrder(@Validated @RequestBody GenericDTO<GetByOrderDTO> genericDTO) {
		logger.info("根据订单号:{}查询订单",genericDTO.getBody().getOrderNo());
		WithdrawOrderRspDTO withdrawRspDTO = withdrawOrderService.getByOrder(genericDTO);
		return GenericRspDTO.newSuccessInstance(withdrawRspDTO);
	}

	/**
	 * 更新订单
	 *
	 * @param genericDTO
	 */
	@ApiOperation(value = "更新订单", notes = "更新订单")
	@ApiResponse(code = 200, message = "更新订单成功")
	@PostMapping(value = "/dm/update/order")
	public GenericRspDTO<WithdrawOrderRspDTO> updateOrder(@Validated @RequestBody GenericDTO<UpdateOrderDTO> genericDTO) {
		logger.info("根据订单号:{}更新订单",genericDTO.getBody().getOrderNo());
		WithdrawOrderRspDTO withdrawRspDTO = withdrawOrderService.updateOrder(genericDTO);
		return GenericRspDTO.newSuccessInstance(withdrawRspDTO);
	}

	/**
	 * 提现申请：生成ACLEDA银行提现订单
	 *
	 * @param genericWithdrawDTO
	 */
	@ApiOperation(value = "申请ACLEDA银行提现", notes = "申请ACLEDA银行提现")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "申请ACLEDA银行提现")
	@PostMapping(value = "/order/acleda")
	public GenericRspDTO createAcledaOrder(@Validated @RequestBody GenericDTO<AcledaWithdrawDTO> genericWithdrawDTO) {
		WithdrawRspDTO acledaWithdrawRspDTO = withdrawOrderService.createAcledaOrder(genericWithdrawDTO);
		return GenericRspDTO.newSuccessInstance(acledaWithdrawRspDTO);
	}

	/**
	 * 提现结果处理：等待异步通知，更新提现单信息
	 * 
	 * @param genericWithdrawResultDTO
	 * @return
	 */
	@ApiOperation(value = "提现结果同步", notes = "接收资金能力的处理结果通知")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "申请提现结果")
	@PatchMapping(value = "/result")
	public GenericRspDTO completeOrder(@Validated @RequestBody GenericDTO<WithdrawResultDTO> genericWithdrawResultDTO) {

	    WithdrawRspDTO withdrawRspDTO = withdrawOrderService.completeOrder(genericWithdrawResultDTO);
		return GenericRspDTO.newSuccessInstance(withdrawRspDTO);
	}

	/**
	 *	查询交易费率
	 */
    @ApiOperation(value = "查询交易费率", notes = "根据业务类型币种查询交易费率")
    @ApiResponse(code = 200, message = "查询交易费率")
    @GetMapping(value = "/rate")
	public GenericRspDTO<WithdrawRateResultDTO> queryWithdrawRate(@Validated WithdrawRateDTO withdrawRateDTO){
		GenericRspDTO genericDTO = withdrawOrderService.queryRate(withdrawRateDTO);
		genericDTO.setMsgCd(LemonUtils.getSuccessMsgCd());
		return genericDTO;
	}

	/**
	 * 查询可提现银行
	 */
	@ApiOperation(value = "查询可提现银行", notes = "查询可提现银行列表")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "查询可提现银行结果")
	@GetMapping(value = "/bank")
	public GenericRspDTO<List<WithdrawBankRspDTO>> queryWithdrawBank(GenericDTO genericDTO){
		List<WithdrawBankRspDTO> withdrawBankRspDTO = withdrawOrderService.queryBank(genericDTO);
		return GenericRspDTO.newSuccessInstance(withdrawBankRspDTO);
	}

	/**
	 * 添加提现银行卡
	 */
	@ApiOperation(value = "添加提现银行卡", notes = "添加提现银行卡")
	@ApiResponse(code = 200, message = "添加提现银行卡")
	@PostMapping(value = "/add")
	public GenericRspDTO addWithdrawCard(@Validated @RequestBody GenericDTO<WithdrawCardBindDTO> genericWithdrawCardBindDTO){
		genericWithdrawCardBindDTO.getBody().setUserId(LemonUtils.getUserId());
		return withdrawOrderService.addCard(genericWithdrawCardBindDTO);
	}

    /**
     * 查询已添加的银行卡
     */
    @ApiOperation(value = "查询可提现银行", notes = "查询可提现银行列表")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "查询可提现银行结果")
    @GetMapping(value = "/card")
    public GenericRspDTO<List<WithdrawCardQueryDTO>> queryWithdrawCard(GenericDTO genericDTO){
        List<WithdrawCardQueryDTO> withdrawCardQueryDTO = withdrawOrderService.queryCard(genericDTO);
        return GenericRspDTO.newSuccessInstance(withdrawCardQueryDTO);
    }

	/**
	 * 删除提现银行卡
	 */
	@ApiOperation(value = "删除提现银行卡", notes = "删除提现银行卡")
	@ApiResponse(code = 200, message = "删除提现银行卡")
	@PutMapping(value = "/del")
	public GenericRspDTO<WithdrawCardDelRspDTO> delWithdrawCard(@Validated @RequestBody GenericDTO<WithdrawCardDelDTO> genericWithdrawCardDelDTO){
		return withdrawOrderService.delCard(genericWithdrawCardDelDTO);
	}

	/**
	 * 提现差错处理
	 */
	@ApiOperation(value = "提现差错处理", notes = "提现差错处理")
	@ApiResponse(code = 200, message = "提现差错处理结果")
	@PostMapping(value = "/chk/error/handle")
	public GenericRspDTO withdrawErrorHandler(@Validated @RequestBody GenericDTO<WithdrawErrorHandleDTO> genericWithdrawErrorHandleDTO) {
		return withdrawOrderService.withdrawErrorHandler(genericWithdrawErrorHandleDTO);
	}

	@ApiOperation(value = "营业厅提现", notes = "营业厅提现处理")
	@ApiResponse(code = 200, message = "营业厅提现结果")
	@PostMapping(value = "/hall")
	public GenericRspDTO<HallWithdrawResultDTO> hallWithdrawHandle(@Validated @RequestBody GenericDTO<HallWithdrawApplyDTO> genericWithdrawResultDTO) {
		HallWithdrawResultDTO hallWithdrawResultDTO = withdrawOrderService.handleHallWithdraw(genericWithdrawResultDTO);
		return GenericRspDTO.newSuccessInstance(hallWithdrawResultDTO);
	}

	@ApiOperation(value = "个人营业厅提现对账撤单处理", notes = "个人营业厅提现对账撤单处理")
	@ApiResponse(code = 200, message = "个人营业厅提现对账撤单处理")
	@PostMapping(value = "/hall/revoke")
	public GenericRspDTO<NoBody> hallWithdrawRevokeHandle(@Validated @RequestBody GenericDTO<HallWithdrawRevokeDTO> genericWithdrawRevokeDTO) {
		try{
			withdrawOrderService.hallWithdrawRevokeHandle(genericWithdrawRevokeDTO);
		}catch (LemonException e){
			LemonException.throwBusinessException(e.getMsgCd());
		}
		return GenericRspDTO.newSuccessInstance();
	}

	@ApiOperation(value = "提现到ACLEDA银行卡下单成功通知", notes = "提现到ACLEDA银行卡下单成功通知")
	@ApiResponse(code = 200, message = "提现到ACLEDA银行卡下单成功通知")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	})
	@PostMapping(value = "/order/acleda/notify")
	public GenericRspDTO<NoBody> acledaCardWithdrawSuccessNotify(@Validated @RequestBody GenericDTO<AcledaCardWithdrawNotifyDTO> withdrawAcledaNotifyDTO) {
		return this.withdrawOrderService.acledaCardWithdrawNotify(withdrawAcledaNotifyDTO);
	}
}
