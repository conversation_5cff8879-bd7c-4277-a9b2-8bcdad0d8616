package com.hisun.lemon.pwm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 更新订单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 10:09
 */
@ApiModel(value = "UpdateOrderDTO", description = "更新订单")
public class UpdateOrderDTO {

    @ApiModelProperty(name = "orderNo", value = "订单号", required = true)
    @NotBlank(message = "更新订单的订单号不能为空")
    private String orderNo;

    @ApiModelProperty(name = "orderStatus", value = "订单状态(W1:系统受理中 W2:资金流出已受理 S1:付款成功 F1:付款失败 F2:付款核销 R9:审批拒绝)")
    private String orderStatus;

    @ApiModelProperty(name = "orderTm", value = "订单时间")
    private LocalDateTime orderTm;

    @ApiModelProperty(name = "orderExpTm", value = "订单失效时间")
    private LocalDateTime orderExpTm;

    @ApiModelProperty(name = "acTm", value = "记账时间")
    private LocalDate acTm;

    @ApiModelProperty(name = "orderCcy", value = "币种")
    private String orderCcy;

    @ApiModelProperty(name = "orderSuccTm", value = "订单成功时间")
    private LocalDateTime orderSuccTm;

    @ApiModelProperty(name = "wcType", value = "提现类型(11:自主提现 21:自动结算)")
    private String wcType;

    @ApiModelProperty(name = "txType", value = "交易类型(01:充值 02:消费 03:转账 04:提现 05:充海币)")
    private String txType;

    @ApiModelProperty(name = "busType", value = "业务类型(04:提现 0401:个人提现 0402:商户提现)")
    private String busType;

    @ApiModelProperty(name = "wcApplyAmt", value = "申请提现金额")
    private BigDecimal wcApplyAmt;

    @ApiModelProperty(name = "wcActAmt", value = "实际提现金额")
    private BigDecimal wcActAmt;

    @ApiModelProperty(name = "wcTotalAmt", value = "提现总金额")
    private BigDecimal wcTotalAmt;

    @ApiModelProperty(name = "feeAmt", value = "手续费金额")
    private BigDecimal feeAmt;

    @ApiModelProperty(name = "payUrgeFlg", value = "付款加急标识")
    private String payUrgeFlg;

    @ApiModelProperty(name = "userId", value = "内部用户编号")
    private String userId;

    @ApiModelProperty(name = "userName", value = "用户/商户名称")
    private String userName;

    @ApiModelProperty(name = "agrNo", value = "签约协议号")
    private String agrNo;

    @ApiModelProperty(name = "capCorgNo", value = "资金合作机构号")
    private String capCorgNo;

    @ApiModelProperty(name = "capCardNo", value = "资金卡号")
    private String capCardNo;

    @ApiModelProperty(name = "capCardType", value = "资金卡账户类型(0:借记卡 1:信用卡 2:准贷记卡 3:储蓄账户)")
    private String capCardType;

    @ApiModelProperty(name = "capCardName", value = "资金卡账户姓名")
    private String capCardName;

    @ApiModelProperty(name = "wcRemark", value = "提现备注")
    private String wcRemark;

    @ApiModelProperty(name = "ntfMbl", value = "通知的手机号")
    private String ntfMbl;

    @ApiModelProperty(name = "rspOrderNo", value = "资金流出模块订单号")
    private String rspOrderNo;

    @ApiModelProperty(name = "rspSuccTm", value = "资金流出模块成功时间")
    private LocalDateTime rspSuccTm;

    @ApiModelProperty(name = "busCnl", value = "业务受理渠道")
    private String busCnl;

    @ApiModelProperty(name = "fkAcNo", value = "付款方账号")
    private String fkAcNo;

    @ApiModelProperty(name = "address", value = "收款方地址")
    private String address;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public LocalDateTime getOrderTm() {
        return orderTm;
    }

    public void setOrderTm(LocalDateTime orderTm) {
        this.orderTm = orderTm;
    }

    public LocalDateTime getOrderExpTm() {
        return orderExpTm;
    }

    public void setOrderExpTm(LocalDateTime orderExpTm) {
        this.orderExpTm = orderExpTm;
    }

    public LocalDate getAcTm() {
        return acTm;
    }

    public void setAcTm(LocalDate acTm) {
        this.acTm = acTm;
    }

    public String getOrderCcy() {
        return orderCcy;
    }

    public void setOrderCcy(String orderCcy) {
        this.orderCcy = orderCcy;
    }

    public LocalDateTime getOrderSuccTm() {
        return orderSuccTm;
    }

    public void setOrderSuccTm(LocalDateTime orderSuccTm) {
        this.orderSuccTm = orderSuccTm;
    }

    public String getWcType() {
        return wcType;
    }

    public void setWcType(String wcType) {
        this.wcType = wcType;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public BigDecimal getWcApplyAmt() {
        return wcApplyAmt;
    }

    public void setWcApplyAmt(BigDecimal wcApplyAmt) {
        this.wcApplyAmt = wcApplyAmt;
    }

    public BigDecimal getWcActAmt() {
        return wcActAmt;
    }

    public void setWcActAmt(BigDecimal wcActAmt) {
        this.wcActAmt = wcActAmt;
    }

    public BigDecimal getWcTotalAmt() {
        return wcTotalAmt;
    }

    public void setWcTotalAmt(BigDecimal wcTotalAmt) {
        this.wcTotalAmt = wcTotalAmt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getPayUrgeFlg() {
        return payUrgeFlg;
    }

    public void setPayUrgeFlg(String payUrgeFlg) {
        this.payUrgeFlg = payUrgeFlg;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public String getCapCorgNo() {
        return capCorgNo;
    }

    public void setCapCorgNo(String capCorgNo) {
        this.capCorgNo = capCorgNo;
    }

    public String getCapCardNo() {
        return capCardNo;
    }

    public void setCapCardNo(String capCardNo) {
        this.capCardNo = capCardNo;
    }

    public String getCapCardType() {
        return capCardType;
    }

    public void setCapCardType(String capCardType) {
        this.capCardType = capCardType;
    }

    public String getCapCardName() {
        return capCardName;
    }

    public void setCapCardName(String capCardName) {
        this.capCardName = capCardName;
    }

    public String getWcRemark() {
        return wcRemark;
    }

    public void setWcRemark(String wcRemark) {
        this.wcRemark = wcRemark;
    }

    public String getNtfMbl() {
        return ntfMbl;
    }

    public void setNtfMbl(String ntfMbl) {
        this.ntfMbl = ntfMbl;
    }

    public String getRspOrderNo() {
        return rspOrderNo;
    }

    public void setRspOrderNo(String rspOrderNo) {
        this.rspOrderNo = rspOrderNo;
    }

    public LocalDateTime getRspSuccTm() {
        return rspSuccTm;
    }

    public void setRspSuccTm(LocalDateTime rspSuccTm) {
        this.rspSuccTm = rspSuccTm;
    }

    public String getBusCnl() {
        return busCnl;
    }

    public void setBusCnl(String busCnl) {
        this.busCnl = busCnl;
    }

    public String getFkAcNo() {
        return fkAcNo;
    }

    public void setFkAcNo(String fkAcNo) {
        this.fkAcNo = fkAcNo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}