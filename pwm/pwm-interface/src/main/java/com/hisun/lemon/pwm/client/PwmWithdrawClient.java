package com.hisun.lemon.pwm.client;

import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.pwm.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hisun.lemon.framework.data.GenericDTO;

/**
 * 提现服务接口
 * <AUTHOR>
 * @date 2017年7月31日
 * @time 上午11:06:23
 *
 */
@FeignClient("PWM")
public interface PwmWithdrawClient {

    @PostMapping("/pwm/withdraw/order")
    public GenericRspDTO createOrder(@Validated @RequestBody GenericDTO<WithdrawDTO> genericWithdrawDTO);

    @PatchMapping("/pwm/withdraw/result")
    public GenericRspDTO completeOrder(@Validated @RequestBody GenericDTO<WithdrawResultDTO> withdrawResultDTO);

    @PostMapping("/pwm/withdraw/chk/error/handle")
    public GenericRspDTO withdrawErrorHandler(@Validated @RequestBody GenericDTO<WithdrawErrorHandleDTO> genericWithdrawErrorHandleDTO);

    @PostMapping("/pwm/withdraw/hall/revoke")
    public GenericRspDTO hallWithdrawRevokeHandle(@Validated @RequestBody GenericDTO<HallWithdrawRevokeDTO> genericWithdrawRevokeDTO);

    @PostMapping(value = "/pwm/withdraw/dm/get/order")
    public GenericRspDTO<WithdrawOrderRspDTO> getByOrder(@Validated @RequestBody GenericDTO<GetByOrderDTO> genericDTO);

    @PostMapping(value = "/pwm/withdraw/dm/order")
    public GenericRspDTO<DmWithdrawRspDTO> createDmWithdrawOrder(@Validated @RequestBody GenericDTO<DmWithdrawReqDTO> dmWithdrawReqDTO);

    @PostMapping(value = "/pwm/withdraw/dm/update/order")
    public GenericRspDTO<WithdrawOrderRspDTO> updateOrder(@Validated @RequestBody GenericDTO<UpdateOrderDTO> genericDTO);
}
