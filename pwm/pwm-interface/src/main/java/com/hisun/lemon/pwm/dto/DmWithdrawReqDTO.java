package com.hisun.lemon.pwm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 数币提现请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/26 16:02
 */
@ApiModel(value = "DmWithdrawReqDTO", description = "数币提现请求参数")
public class DmWithdrawReqDTO {

    //用户编号
    @ApiModelProperty(hidden = true)
    private String userId;

    //付款方账号编码
    @ApiModelProperty(name = "fkAcNo", value = "付款方账号编码",required = true)
    @NotBlank(message="付款方账号编码不能为空")
    private String fkAcNo;

    //提现收款地址
    @ApiModelProperty(name = "skAddress", value = "收款地址",required = true)
    @NotEmpty(message="收款地址不能为空")
    private String skAddress;
    //申请提现金额
    @ApiModelProperty(name = "wcApplyAmt", value = "申请提现金额")
    @NotNull(message = "PWM10042")
    @Min(value=0, message="PWM10029")
    private BigDecimal wcApplyAmt;
    //提现手续费
    @ApiModelProperty(name = "feeAmt", value = "提现手续费")
    @Min(value=0, message="PWM10030")

    private BigDecimal feeAmt;
    //币种
    @ApiModelProperty(name = "orderCcy", value = "币种,当前仅允许提现 USDT")
    @NotEmpty(message="币种不能为空")
    private String orderCcy;

    //网络
    @ApiModelProperty(name = "network", value = "网络")
    private String network;


    //    //提现类型
    @ApiModelProperty(name = "wcType", value = "提现类型 11.自主提现")
    @NotEmpty(message="PWM10031")
    private String wcType = "11";
    //付款加急标识
//    @ApiModelProperty(name = "payUrgeFlg", value = "付款加急标识 1.是 0.否")
//    @NotEmpty(message="PWM10032")
//    @Length(max = 1)
//    private String payUrgeFlg;
    //资金合作机构
//    @ApiModelProperty(name = "capCorgNo", value = "资金合作机构")
//    @NotEmpty(message="PWM10033")
//    @Length(max =16)
//    private String capCorgNo;
    //支付密码
    @ApiModelProperty(name = "payPassWord", value = "支付密码")
    @NotEmpty(message="PWM10034")
    private String payPassWord;

    public String getWcType() {
        return wcType;
    }

    public void setWcType(String wcType) {
        this.wcType = wcType;
    }

    //支付密码随机数
//    @ApiModelProperty(name = "payPassWordRand", value = "支付密码随机数")
//    @NotEmpty(message="PWM10054")
//    private String payPassWordRand;
    //订单渠道
    @ApiModelProperty(name = "busCnl", value = "订单渠道(WEB:web站点|APP:APP手机|HALL:营业厅|OTHER:其他渠道)")
    @NotEmpty(message="PWM10002")
    @Length(max = 5)
    private String busCnl;
    //通知手机号
    @ApiModelProperty(name = "ntfMbl", value = "通知手机号")
    @NotEmpty(message="PWM10035")
    @Length(max =20)
    private String ntfMbl;
    //备注
    @ApiModelProperty(name = "wcRemark", value = "备注")
    @Length(max =100)
    private String wcRemark;

    //银行卡户名
//    @ApiModelProperty(name = "cardUserName", value = "银行卡户名")
//    @NotEmpty(message="PWM10047")
//    @Length(max =20)
//    private String cardUserName;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFkAcNo() {
        return fkAcNo;
    }

    public void setFkAcNo(String fkAcNo) {
        this.fkAcNo = fkAcNo;
    }

    public String getSkAddress() {
        return skAddress;
    }

    public void setSkAddress(String skAddress) {
        this.skAddress = skAddress;
    }

    public BigDecimal getWcApplyAmt() {
        return wcApplyAmt;
    }

    public void setWcApplyAmt(BigDecimal wcApplyAmt) {
        this.wcApplyAmt = wcApplyAmt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getOrderCcy() {
        return orderCcy;
    }

    public void setOrderCcy(String orderCcy) {
        this.orderCcy = orderCcy;
    }


    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getPayPassWord() {
        return payPassWord;
    }

    public void setPayPassWord(String payPassWord) {
        this.payPassWord = payPassWord;
    }

    public String getBusCnl() {
        return busCnl;
    }

    public void setBusCnl(String busCnl) {
        this.busCnl = busCnl;
    }

    public String getNtfMbl() {
        return ntfMbl;
    }

    public void setNtfMbl(String ntfMbl) {
        this.ntfMbl = ntfMbl;
    }

    public String getWcRemark() {
        return wcRemark;
    }

    public void setWcRemark(String wcRemark) {
        this.wcRemark = wcRemark;
    }
}
