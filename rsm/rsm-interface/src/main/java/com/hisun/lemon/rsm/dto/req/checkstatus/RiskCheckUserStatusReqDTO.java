package com.hisun.lemon.rsm.dto.req.checkstatus;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;

/**
 * 检查用户状态传输对象
 *
 * <AUTHOR>
 * @create 2017/8/10
 */
@ClientValidated
@ApiModel(value = "RiskCheckUserStatusReqDTO", description = "查询用户状态")
public class RiskCheckUserStatusReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "用户id", required = true)
    @NotEmpty(message = "RSM10041")
    private String id;

    @ApiModelProperty(value = "类型，01-用户号/商户号，02-银行卡，03-身份证", required = true)
    @Pattern(regexp = "01|02|03", message = "RSM10042")
    private String idTyp;

    @ApiModelProperty(value = "交易类型", required = false)
    private String txTyp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    @Override
    public String toString() {
        return "RiskCheckUserStatusReqDTO{" +
                "id='" + id + '\'' +
                ", idTyp='" + idTyp + '\'' +
                ", txTyp='" + txTyp + '\'' +
                '}';
    }
}
