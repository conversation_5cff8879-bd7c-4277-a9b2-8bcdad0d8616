package com.hisun.lemon.rsm.enums;

/**
 * 实时风控交易检测返回码
 *
 * <AUTHOR>
 * @create 2017/7/12
 */
public enum RsmTxCheckCode {

    PASS(0, "检查通过"),
    EXCEPTION(-1, "参数不合法或者内部系统异常"),
    LESS_THAN_MIN_AMT(1, "单笔金额小于最低金额"),
    GREATER_THAN_MAX_AMT(2, "单笔金额大于最大金额"),
    MORE_THAN_DAILY_CUMULATE_AMT(3, "日累计金额超限"),
    MORE_THAN_DAILY_CUMULATE_COUNT(4, "日累计笔数超限"),
    MORE_THAN_MONTHLY_CUMULATE_AMT(5, "月累计金额超限"),
    MORE_THAN_MONTHLY_CUMULATE_COUNT(6, "月累计笔数超限"),
    PASS_BY_WHITE_LIST(7, "存在白名单通过"),
    REFUSE_BY_BLACK_LIST(8, "存在黑名单拒绝"),
    REFUSE_BY_USER_STS_FREEZE(9, "该用户为冻结状态"),
    REFUSE_BY_USER_STS_PAUSE(10, "该用户为暂停状态"),
    MORE_THEN_PASSWORD_LMT(11, "免密支付额度超限"),
    ;
    private int code;
    private String msg;

    RsmTxCheckCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}