package com.hisun.lemon.rsm.dto.req.userstatus;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2017/8/10
 */
@ClientValidated
@ApiModel(value = "UserStatusQueryReqDTO", description = "查询用户状态数据对象")
public class UserStatusQueryReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "用户号", required = true)
    @NotEmpty(message = "RSM10041")
    private String userId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
