package com.hisun.lemon.rsm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.rsm.dto.req.riskparam.*;
import com.hisun.lemon.rsm.dto.res.RiskRuleParamResDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@FeignClient("RSM")
public interface RiskParamMngClient {

    /**
     * 添加风险规则参数
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/rsm/riskparam/param")
    GenericRspDTO<NoBody> addRiskRuleParam(@Validated @RequestBody GenericDTO<RiskRuleParamInsertReqDTO> reqDTO);

    /**
     * 更新风险规则参数
     *
     * @param reqDTO
     * @return
     */
    @PutMapping(value = "/rsm/riskparam/param")
    GenericRspDTO<NoBody> updateRiskRuleParam(@Validated @RequestBody GenericDTO<RiskRuleParamUpdateReqDTO> reqDTO);

    /**
     * 删除风险规则参数
     *
     * @param reqDTO
     * @return
     */
    @DeleteMapping(value = "/rsm/riskparam/param")
    GenericRspDTO<NoBody> deleteRiskRuleParam(@Validated @RequestBody GenericDTO<RiskRuleParamBaseReqDTO> reqDTO);

    /**
     * 查询风险规则参数
     *
     * @param riskRuleParamQueryReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/riskparam/param")
    GenericRspDTO<RiskRuleParamResDTO> queryRiskRuleParam(@Validated RiskRuleParamQueryReqDTO riskRuleParamQueryReqDTO);

    /**
     * 查询风险参数列表
     *
     * @param riskRuleParamQueryAllReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/riskparam/param/list")
    GenericRspDTO<List<RiskRuleParamResDTO>> queryAllRiskRuleParams(@Validated RiskRuleParamQueryAllReqDTO riskRuleParamQueryAllReqDTO);
}
