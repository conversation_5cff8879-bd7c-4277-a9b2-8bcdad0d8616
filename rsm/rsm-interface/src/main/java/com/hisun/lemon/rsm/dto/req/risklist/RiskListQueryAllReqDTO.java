package com.hisun.lemon.rsm.dto.req.risklist;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Pattern;

/**
 * 查询黑白名单列表传输对象
 *
 * <AUTHOR>
 * @create 2017/8/3
 */
@ClientValidated
@ApiModel(value = "RiskListQueryAllReqDTO", description = "查询黑白名单列表传输对象")
public class RiskListQueryAllReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "名单类型", required = true)
    @Pattern(regexp = "0|1", message = "RSM10002")
    private String listTyp;

    @ApiModelProperty(value = "页码数", required = false)
    private Integer pageNum;

    @ApiModelProperty(value = "页面展示数", required = false)
    private Integer pageSize;

    public String getListTyp() {
        return listTyp;
    }

    public void setListTyp(String listTyp) {
        this.listTyp = listTyp;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
