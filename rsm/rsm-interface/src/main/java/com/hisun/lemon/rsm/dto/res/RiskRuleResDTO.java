package com.hisun.lemon.rsm.dto.res;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 风控规则传输对象
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
public class RiskRuleResDTO {
    private String ruleId;
    private String ruleNm;
    private String ruleDesc;
    private String ruleTyp;
    private String dcPtyFlg;
    private String cpnNm;
    private String ruleEffFlg;
    private String updOprId;
    private LocalDateTime createTime;
    private LocalDateTime modifyTime;

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleNm() {
        return ruleNm;
    }

    public void setRuleNm(String ruleNm) {
        this.ruleNm = ruleNm;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getRuleTyp() {
        return ruleTyp;
    }

    public void setRuleTyp(String ruleTyp) {
        this.ruleTyp = ruleTyp;
    }

    public String getDcPtyFlg() {
        return dcPtyFlg;
    }

    public void setDcPtyFlg(String dcPtyFlg) {
        this.dcPtyFlg = dcPtyFlg;
    }

    public String getCpnNm() {
        return cpnNm;
    }

    public void setCpnNm(String cpnNm) {
        this.cpnNm = cpnNm;
    }

    public String getRuleEffFlg() {
        return ruleEffFlg;
    }

    public void setRuleEffFlg(String ruleEffFlg) {
        this.ruleEffFlg = ruleEffFlg;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RiskRuleResDTO{" +
                "ruleId='" + ruleId + '\'' +
                ", ruleNm='" + ruleNm + '\'' +
                ", ruleDesc='" + ruleDesc + '\'' +
                ", ruleTyp='" + ruleTyp + '\'' +
                ", dcPtyFlg='" + dcPtyFlg + '\'' +
                ", cpnNm='" + cpnNm + '\'' +
                ", ruleEffFlg='" + ruleEffFlg + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}

