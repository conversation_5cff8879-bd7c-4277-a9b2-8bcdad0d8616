package com.hisun.lemon.rsm.dto.req.limitparam;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Pattern;

/**
 * 查询风控限额参数列表传输对象
 *
 * <AUTHOR>
 * @create 2017/8/3
 */
@ClientValidated
@ApiModel(value = "TxRuleParamQueryAllReqDTO", description = "查询风控限额参数列表传输对象")
public class TxRuleParamQueryAllReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "页码数", required = false)
    private Integer pageNum;

    @ApiModelProperty(value = "页面展示数", required = false)
    private Integer pageSize;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
