package com.hisun.lemon.rsm.dto.req.risklist;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;

/**
 * 黑白名单查询请求对象
 *
 * <AUTHOR>
 * @create 2017/8/3
 */
@ClientValidated
@ApiModel(value = "RiskQueryReqDTO", description = "黑白名单传输对象")
public class RiskListQueryReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "黑白名单id", required = true)
    @NotEmpty(message = "RSM10001")
    private String id;

    @ApiModelProperty(value = "名单类型", required = true)
    @Pattern(regexp = "0|1", message = "RSM10002")
    private String listTyp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getListTyp() {
        return listTyp;
    }

    public void setListTyp(String listTyp) {
        this.listTyp = listTyp;
    }
}
