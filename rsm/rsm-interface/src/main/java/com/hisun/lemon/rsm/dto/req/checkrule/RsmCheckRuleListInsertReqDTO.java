package com.hisun.lemon.rsm.dto.req.checkrule;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 风控检查规则
 *
 * <AUTHOR>
 * @create 2017/8/4
 */
@ClientValidated
@ApiModel(value = "RsmCheckRuleListInsertReqDTO", description = "新增风控检查规则对象")
public class RsmCheckRuleListInsertReqDTO {

    @ApiModelProperty(value = "交易类型", required = true)
    @NotEmpty(message = "RSM10005")
    private String txTyp;

    @ApiModelProperty(value = "限制级别", required = true)
    @Pattern(regexp = "1", message = "RSM10038")
    private String lmtLvl;

    @ApiModelProperty(value = "规则描述即风控规则列表，以|分隔", required = true)
    private String ruleDesc;

    @ApiModelProperty(value = "操作类型", required = true)
    @Pattern(regexp = "1", message = "RSM10039")
    private String oprTyp;

    @ApiModelProperty(value = "操作员id", required = true)
    @NotEmpty(message = "RSM10009")
    private String updOprId;

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getLmtLvl() {
        return lmtLvl;
    }

    public void setLmtLvl(String lmtLvl) {
        this.lmtLvl = lmtLvl;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public String toString() {
        return "RsmCheckRuleListDo{" +
                ", txTyp='" + txTyp + '\'' +
                ", lmtLvl='" + lmtLvl + '\'' +
                ", ruleDesc='" + ruleDesc + '\'' +
                ", oprTyp='" + oprTyp + '\'' +
                ", updOprId='" + updOprId + '\'' +
                '}';
    }
}
