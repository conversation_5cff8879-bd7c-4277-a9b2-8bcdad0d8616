package com.hisun.lemon.rsm.dto.req.userstatus;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 用户状态数据对象（暂停，冻结）
 *
 * <AUTHOR>
 * @create 2017/8/10
 */
@ClientValidated
@ApiModel(value = "UserStatusQueryAllReqDTO", description = "查询用户状态数据列表")
public class UserStatusQueryAllReqDTO {

    @ApiModelProperty(value = "用户状态", required = false)
    @NotEmpty(message = "RSM10044")
    private String userSts;

    @ApiModelProperty(value = "页码数", required = false)
    private int pageNum;

    @ApiModelProperty(value = "页面展示数", required = false)
    private int pageSize;

    public String getUserSts() {
        return userSts;
    }

    public void setUserSts(String userSts) {
        this.userSts = userSts;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
