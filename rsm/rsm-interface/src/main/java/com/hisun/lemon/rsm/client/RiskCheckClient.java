package com.hisun.lemon.rsm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 风控检测接口
 *
 * <AUTHOR>
 * @create 2017/7/12
 */
@FeignClient("RSM")
public interface RiskCheckClient {

    /**
     * 实时风控检查
     *
     * @return
     */
    @PostMapping("/rsm/realtimerisk/check")
    GenericRspDTO<NoBody> riskControl(@Validated @RequestBody JrnReqDTO jrnReqDTO);

    /**
     * 批量检查（适用于多种支付方式）
     *
     * @param listDto
     * @return
     */
    @PostMapping("/rsm/notrealtimerisk/batchcheck")
    GenericRspDTO<Map<String, String>> batchCheck(@Validated @RequestBody List<JrnReqDTO> listDto);

    /**
     * 交易额度累计
     */
    @PostMapping("/rsm/notrealtimerisk/accumulateamount")
    GenericRspDTO<NoBody> accumulateAmount(@Validated @RequestBody GenericDTO<JrnReqDTO> genericDTO);

    /**
     * 批量累计（适用于多种支付方式）
     *
     * @param genericDTO
     * @return
     */
    @PostMapping("/rsm/notrealtimerisk/batchaccumulation")
    GenericRspDTO<NoBody> batchAccumulation(@Validated @RequestBody GenericDTO<List<JrnReqDTO>> genericDTO);

    /**
     * 检查用户状态
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/rsm/realtimerisk/status")
    GenericRspDTO<NoBody> checkUserStatus(@Validated @RequestBody RiskCheckUserStatusReqDTO reqDTO);
}
