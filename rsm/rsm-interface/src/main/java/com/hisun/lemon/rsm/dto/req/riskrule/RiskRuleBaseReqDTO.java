package com.hisun.lemon.rsm.dto.req.riskrule;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@ClientValidated
@ApiModel(value = "RiskRuleBaseReqDTO", description = "查询风控规则传输对象")
public class RiskRuleBaseReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "风控id", required = true)
    @NotEmpty(message = "RSM10012")
    private String ruleId;

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }
}
