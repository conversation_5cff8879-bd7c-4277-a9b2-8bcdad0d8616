package com.hisun.lemon.rsm.dto.req.riskrule;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@ClientValidated
@ApiModel(value = "RiskRuleQueryAllReqDTO", description = "查询风控规则列表传输对象")
public class RiskRuleQueryAllReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "页码数", required = false)
    private Integer pageNum;

    @ApiModelProperty(value = "页面展示数", required = false)
    private Integer pageSize;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
