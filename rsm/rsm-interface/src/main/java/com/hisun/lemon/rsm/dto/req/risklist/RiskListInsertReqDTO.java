package com.hisun.lemon.rsm.dto.req.risklist;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 黑白名单传输对象
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
@ClientValidated
@ApiModel(value = "RiskListInsertReqDTO", description = "黑白名单传输对象")
public class RiskListInsertReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "名单类型", required = true)
    @Pattern(regexp = "0|1", message = "RSM10002")
    private String listTyp;

    @ApiModelProperty(value = "名单类型", required = true)
    @Pattern(regexp = "01|02|03|04", message = "RSM10003")
    private String idTyp;

    @ApiModelProperty(value = "用户id", required = true)
    @NotEmpty(message = "RSM10004")
    private String id;

    @ApiModelProperty(value = "交易类型", required = true)
    @NotEmpty(message = "RSM10005")
    private String txTyp;

    @ApiModelProperty(value = "添加原因", required = true)
    @NotEmpty(message = "RSM10006")
    private String listRsn;

    @ApiModelProperty(value = "生效时间", required = true)
    private LocalDateTime beginDt;

    @ApiModelProperty(value = "失效时间", required = true)
    private LocalDateTime endDt;

    @ApiModelProperty(value = "生效标志", required = true)
    @Pattern(regexp = "0|1", message = "RSM10007")
    private String effFlg;

    @ApiModelProperty(value = "名单来源", required = true)
    @NotEmpty(message = "RSM10008")
    private String listSorc;

    @ApiModelProperty(value = "操作员id", required = true)
    @NotEmpty(message = "RSM10009")
    private String updOprId;

    public String getListTyp() {
        return listTyp;
    }

    public void setListTyp(String listTyp) {
        this.listTyp = listTyp;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getListRsn() {
        return listRsn;
    }

    public void setListRsn(String listRsn) {
        this.listRsn = listRsn;
    }

    public LocalDateTime getBeginDt() {
        return beginDt;
    }

    public void setBeginDt(LocalDateTime beginDt) {
        this.beginDt = beginDt;
    }

    public LocalDateTime getEndDt() {
        return endDt;
    }

    public void setEndDt(LocalDateTime endDt) {
        this.endDt = endDt;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getListSorc() {
        return listSorc;
    }

    public void setListSorc(String listSorc) {
        this.listSorc = listSorc;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public String toString() {
        return "RiskListDTO{" +
                "listTyp='" + listTyp + '\'' +
                ", idTyp='" + idTyp + '\'' +
                ", id='" + id + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", listRsn='" + listRsn + '\'' +
                ", beginDt=" + beginDt +
                ", endDt=" + endDt +
                ", effFlg='" + effFlg + '\'' +
                ", listSorc='" + listSorc + '\'' +
                ", updOprId='" + updOprId + '\'' +
                '}';
    }
}
