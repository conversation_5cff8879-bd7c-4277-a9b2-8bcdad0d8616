package com.hisun.lemon.rsm.dto.req.riskparam;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @create 2017/7/7
 */
@ClientValidated
@ApiModel(value = "RiskRuleParamInsertReqDTO", description = "风险交易规则参数对象")
public class RiskRuleParamInsertReqDTO extends GenericDTO<NoBody> {

    private String riskDesc;
    private String cpnNm;
    private String parm1;
    private String parm2;
    private String parm3;
    private String parm4;
    private String parm5;
    private String parm6;
    private String parm7;
    private String parm8;
    private String updOprId;

    public String getRiskDesc() {
        return riskDesc;
    }

    public void setRiskDesc(String riskDesc) {
        this.riskDesc = riskDesc;
    }

    public String getCpnNm() {
        return cpnNm;
    }

    public void setCpnNm(String cpn_nm) {
        this.cpnNm = cpn_nm;
    }

    public String getParm1() {
        return parm1;
    }

    public void setParm1(String parm1) {
        this.parm1 = parm1;
    }

    public String getParm2() {
        return parm2;
    }

    public void setParm2(String parm2) {
        this.parm2 = parm2;
    }

    public String getParm3() {
        return parm3;
    }

    public void setParm3(String parm3) {
        this.parm3 = parm3;
    }

    public String getParm4() {
        return parm4;
    }

    public void setParm4(String parm4) {
        this.parm4 = parm4;
    }

    public String getParm5() {
        return parm5;
    }

    public void setParm5(String parm5) {
        this.parm5 = parm5;
    }

    public String getParm6() {
        return parm6;
    }

    public void setParm6(String parm6) {
        this.parm6 = parm6;
    }

    public String getParm7() {
        return parm7;
    }

    public void setParm7(String parm7) {
        this.parm7 = parm7;
    }

    public String getParm8() {
        return parm8;
    }

    public void setParm8(String parm8) {
        this.parm8 = parm8;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }
}
