package com.hisun.lemon.rsm.dto.req.riskJrn;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 交易风控流水
 *
 * <AUTHOR>
 * @create 2017/7/11
 */
public class RsmJrnReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "收方id", required = false)
    private String stlUserId;

    @ApiModelProperty(value = "收方用户类型", required = false)
    private String stlUserTyp;

    @ApiModelProperty(value = "付方id", required = false)
    private String payUserId;

    @ApiModelProperty(value = "付方用户类型", required = false)
    private String payUserTyp;

    @ApiModelProperty(value = "付方银行卡号（快捷支付时输入，其余支付方式无视）", required = false)
    private String payCrdNo;

    @ApiModelProperty(value = "交易类型", required = false)
    private String txTyp;

    @ApiModelProperty(value = "交易状态", required = true)
    @NotEmpty(message = "RSM10022")
    private String txSts;

    @ApiModelProperty(value = "交易渠道", required = true)
    @NotEmpty(message = "RSM10023")
    private String txCnl;

    @ApiModelProperty(value = "交易金额", required = false)
    private BigDecimal txAmt;

    @ApiModelProperty(value = "交易币种", required = false)
    private String ccy;

    @ApiModelProperty(value = "支付类型", required = false)
    private String payTyp;

    @ApiModelProperty(value = "交易日期", required = true)
    @NotNull(message = "RSM10028")
    private LocalDate txDt;

    @ApiModelProperty(value = "交易时间", required = true)
    @NotNull(message = "RSM10029")
    private LocalTime txTm;

    @ApiModelProperty(value = "原交易流水号", required = true)
    @NotEmpty(message = "RSM10030")
    private String txJrnNo;

    @ApiModelProperty(value = "原交易订单号", required = true)
    @NotEmpty(message = "RSM10031")
    private String txOrdNo;

    @ApiModelProperty(value = "交易数据", required = false)
    private String txData;

    public String getStlUserId() {
        return stlUserId;
    }

    public void setStlUserId(String stlUserId) {
        this.stlUserId = stlUserId;
    }

    public String getStlUserTyp() {
        return stlUserTyp;
    }

    public void setStlUserTyp(String stlUserTyp) {
        this.stlUserTyp = stlUserTyp;
    }

    public String getPayUserId() {
        return payUserId;
    }

    public void setPayUserId(String payUserId) {
        this.payUserId = payUserId;
    }

    public String getPayUserTyp() {
        return payUserTyp;
    }

    public void setPayUserTyp(String payUserTyp) {
        this.payUserTyp = payUserTyp;
    }

    public String getPayCrdNo() {
        return payCrdNo;
    }

    public void setPayCrdNo(String payCrdNo) {
        this.payCrdNo = payCrdNo;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getTxSts() {
        return txSts;
    }

    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    public String getTxCnl() {
        return txCnl;
    }

    public void setTxCnl(String txCnl) {
        this.txCnl = txCnl;
    }

    public BigDecimal getTxAmt() {
        return txAmt;
    }

    public void setTxAmt(BigDecimal txAmt) {
        this.txAmt = txAmt;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getPayTyp() {
        return payTyp;
    }

    public void setPayTyp(String payTyp) {
        this.payTyp = payTyp;
    }

    public LocalDate getTxDt() {
        return txDt;
    }

    public void setTxDt(LocalDate txDt) {
        this.txDt = txDt;
    }

    public LocalTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalTime txTm) {
        this.txTm = txTm;
    }

    public String getTxJrnNo() {
        return txJrnNo;
    }

    public void setTxJrnNo(String txJrnNo) {
        this.txJrnNo = txJrnNo;
    }

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public String getTxData() {
        return txData;
    }

    public void setTxData(String txData) {
        this.txData = txData;
    }

    @Override
    public String toString() {
        return "RsmJrnReqDTO{" +
                "stlUserId='" + stlUserId + '\'' +
                ", stlUserTyp='" + stlUserTyp + '\'' +
                ", payUserId='" + payUserId + '\'' +
                ", payUserTyp='" + payUserTyp + '\'' +
                ", payCrdNo='" + payCrdNo + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", txSts='" + txSts + '\'' +
                ", txCnl='" + txCnl + '\'' +
                ", txAmt=" + txAmt +
                ", ccy='" + ccy + '\'' +
                ", payTyp='" + payTyp + '\'' +
                ", txDt=" + txDt +
                ", txTm=" + txTm +
                ", txJrnNo='" + txJrnNo + '\'' +
                ", txOrdNo='" + txOrdNo + '\'' +
                ", txData='" + txData + '\'' +
                '}';
    }
}
