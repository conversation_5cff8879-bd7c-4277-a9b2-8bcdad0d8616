package com.hisun.lemon.rsm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.rsm.dto.req.userstatus.*;
import com.hisun.lemon.rsm.dto.res.UserStatusResDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户状态
 *
 * <AUTHOR>
 * @create 2017/8/10
 */
@FeignClient("RSM")
public interface RiskCheckUserStatusClient {

    /**
     * 添加记录
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/rsm/user/status")
    GenericRspDTO<NoBody> addRecord(@Validated @RequestBody GenericDTO<UserStatusInsertReqDTO> reqDTO);

    /**
     * 删除记录
     *
     * @param reqDTO
     * @return
     */
    @DeleteMapping(value = "/status")
    GenericRspDTO<NoBody> deleteRecord(@Validated @RequestBody GenericDTO<UserStatusDeleteReqDTO> reqDTO);

    /**
     * 更新记录
     *
     * @param reqDTO
     * @return
     */
    @PutMapping(value = "/status")
    GenericRspDTO<NoBody> updateRecord(@Validated @RequestBody GenericDTO<UserStatusUpdateReqDTO> reqDTO);

    /**
     * 查询记录
     *
     * @param userStatusQueryReqDTO
     * @return
     */
    @GetMapping(value = "/status")
    GenericRspDTO<UserStatusResDTO> queryRecord(@Validated UserStatusQueryReqDTO userStatusQueryReqDTO);

    /**
     * 查询记录列表
     *
     * @param reqDTO
     * @return
     */
    @GetMapping(value = "/status/list")
    GenericRspDTO<List<UserStatusResDTO>> queryAllRecords(@Validated UserStatusQueryAllReqDTO reqDTO);


}
