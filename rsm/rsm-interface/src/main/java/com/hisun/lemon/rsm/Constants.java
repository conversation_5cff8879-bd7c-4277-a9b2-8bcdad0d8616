package com.hisun.lemon.rsm;

/**
 * 常量
 *
 * <AUTHOR>
 * @create 2017/7/10
 */
public class Constants {

    public static final boolean SUCCESS = true;
    public static final boolean FAILURE = false;

    /**
     * ID类型
     */
    public static final String ID_TYP_USER_NO = "01";   //用户号/商户号
    public static final String ID_TYP_CARD = "02";      //银行卡号
    public static final String ID_TYP_ID_NO = "03";     //身份证号
    public static final String ID_TYP_MBL_NO = "04";    //手机号

    /**
     * 收付款标志
     */
    public static final String DC_PTY_FLG_ALL = "0";    //全部
    public static final String DC_PTY_FLG_STL = "1";    //收款方
    public static final String DC_PTY_FLG_PAY = "2";    //付款方

    /**
     * 生效标志
     */
    public static final String EFF_FLG_EFF = "0";         //生效
    public static final String EFF_FLG_EXP = "1";         //失效

    /**
     * 账户类型
     */
    public static final String AC_TYP_CRP = "800";      //商户
    public static final String AC_TYP_PSN = "100";      //个人

    /**
     * 支付方式
     */
    public static final String PAY_TYP_ALL = "00";      //全部
    public static final String PAY_TYP_ACCOUNT = "01";  //账户余额
    public static final String PAY_TYP_QUICK_PAY = "02";//快捷支付
    public static final String PAY_TYP_SEATEL = "03";   //海币
    public static final String PAY_TYP_WECHAT = "04";   //微信支付
    public static final String PAY_TYP_ALIPAY = "05";   //支付宝
    public static final String PAY_TYP_BESTPAY = "06";  //翼支付
    public static final String PAY_TYP_OFFLINE = "07";  //线下汇款

    /**
     * 风控类型 新
     */
    public static final String RULE_TYP_CNT = "01";     //频次
    public static final String RULE_TYP_AMT = "02";     //额度
    public static final String RULE_TYP_CNL = "03";     //渠道

    /**
     * 交易状态
     */
    public static final String TX_STS_ALL = "*";          //全部
    public static final String TX_STS_SUCCESS = "S";      //成功
    public static final String TX_STS_FAILURE = "F";      //失败

    /**
     * 收付款用户类型
     */
    public static final String STL_PAY_USER_TYP_CRP = "01";     //商户ID
    public static final String STL_PAY_USER_TYP_PSN = "02";     //用户ID
    public static final String STL_PAY_USER_TYP_CARD = "03";    //银行卡号

    /**
     * 风控模块表名
     */
    public static final String RSM_TABLE_RISK_LIST = "Black or White list";
    public static final String RSM_TABLE_RULE = "Risk rule info";
    public static final String RSM_TABLE_TX_PARM = "Risk rule limit parameters";
    public static final String RSM_TABLE_RISK_PARM = "Risk rule parameters";
    public static final String RSM_TABLE_JRN = "Risk record";
    public static final String RSM_TABLE_CHECK_RULE = "Risk check rule info";
    public static final String RSM_TABLE_USER_STATUS = "User status";

    /**
     * 交易方式
     */
    public static final String TX_TYP_ALL = "00";            //全项
    public static final String TX_TYP_RECHARGE = "01";      //充值
    public static final String TX_TYP_CONSUME = "02";       //消费
    public static final String TX_TYP_TRANSFER = "03";      //转账
    public static final String TX_TYP_WITHDRAW = "04";      //提现
    public static final String TX_TYP_SEATEL = "05";        //充值海币
    public static final String TX_TYP_REFUNDS = "06";       //退款
    public static final String TX_TYP_INTEREST = "07";      //理财
    public static final String TX_TYP_PAYMENT = "08";       //缴费
    public static final String TX_TYP_DM_WITHDRAW = "DX";      //数币提现

    /**
     * 资金流向
     */
    public static final String CAP_FLW_IN = "0";            //流入
    public static final String CAP_FLW_OUT = "1";           //流出
    public static final String CAP_FLW_ALL = null;          //全部

    /**
     * 卡类型
     */
    public static final String CRD_TYP_CREDIT_CARD = "C";   //贷记卡
    public static final String CRD_TYP_DEBIT_CARD = "D";    //借记卡

    /**
     * 默认页面数页面展示数
     */
    public static final int DEFAULT_PAGE_NUMBER = 0;
    public static final int DEFAULT_PAGE_SIZE = 5;

    /**
     * 用户状态检查
     */
    public static final String USER_STS_PASS = "0";     //通过
    public static final String USER_STS_FREEZE = "1";   //冻结
    public static final String USER_STS_PAUSE = "2";    //暂停

    /**
     * 免密支付
     */
    public static final String PSW_FLG_ENABLE = "1";    //打开免密

    /**
     * 限制级别
     */
    public static final String LIMIT_LEVEL_USER = "0";  //用户级别

    /**
     * 检查规则操作类型
     */
    public static final String OPERATION_REFUSED = "1"; //操作拒绝

    /**
     * 可疑操作
     */
    public static final String RISK_OPR_AMT = "1";      //大额交易
    public static final String RISK_OPR_CNT = "2";      //频繁交易
    public static final String RISK_OPR_SPS = "3";      //可疑交易

    /**
     * 风险事件处理状态
     */
    public static final String OPR_STS_RISK = "1";      //风险事件

    /**
     * 风险来源
     */
    public static final String RISK_SORC = "RSM";       //风险来源
}
