package com.hisun.lemon.rsm.dto.req.limitparam;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@ClientValidated
@ApiModel(value = "TxRuleParamBaseReqDTO", description = "查询交易风控限额参数对象")
public class TxRuleParamQueryReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "限额参数id", required = true)
    @NotEmpty(message = "RSM10037")
    private String parmId;

    public String getParmId() {
        return parmId;
    }

    public void setParmId(String parmId) {
        this.parmId = parmId;
    }
}
