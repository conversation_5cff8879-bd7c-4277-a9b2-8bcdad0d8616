package com.hisun.lemon.rsm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.rsm.dto.req.checkrule.*;
import com.hisun.lemon.rsm.dto.req.limitparam.*;
import com.hisun.lemon.rsm.dto.req.riskrule.RiskRuleBaseReqDTO;
import com.hisun.lemon.rsm.dto.req.riskrule.RiskRuleInsertReqDTO;
import com.hisun.lemon.rsm.dto.req.riskrule.RiskRuleQueryAllReqDTO;
import com.hisun.lemon.rsm.dto.req.riskrule.RiskRuleUpdateReqDTO;
import com.hisun.lemon.rsm.dto.res.RiskRuleResDTO;
import com.hisun.lemon.rsm.dto.res.RsmCheckRuleListResDTO;
import com.hisun.lemon.rsm.dto.res.TxRuleParamResDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @create 2017/7/11
 */
@FeignClient("RSM")
public interface RiskRuleMngClient {

    /**
     * 添加风控规则
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/rsm/riskrulemng/rules")
    GenericRspDTO<NoBody> addRiskRule(@Validated @RequestBody GenericDTO<RiskRuleInsertReqDTO> reqDTO);

    /**
     * 更新风控规则
     *
     * @param reqDTO
     * @return
     */
    @PutMapping(value = "/rsm/riskrulemng/rules")
    GenericRspDTO<NoBody> updateRiskRule(@Validated @RequestBody GenericDTO<RiskRuleUpdateReqDTO> reqDTO);

    /**
     * 查询风控规则
     *
     * @param riskRuleBaseReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/riskrulemng/rules")
    GenericRspDTO<RiskRuleResDTO> queryRiskRule(@Validated RiskRuleBaseReqDTO riskRuleBaseReqDTO);

    /**
     * 查询风控规则列表
     *
     * @param riskRuleQueryAllReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/riskrulemng/rules/list")
    GenericRspDTO<List<RiskRuleResDTO>> queryAllRiskRules(@Validated RiskRuleQueryAllReqDTO riskRuleQueryAllReqDTO);

    /**
     * 添加风控规则限额参数
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/rsm/riskrulemng/limitparam")
    GenericRspDTO<NoBody> addTxRuleParam(@Validated @RequestBody GenericDTO<TxRuleParamInsertReqDTO> reqDTO);

    /**
     * 更新风控规则限额参数
     *
     * @param reqDTO
     * @return
     */
    @PutMapping(value = "/rsm/riskrulemng/limitparam")
    GenericRspDTO<NoBody> updateTxRuleParam(@Validated @RequestBody GenericDTO<TxRuleParamUpdateReqDTO> reqDTO);

    /**
     * 删除风控规则限额参数
     *
     * @param reqDTO
     * @return
     */
    @DeleteMapping(value = "/rsm/riskrulemng/limitparam")
    GenericRspDTO<NoBody> deleteTxRuleParam(@Validated @RequestBody GenericDTO<TxRuleParamBaseReqDTO> reqDTO);

    /**
     * 查询风控规则限额参数
     *
     * @param txRuleParamQueryReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/riskrulemng/limitparam")
    GenericRspDTO<TxRuleParamResDTO> queryTxRuleParam(@Validated TxRuleParamQueryReqDTO txRuleParamQueryReqDTO);

    /**
     * 查询风控规则限额参数列表
     *
     * @param txRuleParamQueryAllReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/riskrulemng/limitparam/list")
    GenericRspDTO<List<TxRuleParamResDTO>> queryAllTxRuleParams(@Validated TxRuleParamQueryAllReqDTO txRuleParamQueryAllReqDTO);

    /**
     * 添加风控检查规则
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/rsm/riskrulemng/checkrules")
    GenericRspDTO<NoBody> addCheckRule(@Validated @RequestBody GenericDTO<RsmCheckRuleListInsertReqDTO> reqDTO);

    /**
     * 删除风控检查规则
     *
     * @param reqDTO
     * @return
     */
    @DeleteMapping(value = "/rsm/riskrulemng/checkrules")
    GenericRspDTO<NoBody> deleteCheckRule(@Validated @RequestBody GenericDTO<RsmCheckRuleListBaseReqDTO> reqDTO);

    /**
     * 查询风控检查规则
     *
     * @param reqDTO
     * @return
     */
    @GetMapping(value = "/rsm/riskrulemng/checkrules")
    GenericRspDTO<RsmCheckRuleListResDTO> queryCheckRule(@Validated RsmCheckRuleListQueryReqDTO reqDTO);

    /**
     * 更新风控检查规则
     *
     * @param reqDTO
     * @return
     */
    @PutMapping(value = "/rsm/riskrulemng/checkrules")
    GenericRspDTO<NoBody> updateRiskList(@Validated @RequestBody GenericDTO<RsmCheckRuleListUpdateReqDTO> reqDTO);

    /**
     * 查询风控检查规则列表
     *
     * @param rsmCheckRuleListQueryAllReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/riskrulemng/checkrules/list")
    GenericRspDTO<List<RsmCheckRuleListResDTO>> queryAllCheckRules(@Validated RsmCheckRuleListQueryAllReqDTO rsmCheckRuleListQueryAllReqDTO);
}
