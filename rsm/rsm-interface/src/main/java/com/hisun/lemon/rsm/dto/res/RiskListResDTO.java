package com.hisun.lemon.rsm.dto.res;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 黑白名单传输对象
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
public class RiskListResDTO {
    private String listId;
    private String listTyp;
    private String idTyp;
    private String Id;
    private String IdHid;
    private String crdNoLast;
    private String txTyp;
    private String listRsn;
    private LocalDateTime beginDt;
    private LocalDateTime endDt;
    private String effFlg;
    private String listSorc;
    private String updOprId;
    private LocalDateTime createTime;
    private LocalDateTime modifyTime;

    public String getListId() {
        return listId;
    }

    public void setListId(String listId) {
        this.listId = listId;
    }

    public String getListTyp() {
        return listTyp;
    }

    public void setListTyp(String listTyp) {
        this.listTyp = listTyp;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getId() {
        return Id;
    }

    public void setId(String id) {
        Id = id;
    }

    public String getIdHid() {
        return IdHid;
    }

    public void setIdHid(String idHid) {
        IdHid = idHid;
    }

    public String getCrdNoLast() {
        return crdNoLast;
    }

    public void setCrdNoLast(String crdNoLast) {
        this.crdNoLast = crdNoLast;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getListRsn() {
        return listRsn;
    }

    public void setListRsn(String listRsn) {
        this.listRsn = listRsn;
    }

    public LocalDateTime getBeginDt() {
        return beginDt;
    }

    public void setBeginDt(LocalDateTime beginDt) {
        this.beginDt = beginDt;
    }

    public LocalDateTime getEndDt() {
        return endDt;
    }

    public void setEndDt(LocalDateTime endDt) {
        this.endDt = endDt;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getListSorc() {
        return listSorc;
    }

    public void setListSorc(String listSorc) {
        this.listSorc = listSorc;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RiskListResDTO{" +
                "listId='" + listId + '\'' +
                ", listTyp='" + listTyp + '\'' +
                ", idTyp='" + idTyp + '\'' +
                ", Id='" + Id + '\'' +
                ", IdHid='" + IdHid + '\'' +
                ", crdNoLast='" + crdNoLast + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", listRsn='" + listRsn + '\'' +
                ", beginDt=" + beginDt +
                ", endDt=" + endDt +
                ", effFlg='" + effFlg + '\'' +
                ", listSorc='" + listSorc + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
