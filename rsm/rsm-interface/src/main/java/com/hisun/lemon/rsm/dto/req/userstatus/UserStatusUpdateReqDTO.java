package com.hisun.lemon.rsm.dto.req.userstatus;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 用户状态数据对象（暂停，冻结）
 *
 * <AUTHOR>
 * @create 2017/8/10
 */
@ClientValidated
@ApiModel(value = "UserStatusUpdateReqDTO", description = "修改用户状态数据对象")
public class UserStatusUpdateReqDTO {

    @ApiModelProperty(value = "用户号", required = true)
    @NotEmpty(message = "RSM10041")
    private String userId;

    @ApiModelProperty(value = "用户状态", required = true)
    @NotEmpty(message = "RSM10044")
    private String userSts;

    @ApiModelProperty(value = "备注", required = true)
    private String rmk;

    @ApiModelProperty(value = "操作员", required = true)
    @NotEmpty(message = "RSM10009")
    private String updOprId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserSts() {
        return userSts;
    }

    public void setUserSts(String userSts) {
        this.userSts = userSts;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }
}
