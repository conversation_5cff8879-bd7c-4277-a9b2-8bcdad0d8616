package com.hisun.lemon.rsm.dto.req.userstatus;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 用户状态数据对象（暂停，冻结）
 *
 * <AUTHOR>
 * @create 2017/8/10
 */
@ClientValidated
@ApiModel(value = "UserStatusInsertReqDTO", description = "新增用户状态数据对象")
public class UserStatusInsertReqDTO {

    @ApiModelProperty(value = "手机号", required = true)
    @NotEmpty(message = "RSM10043")
    private String mblNo;

    @ApiModelProperty(value = "用户状态", required = true)
    @NotEmpty(message = "RSM10044")
    private String userSts;

    @ApiModelProperty(value = "备注", required = true)
    private String rmk;

    @ApiModelProperty(value = "操作员", required = true)
    @NotEmpty(message = "RSM10009")
    private String updOprId;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getUserSts() {
        return userSts;
    }

    public void setUserSts(String userSts) {
        this.userSts = userSts;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }
}
