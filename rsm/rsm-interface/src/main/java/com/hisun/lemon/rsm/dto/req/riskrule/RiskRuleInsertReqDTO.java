package com.hisun.lemon.rsm.dto.req.riskrule;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;

/**
 * 风控规则传输对象
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
@ClientValidated
@ApiModel(value = "RiskRuleInsertReqDTO", description = "新增风控规则传输对象")
public class RiskRuleInsertReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "风控规则名", required = true)
    @NotEmpty(message = "RSM10019")
    private String ruleNm;

    @ApiModelProperty(value = "风控描述", required = true)
    @NotEmpty(message = "RSM10021")
    private String ruleDesc;

    @ApiModelProperty(value = "风控类型，01-频次，02-额度，03-渠道", required = true)
    @Pattern(regexp = "01|02|03", message = "RSM10020")
    private String ruleTyp;

    @ApiModelProperty(value = "收付款标志，0-全部，1-收方，2-付方", required = true)
    @Pattern(regexp = "0|1|2", message = "RSM10024")
    private String dcPtyFlg;

    @ApiModelProperty(value = "组件名称", required = false)
    private String cpnNm;

    @ApiModelProperty(value = "操作员id", required = true)
    @NotEmpty(message = "RSM10009")
    private String updOprId;

    public String getRuleNm() {
        return ruleNm;
    }

    public void setRuleNm(String ruleNm) {
        this.ruleNm = ruleNm;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getRuleTyp() {
        return ruleTyp;
    }

    public void setRuleTyp(String ruleTyp) {
        this.ruleTyp = ruleTyp;
    }

    public String getDcPtyFlg() {
        return dcPtyFlg;
    }

    public void setDcPtyFlg(String dcPtyFlg) {
        this.dcPtyFlg = dcPtyFlg;
    }

    public String getCpnNm() {
        return cpnNm;
    }

    public void setCpnNm(String cpnNm) {
        this.cpnNm = cpnNm;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public String toString() {
        return "RiskRuleInsertReqDTO{" +
                "ruleNm='" + ruleNm + '\'' +
                ", ruleDesc='" + ruleDesc + '\'' +
                ", ruleTyp='" + ruleTyp + '\'' +
                ", dcPtyFlg='" + dcPtyFlg + '\'' +
                ", cpnNm='" + cpnNm + '\'' +
                ", updOprId='" + updOprId + '\'' +
                '}';
    }
}

