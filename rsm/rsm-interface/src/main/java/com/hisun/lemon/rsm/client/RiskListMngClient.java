package com.hisun.lemon.rsm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.rsm.dto.req.risklist.*;
import com.hisun.lemon.rsm.dto.res.RiskListResDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/11
 */
@FeignClient("RSM")
public interface RiskListMngClient {

    /**
     * 查询黑白名单
     *
     * @param riskListQueryReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/risklistmng/users")
    GenericRspDTO<RiskListResDTO> queryRiskList(@Validated RiskListQueryReqDTO riskListQueryReqDTO);

    /**
     * 添加黑白名单
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/rsm/risklistmng/users")
    GenericRspDTO<NoBody> addRiskList(@Validated @RequestBody GenericDTO<RiskListInsertReqDTO> reqDTO);

    /**
     * 删除黑白名单对象
     *
     * @param reqDTO
     * @return
     */
    @DeleteMapping(value = "/rsm/risklistmng/users")
    GenericRspDTO<NoBody> deleteRiskList(@Validated @RequestBody GenericDTO<RiskListBaseReqDTO> reqDTO);

    /**
     * 查询黑白名单列表
     *
     * @param riskListQueryAllReqDTO
     * @return
     */
    @GetMapping(value = "/rsm/risklistmng/users/list")
    GenericRspDTO<List<RiskListResDTO>> queryAllRiskLists(@Validated RiskListQueryAllReqDTO riskListQueryAllReqDTO);

    /**
     * 修改黑白名单
     *
     * @param reqDTO
     * @return
     */
    @PutMapping(value = "/rsm/risklistmng/users")
    GenericRspDTO<NoBody> updateRiskList(@Validated @RequestBody GenericDTO<RiskListUpdateReqDTO> reqDTO);
}