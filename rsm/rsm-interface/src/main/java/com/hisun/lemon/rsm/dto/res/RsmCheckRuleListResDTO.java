package com.hisun.lemon.rsm.dto.res;

/**
 * 风控检查规则
 *
 * <AUTHOR>
 * @create 2017/8/4
 */
public class RsmCheckRuleListResDTO {

    private String checkId;

    private String txTyp;

    private String lmtLvl;

    private String ruleDesc;

    private String oprTyp;

    private String updOprId;

    public String getCheckId() {
        return checkId;
    }

    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getLmtLvl() {
        return lmtLvl;
    }

    public void setLmtLvl(String lmtLvl) {
        this.lmtLvl = lmtLvl;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public String toString() {
        return "RsmCheckRuleListDo{" +
                ", txTyp='" + txTyp + '\'' +
                ", lmtLvl='" + lmtLvl + '\'' +
                ", ruleDesc='" + ruleDesc + '\'' +
                ", oprTyp='" + oprTyp + '\'' +
                ", updOprId='" + updOprId + '\'' +
                '}';
    }
}
