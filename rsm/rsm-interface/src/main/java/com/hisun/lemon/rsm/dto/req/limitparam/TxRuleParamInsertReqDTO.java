package com.hisun.lemon.rsm.dto.req.limitparam;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/7/7
 */
@ClientValidated
@ApiModel(value = "TxRuleParamInsertReqDTO", description = "交易风控限额参数")
public class TxRuleParamInsertReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "账户类型", required = true)
    @NotEmpty(message = "RSM10010")
    private String acTyp;

    @ApiModelProperty(value = "限制级别，1-用户级别", required = true)
    @Pattern(regexp = "1", message = "RSM10038")
    private String lmtLvl;

    @ApiModelProperty(value = "收付款标志，0-全部，1-收方，2-付方", required = true)
    @Pattern(regexp = "0|1|2", message = "RSM10024")
    private String dcPtyFlg;

    @ApiModelProperty(value = "交易类型", required = true)
    @NotEmpty(message = "RSM10005")
    private String txTyp;

    @ApiModelProperty(value = "支付方式", required = true)
    @Pattern(regexp = "00|01|02|03|04", message = "RSM10011")
    private String payTyp;

    @ApiModelProperty(value = "单笔最小金额", required = true)
    @Min(value = 0, message = "RSM10013")
    private BigDecimal minAmtLmt;

    @ApiModelProperty(value = "单笔最大金额", required = true)
    @Min(value = 0, message = "RSM10014")
    private BigDecimal maxAmtLmt;

    @ApiModelProperty(value = "日累计金额", required = true)
    @Min(value = 0, message = "RSM10015")
    private BigDecimal dlyAmtLmt;

    @ApiModelProperty(value = "日累计次数", required = true)
    @Min(value = 0, message = "RSM10016")
    private Integer dlyCntLmt;

    @ApiModelProperty(value = "月累计金额", required = true)
    @Min(value = 0, message = "RSM10017")
    private BigDecimal mlyAmtLmt;

    @ApiModelProperty(value = "月累计次数", required = true)
    @Min(value = 0, message = "RSM10018")
    private Integer mlyCntLmt;

    @ApiModelProperty(value = "操作员id", required = true)
    @NotEmpty(message = "RSM10009")
    private String updOprId;

    public String getAcTyp() {
        return acTyp;
    }

    public void setAcTyp(String acTyp) {
        this.acTyp = acTyp;
    }

    public String getLmtLvl() {
        return lmtLvl;
    }

    public void setLmtLvl(String lmtLvl) {
        this.lmtLvl = lmtLvl;
    }

    public String getDcPtyFlg() {
        return dcPtyFlg;
    }

    public void setDcPtyFlg(String dcPtyFlg) {
        this.dcPtyFlg = dcPtyFlg;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getPayTyp() {
        return payTyp;
    }

    public void setPayTyp(String payTyp) {
        this.payTyp = payTyp;
    }

    public BigDecimal getMinAmtLmt() {
        return minAmtLmt;
    }

    public void setMinAmtLmt(BigDecimal minAmtLmt) {
        this.minAmtLmt = minAmtLmt;
    }

    public BigDecimal getMaxAmtLmt() {
        return maxAmtLmt;
    }

    public void setMaxAmtLmt(BigDecimal maxAmtLmt) {
        this.maxAmtLmt = maxAmtLmt;
    }

    public BigDecimal getDlyAmtLmt() {
        return dlyAmtLmt;
    }

    public void setDlyAmtLmt(BigDecimal dlyAmtLmt) {
        this.dlyAmtLmt = dlyAmtLmt;
    }

    public Integer getDlyCntLmt() {
        return dlyCntLmt;
    }

    public void setDlyCntLmt(Integer dlyCntLmt) {
        this.dlyCntLmt = dlyCntLmt;
    }

    public BigDecimal getMlyAmtLmt() {
        return mlyAmtLmt;
    }

    public void setMlyAmtLmt(BigDecimal mlyAmtLmt) {
        this.mlyAmtLmt = mlyAmtLmt;
    }

    public Integer getMlyCntLmt() {
        return mlyCntLmt;
    }

    public void setMlyCntLmt(Integer mlyCntLmt) {
        this.mlyCntLmt = mlyCntLmt;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public String toString() {
        return "TxRuleParamInsertReqDTO{" +
                "acTyp='" + acTyp + '\'' +
                ", lmtLvl='" + lmtLvl + '\'' +
                ", dcPtyFlg='" + dcPtyFlg + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", payTyp='" + payTyp + '\'' +
                ", minAmtLmt=" + minAmtLmt +
                ", maxAmtLmt=" + maxAmtLmt +
                ", dlyAmtLmt=" + dlyAmtLmt +
                ", dlyCntLmt=" + dlyCntLmt +
                ", mlyAmtLmt=" + mlyAmtLmt +
                ", mlyCntLmt=" + mlyCntLmt +
                ", updOprId='" + updOprId + '\'' +
                '}';
    }
}
