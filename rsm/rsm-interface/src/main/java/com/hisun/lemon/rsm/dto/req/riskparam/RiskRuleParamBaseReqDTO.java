package com.hisun.lemon.rsm.dto.req.riskparam;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@ClientValidated
@ApiModel(value = "RiskRuleParamBaseReqDTO", description = "风险交易规则参数对象")
public class RiskRuleParamBaseReqDTO {

    private String parmId;

    public String getParmId() {
        return parmId;
    }

    public void setParmId(String parmId) {
        this.parmId = parmId;
    }
}
