package com.hisun.lemon.rsm.dto.req.checkrule;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@ClientValidated
@ApiModel(value = "RsmCheckRuleListBaseReqDTO", description = "检查规则")
public class RsmCheckRuleListBaseReqDTO {

    @ApiModelProperty(value = "检查id", required = true)
    @NotEmpty(message = "RSM10040")
    private String checkId;

    public String getCheckId() {
        return checkId;
    }

    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }
}
