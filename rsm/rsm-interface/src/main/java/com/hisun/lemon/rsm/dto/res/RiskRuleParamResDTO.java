package com.hisun.lemon.rsm.dto.res;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @create 2017/7/7
 */
public class RiskRuleParamResDTO {
    private String parmId;
    private String ruleId;
    private String cpn_nm;
    private String parm1;
    private String parm2;
    private String parm3;
    private String parm4;
    private String parm5;
    private String parm6;
    private String parm7;
    private String parm8;
    private String updOprId;
    private LocalDate updDt;
    private LocalTime updTm;

    public String getParmId() {
        return parmId;
    }

    public void setParmId(String parmId) {
        this.parmId = parmId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getCpn_nm() {
        return cpn_nm;
    }

    public void setCpn_nm(String cpn_nm) {
        this.cpn_nm = cpn_nm;
    }

    public String getParm1() {
        return parm1;
    }

    public void setParm1(String parm1) {
        this.parm1 = parm1;
    }

    public String getParm2() {
        return parm2;
    }

    public void setParm2(String parm2) {
        this.parm2 = parm2;
    }

    public String getParm3() {
        return parm3;
    }

    public void setParm3(String parm3) {
        this.parm3 = parm3;
    }

    public String getParm4() {
        return parm4;
    }

    public void setParm4(String parm4) {
        this.parm4 = parm4;
    }

    public String getParm5() {
        return parm5;
    }

    public void setParm5(String parm5) {
        this.parm5 = parm5;
    }

    public String getParm6() {
        return parm6;
    }

    public void setParm6(String parm6) {
        this.parm6 = parm6;
    }

    public String getParm7() {
        return parm7;
    }

    public void setParm7(String parm7) {
        this.parm7 = parm7;
    }

    public String getParm8() {
        return parm8;
    }

    public void setParm8(String parm8) {
        this.parm8 = parm8;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDate getUpdDt() {
        return updDt;
    }

    public void setUpdDt(LocalDate updDt) {
        this.updDt = updDt;
    }

    public LocalTime getUpdTm() {
        return updTm;
    }

    public void setUpdTm(LocalTime updTm) {
        this.updTm = updTm;
    }

    @Override
    public String toString() {
        return "RiskRuleParamResDTO{" +
                "parmId='" + parmId + '\'' +
                ", ruleId='" + ruleId + '\'' +
                ", cpn_nm='" + cpn_nm + '\'' +
                ", parm1='" + parm1 + '\'' +
                ", parm2='" + parm2 + '\'' +
                ", parm3='" + parm3 + '\'' +
                ", parm4='" + parm4 + '\'' +
                ", parm5='" + parm5 + '\'' +
                ", parm6='" + parm6 + '\'' +
                ", parm7='" + parm7 + '\'' +
                ", parm8='" + parm8 + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", updDt=" + updDt +
                ", updTm=" + updTm +
                '}';
    }
}
