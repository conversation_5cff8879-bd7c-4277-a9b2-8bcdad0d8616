package com.hisun.lemon.rsm.dto.res;

import java.time.LocalDateTime;

/**
 * 用户状态数据对象（暂停，冻结）
 *
 * <AUTHOR>
 * @create 2017/8/10
 */
public class UserStatusResDTO {

    private String userId;

    private String userSts;

    private String rmk;

    private String updOprId;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserSts() {
        return userSts;
    }

    public void setUserSts(String userSts) {
        this.userSts = userSts;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}
