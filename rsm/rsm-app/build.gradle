apply plugin: 'application'

dependencies {
    compile project(":rsm-interface")
    compile("com.hisun:lemon-swagger")
    compile("com.hisun:lemon-framework")
    compile("com.hisun:lemon-framework-cumulative")
    compile("com.hisun:cmm-interface")
    compile("com.hisun:urm-interface")
    compile("org.springframework.boot:spring-boot-starter-thymeleaf")
}

mainClassName = 'com.hisun.lemon.Application'

jar {
    manifest {
        attributes(
                "Implementation-Title": "Gradle",
                "Implementation-Version": "${version}",
                "Class-Path": '. config/'
        )
    }
    //exclude('config/')
}

bootRepackage {
    enabled = true
    mainClass = 'com.hisun.lemon.Application'
}

task clearTarget(type:Delete){
    delete 'build/target'
}

task release(type: Copy,dependsOn: [clearTarget,build]) {
    from('build/libs') {
        include '*.jar'
        exclude '*-sources.jar'
    }
//    from('src/main/resources') {
//        include 'config/*'
//    }
    into ('build/target')

    into('bin') {
        from 'shell'
    }
}

task dist(type: Zip,dependsOn: [release]) {
    from ('build/target/') {
    }
}