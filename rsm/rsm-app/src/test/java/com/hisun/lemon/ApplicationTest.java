package com.hisun.lemon;

import com.hisun.lemon.common.utils.ReflectionUtils;
import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;
import com.hisun.lemon.rsm.risklibrary.CheckRuleTemplate;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/7/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ApplicationTest {

    @Test
    @Ignore
    public void test() throws IllegalAccessException, InstantiationException {
        TxRuleParamDo txRuleParamDo = new TxRuleParamDo();
        txRuleParamDo.setMaxAmtLmt(new BigDecimal("100"));
        txRuleParamDo.setMinAmtLmt(new BigDecimal("10"));
        BigDecimal txAmt = new BigDecimal("40");
        Class ruleClass = ReflectionUtils.forName("com.hisun.lemon.rsm.risklibrary.ChkSglAmtLmt");
        CheckRuleTemplate c = (CheckRuleTemplate) ruleClass.newInstance();

        RsmTxCheckCode r = c.check(txRuleParamDo, null, txAmt);

        System.out.println();
    }

}
