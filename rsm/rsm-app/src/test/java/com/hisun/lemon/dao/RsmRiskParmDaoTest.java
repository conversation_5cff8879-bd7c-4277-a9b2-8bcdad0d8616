package com.hisun.lemon.dao;

import com.hisun.lemon.rsm.dao.RsmRiskParmMapper;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/8/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RsmRiskParmDaoTest {

    @Resource
    private RsmRiskParmMapper rsmRiskParmMapper;

    @Test
    @Ignore
    public void testSelectByPrimaryKey(){
        RsmRiskParmDo rsmRiskParmDo = rsmRiskParmMapper.selectByPrimaryKey("RsmRules001");
        System.out.println(rsmRiskParmDo);
        System.out.println();
    }
}
