package com.hisun.lemon.dao;

import com.hisun.lemon.rsm.dao.RsmCheckRuleListMapper;
import com.hisun.lemon.rsm.entity.RsmCheckRuleListDo;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RsmCheckRuleListDaoTest {

    @Resource
    private RsmCheckRuleListMapper rsmCheckRuleListMapper;

    @Test
    @Ignore
    public void testAdd(){
        RsmCheckRuleListDo rsmCheckRuleListDo = new RsmCheckRuleListDo();
        rsmCheckRuleListDo.setCheckId("001502");
        rsmCheckRuleListDo.setTxTyp("01");
        rsmCheckRuleListDo.setLmtLvl("1");
        rsmCheckRuleListDo.setOprTyp("1");
        rsmCheckRuleListDo.setRuleDesc("测试");
        rsmCheckRuleListDo.setUpdOprId("test");
        rsmCheckRuleListDo.setCreateTime(LocalDateTime.now());
        rsmCheckRuleListDo.setModifyTime(LocalDateTime.now());
        int result = rsmCheckRuleListMapper.addCheckRuleList(rsmCheckRuleListDo);

        System.out.println(result);
    }
}
