package com.hisun.lemon.service;

import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/8/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RsmRiskParamServiceTest {

    @Resource
    private IRsmRiskParamService rsmRiskParamService;

    @Test
    @Ignore
    public void testQueryRuleParam(){
        RsmRiskParmDo rsmRiskParmDo = rsmRiskParamService.queryRuleParam("RsmRules001");
        System.out.println();
    }
}
