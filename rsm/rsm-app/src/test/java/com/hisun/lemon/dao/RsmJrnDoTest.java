package com.hisun.lemon.dao;

import com.hisun.lemon.rsm.dao.RsmJrnMapper;
import com.hisun.lemon.rsm.entity.RsmJrnDo;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @create 2017/8/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RsmJrnDoTest {

    @Resource
    private RsmJrnMapper rsmJrnMapper;

    @Test
    @Ignore
    public void testInsert() {
        RsmJrnDo rsmJrnDo = new RsmJrnDo();
        rsmJrnDo.setJrnNo("“RSM2017080716201200000003");
        rsmJrnDo.setStlUserId("1");
        rsmJrnDo.setStlUserTyp("1");
        rsmJrnDo.setPayUserId("1");
        rsmJrnDo.setPayUserTyp("1");
        rsmJrnDo.setTxTyp("01");
        rsmJrnDo.setTxSts("1");
        rsmJrnDo.setTxCnl("01");
        rsmJrnDo.setTxAmt("10");
        rsmJrnDo.setCcy("USD");
        rsmJrnDo.setPayTyp("01");
        rsmJrnDo.setTxDt(LocalDate.of(2017, 8, 25));
        rsmJrnDo.setTxTm(LocalTime.of(11, 12, 34));
        rsmJrnDo.setTxJrnNo("1");
        rsmJrnDo.setTxOrdNo("1");
        rsmJrnDo.setTxData("123213");
        rsmJrnDo.setRuleId("1");
        rsmJrnDo.setCreateTime(LocalDateTime.now());
        rsmJrnDo.setModifyTime(LocalDateTime.now());

        System.out.println();
        rsmJrnMapper.insert(rsmJrnDo);
    }
}
