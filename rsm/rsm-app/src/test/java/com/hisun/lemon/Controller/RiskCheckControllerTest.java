package com.hisun.lemon.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/8/5
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RiskCheckControllerTest {

    @Resource
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    @Test
    @Ignore
    public void testRiskControl() throws Exception {
        String url = "/rsm/realtimerisk/check";
        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        RequestBuilder request = MockMvcRequestBuilders.get(url).contentType(MediaType.APPLICATION_JSON_UTF8);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();
        System.out.println(content);
        System.out.println();
        Assert.assertTrue("正确", status == 200);
        Assert.assertFalse("错误", status != 200);
    }
}
