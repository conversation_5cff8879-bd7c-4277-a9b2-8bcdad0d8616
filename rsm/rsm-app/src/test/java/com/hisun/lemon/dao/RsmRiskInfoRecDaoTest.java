package com.hisun.lemon.dao;

import com.hisun.lemon.rsm.dao.RsmRiskInfoRecMapper;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RsmRiskInfoRecDaoTest {

    @Autowired
    private RsmRiskInfoRecMapper rsmRiskInfoRecMapper;

    @Test
    @Ignore
    public void TestSelectRecsByTimeAndCapFlw(){
        List list = rsmRiskInfoRecMapper.selectRecsByTimeAndCapFlw("1", null, LocalDate.now().minusDays(2));
        System.out.println();
    }
}
