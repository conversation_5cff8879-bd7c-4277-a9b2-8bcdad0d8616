package com.hisun.lemon.dao;

import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.dao.RiskBlackListMapper;
import com.hisun.lemon.rsm.dao.RiskWhiteListMapper;
import com.hisun.lemon.rsm.entity.RiskListDo;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RiskListDaoTest {

    @Autowired
    private RiskBlackListMapper riskBlackListMapper;

    @Autowired
    private RiskWhiteListMapper riskWhiteListMapper;

    @Test
    @Ignore
    public void TestInsertBlackList() {
        RiskListDo riskListDo = new RiskListDo();
        String id = IdGenUtils.generateId("RSM_BLK_LIST", 10);
        riskListDo.setListId(id);
        riskListDo.setIdTyp("04");
        riskListDo.setId("+86-13188388838");
        riskListDo.setIdHid("+86-131****8888");
        riskListDo.setTxTyp("01020304");
        riskListDo.setBeginDt(LocalDateTime.now());
        riskListDo.setEndDt(LocalDateTime.now());
        riskListDo.setEffFlg("1");
        riskListDo.setListSorc("test");
        riskListDo.setListRsn("测试");
        riskListDo.setUpdOprId("测试2号");
        riskListDo.setCreateTime(LocalDateTime.now());
        riskListDo.setModifyTime(LocalDateTime.now());
        riskBlackListMapper.insert(riskListDo);
    }

    @Test
    @Ignore
    public void TestInsertWhiteList() {
        RiskListDo riskListDo = new RiskListDo();
        riskListDo.setListId("1");
        riskListDo.setId("+86-13188888888");
        riskListDo.setIdTyp("04");
        riskListDo.setIdHid("+86-131****8888");
        riskListDo.setTxTyp("01020304");
        riskListDo.setBeginDt(LocalDateTime.now());
        riskListDo.setEndDt(LocalDateTime.now());
        riskListDo.setEffFlg("1");
        riskListDo.setListSorc("test");
        riskListDo.setListRsn("测试");
        riskListDo.setUpdOprId("测试2号");
        riskListDo.setCreateTime(LocalDateTime.now());
        riskListDo.setModifyTime(LocalDateTime.now());
        riskWhiteListMapper.insert(riskListDo);
    }

    @Test
    @Ignore
    public void TestQueryBlackList() {
        System.out.println(riskBlackListMapper.selectByPrimaryKey("0000016001").toString());
    }

    @Test
    @Ignore
    public void TestDeleteBlackList() {
        riskBlackListMapper.deleteByPrimaryKey("0000016001");
    }

    @Test
    @Ignore
    public void TestQueryAllBlackList() {
        List<RiskListDo> list = PageUtils.pageQuery(0, 5, () -> riskBlackListMapper.selectAllLists());
        for (RiskListDo riskListDo : list) {
            System.out.println(riskListDo.toString());
        }
    }

    @Test
    @Ignore
    public void TestSelectByIdAndTypAndTxTyp() {
        List<RiskListDo> riskListDo =  riskBlackListMapper.selectByIdAndTypAndTxTyp("1", "01", "01");
    }
}
