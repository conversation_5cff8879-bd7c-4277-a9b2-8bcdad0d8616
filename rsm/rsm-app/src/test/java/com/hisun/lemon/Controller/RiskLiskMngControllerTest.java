package com.hisun.lemon.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 * @create 2017/7/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RiskLiskMngControllerTest {

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    @Test
    @Ignore
    public void testQueryRuleList() throws Exception {
        String userId = "0000002001";
        String url = "/rsm/risklistmng/users";
        url = url + "/" + userId + "?listTyp=0";
        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        RequestBuilder request = MockMvcRequestBuilders.get(url).contentType(MediaType.APPLICATION_JSON_UTF8);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();
        System.out.println(content);
        System.out.println();
        //System.out.print("return code :" + status);
        Assert.assertTrue("正确", status == 200);
        Assert.assertFalse("错误", status != 200);
    }
}
