package com.hisun.lemon.dao;

import com.hisun.lemon.rsm.dao.RiskRuleMapper;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/7/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RiskRuleDaoTest {

    @Resource
    private RiskRuleMapper riskRuleMapper;

    @Test
    @Ignore
    public void testSelectAllRules(){
        System.out.println(riskRuleMapper.selectAllRules());
    }
}
