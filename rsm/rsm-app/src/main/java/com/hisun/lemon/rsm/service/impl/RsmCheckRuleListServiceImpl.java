package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.RsmCheckRuleListMapper;
import com.hisun.lemon.rsm.entity.RsmCheckRuleListDo;
import com.hisun.lemon.rsm.service.IRsmCheckRuleListService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/4
 */
@Service
@Transactional
public class RsmCheckRuleListServiceImpl extends BaseService implements IRsmCheckRuleListService {

    @Resource
    private RsmCheckRuleListMapper rsmCheckRuleListMapper;

    @Override
    @Transactional(readOnly = true)
    //TODO 缓存
    public RsmCheckRuleListDo queryCheckRule(String txTyp) {
        return rsmCheckRuleListMapper.queryCheckRuleList(txTyp);
    }

    @Override
    public void addCheckRule(RsmCheckRuleListDo rsmCheckRuleListDo) {
        rsmCheckRuleListDo.setCheckId(IdGenUtils.generateId("RSM_CHECK_RULE_ID", 6));
        rsmCheckRuleListDo.setCreateTime(LocalDateTime.now());
        rsmCheckRuleListDo.setModifyTime(LocalDateTime.now());
        int result = rsmCheckRuleListMapper.addCheckRuleList(rsmCheckRuleListDo);
        if (result <= 0) {
            throw new RsmException(MsgCd.INSERT_ERROR, Constants.RSM_TABLE_CHECK_RULE);
        }
    }

    @Override
    public void deleteCheckRule(String checkId) {
        int result = rsmCheckRuleListMapper.deleteCheckRuleList(checkId);
        if (result <= 0) {
            throw new RsmException(MsgCd.DELETE_ERROR, Constants.RSM_TABLE_CHECK_RULE);
        }
    }

    @Override
    public RsmCheckRuleListDo queryCheckRuleByCheckId(String checkId) {
        RsmCheckRuleListDo rsmCheckRuleListDo = rsmCheckRuleListMapper.queryCheckRuleListByCheckId(checkId);
        if (JudgeUtils.isNull(rsmCheckRuleListDo)) {
            throw new RsmException(MsgCd.SELECT_ERROR, Constants.RSM_TABLE_CHECK_RULE);
        }
        return rsmCheckRuleListDo;
    }

    @Override
    public void updateCheckRule(RsmCheckRuleListDo rsmCheckRuleListDo) {
        rsmCheckRuleListDo.setModifyTime(LocalDateTime.now());
        int result = rsmCheckRuleListMapper.updateCheckRuleList(rsmCheckRuleListDo);
        if (result <= 0) {
            throw new RsmException(MsgCd.UPDATE_ERROR, Constants.RSM_TABLE_CHECK_RULE);
        }
    }

    @Override
    public List<RsmCheckRuleListDo> queryAllCheckRules(int pageNum, int pageSize) {
        return PageUtils.pageQuery(pageNum, pageSize, () -> rsmCheckRuleListMapper.queryAllCheckRuleLists());
    }
}
