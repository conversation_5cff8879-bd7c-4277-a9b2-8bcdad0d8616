package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.UserStatusMapper;
import com.hisun.lemon.rsm.entity.UserStatusDo;
import com.hisun.lemon.rsm.service.IUserStatusMngService;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/10
 */
@Service
@Transactional
public class UserStatusMngServiceImpl extends BaseService implements IUserStatusMngService {

    private static final Logger logger = LoggerFactory.getLogger(IUserStatusMngService.class);

    @Resource
    private UserStatusMapper userStatusMapper;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Override
    public UserStatusDo queryUserStatusByUserId(String userId) {
        UserStatusDo userStatusDo = userStatusMapper.selectByPrimaryKey(userId);
        return userStatusDo;
    }

    @Override
    public List<UserStatusDo> queryAllUserStatusRec(String userSts, int pageNum, int pageSize) {
        return PageUtils.pageQuery(pageNum, pageSize, () -> userStatusMapper.selectAllUserStatusRec(userSts));
    }

    @Override
    public void addUserStatusRec(UserStatusDo userStatusDo, String mblNo) {
        GenericRspDTO<UserBasicInfDTO> userBasicInfDTO = userBasicInfClient.queryUserByLoginId(mblNo);
        userStatusDo.setUserId(userBasicInfDTO.getBody().getUserId());
        userStatusDo.setCreateTime(LocalDateTime.now());
        userStatusDo.setModifyTime(LocalDateTime.now());
        int result = userStatusMapper.insertUserStatusRec(userStatusDo);
        if (result <= 0) {
            logger.error("Insert error！");
            throw new RsmException(MsgCd.INSERT_ERROR, Constants.RSM_TABLE_USER_STATUS);
        }
    }

    @Override
    public void deleteUserStatusRec(String userId) {
        int result = userStatusMapper.deleteByPrimaryKey(userId);
        if (result <= 0) {
            logger.error("Delete error！");
            throw new RsmException(MsgCd.DELETE_ERROR, Constants.RSM_TABLE_USER_STATUS);
        }
    }

    @Override
    public void updateUserStatusRec(UserStatusDo userStatusDo) {
        userStatusDo.setModifyTime(LocalDateTime.now());
        int result = userStatusMapper.updateByPrimaryKeySelective(userStatusDo);
        if (result <= 0) {
            logger.error("Update error！");
            throw new RsmException(MsgCd.UPDATE_ERROR, Constants.RSM_TABLE_USER_STATUS);
        }
    }
}
