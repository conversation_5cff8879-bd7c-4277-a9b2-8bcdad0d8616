package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.rsm.common.RsmBaseUtil;
import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/8/25
 */
public class ChkDlyAmtLmt implements CheckRuleTemplate {

    @Override
    public RsmTxCheckCode check(TxRuleParamDo txRuleParamDo, String[] cacheValue, BigDecimal txAmt) {

        BigDecimal dayTotAmt = RsmBaseUtil.divHundred(new BigDecimal(cacheValue[0]));

        //限额信息初始化
        BigDecimal dlyAmtLmt = txRuleParamDo.getDlyAmtLmt();

        if (dlyAmtLmt != null && dayTotAmt.add(txAmt).compareTo(dlyAmtLmt) > 0) {
            return RsmTxCheckCode.MORE_THAN_DAILY_CUMULATE_AMT;
        }

        return RsmTxCheckCode.PASS;
    }
}
