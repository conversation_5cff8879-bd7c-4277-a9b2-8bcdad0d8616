package com.hisun.lemon.rsm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.RsmJrnReqDTO;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;
import com.hisun.lemon.rsm.service.IRsmCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@Api(tags = "风控检查，额度累计，用户状态检查", value = "风控检查")
@RestController
@RequestMapping("/rsm")
public class RiskCheckController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RiskCheckController.class);

    @Resource
    private IRsmCheckService rsmCheckService;

    /**
     * 实时风控检查
     *
     * @return
     */
    @ApiOperation(value = "实时风控检查", notes = "实时风控检查")
    @PostMapping("/realtimerisk/check")
    public GenericRspDTO<NoBody> riskControl(@Validated @RequestBody JrnReqDTO jrnReqDTO) {
        logger.debug(jrnReqDTO.toString());

        GenericRspDTO<NoBody> genericRspDTO = GenericRspDTO.newSuccessInstance();
        RsmJrnReqDTO rsmJrnReqDTO = new RsmJrnReqDTO();
        BeanUtils.copyProperties(rsmJrnReqDTO, jrnReqDTO);
        rsmJrnReqDTO.setTxDt(jrnReqDTO.getTxDate());
        rsmJrnReqDTO.setTxTm(jrnReqDTO.getTxTime());

        //检查黑白名单、暂停、冻结
        RsmTxCheckCode code;
        try {
            code = rsmCheckService.checkBlackAndWhileList(rsmJrnReqDTO);
        } catch (LemonException e) {
            logger.error(e.getMsgCd(), e.getMsgInfo());
            genericRspDTO.setMsgCd(e.getMsgCd());
            genericRspDTO.setMsgInfo(e.getMsgInfo());
            return genericRspDTO;
        }
        switch (code) {
            case REFUSE_BY_BLACK_LIST:
                genericRspDTO.setMsgCd(MsgCd.BLACK_LIST_IS_EXIST.getMsgCd());
                return genericRspDTO;
            case REFUSE_BY_USER_STS_FREEZE:
                genericRspDTO.setMsgCd(MsgCd.USER_STATUS_IS_FROZEN.getMsgCd());
                return genericRspDTO;
            case REFUSE_BY_USER_STS_PAUSE:
                genericRspDTO.setMsgCd(MsgCd.USER_STATUS_IS_PAUSED.getMsgCd());
                return genericRspDTO;
            case PASS_BY_WHITE_LIST:
                return genericRspDTO;
        }

        //没有交易金额时直接返回
        if (JudgeUtils.isNull(jrnReqDTO.getTxAmt())) {
            return genericRspDTO;
        }

        //按照规则校验交易信息（频次，额度）
        try {
            genericRspDTO = rsmCheckService.checkRisk(rsmJrnReqDTO);
        } catch (RsmException e) {
            logger.error("Risk check error:" + e.getMessage());
            genericRspDTO.setMsgCd(e.getMsgCd());
            genericRspDTO.setMsgInfo(e.getMsgInfo());
            return genericRspDTO;
        }
        return genericRspDTO;
    }

    @ApiOperation(value = "批量检查", notes = "批量检查")
    @PostMapping("/notrealtimerisk/batchcheck")
    public GenericRspDTO<Map<String, String>> batchCheck(@Validated @RequestBody List<JrnReqDTO> listDto) {
        GenericRspDTO<Map<String, String>> genericRspDTO = new GenericRspDTO<>();
        genericRspDTO.setMsgCd(LemonUtils.getSuccessMsgCd());
        if (listDto.isEmpty()) {
            return genericRspDTO;
        }

        GenericRspDTO<NoBody> checkResult;
        Map<String, String> result = new HashMap<>();
        for (JrnReqDTO reqDTO : listDto) {
            try {
                checkResult = riskControl(reqDTO);
                logger.info("@@风控检查结果@@：" + checkResult.getMsgCd());
                if (JudgeUtils.isNotSuccess(checkResult.getMsgCd())) {
                    throw new LemonException(checkResult.getMsgCd());
                }
            } catch (LemonException e) {
                result.put(reqDTO.getPayTyp(), e.getMsgCd());
                genericRspDTO.setMsgCd(MsgCd.RSM_REAL_TIME_CHECK_REFUSE.getMsgCd());
            }
        }
        genericRspDTO.setBody(result);
        return genericRspDTO;
    }

    @ApiOperation(value = "批量累计", notes = "批量累计")
    @PostMapping("/notrealtimerisk/batchaccumulation")
    public GenericRspDTO<NoBody> batchAccumulation(@Validated @RequestBody GenericDTO<List<JrnReqDTO>> genericDTO) {
        GenericRspDTO<NoBody> genericRspDTO = GenericRspDTO.newSuccessInstance();
        List<JrnReqDTO> listDto = genericDTO.getBody();

        JrnReqDTO payTypAll = new JrnReqDTO();
        BeanUtils.copyProperties(payTypAll, listDto.get(0));
        payTypAll.setPayTyp(Constants.PAY_TYP_ALL);
        BigDecimal allAmt = BigDecimal.ZERO;

        for (JrnReqDTO jrnReqDTO : listDto) {
            allAmt = allAmt.add(jrnReqDTO.getTxAmt());
        }

        payTypAll.setTxAmt(allAmt);
        listDto.add(payTypAll);

        for (JrnReqDTO jrnReqDTO : listDto) {
            RsmJrnReqDTO rsmJrnReqDTO = new RsmJrnReqDTO();
            BeanUtils.copyProperties(rsmJrnReqDTO, jrnReqDTO);
            rsmJrnReqDTO.setTxDt(jrnReqDTO.getTxDate());
            rsmJrnReqDTO.setTxTm(jrnReqDTO.getTxTime());
            if (JudgeUtils.isNull(rsmJrnReqDTO.getTxData()) ||
                    JudgeUtils.equals("null", rsmJrnReqDTO.getTxData())) {
                rsmJrnReqDTO.setTxData("ipAddr=" + genericDTO.getClientIp());
            } else {
                rsmJrnReqDTO.setTxData(rsmJrnReqDTO.getTxData() + ",ipAddr=" + genericDTO.getClientIp());
            }
            rsmCheckService.accumulateAmount(rsmJrnReqDTO);
        }
        return genericRspDTO;
    }

    /**
     * 交易额度累计
     */
    @ApiOperation(value = "交易额度累计", notes = "交易额度累计")
    @PostMapping("/notrealtimerisk/accumulateamount")
    public GenericRspDTO<NoBody> accumulateAmount(@Validated @RequestBody GenericDTO<JrnReqDTO> genericDTO) {
        JrnReqDTO jrnReqDTO = genericDTO.getBody();
        logger.debug(jrnReqDTO.toString());

        RsmJrnReqDTO rsmJrnReqDTO = new RsmJrnReqDTO();
        BeanUtils.copyProperties(rsmJrnReqDTO, jrnReqDTO);
        rsmJrnReqDTO.setTxDt(jrnReqDTO.getTxDate());
        rsmJrnReqDTO.setTxTm(jrnReqDTO.getTxTime());
        if (JudgeUtils.isNull(rsmJrnReqDTO.getTxData()) ||
                JudgeUtils.equals("null", rsmJrnReqDTO.getTxData())) {
            rsmJrnReqDTO.setTxData("ipAddr=" + genericDTO.getClientIp());
        } else {
            rsmJrnReqDTO.setTxData(rsmJrnReqDTO.getTxData() + ",ipAddr=" + genericDTO.getClientIp());
        }
        return rsmCheckService.accumulateAmount(rsmJrnReqDTO);
    }

    /**
     * 用户状态检查
     *
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "用户状态检查", notes = "用户状态检查")
    @PostMapping("/realtimerisk/status")
    public GenericRspDTO<NoBody> checkUserStatus(@Validated @RequestBody RiskCheckUserStatusReqDTO reqDTO) {

        logger.debug(reqDTO.toString());

        GenericRspDTO<NoBody> genericRspDTO = GenericRspDTO.newSuccessInstance();
        RsmTxCheckCode code;
        try {
            code = rsmCheckService.checkUserStatus(reqDTO);
        } catch (LemonException e) {
            genericRspDTO.setMsgCd(e.getMsgCd());
            genericRspDTO.setMsgInfo(e.getMsgInfo());
            return genericRspDTO;
        }

        if (JudgeUtils.equals(reqDTO.getIdTyp(), Constants.ID_TYP_USER_NO)) {
            String flg = reqDTO.getId().substring(0, 6);

            if (JudgeUtils.equals(code, RsmTxCheckCode.REFUSE_BY_BLACK_LIST)) {
                genericRspDTO.setMsgCd(MsgCd.BLACK_LIST_IS_EXIST.getMsgCd());
                if (JudgeUtils.equals(flg, "888888")) {
                    genericRspDTO.setMsgCd(MsgCd.MERC_IN_BLACK_LIST.getMsgCd());
                }
                return genericRspDTO;
            }
            if (JudgeUtils.equals(code, RsmTxCheckCode.REFUSE_BY_USER_STS_FREEZE)) {
                genericRspDTO.setMsgCd(MsgCd.USER_STATUS_IS_FROZEN.getMsgCd());
                if (JudgeUtils.equals(flg, "888888")) {
                    genericRspDTO.setMsgCd(MsgCd.MERC_STATUS_IS_FROZEN.getMsgCd());
                }
                return genericRspDTO;
            }
            if (JudgeUtils.equals(code, RsmTxCheckCode.REFUSE_BY_USER_STS_PAUSE)) {
                genericRspDTO.setMsgCd(MsgCd.USER_STATUS_IS_PAUSED.getMsgCd());
                if (JudgeUtils.equals(flg, "888888")) {
                    genericRspDTO.setMsgCd(MsgCd.MERC_STATUS_IS_PAUSED.getMsgCd());
                }
                return genericRspDTO;
            }
        }
        return genericRspDTO;
    }
}
