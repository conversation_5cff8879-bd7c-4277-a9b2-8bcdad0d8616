package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.entity.RsmHighRiskUserDo;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmHighRiskUserService;
import com.hisun.lemon.rsm.service.IRsmRiskInfoRecService;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商户账户X日内发生来自于同一IP地址，交易频繁（大于X次），交易金额超过XX万
 * 参数1：X天
 * 参数2：X次交易次数
 * 参数3：X万元交易金额
 *
 * <AUTHOR>
 * @create 2017/7/22
 */
@Component
public class RsmRules004 implements SpecialRuleTemplate {

    private static final Logger logger = LoggerFactory.getLogger(RsmRules004.class);

    @Resource
    private IRsmRiskInfoRecService rsmRiskInfoRecService;

    @Resource
    private IRsmRiskParamService rsmRiskParamService;

    @Resource
    private IRsmHighRiskUserService rsmHighRiskUserService;

    @Override
    public void execute(TxCheckRiskBo txCheckRiskBo) {
        //如果风险参数不存在直接跳过
        RsmRiskParmDo rsmRiskParmDo = rsmRiskParamService.queryRuleParam("RsmRules004");
        if (JudgeUtils.isNull(rsmRiskParmDo)) {
            return;
        }
        //参数1，时间天数
        int dayAgo = Integer.valueOf(rsmRiskParmDo.getParm1());
        //参数2，交易次数
        int txNum = Integer.valueOf(rsmRiskParmDo.getParm2());
        //参数3，交易累计金额
        BigDecimal riskMaxAmt = new BigDecimal(rsmRiskParmDo.getParm3());

        MsgCd result = MsgCd.SUCCESS;
        List<RsmRiskInfoRecDo> recList = rsmRiskInfoRecService.queryRiskInfoRec(txCheckRiskBo.getPayUserId(),
                Constants.CAP_FLW_IN, dayAgo);
        if (recList.size() == 0) {
            return;
        }

        Map<String, Integer> ipRec = new HashMap<>();
        Integer tmpCnt;
        for (RsmRiskInfoRecDo rec : recList) {
            if (rec.getTxAmt().compareTo(riskMaxAmt) > 0) {
                tmpCnt = ipRec.get(rec.getIpAddr());
                if (JudgeUtils.isNull(tmpCnt)) {
                    ipRec.put(rec.getIpAddr(), 1);
                } else {
                    ipRec.put(rec.getIpAddr(), tmpCnt + 1);
                }

            }
        }

        for (Map.Entry<String, Integer> entry : ipRec.entrySet()) {
            if (entry.getValue() > txNum) {
                logger.warn(RsmRules004.class.getName());
                RsmHighRiskUserDo rsmHighRiskUserDo = new RsmHighRiskUserDo();
                rsmHighRiskUserDo.setUserId(txCheckRiskBo.getPayUserId());
                rsmHighRiskUserDo.setTxTyp(txCheckRiskBo.getTxTyp());
                rsmHighRiskUserDo.setRiskOpr(Constants.RISK_OPR_SPS);
                //TODO 风险描述，或多语言支持
                rsmHighRiskUserDo.setRiskDesc("123");
                rsmHighRiskUserDo.setOprSts(Constants.OPR_STS_RISK);
                rsmHighRiskUserDo.setRiskSorc(Constants.RISK_SORC);
                rsmHighRiskUserDo.setTxOrdNo(txCheckRiskBo.getTxOrdNo());
                rsmHighRiskUserDo.setRiskParm(this.getClass().getSimpleName());
                rsmHighRiskUserDo.setCreateTime(LocalDateTime.now());
                rsmHighRiskUserDo.setModifyTime(LocalDateTime.now());
                rsmHighRiskUserService.addUserToHighList(rsmHighRiskUserDo);
                break;
            }
        }
    }
}
