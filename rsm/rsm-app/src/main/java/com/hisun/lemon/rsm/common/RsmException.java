package com.hisun.lemon.rsm.common;

import com.hisun.lemon.common.exception.LemonException;

/**
 * RSM模块异常
 *
 * <AUTHOR>
 * @create 2017/7/10
 */
public class RsmException extends LemonException {

    /**
     * 对数据库用异常
     *
     * @param msgCd   错误码
     * @param tableNm 数据库名
     */
    public RsmException(MsgCd msgCd, String tableNm) {
        super(msgCd.getMsgCd(), msgCd.getMsgInfo() + "(" + tableNm + ")");
    }

    /**
     * 普通用异常
     *
     * @param msgCd 错误码
     */
    public RsmException(MsgCd msgCd) {
        super(msgCd.getMsgCd());
    }
}