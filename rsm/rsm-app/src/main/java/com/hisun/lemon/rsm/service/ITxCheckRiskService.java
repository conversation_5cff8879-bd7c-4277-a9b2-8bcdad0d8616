package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

/**
 * <AUTHOR>
 * @create 2017/7/7
 */
public interface ITxCheckRiskService {

    /**
     * 实时风控检查
     *
     * @param txCheckRiskBo
     * @return
     */
    RsmTxCheckCode riskRealTimeCheck(TxCheckRiskBo txCheckRiskBo);

    /**
     * 交易额度累计
     *
     * @param txCheckRiskBo
     */
    RsmTxCheckCode rsmNotRealTimeCheck(TxCheckRiskBo txCheckRiskBo);
}
