package com.hisun.lemon.rsm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 风控规则数据对象
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
public class RiskRuleDo extends BaseDO {

    private String ruleId;
    private String ruleNm;
    private String ruleDesc;
    private String ruleTyp;
    private String dcPtyFlg;
    private String cpnNm;
    private String updOprId;
    private LocalDateTime createTime;
    private LocalDateTime modifyTime;

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleNm() {
        return ruleNm;
    }

    public void setRuleNm(String ruleNm) {
        this.ruleNm = ruleNm;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getRuleTyp() {
        return ruleTyp;
    }

    public void setRuleTyp(String ruleTyp) {
        this.ruleTyp = ruleTyp;
    }

    public String getDcPtyFlg() {
        return dcPtyFlg;
    }

    public void setDcPtyFlg(String dcPtyFlg) {
        this.dcPtyFlg = dcPtyFlg;
    }

    public String getCpnNm() {
        return cpnNm;
    }

    public void setCpnNm(String cpnNm) {
        this.cpnNm = cpnNm;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    @Override
    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RiskRuleDo{" +
                "ruleId='" + ruleId + '\'' +
                ", ruleNm='" + ruleNm + '\'' +
                ", ruleDesc='" + ruleDesc + '\'' +
                ", ruleTyp='" + ruleTyp + '\'' +
                ", dcPtyFlg='" + dcPtyFlg + '\'' +
                ", cpnNm='" + cpnNm + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
