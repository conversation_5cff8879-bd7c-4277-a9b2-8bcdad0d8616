package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.entity.RsmHighRiskUserDo;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmHighRiskUserService;
import com.hisun.lemon.rsm.service.IRsmRiskInfoRecService;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;

/**
 * 个人客户X天内通过充值转账等集中（小于X次）转入X万元以上，并通过提现转账等方式向X个以上不同账户分散转出；
 * 参数1：X天
 * 参数2：转入交易X次
 * 参数3：转入X元
 * 参数4：X个不同的账户
 *
 * <AUTHOR>
 * @create 2017/7/19
 */
@Component
public class RsmRules001 implements SpecialRuleTemplate {

    private static final Logger logger = LoggerFactory.getLogger(RsmRules001.class);

    @Resource
    private IRsmRiskInfoRecService rsmRiskInfoRecService;

    @Resource
    private IRsmRiskParamService rsmRiskParamService;

    @Resource
    private IRsmHighRiskUserService rsmHighRiskUserService;

    @Override
    public void execute(TxCheckRiskBo txCheckRiskBo) {
        //如果风险参数不存在直接跳过
        RsmRiskParmDo rsmRiskParmDo = rsmRiskParamService.queryRuleParam("RsmRules001");
        if (JudgeUtils.isNull(rsmRiskParmDo)) {
            return;
        }
        //参数1，时间天数
        int dayAgo = Integer.valueOf(rsmRiskParmDo.getParm1());
        //参数2，转入交易次数
        int txNum = Integer.valueOf(rsmRiskParmDo.getParm2());
        //参数3，转入限额
        BigDecimal riskMaxAmt = new BigDecimal(rsmRiskParmDo.getParm3());
        //参数4，X个资金流出交易对象
        int txPerNum = Integer.valueOf(rsmRiskParmDo.getParm4());

        chkUser(txCheckRiskBo.getPayUserId(), txNum, dayAgo, riskMaxAmt, txPerNum, txCheckRiskBo);
        chkUser(txCheckRiskBo.getStlUserId(), txNum, dayAgo, riskMaxAmt, txPerNum, txCheckRiskBo);
    }

    private void chkUser(String userId, int dayAgo, int txNum, BigDecimal riskMaxAmt,
                          int txPerNum, TxCheckRiskBo txCheckRiskBo) {
        List<RsmRiskInfoRecDo> recList = rsmRiskInfoRecService.queryRiskInfoRec(userId, Constants.CAP_FLW_ALL, dayAgo);
        if (recList.isEmpty()) {
            return;
        }
        BigDecimal inAmt = BigDecimal.ZERO;
        HashSet<String> txAcNum = new HashSet<>();
        int num = 0;
        for (RsmRiskInfoRecDo rec : recList) {
            if (JudgeUtils.equals(rec.getCapFlw(), Constants.CAP_FLW_IN)) {
                inAmt = inAmt.add(rec.getTxAmt());
                num++;
            }
            if (JudgeUtils.equals(rec.getCapFlw(), Constants.CAP_FLW_OUT)) {
                txAcNum.add(rec.getTxUserId());
            }
        }
        if (num < txNum && inAmt.compareTo(riskMaxAmt) > 0 && txAcNum.size() > txPerNum) {
            logger.warn(RsmRules001.class.getName());
            RsmHighRiskUserDo rsmHighRiskUserDo = new RsmHighRiskUserDo();
            rsmHighRiskUserDo.setUserId(userId);
            rsmHighRiskUserDo.setTxTyp(txCheckRiskBo.getTxTyp());
            rsmHighRiskUserDo.setRiskOpr(Constants.RISK_OPR_SPS);
            //TODO 风险描述，或多语言支持
            rsmHighRiskUserDo.setRiskDesc("123");
            rsmHighRiskUserDo.setOprSts(Constants.OPR_STS_RISK);
            rsmHighRiskUserDo.setRiskSorc(Constants.RISK_SORC);
            rsmHighRiskUserDo.setTxOrdNo(txCheckRiskBo.getTxOrdNo());
            rsmHighRiskUserDo.setRiskParm(this.getClass().getSimpleName());
            rsmHighRiskUserDo.setCreateTime(LocalDateTime.now());
            rsmHighRiskUserDo.setModifyTime(LocalDateTime.now());
            rsmHighRiskUserService.addUserToHighList(rsmHighRiskUserDo);
        }
    }
}
