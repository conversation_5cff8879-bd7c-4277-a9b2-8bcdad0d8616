package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.entity.RsmRiskParmDo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/21
 */
public interface IRsmRiskParamService {
    /**
     * 增加风险规则参数
     *
     * @param rsmRiskParmDo 风险规则参数对象
     * @return
     */
    boolean addRuleParam(RsmRiskParmDo rsmRiskParmDo);

    /**
     * 删除风险规则参数
     *
     * @param parmId 风险规则参数id
     * @return
     */
    boolean deleteRuleParam(String parmId);

    /**
     * 查询风险规则参数
     *
     * @param parmId 风险规则参数id
     * @return
     */
    RsmRiskParmDo queryRuleParam(String parmId);

    /**
     * 更新风险规则参数
     *
     * @param rsmRiskParmDo 风险规则参数对象
     * @return
     */
    boolean updateRuleParam(RsmRiskParmDo rsmRiskParmDo);

    /**
     * 查询所有风险规则参数列表
     *
     * @param pageNum  页数
     * @param pageSize 页面查询数
     * @return
     */
    List<RsmRiskParmDo> selectAllRuleParams(int pageNum, int pageSize);
}
