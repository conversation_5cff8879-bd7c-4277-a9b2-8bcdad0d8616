package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/8/25
 */
public class ChkDlyCntLmt implements CheckRuleTemplate {

    @Override
    public RsmTxCheckCode check(TxRuleParamDo txRuleParamDo, String[] cacheValue, BigDecimal txAmt) {

        int dayTotCnt = Integer.parseInt(cacheValue[1]);

        int dlyCntLmt = txRuleParamDo.getDlyCntLmt();

        if ((dayTotCnt + 1) > dlyCntLmt) {
            return RsmTxCheckCode.MORE_THAN_DAILY_CUMULATE_COUNT;
        }

        return RsmTxCheckCode.PASS;
    }
}
