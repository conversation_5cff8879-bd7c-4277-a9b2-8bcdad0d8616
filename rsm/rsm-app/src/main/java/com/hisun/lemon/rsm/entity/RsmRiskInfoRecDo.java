package com.hisun.lemon.rsm.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 风险信息记录信息对象
 */
public class RsmRiskInfoRecDo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.rec_id
     *
     * @mbggenerated
     */
    private String recId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.user_id
     *
     * @mbggenerated
     */
    private String userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.user_typ
     *
     * @mbggenerated
     */
    private String userTyp;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.tx_User_id
     *
     * @mbggenerated
     */
    private String txUserId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.tx_User_typ
     *
     * @mbggenerated
     */
    private String txUserTyp;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.cap_flw
     *
     * @mbggenerated
     */
    private String capFlw;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.tx_typ
     *
     * @mbggenerated
     */
    private String txTyp;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.tx_cnl
     *
     * @mbggenerated
     */
    private String txCnl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.tx_sts
     *
     * @mbggenerated
     */
    private String txSts;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.tx_dt
     *
     * @mbggenerated
     */
    private LocalDate txDt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.tx_tm
     *
     * @mbggenerated
     */
    private LocalTime txTm;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.tx_amt
     *
     * @mbggenerated
     */
    private BigDecimal txAmt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.ccy
     *
     * @mbggenerated
     */
    private String ccy;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.crd_typ
     *
     * @mbggenerated
     */
    private String crdTyp;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column rsm_risk_info_rec.ip_addr
     *
     * @mbggenerated
     */
    private String ipAddr;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.rec_id
     *
     * @return the value of rsm_risk_info_rec.rec_id
     * @mbggenerated
     */
    public String getRecId() {
        return recId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.rec_id
     *
     * @param recId the value for rsm_risk_info_rec.rec_id
     * @mbggenerated
     */
    public void setRecId(String recId) {
        this.recId = recId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.user_id
     *
     * @return the value of rsm_risk_info_rec.user_id
     * @mbggenerated
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.user_id
     *
     * @param userId the value for rsm_risk_info_rec.user_id
     * @mbggenerated
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.user_typ
     *
     * @return the value of rsm_risk_info_rec.user_typ
     * @mbggenerated
     */
    public String getUserTyp() {
        return userTyp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.user_typ
     *
     * @param userTyp the value for rsm_risk_info_rec.user_typ
     * @mbggenerated
     */
    public void setUserTyp(String userTyp) {
        this.userTyp = userTyp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.tx_User_id
     *
     * @return the value of rsm_risk_info_rec.tx_User_id
     * @mbggenerated
     */
    public String getTxUserId() {
        return txUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.tx_User_id
     *
     * @param txUserId the value for rsm_risk_info_rec.tx_User_id
     * @mbggenerated
     */
    public void setTxUserId(String txUserId) {
        this.txUserId = txUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.tx_User_typ
     *
     * @return the value of rsm_risk_info_rec.tx_User_typ
     * @mbggenerated
     */
    public String getTxUserTyp() {
        return txUserTyp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.tx_User_typ
     *
     * @param txUserTyp the value for rsm_risk_info_rec.tx_User_typ
     * @mbggenerated
     */
    public void setTxUserTyp(String txUserTyp) {
        this.txUserTyp = txUserTyp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.cap_flw
     *
     * @return the value of rsm_risk_info_rec.cap_flw
     * @mbggenerated
     */
    public String getCapFlw() {
        return capFlw;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.cap_flw
     *
     * @param capFlw the value for rsm_risk_info_rec.cap_flw
     * @mbggenerated
     */
    public void setCapFlw(String capFlw) {
        this.capFlw = capFlw;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.tx_typ
     *
     * @return the value of rsm_risk_info_rec.tx_typ
     * @mbggenerated
     */
    public String getTxTyp() {
        return txTyp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.tx_typ
     *
     * @param txTyp the value for rsm_risk_info_rec.tx_typ
     * @mbggenerated
     */
    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.tx_cnl
     *
     * @return the value of rsm_risk_info_rec.tx_cnl
     * @mbggenerated
     */
    public String getTxCnl() {
        return txCnl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.tx_cnl
     *
     * @param txCnl the value for rsm_risk_info_rec.tx_cnl
     * @mbggenerated
     */
    public void setTxCnl(String txCnl) {
        this.txCnl = txCnl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.tx_sts
     *
     * @return the value of rsm_risk_info_rec.tx_sts
     * @mbggenerated
     */
    public String getTxSts() {
        return txSts;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.tx_sts
     *
     * @param txSts the value for rsm_risk_info_rec.tx_sts
     * @mbggenerated
     */
    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.tx_dt
     *
     * @return the value of rsm_risk_info_rec.tx_dt
     * @mbggenerated
     */
    public LocalDate getTxDt() {
        return txDt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.tx_dt
     *
     * @param txDt the value for rsm_risk_info_rec.tx_dt
     * @mbggenerated
     */
    public void setTxDt(LocalDate txDt) {
        this.txDt = txDt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.tx_tm
     *
     * @return the value of rsm_risk_info_rec.tx_tm
     * @mbggenerated
     */
    public LocalTime getTxTm() {
        return txTm;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.tx_tm
     *
     * @param txTm the value for rsm_risk_info_rec.tx_tm
     * @mbggenerated
     */
    public void setTxTm(LocalTime txTm) {
        this.txTm = txTm;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.tx_amt
     *
     * @return the value of rsm_risk_info_rec.tx_amt
     * @mbggenerated
     */
    public BigDecimal getTxAmt() {
        return txAmt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.tx_amt
     *
     * @param txAmt the value for rsm_risk_info_rec.tx_amt
     * @mbggenerated
     */
    public void setTxAmt(BigDecimal txAmt) {
        this.txAmt = txAmt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.ccy
     *
     * @return the value of rsm_risk_info_rec.ccy
     * @mbggenerated
     */
    public String getCcy() {
        return ccy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.ccy
     *
     * @param ccy the value for rsm_risk_info_rec.ccy
     * @mbggenerated
     */
    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.crd_typ
     *
     * @return the value of rsm_risk_info_rec.crd_typ
     * @mbggenerated
     */
    public String getCrdTyp() {
        return crdTyp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.crd_typ
     *
     * @param crdTyp the value for rsm_risk_info_rec.crd_typ
     * @mbggenerated
     */
    public void setCrdTyp(String crdTyp) {
        this.crdTyp = crdTyp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column rsm_risk_info_rec.ip_addr
     *
     * @return the value of rsm_risk_info_rec.ip_addr
     * @mbggenerated
     */
    public String getIpAddr() {
        return ipAddr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column rsm_risk_info_rec.ip_addr
     *
     * @param ipAddr the value for rsm_risk_info_rec.ip_addr
     * @mbggenerated
     */
    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }

    @Override
    public String toString() {
        return "RsmRiskInfoRecDo{" +
                "recId='" + recId + '\'' +
                ", userId='" + userId + '\'' +
                ", userTyp='" + userTyp + '\'' +
                ", txUserId='" + txUserId + '\'' +
                ", txUserTyp='" + txUserTyp + '\'' +
                ", capFlw='" + capFlw + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", txCnl='" + txCnl + '\'' +
                ", txSts='" + txSts + '\'' +
                ", txDt=" + txDt +
                ", txTm=" + txTm +
                ", txAmt=" + txAmt +
                ", ccy='" + ccy + '\'' +
                ", crdTyp='" + crdTyp + '\'' +
                ", ipAddr='" + ipAddr + '\'' +
                '}';
    }
}