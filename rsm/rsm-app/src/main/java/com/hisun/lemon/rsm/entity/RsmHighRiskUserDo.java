package com.hisun.lemon.rsm.entity;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
public class RsmHighRiskUserDo {

    private String userId;
    private String txTyp;
    private String riskDesc;
    private String riskOpr;
    private String riskSorc;
    private String riskParm;
    private String oprSts;
    private String txOrdNo;
    private LocalDateTime createTime;
    private LocalDateTime modifyTime;

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getRiskDesc() {
        return riskDesc;
    }

    public void setRiskDesc(String riskDesc) {
        this.riskDesc = riskDesc;
    }

    public String getRiskOpr() {
        return riskOpr;
    }

    public void setRiskOpr(String riskOpr) {
        this.riskOpr = riskOpr;
    }

    public String getRiskSorc() {
        return riskSorc;
    }

    public void setRiskSorc(String riskSorc) {
        this.riskSorc = riskSorc;
    }

    public String getOprSts() {
        return oprSts;
    }

    public void setOprSts(String oprSts) {
        this.oprSts = oprSts;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRiskParm() {
        return riskParm;
    }

    public void setRiskParm(String riskParm) {
        this.riskParm = riskParm;
    }

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RsmHighRiskUserDo{" +
                "userId='" + userId + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", riskDesc='" + riskDesc + '\'' +
                ", riskOpr='" + riskOpr + '\'' +
                ", riskSorc='" + riskSorc + '\'' +
                ", riskParm='" + riskParm + '\'' +
                ", oprSts='" + oprSts + '\'' +
                ", txOrdNo='" + txOrdNo + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
