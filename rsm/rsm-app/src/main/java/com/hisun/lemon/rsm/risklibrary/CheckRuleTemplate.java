package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/8/25
 */
public interface CheckRuleTemplate {

    /**
     * 根据限额参数，累计值，交易金额进行检查
     *
     * @param txRuleParamDo 限额参数
     * @param cacheValue    缓存中累计值
     * @param txAmt         交易金额
     * @return 检查结果
     */
    RsmTxCheckCode check(TxRuleParamDo txRuleParamDo, String[] cacheValue, BigDecimal txAmt);
}
