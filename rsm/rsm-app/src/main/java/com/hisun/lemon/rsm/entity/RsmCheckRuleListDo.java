package com.hisun.lemon.rsm.entity;

import java.time.LocalDateTime;

/**
 * 风控检查规则
 *
 * <AUTHOR>
 * @create 2017/8/4
 */
public class RsmCheckRuleListDo {

    private String checkId;
    private String txTyp;
    private String lmtLvl;
    private String ruleDesc;
    private String oprTyp;
    private String updOprId;
    private LocalDateTime createTime;
    private LocalDateTime modifyTime;

    public String getCheckId() {
        return checkId;
    }

    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getLmtLvl() {
        return lmtLvl;
    }

    public void setLmtLvl(String lmtLvl) {
        this.lmtLvl = lmtLvl;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RsmCheckRuleListDo{" +
                "checkId='" + checkId + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", lmtLvl='" + lmtLvl + '\'' +
                ", ruleDesc='" + ruleDesc + '\'' +
                ", oprTyp='" + oprTyp + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
