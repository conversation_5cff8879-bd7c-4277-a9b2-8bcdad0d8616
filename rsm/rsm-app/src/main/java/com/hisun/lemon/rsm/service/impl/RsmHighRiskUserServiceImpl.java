package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.RsmHighRiskUserMapper;
import com.hisun.lemon.rsm.entity.RsmHighRiskUserDo;
import com.hisun.lemon.rsm.service.IRsmHighRiskUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@Service
@Transactional
public class RsmHighRiskUserServiceImpl extends BaseService implements IRsmHighRiskUserService {

    @Resource
    private RsmHighRiskUserMapper rsmHighRiskUserMapper;

    @Override
    public void addUserToHighList(RsmHighRiskUserDo rsmHighRiskUserDo) {
        rsmHighRiskUserDo.setCreateTime(LocalDateTime.now());
        rsmHighRiskUserDo.setCreateTime(LocalDateTime.now());
        int result = rsmHighRiskUserMapper.addHighRiskList(rsmHighRiskUserDo);
        if (result <= 0) {
            throw new RsmException(MsgCd.INSERT_ERROR);
        }
    }

    @Override
    public List<RsmHighRiskUserDo> queryHighUserList(int pageNum, int pageSize) {
        return PageUtils.pageQuery(pageNum, pageSize,() -> rsmHighRiskUserMapper.queryAll());
    }
}
