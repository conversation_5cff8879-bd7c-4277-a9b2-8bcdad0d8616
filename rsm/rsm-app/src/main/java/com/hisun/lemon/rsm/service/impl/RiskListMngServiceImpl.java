package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmBaseUtil;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.RiskBlackListMapper;
import com.hisun.lemon.rsm.dao.RiskWhiteListMapper;
import com.hisun.lemon.rsm.entity.RiskListDo;
import com.hisun.lemon.rsm.service.IRiskListMngService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/7
 */
@Transactional
@Service
public class RiskListMngServiceImpl extends BaseService implements IRiskListMngService {

    @Resource
    private RiskBlackListMapper riskBlackListMapper;

    @Resource
    private RiskWhiteListMapper riskWhiteListMapper;

    @Override
    public boolean addRiskList(RiskListDo riskListDo, String listTyp) {
        //添加黑白名单时，创建操作员和更新操作员为同一个
        riskListDo.setUpdOprId(riskListDo.getUpdOprId());
        //添加黑白名单时，创建更新时间为系统当前时间
        riskListDo.setCreateTime(LocalDateTime.now());
        riskListDo.setModifyTime(LocalDateTime.now());

        //判断证件类型，获取脱敏值
        if (Constants.ID_TYP_MBL_NO.equals(riskListDo.getIdTyp())) {
            System.out.println(riskListDo.toString());
            riskListDo.setIdHid(RsmBaseUtil.getMblNoHid(riskListDo.getId()));
        } else if (Constants.ID_TYP_CARD.equals(riskListDo.getIdTyp())) {
            riskListDo.setIdHid(RsmBaseUtil.getcrdNoOrIdNoHid(riskListDo.getId()));
            //TODO 身份证加密入库
            riskListDo.setId(riskListDo.getId());
        } else if (Constants.ID_TYP_ID_NO.equals(riskListDo.getIdTyp())) {
            riskListDo.setIdHid(RsmBaseUtil.getcrdNoOrIdNoHid(riskListDo.getId()));
            riskListDo.setCrdNoLast(RsmBaseUtil.getCrdLastNo(riskListDo.getId()));
        } else {
            riskListDo.setIdHid("");
        }

        int result = 0;
        if ("0".equals(listTyp)) {
            riskListDo.setListId(IdGenUtils.generateId("RSM_BLK_LIST", 10));
            result = riskBlackListMapper.insert(riskListDo);
        } else if ("1".equals(listTyp)) {
            riskListDo.setListId(IdGenUtils.generateId("RSM_WHT_LIST", 10));
            result = riskWhiteListMapper.insert(riskListDo);
        }
        if (result <= 0) {
            throw new RsmException(MsgCd.INSERT_ERROR, Constants.RSM_TABLE_RISK_LIST);
        }

        return Constants.SUCCESS;
    }

    @Override
    public boolean deleteRiskList(String listId, String listTyp) {
        int result = 0;
        if ("0".equals(listTyp)) {
            result = riskBlackListMapper.deleteByPrimaryKey(listId);
        } else if ("1".equals(listTyp)) {
            result = riskWhiteListMapper.deleteByPrimaryKey(listId);
        }
        if (result <= 0) {
            throw new RsmException(MsgCd.DELETE_ERROR, Constants.RSM_TABLE_RISK_LIST);
        }
        return Constants.SUCCESS;
    }

    @Override
    public RiskListDo queryRiskList(String listId, String listTyp) {
        RiskListDo riskListDo = new RiskListDo();
        if ("0".equals(listTyp)) {
            riskListDo = riskBlackListMapper.selectByPrimaryKey(listId);
        } else if ("1".equals(listTyp)) {
            riskListDo = riskWhiteListMapper.selectByPrimaryKey(listId);
        }
        if ("".equals(riskListDo.getId())) {
            throw new RsmException(MsgCd.SELECT_ERROR, Constants.RSM_TABLE_RISK_LIST);
        }
        return riskListDo;
    }

    @Override
    public List<RiskListDo> queryAllRiskLists(String listTyp, int pageNum, int pageSize) {
        List<RiskListDo> list = null;
        if ("0".equals(listTyp)) {
            list = PageUtils.pageQuery(pageNum, pageSize, () -> riskBlackListMapper.selectAllLists());
        } else if ("1".equals(listTyp)) {
            list = PageUtils.pageQuery(pageNum, pageSize, () -> riskWhiteListMapper.selectAllLists());
        }
        if (JudgeUtils.isNull(list)) {
            throw new RsmException(MsgCd.SELECT_ERROR, Constants.RSM_TABLE_RISK_LIST);
        }
        return list;
    }

    @Override
    public boolean updateRiskList(RiskListDo riskListDo, String listTyp) {
        //更新时间以系统当前时间
        riskListDo.setUpdOprId(riskListDo.getUpdOprId());
        riskListDo.setModifyTime(LocalDateTime.now());
        int result = 0;
        if ("0".equals(listTyp)) {
            result = riskBlackListMapper.updateByPrimaryKeySelective(riskListDo);
        } else if ("1".equals(listTyp)) {
            result = riskWhiteListMapper.updateByPrimaryKeySelective(riskListDo);
        }
        if (result <= 0) {
            throw new RsmException(MsgCd.UPDATE_ERROR, Constants.RSM_TABLE_RISK_LIST);
        }
        return Constants.SUCCESS;
    }
}
