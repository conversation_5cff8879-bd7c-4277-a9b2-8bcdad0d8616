package com.hisun.lemon.rsm.controller;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dto.req.riskparam.*;
import com.hisun.lemon.rsm.dto.res.RiskRuleParamResDTO;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 可疑交易规则参数管理
 *
 * <AUTHOR>
 * @create 2017/8/3
 */
@Api(value = "可疑交易规则参数管理", tags = "可疑交易规则参数")
@RestController
@RequestMapping(value = "/rsm/riskparam")
public class RiskRuleParamMngController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RiskRuleParamMngController.class);

    @Resource
    private IRsmRiskParamService rsmRiskParamService;

    @ApiOperation(value = "添加风险规则参数", notes = "添加风险规则参数")
    @ApiResponse(code = 200, message = "添加风险规则参数结果")
    @PostMapping(value = "/param")
    public GenericRspDTO<NoBody> addRiskRuleParam(@Validated @RequestBody GenericDTO<RiskRuleParamInsertReqDTO> reqDTO) {
        RiskRuleParamInsertReqDTO riskRuleParamInsertReqDTO = reqDTO.getBody();
        RsmRiskParmDo rsmRiskParmDo = new RsmRiskParmDo();
        BeanUtils.copyProperties(rsmRiskParmDo, riskRuleParamInsertReqDTO);
        try {
            rsmRiskParamService.addRuleParam(rsmRiskParmDo);
        } catch (RsmException e) {
            logger.error("Add risk param error：" + e);
            return GenericRspDTO.newInstance(MsgCd.INSERT_ERROR.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "修改风险规则参数", notes = "修改风险规则参数")
    @ApiResponse(code = 200, message = "修改风险规则参数")
    @PutMapping(value = "/param")
    public GenericRspDTO<NoBody> updateRiskRuleParam(@Validated @RequestBody GenericDTO<RiskRuleParamUpdateReqDTO> reqDTO) {
        RiskRuleParamUpdateReqDTO riskRuleParamUpdateReqDTO = reqDTO.getBody();
        RsmRiskParmDo rsmRiskParmDo = new RsmRiskParmDo();
        riskRuleParamUpdateReqDTO.setParmId(riskRuleParamUpdateReqDTO.getParmId());
        BeanUtils.copyProperties(rsmRiskParmDo, riskRuleParamUpdateReqDTO);
        try {
            rsmRiskParamService.updateRuleParam(rsmRiskParmDo);
        } catch (RsmException e) {
            logger.error("Update risk param error：" + e);
            return GenericRspDTO.newInstance(MsgCd.UPDATE_ERROR.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "删除风险规则参数", notes = "删除风险规则参数")
    @ApiResponse(code = 200, message = "删除风险规则参数操作结果")
    @DeleteMapping(value = "/param")
    public GenericRspDTO<NoBody> deleteRiskRuleParam(@Validated @RequestBody GenericDTO<RiskRuleParamBaseReqDTO> reqDTO) {
        RiskRuleParamBaseReqDTO riskRuleParamBaseReqDTO = reqDTO.getBody();
        try {
            rsmRiskParamService.deleteRuleParam(riskRuleParamBaseReqDTO.getParmId());
        } catch (RsmException e) {
            logger.error("删除交易限额规则参数错误：" + e);
            return GenericRspDTO.newInstance(MsgCd.DELETE_ERROR.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询风险规则参数", notes = "查询风险规则参数")
    @ApiResponse(code = 200, message = "查询风险规则参数操作结果")
    @GetMapping(value = "/param")
    public GenericRspDTO<RiskRuleParamResDTO> queryRiskRuleParam(
            @Validated RiskRuleParamQueryReqDTO riskRuleParamQueryReqDTO) {
        GenericRspDTO<RiskRuleParamResDTO> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        RiskRuleParamResDTO riskRuleParamResDTO = new RiskRuleParamResDTO();
        RsmRiskParmDo rsmRiskParmDo;
        try {
            rsmRiskParmDo = rsmRiskParamService.queryRuleParam(riskRuleParamQueryReqDTO.getParmId());
            BeanUtils.copyProperties(riskRuleParamResDTO, rsmRiskParmDo);
        } catch (RsmException e) {
            logger.error("查询风险规则参数操作错误：" + e);
            genericDTO.setMsgCd(MsgCd.SELECT_ERROR.getMsgCd());
            return genericDTO;
        }
        return genericDTO;
    }

    @ApiOperation(value = "查询风险规则参数列表", notes = "查询风险规则参数列表")
    @ApiResponse(code = 200, message = "查询风险规则参数列表结果")
    @GetMapping(value = "/param/list")
    public GenericRspDTO<List<RiskRuleParamResDTO>> queryAllRiskRuleParams(
            @Validated RiskRuleParamQueryAllReqDTO riskRuleParamQueryAllReqDTO) {
        GenericRspDTO<List<RiskRuleParamResDTO>> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        List<RiskRuleParamResDTO> listDtos = new ArrayList<>();
        List<RsmRiskParmDo> listDos;
        int pageNum = Constants.DEFAULT_PAGE_NUMBER;
        int pageSize = Constants.DEFAULT_PAGE_SIZE;
        if (JudgeUtils.isNotNull(riskRuleParamQueryAllReqDTO.getPageNum())) {
            pageNum = riskRuleParamQueryAllReqDTO.getPageNum();
        }
        if (JudgeUtils.isNotNull(riskRuleParamQueryAllReqDTO.getPageSize())) {
            pageSize = riskRuleParamQueryAllReqDTO.getPageSize();
        }
        try {
            listDos = rsmRiskParamService.selectAllRuleParams(pageNum, pageSize);
            for (RsmRiskParmDo rsmRiskParmDo : listDos) {
                RiskRuleParamResDTO riskRuleParamResDTO = new RiskRuleParamResDTO();
                BeanUtils.copyProperties(riskRuleParamResDTO, rsmRiskParmDo);
                listDtos.add(riskRuleParamResDTO);
            }
            genericDTO.setBody(listDtos);
        } catch (RsmException e) {
            logger.error("查询风险规则参数列表错误：" + e);
            genericDTO.setMsgCd(MsgCd.SELECT_ERROR.getMsgCd());
        }
        return genericDTO;
    }
}
