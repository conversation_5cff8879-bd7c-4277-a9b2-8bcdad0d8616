package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.entity.RsmHighRiskUserDo;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmHighRiskUserService;
import com.hisun.lemon.rsm.service.IRsmRiskInfoRecService;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户客户X天内受理单笔超过X万元的大额信用卡交易超过X笔
 * 参数1：X天
 * 参数2：X元
 * 参数3：超过额度笔数
 *
 * <AUTHOR>
 * @create 2017/7/21
 */
@Component
public class RsmRules003 implements SpecialRuleTemplate {

    private static final Logger logger = LoggerFactory.getLogger(RsmRules003.class);

    @Resource
    private IRsmRiskInfoRecService rsmRiskInfoRecService;

    @Resource
    private IRsmRiskParamService rsmRiskParamService;

    @Resource
    private IRsmHighRiskUserService rsmHighRiskUserService;

    @Override
    public void execute(TxCheckRiskBo txCheckRiskBo) {
        //如果风险参数不存在直接跳过
        RsmRiskParmDo rsmRiskParmDo = rsmRiskParamService.queryRuleParam("RsmRules003");
        if (JudgeUtils.isNull(rsmRiskParmDo)) {
            return;
        }
        //参数1，时间天数
        int dayAgo = Integer.valueOf(rsmRiskParmDo.getParm1());
        //参数2，风险额度
        BigDecimal riskMaxAmt = new BigDecimal(rsmRiskParmDo.getParm2());
        //参数3，笔数
        int txNum = Integer.valueOf(rsmRiskParmDo.getParm3());

        if (JudgeUtils.equals(txCheckRiskBo.getTxTyp(), Constants.TX_TYP_CONSUME)) {
            List<RsmRiskInfoRecDo> recList = rsmRiskInfoRecService.queryRiskInfoRec(txCheckRiskBo.getStlUserId(),
                    Constants.CAP_FLW_IN, dayAgo);
            if (recList.size() == 0) {
                return;
            }
            int num = 0;
            for (RsmRiskInfoRecDo rec : recList) {
                if (JudgeUtils.equals(rec.getCrdTyp(), Constants.CRD_TYP_CREDIT_CARD)) {
                    if (rec.getTxAmt().compareTo(riskMaxAmt) > 0) {
                        num++;
                    }
                }
            }
            if (num > txNum) {
                logger.warn(RsmRules003.class.getName());
                RsmHighRiskUserDo rsmHighRiskUserDo = new RsmHighRiskUserDo();
                rsmHighRiskUserDo.setUserId(txCheckRiskBo.getStlUserId());
                rsmHighRiskUserDo.setTxTyp(txCheckRiskBo.getTxTyp());
                rsmHighRiskUserDo.setRiskOpr(Constants.RISK_OPR_CNT);
                //TODO 风险描述，或多语言支持
                rsmHighRiskUserDo.setRiskDesc("123");
                rsmHighRiskUserDo.setOprSts(Constants.OPR_STS_RISK);
                rsmHighRiskUserDo.setRiskSorc(Constants.RISK_SORC);
                rsmHighRiskUserDo.setTxOrdNo(txCheckRiskBo.getTxOrdNo());
                rsmHighRiskUserDo.setRiskParm(this.getClass().getSimpleName());
                rsmHighRiskUserDo.setCreateTime(LocalDateTime.now());
                rsmHighRiskUserDo.setModifyTime(LocalDateTime.now());
                rsmHighRiskUserService.addUserToHighList(rsmHighRiskUserDo);
            }
        }
    }
}
