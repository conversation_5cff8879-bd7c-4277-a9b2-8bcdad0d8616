package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.entity.TxRuleParamDo;

import java.util.List;

/**
 * 风控参数
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
public interface ITxRuleParamMngService {

    /**
     * 增加交易限额参数
     *
     * @param txRuleParamDo 交易限额参数对象
     * @return
     */
    boolean addRuleParam(TxRuleParamDo txRuleParamDo);

    /**
     * 删除交易限额参数
     *
     * @param parmId 交易限额参数id
     * @return
     */
    boolean deleteRuleParam(String parmId);

    /**
     * 查询交易限额参数
     *
     * @param parmId 交易限额参数id
     * @return
     */
    TxRuleParamDo queryRuleParam(String parmId);

    /**
     * 更新交易限额参数
     *
     * @param txRuleParamDo 交易限额参数对象
     * @return
     */
    boolean updateRuleParam(TxRuleParamDo txRuleParamDo);

    /**
     * 查询所有交易限额参数列表
     *
     * @param pageNum  页数
     * @param pageSize 页面查询数
     * @return
     */
    List<TxRuleParamDo> selectAllRuleParams(int pageNum, int pageSize);

    /**
     * 精确匹配
     *
     * @param dcPtyFlg 收付款标志
     * @param txTyp    交易类型
     * @param payTyp   支付类型
     * @param mercLvl  商户等级
     * @return
     */
    List<TxRuleParamDo> exactMatchParam(String dcPtyFlg, String txTyp, String payTyp, String mercLvl);
}
