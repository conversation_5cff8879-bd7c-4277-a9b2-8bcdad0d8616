package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.ReflectionUtils;
import com.hisun.lemon.framework.cumulative.Cumulative;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.entity.*;
import com.hisun.lemon.rsm.risklibrary.CheckRuleTemplate;
import com.hisun.lemon.rsm.service.*;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.RsmBaseUtil;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实时风控相关，参考来自Picc代码
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
@Service
@Transactional
public class TxCheckRiskServiceImpl extends BaseService implements ITxCheckRiskService {

    private static final Logger logger = LoggerFactory.getLogger(ITxCheckRiskService.class);

    @Resource
    private Cumulative cumulative;

    @Resource
    private IRiskRuleMngService riskRuleMngService;

    @Resource
    private ITxRuleParamMngService txRuleParamMngService;

    @Resource
    private IRsmCheckRuleListService rsmCheckRuleListService;

    @Resource
    private IAntiMoneyLaunderingSerivce antiMoneyLaunderingSerivce;

    @Resource
    private IRsmChkPswLmtService rsmChkPswLmtService;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Override
    public RsmTxCheckCode riskRealTimeCheck(TxCheckRiskBo txCheckRiskBo) {
        logger.debug("Data：" + txCheckRiskBo.toString());
        //交易类型为空时默认检测全部
        if (JudgeUtils.isNull(txCheckRiskBo.getTxSts()) ||
                JudgeUtils.equals(txCheckRiskBo.getTxTyp(), Constants.TX_TYP_ALL)) {
            txCheckRiskBo.setTxTyp(Constants.TX_TYP_ALL);
        }

        //免密支付检查，免密支付标志
//        if (JudgeUtils.isNotNull(txCheckRiskBo.getTxData())) {
//            Map data = RsmBaseUtil.string2Map(txCheckRiskBo.getTxData());
//            String pswFlg = null;
//            if (data != null) {
//                pswFlg = (String) data.get("pswFlg");
//            }
//            if (JudgeUtils.equals(txCheckRiskBo.getTxTyp(), Constants.TX_TYP_CONSUME)
//                    && JudgeUtils.equals(pswFlg, Constants.PSW_FLG_ENABLE)) {
//                rsmChkPswLmtService.checkCumlativeRswAmtLmt(txCheckRiskBo.getPayUserId(), txCheckRiskBo.getTxAmt());
//            }
//        }

        //新建待检查规则列表
        Map<RiskRuleDo, String> checkRuleList;
        //装填检查规则
        checkRuleList = getCheckRule(txCheckRiskBo);
        if (checkRuleList == null || checkRuleList.isEmpty()) {
            logger.debug("There is no risk rule to be checked");
            return RsmTxCheckCode.PASS;
        }
        //循环遍历待检查规则

        for (Map.Entry<RiskRuleDo, String> entry : checkRuleList.entrySet()) {
            String cacheValue = getCacheValueByRule(txCheckRiskBo, entry.getKey());
            String[] cache = cacheValue.split("\\|");
            String[] cache1 = {cache[0], cache[1], cache[2], cache[3]};
            String[] cache2 = new String[0];
            if (cache.length > 5) {
                cache2 = new String[]{cache[4], cache[5], cache[6], cache[7]};
            }

            RsmTxCheckCode checkResult = RsmTxCheckCode.PASS;
            switch (entry.getKey().getDcPtyFlg()) {
                case Constants.DC_PTY_FLG_STL:
                    if (JudgeUtils.isEmpty(txCheckRiskBo.getStlUserId())) {
                        break;
                    }
                    checkResult = checkRuleProc(cache1, entry.getKey(), txCheckRiskBo,
                            getMercLvl(txCheckRiskBo.getStlUserId(), txCheckRiskBo.getStlUserTyp()));
                    break;
                case Constants.DC_PTY_FLG_PAY:
                    if (JudgeUtils.isEmpty(txCheckRiskBo.getPayUserId())) {
                        break;
                    }
                    checkResult = checkRuleProc(cache1, entry.getKey(), txCheckRiskBo,
                            getMercLvl(txCheckRiskBo.getPayUserId(), txCheckRiskBo.getPayUserTyp()));
                    break;
                case Constants.DC_PTY_FLG_ALL:
                    if (JudgeUtils.isEmpty(txCheckRiskBo.getPayUserId())) {
                        break;
                    }
                    checkResult = checkRuleProc(cache1, entry.getKey(), txCheckRiskBo,
                            getMercLvl(txCheckRiskBo.getPayUserId(), txCheckRiskBo.getPayUserTyp()));
                    if (JudgeUtils.equals(checkResult, RsmTxCheckCode.PASS.getCode())) {
                        if (JudgeUtils.isEmpty(txCheckRiskBo.getStlUserId())) {
                            break;
                        }
                        checkResult = checkRuleProc(cache2, entry.getKey(), txCheckRiskBo,
                                getMercLvl(txCheckRiskBo.getPayUserId(), txCheckRiskBo.getStlUserTyp()));
                    }
                    break;
            }

            if (checkResult.getCode() != RsmTxCheckCode.PASS.getCode()) {
                //或许根据操作类型返回拒绝或其他操作的错误码
                return checkResult;
            } else {
                continue;
            }
        }
        return RsmTxCheckCode.PASS;
    }

    private String getMercLvl(String userId, String idTyp) {
        if (JudgeUtils.notEquals(idTyp, Constants.ID_TYP_USER_NO)) {
            return Constants.LIMIT_LEVEL_USER;
        }
        GenericRspDTO<UserBasicInfDTO> userInfDTO = userBasicInfClient.queryUser(userId);
        UserBasicInfDTO userBasicInfDTO = userInfDTO.getBody();
        if (JudgeUtils.isEmpty(userBasicInfDTO.getMerLvl())) {
            return Constants.LIMIT_LEVEL_USER;
        }
        return userBasicInfDTO.getMerLvl();
    }

    @Override
    public RsmTxCheckCode rsmNotRealTimeCheck(TxCheckRiskBo txCheckRiskBo) {
        logger.debug("Data：" + txCheckRiskBo.toString());
        //交易类型为空时默认检测全部
        if (JudgeUtils.isNull(txCheckRiskBo.getTxSts()) || JudgeUtils.equals(txCheckRiskBo.getTxTyp(), "0")) {
            txCheckRiskBo.setTxTyp("0");
        }

        //交易类型为退款时，累计额度返回
        if (JudgeUtils.equals(txCheckRiskBo.getTxTyp(), Constants.TX_TYP_REFUNDS)) {
            txCheckRiskBo.setTxAmt(txCheckRiskBo.getTxAmt().multiply(new BigDecimal("-1")));
        }
//        免密支付额度统计
//        rsmChkPswLmtService.cumulativePswAmtLmt(txCheckRiskBo.getPayUserId(), txCheckRiskBo.getTxAmt());
        cumulateRuleProc(txCheckRiskBo);

        antiMoneyLaunderingSerivce.checkAndRecord(txCheckRiskBo);

        return RsmTxCheckCode.PASS;
    }

    /**
     * 风控限额比较
     *
     * @param cacheValue 缓存中累计值
     * @param riskRuleDo 风控规则
     * @param bo         交易对象
     * @return RsmTxCheckCode 返回码
     */
    private RsmTxCheckCode checkRuleProc(String[] cacheValue, RiskRuleDo riskRuleDo, TxCheckRiskBo bo, String mercLvl) {
        //从缓存中，根据风控规则，检查规则的限制维度，查询风控参数限额信息
        List<TxRuleParamDo> list = null;
        try {
            list = txRuleParamMngService.exactMatchParam(riskRuleDo.getDcPtyFlg(), bo.getTxTyp(), bo.getPayTyp(), mercLvl);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        if (null == list || list.isEmpty()) {
            return RsmTxCheckCode.PASS;
        }

        for (TxRuleParamDo txParmInfo : list) {
            //当限额参数不存在时直接跳过
            if (JudgeUtils.isNull(txParmInfo) || txParmInfo == null) {
                return RsmTxCheckCode.PASS;
            }

            String cpnNm = riskRuleDo.getCpnNm();
            //单笔金额限额，只检查全订单
//            if (JudgeUtils.equals(cpnNm, "com.hisun.lemon.rsm.risklibrary.ChkSglAmtLmt")
//                    && JudgeUtils.notEquals(bo.getPayTyp(), Constants.PAY_TYP_ALL)) {
//                return RsmTxCheckCode.PASS;
//            }

            try {
                Class ruleClass = ReflectionUtils.forName(cpnNm);
                CheckRuleTemplate rule = (CheckRuleTemplate) ruleClass.newInstance();
                RsmTxCheckCode r = rule.check(txParmInfo, cacheValue, bo.getTxAmt());
                return r;
            } catch (IllegalAccessException | InstantiationException e) {
                logger.error(e.getMessage());
                logger.error(MsgCd.CPN_NM_INSTANCE_NOT_EXIST.getMsgCd(), MsgCd.CPN_NM_INSTANCE_NOT_EXIST.getMsgInfo());
            }
        }

        return RsmTxCheckCode.PASS;
    }

    /**
     * 进行累计
     *
     * @param txCheckRiskBo 实时风控传入对象
     */
    private void cumulateRuleProc(TxCheckRiskBo txCheckRiskBo) {
        logger.debug(txCheckRiskBo.toString());
        String cnt = "1";
        if (JudgeUtils.equals(Constants.TX_TYP_REFUNDS, txCheckRiskBo.getTxTyp())) {
            String data = txCheckRiskBo.getTxData();
            Map map = RsmBaseUtil.string2Map(data);
            txCheckRiskBo.setTxTyp((String) map.get("orgTxTyp"));
            cnt = "0";
        }

        //付方累计
        cumulative.countByDay(
                "RSM_DLY_TOT_AMT" + txCheckRiskBo.getTxDt().toString(),
                new Cumulative.Dimension(
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp(),
                        RsmBaseUtil.mulHundred(txCheckRiskBo.getTxAmt()).toString()));
        cumulative.countByDay(
                "RSM_DLY_TOT_CNT" + txCheckRiskBo.getTxDt().toString(),
                new Cumulative.Dimension(
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp(), cnt)
        );
        cumulative.countByMonth(
                "RSM_MLY_TOT_AMT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                new Cumulative.Dimension(
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp(),
                        RsmBaseUtil.mulHundred(txCheckRiskBo.getTxAmt()).toString())
        );
        cumulative.countByMonth(
                "RSM_MLY_TOT_CNT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                new Cumulative.Dimension(
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp(), cnt)
        );

        //收方累计
        if (JudgeUtils.equals(txCheckRiskBo.getTxTyp(), Constants.TX_TYP_CONSUME)
                || JudgeUtils.equals(txCheckRiskBo.getTxTyp(), Constants.TX_TYP_TRANSFER)) {
            cumulative.countByDay(
                    "RSM_DLY_TOT_AMT" + txCheckRiskBo.getTxDt().toString(),
                    new Cumulative.Dimension(
                            txCheckRiskBo.getStlUserId() + "."
                                    + txCheckRiskBo.getStlUserTyp() + "."
                                    + txCheckRiskBo.getPayTyp() + "."
                                    + Constants.DC_PTY_FLG_STL + "."
                                    + txCheckRiskBo.getTxTyp(),
                            RsmBaseUtil.mulHundred(txCheckRiskBo.getTxAmt()).toString()));
            cumulative.countByDay(
                    "RSM_DLY_TOT_CNT" + txCheckRiskBo.getTxDt().toString(),
                    new Cumulative.Dimension(
                            txCheckRiskBo.getStlUserId() + "."
                                    + txCheckRiskBo.getStlUserTyp() + "."
                                    + txCheckRiskBo.getPayTyp() + "."
                                    + Constants.DC_PTY_FLG_STL + "."
                                    + txCheckRiskBo.getTxTyp(), cnt)
            );
            cumulative.countByMonth(
                    "RSM_MLY_TOT_AMT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                    new Cumulative.Dimension(
                            txCheckRiskBo.getStlUserId() + "."
                                    + txCheckRiskBo.getStlUserTyp() + "."
                                    + txCheckRiskBo.getPayTyp() + "."
                                    + Constants.DC_PTY_FLG_STL + "."
                                    + txCheckRiskBo.getTxTyp(),
                            RsmBaseUtil.mulHundred(txCheckRiskBo.getTxAmt()).toString())
            );
            cumulative.countByMonth(
                    "RSM_MLY_TOT_CNT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                    new Cumulative.Dimension(
                            txCheckRiskBo.getStlUserId() + "."
                                    + txCheckRiskBo.getStlUserTyp() + "."
                                    + txCheckRiskBo.getPayTyp() + "."
                                    + Constants.DC_PTY_FLG_STL + "."
                                    + txCheckRiskBo.getTxTyp(), cnt)
            );
        }

    }

    /**
     * 根据规则从缓存中获取用户累计值
     *
     * @param txCheckRiskBo 实时风控传入对象
     * @param riskRuleDo    风控规则对象
     * @return String 日累计值|月累计值
     */
    private String getCacheValueByRule(TxCheckRiskBo txCheckRiskBo, RiskRuleDo riskRuleDo) {
        String dayTotAmt = null;
        String monthTotAmt = null;
        String dayTotCnt = null;
        String monthTotCnt = null;

        //双控时，返回多另一方的累计信息
        String dayTotAmt_other = null;
        String monthTotAmt_other = null;
        String dayTotCnt_other = null;
        String monthTotCnt_other = null;

        switch (riskRuleDo.getDcPtyFlg()) {
            case Constants.DC_PTY_FLG_STL:
                dayTotAmt = cumulative.queryByDay(
                        "RSM_DLY_TOT_AMT" + txCheckRiskBo.getTxDt().toString(),
                        txCheckRiskBo.getStlUserId() + "."
                                + txCheckRiskBo.getStlUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_STL + "."
                                + txCheckRiskBo.getTxTyp());
                monthTotAmt = cumulative.queryByMonth(
                        "RSM_MLY_TOT_AMT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                        txCheckRiskBo.getStlUserId() + "."
                                + txCheckRiskBo.getStlUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_STL + "."
                                + txCheckRiskBo.getTxTyp());
                dayTotCnt = cumulative.queryByDay(
                        "RSM_DLY_TOT_CNT" + txCheckRiskBo.getTxDt().toString(),
                        txCheckRiskBo.getStlUserId() + "."
                                + txCheckRiskBo.getStlUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_STL + "."
                                + txCheckRiskBo.getTxTyp());
                monthTotCnt = cumulative.queryByMonth(
                        "RSM_MLY_TOT_CNT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                        txCheckRiskBo.getStlUserId() + "."
                                + txCheckRiskBo.getStlUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_STL + "."
                                + txCheckRiskBo.getTxTyp());
                break;
            case Constants.DC_PTY_FLG_PAY:
                dayTotAmt = cumulative.queryByDay(
                        "RSM_DLY_TOT_AMT" + txCheckRiskBo.getTxDt().toString(),
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp());
                monthTotAmt = cumulative.queryByMonth(
                        "RSM_MLY_TOT_AMT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp());
                dayTotCnt = cumulative.queryByDay(
                        "RSM_DLY_TOT_CNT" + txCheckRiskBo.getTxDt().toString(),
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp());
                monthTotCnt = cumulative.queryByMonth(
                        "RSM_MLY_TOT_CNT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp());
                break;
            case Constants.DC_PTY_FLG_ALL:
                dayTotAmt = cumulative.queryByDay(
                        "RSM_DLY_TOT_AMT" + txCheckRiskBo.getTxDt().toString(),
                        txCheckRiskBo.getStlUserId() + "."
                                + txCheckRiskBo.getStlUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_STL + "."
                                + txCheckRiskBo.getTxTyp());
                monthTotAmt = cumulative.queryByMonth(
                        "RSM_MLY_TOT_AMT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                        txCheckRiskBo.getStlUserId() + "."
                                + txCheckRiskBo.getStlUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_STL + "."
                                + txCheckRiskBo.getTxTyp());
                dayTotCnt = cumulative.queryByDay(
                        "RSM_DLY_TOT_CNT" + txCheckRiskBo.getTxDt().toString(),
                        txCheckRiskBo.getStlUserId() + "."
                                + txCheckRiskBo.getStlUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_STL + "."
                                + txCheckRiskBo.getTxTyp());
                monthTotCnt = cumulative.queryByMonth(
                        "RSM_MLY_TOT_CNT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                        txCheckRiskBo.getStlUserId() + "."
                                + txCheckRiskBo.getStlUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_STL + "."
                                + txCheckRiskBo.getTxTyp());
                dayTotAmt_other = cumulative.queryByDay(
                        "RSM_DLY_TOT_AMT" + txCheckRiskBo.getTxDt().toString(),
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp());
                monthTotAmt_other = cumulative.queryByMonth(
                        "RSM_MLY_TOT_AMT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp());
                dayTotCnt_other = cumulative.queryByDay(
                        "RSM_DLY_TOT_CNT" + txCheckRiskBo.getTxDt().toString(),
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp());
                monthTotCnt_other = cumulative.queryByMonth(
                        "RSM_MLY_TOT_CNT" + RsmBaseUtil.getDateMonth(txCheckRiskBo.getTxDt()),
                        txCheckRiskBo.getPayUserId() + "."
                                + txCheckRiskBo.getPayUserTyp() + "."
                                + txCheckRiskBo.getPayTyp() + "."
                                + Constants.DC_PTY_FLG_PAY + "."
                                + txCheckRiskBo.getTxTyp());
                if (JudgeUtils.isNull(dayTotAmt_other) || JudgeUtils.equals(dayTotAmt_other, "")) {
                    dayTotAmt = "0";
                }
                if (JudgeUtils.isNull(monthTotAmt_other) || JudgeUtils.equals(monthTotAmt_other, "")) {
                    monthTotAmt = "0";
                }
                if (JudgeUtils.isNull(dayTotCnt_other) || JudgeUtils.equals(dayTotCnt_other, "")) {
                    dayTotCnt = "0";
                }
                if (JudgeUtils.isNull(monthTotCnt_other) || JudgeUtils.equals(monthTotCnt_other, "")) {
                    monthTotCnt = "0";
                }
                break;
        }
        if (JudgeUtils.isNull(dayTotAmt) || JudgeUtils.equals(dayTotAmt, "null")) {
            dayTotAmt = "0";
        }
        if (JudgeUtils.isNull(monthTotAmt) || JudgeUtils.equals(monthTotAmt, "null")) {
            monthTotAmt = "0";
        }
        if (JudgeUtils.isNull(dayTotCnt) || JudgeUtils.equals(dayTotCnt, "null")) {
            dayTotCnt = "0";
        }
        if (JudgeUtils.isNull(monthTotCnt) || JudgeUtils.equals(monthTotCnt, "null")) {
            monthTotCnt = "0";
        }

        if (JudgeUtils.equals(riskRuleDo.getDcPtyFlg(), Constants.DC_PTY_FLG_ALL)) {
            return dayTotAmt + "|" + dayTotCnt + "|" + monthTotAmt + "|" + monthTotCnt + "|" +
                    dayTotAmt_other + "|" + dayTotCnt_other + "|" + monthTotAmt_other + "|" + monthTotCnt_other;
        }
        return dayTotAmt + "|" + dayTotCnt + "|" + monthTotAmt + "|" + monthTotCnt;
    }

    /**
     * 装填待风控检查规则
     *
     * @param txCheckRiskBo 待检查交易数据
     * @return List 返回命中规则列表
     */
    private Map<RiskRuleDo, String> getCheckRule(TxCheckRiskBo txCheckRiskBo) {
        //待检测规则
        Map<RiskRuleDo, String> ruleList = new HashMap<>();
        String[] ruleDesc;

        //精确匹配规则
        RsmCheckRuleListDo rsmCheckRuleListDo = rsmCheckRuleListService.queryCheckRule(txCheckRiskBo.getTxTyp());
        //该交易类型没有配置时，走全项检查
        if (JudgeUtils.isNull(rsmCheckRuleListDo)) {
            rsmCheckRuleListDo = rsmCheckRuleListService.queryCheckRule(Constants.TX_TYP_ALL);
        }
        if (JudgeUtils.isNull(rsmCheckRuleListDo)) {
            return null;
        }
        if (JudgeUtils.isNull(rsmCheckRuleListDo.getRuleDesc())) {
            return null;
        }

        ruleDesc = rsmCheckRuleListDo.getRuleDesc().split("\\|");
        //没有待检测规则直接返回空
        if (ruleDesc.length == 0) {
            return null;
        }
        for (String temp : ruleDesc) {
            String[] ruleTemp = temp.split(",");
            RiskRuleDo rule = riskRuleMngService.queryRule(ruleTemp[0]);
            if (JudgeUtils.isNotNull(rule)) {
                ruleList.put(rule, ruleTemp[1]);
            }
        }
        return ruleList;
    }
}