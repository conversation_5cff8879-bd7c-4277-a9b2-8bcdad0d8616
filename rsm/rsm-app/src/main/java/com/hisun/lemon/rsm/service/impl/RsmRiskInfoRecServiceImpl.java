package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmBaseUtil;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.RsmRiskInfoRecMapper;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import com.hisun.lemon.rsm.service.IRsmRiskInfoRecService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/7/20
 */
@Service
@Transactional
public class RsmRiskInfoRecServiceImpl extends BaseService implements IRsmRiskInfoRecService {

    @Resource
    private RsmRiskInfoRecMapper rsmRiskInfoRecMapper;

    @Override
    public MsgCd insertRiskInfoRec(RsmRiskInfoRecDo rsmRiskInfoRecDo, TxCheckRiskBo bo) {
        //获取额外字段信息
        if (JudgeUtils.isNull(bo.getTxData())) {
            throw new RsmException(MsgCd.TX_DATA_IS_NULL);
        }
        Map data = RsmBaseUtil.string2Map(bo.getTxData());

        rsmRiskInfoRecDo.setRecId(IdGenUtils.generateId("RSM_RISK_INFO_REC", 10));
        rsmRiskInfoRecDo.setTxTyp(bo.getTxTyp());
        rsmRiskInfoRecDo.setTxCnl(bo.getTxCnl());
        rsmRiskInfoRecDo.setTxSts(bo.getTxSts());
        rsmRiskInfoRecDo.setTxDt(bo.getTxDt());
        rsmRiskInfoRecDo.setTxTm(bo.getTxTm());
        rsmRiskInfoRecDo.setTxAmt(bo.getTxAmt());
        rsmRiskInfoRecDo.setCcy(bo.getCcy());
        rsmRiskInfoRecDo.setIpAddr((String) data.get("ipAddr"));

        if (!JudgeUtils.isNull(data.get("crdTyp"))) {
            rsmRiskInfoRecDo.setCrdTyp((String) data.get("crdTyp"));
        }

        switch (bo.getTxTyp()) {
            //交易类型为充值：
            case Constants.TX_TYP_RECHARGE:
                rsmRiskInfoRecDo.setUserId(bo.getPayUserId());
                rsmRiskInfoRecDo.setUserTyp(bo.getPayUserTyp());
                rsmRiskInfoRecDo.setCapFlw(Constants.CAP_FLW_IN);
                rsmRiskInfoRecMapper.insert(rsmRiskInfoRecDo);
                break;
            //交易类型为消费、转账：
            case Constants.TX_TYP_CONSUME:
            case Constants.TX_TYP_TRANSFER:
                RsmRiskInfoRecDo recDo = new RsmRiskInfoRecDo();
                BeanUtils.copyProperties(recDo, rsmRiskInfoRecDo);
                //付方为流出
                rsmRiskInfoRecDo.setUserId(bo.getPayUserId());
                rsmRiskInfoRecDo.setUserTyp(bo.getPayUserTyp());
                rsmRiskInfoRecDo.setTxUserId(bo.getStlUserId());
                rsmRiskInfoRecDo.setTxUserTyp(bo.getStlUserTyp());
                rsmRiskInfoRecDo.setCapFlw(Constants.CAP_FLW_OUT);
                rsmRiskInfoRecMapper.insert(rsmRiskInfoRecDo);
                //收方为流入
                recDo.setRecId(IdGenUtils.generateId("RSM_RISK_INFO_REC", 10));
                recDo.setUserId(bo.getStlUserId());
                recDo.setUserTyp(bo.getStlUserTyp());
                recDo.setTxUserId(bo.getPayUserId());
                recDo.setTxUserTyp(bo.getPayUserTyp());
                recDo.setCapFlw(Constants.CAP_FLW_IN);
                rsmRiskInfoRecMapper.insert(recDo);
                break;
            //提现
            case Constants.TX_TYP_WITHDRAW:
                rsmRiskInfoRecDo.setUserId(bo.getPayUserId());
                rsmRiskInfoRecDo.setUserTyp(bo.getPayUserTyp());
                rsmRiskInfoRecDo.setCapFlw(Constants.CAP_FLW_OUT);
                rsmRiskInfoRecMapper.insert(rsmRiskInfoRecDo);
                break;
            //退款
        }
        return MsgCd.SUCCESS;
    }

    @Override
    public List<RsmRiskInfoRecDo> queryRiskInfoRec(String userId, String capFlw, int daysAgo) {
        LocalDate begDay = LocalDate.now().minusDays(daysAgo);
        return rsmRiskInfoRecMapper.selectRecsByTimeAndCapFlw(userId,capFlw,begDay);
    }
}
