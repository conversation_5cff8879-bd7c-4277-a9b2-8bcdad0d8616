package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.risklibrary.*;
import com.hisun.lemon.rsm.service.IAntiMoneyLaunderingSerivce;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/8/8
 */
@Service
public class AntMoneyLaunderingServiceImpl extends BaseService implements IAntiMoneyLaunderingSerivce {

    @Resource
    private RsmRules001 rsmRules001;

    @Resource
    private RsmRules002 rsmRules002;

    @Resource
    private RsmRules004 rsmRules004;

    @Resource
    private RsmRules005 rsmRules005;

    @Resource
    private RsmRules006 rsmRules006;

    @Override
    public void checkAndRecord(TxCheckRiskBo txCheckRiskBo) {
        rsmRules001.execute(txCheckRiskBo);
        rsmRules002.execute(txCheckRiskBo);
        /**
         * 没有该规则
         * rsmRules003.execute(txCheckRiskBo);
         */
        rsmRules004.execute(txCheckRiskBo);
        rsmRules005.execute(txCheckRiskBo);
        rsmRules006.execute(txCheckRiskBo);
    }
}
