package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.entity.RsmHighRiskUserDo;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmHighRiskUserService;
import com.hisun.lemon.rsm.service.IRsmRiskInfoRecService;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 个人客户X日内频繁（大于X次）发生临近（正负百分之X）限额交易，且交易总额超过X万元
 * 参数1：X日
 * 参数2：X次
 * 参数3：限额
 * 参数4：百分比
 * 参数5：交易总限额
 *
 * <AUTHOR>
 * @create 2017/7/22
 */
@Component
public class RsmRules006 implements SpecialRuleTemplate {

    private static final Logger logger = LoggerFactory.getLogger(RsmRules006.class);

    @Resource
    private IRsmRiskInfoRecService rsmRiskInfoRecService;

    @Resource
    private IRsmRiskParamService rsmRiskParamService;

    @Resource
    private IRsmHighRiskUserService rsmHighRiskUserService;

    @Override
    public void execute(TxCheckRiskBo txCheckRiskBo) {
        //如果风险参数不存在直接跳过
        RsmRiskParmDo rsmRiskParmDo = rsmRiskParamService.queryRuleParam("RsmRules006");
        if (JudgeUtils.isNull(rsmRiskParmDo)) {
            return;
        }
        //参数1，时间天数
        int dayAgo = Integer.valueOf(rsmRiskParmDo.getParm1());
        //参数2，交易笔数
        int txCnt = Integer.valueOf(rsmRiskParmDo.getParm2());
        //参数3，限额
        BigDecimal lmtAmt = new BigDecimal(rsmRiskParmDo.getParm3());
        //参数4，百分比
        int pct = Integer.valueOf(rsmRiskParmDo.getParm4());
        //参数5，交易总限额
        BigDecimal totLmtAmt = new BigDecimal(rsmRiskParmDo.getParm5());

        MsgCd result = MsgCd.SUCCESS;
        List<RsmRiskInfoRecDo> recList = rsmRiskInfoRecService.queryRiskInfoRec(txCheckRiskBo.getPayUserId(),
                Constants.CAP_FLW_OUT, dayAgo);
        if (recList.isEmpty()) {
            return;
        }

        BigDecimal difAmt = lmtAmt.multiply(new BigDecimal(pct))
                .divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN);

        BigDecimal totAmt = BigDecimal.ZERO;
        int totCnt = 0;
        for (RsmRiskInfoRecDo rec : recList) {
            if (rec.getTxAmt().compareTo(lmtAmt.subtract(difAmt)) > 0 ||
                    rec.getTxAmt().compareTo(lmtAmt.add(difAmt)) < 0) {
                totCnt = totCnt + 1;
                totAmt = totAmt.add(rec.getTxAmt());
            }
        }

        if (totAmt.compareTo(totLmtAmt) > 0 && totCnt > txCnt) {
            logger.warn(RsmRules006.class.getName());

            RsmHighRiskUserDo rsmHighRiskUserDo = new RsmHighRiskUserDo();
            rsmHighRiskUserDo.setUserId(txCheckRiskBo.getPayUserId());
            rsmHighRiskUserDo.setTxTyp(txCheckRiskBo.getTxTyp());
            rsmHighRiskUserDo.setRiskOpr(Constants.RISK_OPR_SPS);
            //TODO 风险描述，或多语言支持
            rsmHighRiskUserDo.setRiskDesc("123");
            rsmHighRiskUserDo.setOprSts(Constants.OPR_STS_RISK);
            rsmHighRiskUserDo.setRiskSorc(Constants.RISK_SORC);
            rsmHighRiskUserDo.setTxOrdNo(txCheckRiskBo.getTxOrdNo());
            rsmHighRiskUserDo.setRiskParm(this.getClass().getSimpleName());
            rsmHighRiskUserDo.setCreateTime(LocalDateTime.now());
            rsmHighRiskUserDo.setModifyTime(LocalDateTime.now());
            rsmHighRiskUserService.addUserToHighList(rsmHighRiskUserDo);
        }
    }
}
