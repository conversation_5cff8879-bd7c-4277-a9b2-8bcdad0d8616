package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/7/20
 */
public interface IRsmChkPswLmtService {

    /**
     * 累计免密支付的金额
     *
     * @param userId 用户号
     * @param amt    交易金额
     */
    void cumulativePswAmtLmt(String userId, BigDecimal amt);

    /**
     * 检查免密支付阈值
     *
     * @param userId 用户id
     * @param amt    交易金额
     * @return
     */
    RsmTxCheckCode checkCumlativeRswAmtLmt(String userId, BigDecimal amt);
}
