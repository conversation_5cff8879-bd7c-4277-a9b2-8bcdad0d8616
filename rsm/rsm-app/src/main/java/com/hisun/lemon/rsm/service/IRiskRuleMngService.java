package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.entity.RiskRuleDo;

import java.util.List;

/**
 * 风控规则
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
public interface IRiskRuleMngService {

    /**
     * 添加风控规则
     *
     * @param riskRuleDo 风控规则对象
     * @return
     */
    boolean addRule(RiskRuleDo riskRuleDo);

    /**
     * 删除风控规则
     *
     * @param ruleId 风控规则对象
     * @return
     */
    boolean deleteRule(String ruleId);

    /**
     * 查询风控规则
     *
     * @param ruleId 风控规则对象
     * @return
     */
    RiskRuleDo queryRule(String ruleId);

    /**
     * 查询所有风控规则
     *
     * @param pageNum  页数
     * @param pageSize 页面查询数
     * @return
     */
    List<RiskRuleDo> selectAllRules(int pageNum, int pageSize);

    /**
     * 修改风控规则
     *
     * @param riskRuleDo 风控规则对象
     * @return
     */
    boolean updateRule(RiskRuleDo riskRuleDo);

    /**
     * 查找全部风控规则（非ui使用，无分页）
     *
     * @return
     */
    List<RiskRuleDo> queryAllRules();
}
