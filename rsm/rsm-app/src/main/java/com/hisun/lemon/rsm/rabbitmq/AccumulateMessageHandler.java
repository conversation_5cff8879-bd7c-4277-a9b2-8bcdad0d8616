package com.hisun.lemon.rsm.rabbitmq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.service.ITxCheckRiskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 异步累计结果消息消费者
 *
 * <AUTHOR>
 * @create 2017/8/16
 */
@Component("accumulateMessageHandler")
public class AccumulateMessageHandler implements MessageHandler<TxCheckRiskBo> {

    private static final Logger logger = LoggerFactory.getLogger(AccumulateMessageHandler.class);

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ITxCheckRiskService txCheckRiskService;

    @Override
    public void onMessageReceive(GenericCmdDTO<TxCheckRiskBo> genericCmdDTO) {
        String data = ObjectMapperHelper.writeValueAsString(objectMapper, genericCmdDTO, true);
        logger.info("接收风控累计信息：" + data);
        TxCheckRiskBo txCheckRiskBo = genericCmdDTO.getBody();
        txCheckRiskService.rsmNotRealTimeCheck(txCheckRiskBo);
    }
}
