package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/8/25
 */
public class ChkSglAmtLmt implements CheckRuleTemplate {

    @Override
    public RsmTxCheckCode check(TxRuleParamDo txRuleParamDo, String[] cacheValue, BigDecimal txAmt) {

        //限额信息初始化
        BigDecimal minAmtLmt = txRuleParamDo.getMinAmtLmt();
        BigDecimal maxAmtLmt = txRuleParamDo.getMaxAmtLmt();

        if (minAmtLmt != null && txAmt.compareTo(minAmtLmt) < 0) {
            return RsmTxCheckCode.LESS_THAN_MIN_AMT;
        }
        if (maxAmtLmt != null && txAmt.compareTo(maxAmtLmt) > 0) {
            return RsmTxCheckCode.GREATER_THAN_MAX_AMT;
        }

        return RsmTxCheckCode.PASS;
    }
}
