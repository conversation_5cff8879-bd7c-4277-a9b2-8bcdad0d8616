package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/20
 */
public interface IRsmRiskInfoRecService {

    /**
     * 添加风险信息记录
     *
     * @param rsmRiskInfoRecDo 风险信息对象
     * @return MsgCd 错误码
     */
    MsgCd insertRiskInfoRec(RsmRiskInfoRecDo rsmRiskInfoRecDo, TxCheckRiskBo bo);

    /**
     * 查询daysAgo天前该用户的风险记录信息
     *
     * @param userId 用户id
     * @param capFlw 资金流向
     * @param daysAgo 开始日期
     * @return
     */
    List<RsmRiskInfoRecDo> queryRiskInfoRec(String userId, String capFlw, int daysAgo);
}
