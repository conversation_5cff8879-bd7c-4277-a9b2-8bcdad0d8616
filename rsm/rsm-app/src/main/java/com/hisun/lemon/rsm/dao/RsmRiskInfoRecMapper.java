package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Mapper
@Component
public interface RsmRiskInfoRecMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_info_rec
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(String recId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_info_rec
     *
     * @mbggenerated
     */
    int insert(RsmRiskInfoRecDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_info_rec
     *
     * @mbggenerated
     */
    int insertSelective(RsmRiskInfoRecDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_info_rec
     *
     * @mbggenerated
     */
    RsmRiskInfoRecDo selectByPrimaryKey(String recId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_info_rec
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(RsmRiskInfoRecDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_info_rec
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(RsmRiskInfoRecDo record);


    /**
     * 根据资金流向以及时间查询记录信息集合
     *
     * @param userId 用户id
     * @param capFlw 资金流向
     * @param begDay 开始日期
     * @return List 记录集合
     */
    List<RsmRiskInfoRecDo> selectRecsByTimeAndCapFlw(@Param("userId") String userId,
                                                     @Param("capFlw") String capFlw,
                                                     @Param("begDay") LocalDate begDay);
}