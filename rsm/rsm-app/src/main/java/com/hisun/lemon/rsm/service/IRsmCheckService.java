package com.hisun.lemon.rsm.service;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.RsmJrnReqDTO;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

/**
 * 风控检测接口
 *
 * <AUTHOR>
 * @create 2017/7/12
 */
public interface IRsmCheckService {

    /**
     * 交易检查用户状态
     *
     * @param rsmJrnReqDTO 交易信息
     * @return
     */
    RsmTxCheckCode checkBlackAndWhileList(RsmJrnReqDTO rsmJrnReqDTO);

    /**
     * 实时规则检查
     *
     * @param rsmJrnReqDTO
     * @return
     */
    GenericRspDTO<NoBody> checkRisk(RsmJrnReqDTO rsmJrnReqDTO);

    /**
     * 交易累计
     *
     * @param rsmJrnReqDTO 交易信息
     * @return
     */
    GenericRspDTO<NoBody> accumulateAmount(RsmJrnReqDTO rsmJrnReqDTO);

    /**
     * 检查用户状态
     *
     * @param reqDTO
     * @return
     */
    RsmTxCheckCode checkUserStatus(RiskCheckUserStatusReqDTO reqDTO);
}
