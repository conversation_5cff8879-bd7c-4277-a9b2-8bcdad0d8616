package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/8/25
 */
public class ChkMlyCntLmt implements CheckRuleTemplate {

    @Override
    public RsmTxCheckCode check(TxRuleParamDo txRuleParamDo, String[] cacheValue, BigDecimal txAmt) {

        int monthTotCnt = Integer.parseInt(cacheValue[3]);

        int mthCntLmt = txRuleParamDo.getMlyCntLmt();

        if ((monthTotCnt + 1) > mthCntLmt) {
            return RsmTxCheckCode.MORE_THAN_MONTHLY_CUMULATE_COUNT;
        }

        return RsmTxCheckCode.PASS;
    }
}
