package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.rsm.dao.RsmJrnMapper;
import com.hisun.lemon.rsm.entity.RsmJrnDo;
import com.hisun.lemon.rsm.service.IRsmJrnService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 风控流水
 *
 * <AUTHOR>
 * @create 2017/7/11
 */
@Transactional
@Service
public class RsmJrnServiceImpl extends BaseService implements IRsmJrnService {

    @Resource
    private RsmJrnMapper rsmJrnMapper;

    @Override
    public int addRsmJrn(RsmJrnDo rsmJrnDo) {
        rsmJrnDo.setJrnNo(IdGenUtils.generateIdWithDateTime("RSM_TX_JRN", "RSM", 8));
        rsmJrnDo.setCreateTime(LocalDateTime.now());
        rsmJrnDo.setModifyTime(LocalDateTime.now());
        return rsmJrnMapper.insert(rsmJrnDo);
    }

    @Override
    public int updateRsmJrn(RsmJrnDo rsmJrnDo) {
        rsmJrnDo.setModifyTime(LocalDateTime.now());
        return rsmJrnMapper.updateByPrimaryKeySelective(rsmJrnDo);
    }
}
