package com.hisun.lemon.rsm.common;

/**
 * 错误码
 *
 * <AUTHOR>
 * @create 2017/7/10
 */
public enum MsgCd {

    SUCCESS("RSM00000", "success"),

    /**
     * RSM0 - 非业务性系统异常
     */
    DELETE_ERROR("RSM00002", "Delete Error"),
    INSERT_ERROR("RSM00003", "Insert Error"),
    UPDATE_ERROR("RSM00004", "Update Error"),
    DATA_TRANSFORMATION_ERROR("RSM00005", "Data Transformation Error"),
    CPN_NM_INSTANCE_NOT_EXIST("RSM00006", "The component name instance does not exist"),
    SERVICE_EXCEPTION("RSM09999", "System Error"),

    /**
     * RSM1 - 数据校验异常
     */
    LIST_ID_IS_NULL("RSM10001", "Risk list id is null"),
    LIST_TYP_ERROR("RSM10002", "Risk list typ error"),
    ID_TYP_ERROR("RSM10003", "Id typ error"),
    ID_IS_NULL("RSM10004", "Id is null"),
    TX_TYP_IS_NULL("RSM10005", "Transaction typ is null"),
    LIST_RSN_IS_NULL("RSM10006", "Reason is null"),
    EFF_FLG_ERROR("RSM10007", "Effective flg is null"),
    LIST_SORC_IS_NULL("RSM10008", "Source is null"),
    OPR_ID_IS_NULL("RSM10009", "Operator id is null"),

    AC_TYP_IS_NULL("RSM10010", "Account typ is null"),
    PAY_TYP_IS_NULL("RSM10011", "Payment method is null"),
    RULE_ID_IS_NULL("RSM10012", "Rule id is null"),
    MIN_AMT_ERROR("RSM10013", "Minimal amount error"),
    MAX_AMT_ERROR("RSM10014", "Maximum amount error"),
    DLY_AMT_ERROR("RSM10015", "Daily accumulated amount error"),
    DLY_CNL_ERROR("RSM10016", "Daily cumulative number of times error"),
    MLY_AMT_ERROR("RSM10017", "Monthly accumulated amount error"),
    MLY_CNL_ERROR("RSM10018", "Monthly cumulative number of times"),

    RULE_NM_IS_NULL("RSM10019", "Rule name is null"),
    RULE_TYP_ERROR("RSM10020", "Rule typ error"),
    RULE_DESC_IS_NULL("RSM10021", "Rule description error"),
    TX_STS_IS_NULL("RSM10022", "Transaction status is null"),
    TX_CNL_IS_NULL("RSM10023", "Transaction channel is null"),
    DC_PTY_FLG_ERROR("RSM10024", "Payment flg error"),
    CPN_NM_IS_NULL("RSM10025", "Credentials name is null"),

    TX_AMT_ERROR("RSM10026", "Transaction amount error"),
    CCY_IS_NULL("RSM10027", "CCY is null"),
    TX_DT_IS_NULL("RSM10028", "Transaction date is null"),
    TX_TM_IS_NULL("RSM10029", "Transaction time is null"),
    TX_JRN_NO_IS_NULL("RSM10030", "Jrn is null "),
    TX_ORD_NO_IS_NULL("RSM10031", "Order number is null"),
    TX_DATA_IS_NULL("RSM10032", "Transaction data is null"),

    CAP_TYP_IS_NULL("RSM10033", "CAP typ is null"),
    BUS_CNL_IS_NULL("RSM10034", "Business channel is null"),
    REAL_NM_FLG_IS_NULL("RSM10035", "Real name flg is null"),
    TOT_MONTH_IS_NULL("RSM10036", "Total month is null"),

    LIMIT_PARAM_ID_IS_NULL("RSM10037", "Limit param id is null"),
    LIMIT_LEVEL_ERROR("RSM10038", "Limit level error"),
    OPERATION_TYPE_ERROR("RSM10039", "Operation type error"),
    CHECK_ID_IS_NULL("RSM10040", "Check id is null"),
    USER_ID_IS_NULLL("RSM10041", "User id is null"),
    USER_TYP_ERROR("RSM10042", "User typ id error"),

    MOBILE_NUMBER_IS_NULL("RSM10043", "Mobile number is null"),
    USER_STATUS_ERROR("RSM10044", "User status is error"),

    /**
     * RSM2 - 通讯异常
     */

    /**
     * RSM3 - 业务异常
     */
    RSM_REAL_TIME_CHECK_REFUSE("RSM30001", "Risk check refuse"),
    BLACK_LIST_IS_EXIST("RSM30002", "The user is already in blacklist"),
    LESS_THAN_MIN_AMT("RSM30003", "The amount is less than the minimum amount"),
    GREATER_THAN_MAX_AMT("RSM30004", "The amount is greater than the maximum amount"),
    MORE_THAN_DAILY_CUMULATE_AMT("RSM30005", "Accumulated more than the daily limit"),
    MORE_THAN_DAILY_CUMULATE_COUNT("RSM30006", "The cumulative exceeds the daily limit"),
    MORE_THAN_MONTHLY_CUMULATE_AMT("RSM30007", "Accumulated more than the monthly limit"),
    MORE_THAN_MONTHLY_CUMULATE_COUNT("RSM30008", "The cumulative exceeds the monthly limit"),
    RSM_RISK_RULE_REFUSE("RSM30009", "Trigger risk rules"),
    USER_STATUS_IS_PAUSED("RSM30010", "The user status is paused"),
    USER_STATUS_IS_FROZEN("RSM30011", "The user status is frozen"),

    SELECT_ERROR("RSM30012", "The query is empty for the result"),
    CARD_IN_BLACK_LIST("RSM30013", "The bank card is already in blacklist"),
    CUMULATIVE_EXCEPTION("RSM30014", "Cumulative error"),

    MERC_IN_BLACK_LIST("RSM30015", "The merchant is already in blacklist"),
    MERC_STATUS_IS_PAUSED("RSM30016", "The merchant status is paused"),
    MERC_STATUS_IS_FROZEN("RSM30017", "The merchant status is frozen"),

    STL_IN_BLACK_LIST("RSM30018", "The other is already in blacklist"),
    STL_STATUS_IS_PAUSED("RSM30019", "The other status is paused"),
    STL_STATUS_IS_FROZEN("RSM30020", "The other status is frozen"),
    ;
    private String msgCd;
    private String msgInfo;

    MsgCd(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    public String getMsgCd() {
        return msgCd;
    }

    public String getMsgInfo() {
        return msgInfo;
    }
}
