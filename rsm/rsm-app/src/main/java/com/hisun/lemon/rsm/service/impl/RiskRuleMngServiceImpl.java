package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.RiskRuleMapper;
import com.hisun.lemon.rsm.entity.RiskRuleDo;
import com.hisun.lemon.rsm.service.IRiskRuleMngService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 风控规则
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
@Transactional
@Service
public class RiskRuleMngServiceImpl extends BaseService implements IRiskRuleMngService {

    @Resource
    private RiskRuleMapper riskRuleMapper;

    @Override
    public boolean addRule(RiskRuleDo riskRuleDo) {
        //补充扩展信息
        riskRuleDo.setCreateTime(LocalDateTime.now());
        riskRuleDo.setModifyTime(LocalDateTime.now());

        //新增风控规则时获取自增值
        riskRuleDo.setRuleId(IdGenUtils.generateId("RSM_RULE_INFO", 4));
        int result = riskRuleMapper.insert(riskRuleDo);

        if (result <= 0) {
            throw new RsmException(MsgCd.INSERT_ERROR, Constants.RSM_TABLE_RULE);
        }
        return Constants.SUCCESS;
    }

    @Override
    public boolean deleteRule(String ruleId) {
        int result = riskRuleMapper.deleteByPrimaryKey(ruleId);
        if (result <= 0) {
            throw new RsmException(MsgCd.DELETE_ERROR, Constants.RSM_TABLE_RULE);
        }
        return Constants.SUCCESS;
    }

    @Override
    @Transactional(readOnly = true)
    public RiskRuleDo queryRule(String ruleId) {
        return riskRuleMapper.selectByPrimaryKey(ruleId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RiskRuleDo> selectAllRules(int pageNum, int pageSize) {
        List<RiskRuleDo> list;
        list = PageUtils.pageQuery(pageNum, pageSize, () -> riskRuleMapper.selectAllRules());
        if (list.isEmpty()) {
            throw new RsmException(MsgCd.SELECT_ERROR, Constants.RSM_TABLE_RULE);
        }
        return list;
    }

    @Override
    public boolean updateRule(RiskRuleDo riskRuleDo) {
        riskRuleDo.setModifyTime(LocalDateTime.now());
        int result = riskRuleMapper.updateByPrimaryKeySelective(riskRuleDo);
        if (result <= 0) {
            throw new RsmException(MsgCd.UPDATE_ERROR, Constants.RSM_TABLE_RULE);
        }
        return Constants.SUCCESS;
    }

    @Override
    @Transactional(readOnly = true)
    public List<RiskRuleDo> queryAllRules() {
        return riskRuleMapper.selectAllRules();
    }

}
