package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.RiskListDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface RiskWhiteListMapper {

    int deleteByPrimaryKey(String listId);

    int insert(RiskListDo record);

    RiskListDo selectByPrimaryKey(String listId);

    int updateByPrimaryKeySelective(RiskListDo record);

    int updateByPrimaryKey(RiskListDo record);

    /**
     * 根据id和id类型查询白名单
     *
     * @param id    id值
     * @param idTyp id类型
     * @param txTyp 交易类型
     * @return RiskListDo 白名单对象
     */
    List<RiskListDo> selectByIdAndTypAndTxTyp(@Param("id") String id,
                                              @Param("idTyp") String idTyp,
                                              @Param("txTyp") String txTyp);

    /**
     * 查询所有白名单
     *
     * @return
     */
    List<RiskListDo> selectAllLists();
}