package com.hisun.lemon.rsm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDateTime;

/**
 * 黑白名单数据对象
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
public class RiskListDo extends BaseDO {
    private String listId;
    private String idTyp;
    private String id;
    private String idHid;
    private String crdNoLast;
    private String txTyp;
    private LocalDateTime beginDt;
    private LocalDateTime endDt;
    private String effFlg;

    private String listSorc;
    private String listRsn;
    private String updOprId;
    private LocalDateTime createTime;
    private LocalDateTime modifyTime;

    public String getListId() {
        return listId;
    }

    public void setListId(String listId) {
        this.listId = listId;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdHid() {
        return idHid;
    }

    public void setIdHid(String idHid) {
        this.idHid = idHid;
    }

    public String getCrdNoLast() {
        return crdNoLast;
    }

    public void setCrdNoLast(String crdNoLast) {
        this.crdNoLast = crdNoLast;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public LocalDateTime getBeginDt() {
        return beginDt;
    }

    public void setBeginDt(LocalDateTime beginDt) {
        this.beginDt = beginDt;
    }

    public LocalDateTime getEndDt() {
        return endDt;
    }

    public void setEndDt(LocalDateTime endDt) {
        this.endDt = endDt;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getListSorc() {
        return listSorc;
    }

    public void setListSorc(String listSorc) {
        this.listSorc = listSorc;
    }

    public String getListRsn() {
        return listRsn;
    }

    public void setListRsn(String listRsn) {
        this.listRsn = listRsn;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    @Override
    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RiskListDo{" +
                "listId='" + listId + '\'' +
                ", idTyp='" + idTyp + '\'' +
                ", id='" + id + '\'' +
                ", idHid='" + idHid + '\'' +
                ", crdNoLast='" + crdNoLast + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", beginDt=" + beginDt +
                ", endDt=" + endDt +
                ", effFlg='" + effFlg + '\'' +
                ", listSorc='" + listSorc + '\'' +
                ", listRsn='" + listRsn + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
