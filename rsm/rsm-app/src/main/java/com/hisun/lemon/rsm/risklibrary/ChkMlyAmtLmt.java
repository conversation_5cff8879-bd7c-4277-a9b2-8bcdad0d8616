package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.rsm.common.RsmBaseUtil;
import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2017/8/25
 */
public class ChkMlyAmtLmt implements CheckRuleTemplate {

    @Override
    public RsmTxCheckCode check(TxRuleParamDo txRuleParamDo, String[] cacheValue, BigDecimal txAmt) {

        BigDecimal monthTotAmt = RsmBaseUtil.divHundred(new BigDecimal(cacheValue[2]));

        //限额信息初始化
        BigDecimal mthAmtLmt = txRuleParamDo.getMlyAmtLmt();

        if (mthAmtLmt != null && monthTotAmt.add(txAmt).compareTo(mthAmtLmt) > 0) {
            return RsmTxCheckCode.MORE_THAN_MONTHLY_CUMULATE_AMT;
        }

        return RsmTxCheckCode.PASS;
    }
}
