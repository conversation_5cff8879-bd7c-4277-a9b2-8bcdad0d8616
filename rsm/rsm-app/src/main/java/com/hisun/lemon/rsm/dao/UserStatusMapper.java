package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.UserStatusDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/10
 */
@Mapper
@Component
public interface UserStatusMapper {

    /**
     * 添加暂停或冻结记录
     *
     * @param userStatusDo
     * @return
     */
    int insertUserStatusRec(UserStatusDo userStatusDo);

    /**
     * 删除
     *
     * @param userId
     * @return
     */
    int deleteByPrimaryKey(String userId);

    /**
     * 查询
     *
     * @param userId
     * @return
     */
    UserStatusDo selectByPrimaryKey(String userId);

    /**
     * 查询列表
     *
     * @param userSts 用户状态
     * @return
     */
    List<UserStatusDo> selectAllUserStatusRec(String userSts);

    /**
     * 修改
     *
     * @param userStatusDo
     * @return
     */

    int updateByPrimaryKeySelective(UserStatusDo userStatusDo);
}
