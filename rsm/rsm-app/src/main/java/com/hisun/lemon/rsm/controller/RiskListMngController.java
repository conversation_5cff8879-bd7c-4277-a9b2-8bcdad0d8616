package com.hisun.lemon.rsm.controller;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dto.req.risklist.*;
import com.hisun.lemon.rsm.dto.res.RiskListResDTO;
import com.hisun.lemon.rsm.entity.RiskListDo;
import com.hisun.lemon.rsm.service.IRiskListMngService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 风控黑白名单管理
 *
 * <AUTHOR>
 * @create 2017/7/5
 */
@Api(tags = "风控黑白名单管理", value = "风控黑白名单管理")
@RestController
@RequestMapping(value = "/rsm/risklistmng")
public class RiskListMngController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RiskListMngController.class);

    @Resource
    private IRiskListMngService riskListMngService;

    @ApiOperation(value = "检查黑白名单", notes = "检查黑白名单")
    @ApiResponse(code = 200, message = "查询黑白名单结果")
    @GetMapping(value = "/users")
    public GenericRspDTO<RiskListResDTO> queryRiskList(@Validated RiskListQueryReqDTO riskListQueryReqDTO) {
        logger.debug("Params:id = " + riskListQueryReqDTO.getId() + "; listTyp = " + riskListQueryReqDTO.getListTyp());

        GenericRspDTO<RiskListResDTO> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        RiskListResDTO riskListResDTO = new RiskListResDTO();
        RiskListDo riskListDo;
        try {
            riskListDo = riskListMngService.queryRiskList(riskListQueryReqDTO.getId(), riskListQueryReqDTO.getListTyp());
            BeanUtils.copyProperties(riskListResDTO, riskListDo);
            riskListResDTO.setListTyp(riskListQueryReqDTO.getListTyp());
            genericDTO.setBody(riskListResDTO);
        } catch (RsmException e) {
            logger.error("Query risk list error：" + e);
            genericDTO.setMsgCd(MsgCd.SELECT_ERROR.getMsgCd());
            return genericDTO;
        }
        return genericDTO;
    }

    @ApiOperation(value = "添加黑白名单", notes = "添加黑白名单")
    @ApiResponse(code = 200, message = "添加黑白名单操作结果")
    @PostMapping(value = "/users")
    public GenericRspDTO<NoBody> addRiskList(@Validated @RequestBody GenericDTO<RiskListInsertReqDTO> reqDTO) {
        RiskListInsertReqDTO riskListInsertReqDTO = reqDTO.getBody();
        RiskListDo riskListDo = new RiskListDo();
        BeanUtils.copyProperties(riskListDo, riskListInsertReqDTO);
        try {
            riskListMngService.addRiskList(riskListDo, riskListInsertReqDTO.getListTyp());
        } catch (RsmException e) {
            logger.error("添加黑白名单错误：" + e);
            return GenericRspDTO.newInstance(MsgCd.INSERT_ERROR.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "删除黑白名单", notes = "删除黑白名单")
    @ApiResponse(code = 200, message = "删除黑白名单操作结果")
    @DeleteMapping(value = "/users")
    public GenericRspDTO<NoBody> deleteRiskList(@Validated @RequestBody GenericDTO<RiskListBaseReqDTO> reqDTO) {
        RiskListBaseReqDTO riskListBaseReqDTO = reqDTO.getBody();
        try {
            riskListMngService.deleteRiskList(riskListBaseReqDTO.getId(), riskListBaseReqDTO.getListTyp());
        } catch (RsmException e) {
            logger.error("Delete risk list error：" + e);
            return GenericRspDTO.newInstance(MsgCd.DELETE_ERROR.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询黑白名单列表", notes = "查询黑白名单列表")
    @ApiResponse(code = 200, message = "黑白名单列表展示")
    @GetMapping(value = "/users/list")
    public GenericRspDTO<List<RiskListResDTO>> queryAllRiskLists(
            @Validated RiskListQueryAllReqDTO riskListQueryAllReqDTO) {
        List<RiskListResDTO> listDtos = new ArrayList<>();
        List<RiskListDo> listDos;
        int pageNum = Constants.DEFAULT_PAGE_NUMBER;
        int pageSize = Constants.DEFAULT_PAGE_SIZE;
        if (JudgeUtils.isNotNull(riskListQueryAllReqDTO.getPageNum())) {
            pageNum = riskListQueryAllReqDTO.getPageNum();
        }
        if (JudgeUtils.isNotNull(riskListQueryAllReqDTO.getPageSize())) {
            pageSize = riskListQueryAllReqDTO.getPageSize();
        }
        try {
            listDos = riskListMngService.queryAllRiskLists(riskListQueryAllReqDTO.getListTyp(), pageNum, pageSize);
            for (RiskListDo riskListDo : listDos) {
                RiskListResDTO riskListResDTO = new RiskListResDTO();
                BeanUtils.copyProperties(riskListResDTO, riskListDo);
                riskListResDTO.setListTyp(riskListQueryAllReqDTO.getListTyp());
                listDtos.add(riskListResDTO);
            }
        } catch (RsmException e) {
            logger.error("查询黑白名单列表错误：" + e);
            return GenericRspDTO.newInstance(MsgCd.SELECT_ERROR.getMsgCd(), listDtos);
        }
        return GenericRspDTO.newSuccessInstance(listDtos);
    }

    @ApiOperation(value = "修改黑白名单", notes = "修改黑白名单")
    @ApiResponse(code = 200, message = "修改黑白名单操作结果")
    @PutMapping(value = "/users")
    public GenericRspDTO<NoBody> updateRiskList(@Validated @RequestBody GenericDTO<RiskListUpdateReqDTO> reqDTO) {
        RiskListUpdateReqDTO riskListUpdateReqDTO = reqDTO.getBody();
        RiskListDo riskListDo = new RiskListDo();
        BeanUtils.copyProperties(riskListDo, riskListUpdateReqDTO);
        try {
            riskListMngService.updateRiskList(riskListDo, riskListUpdateReqDTO.getListTyp());
        } catch (RsmException e) {
            logger.error("修改黑白名单错误：" + e);
            return GenericRspDTO.newInstance(MsgCd.UPDATE_ERROR.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }
}
