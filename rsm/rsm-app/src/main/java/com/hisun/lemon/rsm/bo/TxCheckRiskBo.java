package com.hisun.lemon.rsm.bo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @create 2017/7/11
 */
public class TxCheckRiskBo {
    //原交易订单号
    private String txOrdNo;
    //收方用户类型
    private String stlUserTyp;
    //付方用户类型
    private String payUserTyp;
    //收方用户ID
    private String stlUserId;
    //付方用户ID
    private String payUserId;
    //交易类型
    private String txTyp;
    //交易状态
    private String txSts;
    //交易渠道
    private String txCnl;
    //交易币种
    private String ccy;
    //支付方式
    private String payTyp;
    //交易金额
    private BigDecimal txAmt;
    //交易数据
    private String txData;
    //交易日期
    private LocalDate txDt;
    //交易时间
    private LocalTime txTm;

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public String getStlUserTyp() {
        return stlUserTyp;
    }

    public void setStlUserTyp(String stlUserTyp) {
        this.stlUserTyp = stlUserTyp;
    }

    public String getPayUserTyp() {
        return payUserTyp;
    }

    public void setPayUserTyp(String payUserTyp) {
        this.payUserTyp = payUserTyp;
    }

    public String getStlUserId() {
        return stlUserId;
    }

    public void setStlUserId(String stlUserId) {
        this.stlUserId = stlUserId;
    }

    public String getPayUserId() {
        return payUserId;
    }

    public void setPayUserId(String payUserId) {
        this.payUserId = payUserId;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getTxSts() {
        return txSts;
    }

    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    public String getTxCnl() {
        return txCnl;
    }

    public void setTxCnl(String txCnl) {
        this.txCnl = txCnl;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getPayTyp() {
        return payTyp;
    }

    public void setPayTyp(String payTyp) {
        this.payTyp = payTyp;
    }

    public BigDecimal getTxAmt() {
        return txAmt;
    }

    public void setTxAmt(BigDecimal txAmt) {
        this.txAmt = txAmt;
    }

    public String getTxData() {
        return txData;
    }

    public void setTxData(String txData) {
        this.txData = txData;
    }

    public LocalDate getTxDt() {
        return txDt;
    }

    public void setTxDt(LocalDate txDt) {
        this.txDt = txDt;
    }

    public LocalTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalTime txTm) {
        this.txTm = txTm;
    }

    @Override
    public String toString() {
        return "TxCheckRiskBo{" +
                "txOrdNo='" + txOrdNo + '\'' +
                ", stlUserTyp='" + stlUserTyp + '\'' +
                ", payUserTyp='" + payUserTyp + '\'' +
                ", stlUserId='" + stlUserId + '\'' +
                ", payUserId='" + payUserId + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", txSts='" + txSts + '\'' +
                ", txCnl='" + txCnl + '\'' +
                ", ccy='" + ccy + '\'' +
                ", payTyp='" + payTyp + '\'' +
                ", txAmt=" + txAmt +
                ", txData='" + txData + '\'' +
                ", txDt=" + txDt +
                ", txTm=" + txTm +
                '}';
    }
}
