package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.entity.RiskListDo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/7
 */
public interface IRiskListMngService {

    /**
     * 新增黑白名单
     *
     * @param riskListDo 黑白名单对象
     * @param listTyp    黑白名单类别
     * @return
     */
    boolean addRiskList(RiskListDo riskListDo, String listTyp);

    /**
     * 删除黑白名单
     *
     * @param listId 黑白名单id
     * @param listTyp 黑白名单类别
     * @return
     */
    boolean deleteRiskList(String listId, String listTyp);

    /**
     * 查询黑白名单信息
     *
     * @param listId  黑白名单id
     * @param listTyp 黑白名单类别
     * @return
     */
    RiskListDo queryRiskList(String listId, String listTyp);

    /**
     * 查询黑白名单列表
     *
     * @param listTyp 黑白名单类别
     * @return
     */
    List<RiskListDo> queryAllRiskLists(String listTyp, int pageNum, int pageSize);

    /**
     * 修改黑白名单
     *
     * @param riskListDo 黑白名单对象
     * @param listTyp    黑白名单类别
     * @return
     */
    boolean updateRiskList(RiskListDo riskListDo, String listTyp);
}
