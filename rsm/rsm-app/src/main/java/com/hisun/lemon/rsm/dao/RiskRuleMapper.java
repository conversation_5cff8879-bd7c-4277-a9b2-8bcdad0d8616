package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.RiskRuleDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface RiskRuleMapper {
    int deleteByPrimaryKey(String ruleId);

    int insert(RiskRuleDo record);

    RiskRuleDo selectByPrimaryKey(String ruleId);

    int updateByPrimaryKeySelective(RiskRuleDo record);

    int updateByPrimaryKey(RiskRuleDo record);

    /**
     * 查询所有规则
     *
     * @return 规则列表
     */
    List<RiskRuleDo> selectAllRules();

    /**
     * 多条件精确查找
     *
     * @param riskRuleDo 规则对象
     * @return
     */
    RiskRuleDo selectByExactMatch(RiskRuleDo riskRuleDo);
}