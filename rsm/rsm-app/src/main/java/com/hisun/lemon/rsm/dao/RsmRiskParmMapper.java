package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface RsmRiskParmMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_parm
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(String parmId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_parm
     *
     * @mbggenerated
     */
    int insert(RsmRiskParmDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_parm
     *
     * @mbggenerated
     */
    int insertSelective(RsmRiskParmDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_parm
     *
     * @mbggenerated
     */
    RsmRiskParmDo selectByPrimaryKey(String parmId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_parm
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(RsmRiskParmDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_risk_parm
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(RsmRiskParmDo record);

    /**
     * 查询所有风险规则
     *
     * @return
     */
    List<RsmRiskParmDo> selectAllRuleParams();

}