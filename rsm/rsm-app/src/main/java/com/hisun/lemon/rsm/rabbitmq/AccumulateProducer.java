package com.hisun.lemon.rsm.rabbitmq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.framework.stream.MultiOutput;
import com.hisun.lemon.framework.stream.producer.Producer;
import com.hisun.lemon.framework.stream.producer.Producers;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 异步累计结果消息生产者
 *
 * <AUTHOR>
 * @create 2017/8/16
 */
@Component
public class AccumulateProducer {

    private static final Logger logger = LoggerFactory.getLogger(AccumulateProducer.class);

    @Resource
    private ObjectMapper objectMapper;

    @Producers({
            @Producer(beanName = "accumulateMessageHandler", channelName = MultiOutput.OUTPUT_SEVEN)
    })
    public TxCheckRiskBo sendAccumulateResult(TxCheckRiskBo txCheckRiskBo) {
        String data = ObjectMapperHelper.writeValueAsString(objectMapper, txCheckRiskBo, true);
        logger.info("风控累计信息：" + data);
        return txCheckRiskBo;
    }

}
