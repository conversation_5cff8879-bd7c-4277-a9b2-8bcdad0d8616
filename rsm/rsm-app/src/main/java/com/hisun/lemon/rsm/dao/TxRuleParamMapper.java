package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface TxRuleParamMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_rule_parm
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(String parmId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_rule_parm
     *
     * @mbggenerated
     */
    int insert(TxRuleParamDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_rule_parm
     *
     * @mbggenerated
     */
    int insertSelective(TxRuleParamDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_rule_parm
     *
     * @mbggenerated
     */
    TxRuleParamDo selectByPrimaryKey(String parmId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_rule_parm
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(TxRuleParamDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_rule_parm
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(TxRuleParamDo record);

    /**
     * 查寻所有限额参数
     *
     * @return 限额参数列表
     */
    List<TxRuleParamDo> selectAllRuleParams();

    /**
     * 通过条件精确匹配
     *
     * @param dcPtyFlg 收付款标志
     * @param txTyp    交易类型
     * @param payTyp   交易类型
     * @param mercLvl  商户等级
     * @return 风控限额参数对象
     */
    List<TxRuleParamDo> selectByExactMatch(@Param("dcPtyFlg") String dcPtyFlg,
                                           @Param("txTyp") String txTyp,
                                           @Param("payTyp") String payTyp,
                                           @Param("mercLvl") String mercLvl);
}