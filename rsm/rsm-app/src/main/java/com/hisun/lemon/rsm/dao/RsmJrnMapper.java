package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.RsmJrnDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

@Mapper
@Component
public interface RsmJrnMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_tx_jrn
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(String jrnNo);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_tx_jrn
     *
     * @mbggenerated
     */
    int insert(RsmJrnDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_tx_jrn
     *
     * @mbggenerated
     */
    RsmJrnDo selectByPrimaryKey(String jrnNo);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_tx_jrn
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(RsmJrnDo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table rsm_tx_jrn
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(RsmJrnDo record);
}