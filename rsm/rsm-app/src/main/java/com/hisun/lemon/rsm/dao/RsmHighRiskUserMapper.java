package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.RsmHighRiskUserDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/3
 */
@Mapper
@Component
public interface RsmHighRiskUserMapper {

    int addHighRiskList(RsmHighRiskUserDo rsmHighRiskUserDo);

    List<RsmHighRiskUserDo> queryAll();
}
