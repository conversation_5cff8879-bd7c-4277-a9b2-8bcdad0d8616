package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.RiskBlackListMapper;
import com.hisun.lemon.rsm.dao.RiskWhiteListMapper;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.RsmJrnReqDTO;
import com.hisun.lemon.rsm.entity.RiskListDo;
import com.hisun.lemon.rsm.entity.RsmJrnDo;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import com.hisun.lemon.rsm.entity.UserStatusDo;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;
import com.hisun.lemon.rsm.rabbitmq.AccumulateProducer;
import com.hisun.lemon.rsm.service.*;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/12
 */
@Service
@Transactional
public class RsmCheckServiceImpl extends BaseService implements IRsmCheckService {

    private static final Logger logger = LoggerFactory.getLogger(IRsmCheckService.class);

    @Resource
    private ITxCheckRiskService txCheckRiskService;

    @Resource
    private IRsmRiskInfoRecService rsmRiskInfoRecService;

    @Resource
    private RiskBlackListMapper riskBlackListMapper;

    @Resource
    private RiskWhiteListMapper riskWhiteListMapper;

    @Resource
    private IRsmJrnService rsmJrnService;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Resource
    private IUserStatusMngService userStatusMngService;

    @Resource
    private AccumulateProducer accumulateProducer;

    @Override
    public RsmTxCheckCode checkBlackAndWhileList(RsmJrnReqDTO rsmJrnReqDTO) throws LemonException {
        logger.debug("Check Param：", rsmJrnReqDTO.toString());
        RsmTxCheckCode codePay = null;
        RsmTxCheckCode codeStl = null;
        RiskCheckUserStatusReqDTO payUser = new RiskCheckUserStatusReqDTO();
        payUser.setId(rsmJrnReqDTO.getPayUserId());
        payUser.setIdTyp(rsmJrnReqDTO.getPayUserTyp());
        payUser.setTxTyp(rsmJrnReqDTO.getTxTyp());
        RiskCheckUserStatusReqDTO stlUser = new RiskCheckUserStatusReqDTO();
        stlUser.setId(rsmJrnReqDTO.getStlUserId());
        stlUser.setIdTyp(rsmJrnReqDTO.getStlUserTyp());
        stlUser.setTxTyp(rsmJrnReqDTO.getTxTyp());

        if (rsmJrnReqDTO.getPayUserId() != null && JudgeUtils.isNotEmpty(rsmJrnReqDTO.getPayUserId()) && !"".equals(rsmJrnReqDTO.getPayUserId())) {
            codePay = checkUserStatus(payUser);
        }

        if (rsmJrnReqDTO.getStlUserId() != null && JudgeUtils.isNotEmpty(rsmJrnReqDTO.getStlUserId()) && !"".equals(rsmJrnReqDTO.getStlUserId())) {
            codeStl = checkUserStatus(stlUser);
        }

        //快捷时检查银行卡是否为黑名单
        RiskCheckUserStatusReqDTO quickCard = new RiskCheckUserStatusReqDTO();
        RsmTxCheckCode quickPay = null;
        if (JudgeUtils.equals(rsmJrnReqDTO.getPayTyp(), Constants.PAY_TYP_QUICK_PAY)) {
            quickCard.setId(rsmJrnReqDTO.getPayCrdNo());
            quickCard.setIdTyp(Constants.ID_TYP_CARD);
            quickCard.setTxTyp(rsmJrnReqDTO.getTxTyp());
            quickPay = checkUserStatus(quickCard);
            if (JudgeUtils.equals(quickPay, RsmTxCheckCode.REFUSE_BY_BLACK_LIST)) {
                throw new RsmException(MsgCd.CARD_IN_BLACK_LIST);
            }
        }

        if (null != codePay) {
            judgeCode(codePay);
        }
        if (null != codeStl) {
            //对于商户需要判断
            try {
                judgeCode(codeStl);
            } catch (LemonException e) {
                String flg = stlUser.getId().substring(0, 6);
                if (JudgeUtils.equals(flg, "888888")) {
                    if (JudgeUtils.equals(e.getMsgCd(), MsgCd.BLACK_LIST_IS_EXIST.getMsgCd())) {
                        throw new RsmException(MsgCd.MERC_IN_BLACK_LIST);
                    }
                    if (JudgeUtils.equals(e.getMsgCd(), MsgCd.USER_STATUS_IS_FROZEN.getMsgCd())) {
                        throw new RsmException(MsgCd.MERC_STATUS_IS_FROZEN);
                    }
                    if (JudgeUtils.equals(e.getMsgCd(), MsgCd.USER_STATUS_IS_PAUSED.getMsgCd())) {
                        throw new RsmException(MsgCd.MERC_STATUS_IS_PAUSED);
                    }
                } else {
                    if (JudgeUtils.equals(e.getMsgCd(), MsgCd.BLACK_LIST_IS_EXIST.getMsgCd())) {
                        throw new RsmException(MsgCd.STL_IN_BLACK_LIST);
                    }
                    if (JudgeUtils.equals(e.getMsgCd(), MsgCd.USER_STATUS_IS_FROZEN.getMsgCd())) {
                        throw new RsmException(MsgCd.STL_STATUS_IS_FROZEN);
                    }
                    if (JudgeUtils.equals(e.getMsgCd(), MsgCd.USER_STATUS_IS_PAUSED.getMsgCd())) {
                        throw new RsmException(MsgCd.STL_STATUS_IS_PAUSED);
                    }
                }
            }
            if (JudgeUtils.notEquals(codeStl, RsmTxCheckCode.PASS)) {
                return codeStl;
            }
        }

        if (null != quickPay) {
            if (JudgeUtils.equals(quickPay, RsmTxCheckCode.PASS_BY_WHITE_LIST)) {
                return RsmTxCheckCode.PASS_BY_WHITE_LIST;
            }
        }
        if (null != codePay) {
            if (JudgeUtils.equals(codePay, RsmTxCheckCode.PASS_BY_WHITE_LIST)) {
                return RsmTxCheckCode.PASS_BY_WHITE_LIST;
            }
        }
        if (null != codeStl) {
            if (JudgeUtils.equals(codeStl, RsmTxCheckCode.PASS_BY_WHITE_LIST)) {
                return RsmTxCheckCode.PASS_BY_WHITE_LIST;
            }
        }
        return RsmTxCheckCode.PASS;
    }

    @Override
    public GenericRspDTO<NoBody> accumulateAmount(RsmJrnReqDTO rsmJrnReqDTO) {
        logger.debug("交易风控额度累计传入参数：" + rsmJrnReqDTO.toString());
        RsmRiskInfoRecDo rsmRiskInfoRecDo = new RsmRiskInfoRecDo();
        TxCheckRiskBo bo = createTxCheckRiskBo(rsmJrnReqDTO);
        rsmRiskInfoRecService.insertRiskInfoRec(rsmRiskInfoRecDo, bo);
        try {
            //记录风控流水
            RsmJrnDo rsmJrnDo = new RsmJrnDo();
            BeanUtils.copyProperties(rsmJrnDo, rsmJrnReqDTO);
            rsmJrnService.addRsmJrn(rsmJrnDo);

            //使用消息队列，异步处理累计
            accumulateProducer.sendAccumulateResult(bo);
//            txCheckRiskService.rsmNotRealTimeCheck(bo);
        } catch (RsmException e) {
            return GenericRspDTO.newInstance(MsgCd.SERVICE_EXCEPTION.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 实时风控按检查规则检查
     *
     * @param rsmJrnReqDTO 实时风控传入对象
     * @return RsmTxCheckCode
     */
    @Override
    public GenericRspDTO<NoBody> checkRisk(RsmJrnReqDTO rsmJrnReqDTO) {
        TxCheckRiskBo riskBo = createTxCheckRiskBo(rsmJrnReqDTO);
        RsmTxCheckCode rsmTxCheckCode = txCheckRiskService.riskRealTimeCheck(riskBo);
        MsgCd msgCd = MsgCd.SUCCESS;
        try {
            switch (rsmTxCheckCode) {
                case EXCEPTION:
                    msgCd = MsgCd.SERVICE_EXCEPTION;
                    break;
                case LESS_THAN_MIN_AMT:
                    msgCd = MsgCd.LESS_THAN_MIN_AMT;
                    break;
                case GREATER_THAN_MAX_AMT:
                    msgCd = MsgCd.GREATER_THAN_MAX_AMT;
                    break;
                case MORE_THAN_DAILY_CUMULATE_AMT:
                    msgCd = MsgCd.MORE_THAN_DAILY_CUMULATE_AMT;
                    break;
                case MORE_THAN_DAILY_CUMULATE_COUNT:
                    msgCd = MsgCd.MORE_THAN_DAILY_CUMULATE_COUNT;
                    break;
                case MORE_THAN_MONTHLY_CUMULATE_AMT:
                    msgCd = MsgCd.MORE_THAN_MONTHLY_CUMULATE_AMT;
                    break;
                case MORE_THAN_MONTHLY_CUMULATE_COUNT:
                    msgCd = MsgCd.MORE_THAN_MONTHLY_CUMULATE_COUNT;
                    break;
            }
        } catch (Exception e) {
            //风控模块程序错误，默认通过
            logger.error("Risk check error:", e);
            return GenericRspDTO.newInstance(MsgCd.SUCCESS.getMsgCd());
        }
        return GenericRspDTO.newInstance(msgCd.getMsgCd());
    }

    private TxCheckRiskBo createTxCheckRiskBo(RsmJrnReqDTO dto) {
        TxCheckRiskBo riskBo = new TxCheckRiskBo();
        riskBo.setStlUserTyp(dto.getStlUserTyp());
        riskBo.setPayUserTyp(dto.getPayUserTyp());
        riskBo.setStlUserId(dto.getStlUserId());
        riskBo.setPayUserId(dto.getPayUserId());
        riskBo.setCcy(dto.getCcy());
        riskBo.setPayTyp(dto.getPayTyp());
        riskBo.setTxTyp(dto.getTxTyp());
        riskBo.setTxSts(dto.getTxSts());
        riskBo.setTxCnl(dto.getTxCnl());
        riskBo.setTxAmt(dto.getTxAmt());
        riskBo.setTxDt(dto.getTxDt());
        riskBo.setTxTm(dto.getTxTm());
        riskBo.setTxOrdNo(dto.getTxOrdNo());
        riskBo.setTxData(dto.getTxData());
        return riskBo;
    }

    /**
     * 先判断用户状态，再判断用户黑白名单
     *
     * @param reqDTO
     * @return
     */
    @Override
    public RsmTxCheckCode checkUserStatus(RiskCheckUserStatusReqDTO reqDTO) {
        String id = reqDTO.getId();
        String idTyp = reqDTO.getIdTyp();

        if (JudgeUtils.isNull(id)) {
            logger.error("check user param" + reqDTO.toString());
            throw new RsmException(MsgCd.ID_IS_NULL);
        }

        if (JudgeUtils.isNull(idTyp)) {
            throw new RsmException(MsgCd.ID_TYP_ERROR);
        }

        String txTyp = reqDTO.getTxTyp();
        if (JudgeUtils.isNull(txTyp)) {
            txTyp = Constants.TX_TYP_ALL;
        }

        RsmTxCheckCode result = RsmTxCheckCode.PASS;

        //按照id和id类型查找黑白名单
        if (JudgeUtils.isNotNull(id) && JudgeUtils.isNotNull(idTyp)) {
            List<RiskListDo> blkLists = riskBlackListMapper.selectByIdAndTypAndTxTyp(id, idTyp, txTyp);
            if (0 != blkLists.size()) {
                return RsmTxCheckCode.REFUSE_BY_BLACK_LIST;
            }
        }
        if (JudgeUtils.isNotNull(id) && JudgeUtils.isNotNull(idTyp)) {
            List<RiskListDo> blkLists = riskWhiteListMapper.selectByIdAndTypAndTxTyp(id, idTyp, txTyp);
            if (0 != blkLists.size()) {
                result = RsmTxCheckCode.PASS_BY_WHITE_LIST;
            }
        }

        //查询是否存在冻结暂停，只有传入为用户或商户号检验
        if (JudgeUtils.equals(idTyp, Constants.ID_TYP_USER_NO)) {

            UserStatusDo userStatusDo;
            try {
                userStatusDo = userStatusMngService.queryUserStatusByUserId(id);
            } catch (LemonException e) {
                logger.error(e.getMsgCd(), e.getMsgInfo());
                throw new LemonException(e.getMsgCd(), e.getMsgInfo());
            }

            //用户没有异常状态为空时
            if (JudgeUtils.isNotNull(userStatusDo)) {
                String sts = userStatusDo.getUserSts();
                if (JudgeUtils.equals(sts, Constants.USER_STS_FREEZE)) {
                    return RsmTxCheckCode.REFUSE_BY_USER_STS_FREEZE;
                }
                if (JudgeUtils.equals(sts, Constants.USER_STS_PAUSE)) {
                    return RsmTxCheckCode.REFUSE_BY_USER_STS_PAUSE;
                }
            }

            //与用户模块交互，如果用户传入为用户或商户还需检查身份证是否在黑白名单
            String idNo = null;
            try {
                GenericRspDTO<UserBasicInfDTO> userInfo = userBasicInfClient.queryUser(id);
                if (JudgeUtils.isNotSuccess(userInfo.getMsgCd())) {
                    logger.error(userInfo.getMsgCd() + userInfo.getMsgInfo());
                    throw new LemonException(userInfo.getMsgCd(), userInfo.getMsgInfo());
                }
                idNo = userInfo.getBody().getIdNo();
                if (JudgeUtils.isNull(idNo)) {
                    return result;
                }
            } catch (LemonException e) {
                logger.error(e.getMsgCd() + e.getMsgInfo());
                throw new LemonException(e.getMsgCd(), e.getMsgInfo());
            }
            RiskCheckUserStatusReqDTO idNoCheckDto = new RiskCheckUserStatusReqDTO();
            idNoCheckDto.setTxTyp(txTyp);
            idNoCheckDto.setId(idNo);
            idNoCheckDto.setIdTyp(Constants.ID_TYP_ID_NO);
            switch (checkUserStatus(idNoCheckDto)) {
                case REFUSE_BY_BLACK_LIST:
                    return RsmTxCheckCode.REFUSE_BY_BLACK_LIST;
                case PASS_BY_WHITE_LIST:
                    result = RsmTxCheckCode.PASS_BY_WHITE_LIST;
            }
        }

        return result;
    }

    //根据状态检查抛出相应异常
    private void judgeCode(RsmTxCheckCode code) {
        //存在黑名单时拒绝
        if (JudgeUtils.equals(code, RsmTxCheckCode.REFUSE_BY_BLACK_LIST)) {
            throw new RsmException(MsgCd.BLACK_LIST_IS_EXIST);
        }
        //存在暂停
        if (JudgeUtils.equals(code, RsmTxCheckCode.REFUSE_BY_USER_STS_PAUSE)) {
            throw new RsmException(MsgCd.USER_STATUS_IS_PAUSED);
        }
        //存在冻结
        if (JudgeUtils.equals(code, RsmTxCheckCode.REFUSE_BY_USER_STS_FREEZE)) {
            throw new RsmException(MsgCd.USER_STATUS_IS_FROZEN);
        }
    }
}