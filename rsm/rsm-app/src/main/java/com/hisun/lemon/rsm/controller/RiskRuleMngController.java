package com.hisun.lemon.rsm.controller;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dto.req.checkrule.*;
import com.hisun.lemon.rsm.dto.req.limitparam.*;
import com.hisun.lemon.rsm.dto.req.riskrule.RiskRuleBaseReqDTO;
import com.hisun.lemon.rsm.dto.req.riskrule.RiskRuleInsertReqDTO;
import com.hisun.lemon.rsm.dto.req.riskrule.RiskRuleQueryAllReqDTO;
import com.hisun.lemon.rsm.dto.req.riskrule.RiskRuleUpdateReqDTO;
import com.hisun.lemon.rsm.dto.res.RiskRuleResDTO;
import com.hisun.lemon.rsm.dto.res.RsmCheckRuleListResDTO;
import com.hisun.lemon.rsm.dto.res.TxRuleParamResDTO;
import com.hisun.lemon.rsm.entity.RiskRuleDo;
import com.hisun.lemon.rsm.entity.RsmCheckRuleListDo;
import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.service.IRiskRuleMngService;
import com.hisun.lemon.rsm.service.IRsmCheckRuleListService;
import com.hisun.lemon.rsm.service.ITxRuleParamMngService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 风控规则管理
 *
 * <AUTHOR>
 * @create 2017/7/5
 */
@Api(value = "风控规则管理", tags = "检查规则，风控规则，交易限制参数")
@RestController
@RequestMapping(value = "/rsm/riskrulemng")
public class RiskRuleMngController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RiskRuleMngController.class);

    @Resource
    private IRiskRuleMngService riskRuleMngService;

    @Resource
    private ITxRuleParamMngService txRuleParamMngService;

    @Resource
    private IRsmCheckRuleListService rsmCheckRuleListService;

    /**
     * 风控规则
     **/

    @ApiOperation(value = "添加风控规则", notes = "添加风控规则")
    @ApiResponse(code = 200, message = "添加风控规则操作结果")
    @PostMapping(value = "/rules")
    public GenericRspDTO<NoBody> addRiskRule(@Validated @RequestBody GenericDTO<RiskRuleInsertReqDTO> reqDTO) {
        RiskRuleInsertReqDTO riskRuleInsertReqDTO = reqDTO.getBody();
        RiskRuleDo riskRuleDo = new RiskRuleDo();
        BeanUtils.copyProperties(riskRuleDo, riskRuleInsertReqDTO);
        try {
            riskRuleMngService.addRule(riskRuleDo);
        } catch (RsmException e) {
            logger.error("Insert risk rule error：" + e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "修改风控规则", notes = "修改风控规则")
    @ApiResponse(code = 200, message = "修改风控规则操作结果")
    @PutMapping(value = "/rules")
    public GenericRspDTO<NoBody> updateRiskRule(@Validated @RequestBody GenericDTO<RiskRuleUpdateReqDTO> reqDTO) {
        RiskRuleUpdateReqDTO riskRuleUpdateReqDTO = reqDTO.getBody();
        RiskRuleDo riskRuleDo = new RiskRuleDo();
        BeanUtils.copyProperties(riskRuleDo, riskRuleUpdateReqDTO);
        riskRuleDo.setRuleId(riskRuleUpdateReqDTO.getRuleId());
        try {
            riskRuleMngService.updateRule(riskRuleDo);
        } catch (RsmException e) {
            logger.error("Update risk rule error：" + e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询风控规则", notes = "查询风控规则")
    @ApiResponse(code = 200, message = "查询风控规则操作结果")
    @GetMapping(value = "/rules")
    public GenericRspDTO<RiskRuleResDTO> queryRiskRule(@Validated RiskRuleBaseReqDTO riskRuleBaseReqDTO) {
        GenericRspDTO<RiskRuleResDTO> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        RiskRuleResDTO riskRuleResDTO = new RiskRuleResDTO();
        RiskRuleDo riskRuleDo;
        try {
            riskRuleDo = riskRuleMngService.queryRule(riskRuleBaseReqDTO.getRuleId());
            BeanUtils.copyProperties(riskRuleResDTO, riskRuleDo);
            genericDTO.setBody(riskRuleResDTO);
        } catch (RsmException e) {
            logger.error("Query risk rule error：" + e);
            genericDTO.setMsgCd(e.getMsgCd());
            return genericDTO;
        }
        return genericDTO;
    }

    @ApiOperation(value = "查询风控规则列表", notes = "查询风控规则列表")
    @ApiResponse(code = 200, message = "查询风控规则列表操作结果")
    @GetMapping(value = "/rules/list")
    public GenericRspDTO<List<RiskRuleResDTO>> queryAllRiskRules(
            @Validated RiskRuleQueryAllReqDTO riskRuleQueryAllReqDTO) {
        GenericRspDTO<List<RiskRuleResDTO>> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        int pageNum = Constants.DEFAULT_PAGE_NUMBER;
        int pageSize = Constants.DEFAULT_PAGE_SIZE;
        if (JudgeUtils.isNotNull(riskRuleQueryAllReqDTO.getPageNum())) {
            pageNum = riskRuleQueryAllReqDTO.getPageNum();
        }
        if (JudgeUtils.isNotNull(riskRuleQueryAllReqDTO.getPageSize())) {
            pageSize = riskRuleQueryAllReqDTO.getPageSize();
        }
        List<RiskRuleResDTO> listDtos = new ArrayList<>();
        List<RiskRuleDo> listDos;
        try {
            listDos = riskRuleMngService.selectAllRules(pageNum, pageSize);
            for (RiskRuleDo riskRuleDo : listDos) {
                RiskRuleResDTO riskRuleResDTO = new RiskRuleResDTO();
                BeanUtils.copyProperties(riskRuleResDTO, riskRuleDo);
                listDtos.add(riskRuleResDTO);
            }
            genericDTO.setBody(listDtos);
        } catch (RsmException e) {
            logger.error("Query all risk rule error：" + e);
            genericDTO.setMsgCd(e.getMsgCd());
        }
        return genericDTO;
    }

    /**
     * 限额参数
     **/

    @ApiOperation(value = "添加交易限额规则参数", notes = "添加交易限额规则参数")
    @ApiResponse(code = 200, message = "添加交易限额规则参数结果")
    @PostMapping(value = "/limitparam")
    public GenericRspDTO<NoBody> addTxRuleParam(@Validated @RequestBody GenericDTO<TxRuleParamInsertReqDTO> reqDTO) {
        TxRuleParamInsertReqDTO txRuleParamInsertReqDTO = reqDTO.getBody();
        TxRuleParamDo txRuleParamDo = new TxRuleParamDo();
        BeanUtils.copyProperties(txRuleParamDo, txRuleParamInsertReqDTO);
        try {
            txRuleParamMngService.addRuleParam(txRuleParamDo);
        } catch (RsmException e) {
            logger.error("Add risk rule limit param error：" + e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "修改交易限额规则参数", notes = "修改交易限额规则参数")
    @ApiResponse(code = 200, message = "修改交易限额规则参数")
    @PutMapping(value = "/limitparam")
    public GenericRspDTO<NoBody> updateTxRuleParam(@Validated @RequestBody GenericDTO<TxRuleParamUpdateReqDTO> reqDTO) {
        TxRuleParamUpdateReqDTO txRuleParamUpdateReqDTO = reqDTO.getBody();
        TxRuleParamDo txRuleParamDo = new TxRuleParamDo();
        txRuleParamUpdateReqDTO.setParmId(txRuleParamUpdateReqDTO.getParmId());
        BeanUtils.copyProperties(txRuleParamDo, txRuleParamUpdateReqDTO);
        try {
            txRuleParamMngService.updateRuleParam(txRuleParamDo);
        } catch (RsmException e) {
            logger.error("Update risk rule limit param error：" + e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "删除交易限额规则参数", notes = "删除交易限额规则参数")
    @ApiResponse(code = 200, message = "删除交易限额规则参数操作结果")
    @DeleteMapping(value = "/limitparam")
    public GenericRspDTO<NoBody> deleteTxRuleParam(@Validated @RequestBody GenericDTO<TxRuleParamBaseReqDTO> reqDTO) {
        TxRuleParamBaseReqDTO txRuleParamBaseReqDTO = reqDTO.getBody();
        try {
            txRuleParamMngService.deleteRuleParam(txRuleParamBaseReqDTO.getParmId());
        } catch (RsmException e) {
            logger.error("Delete risk rule limit param error：" + e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询交易限额规则参数", notes = "查询交易限额规则参数")
    @ApiResponse(code = 200, message = "查询交易限额规则参数操作结果")
    @GetMapping(value = "/limitparam")
    public GenericRspDTO<TxRuleParamResDTO> queryTxRuleParam(
            @Validated TxRuleParamQueryReqDTO txRuleParamQueryReqDTO) {
        GenericRspDTO<TxRuleParamResDTO> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        TxRuleParamResDTO txRuleParamResDTO = new TxRuleParamResDTO();
        TxRuleParamDo txRuleParamDo;
        try {
            txRuleParamDo = txRuleParamMngService.queryRuleParam(txRuleParamQueryReqDTO.getParmId());
            BeanUtils.copyProperties(txRuleParamResDTO, txRuleParamDo);
            genericDTO.setBody(txRuleParamResDTO);
        } catch (RsmException e) {
            logger.error("Query risk rule limit param error：" + e);
            genericDTO.setMsgCd(e.getMsgCd());
            return genericDTO;
        }
        return genericDTO;
    }

    @ApiOperation(value = "查询交易限额规则参数", notes = "查询交易限额规则参数")
    @ApiResponse(code = 200, message = "查询交易限额规则参数结果")
    @GetMapping(value = "/limitparam/list")
    public GenericRspDTO<List<TxRuleParamResDTO>> queryAllTxRuleParams(
            @Validated TxRuleParamQueryAllReqDTO txRuleParamQueryAllReqDTO) {
        GenericRspDTO<List<TxRuleParamResDTO>> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        List<TxRuleParamResDTO> listDtos = new ArrayList<>();
        List<TxRuleParamDo> listDos;
        int pageNum = Constants.DEFAULT_PAGE_NUMBER;
        int pageSize = Constants.DEFAULT_PAGE_SIZE;
        if (JudgeUtils.isNotNull(txRuleParamQueryAllReqDTO.getPageNum())) {
            pageNum = txRuleParamQueryAllReqDTO.getPageNum();
        }
        if (JudgeUtils.isNotNull(txRuleParamQueryAllReqDTO.getPageSize())) {
            pageSize = txRuleParamQueryAllReqDTO.getPageSize();
        }
        try {
            listDos = txRuleParamMngService.selectAllRuleParams(pageNum, pageSize);
            for (TxRuleParamDo txRuleParamDo : listDos) {
                TxRuleParamResDTO txRuleParamResDTO = new TxRuleParamResDTO();
                BeanUtils.copyProperties(txRuleParamResDTO, txRuleParamDo);
                listDtos.add(txRuleParamResDTO);
            }
            genericDTO.setBody(listDtos);
        } catch (RsmException e) {
            logger.error("Query all risk rules limit param error：" + e);
            genericDTO.setMsgCd(e.getMsgCd());
            return genericDTO;
        }
        return genericDTO;
    }

    /**
     * 检查规则
     **/

    @ApiOperation(value = "添加风控检查规则", notes = "添加风控检查规则")
    @ApiResponse(code = 200, message = "添加风控检查规则操作结果")
    @PostMapping(value = "/checkrules")
    public GenericRspDTO<NoBody> addCheckRule(@Validated @RequestBody GenericDTO<RsmCheckRuleListInsertReqDTO> reqDTO) {
        RsmCheckRuleListInsertReqDTO rsmCheckRuleListInsertReqDTO = reqDTO.getBody();
        RsmCheckRuleListDo rsmCheckRuleListDo = new RsmCheckRuleListDo();
        BeanUtils.copyProperties(rsmCheckRuleListDo, rsmCheckRuleListInsertReqDTO);
        try {
            rsmCheckRuleListService.addCheckRule(rsmCheckRuleListDo);
        } catch (RsmException e) {
            logger.error("Insert check rule error：" + e.getMessage());
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "删除风控检查规则", notes = "删除风控检查规则")
    @ApiResponse(code = 200, message = "删除风控检查规则操作结果")
    @DeleteMapping(value = "/checkrules")
    public GenericRspDTO<NoBody> deleteCheckRule(@Validated @RequestBody GenericDTO<RsmCheckRuleListBaseReqDTO> reqDTO) {
        RsmCheckRuleListBaseReqDTO rsmCheckRuleListBaseReqDTO = reqDTO.getBody();
        try {
            rsmCheckRuleListService.deleteCheckRule(rsmCheckRuleListBaseReqDTO.getCheckId());
        } catch (RsmException e) {
            logger.error("Delete check rule error：" + e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询风控检查规则", notes = "查询风控检查规则")
    @ApiResponse(code = 200, message = "查询风控检查规则结果")
    @GetMapping(value = "/checkrules")
    public GenericRspDTO<RsmCheckRuleListResDTO> queryCheckRule(@Validated RsmCheckRuleListQueryReqDTO reqDTO) {
        GenericRspDTO<RsmCheckRuleListResDTO> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        RsmCheckRuleListResDTO rsmCheckRuleListResDTO = new RsmCheckRuleListResDTO();
        RsmCheckRuleListDo rsmCheckRuleListDo;
        try {
            rsmCheckRuleListDo = rsmCheckRuleListService.queryCheckRuleByCheckId(reqDTO.getCheckId());
            BeanUtils.copyProperties(rsmCheckRuleListResDTO, rsmCheckRuleListDo);
            genericDTO.setBody(rsmCheckRuleListResDTO);
        } catch (RsmException e) {
            logger.error("Query check rule error：" + e);
            genericDTO.setMsgCd(e.getMsgCd());
            return genericDTO;
        }
        return genericDTO;
    }

    @ApiOperation(value = "修改风控检查规则", notes = "修改风控检查规则")
    @ApiResponse(code = 200, message = "修改风控检查规则操作结果")
    @PutMapping(value = "/checkrules")
    public GenericRspDTO<NoBody> updateRiskList(@Validated @RequestBody GenericDTO<RsmCheckRuleListUpdateReqDTO> reqDTO) {
        RsmCheckRuleListUpdateReqDTO rsmCheckRuleListUpdateReqDTO = reqDTO.getBody();
        RsmCheckRuleListDo rsmCheckRuleListDo = new RsmCheckRuleListDo();
        BeanUtils.copyProperties(rsmCheckRuleListDo, rsmCheckRuleListUpdateReqDTO);
        try {
            rsmCheckRuleListService.updateCheckRule(rsmCheckRuleListDo);
        } catch (RsmException e) {
            logger.error("修改黑白名单错误：" + e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询风控检查规则列表", notes = "查询风控检查规则列表")
    @ApiResponse(code = 200, message = "查询风控检查规则列表结果")
    @GetMapping(value = "/checkrules/list")
    public GenericRspDTO<List<RsmCheckRuleListResDTO>> queryAllCheckRules(
            @Validated RsmCheckRuleListQueryAllReqDTO rsmCheckRuleListQueryAllReqDTO) {
        GenericRspDTO<List<RsmCheckRuleListResDTO>> genericDTO = new GenericRspDTO<>();
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        List<RsmCheckRuleListResDTO> listDtos = new ArrayList<>();
        List<RsmCheckRuleListDo> listDos;
        int pageNum = Constants.DEFAULT_PAGE_NUMBER;
        int pageSize = Constants.DEFAULT_PAGE_SIZE;
        if (JudgeUtils.isNotNull(rsmCheckRuleListQueryAllReqDTO.getPageNum())) {
            pageNum = rsmCheckRuleListQueryAllReqDTO.getPageNum();
        }
        if (JudgeUtils.isNotNull(rsmCheckRuleListQueryAllReqDTO.getPageSize())) {
            pageSize = rsmCheckRuleListQueryAllReqDTO.getPageSize();
        }
        try {
            listDos = rsmCheckRuleListService.queryAllCheckRules(pageNum, pageSize);
            for (RsmCheckRuleListDo rsmCheckRuleListDo : listDos) {
                RsmCheckRuleListResDTO rsmCheckRuleListResDTO = new RsmCheckRuleListResDTO();
                BeanUtils.copyProperties(rsmCheckRuleListResDTO, rsmCheckRuleListDo);
                listDtos.add(rsmCheckRuleListResDTO);
            }
            genericDTO.setBody(listDtos);
        } catch (RsmException e) {
            logger.error("Query all check rules limit param error：" + e);
            genericDTO.setMsgCd(e.getMsgCd());
            return genericDTO;
        }
        return genericDTO;
    }
}
