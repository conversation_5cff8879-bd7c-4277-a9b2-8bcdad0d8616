package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.framework.cache.redis.RedisCacheable;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.RsmRiskParmMapper;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/21
 */
@Service
@Transactional
public class RsmRiskParamServiceImpl extends BaseService implements IRsmRiskParamService {

    @Resource
    private RsmRiskParmMapper rsmRiskParmMapper;

    @Override
    public boolean addRuleParam(RsmRiskParmDo rsmRiskParmDo) {
        rsmRiskParmDo.setParmId(rsmRiskParmDo.getCpnNm());
        rsmRiskParmDo.setCreateTime(LocalDateTime.now());
        rsmRiskParmDo.setModifyTime(LocalDateTime.now());
        int result = rsmRiskParmMapper.insert(rsmRiskParmDo);
        if (result <= 0) {
            throw new RsmException(MsgCd.INSERT_ERROR, Constants.RSM_TABLE_RISK_PARM);
        }
        return Constants.SUCCESS;
    }

    @Override
    @CacheEvict(cacheNames = "${lemon.cache.cacheName.prefix}.riskParam", cacheResolver = "redisCacheResolver",
            key = "'CACHE.' + #root.targetClass + 'queryRuleParam' + #parmId")
    public boolean deleteRuleParam(String parmId) {
        int result = rsmRiskParmMapper.deleteByPrimaryKey(parmId);
        if (result <= 0) {
            throw new RsmException(MsgCd.DELETE_ERROR, Constants.RSM_TABLE_RISK_PARM);
        }
        return Constants.SUCCESS;
    }

    @Override
    @Transactional(readOnly = true)
    @RedisCacheable(cacheNames = "${lemon.cache.cacheName.prefix}.riskParam",
            key = "'CACHE.' + #root.targetClass + 'queryRuleParam' + #parmId")
    public RsmRiskParmDo queryRuleParam(String parmId) {
        return rsmRiskParmMapper.selectByPrimaryKey(parmId);
    }

    @Override
    @CachePut(cacheNames = "${lemon.cache.cacheName.prefix}.riskParam", cacheResolver = "redisCacheResolver",
            key = "'CACHE.' + #root.targetClass + 'queryRuleParam' + #rsmRiskParmDo.parmId")
    public boolean updateRuleParam(RsmRiskParmDo rsmRiskParmDo) {
        rsmRiskParmDo.setModifyTime(LocalDateTime.now());
        int result = rsmRiskParmMapper.updateByPrimaryKeySelective(rsmRiskParmDo);
        if (result <= 0) {
            throw new RsmException(MsgCd.UPDATE_ERROR, Constants.RSM_TABLE_RISK_PARM);
        }
        return Constants.SUCCESS;
    }

    @Override
    @Transactional(readOnly = true)
    public List<RsmRiskParmDo> selectAllRuleParams(int pageNum, int pageSize) {
        return PageUtils.pageQuery(pageNum, pageSize, () -> rsmRiskParmMapper.selectAllRuleParams());
    }
}
