package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.entity.RsmCheckRuleListDo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/4
 */
public interface IRsmCheckRuleListService {

    /**
     * 根据交易类型检查
     *
     * @param txTyp 交易类型
     * @return RsmCheckRuleListDo
     */
    RsmCheckRuleListDo queryCheckRule(String txTyp);

    /**
     * 添加检查规则
     *
     * @param rsmCheckRuleListDo 检查规则对象
     */
    void addCheckRule(RsmCheckRuleListDo rsmCheckRuleListDo);

    /**
     * 检查规则id
     *
     * @param checkId 检查规则id
     */
    void deleteCheckRule(String checkId);

    /**
     * 查询检查规则
     *
     * @param checkId 交易类型
     */
    RsmCheckRuleListDo queryCheckRuleByCheckId(String checkId);

    /**
     * 更新检查规则
     *
     * @param rsmCheckRuleListDo 检查规则对象
     */
    void updateCheckRule(RsmCheckRuleListDo rsmCheckRuleListDo);

    /**
     * 查询所有检查列表
     *
     * @param pageNum  页码数
     * @param pageSize 页面展示数
     * @return list
     */
    List<RsmCheckRuleListDo> queryAllCheckRules(int pageNum, int pageSize);
}
