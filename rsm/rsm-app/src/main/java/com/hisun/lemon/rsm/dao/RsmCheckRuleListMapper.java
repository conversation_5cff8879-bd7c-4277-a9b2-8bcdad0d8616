package com.hisun.lemon.rsm.dao;

import com.hisun.lemon.rsm.entity.RsmCheckRuleListDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 风控检查规则列表
 *
 * <AUTHOR>
 * @create 2017/8/4
 */
@Mapper
@Component
public interface RsmCheckRuleListMapper {

    /**
     * 获取所有检查规则下所有风控规则
     *
     * @return list
     */
    List<RsmCheckRuleListDo> queryAllCheckRuleLists();

    /**
     * 根据交易类型查询检查规则
     *
     * @param txTyp 交易类型
     * @return list
     */
    RsmCheckRuleListDo queryCheckRuleList(String txTyp);

    /**
     * 根据id查询检查规则
     *
     * @param checkId 检查id
     * @return list
     */
    RsmCheckRuleListDo queryCheckRuleListByCheckId(String checkId);

    /**
     * 新增风控检查规则
     *
     * @param rsmCheckRuleListDo 检查规则对象
     * @return int
     */
    int addCheckRuleList(RsmCheckRuleListDo rsmCheckRuleListDo);

    /**
     * 更新检查规则
     *
     * @param rsmCheckRuleListDo 检查规则对象
     * @return int
     */
    int updateCheckRuleList(RsmCheckRuleListDo rsmCheckRuleListDo);

    /**
     * 删除检查规则
     *
     * @param checkId 检查规则id
     * @return
     */
    int deleteCheckRuleList(String checkId);

}
