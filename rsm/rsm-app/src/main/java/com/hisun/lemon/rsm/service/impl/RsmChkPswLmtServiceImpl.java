package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.cumulative.Cumulative;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.rsm.common.RsmBaseUtil;
import com.hisun.lemon.rsm.enums.RsmTxCheckCode;
import com.hisun.lemon.rsm.service.IRsmChkPswLmtService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2017/7/20
 */
@Service
@Transactional
public class RsmChkPswLmtServiceImpl extends BaseService implements IRsmChkPswLmtService {

    @Resource
    private Cumulative cumulative;

    @Resource
    private ConstantParamClient constantParamClient;

    @Override
    public void cumulativePswAmtLmt(String userId, BigDecimal amt) {
        cumulative.countByMonth("RSM_PSW_LMT_AMT" + RsmBaseUtil.getDateMonth(LocalDate.now()),
                new Cumulative.Dimension(userId, RsmBaseUtil.mulHundred(amt).toString()));
    }

    @Override
    public RsmTxCheckCode checkCumlativeRswAmtLmt(String userId, BigDecimal amt) throws LemonException {
        GenericRspDTO<ConstantParamRspDTO> genericRspDTO = constantParamClient.params("RSM_CHECK_PSW_AMT");
        ConstantParamRspDTO rspDTO = genericRspDTO.getBody();

        //运营没有配置免密额度常量参数时，直接通过
        if (JudgeUtils.isNull(rspDTO)) {
            return RsmTxCheckCode.PASS;
        }

        BigDecimal lmtAmt = new BigDecimal(rspDTO.getParmVal());
        if (JudgeUtils.equals(lmtAmt, BigDecimal.ZERO)) {
            return RsmTxCheckCode.PASS;
        }
        String param = cumulative.queryByMonth("RSM_PSW_LMT_AMT" + RsmBaseUtil.getDateMonth(LocalDate.now()), userId);

        BigDecimal totAmt = new BigDecimal(param);
        totAmt = RsmBaseUtil.divHundred(totAmt).add(amt);

        if (lmtAmt.compareTo(totAmt) < 0) {
            return RsmTxCheckCode.MORE_THEN_PASSWORD_LMT;
        }
        return RsmTxCheckCode.PASS;
    }
}
