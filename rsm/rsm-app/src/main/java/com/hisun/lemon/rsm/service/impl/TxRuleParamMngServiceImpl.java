package com.hisun.lemon.rsm.service.impl;

import com.hisun.lemon.framework.cache.redis.RedisCacheable;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dao.TxRuleParamMapper;
import com.hisun.lemon.rsm.entity.TxRuleParamDo;
import com.hisun.lemon.rsm.service.ITxRuleParamMngService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/7
 */
@Service
@Transactional
public class TxRuleParamMngServiceImpl extends BaseService implements ITxRuleParamMngService {

    @Resource
    private TxRuleParamMapper txRuleParamMapper;

    @Override
    public boolean addRuleParam(TxRuleParamDo txRuleParamDo) {
        txRuleParamDo.setCreateTime(LocalDateTime.now());
        txRuleParamDo.setModifyTime(LocalDateTime.now());
        txRuleParamDo.setParmId(IdGenUtils.generateId("RSM_TX_PARAM", 10));
        int result = txRuleParamMapper.insert(txRuleParamDo);
        if (result <= 0) {
            throw new RsmException(MsgCd.INSERT_ERROR, Constants.RSM_TABLE_TX_PARM);
        }
        return Constants.SUCCESS;
    }

    @Override
    public boolean deleteRuleParam(String parmId) {
        int result = txRuleParamMapper.deleteByPrimaryKey(parmId);
        if (result <= 0) {
            throw new RsmException(MsgCd.DELETE_ERROR, Constants.RSM_TABLE_TX_PARM);
        }
        return Constants.SUCCESS;
    }

    @Override
    @Transactional(readOnly = true)
    public TxRuleParamDo queryRuleParam(String parmId) {
        TxRuleParamDo txRuleParamDo = txRuleParamMapper.selectByPrimaryKey(parmId);
        if (null == txRuleParamDo) {
            throw new RsmException(MsgCd.SELECT_ERROR, Constants.RSM_TABLE_TX_PARM);
        }
        return txRuleParamDo;
    }

    @Override
    public boolean updateRuleParam(TxRuleParamDo txRuleParamDo) {
        txRuleParamDo.setModifyTime(LocalDateTime.now());
        int result = txRuleParamMapper.updateByPrimaryKeySelective(txRuleParamDo);
        if (result <= 0) {
            throw new RsmException(MsgCd.UPDATE_ERROR, Constants.RSM_TABLE_TX_PARM);
        }
        return Constants.SUCCESS;
    }

    @Override
    @Transactional(readOnly = true)
    public List<TxRuleParamDo> selectAllRuleParams(int pageNum, int pageSize) {
        List<TxRuleParamDo> list;
        list = PageUtils.pageQuery(pageNum, pageSize, () -> txRuleParamMapper.selectAllRuleParams());
        if (list.isEmpty()) {
            throw new RsmException(MsgCd.SELECT_ERROR, Constants.RSM_TABLE_RULE);
        }
        return list;
    }

    @Override
//    @RedisCacheable(cacheNames = "${lemon.cache.cacheName.prefix}.txParam",
//            key = "'CACHE.' + #root.targetClass + 'exactMatchParam' + #dcPtyFlg + #txTyp + #payTyp")
    public List<TxRuleParamDo> exactMatchParam(String dcPtyFlg, String txTyp, String payTyp, String mercLvl) {
        return txRuleParamMapper.selectByExactMatch(dcPtyFlg, txTyp, payTyp, mercLvl);
    }
}
