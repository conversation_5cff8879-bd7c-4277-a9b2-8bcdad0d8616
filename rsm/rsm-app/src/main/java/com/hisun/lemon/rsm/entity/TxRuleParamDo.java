package com.hisun.lemon.rsm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 风控规则参数数据对象
 *
 * <AUTHOR>
 * @create 2017/7/7
 */
public class TxRuleParamDo extends BaseDO {
    private String parmId;
    private String acTyp;
    private String lmtLvl;
    private String dcPtyFlg;
    private String txTyp;
    private String payTyp;
    private BigDecimal minAmtLmt;
    private BigDecimal maxAmtLmt;
    private BigDecimal dlyAmtLmt;
    private Integer dlyCntLmt;
    private BigDecimal mlyAmtLmt;
    private Integer mlyCntLmt;

    private String updOprId;
    private LocalDateTime createTime;
    private LocalDateTime modifyTime;
    private LocalDateTime tmSmp;


    public String getParmId() {
        return parmId;
    }

    public void setParmId(String parmId) {
        this.parmId = parmId;
    }

    public String getAcTyp() {
        return acTyp;
    }

    public void setAcTyp(String acTyp) {
        this.acTyp = acTyp;
    }

    public String getLmtLvl() {
        return lmtLvl;
    }

    public void setLmtLvl(String lmtLvl) {
        this.lmtLvl = lmtLvl;
    }

    public String getDcPtyFlg() {
        return dcPtyFlg;
    }

    public void setDcPtyFlg(String dcPtyFlg) {
        this.dcPtyFlg = dcPtyFlg;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getPayTyp() {
        return payTyp;
    }

    public void setPayTyp(String payTyp) {
        this.payTyp = payTyp;
    }

    public BigDecimal getMinAmtLmt() {
        return minAmtLmt;
    }

    public void setMinAmtLmt(BigDecimal minAmtLmt) {
        this.minAmtLmt = minAmtLmt;
    }

    public BigDecimal getMaxAmtLmt() {
        return maxAmtLmt;
    }

    public void setMaxAmtLmt(BigDecimal maxAmtLmt) {
        this.maxAmtLmt = maxAmtLmt;
    }

    public BigDecimal getDlyAmtLmt() {
        return dlyAmtLmt;
    }

    public void setDlyAmtLmt(BigDecimal dlyAmtLmt) {
        this.dlyAmtLmt = dlyAmtLmt;
    }

    public Integer getDlyCntLmt() {
        return dlyCntLmt;
    }

    public void setDlyCntLmt(Integer dlyCntLmt) {
        this.dlyCntLmt = dlyCntLmt;
    }

    public BigDecimal getMlyAmtLmt() {
        return mlyAmtLmt;
    }

    public void setMlyAmtLmt(BigDecimal mlyAmtLmt) {
        this.mlyAmtLmt = mlyAmtLmt;
    }

    public Integer getMlyCntLmt() {
        return mlyCntLmt;
    }

    public void setMlyCntLmt(Integer mlyCntLmt) {
        this.mlyCntLmt = mlyCntLmt;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    @Override
    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    @Override
    public String toString() {
        return "TxRuleParamDo{" +
                "parmId='" + parmId + '\'' +
                ", acTyp='" + acTyp + '\'' +
                ", lmt_lvl='" + lmtLvl + '\'' +
                ", dcPtyFlg='" + dcPtyFlg + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", payTyp='" + payTyp + '\'' +
                ", minAmtLmt=" + minAmtLmt +
                ", maxAmtLmt=" + maxAmtLmt +
                ", dlyAmtLmt=" + dlyAmtLmt +
                ", dlyCntLmt=" + dlyCntLmt +
                ", mlyAmtLmt=" + mlyAmtLmt +
                ", mlyCntLmt=" + mlyCntLmt +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                ", tmSmp=" + tmSmp +
                '}';
    }
}
