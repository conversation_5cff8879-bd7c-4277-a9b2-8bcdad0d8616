package com.hisun.lemon.rsm.common;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.rsm.Constants;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/7/12
 */
public class RsmBaseUtil {

    /**
     * 判断是否为空
     *
     * @param str
     * @return
     */
    public static boolean strIsNull(String str) {
        if ("".equals(str) || str == null || str.isEmpty()) {
            return Constants.SUCCESS;
        }
        return Constants.FAILURE;
    }

    /**
     * 转换LocalDate获取当前月字符串
     *
     * @param localDate
     * @return
     */
    public static String getDateMonth(LocalDate localDate) {
        return localDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }

    /**
     * null值转换为空，以便放入缓存
     *
     * @param str
     * @return
     */
    public static String null2Space(String str) {
        return (null == str) ? "" : str;
    }

    /**
     * 获取手机号码脱敏值
     *
     * @param mblNo 手机号码
     * @return 手机号码脱敏值
     */
    public static String getMblNoHid(String mblNo) {
        String[] str = mblNo.split("-");
        StringBuffer mblHid = new StringBuffer();
        mblHid.append(str[0]).append("-").append(str[1].substring(0, 3))
                .append("****").append(str[1].substring(7));
        return mblHid.toString();
    }

    /**
     * 获取银行卡号，身份证脱敏值
     *
     * @param crdNoOrIdNo 卡号
     * @return
     */
    public static String getcrdNoOrIdNoHid(String crdNoOrIdNo) {
        StringBuffer hid = new StringBuffer();
        int len = crdNoOrIdNo.length();
        hid.append(crdNoOrIdNo.substring(0, 4));
        for (int i = 0; i < len - 8; i++) {
            hid.append("*");
        }
        hid.append(crdNoOrIdNo.substring(len - 4));
        return hid.toString();
    }

    /**
     * 获取银行卡号倒数4位
     *
     * @param crdNo
     * @return
     */
    public static String getCrdLastNo(String crdNo) {
        int len = crdNo.length();
        return crdNo.substring(len - 4);
    }

    public static Map string2Map(String str) {
        String[] data = str.split(",");
        Map<String, String> map = new HashMap<>();
        for (String s : data) {
            String[] temp = s.split("=");
            map.put(temp[0], temp[1]);
        }
        return map;
    }

    /**
     * 元转分
     *
     * @param bigDecimal 金额
     * @return
     */
    public static BigDecimal mulHundred(BigDecimal bigDecimal) {
        return bigDecimal.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_DOWN);
    }

    /**
     * 分转元
     *
     * @param bigDecimal 金额
     * @return
     */
    public static BigDecimal divHundred(BigDecimal bigDecimal) {
        return bigDecimal.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_DOWN);
    }

}
