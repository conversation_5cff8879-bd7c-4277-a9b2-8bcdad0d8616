package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.MsgCd;
import com.hisun.lemon.rsm.entity.RsmHighRiskUserDo;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmHighRiskUserService;
import com.hisun.lemon.rsm.service.IRsmRiskInfoRecService;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户当月发生X笔以上单笔金额超过X万元的交易
 * 参数1：X笔
 * 参数2：X万元
 *
 * <AUTHOR>
 * @create 2017/7/22
 */
@Component
public class RsmRules005 implements SpecialRuleTemplate{

    private static final Logger logger = LoggerFactory.getLogger(RsmRules005.class);

    @Resource
    private IRsmRiskInfoRecService rsmRiskInfoRecService;

    @Resource
    private IRsmRiskParamService rsmRiskParamService;

    @Resource
    private IRsmHighRiskUserService rsmHighRiskUserService;

    @Override
    public void execute(TxCheckRiskBo txCheckRiskBo) {
        //如果风险参数不存在直接跳过
        RsmRiskParmDo rsmRiskParmDo = rsmRiskParamService.queryRuleParam("RsmRules005");
        if (JudgeUtils.isNull(rsmRiskParmDo)) {
            return;
        }
        //参数1，笔数
        int txNum = Integer.valueOf(rsmRiskParmDo.getParm1());
        //参数2，单笔金额阈值
        BigDecimal lmtAmt = new BigDecimal(rsmRiskParmDo.getParm2());

        int dayAgo = LocalDate.now().getDayOfMonth();

        MsgCd result = MsgCd.SUCCESS;
        List<RsmRiskInfoRecDo> recList = rsmRiskInfoRecService.queryRiskInfoRec(txCheckRiskBo.getPayUserId(),
                Constants.CAP_FLW_IN, dayAgo);
        if (recList.size() == 0) {
            return;
        }

        int num = 0;
        for (RsmRiskInfoRecDo rec : recList) {
            if (rec.getTxAmt().compareTo(lmtAmt) > 0) {
                num++;
            }
        }
        if (num > txNum) {
            logger.warn(RsmRules005.class.getName());

            RsmHighRiskUserDo rsmHighRiskUserDo = new RsmHighRiskUserDo();
            rsmHighRiskUserDo.setUserId(txCheckRiskBo.getPayUserId());
            rsmHighRiskUserDo.setTxTyp(txCheckRiskBo.getTxTyp());
            rsmHighRiskUserDo.setRiskOpr(Constants.RISK_OPR_SPS);
            //TODO 风险描述，或多语言支持
            rsmHighRiskUserDo.setRiskDesc("123");
            rsmHighRiskUserDo.setOprSts(Constants.OPR_STS_RISK);
            rsmHighRiskUserDo.setRiskSorc(Constants.RISK_SORC);
            rsmHighRiskUserDo.setTxOrdNo(txCheckRiskBo.getTxOrdNo());
            rsmHighRiskUserDo.setRiskParm(this.getClass().getSimpleName());
            rsmHighRiskUserDo.setCreateTime(LocalDateTime.now());
            rsmHighRiskUserDo.setModifyTime(LocalDateTime.now());
            rsmHighRiskUserService.addUserToHighList(rsmHighRiskUserDo);
        }

    }
}
