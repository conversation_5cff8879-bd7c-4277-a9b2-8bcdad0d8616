package com.hisun.lemon.rsm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 交易风控流水
 *
 * <AUTHOR>
 * @create 2017/7/11
 */
public class RsmJrnDo {

    /**
     * @Fields jrnNo 风控流水号
     */
    private String jrnNo;

    /**
     * @Fields stlUserId 收方id
     */
    private String stlUserId;

    /**
     * @Fields stlUserTyp 收方用户类型
     */
    private String stlUserTyp;

    /**
     * @Fields payUserId 付方id
     */
    private String payUserId;

    /**
     * @Fields payUserTyp 付方用户类型
     */
    private String payUserTyp;

    /**
     * @Fields txTyp 交易类型
     */
    private String txTyp;

    /**
     * @Fields txSts 交易状态
     */
    private String txSts;

    /**
     * @Fields txCnl 交易渠道
     */
    private String txCnl;

    /**
     * @Fields txAmt 交易金额
     */
    private String txAmt;

    /**
     * @Fields ccy 币种
     */
    private String ccy;

    /**
     * @Fields payTyp 支付方式
     */
    private String payTyp;

    /**
     * @Fields txDt 交易日期
     */
    private LocalDate txDt;

    /**
     * @Fields txTm 交易时间
     */
    private LocalTime txTm;

    /**
     * @Fields txJrnNo 原交易流水
     */
    private String txJrnNo;

    /**
     * @Fields txOrdNo 原交易订单号
     */
    private String txOrdNo;

    /**
     * @Fields txData 交易数据
     */
    private String txData;

    /**
     * @Fields ruleId 规则id
     */
    private String ruleId;

    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;

    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getStlUserId() {
        return stlUserId;
    }

    public void setStlUserId(String stlUserId) {
        this.stlUserId = stlUserId;
    }

    public String getStlUserTyp() {
        return stlUserTyp;
    }

    public void setStlUserTyp(String stlUserTyp) {
        this.stlUserTyp = stlUserTyp;
    }

    public String getPayUserId() {
        return payUserId;
    }

    public void setPayUserId(String payUserId) {
        this.payUserId = payUserId;
    }

    public String getPayUserTyp() {
        return payUserTyp;
    }

    public void setPayUserTyp(String payUserTyp) {
        this.payUserTyp = payUserTyp;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getTxSts() {
        return txSts;
    }

    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    public String getTxCnl() {
        return txCnl;
    }

    public void setTxCnl(String txCnl) {
        this.txCnl = txCnl;
    }

    public String getTxAmt() {
        return txAmt;
    }

    public void setTxAmt(String txAmt) {
        this.txAmt = txAmt;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getPayTyp() {
        return payTyp;
    }

    public void setPayTyp(String payTyp) {
        this.payTyp = payTyp;
    }

    public LocalDate getTxDt() {
        return txDt;
    }

    public void setTxDt(LocalDate txDt) {
        this.txDt = txDt;
    }

    public LocalTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalTime txTm) {
        this.txTm = txTm;
    }

    public String getTxJrnNo() {
        return txJrnNo;
    }

    public void setTxJrnNo(String txJrnNo) {
        this.txJrnNo = txJrnNo;
    }

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public String getTxData() {
        return txData;
    }

    public void setTxData(String txData) {
        this.txData = txData;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RsmJrnDo{" +
                "jrnNo='" + jrnNo + '\'' +
                ", stlUserId='" + stlUserId + '\'' +
                ", stlUserTyp='" + stlUserTyp + '\'' +
                ", payUserId='" + payUserId + '\'' +
                ", payUserTyp='" + payUserTyp + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", txSts='" + txSts + '\'' +
                ", txCnl='" + txCnl + '\'' +
                ", txAmt='" + txAmt + '\'' +
                ", ccy='" + ccy + '\'' +
                ", payTyp='" + payTyp + '\'' +
                ", txDt=" + txDt +
                ", txTm=" + txTm +
                ", txJrnNo='" + txJrnNo + '\'' +
                ", txOrdNo='" + txOrdNo + '\'' +
                ", txData='" + txData + '\'' +
                ", ruleId='" + ruleId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
