package com.hisun.lemon.rsm.risklibrary;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.rsm.bo.TxCheckRiskBo;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.entity.RsmHighRiskUserDo;
import com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo;
import com.hisun.lemon.rsm.entity.RsmRiskParmDo;
import com.hisun.lemon.rsm.service.IRsmHighRiskUserService;
import com.hisun.lemon.rsm.service.IRsmRiskInfoRecService;
import com.hisun.lemon.rsm.service.IRsmRiskParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;

/**
 * 个人客户累计X天内单个账户向X个以上账户注出资金X万元以上
 * 参数1：累计X天
 * 参数2：X个以上账户
 * 参数3：注出资金X元
 *
 * <AUTHOR>
 * @create 2017/7/21
 */
@Component
public class RsmRules002 implements SpecialRuleTemplate {

    private static final Logger logger = LoggerFactory.getLogger(RsmRules002.class);

    @Resource
    private IRsmRiskInfoRecService rsmRiskInfoRecService;

    @Resource
    private IRsmRiskParamService rsmRiskParamService;

    @Resource
    private IRsmHighRiskUserService rsmHighRiskUserService;

    @Override
    public void execute(TxCheckRiskBo txCheckRiskBo) {
        //如果风险参数不存在直接跳过
        RsmRiskParmDo rsmRiskParmDo = rsmRiskParamService.queryRuleParam("RsmRules002");
        if (JudgeUtils.isNull(rsmRiskParmDo)) {
            return;
        }
        //参数1，时间天数
        int dayAgo = Integer.valueOf(rsmRiskParmDo.getParm1());
        //参数2，转出账户数
        int txNum = Integer.valueOf(rsmRiskParmDo.getParm2());
        //参数3，注出资金限额
        BigDecimal riskMaxAmt = new BigDecimal(rsmRiskParmDo.getParm3());

        List<RsmRiskInfoRecDo> recList = rsmRiskInfoRecService.queryRiskInfoRec(txCheckRiskBo.getPayUserId(),
                Constants.CAP_FLW_OUT, dayAgo);
        if (recList.isEmpty()) {
            return;
        }
        BigDecimal outAmt = BigDecimal.ZERO;
        HashSet<String> txAcNum = new HashSet<>();
        for (RsmRiskInfoRecDo rec : recList) {
            txAcNum.add(rec.getTxUserId());
            outAmt = outAmt.add(rec.getTxAmt());
        }
        if (outAmt.compareTo(riskMaxAmt) > 0 && txAcNum.size() > txNum) {
            logger.warn(RsmRules002.class.getName());

            RsmHighRiskUserDo rsmHighRiskUserDo = new RsmHighRiskUserDo();
            rsmHighRiskUserDo.setUserId(txCheckRiskBo.getPayUserId());
            rsmHighRiskUserDo.setTxTyp(txCheckRiskBo.getTxTyp());
            rsmHighRiskUserDo.setRiskOpr(Constants.RISK_OPR_SPS);
            //TODO 风险描述，或多语言支持
            rsmHighRiskUserDo.setRiskDesc("123");
            rsmHighRiskUserDo.setOprSts(Constants.OPR_STS_RISK);
            rsmHighRiskUserDo.setRiskSorc(Constants.RISK_SORC);
            rsmHighRiskUserDo.setTxOrdNo(txCheckRiskBo.getTxOrdNo());
            rsmHighRiskUserDo.setRiskParm(this.getClass().getSimpleName());
            rsmHighRiskUserDo.setCreateTime(LocalDateTime.now());
            rsmHighRiskUserDo.setModifyTime(LocalDateTime.now());
        }
    }
}
