package com.hisun.lemon.rsm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.common.RsmException;
import com.hisun.lemon.rsm.dto.req.userstatus.*;
import com.hisun.lemon.rsm.dto.res.UserStatusResDTO;
import com.hisun.lemon.rsm.entity.UserStatusDo;
import com.hisun.lemon.rsm.service.IUserStatusMngService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 冻结暂停管理
 *
 * <AUTHOR>
 * @create 2017/8/10
 */
@Api(value = "用户状态管理", tags = "用户状态管理（暂停，冻结）")
@RestController
@RequestMapping(value = "/rsm/user")
public class UserStatusMngController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(UserStatusMngController.class);

    @Resource
    private IUserStatusMngService userStatusMngService;

    @ApiOperation(value = "添加冻结暂停记录", notes = "添加冻结暂停记录")
    @ApiResponse(code = 200, message = "添加冻结暂停记录结果")
    @PostMapping(value = "/status")
    public GenericRspDTO<NoBody> addRecord(@Validated @RequestBody GenericDTO<UserStatusInsertReqDTO> reqDTO) {
        GenericRspDTO<NoBody> genericRspDTO = GenericRspDTO.newSuccessInstance();
        try {
            UserStatusInsertReqDTO userStatusInsertReqDTO = reqDTO.getBody();
            UserStatusDo userStatusDo = new UserStatusDo();
            BeanUtils.copyProperties(userStatusDo, userStatusInsertReqDTO);
            userStatusMngService.addUserStatusRec(userStatusDo, userStatusInsertReqDTO.getMblNo());
        } catch (LemonException e) {
            logger.error(e.getMsgCd());
            genericRspDTO.setMsgCd(e.getMsgCd());
            return genericRspDTO;
        }
        return genericRspDTO;
    }

    @ApiOperation(value = "删除冻结暂停记录", notes = "删除冻结暂停记录")
    @ApiResponse(code = 200, message = "删除冻结暂停记录结果")
    @DeleteMapping(value = "/status")
    public GenericRspDTO<NoBody> deleteRecord(@Validated @RequestBody GenericDTO<UserStatusDeleteReqDTO> reqDTO) {
        GenericRspDTO<NoBody> genericRspDTO = GenericRspDTO.newSuccessInstance();
        try {
            userStatusMngService.deleteUserStatusRec(reqDTO.getUserId());
        } catch (RsmException e) {
            logger.error(e.getMsgCd());
            genericRspDTO.setMsgCd(e.getMsgCd());
            return genericRspDTO;
        }
        return genericRspDTO;
    }

    @ApiOperation(value = "修改冻结暂停记录", notes = "修改冻结暂停记录")
    @ApiResponse(code = 200, message = "修改冻结暂停记录结果")
    @PutMapping(value = "/status")
    public GenericRspDTO<NoBody> updateRecord(@Validated @RequestBody GenericDTO<UserStatusUpdateReqDTO> reqDTO) {
        GenericRspDTO<NoBody> genericRspDTO = GenericRspDTO.newSuccessInstance();
        UserStatusUpdateReqDTO userStatusUpdateReqDTO = reqDTO.getBody();
        UserStatusDo userStatusDo = new UserStatusDo();
        BeanUtils.copyProperties(userStatusDo, userStatusUpdateReqDTO);
        try {
            userStatusMngService.updateUserStatusRec(userStatusDo);
        } catch (RsmException e) {
            logger.error(e.getMsgCd());
            genericRspDTO.setMsgCd(e.getMsgCd());
            return genericRspDTO;
        }
        return genericRspDTO;
    }

    @ApiOperation(value = "查询冻结暂停记录", notes = "查询冻结暂停记录")
    @ApiResponse(code = 200, message = "查询冻结暂停记录结果")
    @GetMapping(value = "/status")
    public GenericRspDTO<UserStatusResDTO> queryRecord(@Validated UserStatusQueryReqDTO userStatusQueryReqDTO) {
        GenericRspDTO<UserStatusResDTO> genericRspDTO = new GenericRspDTO<>();
        genericRspDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        UserStatusResDTO userStatusResDTO = new UserStatusResDTO();
        try {
            UserStatusDo userStatusDo = userStatusMngService.queryUserStatusByUserId(userStatusQueryReqDTO.getUserId());
            BeanUtils.copyProperties(userStatusResDTO, userStatusDo);
            genericRspDTO.setBody(userStatusResDTO);
        } catch (RsmException e) {
            logger.error(e.getMsgCd());
            genericRspDTO.setMsgCd(e.getMsgCd());
            return genericRspDTO;
        }
        return genericRspDTO;
    }

    @ApiOperation(value = "查询冻结暂停记录列表", notes = "查询冻结暂停记录列表")
    @ApiResponse(code = 200, message = "查询冻结暂停记录列表结果")
    @GetMapping(value = "/status/list")
    public GenericRspDTO<List<UserStatusResDTO>> queryAllRecords(@Validated UserStatusQueryAllReqDTO reqDTO) {
        GenericRspDTO<List<UserStatusResDTO>> genericRspDTO = new GenericRspDTO<>();
        genericRspDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        List<UserStatusResDTO> listDto = new ArrayList<>();
        List<UserStatusDo> listDo;
        int pageNum = Constants.DEFAULT_PAGE_NUMBER;
        int pageSize = Constants.DEFAULT_PAGE_SIZE;
        if (JudgeUtils.isNotNull(reqDTO.getPageNum())) {
            pageNum = reqDTO.getPageNum();
        }
        if (JudgeUtils.isNotNull(reqDTO.getPageSize())) {
            pageSize = reqDTO.getPageSize();
        }
        try {
            listDo = userStatusMngService.queryAllUserStatusRec(reqDTO.getUserSts(), pageNum, pageSize);
            for (UserStatusDo userStatusDo : listDo) {
                UserStatusResDTO userStatusResDTO = new UserStatusResDTO();
                BeanUtils.copyProperties(userStatusResDTO, userStatusDo);
                listDto.add(userStatusResDTO);
            }
            genericRspDTO.setBody(listDto);
        } catch (RsmException e) {
            logger.error("Select error：" + e);
            genericRspDTO.setMsgCd(e.getMsgCd());
            return genericRspDTO;
        }
        return genericRspDTO;
    }
}
