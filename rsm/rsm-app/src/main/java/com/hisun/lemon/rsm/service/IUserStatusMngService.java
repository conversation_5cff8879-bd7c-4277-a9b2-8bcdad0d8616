package com.hisun.lemon.rsm.service;

import com.hisun.lemon.rsm.entity.UserStatusDo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/10
 */
public interface IUserStatusMngService {

    /**
     * 根据用户id查询用户状态
     *
     * @param userId 用户id
     * @return
     */
    UserStatusDo queryUserStatusByUserId(String userId);

    /**
     * 查询暂停冻结状态列表
     *
     * @param userSts  状态
     * @param pageNum  页数
     * @param pageSize 页面展示数
     * @return
     */
    List<UserStatusDo> queryAllUserStatusRec(String userSts, int pageNum, int pageSize);

    /**
     * 添加
     *
     * @param userStatusDo
     */
    void addUserStatusRec(UserStatusDo userStatusDo, String mblNo);

    /**
     * 删除
     *
     * @param userId
     */
    void deleteUserStatusRec(String userId);

    /**
     * 更新
     *
     * @param userStatusDo
     */
    void updateUserStatusRec(UserStatusDo userStatusDo);
}
