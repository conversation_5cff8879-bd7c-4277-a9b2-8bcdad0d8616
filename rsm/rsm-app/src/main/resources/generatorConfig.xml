<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <classPathEntry location="D:\DbVisualizer-8.0.7\jdbc\mysql\mysql-connector-java-5.1.42-bin.jar"/>
    <context id="DB2Tables"  targetRuntime="MyBatis3">
        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>
        <jdbcConnection driverClass="com.mysql.jdbc.Driver" connectionURL="**********************************************"
                        userId="rsm" password="rsm">
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <javaModelGenerator targetPackage="com.hisun.lemon.rsm.entity" targetProject="D:\163" />
        <sqlMapGenerator targetPackage="com.a" targetProject="D:\163" />
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.hisun.lemon.rsm.dao" targetProject="D:\163" />

        <!--
		<table tableName="project_follow" domainObjectName="ProjectFollow" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
		<table tableName="in_comments" domainObjectName="InComments" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
		<table tableName="in_project_file" domainObjectName="InProjectFile" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
		<table tableName="member_apply" domainObjectName="MemberApply" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
		<table tableName="project_member" domainObjectName="ProjectMember" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
		<table tableName="recruit_member" domainObjectName="RecruitMember" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
		<table tableName="recruit" domainObjectName="Recruit" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
		 -->

        <table tableName="rsm_risk_parm" domainObjectName="RsmRiskParmDo" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
            <generatedKey column="parm_id" sqlStatement="MySql" identity="true"/>
        </table>
    </context>
</generatorConfiguration>


