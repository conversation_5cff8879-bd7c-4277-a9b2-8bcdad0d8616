/**
 *	支付风控黑名单表
 */
# drop index uk_rsm_blkid_txtyp on rsm_blk_list;
drop table if exists rsm_blk_list;
create table rsm_blk_list(
	blk_list_id		varchar(32)   not null comment '黑名单id',
	id_typ			  char(2) 	     not null comment '证件类型,01-用户id，02-银行卡，03-身份证，04-手机号',
	id 				    varchar(64)   not null comment '账户号，对应的用户id、商户id、银行卡、身份证、手机号',
	id_hid			  varchar(64)   not null comment '脱敏值',
	crd_no_last		char(4)				           comment '银行卡末4位，证件类型为02的时候需要记录',
	tx_typ 			  varchar(8) 	   not null comment '交易类型',
	begin_dt		  datetime    	 not null comment '生效时间',
	end_dt			  datetime 	     not null comment '失效时间',
	eff_flg			  char(1)		     not null comment '生效标志，0-失效，1-生效',
	blk_list_sorc	varchar(32)	   not null comment '黑名单来源',
	blk_list_rsn	varchar(256)  not null comment '黑名单原因',
	upd_opr_id 		varchar(10)   not null comment '修改操作员',
	create_time	  datetime 		   not null comment '创建时间',
	modify_time		datetime 		   not null comment '修改时间',
	tm_smp 			  timestamp 	   not null default current_timestamp on update current_timestamp comment '时间戳',
	primary key (blk_list_id)
);
alter table rsm_blk_list comment '支付风控黑名单表';
# create unique index uk_rsm_blkid_txtyp on rsm_blk_list(
#    id,
#    tx_typ
# );

/**
 *	支付风控白名单表
 */
# drop index uk_rsm_whtid_txtyp on rsm_wht_list;
drop table if exists rsm_wht_list;
create table rsm_wht_list(
	wht_list_id		varchar(32)  not null comment '白名单id',
	id_typ			  char(2) 	    not null comment '证件类型,01-用户id，02-银行卡，03-身份证，04-手机号',
	id 				    varchar(64)  not null comment '账户号，对应的用户id、商户id、银行卡、身份证、手机号',
	id_hid			  varchar(64)  not null comment '脱敏值',
	crd_no_last		char(4)				         comment '银行卡末4位，证件类型为02的时候需要记录',
	tx_typ 			  varchar(8) 	  not null comment '交易类型',
	begin_dt		  datetime 	    not null comment '生效时间',
	end_dt			  datetime 	    not null comment '失效时间',
	eff_flg			  char(1)		    not null comment '生效标志，0-失效，1-生效',
	wht_list_sorc	varchar(32)	  not null comment '白名单来源',
	wht_list_rsn	varchar(256) not null comment '白名单原因',
	upd_opr_id 		varchar(10)  not null comment '修改操作员',
	create_time		datetime 		  not null comment '创建日期',
	modify_time		datetime 		  not null comment '修改日期',
	tm_smp 			timestamp 	not null default current_timestamp on update current_timestamp comment '时间戳',
	primary key (wht_list_id)
);
alter table rsm_wht_list comment '支付风控白名单表';
# create unique index uk_rsm_whtid_txtyp on rsm_wht_list(
#    id, tx_typ
# );


/**
 * 风控检查列表
 */
drop index uk_rsm_chk_list_tx_typ on rsm_check_rule_list;
drop table if exists rsm_check_rule_list;
create table rsm_check_rule_list(
  check_id      varchar(10) not null comment '检查id',
  tx_typ        varchar(8)  not null comment '交易类型',
  lmt_lvl       char(1)     not null comment '限制级别，1-用户级别',
  rule_desc     varchar(256)not null comment '检查描述，已配置的风控列表',
  opr_typ       char(1)     not null comment '操作类型，1-拒绝交易',
  upd_opr_id 		varchar(10) not null comment '修改操作员',
  create_time   datetime    not null comment '创建时间',
  modify_time   datetime    not null comment '修改时间',
  tm_smp 			  timestamp 	 not null default current_timestamp on update current_timestamp comment '时间戳',
  primary key (check_id)
);
alter table rsm_check_rule_list comment '风控检查列表';
create unique index uk_rsm_chk_list_tx_typ on rsm_check_rule_list(
  tx_typ
);

/**
 * 风控规则表
 */
drop index uk_rsm_ruleinfo_ruletyp_dcptyflg on rsm_rule_info;
drop table if exists rsm_rule_info;
create table rsm_rule_info(
	rule_id			  varchar(4)  not null comment '风控规则id',
	rule_nm			  varchar(64) not null comment '风控规则名字',
	rule_desc		  varchar(256)not null comment '风控规则描述',
	rule_typ		  char(2)		   not null comment '风控规则类型，01-频次，02-额度，03-渠道',
  dc_pty_flg		char(1)		   not null comment '收付款标志，0-全部，1-收款方，2-付款方',
  cpn_nm			  varchar(128)          comment '组件名称',
	upd_opr_id 		varchar(10) not null comment '修改操作员',
  create_time   datetime    not null comment '创建时间',
  modify_time   datetime    not null comment '修改时间',
	tm_smp 			  timestamp 	 not null default current_timestamp on update current_timestamp comment '时间戳',
	primary key (rule_id)
);
alter table rsm_rule_info comment '支付风控规则表';
create unique index uk_rsm_ruleinfo_ruletyp_dcptyflg on rsm_rule_info(
  rule_typ, dc_pty_flg
);

/**
 * 支付风控交易限额参数表
 */
drop index uk_rsm_ruleid on rsm_rule_parm;
drop table if exists rsm_rule_parm;
create table rsm_rule_parm(
	parm_id			  varchar(32)	  not null comment '风控参数id',
	ac_typ			  char(3)		    not null comment '账户类型',
  lmt_lvl       char(1)      not null comment '限制级别，1-用户级别',
  dc_pty_flg		char(1)		    not null comment '收付款标志，0-全部，1-收款方，2-付款方',
  tx_typ        varchar(8)   not null comment '交易类型',
	pay_typ			  char(2)		    not null comment '支付方式，0-全部，1-账户余额，2-账户快捷，3-海币账户，4-微信支付，5-支付宝，6-翼支付',
	min_amt_lmt		decimal(18,2)not null comment '单笔最小金额',
	max_amt_lmt		decimal(18,2)not null comment '单笔最大金额',
	dly_amt_lmt		decimal(18,2)not null comment '日累计限额',
	dly_cnt_lmt		int 		      not null comment '日累计次数限制',
	mly_amt_lmt		decimal(18,2)not null comment '月累计限额',
	mly_cnt_lmt		int 		      not null comment '月累计次数限制',
	upd_opr_id 		varchar(10)  not null comment '修改操作员',
  create_time   datetime     not null comment '创建时间',
  modify_time   datetime     not null comment '修改时间',
	tm_smp 			  timestamp 	  not null default current_timestamp on update current_timestamp comment '时间戳',
	primary key (parm_id)
);
alter table rsm_rule_parm comment '支付风控交易参数表';
create unique index uk_rsm_ruleid on rsm_rule_parm(
  ac_typ, dc_pty_flg, tx_typ, pay_typ
);

/**
 * 交易风控流水表
 */
drop table if exists rsm_tx_jrn;
create table rsm_tx_jrn(
  jrn_no        varchar(32)   not null comment '风控流水号',
  stl_user_id   varchar(64)             comment '收方用户id',
  stl_user_typ  char(2)                 comment '收方用户类型',
  pay_user_id   varchar(64)             comment '付方用户id',
  pay_user_typ  char(2)                 comment '付方用户类型',
  tx_typ        varchar(8)    not null comment '交易类型',
  tx_sts        char(1)       not null comment '交易状态',
  tx_cnl        varchar(8)    not null comment '交易渠道',
  tx_amt        decimal(18,2) not null comment '交易金额',
  ccy           char(3)       not null comment '交易币种',
  pay_typ       char(2)       not null comment '支付方式',
  tx_dt         date          not null comment '交易日期',
  tx_tm         time          not null comment '交易时间',
  tx_jrn_no     varchar(32)   not null comment '原交易流水号',
  tx_ord_no     varchar(32)   not null comment '原交易订单号',
  tx_data       varchar(1024) not null comment '交易数据',
  rule_id       varchar(10)             comment '风控规则id',
  create_time   datetime      not null comment '创建时间',
  modify_time   datetime      not null comment '修改时间',
  tm_smp 			  timestamp 	  not null default current_timestamp on update current_timestamp comment '时间戳',
  primary key (jrn_no)
);
alter table rsm_tx_jrn comment '交易风控流水表';

/**
 * 风险记录表
 */
drop table if exists rsm_risk_info_rec;
create table rsm_risk_info_rec(
  rec_id        varchar(32)   not null comment '记录id',
  user_id       varchar(64)   not null comment '内部用户号',
  user_typ      char(3)       not null comment '账户类别',
  tx_user_id    varchar(64)            comment '交易另一方id',
  tx_user_typ   varchar(64)            comment '交易另一方类型',
  cap_flw       char(1)       not null comment '资金流向：0-流入，1-流出',
  tx_typ        varchar(8)    not null comment '交易类型',
  tx_cnl        varchar(8)    not null comment '交易渠道',
  tx_sts        char(1)       not null comment '交易状态',
  tx_dt         date          not null comment '交易日期',
  tx_tm         time          not null comment '交易时间',
  tx_amt        decimal(18,2) not null comment '交易金额',
  ccy           char(3)       not null comment '币种',
  crd_typ       char(1)                comment '卡类型',
  ip_addr       varchar(15)   not null comment 'ip地址',
  tm_smp 			  timestamp 	  not null default current_timestamp on update current_timestamp comment '时间戳',
  primary key (rec_id)
);
alter table rsm_risk_info_rec comment '风险记录信息表';

/**
 * 风险参数表
 */
drop index uk_rsm_risk_cpn_nm on rsm_risk_parm;
drop table if exists rsm_risk_parm;
create table rsm_risk_parm(
  parm_id			  varchar(20)	 not null comment '风险参数id',
  risk_desc     varchar(128)not null comment '风险规则描述',
  cpn_nm			  varchar(128)not null comment '组件名称',
  parm1         varchar(20)           comment '参数1',
  parm2         varchar(20)           comment '参数2',
  parm3         varchar(20)           comment '参数3',
  parm4         varchar(20)           comment '参数4',
  parm5         varchar(20)           comment '参数5',
  parm6         varchar(20)           comment '参数6',
  parm7         varchar(20)           comment '参数7',
  parm8         varchar(20)           comment '参数8',
  upd_opr_id 		varchar(10) not null comment '修改操作员',
  create_time   datetime    not null comment '创建时间',
  modify_time   datetime    not null comment '修改时间',
  tm_smp 			  timestamp 	 not null default current_timestamp on update current_timestamp comment '时间戳',
  primary key (parm_id)
);
alter table rsm_risk_parm comment '风险参数表';
create unique index uk_rsm_risk_cpn_nm on rsm_risk_parm(
  cpn_nm
);

/**
 * 风险用户列表
 */
drop table if exists rsm_high_risk_user;
create table rsm_high_risk_user(
  rec_id      int AUTO_INCREMENT comment '事件id',
  tx_typ       varchar(8)  not null comment '交易类型',
  user_id     varchar(10) not null comment '用户id',
  risk_opr    char(1) not null comment '可疑行为',
  risk_desc   varchar(128)not null comment '可疑行为描述',
  risk_parm   varchar(10) not null comment '触发风险规则',
  opr_sts     char(1)  not null comment '处理状态，0-风险事件',
  risk_sorc   varchar(10)  not null comment '风险来源，1-风控管理系统',
  tx_ord_no   varchar(20) not null comment '原交易订单号',
  create_time datetime    not null comment '创建时间',
  modify_time datetime    not null comment '修改时间',
  tm_smp 			timestamp 	 not null default current_timestamp on update current_timestamp comment '时间戳',
  primary key (rec_id)
);

/**
 * 问题状态用户列表
 */
drop table if exists rsm_user_status;
create table rsm_user_status(
  user_id     varchar(10) not null comment '用户id',
  user_sts    char(2)     not null comment '用户状态，0-正常，1-冻结，2-暂停',
  rmk         varchar(256)not null comment '备注',
  upd_opr_id  varchar(10) not null comment '操作员id',
  create_time datetime    not null comment '创建时间',
  modify_time datetime    not null comment '修改时间',
  tm_smp 			timestamp 	 not null default current_timestamp on update current_timestamp comment '时间戳',
  primary key (user_id)
);