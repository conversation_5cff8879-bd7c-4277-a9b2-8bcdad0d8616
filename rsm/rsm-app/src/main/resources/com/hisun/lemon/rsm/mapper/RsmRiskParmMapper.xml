<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.rsm.dao.RsmRiskParmMapper" >
  <resultMap id="BaseResultMap" type="com.hisun.lemon.rsm.entity.RsmRiskParmDo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="parm_id" property="parmId" jdbcType="VARCHAR" />
    <result column="risk_desc" property="riskDesc" jdbcType="VARCHAR" />
    <result column="cpn_nm" property="cpnNm" jdbcType="VARCHAR" />
    <result column="parm1" property="parm1" jdbcType="VARCHAR" />
    <result column="parm2" property="parm2" jdbcType="VARCHAR" />
    <result column="parm3" property="parm3" jdbcType="VARCHAR" />
    <result column="parm4" property="parm4" jdbcType="VARCHAR" />
    <result column="parm5" property="parm5" jdbcType="VARCHAR" />
    <result column="parm6" property="parm6" jdbcType="VARCHAR" />
    <result column="parm7" property="parm7" jdbcType="VARCHAR" />
    <result column="parm8" property="parm8" jdbcType="VARCHAR" />
    <result column="upd_opr_id" property="updOprId" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    parm_id, risk_desc, cpn_nm, parm1, parm2, parm3, parm4, parm5, parm6, parm7, parm8,
    upd_opr_id, create_time, modify_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from rsm_risk_parm
    where parm_id = #{parmId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from rsm_risk_parm
    where parm_id = #{parmId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hisun.lemon.rsm.entity.RsmRiskParmDo" >
    <selectKey resultType="java.lang.String" keyProperty="parmId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rsm_risk_parm (parm_id, risk_desc, cpn_nm, parm1,
      parm2, parm3, parm4, 
      parm5, parm6, parm7, 
      parm8, upd_opr_id, create_time, modify_time)
    values (#{parmId,jdbcType=VARCHAR}, #{riskDesc,jdbcType=VARCHAR}, #{cpnNm,jdbcType=VARCHAR}, #{parm1,jdbcType=VARCHAR},
      #{parm2,jdbcType=VARCHAR}, #{parm3,jdbcType=VARCHAR}, #{parm4,jdbcType=VARCHAR}, #{parm5,jdbcType=VARCHAR},
      #{parm6,jdbcType=VARCHAR}, #{parm7,jdbcType=VARCHAR}, #{parm8,jdbcType=VARCHAR}, #{updOprId,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.hisun.lemon.rsm.entity.RsmRiskParmDo" >
    <selectKey resultType="java.lang.String" keyProperty="parmId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rsm_risk_parm
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="riskDesc != null" >
        risk_desc,
      </if>
      <if test="cpnNm != null" >
        cpn_nm,
      </if>
      <if test="parm1 != null" >
        parm1,
      </if>
      <if test="parm2 != null" >
        parm2,
      </if>
      <if test="parm3 != null" >
        parm3,
      </if>
      <if test="parm4 != null" >
        parm4,
      </if>
      <if test="parm5 != null" >
        parm5,
      </if>
      <if test="parm6 != null" >
        parm6,
      </if>
      <if test="parm7 != null" >
        parm7,
      </if>
      <if test="parm8 != null" >
        parm8,
      </if>
      <if test="updOprId != null" >
        upd_opr_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="modifyTime != null" >
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="riskDesc != null" >
        #{riskDesc,jdbcType=VARCHAR},
      </if>
      <if test="cpnNm != null" >
        #{cpnNm,jdbcType=VARCHAR},
      </if>
      <if test="parm1 != null" >
        #{parm1,jdbcType=VARCHAR},
      </if>
      <if test="parm2 != null" >
        #{parm2,jdbcType=VARCHAR},
      </if>
      <if test="parm3 != null" >
        #{parm3,jdbcType=VARCHAR},
      </if>
      <if test="parm4 != null" >
        #{parm4,jdbcType=VARCHAR},
      </if>
      <if test="parm5 != null" >
        #{parm5,jdbcType=VARCHAR},
      </if>
      <if test="parm6 != null" >
        #{parm6,jdbcType=VARCHAR},
      </if>
      <if test="parm7 != null" >
        #{parm7,jdbcType=VARCHAR},
      </if>
      <if test="parm8 != null" >
        #{parm8,jdbcType=VARCHAR},
      </if>
      <if test="updOprId != null" >
        #{updOprId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hisun.lemon.rsm.entity.RsmRiskParmDo" >
    update rsm_risk_parm
    <set >
      <if test="riskDes != null" >
        riskDesc = #{riskDesc,jdbcType=VARCHAR},
      </if>
      <if test="cpnNm != null" >
        cpn_nm = #{cpnNm,jdbcType=VARCHAR},
      </if>
      <if test="parm1 != null" >
        parm1 = #{parm1,jdbcType=VARCHAR},
      </if>
      <if test="parm2 != null" >
        parm2 = #{parm2,jdbcType=VARCHAR},
      </if>
      <if test="parm3 != null" >
        parm3 = #{parm3,jdbcType=VARCHAR},
      </if>
      <if test="parm4 != null" >
        parm4 = #{parm4,jdbcType=VARCHAR},
      </if>
      <if test="parm5 != null" >
        parm5 = #{parm5,jdbcType=VARCHAR},
      </if>
      <if test="parm6 != null" >
        parm6 = #{parm6,jdbcType=VARCHAR},
      </if>
      <if test="parm7 != null" >
        parm7 = #{parm7,jdbcType=VARCHAR},
      </if>
      <if test="parm8 != null" >
        parm8 = #{parm8,jdbcType=VARCHAR},
      </if>
      <if test="updOprId != null" >
        upd_opr_id = #{updOprId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null" >
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where parm_id = #{parmId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hisun.lemon.rsm.entity.RsmRiskParmDo" >
    update rsm_risk_parm
    set risk_desc = #{riskDesc,jdbcType=VARCHAR},
      cpn_nm = #{cpnNm,jdbcType=VARCHAR},
      parm1 = #{parm1,jdbcType=VARCHAR},
      parm2 = #{parm2,jdbcType=VARCHAR},
      parm3 = #{parm3,jdbcType=VARCHAR},
      parm4 = #{parm4,jdbcType=VARCHAR},
      parm5 = #{parm5,jdbcType=VARCHAR},
      parm6 = #{parm6,jdbcType=VARCHAR},
      parm7 = #{parm7,jdbcType=VARCHAR},
      parm8 = #{parm8,jdbcType=VARCHAR},
      upd_opr_id = #{updOprId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where parm_id = #{parmId,jdbcType=VARCHAR}
  </update>

  <select id="selectAllRuleParams" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List"/>
    from rsm_risk_parm
  </select>
</mapper>