<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.rsm.dao.RsmHighRiskUserMapper">

    <select id="queryAll" resultType="com.hisun.lemon.rsm.entity.RsmHighRiskUserDo">
        SELECT
            user_id,
            risk_parm,
            tx_ord_no,
            create_time,
            modify_time
        FROM rsm_high_risk_user
        ORDER BY create_time DESC
    </select>

    <insert id="addHighRiskList" parameterType="com.hisun.lemon.rsm.entity.RsmHighRiskUserDo">
        INSERT INTO rsm_high_risk_user (
            user_id, risk_parm, tx_ord_no, create_time, modify_time, tx_typ, risk_opr, risk_desc, opr_sts, risk_sorc)
        VALUES (#{userId,jdbcType=VARCHAR}, #{riskParm,jdbcType=VARCHAR}, #{txOrdNo,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{txTyp,jdbcType=VARCHAR},
                #{riskOpr,jdbcType=CHAR}, #{riskDesc,jdbcType=VARCHAR}, #{oprSts,jdbcType=CHAR},
                #{riskSorc,jdbcType=VARCHAR})
    </insert>
</mapper>