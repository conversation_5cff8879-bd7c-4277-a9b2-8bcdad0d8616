<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.rsm.dao.TxRuleParamMapper">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.rsm.entity.TxRuleParamDo">
        <id column="parm_id" property="parmId" jdbcType="VARCHAR"/>
        <result column="ac_typ" property="acTyp" jdbcType="CHAR"/>
        <result column="lmt_lvl" property="lmtLvl" jdbcType="CHAR"/>
        <result column="dc_pty_flg" property="dcPtyFlg" jdbcType="CHAR"/>
        <result column="tx_typ" property="txTyp" jdbcType="VARCHAR"/>
        <result column="pay_typ" property="payTyp" jdbcType="CHAR"/>
        <result column="min_amt_lmt" property="minAmtLmt" jdbcType="DECIMAL"/>
        <result column="max_amt_lmt" property="maxAmtLmt" jdbcType="DECIMAL"/>
        <result column="dly_amt_lmt" property="dlyAmtLmt" jdbcType="DECIMAL"/>
        <result column="dly_cnt_lmt" property="dlyCntLmt" jdbcType="INTEGER"/>
        <result column="mly_amt_lmt" property="mlyAmtLmt" jdbcType="DECIMAL"/>
        <result column="mly_cnt_lmt" property="mlyCntLmt" jdbcType="INTEGER"/>
        <result column="upd_opr_id" property="updOprId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        parm_id, ac_typ, lmt_lvl, dc_pty_flg, tx_typ, pay_typ, min_amt_lmt, max_amt_lmt, dly_amt_lmt, dly_cnt_lmt,
        mly_amt_lmt, mly_cnt_lmt, upd_opr_id, create_time, modify_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from rsm_rule_parm
        where PARM_ID = #{parmId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from rsm_rule_parm
        where PARM_ID = #{parmId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.hisun.lemon.rsm.entity.TxRuleParamDo">
        insert into rsm_rule_parm (parm_id, ac_typ, lmt_lvl, dc_pty_flg, tx_typ, pay_typ, min_amt_lmt, max_amt_lmt,
        dly_amt_lmt, dly_cnt_lmt, mly_amt_lmt, mly_cnt_lmt, upd_opr_id, create_time, modify_time)
        values (#{parmId,jdbcType=VARCHAR}, #{acTyp,jdbcType=CHAR}, #{lmtLvl,jdbcType=CHAR}, #{dcPtyFlg,jdbcType=CHAR},
        #{txTyp,jdbcType=VARCHAR}, #{payTyp,jdbcType=CHAR}, #{minAmtLmt,jdbcType=DECIMAL}, #{maxAmtLmt,jdbcType=DECIMAL},
        #{dlyAmtLmt,jdbcType=DECIMAL}, #{dlyCntLmt,jdbcType=INTEGER}, #{mlyAmtLmt,jdbcType=DECIMAL},
        #{mlyCntLmt,jdbcType=INTEGER}, #{updOprId,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.hisun.lemon.rsm.entity.TxRuleParamDo">
        update rsm_rule_parm
        <set>
            <if test="acTyp != null">
                ac_typ = #{acTyp,jdbcType=CHAR},
            </if>
            <if test="lmtLvl != null">
                lmt_lvl = #{lmtLvl,jdbcType=CHAR},
            </if>
            <if test="dcPtyflg != null">
                dc_pty_flg = #{dcPtyFlg,jdbcType=CHAR},
            </if>
            <if test="txTyp != null">
                tx_typ = #{txTyp,jdbcType=VARCHAR},
            </if>
            <if test="payTyp != null">
                pay_typ = #{payTyp,jdbcType=CHAR},
            </if>
            <if test="minAmtLmt != null">
                MIN_AMT_LMT = #{minAmtLmt,jdbcType=DECIMAL},
            </if>
            <if test="maxAmtLmt != null">
                MAX_AMT_LMT = #{maxAmtLmt,jdbcType=DECIMAL},
            </if>
            <if test="dlyAmtLmt != null">
                DLY_AMT_LMT = #{dlyAmtLmt,jdbcType=DECIMAL},
            </if>
            <if test="dlyCntLmt != null">
                DLY_CNT_LMT = #{dlyCntLmt,jdbcType=INTEGER},
            </if>
            <if test="mlyAmtLmt != null">
                MLY_AMT_LMT = #{mlyAmtLmt,jdbcType=DECIMAL},
            </if>
            <if test="mlyCntLmt != null">
                MLY_CNT_LMT = #{mlyCntLmt,jdbcType=INTEGER},
            </if>
            <if test="updOprId != null">
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where PARM_ID = #{parmId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.hisun.lemon.rsm.entity.TxRuleParamDo">
        update rsm_rule_parm
        set ac_typ = #{acTyp,jdbcType=CHAR},
        lmt_lvl = #{lmtLvl,jdbcType=CHAR},
        tx_typ = #{txTyp,jdbcType=VARCHAR},
        pay_typ = #{payTyp,jdbcType=CHAR},
        dc_pty_flg = #{dcPtyFlg,jdbcType=CHAR},
        min_amt_lmt = #{minAmtLmt,jdbcType=DECIMAL},
        max_amt_lmt = #{maxAmtLmt,jdbcType=DECIMAL},
        dly_amt_lmt = #{dlyAmtLmt,jdbcType=DECIMAL},
        dly_cnt_lmt = #{dlyCntLmt,jdbcType=INTEGER},
        mly_amt_lmt = #{mlyAmtLmt,jdbcType=DECIMAL},
        mly_cnt_lmt = #{mlyCntLmt,jdbcType=INTEGER},
        upd_opr_id = #{updOprId,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where PARM_ID = #{parmId,jdbcType=VARCHAR}
    </update>

    <select id="selectAllRuleParams" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from rsm_rule_parm
    </select>

    <select id="selectByExactMatch" resultMap="BaseResultMap" parameterType="java.lang.String">
        select <include refid="Base_Column_List"/> from rsm_rule_parm where dc_pty_flg = #{dcPtyFlg,jdbcType=CHAR}
        and tx_typ = #{txTyp,jdbcType=VARCHAR} and pay_typ = #{payTyp,jdbcType=CHAR} and lmt_lvl = #{mercLvl,jdbcType=CHAR}
    </select>
</mapper>