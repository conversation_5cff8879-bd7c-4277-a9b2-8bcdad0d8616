<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.rsm.dao.RiskWhiteListMapper">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.rsm.entity.RiskListDo">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="WHT_LIST_ID" jdbcType="VARCHAR" property="listId"/>
        <result column="ID_TYP" jdbcType="CHAR" property="idTyp"/>
        <result column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="ID_HID" jdbcType="VARCHAR" property="idHid"/>
        <result column="CRD_NO_LAST" jdbcType="CHAR" property="crdNoLast"/>
        <result column="TX_TYP" jdbcType="VARCHAR" property="txTyp"/>
        <result column="BEGIN_DT" jdbcType="TIMESTAMP" property="beginDt"/>
        <result column="END_DT" jdbcType="TIMESTAMP" property="endDt"/>
        <result column="EFF_FLG" jdbcType="CHAR" property="effFlg"/>
        <result column="WHT_LIST_SORC" jdbcType="VARCHAR" property="listSorc"/>
        <result column="WHT_LIST_RSN" jdbcType="VARCHAR" property="listRsn"/>
        <result column="UPD_OPR_ID" jdbcType="VARCHAR" property="updOprId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        wht_list_id, id_typ, id, id_hid, crd_no_last, tx_typ, begin_dt, end_dt, eff_flg,
        wht_list_sorc, wht_list_rsn, upd_opr_id, create_time, modify_Time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from RSM_WHT_LIST
        where WHT_LIST_ID = #{listId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM RSM_WHT_LIST
        WHERE WHT_LIST_ID = #{listId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.hisun.lemon.rsm.entity.RiskListDo">
        <selectKey keyProperty="listId" order="AFTER" resultType="java.lang.String">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into rsm_wht_list (wht_list_id, id_typ, id, id_hid,
        crd_no_last, tx_typ, begin_dt,
        end_dt, eff_flg, wht_list_sorc,
        wht_list_rsn, upd_opr_id, create_time, modify_Time)
        values (#{listId,jdbcType=VARCHAR}, #{idTyp,jdbcType=CHAR}, #{id,jdbcType=VARCHAR}, #{idHid,jdbcType=VARCHAR},
        #{crdNoLast,jdbcType=CHAR}, #{txTyp,jdbcType=VARCHAR}, #{beginDt,jdbcType=TIMESTAMP},
        #{endDt,jdbcType=TIMESTAMP}, #{effFlg,jdbcType=CHAR}, #{listSorc,jdbcType=VARCHAR},
        #{listRsn,jdbcType=VARCHAR}, #{updOprId,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.hisun.lemon.rsm.entity.RiskListDo">
        update RSM_WHT_LIST
        <set>
            <if test="idTyp != null">
                ID_TYP = #{idTyp,jdbcType=CHAR},
            </if>
            <if test="id != null">
                ID = #{id,jdbcType=VARCHAR},
            </if>
            <if test="idHid != null">
                ID_HID = #{idHid,jdbcType=VARCHAR},
            </if>
            <if test="crdNoLast != null">
                CRD_NO_LAST = #{crdNoLast,jdbcType=CHAR},
            </if>
            <if test="txTyp != null">
                TX_TYP = #{txTyp,jdbcType=VARCHAR},
            </if>
            <if test="beginDt != null">
                BEGIN_DT = #{beginDt,jdbcType=TIMESTAMP},
            </if>
            <if test="endDt != null">
                END_DT = #{endDt,jdbcType=TIMESTAMP},
            </if>
            <if test="effFlg != null">
                EFF_FLG = #{effFlg,jdbcType=CHAR},
            </if>
            <if test="listSorc != null">
                WHT_LIST_SORC = #{listSorc,jdbcType=VARCHAR},
            </if>
            <if test="listRsn != null">
                WHT_LIST_RSN = #{listRsn,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null">
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="updDt != null">
                UPD_DT = #{updDt,jdbcType=DATE},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where WHT_LIST_ID = #{listId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.hisun.lemon.rsm.entity.RiskListDo">
        UPDATE RSM_WHT_LIST
        SET ID_TYP = #{idTyp,jdbcType=CHAR},
        ID = #{id,jdbcType=VARCHAR},
        ID_HID = #{idHid,jdbcType=VARCHAR},
        CRD_NO_LAST = #{crdNoLast,jdbcType=CHAR},
        TX_TYP = #{txTyp,jdbcType=VARCHAR},
        BEGIN_DT = #{beginDt,jdbcType=TIMESTAMP},
        END_DT = #{endDt,jdbcType=TIMESTAMP},
        EFF_FLG = #{effFlg,jdbcType=CHAR},
        WHT_LIST_SORC = #{listSorc,jdbcType=VARCHAR},
        WHT_LIST_RSN = #{listRsn,jdbcType=VARCHAR},
        UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE WHT_LIST_ID = #{listId,jdbcType=VARCHAR}
    </update>

    <select id="selectByIdAndTypAndTxTyp" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rsm_wht_list
        where ID = #{id,jdbcType=VARCHAR} and ID_TYP = #{idTyp,jdbcType=CHAR}
        and tx_typ in ('00', #{txTyp,jdbcType=VARCHAR}) and eff_flg = '0'
        and now() &lt; end_dt and now() &gt; begin_dt
    </select>

    <select id="selectAllLists" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rsm_wht_list
    </select>
</mapper>