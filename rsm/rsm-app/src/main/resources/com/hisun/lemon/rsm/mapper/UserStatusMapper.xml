<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.rsm.dao.UserStatusMapper">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.rsm.entity.UserStatusDo">
        <id column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="user_sts" property="userSts" jdbcType="CHAR" />
        <result column="rmk" property="rmk" jdbcType="VARCHAR" />
        <result column="upd_opr_id" property="updOprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id, user_sts, rmk, upd_opr_id, create_time, modify_time
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from rsm_user_status
        where user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="selectAllUserStatusRec" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from rsm_user_status
        <if test="userSts != null">
            user_sts = #{userSts,jdbcType=VARCHAR},
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from rsm_user_status
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insertUserStatusRec" parameterType="com.hisun.lemon.rsm.entity.UserStatusDo">
        insert into rsm_user_status (user_id, user_sts, rmk, upd_opr_id, create_time, modify_time)
        values (#{userId,jdbcType=VARCHAR}, #{userSts,jdbcType=CHAR}, #{rmk,jdbcType=VARCHAR},
                #{updOprId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hisun.lemon.rsm.entity.UserStatusDo">
        update rsm_user_status
        <set>
            <if test="userSts != null">
                user_sts = #{userSts,jdbcType=CHAR},
            </if>
            <if test="rmk != null">
                rmk = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null">
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>
</mapper>