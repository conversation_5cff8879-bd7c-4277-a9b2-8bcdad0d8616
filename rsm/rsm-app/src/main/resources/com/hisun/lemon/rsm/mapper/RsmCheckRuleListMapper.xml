<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.rsm.dao.RsmCheckRuleListMapper">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.rsm.entity.RsmCheckRuleListDo">
        <id column="check_id" jdbcType="VARCHAR" property="checkId" />
        <result column="tx_typ" jdbcType="VARCHAR" property="txTyp" />
        <result column="lmt_lvl" jdbcType="CHAR" property="lmtLvl" />
        <result column="rule_desc" jdbcType="VARCHAR" property="ruleDesc" />
        <result column="opr_typ" jdbcType="CHAR" property="oprTyp" />
        <result column="upd_opr_id" jdbcType="VARCHAR" property="updOprId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    </resultMap>

    <select id="queryAllCheckRuleLists" resultMap="BaseResultMap">
        SELECT check_id, tx_typ, lmt_lvl, rule_desc, opr_typ, upd_opr_id, create_time, modify_time
        FROM rsm_check_rule_list
    </select>

    <select id="queryCheckRuleList" resultMap="BaseResultMap">
        SELECT check_id, tx_typ, lmt_lvl, rule_desc, opr_typ, upd_opr_id, create_time, modify_time
        FROM rsm_check_rule_list WHERE tx_typ = #{txTyp,jdbcType=VARCHAR}
    </select>

    <select id="queryCheckRuleByCheckId" resultMap="BaseResultMap">
        SELECT check_id, tx_typ, lmt_lvl, rule_desc, opr_typ, upd_opr_id, create_time, modify_time
        FROM rsm_check_rule_list WHERE check_id = #{checkId,jdbcType=VARCHAR}
    </select>

    <insert id="addCheckRuleList" parameterType="com.hisun.lemon.rsm.entity.RsmCheckRuleListDo">
        INSERT INTO rsm_check_rule_list
        (check_id, tx_typ, lmt_lvl, rule_desc, opr_typ, upd_opr_id, create_time, modify_time)
        VALUES (#{checkId,jdbcType=VARCHAR}, #{txTyp,jdbcType=VARCHAR}, #{lmtLvl,jdbcType=CHAR},
                #{ruleDesc,jdbcType=VARCHAR}, #{oprTyp,jdbcType=CHAR}, #{updOprId,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="updateCheckRuleList" parameterType="com.hisun.lemon.rsm.entity.RsmCheckRuleListDo">
        UPDATE rsm_check_rule_list
        <set>
            <if test="checkId != null">
                check_id = #{checkId,jdbcType=VARCHAR},
            </if>
            <if test="txTyp != null">
                tx_typ = #{txTyp,jdbcType=VARCHAR},
            </if>
            <if test="lmtLvl != null">
                lmt_lvl = #{lmtLvl,jdbcType=CHAR},
            </if>
            <if test="ruleDesc != null">
                rule_desc = #{ruleDesc,jdbcType=VARCHAR},
            </if>
            <if test="oprTyp != null">
                opr_typ = #{oprTyp,jdbcType=VARCHAR},
            </if>
            <if test="oprTyp != null">
                opr_typ = #{oprTyp,jdbcType=VARCHAR},
            </if>
            <if test="oprTyp != null">
                upd_opr_id = #{oprTyp,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE check_id = #{checkId,jdbcType=VARCHAR}
    </update>

    <delete id="deleteCheckRuleList" parameterType="java.lang.String">
        DELETE rsm_check_rule_list FROM rsm_check_rule_list WHERE
            check_id = #{checkId,jdbcType=VARCHAR}
    </delete>

</mapper>