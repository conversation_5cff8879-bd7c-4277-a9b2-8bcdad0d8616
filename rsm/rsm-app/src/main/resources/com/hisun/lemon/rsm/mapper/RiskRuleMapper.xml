<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.rsm.dao.RiskRuleMapper">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.rsm.entity.RiskRuleDo">
        <id column="RULE_ID" property="ruleId" jdbcType="VARCHAR"/>
        <result column="RULE_NM" property="ruleNm" jdbcType="VARCHAR"/>
        <result column="RULE_DESC" property="ruleDesc" jdbcType="VARCHAR"/>
        <result column="RULE_TYP" property="ruleTyp" jdbcType="CHAR"/>
        <result column="DC_PTY_FLG" property="dcPtyFlg" jdbcType="CHAR"/>
        <result column="CPN_NM" property="cpnNm" jdbcType="VARCHAR"/>
        <result column="UPD_OPR_ID" property="updOprId" jdbcType="VARCHAR"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        rule_id, rule_nm, rule_desc, rule_typ, dc_pty_flg, cpn_nm, upd_opr_id, create_time, modify_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from rsm_rule_info
        where RULE_ID = #{ruleId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from rsm_rule_info
        where RULE_ID = #{ruleId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.hisun.lemon.rsm.entity.RiskRuleDo">
        <selectKey resultType="java.lang.String" keyProperty="ruleId" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into rsm_rule_info (rule_id, rule_nm, rule_desc, rule_typ,
        dc_pty_flg, cpn_nm, upd_opr_id, create_time, modify_time)
        values (#{ruleId,jdbcType=VARCHAR}, #{ruleNm,jdbcType=VARCHAR}, #{ruleDesc,jdbcType=VARCHAR}, #{ruleTyp,jdbcType=CHAR},
        #{dcPtyFlg,jdbcType=CHAR}, #{cpnNm,jdbcType=VARCHAR}, #{updOprId,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.hisun.lemon.rsm.entity.RiskRuleDo">
        update rsm_rule_info
        <set>
            <if test="ruleNm != null">
                RULE_NM = #{ruleNm,jdbcType=VARCHAR},
            </if>
            <if test="ruleDesc != null">
                RULE_DESC = #{ruleDesc,jdbcType=VARCHAR},
            </if>
            <if test="ruleTyp != null">
                RULE_TYP = #{ruleTyp,jdbcType=CHAR},
            </if>
            <if test="dcPtyFlg != null">
                DC_PTY_FLG = #{dcPtyFlg,jdbcType=CHAR},
            </if>
            <if test="cpnNm != null">
                CPN_NM = #{cpnNm,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null">
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where RULE_ID = #{ruleId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.hisun.lemon.rsm.entity.RiskRuleDo">
        update rsm_rule_info
        set RULE_NM = #{ruleNm,jdbcType=VARCHAR},
        RULE_DESC = #{ruleDesc,jdbcType=VARCHAR},
        RULE_TYP = #{ruleTyp,jdbcType=CHAR},
        DC_PTY_FLG = #{dcPtyFlg,jdbcType=CHAR},
        CPN_NM = #{cpnNm,jdbcType=VARCHAR},
        UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where RULE_ID = #{ruleId,jdbcType=VARCHAR}
    </update>

    <select id="selectAllRules" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from rsm_rule_info
    </select>

    <select id="selectByExactMatch" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from rsm_rule_info
        where
        <if test="ruleTyp != null">
            RULE_TYP = #{ruleTyp,jdbcType=CHAR} and
        </if>
        1 = 1
    </select>
</mapper>