<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.rsm.dao.RsmJrnMapper">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.rsm.entity.RsmJrnDo">
        <id column="JRN_NO" property="jrnNo" jdbcType="VARCHAR"/>
        <result column="STL_USER_ID" property="stlUserId" jdbcType="VARCHAR"/>
        <result column="STL_USER_TYP" property="stlUserTyp" jdbcType="CHAR"/>
        <result column="PAY_USER_ID" property="payUserId" jdbcType="VARCHAR"/>
        <result column="PAY_USER_TYP" property="payUserTyp" jdbcType="CHAR"/>
        <result column="TX_TYP" property="txTyp" jdbcType="VARCHAR"/>
        <result column="TX_STS" property="txSts" jdbcType="CHAR"/>
        <result column="TX_CNL" property="txCnl" jdbcType="VARCHAR"/>
        <result column="TX_AMT" property="txAmt" jdbcType="DECIMAL"/>
        <result column="CCY" property="ccy" jdbcType="CHAR"/>
        <result column="pay_typ" property="pay_typ" jdbcType="CHAR"/>
        <result column="TX_DT" property="txDt" jdbcType="DATE"/>
        <result column="TX_TM" property="txTm" jdbcType="TIME"/>
        <result column="TX_JRN_NO" property="txJrnNo" jdbcType="VARCHAR"/>
        <result column="TX_ORD_NO" property="txOrdNo" jdbcType="VARCHAR"/>
        <result column="TX_DATA" property="txData" jdbcType="VARCHAR"/>
        <result column="RULE_ID" property="ruleId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        jrn_no, stl_user_id, stl_user_typ, pay_user_id, pay_user_typ, tx_typ, tx_sts, tx_cnl,
        tx_amt, ccy, tx_dt, tx_tm, tx_jrn_no, tx_ord_no, tx_data, rule_id, create_time, modify_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from rsm_tx_jrn
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM rsm_tx_jrn
        WHERE JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.hisun.lemon.rsm.entity.RsmJrnDo">
        <selectKey resultType="java.lang.String" keyProperty="jrnNo" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into rsm_tx_jrn (jrn_no, stl_user_id, stl_user_typ, pay_user_id,
        pay_user_typ, tx_typ, tx_sts,
        tx_cnl, tx_amt, ccy, pay_typ, tx_dt,
        tx_tm, tx_jrn_no, tx_ord_no,
        tx_data, rule_id, create_time, modify_time)
        values (#{jrnNo,jdbcType=VARCHAR}, #{stlUserId,jdbcType=VARCHAR}, #{stlUserTyp,jdbcType=CHAR},
        #{payUserId,jdbcType=VARCHAR}, #{payUserTyp,jdbcType=CHAR}, #{txTyp,jdbcType=VARCHAR}, #{txSts,jdbcType=CHAR},
        #{txCnl,jdbcType=VARCHAR}, #{txAmt,jdbcType=DECIMAL}, #{ccy,jdbcType=CHAR}, #{payTyp,jdbcType=CHAR},
        #{txDt,jdbcType=DATE}, #{txTm,jdbcType=TIME}, #{txJrnNo,jdbcType=VARCHAR}, #{txOrdNo,jdbcType=VARCHAR},
        #{txData,jdbcType=VARCHAR}, #{ruleId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.hisun.lemon.rsm.entity.RsmJrnDo">
        update rsm_tx_jrn
        <set>
            <if test="stlUserId != null">
                STL_USER_ID = #{stlUserId,jdbcType=VARCHAR},
            </if>
            <if test="stlUserTyp != null">
                STL_USER_TYP = #{stlUserTyp,jdbcType=CHAR},
            </if>
            <if test="payUserId != null">
                PAY_USER_ID = #{payUserId,jdbcType=VARCHAR},
            </if>
            <if test="payUserTyp != null">
                PAY_USER_TYP = #{payUserTyp,jdbcType=CHAR},
            </if>
            <if test="txTyp != null">
                TX_TYP = #{txTyp,jdbcType=VARCHAR},
            </if>
            <if test="txSts != null">
                TX_STS = #{txSts,jdbcType=CHAR},
            </if>
            <if test="txCnl != null">
                TX_CNL = #{txCnl,jdbcType=VARCHAR},
            </if>
            <if test="txAmt != null">
                TX_AMT = #{txAmt,jdbcType=DECIMAL},
            </if>
            <if test="ccy != null">
                CCY = #{ccy,jdbcType=CHAR},
            </if>
            <if test="payTyp != null">
                pay_typ = #{payTyp,jdbcType=CHAR},
            </if>
            <if test="txDt != null">
                TX_DT = #{txDt,jdbcType=DATE},
            </if>
            <if test="txTm != null">
                TX_TM = #{txTm,jdbcType=TIME},
            </if>
            <if test="txJrnNo != null">
                TX_JRN_NO = #{txJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="txOrdNo != null">
                TX_ORD_NO = #{txOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="txData != null">
                TX_DATA = #{txData,jdbcType=VARCHAR},
            </if>
            <if test="ruleId != null">
                RULE_ID = #{ruleId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.hisun.lemon.rsm.entity.RsmJrnDo">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        UPDATE rsm_tx_jrn
        SET STL_USER_ID = #{stlUserId,jdbcType=VARCHAR},
        STL_USER_TYP = #{stlUserTyp,jdbcType=CHAR},
        PAY_USER_ID = #{payUserId,jdbcType=VARCHAR},
        PAY_USER_TYP = #{payUserTyp,jdbcType=CHAR},
        TX_TYP = #{txTyp,jdbcType=VARCHAR},
        TX_STS = #{txSts,jdbcType=CHAR},
        TX_CNL = #{txCnl,jdbcType=VARCHAR},
        TX_AMT = #{txAmt,jdbcType=DECIMAL},
        CCY = #{ccy,jdbcType=CHAR},
        pay_typ = #{payTyp,jdbcType=CHAR},
        TX_DT = #{txDt,jdbcType=DATE},
        TX_TM = #{txTm,jdbcType=TIME},
        TX_JRN_NO = #{txJrnNo,jdbcType=VARCHAR},
        TX_ORD_NO = #{txOrdNo,jdbcType=VARCHAR},
        TX_DATA = #{txData,jdbcType=VARCHAR},
        RULE_ID = #{ruleId,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </update>
</mapper>