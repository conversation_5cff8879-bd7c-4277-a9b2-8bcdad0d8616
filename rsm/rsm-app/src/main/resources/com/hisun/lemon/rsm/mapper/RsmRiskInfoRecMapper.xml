<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.rsm.dao.RsmRiskInfoRecMapper">
  <resultMap id="BaseResultMap" type="com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="rec_id" jdbcType="VARCHAR" property="recId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_typ" jdbcType="CHAR" property="userTyp" />
    <result column="tx_User_id" jdbcType="VARCHAR" property="txUserId" />
    <result column="tx_User_typ" jdbcType="VARCHAR" property="txUserTyp" />
    <result column="cap_flw" jdbcType="CHAR" property="capFlw" />
    <result column="tx_typ" jdbcType="VARCHAR" property="txTyp" />
    <result column="tx_cnl" jdbcType="VARCHAR" property="txCnl" />
    <result column="tx_sts" jdbcType="CHAR" property="txSts" />
    <result column="tx_dt" jdbcType="DATE" property="txDt" />
    <result column="tx_tm" jdbcType="TIME" property="txTm" />
    <result column="tx_amt" jdbcType="DECIMAL" property="txAmt" />
    <result column="ccy" jdbcType="CHAR" property="ccy" />
    <result column="crd_typ" jdbcType="CHAR" property="crdTyp" />
    <result column="ip_addr" jdbcType="VARCHAR" property="ipAddr" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    rec_id, user_id, user_typ, tx_User_id, tx_User_typ, cap_flw, tx_typ, tx_cnl, tx_sts, 
    tx_dt, tx_tm, tx_amt, ccy, crd_typ, ip_addr
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from rsm_risk_info_rec
    where rec_id = #{recId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from rsm_risk_info_rec
    where rec_id = #{recId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="recId" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rsm_risk_info_rec (rec_id, user_id, user_typ, tx_User_id,
      tx_User_typ, cap_flw, tx_typ, 
      tx_cnl, tx_sts, tx_dt, tx_tm, 
      tx_amt, ccy, crd_typ, ip_addr)
    values (#{recId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{userTyp,jdbcType=CHAR}, #{txUserId,jdbcType=VARCHAR},
      #{txUserTyp,jdbcType=VARCHAR}, #{capFlw,jdbcType=CHAR}, #{txTyp,jdbcType=VARCHAR}, 
      #{txCnl,jdbcType=VARCHAR}, #{txSts,jdbcType=CHAR}, #{txDt,jdbcType=DATE}, #{txTm,jdbcType=TIME}, 
      #{txAmt,jdbcType=DECIMAL}, #{ccy,jdbcType=CHAR}, #{crdTyp,jdbcType=CHAR}, #{ipAddr,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="recId" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rsm_risk_info_rec
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="userTyp != null">
        user_typ,
      </if>
      <if test="txUserId != null">
        tx_User_id,
      </if>
      <if test="txUserTyp != null">
        tx_User_typ,
      </if>
      <if test="capFlw != null">
        cap_flw,
      </if>
      <if test="txTyp != null">
        tx_typ,
      </if>
      <if test="txCnl != null">
        tx_cnl,
      </if>
      <if test="txSts != null">
        tx_sts,
      </if>
      <if test="txDt != null">
        tx_dt,
      </if>
      <if test="txTm != null">
        tx_tm,
      </if>
      <if test="txAmt != null">
        tx_amt,
      </if>
      <if test="ccy != null">
        ccy,
      </if>
      <if test="crdTyp != null">
        crd_typ,
      </if>
      <if test="ipAddr != null">
        ip_addr,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userTyp != null">
        #{userTyp,jdbcType=CHAR},
      </if>
      <if test="txUserId != null">
        #{txUserId,jdbcType=VARCHAR},
      </if>
      <if test="txUserTyp != null">
        #{txUserTyp,jdbcType=VARCHAR},
      </if>
      <if test="capFlw != null">
        #{capFlw,jdbcType=CHAR},
      </if>
      <if test="txTyp != null">
        #{txTyp,jdbcType=VARCHAR},
      </if>
      <if test="txCnl != null">
        #{txCnl,jdbcType=VARCHAR},
      </if>
      <if test="txSts != null">
        #{txSts,jdbcType=CHAR},
      </if>
      <if test="txDt != null">
        #{txDt,jdbcType=DATE},
      </if>
      <if test="txTm != null">
        #{txTm,jdbcType=TIME},
      </if>
      <if test="txAmt != null">
        #{txAmt,jdbcType=DECIMAL},
      </if>
      <if test="ccy != null">
        #{ccy,jdbcType=CHAR},
      </if>
      <if test="crdTyp != null">
        #{crdTyp,jdbcType=CHAR},
      </if>
      <if test="ipAddr != null">
        #{ipAddr,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update rsm_risk_info_rec
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userTyp != null">
        user_typ = #{userTyp,jdbcType=CHAR},
      </if>
      <if test="txUserId != null">
        tx_User_id = #{txUserId,jdbcType=VARCHAR},
      </if>
      <if test="txUserTyp != null">
        tx_User_typ = #{txUserTyp,jdbcType=VARCHAR},
      </if>
      <if test="capFlw != null">
        cap_flw = #{capFlw,jdbcType=CHAR},
      </if>
      <if test="txTyp != null">
        tx_typ = #{txTyp,jdbcType=VARCHAR},
      </if>
      <if test="txCnl != null">
        tx_cnl = #{txCnl,jdbcType=VARCHAR},
      </if>
      <if test="txSts != null">
        tx_sts = #{txSts,jdbcType=CHAR},
      </if>
      <if test="txDt != null">
        tx_dt = #{txDt,jdbcType=DATE},
      </if>
      <if test="txTm != null">
        tx_tm = #{txTm,jdbcType=TIME},
      </if>
      <if test="txAmt != null">
        tx_amt = #{txAmt,jdbcType=DECIMAL},
      </if>
      <if test="ccy != null">
        ccy = #{ccy,jdbcType=CHAR},
      </if>
      <if test="crdTyp != null">
        crd_typ = #{crdTyp,jdbcType=CHAR},
      </if>
      <if test="ipAddr != null">
        ip_addr = #{ipAddr,jdbcType=VARCHAR},
      </if>
    </set>
    where rec_id = #{recId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hisun.lemon.rsm.entity.RsmRiskInfoRecDo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update rsm_risk_info_rec
    set user_id = #{userId,jdbcType=VARCHAR},
      user_typ = #{userTyp,jdbcType=CHAR},
      tx_User_id = #{txUserId,jdbcType=VARCHAR},
      tx_User_typ = #{txUserTyp,jdbcType=VARCHAR},
      cap_flw = #{capFlw,jdbcType=CHAR},
      tx_typ = #{txTyp,jdbcType=VARCHAR},
      tx_cnl = #{txCnl,jdbcType=VARCHAR},
      tx_sts = #{txSts,jdbcType=CHAR},
      tx_dt = #{txDt,jdbcType=DATE},
      tx_tm = #{txTm,jdbcType=TIME},
      tx_amt = #{txAmt,jdbcType=DECIMAL},
      ccy = #{ccy,jdbcType=CHAR},
      crd_typ = #{crdTyp,jdbcType=CHAR},
      ip_addr = #{ipAddr,jdbcType=VARCHAR}
    where rec_id = #{recId,jdbcType=VARCHAR}
  </update>
  <select id="selectRecsByTimeAndCapFlw" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rsm_risk_info_rec
    where user_id = #{userId,jdbcType=VARCHAR}
    <if test="capFlw != null">
    and cap_flw = #{capFlw,jdbcType=CHAR}
    </if>
    and tx_dt >= #{begDay,jdbcType=DATE}
  </select>
</mapper>