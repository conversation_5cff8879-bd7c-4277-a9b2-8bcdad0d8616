TODO
----
__2017.08.25__
- 修改风控检查规则，风控描述添加操作纪录，风控与记录以逗号分隔，规则以竖线分隔

__2017.08.16__
- 添加累计消息队列，用与获取异步进程执行结果，并根据结果判断是否重新调起

__2017.08.15__
- ~~增加批量检查，批量额度累计~~

__2017.08.10__
- ~~状态检查添加暂停冻结检查~~
- ~~风控流水记录调整~~

__2017.08.07__
- ~~额度统计所有支付方式都要统计到全部中~~
- ~~黑白名单检查按交易类型区分~~
- ~~免密支付额度，以及开关由公共参数控制~~
- ~~额度统计，在没有规则的情况下也要累计，更改累计缓存方法~~
- ~~添加对用户状态的检查，非正常状态直接阻断，由运营录入信息~~
- ~~额度统计转账，交易区分统计~~

__2017.08.02__
- 更改风控规则针对对象，不按照收付款限制，从用户级别限制（普通用户，企业用户，个体商户，企业商户）
- 用户交易权限管理
- ~~更改风控检查为需求2.0中要求~~
- ~~修改免密支付统计额度流程~~

__init__
- 敏感信息加密入库
- ~~7个风险规则~~
- ~~累计信息写入数据库~~
- ~~新增风控规则、风控参数刷新redis~~
- ~~框架缓存接口，更新模块内缓存相关操作~~
- ~~风险规则数据库设计~~
- 定时清理累计信息

用户状态检查接口说明
----
>实时风控检查已包括用户状态检查，此接口只提供给特殊情况判断用

id：id号
idTyp：id的类型，01-用户号/商户号，02-身份证，03-银行卡
txTyp：交易类型（签约等非交易填写 00）


关于风控累计特殊情况说明
----
- 转账时双控或控收款方，收款方的转账额度也会被增加


累计信息Key值
----
日累计金额（RSM_DLY_TOT_AMT + 日期）
日累计次数（RSM_DLY_TOT_CNT + 日期）
月累计金额（RSM_MLY_TOT_AMT + yyyyMM）
月累计次数（RSM_MLY_TOT_CNT + yyyyMM）

关于实时风控检测与额度累计接口说明
----

>传入参数RsmJrnReqDTO里txData字段需要所有交易数据（字符串格式键值对已逗号分隔，如：a=b,d=c）
>混合支付方式实时风控需分开按各自的支付方式校验
>风控检查规则装填时，风控规则以"|"作分隔

数据库自增key名
----
- 黑名单表（RSM_BLK_LIST）
- 白名单表（RSM_WHT_LIST）
- 风控规则表（RSM_RULE_INFO）
- 交易限额参数表（RSM_TX_PARAM）
- 风险规则参数表（RSM_TABLE_RISK_PARM）
- 风控流水表（RSM_TX_JRN）
- 风险信息记录表（RSM_RISK_INFO_REC）
- 风控检查规则（RSM_CHECK_RULE_ID）

7个风险规则
----
- 个人客户X天内通过充值转账等集中（小于X次）转入X万元以上，并通过提现转账等方式向X个以上~~不同归属地~~账户分散转出；
- 个人客户累计X天内单个账户向X个以上账户注出资金X万元以上；
- ~~商户客户X天内受理单笔超过X万元的大额信用卡交易超过X笔；~~
- ~~反洗钱黑名单中人员历史交易次数超过X次或金额超过X万；~~
- 商户账户X日内发生来自于同一IP地址，交易频繁（大于X次），交易金额超过XX万；
- 商户当月发生X笔以上单笔金额超过X万元的交易；
- 个人客户X日内频繁（大于X次）发生临近（正负百分之X）限额交易，且交易总额超过X万元；