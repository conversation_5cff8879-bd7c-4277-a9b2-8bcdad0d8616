package com.hisun.lemon.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@ApiModel("用户法币账户详情响应")
public class UserFmAccountDetailRspDTO {
    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    /**
     * 币种logo
     */
    @ApiModelProperty(name = "ccyLogo", value = "币种logo")
    private String ccyLogo;

    /**
     * 账户类型
     */
    @ApiModelProperty(name = "accountType", value = "账户类型")
    private String accountType;

    /**
     * 账户余额
     */
    @ApiModelProperty(name = "availableBalance", value = "账户余额")
    private BigDecimal accountBalance;

    /**
     * 账户总资产
     */
    @ApiModelProperty(name = "totalBalance", value = "账户总资产")
    private BigDecimal totalBalance;

    /**
     * 账户状态
     */
    @ApiModelProperty(name = "acSts", value = "账户状态（正常/冻结/停用）")
    private String acSts;

    /**
     * 账户名称
     */
    @ApiModelProperty(name = "accountName", value = "账户名称")
    private String accountName;

    /**
     * 账户号码
     */
    @ApiModelProperty(name = "acNo", value = "账户号码")
    private String acNo;

    /**
     * 开户行
     */
    @ApiModelProperty(name = "bank", value = "开户行")
    private String bank;

    /**
     * 绑定时间
     */
    @ApiModelProperty(name = "bindTime", value = "绑定时间")
    private LocalDateTime bindTime;

    /**
     * acCreDt 账户创建日期
     */
    @ApiModelProperty(name = "acCreDt", value = "账户创建日期")
    private LocalDate acCreDt;

    /**
     * acCreTm 账户创建时间
     */
    @ApiModelProperty(name = "acCreTm", value = "账户创建时间")
    private LocalTime acCreTm;

    /**
     * userId 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCcyLogo() {
        return ccyLogo;
    }

    public void setCcyLogo(String ccyLogo) {
        this.ccyLogo = ccyLogo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public BigDecimal getAccountBalance() {
        return accountBalance;
    }

    public void setAccountBalance(BigDecimal accountBalance) {
        this.accountBalance = accountBalance;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public String getAcSts() {
        return acSts;
    }

    public void setAcSts(String acSts) {
        this.acSts = acSts;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public LocalDateTime getBindTime() {
        return bindTime;
    }

    public void setBindTime(LocalDateTime bindTime) {
        this.bindTime = bindTime;
    }

    public LocalDate getAcCreDt() {
        return acCreDt;
    }

    public void setAcCreDt(LocalDate acCreDt) {
        this.acCreDt = acCreDt;
    }

    public LocalTime getAcCreTm() {
        return acCreTm;
    }

    public void setAcCreTm(LocalTime acCreTm) {
        this.acCreTm = acCreTm;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
