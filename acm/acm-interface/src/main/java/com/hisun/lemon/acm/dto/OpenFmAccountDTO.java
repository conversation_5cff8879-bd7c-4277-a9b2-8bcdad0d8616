package com.hisun.lemon.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;


@ApiModel("开立法币账户")
public class OpenFmAccountDTO {

    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    @ApiModelProperty(name = "bank", value = "开户行")
    private String bank;

    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    @ApiModelProperty(name = "accountNo", value = "银行卡号")
    @Length(max = 20)
    private String accountNo;

    @ApiModelProperty(name = "accountName", value = "账户名")
    private String accountName;

    @ApiModelProperty(name = "pwyPwd", value = "交易密码")
    private String payPwd;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }
}
