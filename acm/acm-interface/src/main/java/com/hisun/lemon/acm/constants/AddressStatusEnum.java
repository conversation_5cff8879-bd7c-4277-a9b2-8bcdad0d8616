package com.hisun.lemon.acm.constants;

/**
 * 地址状态枚举类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/12 16:20
 */
public enum AddressStatusEnum {
    ENABLED("ENABLED", "已启用"),
    DISABLED("DISABLED", "未启用"),
    FROZEN("FROZEN", "冻结");

    private final String code;
    private final String description;

    AddressStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }
}
