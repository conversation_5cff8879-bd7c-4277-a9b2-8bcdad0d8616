package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 开立数币账户请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/12 12:01
 */
@ApiModel(value = "OpenDmAccountDTO", description = "开立数币账户请求类")
@ClientValidated
public class OpenDmAccountDTO {

    /**
     * ccy 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种", required = true)
    @Length(max = 10)
    @NotBlank(message = "币种不能为空")
    private String ccy;

    /**
     * network 网络
     */
    @ApiModelProperty(name = "network", value = "网络", required = true)
    @Length(max = 100)
    @NotBlank(message = "网络不能为空")
    private String network;

    /**
     * payPwd 交易密码
     */
    @ApiModelProperty(name = "payPwd", value = "交易密码", required = true)
    @Length(max = 32)
    @NotBlank(message = "交易密码不能为空")
    private String payPwd;

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

}
