package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 数币账户地址信息响应类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 16:38
 */
@ApiModel(value = "DmAccountAddressRspDTO", description = "数币账户地址信息响应类")
@ClientValidated
public class DmAccountAddressRspDTO {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键")
    private Long id;

    /**
     * 所属金库编码（关联金库）
     */
    @ApiModelProperty(name = "vaultCode", value = "所属金库编码（关联金库）")
    private String vaultCode;

    /**
     * 所属账户组编码
     */
    @ApiModelProperty(name = "groupCode", value = "所属账户组编码")
    private String groupCode;

    /**
     * 所属账户ID（关联账户管理中的账户）
     */
    @ApiModelProperty(name = "accountId", value = "所属账户ID（关联账户管理中的账户）")
    private Long accountId;

    /**
     * 链上地址（如0xE41d306C7a58687C4Dc433e1FF21cc91d4D7077A）
     */
    @ApiModelProperty(name = "address", value = "链上地址（如0xE41d306C7a58687C4Dc433e1FF21cc91d4D7077A）")
    private String address;

    /**
     * 所属区块链网络
     */
    @ApiModelProperty(name = "network", value = "所属区块链网络")
    private String network;

    /**
     * 地址状态（ENABLED:已启用, DISABLED:未启用, FROZEN:冻结）
     */
    @ApiModelProperty(name = "status", value = "地址状态（ENABLED:已启用, DISABLED:未启用, FROZEN:冻结）",
            allowableValues = "ENABLED,DISABLED,FROZEN")
    private String status;

    /**
     * 对应的账号（关联acm_ac_inf表ac_no）
     */
    @ApiModelProperty(name = "acmAcNo", value = "对应的账号（关联acm_ac_inf表ac_no）")
    private String acmAcNo;

    /**
     * 归属客户ID（启用后登记）
     */
    @ApiModelProperty(name = "userId", value = "归属客户ID（启用后登记）")
    private String userId;

    /**
     * 用途类型（收款:DS /充值:DC）
     */
    @ApiModelProperty(name = "useType", value = "用途类型（收款:DS /充值:DC）",
            allowableValues = "DS,DC")
    private String useType;

    /**
     * 收款二维码Base64编码
     */
    @ApiModelProperty(name = "qrCodeBase64", value = "收款二维码Base64编码")
    private String qrCodeBase64;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间（精确到微秒）")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "modifyTime", value = "修改时间（精确到微秒）")
    private LocalDateTime modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAcmAcNo() {
        return acmAcNo;
    }

    public void setAcmAcNo(String acmAcNo) {
        this.acmAcNo = acmAcNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }

    public String getQrCodeBase64() {
        return qrCodeBase64;
    }

    public void setQrCodeBase64(String qrCodeBase64) {
        this.qrCodeBase64 = qrCodeBase64;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}