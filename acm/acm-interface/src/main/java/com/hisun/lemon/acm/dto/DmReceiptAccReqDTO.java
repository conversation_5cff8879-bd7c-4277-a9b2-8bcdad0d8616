package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 获取当前用户收款/充值数币账户列表请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19 15:44
 */
@ClientValidated
@ApiModel(value = "DmReceiptAccReqDTO", description = "获取当前用户收款/充值数币账户列表请求")
public class DmReceiptAccReqDTO {


    @ApiModelProperty(name = "useType", value = "使用类型,DS:收款,DC:充值", required = true)
    @NotBlank(message ="用途类型不能为空")
    private String useType;

    public String getUseType() {
        return useType;
    }
    public void setUseType(String useType) {
        this.useType = useType;
    }
}
