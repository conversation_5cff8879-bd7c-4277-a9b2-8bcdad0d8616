package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @function UserAccountDTO
 * @description 用户账户信息传输对象
 * @date 8/19/2017 Sat
 * @time 3:44 PM
 */
@ApiModel("用户账户信息")
@ClientValidated
public class UserAccountDTO extends GenericDTO<NoBody> {
    /**
     * 用户账号
     */
    @ApiModelProperty(name = "acNo", value = "用户账号")
    @Length(max = 15)
    private String acNo;

    /**
     * 用户号
     */
    @ApiModelProperty(name = "userId", value = "用户ID")
    @Length(max = 16)
    private String userId;

    /**
     * 资金类型
     */
    @ApiModelProperty(name = "capTyp", value = "资金类型1：现金，8：待结算")
    @Length(max = 1)
    private String capTyp;

    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;


    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }
}
