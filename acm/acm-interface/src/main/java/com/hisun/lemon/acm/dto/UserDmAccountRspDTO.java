package com.hisun.lemon.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 数币账户列表响应
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10 17:06
 */
@ApiModel(value = "UserDmAccountRspDTO", description = "用户数币账户响应")
public class UserDmAccountRspDTO {

    /**
     * 账号编码
     */
    @ApiModelProperty(name = "acNo", value = "账号编码")
    private String acNo;

    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    /**
     * 网络
     */
    @ApiModelProperty(name = "network", value = "网络")
    private String network;

    /**
     * 币种logo
     */
    @ApiModelProperty(name = "ccyLogo", value = "币种logo")
    private String ccyLogo;

    /**
     * 账户余额
     */
    @ApiModelProperty(name = "accountBalance", value = "账户余额")
    private BigDecimal accountBalance;

    /**
     * 账户总资产
     */
    @ApiModelProperty(name = "totalBalance", value = "账户总资产")
    private BigDecimal totalBalance;

    /**
     * acCreDt 账户创建日期
     */
    @ApiModelProperty(name = "acCreDt", value = "账户创建日期")
    private LocalDate acCreDt;

    /**
     * acCreTm 账户创建时间
     */
    @ApiModelProperty(name = "acCreTm", value = "账户创建时间")
    private LocalTime acCreTm;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getCcyLogo() {
        return ccyLogo;
    }

    public void setCcyLogo(String ccyLogo) {
        this.ccyLogo = ccyLogo;
    }

    public BigDecimal getAccountBalance() {
        return accountBalance;
    }

    public void setAccountBalance(BigDecimal accountBalance) {
        this.accountBalance = accountBalance;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public LocalDate getAcCreDt() {
        return acCreDt;
    }

    public void setAcCreDt(LocalDate acCreDt) {
        this.acCreDt = acCreDt;
    }

    public LocalTime getAcCreTm() {
        return acCreTm;
    }

    public void setAcCreTm(LocalTime acCreTm) {
        this.acCreTm = acCreTm;
    }
}
