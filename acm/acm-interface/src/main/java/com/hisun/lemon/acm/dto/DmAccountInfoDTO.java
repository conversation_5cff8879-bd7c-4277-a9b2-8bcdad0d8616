package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 数币账户信息请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15 17:43
 */
@ClientValidated
@ApiModel(value = "DmAccountInfoDTO", description = "数币账户信息请求类")
public class DmAccountInfoDTO extends GenericDTO<NoBody> {
    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    @Length(max = 20)
    private String userId;

    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    /**
     * 网络
     */
    @ApiModelProperty(name = "network", value = "网络")
    private String network;

    /**
     * 地址
     */
    @ApiModelProperty(name = "address", value = "地址")
    private String address;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
