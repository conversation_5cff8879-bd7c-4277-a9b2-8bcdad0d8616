package com.hisun.lemon.acm.dto;

import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @function ItemAccountDTO
 * @description 科目相关接口传输对象
 * @date 7/16/2017 Sun
 * @time 5:16 PM
 */
@ApiModel("科目账户信息")
@ClientValidated
public class ItemAccountDTO {
    /**
     * itmNo 科目号
     */
    @ApiModelProperty(name = "itmNo", value = "科目号", required = true)
    @Length(max = 10)
    @NotEmpty(message = ACMMessageCode.ITM_NO_IS_NULL)
    private String itmNo;
    /**
     * itmEnm 科目英文名称
     */
    @ApiModelProperty(name = "itmEnm", value = "科目英文名称", required = true)
    @Length(max = 128)
    private String itmEnm;
    /**
     * itmCnm 科目中文名称
     */
    @ApiModelProperty(name = "itmCnm", value = "科目中文名称", required = true)
    @Length(max = 128)
    private String itmCnm;
    /**
     * itmLvl 科目级别（1：一级科目、2：二级科目、3：三级科目）
     */
    @ApiModelProperty(name = "itmLvl", value = "科目级别（1：一级科目、2：二级科目、3：三级科目）", required = true)
    @Length(max = 1)
    private Integer itmLvl;
    /**
     * upItmNo 上级科目号
     */
    @ApiModelProperty(name = "upItmNo", value = "上级科目号")
    @Length(max = 10)
    private String upItmNo;
    /**
     * btmItmFlg 最底层科目标（Y：最底层、N：非最底层）
     */
    @ApiModelProperty(name = "btmItmFlg", value = "最底层科目标（Y：最底层、N：非最底层）", required = true)
    @Length(max = 1)
    private String btmItmFlg;
    /**
     * itmTyp 科目类别（A-资产类、L-负债类、C-所有者权益类、I-收入类、E-支出类、O-表外类、S-往来账）
     */
    @ApiModelProperty(name = "itmTyp", value = "科目类别（A-资产类、L-负债类、C-所有者权益类、I-收入类、E-支出类、O-表外类、S-往来账）", required = true)
    @Length(max = 1)
    private String itmTyp;
    /**
     * itmCls 科目分类（1-存放银行资金池账户、2-差错/争议挂账账户、3-其他内部账户）
     */
    @ApiModelProperty(name = "itmCls", value = "科目分类（1-存放银行资金池账户、2-差错/争议挂账账户、3-其他内部账户）", required = true)
    @Length(max = 1)
    private String itmCls;
    /**
     * balOdFlg 余额是否允许透支标识N:不允许Y:允许
     */
    @ApiModelProperty(name = "balOdFlg", value = "余额是否允许透支标识N:不允许Y:允许", required = true)
    @Length(max = 1)
    private String balOdFlg;
    /**
     * balDrt 余额方向（A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）
     */
    @ApiModelProperty(name = "balDrt", value = "余额方向（A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）", required = true)
    @Length(max = 1)
    private String balDrt;
    /**
     * updBalFlg 余额更新方式0：实时更新1：批量更新
     */
    @ApiModelProperty(name = "updBalFlg", value = "余额更新方式0：实时更新1：批量更新", required = true)
    @Length(max = 1)
    private String updBalFlg;
    /**
     * itmSts 科目状态（1-生效、0-失效）
     */
    @ApiModelProperty(name = "itmSts", value = "科目状态（1-生效、0-失效）")
    @Length(max = 1)
    private String itmSts;
    /**
     * effDt 生效日期
     */
    @ApiModelProperty(name = "effDt", value = "生效日期", required = true)
    private LocalDate effDt;
    /**
     * expDt 失效日期
     */
    @ApiModelProperty(name = "expDt", value = "失效日期", required = true)
    private LocalDate expDt;
    /**
     * updOpr 更新操作员
     */
    @ApiModelProperty(name = "updOpr", value = "更新操作员")
    @Length(max = 10)
    private String updOpr;

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public String getItmEnm() {
        return itmEnm;
    }

    public void setItmEnm(String itmEnm) {
        this.itmEnm = itmEnm;
    }

    public String getItmCnm() {
        return itmCnm;
    }

    public void setItmCnm(String itmCnm) {
        this.itmCnm = itmCnm;
    }

    public Integer getItmLvl() {
        return itmLvl;
    }

    public void setItmLvl(Integer itmLvl) {
        this.itmLvl = itmLvl;
    }

    public String getUpItmNo() {
        return upItmNo;
    }

    public void setUpItmNo(String upItmNo) {
        this.upItmNo = upItmNo;
    }

    public String getBtmItmFlg() {
        return btmItmFlg;
    }

    public void setBtmItmFlg(String btmItmFlg) {
        this.btmItmFlg = btmItmFlg;
    }

    public String getItmTyp() {
        return itmTyp;
    }

    public void setItmTyp(String itmTyp) {
        this.itmTyp = itmTyp;
    }

    public String getItmCls() {
        return itmCls;
    }

    public void setItmCls(String itmCls) {
        this.itmCls = itmCls;
    }

    public String getBalOdFlg() {
        return balOdFlg;
    }

    public void setBalOdFlg(String balOdFlg) {
        this.balOdFlg = balOdFlg;
    }

    public String getBalDrt() {
        return balDrt;
    }

    public void setBalDrt(String balDrt) {
        this.balDrt = balDrt;
    }

    public String getUpdBalFlg() {
        return updBalFlg;
    }

    public void setUpdBalFlg(String updBalFlg) {
        this.updBalFlg = updBalFlg;
    }

    public String getItmSts() {
        return itmSts;
    }

    public void setItmSts(String itmSts) {
        this.itmSts = itmSts;
    }

    public LocalDate getEffDt() {
        return effDt;
    }

    public void setEffDt(LocalDate effDt) {
        this.effDt = effDt;
    }

    public LocalDate getExpDt() {
        return expDt;
    }

    public void setExpDt(LocalDate expDt) {
        this.expDt = expDt;
    }

    public String getUpdOpr() {
        return updOpr;
    }

    public void setUpdOpr(String updOpr) {
        this.updOpr = updOpr;
    }
}
