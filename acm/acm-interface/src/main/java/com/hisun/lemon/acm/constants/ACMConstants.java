package com.hisun.lemon.acm.constants;

/**
 * <AUTHOR>
 * @function ACMConstants
 * @description 账务模块业务常量定义
 * @date 7/13/2017 Thu
 * @time 7:40 PM
 */
public class ACMConstants {
    /**
     * 未能获取到国家编码的用户--用NK作为账号前缀 NO KNOWN的缩写
     */
    public final static String UNKNOWN_COUNTRY_CODE = "NK";

    /**
     * 账号的序号长度为10
     */
    public final static int AC_SEQ_LEN = 10;

    /**
     * 账户正常状态
     */
    public final static String AC_OPEN_STS = "0";

    /**
     * 账户销户状态
     */
    public final static String AC_CANCEL_STS = "1";

    /**
     * 账户待审核状态
     */
    public final static String AC_PENDING_STS = "2";

    /**
     * 账户审核被拒状态
     */
    public final static String AC_REJECT_STS = "3";

    /**
     * 系统默认使用的币种(美元)
     */
    public final static String SYS_DEFAULT_CCY = "USD";

    /**
     * 科目余额状态S-停用 D-无效（Deactive） A-有效（Active）
     */
    public final static String GL_STS_INIT = "A";

    /**
     * 科目余额更新方式 余额更新方式 1：批量更新
     */
    public final static String BAL_BAT_UPD = "1";

    /**
     * 科目余额更新方式 余额更新方式 0：实时更新
     */
    public final static String BAL_REAL_TIME_UPD = "0";

    /**
     * 科目状态 1-失效、0-生效
     */
    public final static String ITM_OPEN_STS = "0";

    /**
     * 科目状态 1-失效
     */
    public final static String ITM_CANCEL_STS = "1";

    /**
     * 余额Tag的密钥Key
     */
    public final static String AC_BAL_TAG_KEY = "4F59989D746167";

    /**
     * 账务处理状态 N：正常 C:冲正
     */
    public final static String ACCOUNTING_NOMARL = "N";

    /**
     * 账务处理状态 N：正常 C:冲正
     */
    public final static String ACCOUNTING_CANCEL = "C";

    /**
     * 账务处理 用户账号类型
     */
    public final static String USER_AC_TYP = "U";

    /**
     * 账务处理 科目账号
     */
    public final static String ITM_AC_TYP = "I";

    /**
     * 借贷标志 D 借方
     */
    public final static String AC_D_FLG = "D";

    /**
     * 借贷标志 C 贷
     */
    public final static String AC_C_FLG = "C";

    /**
     * 余额方向A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映
     */
    public final static String BAL_DRT_C = "C";

    /**
     * 余额方向A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映
     */
    public final static String BAL_DRT_D = "D";

    /**
     * 余额方向A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映
     */
    public final static String BAL_DRT_B = "B";

    /**
     * 余额方向A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映
     */
    public final static String BAL_DRT_A = "A";

    /**
     * 核对结果0：正确1：错误2：未核对
     */
    public final static String GENERAL_LEDGER_CHK_RSL_CORRECT = "0";

    /**
     * 核对结果0：正确1：错误2：未核对
     */
    public final static String GENERAL_LEDGER_CHK_RSL_FALSE = "1";

    /**
     * hldSts 状态0：冻结；1：正常使用；2：到期自动处理
     */
    public final static String HOLDED = "0";
    public final static String UNHOLDED = "1";
    public final static String SYS_UNHOLD = "2";

    /**
     * 币种
     */
    public final static String CCY_DM_USDC = "USDC";
    public final static String CCY_DM_USDT = "USDT";

    /**
     * 网络
     */
    public final static String NETWORK_TRON = "TRC20";
    public final static String NETWORK_ETH = "ERC20";
}
