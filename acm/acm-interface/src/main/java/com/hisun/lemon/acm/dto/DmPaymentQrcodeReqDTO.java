package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;

/**
 * 数币收款二维码请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15 10:51
 */
@ApiModel(value = "DmPaymentQrcodeReqDTO", description = "数币收款二维码请求")
@ClientValidated
public class DmPaymentQrcodeReqDTO {

    @ApiModelProperty(name = "acNo", value = "账户编号", required = true)
    @NotBlank(message = "账户编号不能为空")
    private String acNo;

    @ApiModelProperty(name = "address", value = "收款地址", required = true)
    @NotBlank(message = "收款地址不能为空")
    private String address;

    @ApiModelProperty(name = "money", value = "收款金额")
    private BigDecimal money;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

}
