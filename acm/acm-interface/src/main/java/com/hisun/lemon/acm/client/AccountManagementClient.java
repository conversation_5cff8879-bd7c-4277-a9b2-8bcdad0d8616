package com.hisun.lemon.acm.client;

import com.hisun.lemon.acm.dto.*;
import com.hisun.lemon.framework.annotation.LemonBody;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @function AccountManagementClient
 * @description 账户管理服务接口
 * @date 7/24/2017 Mon
 * @time 7:48 PM
 */
@FeignClient(value = "ACM", path = "/acm/account")
public interface AccountManagementClient {
    /**
     * 新建用户账户
     *
     * @param userId
     * @return UserAccountDTO.java
     */
    @PostMapping("/users/{userId}")
    public GenericRspDTO<String> openUserAccount(@PathVariable("userId") String userId);

    /**
     * 新建科目账户
     *
     * @param itemAccountDTO
     * @return GenericDTO
     */
    @PostMapping("/items")
    public GenericRspDTO<NoBody> openItemAccount(@RequestBody GenericDTO<ItemAccountDTO> itemAccountDTO);

    /**
     * 用户账户注销
     *
     * @param userId
     * @return
     */
    @DeleteMapping("/users/{userId}")
    public GenericRspDTO<NoBody> closeUserAccount(@PathVariable("userId") String userId);

    /**
     * 科目账户注销
     *
     * @param itmNo
     * @return
     */
    @DeleteMapping("/items/{itmNo}")
    public GenericRspDTO<NoBody> closeItemAccount(@PathVariable("itmNo") String itmNo);

    /**
     * 根据账号查询用户账户余额
     *
     * @param acNo
     * @param userId
     * @param capTyp
     * @param genericDTO
     * @return
     */
    @GetMapping("/acbal/param")
    public GenericRspDTO<List<QueryAcBalRspDTO>> queryAcBal(@RequestParam("acNo") String acNo, @RequestParam("userId")
            String userId, @RequestParam("capTyp") String capTyp, GenericDTO<NoBody> genericDTO);

    /**
     * 根据账号查询用户账户余额
     *
     * @param userAccountDTO
     * @return
     */
    @PostMapping("/acbal")
    public GenericRspDTO<List<QueryAcBalRspDTO>> queryAcBal(@RequestBody UserAccountDTO userAccountDTO);

    /**
     * 根据用户号查询账户号
     *
     * @param userId
     * @return
     */
    @GetMapping("/acno/{userId}")
    public GenericRspDTO<String> queryAcNo(@PathVariable("userId") String userId);

    /**
     * 根据用户号和币种查询账户号
     *
     * @param userAccountDTO
     * @return
     */
    @GetMapping("/acno/queryAcNo")
    public GenericRspDTO<String> queryAcNo(@Validated @RequestBody UserAccountDTO userAccountDTO);

    /**
     * 根据账户号查询用户号
     *
     * @param acNo
     * @return
     */
    @GetMapping("/users/{acNo}")
    public GenericRspDTO<String> queryUser(@PathVariable("acNo") String acNo);

    /**
     * 根据科目号查询科目信息
     */
    @GetMapping("/items/{itmNo}")
    public GenericRspDTO<ItemAccountDTO> queryItemAccountInf(@PathVariable("itmNo") String itmNo);


    /**
     * 获取所有科目余额信息
     */
    @GetMapping("/items/queryAll")
    public GenericRspDTO<List<ItemAccountBalDTO>> queryAllItemAccountInf();

    /**
     * 获取所有科目余额信息
     */
    @GetMapping("/dm/getAll")
    public GenericRspDTO<List<DmPlatFormBalDTO>> queryDmAccountBals();


    /**
     * @param userAccountDTO
     * @return
     */
    @PostMapping("/settle/acbalFreeze")
    public GenericRspDTO<NoBody> freezeSettleAcBal(@RequestBody GenericDTO<UserAccountFreezeDTO> userAccountDTO);

    /**
     * @param userAccountDTO
     * @return
     */
    @PostMapping("/settle/acbalUnfreeze")
    public GenericRspDTO<NoBody> unfreezeSettleAcBal(@Validated @RequestBody GenericDTO<UserAccountFreezeDTO> userAccountDTO);

    @GetMapping("/enableAcBal")
    public GenericRspDTO<BigDecimal> getEnableAcBal(@RequestBody UserAccountDTO userAccountDTO);

    /**
     * 获取当前用户币种账户列表
     *
     * @param userCcyAccountListDTO 用户币种账户查询参数
     * @return 币种账户列表
     */
    @GetMapping("/list")
    GenericRspDTO<List<UserCcyAccountRspDTO>> queryUserAccountList(@Validated @LemonBody UserCcyAccountListDTO userCcyAccountListDTO);

    /**
     * 获取用户数币账户列表
     *
     * @param userDmAccountListDTO 用户数币账户查询参数
     * @return 数币账户列表
     */
    @GetMapping("/dm/list")
    GenericRspDTO<List<UserDmAccountRspDTO>> queryUserDmAccountList(@Validated @LemonBody UserDmAccountListDTO userDmAccountListDTO);

    /**
     * 查看数币账户详情
     *
     * @param acNo 账户编号
     * @return 数币账户详情
     */
    @GetMapping("/dm/{acNo}")
    GenericRspDTO<DmAccountDetailRspDTO> queryDmAccountDetail(@Validated @PathVariable("acNo") String acNo);

    /**
     * 获取平台支持币种列表
     *
     * @return 币种列表
     */
    @GetMapping("/ccy/list")
    GenericRspDTO<List<DmCcyListRspDTO>> getCcyList(@LemonBody GenericDTO<NoBody> genericDTO);

    /**
     * 开立数币账户
     *
     * @param req 开户请求参数
     * @return 响应结果
     */
    @PostMapping("/dm/open")
    GenericRspDTO<NoBody> openDmAccount(@Validated @RequestBody GenericDTO<OpenDmAccountDTO> req);

    /**
     * 获取当前用户收款/充值数币账户列表
     * @param genericDTO
     * @return
     */
    @PostMapping("/dm/receipt/accounts")
    GenericRspDTO<List<DmReceiptAccRspDTO>> getDmReceiptAcc(@Validated @RequestBody GenericDTO<DmReceiptAccReqDTO> genericDTO);

    /**
     * 收款二维码
     *
     * @param req
     * @return
     */
    @PostMapping("/dm/qrcode/payment")
    GenericRspDTO<DmPaymentQrCodeRspDTO> getDmPaymentQrcode(
            @Validated @RequestBody GenericDTO<DmPaymentQrcodeReqDTO> req);

    /**
     * 获取用户所有数币账户信息
     */
    @PostMapping("/dm/all/info")
    GenericRspDTO<List<DmAccountInfoRspDTO>> getDmAccountInfo(@Validated @RequestBody DmAccountInfoDTO req);

    /**
     * 通过订单集合获取数币账户收款/充值分页记录列表
     */
    @PostMapping("/dm/receipts/list")
    GenericRspDTO<List<ReceiptRecord>> getDmAccountReceiptsList(@RequestBody ReceiptRecordReqDTO req);

    /**
     * 往回调表中新增数据
     *
     * @param req
     * @return
     */
    @PostMapping("/add/callback")
    GenericRspDTO<Long> addCallback(@Validated @RequestBody AddCallbackReqDTO req);

    /**
     * 更新数币账户回调信息
     * @param req
     */
    @PostMapping("/update/callback")
    GenericRspDTO<NoBody> updateCallback(@Validated @RequestBody UpdateCallbackReqDTO req);

    /**
     * 根据地址查询数币账户地址信息
     *
     * @param address
     * @return
     */
    @GetMapping("/get/by-address")
    GenericRspDTO<DmAccountAddressRspDTO> getByAddress(@Validated @NotBlank @Length(max = 100) @RequestParam("address") String address);

    /**
     * 检查数币账户地址是否存在
     *
     * @param address
     * @return
     */
    @GetMapping("/check-address-exists")
    GenericRspDTO<Boolean> checkAddressExists(@Validated @NotBlank @Length(max = 100) @RequestParam("address") String address);

    /**
     * 更新账户余额信息
     *
     * @param req
     * @return
     */
    @PostMapping("/update/balance")
    GenericRspDTO<NoBody> updateBalance(@Validated @RequestBody UpdateAccBalReqDTO req);

    /**
     * 调用cregis更新数币余额
     *
     * @param acNo
     * @return
     */
    @PostMapping("/balance/update")
    GenericRspDTO<NoBody> updateBalByCregis(@Validated @RequestParam(name = "acNo") String acNo);

    /**
     * 数币充值和收款做账务处理
     *
     * @param req
     * @return
     */
    @PostMapping("/rechargeAndReceiptAct")
    GenericRspDTO<NoBody> rechargeAndReceiptAct(@Validated @RequestBody AddOrderReqDTO req);
}
