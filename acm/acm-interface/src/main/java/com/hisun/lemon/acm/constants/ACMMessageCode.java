package com.hisun.lemon.acm.constants;

/**
 * <AUTHOR>
 * @function ACMMessageCode
 * @description 错误码定义
 * @date 7/13/2017 Thu
 * @time 3:33 PM
 */
public class ACMMessageCode {
    /**
     * 交易成功
     * ACM_SUCC
     */
    public final static String ACM_SUCC = "ACM00000";

    /**
     * 账号为空
     * ACNO_IS_NULL
     */
    public final static String ACNO_IS_NULL = "ACM10000";

    /**
     * 用户号为空
     */
    public final static String USER_ID_IS_NULL = "ACM10001";

    /**
     * 请求内容为空
     */
    public final static String PARAM_IS_NULL = "ACM10002";

    /**
     * 科目号为空
     */
    public final static String ITM_NO_IS_NULL = "ACM10003";

    /**
     * 记录为空
     */
    public final static String RECORD_IS_NULL = "ACM10004";

    /**
     * 资金类型为空
     */
    public final static String CAPTYP_IS_NULL = "ACM10005";

    /**
     * 账户余额不为0，不能销户
     */
    public final static String AC_BAL_NOT_ZERO = "ACM30001";

    /**
     * 科目余额不为0,不能销户
     */
    public final static String ITM_BAL_NOT_ZERO = "ACM30002";

    /**
     * 账户不存在
     */
    public final static String AC_NOT_EXIST = "ACM30003";

    /**
     * 账号不合法
     */
    public final static String ILLEGAL_AC_NO = "ACM30004";

    /**
     * 交易状态不一致
     */
    public final static String TX_STS_NOT_CONSISTENT = "ACM30005";

    /**
     * 余额不足，余额记录不存在
     */
    public final static String BAL_NOT_ENOUGH = "ACM30006";

    /**
     * 资金类型为空
     */
    public final static String CAP_TYP_IS_NULL = "ACM30007";

    /**
     * 借贷标志为空
     */
    public final static String DC_FLG_IS_NULL = "ACM30008";

    /**
     * 账号类型为空
     */
    public final static String AC_TYP_IS_NULL = "ACM30009";

    /**
     * 科目号不存在
     */
    public final static String ITM_NOT_EXIST = "ACM30010";

    /**
     * 科目销户
     */
    public final static String ITM_IS_CLOSED = "ACM30011";

    /**
     * 账号销户
     */
    public final static String AC_IS_CLOSED = "ACM30012";

    /**
     * 借贷不平
     */
    public final static String DR_NOT_EQUAL_CR = "ACM30013";

    /**
     * 跨日不能冲正
     */
    public final static String EXP_NOT_CANCEL = "ACM30014";

    /**
     * 流水号不一致
     */
    public final static String TX_JRN_NO_NOT_CONSISTENT = "ACM30015";

    /**
     * 冻结编号不合法
     */
    public final static String HOLD_NO_IS_NULL = "ACM30016";

    /**
     * 该笔订单已解冻
     */
    public final static String ORD_UNHOLDED = "ACM30017";

    /**
     * 非法金额
     */
    public final static String ILLEGAL_AMT = "ACM30018";

    /**
     * 原交易不存在
     */
    public final static String ORIGINAL_TRADE_NOT_EXIST = "ACM30019";

    /**
     * 冲正交易笔数不正确
     */
    public final static String REVERSAL_TRADE_NUM_FALSE = "ACM30020";

    /**
     * 账务初始化失败
     */
    public final static String INIT_ACCOUNTING_FAILURE = "ACM30021";

    /**
     * 数币-账户信息表中无可用地址
     */
    public final static String ADDRESS_NOT_AVAILABLE = "ACM30022";

    /**
     * 已有一个法币开户申请
     */
    public final static String EXIST_OPENING_REQUEST = "ACM30023";

    /**
     * 开户名与当前KYB企业主体名称不一致
     */
    public final static String ACNAME_KYB_NOT_SAME = "ACM30024";

    /**
     * 账户号重复
     */
    public final static String AC_NO_REPEAT = "ACM30025";

    /**
     * 数币账户地址不存在
     */
    public final static String ADDRESS_NOT_EXIST = "ACM30026";
}
