package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @function AccountingReqDTO
 * @description 多借多贷账务请求处理传输对象
 * @date 7/17/2017 Mon
 * @time 6:54 PM
 */
@ApiModel("多借多贷账务请求")
@ClientValidated
public class AccountingReqDTO {
    /**
     * txSts 交易状态 N:正常 R:撤销 C:冲正  撤销暂不考虑
     */
    @ApiModelProperty(name = "txSts", value = "交易状态 N:正常 R:撤销 C:冲正  撤销暂不考虑")
    @Length(max = 1)
    @NotBlank
    private String txSts;
    /**
     * acTyp  账户类型 U:用户 I：科目 DO对象不存在此域
     */
    @ApiModelProperty(name = "acTyp", value = "账户类型 U:用户 I：科目")
    @Length(max = 1)
    @NotBlank
    private String acTyp;
    /**
     * acNo 交易账号
     */
    @ApiModelProperty(name = "acNo", value = "交易账号")
    @Length(max = 15)
    private String acNo;
    /**
     * capTyp 账户资金属性
     */
    @ApiModelProperty(name = "capTyp", value = "账户资金属性， 1：现金，8：待结算")
    @Length(max = 1)
    private String capTyp;
    /**
     * itmNo 科目号
     */
    @ApiModelProperty(name = "itmNo", value = "科目号")
    @Length(max = 10)
    private String itmNo;
    /**
     * txTyp 交易类型
     */
    @ApiModelProperty(name = "txTyp", value = "交易类型", required = true)
    @Length(max = 4)
    @NotBlank
    private String txTyp;
    /**
     * txAmt 交易金额
     */
    @ApiModelProperty(name = "txAmt", value = "交易金额", required = true)
    @NotNull
    @Range(min = 0)
    private BigDecimal txAmt;
    /**
     * dcFlg 借贷标志
     */
    @ApiModelProperty(name = "dcFlg", value = "借贷标志 D:借， C:贷", required = true)
    @Length(max = 1)
    @NotBlank
    private String dcFlg;
    /**
     * txJrnNo 请求唯一流水号(堵重)
     */
    @ApiModelProperty(name = "txJrnNo", value = "请求唯一流水号", required = true)
    @Length(max = 32)
    @NotBlank
    private String txJrnNo;
    /**
     * txOrdNo 请求订单号
     */
    @ApiModelProperty(name = "txOrdNo", value = "请求订单号")
    @Length(max = 32)
    private String txOrdNo;
    /**
     * txOrdDt 请求订单日期
     */
    @ApiModelProperty(name = "txOrdDt", value = "请求订单日期", dataType = "java.time.LocalDate")
    private LocalDate txOrdDt;
    /**
     * txOrdTm 请求订单时间
     */
    @ApiModelProperty(name = "txOrdTm", value = "请求订单时间", dataType = "java.time.LocalTime")
    private LocalTime txOrdTm;
    /**
     * oppAcNo 账号
     */
    @ApiModelProperty(name = "oppAcNo", value = "对手账号")
    @Length(max = 15)
    private String oppAcNo;
    /**
     * oppCapTyp 资金属性
     */
    @ApiModelProperty(name = "oppCapTyp", value = "资金属性")
    @Length(max = 1)
    private String oppCapTyp;
    /**
     * oppUserId 用户号
     */
    @ApiModelProperty(name = "oppUserId", value = "对手用户号")
    @Length(max = 16)
    private String oppUserId;
    /**
     * oppUserTyp 用户类型
     */
    @ApiModelProperty(name = "oppUserTyp", value = "用户类型")
    @Length(max = 1)
    private String oppUserTyp;
    /**
     * usrIpAdr 用户IP地址
     */
    @ApiModelProperty(name = "usrIpAdr", value = "用户IP地址")
    @Length(max = 15)
    private String usrIpAdr;
    /**
     * rmk 备注
     */
    @ApiModelProperty(name = "rmk", value = "备注")
    @Length(max = 512)
    private String rmk;

    public String getTxSts() {
        return txSts;
    }

    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    public String getAcTyp() {
        return acTyp;
    }

    public void setAcTyp(String acTyp) {
        this.acTyp = acTyp;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public BigDecimal getTxAmt() {
        return txAmt;
    }

    public void setTxAmt(BigDecimal txAmt) {
        this.txAmt = txAmt;
    }

    public String getDcFlg() {
        return dcFlg;
    }

    public void setDcFlg(String dcFlg) {
        this.dcFlg = dcFlg;
    }

    public String getTxJrnNo() {
        return txJrnNo;
    }

    public void setTxJrnNo(String txJrnNo) {
        this.txJrnNo = txJrnNo;
    }

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public LocalDate getTxOrdDt() {
        return txOrdDt;
    }

    public void setTxOrdDt(LocalDate txOrdDt) {
        this.txOrdDt = txOrdDt;
    }

    public LocalTime getTxOrdTm() {
        return txOrdTm;
    }

    public void setTxOrdTm(LocalTime txOrdTm) {
        this.txOrdTm = txOrdTm;
    }

    public String getOppAcNo() {
        return oppAcNo;
    }

    public void setOppAcNo(String oppAcNo) {
        this.oppAcNo = oppAcNo;
    }

    public String getOppCapTyp() {
        return oppCapTyp;
    }

    public void setOppCapTyp(String oppCapTyp) {
        this.oppCapTyp = oppCapTyp;
    }

    public String getOppUserId() {
        return oppUserId;
    }

    public void setOppUserId(String oppUserId) {
        this.oppUserId = oppUserId;
    }

    public String getOppUserTyp() {
        return oppUserTyp;
    }

    public void setOppUserTyp(String oppUserTyp) {
        this.oppUserTyp = oppUserTyp;
    }

    public String getUsrIpAdr() {
        return usrIpAdr;
    }

    public void setUsrIpAdr(String usrIpAdr) {
        this.usrIpAdr = usrIpAdr;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }
}
