package com.hisun.lemon.acm.dto;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

public class UserFmAccountRspDTO {
    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    /**
     * 币种logo
     */
    @ApiModelProperty(name = "ccyLogo", value = "币种logo")
    private String ccyLogo;

    /**
     * 账户余额
     */
    @ApiModelProperty(name = "accountBalance", value = "账户余额")
    private BigDecimal accountBalance;

    /**
     * 账户总资产
     */
    @ApiModelProperty(name = "totalBalance", value = "账户总资产")
    private BigDecimal totalBalance;

    /**
     * 账户号码
     */
    @ApiModelProperty(name = "accountNo", value = "账户号码")
    private String accountNo;

    /**
     * 账户名称
     */
    @ApiModelProperty(name = "accountName", value = "账户名称")
    private String accountName;

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCcyLogo() {
        return ccyLogo;
    }

    public void setCcyLogo(String ccyLogo) {
        this.ccyLogo = ccyLogo;
    }

    public BigDecimal getAccountBalance() {
        return accountBalance;
    }

    public void setAccountBalance(BigDecimal accountBalance) {
        this.accountBalance = accountBalance;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

}
