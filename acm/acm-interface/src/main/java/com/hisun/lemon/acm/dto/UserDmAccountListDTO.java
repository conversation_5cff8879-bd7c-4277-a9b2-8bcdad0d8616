package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

/**
 * 数币账户列表查询请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10 17:05
 */
@ClientValidated
@ApiModel(value = "UserDmAccountListDTO", description ="用户数币账户列表查询请求")
public class UserDmAccountListDTO extends GenericDTO<NoBody> {

}
