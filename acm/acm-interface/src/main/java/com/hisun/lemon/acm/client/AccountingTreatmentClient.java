package com.hisun.lemon.acm.client;

import com.hisun.lemon.acm.dto.AccountingHoldReqDTO;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @function AccountingTreatmentClient
 * @description 账务处理服务接口
 * @date 7/24/2017 Mon
 * @time 7:49 PM
 */
@FeignClient("ACM")
public interface AccountingTreatmentClient {
    /**
     * 账务处理
     * @param accountingReqDTOList
     * @return
     */
    @PostMapping("/acm/trade/accounting")
    public GenericRspDTO<NoBody> accountingTreatment(@RequestBody GenericDTO<List<AccountingReqDTO>>
                                                               accountingReqDTOList);

    /**
     * 冻结账户余额
     * @param accountingHoldReqDTO
     * @return
     */
    @PostMapping("/acm/trade/hold")
    public GenericRspDTO<String> holdUserAccountBalance(@RequestBody GenericDTO<AccountingHoldReqDTO>
                                                                accountingHoldReqDTO);

    /**
     * 解冻账户余额
     * @param holdNo
     * @return
     */
    @PutMapping("/acm/trade/unhold/{holdNo}")
    public GenericRspDTO<NoBody> unHoldUserAccountBalance(@PathVariable("holdNo") String holdNo);
}
