package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 数币收款二维码响应
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15 10:57
 */
@ApiModel(value = "DmPaymentQrCodeRspDTO", description = "数币收款二维码响应")
@ClientValidated
public class DmPaymentQrCodeRspDTO {

    @ApiModelProperty(name = "qrcodeBase64", value = "二维码base64", required = true)
    private String qrcodeBase64;

    @ApiModelProperty(name = "expiryDateTime", value = "二维码有效期", required = true)
    private LocalDateTime expiryDateTime;


    public String getQrcodeBase64() {
        return qrcodeBase64;
    }

    public void setQrcodeBase64(String qrcodeBase64) {
        this.qrcodeBase64 = qrcodeBase64;
    }


    public LocalDateTime getExpiryDateTime() {
        return expiryDateTime;
    }

    public void setExpiryDateTime(LocalDateTime expiryDateTime) {
        this.expiryDateTime = expiryDateTime;
    }
}
