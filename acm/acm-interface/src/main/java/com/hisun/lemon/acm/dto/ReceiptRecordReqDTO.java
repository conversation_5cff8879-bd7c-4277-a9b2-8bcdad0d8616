package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * acm模块-数币账户收款/充值记录列表请求传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 17:59
 */
@ClientValidated
@ApiModel(value = "ReceiptRecordReqDTO", description = "acm模块-数币账户收款/充值记录列表请求传输对象")
public class ReceiptRecordReqDTO extends GenericDTO<NoBody> {

    /**
     * 订单号集合
     */
    @ApiModelProperty(name = "orderNos", value = "订单号集合")
    List<String> orderNos;

    /**
     * 页码
     */
    @ApiModelProperty(name = "pageNo", value = "页码，从1开始")
    private int pageNo = 1;

    /**
     * 每页记录数
     */
    @ApiModelProperty(name = "pageSize", value = "每页记录数，默认20")
    private int pageSize = 20;

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
