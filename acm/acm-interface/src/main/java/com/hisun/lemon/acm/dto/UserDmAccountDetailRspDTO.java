package com.hisun.lemon.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户数币账户详情响应
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10 19:17
 */
@ApiModel("用户数币账户详情响应")
public class UserDmAccountDetailRspDTO {
    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    /**
     * 币种logo
     */
    @ApiModelProperty(name = "ccyLogo", value = "币种logo")
    private String ccyLogo;

    /**
     * 币种类型（网络）
     */
    @ApiModelProperty(name = "network", value = "币种类型（如ERC20）")
    private String network;

    /**
     * 账户余额
     */
    @ApiModelProperty(name = "availableBalance", value = "账户余额")
    private BigDecimal accountBalance;

    /**
     * 账户总资产
     */
    @ApiModelProperty(name = "totalBalance", value = "账户总资产")
    private BigDecimal totalBalance;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "账户金额最新更新时间（格式：YYYY-MM-DD hh:mm:ss TZ）")
    private String updateTime;

    /**
     * 账户状态
     */
    @ApiModelProperty(name = "accountStatus", value = "账户状态（正常/冻结/停用）")
    private String accountStatus;

    /**
     * 账户地址
     */
    @ApiModelProperty(name = "address", value = "账户地址")
    private String address;

    /**
     * 账户地址二维码
     */
    @ApiModelProperty(name = "addressQrCode", value = "账户地址二维码")
    private String addressQrCode;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "账户地址创建时间（格式：YYYY-MM-DD hh:mm:ss）")
    private LocalDateTime createTime;

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCcyLogo() {
        return ccyLogo;
    }

    public void setCcyLogo(String ccyLogo) {
        this.ccyLogo = ccyLogo;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public BigDecimal getAccountBalance() {
        return accountBalance;
    }

    public void setAccountBalance(BigDecimal accountBalance) {
        this.accountBalance = accountBalance;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddressQrCode() {
        return addressQrCode;
    }

    public void setAddressQrCode(String addressQrCode) {
        this.addressQrCode = addressQrCode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
