package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @function QueryAcBalReqDTO
 * @description 查询账户余额的DTO
 * @date 7/13/2017 Thu
 * @time 12:03 PM
 */
@ApiModel("用户账户余额")
@ClientValidated
public class QueryAcBalRspDTO {
    /**
     * @param acNo 账号
     */
    @ApiModelProperty(name = "acNo", value = "账号")
    @Length(max = 15)
    private String acNo;

    /**
     * capTyp 资金属性 1：现金 8：待结算资金
     */
    @ApiModelProperty(name = "capTyp", value = "资金属性1：现金 8：待结算资金")
    @Length(max = 1)
    private String capTyp;

    /**
     * ccy 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种 USD:美元")
    @Length(max = 3)
    private String ccy;

    /**
     * acCurBal 账户当前可用余额
     */
    @ApiModelProperty(name = "acCurBal", value = "账户当前可用余额", required = true)
    private BigDecimal acCurBal;

    /**
     * acUavaBal 账户当前不可用余额
     */
    @ApiModelProperty(name = "acUavaBal", value = "账户当前不可用余额", required = true)
    private BigDecimal acUavaBal;

    /**
     * 结算冻结金额
     */
    @ApiModelProperty(name = "acCurFreezeBal", value = "结算冻结金额", required = true)
    private BigDecimal acCurFreezeBal;

    /**
     * userId 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", required = true)
    private String userId;

    @ApiModelProperty(name = "network", value = "数币账户网络")
    private String network;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getAcCurFreezeBal() {
        return acCurFreezeBal;
    }

    public void setAcCurFreezeBal(BigDecimal acCurFreezeBal) {
        this.acCurFreezeBal = acCurFreezeBal;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getAcCurBal() {
        return acCurBal;
    }

    public void setAcCurBal(BigDecimal acCurBal) {
        this.acCurBal = acCurBal;
    }

    public BigDecimal getAcUavaBal() {
        return acUavaBal;
    }

    public void setAcUavaBal(BigDecimal acUavaBal) {
        this.acUavaBal = acUavaBal;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }
}
