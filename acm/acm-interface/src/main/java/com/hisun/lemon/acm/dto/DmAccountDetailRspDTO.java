package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 数币账户详情响应类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14 16:46
 */
@ApiModel(value = "DmAccountDetailRspDTO", description = "返回数币账户的详细信息")
@ClientValidated
public class DmAccountDetailRspDTO {

    @ApiModelProperty(name = "id", value = "ID")
    private Long id;

    @ApiModelProperty(name = "userId", value = "用户ID")
    private String userId;

    @ApiModelProperty(name = "acNo", value = "平台内账号编码")
    private String acNo;

    @ApiModelProperty(name = "accountId", value = "Cregis账户ID")
    private Long accountId;

    @ApiModelProperty(name = "coinId", value = "币种ID")
    private String coinId;

    @ApiModelProperty(name = "address", value = "地址")
    private String address;

    @ApiModelProperty(name = "network", value = "所在区块链网络")
    private String network;

    @ApiModelProperty(name = "status", value = "账户状态：0-开户，1-销户,2-待审核,3-审核不通过")
    private String status;

    @ApiModelProperty(name = "realBal", value = "账户当前可用余额")
    private BigDecimal realBal;

    @ApiModelProperty(name = "acUavaBal", value = "账户当前不可用余额")
    private BigDecimal acUavaBal;

    @ApiModelProperty(name = "acLastBal", value = "账户上一日余额")
    private BigDecimal acLastBal;

    @ApiModelProperty(name = "acLastUavaBal", value = "账户上日不可用余额")
    private BigDecimal acLastUavaBal;

    @ApiModelProperty(name = "acBalTag", value = "余额tag")
    private String acBalTag;

    @ApiModelProperty(name = "acUpdDt", value = "余额最新变动日期")
    private LocalDate acUpdDt;

    @ApiModelProperty(name = "acUpdTm", value = "余额最新变动时间")
    private LocalTime acUpdTm;

    @ApiModelProperty(name = "acFrzDt", value = "账户余额冻结日期")
    private LocalDate acFrzDt;

    @ApiModelProperty(name = "acFrzTm", value = "账户余额冻结时间")
    private LocalTime acFrzTm;

    @ApiModelProperty(name = "rmk", value = "备注")
    private String rmk;

    @ApiModelProperty(name = "acCurFreezeBal", value = "结算冻结金额")
    private BigDecimal acCurFreezeBal;

    @ApiModelProperty(name = "onChainState", value = "地址链上状态")
    private Object onChainState;

    @ApiModelProperty(name = "priority", value = "使用优先级")
    private Integer priority;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getCoinId() {
        return coinId;
    }

    public void setCoinId(String coinId) {
        this.coinId = coinId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getRealBal() {
        return realBal;
    }

    public void setRealBal(BigDecimal realBal) {
        this.realBal = realBal;
    }

    public BigDecimal getAcUavaBal() {
        return acUavaBal;
    }

    public void setAcUavaBal(BigDecimal acUavaBal) {
        this.acUavaBal = acUavaBal;
    }

    public BigDecimal getAcLastBal() {
        return acLastBal;
    }

    public void setAcLastBal(BigDecimal acLastBal) {
        this.acLastBal = acLastBal;
    }

    public BigDecimal getAcLastUavaBal() {
        return acLastUavaBal;
    }

    public void setAcLastUavaBal(BigDecimal acLastUavaBal) {
        this.acLastUavaBal = acLastUavaBal;
    }

    public String getAcBalTag() {
        return acBalTag;
    }

    public void setAcBalTag(String acBalTag) {
        this.acBalTag = acBalTag;
    }

    public LocalDate getAcUpdDt() {
        return acUpdDt;
    }

    public void setAcUpdDt(LocalDate acUpdDt) {
        this.acUpdDt = acUpdDt;
    }

    public LocalTime getAcUpdTm() {
        return acUpdTm;
    }

    public void setAcUpdTm(LocalTime acUpdTm) {
        this.acUpdTm = acUpdTm;
    }

    public LocalDate getAcFrzDt() {
        return acFrzDt;
    }

    public void setAcFrzDt(LocalDate acFrzDt) {
        this.acFrzDt = acFrzDt;
    }

    public LocalTime getAcFrzTm() {
        return acFrzTm;
    }

    public void setAcFrzTm(LocalTime acFrzTm) {
        this.acFrzTm = acFrzTm;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public BigDecimal getAcCurFreezeBal() {
        return acCurFreezeBal;
    }

    public void setAcCurFreezeBal(BigDecimal acCurFreezeBal) {
        this.acCurFreezeBal = acCurFreezeBal;
    }

    public Object getOnChainState() {
        return onChainState;
    }

    public void setOnChainState(Object onChainState) {
        this.onChainState = onChainState;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}