package com.hisun.lemon.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 数币账户信息响应
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15 17:42
 */
@ApiModel(value = "DmAccountInfoRspDTO", description = "数币账户信息响应")
public class DmAccountInfoRspDTO {

    /**
     * 账户号
     */
    @ApiModelProperty(name = "acNo", value = "账户号")
    private String acNo;

    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    /**
     * 币种logo
     */
    @ApiModelProperty(name = "ccyLogo", value = "币种logo")
    private String ccyLogo;

    /**
     * 账户余额
     */
    @ApiModelProperty(name = "accountBalance", value = "账户余额")
    private BigDecimal accountBalance;

    /**
     * 网络
     */
    @ApiModelProperty(name = "network", value = "网络")
    private String network;

    /**
     * 地址
     */
    @ApiModelProperty(name = "address", value = "地址")
    private String address;

    /**
     * 二维码
     */
    @ApiModelProperty(name = "qrCodeBase64", value = "二维码")
    private String qrCodeBase64;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCcyLogo() {
        return ccyLogo;
    }

    public void setCcyLogo(String ccyLogo) {
        this.ccyLogo = ccyLogo;
    }

    public BigDecimal getAccountBalance() {
        return accountBalance;
    }

    public void setAccountBalance(BigDecimal accountBalance) {
        this.accountBalance = accountBalance;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getQrCodeBase64() {
        return qrCodeBase64;
    }

    public void setQrCodeBase64(String qrCodeBase64) {
        this.qrCodeBase64 = qrCodeBase64;
    }
}
