package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 获取当前用户收款/充值数币账户列表响应DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19 15:48
 */
@ClientValidated
@ApiModel(value = "DmReceiptAccRspDTO", description = "获取当前用户收款/充值数币账户列表响应")
public class DmReceiptAccRspDTO {

    @ApiModelProperty(name = "acNo", value = "账户编号")
    private String acNo;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }
}
