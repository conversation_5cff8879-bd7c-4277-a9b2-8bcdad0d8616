package com.hisun.lemon.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户币种账户查询结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10 14:54
 */
@ApiModel(value = "UserCcyAccountRspDTO",description = "用户币种账户响应")
public class UserCcyAccountRspDTO {
    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    /**
     * 币种logo
     */
    @ApiModelProperty(name = "ccyLogo", value = "币种logo")
    private String ccyLogo;

    /**
     * 账户数量
     */
    @ApiModelProperty(name = "accountCount", value = "账户数量")
    private int accountCount;

    /**
     * 开户银行/网络
     */
    @ApiModelProperty(name = "banksOrNetworks", value = "开户银行/网络")
    private List<String> banksOrNetworks;

    /**
     * 账户总额
     */
    @ApiModelProperty(name = "totalBalance", value = "账户总额")
    private BigDecimal totalBalance;

    /**
     * 可用金额
     */
    @ApiModelProperty(name = "availableBalance", value = "可用金额")
    private BigDecimal availableBalance;

    /**
     * 冻结金额
     */
    @ApiModelProperty(name = "frozenBalance", value = "冻结金额")
    private BigDecimal frozenBalance;

    /**
     * 待入账金额
     */
    @ApiModelProperty(name = "pendingBalance", value = "待入账金额")
    private BigDecimal pendingBalance;

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCcyLogo() {
        return ccyLogo;
    }

    public void setCcyLogo(String ccyLogo) {
        this.ccyLogo = ccyLogo;
    }

    public int getAccountCount() {
        return accountCount;
    }

    public void setAccountCount(int accountCount) {
        this.accountCount = accountCount;
    }

    public List<String> getBanksOrNetworks() {
        return banksOrNetworks;
    }

    public void setBanksOrNetworks(List<String> banksOrNetworks) {
        this.banksOrNetworks = banksOrNetworks;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public BigDecimal getAvailableBalance() {
        return availableBalance;
    }

    public void setAvailableBalance(BigDecimal availableBalance) {
        this.availableBalance = availableBalance;
    }

    public BigDecimal getFrozenBalance() {
        return frozenBalance;
    }

    public void setFrozenBalance(BigDecimal frozenBalance) {
        this.frozenBalance = frozenBalance;
    }

    public BigDecimal getPendingBalance() {
        return pendingBalance;
    }

    public void setPendingBalance(BigDecimal pendingBalance) {
        this.pendingBalance = pendingBalance;
    }
}
