package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账户余额更新请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 18:46
 */
@ClientValidated
@ApiModel(value = "UpdateAccBalReqDTO", description = "更新账户余额请求类")
public class UpdateAccBalReqDTO extends GenericDTO<NoBody> {

    /**
     * 账号
     */
    @ApiModelProperty(name = "acNo", value = "账号")
    @NotEmpty(message = "账号不能为空")
    private String acNo;

    /**
     * 资金属性（1：现金, 8：待结算资金）
     */
    @ApiModelProperty(name = "capTyp", value = "资金属性（1：现金, 8：待结算资金）", allowableValues = "1,8")
    @NotEmpty(message = "资金属性不能为空")
    private String capTyp;

    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种")
    @NotEmpty(message = "币种不能为空")
    private String ccy;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID")
    private String userId;

    /**
     * 账户当前可用余额
     */
    @ApiModelProperty(name = "acCurBal", value = "账户当前可用余额")
    @NotNull(message = "账户当前可用余额不能为空")
    @Min(value = 0, message = "账户当前可用余额不能为负")
    private BigDecimal acCurBal;

    /**
     * 账户当前不可用余额
     */
    @ApiModelProperty(name = "acUavaBal", value = "账户当前不可用余额")
    @NotNull(message = "账户当前不可用余额不能为空")
    @Min(value = 0, message = "账户当前不可用余额不能为负")
    private BigDecimal acUavaBal;

    /**
     * 结算冻结金额
     */
    @ApiModelProperty(name = "acCurFreezeBal", value = "结算冻结金额")
    @NotNull(message = "结算冻结金额不能为空")
    @Min(value = 0, message = "结算冻结金额不能为负")
    private BigDecimal acCurFreezeBal;

    /**
     * 余额tag
     */
    @ApiModelProperty(name = "acBalTag", value = "余额tag")
    private String acBalTag;

    /**
     * 备注
     */
    @ApiModelProperty(name = "rmk", value = "备注")
    private String rmk;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private LocalDateTime modifyTime;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getAcCurBal() {
        return acCurBal;
    }

    public void setAcCurBal(BigDecimal acCurBal) {
        this.acCurBal = acCurBal;
    }

    public BigDecimal getAcUavaBal() {
        return acUavaBal;
    }

    public void setAcUavaBal(BigDecimal acUavaBal) {
        this.acUavaBal = acUavaBal;
    }

    public BigDecimal getAcCurFreezeBal() {
        return acCurFreezeBal;
    }

    public void setAcCurFreezeBal(BigDecimal acCurFreezeBal) {
        this.acCurFreezeBal = acCurFreezeBal;
    }

    public String getAcBalTag() {
        return acBalTag;
    }

    public void setAcBalTag(String acBalTag) {
        this.acBalTag = acBalTag;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}