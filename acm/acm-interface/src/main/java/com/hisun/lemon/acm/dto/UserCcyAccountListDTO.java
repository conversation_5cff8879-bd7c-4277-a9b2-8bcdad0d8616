package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

/**
 * 查询用户币种账户列表请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10 14:55
 */
@ClientValidated
@ApiModel(value = "UserCcyAccountListDTO", description = "用户币种账户列表查询请求")
public class UserCcyAccountListDTO extends GenericDTO<NoBody> {

}
