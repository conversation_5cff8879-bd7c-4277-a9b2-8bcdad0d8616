package com.hisun.lemon.acm.dto;

import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @function AccountingHoldReqDTO
 * @description 预授权冻结请求传输对象
 * @date 7/17/2017 Mon
 * @time 8:03 PM
 */
@ClientValidated
public class AccountingHoldReqDTO {
    /**
     * acNo 交易账号
     */
    @ApiModelProperty(name = "acNo", value = "交易账号", required = true)
    @Length(max = 36)
    @NotBlank(message = ACMMessageCode.PARAM_IS_NULL)
    private String acNo;
    /**
     * capTyp 账户资金属性
     */
    @ApiModelProperty(name = "capTyp", value = "资金类型 1:现金，8:待结算", required = true)
    @Length(max = 1)
    @NotBlank(message = ACMMessageCode.PARAM_IS_NULL)
    private String capTyp;

    /**
     * ccy 交易币种
     */
    @ApiModelProperty(name = "ccy", value = "币种，默认USD")
    @Length(max = 10)
    private String ccy;
    /**
     * userId 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID")
    @Length(max = 16)
    private String userId;
    /**
     * hldCd 冻结代码PY-预授权消费冻结TR-待确认转账冻结TX-提现冻结FP-发票红字质押冻结(只有一笔明细)OT-其他冻结
     */
    @ApiModelProperty(name = "hldCd", value = "冻结代码PY-预授权消费冻结TR-待确认转账冻结TX-提现冻结FP-发票红字质押冻结(只有一笔明细)OT-其他冻结", required = true)
    @Length(max = 2)
    @NotBlank(message = ACMMessageCode.PARAM_IS_NULL)
    private String hldCd;
    /**
     * hldBal 冻结余额
     */
    @ApiModelProperty(name = "hldBal", value = "冻结余额", required = true)
    @NotNull(message = ACMMessageCode.PARAM_IS_NULL)
    private BigDecimal hldBal;
    /**
     * expDt 冻结失效日期
     */
    @ApiModelProperty(name = "expDt", value = "冻结失效日期", required = true)
    @NotNull(message = ACMMessageCode.PARAM_IS_NULL)
    private LocalDate expDt;
    /**
     * expTm 冻结失效时间
     */
    @ApiModelProperty(name = "expTm", value = "冻结失效时间", required = true)
    @NotNull(message = ACMMessageCode.PARAM_IS_NULL)
    private LocalTime expTm;
    /**
     * dueProcMod 到期处理方式0：到期释放；1：不自动释放
     */
    @ApiModelProperty(name = "dueProcMod", value = "到期处理方式0：到期释放；1：不自动释放", required = true)
    @Length(max = 1)
    @NotBlank(message = ACMMessageCode.PARAM_IS_NULL)
    private String dueProcMod;
    /**
     * ordTyp 订单种类--TX_TYP
     */
    @ApiModelProperty(name = "ordTyp", value = "订单类型")
    private String ordTyp;
    /**
     * ordNo 订单编号
     */
    @ApiModelProperty(name = "ordNo", value = "订单编号")
    private String ordNo;
    /**
     * rmk 备注
     */
    @ApiModelProperty(name = "rmk", value = "备注")
    private String rmk;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getHldCd() {
        return hldCd;
    }

    public void setHldCd(String hldCd) {
        this.hldCd = hldCd;
    }

    public BigDecimal getHldBal() {
        return hldBal;
    }

    public void setHldBal(BigDecimal hldBal) {
        this.hldBal = hldBal;
    }

    public LocalDate getExpDt() {
        return expDt;
    }

    public void setExpDt(LocalDate expDt) {
        this.expDt = expDt;
    }

    public LocalTime getExpTm() {
        return expTm;
    }

    public void setExpTm(LocalTime expTm) {
        this.expTm = expTm;
    }

    public String getDueProcMod() {
        return dueProcMod;
    }

    public void setDueProcMod(String dueProcMod) {
        this.dueProcMod = dueProcMod;
    }

    public String getOrdTyp() {
        return ordTyp;
    }

    public void setOrdTyp(String ordTyp) {
        this.ordTyp = ordTyp;
    }

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }
}
