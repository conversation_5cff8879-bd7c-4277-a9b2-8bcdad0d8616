package com.hisun.lemon.acm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 数币账户收款/充值记录列表响应传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 10:18
 */
@ClientValidated
@ApiModel(value = "DmAccountReceiptsRspDTO", description = "数币账户收款/充值记录列表响应传输对象")
public class DmAccountReceiptsRspDTO {

    /**
     * 收款/充值记录列表
     */
    @ApiModelProperty(name = "records", value = "收款/充值记录列表")
    private List<ReceiptRecord> records;

    /**
     * 总记录数
     */
    @ApiModelProperty(name = "totalRecords", value = "总记录数")
    private long totalRecords;

    /**
     * 总页数
     */
    @ApiModelProperty(name = "totalPages", value = "总页数")
    private int totalPages;

    /**
     * 当前页码
     */
    @ApiModelProperty(name = "pageNo", value = "当前页码")
    private int pageNo;

    /**
     * 每页记录数
     */
    @ApiModelProperty(name = "pageSize", value = "每页记录数")
    private int pageSize;

    public long getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(long totalRecords) {
        this.totalRecords = totalRecords;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<ReceiptRecord> getRecords() {
        return records;
    }

    public void setRecords(List<ReceiptRecord> records) {
        this.records = records;
    }
}
