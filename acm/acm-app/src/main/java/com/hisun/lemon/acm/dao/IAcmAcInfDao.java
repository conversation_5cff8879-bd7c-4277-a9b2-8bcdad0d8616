/*
 * @ClassName IAcmAcInfDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmAcInfDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface IAcmAcInfDao extends BaseDao<AcmAcInfDO> {
    /**
     * 查询账户信息
     */
    public AcmAcInfDO get(Map map);

    /**
     * 删除账户信息
     */
    public int delete(Map map);

    /**
     * 根据条件查询账户
     */
    AcmAcInfDO queryAcc(@Param("userId") String userId, @Param("acSts") String acSts, @Param("bank") String bank, @Param("acNo") String acNo);

    /**
     * 查询用户是否已有该币种账户
     */
    AcmAcInfDO queryAccByCcy(@Param("userId") String userId, @Param("ccy") String ccy);
}