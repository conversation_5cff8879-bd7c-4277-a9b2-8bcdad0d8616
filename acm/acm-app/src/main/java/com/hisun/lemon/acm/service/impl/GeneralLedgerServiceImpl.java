package com.hisun.lemon.acm.service.impl;

import com.hisun.lemon.acm.bo.LedgerBalanceBo;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.dao.*;
import com.hisun.lemon.acm.entity.*;
import com.hisun.lemon.acm.service.IAccountManagementService;
import com.hisun.lemon.acm.service.IGeneralLedgerService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function GeneralLedgerServiceImpl
 * @description 总账服务业务实现
 * @date 7/15/2017 Sat
 * @time 12:19 PM
 */
@Service
@Transactional
public class GeneralLedgerServiceImpl extends BaseService implements IGeneralLedgerService {
    private final static Logger logger = LoggerFactory.getLogger(GeneralLedgerServiceImpl.class);

    @Resource
    private IAcmCapDetailDao iAcmCapDetailDao;

    @Resource
    private IAcmItmDetailSumDao iAcmItmDetailSumDao;

    @Resource
    private IAcmItmBalDao iAcmItmBalDao;

    @Resource
    private IAcmTotChkInfDao iAcmTotChkInfDao;

    @Resource
    private IAcmVoucherSumDao iAcmVoucherSumDao;

    @Resource
    private IAcmItmPropertyDao iAcmItmPropertyDao;

    @Resource
    private IAcmAcBalDao iAcmAcBalDao;

    @Resource
    private IAcmItmInfDao iAcmItmInfDao;

    @Resource
    private IAccountManagementService managementService;


    /**
     * 导出每日资金明细
     *
     * @return
     */
    @Override
    public void exportCapitalDetail(LocalDate acDt) {
        //清理资金明细表
        try {
            iAcmCapDetailDao.dropCapDetail();
            iAcmCapDetailDao.createCapDetail();
            iAcmCapDetailDao.initCapDetail(acDt);
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }
    }

    /**
     * 更新科目余额
     *
     * @return
     */
    @Override
    public void updateItemBalance(LocalDate acDt) {
        //科目资金明细汇总表初始化
        AcmItmDetailSumDO acmItmDetailSumDO = new AcmItmDetailSumDO();
        iAcmItmDetailSumDao.updateItmDetailSum(acDt);
        List<AcmItmDetailSumDO> itmDetailSumDOS = iAcmItmDetailSumDao.find(acDt);

        //更新科目余额信息
        for (AcmItmDetailSumDO itmDetailSum : itmDetailSumDOS) {
            AcmItmBalDO acmItmBalDO = new AcmItmBalDO();
            acmItmBalDO = iAcmItmBalDao.get(itmDetailSum.getItmNo());
            if (JudgeUtils.isNull(acmItmBalDO)) {
                managementService.createItemAccountBalance(itmDetailSum.getItmNo());
                acmItmBalDO = iAcmItmBalDao.get(itmDetailSum.getItmNo());
            }
            BigDecimal tdDrBal = acmItmBalDO.getTdDrBal();
            BigDecimal tdCrBal = acmItmBalDO.getTdCrBal();
            BigDecimal lastDrBal = acmItmBalDO.getLastDrBal();
            BigDecimal lastCrBal = acmItmBalDO.getLastCrBal();
            String balDrt = acmItmBalDO.getBalDrt();
            LocalDate uppDt = acmItmBalDO.getUppDt();
            LocalDate curAcDt = LemonUtils.getAccDate();
            //判断当前会计日是否为月初1号，是当月进行累计，否则当月数据初始化
            if (acDt.getDayOfMonth() == 1) {
                //初始化月数据
                acmItmBalDO.setMonCrAmt(itmDetailSum.getTdCrAmt());
                acmItmBalDO.setMonDrAmt(itmDetailSum.getTdDrAmt());
                acmItmBalDO.setMonCrNum(itmDetailSum.getTdCrNum());
                acmItmBalDO.setMonDrNum(itmDetailSum.getTdDrNum());
            } else {
                acmItmBalDO.setMonCrNum(acmItmBalDO.getMonCrNum() + itmDetailSum.getTdCrNum());
                acmItmBalDO.setMonDrNum(acmItmBalDO.getMonDrNum() + itmDetailSum.getTdDrNum());
                acmItmBalDO.setMonCrAmt(acmItmBalDO.getMonCrAmt().add(itmDetailSum.getTdCrAmt()));
                acmItmBalDO.setMonDrAmt(acmItmBalDO.getMonDrAmt().add(itmDetailSum.getTdDrAmt()));
            }
            if (StringUtils.equals(acmItmBalDO.getUpdBalFlg(), ACMConstants.BAL_BAT_UPD)) {
                List<BigDecimal> lastBalList = new ArrayList<>();
                lastBalList.add(lastCrBal);
                lastBalList.add(lastDrBal);
                lastBalList.add(itmDetailSum.getTdCrAmt());
                lastBalList.add(itmDetailSum.getTdDrAmt());
                List<BigDecimal> tdBalList = new ArrayList<>();
                tdBalList.add(tdCrBal);
                tdBalList.add(tdDrBal);
                tdBalList.add(itmDetailSum.getTdCrAmt());
                tdBalList.add(itmDetailSum.getTdDrAmt());
                Map<String, BigDecimal> tdBalMap = computeBalance(balDrt, tdBalList);
                Map<String, BigDecimal> lastBalMap = computeBalance(balDrt, lastBalList);
                BigDecimal tdDrBalNew = tdBalMap.get("drBal");
                BigDecimal tdCrBalNew = tdBalMap.get("crBal");
                lastCrBal = lastBalMap.get("crBal");
                lastDrBal = lastBalMap.get("drBal");
                if (curAcDt.compareTo(uppDt) > 0) {
                    acmItmBalDO.setLastDrBal(tdDrBal);
                    acmItmBalDO.setLastCrBal(tdCrBal);
                    acmItmBalDO.setTdDrBal(tdDrBalNew);
                    acmItmBalDO.setTdDrBal(tdCrBalNew);
                } else if (curAcDt.compareTo(uppDt) < 0) {
                    acmItmBalDO.setLastCrBal(lastCrBal);
                    acmItmBalDO.setLastDrBal(lastDrBal);
                } else {
                    acmItmBalDO.setTdCrBal(tdCrBalNew);
                    acmItmBalDO.setTdDrBal(tdDrBalNew);
                }
            }
            acmItmBalDO.setTdCrAmt(itmDetailSum.getTdCrAmt());
            acmItmBalDO.setTdDrAmt(itmDetailSum.getTdDrAmt());
            acmItmBalDO.setTdDrNum(itmDetailSum.getTdDrNum());
            acmItmBalDO.setTdCrNum(itmDetailSum.getTdCrNum());
            acmItmBalDO.setUppDt(curAcDt);
            iAcmItmBalDao.update(acmItmBalDO);
            AcmItmInfDO acmItmInfDO = new AcmItmInfDO();
            if (JudgeUtils.isNotBlank(acmItmBalDO.getUpItmNo())) {
                acmItmInfDO = iAcmItmInfDao.get(acmItmBalDO.getUpItmNo());
                if (JudgeUtils.isNotNull(acmItmInfDO)) {
                    if (JudgeUtils.isNotBlank(acmItmBalDO.getUpItmNo())) {
                        updateGeneralLedger(acDt, acmItmBalDO.getUpItmNo(), acmItmBalDO);
                    }
                }
            }
        }
    }

    private Map<String, BigDecimal> computeBalance(String balDrt, List<BigDecimal> amtList) {
        BigDecimal crBal = amtList.get(0);
        BigDecimal drBal = amtList.get(1);
        BigDecimal crAmt = amtList.get(2);
        BigDecimal drAmt = amtList.get(3);
        if (JudgeUtils.equals(balDrt, ACMConstants.BAL_DRT_C)) {
            crBal = crBal.add(crAmt).subtract(drAmt);
        } else if (JudgeUtils.equals(balDrt, ACMConstants.BAL_DRT_D)) {
            drBal = drBal.add(drAmt).subtract(crAmt);
        } else if (JudgeUtils.equals(balDrt, ACMConstants.BAL_DRT_A)) {
            crBal = crBal.add(crAmt);
            drBal = drBal.add(drAmt);
        } else {
            if (drAmt.compareTo(crAmt) > 0) {
                drBal = drBal.add(drAmt).subtract(crAmt);
            } else if (drAmt.compareTo(crAmt) < 0) {
                crBal = crBal.add(crAmt).subtract(drAmt);
            } else {
                //do nothing
            }
        }
        Map<String, BigDecimal> balMap = new HashMap<>();
        balMap.put("crBal", crBal);
        balMap.put("drBal", drBal);
        return balMap;
    }

    /**
     * 更新上级科目余额，直到更新到顶层科目
     *
     * @return
     */
    public void updateGeneralLedger(LocalDate acDt, String upItmNo, AcmItmBalDO acmItmBalDO) {
        AcmItmBalDO upItmBal = iAcmItmBalDao.get(upItmNo);
        if (JudgeUtils.isNull(upItmBal)) {
            managementService.createItemAccountBalance(acmItmBalDO.getUpItmNo());
            upItmBal = iAcmItmBalDao.get(acmItmBalDO.getUpItmNo());
        }
        //判断当前会计日是否为月初1号，是当月进行累计，否则当月数据初始化
        int monCrNum = 0;
        int monDrNum = 0;
        BigDecimal monCrAmt = BigDecimal.valueOf(0, 2);
        BigDecimal monDrAmt = BigDecimal.valueOf(0, 2);
        if (acDt.getDayOfMonth() != 1) {
            //初始化月数据
            monCrAmt = upItmBal.getMonCrAmt();
            monDrAmt = upItmBal.getMonDrAmt();
            monCrNum = upItmBal.getMonCrNum();
            monDrNum = upItmBal.getMonDrNum();
        }
        upItmBal.setMonCrNum(monCrNum + acmItmBalDO.getTdCrNum());
        upItmBal.setMonDrNum(monDrNum + acmItmBalDO.getTdDrNum());
        upItmBal.setMonCrAmt(monCrAmt.add(acmItmBalDO.getTdCrAmt()));
        upItmBal.setMonDrAmt(monDrAmt.add(acmItmBalDO.getTdDrAmt()));
        upItmBal.setLastDrBal(upItmBal.getTdDrBal());
        upItmBal.setLastCrBal(upItmBal.getTdCrBal());
        String balDrt = upItmBal.getBalDrt();
        List<BigDecimal> balList= new ArrayList<>();
        balList.add(upItmBal.getTdCrBal());
        balList.add(upItmBal.getTdDrBal());
        balList.add(acmItmBalDO.getTdCrAmt());
        balList.add(acmItmBalDO.getTdDrAmt());
        BigDecimal tdCrBal = computeBalance(balDrt, balList).get("crBal");
        BigDecimal tdDrBal = computeBalance(balDrt, balList).get("drBal");
        upItmBal.setTdCrBal(tdCrBal);
        upItmBal.setTdDrBal(tdDrBal);
        upItmBal.setTdCrAmt(upItmBal.getTdCrAmt().add(acmItmBalDO.getTdCrAmt()));
        upItmBal.setTdDrAmt(upItmBal.getTdDrAmt().add(acmItmBalDO.getTdDrAmt()));
        upItmBal.setTdDrNum(upItmBal.getTdDrNum() + acmItmBalDO.getTdDrNum());
        upItmBal.setTdCrNum(upItmBal.getTdCrNum() + acmItmBalDO.getTdCrNum());
        upItmBal.setUppDt(LemonUtils.getAccDate());
        iAcmItmBalDao.update(upItmBal);
        if (StringUtils.isNotBlank(upItmBal.getUpItmNo())) {
            updateGeneralLedger(acDt, upItmBal.getUpItmNo(), acmItmBalDO);
        }
    }

    /**
     * 稽核总账(总分核对)
     *
     * @return
     */
    @Override
    public void checkGeneralLedger(LocalDate acDt) {
        List<LedgerBalanceBo> ledgerBalList = iAcmAcBalDao.getLedgerBal(acDt);
        //根据用户账户对应的底层科目计算分户贷方余额和分户借方余额
        for (LedgerBalanceBo ledgerBal : ledgerBalList ) {
            AcmTotChkInfDO acmTotChkInfDO = new AcmTotChkInfDO();
            AcmItmProperty acmItmProperty;
            String capTyp = ledgerBal.getCapTyp();
            String ccy = ledgerBal.getCcy();
            acmItmProperty = iAcmItmPropertyDao.get(capTyp, ccy);
            if (JudgeUtils.isNull(acmItmProperty)) {
                LemonException.throwLemonException(ACMMessageCode.ITM_NOT_EXIST);
            }
            String itmNo = acmItmProperty.getItmNo();
            acmTotChkInfDO = iAcmTotChkInfDao.get(itmNo, acDt);
            if (JudgeUtils.isNull(acmTotChkInfDO)) {
                AcmItmBalDO acmItmBalDO = iAcmItmBalDao.get(itmNo);
                acmTotChkInfDO = new AcmTotChkInfDO();
                acmTotChkInfDO.setAcDt(acDt);
                acmTotChkInfDO.setCcy(ccy);
                acmTotChkInfDO.setItmNo(itmNo);
                acmTotChkInfDO.setGlCrBl(acmItmBalDO.getTdCrBal());
                acmTotChkInfDO.setGlDrBl(acmItmBalDO.getTdDrBal());
                acmTotChkInfDO.setBalDrt(acmItmBalDO.getBalDrt());
                if (StringUtils.equals(acmItmProperty.getBalDrt(), ACMConstants.BAL_DRT_C)) {
                    acmTotChkInfDO.setLdgCrBal(ledgerBal.getTotBal());
                    acmTotChkInfDO.setLdgDrBal(BigDecimal.valueOf(0, 2));
                } else {
                    acmTotChkInfDO.setLdgDrBal(ledgerBal.getTotBal());
                    acmTotChkInfDO.setLdgCrBal(BigDecimal.valueOf(0, 2));
                }
                //分户贷方余额和分户的借方余额，与科目余额更新后的当日贷方余额，借方余额对比，相等核对正确，不对，核对错误
                if (acmTotChkInfDO.getGlCrBl().compareTo(acmTotChkInfDO.getLdgCrBal()) != 0
                        || acmTotChkInfDO.getGlDrBl().compareTo(acmTotChkInfDO.getLdgDrBal()) != 0) {
                    acmTotChkInfDO.setChkRsl(ACMConstants.GENERAL_LEDGER_CHK_RSL_FALSE);
                } else {
                    acmTotChkInfDO.setChkRsl(ACMConstants.GENERAL_LEDGER_CHK_RSL_CORRECT);
                }
                iAcmTotChkInfDao.insert(acmTotChkInfDO);
            } else {
                if (StringUtils.equals(acmItmProperty.getBalDrt(), ACMConstants.BAL_DRT_C)) {
                    acmTotChkInfDO.setLdgCrBal(acmTotChkInfDO.getLdgCrBal().add(ledgerBal.getTotBal()));
                } else {
                    acmTotChkInfDO.setLdgDrBal(acmTotChkInfDO.getLdgDrBal().add(ledgerBal.getTotBal()));
                }
                if (acmTotChkInfDO.getGlCrBl().compareTo(acmTotChkInfDO.getLdgCrBal()) != 0
                        || acmTotChkInfDO.getGlDrBl().compareTo(acmTotChkInfDO.getLdgDrBal()) != 0) {
                    acmTotChkInfDO.setChkRsl(ACMConstants.GENERAL_LEDGER_CHK_RSL_FALSE);
                } else {
                    acmTotChkInfDO.setChkRsl(ACMConstants.GENERAL_LEDGER_CHK_RSL_CORRECT);
                }
                iAcmTotChkInfDao.update(acmTotChkInfDO);
            }
        }
    }

    /**
     * 检查传票平衡
     *
     * @return
     */
    @Override
    public void checkAllVouchers(LocalDate acDt) {
        iAcmVoucherSumDao.initVoucher();
    }

    /**
     * 科目数据清理
     *
     * @return
     */
    @Override
    public void clearItemAccountData(LocalDate acDt) {
        //to-do
    }
}
