/*
 * @ClassName AcmItmInfDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDate;

public class AcmItmInfDO extends BaseDO {
    /**
     * itmNo 科目号
     */
    private String itmNo;
    /**
     * itmEnm 科目英文名称
     */
    private String itmEnm;
    /**
     * itmCnm 科目中文名称
     */
    private String itmCnm;
    /**
     * itmLvl 科目级别（1：一级科目、2：二级科目、3：三级科目）
     */
    private Integer itmLvl;
    /**
     * upItmNo 上级科目号
     */
    private String upItmNo;
    /**
     * btmItmFlg 最底层科目标（Y：最底层、N：非最底层）
     */
    private String btmItmFlg;
    /**
     * itmTyp 科目类别（A-资产类、L-负债类、C-所有者权益类、I-收入类、E-支出类、O-表外类、S-往来账）
     */
    private String itmTyp;
    /**
     * itmCls 科目分类（1-存放银行资金池账户、2-差错/争议挂账账户、3-其他内部账户）
     */
    private String itmCls;
    /**
     * balOdFlg 余额是否允许透支标识
     */
    private String balOdFlg;
    /**
     * balDrt 余额方向（A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）
     */
    private String balDrt;
    /**
     * updBalFlg 余额更新方式0：实时更新1：批量更新
     */
    private String updBalFlg;
    /**
     * itmSts 科目状态（1-生效、0-失效）
     */
    private String itmSts;
    /**
     * effDt 生效日期
     */
    private LocalDate effDt;
    /**
     * expDt 失效日期
     */
    private LocalDate expDt;
    /**
     * updOpr 更新操作员
     */
    private String updOpr;

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public String getItmEnm() {
        return itmEnm;
    }

    public void setItmEnm(String itmEnm) {
        this.itmEnm = itmEnm;
    }

    public String getItmCnm() {
        return itmCnm;
    }

    public void setItmCnm(String itmCnm) {
        this.itmCnm = itmCnm;
    }

    public Integer getItmLvl() {
        return itmLvl;
    }

    public void setItmLvl(Integer itmLvl) {
        this.itmLvl = itmLvl;
    }

    public String getUpItmNo() {
        return upItmNo;
    }

    public void setUpItmNo(String upItmNo) {
        this.upItmNo = upItmNo;
    }

    public String getBtmItmFlg() {
        return btmItmFlg;
    }

    public void setBtmItmFlg(String btmItmFlg) {
        this.btmItmFlg = btmItmFlg;
    }

    public String getItmTyp() {
        return itmTyp;
    }

    public void setItmTyp(String itmTyp) {
        this.itmTyp = itmTyp;
    }

    public String getItmCls() {
        return itmCls;
    }

    public void setItmCls(String itmCls) {
        this.itmCls = itmCls;
    }

    public String getBalOdFlg() {
        return balOdFlg;
    }

    public void setBalOdFlg(String balOdFlg) {
        this.balOdFlg = balOdFlg;
    }

    public String getBalDrt() {
        return balDrt;
    }

    public void setBalDrt(String balDrt) {
        this.balDrt = balDrt;
    }

    public String getUpdBalFlg() {
        return updBalFlg;
    }

    public void setUpdBalFlg(String updBalFlg) {
        this.updBalFlg = updBalFlg;
    }

    public String getItmSts() {
        return itmSts;
    }

    public void setItmSts(String itmSts) {
        this.itmSts = itmSts;
    }

    public LocalDate getEffDt() {
        return effDt;
    }

    public void setEffDt(LocalDate effDt) {
        this.effDt = effDt;
    }

    public LocalDate getExpDt() {
        return expDt;
    }

    public void setExpDt(LocalDate expDt) {
        this.expDt = expDt;
    }

    public String getUpdOpr() {
        return updOpr;
    }

    public void setUpdOpr(String updOpr) {
        this.updOpr = updOpr;
    }
}