/*
 * @ClassName AcmVoucherSumDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;

public class AcmVoucherSumDO extends BaseDO {
    /**
     * vchNo 传票号
     */
    private String vchNo;
    /**
     * acDt 记账日期
     */
    private LocalDate acDt;
    /**
     * ccy 币种
     */
    private String ccy;
    /**
     * totDrAmt 累计支出金额
     */
    private BigDecimal totDrAmt;
    /**
     * totCrAmt 累计收入金额
     */
    private BigDecimal totCrAmt;
    /**
     * netAmt 差额
     */
    private BigDecimal netAmt;
    /**
     * rmk 备注
     */
    private String rmk;

    public String getVchNo() {
        return vchNo;
    }

    public void setVchNo(String vchNo) {
        this.vchNo = vchNo;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getTotDrAmt() {
        return totDrAmt;
    }

    public void setTotDrAmt(BigDecimal totDrAmt) {
        this.totDrAmt = totDrAmt;
    }

    public BigDecimal getTotCrAmt() {
        return totCrAmt;
    }

    public void setTotCrAmt(BigDecimal totCrAmt) {
        this.totCrAmt = totCrAmt;
    }

    public BigDecimal getNetAmt() {
        return netAmt;
    }

    public void setNetAmt(BigDecimal netAmt) {
        this.netAmt = netAmt;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }
}