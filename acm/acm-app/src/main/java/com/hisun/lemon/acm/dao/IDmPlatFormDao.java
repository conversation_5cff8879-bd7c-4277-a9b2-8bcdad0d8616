package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.DmPlatFormDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IDmPlatFormDao extends BaseDao<DmPlatFormDO> {

    DmPlatFormDO getByPlatformNo(@Param("platformAcNo") String platformAcNo);

    List<DmPlatFormDO> findAll();
}
