/*
 * @ClassName IAcmAcDetailDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmAcDetailDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.List;

@Mapper
public interface IAcmAcDetailDao extends BaseDao<AcmAcDetailDO> {

    /**
     * 查询最大的交易序号
     */
    public int getJrnSeq(String txJrnNo);

    /**
     * 冲正，更新原交易状态
     */
    public int updateOriginalTransaction(AcmAcDetailDO acmAcDetailDO);

    /**
     * 查询原交易信息
     */
    public List<AcmAcDetailDO> find(String txJrnNo);

    /**
     * 查询商户账户明细
     */
    public List<AcmAcDetailDO> getTranList(@Param("acNo")String acNo, @Param("acDt")LocalDate acDt);
}