/*
 * @ClassName IAcmItmDetailDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmItmDetailDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IAcmItmDetailDao extends BaseDao<AcmItmDetailDO> {
    /**
     * 查询最大交易序号
     */
    public int getJrnSeq(String txJrnNo);
    /**
     * 查询科目收支明细
     */
    public List<AcmItmDetailDO> find(AcmItmDetailDO acmItmDetailDO);

    /**
     *  冲正，更新原交易状态
     */
    public int updateOriginalTransaction(AcmItmDetailDO acmItmDetailDO);

    /**
     * 新增科目收支明细
     */
    public int insert(AcmItmDetailDO acmItmDetailDO);
}