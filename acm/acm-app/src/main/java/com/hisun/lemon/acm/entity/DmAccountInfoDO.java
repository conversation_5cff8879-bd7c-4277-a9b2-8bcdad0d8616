package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;

/**
 * 数币账户信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/11 19:39
 */
public class DmAccountInfoDO extends BaseDO {
    /**
     * id 内部自增ID
     */
    private Long id;

    /**
     * accountId 账户唯一ID
     */
    private String accountId;

    /**
     * vaultCode 所属金库编码
     */
    private String vaultCode;

    /**
     * groupCode 所属账户组编码
     */
    private String groupCode;

    /**
     * accountName 账户名称
     */
    private String accountName;

    /**
     * accountType 账户类型（PS:平台, MA:商户）
     */
    private String accountType;

    /**
     * remark 账户备注
     */
    private String remark;

    /**
     * status 账户状态（ACTIVE:活跃, FROZEN:冻结, CLOSED:关闭）
     */
    private String status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
