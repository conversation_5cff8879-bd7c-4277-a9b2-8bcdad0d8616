package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;

/**
 * 数币-平台支持币种信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/12 9:57
 */
public class DmCcyListDO extends BaseDO {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 币种ID（如：ETH）
     */
    private String coinId;

    /**
     * 币种单位（如：eth）
     */
    private String unit;

    /**
     * 币种图标URL
     */
    private String coinIconUrl;

    /**
     * 显示精度位数
     */
    private Integer prec;

    /**
     * 币种类型编码
     */
    private String coinType;

    /**
     * 区块链精度位数
     */
    private Integer decimals;

    /**
     * 所属系列（如：EVM）
     */
    private String series;

    /**
     * 是否为代币（0:否,1:是）
     */
    private Integer tokenFlag;

    /**
     * 链上币种ID（如：ETH-SEPOLIA）
     */
    private String coinIdOnChain;

    /**
     * 主币ID（如：EVM）
     */
    private String mainCoinId;

    /**
     * 所属网络（如：ethereum-sepolia）
     */
    private String network;

    /**
     * 附加信息
     */
    private String appendix;

    /**
     * 签名算法（如：ECDSA）
     */
    private String algorithm;

    /**
     * 提现手续费
     */
    private BigDecimal withdrawingFee;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCoinId() {
        return coinId;
    }

    public void setCoinId(String coinId) {
        this.coinId = coinId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCoinIconUrl() {
        return coinIconUrl;
    }

    public void setCoinIconUrl(String coinIconUrl) {
        this.coinIconUrl = coinIconUrl;
    }

    public Integer getPrec() {
        return prec;
    }

    public void setPrec(Integer prec) {
        this.prec = prec;
    }

    public String getCoinType() {
        return coinType;
    }

    public void setCoinType(String coinType) {
        this.coinType = coinType;
    }

    public Integer getDecimals() {
        return decimals;
    }

    public void setDecimals(Integer decimals) {
        this.decimals = decimals;
    }

    public String getSeries() {
        return series;
    }

    public void setSeries(String series) {
        this.series = series;
    }

    public Integer getTokenFlag() {
        return tokenFlag;
    }

    public void setTokenFlag(Integer tokenFlag) {
        this.tokenFlag = tokenFlag;
    }

    public String getCoinIdOnChain() {
        return coinIdOnChain;
    }

    public void setCoinIdOnChain(String coinIdOnChain) {
        this.coinIdOnChain = coinIdOnChain;
    }

    public String getMainCoinId() {
        return mainCoinId;
    }

    public void setMainCoinId(String mainCoinId) {
        this.mainCoinId = mainCoinId;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getAppendix() {
        return appendix;
    }

    public void setAppendix(String appendix) {
        this.appendix = appendix;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public BigDecimal getWithdrawingFee() {
        return withdrawingFee;
    }

    public void setWithdrawingFee(BigDecimal withdrawingFee) {
        this.withdrawingFee = withdrawingFee;
    }
}
