/*
 * @ClassName AcmItmDetailSumDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;

public class AcmItmDetailSumDO extends BaseDO {
    /**
     * acDt 会计日期
     */
    private LocalDate acDt;
    /**
     * itmNo 科目号
     */
    private String itmNo;
    /**
     * ccy 币种
     */
    private String ccy;
    /**
     * tdDrAmt 当日借方发生额
     */
    private BigDecimal tdDrAmt;
    /**
     * tdCrAmt 当日贷方发生额
     */
    private BigDecimal tdCrAmt;
    /**
     * tdDrNum 当日借方交易笔数
     */
    private Integer tdDrNum;
    /**
     * tdCrNum 当日贷方交易笔数
     */
    private Integer tdCrNum;

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getTdDrAmt() {
        return tdDrAmt;
    }

    public void setTdDrAmt(BigDecimal tdDrAmt) {
        this.tdDrAmt = tdDrAmt;
    }

    public BigDecimal getTdCrAmt() {
        return tdCrAmt;
    }

    public void setTdCrAmt(BigDecimal tdCrAmt) {
        this.tdCrAmt = tdCrAmt;
    }

    public Integer getTdDrNum() {
        return tdDrNum;
    }

    public void setTdDrNum(Integer tdDrNum) {
        this.tdDrNum = tdDrNum;
    }

    public Integer getTdCrNum() {
        return tdCrNum;
    }

    public void setTdCrNum(Integer tdCrNum) {
        this.tdCrNum = tdCrNum;
    }
}