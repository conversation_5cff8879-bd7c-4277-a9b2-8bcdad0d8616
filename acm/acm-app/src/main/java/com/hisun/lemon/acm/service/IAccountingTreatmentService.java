package com.hisun.lemon.acm.service;

import com.hisun.lemon.acm.bo.AccountingBO;
import com.hisun.lemon.acm.entity.AcmAcHoldDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @function IAccountingTreatmentService
 * @description 账务处理服务
 * @date 7/13/2017 Thu
 * @time 11:43 AM
 */
public interface IAccountingTreatmentService {
    /**
     * 账务处理
     * @param accountingBOList
     * @return
     */
    public void accountingTreatment(List<AccountingBO> accountingBOList);

    /**
     * 冻结用户账户的部分余额
     * @param acmAcHoldDO
     * @return
     */
    public String holdUserAccountBalance(AcmAcHoldDO acmAcHoldDO);

    /**
     * 解冻用户的冻结余额
     * @param holdNo
     * @return
     */
    public void unHoldUserAccountBalance(String holdNo);
}
