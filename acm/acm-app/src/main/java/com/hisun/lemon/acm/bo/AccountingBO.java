package com.hisun.lemon.acm.bo;

import com.hisun.lemon.framework.validation.ClientValidated;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @function AccountingReqDTO
 * @description 多借多贷账务请求处理传输对象
 * @date 7/17/2017 Mon
 * @time 6:54 PM
 */
@ClientValidated
public class AccountingBO {
    /**
     * jrnNo 交易流水号
     */
    private String jrnNo;

    /**
     * jrnSeq 交易流水序号
     */
    private String jrnSeq;

    /**
     * txSts 交易状态 N:正常 R:撤销 C:冲正  撤销暂不考虑
     */
    @NotEmpty
    private String txSts;
    /**
     * acTyp  账户类型 U:用户 I：科目 DO对象不存在此域
     */
    @NotEmpty
    private String acTyp;
    /**
     * acNo 交易账号
     */
    private String acNo;
    /**
     * capTyp 账户资金属性
     */
    private String capTyp;
    /**
     * itmNo 科目号
     */
    private String itmNo;
    /**
     * txTyp 交易类型
     */
    @NotEmpty
    private String txTyp;
    /**
     * txAmt 交易金额
     */
    @NotEmpty
    private BigDecimal txAmt;
    /**
     * dcFlg 借贷标志
     */
    @NotEmpty
    private String dcFlg;
    /**
     * txJrnNo 请求唯一流水号(堵重)
     */
    @NotEmpty
    private String txJrnNo;
    /**
     * txOrdNo 请求订单号
     */
    @NotEmpty
    private String txOrdNo;
    /**
     * txOrdDt 请求订单日期
     */
    @NotEmpty
    private LocalDate txOrdDt;
    /**
     * txOrdTm 请求订单时间
     */
    @NotEmpty
    private LocalTime txOrdTm;
    /**
     * oppAcNo 账号
     */
    private String oppAcNo;
    /**
     * oppCapTyp 资金属性
     */
    private String oppCapTyp;
    /**
     * oppUserId 用户号
     */
    private String oppUserId;
    /**
     * oppUserTyp 用户类型
     */
    private String oppUserTyp;
    /**
     * usrIpAdr 用户IP地址
     */
    private String usrIpAdr;
    /**
     * rmk 备注
     */
    private String rmk;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getJrnSeq() {
        return jrnSeq;
    }

    public void setJrnSeq(String jrnSeq) {
        this.jrnSeq = jrnSeq;
    }

    public String getTxSts() {
        return txSts;
    }

    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    public String getAcTyp() {
        return acTyp;
    }

    public void setAcTyp(String acTyp) {
        this.acTyp = acTyp;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public BigDecimal getTxAmt() {
        return txAmt;
    }

    public void setTxAmt(BigDecimal txAmt) {
        this.txAmt = txAmt;
    }

    public String getDcFlg() {
        return dcFlg;
    }

    public void setDcFlg(String dcFlg) {
        this.dcFlg = dcFlg;
    }

    public String getTxJrnNo() {
        return txJrnNo;
    }

    public void setTxJrnNo(String txJrnNo) {
        this.txJrnNo = txJrnNo;
    }

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public LocalDate getTxOrdDt() {
        return txOrdDt;
    }

    public void setTxOrdDt(LocalDate txOrdDt) {
        this.txOrdDt = txOrdDt;
    }

    public LocalTime getTxOrdTm() {
        return txOrdTm;
    }

    public void setTxOrdTm(LocalTime txOrdTm) {
        this.txOrdTm = txOrdTm;
    }

    public String getOppAcNo() {
        return oppAcNo;
    }

    public void setOppAcNo(String oppAcNo) {
        this.oppAcNo = oppAcNo;
    }

    public String getOppCapTyp() {
        return oppCapTyp;
    }

    public void setOppCapTyp(String oppCapTyp) {
        this.oppCapTyp = oppCapTyp;
    }

    public String getOppUserId() {
        return oppUserId;
    }

    public void setOppUserId(String oppUserId) {
        this.oppUserId = oppUserId;
    }

    public String getOppUserTyp() {
        return oppUserTyp;
    }

    public void setOppUserTyp(String oppUserTyp) {
        this.oppUserTyp = oppUserTyp;
    }

    public String getUsrIpAdr() {
        return usrIpAdr;
    }

    public void setUsrIpAdr(String usrIpAdr) {
        this.usrIpAdr = usrIpAdr;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }
}
