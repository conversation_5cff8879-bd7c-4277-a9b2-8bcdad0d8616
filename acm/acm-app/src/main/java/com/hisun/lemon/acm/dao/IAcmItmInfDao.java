/*
 * @ClassName IAcmItmInfDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmItmInfDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IAcmItmInfDao extends BaseDao<AcmItmInfDO> {
    /**
     * 查询科目信息
     */
    public AcmItmInfDO get(String itmNo);

    /**
     * 更新科目信息
     */
    public int update(AcmItmInfDO acmItmInfDO);

}