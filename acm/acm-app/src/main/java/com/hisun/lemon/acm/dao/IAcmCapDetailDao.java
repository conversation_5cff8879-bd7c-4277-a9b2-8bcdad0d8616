/*
 * @ClassName IAcmCapDetailDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmCapDetailDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;

@Mapper
public interface IAcmCapDetailDao extends BaseDao<AcmCapDetailDO> {
    /**
     * 删除表acm_cap_detail
     */
    public int dropCapDetail();

    /**
     * 初始化表并导入明细数据
     */
    public int initCapDetail(LocalDate acDt);

    /**
     * 创建表acm_cap_detail
     */
    public int createCapDetail();
}