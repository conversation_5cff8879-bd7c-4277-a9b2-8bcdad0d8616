package com.hisun.lemon.acm.service;

import com.hisun.lemon.acm.dto.*;
import com.hisun.lemon.acm.entity.AcmAcBalDO;
import com.hisun.lemon.acm.entity.AcmItmInfDO;
import com.hisun.lemon.acm.entity.DmPlatFormDO;
import com.hisun.lemon.framework.data.GenericDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @function IAccountManagementService
 * @description 账户管理服务
 * @date 7/13/2017 Thu
 * @time 6:12 PM
 */
public interface IAccountManagementService {

    /**
     * 开通支付账户
     * @param userId
     * @return
     */
    public String openUserAccount(String userId);

    /**
     * 开通科目账户
     * @param acmItmInfDO
     * @return
     */
    public void openItemAccount(AcmItmInfDO acmItmInfDO);

    /**
     * 账户销户
     * @param userId
     * @return
     */
    public void closeUserAccount(String userId);

    /**
     * 科目销户
     * @param itmNo
     * @return
     */
    public void closeItemAccount(String itmNo);

    /**
     * 建立用户账户余额
     * @param acNo
     * @param capTyp
     * @return
     */
    public void createUserAccountBalance(String acNo, String ccy, String capTyp);

    /**
     * 建立科目账户余额
     * @param itmNo
     * @return
     */
    public void createItemAccountBalance(String itmNo);


    /**
     * 根据账号和资金属性查询账户余额信息
     * @param acNo
     * @param capTyp
     * @param userId
     * @return
     */
    public List<AcmAcBalDO> queryAcBal(String acNo, String userId, String capTyp);

    /**
     * 根据账号和资金属性查询账户余额信息
     * @param acNo
     * @param capTyp
     * @param userId
     * @param ccy
     * @return
     */
    public List<AcmAcBalDO> queryAcBal(String acNo, String userId, String capTyp, String ccy);

    /**
     * 根据账号查询、更新冻结账户结算余额
     * @param acNo
     * @return
     */
    public void handleSettleAcBal(String acNo, BigDecimal freezeAmt);

    /**
     * 结算完成，解冻冻结金额
     * @param userId
     * @return
     */
    public void unfreezeSettleAcBal(String userId, String capTyp);

    /**
     * 根据用户ID查询用户所有账户的余额信息
     * @param userId
     * @return
     */
    public String queryAcNo(String userId);

    /**
     * 根据用户ID和币种查询用户所有账户的余额信息
     * @param userId
     * @return
     */
    public String queryAcNo(String userId, String ccy);

    /**
     * 获取当前用户币种账户列表
     * @param userCcyAccountListDTO
     * @return
     */
    List<UserCcyAccountRspDTO> getUserCcyAcountList(UserCcyAccountListDTO userCcyAccountListDTO);

    /**
     * 获取用户数币账户列表
     * @param userDmAccountListDTO
     * @return
     */
    List<UserDmAccountRspDTO> getUserDmAcountList(UserDmAccountListDTO userDmAccountListDTO);


    /**
     * 获取用户数币账户列表
     * @param userDmAccountListDTO
     * @return
     */
    List<UserFmAccountRspDTO> getUserFmAccountList(UserDmAccountListDTO userDmAccountListDTO);

    /**
     * 查看法币账户详情
     * @param acNo 账户编号
     * @return
     */
    UserFmAccountDetailRspDTO getFmAccountDetail(String acNo);

    /**
     * 开立数币账户
     * @param req
     * @return
     */
    void openDmAccount(GenericDTO<OpenDmAccountDTO> req);

    /**
     * 获取收款二维码
     * @param dmPaymentQrcodeDTO
     * @return
     */
    DmPaymentQrCodeRspDTO getPaymentQrcode(DmPaymentQrcodeReqDTO dmPaymentQrcodeDTO);

    String openFmAccount(OpenFmAccountDTO openFmAccountDTO);

    /**
     * 获取用户所有数币账户信息
     * @param req
     * @return
     */
    List<DmAccountInfoRspDTO> getDmAcountInfoList(DmAccountInfoDTO req);

    /**
     * 通过订单集合获取数币账户收款/充值记录列表
     * @param req
     * @return
     */
    List<ReceiptRecord> getDmAccountReceiptsList(ReceiptRecordReqDTO req);

    /**
     * 添加数币账户回调信息
     *
     * @param req
     * @return
     */
    Long addCallback(AddCallbackReqDTO req);

    /**
     * 通过地址获取数币账户信息
     * @param address
     * @return
     */
    DmAccountAddressRspDTO getByAddress(String address);

    /**
     * 更新数币账户回调信息
     * @param req
     */
    void updateCallback(UpdateCallbackReqDTO req);

    /**
     * 更新数币账户余额信息
     * @param req
     */
    void updateAccBal(UpdateAccBalReqDTO req);

    /**
     * 获取当前用户收款/充值数币账户列表
     * @param req
     * @return
     */
    List<DmReceiptAccRspDTO> getDmReceiptAcc(DmReceiptAccReqDTO req);

    /**
     * 计算用户当前净值
     * @param userId
     * @return
     */
    String calNetWorth(String userId);

    /**
     * 校验地址是否存在
     * @param address
     * @return
     */
    boolean checkAddressExists(String address);

    /**
     * 查询数币账户详情
     * @param acNo
     * @return
     */
    DmAccountDetailRspDTO queryDmAccountDetail(String acNo);

    /**
     * 条件查询数币账户地址信息
     * @param findDmAddressReqDTO
     * @return
     */
    List<DmAccountAddressRspDTO> findDmAddress(FindDmAddressReqDTO findDmAddressReqDTO);

    /**
     * 调用cregis更新数币余额
     * @param acNo
     * @return
     */
    void updateBalByCregis(String acNo);

    void rechargeAndReceiptAct(AddOrderReqDTO req);

    List<DmPlatFormBalDTO> queryDmAccountBals();
}
