package com.hisun.lemon.acm.controller;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.dao.*;
import com.hisun.lemon.acm.dto.*;
import com.hisun.lemon.acm.entity.*;
import com.hisun.lemon.acm.service.IAccountManagementService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.client.CregisClient;
import com.hisun.lemon.cpi.dto.CregisAccDetailReqDTO;
import com.hisun.lemon.cpi.dto.CregisAccDetailRspDTO;
import com.hisun.lemon.framework.annotation.LemonBody;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @function AccountManagementController
 * @description 账户管理控制层
 * @date 7/24/2017 Mon
 * @time 10:14 PM
 */
@Api(tags = "AccountManagementController", description = "账户管理")
@RestController
@RequestMapping("/acm/account")
public class AccountManagementController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(AccountManagementController.class);

    @Resource
    private IAccountManagementService iAccountManagementService;

    @Resource
    private IAcmItmInfDao itmInfDao;

    @Resource
    private IAcmItmBalDao itmBalDao;

    @Resource
    private IAcmAcInfDao acInfDao;

    @Resource
    private IDmCcyListDao dmCcyListDao;


    @ApiOperation(value = "建立用户账户", notes = "建立用户账户")
    @ApiResponse(code = 200, message = "建立用户账户成功")
    @PostMapping("/users/{userId}")
    public GenericRspDTO<String> openUserAccount(@Validated @PathVariable("userId") String userId) {
        String acNo = this.iAccountManagementService.openUserAccount(userId);
        return GenericRspDTO.newSuccessInstance(acNo);
    }

    @ApiOperation(value = "建立科目账户", notes = "建立科目账户")
    @ApiResponse(code = 200, message = "科目账户建立")
    @PostMapping("/items")
    public GenericRspDTO<NoBody> openItemAccount(@Validated @RequestBody GenericDTO<ItemAccountDTO> itemAccountDTO) {
        AcmItmInfDO acmItmInfDO = new AcmItmInfDO();
        BeanUtils.copyProperties(acmItmInfDO, itemAccountDTO.getBody());
        this.iAccountManagementService.openItemAccount(acmItmInfDO);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "用户账户销户", notes = "用户账户销户")
    @ApiResponse(code = 200, message = "用户账户销户")
    @DeleteMapping("/users/{userId}")
    public GenericRspDTO<NoBody> closeUserAccount(@Validated @PathVariable("userId") String userId) {
        this.iAccountManagementService.closeUserAccount(userId);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "科目账户注销", notes = "科目账户注销")
    @ApiResponse(code = 200, message = "科目账户注销")
    @DeleteMapping("/items/{itmNo}")
    public GenericRspDTO<NoBody> closeItemAccount(@Validated @PathVariable("itmNo") String itmNo) {
        this.iAccountManagementService.closeUserAccount(itmNo);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "通过账户号查询账户余额", notes = "通过账户号查询账户余额")
    @ApiResponse(code = 200, message = "通过账户号查询账户余额")
    @GetMapping("/acbal/param")
    public GenericRspDTO<List<QueryAcBalRspDTO>> queryAcBal(@RequestParam("acNo") String acNo, @RequestParam("userId")
            String userId, @RequestParam("capTyp") String capTyp, GenericDTO<NoBody> genericDTO) {
        if (JudgeUtils.isBlank(acNo) && JudgeUtils.isBlank(userId)) {
            userId = LemonUtils.getUserId();
        }
        if (JudgeUtils.isBlank(acNo)) {
            acNo = null;
        }
        if (JudgeUtils.isBlank(userId)) {
            userId = null;
        }
        if (JudgeUtils.isBlank(capTyp)) {
            capTyp = null;
        }
        List<AcmAcBalDO> acmAcBalDOS = this.iAccountManagementService.queryAcBal(acNo, userId, capTyp);
        if(JudgeUtils.isNull(acmAcBalDOS)) {
            LemonException.throwBusinessException(ACMMessageCode.RECORD_IS_NULL);
        }
        return GenericRspDTO.newSuccessInstance(JudgeUtils.isNull(acmAcBalDOS)?null:formatAcBalResult(acmAcBalDOS));
    }

    @ApiOperation(value = "通过账户号查询账户余额", notes = "通过账户号查询账户余额")
    @ApiResponse(code = 200, message = "通过账户号查询账户余额")
    @PostMapping("/acbal")
    public GenericRspDTO<List<QueryAcBalRspDTO>> queryAcBal(@Validated @RequestBody UserAccountDTO userAccountDTO) {
        String acNo = userAccountDTO.getAcNo();
        String userId = userAccountDTO.getUserId();
        String ccy = userAccountDTO.getCcy();
        if (JudgeUtils.isBlank(acNo) && JudgeUtils.isBlank(userId)) {
            userId = LemonUtils.getUserId();
        }
        if (JudgeUtils.isBlank(acNo)) {
            acNo = null;
        }
        if (JudgeUtils.isBlank(userId)) {
            userId = null;
        }
        String capTyp = userAccountDTO.getCapTyp();
        if (JudgeUtils.isBlank(capTyp)) {
            capTyp = null;
        }
        List<AcmAcBalDO> acmAcBalDOS;
        if (JudgeUtils.isBlank(ccy)) {
            acmAcBalDOS = this.iAccountManagementService.queryAcBal(acNo, userId, capTyp);
        } else {
            acmAcBalDOS = this.iAccountManagementService.queryAcBal(acNo, userId, capTyp, ccy);
        }
        if(JudgeUtils.isNull(acmAcBalDOS)) {
            LemonException.throwBusinessException(ACMMessageCode.RECORD_IS_NULL);
        }
        return GenericRspDTO.newSuccessInstance(JudgeUtils.isNull(acmAcBalDOS)?null:formatAcBalResult(acmAcBalDOS));
    }

    @ApiOperation(value = "账户结算余额冻结", notes = "账户结算余额冻结")
    @ApiResponse(code = 200, message = "账户结算余额冻结")
    @PostMapping("/settle/acbalFreeze")
    public GenericRspDTO<NoBody> freezeSettleAcBal(@Validated @RequestBody GenericDTO<UserAccountFreezeDTO>  userAccountDTO) {
        String acNo = userAccountDTO.getBody().getAcNo();
        String userId = userAccountDTO.getBody().getUserId();
        LocalDate acDt = userAccountDTO.getBody().getAcDt();
        BigDecimal acBal = BigDecimal.ZERO;
        BigDecimal freezeAmt = userAccountDTO.getBody().getFreezeAmt();
        if (JudgeUtils.isBlank(userId)) {
            userId = LemonUtils.getUserId();
        }
        String capTyp = userAccountDTO.getBody().getCapTyp();
        if (JudgeUtils.isBlank(capTyp)) {
            capTyp = null;
        }
        //查询、更新冻结账户账户结算余额
        iAccountManagementService.handleSettleAcBal(acNo, freezeAmt);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "结算完成，解冻冻结金额", notes = "结算完成，解冻冻结金额")
    @ApiResponse(code = 200, message = "结算完成，解冻冻结金额")
    @PostMapping("/settle/acbalUnfreeze")
    public GenericRspDTO<NoBody> unfreezeSettleAcBal(@Validated @RequestBody GenericDTO<UserAccountFreezeDTO> userAccountDTO) {
        String userId = userAccountDTO.getBody().getUserId();
        String capType = userAccountDTO.getBody().getCapTyp();
        //查询、更新冻结账户账户结算余额
        iAccountManagementService.unfreezeSettleAcBal(userId, capType);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询商户可用操作余额", notes = "查询商户可用操作余额")
    @ApiResponse(code = 200, message = "查询商户可用操作余额")
    @GetMapping("/enableAcBal")
    public GenericRspDTO<BigDecimal> getEnableAcBal(@Validated @RequestBody UserAccountDTO userAccountDTO) {
        String acNo = userAccountDTO.getAcNo();
        String userId = userAccountDTO.getUserId();
        if (JudgeUtils.isBlank(acNo) && JudgeUtils.isBlank(userId)) {
            userId = LemonUtils.getUserId();
        }
        if (JudgeUtils.isBlank(acNo)) {
            acNo = null;
        }
        if (JudgeUtils.isBlank(userId)) {
            userId = null;
        }
        String capTyp = userAccountDTO.getCapTyp();
        if (JudgeUtils.isBlank(capTyp)) {
            capTyp = null;
        }
        List<AcmAcBalDO> acmAcBalDOS = this.iAccountManagementService.queryAcBal(acNo, userId, capTyp);
        if(JudgeUtils.isNull(acmAcBalDOS)) {
            LemonException.throwBusinessException(ACMMessageCode.RECORD_IS_NULL);
        }
        AcmAcBalDO acmAcBalDO = acmAcBalDOS.get(0);
        return GenericRspDTO.newSuccessInstance(acmAcBalDO.getAcCurBal().subtract(acmAcBalDO.getAcCurFreezeBal()));
    }

    @ApiOperation(value = "通过用户号查询账户号", notes = "通过用户号查询账户号")
    @ApiResponse(code = 200, message = "通过用户号查询账户号")
    @GetMapping("/acno/{userId}")
    public GenericRspDTO<String> queryAcNo(@Validated @PathVariable("userId") String userId) {
        String acNo = this.iAccountManagementService.queryAcNo(userId);
        return GenericRspDTO.newSuccessInstance(acNo);
    }

    @ApiOperation(value = "通过用户号和币种查询账户号", notes = "通过用户号和币种查询账户号")
    @ApiResponse(code = 200, message = "通过用户号和币种查询账户号")
    @GetMapping("/acno/queryAcNo")
    public GenericRspDTO<String> queryAcNo(@Validated @RequestBody UserAccountDTO userAccountDTO) {
        String acNo = this.iAccountManagementService.queryAcNo(userAccountDTO.getUserId(), userAccountDTO.getCcy());
        return GenericRspDTO.newSuccessInstance(acNo);
    }

    private List<QueryAcBalRspDTO> formatAcBalResult(List<AcmAcBalDO> acmAcBalDOS) {
        List<QueryAcBalRspDTO> queryAcBalRspDTOS;
        queryAcBalRspDTOS = acmAcBalDOS.stream().map(acmAcBalDO -> {
            QueryAcBalRspDTO queryAcBalRspDTO = new QueryAcBalRspDTO();
            BeanUtils.copyProperties(queryAcBalRspDTO, acmAcBalDO);
//            queryAcBalRspDTO.setAcCurBal(new BigDecimal(10));//
            return queryAcBalRspDTO;
        }).collect(Collectors.toList());
        return queryAcBalRspDTOS;
    }

    @ApiOperation(value = "通过账户号查询用户号", notes = "通过账户号查询用户号")
    @ApiResponse(code = 200, message = "通过账户号查询用户号")
    @GetMapping("/users/{acNo}")
    public GenericRspDTO<String> queryUser(@Validated @PathVariable("acNo") String acNo) {
        AcmAcInfDO acInfDO = new AcmAcInfDO();
        Map<String, String> map = new HashMap<>();
        map.put("acNo", acNo);
        map.put("ccy", ACMConstants.SYS_DEFAULT_CCY);
        acInfDO = acInfDao.get(map);
        String userId = null;
        if (JudgeUtils.isNotNull(acInfDO)) {
            userId = acInfDO.getUserId();
        } else {
            LemonException.throwLemonException(ACMMessageCode.AC_NOT_EXIST);
        }
        return GenericRspDTO.newSuccessInstance(userId);
    }

    @ApiOperation(value = "通过科目号查询科目信息", notes = "通过科目号查询科目信息")
    @ApiResponse(code = 200, message = "通过科目号查询科目信息")
    @GetMapping("/items/{itmNo}")
    public GenericRspDTO<ItemAccountDTO> queryItemAccountInf(@Validated @PathVariable("itmNo") String itmNo) {
        AcmItmInfDO itmInfDO = itmInfDao.get(itmNo);
        ItemAccountDTO itemAccountDTO = new ItemAccountDTO();
        if (JudgeUtils.isNotNull(itmInfDO)) {
            BeanUtils.copyProperties(itemAccountDTO, itmInfDO);
        } else {
            LemonException.throwLemonException(ACMMessageCode.ITM_NOT_EXIST);
        }
        return GenericRspDTO.newSuccessInstance(itemAccountDTO);
    }

    @ApiOperation(value = "获取所有科目余额信息", notes = "获取所有科目余额信息")
    @ApiResponse(code = 200, message = "获取所有科目余额信息")
    @GetMapping("/items/queryAll")
    public GenericRspDTO<List<ItemAccountBalDTO>> queryAllItemAccountInf() {
        List<AcmItmBalDO> itmBalDO = itmBalDao.getAll();
        List<ItemAccountBalDTO> itemAccountBalDTO = new ArrayList<>();
        if (JudgeUtils.isNotNull(itmBalDO) || !itmBalDO.isEmpty()) {
            itemAccountBalDTO = itmBalDO.stream().map(acmItmBalDO -> {
                ItemAccountBalDTO itemAccountBalDTO1 = new ItemAccountBalDTO();
                BeanUtils.copyProperties(itemAccountBalDTO1, acmItmBalDO);
                return itemAccountBalDTO1;
            }).collect(Collectors.toList());
        }
        return GenericRspDTO.newSuccessInstance(itemAccountBalDTO);
    }

    @ApiOperation(value = "获取当前用户币种账户列表", notes = "获取当前用户币种账户列表")
    @ApiResponse(code = 200, message = "获取当前用户币种账户列表")
    @GetMapping("/list")
    public GenericRspDTO<List<UserCcyAccountRspDTO>> queryUserAccountList(@Validated @LemonBody UserCcyAccountListDTO userCcyAccountListDTO) {

        logger.info("获取当前用户币种账户列表,用户编号: {}", userCcyAccountListDTO.getUserId());
        List<UserCcyAccountRspDTO> list = iAccountManagementService.getUserCcyAcountList(userCcyAccountListDTO);
        return GenericRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "获取用户数币账户列表", notes = "查询当前用户的数币账户列表")
    @ApiResponse(code = 200, message = "获取用户数币账户列表成功")
    @GetMapping("/dm/list")
    public GenericRspDTO<List<UserDmAccountRspDTO>> queryUserDmAccountList(@Validated @LemonBody UserDmAccountListDTO userDmAccountListDTO) {

        logger.info("获取用户数币账户列表,用户编号: {}", userDmAccountListDTO.getUserId());
        List<UserDmAccountRspDTO> list = iAccountManagementService.getUserDmAcountList(userDmAccountListDTO);
        return GenericRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "查看数币账户详情", notes = "查看数币账户详情")
    @ApiResponse(code = 200, message = "查看数币账户详情")
    @GetMapping("/dm/{acNo}")
    public GenericRspDTO<DmAccountDetailRspDTO> queryDmAccountDetail(
            @Validated @PathVariable("acNo") @NotBlank(message = "账户编号不能为空") @Length(max = 128) String acNo) {
        logger.info("查看数币账户详情,账号编号: {}", acNo);

        DmAccountDetailRspDTO dmAccountDetailRspDTO = iAccountManagementService.queryDmAccountDetail(acNo);

        return GenericRspDTO.newSuccessInstance(dmAccountDetailRspDTO);
    }

    @ApiOperation(value = "获取平台支持币种列表", notes = "获取平台支持币种列表")
    @ApiResponse(code = 200, message = "获取平台支持币种列表")
    @GetMapping("/ccy/list")
    public GenericRspDTO<List<DmCcyListRspDTO>> getCcyList(@LemonBody GenericDTO<NoBody> genericDTO) {
        List<DmCcyListDO> ccyList = dmCcyListDao.getCcyList();
        if (JudgeUtils.isEmpty(ccyList)) {
            logger.info("平台支持币种列表为空");
            return GenericRspDTO.newSuccessInstance(Collections.emptyList());
        }
        List<DmCcyListRspDTO> rspList = ccyList.stream().map(doObj -> {
            DmCcyListRspDTO rsp = new DmCcyListRspDTO();
            BeanUtils.copyProperties(rsp, doObj);
            return rsp;
        }).collect(Collectors.toList());
        logger.info("获取平台支持币种列表成功，数量: {}", rspList.size());
        return GenericRspDTO.newSuccessInstance(rspList);
    }

    @ApiOperation(value = "开立数币账户", notes = "开立数币账户")
    @ApiResponse(code = 200, message = "开立数币账户")
    @PostMapping("/dm/open")
    public GenericRspDTO<NoBody> openDmAccount(@Validated @RequestBody GenericDTO<OpenDmAccountDTO> req) {

        logger.info("开立数币账户: {}", req);
//        if (JudgeUtils.isBlank(req.getUserId())){
//            req.setUserId(LemonUtils.getUserId());
//        }
        iAccountManagementService.openDmAccount(req);

        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "获取用户法币账户列表", notes = "查询当前用户的法币账户列表")
    @ApiResponse(code = 200, message = "获取用户法币账户列表成功")
    @PostMapping("/fm/list")
    public GenericRspDTO<List<UserFmAccountRspDTO>> queryUserFmAccountList(@Validated UserDmAccountListDTO userDmAccountListDTO) {
//        userDmAccountListDTO.setUserId(LemonUtils.getUserId());
        List<UserFmAccountRspDTO> list = iAccountManagementService.getUserFmAccountList(userDmAccountListDTO);
        return GenericRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "查看法币账户详情", notes = "查看法币账户详情")
    @ApiResponse(code = 200, message = "查看法币账户详情")
    @PostMapping("/fm/{acNo}")
    public GenericRspDTO<UserFmAccountDetailRspDTO> queryFmAccountDetail(
            @Validated @PathVariable("acNo") @NotBlank(message = "账户编号不能为空") @Length(max = 128) String acNo) {
        logger.info("查看法币账户详情: {}", acNo);
        return GenericRspDTO.newSuccessInstance(iAccountManagementService.getFmAccountDetail(acNo));
    }

    @ApiOperation(value = "获取当前用户收款/充值数币账户列表", notes = "获取当前用户收款/充值数币账户列表")
    @ApiResponse(code = 200, message = "获取当前用户收款/充值数币账户列表成功")
    @PostMapping("/dm/receipt/accounts")
    public GenericRspDTO<List<DmReceiptAccRspDTO>> getDmReceiptAcc(@Validated @RequestBody GenericDTO<DmReceiptAccReqDTO> genericDTO) {
        DmReceiptAccReqDTO req = genericDTO.getBody();
        logger.info("获取当前用户收款/充值数币账户列表: {}", req);

//        if (JudgeUtils.isBlank(req.getUserId())){
//            req.setUserId(LemonUtils.getUserId());
//        }
        List<DmReceiptAccRspDTO> list = iAccountManagementService.getDmReceiptAcc(req);
        return GenericRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "收款二维码", notes = "收款二维码")
    @ApiResponse(code = 200, message = "收款二维码")
    @PostMapping("/dm/qrcode/payment")
    public GenericRspDTO<DmPaymentQrCodeRspDTO> getDmPaymentQrcode(
            @Validated @RequestBody GenericDTO<DmPaymentQrcodeReqDTO> req) {
        logger.info("收款二维码: {}", req);
        DmPaymentQrcodeReqDTO dmPaymentQrcodeDTO = req.getBody();
        DmPaymentQrCodeRspDTO resp = iAccountManagementService.getPaymentQrcode(dmPaymentQrcodeDTO);
        return GenericRspDTO.newSuccessInstance(resp);
    }

    @ApiOperation(value = "开立法币账户", notes = "openFmAccount")
    @ApiResponse(code = 200, message = "开立法币账户")
    @PostMapping(value = "/fm/open")
    public GenericRspDTO<String> openFmAccount(@Validated @RequestBody GenericDTO<OpenFmAccountDTO> genericDTO) {
        OpenFmAccountDTO openFmAccountDTO = genericDTO.getBody();
        openFmAccountDTO.setUserId(LemonUtils.getUserId());
        return GenericRspDTO.newSuccessInstance(iAccountManagementService.openFmAccount(openFmAccountDTO));
    }

    @ApiOperation(value = "获取用户所有数币账户信息", notes = "获取用户所有数币账户信息")
    @ApiResponse(code = 200, message = "获取用户所有数币账户信息成功")
    @PostMapping("/dm/all/info")
    public GenericRspDTO<List<DmAccountInfoRspDTO>> getDmAccountInfo(@Validated @RequestBody DmAccountInfoDTO req) {

        logger.info("获取用户所有数币账户信息: {}", req);
        if (JudgeUtils.isBlank(req.getUserId())) {
            req.setUserId(LemonUtils.getUserId());
        }
        List<DmAccountInfoRspDTO> list = iAccountManagementService.getDmAcountInfoList(req);
        return GenericRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "通过订单集合获取数币账户收款/充值分页记录列表", notes = "通过订单集合获取数币账户收款/充值分页记录列表")
    @ApiResponse(code = 200, message = "通过订单集合获取数币账户收款/充值分页记录列表成功")
    @PostMapping("/dm/receipts/list")
    public GenericRspDTO<List<ReceiptRecord>> getDmAccountReceiptsList(@RequestBody ReceiptRecordReqDTO req) {

        List<ReceiptRecord> result = iAccountManagementService.getDmAccountReceiptsList(req);
        return GenericRspDTO.newSuccessInstance(result);
    }

    @ApiOperation(value = "往回调表中新增数据", notes = "往回调表中新增数据")
    @ApiResponse(code = 200, message = "往回调表中新增数据成功")
    @PostMapping("/add/callback")
    public GenericRspDTO<Long> addCallback(@Validated @RequestBody AddCallbackReqDTO req) {
        logger.info("往回调表中新增数据: {}", req);
        Long id = iAccountManagementService.addCallback(req);
        logger.info("往回调表中新增数据成功,id = {}", id);
        return GenericRspDTO.newSuccessInstance(id);
    }

    @ApiOperation(value = "更新回调表数据", notes = "更新回调表数据")
    @ApiResponse(code = 200, message = "更新回调表数据成功")
    @PostMapping("/update/callback")
    public GenericRspDTO<NoBody> updateCallback(@Validated @RequestBody UpdateCallbackReqDTO req) {
        logger.info("更新回调表数据: {}", req);
        iAccountManagementService.updateCallback(req);
        logger.info("更新回调表数据成功");
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "根据地址查询数币账户地址信息", notes = "根据地址查询数币账户地址信息")
    @ApiResponse(code = 200, message = "根据地址查询数币账户地址信息成功")
    @GetMapping("/get/by-address")
    public GenericRspDTO<DmAccountAddressRspDTO> getByAddress(@Validated @NotBlank @Length(max = 100) String address) {
        logger.info("根据地址查询数币账户地址信息: {}", address);
        DmAccountAddressRspDTO result = iAccountManagementService.getByAddress(address);
        return GenericRspDTO.newSuccessInstance(result);
    }

    @ApiOperation(value = "检查数币账户地址是否存在", notes = "检查指定地址是否存在于数币账户地址表中")
    @ApiResponse(code = 200, message = "地址存在性检查成功，返回true表示存在，false表示不存在")
    @GetMapping("/check-address-exists")
    public GenericRspDTO<Boolean> checkAddressExists(@Validated @NotBlank @Length(max = 100) String address) {
        logger.info("检查数币账户地址是否存在: {}", address);
        boolean exists = iAccountManagementService.checkAddressExists(address);
        return GenericRspDTO.newSuccessInstance(exists);
    }

    @ApiOperation(value = "更新账户余额信息", notes = "更新账户余额信息")
    @ApiResponse(code = 200, message = "更新账户余额信息成功")
    @PostMapping("/update/balance")
    public GenericRspDTO<NoBody> updateBalance(@Validated @RequestBody UpdateAccBalReqDTO req) {
        logger.info("更新账户余额信息: {}", req);
        iAccountManagementService.updateAccBal(req);
        logger.info("更新账户余额信息成功");
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "获取当前用户账户净值", notes = "获取当前用户账户净值")
    @ApiResponse(code = 200, message = "获取当前用户账户净值")
    @PostMapping("/networth")
    public GenericRspDTO<String> calNetWorth() {
        return GenericRspDTO.newSuccessInstance(iAccountManagementService.calNetWorth(LemonUtils.getUserId()));
    }

    @ApiOperation(value = "查询数币账户地址信息", notes = "查询数币账户地址信息")
    @ApiResponse(code = 200, message = "查询数币账户地址信息成功")
    @PostMapping("/find/dm_address")
    public GenericRspDTO<List<DmAccountAddressRspDTO>> findDmAddress(@Validated @RequestBody GenericDTO<FindDmAddressReqDTO> req) {
        logger.info("条件查询数币账户地址信息");
        FindDmAddressReqDTO findDmAddressReqDTO = req.getBody();
        List<DmAccountAddressRspDTO> result = iAccountManagementService.findDmAddress(findDmAddressReqDTO);
        return GenericRspDTO.newSuccessInstance(result);
    }

    @ApiOperation(value = "调用cregis更新数币余额", notes = "调用cregis更新数币余额")
    @ApiResponse(code = 200, message = "调用cregis更新数币余额")
    @PostMapping("/balance/update")
    public GenericRspDTO<NoBody> updateBalByCregis(@Validated @RequestParam(name = "acNo") String acNo) {
        iAccountManagementService.updateBalByCregis(acNo);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "数币充值和收款做账务处理", notes = "数币充值和收款做账务处理")
    @ApiResponse(code = 200, message = "数币充值和收款做账务处理")
    @PostMapping("/rechargeAndReceiptAct")
    public GenericRspDTO<NoBody> rechargeAndReceiptAct(@Validated @RequestBody AddOrderReqDTO req) {
        iAccountManagementService.rechargeAndReceiptAct(req);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "获取数币头寸账户余额", notes = "获取数币头寸账户余额")
    @ApiResponse(code = 200, message = "获取数币头寸账户余额")
    @GetMapping("/dm/getAll")
    public GenericRspDTO<List<DmPlatFormBalDTO>> queryDmAccountBals() {
        logger.info("获取数币头寸账户余额");
        List<DmPlatFormBalDTO> dmAccountDetailRspDTO = iAccountManagementService.queryDmAccountBals();

        return GenericRspDTO.newSuccessInstance(dmAccountDetailRspDTO);
    }
}
