/*
 * @ClassName IAcmAcBalDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.bo.LedgerBalanceBo;
import com.hisun.lemon.acm.entity.AcmAcBalDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Mapper
public interface IAcmAcBalDao extends BaseDao<AcmAcBalDO> {
    /**
     * 查询账户余额
     */
    public AcmAcBalDO get(@Param("acNo") String acNo, @Param("capTyp") String capTyp);
    /**
     * 查询并锁定账户余额信息
     */
    public AcmAcBalDO getBalAndLock(AcmAcBalDO acmAcBalDO);

    /**
     * 按照资金类型和币种分组，统计分户账余额
     */
    public List<LedgerBalanceBo> getLedgerBal(LocalDate updDt);

    /**
     * 删除余额记录
     */
    public int delete(Map map);

    /**
     * 更新冻结金额记录
     */
    public int updateFreezeAmt(@Param("acNo") String acNo, @Param("freezeAmt") BigDecimal freezeAmt);

    /**
     * 结算完成，删除冻结金额记录
     */
    public int delFreezeAmt(@Param("userId") String userId, @Param("capType") String capType);
}
