package com.hisun.lemon.acm.controller;

import com.hisun.lemon.acm.bo.AccountingBO;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.dto.AccountingHoldReqDTO;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.entity.AcmAcHoldDO;
import com.hisun.lemon.acm.service.IAccountingTreatmentService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @function AccountingTreatmentController
 * @description 账务处理控制
 * @date 7/24/2017 Mon
 * @time 10:58 PM
 */
@Api(tags = "AccountingTreatmentController", description = "账务处理")
@RestController
@RequestMapping("/acm/trade/")
public class AccountingTreatmentController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(AccountManagementController.class);

    @Resource
    private IAccountingTreatmentService iAccountingTreatmentService;

    @ApiOperation(value = "账务处理", notes = "账务处理")
    @ApiResponse(code = 200, message = "账务处理")
    @PostMapping("/accounting")
    public GenericRspDTO<NoBody> accountingTreatment(@Validated @RequestBody GenericDTO<List<AccountingReqDTO>>
                                                              accountingReqDTOList) {
        List<AccountingBO> accountingBOList = new ArrayList<AccountingBO>();
        List<AccountingReqDTO> reqDTOS = accountingReqDTOList.getBody();
        if (JudgeUtils.isNotNull(reqDTOS)) {
            accountingBOList = reqDTOS.stream().map(accountingReqDTO -> {
                AccountingBO accountingBO = new AccountingBO();
                BeanUtils.copyProperties(accountingBO, accountingReqDTO);
                return accountingBO;
            }).collect(Collectors.toList());
        }
        this.iAccountingTreatmentService.accountingTreatment(accountingBOList);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "冻结", notes = "holdUserAccountBalance")
    @ApiResponse(code = 200, message = "冻结")
    @PostMapping("/hold")
    public GenericRspDTO<String> holdUserAccountBalance(@Validated @RequestBody GenericDTO<AccountingHoldReqDTO>
                                                                    holdReqDTO) {
        AcmAcHoldDO acmAcHoldDO = new AcmAcHoldDO();
        AccountingHoldReqDTO req = holdReqDTO.getBody();
        BeanUtils.copyProperties(acmAcHoldDO, req);
        String holdNo = this.iAccountingTreatmentService.holdUserAccountBalance(acmAcHoldDO);
        return GenericRspDTO.newSuccessInstance(holdNo);
    }

    @ApiOperation(value = "解冻", notes = "unHoldUserAccountBalance")
    @ApiResponse(code = 200, message = "解冻")
    @PutMapping("/unhold/{holdNo}")
    public GenericRspDTO<NoBody> unHoldUserAccountBalance(@Validated @PathVariable("holdNo") String holdNo) {
        if (JudgeUtils.isBlank(holdNo)) {
            LemonException.throwLemonException(ACMMessageCode.HOLD_NO_IS_NULL);
        }
        this.iAccountingTreatmentService.unHoldUserAccountBalance(holdNo);
        return GenericRspDTO.newSuccessInstance();
    }
}
