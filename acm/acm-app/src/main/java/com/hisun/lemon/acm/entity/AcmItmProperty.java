/*
 * @ClassName AcmItmProperty
 * @Description 
 * @version 1.0
 * @Date 2017-07-20 20:40:00
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class AcmItmProperty extends BaseDO {
    /**
     * capTyp 资金类型 1：现金 8：待结算资金
     */
    private String capTyp;
    /**
     * ccy 币种
     */
    private String ccy;
    /**
     * itmNo 科目号
     */
    private String itmNo;
    /**
     * balDrt 余额方向（A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）
     */
    private String balDrt;
    /**
     * updOpr 更新操作员
     */
    private String updOpr;

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public String getBalDrt() {
        return balDrt;
    }

    public void setBalDrt(String balDrt) {
        this.balDrt = balDrt;
    }

    public String getUpdOpr() {
        return updOpr;
    }

    public void setUpdOpr(String updOpr) {
        this.updOpr = updOpr;
    }
}