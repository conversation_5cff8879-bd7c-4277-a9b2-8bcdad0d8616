package com.hisun.lemon.acm.service.impl;

import com.hisun.lemon.acm.bo.AccountingBO;
import com.hisun.lemon.acm.common.utils.BalanceTagUtil;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.dao.*;
import com.hisun.lemon.acm.entity.*;
import com.hisun.lemon.acm.service.IAccountManagementService;
import com.hisun.lemon.acm.service.IAccountingTreatmentService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @function AccountingTreatmentServiceImpl
 * @description 账务处理服务业务实现
 * @date 7/13/2017 Thu
 * @time 12:20 PM
 */
@Service
@Transactional
public class AccountingTreatmentServiceImpl extends BaseService implements IAccountingTreatmentService {
    private final static Logger logger = LoggerFactory.getLogger(GeneralLedgerServiceImpl.class);

    @Resource
    private IAcmItmInfDao iAcmItmInfDao;

    @Resource
    private IAcmAcInfDao iAcmAcInfDao;

    @Resource
    private IAcmAcBalDao iAcmAcBalDao;

    @Resource
    private IAcmItmBalDao iAcmItmBalDao;

    @Resource
    private IAcmAcDetailDao iAcmAcDetailDao;

    @Resource
    private IAcmItmDetailDao iAcmItmDetailDao;

    @Resource
    private IAcmAcHoldDao iAcmAcHoldDao;

    @Resource
    IAccountManagementService managementService;
    /**
     * 账务处理
     *
     * @return
     */
    @Override
    public void accountingTreatment(List<AccountingBO> accountingBOList) {
        if (JudgeUtils.isNull(LemonUtils.getAccDate())) {
            LemonException.throwLemonException(ACMMessageCode.INIT_ACCOUNTING_FAILURE);
        }

        if (JudgeUtils.isEmpty(accountingBOList)) {
            LemonException.throwLemonException(ACMMessageCode.PARAM_IS_NULL);
        }
        //判断借贷是否平衡
        BigDecimal sumDrAmt = BigDecimal.ZERO.setScale(2);
        BigDecimal sumCrAmt = BigDecimal.ZERO.setScale(2);
        int acJrnSeq = 0;
        int itmJrnSeq = 0;
        if (JudgeUtils.equals(accountingBOList.get(0).getTxSts(),ACMConstants.ACCOUNTING_CANCEL)) {
            String txJrnNo = accountingBOList.get(0).getTxJrnNo();
            if (JudgeUtils.isNotBlank(txJrnNo)) {
                acJrnSeq = iAcmAcDetailDao.getJrnSeq(txJrnNo) + 1;
                itmJrnSeq = iAcmItmDetailDao.getJrnSeq(txJrnNo) + 1;
            } else {
                LemonException.throwLemonException(ACMMessageCode.PARAM_IS_NULL);
            }

            List<AcmAcDetailDO> acmAcDetailDOS = iAcmAcDetailDao.find(txJrnNo);
            if (JudgeUtils.isEmpty(acmAcDetailDOS)) {
                LemonException.throwLemonException(ACMMessageCode.ORIGINAL_TRADE_NOT_EXIST);
            }
            if (accountingBOList.size() != acmAcDetailDOS.size()) {
                LemonException.throwLemonException(ACMMessageCode.REVERSAL_TRADE_NUM_FALSE);
            }
            LocalDate acDt = LemonUtils.getAccDate();
            if (acDt.compareTo(acmAcDetailDOS.get(0).getAcDt()) !=0 ) {
                LemonException.throwLemonException(ACMMessageCode.EXP_NOT_CANCEL);
            }
        }
        String txJrnNo = null;
        String initTxSts = "";//初始化交易状态为空
        String jrnNo = IdGenUtils.generateId("JRN_NO", DateTimeUtils.formatLocalDateTime(LocalDateTime.now()), 18);
        //循环处理交易
        for (AccountingBO accountingBO : accountingBOList) {
            if (JudgeUtils.isNull(accountingBO.getTxAmt()) || accountingBO.getTxAmt().compareTo(BigDecimal.ZERO) <= 0) {
                LemonException.throwLemonException(ACMMessageCode.ILLEGAL_AMT);
            }
            //检查账号是否合法
            if (!verifyAccount(accountingBO)) {
                LemonException.throwBusinessException(ACMMessageCode.ILLEGAL_AC_NO);
            }
            String txSts = accountingBO.getTxSts();
            //判断集合中交易状态是否一致
            if (StringUtils.isBlank(initTxSts)) {
                initTxSts = txSts;
            }
            if (!initTxSts.equals(txSts)) {
                LemonException.throwBusinessException(ACMMessageCode.TX_STS_NOT_CONSISTENT);
            }
            //检查账务的交易请求流水号是否一致
            if (StringUtils.isBlank(txJrnNo)) {
                txJrnNo = accountingBO.getTxJrnNo();
            }
            if (!txJrnNo.equals(accountingBO.getTxJrnNo())) {
                LemonException.throwBusinessException(ACMMessageCode.TX_JRN_NO_NOT_CONSISTENT);
            }

            String acTyp = accountingBO.getAcTyp();
            String dcFlg = accountingBO.getDcFlg();
            BigDecimal txAmt = accountingBO.getTxAmt();

            //检查金额的合法性(金额的精度大于2)无法处理
//            if (txAmt.scale() > 2) {
//                LemonException.throwLemonException(ACMMessageCode.ILLEGAL_AMT);
//            }

            if (StringUtils.equals(dcFlg, ACMConstants.AC_D_FLG)) {
                //统计借方金额
                sumDrAmt = sumDrAmt.add(txAmt);
            } else {
                //统计贷方金额
                sumCrAmt = sumCrAmt.add(txAmt);
            }

            //判断用户类型
            if (acTyp.equalsIgnoreCase(ACMConstants.USER_AC_TYP)) {
                //用户账务处理
                accountingBO.setJrnNo(jrnNo);
                accountingBO.setJrnSeq(String.valueOf(acJrnSeq));
                userAccountingTreatment(accountingBO);
                acJrnSeq++;
            } else if (StringUtils.equals(accountingBO.getAcTyp(), ACMConstants.ITM_AC_TYP)) {
                //科目账务处理
                accountingBO.setJrnNo(jrnNo);
                accountingBO.setJrnSeq(String.valueOf(itmJrnSeq));
                itemAccountingTreatment(accountingBO);
                itmJrnSeq++;
            } else {
                LemonException.throwBusinessException(ACMMessageCode.AC_TYP_IS_NULL);
            }
        }
        ArrayList<String> list = new ArrayList<>();
        list.add("DC");
        list.add("DS");
        if (!list.contains(accountingBOList.get(0).getTxTyp())) {
            if (sumDrAmt.compareTo(sumCrAmt) != 0) {
                LemonException.throwBusinessException(ACMMessageCode.DR_NOT_EQUAL_CR);
            }
        }

    }

    /**
     * 检查账号是否合法
     */
    private boolean verifyAccount(AccountingBO accountingBO) {
        String acTyp = accountingBO.getAcTyp();
        if (acTyp.equals(ACMConstants.USER_AC_TYP)) {
            String acNo = accountingBO.getAcNo();
            if (JudgeUtils.isBlank(acNo)) {
                LemonException.throwLemonException(ACMMessageCode.ILLEGAL_AC_NO);
            }
            AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
            acmAcInfDO.setAcNo(acNo);
//            acmAcInfDO.setCcy(ACMConstants.SYS_DEFAULT_CCY);
            List<AcmAcInfDO> acmAcInfDOS = iAcmAcInfDao.find(acmAcInfDO);
            if (JudgeUtils.isEmpty(acmAcInfDOS)) {
                LemonException.throwBusinessException(ACMMessageCode.AC_NOT_EXIST);
            }
            for (AcmAcInfDO acmAcInf : acmAcInfDOS) {
                if (JudgeUtils.notEquals(acmAcInf.getAcSts(), ACMConstants.AC_OPEN_STS)) {
                    LemonException.throwBusinessException(ACMMessageCode.AC_IS_CLOSED);
                }
            }
        } else if (StringUtils.equals(acTyp, ACMConstants.ITM_AC_TYP)) {
            String itmNo = accountingBO.getItmNo();
            AcmItmInfDO acmItmInfDO = iAcmItmInfDao.get(itmNo);
            if (JudgeUtils.isNull(acmItmInfDO)) {
                LemonException.throwBusinessException(ACMMessageCode.ITM_NOT_EXIST);
            }
            if (JudgeUtils.notEquals(acmItmInfDO.getItmSts(), ACMConstants.ITM_OPEN_STS)) {
                LemonException.throwBusinessException(ACMMessageCode.ITM_IS_CLOSED);
            }
        } else {
            LemonException.throwBusinessException(ACMMessageCode.AC_TYP_IS_NULL);
        }
        return true;
    }

    /**
     * 用户账务处理
     */
    private void userAccountingTreatment(AccountingBO accountingBO) {
        String acNo = accountingBO.getAcNo();
        String capTyp = accountingBO.getCapTyp();
        String dcFlg = accountingBO.getDcFlg();
        LocalDate acDt = LemonUtils.getAccDate();
        BigDecimal txAmt = accountingBO.getTxAmt();
        String txSts = accountingBO.getTxSts();
        //判断用户余额记录是否创建
        AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
        acmAcBalDO.setAcNo(acNo);
        acmAcBalDO.setCapTyp(capTyp);
        if (JudgeUtils.isBlank(capTyp)) {
            LemonException.throwBusinessException(ACMMessageCode.CAP_TYP_IS_NULL);
        }
        logger.info("acNo:" + acNo + " capTyp:" + capTyp);
        acmAcBalDO = iAcmAcBalDao.getBalAndLock(acmAcBalDO);
        if (JudgeUtils.isNull(acmAcBalDO)) {
            //余额记录不存在，如果贷用户则建立用户余额记录，如果借用户直接报错退出
            if (StringUtils.equals(dcFlg, ACMConstants.AC_C_FLG)
                    && StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                //建立余额记录
                managementService.createUserAccountBalance(acNo, ACMConstants.SYS_DEFAULT_CCY, capTyp);
                //获取最新记录
                acmAcBalDO = new AcmAcBalDO();
                acmAcBalDO.setAcNo(acNo);
                acmAcBalDO.setCapTyp(capTyp);
                acmAcBalDO = iAcmAcBalDao.getBalAndLock(acmAcBalDO);
            } else {
                logger.info("acNo:" + acNo + " capTyp:" + capTyp + "根据acNo和capType查不到用户余额记录，无法进行账务处理1");
                LemonException.throwBusinessException(ACMMessageCode.BAL_NOT_ENOUGH);
            }
        } else {
            //校验余额TAG
            String balTag = acmAcBalDO.getAcBalTag();
            logger.info("acNo:" + acmAcBalDO.getAcNo() + " capTyp:" +acmAcBalDO.getCapTyp() + " acCurBal:" +
                    acmAcBalDO.getAcCurBal() + " balTag:" + acmAcBalDO.getAcBalTag());
            String tmpBalTag = BalanceTagUtil.createBalanceTag(acmAcBalDO.getAcNo(), acmAcBalDO.getCapTyp(),
                    acmAcBalDO.getAcCurBal());
            logger.info(tmpBalTag);
//            if (!BalanceTagUtil.checkBalanceTag(acmAcBalDO.getAcNo(), acmAcBalDO.getCapTyp(),
//                    acmAcBalDO.getAcCurBal(), balTag)) {
//                LemonException.throwLemonException(LemonException.SYS_ERROR_MSGCD);
//            }
        }
        AcmAcDetailDO acmAcDetailDO = new AcmAcDetailDO();
        AcmAcBalDO acmAcBalInf = acmAcBalDO;
        //更新用户账户余额
        acNo = acmAcBalInf.getAcNo();
        capTyp = acmAcBalInf.getCapTyp();
        LocalDate lastAcDt = acmAcBalInf.getAcUpdDt();
        BigDecimal acAcCurAcBal = acmAcBalInf.getAcCurBal();
        BigDecimal acAcUavaBal = acmAcBalInf.getAcUavaBal();
        BigDecimal lastBalAmt = acmAcBalInf.getAcLastBal();
        BigDecimal lastUavaBal = acmAcBalInf.getAcLastUavaBal();
        if (acDt.compareTo(lastAcDt) > 0) {
            lastBalAmt = acmAcBalInf.getAcCurBal();
            lastUavaBal = acmAcBalInf.getAcUavaBal();
            lastAcDt = acDt;
        } else if (acDt.compareTo(lastAcDt) < 0) {
            if (StringUtils.equalsIgnoreCase(dcFlg, ACMConstants.AC_D_FLG)) {
                if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                    lastBalAmt = lastBalAmt.subtract(txAmt);
                } else {
                    lastBalAmt = lastBalAmt.add(txAmt);
                }
            } else {
                if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                    lastBalAmt = lastBalAmt.add(txAmt);
                } else {
                    lastBalAmt = lastBalAmt.subtract(txAmt);
                }
            }
        }

        BigDecimal acBal = BigDecimal.valueOf(0,2);
        if (StringUtils.equals(dcFlg, ACMConstants.AC_D_FLG)) {
            if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                acBal = acAcCurAcBal.subtract(txAmt);
                //检查余额是否可以完成交易
                if (acBal.compareTo(acAcUavaBal) < 0) {
                    logger.info("acNo:" + acNo + " capTyp:" + capTyp + "根据acNo和capType查不到用户余额记录，无法进行账务处理2");
                    LemonException.throwBusinessException(ACMMessageCode.BAL_NOT_ENOUGH);
                }
            } else {
                acBal = acAcCurAcBal.add(txAmt);
            }
        } else if (StringUtils.equals(dcFlg, ACMConstants.AC_C_FLG)) {
            if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                acBal = acAcCurAcBal.add(txAmt);
            } else {
                acBal = acAcCurAcBal.subtract(txAmt);
                //检查余额是否可以完成交易
                if (acBal.compareTo(acAcUavaBal) < 0) {
                    logger.info("acNo:" + acNo + " capTyp:" + capTyp + "根据acNo和capType查不到用户余额记录，无法进行账务处理3");
                    LemonException.throwBusinessException(ACMMessageCode.BAL_NOT_ENOUGH);
                }
            }
        } else {
            LemonException.throwBusinessException(ACMMessageCode.DC_FLG_IS_NULL);
        }
        acmAcBalInf.setAcUpdDt(lastAcDt);
        acmAcBalInf.setAcUpdTm(DateTimeUtils.getCurrentLocalTime());
        acmAcBalInf.setAcLastBal(lastBalAmt);
        acmAcBalInf.setAcLastUavaBal(lastUavaBal);
        acmAcBalInf.setAcCurBal(acBal);
        String curBalTag = BalanceTagUtil.createBalanceTag(acNo, capTyp, acBal);
        logger.info(curBalTag);
        acmAcBalInf.setAcBalTag(curBalTag);
        //判断执行结果
        iAcmAcBalDao.update(acmAcBalInf);


        //登记用户账户收支明细
        BeanUtils.copyProperties(acmAcDetailDO, accountingBO);
        acmAcDetailDO.setCcy(ACMConstants.SYS_DEFAULT_CCY);
        if (StringUtils.equals(dcFlg, ACMConstants.AC_D_FLG)) {
            acmAcDetailDO.setDrAmt(txAmt);
        } else {
            acmAcDetailDO.setCrAmt(txAmt);
        }
        acmAcDetailDO.setCurBal(acAcCurAcBal);
        acmAcDetailDO.setUpdBalFlg(ACMConstants.BAL_REAL_TIME_UPD);
        acmAcDetailDO.setAcDt(LemonUtils.getAccDate());
        iAcmAcDetailDao.insert(acmAcDetailDO);

        //如果为冲正，则修改原交易状态及冲正信息
        if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_CANCEL)) {
            //更新原明细流水的冲正流水号和冲正流水序号
            AcmAcDetailDO updAcmAcDetail = new AcmAcDetailDO();
            updAcmAcDetail.setTxJrnNo(accountingBO.getTxJrnNo());
            updAcmAcDetail.setAcNo(acNo);
            updAcmAcDetail.setCapTyp(capTyp);
            updAcmAcDetail.setAcDt(acDt);
            updAcmAcDetail.setTxAmt(txAmt);
            updAcmAcDetail.setTxSts(ACMConstants.ACCOUNTING_NOMARL);
            updAcmAcDetail.setRvsJrnNo(accountingBO.getJrnNo());
            updAcmAcDetail.setRvsJrnSeq(accountingBO.getJrnSeq());
            iAcmAcDetailDao.updateOriginalTransaction(updAcmAcDetail);
        }
    }

    /**
     * 科目账务处理
     */
    private void itemAccountingTreatment(AccountingBO accountingBO) {
        //检查余额记录
        String itmNo = accountingBO.getItmNo();
        String dcFlg = accountingBO.getDcFlg();
        LocalDate acDt = LemonUtils.getAccDate();
        BigDecimal txAmt = accountingBO.getTxAmt();
        String txSts = accountingBO.getTxSts();
        AcmItmBalDO acmItmBalDO = iAcmItmBalDao.get(itmNo);
        if (JudgeUtils.isNull(acmItmBalDO)) {
            if (StringUtils.equals(dcFlg, ACMConstants.AC_C_FLG)
                    && StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                managementService.createItemAccountBalance(itmNo);
                acmItmBalDO = iAcmItmBalDao.get(itmNo);
            } else {
                LemonException.throwBusinessException(ACMMessageCode.BAL_NOT_ENOUGH);
            }
        }

        //根据余额更新标志进行余额更新
        if (JudgeUtils.equals(ACMConstants.BAL_REAL_TIME_UPD, acmItmBalDO.getUpdBalFlg())) {
            //判断会计日
            LocalDate lastAcDate = acmItmBalDO.getUppDt();
            BigDecimal lastCrBal = acmItmBalDO.getLastCrBal();
            BigDecimal lastDrBal = acmItmBalDO.getLastDrBal();
            BigDecimal tdCrBal = acmItmBalDO.getTdCrBal();
            BigDecimal tdDrBal = acmItmBalDO.getTdDrBal();
            if (acDt.compareTo(lastAcDate) > 0) {
                lastAcDate = acDt;
                lastDrBal = acmItmBalDO.getTdDrBal();
                lastCrBal = acmItmBalDO.getTdCrBal();
                if (StringUtils.equals(dcFlg, ACMConstants.AC_C_FLG)) {
                    tdCrBal = txAmt;
                    tdDrBal = BigDecimal.valueOf(0,2);
                } else {

                    tdCrBal = BigDecimal.valueOf(0,2);
                    tdDrBal = txAmt;
                }
            } else if (acDt.compareTo(lastAcDate) < 0) {
                if (StringUtils.equals(dcFlg, ACMConstants.AC_C_FLG)) {
                    if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                        lastCrBal = lastCrBal.add(txAmt);
                    } else {
                        lastCrBal = lastCrBal.subtract(txAmt);
                    }
                } else {
                    if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                        lastDrBal = lastDrBal.add(txAmt);
                    } else {
                        lastDrBal = lastDrBal.subtract(txAmt);
                    }
                }
            } else {
                if (StringUtils.equals(dcFlg, ACMConstants.AC_C_FLG)) {
                    if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                        tdCrBal = tdCrBal.add(txAmt);
                    } else {
                        tdCrBal = tdCrBal.subtract(txAmt);
                    }
                } else {
                    if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_NOMARL)) {
                        tdDrBal = tdDrBal.add(txAmt);
                    } else {
                        tdDrBal = tdDrBal.subtract(txAmt);
                    }
                }
            }
            acmItmBalDO.setUppDt(lastAcDate);
            acmItmBalDO.setLastCrBal(lastCrBal);
            acmItmBalDO.setLastDrBal(lastDrBal);
            acmItmBalDO.setTdDrBal(tdDrBal);
            acmItmBalDO.setTdCrBal(tdCrBal);
            acmItmBalDO.setUppDt(LemonUtils.getAccDate());
            iAcmItmBalDao.update(acmItmBalDO);
        }

        //登记科目收支明细
        AcmItmDetailDO acmItmDetail = new AcmItmDetailDO();
        BeanUtils.copyProperties(acmItmDetail, accountingBO);
        acmItmDetail.setCcy(ACMConstants.SYS_DEFAULT_CCY);
        if (StringUtils.equals(dcFlg, ACMConstants.AC_D_FLG)) {
            acmItmDetail.setDrAmt(txAmt);
        } else {
            acmItmDetail.setCrAmt(txAmt);
        }
        acmItmDetail.setUpdBalFlg(acmItmBalDO.getUpdBalFlg());
        acmItmDetail.setAcDt(LemonUtils.getAccDate());
        iAcmItmDetailDao.insert(acmItmDetail);

        if (StringUtils.equals(txSts, ACMConstants.ACCOUNTING_CANCEL)) {
            //更新原明细流水的冲正流水号和冲正流水序号
            AcmItmDetailDO updAcmItmDetail = new AcmItmDetailDO();
            updAcmItmDetail.setTxJrnNo(accountingBO.getTxJrnNo());
            updAcmItmDetail.setItmNo(itmNo);
            updAcmItmDetail.setAcDt(acDt);
            updAcmItmDetail.setTxAmt(txAmt);
            updAcmItmDetail.setTxSts(ACMConstants.ACCOUNTING_NOMARL);
            updAcmItmDetail.setRvsJrnNo(accountingBO.getJrnNo());
            updAcmItmDetail.setRvsJrnSeq(accountingBO.getJrnSeq());
            iAcmItmDetailDao.updateOriginalTransaction(updAcmItmDetail);
        }
    }

    /**
     * 冻结用户账户的部分余额
     *
     * @return
     */
    @Override
    public String holdUserAccountBalance(AcmAcHoldDO acmAcHoldDO) {
        if (JudgeUtils.isBlank(acmAcHoldDO.getCcy())) {
            acmAcHoldDO.setCcy(ACMConstants.SYS_DEFAULT_CCY);
        }
        String holdNo = IdGenUtils.generateId("HOLD_NO", 10);
        acmAcHoldDO.setHldNo(holdNo);
        acmAcHoldDO.setEffDt(LemonUtils.getAccDate());
        acmAcHoldDO.setEffTm(DateTimeUtils.getCurrentLocalTime());
        acmAcHoldDO.setHldSts(ACMConstants.HOLDED);
        iAcmAcHoldDao.insert(acmAcHoldDO);
        String acNo = acmAcHoldDO.getAcNo();
        String capTyp = acmAcHoldDO.getCapTyp();
        if (JudgeUtils.isBlank(acNo)) {
            LemonException.throwLemonException(ACMMessageCode.ILLEGAL_AC_NO);
        }
        if (JudgeUtils.isBlank(capTyp)) {
            LemonException.throwLemonException(ACMMessageCode.CAP_TYP_IS_NULL);
        }
        AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
        acmAcBalDO.setAcNo(acNo);
        acmAcBalDO.setCapTyp(capTyp);
        acmAcBalDO = iAcmAcBalDao.getBalAndLock(acmAcBalDO);
        if (JudgeUtils.isNull(acmAcBalDO)) {
            LemonException.throwLemonException(ACMMessageCode.BAL_NOT_ENOUGH);
        }
        BigDecimal holdAmt = acmAcHoldDO.getHldBal();
        BigDecimal avaBal = acmAcBalDO.getAcCurBal().subtract(acmAcBalDO.getAcUavaBal());
        if (holdAmt.compareTo(avaBal) > 0) {
            LemonException.throwLemonException(ACMMessageCode.BAL_NOT_ENOUGH);
        }
        BigDecimal uavaBal = acmAcBalDO.getAcUavaBal().add(holdAmt);
        acmAcBalDO.setAcUavaBal(uavaBal);
        acmAcBalDO.setAcUpdDt(LemonUtils.getAccDate());
        acmAcBalDO.setAcUpdTm(DateTimeUtils.getCurrentLocalTime());
        //insert??
        iAcmAcBalDao.update(acmAcBalDO);
        logger.info("冻结金额成功");
        return holdNo;
    }

    /**
     * 解冻用户的冻结余额
     *
     * @return
     */
    @Override
    public void unHoldUserAccountBalance(String holdNo) {
        AcmAcHoldDO acmAcHoldDO = iAcmAcHoldDao.get(holdNo);
        if (JudgeUtils.notEquals(ACMConstants.HOLDED,acmAcHoldDO.getHldSts())) {
            LemonException.throwLemonException(ACMMessageCode.ORD_UNHOLDED);
        }
        String acNo = acmAcHoldDO.getAcNo();
        String capTyp = acmAcHoldDO.getCapTyp();
        AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
        acmAcBalDO.setAcNo(acNo);
        acmAcBalDO.setCapTyp(capTyp);
        acmAcBalDO = iAcmAcBalDao.getBalAndLock(acmAcBalDO);
        BigDecimal uavaBal = acmAcBalDO.getAcUavaBal();
        BigDecimal holdBal = acmAcHoldDO.getHldBal();
        if (holdBal.compareTo(uavaBal) > 0) {
            LemonException.throwLemonException(LemonException.SYS_ERROR_MSGCD);
        }
        uavaBal = uavaBal.subtract(holdBal);
        acmAcBalDO.setAcUavaBal(uavaBal);
        acmAcBalDO.setAcUpdDt(LemonUtils.getAccDate());
        acmAcBalDO.setAcUpdTm(DateTimeUtils.getCurrentLocalTime());
        //insert??
        iAcmAcBalDao.update(acmAcBalDO);

        //处理冻结表里的数据
        acmAcHoldDO.setHldSts(ACMConstants.UNHOLDED);
        iAcmAcHoldDao.update(acmAcHoldDO);
        logger.info("解冻金额成功");
    }
}
