/*
 * @ClassName AcmAcInfDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalTime;

public class AcmAcInfDO extends BaseDO {
    /**
     * acNo 账号
     */
    private String acNo;
    /**
     * ccy 币种
     */
    private String ccy;

    /**
     * ccyType 币种类型 FM-法币DM-数币
     */
    private String ccyType;
    /**
     * userId 用户ID
     */
    private String userId;
    /**
     * acSts 账户状态 0:开户 1:销户
     */
    private String acSts;
    /**
     * acCreDt 账户创建日期
     */
    private LocalDate acCreDt;
    /**
     * acCreTm 账户创建时间
     */
    private LocalTime acCreTm;
    /**
     * acClsDt 账户销户日期
     */
    private LocalDate acClsDt;
    /**
     * acClsTm 账户销户时间
     */
    private LocalTime acClsTm;
    /**
     * bank 开户行
     */
    private String bank;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAcSts() {
        return acSts;
    }

    public void setAcSts(String acSts) {
        this.acSts = acSts;
    }

    public LocalDate getAcCreDt() {
        return acCreDt;
    }

    public void setAcCreDt(LocalDate acCreDt) {
        this.acCreDt = acCreDt;
    }

    public LocalTime getAcCreTm() {
        return acCreTm;
    }

    public void setAcCreTm(LocalTime acCreTm) {
        this.acCreTm = acCreTm;
    }

    public LocalDate getAcClsDt() {
        return acClsDt;
    }

    public void setAcClsDt(LocalDate acClsDt) {
        this.acClsDt = acClsDt;
    }

    public LocalTime getAcClsTm() {
        return acClsTm;
    }

    public void setAcClsTm(LocalTime acClsTm) {
        this.acClsTm = acClsTm;
    }
    public String getCcyType() {
        return ccyType;
    }
    public void setCcyType(String ccyType) {
        this.ccyType = ccyType;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }
}