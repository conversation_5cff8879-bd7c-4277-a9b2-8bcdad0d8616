package com.hisun.lemon.acm.common.utils;

import com.hisun.lemon.acm.constants.ACMConstants;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @function BalanceTagUtil
 * @description 计算余额Tag值
 * @date 7/17/2017 Mon
 * @time 7:07 PM
 */
public class BalanceTagUtil {

    public static boolean checkBalanceTag(String acNo, String capTyp, BigDecimal bal, String tag) {
        String balanceTag;
        String amt = bal.toString();
        CryptUtilImpl crp = new CryptUtilImpl();
        String cryptSeed = acNo + capTyp + amt;
        balanceTag = crp.cryptMd5(cryptSeed, ACMConstants.AC_BAL_TAG_KEY);
        return StringUtils.equals(balanceTag, tag);
    }

    public static String createBalanceTag(String acNo, String capTyp, BigDecimal bal) {
        String amt = bal.toString();
        CryptUtilImpl crp = new CryptUtilImpl();
        String cryptSeed = acNo + capTyp + amt;
        return crp.cryptMd5(cryptSeed, ACMConstants.AC_BAL_TAG_KEY);
    }
}
