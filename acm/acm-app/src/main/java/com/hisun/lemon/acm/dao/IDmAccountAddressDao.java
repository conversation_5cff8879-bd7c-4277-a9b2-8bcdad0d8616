package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.DmAccountAddressDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/11 19:46
 */
@Mapper
public interface IDmAccountAddressDao extends BaseDao<DmAccountAddressDO> {

    /**
     * 根据账户编号查询数币账户地址信息
     *
     * @param acNo 账户编号
     * @return 账户信息
     */
    DmAccountAddressDO getByAcNo(String acNo);

    /**
     * 根据地址查询数币账户地址信息
     *
     * @param address 地址
     * @return 账户信息
     */
    DmAccountAddressDO getByAddress(String address);

    /**
     * 根据账户编号列表查询账户地址信息
     *
     * @param acNoList 账户编号列表
     * @param useType  使用类型
     * @return 账户信息
     */
    List<DmAccountAddressDO> findByAcNoListAndUseType(@Param("acNoList") List<String> acNoList,
                                                      @Param("useType") String useType);

    /**
     * 查询指定网络下ID最小的未启用地址记录,加锁
     *
     * @param network 网络
     * @return 账户地址记录
     */
    DmAccountAddressDO getMinDisabledAddress(String network);


    /**
     * 查询指定网络下两个ID最小的未启用地址记录,加锁
     *
     * @param network 网络
     * @return 账户地址记录
     */
    List<DmAccountAddressDO> getTwoMinDisabledAddress(@Param("network") String network);

    /**
     * 根据用户编号查询数币账户地址信息
     *
     * @param userId 网络
     * @return 账户地址记录
     */
    List<DmAccountAddressDO> getByUserIdAndNet(@Param("userId") String userId, @Param("network") String network);
}
