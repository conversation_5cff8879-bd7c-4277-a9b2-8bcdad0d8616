/*
 * @ClassName IAcmItmBalDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmAcBalDO;
import com.hisun.lemon.acm.entity.AcmItmBalDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IAcmItmBalDao extends BaseDao<AcmItmBalDO> {
    /**
     * 新增科目余额
     */
    public int insert(AcmAcBalDO acmAcBalDO);

    /**
     * 查询科目余额信息
     */
    public AcmItmBalDO get(String itmNo);

    /**
     * 更新科目余额
     */
    public int update(AcmItmBalDO acmItmBalDO);

    /**
     * 查询所有科目余额信息
     */
    public List<AcmItmBalDO> find();


    /**
     * 查询所有科目余额信息
     */
    public List<AcmItmBalDO> getAll();
}