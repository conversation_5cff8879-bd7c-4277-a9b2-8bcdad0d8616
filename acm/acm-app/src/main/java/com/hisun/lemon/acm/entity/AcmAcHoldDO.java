/*
 * @ClassName AcmAcHoldDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

public class AcmAcHoldDO extends BaseDO {
    /**
     * acNo 交易账号
     */
    private String acNo;
    /**
     * capTyp 账户资金属性
     */
    private String capTyp;
    /**
     * hldNo 冻结编号
     */
    private String hldNo;
    /**
     * ccy 交易币种
     */
    private String ccy;
    /**
     * userId 用户ID
     */
    private String userId;
    /**
     * userTyp 用户类别
     */
    private String userTyp;
    /**
     * hldSts 状态0：冻结；1：正常使用；2：到期自动处理
     */
    private String hldSts;
    /**
     * hldCd 冻结代码PY-预授权消费冻结TR-待确认转账冻结TX-提现冻结FP-发票红字质押冻结(只有一笔明细)OT-其他冻结
     */
    private String hldCd;
    /**
     * hldBal 冻结余额
     */
    private BigDecimal hldBal;
    /**
     * effDt 冻结生效日期
     */
    private LocalDate effDt;
    /**
     * effTm 冻结生效时间
     */
    private LocalTime effTm;
    /**
     * expDt 冻结失效日期
     */
    private LocalDate expDt;
    /**
     * expTm 冻结失效时间
     */
    private LocalTime expTm;
    /**
     * rlsDt 冻结释放日期
     */
    private LocalDate rlsDt;
    /**
     * rlsTm 冻结释放时间
     */
    private LocalTime rlsTm;
    /**
     * dueProcMod 到期处理方式0：到期释放；1：不自动释放
     */
    private String dueProcMod;
    /**
     * ordTyp 订单种类--TX_TYP
     */
    private String ordTyp;
    /**
     * ordNo 订单编号
     */
    private String ordNo;
    /**
     * rmk 备注
     */
    private String rmk;
    /**
     * rmk 资金类型
     */
    private String capTypEnum;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getHldNo() {
        return hldNo;
    }

    public void setHldNo(String hldNo) {
        this.hldNo = hldNo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserTyp() {
        return userTyp;
    }

    public void setUserTyp(String userTyp) {
        this.userTyp = userTyp;
    }

    public String getHldSts() {
        return hldSts;
    }

    public void setHldSts(String hldSts) {
        this.hldSts = hldSts;
    }

    public String getHldCd() {
        return hldCd;
    }

    public void setHldCd(String hldCd) {
        this.hldCd = hldCd;
    }

    public BigDecimal getHldBal() {
        return hldBal;
    }

    public void setHldBal(BigDecimal hldBal) {
        this.hldBal = hldBal;
    }

    public LocalDate getEffDt() {
        return effDt;
    }

    public void setEffDt(LocalDate effDt) {
        this.effDt = effDt;
    }

    public LocalTime getEffTm() {
        return effTm;
    }

    public void setEffTm(LocalTime effTm) {
        this.effTm = effTm;
    }

    public LocalDate getExpDt() {
        return expDt;
    }

    public void setExpDt(LocalDate expDt) {
        this.expDt = expDt;
    }

    public LocalTime getExpTm() {
        return expTm;
    }

    public void setExpTm(LocalTime expTm) {
        this.expTm = expTm;
    }

    public LocalDate getRlsDt() {
        return rlsDt;
    }

    public void setRlsDt(LocalDate rlsDt) {
        this.rlsDt = rlsDt;
    }

    public LocalTime getRlsTm() {
        return rlsTm;
    }

    public void setRlsTm(LocalTime rlsTm) {
        this.rlsTm = rlsTm;
    }

    public String getDueProcMod() {
        return dueProcMod;
    }

    public void setDueProcMod(String dueProcMod) {
        this.dueProcMod = dueProcMod;
    }

    public String getOrdTyp() {
        return ordTyp;
    }

    public void setOrdTyp(String ordTyp) {
        this.ordTyp = ordTyp;
    }

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getCapTypEnum() {
        return capTypEnum;
    }

    public void setCapTypEnum(String capTypEnum) {
        this.capTypEnum = capTypEnum;
    }
}