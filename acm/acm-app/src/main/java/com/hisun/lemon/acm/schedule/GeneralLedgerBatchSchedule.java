package com.hisun.lemon.acm.schedule;

import com.hisun.lemon.acm.service.IGeneralLedgerService;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @function GeneralLedgerBatchSchedule
 * @description 日终批量定时触发
 * @date 7/20/2017 Thu
 * @time 10:30 AM
 */
@Component
public class GeneralLedgerBatchSchedule {
    @Resource
    private IGeneralLedgerService iGeneralLedgerService;

    @BatchScheduled(cron = "0 30 1 * * *")
    public void GeneralLedgerBatch() {
        LocalDate acDt = LemonUtils.getAccDate().minusDays(1);
        iGeneralLedgerService.exportCapitalDetail(acDt);
//        iGeneralLedgerService.initializingGeneralLedger(acDt);
        iGeneralLedgerService.updateItemBalance(acDt);
        iGeneralLedgerService.checkAllVouchers(acDt);
//        iGeneralLedgerService.gatherVouchers(acDt);
//        iGeneralLedgerService.updateGeneralLedger(acDt);
        iGeneralLedgerService.checkGeneralLedger(acDt);
    }
}