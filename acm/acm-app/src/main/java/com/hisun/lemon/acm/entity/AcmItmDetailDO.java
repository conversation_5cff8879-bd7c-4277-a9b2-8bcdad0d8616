/*
 * @ClassName AcmItmDetailDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

public class AcmItmDetailDO extends BaseDO {
    /**
     * jrnNo 流水号
     */
    private String jrnNo;
    /**
     * jrnSeq 明细序号
     */
    private String jrnSeq;
    /**
     * acDt 会计日期
     */
    private LocalDate acDt;
    /**
     * txTyp 交易类型
     */
    private String txTyp;
    /**
     * txSts 交易状态
     */
    private String txSts;

    /**
     * itmNo 科目号
     */
    private String itmNo;
    /**
     * capTyp
     */
    private String capTyp;
    /**
     * dcFlg 借贷标志
     */
    private String dcFlg;
    /**
     * txAmt 交易金额
     */
    private BigDecimal txAmt;
    /**
     * ccy 币种
     */
    private String ccy;
    /**
     * hldNo 冻结编号
     */
    private String hldNo;
    /**
     * rvsJrnNo 冲正流水号
     */
    private String rvsJrnNo;
    /**
     * rvsJrnSeq 冲正流水明细序号
     */
    private String rvsJrnSeq;
    /**
     * drAmt 支出金额
     */
    private BigDecimal drAmt;
    /**
     * crAmt 收入金额
     */
    private BigDecimal crAmt;
    /**
     * odAmt 透支金额
     */
    private BigDecimal odAmt;
    /**
     * curBal 余额
     */
    private BigDecimal curBal;
    /**
     * updBalFlg 余额更新标志
     */
    private String updBalFlg;
    /**
     * txJrnNo 请求唯一流水号(堵重)
     */
    private String txJrnNo;
    /**
     * txOrdNo 请求订单号
     */
    private String txOrdNo;
    /**
     * txOrdDt 请求订单日期
     */
    private LocalDate txOrdDt;
    /**
     * txOrdTm 请求订单时间
     */
    private LocalTime txOrdTm;
    /**
     * oppAcNo 账号
     */
    private String oppAcNo;
    /**
     * oppCapTyp 资金属性
     */
    private String oppCapTyp;
    /**
     * oppUserId 用户号
     */
    private String oppUserId;
    /**
     * oppUserTyp 用户类型
     */
    private String oppUserTyp;
    /**
     * usrIpAdr 用户IP地址
     */
    private String usrIpAdr;
    /**
     * rmk 备注
     */
    private String rmk;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getJrnSeq() {
        return jrnSeq;
    }

    public void setJrnSeq(String jrnSeq) {
        this.jrnSeq = jrnSeq;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getTxSts() {
        return txSts;
    }

    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getDcFlg() {
        return dcFlg;
    }

    public void setDcFlg(String dcFlg) {
        this.dcFlg = dcFlg;
    }

    public BigDecimal getTxAmt() {
        return txAmt;
    }

    public void setTxAmt(BigDecimal txAmt) {
        this.txAmt = txAmt;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getHldNo() {
        return hldNo;
    }

    public void setHldNo(String hldNo) {
        this.hldNo = hldNo;
    }

    public String getRvsJrnNo() {
        return rvsJrnNo;
    }

    public void setRvsJrnNo(String rvsJrnNo) {
        this.rvsJrnNo = rvsJrnNo;
    }

    public String getRvsJrnSeq() {
        return rvsJrnSeq;
    }

    public void setRvsJrnSeq(String rvsJrnSeq) {
        this.rvsJrnSeq = rvsJrnSeq;
    }

    public BigDecimal getDrAmt() {
        return drAmt;
    }

    public void setDrAmt(BigDecimal drAmt) {
        this.drAmt = drAmt;
    }

    public BigDecimal getCrAmt() {
        return crAmt;
    }

    public void setCrAmt(BigDecimal crAmt) {
        this.crAmt = crAmt;
    }

    public BigDecimal getOdAmt() {
        return odAmt;
    }

    public void setOdAmt(BigDecimal odAmt) {
        this.odAmt = odAmt;
    }

    public BigDecimal getCurBal() {
        return curBal;
    }

    public void setCurBal(BigDecimal curBal) {
        this.curBal = curBal;
    }

    public String getUpdBalFlg() {
        return updBalFlg;
    }

    public void setUpdBalFlg(String updBalFlg) {
        this.updBalFlg = updBalFlg;
    }

    public String getTxJrnNo() {
        return txJrnNo;
    }

    public void setTxJrnNo(String txJrnNo) {
        this.txJrnNo = txJrnNo;
    }

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public LocalDate getTxOrdDt() {
        return txOrdDt;
    }

    public void setTxOrdDt(LocalDate txOrdDt) {
        this.txOrdDt = txOrdDt;
    }

    public LocalTime getTxOrdTm() {
        return txOrdTm;
    }

    public void setTxOrdTm(LocalTime txOrdTm) {
        this.txOrdTm = txOrdTm;
    }

    public String getOppAcNo() {
        return oppAcNo;
    }

    public void setOppAcNo(String oppAcNo) {
        this.oppAcNo = oppAcNo;
    }

    public String getOppCapTyp() {
        return oppCapTyp;
    }

    public void setOppCapTyp(String oppCapTyp) {
        this.oppCapTyp = oppCapTyp;
    }

    public String getOppUserId() {
        return oppUserId;
    }

    public void setOppUserId(String oppUserId) {
        this.oppUserId = oppUserId;
    }

    public String getOppUserTyp() {
        return oppUserTyp;
    }

    public void setOppUserTyp(String oppUserTyp) {
        this.oppUserTyp = oppUserTyp;
    }

    public String getUsrIpAdr() {
        return usrIpAdr;
    }

    public void setUsrIpAdr(String usrIpAdr) {
        this.usrIpAdr = usrIpAdr;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }
}