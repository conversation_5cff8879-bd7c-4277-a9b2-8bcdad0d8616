package com.hisun.lemon.acm.service;

import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @function IGeneralLedgerService
 * @description 总账服务
 * @date 7/15/2017 Sat
 * @time 12:15 PM
 */
public interface IGeneralLedgerService {
    /**
     * 导出每日资金明细
     * @param acDt
     * @return
     */
    public void exportCapitalDetail(LocalDate acDt);

    /**
     * 更新科目余额
     * @param acDt
     * @return
     */
    public void updateItemBalance(LocalDate acDt);

    /**
     * 初始化总账
     * @param acDt
     * @return
     */
//    public void initializingGeneralLedger(LocalDate acDt);

    /**
     * 当日传票汇总
     * @param acDt
     * @return
     */
//    public void gatherVouchers(LocalDate acDt);

    /**
     * 稽核总账(总分核对)
     * @param acDt
     * @return
     */
    public void checkGeneralLedger(LocalDate acDt);

    /**
     * 检查传票平衡
     * @param acDt
     * @return
     */
    public void checkAllVouchers(LocalDate acDt);

    /**
     * 科目数据清理
     * @param acDt
     * @return
     */
    public void clearItemAccountData(LocalDate acDt);


}
