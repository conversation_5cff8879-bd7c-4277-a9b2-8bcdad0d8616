/*
 * @ClassName IAcmItmDetailSumDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmItmDetailSumDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface IAcmItmDetailSumDao extends BaseDao<AcmItmDetailSumDO> {
    /**
     * 初始化科目资金汇总信息
     */
    public int updateItmDetailSum(LocalDate acDt);

    /**
     * 获取当日科目的交易金额和笔数
     */
    public List<AcmItmDetailSumDO> find(LocalDate acDt);
}