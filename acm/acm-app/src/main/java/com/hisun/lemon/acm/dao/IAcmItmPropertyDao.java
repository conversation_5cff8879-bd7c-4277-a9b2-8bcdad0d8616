/*
 * @ClassName IAcmItmProperDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-20 20:40:00
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmItmProperty;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAcmItmPropertyDao extends BaseDao<AcmItmProperty> {

    /**
     * 查询分户账对应的总账科目
     */
    public AcmItmProperty get(@Param("capTyp") String capTyp, @Param("ccy") String ccy);
}