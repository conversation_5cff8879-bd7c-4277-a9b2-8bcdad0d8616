package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.DmTransferCallbackDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数币交易回调记录Dao
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 10:49
 */
@Mapper
public interface IDmTransferCallbackDao {
    /**
     * 根据ID查询回调记录
     *
     * @param id 主键ID
     * @return 回调记录实体
     */
    DmTransferCallbackDO get(@Param("id") String id);

    /**
     * 动态查询回调记录
     *
     * @param dmTransferCallbackDO 查询条件
     * @return 回调记录列表
     */
    List<DmTransferCallbackDO> find(DmTransferCallbackDO dmTransferCallbackDO);

    /**
     * 查询数币账户收款/充值总记录数
     *
     * @param accountId 账户ID
     * @param direction 交易方向
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 记录总数
     */
    long countReceipts(@Param("accountId") String accountId,
                       @Param("direction") String direction,
                       @Param("startTime") LocalDateTime startTime,
                       @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询数币账户收款/充值记录
     *
     * @param orderNos 订单号集合
     * @param pageSize 分页大小
     * @param offset   分页偏移量
     * @return 回调记录列表
     */
    List<DmTransferCallbackDO> findReceipts(@Param("orderNos") List<String> orderNos,
                                            @Param("pageSize") int pageSize,
                                            @Param("offset") int offset);

    /**
     * 插入回调记录
     *
     * @param dmTransferCallbackDO 回调记录实体
     */
    void insert(DmTransferCallbackDO dmTransferCallbackDO);

    /**
     * 更新回调记录
     *
     * @param dmTransferCallbackDO 回调记录实体
     */
    void update(DmTransferCallbackDO dmTransferCallbackDO);

    /**
     * 删除回调记录
     *
     * @param id 主键ID
     */
    void delete(@Param("id") String id);
}
