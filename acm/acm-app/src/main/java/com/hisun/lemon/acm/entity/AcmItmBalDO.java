/*
 * @ClassName AcmItmBalDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;

public class AcmItmBalDO extends BaseDO {
    /**
     * itmNo 科目号
     */
    private String itmNo;
    /**
     * ccy 币种
     */
    private String ccy;
    /**
     * itmCnm 科目中文名称
     */
    private String itmCnm;
    /**
     * itmLvl 科目级别（1-一级科目、2-二级科目、3-三级科目）
     */
    private String itmLvl;
    /**
     * itmCls 科目分类（1-存放银行资金池账户、2-差错/争议挂账账户、3-其他内部账户）
     */
    private String itmCls;
    /**
     * balOdFlg 余额是否允许透支标志（Y-可透支、N-不可透支）
     */
    private String balOdFlg;
    /**
     * balDrt 余额方向（A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）
     */
    private String balDrt;
    /**
     * updBalFlg 余额更新方式 0：实时更新1：批量更新
     */
    private String updBalFlg;
    /**
     * uppDt 最新更新日期
     */
    private LocalDate uppDt;
    /**
     * upItmNo 上级科目号
     */
    private String upItmNo;
    /**
     * lastDrBal 上日借方余额
     */
    private BigDecimal lastDrBal;
    /**
     * lastCrBal 上日贷方余额
     */
    private BigDecimal lastCrBal;
    /**
     * tdDrAmt 当日借方金额
     */
    private BigDecimal tdDrAmt;
    /**
     * tdCrAmt 当日贷方金额
     */
    private BigDecimal tdCrAmt;
    /**
     * tdDrNum 当日借方笔数
     */
    private Integer tdDrNum;
    /**
     * tdCrNum 当日贷方笔数
     */
    private Integer tdCrNum;
    /**
     * monDrAmt 当月借方金额
     */
    private BigDecimal monDrAmt;
    /**
     * monCrAmt 当月贷方金额
     */
    private BigDecimal monCrAmt;
    /**
     * monDrNum 当月借方笔数
     */
    private Integer monDrNum;
    /**
     * monCrNum 当月贷方笔数
     */
    private Integer monCrNum;
    /**
     * tdDrBal 当日借方余额
     */
    private BigDecimal tdDrBal;
    /**
     * tdCrBal 当日贷方余额
     */
    private BigDecimal tdCrBal;
    /**
     * glSts 余额状态
     */
    private String glSts;

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getItmCnm() {
        return itmCnm;
    }

    public void setItmCnm(String itmCnm) {
        this.itmCnm = itmCnm;
    }

    public String getItmLvl() {
        return itmLvl;
    }

    public void setItmLvl(String itmLvl) {
        this.itmLvl = itmLvl;
    }

    public String getItmCls() {
        return itmCls;
    }

    public void setItmCls(String itmCls) {
        this.itmCls = itmCls;
    }

    public String getBalOdFlg() {
        return balOdFlg;
    }

    public void setBalOdFlg(String balOdFlg) {
        this.balOdFlg = balOdFlg;
    }

    public String getBalDrt() {
        return balDrt;
    }

    public void setBalDrt(String balDrt) {
        this.balDrt = balDrt;
    }

    public String getUpdBalFlg() {
        return updBalFlg;
    }

    public void setUpdBalFlg(String updBalFlg) {
        this.updBalFlg = updBalFlg;
    }

    public LocalDate getUppDt() {
        return uppDt;
    }

    public void setUppDt(LocalDate uppDt) {
        this.uppDt = uppDt;
    }

    public String getUpItmNo() {
        return upItmNo;
    }

    public void setUpItmNo(String upItmNo) {
        this.upItmNo = upItmNo;
    }

    public BigDecimal getLastDrBal() {
        return lastDrBal;
    }

    public void setLastDrBal(BigDecimal lastDrBal) {
        this.lastDrBal = lastDrBal;
    }

    public BigDecimal getLastCrBal() {
        return lastCrBal;
    }

    public void setLastCrBal(BigDecimal lastCrBal) {
        this.lastCrBal = lastCrBal;
    }

    public BigDecimal getTdDrAmt() {
        return tdDrAmt;
    }

    public void setTdDrAmt(BigDecimal tdDrAmt) {
        this.tdDrAmt = tdDrAmt;
    }

    public BigDecimal getTdCrAmt() {
        return tdCrAmt;
    }

    public void setTdCrAmt(BigDecimal tdCrAmt) {
        this.tdCrAmt = tdCrAmt;
    }

    public Integer getTdDrNum() {
        return tdDrNum;
    }

    public void setTdDrNum(Integer tdDrNum) {
        this.tdDrNum = tdDrNum;
    }

    public Integer getTdCrNum() {
        return tdCrNum;
    }

    public void setTdCrNum(Integer tdCrNum) {
        this.tdCrNum = tdCrNum;
    }

    public BigDecimal getMonDrAmt() {
        return monDrAmt;
    }

    public void setMonDrAmt(BigDecimal monDrAmt) {
        this.monDrAmt = monDrAmt;
    }

    public BigDecimal getMonCrAmt() {
        return monCrAmt;
    }

    public void setMonCrAmt(BigDecimal monCrAmt) {
        this.monCrAmt = monCrAmt;
    }

    public Integer getMonDrNum() {
        return monDrNum;
    }

    public void setMonDrNum(Integer monDrNum) {
        this.monDrNum = monDrNum;
    }

    public Integer getMonCrNum() {
        return monCrNum;
    }

    public void setMonCrNum(Integer monCrNum) {
        this.monCrNum = monCrNum;
    }

    public BigDecimal getTdDrBal() {
        return tdDrBal;
    }

    public void setTdDrBal(BigDecimal tdDrBal) {
        this.tdDrBal = tdDrBal;
    }

    public BigDecimal getTdCrBal() {
        return tdCrBal;
    }

    public void setTdCrBal(BigDecimal tdCrBal) {
        this.tdCrBal = tdCrBal;
    }

    public String getGlSts() {
        return glSts;
    }

    public void setGlSts(String glSts) {
        this.glSts = glSts;
    }
}