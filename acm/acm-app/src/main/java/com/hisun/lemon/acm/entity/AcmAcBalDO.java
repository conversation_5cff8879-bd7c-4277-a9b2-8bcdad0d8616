/*
 * @ClassName AcmAcBalDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 16:10:53
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

public class AcmAcBalDO extends BaseDO {
    /**
     * acNo 账号
     */
    private String acNo;
    /**
     * capTyp 资金属性 1：现金 8：待结算资金
     */
    private String capTyp;
    /**
     * ccy 币种
     */
    private String ccy;
    /**
     * userId 用户ID
     */
    private String userId;
    /**
     * acCurBal 账户当前可用余额
     */
    private BigDecimal acCurBal;
    /**
     * acUavaBal 账户当前不可用余额
     */
    private BigDecimal acUavaBal;
    /**
     * acLastBal 账户上一日余额
     */
    private BigDecimal acLastBal;
    /**
     * acLastUavaBal 账户上日不可用余额
     */
    private BigDecimal acLastUavaBal;
    /**
     * acBalTag 余额tag
     */
    private String acBalTag;
    /**
     * acUpdDt 余额最新变动日期
     */
    private LocalDate acUpdDt;
    /**
     * acUpdTm 余额最新变动时间
     */
    private LocalTime acUpdTm;
    /**
     * acFrzDt 账户余额冻结日期
     */
    private LocalDate acFrzDt;
    /**
     * acFrzTm 账户余额冻结时间
     */
    private LocalTime acFrzTm;
    /**
     * rmk 备注
     */
    private String rmk;

    /**
     * 结算冻结金额
     */
    private BigDecimal acCurFreezeBal;

    /**
     * 网络(数币账户专有)
     */
    private String network;

    public BigDecimal getAcCurFreezeBal() {
        return acCurFreezeBal;
    }

    public void setAcCurFreezeBal(BigDecimal acCurFreezeBal) {
        this.acCurFreezeBal = acCurFreezeBal;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getAcCurBal() {
        return acCurBal;
    }

    public void setAcCurBal(BigDecimal acCurBal) {
        this.acCurBal = acCurBal;
    }

    public BigDecimal getAcUavaBal() {
        return acUavaBal;
    }

    public void setAcUavaBal(BigDecimal acUavaBal) {
        this.acUavaBal = acUavaBal;
    }

    public BigDecimal getAcLastBal() {
        return acLastBal;
    }

    public void setAcLastBal(BigDecimal acLastBal) {
        this.acLastBal = acLastBal;
    }

    public BigDecimal getAcLastUavaBal() {
        return acLastUavaBal;
    }

    public void setAcLastUavaBal(BigDecimal acLastUavaBal) {
        this.acLastUavaBal = acLastUavaBal;
    }

    public String getAcBalTag() {
        return acBalTag;
    }

    public void setAcBalTag(String acBalTag) {
        this.acBalTag = acBalTag;
    }

    public LocalDate getAcUpdDt() {
        return acUpdDt;
    }

    public void setAcUpdDt(LocalDate acUpdDt) {
        this.acUpdDt = acUpdDt;
    }

    public LocalTime getAcUpdTm() {
        return acUpdTm;
    }

    public void setAcUpdTm(LocalTime acUpdTm) {
        this.acUpdTm = acUpdTm;
    }

    public LocalDate getAcFrzDt() {
        return acFrzDt;
    }

    public void setAcFrzDt(LocalDate acFrzDt) {
        this.acFrzDt = acFrzDt;
    }

    public LocalTime getAcFrzTm() {
        return acFrzTm;
    }

    public void setAcFrzTm(LocalTime acFrzTm) {
        this.acFrzTm = acFrzTm;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }
}