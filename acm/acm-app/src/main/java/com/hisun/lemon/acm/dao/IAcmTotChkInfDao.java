/*
 * @ClassName IAcmTotChkInfDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 11:21:24
 */
package com.hisun.lemon.acm.dao;

import com.hisun.lemon.acm.entity.AcmTotChkInfDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

@Mapper
public interface IAcmTotChkInfDao extends BaseDao<AcmTotChkInfDO> {

    /**
     * 查询总账检查明细
     */
    public AcmTotChkInfDO get(@Param("itmNo") String itmNo, @Param("acDt") LocalDate acDt);
}