package com.hisun.lemon.acm.bo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @function LedgerBalanceBo
 * @description 分户账余额统计
 * @date 7/24/2017 Mon
 * @time 10:52 AM
 */
public class LedgerBalanceBo {
    /**
     * 资金类型
     */
    private String capTyp;

    /**
     * 币种
     */
    private String ccy;

    /**
     * 分户账余额汇总
     */
    private BigDecimal totBal;

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getTotBal() {
        return totBal;
    }

    public void setTotBal(BigDecimal totBal) {
        this.totBal = totBal;
    }
}
