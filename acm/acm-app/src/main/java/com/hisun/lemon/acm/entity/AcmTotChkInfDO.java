/*
 * @ClassName AcmTotChkInfDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 11:21:24
 */
package com.hisun.lemon.acm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;

public class AcmTotChkInfDO extends BaseDO {
    /**
     * itmNo 科目号
     */
    private String itmNo;
    /**
     * acDt 会计日
     */
    private LocalDate acDt;
    /**
     * ccy 币种
     */
    private String ccy;
    /**
     * balDrt 余额方向（A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）
     */
    private String balDrt;
    /**
     * ldgDrBal 分户借方余额
     */
    private BigDecimal ldgDrBal;
    /**
     * ldgCrBal 分户贷方余额
     */
    private BigDecimal ldgCrBal;
    /**
     * glDrBl 总账借方余额
     */
    private BigDecimal glDrBl;
    /**
     * glCrBl 总账贷方户余额
     */
    private BigDecimal glCrBl;
    /**
     * chkRsl 核对结果0：正确1：错误2：未核对（总分核对完成后如果为此状态，说明总账余额不存在）
     */
    private String chkRsl;

    public String getItmNo() {
        return itmNo;
    }

    public void setItmNo(String itmNo) {
        this.itmNo = itmNo;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getBalDrt() {
        return balDrt;
    }

    public void setBalDrt(String balDrt) {
        this.balDrt = balDrt;
    }

    public BigDecimal getLdgDrBal() {
        return ldgDrBal;
    }

    public void setLdgDrBal(BigDecimal ldgDrBal) {
        this.ldgDrBal = ldgDrBal;
    }

    public BigDecimal getLdgCrBal() {
        return ldgCrBal;
    }

    public void setLdgCrBal(BigDecimal ldgCrBal) {
        this.ldgCrBal = ldgCrBal;
    }

    public BigDecimal getGlDrBl() {
        return glDrBl;
    }

    public void setGlDrBl(BigDecimal glDrBl) {
        this.glDrBl = glDrBl;
    }

    public BigDecimal getGlCrBl() {
        return glCrBl;
    }

    public void setGlCrBl(BigDecimal glCrBl) {
        this.glCrBl = glCrBl;
    }

    public String getChkRsl() {
        return chkRsl;
    }

    public void setChkRsl(String chkRsl) {
        this.chkRsl = chkRsl;
    }
}