<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmTotChkInfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmTotChkInfDO" >
        <id column="ITM_NO" property="itmNo" jdbcType="VARCHAR" />
        <id column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="CCY" property="ccy" jdbcType="VARCHAR" />
        <result column="BAL_DRT" property="balDrt" jdbcType="VARCHAR" />
        <result column="LDG_DR_BAL" property="ldgDrBal" jdbcType="DECIMAL" />
        <result column="LDG_CR_BAL" property="ldgCrBal" jdbcType="DECIMAL" />
        <result column="GL_DR_BL" property="glDrBl" jdbcType="DECIMAL" />
        <result column="GL_CR_BL" property="glCrBl" jdbcType="DECIMAL" />
        <result column="CHK_RSL" property="chkRsl" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ITM_NO, AC_DT, CCY, BAL_DRT, LDG_DR_BAL, LDG_CR_BAL, GL_DR_BL, GL_CR_BL, CHK_RSL
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from acm_tot_chkinf
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
          and AC_DT = #{acDt,jdbcType=DATE}
    </select>

    <delete id="delete" parameterType="map" >
        delete from acm_tot_chkinf
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
          and AC_DT = #{acDt,jdbcType=DATE}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmTotChkInfDO" >
        insert into acm_tot_chkinf
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="itmNo != null" >
                ITM_NO,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="ccy != null" >
                CCY,
            </if>
            <if test="balDrt != null" >
                BAL_DRT,
            </if>
            <if test="ldgDrBal != null" >
                LDG_DR_BAL,
            </if>
            <if test="ldgCrBal != null" >
                LDG_CR_BAL,
            </if>
            <if test="glDrBl != null" >
                GL_DR_BL,
            </if>
            <if test="glCrBl != null" >
                GL_CR_BL,
            </if>
            <if test="chkRsl != null" >
                CHK_RSL,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="itmNo != null" >
                #{itmNo,jdbcType=VARCHAR},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="balDrt != null" >
                #{balDrt,jdbcType=VARCHAR},
            </if>
            <if test="ldgDrBal != null" >
                #{ldgDrBal,jdbcType=DECIMAL},
            </if>
            <if test="ldgCrBal != null" >
                #{ldgCrBal,jdbcType=DECIMAL},
            </if>
            <if test="glDrBl != null" >
                #{glDrBl,jdbcType=DECIMAL},
            </if>
            <if test="glCrBl != null" >
                #{glCrBl,jdbcType=DECIMAL},
            </if>
            <if test="chkRsl != null" >
                #{chkRsl,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.acm.entity.AcmTotChkInfDO" >
        update acm_tot_chkinf
        <set >
            <if test="ccy != null" >
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="balDrt != null" >
                BAL_DRT = #{balDrt,jdbcType=VARCHAR},
            </if>
            <if test="ldgDrBal != null" >
                LDG_DR_BAL = #{ldgDrBal,jdbcType=DECIMAL},
            </if>
            <if test="ldgCrBal != null" >
                LDG_CR_BAL = #{ldgCrBal,jdbcType=DECIMAL},
            </if>
            <if test="glDrBl != null" >
                GL_DR_BL = #{glDrBl,jdbcType=DECIMAL},
            </if>
            <if test="glCrBl != null" >
                GL_CR_BL = #{glCrBl,jdbcType=DECIMAL},
            </if>
            <if test="chkRsl != null" >
                CHK_RSL = #{chkRsl,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
          and AC_DT = #{acDt,jdbcType=DATE}
    </update>
</mapper>