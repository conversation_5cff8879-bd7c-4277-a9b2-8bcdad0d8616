<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmItmPropertyDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmItmProperty" >
        <id column="CAP_TYP" property="capTyp" jdbcType="VARCHAR" />
        <id column="CCY" property="ccy" jdbcType="VARCHAR" />
        <result column="ITM_NO" property="itmNo" jdbcType="VARCHAR" />
        <result column="BAL_DRT" property="balDrt" jdbcType="VARCHAR" />
        <result column="UPD_OPR" property="updOpr" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        CAP_TYP, CCY, ITM_NO, BAL_DRT, UPD_OPR
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from acm_itm_property
        where CAP_TYP = #{capTyp,jdbcType=VARCHAR}
          and CCY = #{ccy,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="map" >
        delete from acm_itm_property
        where CAP_TYP = #{capTyp,jdbcType=VARCHAR}
          and CCY = #{ccy,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmItmProperty" >
        insert into acm_itm_property
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="capTyp != null" >
                CAP_TYP,
            </if>
            <if test="ccy != null" >
                CCY,
            </if>
            <if test="itmNo != null" >
                ITM_NO,
            </if>
            <if test="balDrt != null" >
                BAL_DRT,
            </if>
            <if test="updOpr != null" >
                UPD_OPR,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="capTyp != null" >
                #{capTyp,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="itmNo != null" >
                #{itmNo,jdbcType=VARCHAR},
            </if>
            <if test="balDrt != null" >
                #{balDrt,jdbcType=VARCHAR},
            </if>
            <if test="updOpr != null" >
                #{updOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.acm.entity.AcmItmProperty" >
        update acm_itm_property
        <set >
            <if test="itmNo != null" >
                ITM_NO = #{itmNo,jdbcType=VARCHAR},
            </if>
            <if test="balDrt != null" >
                BAL_DRT = #{balDrt,jdbcType=VARCHAR},
            </if>
            <if test="updOpr != null" >
                UPD_OPR = #{updOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where CAP_TYP = #{capTyp,jdbcType=VARCHAR}
          and CCY = #{ccy,jdbcType=VARCHAR}
    </update>
</mapper>