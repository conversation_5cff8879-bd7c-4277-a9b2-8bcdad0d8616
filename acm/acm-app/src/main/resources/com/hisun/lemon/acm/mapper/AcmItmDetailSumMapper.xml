<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmItmDetailSumDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmItmDetailSumDO" >
        <id column="AC_DT" property="acDt" jdbcType="DATE" />
        <id column="ITM_NO" property="itmNo" jdbcType="VARCHAR" />
        <result column="CCY" property="ccy" jdbcType="VARCHAR" />
        <result column="TD_DR_AMT" property="tdDrAmt" jdbcType="DECIMAL" />
        <result column="TD_CR_AMT" property="tdCrAmt" jdbcType="DECIMAL" />
        <result column="TD_DR_NUM" property="tdDrNum" jdbcType="INTEGER" />
        <result column="TD_CR_NUM" property="tdCrNum" jdbcType="INTEGER" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        AC_DT, ITM_NO, CCY, TD_DR_AMT, TD_CR_AMT, TD_DR_NUM, TD_CR_NUM
    </sql>

    <select id="find" resultMap="BaseResultMap" parameterType="java.time.LocalDate" >
        select 
        <include refid="Base_Column_List" />
        from acm_itm_detail_sum
        where AC_DT = #{acDt,jdbcType=DATE}
    </select>

    <update id="updateItmDetailSum" parameterType="java.time.LocalDate">
        INSERT INTO acm_itm_detail_sum (AC_DT, ITM_NO,CCY,TD_CR_AMT,TD_CR_NUM,TD_DR_AMT,TD_DR_NUM)
            SELECT AC_DT,ITM_NO, CCY,
            SUM(CR_AMT) TOT_CR_AMT,
            COUNT( CASE WHEN CR_AMT=0 THEN NULL ELSE CR_AMT END) TOT_CR_NUM,
            SUM(DR_AMT) TOT_DR_AMT,
            COUNT( CASE WHEN DR_AMT=0 THEN NULL ELSE DR_AMT END ) TOT_DR_NUM
            FROM acm_itm_detail
            WHERE AC_DT = #{acDt,jdbcType=DATE} AND TX_STS = 'N'
            GROUP BY ITM_NO, CCY
    </update>
</mapper>