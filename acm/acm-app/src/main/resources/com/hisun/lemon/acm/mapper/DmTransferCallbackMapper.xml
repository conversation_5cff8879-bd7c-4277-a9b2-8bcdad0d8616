<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.acm.dao.IDmTransferCallbackDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.DmTransferCallbackDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tx_base_network" property="txBaseNetwork" jdbcType="VARCHAR"/>
        <result column="tx_base_block_id" property="txBaseBlockId" jdbcType="BIGINT"/>
        <result column="tx_base_tx_id" property="txBaseTxId" jdbcType="VARCHAR"/>
        <result column="tx_base_ecode" property="txBaseEcode" jdbcType="VARCHAR"/>
        <result column="tx_base_group_id" property="txBaseGroupId" jdbcType="VARCHAR"/>
        <result column="tx_base_fee" property="txBaseFee" jdbcType="DECIMAL"/>
        <result column="tx_base_status" property="txBaseStatus" jdbcType="VARCHAR"/>
        <result column="tx_base_create_time" property="txBaseCreateTime" jdbcType="TIMESTAMP"/>
        <result column="tx_base_block_hash" property="txBaseBlockHash" jdbcType="VARCHAR"/>
        <result column="tx_base_program_id" property="txBaseProgramId" jdbcType="VARCHAR"/>
        <result column="tx_base_compute_units_consumed" property="txBaseComputeUnitsConsumed" jdbcType="BIGINT"/>
        <result column="tx_base_memo" property="txBaseMemo" jdbcType="VARCHAR"/>
        <result column="vault_code" property="vaultCode" jdbcType="VARCHAR"/>
        <result column="account_id" property="accountId" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="VARCHAR"/>
        <result column="bil_order_no" property="bilOrderNo" jdbcType="VARCHAR"/>
        <result column="coin_id" property="coinId" jdbcType="VARCHAR"/>
        <result column="network" property="network" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="cp_address" property="cpAddress" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="direction" property="direction" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="callback_time" property="callbackTime" jdbcType="TIMESTAMP"/>
        <result column="is_processed" property="isProcessed" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基本列定义 -->
    <sql id="Base_Column_List">
        id,
        tx_base_network,
        tx_base_block_id,
        tx_base_tx_id,
        tx_base_ecode,
        tx_base_group_id,
        tx_base_fee,
        tx_base_status,
        tx_base_create_time,
        tx_base_block_hash,
        tx_base_program_id,
        tx_base_compute_units_consumed,
        tx_base_memo,
        vault_code,
        account_id,
        order_id,
        bil_order_no,
        coin_id,
        network,
        address,
        cp_address,
        amount,
        direction,
        channel,
        callback_time,
        is_processed
    </sql>

    <!-- 根据 id 查询 -->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_transfer_callback
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 动态查询 -->
    <select id="find" parameterType="com.hisun.lemon.acm.entity.DmTransferCallbackDO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_transfer_callback
        <where>
            <if test="id != null">
                AND id = #{id,jdbcType=BIGINT}
            </if>
            <if test="txBaseNetwork != null">
                AND tx_base_network = #{txBaseNetwork,jdbcType=VARCHAR}
            </if>
            <if test="txBaseBlockId != null">
                AND tx_base_block_id = #{txBaseBlockId,jdbcType=BIGINT}
            </if>
            <if test="txBaseTxId != null">
                AND tx_base_tx_id = #{txBaseTxId,jdbcType=VARCHAR}
            </if>
            <if test="txBaseEcode != null">
                AND tx_base_ecode = #{txBaseEcode,jdbcType=VARCHAR}
            </if>
            <if test="txBaseGroupId != null">
                AND tx_base_group_id = #{txBaseGroupId,jdbcType=VARCHAR}
            </if>
            <if test="txBaseFee != null">
                AND tx_base_fee = #{txBaseFee,jdbcType=DECIMAL}
            </if>
            <if test="txBaseStatus != null">
                AND tx_base_status = #{txBaseStatus,jdbcType=VARCHAR}
            </if>
            <if test="txBaseCreateTime != null">
                AND tx_base_create_time = #{txBaseCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="txBaseBlockHash != null">
                AND tx_base_block_hash = #{txBaseBlockHash,jdbcType=VARCHAR}
            </if>
            <if test="txBaseProgramId != null">
                AND tx_base_program_id = #{txBaseProgramId,jdbcType=VARCHAR}
            </if>
            <if test="txBaseComputeUnitsConsumed != null">
                AND tx_base_compute_units_consumed = #{txBaseComputeUnitsConsumed,jdbcType=BIGINT}
            </if>
            <if test="txBaseMemo != null">
                AND tx_base_memo = #{txBaseMemo,jdbcType=VARCHAR}
            </if>
            <if test="vaultCode != null">
                AND vault_code = #{vaultCode,jdbcType=VARCHAR}
            </if>
            <if test="accountId != null">
                AND account_id = #{accountId,jdbcType=BIGINT}
            </if>
            <if test="orderId != null">
                AND order_id = #{orderId,jdbcType=VARCHAR}
            </if>
            <if test="bilOrderNo != null">
                AND bil_order_no = #{bilOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="coinId != null">
                AND coin_id = #{coinId,jdbcType=VARCHAR}
            </if>
            <if test="network != null">
                AND network = #{network,jdbcType=VARCHAR}
            </if>
            <if test="address != null">
                AND address = #{address,jdbcType=VARCHAR}
            </if>
            <if test="cpAddress != null">
                AND cp_address = #{cpAddress,jdbcType=VARCHAR}
            </if>
            <if test="amount != null">
                AND amount = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="direction != null">
                AND direction = #{direction,jdbcType=VARCHAR}
            </if>
            <if test="channel != null">
                AND channel = #{channel,jdbcType=VARCHAR}
            </if>
            <if test="callbackTime != null">
                AND callback_time = #{callbackTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isProcessed != null">
                AND is_processed = #{isProcessed,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <!--查询数币账户收款/充值总记录数-->
    <select id="countReceipts" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM dm_transfer_callback
        <where>
            <if test="accountId != null">
                AND account_id = #{accountId,jdbcType=BIGINT}
            </if>
            <if test="direction != null">
                AND direction = #{direction,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                AND tx_base_create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND tx_base_create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <!--分页查询数币账户收款/充值记录-->
    <select id="findReceipts" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_transfer_callback
        <where>
            <if test="orderNos != null and orderNos.size() > 0">
                AND bil_order_no IN
                <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
                    #{orderNo,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        ORDER BY tx_base_create_time DESC
        LIMIT #{pageSize,jdbcType=INTEGER} OFFSET #{offset,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.DmTransferCallbackDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dm_transfer_callback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="txBaseNetwork != null">
                tx_base_network,
            </if>
            <if test="txBaseBlockId != null">
                tx_base_block_id,
            </if>
            <if test="txBaseTxId != null">
                tx_base_tx_id,
            </if>
            <if test="txBaseEcode != null">
                tx_base_ecode,
            </if>
            <if test="txBaseGroupId != null">
                tx_base_group_id,
            </if>
            <if test="txBaseFee != null">
                tx_base_fee,
            </if>
            <if test="txBaseStatus != null">
                tx_base_status,
            </if>
            <if test="txBaseCreateTime != null">
                tx_base_create_time,
            </if>
            <if test="txBaseBlockHash != null">
                tx_base_block_hash,
            </if>
            <if test="txBaseProgramId != null">
                tx_base_program_id,
            </if>
            <if test="txBaseComputeUnitsConsumed != null">
                tx_base_compute_units_consumed,
            </if>
            <if test="txBaseMemo != null">
                tx_base_memo,
            </if>
            <if test="vaultCode != null">
                vault_code,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="bilOrderNo != null">
                bil_order_no,
            </if>
            <if test="coinId != null">
                coin_id,
            </if>
            <if test="network != null">
                network,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="cpAddress != null">
                cp_address,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="direction != null">
                direction,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="callbackTime != null">
                callback_time,
            </if>
            <if test="isProcessed != null">
                is_processed,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="txBaseNetwork != null">
                #{txBaseNetwork,jdbcType=VARCHAR},
            </if>
            <if test="txBaseBlockId != null">
                #{txBaseBlockId,jdbcType=BIGINT},
            </if>
            <if test="txBaseTxId != null">
                #{txBaseTxId,jdbcType=VARCHAR},
            </if>
            <if test="txBaseEcode != null">
                #{txBaseEcode,jdbcType=VARCHAR},
            </if>
            <if test="txBaseGroupId != null">
                #{txBaseGroupId,jdbcType=VARCHAR},
            </if>
            <if test="txBaseFee != null">
                #{txBaseFee,jdbcType=DECIMAL},
            </if>
            <if test="txBaseStatus != null">
                #{txBaseStatus,jdbcType=VARCHAR},
            </if>
            <if test="txBaseCreateTime != null">
                #{txBaseCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="txBaseBlockHash != null">
                #{txBaseBlockHash,jdbcType=VARCHAR},
            </if>
            <if test="txBaseProgramId != null">
                #{txBaseProgramId,jdbcType=VARCHAR},
            </if>
            <if test="txBaseComputeUnitsConsumed != null">
                #{txBaseComputeUnitsConsumed,jdbcType=BIGINT},
            </if>
            <if test="txBaseMemo != null">
                #{txBaseMemo,jdbcType=VARCHAR},
            </if>
            <if test="vaultCode != null">
                #{vaultCode,jdbcType=VARCHAR},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="bilOrderNo != null">
                #{bilOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="coinId != null">
                #{coinId,jdbcType=VARCHAR},
            </if>
            <if test="network != null">
                #{network,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="cpAddress != null">
                #{cpAddress,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="direction != null">
                #{direction,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="callbackTime != null">
                #{callbackTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isProcessed != null">
                #{isProcessed,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.hisun.lemon.acm.entity.DmTransferCallbackDO">
        UPDATE dm_transfer_callback
        <set>
            <if test="txBaseNetwork != null">
                tx_base_network = #{txBaseNetwork,jdbcType=VARCHAR},
            </if>
            <if test="txBaseBlockId != null">
                tx_base_block_id = #{txBaseBlockId,jdbcType=BIGINT},
            </if>
            <if test="txBaseTxId != null">
                tx_base_tx_id = #{txBaseTxId,jdbcType=VARCHAR},
            </if>
            <if test="txBaseEcode != null">
                tx_base_ecode = #{txBaseEcode,jdbcType=VARCHAR},
            </if>
            <if test="txBaseGroupId != null">
                tx_base_group_id = #{txBaseGroupId,jdbcType=VARCHAR},
            </if>
            <if test="txBaseFee != null">
                tx_base_fee = #{txBaseFee,jdbcType=DECIMAL},
            </if>
            <if test="txBaseStatus != null">
                tx_base_status = #{txBaseStatus,jdbcType=VARCHAR},
            </if>
            <if test="txBaseCreateTime != null">
                tx_base_create_time = #{txBaseCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="txBaseBlockHash != null">
                tx_base_block_hash = #{txBaseBlockHash,jdbcType=VARCHAR},
            </if>
            <if test="txBaseProgramId != null">
                tx_base_program_id = #{txBaseProgramId,jdbcType=VARCHAR},
            </if>
            <if test="txBaseComputeUnitsConsumed != null">
                tx_base_compute_units_consumed = #{txBaseComputeUnitsConsumed,jdbcType=BIGINT},
            </if>
            <if test="txBaseMemo != null">
                tx_base_memo = #{txBaseMemo,jdbcType=VARCHAR},
            </if>
            <if test="vaultCode != null">
                vault_code = #{vaultCode,jdbcType=VARCHAR},
            </if>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="bilOrderNo != null">
                bil_order_no = #{bilOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="coinId != null">
                coin_id = #{coinId,jdbcType=VARCHAR},
            </if>
            <if test="network != null">
                network = #{network,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="cpAddress != null">
                cp_address = #{cpAddress,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="direction != null">
                direction = #{direction,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="callbackTime != null">
                callback_time = #{callbackTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isProcessed != null">
                is_processed = #{isProcessed,jdbcType=TINYINT},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 删除记录 -->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM dm_transfer_callback
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>