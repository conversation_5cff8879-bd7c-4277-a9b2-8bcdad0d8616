<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmItmBalDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmItmBalDO" >
        <id column="ITM_NO" property="itmNo" jdbcType="VARCHAR" />
        <result column="CCY" property="ccy" jdbcType="VARCHAR" />
        <result column="ITM_CNM" property="itmCnm" jdbcType="VARCHAR" />
        <result column="ITM_LVL" property="itmLvl" jdbcType="VARCHAR" />
        <result column="ITM_CLS" property="itmCls" jdbcType="VARCHAR" />
        <result column="BAL_OD_FLG" property="balOdFlg" jdbcType="VARCHAR" />
        <result column="BAL_DRT" property="balDrt" jdbcType="VARCHAR" />
        <result column="UPD_BAL_FLG" property="updBalFlg" jdbcType="VARCHAR" />
        <result column="UPP_DT" property="uppDt" jdbcType="DATE" />
        <result column="UP_ITM_NO" property="upItmNo" jdbcType="VARCHAR" />
        <result column="LAST_DR_BAL" property="lastDrBal" jdbcType="DECIMAL" />
        <result column="LAST_CR_BAL" property="lastCrBal" jdbcType="DECIMAL" />
        <result column="TD_DR_AMT" property="tdDrAmt" jdbcType="DECIMAL" />
        <result column="TD_CR_AMT" property="tdCrAmt" jdbcType="DECIMAL" />
        <result column="TD_DR_NUM" property="tdDrNum" jdbcType="INTEGER" />
        <result column="TD_CR_NUM" property="tdCrNum" jdbcType="INTEGER" />
        <result column="MON_DR_AMT" property="monDrAmt" jdbcType="DECIMAL" />
        <result column="MON_CR_AMT" property="monCrAmt" jdbcType="DECIMAL" />
        <result column="MON_DR_NUM" property="monDrNum" jdbcType="INTEGER" />
        <result column="MON_CR_NUM" property="monCrNum" jdbcType="INTEGER" />
        <result column="TD_DR_BAL" property="tdDrBal" jdbcType="DECIMAL" />
        <result column="TD_CR_BAL" property="tdCrBal" jdbcType="DECIMAL" />
        <result column="GL_STS" property="glSts" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ITM_NO, CCY, ITM_CNM, ITM_LVL, ITM_CLS, BAL_OD_FLG, BAL_DRT, UPD_BAL_FLG, UPP_DT, 
        UP_ITM_NO, LAST_DR_BAL, LAST_CR_BAL, TD_DR_AMT, TD_CR_AMT, TD_DR_NUM, TD_CR_NUM, 
        MON_DR_AMT, MON_CR_AMT, MON_DR_NUM, MON_CR_NUM, TD_DR_BAL, TD_CR_BAL, GL_STS
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from acm_itm_bal
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
    </select>

    <select id="find" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from acm_itm_bal
        where GL_STS = 'A'
    </select>

    <select id="getAll" resultMap="BaseResultMap">
        select *
        from acm_itm_bal
        where ITM_NO in (select itm_no from acm_itm_inf)
            <![CDATA[and  GL_STS = 'A']]>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from acm_itm_bal
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmItmBalDO" >
        insert into acm_itm_bal
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="itmNo != null" >
                ITM_NO,
            </if>
            <if test="ccy != null" >
                CCY,
            </if>
            <if test="itmCnm != null" >
                ITM_CNM,
            </if>
            <if test="itmLvl != null" >
                ITM_LVL,
            </if>
            <if test="itmCls != null" >
                ITM_CLS,
            </if>
            <if test="balOdFlg != null" >
                BAL_OD_FLG,
            </if>
            <if test="balDrt != null" >
                BAL_DRT,
            </if>
            <if test="updBalFlg != null" >
                UPD_BAL_FLG,
            </if>
            <if test="uppDt != null" >
                UPP_DT,
            </if>
            <if test="upItmNo != null" >
                UP_ITM_NO,
            </if>
            <if test="lastDrBal != null" >
                LAST_DR_BAL,
            </if>
            <if test="lastCrBal != null" >
                LAST_CR_BAL,
            </if>
            <if test="tdDrAmt != null" >
                TD_DR_AMT,
            </if>
            <if test="tdCrAmt != null" >
                TD_CR_AMT,
            </if>
            <if test="tdDrNum != null" >
                TD_DR_NUM,
            </if>
            <if test="tdCrNum != null" >
                TD_CR_NUM,
            </if>
            <if test="monDrAmt != null" >
                MON_DR_AMT,
            </if>
            <if test="monCrAmt != null" >
                MON_CR_AMT,
            </if>
            <if test="monDrNum != null" >
                MON_DR_NUM,
            </if>
            <if test="monCrNum != null" >
                MON_CR_NUM,
            </if>
            <if test="tdDrBal != null" >
                TD_DR_BAL,
            </if>
            <if test="tdCrBal != null" >
                TD_CR_BAL,
            </if>
            <if test="glSts != null" >
                GL_STS,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="itmNo != null" >
                #{itmNo,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="itmCnm != null" >
                #{itmCnm,jdbcType=VARCHAR},
            </if>
            <if test="itmLvl != null" >
                #{itmLvl,jdbcType=VARCHAR},
            </if>
            <if test="itmCls != null" >
                #{itmCls,jdbcType=VARCHAR},
            </if>
            <if test="balOdFlg != null" >
                #{balOdFlg,jdbcType=VARCHAR},
            </if>
            <if test="balDrt != null" >
                #{balDrt,jdbcType=VARCHAR},
            </if>
            <if test="updBalFlg != null" >
                #{updBalFlg,jdbcType=VARCHAR},
            </if>
            <if test="uppDt != null" >
                #{uppDt,jdbcType=DATE},
            </if>
            <if test="upItmNo != null" >
                #{upItmNo,jdbcType=VARCHAR},
            </if>
            <if test="lastDrBal != null" >
                #{lastDrBal,jdbcType=DECIMAL},
            </if>
            <if test="lastCrBal != null" >
                #{lastCrBal,jdbcType=DECIMAL},
            </if>
            <if test="tdDrAmt != null" >
                #{tdDrAmt,jdbcType=DECIMAL},
            </if>
            <if test="tdCrAmt != null" >
                #{tdCrAmt,jdbcType=DECIMAL},
            </if>
            <if test="tdDrNum != null" >
                #{tdDrNum,jdbcType=INTEGER},
            </if>
            <if test="tdCrNum != null" >
                #{tdCrNum,jdbcType=INTEGER},
            </if>
            <if test="monDrAmt != null" >
                #{monDrAmt,jdbcType=DECIMAL},
            </if>
            <if test="monCrAmt != null" >
                #{monCrAmt,jdbcType=DECIMAL},
            </if>
            <if test="monDrNum != null" >
                #{monDrNum,jdbcType=INTEGER},
            </if>
            <if test="monCrNum != null" >
                #{monCrNum,jdbcType=INTEGER},
            </if>
            <if test="tdDrBal != null" >
                #{tdDrBal,jdbcType=DECIMAL},
            </if>
            <if test="tdCrBal != null" >
                #{tdCrBal,jdbcType=DECIMAL},
            </if>
            <if test="glSts != null" >
                #{glSts,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.acm.entity.AcmItmBalDO" >
        update acm_itm_bal
        <set >
            <if test="ccy != null" >
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="itmCnm != null" >
                ITM_CNM = #{itmCnm,jdbcType=VARCHAR},
            </if>
            <if test="itmLvl != null" >
                ITM_LVL = #{itmLvl,jdbcType=VARCHAR},
            </if>
            <if test="itmCls != null" >
                ITM_CLS = #{itmCls,jdbcType=VARCHAR},
            </if>
            <if test="balOdFlg != null" >
                BAL_OD_FLG = #{balOdFlg,jdbcType=VARCHAR},
            </if>
            <if test="balDrt != null" >
                BAL_DRT = #{balDrt,jdbcType=VARCHAR},
            </if>
            <if test="updBalFlg != null" >
                UPD_BAL_FLG = #{updBalFlg,jdbcType=VARCHAR},
            </if>
            <if test="uppDt != null" >
                UPP_DT = #{uppDt,jdbcType=DATE},
            </if>
            <if test="upItmNo != null" >
                UP_ITM_NO = #{upItmNo,jdbcType=VARCHAR},
            </if>
            <if test="lastDrBal != null" >
                LAST_DR_BAL = #{lastDrBal,jdbcType=DECIMAL},
            </if>
            <if test="lastCrBal != null" >
                LAST_CR_BAL = #{lastCrBal,jdbcType=DECIMAL},
            </if>
            <if test="tdDrAmt != null" >
                TD_DR_AMT = #{tdDrAmt,jdbcType=DECIMAL},
            </if>
            <if test="tdCrAmt != null" >
                TD_CR_AMT = #{tdCrAmt,jdbcType=DECIMAL},
            </if>
            <if test="tdDrNum != null" >
                TD_DR_NUM = #{tdDrNum,jdbcType=INTEGER},
            </if>
            <if test="tdCrNum != null" >
                TD_CR_NUM = #{tdCrNum,jdbcType=INTEGER},
            </if>
            <if test="monDrAmt != null" >
                MON_DR_AMT = #{monDrAmt,jdbcType=DECIMAL},
            </if>
            <if test="monCrAmt != null" >
                MON_CR_AMT = #{monCrAmt,jdbcType=DECIMAL},
            </if>
            <if test="monDrNum != null" >
                MON_DR_NUM = #{monDrNum,jdbcType=INTEGER},
            </if>
            <if test="monCrNum != null" >
                MON_CR_NUM = #{monCrNum,jdbcType=INTEGER},
            </if>
            <if test="tdDrBal != null" >
                TD_DR_BAL = #{tdDrBal,jdbcType=DECIMAL},
            </if>
            <if test="tdCrBal != null" >
                TD_CR_BAL = #{tdCrBal,jdbcType=DECIMAL},
            </if>
            <if test="glSts != null" >
                GL_STS = #{glSts,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
    </update>
</mapper>