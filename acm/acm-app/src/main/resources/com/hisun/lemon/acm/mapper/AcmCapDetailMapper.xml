<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmCapDetailDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmCapDetailDO">
        <result column="JRN_NO" property="jrnNo" jdbcType="VARCHAR"/>
        <result column="JRN_SEQ" property="jrnSeq" jdbcType="VARCHAR"/>
        <result column="AC_DT" property="acDt" jdbcType="DATE"/>
        <result column="TX_TYP" property="txTyp" jdbcType="VARCHAR"/>
        <result column="TX_STS" property="txSts" jdbcType="VARCHAR"/>
        <result column="AC_NO" property="acNo" jdbcType="VARCHAR"/>
        <result column="CAP_TYP" property="capTyp" jdbcType="VARCHAR"/>
        <result column="DC_FLG" property="dcFlg" jdbcType="VARCHAR"/>
        <result column="TX_AMT" property="txAmt" jdbcType="DECIMAL"/>
        <result column="CCY" property="ccy" jdbcType="VARCHAR"/>
        <result column="HLD_NO" property="hldNo" jdbcType="VARCHAR"/>
        <result column="RVS_JRN_NO" property="rvsJrnNo" jdbcType="VARCHAR"/>
        <result column="RVS_JRN_SEQ" property="rvsJrnSeq" jdbcType="VARCHAR"/>
        <result column="DR_AMT" property="drAmt" jdbcType="DECIMAL"/>
        <result column="CR_AMT" property="crAmt" jdbcType="DECIMAL"/>
        <result column="OD_AMT" property="odAmt" jdbcType="DECIMAL"/>
        <result column="CUR_BAL" property="curBal" jdbcType="DECIMAL"/>
        <result column="UPD_BAL_FLG" property="updBalFlg" jdbcType="VARCHAR"/>
        <result column="TX_JRN_NO" property="txJrnNo" jdbcType="VARCHAR"/>
        <result column="TX_ORD_NO" property="txOrdNo" jdbcType="VARCHAR"/>
        <result column="TX_ORD_DT" property="txOrdDt" jdbcType="DATE"/>
        <result column="TX_ORD_TM" property="txOrdTm" jdbcType="TIME"/>
        <result column="OPP_AC_NO" property="oppAcNo" jdbcType="VARCHAR"/>
        <result column="OPP_CAP_TYP" property="oppCapTyp" jdbcType="VARCHAR"/>
        <result column="OPP_USER_ID" property="oppUserId" jdbcType="VARCHAR"/>
        <result column="OPP_USER_TYP" property="oppUserTyp" jdbcType="VARCHAR"/>
        <result column="USR_IP_ADR" property="usrIpAdr" jdbcType="VARCHAR"/>
        <result column="RMK" property="rmk" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        JRN_NO, JRN_SEQ, AC_DT, TX_TYP, TX_STS, AC_NO, CAP_TYP, DC_FLG, TX_AMT, CCY, HLD_NO, RVS_JRN_NO,
        RVS_JRN_SEQ, DR_AMT, CR_AMT, OD_AMT, CUR_BAL, UPD_BAL_FLG, TX_JRN_NO, TX_ORD_NO, 
        TX_ORD_DT, TX_ORD_TM, OPP_AC_NO, OPP_CAP_TYP, OPP_USER_ID, OPP_USER_TYP, USR_IP_ADR, RMK
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from acm_cap_detail
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </select>

    <update id="dropCapDetail">
        drop table if EXISTS acm_cap_detail
    </update>

    <update id="createCapDetail">
     CREATE TABLE acm_cap_detail (
      ID Int(32) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
      JRN_NO VARCHAR(32) COLLATE utf8_bin NOT NULL COMMENT '流水号',
      JRN_SEQ VARCHAR(6) COLLATE utf8_bin NOT NULL COMMENT '明细序号',
      AC_DT date NOT NULL COMMENT '会计日期',
      TX_TYP VARCHAR(4) COLLATE utf8_bin COMMENT '交易类型',
      TX_STS VARCHAR(1) COLLATE utf8_bin COMMENT '交易状态',
      AC_NO VARCHAR(15) COLLATE utf8_bin COMMENT '交易账号',
      CAP_TYP VARCHAR(1) COLLATE utf8_bin COMMENT '账户资金属性',
      DC_FLG VARCHAR(1) COLLATE utf8_bin COMMENT '借贷标志',
      TX_AMT DECIMAL(15,2) NOT NULL COMMENT '交易金额',
      CCY VARCHAR(3) COLLATE utf8_bin COMMENT '交易币种',
      HLD_NO VARCHAR(20) COLLATE utf8_bin DEFAULT NULL COMMENT '冻结编号',
      RVS_JRN_NO VARCHAR(32) COLLATE utf8_bin DEFAULT NULL COMMENT '冲正流水号',
      RVS_JRN_SEQ VARCHAR(6) COLLATE utf8_bin DEFAULT NULL COMMENT '冲正流水明细序号',
      DR_AMT  DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '支出金额',
      CR_AMT  DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '收入金额',
      OD_AMT  DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '透支金额',
      CUR_BAL DECIMAL(15,2) NOT NULL COMMENT '余额',
      UPD_BAL_FLG varchar(1) NOT NULL COMMENT '余额更新标志',
      TX_JRN_NO VARCHAR(32) COLLATE utf8_bin NOT NULL COMMENT '请求唯一流水号(堵重)',
      TX_ORD_NO VARCHAR(32) COLLATE utf8_bin COMMENT '请求订单号',
      TX_ORD_DT date COMMENT '请求订单日期',
      TX_ORD_TM time COMMENT '请求订单时间',
      OPP_AC_NO VARCHAR(15) COLLATE utf8_bin DEFAULT NULL COMMENT '交易对手账号',
      OPP_CAP_TYP VARCHAR(1) COLLATE utf8_bin DEFAULT NULL COMMENT '交易对手资金属性',
      OPP_USER_ID VARCHAR(20) COLLATE utf8_bin DEFAULT NULL COMMENT '交易对手用户ID',
      OPP_USER_TYP VARCHAR(1) COLLATE utf8_bin DEFAULT NULL COMMENT '交易对手用户类别',
      USR_IP_ADR VARCHAR(15) COLLATE utf8_bin DEFAULT NULL COMMENT '用户IP',
      RMK VARCHAR(512) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
      PRIMARY key (id)
    ) ENGINE=INNODB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='每日资金收支明细汇总表';
    </update>

    <insert id="initCapDetail" parameterType="java.time.LocalDate">
        insert into acm_cap_detail
        (JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,AC_NO,
         CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
         RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
         UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
         TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
         OPP_USER_TYP,USR_IP_ADR,RMK)
         select
         JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,AC_NO,
         CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
         RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
         UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
         TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
         OPP_USER_TYP,USR_IP_ADR,RMK
         from acm_ac_detail where AC_DT = #{acDt,jdbcType=DATE}
         UNION
         select JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,ITM_NO AS AC_NO,
         CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
         RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
         UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
         TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
         OPP_USER_TYP,USR_IP_ADR,RMK
         from acm_itm_detail where AC_DT = #{acDt,jdbcType=DATE}
    </insert>
</mapper>