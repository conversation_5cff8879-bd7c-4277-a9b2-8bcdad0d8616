<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmAcBalDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmAcBalDO">
        <id column="AC_NO" property="acNo" jdbcType="VARCHAR"/>
        <id column="CAP_TYP" property="capTyp" jdbcType="VARCHAR"/>
        <result column="CCY" property="ccy" jdbcType="VARCHAR"/>
        <result column="USER_ID" property="userId" jdbcType="VARCHAR"/>
        <result column="AC_CUR_BAL" property="acCurBal" jdbcType="DECIMAL"/>
        <result column="AC_UAVA_BAL" property="acUavaBal" jdbcType="DECIMAL"/>
        <result column="AC_LAST_BAL" property="acLastBal" jdbcType="DECIMAL"/>
        <result column="AC_LAST_UAVA_BAL" property="acLastUavaBal" jdbcType="DECIMAL"/>
        <result column="AC_BAL_TAG" property="acBalTag" jdbcType="VARCHAR"/>
        <result column="AC_UPD_DT" property="acUpdDt" jdbcType="DATE"/>
        <result column="AC_UPD_TM" property="acUpdTm" jdbcType="TIME"/>
        <result column="AC_FRZ_DT" property="acFrzDt" jdbcType="DATE"/>
        <result column="AC_FRZ_TM" property="acFrzTm" jdbcType="TIME"/>
        <result column="RMK" property="rmk" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="AC_CUR_FREEZE_BAL" property="acCurFreezeBal" jdbcType="DECIMAL"/>
    </resultMap>


    <sql id="Base_Column_List">
        AC_NO, CAP_TYP, CCY, USER_ID, AC_CUR_BAL, AC_UAVA_BAL, AC_LAST_BAL, AC_LAST_UAVA_BAL, 
        AC_BAL_TAG, AC_UPD_DT, AC_UPD_TM, AC_FRZ_DT, AC_FRZ_TM, RMK, AC_CUR_FREEZE_BAL,MODIFY_TIME,CREATE_TIME
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from acm_ac_bal
        where AC_NO = #{acNo,jdbcType=VARCHAR}
        and CAP_TYP = #{capTyp,jdbcType=VARCHAR}
    </select>

    <select id="getBalAndLock" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from acm_ac_bal
        where AC_NO = #{acNo,jdbcType=VARCHAR}
        and CAP_TYP = #{capTyp,jdbcType=VARCHAR}
        for update
    </select>

    <delete id="delete" parameterType="map">
        delete from acm_ac_bal
        where AC_NO = #{acNo,jdbcType=VARCHAR}
          and CAP_TYP = #{capTyp,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmAcBalDO">
        insert into acm_ac_bal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="acNo != null">
                AC_NO,
            </if>
            <if test="capTyp!= null">
                CAP_TYP,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="acCurBal != null">
                AC_CUR_BAL,
            </if>
            <if test="acUavaBal != null">
                AC_UAVA_BAL,
            </if>
            <if test="acLastBal != null">
                AC_LAST_BAL,
            </if>
            <if test="acLastUavaBal != null">
                AC_LAST_UAVA_BAL,
            </if>
            <if test="acBalTag != null">
                AC_BAL_TAG,
            </if>
            <if test="acUpdDt != null">
                AC_UPD_DT,
            </if>
            <if test="acUpdTm != null">
                AC_UPD_TM,
            </if>
            <if test="acFrzDt != null">
                AC_FRZ_DT,
            </if>
            <if test="acFrzTm != null">
                AC_FRZ_TM,
            </if>
            <if test="rmk != null">
                RMK,
            </if>
            <if test="modifyTime != null">
                MODIFY_TIME,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="acNo != null">
                #{acNo,jdbcType=VARCHAR},
            </if>
            <if test="capTyp!= null">
                #{capTyp,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null">
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="acCurBal != null">
                #{acCurBal,jdbcType=DECIMAL},
            </if>
            <if test="acUavaBal != null">
                #{acUavaBal,jdbcType=DECIMAL},
            </if>
            <if test="acLastBal != null">
                #{acLastBal,jdbcType=DECIMAL},
            </if>
            <if test="acLastUavaBal != null">
                #{acLastUavaBal,jdbcType=DECIMAL},
            </if>
            <if test="acBalTag != null">
                #{acBalTag,jdbcType=VARCHAR},
            </if>
            <if test="acUpdDt != null">
                #{acUpdDt,jdbcType=DATE},
            </if>
            <if test="acUpdTm != null">
                #{acUpdTm,jdbcType=TIME},
            </if>
            <if test="acFrzDt != null">
                #{acFrzDt,jdbcType=DATE},
            </if>
            <if test="acFrzTm != null">
                #{acFrzTm,jdbcType=TIME},
            </if>
            <if test="rmk != null">
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.acm.entity.AcmAcBalDO">
        update acm_ac_bal
        <set>
            <if test="ccy != null">
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="acCurBal != null">
                AC_CUR_BAL = #{acCurBal,jdbcType=DECIMAL},
            </if>
            <if test="acUavaBal != null">
                AC_UAVA_BAL = #{acUavaBal,jdbcType=DECIMAL},
            </if>
            <if test="acLastBal != null">
                AC_LAST_BAL = #{acLastBal,jdbcType=DECIMAL},
            </if>
            <if test="acLastUavaBal != null">
                AC_LAST_UAVA_BAL = #{acLastUavaBal,jdbcType=DECIMAL},
            </if>
            <if test="acBalTag != null">
                AC_BAL_TAG = #{acBalTag,jdbcType=VARCHAR},
            </if>
            <if test="acUpdDt != null">
                AC_UPD_DT = #{acUpdDt,jdbcType=DATE},
            </if>
            <if test="acUpdTm != null">
                AC_UPD_TM = #{acUpdTm,jdbcType=TIME},
            </if>
            <if test="acFrzDt != null">
                AC_FRZ_DT = #{acFrzDt,jdbcType=DATE},
            </if>
            <if test="acFrzTm != null">
                AC_FRZ_TM = #{acFrzTm,jdbcType=TIME},
            </if>
            <if test="rmk != null">
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <if test="acNo != null">
                and AC_NO = #{acNo,jdbcType=VARCHAR}
            </if>
            <if test="capTyp != null">
                and CAP_TYP = #{capTyp,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
        </where>
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.hisun.lemon.acm.entity.AcmAcBalDO">
        select
        <include refid="Base_Column_List"/>
        from acm_ac_bal
        <where>
            <if test="acNo != null">
                and AC_NO = #{acNo,jdbcType=VARCHAR}
            </if>
            <if test="capTyp != null">
                and CAP_TYP = #{capTyp,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null">
                and CCY = #{ccy,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <resultMap id="LedgerMap" type="com.hisun.lemon.acm.bo.LedgerBalanceBo">
        <id column="CAP_TYP" property="capTyp" jdbcType="VARCHAR"/>
        <result column="CCY" property="ccy" jdbcType="VARCHAR"/>
        <result column="TOT_BAL" property="totBal" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getLedgerBal" resultMap="LedgerMap" parameterType="java.time.LocalDate">
        select
        CAP_TYP, CCY, SUM( CASE WHEN AC_UPD_DT > #{acUpdDt, jdbcType=DATE} THEN AC_CUR_BAL ELSE AC_LAST_BAL END ) AS
        TOT_BAL
        from ACM_AC_BAL
        GROUP BY CAP_TYP, CCY
    </select>

    <update id="updateFreezeAmt" >
        update acm_ac_bal set AC_UAVA_BAL = AC_UAVA_BAL + #{freezeAmt,jdbcType=DECIMAL},
                              AC_CUR_BAL = AC_CUR_BAL - #{freezeAmt,jdbcType=DECIMAL}
        where AC_NO = #{acNo,jdbcType=VARCHAR} and  CAP_TYP = '1'
    </update>

    <update id="delFreezeAmt" >
        update acm_ac_bal set AC_CUR_FREEZE_BAL = 0
        where USER_ID = #{userId,jdbcType=VARCHAR} and  CAP_TYP = #{capType,jdbcType=VARCHAR}
    </update>

</mapper>