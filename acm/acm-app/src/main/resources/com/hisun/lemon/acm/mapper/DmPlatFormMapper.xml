<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.acm.dao.IDmPlatFormDao">
    
    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.DmPlatFormDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="vault_code" property="vaultCode" jdbcType="VARCHAR"/>
        <result column="group_code" property="groupCode" jdbcType="VARCHAR"/>
        <result column="account_id" property="accountId" jdbcType="BIGINT"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="network" property="network" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="platform_ac_no" property="platformAcNo" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        platform_ac_no, address
    </sql>

    <select id="getByPlatformNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
            from dm_platform_address where platform_ac_no = #{platformAcNo} and status = "ENABLED"
    </select>


    <select id="findAll" resultMap="BaseResultMap">
        select * from dm_platform_address where status = "ENABLED"
    </select>
        
</mapper>