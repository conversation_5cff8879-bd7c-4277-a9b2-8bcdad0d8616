<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.acm.dao.IFmAccountInfoDao">
    <select id="queryUserAccountName" resultType="java.lang.String" parameterType="java.lang.String">
        select corp_full_name_eng
        from urm_kyb_cert_inf where uesr_id = #{userId}
    </select>

    <select id="checkPayPwd" resultType="java.lang.String" parameterType="java.lang.String">
        select PAY_PWD
        from urm_safe_inf where USER_ID = #{userId}
    </select>
</mapper>