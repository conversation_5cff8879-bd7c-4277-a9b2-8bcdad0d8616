<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmVoucherSumDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmVoucherSumDO">
        <id column="VCH_NO" property="vchNo" jdbcType="VARCHAR"/>
        <id column="AC_DT" property="acDt" jdbcType="DATE"/>
        <result column="CCY" property="ccy" jdbcType="VARCHAR"/>
        <result column="TOT_DR_AMT" property="totDrAmt" jdbcType="DECIMAL"/>
        <result column="TOT_CR_AMT" property="totCrAmt" jdbcType="DECIMAL"/>
        <result column="NET_AMT" property="netAmt" jdbcType="DECIMAL"/>
        <result column="RMK" property="rmk" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        VCH_NO, AC_DT, CCY, TOT_DR_AMT, TOT_CR_AMT, NET_AMT
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from acm_voucher_sum
        where VCH_NO = #{vchNo,jdbcType=VARCHAR}
        and AC_DT = #{acDt,jdbcType=DATE}
    </select>

    <delete id="delete" parameterType="map">
        delete from acm_voucher_sum
        where VCH_NO = #{vchNo,jdbcType=VARCHAR}
          and AC_DT = #{acDt,jdbcType=DATE}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmVoucherSumDO">
        insert into acm_voucher_sum
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vchNo != null">
                VCH_NO,
            </if>
            <if test="acDt != null">
                AC_DT,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="totDrAmt != null">
                TOT_DR_AMT,
            </if>
            <if test="totCrAmt != null">
                TOT_CR_AMT,
            </if>
            <if test="netAmt != null">
                NET_AMT,
            </if>
            <if test="rmk != null">
                RMK,
            </if>
            <if test="modifyTime != null">
                MODIFY_TIME,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vchNo != null">
                #{vchNo,jdbcType=VARCHAR},
            </if>
            <if test="acDt != null">
                #{acDt,jdbcType=DATE},
            </if>
            <if test="ccy != null">
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="totDrAmt != null">
                #{totDrAmt,jdbcType=DECIMAL},
            </if>
            <if test="totCrAmt != null">
                #{totCrAmt,jdbcType=DECIMAL},
            </if>
            <if test="netAmt != null">
                #{netAmt,jdbcType=DECIMAL},
            </if>
            <if test="rmk != null">
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.acm.entity.AcmVoucherSumDO">
        update acm_voucher_sum
        <set>
            <if test="ccy != null">
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="totDrAmt != null">
                TOT_DR_AMT = #{totDrAmt,jdbcType=DECIMAL},
            </if>
            <if test="totCrAmt != null">
                TOT_CR_AMT = #{totCrAmt,jdbcType=DECIMAL},
            </if>
            <if test="netAmt != null">
                NET_AMT = #{netAmt,jdbcType=DECIMAL},
            </if>
            <if test="rmk != null">
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where VCH_NO = #{vchNo,jdbcType=VARCHAR}
        and AC_DT = #{acDt,jdbcType=DATE}
    </update>

    <update id="initVoucher">
          INSERT INTO ACM_VOUCHER_SUM (AC_DT,VCH_NO,CCY,TOT_CR_AMT,TOT_DR_AMT,NET_AMT)
              SELECT AC_DT,JRN_NO,CCY,SUM(CR_AMT) AS TOT_CR_AMT,SUM(DR_AMT) AS TOT_DR_AMT,
                SUM(CR_AMT) - SUM(DR_AMT) AS NET_AMT FROM ACM_CAP_DETAIL GROUP BY AC_DT,JRN_NO,CCY
    </update>
</mapper>