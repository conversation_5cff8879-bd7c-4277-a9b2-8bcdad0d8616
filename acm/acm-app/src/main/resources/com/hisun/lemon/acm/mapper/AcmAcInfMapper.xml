<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmAcInfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmAcInfDO" >
        <id column="AC_NO" property="acNo" jdbcType="VARCHAR" />
        <id column="CCY" property="ccy" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="AC_STS" property="acSts" jdbcType="VARCHAR" />
        <result column="AC_CRE_DT" property="acCreDt" jdbcType="DATE" />
        <result column="AC_CRE_TM" property="acCreTm" jdbcType="TIME" />
        <result column="AC_CLS_DT" property="acClsDt" jdbcType="DATE" />
        <result column="AC_CLS_TM" property="acClsTm" jdbcType="TIME" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="CCY_TYPE" property="ccyType" jdbcType="VARCHAR" />
        <result column="BANK" property="bank" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        AC_NO, CCY, USER_ID, AC_STS, AC_CRE_DT, AC_CRE_TM, AC_CLS_DT, AC_CLS_TM, BANK
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from acm_ac_inf
        where AC_NO = #{acNo,jdbcType=VARCHAR}
          and CCY = #{ccy,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="map" >
        delete from acm_ac_inf
        where AC_NO = #{acNo,jdbcType=VARCHAR}
          and CCY = #{ccy,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmAcInfDO" >
        insert into acm_ac_inf
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="acNo != null" >
                AC_NO,
            </if>
            <if test="ccy != null" >
                CCY,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="acSts != null" >
                AC_STS,
            </if>
            <if test="acCreDt != null" >
                AC_CRE_DT,
            </if>
            <if test="acCreTm != null" >
                AC_CRE_TM,
            </if>
            <if test="acClsDt != null" >
                AC_CLS_DT,
            </if>
            <if test="acClsTm != null" >
                AC_CLS_TM,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="ccyType != null" >
                CCY_TYPE,
            </if>
            <if test="bank != null" >
                BANK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="acNo != null" >
                #{acNo,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="acSts != null" >
                #{acSts,jdbcType=VARCHAR},
            </if>
            <if test="acCreDt != null" >
                #{acCreDt,jdbcType=DATE},
            </if>
            <if test="acCreTm != null" >
                #{acCreTm,jdbcType=TIME},
            </if>
            <if test="acClsDt != null" >
                #{acClsDt,jdbcType=DATE},
            </if>
            <if test="acClsTm != null" >
                #{acClsTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ccyType != null" >
                #{ccyType,jdbcType=VARCHAR},
            </if>
            <if test="bank != null" >
                #{bank,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.acm.entity.AcmAcInfDO" >
        update acm_ac_inf
        <set>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="acSts != null" >
                AC_STS = #{acSts,jdbcType=VARCHAR},
            </if>
            <if test="acCreDt != null" >
                AC_CRE_DT = #{acCreDt,jdbcType=DATE},
            </if>
            <if test="acCreTm != null" >
                AC_CRE_TM = #{acCreTm,jdbcType=TIME},
            </if>
            <if test="acClsDt != null" >
                AC_CLS_DT = #{acClsDt,jdbcType=DATE},
            </if>
            <if test="acClsTm != null" >
                AC_CLS_TM = #{acClsTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <if test="acNo != null">
                and AC_NO = #{acNo,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null">
                and CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
        </where>
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.hisun.lemon.acm.entity.AcmAcInfDO">
        select
        <include refid="Base_Column_List"/>
        from acm_ac_inf
        <where>
            <if test="acNo != null">
                and AC_NO = #{acNo,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null">
                and CCY = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="ccyType != null">
                and CCY_TYPE = #{ccyType,jdbcType=VARCHAR}
            </if>
            <if test="acSts != null">
                and AC_STS = #{acSts,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryAcc" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from acm_ac_inf
        <where>
            <if test="userId != null">
                and USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="acSts != null">
                and AC_STS = #{acSts,jdbcType=VARCHAR}
            </if>
            <if test="bank != null">
                and BANK = #{bank,jdbcType=VARCHAR}
            </if>
            <if test="acNo != null">
                and AC_NO = #{acNo,jdbcType=VARCHAR}
            </if>
            and CCY_TYPE = 'FM';
        </where>
    </select>

    <select id="queryAccByCcy" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from acm_ac_inf where USER_ID = #{userId,jdbcType=VARCHAR} and CCY = #{ccy,jdbcType=VARCHAR} and AC_STS = '0'
    </select>
</mapper>