<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmAcDetailDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmAcDetailDO" >
        <id column="JRN_NO" property="jrnNo" jdbcType="VARCHAR" />
        <result column="JRN_SEQ" property="jrnSeq" jdbcType="VARCHAR" />
        <result column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="TX_TYP" property="txTyp" jdbcType="VARCHAR" />
        <result column="TX_STS" property="txSts" jdbcType="VARCHAR" />
        <result column="AC_NO" property="acNo" jdbcType="VARCHAR" />
        <result column="CAP_TYP" property="capTyp" jdbcType="VARCHAR" />
        <result column="DC_FLG" property="dcFlg" jdbcType="VARCHAR" />
        <result column="TX_AMT" property="txAmt" jdbcType="DECIMAL" />
        <result column="CCY" property="ccy" jdbcType="VARCHAR" />
        <result column="HLD_NO" property="hldNo" jdbcType="VARCHAR" />
        <result column="RVS_JRN_NO" property="rvsJrnNo" jdbcType="VARCHAR" />
        <result column="RVS_JRN_SEQ" property="rvsJrnSeq" jdbcType="VARCHAR" />
        <result column="DR_AMT" property="drAmt" jdbcType="DECIMAL" />
        <result column="CR_AMT" property="crAmt" jdbcType="DECIMAL" />
        <result column="OD_AMT" property="odAmt" jdbcType="DECIMAL" />
        <result column="CUR_BAL" property="curBal" jdbcType="DECIMAL" />
        <result column="UPD_BAL_FLG" property="updBalFlg" jdbcType="VARCHAR" />
        <result column="TX_JRN_NO" property="txJrnNo" jdbcType="VARCHAR" />
        <result column="TX_ORD_NO" property="txOrdNo" jdbcType="VARCHAR" />
        <result column="TX_ORD_DT" property="txOrdDt" jdbcType="DATE" />
        <result column="TX_ORD_TM" property="txOrdTm" jdbcType="TIME" />
        <result column="OPP_AC_NO" property="oppAcNo" jdbcType="VARCHAR" />
        <result column="OPP_CAP_TYP" property="oppCapTyp" jdbcType="VARCHAR" />
        <result column="OPP_USER_ID" property="oppUserId" jdbcType="VARCHAR" />
        <result column="OPP_USER_TYP" property="oppUserTyp" jdbcType="VARCHAR" />
        <result column="USR_IP_ADR" property="usrIpAdr" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        JRN_NO, JRN_SEQ, AC_DT, TX_TYP, TX_STS, AC_NO, CAP_TYP, DC_FLG, TX_AMT, CCY, HLD_NO, 
        RVS_JRN_NO, RVS_JRN_SEQ, DR_AMT, CR_AMT, OD_AMT, CUR_BAL, UPD_BAL_FLG, TX_JRN_NO, 
        TX_ORD_NO, TX_ORD_DT, TX_ORD_TM, OPP_AC_NO, OPP_CAP_TYP, OPP_USER_ID, OPP_USER_TYP, 
        USR_IP_ADR, RMK
    </sql>

    <select id="getJrnSeq" resultType="int" parameterType="java.lang.String" >
        SELECT IFNULL(MAX(JRN_SEQ),0)
        FROM ACM_AC_DETAIL
         wHERE TX_JRN_NO = #{txJrnNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from acm_ac_detail
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmAcDetailDO" >
        insert into acm_ac_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                JRN_NO,
            </if>
            <if test="jrnSeq != null" >
                JRN_SEQ,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="txTyp != null" >
                TX_TYP,
            </if>
            <if test="txSts != null" >
                TX_STS,
            </if>
            <if test="acNo != null" >
                AC_NO,
            </if>
            <if test="capTyp != null" >
                CAP_TYP,
            </if>
            <if test="dcFlg != null" >
                DC_FLG,
            </if>
            <if test="txAmt != null" >
                TX_AMT,
            </if>
            <if test="ccy != null" >
                CCY,
            </if>
            <if test="hldNo != null" >
                HLD_NO,
            </if>
            <if test="rvsJrnNo != null" >
                RVS_JRN_NO,
            </if>
            <if test="rvsJrnSeq != null" >
                RVS_JRN_SEQ,
            </if>
            <if test="drAmt != null" >
                DR_AMT,
            </if>
            <if test="crAmt != null" >
                CR_AMT,
            </if>
            <if test="odAmt != null" >
                OD_AMT,
            </if>
            <if test="curBal != null" >
                CUR_BAL,
            </if>
            <if test="updBalFlg != null" >
                UPD_BAL_FLG,
            </if>
            <if test="txJrnNo != null" >
                TX_JRN_NO,
            </if>
            <if test="txOrdNo != null" >
                TX_ORD_NO,
            </if>
            <if test="txOrdDt != null" >
                TX_ORD_DT,
            </if>
            <if test="txOrdTm != null" >
                TX_ORD_TM,
            </if>
            <if test="oppAcNo != null" >
                OPP_AC_NO,
            </if>
            <if test="oppCapTyp != null" >
                OPP_CAP_TYP,
            </if>
            <if test="oppUserId != null" >
                OPP_USER_ID,
            </if>
            <if test="oppUserTyp != null" >
                OPP_USER_TYP,
            </if>
            <if test="usrIpAdr != null" >
                USR_IP_ADR,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="jrnSeq != null" >
                #{jrnSeq,jdbcType=VARCHAR},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="txTyp != null" >
                #{txTyp,jdbcType=VARCHAR},
            </if>
            <if test="txSts != null" >
                #{txSts,jdbcType=VARCHAR},
            </if>
            <if test="acNo != null" >
                #{acNo,jdbcType=VARCHAR},
            </if>
            <if test="capTyp != null" >
                #{capTyp,jdbcType=VARCHAR},
            </if>
            <if test="dcFlg != null" >
                #{dcFlg,jdbcType=VARCHAR},
            </if>
            <if test="txAmt != null" >
                #{txAmt,jdbcType=DECIMAL},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="hldNo != null" >
                #{hldNo,jdbcType=VARCHAR},
            </if>
            <if test="rvsJrnNo != null" >
                #{rvsJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="rvsJrnSeq != null" >
                #{rvsJrnSeq,jdbcType=VARCHAR},
            </if>
            <if test="drAmt != null" >
                #{drAmt,jdbcType=DECIMAL},
            </if>
            <if test="crAmt != null" >
                #{crAmt,jdbcType=DECIMAL},
            </if>
            <if test="odAmt != null" >
                #{odAmt,jdbcType=DECIMAL},
            </if>
            <if test="curBal != null" >
                #{curBal,jdbcType=DECIMAL},
            </if>
            <if test="updBalFlg != null" >
                #{updBalFlg,jdbcType=VARCHAR},
            </if>
            <if test="txJrnNo != null" >
                #{txJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="txOrdNo != null" >
                #{txOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="txOrdDt != null" >
                #{txOrdDt,jdbcType=DATE},
            </if>
            <if test="txOrdTm != null" >
                #{txOrdTm,jdbcType=TIME},
            </if>
            <if test="oppAcNo != null" >
                #{oppAcNo,jdbcType=VARCHAR},
            </if>
            <if test="oppCapTyp != null" >
                #{oppCapTyp,jdbcType=VARCHAR},
            </if>
            <if test="oppUserId != null" >
                #{oppUserId,jdbcType=VARCHAR},
            </if>
            <if test="oppUserTyp != null" >
                #{oppUserTyp,jdbcType=VARCHAR},
            </if>
            <if test="usrIpAdr != null" >
                #{usrIpAdr,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateOriginalTransaction" parameterType="com.hisun.lemon.acm.entity.AcmAcDetailDO" >
        update acm_ac_detail
        <set>
            <if test="acDt != null" >
                AC_DT = #{acDt,jdbcType=DATE},
            </if>
            TX_STS = 'C',
            <if test="rvsJrnNo != null" >
                RVS_JRN_NO = #{rvsJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="rvsJrnSeq != null" >
                RVS_JRN_SEQ = #{rvsJrnSeq,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where JRN_SEQ IN ( SELECT JRN_SEQ FROM (SELECT MAX(JRN_SEQ) FROM acm_ac_detail WHERE
           TX_JRN_NO = #{txJrnNo,jdbcType=VARCHAR} AND AC_DT = #{acDt, jdbcType=DATE}
           AND TX_AMT = #{txAmt,jdbcType=DECIMAL}
           AND AC_NO = #{acNo, jdbcType=VARCHAR}
           AND CAP_TYP = #{capTyp, jdbcType=VARCHAR}
           AND TX_STS = #{txSts, jdbcType=VARCHAR}) AS A)
          AND AC_DT =  #{acDt, jdbcType=DATE}
          AND TX_JRN_NO = #{txJrnNo,jdbcType=VARCHAR}
    </update>

    <select id="find"  resultMap="BaseResultMap" parameterType="java.lang.String">
        select
a.*
        from (
        select
        JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,AC_NO,
        CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
        RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
        UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
        TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
        OPP_USER_TYP,USR_IP_ADR,RMK
        from acm_ac_detail where TX_JRN_NO = #{txJrnNo,jdbcType=VARCHAR}
        UNION
        select JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,ITM_NO AS AC_NO,
        CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
        RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
        UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
        TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
        OPP_USER_TYP,USR_IP_ADR,RMK
        from acm_itm_detail where TX_JRN_NO = #{txJrnNo,jdbcType=VARCHAR}
        ) a
    </select>

    <select id="getTranList"  resultMap="BaseResultMap">
        select *  from acm_ac_detail where ac_no = #{acNo,jdbcType=VARCHAR}
        AND AC_DT =  #{acDt, jdbcType=DATE} order by JRN_NO desc, JRN_SEQ  desc
    </select>
</mapper>