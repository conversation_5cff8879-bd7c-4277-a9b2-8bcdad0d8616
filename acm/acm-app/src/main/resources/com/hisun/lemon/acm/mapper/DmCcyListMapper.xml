<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.acm.dao.IDmCcyListDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.DmCcyListDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="coin_id" property="coinId" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="coin_icon_url" property="coinIconUrl" jdbcType="VARCHAR"/>
        <result column="prec" property="prec" jdbcType="TINYINT"/>
        <result column="coin_type" property="coinType" jdbcType="VARCHAR"/>
        <result column="decimals" property="decimals" jdbcType="TINYINT"/>
        <result column="series" property="series" jdbcType="VARCHAR"/>
        <result column="token_flag" property="tokenFlag" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="coin_id_on_chain" property="coinIdOnChain" jdbcType="VARCHAR"/>
        <result column="main_coin_id" property="mainCoinId" jdbcType="VARCHAR"/>
        <result column="network" property="network" jdbcType="VARCHAR"/>
        <result column="appendix" property="appendix" jdbcType="LONGVARCHAR"/>
        <result column="algorithm" property="algorithm" jdbcType="VARCHAR"/>
        <result column="withdrawing_fee" property="withdrawingFee" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, coin_id, unit, coin_icon_url, prec, coin_type, decimals, series, token_flag,
        create_time, update_time, coin_id_on_chain, main_coin_id, network, appendix, algorithm, withdrawing_fee
    </sql>

    <!-- 获取币种列表 -->
    <select id="getCcyList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_ccy_list
        ORDER BY create_time DESC
    </select>

    <!-- 根据 coin_id 查询 -->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_ccy_list
        WHERE coin_id = #{coinId,jdbcType=VARCHAR}
    </select>

    <!-- 动态查询 -->
    <select id="find" parameterType="com.hisun.lemon.acm.entity.DmCcyListDO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_ccy_list
        <where>
            <if test="id != null">
                AND id = #{id,jdbcType=BIGINT}
            </if>
            <if test="coinId != null">
                AND coin_id = #{coinId,jdbcType=VARCHAR}
            </if>
            <if test="unit != null">
                AND unit = #{unit,jdbcType=VARCHAR}
            </if>
            <if test="coinIconUrl != null">
                AND coin_icon_url = #{coinIconUrl,jdbcType=VARCHAR}
            </if>
            <if test="prec != null">
                AND prec = #{prec,jdbcType=TINYINT}
            </if>
            <if test="coinType != null">
                AND coin_type = #{coinType,jdbcType=VARCHAR}
            </if>
            <if test="decimals != null">
                AND decimals = #{decimals,jdbcType=TINYINT}
            </if>
            <if test="series != null">
                AND series = #{series,jdbcType=VARCHAR}
            </if>
            <if test="tokenFlag != null">
                AND token_flag = #{tokenFlag,jdbcType=TINYINT}
            </if>
            <if test="coinIdOnChain != null">
                AND coin_id_on_chain = #{coinIdOnChain,jdbcType=VARCHAR}
            </if>
            <if test="mainCoinId != null">
                AND main_coin_id = #{mainCoinId,jdbcType=VARCHAR}
            </if>
            <if test="network != null">
                AND network = #{network,jdbcType=VARCHAR}
            </if>
            <if test="appendix != null">
                AND appendix = #{appendix,jdbcType=LONGVARCHAR}
            </if>
            <if test="algorithm != null">
                AND algorithm = #{algorithm,jdbcType=VARCHAR}
            </if>
            <if test="withdrawingFee != null">
                AND withdrawing_fee = #{withdrawingFee,jdbcType=DECIMAL}
            </if>
        </where>
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.DmCcyListDO">
        INSERT INTO dm_ccy_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="coinId != null">
                coin_id,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="coinIconUrl != null">
                coin_icon_url,
            </if>
            <if test="prec != null">
                prec,
            </if>
            <if test="coinType != null">
                coin_type,
            </if>
            <if test="decimals != null">
                decimals,
            </if>
            <if test="series != null">
                series,
            </if>
            <if test="tokenFlag != null">
                token_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                update_time,
            </if>
            <if test="coinIdOnChain != null">
                coin_id_on_chain,
            </if>
            <if test="mainCoinId != null">
                main_coin_id,
            </if>
            <if test="network != null">
                network,
            </if>
            <if test="appendix != null">
                appendix,
            </if>
            <if test="algorithm != null">
                algorithm,
            </if>
            <if test="withdrawingFee != null">
                withdrawing_fee,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="coinId != null">
                #{coinId,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="coinIconUrl != null">
                #{coinIconUrl,jdbcType=VARCHAR},
            </if>
            <if test="prec != null">
                #{prec,jdbcType=TINYINT},
            </if>
            <if test="coinType != null">
                #{coinType,jdbcType=VARCHAR},
            </if>
            <if test="decimals != null">
                #{decimals,jdbcType=TINYINT},
            </if>
            <if test="series != null">
                #{series,jdbcType=VARCHAR},
            </if>
            <if test="tokenFlag != null">
                #{tokenFlag,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="coinIdOnChain != null">
                #{coinIdOnChain,jdbcType=VARCHAR},
            </if>
            <if test="mainCoinId != null">
                #{mainCoinId,jdbcType=VARCHAR},
            </if>
            <if test="network != null">
                #{network,jdbcType=VARCHAR},
            </if>
            <if test="appendix != null">
                #{appendix,jdbcType=LONGVARCHAR},
            </if>
            <if test="algorithm != null">
                #{algorithm,jdbcType=VARCHAR},
            </if>
            <if test="withdrawingFee != null">
                #{withdrawingFee,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.hisun.lemon.acm.entity.DmCcyListDO">
        UPDATE dm_ccy_list
        <set>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="coinIconUrl != null">
                coin_icon_url = #{coinIconUrl,jdbcType=VARCHAR},
            </if>
            <if test="prec != null">
                prec = #{prec,jdbcType=TINYINT},
            </if>
            <if test="coinType != null">
                coin_type = #{coinType,jdbcType=VARCHAR},
            </if>
            <if test="decimals != null">
                decimals = #{decimals,jdbcType=TINYINT},
            </if>
            <if test="series != null">
                series = #{series,jdbcType=VARCHAR},
            </if>
            <if test="tokenFlag != null">
                token_flag = #{tokenFlag,jdbcType=TINYINT},
            </if>
            <if test="modifyTime != null">
                update_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="coinIdOnChain != null">
                coin_id_on_chain = #{coinIdOnChain,jdbcType=VARCHAR},
            </if>
            <if test="mainCoinId != null">
                main_coin_id = #{mainCoinId,jdbcType=VARCHAR},
            </if>
            <if test="network != null">
                network = #{network,jdbcType=VARCHAR},
            </if>
            <if test="appendix != null">
                appendix = #{appendix,jdbcType=LONGVARCHAR},
            </if>
            <if test="algorithm != null">
                algorithm = #{algorithm,jdbcType=VARCHAR},
            </if>
            <if test="withdrawingFee != null">
                withdrawing_fee = #{withdrawingFee,jdbcType=DECIMAL},
            </if>
        </set>
        WHERE coin_id = #{coinId,jdbcType=VARCHAR}
    </update>

    <!-- 删除记录 -->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM dm_ccy_list
        WHERE coin_id = #{coinId,jdbcType=VARCHAR}
    </delete>
</mapper>