<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.acm.dao.IDmAccountInfoDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.DmAccountInfoDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="account_id" property="accountId" jdbcType="VARCHAR"/>
        <result column="vault_code" property="vaultCode" jdbcType="VARCHAR"/>
        <result column="group_code" property="groupCode" jdbcType="VARCHAR"/>
        <result column="account_name" property="accountName" jdbcType="VARCHAR"/>
        <result column="account_type" property="accountType" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , account_id, vault_code, group_code, account_name, account_type, remark, status, create_time, update_time
    </sql>

    <!-- 根据 account_id 查询 -->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_info
        WHERE account_id = #{accountId,jdbcType=VARCHAR}
    </select>

    <!-- 动态查询 -->
    <select id="find" parameterType="com.hisun.lemon.acm.entity.DmAccountInfoDO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_info
        <where>
            <if test="id != null">
                AND id = #{id,jdbcType=BIGINT}
            </if>
            <if test="accountId != null">
                AND account_id = #{accountId,jdbcType=VARCHAR}
            </if>
            <if test="vaultCode != null">
                AND vault_code = #{vaultCode,jdbcType=VARCHAR}
            </if>
            <if test="groupCode != null">
                AND group_code = #{groupCode,jdbcType=VARCHAR}
            </if>
            <if test="accountName != null">
                AND account_name = #{accountName,jdbcType=VARCHAR}
            </if>
            <if test="accountType != null">
                AND account_type = #{accountType,jdbcType=VARCHAR}
            </if>
            <if test="remark != null">
                AND remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                AND status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.DmAccountInfoDO">
        INSERT INTO dm_account_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="vaultCode != null">
                vault_code,
            </if>
            <if test="groupCode != null">
                group_code,
            </if>
            <if test="accountName != null">
                account_name,
            </if>
            <if test="accountType != null">
                account_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=VARCHAR},
            </if>
            <if test="vaultCode != null">
                #{vaultCode,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null">
                #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                #{accountType,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.hisun.lemon.acm.entity.DmAccountInfoDO">
        UPDATE dm_account_info
        <set>
            <if test="vaultCode != null">
                vault_code = #{vaultCode,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                group_code = #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null">
                account_name = #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                account_type = #{accountType,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                update_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE account_id = #{accountId,jdbcType=VARCHAR}
    </update>

    <!-- 删除记录 -->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM dm_account_info
        WHERE account_id = #{accountId,jdbcType=VARCHAR}
    </delete>
</mapper>
