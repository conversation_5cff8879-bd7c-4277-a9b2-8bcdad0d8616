<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmItmInfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmItmInfDO" >
        <id column="ITM_NO" property="itmNo" jdbcType="VARCHAR" />
        <result column="ITM_ENM" property="itmEnm" jdbcType="VARCHAR" />
        <result column="ITM_CNM" property="itmCnm" jdbcType="VARCHAR" />
        <result column="ITM_LVL" property="itmLvl" jdbcType="INTEGER" />
        <result column="UP_ITM_NO" property="upItmNo" jdbcType="VARCHAR" />
        <result column="BTM_ITM_FLG" property="btmItmFlg" jdbcType="VARCHAR" />
        <result column="ITM_TYP" property="itmTyp" jdbcType="VARCHAR" />
        <result column="ITM_CLS" property="itmCls" jdbcType="VARCHAR" />
        <result column="BAL_OD_FLG" property="balOdFlg" jdbcType="VARCHAR" />
        <result column="BAL_DRT" property="balDrt" jdbcType="VARCHAR" />
        <result column="UPD_BAL_FLG" property="updBalFlg" jdbcType="VARCHAR" />
        <result column="ITM_STS" property="itmSts" jdbcType="VARCHAR" />
        <result column="EFF_DT" property="effDt" jdbcType="DATE" />
        <result column="EXP_DT" property="expDt" jdbcType="DATE" />
        <result column="UPD_OPR" property="updOpr" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ITM_NO, ITM_ENM, ITM_CNM, ITM_LVL, UP_ITM_NO, BTM_ITM_FLG, ITM_TYP, ITM_CLS, BAL_OD_FLG, 
        BAL_DRT, UPD_BAL_FLG, ITM_STS, EFF_DT, EXP_DT, UPD_OPR
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from acm_itm_inf
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from acm_itm_inf
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmItmInfDO" >
        insert into acm_itm_inf
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="itmNo != null" >
                ITM_NO,
            </if>
            <if test="itmEnm != null" >
                ITM_ENM,
            </if>
            <if test="itmCnm != null" >
                ITM_CNM,
            </if>
            <if test="itmLvl != null" >
                ITM_LVL,
            </if>
            <if test="upItmNo != null" >
                UP_ITM_NO,
            </if>
            <if test="btmItmFlg != null" >
                BTM_ITM_FLG,
            </if>
            <if test="itmTyp != null" >
                ITM_TYP,
            </if>
            <if test="itmCls != null" >
                ITM_CLS,
            </if>
            <if test="balOdFlg != null" >
                BAL_OD_FLG,
            </if>
            <if test="balDrt != null" >
                BAL_DRT,
            </if>
            <if test="updBalFlg!= null" >
                UPD_BAL_FLG,
            </if>
            <if test="itmSts != null" >
                ITM_STS,
            </if>
            <if test="effDt != null" >
                EFF_DT,
            </if>
            <if test="expDt != null" >
                EXP_DT,
            </if>
            <if test="updOpr != null" >
                UPD_OPR,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="itmNo != null" >
                #{itmNo,jdbcType=VARCHAR},
            </if>
            <if test="itmEnm != null" >
                #{itmEnm,jdbcType=VARCHAR},
            </if>
            <if test="itmCnm != null" >
                #{itmCnm,jdbcType=VARCHAR},
            </if>
            <if test="itmLvl != null" >
                #{itmLvl,jdbcType=INTEGER},
            </if>
            <if test="upItmNo != null" >
                #{upItmNo,jdbcType=VARCHAR},
            </if>
            <if test="btmItmFlg != null" >
                #{btmItmFlg,jdbcType=VARCHAR},
            </if>
            <if test="itmTyp != null" >
                #{itmTyp,jdbcType=VARCHAR},
            </if>
            <if test="itmCls != null" >
                #{itmCls,jdbcType=VARCHAR},
            </if>
            <if test="balOdFlg != null" >
                #{balOdFlg,jdbcType=VARCHAR},
            </if>
            <if test="balDrt != null" >
                #{balDrt,jdbcType=VARCHAR},
            </if>
            <if test="updBalFlg!= null" >
                #{updBalFlg,jdbcType=VARCHAR},
            </if>
            <if test="itmSts != null" >
                #{itmSts,jdbcType=VARCHAR},
            </if>
            <if test="effDt != null" >
                #{effDt,jdbcType=DATE},
            </if>
            <if test="expDt != null" >
                #{expDt,jdbcType=DATE},
            </if>
            <if test="updOpr != null" >
                #{updOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.acm.entity.AcmItmInfDO" >
        update acm_itm_inf
        <set >
            <if test="itmEnm != null" >
                ITM_ENM = #{itmEnm,jdbcType=VARCHAR},
            </if>
            <if test="itmCnm != null" >
                ITM_CNM = #{itmCnm,jdbcType=VARCHAR},
            </if>
            <if test="itmLvl != null" >
                ITM_LVL = #{itmLvl,jdbcType=INTEGER},
            </if>
            <if test="upItmNo != null" >
                UP_ITM_NO = #{upItmNo,jdbcType=VARCHAR},
            </if>
            <if test="btmItmFlg != null" >
                BTM_ITM_FLG = #{btmItmFlg,jdbcType=VARCHAR},
            </if>
            <if test="itmTyp != null" >
                ITM_TYP = #{itmTyp,jdbcType=VARCHAR},
            </if>
            <if test="itmCls != null" >
                ITM_CLS = #{itmCls,jdbcType=VARCHAR},
            </if>
            <if test="balOdFlg != null" >
                BAL_OD_FLG = #{balOdFlg,jdbcType=VARCHAR},
            </if>
            <if test="balDrt != null" >
                BAL_DRT = #{balDrt,jdbcType=VARCHAR},
            </if>
            <if test="updBalFlg!= null" >
                UPD_BAL_FLG = #{updBalFlg,jdbcType=VARCHAR},
            </if>
            <if test="itmSts != null" >
                ITM_STS = #{itmSts,jdbcType=VARCHAR},
            </if>
            <if test="effDt != null" >
                EFF_DT = #{effDt,jdbcType=DATE},
            </if>
            <if test="expDt != null" >
                EXP_DT = #{expDt,jdbcType=DATE},
            </if>
            <if test="updOpr != null" >
                UPD_OPR = #{updOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ITM_NO = #{itmNo,jdbcType=VARCHAR}
    </update>
</mapper>