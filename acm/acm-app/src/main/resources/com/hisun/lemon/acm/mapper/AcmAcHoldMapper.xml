<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.acm.dao.IAcmAcHoldDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.AcmAcHoldDO" >
        <id column="AC_NO" property="acNo" jdbcType="VARCHAR" />
        <id column="CAP_TYP" property="capTyp" jdbcType="VARCHAR" />
        <id column="HLD_NO" property="hldNo" jdbcType="VARCHAR" />
        <result column="CCY" property="ccy" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="USER_TYP" property="userTyp" jdbcType="VARCHAR" />
        <result column="HLD_STS" property="hldSts" jdbcType="VARCHAR" />
        <result column="HLD_CD" property="hldCd" jdbcType="VARCHAR" />
        <result column="HLD_BAL" property="hldBal" jdbcType="DECIMAL" />
        <result column="EFF_DT" property="effDt" jdbcType="DATE" />
        <result column="EFF_TM" property="effTm" jdbcType="TIME" />
        <result column="EXP_DT" property="expDt" jdbcType="DATE" />
        <result column="EXP_TM" property="expTm" jdbcType="TIME" />
        <result column="RLS_DT" property="rlsDt" jdbcType="DATE" />
        <result column="RLS_TM" property="rlsTm" jdbcType="TIME" />
        <result column="DUE_PROC_MOD" property="dueProcMod" jdbcType="VARCHAR" />
        <result column="ORD_TYP" property="ordTyp" jdbcType="VARCHAR" />
        <result column="ORD_NO" property="ordNo" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        AC_NO, CAP_TYP, HLD_NO, CCY, USER_ID, USER_TYP, HLD_STS, HLD_CD, HLD_BAL, EFF_DT, 
        EFF_TM, EXP_DT, EXP_TM, RLS_DT, RLS_TM, DUE_PROC_MOD, ORD_TYP, ORD_NO, RMK
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from acm_ac_hold
        where HLD_NO = #{hldNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="map" >
        delete from acm_ac_hold
        where AC_NO = #{acNo,jdbcType=VARCHAR}
          and CAP_TYP = #{capTyp,jdbcType=VARCHAR}
          and HLD_NO = #{hldNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.AcmAcHoldDO" >
        insert into acm_ac_hold
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="acNo != null" >
                AC_NO,
            </if>
            <if test="capTyp != null" >
                CAP_TYP,
            </if>
            <if test="hldNo != null" >
                HLD_NO,
            </if>
            <if test="ccy != null" >
                CCY,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="userTyp != null" >
                USER_TYP,
            </if>
            <if test="hldSts != null" >
                HLD_STS,
            </if>
            <if test="hldCd != null" >
                HLD_CD,
            </if>
            <if test="hldBal != null" >
                HLD_BAL,
            </if>
            <if test="effDt != null" >
                EFF_DT,
            </if>
            <if test="effTm != null" >
                EFF_TM,
            </if>
            <if test="expDt != null" >
                EXP_DT,
            </if>
            <if test="expTm != null" >
                EXP_TM,
            </if>
            <if test="rlsDt != null" >
                RLS_DT,
            </if>
            <if test="rlsTm != null" >
                RLS_TM,
            </if>
            <if test="dueProcMod != null" >
                DUE_PROC_MOD,
            </if>
            <if test="ordTyp != null" >
                ORD_TYP,
            </if>
            <if test="ordNo != null" >
                ORD_NO,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="acNo != null" >
                #{acNo,jdbcType=VARCHAR},
            </if>
            <if test="capTyp != null" >
                #{capTyp,jdbcType=VARCHAR},
            </if>
            <if test="hldNo != null" >
                #{hldNo,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userTyp != null" >
                #{userTyp,jdbcType=VARCHAR},
            </if>
            <if test="hldSts != null" >
                #{hldSts,jdbcType=VARCHAR},
            </if>
            <if test="hldCd != null" >
                #{hldCd,jdbcType=VARCHAR},
            </if>
            <if test="hldBal != null" >
                #{hldBal,jdbcType=DECIMAL},
            </if>
            <if test="effDt != null" >
                #{effDt,jdbcType=DATE},
            </if>
            <if test="effTm != null" >
                #{effTm,jdbcType=TIME},
            </if>
            <if test="expDt != null" >
                #{expDt,jdbcType=DATE},
            </if>
            <if test="expTm != null" >
                #{expTm,jdbcType=TIME},
            </if>
            <if test="rlsDt != null" >
                #{rlsDt,jdbcType=DATE},
            </if>
            <if test="rlsTm != null" >
                #{rlsTm,jdbcType=TIME},
            </if>
            <if test="dueProcMod != null" >
                #{dueProcMod,jdbcType=VARCHAR},
            </if>
            <if test="ordTyp != null" >
                #{ordTyp,jdbcType=VARCHAR},
            </if>
            <if test="ordNo != null" >
                #{ordNo,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.acm.entity.AcmAcHoldDO" >
        update acm_ac_hold
        <set >
            <if test="ccy != null" >
                CCY = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userTyp != null" >
                USER_TYP = #{userTyp,jdbcType=VARCHAR},
            </if>
            <if test="hldSts != null" >
                HLD_STS = #{hldSts,jdbcType=VARCHAR},
            </if>
            <if test="hldCd != null" >
                HLD_CD = #{hldCd,jdbcType=VARCHAR},
            </if>
            <if test="hldBal != null" >
                HLD_BAL = #{hldBal,jdbcType=DECIMAL},
            </if>
            <if test="effDt != null" >
                EFF_DT = #{effDt,jdbcType=DATE},
            </if>
            <if test="effTm != null" >
                EFF_TM = #{effTm,jdbcType=TIME},
            </if>
            <if test="expDt != null" >
                EXP_DT = #{expDt,jdbcType=DATE},
            </if>
            <if test="expTm != null" >
                EXP_TM = #{expTm,jdbcType=TIME},
            </if>
            <if test="rlsDt != null" >
                RLS_DT = #{rlsDt,jdbcType=DATE},
            </if>
            <if test="rlsTm != null" >
                RLS_TM = #{rlsTm,jdbcType=TIME},
            </if>
            <if test="dueProcMod != null" >
                DUE_PROC_MOD = #{dueProcMod,jdbcType=VARCHAR},
            </if>
            <if test="ordTyp != null" >
                ORD_TYP = #{ordTyp,jdbcType=VARCHAR},
            </if>
            <if test="ordNo != null" >
                ORD_NO = #{ordNo,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where HLD_NO = #{hldNo,jdbcType=VARCHAR}
    </update>
</mapper>