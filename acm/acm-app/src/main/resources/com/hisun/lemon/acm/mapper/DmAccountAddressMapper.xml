<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.acm.dao.IDmAccountAddressDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.acm.entity.DmAccountAddressDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="vault_code" property="vaultCode" jdbcType="VARCHAR"/>
        <result column="group_code" property="groupCode" jdbcType="VARCHAR"/>
        <result column="account_id" property="accountId" jdbcType="BIGINT"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="network" property="network" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="acm_ac_no" property="acmAcNo" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="qrcode_base64" property="qrCodeBase64" jdbcType="LONGVARCHAR"/>
        <result column="use_type" property="useType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, vault_code, group_code, account_id, address, network, status, acm_ac_no, user_id, qrcode_base64, use_type, create_time, update_time
    </sql>

    <!-- 根据账户编号查询数币账户地址信息 -->
    <select id="getByAcNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_address
        WHERE acm_ac_no = #{acNo,jdbcType=VARCHAR}
    </select>

    <!-- 根据 ID 查询 -->
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_address
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getByUserIdAndNet" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dm_account_address where user_id = #{userId,jdbcType=VARCHAR} and network = #{network,jdbcType=VARCHAR}
    </select>

    <!-- 动态查询 -->
    <select id="find" parameterType="com.hisun.lemon.acm.entity.DmAccountAddressDO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_address
        <where>
            <if test="id != null">
                AND id = #{id,jdbcType=BIGINT}
            </if>
            <if test="vaultCode != null">
                AND vault_code = #{vaultCode,jdbcType=VARCHAR}
            </if>
            <if test="groupCode != null">
                AND group_code = #{groupCode,jdbcType=VARCHAR}
            </if>
            <if test="accountId != null">
                AND account_id = #{accountId,jdbcType=BIGINT}
            </if>
            <if test="address != null">
                AND address = #{address,jdbcType=VARCHAR}
            </if>
            <if test="network != null">
                AND network = #{network,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                AND status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="acmAcNo != null">
                AND acm_ac_no = #{acmAcNo,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND user_id = #{userId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--根据地址查询数币账户地址信息-->
    <select id="getByAddress" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_address
        WHERE address = #{address,jdbcType=VARCHAR} and status = "ENABLED"
    </select>

    <!--根据账户编号列表查询账户地址信息-->
    <select id="findByAcNoListAndUseType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_address
        <where>
            <if test="acNoList != null and acNoList.size > 0">
                acm_ac_no IN
                <foreach item="item" collection="acNoList" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="useType != null and useType != ''">
                AND use_type = #{useType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--查询指定网络下两个ID最小的未启用地址记录,加锁-->
    <select id="getTwoMinDisabledAddress" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_address
        WHERE network = #{network,jdbcType=VARCHAR}
        AND status = 'DISABLED'
        ORDER BY id ASC
        LIMIT 2
        FOR UPDATE
    </select>

    <!-- 查询ID最小的未启用地址记录，加锁 -->
    <select id="getMinDisabledAddress" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_account_address
        WHERE network = #{network,jdbcType=VARCHAR}
        AND status = 'DISABLED'
        ORDER BY id ASC
        LIMIT 1
        FOR UPDATE
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.hisun.lemon.acm.entity.DmAccountAddressDO">
        INSERT INTO dm_account_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="vaultCode != null">
                vault_code,
            </if>
            <if test="groupCode != null">
                group_code,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="network != null">
                network,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="acmAcNo != null">
                acm_ac_no,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="qrCodeBase64 != null">
                qrcode_base64,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="vaultCode != null">
                #{vaultCode,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="network != null">
                #{network,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="acmAcNo != null">
                #{acmAcNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="qrCodeBase64 != null">
                #{qrCodeBase64,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.hisun.lemon.acm.entity.DmAccountAddressDO">
        UPDATE dm_account_address
        <set>
            <if test="vaultCode != null">
                vault_code = #{vaultCode,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                group_code = #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="network != null">
                network = #{network,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="acmAcNo != null">
                acm_ac_no = #{acmAcNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="qrCodeBase64 != null">
                qrcode_base64 = #{qrCodeBase64,jdbcType=LONGVARCHAR},
            </if>
            <if test="useType != null">
                use_type = #{useType,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                update_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 删除记录 -->
    <delete id="delete" parameterType="java.lang.String">
        DELETE
        FROM dm_account_address
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>