package com.hisun.lemon.acm.service;

import com.hisun.lemon.acm.bo.AccountingBO;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.framework.data.LemonData;
import com.hisun.lemon.framework.data.LemonHolder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @function AccountingTreatmentServiceTest
 * @description 账务处理服务
 * @date 8/4/2017 Fri
 * @time 11:22 AM
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class AccountingTreatmentServiceTest {
    @Autowired
    IAccountingTreatmentService treatmentService;

    @Test
    @Transactional
    @Rollback
    public void testAccountingTreatment() {
        ArrayList<AccountingBO> accountingBOList = new ArrayList<AccountingBO>();
        LemonData lemonData = new LemonData();
        lemonData.setAccDate(LocalDate.now());
        LemonHolder.setLemonData(lemonData);
        AccountingBO accountingBO = new AccountingBO();
        accountingBO.setAcNo("***************");
        accountingBO.setAcTyp(ACMConstants.USER_AC_TYP);
        accountingBO.setDcFlg(ACMConstants.AC_C_FLG);
        accountingBO.setCapTyp("1");
        accountingBO.setTxSts(ACMConstants.ACCOUNTING_NOMARL);
        accountingBO.setTxJrnNo("***************");
        accountingBO.setItmNo("100001");
        accountingBO.setTxTyp("消费");
        accountingBO.setTxJrnNo("***************");
        accountingBO.setTxAmt(BigDecimal.ONE);
        accountingBO.setTxOrdNo("***************");
        accountingBO.setTxOrdDt(LocalDate.now());
        accountingBO.setTxOrdTm(LocalTime.now());
        accountingBO.setOppAcNo("");
        accountingBO.setOppCapTyp("");
        accountingBO.setOppUserId("");
        accountingBO.setOppUserTyp("");
        accountingBOList.add(accountingBO);
        AccountingBO accountingBO1 = new AccountingBO();
        accountingBO1.setAcNo("***************");
        accountingBO1.setAcTyp(ACMConstants.ITM_AC_TYP);
        accountingBO1.setDcFlg(ACMConstants.AC_D_FLG);
        accountingBO1.setCapTyp("1");
        accountingBO1.setTxSts(ACMConstants.ACCOUNTING_NOMARL);
        accountingBO1.setTxJrnNo("***************");
        accountingBO1.setItmNo("100001");
        accountingBO1.setTxTyp("消费");
        accountingBO1.setTxJrnNo("***************");
        accountingBO1.setTxAmt(BigDecimal.ONE);
        accountingBO1.setTxOrdNo("***************");
        accountingBO1.setTxOrdDt(LocalDate.now());
        accountingBO1.setTxOrdTm(LocalTime.now());
        accountingBO1.setOppAcNo("");
        accountingBO1.setOppCapTyp("");
        accountingBO1.setOppUserId("");
        accountingBO1.setOppUserTyp("");
        accountingBOList.add(accountingBO1);
        AccountingBO accountingBO2 = new AccountingBO();
        BeanUtils.copyProperties(accountingBO2, accountingBO1);
        accountingBOList.add(accountingBO2);
        AccountingBO accountingBO3 = new AccountingBO();
        BeanUtils.copyProperties(accountingBO3, accountingBO);
        accountingBOList.add(accountingBO3);
        treatmentService.accountingTreatment(accountingBOList);
        accountingBO.setTxSts(ACMConstants.ACCOUNTING_CANCEL);
        accountingBO1.setTxSts(ACMConstants.ACCOUNTING_CANCEL);
        accountingBO2.setTxSts(ACMConstants.ACCOUNTING_CANCEL);
        accountingBO3.setTxSts(ACMConstants.ACCOUNTING_CANCEL);
        accountingBOList.clear();
        accountingBOList.add(accountingBO);
        accountingBOList.add(accountingBO1);
        accountingBOList.add(accountingBO2);
        accountingBOList.add(accountingBO3);
        treatmentService.accountingTreatment(accountingBOList);
    }
}
