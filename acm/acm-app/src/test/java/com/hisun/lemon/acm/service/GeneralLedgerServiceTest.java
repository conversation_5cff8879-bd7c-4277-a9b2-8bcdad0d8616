package com.hisun.lemon.acm.service;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @function GeneralLedgerServiceTest
 * @description 日终批量测试
 * @date 8/14/2017 Mon
 * @time 5:06 PM
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class GeneralLedgerServiceTest {
    @Resource
    private IGeneralLedgerService ledgerService;

    @Test
    @Transactional
    @Rollback
    public void testBatch() throws Exception {
//        LocalDate acDt = LemonUtils.getAccDate().minusDays(1);
        LocalDate acDt = LocalDate.of(2017, 8, 12);
        ledgerService.exportCapitalDetail(acDt);
        ledgerService.updateItemBalance(acDt);
        ledgerService.checkAllVouchers(acDt);
        ledgerService.checkGeneralLedger(acDt);
    }

}
