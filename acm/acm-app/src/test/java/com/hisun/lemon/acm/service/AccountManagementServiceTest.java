package com.hisun.lemon.acm.service;

import com.google.gson.Gson;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.dao.IAcmAcBalDao;
import com.hisun.lemon.acm.dao.IAcmAcInfDao;
import com.hisun.lemon.acm.dao.IAcmItmBalDao;
import com.hisun.lemon.acm.dao.IAcmItmInfDao;
import com.hisun.lemon.acm.dto.UserCcyAccountListDTO;
import com.hisun.lemon.acm.dto.UserCcyAccountRspDTO;
import com.hisun.lemon.acm.dto.UserDmAccountListDTO;
import com.hisun.lemon.acm.dto.UserDmAccountRspDTO;
import com.hisun.lemon.acm.entity.AcmAcBalDO;
import com.hisun.lemon.acm.entity.AcmItmInfDO;
import com.hisun.lemon.common.utils.DateTimeUtils;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @function AccountManagementServiceTest
 * @description FeignClient测试
 * @date 8/3/2017 Thu
 * @time 8:49 PM
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class AccountManagementServiceTest {
    @Autowired
    IAccountManagementService accountManagementService;

    @Autowired
    IAcmAcInfDao iAcmAcInfDao;

    @Autowired
    IAcmAcBalDao iAcmAcBalDao;

    @Autowired
    IAcmItmInfDao iAcmItmInfDao;

    @Autowired
    IAcmItmBalDao iAcmItmBalDao;

    @Before
    public void before() throws Exception{
    }

    @Test
    @Transactional
    @Rollback
    public void testOpenUserAccount() {
        String acNo = accountManagementService.openUserAccount("2");
        System.out.println(acNo);
//        Map<String, String> mapAcInf = new HashMap<String, String>();
//        Map<String, String> mapBal = new HashMap<String, String>();
//        mapAcInf.put("acNo", acNo);
//        mapAcInf.put("ccy", "USD");
//        iAcmAcInfDao.delete(mapAcInf);
//        mapBal.put("acNo", acNo);
//        for (CapTypEnum capTyp : CapTypEnum.values()) {
//            mapBal.put("capTyp", capTyp.getCapTyp());
//            iAcmAcBalDao.delete(mapBal);
//        }
    }

    @Test
    @Transactional
    @Rollback
    public void testOpenItemAccount() {
        AcmItmInfDO acmItmInfDO = new AcmItmInfDO();
        iAcmItmInfDao.delete("100001");
        iAcmItmBalDao.delete("100001");
        acmItmInfDO.setItmNo("100001");
        acmItmInfDO.setItmSts("0");
        acmItmInfDO.setItmCnm("账户余额");
        acmItmInfDO.setItmEnm("BAL ITM");
        acmItmInfDO.setItmCls("1");
        acmItmInfDO.setItmTyp("A");
        acmItmInfDO.setItmLvl(2);
        acmItmInfDO.setBalDrt("C");
        acmItmInfDO.setBalOdFlg("Y");
        acmItmInfDO.setBtmItmFlg("Y");
        acmItmInfDO.setUpdOpr("SYS");
        acmItmInfDO.setUpdBalFlg(ACMConstants.BAL_REAL_TIME_UPD);
        acmItmInfDO.setEffDt(DateTimeUtils.getCurrentLocalDate());
        acmItmInfDO.setExpDt(LocalDate.parse("9999-12-31"));
        accountManagementService.openItemAccount(acmItmInfDO);
    }

    @Test
    @Transactional
    @Rollback
    public void testCloseUserAccount() {
        String userId = "1";
        accountManagementService.closeUserAccount(userId);
//        AcmAcInfDO acInfDO = new AcmAcInfDO();
//        acInfDO.setUserId(userId);
//        acInfDO.setAcSts(ACMConstants.AC_OPEN_STS);
//        iAcmAcInfDao.update(acInfDO);
    }

    @Test
    @Transactional
    @Rollback
    public void testCloseItemAccount() {
        String itmNo = "100001";
        accountManagementService.closeItemAccount(itmNo);
//        AcmItmInfDO itmInfDO = new AcmItmInfDO();
//        itmInfDO.setItmNo(itmNo);
//        itmInfDO.setItmSts(ACMConstants.ITM_OPEN_STS);
//        iAcmItmInfDao.update(itmInfDO);
    }

    @Test
    public void testQueryAcBal() {
        String acNo = null;
        String capTyp = "8";
        String userId = "1";
        List<AcmAcBalDO> acBalDOS = accountManagementService.queryAcBal(acNo, userId, capTyp);
        Gson gson = new Gson();
        System.out.println(gson.toJson(acBalDOS));
    }

    @Test
    public void testQueryAcNo() {
        String userId = "1";
        String acNo = accountManagementService.queryAcNo(userId);
        System.out.println(acNo);
    }

//    @Test
//    @Transactional
//    @Rollback
//    public void testQueryUserAccountList() {
//        UserCcyAccountListDTO userCcyAccountListDTO = new UserCcyAccountListDTO();
//        userCcyAccountListDTO.setUserId("111");
//        List<UserCcyAccountRspDTO> list = accountManagementService.getUserCcyAcountList(userCcyAccountListDTO);
//        System.out.println(list);
//    }

//    @Test
//    @Transactional
//    @Rollback
//    public void testQueryUserDmAccountList() {
//        UserDmAccountListDTO dto = new UserDmAccountListDTO();
//        dto.setUserId("111");
//        List<UserDmAccountRspDTO> list = accountManagementService.getUserDmAcountList(dto);
//        System.out.println(list);
//    }
}
