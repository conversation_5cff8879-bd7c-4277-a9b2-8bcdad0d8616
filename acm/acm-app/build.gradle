apply plugin: 'application'

dependencies {
    compile project(":acm-interface")
    compile("com.hisun:lemon-swagger")
    compile("com.hisun:lemon-framework")
    compile("org.springframework.boot:spring-boot-starter-thymeleaf")
    compile("com.hisun:urm-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:cpi-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:tam-interface")
    // 添加 Hutool 依赖
    compile ('cn.hutool:hutool-all:5.8.16')
    // 添加 ZXing 依赖，Hutool 的 QrCodeUtil 依赖它
    compile ('com.google.zxing:core:3.5.3')
}

mainClassName = 'com.hisun.lemon.Application'

jar {
    manifest {
        attributes(
                   "Implementation-Title": "Gradle",
                   "Implementation-Version": "${version}",
                   "Class-Path": '. config/'
                  )
    }
//    exclude('config/')
}

bootRepackage {
    enabled = true
    mainClass = 'com.hisun.lemon.Application'
}

task clearTarget(type:Delete){
   delete 'build/target'
}

task release(type: Copy,dependsOn: [clearTarget,build]) {
    from('build/libs') {
        include '*.jar'
        exclude '*-sources.jar'
    }
//    from('src/main/resources') {
//        include 'config/*'
//    }
    into ('build/target')

    into('bin') {
        from 'shell'
    }
}

task dist(type: Zip,dependsOn: [release]) {
    from ('build/target/') {
    }
}