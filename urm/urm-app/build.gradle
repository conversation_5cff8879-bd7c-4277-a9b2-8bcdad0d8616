apply plugin: 'application'

dependencies {
    compile project(":urm-interface")
    compile("com.hisun:lemon-swagger")
    compile("com.hisun:lemon-framework")
    compile("com.hisun:jcommon")
    compile("com.hisun:acm-interface")
    compile("com.hisun:cmm-interface")
    compile("org.springframework.boot:spring-boot-starter-thymeleaf")
    compile("com.hisun:hsm")
    compile("com.hisun:rsm-interface")
    compile("com.hisun:mkm-interface")
    compile("com.hisun:inv-interface")
    compile("com.hisun:cpi-interface")
    compile('org.springframework.boot:spring-boot-starter-mail')
    compile('com.sun.mail:javax.mail')
    compile('eu.bitwalker:UserAgentUtils')
    compile('com.maxmind.geoip2:geoip2')
}

mainClassName = 'com.hisun.lemon.Application'

jar {
    manifest {
        attributes(
                   "Implementation-Title": "Gradle",
                   "Implementation-Version": "${version}",
                   "Class-Path": '. config/'
                  )
    }
//    exclude('config/')
}

bootRepackage {
    enabled = true
    mainClass = 'com.hisun.lemon.Application'
}

task clearTarget(type:Delete){  
   delete 'build/target'  
}  

task release(type: Copy,dependsOn: [clearTarget,build]) {  
    from('build/libs') {  
        include '*.jar'
        exclude '*-sources.jar'  
    }  
//    from('src/main/resources') {
//        include 'config/*'
//    }
    into ('build/target') 
    
    into('bin') {
        from 'shell'
    } 
} 

task dist(type: Zip,dependsOn: [release]) {  
    from ('build/target/') {
    } 
}

