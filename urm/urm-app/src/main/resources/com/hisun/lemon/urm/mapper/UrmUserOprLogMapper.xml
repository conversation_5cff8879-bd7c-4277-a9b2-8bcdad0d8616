<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.UrmUserOprLogDao">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.UrmUserOprLogDO">
        <!-- 主键映射 -->
        <id column="id" property="id" jdbcType="INTEGER" />
        <!-- 普通字段映射 -->
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="ip" property="ip" jdbcType="VARCHAR" />
        <result column="location" property="location" jdbcType="VARCHAR" />
        <result column="device" property="device" jdbcType="VARCHAR" />
        <result column="opr_nm" property="oprNm" jdbcType="VARCHAR" />
        <result column="login_id" property="loginId" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmUserOprLogDO">
        insert into urm_user_opr_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="ip != null" >
                ip,
            </if>
            <if test="location != null" >
                location,
            </if>
            <if test="device != null" >
                device,
            </if>
            <if test="oprNm != null" >
                opr_nm,
            </if>
            <if test="loginId != null" >
                login_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ip != null" >
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="location != null" >
                #{location,jdbcType=VARCHAR},
            </if>
            <if test="device != null" >
                #{device,jdbcType=VARCHAR},
            </if>
            <if test="oprNm != null" >
                #{oprNm,jdbcType=VARCHAR},
            </if>
            <if test="loginId != null" >
                #{loginId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateLoginId">
        update urm_user_opr_log
        <set>
            <if test="newLoginId != null" >
                login_id = #{newLoginId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{userId,jdbcType=INTEGER}
    </update>
    <select id="getByLoginId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from urm_user_opr_log where login_id = #{loginId,jdbcType=VARCHAR}
        order by create_time desc
        limit 5
    </select>
</mapper>