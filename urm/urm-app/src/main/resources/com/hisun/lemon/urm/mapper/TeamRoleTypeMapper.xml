<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.TeamRoleTypeDao">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.TeamRoleTypeDO">
        <result column="ROLE_ID" property="roleId" jdbcType="VARCHAR" />
        <result column="TYPE_NAME" property="typeName" jdbcType="VARCHAR" />
        <result column="REMARK" property="remark" jdbcType="VARCHAR" />
        <result column="STATUS" property="status" jdbcType="CHAR" />
        <result column="PERM_TYPE" property="permType" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_ID" property="createId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ROLE_ID, TYPE_NAME, REMARK, STATUS, PERM_TYPE, MODIFY_TIME, CREATE_TIME, CREATE_ID
    </sql>
    
    <select id="queryRoleList" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from role_type where CREATE_ID = #{userId, jdbcType=VARCHAR}
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from role_type where ROLE_ID = #{roleId, jdbcType=VARCHAR}
    </select>
    <select id="getRoleName" resultType="java.lang.String">
        select TYPE_NAME from role_type where ROLE_ID = #{roleId, jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from role_type
        where ROLE_ID = #{roleId, jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.TeamRoleTypeDO">
        insert into role_type
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="roleId != null" >
                ROLE_ID,
            </if>
            <if test="typeName != null" >
                TYPE_NAME,
            </if>
            <if test="remark != null" >
                REMARK,
            </if>
            <if test="status != null" >
                STATUS,
            </if>
            <if test="permType != null" >
                PERM_TYPE,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="createId != null" >
                CREATE_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="roleId != null" >
                #{roleId,jdbcType=VARCHAR},
            </if>
            <if test="typeName != null" >
                #{typeName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=CHAR},
            </if>
            <if test="permType != null" >
                #{permType,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createId != null" >
                #{createId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.TeamRoleTypeDO">
        update role_type
        <set>
            <if test="typeName != null" >
                TYPE_NAME = #{typeName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                STATUS = #{status,jdbcType=CHAR},
            </if>
            <if test="permType != null" >
                PERM_TYPE =  #{permType,jdbcType=VARCHAR},
            </if>
            <if test="createId != null" >
                CREATE_ID = #{createId,jdbcType=VARCHAR},
            </if>
        </set>
        where ROLE_ID = #{roleId, jdbcType=VARCHAR}
    </update>
</mapper>