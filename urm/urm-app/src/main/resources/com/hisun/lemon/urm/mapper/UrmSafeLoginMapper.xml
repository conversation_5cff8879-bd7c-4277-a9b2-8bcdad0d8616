<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IUrmSafeLoginDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.UrmSafeLoginDO" >
        <id column="LOGIN_ID" property="loginId" jdbcType="VARCHAR" />
        <result column="SAFE_ID" property="safeId" jdbcType="VARCHAR" />
        <result column="LOGIN_TYP" property="loginTyp" jdbcType="VARCHAR" />
        <result column="DISPLAY_NM" property="displayNm" jdbcType="VARCHAR" />
        <result column="AVATAR_PATH" property="avatarPath" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        LOGIN_ID, SAFE_ID, LOGIN_TYP, DISPLAY_NM, AVATAR_PATH
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from urm_safe_login
        where LOGIN_ID = #{loginId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from urm_safe_login
        where LOGIN_ID = #{loginId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmSafeLoginDO" >
        insert into urm_safe_login
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="loginId != null" >
                LOGIN_ID,
            </if>
            <if test="safeId != null" >
                SAFE_ID,
            </if>
            <if test="loginTyp != null" >
                LOGIN_TYP,
            </if>
            <if test="displayNm != null" >
                DISPLAY_NM,
            </if>
            <if test="avatarPath != null" >
                AVATAR_PATH,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="loginId != null" >
                #{loginId,jdbcType=VARCHAR},
            </if>
            <if test="safeId != null" >
                #{safeId,jdbcType=VARCHAR},
            </if>
            <if test="loginTyp != null" >
                #{loginTyp,jdbcType=VARCHAR},
            </if>
            <if test="displayNm != null" >
                #{displayNm,jdbcType=VARCHAR},
            </if>
            <if test="avatarPath!= null" >
                #{avatarPath,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.UrmSafeLoginDO" >
        update urm_safe_login
        <set >
            <if test="safeId != null" >
                SAFE_ID = #{safeId,jdbcType=VARCHAR},
            </if>
            <if test="loginTyp != null" >
                LOGIN_TYP = #{loginTyp,jdbcType=VARCHAR},
            </if>
            <if test="displayNm!= null" >
                DISPLAY_NM = #{displayNm,jdbcType=VARCHAR},
            </if>
            <if test="avatarPath != null" >
                AVATAR_PATH = #{avatarPath ,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where LOGIN_ID = #{loginId,jdbcType=VARCHAR}
    </update>

    <update id="updateBySafeId" parameterType="com.hisun.lemon.urm.entity.UrmSafeLoginDO" >
        update urm_safe_login
        <set >
            <if test="loginTyp != null" >
                LOGIN_TYP = #{loginTyp,jdbcType=VARCHAR},
            </if>
            <if test="displayNm!= null" >
                DISPLAY_NM = #{displayNm,jdbcType=VARCHAR},
            </if>
            <if test="avatarPath != null" >
                AVATAR_PATH = #{avatarPath ,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginId != null" >
                LOGIN_ID = #{loginId, jdbcType=TIMESTAMP},
            </if>
        </set>
        where SAFE_ID = #{safeId,jdbcType=VARCHAR}
    </update>
    <update id="updateEmail">
        update urm_safe_login
        set LOGIN_ID = #{newEmail,jdbcType=VARCHAR}
        where LOGIN_ID  = #{userId,jdbcType=VARCHAR}
    </update>
</mapper>