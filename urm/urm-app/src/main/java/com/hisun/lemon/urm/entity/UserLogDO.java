package com.hisun.lemon.urm.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.lemon.framework.data.BaseDO;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 获取用户登录信息DO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/6 14:22
 */
public class UserLogDO extends BaseDO {
    //账号ID
    /**
     * 账户ID
     */
    @ApiModelProperty(name = "userId", value = "账户ID", dataType = "Long")
    private String userId;
    //账号名称
    /**
     * 账户名称
     */
    @ApiModelProperty(name = "userName", value = "账户名称", dataType = "String")
    private String userName;
    //注册时间
    /**
     * 注册时间
     */
    @ApiModelProperty(name = "registerTime", value = "注册时间", dataType = "Date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;
    //账户角色
    /**
     * 账户角色
     */
    @ApiModelProperty(name = "userRole", value = "账户角色", dataType = "String")
    private String userRole;
    /**
     * 账户状态
     */
    @ApiModelProperty(name = "userStatus", value = "账户状态", dataType = "String")
    private String userStatus;
    /**
     * 登录情况
     */
    @ApiModelProperty(name = "loginInfo", value = "登录情况", dataType = "List<LoginInfo>")
    private List<LoginInfo> loginInfoList;

    public static class LoginInfo {
        /**
         * 登录时间
         */
        @ApiModelProperty(name = "loginTime", value = "登录时间", dataType = "Date")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime loginTime;
        /**
         * 登录设备
         */
        @ApiModelProperty(name = "loginDevice", value = "登录设备", dataType = "String")
        private String loginDevice;
        /**
         * 登录IP
         */
        @ApiModelProperty(name = "loginIp", value = "登录IP", dataType = "String")
        private String loginIp;
        /**
         * 登录地点
         */
        @ApiModelProperty(name = "location", value = "登录地点", dataType = "String")
        private String location;

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public LocalDateTime getLoginTime() {
            return loginTime;
        }

        public void setLoginTime(LocalDateTime loginTime) {
            this.loginTime = loginTime;
        }

        public String getLoginDevice() {
            return loginDevice;
        }

        public void setLoginDevice(String loginDevice) {
            this.loginDevice = loginDevice;
        }

        public String getLoginIp() {
            return loginIp;
        }

        public void setLoginIp(String loginIp) {
            this.loginIp = loginIp;
        }
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public LocalDateTime getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(LocalDateTime registerTime) {
        this.registerTime = registerTime;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public List<LoginInfo> getLoginInfoList() {
        return loginInfoList;
    }

    public void setLoginInfoList(List<LoginInfo> loginInfoList) {
        this.loginInfoList = loginInfoList;
    }

}
