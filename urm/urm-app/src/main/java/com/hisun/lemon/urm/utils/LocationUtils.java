package com.hisun.lemon.urm.utils;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.File;
import java.net.InetAddress;

public class LocationUtils {

    private static final Logger logger = LoggerFactory.getLogger(LocationUtils.class);

    public static String getLocation(String ipAddress) {
        try {
            Resource resource = new ClassPathResource("GeoLite2-City.mmdb");
            File dataBase = resource.getFile();
            DatabaseReader dbReader = new DatabaseReader.Builder(dataBase).build();
            // 解析 IP 地址
            InetAddress inetAddress = InetAddress.getByName(ipAddress);
            // 获取对应 IP 的地理位置信息
            CityResponse response = dbReader.city(inetAddress);

            // 解析地理位置信息
            Country country = response.getCountry();
            City city = response.getCity();

            return country.getName()+","+city.getName();
        } catch (Exception e) {
            // 处理异常（如内网IP、无效IP）
            logger.warn("解析IP地址失败: {}", ipAddress, e);
            return "Unknown"; // 返回默认未知信息
        }
    }
}
