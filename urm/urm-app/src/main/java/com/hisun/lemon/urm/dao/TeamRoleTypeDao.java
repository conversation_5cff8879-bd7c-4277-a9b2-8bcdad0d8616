package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.TeamRoleTypeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TeamRoleTypeDao extends BaseDao<TeamRoleTypeDO> {

    List<TeamRoleTypeDO> queryRoleList(@Param("userId") String userId);

    String getRoleName(String roleId);
}
