package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import io.swagger.annotations.ApiModelProperty;

public class TeamRoleTypeDO extends BaseDO {

    @ApiModelProperty(name = "roleId", value = "角色ID")
    private String roleId;

    @ApiModelProperty(name = "typeName", value = "角色类型名称")
    private String typeName;

    @ApiModelProperty(name = "remark", value = "角色备注")
    private String remark;

    @ApiModelProperty(name = "status", value = "状态 0-无效 1-正常", example = "1")
    private String status;

    @ApiModelProperty(name = "permType", value = "角色类型拥有的权限（对应的页面路由）")
    private String permType;

    @ApiModelProperty(name = "createId", value = "创建人")
    private String createId;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPermType() {
        return permType;
    }

    public void setPermType(String permType) {
        this.permType = permType;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
}
