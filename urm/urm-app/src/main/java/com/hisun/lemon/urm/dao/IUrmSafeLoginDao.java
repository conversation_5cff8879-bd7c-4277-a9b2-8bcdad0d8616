/*
 * @ClassName IUrmSafeLoginDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.UrmSafeLoginDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IUrmSafeLoginDao extends BaseDao<UrmSafeLoginDO> {
    /**
     * 通过safeId更新用户登录信息
     * @param safeLoginDO
     * @return
     */
    public int updateBySafeId(UrmSafeLoginDO safeLoginDO);

    void updateEmail(@Param("userId") String userId, @Param("newEmail") String newEmail);
}