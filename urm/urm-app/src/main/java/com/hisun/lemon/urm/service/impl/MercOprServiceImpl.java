package com.hisun.lemon.urm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.NumberUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IUrmMercOprAuthDao;
import com.hisun.lemon.urm.dao.IUrmSafeInfDao;
import com.hisun.lemon.urm.dao.IUrmSafeLoginDao;
import com.hisun.lemon.urm.dao.TeamRoleTypeDao;
import com.hisun.lemon.urm.dto.TeamOprAddDTO;
import com.hisun.lemon.urm.dto.TeamOprInfoDTO;
import com.hisun.lemon.urm.dto.TeamRoleConfigDTO;
import com.hisun.lemon.urm.entity.*;
import com.hisun.lemon.urm.service.IMercOprService;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils.convertPwdfromZpkToPvk;

/**
 * <AUTHOR>
 * @create 2017/10/12
 */
@Service
@Transactional
public class MercOprServiceImpl extends BaseService implements IMercOprService {

    private static final Logger logger = LoggerFactory.getLogger(MercOprServiceImpl.class);

    @Resource
    private IUrmSafeLoginDao urmSafeLoginDao;

    @Resource
    private IUrmSafeInfDao urmSafeInfDao;

    @Resource
    private IUrmMercOprAuthDao urmMercOprAuthDao;

    @Resource
    private TeamRoleTypeDao teamRoleTypeDao;

    @Override
    public void addMercOpr(UrmSafeLoginDO urmSafeLoginDO, UrmSafeInfDO urmSafeInfDO) {

        UrmSafeLoginDO search = urmSafeLoginDao.get(urmSafeLoginDO.getLoginId());
        if (JudgeUtils.isNotNull(search)) {
            throw new LemonException(URMMessageCode.LOGIN_ID_IS_EXISTS);
        }

        List<UrmSafeInfDO> temp = urmSafeInfDao.findByUserId(urmSafeInfDO.getUserId());
        String password = null;
        for (UrmSafeInfDO infDO : temp) {
            if (JudgeUtils.equals(infDO.getOprTyp(), URMConstants.MER_ADMIN_TYP)) {
                password = infDO.getPayPwd();
            }
        }

        if (JudgeUtils.isNull(password)) {
            LemonException.throwBusinessException(URMMessageCode.MER_INF_IS_NULL);
        }
        String userId = urmSafeInfDO.getUserId();
        String loginPwd = urmSafeInfDO.getLoginPwd();
        if (loginPwd.length() != URMConstants.FIFTEEN_PWD_LEN) {
            loginPwd = convertPwdfromZpkToPvk(userId, loginPwd);
        }
        urmSafeInfDO.setLoginPwd(loginPwd);

        String safeId = IdGenUtils.generateId("SafeId", 16);
        urmSafeLoginDO.setSafeId(safeId);
        urmSafeLoginDO.setLoginTyp(URMConstants.USER_DEFINE_LOGIN_ID);
        urmSafeLoginDO.setCreateTime(LocalDateTime.now());
        urmSafeLoginDO.setModifyTime(LocalDateTime.now());

        urmSafeInfDO.setSafeId(safeId);
        urmSafeInfDO.setOprTyp(URMConstants.MER_OPE_TYP);
        urmSafeInfDO.setSafeSts(URMConstants.SAFE_STS_EFF);
        urmSafeInfDO.setPwdSalt(urmSafeInfDO.getUserId());
        urmSafeInfDO.setLoginFailCnt(NumberUtils.toByte("0"));
        urmSafeInfDO.setPayPwd(password);
        urmSafeInfDO.setPayFailCnt(NumberUtils.toByte("0"));
        urmSafeInfDO.setCreDt(LocalDate.now());
        urmSafeInfDO.setCreTm(LocalTime.now());
        urmSafeInfDO.setCreateTime(LocalDateTime.now());
        urmSafeInfDO.setModifyTime(LocalDateTime.now());

        MercOprAuthDo mercOprAuthDo = new MercOprAuthDo();
        mercOprAuthDo.setLoginId(urmSafeLoginDO.getLoginId());
        mercOprAuthDo.setCreateTime(LocalDateTime.now());
        mercOprAuthDo.setModifyTime(LocalDateTime.now());
        mercOprAuthDo.setAuthority(URMConstants.MERC_OPR_INIT_AUTH);

        urmMercOprAuthDao.insert(mercOprAuthDo);
        urmSafeLoginDao.insert(urmSafeLoginDO);
        urmSafeInfDao.insert(urmSafeInfDO);
    }

    @Override
    public void modifyOprInfo(String loginId, String mblNo, String displayNm) {
        String safeId = urmSafeLoginDao.get(loginId).getSafeId();
        UrmSafeLoginDO urmSafeLoginDO = new UrmSafeLoginDO();
        urmSafeLoginDO.setLoginId(loginId);
        urmSafeLoginDO.setDisplayNm(displayNm);
        urmSafeLoginDao.update(urmSafeLoginDO);

        UrmSafeInfDO urmSafeInfDO = new UrmSafeInfDO();
        urmSafeInfDO.setSafeId(safeId);
        urmSafeInfDO.setMblNo(mblNo);
        urmSafeInfDO.setModifyTime(LocalDateTime.now());
        urmSafeInfDao.update(urmSafeInfDO);
    }

    @Override
    public void deleteMercOpr(String loginId, String userId) {
        List<MercOprInfoDO> list = urmSafeInfDao.queryMercOprInfoList(userId, null, loginId, URMConstants.MER_OPE_TYP);
        if (list.isEmpty()) {
            throw new LemonException(URMMessageCode.NO_PERMISSION);
        }

        UrmSafeLoginDO urmSafeLoginDO = urmSafeLoginDao.get(loginId);
        String safeId = urmSafeLoginDO.getSafeId();
        UrmSafeInfDO urmSafeInfDO = urmSafeInfDao.getBySafeId(safeId);
        if (JudgeUtils.notEquals(urmSafeInfDO.getOprTyp(), URMConstants.MER_OPE_TYP)) {
            throw new LemonException(URMMessageCode.NOT_MERCHANT_OPERATOR);
        }

        urmSafeLoginDao.delete(loginId);
        urmSafeInfDao.delete(safeId);
        urmMercOprAuthDao.delete(loginId);
    }

    @Override
    public List<MercOprInfoDO> queryAll(String userId, String displayNm, String loginId, String oprTyp, int num, int size) {
        return PageUtils.pageQuery(num, size, () -> urmSafeInfDao.queryMercOprInfoList(userId, displayNm, loginId, oprTyp));
    }

    @Override
    public int queryListSize(String userId) {
        return urmSafeInfDao.queryTotNum(userId);
    }

    @Override
    public String queryAuthority(String loginId) {
        UrmSafeLoginDO urmSafeLoginDO = urmSafeLoginDao.get(loginId);
        String safeId = urmSafeLoginDO.getSafeId();
        UrmSafeInfDO urmSafeInfDO = urmSafeInfDao.getBySafeId(safeId);

        if (JudgeUtils.equals(urmSafeInfDO.getOprTyp(), URMConstants.USR_TYP)) {
            throw new LemonException(URMMessageCode.NOT_MERCHANT_OPERATOR);
        }

        if (JudgeUtils.equals(urmSafeInfDO.getOprTyp(), URMConstants.MER_ADMIN_TYP)) {
            return URMConstants.MERC_OPR_ADMIN_AUTH;
        }

        MercOprAuthDo mercOprAuthDo = urmMercOprAuthDao.get(loginId);
        if (JudgeUtils.isNull(mercOprAuthDo)) {
            mercOprAuthDo.setLoginId(loginId);
            mercOprAuthDo.setCreateTime(LocalDateTime.now());
            mercOprAuthDo.setModifyTime(LocalDateTime.now());
            mercOprAuthDo.setAuthority(URMConstants.MERC_OPR_INIT_AUTH);
            urmMercOprAuthDao.insert(mercOprAuthDo);
        }
        return urmMercOprAuthDao.get(loginId).getAuthority();
    }

    @Override
    public void modifyMercOprAuth(MercOprAuthDo mercOprAuthDo) {
        mercOprAuthDo.setModifyTime(LocalDateTime.now());
        urmMercOprAuthDao.update(mercOprAuthDo);
    }

    @Override
    public List<TeamOprInfoDTO> queryTeamOprList(String userId) {
        List<UrmSafeInfDO> safeList = urmSafeInfDao.queryTeamOprInfoList(userId);
        List<TeamOprInfoDTO> rspDto = new ArrayList<>();
        for (UrmSafeInfDO urmSafeInfDO : safeList) {
            TeamOprInfoDTO teamOprInfoDTO = new TeamOprInfoDTO();
            BeanUtils.copyProperties(teamOprInfoDTO, urmSafeInfDO);
            TeamRoleTypeDO teamRoleTypeDO = teamRoleTypeDao.get(urmSafeInfDO.getRoleId());
            teamOprInfoDTO.setTypeName(teamRoleTypeDO.getTypeName());
            rspDto.add(teamOprInfoDTO);
        }
        return rspDto;
    }

    @Override
    public void modifyTeamOpr(TeamOprInfoDTO teamOprInfoDTO) {
        UrmSafeInfDO safeInfDO = new UrmSafeInfDO();
        BeanUtils.copyProperties(safeInfDO, teamOprInfoDTO);
        safeInfDO.setEmail(null);
        urmSafeInfDao.update(safeInfDO);
    }

    @Override
    public void deleteTeamOpr(TeamOprInfoDTO teamOprInfoDTO) {
        String safeId = teamOprInfoDTO.getSafeId();
        String email = teamOprInfoDTO.getEmail();
        urmSafeInfDao.delete(safeId);
        urmSafeLoginDao.delete(email);
    }

    @Override
    public TeamOprInfoDTO searchTeamOpr(String email) {
        UrmSafeInfDO safeInfDO = urmSafeInfDao.searchTeamOpr(email);
        TeamOprInfoDTO teamOprInfoDTO = new TeamOprInfoDTO();
        BeanUtils.copyProperties(teamOprInfoDTO, safeInfDO);
        TeamRoleTypeDO teamRoleTypeDO = teamRoleTypeDao.get(safeInfDO.getRoleId());
        teamOprInfoDTO.setTypeName(teamRoleTypeDO.getTypeName());
        return teamOprInfoDTO;
    }

    @Override
    public void addTeamOpr(TeamOprAddDTO teamOprAddDTO) {
        logger.info("团队管理-添加用户");
        String email = teamOprAddDTO.getEmail();
        String userId = LemonUtils.getUserId();
        UrmSafeInfDO existSafeInfDO = urmSafeInfDao.searchTeamOpr(email);
        //校验邮箱是否已经使用
        if(JudgeUtils.isNotNull(existSafeInfDO) && JudgeUtils.equals(URMConstants.USR_OPEN, existSafeInfDO.getSafeSts())) {
            logger.info("该邮箱已使用：{}",email);
            LemonException.throwLemonException(URMMessageCode.USR_ALREADY_REGISTERED);
        }
        String safeId = IdGenUtils.generateId("SafeId", 16);
        UrmSafeInfDO safeInfDO = new UrmSafeInfDO();
        safeInfDO.setSafeSts(URMConstants.SAFE_STS_EFF);
        safeInfDO.setSafeId(safeId);
        safeInfDO.setUserId(userId);
        safeInfDO.setRoleId(teamOprAddDTO.getRoleType());
        safeInfDO.setOprTyp(URMConstants.MER_OPE_TYP);
        safeInfDO.setSafeStsw(URMConstants.DEFAULT_PASSWORD);
        safeInfDO.setPwdSalt(userId);
        //此处设置默认密码 不允许添加者自行设置密码
        //TODO 把初始密码发送至邮箱
        String loginPwd = "#a123456";
        String payPwd = "123456";
        loginPwd = DigestUtils.md5Hex(loginPwd);
        payPwd = DigestUtils.md5Hex(payPwd);
        //密码md5加密 加验userId
        if (JudgeUtils.isNotBlank(loginPwd)) {
            loginPwd = DigestUtils.md5Hex(loginPwd + userId);
            payPwd = DigestUtils.md5Hex(payPwd + userId);
        }

        try {
            if (JudgeUtils.isNull(loginPwd)) {
                safeInfDO.setLoginPwd("a123456");
            } else {
                safeInfDO.setLoginPwd(loginPwd);
            }
            safeInfDO.setLoginFailCnt(Byte.valueOf("0", 10));
        } catch (Throwable e) {
            LemonException.throwLemonException(e);
        }
        safeInfDO.setPayFailCnt(Byte.valueOf("0", 10));
        safeInfDO.setCreDt(DateTimeUtils.getCurrentLocalDate());
        safeInfDO.setCreTm(DateTimeUtils.getCurrentLocalTime());
        safeInfDO.setPayPwd(payPwd);
        safeInfDO.setPwdSalt(userId);
        safeInfDO.setEmail(email);
        safeInfDO.setUserId(userId);
        urmSafeInfDao.insert(safeInfDO);

        UrmSafeLoginDO safeLoginDO = new UrmSafeLoginDO();
        safeLoginDO.setSafeId(safeId);
        safeLoginDO.setLoginId(email);
        safeLoginDO.setLoginTyp(URMConstants.EMAIL_LOGIN_ID);
        safeLoginDO.setDisplayNm(teamOprAddDTO.getRemark());
        urmSafeLoginDao.insert(safeLoginDO);
    }

    @Override
    public List<TeamRoleConfigDTO> queryTeamRoleList(String userId) {
        List<TeamRoleTypeDO> roleTypeDOS = teamRoleTypeDao.queryRoleList(userId);
        if(JudgeUtils.isEmpty(roleTypeDOS)) {
            return null;
        }
        List<TeamRoleConfigDTO> roleList = new ArrayList<>();
        for(TeamRoleTypeDO teamRoleTypeDO : roleTypeDOS) {
            TeamRoleConfigDTO teamRoleConfigDTO = new TeamRoleConfigDTO();
            BeanUtils.copyProperties(teamRoleConfigDTO, teamRoleTypeDO);
            teamRoleConfigDTO.setUserNum(urmSafeInfDao.countRoleNum(teamRoleTypeDO.getRoleId()));
            roleList.add(teamRoleConfigDTO);
        }
        return roleList;
    }

    @Override
    public void modifyTeamRole(TeamRoleConfigDTO teamRoleConfigDTO) {
        TeamRoleTypeDO teamRoleTypeDO = new TeamRoleTypeDO();
        BeanUtils.copyProperties(teamRoleTypeDO, teamRoleConfigDTO);
        teamRoleTypeDao.update(teamRoleTypeDO);
    }

    @Override
    public void deleteTeamRole(TeamRoleConfigDTO teamRoleConfigDTO) {
        //检查是否有用户绑定该角色
        int count = urmSafeInfDao.countRoleNum(teamRoleConfigDTO.getRoleId());
        if(count > 0) {
            logger.info("该角色下有用户绑定，不能删除：{}", teamRoleConfigDTO.getRoleId());
            LemonException.throwLemonException(URMMessageCode.USR_BIND_ROLE);
        }

        teamRoleTypeDao.delete(teamRoleConfigDTO.getRoleId());
    }

    @Override
    public String addTeamRole(TeamRoleConfigDTO teamRoleConfigDTO) {
        String roleId = IdGenUtils.generateId("RoleId", 16);
        String createId = teamRoleConfigDTO.getUserId();
        TeamRoleTypeDO teamRoleTypeDO = new TeamRoleTypeDO();
        BeanUtils.copyProperties(teamRoleTypeDO, teamRoleConfigDTO);
        teamRoleTypeDO.setRoleId(roleId);
        teamRoleTypeDO.setCreateId(createId);
        teamRoleTypeDO.setStatus(URMConstants.ROLE_STS_NORMAL);
        teamRoleTypeDao.insert(teamRoleTypeDO);
        return roleId;
    }
}
