package com.hisun.lemon.urm.utils;

import eu.bitwalker.useragentutils.UserAgent;

import javax.servlet.http.HttpServletRequest;

public class DeviceUtils {

    /**
     * 获取设备信息
     */
    public static String getDeviceInfo(HttpServletRequest request) {
        String userAgentStr = request.getHeader("User-Agent");
        if (userAgentStr == null || userAgentStr.trim().isEmpty()) {
            return "Unknown Device";
        }

        try {
            UserAgent userAgent = UserAgent.parseUserAgentString(userAgentStr);

            // 获取浏览器信息（增加空值检查）
            String browser = userAgent.getBrowser() != null ? userAgent.getBrowser().getName() : "Unknown Browser";
            String browserVersion = "Unknown Version";
            if (userAgent.getBrowserVersion() != null) {
                browserVersion = userAgent.getBrowserVersion().getVersion();
            }

            // 获取操作系统信息
            String os = userAgent.getOperatingSystem() != null ?
                    userAgent.getOperatingSystem().getName() : "Unknown OS";

            // 特殊处理非标准User-Agent（如Apifox）
            if ("Unknown Browser".equals(browser) && "Unknown Version".equals(browserVersion)) {
                // 提取自定义User-Agent的前缀部分
                int slashIndex = userAgentStr.indexOf('/');
                if (slashIndex > 0) {
                    String appName = userAgentStr.substring(0, slashIndex);
                    String versionPart = userAgentStr.substring(slashIndex + 1);
                    int spaceIndex = versionPart.indexOf(' ');
                    if (spaceIndex > 0) {
                        versionPart = versionPart.substring(0, spaceIndex);
                    }
                    return String.format("%s %s, %s", appName, versionPart, os);
                }
                return userAgentStr + ", " + os;
            }

            return String.format("%s %s, %s", browser, browserVersion, os);
        } catch (Exception e) {
            // 解析失败时返回原始User-Agent
            return userAgentStr + ", Unknown OS";
        }
    }
}
