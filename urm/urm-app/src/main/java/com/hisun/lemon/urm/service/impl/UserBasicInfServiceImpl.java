package com.hisun.lemon.urm.service.impl;

import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.RandomUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.urm.common.AcmService;
import com.hisun.lemon.urm.common.CrmService;
import com.hisun.lemon.urm.common.SensitiveDataHandle;
import com.hisun.lemon.urm.common.SmsSend;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.*;
import com.hisun.lemon.urm.dto.MercListInfDTO;
import com.hisun.lemon.urm.dto.TeamRoleConfigDTO;
import com.hisun.lemon.urm.dto.UrmMercInfDTO;
import com.hisun.lemon.urm.entity.*;
import com.hisun.lemon.urm.service.IMercOprService;
import com.hisun.lemon.urm.service.IUserAuthenticationService;
import com.hisun.lemon.urm.service.IUserBasicInfService;
import com.hisun.lemon.urm.utils.DeviceUtils;
import com.hisun.lemon.urm.utils.IpUtils;
import com.hisun.lemon.urm.utils.KeyDataHideUtils;
import com.hisun.lemon.urm.utils.LocationUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @funciton UserManagementDao
 * @description 用户管理传输对象
 * @date 7/8/2017 FRI
 * @time 4:44 PM
 */
@Transactional
@Service
public class UserBasicInfServiceImpl extends BaseService implements IUserBasicInfService {
    private static final Logger logger = LoggerFactory.getLogger(UserBasicInfServiceImpl.class);
    @Resource
    private IUrmUserBasicInfDao userBasicInfDao;

    @Autowired
    @Qualifier("redisTemplateString")
    RedisTemplate<String, String> redisTemplate;
    @Resource
    private IUserAuthenticationService iUserAuthenticationService;

    @Resource
    private IUrmSafeInfDao safeInfDao;

    @Resource
    private IUrmCprExtInfDao cprExtInfDao;
    @Resource
    private TeamRoleTypeDao teamRoleTypeDao;
    @Resource
    private UrmUserOprLogDao urmUserOprLogDao;

    @Resource
    private IUrmCprItfAuthDao cprItfAuthDao;

    @Resource
    private IUrmSafeLoginDao safeLoginDao;

    @Resource
    private IUrmUserRegHistoryDao userRegHistoryDao;

    @Resource
    private AcmService acm;

    @Resource
    private ConstantParamClient paramClient;

    @Resource
    private SensitiveDataHandle sensData;

    @Resource
    private SmsSend sms;

    @Resource
    private CrmService crm;

//    @Resource
//    private IEmailService emailService;

    @Resource
    private MessageSource messageSource;

    @Resource
    private IQuesDao quesDao;

    @Resource
    private IUrmSafeInfDao iUrmSafeInfDao;

    @Resource
    private IBatchOpenUserDao batchOpenUserDao;

    @Resource
    private IUrmUserBasicInfDao basicInfDao;

    @Resource
    private IUserBasicInfService basicInfService;

    @Resource
    private IMercOprService mercOprService;

    @Override
    public String openUser(UrmUserBasicInfDO userBasicInfDO, UrmSafeInfDO safeInfDO, UrmCprExtInfDO
            cprExtInfDO, UrmSafeLoginDO safeLoginDO, String random) {
        String usrLvl = userBasicInfDO.getUsrLvl();
        //默认为888888商户起始编号
        String countryCode = URMConstants.MER_STATIC_ID;
        if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER) || JudgeUtils.equals(usrLvl, URMConstants
                .INDIVIDUAL_BUSINESS)) {
            String mblNo = userBasicInfDO.getMblNo();
            //固定国家为+86
            countryCode = "+86";// MobileHandlerUtils.getNationCode(mblNo);
            if (JudgeUtils.isBlank(countryCode)) {
                LemonException.throwLemonException(URMMessageCode.ILLEGAL_MBL_NO);
            }
            countryCode = String.format("%06d", Integer.valueOf(countryCode));
        }
        String userId = IdGenUtils.generateId("UserId", countryCode, 10);
        //String acNo = "";
        //更新用户基本信息
        if (JudgeUtils.isNotNull(userBasicInfDO)) {
            if (JudgeUtils.isBlank(userBasicInfDO.getUsrLvl())) {
                usrLvl = URMConstants.ORDINARY_USER;
            }
            if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER)) {
                String idNo = userBasicInfDO.getIdNo();
                //TODO 检查证件号是否合法
                String idChkFlg = URMConstants.NOT_REAL_NM_FLG;
//                if (JudgeUtils.isNotBlank(userBasicInfDO.getIdNo()) && JudgeUtils.isNotBlank(userBasicInfDO.getUsrNm())) {
//                    idChkFlg = URMConstants.REAL_NM_FLG;
//                }
                userBasicInfDO.setIdChkFlg(idChkFlg);
                if (JudgeUtils.equals(idChkFlg, URMConstants.REAL_NM_FLG)) {
                    userBasicInfDO.setIdNoHid(KeyDataHideUtils.idNoHide(idNo));
                    userBasicInfDO.setUsrNmHid(userBasicInfDO.getUsrNm());
                }
                if (JudgeUtils.isNotBlank(idNo)) {
                    userBasicInfDO.setIdNoHid(KeyDataHideUtils.idNoHide(idNo));
                    try {
                        //                idNo = sensData.encryptSensitiveData(idNo);
                        userBasicInfDO.setIdNo(idNo);
                    } catch (Exception e) {
                        LemonException.throwLemonException(e);
                    }

                    //证件不能存在五个以上
//                    int count = basicInfDao.countIdNo(idNo);
//                    int maxSameIdNo = URMConstants.MAX_SAME_ID_NO;
//                    if (count >= maxSameIdNo) {
//                        LemonException.throwLemonException(URMMessageCode.ID_EXCEED_MAX_NUM);
//                    }
                }


            }
            userBasicInfDO.setUserId(userId);
            userBasicInfDO.setUsrLvl(usrLvl);
            userBasicInfDO.setUsrRegDt(DateTimeUtils.getCurrentLocalDate());
            userBasicInfDO.setUsrRegTm(DateTimeUtils.getCurrentLocalTime());
            userBasicInfDO.setUsrRegCnl(LemonUtils.getChannel());
            userBasicInfDO.setUsrRegIp(LemonUtils.getClientIp());
            userBasicInfDao.insert(userBasicInfDO);
            //新注册用户不开户
//            acNo = acm.openUserAccount(userId);
//            if (JudgeUtils.isBlank(acNo)) {
//                LemonException.throwLemonException(URMMessageCode.OPEN_USER_FAIL);
//            }
        } else {
            LemonException.throwBusinessException(URMMessageCode.USR_BASIC_INF_IS_NULL);
        }

        //更新用户安全信息表
        if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER)) {
            if (JudgeUtils.isNull(safeInfDO)) {
                LemonException.throwLemonException(URMMessageCode.USR_SAFE_INF_IS_NULL);
            }
//            if (JudgeUtils.isNull(safeInfDO.getSafeAns1()) || JudgeUtils.isNull(safeInfDO.getSafeQues1())) {
//                LemonException.throwBusinessException(URMMessageCode.USR_SAFE_INF_IS_NULL);
//            }
        }
        if (JudgeUtils.isNull(safeInfDO)) {
            safeInfDO = new UrmSafeInfDO();
        }
        //处理密保
//        String safeQues1 = safeInfDO.getSafeQues1();
//        String safeAns1 = safeInfDO.getSafeAns1();
//
//        if (JudgeUtils.equals(userBasicInfDO.getUsrLvl(), URMConstants.ORDINARY_USER)
//                && JudgeUtils.equals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
//            safeQues1 = quesDao.findQuesByQuesNo(Integer.valueOf(safeQues1)).getSafeQues();
//        }
//
//        if (JudgeUtils.isNotBlank(safeAns1) && JudgeUtils.isNotBlank(safeQues1)) {
//            try {
////                safeQues1 = sensData.encryptSensitiveData(safeQues1);
////                safeAns1 = sensData.encryptSensitiveData(safeAns1);
//                safeInfDO.setSafeQues1(safeQues1);
//                safeInfDO.setSafeAns1(safeAns1);
//            } catch (Exception e) {
//                LemonException.throwLemonException(e);
//            }
//        }
        String safeId = IdGenUtils.generateId("SafeId", 16);
        safeInfDO.setSafeSts(URMConstants.SAFE_STS_EFF);
        safeInfDO.setSafeId(safeId);
        safeInfDO.setUserId(userId);
        if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER)) {
            safeInfDO.setOprTyp(URMConstants.USR_TYP);
        } else {
            safeInfDO.setOprTyp(URMConstants.MER_ADMIN_TYP);
        }
        safeInfDO.setSafeStsw(" ");
        safeInfDO.setPwdSalt(userId);
        String loginPwd = safeInfDO.getLoginPwd();
        String payPwd = safeInfDO.getPayPwd();
        String loginPwdPlaintext = null;
        String payPwdPlaintext = null;

        //密码md5加密 加盐userId
        if (JudgeUtils.isNotBlank(loginPwd)) {
            loginPwd = DigestUtils.md5Hex(loginPwd + userId);
        }
        //支付密码加密
        if (JudgeUtils.isNotBlank(payPwd)) {
            payPwd = DigestUtils.md5Hex(payPwd + userId);
        }

        try {
            if (JudgeUtils.isNull(loginPwd)) {
//                    Map<String, String> loginPwdMap = initLoginPwd(userId);
//                    loginPwd = loginPwdMap.get("loginPwd");
//                    loginPwdPlaintext = loginPwdMap.get("loginPwdPlaintext");
//                    logger.debug("loginPwdPlaintext: " + loginPwdPlaintext);
                safeInfDO.setLoginPwd("a123456");
            } else {
//                    if (loginPwd.length() != URMConstants.FIFTEEN_PWD_LEN) {
//                        loginPwd = convertPwdfromZpkToPvk(userId, loginPwd);
//                    }
                safeInfDO.setLoginPwd(loginPwd);
            }
            if (JudgeUtils.isNull(payPwd)) {
//                    Map<String, String> payPwdMap = initPayPwd(acNo);
//                    payPwd = payPwdMap.get("payPwdPinOffset");
//                    payPwdPlaintext = payPwdMap.get("payPwdPlaintext");
//                    logger.debug("payPwdPlaintext: " + payPwdPlaintext);
                safeInfDO.setPayPwd("123456");
            } else {
                //            payPwd = convertPinToPinOffset(acNo, payPwd);
                safeInfDO.setPayPwd(payPwd);
            }

            safeInfDO.setLoginFailCnt(Byte.valueOf("0", 10));
        } catch (Throwable e) {
            LemonException.throwLemonException(e);
        }
        //创建用户成功默认预设一个管理员权限配置
        TeamRoleConfigDTO roleConfig = new TeamRoleConfigDTO();
        roleConfig.setTypeName("Admin");
        roleConfig.setRemark("System generated");
        roleConfig.setPermType("1,2,3,4,5,6,7");//全部页面的权限
        roleConfig.setUserId(userId);
        String roleId = mercOprService.addTeamRole(roleConfig);
        safeInfDO.setPayFailCnt(Byte.valueOf("0", 10));
        safeInfDO.setCreDt(DateTimeUtils.getCurrentLocalDate());
        safeInfDO.setCreTm(DateTimeUtils.getCurrentLocalTime());
        safeInfDO.setRoleId(roleId);
        safeInfDao.insert(safeInfDO);


        //更新用户登录信息表
        String loginId = "";
        String displayNm = "";
        String loginTyp = "";
        if (JudgeUtils.isNotNull(safeLoginDO)) {
            loginId = safeLoginDO.getLoginId();
            displayNm = safeLoginDO.getDisplayNm();
        } else {
            safeLoginDO = new UrmSafeLoginDO();
        }
        if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER)) {
            loginId = safeInfDO.getEmail();
            loginTyp = URMConstants.EMAIL_LOGIN_ID;
            if (JudgeUtils.isNotBlank(userBasicInfDO.getUsrNm())) {
                displayNm = userBasicInfDO.getUsrNm();
            }
        } else if (JudgeUtils.equals(usrLvl, URMConstants.ENTERPRISE_BUSINESS)) {
            if (JudgeUtils.isNotBlank(loginId)) {
                loginId = safeInfDO.getEmail();
                loginTyp = URMConstants.EMAIL_LOGIN_ID;
            } else {
                loginId = safeInfDO.getEmail();
                loginTyp = URMConstants.EMAIL_LOGIN_ID;
            }
        } else {
            LemonException.throwLemonException(URMMessageCode.USRLVL_NOT_SUPPORT);
        }
        safeLoginDO.setSafeId(safeId);
        safeLoginDO.setLoginId(loginId);
        safeLoginDO.setLoginTyp(loginTyp);
        safeLoginDO.setDisplayNm(displayNm);
        safeLoginDao.insert(safeLoginDO);
        logger.info("loginId:" + loginId + " displayNm:" + displayNm);

        //更新商户扩展信息表
        if (JudgeUtils.isNotNull(cprExtInfDO)) {
            cprExtInfDO.setUserId(userId);
            cprExtInfDao.insert(cprExtInfDO);
        }

        //更新商户接口密钥信息表
        if (JudgeUtils.notEquals(usrLvl, URMConstants.ORDINARY_USER)) {
            UrmCprItfAuthDO cprItfAuthDO = new UrmCprItfAuthDO();
            cprItfAuthDO.setUserId(userId);
            cprItfAuthDO.setItfNm("");
            cprItfAuthDO.setVersion(URMConstants.CPR_KEY_VER);
            cprItfAuthDO.setVerifyType(URMConstants.CPR_KEY_TYP);
            String secretKey = RandomUtils.randomLetterFixLength(32);
            cprItfAuthDO.setSecretKey(secretKey);
            cprItfAuthDO.setSts(URMConstants.CPR_KEY_EFF);
            cprItfAuthDao.insert(cprItfAuthDO);

            String[] emailAddr = {safeInfDO.getEmail()};
            if (JudgeUtils.isNotEmpty(emailAddr)) {
                Locale locale = LocaleContextHolder.getLocale();
                String subject = messageSource.getMessage("email.merckey.subject", null, locale);
                String name = userBasicInfDO.getUsrNm();
                String content = messageSource.getMessage("email.merckey.content", null, locale) + secretKey;
                String end = messageSource.getMessage("email.merckey.end", null, locale);
                Email email = new Email();
                email.setSubject(subject);
                email.setTo(emailAddr);
                email.setTemplate("email/merckey");
                Map<String, Object> variables = new HashMap<>();
                variables.put("name", name);
                variables.put("content", content);
                variables.put("end", end);
                logger.info("subject: {}", subject);
                logger.info("name: {}", name);
                logger.info("content: {}", content);
                logger.info("end: {}", end);
                try {
//                    emailService.sendEmail(email, null, variables, locale);
                } catch (Exception e) {
                    logger.error("发送邮件失败" + e.getMessage());
                    //do nothing
                }
            }
        }

        //登记开销户历史
        UrmUserRegHistoryDO userRegHistory = new UrmUserRegHistoryDO();
        BeanUtils.copyProperties(userRegHistory, userBasicInfDO);
        userRegHistoryDao.insert(userRegHistory);

        //商户管理员下发密码短信
        if (JudgeUtils.equals(safeInfDO.getOprTyp(), URMConstants.MER_ADMIN_TYP) && JudgeUtils.isNotBlank(safeInfDO
                .getMblNo())) {
            String mblNo = safeInfDO.getMblNo();
            Map<String, String> smsMap = new HashMap<>();
            smsMap.put("loginId", loginId);
            if (JudgeUtils.isNotBlank(loginPwdPlaintext) && JudgeUtils.isNotBlank(payPwdPlaintext)) {
                smsMap.put("loginPwd", loginPwdPlaintext);
                smsMap.put("payPwd", payPwdPlaintext);
                String smsType = URMConstants.SMS_TYP;
                String templateId = URMConstants.MER_ADMIN_TEMP_ID;
//                try {
//                    sms.smsSend(mblNo, smsType, templateId, smsMap);
//                } catch (Exception e) {
//                    if (e instanceof LemonException) {
//                        LemonException lemon = (LemonException) e;
//                        logger.info("SMS SEND FAIL :" + lemon.getMsgCd());
//                    } else {
//                        logger.info("SMS SERVER NOT AVAILABLE");
//                    }
//                }
            }
        }

        //通知CRM用户开户
//        if (JudgeUtils.equals(userBasicInfDO.getUsrLvl(), URMConstants.ORDINARY_USER) && JudgeUtils.notEquals(LemonUtils
//                .getSource(), URMConstants.INTERNAL_GATE)) {
//            crm.notifyCrm(userBasicInfDO, safeInfDO);
//        }
        return userId;
    }

    @Override
    public void cancelUser(String userId) {
        UrmUserBasicInfDO userBasicInfDO = userBasicInfDao.get(userId);
        if (JudgeUtils.isNull(userBasicInfDO)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        if (JudgeUtils.equals(userBasicInfDO.getUsrSts(), URMConstants.USR_CANCEL)) {
            LemonException.throwLemonException(URMMessageCode.USER_ALREADY_CANCEL);
        }

        //账户销户
        acm.closeUserAccount(userId);
        //用户销户
        userBasicInfDO.setUserId(userId);
        userBasicInfDO.setUsrSts(URMConstants.USR_CANCEL);
        userBasicInfDO.setUsrClsDt(DateTimeUtils.getCurrentLocalDate());
        userBasicInfDO.setUsrClsTm(DateTimeUtils.getCurrentLocalTime());
        userBasicInfDao.update(userBasicInfDO);

        //安全信息置为失效
        List<UrmSafeInfDO> safeInfDOList = safeInfDao.findByUserId(userId);
        if (JudgeUtils.isEmpty(safeInfDOList)) {
            return;
        }
        for (int i = 0; i < safeInfDOList.size(); ++i) {
            UrmSafeInfDO safeInfDO = safeInfDOList.get(i);
            safeInfDO.setUserId(userId);
            safeInfDO.setSafeSts(URMConstants.SAFE_STS_EXP);
            safeInfDao.update(safeInfDO);

            UrmSafeLoginDO safeLoginDO = new UrmSafeLoginDO();
            if (JudgeUtils.equals(safeInfDO.getOprTyp(), URMConstants.ORDINARY_USER)) {
                safeLoginDO.setLoginId(userId.substring(6));
                safeLoginDO.setLoginTyp(URMConstants.SYS_LOGIN_ID);
                safeLoginDO.setSafeId(safeInfDO.getSafeId());
                safeLoginDao.updateBySafeId(safeLoginDO);
            }
        }
        UrmUserRegHistoryDO userRegHistory = new UrmUserRegHistoryDO();
        BeanUtils.copyProperties(userRegHistory, userBasicInfDO);
        userRegHistory.setUsrClsIp(LemonUtils.getClientIp());
        userRegHistory.setUsrClsCnl(LemonUtils.getChannel());
        userRegHistoryDao.update(userRegHistory);
    }

    @Override
    public Map<String, Object> queryUser(String userId) {
        UrmUserBasicInfDO userBasicInfDO = userBasicInfDao.get(userId);
        if (JudgeUtils.isNull(userBasicInfDO) || JudgeUtils.equals(URMConstants.USR_CANCEL, userBasicInfDO.getUsrSts
                ())) {
            LemonException.throwBusinessException(URMMessageCode.USR_NOT_EXIST);
        }
        String usrLvl = userBasicInfDO.getUsrLvl();
        UrmCprExtInfDO cprExtInfDO;
        if (JudgeUtils.notEquals(URMConstants.USR_TYP, usrLvl)) {
            cprExtInfDO = cprExtInfDao.get(userId);
        } else {
            cprExtInfDO = null;
        }
        Map<String, Object> userInfObj = new HashMap<String, Object>();
        userInfObj.put("basicInf", userBasicInfDO);
        userInfObj.put("cprExtInf", cprExtInfDO);
        return userInfObj;
    }

    @Override
    public Map<String, Object> queryUserByLoginId(String loginId) {
        UrmUserBasicInfDO userBasicInfDO = userBasicInfDao.getByLoginId(loginId);
        if (JudgeUtils.isNull(userBasicInfDO) || JudgeUtils.equals(URMConstants.USR_CANCEL, userBasicInfDO.getUsrSts
                ())) {
            LemonException.throwBusinessException(URMMessageCode.USR_NOT_EXIST);
        }
        String userId = userBasicInfDO.getUserId();
        String usrLvl = userBasicInfDO.getUsrLvl();
        UrmCprExtInfDO cprExtInfDO;
        if (JudgeUtils.notEquals(URMConstants.USR_TYP, usrLvl)) {
            cprExtInfDO = cprExtInfDao.get(userId);
        } else {
            cprExtInfDO = null;
        }
        Map<String, Object> userInfObj = new HashMap<String, Object>();
        userInfObj.put("basicInf", userBasicInfDO);
        userInfObj.put("cprExtInf", cprExtInfDO);
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        userInfObj.put("safeInf", safeInfDO);
        UrmSafeLoginDO safeLoginDO = safeLoginDao.get(loginId);
        userInfObj.put("loginInf", safeLoginDO);
        return userInfObj;
    }

    @Override
    public void completeRealName(UrmUserBasicInfDO basicInfDO) {
        String idNo = basicInfDO.getIdNo();
        String idType = basicInfDO.getIdType();
        String userId = basicInfDO.getUserId();
        if (JudgeUtils.isBlank(userId)) {
            LemonException.throwLemonException(URMMessageCode.USER_ID_IS_NULL);
        }
        UrmUserBasicInfDO userInf = userBasicInfDao.get(userId);
        if (JudgeUtils.equals(userInf.getIdChkFlg(), URMConstants.REAL_NM_FLG)) {
            LemonException.throwLemonException(URMMessageCode.USER_ALREADY_REALNM);
        }
        String idNoEnc = "";
        try {
            //      idNoEnc = sensData.encryptSensitiveData(idNo);
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }
        int count = userBasicInfDao.countId(idNoEnc, idType, userId);
        int maxSameIdNo = URMConstants.MAX_SAME_ID_NO;
        try {
            GenericRspDTO<ConstantParamRspDTO> params = paramClient.params("MAX_SAME_ID_NO");
            if (JudgeUtils.isBlank(params.getBody().getParmVal())) {
                maxSameIdNo = URMConstants.MAX_SAME_ID_NO;
            } else {
                maxSameIdNo = NumberUtils.toInt(params.getBody().getParmVal());
            }
        } catch (Exception e) {
            //do nothing
        }
        if (count >= maxSameIdNo) {
            LemonException.throwLemonException(URMMessageCode.ID_EXCEED_MAX_NUM);
        }
        basicInfDO.setIdNo(idNoEnc);
        basicInfDO.setIdChkNo(LemonUtils.getRequestId());
        basicInfDO.setIdNoHid(KeyDataHideUtils.idNoHide(idNo));
        basicInfDO.setUsrNmHid(basicInfDO.getUsrNm());
        basicInfDO.setIdChkFlg(URMConstants.REAL_NM_FLG);
        basicInfDO.setIdChkDt(DateTimeUtils.getCurrentLocalDate());
        basicInfDO.setIdChkTm(DateTimeUtils.getCurrentLocalTime());
        userBasicInfDao.update(basicInfDO);
    }

    @Override
    public UrmCprItfAuthDO queryTradingPrivilege(String userId, String itfNm, String version) {
        if (JudgeUtils.isBlank(userId) || JudgeUtils.isBlank(version)) {
            LemonException.throwLemonException(URMMessageCode.PARAM_IS_NULL);
        }
        if (JudgeUtils.isBlank(itfNm)) {
            itfNm = "";
        }
        UrmCprItfAuthDO itfAuthDO = new UrmCprItfAuthDO();
        itfAuthDO.setUserId(userId);
        itfAuthDO.setItfNm(itfNm);
        itfAuthDO.setVersion(version);
        itfAuthDO = cprItfAuthDao.get(itfAuthDO);
        return itfAuthDO;
    }

    @Override
    public void upgradeInformation(UrmUserBasicInfDO basicInfDO) {
        String idNo = basicInfDO.getIdNo();
        String idType = basicInfDO.getIdType();
        String userId = basicInfDO.getUserId();
        if (JudgeUtils.isBlank(userId)) {
            LemonException.throwLemonException(URMMessageCode.USER_ID_IS_NULL);
        }
        String idNoEnc = "";
        try {
            //         idNoEnc = sensData.encryptSensitiveData(idNo);
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }
//        int count = userBasicInfDao.countId(idNoEnc, idType, userId);
        int count = userBasicInfDao.countIdNo(idNoEnc);
        int maxSameIdNo = URMConstants.MAX_SAME_ID_NO;
        try {
            GenericRspDTO<ConstantParamRspDTO> params = paramClient.params("MAX_SAME_ID_NO");
            if (JudgeUtils.isBlank(params.getBody().getParmVal())) {
                maxSameIdNo = URMConstants.MAX_SAME_ID_NO;
            } else {
                maxSameIdNo = NumberUtils.toInt(params.getBody().getParmVal());
            }
        } catch (Exception e) {
            //do nothing
        }
        if (count >= maxSameIdNo) {
            LemonException.throwLemonException(URMMessageCode.ID_EXCEED_MAX_NUM);
        }
        basicInfDO.setIdNo(idNoEnc);
        basicInfDO.setIdChkNo(LemonUtils.getRequestId());
        basicInfDO.setIdNoHid(KeyDataHideUtils.idNoHide(idNo));
        basicInfDO.setUsrNmHid(basicInfDO.getUsrNm());
        basicInfDO.setIdChkDt(DateTimeUtils.getCurrentLocalDate());
        basicInfDO.setIdChkTm(DateTimeUtils.getCurrentLocalTime());
        userBasicInfDao.update(basicInfDO);
    }

    @Override
    public String querySafeQuesNo(String loginId) {
        UrmSafeInfDO urmSafeInfDO = safeInfDao.get(loginId);
        String ques = urmSafeInfDO.getSafeQues1();
        String safeQues1 = sensData.decryptSensitiveData(ques);
        int result = quesDao.findNoByQues(safeQues1).getQuesNo();
        return String.valueOf(result);
    }

    //查询用户是否设置手势密码
    @Override
    public String queryHandSet() {
        String loginId = LemonUtils.getLoginName();
        UrmSafeInfDO urmSafeInfDO = iUrmSafeInfDao.get(loginId);
        if (urmSafeInfDO == null) {
            LemonException.throwLemonException(URMMessageCode.USR_BASIC_INF_IS_NULL);
        }
        String flg = urmSafeInfDO.getHandFlg();
        if (flg == null) {
            return "0";
        }
        return flg;
    }

    @Override
    public List<BatchOpenUserDo> batchOpenUser(String loginPwd, String payPwd, String ques, String ans) {
        return batchOpenUserDao.getList();
    }

    @Override
    public void checkIdNo(String idNo) {
        try {
            idNo = sensData.encryptSensitiveData(idNo);
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }

        //证件不能存在五个以上
        int count = basicInfDao.countIdNo(idNo);
        int maxSameIdNo = URMConstants.MAX_SAME_ID_NO;
        if (count >= maxSameIdNo) {
            LemonException.throwLemonException(URMMessageCode.ID_EXCEED_MAX_NUM);
        }
    }

    @Override
    public void cleanHandPw(String userId) {
        safeInfDao.updateHandInfo(userId);
    }

    @Override
    public List<String> getAffiliateMerchantList(String userId) {
        return cprExtInfDao.getAffiliateMerchant(userId);
    }

    @Override
    public MercListInfDTO getMercInfoList(List<String> userList, String mercId, String mercNm, int pageNum, int pageSize) {
        if (JudgeUtils.isNull(userList) || userList.isEmpty()) {
            return null;
        }
        PageInfo<MercInfoList> pageInfo = PageUtils.pageQueryWithCount(pageNum, pageSize, () -> basicInfDao.getListInfo(userList, mercId, mercNm));
        MercListInfDTO mercListInfDTO = new MercListInfDTO();
        mercListInfDTO.setTotalNum(pageInfo.getTotal());
        mercListInfDTO.setCurrPage(pageInfo.getPageNum());
        List<UrmMercInfDTO> list = new ArrayList<>();
        for (Object o : pageInfo.getList()) {
            UrmMercInfDTO urmMercInfDTO = new UrmMercInfDTO();
            BeanUtils.copyProperties(urmMercInfDTO, o);
            list.add(urmMercInfDTO);
        }
        mercListInfDTO.setList(list);
        return mercListInfDTO;
    }

    /**
     * 获取用户登录信息
     * @param loginId
     * @return
     */
    @Override
    public UserLogDO getUserLog(String loginId) {
        UrmSafeInfDO urmSafeInfDO = safeInfDao.getByEmail(loginId);
        if (urmSafeInfDO == null) {
            LemonException.throwLemonException(URMMessageCode.USR_BASIC_INF_IS_NULL);
        }
        //通过角色id查询角色名称
        String roleName = teamRoleTypeDao.getRoleName(urmSafeInfDO.getRoleId());

        //通过loginid获取登录设备信息
        List<UrmUserOprLogDO> urmUserOprLogDO = urmUserOprLogDao.getByLoginId(loginId);

        UserLogDO userLogDO = new UserLogDO();
        userLogDO.setUserId(urmSafeInfDO.getUserId());
        userLogDO.setRegisterTime(urmSafeInfDO.getCreateTime());
        userLogDO.setUserName(loginId);
        userLogDO.setUserRole(roleName);
        userLogDO.setUserStatus(urmSafeInfDO.getSafeSts());
        List<UserLogDO.LoginInfo> loginInfoList = new ArrayList<>();
        if (JudgeUtils.isNotNull(urmUserOprLogDO) && !urmUserOprLogDO.isEmpty()) {
            for (UrmUserOprLogDO logDO : urmUserOprLogDO) {
                UserLogDO.LoginInfo loginInfo = new UserLogDO.LoginInfo();
                loginInfo.setLoginTime(logDO.getCreateTime());
                loginInfo.setLoginDevice(logDO.getDevice());
                loginInfo.setLoginIp(logDO.getIp());
                loginInfo.setLocation(logDO.getLocation());
                loginInfoList.add(loginInfo);
            }
        }
        userLogDO.setLoginInfoList(loginInfoList);

        return userLogDO;
    }

    @Override
    public void modifyUserEmail(ModifyEmailDTO body) {

        //根据旧邮箱获取用户id
        String userId = userBasicInfDao.getUserIdByEmail(body.getOldEmail());
        if (JudgeUtils.isNull(userId)) {
            LemonException.throwBusinessException(URMMessageCode.USR_NOT_EXIST);
        }
        //根据新邮箱获取用户id
        String newUserId = userBasicInfDao.getUserIdByEmail(body.getNewEmail());
        if (!JudgeUtils.isNull(newUserId)) {
            LemonException.throwBusinessException(URMMessageCode.EMAIL_EXIST);
        }
        //支付密码加密
        String payPwd = body.getPayPwd();
        if (JudgeUtils.isNotBlank(payPwd)) {
            payPwd = DigestUtils.md5Hex(payPwd + userId);
        }
        //检验支付密码
        iUserAuthenticationService.checkPayPwd(userId, payPwd);
        //检验旧邮箱验证码
        String redisRandomKey = "email:random:3:" + body.getOldEmail();
        String redisEmailNo = redisTemplate.opsForValue().get(redisRandomKey);
        if (!body.getOldEmailCode().equals(redisEmailNo)) {
            LemonException.throwBusinessException(URMMessageCode.EMAIL_AUTH_FAIL);
        }
        //移除验证码
        redisTemplate.delete(redisRandomKey);
        //检验新邮箱验证码
        redisRandomKey = "email:random:3:" + body.getNewEmail();
        redisEmailNo = redisTemplate.opsForValue().get(redisRandomKey);
        if (!body.getNewEmailCode().equals(redisEmailNo)) {
            LemonException.throwBusinessException(URMMessageCode.EMAIL_AUTH_FAIL);
        }
        //移除验证码
        redisTemplate.delete(redisRandomKey);
        //修改邮箱safeInf safelogin
        safeInfDao.updateEmail(body.getOldEmail(), body.getNewEmail());
        safeLoginDao.updateEmail(body.getOldEmail(), body.getNewEmail());
        //修改用户登录信息
        urmUserOprLogDao.updateLoginId(body.getOldEmail(), body.getNewEmail());
    }

    @Override
    public void modifyUserLoginPwd(ModifyLoginPwdDTO body) {
        //检验支付密码
        String userId = LemonUtils.getUserId();
        //支付密码加密
//        String payPwd = body.getPayPwd();
//        if (JudgeUtils.isNotBlank(payPwd)) {
//            payPwd = DigestUtils.md5Hex(payPwd + userId);
//        }
//        //检验支付密码
//        iUserAuthenticationService.checkPayPwd(userId, payPwd);
//        //检验邮箱验证码
//        String redisRandomKey = "email:random:2:" + body.getEmail();
//        String redisEmailNo = redisTemplate.opsForValue().get(redisRandomKey);
//        if (!body.getEmailCode().equals(redisEmailNo)) {
//            LemonException.throwBusinessException(URMMessageCode.EMAIL_AUTH_FAIL);
//        }
////        移除验证码
//        redisTemplate.delete(redisRandomKey);
        //设置登录密码
        safeInfDao.updateLoginPwd(body.getEmail() , DigestUtils.md5Hex(body.getNewPwd() + userId));

        //设置交易密码
        safeInfDao.updatePayPwd(body.getEmail(), DigestUtils.md5Hex(body.getPayPwd() + userId));

    }

    @Override
    public void saveUserLog(String loginId, HttpServletRequest request) {
        String ip = IpUtils.getIpAddress(request);
        String deviceInfo = DeviceUtils.getDeviceInfo(request);
        String location = LocationUtils.getLocation(ip);

        logger.info("用户登录信息 IP:{}, 设备:{}, 位置:{}",ip,deviceInfo,location);

        UrmUserOprLogDO loginLogDO = new UrmUserOprLogDO();
        loginLogDO.setIp(ip);
        loginLogDO.setLocation(location);
        loginLogDO.setDevice(deviceInfo);
        loginLogDO.setOprNm("登录");
        loginLogDO.setLoginId(loginId);
        urmUserOprLogDao.insert(loginLogDO);
    }
}
