package com.hisun.lemon.urm.service;

import com.hisun.lemon.urm.dto.TeamOprAddDTO;
import com.hisun.lemon.urm.dto.TeamOprInfoDTO;
import com.hisun.lemon.urm.dto.TeamRoleConfigDTO;
import com.hisun.lemon.urm.entity.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/10/12
 */
public interface IMercOprService {

    void addMercOpr(UrmSafeLoginDO urmSafeLoginDO, UrmSafeInfDO urmSafeInfDO);

    void modifyOprInfo(String loginId, String mblNo, String displayNm);

    void deleteMercOpr(String loginId, String userId);

    List<MercOprInfoDO> queryAll(String userId, String displayNm, String loginId, String oprTyp, int num, int size);

   int queryListSize(String userId);

    String queryAuthority(String loginId);

    void modifyMercOprAuth(MercOprAuthDo mercOprAuthDo);

    /**
     * 权限管理获取用户列表
     */
    List<TeamOprInfoDTO> queryTeamOprList(String userId);

    /**
     * 权限管理修改用户信息
     */
    void modifyTeamOpr(TeamOprInfoDTO teamOprInfoDTO);

    /**
     * 权限管理删除用户
     */
    void deleteTeamOpr(TeamOprInfoDTO teamOprInfoDTO);

    /**
     * 权限管理搜索用户
     */
    TeamOprInfoDTO searchTeamOpr(String email);

    /**
     * 权限管理添加用户
     */
    void addTeamOpr(TeamOprAddDTO teamOprAddDTO);

    /**
     * 权限管理获取角色配置列表
     */
    List<TeamRoleConfigDTO> queryTeamRoleList(String userId);

    /**
     * 权限管理修改角色类型权限
     */
    void modifyTeamRole(TeamRoleConfigDTO teamRoleConfigDTO);

    /**
     * 权限管理删除角色类型
     */
    void deleteTeamRole(TeamRoleConfigDTO teamRoleConfigDTO);

    /**
     * 权限管理新增角色类型
     */
    String addTeamRole(TeamRoleConfigDTO teamRoleConfigDTO);

}
