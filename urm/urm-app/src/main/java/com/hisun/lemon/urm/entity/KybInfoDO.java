package com.hisun.lemon.urm.entity;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class KybInfoDO {

    @ApiModelProperty(name = "userId", value = "内部用户号", dataType = "String")
    private String userId;

    @ApiModelProperty(name = "examineStatus", value = "kyb审核状态", dataType = "String")
    private String examineStatus;

    @ApiModelProperty(name = "registRegion", value = "注册国家/地区", dataType = "String")
    private String registRegion;

    @ApiModelProperty(name = "corpFullNameCn", value = "企业全称中文", dataType = "String")
    private String corpFullNameCn;

    @ApiModelProperty(name = "corpFullNameEng", value = "企业全称英文", dataType = "String")
    private String corpFullNameEng;

    @ApiModelProperty(name = "contactName", value = "联系人姓名", dataType = "String")
    private String contactName;

    @ApiModelProperty(name = "contactEmail", value = "联系人邮箱", dataType = "String")
    private String contactEmail;

    @ApiModelProperty(name = "contactNumber", value = "联系人电话及区号", dataType = "String")
    private String contactNumber;

    @ApiModelProperty(name = "entityLegalForm", value = "企业法定性质", dataType = "String")
    private String entityLegalForm;

    @ApiModelProperty(name = "entityBusinessForm", value = "企业业务性质", dataType = "String")
    private String entityBusinessForm;

    @ApiModelProperty(name = "webUrl", value = "企业网站", dataType = "String")
    private String webUrl;

    @ApiModelProperty(name = "registTime", value = "注册日期", dataType = "java.util.Date")
    private Date registTime;

    @ApiModelProperty(name = "registAddr", value = "注册地址", dataType = "String")
    private String registAddr;

    @ApiModelProperty(name = "actualAddr", value = "实际地址", dataType = "String")
    private String actualAddr;

    @ApiModelProperty(name = "registCode", value = "注册号码", dataType = "String")
    private String registCode;

    @ApiModelProperty(name = "certificateOfIncorporationCi", value = "公司注册证明书CI文件路径", dataType = "String")
    private List<String> certificateOfIncorporationCi;

    @ApiModelProperty(name = "businessRegistrationBr", value = "商业登记证BR文件路径", dataType = "String")
    private List<String> businessRegistrationBr;

    @ApiModelProperty(name = "nnc1Nar1", value = "NNC1/NAR1文件路径", dataType = "String")
    private List<String> nnc1Nar1;

    @ApiModelProperty(name = "articleOfAssociation", value = "公司章程文件路径", dataType = "String")
    private List<String> articleOfAssociation;

    @ApiModelProperty(name = "structureOfMembers", value = "股权证明文件路径", dataType = "String")
    private List<String> structureOfMembers;

    @ApiModelProperty(name = "ceoIdentification", value = "董事/同等职位人员文件路径", dataType = "String")
    private List<String> ceoIdentification;

    @ApiModelProperty(name = "ultimateBeneficialOwners", value = "实际受益人", dataType = "String")
    private List<UltimateBeneficialOwnersDO> ultimateBeneficialOwners;

    @ApiModelProperty(name = "businessDesc", value = "企业主要业务描述", dataType = "String")
    private String businessDesc;

    @ApiModelProperty(name = "expectedMonthAmt", value = "预计月交易额", dataType = "java.math.BigDecimal")
    private BigDecimal expectedMonthAmt;

    @ApiModelProperty(name = "sourceOfFund", value = "主要资金来源", dataType = "String")
    private String sourceOfFund;

    @ApiModelProperty(name = "isSensitive", value = "是否存在敏感身份", dataType = "String")
    private String isSensitive;

    @ApiModelProperty(name = "additional", value = "附加说明/补充材料文件路径", dataType = "String")
    private List<String> additional;

    public KybInfoDO() {}

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(String examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getRegistRegion() {
        return registRegion;
    }

    public void setRegistRegion(String registRegion) {
        this.registRegion = registRegion;
    }

    public String getCorpFullNameCn() {
        return corpFullNameCn;
    }

    public void setCorpFullNameCn(String corpFullNameCn) {
        this.corpFullNameCn = corpFullNameCn;
    }

    public String getCorpFullNameEng() {
        return corpFullNameEng;
    }

    public void setCorpFullNameEng(String corpFullNameEng) {
        this.corpFullNameEng = corpFullNameEng;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getEntityLegalForm() {
        return entityLegalForm;
    }

    public void setEntityLegalForm(String entityLegalForm) {
        this.entityLegalForm = entityLegalForm;
    }

    public String getEntityBusinessForm() {
        return entityBusinessForm;
    }

    public void setEntityBusinessForm(String entityBusinessForm) {
        this.entityBusinessForm = entityBusinessForm;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public Date getRegistTime() {
        return registTime;
    }

    public void setRegistTime(Date registTime) {
        this.registTime = registTime;
    }

    public String getRegistAddr() {
        return registAddr;
    }

    public void setRegistAddr(String registAddr) {
        this.registAddr = registAddr;
    }

    public String getActualAddr() {
        return actualAddr;
    }

    public void setActualAddr(String actualAddr) {
        this.actualAddr = actualAddr;
    }

    public String getRegistCode() {
        return registCode;
    }

    public void setRegistCode(String registCode) {
        this.registCode = registCode;
    }

    public List<String> getCertificateOfIncorporationCi() {
        return certificateOfIncorporationCi;
    }

    public void setCertificateOfIncorporationCi(List<String> certificateOfIncorporationCi) {
        this.certificateOfIncorporationCi = certificateOfIncorporationCi;
    }

    public List<String> getBusinessRegistrationBr() {
        return businessRegistrationBr;
    }

    public void setBusinessRegistrationBr(List<String> businessRegistrationBr) {
        this.businessRegistrationBr = businessRegistrationBr;
    }

    public List<String> getNnc1Nar1() {
        return nnc1Nar1;
    }

    public void setNnc1Nar1(List<String> nnc1Nar1) {
        this.nnc1Nar1 = nnc1Nar1;
    }

    public List<String> getArticleOfAssociation() {
        return articleOfAssociation;
    }

    public void setArticleOfAssociation(List<String> articleOfAssociation) {
        this.articleOfAssociation = articleOfAssociation;
    }

    public List<String> getStructureOfMembers() {
        return structureOfMembers;
    }

    public void setStructureOfMembers(List<String> structureOfMembers) {
        this.structureOfMembers = structureOfMembers;
    }

    public List<String> getCeoIdentification() {
        return ceoIdentification;
    }

    public void setCeoIdentification(List<String> ceoIdentification) {
        this.ceoIdentification = ceoIdentification;
    }

    public List<UltimateBeneficialOwnersDO> getUltimateBeneficialOwners() {
        return ultimateBeneficialOwners;
    }

    public void setUltimateBeneficialOwners(List<UltimateBeneficialOwnersDO> ultimateBeneficialOwners) {
        this.ultimateBeneficialOwners = ultimateBeneficialOwners;
    }

    public String getBusinessDesc() {
        return businessDesc;
    }

    public void setBusinessDesc(String businessDesc) {
        this.businessDesc = businessDesc;
    }

    public BigDecimal getExpectedMonthAmt() {
        return expectedMonthAmt;
    }

    public void setExpectedMonthAmt(BigDecimal expectedMonthAmt) {
        this.expectedMonthAmt = expectedMonthAmt;
    }

    public String getSourceOfFund() {
        return sourceOfFund;
    }

    public void setSourceOfFund(String sourceOfFund) {
        this.sourceOfFund = sourceOfFund;
    }

    public String getIsSensitive() {
        return isSensitive;
    }

    public void setIsSensitive(String isSensitive) {
        this.isSensitive = isSensitive;
    }

    public List<String> getAdditional() {
        return additional;
    }

    public void setAdditional(List<String> additional) {
        this.additional = additional;
    }
}
