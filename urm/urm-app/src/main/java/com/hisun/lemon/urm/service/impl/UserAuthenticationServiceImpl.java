package com.hisun.lemon.urm.service.impl;

import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.NumberUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.common.AcmService;
import com.hisun.lemon.urm.common.CrmService;
import com.hisun.lemon.urm.common.RiskCheck;
import com.hisun.lemon.urm.common.SensitiveDataHandle;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IUrmSafeInfDao;
import com.hisun.lemon.urm.dao.IUrmSafeLoginDao;
import com.hisun.lemon.urm.dto.ReSetCheckPayPwdDTO;
import com.hisun.lemon.urm.entity.UrmSafeInfDO;
import com.hisun.lemon.urm.entity.UrmSafeLoginDO;
import com.hisun.lemon.urm.service.IUserAuthenticationService;
import com.hisun.lemon.urm.service.IUserBasicInfService;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils.verifyLoginPwd;

/**
 * <AUTHOR>
 * @function UserAuthenticationServiceImpl
 * @description 验证用户密码服务实现
 * @date 7/25/2017 Tue
 * @time 10:10 AM
 */
@Service
@Transactional(noRollbackFor = LemonException.class)
public class UserAuthenticationServiceImpl extends BaseService implements IUserAuthenticationService {
    private static final Logger logger = LoggerFactory.getLogger(UserAuthenticationServiceImpl.class);
    @Resource
    private IUrmSafeInfDao iUrmSafeInfDao;

    @Resource
    private IUrmSafeLoginDao iUrmSafeLoginDao;

    @Resource
    private IUserBasicInfService iUserBasicInfService;


    @Autowired
    @Qualifier("redisTemplateString")
    RedisTemplate<String, String> redisTemplate;

    @Resource
    private ConstantParamClient paramClient;

    @Resource
    private CmmServerClient cmmServerClient;

    @Resource
    private RiskCheck riskCheck;

    @Resource
    private SensitiveDataHandle sensData;

    @Resource
    private CrmService crm;

    @Resource
    private AcmService acm;

    @Resource
    private IUrmSafeInfDao safeInfDao;
    /**
     * 用户登录
     *
     * @param loginId
     * @param loginPwd
     */
    @Override
    public Map<String, Object> userLogin(String loginId, String loginPwd, String handPwd) {
        UrmSafeInfDO urmSafeInfDO;
        urmSafeInfDO = iUrmSafeInfDao.get(loginId);
        if (JudgeUtils.isNull(urmSafeInfDO)) {
            LemonException.throwBusinessException(URMMessageCode.USR_NOT_EXIST);
        }

        if (JudgeUtils.equals(urmSafeInfDO.getSafeSts(), URMConstants.SAFE_STS_EXP)) {
            LemonException.throwBusinessException(URMMessageCode.CANCEL_THE_ACCOUNT);
        }
//        if (JudgeUtils.equals(URMConstants.USR_TYP, urmSafeInfDO.getOprTyp())) {
//        }
        logger.debug("loginId: " + loginId + "loginPwd :" + loginPwd + ", handPwd :" + handPwd);

        //密码md5加密加盐userid
        if (JudgeUtils.isNotBlank(loginPwd)) {
            loginPwd = DigestUtils.md5Hex(loginPwd + urmSafeInfDO.getUserId());
        }

        //检查设备是否切换
//        GenericDTO genericDTO = new GenericDTO();
//        MessageClientSwitchReqDTO messageClientSwitchReqDTO = new MessageClientSwitchReqDTO();
//        messageClientSwitchReqDTO.setUserId(urmSafeInfDO.getUserId());
//        genericDTO.setBody(messageClientSwitchReqDTO);
        //GenericRspDTO<Boolean> genericRspDTO = cmmServerClient.messageClientSwitch(genericDTO);
        //Boolean rs1 = genericRspDTO.getBody();


//        if(JudgeUtils.isNotBlank(handPwd)){
//            if(!rs1){
//                LemonException.throwLemonException(URMMessageCode.HAND_PWD_NOT_SET);
//            }
//            if(urmSafeInfDO.getHandFlg() != null && urmSafeInfDO.getHandFlg().equals(URMConstants.HAND_FLG_C)){
//                LemonException.throwLemonException(URMMessageCode.HAND_PWD_NOT_OPEN);
//            }
//            if(urmSafeInfDO.getHandPwd() == null || "".equals(urmSafeInfDO.getHandPwd())){
//                LemonException.throwLemonException(URMMessageCode.HAND_PWD_NOT_SET);
//            }
//            //String handPwdDb = sensData.decryptSensitiveData(urmSafeInfDO.getHandPwd());
//            if (!checkHandPwd(loginId, handPwd)) {
//                LemonException.throwLemonException(URMMessageCode.HAND_PWD_ERROR);
//            }
//            /*if(JudgeUtils.notEquals(handPwd, handPwdDb)){
//                LemonException.throwLemonException(URMMessageCode.HAND_PWD_ERROR);
//            }*/
//        }else if (JudgeUtils.isNotBlank(urmSafeInfDO.getLoginPwd())) {
//            if (loginPwd.length() >= URMConstants.NEW_USER_PWD) {
//                if (!checkLoginPwd(loginId, loginPwd)) {
//                    LemonException.throwLemonException(URMMessageCode.LOG_PWD_ERROR);
//                }
//            } else {
//                LemonException.throwLemonException(URMMessageCode.LOG_PWD_ERROR);
//            }
//        }
        if (JudgeUtils.isNotBlank(urmSafeInfDO.getLoginPwd())){
            if (!checkLoginPwd(loginId, loginPwd)) {
                LemonException.throwLemonException(URMMessageCode.LOG_PWD_ERROR);
            }
        } else {
            if (loginPwd.length() == 6) {
                crm.verifyCrmLoginPwd(loginId, loginPwd);
            } else {
                LemonException.throwLemonException(URMMessageCode.LOG_PWD_ERROR);
            }
        }
        String userId = urmSafeInfDO.getUserId();

        //用户状态检查
        riskCheck.checkUser(userId);

        UrmSafeInfDO urmSafeInfDO1 = new UrmSafeInfDO();
        urmSafeInfDO1.setLastLoginDt(DateTimeUtils.getCurrentLocalDate());
        urmSafeInfDO1.setLastLoginTm(DateTimeUtils.getCurrentLocalTime());
        urmSafeInfDO1.setSafeId(urmSafeInfDO.getSafeId());
        iUrmSafeInfDao.update(urmSafeInfDO1);
        UrmSafeLoginDO safeLoginDO = iUrmSafeLoginDao.get(loginId);
        Map<String, Object> userInf = iUserBasicInfService.queryUser(userId);
        String oprTyp = urmSafeInfDO.getOprTyp();
        if (JudgeUtils.isNotBlank(LemonUtils.getChannel())) {
            String logCnl = LemonUtils.getChannel().substring(0, 3);
            if (JudgeUtils.isNotBlank(oprTyp)) {
                if (JudgeUtils.equals(oprTyp, URMConstants.USR_TYP)) {
                    if (JudgeUtils.equals(logCnl.toUpperCase(), "MER")) {
                        LemonException.throwLemonException(URMMessageCode.NOT_MER_USER);
                    }
                } else {
                    if (JudgeUtils.equals(logCnl.toUpperCase(), "USR")) {
                        LemonException.throwLemonException(URMMessageCode.NOT_ORDINARY_USER);
                    }
                }
            }
        }
//        if(!rs1){
//            iUrmSafeInfDao.updateHandInfo(userId);
//            urmSafeInfDO.setHandFlg("0");
//        }
//        String safeQues1 = urmSafeInfDO.getSafeQues1();
//        if (JudgeUtils.isNotBlank(safeQues1)) {
//            try {
//                safeQues1 = sensData.decryptSensitiveData(safeQues1);
//            } catch (Exception e) {
//                LemonException.throwLemonException(e);
//            }
//        }
//        urmSafeInfDO.setSafeQues1(safeQues1);
        userInf.put("loginInf", safeLoginDO);
        userInf.put("safeInf", "测试登录");
        return userInf;
    }

    /**
     * 用户支付密码校验
     *
     * @param userId
     * @param payPwd
     */
    @Override
    public Boolean checkPayPwd(String userId, String payPwd) {
        //查询用户的支付密码
        List<UrmSafeInfDO> safeInfDOS = iUrmSafeInfDao.findByUserId(userId);
        if (JudgeUtils.isNotEmpty(safeInfDOS)) {
            String pwd = safeInfDOS.get(0).getPayPwd();
            if (JudgeUtils.isNotBlank(pwd)) {
                logger.info("******* Start to check pay password *******");
                int payFailCnt = safeInfDOS.get(0).getPayFailCnt().intValue();
                if (JudgeUtils.isNull(payFailCnt)) {
                    payFailCnt = 0;
                }
                LocalDate payLckDt = safeInfDOS.get(0).getPayLckDt();
                LocalDateTime payLckTm = safeInfDOS.get(0).getPayLckTm();
                int maxPayPwdCnt = 0;
                try {
                    GenericRspDTO<ConstantParamRspDTO> paramRspDTO = paramClient.params("PAY_PWD_LOCK_CNT");
                    maxPayPwdCnt = Integer.valueOf(paramRspDTO.getBody().getParmVal());
                } catch (Exception e) {
                    //do nothing
                }
                if (JudgeUtils.isNotNull(payLckTm)) {
                    LocalDateTime now = DateTimeUtils.getCurrentLocalDateTime();
                    if (ChronoUnit.MINUTES.between(payLckTm, now) <= 60 ) {
                        //关闭手势密码
                        //safeInfDao.updateHandInfo(userId);
                        LemonException.throwLemonException(URMMessageCode.PAY_PWD_ALREADY_LOCK);
                    } else {
                        if (payFailCnt >= maxPayPwdCnt) {
                            payFailCnt = 0;
                        }
                    }
                }
                if (!JudgeUtils.equals(payPwd, pwd)){
                    payFailCnt++;
                    for (UrmSafeInfDO safeInfDO : safeInfDOS) {
                        logger.info("CHK_FAIL_CNT: " + safeInfDO.getPayFailCnt().toString());
                        byte payFailCntByte = NumberUtils.toByte(String.valueOf(payFailCnt));
                        safeInfDO.setPayFailCnt(Byte.valueOf(payFailCntByte));
                        logger.info("CHK_FAIL_CNT ++: " + Byte.valueOf(payFailCntByte).toString());
                        if (payFailCnt >= maxPayPwdCnt) {
                            safeInfDO.setPayLckDt(DateTimeUtils.getCurrentLocalDate());
                            safeInfDO.setPayLckTm(DateTimeUtils.getCurrentLocalDateTime());
                        }
                        iUrmSafeInfDao.update(safeInfDO);
                    }
                    /*if (payFailCnt >= maxPayPwdCnt) {
                        //关闭手势密码
                        safeInfDao.updateHandInfo(userId);
                        LemonException.throwLemonException(URMMessageCode.PAY_PWD_ALREADY_LOCK);
                    }*/
                    return false;
                }
                /*if (!verifyPayPwd(payPwd, acNo, pwd)){
                    payFailCnt++;
                    for (UrmSafeInfDO safeInfDO : safeInfDOS) {
                        logger.info("CHK_FAIL_CNT: " + safeInfDO.getPayFailCnt().toString());
                        byte payFailCntByte = NumberUtils.toByte(String.valueOf(payFailCnt));
                        safeInfDO.setPayFailCnt(Byte.valueOf(payFailCntByte));
                        logger.info("CHK_FAIL_CNT ++: " + Byte.valueOf(payFailCntByte).toString());
                        if (payFailCnt >= maxPayPwdCnt) {
                            safeInfDO.setPayLckDt(DateTimeUtils.getCurrentLocalDate());
                            safeInfDO.setPayLckTm(DateTimeUtils.getCurrentLocalTime());
                        }
                        iUrmSafeInfDao.update(safeInfDO);
                    }
                    *//*if (payFailCnt >= maxPayPwdCnt) {
                        //关闭手势密码
                        safeInfDao.updateHandInfo(userId);
                        LemonException.throwLemonException(URMMessageCode.PAY_PWD_ALREADY_LOCK);
                    }*//*
                    return false;
                }*/

                if (payFailCnt != 0) {
                    UrmSafeInfDO safeInfDO1 = new UrmSafeInfDO();
                    safeInfDO1.setUserId(userId);
                    safeInfDO1.setPayFailCnt(NumberUtils.toByte("0"));
                    iUrmSafeInfDao.initPwdInf(safeInfDO1);
                }
                logger.info("******* End *******");
            } else {
                LemonException.throwBusinessException(URMMessageCode.NO_EFFECTIVE_PWD);
            }
        } else {
            LemonException.throwBusinessException(URMMessageCode.NO_EFFECTIVE_PWD);
        }
        return true;
    }

    /**
     * 用户登录密码校验
     *
     * @param loginId
     * @param loginPwd
     * @return
     */
    @Override
    public Boolean checkLoginPwd(String loginId, String loginPwd) {
        UrmSafeInfDO safeInfDO = iUrmSafeInfDao.get(loginId);
        if (JudgeUtils.isNotNull(safeInfDO)) {
            String pwd = safeInfDO.getLoginPwd();
            String userId = safeInfDO.getUserId();
            if (JudgeUtils.isNotBlank(pwd)) {
                logger.info("******* Start to check login password *******");
                int loginFailCnt = safeInfDO.getLoginFailCnt().intValue();
                if (JudgeUtils.isNull(loginFailCnt)) {
                    loginFailCnt = 0;
                }
                LocalDateTime loginLckTm = safeInfDO.getLoginLckTm();
                int maxLogPwdCnt = 0;
                try {
                    GenericRspDTO<ConstantParamRspDTO> paramRspDTO = paramClient.params("LOG_PWD_LOCK_CNT");
                    maxLogPwdCnt = Integer.valueOf(paramRspDTO.getBody().getParmVal());
                } catch (Exception e) {
                    //do nothing
                }
                if (JudgeUtils.isNotNull(loginLckTm)) {
                    LocalDateTime now = DateTimeUtils.getCurrentLocalDateTime();
                    if (ChronoUnit.MINUTES.between(loginLckTm, now) <= 60) {
                        //关闭手势密码
                        safeInfDao.updateHandInfo(userId);
                        LemonException.throwLemonException(URMMessageCode.LOG_PWD_ALREADY_LOCK);
                    } else {
                        if (loginFailCnt >= maxLogPwdCnt) {
                            loginFailCnt = 0;
                        }
                    }
                }
                if (!verifyLoginPwd(loginPwd, userId, pwd)) {
                    loginFailCnt++;
                    logger.info("CHK_FAIL_CNT: " + safeInfDO.getLoginFailCnt().toString());
                    byte loginFailCntByte = NumberUtils.toByte(String.valueOf(loginFailCnt));
                    safeInfDO.setLoginFailCnt(Byte.valueOf(loginFailCntByte));
                    logger.info("CHK_FAIL_CNT ++: " + Byte.valueOf(loginFailCntByte).toString());
                    if (loginFailCnt >= maxLogPwdCnt) {
                        safeInfDO.setLoginLckTm(LocalDateTime.now());
                    }
                    iUrmSafeInfDao.update(safeInfDO);
                    if (loginFailCnt >= maxLogPwdCnt) {
                        //关闭手势密码
                        safeInfDao.updateHandInfo(userId);
                        LemonException.throwLemonException(URMMessageCode.LOG_PWD_ALREADY_LOCK);
                    }
                    return false;
                }

                if (loginFailCnt != 0) {
                    UrmSafeInfDO safeInfDO1 = new UrmSafeInfDO();
                    safeInfDO1.setSafeId(safeInfDO.getSafeId());
                    safeInfDO1.setLoginFailCnt(NumberUtils.toByte("0"));
                    iUrmSafeInfDao.update(safeInfDO1);
                }
                logger.info("******* End *******");
            } else {
                LemonException.throwBusinessException(URMMessageCode.NO_EFFECTIVE_PWD);
            }
        } else {
            LemonException.throwBusinessException(URMMessageCode.NO_EFFECTIVE_PWD);
        }
        return true;
    }

    /**
     * 用户密保问题答案校验
     *
     * @param loginId
     * @param safeQues
     * @param safeAns
     * @return
     */
    @Override
    public Boolean checkSafeQues(String loginId, String safeQues, String safeAns) {
        UrmSafeInfDO safeInfDO = iUrmSafeInfDao.get(loginId);
        try {
            if (JudgeUtils.isNotNull(safeInfDO)) {
                String safeQues1 = safeInfDO.getSafeQues1();
                if (JudgeUtils.isNotBlank(safeQues1)) {
                    safeQues1 = sensData.decryptSensitiveData(safeQues1);
                }
                String safeAns1 = safeInfDO.getSafeAns1();
                if (JudgeUtils.isNotBlank(safeAns1)) {
                    safeAns1 = sensData.decryptSensitiveData(safeAns1);
                }
                if (JudgeUtils.notEquals(safeQues, safeQues1) || JudgeUtils.notEquals(safeAns, safeAns1)) {
                    LemonException.throwLemonException(URMMessageCode.SAFE_QUES_CHECK_FAIL);
                }
            } else {
                LemonException.throwLemonException(URMMessageCode.USR_SAFE_INF_IS_NULL);
            }
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }
        return true;
    }

    @Override
    public Boolean checkSafeQues(String loginId, List<String> safeQuesNum, String safeAns) {
        UrmSafeInfDO safeInfDO = iUrmSafeInfDao.get(loginId);
        if (JudgeUtils.isNotNull(safeInfDO)) {
            String safeQues1 = safeInfDO.getSafeQues1();
            String safeAns1 = safeInfDO.getSafeAns1();
            if (JudgeUtils.isNotBlank(safeQues1)) {
                safeQues1 = sensData.decryptSensitiveData(safeQues1);
            }
            if (JudgeUtils.isNotBlank(safeAns1)) {
                safeAns1 = sensData.decryptSensitiveData(safeAns1);
            }

            for (String str : safeQuesNum) {
                logger.info("Q: " + str + " |Qi: " + safeQues1);
                logger.info("A: " + safeAns + " |Ai: " + safeAns1);
                if (JudgeUtils.equals(str, safeQues1) && JudgeUtils.equals(safeAns, safeAns1)) {
                    return true;
                }
            }
        } else {
            LemonException.throwLemonException(URMMessageCode.USR_SAFE_INF_IS_NULL);
        }
        return false;
    }

    /**
     * 用户手势密码校验
     * @param loginId
     * @param handPwd
     * @return
     */
    public Boolean checkHandPwd(String loginId, String handPwd){
        UrmSafeInfDO safeInfDO = iUrmSafeInfDao.get(loginId);
        if (JudgeUtils.isNotNull(safeInfDO)) {
            String pwd = safeInfDO.getHandPwd();
            String userId = safeInfDO.getUserId();
            if (JudgeUtils.isNotBlank(pwd)) {
                logger.info("******* Start to check hand password *******");
                int handFailCnt = safeInfDO.getHandFailCnt().intValue();
                if (JudgeUtils.isNull(handFailCnt)) {
                    handFailCnt = 0;
                }
                int maxHandPwdCnt = 0;
                try {
                    GenericRspDTO<ConstantParamRspDTO> paramRspDTO = paramClient.params("HAND_PWD_LOCK_CNT");
                    maxHandPwdCnt = Integer.valueOf(paramRspDTO.getBody().getParmVal());
                } catch (Exception e) {
                    //do nothing
                }
                if (handFailCnt >= maxHandPwdCnt) {
                    //关闭手势密码
                    safeInfDao.updateHandInfo(userId);
                    LemonException.throwLemonException(URMMessageCode.HAND_PWD_ALREADY_LOCK);
                }
                pwd = sensData.decryptSensitiveData(pwd);
               // handPwd = sensData.encryptSensitiveData(handPwd);
                if (!JudgeUtils.equals(handPwd, pwd)) {
                    handFailCnt ++;
                    logger.info("CHK_FAIL_CNT: " + safeInfDO.getHandFailCnt().toString());
                    byte handFailCntByte = NumberUtils.toByte(String.valueOf(handFailCnt));
                    safeInfDO.setHandFailCnt(Byte.valueOf(handFailCntByte));
                    logger.info("CHK_FAIL_CNT ++: " + Byte.valueOf(handFailCntByte).toString());
                    iUrmSafeInfDao.update(safeInfDO);
                    if (handFailCnt >= maxHandPwdCnt) {
                        //关闭手势密码
                        safeInfDao.updateHandInfo(userId);
                        LemonException.throwLemonException(URMMessageCode.HAND_PWD_ALREADY_LOCK);
                    }
                    return false;
                }
                if (handFailCnt != 0) {
                    UrmSafeInfDO safeInfDO1 = new UrmSafeInfDO();
                    safeInfDO1.setHandFailCnt(NumberUtils.toByte("0"));
                    safeInfDO1.setSafeId(safeInfDO.getSafeId());
                    iUrmSafeInfDao.update(safeInfDO1);
                }
                logger.info("******* End *******");
            } else {
                LemonException.throwBusinessException(URMMessageCode.NO_EFFECTIVE_PWD);
            }
        } else {
            LemonException.throwBusinessException(URMMessageCode.NO_EFFECTIVE_PWD);
        }
        return true;
    }

    /**
     * 重置密码校验支付密码
     *
     * @param
     * @return
     */
    @Override
    public String resetPwdCheckPayPwd(GenericDTO<ReSetCheckPayPwdDTO> forgetPwdDTO) {
        //校验支付密码
        UrmSafeInfDO safeInfDO = safeInfDao.get(forgetPwdDTO.getBody().getLoginId());
        if (safeInfDO == null) {
            LemonException.throwBusinessException(URMMessageCode.USR_NOT_EXIST);
        }
        String userId = safeInfDO.getUserId();
        String payPwd = forgetPwdDTO.getBody().getPayPwd();
        //支付密码加密
        if (JudgeUtils.isNotBlank(payPwd)) {
            payPwd = DigestUtils.md5Hex(payPwd + userId);
        }
        checkPayPwd(userId, payPwd);

        String payPwdDb = safeInfDO.getPayPwd();

        if (JudgeUtils.isNotBlank(payPwdDb)) {
            //String tmpPwd = sensData.decryptSensitiveData(payPwdDb);
            if (!payPwd.equals(payPwdDb)) {
                LemonException.throwBusinessException(URMMessageCode.PAY_PWD_CHECK_FAIL);
            }
        }
        // 生产4位数验证码
        String verificationCode = String.format("%04d", new Random().nextInt(9999));
        String redisKey = "urm:pwdReset:" + forgetPwdDTO.getBody().getLoginId();
        redisTemplate.opsForValue().set(redisKey, verificationCode, 5, TimeUnit.MINUTES);

        return verificationCode;
    }


}
