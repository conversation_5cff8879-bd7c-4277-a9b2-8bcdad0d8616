/*
 * @ClassName IUrmSafeInfDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.MercOprInfoDO;
import com.hisun.lemon.urm.entity.UrmSafeInfDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IUrmSafeInfDao extends BaseDao<UrmSafeInfDO> {
    /**
     * 根据用户号查询用户安全信息
     */
    public List<UrmSafeInfDO> findByUserId(String userId);

    /**
     * 根据安全ID查询用户安全信息
     */
    public UrmSafeInfDO getBySafeId(String safeId);

    /**
     * 根据用户号更新密码信息
     */
    public int initPwdInf(UrmSafeInfDO safeInfDO);

    /**
     * 查询商户操作员列表
     *
     * @param userId
     * @return
     */
    List<MercOprInfoDO> queryMercOprInfoList(@Param("userId") String userId,
                                             @Param("displayNm") String displayNm,
                                             @Param("loginId") String loginId,
                                             @Param("oprTyp") String oprTyp);

    /**
     * 权限管理获取用户列表
     *
     * @param userId
     * @return
     */
    List<UrmSafeInfDO> queryTeamOprInfoList(@Param("userId") String userId);

    /**
     * 权限管理搜索用户
     *
     * @param email
     * @return
     */
    UrmSafeInfDO searchTeamOpr(@Param("email") String email);

    /**
     * 权限管理统计用户类型数量
     *
     * @param roleId
     * @return
     */
    int countRoleNum(@Param("roleId") String roleId);

    /**
     * 获取用户最初创建的
     *
     * @param userId
     * @return
     */
    UrmSafeInfDO queryMainAccSafeInfo(@Param("userId") String userId);

    /**
     * 查询商户操作员总数
     *
     * @param userId
     * @return
     */
    int queryTotNum(String userId);

    /**
     * 开启关闭手势密码
     * @param flg
     * @param flgOld
     * @return
     */
    int updateFlg(@Param("userId") String userId, @Param("flg") String flg, @Param("flgOld") String flgOld);

    int updateHandInfo(@Param("userId") String userId);

    UrmSafeInfDO getByEmail(@Param("email") String loginId);

    void updateEmail(@Param("userId") String userId, @Param("newEmail") String newEmail);

    void updateLoginPwd(@Param("email") String email, @Param("newPwd") String newPwd);

    void updatePayPwd(@Param("email") String email, @Param("newPayPwd") String newPayPwd);
}