package com.hisun.lemon.urm.entity;

import io.swagger.annotations.ApiModelProperty;

/**
 * TODO 此处填写功能说明
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/6 15:59
 */
public class ModifyLoginPwdDTO   {
    @ApiModelProperty(name = "email", value = "邮箱", dataType = "String")
    private String email;
    //支付密码
    @ApiModelProperty(name = "payPwd", value = "支付密码", dataType = "String")
    private String payPwd;
    //邮箱验证码
    @ApiModelProperty(name = "emailCode", value = "邮箱验证码", dataType = "String")
    private String emailCode;

    //新登录密码
    @ApiModelProperty(name = "newPwd", value = "新登录密码", dataType = "String")
    private String newPwd;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public String getEmailCode() {
        return emailCode;
    }

    public void setEmailCode(String emailCode) {
        this.emailCode = emailCode;
    }

    public String getNewPwd() {
        return newPwd;
    }

    public void setNewPwd(String newPwd) {
        this.newPwd = newPwd;
    }
}
