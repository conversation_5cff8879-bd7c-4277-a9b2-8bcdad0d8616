package com.hisun.lemon.urm.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.UrmKybSeqDao;
import com.hisun.lemon.urm.entity.KybInfoDO;
import com.hisun.lemon.urm.entity.UrmKybSeqDO;
import com.hisun.lemon.urm.service.KybCertificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("kybCertificationServiceImpl")
public class KybCertificationServiceImpl extends BaseService implements KybCertificationService {

    private final static Logger logger = LoggerFactory.getLogger(KybCertificationServiceImpl.class);

    @Resource
    private UrmKybSeqDao urmKybSeqDao;

    @Override
    public void submitKybInfo(KybInfoDO kybInfoDO) {
        // 将对象转换成json
        String kybJson;
        try {
            kybJson = new ObjectMapper().writeValueAsString(kybInfoDO);
            logger.info(kybJson);
        } catch (Exception e) {
            e.printStackTrace();
            throw new LemonException(URMMessageCode.KYB_SUBMIT_ERROR);
        }

        if (!JudgeUtils.equalsAny(kybInfoDO.getExamineStatus(), URMConstants.EXAMINE_TEMP, URMConstants.EXAMINE_STATUS_NO_PASS)) {
            throw new LemonException(URMMessageCode.KYB_SUBMIT_ERROR);
        }

        // 查询现有记录
        UrmKybSeqDO record = urmKybSeqDao.getKybList(kybInfoDO.getUserId());
        if (record != null) {
            // 检查状态是否已通过或拒绝
            if (JudgeUtils.equalsAny(record.getExamineStatus(),
                    URMConstants.EXAMINE_FIRST_AUDIT,
                    URMConstants.EXAMINE_STATUS_NO_PASS,
                    URMConstants.EXAMINE_SECOND_AUDIT,
                    URMConstants.EXAMINE_APPROVED)) {
                throw new LemonException(URMMessageCode.KYB_CERT_EXIST);
            }

            // 更新暂存
            if (JudgeUtils.equalsAny(record.getExamineStatus(),
                    URMConstants.EXAMINE_REJECTED,
                    URMConstants.EXAMINE_TEMP)) {
                setRecord(record, kybInfoDO, kybJson);
                urmKybSeqDao.update(record);
                return;
            }
        }

        // 插入新记录
        UrmKybSeqDO newRecord = new UrmKybSeqDO();
        setRecord(newRecord, kybInfoDO, kybJson);
        urmKybSeqDao.insert(newRecord);
    }

    @Override
    public KybInfoDO queryKybInfo(String userId) {
        UrmKybSeqDO urmKybSeqDO = urmKybSeqDao.getKybList(userId);
        String kybJson;
        if(urmKybSeqDO == null){
            return null;
        }
        try {
            kybJson = urmKybSeqDO.getKybInfo();
            KybInfoDO result = new ObjectMapper().readValue(kybJson, KybInfoDO.class);
            result.setExamineStatus(urmKybSeqDO.getExamineStatus());
            logger.info(result.toString());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void setRecord(UrmKybSeqDO record, KybInfoDO kybInfoDO, String kybJson) {
        record.setKybInfo(kybJson);
        record.setExamineStatus(kybInfoDO.getExamineStatus());
        record.setUserId(kybInfoDO.getUserId());
    }
}
