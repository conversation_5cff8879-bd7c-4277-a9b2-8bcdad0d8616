package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import io.swagger.annotations.ApiModelProperty;

public class UrmUserOprLogDO extends BaseDO {

    @ApiModelProperty(name = "id", value = "主键ID")
    private Integer id;

    @ApiModelProperty(name = "ip", value = "IP地址")
    private String ip;

    @ApiModelProperty(name = "location", value = "位置信息")
    private String location;

    @ApiModelProperty(name = "device", value = "设备信息")
    private String device;

    @ApiModelProperty(name = "oprNm", value = "操作名称")
    private String oprNm;

    @ApiModelProperty(name = "loginId", value = "登录ID")
    private String loginId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getOprNm() {
        return oprNm;
    }

    public void setOprNm(String oprNm) {
        this.oprNm = oprNm;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }
}
