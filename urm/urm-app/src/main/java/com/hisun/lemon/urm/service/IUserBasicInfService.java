package com.hisun.lemon.urm.service;

import com.hisun.lemon.urm.dto.MercListInfDTO;
import com.hisun.lemon.urm.entity.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @funciton UserManagementDao
 * @description 用户管理传输对象
 * <AUTHOR>
 * @date 7/8/2017 FRI
 * @time 4:44 PM
 */
public interface IUserBasicInfService {
    /**
     * 用户开户
     * @param userBasicInfDO
     * @param safeInfDO
     * @param cprExtInfDO
     */
    public String openUser(UrmUserBasicInfDO userBasicInfDO, UrmSafeInfDO safeInfDO, UrmCprExtInfDO cprExtInfDO,
                         UrmSafeLoginDO safeLoginDO, String random);

    /**
     * 用户销户
     * @param userId
     */
    public void cancelUser(String userId);

    /**
     * 通过UserID用户信息查询
     * @param userId
     * @return
     */
    public Map<String, Object> queryUser(String userId);

    /**
     * 通过loginId查询用户信息
     * @param loginId
     * @return
     */
    public Map<String, Object> queryUserByLoginId(String loginId);

    /**
     * 完善用户实名信息
     * @param basicInfDO
     */
    public void completeRealName(UrmUserBasicInfDO basicInfDO);

    /**
     * 查询商户交易权限
     * @param userId
     * @param itfNm
     * @param version
     * @return
     */
    public UrmCprItfAuthDO queryTradingPrivilege(String userId, String itfNm, String version);

    /**
     * 给app调用的补全证件号和姓名，并不改实名标志的接口
     * @param basicInfDO
     */
    void upgradeInformation(UrmUserBasicInfDO basicInfDO);

    /**
     * 根据登录名查询安全问题对应序号
     * @param loginId
     * @return
     */
    String querySafeQuesNo(String loginId);

    //查询用户是否设置手势密码
    String queryHandSet();

    //批量开户
    List<BatchOpenUserDo> batchOpenUser(String loginPwd, String payPwd, String ques, String ans);

    //校验证件号重复
    void checkIdNo(String idNo);

    /**
     * 清空手势密码
     * @param userId
     */
    void cleanHandPw(String userId);

    List<String> getAffiliateMerchantList(String userId);

    MercListInfDTO getMercInfoList(List<String> userList, String mercId, String mercNm, int pageNum, int pageSize);

    UserLogDO getUserLog(String loginId);

    void modifyUserEmail(ModifyEmailDTO body);

    void modifyUserLoginPwd(ModifyLoginPwdDTO body);

    void saveUserLog(String loginId, HttpServletRequest request);
}
