package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.UrmUserOprLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UrmUserOprLogDao extends BaseDao<UrmUserOprLogDO> {
    List<UrmUserOprLogDO> getByLoginId(@Param("loginId") String loginId);

    void updateLoginId(@Param("userId") String userId, @Param("newLoginId") String newLoginId);

}
