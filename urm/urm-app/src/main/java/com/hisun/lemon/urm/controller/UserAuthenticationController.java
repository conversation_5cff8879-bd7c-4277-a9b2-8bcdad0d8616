package com.hisun.lemon.urm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.RandomTemplete;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IUrmSafeInfDao;
import com.hisun.lemon.urm.dao.TeamRoleTypeDao;
import com.hisun.lemon.urm.dao.UrmUserOprLogDao;
import com.hisun.lemon.urm.dto.*;
import com.hisun.lemon.urm.entity.*;
import com.hisun.lemon.urm.service.IUserAuthenticationService;
import com.hisun.lemon.urm.service.IUserPasswordService;
import com.hisun.lemon.urm.utils.DeviceUtils;
import com.hisun.lemon.urm.utils.IpUtils;
import com.hisun.lemon.urm.utils.LocationUtils;
import com.hisun.lemon.urm.utils.RSAUtil2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.apache.commons.codec.digest.DigestUtils;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils.convertRSAEncryptedPin;
import static com.hisun.lemon.urm.utils.CipherProcessorUtils.convertRSAEncryptedPinNew;

/**
 * <AUTHOR>
 * @function UserAuthenticatiaoController
 * @description 用户验证控制器
 * @date 7/25/2017 Tue
 * @time 10:13 AM
 */
@Api(tags = "UserAuthenticationController", description = "用户验证服务")
@RestController
@RequestMapping("/urm/authentication")
public class UserAuthenticationController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(UserAuthenticationController.class);
    @Resource
    private IUserAuthenticationService iUserAuthenticationService;
    @Resource
    private IUserPasswordService userPasswordService;

    @Resource(name = "bindingTokenRandomTemplete")
    private RandomTemplete randomTemplate;

    @Resource
    private IUrmSafeInfDao urmSafeInfDao;

    @Resource
    private TeamRoleTypeDao teamRoleTypeDao;

    @Resource
    private UrmUserOprLogDao urmUserOprLogDao;

    @ApiOperation(value = "用户登录", notes = "userLogin")
    @ApiResponse(code = 200, message = "用户登录")
    @PostMapping(value = "/login")
    public GenericRspDTO<UserBasicInfDTO> userLogin(@Validated @RequestBody GenericDTO<UserLoginDTO> userLoginDTO, HttpServletRequest request) {
        String loginId = userLoginDTO.getBody().getLoginId();
        String loginPwd = userLoginDTO.getBody().getLoginPwd();
        String loginPwdRandom = userLoginDTO.getBody().getLoginPwdRandom();
        String loginType = userLoginDTO.getBody().getLoginType();
        String handPwd = userLoginDTO.getBody().getHandPwd();
        String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
//        int pwdLength = 0;
//        if(!URMConstants.LOGIN_TYPE_H.equals(loginType)){
//            pwdLength = Hex.toHexString(Base64.decode(loginPwd)).length();
//        }
//        if(JudgeUtils.isNotBlank(loginType) && JudgeUtils.equals(URMConstants.LOGIN_TYPE_H, loginType)){
//            try {
//                if(JudgeUtils.isNull(handPwd) || JudgeUtils.isEmpty(handPwd)){
//                    LemonException.throwLemonException(URMMessageCode.HAND_PWD_ISNULL);
//                }
//                handPwd = RSAUtil2.decryptByKey(handPwd.getBytes("UTF-8"));
//                if(handPwd == null || "".equals(handPwd)){
//                    throw new Exception("Decrypt error !");
//                }
//                if(randomServer == null || "".equals(randomServer)){
//                    logger.info("randomServer error : " + randomServer);
//                    throw new Exception("randomServer null !");
//                }
//                logger.info("handPwd : " + handPwd );
//                //handPwd = handPwd.substring(0, 5 + randomServer.length());
//                //String tmpRandom = handPwd.substring(handPwd.length() - randomServer.length(), handPwd.length());
//                String []tmp = handPwd.split(",");
//                handPwd = tmp[0];
//                tmp[1] = tmp[1].substring(0, randomServer.length());
//                logger.info("handPwd : " + handPwd + ", radom : " + tmp[1] + ", randomServer : " + randomServer);
//                if(!tmp[1].equals(randomServer)){
//                    throw new Exception("randomServer error !");
//                }
//                logger.info("RSA解密成功！");
//            }catch (Exception e){
//                e.printStackTrace();
//                logger.error("RSA解密失败：" + e.getMessage());
//                LemonException.throwLemonException(URMMessageCode.RSA2_DECRYPT_FAILURE);
//            }
//        } else if (JudgeUtils.equals(pwdLength, URMConstants.OLD_USER_PWD)) {
//            String randomClient = decryptRSAEncryptedRandom(loginPwdRandom);
//            loginPwd = decrypt3DESEncryptedPin(randomClient, randomServer, loginPwd);
//        } else if (JudgeUtils.equals(pwdLength, URMConstants.NEW_USER_PWD)){
//            loginPwd = convertRSAEncryptedPin(loginPwdRandom, randomServer, loginPwd, URMConstants.LOG_PWD_MODE);
//        } else {
//            LemonException.throwLemonException(URMMessageCode.LOG_PWD_ERROR);
//        }
        Map<String, Object> userInf = this.iUserAuthenticationService.userLogin(loginId, loginPwd, handPwd);
        UrmUserBasicInfDO userBasicInfDO = (UrmUserBasicInfDO) userInf.get("basicInf");
        UrmCprExtInfDO cprExtInfDO = (UrmCprExtInfDO) userInf.get("cprExtInf");
        UrmSafeLoginDO safeLoginDO = (UrmSafeLoginDO) userInf.get("loginInf");
//        UrmSafeInfDO safeInfDO = (UrmSafeInfDO) userInf.get("safeInf");
        UserBasicInfDTO userBasicInfDTO = new UserBasicInfDTO();
        BeanUtils.copyProperties(userBasicInfDTO, userBasicInfDO);
        if (JudgeUtils.isNotNull(cprExtInfDO)) {
            BeanUtils.copyProperties(userBasicInfDTO, cprExtInfDO);
        }
        if (JudgeUtils.isNotNull(safeLoginDO)) {
            BeanUtils.copyProperties(userBasicInfDTO, safeLoginDO);
        }

        UrmSafeInfDO safeInfDO = urmSafeInfDao.searchTeamOpr(safeLoginDO.getLoginId());
        TeamRoleTypeDO teamRoleTypeDO = teamRoleTypeDao.get(safeInfDO.getRoleId());
        userBasicInfDTO.setCprTyp(teamRoleTypeDO.getPermType());
        userBasicInfDTO.setInvMod(safeInfDO.getSafeStsw());

//        if (JudgeUtils.isNotNull(safeInfDO)) {
//            BeanUtils.copyProperties(userBasicInfDTO, safeInfDO);
//            if(safeInfDO.getHandFlg() != null && !(URMConstants.HAND_FLG_N).equals(safeInfDO.getHandFlg())){
//                Boolean checkRs = userPasswordService.checkHandPwdValid(safeInfDO);
//                if(checkRs){
//                    userBasicInfDTO.setHandFlg("0");
//                }
//            }
//        }
        return GenericRspDTO.newSuccessInstance(userBasicInfDTO);
    }

    @ApiOperation(value = "支付密码校验", notes = "checkPayPassword")
    @ApiResponse(code = 200, message = "支付密码校验")
    @PutMapping(value = "/checkpaypwd")
    public GenericRspDTO<NoBody> checkPayPassword(@Validated @RequestBody GenericDTO<CheckPayPwdDTO> checkPayPwdDTO) {
        logger.info("支付密码校验...");
//        String userId = checkPayPwdDTO.getBody().getUserId();
//        if (JudgeUtils.isNull(userId)) {
//            userId = LemonUtils.getUserId();
//        }
//        String payPwd = checkPayPwdDTO.getBody().getPayPwd();
//        String payPwdRandom = checkPayPwdDTO.getBody().getPayPwdRandom();
//        String randomServer;
//        if (JudgeUtils.notEquals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
//            randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
//        } else {
//            randomServer = decryptRSAEncryptedRandom(payPwdRandom);
//        }
//        payPwd = convertRSAEncryptedPin(payPwdRandom, randomServer, payPwd, URMConstants.PAY_PWD_MODE);
        String userId = JudgeUtils.isBlank(checkPayPwdDTO.getUserId()) ? LemonUtils.getUserId() : checkPayPwdDTO.getUserId();
        String payPwd = checkPayPwdDTO.getBody().getPayPwd();
        //支付密码加密
        if (JudgeUtils.isNotBlank(payPwd)) {
            payPwd = DigestUtils.md5Hex(payPwd + userId);
        }
        Boolean result = this.iUserAuthenticationService.checkPayPwd(userId, payPwd);
        logger.info(result.toString());
        if (result) {
            return GenericRspDTO.newSuccessInstance();
        } else {
            return GenericRspDTO.newInstance(URMMessageCode.PAY_PWD_CHECK_FAIL);
        }
    }

    @ApiOperation(value = "重置密码校验支付密码", notes = "checkLoginPassword")
    @ApiResponse(code = 200, message = "重置密码校验支付密码")
    @PostMapping(value = "/checkresetpwd")
    public GenericRspDTO<String> checkResetPassword(@Validated @RequestBody GenericDTO<ReSetCheckPayPwdDTO> checkResetPwdDTO) {
        String payPwdRandom = iUserAuthenticationService.resetPwdCheckPayPwd(checkResetPwdDTO);
        return GenericRspDTO.newSuccessInstance(payPwdRandom);
    }

    @ApiOperation(value = "登录密码校验")
    @ApiResponse(code = 200, message = "登录密码校验")
    @PutMapping(value = "/checkloginpwd")
    public GenericRspDTO<NoBody> checkLoginPwd(@Validated @RequestBody GenericDTO<CheckLoginPwdDTO> loginPwdDTO) {
        String loginId = loginPwdDTO.getBody().getLoginId();
        String loginPwd = loginPwdDTO.getBody().getLoginPwd();
        logger.debug("loginPwd: " + loginPwd);
        String loginPwdRandom = loginPwdDTO.getBody().getLoginPwdRandom();
        int pwdLength = Hex.toHexString(Base64.decode(loginPwd)).length();
        String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
        if (JudgeUtils.equals(pwdLength, URMConstants.NEW_USER_PWD)){
            loginPwd = convertRSAEncryptedPin(loginPwdRandom, randomServer, loginPwd, URMConstants.LOG_PWD_MODE);
        } else {
            LemonException.throwLemonException(URMMessageCode.LOG_PWD_CHECK_FAIL);
        }
        logger.debug("translatedLoginPwd: " + loginPwd);
        Boolean result = this.iUserAuthenticationService.checkLoginPwd(loginId, loginPwd);
        if (result) {
            return GenericRspDTO.newSuccessInstance();
        } else {
            return GenericRspDTO.newInstance(URMMessageCode.USR_AUTH_FAILURE);
        }
    }

    @ApiOperation(value = "密保校验")
    @ApiResponse(code = 200, message = "密保校验")
    @PutMapping(value = "/checksafeques")
    public GenericRspDTO<NoBody> checkSafeQues(@Validated @RequestBody GenericDTO<CheckSafeQuesDTO> safeQuesDTO) {
        String loginId = safeQuesDTO.getBody().getLoginId();
        String safeQues = safeQuesDTO.getBody().getSafeQues();
        String safeAns = safeQuesDTO.getBody().getSafeAns();
        Boolean result = this.iUserAuthenticationService.checkSafeQues(loginId, safeQues, safeAns);
        if (result) {
            return  GenericRspDTO.newSuccessInstance();
        } else {
            return GenericRspDTO.newInstance(URMMessageCode.SAFE_QUES_CHECK_FAIL);
        }
    }

    @ApiOperation(value = "手势密码校验")
    @ApiResponse(code = 200, message = "手势密码校验")
    @PutMapping(value = "/checkHandPwd")
    public GenericRspDTO<NoBody> checkHandPwd(@Validated @RequestBody GenericDTO<CheckHandPwdDTO> loginPwdDTO) {
        String loginId = LemonUtils.getLoginName();
        String handPwd = loginPwdDTO.getBody().getHandPwd();
        logger.debug("handPwd: " + handPwd);
        String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
        try{
            handPwd = RSAUtil2.decryptByKey(handPwd.getBytes("UTF-8"));
            if(randomServer == null || "".equals(randomServer)){
                throw new Exception("randomServer null !");
            }
            if(handPwd == null || "".equals(handPwd)){
                throw new Exception("Decrypt error !");
            }
            logger.info("handPwd : " + handPwd );
            String []tmp = handPwd.split(",");
            handPwd = tmp[0];
            tmp[1] = tmp[1].substring(0, randomServer.length());
            logger.info("handPwd : " + handPwd + ", radom : " + tmp[1] + ", randomServer : " + randomServer);
            if(!tmp[1].equals(randomServer)){
                throw new Exception("randomServer error !");
            }
            logger.info("RSA解密成功！");
        }catch (Exception e){
            e.printStackTrace();
            logger.error("RSA解密失败：" + e.getMessage());
            LemonException.throwLemonException(URMMessageCode.RSA2_DECRYPT_FAILURE);
        }
        logger.debug("translatedHandPwd: " + handPwd);
        Boolean result = this.iUserAuthenticationService.checkHandPwd(loginId, handPwd);
        if (result) {
            return  GenericRspDTO.newSuccessInstance();
        } else {
            return GenericRspDTO.newInstance(URMMessageCode.Hand_PWD_CHECK_FAIL);
        }
    }

    @ApiOperation(value = "支付密码校验", notes = "checkPayPassword")
    @ApiResponse(code = 200, message = "支付密码校验")
    @PutMapping(value = "/sea/checkpaypwd")
    public GenericRspDTO<NoBody> checkPayPasswordSea(@Validated @RequestBody GenericDTO<CheckPayPwdSeaDTO> checkPayPwdDTO) {
        String userId = checkPayPwdDTO.getBody().getUserId();
        if (JudgeUtils.isNull(userId)) {
            userId = LemonUtils.getUserId();
        }
        String payPwd = checkPayPwdDTO.getBody().getPayPwd();
        String payPwdRandom = checkPayPwdDTO.getBody().getPayPwdRandom();
        String randomServer = checkPayPwdDTO.getBody().getSeaRandom();
        payPwd = convertRSAEncryptedPinNew(payPwdRandom, randomServer, payPwd, URMConstants.PAY_PWD_MODE);
        Boolean result = this.iUserAuthenticationService.checkPayPwd(userId, payPwd);
        logger.info(result.toString());
        if (result) {
            return GenericRspDTO.newSuccessInstance();
        } else {
            return GenericRspDTO.newInstance(URMMessageCode.PAY_PWD_CHECK_FAIL);
        }
    }
}
