package com.hisun.lemon.urm.entity;

import io.swagger.annotations.ApiModelProperty;

/**
 * 修改邮箱dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/6 15:22
 */
public class ModifyEmailDTO  {
    //旧邮箱
    /**
     * 旧邮箱
     */
    @ApiModelProperty(name = "oldEmail", value = "旧邮箱", dataType = "String")
    private String oldEmail;
    //旧邮箱验证码
    /**
     * 旧邮箱验证码
     */
    @ApiModelProperty(name = "oldEmailCode", value = "旧邮箱验证码随机数", dataType = "String")
    private String oldEmailCode;
    //新邮箱
    /**
     * 新邮箱
     */
    @ApiModelProperty(name = "newEmail", value = "新邮箱", dataType = "String")
    private String newEmail;
    //新邮箱验证码
    /**
     * 新邮箱验证码
     */
    @ApiModelProperty(name = "newEmailCode", value = "新邮箱验证码随机数", dataType = "String")
    private String newEmailCode;
    //支付密码
    /**
     * 支付密码
     */
    @ApiModelProperty(name = "payPwd", value = "支付密码", dataType = "String")
    private String payPwd;

    //getset方法
    public String getOldEmail() {
        return oldEmail;
    }

    public void setOldEmail(String oldEmail) {
        this.oldEmail = oldEmail;
    }

    public String getOldEmailCode() {
        return oldEmailCode;
    }

    public void setOldEmailCode(String oldEmailCode) {
        this.oldEmailCode = oldEmailCode;
    }

    public String getNewEmail() {
        return newEmail;
    }

    public void setNewEmail(String newEmail) {
        this.newEmail = newEmail;
    }

    public String getNewEmailCode() {
        return newEmailCode;
    }

    public void setNewEmailCode(String newEmailCode) {
        this.newEmailCode = newEmailCode;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    @Override
    public String toString() {
        return "ModifyEmailDTO{" +
                "oldEmail='" + oldEmail + '\'' +
                ", oldEmailCode='" + oldEmailCode + '\'' +
                ", newEmail='" + newEmail + '\'' +
                ", newEmailCode='" + newEmailCode + '\'' +
                ", payPwd='" + payPwd + '\'' +
                '}';
    }
}
