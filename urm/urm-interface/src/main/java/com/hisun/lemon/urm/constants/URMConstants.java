package com.hisun.lemon.urm.constants;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @function URMConstants
 * @description 用户常量信息
 * @date 7/25/2017 Tue
 * @time 3:08 PM
 */
public class URMConstants {

    private URMConstants() {

    }

    /**
     * 商户ID起始编码
     */
    public static final String MER_STATIC_ID = "888888";

    /**
     * 登录用户类别0,普通用户，1, 商户操作员，商户操作员只能LOGIN_ID登录 2,商户管理员
     */
    public static final String USR_TYP = "0";

    public static final String MER_OPE_TYP = "1";

    public static final String MER_ADMIN_TYP = "2";

    /**
     * 用户状态 0:开户 1:销户
     */
    public static final String USR_OPEN = "0";

    public static final String USR_CANCEL = "1";

    /**
     * 用户实名标志 0 非实名， 1 实名
     */
    public static final String REAL_NM_FLG = "1";

    public static final String NOT_REAL_NM_FLG = "0";

    /**
     * USR_LVL用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家
     */
    public static final String ORDINARY_USER = "0";

    public static final String ENTERPRISE_USER = "1";

    public static final String INDIVIDUAL_BUSINESS = "2";

    public static final String ENTERPRISE_BUSINESS = "3";

    /**
     * 登录状态 1：失效 0：生效
     */
    public static final String SAFE_STS_EFF = "0";

    public static final String SAFE_STS_EXP = "1";

    /**
     * 角色状态 1：正常 0：停用
     */
    public static final String ROLE_STS_NORMAL = "1";
    public static final String ROLE_STS_DISABLE = "0";
    /**
     * 安全信息变更类型 0 重置支付密码 1 修改支付密码 2 重置登录密码 3 修改登录密码 4重置手势密码 5修改手势密码
     */
    public static final String LOGIN_PWD_UPD = "1";
    public static final String LOGIN_PWD_RESET = "0";
    public static final String PAY_PWD_UPD = "3";
    public static final String PAY_PWD_RESET = "2";
    public static final String HAND_PWD_UPD = "5";
    public static final String HAND_PWD_RESET = "4";

    /**
     * 登录ID的类型：0，系统生成登录号，1 手机号码 2 邮箱 3 昵称
     */
    public static final String SYS_LOGIN_ID = "0";

    public static final String MBL_LOGIN_ID = "1";

    public static final String EMAIL_LOGIN_ID = "2";

    public static final String USER_DEFINE_LOGIN_ID = "3";

    /**
     * 默认最大的同一证件的持有账户为5个
     */
    public static final int MAX_SAME_ID_NO = 5;

    /**
     * 中国的默认代码
     */
    public static final String ZH_CN = "+86";

    /**
     * 用户重置支付密码的余额标准
     */
    public static final BigDecimal BAL_STANDARD = BigDecimal.valueOf(2000, 2);

    /**
     * 证件类型
     */
    public static final String ID_CARD_TYP = "0";
    public static final String PASSPORT_TYP = "1";

    /**
     * 密码控件随机数KEY
     */
    public static final String PWD_RANDOM = "PWD_RANDOM";
   //随机数的有效时间毫秒
    public static final int RANDOM_EFF_TIME = 3600000;

    //随机数的长度
    public static final int RANDOM_LENGTH = 16;

    /**
     * 密钥常量
     */
    //APP渠道ZPK
//    public static final String APP_ZPK = "4961F18392540BDA09A6D3867CD79414";

    // 平台PVK
//    public static final String SYS_LOG_PVK = "F216FB126D6502D05783DBE2CC1DC5BA";
//    public static final String SYS_PAY_PVK = "372F7DAA46BDF8C8";
//    public static final String SYS_PAY_ZPK = "30C8AC78B0D77139";

    //加密控件公钥
//    public static final String APP_PUB_KEY =
//            "30818902818100CE77F54D371BF98D29B55537BCA579EF95BC82CBF817147A49D0867E3FD0BB25C900CE8FB563F3F9BF786078CFBA3AE4DE8399E856F235AC806F7CC442DFCD1F86FD6453F6285B1CBCC58D24E5943C33EB5B2E436E949C3A156A264D9395EEAF0CDBDC1240F1ADD9B66B276B87B631E024F467014BF3DEBE0F9C2FB967C202410203010001";

    /**
     * PINMODE 密码模式 P 登录密码(8-15位) O 支付密码(<8位)
     */
    public static final String LOG_PWD_MODE = "P";
    public static final String PAY_PWD_MODE = "S";
    //密码长度6位明文，16位密文，8-15位明文 32位密文
    public static final int OLD_USER_PWD = 16;
    public static final int NEW_USER_PWD = 32;
    public static final int FIFTEEN_PWD_LEN = 48;

    //商户管理员密码短信
    public static final String SMS_TYP = "0";
    public static final String MER_ADMIN_TEMP_ID = "URM001";
    public static final String PAY_PWD_TEMP_ID = "URM002";
    public static final String LOG_PWD_TEMP_ID = "URM003";

    //CRM登录鉴权接口
    public static final String CRM_LOGIN_URL = "/crm/auth";
    //CRM注册接口
    public static final String CRM_REG_URL = "/user/register";

    //CRM返回交易成功
    public static final String CRM_SUCC = "1";

    //商户密钥版本1.0
    public static final String CPR_KEY_VER = "1.0";

    //网关
    public static final String INTERNAL_GATE = "IGW";
    public static final String EXTERNAL_GATE = "AGW";

    //商户密钥
    public static final String CPR_KEY_TYP = "MD5";
    public static final String CPR_KEY_EFF = "1";

    //商户操作员初始权限
    public static final String MERC_OPR_INIT_AUTH = "00000000000000";
    //商户管理员权限
    public static final String MERC_OPR_ADMIN_AUTH = "11111111111111";

    //1:手势密码,
    public static final String LOGIN_TYPE_H = "2";
    //2:密码控件
    public static final String LOGIN_TYPE_C = "1";

    //手势密码标志：
    // 0未开通
    public static final String HAND_FLG_N = "0";
    // 1开启
    public static final String HAND_FLG_O = "1";
     //2关闭
    public static final String HAND_FLG_C = "2";
    //3设置
    public static final String HAND_FLG_S = "3";

    public static final String SOURCE_BATCH_OPEN = "BATCH_O";

    /**
     * Kyb审核信息
     * 00-新建待审核
     * 02-初审中
     * 03-复核中
     * 04-审核通过
     */
    public static final String EXAMINE_STATUS_NO_PASS ="00";
    public static final String EXAMINE_FIRST_AUDIT ="02";
    public static final String EXAMINE_SECOND_AUDIT ="03";
    public static final String EXAMINE_APPROVED ="04";
    public static final String EXAMINE_REJECTED = "05";
    public static final String EXAMINE_TEMP = "06";

    //已通过kyb认证
    public static final String KYB_CERT = "Y";
    //未通过kyb认证
    public static final String NO_KYB_CERT = "N";

    //默认密码首次登录标识
    public static final String DEFAULT_PASSWORD = "Y";
}
