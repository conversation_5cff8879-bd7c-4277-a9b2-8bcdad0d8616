package com.hisun.lemon.urm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Function : 用户管理传输对象
 * Description : 用户管理传输对象
 * Date : 7/6/2017 Thu
 *
 * <AUTHOR>
 */
@ApiModel("用户基本信息")
public class UserBasicInfDTO {
    /**
     *  userId 内部用户号
     */
    @ApiModelProperty(name = "userId", value = "用户ID")
    @Length(max = 16)
    private String userId;

    /**
     * mblNo 手机号
     */
    @ApiModelProperty(name = "mblNo", value = "用户手机号")
    private String mblNo;

    /**
     * displayNm 显示姓名
     */
    @ApiModelProperty(name = "displayNm", value = "显示姓名")
    private String displayNm;

    /**
     * avatarPath 头像路径
     */
    @ApiModelProperty(name = "avatarPath", value = "头像路径")
    private String avatarPath;

    /**
     *  usrSts 用户状态 0:开户 1:销户
     */
    @ApiModelProperty(name = "usrSts", value = "用户状态0:开户 1:销户")
    @Length(max = 1)
    private String usrSts;
    /**
     *  用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家
     */
    @ApiModelProperty(name = "usrLvl", value = "用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家")
    @Length(max = 1)
    private String usrLvl;
    /**
     *  idChkFlg 实名标志 0：非实名 1：实名
     */
    @ApiModelProperty(name = "idChkFlg", value = "实名标志0：非实名 1：实名")
    @Length(max = 1)
    private String idChkFlg;
    /**
     *  idType 证件类型
     */
    @ApiModelProperty(name = "idType", value = "证件类型")
    @Length(max = 2)
    private String idType;
    /**
     *  idNo 证件号码
     */
    @ApiModelProperty(name = "idNo", value = "证件号码")
    @Length(max = 64)
    private String idNo;
    /**
     *  idNoHid 脱敏证件号码
     */
    @ApiModelProperty(name = "idNoHid", value = "脱敏证件号码")
    @Length(max = 64)
    private String idNoHid;
    /**
     *  usrNm 用户姓名
     */
    @ApiModelProperty(name = "usrNm", value = "用户姓名")
    @Length(max = 64)
    private String usrNm;
    /**
     *  usrNmHid 脱敏用户姓名
     */
    @ApiModelProperty(name = "usrNmHid", value = "脱敏用户姓名")
    @Length(max = 64)
    private String usrNmHid;
    /**
     *  usrGender 用户性别 M：男 F：女
     */
    @ApiModelProperty(name = "usrGender", value = "用户性别M：男 F：女")
    @Length(max = 1)
    private String usrGender;
    /**
     *  usrNation 用户归属国家
     */
    @ApiModelProperty(name = "usrNation", value = "用户归属国家")
    @Length(max = 64)
    private String usrNation;
    /**
     *  usrBirthDt 出生日期
     */
    @ApiModelProperty(name = "usrBirthDt", value = "出生日期")
    @Length(max = 8)
    private String usrBirthDt;
    /**
     *  issuAuth 签发机关
     */
    @ApiModelProperty(name = "issuAuth", value = "签发机关")
    @Length(max = 128)
    private String issuAuth;
    /**
     *  idEffDt 证件有效期起始
     */
    @ApiModelProperty(name = "idEffDt", value = "证件有效期起始")
    @Length(max = 8)
    private String idEffDt;
    /**
     *  idExpDt 证件有效期截止
     */
    @ApiModelProperty(name = "idExpDt", value = "证件有效期截止")
    @Length(max = 8)
    private String idExpDt;
    /**
     *  usrRegCnl 注册渠道
     */
    @ApiModelProperty(name = "usrRegCnl", value = "注册渠道")
    @Length(max = 5)
    private String usrRegCnl;
    /**
     *  usrRegIp 注册IP
     */
    @ApiModelProperty(name = "usrRegIp", value = "用户注册IP")
    @Length(max = 15)
    private String usrRegIp;
    /**
     *  usrRegDt 注册日期
     */
    @ApiModelProperty(name = "usrRegDt", value = "用户注册日期")
    private LocalDate usrRegDt;
    /**
     *  usrRegTm 注册时间
     */
    @ApiModelProperty(name = "usrRegTm", value = "用户注册时间")
    private LocalTime usrRegTm;
    /**
     *  usrClsDt 销户日期
     */
    @ApiModelProperty(name = "usrClsDt", value = "用户销户日期")
    private LocalDate usrClsDt;
    /**
     *  usrClsTm 销户时间
     */
    @ApiModelProperty(name = "usrClsTm", value = "用户销户时间")
    private LocalTime usrClsTm;
    /**
     *  kybCert kyb认证
     */
    @ApiModelProperty(name = "kybCert", value = "kyb认证")
    private String kybCert;

    //商户扩展信息
    /**
     *  mercName 商户名称
     */
    @ApiModelProperty(name = "mercName", value = "商户名称")
    @Length(max = 100)
    private String mercName;
    /**
     *  mercShortName 商户简称
     */
    @ApiModelProperty(name = "mercShortName", value = "商户简称")
    @Length(max = 50)
    private String mercShortName;

    /**
     * merLvl 商户级别
     */
    @ApiModelProperty(name = "merLvl", value = "商户级别")
    @Length(max = 10)
    private String merLvl;

    /**
     *  cprRegNmCn 注册名称(中文)
     */
    @ApiModelProperty(name = "cprRegNmCn", value = "注册名称")
    @Length(max = 60)
    private String cprRegNmCn;
    /**
     *  cprOperNmCn 经营名称（中文）
     */
    @ApiModelProperty(name = "cprOperNmCn", value = "经营名称")
    @Length(max = 60)
    private String cprOperNmCn;
    /**
     *  prinNm 负责人名称
     */
    @ApiModelProperty(name = "prinNm", value = "负责人名称")
    @Length(max = 64)
    private String prinNm;
    /**
     *  comercReg 工商注册号
     */
    @ApiModelProperty(name = "comercReg", value = "工商注册号")
    @Length(max = 32)
    private String comercReg;
    /**
     *  socialCrdCd 社会信用代码
     */
    @ApiModelProperty(name = "socialCrdCd", value = "社会信用代码")
    @Length(max = 32)
    private String socialCrdCd;
    /**
     *  orgCd 组织机构代码
     */
    @ApiModelProperty(name = "orgCd", value = "组织机构代码")
    @Length(max = 32)
    private String orgCd;
    /**
     *  busiLisc 营业执照
     */
    @ApiModelProperty(name = "busiLisc", value = "营业执照")
    @Length(max = 32)
    private String busiLisc;
    /**
     *  taxCertId 税务证明
     */
    @ApiModelProperty(name = "taxCertId", value = "税务证明")
    @Length(max = 128)
    private String taxCertId;
    /**
     *  webNm 网站名称
     */
    @ApiModelProperty(name = "webNm", value = "网站名称")
    @Length(max = 64)
    private String webNm;
    /**
     *  webUrl 网站地址
     */
    @ApiModelProperty(name = "webUrl", value = "网站地址")
    @Length(max = 128)
    private String webUrl;
    /**
     *  merRegAddr 公司注册地址
     */
    @ApiModelProperty(name = "merRegAddr", value = "公司注册地址")
    @Length(max = 128)
    private String merRegAddr;
    /**
     *  merAddrLongitude 公司地址的所在经度
     */
    @ApiModelProperty(name = "merAddrLongitude", value = "公司地址所在经度")
    @Length(max = 15)
    private BigDecimal merAddrLongitude;
    /**
     *  merAddrLatitude 公司地址的所在纬度
     */
    @ApiModelProperty(name = "merAddrLatitude", value = "公司地址所在纬度")
    @Length(max = 15)
    private BigDecimal merAddrLatitude;
    /**
     *  mgtScp 经营范围
     */
    @ApiModelProperty(name = "mgtScp", value = "经营范围")
    @Length(max = 1024)
    private String mgtScp;
    /**
     *  needInvFlg 是否开具发票 Y：需要；N：不需要；
     */
    @ApiModelProperty(name = "needInvFlg", value = "是否开具发票")
    @Length(max = 2)
    private String needInvFlg;
    /**
     *  invMod 开具发票方式 0 - 按季度开；1 - 按月开；2 –按年开；
     */
    @ApiModelProperty(name = "invMod", value = "开具发票方式")
    @Length(max = 2)
    private String invMod;
    /**
     *  invTit 发票抬头
     */
    @ApiModelProperty(name = "invTit", value = "发票抬头")
    @Length(max = 64)
    private String invTit;
    /**
     *  invMailAddr 发票邮寄地址
     */
    @ApiModelProperty(name = "invMailAddr", value = "发票邮寄地址")
    @Length(max = 128)
    private String invMailAddr;
    /**
     *  invMailZip 发票邮寄邮编
     */
    @ApiModelProperty(name = "invMailZip", value = "发票邮寄邮编")
    @Length(max = 12)
    private String invMailZip;
    /**
     *  mercTrdCls 商户行业类别
     */
    @ApiModelProperty(name = "mercTrdCls", value = "商户行业类别")
    @Length(max = 20)
    private String mercTrdCls;
    /**
     *  mercTrdDesc 商户行业描述
     */
    @ApiModelProperty(name = "mercTrdDesc", value = "商户商业描述")
    private String mercTrdDesc;
    /**
     *  cprTyp 商户类别 01-国有，02-私有，03-外资，04-合资 08-个人，10-公司，11-个人独资
     */
    @ApiModelProperty(name = "cprTyp", value = "商户类别")
    private String cprTyp;
    /**
     *  csTelNo 商户客服电话
     */
    @ApiModelProperty(name = "csTelNo", value = "商户客服电话")
    @Length(max = 20)
    private String csTelNo;
    /**
     *  mercHotLin 商户热线
     */
    @ApiModelProperty(name = "mercHotLin", value = "商户热线")
    @Length(max = 20)
    private String mercHotLin;
    /**
     *  cusMgr 客户经理编号
     */
    @ApiModelProperty(name = "cusMgr", value = "客户经理编号")
    @Length(max = 24)
    private String cusMgr;
    /**
     *  cusMgrNm 客户经理名称
     */
    @ApiModelProperty(name = "cusMgrNm", value = "客户经理名称")
    @Length(max = 64)
    private String cusMgrNm;
    /**
     *  rcvMagAmt 应收商户保证金
     */
    @ApiModelProperty(name = "rcvMagAmt", value = "应收商户保证金")
    @Length(max = 13)
    private BigDecimal rcvMagAmt;

    @ApiModelProperty(name = "handFlg", value = "手势密码标志 1.开通，2.关闭， 0.未开通")
    private String handFlg;

    @ApiModelProperty(name = "crpIdNo", value = "商户法人证件号")
    private String crpIdNo;

    @ApiModelProperty(name = "crpIdTyp", value = "商户法人证件类型")
    private String crpIdTyp;

    @ApiModelProperty(name = "permType", value = "拥有的权限")
    private String permType;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getDisplayNm() {
        return displayNm;
    }

    public void setDisplayNm(String displayNm) {
        this.displayNm = displayNm;
    }

    public String getAvatarPath() {
        return avatarPath;
    }

    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath;
    }

    public String getUsrSts() {
        return usrSts;
    }

    public void setUsrSts(String usrSts) {
        this.usrSts = usrSts;
    }

    public String getUsrLvl() {
        return usrLvl;
    }

    public void setUsrLvl(String usrLvl) {
        this.usrLvl = usrLvl;
    }

    public String getIdChkFlg() {
        return idChkFlg;
    }

    public void setIdChkFlg(String idChkFlg) {
        this.idChkFlg = idChkFlg;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdNoHid() {
        return idNoHid;
    }

    public void setIdNoHid(String idNoHid) {
        this.idNoHid = idNoHid;
    }

    public String getUsrNm() {
        return usrNm;
    }

    public void setUsrNm(String usrNm) {
        this.usrNm = usrNm;
    }

    public String getUsrNmHid() {
        return usrNmHid;
    }

    public void setUsrNmHid(String usrNmHid) {
        this.usrNmHid = usrNmHid;
    }

    public String getUsrGender() {
        return usrGender;
    }

    public void setUsrGender(String usrGender) {
        this.usrGender = usrGender;
    }

    public String getUsrNation() {
        return usrNation;
    }

    public void setUsrNation(String usrNation) {
        this.usrNation = usrNation;
    }

    public String getUsrBirthDt() {
        return usrBirthDt;
    }

    public void setUsrBirthDt(String usrBirthDt) {
        this.usrBirthDt = usrBirthDt;
    }

    public String getIssuAuth() {
        return issuAuth;
    }

    public void setIssuAuth(String issuAuth) {
        this.issuAuth = issuAuth;
    }

    public String getIdEffDt() {
        return idEffDt;
    }

    public void setIdEffDt(String idEffDt) {
        this.idEffDt = idEffDt;
    }

    public String getIdExpDt() {
        return idExpDt;
    }

    public void setIdExpDt(String idExpDt) {
        this.idExpDt = idExpDt;
    }

    public String getUsrRegCnl() {
        return usrRegCnl;
    }

    public void setUsrRegCnl(String usrRegCnl) {
        this.usrRegCnl = usrRegCnl;
    }

    public String getUsrRegIp() {
        return usrRegIp;
    }

    public void setUsrRegIp(String usrRegIp) {
        this.usrRegIp = usrRegIp;
    }

    public LocalDate getUsrRegDt() {
        return usrRegDt;
    }

    public void setUsrRegDt(LocalDate usrRegDt) {
        this.usrRegDt = usrRegDt;
    }

    public LocalTime getUsrRegTm() {
        return usrRegTm;
    }

    public void setUsrRegTm(LocalTime usrRegTm) {
        this.usrRegTm = usrRegTm;
    }

    public LocalDate getUsrClsDt() {
        return usrClsDt;
    }

    public void setUsrClsDt(LocalDate usrClsDt) {
        this.usrClsDt = usrClsDt;
    }

    public LocalTime getUsrClsTm() {
        return usrClsTm;
    }

    public void setUsrClsTm(LocalTime usrClsTm) {
        this.usrClsTm = usrClsTm;
    }

    public String getKybCert() {
        return kybCert;
    }

    public void setKybCert(String kybCert) {
        this.kybCert = kybCert;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getMercShortName() {
        return mercShortName;
    }

    public void setMercShortName(String mercShortName) {
        this.mercShortName = mercShortName;
    }

    public String getCprRegNmCn() {
        return cprRegNmCn;
    }

    public void setCprRegNmCn(String cprRegNmCn) {
        this.cprRegNmCn = cprRegNmCn;
    }

    public String getCprOperNmCn() {
        return cprOperNmCn;
    }

    public void setCprOperNmCn(String cprOperNmCn) {
        this.cprOperNmCn = cprOperNmCn;
    }

    public String getPrinNm() {
        return prinNm;
    }

    public void setPrinNm(String prinNm) {
        this.prinNm = prinNm;
    }

    public String getComercReg() {
        return comercReg;
    }

    public void setComercReg(String comercReg) {
        this.comercReg = comercReg;
    }

    public String getSocialCrdCd() {
        return socialCrdCd;
    }

    public void setSocialCrdCd(String socialCrdCd) {
        this.socialCrdCd = socialCrdCd;
    }

    public String getOrgCd() {
        return orgCd;
    }

    public void setOrgCd(String orgCd) {
        this.orgCd = orgCd;
    }

    public String getBusiLisc() {
        return busiLisc;
    }

    public void setBusiLisc(String busiLisc) {
        this.busiLisc = busiLisc;
    }

    public String getTaxCertId() {
        return taxCertId;
    }

    public void setTaxCertId(String taxCertId) {
        this.taxCertId = taxCertId;
    }

    public String getWebNm() {
        return webNm;
    }

    public void setWebNm(String webNm) {
        this.webNm = webNm;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getMerRegAddr() {
        return merRegAddr;
    }

    public void setMerRegAddr(String merRegAddr) {
        this.merRegAddr = merRegAddr;
    }

    public BigDecimal getMerAddrLongitude() {
        return merAddrLongitude;
    }

    public void setMerAddrLongitude(BigDecimal merAddrLongitude) {
        this.merAddrLongitude = merAddrLongitude;
    }

    public BigDecimal getMerAddrLatitude() {
        return merAddrLatitude;
    }

    public void setMerAddrLatitude(BigDecimal merAddrLatitude) {
        this.merAddrLatitude = merAddrLatitude;
    }

    public String getMgtScp() {
        return mgtScp;
    }

    public void setMgtScp(String mgtScp) {
        this.mgtScp = mgtScp;
    }

    public String getNeedInvFlg() {
        return needInvFlg;
    }

    public void setNeedInvFlg(String needInvFlg) {
        this.needInvFlg = needInvFlg;
    }

    public String getInvMod() {
        return invMod;
    }

    public void setInvMod(String invMod) {
        this.invMod = invMod;
    }

    public String getInvTit() {
        return invTit;
    }

    public void setInvTit(String invTit) {
        this.invTit = invTit;
    }

    public String getInvMailAddr() {
        return invMailAddr;
    }

    public void setInvMailAddr(String invMailAddr) {
        this.invMailAddr = invMailAddr;
    }

    public String getInvMailZip() {
        return invMailZip;
    }

    public void setInvMailZip(String invMailZip) {
        this.invMailZip = invMailZip;
    }

    public String getMercTrdCls() {
        return mercTrdCls;
    }

    public void setMercTrdCls(String mercTrdCls) {
        this.mercTrdCls = mercTrdCls;
    }

    public String getMercTrdDesc() {
        return mercTrdDesc;
    }

    public void setMercTrdDesc(String mercTrdDesc) {
        this.mercTrdDesc = mercTrdDesc;
    }

    public String getCprTyp() {
        return cprTyp;
    }

    public void setCprTyp(String cprTyp) {
        this.cprTyp = cprTyp;
    }

    public String getCsTelNo() {
        return csTelNo;
    }

    public void setCsTelNo(String csTelNo) {
        this.csTelNo = csTelNo;
    }

    public String getMercHotLin() {
        return mercHotLin;
    }

    public void setMercHotLin(String mercHotLin) {
        this.mercHotLin = mercHotLin;
    }

    public String getCusMgr() {
        return cusMgr;
    }

    public void setCusMgr(String cusMgr) {
        this.cusMgr = cusMgr;
    }

    public String getCusMgrNm() {
        return cusMgrNm;
    }

    public void setCusMgrNm(String cusMgrNm) {
        this.cusMgrNm = cusMgrNm;
    }

    public BigDecimal getRcvMagAmt() {
        return rcvMagAmt;
    }

    public void setRcvMagAmt(BigDecimal rcvMagAmt) {
        this.rcvMagAmt = rcvMagAmt;
    }

    public String getMerLvl() {
        return merLvl;
    }

    public void setMerLvl(String merLvl) {
        this.merLvl = merLvl;
    }

    public String getHandFlg() {
        return handFlg;
    }

    public void setHandFlg(String handFlg) {
        this.handFlg = handFlg;
    }

    public String getCrpIdNo() {
        return crpIdNo;
    }

    public void setCrpIdNo(String crpIdNo) {
        this.crpIdNo = crpIdNo;
    }

    public String getCrpIdTyp() {
        return crpIdTyp;
    }

    public void setCrpIdTyp(String crpIdTyp) {
        this.crpIdTyp = crpIdTyp;
    }

    public String getPermType() {
        return permType;
    }

    public void setPermType(String permType) {
        this.permType = permType;
    }
}
