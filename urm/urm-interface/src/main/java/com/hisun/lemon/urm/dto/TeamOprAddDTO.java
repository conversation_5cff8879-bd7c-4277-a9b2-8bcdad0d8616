package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

public class TeamOprAddDTO {

    @ApiModelProperty(name = "email", value = "账号", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String email;

    @ApiModelProperty(name = "roleType", value = "角色类型", required = true)
    @NotBlank(message = "URM30076")
    private String roleType;

    @ApiModelProperty(name = "remark", value = "角色备注", required = true)
    private String remark;

    @ApiModelProperty(name = "isMaster", value = "是否主账户", required = true)
    private String isMaster;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRoleType() {
        return roleType;
    }

    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsMaster() {
        return isMaster;
    }

    public void setIsMaster(String isMaster) {
        this.isMaster = isMaster;
    }
}
