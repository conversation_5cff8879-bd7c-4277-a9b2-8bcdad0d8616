package com.hisun.lemon.cpo.enums;

/**
 * Created by Rui on 2017/7/5.
 */
public enum CorpBusTyp {
    /**
     * 签约类型
     */
    SIGN("01"),
    /**
     * 快捷
     */
    FASTPAY("02"),
    /**
     * 网银（支付宝，微信）
     */
    EBANKPAY("03"),
    /**
     * 汇款
     */
    REMITTANCE("04"),
    /**
     * 退款
     */
    REDUND("05"),
    /**
     * 提现
     */
    WITHDRAW("06"),

    /**
     * 数币提现
     */
    DM_WITHDRAW("DX");

    CorpBusTyp(String type) {
        this.type = type;
    }

    // 成员变量
    private String type;

    public String getType() {
        return type;
    }
}
