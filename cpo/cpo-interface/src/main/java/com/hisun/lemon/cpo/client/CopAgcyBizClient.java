package com.hisun.lemon.cpo.client;

import com.hisun.lemon.cpo.dto.CopAgcyInfoDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hisun.lemon.cpo.dto.CopAgcyBizDTO;
import com.hisun.lemon.framework.data.GenericDTO;

import java.util.List;

@FeignClient("CPO")
public interface CopAgcyBizClient {
	@GetMapping("/copAgcyBiz/addCopAgcyBiz")
    GenericRspDTO addCopAgcyBiz(@RequestBody CopAgcyBizDTO copAgcyBizDTO);

    @GetMapping("/copAgcyBiz/selectByKey")
    GenericRspDTO<List<CopAgcyInfoDTO>> selectByKey();
}
