package com.hisun.lemon.cpo.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotEmpty;

import com.hisun.lemon.framework.data.GenericDTO;

/**
 * CopAgcyBiz合作机构业务表 传输对象
 * <AUTHOR>
 *
 */
public class CopAgcyBizDTO extends GenericDTO{

	/**
	 * 合作机构编号
	 */
	@NotNull
	private String corpOrgId;

	/**
	 * 合作业务类型：01：提现,02：快捷,03：网银
	 */
	@NotNull
	private char corpBusTyp;

	/**
	 * 合作业务子类型:01-提现：0101：个人提现,0102：个人转账,02-快捷,0201：签约,0202：支付,03-网银,0301：支付
	 */
	@NotNull
	private char corpBusSubTyp;

	/**
	 * 业务生效标志，0失效，1生效
	 */
	@NotNull
	private char busEffFlg;

	/**
	 * 创建人ID
	 */
	@NotNull
	private char creOprId;

	/**
	 * 修改人ID
	 */
	@NotNull
	private String updOprId;

	public String getCorpOrgId() {
		return corpOrgId;
	}

	public void setCorpOrgId(String corpOrgId) {
		this.corpOrgId = corpOrgId;
	}

	public char getCorpBusTyp() {
		return corpBusTyp;
	}

	public void setCorpBusTyp(char corpBusTyp) {
		this.corpBusTyp = corpBusTyp;
	}

	public char getCorpBusSubTyp() {
		return corpBusSubTyp;
	}

	public void setCorpBusSubTyp(char corpBusSubTyp) {
		this.corpBusSubTyp = corpBusSubTyp;
	}

	public char getBusEffFlg() {
		return busEffFlg;
	}

	public void setBusEffFlg(char busEffFlg) {
		this.busEffFlg = busEffFlg;
	}

	public char getCreOprId() {
		return creOprId;
	}

	public void setCreOprId(char creOprId) {
		this.creOprId = creOprId;
	}

	public String getUpdOprId() {
		return updOprId;
	}

	public void setUpdOprId(String updOprId) {
		this.updOprId = updOprId;
	}
}
