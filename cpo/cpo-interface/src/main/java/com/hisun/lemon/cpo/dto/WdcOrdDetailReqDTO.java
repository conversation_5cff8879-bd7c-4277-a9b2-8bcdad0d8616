package com.hisun.lemon.cpo.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 查询提现订单详细信息请求
 */
public class WdcOrdDetailReqDTO {

    /**
     * 提现订单号
     */
    @ApiModelProperty(name = "wcOrdNo", value = "提现订单号", dataType = "String")
    @NotEmpty(message="CPO10024")
    @Length(max = 32)
    private String ordNo;

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }
}
