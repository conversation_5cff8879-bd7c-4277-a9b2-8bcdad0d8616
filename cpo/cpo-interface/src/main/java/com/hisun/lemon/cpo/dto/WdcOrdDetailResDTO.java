package com.hisun.lemon.cpo.dto;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 查询提现订单详细信息返回结果
 */
public class WdcOrdDetailResDTO {

    /**
     * 返回码
     */
    @ApiModelProperty(name = "code", value = "返回码")
    private String code;

    /**
     * 返回信息
     */
    @ApiModelProperty(name = "msg", value = "返回信息")
    private String msg;

    /**
     * 提现订单号
     */
    @ApiModelProperty(name = "wcOrdNo", value = "提现订单号")
    private String wcOrdNo;

    /**
     * 订单状态
     */
    @ApiModelProperty(name = "ordSts", value = "订单状态")
    private String ordSts;

    /**
     * 订单日期
     */
    @ApiModelProperty(name = "ordDt", value = "订单日期")
    private LocalDate ordDt;

    /**
     * 订单时间
     */
    @ApiModelProperty(name = "ordTm", value = "订单时间")
    private LocalTime ordTm;

    /**
     * 资金合作机构号
     */
    @ApiModelProperty(name = "capCorg", value = "资金合作机构号")
    private String capCorg;

    /**
     * 路径合作机构号
     */
    @ApiModelProperty(name = "rutCorg", value = "路径合作机构号")
    private String rutCorg;

    /**
     * 合作业务类型
     */
    @ApiModelProperty(name = "corpBusTyp", value = "合作业务类型")
    private String corpBusTyp;

    /**
     * 合作业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "合作业务子类型")
    private String corpBusSubTyp;

    /**
     * 请求方订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求方订单号")
    private String reqOrdNo;

    /**
     * 请求方订单日期
     */
    @ApiModelProperty(name = "reqOrdDt", value = "请求方订单日期")
    private LocalDate reqOrdDt;

    /**
     * 协议付款日
     */
    @ApiModelProperty(name = "agrPayDt", value = "协议付款日")
    private LocalDate agrPayDt;

    /**
     * 清算周期
     */
    @ApiModelProperty(name = "stlAcPerd", value = "清算周期")
    private String stlAcPerd;

    /**
     * 提现申请金额
     */
    @ApiModelProperty(name = "wcAplAmt", value = "提现申请金额")
    private BigDecimal wcAplAmt;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getWcOrdNo() {
        return wcOrdNo;
    }

    public void setWcOrdNo(String wcOrdNo) {
        this.wcOrdNo = wcOrdNo;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalDate getAgrPayDt() {
        return agrPayDt;
    }

    public void setAgrPayDt(LocalDate agrPayDt) {
        this.agrPayDt = agrPayDt;
    }

    public String getStlAcPerd() {
        return stlAcPerd;
    }

    public void setStlAcPerd(String stlAcPerd) {
        this.stlAcPerd = stlAcPerd;
    }

    public BigDecimal getWcAplAmt() {
        return wcAplAmt;
    }

    public void setWcAplAmt(BigDecimal wcAplAmt) {
        this.wcAplAmt = wcAplAmt;
    }
}
