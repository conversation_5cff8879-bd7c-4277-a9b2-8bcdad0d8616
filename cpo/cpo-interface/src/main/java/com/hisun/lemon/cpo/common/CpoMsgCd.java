package com.hisun.lemon.cpo.common;


/**
 * 错误码
 */
public enum CpoMsgCd {
    SUCCESS("CPO00000", "交易成功"),

    // 0 非业务性系统异常
    /**插入数据失败*/
    INSERT_IS_FAILURE("CPO00002", "insert record failed"),
    /**更新数据失败*/
    UPDATE_IS_FAILURE("CPO00003", "update record failed"),
    /**删除数据失败*/
    DELETE_IS_FAILURE("CPO00004", "delete record failed"),
    /**没有找到满足条件的记录*/
    SELECT_IS_NULL("CPO00005", "no records selected"),

    // 1 Validated 检查错误
    /**金额小于0*/
    AMOUNT_IS_WRONG("CPO10001", "amt is lower than zero"),
    /**资金类型为空*/
    CAP_TYP_IS_NULL("CPO10002", "cap_typ is null"),
    /**业务类型为空*/
    CORP_BUS_TYP_IS_NULL("CPO10003", "corp_bus_typ is null"),
    /**业务子类型为空*/
    CORP_BUS_SUB_TYP_IS_NULL("CPO10004", "corp_bus_sub_typ is null"),
    /**币种为空*/
    CCY_IS_NULL("CPO10005", "ccy is null"),
    /**资金机构为空*/
    CAP_CORG_IS_NULL("CPO10006", "cap_corg is null"),
    /**卡类型为空*/
    CRD_AC_TYP_IS_NULL("CPO10007", "card_ac_typ is null"),
    /**卡类型错误*/
    CRD_AC_TYP_IS_WRONG("CPO10008", "card_ac_typ is error"),
    /**协议付款日为空*/
    AGR_PAY_DT_IS_NULL("CPO10009", "agr_pay_dt is null"),
    /**申请提现金额为空*/
    WC_APL_AMT_IS_NULL("CPO10010", "wc_apl_amt is null"),
    /**冻结编号为空*/
    HOLD_NO_IS_NULL("CPO10011", "hold_no is null"),
    /**个人企业标志为空*/
    PSN_CRP_FLG_IS_NULL("CPO10012", "psn_crp_flg is null"),
    /**个人企业标志错误*/
    PSN_CRP_FLG_IS_WRONG("CPO10013", "psn_crp_flg is error"),
    /**手机号为空*/
    MBL_NO_IS_NULL("CPO10014", "mbl_no is null"),
    /**用户名为空*/
    USER_NM_IS_NULL("CPO10015", "user_nm is null"),
    /**用户号为空*/
    USER_ID_IS_NULL("CPO10016", "user_id is null"),
    /**证件类型为空*/
    ID_TYP_IS_NULL("CPO10017", "id_typ is null"),
    /**证件号码为空*/
    ID_NO_IS_NULL("CPO10018", "id_no is null"),
    /**银行卡号为空*/
    CRD_NO_IS_NULL("CPO10019", "card_no is null"),
    /**银行用户名为空*/
    CAP_CRD_NM_IS_NULL("CPO10020", "cap_crd_nm is null"),
    /**请求订单号为空*/
    REQ_ORD_NO_IS_NULL("CPO10021", "req_ord_no is null"),
    /**请求订单日期为空*/
    REQ_ORD_DT_IS_NULL("CPO10022", "req_ord_dt is null"),
    /**请求订单时间为空*/
    REQ_ORD_TM_IS_NULL("CPO10023", "req_ord_tm is null"),
    /**提现订单号为空*/
    ORD_NO_IS_NULL("CPO10024", "wc_ord_no is null"),
    /**提现状态为空*/
    ORD_STS_IS_NULL("CPO10025", "ord_sts is null"),

    // 2 通讯异常

    // 3-9 业务异常
    /**创建提现订单失败*/
    WITHDRAWORDER_CREATE_FAILURE("CPO30001", "create withdraw order failed"),
    /**合作业务类型错误*/
    CORP_BUS_TYP_WRONG("CPO30002", "corp_bus_typ is error"),
    /**合作业务子类型错误*/
    CORP_BUS_SUB_TYP_WRONG("CPO30003", "corp_bus_sub_typ is error"),
    /**没有可用路由*/
    RUT_CORG_NOT_EXISTS("CPO30004", "rut_corg not exists"),
    /**账户状态异常*/
    ACCOUNT_STATUS_WRONG("CPO30005", "account status is error"),
    /**审批状态异常*/
    WC_WF_STS_IS_WRONG("CPO30006", "wc_wf_sts is error"),
    /**创建对账批次失败*/
    CREATE_CHK_BAT_FAIL("CPO30007", "create check bat failed"),
    /**批量对账失败*/
    CHECK_BAT_FAIL("CPO30008", "check bat failed"),
    /**解析银行对账文件失败*/
    READ_CHECK_FILE_FAIL("CPO30009", "read check file failed"),
    /**账务处理失败*/
    ACCOUNT_DEAL_FAIL("CPO30010", "account deal failed"),
    /**该批次对账明细已存在*/
    IMPORT_CHECK_FILE_REPEATED("CPO30011", "check file details already exists"),
    /**下载银行对账文件失败*/
    DOWNLOAD_CHECK_FILE_FAIL("CPO30012", "download check file failed"),
    /**对账差错类型不存在*/
    CHECK_ERR_TYP_NOT_EXIST("CPO30013", "check_err_typ not exists"),
    /**导入对账文件失败*/
    IMPORT_CHECK_FILE_FAIL("CPO30014", "import check file fail"),
    /**合作资金机构信息不存在*/
    ORG_INFO_IS_NOT_FOUND("CPO30015", "crdCorpOrg is not found"),
    /**原提现订单状态已是最终态，不允许再次更改*/
    ORG_STS_IS_FINAL("CPO30016", "ordSts is final status, updated negative"),
    /**原提现订单不是处理中状态，不允许更改*/
    ORG_STS_IS_NOT_PROCESSING("CPO30017", "ordSts is not processing"),
    /**复核人和审核人不能是同一个用户*/
    REVIEWER_AND_THE_AUDITOR_CANNOT_BE_THE_SAME("CPO30018", "The reviewer and the auditor cannot be the same user"),

    /**系统异常*/
    CPO_SYS_EXCEPTION("CPO99999", "system   exception")
    ;

    private String msgCd;
    private String msgInfo;
    CpoMsgCd(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }
    public String getMsgCd() {
        return msgCd;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    
}
