package com.hisun.lemon.cpo.client;

import com.hisun.lemon.cpo.dto.RouteRspDTO;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient("CPO")
public interface RouteClient {

    /**
     * 根据业务类型、业务子类型，查询生效的合作资金机构
     * @param corpBusTyp
     * @param corpBusSubTyp
     * @return
     */
    @GetMapping("/cpo/route/capOrg")
    GenericRspDTO<RouteRspDTO> queryEffCapOrgInfo(@Validated @RequestParam(value = "corpBusTyp") CorpBusTyp corpBusTyp,
                                                        @Validated @RequestParam(value = "corpBusSubTyp") CorpBusSubTyp corpBusSubTyp);
}
