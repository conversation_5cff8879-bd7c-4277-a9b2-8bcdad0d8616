package com.hisun.lemon.cpo.dto;

import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 申请提现订单
 */
public class WithdrawReqDTO {

    /**
     * 用户ID,在userID为空的时候取这个值
     */
    @ApiModelProperty(name = "userNo", value = "用户号", required = true, dataType = "String")
    private String userNo;

    /**
     * 资金种类，1现金
     */
    @ApiModelProperty(name = "capTyp", value = "资金种类", required = true, dataType = "String")
    @NotNull(message="CPO10002")
    private String capTyp;

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", required = true, dataType = "CorpBusTyp")
    @NotNull(message="CPO10003")
    private CorpBusTyp corpBusTyp;

    /**
     * 付款发账户编码
     */
    @ApiModelProperty(name = "acNo", value = "付款发账户编码")
    private String acNo;

    /**
     * 数币收款方地址
     */
    @ApiModelProperty(name = "address", value = "数币收款方地址")
    private String address;

    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", required = true, dataType = "CorpBusSubTyp")
    @NotNull(message="CPO10004")
    private CorpBusSubTyp corpBusSubTyp;

    /**
     * 币种
     */
    @ApiModelProperty(name = "ccy", value = "币种", required = true, dataType = "String")
    @NotNull(message="CPO10005")
    private String ccy;

    /**
     * 资金机构
     */
    @ApiModelProperty(name = "capCorg", value = "资金机构", required = true, dataType = "String")
    @NotNull(message="CPO10006")
    private String capCorg;

    /**
     * 卡种，C贷记卡，D借记卡
     */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种", required = true, dataType = "String")
    @Pattern(regexp="D|C",message="CPO10008")
    private String crdAcTyp;

    /**
     * 协议付款日
     */
    @ApiModelProperty(name = "agrPayDt", value = "协议付款日", required = false, dataType = "LocalDate")
    private LocalDate agrPayDt;

    /**
     * 申请提现金额
     */
    @ApiModelProperty(name = "wcAplAmt", value = "申请提现金额", required = true, dataType = "BigDecimal")
    @NotNull(message="CPO10010")
    @DecimalMax(value = "99999999999.99", message = "wcAplAmt muse be lower than 99999999999.99")
    @DecimalMin(value = "0", message = "wcAplAmt muse be greater than 0")
    private BigDecimal wcAplAmt;

    /**
     * 冻结编号
     */
    @ApiModelProperty(name = "holdNo", value = "冻结编号", required = true, dataType = "String")
//    @NotNull(message="CPO10011")
    private String holdNo;

    /**
     * 个企标识，B企业，C个人
     */
    @ApiModelProperty(name = "psnCrpFlg", value = "个企标识", required = true, dataType = "String")
    @Pattern(regexp="B|C",message="CPO10013")
    private String psnCrpFlg;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mblNo", value = "手机号", required = false, dataType = "String")
    private String mblNo;

    /**
     * 用户名
     */
    @ApiModelProperty(name = "userNm", value = "用户名", required = true, dataType = "String")
//    @NotNull(message="CPO10015")
    private String userNm;

    /**
     * 证件类型
     */
    @ApiModelProperty(name = "idTyp", value = "证件类型", required = false, dataType = "String")
    private String idTyp;

    /**
     * 加密证件号
     */
    @ApiModelProperty(name = "idNoEnc", value = "加密证件号", required = false, dataType = "String")
    private String idNoEnc;

    /**
     * 加密银行卡号
     */
    @ApiModelProperty(name = "crdNoEnc", value = "加密银行卡号", required = true, dataType = "String")
    @NotNull(message="CPO10019")
    private String crdNoEnc;

    /**
     * 银行卡户名
     */
    @ApiModelProperty(name = "capCrdNm", value = "银行卡户名", required = true, dataType = "String")
    @NotNull(message="CPO10020")
    private String capCrdNm;

    /**
     * 提现备注
     */
    private String wcRmk;

    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", required = true, dataType = "String")
    @NotNull(message="CPO10021")
    @Length(max = 32)
    private String reqOrdNo;

    /**
     * 请求日期
     */
    @ApiModelProperty(name = "reqOrdDt", value = "请求日期", required = false, dataType = "LocalDate")
    private LocalDate reqOrdDt;

    /**
     * 请求时间
     */
    @ApiModelProperty(name = "reqOrdTm", value = "请求时间", required = false, dataType = "LocalTime")
    private LocalTime reqOrdTm;

    /**
     * 分行名称
     */
    @ApiModelProperty(name = "subbranch", value = "分行名称", required = false, dataType = "String")
    private String subbranch;


    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }


    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }


    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public LocalDate getAgrPayDt() {
        return agrPayDt;
    }

    public void setAgrPayDt(LocalDate agrPayDt) {
        this.agrPayDt = agrPayDt;
    }

    public BigDecimal getWcAplAmt() {
        return wcAplAmt;
    }

    public void setWcAplAmt(BigDecimal wcAplAmt) {
        this.wcAplAmt = wcAplAmt;
    }

    public String getHoldNo() {
        return holdNo;
    }

    public void setHoldNo(String holdNo) {
        this.holdNo = holdNo;
    }

    public String getPsnCrpFlg() {
        return psnCrpFlg;
    }

    public void setPsnCrpFlg(String psnCrpFlg) {
        this.psnCrpFlg = psnCrpFlg;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNoEnc() {
        return idNoEnc;
    }

    public void setIdNoEnc(String idNoEnc) {
        this.idNoEnc = idNoEnc;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getCapCrdNm() {
        return capCrdNm;
    }

    public void setCapCrdNm(String capCrdNm) {
        this.capCrdNm = capCrdNm;
    }

    public String getWcRmk() {
        return wcRmk;
    }

    public void setWcRmk(String wcRmk) {
        this.wcRmk = wcRmk;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getUserNm() {
        return userNm;
    }

    public void setUserNm(String userNm) {
        this.userNm = userNm;
    }

    public String getSubbranch() {
        return subbranch;
    }

    public void setSubbranch(String subbranch) {
        this.subbranch = subbranch;
    }

}
