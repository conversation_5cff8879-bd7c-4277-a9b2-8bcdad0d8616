package com.hisun.lemon.cpo.common;

/**
 * 常量参数类
 */
public class CpoConstants {
    /**
     * 短信下发方式
     * 0：平台下发平台校验
     * 1：银行下发平台校验
     * 2，银行下发银行校验
     */
    public static final String PLAT_SMS_SEND_PLAT_CHECK = "0";
    public static final String BANK_SMS_SEND_PLAT_CHECK = "1";
    public static final String BANK_SMS_SEND_BANK_CHECK = "2";

    /**
     * 订单状态
     * W1: 等待充值或者等待退款
     * W2: 充值银行处理中或者退款处理中
     * W3: 提现申请(提现待审核)
     * A1: 提现待复核
     * W4: 提现银行处理中
     * S1: 订单处理成功
     * F1: 订单处理失败
     * R1: 部分退款
     * R2: 全额退款
     */
    public static final String ORD_WATING_PAY = "W1";
    public static final String ORD_PAY_BANKING = "W2";
    public static final String ORD_WAITING_WITHDRAW = "W3";
    public static final String WAIT_REVIEW = "A1";
    public static final String ORD_WAITING_BANKING = "W4";
    public static final String ORD_SUCCESS = "S1";
    public static final String ORD_FAIL = "F1";
    public static final String ORD_WATING_REFUND = "W1";
    public static final String ORD_REFUND_BANKING = "W2";
    public static final String ORD_REFUND_PART = "R1";
    public static final String ORD_REFUND_ALL = "R2";

    /**
     * 订单审批状态
     * P1: 等待审批
     * E1: 审核通过
     */
    public static final String ORD_APPROVAL_WAITING = "P1";
    public static final String ORD_APPROVAL_SUCCESS = "E1";

    /**
     * 资金或者路径合作机构
     * ICBC:工行
     */
    public static final String ICBC = "ICBC";

    /**
     * 银行返回结果
     * S: 成功
     * W: 等待，银行处理中
     * F: 失败
     */
    public static final String BANK_RSP_SUC = "S";
    public static final String BANK_RSP_WAIT = "W";
    public static final String BANK_RSP_FAIL = "F";

    /**
     * 卡种类型
     * DEBIT_CARD：借记卡
     * CREDIT_CARD：贷记卡，信用卡
     */
    public static final String DEBIT_CARD = "D";
    public static final String CREDIT_CARD = "C";

    /**
     * 对账批次锁状态，U-未加锁，L-已加锁
     */
    public static final String LOCK_STATUS_UNLOCK ="U";
    public static final String LOCK_STATUS_LOCKED ="L";

    /**
     * 对账锁前缀
     */
    public static final String CHK_LOCK_PREFIX = "CHK_";

    /**
     * 对账批次的对账状态
     * 0: 未对账
     * 1: 对账文件已下载
     * 2: 对账文件明细已入库
     * 3: 已对账
     * 4: 对账结束
     *
     */
    public static final String CHK_FIL_NOT_START = "0";
    public static final String CHK_FIL_DOWNLOAD = "1";
    public static final String CHK_FIL_IMPORT = "2";
    public static final String CHK_FIL_FINISHED = "4";

    /**
     * 子订单的对账状态
     * 0: 未对账
     * 1: 对账成功
     * 2: 我方有机构无
     * 3: 机构有我方无
     * 4: 金额错误
     * 5: 存疑
     */
    public static final String CHK_STS_NOT_START = "0";
    public static final String CHK_STS_SUCCESS = "1";
    public static final String CHK_STS_PLAT_EXIST = "2";
    public static final String CHK_STS_ORG_EXIST = "3";
    public static final String CHK_STS_AMT_ERROR = "4";
    public static final String CHK_STS_DOUBT = "5";

    /**
     * 差错类型
     * 2：短款差错
     * 3：长款差错
     * 4：金额错误
     */
    public static final String CHK_ERR_TYP_SHORT = "2";
    public static final String CHK_ERR_TYP_LONG = "3";
    public static final String CHK_ERR_TYP_AMTERR = "4";

    /**
     * 提现对账文件本地存放路径
     */
    public static final String ICBC_WITHDRAW_CHK_PATH = "E:\\chkfile";

    /**
     * 对账文件内容分隔符
     */
    public final static String FILE_SPLITSTR = "\\|";

    /**
     * 定时任务-提现订单结果通知
     * maxPageNum: 分页查询最大页数
     * maxQueryNum: 每页最大笔数
     */
    public final static int maxPageNum = 20;
    public final static int maxQueryNum = 500;

    /**
     * 个人/商户标识
     * C：client 个人(对私)
     * B：business 商户(对公)
     */
    public final static String PSN_ORG_FLG_PERSON = "C";
    public final static String PSN_ORG_FLG_ORGANIZATION = "B";

    /**
     * 交易类型
     * 04: 提现
     */
    public static final String TX_TYP_WITHDRAW = "04";

    /**
     * 通知状态，
     * W：待通知，
     * S：通知成功，
     * F：通知失败（可重发），
     * E：通知失败（不可重发）
     */
    public static final String NOTIFY_STATUS_WAITING = "W";
    public static final String NOTIFY_STATUS_SUCCESS = "S";
    public static final String NOTIFY_STATUS_FAILURE = "F";
    public static final String NOTIFY_STATUS_END = "E";

    /**
     * 加解密类型 encrypt:加密  decrypt:解密
     */
    public static final String ENCRYPT = "encrypt";
    public static final String DECRYPT = "decrypt";
}
