package com.hisun.lemon.cpo.enums;

/**
 * Created by <PERSON><PERSON> on 2017/7/5.
 */
public enum  CorpBusSubTyp {

    /**
     * 快捷预签约
     */
    PREFAST_SIGN("0101"),

    /**
     * 快捷签约
     */
    FAST_SIGN("0102"),

    /**
     * 快捷解约
     */
    FAST_UNSIGN("0103"),

    /**
     * 提现签约
     */
    WITHDRAW_SIGN("0104"),

    /**
     * 提现解约
     */
    WITHDRAW_UNSIGN("0105"),

    /**
     * 快捷支付
     */
    FASTPAY("0201"),

    /**
     * 快捷消费
     */
    FASTPAY_CONSUME("0202"),

    /**
     * 网银支付
     */
    EBANKPAY("0301"),

    /**
     * 网银消费
     */
    EBANKPAY_CONSUME("0302"),

    /**
     * 商户扫码收款
     */
    EBANKPAY_MERCSCAN("0303"),

    /**
     * 用户扫码付款
     */
    EBANKPAY_USERSCAN("0304"),

    /**
     * 汇款充值
     */
    REMITTANCE("0401"),

    /**
     * 营业厅充值
     */
    BUSSINESS_REMITTANCE("0402"),

    /**
     * 快捷退款
     */
    FAST_REFUND("0501"),

    /**
     * 网银退款
     */
    EBANK_REFDND("0502"),

    /**
     * 汇款退款
     */
    REMITTANCE_REFDND("0503"),

    /**
     * 个人提现
     */
    PER_WITHDRAW("0601"),

    /**
     * 转账到银行卡
     */
    CARD_WITHDRAW("0602"),

    /**
     * 商户结算
     */
    MERC_WITHDRAW("0603"),

    /**
     * 个人营业厅提现
     */
    PER_HALL_WITHDRAW("0604"),

    /**
     * 商户营业厅提现
     */
    MERC_HALL_WITHDRAW("0605"),

    /**
     * 数币提现
     */
    DM_WITHDRAW("DX01");

    CorpBusSubTyp(String type) {
        this.type = type;
    }

    // 成员变量
    private String type;

    public String getType() {
        return type;
    }
}
