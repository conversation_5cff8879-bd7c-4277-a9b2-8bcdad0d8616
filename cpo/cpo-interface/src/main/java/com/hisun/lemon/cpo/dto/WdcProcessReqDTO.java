package com.hisun.lemon.cpo.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * 确认付款成功，或退回，返回处理理由
 */
public class WdcProcessReqDTO {
    /**
     * 订单号
     */
    @ApiModelProperty(name = "wcOrdNo", value = "提现订单号", required = true, dataType = "String")
    @NotNull(message="CPO10024")
    private String wcOrdNo;

    /**
     * 订单状态
     */
    @ApiModelProperty(name = "ordSts", value = "提现订单状态", required = true, dataType = "String")
    @NotNull(message="CPO10025")
    private String ordSts;

    /**
     * 订单处理原因
     */
    @ApiModelProperty(name = "reason", value = "订单处理原因", required = false, dataType = "String")
    private String reason;

    /**
     * 处理人id
     * @return
     */
    @ApiModelProperty(name = "userId", value = "处理人id", required = false, dataType = "String")
    private String userId;

    public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

    public String getWcOrdNo() {
        return wcOrdNo;
    }

    public void setWcOrdNo(String wcOrdNo) {
        this.wcOrdNo = wcOrdNo;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

}
