package com.hisun.lemon.cpo.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 合作路由信息，以及支持的资金机构信息
 */
public class RouteRspDTO {

    private List<RouteDTO> list;

    public List<RouteDTO> getList() {
        return list;
    }

    public void setList(List<RouteDTO> list) {
        this.list = list;
    }

    public static class RouteDTO implements Serializable {

        /**
         * 资金机构
         */
        @ApiModelProperty(name = "crdCorpOrg", value = "资金机构", dataType = "String")
        private String crdCorpOrg;

        /**
         * 合作资金机构名称
         */
        @ApiModelProperty(name = "corpOrgNm", value = "合作资金机构名称", dataType = "String")
        private String corpOrgNm;

        /**
         * 合作资金机构名简称
         */
        @ApiModelProperty(name = "corpOrgSnm", value = "合作资金机构名简称", dataType = "String")
        private String corpOrgSnm;

        /**
         * 路径机构
         */
        @ApiModelProperty(name = "rutCorpOrg", value = "路径机构", dataType = "String")
        private String rutCorpOrg;

        /**
         * 卡种
         */
        @ApiModelProperty(name = "crdAcTyp", value = "卡种，D借记卡，C贷记卡", dataType = "String")
        private String crdAcTyp;

        public String getCrdCorpOrg() {
            return crdCorpOrg;
        }

        public void setCrdCorpOrg(String crdCorpOrg) {
            this.crdCorpOrg = crdCorpOrg;
        }

        public String getRutCorpOrg() {
            return rutCorpOrg;
        }

        public void setRutCorpOrg(String rutCorpOrg) {
            this.rutCorpOrg = rutCorpOrg;
        }

        public String getCrdAcTyp() {
            return crdAcTyp;
        }

        public void setCrdAcTyp(String crdAcTyp) {
            this.crdAcTyp = crdAcTyp;
        }

        public String getCorpOrgNm() {
            return corpOrgNm;
        }

        public void setCorpOrgNm(String corpOrgNm) {
            this.corpOrgNm = corpOrgNm;
        }

        public String getCorpOrgSnm() {
            return corpOrgSnm;
        }

        public void setCorpOrgSnm(String corpOrgSnm) {
            this.corpOrgSnm = corpOrgSnm;
        }
    }

}
