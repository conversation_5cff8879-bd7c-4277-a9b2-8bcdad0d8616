package com.hisun.lemon.cpo.client;

import com.hisun.lemon.cpo.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * Created by gongle<PERSON> on 2017/7/7.
 */
@FeignClient("CPO")
public interface WithdrawClient {

    /**
     * 申请提现订单
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpo/orders/payment")
    GenericRspDTO<WithdrawResDTO> createOrder(@RequestBody GenericDTO<WithdrawReqDTO> genericDTO);

    /**
     * 查询提现订单
     * @param ordNo
     * @return
     */
    @GetMapping("/cpo/orders/detail")
    GenericRspDTO<WdcOrdDetailResDTO> queryOrdero(@RequestParam(value = "ordNo") String ordNo);

    /**
     * 付款确认结果
     * @param genericDTO
     * @return
     */
    @PutMapping("/cpo/orders/result")
    GenericRspDTO<NoBody> payOrder(@RequestBody GenericDTO<WdcProcessReqDTO> genericDTO);

    /**
     * 登记营业厅提现订单
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpo/orders/hall/payment")
    GenericRspDTO<WithdrawResDTO> createHallOrder(@RequestBody GenericDTO<WithdrawHallReqDTO> genericDTO);
}
