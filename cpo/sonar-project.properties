# must be unique in a given SonarQube instance
sonar.projectKey=seatel:cpo
# this is the name and version displayed in the SonarQube UI. Was mandatory prior to SonarQube 6.1.
sonar.projectName=seatel:cpo
sonar.projectVersion=1.0

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
# This property is optional if sonar.modules is set.
sonar.sources=.

# Encoding of the source code. Default is default system encoding
sonar.sourceEncoding=UTF-8

#class
sonar.java.binaries=*/build/classes

# for login
sonar.login=admin
sonar.password=Hisunpay2017