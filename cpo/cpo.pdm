<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ID="{62E99863-569E-4158-A033-0F9A8B55BFF7}" Label="" LastModificationDate="1507772584" Name="cpo" Objects="398" Symbols="3" Target="MySQL 5.0" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="16.5.0.3982"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>62E99863-569E-4158-A033-0F9A8B55BFF7</a:ObjectID>
<a:Name>cpo</a:Name>
<a:Code>cpo</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas.sql
GenScriptName0=crebas
GenScriptName1=crebas.sql
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=D:\Program Files (x86)\Sybase\PowerDesigner 16\
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=No
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=No
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
EstimationYears=0
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
UseTerm=No
EnableRequirements=No
EnableFullShortcut=Yes
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=1
RefrUpdateConstraint=1
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Xsm]
GenRootElement=Yes
GenComplexType=No
GenAttribute=Yes
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>C9A13EB9-71D5-4865-8782-2DD9A08AD950</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1499241649</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241649</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>038E9715-91C4-43B5-B62A-0DFC7F60A7D7</a:ObjectID>
<a:Name>cpo</a:Name>
<a:Code>cpo</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Show Links intersections=Yes
Activate automatic link routing=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Show Icon=No
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;#$%&amp;&#39;)*+,-./:;=&gt;?@\]^_`|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject.TextStyle=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.TextStyle=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Package.Stereotype=Yes
Package.Comment=No
Package.IconPicture=No
Package.TextStyle=No
Package_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table.TextStyle=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;\&amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;\&amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View.TextStyle=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure.TextStyle=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:TableSymbol Id="o5">
<a:CreationDate>1499753577</a:CreationDate>
<a:ModificationDate>1499753797</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-6039,-3186), (6036,3188))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o6"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o7">
<a:CreationDate>1499823348</a:CreationDate>
<a:ModificationDate>1499823368</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-7198,-17907), (7196,-2459))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o8"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o9">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>1500541930</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-16860,4320), (3060,26058))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o10"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o11">
<a:ObjectID>E7412653-C95E-4927-90C8-40DD142A1280</a:ObjectID>
<a:Name>cpo_acc_control</a:Name>
<a:Code>cpo_acc_control</a:Code>
<a:CreationDate>1497700031</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499677242</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>资金模块对账总控表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o12">
<a:ObjectID>18E6C14A-8C92-4B17-97D2-976E5B60D439</a:ObjectID>
<a:Name>CHK_BAT_NO</a:Name>
<a:Code>CHK_BAT_NO</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224227</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>批次号</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o13">
<a:ObjectID>B6627087-959B-41FF-85CB-919D6A25ABFA</a:ObjectID>
<a:Name>RUT_CORG</a:Name>
<a:Code>RUT_CORG</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路径合作机构</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o14">
<a:ObjectID>3BD12E61-8AB2-4678-B9AC-624C013885F6</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500002482</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>业务类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o15">
<a:ObjectID>98348A1D-CE71-4781-AA43-7156F85EC639</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1497703549</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500002482</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>业务子类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(4)</a:DataType>
<a:Length>4</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o16">
<a:ObjectID>F7A2C18D-3EDB-4CE2-97A1-35AC0402DE6C</a:ObjectID>
<a:Name>CHK_FIL_DT</a:Name>
<a:Code>CHK_FIL_DT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499738609</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账文件日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o17">
<a:ObjectID>96CA04B2-3F47-4EED-9DF0-5DA2722315B3</a:ObjectID>
<a:Name>CHK_FIL_NM</a:Name>
<a:Code>CHK_FIL_NM</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499743643</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账文件名</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o18">
<a:ObjectID>920FA6F9-E22E-4A32-8D8C-E5C8C009D7CD</a:ObjectID>
<a:Name>CHK_FIL_STS</a:Name>
<a:Code>CHK_FIL_STS</a:Code>
<a:CreationDate>1497700225</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账状态,0未对账，1文件已取，2文件入库，3已对账，4对账完成</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o19">
<a:ObjectID>DBAC8AFD-D22A-4262-A03C-B5EB0BE936C5</a:ObjectID>
<a:Name>FILE_RCV_DT</a:Name>
<a:Code>FILE_RCV_DT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499738609</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>获取对账文件日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o20">
<a:ObjectID>3AC0D063-F87E-4333-BC39-D57CBDDE9685</a:ObjectID>
<a:Name>CHK_BEG_TM</a:Name>
<a:Code>CHK_BEG_TM</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499667816</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账开始时间</a:Comment>
<a:DataType>time</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o21">
<a:ObjectID>E16DAB12-D7E1-4466-88BD-44CD00EF7E47</a:ObjectID>
<a:Name>CHK_END_TM</a:Name>
<a:Code>CHK_END_TM</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499667820</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账结束时间</a:Comment>
<a:DataType>time</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o22">
<a:ObjectID>143E3D16-4B88-41AB-9E20-B41791A5B150</a:ObjectID>
<a:Name>CHK_DT</a:Name>
<a:Code>CHK_DT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499738609</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o23">
<a:ObjectID>78FF85D8-52CE-46B5-BD40-7FCB9D82B5D3</a:ObjectID>
<a:Name>FIL_TOT_AMT</a:Name>
<a:Code>FIL_TOT_AMT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账总金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o24">
<a:ObjectID>86986C07-0B3B-4CE9-AE26-81925AB68225</a:ObjectID>
<a:Name>FIL_TOT_CNT</a:Name>
<a:Code>FIL_TOT_CNT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账总笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o25">
<a:ObjectID>9FBDE292-F861-46B4-B118-00B659D4D260</a:ObjectID>
<a:Name>TOT_MCH_AMT</a:Name>
<a:Code>TOT_MCH_AMT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对平总金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o26">
<a:ObjectID>213A8D8B-63FC-427E-ACC1-A3C5D44F24D2</a:ObjectID>
<a:Name>TOT_MCH_CNT</a:Name>
<a:Code>TOT_MCH_CNT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对平总笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o27">
<a:ObjectID>8E76B206-DBC4-4A0D-8A2B-B98D3AACC032</a:ObjectID>
<a:Name>ERR_TOT_CNT</a:Name>
<a:Code>ERR_TOT_CNT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>差错笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o28">
<a:ObjectID>4B1B6761-5596-4E39-87BE-808962A9B3A6</a:ObjectID>
<a:Name>ERR_TOT_AMT</a:Name>
<a:Code>ERR_TOT_AMT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>差错金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o29">
<a:ObjectID>8A9DF2E4-1B78-4BD8-8A88-B0A3FB6F0EEE</a:ObjectID>
<a:Name>LONG_AMT</a:Name>
<a:Code>LONG_AMT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>长款金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o30">
<a:ObjectID>E128DFBE-DF0D-4F87-85E2-D429BC832092</a:ObjectID>
<a:Name>LONG_CNT</a:Name>
<a:Code>LONG_CNT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>长款笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o31">
<a:ObjectID>590CDC92-022D-437B-B678-39DD2B78E926</a:ObjectID>
<a:Name>SHORT_AMT</a:Name>
<a:Code>SHORT_AMT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>短款金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o32">
<a:ObjectID>6008A386-F8FA-40BE-A1BD-C20F600BA48F</a:ObjectID>
<a:Name>SHORT_CNT</a:Name>
<a:Code>SHORT_CNT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>短款笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o33">
<a:ObjectID>8F9BC53D-5F89-4B06-8170-D5CA5911D053</a:ObjectID>
<a:Name>DOUBT_AMT</a:Name>
<a:Code>DOUBT_AMT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>存疑金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o34">
<a:ObjectID>D4DA1917-5F00-4745-8B86-DF296FF5A2F9</a:ObjectID>
<a:Name>DOUBT_CNT</a:Name>
<a:Code>DOUBT_CNT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>存疑笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o35">
<a:ObjectID>B896E208-AC4E-4DA8-B449-0C093EA10458</a:ObjectID>
<a:Name>DBT_ERR_AMT</a:Name>
<a:Code>DBT_ERR_AMT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>存疑转差错金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o36">
<a:ObjectID>8B52CF48-2848-4C85-8ADC-6B3B7298037C</a:ObjectID>
<a:Name>DBT_ERR_CNT</a:Name>
<a:Code>DBT_ERR_CNT</a:Code>
<a:CreationDate>1497700033</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>存疑转差错笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o37">
<a:ObjectID>3B98902D-9D86-4682-84EA-C372794A4A5E</a:ObjectID>
<a:Name>TOT_DR_AMT</a:Name>
<a:Code>TOT_DR_AMT</a:Code>
<a:CreationDate>1497700225</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>借方总金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o38">
<a:ObjectID>2BEFA0E9-E9D0-4E04-A2D9-4C9B733880E3</a:ObjectID>
<a:Name>TOT_DR_NUM</a:Name>
<a:Code>TOT_DR_NUM</a:Code>
<a:CreationDate>1497700225</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>借方总笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o39">
<a:ObjectID>5370910B-5A4D-4B92-86CF-B487B2043673</a:ObjectID>
<a:Name>TOT_CR_AMT</a:Name>
<a:Code>TOT_CR_AMT</a:Code>
<a:CreationDate>1497700225</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>贷方总金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o40">
<a:ObjectID>30865295-9897-47DD-97D9-AC6A9E92164E</a:ObjectID>
<a:Name>TOT_CR_NUM</a:Name>
<a:Code>TOT_CR_NUM</a:Code>
<a:CreationDate>1497700225</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>贷方总笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o41">
<a:ObjectID>72D19484-5AA1-4F20-9D90-917411FD498E</a:ObjectID>
<a:Name>PAY_TOT_AMT</a:Name>
<a:Code>PAY_TOT_AMT</a:Code>
<a:CreationDate>1497700225</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>应付总金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o42">
<a:ObjectID>034FBEF5-F548-4F5C-ACB0-D6C2DF691302</a:ObjectID>
<a:Name>RCV_TOT_AMT</a:Name>
<a:Code>RCV_TOT_AMT</a:Code>
<a:CreationDate>1497700225</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>应收总金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o43">
<a:ObjectID>F900DF10-F379-4EF8-A3F2-0DBD4E778AB6</a:ObjectID>
<a:Name>RMK</a:Name>
<a:Code>RMK</a:Code>
<a:CreationDate>1498532173</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499742238</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>备注</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o44">
<a:ObjectID>546BAC8C-DAB2-4996-9CA5-593B3DBA9160</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o45">
<a:ObjectID>A3AD0F83-89C9-4645-B294-260CC629EF16</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o46">
<a:ObjectID>72FA0B04-7576-46AA-99F8-65648661F613</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o47">
<a:ObjectID>4A1FE906-D0D2-451D-B604-64EA37AAB662</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497703801</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o12"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o48">
<a:ObjectID>CDD7D081-FB6C-4B6C-B6A6-2ADFF9673A30</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1498728047</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o49">
<a:ObjectID>F657D21A-EB85-42A2-A585-D623FF5F14CE</a:ObjectID>
<a:CreationDate>1498728070</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o13"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o50">
<a:ObjectID>93845359-9982-4FCD-9DD7-45582C4E0ED7</a:ObjectID>
<a:CreationDate>1498728070</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o14"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o51">
<a:ObjectID>6EE75CBC-7FF7-46E8-B183-0B6F1B6C0157</a:ObjectID>
<a:CreationDate>1498728070</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o15"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o52">
<a:ObjectID>D5A558B8-CC66-4DF0-995F-D1918BF4A995</a:ObjectID>
<a:CreationDate>1498728070</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o16"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o47"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o53">
<a:ObjectID>1FF1495E-18F2-49E4-A377-420BD5D09650</a:ObjectID>
<a:Name>cpo_acc_error</a:Name>
<a:Code>cpo_acc_error</a:Code>
<a:CreationDate>1497701698</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499677246</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账差错表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o54">
<a:ObjectID>356DAFA6-6CEB-4A7D-9178-B2695703B62C</a:ObjectID>
<a:Name>CHK_ER_ID</a:Name>
<a:Code>CHK_ER_ID</a:Code>
<a:CreationDate>1498117507</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224260</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>99622E8B-BD68-4479-A648-32EBC6301233</a:ObjectID>
<a:Name>RUT_CORG</a:Name>
<a:Code>RUT_CORG</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路径合作机构</a:Comment>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o56">
<a:ObjectID>AA47D071-7B86-4F5F-8D20-5D659FC9BB18</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500002492</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>业务合作类型</a:Comment>
<a:DataType>varchar(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o57">
<a:ObjectID>5375188D-E533-4ED3-8DD7-A85CCA41F864</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500002492</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>业务合作子类型</a:Comment>
<a:DataType>varchar(4)</a:DataType>
<a:Length>4</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>619BB6E7-883A-4398-8302-0EA27955F524</a:ObjectID>
<a:Name>ERR_KEY_ID</a:Name>
<a:Code>ERR_KEY_ID</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>差错键值</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>A9F75710-5CDF-4F61-8AD9-B64E68BE7C36</a:ObjectID>
<a:Name>CHK_ERR_DT</a:Name>
<a:Code>CHK_ERR_DT</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499667879</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>差错创建日期</a:Comment>
<a:DataType>date</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>829B1D0B-6950-4E45-9CDC-DF34385CD544</a:ObjectID>
<a:Name>CHK_ERR_TM</a:Name>
<a:Code>CHK_ERR_TM</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499667882</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>差错创建时间</a:Comment>
<a:DataType>time</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>6B42AF90-24B9-471C-AE3D-2D249D782A79</a:ObjectID>
<a:Name>CHK_BAT_NO</a:Name>
<a:Code>CHK_BAT_NO</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224260</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账批次</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>9A9FF711-BC44-4F32-8328-52EFF03E46E3</a:ObjectID>
<a:Name>SPL_ABLE_FLG</a:Name>
<a:Code>SPL_ABLE_FLG</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>补单允许标识，Y允许，N不允许</a:Comment>
<a:DefaultValue>N</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>F7145632-FC20-451F-BDC2-B9FB02B85C0B</a:ObjectID>
<a:Name>CAN_ABLE_FLG</a:Name>
<a:Code>CAN_ABLE_FLG</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>撤单允许标识，Y允许，N不允许</a:Comment>
<a:DefaultValue>N</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>CBF69504-B632-4796-96DA-6A558A6B4AFA</a:ObjectID>
<a:Name>CHK_ERR_TYP</a:Name>
<a:Code>CHK_ERR_TYP</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>差错类型，2：短款差错，3：长款差错，4：金额不符
</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>BD72DD6E-CEF6-4741-8841-0F82E0F0FE3B</a:ObjectID>
<a:Name>ERR_STS</a:Name>
<a:Code>ERR_STS</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>差错状态，0：待处理，1：已补单，2：已撤单，3：人工取消
</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>FFEB8D4C-2E55-4697-BDA9-349476EC2076</a:ObjectID>
<a:Name>OTH_TX_AMT</a:Name>
<a:Code>OTH_TX_AMT</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对方交易金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>1ABEB130-A81A-42F6-86C3-2066C37A76F8</a:ObjectID>
<a:Name>MY_TX_AMT</a:Name>
<a:Code>MY_TX_AMT</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>我方交易金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>EDDF2AA4-25B0-406B-9B04-AF5AC829CE2D</a:ObjectID>
<a:Name>PSN_CRP_FLG</a:Name>
<a:Code>PSN_CRP_FLG</a:Code>
<a:CreationDate>1497702653</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>个人/商户标识，B商户，C个人</a:Comment>
<a:DefaultValue>C</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>C3DE7069-86E7-4116-BE75-4395A6033278</a:ObjectID>
<a:Name>OLD_TX_DT</a:Name>
<a:Code>OLD_TX_DT</a:Code>
<a:CreationDate>1497702900</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500106100</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>原交易日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>D1D0254C-13C1-4EF0-B8E8-3988F3CC89C8</a:ObjectID>
<a:Name>OLD_JRN_NO</a:Name>
<a:Code>OLD_JRN_NO</a:Code>
<a:CreationDate>1497702918</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224260</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>原交易流水号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o71">
<a:ObjectID>17AABD7C-F364-49C7-A982-3E3B77AD364A</a:ObjectID>
<a:Name>OLD_ORD_NO</a:Name>
<a:Code>OLD_ORD_NO</a:Code>
<a:CreationDate>1497702918</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224260</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>原交易订单号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o72">
<a:ObjectID>2270A721-D5DD-40AC-A2D3-E701D9235AA9</a:ObjectID>
<a:Name>OLD_CORG_KEY</a:Name>
<a:Code>OLD_CORG_KEY</a:Code>
<a:CreationDate>1497702918</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224260</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>原路径合作机构对账主键</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o73">
<a:ObjectID>F43BD450-DE0F-47F9-8544-2F719854CEBF</a:ObjectID>
<a:Name>OPR_ID</a:Name>
<a:Code>OPR_ID</a:Code>
<a:CreationDate>1497702918</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>操作员</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>04A2E718-4E2F-4499-AA20-E711AE1438F9</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>7A9102AC-7B33-44AF-9C38-03E4E5ADA438</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>03F47757-C7CF-40E0-9A61-F134D223492D</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o77">
<a:ObjectID>97C2815F-3EBA-471F-8EC3-1946120374C8</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497705183</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o54"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o78">
<a:ObjectID>254FA43C-B088-401E-988D-F8332D6D62B4</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1498117546</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o79">
<a:ObjectID>97B34EC2-202A-433C-81C5-5814B976B71B</a:ObjectID>
<a:CreationDate>1498118398</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o55"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o80">
<a:ObjectID>AEC8E4F0-9D3C-443B-9326-4A9A542E673D</a:ObjectID>
<a:CreationDate>1498118398</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o56"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o81">
<a:ObjectID>41946B2F-5C51-445A-8345-A353EBFBDFE7</a:ObjectID>
<a:CreationDate>1498118398</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o57"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o82">
<a:ObjectID>CFAA826C-1CB8-410D-B0DD-76C6E1396082</a:ObjectID>
<a:CreationDate>1498118398</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o58"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o83">
<a:ObjectID>1B81C0D1-94C7-4118-9BE8-16B7B83901DB</a:ObjectID>
<a:Name>Index_2</a:Name>
<a:Code>Index_2</a:Code>
<a:CreationDate>1498728110</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o84">
<a:ObjectID>00FF5945-C2C6-4CA6-9186-3BC19538BA40</a:ObjectID>
<a:CreationDate>1498728152</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o61"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o77"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o85">
<a:ObjectID>3E09F1B0-6F52-45B7-BD89-9A4CF78E34C0</a:ObjectID>
<a:Name>cpo_acc_param</a:Name>
<a:Code>cpo_acc_param</a:Code>
<a:CreationDate>1497700666</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499677250</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>资金模块对账参数表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o86">
<a:ObjectID>FCB31EF7-F14F-44AE-A75C-665309025F0E</a:ObjectID>
<a:Name>CHK_PM_ID</a:Name>
<a:Code>CHK_PM_ID</a:Code>
<a:CreationDate>1498118581</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224299</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>id</a:Comment>
<a:DataType>VARCHAR(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>2132EE38-419D-4A4B-940B-AE7AA162F481</a:ObjectID>
<a:Name>RUT_CORG</a:Name>
<a:Code>RUT_CORG</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路径机构</a:Comment>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>9B6C0C14-7A72-4DEE-8407-1319A7C41AD7</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500002502</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>业务类型</a:Comment>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>23FF641F-E21E-44FB-9C14-49ED36EBA128</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500002502</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>业务子类型</a:Comment>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>6E7523DC-8077-479B-A74C-AFF06DF2CA3F</a:ObjectID>
<a:Name>EFF_FLG</a:Name>
<a:Code>EFF_FLG</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>生效标识，Y生效，N不生效</a:Comment>
<a:DefaultValue>N</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>1A76769A-A93A-4B0A-9E9F-4B0253D26FB3</a:ObjectID>
<a:Name>CHK_DO_FLG</a:Name>
<a:Code>CHK_DO_FLG</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>是否对账标识，Y对账，N不对账</a:Comment>
<a:DefaultValue>N</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>2A47D4D1-095D-4A89-B6D6-FE817B1CF647</a:ObjectID>
<a:Name>BAT_CHK_FLG</a:Name>
<a:Code>BAT_CHK_FLG</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>批量对账标识，B批量,O:实时对账</a:Comment>
<a:DefaultValue>B</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>4A19FCC3-87F5-4C23-B6F7-C82037AE2987</a:ObjectID>
<a:Name>CHK_BEG_TM</a:Name>
<a:Code>CHK_BEG_TM</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账开始时间</a:Comment>
<a:DataType>time</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o94">
<a:ObjectID>50E4B2FA-B3A6-494F-BB7C-D88AD2335BF9</a:ObjectID>
<a:Name>CHK_END_TM</a:Name>
<a:Code>CHK_END_TM</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账结束时间</a:Comment>
<a:DataType>time</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o95">
<a:ObjectID>B913CD8D-0D23-4FF9-8E65-B9EE611FAEF3</a:ObjectID>
<a:Name>SPL_ABLE_FLG</a:Name>
<a:Code>SPL_ABLE_FLG</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>补单标识，Y支持，N不支持</a:Comment>
<a:DefaultValue>N</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>228DF496-CF0B-4AC3-86C8-C3E283C584C2</a:ObjectID>
<a:Name>CAN_ABLE_FLG</a:Name>
<a:Code>CAN_ABLE_FLG</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>撤单标识，Y支持，N不支持</a:Comment>
<a:DefaultValue>N</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>4E972295-9311-474A-975F-53C523353248</a:ObjectID>
<a:Name>AU_SK_FLG</a:Name>
<a:Code>AU_SK_FLG</a:Code>
<a:CreationDate>1497700838</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>自动跳账标识，Y支持，N不支持</a:Comment>
<a:DefaultValue>N</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>01E78F09-35C5-4DFB-A55D-BC741A15BB14</a:ObjectID>
<a:Name>OPR_ID</a:Name>
<a:Code>OPR_ID</a:Code>
<a:CreationDate>1498532385</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>操作员</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>756ED53A-05B3-47F2-AA87-CC5540A8A2CF</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>3EA0B02A-47AC-4A45-9AD0-5D9BB533FC52</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>4D22B238-6BDD-47BE-BABB-206033474F47</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o102">
<a:ObjectID>6A92E305-8555-4ACF-87AE-EB9908C13C91</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497704618</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o86"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o103">
<a:ObjectID>291A86B6-4481-41A0-BD47-0B1870A31925</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1498118604</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o104">
<a:ObjectID>9642F82C-F285-4C62-891F-6635C6F886DB</a:ObjectID>
<a:CreationDate>1498118610</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o87"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o105">
<a:ObjectID>A20F1EA8-F105-4BBB-A919-2B38EDB42EDA</a:ObjectID>
<a:CreationDate>1498118610</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o88"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o106">
<a:ObjectID>F37895C9-A654-4FDC-A227-D0AE03372792</a:ObjectID>
<a:CreationDate>1498118610</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o89"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o102"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o107">
<a:ObjectID>38EF5AEF-6F5E-474E-8B6B-65C6ABBC52B7</a:ObjectID>
<a:Name>cpo_card_bin</a:Name>
<a:Code>cpo_card_bin</a:Code>
<a:CreationDate>1497706556</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>资金模块BIN表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o108">
<a:ObjectID>B4AA7853-43BB-44CB-B0E1-9837667FBE87</a:ObjectID>
<a:Name>BIN_ID</a:Name>
<a:Code>BIN_ID</a:Code>
<a:CreationDate>1498723140</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224311</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>VARCHAR(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>82711645-624F-4A8E-98E8-953FC3B9DA95</a:ObjectID>
<a:Name>CRD_BIN</a:Name>
<a:Code>CRD_BIN</a:Code>
<a:CreationDate>1497706596</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224311</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>卡BIN</a:Comment>
<a:DataType>VARCHAR(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>64C285C3-FF23-4803-A519-345A7E9CFAED</a:ObjectID>
<a:Name>CAP_CORG</a:Name>
<a:Code>CAP_CORG</a:Code>
<a:CreationDate>1497706596</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>资金机构</a:Comment>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>C88E93AF-A02D-46DA-9F1B-8A70DC89E9AE</a:ObjectID>
<a:Name>CRD_AC_TYP</a:Name>
<a:Code>CRD_AC_TYP</a:Code>
<a:CreationDate>1497706596</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>卡类型，D借记卡，C贷记卡</a:Comment>
<a:DefaultValue>D</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>0CD3658E-DE9B-4BC9-A59C-3CE6DE959353</a:ObjectID>
<a:Name>CRD_LTH</a:Name>
<a:Code>CRD_LTH</a:Code>
<a:CreationDate>1497706596</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>卡长度</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>EC98E778-AF42-4931-970B-D9C4F861FA0B</a:ObjectID>
<a:Name>OPR_ID</a:Name>
<a:Code>OPR_ID</a:Code>
<a:CreationDate>1497706657</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>操作员</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>C2564FB0-1A70-4A99-AEDF-E0ABB984BE56</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>0AB9D1FC-E637-48F0-A05E-57E9F3E31869</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>59877983-CD19-4938-80B1-6AB2DBB5E4AB</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o117">
<a:ObjectID>84BA5B78-F8E4-4466-8485-2616167075AF</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497706596</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o108"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o117"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o118">
<a:ObjectID>3CCFD628-66B2-4CCE-920B-23005811E207</a:ObjectID>
<a:Name>cpo_cop_agcy_biz</a:Name>
<a:Code>cpo_cop_agcy_biz</a:Code>
<a:CreationDate>1497515009</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作机构业务表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o119">
<a:ObjectID>AE0D25B4-6141-4071-882B-4FC465EB01CB</a:ObjectID>
<a:Name>ORG_BUS_ID</a:Name>
<a:Code>ORG_BUS_ID</a:Code>
<a:CreationDate>1498118506</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224325</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>VARCHAR(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>A1B28A93-4697-4DCA-90C8-AC2A10B77D62</a:ObjectID>
<a:Name>CORP_ORG_ID</a:Name>
<a:Code>CORP_ORG_ID</a:Code>
<a:CreationDate>1497515247</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作机构编号</a:Comment>
<a:DataType>varchar(8)</a:DataType>
<a:Length>8</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>4CEC3039-9B0F-49DB-BDAA-75BD09B71FA9</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1497515247</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作业务类型</a:Comment>
<a:DataType>char(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>87758E9E-FFEE-4B3D-9A57-CB335762BF9F</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1497515247</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作业务子类型</a:Comment>
<a:DataType>char(4)</a:DataType>
<a:Length>4</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>22F24451-946D-4AEE-9B44-72A714375D8A</a:ObjectID>
<a:Name>BUS_EFF_FLG</a:Name>
<a:Code>BUS_EFF_FLG</a:Code>
<a:CreationDate>1497515247</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>业务生效标志，0失效，1生效</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>44E30175-226F-41B8-B106-EE9AD28879B5</a:ObjectID>
<a:Name>CRE_OPR_ID</a:Name>
<a:Code>CRE_OPR_ID</a:Code>
<a:CreationDate>1497515247</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224325</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>DF52F9AE-02D0-4E7B-868E-B67A681B357C</a:ObjectID>
<a:Name>UPD_OPR_ID</a:Name>
<a:Code>UPD_OPR_ID</a:Code>
<a:CreationDate>1497515522</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改人ID</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>55DC54E8-FD42-48C0-8F00-C97B6E96DB96</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507623831</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>D5236B97-C383-4367-98FC-B5432B34E794</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507623831</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>2F613437-4294-4063-9825-D8532CBC52E4</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507623831</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o129">
<a:ObjectID>3FAD2F68-0C7C-449C-833C-C24DF14328D4</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497515247</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o119"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o130">
<a:ObjectID>7D613424-94AC-4963-821B-7D5E5B2C663F</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1498118547</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o131">
<a:ObjectID>EE711B40-3121-4821-96FC-D97A7E9B349E</a:ObjectID>
<a:CreationDate>1498118552</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o120"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o132">
<a:ObjectID>11903477-66E0-49D0-A939-A55BA15A2FBE</a:ObjectID>
<a:CreationDate>1498118552</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o121"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o133">
<a:ObjectID>C42DDC81-F629-4EBF-A255-5CEDFB61B37F</a:ObjectID>
<a:CreationDate>1498118552</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o122"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o129"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o134">
<a:ObjectID>F74B184C-E4D3-4D8B-858A-497532D8112F</a:ObjectID>
<a:Name>cpo_cop_agcy_info</a:Name>
<a:Code>cpo_cop_agcy_info</a:Code>
<a:CreationDate>1497510684</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507520457</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>合作机构基本信息表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o135">
<a:ObjectID>B99280ED-F112-4D65-9FD4-65B845A5EE67</a:ObjectID>
<a:Name>ORG_INF_ID</a:Name>
<a:Code>ORG_INF_ID</a:Code>
<a:CreationDate>1498118685</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224350</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>3EE76F44-134D-4C46-8FA9-18BDB06A15D9</a:ObjectID>
<a:Name>CORP_ORG_ID</a:Name>
<a:Code>CORP_ORG_ID</a:Code>
<a:CreationDate>1497511355</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作机构编号</a:Comment>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o137">
<a:ObjectID>A7BE7528-AF68-41F5-8BB3-E5418E3C01BE</a:ObjectID>
<a:Name>CORP_ORG_NM</a:Name>
<a:Code>CORP_ORG_NM</a:Code>
<a:CreationDate>1497511355</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224350</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>合作机构名称</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>9062AFF5-A52A-4CAB-83C9-4605015BDF05</a:ObjectID>
<a:Name>CORP_ORG_SNM</a:Name>
<a:Code>CORP_ORG_SNM</a:Code>
<a:CreationDate>1497511355</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224350</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>合作机构名简称</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o139">
<a:ObjectID>15898821-6A45-4A76-97B3-B1A29F570541</a:ObjectID>
<a:Name>CORP_ORG_TYP</a:Name>
<a:Code>CORP_ORG_TYP</a:Code>
<a:CreationDate>1497511355</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作机构类型，0银行，1非银行</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o140">
<a:ObjectID>E2AA659C-B1B5-49D8-BC4F-2F5EE22F565D</a:ObjectID>
<a:Name>CRE_OPR_ID</a:Name>
<a:Code>CRE_OPR_ID</a:Code>
<a:CreationDate>1497511355</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建柜员ID</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o141">
<a:ObjectID>9E4642DB-28C5-49DD-8C57-020C5650E345</a:ObjectID>
<a:Name>UPD_OPR_ID</a:Name>
<a:Code>UPD_OPR_ID</a:Code>
<a:CreationDate>1497511355</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改柜员ID</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o142">
<a:ObjectID>2D1EA8EB-0B52-4FB9-847D-13D641B7581A</a:ObjectID>
<a:Name>CORP_ACC_NM</a:Name>
<a:Code>CORP_ACC_NM</a:Code>
<a:CreationDate>1502415027</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1507520457</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>合作机构账户名</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>7B56E533-1DD5-4DAC-AD84-F5BB4951398B</a:ObjectID>
<a:Name>CORP_ACC_NO</a:Name>
<a:Code>CORP_ACC_NO</a:Code>
<a:CreationDate>1502415027</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1507520457</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>合作机构卡号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>E0C00028-4AE2-42F2-938F-72E1FCFDF8CC</a:ObjectID>
<a:Name>RMK</a:Name>
<a:Code>RMK</a:Code>
<a:CreationDate>1502415027</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1507520457</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>备注</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>EAEC4499-9CF0-4365-9E70-309B300E7476</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507623129</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>0D1DBD8B-2230-4D53-BB41-E8DC6815E009</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507623129</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>94719393-E0A2-4EFA-985A-F9DC7A206DEC</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507623129</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o148">
<a:ObjectID>EEE801C1-2CE7-4660-8BE9-B5BA7AA60191</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497511689</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o135"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o149">
<a:ObjectID>F0D45D32-C3BE-4D60-8469-8F76F2CABFA2</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1498118704</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o150">
<a:ObjectID>2E667D64-D52A-49F0-A6A4-0351D426AA9A</a:ObjectID>
<a:CreationDate>1498118712</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o136"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o148"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o151">
<a:ObjectID>A7F4AA21-2F79-4697-87BB-83E38F0ECFB2</a:ObjectID>
<a:Name>cpo_cop_biz_route</a:Name>
<a:Code>cpo_cop_biz_route</a:Code>
<a:CreationDate>1497516105</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>业务合作路由表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o152">
<a:ObjectID>2D7F64B5-87E7-4BD3-8C16-DB6123BD3582</a:ObjectID>
<a:Name>RUT_INF_ID</a:Name>
<a:Code>RUT_INF_ID</a:Code>
<a:CreationDate>1498118734</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224363</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>122FFCEF-87FB-40C8-9407-E09B6D17DE2A</a:ObjectID>
<a:Name>CRD_CORP_ORG</a:Name>
<a:Code>CRD_CORP_ORG</a:Code>
<a:CreationDate>1497516526</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>资金合作机构编号</a:Comment>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o154">
<a:ObjectID>74387512-96FE-4F3D-A3B6-358B2508553F</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1497517585</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作业务类型</a:Comment>
<a:DataType>char(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>BAE54B18-8468-45B9-8190-AD330D617FF8</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1497517585</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作业务子类型</a:Comment>
<a:DataType>char(4)</a:DataType>
<a:Length>4</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>B20D79E4-089F-429D-B62F-C7BAA653B925</a:ObjectID>
<a:Name>RUT_CORP_ORG</a:Name>
<a:Code>RUT_CORP_ORG</a:Code>
<a:CreationDate>1497517585</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路径合作机构号</a:Comment>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o157">
<a:ObjectID>1DF085C7-8B08-41AE-AAEE-B732E9947FD4</a:ObjectID>
<a:Name>CRD_AC_TYP</a:Name>
<a:Code>CRD_AC_TYP</a:Code>
<a:CreationDate>1497517585</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>银行卡类型，D借记卡，C贷记卡</a:Comment>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>F8363F27-F55D-4CC7-804F-3A44B99C5CA8</a:ObjectID>
<a:Name>RUT_EFF_FLG</a:Name>
<a:Code>RUT_EFF_FLG</a:Code>
<a:CreationDate>1497517793</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路由生效标志，0失效，1生效</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>4AB6A3CB-A1C1-416D-AE74-D8BA4E396A51</a:ObjectID>
<a:Name>PRI_LVL</a:Name>
<a:Code>PRI_LVL</a:Code>
<a:CreationDate>1497517793</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>0-99，数字越大级别越高</a:Comment>
<a:DefaultValue>99</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>3461213D-DAE5-417A-8F28-8CA31B101C2C</a:ObjectID>
<a:Name>LOW_AMT</a:Name>
<a:Code>LOW_AMT</a:Code>
<a:CreationDate>1497519051</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>最低金额</a:Comment>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>0E648765-2C56-48E1-9BA2-729A38EBCB62</a:ObjectID>
<a:Name>HIGH_AMT</a:Name>
<a:Code>HIGH_AMT</a:Code>
<a:CreationDate>1497519051</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>最高金额</a:Comment>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>0A13E09D-44CF-4F25-9B30-7D673FC51902</a:ObjectID>
<a:Name>OPR_ID</a:Name>
<a:Code>OPR_ID</a:Code>
<a:CreationDate>1497517865</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建柜员ID</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>7D9422AC-1379-4EA3-BE69-894DD1E42985</a:ObjectID>
<a:Name>RMK</a:Name>
<a:Code>RMK</a:Code>
<a:CreationDate>1498532821</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>备注</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>8ECDC5C3-1902-44A9-AE2B-B1E2867CD8B5</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507772584</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o165">
<a:ObjectID>F2A129C5-4E20-4828-BB1F-74E011D47D67</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507772584</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o166">
<a:ObjectID>2CB688EA-6EC2-4637-BBAF-1711C9CFEE94</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1507772584</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o167">
<a:ObjectID>DA176458-B0CA-408B-9169-F63FF464FB9E</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497518746</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o152"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o168">
<a:ObjectID>A5A6F3D7-FFE9-4FBF-84F0-B5BD91C31650</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1498118758</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o169">
<a:ObjectID>994D6247-6DFF-405A-BFAD-71524BED9D85</a:ObjectID>
<a:CreationDate>1498118763</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o153"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o170">
<a:ObjectID>3197EFFA-83A9-498B-8FA0-AC4D2B030371</a:ObjectID>
<a:CreationDate>1498118763</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o154"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o171">
<a:ObjectID>B65A2122-AC51-4F1E-B2FD-E81C1F4142B3</a:ObjectID>
<a:CreationDate>1498118763</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o155"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o172">
<a:ObjectID>2068B274-1DEB-472F-B4C8-1B0C6F5044F4</a:ObjectID>
<a:CreationDate>1498118763</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o156"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o173">
<a:ObjectID>DA39E157-526A-4AF9-85C3-E4B5D762E3A4</a:ObjectID>
<a:CreationDate>1498118763</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o157"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o167"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o174">
<a:ObjectID>26396BE0-1B83-4679-964C-195E860DC558</a:ObjectID>
<a:Name>cpo_withdraw_order</a:Name>
<a:Code>cpo_withdraw_order</a:Code>
<a:CreationDate>1497750460</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504860229</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>资金流出订单表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o175">
<a:ObjectID>9538E649-2121-4C8F-962A-8BF8530E233B</a:ObjectID>
<a:Name>WC_ORD_NO</a:Name>
<a:Code>WC_ORD_NO</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224173</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>内部订单号</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o176">
<a:ObjectID>6DE8501E-8BA7-4E73-8B7F-C619AE06BD6C</a:ObjectID>
<a:Name>ORD_DT</a:Name>
<a:Code>ORD_DT</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504490318</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>订单日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>9CC28846-7C49-4383-935E-3ADB7005B8E1</a:ObjectID>
<a:Name>ORD_TM</a:Name>
<a:Code>ORD_TM</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504490318</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>订单时间</a:Comment>
<a:DataType>time</a:DataType>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>5D68293F-1847-429C-AAB7-59AF5BC34D4D</a:ObjectID>
<a:Name>AC_DT</a:Name>
<a:Code>AC_DT</a:Code>
<a:CreationDate>1497762644</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1504490318</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>会计日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>75ABB140-6B0F-413F-8AF6-3BF3E9B70F79</a:ObjectID>
<a:Name>CCY</a:Name>
<a:Code>CCY</a:Code>
<a:CreationDate>1499998611</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499998899</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>币种</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(3)</a:DataType>
<a:Length>3</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>FCD13CE7-0100-4F91-AC07-CDCBDE150915</a:ObjectID>
<a:Name>CAP_TYP</a:Name>
<a:Code>CAP_TYP</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>资金种类，1现金</a:Comment>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>EEC8F56C-1810-4596-BA0E-E393A10C180D</a:ObjectID>
<a:Name>CAP_CORG</a:Name>
<a:Code>CAP_CORG</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>资金合作机构号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>2174C18D-B9CF-45CB-BD41-208F4DC1B50C</a:ObjectID>
<a:Name>RUT_CORG</a:Name>
<a:Code>RUT_CORG</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路径合作机构号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>7F3B7415-923F-4929-BAD7-5E27A0551780</a:ObjectID>
<a:Name>CRD_AC_TYP</a:Name>
<a:Code>CRD_AC_TYP</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>卡种类，D借记卡，C贷记卡</a:Comment>
<a:DefaultValue>D</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>35AB174D-2D57-4A65-92FD-EDB830DA9B30</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作业务类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>F262E0BB-AA32-418D-A47B-4AE35E372071</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作业务子类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(4)</a:DataType>
<a:Length>4</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>7D0CCCDC-FA87-46EA-B46D-9B9128847A71</a:ObjectID>
<a:Name>REQ_ORD_NO</a:Name>
<a:Code>REQ_ORD_NO</a:Code>
<a:CreationDate>1498030452</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1503298335</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>请求方订单号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>B47840FD-7557-4FE3-BDE3-DD59EDAA967A</a:ObjectID>
<a:Name>REQ_ORD_DT</a:Name>
<a:Code>REQ_ORD_DT</a:Code>
<a:CreationDate>1498031062</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504492112</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>请求方订单日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o188">
<a:ObjectID>DA0E8A33-ECE7-4481-8826-07A151DFB18C</a:ObjectID>
<a:Name>REQ_ORD_TM</a:Name>
<a:Code>REQ_ORD_TM</a:Code>
<a:CreationDate>1498031062</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504492112</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>请求方订单时间</a:Comment>
<a:DataType>time</a:DataType>
</o:Column>
<o:Column Id="o189">
<a:ObjectID>96F42643-165D-4BD5-859A-F3B9BD7DBC50</a:ObjectID>
<a:Name>AGR_PAY_DT</a:Name>
<a:Code>AGR_PAY_DT</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1503288076</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>协议付款日</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o190">
<a:ObjectID>867C02A3-B523-4E01-A071-02912E9F12A8</a:ObjectID>
<a:Name>STL_AC_PERD</a:Name>
<a:Code>STL_AC_PERD</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>清算周期</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o191">
<a:ObjectID>EAFBBE64-AA8E-4F04-9B4D-2E9369965ABE</a:ObjectID>
<a:Name>WC_APL_AMT</a:Name>
<a:Code>WC_APL_AMT</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>提现金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o192">
<a:ObjectID>B1997779-245B-486A-ABA9-AFB211B0F8C7</a:ObjectID>
<a:Name>HOLD_NO</a:Name>
<a:Code>HOLD_NO</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499578850</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>冻结编号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(24)</a:DataType>
<a:Length>24</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o193">
<a:ObjectID>323E8F4D-C383-491C-B00F-2486B1D0A852</a:ObjectID>
<a:Name>PSN_CRP_FLG</a:Name>
<a:Code>PSN_CRP_FLG</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>个人/商户标识</a:Comment>
<a:DefaultValue>C</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>6ECA155D-64E3-4548-92B2-101432D2F50E</a:ObjectID>
<a:Name>MBL_NO</a:Name>
<a:Code>MBL_NO</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1503288076</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>手机号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>2340F06D-7A97-47BE-B10B-3FD23B2C7B14</a:ObjectID>
<a:Name>USER_NM</a:Name>
<a:Code>USER_NM</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499578832</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>用户/商户名称</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>A068B337-0AD4-4DF4-8172-CF720A03B5E2</a:ObjectID>
<a:Name>USER_ID</a:Name>
<a:Code>USER_ID</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1503288076</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>内部用户号/商户号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>F4E3FA77-559A-4C70-90E5-8EDD94F413E4</a:ObjectID>
<a:Name>ID_TYP</a:Name>
<a:Code>ID_TYP</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1503288076</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>证件类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(2)</a:DataType>
<a:Length>2</a:Length>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>74DF7FC1-517B-45F3-B2F7-6ABC0AF9A25B</a:ObjectID>
<a:Name>ID_NO</a:Name>
<a:Code>ID_NO_ENC</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1503288076</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>证件号码</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>D5746C9F-0665-4365-BBA5-F9EB397BA2D6</a:ObjectID>
<a:Name>CRD_NO_ENC</a:Name>
<a:Code>CRD_NO_ENC</a:Code>
<a:CreationDate>1497752883</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>卡号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>F5EDF4F0-51B2-4078-B32B-C4BF2019B713</a:ObjectID>
<a:Name>CAP_CRD_NM</a:Name>
<a:Code>CAP_CRD_NM</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1503298335</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>用户名</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o201">
<a:ObjectID>E560CFF6-7F3E-4B77-BA3F-DD2CCCB4CAA7</a:ObjectID>
<a:Name>WC_RMK</a:Name>
<a:Code>WC_RMK</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>提现备注</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o202">
<a:ObjectID>553A6925-A15A-4E17-A647-F5B944AC76E2</a:ObjectID>
<a:Name>ORD_STS</a:Name>
<a:Code>ORD_STS</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>订单状态</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o203">
<a:ObjectID>ED4FC252-DE6A-40BA-A7E1-E53A87C7A3EE</a:ObjectID>
<a:Name>WC_WF_STS</a:Name>
<a:Code>WC_WF_STS</a:Code>
<a:CreationDate>1497750795</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>提现审批工作流状态</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>A1C102F5-9ED1-45E3-A1FA-556ED848C7DB</a:ObjectID>
<a:Name>OPR_ID</a:Name>
<a:Code>OPR_ID</a:Code>
<a:CreationDate>1499578684</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1503298335</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>审批人ID</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o205">
<a:ObjectID>970C01C8-CE46-4575-BAF2-460542227E91</a:ObjectID>
<a:Name>WC_WF_RSN</a:Name>
<a:Code>WC_WF_RSN</a:Code>
<a:CreationDate>1499578724</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499676954</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>审批拒绝原因</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o206">
<a:ObjectID>60E0DD3C-63F0-49F1-9FD0-47197F83F171</a:ObjectID>
<a:Name>WC_BAT_NO</a:Name>
<a:Code>WC_BAT_NO</a:Code>
<a:CreationDate>1497751375</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224173</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>提现内部批次号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o207">
<a:ObjectID>E01B4A64-3731-469B-9557-344F7C853B26</a:ObjectID>
<a:Name>WC_BAT_SEQ</a:Name>
<a:Code>WC_BAT_SEQ</a:Code>
<a:CreationDate>1497751375</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1503298335</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>提现批次子序号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o208">
<a:ObjectID>9D91FBA7-04FB-49C2-B7CE-0517EF9EC744</a:ObjectID>
<a:Name>POST_DT</a:Name>
<a:Code>POST_DT</a:Code>
<a:CreationDate>1497751375</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504492112</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>记账日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o209">
<a:ObjectID>F436B7FB-08B8-4D80-965D-46AA034C7D10</a:ObjectID>
<a:Name>POST_TM</a:Name>
<a:Code>POST_TM</a:Code>
<a:CreationDate>1497751375</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504492112</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>记账时间</a:Comment>
<a:DataType>time</a:DataType>
</o:Column>
<o:Column Id="o210">
<a:ObjectID>0FCA9751-4A23-4E48-8B3C-3E9D631D0D4D</a:ObjectID>
<a:Name>AUTO_PAY_FLG</a:Name>
<a:Code>AUTO_PAY_FLG</a:Code>
<a:CreationDate>1497752383</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>自动付款标志，0：自动，1：手工</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o211">
<a:ObjectID>1AA09CF6-CD32-401E-9214-C8A806DA41F4</a:ObjectID>
<a:Name>WDC_AUTO_FLG</a:Name>
<a:Code>WDC_AUTO_FLG</a:Code>
<a:CreationDate>1497752383</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>实时付款标识，0实时，1，非实时</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o212">
<a:ObjectID>EE3D9B0C-1269-4114-BE5E-02FCE7B47160</a:ObjectID>
<a:Name>PAYTM_FLG</a:Name>
<a:Code>PAYTM_FLG</a:Code>
<a:CreationDate>1497752383</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>付款时间内标识，0付款时间内，1付款时间外</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o213">
<a:ObjectID>BE6392CE-C1F8-4949-ACD9-DAFC03D1DF52</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o214">
<a:ObjectID>0BD8965A-E23C-4548-B409-83B577C9A8BE</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o215">
<a:ObjectID>63A4F37C-07EA-464F-81D6-16D3D0099638</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o216">
<a:ObjectID>B1AC5DB5-3A0F-427F-884A-DB2847A0C36D</a:ObjectID>
<a:Name>ORD_SUCC_DT</a:Name>
<a:Code>ORD_SUCC_DT</a:Code>
<a:CreationDate>1497584960</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1503719489</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>订单成功日期</a:Comment>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o217">
<a:ObjectID>7563AE82-A039-46AC-B386-E1221F10F0E6</a:ObjectID>
<a:Name>ORD_SUCC_TM</a:Name>
<a:Code>ORD_SUCC_TM</a:Code>
<a:CreationDate>1497584960</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1503719494</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>订单成功时间</a:Comment>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>time</a:DataType>
</o:Column>
<o:Column Id="o218">
<a:ObjectID>EDA5F8E7-B75C-478C-BB4F-ACCE8D59423A</a:ObjectID>
<a:Name>NTF_STS</a:Name>
<a:Code>NTF_STS</a:Code>
<a:CreationDate>1497595111</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1503719405</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>通知状态，W：待通知，S：通知成功，F：通知失败（可重发），E：通知失败（不可重发）</a:Comment>
<a:DefaultValue>W</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o219">
<a:ObjectID>C1746100-A3C4-40F3-9A0D-43BB95EB40CC</a:ObjectID>
<a:Name>NTF_DT</a:Name>
<a:Code>NTF_DT</a:Code>
<a:CreationDate>1497595111</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1503719386</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>通知日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o220">
<a:ObjectID>7BF8B73C-649D-44AA-AD81-C4FD2B21BC30</a:ObjectID>
<a:Name>NTF_TM</a:Name>
<a:Code>NTF_TM</a:Code>
<a:CreationDate>1497595111</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1503719386</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>通知时间</a:Comment>
<a:DataType>time</a:DataType>
</o:Column>
<o:Column Id="o221">
<a:ObjectID>F1CE0CBD-D811-4A10-BD4B-434EFFCAFBE5</a:ObjectID>
<a:Name>NTF_RSP_CD</a:Name>
<a:Code>NTF_RSP_CD</a:Code>
<a:CreationDate>1497595147</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1503735067</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>通知返回码</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
</o:Column>
<o:Column Id="o222">
<a:ObjectID>4F137593-470E-4A26-8F98-5224C32313AE</a:ObjectID>
<a:Name>NTF_RSP_MSG</a:Name>
<a:Code>NTF_RSP_MSG</a:Code>
<a:CreationDate>1497595111</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1503719416</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>通知返回信息</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
</o:Column>
<o:Column Id="o223">
<a:ObjectID>04680449-E40C-47A4-A126-7DDB36E75690</a:ObjectID>
<a:Name>RMK</a:Name>
<a:Code>RMK</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>xuxiaofen</a:Creator>
<a:ModificationDate>1504860267</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>备注</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o224">
<a:ObjectID>13B1376B-8CDE-4C62-87D3-05A3C602A72D</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497752460</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o175"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o225">
<a:ObjectID>4F897624-D2A3-437B-979F-94CE3224928A</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1498728507</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o226">
<a:ObjectID>47FF8F88-75F8-4E4F-986F-FD057E0D56D3</a:ObjectID>
<a:CreationDate>1498728520</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o176"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o227">
<a:ObjectID>05F57354-4D7F-4314-A163-15B7138ACA10</a:ObjectID>
<a:Name>Index_2</a:Name>
<a:Code>Index_2</a:Code>
<a:CreationDate>1498728507</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o228">
<a:ObjectID>1B463DD5-B724-402F-B303-77D0878FA559</a:ObjectID>
<a:CreationDate>1498728557</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o196"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o224"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o229">
<a:ObjectID>6437851E-A487-4E5A-89D8-B16EE1E41ABC</a:ObjectID>
<a:Name>cpo_withdraw_param</a:Name>
<a:Code>cpo_withdraw_param</a:Code>
<a:CreationDate>1497942443</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>资金流出业务参数表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o230">
<a:ObjectID>E4D168F5-3D6A-41F3-B97A-77AF1FD7FADD</a:ObjectID>
<a:Name>BUS_PAR</a:Name>
<a:Code>BUS_PAR_ID</a:Code>
<a:CreationDate>1498118993</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o231">
<a:ObjectID>42C80F85-3B90-42B4-BF6D-057E497645E5</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500002545</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>付款类型</a:Comment>
<a:DataType>char(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o232">
<a:ObjectID>4B5286C9-7BE9-4F84-B255-0F7AA968EF83</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500002545</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>付款子类型</a:Comment>
<a:DataType>char(4)</a:DataType>
<a:Length>4</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o233">
<a:ObjectID>8EF5BB59-A1B2-4CD2-9382-1370BDA357E9</a:ObjectID>
<a:Name>CAP_TYP</a:Name>
<a:Code>CAP_TYP</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>资金种类，1现金</a:Comment>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o234">
<a:ObjectID>56828C38-CE80-4CEB-8EA7-EFFABB5F01BB</a:ObjectID>
<a:Name>PSN_CRP_FLG</a:Name>
<a:Code>PSN_CRP_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>个人/商户标识，C个人，B商户</a:Comment>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o235">
<a:ObjectID>BF29604D-33FB-47C5-A53B-7F47AA0B04B1</a:ObjectID>
<a:Name>HOLD_FLG</a:Name>
<a:Code>HOLD_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>冻结标识，0不冻结，1冻结</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o236">
<a:ObjectID>E7123E25-AB28-42EF-BB04-F61660E3EE07</a:ObjectID>
<a:Name>HOLD_DESC</a:Name>
<a:Code>HOLD_DESC</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499578819</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>冻结描述</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o237">
<a:ObjectID>FBA1C335-E99C-42C8-9916-5D2C85A9711B</a:ObjectID>
<a:Name>AUTO_PAY_FLG</a:Name>
<a:Code>AUTO_PAY_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>自动付款标识，0自动，1手工</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o238">
<a:ObjectID>E4C6568F-6850-48B3-8970-314A2C62EE1C</a:ObjectID>
<a:Name>RSK_CHK_FLG</a:Name>
<a:Code>RSK_CHK_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>风控检查标识，0不检查，1检查</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o239">
<a:ObjectID>DCCEFD39-9D9E-4E98-B6FF-CF45D4351BEB</a:ObjectID>
<a:Name>WDC_AUTO_FLG</a:Name>
<a:Code>WDC_AUTO_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>实时付款标识，0实时，1，非实时</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o240">
<a:ObjectID>4DD56391-B8E9-4113-AEAB-6F65E2380254</a:ObjectID>
<a:Name>RRC_CML_FLG</a:Name>
<a:Code>RRC_CML_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>风控累积标识，0不累积，1累积</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o241">
<a:ObjectID>2CAB1E55-DEE9-4AB7-8E99-99A776E712D6</a:ObjectID>
<a:Name>PAYTM_CHK_FLG</a:Name>
<a:Code>PAYTM_CHK_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>付款时间检查标识，0不检查，1检查</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o242">
<a:ObjectID>81C61BAC-3EA6-46FB-B255-F762581AA999</a:ObjectID>
<a:Name>CHK_USR_FLG</a:Name>
<a:Code>CHK_USR_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>用户状态检查标识，0不检查，1检查</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o243">
<a:ObjectID>26D11B5F-28B4-4B36-AE2B-4DA55E75F750</a:ObjectID>
<a:Name>PAUSE_FLG</a:Name>
<a:Code>PAUSE_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>付款失败暂停标识，0不暂停，1暂停</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o244">
<a:ObjectID>1B5262F8-1864-4500-8628-EA7FADBB076F</a:ObjectID>
<a:Name>SMS_FLG</a:Name>
<a:Code>SMS_FLG</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>短信下发标识，0不下发，1下发</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o245">
<a:ObjectID>59F4D0A6-4632-4E09-BC09-0D7B345D2809</a:ObjectID>
<a:Name>OPR_ID</a:Name>
<a:Code>OPR_ID</a:Code>
<a:CreationDate>1497942492</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>操作员ID</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o246">
<a:ObjectID>19FC5EB6-B622-4710-A55B-D935A7B1BC81</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o247">
<a:ObjectID>6F1B8DBE-1238-4D7B-B515-B870E58639F9</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o248">
<a:ObjectID>18120BEC-9CD9-494A-871F-083C612C8787</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o249">
<a:ObjectID>74F7013C-5F0B-41AD-9ED9-76FA75866A0B</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497946313</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o230"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o250">
<a:ObjectID>187A4628-A362-46C6-8A6A-FC8AB1A835EB</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1498119016</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o251">
<a:ObjectID>20FEA01D-5872-42F5-ADED-398ED45E66E2</a:ObjectID>
<a:CreationDate>1498119020</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o231"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o252">
<a:ObjectID>D2B77449-FFC0-42FA-9C7B-A2FEB3DAECD3</a:ObjectID>
<a:CreationDate>1498119020</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o232"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o253">
<a:ObjectID>699D658D-5DEA-42E7-AF4F-18F3BC5C0418</a:ObjectID>
<a:CreationDate>1498119020</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o233"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o254">
<a:ObjectID>3CF86DFF-CEFE-4EF6-BD47-B810D0142219</a:ObjectID>
<a:CreationDate>1498119020</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o234"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o249"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o255">
<a:ObjectID>127F8FC2-F321-4842-8DCC-CFD7B9A9A433</a:ObjectID>
<a:Name>cpo_withdraw_suborder</a:Name>
<a:Code>cpo_withdraw_suborder</a:Code>
<a:CreationDate>1497750529</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504923596</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>资金流出子订单表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o256">
<a:ObjectID>1116EDCD-87E8-4719-BE22-349A29795396</a:ObjectID>
<a:Name>SUB_ORD_NO</a:Name>
<a:Code>SUB_ORD_NO</a:Code>
<a:CreationDate>1497762644</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224199</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>子订单号</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o257">
<a:ObjectID>8BD0C363-5DA5-4900-88D9-C6447FFCB313</a:ObjectID>
<a:Name>WC_ORD_NO</a:Name>
<a:Code>WC_ORD_NO</a:Code>
<a:CreationDate>1497762644</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1501224199</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>订单号</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o258">
<a:ObjectID>6D0C9AE2-0E0F-45ED-9B7D-F01C923783CA</a:ObjectID>
<a:Name>RUT_CORG</a:Name>
<a:Code>RUT_CORG</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路径合作机构号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o259">
<a:ObjectID>5AEB4830-1B29-4CD0-A85C-0406F173279C</a:ObjectID>
<a:Name>CAP_CORG</a:Name>
<a:Code>CAP_CORG</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>资金合作机构号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o260">
<a:ObjectID>D5D5EE9B-805B-4326-B9B4-AEB768FC124D</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作业务类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o261">
<a:ObjectID>64D11418-6CC4-41CB-B0D4-6C00955E6479</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>合作业务子类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(4)</a:DataType>
<a:Length>4</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o262">
<a:ObjectID>91E2E699-F44C-460A-A76A-15E4FD6A741A</a:ObjectID>
<a:Name>WC_APL_AMT</a:Name>
<a:Code>WC_APL_AMT</a:Code>
<a:CreationDate>1497750584</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499737284</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>提现金额</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>decimal(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o263">
<a:ObjectID>D05DE354-545E-4433-807D-DCF989099B7A</a:ObjectID>
<a:Name>ORD_STS</a:Name>
<a:Code>ORD_STS</a:Code>
<a:CreationDate>1499737079</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499737267</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>订单状态</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o264">
<a:ObjectID>06FFEFFE-29A0-492B-8482-8B904DB7A97F</a:ObjectID>
<a:Name>ORD_DT</a:Name>
<a:Code>ORD_DT</a:Code>
<a:CreationDate>1499852899</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1504492139</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>订单日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o265">
<a:ObjectID>E9B34850-6714-4386-858D-F9FC2A2B41A3</a:ObjectID>
<a:Name>AC_DT</a:Name>
<a:Code>AC_DT</a:Code>
<a:CreationDate>1497762644</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1504492139</a:ModificationDate>
<a:Modifier>xuxiaofen</a:Modifier>
<a:Comment>会计日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o266">
<a:ObjectID>2BF2B062-2BF3-4537-A410-A57A7E42654C</a:ObjectID>
<a:Name>RUT_CORG_JRN</a:Name>
<a:Code>RUT_CORG_JRN</a:Code>
<a:CreationDate>1497762644</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>银行返回流水号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o267">
<a:ObjectID>49CD703E-4F06-418A-A616-5E2D2CBD656D</a:ObjectID>
<a:Name>RUT_CORG_DT</a:Name>
<a:Code>RUT_CORG_DT</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500017054</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>银行返回日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o268">
<a:ObjectID>254ED9E5-26C8-4A68-9879-837E34AADE1D</a:ObjectID>
<a:Name>RUT_CORG_TM</a:Name>
<a:Code>RUT_CORG_TM</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500017054</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>银行返回时间</a:Comment>
<a:DataType>time</a:DataType>
</o:Column>
<o:Column Id="o269">
<a:ObjectID>5F19580D-E3A7-46EF-B4CB-D0A0A5A1345B</a:ObjectID>
<a:Name>ORG_RSP_CD</a:Name>
<a:Code>ORG_RSP_CD</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>返回码</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o270">
<a:ObjectID>ABFC8C15-F06E-49F7-A4CA-0AF423EE77C8</a:ObjectID>
<a:Name>ORG_RSP_MSG</a:Name>
<a:Code>ORG_RSP_MSG</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>返回信息</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o271">
<a:ObjectID>25C77264-C904-4F28-B1FD-FCF4BCF090DD</a:ObjectID>
<a:Name>CHK_KEY</a:Name>
<a:Code>CHK_KEY</a:Code>
<a:CreationDate>1500945288</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1501224199</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账外键(发往银行的订单号)</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o272">
<a:ObjectID>09209F5F-69FD-4C42-8BCF-9E9E520A52D5</a:ObjectID>
<a:Name>CHK_STS</a:Name>
<a:Code>CHK_STS</a:Code>
<a:CreationDate>1499576752</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499847632</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账状态 0-未对账；1-对账成功；2-我方有机构无；3-机构有我方无；4-金额错；5-存疑</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o273">
<a:ObjectID>2DAA7131-5C87-4C14-A7EF-A0271A2A3DB3</a:ObjectID>
<a:Name>CHK_DT</a:Name>
<a:Code>CHK_DT</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500017054</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o274">
<a:ObjectID>D38E3624-08DE-4CC4-B3C8-CD7FB6F65590</a:ObjectID>
<a:Name>CHK_TM</a:Name>
<a:Code>CHK_TM</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500017054</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账时间</a:Comment>
<a:DataType>time</a:DataType>
</o:Column>
<o:Column Id="o275">
<a:ObjectID>682A9E1D-BBE2-4021-B6A6-8D6206D4E483</a:ObjectID>
<a:Name>CAV_DT</a:Name>
<a:Code>CAV_DT</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500017054</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>核销日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o276">
<a:ObjectID>ADB06D13-9764-461E-8798-F8B65CB0D8ED</a:ObjectID>
<a:Name>CAV_TM</a:Name>
<a:Code>CAV_TM</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500017054</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>核销时间</a:Comment>
<a:DataType>time</a:DataType>
</o:Column>
<o:Column Id="o277">
<a:ObjectID>758168F3-D153-4216-8331-01FA59F3C18C</a:ObjectID>
<a:Name>CAV_OPER</a:Name>
<a:Code>CAV_OPER</a:Code>
<a:CreationDate>1497762783</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>核销操作员</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o278">
<a:ObjectID>50320814-D421-4EF3-94DD-55871E2CC8F6</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o279">
<a:ObjectID>286DCEA8-129B-4B33-BC2E-CF4ADFEE8C1D</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o280">
<a:ObjectID>C0439990-1DB8-4797-9F75-AD52E92AE3CC</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o281">
<a:ObjectID>5E57324E-A11C-43D6-AC75-FFE41262FCE8</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1497763117</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o256"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o282">
<a:ObjectID>087CAC6F-BA63-4388-8550-CFF05C92A633</a:ObjectID>
<a:Name>withdraw_suborder_in1</a:Name>
<a:Code>withdraw_suborder_in1</a:Code>
<a:CreationDate>1497796096</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500950441</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o283">
<a:ObjectID>102E8F5D-904F-4178-8E3F-01F722DE61B1</a:ObjectID>
<a:CreationDate>1497796284</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241687</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o257"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o284">
<a:ObjectID>1EE85616-9CAD-4090-B615-08A1C91567BA</a:ObjectID>
<a:Name>withdraw_suborder_ui1</a:Name>
<a:Code>withdraw_suborder_ui1</a:Code>
<a:CreationDate>1500950049</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500950453</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o285">
<a:ObjectID>8DC0B3D0-1A92-4280-BF7E-B28E497BBE77</a:ObjectID>
<a:CreationDate>1500950442</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500950453</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<c:Column>
<o:Column Ref="o271"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o281"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o6">
<a:ObjectID>FB7EB6AC-D72F-4128-98A4-033FA8A12FA4</a:ObjectID>
<a:Name>cpo_lock</a:Name>
<a:Code>cpo_lock</a:Code>
<a:CreationDate>1499753577</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499823155</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>对账批次锁状态表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o286">
<a:ObjectID>09A1347F-64D3-4C24-83A1-8A8F5B896A79</a:ObjectID>
<a:Name>LOCK_ID</a:Name>
<a:Code>LOCK_ID</a:Code>
<a:CreationDate>1499753577</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1501224371</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>锁ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o287">
<a:ObjectID>452902D0-C9C5-4E15-ADF5-FD98FE7A966C</a:ObjectID>
<a:Name>LOCK_NAME</a:Name>
<a:Code>LOCK_NAME</a:Code>
<a:CreationDate>1499753577</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499753824</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>锁名称</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o288">
<a:ObjectID>E4C65121-A545-4549-AFD8-64ABD0068CD7</a:ObjectID>
<a:Name>LOCK_STS</a:Name>
<a:Code>LOCK_STS</a:Code>
<a:CreationDate>1499753577</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499753956</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>锁状态，U：未加锁；L：已加锁</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o289">
<a:ObjectID>4FE81E74-7CC7-4945-825C-5597CD176431</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1499823112</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499823166</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o290">
<a:ObjectID>CFDDE52D-A0EA-481C-97A0-70F4DAE55487</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1499823112</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499823174</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o291">
<a:ObjectID>349E2DF4-6F31-4704-89FE-97BADCF7883B</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1499753577</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499753873</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o292">
<a:ObjectID>E7743347-04FA-4C28-945B-8525DAEE2B57</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1499753577</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1499753797</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o286"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o292"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o8">
<a:ObjectID>477C5DEF-F833-42DF-9C34-33EDF1860E3C</a:ObjectID>
<a:Name>cpo_acc_icbc_withdraw</a:Name>
<a:Code>cpo_acc_icbc_withdraw</a:Code>
<a:CreationDate>1499823348</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888206</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>工行对账明细表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o293">
<a:ObjectID>95A93399-1D9D-4720-93B3-66126574DADE</a:ObjectID>
<a:Name>company_date</a:Name>
<a:Code>company_date</a:Code>
<a:CreationDate>1500376098</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>公司方日期</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(8)</a:DataType>
<a:Length>8</a:Length>
</o:Column>
<o:Column Id="o294">
<a:ObjectID>50163B78-76D3-40AA-9BD1-68A995C7082E</a:ObjectID>
<a:Name>company_serino</a:Name>
<a:Code>company_serino</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>公司方交易流水号(同机构交易流水号)</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o295">
<a:ObjectID>A530CCDA-B8E3-461C-9D22-2AC97061584D</a:ObjectID>
<a:Name>bank_serno</a:Name>
<a:Code>bank_serno</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>银行流水号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o296">
<a:ObjectID>2E9BF70F-B3BF-4BBE-A27B-F531EE87819E</a:ObjectID>
<a:Name>settle_date</a:Name>
<a:Code>settle_date</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>清算日期，格式：YYYYMMDD</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(8)</a:DataType>
<a:Length>8</a:Length>
</o:Column>
<o:Column Id="o297">
<a:ObjectID>4814C4DD-D301-4240-B2D5-D0D158226EE7</a:ObjectID>
<a:Name>cardNo</a:Name>
<a:Code>card_no</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>银行卡号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o298">
<a:ObjectID>2DBD9745-C0F3-4F04-A559-51659C331C4F</a:ObjectID>
<a:Name>pay_amt</a:Name>
<a:Code>pay_amt</a:Code>
<a:CreationDate>1500377726</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>银行订单金额(单位:元)</a:Comment>
<a:DefaultValue>0.00</a:DefaultValue>
<a:DataType>DECIMAL(13,2)</a:DataType>
<a:Length>13</a:Length>
<a:Precision>2</a:Precision>
</o:Column>
<o:Column Id="o299">
<a:ObjectID>6B8D7F83-688F-4A88-AFC7-EFA863BD4E0E</a:ObjectID>
<a:Name>curr_type</a:Name>
<a:Code>curr_type</a:Code>
<a:CreationDate>1500377770</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>币种</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(3)</a:DataType>
<a:Length>3</a:Length>
</o:Column>
<o:Column Id="o300">
<a:ObjectID>6B1C5882-7268-43D1-B093-4C6D9E95EBC1</a:ObjectID>
<a:Name>status</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1500377770</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>明细状态 2-成功; 3-失败</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o301">
<a:ObjectID>35A618AB-567F-4A0F-B5CA-D5752B685BE1</a:ObjectID>
<a:Name>CHK_ID</a:Name>
<a:Code>CHK_ID</a:Code>
<a:CreationDate>1500541834</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增对账明细主键</a:Comment>
<a:DataType>VARCHAR(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o302">
<a:ObjectID>1E2081AD-6DD7-4ABE-8EBF-7371C825A83C</a:ObjectID>
<a:Name>CHK_BAT_NO</a:Name>
<a:Code>CHK_BAT_NO</a:Code>
<a:CreationDate>1500426252</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增对账批次号</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o303">
<a:ObjectID>B1835F16-00A6-4317-999F-25E198E65BE4</a:ObjectID>
<a:Name>RUT_CORG</a:Name>
<a:Code>RUT_CORG</a:Code>
<a:CreationDate>1500426496</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888179</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增路径合作机构</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
</o:Column>
<o:Column Id="o304">
<a:ObjectID>85B848E5-A6AC-4727-B7DC-CA857518EB9D</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1500426526</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888183</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增合作业务类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(2)</a:DataType>
<a:Length>2</a:Length>
</o:Column>
<o:Column Id="o305">
<a:ObjectID>77D45F20-149D-4FC6-A005-18359420386C</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1500426544</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888187</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增合作业务子类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>VARCHAR(4)</a:DataType>
<a:Length>4</a:Length>
</o:Column>
<o:Column Id="o306">
<a:ObjectID>826D52B7-8B2F-4100-B25E-06B7BFD05E5C</a:ObjectID>
<a:Name>CHK_FIL_DT</a:Name>
<a:Code>CHK_FIL_DT</a:Code>
<a:CreationDate>1500426576</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888190</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增对账文件日期</a:Comment>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o307">
<a:ObjectID>D5D4057B-CCB7-4638-A2A4-4C1FB7CE7815</a:ObjectID>
<a:Name>CHK_FIL_NM</a:Name>
<a:Code>CHK_FIL_NM</a:Code>
<a:CreationDate>1500426722</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888194</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增对账文件名称</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o308">
<a:ObjectID>84F380D7-6507-449F-BA6A-89BC0EDB6B11</a:ObjectID>
<a:Name>CHK_STS</a:Name>
<a:Code>CHK_STS</a:Code>
<a:CreationDate>1497594840</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888204</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>VARCHAR(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o309">
<a:ObjectID>83ED8EBC-2E1B-4147-B7C9-E24690E4AC74</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888197</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<a:Comment>新增时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o310">
<a:ObjectID>9D62C5B4-468C-4308-97BB-A437AD27EE47</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1499823368</a:CreationDate>
<a:Creator>gonglei</a:Creator>
<a:ModificationDate>1500888006</a:ModificationDate>
<a:Modifier>gonglei</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o301"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o310"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o10">
<a:ObjectID>08B19FF7-8E73-4F1A-B6D0-7AE5492B9CCB</a:ObjectID>
<a:Name>cpo_acc_cfg</a:Name>
<a:Code>cpo_acc_cfg</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500552996</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账配置表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o311">
<a:ObjectID>46797435-05B0-4F56-962D-7EFB1C7DBB0C</a:ObjectID>
<a:Name>ACC_CFG_ID</a:Name>
<a:Code>ACC_CFG_ID</a:Code>
<a:CreationDate>1500540803</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500540855</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>id</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o312">
<a:ObjectID>C70F2F14-B679-46D3-B3F4-B137C5E824CE</a:ObjectID>
<a:Name>RUT_CORG</a:Name>
<a:Code>RUT_CORG</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541892</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路径机构</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o313">
<a:ObjectID>892BE96C-8B68-414F-A580-3E144ACE878F</a:ObjectID>
<a:Name>CORP_BUS_TYP</a:Name>
<a:Code>CORP_BUS_TYP</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541892</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>业务类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(2)</a:DataType>
<a:Length>2</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o314">
<a:ObjectID>9CFFD9C4-80B7-4126-8A2D-30007CCD2AF4</a:ObjectID>
<a:Name>CORP_BUS_SUB_TYP</a:Name>
<a:Code>CORP_BUS_SUB_TYP</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541892</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>业务子类型</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>char(4)</a:DataType>
<a:Length>4</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o315">
<a:ObjectID>649D9724-9913-4BAD-B7B5-C6717C6F3792</a:ObjectID>
<a:Name>CHK_CLAZZ</a:Name>
<a:Code>CHK_CLAZZ</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541555</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>获取文件的类</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o316">
<a:ObjectID>8ABD328B-3AF4-4DD1-A7F7-024DC93C4167</a:ObjectID>
<a:Name>GET_FILE_METHOD</a:Name>
<a:Code>GET_FILE_METHOD</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541570</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>获取文件的方法</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o317">
<a:ObjectID>E61A2C0C-812D-4A29-8D0F-A385159CC0EE</a:ObjectID>
<a:Name>IMPORT_FIELDS</a:Name>
<a:Code>IMPORT_FIELDS</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541576</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>入库的字段</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o318">
<a:ObjectID>2200654F-6A2B-438C-88E6-B389BE35DCD5</a:ObjectID>
<a:Name>IMPORT_CLAZZ</a:Name>
<a:Code>IMPORT_CLAZZ</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500548045</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>入库的明细类</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o319">
<a:ObjectID>E00DC923-0F67-42F2-9069-20E1994C5748</a:ObjectID>
<a:Name>IMPORT_METHOD</a:Name>
<a:Code>IMPORT_METHOD</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541588</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>入库的方法</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o320">
<a:ObjectID>0E3900B8-BDB7-4920-B337-73BF70F8297D</a:ObjectID>
<a:Name>SPLIT_SIGN</a:Name>
<a:Code>SPLIT_SIGN</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541595</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>分隔符</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(8)</a:DataType>
<a:Length>8</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o321">
<a:ObjectID>02BD6AA7-562F-480E-BC2B-1B3F0DA45856</a:ObjectID>
<a:Name>CHK_FILE_PATH</a:Name>
<a:Code>CHK_FILE_PATH</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500543294</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>文件路径</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o322">
<a:ObjectID>402ECC9F-93A4-49B2-BDA1-FE83B2DD71DF</a:ObjectID>
<a:Name>CONTINUE_NUM</a:Name>
<a:Code>CONTINUE_NUM</a:Code>
<a:CreationDate>1500540102</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541610</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>跳过多少行</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o323">
<a:ObjectID>097D7029-C6E3-47D7-AE2E-8C0133B5BC00</a:ObjectID>
<a:Name>SELECT_DETAIL_METHOD</a:Name>
<a:Code>SELECT_DETAIL_METHOD</a:Code>
<a:CreationDate>1500540386</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541622</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>查询明细方法</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o324">
<a:ObjectID>A1906E4A-446D-46A5-9DCA-A3323907AE1A</a:ObjectID>
<a:Name>SUCCESS_FLAG</a:Name>
<a:Code>SUCCESS_FLAG</a:Code>
<a:CreationDate>1500540386</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541628</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账成功标志</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(8)</a:DataType>
<a:Length>8</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o325">
<a:ObjectID>16941608-2312-4E2D-9B6A-84B98383252C</a:ObjectID>
<a:Name>CHECK_KEY_FILED</a:Name>
<a:Code>CHECK_KEY_FILED</a:Code>
<a:CreationDate>1500540386</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500543409</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账键值对应的域</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o326">
<a:ObjectID>F7D925E3-8389-4CA1-B9B0-F9C002433590</a:ObjectID>
<a:Name>CHECK_KEY_BAK_FILED</a:Name>
<a:Code>CHECK_KEY_BAK_FILED</a:Code>
<a:CreationDate>1500540386</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500543409</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账键值备份对应的域</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o327">
<a:ObjectID>7B5FF5E6-9E79-4EC7-905B-989FC4ABF1EE</a:ObjectID>
<a:Name>CHECK_AMT_FILED</a:Name>
<a:Code>CHECK_AMT_FILED</a:Code>
<a:CreationDate>1500540386</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500543409</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账金额对应的域</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o328">
<a:ObjectID>C635477C-DF04-4829-B64D-B290C0216D92</a:ObjectID>
<a:Name>TX_STS_FILED</a:Name>
<a:Code>TX_STS_FILED</a:Code>
<a:CreationDate>1500540386</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500543431</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账状态对应的域</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o329">
<a:ObjectID>7090DF43-F99E-4246-9AD9-530BD4C137F5</a:ObjectID>
<a:Name>QUERY_NUM</a:Name>
<a:Code>QUERY_NUM</a:Code>
<a:CreationDate>1500540386</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500543496</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>对账时每次查询的笔数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o330">
<a:ObjectID>6B280FEF-10E9-4AC9-B157-A09078A8A74E</a:ObjectID>
<a:Name>UPDATE_METHOD</a:Name>
<a:Code>UPDATE_METHOD</a:Code>
<a:CreationDate>1500552963</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500553000</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>更新对账状态方法</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o331">
<a:ObjectID>A35C4958-371D-45B2-8DFA-14514AA596F2</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1500540870</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500551382</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o332">
<a:ObjectID>58E6F226-2BE2-48EC-96C3-1C58779D2551</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1500540870</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500551382</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o333">
<a:ObjectID>68A9662A-E57E-4CE3-B924-A64862EA9A79</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1499823368</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500542400</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DataType>timestamp</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o334">
<a:ObjectID>654EABF4-B3F2-4D2C-9929-B2B8DBDD1DC1</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1500540803</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500540855</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o311"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o335">
<a:ObjectID>0314D6FF-218E-4B6A-BCAE-DE8E6FB85C69</a:ObjectID>
<a:Name>in1</a:Name>
<a:Code>in1</a:Code>
<a:CreationDate>1500541648</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541892</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o336">
<a:ObjectID>DD488EFD-C8C2-491E-A33D-7347D3DDD2DB</a:ObjectID>
<a:CreationDate>1500541883</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541892</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o312"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o337">
<a:ObjectID>AC046FC0-E51E-4A4F-A296-24C0AC32CC14</a:ObjectID>
<a:CreationDate>1500541883</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541892</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o313"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o338">
<a:ObjectID>E4FEF28D-ABB2-4F19-A552-0937F679E4AB</a:ObjectID>
<a:CreationDate>1500541883</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1500541892</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o314"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o334"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o339">
<a:ObjectID>52D70DB4-1062-41BF-8BCB-20E661FAC65D</a:ObjectID>
<a:Name>cpo_acc_upload_cfg</a:Name>
<a:Code>cpo_acc_upload_cfg</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674984</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>上传对账文件配置表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o340">
<a:ObjectID>36A559A3-6ADF-4D77-A06F-DEFA21B0C356</a:ObjectID>
<a:Name>CHK_ID</a:Name>
<a:Code>CHK_ID</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>主键id</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o341">
<a:ObjectID>FF212BA7-325F-45A4-A999-78EE70AC5381</a:ObjectID>
<a:Name>RUT_CORG</a:Name>
<a:Code>RUT_CORG</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>路径机构或模块名</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
</o:Column>
<o:Column Id="o342">
<a:ObjectID>84DDA33F-3830-4EB2-9638-9037E09DF463</a:ObjectID>
<a:Name>LOCAL_FILE_PATH</a:Name>
<a:Code>LOCAL_FILE_PATH</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>本地路径</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o343">
<a:ObjectID>8A2E3AC0-7BAB-493A-B332-3B7094F96E38</a:ObjectID>
<a:Name>UPLOAD_IP</a:Name>
<a:Code>UPLOAD_IP</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>上传ip</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o344">
<a:ObjectID>A1D015BA-7D53-4264-A95F-01457D9C9A82</a:ObjectID>
<a:Name>UPLOAD_PORT</a:Name>
<a:Code>UPLOAD_PORT</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>上传端口</a:Comment>
<a:DefaultValue>22</a:DefaultValue>
<a:DataType>varchar(8)</a:DataType>
<a:Length>8</a:Length>
</o:Column>
<o:Column Id="o345">
<a:ObjectID>44F6F7D9-3156-4C01-8C8D-B52C75DCBF23</a:ObjectID>
<a:Name>CONNECT_TIME</a:Name>
<a:Code>CONNECT_TIME</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>连接超时时长(毫秒)</a:Comment>
<a:DefaultValue>120000</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o346">
<a:ObjectID>165C0926-17D7-4B12-A5C1-03CA1EC70EC6</a:ObjectID>
<a:Name>UPLOAD_PATH</a:Name>
<a:Code>UPLOAD_PATH</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>上传路径</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o347">
<a:ObjectID>3EDA7B6F-27B5-406E-A431-C17D73230934</a:ObjectID>
<a:Name>UPLOAD_NM</a:Name>
<a:Code>UPLOAD_NM</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>服务器用户名</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o348">
<a:ObjectID>0362D9FE-E511-49F3-AC3E-2A7815DAA63B</a:ObjectID>
<a:Name>UPLOAD_PWD</a:Name>
<a:Code>UPLOAD_PWD</a:Code>
<a:CreationDate>1502673688</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>服务器密码</a:Comment>
<a:DefaultValue> </a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o349">
<a:ObjectID>E7694D24-03FA-45FE-9668-D10E969D7060</a:ObjectID>
<a:Name>CREATE_TIME</a:Name>
<a:Code>CREATE_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o350">
<a:ObjectID>CD340458-2219-43BF-B6C8-AF4113EC0E3F</a:ObjectID>
<a:Name>MODIFY_TIME</a:Name>
<a:Code>MODIFY_TIME</a:Code>
<a:CreationDate>1498701463</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o351">
<a:ObjectID>CD4EC8FC-B710-4B75-BC4C-AE04AE51072A</a:ObjectID>
<a:Name>TM_SMP</a:Name>
<a:Code>TM_SMP</a:Code>
<a:CreationDate>1498722305</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Comment>时间戳</a:Comment>
<a:DefaultValue>CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>timestamp</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o352">
<a:ObjectID>8F3506A1-3710-4FC5-9E88-66C416DFF5F5</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1502674548</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o340"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o353">
<a:ObjectID>196B9609-DB7D-4647-AFCE-7E75E6420EA5</a:ObjectID>
<a:Name>Index_1</a:Name>
<a:Code>Index_1</a:Code>
<a:CreationDate>1502674552</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o354">
<a:ObjectID>1E179DCC-3B24-418F-A444-17DD0DE3634F</a:ObjectID>
<a:CreationDate>1502674579</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1502674977</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<c:Column>
<o:Column Ref="o341"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o352"/>
</c:PrimaryKey>
</o:Table>
</c:Tables>
<c:DefaultGroups>
<o:Group Id="o355">
<a:ObjectID>43C54372-9F0C-458E-96A3-C7EDA11C1A41</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1499241649</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241649</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o356">
<a:ObjectID>E789763C-C5C1-4839-B3CC-83916FB3623B</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1499241649</a:CreationDate>
<a:Creator>Rui</a:Creator>
<a:ModificationDate>1499241649</a:ModificationDate>
<a:Modifier>Rui</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/mysql50.xdb</a:TargetModelURL>
<a:TargetModelID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<a:TargetModelLastModificationDate>1276524678</a:TargetModelLastModificationDate>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>