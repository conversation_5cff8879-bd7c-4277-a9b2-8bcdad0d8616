//package com.hisun.lemon.cpo;
//
//import com.hisun.lemon.cpo.entity.WithdrawParamDO;
//import com.hisun.lemon.cpo.service.IWithdrawService;
//import com.hisun.lemon.framework.utils.IdGenUtils;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
///**
// * Created by gonglei on 2017/7/5.
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@Ignore
//public class WithdrawServiceTest {
//
//    @Resource
//    private IWithdrawService withdrawService;
//
//    @Test
//    @Ignore
//    public void contextLoads() throws Exception {
//        removeWithdrawParamByPrimaryKey();
//        System.out.println("========= contextLoads() end =========");
//    }
//
//    /**
//     * 新增付款业务参数信息
//     * @param
//     * @return
//     */
//    @Test
//    @Ignore
//    public void addWithdrawParam() {
//        System.out.println("========= addWithdrawParam() start =========");
//        WithdrawParamDO withdrawParamDO = new WithdrawParamDO();
//        withdrawParamDO.setBusParId(IdGenUtils.generateId("CPO", "CP", 18));
//        withdrawParamDO.setCorpBusTyp("01");
//        withdrawParamDO.setCorpBusSubTyp("0102");
//        withdrawParamDO.setCapTyp("1");
//        withdrawParamDO.setPsnCrpFlg("B");
//        withdrawParamDO.setHoldFlg("1");
//        withdrawParamDO.setHoldDesc("test addWithdrawParam");
//        withdrawParamDO.setAutoPayFlg("1");
//        withdrawParamDO.setRskChkFlg("1");
//        withdrawParamDO.setWdcAutoFlg("1");
//        withdrawParamDO.setRrcCmlFlg("1");
//        withdrawParamDO.setPaytmChkFlg("1");
//        withdrawParamDO.setChkUsrFlg("1");
//        withdrawParamDO.setPauseFlg("1");
//        withdrawParamDO.setSmsFlg("1");
//        withdrawParamDO.setOprId("100001");
//        boolean isSuccess = withdrawService.addWithdrawParam(withdrawParamDO);
//        System.out.println("========= addWithdrawParam() isSuccess = " + isSuccess);
//    }
//
//    /**
//     * 根据主键查询付款业务参数信息
//     * @param
//     * @return
//     */
//    @Test
//    @Ignore
//    public void getWithdrawParamByPrimaryKey() {
//        System.out.println("========= getWithdrawParamByPrimaryKey() start =========");
//        String busParId = "CP000000000000002001";
//        WithdrawParamDO withdrawParamDO1 = withdrawService.getWithdrawParamByKey(busParId);
//        if(null == withdrawParamDO1) {
//            System.out.println("========= getWithdrawParamByPrimaryKey() isSuccess = false");
//        } else {
//            System.out.println("========= getWithdrawParamByPrimaryKey() isSuccess = true");
//        }
//    }
//
//    /**
//     * 根据唯一索引查询付款业务参数信息
//     * @param
//     * @return
//     */
//    @Test
//    @Ignore
//    public void getWithdrawParam() {
//        System.out.println("========= getWithdrawParam() start =========");
//        WithdrawParamDO withdrawParamDO = new WithdrawParamDO();
//        withdrawParamDO.setCorpBusTyp("01");
//        withdrawParamDO.setCorpBusSubTyp("0102");
//        withdrawParamDO.setCapTyp("1");
//        withdrawParamDO.setPsnCrpFlg("B");
//        WithdrawParamDO withdrawParamDO1 = withdrawService.getWithdrawParam(withdrawParamDO);
//        if(null == withdrawParamDO1) {
//            System.out.println("========= getWithdrawParam() isSuccess = false");
//        } else {
//            System.out.println("========= getWithdrawParam() isSuccess = true");
//        }
//    }
//
//    /**
//     * 根据唯一索引更新付款业务参数信息
//     * @param
//     * @return
//     */
//    @Test
//    @Ignore
//    public void updateWithdrawParam() {
//        System.out.println("========= updateWithdrawParam() start =========");
//        WithdrawParamDO withdrawParamDO = new WithdrawParamDO();
//        withdrawParamDO.preUpdate();
//        withdrawParamDO.setBusParId("CP000000000000002001");
//        withdrawParamDO.setCorpBusTyp("01");
//        withdrawParamDO.setCorpBusSubTyp("0102");
//        withdrawParamDO.setCapTyp("1");
//        withdrawParamDO.setPsnCrpFlg("B");
//        withdrawParamDO.setHoldFlg("0");
//        withdrawParamDO.setHoldDesc("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa");
//        withdrawParamDO.setAutoPayFlg("1");
//        withdrawParamDO.setRskChkFlg("1");
//        withdrawParamDO.setWdcAutoFlg("1");
//        withdrawParamDO.setRrcCmlFlg("1");
//        withdrawParamDO.setPaytmChkFlg("1");
//        withdrawParamDO.setChkUsrFlg("1");
//        withdrawParamDO.setPauseFlg("1");
//        withdrawParamDO.setSmsFlg("1");
//        withdrawParamDO.setOprId("111111");
//        boolean isSuccess = withdrawService.updateWithdrawParam(withdrawParamDO);
//        System.out.println("========= updateWithdrawParam() isSuccess = " + isSuccess);
//    }
//
//    /**
//     * 根据主键删除付款业务参数信息
//     * @param
//     * @return
//     */
//    @Test
//    @Ignore
//    public void removeWithdrawParamByPrimaryKey() {
//        System.out.println("========= removeWithdrawParamByPrimaryKey() start =========");
//        String busParId = "CP000000000000002001";
//        withdrawService.deleteWithdrawParamByKey(busParId);
//    }
//
//}
