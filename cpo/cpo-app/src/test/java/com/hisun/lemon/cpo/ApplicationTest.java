//package com.hisun.lemon.cpo;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.ObjectWriter;
//import com.hisun.lemon.common.utils.DateTimeUtils;
//import com.hisun.lemon.common.utils.JudgeUtils;
//import com.hisun.lemon.cpo.common.CpoConstants;
//import com.hisun.lemon.cpo.dao.IAccCfgDao;
//import com.hisun.lemon.cpo.dao.IAccControlDao;
//import com.hisun.lemon.cpo.dao.IAccParamDao;
//import com.hisun.lemon.cpo.dto.WithdrawReqDTO;
//import com.hisun.lemon.cpo.entity.AccCfgDO;
//import com.hisun.lemon.cpo.entity.AccControlDO;
//import com.hisun.lemon.cpo.entity.AccParamDO;
//import com.hisun.lemon.cpo.entity.CopAgcyInfoDO;
//import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
//import com.hisun.lemon.cpo.enums.CorpBusTyp;
//import com.hisun.lemon.cpo.schedule.WriteCheckFileSchedule;
//import com.hisun.lemon.cpo.service.ICheckAccControlService;
//import com.hisun.lemon.cpo.service.ICopAgcyInfoService;
//import com.hisun.lemon.cpo.service.ICopBizRouteService;
//import com.hisun.lemon.cpo.utils.EncryptUtils;
//import com.hisun.lemon.framework.data.GenericDTO;
//import com.hisun.lemon.framework.utils.IdGenUtils;
//import org.junit.Before;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.MvcResult;
//import org.springframework.test.web.servlet.RequestBuilder;
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//import org.springframework.web.context.WebApplicationContext;
//
//import javax.annotation.Resource;
//import java.io.BufferedReader;
//import java.io.File;
//import java.io.FileReader;
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.time.LocalTime;
//import java.util.List;
//
///**
// * 测试类
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@Ignore
//public class ApplicationTest {
//
//    @Autowired
//    private WebApplicationContext context;
//
//    @Resource
//    private ICheckAccControlService checkAccControlService;
//
//    @Resource
//    private ICopBizRouteService copBizRouteService;
//
//    @Resource
//    private ICopAgcyInfoService copAgcyInfoService;
//
//    @Resource
//    private IAccControlDao accControlDao;
//
//    @Resource
//    private IAccParamDao accParamDao;
//
//    @Resource
//    private IAccCfgDao accCfgDao;
//
//    @Resource
//    private WriteCheckFileSchedule writeCheckFileSchedule;
//
//    @Resource
//    private EncryptUtils encryptUtils;
//
//    private MockMvc mockMvc;
//
//    @Before
//    public void setupMockMvc() throws Exception {
//        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
//    }
//
//    @Test
//    @Ignore
//    public void contextLoads() throws Exception {
//        for (int i = 0; i < 3; i++) {
//            createOrder();
//        }
//        System.out.println("================a=============");
//    }
//
//    /**
//     * 申请提现订单
//     */
//    @Test
//    @Ignore
//    public void createOrder() throws Exception {
//        GenericDTO<WithdrawReqDTO> genericDTO = new GenericDTO<>();
//        WithdrawReqDTO withdrawReqDTO = new WithdrawReqDTO();
////        withdrawReqDTO.setUserNo("0008550000008002");
//        withdrawReqDTO.setCapTyp("1");
//        withdrawReqDTO.setCorpBusTyp(CorpBusTyp.WITHDRAW);
//        withdrawReqDTO.setCorpBusSubTyp(CorpBusSubTyp.PER_WITHDRAW);
//        withdrawReqDTO.setCcy("RMB");
//        withdrawReqDTO.setCapCorg("ICBC");
//        withdrawReqDTO.setCrdAcTyp("D");
//        withdrawReqDTO.setWcAplAmt(BigDecimal.valueOf(100));
//        withdrawReqDTO.setHoldNo("9527");
//        withdrawReqDTO.setPsnCrpFlg("C");
//        withdrawReqDTO.setMblNo("18912344321");
//        withdrawReqDTO.setUserNm("张三");
////        withdrawReqDTO.setUserId("18912344321");
//        withdrawReqDTO.setIdTyp("0");
//        withdrawReqDTO.setIdNoEnc("");
//        withdrawReqDTO.setCrdNoEnc("Zbl/puag76Q4K5Nmw4tifJhEOjuCsmcqB86crYuJpnU=");
//        withdrawReqDTO.setCapCrdNm("中国工商银行");
//        withdrawReqDTO.setWcRmk("测试申请提现订单");
//        withdrawReqDTO.setReqOrdNo(IdGenUtils.generateIdWithDate("CPO", 10));
//        genericDTO.setBody(withdrawReqDTO);
//
//        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//        java.lang.String requestJson = ow.writeValueAsString(genericDTO);
//        RequestBuilder request = MockMvcRequestBuilders.post("/cpo/orders/payment")
//                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                .content(requestJson);
//
//        MvcResult mvcResult = mockMvc.perform(request).andReturn();
//        int status = mvcResult.getResponse().getStatus();
//        String content = mvcResult.getResponse().getContentAsString();
//        System.out.println("================ status: " + status);
//        System.out.println("================ content: " + content);
//        System.out.println("================ createOrder end =============");
//    }
//
//    /**
//     * 查询提现订单信息 待定
//     */
//    @Test
//    @Ignore
//    public void queryOrder() throws Exception {
//        RequestBuilder request = MockMvcRequestBuilders.get("/cpo/orders/orders/detail/CP201707140000017502");
//        MvcResult mvcResult = mockMvc.perform(request).andReturn();
//        int status = mvcResult.getResponse().getStatus();
//        String content = mvcResult.getResponse().getContentAsString();
//        System.out.println("================ status: " + status);
//        System.out.println("================ content: " + content);
//        System.out.println("================ queryOrder end =============");
//    }
//
//    /**
//     * 增加对账参数
//     */
//    @Test
//    @Ignore
//    public void addChkAccParam() {
//        AccParamDO chkAccParamDO = new AccParamDO();
//        chkAccParamDO.preInsert();
//        chkAccParamDO.preUpdate();
//        chkAccParamDO.setChkPmId(IdGenUtils.generateIdWithDate("CHK", "CHK", 9));
//        chkAccParamDO.setRutCorg("ICBC");
//        chkAccParamDO.setCorpBusTyp("06");
//        chkAccParamDO.setCorpBusSubTyp("0601");
//        chkAccParamDO.setEffFlg("Y");
//        chkAccParamDO.setChkDoFlg("Y");
//        chkAccParamDO.setChkBegTm(LocalTime.of(9, 30, 0));
//        chkAccParamDO.setChkEndTm(LocalTime.of(23, 30,0));
//        chkAccParamDO.setOprId("sys001");
//        checkAccControlService.addChkAccParam(chkAccParamDO);
//    }
//
//    /**
//     * 创建对账批次
//     */
//    @Test
//    @Ignore
//    public void registerChkBatNo() {
//        LocalDate checkDt = DateTimeUtils.getCurrentLocalDate().minusDays(1L);
//        checkAccControlService.registerChkBatNo(checkDt);
//    }
//
//    /**
//     * 开始对账
//     */
//    @Test
//    @Ignore
//    public void beginCheckAcc() {
//        List<AccParamDO> list = accParamDao.queryEffAccParamList();
//        for(AccParamDO chkAccParam : list){
//            AccControlDO chkAccControlDO= accControlDao.queryUnfinishedAccControl(chkAccParam);
//            if (JudgeUtils.isNotNull(chkAccControlDO)) {
//                checkAccControlService.beginCheckAcc(chkAccControlDO);
//            }
//        }
//        System.out.println("====================== 对账结束 ======================");
//    }
//
//    /**
//     * 新增对账配置
//     */
//    @Test
//    @Ignore
//    public void addAccCfgInfo() {
//        AccCfgDO accCfgDO = new AccCfgDO();
//        accCfgDO.setAccCfgId(IdGenUtils.generateIdWithDateTime("CP0", 6));
//        accCfgDO.setRutCorg("ICBC");
//        accCfgDO.setCorpBusTyp("06");
//        accCfgDO.setCorpBusSubTyp("0601");
//        accCfgDO.setChkClazz("com.hisun.lemon.cpo.bankapi.withdraw.CheckAccICBCWithdrawApi");
//        accCfgDO.setGetFileMethod("getCheckFile");
//        accCfgDO.setImportFields("companyDate|companySerino|bankSerno|settleDate|cardNo|payAmt|currType|status");
//        accCfgDO.setImportClazz("com.hisun.lemon.cpo.entity.icbc.AccIcbcWithdrawDO");
//        accCfgDO.setImportMethod("batchInsertCheckDO");
//        accCfgDO.setSplitSign("\\|");
//        accCfgDO.setChkFilePath("E:\\chkfile");
//        accCfgDO.setContinueNum(0);
//        accCfgDO.setSelectDetailMethod("getAccDOList");
//        accCfgDO.setSuccessFlag("2");
//        accCfgDO.setCheckKeyFiled("companySerino");
//        accCfgDO.setCheckKeyBakFiled("");
//        accCfgDO.setCheckAmtFiled("payAmt");
//        accCfgDO.setTxStsFiled("status");
//        accCfgDO.setQueryNum(200);
//        accCfgDO.setUpdateMethod("updateAccDOChkSts");
//        accCfgDO.preInsert();
//        accCfgDO.preUpdate();
//        accCfgDao.insert(accCfgDO);
//    }
//
//    @Test
//    @Ignore
//    public void writeCheckFileTest() throws Exception{
//        writeCheckFileSchedule.writeCheckFile();
////        checkAccControlService.writeCheckFile(LocalDate.now().minusDays(1),"CPO", CorpBusSubTyp.PER_WITHDRAW);
//    }
//
//    @Test
//    @Ignore
//    public void testEnc() throws Exception{
//        String enc = encryptUtils.encrypt("34234234234234",CpoConstants.ENCRYPT);
//        String desc = encryptUtils.encrypt(enc,CpoConstants.DECRYPT);
//        System.out.println("================END================" + desc);
//    }
//
//    @Test
//    @Ignore
//    public void test() throws Exception{
//        String[] tempArray = "CP201707140000017502|10.25|S|".split("\\|");
//        System.out.println(tempArray.length);
//
//        for (int i = 0; i < tempArray.length; i++) {
//            System.out.println(tempArray[i]);
//        }
//
//        System.out.println("======================");
//
//        File file = new File("E:\\0601_20170714.txt");
//        BufferedReader reader = null;
//        String tempString = null;
//        if(file.exists() && file.length() > 0) {
//            try {
//                reader = new BufferedReader(new FileReader(file));
//                while ((tempString = reader.readLine()) != null) {
//                    System.out.println(tempString);
//                    tempArray = tempString.split(CpoConstants.FILE_SPLITSTR);
//                    for (int i = 0; i < tempArray.length; i++) {
//                        System.out.print(tempArray[i] + ", ");
//                    }
//                    System.out.println("\n\n");
//                }
//            } finally {
//                if(reader!=null){
//                    reader.close();
//                }
//            }
//        }
//    }
//
//    /**
//     * 增加合作机构基本信息
//     */
//    @Test
//    @Ignore
//    public void addCopAgcyInfoDO() {
//        CopAgcyInfoDO copAgcyInfoDO = new CopAgcyInfoDO();
//        copAgcyInfoDO.setOrgInfId(DateTimeUtils.getCurrentDateTimeStr());
//        copAgcyInfoDO.setCorpOrgId("AAAAA");
//        copAgcyInfoDO.setCorpOrgNm("AAAAA测试银行");
//        copAgcyInfoDO.setCorpOrgSnm("AAAAA测试银行");
//        copAgcyInfoDO.setCorpOrgTyp("0");
//        copAgcyInfoDO.setCorpAccNm("王五");
//        copAgcyInfoDO.setCorpAccNo("55555");
//        copAgcyInfoDO.setRmk("AAAAA测试银行");
//        copAgcyInfoService.add(copAgcyInfoDO);
//        System.out.println("============end============");
//    }
//
//}
