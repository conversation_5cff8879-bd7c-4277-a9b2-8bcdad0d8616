/*
MySQL Backup
Source Server Version: 5.7.18
Source Database: seatelpay_cpo
Date: 2017/8/7 17:01:05
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
--  Table structure for `cpo_acc_cfg`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_acc_cfg`;
CREATE TABLE `cpo_acc_cfg` (
  `ACC_CFG_ID` varchar(32) NOT NULL COMMENT 'id',
  `RUT_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径机构',
  `CORP_BUS_TYP` char(2) NOT NULL DEFAULT '' COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL DEFAULT '' COMMENT '业务子类型',
  `CHK_CLAZZ` varchar(128) NOT NULL DEFAULT ' ' COMMENT '获取文件的类',
  `GET_FILE_METHOD` varchar(128) NOT NULL DEFAULT ' ' COMMENT '获取文件的方法',
  `IMPORT_FIELDS` varchar(256) NOT NULL DEFAULT ' ' COMMENT '入库的字段',
  `IMPORT_CLAZZ` varchar(128) NOT NULL DEFAULT ' ' COMMENT '入库的明细类',
  `IMPORT_METHOD` varchar(64) NOT NULL DEFAULT ' ' COMMENT '入库的方法',
  `SPLIT_SIGN` varchar(8) NOT NULL DEFAULT ' ' COMMENT '分隔符',
  `CHK_FILE_PATH` varchar(64) NOT NULL DEFAULT ' ' COMMENT '文件路径',
  `CONTINUE_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '跳过多少行',
  `SELECT_DETAIL_METHOD` varchar(64) NOT NULL DEFAULT ' ' COMMENT '查询明细方法',
  `SUCCESS_FLAG` varchar(8) NOT NULL DEFAULT ' ' COMMENT '对账成功标志',
  `CHECK_KEY_FILED` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账键值对应的域',
  `CHECK_KEY_BAK_FILED` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账键值备份对应的域',
  `CHECK_AMT_FILED` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账金额对应的域',
  `TX_STS_FILED` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账状态对应的域',
  `QUERY_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '对账时每次查询的笔数',
  `UPDATE_METHOD` varchar(32) NOT NULL DEFAULT ' ' COMMENT '更新对账状态方法',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`ACC_CFG_ID`),
  UNIQUE KEY `in1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对账配置表';

-- ----------------------------
--  Table structure for `cpo_acc_control`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_acc_control`;
CREATE TABLE `cpo_acc_control` (
  `CHK_BAT_NO` varchar(32) NOT NULL COMMENT '批次号',
  `RUT_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构',
  `CORP_BUS_TYP` char(2) NOT NULL DEFAULT '' COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL DEFAULT '' COMMENT '业务子类型',
  `CHK_FIL_DT` date DEFAULT NULL COMMENT '对账文件日期',
  `CHK_FIL_NM` varchar(128) NOT NULL DEFAULT ' ' COMMENT '对账文件名',
  `CHK_FIL_STS` char(1) NOT NULL DEFAULT '0' COMMENT '对账状态,0未对账，1文件已取，2文件入库，3已对账，4对账完成',
  `FILE_RCV_DT` date DEFAULT NULL COMMENT '获取对账文件日期',
  `CHK_BEG_TM` time NOT NULL COMMENT '对账开始时间',
  `CHK_END_TM` time NOT NULL COMMENT '对账结束时间',
  `CHK_DT` date DEFAULT NULL COMMENT '对账日期',
  `FIL_TOT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '对账总金额',
  `FIL_TOT_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '对账总笔数',
  `TOT_MCH_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '对平总金额',
  `TOT_MCH_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '对平总笔数',
  `ERR_TOT_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '差错笔数',
  `ERR_TOT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '差错金额',
  `LONG_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '长款金额',
  `LONG_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '长款笔数',
  `SHORT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '短款金额',
  `SHORT_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '短款笔数',
  `DOUBT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '存疑金额',
  `DOUBT_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '存疑笔数',
  `DBT_ERR_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '存疑转差错金额',
  `DBT_ERR_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '存疑转差错笔数',
  `TOT_DR_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '借方总金额',
  `TOT_DR_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '借方总笔数',
  `TOT_CR_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '贷方总金额',
  `TOT_CR_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '贷方总笔数',
  `PAY_TOT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '应付总金额',
  `RCV_TOT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '应收总金额',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`CHK_BAT_NO`),
  UNIQUE KEY `Index_1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`,`CHK_FIL_DT`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金模块对账总控表';

-- ----------------------------
--  Table structure for `cpo_acc_error`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_acc_error`;
CREATE TABLE `cpo_acc_error` (
  `CHK_ER_ID` varchar(32) NOT NULL COMMENT '主键',
  `RUT_CORG` varchar(16) NOT NULL COMMENT '路径合作机构',
  `CORP_BUS_TYP` varchar(2) NOT NULL COMMENT '业务合作类型',
  `CORP_BUS_SUB_TYP` varchar(4) NOT NULL COMMENT '业务合作子类型',
  `ERR_KEY_ID` varchar(64) NOT NULL COMMENT '差错键值',
  `CHK_ERR_DT` date NOT NULL COMMENT '差错创建日期',
  `CHK_ERR_TM` time NOT NULL COMMENT '差错创建时间',
  `CHK_BAT_NO` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账批次',
  `SPL_ABLE_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '补单允许标识，Y允许，N不允许',
  `CAN_ABLE_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '撤单允许标识，Y允许，N不允许',
  `CHK_ERR_TYP` char(1) NOT NULL DEFAULT '' COMMENT '差错类型，2：短款差错，3：长款差错，4：金额不符\n            ',
  `ERR_STS` char(1) NOT NULL DEFAULT '0' COMMENT '差错状态，0：待处理，1：已补单，2：已撤单，3：人工取消\n            ',
  `OTH_TX_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '对方交易金额',
  `MY_TX_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '我方交易金额',
  `PSN_CRP_FLG` char(1) NOT NULL DEFAULT 'C' COMMENT '个人/商户标识，B商户，C个人',
  `OLD_TX_DT` date DEFAULT NULL COMMENT '原交易日期',
  `OLD_JRN_NO` varchar(32) DEFAULT ' ' COMMENT '原交易流水号',
  `OLD_ORD_NO` varchar(32) DEFAULT ' ' COMMENT '原交易订单号',
  `OLD_CORG_KEY` varchar(32) DEFAULT ' ' COMMENT '原路径合作机构对账主键',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`CHK_ER_ID`),
  UNIQUE KEY `Index_1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`,`ERR_KEY_ID`),
  KEY `Index_2` (`CHK_BAT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对账差错表';

-- ----------------------------
--  Table structure for `cpo_acc_icbc_withdraw`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_acc_icbc_withdraw`;
CREATE TABLE `cpo_acc_icbc_withdraw` (
  `company_date` varchar(8) DEFAULT ' ' COMMENT '公司方日期',
  `company_serino` varchar(64) DEFAULT ' ' COMMENT '公司方交易流水号(同机构交易流水号)',
  `bank_serno` varchar(64) DEFAULT ' ' COMMENT '银行流水号',
  `settle_date` varchar(8) DEFAULT ' ' COMMENT '清算日期，格式：YYYYMMDD',
  `card_no` varchar(64) DEFAULT ' ' COMMENT '银行卡号',
  `pay_amt` decimal(13,2) DEFAULT '0.00' COMMENT '银行订单金额(单位:元)',
  `curr_type` varchar(3) DEFAULT ' ' COMMENT '币种',
  `status` varchar(1) DEFAULT ' ' COMMENT '明细状态 2-成功; 3-失败',
  `CHK_ID` varchar(64) NOT NULL COMMENT '新增对账明细主键',
  `CHK_BAT_NO` varchar(64) DEFAULT ' ' COMMENT '新增对账批次号',
  `RUT_CORG` varchar(16) DEFAULT ' ' COMMENT '新增路径合作机构',
  `CORP_BUS_TYP` varchar(2) DEFAULT ' ' COMMENT '新增合作业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) DEFAULT ' ' COMMENT '新增合作业务子类型',
  `CHK_FIL_DT` date DEFAULT NULL COMMENT '新增对账文件日期',
  `CHK_FIL_NM` varchar(64) DEFAULT ' ' COMMENT '新增对账文件名称',
  `CHK_STS` varchar(1) DEFAULT '0' COMMENT '新增对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '新增时间戳',
  PRIMARY KEY (`CHK_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工行对账明细表';

-- ----------------------------
--  Table structure for `cpo_acc_param`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_acc_param`;
CREATE TABLE `cpo_acc_param` (
  `CHK_PM_ID` varchar(32) NOT NULL COMMENT 'id',
  `RUT_CORG` varchar(16) NOT NULL COMMENT '路径机构',
  `CORP_BUS_TYP` varchar(16) NOT NULL COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` varchar(16) NOT NULL COMMENT '业务子类型',
  `EFF_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '生效标识，Y生效，N不生效',
  `CHK_DO_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '是否对账标识，Y对账，N不对账',
  `BAT_CHK_FLG` char(1) NOT NULL DEFAULT 'B' COMMENT '批量对账标识，B批量,O:实时对账',
  `CHK_BEG_TM` time NOT NULL COMMENT '对账开始时间',
  `CHK_END_TM` time NOT NULL COMMENT '对账结束时间',
  `SPL_ABLE_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '补单标识，Y支持，N不支持',
  `CAN_ABLE_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '撤单标识，Y支持，N不支持',
  `AU_SK_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '自动跳账标识，Y支持，N不支持',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`CHK_PM_ID`),
  UNIQUE KEY `Index_1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金模块对账参数表';

-- ----------------------------
--  Table structure for `cpo_card_bin`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_card_bin`;
CREATE TABLE `cpo_card_bin` (
  `BIN_ID` varchar(32) NOT NULL COMMENT '主键',
  `CRD_BIN` varchar(32) NOT NULL COMMENT '卡BIN',
  `CAP_CORG` varchar(16) NOT NULL COMMENT '资金机构',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT 'D' COMMENT '卡类型，D借记卡，C贷记卡',
  `CRD_LTH` int(11) NOT NULL DEFAULT '0' COMMENT '卡长度',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`BIN_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金模块BIN表';

-- ----------------------------
--  Table structure for `cpo_cop_agcy_biz`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_cop_agcy_biz`;
CREATE TABLE `cpo_cop_agcy_biz` (
  `ORG_BUS_ID` varchar(32) NOT NULL COMMENT '主键',
  `CORP_ORG_ID` varchar(8) NOT NULL COMMENT '合作机构编号',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '合作业务子类型',
  `BUS_EFF_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '业务生效标志，0失效，1生效',
  `CRE_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '创建人ID',
  `UPD_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '修改人ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`ORG_BUS_ID`),
  UNIQUE KEY `Index_1` (`CORP_ORG_ID`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合作机构业务表';

-- ----------------------------
--  Table structure for `cpo_cop_agcy_info`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_cop_agcy_info`;
CREATE TABLE `cpo_cop_agcy_info` (
  `ORG_INF_ID` varchar(32) NOT NULL COMMENT '主键',
  `CORP_ORG_ID` varchar(16) NOT NULL COMMENT '合作机构编号',
  `CORP_ORG_NM` varchar(64) NOT NULL DEFAULT ' ' COMMENT '合作机构名称',
  `CORP_ORG_SNM` varchar(64) NOT NULL DEFAULT ' ' COMMENT '合作机构名简称',
  `CORP_ORG_TYP` char(1) NOT NULL DEFAULT '0' COMMENT '合作机构类型，0银行，1非银行',
  `CRE_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '创建柜员ID',
  `UPD_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '修改柜员ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`ORG_INF_ID`),
  UNIQUE KEY `Index_1` (`CORP_ORG_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合作机构基本信息表';

-- ----------------------------
--  Table structure for `cpo_cop_biz_route`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_cop_biz_route`;
CREATE TABLE `cpo_cop_biz_route` (
  `RUT_INF_ID` varchar(32) NOT NULL COMMENT '主键',
  `CRD_CORP_ORG` varchar(16) NOT NULL COMMENT '资金合作机构编号',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '合作业务子类型',
  `RUT_CORP_ORG` varchar(16) NOT NULL COMMENT '路径合作机构号',
  `CRD_AC_TYP` char(1) NOT NULL COMMENT '银行卡类型，D借记卡，C贷记卡',
  `RUT_EFF_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '路由生效标志，0失效，1生效',
  `PRI_LVL` int(11) NOT NULL DEFAULT '99' COMMENT '0-99，数字越大级别越高',
  `LOW_AMT` decimal(13,2) NOT NULL COMMENT '最低金额',
  `HIGH_AMT` decimal(13,2) NOT NULL COMMENT '最高金额',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '创建柜员ID',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`RUT_INF_ID`),
  UNIQUE KEY `Index_1` (`CRD_CORP_ORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`,`RUT_CORP_ORG`,`CRD_AC_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='业务合作路由表';

-- ----------------------------
--  Table structure for `cpo_lock`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_lock`;
CREATE TABLE `cpo_lock` (
  `LOCK_ID` varchar(32) NOT NULL COMMENT '锁ID',
  `LOCK_NAME` varchar(128) NOT NULL DEFAULT ' ' COMMENT '锁名称',
  `LOCK_STS` varchar(2) NOT NULL DEFAULT ' ' COMMENT '锁状态，U：未加锁；L：已加锁',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '更新时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`LOCK_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对账批次锁状态表';

-- ----------------------------
--  Table structure for `cpo_withdraw_order`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_withdraw_order`;
CREATE TABLE `cpo_withdraw_order` (
  `WC_ORD_NO` varchar(32) NOT NULL COMMENT '内部订单号',
  `ORD_DT` date NOT NULL COMMENT '订单日期',
  `ORD_TM` time NOT NULL COMMENT '订单时间',
  `CCY` char(3) NOT NULL DEFAULT '' COMMENT '币种',
  `CAP_TYP` char(1) NOT NULL DEFAULT '1' COMMENT '资金种类，1现金',
  `CAP_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '资金合作机构号',
  `RUT_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构号',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT 'D' COMMENT '卡种类，D借记卡，C贷记卡',
  `CORP_BUS_TYP` varchar(2) NOT NULL DEFAULT ' ' COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) NOT NULL DEFAULT ' ' COMMENT '合作业务子类型',
  `REQ_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '请求方订单号',
  `REQ_ORD_DT` date NOT NULL COMMENT '请求方订单日期',
  `REQ_ORD_TM` time NOT NULL COMMENT '请求方订单时间',
  `AGR_PAY_DT` date NOT NULL COMMENT '协议付款日',
  `STL_AC_PERD` varchar(16) NOT NULL DEFAULT ' ' COMMENT '清算周期',
  `WC_APL_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '提现金额',
  `HOLD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '冻结编号',
  `PSN_CRP_FLG` char(1) NOT NULL DEFAULT 'C' COMMENT '个人/商户标识',
  `MBL_NO` varchar(16) NOT NULL DEFAULT ' ' COMMENT '手机号',
  `USER_NM` varchar(32) NOT NULL DEFAULT ' ' COMMENT '用户/商户名称',
  `USER_ID` varchar(32) NOT NULL DEFAULT ' ' COMMENT '内部用户号/商户号',
  `ID_TYP` char(2) NOT NULL DEFAULT '' COMMENT '证件类型',
  `ID_NO_ENC` varchar(128) NOT NULL DEFAULT ' ' COMMENT '证件号码',
  `CRD_NO_ENC` varchar(128) NOT NULL DEFAULT ' ' COMMENT '卡号',
  `CAP_CRD_NM` varchar(32) NOT NULL DEFAULT ' ' COMMENT '用户名',
  `WC_RMK` varchar(128) NOT NULL DEFAULT ' ' COMMENT '提现备注',
  `ORD_STS` varchar(2) NOT NULL DEFAULT ' ' COMMENT '订单状态',
  `WC_WF_STS` varchar(2) NOT NULL DEFAULT ' ' COMMENT '提现审批工作流状态',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '审批人ID',
  `WC_WF_RSN` varchar(128) NOT NULL DEFAULT ' ' COMMENT '审批拒绝原因',
  `WC_BAT_NO` varchar(32) NOT NULL DEFAULT ' ' COMMENT '提现内部批次号',
  `WC_BAT_SEQ` varchar(24) NOT NULL DEFAULT ' ' COMMENT '提现批次子序号',
  `POST_DT` date NOT NULL COMMENT '记账日期',
  `POST_TM` time NOT NULL COMMENT '记账时间',
  `AUTO_PAY_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '自动付款标志，0：自动，1：手工',
  `WDC_AUTO_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '实时付款标识，0实时，1，非实时',
  `PAYTM_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '付款时间内标识，0付款时间内，1付款时间外',
  `NOTIFY_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '提现结果通知标识: Y-已通知；N-未通知',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`WC_ORD_NO`),
  KEY `Index_1` (`ORD_DT`),
  KEY `Index_2` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金流出订单表';

-- ----------------------------
--  Table structure for `cpo_withdraw_param`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_withdraw_param`;
CREATE TABLE `cpo_withdraw_param` (
  `BUS_PAR_ID` varchar(20) NOT NULL COMMENT '主键',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '付款类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '付款子类型',
  `CAP_TYP` char(1) NOT NULL COMMENT '资金种类，1现金',
  `PSN_CRP_FLG` char(1) NOT NULL COMMENT '个人/商户标识，C个人，B商户',
  `HOLD_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '冻结标识，0不冻结，1冻结',
  `HOLD_DESC` varchar(128) NOT NULL DEFAULT ' ' COMMENT '冻结描述',
  `AUTO_PAY_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '自动付款标识，0自动，1手工',
  `RSK_CHK_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '风控检查标识，0不检查，1检查',
  `WDC_AUTO_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '实时付款标识，0实时，1，非实时',
  `RRC_CML_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '风控累积标识，0不累积，1累积',
  `PAYTM_CHK_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '付款时间检查标识，0不检查，1检查',
  `CHK_USR_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '用户状态检查标识，0不检查，1检查',
  `PAUSE_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '付款失败暂停标识，0不暂停，1暂停',
  `SMS_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '短信下发标识，0不下发，1下发',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`BUS_PAR_ID`),
  UNIQUE KEY `Index_1` (`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`,`CAP_TYP`,`PSN_CRP_FLG`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金流出业务参数表';

-- ----------------------------
--  Table structure for `cpo_withdraw_suborder`
-- ----------------------------
DROP TABLE IF EXISTS `cpo_withdraw_suborder`;
CREATE TABLE `cpo_withdraw_suborder` (
  `SUB_ORD_NO` varchar(32) NOT NULL COMMENT '子订单号',
  `WC_ORD_NO` varchar(32) NOT NULL COMMENT '订单号',
  `RUT_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构号',
  `CAP_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '资金合作机构号',
  `CORP_BUS_TYP` char(2) NOT NULL DEFAULT '' COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL DEFAULT '' COMMENT '合作业务子类型',
  `WC_APL_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '提现金额',
  `ORD_STS` varchar(2) NOT NULL DEFAULT ' ' COMMENT '订单状态',
  `ORD_DT` date NOT NULL COMMENT '订单日期',
  `AC_DT` date NOT NULL COMMENT '会计日期',
  `RUT_CORG_JRN` varchar(16) NOT NULL DEFAULT ' ' COMMENT '银行返回流水号',
  `RUT_CORG_DT` date DEFAULT NULL COMMENT '银行返回日期',
  `RUT_CORG_TM` time DEFAULT NULL COMMENT '银行返回时间',
  `ORG_RSP_CD` varchar(10) NOT NULL DEFAULT ' ' COMMENT '返回码',
  `ORG_RSP_MSG` varchar(256) NOT NULL DEFAULT ' ' COMMENT '返回信息',
  `CHK_KEY` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账外键(发往银行的订单号)',
  `CHK_STS` char(1) NOT NULL DEFAULT '0' COMMENT '对账状态 0-未对账；1-对账成功；2-我方有机构无；3-机构有我方无；4-金额错；5-存疑',
  `CHK_DT` date DEFAULT NULL COMMENT '对账日期',
  `CHK_TM` time DEFAULT NULL COMMENT '对账时间',
  `CAV_DT` date DEFAULT NULL COMMENT '核销日期',
  `CAV_TM` time DEFAULT NULL COMMENT '核销时间',
  `CAV_OPER` varchar(16) NOT NULL DEFAULT ' ' COMMENT '核销操作员',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`SUB_ORD_NO`),
  UNIQUE KEY `withdraw_suborder_ui1` (`CHK_KEY`),
  KEY `withdraw_suborder_in1` (`WC_ORD_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金流出子订单表';

-- ----------------------------
--  Records 
-- ----------------------------
INSERT INTO `cpo_acc_cfg` VALUES ('20170728163056000501','ICBC','06','0601','com.hisun.lemon.cpo.bankapi.withdraw.CheckAccICBCWithdrawApi','getCheckFile','companyDate|companySerino|bankSerno|settleDate|cardNo|payAmt|currType|status','com.hisun.lemon.cpo.entity.icbc.AccIcbcWithdrawDO','batchInsertCheckDO','\\|','E:\\chkfile','0','getAccDOList','2','companySerino','','payAmt','status','200','updateAccDOChkSts','2017-07-28 16:30:56','2017-07-28 16:30:56','2017-07-28 16:30:46');
INSERT INTO `cpo_acc_control` VALUES ('CHK20170728000038001','ICBC','06','0601','2017-07-27',' ','0',NULL,'09:30:00','23:30:00','2017-07-27','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-07-28 16:30:58','2017-07-28 16:30:58','2017-07-28 15:56:51'), ('CHK20170729000038501','ICBC','06','0601','2017-07-28',' ','0',NULL,'09:30:00','23:30:00','2017-07-28','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-07-29 00:00:02','2017-07-29 00:00:02','2017-07-28 15:56:51'), ('CHK20170730000039001','ICBC','06','0601','2017-07-29',' ','0',NULL,'09:30:00','23:30:00','2017-07-29','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-07-30 00:00:01','2017-07-30 00:00:01','2017-07-28 15:56:51'), ('CHK20170731000039002','ICBC','06','0601','2017-07-30',' ','0',NULL,'09:30:00','23:30:00','2017-07-30','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-07-31 00:00:00','2017-07-31 00:00:00','2017-07-28 15:56:51'), ('CHK20170801000039501','ICBC','06','0601','2017-07-31',' ','0',NULL,'09:30:00','23:30:00','2017-07-31','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-08-01 00:00:02','2017-08-01 00:00:02','2017-07-28 15:56:51'), ('CHK20170802000040001','ICBC','06','0601','2017-08-01',' ','0',NULL,'09:30:00','23:30:00','2017-08-01','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-08-02 00:00:03','2017-08-02 00:00:03','2017-08-02 00:00:03'), ('CHK20170803000040501','ICBC','06','0601','2017-08-02',' ','0',NULL,'09:30:00','23:30:00','2017-08-02','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-08-03 00:00:01','2017-08-03 00:00:01','2017-08-03 00:00:01'), ('CHK20170804000041001','ICBC','06','0601','2017-08-03',' ','0',NULL,'09:30:00','23:30:00','2017-08-03','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-08-04 00:00:01','2017-08-04 00:00:01','2017-08-04 00:00:01'), ('CHK20170805000041501','ICBC','06','0601','2017-08-04',' ','0',NULL,'09:30:00','23:30:00','2017-08-04','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-08-05 00:00:00','2017-08-05 00:00:00','2017-08-05 00:00:00'), ('CHK20170806000041502','ICBC','06','0601','2017-08-05',' ','0',NULL,'09:30:00','23:30:00','2017-08-05','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-08-06 00:00:00','2017-08-06 00:00:00','2017-08-06 00:00:00'), ('CHK20170807000041503','ICBC','06','0601','2017-08-06',' ','0',NULL,'09:30:00','23:30:00','2017-08-06','0.00','0','0.00','0','0','0.00','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0','0.00','0.00',' ','2017-08-07 00:00:00','2017-08-07 00:00:00','2017-08-07 00:00:00');
INSERT INTO `cpo_acc_param` VALUES ('CHK20170728000037501','ICBC','06','0601','Y','Y','B','09:30:00','23:30:00','N','N','N','sys001','2017-07-28 15:56:59','2017-07-28 15:56:59','2017-07-28 15:56:51');
