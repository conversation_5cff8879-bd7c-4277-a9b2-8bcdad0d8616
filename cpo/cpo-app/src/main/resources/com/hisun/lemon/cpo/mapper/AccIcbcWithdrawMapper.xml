<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.icbc.IAccIcbcWithdrawDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.icbc.AccIcbcWithdrawDO" >
        <id column="CHK_ID" property="chkId" jdbcType="VARCHAR" />
        <result column="company_date" property="companyDate" jdbcType="VARCHAR" />
        <result column="company_serino" property="companySerino" jdbcType="VARCHAR" />
        <result column="bank_serno" property="bankSerno" jdbcType="VARCHAR" />
        <result column="settle_date" property="settleDate" jdbcType="VARCHAR" />
        <result column="card_no" property="cardNo" jdbcType="VARCHAR" />
        <result column="pay_amt" property="payAmt" jdbcType="DECIMAL" />
        <result column="curr_type" property="currType" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="CHK_BAT_NO" property="chkBatNo" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="CHK_FIL_DT" property="chkFilDt" jdbcType="DATE" />
        <result column="CHK_FIL_NM" property="chkFilNm" jdbcType="VARCHAR" />
        <result column="CHK_STS" property="chkSts" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        CHK_ID, company_date, company_serino, bank_serno, settle_date, card_no,
        pay_amt, curr_type, status, CHK_BAT_NO, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP,
        CHK_FIL_DT, CHK_FIL_NM, CHK_STS
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpo_acc_icbc_withdraw
        where CHK_ID = #{chkId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_acc_icbc_withdraw
        where CHK_ID = #{chkId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.icbc.AccIcbcWithdrawDO" >
        insert into cpo_acc_icbc_withdraw
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="chkId != null" >
                CHK_ID,
            </if>
            <if test="companyDate != null" >
                company_date,
            </if>
            <if test="companySerino != null" >
                company_serino,
            </if>
            <if test="bankSerno != null" >
                bank_serno,
            </if>
            <if test="settleDate != null" >
                settle_date,
            </if>
            <if test="cardNo != null" >
                card_no,
            </if>
            <if test="payAmt != null" >
                pay_amt,
            </if>
            <if test="currType != null" >
                curr_type,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="chkBatNo != null" >
                CHK_BAT_NO,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="chkFilDt != null" >
                CHK_FIL_DT,
            </if>
            <if test="chkFilNm != null" >
                CHK_FIL_NM,
            </if>
            <if test="chkSts != null" >
                CHK_STS
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="chkId != null" >
                #{chkId,jdbcType=VARCHAR},
            </if>
            <if test="companyDate != null" >
                #{companyDate,jdbcType=VARCHAR},
            </if>
            <if test="companySerino != null" >
                #{companySerino,jdbcType=VARCHAR},
            </if>
            <if test="bankSerno != null" >
                #{bankSerno,jdbcType=VARCHAR},
            </if>
            <if test="settleDate != null" >
                #{settleDate,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null" >
                #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="payAmt != null" >
                #{payAmt,jdbcType=DECIMAL},
            </if>
            <if test="currType != null" >
                #{currType,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="chkBatNo != null" >
                #{chkBatNo,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="chkFilDt != null" >
                #{chkFilDt,jdbcType=DATE},
            </if>
            <if test="chkFilNm != null" >
                #{chkFilNm,jdbcType=VARCHAR},
            </if>
            <if test="chkSts != null" >
                #{chkSts,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.icbc.AccIcbcWithdrawDO" >
        update cpo_acc_icbc_withdraw
        <set >
            <if test="companyDate != null" >
                company_date = #{companyDate,jdbcType=VARCHAR},
            </if>
            <if test="companySerino != null" >
                company_serino = #{companySerino,jdbcType=VARCHAR},
            </if>
            <if test="bankSerno != null" >
                bank_serno = #{bankSerno,jdbcType=VARCHAR},
            </if>
            <if test="settleDate != null" >
                settle_date = #{settleDate,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null" >
                card_no = #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="payAmt != null" >
                pay_amt = #{payAmt,jdbcType=DECIMAL},
            </if>
            <if test="currType != null" >
                curr_type = #{currType,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="chkBatNo != null" >
                CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="chkFilDt != null" >
                CHK_FIL_DT = #{chkFilDt,jdbcType=DATE},
            </if>
            <if test="chkFilNm != null" >
                CHK_FIL_NM = #{chkFilNm,jdbcType=VARCHAR},
            </if>
            <if test="chkSts != null" >
                CHK_STS = #{chkSts,jdbcType=VARCHAR}
            </if>
        </set>
        where CHK_ID = #{chkId,jdbcType=VARCHAR}
    </update>

    <!-- 批量插入银行对账明细 -->
    <insert id="batchInsertCheckDO" parameterType="java.util.List" >
        insert into cpo_acc_icbc_withdraw (
            CHK_ID, company_date, company_serino, bank_serno, settle_date, card_no,
            pay_amt, curr_type, status, CHK_BAT_NO, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP,
            CHK_FIL_DT, CHK_FIL_NM)
        <foreach collection="checkDOList" item="checkDO" index="index" separator="union all" >
            select
            #{checkDO.chkId,jdbcType=VARCHAR},
            #{checkDO.companyDate,jdbcType=VARCHAR},
            #{checkDO.companySerino,jdbcType=VARCHAR},
            #{checkDO.bankSerno,jdbcType=VARCHAR},
            #{checkDO.settleDate,jdbcType=VARCHAR},
            #{checkDO.cardNo,jdbcType=VARCHAR},
            #{checkDO.payAmt,jdbcType=DECIMAL},
            #{checkDO.currType,jdbcType=VARCHAR},
            #{checkDO.status,jdbcType=VARCHAR},
            #{checkDO.chkBatNo,jdbcType=VARCHAR},
            #{checkDO.rutCorg,jdbcType=VARCHAR},
            #{checkDO.corpBusTyp,jdbcType=VARCHAR},
            #{checkDO.corpBusSubTyp,jdbcType=VARCHAR},
            #{checkDO.chkFilDt,jdbcType=DATE},
            #{checkDO.chkFilNm,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <!--根据对账批次号查询一条记录，判断该批次是否已插入对账明细数据-->
    <select id="getAccIcbcWithdrawDOCount" resultType="java.lang.Integer" >
        select count(1) from cpo_acc_icbc_withdraw
        where CHK_BAT_NO = #{chkBatNo, jdbcType=VARCHAR}
        limit 1
    </select>

    <!--工行提现对账：获取银行对账明细 List-->
    <select id="getAccIcbcWithdrawDOList" resultMap="BaseResultMap" >
        select company_serino, pay_amt, status, CHK_BAT_NO, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP
          from cpo_acc_icbc_withdraw
        where CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR}
          and status = #{txSts,jdbcType=VARCHAR}
        ORDER BY company_serino ASC
        limit #{beginNum,jdbcType=INTEGER}, #{countNum,jdbcType=INTEGER}
    </select>

    <!--更新银行明细的对账状态-->
    <update id="updateAccIcbcWithdrawDOChkSts" >
        update cpo_acc_icbc_withdraw
          set CHK_STS = #{chkSts,jdbcType=VARCHAR}
        where company_serino = #{checkKey,jdbcType=VARCHAR}
          and CHK_STS = '0'
          and status = #{txSts,jdbcType=VARCHAR}
    </update>

</mapper>