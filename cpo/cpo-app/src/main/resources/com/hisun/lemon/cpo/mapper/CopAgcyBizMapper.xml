<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.ICopAgcyBizDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.CopAgcyBizDO" >
        <id column="ORG_BUS_ID" property="orgBusId" jdbcType="VARCHAR" />
        <result column="CORP_ORG_ID" property="corpOrgId" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="BUS_EFF_FLG" property="busEffFlg" jdbcType="CHAR" />
        <result column="CRE_OPR_ID" property="creOprId" jdbcType="CHAR" />
        <result column="UPD_OPR_ID" property="updOprId" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ORG_BUS_ID, CORP_ORG_ID, CORP_BUS_TYP, CORP_BUS_SUB_TYP, BUS_EFF_FLG, CRE_OPR_ID, 
        UPD_OPR_ID, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpo_cop_agcy_biz
        where ORG_BUS_ID = #{orgBusId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_cop_agcy_biz
        where ORG_BUS_ID = #{orgBusId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.CopAgcyBizDO" >
        insert into cpo_cop_agcy_biz
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orgBusId != null" >
                ORG_BUS_ID,
            </if>
            <if test="corpOrgId != null" >
                CORP_ORG_ID,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="busEffFlg != null" >
                BUS_EFF_FLG,
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID,
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orgBusId != null" >
                #{orgBusId,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgId != null" >
                #{corpOrgId,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="busEffFlg != null" >
                #{busEffFlg,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                #{creOprId,jdbcType=CHAR},
            </if>
            <if test="updOprId != null" >
                #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.CopAgcyBizDO" >
        update cpo_cop_agcy_biz
        <set >
            <if test="corpOrgId != null" >
                CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="busEffFlg != null" >
                BUS_EFF_FLG = #{busEffFlg,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID = #{creOprId,jdbcType=CHAR},
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ORG_BUS_ID = #{orgBusId,jdbcType=VARCHAR}
    </update>
</mapper>