<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.IPwmWithdrawOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.PwmWithdrawOrderDO" >
        <id column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="order_tm" property="orderTm" jdbcType="TIMESTAMP" />
        <result column="order_exp_tm" property="orderExpTm" jdbcType="TIMESTAMP" />
        <result column="ac_tm" property="acTm" jdbcType="DATE" />
        <result column="order_ccy" property="orderCcy" jdbcType="CHAR" />
        <result column="order_succ_tm" property="orderSuccTm" jdbcType="TIMESTAMP" />
        <result column="wc_type" property="wcType" jdbcType="CHAR" />
        <result column="tx_type" property="txType" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="wc_apply_amt" property="wcApplyAmt" jdbcType="DECIMAL" />
        <result column="wc_act_amt" property="wcActAmt" jdbcType="DECIMAL" />
        <result column="wc_total_amt" property="wcTotalAmt" jdbcType="DECIMAL" />
        <result column="fee_amt" property="feeAmt" jdbcType="DECIMAL" />
        <result column="pay_urge_flg" property="payUrgeFlg" jdbcType="CHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="agr_no" property="agrNo" jdbcType="VARCHAR" />
        <result column="cap_corg_no" property="capCorgNo" jdbcType="VARCHAR" />
        <result column="cap_card_no" property="capCardNo" jdbcType="VARCHAR" />
        <result column="cap_card_type" property="capCardType" jdbcType="CHAR" />
        <result column="cap_card_name" property="capCardName" jdbcType="VARCHAR" />
        <result column="wc_remark" property="wcRemark" jdbcType="VARCHAR" />
        <result column="ntf_mbl" property="ntfMbl" jdbcType="VARCHAR" />
        <result column="order_status" property="orderStatus" jdbcType="CHAR" />
        <result column="rsp_order_no" property="rspOrderNo" jdbcType="VARCHAR" />
        <result column="rsp_succ_tm" property="rspSuccTm" jdbcType="TIMESTAMP" />
        <result column="bus_cnl" property="busCnl" jdbcType="VARCHAR" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        order_no, order_tm, order_exp_tm, ac_tm, order_ccy, order_succ_tm, wc_type, tx_type, 
        bus_type, wc_apply_amt, wc_act_amt, fee_amt, pay_urge_flg, user_id, user_name, agr_no, 
        cap_corg_no, cap_card_no, cap_card_type, cap_card_name, wc_remark, ntf_mbl, order_status, 
        rsp_order_no, rsp_succ_tm, bus_cnl, modify_time, wc_total_amt,
        create_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from pwm_withdraw_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>


</mapper>