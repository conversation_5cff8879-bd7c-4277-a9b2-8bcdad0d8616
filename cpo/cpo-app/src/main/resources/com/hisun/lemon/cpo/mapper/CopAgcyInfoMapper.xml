<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.ICopAgcyInfoDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.CopAgcyInfoDO" >
        <id column="ORG_INF_ID" property="orgInfId" jdbcType="VARCHAR" />
        <result column="CORP_ORG_ID" property="corpOrgId" jdbcType="VARCHAR" />
        <result column="CORP_ORG_NM" property="corpOrgNm" jdbcType="VARCHAR" />
        <result column="CORP_ORG_SNM" property="corpOrgSnm" jdbcType="VARCHAR" />
        <result column="CORP_ORG_TYP" property="corpOrgTyp" jdbcType="CHAR" />
        <result column="CRE_OPR_ID" property="creOprId" jdbcType="VARCHAR" />
        <result column="UPD_OPR_ID" property="updOprId" jdbcType="VARCHAR" />
        <result column="CORP_ACC_NM" property="corpAccNm" jdbcType="VARCHAR" />
        <result column="CORP_ACC_NO" property="corpAccNo" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ORG_INF_ID, CORP_ORG_ID, CORP_ORG_NM, CORP_ORG_SNM, CORP_ORG_TYP, CRE_OPR_ID, UPD_OPR_ID, 
        CREATE_TIME, MODIFY_TIME, TM_SMP, CORP_ACC_NM, CORP_ACC_NO, RMK
    </sql>

    <sql id="Column_List" >
        ORG_INF_ID, CORP_ORG_ID, CORP_ORG_NM, CORP_ORG_SNM, CORP_ORG_TYP
    </sql>

    <select id="getCopAgcyList" resultMap="BaseResultMap" >
        select
        <include refid="Column_List" />
        from cpo_cop_agcy_info
        limit 2
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpo_cop_agcy_info
        where ORG_INF_ID = #{orgInfId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_cop_agcy_info
        where ORG_INF_ID = #{orgInfId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.CopAgcyInfoDO" >
        insert into cpo_cop_agcy_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orgInfId != null" >
                ORG_INF_ID,
            </if>
            <if test="corpOrgId != null" >
                CORP_ORG_ID,
            </if>
            <if test="corpOrgNm != null" >
                CORP_ORG_NM,
            </if>
            <if test="corpOrgSnm != null" >
                CORP_ORG_SNM,
            </if>
            <if test="corpOrgTyp != null" >
                CORP_ORG_TYP,
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID,
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID,
            </if>
            <if test="corpAccNm != null" >
                CORP_ACC_NM,
            </if>
            <if test="corpAccNo != null" >
                CORP_ACC_NO,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orgInfId != null" >
                #{orgInfId,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgId != null" >
                #{corpOrgId,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgNm != null" >
                #{corpOrgNm,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgSnm != null" >
                #{corpOrgSnm,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgTyp != null" >
                #{corpOrgTyp,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                #{creOprId,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null" >
                #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="corpAccNm != null" >
                #{corpAccNm,jdbcType=VARCHAR},
            </if>
            <if test="corpAccNo != null" >
                #{corpAccNo,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.CopAgcyInfoDO" >
        update cpo_cop_agcy_info
        <set >
            <if test="corpOrgId != null" >
                CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgNm != null" >
                CORP_ORG_NM = #{corpOrgNm,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgSnm != null" >
                CORP_ORG_SNM = #{corpOrgSnm,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgTyp != null" >
                CORP_ORG_TYP = #{corpOrgTyp,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID = #{creOprId,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="corpAccNm != null" >
                CORP_ACC_NM = #{corpAccNm,jdbcType=VARCHAR},
            </if>
            <if test="corpAccNo != null" >
                CORP_ACC_NO = #{corpAccNo,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ORG_INF_ID = #{orgInfId,jdbcType=VARCHAR}
    </update>
</mapper>