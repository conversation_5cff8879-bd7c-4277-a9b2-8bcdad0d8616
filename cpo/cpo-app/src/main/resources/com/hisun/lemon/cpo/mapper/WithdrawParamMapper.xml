<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.IWithdrawParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.WithdrawParamDO" >
        <id column="BUS_PAR_ID" property="busParId" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="CAP_TYP" property="capTyp" jdbcType="CHAR" />
        <result column="PSN_CRP_FLG" property="psnCrpFlg" jdbcType="CHAR" />
        <result column="HOLD_FLG" property="holdFlg" jdbcType="CHAR" />
        <result column="HOLD_DESC" property="holdDesc" jdbcType="VARCHAR" />
        <result column="AUTO_PAY_FLG" property="autoPayFlg" jdbcType="CHAR" />
        <result column="RSK_CHK_FLG" property="rskChkFlg" jdbcType="CHAR" />
        <result column="WDC_AUTO_FLG" property="wdcAutoFlg" jdbcType="CHAR" />
        <result column="RRC_CML_FLG" property="rrcCmlFlg" jdbcType="CHAR" />
        <result column="PAYTM_CHK_FLG" property="paytmChkFlg" jdbcType="CHAR" />
        <result column="CHK_USR_FLG" property="chkUsrFlg" jdbcType="CHAR" />
        <result column="PAUSE_FLG" property="pauseFlg" jdbcType="CHAR" />
        <result column="SMS_FLG" property="smsFlg" jdbcType="CHAR" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        BUS_PAR_ID, CORP_BUS_TYP, CORP_BUS_SUB_TYP, CAP_TYP, PSN_CRP_FLG, HOLD_FLG, HOLD_DESC,
        AUTO_PAY_FLG, RSK_CHK_FLG, WDC_AUTO_FLG, RRC_CML_FLG, PAYTM_CHK_FLG, CHK_USR_FLG, 
        PAUSE_FLG, SMS_FLG, OPR_ID, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <!-- 根据主键查询付款业务参数信息 -->
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpo_withdraw_param
        where BUS_PAR_ID = #{busParId,jdbcType=VARCHAR}
    </select>

    <!-- 根据唯一索引查询付款业务参数信息 -->
    <select id="getWithdrawParam" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cpo.entity.WithdrawParamDO" >
        select
        <include refid="Base_Column_List" />
        from cpo_withdraw_param
        where CORP_BUS_TYP=#{corpBusTyp,jdbcType=CHAR}
          AND CORP_BUS_SUB_TYP=#{corpBusSubTyp,jdbcType=CHAR}
          AND CAP_TYP=#{capTyp,jdbcType=CHAR}
          AND PSN_CRP_FLG=#{psnCrpFlg,jdbcType=CHAR}
    </select>

    <!-- 根据主键删除付款业务参数信息 -->
    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_withdraw_param
        where BUS_PAR_ID = #{busParId,jdbcType=VARCHAR}
    </delete>

    <!-- 新增付款业务参数信息，取不为null的字段值存入表中 -->
    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.WithdrawParamDO" >
        insert into cpo_withdraw_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="busParId != null" >
                BUS_PAR_ID,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="capTyp != null" >
                CAP_TYP,
            </if>
            <if test="psnCrpFlg != null" >
                PSN_CRP_FLG,
            </if>
            <if test="holdFlg != null" >
                HOLD_FLG,
            </if>
            <if test="holdDesc != null" >
                HOLD_DESC,
            </if>
            <if test="autoPayFlg != null" >
                AUTO_PAY_FLG,
            </if>
            <if test="rskChkFlg != null" >
                RSK_CHK_FLG,
            </if>
            <if test="wdcAutoFlg != null" >
                WDC_AUTO_FLG,
            </if>
            <if test="rrcCmlFlg != null" >
                RRC_CML_FLG,
            </if>
            <if test="paytmChkFlg != null" >
                PAYTM_CHK_FLG,
            </if>
            <if test="chkUsrFlg != null" >
                CHK_USR_FLG,
            </if>
            <if test="pauseFlg != null" >
                PAUSE_FLG,
            </if>
            <if test="smsFlg != null" >
                SMS_FLG,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="busParId != null" >
                #{busParId,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="capTyp != null" >
                #{capTyp,jdbcType=CHAR},
            </if>
            <if test="psnCrpFlg != null" >
                #{psnCrpFlg,jdbcType=CHAR},
            </if>
            <if test="holdFlg != null" >
                #{holdFlg,jdbcType=CHAR},
            </if>
            <if test="holdDesc != null" >
                #{holdDesc,jdbcType=VARCHAR},
            </if>
            <if test="autoPayFlg != null" >
                #{autoPayFlg,jdbcType=CHAR},
            </if>
            <if test="rskChkFlg != null" >
                #{rskChkFlg,jdbcType=CHAR},
            </if>
            <if test="wdcAutoFlg != null" >
                #{wdcAutoFlg,jdbcType=CHAR},
            </if>
            <if test="rrcCmlFlg != null" >
                #{rrcCmlFlg,jdbcType=CHAR},
            </if>
            <if test="paytmChkFlg != null" >
                #{paytmChkFlg,jdbcType=CHAR},
            </if>
            <if test="chkUsrFlg != null" >
                #{chkUsrFlg,jdbcType=CHAR},
            </if>
            <if test="pauseFlg != null" >
                #{pauseFlg,jdbcType=CHAR},
            </if>
            <if test="smsFlg != null" >
                #{smsFlg,jdbcType=CHAR},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新付款业务参数信息，取不为null的字段值存入表中 -->
    <update id="update" parameterType="com.hisun.lemon.cpo.entity.WithdrawParamDO" >
        update cpo_withdraw_param
        <set >
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="capTyp != null" >
                CAP_TYP = #{capTyp,jdbcType=CHAR},
            </if>
            <if test="psnCrpFlg != null" >
                PSN_CRP_FLG = #{psnCrpFlg,jdbcType=CHAR},
            </if>
            <if test="holdFlg != null" >
                HOLD_FLG = #{holdFlg,jdbcType=CHAR},
            </if>
            <if test="holdDesc != null" >
                HOLD_DESC = #{holdDesc,jdbcType=VARCHAR},
            </if>
            <if test="autoPayFlg != null" >
                AUTO_PAY_FLG = #{autoPayFlg,jdbcType=CHAR},
            </if>
            <if test="rskChkFlg != null" >
                RSK_CHK_FLG = #{rskChkFlg,jdbcType=CHAR},
            </if>
            <if test="wdcAutoFlg != null" >
                WDC_AUTO_FLG = #{wdcAutoFlg,jdbcType=CHAR},
            </if>
            <if test="rrcCmlFlg != null" >
                RRC_CML_FLG = #{rrcCmlFlg,jdbcType=CHAR},
            </if>
            <if test="paytmChkFlg != null" >
                PAYTM_CHK_FLG = #{paytmChkFlg,jdbcType=CHAR},
            </if>
            <if test="chkUsrFlg != null" >
                CHK_USR_FLG = #{chkUsrFlg,jdbcType=CHAR},
            </if>
            <if test="pauseFlg != null" >
                PAUSE_FLG = #{pauseFlg,jdbcType=CHAR},
            </if>
            <if test="smsFlg != null" >
                SMS_FLG = #{smsFlg,jdbcType=CHAR},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where BUS_PAR_ID = #{busParId,jdbcType=VARCHAR}
    </update>

    <!-- 根据唯一索引更新付款业务参数信息，取不为null的字段值存入表中 -->
    <update id="updateWithdrawParam" parameterType="com.hisun.lemon.cpo.entity.WithdrawParamDO" >
        update cpo_withdraw_param
        <set >
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="capTyp != null" >
                CAP_TYP = #{capTyp,jdbcType=CHAR},
            </if>
            <if test="psnCrpFlg != null" >
                PSN_CRP_FLG = #{psnCrpFlg,jdbcType=CHAR},
            </if>
            <if test="holdFlg != null" >
                HOLD_FLG = #{holdFlg,jdbcType=CHAR},
            </if>
            <if test="holdDesc != null" >
                HOLD_DESC = #{holdDesc,jdbcType=VARCHAR},
            </if>
            <if test="autoPayFlg != null" >
                AUTO_PAY_FLG = #{autoPayFlg,jdbcType=CHAR},
            </if>
            <if test="rskChkFlg != null" >
                RSK_CHK_FLG = #{rskChkFlg,jdbcType=CHAR},
            </if>
            <if test="wdcAutoFlg != null" >
                WDC_AUTO_FLG = #{wdcAutoFlg,jdbcType=CHAR},
            </if>
            <if test="rrcCmlFlg != null" >
                RRC_CML_FLG = #{rrcCmlFlg,jdbcType=CHAR},
            </if>
            <if test="paytmChkFlg != null" >
                PAYTM_CHK_FLG = #{paytmChkFlg,jdbcType=CHAR},
            </if>
            <if test="chkUsrFlg != null" >
                CHK_USR_FLG = #{chkUsrFlg,jdbcType=CHAR},
            </if>
            <if test="pauseFlg != null" >
                PAUSE_FLG = #{pauseFlg,jdbcType=CHAR},
            </if>
            <if test="smsFlg != null" >
                SMS_FLG = #{smsFlg,jdbcType=CHAR},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where CORP_BUS_TYP=#{corpBusTyp,jdbcType=CHAR}
          AND CORP_BUS_SUB_TYP=#{corpBusSubTyp,jdbcType=CHAR}
          AND CAP_TYP=#{capTyp,jdbcType=CHAR}
          AND PSN_CRP_FLG=#{psnCrpFlg,jdbcType=CHAR}
    </update>

</mapper>