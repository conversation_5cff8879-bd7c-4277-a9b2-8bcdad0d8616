<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.IAccErrorDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.AccErrorDO" >
        <id column="CHK_ER_ID" property="chkErId" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="ERR_KEY_ID" property="errKeyId" jdbcType="VARCHAR" />
        <result column="CHK_ERR_DT" property="chkErrDt" jdbcType="DATE" />
        <result column="CHK_ERR_TM" property="chkErrTm" jdbcType="TIME" />
        <result column="CHK_BAT_NO" property="chkBatNo" jdbcType="VARCHAR" />
        <result column="SPL_ABLE_FLG" property="splAbleFlg" jdbcType="CHAR" />
        <result column="CAN_ABLE_FLG" property="canAbleFlg" jdbcType="CHAR" />
        <result column="CHK_ERR_TYP" property="chkErrTyp" jdbcType="CHAR" />
        <result column="ERR_STS" property="errSts" jdbcType="CHAR" />
        <result column="OTH_TX_AMT" property="othTxAmt" jdbcType="DECIMAL" />
        <result column="MY_TX_AMT" property="myTxAmt" jdbcType="DECIMAL" />
        <result column="PSN_CRP_FLG" property="psnCrpFlg" jdbcType="CHAR" />
        <result column="OLD_TX_DT" property="oldTxDt" jdbcType="DATE" />
        <result column="OLD_JRN_NO" property="oldJrnNo" jdbcType="VARCHAR" />
        <result column="OLD_ORD_NO" property="oldOrdNo" jdbcType="VARCHAR" />
        <result column="OLD_CORG_KEY" property="oldCorgKey" jdbcType="VARCHAR" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        CHK_ER_ID, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, ERR_KEY_ID, CHK_ERR_DT, CHK_ERR_TM,
        CHK_BAT_NO, SPL_ABLE_FLG, CAN_ABLE_FLG, CHK_ERR_TYP, ERR_STS, OTH_TX_AMT, MY_TX_AMT, 
        PSN_CRP_FLG, OLD_TX_DT, OLD_JRN_NO, OLD_ORD_NO, OLD_CORG_KEY, OPR_ID, CREATE_TIME, 
        MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpo_acc_error
        where CHK_ER_ID = #{chkErId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_acc_error
        where CHK_ER_ID = #{chkErId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.AccErrorDO" >
        insert into cpo_acc_error
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="chkErId != null" >
                CHK_ER_ID,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="errKeyId != null" >
                ERR_KEY_ID,
            </if>
            <if test="chkErrDt != null" >
                CHK_ERR_DT,
            </if>
            <if test="chkErrTm != null" >
                CHK_ERR_TM,
            </if>
            <if test="chkBatNo != null" >
                CHK_BAT_NO,
            </if>
            <if test="splAbleFlg != null" >
                SPL_ABLE_FLG,
            </if>
            <if test="canAbleFlg != null" >
                CAN_ABLE_FLG,
            </if>
            <if test="chkErrTyp != null" >
                CHK_ERR_TYP,
            </if>
            <if test="errSts != null" >
                ERR_STS,
            </if>
            <if test="othTxAmt != null" >
                OTH_TX_AMT,
            </if>
            <if test="myTxAmt != null" >
                MY_TX_AMT,
            </if>
            <if test="psnCrpFlg != null" >
                PSN_CRP_FLG,
            </if>
            <if test="oldTxDt != null" >
                OLD_TX_DT,
            </if>
            <if test="oldJrnNo != null" >
                OLD_JRN_NO,
            </if>
            <if test="oldOrdNo != null" >
                OLD_ORD_NO,
            </if>
            <if test="oldCorgKey != null" >
                OLD_CORG_KEY,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="chkErId != null" >
                #{chkErId,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="errKeyId != null" >
                #{errKeyId,jdbcType=VARCHAR},
            </if>
            <if test="chkErrDt != null" >
                #{chkErrDt,jdbcType=DATE},
            </if>
            <if test="chkErrTm != null" >
                #{chkErrTm,jdbcType=TIME},
            </if>
            <if test="chkBatNo != null" >
                #{chkBatNo,jdbcType=VARCHAR},
            </if>
            <if test="splAbleFlg != null" >
                #{splAbleFlg,jdbcType=CHAR},
            </if>
            <if test="canAbleFlg != null" >
                #{canAbleFlg,jdbcType=CHAR},
            </if>
            <if test="chkErrTyp != null" >
                #{chkErrTyp,jdbcType=CHAR},
            </if>
            <if test="errSts != null" >
                #{errSts,jdbcType=CHAR},
            </if>
            <if test="othTxAmt != null" >
                #{othTxAmt,jdbcType=DECIMAL},
            </if>
            <if test="myTxAmt != null" >
                #{myTxAmt,jdbcType=DECIMAL},
            </if>
            <if test="psnCrpFlg != null" >
                #{psnCrpFlg,jdbcType=CHAR},
            </if>
            <if test="oldTxDt != null" >
                #{oldTxDt,jdbcType=DATE},
            </if>
            <if test="oldJrnNo != null" >
                #{oldJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="oldOrdNo != null" >
                #{oldOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="oldCorgKey != null" >
                #{oldCorgKey,jdbcType=VARCHAR},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.AccErrorDO" >
        update cpo_acc_error
        <set >
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="errKeyId != null" >
                ERR_KEY_ID = #{errKeyId,jdbcType=VARCHAR},
            </if>
            <if test="chkErrDt != null" >
                CHK_ERR_DT = #{chkErrDt,jdbcType=DATE},
            </if>
            <if test="chkErrTm != null" >
                CHK_ERR_TM = #{chkErrTm,jdbcType=TIME},
            </if>
            <if test="chkBatNo != null" >
                CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR},
            </if>
            <if test="splAbleFlg != null" >
                SPL_ABLE_FLG = #{splAbleFlg,jdbcType=CHAR},
            </if>
            <if test="canAbleFlg != null" >
                CAN_ABLE_FLG = #{canAbleFlg,jdbcType=CHAR},
            </if>
            <if test="chkErrTyp != null" >
                CHK_ERR_TYP = #{chkErrTyp,jdbcType=CHAR},
            </if>
            <if test="errSts != null" >
                ERR_STS = #{errSts,jdbcType=CHAR},
            </if>
            <if test="othTxAmt != null" >
                OTH_TX_AMT = #{othTxAmt,jdbcType=DECIMAL},
            </if>
            <if test="myTxAmt != null" >
                MY_TX_AMT = #{myTxAmt,jdbcType=DECIMAL},
            </if>
            <if test="psnCrpFlg != null" >
                PSN_CRP_FLG = #{psnCrpFlg,jdbcType=CHAR},
            </if>
            <if test="oldTxDt != null" >
                OLD_TX_DT = #{oldTxDt,jdbcType=DATE},
            </if>
            <if test="oldJrnNo != null" >
                OLD_JRN_NO = #{oldJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="oldOrdNo != null" >
                OLD_ORD_NO = #{oldOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="oldCorgKey != null" >
                OLD_CORG_KEY = #{oldCorgKey,jdbcType=VARCHAR},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where CHK_ER_ID = #{chkErId,jdbcType=VARCHAR}
    </update>
</mapper>