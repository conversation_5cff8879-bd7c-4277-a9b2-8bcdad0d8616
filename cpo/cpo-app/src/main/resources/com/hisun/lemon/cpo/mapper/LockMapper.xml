<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.ILockDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.LockDO" >
        <id column="LOCK_ID" property="lockId" jdbcType="VARCHAR" />
        <result column="LOCK_NAME" property="lockName" jdbcType="VARCHAR" />
        <result column="LOCK_STS" property="lockSts" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        LOCK_ID, LOCK_NAME, LOCK_STS, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpo_lock
        where LOCK_ID = #{lockId,jdbcType=VARCHAR}
    </select>

    <!-- 查询锁状态 -->
    <select id="getLockSts" resultType="java.lang.String" parameterType="java.lang.String" >
        select LOCK_STS
        from cpo_lock
        where LOCK_ID = #{lockId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_lock
        where LOCK_ID = #{lockId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.LockDO" >
        insert into cpo_lock
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="lockId != null" >
              LOCK_ID,
            </if>
            <if test="lockName != null" >
              LOCK_NAME,
            </if>
            <if test="lockSts != null" >
              LOCK_STS,
            </if>
            <if test="createTime != null" >
              CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
              MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
              TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="lockId != null" >
              #{lockId,jdbcType=VARCHAR},
            </if>
            <if test="lockName != null" >
              #{lockName,jdbcType=VARCHAR},
            </if>
            <if test="lockSts != null" >
              #{lockSts,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
              #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
              #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
              #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.LockDO" >
        update cpo_lock
        <set >
            <if test="lockName != null" >
              LOCK_NAME = #{lockName,jdbcType=VARCHAR},
            </if>
            <if test="lockSts != null" >
              LOCK_STS = #{lockSts,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
              CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
              MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
              TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where LOCK_ID = #{lockId,jdbcType=VARCHAR}
    </update>

</mapper>