<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.IWithdrawSuborderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.WithdrawSuborderDO" >
        <id column="SUB_ORD_NO" property="subOrdNo" jdbcType="VARCHAR" />
        <result column="WC_ORD_NO" property="wcOrdNo" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CAP_CORG" property="capCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="WC_APL_AMT" property="wcAplAmt" jdbcType="DECIMAL" />
        <result column="ORD_STS" property="ordSts" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="RUT_CORG_JRN" property="rutCorgJrn" jdbcType="VARCHAR" />
        <result column="RUT_CORG_DT" property="rutCorgDt" jdbcType="DATE" />
        <result column="RUT_CORG_TM" property="rutCorgTm" jdbcType="TIME" />
        <result column="ORG_RSP_CD" property="orgRspCd" jdbcType="VARCHAR" />
        <result column="ORG_RSP_MSG" property="orgRspMsg" jdbcType="VARCHAR" />
        <result column="CHK_KEY" property="chkKey" jdbcType="VARCHAR" />
        <result column="CHK_STS" property="chkSts" jdbcType="CHAR" />
        <result column="CHK_DT" property="chkDt" jdbcType="DATE" />
        <result column="CHK_TM" property="chkTm" jdbcType="TIME" />
        <result column="CAV_DT" property="cavDt" jdbcType="DATE" />
        <result column="CAV_TM" property="cavTm" jdbcType="TIME" />
        <result column="CAV_OPER" property="cavOper" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        SUB_ORD_NO, WC_ORD_NO, RUT_CORG, CAP_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, WC_APL_AMT, 
        ORD_STS, ORD_DT, AC_DT, RUT_CORG_JRN, RUT_CORG_DT, RUT_CORG_TM, ORG_RSP_CD, ORG_RSP_MSG,
        CHK_KEY, CHK_STS, CHK_DT, CHK_TM, CAV_DT, CAV_TM, CAV_OPER, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="getWithdrawSuborderByOrdNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpo_withdraw_suborder
        where WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_withdraw_suborder
        where WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.WithdrawSuborderDO" >
        insert into cpo_withdraw_suborder
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="subOrdNo != null" >
                SUB_ORD_NO,
            </if>
            <if test="wcOrdNo != null" >
                WC_ORD_NO,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="capCorg != null" >
                CAP_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="wcAplAmt != null" >
                WC_APL_AMT,
            </if>
            <if test="ordSts != null" >
                ORD_STS,
            </if>
            <if test="ordDt != null" >
                ORD_DT,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="rutCorgJrn != null" >
                RUT_CORG_JRN,
            </if>
            <if test="rutCorgDt != null" >
                RUT_CORG_DT,
            </if>
            <if test="rutCorgTm != null" >
                RUT_CORG_TM,
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD,
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG,
            </if>
            <if test="chkKey != null" >
                CHK_KEY,
            </if>
            <if test="chkSts != null" >
                CHK_STS,
            </if>
            <if test="chkDt != null" >
                CHK_DT,
            </if>
            <if test="chkTm != null" >
                CHK_TM,
            </if>
            <if test="cavDt != null" >
                CAV_DT,
            </if>
            <if test="cavTm != null" >
                CAV_TM,
            </if>
            <if test="cavOper != null" >
                CAV_OPER,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="subOrdNo != null" >
                #{subOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="wcOrdNo != null" >
                #{wcOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="capCorg != null" >
                #{capCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="wcAplAmt != null" >
                #{wcAplAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                #{ordSts,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                #{ordDt,jdbcType=DATE},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="rutCorgJrn != null" >
                #{rutCorgJrn,jdbcType=VARCHAR},
            </if>
            <if test="rutCorgDt != null" >
                #{rutCorgDt,jdbcType=DATE},
            </if>
            <if test="rutCorgTm != null" >
                #{rutCorgTm,jdbcType=TIME},
            </if>
            <if test="orgRspCd != null" >
                #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="chkKey != null" >
                #{chkKey,jdbcType=VARCHAR},
            </if>
            <if test="chkSts != null" >
                #{chkSts,jdbcType=CHAR},
            </if>
            <if test="chkDt != null" >
                #{chkDt,jdbcType=DATE},
            </if>
            <if test="chkTm != null" >
                #{chkTm,jdbcType=TIME},
            </if>
            <if test="cavDt != null" >
                #{cavDt,jdbcType=DATE},
            </if>
            <if test="cavTm != null" >
                #{cavTm,jdbcType=TIME},
            </if>
            <if test="cavOper != null" >
                #{cavOper,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.WithdrawSuborderDO" >
        update cpo_withdraw_suborder
        <set >
            <if test="wcOrdNo != null" >
                WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="capCorg != null" >
                CAP_CORG = #{capCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="wcAplAmt != null" >
                WC_APL_AMT = #{wcAplAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                ORD_STS = #{ordSts,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                ORD_DT = #{ordDt,jdbcType=DATE},
            </if>
            <if test="acDt != null" >
                AC_DT = #{acDt,jdbcType=DATE},
            </if>
            <if test="rutCorgJrn != null" >
                RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR},
            </if>
            <if test="rutCorgDt != null" >
                RUT_CORG_DT = #{rutCorgDt,jdbcType=DATE},
            </if>
            <if test="rutCorgTm != null" >
                RUT_CORG_TM = #{rutCorgTm,jdbcType=TIME},
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD = #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG = #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="chkKey != null" >
                CHK_KEY = #{chkKey,jdbcType=VARCHAR},
            </if>
            <if test="chkSts != null" >
                CHK_STS = #{chkSts,jdbcType=CHAR},
            </if>
            <if test="chkDt != null" >
                CHK_DT = #{chkDt,jdbcType=DATE},
            </if>
            <if test="chkTm != null" >
                CHK_TM = #{chkTm,jdbcType=TIME},
            </if>
            <if test="cavDt != null" >
                CAV_DT = #{cavDt,jdbcType=DATE},
            </if>
            <if test="cavTm != null" >
                CAV_TM = #{cavTm,jdbcType=TIME},
            </if>
            <if test="cavOper != null" >
                CAV_OPER = #{cavOper,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP}
            </if>
        </set>
        where WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
    </update>

    <!-- 查询我方ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单，ORD_DT订单日期小于对账日期 -->
    <select id="getWithdrawSuborderListByChkStsDoubt" resultMap="BaseResultMap" >
        select SUB_ORD_NO, WC_ORD_NO,RUT_CORG,CORP_BUS_TYP,CORP_BUS_SUB_TYP,
          WC_APL_AMT,CHK_STS,ORD_STS, CHK_KEY
        from cpo_withdraw_suborder
        where ORD_STS = #{ordSts,jdbcType=VARCHAR}
        and CHK_STS = #{chkSts,jdbcType=VARCHAR}
        and ORD_DT &lt; #{chkDt,jdbcType=DATE}
    </select>

    <!-- 查询我方ORD_STS='S'、CHK_STS='0'(未对账)的订单，订单日期等于对账日期 -->
    <select id="getWithdrawSuborderListByChkStsNotstart" resultMap="BaseResultMap" >
        select WC_ORD_NO,RUT_CORG,CORP_BUS_TYP,CORP_BUS_SUB_TYP,WC_APL_AMT,CHK_STS,ORD_STS
        from cpo_withdraw_suborder
        where ORD_STS = #{ordSts,jdbcType=VARCHAR}
        and CHK_STS = #{chkSts,jdbcType=VARCHAR}
        and ORD_DT = #{chkDt,jdbcType=DATE}
    </select>

    <!--将ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'，(ORD_DT)订单日期小于对账日期-->
    <update id="updateWithdrawSuborderByChkStsDoubt" >
        update cpo_withdraw_suborder
        set CHK_DT = #{chkDt,jdbcType=DATE},
            CHK_TM = #{chkTm,jdbcType=TIME},
            CHK_STS = #{newChkSts,jdbcType=CHAR}
            where ORD_STS = #{ordSts,jdbcType=VARCHAR}
            and CHK_STS = #{oldChkSts,jdbcType=VARCHAR}
            and ORD_DT &lt; #{chkDt,jdbcType=DATE}
    </update>

    <!-- 将ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)，订单日期等于对账日期 -->
    <update id="updateWithdrawSuborderListByChkStsNotstart" >
        update cpo_withdraw_suborder
        set CHK_DT = #{chkDt,jdbcType=DATE},
        CHK_TM = #{chkTm,jdbcType=TIME},
        CHK_STS = #{newChkSts,jdbcType=CHAR}
        where ORD_STS = #{ordSts,jdbcType=VARCHAR}
        and CHK_STS = #{oldChkSts,jdbcType=VARCHAR}
        and ORD_DT = #{chkDt,jdbcType=DATE}
    </update>

    <!--根据对账键值，查询提现订单信息，查询条件 ORD_STS='S'(交易成功)、CHK_STS='0'(未对账) OR CHK_STS='5'(存疑)-->
    <select id="getWithdrawSuborderByChkKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpo_withdraw_suborder
        where CHK_KEY = #{chkKey,jdbcType=VARCHAR}
    </select>

    <!--根据对账键值，更新提现订单的对账状态、对账时间等-->
    <update id="updateWithdrawSuborderByChkKey" parameterType="com.hisun.lemon.cpo.entity.WithdrawSuborderDO" >
        update cpo_withdraw_suborder
        set CHK_STS = #{chkSts,jdbcType=CHAR},
        CHK_DT = #{chkDt,jdbcType=DATE},
        CHK_TM = #{chkTm,jdbcType=TIME}
        where CHK_KEY = #{chkKey,jdbcType=VARCHAR}
    </update>

</mapper>