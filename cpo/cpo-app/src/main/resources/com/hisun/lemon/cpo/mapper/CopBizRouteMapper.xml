<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.ICopBizRouteDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        <id column="RUT_INF_ID" property="rutInfId" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="RUT_EFF_FLG" property="rutEffFlg" jdbcType="CHAR" />
        <result column="PRI_LVL" property="priLvl" jdbcType="INTEGER" />
        <result column="LOW_AMT" property="lowAmt" jdbcType="DECIMAL" />
        <result column="HIGH_AMT" property="highAmt" jdbcType="DECIMAL" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        RUT_INF_ID, CRD_CORP_ORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, RUT_CORP_ORG, CRD_AC_TYP, 
        RUT_EFF_FLG, PRI_LVL, LOW_AMT, HIGH_AMT, OPR_ID, RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpo_cop_biz_route
        where RUT_INF_ID = #{rutInfId,jdbcType=VARCHAR}
    </select>

    <!-- 根据机构和业务类型，查询路由信息 -->
    <select id="getByRutCorgAndBusTyp" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        select
        <include refid="Base_Column_List" />
        from cpo_cop_biz_route
        WHERE CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
          AND CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
          AND CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
          AND RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
          AND CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
    </select>

    <!-- 根据机构信息、机构业务信息、机构路由信息，查询合作机构业务路由信息 -->
    <select id="getAgcyRoute" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        SELECT A.RUT_INF_ID,A.CRD_CORP_ORG,A.CORP_BUS_TYP,A.CORP_BUS_SUB_TYP,
               A.RUT_CORP_ORG,A.CRD_AC_TYP,A.RUT_EFF_FLG,
               A.PRI_LVL,A.LOW_AMT,A.HIGH_AMT,A.OPR_ID,
               A.RMK,A.CREATE_TIME,A.MODIFY_TIME
		FROM cpo_cop_biz_route A,
		     CPO_COP_AGCY_INFO B,
		     cpo_cop_agcy_biz C
		WHERE A.RUT_CORP_ORG = C.CORP_ORG_ID
		  AND A.CORP_BUS_TYP = C.CORP_BUS_TYP
		  AND A.CORP_BUS_SUB_TYP = C.CORP_BUS_SUB_TYP
		  AND A.RUT_CORP_ORG = B.CORP_ORG_ID
		  AND A.RUT_EFF_FLG = '1'
		  AND C.BUS_EFF_FLG = '1'
		  AND A.CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
		  AND A.CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
		  AND A.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
		  AND A.CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
    </select>

    <!-- 根据机构业务信息、机构路由信息，查询某业务类型的路由信息 -->
    <select id="getRouteList" resultMap="BaseResultMap" >
        SELECT a.CRD_CORP_ORG,a.CORP_BUS_TYP,a.CORP_BUS_SUB_TYP,a.RUT_CORP_ORG,a.CRD_AC_TYP
        FROM cpo_cop_biz_route a,
             cpo_cop_agcy_biz b
        WHERE a.CRD_CORP_ORG =  b.CORP_ORG_ID
        AND a.RUT_EFF_FLG = '1'
        AND b.BUS_EFF_FLG = '1'
        AND a.CORP_BUS_SUB_TYP = b.CORP_BUS_SUB_TYP
        AND a.CORP_BUS_TYP = b.CORP_BUS_TYP
        AND a.CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        AND a.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        AND (ISNULL(#{crdAcTyp,jdbcType=CHAR}) OR (a.CRD_AC_TYP=#{crdAcTyp,jdbcType=CHAR}))
    </select>

    <!-- 根据机构业务信息表、机构路由信息、金额，查询匹配的路由信息，默认只取一条 -->
    <select id="getRouteInfo" resultMap="BaseResultMap" >
        SELECT a.CRD_CORP_ORG,a.CORP_BUS_TYP,a.CORP_BUS_SUB_TYP,a.RUT_CORP_ORG,a.CRD_AC_TYP
        FROM cpo_cop_biz_route a,
             cpo_cop_agcy_biz b
        WHERE a.CRD_CORP_ORG =  b.CORP_ORG_ID
        AND a.RUT_EFF_FLG = '1'
        AND b.BUS_EFF_FLG = '1'
        AND a.CORP_BUS_SUB_TYP = b.CORP_BUS_SUB_TYP
        AND a.CORP_BUS_TYP = b.CORP_BUS_TYP
        AND a.CORP_BUS_TYP = #{routeDO.corpBusTyp,jdbcType=CHAR}
        AND a.CORP_BUS_SUB_TYP = #{routeDO.corpBusSubTyp,jdbcType=CHAR}
        AND (ISNULL(#{routeDO.crdCorpOrg,jdbcType=VARCHAR}) OR (a.CRD_CORP_ORG=#{routeDO.crdCorpOrg,jdbcType=VARCHAR}))
        AND (ISNULL(#{routeDO.crdAcTyp,jdbcType=CHAR}) OR (a.CRD_AC_TYP=#{routeDO.crdAcTyp,jdbcType=CHAR}))
        AND (ISNULL(#{ordAmt,jdbcType=DECIMAL}) OR (a.LOW_AMT &lt;= #{ordAmt,jdbcType=DECIMAL}))
        AND (ISNULL(#{ordAmt,jdbcType=DECIMAL}) OR (a.HIGH_AMT &gt; #{ordAmt,jdbcType=DECIMAL}))
        ORDER BY a.PRI_LVL DESC
        LIMIT 1
    </select>

    <!-- 根据机构和业务类型，查询路由信息列表 -->
    <select id="getAgcyRouteList" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        select
        <include refid="Base_Column_List" />
        FROM  cpo_cop_biz_route
		WHERE (ISNULL(#{crdCorpOrg,jdbcType=VARCHAR}) OR (CRD_CORP_ORG=#{crdCorpOrg,jdbcType=VARCHAR}))
		  AND (ISNULL(#{corpBusTyp,jdbcType=CHAR}) OR (CORP_BUS_TYP=#{corpBusTyp,jdbcType=CHAR}))
		  AND (ISNULL(#{corpBusSubTyp,jdbcType=CHAR}) OR (CORP_BUS_SUB_TYP=#{corpBusSubTyp,jdbcType=CHAR}))
		  AND (ISNULL(#{rutCorpOrg,jdbcType=VARCHAR}) OR (RUT_CORP_ORG=#{rutCorpOrg,jdbcType=VARCHAR}))
		  AND (ISNULL(#{crdAcTyp,jdbcType=CHAR}) OR (CRD_AC_TYP=#{crdAcTyp,jdbcType=CHAR}))
    </select>

    <!-- 根据机构信息表、机构业务信息表、机构路由信息表，查询合作机构业务路由状态 -->
    <select id="getRutEffFlg" resultType="java.lang.String" parameterType="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        SELECT A.RUT_EFF_FLG
        FROM CPO_COP_BIZ_ROUTE A,
             CPO_COP_AGCY_INFO B,
             CPO_COP_AGCY_BIZ C
		WHERE A.CRD_CORP_ORG=C.CORP_ORG_ID
		  AND A.CRD_CORP_ORG=B.CORP_ORG_ID
		  AND A.CORP_BUS_TYP=C.CORP_BUS_TYP
		  AND A.CORP_BUS_SUB_TYP=C.CORP_BUS_SUB_TYP
		  AND C.BUS_EFF_FLG='1'
		  AND A.CRD_CORP_ORG=#{crdCorpOrg,jdbcType=VARCHAR}
		  AND A.CORP_BUS_TYP=#{corpBusTyp,jdbcType=CHAR}
		  AND A.CORP_BUS_SUB_TYP=#{corpBusSubTyp,jdbcType=CHAR}
		  AND A.RUT_CORP_ORG=#{rutCorpOrg,jdbcType=VARCHAR}
		  AND CRD_AC_TYP=#{crdAcTyp,jdbcType=CHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_cop_biz_route
        where RUT_INF_ID = #{rutInfId,jdbcType=VARCHAR}
    </delete>

    <!-- 根据机构和业务类型，删除路由信息 -->
    <delete id="deleteByRutCorgAndBusTyp" parameterType="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        delete from cpo_cop_biz_route
        WHERE CRD_CORP_ORG=#{crdCorpOrg,jdbcType=VARCHAR}
          AND CORP_BUS_TYP=#{corpBusTyp,jdbcType=CHAR}
          AND CORP_BUS_SUB_TYP=#{corpBusSubTyp,jdbcType=CHAR}
          AND RUT_CORP_ORG=#{rutCorpOrg,jdbcType=VARCHAR}
          AND CRD_AC_TYP=#{crdAcTyp,jdbcType=CHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        insert into cpo_cop_biz_route
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="rutInfId != null" >
                RUT_INF_ID,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="rutEffFlg != null" >
                RUT_EFF_FLG,
            </if>
            <if test="priLvl != null" >
                PRI_LVL,
            </if>
            <if test="lowAmt != null" >
                LOW_AMT,
            </if>
            <if test="highAmt != null" >
                HIGH_AMT,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="rutInfId != null" >
                #{rutInfId,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="rutEffFlg != null" >
                #{rutEffFlg,jdbcType=CHAR},
            </if>
            <if test="priLvl != null" >
                #{priLvl,jdbcType=INTEGER},
            </if>
            <if test="lowAmt != null" >
                #{lowAmt,jdbcType=DECIMAL},
            </if>
            <if test="highAmt != null" >
                #{highAmt,jdbcType=DECIMAL},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        update cpo_cop_biz_route
        <set >
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="rutEffFlg != null" >
                RUT_EFF_FLG = #{rutEffFlg,jdbcType=CHAR},
            </if>
            <if test="priLvl != null" >
                PRI_LVL = #{priLvl,jdbcType=INTEGER},
            </if>
            <if test="lowAmt != null" >
                LOW_AMT = #{lowAmt,jdbcType=DECIMAL},
            </if>
            <if test="highAmt != null" >
                HIGH_AMT = #{highAmt,jdbcType=DECIMAL},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where RUT_INF_ID = #{rutInfId,jdbcType=VARCHAR}
    </update>

    <!-- 根据机构和业务类型，更新路由信息 -->
    <update id="updateByRutCorgAndBusTyp" parameterType="com.hisun.lemon.cpo.entity.CopBizRouteDO" >
        update cpo_cop_biz_route
        <set >
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="rutEffFlg != null" >
                RUT_EFF_FLG = #{rutEffFlg,jdbcType=CHAR},
            </if>
            <if test="priLvl != null" >
                PRI_LVL = #{priLvl,jdbcType=INTEGER},
            </if>
            <if test="lowAmt != null" >
                LOW_AMT = #{lowAmt,jdbcType=DECIMAL},
            </if>
            <if test="highAmt != null" >
                HIGH_AMT = #{highAmt,jdbcType=DECIMAL},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE CRD_CORP_ORG=#{crdCorpOrg,jdbcType=VARCHAR}
          AND CORP_BUS_TYP=#{corpBusTyp,jdbcType=CHAR}
          AND CORP_BUS_SUB_TYP=#{corpBusSubTyp,jdbcType=CHAR}
          AND RUT_CORP_ORG=#{rutCorpOrg,jdbcType=VARCHAR}
          AND CRD_AC_TYP=#{crdAcTyp,jdbcType=CHAR}
    </update>

</mapper>