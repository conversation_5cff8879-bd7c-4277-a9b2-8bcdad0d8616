<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.IRouteDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.RouteDO" >
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="INTEGER" />
        <result column="CORP_ORG_ID" property="corpOrgId" jdbcType="VARCHAR" />
        <result column="CORP_ORG_NM" property="corpOrgNm" jdbcType="VARCHAR" />
        <result column="CORP_ORG_SNM" property="corpOrgSnm" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="VARCHAR" />
        <result column="CORP_ACC_NM" property="corpAccNm" jdbcType="VARCHAR" />
        <result column="CORP_ACC_NO" property="corpAccNo" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
    </resultMap>

    <!--查询生效的合作资金机构信息-->
    <select id="queryEffCapOrgInfo" resultMap="BaseResultMap">
        SELECT
          a.CRD_CORP_ORG, a.CORP_BUS_TYP, a.CORP_BUS_SUB_TYP, a.RUT_CORP_ORG, a.CRD_AC_TYP,
          c.CORP_ORG_ID, c.CORP_ORG_NM, c.CORP_ORG_SNM
          FROM cpo_cop_biz_route a, cpo_cop_agcy_biz b, cpo_cop_agcy_info c
        WHERE a.CRD_CORP_ORG =  b.CORP_ORG_ID
          and a.CRD_CORP_ORG = c.CORP_ORG_ID
          and a.CORP_BUS_SUB_TYP = b.CORP_BUS_SUB_TYP
          and a.CORP_BUS_TYP = b.CORP_BUS_TYP
          and a.RUT_EFF_FLG = '1'
          and b.BUS_EFF_FLG = '1'
          and a.CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
          and a.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
          order by a.CRD_CORP_ORG
    </select>

</mapper>