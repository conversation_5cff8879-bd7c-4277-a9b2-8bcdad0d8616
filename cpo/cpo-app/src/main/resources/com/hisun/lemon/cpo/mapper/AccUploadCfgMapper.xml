<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.IAccUploadCfgDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.AccUploadCfgDO" >
        <id column="CHK_ID" property="chkId" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="LOCAL_FILE_PATH" property="localFilePath" jdbcType="VARCHAR" />
        <result column="UPLOAD_IP" property="uploadIp" jdbcType="VARCHAR" />
        <result column="UPLOAD_PORT" property="uploadPort" jdbcType="VARCHAR" />
        <result column="CONNECT_TIME" property="connectTime" jdbcType="INTEGER" />
        <result column="UPLOAD_PATH" property="uploadPath" jdbcType="VARCHAR" />
        <result column="UPLOAD_NM" property="uploadNm" jdbcType="VARCHAR" />
        <result column="UPLOAD_PWD" property="uploadPwd" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        CHK_ID, RUT_CORG, LOCAL_FILE_PATH, UPLOAD_IP, UPLOAD_PORT, CONNECT_TIME, UPLOAD_PATH, 
        UPLOAD_NM, UPLOAD_PWD, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpo_acc_upload_cfg
        where CHK_ID = #{chkId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_acc_upload_cfg
        where CHK_ID = #{chkId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.AccUploadCfgDO" >
        insert into cpo_acc_upload_cfg
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="chkId != null" >
                CHK_ID,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="localFilePath != null" >
                LOCAL_FILE_PATH,
            </if>
            <if test="uploadIp != null" >
                UPLOAD_IP,
            </if>
            <if test="uploadPort != null" >
                UPLOAD_PORT,
            </if>
            <if test="connectTime != null" >
                CONNECT_TIME,
            </if>
            <if test="uploadPath != null" >
                UPLOAD_PATH,
            </if>
            <if test="uploadNm != null" >
                UPLOAD_NM,
            </if>
            <if test="uploadPwd != null" >
                UPLOAD_PWD,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="chkId != null" >
                #{chkId,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="localFilePath != null" >
                #{localFilePath,jdbcType=VARCHAR},
            </if>
            <if test="uploadIp != null" >
                #{uploadIp,jdbcType=VARCHAR},
            </if>
            <if test="uploadPort != null" >
                #{uploadPort,jdbcType=VARCHAR},
            </if>
            <if test="connectTime != null" >
                #{connectTime,jdbcType=INTEGER},
            </if>
            <if test="uploadPath != null" >
                #{uploadPath,jdbcType=VARCHAR},
            </if>
            <if test="uploadNm != null" >
                #{uploadNm,jdbcType=VARCHAR},
            </if>
            <if test="uploadPwd != null" >
                #{uploadPwd,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.AccUploadCfgDO" >
        update cpo_acc_upload_cfg
        <set >
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="localFilePath != null" >
                LOCAL_FILE_PATH = #{localFilePath,jdbcType=VARCHAR},
            </if>
            <if test="uploadIp != null" >
                UPLOAD_IP = #{uploadIp,jdbcType=VARCHAR},
            </if>
            <if test="uploadPort != null" >
                UPLOAD_PORT = #{uploadPort,jdbcType=VARCHAR},
            </if>
            <if test="connectTime != null" >
                CONNECT_TIME = #{connectTime,jdbcType=INTEGER},
            </if>
            <if test="uploadPath != null" >
                UPLOAD_PATH = #{uploadPath,jdbcType=VARCHAR},
            </if>
            <if test="uploadNm != null" >
                UPLOAD_NM = #{uploadNm,jdbcType=VARCHAR},
            </if>
            <if test="uploadPwd != null" >
                UPLOAD_PWD = #{uploadPwd,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where CHK_ID = #{chkId,jdbcType=VARCHAR}
    </update>

    <select id="getByUnique" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpo_acc_upload_cfg
        where RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
    </select>

</mapper>