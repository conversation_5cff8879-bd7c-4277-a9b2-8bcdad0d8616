<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.IAccControlDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.AccControlDO" >
        <id column="CHK_BAT_NO" property="chkBatNo" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="CHK_FIL_DT" property="chkFilDt" jdbcType="DATE" />
        <result column="CHK_FIL_NM" property="chkFilNm" jdbcType="VARCHAR" />
        <result column="CHK_FIL_STS" property="chkFilSts" jdbcType="CHAR" />
        <result column="FILE_RCV_DT" property="fileRcvDt" jdbcType="DATE" />
        <result column="CHK_BEG_TM" property="chkBegTm" jdbcType="TIME" />
        <result column="CHK_END_TM" property="chkEndTm" jdbcType="TIME" />
        <result column="CHK_DT" property="chkDt" jdbcType="DATE" />
        <result column="FIL_TOT_AMT" property="filTotAmt" jdbcType="DECIMAL" />
        <result column="FIL_TOT_CNT" property="filTotCnt" jdbcType="INTEGER" />
        <result column="TOT_MCH_AMT" property="totMchAmt" jdbcType="DECIMAL" />
        <result column="TOT_MCH_CNT" property="totMchCnt" jdbcType="INTEGER" />
        <result column="ERR_TOT_CNT" property="errTotCnt" jdbcType="INTEGER" />
        <result column="ERR_TOT_AMT" property="errTotAmt" jdbcType="DECIMAL" />
        <result column="LONG_AMT" property="longAmt" jdbcType="DECIMAL" />
        <result column="LONG_CNT" property="longCnt" jdbcType="INTEGER" />
        <result column="SHORT_AMT" property="shortAmt" jdbcType="DECIMAL" />
        <result column="SHORT_CNT" property="shortCnt" jdbcType="INTEGER" />
        <result column="DOUBT_AMT" property="doubtAmt" jdbcType="DECIMAL" />
        <result column="DOUBT_CNT" property="doubtCnt" jdbcType="INTEGER" />
        <result column="DBT_ERR_AMT" property="dbtErrAmt" jdbcType="DECIMAL" />
        <result column="DBT_ERR_CNT" property="dbtErrCnt" jdbcType="INTEGER" />
        <result column="TOT_DR_AMT" property="totDrAmt" jdbcType="DECIMAL" />
        <result column="TOT_DR_NUM" property="totDrNum" jdbcType="INTEGER" />
        <result column="TOT_CR_AMT" property="totCrAmt" jdbcType="DECIMAL" />
        <result column="TOT_CR_NUM" property="totCrNum" jdbcType="INTEGER" />
        <result column="PAY_TOT_AMT" property="payTotAmt" jdbcType="DECIMAL" />
        <result column="RCV_TOT_AMT" property="rcvTotAmt" jdbcType="DECIMAL" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        CHK_BAT_NO, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, CHK_FIL_DT, CHK_FIL_NM, CHK_FIL_STS,
        FILE_RCV_DT, CHK_BEG_TM, CHK_END_TM, CHK_DT, FIL_TOT_AMT, FIL_TOT_CNT, TOT_MCH_AMT,
        TOT_MCH_CNT, ERR_TOT_CNT, ERR_TOT_AMT, LONG_AMT, LONG_CNT, SHORT_AMT, SHORT_CNT,
        DOUBT_AMT, DOUBT_CNT, DBT_ERR_AMT, DBT_ERR_CNT, TOT_DR_AMT, TOT_DR_NUM, TOT_CR_AMT,
        TOT_CR_NUM, PAY_TOT_AMT, RCV_TOT_AMT, RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpo_acc_control
        where CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR}
    </select>

    <!--根据业务类型、业务子类型、路径合作机构、对账日期查询对账批次信息列表-->
    <!--检查上一对账日期之前，是否还有未完成的对账批次-->
    <select id="getChkAccControlListByLastChkDt" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cpo.entity.AccControlDO" >
        select
        <include refid="Base_Column_List" />
        from cpo_acc_control
        where CHK_DT &lt;= #{chkDt, jdbcType=DATE}
        and RUT_CORG = #{rutCorg, jdbcType=VARCHAR}
        and CORP_BUS_TYP = #{corpBusTyp, jdbcType=VARCHAR}
        and CORP_BUS_SUB_TYP = #{corpBusSubTyp, jdbcType=VARCHAR}
    </select>

    <!-- 根据业务类型、业务子类型、路径合作机构、对账日期查询对账批次信息-->
    <select id="getChkAccControlDO" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpo_acc_control
        where CHK_DT = #{chkDt, jdbcType=DATE}
        and RUT_CORG = #{rutCorg, jdbcType=VARCHAR}
        and CORP_BUS_TYP = #{corpBusTyp, jdbcType=VARCHAR}
        and CORP_BUS_SUB_TYP = #{corpBusSubTyp, jdbcType=VARCHAR}
    </select>

    <!-- 查询对账未完成的批次信息 -->
    <select id="getUnfinishedChkBat" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpo_acc_control
        where CHK_FIL_STS &lt; '4'
          and RUT_CORG = #{rutCorg, jdbcType=VARCHAR}
          and CORP_BUS_TYP = #{corpBusTyp, jdbcType=VARCHAR}
          and CORP_BUS_SUB_TYP = #{corpBusSubTyp, jdbcType=VARCHAR}
          order by chk_fil_dt asc
          limit 1
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_acc_control
        where CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.AccControlDO" >
        insert into cpo_acc_control
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="chkBatNo != null" >
                CHK_BAT_NO,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="chkFilDt != null" >
                CHK_FIL_DT,
            </if>
            <if test="chkFilNm != null" >
                CHK_FIL_NM,
            </if>
            <if test="chkFilSts != null" >
                CHK_FIL_STS,
            </if>
            <if test="fileRcvDt != null" >
                FILE_RCV_DT,
            </if>
            <if test="chkBegTm != null" >
                CHK_BEG_TM,
            </if>
            <if test="chkEndTm != null" >
                CHK_END_TM,
            </if>
            <if test="chkDt != null" >
                CHK_DT,
            </if>
            <if test="filTotAmt != null" >
                FIL_TOT_AMT,
            </if>
            <if test="filTotCnt != null" >
                FIL_TOT_CNT,
            </if>
            <if test="totMchAmt != null" >
                TOT_MCH_AMT,
            </if>
            <if test="totMchCnt != null" >
                TOT_MCH_CNT,
            </if>
            <if test="errTotCnt != null" >
                ERR_TOT_CNT,
            </if>
            <if test="errTotAmt != null" >
                ERR_TOT_AMT,
            </if>
            <if test="longAmt != null" >
                LONG_AMT,
            </if>
            <if test="longCnt != null" >
                LONG_CNT,
            </if>
            <if test="shortAmt != null" >
                SHORT_AMT,
            </if>
            <if test="shortCnt != null" >
                SHORT_CNT,
            </if>
            <if test="doubtAmt != null" >
                DOUBT_AMT,
            </if>
            <if test="doubtCnt != null" >
                DOUBT_CNT,
            </if>
            <if test="dbtErrAmt != null" >
                DBT_ERR_AMT,
            </if>
            <if test="dbtErrCnt != null" >
                DBT_ERR_CNT,
            </if>
            <if test="totDrAmt != null" >
                TOT_DR_AMT,
            </if>
            <if test="totDrNum != null" >
                TOT_DR_NUM,
            </if>
            <if test="totCrAmt != null" >
                TOT_CR_AMT,
            </if>
            <if test="totCrNum != null" >
                TOT_CR_NUM,
            </if>
            <if test="payTotAmt != null" >
                PAY_TOT_AMT,
            </if>
            <if test="rcvTotAmt != null" >
                RCV_TOT_AMT,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="chkBatNo != null" >
                #{chkBatNo,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="chkFilDt != null" >
                #{chkFilDt,jdbcType=DATE},
            </if>
            <if test="chkFilNm != null" >
                #{chkFilNm,jdbcType=VARCHAR},
            </if>
            <if test="chkFilSts != null" >
                #{chkFilSts,jdbcType=CHAR},
            </if>
            <if test="fileRcvDt != null" >
                #{fileRcvDt,jdbcType=DATE},
            </if>
            <if test="chkBegTm != null" >
                #{chkBegTm,jdbcType=TIME},
            </if>
            <if test="chkEndTm != null" >
                #{chkEndTm,jdbcType=TIME},
            </if>
            <if test="chkDt != null" >
                #{chkDt,jdbcType=DATE},
            </if>
            <if test="filTotAmt != null" >
                #{filTotAmt,jdbcType=DECIMAL},
            </if>
            <if test="filTotCnt != null" >
                #{filTotCnt,jdbcType=INTEGER},
            </if>
            <if test="totMchAmt != null" >
                #{totMchAmt,jdbcType=DECIMAL},
            </if>
            <if test="totMchCnt != null" >
                #{totMchCnt,jdbcType=INTEGER},
            </if>
            <if test="errTotCnt != null" >
                #{errTotCnt,jdbcType=INTEGER},
            </if>
            <if test="errTotAmt != null" >
                #{errTotAmt,jdbcType=DECIMAL},
            </if>
            <if test="longAmt != null" >
                #{longAmt,jdbcType=DECIMAL},
            </if>
            <if test="longCnt != null" >
                #{longCnt,jdbcType=INTEGER},
            </if>
            <if test="shortAmt != null" >
                #{shortAmt,jdbcType=DECIMAL},
            </if>
            <if test="shortCnt != null" >
                #{shortCnt,jdbcType=INTEGER},
            </if>
            <if test="doubtAmt != null" >
                #{doubtAmt,jdbcType=DECIMAL},
            </if>
            <if test="doubtCnt != null" >
                #{doubtCnt,jdbcType=INTEGER},
            </if>
            <if test="dbtErrAmt != null" >
                #{dbtErrAmt,jdbcType=DECIMAL},
            </if>
            <if test="dbtErrCnt != null" >
                #{dbtErrCnt,jdbcType=INTEGER},
            </if>
            <if test="totDrAmt != null" >
                #{totDrAmt,jdbcType=DECIMAL},
            </if>
            <if test="totDrNum != null" >
                #{totDrNum,jdbcType=INTEGER},
            </if>
            <if test="totCrAmt != null" >
                #{totCrAmt,jdbcType=DECIMAL},
            </if>
            <if test="totCrNum != null" >
                #{totCrNum,jdbcType=INTEGER},
            </if>
            <if test="payTotAmt != null" >
                #{payTotAmt,jdbcType=DECIMAL},
            </if>
            <if test="rcvTotAmt != null" >
                #{rcvTotAmt,jdbcType=DECIMAL},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.AccControlDO" >
        update cpo_acc_control
        <set >
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="chkFilDt != null" >
                CHK_FIL_DT = #{chkFilDt,jdbcType=DATE},
            </if>
            <if test="chkFilNm != null" >
                CHK_FIL_NM = #{chkFilNm,jdbcType=VARCHAR},
            </if>
            <if test="chkFilSts != null" >
                CHK_FIL_STS = #{chkFilSts,jdbcType=CHAR},
            </if>
            <if test="fileRcvDt != null" >
                FILE_RCV_DT = #{fileRcvDt,jdbcType=DATE},
            </if>
            <if test="chkBegTm != null" >
                CHK_BEG_TM = #{chkBegTm,jdbcType=TIME},
            </if>
            <if test="chkEndTm != null" >
                CHK_END_TM = #{chkEndTm,jdbcType=TIME},
            </if>
            <if test="chkDt != null" >
                CHK_DT = #{chkDt,jdbcType=DATE},
            </if>
            <if test="filTotAmt != null" >
                FIL_TOT_AMT = #{filTotAmt,jdbcType=DECIMAL},
            </if>
            <if test="filTotCnt != null" >
                FIL_TOT_CNT = #{filTotCnt,jdbcType=INTEGER},
            </if>
            <if test="totMchAmt != null" >
                TOT_MCH_AMT = #{totMchAmt,jdbcType=DECIMAL},
            </if>
            <if test="totMchCnt != null" >
                TOT_MCH_CNT = #{totMchCnt,jdbcType=INTEGER},
            </if>
            <if test="errTotCnt != null" >
                ERR_TOT_CNT = #{errTotCnt,jdbcType=INTEGER},
            </if>
            <if test="errTotAmt != null" >
                ERR_TOT_AMT = #{errTotAmt,jdbcType=DECIMAL},
            </if>
            <if test="longAmt != null" >
                LONG_AMT = #{longAmt,jdbcType=DECIMAL},
            </if>
            <if test="longCnt != null" >
                LONG_CNT = #{longCnt,jdbcType=INTEGER},
            </if>
            <if test="shortAmt != null" >
                SHORT_AMT = #{shortAmt,jdbcType=DECIMAL},
            </if>
            <if test="shortCnt != null" >
                SHORT_CNT = #{shortCnt,jdbcType=INTEGER},
            </if>
            <if test="doubtAmt != null" >
                DOUBT_AMT = #{doubtAmt,jdbcType=DECIMAL},
            </if>
            <if test="doubtCnt != null" >
                DOUBT_CNT = #{doubtCnt,jdbcType=INTEGER},
            </if>
            <if test="dbtErrAmt != null" >
                DBT_ERR_AMT = #{dbtErrAmt,jdbcType=DECIMAL},
            </if>
            <if test="dbtErrCnt != null" >
                DBT_ERR_CNT = #{dbtErrCnt,jdbcType=INTEGER},
            </if>
            <if test="totDrAmt != null" >
                TOT_DR_AMT = #{totDrAmt,jdbcType=DECIMAL},
            </if>
            <if test="totDrNum != null" >
                TOT_DR_NUM = #{totDrNum,jdbcType=INTEGER},
            </if>
            <if test="totCrAmt != null" >
                TOT_CR_AMT = #{totCrAmt,jdbcType=DECIMAL},
            </if>
            <if test="totCrNum != null" >
                TOT_CR_NUM = #{totCrNum,jdbcType=INTEGER},
            </if>
            <if test="payTotAmt != null" >
                PAY_TOT_AMT = #{payTotAmt,jdbcType=DECIMAL},
            </if>
            <if test="rcvTotAmt != null" >
                RCV_TOT_AMT = #{rcvTotAmt,jdbcType=DECIMAL},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR}
    </update>

    <!-- 查询对账未完成的批次信息 -->
    <select id="queryUnfinishedAccControl" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cpo.entity.AccParamDO">
        select
        <include refid="Base_Column_List" />
        from cpo_acc_control
        where CHK_FIL_STS &lt; '4'
        and RUT_CORG = #{rutCorg, jdbcType=VARCHAR}
        and CORP_BUS_TYP = #{corpBusTyp, jdbcType=CHAR}
        and CORP_BUS_SUB_TYP = #{corpBusSubTyp, jdbcType=CHAR}
        order by chk_fil_dt asc
        limit 1
    </select>

    <!--根据业务类型、业务子类型、路径合作机构、对账日期查询对账批次信息列表-->
    <!--检查上一对账日期之前，是否还有未完成的对账批次-->
    <select id="getAccControlListByLastChkDt" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cpo.entity.AccControlDO" >
        select
        <include refid="Base_Column_List" />
        from cpo_acc_control
        where CHK_DT &lt;= #{chkDt, jdbcType=DATE}
        and RUT_CORG = #{rutCorg, jdbcType=VARCHAR}
        and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        and CORP_BUS_SUB_TYP = #{corpBusSubTyp, jdbcType=CHAR}
    </select>

    <!-- 根据业务类型、业务子类型、路径合作机构、对账日期查询对账批次信息-->
    <select id="getAccControl" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpo_acc_control
        where CHK_DT = #{chkDt, jdbcType=DATE}
        and RUT_CORG = #{rutCorg, jdbcType=VARCHAR}
        and CORP_BUS_TYP = #{corpBusTyp, jdbcType=CHAR}
        and CORP_BUS_SUB_TYP = #{corpBusSubTyp, jdbcType=CHAR}
    </select>

</mapper>