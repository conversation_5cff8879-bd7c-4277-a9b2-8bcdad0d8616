<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpo.dao.IWithdrawOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpo.entity.WithdrawOrderDO" >
        <id column="WC_ORD_NO" property="wcOrdNo" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="CCY" property="ccy" jdbcType="CHAR" />
        <result column="CAP_TYP" property="capTyp" jdbcType="CHAR" />
        <result column="AC_NO" property="acNo" jdbcType="VARCHAR" />
        <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
        <result column="CAP_CORG" property="capCorg" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="REQ_ORD_NO" property="reqOrdNo" jdbcType="VARCHAR" />
        <result column="REQ_ORD_DT" property="reqOrdDt" jdbcType="DATE" />
        <result column="REQ_ORD_TM" property="reqOrdTm" jdbcType="TIME" />
        <result column="AGR_PAY_DT" property="agrPayDt" jdbcType="DATE" />
        <result column="STL_AC_PERD" property="stlAcPerd" jdbcType="VARCHAR" />
        <result column="WC_APL_AMT" property="wcAplAmt" jdbcType="DECIMAL" />
        <result column="HOLD_NO" property="holdNo" jdbcType="VARCHAR" />
        <result column="PSN_CRP_FLG" property="psnCrpFlg" jdbcType="CHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="USER_NM" property="userNm" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="ID_TYP" property="idTyp" jdbcType="CHAR" />
        <result column="ID_NO_ENC" property="idNoEnc" jdbcType="VARCHAR" />
        <result column="CRD_NO_ENC" property="crdNoEnc" jdbcType="VARCHAR" />
        <result column="CAP_CRD_NM" property="capCrdNm" jdbcType="VARCHAR" />
        <result column="WC_RMK" property="wcRmk" jdbcType="VARCHAR" />
        <result column="ORD_STS" property="ordSts" jdbcType="VARCHAR" />
        <result column="WC_WF_STS" property="wcWfSts" jdbcType="VARCHAR" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="WC_WF_RSN" property="wcWfRsn" jdbcType="VARCHAR" />
        <result column="WC_BAT_NO" property="wcBatNo" jdbcType="VARCHAR" />
        <result column="WC_BAT_SEQ" property="wcBatSeq" jdbcType="VARCHAR" />
        <result column="SUB_BRANCH" property="subbranch" jdbcType="VARCHAR" />
        <result column="POST_DT" property="postDt" jdbcType="DATE" />
        <result column="POST_TM" property="postTm" jdbcType="TIME" />
        <result column="AUTO_PAY_FLG" property="autoPayFlg" jdbcType="CHAR" />
        <result column="WDC_AUTO_FLG" property="wdcAutoFlg" jdbcType="CHAR" />
        <result column="PAYTM_FLG" property="paytmFlg" jdbcType="CHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="ORD_SUCC_DT" property="ordSuccDt" jdbcType="DATE" />
        <result column="ORD_SUCC_TM" property="ordSuccTm" jdbcType="TIME" />
        <result column="NTF_STS" property="ntfSts" jdbcType="VARCHAR" />
        <result column="NTF_DT" property="ntfDt" jdbcType="DATE" />
        <result column="NTF_TM" property="ntfTm" jdbcType="TIME" />
        <result column="NTF_RSP_CD" property="ntfRspCd" jdbcType="VARCHAR" />
        <result column="NTF_RSP_MSG" property="ntfRspMsg" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        WC_ORD_NO, ORD_DT, ORD_TM, AC_DT, CCY, CAP_TYP, AC_NO, ADDRESS, CAP_CORG, RUT_CORG, CRD_AC_TYP, CORP_BUS_TYP,
        CORP_BUS_SUB_TYP, REQ_ORD_NO, REQ_ORD_DT, REQ_ORD_TM, AGR_PAY_DT, STL_AC_PERD, WC_APL_AMT, 
        HOLD_NO, PSN_CRP_FLG, MBL_NO, USER_NM, USER_ID, ID_TYP, ID_NO_ENC, CRD_NO_ENC, CAP_CRD_NM, 
        WC_RMK, ORD_STS, WC_WF_STS, OPR_ID, WC_WF_RSN, WC_BAT_NO, WC_BAT_SEQ, POST_DT, POST_TM, 
        AUTO_PAY_FLG, WDC_AUTO_FLG, PAYTM_FLG, CREATE_TIME, MODIFY_TIME, TM_SMP, RMK, SUB_BRANCH
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpo_withdraw_order
        where WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
    </select>

    <!-- 根据主键，查询资金流出订单的审批状态 -->
    <select id="getWcWfStsByKey" resultType="java.lang.String" parameterType="java.lang.String" >
        select WC_WF_STS
        from cpo_withdraw_order
        where WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpo_withdraw_order
        where WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpo.entity.WithdrawOrderDO" >
        insert into cpo_withdraw_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="wcOrdNo != null" >
                WC_ORD_NO,
            </if>
            <if test="ordDt != null" >
                ORD_DT,
            </if>
            <if test="ordTm != null" >
                ORD_TM,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="ccy != null" >
                CCY,
            </if>
            <if test="capTyp != null" >
                CAP_TYP,
            </if>
            <if test="capCorg != null" >
                CAP_CORG,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="reqOrdNo != null" >
                REQ_ORD_NO,
            </if>
            <if test="reqOrdDt != null" >
                REQ_ORD_DT,
            </if>
            <if test="reqOrdTm != null" >
                REQ_ORD_TM,
            </if>
            <if test="agrPayDt != null" >
                AGR_PAY_DT,
            </if>
            <if test="stlAcPerd != null" >
                STL_AC_PERD,
            </if>
            <if test="wcAplAmt != null" >
                WC_APL_AMT,
            </if>
            <if test="holdNo != null" >
                HOLD_NO,
            </if>
            <if test="psnCrpFlg != null" >
                PSN_CRP_FLG,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="userNm != null" >
                USER_NM,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="idTyp != null" >
                ID_TYP,
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC,
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC,
            </if>
            <if test="capCrdNm != null" >
                CAP_CRD_NM,
            </if>
            <if test="wcRmk != null" >
                WC_RMK,
            </if>
            <if test="ordSts != null" >
                ORD_STS,
            </if>
            <if test="wcWfSts != null" >
                WC_WF_STS,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="wcWfRsn != null" >
                WC_WF_RSN,
            </if>
            <if test="wcBatNo != null" >
                WC_BAT_NO,
            </if>
            <if test="wcBatSeq != null" >
                WC_BAT_SEQ,
            </if>
            <if test="postDt != null" >
                POST_DT,
            </if>
            <if test="postTm != null" >
                POST_TM,
            </if>
            <if test="autoPayFlg != null" >
                AUTO_PAY_FLG,
            </if>
            <if test="wdcAutoFlg != null" >
                WDC_AUTO_FLG,
            </if>
            <if test="paytmFlg != null" >
                PAYTM_FLG,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="ordSuccDt != null" >
                ORD_SUCC_DT,
            </if>
            <if test="ordSuccTm != null" >
                ORD_SUCC_TM,
            </if>
            <if test="ntfSts != null" >
                NTF_STS,
            </if>
            <if test="ntfDt != null" >
                NTF_DT,
            </if>
            <if test="ntfTm != null" >
                NTF_TM,
            </if>
            <if test="ntfRspCd != null" >
                NTF_RSP_CD,
            </if>
            <if test="ntfRspMsg != null" >
                NTF_RSP_MSG,
            </if>
            <if test="subbranch != null" >
                SUB_BRANCH,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="firstAuditUser != null" >
                FIRST_AUDIT_USER,
            </if>
            <if test="firstAuditTime != null" >
                FIRST_AUDIT_TIME,
            </if>
            <if test="firstAuditResult != null" >
                FIRST_AUDIT_RESULT,
            </if>
            <if test="firstAuditOpinion != null" >
                FIRST_AUDIT_OPINION,
            </if>
            <if test="secondAuditUser != null" >
                SECOND_AUDIT_USER,
            </if>
            <if test="secondAuditTime != null" >
                SECOND_AUDIT_TIME,
            </if>
            <if test="secondAuditResult != null" >
                SECOND_AUDIT_RESULT,
            </if>
            <if test="secondAuditOpinion != null" >
                SECOND_AUDIT_OPINION,
            </if>
            <if test="executeTime != null" >
                EXECUTE_TIME,
            </if>
            <if test="rejectReason != null" >
                REJECT_REASON
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="wcOrdNo != null" >
                #{wcOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                #{ordTm,jdbcType=TIME},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=CHAR},
            </if>
            <if test="capTyp != null" >
                #{capTyp,jdbcType=CHAR},
            </if>
            <if test="capCorg != null" >
                #{capCorg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdNo != null" >
                #{reqOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdDt != null" >
                #{reqOrdDt,jdbcType=DATE},
            </if>
            <if test="reqOrdTm != null" >
                #{reqOrdTm,jdbcType=TIME},
            </if>
            <if test="agrPayDt != null" >
                #{agrPayDt,jdbcType=DATE},
            </if>
            <if test="stlAcPerd != null" >
                #{stlAcPerd,jdbcType=VARCHAR},
            </if>
            <if test="wcAplAmt != null" >
                #{wcAplAmt,jdbcType=DECIMAL},
            </if>
            <if test="holdNo != null" >
                #{holdNo,jdbcType=VARCHAR},
            </if>
            <if test="psnCrpFlg != null" >
                #{psnCrpFlg,jdbcType=CHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="userNm != null" >
                #{userNm,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="idTyp != null" >
                #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdNoEnc != null" >
                #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="capCrdNm != null" >
                #{capCrdNm,jdbcType=VARCHAR},
            </if>
            <if test="wcRmk != null" >
                #{wcRmk,jdbcType=VARCHAR},
            </if>
            <if test="ordSts != null" >
                #{ordSts,jdbcType=VARCHAR},
            </if>
            <if test="wcWfSts != null" >
                #{wcWfSts,jdbcType=VARCHAR},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="wcWfRsn != null" >
                #{wcWfRsn,jdbcType=VARCHAR},
            </if>
            <if test="wcBatNo != null" >
                #{wcBatNo,jdbcType=VARCHAR},
            </if>
            <if test="wcBatSeq != null" >
                #{wcBatSeq,jdbcType=VARCHAR},
            </if>
            <if test="postDt != null" >
                #{postDt,jdbcType=DATE},
            </if>
            <if test="postTm != null" >
                #{postTm,jdbcType=TIME},
            </if>
            <if test="autoPayFlg != null" >
                #{autoPayFlg,jdbcType=CHAR},
            </if>
            <if test="wdcAutoFlg != null" >
                #{wdcAutoFlg,jdbcType=CHAR},
            </if>
            <if test="paytmFlg != null" >
                #{paytmFlg,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="ordSuccDt != null" >
                #{ordSuccDt,jdbcType=DATE},
            </if>
            <if test="ordSuccTm != null" >
                #{ordSuccTm,jdbcType=TIME},
            </if>
            <if test="ntfSts != null" >
                #{ntfSts,jdbcType=VARCHAR},
            </if>
            <if test="ntfDt != null" >
                #{ntfDt,jdbcType=DATE},
            </if>
            <if test="ntfTm != null" >
                #{ntfTm,jdbcType=TIME},
            </if>
            <if test="ntfRspCd != null" >
                #{ntfRspCd,jdbcType=VARCHAR},
            </if>
            <if test="ntfRspMsg != null" >
                #{ntfRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="subbranch != null" >
                #{subbranch,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpo.entity.WithdrawOrderDO" >
        update cpo_withdraw_order
        <set >
            <if test="ordDt != null" >
                ORD_DT = #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                ORD_TM = #{ordTm,jdbcType=TIME},
            </if>
            <if test="acDt != null" >
                AC_DT = #{acDt,jdbcType=DATE},
            </if>
            <if test="ccy != null" >
                CCY = #{capTyp,jdbcType=CHAR},
            </if>
            <if test="capTyp != null" >
                CAP_TYP = #{capTyp,jdbcType=CHAR},
            </if>
            <if test="capCorg != null" >
                CAP_CORG = #{capCorg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdNo != null" >
                REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdDt != null" >
                REQ_ORD_DT = #{reqOrdDt,jdbcType=DATE},
            </if>
            <if test="reqOrdTm != null" >
                REQ_ORD_TM = #{reqOrdTm,jdbcType=TIME},
            </if>
            <if test="agrPayDt != null" >
                AGR_PAY_DT = #{agrPayDt,jdbcType=DATE},
            </if>
            <if test="stlAcPerd != null" >
                STL_AC_PERD = #{stlAcPerd,jdbcType=VARCHAR},
            </if>
            <if test="wcAplAmt != null" >
                WC_APL_AMT = #{wcAplAmt,jdbcType=DECIMAL},
            </if>
            <if test="holdNo != null" >
                HOLD_NO = #{holdNo,jdbcType=VARCHAR},
            </if>
            <if test="psnCrpFlg != null" >
                PSN_CRP_FLG = #{psnCrpFlg,jdbcType=CHAR},
            </if>
            <if test="mblNo != null" >
                MBL_NO = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="userNm != null" >
                USER_NM = #{userNm,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="idTyp != null" >
                ID_TYP = #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC = #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="capCrdNm != null" >
                CAP_CRD_NM = #{capCrdNm,jdbcType=VARCHAR},
            </if>
            <if test="wcRmk != null" >
                WC_RMK = #{wcRmk,jdbcType=VARCHAR},
            </if>
            <if test="ordSts != null" >
                ORD_STS = #{ordSts,jdbcType=VARCHAR},
            </if>
            <if test="wcWfSts != null" >
                WC_WF_STS = #{wcWfSts,jdbcType=VARCHAR},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="wcWfRsn != null" >
                WC_WF_RSN = #{wcWfRsn,jdbcType=VARCHAR},
            </if>
            <if test="wcBatNo != null" >
                WC_BAT_NO = #{wcBatNo,jdbcType=VARCHAR},
            </if>
            <if test="wcBatSeq != null" >
                WC_BAT_SEQ = #{wcBatSeq,jdbcType=VARCHAR},
            </if>
            <if test="postDt != null" >
                POST_DT = #{postDt,jdbcType=DATE},
            </if>
            <if test="postTm != null" >
                POST_TM = #{postTm,jdbcType=TIME},
            </if>
            <if test="autoPayFlg != null" >
                AUTO_PAY_FLG = #{autoPayFlg,jdbcType=CHAR},
            </if>
            <if test="wdcAutoFlg != null" >
                WDC_AUTO_FLG = #{wdcAutoFlg,jdbcType=CHAR},
            </if>
            <if test="paytmFlg != null" >
                PAYTM_FLG = #{paytmFlg,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="ordSuccDt != null" >
                ORD_SUCC_DT = #{ordSuccDt,jdbcType=DATE},
            </if>
            <if test="ordSuccTm != null" >
                ORD_SUCC_TM = #{ordSuccTm,jdbcType=TIME},
            </if>
            <if test="ntfSts != null" >
                NTF_STS = #{ntfSts,jdbcType=VARCHAR},
            </if>
            <if test="ntfDt != null" >
                NTF_DT = #{ntfDt,jdbcType=DATE},
            </if>
            <if test="ntfTm != null" >
                NTF_TM = #{ntfTm,jdbcType=TIME},
            </if>
            <if test="ntfRspCd != null" >
                NTF_RSP_CD = #{ntfRspCd,jdbcType=VARCHAR},
            </if>
            <if test="ntfRspMsg != null" >
                NTF_RSP_MSG = #{ntfRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="firstAuditUser != null" >
                FIRST_AUDIT_USER = #{firstAuditUser,jdbcType=VARCHAR},
            </if>
            <if test="firstAuditTime != null" >
                FIRST_AUDIT_TIME = #{firstAuditTime,jdbcType=DATE},
            </if>
            <if test="firstAuditResult != null" >
                FIRST_AUDIT_RESULT = #{firstAuditResult,jdbcType=VARCHAR},
            </if>
            <if test="firstAuditOpinion != null" >
                FIRST_AUDIT_OPINION = #{firstAuditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="secondAuditUser != null" >
                SECOND_AUDIT_USER = #{secondAuditUser},
            </if>
            <if test="secondAuditTime != null" >
                SECOND_AUDIT_TIME = #{secondAuditTime,jdbcType=DATE},
            </if>
            <if test="secondAuditResult != null" >
                SECOND_AUDIT_RESULT = #{secondAuditResult,jdbcType=VARCHAR},
            </if>
            <if test="secondAuditOpinion != null" >
                SECOND_AUDIT_OPINION = #{secondAuditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="executeTime != null" >
                EXECUTE_TIME = #{executeTime,jdbcType=DATE},
            </if>
            <if test="rejectReason != null" >
                REJECT_REASON = #{rejectReason,jdbcType=VARCHAR}
            </if>
        </set>
        where WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
    </update>

    <!-- 提现审批，更新提现订单的审批状态，wc_wf_sts由 P1:待审批 更新为 E1:审批通过 或 R9:审批拒绝 -->
    <update id="updateOrderWcWfSts" parameterType="com.hisun.lemon.cpo.entity.WithdrawOrderDO" >
        update cpo_withdraw_order
          set WC_WF_STS = #{wcWfSts,jdbcType=VARCHAR},
              OPR_ID = #{oprId,jdbcType=VARCHAR},
              WC_WF_RSN = #{wcWfRsn,jdbcType=VARCHAR},
              MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
        where WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
          and WC_WF_STS='P1'
    </update>

    <!--分页查询待通知的提现订单-->
    <select id="getWithdrawOrderToNotify" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpo_withdraw_order
        where NTF_STS = 'W'
          and ORD_STS in ('S1', 'F1')
        order by CREATE_TIME
        limit #{beginNum,jdbcType=INTEGER}, #{maxQueryNum,jdbcType=INTEGER}
    </select>

    <!--获取银行对账明细 List-->
    <select id="getWithdrawOrderList" resultMap="BaseResultMap" >
        select WC_ORD_NO,WC_APL_AMT,ORD_STS,REQ_ORD_NO,CORP_BUS_SUB_TYP
        from cpo_withdraw_order
        where AC_DT = #{ordDt,jdbcType=DATE}
          and ORD_STS = #{ordSts,jdbcType=VARCHAR}
          and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR}
        ORDER BY WC_ORD_NO ASC
        limit #{beginNum,jdbcType=INTEGER}, #{countNum,jdbcType=INTEGER}
    </select>
</mapper>