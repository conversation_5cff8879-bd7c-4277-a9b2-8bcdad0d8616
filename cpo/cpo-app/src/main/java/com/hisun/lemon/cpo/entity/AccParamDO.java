/*
 * @ClassName ChkAccParamDO
 * @Description
 * @version 1.0
 * @Date 2017-07-12 19:25:57
 */
package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class AccParamDO extends BaseDO {
    /**
     * @Fields chkPmId id
     */
    private String chkPmId;
    /**
     * @Fields rutCorg 路径机构
     */
    private String rutCorg;
    /**
     * @Fields corpBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields effFlg 生效标识，Y生效，N不生效
     */
    private String effFlg;
    /**
     * @Fields chkDoFlg 是否对账标识，Y对账，N不对账
     */
    private String chkDoFlg;
    /**
     * @Fields batChkFlg 批量对账标识，B批量,O:实时对账
     */
    private String batChkFlg;
    /**
     * @Fields chkBegTm 对账开始时间
     */
    private LocalTime chkBegTm;
    /**
     * @Fields chkEndTm 对账结束时间
     */
    private LocalTime chkEndTm;
    /**
     * @Fields splAbleFlg 补单标识，Y支持，N不支持
     */
    private String splAbleFlg;
    /**
     * @Fields canAbleFlg 撤单标识，Y支持，N不支持
     */
    private String canAbleFlg;
    /**
     * @Fields auSkFlg 自动跳账标识，Y支持，N不支持
     */
    private String auSkFlg;
    /**
     * @Fields oprId 操作员
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getChkPmId() {
        return chkPmId;
    }

    public void setChkPmId(String chkPmId) {
        this.chkPmId = chkPmId;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getChkDoFlg() {
        return chkDoFlg;
    }

    public void setChkDoFlg(String chkDoFlg) {
        this.chkDoFlg = chkDoFlg;
    }

    public String getBatChkFlg() {
        return batChkFlg;
    }

    public void setBatChkFlg(String batChkFlg) {
        this.batChkFlg = batChkFlg;
    }

    public LocalTime getChkBegTm() {
        return chkBegTm;
    }

    public void setChkBegTm(LocalTime chkBegTm) {
        this.chkBegTm = chkBegTm;
    }

    public LocalTime getChkEndTm() {
        return chkEndTm;
    }

    public void setChkEndTm(LocalTime chkEndTm) {
        this.chkEndTm = chkEndTm;
    }

    public String getSplAbleFlg() {
        return splAbleFlg;
    }

    public void setSplAbleFlg(String splAbleFlg) {
        this.splAbleFlg = splAbleFlg;
    }

    public String getCanAbleFlg() {
        return canAbleFlg;
    }

    public void setCanAbleFlg(String canAbleFlg) {
        this.canAbleFlg = canAbleFlg;
    }

    public String getAuSkFlg() {
        return auSkFlg;
    }

    public void setAuSkFlg(String auSkFlg) {
        this.auSkFlg = auSkFlg;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}