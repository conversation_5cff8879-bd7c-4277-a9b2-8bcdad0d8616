package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 银行对账明细基础类
 * Created by gongle<PERSON> on 2017/7/11.
 */
public class BaseCheckAccDO extends BaseDO{
    /**
     * @Fields checkKey 对账外键(订单号)
     */
    private String checkKey;
    /**
     * @Fields checkAmt 对账金额(订单金额)
     */
    private BigDecimal checkAmt;
    /**
     * @Fields errorCount 存疑次数
     */
    private Integer errorCount;
    /**
     * @Fields orgFeeAmt 机构手续费
     */
    private BigDecimal orgFeeAmt;
    /**
     * @Fields rutCorg 路径合作机构
     */
    private String rutCorg;
    /**
     * @Fields copBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields copBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields chkBatNo 对账批次号
     */
    private String chkBatNo;
    /**
     * @Fields chkFilDt 对账文件日期
     */
    private LocalDate chkFilDt;
    /**
     * @Fields chkFilNm 对账文件名
     */
    private String chkFilNm;
    /**
     * @Fields txSts 银行交易状态
     */
    private String txSts;
    /**
     * @Fields orgResCd 银行返回码
     */
    private String orgResCd;
    /**
     * @Fields orgResMsg 银行返回信息
     */
    private String orgResMsg;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getCheckKey() {
        return checkKey;
    }

    public void setCheckKey(String checkKey) {
        this.checkKey = checkKey;
    }

    public BigDecimal getCheckAmt() {
        return checkAmt;
    }

    public void setCheckAmt(BigDecimal checkAmt) {
        this.checkAmt = checkAmt;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public BigDecimal getOrgFeeAmt() {
        return orgFeeAmt;
    }

    public void setOrgFeeAmt(BigDecimal orgFeeAmt) {
        this.orgFeeAmt = orgFeeAmt;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    public LocalDate getChkFilDt() {
        return chkFilDt;
    }

    public void setChkFilDt(LocalDate chkFilDt) {
        this.chkFilDt = chkFilDt;
    }

    public String getChkFilNm() {
        return chkFilNm;
    }

    public void setChkFilNm(String chkFilNm) {
        this.chkFilNm = chkFilNm;
    }

    public String getTxSts() {
        return txSts;
    }

    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    public String getOrgResCd() {
        return orgResCd;
    }

    public void setOrgResCd(String orgResCd) {
        this.orgResCd = orgResCd;
    }

    public String getOrgResMsg() {
        return orgResMsg;
    }

    public void setOrgResMsg(String orgResMsg) {
        this.orgResMsg = orgResMsg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}
