//package com.hisun.lemon.cpo.mq;
//
//import com.hisun.lemon.common.utils.DateTimeUtils;
//import com.hisun.lemon.common.utils.JudgeUtils;
//import com.hisun.lemon.cpo.common.CpoConstants;
//import com.hisun.lemon.cpo.dao.IWithdrawOrderDao;
//import com.hisun.lemon.cpo.entity.WithdrawOrderDO;
//import com.hisun.lemon.csm.client.CsmServerClient;
//import com.hisun.lemon.csm.constants.IConstants;
//import com.hisun.lemon.csm.dto.SettleConfirmReqDTO;
//import com.hisun.lemon.framework.data.GenericCmdDTO;
//import com.hisun.lemon.framework.data.GenericDTO;
//import com.hisun.lemon.framework.data.GenericRspDTO;
//import com.hisun.lemon.framework.stream.MessageHandler;
//import com.hisun.lemon.jcommon.encrypt.EncryptionUtils;
//import com.hisun.lemon.pwm.client.PwmWithdrawClient;
//import com.hisun.lemon.pwm.dto.WithdrawResultDTO;
//import com.hisun.lemon.tam.client.TransferOrderClient;
//import com.hisun.lemon.tam.constants.TamConstants;
//import com.hisun.lemon.tam.dto.ResultTransferOrderDTO;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 提现结果异步通知线程
// */
//@Component("noticeHandler")
//public class WithdrawNotifyHandler implements MessageHandler<WithdrawOrderDO> {
//    private static final Logger logger = LoggerFactory.getLogger(WithdrawNotifyHandler.class);
//
//    /**
//     * 充提模块，个人提现结果通知
//     */
//    @Resource
//    private PwmWithdrawClient pwmWithdrawClient;
//
//    /**
//     * 转账模块，转账到银行卡通知
//     */
//    @Resource
//    private TransferOrderClient transferOrderClient;
//
//    /**
//     * 清分结算模块，商户结算结果通知
//     */
//    @Resource
//    private CsmServerClient csmServerClient;
//
//    /**
//     * 通知成功，更新提现订单的结果通知标志为 Y-已通知
//     */
//    @Resource
//    private IWithdrawOrderDao withdrawOrderDao;
//
//    /**
//     * 个人提现订单，发起结果通知
//     */
//    public void pwmCompleteOrder(WithdrawOrderDO withdrawOrderDO) {
//        try {
//            logger.debug("WithdrawNotifyThread.pwmCompleteOrder() 异步通知提现结果");
//
//            GenericDTO<WithdrawResultDTO> genericReqDTO = new GenericDTO<>();
//            WithdrawResultDTO withdrawResultDTO = new WithdrawResultDTO();
//            withdrawResultDTO.setOrderNo(withdrawOrderDO.getReqOrdNo());
//            withdrawResultDTO.setAcTm(withdrawOrderDO.getOrdDt());
//            withdrawResultDTO.setOrderStatus(withdrawOrderDO.getOrdSts());
//            withdrawResultDTO.setWcActAmt(withdrawOrderDO.getWcAplAmt());
//            withdrawResultDTO.setRspOrderNo(withdrawOrderDO.getWcOrdNo());
//
//            //获取银行卡号后四位
//            String crdNoEnc = withdrawOrderDO.getCrdNoEnc();
//            String crdNo = EncryptionUtils.decrypt(crdNoEnc);
//            withdrawResultDTO.setCardNoLast(crdNo.substring(crdNo.length() - 4));
//            genericReqDTO.setBody(withdrawResultDTO);
//
//            //推送结果返回信息
//            GenericRspDTO genericRspDTO = pwmWithdrawClient.completeOrder(genericReqDTO);
//            String msgCd = genericRspDTO.getMsgCd();
//            String msgInfo = genericRspDTO.getMsgInfo();
//            logger.debug("WithdrawNotifyThread.pwmCompleteOrder() 个人提现结果异步通知，返回码=" + msgCd + "; 返回消息=" + msgInfo);
//
//            //提现结果通知成功，更新订单的通知返回码、返回信息、通知日期、通知时间
//            WithdrawOrderDO updateWithdrawOrderDO = new WithdrawOrderDO();
//            updateWithdrawOrderDO.setWcOrdNo(withdrawOrderDO.getWcOrdNo());
//            updateWithdrawOrderDO.setNtfRspCd(msgCd);
//            updateWithdrawOrderDO.setNtfRspMsg(msgInfo);
//            updateWithdrawOrderDO.setNtfDt(DateTimeUtils.getCurrentLocalDate());
//            updateWithdrawOrderDO.setNtfTm(DateTimeUtils.getCurrentLocalTime());
//            if (JudgeUtils.isSuccess(msgCd)) {
//                updateWithdrawOrderDO.setNtfSts(CpoConstants.NOTIFY_STATUS_SUCCESS);
//            } else {
//                updateWithdrawOrderDO.setNtfSts(CpoConstants.NOTIFY_STATUS_FAILURE);
//            }
//            withdrawOrderDao.update(updateWithdrawOrderDO);
//        } catch (Exception e) {
//            logger.error("WithdrawNotifyThread.pwmCompleteOrder() 个人提现结果异步通知失败，异常为", e);
//            return;
//        }
//    }
//
//    /**
//     * 转账到银行卡订单，发起结果通知
//     */
//    public void tamTransferOrderResult(WithdrawOrderDO withdrawOrderDO) {
//        try {
//            logger.debug("WithdrawNotifyThread.tamTransferOrderResult() 转账到银行卡订单异步通知结果");
//
//            GenericDTO<ResultTransferOrderDTO> genericReqDTO = new GenericDTO<>();
//            ResultTransferOrderDTO resultTransferOrderDTO = new ResultTransferOrderDTO();
//            resultTransferOrderDTO.setOrderNo(withdrawOrderDO.getReqOrdNo());
//            resultTransferOrderDTO.setAmount(withdrawOrderDO.getWcAplAmt());
//            resultTransferOrderDTO.setTxType(withdrawOrderDO.getCorpBusTyp());
//            resultTransferOrderDTO.setBusType(withdrawOrderDO.getCorpBusSubTyp());
//            resultTransferOrderDTO.setOrderCcy(withdrawOrderDO.getCcy());
//
//            //判断订单状态
//            if (JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.ORD_SUCCESS)) {
//                resultTransferOrderDTO.setOrderSts(TamConstants.ORD_STS_S);
//            } else {
//                resultTransferOrderDTO.setOrderSts(TamConstants.ORD_STS_F);
//            }
//            genericReqDTO.setBody(resultTransferOrderDTO);
//
//            //通知返回结果
//            GenericRspDTO genericRspDTO = transferOrderClient.transferOrderResult(genericReqDTO);
//            String msgCd = genericRspDTO.getMsgCd();
//            String msgInfo = genericRspDTO.getMsgInfo();
//            logger.debug("WithdrawNotifyThread.tamTransferOrderResult() 转账到银行卡结果异步通知，返回码=" + msgCd + "; 返回消息=" + msgInfo);
//
//            //提现结果通知成功，更新订单的通知返回码、返回信息、通知日期、通知时间
//            WithdrawOrderDO updateWithdrawOrderDO = new WithdrawOrderDO();
//            updateWithdrawOrderDO.setWcOrdNo(withdrawOrderDO.getWcOrdNo());
//            updateWithdrawOrderDO.setNtfRspCd(msgCd);
//            updateWithdrawOrderDO.setNtfRspMsg(msgInfo);
//            updateWithdrawOrderDO.setNtfDt(DateTimeUtils.getCurrentLocalDate());
//            updateWithdrawOrderDO.setNtfTm(DateTimeUtils.getCurrentLocalTime());
//            if (JudgeUtils.isSuccess(msgCd)) {
//                updateWithdrawOrderDO.setNtfSts(CpoConstants.NOTIFY_STATUS_SUCCESS);
//            } else {
//                updateWithdrawOrderDO.setNtfSts(CpoConstants.NOTIFY_STATUS_FAILURE);
//            }
//            withdrawOrderDao.update(updateWithdrawOrderDO);
//        } catch (Exception e) {
//            logger.error("WithdrawNotifyThread.tamTransferOrderResult() 转账到银行卡订单异步通知失败，异常为", e);
//            return;
//        }
//    }
//
//    /**
//     * 商户结算订单，发起结果通知
//     */
//    public void csmSettleConfirm(WithdrawOrderDO withdrawOrderDO) {
//        try {
//            logger.debug("WithdrawNotifyThread.csmSettleConfirm() 商户结算订单异步通知结果");
//
//            GenericDTO<SettleConfirmReqDTO> genericReqDTO = new GenericDTO<>();
//            SettleConfirmReqDTO settleConfirmReqDTO = new SettleConfirmReqDTO();
//            settleConfirmReqDTO.setOrderNo(withdrawOrderDO.getWcOrdNo());
//            settleConfirmReqDTO.setMessage(withdrawOrderDO.getWcRmk());
//
//            //判断订单状态
//            if (JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.ORD_SUCCESS)) {
//                settleConfirmReqDTO.setStats(IConstants.ORDER_STATUS_SUCCESS);
//            } else {
//                settleConfirmReqDTO.setStats(IConstants.ORDER_STATUS_FAILURE);
//            }
//            genericReqDTO.setBody(settleConfirmReqDTO);
//
//            //返回通知结果
//            GenericRspDTO genericRspDTO = csmServerClient.settleConfirm(genericReqDTO);
//            String msgCd = genericRspDTO.getMsgCd();
//            String msgInfo = genericRspDTO.getMsgInfo();
//            logger.debug("WithdrawNotifyThread.csmSettleConfirm() 商户结算结果异步通知，返回码=" + msgCd + "; 返回消息=" + msgInfo);
//
//            //提现结果通知成功，更新订单的通知返回码、返回信息、通知日期、通知时间
//            WithdrawOrderDO updateWithdrawOrderDO = new WithdrawOrderDO();
//            updateWithdrawOrderDO.setWcOrdNo(withdrawOrderDO.getWcOrdNo());
//            updateWithdrawOrderDO.setNtfRspCd(msgCd);
//            updateWithdrawOrderDO.setNtfRspMsg(msgInfo);
//            updateWithdrawOrderDO.setNtfDt(DateTimeUtils.getCurrentLocalDate());
//            updateWithdrawOrderDO.setNtfTm(DateTimeUtils.getCurrentLocalTime());
//            if (JudgeUtils.isSuccess(msgCd)) {
//                updateWithdrawOrderDO.setNtfSts(CpoConstants.NOTIFY_STATUS_SUCCESS);
//            } else {
//                updateWithdrawOrderDO.setNtfSts(CpoConstants.NOTIFY_STATUS_FAILURE);
//            }
//            withdrawOrderDao.update(updateWithdrawOrderDO);
//        } catch (Exception e) {
//            logger.error("WithdrawNotifyThread.csmSettleConfirm() 商户结算订单异步通知结果失败，异常为", e);
//            return;
//        }
//    }
//
//    /**
//     * 接收发布的消息
//     */
//    @Override
//    public void onMessageReceive(GenericCmdDTO<WithdrawOrderDO> genericCmdDTO) {
//        WithdrawOrderDO withdrawOrderDO = genericCmdDTO.getBody();
//
//        //根据业务子类型，异步通知其他模块
//        String corpBusSubTyp = withdrawOrderDO.getCorpBusSubTyp();
//        switch (corpBusSubTyp) {
//            //个人提现
//            case "0601":
//                pwmCompleteOrder(withdrawOrderDO);
//                break;
//
//            //转账到银行卡
//            case "0602":
//                tamTransferOrderResult(withdrawOrderDO);
//                break;
//
//            //商户结算
//            case "0603":
//                csmSettleConfirm(withdrawOrderDO);
//                break;
//        }
//    }
//
//}
