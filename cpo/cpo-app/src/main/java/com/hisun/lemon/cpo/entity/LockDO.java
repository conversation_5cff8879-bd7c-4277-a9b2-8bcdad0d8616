package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDateTime;

/**
 * 对账交易锁对象
 */
public class LockDO extends BaseDO {

    /**
     * 锁ID
     */
    private String lockId;

    /**
     * 锁名称
     */
    private String lockName;

    /**
     * 锁状态
     */
    private String lockSts;

    /**
     * 时间戳
     */
    private LocalDateTime tmSmp;

    public String getLockId() {
        return lockId;
    }

    public void setLockId(String lockId) {
        this.lockId = lockId;
    }

    public String getLockName() {
        return lockName;
    }

    public void setLockName(String lockName) {
        this.lockName = lockName;
    }

    public String getLockSts() {
        return lockSts;
    }

    public void setLockSts(String lockSts) {
        this.lockSts = lockSts;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

}
