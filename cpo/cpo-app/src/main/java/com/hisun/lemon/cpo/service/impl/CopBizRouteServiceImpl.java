package com.hisun.lemon.cpo.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.cpo.dao.ICopBizRouteDao;
import com.hisun.lemon.cpo.entity.CopBizRouteDO;
import com.hisun.lemon.cpo.service.ICopBizRouteService;

/**
 * 业务合作路由service实现
 * 
 * <AUTHOR>
 *
 */
@Transactional
@Service
public class CopBizRouteServiceImpl extends BaseService implements ICopBizRouteService {

	@Resource
	private ICopBizRouteDao rutInfDao;

	@Override
	public boolean add(CopBizRouteDO copBizRouteDO) {
		boolean bool = false;
		int num = rutInfDao.insert(copBizRouteDO);
		if (num > 0) {
			bool = true;
		}
		return bool;
	}

	@Override
	public boolean update(CopBizRouteDO copBizRouteDO) {
		boolean bool = false;
		int num = rutInfDao.update(copBizRouteDO);
		if (num > 0) {
			bool = true;
		}
		return bool;
	}

	@Override
	public boolean updateByKey(String key, CopBizRouteDO copBizRouteDO) {
		return false;
	}

	@Override
	public CopBizRouteDO selectByKey(String rutInfId) {
		return rutInfDao.get(rutInfId);
	}

	@Override
	public CopBizRouteDO selectByDO(CopBizRouteDO copBizRouteDO) {
		return null;
	}

	@Override
	public boolean deleteByKey(String rutInfId) {
		boolean bool = false;
		int num = rutInfDao.delete(rutInfId);
		if (num > 0) {
			bool = true;
		}
		return bool;
	}

	@Override
	public boolean deleteByDO(CopBizRouteDO copBizRouteDO) {
		return false;
	}

	@Override
	public List<CopBizRouteDO> listByDO(CopBizRouteDO copBizRouteDO) {
		return rutInfDao.getAgcyRouteList(copBizRouteDO);
	}

	@Override
	public String selectRutEffFlg(CopBizRouteDO copBizRouteDO) {
		return rutInfDao.getRutEffFlg(copBizRouteDO);
	}

	@Override
	public CopBizRouteDO selectAgcyRoute(CopBizRouteDO copBizRouteDO) {
		return rutInfDao.getAgcyRoute(copBizRouteDO);
	}

	

}
