/*
 * @ClassName AccCfgDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-20 20:30:24
 */
package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AccCfgDO extends BaseDO {
    /**
     * @Fields accCfgId id
     */
    private String accCfgId;
    /**
     * @Fields rutCorg 路径机构
     */
    private String rutCorg;
    /**
     * @Fields corpBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields chkClazz 获取文件的类
     */
    private String chkClazz;
    /**
     * @Fields getFileMethod 获取文件的方法
     */
    private String getFileMethod;
    /**
     * @Fields importFields 入库的字段
     */
    private String importFields;
    /**
     * @Fields importClazz 入库的明细类
     */
    private String importClazz;
    /**
     * @Fields importMethod 入库的方法
     */
    private String importMethod;
    /**
     * @Fields splitSign 分隔符
     */
    private String splitSign;
    /**
     * @Fields chkFilePath 文件路径
     */
    private String chkFilePath;
    /**
     * @Fields continueNum 跳过多少行
     */
    private Integer continueNum;
    /**
     * @Fields selectDetailMethod 查询明细方法
     */
    private String selectDetailMethod;
    /**
     * @Fields successFlag 对账成功标志
     */
    private String successFlag;
    /**
     * @Fields checkKeyFiled 对账键值对应的域
     */
    private String checkKeyFiled;
    /**
     * @Fields checkKeyBakFiled 对账键值备份对应的域
     */
    private String checkKeyBakFiled;
    /**
     * @Fields checkAmtFiled 对账金额对应的域
     */
    private String checkAmtFiled;
    /**
     * @Fields txStsFiled 对账状态对应的域
     */
    private String txStsFiled;
    /**
     * @Fields queryNum 对账时每次查询的笔数
     */
    private Integer queryNum;
    /**
     * @Fields updateMethod 更新对账明细方法
     */
    private String updateMethod;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getAccCfgId() {
        return accCfgId;
    }

    public void setAccCfgId(String accCfgId) {
        this.accCfgId = accCfgId;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getChkClazz() {
        return chkClazz;
    }

    public void setChkClazz(String chkClazz) {
        this.chkClazz = chkClazz;
    }

    public String getGetFileMethod() {
        return getFileMethod;
    }

    public void setGetFileMethod(String getFileMethod) {
        this.getFileMethod = getFileMethod;
    }

    public String getImportFields() {
        return importFields;
    }

    public void setImportFields(String importFields) {
        this.importFields = importFields;
    }

    public String getImportClazz() {
        return importClazz;
    }

    public void setImportClazz(String importClazz) {
        this.importClazz = importClazz;
    }

    public String getImportMethod() {
        return importMethod;
    }

    public void setImportMethod(String importMethod) {
        this.importMethod = importMethod;
    }

    public String getSplitSign() {
        return splitSign;
    }

    public void setSplitSign(String splitSign) {
        this.splitSign = splitSign;
    }

    public String getChkFilePath() {
        return chkFilePath;
    }

    public void setChkFilePath(String chkFilePath) {
        this.chkFilePath = chkFilePath;
    }

    public Integer getContinueNum() {
        return continueNum;
    }

    public void setContinueNum(Integer continueNum) {
        this.continueNum = continueNum;
    }

    public String getSelectDetailMethod() {
        return selectDetailMethod;
    }

    public void setSelectDetailMethod(String selectDetailMethod) {
        this.selectDetailMethod = selectDetailMethod;
    }

    public String getSuccessFlag() {
        return successFlag;
    }

    public void setSuccessFlag(String successFlag) {
        this.successFlag = successFlag;
    }

    public String getCheckKeyFiled() {
        return checkKeyFiled;
    }

    public void setCheckKeyFiled(String checkKeyFiled) {
        this.checkKeyFiled = checkKeyFiled;
    }

    public String getCheckKeyBakFiled() {
        return checkKeyBakFiled;
    }

    public void setCheckKeyBakFiled(String checkKeyBakFiled) {
        this.checkKeyBakFiled = checkKeyBakFiled;
    }

    public String getCheckAmtFiled() {
        return checkAmtFiled;
    }

    public void setCheckAmtFiled(String checkAmtFiled) {
        this.checkAmtFiled = checkAmtFiled;
    }

    public String getTxStsFiled() {
        return txStsFiled;
    }

    public void setTxStsFiled(String txStsFiled) {
        this.txStsFiled = txStsFiled;
    }

    public Integer getQueryNum() {
        return queryNum;
    }

    public void setQueryNum(Integer queryNum) {
        this.queryNum = queryNum;
    }

    public String getUpdateMethod() {
        return updateMethod;
    }

    public void setUpdateMethod(String updateMethod) {
        this.updateMethod = updateMethod;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}