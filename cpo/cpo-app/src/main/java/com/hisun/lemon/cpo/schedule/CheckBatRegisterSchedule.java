package com.hisun.lemon.cpo.schedule;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpo.service.ICheckAccControlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 定时任务-创建对账批次
 */
@Component
public class CheckBatRegisterSchedule {
    /**
     * 注入对账主控服务
     */
    @Resource
    private ICheckAccControlService checkAccControlService;

    private static final Logger logger = LoggerFactory.getLogger(CheckBatRegisterSchedule.class);

    /**
     * 定时任务执行方法
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
//    @Scheduled(cron = "0 0/20 0-2 * * ?")
    public void execute() {
        logger.debug("==================生成对账批次定时任务 开始时间：" + DateTimeUtils.getCurrentDateTimeStr());

        //取上一个日期
        LocalDate checkDt = DateTimeUtils.getCurrentLocalDate().minusDays(1L);

        //开始生成批次
        checkAccControlService.registerChkBatNo(checkDt);

        logger.debug("==================生成对账批次定时任务 结束时间：" + DateTimeUtils.getCurrentDateTimeStr());
    }
}
