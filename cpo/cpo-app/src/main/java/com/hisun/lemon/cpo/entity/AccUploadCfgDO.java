/*
 * @ClassName AccUploadCfgDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-14 09:46:37
 */
package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDateTime;

public class AccUploadCfgDO extends BaseDO {
    /**
     * @Fields chkId 主键id
     */
    private String chkId;
    /**
     * @Fields rutCorg 路径机构或模块名
     */
    private String rutCorg;
    /**
     * @Fields localFilePath 本地路径
     */
    private String localFilePath;
    /**
     * @Fields uploadIp 上传ip
     */
    private String uploadIp;
    /**
     * @Fields uploadPort 上传端口
     */
    private String uploadPort;
    /**
     * @Fields connectTime 连接超时时长(毫秒)
     */
    private Integer connectTime;
    /**
     * @Fields uploadPath 上传路径
     */
    private String uploadPath;
    /**
     * @Fields uploadNm 服务器用户名
     */
    private String uploadNm;
    /**
     * @Fields uploadPwd 服务器密码
     */
    private String uploadPwd;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getChkId() {
        return chkId;
    }

    public void setChkId(String chkId) {
        this.chkId = chkId;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getLocalFilePath() {
        return localFilePath;
    }

    public void setLocalFilePath(String localFilePath) {
        this.localFilePath = localFilePath;
    }

    public String getUploadIp() {
        return uploadIp;
    }

    public void setUploadIp(String uploadIp) {
        this.uploadIp = uploadIp;
    }

    public String getUploadPort() {
        return uploadPort;
    }

    public void setUploadPort(String uploadPort) {
        this.uploadPort = uploadPort;
    }

    public Integer getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(Integer connectTime) {
        this.connectTime = connectTime;
    }

    public String getUploadPath() {
        return uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public String getUploadNm() {
        return uploadNm;
    }

    public void setUploadNm(String uploadNm) {
        this.uploadNm = uploadNm;
    }

    public String getUploadPwd() {
        return uploadPwd;
    }

    public void setUploadPwd(String uploadPwd) {
        this.uploadPwd = uploadPwd;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}