package com.hisun.lemon.cpo.service.impl;

import com.hisun.lemon.common.extension.ExtensionLoader;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.ReflectionUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpo.bo.BaseAccWithdrawBO;
import com.hisun.lemon.cpo.common.CpoConstants;
import com.hisun.lemon.cpo.dao.IAccErrorDao;
import com.hisun.lemon.cpo.dao.IWithdrawSuborderDao;
import com.hisun.lemon.cpo.entity.AccCfgDO;
import com.hisun.lemon.cpo.entity.AccControlDO;
import com.hisun.lemon.cpo.entity.AccErrorDO;
import com.hisun.lemon.cpo.entity.WithdrawSuborderDO;
import com.hisun.lemon.cpo.service.ICheckAccWithdrawService;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 工行提现对账服务
 */
@Service
public class CheckAccWithdrawServiceImpl extends BaseService implements ICheckAccWithdrawService {
    private static final Logger logger = LoggerFactory.getLogger(CheckAccWithdrawServiceImpl.class);
    
    @Resource
    private IWithdrawSuborderDao withdrawSuborderDao;
    
    @Resource
    private IAccErrorDao accErrorDao;

    /**
     * 根据银行成功的对账明细，和我方订单进行对账
     * @param accControlDO 对账批次信息
     * @param accCfgDO 对账配置信息
     */
    @Override
    @SuppressWarnings("unchecked")
    public String checkAccountWithdraw(AccControlDO accControlDO, AccCfgDO accCfgDO) throws Exception {
        //step1: 对账批次信息
        String chkFilSts = accControlDO.getChkFilSts();
        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusTyp = accControlDO.getCorpBusTyp();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        logger.debug("CheckAccWithdrawServiceImpl.checkAccountWithdraw()开始对账，机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo);

        //step2: 分页查询银行对账明细，查询结果进行排序后，每次取最大记录为queryNum
        int i = 0;//第i次查询银行明细
        int beginNum = 0;//从第多少条开始取记录
        int countNum = 0;//每次取多少条记录
        int queryNum = accCfgDO.getQueryNum();//每次查询大最大笔数

        List<Object> checkDOList = null;//银行API分页查询返回的明细 List
        String selectDetailMethod = accCfgDO.getSelectDetailMethod();//查询对账明细需执行的方法
        String chkClazzName = accCfgDO.getChkClazz();//银行API类名
        String txSts = accCfgDO.getSuccessFlag();//银行对账明细为交易成功对应的值

        //反射得到银行API类对账实例和执行方法
        Class chkClazz = Class.forName(chkClazzName);
        Method method = chkClazz.getMethod(selectDetailMethod, String.class, String.class, Integer.class, Integer.class);

        //将首字母转成小写，获取银行API类的bean
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(Character.toLowerCase(chkClazz.getSimpleName().charAt(0)));
        stringBuilder.append(chkClazz.getSimpleName().substring(1));
        String beanName = stringBuilder.toString();

        //获取到Spring容器中的对象
        Object instance = ExtensionLoader.getSpringBean(beanName, chkClazz);

        //step3: 分页查询，进行对账
        BaseAccWithdrawBO baseAccWithdrawBO = new BaseAccWithdrawBO();
        while (true) {
            beginNum = queryNum * i;
            countNum = queryNum;

            //通过反射，调用查询银行对账明细的方法
            checkDOList = (List<Object>)method.invoke(instance, chkBatNo, txSts, beginNum, countNum);

            //银行对账明细不为空，开始对账
            if (CollectionUtils.isNotEmpty(checkDOList)) {
                checkAccount(checkDOList, baseAccWithdrawBO, accControlDO, accCfgDO);
            } else {
                //查无对账明细数据，退出循环
                break;
            }
            i++;
        }

        //step4: 将我方ORD_STS='S'、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'(我方有机构无)，并录入差错
        updateChkSts5To2(baseAccWithdrawBO, accControlDO);

        //step5: 将我方ORD_STS='S'、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)
        updateChkSts0To5(accControlDO);

        //step6: 录入差错表
        insertChkAccError(baseAccWithdrawBO, accControlDO, accCfgDO);

        //step7:对账完成，更新对账批次状态
        chkFilSts = CpoConstants.CHK_FIL_FINISHED;
        return chkFilSts;
    }


    /**
     * 分页查询后，进行明细对账
     * @param checkDOList 分页查询获取对账明细 List
     * @param baseAccWithdrawBO 对账主控服务差异记录扩展业务对象
     * @param accControlDO 对账批次信息
     * @param accCfgDO 对账配置信息
     * @throws Exception 抛出异常
     */
    @SuppressWarnings("unchecked")
    private void checkAccount(List<Object> checkDOList, BaseAccWithdrawBO baseAccWithdrawBO, AccControlDO accControlDO, AccCfgDO accCfgDO)  throws Exception {
        //step1：初始化参数
        String chkKey = null;//银行明细中的公司方交易流水号
        String txSts = null;//银行交易状态
        BigDecimal checkAmt = null;//银行订单金额
        BigDecimal totMchAmt = new BigDecimal(0);//对平总金额
        Integer totMchCnt = 0;//对平总笔数

        LocalDate chkDt = accControlDO.getChkDt();//对账日期
        LocalTime chkTm = DateTimeUtils.getCurrentLocalTime();//对账时间
        List<Object> orgExistList = new ArrayList<>();//机构有我方无的差异记录，保存银行对账明细
        List<Object> amtErrorList = new ArrayList<>();//金额不相等的差异记录，保存银行对账明细

        WithdrawSuborderDO myWithdrawSuborderDO = null;//我方提现子订单信息
        WithdrawSuborderDO updateWithdrawSuborderDO = null;//用于更新我方提现子订单信息

        //获取更新的方法
        String chkClazz = accCfgDO.getChkClazz();
        String updateMethod = accCfgDO.getUpdateMethod();
        Class bankApi = Class.forName(chkClazz);
        Method method = bankApi.getMethod(updateMethod, String.class, String.class, String.class);

        //将首字母转成小写
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(Character.toLowerCase(bankApi.getSimpleName().charAt(0)));
        stringBuilder.append(bankApi.getSimpleName().substring(1));
        String beanName = stringBuilder.toString();

        //获取Spring容器中的对象bean
        Object instance = ExtensionLoader.getSpringBean(beanName, bankApi);

        //取出每条银行对账明细，根据订单号，查询我方订单信息
        for (Object orgCheckDO : checkDOList) {
            //1.获取银行对账明细类DO
            Class clazz = orgCheckDO.getClass();

            //获取银行明细中的机构流水号
            Field checkKey = clazz.getDeclaredField(accCfgDO.getCheckKeyFiled());
            chkKey = ReflectionUtils.getField(checkKey,orgCheckDO).toString();

            //获取银行明细的交易状态
            Field txStsField = clazz.getDeclaredField(accCfgDO.getTxStsFiled());
            txSts = ReflectionUtils.getField(txStsField,orgCheckDO).toString();

            //获取银行明细的交易金额，以元为单位
            Field checkAmtField = clazz.getDeclaredField(accCfgDO.getCheckAmtFiled());
            checkAmt = new BigDecimal(ReflectionUtils.getField(checkAmtField,orgCheckDO).toString());

            //2.根据订单号查询我方的提现订单信息
            myWithdrawSuborderDO = withdrawSuborderDao.getWithdrawSuborderByChkKey(chkKey);

            //3.若我方没有该订单信息，则存入【机构有我方无】差异记录
            if (JudgeUtils.isNull(myWithdrawSuborderDO)) {
                orgExistList.add(orgCheckDO);

                //更新银行对账明细的对账状态
                method.invoke(instance, chkKey, txSts, CpoConstants.CHK_STS_ORG_EXIST);
            } else if (checkAmt.compareTo(myWithdrawSuborderDO.getWcAplAmt()) != 0) {
                //4.若金额不相等，则存入【金额不相等】差异记录
                amtErrorList.add(orgCheckDO);

                //更新快捷订单对账状态
                updateWithdrawSuborderDO = new WithdrawSuborderDO();
                updateWithdrawSuborderDO.setChkKey(chkKey);
                updateWithdrawSuborderDO.setChkDt(chkDt);
                updateWithdrawSuborderDO.setChkTm(chkTm);
                updateWithdrawSuborderDO.setChkSts(CpoConstants.CHK_STS_AMT_ERROR);
                withdrawSuborderDao.updateWithdrawSuborderByChkKey(updateWithdrawSuborderDO);

                //更新银行对账明细的对账状态
                method.invoke(instance, chkKey, txSts, CpoConstants.CHK_STS_AMT_ERROR);
            } else if (StringUtils.equals(txSts, accCfgDO.getSuccessFlag()) && !StringUtils.equals(myWithdrawSuborderDO.getOrdSts(), CpoConstants.ORD_SUCCESS)) {
                //5.若金额相等，我方不成功，银行成功，则存入【机构有我方无】差异记录
                orgExistList.add(orgCheckDO);

                //更新子订单对账状态
                updateWithdrawSuborderDO = new WithdrawSuborderDO();
                updateWithdrawSuborderDO.setChkKey(chkKey);
                updateWithdrawSuborderDO.setChkDt(chkDt);
                updateWithdrawSuborderDO.setChkTm(chkTm);
                updateWithdrawSuborderDO.setChkSts(CpoConstants.CHK_STS_ORG_EXIST);
                withdrawSuborderDao.updateWithdrawSuborderByChkKey(updateWithdrawSuborderDO);

                //更新银行对账明细的对账状态
                method.invoke(instance, chkKey, txSts, CpoConstants.CHK_STS_ORG_EXIST);
            } else if (StringUtils.equals(txSts, accCfgDO.getSuccessFlag()) && StringUtils.equals(myWithdrawSuborderDO.getOrdSts(), CpoConstants.ORD_SUCCESS)) {
                //6.对账成功
                //更新子订单对账状态
                updateWithdrawSuborderDO = new WithdrawSuborderDO();
                updateWithdrawSuborderDO.setChkKey(chkKey);
                updateWithdrawSuborderDO.setChkDt(chkDt);
                updateWithdrawSuborderDO.setChkTm(chkTm);
                updateWithdrawSuborderDO.setChkSts(CpoConstants.CHK_STS_SUCCESS);
                withdrawSuborderDao.updateWithdrawSuborderByChkKey(updateWithdrawSuborderDO);

                //更新银行对账明细的对账状态
                method.invoke(instance, chkKey, txSts, CpoConstants.CHK_STS_SUCCESS);

                //对平金额和笔数累加
                totMchAmt = totMchAmt.add(checkAmt);
                totMchCnt++;
            }
        }

        //更新累计结果
        accControlDO.setTotMchAmt(totMchAmt);
        accControlDO.setTotMchCnt(totMchCnt);

        //更新对账差错订单
        if(CollectionUtils.isNotEmpty(orgExistList)) {
            baseAccWithdrawBO.addAllOrgExistList(orgExistList);
        }
        if(CollectionUtils.isNotEmpty(amtErrorList)) {
            baseAccWithdrawBO.addAllAmtErrorList(amtErrorList);
        }
    }

    /**
     * 将我方ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'(我方有机构无)，并录入差错
     */
    private void updateChkSts5To2(BaseAccWithdrawBO baseAccWithdrawBO, AccControlDO accControlDO) {
        //订单状态为成功的对应值
        String ordSts = CpoConstants.ORD_SUCCESS;
        String oldChkSts = CpoConstants.CHK_STS_DOUBT;
        String newChkSts = CpoConstants.CHK_STS_PLAT_EXIST;

        //对账批次信息
        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusTyp = accControlDO.getCorpBusTyp();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        LocalDate chkDt = accControlDO.getChkDt();
        LocalTime chkTm = DateTimeUtils.getCurrentLocalTime();
        logger.debug("CheckAccWithdrawServiceImpl.updateChkSts5To2() 机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 存疑订单(5)更新为(2)我方有机构无");

        //累计存疑转差错总金额和笔数
        BigDecimal dbtErrAmt = new BigDecimal(0);
        Integer dbtErrCnt = 0;

        //登记我方有机构无的订单
        List<WithdrawSuborderDO> platExistList = new ArrayList<>();

        //1.查询我方ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单，(ORD_DT)订单日期小于对账日期
        List<WithdrawSuborderDO> withdrawSuborderDOList = withdrawSuborderDao.getWithdrawSuborderListByChkStsDoubt(ordSts, oldChkSts, chkDt);

        //2.判断我方存疑订单集合是否为空
        if (CollectionUtils.isNotEmpty(withdrawSuborderDOList)) {
            for (WithdrawSuborderDO withdrawSuborderDO : withdrawSuborderDOList) {
                //3.录入差错
                withdrawSuborderDO.setChkSts(CpoConstants.CHK_STS_PLAT_EXIST);
                platExistList.add(withdrawSuborderDO);

                //存疑转差错金额和笔数累加
                dbtErrAmt = dbtErrAmt.add(withdrawSuborderDO.getWcAplAmt());
                dbtErrCnt++;
            }

            //4.将ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'，(ORD_DT)订单日期小于对账日期
            withdrawSuborderDao.updateWithdrawSuborderByChkStsDoubt(ordSts, oldChkSts, newChkSts, chkDt, chkTm);

            //5.更新对账差错订单 和 累计结果
            accControlDO.setDbtErrAmt(dbtErrAmt);
            accControlDO.setDbtErrCnt(dbtErrCnt);
            if (CollectionUtils.isNotEmpty(platExistList)) {
                baseAccWithdrawBO.addAllPlatExistList(platExistList);
            }
        }
    }

    /**
     * 将我方ORD_STS='S'、CHK_STS='0'(未对账)的订单对账状态更新为CHK_STS='5'(存疑)
     */
    private void updateChkSts0To5(AccControlDO accControlDO) {
        //订单状态为成功的对应值
        String ordSts = CpoConstants.ORD_SUCCESS;
        String oldChkSts = CpoConstants.CHK_STS_NOT_START;
        String newChkSts = CpoConstants.CHK_STS_DOUBT;

        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();

        LocalDate chkDt = accControlDO.getChkDt();
        LocalTime chkTm = DateTimeUtils.getCurrentLocalTime();
        String chkDtStr = DateTimeUtils.formatLocalDate(accControlDO.getChkDt());
        String chkTmStr = DateTimeUtils.getCurrentTimeStr();
        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 未对账订单(0)更新为(5)存疑");

        //累计存疑订单金额和笔数
        BigDecimal doubtAmt = new BigDecimal(0);
        Integer doubtCnt = 0;

        //1.查询我方ORD_STS='S'、CHK_STS='0'(未对账)的订单，订单日期等于对账日期
        List<WithdrawSuborderDO> withdrawSuborderDOList = withdrawSuborderDao.getWithdrawSuborderListByChkStsNotstart(ordSts, oldChkSts, chkDt);

        //2.判断我方未对账订单集合是否为空
        if (CollectionUtils.isNotEmpty(withdrawSuborderDOList)) {
            for (WithdrawSuborderDO withdrawSuborderDO : withdrawSuborderDOList) {
                //存疑金额和笔数累加
                doubtAmt = doubtAmt.add(withdrawSuborderDO.getWcAplAmt());
                doubtCnt++;
            }

            //3.将ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)
            withdrawSuborderDao.updateWithdrawSuborderListByChkStsNotstart(ordSts, oldChkSts, newChkSts, chkDt, chkTm);

            //4.更新存疑总金额和笔数
            accControlDO.setDoubtAmt(doubtAmt);
            accControlDO.setDoubtCnt(doubtCnt);
        }
    }

    /**
     * 录入对账差错信息表，并进行差错账务处理
     */
    private void insertChkAccError(BaseAccWithdrawBO baseAccWithdrawBO, AccControlDO accControlDO, AccCfgDO accCfgDO) throws Exception {
        //1.获取所有的对账差错数据
        List<Object> orgExistList = baseAccWithdrawBO.getOrgExistList();//机构有我方无的差异记录，保存银行对账明细
        List<Object> amtErrorList = baseAccWithdrawBO.getAmtErrorList();//金额不相等的差异记录，保存银行对账明细
        List<WithdrawSuborderDO> platExistList = baseAccWithdrawBO.getPlatExistList();//我方有机构无的差异记录，保存我方订单信息

        //对账批次信息
        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        LocalDate chkDt = accControlDO.getChkDt();
        LocalTime chkTm = DateTimeUtils.getCurrentLocalTime();
        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 开始录入对账差错记录");

        //累计金额和笔数
        BigDecimal shortAmt = new BigDecimal(0);//短款总金额
        Integer shortCnt = 0;//短款总笔数
        BigDecimal longAmt = new BigDecimal(0);//长款总金额
        Integer longCnt = 0;//长款总笔数
        BigDecimal errTotAmt = new BigDecimal(0);//金额错误类型总金额
        Integer errTotCnt = 0;//金额错误类型总笔数
        BigDecimal txAmt = BigDecimal.ZERO;//交易金额

        //2.录入差异记录(我方有机构无)，T-1日对账已更新状态为存疑，T日将存疑的订单更新为我方有机构无
        if (CollectionUtils.isNotEmpty(platExistList)) {
            AccErrorDO accErrorDO = null;//差异信息对象，录入差错使用
            for (WithdrawSuborderDO withdrawSuborderDO : platExistList) {
                accErrorDO = new AccErrorDO();
                accErrorDO.setChkErId(IdGenUtils.generateIdWithDateTime("CHK", 6));
                accErrorDO.setRutCorg(withdrawSuborderDO.getRutCorg());
                accErrorDO.setCorpBusTyp(withdrawSuborderDO.getCorpBusTyp());
                accErrorDO.setCorpBusSubTyp(withdrawSuborderDO.getCorpBusSubTyp());
                accErrorDO.setErrKeyId(withdrawSuborderDO.getChkKey());
                accErrorDO.setChkErrDt(chkDt);
                accErrorDO.setChkErrTm(chkTm);
                accErrorDO.setChkBatNo(chkBatNo);
                accErrorDO.setMyTxAmt(withdrawSuborderDO.getWcAplAmt());
                accErrorDO.setOldJrnNo(withdrawSuborderDO.getSubOrdNo());
                accErrorDO.setOldOrdNo(withdrawSuborderDO.getWcOrdNo());
                accErrorDO.setOldCorgKey(withdrawSuborderDO.getChkKey());
                accErrorDO.setOldTxDt(withdrawSuborderDO.getOrdDt());
                accErrorDO.setSplAbleFlg("N");
                accErrorDO.setCanAbleFlg("N");

                //业务类型为 06-提现，对账状态为 2-我方有银行无，则差错类型为 3-长款差错
                txAmt = accErrorDO.getMyTxAmt();
                longAmt = longAmt.add(txAmt);
                longCnt++;

                //插入差错信息表
                accErrorDO.setChkErrTyp(CpoConstants.CHK_ERR_TYP_LONG);
                accErrorDao.insert(accErrorDO);
            }
        }

        //3.添加差异记录(机构有我方无)
        if (CollectionUtils.isNotEmpty(orgExistList)) {
            for (Object baseCheckAccDO : orgExistList) {
                //获取银行对账明细表对应的DO类型
                Class clazz = baseCheckAccDO.getClass();

                //获取银行对账明细的对账外键
                Field checkKeyField = clazz.getDeclaredField(accCfgDO.getCheckKeyFiled());
                String checkKey = ReflectionUtils.getField(checkKeyField,baseCheckAccDO).toString();

                //获取银行对账明细的交易金额
                Field checkAmtField = clazz.getDeclaredField(accCfgDO.getCheckAmtFiled());
                BigDecimal checkAmt = new BigDecimal(ReflectionUtils.getField(checkAmtField,baseCheckAccDO).toString());

                //获取银行对账明细表对应的DO类的所有属性域
                AccErrorDO accErrorDO = new AccErrorDO();
                Field[] declaredFields = clazz.getDeclaredFields();
                for(Field field : declaredFields){
                    if (field.getName().equals("corpBusTyp")) {
                        Object tmpCorpBustyp = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpCorpBustyp)){
                            accErrorDO.setCorpBusTyp(tmpCorpBustyp.toString());
                        }
                    } else if (field.getName().equals("corpBusSubTyp")) {
                        Object tmpCorpBusSubtyp = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpCorpBusSubtyp)){
                            accErrorDO.setCorpBusSubTyp(tmpCorpBusSubtyp.toString());
                        }
                    } else if (field.getName().equals("rutCorg")) {
                        Object tmpRutCorg = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpRutCorg)){
                            accErrorDO.setRutCorg(tmpRutCorg.toString());
                        }
                    }
                }
                accErrorDO.setChkErId(IdGenUtils.generateIdWithDateTime("CHK", 6));
                accErrorDO.setErrKeyId(checkKey);
                accErrorDO.setChkErrDt(chkDt);
                accErrorDO.setChkErrTm(chkTm);
                accErrorDO.setChkBatNo(chkBatNo);
                accErrorDO.setOthTxAmt(checkAmt);
                accErrorDO.setSplAbleFlg("N");
                accErrorDO.setCanAbleFlg("N");

                //根据对账外键，查询我方提现子订单信息，若查询无结果，则原交易流失、原订单号均设置为空
                WithdrawSuborderDO withdrawSuborderDO = withdrawSuborderDao.getWithdrawSuborderByChkKey(checkKey);
                if (JudgeUtils.isNull(withdrawSuborderDO)) {
                    accErrorDO.setOldJrnNo("");
                    accErrorDO.setOldOrdNo("");
                    accErrorDO.setOldCorgKey(checkKey);
                } else {
                    accErrorDO.setOldJrnNo(withdrawSuborderDO.getSubOrdNo());
                    accErrorDO.setOldOrdNo(withdrawSuborderDO.getWcOrdNo());
                    accErrorDO.setOldCorgKey(checkKey);
                    accErrorDO.setOldTxDt(withdrawSuborderDO.getOrdDt());
                }

                //业务类型为 06-提现，对账状态为 3-银行有我方无，差错类型为 2-短款差错
                txAmt = accErrorDO.getOthTxAmt();
                shortAmt = shortAmt.add(txAmt);
                shortCnt++;

                //插入差错信息表
                accErrorDO.setChkErrTyp(CpoConstants.CHK_ERR_TYP_SHORT);
                accErrorDao.insert(accErrorDO);
            }
        }

        //4.添加差异记录(金额不相等)
        if (CollectionUtils.isNotEmpty(amtErrorList)) {
            for (Object baseCheckAccDO : amtErrorList) {
                //获取银行对账明细表对应的DO类型
                Class clazz = baseCheckAccDO.getClass();

                //获取银行对账明细的对账外键
                Field checkKeyField = clazz.getDeclaredField(accCfgDO.getCheckKeyFiled());
                String checkKey = ReflectionUtils.getField(checkKeyField,baseCheckAccDO).toString();

                //获取银行对账明细的交易金额
                Field checkAmtField = clazz.getDeclaredField(accCfgDO.getCheckAmtFiled());
                BigDecimal checkAmt = new BigDecimal(ReflectionUtils.getField(checkAmtField,baseCheckAccDO).toString());

                //获取银行对账明细表对应的DO类的所有属性域
                AccErrorDO accErrorDO = new AccErrorDO();
                Field[] declaredFields = clazz.getDeclaredFields();
                for(Field field : declaredFields){
                    if (field.getName().equals("corpBusTyp")) {
                        Object tmpCorpBustyp = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpCorpBustyp)){
                            accErrorDO.setCorpBusTyp(tmpCorpBustyp.toString());
                        }
                    } else if (field.getName().equals("corpBusSubTyp")) {
                        Object tmpCorpBusSubtyp = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpCorpBusSubtyp)){
                            accErrorDO.setCorpBusSubTyp(tmpCorpBusSubtyp.toString());
                        }
                    } else if (field.getName().equals("rutCorg")) {
                        Object tmpRutCorg = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpRutCorg)){
                            accErrorDO.setRutCorg(tmpRutCorg.toString());
                        }
                    }
                }
                accErrorDO.setChkErId(IdGenUtils.generateIdWithDateTime("CHK", 6));
                accErrorDO.setErrKeyId(checkKey);
                accErrorDO.setChkErrDt(chkDt);
                accErrorDO.setChkErrTm(chkTm);
                accErrorDO.setChkBatNo(chkBatNo);
                accErrorDO.setOthTxAmt(checkAmt);
                accErrorDO.setSplAbleFlg("N");
                accErrorDO.setCanAbleFlg("N");

                //根据对账外键，查询我方提现子订单信息，若查询无结果，则原交易流失、原订单号均设置为空
                WithdrawSuborderDO withdrawSuborderDO = withdrawSuborderDao.getWithdrawSuborderByChkKey(checkKey);
                if (JudgeUtils.isNull(withdrawSuborderDO)) {
                    accErrorDO.setOldJrnNo("");
                    accErrorDO.setOldOrdNo("");
                    accErrorDO.setOldCorgKey(checkKey);
                } else {
                    accErrorDO.setOldJrnNo(withdrawSuborderDO.getSubOrdNo());
                    accErrorDO.setOldOrdNo(withdrawSuborderDO.getWcOrdNo());
                    accErrorDO.setOldCorgKey(checkKey);
                    accErrorDO.setMyTxAmt(withdrawSuborderDO.getWcAplAmt());
                    accErrorDO.setOldTxDt(withdrawSuborderDO.getOrdDt());
                }

                //业务类型为 06-提现，对账状态为 4-金额不符，差错类型为 4-金额错误
                txAmt = accErrorDO.getOthTxAmt();
                errTotAmt = errTotAmt.add(txAmt);
                errTotCnt++;

                //插入差错信息表
                accErrorDO.setChkErrTyp(CpoConstants.CHK_ERR_TYP_AMTERR);
                accErrorDao.insert(accErrorDO);
            }
        }

        //更新差错金额和笔数
        accControlDO.setShortAmt(shortAmt);
        accControlDO.setShortCnt(shortCnt);
        accControlDO.setLongAmt(longAmt);
        accControlDO.setLongCnt(longCnt);
        accControlDO.setErrTotAmt(errTotAmt);
        accControlDO.setErrTotCnt(errTotCnt);
    }
}
