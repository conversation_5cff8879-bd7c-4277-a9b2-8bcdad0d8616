/*
 * @ClassName WithdrawParamDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 19:25:57
 */
package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

/**
 * 资金流出业务参数表
 */
public class WithdrawParamDO extends BaseDO {
    /**
     * @Fields busParId 主键
     */
    private String busParId;
    /**
     * @Fields copBusTyp 付款类型
     */
    private String corpBusTyp;
    /**
     * @Fields copBusSubTyp 付款子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields capTyp 资金种类，1现金
     */
    private String capTyp;
    /**
     * @Fields psnCrpFlg 个人/商户标识，C个人，B商户
     */
    private String psnCrpFlg;
    /**
     * @Fields holdFlg 冻结标识，0不冻结，1冻结
     */
    private String holdFlg;
    /**
     * @Fields holdDesc 冻结描述
     */
    private String holdDesc;
    /**
     * @Fields autoPayFlg 自动付款标识，0自动，1手工
     */
    private String autoPayFlg;
    /**
     * @Fields rskChkFlg 风控检查标识，0不检查，1检查
     */
    private String rskChkFlg;
    /**
     * @Fields wdcAutoFlg 实时付款标识，0实时，1，非实时
     */
    private String wdcAutoFlg;
    /**
     * @Fields rrcCmlFlg 风控累积标识，0不累积，1累积
     */
    private String rrcCmlFlg;
    /**
     * @Fields paytmChkFlg 付款时间检查标识，0不检查，1检查
     */
    private String paytmChkFlg;
    /**
     * @Fields chkUsrFlg 用户状态检查标识，0不检查，1检查
     */
    private String chkUsrFlg;
    /**
     * @Fields pauseFlg 付款失败暂停标识，0不暂停，1暂停
     */
    private String pauseFlg;
    /**
     * @Fields smsFlg 短信下发标识，0不下发，1下发
     */
    private String smsFlg;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getBusParId() {
        return busParId;
    }

    public void setBusParId(String busParId) {
        this.busParId = busParId;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getPsnCrpFlg() {
        return psnCrpFlg;
    }

    public void setPsnCrpFlg(String psnCrpFlg) {
        this.psnCrpFlg = psnCrpFlg;
    }

    public String getHoldFlg() {
        return holdFlg;
    }

    public void setHoldFlg(String holdFlg) {
        this.holdFlg = holdFlg;
    }

    public String getHoldDesc() {
        return holdDesc;
    }

    public void setHoldDesc(String holdDesc) {
        this.holdDesc = holdDesc;
    }

    public String getAutoPayFlg() {
        return autoPayFlg;
    }

    public void setAutoPayFlg(String autoPayFlg) {
        this.autoPayFlg = autoPayFlg;
    }

    public String getRskChkFlg() {
        return rskChkFlg;
    }

    public void setRskChkFlg(String rskChkFlg) {
        this.rskChkFlg = rskChkFlg;
    }

    public String getWdcAutoFlg() {
        return wdcAutoFlg;
    }

    public void setWdcAutoFlg(String wdcAutoFlg) {
        this.wdcAutoFlg = wdcAutoFlg;
    }

    public String getRrcCmlFlg() {
        return rrcCmlFlg;
    }

    public void setRrcCmlFlg(String rrcCmlFlg) {
        this.rrcCmlFlg = rrcCmlFlg;
    }

    public String getPaytmChkFlg() {
        return paytmChkFlg;
    }

    public void setPaytmChkFlg(String paytmChkFlg) {
        this.paytmChkFlg = paytmChkFlg;
    }

    public String getChkUsrFlg() {
        return chkUsrFlg;
    }

    public void setChkUsrFlg(String chkUsrFlg) {
        this.chkUsrFlg = chkUsrFlg;
    }

    public String getPauseFlg() {
        return pauseFlg;
    }

    public void setPauseFlg(String pauseFlg) {
        this.pauseFlg = pauseFlg;
    }

    public String getSmsFlg() {
        return smsFlg;
    }

    public void setSmsFlg(String smsFlg) {
        this.smsFlg = smsFlg;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}