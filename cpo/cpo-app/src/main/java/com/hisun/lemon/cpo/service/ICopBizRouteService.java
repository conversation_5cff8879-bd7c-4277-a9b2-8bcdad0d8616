package com.hisun.lemon.cpo.service;

import java.util.List;

import com.hisun.lemon.cpo.entity.CopBizRouteDO;

/**
 * 资金流出业务合作路由service
 * <AUTHOR>
 *
 */
public interface ICopBizRouteService {

	/**
	 * 新增业务合作路由
	 * @param copBizRouteDO
	 * @return boolean
	 */
	boolean add(CopBizRouteDO copBizRouteDO);
	
	/**
	 * 更新路由信息
	 * @param copBizRouteDO
	 * @return boolean
	 */
	boolean update(CopBizRouteDO copBizRouteDO);
	
	/**
	 * 更新路由信息
	 * @param key
	 * @param copBizRouteDO
	 * @return boolean
	 */
	boolean updateByKey(String key,CopBizRouteDO copBizRouteDO);
	
	/**
	 * 根据主键获取路由
	 * @param rutInfId
	 * @return CopBizRouteDO
	 */
	CopBizRouteDO selectByKey(String rutInfId);
	
	/**
	 * 获取路由信息
	 * @param copBizRouteDO
	 * @return CopBizRouteDO
	 */
	CopBizRouteDO selectByDO(CopBizRouteDO copBizRouteDO);
	
	/**
	 * 获取路由列表
	 * @param copBizRouteDO
	 * @return list<CopBizRouteDO>
	 */
	List<CopBizRouteDO> listByDO(CopBizRouteDO copBizRouteDO);
	
	/**
	 * 根据主键删除路由
	 * @param rutInfId
	 * @return boolean
	 */
	boolean deleteByKey(String rutInfId);
	
	/**
	 * 根据对象信息删除路由
	 * @param copBizRouteDO
	 * @return boolean
	 */
	boolean deleteByDO(CopBizRouteDO copBizRouteDO);
	
	/**
	 * 获取路由状态
	 * @param copBizRouteDO
	 * @return String
	 */
	String selectRutEffFlg(CopBizRouteDO copBizRouteDO);
	
	/**
	 * 查询合作机构业务路由信息
	 * @param copBizRouteDO
	 * @return CopBizRouteDO
	 */
	CopBizRouteDO selectAgcyRoute(CopBizRouteDO copBizRouteDO);
}
