package com.hisun.lemon.cpo.dao;

import com.hisun.lemon.cpo.entity.CopBizRouteDO;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资金流出业务合作路由dao
 * 
 * <AUTHOR>
 *
 */
@Mapper
public interface ICopBizRouteDao extends BaseDao<CopBizRouteDO> {

	/**
	 * 根据机构和业务类型，查询路由详细信息
	 */
	CopBizRouteDO getByRutCorgAndBusTyp(CopBizRouteDO copBizRouteDO);

	/**
	 * 根据机构信息表、机构业务信息表、机构路由信息表，查询合作机构业务路由信息
	 */
	CopBizRouteDO getAgcyRoute(CopBizRouteDO copBizRouteDO);

	/**
	 * 根据机构和业务类型，查询路由详细信息列表
	 */
	List<CopBizRouteDO> getAgcyRouteList(CopBizRouteDO copBizRouteDO);

	/**
	 * 根据机构信息表、机构业务信息表、机构路由信息表，查询合作机构业务路由状态
	 */
	String getRutEffFlg(CopBizRouteDO copBizRouteDO);

	/**
	 * 根据机构和业务类型，更新路由信息
	 */
	int updateByRutCorgAndBusTyp(CopBizRouteDO copBizRouteDO);

	/**
	 * 根据机构和业务类型，删除路由信息
	 */
	int deleteByRutCorgAndBusTyp(CopBizRouteDO copBizRouteDO);

	/**
	 * 根据机构业务信息表、机构路由信息表，查询某种业务类型的路由列表
	 */
	List<CopBizRouteDO> getRouteList(@Param("corpBusTyp")CorpBusTyp corpBusTyp, @Param("corpBusSubTyp")String corpBusSubTyp, @Param("crdAcTyp")String crdAcTyp);

	/**
	 * 根据机构业务信息表、机构路由信息、金额，查询匹配的路由信息，默认只取一条
	 */
	CopBizRouteDO getRouteInfo(@Param("routeDO") CopBizRouteDO routeDO, @Param("ordAmt")BigDecimal ordAmt);

}
