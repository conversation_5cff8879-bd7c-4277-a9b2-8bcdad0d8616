package com.hisun.lemon.cpo.service;

import com.hisun.lemon.cpo.entity.AccControlDO;
import com.hisun.lemon.cpo.entity.AccParamDO;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;

import java.time.LocalDate;
import java.util.List;

/**
 * 资金模块对账主控 Service
 */
public interface ICheckAccControlService {

    /**
     * 增加对账参数
     */
    boolean addChkAccParam(AccParamDO chkAccParamDO);

    /**
     * 查询生效的对账参数
     */
    List<AccParamDO> queryEffAccParamList();

    /**
     * 查询对账批次是否已存在
     */
    AccControlDO queryUnfinishedAccControl(AccParamDO accParamDO);

    /**
     * 根据对账参数信息，生成对账批次信息，插入到对账主控表中
     */
    void registerChkBatNo(LocalDate checkDt);

    /**
     * 开始对账
     */
    void beginCheckAcc(AccControlDO chkAccControlDO);

    /**
     * 写入对账服务
     */
    void writeCheckFile(LocalDate chkFilDt, String rutCorg, CorpBusSubTyp corpBusSubTyp, String ordSts);
}
