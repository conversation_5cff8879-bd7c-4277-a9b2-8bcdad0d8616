package com.hisun.lemon.cpo.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.DmAccountDetailRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.client.CregisClient;
import com.hisun.lemon.cpi.dto.CregisRsp;
import com.hisun.lemon.cpi.dto.PaymentPrepareReqDTO;
import com.hisun.lemon.cpi.dto.PaymentReqDTO;
import com.hisun.lemon.cpo.common.CpoConstants;
import com.hisun.lemon.cpo.common.CpoMsgCd;
import com.hisun.lemon.cpo.dao.*;
import com.hisun.lemon.cpo.dto.*;
import com.hisun.lemon.cpo.entity.*;
import com.hisun.lemon.cpo.enums.BankAcItemEnum;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import com.hisun.lemon.cpo.mq.WithdrawNotifyProduce;
import com.hisun.lemon.cpo.service.IWithdrawService;
import com.hisun.lemon.cpo.utils.AccountUtils;
import com.hisun.lemon.cpo.utils.EncryptUtils;
import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import com.hisun.lemon.tam.constants.CcyAcItemEnum;
import com.hisun.lemon.tam.constants.TamConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 资金流出业务Service实现
 */
@Transactional
@Service("withdrawService")
public class WithdrawServiceImpl extends BaseService implements IWithdrawService {

    @Resource
    private IWithdrawOrderDao withdrawOrderDao;

    @Resource
    private IPwmWithdrawOrderDao pwmWithdrawOrderDao;

    @Resource
    private IWithdrawSuborderDao withdrawSuborderDao;

    @Resource
    private ICopBizRouteDao copBizRouteDao;

    @Resource
    private IWithdrawParamDao withdrawParamDao;

    @Resource
    private AccountingTreatmentClient accountingTreatmentClient;

    @Resource
    private RiskCheckClient riskCheckClient;

    @Resource
    private WithdrawNotifyProduce withdrawNotifyProduce;

    @Resource
    private EncryptUtils encryptUtils;

    @Resource
    private CregisClient cregisClient;

    @Value("${Cregis.fundFlowCode}")
    private String fundFlowCode;

    @Resource
    private AccountManagementClient acmManageClient;

    private static final Logger logger = LoggerFactory.getLogger(WithdrawServiceImpl.class);

    /**
     * 创建资金流出订单
     */
    @Override
    public GenericRspDTO<WithdrawResDTO> createOrder(GenericDTO<WithdrawReqDTO> genericDTO) {
        logger.info("==================开始创建资金流出订单==================");

        //请求对象
        WithdrawReqDTO withdrawReqDTO = genericDTO.getBody();
        // 创建DO对象
        WithdrawOrderDO withdrawOrderDO = new WithdrawOrderDO();
        // 创建申请提现返回结果对象
        WithdrawResDTO withdrawResDTO = new WithdrawResDTO();
        //userId
        String userId = LemonUtils.getUserId();

        // 将DTO对象的数据复制到DO对象
        BeanUtils.copyProperties(withdrawOrderDO, withdrawReqDTO);
        withdrawOrderDO.setCorpBusTyp(withdrawReqDTO.getCorpBusTyp().getType());
        withdrawOrderDO.setCorpBusSubTyp(withdrawReqDTO.getCorpBusSubTyp().getType());

        // 返回提现订单号
        String wcOrdNo = IdGenUtils.generateIdWithDateTime("CPO", "CPO", 6);
        try {
            // step1: 提现金额检查
            BigDecimal wcAplAmt = withdrawOrderDO.getWcAplAmt();
            if (wcAplAmt.compareTo(BigDecimal.ZERO) < 0) {
                logger.error("==================金额不能小于零==================");
                throw new LemonException(CpoMsgCd.AMOUNT_IS_WRONG.getMsgCd());
            }

            // step2: 业务类型和业务子类型检查
            String corpBusTyp = withdrawOrderDO.getCorpBusTyp();
            String corpBusSubTyp = withdrawOrderDO.getCorpBusSubTyp();
            logger.info("==================业务类型 corpBusTyp：" + corpBusTyp + ", corpBusSubTyp：" + corpBusSubTyp +"订单号："+wcOrdNo);

            if(!StringUtils.equals(CorpBusTyp.WITHDRAW.getType(), corpBusTyp)) {
                logger.error("==================业务类型不正确==================");
                throw new LemonException(CpoMsgCd.CORP_BUS_TYP_WRONG.getMsgCd());
            }
            if (!StringUtils.equals(CorpBusSubTyp.PER_WITHDRAW.getType(), corpBusSubTyp) && !StringUtils.equals(CorpBusSubTyp.CARD_WITHDRAW.getType(), corpBusSubTyp)
                    && !StringUtils.equals(CorpBusSubTyp.MERC_WITHDRAW.getType(), corpBusSubTyp)) {
                logger.error("==================业务子类型不正确==================");
                throw new LemonException(CpoMsgCd.CORP_BUS_SUB_TYP_WRONG.getMsgCd());
            }

            // step3: 根据业务类型、业务子类型、资金类型，查询路由信息
            CopBizRouteDO copBizRouteDO = new CopBizRouteDO();
            copBizRouteDO.setCorpBusTyp(withdrawOrderDO.getCorpBusTyp());
            copBizRouteDO.setCorpBusSubTyp(withdrawOrderDO.getCorpBusSubTyp());
            copBizRouteDO.setCrdCorpOrg(withdrawOrderDO.getCapCorg());
            copBizRouteDO.setCrdAcTyp(withdrawOrderDO.getCrdAcTyp());
            copBizRouteDO = copBizRouteDao.getRouteInfo(copBizRouteDO, wcAplAmt);
            if (JudgeUtils.isNull(copBizRouteDO)) {
                logger.error("==================没有找到可用路由==================");
                throw new LemonException(CpoMsgCd.RUT_CORG_NOT_EXISTS.getMsgCd());
            }

            String rutCorg = copBizRouteDO.getRutCorpOrg();
            if(StringUtils.isBlank(rutCorg)) {
                logger.error("==================没有找到可用路由==================");
                throw new LemonException(CpoMsgCd.RUT_CORG_NOT_EXISTS.getMsgCd());
            } else {
                //获取路由信息
                withdrawOrderDO.setRutCorg(rutCorg);
            }

            //step4:银行卡检查
            RiskCheckUserStatusReqDTO userStatusReqDTO = new RiskCheckUserStatusReqDTO();
//            String crdNo = encryptUtils.encrypt(withdrawReqDTO.getCrdNoEnc(), CpoConstants.DECRYPT);
//            userStatusReqDTO.setId(crdNo);
            userStatusReqDTO.setId(withdrawReqDTO.getCrdNoEnc());
            //ID类型
            userStatusReqDTO.setIdTyp(Constants.ID_TYP_CARD);
            //交易类型
            userStatusReqDTO.setTxTyp(Constants.TX_TYP_WITHDRAW);
            GenericRspDTO<NoBody> riskRsp = riskCheckClient.checkUserStatus(userStatusReqDTO);
            if(JudgeUtils.isNotSuccess(riskRsp.getMsgCd())) {
                logger.error("==================用户状态检查不通过==================");
                throw new LemonException(riskRsp.getMsgCd());
            }

            //step5:风控检查
            if(JudgeUtils.isEmpty(userId)){
                userId = withdrawReqDTO.getUserNo();
            }

            JrnReqDTO jrnReqDTO = new JrnReqDTO();
            //收方ID
            jrnReqDTO.setPayUserId(userId);
            //收方类型
            jrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
            //交易类型
            jrnReqDTO.setTxTyp(Constants.TX_TYP_WITHDRAW);
            //交易状态
            jrnReqDTO.setTxSts(Constants.EFF_FLG_EFF);
            //交易渠道
            jrnReqDTO.setTxCnl("APP");
            //交易金额
            jrnReqDTO.setTxAmt(withdrawReqDTO.getWcAplAmt());
            //交易币种
            jrnReqDTO.setCcy(withdrawReqDTO.getCcy());
            //交易日期
            jrnReqDTO.setTxDate(LocalDate.now());
            //交易时间
            jrnReqDTO.setTxTime(LocalTime.now());
            //原交易流水号
            jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
            //原交易订单号
            jrnReqDTO.setTxOrdNo(wcOrdNo);
            //支付类型，支付方式，00-全部，01-账户余额，02-账户快捷，03-海币账户，04-微信支付，05-支付宝，06-翼支付
            jrnReqDTO.setPayTyp(Constants.PAY_TYP_ALL);
            riskRsp = riskCheckClient.riskControl(jrnReqDTO);
            if(JudgeUtils.isNotSuccess(riskRsp.getMsgCd())&&JudgeUtils.notEquals(riskRsp.getMsgCd(),"RSM30016")&&JudgeUtils.notEquals(riskRsp.getMsgCd(),"RSM30010")) {
                logger.error("==================风控检查不通过==================");
                throw new LemonException(riskRsp.getMsgCd());
            }

            // step6: 创建资金流出订单
            LocalDate currDate = DateTimeUtils.getCurrentLocalDate();
            LocalTime currTime = DateTimeUtils.getCurrentLocalTime();
            withdrawOrderDO.setUserId(userId);
            withdrawOrderDO.setWcOrdNo(wcOrdNo);
            withdrawOrderDO.setOrdDt(currDate);
            withdrawOrderDO.setOrdTm(currTime);
            withdrawOrderDO.setOrdSts(CpoConstants.ORD_WAITING_WITHDRAW);
            withdrawOrderDO.setWcWfSts(CpoConstants.ORD_APPROVAL_SUCCESS);
            withdrawOrderDao.insert(withdrawOrderDO);
            logger.info("==================创建资金流出订单，订单号: " + wcOrdNo);

            // step7: 创建资金流出子订单
            WithdrawSuborderDO withdrawSuborderDO = new WithdrawSuborderDO();
            BeanUtils.copyProperties(withdrawSuborderDO, withdrawOrderDO);
            String subOrdNo = IdGenUtils.generateIdWithDateTime("CPO", "CPO", 6);
            withdrawSuborderDO.setSubOrdNo(subOrdNo);
            withdrawSuborderDO.setChkKey(subOrdNo);
            withdrawSuborderDao.insert(withdrawSuborderDO);
            logger.info("==================创建资金流出子订单，子订单序号: " + subOrdNo);

            //接口返回结果
            withdrawResDTO.setOrdNo(wcOrdNo);
            return GenericRspDTO.newSuccessInstance(withdrawResDTO);
        } catch (LemonException e){
            logger.error("WithdrawServiceImpl.createOrder LemonException : ", e);
            return GenericRspDTO.newInstance(e.getMsgCd(),withdrawResDTO);
        } catch(Exception e) {
            logger.error("WithdrawServiceImpl.createOrder exception : ", e);
            return GenericRspDTO.newInstance(CpoMsgCd.CPO_SYS_EXCEPTION.getMsgCd(),withdrawResDTO);
        }
    }

    /**
     * 根据主键查询资金流出订单信息
     */
    @Override
    @Transactional(readOnly = true)
    public GenericRspDTO<WdcOrdDetailResDTO> queryOrder(String ordNo) {
        // 创建DTO对象
        WdcOrdDetailResDTO wdcOrdDetailResDTO = new WdcOrdDetailResDTO();

        // 根据订单号查询提现订单详细信息，包括处理状态等
        WithdrawOrderDO withdrawOrderDO = withdrawOrderDao.get(ordNo);

        // 判断查询结果是否为空
        if(JudgeUtils.isNotNull(withdrawOrderDO)) {
            BeanUtils.copyProperties(wdcOrdDetailResDTO, withdrawOrderDO);
            return GenericRspDTO.newSuccessInstance(wdcOrdDetailResDTO);
        } else {
            return GenericRspDTO.newInstance(CpoMsgCd.SELECT_IS_NULL.getMsgCd(),wdcOrdDetailResDTO);
        }
    }

    /**
     * 提现订单审批，修改审批工作流状态、审批拒绝原因、审批人
     * 根据主键更新资金流出订单信息，取不为null的字段值更新表记录
     */
    @Override
    public boolean approveWithdrawOrder(WithdrawOrderDO withdrawOrderDO) {
        try {
            // step1: 根据订单号查询提现订单审批状态
            String wcOrdNo = withdrawOrderDO.getWcOrdNo();
            String wcWfSts = withdrawOrderDao.getWcWfStsByKey(wcOrdNo);

            // 该订单审批状态不是 P1：待审批，返回订单审批状态异常
            if (!CpoConstants.ORD_APPROVAL_WAITING.equals(wcWfSts)) {
                logger.error("WithdrawServiceImpl.approveWithdrawOrder() 提现订单审批状态异常");
                return false;
//                throw new LemonException(CpoMsgCd.WC_WF_STS_IS_WRONG.getMsgCd(), CpoMsgCd.WC_WF_STS_IS_WRONG.getMsgInfo());
            }

            // step2: 提现订单审批状态由 P1：待审批 更新为 E1：审批通过 或 R9：审批拒绝
            withdrawOrderDao.updateOrderWcWfSts(withdrawOrderDO);
            return true;
        } catch (Exception e) {
            logger.error("WithdrawServiceImpl.approveWithdrawOrder() 系统异常");
            return false;
//            throw new LemonException(CpoMsgCd.UPDATE_IS_FAILURE.getMsgCd(), CpoMsgCd.UPDATE_IS_FAILURE.getMsgInfo());
        }
    }

    /**
     * 根据付款结果，调用账务模块进行处理，ordSts='A1'(审核通过)、ordSts='S1'(复核通过)、ordSts='F1'(复核拒绝)、ordSts='F2'(审核拒绝)
     */
    @Override
    public GenericRspDTO<NoBody> payOrder(GenericDTO<WdcProcessReqDTO> genericDTO) {
        //请求对象
        WdcProcessReqDTO wdcProcessReqDTO = genericDTO.getBody();
        String ordSts = wdcProcessReqDTO.getOrdSts();
        String reason = wdcProcessReqDTO.getReason();
        String wcOrdNo = wdcProcessReqDTO.getWcOrdNo();
        boolean ordStsChangeFlag = false;
        try {
            // 根据主键查询提现订单信息
            WithdrawOrderDO withdrawOrderDO = withdrawOrderDao.get(wcOrdNo);
            if (JudgeUtils.isNull(withdrawOrderDO)) {
                logger.error("WithdrawServiceImpl.payOrder() 根据订单号没有找到相应的提现订单");
                throw new LemonException(CpoMsgCd.SELECT_IS_NULL.getMsgCd());
            }
            if (JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.ORD_SUCCESS) || JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.ORD_FAIL)) {
                logger.error("WithdrawServiceImpl.payOrder() 原提现订单状态已是最终态，不允许再次更改");
                throw new LemonException(CpoMsgCd.ORG_STS_IS_FINAL.getMsgCd());
            }
            if (!JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.ORD_WAITING_WITHDRAW) && !JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.WAIT_REVIEW)) {
                logger.error("WithdrawServiceImpl.payOrder() 原提现订单状态非处理中，不允许更改");
                throw new LemonException(CpoMsgCd.ORG_STS_IS_NOT_PROCESSING.getMsgCd());
            }

            // 如果请求状态是复核通过或复核拒绝，需要判断提现订单状态是否是待复核
            if (JudgeUtils.equals(ordSts, CpoConstants.ORD_SUCCESS) || JudgeUtils.equals(ordSts, CpoConstants.ORD_FAIL)) {
                if (!JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.WAIT_REVIEW)) {
                    logger.error("提现订单状态非待复核，不允许更改");
                    throw new LemonException(CpoMsgCd.ORG_STS_IS_NOT_PROCESSING.getMsgCd());
                }
                if (JudgeUtils.isNotBlank(withdrawOrderDO.getFirstAuditUser()) && JudgeUtils.equals(withdrawOrderDO.getFirstAuditUser(), genericDTO.getUserId())) {
                    logger.error("复核人和审核人不能是同一个用户");
                    throw new LemonException(CpoMsgCd.REVIEWER_AND_THE_AUDITOR_CANNOT_BE_THE_SAME.getMsgCd());
                }
            }

            if (JudgeUtils.equals(ordSts, CpoConstants.WAIT_REVIEW)) {
                logger.info("审核通过，更新提现订单：{}审核信息", withdrawOrderDO.getWcOrdNo());
                updateCpoWithdrwaOrderAuditInfo(withdrawOrderDO,"APPROVED",reason,genericDTO.getUserId());
                return GenericRspDTO.newSuccessInstance();
            }
            if (JudgeUtils.equals(ordSts, "F2")) {
                logger.info("更新提现订单：{}审核信息", withdrawOrderDO.getWcOrdNo());
                updateCpoWithdrwaOrderAuditInfo(withdrawOrderDO,"REJECTED",reason,genericDTO.getUserId());
                ordSts = CpoConstants.ORD_FAIL;
                ordStsChangeFlag = true;
            }

            //更新提现订单状态
            WithdrawOrderDO updateOrderDO = new WithdrawOrderDO();
            updateOrderDO.setWcOrdNo(wcOrdNo);
            updateOrderDO.setAcDt(genericDTO.getAccDate());
            updateOrderDO.setPostDt(DateTimeUtils.getCurrentLocalDate());
            updateOrderDO.setPostTm(DateTimeUtils.getCurrentLocalTime());
            updateOrderDO.setOrdSts(ordSts);
            updateOrderDO.setRmk(reason);
            if(JudgeUtils.equals(ordSts,CpoConstants.ORD_SUCCESS)) {
                updateOrderDO.setOrdSuccDt(DateTimeUtils.getCurrentLocalDate());
                updateOrderDO.setOrdSuccTm(DateTimeUtils.getCurrentLocalTime());
                logger.info("复核通过，更新提现订单：{}审核信息", withdrawOrderDO.getWcOrdNo());
                updateOrderDO.setSecondAuditUser(genericDTO.getUserId());
                updateOrderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
                updateOrderDO.setSecondAuditResult("APPROVED");
                updateOrderDO.setSecondAuditOpinion(reason);
                updateOrderDO.setExecuteTime(DateTimeUtils.getCurrentLocalDateTime());
            } else if (ordSts.equals(CpoConstants.ORD_FAIL) && !ordStsChangeFlag){
                updateOrderDO.setSecondAuditUser(genericDTO.getUserId());
                updateOrderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
                updateOrderDO.setSecondAuditResult("REJECTED");
                updateOrderDO.setSecondAuditOpinion(reason);
                updateOrderDO.setRejectReason(reason);
            }
            withdrawOrderDao.update(updateOrderDO);

            //更新提现子订单状态
            WithdrawSuborderDO updateSubOrderDo = new WithdrawSuborderDO();
            updateSubOrderDo.setWcOrdNo(wcOrdNo);
            updateSubOrderDo.setAcDt(genericDTO.getAccDate());
            updateSubOrderDo.setOrdSts(ordSts);
            withdrawSuborderDao.update(updateSubOrderDo);

            // 组织付款账务处理所需参数
            if(StringUtils.equals(CpoConstants.ORD_SUCCESS, ordSts)) {
                if (JudgeUtils.equals(CorpBusTyp.DM_WITHDRAW, withdrawOrderDO.getCorpBusTyp())) {
                    //数币提现，调用 Cregis 进行提现
                    //组装cregis请求体
                    // 付款方账户信息
                    GenericRspDTO<DmAccountDetailRspDTO> fkRspDTO = acmManageClient.queryDmAccountDetail(withdrawOrderDO.getAcNo());
                    if (JudgeUtils.isNotSuccess(fkRspDTO.getMsgCd())) {
                        LemonException.throwLemonException("TAM10031");
                    }
                    DmAccountDetailRspDTO fkAcBalRspDTO = fkRspDTO.getBody();

                    PaymentReqDTO paymentReqDTO = new PaymentReqDTO();
                    List<String> includes = new ArrayList<>();
                    paymentReqDTO.setOrderId(withdrawOrderDO.getReqOrdNo());
                    paymentReqDTO.setCoinId(withdrawOrderDO.getCcy());
                    paymentReqDTO.setNetwork(fkAcBalRspDTO.getNetwork());
                    paymentReqDTO.setLang(TamConstants.lang);
                    paymentReqDTO.setFundFlowCode(fundFlowCode);
                    includes.add(fkAcBalRspDTO.getAddress());
                    paymentReqDTO.setIncludes(includes);
                    PaymentPrepareReqDTO.PayTo[] payToList = new PaymentPrepareReqDTO.PayTo[1];
                    payToList[0] = new PaymentPrepareReqDTO.PayTo();
                    payToList[0].setTo(withdrawOrderDO.getAddress());
                    payToList[0].setReadableAmount(withdrawOrderDO.getWcAplAmt());
                    paymentReqDTO.setPayToList(payToList);

                    CregisRsp<NoBody> withdraw = cregisClient.transferAll(paymentReqDTO);
                    logger.info("订单：" + withdrawOrderDO.getReqOrdNo() + " 调用Cregis提交提现结果：" + withdraw.getCode());
//        transferAll.setCode("200");
                    if (JudgeUtils.equals("200", withdraw.getCode())) {
                        logger.info("调用Cregis提交提现订单:{}成功", withdrawOrderDO.getReqOrdNo());
                        handleDmwithdrawOk(withdrawOrderDO);
                        //成功,更新订单状态,财务处理
//				orderSts = TamConstants.ORD_STS_S;
//				TransferOrderDO updateTransfer = new TransferOrderDO();
//				updateTransfer.setBusOrderNo(paymentResultDTO.getBusOrderNo());
//				updateTransfer.setOrderNo(orderNo);
//				updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
//				transactionalService.updateTransferOrder(updateTransfer);
//				// 财务处理
//				GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
//				HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
//				// csh_order表中订单编号
//				handleFinanceDTO.setOrderNo(paymentResultDTO.getOrderNo());
//				// 业务类型
//				handleFinanceDTO.setBusType(TamConstants.BUS_TYPE_DM_TRANSFER);
//				handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
//				GenericRspDTO<NoBody> genericRspDTO = cshOrderClient.handleFinance(handleFinanceDTOGenericDTO);
//				if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
//					LemonException.throwBusinessException(genericRspDTO.getMsgCd());
//				}
                    } else {
                        logger.error("调用Cregis提交提现订单:{}失败", withdrawOrderDO.getReqOrdNo());
//                        ordSts = CpoConstants.ORD_FAIL;
//                        //失败,账务冲正,更新订单状态
//                        TransferOrderDO updateTransfer = new TransferOrderDO();
//                        updateTransfer.setOrderNo(paymentResultDTO.getBusOrderNo());
//                        updateTransfer.setBusOrderNo(paymentResultDTO.getOrderNo());
//                        // 更新订单信息
//                        transactionalService.updateTransferOrder(updateTransfer);
//                        GenericDTO<HandleFailTranDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
//                        HandleFailTranDTO handleFailTranDTO = new HandleFailTranDTO();
//                        // csh_order表中订单编号
//                        handleFailTranDTO.setOrderNo(paymentResultDTO.getOrderNo());
//                        // 业务类型
//                        handleFailTranDTO.setBusType(busType);
//                        handleFinanceDTOGenericDTO.setBody(handleFailTranDTO);
//                        GenericRspDTO<NoBody> genericRspDTO = cshOrderClient.handleFailTransfer(handleFinanceDTOGenericDTO);
//                        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
//                            LemonException.throwBusinessException(genericRspDTO.getMsgCd());
//                        }
                    }
                } else {
                    //代付成功，调用账务模块进行付款,失败不做账，通知外围做账
                    String msgCd = payOrderSuccAccDeal(withdrawOrderDO);
                    if (JudgeUtils.isNotSuccess(msgCd)) {
                        logger.error("WithdrawServiceImpl.payOrder() 付款成功的提现订单记账失败");
                        throw new LemonException(msgCd);
                    }
                    logger.info("==================付款成功订单，记账模块返回码 = " + msgCd);

                    //提现成功，调用风控模块进行累计
                    JrnReqDTO jrnReqDTO = new JrnReqDTO();
                    //收方ID
                    jrnReqDTO.setPayUserId(withdrawOrderDO.getUserId());
                    //收方类型
                    jrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
                    //交易类型
                    jrnReqDTO.setTxTyp(Constants.TX_TYP_WITHDRAW);
                    //交易状态
                    jrnReqDTO.setTxSts(Constants.EFF_FLG_EFF);
                    //交易渠道
                    jrnReqDTO.setTxCnl("APP");
                    //交易金额
                    jrnReqDTO.setTxAmt(withdrawOrderDO.getWcAplAmt());
                    //交易币种
                    jrnReqDTO.setCcy(withdrawOrderDO.getCcy());
                    //交易日期
                    jrnReqDTO.setTxDate(LocalDate.now());
                    //交易时间
                    jrnReqDTO.setTxTime(LocalTime.now());
                    //原交易流水号
                    jrnReqDTO.setTxJrnNo(genericDTO.getRequestId());
                    //原交易订单号
                    jrnReqDTO.setTxOrdNo(wcOrdNo);
                    //支付类型，支付方式，00-全部，01-账户余额，02-账户快捷，03-海币账户，04-微信支付，05-支付宝，06-翼支付
                    jrnReqDTO.setPayTyp(Constants.PAY_TYP_ALL);
                    GenericDTO<JrnReqDTO> rsmJrnReqDTO = new GenericDTO<>();
                    rsmJrnReqDTO.setBody(jrnReqDTO);
                    riskCheckClient.accumulateAmount(rsmJrnReqDTO);
                    logger.info("==================提现成功，调用风控模块进行累计==================");
                }
            }

            //判断业务子类型、对应的内部模块的接口是否为空
            withdrawOrderDO.setOrdSts(ordSts);
            withdrawOrderDO.setRmk(reason);
            withdrawResultNotify(withdrawOrderDO);
            return GenericRspDTO.newSuccessInstance();
        } catch (LemonException e){
            logger.error("WithdrawServiceImpl.payOrder LemonException : ", e);
            return GenericRspDTO.newInstance(e.getMsgCd());

        } catch(Exception e) {
            logger.error("WithdrawServiceImpl.payOrder exception : ", e);
            return GenericRspDTO.newInstance(CpoMsgCd.ACCOUNT_DEAL_FAIL.getMsgCd());
        }

    }

    /**
     * 数币提现成功账务处理
     * @param withdrawOrderDO
     */
    private void handleDmwithdrawOk(WithdrawOrderDO withdrawOrderDO) {
        //根据币种选择应付款科目号 todo 科目号处理
        String itemNo = "";
        for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
            if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), withdrawOrderDO.getCcy())) {
                itemNo = ccyAcItemEnum.getItemNo();
            }
        }

        String bankItemNo = "";
        for(BankAcItemEnum bankAcItemEnum : BankAcItemEnum.values()) {
            if(JudgeUtils.equals(bankAcItemEnum.getCcy(), withdrawOrderDO.getCcy())) {
                bankItemNo = bankAcItemEnum.getItemNo();
            }
        }

        // 根据请求订单号获取请求订单信息，拿到实际提现金额
        String rspOrdNo = withdrawOrderDO.getReqOrdNo();
        PwmWithdrawOrderDO byReqOrder = pwmWithdrawOrderDao.get(rspOrdNo);
        if(byReqOrder == null) {
            logger.error("WithdrawServiceImpl.payOrder() 获取不到请求订单信息");
            throw new LemonException(CpoMsgCd.CPO_SYS_EXCEPTION.getMsgCd());
        }

        //调用账务处理
        GenericDTO<List<AccountingReqDTO>> genericReqDTO = new GenericDTO<>();
        List<AccountingReqDTO> accountingReqInfList = new ArrayList<>();

        //借：应付账款-待结算-支付结算 **********
        String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPO", 8);
        AccountingReqDTO accReqInf1 = AccountUtils.getAccountingReqDTO("N","I","","1",itemNo,CpoConstants.TX_TYP_WITHDRAW,
                byReqOrder.getWcActAmt(), "D", txJrnNo, withdrawOrderDO.getWcOrdNo(),"","","","","");

        //贷：银行存款-备付金账户-XX银行 **********
        AccountingReqDTO accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1",bankItemNo,CpoConstants.TX_TYP_WITHDRAW,
                byReqOrder.getWcActAmt(), "C", txJrnNo, withdrawOrderDO.getWcOrdNo(),"","","","","");

        accountingReqInfList.add(accReqInf1);
        accountingReqInfList.add(accReqInf2);
        genericReqDTO.setBody(accountingReqInfList);
        GenericRspDTO<NoBody> genericDTO = accountingTreatmentClient.accountingTreatment(genericReqDTO);

        //判断账务处理是否成功，不成功退出
        if(JudgeUtils.isNotSuccess(genericDTO.getMsgCd())){
            logger.error("付款成功的提现订单:{}记账失败",withdrawOrderDO.getReqOrdNo());
            LemonException.throwBusinessException(genericDTO.getMsgCd());
        }
        logger.info("==================付款成功订单，记账模块返回码 = " + genericDTO.getMsgCd());

        //提现成功，调用风控模块进行累计
        JrnReqDTO jrnReqDTO = new JrnReqDTO();
        //收方ID
        jrnReqDTO.setPayUserId(withdrawOrderDO.getUserId());
        //收方类型
        jrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
        //交易类型
        jrnReqDTO.setTxTyp(Constants.TX_TYP_DM_WITHDRAW);
        //交易状态
        jrnReqDTO.setTxSts(Constants.EFF_FLG_EFF);
        //交易渠道
        jrnReqDTO.setTxCnl("APP");
        //交易金额
        jrnReqDTO.setTxAmt(withdrawOrderDO.getWcAplAmt());
        //交易币种
        jrnReqDTO.setCcy(withdrawOrderDO.getCcy());
        //交易日期
        jrnReqDTO.setTxDate(LocalDate.now());
        //交易时间
        jrnReqDTO.setTxTime(LocalTime.now());
        //原交易流水号
        jrnReqDTO.setTxJrnNo(genericDTO.getRequestId());
        //原交易订单号
        jrnReqDTO.setTxOrdNo(withdrawOrderDO.getWcOrdNo());
        //支付类型，支付方式，00-全部，01-账户余额，02-账户快捷，03-海币账户，04-微信支付，05-支付宝，06-翼支付
        jrnReqDTO.setPayTyp(Constants.PAY_TYP_ALL);
        GenericDTO<JrnReqDTO> rsmJrnReqDTO = new GenericDTO<>();
        rsmJrnReqDTO.setBody(jrnReqDTO);
        riskCheckClient.accumulateAmount(rsmJrnReqDTO);
        logger.info("==================提现成功，调用风控模块进行累计==================");
    }

    /**
     * 更新提现订单审核信息
     * @param withdrawOrderDO    提现订单信息
     * @param auditResult    审核结果
     * @param reason    审核原因
     */
    private void updateCpoWithdrwaOrderAuditInfo(WithdrawOrderDO withdrawOrderDO,String auditResult,String reason,String userId) {
        WithdrawOrderDO orderDO = new WithdrawOrderDO();
        orderDO.setWcOrdNo(withdrawOrderDO.getWcOrdNo());
        orderDO.setOrdSts(CpoConstants.WAIT_REVIEW);
        orderDO.setFirstAuditUser(userId);
        orderDO.setFirstAuditTime(DateTimeUtils.getCurrentLocalDateTime());
        orderDO.setFirstAuditOpinion(reason);
        orderDO.setFirstAuditResult(auditResult);
        orderDO.setRmk(reason);
        withdrawOrderDao.update(orderDO);
    }

    /**
     * 代付成功，账务处理
     */
    private String payOrderSuccAccDeal(WithdrawOrderDO withdrawOrderDO) {

        //根据币种选择应付款科目号
        String itemNo = "";
        for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
            if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), withdrawOrderDO.getCcy())) {
                itemNo = ccyAcItemEnum.getItemNo();
            }
        }

        String bankItemNo = "";
        for(BankAcItemEnum bankAcItemEnum : BankAcItemEnum.values()) {
            if(JudgeUtils.equals(bankAcItemEnum.getCcy(), withdrawOrderDO.getCcy())) {
                bankItemNo = bankAcItemEnum.getItemNo();
            }
        }

        // 根据请求订单号获取请求订单信息，拿到实际提现金额
        String rspOrdNo = withdrawOrderDO.getReqOrdNo();
        PwmWithdrawOrderDO byReqOrder = pwmWithdrawOrderDao.get(rspOrdNo);
        if(byReqOrder == null) {
            logger.error("WithdrawServiceImpl.payOrder() 获取不到请求订单信息");
            throw new LemonException(CpoMsgCd.CPO_SYS_EXCEPTION.getMsgCd());
        }

        //调用账务处理
        GenericDTO<List<AccountingReqDTO>> genericReqDTO = new GenericDTO<>();
        List<AccountingReqDTO> accountingReqInfList = new ArrayList<>();

        //借：应付账款-待结算-支付结算 **********
        String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPO", 8);
        AccountingReqDTO accReqInf1 = AccountUtils.getAccountingReqDTO("N","I","","1",itemNo,CpoConstants.TX_TYP_WITHDRAW,
                byReqOrder.getWcActAmt(), "D", txJrnNo, withdrawOrderDO.getWcOrdNo(),"","","","","");

        //贷：银行存款-备付金账户-XX银行 **********
        AccountingReqDTO accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1",bankItemNo,CpoConstants.TX_TYP_WITHDRAW,
                byReqOrder.getWcActAmt(), "C", txJrnNo, withdrawOrderDO.getWcOrdNo(),"","","","","");

        accountingReqInfList.add(accReqInf1);
        accountingReqInfList.add(accReqInf2);
        genericReqDTO.setBody(accountingReqInfList);
        GenericRspDTO<NoBody> genericDTO = accountingTreatmentClient.accountingTreatment(genericReqDTO);

        //判断账务处理是否成功，不成功退出
        return genericDTO.getMsgCd();
    }

    /**
     * 新增付款业务参数信息，取不为null的字段值存入表中
     */
    @Override
    public boolean addWithdrawParam(WithdrawParamDO withdrawParamDO) {
        return withdrawParamDao.insert(withdrawParamDO) > 0;
    }

    /**
     * 根据主键查询付款业务参数信息
     */
    @Override
    public WithdrawParamDO getWithdrawParamByKey(String busParId) {
        return withdrawParamDao.get(busParId);
    }

    /**
     * 根据唯一索引查询付款业务参数信息
     */
    @Override
    public WithdrawParamDO getWithdrawParam(WithdrawParamDO withdrawParamDO) {
        return withdrawParamDao.getWithdrawParam(withdrawParamDO);
    }

    /**
     * 根据主键删除付款业务参数信息
     */
    @Override
    public void deleteWithdrawParamByKey(String busParId) {
        withdrawParamDao.delete(busParId);
    }

    /**
     * 根据主键更新付款业务参数信息，取不为null的字段值存入表中
     */
    @Override
    public boolean updateWithdrawParamByKey(WithdrawParamDO withdrawParamDO) {
        return withdrawParamDao.update(withdrawParamDO) > 0;
    }

    /**
     * 根据唯一索引更新付款业务参数信息，取不为null的字段值存入表中
     */
    @Override
    public boolean updateWithdrawParam(WithdrawParamDO withdrawParamDO) {
        return withdrawParamDao.updateWithdrawParam(withdrawParamDO) > 0;
    }

    /**
     * 分页查询待通知的提现订单
     */
    @Override
    public List<WithdrawOrderDO> getWithdrawOrderToNotify(int beginNum, int maxQueryNum) {
        return withdrawOrderDao.getWithdrawOrderToNotify(beginNum, maxQueryNum);
    }

    /**
     * 提现订单结果通知
     */
    private void withdrawResultNotify(WithdrawOrderDO withdrawOrderDO) {
        //更新提现订单的通知状态，默认为成功
        WithdrawOrderDO updateWithdrawOrderDO = new WithdrawOrderDO();
        updateWithdrawOrderDO.setWcOrdNo(withdrawOrderDO.getWcOrdNo());
        updateWithdrawOrderDO.setNtfRspCd("00000");
        updateWithdrawOrderDO.setNtfRspMsg("默认通知成功");
        updateWithdrawOrderDO.setNtfDt(DateTimeUtils.getCurrentLocalDate());
        updateWithdrawOrderDO.setNtfTm(DateTimeUtils.getCurrentLocalTime());
        updateWithdrawOrderDO.setNtfSts(CpoConstants.NOTIFY_STATUS_SUCCESS);
        withdrawOrderDao.update(updateWithdrawOrderDO);

        //根据业务子类型，调用不同模块的通知接口
        String corpBusSubTyp = withdrawOrderDO.getCorpBusSubTyp();
        switch(corpBusSubTyp) {
            //个人提现
            case "0601":
                withdrawNotifyProduce.pwmWithdrawNotify(withdrawOrderDO);
                break;

            //转账到银行卡
            case "0602":
                withdrawNotifyProduce.tamWithdrawNotify(withdrawOrderDO);
                break;

            //商户结算
            case "0603":
                withdrawNotifyProduce.csmWithdrawNotify(withdrawOrderDO);
                break;

            //数币提现
            case "DX01":
                withdrawNotifyProduce.pwmDmWithdrawNotify(withdrawOrderDO);
                break;
            default:
                break;
        }
    }

    /**
     * 登记营业厅提现订单
     */
    @Override
    public GenericRspDTO<WithdrawResDTO> createHallOrder(GenericDTO<WithdrawHallReqDTO> genericDTO) {
        logger.info("==================开始创建资金流出订单==================");

        //请求参数
        WithdrawHallReqDTO withdrawReqDTO = genericDTO.getBody();
        //返回结果
        WithdrawResDTO withdrawResDTO = new WithdrawResDTO();
        //提现订单
        WithdrawOrderDO withdrawOrderDO = new WithdrawOrderDO();
        //用户号/商户号
        String userId = LemonUtils.getUserId();
        if(JudgeUtils.isBlank(userId)) {
            userId = withdrawReqDTO.getUserNo();
        }

        //将DTO对象的数据复制到DO对象
        BeanUtils.copyProperties(withdrawOrderDO, withdrawReqDTO);
        withdrawOrderDO.setCorpBusTyp(withdrawReqDTO.getCorpBusTyp().getType());
        withdrawOrderDO.setCorpBusSubTyp(withdrawReqDTO.getCorpBusSubTyp().getType());

        //提现订单号
        String wcOrdNo = IdGenUtils.generateIdWithDateTime("CPO", "CPO", 6);
        try {
            //业务类型和业务子类型检查
            String corpBusTyp = withdrawOrderDO.getCorpBusTyp();
            String corpBusSubTyp = withdrawOrderDO.getCorpBusSubTyp();
            logger.debug("==================业务类型 corpBusTyp：" + corpBusTyp + ", corpBusSubTyp：" + corpBusSubTyp +"订单号："+wcOrdNo);
            if(!StringUtils.equals(CorpBusTyp.WITHDRAW.getType(), corpBusTyp)) {
                logger.error("==================业务类型不正确==================");
                throw new LemonException(CpoMsgCd.CORP_BUS_TYP_WRONG.getMsgCd());
            }
            if (!StringUtils.equals(CorpBusSubTyp.PER_WITHDRAW.getType(), corpBusSubTyp)
                    && !StringUtils.equals(CorpBusSubTyp.CARD_WITHDRAW.getType(), corpBusSubTyp)
                    && !StringUtils.equals(CorpBusSubTyp.MERC_WITHDRAW.getType(), corpBusSubTyp)
                    && !StringUtils.equals(CorpBusSubTyp.PER_HALL_WITHDRAW.getType(), corpBusSubTyp)
                    && !StringUtils.equals(CorpBusSubTyp.MERC_HALL_WITHDRAW.getType(), corpBusSubTyp)) {
                logger.error("==================业务子类型不正确==================");
                throw new LemonException(CpoMsgCd.CORP_BUS_SUB_TYP_WRONG.getMsgCd());
            }

            // step6: 创建资金流出订单
            LocalDate currDate = DateTimeUtils.getCurrentLocalDate();
            LocalTime currTime = DateTimeUtils.getCurrentLocalTime();
            withdrawOrderDO.setRutCorg("HALL");
            withdrawOrderDO.setUserId(userId);
            withdrawOrderDO.setWcOrdNo(wcOrdNo);
            withdrawOrderDO.setOrdDt(currDate);
            withdrawOrderDO.setOrdTm(currTime);
            withdrawOrderDO.setOrdSts(CpoConstants.ORD_SUCCESS);
            withdrawOrderDO.setWcWfSts(CpoConstants.ORD_APPROVAL_SUCCESS);
            withdrawOrderDao.insert(withdrawOrderDO);
            logger.info("==================创建资金流出订单，订单号: " + wcOrdNo);

            // step7: 创建资金流出子订单
            WithdrawSuborderDO withdrawSuborderDO = new WithdrawSuborderDO();
            BeanUtils.copyProperties(withdrawSuborderDO, withdrawOrderDO);
            String subOrdNo = IdGenUtils.generateIdWithDateTime("CPO", "CPO", 6);
            withdrawSuborderDO.setSubOrdNo(subOrdNo);
            withdrawSuborderDO.setChkKey(subOrdNo);
            withdrawSuborderDao.insert(withdrawSuborderDO);
            logger.info("==================创建资金流出子订单，子订单序号: " + subOrdNo);

            //接口返回结果
            withdrawResDTO.setOrdNo(wcOrdNo);
            return GenericRspDTO.newSuccessInstance(withdrawResDTO);
        } catch (LemonException e){
            logger.error("WithdrawServiceImpl.createOrder LemonException : ", e);
            return GenericRspDTO.newInstance(e.getMsgCd(),withdrawResDTO);
        } catch(Exception e) {
            logger.error("WithdrawServiceImpl.createOrder exception : ", e);
            return GenericRspDTO.newInstance(CpoMsgCd.CPO_SYS_EXCEPTION.getMsgCd(),withdrawResDTO);
        }
    }

}
