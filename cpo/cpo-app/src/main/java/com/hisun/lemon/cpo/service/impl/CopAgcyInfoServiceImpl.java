package com.hisun.lemon.cpo.service.impl;

import java.io.IOException;

import javax.annotation.Resource;

import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.common.utils.ResourceUtils;
import com.hisun.lemon.cpo.dao.ICopAgcyInfoDao;
import com.hisun.lemon.cpo.entity.CopAgcyInfoDO;
import com.hisun.lemon.cpo.service.ICopAgcyInfoService;

/**
 * 合作机构基本信息service实现
 * 
 * <AUTHOR>
 *
 */
@Transactional
@Service
public class CopAgcyInfoServiceImpl extends BaseService implements ICopAgcyInfoService {

	@Resource
	private ICopAgcyInfoDao copAgcyInfoDao;

	@Override
	public boolean add(CopAgcyInfoDO copAgcyInfoDO) {
		boolean bool = false;
		int num = copAgcyInfoDao.insert(copAgcyInfoDO);
		if (num > 0) {
			bool = true;
		}
		return bool;
	}

	@Override
	public CopAgcyInfoDO selectByKey(String orgInfId) {
		return copAgcyInfoDao.get(orgInfId);
	}

	@Override
	public boolean updateByKey(CopAgcyInfoDO copAgcyInfoDO) {
		boolean bool = false;
		int num = copAgcyInfoDao.update(copAgcyInfoDO);
		if (num > 0) {
			bool = true;
		}
		return bool;
	}

	@Override
	public boolean deleteByKey(String orgInfId) {
		boolean bool = false;
		int num = copAgcyInfoDao.delete(orgInfId);
		if (num > 0) {
			bool = true;
		}
		return bool;
	}

}
