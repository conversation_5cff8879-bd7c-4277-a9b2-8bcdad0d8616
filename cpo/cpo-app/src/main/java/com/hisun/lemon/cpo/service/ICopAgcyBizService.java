package com.hisun.lemon.cpo.service;

import com.hisun.lemon.cpo.dto.CopAgcyBizDTO;
import com.hisun.lemon.cpo.dto.CopAgcyInfoDTO;
import com.hisun.lemon.cpo.entity.CopAgcyBizDO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

import java.util.List;

/**
 * 合作机构业务表service
 * 
 * <AUTHOR>
 *
 */
public interface ICopAgcyBizService {

	/**
	 * 新增机构业务信息
	 * @param genericDTO
	 * @return boolean
	 */
	GenericRspDTO<NoBody> addCopAgcyBiz(GenericDTO<CopAgcyBizDTO> genericDTO);

	/**
	 * 根据主键获取机构业务信息
	 * @param orgBusId
	 * @return CopAgcyBizDO
	 */
	CopAgcyBizDO selectByKey(String orgBusId);

	/**
	 * 更新机构业务信息
	 * @param copAgcyBizDO
	 * @return boolean
	 */
	boolean updateByKey(CopAgcyBizDO copAgcyBizDO);

	/**
	 * 根据主键删除机构业务信息
	 * @param orgBusId
	 * @return boolean
	 */
	boolean deleteByKey(String orgBusId);

	/**
	 * 查询合作机构业务信息
	 * @return List<CopAgcyBizDO>
	 */
	GenericRspDTO<List<CopAgcyInfoDTO>> getAgcyList();
}
