package com.hisun.lemon.cpo.service;

import com.hisun.lemon.cpo.entity.AccCfgDO;
import com.hisun.lemon.cpo.entity.AccControlDO;

/**
 * 对账服务扩展 service
 * 业务类型：提现
 */
public interface ICheckAccWithdrawService {
    /**
     * 根据银行成功的对账明细，和我方订单进行对账
     * @param accControlDO 对账批次信息
     * @param accCfgDO 对账配置信息
     */
    String checkAccountWithdraw(AccControlDO accControlDO, AccCfgDO accCfgDO) throws Exception;
}
