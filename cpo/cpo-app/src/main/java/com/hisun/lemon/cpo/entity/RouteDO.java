package com.hisun.lemon.cpo.entity;

/**
 * Created by <PERSON><PERSON> on 2017/7/8.
 * 合作路由信息
 */
public class RouteDO {
    /**
     * 资金机构
     */
    private String crdCorpOrg;

    /**
     * 业务类型
     */
    private String corpBusTyp;

    /**
     * 业务子类型
     */
    private String corpBusSubTyp;

    /**
     * 路径机构
     */
    private String rutCorpOrg;

    /**
     * 卡种
     */
    private String crdAcTyp;

    /**
     * 合作机构编号
     */
    private String corpOrgId;
    /**
     * 合作机构名称
     */
    private String corpOrgNm;
    /**
     * 合作机构名简称
     */
    private String corpOrgSnm;

    /**
     * 资金机构账户名称
     */
    private String corpAccNm;

    /**
     * 资金机构卡号
     */
    private String corpAccNo;

    /**
     * 备注
     */
    private String rmk;

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCorpOrgId() {
        return corpOrgId;
    }

    public void setCorpOrgId(String corpOrgId) {
        this.corpOrgId = corpOrgId;
    }

    public String getCorpOrgNm() {
        return corpOrgNm;
    }

    public void setCorpOrgNm(String corpOrgNm) {
        this.corpOrgNm = corpOrgNm;
    }

    public String getCorpOrgSnm() {
        return corpOrgSnm;
    }

    public void setCorpOrgSnm(String corpOrgSnm) {
        this.corpOrgSnm = corpOrgSnm;
    }

    public String getCorpAccNm() {
        return corpAccNm;
    }

    public void setCorpAccNm(String corpAccNm) {
        this.corpAccNm = corpAccNm;
    }

    public String getCorpAccNo() {
        return corpAccNo;
    }

    public void setCorpAccNo(String corpAccNo) {
        this.corpAccNo = corpAccNo;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }
}
