package com.hisun.lemon.cpo.service;

import com.hisun.lemon.cpo.dto.RouteRspDTO;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericRspDTO;

import java.util.List;

public interface IRouteService {

    /**
     * 根据业务类型和子类型查询生效的资金合作机构
     * @param corpBusTyp
     * @param corpBusSubTyp
     * @return
     */
    GenericRspDTO<RouteRspDTO> queryEffCapOrgInfo(CorpBusTyp corpBusTyp, CorpBusSubTyp corpBusSubTyp);
}
