package com.hisun.lemon.cpo.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.cpo.common.CpoMsgCd;
import com.hisun.lemon.cpo.dao.IRouteDao;
import com.hisun.lemon.cpo.dto.RouteRspDTO;
import com.hisun.lemon.cpo.entity.CopAgcyInfoDO;
import com.hisun.lemon.cpo.entity.RouteDO;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import com.hisun.lemon.cpo.service.IRouteService;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Transactional
@Service
public class RouteServiceImpl extends BaseService implements IRouteService{

    @Resource
    IRouteDao routeDao;

    /**
     * 根据业务类型和子类型查询生效的资金合作机构
     */
    @Override
    public GenericRspDTO<RouteRspDTO> queryEffCapOrgInfo(CorpBusTyp corpBusTyp, CorpBusSubTyp corpBusSubTyp) {
        String corpBusTypStr = corpBusTyp.getType();
        String corpBusSubTypStr = corpBusSubTyp.getType();
        List<RouteDO> routeDOList = routeDao.queryEffCapOrgInfo(corpBusTypStr, corpBusSubTypStr);

        //若查询结果不为空
        RouteRspDTO routeRspDTO = new RouteRspDTO();
        List<RouteRspDTO.RouteDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(routeDOList)) {
            for (RouteDO routeDO : routeDOList) {
                RouteRspDTO.RouteDTO routeDTO = new RouteRspDTO.RouteDTO();
                BeanUtils.copyProperties(routeDTO,routeDO);
                list.add(routeDTO);
            }
        }

        routeRspDTO.setList(list);
        //返回生效的、支持提现业务的合作资金机构信息
        if (CollectionUtils.isNotEmpty(list)) {
            return GenericRspDTO.newSuccessInstance(routeRspDTO);
        } else {
            return GenericRspDTO.newInstance(CpoMsgCd.ORG_INFO_IS_NOT_FOUND.getMsgCd(),routeRspDTO);
        }
    }
}
