package com.hisun.lemon.cpo.controller;

import com.hisun.lemon.cpo.dto.*;
import com.hisun.lemon.cpo.service.IWithdrawService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 资金流出业务controller
 */
@RestController
@RequestMapping("/cpo/orders")
public class WithdrawController extends BaseController {

    @Resource
    IWithdrawService withdrawService;

    @ApiOperation(value="申请提现订单", notes="申请提现订单")
    @ApiResponse(code = 200, message = "申请提现订单结果")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @PostMapping("/payment")
    public GenericRspDTO<WithdrawResDTO> createOrder(@Validated @RequestBody GenericDTO<WithdrawReqDTO> genericDTO) {
        return withdrawService.createOrder(genericDTO);
    }

    @ApiOperation(value="提现订单详细信息查询", notes="提现订单详细信息查询")
    @ApiResponse(code = 200, message = "提现订单详细信息")
    @GetMapping("/detail")
    public GenericRspDTO<WdcOrdDetailResDTO> queryOrder(@Validated @ApiParam(name = "ordNo", value = "订单号", required = true) @RequestParam(value = "ordNo") String ordNo) {
        return withdrawService.queryOrder(ordNo);

    }

//    @ApiOperation(value="提现订单审批", notes="提现订单审批")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "wcOrdNo", value = "提现订单号", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "wcWfSts", value = "审核状态", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "wcWfRsn", value = "审核拒绝原因", required = false, dataType = "String"),
//            @ApiImplicitParam(name = "oprId", value = "审批人ID", required = true, dataType = "String")
//    })
//    @ApiResponse(code = 200, message = "提现订单审批")
//    @PutMapping("/orders/{ordNo}")
//    public GenericDTO approveOrder(@Validated @RequestParam String wcOrdNo, @Validated @RequestParam String wcWfSts, @RequestParam String wcWfRsn, @RequestParam String oprId) {
//        //创建DTO对象
//        WithdrawDTO withdrawDTO = new WithdrawDTO();
//
//        //更新提现订单审批状态
//        WithdrawOrderDO withdrawOrderDO = new WithdrawOrderDO();
//        withdrawOrderDO.setOrdNo(wcOrdNo);
//        withdrawOrderDO.setWcWfSts(wcWfSts);
//        withdrawOrderDO.setWcWfRsn(wcWfRsn);
//        withdrawOrderDO.setOprId(oprId);
//        boolean isSuccess = withdrawService.approveWithdrawOrder(withdrawOrderDO);
//
//        //判断更新数据是否成功
//        if(isSuccess) {
//            withdrawDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
//        } else {
//            withdrawDTO.setMsgCd(CpoMsgCd.UPDATE_IS_FAILURE.getMsgCd());
//        }
//        return withdrawDTO;
//    }

    @ApiOperation(value="付款处理", notes="付款处理")
    @ApiResponse(code = 200, message = "付款处理")
    @PutMapping("/result")
    public GenericDTO<NoBody> payOrder(@Validated @RequestBody GenericDTO<WdcProcessReqDTO> genericDTO) {
        return withdrawService.payOrder(genericDTO);
    }

    @ApiOperation(value="营业厅提现订单登记", notes="营业厅提现订单登记")
    @ApiResponse(code = 200, message = "营业厅提现订单登记结果")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @PostMapping("/hall/payment")
    public GenericRspDTO<WithdrawResDTO> createHallOrder(@Validated @RequestBody GenericDTO<WithdrawHallReqDTO> genericDTO) {
        return withdrawService.createHallOrder(genericDTO);
    }

}
