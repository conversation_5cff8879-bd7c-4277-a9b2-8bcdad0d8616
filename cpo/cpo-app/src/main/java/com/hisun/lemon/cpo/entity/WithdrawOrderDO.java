package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

/**
 * 资金流出业务订单表
 * <AUTHOR>
 */
public class WithdrawOrderDO extends BaseDO {

    /**
     * 内部订单号
     */
    private String wcOrdNo;

    /**
     * 订单日期
     */
    private LocalDate ordDt;

    /**
     * 订单时间
     */
    private LocalTime ordTm;

    /**
     * 会计日期
     */
    private LocalDate acDt;

    /**
     * 币种
     */
    private String ccy;

    /**
     * 资金种类，1现金
     */
    private String capTyp;

    /**
     * 付款方账户编码
     */
    private String acNo;

    /**
     * 数币收款方地址
     */
    private String address;

    /**
     * 资金合作机构号
     */
    private String capCorg;

    /**
     * 路径合作机构号
     */
    private String rutCorg;

    /**
     * 卡种类，D借记卡，C贷记卡
     */
    private String crdAcTyp;

    /**
     * 合作业务类型
     */
    private String corpBusTyp;

    /**
     * 合作业务子类型
     */
    private String corpBusSubTyp;

    /**
     * 请求方订单号
     */
    private String reqOrdNo;

    /**
     * 请求方订单日期
     */
    private LocalDate reqOrdDt;

    /**
     * 请求方订单时间
     */
    private LocalTime reqOrdTm;

    /**
     * 协议付款日
     */
    private LocalDate agrPayDt;

    /**
     * 清算周期
     */
    private String stlAcPerd;

    /**
     * 提现申请金额
     */
    private BigDecimal wcAplAmt;

    /**
     * 提现手续费
     */
    private BigDecimal wcFeeAmt;

    /**
     * 冻结编号
     */
    private String holdNo;

    /**
     * 个人/商户标识
     */
    private String psnCrpFlg;

    /**
     * 手机号
     */
    private String mblNo;

    /**
     * 用户/商户名称
     */
    private String userNm;

    /**
     * 内部用户号/商户号
     */
    private String userId;

    /**
     * 证件类型
     */
    private String idTyp;

    /**
     * 加密证件号码
     */
    private String idNoEnc;

    /**
     * 加密银行卡号
     */
    private String crdNoEnc;

    /**
     * 银行卡用户名
     */
    private String capCrdNm;

    /**
     * 提现备注
     */
    private String wcRmk;

    /**
     * 订单状态
     */
    private String ordSts;

    /**
     * 提现审批工作流状态
     */
    private String wcWfSts;

    /**
     * 审批人ID
     */
    private String oprId;

    /**
     * 审批原因
     */
    private String wcWfRsn;

    /**
     * 提现内部批次号
     */
    private String wcBatNo;

    /**
     * 提现批次子序号
     */
    private String wcBatSeq;

    /**
     * 记账日期
     */
    private LocalDate postDt;

    /**
     * 记账时间
     */
    private LocalTime postTm;

    /**
     * 自动付款标志，0：自动，1：手工
     */
    private String autoPayFlg;

    /**
     * 实时付款标识，0实时，1，非实时
     */
    private String wdcAutoFlg;

    /**
     * 付款时间内标识，0付款时间内，1付款时间外
     */
    private String paytmFlg;

    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    /**
     * @Fields ordSuccDt 订单成功日期
     */
    private LocalDate ordSuccDt;

    /**
     * @Fields ordSuccTm 订单成功时间
     */
    private LocalTime ordSuccTm;

    /**
     * @Fields ntfSts 通知状态，W：待通知，S：通知成功，F：通知失败（可重发），E：通知失败（不可重发）
     */
    private String ntfSts;

    /**
     * @Fields ntfDt 通知日期
     */
    private LocalDate ntfDt;

    /**
     * @Fields ntfTm 通知时间
     */
    private LocalTime ntfTm;

    /**
     * @Fields ntfRspCd 通知返回码
     */
    private String ntfRspCd;

    /**
     * @Fields ntfRspMsg 通知返回信息
     */
    private String ntfRspMsg;

    /**
     * 备注
     */
    private String rmk;

    /**
     * 分行名称
     */
    private String subbranch;

    /**
     * 初审人
     */
    private String firstAuditUser;

    /**
     * 初审时间
     */
    private LocalDateTime firstAuditTime;

    /**
     * 初审结果：APPROVED/REJECTED
     */
    private String firstAuditResult;

    /**
     * 初审意见
     */
    private String firstAuditOpinion;

    /**
     * 复核人
     */
    private String secondAuditUser;

    /**
     * 复核时间
     */
    private LocalDateTime secondAuditTime;

    /**
     * 复核结果：APPROVED/REJECTED
     */
    private String secondAuditResult;


    /**
     * 复核意见
     */
    private String secondAuditOpinion;


    /**
     * 执行时间（成功时）
     */
    private LocalDateTime executeTime;


    /**
     * 拒绝原因（最终拒绝原因）
     */
    private String rejectReason;
    

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public LocalDate getAgrPayDt() {
        return agrPayDt;
    }

    public void setAgrPayDt(LocalDate agrPayDt) {
        this.agrPayDt = agrPayDt;
    }

    public String getStlAcPerd() {
        return stlAcPerd;
    }

    public void setStlAcPerd(String stlAcPerd) {
        this.stlAcPerd = stlAcPerd;
    }

    public BigDecimal getWcAplAmt() {
        return wcAplAmt;
    }

    public void setWcAplAmt(BigDecimal wcAplAmt) {
        this.wcAplAmt = wcAplAmt;
    }

    public BigDecimal getWcFeeAmt() {
        return wcFeeAmt;
    }

    public void setWcFeeAmt(BigDecimal wcFeeAmt) {
        this.wcFeeAmt = wcFeeAmt;
    }

    public String getHoldNo() {
        return holdNo;
    }

    public void setHoldNo(String holdNo) {
        this.holdNo = holdNo;
    }

    public String getPsnCrpFlg() {
        return psnCrpFlg;
    }

    public void setPsnCrpFlg(String psnCrpFlg) {
        this.psnCrpFlg = psnCrpFlg;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getUserNm() {
        return userNm;
    }

    public void setUserNm(String userNm) {
        this.userNm = userNm;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNoEnc() {
        return idNoEnc;
    }

    public void setIdNoEnc(String idNoEnc) {
        this.idNoEnc = idNoEnc;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getCapCrdNm() {
        return capCrdNm;
    }

    public void setCapCrdNm(String capCrdNm) {
        this.capCrdNm = capCrdNm;
    }

    public String getWcRmk() {
        return wcRmk;
    }

    public void setWcRmk(String wcRmk) {
        this.wcRmk = wcRmk;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getWcWfSts() {
        return wcWfSts;
    }

    public void setWcWfSts(String wcWfSts) {
        this.wcWfSts = wcWfSts;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public String getWcWfRsn() {
        return wcWfRsn;
    }

    public void setWcWfRsn(String wcWfRsn) {
        this.wcWfRsn = wcWfRsn;
    }

    public String getWcBatNo() {
        return wcBatNo;
    }

    public void setWcBatNo(String wcBatNo) {
        this.wcBatNo = wcBatNo;
    }

    public String getWcBatSeq() {
        return wcBatSeq;
    }

    public void setWcBatSeq(String wcBatSeq) {
        this.wcBatSeq = wcBatSeq;
    }

    public LocalDate getPostDt() {
        return postDt;
    }

    public void setPostDt(LocalDate postDt) {
        this.postDt = postDt;
    }

    public LocalTime getPostTm() {
        return postTm;
    }

    public void setPostTm(LocalTime postTm) {
        this.postTm = postTm;
    }

    public String getAutoPayFlg() {
        return autoPayFlg;
    }

    public void setAutoPayFlg(String autoPayFlg) {
        this.autoPayFlg = autoPayFlg;
    }

    public String getWdcAutoFlg() {
        return wdcAutoFlg;
    }

    public void setWdcAutoFlg(String wdcAutoFlg) {
        this.wdcAutoFlg = wdcAutoFlg;
    }

    public String getPaytmFlg() {
        return paytmFlg;
    }

    public void setPaytmFlg(String paytmFlg) {
        this.paytmFlg = paytmFlg;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getWcOrdNo() {
        return wcOrdNo;
    }

    public void setWcOrdNo(String wcOrdNo) {
        this.wcOrdNo = wcOrdNo;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public LocalDate getOrdSuccDt() {
        return ordSuccDt;
    }

    public void setOrdSuccDt(LocalDate ordSuccDt) {
        this.ordSuccDt = ordSuccDt;
    }

    public LocalTime getOrdSuccTm() {
        return ordSuccTm;
    }

    public void setOrdSuccTm(LocalTime ordSuccTm) {
        this.ordSuccTm = ordSuccTm;
    }

    public String getNtfSts() {
        return ntfSts;
    }

    public void setNtfSts(String ntfSts) {
        this.ntfSts = ntfSts;
    }

    public LocalDate getNtfDt() {
        return ntfDt;
    }

    public void setNtfDt(LocalDate ntfDt) {
        this.ntfDt = ntfDt;
    }

    public LocalTime getNtfTm() {
        return ntfTm;
    }

    public void setNtfTm(LocalTime ntfTm) {
        this.ntfTm = ntfTm;
    }

    public String getNtfRspCd() {
        return ntfRspCd;
    }

    public void setNtfRspCd(String ntfRspCd) {
        this.ntfRspCd = ntfRspCd;
    }

    public String getNtfRspMsg() {
        return ntfRspMsg;
    }

    public void setNtfRspMsg(String ntfRspMsg) {
        this.ntfRspMsg = ntfRspMsg;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getSubbranch() {
        return subbranch;
    }

    public void setSubbranch(String subbranch) {
        this.subbranch = subbranch;
    }

    public String getFirstAuditUser() {
        return firstAuditUser;
    }

    public void setFirstAuditUser(String firstAuditUser) {
        this.firstAuditUser = firstAuditUser;
    }

    public LocalDateTime getFirstAuditTime() {
        return firstAuditTime;
    }

    public void setFirstAuditTime(LocalDateTime firstAuditTime) {
        this.firstAuditTime = firstAuditTime;
    }

    public String getFirstAuditResult() {
        return firstAuditResult;
    }

    public void setFirstAuditResult(String firstAuditResult) {
        this.firstAuditResult = firstAuditResult;
    }

    public String getFirstAuditOpinion() {
        return firstAuditOpinion;
    }

    public void setFirstAuditOpinion(String firstAuditOpinion) {
        this.firstAuditOpinion = firstAuditOpinion;
    }

    public String getSecondAuditUser() {
        return secondAuditUser;
    }

    public void setSecondAuditUser(String secondAuditUser) {
        this.secondAuditUser = secondAuditUser;
    }

    public LocalDateTime getSecondAuditTime() {
        return secondAuditTime;
    }

    public void setSecondAuditTime(LocalDateTime secondAuditTime) {
        this.secondAuditTime = secondAuditTime;
    }

    public String getSecondAuditResult() {
        return secondAuditResult;
    }

    public void setSecondAuditResult(String secondAuditResult) {
        this.secondAuditResult = secondAuditResult;
    }

    public String getSecondAuditOpinion() {
        return secondAuditOpinion;
    }

    public void setSecondAuditOpinion(String secondAuditOpinion) {
        this.secondAuditOpinion = secondAuditOpinion;
    }

    public LocalDateTime getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(LocalDateTime executeTime) {
        this.executeTime = executeTime;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }
}