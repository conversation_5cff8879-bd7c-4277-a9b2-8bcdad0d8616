package com.hisun.lemon.cpo.dao;

import com.hisun.lemon.cpo.entity.WithdrawOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 资金流出订单表dao
 *
 */
@Mapper
public interface IWithdrawOrderDao extends BaseDao<WithdrawOrderDO> {

    /**
     * 根据主键查询资金流出订单的审批状态
     */
    String getWcWfStsByKey(String wcOrdNo);

    /**
     * 提现审批，更新提现订单的审批状态，wc_wf_sts由 P1:待审批 更新为 E1:审批通过 或 R9:审批拒绝
     */
    int updateOrderWcWfSts(WithdrawOrderDO withdrawOrderDO);

    /**
     * 分页查询待通知的提现订单
     * @param beginNum 从表中第几行开始获取记录
     * @param maxQueryNum 每页最大笔数
     */
    List<WithdrawOrderDO> getWithdrawOrderToNotify(@Param("beginNum")int beginNum, @Param("maxQueryNum")int maxQueryNum);

    /**
     * 获取提现明细 List
     * @param ordDt 订单日期
     * @param beginNum 从表中第几行开始取值
     * @param countNum 最大读取多少行记录
     */
    List<WithdrawOrderDO> getWithdrawOrderList(@Param("ordDt") LocalDate ordDt,
                                               @Param("beginNum")Integer beginNum,
                                               @Param("countNum")Integer countNum,
                                               @Param("corpBusSubTyp")String corpBusSubTyp,
                                               @Param("ordSts")String ordSts);
}