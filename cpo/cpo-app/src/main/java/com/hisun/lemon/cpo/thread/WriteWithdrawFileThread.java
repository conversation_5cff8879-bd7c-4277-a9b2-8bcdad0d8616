package com.hisun.lemon.cpo.thread;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.service.ICheckAccControlService;
import com.hisun.lemon.framework.lock.DistributedLocker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;

/**
 * 写入充值对账文件线程
 */
public class WriteWithdrawFileThread implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(WriteWithdrawFileThread.class);

    /**
     * redis锁
     */
    private DistributedLocker distributedLocker;
    /**
     * 对账服务
     */
    private ICheckAccControlService checkAccControlService;
    /**
     * 业务类型
     */
    private CorpBusSubTyp corpBusSubTyp;
    /**
     * 需查询的订单状态
     */
    private String ordSts;

    /**
     * 线程中不能用注解来注入service服务等
     */
    public WriteWithdrawFileThread() {
        super();
    }

    public WriteWithdrawFileThread(ICheckAccControlService checkAccControlService, DistributedLocker distributedLocker,
                                   CorpBusSubTyp corpBusSubTyp, String ordSts) {
        this.checkAccControlService = checkAccControlService;
        this.distributedLocker = distributedLocker;
        this.corpBusSubTyp = corpBusSubTyp;
        this.ordSts = ordSts;
    }

    /**
     * 执行对账服务，传入对象参数DO
     */
    @Override
    public void run() {
        try {
            //锁名
            String lockName = "CPO_WRITE_WITHDRAW_FILE_"+corpBusSubTyp.getType()+"_"+ordSts;
            //释放锁的时间（异常情况下最长的释放锁的时间，单位秒）
            int leaseTime = 40*60;
            //获取应用锁的时间，时间设置必须短，否则影响后面的线程获取锁（单位秒）
            int waitTime = 30;
            //对账日期
            LocalDate chkFilDt = DateTimeUtils.getCurrentLocalDate().minusDays(1L);

            //方法加锁
            distributedLocker.lock(lockName, leaseTime, waitTime, () -> {
                checkAccControlService.writeCheckFile(chkFilDt,"CPO", corpBusSubTyp, ordSts);
                return null;
            });
        } catch (Exception e) {
            logger.error("写入提现对账文件执行失败，异常为 ",e);
            return ;
        }
    }
}
