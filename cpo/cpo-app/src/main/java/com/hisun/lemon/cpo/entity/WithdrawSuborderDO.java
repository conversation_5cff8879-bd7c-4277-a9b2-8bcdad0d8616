/*
 * @ClassName WithdrawSuborderDO
 * @Description
 * @version 1.0
 * @Date 2017-07-12 19:25:57
 */
package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class WithdrawSuborderDO extends BaseDO {
    /**
     * @Fields subOrdNo 子订单号
     */
    private String subOrdNo;

    /**
     * @Fields wcOrdNo 订单号
     */
    private String wcOrdNo;

    /**
     * @Fields rutCorg 路径合作机构号
     */
    private String rutCorg;

    /**
     * @Fields capCorg 资金合作机构号
     */
    private String capCorg;

    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;

    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;

    /**
     * @Fields wcAplAmt 提现金额
     */
    private BigDecimal wcAplAmt;

    /**
     * @Fields ordSts 订单状态
     */
    private String ordSts;

    /**
     * 订单日期
     */
    private LocalDate ordDt;

    /**
     * @Fields acDt 记账日期
     */
    private LocalDate acDt;

    /**
     * @Fields rutCorgJrn 银行返回流水号
     */
    private String rutCorgJrn;

    /**
     * @Fields rutCorgDt 银行返回日期
     */
    private LocalDate rutCorgDt;

    /**
     * @Fields rutCorgTm 银行返回时间
     */
    private LocalTime rutCorgTm;

    /**
     * @Fields orgRspCd 返回码
     */
    private String orgRspCd;

    /**
     * @Fields orgRspMsg 返回信息
     */
    private String orgRspMsg;

    /**
     * 对账外键(发往银行的订单号)
     */
    private String chkKey;

    /**
     * @Fields chkSts 对账状态 0-未对账；1-对账成功；2-长款；3-短款；4-金额错；5-存疑
     */
    private String chkSts;

    /**
     * @Fields chkDt 对账日期
     */
    private LocalDate chkDt;

    /**
     * @Fields chkTm 对账时间
     */
    private LocalTime chkTm;

    /**
     * @Fields cavDt 核销日期
     */
    private LocalDate cavDt;

    /**
     * @Fields cavTm 核销时间
     */
    private LocalTime cavTm;

    /**
     * @Fields cavOper 核销操作员
     */
    private String cavOper;

    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getSubOrdNo() {
        return subOrdNo;
    }

    public void setSubOrdNo(String subOrdNo) {
        this.subOrdNo = subOrdNo;
    }

    public String getWcOrdNo() {
        return wcOrdNo;
    }

    public void setWcOrdNo(String wcOrdNo) {
        this.wcOrdNo = wcOrdNo;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public BigDecimal getWcAplAmt() {
        return wcAplAmt;
    }

    public void setWcAplAmt(BigDecimal wcAplAmt) {
        this.wcAplAmt = wcAplAmt;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getRutCorgJrn() {
        return rutCorgJrn;
    }

    public void setRutCorgJrn(String rutCorgJrn) {
        this.rutCorgJrn = rutCorgJrn;
    }

    public LocalDate getRutCorgDt() {
        return rutCorgDt;
    }

    public void setRutCorgDt(LocalDate rutCorgDt) {
        this.rutCorgDt = rutCorgDt;
    }

    public LocalTime getRutCorgTm() {
        return rutCorgTm;
    }

    public void setRutCorgTm(LocalTime rutCorgTm) {
        this.rutCorgTm = rutCorgTm;
    }

    public String getOrgRspCd() {
        return orgRspCd;
    }

    public void setOrgRspCd(String orgRspCd) {
        this.orgRspCd = orgRspCd;
    }

    public String getOrgRspMsg() {
        return orgRspMsg;
    }

    public void setOrgRspMsg(String orgRspMsg) {
        this.orgRspMsg = orgRspMsg;
    }

    public String getChkKey() {
        return chkKey;
    }

    public void setChkKey(String chkKey) {
        this.chkKey = chkKey;
    }

    public String getChkSts() {
        return chkSts;
    }

    public void setChkSts(String chkSts) {
        this.chkSts = chkSts;
    }

    public LocalDate getChkDt() {
        return chkDt;
    }

    public void setChkDt(LocalDate chkDt) {
        this.chkDt = chkDt;
    }

    public LocalTime getChkTm() {
        return chkTm;
    }

    public void setChkTm(LocalTime chkTm) {
        this.chkTm = chkTm;
    }

    public LocalDate getCavDt() {
        return cavDt;
    }

    public void setCavDt(LocalDate cavDt) {
        this.cavDt = cavDt;
    }

    public LocalTime getCavTm() {
        return cavTm;
    }

    public void setCavTm(LocalTime cavTm) {
        this.cavTm = cavTm;
    }

    public String getCavOper() {
        return cavOper;
    }

    public void setCavOper(String cavOper) {
        this.cavOper = cavOper;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }
}