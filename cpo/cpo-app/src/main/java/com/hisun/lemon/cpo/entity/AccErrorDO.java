/*
 * @ClassName ChkAccErrorDO
 * @Description
 * @version 1.0
 * @Date 2017-07-12 19:25:57
 */
package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class AccErrorDO extends BaseDO {
    /**
     * @Fields chkErId 主键
     */
    private String chkErId;
    /**
     * @Fields rutCorg 路径合作机构
     */
    private String rutCorg;
    /**
     * @Fields copBusTyp 业务合作类型
     */
    private String corpBusTyp;
    /**
     * @Fields copBusSubTyp 业务合作子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields errKeyId 差错键值
     */
    private String errKeyId;
    /**
     * @Fields chkErrDt 差错创建日期
     */
    private LocalDate chkErrDt;
    /**
     * @Fields chkErrTm 差错创建时间
     */
    private LocalTime chkErrTm;
    /**
     * @Fields chkBatNo 对账批次
     */
    private String chkBatNo;
    /**
     * @Fields splAbleFlg 补单允许标识，Y允许，N不允许
     */
    private String splAbleFlg;
    /**
     * @Fields canAbleFlg 撤单允许标识，Y允许，N不允许
     */
    private String canAbleFlg;
    /**
     * @Fields chkErrTyp 差错类型，2：短款差错，3：长款差错，4：金额不符

     */
    private String chkErrTyp;
    /**
     * @Fields errSts 差错状态，0：待处理，1：已补单，2：已撤单，3：人工取消

     */
    private String errSts;
    /**
     * @Fields othTxAmt 对方交易金额
     */
    private BigDecimal othTxAmt;
    /**
     * @Fields myTxAmt 我方交易金额
     */
    private BigDecimal myTxAmt;
    /**
     * @Fields psnCrpFlg 个人/商户标识，B商户，C个人
     */
    private String psnCrpFlg;
    /**
     * @Fields oldTxDt 原交易日期
     */
    private LocalDate oldTxDt;
    /**
     * @Fields oldJrnNo 原交易流水号
     */
    private String oldJrnNo;
    /**
     * @Fields oldOrdNo 原交易订单号
     */
    private String oldOrdNo;
    /**
     * @Fields oldCorgKey 原路径合作机构对账主键
     */
    private String oldCorgKey;
    /**
     * @Fields oprId 操作员
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getChkErId() {
        return chkErId;
    }

    public void setChkErId(String chkErId) {
        this.chkErId = chkErId;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getErrKeyId() {
        return errKeyId;
    }

    public void setErrKeyId(String errKeyId) {
        this.errKeyId = errKeyId;
    }

    public LocalDate getChkErrDt() {
        return chkErrDt;
    }

    public void setChkErrDt(LocalDate chkErrDt) {
        this.chkErrDt = chkErrDt;
    }

    public LocalTime getChkErrTm() {
        return chkErrTm;
    }

    public void setChkErrTm(LocalTime chkErrTm) {
        this.chkErrTm = chkErrTm;
    }

    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    public String getSplAbleFlg() {
        return splAbleFlg;
    }

    public void setSplAbleFlg(String splAbleFlg) {
        this.splAbleFlg = splAbleFlg;
    }

    public String getCanAbleFlg() {
        return canAbleFlg;
    }

    public void setCanAbleFlg(String canAbleFlg) {
        this.canAbleFlg = canAbleFlg;
    }

    public String getChkErrTyp() {
        return chkErrTyp;
    }

    public void setChkErrTyp(String chkErrTyp) {
        this.chkErrTyp = chkErrTyp;
    }

    public String getErrSts() {
        return errSts;
    }

    public void setErrSts(String errSts) {
        this.errSts = errSts;
    }

    public BigDecimal getOthTxAmt() {
        return othTxAmt;
    }

    public void setOthTxAmt(BigDecimal othTxAmt) {
        this.othTxAmt = othTxAmt;
    }

    public BigDecimal getMyTxAmt() {
        return myTxAmt;
    }

    public void setMyTxAmt(BigDecimal myTxAmt) {
        this.myTxAmt = myTxAmt;
    }

    public String getPsnCrpFlg() {
        return psnCrpFlg;
    }

    public void setPsnCrpFlg(String psnCrpFlg) {
        this.psnCrpFlg = psnCrpFlg;
    }

    public LocalDate getOldTxDt() {
        return oldTxDt;
    }

    public void setOldTxDt(LocalDate oldTxDt) {
        this.oldTxDt = oldTxDt;
    }

    public String getOldJrnNo() {
        return oldJrnNo;
    }

    public void setOldJrnNo(String oldJrnNo) {
        this.oldJrnNo = oldJrnNo;
    }

    public String getOldOrdNo() {
        return oldOrdNo;
    }

    public void setOldOrdNo(String oldOrdNo) {
        this.oldOrdNo = oldOrdNo;
    }

    public String getOldCorgKey() {
        return oldCorgKey;
    }

    public void setOldCorgKey(String oldCorgKey) {
        this.oldCorgKey = oldCorgKey;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }
}