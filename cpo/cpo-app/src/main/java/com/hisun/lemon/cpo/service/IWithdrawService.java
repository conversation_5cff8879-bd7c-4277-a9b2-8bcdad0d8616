package com.hisun.lemon.cpo.service;

import com.hisun.lemon.cpo.dto.*;
import com.hisun.lemon.cpo.entity.WithdrawOrderDO;
import com.hisun.lemon.cpo.entity.WithdrawParamDO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

import java.util.List;

/**
 * 资金流出业务Service
 */
public interface IWithdrawService {

    /**
     * 增加资金流出订单，返回内部提现订单号
     */
    GenericRspDTO<WithdrawResDTO> createOrder(GenericDTO<WithdrawReqDTO> genericDTO);

    /**
     * 根据主键查询资金流出订单信息
     */
    GenericRspDTO<WdcOrdDetailResDTO> queryOrder(String ordNo);

    /**
     * 提现订单审批，修改审批工作流状态、审批拒绝原因、审批人
     * 根据主键更新资金流出订单信息，取不为null的字段值更新表记录
     */
    boolean approveWithdrawOrder(WithdrawOrderDO withdrawOrderDO);

    /**
     * 提现订单，付款确认或付款失败后的账务处理
     */
    GenericRspDTO<NoBody> payOrder(GenericDTO<WdcProcessReqDTO> genericDTO);

    /**
     * 增加资金流出业务参数
     */
    boolean addWithdrawParam(WithdrawParamDO withdrawParamDO);

    /**
     * 根据主键查询付款业务参数信息
     */
    WithdrawParamDO getWithdrawParamByKey(String busParId);

    /**
     * 根据唯一索引查询付款业务参数信息
     */
    WithdrawParamDO getWithdrawParam(WithdrawParamDO withdrawParamDO);

    /**
     * 根据主键删除付款业务参数信息
     */
    void deleteWithdrawParamByKey(String busParId);

    /**
     * 根据主键更新付款业务参数信息，取不为null的字段值存入表中
     */
    boolean updateWithdrawParamByKey(WithdrawParamDO withdrawParamDO);

    /**
     * 根据唯一索引更新付款业务参数信息，取不为null的字段值存入表中
     */
    boolean updateWithdrawParam(WithdrawParamDO withdrawParamDO);

    /**
     * 分页查询待通知的提现订单
     */
    List<WithdrawOrderDO> getWithdrawOrderToNotify(int beginNum, int maxQueryNum);

    /**
     * 登记营业厅提现订单
     */
    GenericRspDTO<WithdrawResDTO> createHallOrder(GenericDTO<WithdrawHallReqDTO> genericDTO);

}
