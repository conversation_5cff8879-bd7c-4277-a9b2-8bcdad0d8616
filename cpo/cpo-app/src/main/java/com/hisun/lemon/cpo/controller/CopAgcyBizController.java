package com.hisun.lemon.cpo.controller;

import com.hisun.lemon.cpo.dto.CopAgcyBizDTO;
import com.hisun.lemon.cpo.dto.CopAgcyInfoDTO;
import com.hisun.lemon.cpo.service.ICopAgcyBizService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/copAgcyBiz")
public class CopAgcyBizController extends BaseController{

	@Resource
	private ICopAgcyBizService copAgcyBizService;

	@ApiOperation(value="新增合作机构业务",notes="新增合作机构业务")
	@ApiImplicitParams({
		@ApiImplicitParam(name="CORP_ORG_ID",value="合作机构编号",required=true,dataType="String"),
		@ApiImplicitParam(name="CORP_BUS_TYP",value="合作业务类型",required=true,dataType="char"),
		@ApiImplicitParam(name="CORP_BUS_SUB_TYP",value="合作业务子类型",required=true,dataType="char"),
		@ApiImplicitParam(name="BUS_EFF_FLG",value="业务生效标志，0失效，1生效",required=true,dataType="char"),
		@ApiImplicitParam(name="CRE_OPR_ID",value="创建人ID",required=true,dataType="String"),
		@ApiImplicitParam(name="UPD_OPR_ID",value="修改人ID",required=true,dataType="String")
	})
	@ApiResponse(code = 200, message = "新增合作机构业务")
	@PostMapping("/addCopAgcyBiz")
	public GenericRspDTO<NoBody> addCopAgcyBiz(@RequestBody GenericDTO<CopAgcyBizDTO> copAgcyBizDTO) {
		return copAgcyBizService.addCopAgcyBiz(copAgcyBizDTO);
	}

	@ApiOperation(value="查询合作机构业务",notes="查询合作机构业务")
	@GetMapping("/selectByKey")
	public GenericRspDTO<List<CopAgcyInfoDTO>> selectByKey() {
		return copAgcyBizService.getAgcyList();
	}
}
