package com.hisun.lemon.cpo.utils;

import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.framework.utils.LemonUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

public class AccountUtils {

    /**
     * 组织账务对象
     * @param txSts 交易状态
     * @param acTyp 科目
     * @param acNo 交易账号
     * @param capTyp 资金种类
     * @param itmNo 科目号
     * @param txTyp 交易类型
     * @param txAmt 交易金额
     * @param dcFlg 借贷标识
     * @param txJrnNo 记账流水号
     * @param ordNo 请求订单号
     * @param oppAcNo 设置对手账号
     * @param oppCapTyp 设置对手资金属性
     * @param oppUserId 设置对手用户id
     * @param oppUsertyp 设置对手类型
     * @param usrIpAdr IP地址
     * @return
     */
    public static AccountingReqDTO getAccountingReqDTO(String txSts, String acTyp, String acNo, String capTyp, String itmNo, String txTyp,
                                                       BigDecimal txAmt, String dcFlg, String txJrnNo, String ordNo, String oppAcNo, String oppCapTyp, String oppUserId,
                                                       String oppUsertyp, String usrIpAdr){
        AccountingReqDTO accountingReqDTO = new AccountingReqDTO();
        accountingReqDTO.setTxSts(txSts); //正常
        accountingReqDTO.setAcTyp(acTyp); //科目
        accountingReqDTO.setAcNo(acNo); //交易账号
        accountingReqDTO.setCapTyp(capTyp); //现金
        accountingReqDTO.setItmNo(itmNo); //科目号
        accountingReqDTO.setTxTyp(txTyp); //交易类型
        accountingReqDTO.setTxAmt(txAmt); //交易金额
        accountingReqDTO.setDcFlg(dcFlg); //借贷标识
        accountingReqDTO.setTxJrnNo(txJrnNo); //交易流水号
        accountingReqDTO.setTxOrdNo(ordNo); //请求订单号
        accountingReqDTO.setTxOrdDt(LocalDate.now()); //请求订单日期
        accountingReqDTO.setTxOrdTm(LocalTime.now()); //请求订单时间
        accountingReqDTO.setOppAcNo(oppAcNo); //设置对手账号
        accountingReqDTO.setOppCapTyp(oppCapTyp); //设置对手资金属性
        accountingReqDTO.setOppUserId(oppUserId); //设置对手用户id
        accountingReqDTO.setOppUserTyp(oppUsertyp); //设置对手类型
        accountingReqDTO.setUsrIpAdr(usrIpAdr); //IP地址
        return accountingReqDTO;
    }

    /**
     * 根据银行英文缩写，获取银行备付金科目号
     */
    public static String switchBankItemNo(String rutCorg) {
        String bankAccount = null;
        switch(rutCorg) {
            case "ICBC":
                bankAccount = "**********";
                break;
            default:
                bankAccount = "**********";//默认取工行备付金科目号
                break;
        }
        return bankAccount;
    }
}
