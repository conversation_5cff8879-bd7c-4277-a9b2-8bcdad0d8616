package com.hisun.lemon.cpo.dao;

import com.hisun.lemon.cpo.entity.AccControlDO;
import com.hisun.lemon.cpo.entity.AccParamDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 资金模块对账总控表
 */
@Mapper
public interface IAccControlDao extends BaseDao<AccControlDO> {

    /**
     * 根据对账日期、路径合作机构、业务类型、业务子类型查询对账批次信息
     * 检查上一对账日期之前，是否还有未完成的对账批次
     */
    List<AccControlDO> getAccControlListByLastChkDt(AccControlDO accControlDO);

    /**
     * 根据对账日期、路径合作机构、业务类型、业务子类型查询对账批次信息
     */
    AccControlDO getAccControl(@Param("chkDt")LocalDate chkDt, @Param("rutCorg")String rutCorg, @Param("corpBusTyp")String corpBusTyp, @Param("corpBusSubTyp")String corpBusSubTyp);

    /**
     * 查询对账未完成的批次信息
     */
    /**
     * 查询对账未完成的批次信息
     */
    AccControlDO queryUnfinishedAccControl(AccParamDO accParamDO);

}