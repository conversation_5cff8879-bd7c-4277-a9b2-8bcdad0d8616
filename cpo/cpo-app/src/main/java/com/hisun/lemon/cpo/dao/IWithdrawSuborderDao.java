package com.hisun.lemon.cpo.dao;

import com.hisun.lemon.cpo.entity.WithdrawSuborderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Mapper
public interface IWithdrawSuborderDao extends BaseDao<WithdrawSuborderDO> {

    /**
     * 根据订单号查询资金流出子订单
     * 查询条件 ORD_STS='S'(交易成功)、CHK_STS='0'(未对账) OR CHK_STS='5'(存疑)
     */
    WithdrawSuborderDO getWithdrawSuborderByOrdNo(@Param("wcOrdNo")String wdcOrdNo);

    /**
     * 查询我方ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单，(ORD_DT)订单日期小于对账日期
     */
    List<WithdrawSuborderDO> getWithdrawSuborderListByChkStsDoubt(@Param("ordSts")String ordSts, @Param("chkSts")String chkSts, @Param("chkDt")LocalDate chkDt);

    /**
     * 查询我方ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单，(ORD_DT)订单日期等于对账日期
     */
    List<WithdrawSuborderDO> getWithdrawSuborderListByChkStsNotstart(@Param("ordSts")String ordSts, @Param("chkSts")String chkSts, @Param("chkDt")LocalDate chkDt);

    /**
     * 将ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'，(ORD_DT)订单日期小于对账日期
     */
    void updateWithdrawSuborderByChkStsDoubt(@Param("ordSts")String ordSts, @Param("oldChkSts")String oldChkSts, @Param("newChkSts")String newChkSts, @Param("chkDt")LocalDate chkDt, @Param("chkTm")LocalTime chkTm);

    /**
     * 将我方ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)，(ORD_DT)订单日期等于对账日期
     */
    void updateWithdrawSuborderListByChkStsNotstart(@Param("ordSts")String ordSts, @Param("oldChkSts")String oldChkSts, @Param("newChkSts")String newChkSts, @Param("chkDt")LocalDate chkDt, @Param("chkTm")LocalTime chkTm);

    /**
     * 根据对账外键，查询我方提现子订单信息
     */
    WithdrawSuborderDO getWithdrawSuborderByChkKey(@Param("chkKey")String chkKey);

    /**
     * 根据对账外键，更新我方提现子订单对账状态、对账日期
     */
    void updateWithdrawSuborderByChkKey(WithdrawSuborderDO withdrawSuborderDO);
}