package com.hisun.lemon.cpo.dao;

import com.hisun.lemon.cpo.entity.WithdrawParamDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资金流出业务dao
 * <AUTHOR>
 *
 */
@Mapper
public interface IWithdrawParamDao extends BaseDao<WithdrawParamDO> {

    /**
     * 根据唯一索引查询付款业务参数信息
     */
    WithdrawParamDO getWithdrawParam(WithdrawParamDO withdrawParamDO);

    /**
     * 根据唯一索引更新付款业务参数信息，取不为null的字段值存入表中
     */
    int updateWithdrawParam(WithdrawParamDO withdrawParamDO);

}
