package com.hisun.lemon.cpo.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpo.bo.BaseAccChkFilBO;
import com.hisun.lemon.cpo.common.CpoConstants;
import com.hisun.lemon.cpo.common.CpoMsgCd;
import com.hisun.lemon.cpo.dao.*;
import com.hisun.lemon.cpo.entity.*;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.service.ICheckAccControlService;
import com.hisun.lemon.cpo.service.ICheckAccExtendService;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.lemon.jcommon.file.FileUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 对账主控信息service
 */
@Service
public class CheckAccControlServiceImpl extends BaseService implements ICheckAccControlService {

    @Resource
    private IAccParamDao accParamDao;

    @Resource
    private IAccControlDao accControlDao;

    @Resource
    private IAccCfgDao accCfgDao;

    @Resource
    private ICheckAccExtendService checkAccExtendService;

    @Resource
    private IWithdrawOrderDao withdrawOrderDao;

    @Resource
    private IAccUploadCfgDao accUploadCfgDao;

    private static final Logger logger = LoggerFactory.getLogger(CheckAccControlServiceImpl.class);

    /**
     * 增加对账参数
     */
    @Override
    public boolean addChkAccParam(AccParamDO accParamDO) {
        return accParamDao.insert(accParamDO) > 0 ? true : false;
    }

    /**
     * 查询对账批次是否已存在
     */
    @Override
    public AccControlDO queryUnfinishedAccControl(AccParamDO accParamDO) {
        return accControlDao.queryUnfinishedAccControl(accParamDO);
    }

    /**
     * 查询生效的对账参数信息
     */
    @Override
    public List<AccParamDO> queryEffAccParamList() {
        List<AccParamDO> accParamDOList = accParamDao.queryEffAccParamList();
        logger.debug("==================生效的对账参数记录数：" + accParamDOList.size());
        return accParamDOList.size() > 0 ? accParamDOList : null;
    }

    /**
     * 根据对账参数信息，生成对账批次信息，插入到对账主控表中
     */
    @Override
    @Transactional
    public void registerChkBatNo(LocalDate checkDt) {
        try {
            String chkBatNo = null;
            String rutCorg = null;
            String corpBusTyp = null;
            String corpBusSubTyp = null;
            AccControlDO accControlDO = null;

            //查询有效的对账参数信息
            List<AccParamDO> AccParamList = accParamDao.queryEffAccParamList();

            //根据对账参数信息，创建对账批次信息
            if (CollectionUtils.isNotEmpty(AccParamList)) {
                for (AccParamDO accParamDO : AccParamList) {

                    //检查是否已生成当天的对账批次
                    rutCorg = accParamDO.getRutCorg();
                    corpBusTyp = accParamDO.getCorpBusTyp();
                    corpBusSubTyp = accParamDO.getCorpBusSubTyp();
                    accControlDO = accControlDao.getAccControl(checkDt, rutCorg, corpBusTyp, corpBusSubTyp);
                    if (JudgeUtils.isNotNull(accControlDO)) {
                        logger.debug("==================机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 对账日期: " + DateTimeUtils.formatLocalDate(checkDt) + " 对账批次已存在");
                        continue;
                    }

                    //生成对账批次号
                    chkBatNo = IdGenUtils.generateIdWithDateTime("CHK", "CHK", 3);

                    //插入对账批次信息
                    accControlDO = new AccControlDO();
                    BeanUtils.copyProperties(accControlDO, accParamDO);
                    accControlDO.setChkBatNo(chkBatNo);
                    accControlDO.setChkDt(checkDt);
                    accControlDO.setChkFilDt(checkDt);
                    accControlDO.setChkFilSts(CpoConstants.CHK_FIL_NOT_START);
                    accControlDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
                    accControlDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                    accControlDO.setTmSmp(DateTimeUtils.getCurrentLocalDateTime());
                    accControlDao.insert(accControlDO);
                }
            }
        } catch (Exception e) {
            logger.error("CheckMgrServiceImpl.registerChkBatNo()，生成对账批次失败，异常为: ", e);
            throw new LemonException(CpoMsgCd.CREATE_CHK_BAT_FAIL.getMsgCd());
        }
    }

    /**
     * 开始执行对账服务
     */
    @Override
    public void beginCheckAcc(AccControlDO accControlDO) {
        String chkBatNo = accControlDO.getChkBatNo();//对账批次号
        LocalDate chkDt = accControlDO.getChkDt();//当前对账日期
        String rutCorg = accControlDO.getRutCorg();//路径合作机构
        String corpBusTyp = accControlDO.getCorpBusTyp();//合作业务类型
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();//合作业务子类型

        //锁ID，前缀_路由合作机构_合作业务子类型：CHK_ICBC_02_0201
        String lockId = CpoConstants.CHK_LOCK_PREFIX + rutCorg + "_" + corpBusTyp + "_" + corpBusSubTyp;
        logger.debug("调起对账服务，对账锁ID： " + lockId + "; 对账日期：" + DateTimeUtils.formatLocalDate(chkDt));

        //对账流程
        try {
//            //step1:判断该路径合作机构的业务子类型的对账交易是否已加锁
//            String lockSts = checkAccExtendService.getLockSts(lockId);
//
//            //状态为空，说明没有配置对账交易锁
//            if (StringUtils.isBlank(lockSts)) {
//                logger.error("==================机构: " + rutCorg + "; 业务: " + corpBusSubTyp + " 没有配置锁");
//                return;
//            }
//            //交易已加锁，直接退出
//            if (CpoConstants.LOCK_STATUS_LOCKED.equals(lockSts)) {
//                logger.error("==================机构: " + rutCorg + "; 业务: " + corpBusSubTyp + " 对账流程正在处理中");
//                return;
//            }
//
//            //step2:对账前先加锁
//            LockDO lockDO = new LockDO();
//            lockDO.setLockId(lockId);
//            lockDO.setLockSts(CpoConstants.LOCK_STATUS_LOCKED);
//            lockDO.setTmSmp(DateTimeUtils.getCurrentLocalDateTime());
//            checkAccExtendService.updateLockSts(lockDO);

            //step3:查询出对账配置表中的配置信息
            AccCfgDO accCfgDO = accCfgDao.getAccCfgDO(rutCorg, corpBusTyp, corpBusSubTyp);
            if (JudgeUtils.isNull(accCfgDO)) {
                logger.error("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + " 没有添加银行对账配置信息");
                return ;
            }

            //step4:开始对账
            logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 开始对账");
            String chkFilSts = accControlDO.getChkFilSts();
            switch (chkFilSts) {
                //0：未对账，开始获取对账文件
                case CpoConstants.CHK_FIL_NOT_START:
                    chkFilSts = checkAccExtendService.getOrgChkFile(accControlDO, accCfgDO);
                    if(!CpoConstants.CHK_FIL_DOWNLOAD.equals(chkFilSts)){
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 获取对账文件异常");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 获取对账文件完成");

                    //1：已获取对账文件，解析对账文件，批量导入数据库
                case CpoConstants.CHK_FIL_DOWNLOAD:
                    chkFilSts = checkAccExtendService.importOrgChkFile(accControlDO, accCfgDO);
                    if(!CpoConstants.CHK_FIL_IMPORT.equals(chkFilSts)){
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账入库出错");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账明细导入数据库完成");

                    //2：文件已入库，开始对账
                case CpoConstants.CHK_FIL_IMPORT:
                    chkFilSts = checkAccExtendService.checkFile(accControlDO, accCfgDO);
                    if(!CpoConstants.CHK_FIL_FINISHED.equals(chkFilSts)){
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 明细对账失败");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账结束");

                    //4：对账完成
                case CpoConstants.CHK_FIL_FINISHED:
                    break;
                default:
                    break;
            }

            //step5:根据业务类型，调用账务处理方法
        } catch (Exception e) {
            logger.error("CheckMgrServiceImpl.beginCheckAcc()，机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账失败! 异常为: ", e);
            return;
        } finally {
//            //对账结束或抛出异常，更新锁状态为未锁定
//            LockDO lockDO = new LockDO();
//            lockDO.setLockId(lockId);
//            lockDO.setLockSts(CpoConstants.LOCK_STATUS_UNLOCK);
//            lockDO.setTmSmp(DateTimeUtils.getCurrentLocalDateTime());
//            checkAccExtendService.updateLockSts(lockDO);
        }
    }

    @Override
    public void writeCheckFile(LocalDate chkFilDt, String rutCorg, CorpBusSubTyp corpBusSubTyp, String ordSts) {
        //获取文件上传服务器参数
        AccUploadCfgDO accUploadCfgDO = accUploadCfgDao.getByUnique(rutCorg);

        //step1:检测本地是否有文件，有则退出
        //对账文件名
        String chkFilNm = null;
        if(CpoConstants.ORD_SUCCESS.equals(ordSts)) {
            chkFilNm = "CPO_WITHDRAW_" + corpBusSubTyp.getType() + "_" + DateTimeUtils.formatLocalDate(chkFilDt) + ".dat";
        }
        if(CpoConstants.ORD_FAIL.equals(ordSts)) {
            chkFilNm = "CPO_WITHDRAW_FAIL_" + corpBusSubTyp.getType() + "_" + DateTimeUtils.formatLocalDate(chkFilDt) + ".dat";
        }
        String filePathAndName = accUploadCfgDO.getLocalFilePath() + File.separator + chkFilNm;
        File file = new File(filePathAndName);
        if (file.exists()) {
            logger.debug("本地已存有对账文件 " + chkFilNm);
            return;
        }

        //创建一个新对账文件
        try {
            file.createNewFile();
        } catch (IOException e) {
            logger.error("创建对账文件失败，", e);
        }

        //step2:查询出来ordDt等于chkFilDt的订单,写入对账文件
        int beginNum = 0;
        int countNum = 200;
        int i = 0;
        StringBuilder stringBuilder = null;
        List<BaseAccChkFilBO> chkFilBOList = null;
        logger.debug("开始生成对账文件 " + chkFilNm);
        while (true) {
            beginNum = countNum * i;
            chkFilBOList = new ArrayList<>();
            List<WithdrawOrderDO> withdrawOrderDOList = withdrawOrderDao.getWithdrawOrderList(chkFilDt, beginNum, countNum, corpBusSubTyp.getType(), ordSts);

            if (CollectionUtils.isNotEmpty(withdrawOrderDOList)) {
                for (WithdrawOrderDO withdrawOrderDO : withdrawOrderDOList) {
                    BaseAccChkFilBO bo = new BaseAccChkFilBO();
                    bo.setChkKey(withdrawOrderDO.getWcOrdNo());
                    bo.setChkAmt(withdrawOrderDO.getWcAplAmt());
                    bo.setChkSts(withdrawOrderDO.getOrdSts());
                    bo.setRequestId(withdrawOrderDO.getReqOrdNo());
                    chkFilBOList.add(bo);
                }
            }

            if (CollectionUtils.isNotEmpty(chkFilBOList)) {
                //准备数据
                stringBuilder = new StringBuilder("");
                for (BaseAccChkFilBO bo : chkFilBOList) {
                    stringBuilder.append(bo.getChkKey())//对账key值
                            .append("|").append(bo.getChkAmt())//对账金额
                            .append("|").append(bo.getChkSts())//订单状态
                            .append("|").append(bo.getRequestId())//请求订单号
                            .append("\n");
                }
            } else {
                //查无对账明细数据，退出循环
                break;
            }

            //写入对账文件，失败的话，删除文件
            try {
                FileUtils.writeAppend(stringBuilder.toString(), filePathAndName);
            } catch (Exception e) {
                logger.error("写入个人提现对账文件失败：", e);
                return;
            }

            //分页查询，页数自增
            i++;
        }

        //step3：上传对账文件，失败删除本地对账文件
        String uploadIp = accUploadCfgDO.getUploadIp();
        int uploadPort = Integer.parseInt(accUploadCfgDO.getUploadPort());
        int connectTime = accUploadCfgDO.getConnectTime();
        String uploadPath = accUploadCfgDO.getUploadPath();
        String uploadNm = accUploadCfgDO.getUploadNm();
        String uploadPass = accUploadCfgDO.getUploadPwd();

        //上传文件
        try {
            logger.debug("开始上传对账文件 " + chkFilNm);
            FileSftpUtils.upload(filePathAndName, uploadIp, uploadPort, connectTime, uploadPath, uploadNm, uploadPass);
            logger.debug("成功上传对账文件 " + chkFilNm);
        } catch (Exception e) {
            logger.error("上传失败：", e);
            file.delete();
            return;
        }
    }

}
