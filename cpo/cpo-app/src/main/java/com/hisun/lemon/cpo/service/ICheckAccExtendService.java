package com.hisun.lemon.cpo.service;

import com.hisun.lemon.cpo.bo.BaseAccWithdrawBO;
import com.hisun.lemon.cpo.entity.*;

/**
 * 资金模块对账扩展服务 Service
 */
public interface ICheckAccExtendService {

    /**
     * 查询对账锁状态
     */
    String getLockSts(String lockId);

    /**
     * 更新对账锁状态
     */
    void updateLockSts(LockDO lockDO);

    /**
     * 添加对账锁信息
     */
    void addLockInfo(LockDO lockDO);

    /**
     * 对账第一步：获取银行机构对账文件
     */
    String getOrgChkFile(AccControlDO accControlDO, AccCfgDO accCfgDO) throws Exception;

    /**
     * 对账第二步：解析银行对账文件
     */
    String importOrgChkFile(AccControlDO accControlDO, AccCfgDO accCfgDO) throws Exception;

    /**
     * 对账第三步：对账，以银行成功的对账明细为准
     */
    String checkFile(AccControlDO accControlDO, AccCfgDO accCfgDO) throws Exception;

}
