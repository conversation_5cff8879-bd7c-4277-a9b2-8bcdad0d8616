package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资金机构路由信息
 */
public class CopBizRouteDO extends BaseDO {
	/**
	 * @Fields rutInfId 主键
	 */
	private String rutInfId;
	/**
	 * @Fields crdCorpOrg 资金合作机构编号
	 */
	private String crdCorpOrg;
	/**
	 * @Fields corpBusTyp 合作业务类型
	 */
	private String corpBusTyp;
	/**
	 * @Fields corpBusSubTyp 合作业务子类型
	 */
	private String corpBusSubTyp;
	/**
	 * @Fields rutCorpOrg 路径合作机构号
	 */
	private String rutCorpOrg;
	/**
	 * @Fields crdAcTyp 银行卡类型，D借记卡，C贷记卡
	 */
	private String crdAcTyp;
	/**
	 * @Fields rutEffFlg 路由生效标志，0失效，1生效
	 */
	private String rutEffFlg;
	/**
	 * @Fields priLvl 0-99，数字越大级别越高
	 */
	private Integer priLvl;
	/**
	 * @Fields lowAmt 最低金额
	 */
	private BigDecimal lowAmt;
	/**
	 * @Fields highAmt 最高金额
	 */
	private BigDecimal highAmt;
	/**
	 * @Fields oprId 创建柜员ID
	 */
	private String oprId;
	/**
	 * @Fields rmk 备注
	 */
	private String rmk;
	/**
	 * @Fields tmSmp 时间戳
	 */
	private LocalDateTime tmSmp;

	public String getRutInfId() {
		return rutInfId;
	}

	public void setRutInfId(String rutInfId) {
		this.rutInfId = rutInfId;
	}

	public String getCrdCorpOrg() {
		return crdCorpOrg;
	}

	public void setCrdCorpOrg(String crdCorpOrg) {
		this.crdCorpOrg = crdCorpOrg;
	}

	public String getCorpBusTyp() {
		return corpBusTyp;
	}

	public void setCorpBusTyp(String corpBusTyp) {
		this.corpBusTyp = corpBusTyp;
	}

	public String getCorpBusSubTyp() {
		return corpBusSubTyp;
	}

	public void setCorpBusSubTyp(String corpBusSubTyp) {
		this.corpBusSubTyp = corpBusSubTyp;
	}

	public String getRutCorpOrg() {
		return rutCorpOrg;
	}

	public void setRutCorpOrg(String rutCorpOrg) {
		this.rutCorpOrg = rutCorpOrg;
	}

	public String getCrdAcTyp() {
		return crdAcTyp;
	}

	public void setCrdAcTyp(String crdAcTyp) {
		this.crdAcTyp = crdAcTyp;
	}

	public String getRutEffFlg() {
		return rutEffFlg;
	}

	public void setRutEffFlg(String rutEffFlg) {
		this.rutEffFlg = rutEffFlg;
	}

	public Integer getPriLvl() {
		return priLvl;
	}

	public void setPriLvl(Integer priLvl) {
		this.priLvl = priLvl;
	}

	public BigDecimal getLowAmt() {
		return lowAmt;
	}

	public void setLowAmt(BigDecimal lowAmt) {
		this.lowAmt = lowAmt;
	}

	public BigDecimal getHighAmt() {
		return highAmt;
	}

	public void setHighAmt(BigDecimal highAmt) {
		this.highAmt = highAmt;
	}

	public String getOprId() {
		return oprId;
	}

	public void setOprId(String oprId) {
		this.oprId = oprId;
	}

	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	public LocalDateTime getTmSmp() {
		return tmSmp;
	}

	public void setTmSmp(LocalDateTime tmSmp) {
		this.tmSmp = tmSmp;
	}
}