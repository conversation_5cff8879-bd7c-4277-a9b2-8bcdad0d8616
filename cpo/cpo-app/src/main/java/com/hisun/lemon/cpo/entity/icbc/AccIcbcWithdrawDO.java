package com.hisun.lemon.cpo.entity.icbc;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 资金流出工行提现对账明细表
 * 表名：cpo_acc_icbc_withdraw
 */
public class AccIcbcWithdrawDO extends BaseDO {
    /**
     * @Fields chkId 对账明细序号主键
     */
    private String chkId;

    /**
     * @Fields companyDate 公司方日期
     */
    private String companyDate;

    /**
     * @Fields companySerino 公司方交易流水号(同机构交易流水号)
     */
    private String companySerino;

    /**
     * @Fields bankSerno 银行流水号
     */
    private String bankSerno;

    /**
     * @Fields settleDate 清算日期，格式：YYYYMMDD
     */
    private String settleDate;

    /**
     * @Fields cardNo 银行卡号
     */
    private String cardNo;

    /**
     * @Fields payAmt 银行订单金额(单位:元)
     */
    private BigDecimal payAmt;

    /**
     * @Fields currType 币种
     */
    private String currType;

    /**
     * @Fields status 明细状态 2-成功; 3-失败
     */
    private String status;

    /**
     * @Fields chkBatNo 对账批次号
     */
    private String chkBatNo;

    /**
     * @Fields rutCorg 路径合作机构
     */
    private String rutCorg;

    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;

    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;

    /**
     * @Fields chkFilDt 对账文件日期
     */
    private LocalDate chkFilDt;

    /**
     * @Fields chkFilNm 对账文件名称
     */
    private String chkFilNm;

    /**
     * @Fields chkSts 对账状态，0：未对账，1：对账成功，2：我方有对方无，
     * 3：对方有我方无，4：金额错，5：存疑
     */
    private String chkSts;

    /**
     * tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getChkId() {
        return chkId;
    }

    public void setChkId(String chkId) {
        this.chkId = chkId;
    }

    public String getCompanyDate() {
        return companyDate;
    }

    public void setCompanyDate(String companyDate) {
        this.companyDate = companyDate;
    }

    public String getCompanySerino() {
        return companySerino;
    }

    public void setCompanySerino(String companySerino) {
        this.companySerino = companySerino;
    }

    public String getBankSerno() {
        return bankSerno;
    }

    public void setBankSerno(String bankSerno) {
        this.bankSerno = bankSerno;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public BigDecimal getPayAmt() {
        return payAmt;
    }

    public void setPayAmt(BigDecimal payAmt) {
        this.payAmt = payAmt;
    }

    public String getCurrType() {
        return currType;
    }

    public void setCurrType(String currType) {
        this.currType = currType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public LocalDate getChkFilDt() {
        return chkFilDt;
    }

    public void setChkFilDt(LocalDate chkFilDt) {
        this.chkFilDt = chkFilDt;
    }

    public String getChkFilNm() {
        return chkFilNm;
    }

    public void setChkFilNm(String chkFilNm) {
        this.chkFilNm = chkFilNm;
    }

    public String getChkSts() {
        return chkSts;
    }

    public void setChkSts(String chkSts) {
        this.chkSts = chkSts;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}