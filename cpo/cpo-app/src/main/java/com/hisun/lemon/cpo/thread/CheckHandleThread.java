package com.hisun.lemon.cpo.thread;

import com.hisun.lemon.cpo.entity.AccControlDO;
import com.hisun.lemon.cpo.service.ICheckAccControlService;
import com.hisun.lemon.framework.lock.DistributedLocker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 对账服务线程
 */
public class CheckHandleThread implements Runnable {

    /**
     * 对账批次信息 DO
     */
    private AccControlDO accControlDO;

    private ICheckAccControlService chkAccControlService;

    private DistributedLocker distributedLocker;

    private static final Logger logger = LoggerFactory.getLogger(CheckHandleThread.class);

    /**
     * 线程中不能用注解来注入service服务等
     */
    public CheckHandleThread(AccControlDO accControlDO, ICheckAccControlService chkAccControlService, DistributedLocker distributedLocker) {
        this.accControlDO = accControlDO;
        this.chkAccControlService = chkAccControlService;
        this.distributedLocker = distributedLocker;
    }

    /**
     * 执行对账服务，传入对象参数DO
     */
    @Override
    public void run() {
        try {
            //异步调起对账
            //锁名
            String lockName = "CHK" + accControlDO.getRutCorg() + "_" + accControlDO.getCorpBusTyp() + "_" + accControlDO.getCorpBusSubTyp();

            //释放锁的时间（异常情况下最长的释放锁的时间，单位秒）
            int leaseTime = 75*60*60;

            //获取应用锁的时间，时间设置必须短，否则影响后面的线程获取锁（单位秒）
            int waitTime = 10;

            //分布式系统加锁
            distributedLocker.lock(lockName, leaseTime, waitTime, () -> {
                chkAccControlService.beginCheckAcc(accControlDO);
                return null;
            });
        } catch (Exception e) {
            logger.error("对账定时任务执行失败，异常为 " + e);
            return ;
        }
    }
}
