package com.hisun.lemon.cpo.bankapi.withdraw;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpo.common.CpoMsgCd;
import com.hisun.lemon.cpo.dao.icbc.IAccIcbcWithdrawDao;
import com.hisun.lemon.cpo.entity.icbc.AccIcbcWithdrawDO;
import com.hisun.lemon.cpo.entity.AccControlDO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 工行提现业务对账操作 API
 */
@Component
public class CheckAccICBCWithdrawApi {

    @Resource
    IAccIcbcWithdrawDao accIcbcWithdrawDao;

    private static final Logger logger = LoggerFactory.getLogger(CheckAccICBCWithdrawApi.class);

    /**
     * 1.1 工行快捷退款对账：获取对账文件
     */
    public String getCheckFile(String chkFilPath, LocalDate chkDt) throws Exception{
        //对账文件本地系统存放路径
        String chkDtStr = DateTimeUtils.formatLocalDate(chkDt);
        String chkFilNm = "ICBC_06_0601_" + chkDtStr + ".txt";
        String chkfileFullPath = chkFilPath + File.separator + chkFilNm;
        logger.debug("==================银行对账文件全路径: " + chkfileFullPath);

        //step1：获取银行对账文件，可能是SFTP下载、请求接口下载等

        //step2：若文件不存在或文件内容为空，则下载文件异常
        File file = new File(chkfileFullPath);
        if (!file.exists() || (file.exists() && file.length() <= 0)) {
            logger.error("CheckAccICBCRefundApi.getCheckFile()，工行提现对账文件不存在，或对账文件内容为空，对账日期：" + chkDtStr);
            return null;
        }
        return chkFilNm;
    }

    /**
     * 1.2 工行快捷退款对账：解析对账文件，对账明细批量入库
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void batchInsertCheckDO(List<Object> checkDOList, AccControlDO accControlDO) {
        logger.debug("==================工行提现对账，开始解析对账文件==================");

        //step1：根据对账批次号查询一条记录，判断该批次是否已插入对账明细数据
        String chkBatNo = accControlDO.getChkBatNo();
        int totalNum = accIcbcWithdrawDao.getAccIcbcWithdrawDOCount(chkBatNo);
        if (totalNum > 0) {
            logger.error("该批次已录入银行对账明细，请重新确认，对账批次号: " + chkBatNo);
            throw new LemonException(CpoMsgCd.IMPORT_CHECK_FILE_REPEATED.getMsgCd());
        }

        //step2：批次插入银行对账明细
        AccIcbcWithdrawDO accIcbcWithdrawDO = null;
        List<AccIcbcWithdrawDO> withdrawDOList = new ArrayList<>();
        Integer filTotCnt = 0;
        BigDecimal filTotAmt = BigDecimal.ZERO;
        String chkId = null;

        if (CollectionUtils.isNotEmpty(checkDOList)) {
            //循环获取文件中，交易类型为 3-退款(快捷退款) 的对账明细，并统计对账总金额和总笔数
            for (Object checkAccDO : checkDOList) {
                accIcbcWithdrawDO = (AccIcbcWithdrawDO)checkAccDO;
                chkId = IdGenUtils.generateIdWithDateTime("CPI", 6);
                accIcbcWithdrawDO.setChkId(chkId);
                withdrawDOList.add(accIcbcWithdrawDO);
                filTotAmt = filTotAmt.add(accIcbcWithdrawDO.getPayAmt());//金额以元为单位
                filTotCnt++;
            }

            //记录数大于200，分批量进行插入
            if (withdrawDOList.size() > 200) {
                List<AccIcbcWithdrawDO> tmpList = new ArrayList<>();
                for (AccIcbcWithdrawDO checkAccDO : withdrawDOList) {
                    if (tmpList.size() == 200) {
                        accIcbcWithdrawDao.batchInsertCheckDO(tmpList);
                        tmpList.clear();
                    }
                    tmpList.add(checkAccDO);
                }
                if (tmpList.size() > 0) {
                    accIcbcWithdrawDao.batchInsertCheckDO(tmpList);
                }
            } else {
                //记录数小于200，直接批量插入
                accIcbcWithdrawDao.batchInsertCheckDO(withdrawDOList);
            }
        }
        //更新文件总笔数和总金额
        accControlDO.setFilTotAmt(filTotAmt);
        accControlDO.setFilTotCnt(filTotCnt);
    }

    /**
     * 1.3 工行快捷退款对账：获取银行对账明细 List
     * @param chkBatNo 对账批次号
     * @param txSts 银行明细交易状态
     * @param beginNum 从表中第几行开始取值
     * @param countNum 最大读取多少行记录
     */
    public List<AccIcbcWithdrawDO> getAccDOList(String chkBatNo, String txSts, Integer beginNum, Integer countNum){
        return accIcbcWithdrawDao.getAccIcbcWithdrawDOList(chkBatNo, txSts, beginNum, countNum);
    }

    /**
     * 1.4 工行快捷退款对账：更新银行明细的对账状态
     * @param checkKey 银行明细的主键，取机构的交易流水号
     * @param txSts 银行明细的交易状态
     * @param chkSts 银行明细的对账状态
     */
    public void updateAccDOChkSts(String checkKey,String txSts,String chkSts){
        accIcbcWithdrawDao.updateAccIcbcWithdrawDOChkSts(checkKey,txSts,chkSts);
    }
}
