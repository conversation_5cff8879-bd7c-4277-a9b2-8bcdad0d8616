package com.hisun.lemon.cpo.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpo.common.CpoConstants;
import com.hisun.lemon.cpo.entity.WithdrawOrderDO;
import com.hisun.lemon.cpo.utils.EncryptUtils;
import com.hisun.lemon.csm.constants.IConstants;
import com.hisun.lemon.csm.dto.SettleConfirmReqDTO;
import com.hisun.lemon.framework.stream.MultiOutput;
import com.hisun.lemon.framework.stream.producer.Producer;
import com.hisun.lemon.framework.stream.producer.Producers;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.pwm.dto.WithdrawResultDTO;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tam.dto.ResultTransferOrderDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * User : Rui
 * Date : 2017/8/28
 * Time : 11:45
 **/
@Component
public class WithdrawNotifyProduce {

    private static final Logger logger = LoggerFactory.getLogger(WithdrawNotifyProduce.class);

    @Resource
    private EncryptUtils encryptUtils;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 充提模块，提现订单结果异步通知
     */
    @Producers({
            @Producer(beanName="noticeHandler", channelName= MultiOutput.OUTPUT_DEFAULT)
    })
    public WithdrawResultDTO pwmWithdrawNotify(WithdrawOrderDO withdrawOrderDO) {
        //通知消息
        WithdrawResultDTO withdrawResultDTO = new WithdrawResultDTO();
        withdrawResultDTO.setOrderNo(withdrawOrderDO.getReqOrdNo());
        withdrawResultDTO.setAcTm(withdrawOrderDO.getAcDt());
        withdrawResultDTO.setOrderStatus(withdrawOrderDO.getOrdSts());
        withdrawResultDTO.setWcActAmt(withdrawOrderDO.getWcAplAmt());
        withdrawResultDTO.setRspOrderNo(withdrawOrderDO.getWcOrdNo());
        withdrawResultDTO.setWcRemark(withdrawOrderDO.getRmk());

        //获取银行卡号后四位
        String crdNoEnc = withdrawOrderDO.getCrdNoEnc();
        String crdNo = crdNoEnc;
//        try {
//            crdNo = encryptUtils.encrypt(crdNoEnc,CpoConstants.DECRYPT);
//        } catch (Exception e) {
//            logger.error("WithdrawNotifyProduce.pwmWithdrawNotify()解密银行卡异常：",e);
//        }
        if(JudgeUtils.isNotEmpty(crdNo)) {
            withdrawResultDTO.setCardNoLast(crdNo.substring(crdNo.length() - 4));
        }
        String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, withdrawResultDTO, true);
        logger.info("WithdrawNotifyProduce.pwmWithdrawNotify()充提模块，提现订单结果异步通知：" + rspData);
        return withdrawResultDTO;
    }

    /**
     * 转账模块，提现订单结果异步通知
     */
    @Producers({
            @Producer(beanName="transferResultHandler", channelName= MultiOutput.OUTPUT_TWO)
    })
    public ResultTransferOrderDTO tamWithdrawNotify(WithdrawOrderDO withdrawOrderDO) {
        //通知消息
        ResultTransferOrderDTO resultTransferOrderDTO = new ResultTransferOrderDTO();
        resultTransferOrderDTO.setOrderNo(withdrawOrderDO.getReqOrdNo());
        resultTransferOrderDTO.setAmount(withdrawOrderDO.getWcAplAmt());
        resultTransferOrderDTO.setTxType(withdrawOrderDO.getCorpBusTyp());
        resultTransferOrderDTO.setBusType(withdrawOrderDO.getCorpBusSubTyp());
        resultTransferOrderDTO.setOrderCcy(withdrawOrderDO.getCcy());

        //判断订单状态
        if (JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.ORD_SUCCESS)) {
            resultTransferOrderDTO.setOrderSts(TamConstants.ORD_STS_S);
        } else {
            resultTransferOrderDTO.setOrderSts(TamConstants.ORD_STS_F);
        }
        String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, resultTransferOrderDTO, true);
        logger.info("WithdrawNotifyProduce.tamWithdrawNotify()转账模块，提现订单结果异步通知：" + rspData);
        return resultTransferOrderDTO;
    }

    /**
     * 结算模块，提现订单结果异步通知
     */
    @Producers({
            @Producer(beanName="settleConfirmConsumer", channelName= MultiOutput.OUTPUT_THREE)
    })
    public SettleConfirmReqDTO csmWithdrawNotify(WithdrawOrderDO withdrawOrderDO) {
        //通知消息
        SettleConfirmReqDTO settleConfirmReqDTO = new SettleConfirmReqDTO();
        settleConfirmReqDTO.setOrderNo(withdrawOrderDO.getWcOrdNo());
        settleConfirmReqDTO.setMessage(withdrawOrderDO.getWcRmk());

        //判断订单状态
        if (JudgeUtils.equals(withdrawOrderDO.getOrdSts(), CpoConstants.ORD_SUCCESS)) {
            settleConfirmReqDTO.setStats(IConstants.ORDER_STATUS_SUCCESS);
        } else {
            settleConfirmReqDTO.setStats(IConstants.ORDER_STATUS_FAILURE);
        }
        String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, settleConfirmReqDTO, true);
        logger.info("WithdrawNotifyProduce.csmWithdrawNotify()结算模块，提现订单结果异步通知：" + rspData);
        return settleConfirmReqDTO;
    }

    /**
     * 充提模块，数币提现订单结果异步通知
     * @param withdrawOrderDO
     */
    @Producers({
            @Producer(beanName="noticeHandler", channelName= MultiOutput.OUTPUT_DEFAULT)
    })
    public WithdrawResultDTO pwmDmWithdrawNotify(WithdrawOrderDO withdrawOrderDO) {
        //通知消息
        WithdrawResultDTO withdrawResultDTO = new WithdrawResultDTO();
        withdrawResultDTO.setOrderNo(withdrawOrderDO.getReqOrdNo());
        withdrawResultDTO.setAcTm(withdrawOrderDO.getAcDt());
        withdrawResultDTO.setOrderStatus(withdrawOrderDO.getOrdSts());
        withdrawResultDTO.setWcActAmt(withdrawOrderDO.getWcAplAmt());
        withdrawResultDTO.setRspOrderNo(withdrawOrderDO.getWcOrdNo());
        withdrawResultDTO.setWcRemark(withdrawOrderDO.getRmk());

        //获取银行卡号后四位
        String crdNoEnc = withdrawOrderDO.getCrdNoEnc();
        String crdNo = crdNoEnc;
//        try {
//            crdNo = encryptUtils.encrypt(crdNoEnc,CpoConstants.DECRYPT);
//        } catch (Exception e) {
//            logger.error("WithdrawNotifyProduce.pwmWithdrawNotify()解密银行卡异常：",e);
//        }
        if(JudgeUtils.isNotEmpty(crdNo)) {
            withdrawResultDTO.setCardNoLast(crdNo.substring(crdNo.length() - 4));
        }
        String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, withdrawResultDTO, true);
        logger.info("WithdrawNotifyProduce.pwmDmWithdrawNotify()充提模块，数币提现订单结果异步通知：" + rspData);
        return withdrawResultDTO;
    }
}
