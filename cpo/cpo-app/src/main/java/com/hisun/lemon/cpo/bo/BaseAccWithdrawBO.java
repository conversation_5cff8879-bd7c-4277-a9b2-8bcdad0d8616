package com.hisun.lemon.cpo.bo;

import com.hisun.lemon.cpo.entity.WithdrawSuborderDO;

import java.util.ArrayList;
import java.util.List;

/**
 * 对账主控服务差异记录扩展，针对提现业务
 */
public class BaseAccWithdrawBO {

    /**
     * 我方有机构无的差异记录，保存子订单信息
     */
    private List<WithdrawSuborderDO> platExistList;

    /**
     * 机构有我方无的差异记录，保存银行对账明细
     */
    private List<Object> orgExistList;

    /**
     * 金额不相等的差异记录，保存银行对账明细
     */
    private List<Object> amtErrorList;

    /**
     * 对账成功，保存银行对账明细
     */
    private List<Object> chkSuccList;

    /**
     * 初始化
     */
    public BaseAccWithdrawBO() {
        platExistList = new ArrayList<>();
        orgExistList = new ArrayList<>();
        amtErrorList = new ArrayList<>();
        chkSuccList = new ArrayList<>();
    }

    public List<WithdrawSuborderDO> getPlatExistList() {
        return platExistList;
    }

    public void setPlatExistList(List<WithdrawSuborderDO> platExistList) {
        this.platExistList = platExistList;
    }

    public List<Object> getOrgExistList() {
        return orgExistList;
    }

    public void setOrgExistList(List<Object> orgExistList) {
        this.orgExistList = orgExistList;
    }

    public List<Object> getAmtErrorList() {
        return amtErrorList;
    }

    public void setAmtErrorList(List<Object> amtErrorList) {
        this.amtErrorList = amtErrorList;
    }

    public List<Object> getChkSuccList() {
        return chkSuccList;
    }

    public void setChkSuccList(List<Object> chkSuccList) {
        this.chkSuccList = chkSuccList;
    }

    public void addAllPlatExistList(List<WithdrawSuborderDO> platExistList) {
        this.platExistList.addAll(platExistList);
    }

    public void addAllOrgExistList(List<Object> orgExistList) {
        this.orgExistList.addAll(orgExistList);
    }

    public void addAllAmtErrorList(List<Object> amtErrorList) {
        this.amtErrorList.addAll(amtErrorList);
    }

    public void addAllChkSuccList(List<Object> chkSuccList) {
        this.chkSuccList.addAll(chkSuccList);
    }
}
