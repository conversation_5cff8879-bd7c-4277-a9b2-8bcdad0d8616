package com.hisun.lemon.cpo.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

/**
 * 资金机构基本信息
 */
public class CopAgcyInfoDO extends BaseDO {
	/**
	 * @Fields orgInfId 主键
	 */
	private String orgInfId;

	/**
	 * @Fields corpOrgId 合作机构编号
	 */
	private String corpOrgId;

	/**
	 * @Fields corpOrgNm 合作机构名称
	 */
	private String corpOrgNm;

	/**
	 * @Fields corpOrgSnm 合作机构名简称
	 */
	private String corpOrgSnm;

	/**
	 * @Fields corpOrgTyp 合作机构类型，0银行，1非银行
	 */
	private String corpOrgTyp;

	/**
	 * @Fields creOprId 创建柜员ID
	 */
	private String creOprId;

	/**
	 * @Fields updOprId 修改柜员ID
	 */
	private String updOprId;

	/**
	 * 合作机构账户名
	 */
	private String corpAccNm;

	/**
	 * 合作机构卡号
	 */
	private String corpAccNo;

	/**
	 * 备注
	 */
	private String rmk;

	/**
	 * @Fields tmSmp 时间戳
	 */
	private LocalDateTime tmSmp;

	public String getOrgInfId() {
		return orgInfId;
	}

	public void setOrgInfId(String orgInfId) {
		this.orgInfId = orgInfId;
	}

	public String getCorpOrgId() {
		return corpOrgId;
	}

	public void setCorpOrgId(String corpOrgId) {
		this.corpOrgId = corpOrgId;
	}

	public String getCorpOrgNm() {
		return corpOrgNm;
	}

	public void setCorpOrgNm(String corpOrgNm) {
		this.corpOrgNm = corpOrgNm;
	}

	public String getCorpOrgSnm() {
		return corpOrgSnm;
	}

	public void setCorpOrgSnm(String corpOrgSnm) {
		this.corpOrgSnm = corpOrgSnm;
	}

	public String getCorpOrgTyp() {
		return corpOrgTyp;
	}

	public void setCorpOrgTyp(String corpOrgTyp) {
		this.corpOrgTyp = corpOrgTyp;
	}

	public String getCreOprId() {
		return creOprId;
	}

	public void setCreOprId(String creOprId) {
		this.creOprId = creOprId;
	}

	public String getUpdOprId() {
		return updOprId;
	}

	public void setUpdOprId(String updOprId) {
		this.updOprId = updOprId;
	}

	public LocalDateTime getTmSmp() {
		return tmSmp;
	}

	public void setTmSmp(LocalDateTime tmSmp) {
		this.tmSmp = tmSmp;
	}

	public String getCorpAccNm() {
		return corpAccNm;
	}

	public void setCorpAccNm(String corpAccNm) {
		this.corpAccNm = corpAccNm;
	}

	public String getCorpAccNo() {
		return corpAccNo;
	}

	public void setCorpAccNo(String corpAccNo) {
		this.corpAccNo = corpAccNo;
	}

	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

}