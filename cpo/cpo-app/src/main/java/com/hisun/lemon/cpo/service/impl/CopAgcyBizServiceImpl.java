package com.hisun.lemon.cpo.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.lang.model.type.ArrayType;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.cpo.dao.ICopAgcyInfoDao;
import com.hisun.lemon.cpo.dto.CopAgcyBizDTO;
import com.hisun.lemon.cpo.dto.CopAgcyInfoDTO;
import com.hisun.lemon.cpo.entity.AccCfgDO;
import com.hisun.lemon.cpo.entity.CopAgcyInfoDO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.common.utils.ResourceUtils;
import com.hisun.lemon.cpo.dao.ICopAgcyBizDao;
import com.hisun.lemon.cpo.entity.CopAgcyBizDO;
import com.hisun.lemon.cpo.service.ICopAgcyBizService;

/**
 * 合作机构业务表service实现
 * 
 * <AUTHOR>
 *
 */
@Transactional
@Service
public class CopAgcyBizServiceImpl extends BaseService implements ICopAgcyBizService {

	@Resource
	private ICopAgcyBizDao copAgcyBizDao;

	@Resource
	private ICopAgcyInfoDao copAgcyInfoDao;

	@Override
	public GenericRspDTO<NoBody> addCopAgcyBiz(GenericDTO<CopAgcyBizDTO> genericDTO) {
		//请求对象
		CopAgcyBizDTO copAgcyBizDTO = genericDTO.getBody();
		CopAgcyBizDO copAgcyBizDO = new CopAgcyBizDO();
		BeanUtils.copyProperties(copAgcyBizDO, copAgcyBizDTO);
		copAgcyBizDao.insert(copAgcyBizDO);
		return GenericRspDTO.newSuccessInstance();
	}

	@Override
	public CopAgcyBizDO selectByKey(String orgBusId) {
		return copAgcyBizDao.get(orgBusId);
	}

	@Override
	public boolean updateByKey(CopAgcyBizDO copAgcyBizDO) {
		//int num = copAgcyBizDao.update(copAgcyBizDO);
		return false;
	}

	@Override
	public boolean deleteByKey(String orgBusId) {
		boolean bool = false;
		int num = copAgcyBizDao.delete(orgBusId);
		if (num > 0) {
			bool = true;
		}
		return bool;
	}

	@Override
	public GenericRspDTO<List<CopAgcyInfoDTO>> getAgcyList() {
		List<CopAgcyInfoDO> list =copAgcyInfoDao.getCopAgcyList();
		List<CopAgcyInfoDTO> copAgcyInfoDTOList = new ArrayList<>();
		for (CopAgcyInfoDO accCfgDO : list) {
			CopAgcyInfoDTO copAgcyBizDTO = new CopAgcyInfoDTO();
			BeanUtils.copyProperties(copAgcyBizDTO, accCfgDO);
			copAgcyInfoDTOList.add(copAgcyBizDTO);
		}
		GenericRspDTO<List<CopAgcyInfoDTO>> listGenericDTO = GenericRspDTO.newSuccessInstance(copAgcyInfoDTOList);
		return listGenericDTO;
	}
}
