package com.hisun.lemon.cpo.service;

import com.hisun.lemon.cpo.entity.CopAgcyInfoDO;

/**
 * 合作机构基本信息service
 * <AUTHOR>
 *
 */
public interface ICopAgcyInfoService {

	/**
	 * 新增机构信息
	 * @param copAgcyBizDO
	 * @return boolean
	 */
	boolean add(CopAgcyInfoDO copAgcyBizDO);
	
	/**
	 * 根据主键获取机构信息
	 * @param orgInfId
	 * @return CopAgcyBizDO
	 */
	CopAgcyInfoDO selectByKey(String orgInfId);
	
	/**
	 * 更新机构信息 
	 * @param copAgcyBizDO
	 * @return boolean
	 */
	boolean updateByKey(CopAgcyInfoDO copAgcyBizDO);
	
	/**
	 * 根据主键删除机构信息
	 * @param orgInfId
	 * @return boolean
	 */
	boolean deleteByKey(String orgInfId);
}
