package com.hisun.lemon.cpo.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.extension.ExtensionLoader;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.ReflectionUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpo.common.CpoConstants;
import com.hisun.lemon.cpo.common.CpoMsgCd;
import com.hisun.lemon.cpo.dao.IAccControlDao;
import com.hisun.lemon.cpo.dao.IAccErrorDao;
import com.hisun.lemon.cpo.dao.ILockDao;
import com.hisun.lemon.cpo.dao.IWithdrawSuborderDao;
import com.hisun.lemon.cpo.entity.AccCfgDO;
import com.hisun.lemon.cpo.entity.AccControlDO;
import com.hisun.lemon.cpo.entity.LockDO;
import com.hisun.lemon.cpo.service.ICheckAccExtendService;
import com.hisun.lemon.cpo.service.ICheckAccWithdrawService;
import com.hisun.lemon.framework.service.BaseService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 资金模块对账扩展服务实现 Service
 */
@Service
public class CheckAccExtendServiceImpl extends BaseService implements ICheckAccExtendService {
    private static final Logger logger = LoggerFactory.getLogger(CheckAccExtendServiceImpl.class);

    @Resource
    private ILockDao lockDao;

    @Resource
    private IAccControlDao accControlDao;

    @Resource
    private IWithdrawSuborderDao withdrawSuborderDao;

    @Resource
    private IAccErrorDao accErrorDao;

    @Resource
    private ICheckAccWithdrawService checkAccWithdrawService;

    /**
     * 查询对账锁状态
     */
    @Override
    public String getLockSts(String lockId) {
        return lockDao.getLockSts(lockId);
    }

    /**
     * 更新对账锁状态
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateLockSts(LockDO lockDO) {
        lockDao.update(lockDO);
    }

    /**
     * 添加对账锁信息
     */
    @Transactional
    @Override
    public void addLockInfo(LockDO lockDO) {
        lockDao.insert(lockDO);
    }


    /**
     * 对账第一步：获取银行机构对账文件
     */
    @Override
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getOrgChkFile(AccControlDO accControlDO, AccCfgDO accCfgDO) {
        logger.debug("==================获取银行机构对账文件==================");
        String chkFilSts = accControlDO.getChkFilSts();
        try {
            //根据配置的反射类进行反射
            Class chkClazz = Class.forName(accCfgDO.getChkClazz());
            Method method = chkClazz.getMethod(accCfgDO.getGetFileMethod(), String.class,LocalDate.class);

            //将首字母转成小写
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.append(Character.toLowerCase(chkClazz.getSimpleName().charAt(0)));//首字母转为小写
            stringBuilder.append(chkClazz.getSimpleName().substring(1));//从第一位开始截取字符串
            String beanName = stringBuilder.toString();//所需bean的名称

            //获取到Spring容器中的对象
            Object instance = ExtensionLoader.getSpringBean(beanName, chkClazz);

            //调用getFile方法
            Object rspObject = method.invoke(instance, accCfgDO.getChkFilePath(), accControlDO.getChkFilDt());
            if (JudgeUtils.isNotNull(rspObject)) {
                chkFilSts = CpoConstants.CHK_FIL_DOWNLOAD;
                accControlDO.setChkFilSts(chkFilSts);
                AccControlDO updateAccControlDO = new AccControlDO();
                updateAccControlDO.setChkBatNo(accControlDO.getChkBatNo());
                updateAccControlDO.setFileRcvDt(DateTimeUtils.getCurrentLocalDate());
                updateAccControlDO.setChkFilSts(chkFilSts);
                updateAccControlDO.setChkFilNm((String)rspObject);
                accControlDao.update(updateAccControlDO);
            }
        } catch (Exception e){
            logger.error("CheckAccExtendServiceImpl.getOrgChkFile()，反射调用getCheckFile方法失败，异常为: ", e);
            throw new LemonException("CheckAccExtendServiceImpl.getOrgChkFile()，反射调用getCheckFile方法失败");
        }
        return chkFilSts;
    }


    /**
     * 对账第二步：解析银行对账文件
     */
    @Override
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String importOrgChkFile(AccControlDO accControlDO, AccCfgDO accCfgDO) {
        logger.debug("==================开始解析银行对账文件==================");
        List<Object> checkDOList = null;
        BufferedReader reader = null;
        String lineString = null;
        String[] valueArray = null;
        String chkFilSts = accControlDO.getChkFilSts();
        try {
            //step1: 获取对账文件
            File file = new File(accCfgDO.getChkFilePath() + File.separator + accControlDO.getChkFilNm());

            //step2:若文件存在，则根据对账配置，调用银行API类的文件解析方法
            if(file.exists() && file.length() > 0) {
                checkDOList = new ArrayList<>();
                reader = new BufferedReader(new FileReader(file));//文件流
                String[] filedNames = accCfgDO.getImportFields().split(accCfgDO.getSplitSign());//文件格式字段
                String fileSplitter = accCfgDO.getSplitSign();//文件中每行记录的字段分隔符
                int continueNum = accCfgDO.getContinueNum();//需要跳过的行数

                //循环解析对账文件中的每行记录
                int lingNum = 0;
                while ((lineString = reader.readLine()) != null) {
                    //需要跳过的行数
                    if (lingNum < continueNum) {
                        lingNum++;
                        continue;
                    }

                    //初始化对账明细表对应的DO类
                    Class importClazz = Class.forName(accCfgDO.getImportClazz());
                    Object importObject = importClazz.newInstance();

                    //根据分割符，得到每行记录的字段值数组
                    valueArray = lineString.split(fileSplitter);
                    for (int i = 0; i < filedNames.length; i++) {
                        //获取DO类的属性域
                        Field field = importClazz.getDeclaredField(filedNames[i]);

                        //打破封装: 实际上setAccessible是启用和禁用访问安全检查的开关,并不是为true就能访问、为false就不能访问。
                        //由于JDK的安全检查耗时较多.所以通过setAccessible(true)的方式关闭安全检查就可以达到提升反射速度的目的。
                        field.setAccessible(true);

                        //属性赋值
                        ReflectionUtils.setField(field, importObject, valueArray[i]);
                    }

                    //获取DO类所有属性域
                    Field[] declaredFields = importClazz.getDeclaredFields();
                    for(Field field : declaredFields){
                        //关闭JDK的安全检查
                        field.setAccessible(true);
                        switch (field.getName()) {
                            //对账批次号
                            case "chkBatNo":
                                ReflectionUtils.setField(field, importObject, accControlDO.getChkBatNo());
                                break;

                            //对账文件日期
                            case "chkFilDt":
                                ReflectionUtils.setField(field, importObject, DateTimeUtils.formatLocalDate(accControlDO.getChkDt()));
                                break;

                            //对账文件名称
                            case "chkFilNm":
                                ReflectionUtils.setField(field, importObject, accControlDO.getChkFilNm());
                                break;

                            //合作业务类型
                            case "corpBusTyp":
                                ReflectionUtils.setField(field, importObject, accControlDO.getCorpBusTyp());
                                break;

                            //合作业务子类型
                            case "corpBusSubTyp":
                                ReflectionUtils.setField(field, importObject, accControlDO.getCorpBusSubTyp());
                                break;

                            //路径合作机构
                            case "rutCorg":
                                ReflectionUtils.setField(field, importObject, accControlDO.getRutCorg());
                                break;
                            default:
                                break;
                        }
                    }
                    checkDOList.add(importObject);
                }
            } else {
                return chkFilSts;
            }

            //反射获取银行API中批量插入对账明细的方法
            Class chkClazz = Class.forName(accCfgDO.getChkClazz());
            Method method = chkClazz.getMethod(accCfgDO.getImportMethod(), List.class, AccControlDO.class);

            //将首字母转成小写
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.append(Character.toLowerCase(chkClazz.getSimpleName().charAt(0)));//首字母转为小写
            stringBuilder.append(chkClazz.getSimpleName().substring(1));//从第一位开始截取字符串
            String beanName = stringBuilder.toString();//所需bean的名称

            //获取到Spring容器中的对象，银行API类的bean
            Object instance = ExtensionLoader.getSpringBean(beanName, chkClazz);

            //调用对账明细批量入库方法
            method.invoke(instance, checkDOList, accControlDO);

            //批量导入成功
            chkFilSts = CpoConstants.CHK_FIL_IMPORT;
            accControlDO.setChkFilSts(chkFilSts);

            //更新对账批次状态、文件总笔数、文件总金额
            AccControlDO updateAccControlDO = new AccControlDO();
            updateAccControlDO.setChkFilSts(chkFilSts);
            updateAccControlDO.setChkBatNo(accControlDO.getChkBatNo());
            updateAccControlDO.setFilTotCnt(accControlDO.getFilTotCnt());
            updateAccControlDO.setFilTotAmt(accControlDO.getFilTotAmt());
            accControlDao.update(updateAccControlDO);
        } catch (Exception e) {
            logger.error("CheckAccExtendServiceImpl.importOrgChkFile()，解析银行对账文件失败，异常为: ", e);
            throw new LemonException(CpoMsgCd.IMPORT_CHECK_FILE_FAIL.getMsgCd());
        } finally {
            if (null != reader){
                try {
                    reader.close();
                } catch (Exception e) {
                    logger.error("CheckAccExtendServiceImpl.importOrgChkFile()，关闭文件流异常，异常为: ", e);
                }
            }
        }
        return chkFilSts;
    }

    /**
     * 对账第三步:对账业务逻辑，以银行成功的对账明细为准
     */
    @Override
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String checkFile(AccControlDO accControlDO, AccCfgDO accCfgDO) throws Exception {
        //step1: 对账批次信息
        String chkFilSts = accControlDO.getChkFilSts();
        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusTyp = accControlDO.getCorpBusTyp();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo);

        // step2: 检查上一对账日期之前，是否还有未完成的对账批次
        AccControlDO beforeAccControlDO = new AccControlDO();
        beforeAccControlDO.setChkDt(accControlDO.getChkFilDt().minusDays(1));
        beforeAccControlDO.setRutCorg(accControlDO.getRutCorg());
        beforeAccControlDO.setCorpBusTyp(accControlDO.getCorpBusTyp());
        beforeAccControlDO.setCorpBusSubTyp(accControlDO.getCorpBusSubTyp());
        List<AccControlDO> accControlDOList = accControlDao.getAccControlListByLastChkDt(beforeAccControlDO);
        if (CollectionUtils.isNotEmpty(accControlDOList)) {
            if (!CpoConstants.CHK_FIL_FINISHED.equals(accControlDOList.get(0).getChkFilSts())) {
                logger.error("CheckAccExtendServiceImpl.checkFile()，机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + " 上日对账仍有未完成的批次");
                return chkFilSts;
            }
        }

        //step3: 根据业务类型，分别调用不同的对账服务
        switch (corpBusTyp) {
            //快捷(充值、消费)
            case "06":
                chkFilSts = checkAccWithdrawService.checkAccountWithdraw(accControlDO, accCfgDO);
                break;

            default:
                logger.error("CheckAccExtendServiceImpl.checkFile()，机构: " + rutCorg + "; 对账批次号: " + chkBatNo + " 合作业务类型为空");
                throw new LemonException("机构: " + rutCorg + "; 合作业务类型为空");
        }

        //对账完成，更新对账批次状态
        if (StringUtils.equals(chkFilSts, CpoConstants.CHK_FIL_FINISHED)) {
            accControlDO.setChkFilSts(chkFilSts);
            accControlDao.update(accControlDO);
        }
        return chkFilSts;
    }

}
