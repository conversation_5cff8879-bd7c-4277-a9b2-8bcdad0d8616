buildscript {
    repositories {
        maven { url "http://*************:8081/repository/maven-public/" }
    }
    ext {
        springBootVersion = '1.5.3.RELEASE'
        //springCloudVersion = 'Dalston.SR1'
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath("io.spring.gradle:dependency-management-plugin:1.0.1.RELEASE")
    }
}

subprojects{
    apply plugin: 'java'
    apply plugin: 'eclipse'
    apply plugin: 'maven'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'

    version = '1.0.0-SNAPSHOT'
    group = 'com.hisun'
    
    sourceCompatibility = 1.8
    targetCompatibility = 1.8
	
    configurations.all {
        // check for updates every build
        resolutionStrategy.cacheChangingModulesFor 10, 'seconds'
        resolutionStrategy.cacheDynamicVersionsFor 60, 'seconds'
    }
    
    bootRepackage {
        enabled = false
        //mainClass=
    }
    
    repositories {
        maven { url "http://*************:8081/repository/maven-public/" }
    }

    dependencyManagement {
	    dependencies {
	        dependency 'mysql:mysql-connector-java:6.0.6'
	        dependency 'com.hisun:lemon-framework:1.0.1-SNAPSHOT'
	        dependency 'com.hisun:lemon-swagger:1.0.1-SNAPSHOT'
            dependency 'com.hisun:lemon-interface:1.0.1-SNAPSHOT'
            dependency 'com.hisun:jcommon:1.0.1-SNAPSHOT'
            dependency 'com.hisun:acm-interface:1.0.1-SNAPSHOT'
            dependency 'com.hisun:pwm-interface:1.0.0-SNAPSHOT'
            dependency 'com.hisun:rsm-interface:1.0.0-SNAPSHOT'
            dependency 'com.hisun:csm-interface:1.0.0-SNAPSHOT'
            dependency 'com.hisun:csh-interface:1.0.0-SNAPSHOT'
            dependency 'com.hisun:tam-interface:1.0.0-SNAPSHOT'
            dependency 'com.hisun:cmm-interface:1.0.0-SNAPSHOT'
            dependency 'com.hisun:lemon-framework-lock:1.0.1-SNAPSHOT'
            dependency 'com.hisun:cpi-interface:1.0.0-SNAPSHOT'

	     }
    }
    
//    dependencies {
//	    testCompile("org.springframework.boot:spring-boot-starter-test")
//	}
	
	jar {
        from('src/main/resources') {
            include '**/*.*'
        }
    }

    //打包源代码
    task sourcesJar(type: Jar) {
        classifier = 'sources'
        from sourceSets.main.allSource
    }
    artifacts {
        archives jar
        archives sourcesJar
    }
    uploadArchives {
        repositories {
            mavenDeployer {
                snapshotRepository(url: "http://*************:8081/repository/maven-snapshots/") {
                    authentication(userName:'admin', password:'hq1q2w3e$R')
                }
                repository(url: "http://*************:8081/repository/maven-releases/") {
                    authentication(userName:'admin', password:'hq1q2w3e$R')
                }
            }
        }
    }
}

description = 'lemon cpo app-group'
