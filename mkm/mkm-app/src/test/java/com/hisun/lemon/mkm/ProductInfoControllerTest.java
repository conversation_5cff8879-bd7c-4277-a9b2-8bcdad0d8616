/*
package com.hisun.lemon.mkm;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.res.dto.ConsumeSeaCcyResDTO;
import com.hisun.lemon.mkm.res.dto.RevokedConsumeCouponResDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.xml.crypto.Data;
import java.math.BigDecimal;


@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class ProductInfoControllerTest {

	@Autowired
	private WebApplicationContext context;

	private MockMvc mockMvc;

	@Before
	public void setupMockMvc(){
		mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
	}

	*/
/***
	 * 新增活动-海币
	 * @throws Exception
	 *//*

	@Test
	//@Ignore
	public void testAddMarketActivityReqDTO() throws Exception {
		AddMarketActivityReqDTO addMarketActivityReqDTO = new AddMarketActivityReqDTO();
		addMarketActivityReqDTO.setAtvNm("新增");
		addMarketActivityReqDTO.setAmt(BigDecimal.valueOf(300));
		addMarketActivityReqDTO.setBeginTime("20170725000000");
		addMarketActivityReqDTO.setCrtUserOpr("匆匆");
		addMarketActivityReqDTO.setEndTime("20170726000000");
		addMarketActivityReqDTO.setItem("100002");
		addMarketActivityReqDTO.setMkTool("02");
		addMarketActivityReqDTO.setReceiveTimes(3);
		addMarketActivityReqDTO.setTotal(1000);
		addMarketActivityReqDTO.setTotalAmt(BigDecimal.valueOf(10000000));
		GenericRspDTO<AddMarketActivityReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(addMarketActivityReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/marketActivity/add")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 新增活动-电子券
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testAddMarketActivityReqDTO2() throws Exception {
		AddMarketActivityReqDTO addMarketActivityReqDTO = new AddMarketActivityReqDTO();
		addMarketActivityReqDTO.setAtvNm("新增");
		addMarketActivityReqDTO.setAmt(BigDecimal.valueOf(300));
		addMarketActivityReqDTO.setBeginTime("20170725000000");
		addMarketActivityReqDTO.setCrtUserOpr("匆匆");
		addMarketActivityReqDTO.setEndTime("20170726000000");
		addMarketActivityReqDTO.setItem("25840");
		addMarketActivityReqDTO.setMkTool("01");
		addMarketActivityReqDTO.setReceiveTimes(3);
		addMarketActivityReqDTO.setTotal(1000);
		addMarketActivityReqDTO.setTotalAmt(BigDecimal.valueOf(10000000));
		addMarketActivityReqDTO.setCouponInvalTm("20170726000000");
		addMarketActivityReqDTO.setCouponValTm("20170725000000");
		addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		addMarketActivityReqDTO.setInstId("1122225,3333336");
		GenericRspDTO<AddMarketActivityReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(addMarketActivityReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/marketActivity/add")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 修改活动-海币
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testUpdateMarketActivityReqDTO() throws Exception {
		UpdateMarketActivityReqDTO addMarketActivityReqDTO = new UpdateMarketActivityReqDTO();
		addMarketActivityReqDTO.setAtvNm("修改");
		addMarketActivityReqDTO.setAmt(BigDecimal.valueOf(300));
		addMarketActivityReqDTO.setEndTimeSr("20170726000000");
		addMarketActivityReqDTO.setItem("25840");
		addMarketActivityReqDTO.setReceiveTimes(4);
		addMarketActivityReqDTO.setTotal(1001);
		addMarketActivityReqDTO.setTotalAmt(BigDecimal.valueOf(10000001));
		addMarketActivityReqDTO.setId("0000000000000000000002001");
		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<UpdateMarketActivityReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(addMarketActivityReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/marketActivity/update")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	*/
/***
	 * 修改活动-优惠券
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testUpdateMarketActivityReqDTO02() throws Exception {
		UpdateMarketActivityReqDTO addMarketActivityReqDTO = new UpdateMarketActivityReqDTO();
		addMarketActivityReqDTO.setAtvNm("修改");
		addMarketActivityReqDTO.setAmt(BigDecimal.valueOf(300));
		addMarketActivityReqDTO.setEndTimeSr("20170726000000");
		addMarketActivityReqDTO.setItem("25840");
		addMarketActivityReqDTO.setReceiveTimes(5);
		addMarketActivityReqDTO.setTotal(1002);
		addMarketActivityReqDTO.setTotalAmt(BigDecimal.valueOf(10000002));
		addMarketActivityReqDTO.setInstId("1122222,144444");
		addMarketActivityReqDTO.setId("0000000000000000000002501");
		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<UpdateMarketActivityReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(addMarketActivityReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/marketActivity/update")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	*/
/***
	 * 查询营销活动
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testfindMarketActivityReqDTO() throws Exception {
		FindMarketActivityReqDTO addMarketActivityReqDTO = new FindMarketActivityReqDTO();
		addMarketActivityReqDTO.setAtvNm("新增");
		addMarketActivityReqDTO.setPageNo(1);
		addMarketActivityReqDTO.setPageSize(10);
		addMarketActivityReqDTO.setCreateDate("20170725");
		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<FindMarketActivityReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(addMarketActivityReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/marketActivity/find")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	*/
/***
	 * 删除营销活动
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testdeleteMarketActivityReqDTO() throws Exception {
		DeleteMarketActivityReqDTO deleteMarketActivityReqDTO = new DeleteMarketActivityReqDTO();
		deleteMarketActivityReqDTO.setId("0000000000000000000002501");
		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<DeleteMarketActivityReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(deleteMarketActivityReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/marketActivity/delete")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 维护营销活动
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testMaintainActivityReqDTO() throws Exception {
		MaintainActivityReqDTO maintainActivityReqDTO = new MaintainActivityReqDTO();
		maintainActivityReqDTO.setId("0000000000000000000003001");
	//	maintainActivityReqDTO.setExamineStatus("02");
		maintainActivityReqDTO.setStatus("03");
		maintainActivityReqDTO.setType("01");
		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<MaintainActivityReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(maintainActivityReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/marketActivity/maintian")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 客户充值海币
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testRechargeMkmToolReqDTO() throws Exception {
		RechargeMkmToolReqDTO rechargeMkmToolReqDTO = new RechargeMkmToolReqDTO();
		rechargeMkmToolReqDTO.setCount(10);
		rechargeMkmToolReqDTO.setSeq(DateTimeUtils.getCurrentDateTimeStr());
		rechargeMkmToolReqDTO.setType("00");
		rechargeMkmToolReqDTO.setMobile("1375052486");
		rechargeMkmToolReqDTO.setUserId("254845123");
		rechargeMkmToolReqDTO.setMkTool("02");
		rechargeMkmToolReqDTO.setRechargeTm(DateTimeUtils.getCurrentLocalDateTime());

		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<RechargeMkmToolReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(rechargeMkmToolReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/mkmTool/get")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 海币发放
	 * @throws Exception
	 *//*

	@Test
	//@Ignore
	public void testRechargeMkmToolReqDTO02() throws Exception {
		RechargeMkmToolReqDTO rechargeMkmToolReqDTO = new RechargeMkmToolReqDTO();
		rechargeMkmToolReqDTO.setCount(10);
		rechargeMkmToolReqDTO.setSeq(DateTimeUtils.getCurrentDateTimeStr());
		rechargeMkmToolReqDTO.setType("01");
		rechargeMkmToolReqDTO.setMobile("1375052486");
		rechargeMkmToolReqDTO.setUserId("1222222");
		rechargeMkmToolReqDTO.setMkTool("02");
		rechargeMkmToolReqDTO.setAtvId("0000004001");
		rechargeMkmToolReqDTO.setRechargeTm(DateTimeUtils.getCurrentLocalDateTime());

		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<RechargeMkmToolReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(rechargeMkmToolReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/mkmTool/get")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	*/
/***
	 * 电子券发放发放
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testGetConponReqDTO() throws Exception {
		GetConponReqDTO rechargeMkmToolReqDTO = new GetConponReqDTO();
		rechargeMkmToolReqDTO.setAmt(BigDecimal.valueOf(10));
		rechargeMkmToolReqDTO.setSeq(DateTimeUtils.getCurrentDateTimeStr());
		rechargeMkmToolReqDTO.setType("01");
		rechargeMkmToolReqDTO.setMobile("1375052486");
		rechargeMkmToolReqDTO.setUserId("2548456");
		rechargeMkmToolReqDTO.setMkTool("01");
		rechargeMkmToolReqDTO.setAtvId("0000000001");
		rechargeMkmToolReqDTO.setRechargeTm(DateTimeUtils.getCurrentLocalDateTime());
		rechargeMkmToolReqDTO.setInstId("1122225");


		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<GetConponReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(rechargeMkmToolReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/mkmTool/getConpon")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 海币消费
	 * @throws Exception
	 *//*

	@Test
	public void testConsumeSeaCcyResDTO() throws Exception {
		ConsumeSeaCcyReqDTO rechargeMkmToolReqDTO = new ConsumeSeaCcyReqDTO();
		rechargeMkmToolReqDTO.setCount(20);
		rechargeMkmToolReqDTO.setSeq(DateTimeUtils.getCurrentDateTimeStr());
		rechargeMkmToolReqDTO.setType("02");
		rechargeMkmToolReqDTO.setMobile("1375052486");
		rechargeMkmToolReqDTO.setUserId("1");
		rechargeMkmToolReqDTO.setMkTool("02");
		rechargeMkmToolReqDTO.setConsumeTm(DateTimeUtils.getCurrentLocalDateTime());
		rechargeMkmToolReqDTO.setOrderNo(DateTimeUtils.getCurrentDateTimeStr());


		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<ConsumeSeaCcyReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(rechargeMkmToolReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/mkmTool/consumeSeaCcy")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 电子券消费
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testConsumeCouponReqDTO() throws Exception {
		ConsumeCouponReqDTO rechargeMkmToolReqDTO = new ConsumeCouponReqDTO();
		rechargeMkmToolReqDTO.setSeq(DateTimeUtils.getCurrentDateTimeStr());
		rechargeMkmToolReqDTO.setType("02");
		rechargeMkmToolReqDTO.setUserId("2548456");
		rechargeMkmToolReqDTO.setMkTool("01");
		rechargeMkmToolReqDTO.setCouponNo("00000000010000000501");
		rechargeMkmToolReqDTO.setConsumeTm(DateTimeUtils.getCurrentLocalDateTime());
		rechargeMkmToolReqDTO.setInstId("1122225");
		rechargeMkmToolReqDTO.setOrderNo(DateTimeUtils.getCurrentDateTimeStr());
		rechargeMkmToolReqDTO.setOrderAmt(BigDecimal.valueOf(10));


		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<ConsumeCouponReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(rechargeMkmToolReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/mkmTool/consumeCoupon")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	*/
/***
	 * 海币消费撤销
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testRevokedConsumeCouponResDTO() throws Exception {
		RevokedConsumeCouponReqDTO rechargeMkmToolReqDTO = new RevokedConsumeCouponReqDTO();
		rechargeMkmToolReqDTO.setSeq(DateTimeUtils.getCurrentDateTimeStr());
		rechargeMkmToolReqDTO.setMkTool("02");
		rechargeMkmToolReqDTO.setOriSeq("20170726151210");
		rechargeMkmToolReqDTO.setRevokedTm(DateTimeUtils.getCurrentLocalDateTime());


		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<RevokedConsumeCouponReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(rechargeMkmToolReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/mkmTool/revoconsume")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 客户可领取的营销工具
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testQuery() throws Exception {
		FindMarketActivityReqDTO rechargeMkmToolReqDTO = new FindMarketActivityReqDTO();
		rechargeMkmToolReqDTO.setInstId("1122225");


		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<FindMarketActivityReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(rechargeMkmToolReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/mkmTool/query")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	@Test
	@Ignore
	public void queryUserMkmTool() throws Exception {
		QueryUserMkmToolReqDTO rechargeMkmToolReqDTO = new QueryUserMkmToolReqDTO();
		rechargeMkmToolReqDTO.setUserId("254845");


		//addMarketActivityReqDTO.setMinAmt(BigDecimal.valueOf(13));
		//addMarketActivityReqDTO.setMaxAmt(BigDecimal.valueOf(15));
		//addMarketActivityReqDTO.setInstId("1122222,3333333");
		GenericRspDTO<QueryUserMkmToolReqDTO> genericDTO = GenericRspDTO.newSuccessInstance(rechargeMkmToolReqDTO);
		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		String requestJson = ow.writeValueAsString(genericDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/mkmTool/queryUserMkmTool")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();
		System.out.print("return code :" + status);
		System.out.print("return content :" + content);
		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}
	*/
/***
	 * 理财产品更新接口
	 * @throws Exception
	 *//*

	@Test
	@Ignore
	public void testProductUpdate() throws Exception {
//		InvProductInfoDTO invProductInfoDTO = new InvProductInfoDTO();
//		invProductInfoDTO.setProId("201707181852227501");
//		invProductInfoDTO.setInvTerm(999999);
//		invProductInfoDTO.setProDesc("test111");
//		invProductInfoDTO.setProEffTmStr("20170101 00:00:00");
//		invProductInfoDTO.setProExpTmStr("20171205 23:59:59");
//		invProductInfoDTO.setProName("理财测试update");
//		invProductInfoDTO.setProType("D");
//		invProductInfoDTO.setRate(BigDecimal.valueOf(1.99));
//		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//		String requestJson = ow.writeValueAsString(invProductInfoDTO);
//		RequestBuilder request = MockMvcRequestBuilders.put("/inv-service/configure/products/product")
//				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
//		MvcResult mvcResult = mockMvc.perform(request).andReturn();
//		int status = mvcResult.getResponse().getStatus();
//		String content = mvcResult.getResponse().getContentAsString();
//		System.out.print("return code :" + status);
//		System.out.print("return content :" + content);
//		Assert.assertTrue("正确", status == 200);
//		Assert.assertFalse("错误", status != 200);

	}

//	*/
/***
//	 * 理财产品查询接口
//	 * @throws Exception
//	 *//*

//	@Test
//	public void testProductSelect() throws Exception {
//		InvProductInfoDTO invProductInfoDTO = new InvProductInfoDTO();
//		//invProductInfoDTO.setProId("201707141127296501");
//		//invProductInfoDTO.setInvTerm(999999);
//		//invProductInfoDTO.setProDesc("test111");
//		//invProductInfoDTO.setProEffTm("20170101");
//		//invProductInfoDTO.setProExpTm("20171210");
//		//invProductInfoDTO.setProName("理财测试update");
//		//invProductInfoDTO.setProType("D");
//		//invProductInfoDTO.setRate(1.99);
//		String url = "/inv-service/configure/products";
//		String pageSize = "10";
//		String pageNo = "0";
//		url = url + "/" + pageSize + "/" + pageNo;
//		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//		String requestJson = ow.writeValueAsString(invProductInfoDTO);
//		RequestBuilder request = MockMvcRequestBuilders.get(url)
//				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
//		MvcResult mvcResult = mockMvc.perform(request).andReturn();
//		int status = mvcResult.getResponse().getStatus();
//		String content = mvcResult.getResponse().getContentAsString();
//		System.out.print("return code :" + status);
//		System.out.print("return content :" + content);
//		Assert.assertTrue("正确", status == 200);
//		Assert.assertFalse("错误", status != 200);
//	}
//
//	*/
/***
//	 * 理财产品删除接口
//	 * @throws Exception
//	 *//*

//	@Test
//	public void testProductDelete() throws Exception {
//		String proId = "201707181856148001";
//		String url = "/inv-service/configure/products/product";
//		url = url + "/" + proId;
//		RequestBuilder request = MockMvcRequestBuilders.delete(url).contentType(MediaType.APPLICATION_JSON_UTF8);
//		MvcResult mvcResult = mockMvc.perform(request).andReturn();
//		int status = mvcResult.getResponse().getStatus();
//		String content = mvcResult.getResponse().getContentAsString();
//		System.out.print("return code :" + status);
//		System.out.print("return content :" + content);
//		Assert.assertTrue("正确", status == 200);
//		Assert.assertFalse("错误", status != 200);
//	}
}
*/
