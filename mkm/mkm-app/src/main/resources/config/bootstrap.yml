spring :
  application :
    name : MKM

server :
  port : 9009

eureka :
  client :
    serviceUrl :
      defaultZone : ${eureka.zone}
    registerWithEureka : ${registerWithEureka:true}
    #registerWithEureka : false
  instance :
    preferIpAddress : true

endpoints :
  shutdown :
    enabled : true
    sensitive : false
    
#logging configuration
logging :
  config : classpath:config/logback-spring.xml
