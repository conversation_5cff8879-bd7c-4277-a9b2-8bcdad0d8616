/**
 * 需要设计表
 */

CREATE TABLE mkm_activity(
  id VARCHAR(25) NOT NULL,
  atv_nm VARCHAR(100) NOT NULL,
  mk_tool VARCHAR(2) NOT NULL,
  examine_status char(2) NOT NULL,
  status char(2) NOT NULL,
  total INT NOT NULL,
  total_amt DECIMAL(15,2) NOT NULL,
  amt DECIMAL(15,2) ,
  discount DECIMAL(15,2) ,
  coupon_name VARCHAR(100),
  begin_time DATETIME NOT NULL,
  end_time	DATETIME NOT NULL,
  remain_num INT NOT NULL,
  remain_amt DECIMAL(15,2) NOT NULL,
  aclt INT,
  aclt_amt DECIMAL(15,2) ,
  modify_time DATETIME NOT NULL,
  create_time DATETIME NOT NULL,
  crt_user_opr VARCHAR(20),
  mdf_user_opr VARCHAR(20),
  item VARCHAR(25) ,
  inst_id VARCHAR(300) ,
  cost_side VARCHAR(50) ,
  PRIMARY KEY (id)
);

ALTER TABLE mkm_activity COMMENT '营销活动表';
ALTER TABLE mkm_activity MODIFY COLUMN id VARCHAR(25) COMMENT '活动id';
ALTER TABLE mkm_activity MODIFY COLUMN atv_nm VARCHAR(100) COMMENT '活动名称';
ALTER TABLE mkm_activity MODIFY COLUMN  mk_tool VARCHAR(2) COMMENT '营销工具 01-电子券，02-海币 03-优惠券' ;
ALTER TABLE mkm_activity MODIFY COLUMN  status char(2)COMMENT '状态 00-正常 01-冻结 ，02-解冻 03-记账异常';
ALTER TABLE mkm_activity MODIFY COLUMN  examine_status char(2)COMMENT '审核状态 01-未审核 ，02-已审核';
ALTER TABLE mkm_activity MODIFY COLUMN total INT COMMENT '发放总量';
ALTER TABLE mkm_activity MODIFY COLUMN total_amt DECIMAL(15,2) COMMENT '发放总金额';
ALTER TABLE mkm_activity MODIFY COLUMN amt DECIMAL(15,2)  COMMENT '单券金额';
ALTER TABLE mkm_activity MODIFY COLUMN coupon_name VARCHAR(100) COMMENT '券别名称';
ALTER TABLE mkm_activity MODIFY COLUMN discount DECIMAL(15,2)  COMMENT '折扣';
ALTER TABLE mkm_activity MODIFY COLUMN begin_time DATETIME COMMENT '活动开始时间';
ALTER TABLE mkm_activity MODIFY COLUMN end_time DATETIME COMMENT '活动结束时间';
ALTER TABLE mkm_activity MODIFY COLUMN  remain_num INT COMMENT '剩余发放总数';
ALTER TABLE mkm_activity MODIFY COLUMN remain_amt DECIMAL(15,2) COMMENT '剩余发放总金额';
ALTER TABLE mkm_activity MODIFY COLUMN aclt_amt DECIMAL(15,2) COMMENT '累计消费金额 海币不需维护';
ALTER TABLE mkm_activity MODIFY COLUMN  aclt INT COMMENT '累计消费总量';
ALTER TABLE mkm_activity MODIFY COLUMN modify_time DATETIME COMMENT '修改时间';
ALTER TABLE mkm_activity MODIFY COLUMN create_time DATETIME COMMENT '创建时间';
ALTER TABLE mkm_activity MODIFY COLUMN crt_user_opr VARCHAR(20) COMMENT '创建人员';
ALTER TABLE mkm_activity MODIFY COLUMN mdf_user_opr VARCHAR(20) COMMENT '操作人员';
ALTER TABLE mkm_activity MODIFY COLUMN item VARCHAR(25) COMMENT '借方科目号';
ALTER TABLE mkm_activity MODIFY COLUMN inst_id VARCHAR(2) COMMENT '是否有商户';
ALTER TABLE mkm_activity MODIFY COLUMN cost_side VARCHAR(50) COMMENT '费用承担方';
ALTER TABLE mkm_activity MODIFY COLUMN min_amt DECIMAL(15,2) COMMENT '可使用订单最小金额(电子券)';
ALTER TABLE mkm_activity MODIFY COLUMN max_amt DECIMAL(15,2) COMMENT '可使用订单最大金额(电子券)';

##活动发放指定交易商户范围表- mkm_inst
CREATE TABLE mkm_inst(
  id INT NOT NULL AUTO_INCREMENT,
  atv_id VARCHAR(25) NOT NULL,
  inst_id VARCHAR(32),
  total INT NOT NULL,
  total_amt DECIMAL(15,2) NOT NULL,
  tot_iss_amt DECIMAL(15,2),
  tot_iss_cnt INT,
  modify_time DATETIME NOT NULL,
  create_time DATETIME NOT NULL,
  PRIMARY KEY (id)
);
ALTER TABLE mkm_inst COMMENT '营销活动表';
ALTER TABLE mkm_inst MODIFY COLUMN atv_id VARCHAR(25) COMMENT '活动id';
ALTER TABLE mkm_inst MODIFY COLUMN total INT COMMENT '发放总量';
ALTER TABLE mkm_inst MODIFY COLUMN total_amt DECIMAL(15,2) COMMENT '发放总金额';
ALTER TABLE mkm_inst MODIFY COLUMN tot_iss_cnt INT COMMENT '累计总量';
ALTER TABLE mkm_inst MODIFY COLUMN tot_iss_amt DECIMAL(15,2) COMMENT '累计总金额';
ALTER TABLE mkm_inst MODIFY COLUMN  inst_id VARCHAR(32) COMMENT '商户id';
ALTER TABLE mkm_inst MODIFY COLUMN modify_time DATETIME COMMENT '修改时间';
ALTER TABLE mkm_inst MODIFY COLUMN create_time DATETIME COMMENT '创建时间';


/**
 * 电子券，优惠券交易流水表
 * 发放流水、券别编号、券别类型、活动编号、用户手机、用户id、发放金额、
 * 发放时间、发放日期、营销工具类别（海币、电子券）、状态、类型
 */
CREATE TABLE mkm_coupon_seq(
  seq VARCHAR(32) NOT NULL PRIMARY KEY,
  channel VARCHAR(6) ,
  coupon_no VARCHAR(25),
  mk_tool VARCHAR(2) NOT NULL ,
  atv_id VARCHAR(25) ,
  type VARCHAR(2)    NOT NULL,
  user_id VARCHAR(32) NOT NULL,
  mobile VARCHAR(20) ,
  amt DECIMAL(15,2) ,
  balance DECIMAL(15,2) ,
  release_tm TIME ,
  release_dt DATE ,
  status VARCHAR(2) NOT NULL,
  order_no varchar(32) ,
  order_amt DECIMAL(15,2) ,
  modify_time DATETIME NOT NULL,
  create_time DATETIME NOT NULL,
  ori_time DATETIME ,
  ori_seq VARCHAR(25),
  msg_cd VARCHAR(10),
  msg_info VARCHAR(50)
);
ALTER TABLE mkm_coupon_seq COMMENT '营销工具发放流水表';
ALTER TABLE mkm_coupon_seq MODIFY  seq VARCHAR(32) COMMENT '流水';
ALTER TABLE mkm_coupon_seq MODIFY  channel VARCHAR(6) COMMENT '渠道';
ALTER TABLE mkm_coupon_seq MODIFY  coupon_no VARCHAR(25) COMMENT '券别编号';
ALTER TABLE mkm_coupon_seq MODIFY  mk_tool VARCHAR(2) COMMENT '券别类型';
ALTER TABLE mkm_coupon_seq MODIFY  atv_id VARCHAR(32) COMMENT '活动id';
ALTER TABLE mkm_coupon_seq MODIFY   user_id VARCHAR(32) COMMENT '用户id';
ALTER TABLE mkm_coupon_seq MODIFY   mobile VARCHAR(20) COMMENT '手机号';
ALTER TABLE mkm_coupon_seq MODIFY   type VARCHAR(2) COMMENT '流水类型 01-发放 02-核销';
ALTER TABLE mkm_coupon_seq MODIFY   amt DECIMAL(15,2) COMMENT '交易金额';
ALTER TABLE mkm_coupon_seq MODIFY   balance DECIMAL(15,2) COMMENT '剩余金额';
ALTER TABLE mkm_coupon_seq MODIFY   release_tm TIME COMMENT '发放时间';
ALTER TABLE mkm_coupon_seq MODIFY  release_dt DATE COMMENT '发放日期';
ALTER TABLE mkm_coupon_seq MODIFY   mk_tool VARCHAR(2) COMMENT '营销工具';
ALTER TABLE mkm_coupon_seq MODIFY  status VARCHAR(2) COMMENT '状态 0-失败 1-成功 2-异常';
ALTER TABLE mkm_coupon_seq MODIFY   order_no VARCHAR(32) COMMENT '订单号';
ALTER TABLE mkm_coupon_seq MODIFY   order_amt DECIMAL(15,2) COMMENT '订单金额';
ALTER TABLE mkm_coupon_seq MODIFY COLUMN modify_time DATETIME COMMENT '修改时间';
ALTER TABLE mkm_coupon_seq MODIFY COLUMN create_time DATETIME COMMENT '创建时间';
ALTER TABLE mkm_coupon_seq MODIFY COLUMN ori_time DATETIME COMMENT '原交易时间';
ALTER TABLE mkm_coupon_seq MODIFY  msg_cd VARCHAR(10) COMMENT '错误码';
ALTER TABLE mkm_coupon_seq MODIFY  msg_info VARCHAR(50) COMMENT '错误信息';

##券别明细表-coupon
CREATE TABLE mkm_coupon_detail(
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  coupon_no VARCHAR(25) NOT NULL COMMENT '抵用券编号',
  mk_tool VARCHAR(2) NOT NULL COMMENT '营销工具类型',
  atv_id VARCHAR(25) NOT NULL COMMENT '活动编号',
  inst_id VARCHAR(32) COMMENT '商户id',
  user_id VARCHAR(32) NOT NULL COMMENT '用户id',
  mobile VARCHAR(20) COMMENT '用户手机号',
  amt DECIMAL(15,2) NOT NULL COMMENT '券别金额',
  balance DECIMAL(15,2) NOT NULL COMMENT '券别余额',
  release_tm TIME  COMMENT '发放时间',
  release_dt DATE  COMMENT '发放日期',
  coupon_val_tm DATETIME COMMENT '券别有效日期',
  coupon_inval_tm DATETIME COMMENT '券别失效日期',
  status VARCHAR(2) NOT NULL COMMENT '状态 01-未使用 02-已使用 03-已过期 04-冻结 05-待激活 06-批量过期 07-退款',
  order_no varchar(25)  COMMENT '最后使用订单号',
  order_amt DECIMAL(15,2) COMMENT '订单金额',
  modify_time DATETIME NOT NULL COMMENT '修改时间',
  create_time DATETIME NOT NULL COMMENT '创建时间'
);

##海币交易流水表- sea_ccy_seq
CREATE TABLE mkm_sea_ccy_seq(
  seq VARCHAR(32) NOT NULL PRIMARY KEY,
  channel VARCHAR(6) ,
  atv_id VARCHAR(25),
  mk_tool VARCHAR(2) NOT NULL ,
  type VARCHAR(2)    NOT NULL,
  user_id VARCHAR(32) NOT NULL,
  inst_id VARCHAR(32) ,
  mobile VARCHAR(20) ,
  count INT NOT NULL,
  release_tm TIME ,
  release_dt DATE,
  status VARCHAR(2) NOT NULL,
  order_no VARCHAR (25),
  order_amt DECIMAL(15,2) ,
  modify_time DATETIME NOT NULL,
  create_time DATETIME NOT NULL,
  ori_time DATETIME ,
  ori_seq VARCHAR(25),
  msg_cd VARCHAR(10),
  msg_info VARCHAR(50),
  val_refund INTEGER,
  gmoblie VARCHAR (20),
  guser VARCHAR (32)
);
ALTER TABLE mkm_sea_ccy_seq COMMENT '海币交易流水表';
ALTER TABLE mkm_sea_ccy_seq MODIFY  seq VARCHAR(32) COMMENT '流水';
ALTER TABLE mkm_sea_ccy_seq MODIFY  channel VARCHAR(6) COMMENT '渠道';
ALTER TABLE mkm_sea_ccy_seq MODIFY  atv_id VARCHAR(25) COMMENT '活动id';
ALTER TABLE mkm_sea_ccy_seq MODIFY  mk_tool VARCHAR(2) COMMENT '券别类型';
ALTER TABLE mkm_sea_ccy_seq MODIFY   user_id VARCHAR(32) COMMENT '用户id';
ALTER TABLE mkm_sea_ccy_seq MODIFY   inst_id VARCHAR(32) COMMENT '商户id';
ALTER TABLE mkm_sea_ccy_seq MODIFY   mobile VARCHAR(20) COMMENT '手机号';
ALTER TABLE mkm_sea_ccy_seq MODIFY   type VARCHAR(2) COMMENT '流水类型 00-购买 01-发放 02-核销';
ALTER TABLE mkm_sea_ccy_seq MODIFY   count INT COMMENT '海币交易数量';
ALTER TABLE mkm_sea_ccy_seq MODIFY   release_tm TIME COMMENT '发放时间';
ALTER TABLE mkm_sea_ccy_seq MODIFY  release_dt DATE COMMENT '发放日期';
ALTER TABLE mkm_sea_ccy_seq MODIFY   mk_tool VARCHAR(2) COMMENT '营销工具';
ALTER TABLE mkm_sea_ccy_seq MODIFY  status VARCHAR(2) COMMENT '状态 0-失败 1-成功 2-异常';
ALTER TABLE mkm_sea_ccy_seq MODIFY   order_no VARCHAR(25) COMMENT '订单号';
ALTER TABLE mkm_sea_ccy_seq MODIFY   order_amt DECIMAL(15,2) COMMENT '订单金额';
ALTER TABLE mkm_sea_ccy_seq MODIFY COLUMN modify_time DATETIME COMMENT '修改时间';
ALTER TABLE mkm_sea_ccy_seq MODIFY COLUMN create_time DATETIME COMMENT '创建时间';
ALTER TABLE mkm_sea_ccy_seq MODIFY COLUMN ori_time DATETIME COMMENT '原交易时间';
ALTER TABLE mkm_sea_ccy_seq MODIFY  msg_cd VARCHAR(10) COMMENT '错误码';
ALTER TABLE mkm_sea_ccy_seq MODIFY  msg_cd VARCHAR(10) COMMENT '错误码';
ALTER TABLE mkm_sea_ccy_seq MODIFY  val_refund INTEGER COMMENT '可撤销海币数量';
ALTER TABLE mkm_sea_ccy_seq MODIFY  gmoblie VARCHAR(20) COMMENT '可撤销海币数量';
ALTER TABLE mkm_sea_ccy_seq MODIFY  guser VARCHAR(32) COMMENT '可撤销海币数量';

##用户海币明细表
##sea_ccy_detail
CREATE TABLE mkm_sea_ccy_detail(
  mk_tool VARCHAR(2) NOT NULL COMMENT '营销工具类型 02-海币',
  user_id VARCHAR(32) NOT NULL COMMENT '用户id',
  mobile VARCHAR(20) COMMENT '用户手机号',
  count INT NOT NULL COMMENT ' 海币余额',
  release_tm TIME  COMMENT '发放时间',
  release_dt DATE  COMMENT '发放日期',
  status VARCHAR(2) NOT NULL COMMENT '状态 01-正常 02-冻结',
  order_no varchar(25) COMMENT '最后使用订单号',
  modify_time DATETIME NOT NULL COMMENT '修改时间',
  create_time DATETIME NOT NULL COMMENT '创建时间',
  PRIMARY KEY (user_id)
);

CREATE TABLE `mkm_batch_file_info` (
  `rec_no` varchar(32) NOT NULL COMMENT '批量序号',
  `opr_typ` char(1) NOT NULL COMMENT '操作类型，1-批量过期，2-批量延期，3-批量冻结，4-批量解冻',
  `coupon_no` varchar(25) NOT NULL COMMENT '电子券号',
  `mbl_no` varchar(20) NOT NULL COMMENT '手机号',
  `release_dt` date NOT NULL COMMENT '发放日期',
  `amt` decimal(18,2) NOT NULL COMMENT '金额',
  `delay_tm` datetime  COMMENT '延期时间',
  `opr_sts` char(1) NOT NULL COMMENT '操作类型'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `mkm_batch_file_rec` (
  `rec_no` varchar(32) NOT NULL AUTO_INCREMENT COMMENT '批量序号',
  `batch_file` varchar(128) NOT NULL COMMENT '批量文件名',
  `ac_id` varchar(25) DEFAULT NULL COMMENT '活动id',
  `process_dt` datetime DEFAULT NULL COMMENT '处理时间',
  `tot_num` int(11) DEFAULT NULL COMMENT '总交易数',
  `tot_amt` decimal(18,2) DEFAULT NULL COMMENT '总交易金额',
  `success_num` int(11) DEFAULT NULL COMMENT '处理成功数',
  `success_amt` decimal(18,2) DEFAULT NULL COMMENT '成功金额',
  `failure_num` int(11) DEFAULT NULL COMMENT '处理失败数',
  `failure_amt` decimal(18,2) DEFAULT NULL COMMENT '失败金额',
  `opr_typ` char(1) NOT NULL COMMENT '操作类型，1-批量过期，2-批量延期，3-批量冻结，4-批量解冻',
  `process_sts` char(1) NOT NULL COMMENT '处理状态，0-处理中，1-处理完成',
  `result_file` varchar(128) DEFAULT NULL COMMENT '回盘文件路径',
  `upd_opr_id` varchar(10) NOT NULL COMMENT '操作员id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  `cre_opr_id` varchar(255) DEFAULT NULL,
  `mkm_batch_file_rec` int(11) DEFAULT NULL,
  PRIMARY KEY (`rec_no`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COMMENT='营销批量处理信息表'

create table if not exists mkm_accounting_seq (
  acm_seq VARCHAR(32) NOT NULL,
  atv_id VARCHAR (25) ,
  item_d VARCHAR(25),
  item_c VARCHAR(25),
  acm_status VARCHAR(2),
  trace_tm DATETIME,
  msg_cd VARCHAR(10),
  msg_info VARCHAR(50),
  amt DECIMAL(15,2) ,
  create_time datetime       NOT NULL ,
  modify_time datetime       NOT NULL,
  primary key (acm_seq)
);
ALTER TABLE mkm_accounting_seq MODIFY COLUMN acm_seq VARCHAR(32) COMMENT '账务登记流水';
ALTER TABLE mkm_accounting_seq MODIFY COLUMN atv_id VARCHAR(25) COMMENT '活动id';
ALTER TABLE mkm_accounting_seq MODIFY COLUMN item_d VARCHAR(25) COMMENT '借方科目号';
ALTER TABLE mkm_accounting_seq MODIFY COLUMN item_c VARCHAR(25) COMMENT '借方科目号';
ALTER TABLE mkm_accounting_seq MODIFY COLUMN acm_status VARCHAR(25) COMMENT '记账状态 1-成功 0-失败 2-异常';
ALTER TABLE mkm_accounting_seq MODIFY COLUMN msg_cd VARCHAR(10) COMMENT '记账错误码';
ALTER TABLE mkm_accounting_seq MODIFY COLUMN amt DECIMAL(15,2) COMMENT '交易金额';
ALTER TABLE mkm_accounting_seq MODIFY COLUMN msg_info VARCHAR(50) COMMENT '记账错误信息';


ALTER TABLE mkm_coupon_seq ADD INDEX query_oriSeq ( type, order_no, status );
ALTER TABLE mkm_sea_ccy_seq ADD INDEX query_oriSeq ( type, order_no, status);
ALTER TABLE mkm_coupon_detail ADD INDEX coupon_no_and_status ( coupon_no, status);

drop table mkm_atv_user;
create table if not exists mkm_atv_user (
  atv_id      varchar(25)    not null comment '活动编号',
  user_id     varchar(25)        not null comment '用户编号',
  create_time datetime       not null comment '创建时间',
  modify_time datetime       not null comment '修改时间',
  primary key (atv_id, user_id)
);
ALTER TABLE mkm_atv_user COMMENT '用户活动关联表';

drop table mkm_atv_rule;
create table if not exists mkm_atv_rule (
  atv_id      varchar(25)    not null comment '活动编号',
  create_time datetime       not null comment '创建时间',
  modify_time datetime       not null comment '修改时间',
  crt_user_opr VARCHAR(20) comment '操作员id',
  receive_times INT comment '单用户领取次数',
  receive_cycle VARCHAR(10) comment '领取次数的周期 day 天 month 月 year 年',
  user_scope VARCHAR(3) comment '用户范围',
  start_days int comment '领用后开始生效的天数',
  coupon_val_days int comment '券别有效天数',
  coupon_inval_tm DATETIME comment '券别最后失效时间',
  min_amt DECIMAL(15,2) comment '订单最大金额',
  max_amt DECIMAL(15,2) comment '订单最小金额',
  primary key (atv_id)
);
ALTER TABLE mkm_atv_rule COMMENT '活动规则表';

ALTER TABLE mkm_activity Add COLUMN coupon_name VARCHAR(100) COMMENT '券别名称';
ALTER TABLE mkm_activity Add COLUMN cost_side VARCHAR(50) COMMENT '费用承担方';
ALTER TABLE mkm_accounting_seq Add COLUMN type VARCHAR(3) COMMENT '交易类型';
ALTER TABLE mkm_coupon_detail add COLUMN batchFlag VARCHAR(32) COMMENT '批次更新成功标志 标志为批次号';
ALTER TABLE mkm_batch_file_info add COLUMN delay_tm datetime COMMENT '延期时间';
ALTER TABLE mkm_coupon_detail add COLUMN discount DECIMAL(15,2) COMMENT '折扣';
ALTER TABLE mkm_coupon_detail add COLUMN coupon_name VARCHAR(100) COMMENT '券别名称';
ALTER TABLE mkm_coupon_detail MODIFY COLUMN amt DECIMAL(15,2) null COMMENT '单券金额' ;


