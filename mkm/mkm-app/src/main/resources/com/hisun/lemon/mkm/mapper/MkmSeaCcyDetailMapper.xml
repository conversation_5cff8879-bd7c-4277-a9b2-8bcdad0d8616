<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IMkmSeaCcyDetailDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmSeaCcyDetailDO" >
        <id column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="count" property="count" jdbcType="INTEGER" />
        <result column="release_tm" property="releaseTm" jdbcType="TIME" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        user_id, mk_tool, mobile, count, release_tm, release_dt, status, order_no, modify_time,
        create_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from mkm_sea_ccy_detail
        where user_id = #{userId,jdbcType=VARCHAR}  for update
    </select>
    <select id="queryByMobile" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from mkm_sea_ccy_detail
        where mobile = #{mobile,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from mkm_sea_ccy_detail
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmSeaCcyDetailDO" >
        insert into mkm_sea_ccy_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                user_id,
            </if>
            <if test="mkTool != null" >
                mk_tool,
            </if>
            <if test="mobile != null" >
                mobile,
            </if>
            <if test="count != null" >
                count,
            </if>
            <if test="releaseTm != null" >
                release_tm,
            </if>
            <if test="releaseDt != null" >
                release_dt,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="count != null" >
                #{count,jdbcType=INTEGER},
            </if>
            <if test="releaseTm != null" >
                #{releaseTm,jdbcType=TIME},
            </if>
            <if test="releaseDt != null" >
                #{releaseDt,jdbcType=DATE},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmSeaCcyDetailDO" >
        update mkm_sea_ccy_detail
        <set >
            <if test="mkTool != null" >
                mk_tool = #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="count != null" >
                count = #{count,jdbcType=INTEGER},
            </if>
            <if test="releaseTm != null" >
                release_tm = #{releaseTm,jdbcType=TIME},
            </if>
            <if test="releaseDt != null" >
                release_dt = #{releaseDt,jdbcType=DATE},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <update id="updateBymkmSeaCcyDetailDO" parameterType="com.hisun.lemon.mkm.entity.MkmSeaCcyDetailDO" >
        update mkm_sea_ccy_detail
        <set >

            <if test="count != null" >
                count =count - #{count,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>

        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
        <![CDATA[   and count - #{count,jdbcType=DECIMAL} >=0 ]]>
    </update>
</mapper>