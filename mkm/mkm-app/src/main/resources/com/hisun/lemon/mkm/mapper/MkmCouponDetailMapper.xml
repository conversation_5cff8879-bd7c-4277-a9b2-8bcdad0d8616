<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IMkmCouponDetailDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmCouponDetailDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="coupon_no" property="couponNo" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="balance" property="balance" jdbcType="DECIMAL" />
        <result column="release_tm" property="releaseTm" jdbcType="TIME" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="coupon_val_tm" property="couponValTm" jdbcType="TIMESTAMP" />
        <result column="coupon_inval_tm" property="couponInvalTm" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="coupon_name" property="couponName" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="discount" property="discount" jdbcType="DECIMAL" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <resultMap id="CouponDetailBaseResultMap" type="com.hisun.lemon.mkm.res.dto.CouponDetail" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="coupon_no" property="couponNo" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="balance" property="balance" jdbcType="DECIMAL" />
        <result column="release_tm" property="releaseTm" jdbcType="TIME" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="coupon_val_tm" property="couponValTm" jdbcType="TIMESTAMP" />
        <result column="coupon_inval_tm" property="couponInvalTm" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="coupon_name" property="couponName" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="discount" property="discount" jdbcType="DECIMAL" />
        <result column="min_amt" property="minAmt" jdbcType="DECIMAL" />
        <result column="max_amt" property="maxAmt" jdbcType="DECIMAL" />
    </resultMap>

    <resultMap id="sumRemainMap" type="com.hisun.lemon.mkm.entity.MkmActivityDO">
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="remainNum" property="remainNum" jdbcType="INTEGER" />
        <result column="remainAmt" property="remainAmt" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, coupon_no, mk_tool, atv_id, inst_id, user_id, mobile, amt, balance, release_tm, 
        release_dt, coupon_val_tm, coupon_inval_tm, status, order_no,order_amt, modify_time, create_time ,coupon_name,discount
    </sql>

    <select id="chenckMonthTime" resultType="java.lang.Integer"  >
        select count(times) from (select  count(*) times
        from mkm_coupon_detail where user_id=#{userId,jdbcType=VARCHAR}
        and atv_id=#{atvId,jdbcType=VARCHAR} and (status='02' or status='07') and DATE_FORMAT( modify_time, '%Y%m') = DATE_FORMAT( now(), '%Y%m')) as time_tb where  <![CDATA[   time_tb.times < #{times,jdbcType=INTEGER} ]]>
    </select>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from mkm_coupon_detail
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="getByCouponId" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from mkm_coupon_detail
        where coupon_no = #{couponNo,jdbcType=VARCHAR} and (status = '01' or status = '09' )
    </select>
    <select id="getByCouponNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from mkm_coupon_detail
        where coupon_no = #{couponNo,jdbcType=VARCHAR} for update
    </select>
    <select id="queryUserCoupon" resultMap="CouponDetailBaseResultMap"  >
        select distinct
        d.id, d.coupon_no, d.mk_tool, d.atv_id, d.inst_id, d.user_id, d.mobile, d.amt, d.balance, d.release_tm,
        d.release_dt, d.coupon_val_tm, d.coupon_inval_tm, d.status, d.order_no,order_amt, d.modify_time, d.create_time ,d.coupon_name,d.discount,
        r.min_amt ,r.max_amt
        from mkm_coupon_detail d  left join mkm_atv_rule r on d.atv_id = r.atv_id
        <if test="queryUserMkmToolReqDTO.instId != null">
            left join mkm_inst i on d.atv_id = i.atv_id left join mkm_activity a on d.atv_id = a.id
        </if>
        where 1=1

        <if test="queryUserMkmToolReqDTO.orderAmt != null">
            <![CDATA[ and r.min_amt <= #{queryUserMkmToolReqDTO.orderAmt,jdbcType=DECIMAL} and r.max_amt >= #{queryUserMkmToolReqDTO.orderAmt,jdbcType=DECIMAL} ]]>
        </if>

        <if test="queryUserMkmToolReqDTO.instId != null">
            and ((a.inst_id = 'Y' and i.inst_id = #{queryUserMkmToolReqDTO.instId,jdbcType=VARCHAR})  or (a.inst_id = 'N'))
        </if>

        <if test="queryUserMkmToolReqDTO.userId != null">
            and d.user_id = #{queryUserMkmToolReqDTO.userId,jdbcType=VARCHAR}
        </if>

        <if test="queryUserMkmToolReqDTO.couponType != null  ">
            and d.mk_tool in
            <foreach collection="queryUserMkmToolReqDTO.couponType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryUserMkmToolReqDTO.releaseDt != null and queryUserMkmToolReqDTO.releaseDt != ''">
            and DATE_FORMAT( d.release_dt, '%Y%m%d') = #{queryUserMkmToolReqDTO.releaseDt,jdbcType=VARCHAR}
        </if>
        <if test='queryUserMkmToolReqDTO.val == "Y" '>
            and d.status = '01' <![CDATA[   and d.coupon_val_tm <= #{currentLocalDateTime,jdbcType=TIMESTAMP} and   #{currentLocalDateTime,jdbcType=TIMESTAMP} <= d.coupon_inval_tm ]]>

        </if>
        <if test='queryUserMkmToolReqDTO.val == "N" '>
            and ( d.status = '03' OR <![CDATA[     #{currentLocalDateTime,jdbcType=TIMESTAMP} >= d.coupon_inval_tm ]]> )

        </if>
    </select>
    <select id="queryUserCouponCount" resultType="java.lang.Integer"  >
        select
       count(*)
        from mkm_coupon_detail d
        <if test="queryUserMkmToolReqDTO.instId != null">
            left join mkm_inst i on d.atv_id = i.atv_id left join mkm_activity a on d.atv_id = a.id
        </if>

        <if test="queryUserMkmToolReqDTO.orderAmt != null">
            left join mkm_atv_rule r on d.atv_id = r.atv_id
        </if>

        where 1=1

        <if test="queryUserMkmToolReqDTO.orderAmt != null">

            <![CDATA[ and r.min_amt <= #{queryUserMkmToolReqDTO.orderAmt,jdbcType=DECIMAL} and r.max_amt >= #{queryUserMkmToolReqDTO.orderAmt,jdbcType=DECIMAL} ]]>
        </if>
        <if test="queryUserMkmToolReqDTO.instId != null">
            and ((a.inst_id = 'Y' and i.inst_id = #{queryUserMkmToolReqDTO.instId,jdbcType=VARCHAR})  or (a.inst_id = 'N'))
        </if>

        <if test="queryUserMkmToolReqDTO.userId != null">
            and d.user_id = #{queryUserMkmToolReqDTO.userId,jdbcType=VARCHAR}
        </if>

        <if test="queryUserMkmToolReqDTO.couponType != null  ">
            and d.mk_tool in
            <foreach collection="queryUserMkmToolReqDTO.couponType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryUserMkmToolReqDTO.releaseDt != null and queryUserMkmToolReqDTO.releaseDt != ''">
            and DATE_FORMAT( d.release_dt, '%Y%m%d') = #{queryUserMkmToolReqDTO.releaseDt,jdbcType=VARCHAR}
        </if>
        <if test='queryUserMkmToolReqDTO.val == "Y" '>
            and d.status = '01' <![CDATA[   and d.coupon_val_tm <= #{currentLocalDateTime,jdbcType=TIMESTAMP} and   #{currentLocalDateTime,jdbcType=TIMESTAMP} <= d.coupon_inval_tm ]]>

        </if>
        <if test='queryUserMkmToolReqDTO.val == "N" '>
            and (d.status = '03' or  <![CDATA[      #{currentLocalDateTime,jdbcType=TIMESTAMP} >= d.coupon_inval_tm ]]>)

        </if>
    </select>
    <delete id="delete" parameterType="java.lang.Integer" >
        delete from mkm_coupon_detail
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmCouponDetailDO" >
        insert into mkm_coupon_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="couponNo != null" >
                coupon_no,
            </if>
            <if test="mkTool != null" >
                mk_tool,
            </if>
            <if test="atvId != null" >
                atv_id,
            </if>
            <if test="instId != null" >
                inst_id,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="mobile != null" >
                mobile,
            </if>
            <if test="amt != null" >
                amt,
            </if>
            <if test="balance != null" >
                balance,
            </if>
            <if test="releaseTm != null" >
                release_tm,
            </if>
            <if test="releaseDt != null" >
                release_dt,
            </if>
            <if test="couponValTm != null" >
                coupon_val_tm,
            </if>
            <if test="couponInvalTm != null" >
                coupon_inval_tm,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="orderAmt != null" >
                order_amt,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="discount != null" >
                discount,
            </if>
            <if test="couponName != null" >
                coupon_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="couponNo != null" >
                #{couponNo,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="atvId != null" >
                #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="instId != null" >
                #{instId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="amt != null" >
                #{amt,jdbcType=DECIMAL},
            </if>
            <if test="balance != null" >
                #{balance,jdbcType=DECIMAL},
            </if>
            <if test="releaseTm != null" >
                #{releaseTm,jdbcType=TIME},
            </if>
            <if test="releaseDt != null" >
                #{releaseDt,jdbcType=DATE},
            </if>
            <if test="couponValTm != null" >
                #{couponValTm,jdbcType=TIMESTAMP},
            </if>
            <if test="couponInvalTm != null" >
                #{couponInvalTm,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="discount != null" >
                #{discount,jdbcType=DECIMAL},
            </if>
            <if test="couponName != null" >
                #{couponName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmCouponDetailDO" >
        update mkm_coupon_detail
        <set >
            <if test="couponNo != null" >
                coupon_no = #{couponNo,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                mk_tool = #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="atvId != null" >
                atv_id = #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="instId != null" >
                inst_id = #{instId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="amt != null" >
                amt = #{amt,jdbcType=DECIMAL},
            </if>
            <if test="balance != null" >
                balance = #{balance,jdbcType=DECIMAL},
            </if>
            <if test="releaseTm != null" >
                release_tm = #{releaseTm,jdbcType=TIME},
            </if>
            <if test="releaseDt != null" >
                release_dt = #{releaseDt,jdbcType=DATE},
            </if>
            <if test="couponValTm != null" >
                coupon_val_tm = #{couponValTm,jdbcType=TIMESTAMP},
            </if>
            <if test="couponInvalTm != null" >
                coupon_inval_tm = #{couponInvalTm,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                order_amt = #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="discount != null" >
                discount = #{discount,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="couponName != null" >
                coupon_name = #{couponName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="batchInvalidCoupon"  >
        update mkm_coupon_detail
          set status = '03' ,  modify_time =  now()
        where status in ('01' , '05')  <![CDATA[   and now() > coupon_inval_tm ]]>
    </update>
    <select id="getSumInvalidAmt" resultMap="sumRemainMap" >
        select atv_id as id , sum(amt)*(-1) as remainamt , count(amt) * (-1) as remainnum from mkm_coupon_detail
        where status in ('01' , '05')  <![CDATA[   and now() > coupon_inval_tm ]]> group by atv_id
    </select>
</mapper>