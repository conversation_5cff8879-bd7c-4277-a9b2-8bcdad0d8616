<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.ICouponUsageDetailsDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.CouponUsageDetailsDO" >
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="mobile" property="mblNo" jdbcType="VARCHAR" />
        <result column="release_tm" property="releaseTm" jdbcType="TIME" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="amt" property="couponAmt" jdbcType="DECIMAL" />
        <result column="balance" property="balance" jdbcType="DECIMAL" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
    </resultMap>

    <select id="selectUsageByMblNoAndBegDt" resultMap="BaseResultMap">
        select
            atv_id, mobile, release_tm, release_dt, amt, balance, inst_id, order_no, order_amt
        from mkm_coupon_detail
        where
            <if test="mblNo != null" >
                mobile = #{mblNo,jdbctype=VARCHAR} and
            </if>
            <if test="release_dt != null" >
                release_dt > #{begDt,jdbctype=date} and
            </if>
            1 = 1
        order by release_dt asc
    </select>


</mapper>