<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IFAQManagerDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.FAQManagerDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="question_content" property="questionContent" jdbcType="VARCHAR" />
        <result column="answer_content" property="answerContent" jdbcType="VARCHAR" />
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="updated_date" property="updatedDate" jdbcType="TIMESTAMP" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="deleted_flag" property="deletedFlag" jdbcType="VARCHAR" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, question_content, answer_content, created_date, created_by, updated_date, updated_by, deleted_flag
    </sql>
    
    <!-- 根据条件查询常见问题列表 -->
    <select id="queryFAQList" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM question_answer_info
        WHERE deleted_flag = 'N'
        <if test="questionContent != null and questionContent != ''">
            AND question_content LIKE CONCAT('%', #{questionContent}, '%')
        </if>
        <if test="language != null and language != ''">
            AND language = #{language}
        </if>
        <if test="id != null">
            AND id = #{id}
        </if>
        ORDER BY updated_date DESC
    </select>
</mapper> 