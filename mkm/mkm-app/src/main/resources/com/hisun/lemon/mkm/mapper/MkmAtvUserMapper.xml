<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IMkmAtvUserDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmAtvUserDO" >
        <id column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <id column="user_id" property="userId" jdbcType="CHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        atv_id, user_id, create_time, modify_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from mkm_atv_user
        where atv_id = #{atvId,jdbcType=VARCHAR}
          and user_id = #{userId,jdbcType=CHAR}
    </select>

    <delete id="delete" parameterType="map" >
        delete from mkm_atv_user
        where atv_id = #{atvId,jdbcType=VARCHAR}
          and user_id = #{userId,jdbcType=CHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmAtvUserDO" >
        insert into mkm_atv_user
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="atvId != null" >
                atv_id,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="atvId != null" >
                #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmAtvUserDO" >
        update mkm_atv_user
        <set >
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where atv_id = #{atvId,jdbcType=VARCHAR}
          and user_id = #{userId,jdbcType=CHAR}
    </update>
</mapper>