<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IBatchFileRecDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.BatchFileRecDO" >
        <result column="rec_no" property="recNo" jdbcType="VARCHAR" />
        <result column="batch_file" property="batchFile" jdbcType="VARCHAR" />
        <result column="ac_id" property="acId" jdbcType="VARCHAR" />
        <result column="process_dt" property="processDt" jdbcType="TIMESTAMP" />
        <result column="tot_num" property="totNum" jdbcType="INTEGER" />
        <result column="tot_amt" property="totAmt" jdbcType="DECIMAL" />
        <result column="success_num" property="successNum" jdbcType="INTEGER" />
        <result column="success_amt" property="successAmt" jdbcType="DECIMAL" />
        <result column="failure_num" property="failureNum" jdbcType="INTEGER" />
        <result column="failure_amt" property="failureAmt" jdbcType="DECIMAL" />
        <result column="opr_typ" property="oprTyp" jdbcType="CHAR" />
        <result column="process_sts" property="processSts" jdbcType="CHAR" />
        <result column="result_file" property="resultFile" jdbcType="VARCHAR" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="selectBatchRecByRecNo" parameterType="java.lang.String">
        SELECT rec_no, batch_file, ac_id, process_dt, tot_num, tot_amt, success_num, success_amt, failure_num,
            failure_amt, opr_typ, process_sts, result_file, modify_time FROM mkm_batch_file_rec
        WHERE rec_no = #{rec_no,jdbcType=VARCHAR}
    </select>


    <update id="updateBatchRec">
        UPDATE mkm_batch_file_rec
        <set>
            <if test="processDt != null" >
                process_dt = #{processDt,jdbcType=TIMESTAMP},
            </if>
            <if test="totNum != null" >
                tot_num = #{totNum,jdbcType=TIMESTAMP},
            </if>
            <if test="totAmt != null" >
                tot_amt = #{totAmt,jdbcType=DECIMAL},
            </if>
            <if test="successNum != null" >
                success_num = #{successNum,jdbcType=TIMESTAMP},
            </if>
            <if test="successAmt != null" >
                success_amt = #{successAmt,jdbcType=DECIMAL},
            </if>
            <if test="failureNum != null" >
                failure_num = #{failureNum,jdbcType=TIMESTAMP},
            </if>
            <if test="failureAmt != null" >
                failure_amt = #{failureAmt,jdbcType=DECIMAL},
            </if>
            <if test="processSts != null" >
                process_sts = #{processSts,jdbcType=CHAR},
            </if>
            <if test="resultFile != null" >
                result_file = #{resultFile,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where rec_no = #{recNo,jdbcType=VARCHAR}
    </update>

</mapper>