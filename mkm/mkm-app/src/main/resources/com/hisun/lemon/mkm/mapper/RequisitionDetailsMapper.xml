<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IRequisitionDetailsDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.RequisitionDetailsDO" >
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="mkmType" property="mkmTyp" jdbcType="VARCHAR" />
        <result column="mobile" property="mblNo" jdbcType="VARCHAR" />
        <result column="release_tm" property="releaseTm" jdbcType="TIME" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="status" property="couponSts" jdbcType="VARCHAR" />
        <result column="order_amt" property="couponAmt" jdbcType="DECIMAL" />
    </resultMap>

    <select id="selectRequisitionByMblNoAndBegDt" resultMap="BaseResultMap">
        select result.* from (
            select
                atv_id, 1 as mkmType, mobile, release_dt, release_tm, status, amt as order_amt
            from
                mkm_coupon_detail
            where
                <if test="mblNo != null" >
                    mobile = #{mblNo,jdbctype=varchar} and
                </if>
                <if test="release_dt != null" >
                    release_dt > #{begDt,jdbctype=date} and
                </if>
                1 = 1
            union
            select
                atv_id, 2 as mkmType, mobile, release_dt, release_tm, status, order_amt
            from
                mkm_sea_ccy_seq
            where
                type = '01' and status = '1' and
                <if test="mblNo != null" >
                    mobile = #{mblNo,jdbctype=varchar} and
                </if>
                <if test="release_dt != null" >
                    release_dt > #{begDt,jdbctype=date} and
                </if>
                1 = 1
        ) as result
        order by result.release_dt asc
    </select>


</mapper>