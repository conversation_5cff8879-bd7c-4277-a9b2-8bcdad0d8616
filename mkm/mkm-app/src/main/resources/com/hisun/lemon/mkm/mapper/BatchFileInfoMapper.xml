<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IBatchFileInfoDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.BatchFileInfoDo" >
        <result column="rec_no" property="recNo" jdbcType="VARCHAR" />
        <result column="opr_typ" property="oprTyp" jdbcType="CHAR" />
        <result column="coupon_no" property="couponNo" jdbcType="VARCHAR" />
        <result column="mbl_no" property="mblNo" jdbcType="VARCHAR" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="delay_tm" property="delayTm" jdbcType="TIMESTAMP" />
        <result column="opr_sts" property="oprSts" jdbcType="CHAR" />
    </resultMap>

    <insert id="batchInsert">
        insert into mkm_batch_file_info (rec_no, opr_typ, coupon_no, mbl_no, release_dt, amt, opr_sts ,delay_tm) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.recNo}, #{item.oprTyp} ,#{item.couponNo}, #{item.mblNo}, #{item.releaseDt}, #{item.amt}, #{item.oprSts} ,  #{item.delayTm})
        </foreach>
    </insert>

    <select id="selectTotAmtByRecNo" parameterType="java.lang.String" resultType="java.math.BigDecimal">
        select sum(amt) as totAmt from mkm_batch_file_info where rec_no = #{recNo,jdbcType=VARCHAR}
    </select>

    <select id="selectFileInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select rec_no, opr_typ, coupon_no, mbl_no, release_dt, amt, opr_sts
        from mkm_batch_file_info where rec_no = #{recNo,jdbcType=VARCHAR} and opr_sts <![CDATA[<>]]> '0'
    </select>

    <update id="updateStsByCompareDetailTable">
        update mkm_batch_file_info  set chk_sts = #{chkSts,jdbcType=CHAR}, modify_time = now()
        where rec_no = #{recNo,jdbcType=VARCHAR} and coupon_no in
        <foreach collection="list" item="list" index="index" open="(" close=")" separator=",">
            #{list}
        </foreach>
    </update>

    <!-- 过期 -->
    <update id="updateCouponExpiredByTempSts">
        update mkm_coupon_detail detail set detail.status = '06' where detail.coupon_no in (
                select info.coupon_no from mkm_batch_file_info info
                where chk_sts = '1' and rec_no = #{recNo,jdbcType=VARCHAR}
        )
    </update>

    <!-- 延期 -->
    <update id="updateCouponExtensionByTempSts">
        update mkm_coupon_detail detail set detail.coupon_inval_tm = (
            select TIMESTAMP(date_format(info.release_dt, '%Y-%m-%d')) from mkm_batch_file_info info
            where info.coupon_no = detail.coupon_no and info.rec_no = #{recNo,jdbcType=VARCHAR}
        ) where detail.coupon_no in (
            select info.coupon_no from mkm_batch_file_info info
            where chk_sts = '1' and rec_no = #{recNo,jdbcType=VARCHAR}
        )
    </update>

    <!-- 冻结 -->
    <update id="updateCouponStsToFreeze">
        update mkm_coupon_detail detail set detail.status = '04' where detail.coupon_no in (
            select info.coupon_no from mkm_batch_file_info info
            where chk_sts = '1' and rec_no = #{recNo,jdbcType=VARCHAR}
        )
    </update>

    <!-- 解冻 -->
    <update id="updateCouponStsToUnFreeze">
        update mkm_coupon_detail detail set detail.status = '01' where detail.coupon_no in (
            select info.coupon_no from mkm_batch_file_info info
            where chk_sts = '5' and rec_no = #{recNo,jdbcType=VARCHAR}
        )
    </update>

    <select id="selectRowNumByChkSts" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*) rowNum from mkm_batch_file_info where rec_no = #{recNo,jdbcType=VARCHAR}
        and chk_sts = #{chkSts,jdbcType=CHAR}
    </select>

    <select id="selectAllRowNumByRecNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*) rowNum from mkm_batch_file_info where rec_no = #{recNo,jdbcType=VARCHAR}
    </select>

    <select id="selectSuccessAmtByRecNo" parameterType="java.lang.String" resultType="java.math.BigDecimal">
        select sum(amt) totAmt from mkm_batch_file_info where rec_no = #{recNo,jdbcType=VARCHAR}
        and chk_sts = #{chkSts,jdbcType=CHAR}
    </select>
    <update id="oprData" parameterType="java.lang.String" >
        <!--<foreach collection="list" item="item" index="index" separator=";">-->

            <!--call mkm_batch_opr ( #{item.couponNo,mode=IN ,jdbcType=VARCHAR},#{oprTyp,mode=IN ,jdbcType=VARCHAR},#{invalidTm,mode=IN ,jdbcType=TIMESTAMP},#{pResult, mode=OUT,jdbcType=VARCHAR})-->

        <!--</foreach>-->

          update mkm_coupon_detail c,mkm_batch_file_info b SET
            <if test='oprTyp == "1"'>
                c.status = '03', c.modify_time = now() , c.batchFlag =   #{recNo,jdbcType=VARCHAR}
            </if>
            <if test='oprTyp == "2"'>
                c.coupon_inval_tm = b.delay_tm, c.modify_time = now() , c.batchFlag =   #{recNo,jdbcType=VARCHAR}
            </if>
            <if test='oprTyp == "3"'>
                c.status = '05', c.modify_time = now() ,c.batchFlag =   #{recNo,jdbcType=VARCHAR}
            </if>
            <if test='oprTyp == "4"'>
                c.status = '01', c.modify_time = now() ,c.batchFlag =   #{recNo,jdbcType=VARCHAR}
            </if>
            where
        c.coupon_no  = b.coupon_no and b.rec_no =  #{recNo,jdbcType=VARCHAR} and c.amt  = b.amt

        <if test='oprTyp == "1"'>
            and c.status in ('01' ,'05')
        </if>
        <if test='oprTyp == "3"'>
            and status = '01'
        </if>
        <if test='oprTyp == "4"'>
            and c.status = '05'
        </if>
        <if test='oprTyp == "2"'>
            and c.status in ('01' ,'05')
        </if>
    </update>

    <update id="updateInfo" parameterType="java.lang.String" >
        update mkm_batch_file_info b, mkm_coupon_detail c set b.opr_sts = '1' where b.coupon_no = c.coupon_no and c.batchFlag = #{recNo ,jdbcType=VARCHAR }
    </update>

    <update id="deleteByRecNo" parameterType="java.lang.String" >
        delete from mkm_batch_file_info where rec_no = #{recNo,jdbcType=VARCHAR}
    </update>

    <!--<parameterMap type="java.util.Map" id="getUserCountMap">-->
        <!--<parameter property="recNo" mode="IN" jdbcType="INTEGER"/>-->
        <!--<parameter property="oprTyp" mode="IN" jdbcType="INTEGER"/>-->
        <!--<parameter property="invalidTm" mode="IN" jdbcType="INTEGER"/>-->
        <!--<parameter property="pResult" mode="OUT" jdbcType="VARCHAR"/>-->
    <!--</parameterMap>-->
</mapper>