<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IIMkmAccountingSeqDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmAccountingSeqDO" >
        <id column="acm_seq" property="acmSeq" jdbcType="VARCHAR" />
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="item_d" property="itemD" jdbcType="VARCHAR" />
        <result column="item_c" property="itemC" jdbcType="VARCHAR" />
        <result column="acm_status" property="acmStatus" jdbcType="VARCHAR" />
        <result column="trace_tm" property="traceTm" jdbcType="TIMESTAMP" />
        <result column="msg_cd" property="msgCd" jdbcType="VARCHAR" />
        <result column="msg_info" property="msgInfo" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        acm_seq, item_d, item_c, acm_status, trace_tm, msg_cd, msg_info, create_time, modify_time,amt,type
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from mkm_accounting_seq
        where acm_seq = #{acmSeq,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from mkm_accounting_seq
        where acm_seq = #{acmSeq,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmAccountingSeqDO" >
        insert into mkm_accounting_seq
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="acmSeq != null" >
                acm_seq,
            </if>
            <if test="atvId != null" >
                atv_id,
            </if>
            <if test="itemD != null" >
                item_d,
            </if>
            <if test="itemC != null" >
                item_c,
            </if>
            <if test="acmStatus != null" >
                acm_status,
            </if>
            <if test="traceTm != null" >
                trace_tm,
            </if>
            <if test="msgCd != null" >
                msg_cd,
            </if>
            <if test="msgInfo != null" >
                msg_info,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="amt != null" >
                amt,
            </if>
            <if test="type != null" >
                type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="acmSeq != null" >
                #{acmSeq,jdbcType=VARCHAR},
            </if>
            <if test="atvId != null" >
                #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="itemD != null" >
                #{itemD,jdbcType=VARCHAR},
            </if>
            <if test="itemC != null" >
                #{itemC,jdbcType=VARCHAR},
            </if>
            <if test="acmStatus != null" >
                #{acmStatus,jdbcType=VARCHAR},
            </if>
            <if test="traceTm != null" >
                #{traceTm,jdbcType=TIMESTAMP},
            </if>
            <if test="msgCd != null" >
                #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="amt != null" >
                #{amt,jdbcType=DECIMAL},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmAccountingSeqDO" >
        update mkm_accounting_seq
        <set >
            <if test="itemD != null" >
                item_d = #{itemD,jdbcType=VARCHAR},
            </if>
            <if test="itemC != null" >
                item_c = #{itemC,jdbcType=VARCHAR},
            </if>
            <if test="acmStatus != null" >
                acm_status = #{acmStatus,jdbcType=VARCHAR},
            </if>
            <if test="traceTm != null" >
                trace_tm = #{traceTm,jdbcType=TIMESTAMP},
            </if>
            <if test="msgCd != null" >
                msg_cd = #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                msg_info = #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where acm_seq = #{acmSeq,jdbcType=VARCHAR}
    </update>
</mapper>