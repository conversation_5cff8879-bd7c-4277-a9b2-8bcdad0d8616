<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IMkmSeaCcySeqDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmSeaCcySeqDO" >
        <id column="seq" property="seq" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="count" property="count" jdbcType="INTEGER" />
        <result column="release_tm" property="releaseTm" jdbcType="TIME" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="ori_time" property="oriTime" jdbcType="TIMESTAMP" />
        <result column="ori_seq" property="oriSeq" jdbcType="VARCHAR" />
        <result column="msg_cd" property="msgCd" jdbcType="VARCHAR" />
        <result column="msg_info" property="msgInfo" jdbcType="VARCHAR" />
        <result column="val_refund" property="valRefund" jdbcType="INTEGER" />
        <result column="channel" property="channel" jdbcType="INTEGER" />
        <result column="gmoblie" property="gmobile" jdbcType="VARCHAR" />
        <result column="guser" property="guserId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        seq, mk_tool, type,atv_id, user_id, mobile, count, release_tm, release_dt, status, order_no,
        order_amt, modify_time, create_time, ori_time, ori_seq, msg_cd, msg_info,val_refund,channel,inst_id,gmoblie,guser
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from mkm_sea_ccy_seq
        where seq = #{seq,jdbcType=VARCHAR}
    </select>
    <select id="getValRefund" resultMap="BaseResultMap"  >
        select
        <include refid="Base_Column_List" />
        from mkm_sea_ccy_seq
        where order_no = #{oriSeq,jdbcType=VARCHAR}
        <if test = "type == '02'" >
            and type = #{type,jdbcType=VARCHAR}  and (status = '1' or status = '5')
        </if>
        <if test = "type == '07'" >
            and (type = '06')  and (status = '1')
        </if>
    </select>
    <select id="checkTimes" resultType="java.lang.Integer"  >
        select count(times) from (select  count(*) times
        from mkm_sea_ccy_seq where user_id=#{userId,jdbcType=VARCHAR}
        and atv_id=#{atvId,jdbcType=VARCHAR} and (status='1' or status='2') and type='01'
        <if test="receiveCycle == 'day'" >
            and DATE_FORMAT( modify_time, '%Y%m%d') = DATE_FORMAT( now(), '%Y%m%d')
        </if>
        <if test="receiveCycle == 'month'" >
           and  DATE_FORMAT( modify_time, '%Y%m') = DATE_FORMAT( now(), '%Y%m')
        </if>
        <if test="receiveCycle == 'year'" >
            and DATE_FORMAT( modify_time, '%Y') = DATE_FORMAT( now(), '%Y')
        </if>
        )
        as time_tb where  <![CDATA[   time_tb.times < #{times,jdbcType=INTEGER} ]]>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from mkm_sea_ccy_seq
        where seq = #{seq,jdbcType=VARCHAR}
    </delete>

    <select id="seaccyTraceDetail" resultMap="BaseResultMap"  parameterType="com.hisun.lemon.mkm.req.dto.SeaccyTraceDetailRepDTO">
        select
        <include refid="Base_Column_List" />
        from mkm_sea_ccy_seq
        where 1=1
        <if test="type != null and type != ''">
            and type = #{type ,jdbcType = VARCHAR}
        </if>
        <if test="orderNo != null and orderNo != ''">
           and  order_no = #{orderNo ,jdbcType = VARCHAR}
        </if>
        <if test="tarceDt != null ">
            and release_dt = #{tarceDt ,jdbcType = DATE}
        </if>
        <if test="mobile != null ">
            and mobile = #{mobile ,jdbcType = VARCHAR}
        </if>
    </select>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmSeaCcySeqDO" >
        insert into mkm_sea_ccy_seq
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="seq != null" >
                seq,
            </if>
            <if test="atvId != null" >
                atv_id,
            </if>
            <if test="mkTool != null" >
                mk_tool,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="mobile != null" >
                mobile,
            </if>
            <if test="count != null" >
                count,
            </if>
            <if test="releaseTm != null" >
                release_tm,
            </if>
            <if test="releaseDt != null" >
                release_dt,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="orderAmt != null" >
                order_amt,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="oriTime != null" >
                ori_time,
            </if>
            <if test="oriSeq != null" >
                ori_seq,
            </if>
            <if test="msgCd != null" >
                msg_cd,
            </if>
            <if test="msgInfo != null" >
                msg_info,
            </if>
            <if test="valRefund != null" >
                val_refund,
            </if>
            <if test="channel != null" >
                channel,
            </if>
            <if test="instId != null" >
                inst_id,
            </if>
            <if test="gmobile != null" >
                gmoblie,
            </if>
            <if test="guserId != null" >
                guser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="seq != null" >
                #{seq,jdbcType=VARCHAR},
            </if>
            <if test="atvId != null" >
                #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="count != null" >
                #{count,jdbcType=INTEGER},
            </if>
            <if test="releaseTm != null" >
                #{releaseTm,jdbcType=TIME},
            </if>
            <if test="releaseDt != null" >
                #{releaseDt,jdbcType=DATE},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="oriTime != null" >
                #{oriTime,jdbcType=TIMESTAMP},
            </if>
            <if test="oriSeq != null" >
                #{oriSeq,jdbcType=VARCHAR},
            </if>
            <if test="msgCd != null" >
                #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="valRefund != null" >
                #{valRefund,jdbcType=INTEGER},
            </if>
            <if test="channel != null" >
                #{channel,jdbcType=INTEGER},
            </if>
            <if test="instId != null" >
                #{instId,jdbcType=VARCHAR},
            </if>
            <if test="gmobile != null" >
                #{gmobile,jdbcType=VARCHAR},
            </if>
            <if test="guserId != null" >
                #{guserId,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmSeaCcySeqDO" >
        update mkm_sea_ccy_seq
        <set >
            <if test="mkTool != null" >
                mk_tool = #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="atvId != null" >
                atv_id = #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="count != null" >
                count = #{count,jdbcType=INTEGER},
            </if>
            <if test="releaseTm != null" >
                release_tm = #{releaseTm,jdbcType=TIME},
            </if>
            <if test="releaseDt != null" >
                release_dt = #{releaseDt,jdbcType=DATE},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                order_amt = #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="oriTime != null" >
                ori_time = #{oriTime,jdbcType=TIMESTAMP},
            </if>
            <if test="oriSeq != null" >
                ori_seq = #{oriSeq,jdbcType=VARCHAR},
            </if>
            <if test="msgCd != null" >
                msg_cd = #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                msg_info = #{msgInfo,jdbcType=VARCHAR},
            </if>
        </set>
        where seq = #{seq,jdbcType=VARCHAR}
    </update>
    <update id="updateValRefund" parameterType="com.hisun.lemon.mkm.entity.MkmSeaCcySeqDO" >
        update mkm_sea_ccy_seq
        <set >

            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>

            <if test="valRefund != null" >
                val_refund =val_refund - #{valRefund,jdbcType=VARCHAR},
            </if>
        </set>
        where seq = #{seq,jdbcType=VARCHAR} and <![CDATA[  val_refund - #{valRefund,jdbcType=INTEGER} >=0  ]]>
    </update>
</mapper>