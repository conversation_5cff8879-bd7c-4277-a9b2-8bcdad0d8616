<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IMkmInstDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmInstDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="total" property="total" jdbcType="INTEGER" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="tot_iss_amt" property="totIssAmt" jdbcType="DECIMAL" />
        <result column="tot_iss_cnt" property="totIssCnt" jdbcType="INTEGER" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, atv_id, inst_id, total, total_amt, tot_iss_amt, tot_iss_cnt, modify_time, create_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from mkm_inst
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="judgeConsumeInst" resultType="java.lang.Integer" parameterType="java.lang.String" >
        select
        count(*)
        from mkm_activity a left join mkm_inst i on a.id = i.atv_id
        where a.id =  #{atvId,jdbcType=VARCHAR} and (i.inst_id = #{instId,jdbcType=VARCHAR} or a.inst_id != 'Y')
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from mkm_inst
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmInstDO" >
        insert into mkm_inst
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="atvId != null" >
                atv_id,
            </if>
            <if test="instId != null" >
                inst_id,
            </if>
            <if test="total != null" >
                total,
            </if>
            <if test="totalAmt != null" >
                total_amt,
            </if>
            <if test="totIssAmt != null" >
                tot_iss_amt,
            </if>
            <if test="totIssCnt != null" >
                tot_iss_cnt,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="atvId != null" >
                #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="instId != null" >
                #{instId,jdbcType=VARCHAR},
            </if>
            <if test="total != null" >
                #{total,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="totIssAmt != null" >
                #{totIssAmt,jdbcType=DECIMAL},
            </if>
            <if test="totIssCnt != null" >
                #{totIssCnt,jdbcType=INTEGER},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmInstDO" >
        update mkm_inst
        <set >
            <if test="atvId != null" >
                atv_id = #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="instId != null" >
                inst_id = #{instId,jdbcType=VARCHAR},
            </if>
            <if test="total != null" >
                total = #{total,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                total_amt = #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="totIssAmt != null" >
                tot_iss_amt = #{totIssAmt,jdbcType=DECIMAL},
            </if>
            <if test="totIssCnt != null" >
                tot_iss_cnt = #{totIssCnt,jdbcType=INTEGER},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByActIdAndIsntId" parameterType="com.hisun.lemon.mkm.entity.MkmInstDO" >
        update mkm_inst
        <set >

            <if test="total != null" >
                total = #{total,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                total_amt = #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="totIssAmt != null" >
                tot_iss_amt = #{totIssAmt,jdbcType=DECIMAL},
            </if>
            <if test="totIssCnt != null" >
                tot_iss_cnt = #{totIssCnt,jdbcType=INTEGER},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where atv_id = #{atvId,jdbcType=VARCHAR} and inst_id = #{instId,jdbcType=VARCHAR}
    </update>
    <update id="updateRelease" parameterType="com.hisun.lemon.mkm.entity.MkmInstDO" >
        update mkm_inst
        <set >

            <if test="total != null" >
                total =total+ #{total,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                total_amt =total_amt + #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="totIssAmt != null" >
                tot_iss_amt =tot_iss_amt + #{totIssAmt,jdbcType=DECIMAL},
            </if>
            <if test="totIssCnt != null" >
                tot_iss_cnt = tot_iss_cnt + #{totIssCnt,jdbcType=INTEGER},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where atv_id = #{atvId,jdbcType=VARCHAR} and inst_id = #{instId,jdbcType=VARCHAR}
    </update>
    <select id="getAllInstIdByActId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select inst_id,id from mkm_inst where atv_id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="findByinstIdAndActId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List" /> from mkm_inst where atv_id = #{atvId,jdbcType=VARCHAR} and inst_id = #{instId,jdbcType=VARCHAR}
    </select>
</mapper>