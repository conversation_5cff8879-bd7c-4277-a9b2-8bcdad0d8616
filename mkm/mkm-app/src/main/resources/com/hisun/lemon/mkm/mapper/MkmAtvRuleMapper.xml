<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IMkmAtvRuleDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmAtvRuleDO" >
        <id column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="crt_user_opr" property="crtUserOpr" jdbcType="VARCHAR" />
        <result column="receive_times" property="receiveTimes" jdbcType="INTEGER" />
        <result column="receive_cycle" property="receiveCycle" jdbcType="VARCHAR" />
        <result column="user_scope" property="userScope" jdbcType="VARCHAR" />
        <result column="start_days" property="startDays" jdbcType="INTEGER" />
        <result column="coupon_val_days" property="couponValDays" jdbcType="INTEGER" />
        <result column="coupon_inval_tm" property="couponInvalTm" jdbcType="TIMESTAMP" />
        <result column="min_amt" property="minAmt" jdbcType="DECIMAL" />
        <result column="max_amt" property="maxAmt" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        atv_id, create_time, modify_time, crt_user_opr, receive_times, receive_cycle, user_scope, 
        start_days, coupon_val_days, coupon_inval_tm, min_amt, max_amt
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from mkm_atv_rule
        where atv_id = #{atvId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from mkm_atv_rule
        where atv_id = #{atvId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmAtvRuleDO" >
        insert into mkm_atv_rule
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="atvId != null" >
                atv_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="crtUserOpr != null" >
                crt_user_opr,
            </if>
            <if test="receiveTimes != null" >
                receive_times,
            </if>
            <if test="receiveCycle != null" >
                receive_cycle,
            </if>
            <if test="userScope != null" >
                user_scope,
            </if>
            <if test="startDays != null" >
                start_days,
            </if>
            <if test="couponValDays != null" >
                coupon_val_days,
            </if>
            <if test="couponInvalTm != null" >
                coupon_inval_tm,
            </if>
            <if test="minAmt != null" >
                min_amt,
            </if>
            <if test="maxAmt != null" >
                max_amt,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="atvId != null" >
                #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="crtUserOpr != null" >
                #{crtUserOpr,jdbcType=VARCHAR},
            </if>
            <if test="receiveTimes != null" >
                #{receiveTimes,jdbcType=INTEGER},
            </if>
            <if test="receiveCycle != null" >
                #{receiveCycle,jdbcType=VARCHAR},
            </if>
            <if test="userScope != null" >
                #{userScope,jdbcType=VARCHAR},
            </if>
            <if test="startDays != null" >
                #{startDays,jdbcType=INTEGER},
            </if>
            <if test="couponValDays != null" >
                #{couponValDays,jdbcType=INTEGER},
            </if>
            <if test="couponInvalTm != null" >
                #{couponInvalTm,jdbcType=TIMESTAMP},
            </if>
            <if test="minAmt != null" >
                #{minAmt,jdbcType=DECIMAL},
            </if>
            <if test="maxAmt != null" >
                #{maxAmt,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmAtvRuleDO" >
        update mkm_atv_rule
        <set >
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="crtUserOpr != null" >
                crt_user_opr = #{crtUserOpr,jdbcType=VARCHAR},
            </if>
            <if test="receiveTimes != null" >
                receive_times = #{receiveTimes,jdbcType=INTEGER},
            </if>
            <if test="receiveCycle != null" >
                receive_cycle = #{receiveCycle,jdbcType=VARCHAR},
            </if>
            <if test="userScope != null" >
                user_scope = #{userScope,jdbcType=VARCHAR},
            </if>
            <if test="startDays != null" >
                start_days = #{startDays,jdbcType=INTEGER},
            </if>
            <if test="couponValDays != null" >
                coupon_val_days = #{couponValDays,jdbcType=INTEGER},
            </if>
            <if test="couponInvalTm != null" >
                coupon_inval_tm = #{couponInvalTm,jdbcType=TIMESTAMP},
            </if>
            <if test="minAmt != null" >
                min_amt = #{minAmt,jdbcType=DECIMAL},
            </if>
            <if test="maxAmt != null" >
                max_amt = #{maxAmt,jdbcType=DECIMAL},
            </if>
        </set>
        where atv_id = #{atvId,jdbcType=VARCHAR}
    </update>
</mapper>