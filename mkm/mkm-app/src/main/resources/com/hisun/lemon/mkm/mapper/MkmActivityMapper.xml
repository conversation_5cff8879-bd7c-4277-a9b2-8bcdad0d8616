<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IMkmActivityDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmActivityDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="atv_nm" property="atvNm" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="examine_status" property="examineStatus" jdbcType="CHAR" />
        <result column="status" property="status" jdbcType="CHAR" />
        <result column="total" property="total" jdbcType="INTEGER" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="discount" property="discount" jdbcType="DECIMAL" />
        <result column="coupon_name" property="couponName" jdbcType="VARCHAR" />
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="remain_num" property="remainNum" jdbcType="INTEGER" />
        <result column="remain_amt" property="remainAmt" jdbcType="DECIMAL" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="crt_user_opr" property="crtUserOpr" jdbcType="VARCHAR" />
        <result column="mdf_user_opr" property="mdfUserOpr" jdbcType="VARCHAR" />
        <result column="aclt_amt" property="acltAmt" jdbcType="DECIMAL" />
        <result column="aclt" property="aclt" jdbcType="INTEGER" />
        <result column="item" property="item" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="cost_side" property="costSide" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="JoinResultMap" type="com.hisun.lemon.mkm.res.dto.MarketActivityDetail" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="atv_nm" property="atvNm" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="examine_status" property="examineStatus" jdbcType="CHAR" />
        <result column="status" property="status" jdbcType="CHAR" />
        <result column="total" property="total" jdbcType="INTEGER" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="discount" property="discount" jdbcType="DECIMAL" />
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="remain_num" property="remainNum" jdbcType="INTEGER" />
        <result column="remain_amt" property="remainAmt" jdbcType="DECIMAL" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="mdf_user_opr" property="mdfUserOpr" jdbcType="VARCHAR" />
        <result column="aclt_amt" property="acltAmt" jdbcType="DECIMAL" />
        <result column="aclt" property="aclt" jdbcType="INTEGER" />
        <result column="item" property="item" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="coupon_name" property="couponName" jdbcType="VARCHAR" />

        <result column="receive_times" property="receiveTimes" jdbcType="INTEGER" />
        <result column="receive_cycle" property="receiveCycle" jdbcType="VARCHAR" />
        <result column="start_days" property="startDays" jdbcType="INTEGER" />
        <result column="coupon_val_days" property="couponValDays" jdbcType="INTEGER" />
        <result column="coupon_inval_tm" property="couponInvalTm" jdbcType="TIMESTAMP" />
        <result column="min_amt" property="minAmt" jdbcType="DECIMAL" />
        <result column="max_amt" property="maxAmt" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, atv_nm, mk_tool, examine_status, status, total, total_amt,  amt,
        begin_time, end_time, remain_num, remain_amt, modify_time, create_time, crt_user_opr, 
        mdf_user_opr,aclt,max_amt ,discount,item,inst_id,cost_side,coupon_name
    </sql>

    <select id="findByfindMarketActivityDTO" resultMap="BaseResultMap" parameterType="com.hisun.lemon.mkm.req.dto.FindMarketActivityReqDTO" >
        select
        <include refid="Base_Column_List" />
        from mkm_activity
        where 1 = 1
        <if test=" id !=null and  id != ''">
           and  id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="atvNm != null and atvNm != ''">
            and atv_nm = #{atvNm,jdbcType=VARCHAR}
        </if>

        <if test="createDate != null and createDate != ''">
            and DATE_FORMAT( create_time, '%Y%m%d') = #{createDate,jdbcType=VARCHAR}
        </if>
         ORDER BY modify_time DESC
    </select>

    <select id="counTotal" resultType="java.lang.Integer" parameterType="com.hisun.lemon.mkm.req.dto.FindMarketActivityReqDTO" >
        select
        count(*)
        from mkm_activity
        where 1 = 1
        <if test=" id !=null and  id != ''">
            and  id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="atvNm != null and atvNm != '' ">
            and atv_nm = #{atvNm,jdbcType=VARCHAR}
        </if>

        <if test="createDate != null and createDate != ''">
            and DATE_FORMAT( create_time, '%Y%m%d') = #{createDate,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="queryByFindMarketActivityDTO"  resultMap="JoinResultMap" parameterType="com.hisun.lemon.mkm.req.dto.FindMarketActivityReqDTO" >
        select
        distinct a.id ,a.atv_nm ,a.mk_tool ,a.total ,a.total_amt ,a.amt ,a.coupon_name ,a.discount ,a.begin_time ,a.end_time ,a.remain_num ,a.remain_amt
        ,a.crt_user_opr,a.aclt_amt ,a.aclt ,a.inst_id, r.receive_times ,r.receive_cycle
        ,r.user_scope,r.start_days,r.coupon_val_days,r.coupon_inval_tm,r.min_amt,r.max_amt
        from mkm_activity a left join mkm_inst i on a.id = i.atv_id left join mkm_atv_user u on a.id = u.atv_id
        left join mkm_atv_rule r on a.id = r.atv_id
        where a.status = '00' and examine_status = '02' <![CDATA[   and a.begin_time <= now() and  now() <= a.end_time ]]>
        <if test=" id !=null and id != '' ">
            and  a.id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="atvNm != null and atvNm != '' ">
            and a.atv_nm = #{atvNm,jdbcType=VARCHAR}
        </if>
        <if test="mkTools != null ">
            and a.mk_tool in
            <foreach collection="mkTools" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="createDate != null and createDate != '' ">
            and DATE_FORMAT( a.create_time, '%Y%m%d') = #{createDate,jdbcType=VARCHAR}
        </if>
        <if test="instId != null and instId != '' ">
            and (i.inst_id = #{instId,jdbcType=VARCHAR} or i.inst_id is null)
        </if>
        <if test="userId != null and userId != '' ">
            and (u.user_Id = #{userId,jdbcType=VARCHAR} or u.user_Id is null)
        </if>
    </select>
    <select id="queryByFindMarketActivityDTOCont"  resultType="java.lang.Integer" parameterType="com.hisun.lemon.mkm.req.dto.FindMarketActivityReqDTO" >
        select count(*) from (select
        distinct a.id
        from mkm_activity a left join mkm_inst i on a.id = i.atv_id left join mkm_atv_user u on a.id = u.atv_id
        left join mkm_atv_rule r on a.id = r.atv_id
        where a.status = '00' and examine_status = '02' <![CDATA[   and a.begin_time <= now() and  now() <= a.end_time ]]>
        <if test=" id !=null and id != '' ">
            and  a.id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="atvNm != null and atvNm != '' ">
            and a.atv_nm = #{atvNm,jdbcType=VARCHAR}
        </if>

        <if test="mkTools != null ">
            and a.mk_tool in
            <foreach collection="mkTools" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="createDate != null and createDate != '' ">
            and DATE_FORMAT( a.create_time, '%Y%m%d') = #{createDate,jdbcType=VARCHAR}
        </if>
        <if test="instId != null and instId != '' ">
            and (i.inst_id = #{instId,jdbcType=VARCHAR} or i.inst_id is null)
        </if>
        <if test="userId != null and userId != '' ">
            and (u.user_Id = #{userId,jdbcType=VARCHAR} or u.user_Id is null)
        </if>
        ) as a
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from mkm_activity
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="queryOptimalActivity" resultMap="BaseResultMap" parameterType="com.hisun.lemon.mkm.req.dto.AutoRealeaseReqDTO" >

        select
            distinct a.*
            from mkm_activity a left join mkm_inst i on a.id = i.atv_id left join mkm_atv_rule r on a.id = r.atv_id
            left join mkm_atv_user u on a.id = u.atv_id
            where a.status = '00' and examine_status = '02' <![CDATA[   and a.begin_time <= now() and  now() <= a.end_time]]>
            and (i.inst_id is null or i.inst_id = #{instId,jdbcType=VARCHAR} ) and (u.atv_id is null  or u.user_id = #{userId,jdbcType=VARCHAR})
            <if test="mkTool != null and mkTool != '' ">
                and a.mk_tool = #{mkTool,jdbcType=VARCHAR}
            </if>
            <if test="orderAmt != null ">
                <![CDATA[    and r.min_amt <= #{orderAmt,jdbcType=DECIMAL} and r.max_amt >=#{orderAmt,jdbcType=DECIMAL}]]>
            </if>
            <if test="mkTool == '04'">
                    order by a.discount ASC
            </if>
            <if test="mkTool == '03' or  mkTool == '01' or mkTool==null or mkTool ==''">
                    order by a.amt desc
            </if>
             limit 0, 1
    </select>

    <select id="getValid" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from mkm_activity
        where id = #{id,jdbcType=VARCHAR} and status = '00' and examine_status = '02'
        <![CDATA[   and begin_time<=now() and  now()<=end_time ]]>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from mkm_activity
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmActivityDO" >
        insert into mkm_activity
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="atvNm != null" >
                atv_nm,
            </if>
            <if test="mkTool != null" >
                mk_tool,
            </if>
            <if test="examineStatus != null" >
                examine_status,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="total != null" >
                total,
            </if>
            <if test="totalAmt != null" >
                total_amt,
            </if>
            <if test="amt != null" >
                amt,
            </if>
            <if test="beginTime != null" >
                begin_time,
            </if>
            <if test="endTime != null" >
                end_time,
            </if>
            <if test="remainNum != null" >
                remain_num,
            </if>
            <if test="remainAmt != null" >
                remain_amt,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="crtUserOpr != null" >
                crt_user_opr,
            </if>
            <if test="mdfUserOpr != null" >
                mdf_user_opr,
            </if>
            <if test="acltAmt != null" >
                aclt_amt,
            </if>
            <if test="aclt != null" >
                aclt,
            </if>
            <if test="discount != null" >
                discount,
            </if>
            <if test="item != null" >
                item,
            </if>
            <if test="instId != null" >
                inst_id,
            </if>
            <if test="couponName != null" >
                coupon_name,
            </if>
            <if test="costSide != null" >
                cost_side,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="atvNm != null" >
                #{atvNm,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="examineStatus != null" >
                #{examineStatus,jdbcType=CHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=CHAR},
            </if>
            <if test="total != null" >
                #{total,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="amt != null" >
                #{amt,jdbcType=DECIMAL},
            </if>
            <if test="beginTime != null" >
                #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remainNum != null" >
                #{remainNum,jdbcType=INTEGER},
            </if>
            <if test="remainAmt != null" >
                #{remainAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="crtUserOpr != null" >
                #{crtUserOpr,jdbcType=VARCHAR},
            </if>
            <if test="mdfUserOpr != null" >
                #{mdfUserOpr,jdbcType=VARCHAR},
            </if>

            <if test="acltAmt != null" >
                #{acltAmt,jdbcType=DECIMAL},
            </if>
            <if test="aclt != null" >
                #{aclt,jdbcType=INTEGER},
            </if>
            <if test="discount != null" >
                #{discount,jdbcType=DECIMAL},
            </if>
            <if test="item != null" >
                #{item,jdbcType=VARCHAR},
            </if>
            <if test="instId != null" >
                #{instId,jdbcType=VARCHAR},
            </if>
            <if test="couponName != null" >
                #{couponName,jdbcType=VARCHAR},
            </if>
            <if test="costSide != null" >
                #{costSide,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmActivityDO" >
        update mkm_activity
        <set >
            <if test="atvNm != null" >
                atv_nm = #{atvNm,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                mk_tool = #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="examineStatus != null and examineStatus !='' " >
                examine_status = #{examineStatus,jdbcType=CHAR},
            </if>
            <if test="status != null and status !=''" >
                status = #{status,jdbcType=CHAR},
            </if>
            <if test="total != null" >
                total = #{total,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                total_amt = #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="amt != null" >
                amt = #{amt,jdbcType=DECIMAL},
            </if>
            <if test="beginTime != null" >
                begin_time = #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remainNum != null" >
                remain_num = #{remainNum,jdbcType=INTEGER},
            </if>
            <if test="remainAmt != null" >
                remain_amt = #{remainAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="crtUserOpr != null" >
                crt_user_opr = #{crtUserOpr,jdbcType=VARCHAR},
            </if>
            <if test="mdfUserOpr != null" >
                mdf_user_opr = #{mdfUserOpr,jdbcType=VARCHAR},
            </if>
            <if test="acltAmt != null" >
                aclt_amt = #{acltAmt,jdbcType=DECIMAL},
            </if>
            <if test="aclt != null" >
                aclt = #{aclt,jdbcType=INTEGER},
            </if>
            <if test="discount != null" >
                discount = #{discount,jdbcType=DECIMAL},
            </if>
            <if test="instId != null" >
                inst_id = #{instId,jdbcType=VARCHAR},
            </if>
            <if test="couponName != null" >
                coupon_name = #{couponName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateRemainById" parameterType="com.hisun.lemon.mkm.entity.MkmActivityDO" >
        update mkm_activity
        <set >
            <if test="remainNum != null" >
                remain_num = remain_num - #{remainNum,jdbcType=INTEGER},
            </if>
            <if test="remainAmt != null" >
                remain_amt = remain_amt - #{remainAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
        <if test="remainNum != null" >
            <![CDATA[  and  remain_num - #{remainNum,jdbcType=INTEGER} >=0 ]]>
        </if>
        <if test="remainAmt != null" >
            <![CDATA[   and remain_amt - #{remainAmt,jdbcType=DECIMAL} >=0 ]]>
        </if>
    </update>

    <update id="updateAclt" parameterType="com.hisun.lemon.mkm.entity.MkmActivityDO" >
        update mkm_activity
        <set >
            <if test="acltAmt != null" >
                aclt_amt =aclt_amt + #{acltAmt,jdbcType=DECIMAL},
            </if>
            <if test="aclt != null" >
                aclt =aclt + #{aclt,jdbcType=INTEGER},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}

    </update>
</mapper>