<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.mkm.dao.IMkmCouponSeqDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.mkm.entity.MkmCouponSeqDO" >
        <id column="seq" property="seq" jdbcType="VARCHAR" />
        <result column="coupon_no" property="couponNo" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="release_tm" property="releaseTm" jdbcType="TIME" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="ori_time" property="oriTime" jdbcType="TIMESTAMP" />
        <result column="ori_seq" property="oriSeq" jdbcType="VARCHAR" />
        <result column="msg_cd" property="msgCd" jdbcType="VARCHAR" />
        <result column="msg_info" property="msgInfo" jdbcType="VARCHAR" />
        <result column="channel" property="channel" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        seq, coupon_no, mk_tool, atv_id, type, user_id, mobile, amt, release_tm, release_dt, 
        status, order_no, order_amt, modify_time, create_time, ori_time, ori_seq, msg_cd, channel,
        msg_info
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from mkm_coupon_seq
        where seq = #{seq,jdbcType=VARCHAR}
    </select>

    <select id="oriSeq" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from mkm_coupon_seq
        where order_no = #{oriSeq,jdbcType=VARCHAR} and (status = '1' or status = '5')
        <if test = "type == '02'" >
            and type = #{type,jdbcType=VARCHAR}
        </if>
        <if test = "type == '07'" >
            and type = '06'
        </if>
        <if test = "type == '01'" >
            and type = '01'
        </if>
    </select>

    <select id="checkTimes" resultType="java.lang.Integer"  >
        select count(times) from (select  count(*) times
        from mkm_coupon_seq where user_id=#{userId,jdbcType=VARCHAR}
        and atv_id=#{atvId,jdbcType=VARCHAR} and (status='1' or status='2') and type='01'
        <if test="receiveCycle == 'day'" >
           and DATE_FORMAT( modify_time, '%Y%m%d') = DATE_FORMAT( now(), '%Y%m%d')
        </if>
        <if test="receiveCycle == 'month'" >
           and DATE_FORMAT( modify_time, '%Y%m') = DATE_FORMAT( now(), '%Y%m')
        </if>
        <if test="receiveCycle == 'year'" >
           and DATE_FORMAT( modify_time, '%Y') = DATE_FORMAT( now(), '%Y')
        </if>
        )
        as time_tb where  <![CDATA[   time_tb.times < #{times,jdbcType=INTEGER} ]]>
    </select>
    <delete id="delete" parameterType="java.lang.String" >
        delete from mkm_coupon_seq
        where seq = #{seq,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.mkm.entity.MkmCouponSeqDO" >
        insert into mkm_coupon_seq
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="seq != null" >
                seq,
            </if>
            <if test="couponNo != null" >
                coupon_no,
            </if>
            <if test="mkTool != null" >
                mk_tool,
            </if>
            <if test="atvId != null" >
                atv_id,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="mobile != null" >
                mobile,
            </if>
            <if test="amt != null" >
                amt,
            </if>
            <if test="releaseTm != null" >
                release_tm,
            </if>
            <if test="releaseDt != null" >
                release_dt,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="orderAmt != null" >
                order_amt,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="oriTime != null" >
                ori_time,
            </if>
            <if test="oriSeq != null" >
                ori_seq,
            </if>
            <if test="msgCd != null" >
                msg_cd,
            </if>
            <if test="msgInfo != null" >
                msg_info,
            </if>
            <if test="channel != null" >
                channel,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="seq != null" >
                #{seq,jdbcType=VARCHAR},
            </if>
            <if test="couponNo != null" >
                #{couponNo,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="atvId != null" >
                #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="amt != null" >
                #{amt,jdbcType=DECIMAL},
            </if>
            <if test="releaseTm != null" >
                #{releaseTm,jdbcType=TIME},
            </if>
            <if test="releaseDt != null" >
                #{releaseDt,jdbcType=DATE},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="oriTime != null" >
                #{oriTime,jdbcType=TIMESTAMP},
            </if>
            <if test="oriSeq != null" >
                #{oriSeq,jdbcType=VARCHAR},
            </if>
            <if test="msgCd != null" >
                #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="channel != null" >
                #{channel,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.mkm.entity.MkmCouponSeqDO" >
        update mkm_coupon_seq
        <set >
            <if test="couponNo != null" >
                coupon_no = #{couponNo,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                mk_tool = #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="atvId != null" >
                atv_id = #{atvId,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="amt != null" >
                amt = #{amt,jdbcType=DECIMAL},
            </if>
            <if test="releaseTm != null" >
                release_tm = #{releaseTm,jdbcType=TIME},
            </if>
            <if test="releaseDt != null" >
                release_dt = #{releaseDt,jdbcType=DATE},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                order_amt = #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="oriTime != null" >
                ori_time = #{oriTime,jdbcType=TIMESTAMP},
            </if>
            <if test="oriSeq != null" >
                ori_seq = #{oriSeq,jdbcType=VARCHAR},
            </if>
            <if test="msgCd != null" >
                msg_cd = #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                msg_info = #{msgInfo,jdbcType=VARCHAR},
            </if>
        </set>
        where seq = #{seq,jdbcType=VARCHAR}
    </update>
</mapper>