package com.hisun.lemon.mkm.common;
import java.time.format.DateTimeFormatter;

/**
 * Created by <PERSON><PERSON> on 2017/7/15.
 */
public class Constants {
    public  static final String SUCCESS = "0000";
    public  static final String MKM_SUCCESS = "MKM00000";

    //折扣券发放撤销bean
    public static final String RELEASE_REVOKE_MESSAGE_BEAN="ReleaseRevokeMessageHandler";

    //折扣券消费撤销bean
    public static final String CONSUME_REVOKE_MESSAGE_BEAN="ConsumeRevokeMessageHandler";
    /**
     * 营销活动审核状态
     * 01-未审核
     * 02-已审核
     * 00-审核不通过
     */
    public static final String EXAMINE_STATUS_NO = "01";
    public static final String EXAMINE_STATUS_FAILD = "00";
    public static final String EXAMINE_STATUS_YES = "02";


    /**
     * 营销活动状态
     * 00-正常
     * 01-冻结
     * 02-解冻
     * 03-记账交易异常
     * 04-记账交易失败
     * 05-暂停
     */
    public static final String STATUS = "00";
    public static final String STATUS_NO = "01";
    public static final String STATUS_YES = "02";
    public static final String TRACE_EXC = "03";
    public static final String TRACE_FAIL = "04";
    public static final String ACTIVITY_STATUS_STOP = "05";
    /**
     * 营销工具
     * 01-电子券
     * 02-海币
     * 03-优惠券
     * 04-折扣券
     */
    public static final String MK_TOOL_ELE = "01";
    public static final String MK_TOOL_SEA = "02";
    public static final String MK_TOOL_COU = "03";
    public static final String MK_TOOL_DISCOUNT = "04";

    /**
     * 流水交易状态 1-成功 0-失败 2-异常 3-撤销 4-全额退款 5-部分退款
     */
    public static final String SEQ_SUCCESS = "1";
    public static final String SEQ_FAIL = "0";
    public static final String SEQ_EXCEPTION = "2";
    public static final String SEQ_REVOKED = "3";
    public static final String SEQ_REFUND = "4";
    public static final String SEQ_REFUND_PART = "5";

    /**
     * 海币交易流水类型
     * 00-充值 01-发放 02-消费 03-转赠  05-发放撤销 09-消费撤销 06-退款 07-退款撤销
     */
    public static final String SEQ_TYPE_RECHARGE = "00";
    public static final String SEQ_TYPE_RELEASE = "01";
    public static final String SEQ_TYPE_CONSUME = "02";
    public static final String SEQ_TYPE_GITF = "03";
    public static final String SEQ_TYPE_REVOKED = "09";
    public static final String SEQ_TYPE_REVOKED_REALEASE = "05";
    public static final String SEQ_TYPE_REFUND = "06";
    public static final String SEQ_TYPE_REFUND_REVOKE = "07";

    /**
     * 电子券明细状态
     * 01-未使用 02-已使用 03-已过期 04-冻结 05-待激活  07-退款 09-撤销
     */
    public static final String COUPON_DETAIL_STATUS_UN_USER= "01";
    public static final String COUPON_DETAIL_STATUS_USER  = "02";
    public static final String COUPON_DETAIL_STATUS_INVALD = "03";
    public static final String COUPON_DETAIL_STATUS_FREEZE = "04";
    public static final String COUPON_DETAIL_STATUS_ACT = "05";
    public static final String COUPON_DETAIL_STATUS_BATCH_INVALD = "06";
    public static final String COUPON_DETAIL_STATUS_REFUND = "07";
    public static final String COUPON_DETAIL_STATUS_REVOKE = "09";

    public static final String SEA_CCY_DETAIL_STATUS_N="01";
    public static final String SEA_CCY_DETAIL_STATUS_F="02";

    /**
     * 批量文件处理状态
     * 处理状态，0-已上传处理中，1-检查处理完成，2-生成回盘文件中，3-完成(最终状态)
     */
    public static final String BATCH_FILE_STS_INIT = "0";
    public static final String BATCH_FILE_STS_CHECK_COMPLETE = "1";
    public static final String BATCH_FILE_STS_RET_FILE = "2";
    public static final String BATCH_FILE_STS_COMPLETE = "3";

    /**
     * 待检测表检查状态
     * 0-录入，1-正常，2-活动内无此电子券，3-手机号不符，4-已过期，5-已冻结，6-无此券信息，7-其他异常
     */
    public static final String CHECK_STS_INIT = "0";
    public static final String CHECK_STS_PASS = "1";
    public static final String CHECK_STS_ACT_NO_FOUND = "2";
    public static final String CHECK_STS_MBL_ERROR = "3";
    public static final String CHECK_STS_EXPIRED = "4";
    public static final String CHECK_STS_FREEZE = "5";
    public static final String CHECK_STS_NO_FOUND = "6";
    public static final String CHECK_STS_ERROR = "7";

    /**
     * 批量操作类型
     * 0-过期，1-延期，2-冻结，3-解冻
     */
    public static final String BATCH_OPR_TYP_EXPIRED = "0";
    public static final String BATCH_OPR_TYP_EXTENSION = "1";
    public static final String BATCH_OPR_TYP_FREEZE = "2";
    public static final String BATCH_OPR_TYP_UNFREEZE = "3";

    /**
     * 回盘文件，处理状态
     */
    public static final String RET_FILE_STATUS_SECTION = "部分成功";
    public static final String RET_FILE_STATUS_ALL = "全部成功";

    /**
     * 海币流水交易类型
     * 00-充值
     * 01-发放
     * 02-消费
     * 03-撤销
     */
    public static  final String SEACCY_SEA_TRACE_TYPE_R = "00";
    public static  final String SEACCY_SEA_TRACE_TYPE_G = "01";
    public static  final String SEACCY_SEA_TRACE_TYPE_C = "02";
    public static  final String SEACCY_SEA_TRACE_TYPE_V = "03";
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");


    //************************记账交易类型*********************
    //营销活动
    public static final String ACC_TRACE_TYPE_M = "10" ;
    //充值
    public static final String ACC_TRACE_TYPE_RC = "01" ;

    //消费
    public static final String ACC_TRACE_TYPE_C = "02" ;

    //充海币
    public static final String ACC_TRACE_TYPE_RS = "05" ;

    //退款
    public static final String ACC_TRACE_TYPE_RF = "06" ;

    //撤销
    public static final String ACC_TRACE_TYPE_RK = "09" ;

    //海币发放
    public static final String ACC_TRACE_TYPE_RL = "11" ;


    //**************关联系统成功标志*********************
    /**
     * 账户模块
     */
    public static final String URM_SUCCESS = "URM00000";

    /**
     * 账务模块
     */
    public static final String ACM_SUCCESS = "ACM00000";
    /**
     * 风控模块
     */
    public static final String RSM_SUCCESS = "RSM00000";
    // *****************关联系统成功标志******************


    //******************风控参数******************

    /**
     * 币种
     * USD-美元
     */
    public static final String RSM_CCY = "USD";
    /**
     * 付款方用户类型
     * 01-用户商户
     */
    public static final String RSM_PAY_USER_TYP = "01";

    /**
     *支付类型
     */
    public static final String RSM_PAY_TYPE = "01";
    /**
     * 交易类型
     * 02-消费
     */
    public static final String RSM_TX_TYP = "02";

    /**
     *交易渠道
     */
    public static final String MKM = "MKM";

    /**
     * 交易状态
     */
    public static final String RSM_TX_STS = "0";

    /**
     * 用户类型
     */
    public static final String RSM_ID_TY = "01";


    //******************风控参数******************


    /**
     * 账务处理状态 N：正常 C:冲正
     */
    public final static String ACCOUNTING_NOMARL = "N";
    /**
     * 账务处理状态 N：正常 C:冲正
     */
    public final static String CAP_TYPE_CASH = "1";
    public final static String CAP_TYPE_SETTLE = "8";
}
