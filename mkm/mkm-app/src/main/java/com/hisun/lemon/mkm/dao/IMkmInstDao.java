/*
 * @ClassName IMkmInstDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.mkm.entity.MkmInstDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IMkmInstDao extends BaseDao<MkmInstDO> {
    int updateByActIdAndIsntId(MkmInstDO mkmInstDO);

    List<MkmInstDO> getAllInstIdByActId(String id);

    MkmInstDO findByinstIdAndActId(@Param("atvId") String atvId,@Param("instId") String instId);

    int delete(Integer id);

    int updateRelease(MkmInstDO mkmInstDO);

    int judgeConsumeInst(@Param("atvId") String atvId,  @Param("instId") String instId);
}