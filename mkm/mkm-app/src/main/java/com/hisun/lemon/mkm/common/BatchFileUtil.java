package com.hisun.lemon.mkm.common;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @create 2017/7/25
 */
public class BatchFileUtil {

    private static final Logger logger = LoggerFactory.getLogger(BatchFileUtil.class);

    public static void saveFile(MultipartFile file, String filePath) throws IOException {
        if (!file.isEmpty()) {
            BufferedOutputStream out = null;
            try {
                out = new BufferedOutputStream(new FileOutputStream(new File(filePath)));
                out.write(file.getBytes());
                out.flush();
            } catch (Exception e) {
                logger.error(e.getMessage());
                throw new LemonException(MsgCd.SAVE_FILE_FAILURE.getMsgCd());
            } finally {
                if (out != null) {
                    out.close();
                }
            }
        } else {
            throw new LemonException(MsgCd.SAVE_FILE_FAILURE.getMsgCd());
        }
    }
}
