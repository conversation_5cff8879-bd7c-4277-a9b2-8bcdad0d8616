/*
 * @ClassName MkmSeaCcyDetailDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class MkmSeaCcyDetailDO extends BaseDO {

    /**
     * @Fields userId 用户id
     */
    private String userId;
    /**
     * @Fields mkTool 营销工具类型 02-海币
     */
    private String mkTool;
    /**
     * @Fields mobile 用户手机号
     */
    private String mobile;
    /**
     * @Fields count  海币余额
     */
    private Integer count;
    /**
     * @Fields releaseTm 发放时间
     */
    private LocalTime releaseTm;
    /**
     * @Fields releaseDt 发放日期
     */
    private LocalDate releaseDt;
    /**
     * @Fields status 状态 01-未使用 02-已使用 03-已过期 04-冻结 05-待激活
     */
    private String status;
    /**
     * @Fields orderNo 最后使用订单号
     */
    private String orderNo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public LocalTime getReleaseTm() {
        return releaseTm;
    }

    public void setReleaseTm(LocalTime releaseTm) {
        this.releaseTm = releaseTm;
    }

    public LocalDate getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(LocalDate releaseDt) {
        this.releaseDt = releaseDt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }


}