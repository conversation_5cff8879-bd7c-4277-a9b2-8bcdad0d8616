package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.mkm.entity.BatchFileRecDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/28
 */
@Mapper
public interface IBatchFileRecDao {

    /**
     * 更新批量记录
     *
     * @param recDO 批量文件记录
     */
    void updateBatchRec(BatchFileRecDO recDO);

    /**
     * 根据批次号查询信息
     *
     * @param recNo 批次号
     * @return
     */
    BatchFileRecDO selectBatchRecByRecNo(String recNo);

    String oprData(@Param("oprTyp") String oprTyp,@Param("recNo")  String recNo,@Param("invalidTm")  LocalDateTime invalidTm);
}
