package com.hisun.lemon.mkm.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.mkm.dao.IFAQManagerDao;
import com.hisun.lemon.mkm.entity.FAQManagerDO;
import com.hisun.lemon.mkm.req.dto.FAQManagerReqDTO;
import com.hisun.lemon.mkm.res.dto.FAQManagerRspDTO;
import com.hisun.lemon.mkm.service.FAQManagerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 常见问题服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 20:35
 */
@Service("faqManagerService")
public class FAQManagerServiceImpl extends BaseService implements FAQManagerService {
    
    private static final Logger logger = LoggerFactory.getLogger(FAQManagerServiceImpl.class);
    
    @Resource
    private IFAQManagerDao faqManagerDao;
    
    @Override
    public FAQManagerRspDTO queryFAQList(FAQManagerReqDTO reqDTO) {
        logger.info("查询常见问题列表开始，参数：{}", reqDTO);
        
        // 构建查询参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("questionContent", reqDTO.getQuestion());
        
        // 分页参数
        Integer pageNum = reqDTO.getPageNum();
        Integer pageSize = reqDTO.getPageSize();
        
        if (JudgeUtils.isNull(pageNum)) {
            pageNum = 0; // 默认第一页
        }
        
        if (JudgeUtils.isNull(pageSize)) {
            pageSize = 10; // 默认每页10条
        }
        
        if (JudgeUtils.isNotNull(reqDTO.getId())) {
            // 如果传入ID，则只查询指定ID的记录
            paramMap.put("id", reqDTO.getId());
        }
        
        if (JudgeUtils.isNotNull(reqDTO.getLanguage())) {
            // 如果传入语言，则只查询指定语言的记录
            paramMap.put("language", reqDTO.getLanguage());
        }

        // 使用PageUtils进行分页查询
        PageInfo<FAQManagerDO> pageInfo = PageUtils.pageQueryWithCount(pageNum, pageSize, 
            () -> faqManagerDao.queryFAQList(paramMap));
        
        // 构建返回结果
        FAQManagerRspDTO rspDTO = new FAQManagerRspDTO();
        rspDTO.setTotNum((int)pageInfo.getTotal());
        rspDTO.setCurrPage(pageInfo.getPageNum());
        
        // 转换结果
        List<FAQManagerRspDTO.FAQManagerItemDTO> itemList = new ArrayList<>();
        List<FAQManagerDO> faqList = pageInfo.getList();
        
        if (JudgeUtils.isNotEmpty(faqList)) {
            for (FAQManagerDO faqDO : faqList) {
                FAQManagerRspDTO.FAQManagerItemDTO itemDTO = new FAQManagerRspDTO.FAQManagerItemDTO();
                BeanUtils.copyProperties(itemDTO, faqDO);
                itemList.add(itemDTO);
            }
        }
        
        rspDTO.setFaqList(itemList);
        logger.info("查询常见问题列表结束，结果数量：{}", itemList.size());
        
        return rspDTO;
    }
} 