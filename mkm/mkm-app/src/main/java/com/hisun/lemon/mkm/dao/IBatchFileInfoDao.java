package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.mkm.entity.BatchFileInfoDo;
import com.hisun.lemon.mkm.entity.BatchFileRecDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/26
 */
@Mapper
public interface IBatchFileInfoDao {

    /**
     * 批量插入数据到临时表
     *
     * @param list 批量数据
     * @return
     */
    int batchInsert(@Param("list") List<BatchFileInfoDo> list);

    /**
     * 根据批次号统计
     *
     * @param recNo 批次号
     * @return
     */
    BigDecimal selectTotAmtByRecNo(@Param("recNo") String recNo);


    /**
     * 根据批次查询所有批次内文件录入信息
     *
     * @param recNo 批次号
     * @return
     */
    List<BatchFileInfoDo> selectFileInfo(@Param("recNo") String recNo);

    /**
     * 根据批次号和操作类型比较数据，并更新临时表状态
     *
     * @param recNo  批次号
     * @param chkSts 检查状态值
     * @param list   券号
     */
    int updateStsByCompareDetailTable(@Param("recNo") String recNo,
                                      @Param("chkSts") String chkSts,
                                      @Param("list") List list);

    /**
     * 根据批次号，和临时表状态更新明细表状态过期
     *
     * @param recNo 批次号
     * @return 影响行数
     */
    int updateCouponExpiredByTempSts(@Param("recNo") String recNo);

    /**
     * 根据批次号，和临时表状态更新时间
     *
     * @param recNo 批次号
     * @return 影响行数
     */
    int updateCouponExtensionByTempSts(@Param("recNo") String recNo);

    /**
     * 根据批次号，和临时表状态更新明细表状态为冻结
     *
     * @param recNo 批次号
     * @return 影响行数
     */
    int updateCouponStsToFreeze(@Param("recNo") String recNo);

    /**
     * 根据批次号，和临时表状态更新明细表状态为解冻
     *
     * @param recNo 批次号
     * @return 影响行数
     */
    int updateCouponStsToUnFreeze(@Param("recNo") String recNo);

    /**
     * 根据检查返回状态查询数量
     *
     * @param recNo  批次号
     * @param chkSts 检查返回状态
     * @return 笔数
     */
    int selectRowNumByChkSts(@Param("recNo") String recNo, @Param("chkSts") String chkSts);

    /**
     * 根据批量记录查询批量记录总数
     *
     * @param recNo 批次号
     * @return 总笔数
     */
    int selectAllRowNumByRecNo(@Param("recNo") String recNo);

    /**
     * 根据检查返回状态查询成功金额
     *
     * @param recNo  批次号
     * @param chkSts 检查返回状态
     * @return 金额
     */
    BigDecimal selectSuccessAmtByRecNo(@Param("recNo") String recNo, @Param("chkSts") String chkSts);

    void  updateBatchRec(BatchFileRecDO batchFileRecDO);

    int  oprData(@Param("oprTyp") String oprTyp, @Param("recNo") String recNo);

    int updateInfo(@Param("recNo") String recNo);

    void deleteByRecNo(String recNo);
}
