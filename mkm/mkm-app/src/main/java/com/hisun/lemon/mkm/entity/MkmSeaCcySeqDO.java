/*
 * @ClassName MkmSeaCcySeqDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class MkmSeaCcySeqDO extends BaseDO {

    /**
     * @Fields seq 流水
     */
    private String seq;
    /**
     * @Fields seq 渠道
     */
    private String channel;
    /**
     * @Fields seq 活动Id
     */
    private String atvId;
    /**
     * @Fields mkTool 营销工具
     */
    private String mkTool;
    /**
     * @Fields type 流水类型 01-发放 02-核销
     */
    private String type;
    /**
     * @Fields userId 用户id
     */
    private String userId;
    /**
     * @Fields mobile 手机号
     */
    private String mobile;
    /**
     * @Fields count 海币交易数量
     */
    private Integer count;
    /**
     * @Fields releaseTm 发放时间
     */
    private LocalTime releaseTm;
    /**
     * @Fields releaseDt 发放日期
     */
    private LocalDate releaseDt;
    /**
     * @Fields status 状态 0-失败 1-成功 2-异常
     */
    private String status;
    /**
     * @Fields orderNo 订单号
     */
    private String orderNo;
    /**
     * @Fields orderAmt 订单金额
     */
    private BigDecimal orderAmt;
    /**
     * @Fields oriTime 原交易时间
     */
    private LocalDateTime oriTime;
    /**
     * @Fields oriSeq 
     */
    private String oriSeq;
    /**
     * @Fields msgCd 错误码
     */
    private String msgCd;
    /**
     * @Fields msgInfo 错误信息
     */
    private String msgInfo;

    /**
     * 可退款金额
     */
    private Integer valRefund;

    /**
     * 商户号
     */
    private String instId;

    /**
     * 被赠送用户手机号
     * @return
     */
    private String gmobile;

    /**
     * 被赠送用户号
     * @return
     */
    private String guserId;

    public String getGmobile() {
        return gmobile;
    }

    public void setGmobile(String gmobile) {
        this.gmobile = gmobile;
    }

    public String getGuserId() {
        return guserId;
    }

    public void setGuserId(String guserId) {
        this.guserId = guserId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Integer getValRefund() {
        return valRefund;
    }

    public void setValRefund(Integer valRefund) {
        this.valRefund = valRefund;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public LocalTime getReleaseTm() {
        return releaseTm;
    }

    public void setReleaseTm(LocalTime releaseTm) {
        this.releaseTm = releaseTm;
    }

    public LocalDate getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(LocalDate releaseDt) {
        this.releaseDt = releaseDt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public LocalDateTime getOriTime() {
        return oriTime;
    }

    public void setOriTime(LocalDateTime oriTime) {
        this.oriTime = oriTime;
    }

    public String getOriSeq() {
        return oriSeq;
    }

    public void setOriSeq(String oriSeq) {
        this.oriSeq = oriSeq;
    }

    public String getMsgCd() {
        return msgCd;
    }

    public void setMsgCd(String msgCd) {
        this.msgCd = msgCd;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }
}