package com.hisun.lemon.mkm.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/7/26
 */
public class BatchFileRecDO {

    private String recNo;

    private String batchFile;

    private String acId;

    private LocalDateTime processDt;

    private Integer totNum;

    private BigDecimal totAmt;

    private Integer successNum;

    private BigDecimal successAmt;

    private Integer failureNum;

    private BigDecimal failureAmt;

    private String oprTyp;

    private String processSts;

    private String resultFile;

    private LocalDateTime modifyTime;



    public String getBatchFile() {
        return batchFile;
    }

    public void setBatchFile(String batchFile) {
        this.batchFile = batchFile;
    }

    public String getAcId() {
        return acId;
    }

    public void setAcId(String acId) {
        this.acId = acId;
    }

    public LocalDateTime getProcessDt() {
        return processDt;
    }

    public void setProcessDt(LocalDateTime processDt) {
        this.processDt = processDt;
    }
    public BigDecimal getTotAmt() {
        return totAmt;
    }

    public void setTotAmt(BigDecimal totAmt) {
        this.totAmt = totAmt;
    }

    public BigDecimal getSuccessAmt() {
        return successAmt;
    }

    public void setSuccessAmt(BigDecimal successAmt) {
        this.successAmt = successAmt;
    }

    public BigDecimal getFailureAmt() {
        return failureAmt;
    }

    public void setFailureAmt(BigDecimal failureAmt) {
        this.failureAmt = failureAmt;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getProcessSts() {
        return processSts;
    }

    public void setProcessSts(String processSts) {
        this.processSts = processSts;
    }

    public String getResultFile() {
        return resultFile;
    }

    public void setResultFile(String resultFile) {
        this.resultFile = resultFile;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRecNo() {
        return recNo;
    }

    public void setRecNo(String recNo) {
        this.recNo = recNo;
    }

    public Integer getTotNum() {
        return totNum;
    }

    public void setTotNum(Integer totNum) {
        this.totNum = totNum;
    }

    public Integer getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(Integer successNum) {
        this.successNum = successNum;
    }

    public Integer getFailureNum() {
        return failureNum;
    }

    public void setFailureNum(Integer failureNum) {
        this.failureNum = failureNum;
    }

    @Override
    public String toString() {
        return "BatchFileRecDO{" +
                "recNo=" + recNo +
                ", batchFile='" + batchFile + '\'' +
                ", acId='" + acId + '\'' +
                ", processDt=" + processDt +
                ", totNum=" + totNum +
                ", totAmt=" + totAmt +
                ", successNum=" + successNum +
                ", successAmt=" + successAmt +
                ", failureNum=" + failureNum +
                ", failureAmt=" + failureAmt +
                ", oprTyp='" + oprTyp + '\'' +
                ", processSts='" + processSts + '\'' +
                ", resultFile='" + resultFile + '\'' +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
