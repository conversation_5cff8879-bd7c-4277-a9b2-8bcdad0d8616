/*
 * @ClassName IMkmSeaCcySeqDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.mkm.entity.MkmSeaCcySeqDO;
import com.hisun.lemon.mkm.req.dto.SeaccyTraceDetailRepDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IMkmSeaCcySeqDao extends BaseDao<MkmSeaCcySeqDO> {
    int checkTimes(@Param("atvId") String atvId, @Param("userId") String userId, @Param("times") Integer times,@Param("receiveCycle") String receiveCycle);

    MkmSeaCcySeqDO getValRefund(@Param("oriSeq") String oriSeq, @Param("count") Integer count, @Param("type") String type);

    int updateValRefund(MkmSeaCcySeqDO upStatusSeqDO);

    List<MkmSeaCcySeqDO> seaccyTraceDetail(SeaccyTraceDetailRepDTO body);
}