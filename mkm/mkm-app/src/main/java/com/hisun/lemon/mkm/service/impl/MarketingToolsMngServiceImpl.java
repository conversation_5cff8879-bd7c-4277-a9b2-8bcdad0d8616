package com.hisun.lemon.mkm.service.impl;

import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.mkm.dao.ICouponUsageDetailsDao;
import com.hisun.lemon.mkm.dao.IRequisitionDetailsDao;
import com.hisun.lemon.mkm.entity.CouponUsageDetailsDO;
import com.hisun.lemon.mkm.entity.RequisitionDetailsDO;
import com.hisun.lemon.mkm.service.IMarketingToolsMngService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/24
 */
@Service
@Transactional
public class MarketingToolsMngServiceImpl extends BaseService implements IMarketingToolsMngService {

    @Resource
    private IRequisitionDetailsDao requisitionDetailsDao;

    @Resource
    private ICouponUsageDetailsDao couponUsageDetailsDao;

    @Override
    @Transactional(readOnly = true)
    public List<RequisitionDetailsDO> queryDetailsList(String mblNo, LocalDate releaseDt, int pageNum, int pageSize) {
        List<RequisitionDetailsDO> list = PageUtils.pageQuery(pageNum, pageSize,
                () -> requisitionDetailsDao.selectRequisitionByMblNoAndBegDt(mblNo, releaseDt));
        return list;
    }

    @Override
    @Transactional(readOnly = true)
    public List<CouponUsageDetailsDO> queryUsageList(String mblNo, LocalDate releaseDt, int pageNum, int pageSize) {
        List<CouponUsageDetailsDO> list = PageUtils.pageQuery(pageNum, pageSize,
                () -> couponUsageDetailsDao.selectUsageByMblNoAndBegDt(mblNo, releaseDt));
        return list;
    }

}
