/*
 * @ClassName IMkmCouponSeqDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.mkm.entity.MkmCouponSeqDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IMkmCouponSeqDao extends BaseDao<MkmCouponSeqDO> {
    int checkTimes(@Param("atvId") String atvId, @Param("userId") String userId, @Param("times") Integer times, @Param("receiveCycle") String receiveCycle);

    MkmCouponSeqDO oriSeq(@Param("oriSeq") String oriSeq, @Param("type") String type);
}