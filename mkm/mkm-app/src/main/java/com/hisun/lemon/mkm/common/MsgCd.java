package com.hisun.lemon.mkm.common;


/**
 * 错误码
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 下午4:40:05
 *
 */
public enum MsgCd {
    SUCCESS("MKM00000", ""),
    IO_EXC("MKM00010", "IO exception"),
    INST_ID_IS_NULL("MKM10001", "instId is null"),
    MIN_AMT_IS_NULL("MKM10002", "minAmt is null"),
    MAX_AMT_IS_NULL("MKM10003", "maxAmt is null"),
    COUNT_IS_NULL("MKM10004", "count is null"),
    CONPON_VAL_TM_IS_NULL("MKM10006", "conponValTm is null"),
    CONPON_INVAL_TM_IS_NULL("MKM10005", " conponInvalTm is null"),
    ADD_MARKET_ACTIVITY_FAIL("MKM30006", " add marketActivity fail"),
    UPDATE_MARKET_ACTIVITY_FAIL("MKM30007", " update marketActivity fail"),
    CAN_NOT_FIND_MARKET_ACTIVITY("MKM30008","can't find markey activity"),
    TOTAL_CHECK_FAIL("MKM30009","only increase"),
    TOTAL_AMOUNT_CHECK_FAIL("MKM30010","only increase"),
    RELEASE_TIMES_CHECK_FAIL("MKM30011","only increase"),
    ADD_MKM_INST_FAIL("MKM30012", " add market inst fail"),
    INSERT_SEA_CCY_SEQ_FAIL("MKM30013", " insert sea ccy sequence fail"),
    INSERT_SEA_CCY_DETAIL_FAIL("MKM30014", " insert sea ccy detail fail"),
    UPDATE_SEA_CCY_DETAIL_FAIL("MKM30015", " update sea ccy detail fail"),
    GET_SEA_CCY_ACIVITY_FAIL("MKM30016", " get activity fail"),
    UPDATE_ACIVITY_FAIL("MKM30017", " update sea cyy activity fail"),
    ACIVITY_STATUS_INVAILD("MKM30018", " activity is invalid"),
    INSERT_CONPON_SEQ_FAIL("MKM30019", " insert conpon sequence fail"),
    INSERT_CONPON_DETAIL_FAIL("MKM30020", " insert conpon detail fail"),
    CONSUME_SEA_CYY_FAIL("MKM30021", " Insufficient balance or user sea cyy not exist"),
    INST_NOT_IN_ACT("MKM30022", " Not in the activity return"),
    INST_NOT_CONSUME("MKM30023", " The coupon is no belong of inst"),
    USER_CONSUME_FAIL("MKM30024", " can not find coupon by userId"),
    ORDER_AMT_SCOPR("MKM30025", "amt not in scope"),
    UPDATE_COUPON_FAIL("MKM30026", "update coupon detail fail"),
    UPDATE_ACTIVITY_ACLT("MKM30027", "update accumulative fail"),
    GET_ORISEQ_FAIL("MKM30028", "get source sequence fail"),
    REVOKED_FAIL("MKM30029", "revoke failed"),
    BEYOND_TIMES("MKM30030", "beyond the number of times"),
    ACCOUNTING_FAIL("MKM30031", "accounting fail"),
    ACCOUNTING_EXCEPTION("MKM30032", "accounting exception"),
    ADD_ACOUNTING_SEQ_FAILED("MKM30033", "add acounting seq failed"),
    QUERY_USER_EXP("MKM30034", "query user exception"),
    USER_ACCOUNT_EXP("MKM30035", " user account statu exception"),
    UPDATE_INST_RELEASE_FAILED("MKM30036", " update merchant release failed"),
    UPDATE_INST_CONSUME_FAILED("MKM30037", " update merchant failed"),
    UPDATE_VAL_REFUND_FAILED("MKM30038", " update refund balance failed"),
    NOT_EQUELS_ORDERNO("MKM30039", " no equels orderNo"),
    ONLY_ORDER("MKM30040", " The same order only get a discount"),
    REVOKE_REFUND_BEYONG("MKM30041", " BEYONG PAY AMT"),
    UPDATE_MKM_INST_FAIL("MKM30042", " update market inst fail"),
    DELETE_MARKET_ACTIVITY_FAIL("MKM30043", " delete marketActivity fail"),
    COUPON_NOT_EXITE("MKM30044", " coupon not exist"),
    COUPON_AlREADY_VALIDATION("MKM30045", " coupon aleady invalid or coupon not valid"),
    ORDER_CONSUME_REPEAT("MKM30046", " order consume repeat"),
    CAN_NOT_FIND_MARKET_RULE("MKM30047","can't find markey rule"),
    UPDATE_MARKET_ACTIVITY_RULE_FAIL("MKM30048", " update marketActivityRule fail"),
    D_C_NOT_BANLANCE("MKM30048", "借贷不平"),
    ACTIVITY_EXAMIN_FAIL("MKM30049", "活动审核失败"),
    REPEAT_EXAMIN("MKM30050", "重复审核"),
    FILE_OPERATION_FAILED("MKM40001", "File operation failed"),
    FILE_IS_NULL("MKM40002", "file is null"),
    CHECK_DOES_NOT_PASS("MKM40003", "file check does not pass"),
    FILE_CREATE_FAILED("MKM40004", "file create failed"),
    FORMAT_IS_INCONSISTENT("MKM40005", "file format is inconsistent"),
    FIELD_IS_NULL("MKM40006", "Field can not be empty"),
    FILE_WRITE_FAILED("MKM40007", "file_write_failed"),
    FILE_CHECK_STATUS_NOT_MATCH("MKM40008", "file status is not check complete"),
    REVOKE_COUNT_MORE_BANK_CONSUME("MKM40009", "refund count more than consume count"),
    UPDATE_GITF_FAILED("MKM40010", "update seaccy gitf failed"),
    FILE_TOLNUM_NO_EQUELS("MKM40011", "文件总笔数不一致"),
    FILE_TOLAMT_NO_EQUELS("MKM40012", "文件金额不一致"),
    UPLOAD_FILE_FAILURE("MKM40013", "上传文件失败"),
    UPDATE_COUPON_FAILURE("MKM40014", "更新券别操作失败"),
    INSERT_BATCH_INFO_FAILURE("MKM40015", "登记批量信息失败"),
    BATCH_FILE_NO_EXISTS("MKM40016", "文件不存在"),
    BATCH_FILE_TYPE_ERROR("MKM40017", "文件格式错误"),
    SAVE_FILE_FAILURE("MKM40018", "保存文件失败"),
    RISK_CHECK_EXCEPTION("MKM50001", "risk check exception"),
    CHECK_PASSWORD_E("MKM50002", "check pay password exception"),
    BANK_COMMUNICATION_EXCEPTION("CPI30099","bank communication exception");

    private String msgCd;
    private String msgInfo;
    private MsgCd(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }
    public String getMsgCd() {
        return msgCd;
    }
//    public void setMsgCd(String msgCd) {
//        this.msgCd = msgCd;
//    }
    public String getMsgInfo() {
        return msgInfo;
    }
//   public void setMsgInfo(String msgInfo) {
//        this.msgInfo = msgInfo;
//    }
    
}
