package com.hisun.lemon.mkm.mq;

import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.mkm.common.Constants;
import com.hisun.lemon.mkm.req.dto.RevokedConsumeCouponReqDTO;
import com.hisun.lemon.mkm.service.MkmToolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by chen on 8/28 0028.
 */

@Component(Constants.CONSUME_REVOKE_MESSAGE_BEAN)
public class ConsumeRevokeMessageHandler implements MessageHandler<RevokedConsumeCouponReqDTO> {
    private static final Logger logger = LoggerFactory.getLogger(ConsumeRevokeMessageHandler.class);

    @Resource
    MkmToolService mkmToolService;

    @Override
    public void onMessageReceive(GenericCmdDTO<RevokedConsumeCouponReqDTO> genericCmdDTO) {
        logger.info("接收消费撤销消息 {}", genericCmdDTO.getBody().getSeq());
        mkmToolService.revokedConsume(genericCmdDTO);
    }
}
