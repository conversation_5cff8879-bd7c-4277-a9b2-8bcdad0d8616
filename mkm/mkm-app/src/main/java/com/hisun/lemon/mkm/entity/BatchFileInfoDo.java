package com.hisun.lemon.mkm.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/7/26
 */
public class BatchFileInfoDo {

    private String recNo;

    private String CouponNo;

    private String mblNo;

    private LocalDate releaseDt;

    private BigDecimal amt;

    private String oprTyp;
    private String oprSts;

    private LocalDateTime delayTm;


    public String getRecNo() {
        return recNo;
    }

    public void setRecNo(String recNo) {
        this.recNo = recNo;
    }

    public String getCouponNo() {
        return CouponNo;
    }

    public void setCouponNo(String couponNo) {
        CouponNo = couponNo;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public LocalDate getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(LocalDate releaseDt) {
        this.releaseDt = releaseDt;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }


    public String getOprSts() {
        return oprSts;
    }

    public void setOprSts(String oprSts) {
        this.oprSts = oprSts;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public LocalDateTime getDelayTm() {
        return delayTm;
    }

    public void setDelayTm(LocalDateTime delayTm) {
        this.delayTm = delayTm;
    }

    @Override
    public String toString() {
        return "BatchFileInfoDo{" +
                "recNo='" + recNo + '\'' +
                ", CouponNo='" + CouponNo + '\'' +
                ", mblNo='" + mblNo + '\'' +
                ", releaseDt=" + releaseDt +
                ", amt=" + amt +
                ", status='" + oprSts + '\'' +
                '}';
    }

    public BatchFileInfoDo getClone() {
        try{
            return (BatchFileInfoDo) this.clone();
        } catch (CloneNotSupportedException e) {
        }
        return null;
    }
}
