package com.hisun.lemon.mkm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.service.MarketActivityService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import com.hisun.lemon.mkm.res.dto.FindMarkeyActivityRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by chen on 7/19 0019.
 */

/**
 * 营销活动管理
 * @GetMapping @PostMapping @PutMapping @DeleteMapping @PatchMapping
 * @date 2017年7月19日
 * @time 上午10:46:30
 *
 */
@RestController
@RequestMapping("/marketActivity")
public class MarketActivitiesController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(MarketActivitiesController.class);
    /**
     * 营销活动
     */
    @Resource
    private MarketActivityService marketActivityService ;

    @ApiOperation(value="新增营销活动", notes="新增营销活动")
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "atvNm", value = "活动名称", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mkTool", value = "营销工具", required = true, dataType = "String"),
            @ApiImplicitParam(name = "total", value = "发放总量", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "totalAmt", value = "发放总金额", required = true, dataType = "BigDecimal"),
            @ApiImplicitParam(name = "receiveTimes", value = "单用户领取次数", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "amt", value = "单券金额", required = false, dataType = "BigDecimal"),
            @ApiImplicitParam(name = "beginTime", value = "活动开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "活动结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "couponValTm", value = "有效日期(电子券)", required = false, dataType = "String"),
            @ApiImplicitParam(name = "couponInvalTm", value = "失效日期(电子券)", required = false, dataType = "String"),
            @ApiImplicitParam(name = "instId", value = "商户id(电子券)", required = false, dataType = "String"),
            @ApiImplicitParam(name = "item", value = "科目号", required =true, dataType = "String"),
            @ApiImplicitParam(name = "crtUserOpr", value = "创建人员", required = false, dataType = "String"),
            @ApiImplicitParam(name = "minAmt", value = "可使用订单最小金额(电子券)", required = false, dataType = "BigDecimal"),
            @ApiImplicitParam(name = "maxAmt", value = "可使用订单最大金额(电子券)", required = false, dataType = "BigDecimal")
    })*/
    @ApiResponse(code = 200, message = "新增结果")
    @PostMapping("/add")
    public GenericRspDTO<String> addMarketActivity(@RequestBody GenericDTO<AddMarketActivityReqDTO> addMarketActivityDTO) {
        GenericRspDTO addMarkeyActivityRspDTO = GenericRspDTO.newSuccessInstance();
        logger.info("新增活动");
        try {
            addMarkeyActivityRspDTO=marketActivityService.insert(addMarketActivityDTO.getBody());
        } catch (LemonException e) {
            addMarkeyActivityRspDTO.setMsgCd(e.getMsgCd());
            addMarkeyActivityRspDTO.setMsgInfo(e.getMsgInfo());
        }
        logger.info("新增活动结束" + addMarkeyActivityRspDTO.getMsgCd());
        return addMarkeyActivityRspDTO;
    }

    @ApiOperation(value="修改营销活动", notes="修改营销活动")
    /*@ApiImplicitParams({
            @ApiImplicitParam(name = "atvNm", value = "活动名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "id", value = "活动id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "total", value = "发放总量", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "totalAmt", value = "发放总金额", required = false, dataType = "BigDecimal"),
            @ApiImplicitParam(name = "receiveTimes", value = "单用户领取次数", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "amt", value = "单券金额", required = false, dataType = "BigDecimal"),
            @ApiImplicitParam(name = "endTime", value = "活动结束时间", required = false, dataType = "String"),
            @ApiImplicitParam(name = "instId", value = "商户id(电子券)", required = false, dataType = "String"),
            @ApiImplicitParam(name = "item", value = "科目号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mdfUserOpr", value = "修改操作员", required = false, dataType = "String"),
            @ApiImplicitParam(name = "minAmt", value = "可使用订单最小金额(电子券)", required = false, dataType = "BigDecimal"),
            @ApiImplicitParam(name = "maxAmt", value = "可使用订单最大金额(电子券)", required = false, dataType = "BigDecimal")
    })*/
    @ApiResponse(code = 200, message = "修改结果")
    @PostMapping("/update")
    public GenericRspDTO<NoBody> updateMarketActivity(@Validated @RequestBody GenericDTO<UpdateMarketActivityReqDTO> updateMarketActivityDTO) {
        GenericRspDTO updateMarkeyActivityRspDTO = GenericRspDTO.newSuccessInstance();
        try {
            updateMarkeyActivityRspDTO=marketActivityService.update(updateMarketActivityDTO.getBody());
        } catch (LemonException e) {
            updateMarkeyActivityRspDTO.setMsgCd(e.getMsgCd());
            updateMarkeyActivityRspDTO.setMsgInfo(e.getMsgInfo());
        }
        return updateMarkeyActivityRspDTO;
    }

    @ApiOperation(value="删除营销活动", notes="删除营销活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "活动id", required = true, dataType = "String")
    })
    @ApiResponse(code = 200, message = "新增结果")
    @PostMapping("/delete")
    public GenericRspDTO<NoBody> deleteMarketActivity(@Validated @RequestBody GenericDTO<DeleteMarketActivityReqDTO> deleteMarketActivityDTO) {
        GenericRspDTO deleteMarkeyActivityRspDTO = GenericRspDTO.newSuccessInstance();
        try {
            deleteMarkeyActivityRspDTO=marketActivityService.delete(deleteMarketActivityDTO.getBody());
        } catch (LemonException e) {
            deleteMarkeyActivityRspDTO.setMsgCd(e.getMsgCd());
            deleteMarkeyActivityRspDTO.setMsgInfo(e.getMsgInfo());
        }
        return deleteMarkeyActivityRspDTO;
    }

    @ApiOperation(value="查询营销活动", notes="查询营销活动")
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "atvNm", value = "活动名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "id", value = "活动id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "当前页", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "createDate", value = "活动创建日期", required = false, dataType = "String")
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/find")
    public GenericRspDTO<FindMarkeyActivityRspDTO> findMarketActivity(@Validated @RequestBody GenericDTO<FindMarketActivityReqDTO> findMarketActivityDTO) {
        GenericRspDTO<FindMarkeyActivityRspDTO> findMarkeyActivityRspDTO =GenericRspDTO.newSuccessInstance(new FindMarkeyActivityRspDTO());
        try {
            findMarkeyActivityRspDTO=marketActivityService.find(findMarketActivityDTO.getBody());
        } catch (LemonException e) {
            findMarkeyActivityRspDTO.setMsgCd(e.getMsgCd());
            findMarkeyActivityRspDTO.setMsgInfo(e.getMsgInfo());
        }
        return findMarkeyActivityRspDTO;
    }

    @ApiOperation(value="营销活动维护和审核", notes="营销活动维护和审核")
    /*@ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "活动id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "类型 01-审核 02-维护", required = false, dataType = "String"),
            @ApiImplicitParam(name = "examineStatus", value = "审核状态", required = false, dataType = "String"),
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/maintian")
    public GenericRspDTO<NoBody> maintainMarketActivity(@Validated @RequestBody GenericDTO<MaintainActivityReqDTO> maintainActivityReqDTO) {
        GenericRspDTO<NoBody> findMarkeyActivityRspDTO =GenericRspDTO.newSuccessInstance();
        try {
            findMarkeyActivityRspDTO=marketActivityService.maintain(maintainActivityReqDTO.getBody());
        } catch (LemonException e) {
            findMarkeyActivityRspDTO.setMsgCd(e.getMsgCd());
            findMarkeyActivityRspDTO.setMsgInfo(e.getMsgInfo());
        }
        return findMarkeyActivityRspDTO;
    }


}
