package com.hisun.lemon.mkm.common;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.mkm.entity.BatchFileInfoDo;
import com.hisun.lemon.mkm.service.IBatchFileOperationService;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2017/10/18
 */
public class ExcelFileUtil {

    private static final String EXCEL_XLS = "xls";

    private static final String EXCEL_XLSX = "xlsx";


    /**
     * 判断Excel的版本
     *
     * @param in
     * @param file
     * @return
     * @throws IOException
     */
    public static Workbook getWorkbok(InputStream in, File file) throws Exception {
        Workbook workbook = null;
        try {
            if (file.getName().endsWith(EXCEL_XLS)) {
                workbook = new HSSFWorkbook(in);
            } else if (file.getName().endsWith(EXCEL_XLSX)) {
                workbook = new XSSFWorkbook(in);
            }
        } catch (IOException e) {

        } finally {
            in.close();
        }
        return workbook;
    }

    /**
     * 文件
     *
     * @param file
     * @throws LemonException
     */
    public static void checkExcelVaild(File file) throws LemonException {
        if (!file.exists()) {
            throw new LemonException(MsgCd.BATCH_FILE_NO_EXISTS.getMsgCd());
        }
        if (!(file.isFile() && (file.getName().endsWith(EXCEL_XLS) || file.getName().endsWith(EXCEL_XLSX)))) {
            throw new LemonException(MsgCd.BATCH_FILE_TYPE_ERROR.getMsgCd());
        }
    }

    public static List<BatchFileInfoDo> insertDetail(File file, Map<String, Object> ctx) throws IOException {
        int totNum = 0;
        List<BatchFileInfoDo> list = new LinkedList<>();
        BigDecimal totAmt = new BigDecimal(0);
        FileInputStream is = null ;
        Workbook workbook = null ;
        try {
            is = new FileInputStream(file);
            checkExcelVaild(file);
            workbook = getWorkbok(is, file);
            if ( workbook == null ) {
                return list ;
            }
            Sheet sheet = workbook.getSheetAt(0);
            //文件名为fileNm = 活动号_日期_批次
            String fileName = file.getName();
            String[] fileNameArray = fileName.split("_");
            String atvId = fileNameArray[1];
            String type = fileNameArray[0];
            String recNo = fileName.substring(0, fileName.lastIndexOf(".")).replaceAll("_", "");
            //Iterator<Row> rows = sheet.getLastRowNum()
            Cell cell;


            BatchFileInfoDo temp = new BatchFileInfoDo();
            BigDecimal countAmt = BigDecimal.valueOf(0);
            for ( int i =0 ; i<sheet.getLastRowNum() ; i++) {
                Row row =sheet.getRow(i);
                Iterator<Cell> cells = row.cellIterator();

                //文件第一行为统计信息
                if (row.getRowNum() == 0) {
                    cell = row.getCell(1);
                    totNum = Integer.valueOf(cell.getStringCellValue());
                    cell = row.getCell(3);
                    totAmt = new BigDecimal(cell.getStringCellValue());
                    //判断文件的总笔数是否符合
                    continue;
                }

                //文件第二行为标题，直接跳过
                if (row.getRowNum() == 1) {
                    continue;
                }

                //文件第三行开始为详细数据
                cell = row.getCell(0);
                if(cell == null || JudgeUtils.isBlank(String.valueOf(cell.getRichStringCellValue()))) {
                    break;
                }
                BatchFileInfoDo batchFileInfoDo = new BatchFileInfoDo();
                batchFileInfoDo.setOprTyp(fileNameArray[0]);
                batchFileInfoDo.setOprSts("2");
                batchFileInfoDo.setRecNo(recNo);

                batchFileInfoDo.setCouponNo(String.valueOf(cell.getRichStringCellValue()));
                cell = row.getCell(1);
                batchFileInfoDo.setMblNo(String.valueOf(cell.getStringCellValue()));
                cell = row.getCell(2);
                batchFileInfoDo.setReleaseDt(LocalDate.parse(cell.getStringCellValue()));
                cell = row.getCell(3);
                batchFileInfoDo.setAmt(new BigDecimal(cell.getStringCellValue()));
                countAmt = countAmt.add(new BigDecimal(cell.getStringCellValue()));
                if ("2".equals(type)) {
                    cell = row.getCell(4);
                    if (cell.getStringCellValue() != null) {
                        String delayTimeStr = cell.getStringCellValue().replaceAll("-", "").replaceAll("\\:", "").replaceAll(" ", "").trim();
                        batchFileInfoDo.setDelayTm(DateTimeUtils.parseLocalDateTime(delayTimeStr));
                    }
                }

                list.add(batchFileInfoDo);
            }

            if (totNum != list.size()) {
                throw new LemonException(MsgCd.FILE_TOLNUM_NO_EQUELS.getMsgCd(), MsgCd.FILE_TOLNUM_NO_EQUELS.getMsgInfo());
            }

            if (totAmt.compareTo(countAmt) != 0) {
                throw new LemonException(MsgCd.FILE_TOLAMT_NO_EQUELS.getMsgCd(), MsgCd.FILE_TOLAMT_NO_EQUELS.getMsgInfo());
            }
            ctx.put("totNum", totNum);
            ctx.put("totAmt", totAmt);
        } catch (Exception e) {
            //e.printStackTrace();
        } finally {
            is.close();
        }
        return list;
    }

    /**
     *
     * 将数据写入到 Excel 指定 Sheet 页指定开始行中,指定行后面数据向后移动
     *
     * @param rowData
     *            数据
     * @param ctx
     * @param tileStr
     * @return
     * @throws IOException
     */
    public static boolean write(List<BatchFileInfoDo> rowData, String path, Map<String, Object> ctx, String tileStr) throws Exception {
        File file = new File(path);
        XSSFWorkbook workbook =  new XSSFWorkbook();
        workbook.createSheet();
        Sheet sheet = workbook.getSheetAt(0);

        Integer tolNum = (Integer) ctx.get("totNum");
        BigDecimal tolAmt = (BigDecimal) ctx.get("totAmt");

        int dataSize = rowData.size();
       //设置文件头
        Row tile = sheet.createRow(0);
        //合并单元格
        region(workbook ,0, 0,0,0,5);
        tile.createCell(0).setCellValue(tileStr);
        tile.getCell(0).setCellStyle(getTitleStyle(workbook));
        Row cont = sheet.createRow(1);
        String tolNumStr = "totalNum:" + String.valueOf(tolNum);
        cont.createCell(0).setCellValue(tolNumStr);
        String tolAmtStr  = "totalAmt:" + String.valueOf(tolAmt);
        cont.createCell(1).setCellValue(tolAmtStr);
        int successNum = 0;
        int failNum = 0 ;
        BigDecimal successAmt = BigDecimal.valueOf(0) ;
        BigDecimal failAmt = BigDecimal.valueOf(0)  ;
        Row head = sheet.createRow(2);
        head.createCell(0).setCellValue("券别编号");
        head.createCell(1).setCellValue("用户手机号");
        head.createCell(2).setCellValue("状态");
        head.createCell(3).setCellValue("金额");
        for (int i = 0; i < dataSize; i++) {
            BatchFileInfoDo batchFileInfoDo = rowData.get(i) ;
            Row row = sheet.createRow(i+3);
            row.createCell(0).setCellValue(batchFileInfoDo.getCouponNo());
            row.createCell(1).setCellValue(batchFileInfoDo.getMblNo());
            if ("1".equals(batchFileInfoDo.getOprSts())) {
                row.createCell(2).setCellValue("success");
                successNum += 1;
                successAmt = successAmt.add(batchFileInfoDo.getAmt());
            } else {
                row.createCell(2).setCellValue("failure");
                failAmt = failAmt.add(batchFileInfoDo.getAmt());
                failNum += 1;
            }
            row.createCell(3).setCellValue(String.valueOf(batchFileInfoDo.getAmt()));
        }

        String successStr = "success :" + successNum ;
        ctx.put("successNum" ,successNum) ;
        cont.createCell(2).setCellValue(successStr);
        String failureStr = "failure :" + failNum ;
        ctx.put("failNum" ,failNum) ;
        cont.createCell(3).setCellValue(failureStr);
        String successAmtStr = "success amt :" + String.valueOf(successAmt);
        ctx.put("successAmt" ,successAmt) ;
        cont.createCell(4).setCellValue(failureStr);
        String failAmtStr = "failure amt :" + String.valueOf(failAmt);
        ctx.put("failAmt" ,failAmt) ;
        cont.createCell(5).setCellValue(failAmtStr);
        for (int i = 0 ; i<cont.getLastCellNum() ; i++) {
            sheet.autoSizeColumn(i);
            sheet.autoSizeColumn(i, true);
        }
        FileOutputStream out = null ;
        if (workbook != null) {
            try {
                out = new FileOutputStream(file) ;
                workbook.write(out);
                ctx.put("rsFilePath", path);
            } catch (IOException e) {
            } finally {
                if (out != null ) {
                    out.close();
                }
            }
        }
        return true;
    }

    /**
     *
     * 合并单元格
     *
     * @param sheetIx
     *            指定 Sheet 页，从 0 开始
     * @param firstRow
     *            开始行
     * @param lastRow
     *            结束行
     * @param firstCol
     *            开始列
     * @param lastCol
     *            结束列
     */
    public static void region(Workbook workbook, int sheetIx, int firstRow, int lastRow, int firstCol, int lastCol) {
        Sheet sheet = workbook.getSheetAt(sheetIx);
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }
    public static XSSFCellStyle getTitleStyle(XSSFWorkbook workbook) {
        // 设置字体
        XSSFFont font = workbook.createFont();
        // 设置字体大小
        font.setFontHeightInPoints((short)18);
        // 字体加粗
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 设置字体名字
        font.setFontName("Courier New");
        // 设置样式;
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        // 设置左边框;
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        // 设置右边框;
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(HSSFColor.BLACK.index);
        // 设置顶边框;
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(HSSFColor.BLACK.index);
        // 在样式用应用设置的字体;
        style.setFont(font);
        // 设置自动换行;
        style.setWrapText(false);
        // 设置水平对齐的样式为居中对齐;
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 设置垂直对齐的样式为居中对齐;
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        return style;

    }
//    public static void main(String[] args) {
//        String path  = "D:\\data\\mkm\\upload\\1_1234567789_hh_14_rs.xlsx";
//
//        Map<String , Object> ctx = new HashMap<String , Object>() ;
//        ctx.put("tolNum",100);
//        ctx.put("tolAmt",BigDecimal.valueOf(5000));
//        List<BatchFileInfoDo> lsit = new ArrayList<BatchFileInfoDo>() ;
//        BatchFileInfoDo batchFileInfoDo = new BatchFileInfoDo();
//        batchFileInfoDo.setRecNo("1234567789");
//        batchFileInfoDo.setAmt(BigDecimal.valueOf(50));
//        batchFileInfoDo.setMblNo("1235847888");
//        batchFileInfoDo.setCouponNo("kkkkkkkkkkkk");
//        batchFileInfoDo.setReleaseDt(DateTimeUtils.getCurrentLocalDate());
//        batchFileInfoDo.setStatus("1");
//        for (int i = 0 ; i<10000 ;i++) {
//            BatchFileInfoDo bf = new BatchFileInfoDo();
//            BeanUtils.copyProperties(bf ,batchFileInfoDo);
//            lsit.add(bf) ;
//        }
//
//        try {
//            ExcelFileUtil.write(lsit ,path ,ctx ,"回盘文件");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//    public static void main(String[] args) {
//        File a = new File("D:/123.xls");
//        try {
//            insertDetail(a);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        System.out.println();
//    }
}
