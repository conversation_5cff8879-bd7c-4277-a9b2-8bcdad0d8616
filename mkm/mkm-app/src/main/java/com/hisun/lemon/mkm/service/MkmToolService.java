package com.hisun.lemon.mkm.service;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.mkm.entity.MkmActivityDO;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.req.innerDto.QueryUserSeaCcyReqDTO;
import com.hisun.lemon.mkm.res.dto.*;
import com.hisun.lemon.mkm.res.innerDto.QueryUserSeaCcyResDTO;

/**
 * Created by chen on 7/22 0022.
 */
public interface MkmToolService {

    /**
     * 获取和充值海币
     * @param rechargeMkmToolReqDTO
     * @return
     */
    GenericRspDTO<RechargeMkmToolResDTO> recharge(GenericDTO<RechargeMkmToolReqDTO> rechargeMkmToolReqDTO);

    /**
     * 获取电子券或者优惠券
     * @param getConponReqDTO
     * @return
     */
    GenericRspDTO<GetConponResDTO> getConpon(GenericDTO<GetConponReqDTO> getConponReqDTO);

    /**
     * 海币消费接口
     * @param consumeSeaCcyReqDTO
     * @return
     */
    GenericRspDTO<ConsumeSeaCcyResDTO> consumeSeaCyy(GenericDTO<ConsumeSeaCcyReqDTO> consumeSeaCcyReqDTO);

    /**
     * 电子券消费接口
     * @param consumeCouponReqDTO
     * @return
     */
    GenericRspDTO<ConsumeCouponResDTO> consumeCoupon(GenericDTO<ConsumeCouponReqDTO> consumeCouponReqDTO);

    /**
     * 消费撤销撤销接口
     * @param revokedConsumeCouponReqDTO
     * @return
     */
    GenericRspDTO<RevokedConsumeCouponResDTO> revokedConsume(GenericDTO<RevokedConsumeCouponReqDTO> revokedConsumeCouponReqDTO);

    GenericRspDTO<FindMarkeyActivityRspDTO> qurey(FindMarketActivityReqDTO findMarketActivityDTO);

    GenericRspDTO<QueryUserMkmToolRspDTO> queryUserMkmTool(QueryUserMkmToolReqDTO queryUserMkmToolReqDTO);

    /**
     * 选择最优的优惠活动
     * @param autoRealeaseReqDTO
     * @return
     */
    MkmActivityDO queryOptimalActivity(AutoRealeaseReqDTO autoRealeaseReqDTO);

    /**
     * 发放撤销
     * @param body
     * @return
     */
    GenericRspDTO<RealeaseRevokeRspDTO> realeaseRevoke(RealeaseRevokeReqDTO body);

    /**
     * 海币转赠
     * @param req
     * @return
     */
    GenericRspDTO<SeaccyGitfRspDTO> seaccyGitf(GenericDTO<SeaccyGitfRepDTO> req);

    /**
     * 海币交易记录查询
     * @param req
     * @return
     */
    GenericRspDTO<SeaccyTraceDetailRspDTO> seaccyTraceDetail(GenericDTO<SeaccyTraceDetailRepDTO> req);

    GenericRspDTO<QueryUserSeaCcyResDTO> queryUserSeaccy(QueryUserSeaCcyReqDTO body);


    MkmActivityDO getMkmActivity(String atvId);
}
