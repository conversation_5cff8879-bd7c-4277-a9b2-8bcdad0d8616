/*
 * @ClassName IMkmCouponDetailDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.mkm.entity.MkmActivityDO;
import com.hisun.lemon.mkm.entity.MkmCouponDetailDO;
import com.hisun.lemon.mkm.req.dto.QueryUserMkmToolReqDTO;
import com.hisun.lemon.mkm.res.dto.CouponDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface IMkmCouponDetailDao extends BaseDao<MkmCouponDetailDO> {
    MkmCouponDetailDO getByCouponId(String couponNo);

    MkmCouponDetailDO getByCouponNo(String couponNo);


    List<CouponDetail> queryUserCoupon(@Param("queryUserMkmToolReqDTO") QueryUserMkmToolReqDTO queryUserMkmToolReqDTO, @Param("currentLocalDateTime") LocalDateTime currentLocalDateTime);

    int chenckMonthTime(@Param("atvId") String atvId, @Param("userId") String userId, @Param("times") int times);

    int queryUserCouponCount(@Param("queryUserMkmToolReqDTO") QueryUserMkmToolReqDTO queryUserMkmToolReqDTO, @Param("currentLocalDateTime") LocalDateTime currentLocalDateTime);

    int batchInvalidCoupon();

    List<MkmActivityDO> getSumInvalidAmt();
}