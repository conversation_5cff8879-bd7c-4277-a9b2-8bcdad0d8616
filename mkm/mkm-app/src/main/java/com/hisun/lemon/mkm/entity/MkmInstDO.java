/*
 * @ClassName MkmInstDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MkmInstDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Integer id;
    /**
     * @Fields atvId 活动id
     */
    private String atvId;
    /**
     * @Fields instId 商户id
     */
    private String instId;
    /**
     * @Fields total 发放总量
     */
    private Integer total;
    /**
     * @Fields totalAmt 发放总金额
     */
    private BigDecimal totalAmt;
    /**
     * @Fields totIssAmt 累计总金额
     */
    private BigDecimal totIssAmt;
    /**
     * @Fields totIssCnt 累计总量
     */
    private Integer totIssCnt;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getTotIssAmt() {
        return totIssAmt;
    }

    public void setTotIssAmt(BigDecimal totIssAmt) {
        this.totIssAmt = totIssAmt;
    }

    public Integer getTotIssCnt() {
        return totIssCnt;
    }

    public void setTotIssCnt(Integer totIssCnt) {
        this.totIssCnt = totIssCnt;
    }


}