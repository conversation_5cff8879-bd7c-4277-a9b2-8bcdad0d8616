package com.hisun.lemon.mkm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.mkm.common.Constants;
import com.hisun.lemon.mkm.common.MsgCd;
import com.hisun.lemon.mkm.dao.*;
import com.hisun.lemon.mkm.entity.*;
import com.hisun.lemon.mkm.remote.service.RemoteService;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.res.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;

/**
 * Created by chen on 8/1 0001.
 */
@Service
@Transactional
public class MkmToolServiceTransaction {
    private static final Logger logger = LoggerFactory.getLogger(MkmToolServiceTransaction.class);
    @Resource
    private IMkmSeaCcySeqDao iMkmSeaCcySeqDao;

    @Resource
    private IMkmSeaCcyDetailDao iMkmSeaCcyDetailDao;

    @Resource
    private IMkmActivityDao iMkmActivityDao;

    @Resource
    private IMkmCouponSeqDao iMkmCouponSeqDao;

    @Resource
    private IMkmCouponDetailDao iMkmCouponDetailDao;

    @Resource
    private IMkmInstDao iMkmInstDao;

    @Resource
    private IMkmAtvRuleDao iMkmAtvRuleDao;

    @Resource
    private IIMkmAccountingSeqDao iIMkmAccountingSeqDao;
    @Resource
    private RemoteService remoteService;

//    @Value("${mkm.activityItemS}")
//    private String activityItemS ;
//    @Value("${mkm.activityItemE}")
//    private String activityItemE ;
//    @Value("${mkm.cshItem}")
//    private String cshItem ;
    /**
     * 开事务抽出来的方法
     */
    public void rechargeAndReleasse(RechargeMkmToolReqDTO rechargeMkmToolReqDTO, GenericRspDTO<RechargeMkmToolResDTO> rechargeMkmToolResDTO, MkmSeaCcySeqDO mkmSeaCcySeqDO) {
        //如果交易类型为活动发放类型择需要更新活动剩余量
        String itemD = "";
        String itemC = "";
        if (Constants.SEQ_TYPE_RELEASE.equals(rechargeMkmToolReqDTO.getType()) && JudgeUtils.isNotBlank(rechargeMkmToolReqDTO.getAtvId())) {
            //获取海币发放活动
            MkmActivityDO mkmActivityDO = iMkmActivityDao.getValid(rechargeMkmToolReqDTO.getAtvId());
            MkmAtvRuleDO mkmAtvRuleDO = iMkmAtvRuleDao.get(rechargeMkmToolReqDTO.getAtvId());
            //如果交易类型为发放则借方为海币 ,发放为 公司
            if (mkmActivityDO == null) {
                //获取海币活动记录失败
                rechargeMkmToolResDTO.getBody().setResult(Constants.SEQ_FAIL);
                rechargeMkmToolResDTO.setMsgCd( MsgCd.GET_SEA_CCY_ACIVITY_FAIL. getMsgCd());
                rechargeMkmToolResDTO.setMsgInfo(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgInfo());
                return ;
            } else {
                //检查活动是否还可用
                if (!(Constants.STATUS.equals(mkmActivityDO.getStatus()) && Constants.EXAMINE_STATUS_YES.equals(mkmActivityDO.getExamineStatus()))) {
                    rechargeMkmToolResDTO.setMsgCd( MsgCd.ACIVITY_STATUS_INVAILD. getMsgCd());
                    rechargeMkmToolResDTO.setMsgInfo(MsgCd.ACIVITY_STATUS_INVAILD.getMsgInfo());
                    return ;
                }
                //检查用户领取次数 因为前面会先插入一条异常的流水所以获取的次数要先加1 mkmAtvRuleDO.getReceiveTimes()+1
                int times = iMkmSeaCcySeqDao.checkTimes(rechargeMkmToolReqDTO.getAtvId(), rechargeMkmToolReqDTO.getUserId() ,mkmAtvRuleDO.getReceiveTimes() +1 , mkmAtvRuleDO.getReceiveCycle());
                if (times == 0) {
                    rechargeMkmToolResDTO.setMsgCd( MsgCd.BEYOND_TIMES. getMsgCd());
                    rechargeMkmToolResDTO.setMsgInfo(MsgCd.BEYOND_TIMES.getMsgInfo());
                    return;
                }
                //更新活动剩余量
                MkmActivityDO mkmActivityDOUpdate = new MkmActivityDO();
                mkmActivityDOUpdate.setId(rechargeMkmToolReqDTO.getAtvId());
                mkmActivityDOUpdate.setRemainNum(rechargeMkmToolReqDTO.getCount());
                mkmActivityDOUpdate.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                int updateAct = iMkmActivityDao.updateRemainById(mkmActivityDOUpdate);
                if ( updateAct !=1 ) {
                    rechargeMkmToolResDTO.getBody().setResult(Constants.SEQ_FAIL);
                    rechargeMkmToolResDTO.setMsgCd( MsgCd.UPDATE_ACIVITY_FAIL. getMsgCd());
                    rechargeMkmToolResDTO.setMsgInfo(MsgCd.UPDATE_ACIVITY_FAIL.getMsgInfo());
                    return;
                }

            }

        }

//        //账充值或者发放账务处理
////
//        //借：其他应付款-暂收-收银台         100
//        //贷：其他应付款-中转挂账-海币       100
//        //交易类型
//        String type = Constants.ACC_TRACE_TYPE_RC ;
//        String remark = "海币充值";
//        if (!Constants.SEQ_TYPE_RELEASE.equals(mkmSeaCcySeqDO.getType())) {
//            //如果交易类型为发放则借方为海币 ,发放为 公司 如果为充值则借方为收银台中间科目。贷方为海币科目
//            //发放 ：借：其他应付款-中转挂账-海币        10000 贷：应收账款-海币充值-XX公司       10000
//            //充值借：其他应付款-暂收-收银台         100  贷：其他应付款-中转挂账-海币       100
//            itemD = activityItemS ;
//            itemC = cshItem;
//
//        } else {
//             type = Constants.ACC_TRACE_TYPE_RL ;
//            remark = "海币发放";
//        }
//        //海币换算为金额
//        BigDecimal amt = BigDecimal.valueOf(mkmSeaCcySeqDO.getCount()).multiply(BigDecimal.valueOf(0.01));
//        MkmAccountingSeqDO mkmAccountingSeqDO = new MkmAccountingSeqDO(
//                mkmSeaCcySeqDO.getSeq(),
//                "",
//                itemD,
//                itemC,
//                Constants.SEQ_EXCEPTION,
//                DateTimeUtils.getCurrentLocalDateTime(),
//                amt,
//                "",
//                "",
//                type,
//                Constants.CAP_TYPE_CASH,
//                remark
//        );
//        mkmAccountingSeqDO.setModifyTime(mkmSeaCcySeqDO.getCreateTime());
//        mkmAccountingSeqDO.setCreateTime(mkmSeaCcySeqDO.getModifyTime());
//        int i = iIMkmAccountingSeqDao.insert(mkmAccountingSeqDO);
//        if (i != 1) {
//            throw new LemonException(MsgCd.ADD_ACOUNTING_SEQ_FAILED.getMsgCd(),MsgCd.ADD_ACOUNTING_SEQ_FAILED.getMsgInfo());
//        }
//        //获取记账列表
//        List<AccountingReqDTO> acmList = remoteService.getAccList(mkmAccountingSeqDO);
//        try {
//            acmComponent.requestAc(acmList);
//        } catch ( LemonException l) {
//            throw new LemonException(l.getMsgCd() , l.getMsgInfo());
//        }
//
//        //更新账务登记状态
//        mkmAccountingSeqDO.setAcmStatus(Constants.SEQ_SUCCESS);
//        iIMkmAccountingSeqDao.update(mkmAccountingSeqDO);

        //账务登记结束
        //登记客户海币记录
        MkmSeaCcyDetailDO mkmSeaCcyDetailDO = iMkmSeaCcyDetailDao.get(mkmSeaCcySeqDO.getUserId());
        if (mkmSeaCcyDetailDO == null) {
            mkmSeaCcyDetailDO = new MkmSeaCcyDetailDO();
            BeanUtils.copyProperties(mkmSeaCcyDetailDO,mkmSeaCcySeqDO);
            mkmSeaCcyDetailDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcyDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcyDetailDO.setUserId(mkmSeaCcySeqDO.getUserId());
            mkmSeaCcyDetailDO.setStatus(Constants.SEA_CCY_DETAIL_STATUS_N);
            try {
                int insertDt = iMkmSeaCcyDetailDao.insert(mkmSeaCcyDetailDO);
                if (insertDt != 1) {
                    throw new LemonException();
                }
            } catch (Exception e) {
                rechargeMkmToolResDTO.setMsgCd(MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgCd());
                rechargeMkmToolResDTO.setMsgInfo(MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgInfo());
                throw new LemonException(MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgCd(), MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgInfo());
            }
        }else {
            mkmSeaCcyDetailDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcyDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcyDetailDO.setCount(mkmSeaCcyDetailDO.getCount() + rechargeMkmToolReqDTO.getCount());
            try {
                int updedetail = iMkmSeaCcyDetailDao.update(mkmSeaCcyDetailDO);
                if (updedetail != 1) {
                    rechargeMkmToolResDTO.setMsgCd(MsgCd.UPDATE_SEA_CCY_DETAIL_FAIL.getMsgCd());
                    rechargeMkmToolResDTO.setMsgInfo(MsgCd.UPDATE_SEA_CCY_DETAIL_FAIL.getMsgInfo());
                    throw new LemonException();
                }
            } catch (Exception e) {
                rechargeMkmToolResDTO.setMsgCd(MsgCd.UPDATE_SEA_CCY_DETAIL_FAIL.getMsgCd());
                rechargeMkmToolResDTO.setMsgInfo(MsgCd.UPDATE_SEA_CCY_DETAIL_FAIL.getMsgInfo());
                throw new LemonException(MsgCd.UPDATE_SEA_CCY_DETAIL_FAIL.getMsgCd(), MsgCd.UPDATE_SEA_CCY_DETAIL_FAIL.getMsgInfo());
            }
        }
        mkmSeaCcySeqDO.setStatus(Constants.SEQ_SUCCESS);
        iMkmSeaCcySeqDao.update(mkmSeaCcySeqDO);
    }

    public void getConpouTransaction(GetConponReqDTO getConponReqDTO, GenericRspDTO<GetConponResDTO> genericDTO, MkmCouponSeqDO mkmCouponSeqDO) {
        GetConponResDTO getConponResDTO = genericDTO.getBody();

        //获取发放活动
        MkmActivityDO mkmActivityDO = iMkmActivityDao.getValid(getConponReqDTO.getAtvId());
        MkmAtvRuleDO mkmAtvRuleDO = iMkmAtvRuleDao.get(getConponReqDTO.getAtvId());
        if (mkmActivityDO == null) {
            //获取活动记录失败
            getConponResDTO.setResult(Constants.SEQ_FAIL);
            genericDTO.setMsgCd(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgInfo());
            return;
        } else {

            //检查活动是否还可用
            if (!(Constants.STATUS.equals(mkmActivityDO.getStatus()) && Constants.EXAMINE_STATUS_YES.equals(mkmActivityDO.getExamineStatus()))) {
                getConponResDTO.setResult(Constants.SEQ_FAIL);
                genericDTO.setMsgCd(MsgCd.ACIVITY_STATUS_INVAILD.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.ACIVITY_STATUS_INVAILD.getMsgInfo());
            }

            int times = iMkmCouponSeqDao.checkTimes(getConponReqDTO.getAtvId(), getConponReqDTO.getUserId() ,mkmAtvRuleDO.getReceiveTimes(),mkmAtvRuleDO.getReceiveCycle());
            if (times == 0) {
                genericDTO.setMsgCd(MsgCd.BEYOND_TIMES.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.BEYOND_TIMES.getMsgInfo());
                return ;
            }

            //更新商户发放数量
            if (JudgeUtils.isNotBlank(getConponReqDTO.getInstId()) && JudgeUtils.isNotBlank(mkmActivityDO.getInstId())) {
                MkmInstDO mkmInstDO = new MkmInstDO();
                mkmInstDO.setInstId(getConponReqDTO.getInstId());
                mkmInstDO.setAtvId(getConponReqDTO.getAtvId());
                mkmInstDO.setTotal(1);
                mkmInstDO.setTotalAmt(getConponReqDTO.getAmt());
                mkmInstDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                try{
                    int upInst = iMkmInstDao.updateRelease(mkmInstDO);
                }catch (Exception e){
                    throw new LemonException(MsgCd.UPDATE_INST_RELEASE_FAILED.getMsgCd(),MsgCd.UPDATE_INST_RELEASE_FAILED.getMsgInfo());
                }
            }

            //更新活动剩余量
            MkmActivityDO mkmActivityDOUpdate = new MkmActivityDO();
            mkmActivityDOUpdate.setId(getConponReqDTO.getAtvId());
            mkmActivityDOUpdate.setRemainAmt(getConponReqDTO.getAmt());
            mkmActivityDOUpdate.setRemainNum(1);
            mkmActivityDOUpdate.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            int updateAct = iMkmActivityDao.updateRemainById(mkmActivityDOUpdate);
            if ( updateAct != 1 ) {
                getConponResDTO.setResult(Constants.SEQ_FAIL);
                genericDTO.setMsgCd(MsgCd.UPDATE_ACIVITY_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.UPDATE_ACIVITY_FAIL.getMsgInfo());
                throw new LemonException(MsgCd.UPDATE_ACIVITY_FAIL.getMsgCd(),MsgCd.UPDATE_ACIVITY_FAIL.getMsgInfo());
            }

            //登记电子券
            MkmCouponDetailDO mkmCouponDetailDO = new MkmCouponDetailDO();
            BeanUtils.copyProperties(mkmCouponDetailDO,mkmCouponSeqDO);
            mkmCouponDetailDO.setBalance(mkmCouponSeqDO.getAmt());
            mkmCouponDetailDO.setCouponNo(IdGenUtils.generateId("mkm",mkmActivityDO.getId(),10));
            mkmCouponDetailDO.setCouponValTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(mkmAtvRuleDO.getStartDays()));
            //判断券别的整体失效时间是否为null如果不为null则判断生效时间+有效天数是否大于券别整体失效时间，如果不大于则券别失效时间为生效时间+有效天数 否则为券别整体失效时间
            if (mkmAtvRuleDO.getCouponInvalTm() != null ) {
                Duration duration = Duration.between(mkmCouponDetailDO.getCouponValTm().plusDays(mkmAtvRuleDO.getCouponValDays()) ,mkmAtvRuleDO.getCouponInvalTm() ) ;
                if (duration.toMillis() > 0) {
                    mkmCouponDetailDO.setCouponInvalTm(mkmCouponDetailDO.getCouponValTm().plusDays(mkmAtvRuleDO.getCouponValDays()));
                } else {
                    mkmCouponDetailDO.setCouponInvalTm(mkmAtvRuleDO.getCouponInvalTm());
                }
            } else {
                mkmCouponDetailDO.setCouponInvalTm(mkmCouponDetailDO.getCouponValTm().plusDays(mkmAtvRuleDO.getCouponValDays()));
            }
            mkmCouponDetailDO.setStatus(Constants.COUPON_DETAIL_STATUS_UN_USER);
            mkmCouponDetailDO.setInstId(getConponReqDTO.getInstId());
            mkmCouponDetailDO.setCouponName(mkmActivityDO.getCouponName());
            if (Constants.MK_TOOL_DISCOUNT.equals(mkmCouponDetailDO.getMkTool())) {
                mkmCouponDetailDO.setDiscount(getConponReqDTO.getDiscount());
            }
            try {
                int couponDetail = iMkmCouponDetailDao.insert(mkmCouponDetailDO);
                if (couponDetail != 1) {
                    //金额回滚
                    throw new LemonException();
                }
            }catch (Exception e) {
                genericDTO.setMsgCd(MsgCd.INSERT_CONPON_DETAIL_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.INSERT_CONPON_DETAIL_FAIL.getMsgInfo());
                throw new LemonException(MsgCd.INSERT_CONPON_DETAIL_FAIL.getMsgCd(), MsgCd.INSERT_CONPON_DETAIL_FAIL.getMsgInfo());
            }
            //检查用户领取次数
            int count = iMkmCouponSeqDao.checkTimes(getConponReqDTO.getAtvId(), getConponReqDTO.getUserId() ,mkmAtvRuleDO.getReceiveTimes(),mkmAtvRuleDO.getReceiveCycle());
            if (count == 0) {
                genericDTO.getBody().setGetFlag("0");
            } else {
                genericDTO.getBody().setGetFlag(count+"");
            }
            mkmCouponSeqDO.setStatus(Constants.SEQ_SUCCESS);
            mkmCouponSeqDO.setCouponNo(mkmCouponDetailDO.getCouponNo());
            genericDTO.getBody().setConponNo(mkmCouponDetailDO.getCouponNo());
            iMkmCouponSeqDao.update(mkmCouponSeqDO);
        }
    }

    public void consumeCouponTransaction(ConsumeCouponReqDTO consumeCouponReqDTO, GenericRspDTO genericDTO, MkmCouponSeqDO mkmCouponSeqDO, MkmCouponDetailDO mkmCouponDetailDO) {
        //判断商户是否可以使用该优惠
        int count = iMkmInstDao.judgeConsumeInst(mkmCouponDetailDO.getAtvId() , consumeCouponReqDTO.getInstId()) ;
        if (count == 0 ) {
            genericDTO.setMsgCd(MsgCd.INST_NOT_CONSUME.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.INST_NOT_CONSUME.getMsgInfo());
            return ;
        }
        //修改电子券为已用状态
        MkmCouponDetailDO upDo = new MkmCouponDetailDO();
        upDo.setStatus(Constants.COUPON_DETAIL_STATUS_USER);
        upDo.setId(mkmCouponDetailDO.getId());
        upDo.setOrderNo(consumeCouponReqDTO.getOrderNo());
        upDo.setInstId(consumeCouponReqDTO.getInstId());
        upDo.setOrderAmt(consumeCouponReqDTO.getOrderAmt());
        upDo.setAmt(mkmCouponSeqDO.getAmt());
        upDo.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        //余额暂时是整张使用
        upDo.setBalance(BigDecimal.valueOf(0));
        int upRs = iMkmCouponDetailDao.update(upDo);
        if (upRs != 1) {
            genericDTO.setMsgCd(MsgCd.UPDATE_COUPON_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.UPDATE_COUPON_FAIL.getMsgInfo());
            return ;
        }
        //更新活动累计使用金额和数量
        MkmActivityDO mkmActivityDO = new MkmActivityDO();
        mkmActivityDO.setId(mkmCouponDetailDO.getAtvId());
        mkmActivityDO.setAcltAmt(mkmCouponDetailDO.getAmt());
        mkmActivityDO.setAclt(1);

        try {
            int upAct = iMkmActivityDao.updateAclt(mkmActivityDO);
            if (upAct != 1) {
                //回滚
                throw new LemonException();
            }
            if (Constants.COUPON_DETAIL_STATUS_REVOKE.equals(mkmCouponDetailDO.getStatus())) {
                mkmActivityDO.setId(mkmCouponDetailDO.getAtvId());
                mkmActivityDO.setRemainNum(1);
                if (mkmCouponDetailDO.getAmt() != null) {
                    mkmActivityDO.setRemainAmt(mkmCouponDetailDO.getAmt());
                }
                int upActRemain = iMkmActivityDao.updateRemainById(mkmActivityDO);
                if (upActRemain == 0) {
                    throw new LemonException();
                }
            }
        }catch (Exception e) {
            genericDTO.setMsgCd(MsgCd.UPDATE_ACTIVITY_ACLT.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.UPDATE_ACTIVITY_ACLT.getMsgInfo());
            throw new LemonException( MsgCd.UPDATE_ACTIVITY_ACLT.getMsgCd(), MsgCd.UPDATE_ACTIVITY_ACLT.getMsgInfo());
        }
        //更新商户消费累计金额
        if (JudgeUtils.isNotBlank(consumeCouponReqDTO.getInstId())) {
            MkmInstDO mkmInstDO = new MkmInstDO();
            mkmInstDO.setInstId(consumeCouponReqDTO.getInstId());
            mkmInstDO.setAtvId(mkmCouponDetailDO.getAtvId());
            mkmInstDO.setTotIssAmt(mkmCouponDetailDO.getAmt());
            mkmInstDO.setTotIssCnt(1);
            mkmInstDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            if (Constants.COUPON_DETAIL_STATUS_REVOKE.equals(mkmCouponDetailDO.getStatus())) {
                mkmInstDO.setTotal(1);
                mkmInstDO.setTotalAmt(mkmCouponDetailDO.getAmt());
            }
            try{
                int upInst = iMkmInstDao.updateRelease(mkmInstDO);
            }catch (Exception e){
                throw new LemonException(MsgCd.UPDATE_INST_CONSUME_FAILED.getMsgCd(),MsgCd.UPDATE_INST_CONSUME_FAILED.getMsgInfo());
            }
        }
        mkmCouponSeqDO.setStatus(Constants.SEQ_SUCCESS);
        mkmCouponSeqDO.setAtvId(mkmCouponDetailDO.getAtvId());
        iMkmCouponSeqDao.update(mkmCouponSeqDO);
    }

    public void revokeEleConsumeTransaction(RevokedConsumeCouponReqDTO revokedConsumeCouponReqDTO, GenericRspDTO genericDTO, MkmCouponSeqDO mkmCouponSeqDO, MkmCouponSeqDO oriSeq) {

        //获取原电子券明细
        MkmCouponDetailDO mkmCouponDetailDO = iMkmCouponDetailDao.getByCouponNo(mkmCouponSeqDO.getCouponNo());
        BigDecimal oriAmt = BigDecimal.valueOf(0);
        String traceType = revokedConsumeCouponReqDTO.getType() ;
        if (mkmCouponDetailDO == null ||
                (mkmCouponDetailDO.getBalance().add(revokedConsumeCouponReqDTO.getAmt()).compareTo(mkmCouponDetailDO.getAmt()) > 0 && !Constants.SEQ_TYPE_REFUND_REVOKE.equals(traceType))) {
            genericDTO.setMsgCd(MsgCd.COUPON_NOT_EXITE.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.COUPON_NOT_EXITE.getMsgInfo());
            return ;
        } else {
            oriAmt = mkmCouponDetailDO.getAmt();
        }

        //撤销电子券消费流水
        MkmCouponDetailDO reMkmCouponDetailDO = new MkmCouponDetailDO();
        reMkmCouponDetailDO.setId(mkmCouponDetailDO.getId());
        if (Constants.SEQ_TYPE_REFUND.equals(revokedConsumeCouponReqDTO.getType())) {
            //退款加上余下额 状态改为退款
            reMkmCouponDetailDO.setBalance(mkmCouponDetailDO.getBalance().add(revokedConsumeCouponReqDTO.getAmt()));
            reMkmCouponDetailDO.setStatus(Constants.COUPON_DETAIL_STATUS_REFUND);
        } else if (Constants.SEQ_TYPE_REVOKED.equals(revokedConsumeCouponReqDTO.getType())){
            reMkmCouponDetailDO.setBalance(mkmCouponDetailDO.getAmt());
            reMkmCouponDetailDO.setStatus(Constants.COUPON_DETAIL_STATUS_UN_USER);
        } else {
            //退款撤销则剩余额需要减去
            reMkmCouponDetailDO.setBalance(mkmCouponDetailDO.getBalance().subtract(revokedConsumeCouponReqDTO.getAmt()));
            if (oriSeq.getType().equals(Constants.SEQ_TYPE_REFUND)) {
                if (reMkmCouponDetailDO.getBalance().compareTo(BigDecimal.valueOf(0)) == 0) {
                    reMkmCouponDetailDO.setStatus(Constants.COUPON_DETAIL_STATUS_USER);
                } else {
                    reMkmCouponDetailDO.setStatus(Constants.COUPON_DETAIL_STATUS_REFUND);
                }
            }
        }

        //如果为剩余额小于0怎么报错
        if (reMkmCouponDetailDO.getBalance().compareTo(BigDecimal.valueOf(0)) <0) {
            logger.info("退款金额超过原优惠券的剩余金额");
            genericDTO.setMsgCd(MsgCd.CONSUME_SEA_CYY_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.CONSUME_SEA_CYY_FAIL.getMsgInfo());
            return ;
        }
        reMkmCouponDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());

        int upRs = iMkmCouponDetailDao.update(reMkmCouponDetailDO);
        if (upRs != 1){
            genericDTO.setMsgCd(MsgCd.REVOKED_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.REVOKED_FAIL.getMsgInfo());
            return ;
        }
        //更新活动累计金额
        if (JudgeUtils.isNotBlank(mkmCouponDetailDO.getAtvId())) {
            MkmActivityDO mkmActivityDO = new MkmActivityDO();
            mkmActivityDO.setId(mkmCouponDetailDO.getAtvId());
            if (Constants.SEQ_TYPE_REFUND.equals(revokedConsumeCouponReqDTO.getType())) {
                mkmActivityDO.setAcltAmt(revokedConsumeCouponReqDTO.getAmt().multiply(BigDecimal.valueOf(-1)));
            } else if (Constants.SEQ_TYPE_REVOKED.equals(revokedConsumeCouponReqDTO.getType())){
                mkmActivityDO.setAclt(-1);
                mkmActivityDO.setAcltAmt(mkmCouponDetailDO.getAmt().multiply(BigDecimal.valueOf(-1)));
            } else if (Constants.SEQ_TYPE_REFUND_REVOKE.equals(revokedConsumeCouponReqDTO.getType())) {
                mkmActivityDO.setAcltAmt(revokedConsumeCouponReqDTO.getAmt());
            }

            try {
                int upAct = iMkmActivityDao.updateAclt(mkmActivityDO);
                if (upAct != 1) {
                    throw new LemonException(MsgCd.UPDATE_ACTIVITY_ACLT.getMsgCd(), MsgCd.UPDATE_ACTIVITY_ACLT.getMsgInfo());
                }
            } catch (Exception e) {
                genericDTO.setMsgCd(MsgCd.UPDATE_ACTIVITY_ACLT.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.UPDATE_ACTIVITY_ACLT.getMsgInfo());
                throw new LemonException(MsgCd.UPDATE_ACTIVITY_ACLT.getMsgCd(), MsgCd.UPDATE_ACTIVITY_ACLT.getMsgInfo());
            }
            //更新商户使用额
            if (JudgeUtils.isNotBlank(mkmCouponDetailDO.getInstId())) {
                MkmInstDO instDO = new MkmInstDO();
                instDO.setInstId(mkmCouponDetailDO.getInstId());
                instDO.setAtvId(mkmCouponDetailDO.getAtvId());

                if (Constants.SEQ_TYPE_REFUND.equals(revokedConsumeCouponReqDTO.getType())) {
                    instDO.setTotIssAmt(revokedConsumeCouponReqDTO.getAmt().multiply(BigDecimal.valueOf(-1)));
                } else if (Constants.SEQ_TYPE_REVOKED.equals(revokedConsumeCouponReqDTO.getType())){
                    instDO.setTotIssCnt(-1);
                    instDO.setTotIssAmt(mkmCouponDetailDO.getAmt().multiply(BigDecimal.valueOf(-1)));

                } else if (Constants.SEQ_TYPE_REFUND_REVOKE.equals(revokedConsumeCouponReqDTO.getType())) {
                    instDO.setTotIssAmt(revokedConsumeCouponReqDTO.getAmt());
                }
                try {
                    int upInst = iMkmInstDao.updateRelease(instDO);
                } catch (Exception e) {
                    genericDTO.setMsgCd(MsgCd.UPDATE_INST_CONSUME_FAILED.getMsgCd());
                    genericDTO.setMsgInfo(MsgCd.UPDATE_INST_CONSUME_FAILED.getMsgInfo());
                    throw new LemonException(MsgCd.UPDATE_INST_CONSUME_FAILED.getMsgCd(), MsgCd.UPDATE_INST_CONSUME_FAILED.getMsgInfo());
                }
            }
        }
        MkmCouponSeqDO upStatusSeqDO = new MkmCouponSeqDO();
        upStatusSeqDO.setSeq(oriSeq.getSeq());
        if (Constants.SEQ_TYPE_REFUND.equals(revokedConsumeCouponReqDTO.getType())) {
            if (reMkmCouponDetailDO.getBalance().compareTo(oriAmt) == 0){
                upStatusSeqDO.setStatus(Constants.SEQ_REFUND);
            }else if (reMkmCouponDetailDO.getBalance().compareTo(oriAmt) < 0){
                upStatusSeqDO.setStatus(Constants.SEQ_REFUND_PART);
            } else {
                genericDTO.setMsgCd(MsgCd.REVOKE_REFUND_BEYONG.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.REVOKE_REFUND_BEYONG.getMsgInfo());
                throw new LemonException(MsgCd.REVOKE_REFUND_BEYONG.getMsgCd(), MsgCd.REVOKE_REFUND_BEYONG.getMsgInfo());
            }
        } else if (Constants.SEQ_TYPE_REVOKED.equals(revokedConsumeCouponReqDTO.getType())){
            upStatusSeqDO.setStatus(Constants.SEQ_REVOKED);
        } else if (Constants.SEQ_TYPE_REFUND_REVOKE.equals(revokedConsumeCouponReqDTO.getType())) {
            upStatusSeqDO.setStatus(Constants.SEQ_REVOKED);

            //退款撤销需要更新原交易流水的状态让后续还可以退款
            MkmCouponSeqDO paySeq = new MkmCouponSeqDO();
            paySeq.setSeq(oriSeq.getOriSeq());
            if (reMkmCouponDetailDO.getBalance().compareTo(BigDecimal.valueOf(0)) == 0) {
                //通过退款后的金额为原来交易的金额
                paySeq.setStatus(Constants.SEQ_SUCCESS);
            } else if (reMkmCouponDetailDO.getBalance().compareTo(BigDecimal.valueOf(0)) > 0){
                paySeq.setStatus(Constants.SEQ_REFUND_PART);
            } else {
                throw new LemonException(MsgCd.UPDATE_INST_CONSUME_FAILED.getMsgCd(), MsgCd.UPDATE_INST_CONSUME_FAILED.getMsgInfo());
            }
            int paySeqRsu = iMkmCouponSeqDao.update(paySeq);


        }
        iMkmCouponSeqDao.update(upStatusSeqDO);
        upStatusSeqDO.setStatus(Constants.SEQ_SUCCESS);
        upStatusSeqDO.setSeq(mkmCouponSeqDO.getSeq());
        iMkmCouponSeqDao.update(upStatusSeqDO);
    }

    public void revokeSeaCyyTransaction(MkmSeaCcySeqDO oriMkmSeaCcySeqDO, RevokedConsumeCouponReqDTO revokedConsumeCouponReqDTO, MkmSeaCcySeqDO mkmSeaCcySeqDO, GenericRspDTO<RevokedConsumeCouponResDTO> genericDTO, String oriSeq) {
        //冲正 客户海币记录金额
        //锁住数据
        MkmSeaCcyDetailDO mkmSeaCcyDetailDO = iMkmSeaCcyDetailDao.get(oriMkmSeaCcySeqDO.getUserId());
        if (mkmSeaCcyDetailDO == null){
            genericDTO.setMsgInfo(MsgCd.CONSUME_SEA_CYY_FAIL.getMsgInfo());
            genericDTO.setMsgCd( MsgCd.CONSUME_SEA_CYY_FAIL.getMsgCd());
            return ;
        }
        mkmSeaCcyDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        if (Constants.SEQ_TYPE_REFUND_REVOKE.equals(revokedConsumeCouponReqDTO.getType())){
            //如果为退款撤销则交易为证交易
            mkmSeaCcyDetailDO.setCount(revokedConsumeCouponReqDTO.getCount() );
        } else {
            //如果为退款撤销则交易为反交易
            mkmSeaCcyDetailDO.setCount(revokedConsumeCouponReqDTO.getCount() * (-1));
        }

        int revokedRs = iMkmSeaCcyDetailDao.updateBymkmSeaCcyDetailDO(mkmSeaCcyDetailDO) ;
        if (revokedRs != 1){
            genericDTO.setMsgInfo(MsgCd.REVOKED_FAIL.getMsgInfo());
            genericDTO.setMsgCd( MsgCd.REVOKED_FAIL.getMsgCd());
            return;
        }

        MkmSeaCcySeqDO upStatusSeqDO = new MkmSeaCcySeqDO();
        upStatusSeqDO.setSeq(oriSeq);

        if (Constants.SEQ_TYPE_REFUND.equals(revokedConsumeCouponReqDTO.getType())) {
            //部分退款
            if (revokedConsumeCouponReqDTO.getCount().compareTo(oriMkmSeaCcySeqDO.getValRefund()) < 0) {
                upStatusSeqDO.setStatus(Constants.SEQ_REFUND_PART);
            } else {
                //全额退款
                upStatusSeqDO.setStatus(Constants.SEQ_REFUND);
            }
        } else {
            //撤销 包括消费撤销和退款撤销
            upStatusSeqDO.setStatus(Constants.SEQ_REVOKED);
        }
        upStatusSeqDO.setValRefund(revokedConsumeCouponReqDTO.getCount());
        try {
            int up = iMkmSeaCcySeqDao.updateValRefund(upStatusSeqDO);
            if (up != 1) {
                throw new LemonException();
            }
            //如果为退款撤销操作需要更新原支付订单的状态
            if (Constants.SEQ_TYPE_REFUND_REVOKE.equals(revokedConsumeCouponReqDTO.getType())) {
                MkmSeaCcySeqDO consumeSeq  = iMkmSeaCcySeqDao.get(oriMkmSeaCcySeqDO.getOriSeq()) ;
                Integer allCount = consumeSeq.getValRefund() + revokedConsumeCouponReqDTO.getCount();
                if (allCount.compareTo(consumeSeq.getCount()) == 0) {
                    consumeSeq.setStatus(Constants.SEQ_SUCCESS);
                } else if (allCount.compareTo(consumeSeq.getCount()) == -1) {
                    consumeSeq.setStatus(Constants.SEQ_REFUND_PART);
                } else {
                    throw new LemonException(MsgCd.REVOKE_REFUND_BEYONG.getMsgCd() ,MsgCd.REVOKE_REFUND_BEYONG.getMsgInfo());
                }
                consumeSeq.setValRefund(revokedConsumeCouponReqDTO.getCount() * (-1));
                int i = iMkmSeaCcySeqDao.updateValRefund(consumeSeq) ;
            }
        } catch (Exception e) {
            logger.error("异常"+e.getMessage());
            throw new LemonException(MsgCd.UPDATE_VAL_REFUND_FAILED.getMsgCd() ,MsgCd.UPDATE_VAL_REFUND_FAILED.getMsgInfo());
        }



    }

    public void revokeReleaseTransaction(RealeaseRevokeReqDTO realeaseRevokeReqDTO, GenericRspDTO<RealeaseRevokeRspDTO> genericDTO, MkmCouponSeqDO mkmCouponSeqDO) {
        MkmCouponDetailDO mkmCouponDetailDO = iMkmCouponDetailDao.getByCouponNo(mkmCouponSeqDO.getCouponNo());
        BigDecimal oriAmt = BigDecimal.valueOf(0);
        if (mkmCouponDetailDO == null) {
            genericDTO.setMsgCd(MsgCd.COUPON_NOT_EXITE.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.COUPON_NOT_EXITE.getMsgInfo());
            return ;
        } else {
            if (!Constants.COUPON_DETAIL_STATUS_UN_USER.equals(mkmCouponDetailDO.getStatus())){
                genericDTO.setMsgCd(MsgCd.COUPON_NOT_EXITE.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.COUPON_NOT_EXITE.getMsgInfo());
                return ;
            }
            //更新活动累计金额
            if (JudgeUtils.isNotBlank(mkmCouponDetailDO.getAtvId())) {
                MkmActivityDO mkmActivityDO = new MkmActivityDO();
                mkmActivityDO.setId(mkmCouponDetailDO.getAtvId());
                mkmActivityDO.setRemainNum(-1);
                if (mkmCouponDetailDO.getAmt() != null) {
                    mkmActivityDO.setRemainAmt(mkmCouponDetailDO.getAmt().multiply(BigDecimal.valueOf(-1)));
                }
                try {
                    int upAct = iMkmActivityDao.updateRemainById(mkmActivityDO);
                    if (upAct != 1) {
                        throw new LemonException();
                    }
                } catch (Exception e) {
                    logger.error("异常"+e.getMessage());
                    genericDTO.setMsgCd(MsgCd.UPDATE_ACIVITY_FAIL.getMsgCd());
                    genericDTO.setMsgInfo(MsgCd.UPDATE_ACIVITY_FAIL.getMsgInfo());
                    throw new LemonException(MsgCd.UPDATE_ACIVITY_FAIL.getMsgCd(), MsgCd.UPDATE_ACIVITY_FAIL.getMsgInfo());
                }
            }
            //更新商户发放数量
            if (JudgeUtils.isNotBlank(mkmCouponDetailDO.getInstId())) {
                MkmInstDO mkmInstDO = new MkmInstDO();
                mkmInstDO.setInstId(mkmCouponDetailDO.getInstId());
                mkmInstDO.setAtvId(mkmCouponDetailDO.getAtvId());
                mkmInstDO.setTotal(-1);
                mkmInstDO.setTotalAmt(mkmCouponDetailDO.getAmt().multiply(BigDecimal.valueOf(-1)));
                mkmInstDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                try{
                    iMkmInstDao.updateRelease(mkmInstDO);
                }catch (Exception e){
                    throw new LemonException(MsgCd.UPDATE_INST_RELEASE_FAILED.getMsgCd(),MsgCd.UPDATE_INST_RELEASE_FAILED.getMsgInfo());
                }
            }
            mkmCouponDetailDO.setStatus(Constants.COUPON_DETAIL_STATUS_REVOKE);
            mkmCouponDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            try {
                int upRs = iMkmCouponDetailDao.update(mkmCouponDetailDO);
                if (upRs != 1){
                    throw new LemonException(MsgCd.REVOKED_FAIL.getMsgCd(), MsgCd.REVOKED_FAIL.getMsgInfo());
                }
                MkmCouponSeqDO upStatusSeqDO = new MkmCouponSeqDO();
                upStatusSeqDO.setSeq(mkmCouponSeqDO.getOriSeq());
                upStatusSeqDO.setStatus(Constants.SEQ_REVOKED);
                iMkmCouponSeqDao.update(upStatusSeqDO);
                upStatusSeqDO.setStatus(Constants.SEQ_SUCCESS);
                upStatusSeqDO.setSeq(mkmCouponSeqDO.getSeq());
                iMkmCouponSeqDao.update(upStatusSeqDO);
            }catch (LemonException e){
                logger.error("异常"+e.getMessage());
                genericDTO.setMsgCd(MsgCd.REVOKED_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.REVOKED_FAIL.getMsgInfo());
                throw new LemonException(MsgCd.REVOKED_FAIL.getMsgCd(), MsgCd.REVOKED_FAIL.getMsgInfo());
            }

        }
    }

    public void seaccyGitfTransaction(GenericDTO<SeaccyGitfRepDTO> req, GenericRspDTO<SeaccyGitfRspDTO> genericDTO, MkmSeaCcySeqDO mkmSeaCcySeqDO) {
        //扣减用户海币
        MkmSeaCcyDetailDO mkmSeaCcyDetailDO = new MkmSeaCcyDetailDO();
        mkmSeaCcyDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmSeaCcyDetailDO.setCount(mkmSeaCcySeqDO.getCount());
        mkmSeaCcyDetailDO.setOrderNo(mkmSeaCcySeqDO.getOrderNo());
        mkmSeaCcyDetailDO.setUserId(mkmSeaCcySeqDO.getUserId());
        int consumeResult = iMkmSeaCcyDetailDao.updateBymkmSeaCcyDetailDO(mkmSeaCcyDetailDO) ;
        if (consumeResult != 1) {
            genericDTO.setMsgCd(MsgCd.CONSUME_SEA_CYY_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.CONSUME_SEA_CYY_FAIL.getMsgInfo());
            return ;
        }
        //增加转赠用户的海币数量
        mkmSeaCcyDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmSeaCcyDetailDO.setCount(mkmSeaCcySeqDO.getCount() * -1);
        mkmSeaCcyDetailDO.setOrderNo(mkmSeaCcySeqDO.getOrderNo());
        mkmSeaCcyDetailDO.setUserId(req.getBody().getGitfUser());
        try {
            int gitfUp = iMkmSeaCcyDetailDao.updateBymkmSeaCcyDetailDO(mkmSeaCcyDetailDO);
            if (gitfUp == -1) {
                throw new LemonException(MsgCd.UPDATE_GITF_FAILED.getMsgCd(), MsgCd.UPDATE_GITF_FAILED.getMsgInfo());
            } else if (gitfUp == 0) {
                //增加一笔用户海币记录
                MkmSeaCcyDetailDO imkmSeaCcyDetailDO = new MkmSeaCcyDetailDO();
                BeanUtils.copyProperties(imkmSeaCcyDetailDO,mkmSeaCcySeqDO);
                imkmSeaCcyDetailDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
                imkmSeaCcyDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                imkmSeaCcyDetailDO.setUserId(req.getBody().getGitfUser());
                imkmSeaCcyDetailDO.setMobile(req.getBody().getgMobile());
                imkmSeaCcyDetailDO.setStatus(Constants.SEA_CCY_DETAIL_STATUS_N);
                try {
                    int insert = iMkmSeaCcyDetailDao.insert(imkmSeaCcyDetailDO);
                    if (insert != 1) {
                        throw new LemonException(MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgCd(), MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgInfo());
                    }
                } catch (Exception e) {
                    genericDTO.setMsgCd(MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgCd());
                    genericDTO.setMsgInfo(MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgInfo());
                    throw new LemonException(MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgCd(), MsgCd.INSERT_SEA_CCY_DETAIL_FAIL.getMsgInfo());
                }
            }
        }catch (Exception e){
            throw new LemonException(MsgCd.UPDATE_GITF_FAILED.getMsgCd(), MsgCd.UPDATE_GITF_FAILED.getMsgInfo());
        }
    }
}
