package com.hisun.lemon.mkm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.mkm.common.Constants;
import com.hisun.lemon.mkm.common.MsgCd;
import com.hisun.lemon.mkm.entity.MkmActivityDO;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.res.dto.*;
import com.hisun.lemon.mkm.service.MarketActivityService;
import com.hisun.lemon.mkm.service.MkmToolService;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * Created by chen on 7/22 0022.
 */

@RestController
@RequestMapping("/mkmTool")
public class MkmToolController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(MkmToolController.class);
    @Resource
    private MkmToolService mkmToolService;

    @Resource
    private MarketActivityService marketActivityService;


    /**
     * 营销工具交易(发放，核销，撤销)
     */
    @ApiOperation(value="客户充值海币", notes="客户充值海币")
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "seq", value = "流水号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mkTool", value = "充值类型 02-海币", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "交易类型 00-充值 01-发放", required = true, dataType = "String"),
            @ApiImplicitParam(name = "atvId", value = "活动id  如果type为01市必输", required = false, dataType = "String"),
            @ApiImplicitParam(name = "mobile", value = "用户手机号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "count", value = "充值数量", required = true, dataType = "Intger"),
            @ApiImplicitParam(name = "rechargeTm", value = "充值时间", required = true, dataType = "LocalDateTime")
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/get")
    public GenericRspDTO<RechargeMkmToolResDTO> getSeaCyy(@Validated @RequestBody GenericDTO<RechargeMkmToolReqDTO> rechargeMkmToolReqDTO) {
        RechargeMkmToolResDTO body =  new RechargeMkmToolResDTO();
        GenericRspDTO<RechargeMkmToolResDTO> rechargeMkmToolResDTO =GenericRspDTO.newSuccessInstance(body);
        try {
            rechargeMkmToolResDTO = mkmToolService.recharge(rechargeMkmToolReqDTO);
        } catch (LemonException e) {
            body.setResult(Constants.SEQ_EXCEPTION);
            rechargeMkmToolResDTO.setMsgCd(e.getMsgCd());
            rechargeMkmToolResDTO.setMsgInfo(e.getMsgInfo());
        }
        return rechargeMkmToolResDTO;
    }

    /**
     * 发放电子券和优惠券接口
     */
    @ApiOperation(value="发放电子券和优惠券接口", notes="发放电子券和优惠券接口")
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "seq", value = "流水号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mkTool", value = "券别类型 01-电子券 03-优惠券", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "交易类型  01-发放 ", required = true, dataType = "String"),
            @ApiImplicitParam(name = "atvId", value = "活动id  如果type为01时必输", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instId", value = "商户编号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "mobile", value = "用户手机号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "amt", value = "金额", required = true, dataType = "BigDecimal"),
            @ApiImplicitParam(name = "rechargeTm", value = "时间", required = true, dataType = "LocalDateTime")
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/getConpon")
    public GenericRspDTO<GetConponResDTO> getConpon(@Validated @RequestBody GenericDTO<GetConponReqDTO> getConponReqDTO) {
        GetConponResDTO body =  new GetConponResDTO();
        GenericRspDTO<GetConponResDTO> getConponResDTO =GenericRspDTO.newSuccessInstance(body);
        try {
            MkmActivityDO mkmActivityDO=mkmToolService.getMkmActivity(getConponReqDTO.getBody().getAtvId());
            if (mkmActivityDO == null) {
                getConponResDTO.setMsgCd(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgCd());
                getConponResDTO.setMsgInfo(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgInfo());
            } else {
                if (Constants.MK_TOOL_DISCOUNT.equals(mkmActivityDO.getMkTool())) {
                    getConponReqDTO.getBody().setDiscount(mkmActivityDO.getDiscount());
                    getConponReqDTO.getBody().setAmt(BigDecimal.valueOf(0));
                } else {
                    getConponReqDTO.getBody().setDiscount(mkmActivityDO.getAmt());
                }
            }
            getConponReqDTO.getBody().setSeq(getConponReqDTO.getMsgId());
            getConponResDTO = mkmToolService.getConpon(getConponReqDTO);
            // body = getConponResDTO.getBody();
            //body.setResult(Constants.SEQ_SUCCESS);
        } catch (LemonException e) {
            body.setResult(Constants.SEQ_EXCEPTION);
            getConponResDTO.setMsgCd(e.getMsgCd());
            getConponResDTO.setMsgInfo(e.getMsgInfo());
        }
        return getConponResDTO;
    }

    /**
     * 海币消费接口
     */
    @ApiOperation(value="海币消费接口", notes="海币消费接口")
/*    @ApiImplicitParams({
            @ApiImplicitParam(name = "seq", value = "流水号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mkTool", value = "券别类型 02-海币", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "交易类型  02-消费 ", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mobile", value = "用户手机号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "count", value = "消费海币数量", required = true, dataType = "Intger"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "consumeTm", value = "时间", required = true, dataType = "LocalDateTime")
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/consumeSeaCcy")
    public GenericRspDTO<ConsumeSeaCcyResDTO> consumeSeaCcy(@Validated @RequestBody GenericDTO<ConsumeSeaCcyReqDTO> consumeSeaCcyReqDTO) {
        ConsumeSeaCcyResDTO body =  new ConsumeSeaCcyResDTO();
        GenericRspDTO<ConsumeSeaCcyResDTO> getConponResDTO =GenericRspDTO.newSuccessInstance(body);
        try {
            getConponResDTO = mkmToolService.consumeSeaCyy(consumeSeaCcyReqDTO);
            // body = getConponResDTO.getBody();
            //body.setResult(Constants.SEQ_SUCCESS);
        } catch (LemonException e) {
            body.setResult(Constants.SEQ_EXCEPTION);
            getConponResDTO.setMsgCd(e.getMsgCd());
            getConponResDTO.setMsgInfo(e.getMsgInfo());
        }
        return getConponResDTO;
    }

    /**
     * 电子券和优惠券消费接口
     */
    @ApiOperation(value="电子券消费接口", notes="电子券消费接口")
   /* @ApiImplicitParams({
            @ApiImplicitParam(name = "seq", value = "流水号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "couponNo", value = "券别编号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mkTool", value = "券别类型 01-电子券 03-优惠券", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "交易类型  02-消费 ", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "instId", value = "商户编号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "orderAmt", value = "订单金额", required = true, dataType = "String"),
            @ApiImplicitParam(name = "consumeTm", value = "时间", required = true, dataType = "LocalDateTime")
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/consumeCoupon")
    public GenericRspDTO<ConsumeCouponResDTO> consumeCoupon(@Validated @RequestBody GenericDTO<ConsumeCouponReqDTO> consumeCouponReqDTO) {
        ConsumeCouponResDTO body =  new ConsumeCouponResDTO();
        GenericRspDTO<ConsumeCouponResDTO> consumeCouponResDTO =GenericRspDTO.newSuccessInstance(body);
        try {
            consumeCouponResDTO = mkmToolService.consumeCoupon(consumeCouponReqDTO);
            // body = getConponResDTO.getBody();
            //body.setResult(Constants.SEQ_SUCCESS);
        } catch (LemonException e) {
            body.setResult(Constants.SEQ_EXCEPTION);
            consumeCouponResDTO.setMsgCd(e.getMsgCd());
            consumeCouponResDTO.setMsgInfo(e.getMsgInfo());
        }
        return consumeCouponResDTO;
    }

    /**
     * 交易撤销接口
     */
    @ApiOperation(value="交易撤销接口", notes="海币消费撤销接口")
  /*  @ApiImplicitParams({
            @ApiImplicitParam(name = "seq", value = "流水号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mkTool", value = "交易营销工具 01-电子券 02-海币 03-优惠券", required = true, dataType = "String"),
            @ApiImplicitParam(name = "oriSeq", value = "原交易流水号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "revokedTm", value = "撤销时间", required = true, dataType = "LocalDateTime")
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/revoconsume")
    public GenericRspDTO<RevokedConsumeCouponResDTO> revokedConsume(@Validated @RequestBody GenericDTO<RevokedConsumeCouponReqDTO> revokedConsumeCouponReqDTO) {
        RevokedConsumeCouponResDTO body =  new RevokedConsumeCouponResDTO();
        GenericRspDTO<RevokedConsumeCouponResDTO> revokedConsumeCouponResDTO =GenericRspDTO.newSuccessInstance(body);
        try {
            revokedConsumeCouponResDTO = mkmToolService.revokedConsume(revokedConsumeCouponReqDTO);
            // body = getConponResDTO.getBody();
            //body.setResult(Constants.SEQ_SUCCESS);
        } catch (LemonException e) {
            body.setResult(Constants.SEQ_EXCEPTION);
            revokedConsumeCouponResDTO.setMsgCd(e.getMsgCd());
            revokedConsumeCouponResDTO.setMsgInfo(e.getMsgInfo());
        }
        return revokedConsumeCouponResDTO;
    }

    @ApiOperation(value="前端查询可领取营销工具", notes="前端查询可领取营销工具")
  /*  @ApiImplicitParams({
            @ApiImplicitParam(name = "atvNm", value = "活动名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "id", value = "活动id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "isntId", value = "商户号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "createDate", value = "活动创建日期", required = false, dataType = "String")
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/query")
    public GenericRspDTO<FindMarkeyActivityRspDTO> queryMarketActivity(@Validated @RequestBody GenericDTO<FindMarketActivityReqDTO> findMarketActivityDTO) {
        GenericRspDTO<FindMarkeyActivityRspDTO> findMarkeyActivityRspDTO =GenericRspDTO.newSuccessInstance(new FindMarkeyActivityRspDTO());
        try {
            findMarkeyActivityRspDTO=mkmToolService.qurey(findMarketActivityDTO.getBody());
        } catch (LemonException e) {
            findMarkeyActivityRspDTO.setMsgCd(e.getMsgCd());
            findMarkeyActivityRspDTO.setMsgInfo(e.getMsgInfo());
        }
        return findMarkeyActivityRspDTO;
    }

    @ApiOperation(value="查询用户可消费营销工具", notes="查询用户可消费营销工具")
   /* @ApiImplicitParams({

            @ApiImplicitParam(name = "userId", value = "", required = true, dataType = "String"),
            @ApiImplicitParam(name = "mkTool", value = "", required = false, dataType = "String"),
            @ApiImplicitParam(name = "releaseDt", value = "活动创建日期", required = false, dataType = "String")
    })*/
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/queryUserMkmTool")
    public GenericRspDTO<QueryUserMkmToolRspDTO> queryUserMkmTool(@Validated @RequestBody GenericDTO<QueryUserMkmToolReqDTO> queryUserMkmToolReqDTO) {
        GenericRspDTO<QueryUserMkmToolRspDTO> queryUserMkmToolRspDTO =GenericRspDTO.newSuccessInstance(new QueryUserMkmToolRspDTO());
        try {
            queryUserMkmToolRspDTO=mkmToolService.queryUserMkmTool(queryUserMkmToolReqDTO.getBody());
        } catch (LemonException e) {
            queryUserMkmToolRspDTO.setMsgCd(e.getMsgCd());
            queryUserMkmToolRspDTO.setMsgInfo(e.getMsgInfo());
        }
        return queryUserMkmToolRspDTO;
    }

    /**
     * 优惠券自动发放
     * @param req
     * @return
     */
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/autoRealease")
    public GenericRspDTO<AutoRealeaseRspDTO> autoRealease(@Validated @RequestBody GenericDTO<AutoRealeaseReqDTO> req) {
        GenericRspDTO<AutoRealeaseRspDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new AutoRealeaseRspDTO());
        try {
            AutoRealeaseReqDTO  autoRealeaseReqDTO = req.getBody();
            MkmActivityDO mkmActivityDO=mkmToolService.queryOptimalActivity(autoRealeaseReqDTO);
            if (mkmActivityDO == null) {
                genericRspDTO.setMsgCd(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgCd());
                genericRspDTO.setMsgInfo(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgInfo());
            } else {
                GetConponReqDTO getConponReqDTO = new GetConponReqDTO();
                BeanUtils.copyProperties(getConponReqDTO, autoRealeaseReqDTO);
                if (Constants.MK_TOOL_DISCOUNT.equals(autoRealeaseReqDTO.getMkTool())) {
                    getConponReqDTO.setAmt(autoRealeaseReqDTO.getOrderAmt().multiply(BigDecimal.valueOf(1).subtract(mkmActivityDO.getDiscount())).divide(BigDecimal.valueOf(1),2,BigDecimal.ROUND_HALF_UP));
                } else {
                    getConponReqDTO.setAmt(mkmActivityDO.getAmt());
                }
                getConponReqDTO.setAtvId(mkmActivityDO.getId());
                getConponReqDTO.setType(Constants.SEQ_TYPE_RELEASE);
                GenericDTO<GetConponReqDTO> genericDTO = GenericDTO.newInstance(getConponReqDTO);
                getConponReqDTO.setSeq(req.getMsgId());
                getConponReqDTO.setMkTool(mkmActivityDO.getMkTool());
                GenericRspDTO<GetConponResDTO> res = mkmToolService.getConpon(genericDTO);
                genericRspDTO.setMsgCd(res.getMsgCd());
                genericRspDTO.setMsgInfo(res.getMsgInfo());
                genericRspDTO.getBody().setConponNo(res.getBody().getConponNo());
                genericRspDTO.getBody().setResult(res.getBody().getResult());
                genericRspDTO.getBody().setAmt(getConponReqDTO.getAmt());
                genericRspDTO.getBody().setMkTool(mkmActivityDO.getMkTool());
            }
        } catch (LemonException e) {
            genericRspDTO.setMsgInfo(e.getMsgInfo());
            genericRspDTO.setMsgCd(e.getMsgCd());
        }
        return genericRspDTO;
    }

    /**
     * 发放撤销
     * @param req
     * @return
     */
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/realeaseRevoke")
    public GenericRspDTO<RealeaseRevokeRspDTO> realeaseRevoke(@Validated @RequestBody GenericDTO<RealeaseRevokeReqDTO> req) {
        GenericRspDTO<RealeaseRevokeRspDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new RealeaseRevokeRspDTO());
        try{
            genericRspDTO = mkmToolService.realeaseRevoke(req.getBody());
        }catch (LemonException e) {

        }
        return genericRspDTO;
    }

    /**
     * 海币赠送
     * @param req
     * @return
     */
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/seaccyGitf")
    public GenericRspDTO<SeaccyGitfRspDTO> seaccyGitf(@Validated @RequestBody GenericDTO<SeaccyGitfRepDTO> req) {
        GenericRspDTO<SeaccyGitfRspDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new SeaccyGitfRspDTO());
        try{
            genericRspDTO = mkmToolService.seaccyGitf(req);
        }catch (LemonException e) {
            genericRspDTO.setMsgInfo(e.getMsgInfo());
            genericRspDTO.setMsgCd(e.getMsgCd());
        }
        return genericRspDTO;
    }

    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/seaccyTraceDetail")
    public GenericRspDTO<SeaccyTraceDetailRspDTO> seaccyTraceDetail(@Validated @RequestBody GenericDTO<SeaccyTraceDetailRepDTO> req) {
        GenericRspDTO<SeaccyTraceDetailRspDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new SeaccyTraceDetailRspDTO());
        try{
            genericRspDTO = mkmToolService.seaccyTraceDetail(req);
        }catch (LemonException e) {
            genericRspDTO.setMsgInfo(e.getMsgInfo());
            genericRspDTO.setMsgCd(e.getMsgCd());
        }
        return genericRspDTO;
    }



}
