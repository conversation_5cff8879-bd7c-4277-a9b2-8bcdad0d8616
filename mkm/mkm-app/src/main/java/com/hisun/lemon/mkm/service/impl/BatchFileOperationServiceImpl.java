package com.hisun.lemon.mkm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.lemon.mkm.common.Constants;
import com.hisun.lemon.mkm.common.ExcelFileUtil;
import com.hisun.lemon.mkm.common.MsgCd;
import com.hisun.lemon.mkm.dao.IBatchFileInfoDao;
import com.hisun.lemon.mkm.dao.IBatchFileRecDao;
import com.hisun.lemon.mkm.dao.IMkmActivityDao;
import com.hisun.lemon.mkm.entity.BatchFileInfoDo;
import com.hisun.lemon.mkm.entity.BatchFileRecDO;
import com.hisun.lemon.mkm.entity.MkmActivityDO;
import com.hisun.lemon.mkm.service.IBatchFileOperationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/7/25
 */
@Service
@Transactional
public class BatchFileOperationServiceImpl extends BaseService implements IBatchFileOperationService {

    private static final Logger logger = LoggerFactory.getLogger(IBatchFileOperationService.class);

    @Resource
    private MessageSource messageSource;
    @Resource
    private IBatchFileInfoDao batchFileInfoDao;

    @Resource
    private IBatchFileRecDao batchFileRecDao;

    @Resource
    private IMkmActivityDao iMkmActivityDao;

    @Value("${mkm.batchFileDir}")
    private String batchFileDir;

    @Value("${mkm.sftp.ip}")
    private String remoteIp;

    @Value("${mkm.sftp.port}")
    private String remotePort;

    @Value("${mkm.sftp.remotePath}")
    private String remotePath;

    @Value("${mkm.sftp.timeout}")
    private String timeout;

    @Value("${mkm.sftp.user}")
    private String username;

    @Value("${mkm.sftp.password}")
    private String password;

    @Value("${mkm.batchFileDir}")
    private String localPath;

    @Override
    public String getBatchFile(String fileName) throws Exception {
      FileSftpUtils.download(remoteIp, Integer.valueOf(remotePort), Integer.valueOf(timeout), remotePath,
                fileName, batchFileDir, username, password);
        return batchFileDir + File.separator + fileName;
    }

    @Override
    public int insertBatchInfo(String path, Map<String, Object> ctx) throws Exception {
        File file = new File(path);
        List<BatchFileInfoDo> list = ExcelFileUtil.insertDetail(file ,ctx);
        if ( list == null || list.isEmpty() ) {
            throw new LemonException(MsgCd.INSERT_BATCH_INFO_FAILURE.getMsgCd()) ;
        }
        int i = batchFileInfoDao.batchInsert(list);
        if (i <= 0 ) {
            throw new LemonException(MsgCd.INSERT_BATCH_INFO_FAILURE.getMsgCd()) ;
        }
        return i ;
    }

    @Override
    public void analyticalData(String recNo) {
        BatchFileRecDO batchFileRecDO = new BatchFileRecDO();
        batchFileRecDO.setModifyTime(LocalDateTime.now());
        batchFileRecDO.setProcessSts(Constants.BATCH_FILE_STS_INIT);
        batchFileInfoDao.updateBatchRec(batchFileRecDO);

        
    }

    @Override
    public void oprData(String oprTyp, String recNo , LocalDateTime invalidTm ,Map<String,Object> ctx) {
            int i = batchFileInfoDao.oprData(oprTyp,recNo);

        //更新信息表
        int u = batchFileInfoDao.updateInfo(recNo) ;
    }

    @Override
    public void creatResultFile(String fileNm, String recNo, String oprTyp, Map<String, Object> ctx) throws Exception {
        String[] fileNameArry = fileNm.split("\\.");
        String rsFileName = fileNameArry[0] + "_rs" +(new Date().getTime()) +".xlsx" ;
        ctx.put("rsFileName" , rsFileName) ;
        String path = batchFileDir + "/" + rsFileName;
        List<BatchFileInfoDo> batchFileInfoDos = batchFileInfoDao.selectFileInfo(recNo);
        Locale locale = LocaleContextHolder.getLocale();
        String tileStr ="" ;
        if ("1".equals(oprTyp)) {
            tileStr = messageSource.getMessage("delayRsFile", null, locale);
        }
        if ("2".equals(oprTyp)) {
            tileStr = messageSource.getMessage("invalidRsFile", null, locale);
        }
        if ("3".equals(oprTyp)) {
            tileStr = messageSource.getMessage("freezeRsFile", null, locale);
        }
        if ("4".equals(oprTyp)) {
            tileStr = messageSource.getMessage("unfreezeRsFile", null, locale);
        }
        ExcelFileUtil.write(batchFileInfoDos,path ,ctx , tileStr);
        BatchFileRecDO batchFileRecDO = new BatchFileRecDO();
        batchFileRecDO.setRecNo(recNo);
        batchFileRecDO.setFailureAmt((BigDecimal) ctx.get("failAmt"));
        batchFileRecDO.setFailureNum((Integer) ctx.get("failNum"));
        batchFileRecDO.setSuccessAmt((BigDecimal) ctx.get("successAmt"));
        batchFileRecDO.setSuccessNum((Integer) ctx.get("successNum"));
        batchFileRecDO.setAcId(fileNm.split("_")[1]);
        batchFileRecDO.setTotAmt((BigDecimal) ctx.get("totAmt"));
        batchFileRecDO.setTotNum((Integer) ctx.get("totNum"));
        batchFileRecDO.setProcessDt(DateTimeUtils.getCurrentLocalDateTime());
        batchFileRecDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        logger.info(batchFileRecDO.getTotNum()+":"+ batchFileRecDO.getSuccessNum());
        if (batchFileRecDO.getTotNum() == batchFileRecDO.getSuccessNum()) {
            batchFileRecDO.setProcessSts("1");
        } else {
            batchFileRecDO.setProcessSts("0");
        }
        batchFileRecDao.updateBatchRec(batchFileRecDO);
        //如果为失效文件，需要返回给活动
        if ("1".equals(oprTyp)) {
            MkmActivityDO mkmActivityDO = new MkmActivityDO();
            mkmActivityDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmActivityDO.setId(batchFileRecDO.getAcId());
            mkmActivityDO.setRemainAmt(BigDecimal.valueOf(-1).multiply(batchFileRecDO.getSuccessAmt()));
            mkmActivityDO.setRemainNum(batchFileRecDO.getSuccessNum()*(-1));
            iMkmActivityDao.updateRemainById(mkmActivityDO) ;
        }
    }

    @Override
    public void uploadFiletoRemote(Map<String, Object> ctx, String recNo) {
        String rsFileName = (String) ctx.get("rsFileName");
        String rsFilePath = (String) ctx.get("rsFilePath");
        /**
         * 上传文件
         *
         * @param filePath       上传文件路径
         * @param remoteIp       sftp服务器ip
         * @param remotePort     sftp服务器端口
         * @param connectTimeout 连接超时时间
         * @param remotePath     sftp服务器路径
         * @param name           sftp服务器帐号
         * @param pwd            sftp服务器密码
         * @throws Exception
         * */

        try {
            FileSftpUtils.upload(rsFilePath , remoteIp ,Integer.valueOf(remotePort) ,Integer.valueOf(timeout),
                    remotePath,username , password);
        } catch (Exception e) {
            logger.error("上传失败"+e.getMessage());
            throw new LemonException(MsgCd.UPLOAD_FILE_FAILURE.getMsgCd() ,MsgCd.UPLOAD_FILE_FAILURE.getMsgInfo());
        }
        //上传成功，更新回盘文件的路径

        BatchFileRecDO batchFileRecDO = new BatchFileRecDO();
        batchFileRecDO.setRecNo(recNo);
        batchFileRecDO.setResultFile(remotePath+ File.separator + rsFileName);
        batchFileRecDao.updateBatchRec(batchFileRecDO);
    }

    @Override
    public void deleteBatchFileInfo(String recNo) {
        batchFileInfoDao.deleteByRecNo(recNo);
    }

}
