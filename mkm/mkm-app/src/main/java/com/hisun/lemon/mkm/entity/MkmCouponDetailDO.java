/*
 * @ClassName MkmCouponDetailDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class MkmCouponDetailDO  extends BaseDO{
    /**
     * @Fields id 
     */
    private Integer id;
    /**
     * @Fields couponNo 抵用券编号
     */
    private String couponNo;
    /**
     * @Fields mkTool 营销工具类型
     */
    private String mkTool;
    /**
     * @Fields atvId 活动编号
     */
    private String atvId;
    /**
     * @Fields instId 商户id
     */
    private String instId;
    /**
     * @Fields userId 用户id
     */
    private String userId;
    /**
     * @Fields mobile 用户手机号
     */
    private String mobile;
    /**
     * @Fields amt 券别金额
     */
    private BigDecimal amt;
    /**
     * @Fields balance 券别余额
     */
    private BigDecimal balance;
    /**
     * @Fields releaseTm 发放时间
     */
    private LocalTime releaseTm;
    /**
     * @Fields releaseDt 发放日期
     */
    private LocalDate releaseDt;
    /**
     * @Fields couponValTm 券别有效日期
     */
    private LocalDateTime couponValTm;
    /**
     * @Fields couponInvalTm 券别失效日期
     */
    private LocalDateTime couponInvalTm;
    /**
     * @Fields status 状态 01-未使用 02-已使用 03-已过期 04-冻结 05-待激活
     */
    private String status;
    /**
     * @Fields orderNo 最后使用订单号
     */
    private String orderNo;

    /**
     * 订单金额
     */
    private BigDecimal orderAmt;

    /**
     * 折扣
     * @return
     */

    private BigDecimal discount;

    /**
     * 券别名称
     */
    private String couponName;

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCouponNo() {
        return couponNo;
    }

    public void setCouponNo(String couponNo) {
        this.couponNo = couponNo;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public LocalTime getReleaseTm() {
        return releaseTm;
    }

    public void setReleaseTm(LocalTime releaseTm) {
        this.releaseTm = releaseTm;
    }

    public LocalDate getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(LocalDate releaseDt) {
        this.releaseDt = releaseDt;
    }

    public LocalDateTime getCouponValTm() {
        return couponValTm;
    }

    public void setCouponValTm(LocalDateTime couponValTm) {
        this.couponValTm = couponValTm;
    }

    public LocalDateTime getCouponInvalTm() {
        return couponInvalTm;
    }

    public void setCouponInvalTm(LocalDateTime couponInvalTm) {
        this.couponInvalTm = couponInvalTm;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }
}