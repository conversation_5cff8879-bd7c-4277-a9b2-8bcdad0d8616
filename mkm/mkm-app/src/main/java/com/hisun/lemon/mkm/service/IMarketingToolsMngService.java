package com.hisun.lemon.mkm.service;

import com.hisun.lemon.mkm.entity.CouponUsageDetailsDO;
import com.hisun.lemon.mkm.entity.RequisitionDetailsDO;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/24
 */
public interface IMarketingToolsMngService {

    /**
     * 根据手机号码和领取时间查询营销工具领取列表信息
     *
     * @param mblNo     手机号码
     * @param releaseDt 领取时间
     * @return list
     */
    List<RequisitionDetailsDO> queryDetailsList(String mblNo, LocalDate releaseDt, int pageNum, int pageSize);

    /**
     * 根据手机号码和领取时间查询电子券使用明细列表信息
     *
     * @param mblNo     手机号码
     * @param releaseDt 领取时间
     * @return list
     */
    List<CouponUsageDetailsDO> queryUsageList(String mblNo, LocalDate releaseDt, int pageNum, int pageSize);

}
