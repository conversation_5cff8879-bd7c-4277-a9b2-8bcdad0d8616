/*
 * @ClassName MkmAtvRuleDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-10 11:14:45
 */
package com.hisun.lemon.mkm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MkmAtvRuleDO extends BaseDO {
    /**
     * @Fields atvId 活动编号
     */
    private String atvId;
    /**
     * @Fields crtUserOpr 操作员id
     */
    private String crtUserOpr;
    /**
     * @Fields receiveTimes 单用户领取次数
     */
    private Integer receiveTimes;
    /**
     * @Fields receiveCycle 领取次数的周期 day 天 month 月 year 年
     */
    private String receiveCycle;
    /**
     * @Fields userScope 用户范围
     */
    private String userScope;
    /**
     * @Fields startDays 领用后开始生效的天数
     */
    private Integer startDays;
    /**
     * @Fields couponValDays 券别有效天数
     */
    private Integer couponValDays;
    /**
     * @Fields couponInvalTm 券别最后失效时间
     */
    private LocalDateTime couponInvalTm;
    /**
     * @Fields minAmt 订单最大金额
     */
    private BigDecimal minAmt;
    /**
     * @Fields maxAmt 订单最小金额
     */
    private BigDecimal maxAmt;

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getCrtUserOpr() {
        return crtUserOpr;
    }

    public void setCrtUserOpr(String crtUserOpr) {
        this.crtUserOpr = crtUserOpr;
    }

    public Integer getReceiveTimes() {
        return receiveTimes;
    }

    public void setReceiveTimes(Integer receiveTimes) {
        this.receiveTimes = receiveTimes;
    }

    public String getReceiveCycle() {
        return receiveCycle;
    }

    public void setReceiveCycle(String receiveCycle) {
        this.receiveCycle = receiveCycle;
    }

    public String getUserScope() {
        return userScope;
    }

    public void setUserScope(String userScope) {
        this.userScope = userScope;
    }

    public Integer getStartDays() {
        return startDays;
    }

    public void setStartDays(Integer startDays) {
        this.startDays = startDays;
    }

    public Integer getCouponValDays() {
        return couponValDays;
    }

    public void setCouponValDays(Integer couponValDays) {
        this.couponValDays = couponValDays;
    }

    public LocalDateTime getCouponInvalTm() {
        return couponInvalTm;
    }

    public void setCouponInvalTm(LocalDateTime couponInvalTm) {
        this.couponInvalTm = couponInvalTm;
    }

    public BigDecimal getMinAmt() {
        return minAmt;
    }

    public void setMinAmt(BigDecimal minAmt) {
        this.minAmt = minAmt;
    }

    public BigDecimal getMaxAmt() {
        return maxAmt;
    }

    public void setMaxAmt(BigDecimal maxAmt) {
        this.maxAmt = maxAmt;
    }
}