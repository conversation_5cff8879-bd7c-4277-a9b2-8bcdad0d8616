package com.hisun.lemon.mkm.service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 批量文件操作Service
 *
 * <AUTHOR>
 * @create 2017/7/25
 */
public interface IBatchFileOperationService {

    /**
     * 从文件服务器上下载文件
     *
     * @param fileName
     * @return
     */
    String getBatchFile(String fileName) throws Exception;

    /**
     * 读取本地文件内容并插入到数据库中
     *
     * @param path
     * @param ctx
     */
    int insertBatchInfo(String path, Map<String, Object> ctx) throws IOException, Exception;

    /**
     * 解析数据
     *
     * @param recNo
     */
    void analyticalData(String recNo);

    void oprData(String oprTyp, String recNo ,LocalDateTime invalidTm ,Map<String,Object> ctx);

    void creatResultFile(String fileNm, String recNo, String oprTyp, Map<String, Object> ctx) throws Exception;

    void uploadFiletoRemote(Map<String, Object> ctx, String recNo);

    void deleteBatchFileInfo(String recNo);
}
