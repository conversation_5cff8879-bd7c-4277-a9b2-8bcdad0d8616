package com.hisun.lemon.mkm.controller;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.mkm.req.dto.FAQManagerReqDTO;
import com.hisun.lemon.mkm.res.dto.FAQManagerRspDTO;
import com.hisun.lemon.mkm.service.FAQManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 常见问题管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 20:40
 */
@Api(tags = "常见问题管理接口")
@RestController
@RequestMapping("/mkm/faq")
public class FAQManagerController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(FAQManagerController.class);
    
    @Resource
    private FAQManagerService faqManagerService;
    
    /**
     * 查询常见问题列表
     *
     * @param genericDTO 请求参数
     * @return 常见问题列表
     */
    @ApiOperation(value = "查询常见问题列表", notes = "根据条件查询常见问题列表")
    @ApiResponse(code = 200, message = "查询常见问题列表")
    @PostMapping("/queryFAQList")
    public GenericRspDTO<FAQManagerRspDTO> queryFAQList(@Validated @RequestBody GenericDTO<FAQManagerReqDTO> genericDTO, HttpServletRequest httpRequest) {
        logger.info("查询常见问题列表请求开始");
        FAQManagerReqDTO reqDTO = genericDTO.getBody();
        String acceptLanguage = httpRequest.getHeader("x-lemon-locale");
        logger.info("获取到的Accept-Language: {}", acceptLanguage);
        //截取前两个字符
        if (JudgeUtils.isNotNull(acceptLanguage)) {
            acceptLanguage = acceptLanguage.substring(0, 2);
            reqDTO.setLanguage(acceptLanguage);
        }
        // 设置默认分页参数
        if (JudgeUtils.isNull(reqDTO.getPageNum())) {
            reqDTO.setPageNum(0);
        }
        if (JudgeUtils.isNull(reqDTO.getPageSize())) {
            reqDTO.setPageSize(10);
        }
        
        // 调用服务查询
        FAQManagerRspDTO rspDTO = null;
        GenericRspDTO<FAQManagerRspDTO> genericRspDTO = null;

        rspDTO = faqManagerService.queryFAQList(reqDTO);
        genericRspDTO = GenericRspDTO.newSuccessInstance(rspDTO);
        
        logger.info("查询常见问题列表请求结束");
        return genericRspDTO;
    }
}