/*
 * @ClassName IMkmSeaCcyDetailDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.mkm.entity.MkmSeaCcyDetailDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IMkmSeaCcyDetailDao extends BaseDao<MkmSeaCcyDetailDO> {
    int updateBymkmSeaCcyDetailDO(MkmSeaCcyDetailDO mkmSeaCcyDetailDO);

    MkmSeaCcyDetailDO queryByMobile(String moile);
}