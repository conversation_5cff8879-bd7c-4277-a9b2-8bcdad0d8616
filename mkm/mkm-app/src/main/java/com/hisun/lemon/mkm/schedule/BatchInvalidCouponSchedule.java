package com.hisun.lemon.mkm.schedule;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import com.hisun.lemon.mkm.dao.IMkmActivityDao;
import com.hisun.lemon.mkm.dao.IMkmCouponDetailDao;
import com.hisun.lemon.mkm.entity.MkmActivityDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by chen on 10/19 0019.
 */
@Component
public class BatchInvalidCouponSchedule {

    private static final Logger logger = LoggerFactory.getLogger(BatchInvalidCouponSchedule.class);
    @Resource
    private IMkmCouponDetailDao iMkmCouponDetailDao;

    @Resource
    private IMkmActivityDao iMkmActivityDao;
    /**
     * 定时任务执行方法
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 0 0 * * ?")
    public void batchInvalidCoupon () {
        logger.debug("==================失效电子券批次执行开始：" + DateTimeUtils.getCurrentDateTimeStr());
        try{
            //查询根据活动编号分类失效卡券的总金额和数量
            List<MkmActivityDO> remainList = iMkmCouponDetailDao.getSumInvalidAmt();
            int i = iMkmCouponDetailDao.batchInvalidCoupon();

            //失效的券回填活动
            if (remainList != null ) {
                logger.debug("==================失效电子券回填活动" + DateTimeUtils.getCurrentDateTimeStr());
                for (MkmActivityDO mkmActivityDO : remainList) {
                    logger.debug("失效电子券回填活动：" + mkmActivityDO.getId() + "回填金额" +mkmActivityDO.getRemainAmt() + "回填金额" +mkmActivityDO.getRemainAmt());
                    mkmActivityDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                    iMkmActivityDao.updateRemainById(mkmActivityDO) ;
                }
            }
        }catch (LemonException e) {
            logger.debug("==================失效电子券批次执行异常：" + DateTimeUtils.getCurrentDateTimeStr() + e.getMessage());
        }
        logger.debug("==================失效电子券批次执行结束：" + DateTimeUtils.getCurrentDateTimeStr());
    }

}
