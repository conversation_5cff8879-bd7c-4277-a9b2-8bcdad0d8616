package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.mkm.entity.CouponUsageDetailsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户营销工具使用明细
 * 根据手机号和开始时间查询手机号，电子券使用详细信息
 *
 * <AUTHOR>
 * @create 2017/7/25
 */
@Mapper
public interface ICouponUsageDetailsDao {

    List<CouponUsageDetailsDO> selectUsageByMblNoAndBegDt(@Param("mblNo") String mblNo,
                                                          @Param("begDt") LocalDate begDt);
}
