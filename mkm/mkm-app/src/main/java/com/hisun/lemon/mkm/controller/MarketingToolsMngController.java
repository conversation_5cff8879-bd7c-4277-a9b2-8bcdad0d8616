package com.hisun.lemon.mkm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.annotation.LemonBody;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.mkm.common.MsgCd;
import com.hisun.lemon.mkm.entity.CouponUsageDetailsDO;
import com.hisun.lemon.mkm.entity.RequisitionDetailsDO;
import com.hisun.lemon.mkm.res.dto.CouponUsageDetailsResDTO;
import com.hisun.lemon.mkm.res.dto.RequisitionDetailsResDTO;
import com.hisun.lemon.mkm.service.IBatchFileOperationService;
import com.hisun.lemon.mkm.service.IMarketingToolsMngService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户营销工具管理
 * 用户营销工具领用明细查询和电子券使用明细查询，用户电子券管理（延期、过期、冻结、解冻）
 *
 * <AUTHOR>
 * @create 2017/7/24
 */
@RestController
@RequestMapping("/mkm/toolsmng")
public class MarketingToolsMngController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MarketingToolsMngController.class);

    @Value("${mkm.sftp.ip}")
    private String remoteIp;

    @Value("${mkm.sftp.port}")
    private String remotePort;

    @Value("${mkm.sftp.remotePath}")
    private String remotePath;

    @Value("${mkm.sftp.timeout}")
    private String timeout;

    @Value("${mkm.sftp.user}")
    private String username;

    @Value("${mkm.sftp.password}")
    private String password;

    @Value("${mkm.batchFileDir}")
    private String localPath;

    @Resource
    private IMarketingToolsMngService marketingToolsMngService;

    @Resource
    private IBatchFileOperationService batchFileOperationService;

    @Value("${mkm.batchFileDir}")
    private String batchFileDir;

    @ApiOperation(value = "用户营销工具领用明细功能", notes = "查询用户营销工具领用明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mblNo", value = "手机号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "begDt", value = "起始日期", required = true, dataType = "LocalDate"),
            @ApiImplicitParam(name = "pageNum", value = "页码数", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "页面展示数", required = false, dataType = "Integer"),
    })
    @ApiResponse(code = 200, message = "查询结果")
    @GetMapping("/requisition/{mblNo}")
    public GenericRspDTO<List<RequisitionDetailsResDTO>> queryToolRequisitionDetails(@PathVariable String mblNo,
                                                                                     @RequestParam LocalDate begDt,
                                                                                     @RequestParam(defaultValue = "0") int pageNum,
                                                                                     @RequestParam(defaultValue = "10") int pageSize,
                                                                                      GenericDTO<NoBody> genericreqDTO) {
        GenericRspDTO<List<RequisitionDetailsResDTO>> genericDTO = new GenericRspDTO<>();
        List<RequisitionDetailsDO> list = marketingToolsMngService.queryDetailsList(mblNo, begDt, pageNum, pageSize);
        if (JudgeUtils.isNull(list) || list.isEmpty()) {
            genericDTO.setMsgCd(MsgCd.USER_CONSUME_FAIL.getMsgCd());
            return genericDTO;
        }
        List<RequisitionDetailsResDTO> resDTOs = new ArrayList<>();
        for (RequisitionDetailsDO detailsDO : list) {
            RequisitionDetailsResDTO detailsResDTO = new RequisitionDetailsResDTO();
            BeanUtils.copyProperties(detailsResDTO, detailsDO);
            resDTOs.add(detailsResDTO);
        }
        genericDTO.setBody(resDTOs);
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        return genericDTO;
    }

    @ApiOperation(value = "用户营销工具消费明细", notes = "查询用户营销工具消费明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mblNo", value = "手机号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "begDt", value = "起始日期", required = true, dataType = "LocalDate"),
            @ApiImplicitParam(name = "pageNum", value = "页码数", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "页面展示数", required = false, dataType = "Integer"),
    })
    @ApiResponse(code = 200, message = "查询结果")
    @GetMapping("/usage/{mblNo}")
    public GenericRspDTO<List<CouponUsageDetailsResDTO>> queryToolUsageDetails(@PathVariable String mblNo,
                                                                            @RequestParam LocalDate begDt,
                                                                            @RequestParam(defaultValue = "0") int pageNum,
                                                                            GenericDTO<NoBody> genericreqDTO,
                                                                            @RequestParam(defaultValue = "5") int pageSize) {
        GenericRspDTO<List<CouponUsageDetailsResDTO>> genericDTO = new GenericRspDTO<>();
        List<CouponUsageDetailsDO> list = marketingToolsMngService.queryUsageList(mblNo, begDt, pageNum, pageSize);
        if (JudgeUtils.isNull(list) || list.isEmpty()) {
            genericDTO.setMsgCd(MsgCd.USER_CONSUME_FAIL.getMsgCd());
            return genericDTO;
        }
        List<CouponUsageDetailsResDTO> resDTOs = new ArrayList<>();
        for (CouponUsageDetailsDO detailsDO : list) {
            CouponUsageDetailsResDTO detailsResDTO = new CouponUsageDetailsResDTO();
            BeanUtils.copyProperties(detailsResDTO, detailsDO);
            resDTOs.add(detailsResDTO);
        }
        genericDTO.setBody(resDTOs);
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        return genericDTO;
    }

    @ApiOperation(value = "批量过期及延期，冻结及解冻", notes = "批量过期及延期，冻结及解冻")
    @PostMapping("/batch" )
    public GenericRspDTO<NoBody> batchChangeState(@LemonBody GenericDTO<NoBody> genericDTO,
                                                  @RequestParam String fileNm,
                                                  @RequestParam String oprTyp) {

        //文件名为fileNm = 活动号_日期_批次
        Map<String ,Object > ctx = new HashMap<String, Object > ();
        String recNo = "";
        try {
            //下载文件倒本地
            String path = batchFileOperationService.getBatchFile(fileNm);
            //数据插入到数据库
            int i = batchFileOperationService.insertBatchInfo(path ,ctx);
            //数据处理
            recNo = fileNm.substring(0,fileNm.lastIndexOf(".")).replaceAll("_" , "") ;
            batchFileOperationService.oprData(oprTyp,recNo,null ,ctx);
            //生成回盘信息文件
            batchFileOperationService.creatResultFile(fileNm,recNo,oprTyp,ctx);
            //上传到文件服务器
            batchFileOperationService.uploadFiletoRemote(ctx,recNo);
        } catch (LemonException e) {
            logger.error("File operation exception：" + e.getMessage());
            return GenericRspDTO.newInstance(e.getMsgCd(),e.getMsgInfo());
        }catch (Exception e) {
            logger.error("File operation exception：" + e.getMessage());
            return GenericRspDTO.newInstance(e.getMessage());
        } finally {
            //删除临时表数据
            if (JudgeUtils.isNotBlank(recNo)) {
                batchFileOperationService.deleteBatchFileInfo(recNo);
            }
        }
        return GenericRspDTO.newSuccessInstance();
    }

}
