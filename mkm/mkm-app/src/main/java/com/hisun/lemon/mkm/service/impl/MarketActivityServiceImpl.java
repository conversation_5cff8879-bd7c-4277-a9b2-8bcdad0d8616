package com.hisun.lemon.mkm.service.impl;

import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.mkm.common.Constants;
import com.hisun.lemon.mkm.common.MsgCd;
import com.hisun.lemon.mkm.dao.IIMkmAccountingSeqDao;
import com.hisun.lemon.mkm.dao.IMkmActivityDao;
import com.hisun.lemon.mkm.dao.IMkmAtvRuleDao;
import com.hisun.lemon.mkm.dao.IMkmInstDao;
import com.hisun.lemon.mkm.entity.MkmAccountingSeqDO;
import com.hisun.lemon.mkm.entity.MkmActivityDO;
import com.hisun.lemon.mkm.entity.MkmAtvRuleDO;
import com.hisun.lemon.mkm.entity.MkmInstDO;
import com.hisun.lemon.mkm.remote.service.RemoteService;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.service.MarketActivityService;
import com.hisun.lemon.mkm.res.dto.FindMarkeyActivityRspDTO;
import com.hisun.lemon.mkm.res.dto.MarketActivityDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by chen on 7/19 0019.
 */
@Transactional
@Service
public class MarketActivityServiceImpl extends BaseService implements MarketActivityService{
    private static final Logger logger = LoggerFactory.getLogger(MarketActivityServiceImpl.class);
    public static final String MAINTAIN_EXAMINE="01";
    public static final String MAINTAIN_STATUS="02";
    @Resource
    private IMkmActivityDao imarketActivityDao;
    @Resource
    private IMkmInstDao iMkmInstDao;


    @Resource
    private IIMkmAccountingSeqDao iiMkmAccountingSeqDao;

    @Resource
    private RemoteService remoteService;

    @Resource
    private IMkmAtvRuleDao iMkmAtvRuleDao;

    @Value("${mkm.activityItemS}")
    private String activityItemS ;
    @Value("${mkm.activityItemE}")
    private String activityItemE ;
    @Override
    public GenericRspDTO<String> insert(AddMarketActivityReqDTO addMarketActivityReqDTO) {
        GenericRspDTO addMarkeyActivityRspDTO = GenericRspDTO.newSuccessInstance();
        if (!Constants.MK_TOOL_SEA.equals(addMarketActivityReqDTO.getMkTool()) ) {
            HashMap<String,String> map = new HashMap<String,String>();
            map = check(addMarketActivityReqDTO);
            if (!Constants.SUCCESS.equals(map.get("msgCd"))) {
                addMarkeyActivityRspDTO.setMsgCd(map.get("msgCd"));
                addMarkeyActivityRspDTO.setMsgInfo(map.get("msgInfo"));
                return addMarkeyActivityRspDTO;
            }
        }
        MkmActivityDO marketActivityDO = copyToMarketActivityDO(addMarketActivityReqDTO);
        marketActivityDO.setAclt(0);
        marketActivityDO.setAcltAmt(BigDecimal.valueOf(0));
        if (marketActivityDO.getDiscount() != null){
            marketActivityDO.setDiscount(marketActivityDO.getDiscount().divide(BigDecimal.valueOf(10)));
        }
        MkmInstDO mkmInstDO=getMkmInst(marketActivityDO);
        if (!JudgeUtils.isBlank(addMarketActivityReqDTO.getInstId())) {
            marketActivityDO.setInstId("Y");
            String[] instArry = addMarketActivityReqDTO.getInstId().split("\\,");
            for (String inst : instArry) {
                mkmInstDO.setInstId(inst);
                int i = iMkmInstDao.insert(mkmInstDO);
                if(i != 1){
                    throw new LemonException(MsgCd.ADD_MKM_INST_FAIL.getMsgCd(),MsgCd.ADD_MKM_INST_FAIL.getMsgInfo());
                }
            }
        } else {
            marketActivityDO.setInstId("N");
        }
        int result = imarketActivityDao.insert(marketActivityDO);
        if(result != 1){
            throw new LemonException(MsgCd.ADD_MARKET_ACTIVITY_FAIL.getMsgCd(),MsgCd.ADD_MARKET_ACTIVITY_FAIL.getMsgInfo());
        }
        //添加营销规则
        MkmAtvRuleDO mkmAtvRuleDO = copyToMkmAtvRuleDO(addMarketActivityReqDTO ,marketActivityDO.getId());
        int ruleResult = iMkmAtvRuleDao.insert(mkmAtvRuleDO);
        if(ruleResult != 1){
            throw new LemonException(MsgCd.ADD_MARKET_ACTIVITY_FAIL.getMsgCd(),MsgCd.ADD_MARKET_ACTIVITY_FAIL.getMsgInfo());
        }
        addMarkeyActivityRspDTO.setBody(marketActivityDO.getId());
        return addMarkeyActivityRspDTO;
    }

    /**
     *登记账务接口
     * @param marketActivityDO
     * @param mkmAccountingSeqDO
     * @return
     */
    private GenericRspDTO traceAccounting(MkmActivityDO marketActivityDO, MkmAccountingSeqDO mkmAccountingSeqDO) {
        mkmAccountingSeqDO.setAcmSeq(IdGenUtils.generateId("MKM","mkm",25));
        mkmAccountingSeqDO.setAtvId(marketActivityDO.getId());
        mkmAccountingSeqDO.setAcmStatus("2");
        //TODO 该科目尚未确认
        // 贷方科目为平台的一个营销科目
        if (Constants.MK_TOOL_SEA.equals(marketActivityDO.getMkTool())) {
            mkmAccountingSeqDO.setItemC(activityItemS);
        } else {
            mkmAccountingSeqDO.setItemC(activityItemE);
        }
        mkmAccountingSeqDO.setAmt(marketActivityDO.getTotalAmt());
        mkmAccountingSeqDO.setTraceTm(DateTimeUtils.getCurrentLocalDateTime());
        mkmAccountingSeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmAccountingSeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmAccountingSeqDO.setType(Constants.ACC_TRACE_TYPE_M);
        int i = iiMkmAccountingSeqDao.insert(mkmAccountingSeqDO);
        if (i != 1) {
            throw new LemonException(MsgCd.ADD_ACOUNTING_SEQ_FAILED.getMsgCd(),MsgCd.ADD_ACOUNTING_SEQ_FAILED.getMsgInfo());
        }

        GenericRspDTO dot = remoteService.accountingTreatment(mkmAccountingSeqDO);
        MkmActivityDO upMkmActivityDO = new MkmActivityDO();
        upMkmActivityDO.setId(marketActivityDO.getId());
        if (Constants.ACM_SUCCESS.equals(dot.getMsgCd())) {
            upMkmActivityDO.setStatus(Constants.STATUS);
            mkmAccountingSeqDO.setAcmStatus(Constants.SEQ_SUCCESS);
        } else {
            upMkmActivityDO.setStatus(Constants.TRACE_FAIL);
            mkmAccountingSeqDO.setAcmStatus(Constants.SEQ_FAIL);
            mkmAccountingSeqDO.setMsgCd(dot.getMsgCd());
            mkmAccountingSeqDO.setMsgInfo(dot.getMsgInfo());
        }
        int up = imarketActivityDao.update(upMkmActivityDO);
        int  upSeq = iiMkmAccountingSeqDao.update(mkmAccountingSeqDO);
        return dot;
    }

    @Override
    public GenericRspDTO update(UpdateMarketActivityReqDTO updateMarketActivityReqDTO) {
        GenericRspDTO updateMarkeyActivityRspDTO = GenericRspDTO.newSuccessInstance();
        MkmActivityDO marketActivityDO = new MkmActivityDO();
        MkmAtvRuleDO mkmAtvRuleDO = new MkmAtvRuleDO();
        HashMap<String,String> map = new HashMap<String,String>();
        MkmActivityDO marketActivityDOOri = imarketActivityDao.get(updateMarketActivityReqDTO.getId());
        MkmAtvRuleDO mkmAtvRuleDOOri = iMkmAtvRuleDao.get(updateMarketActivityReqDTO.getId());
        BigDecimal increateAmt = BigDecimal.valueOf(0);
        //检查参数合法性
        map = checkUpdate(updateMarketActivityReqDTO,marketActivityDOOri ,mkmAtvRuleDOOri);
        if (!Constants.SUCCESS.equals(map.get("msgCd"))) {
            updateMarkeyActivityRspDTO.setMsgCd(map.get("msgCd"));
            updateMarkeyActivityRspDTO.setMsgInfo(map.get("msgInfo"));
            return updateMarkeyActivityRspDTO;
        }

        BeanUtils.copyProperties(marketActivityDO,updateMarketActivityReqDTO);
        BeanUtils.copyProperties(mkmAtvRuleDO,updateMarketActivityReqDTO);
        mkmAtvRuleDO.setAtvId(updateMarketActivityReqDTO.getId());
        if (marketActivityDO.getDiscount() != null){
            marketActivityDO.setDiscount(marketActivityDO.getDiscount().divide(BigDecimal.valueOf(10)));
        }
        if (JudgeUtils.isNotBlank(updateMarketActivityReqDTO.getEndTimeSr())) {
            marketActivityDO.setEndTime(DateTimeUtils.parseLocalDateTime(updateMarketActivityReqDTO.getEndTimeSr()));
        }
        marketActivityDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        if(updateMarketActivityReqDTO.getTotal() != null) {
            marketActivityDO.setRemainNum( (marketActivityDOOri.getRemainNum()+ updateMarketActivityReqDTO.getTotal() - marketActivityDOOri.getTotal()));
        }
        if (updateMarketActivityReqDTO.getTotalAmt() != null) {
            increateAmt = updateMarketActivityReqDTO.getTotalAmt().subtract(marketActivityDOOri.getTotalAmt());
            marketActivityDO.setRemainAmt(marketActivityDOOri.getRemainAmt().add(updateMarketActivityReqDTO.getTotalAmt().subtract(marketActivityDOOri.getTotalAmt())));
        }

        if (JudgeUtils.isNotBlank(updateMarketActivityReqDTO.getInstId())) {
            marketActivityDO.setInstId("Y");
        } else {
            marketActivityDO.setInstId("N");
        }
        int result = imarketActivityDao.update(marketActivityDO);
        if (result != 1) {
            updateMarkeyActivityRspDTO.setMsgCd(MsgCd.UPDATE_MARKET_ACTIVITY_FAIL.getMsgCd());
            updateMarkeyActivityRspDTO.setMsgInfo(MsgCd.UPDATE_MARKET_ACTIVITY_FAIL.getMsgInfo());
            return updateMarkeyActivityRspDTO;

        }
        int resultRule = iMkmAtvRuleDao.update(mkmAtvRuleDO);
        if (resultRule != 1) {
            throw new LemonException(MsgCd.UPDATE_MARKET_ACTIVITY_RULE_FAIL.getMsgCd(),MsgCd.UPDATE_MARKET_ACTIVITY_RULE_FAIL.getMsgInfo());
        }
        //如果有商户修改则需要修改商户信息

        if (!JudgeUtils.isBlank(updateMarketActivityReqDTO.getInstId())) {
            //获取活动原有商户的id
            List<MkmInstDO> instList = iMkmInstDao.getAllInstIdByActId(marketActivityDO.getId());
            MkmInstDO mkmInstDO = getMkmInst(marketActivityDO);
            String[] instArry = updateMarketActivityReqDTO.getInstId().split("\\,");
            //查询原来的商户如果不在修改后的商户中则需要删除原来的商户信息
            for (MkmInstDO instori:instList) {
                boolean flag=false;
                for (String inst : instArry) {
                    if (instori.getInstId().equals(inst)){
                        flag=true;
                        break;
                    }
                }
                if (flag == false){
                    int d=iMkmInstDao.delete(instori.getId());
                    if (d != 1) {
                        throw new LemonException(MsgCd.UPDATE_MKM_INST_FAIL.getMsgCd(),MsgCd.UPDATE_MKM_INST_FAIL.getMsgInfo());
                    }
                }
            }
            //修改原的记录和如果这次有新增的商户则新增
            for (String inst : instArry) {
                mkmInstDO.setInstId(inst);
                int i = iMkmInstDao.updateByActIdAndIsntId(mkmInstDO);
                if (i == -1) {
                    throw new LemonException(MsgCd.UPDATE_MKM_INST_FAIL.getMsgCd(),MsgCd.UPDATE_MKM_INST_FAIL.getMsgInfo());
                } else if ( i == 0 ) {
                    MkmInstDO addmkmInstDO = getMkmInst(marketActivityDOOri);
                    addmkmInstDO.setInstId(inst);
                    int j = iMkmInstDao.insert(addmkmInstDO);
                    if (j != 1) {
                        throw new LemonException(MsgCd.UPDATE_MKM_INST_FAIL.getMsgCd(),MsgCd.UPDATE_MKM_INST_FAIL.getMsgInfo());
                    }
                }
            }
        }
        /**
         * 需要掉账务接口记录新增的营销金额,审核通过的才能做账务登记.还有电子券做账
         */
        if (Constants.MK_TOOL_SEA.equals(marketActivityDOOri.getMkTool()) && JudgeUtils.isNotBlank(marketActivityDOOri.getItem())
                && Constants.EXAMINE_STATUS_YES.equals(marketActivityDOOri.getExamineStatus())) {
            if (updateMarketActivityReqDTO.getTotalAmt() != null) {
                //初始化账务登记信息到活动表中
                MkmAccountingSeqDO mkmAccountingSeqDO = new MkmAccountingSeqDO();
                mkmAccountingSeqDO.setItemC(updateMarketActivityReqDTO.getItem());

                /**
                 * 注意
                 * 计算修改后增加的金额
                 */
                marketActivityDO.setTotalAmt(increateAmt);

                /**
                 * 添加成后需要调用账务接口从商户的科目扣钱到营销科目
                 */
                try {
                    marketActivityDO.setMkTool(marketActivityDOOri.getMkTool());
                    GenericRspDTO genericDTO = traceAccounting(marketActivityDO, mkmAccountingSeqDO);
                    if (!Constants.ACM_SUCCESS.equals(genericDTO.getMsgCd())) {
                        updateMarkeyActivityRspDTO.setMsgCd(genericDTO.getMsgCd());
                        updateMarkeyActivityRspDTO.setMsgInfo(genericDTO.getMsgInfo());
                        throw new LemonException();
                    }
                } catch (Exception e) {
                    logger.error("异常"+e.getMessage());
                    //暂不做处理
                    updateMarkeyActivityRspDTO.setMsgCd(MsgCd.ACCOUNTING_EXCEPTION.getMsgCd());
                    updateMarkeyActivityRspDTO.setMsgInfo(MsgCd.ACCOUNTING_EXCEPTION.getMsgInfo());
                    throw new LemonException(MsgCd.ACCOUNTING_EXCEPTION.getMsgCd(),MsgCd.ACCOUNTING_EXCEPTION.getMsgInfo());
                }
            }
        }


        return updateMarkeyActivityRspDTO;
    }

    @Override
    public GenericRspDTO delete(DeleteMarketActivityReqDTO deleteMarketActivityDTO) {
        int result = imarketActivityDao.delete(deleteMarketActivityDTO.getId());
        GenericRspDTO deleteMarkeyActivityRspDTO = GenericRspDTO.newSuccessInstance();
        deleteMarkeyActivityRspDTO.setMsgCd(MsgCd.SUCCESS.getMsgInfo());
        if (result != 1) {
            deleteMarkeyActivityRspDTO.setMsgCd(MsgCd.DELETE_MARKET_ACTIVITY_FAIL.getMsgCd());
            deleteMarkeyActivityRspDTO.setMsgInfo(MsgCd.DELETE_MARKET_ACTIVITY_FAIL.getMsgInfo());

        }
        return deleteMarkeyActivityRspDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public GenericRspDTO<FindMarkeyActivityRspDTO> find(FindMarketActivityReqDTO findMarketActivityDTO) {
        FindMarkeyActivityRspDTO findMarkeyActivityRspDTO = new FindMarkeyActivityRspDTO();
        //查询理财产品信息
        List<MkmActivityDO> list = PageUtils.pageQuery(findMarketActivityDTO.getPageNo(), findMarketActivityDTO.getPageSize(), ()-> imarketActivityDao.findByfindMarketActivityDTO(findMarketActivityDTO));
        int total = imarketActivityDao.counTotal(findMarketActivityDTO);
        if (!(null != list && list.size() > 0)) {
            return GenericRspDTO.newSuccessInstance(findMarkeyActivityRspDTO);
        }
        List<MarketActivityDetail> marketActivityDetails = new ArrayList<>();
        for (MkmActivityDO marketActivityDO: list) {
            MarketActivityDetail marketActivityDetail = new MarketActivityDetail();
            List<MkmInstDO> mkmInstDOList = iMkmInstDao.getAllInstIdByActId(marketActivityDO.getId());
            if (mkmInstDOList != null){
                StringBuilder s = new StringBuilder("");
                int i = 0;
                for (MkmInstDO mkmInstDO : mkmInstDOList) {
                    s.append(mkmInstDO.getInstId());
                    if (i != mkmInstDOList.size() -1) {
                        s.append(",");
                    }
                    i++;
                }
                marketActivityDO.setInstId(s.toString());
            }

            BeanUtils.copyProperties(marketActivityDetail,marketActivityDO);
            marketActivityDetails.add(marketActivityDetail);
        }
        findMarkeyActivityRspDTO.setList(marketActivityDetails);
        findMarkeyActivityRspDTO.setPageNo(findMarketActivityDTO.getPageNo());
        findMarkeyActivityRspDTO.setPageSize(findMarketActivityDTO.getPageSize());
        findMarkeyActivityRspDTO.setTotal(total);
        GenericRspDTO<FindMarkeyActivityRspDTO> genericDTO = GenericRspDTO.newSuccessInstance(findMarkeyActivityRspDTO);
        genericDTO.setBody(findMarkeyActivityRspDTO);
        return genericDTO;
    }

    @Override
    public GenericRspDTO<NoBody> maintain(MaintainActivityReqDTO maintainActivityReqDTO) {
        GenericRspDTO<NoBody> genericDTO=GenericRspDTO.newSuccessInstance();
        MkmActivityDO oriMarketActivityDO = imarketActivityDao.get(maintainActivityReqDTO.getId());
        if (oriMarketActivityDO == null) {
            genericDTO.setMsgCd(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.GET_SEA_CCY_ACIVITY_FAIL.getMsgInfo());
            return genericDTO;
        }
        MkmActivityDO marketActivityDO=new MkmActivityDO();
        if (MAINTAIN_EXAMINE.equals(maintainActivityReqDTO.getType()) ) {
            if (JudgeUtils.isBlank(maintainActivityReqDTO.getExamineStatus())) {
                genericDTO.setMsgCd(MsgCd.UPDATE_MARKET_ACTIVITY_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.UPDATE_MARKET_ACTIVITY_FAIL.getMsgInfo());
                return genericDTO;
            }
            if (!Constants.STATUS.equals(oriMarketActivityDO.getStatus()) && !Constants.TRACE_FAIL.equals(oriMarketActivityDO.getStatus())) {
                genericDTO.setMsgCd(MsgCd.ACTIVITY_EXAMIN_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.ACTIVITY_EXAMIN_FAIL.getMsgInfo());
                return genericDTO;
            }
            if (Constants.EXAMINE_STATUS_YES.equals(oriMarketActivityDO.getExamineStatus())) {
                genericDTO.setMsgCd(MsgCd.REPEAT_EXAMIN.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.REPEAT_EXAMIN.getMsgInfo());
                return genericDTO;
            }
        }
        BeanUtils.copyProperties(marketActivityDO,maintainActivityReqDTO);
        marketActivityDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());

        //初始化账务登记信息到活动表中
        if (MAINTAIN_EXAMINE.equals(maintainActivityReqDTO.getType()) && Constants.EXAMINE_STATUS_YES.equals(maintainActivityReqDTO.getExamineStatus())) {
            MkmAccountingSeqDO mkmAccountingSeqDO = new MkmAccountingSeqDO();
            mkmAccountingSeqDO.setItemD(oriMarketActivityDO.getItem());

            /**
             * 审核成功后需要调用账务接口从商户的科目扣钱到营销科目.只有海币才需要做账.
             * 电子券和折扣券只有消费时收银台做账.从电子券xx-公司转到收银台
             * 海币账务
             * 借：应收账款-海币充值-XX公司       10000
             *贷：其他应付款-中转挂账-海币        10000
             */

            try {
                if (Constants.MK_TOOL_SEA.equals(oriMarketActivityDO.getMkTool()) && JudgeUtils.isNotBlank(oriMarketActivityDO.getItem())) {
                    GenericRspDTO genericDTOAcm = new GenericRspDTO();
                    genericDTOAcm = traceAccounting(oriMarketActivityDO, mkmAccountingSeqDO);
                    if (!"ACM00000".equals(genericDTOAcm.getMsgCd())) {
                        genericDTO.setMsgCd(genericDTOAcm.getMsgCd());
                        genericDTO.setMsgInfo(genericDTOAcm.getMsgInfo());
                        marketActivityDO.setStatus(Constants.TRACE_FAIL);
                        imarketActivityDao.update(marketActivityDO);
                        return genericDTO;
                    }

                }
            } catch (Exception e) {
                logger.error("账务登记异常" + e.getMessage());
                //暂不做处理
                genericDTO.setMsgCd(MsgCd.ACCOUNTING_EXCEPTION.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.ACCOUNTING_EXCEPTION.getMsgInfo());
                marketActivityDO.setStatus(Constants.TRACE_EXC);
                imarketActivityDao.update(marketActivityDO);
                return genericDTO;
            }
        }
        int result = imarketActivityDao.update(marketActivityDO);
        if (result != 1) {
            genericDTO.setMsgCd(MsgCd.UPDATE_MARKET_ACTIVITY_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.UPDATE_MARKET_ACTIVITY_FAIL.getMsgInfo());

        }
        return genericDTO;
    }
    private HashMap<String,String> checkUpdate(UpdateMarketActivityReqDTO updateMarketActivityReqDTO, MkmActivityDO marketActivityDO, MkmAtvRuleDO mkmAtvRuleDOOri) {
        HashMap<String,String> map = new HashMap<String,String>();
        map.put("msgCd",Constants.SUCCESS);

        if (marketActivityDO==null) {
            map.put("msgCd",MsgCd.CAN_NOT_FIND_MARKET_ACTIVITY.getMsgCd());
            map.put("msgInfo",MsgCd.CAN_NOT_FIND_MARKET_ACTIVITY.getMsgInfo());
            return map;
        }
        if (mkmAtvRuleDOOri == null ) {
            map.put("msgCd",MsgCd.CAN_NOT_FIND_MARKET_RULE.getMsgCd());
            map.put("msgInfo",MsgCd.CAN_NOT_FIND_MARKET_RULE.getMsgInfo());
            return map;
        }
        if (updateMarketActivityReqDTO.getTotal() != null ) {
            if (updateMarketActivityReqDTO.getTotal().compareTo(marketActivityDO.getTotal())<0) {
                map.put("msgCd", MsgCd.TOTAL_CHECK_FAIL.getMsgCd());
                map.put("msgInfo", MsgCd.TOTAL_CHECK_FAIL.getMsgInfo());
                return map;
            } else if (updateMarketActivityReqDTO.getTotal().compareTo(marketActivityDO.getTotal()) == 0){
                updateMarketActivityReqDTO.setTotal(null);
            }
        }
        if (updateMarketActivityReqDTO.getTotalAmt() != null  ) {
            if(updateMarketActivityReqDTO.getTotalAmt().compareTo(marketActivityDO.getTotalAmt()) < 0) {
                map.put("msgCd", MsgCd.TOTAL_AMOUNT_CHECK_FAIL.getMsgCd());
                map.put("msgInfo", MsgCd.TOTAL_AMOUNT_CHECK_FAIL.getMsgInfo());
                return map;
            } else if (updateMarketActivityReqDTO.getTotalAmt().compareTo(marketActivityDO.getTotalAmt()) == 0) {
                updateMarketActivityReqDTO.setTotalAmt(null);
            }
        }
        if (updateMarketActivityReqDTO.getReceiveTimes() != null && updateMarketActivityReqDTO.getReceiveTimes().compareTo(mkmAtvRuleDOOri.getReceiveTimes()) < 0) {
            map.put("msgCd",MsgCd.RELEASE_TIMES_CHECK_FAIL.getMsgCd());
            map.put("msgInfo",MsgCd.RELEASE_TIMES_CHECK_FAIL.getMsgInfo());
            return map;
        }
        return map;
    }
    private MkmInstDO getMkmInst(MkmActivityDO mkmActivityDO){
        MkmInstDO mkmInstDO = new MkmInstDO();
        mkmInstDO.setAtvId(mkmActivityDO.getId());
        mkmInstDO.setTotal(0);
        mkmInstDO.setTotalAmt(BigDecimal.valueOf(0));
        mkmInstDO.setTotIssAmt(BigDecimal.valueOf(0));
        mkmInstDO.setTotIssCnt(0);
        mkmInstDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmInstDO.setModifyTime(mkmInstDO.getCreateTime());
        return mkmInstDO;
    }


    HashMap<String,String> check(AddMarketActivityReqDTO addMarketActivityReqDTO){
        HashMap<String,String> map = new HashMap<String,String>();
        map.put("msgCd",Constants.SUCCESS);
//        if (JudgeUtils.isBlank(addMarketActivityReqDTO.getInstId())) {
//            map.put("msgCd", MsgCd.INST_ID_IS_NULL.getMsgCd());
//            map.put("msgInfo",MsgCd.INST_ID_IS_NULL.getMsgInfo());
//            return map;
//        }

        if (addMarketActivityReqDTO.getMinAmt() == null) {
            map.put("msgCd",MsgCd.MIN_AMT_IS_NULL.getMsgCd());
            map.put("msgInfo",MsgCd.MIN_AMT_IS_NULL.getMsgInfo());
            return map;
        }

        if (addMarketActivityReqDTO.getMaxAmt() == null) {
            map.put("msgCd",MsgCd.MAX_AMT_IS_NULL.getMsgCd());
            map.put("msgInfo",MsgCd.MAX_AMT_IS_NULL.getMsgInfo());
            return map;
        }

        if (addMarketActivityReqDTO.getStartDays() == null) {
            map.put("msgCd",MsgCd.CONPON_VAL_TM_IS_NULL.getMsgCd());
            map.put("msgInfo",MsgCd.CONPON_VAL_TM_IS_NULL.getMsgInfo());
            return map;
        }
        if (addMarketActivityReqDTO.getCouponValDays() == null) {
            map.put("msgCd",MsgCd.CONPON_VAL_TM_IS_NULL.getMsgCd());
            map.put("msgInfo",MsgCd.CONPON_VAL_TM_IS_NULL.getMsgInfo());
            return map;
        }

        if (addMarketActivityReqDTO.getCouponInvalTm() == null) {
            map.put("msgCd",MsgCd.CONPON_INVAL_TM_IS_NULL.getMsgCd());
            map.put("msgInfo",MsgCd.CONPON_INVAL_TM_IS_NULL.getMsgInfo());
            return map;
        }
        return map;
    }
    MkmAtvRuleDO copyToMkmAtvRuleDO(AddMarketActivityReqDTO addMarketActivityReqDTO , String atvId){
        MkmAtvRuleDO mkmAtvRuleDO = new MkmAtvRuleDO();
        if (JudgeUtils.isNotBlank(addMarketActivityReqDTO.getCouponInvalTm())) {
            mkmAtvRuleDO.setCouponInvalTm(DateTimeUtils.parseLocalDateTime(addMarketActivityReqDTO.getCouponInvalTm()));
        }
        mkmAtvRuleDO.setAtvId(atvId);
        if (addMarketActivityReqDTO.getCouponValDays() != null ) {
            mkmAtvRuleDO.setCouponValDays(addMarketActivityReqDTO.getCouponValDays());
        } else {
            mkmAtvRuleDO.setCouponValDays(99999);
        }
        mkmAtvRuleDO.setCrtUserOpr(addMarketActivityReqDTO.getCrtUserOpr());
        if (addMarketActivityReqDTO.getMinAmt() != null ) {
            mkmAtvRuleDO.setMaxAmt(addMarketActivityReqDTO.getMaxAmt());
        } else {
            mkmAtvRuleDO.setMaxAmt(new BigDecimal("9999999999999.99"));
        }
        if (addMarketActivityReqDTO.getMinAmt() != null ) {
            mkmAtvRuleDO.setMinAmt(addMarketActivityReqDTO.getMinAmt());
        } else {
            mkmAtvRuleDO.setMinAmt(BigDecimal.valueOf(0));
        }
        mkmAtvRuleDO.setReceiveCycle(addMarketActivityReqDTO.getReceiveCycle());
        mkmAtvRuleDO.setReceiveTimes(addMarketActivityReqDTO.getReceiveTimes());
        if (addMarketActivityReqDTO.getStartDays() != null) {
            mkmAtvRuleDO.setStartDays(addMarketActivityReqDTO.getStartDays());
        } else {
            mkmAtvRuleDO.setStartDays(0);
        }
        mkmAtvRuleDO.setUserScope(addMarketActivityReqDTO.getUserScope());
        mkmAtvRuleDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmAtvRuleDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        return mkmAtvRuleDO;
    }


    MkmActivityDO copyToMarketActivityDO(AddMarketActivityReqDTO addMarketActivityReqDTO){
        MkmActivityDO marketActivityDO = new MkmActivityDO();
        if (addMarketActivityReqDTO.getAmt() == null ) {
            //初始化
            marketActivityDO.setAmt(BigDecimal.valueOf(0));
        }else {
            marketActivityDO.setAmt(addMarketActivityReqDTO.getAmt());
        }
        marketActivityDO.setId(IdGenUtils.generateId("mk",10));
        marketActivityDO.setAtvNm(addMarketActivityReqDTO.getAtvNm());
        marketActivityDO.setBeginTime(DateTimeUtils.parseLocalDateTime(addMarketActivityReqDTO.getBeginTime()));

        marketActivityDO.setEndTime(DateTimeUtils.parseLocalDateTime(addMarketActivityReqDTO.getEndTime()));
        //marketActivityDO.setInstId(addMarketActivityReqDTO.getInstId());
        marketActivityDO.setMkTool(addMarketActivityReqDTO.getMkTool());
        marketActivityDO.setRemainAmt(addMarketActivityReqDTO.getTotalAmt());
        marketActivityDO.setRemainNum(addMarketActivityReqDTO.getTotal());
        marketActivityDO.setStatus(Constants.STATUS);
        marketActivityDO.setExamineStatus(Constants.EXAMINE_STATUS_NO);
        marketActivityDO.setTotal(addMarketActivityReqDTO.getTotal());
        marketActivityDO.setTotalAmt(addMarketActivityReqDTO.getTotalAmt());
        marketActivityDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        marketActivityDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        marketActivityDO.setDiscount(addMarketActivityReqDTO.getDiscount());
        marketActivityDO.setItem(addMarketActivityReqDTO.getItem());
        marketActivityDO.setCouponName(addMarketActivityReqDTO.getCouponName());
        marketActivityDO.setCostSide(addMarketActivityReqDTO.getCostSide());
        return marketActivityDO;
    }


}
