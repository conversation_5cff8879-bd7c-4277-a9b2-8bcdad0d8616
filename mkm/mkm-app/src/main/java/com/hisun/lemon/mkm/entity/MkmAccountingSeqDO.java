/*
 * @ClassName IMkmAccountingSeqDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 09:48:57
 */
package com.hisun.lemon.mkm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MkmAccountingSeqDO extends BaseDO {

    public MkmAccountingSeqDO(){
        super();
    };
    public MkmAccountingSeqDO(String acmSeq, String atvId, String itemD,
                              String itemC, String acmStatus, LocalDateTime traceTm,
                              BigDecimal amt, String msgCd, String msgInfo, String type , String remark ,String  capTyp ) {

        this.acmSeq = acmSeq;
        this.atvId = atvId;
        this.itemD = itemD;
        this.itemC = itemC;
        this.acmStatus = acmStatus;
        this.traceTm = traceTm;
        this.amt = amt;
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
        this.type = type;
        this.remark = remark;
        this.capTyp = capTyp;

    }

    /**
     * @Fields acmSeq 账务登记流水
     */
    private String acmSeq;
    /**
     * @Fields acmSeq 账务登记流水
     */
    private String atvId;
    /**
     * @Fields itemD 借方科目号
     */
    private String itemD;
    /**
     * @Fields itemC 借方科目号
     */
    private String itemC;
    /**
     * @Fields acmStatus 记账状态 1-成功 0-失败 2-异常
     */
    private String acmStatus;
    /**
     * @Fields traceTm 
     */
    private LocalDateTime traceTm;
    /**
     * @Fields 金额
     */
    private BigDecimal amt;
    /**
     * @Fields msgCd 记账错误码
     */
    private String msgCd;
    /**
     * @Fields msgInfo 记账错误信息
     */
    private String msgInfo;

    /**
     * 交易类型
     * @return
     */
    private String type ;

    private String remark;

   // @ApiModelProperty(name = "capTyp", value = "账户资金属性， 1：现金，8：待结算")

    private String capTyp;

    public void setType(String type) {
        this.type = type;
    }

    public String getAcmSeq() {
        return acmSeq;
    }

    public void setAcmSeq(String acmSeq) {
        this.acmSeq = acmSeq;
    }

    public String getItemD() {
        return itemD;
    }

    public void setItemD(String itemD) {
        this.itemD = itemD;
    }

    public String getItemC() {
        return itemC;
    }

    public void setItemC(String itemC) {
        this.itemC = itemC;
    }

    public String getAcmStatus() {
        return acmStatus;
    }

    public void setAcmStatus(String acmStatus) {
        this.acmStatus = acmStatus;
    }

    public LocalDateTime getTraceTm() {
        return traceTm;
    }

    public void setTraceTm(LocalDateTime traceTm) {
        this.traceTm = traceTm;
    }

    public String getMsgCd() {
        return msgCd;
    }

    public void setMsgCd(String msgCd) {
        this.msgCd = msgCd;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getType() {
        return type;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}