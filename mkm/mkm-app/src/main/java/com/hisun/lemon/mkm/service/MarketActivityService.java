package com.hisun.lemon.mkm.service;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.res.dto.FindMarkeyActivityRspDTO;

/**
 * Created by chen on 7/19 0019.
 */
public interface MarketActivityService {
    /**
     * 新增营销活动
     * @param addMarketActivityReqDTO
     * @return
     */
    GenericRspDTO<String> insert(AddMarketActivityReqDTO addMarketActivityReqDTO);

    /**
     * 修改营销活动
     * @param updateMarketActivityReqDTO
     * @return
     */
    GenericRspDTO update(UpdateMarketActivityReqDTO updateMarketActivityReqDTO);

    /**
     *删除营销活动
     * @param deleteMarketActivityDTO
     * @return
     */
    GenericRspDTO delete(DeleteMarketActivityReqDTO deleteMarketActivityDTO);

    /**
     * 查询营销活动
     * @param findMarketActivityDTO
     * @return
     */
    GenericRspDTO<FindMarkeyActivityRspDTO> find(FindMarketActivityReqDTO findMarketActivityDTO);

    /**
     * 维护营销活动
     * @param maintainActivityReqDTO
     * @return
     */
    GenericRspDTO<NoBody> maintain(MaintainActivityReqDTO maintainActivityReqDTO);
}
