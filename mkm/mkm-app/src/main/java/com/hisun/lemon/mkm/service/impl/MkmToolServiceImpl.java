package com.hisun.lemon.mkm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.mkm.common.Constants;
import com.hisun.lemon.mkm.common.MsgCd;
import com.hisun.lemon.mkm.controller.MkmToolController;
import com.hisun.lemon.mkm.dao.*;
import com.hisun.lemon.mkm.entity.*;
import com.hisun.lemon.mkm.remote.service.RemoteService;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.req.innerDto.QueryUserSeaCcyReqDTO;
import com.hisun.lemon.mkm.res.dto.*;
import com.hisun.lemon.mkm.res.innerDto.QueryUserSeaCcyResDTO;
import com.hisun.lemon.mkm.service.MkmToolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.awt.*;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by chen on 7/22 0022.
 */
@Service
public class MkmToolServiceImpl extends BaseService implements MkmToolService{
    private static final Logger logger = LoggerFactory.getLogger(MkmToolController.class);

    @Resource
    private IMkmSeaCcySeqDao iMkmSeaCcySeqDao;

    @Resource
    private IMkmSeaCcyDetailDao iMkmSeaCcyDetailDao;

    @Resource
    private IMkmActivityDao iMkmActivityDao;

    @Resource
    private IMkmCouponSeqDao iMkmCouponSeqDao;

    @Resource
    private IMkmAtvRuleDao iMkmAtvRuleDao;

    @Resource
    private IMkmCouponDetailDao iMkmCouponDetailDao;

    @Resource
    private IMkmInstDao iMkmInstDao;

    @Resource
    private MkmToolServiceTransaction mkmToolServiceTransaction;

    @Resource
    private RemoteService remoteService;


    /**
     * 海币充值和发放
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<RechargeMkmToolResDTO> recharge(GenericDTO<RechargeMkmToolReqDTO> req) {
        RechargeMkmToolResDTO body =  new RechargeMkmToolResDTO();
        RechargeMkmToolReqDTO rechargeMkmToolReqDTO = req.getBody();
        GenericRspDTO<RechargeMkmToolResDTO> rechargeMkmToolResDTO = GenericRspDTO.newSuccessInstance(body);
        //登记流水
        if (Constants.MK_TOOL_SEA.equals(rechargeMkmToolReqDTO.getMkTool())) {
            MkmSeaCcySeqDO mkmSeaCcySeqDO = new MkmSeaCcySeqDO();
            BeanUtils.copyProperties(mkmSeaCcySeqDO,rechargeMkmToolReqDTO);
            if (JudgeUtils.isNotBlank(req.getMsgId())) {
                mkmSeaCcySeqDO.setSeq(req.getMsgId());
            }
            mkmSeaCcySeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcySeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcySeqDO.setStatus(Constants.SEQ_EXCEPTION);
            LocalDateTime dateTime = rechargeMkmToolReqDTO.getRechargeTm();
            mkmSeaCcySeqDO.setReleaseDt(dateTime.toLocalDate());
            mkmSeaCcySeqDO.setReleaseTm(dateTime.toLocalTime());
            mkmSeaCcySeqDO.setOrderNo(rechargeMkmToolReqDTO.getSeq());
            if (JudgeUtils.isNotBlank(req.getChannel())) {
                mkmSeaCcySeqDO.setChannel(req.getChannel());
            } else {
                if (JudgeUtils.isNotBlank(req.getMsgId()) && req.getMsgId().length()>4)
                    mkmSeaCcySeqDO.setChannel(req.getMsgId().substring(0,3));
            }
            int insertSeq = iMkmSeaCcySeqDao.insert(mkmSeaCcySeqDO);
            if (insertSeq != 1) {
                logger.info("failed" + MsgCd.INSERT_SEA_CCY_SEQ_FAIL.getMsgInfo());
                rechargeMkmToolResDTO.setMsgCd( MsgCd.INSERT_SEA_CCY_SEQ_FAIL. getMsgCd());
                rechargeMkmToolResDTO.setMsgInfo(MsgCd.INSERT_SEA_CCY_SEQ_FAIL.getMsgInfo());
                return  rechargeMkmToolResDTO;
            }

            //检查用户是否开户
            if (Constants.SEACCY_SEA_TRACE_TYPE_G.equals(rechargeMkmToolReqDTO.getType())) {
                String mobile = remoteService.queryUser(rechargeMkmToolReqDTO.getUserId(), rechargeMkmToolResDTO);
                if (!Constants.MKM_SUCCESS.equals(rechargeMkmToolResDTO.getMsgCd())) {
                    logger.info(rechargeMkmToolResDTO.getRequestId() + "failed" + rechargeMkmToolResDTO.getMsgCd() + ":" + rechargeMkmToolResDTO.getMsgInfo());
                    return updateSeq(mkmSeaCcySeqDO, rechargeMkmToolResDTO, rechargeMkmToolResDTO.getMsgCd(), rechargeMkmToolResDTO.getMsgInfo(), Constants.SEQ_FAIL);

                } else {
                    if (JudgeUtils.isBlank(rechargeMkmToolReqDTO.getMobile())) {
                        mkmSeaCcySeqDO.setMobile(mobile);
                    }
                }
            }

            //海币发放或者充值事务处理
            mkmToolServiceTransaction.rechargeAndReleasse(rechargeMkmToolReqDTO,rechargeMkmToolResDTO,mkmSeaCcySeqDO);
            if ( !Constants.MKM_SUCCESS.equals(rechargeMkmToolResDTO.getMsgCd())) {
                logger.info(rechargeMkmToolResDTO.getRequestId() + "failed" + rechargeMkmToolResDTO.getMsgCd() + ":" + rechargeMkmToolResDTO.getMsgInfo());
                return  updateSeq(mkmSeaCcySeqDO, rechargeMkmToolResDTO,rechargeMkmToolResDTO.getMsgCd(), rechargeMkmToolResDTO.getMsgInfo(), Constants.SEQ_FAIL);
            }

        }

        //返回结果
        body.setResult(Constants.SEQ_SUCCESS);
        return rechargeMkmToolResDTO;
    }


    /**
     * 用户获取电子券
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<GetConponResDTO> getConpon(GenericDTO<GetConponReqDTO> req) {
        GetConponReqDTO getConponReqDTO = req.getBody();
        GetConponResDTO getConponResDTO = new GetConponResDTO();
        GenericRspDTO<GetConponResDTO> genericDTO = GenericRspDTO.newSuccessInstance(getConponResDTO);
        //发券方重,通过订单号和状态

        MkmCouponSeqDO mkmCouponSeqDOReapt = iMkmCouponSeqDao.oriSeq(getConponReqDTO.getOrderNo() , Constants.SEQ_TYPE_RELEASE);
        if (mkmCouponSeqDOReapt != null && Constants.SEQ_SUCCESS.equals(mkmCouponSeqDOReapt.getStatus())) {
            genericDTO.setMsgCd(MsgCd.ONLY_ORDER.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.ONLY_ORDER.getMsgInfo());
            return genericDTO;
        }
        //登记流水
        MkmCouponSeqDO mkmCouponSeqDO = new MkmCouponSeqDO();
        BeanUtils.copyProperties(mkmCouponSeqDO,getConponReqDTO);
        mkmCouponSeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmCouponSeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmCouponSeqDO.setStatus(Constants.SEQ_FAIL);
        LocalDateTime dateTime = DateTimeUtils.getCurrentLocalDateTime();//getConponReqDTO.getRechargeTm();
        mkmCouponSeqDO.setReleaseDt(dateTime.toLocalDate());
        mkmCouponSeqDO.setReleaseTm(dateTime.toLocalTime());
        mkmCouponSeqDO.setAmt(getConponReqDTO.getAmt());
        if (JudgeUtils.isNotBlank(req.getChannel())) {
            mkmCouponSeqDO.setChannel(req.getChannel());
        } else {
            if (JudgeUtils.isNotBlank(req.getMsgId()) && req.getMsgId().length()>4)
                mkmCouponSeqDO.setChannel(req.getMsgId().substring(0,3));
        }
        int seqResult = iMkmCouponSeqDao.insert(mkmCouponSeqDO);
        if (seqResult != 1) {
            genericDTO.setMsgCd(MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgInfo());
            return genericDTO;
        }
        //风控检查
        GenericRspDTO riskRes = remoteService.riskCheck(getConponReqDTO);
        if (!Constants.RSM_SUCCESS.equals(riskRes.getMsgCd())) {
            logger.info("风控检查失败{}:{}",riskRes.getMsgCd(),riskRes.getMsgInfo());
            return updateConponSeq( mkmCouponSeqDO,genericDTO, riskRes.getMsgCd(),riskRes.getMsgInfo(), Constants.SEQ_FAIL);
        }
        //检查用户是否开户
        String mobile = remoteService.queryUser(getConponReqDTO.getUserId() ,genericDTO);

        if (JudgeUtils.isBlank(getConponReqDTO.getMobile())) {
            mkmCouponSeqDO.setMobile(mobile);
        }


        //插入流水后判断用户开户结果
        if (!Constants.MKM_SUCCESS.equals(genericDTO.getMsgCd())) {
            logger.info(genericDTO.getRequestId() + "failed" + genericDTO.getMsgCd() + ":" + genericDTO.getMsgInfo());
            return updateConponSeq( mkmCouponSeqDO,genericDTO, genericDTO.getMsgCd(),genericDTO.getMsgInfo(), Constants.SEQ_FAIL);

        }

        //开启电子发放事务处理
        mkmToolServiceTransaction.getConpouTransaction(getConponReqDTO, genericDTO, mkmCouponSeqDO);
        if ( !Constants.MKM_SUCCESS.equals(genericDTO.getMsgCd())) {
            logger.info(genericDTO.getRequestId() + "failed" + genericDTO.getMsgCd() + ":" + genericDTO.getMsgInfo());
            return  updateConponSeq(mkmCouponSeqDO, genericDTO,genericDTO.getMsgCd(), genericDTO.getMsgInfo(), Constants.SEQ_FAIL);
        }

        //更新结果
        genericDTO.getBody().setResult(Constants.SEQ_SUCCESS);
        return genericDTO;
    }

    /**
     * 海币消费
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<ConsumeSeaCcyResDTO> consumeSeaCyy(GenericDTO<ConsumeSeaCcyReqDTO> req) {
        ConsumeSeaCcyReqDTO consumeSeaCcyReqDTO = req.getBody();
        ConsumeSeaCcyResDTO body = new ConsumeSeaCcyResDTO();
        GenericRspDTO<ConsumeSeaCcyResDTO> genericDTO = GenericRspDTO.newSuccessInstance(body);

        //登记消费流水
        if (Constants.MK_TOOL_SEA.equals(consumeSeaCcyReqDTO.getMkTool())) {
            MkmSeaCcySeqDO mkmSeaCcySeqDO = new MkmSeaCcySeqDO();
            BeanUtils.copyProperties(mkmSeaCcySeqDO,consumeSeaCcyReqDTO);
            //查询手机号
            MkmSeaCcyDetailDO getMobile = iMkmSeaCcyDetailDao.get(consumeSeaCcyReqDTO.getUserId());
            if (getMobile != null){
                mkmSeaCcySeqDO.setMobile(getMobile.getMobile());
            }
            mkmSeaCcySeqDO.setSeq(req.getMsgId());
            mkmSeaCcySeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcySeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcySeqDO.setStatus(Constants.SEQ_EXCEPTION);
            LocalDateTime dateTime = consumeSeaCcyReqDTO.getConsumeTm();
            mkmSeaCcySeqDO.setReleaseDt(dateTime.toLocalDate());
            mkmSeaCcySeqDO.setReleaseTm(dateTime.toLocalTime());
            mkmSeaCcySeqDO.setValRefund(consumeSeaCcyReqDTO.getCount());
            if (JudgeUtils.isNotBlank(req.getChannel())) {
                mkmSeaCcySeqDO.setChannel(req.getChannel());
            } else {
                if (JudgeUtils.isNotBlank(req.getMsgId()) && req.getMsgId().length()>4)
                    mkmSeaCcySeqDO.setChannel(req.getMsgId().substring(0,3));
            }
            //防重
            MkmSeaCcySeqDO oriMkmSeaCcySeqDO = iMkmSeaCcySeqDao.getValRefund(consumeSeaCcyReqDTO.getOrderNo(),consumeSeaCcyReqDTO.getCount() ,Constants.SEQ_TYPE_CONSUME) ;
            if (oriMkmSeaCcySeqDO != null){
                if (!( Constants.SEQ_FAIL.equals(oriMkmSeaCcySeqDO.getStatus()) || Constants.SEQ_REVOKED.equals(oriMkmSeaCcySeqDO.getStatus()))) {
                    genericDTO.setMsgInfo(MsgCd.ORDER_CONSUME_REPEAT.getMsgInfo());
                    genericDTO.setMsgCd(MsgCd.ORDER_CONSUME_REPEAT.getMsgCd());
                    return genericDTO;
                }
            }

            //插入流水号
            int insertSeq = iMkmSeaCcySeqDao.insert(mkmSeaCcySeqDO);
            if (insertSeq != 1) {
                return updateSeq( mkmSeaCcySeqDO,genericDTO, MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgCd(),MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgInfo(), Constants.SEQ_FAIL);
            }

            //判断风控是否检查通过
            GenericRspDTO riskResDto = remoteService.riskTimeCheck(null,null,consumeSeaCcyReqDTO);
            if (!Constants.RSM_SUCCESS.equals(riskResDto.getMsgCd())) {
                logger.error("风控检查不通过{}:{}",riskResDto.getMsgCd(),riskResDto.getMsgInfo());
                return updateSeq( mkmSeaCcySeqDO,genericDTO,riskResDto.getMsgCd(),riskResDto.getMsgInfo(), Constants.SEQ_FAIL);
            }

            //扣减用户海币
            MkmSeaCcyDetailDO mkmSeaCcyDetailDO = new MkmSeaCcyDetailDO();
            mkmSeaCcyDetailDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcyDetailDO.setCount(consumeSeaCcyReqDTO.getCount());
            mkmSeaCcyDetailDO.setOrderNo(consumeSeaCcyReqDTO.getOrderNo());
            mkmSeaCcyDetailDO.setUserId(consumeSeaCcyReqDTO.getUserId());
            mkmSeaCcyDetailDO.setMkTool(consumeSeaCcyReqDTO.getMkTool());
            int consumeResult = iMkmSeaCcyDetailDao.updateBymkmSeaCcyDetailDO(mkmSeaCcyDetailDO) ;
            if (consumeResult != 1) {
                logger.info("更新海币余额失败");
                return updateSeq( mkmSeaCcySeqDO,genericDTO, MsgCd.CONSUME_SEA_CYY_FAIL.getMsgCd(),MsgCd.CONSUME_SEA_CYY_FAIL.getMsgInfo(), Constants.SEQ_FAIL);
            }

            //更新流水状态为成功
            mkmSeaCcySeqDO.setStatus(Constants.SEQ_SUCCESS);
            iMkmSeaCcySeqDao.update(mkmSeaCcySeqDO);
        }
        genericDTO.getBody().setResult(Constants.SEQ_SUCCESS);
        return genericDTO;
    }

    /**
     * 券类消费
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<ConsumeCouponResDTO> consumeCoupon(GenericDTO<ConsumeCouponReqDTO> req) {
        ConsumeCouponReqDTO consumeCouponReqDTO = req.getBody();
        ConsumeCouponResDTO body = new ConsumeCouponResDTO();
        GenericRspDTO<ConsumeCouponResDTO> genericDTO = GenericRspDTO.newSuccessInstance(body);
        //检查该电子券时候可以可用
        MkmCouponDetailDO mkmCouponDetailDO  = iMkmCouponDetailDao.getByCouponId(consumeCouponReqDTO.getCouponNo());
        if (mkmCouponDetailDO == null) {
            genericDTO.setMsgCd( MsgCd.COUPON_NOT_EXITE.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.COUPON_NOT_EXITE.getMsgInfo());
            return genericDTO;
        } else {
            //检查电子权是否已经失效
            LocalDateTime now = DateTimeUtils.getCurrentLocalDateTime();
            Duration period = Duration.between(now, mkmCouponDetailDO.getCouponInvalTm());
            Duration period2 = Duration.between(now, mkmCouponDetailDO.getCouponValTm());
            if (period.getSeconds()<0 || period2.getSeconds()>0) {
                genericDTO.setMsgCd( MsgCd.COUPON_AlREADY_VALIDATION.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.COUPON_AlREADY_VALIDATION.getMsgInfo());
                return genericDTO;
            }

            //如果是手工获取的折扣券。则全面金额是0的。需要在消费的时候根据订单金额做金额做补全
            if (mkmCouponDetailDO.getAmt().compareTo(BigDecimal.valueOf(0)) <= 0 && mkmCouponDetailDO.getDiscount() != null) {
                mkmCouponDetailDO.setAmt((BigDecimal.valueOf(1).subtract(mkmCouponDetailDO.getDiscount()).multiply(consumeCouponReqDTO.getOrderAmt())));
            }
        }

        //登记交易流水
        MkmCouponSeqDO mkmCouponSeqDO = new MkmCouponSeqDO();
        BeanUtils.copyProperties(mkmCouponSeqDO,consumeCouponReqDTO);
        mkmCouponSeqDO.setSeq(req.getMsgId());
        mkmCouponSeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmCouponSeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmCouponSeqDO.setStatus(Constants.SEQ_EXCEPTION);
        LocalDateTime dateTime = consumeCouponReqDTO.getConsumeTm();
        mkmCouponSeqDO.setReleaseDt(dateTime.toLocalDate());
        mkmCouponSeqDO.setReleaseTm(dateTime.toLocalTime());
        mkmCouponSeqDO.setMobile(mkmCouponDetailDO.getMobile());
        mkmCouponSeqDO.setAtvId(mkmCouponDetailDO.getAtvId());
        mkmCouponSeqDO.setAmt(mkmCouponDetailDO.getAmt());
        if (JudgeUtils.isNotBlank(req.getChannel())) {
            mkmCouponSeqDO.setChannel(req.getChannel());
        } else {
            if (JudgeUtils.isNotBlank(req.getMsgId()) && req.getMsgId().length()>4)
                mkmCouponSeqDO.setChannel(req.getMsgId().substring(0,3));
        }
        //防重
        MkmCouponSeqDO oriMkmCouponSeqDo = iMkmCouponSeqDao.oriSeq(consumeCouponReqDTO.getOrderNo(),Constants.SEQ_TYPE_CONSUME);
        if (oriMkmCouponSeqDo != null){
            if (!(Constants.SEQ_FAIL.equals(oriMkmCouponSeqDo.getStatus()) || Constants.SEQ_REVOKED.equals(oriMkmCouponSeqDo.getStatus()))) {
                genericDTO.setMsgInfo(MsgCd.ORDER_CONSUME_REPEAT.getMsgInfo());
                genericDTO.setMsgCd(MsgCd.ORDER_CONSUME_REPEAT.getMsgCd());
                return genericDTO;
            }
        }
        int seqResult = iMkmCouponSeqDao.insert(mkmCouponSeqDO);
        if (seqResult != 1) {
            return updateConponSeq( mkmCouponSeqDO,genericDTO, MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgCd(),MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgInfo(), Constants.SEQ_FAIL);
        }

        //风控检查
        GenericRspDTO riskResDto = remoteService.riskTimeCheck(consumeCouponReqDTO,mkmCouponDetailDO,null);
        if (!Constants.RSM_SUCCESS.equals(riskResDto.getMsgCd())) {
            logger.info("风控检查失败{}:{}",riskResDto.getMsgCd(),riskResDto.getMsgInfo());
            return updateConponSeq( mkmCouponSeqDO,genericDTO, riskResDto.getMsgCd(),riskResDto.getMsgInfo(), Constants.SEQ_FAIL);
        }

        //检查订单是否可以使用优惠
        GenericRspDTO check = checkConsume(mkmCouponDetailDO,consumeCouponReqDTO,genericDTO);
        if (!MsgCd.SUCCESS.getMsgCd().equals(check.getMsgCd())) {
            return updateConponSeq( mkmCouponSeqDO,genericDTO, check.getMsgCd(), check.getMsgInfo(), Constants.SEQ_FAIL);
        }


        //开启事务
        mkmToolServiceTransaction.consumeCouponTransaction( consumeCouponReqDTO, genericDTO, mkmCouponSeqDO, mkmCouponDetailDO);
        if ( !Constants.MKM_SUCCESS.equals(genericDTO.getMsgCd())) {
            logger.info(genericDTO.getRequestId() + "failed" + genericDTO.getMsgCd() + ":" + genericDTO.getMsgInfo());
            return  updateConponSeq(mkmCouponSeqDO, genericDTO, genericDTO.getMsgCd(), genericDTO.getMsgInfo(), Constants.SEQ_FAIL);
        }

        //返回结果
        genericDTO.getBody().setResult(Constants.SEQ_SUCCESS);
        return genericDTO;
    }

    /**
     * 消费撤销以及退款接口
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<RevokedConsumeCouponResDTO> revokedConsume(GenericDTO<RevokedConsumeCouponReqDTO> req) {
        RevokedConsumeCouponResDTO body = new RevokedConsumeCouponResDTO();
        GenericRspDTO<RevokedConsumeCouponResDTO> genericDTO = GenericRspDTO.newSuccessInstance(body);
        RevokedConsumeCouponReqDTO revokedConsumeCouponReqDTO = req.getBody();

        //海币消费撤销
        if (Constants.MK_TOOL_SEA.equals(revokedConsumeCouponReqDTO.getMkTool())) {
            if (revokedConsumeCouponReqDTO.getCount() == null){
                genericDTO.setMsgCd( MsgCd.COUNT_IS_NULL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.COUNT_IS_NULL.getMsgInfo());
                return genericDTO;
            }

            //海币撤销
            String type = Constants.SEQ_TYPE_CONSUME;
            String traceType = revokedConsumeCouponReqDTO.getType();
            if (Constants.SEQ_TYPE_REFUND.equals(traceType) || Constants.SEQ_TYPE_REVOKED.equals(traceType)) {
                type = Constants.SEQ_TYPE_CONSUME;
            }
            if (Constants.SEQ_TYPE_REFUND_REVOKE.equals(traceType)) {
                type = Constants.SEQ_TYPE_REFUND_REVOKE;
            }
            MkmSeaCcySeqDO oriMkmSeaCcySeqDO = iMkmSeaCcySeqDao.getValRefund(revokedConsumeCouponReqDTO.getOriSeq(),revokedConsumeCouponReqDTO.getCount() ,type) ;
            if (oriMkmSeaCcySeqDO == null || oriMkmSeaCcySeqDO.getValRefund().compareTo(revokedConsumeCouponReqDTO.getCount()) < 0 ) {
                logger.info("查询海币交易流水失败");
                genericDTO.setMsgCd( MsgCd.GET_ORISEQ_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.GET_ORISEQ_FAIL.getMsgInfo());
                return genericDTO;
            } else {
                if ((revokedConsumeCouponReqDTO.getCount().compareTo(oriMkmSeaCcySeqDO.getCount()) > 0)) {
                    logger.info("交易海币数量多于原消费海币数量");
                    genericDTO.setMsgCd( MsgCd.REVOKE_COUNT_MORE_BANK_CONSUME.getMsgCd());
                    genericDTO.setMsgInfo(MsgCd.REVOKE_COUNT_MORE_BANK_CONSUME.getMsgInfo());
                    return genericDTO;
                }
            }
            //登记撤销消费流水
            MkmSeaCcySeqDO mkmSeaCcySeqDO = new MkmSeaCcySeqDO();
            BeanUtils.copyProperties(mkmSeaCcySeqDO, oriMkmSeaCcySeqDO);
            //退款支持部分退款
            if (Constants.SEQ_TYPE_REFUND.equals(revokedConsumeCouponReqDTO.getType())) {
                mkmSeaCcySeqDO.setCount(revokedConsumeCouponReqDTO.getCount());
                mkmSeaCcySeqDO.setType(Constants.SEQ_TYPE_REFUND);
            } else if (Constants.SEQ_TYPE_REVOKED.equals(revokedConsumeCouponReqDTO.getType())){
                //撤销为整单撤销
                mkmSeaCcySeqDO.setCount(oriMkmSeaCcySeqDO.getCount());
                mkmSeaCcySeqDO.setType(Constants.SEQ_TYPE_REVOKED);
            } else {
                //退款撤销
                mkmSeaCcySeqDO.setCount(oriMkmSeaCcySeqDO.getCount());
                mkmSeaCcySeqDO.setType(Constants.SEQ_TYPE_REFUND_REVOKE);
            }
            mkmSeaCcySeqDO.setOriSeq(oriMkmSeaCcySeqDO.getSeq());
            mkmSeaCcySeqDO.setOriTime(oriMkmSeaCcySeqDO.getCreateTime());
            mkmSeaCcySeqDO.setStatus(Constants.SEQ_EXCEPTION);
            mkmSeaCcySeqDO.setSeq(revokedConsumeCouponReqDTO.getSeq());
            mkmSeaCcySeqDO.setOrderNo(revokedConsumeCouponReqDTO.getSeq());
            mkmSeaCcySeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcySeqDO.setValRefund(mkmSeaCcySeqDO.getCount());
            mkmSeaCcySeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmSeaCcySeqDO.setReleaseDt(DateTimeUtils.getCurrentLocalDate());
            mkmSeaCcySeqDO.setReleaseTm(DateTimeUtils.getCurrentLocalTime());
            if (JudgeUtils.isNotBlank(req.getChannel())) {
                mkmSeaCcySeqDO.setChannel(req.getChannel());
            } else {
                if (JudgeUtils.isNotBlank(req.getMsgId()) && req.getMsgId().length()>4)
                    mkmSeaCcySeqDO.setChannel(req.getMsgId().substring(0,3));
            }
            int insertSeq = iMkmSeaCcySeqDao.insert(mkmSeaCcySeqDO);
            if (insertSeq != 1) {
                genericDTO.setMsgCd(MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgInfo());
                return genericDTO;
                //return updateSeq( mkmSeaCcySeqDO,genericDTO, MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgCd(),MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgInfo(), Constants.SEQ_FAIL);
            }

            //退款开启事务
            try {
                mkmToolServiceTransaction.revokeSeaCyyTransaction(oriMkmSeaCcySeqDO, revokedConsumeCouponReqDTO, mkmSeaCcySeqDO, genericDTO,oriMkmSeaCcySeqDO.getSeq());
            } catch (LemonException l) {
                logger.error("异常"+l.getMsgCd());
                genericDTO.setMsgCd(l.getMsgCd());
                genericDTO.setMsgInfo(l.getMsgInfo());
            }
            if ( !Constants.MKM_SUCCESS.equals(genericDTO.getMsgCd())) {
                logger.info(genericDTO.getRequestId() + "failed" + genericDTO.getMsgCd() + ":" + genericDTO.getMsgInfo());
                return  updateSeq(mkmSeaCcySeqDO, genericDTO, genericDTO.getMsgCd(), genericDTO.getMsgInfo(), Constants.SEQ_FAIL);
            }
            mkmSeaCcySeqDO.setStatus(Constants.SEQ_SUCCESS);
            iMkmSeaCcySeqDao.update(mkmSeaCcySeqDO);

        } else {
            String type = Constants.SEQ_TYPE_CONSUME;
            String traceType = revokedConsumeCouponReqDTO.getType();
            if (Constants.SEQ_TYPE_REFUND.equals(traceType) || Constants.SEQ_TYPE_REVOKED.equals(traceType)) {
                type = Constants.SEQ_TYPE_CONSUME;
            }
            if (Constants.SEQ_TYPE_REFUND_REVOKE.equals(traceType)) {
                type = Constants.SEQ_TYPE_REFUND_REVOKE;
            }
            //电子券消费撤销
            MkmCouponSeqDO oriMkmCouponSeqDo = iMkmCouponSeqDao.oriSeq(revokedConsumeCouponReqDTO.getOriSeq(),type);
            if (!(oriMkmCouponSeqDo != null && (Constants.SEQ_SUCCESS.equals(oriMkmCouponSeqDo.getStatus()) || Constants.SEQ_REFUND_PART.equals(oriMkmCouponSeqDo.getStatus())))) {
                logger.info("查询原电子券消费交易失败");
                genericDTO.setMsgCd( MsgCd.GET_ORISEQ_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.GET_ORISEQ_FAIL.getMsgInfo());
                return genericDTO;
                //              return updateConponSeq( oriMkmCouponSeqDo,genericDTO, MsgCd.GET_ORISEQ_FAIL.getMsgCd(),MsgCd.GET_ORISEQ_FAIL.getMsgInfo(), Constants.SEQ_FAIL);
            }

            //如果为撤销，则交易为整个订单撤销
            if (Constants.SEQ_TYPE_REVOKED.equals(traceType) || Constants.SEQ_TYPE_REFUND_REVOKE.equals(traceType)) {
                revokedConsumeCouponReqDTO.setAmt(oriMkmCouponSeqDo.getAmt());
            }

            //新增交易撤销流水
            MkmCouponSeqDO mkmCouponSeqDO = new MkmCouponSeqDO();
            BeanUtils.copyProperties(mkmCouponSeqDO, oriMkmCouponSeqDo);
            mkmCouponSeqDO.setOriSeq(oriMkmCouponSeqDo.getSeq());
            mkmCouponSeqDO.setOriTime(oriMkmCouponSeqDo.getCreateTime());
            mkmCouponSeqDO.setStatus(Constants.SEQ_EXCEPTION);
            mkmCouponSeqDO.setSeq(revokedConsumeCouponReqDTO.getSeq());
            mkmCouponSeqDO.setOrderNo(revokedConsumeCouponReqDTO.getSeq());
            mkmCouponSeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            mkmCouponSeqDO.setReleaseDt(DateTimeUtils.getCurrentLocalDate());
            mkmCouponSeqDO.setReleaseTm(DateTimeUtils.getCurrentLocalTime());
            mkmCouponSeqDO.setAmt(revokedConsumeCouponReqDTO.getAmt());
            mkmCouponSeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
            if (JudgeUtils.isNotBlank(req.getChannel())) {
                mkmCouponSeqDO.setChannel(req.getChannel());
            } else {
                if (JudgeUtils.isNotBlank(req.getMsgId()) && req.getMsgId().length()>4)
                    mkmCouponSeqDO.setChannel(req.getMsgId().substring(0,3));
            }

            //设置流水类型
            mkmCouponSeqDO.setType(revokedConsumeCouponReqDTO.getType());

            int insertRs = iMkmCouponSeqDao.insert(mkmCouponSeqDO);
            if (insertRs != 1) {
                genericDTO.setMsgCd(MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgInfo());
                return genericDTO ;
                //return updateConponSeq( oriMkmCouponSeqDo,genericDTO, MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgCd(),MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgInfo(), Constants.SEQ_FAIL);
            }

            //开启电子券消费撤销事务
            try {
                mkmToolServiceTransaction.revokeEleConsumeTransaction( revokedConsumeCouponReqDTO, genericDTO, mkmCouponSeqDO ,oriMkmCouponSeqDo);
            } catch (LemonException e) {
                logger.error(e.getMsgInfo());
                return updateConponSeq( mkmCouponSeqDO,genericDTO, e.getMsgCd(),e.getMsgInfo(), Constants.SEQ_FAIL);

            }
            if ( !Constants.MKM_SUCCESS.equals(genericDTO.getMsgCd())) {
                logger.info(genericDTO.getRequestId() + "failed" + genericDTO.getMsgCd() + ":" + genericDTO.getMsgInfo());
                return  updateConponSeq(mkmCouponSeqDO, genericDTO, genericDTO.getMsgCd(), genericDTO.getMsgInfo(), Constants.SEQ_FAIL);
            }
        }
        genericDTO.getBody().setResult(Constants.SEQ_SUCCESS);
        return genericDTO;
    }

    /**
     * 查询活动
     * @param findMarketActivityDTO
     * @return
     */
    @Override
    public GenericRspDTO<FindMarkeyActivityRspDTO> qurey(FindMarketActivityReqDTO findMarketActivityDTO) {

        FindMarkeyActivityRspDTO findMarkeyActivityRspDTO = new FindMarkeyActivityRspDTO();
        List<MarketActivityDetail>  list = PageUtils.pageQuery(findMarketActivityDTO.getPageNo(), findMarketActivityDTO.getPageSize(), ()-> iMkmActivityDao.queryByFindMarketActivityDTO(findMarketActivityDTO));
        int total = iMkmActivityDao.queryByFindMarketActivityDTOCont(findMarketActivityDTO);
        if (null == list || list.size() <= 0) {
            list = new ArrayList<MarketActivityDetail>() ;
            findMarkeyActivityRspDTO.setList(list);
            return GenericRspDTO.newSuccessInstance(findMarkeyActivityRspDTO);
        }
        if (JudgeUtils.isNotBlank(findMarketActivityDTO.getUserId())) {
            for (MarketActivityDetail marketActivityDetail : list) {
                String receiveCycle = marketActivityDetail.getReceiveCycle();
                if (Constants.MK_TOOL_SEA.equals(marketActivityDetail.getMkTool())) {
                    int count = iMkmSeaCcySeqDao.checkTimes(marketActivityDetail.getId(), findMarketActivityDTO.getUserId(),
                            marketActivityDetail.getReceiveTimes(), receiveCycle);
                    if (count == 0) {
                        marketActivityDetail.setGetFlag("0");
                    } else {
                        marketActivityDetail.setGetFlag(count + "");
                    }
                } else {
                    int count = iMkmCouponSeqDao.checkTimes(marketActivityDetail.getId(), findMarketActivityDTO.getUserId(),
                            marketActivityDetail.getReceiveTimes(), receiveCycle);
                    if (count == 0) {
                        marketActivityDetail.setGetFlag("0");
                    } else {
                        marketActivityDetail.setGetFlag(count + "");
                    }
                }
            }
        }
        findMarkeyActivityRspDTO.setList(list);

        findMarkeyActivityRspDTO.setTotal(total);
        GenericRspDTO<FindMarkeyActivityRspDTO> genericDTO = GenericRspDTO.newSuccessInstance(findMarkeyActivityRspDTO);
        genericDTO.setBody(findMarkeyActivityRspDTO);
        return genericDTO;
    }

    /**
     * 查询用户可用的海币和券
     * @param queryUserMkmToolReqDTO
     * @return
     */
    @Override
    public GenericRspDTO<QueryUserMkmToolRspDTO> queryUserMkmTool(QueryUserMkmToolReqDTO queryUserMkmToolReqDTO) {
        QueryUserMkmToolRspDTO queryUserMkmToolRspDTO = new QueryUserMkmToolRspDTO();
        GenericRspDTO<QueryUserMkmToolRspDTO> genericDTO = GenericRspDTO.newSuccessInstance(queryUserMkmToolRspDTO);
        List<CouponDetail> listCoupon = new ArrayList<CouponDetail>();
        if (!Constants.MK_TOOL_SEA.equals(queryUserMkmToolReqDTO.getMkTool()) || JudgeUtils.isBlank(queryUserMkmToolReqDTO.getMkTool())) {
            LocalDateTime now = DateTimeUtils.getCurrentLocalDateTime().plusMinutes(1) ;
            listCoupon = PageUtils.pageQuery(queryUserMkmToolReqDTO.getPageNo(), queryUserMkmToolReqDTO.getPageSize(), ()-> iMkmCouponDetailDao.queryUserCoupon(queryUserMkmToolReqDTO ,now) );
            int total = iMkmCouponDetailDao.queryUserCouponCount(queryUserMkmToolReqDTO ,now);
            if (listCoupon!=null && listCoupon.size()>0) {
//                for (MkmCouponDetailDO detailDO: list){
//                    CouponDetail couponDetail = new CouponDetail();
//                    BeanUtils.copyProperties(couponDetail, detailDO);
//                    listCoupon.add(couponDetail);
//                }

                queryUserMkmToolRspDTO.setTotal(total);
                queryUserMkmToolRspDTO.setPage(queryUserMkmToolReqDTO.getPageNo());
                queryUserMkmToolRspDTO.setPageSize(queryUserMkmToolReqDTO.getPageSize());
            }

        }
        queryUserMkmToolRspDTO.setCouponDetail(listCoupon);

        if (Constants.MK_TOOL_SEA.equals(queryUserMkmToolReqDTO.getMkTool()) || JudgeUtils.isBlank(queryUserMkmToolReqDTO.getMkTool())) {
            MkmSeaCcyDetailDO mkmSeaCcyDetailDO = iMkmSeaCcyDetailDao.get(queryUserMkmToolReqDTO.getUserId());
            if (mkmSeaCcyDetailDO != null) {
                SeaCcyDetail seaCcyDetail = new SeaCcyDetail();
                BeanUtils.copyProperties(seaCcyDetail, mkmSeaCcyDetailDO);
                queryUserMkmToolRspDTO.setSeaCcyDetal(seaCcyDetail);
            }
        }
        return genericDTO;
    }

    /**
     * 查询最优的活动
     * @param autoRealeaseReqDTO
     * @return
     */
    @Override
    public MkmActivityDO queryOptimalActivity(AutoRealeaseReqDTO autoRealeaseReqDTO) {
        if (JudgeUtils.isNotBlank(autoRealeaseReqDTO.getMkTool())) {
            MkmActivityDO mkmActivityDO = iMkmActivityDao.queryOptimalActivity(autoRealeaseReqDTO);
            return mkmActivityDO;
        } else {
            autoRealeaseReqDTO.setMkTool("04");
            MkmActivityDO mkmActivityDO = iMkmActivityDao.queryOptimalActivity(autoRealeaseReqDTO);
            autoRealeaseReqDTO.setMkTool(null);
            MkmActivityDO mkmActivityDO2 = iMkmActivityDao.queryOptimalActivity(autoRealeaseReqDTO);
            if (autoRealeaseReqDTO.getOrderAmt() != null ) {
                if (mkmActivityDO != null && mkmActivityDO2 !=null) {
                    BigDecimal discount = mkmActivityDO.getDiscount().multiply(autoRealeaseReqDTO.getOrderAmt());
                    if (discount.compareTo(mkmActivityDO2.getAmt()) < 0) {
                        mkmActivityDO = mkmActivityDO2;
                    }
                } else if (mkmActivityDO2 !=null) {
                    mkmActivityDO = mkmActivityDO2;
                }

            }

            return mkmActivityDO;
        }


    }


    /**
     * 发放撤销
     * @param realeaseRevokeReqDTO
     * @return
     */
    @Override
    public GenericRspDTO<RealeaseRevokeRspDTO> realeaseRevoke(RealeaseRevokeReqDTO realeaseRevokeReqDTO) {
        RealeaseRevokeRspDTO body = new RealeaseRevokeRspDTO();
        GenericRspDTO<RealeaseRevokeRspDTO> genericDTO = GenericRspDTO.newSuccessInstance(body);
        //券别撤销
        MkmCouponSeqDO oriMkmCouponSeqDo = iMkmCouponSeqDao.oriSeq(realeaseRevokeReqDTO.getOriSeq(),"01");
        if (!(oriMkmCouponSeqDo != null && Constants.SEQ_TYPE_RELEASE.equals(oriMkmCouponSeqDo.getType()) && Constants.SEQ_SUCCESS.equals(oriMkmCouponSeqDo.getStatus()))) {
            genericDTO.setMsgCd( MsgCd.GET_ORISEQ_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.GET_ORISEQ_FAIL.getMsgInfo());
            return genericDTO;
        }
        //新增交易撤销流水
        MkmCouponSeqDO mkmCouponSeqDO = new MkmCouponSeqDO();
        BeanUtils.copyProperties(mkmCouponSeqDO, oriMkmCouponSeqDo);
        mkmCouponSeqDO.setOriSeq(oriMkmCouponSeqDo.getSeq());
        mkmCouponSeqDO.setOriTime(oriMkmCouponSeqDo.getCreateTime());
        mkmCouponSeqDO.setStatus(Constants.SEQ_EXCEPTION);
        mkmCouponSeqDO.setSeq(realeaseRevokeReqDTO.getSeq());
        mkmCouponSeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmCouponSeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmCouponSeqDO.setType(Constants.SEQ_TYPE_REVOKED_REALEASE);
        int insertRs = iMkmCouponSeqDao.insert(mkmCouponSeqDO);
        if (insertRs != 1) {
            return updateConponSeq( oriMkmCouponSeqDo,genericDTO, MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgCd(),MsgCd.INSERT_CONPON_SEQ_FAIL.getMsgInfo(), Constants.SEQ_FAIL);
        }
        try {
            mkmToolServiceTransaction.revokeReleaseTransaction( realeaseRevokeReqDTO, genericDTO, mkmCouponSeqDO);
        } catch (LemonException e) {
            logger.error(e.getMsgInfo());
            return updateConponSeq( mkmCouponSeqDO,genericDTO, e.getMsgCd(),e.getMsgInfo(), Constants.SEQ_FAIL);

        }
        if ( !Constants.MKM_SUCCESS.equals(genericDTO.getMsgCd())) {
            logger.info(genericDTO.getRequestId() + "failed" + genericDTO.getMsgCd() + ":" + genericDTO.getMsgInfo());
            return  updateConponSeq(mkmCouponSeqDO, genericDTO, genericDTO.getMsgCd(), genericDTO.getMsgInfo(), Constants.SEQ_FAIL);
        }
        return genericDTO;
    }

    /**
     * 海币转赠
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<SeaccyGitfRspDTO> seaccyGitf(GenericDTO<SeaccyGitfRepDTO> req) {
        SeaccyGitfRspDTO body = new SeaccyGitfRspDTO();
        GenericRspDTO<SeaccyGitfRspDTO> genericDTO = GenericRspDTO.newSuccessInstance(body);
        SeaccyGitfRepDTO seaccyGitfRepDTO = req.getBody();
        //登记交易流水
        MkmSeaCcySeqDO mkmSeaCcySeqDO = new MkmSeaCcySeqDO();
        mkmSeaCcySeqDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmSeaCcySeqDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        mkmSeaCcySeqDO.setStatus(Constants.SEQ_EXCEPTION);
        LocalDateTime dateTime = seaccyGitfRepDTO.getTraceTime();
        mkmSeaCcySeqDO.setReleaseDt(dateTime.toLocalDate());
        mkmSeaCcySeqDO.setReleaseTm(dateTime.toLocalTime());
        mkmSeaCcySeqDO.setUserId(seaccyGitfRepDTO.getUserId());
        mkmSeaCcySeqDO.setMobile(seaccyGitfRepDTO.getMobile());
        mkmSeaCcySeqDO.setGmobile(seaccyGitfRepDTO.getgMobile());
        mkmSeaCcySeqDO.setGuserId(seaccyGitfRepDTO.getGitfUser());
        mkmSeaCcySeqDO.setSeq(seaccyGitfRepDTO.getSeq());
        mkmSeaCcySeqDO.setCount(seaccyGitfRepDTO.getCount());
        mkmSeaCcySeqDO.setOrderNo(seaccyGitfRepDTO.getSeq());
        mkmSeaCcySeqDO.setMkTool(Constants.MK_TOOL_SEA);
        mkmSeaCcySeqDO.setType(Constants.SEQ_TYPE_GITF);
        mkmSeaCcySeqDO.setValRefund(seaccyGitfRepDTO.getCount());
        if (JudgeUtils.isNotBlank(req.getChannel())) {
            mkmSeaCcySeqDO.setChannel(req.getChannel());
        } else {
            if (JudgeUtils.isNotBlank(req.getMsgId()) && req.getMsgId().length()>4)
                mkmSeaCcySeqDO.setChannel(req.getMsgId().substring(0,3));
        }
        int insertSeq = iMkmSeaCcySeqDao.insert(mkmSeaCcySeqDO);
        if (insertSeq != 1) {
            genericDTO.setMsgCd(MsgCd.INSERT_SEA_CCY_SEQ_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.INSERT_SEA_CCY_SEQ_FAIL.getMsgInfo());
            return genericDTO;
        }

        //开启海币转赠事务
        try {
            mkmToolServiceTransaction.seaccyGitfTransaction( req, genericDTO, mkmSeaCcySeqDO);
        } catch (LemonException e) {
            logger.error(e.getMsgInfo());
            return updateSeq( mkmSeaCcySeqDO,genericDTO, e.getMsgCd(),e.getMsgInfo(), Constants.SEQ_FAIL);

        }
        if ( !Constants.MKM_SUCCESS.equals(genericDTO.getMsgCd())) {
            logger.info(genericDTO.getRequestId() + "failed" + genericDTO.getMsgCd() + ":" + genericDTO.getMsgInfo());
            return  updateSeq(mkmSeaCcySeqDO, genericDTO, genericDTO.getMsgCd(), genericDTO.getMsgInfo(), Constants.SEQ_FAIL);
        }
        mkmSeaCcySeqDO.setStatus(Constants.SEQ_SUCCESS);
        iMkmSeaCcySeqDao.update(mkmSeaCcySeqDO);
        return genericDTO;
    }

    /**
     * 海币交易查询
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<SeaccyTraceDetailRspDTO> seaccyTraceDetail(GenericDTO<SeaccyTraceDetailRepDTO> req) {
        GenericRspDTO<SeaccyTraceDetailRspDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new SeaccyTraceDetailRspDTO());
        SeaccyTraceDetailRepDTO body = req.getBody();
        List<MkmSeaCcySeqDO> list =PageUtils.pageQuery(body.getPageNo(), body.getPageSize(), ()-> iMkmSeaCcySeqDao.seaccyTraceDetail(body));
        List<SeaccyTraceDetail> seaccyTraceDetailList = new ArrayList<SeaccyTraceDetail>();
        genericRspDTO.getBody().setList(seaccyTraceDetailList);
        for (MkmSeaCcySeqDO seqdo : list) {
            SeaccyTraceDetail seaccyTraceDetail = new SeaccyTraceDetail();
            BeanUtils.copyProperties( seaccyTraceDetail ,seqdo);
            seaccyTraceDetailList.add(seaccyTraceDetail);
        }
        return genericRspDTO;
    }

    /**
     * 内部接口查询用户海币剩余额度
     * @param body
     * @return
     */
    @Override
    public GenericRspDTO<QueryUserSeaCcyResDTO> queryUserSeaccy(QueryUserSeaCcyReqDTO body) {
        GenericRspDTO<QueryUserSeaCcyResDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new QueryUserSeaCcyResDTO());
        String userId = remoteService.queryUserByLoginId(body.getMobile(),genericRspDTO);
        if (JudgeUtils.isBlank(userId) ) {
            genericRspDTO.setMsgCd(MsgCd.QUERY_USER_EXP.getMsgCd());
            genericRspDTO.setMsgInfo(MsgCd.QUERY_USER_EXP.getMsgInfo());
            return genericRspDTO;
        }
        MkmSeaCcyDetailDO mkmSeaCcyDetailDO = iMkmSeaCcyDetailDao.get(userId);
        if (mkmSeaCcyDetailDO != null) {
            BeanUtils.copyProperties(genericRspDTO.getBody(), mkmSeaCcyDetailDO);
        }
        return genericRspDTO;
    }

    @Override
    public MkmActivityDO getMkmActivity(String atvId) {
        return iMkmActivityDao.get(atvId);
    }


    /**
     * 检查消费参数合法性
     * @param mkmCouponDetailDO
     * @param consumeCouponReqDTO
     * @param genericDTO
     * @return
     */
    private GenericRspDTO checkConsume(MkmCouponDetailDO mkmCouponDetailDO, ConsumeCouponReqDTO consumeCouponReqDTO, GenericRspDTO genericDTO) {
        if (mkmCouponDetailDO == null) {
            genericDTO.setMsgCd( MsgCd.COUPON_NOT_EXITE.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.COUPON_NOT_EXITE.getMsgInfo());
            return genericDTO;
        }
        if (JudgeUtils.isNotBlank(mkmCouponDetailDO.getInstId())) {
            if (!mkmCouponDetailDO.getInstId().equals(consumeCouponReqDTO.getInstId())) {
                genericDTO.setMsgCd(MsgCd.INST_NOT_CONSUME.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.INST_NOT_CONSUME.getMsgInfo());
                return genericDTO;
            }
        }
        if (!mkmCouponDetailDO.getUserId().equals(consumeCouponReqDTO.getUserId())) {
            genericDTO.setMsgCd( MsgCd.USER_CONSUME_FAIL.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.USER_CONSUME_FAIL.getMsgInfo());
            return genericDTO;
        }
        if (JudgeUtils.isNotBlank(mkmCouponDetailDO.getOrderNo())) {
            if (!mkmCouponDetailDO.getOrderNo().equals(consumeCouponReqDTO.getOrderNo())) {
                genericDTO.setMsgCd( MsgCd.NOT_EQUELS_ORDERNO.getMsgCd());
                genericDTO.setMsgInfo(MsgCd.NOT_EQUELS_ORDERNO.getMsgInfo());
                return genericDTO;
            }
        }
        MkmAtvRuleDO mkmAtvRuleDO = iMkmAtvRuleDao.get(mkmCouponDetailDO.getAtvId());
        if (mkmAtvRuleDO.getMinAmt() !=null && consumeCouponReqDTO.getOrderAmt().compareTo(mkmAtvRuleDO.getMinAmt()) == -1) {
            genericDTO.setMsgCd( MsgCd.ORDER_AMT_SCOPR.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.ORDER_AMT_SCOPR.getMsgInfo());
            return genericDTO;
        }
        if (mkmAtvRuleDO.getMaxAmt() !=null && consumeCouponReqDTO.getOrderAmt().compareTo(mkmAtvRuleDO.getMaxAmt()) == 1) {
            genericDTO.setMsgCd( MsgCd.ORDER_AMT_SCOPR.getMsgCd());
            genericDTO.setMsgInfo(MsgCd.ORDER_AMT_SCOPR.getMsgInfo());
            return genericDTO;
        }

        return genericDTO;
    }


    /**
     * 更新海币流水状态
     * @param mkmSeaCcySeqDO
     * @param rechargeMkmToolResDTO
     * @param msgCd
     * @param msgInfo
     * @param status
     * @return
     */
    private  GenericRspDTO updateSeq(MkmSeaCcySeqDO mkmSeaCcySeqDO,GenericRspDTO rechargeMkmToolResDTO,String msgCd ,String msgInfo,String status){
        mkmSeaCcySeqDO.setStatus(status);
        mkmSeaCcySeqDO.setMsgCd(msgCd);
        mkmSeaCcySeqDO.setMsgInfo(msgInfo);
        iMkmSeaCcySeqDao.update(mkmSeaCcySeqDO);
        if ( msgCd != null) {
            rechargeMkmToolResDTO.setMsgInfo(msgInfo);
            rechargeMkmToolResDTO.setMsgCd(msgCd);
        }
        return rechargeMkmToolResDTO;
    }

    /**
     * 更新券别流水
     * @param mkmCouponSeqDO
     * @param getConponReqDTO
     * @param msgCd
     * @param msgInfo
     * @param status
     * @return
     */
    private  GenericRspDTO updateConponSeq(MkmCouponSeqDO mkmCouponSeqDO,GenericRspDTO getConponReqDTO,String msgCd ,String msgInfo,String status){
        mkmCouponSeqDO.setStatus(status);
        mkmCouponSeqDO.setMsgCd(msgCd);
        mkmCouponSeqDO.setMsgInfo(msgInfo);
        iMkmCouponSeqDao.update(mkmCouponSeqDO);
        getConponReqDTO.setMsgInfo(msgInfo);
        getConponReqDTO.setMsgCd(msgCd);
        return getConponReqDTO;
    }
}
