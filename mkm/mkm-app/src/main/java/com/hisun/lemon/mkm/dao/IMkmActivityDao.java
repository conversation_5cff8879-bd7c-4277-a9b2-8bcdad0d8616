/*
 * @ClassName IMkmActivityDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.mkm.entity.MkmActivityDO;
import com.hisun.lemon.mkm.req.dto.AutoRealeaseReqDTO;
import com.hisun.lemon.mkm.req.dto.FindMarketActivityReqDTO;
import com.hisun.lemon.mkm.res.dto.MarketActivityDetail;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface IMkmActivityDao extends BaseDao<MkmActivityDO> {
    List<MkmActivityDO> findByfindMarketActivityDTO(FindMarketActivityReqDTO findMarketActivityDTO);

    int updateRemainById(MkmActivityDO paramMap);

    MkmActivityDO getValid(String atvId);

    int updateAclt(MkmActivityDO mkmActivityDO);

    int counTotal(FindMarketActivityReqDTO findMarketActivityDTO);

    List<MarketActivityDetail> queryByFindMarketActivityDTO(FindMarketActivityReqDTO findMarketActivityDTO);

    MkmActivityDO queryOptimalActivity(AutoRealeaseReqDTO autoRealeaseReqDTO);

    int queryByFindMarketActivityDTOCont(FindMarketActivityReqDTO findMarketActivityDTO);
}