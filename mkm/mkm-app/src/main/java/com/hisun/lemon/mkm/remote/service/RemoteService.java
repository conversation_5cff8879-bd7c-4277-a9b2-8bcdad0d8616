package com.hisun.lemon.mkm.remote.service;

import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.mkm.common.Constants;
import com.hisun.lemon.mkm.common.MsgCd;
import com.hisun.lemon.mkm.entity.MkmAccountingSeqDO;
import com.hisun.lemon.mkm.entity.MkmCouponDetailDO;
import com.hisun.lemon.mkm.req.dto.ConsumeCouponReqDTO;
import com.hisun.lemon.mkm.req.dto.ConsumeSeaCcyReqDTO;
import com.hisun.lemon.mkm.req.dto.GetConponReqDTO;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import com.hisun.lemon.urm.client.UserAuthenticationClient;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.CheckPayPwdDTO;
//import com.hisun.lemon.urm.dto.CheckPayPwdSeaDTO;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by chen on 8/4 0004.
 */
@Service
public class RemoteService {
    private static final Logger logger = LoggerFactory.getLogger(RemoteService.class);
    @Resource
    private AccountingTreatmentClient accountingTreatmentClient;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Resource
    private RiskCheckClient riskCheckClient;

    @Resource
    private UserAuthenticationClient userAuthenticationClient;

    /**
     * 调用账务中心账务登记接口
     * @param mkmAccountingSeqDO
     * @return
     */
    public GenericRspDTO accountingTreatment(MkmAccountingSeqDO mkmAccountingSeqDO) {
        GenericRspDTO dot = GenericRspDTO.newSuccessInstance();
        List<AccountingReqDTO> listAcc = new ArrayList<AccountingReqDTO>();
        //借方DTO
        AccountingReqDTO accountingReqDTOD = new AccountingReqDTO();

        // txSts 交易状态 N:正常 R:撤销 C:冲正  撤销暂不考虑
        accountingReqDTOD.setTxSts("N");
        //cTyp  账户类型 U:用户 I：科目 DO对象不存在此域
        accountingReqDTOD.setAcTyp("I");
        //科目号
        accountingReqDTOD.setItmNo(mkmAccountingSeqDO.getItemD());
        //txTyp 交易类型
        accountingReqDTOD.setTxTyp(mkmAccountingSeqDO.getType());
        //交易金额
        accountingReqDTOD.setTxAmt(mkmAccountingSeqDO.getAmt());
        //借贷标志
        accountingReqDTOD.setDcFlg("D");
        // 请求流水号
        accountingReqDTOD.setTxJrnNo(mkmAccountingSeqDO.getAcmSeq());
        //账户资金属性capTyp
        accountingReqDTOD.setCapTyp("1");
        //请求订单号
        accountingReqDTOD.setTxOrdNo(mkmAccountingSeqDO.getAcmSeq());
        //请求订单日期
        accountingReqDTOD.setTxOrdDt(mkmAccountingSeqDO.getTraceTm().toLocalDate());
        //请求订单时间
        accountingReqDTOD.setTxOrdTm(mkmAccountingSeqDO.getTraceTm().toLocalTime());
        accountingReqDTOD.setRmk("营销活动账务登记");
        AccountingReqDTO accountingReqDTOC = new AccountingReqDTO();
        BeanUtils.copyProperties(accountingReqDTOC,accountingReqDTOD);
        accountingReqDTOC.setItmNo(mkmAccountingSeqDO.getItemC());
        accountingReqDTOC.setDcFlg("C");
        listAcc.add(accountingReqDTOD);
        listAcc.add(accountingReqDTOC);
        GenericDTO<List<AccountingReqDTO>> listGenericDTO = GenericDTO.newInstance(listAcc) ;
        return dot = accountingTreatmentClient.accountingTreatment(listGenericDTO);
    }

    /**
     * 查询用户开户状态
     * @param userId
     * @param genericDTO
     * @return
     */
    public String queryUser(String userId, GenericRspDTO genericDTO) {
        GenericRspDTO dot = GenericRspDTO.newSuccessInstance();
        String mobile = "";
        try {
            dot = userBasicInfClient.queryUser(userId);
            if (Constants.URM_SUCCESS.equals(dot.getMsgCd())) {
                UserBasicInfDTO userBasicInfDTO = (UserBasicInfDTO) dot.getBody();
                //用户为开户状态
                if ("0".equals(userBasicInfDTO.getUsrSts())) {
                    genericDTO.setMsgCd(Constants.MKM_SUCCESS);
                    mobile = ((UserBasicInfDTO) dot.getBody()).getMblNo();
                } else {
                    genericDTO.setMsgCd(MsgCd.USER_ACCOUNT_EXP.getMsgCd());
                    genericDTO.setMsgInfo(MsgCd.USER_ACCOUNT_EXP.getMsgInfo());
                }
            }
        } catch (LemonException l){
            logger.error(l.getMessage());
            genericDTO.setMsgCd(l.getMsgCd());
            genericDTO.setMsgInfo(l.getMsgInfo());
        }

        return mobile;
    }
    /**
     * 使用手机号查询用户信息
     * @param mobile
     * @param genericDTO
     * @return
     */
    public String queryUserByLoginId(String mobile, GenericRspDTO genericDTO) {
        GenericRspDTO dot = GenericRspDTO.newSuccessInstance();
        String userId = "";
        try {
            dot = userBasicInfClient.queryUserByLoginId(mobile);
            if (Constants.URM_SUCCESS.equals(dot.getMsgCd())) {
                UserBasicInfDTO userBasicInfDTO = (UserBasicInfDTO) dot.getBody();
                //用户为开户状态
                if ("0".equals(userBasicInfDTO.getUsrSts())) {
                    genericDTO.setMsgCd(Constants.MKM_SUCCESS);
                    userId = ((UserBasicInfDTO) dot.getBody()).getUserId();
                } else {
                    genericDTO.setMsgCd(MsgCd.USER_ACCOUNT_EXP.getMsgCd());
                    genericDTO.setMsgInfo(MsgCd.USER_ACCOUNT_EXP.getMsgInfo());
                }
            }
        } catch (LemonException l){
            logger.error(l.getMessage());
            genericDTO.setMsgCd(l.getMsgCd());
            genericDTO.setMsgInfo(l.getMsgInfo());
        }

        return userId;
    }
    /**
     * 校验支付密码
     * @param userId
     * @param payPwdRandom
     *@param genericDTO  @return
     */
    public String checkPayPassword(String userId, String pwd, String payPwdRandom, String seaRandom, GenericRspDTO genericDTO) {
        GenericRspDTO dot = GenericRspDTO.newSuccessInstance();
        try {
            if(seaRandom != null && !"".equals(seaRandom)){
//                CheckPayPwdSeaDTO checkPayPwdSeaDTO = new CheckPayPwdSeaDTO();
//                GenericDTO<CheckPayPwdSeaDTO> generic = GenericDTO.newInstance(checkPayPwdSeaDTO);
//                checkPayPwdSeaDTO.setPayPwd(pwd);
//                checkPayPwdSeaDTO.setUserId(userId);
//                checkPayPwdSeaDTO.setPayPwdRandom(payPwdRandom);
//                checkPayPwdSeaDTO.setSeaRandom(seaRandom);
//                dot =userAuthenticationClient.checkPayPasswordSea(generic);
            }else{
                CheckPayPwdDTO body  = new CheckPayPwdDTO();
                GenericDTO<CheckPayPwdDTO> generic = GenericDTO.newInstance(body);
                body.setPayPwd(pwd);
//                body.setUserId(userId);
                body.setPayPwdRandom(payPwdRandom);
                dot =userAuthenticationClient.checkPayPwd(generic);
            }

            if (!Constants.URM_SUCCESS.equals(dot.getMsgCd())) {
                genericDTO.setMsgInfo(dot.getMsgInfo());
                genericDTO.setMsgCd(dot.getMsgCd());
                return "";
            }
        } catch (LemonException l){
            logger.error(l.getMessage());
            genericDTO.setMsgCd(l.getMsgCd());
            genericDTO.setMsgInfo(l.getMsgInfo());
        }

        return dot.getMsgCd();
    }

    /**
     *用户风控检查
     * @param getConponReqDTO
     * @return
     */
    public GenericRspDTO riskCheck(GetConponReqDTO getConponReqDTO) {
        RiskCheckUserStatusReqDTO riskCheckUserStatusReqDTO = new RiskCheckUserStatusReqDTO();
        GenericRspDTO ris = GenericRspDTO.newInstance();
        riskCheckUserStatusReqDTO.setId(getConponReqDTO.getUserId());
        riskCheckUserStatusReqDTO.setIdTyp(Constants.RSM_ID_TY);
        try {
            ris = riskCheckClient.checkUserStatus(riskCheckUserStatusReqDTO);
        } catch (LemonException e){
            logger.error("异常"+e.getMessage());
            ris.setMsgCd(MsgCd.RISK_CHECK_EXCEPTION.getMsgCd());
            ris.setMsgInfo(MsgCd.RISK_CHECK_EXCEPTION.getMsgInfo());
        }
        return ris;
    }

    /**
     * 实时风控检查
     * @param consumeCouponReqDTO
     * @param mkmCouponDetailDO
     * @param consumeSeaCcyReqDTO
     * @return
     */
    public GenericRspDTO riskTimeCheck(ConsumeCouponReqDTO consumeCouponReqDTO, MkmCouponDetailDO mkmCouponDetailDO, ConsumeSeaCcyReqDTO consumeSeaCcyReqDTO) {
        JrnReqDTO jrnReqDTO = new JrnReqDTO();
        GenericRspDTO genericRspDTO = GenericRspDTO.newInstance();
        if (consumeCouponReqDTO != null) {
           jrnReqDTO.setStlUserId(mkmCouponDetailDO.getInstId());
            jrnReqDTO.setStlUserTyp("01");
            jrnReqDTO.setTxOrdNo(consumeCouponReqDTO.getOrderNo());
            jrnReqDTO.setPayUserId(consumeCouponReqDTO.getUserId());
            jrnReqDTO.setTxAmt(mkmCouponDetailDO.getAmt());
            jrnReqDTO.setTxJrnNo(consumeCouponReqDTO.getSeq());
            jrnReqDTO.setTxDate(consumeCouponReqDTO.getConsumeTm().toLocalDate());
            jrnReqDTO.setTxTime(consumeCouponReqDTO.getConsumeTm().toLocalTime());
        } else {
            jrnReqDTO.setStlUserId(consumeSeaCcyReqDTO.getInstId());
            jrnReqDTO.setStlUserTyp("01");
            jrnReqDTO.setTxOrdNo(consumeSeaCcyReqDTO.getOrderNo());
            jrnReqDTO.setPayUserId(consumeSeaCcyReqDTO.getUserId());
            jrnReqDTO.setTxAmt(BigDecimal.valueOf(consumeSeaCcyReqDTO.getCount()));
            jrnReqDTO.setTxJrnNo(consumeSeaCcyReqDTO.getSeq());
            jrnReqDTO.setTxDate(consumeSeaCcyReqDTO.getConsumeTm().toLocalDate());
            jrnReqDTO.setTxTime(consumeSeaCcyReqDTO.getConsumeTm().toLocalTime());
        }
        jrnReqDTO.setCcy(Constants.RSM_CCY);
        jrnReqDTO.setPayTyp(Constants.RSM_PAY_TYPE);
        jrnReqDTO.setPayUserTyp(Constants.RSM_PAY_USER_TYP);
        jrnReqDTO.setTxTyp(Constants.RSM_TX_TYP);
        jrnReqDTO.setTxCnl(Constants.MKM);
        jrnReqDTO.setTxSts(Constants.RSM_TX_STS);
        try {
            genericRspDTO =  riskCheckClient.riskControl(jrnReqDTO);
        } catch (LemonException e) {
            logger.error("异常"+e.getMessage());
            genericRspDTO.setMsgCd(MsgCd.RISK_CHECK_EXCEPTION.getMsgCd());
            genericRspDTO.setMsgInfo(MsgCd.RISK_CHECK_EXCEPTION.getMsgInfo());
        }
        return genericRspDTO;
    }


//    //生成一组账务
//
//    public List<AccountingReqDTO> getAccList(MkmAccountingSeqDO... mkmAccountingSeqDOs) {
//        AccountingReqDTO userAccountReqDTOC = null;
//        AccountingReqDTO userAccountReqDTOD = null;
//        List<AccountingReqDTO> list = new ArrayList<>();
//        for (MkmAccountingSeqDO mkmAccountingSeqDO : mkmAccountingSeqDOs) {
//            userAccountReqDTOD = acmComponent.createAccountingReqDTO(
//                    mkmAccountingSeqDO.getAcmSeq(),
//                    mkmAccountingSeqDO.getAcmSeq(),
//                    mkmAccountingSeqDO.getType(),
//                    ACMConstants.ACCOUNTING_NOMARL,
//                    mkmAccountingSeqDO.getAmt(),
//                    "",
//                    ACMConstants.ITM_AC_TYP,
//                    mkmAccountingSeqDO.getCapTyp(),
//                    ACMConstants.AC_D_FLG,
//                    mkmAccountingSeqDO.getItemD(),
//                    "",
//                    null,
//                    null,
//                    null,
//                    mkmAccountingSeqDO.getRemark());
//            //贷：应收账款-用户赎回款- xx活期理财
//            userAccountReqDTOC = acmComponent.createAccountingReqDTO(
//                    mkmAccountingSeqDO.getAcmSeq(),
//                    mkmAccountingSeqDO.getAcmSeq(),
//                    mkmAccountingSeqDO.getType(),
//                    ACMConstants.ACCOUNTING_NOMARL,
//                    mkmAccountingSeqDO.getAmt(),
//                    "",
//                    ACMConstants.ITM_AC_TYP,
//                    mkmAccountingSeqDO.getCapTyp(),
//                    ACMConstants.AC_C_FLG,
//                    mkmAccountingSeqDO.getItemC(),
//                    "",
//                    null,
//                    null,
//                    null,
//                    mkmAccountingSeqDO.getRemark());
//            list.add(userAccountReqDTOD);
//            list.add(userAccountReqDTOC);
//        }
//        return list;
//
//    }
}
