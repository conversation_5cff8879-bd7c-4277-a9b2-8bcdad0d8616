package com.hisun.lemon.mkm.service;

import com.hisun.lemon.mkm.req.dto.FAQManagerReqDTO;
import com.hisun.lemon.mkm.res.dto.FAQManagerRspDTO;

/**
 * 常见问题服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 20:30
 */
public interface FAQManagerService {
    
    /**
     * 查询常见问题列表
     *
     * @param reqDTO 查询条件
     * @return 常见问题列表
     */
    FAQManagerRspDTO queryFAQList(FAQManagerReqDTO reqDTO);
} 