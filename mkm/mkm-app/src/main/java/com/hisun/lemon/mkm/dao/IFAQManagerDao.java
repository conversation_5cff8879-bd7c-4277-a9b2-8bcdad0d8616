package com.hisun.lemon.mkm.dao;

import com.hisun.lemon.mkm.entity.FAQManagerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 常见问题DAO接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 20:25
 */
@Mapper
public interface IFAQManagerDao {
    
    /**
     * 根据条件查询常见问题列表
     *
     * @param paramMap 查询条件
     * @return 常见问题列表
     */
    List<FAQManagerDO> queryFAQList(Map<String, Object> paramMap);
} 