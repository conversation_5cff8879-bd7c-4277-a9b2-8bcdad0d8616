package com.hisun.lemon.mkm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.mkm.common.Constants;
import com.hisun.lemon.mkm.common.MsgCd;
import com.hisun.lemon.mkm.remote.service.RemoteService;
import com.hisun.lemon.mkm.req.dto.QueryUserMkmToolReqDTO;
import com.hisun.lemon.mkm.req.dto.RechargeMkmToolReqDTO;
import com.hisun.lemon.mkm.req.dto.SeaccyGitfRepDTO;
import com.hisun.lemon.mkm.req.dto.SeaccyTraceDetailRepDTO;
import com.hisun.lemon.mkm.req.innerDto.InSeaccyGitfRepDTO;
import com.hisun.lemon.mkm.req.innerDto.InnerRechargeMkmToolReqDTO;
import com.hisun.lemon.mkm.req.innerDto.QueryUserSeaCcyReqDTO;
import com.hisun.lemon.mkm.res.dto.QueryUserMkmToolRspDTO;
import com.hisun.lemon.mkm.res.dto.RechargeMkmToolResDTO;
import com.hisun.lemon.mkm.res.dto.SeaccyGitfRspDTO;
import com.hisun.lemon.mkm.res.dto.SeaccyTraceDetailRspDTO;
import com.hisun.lemon.mkm.res.innerDto.QueryUserSeaCcyResDTO;
import com.hisun.lemon.mkm.service.MkmToolService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by chen on 8/29 0029.
 */
@RestController
@RequestMapping("/innerMkmTool")
public class InnerMkmToolController extends BaseController{

    @Resource
    private MkmToolService mkmToolService;

    @Resource
    private RemoteService remoteService;

    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/seaccyTraceDetail")
    public GenericRspDTO<SeaccyTraceDetailRspDTO> seaccyTraceDetail(@Validated @RequestBody GenericDTO<SeaccyTraceDetailRepDTO> req) {
        GenericRspDTO<SeaccyTraceDetailRspDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new SeaccyTraceDetailRspDTO());
        try{
            genericRspDTO = mkmToolService.seaccyTraceDetail(req);
        }catch (LemonException e) {
            genericRspDTO.setMsgInfo(e.getMsgInfo());
            genericRspDTO.setMsgCd(e.getMsgCd());
        }
        return genericRspDTO;
    }


    /**
     * 海币赠送
     * @param req
     * @return
     */
    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/seaccyGitf")
    public GenericRspDTO<NoBody> seaccyGitf(@Validated @RequestBody GenericDTO<InSeaccyGitfRepDTO> req) {
        SeaccyGitfRepDTO body  = new SeaccyGitfRepDTO();
        GenericDTO<SeaccyGitfRepDTO> seaccyGitfRepDTO = GenericDTO.newInstance(body);
        GenericRspDTO<SeaccyGitfRspDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new SeaccyGitfRspDTO());
        GenericRspDTO<NoBody> res =GenericRspDTO.newInstance();
        //查询用户id
        String userId = remoteService.queryUserByLoginId(req.getBody().getMobile(),res);
        String gUserId = remoteService.queryUserByLoginId(req.getBody().getgMobile(),res);
        if (JudgeUtils.isBlank(userId) || JudgeUtils.isBlank(gUserId)) {
            res.setMsgCd(MsgCd.QUERY_USER_EXP.getMsgCd());
            res.setMsgInfo(MsgCd.QUERY_USER_EXP.getMsgInfo());
            return res;
        }
        //检验支付密码
        String result = remoteService.checkPayPassword(userId ,req.getBody().getPwd(),req.getBody().getPayPwdRandom(), req.getBody().getSeaRandom(), res);
        if (!Constants.URM_SUCCESS.equals(result)){
            return res;
        }
        body.setSeq(req.getBody().getSeq());
        body.setCount(req.getBody().getCount());

        body.setGitfUser(gUserId);
        body.setUserId(userId);
        body.setgMobile(req.getBody().getgMobile());
        body.setTraceTime(DateTimeUtils.parseLocalDateTime(req.getBody().getTraceTime()));
        body.setMobile(req.getBody().getMobile());
        //body.setGitfUser();
        try{
            genericRspDTO = mkmToolService.seaccyGitf(seaccyGitfRepDTO);
            res.setMsgCd(genericRspDTO.getMsgCd());
            res.setMsgInfo(genericRspDTO.getMsgInfo());
        }catch (LemonException e) {
            res.setMsgInfo(e.getMsgInfo());
            res.setMsgCd(e.getMsgCd());
        }
        return res;
    }

    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/queryUserSeaccy")
    public GenericRspDTO<QueryUserSeaCcyResDTO> queryUserSeaccy(@Validated @RequestBody GenericDTO<QueryUserSeaCcyReqDTO> req) {
        GenericRspDTO<QueryUserSeaCcyResDTO> genericRspDTO =GenericRspDTO.newSuccessInstance(new QueryUserSeaCcyResDTO());
        try{
            genericRspDTO = mkmToolService.queryUserSeaccy(req.getBody());
        }catch (LemonException e) {
            genericRspDTO.setMsgInfo(e.getMsgInfo());
            genericRspDTO.setMsgCd(e.getMsgCd());
        }
        return genericRspDTO;
    }

    @ApiResponse(code = 200, message = "查询结果")
    @PostMapping("/get")
    public GenericRspDTO<RechargeMkmToolResDTO> getSeaCyy(@Validated @RequestBody GenericDTO<InnerRechargeMkmToolReqDTO> innerRechargeMkmToolReqDTO) {
        RechargeMkmToolResDTO body =  new RechargeMkmToolResDTO();
        GenericRspDTO<RechargeMkmToolResDTO> rechargeMkmToolResDTO =GenericRspDTO.newSuccessInstance(body);
        try {
            RechargeMkmToolReqDTO rechargeMkmToolReqDTO = new RechargeMkmToolReqDTO();
            GenericDTO<RechargeMkmToolReqDTO> genericDTO =  GenericDTO.newInstance(rechargeMkmToolReqDTO);
            //查询用户id
            String userId = remoteService.queryUserByLoginId(innerRechargeMkmToolReqDTO.getBody().getMobile(),rechargeMkmToolResDTO);
            if (JudgeUtils.isBlank(userId)) {
                rechargeMkmToolResDTO.setMsgCd(MsgCd.QUERY_USER_EXP.getMsgCd());
                rechargeMkmToolResDTO.setMsgInfo(MsgCd.QUERY_USER_EXP.getMsgInfo());
            }
            innerRechargeMkmToolReqDTO.getBody().setUserId(userId);
            BeanUtils.copyProperties(rechargeMkmToolReqDTO , innerRechargeMkmToolReqDTO.getBody());
            rechargeMkmToolReqDTO.setRechargeTm(DateTimeUtils.parseLocalDateTime(innerRechargeMkmToolReqDTO.getBody().getTraceTime()));
            rechargeMkmToolResDTO = mkmToolService.recharge(genericDTO);
        } catch (LemonException e) {
            body.setResult(Constants.SEQ_EXCEPTION);
            rechargeMkmToolResDTO.setMsgCd(e.getMsgCd());
            rechargeMkmToolResDTO.setMsgInfo(e.getMsgInfo());
        }
        return rechargeMkmToolResDTO;
    }
}
