/*
 * @ClassName MkmActivityDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 16:42:40
 */
package com.hisun.lemon.mkm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MkmActivityDO extends BaseDO {
    /**
     * @Fields id 活动id
     */
    private String id;
    /**
     * @Fields atvNm 活动名称
     */
    private String atvNm;
    /**
     * @Fields mkTool 营销工具 01-电子券，02-海币 03-优惠券
     */
    private String mkTool;
    /**
     * @Fields examineStatus 审核状态 01-未审核 ，02-已审核
     */
    private String examineStatus;
    /**
     * @Fields status 状态 01-冻结 ，02-解冻
     */
    private String status;
    /**
     * @Fields total 发放总量
     */
    private Integer total;
    /**
     * @Fields totalAmt 发放总金额
     */
    private BigDecimal totalAmt;
    /**
     * @Fields amt 单券金额
     */
    private BigDecimal amt;
    /**
     * @Fields  折扣
     */
    private BigDecimal discount;
    /**
     * @Fields beginTime 活动开始时间
     */
    private LocalDateTime beginTime;
    /**
     * @Fields endTime 活动结束时间
     */
    private LocalDateTime endTime;
    /**
     * @Fields remainNum 剩余发放总数
     */
    private Integer remainNum;
    /**
     * @Fields remainAmt 剩余发放总金额
     */
    private BigDecimal remainAmt;
    /**
     * @Fields crtUserOpr 创建人员
     */
    private String crtUserOpr;
    /**
     * @Fields mdfUserOpr 操作人员
     */
    private String mdfUserOpr;
    /**
     * @Fields instId 商户id （电子券）
     */
    private String instId;

    /**
     * 累计消费金额
     */
    private BigDecimal acltAmt;

    /**
     * 累计消费总数
     */
    private Integer aclt;

    /**
     * 科目号
     */
    private String item;

    /**
     * 券别名称
     */
    private String couponName;

    /**
     * 费用承担方
     */
    private String costSide;

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAtvNm() {
        return atvNm;
    }

    public void setAtvNm(String atvNm) {
        this.atvNm = atvNm;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(String examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }


    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public LocalDateTime getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(LocalDateTime beginTime) {
        this.beginTime = beginTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getRemainNum() {
        return remainNum;
    }

    public void setRemainNum(Integer remainNum) {
        this.remainNum = remainNum;
    }

    public BigDecimal getRemainAmt() {
        return remainAmt;
    }

    public void setRemainAmt(BigDecimal remainAmt) {
        this.remainAmt = remainAmt;
    }

    public String getCrtUserOpr() {
        return crtUserOpr;
    }

    public void setCrtUserOpr(String crtUserOpr) {
        this.crtUserOpr = crtUserOpr;
    }

    public String getMdfUserOpr() {
        return mdfUserOpr;
    }

    public void setMdfUserOpr(String mdfUserOpr) {
        this.mdfUserOpr = mdfUserOpr;
    }
//    public String getInstId() {
//        return instId;
//    }
//
//    public void setInstId(String instId) {
//        this.instId = instId;
//    }

    public BigDecimal getAcltAmt() {
        return acltAmt;
    }

    public void setAcltAmt(BigDecimal acltAmt) {
        this.acltAmt = acltAmt;
    }

    public Integer getAclt() {
        return aclt;
    }

    public void setAclt(Integer aclt) {
        this.aclt = aclt;
    }

    public String getInstId() {
        return instId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getCostSide() {
        return costSide;
    }



    public void setCostSide(String costSide) {
        this.costSide = costSide;
    }
}