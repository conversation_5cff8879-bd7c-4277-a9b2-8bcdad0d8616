apply plugin: 'application'

dependencies {
    compile project(":mkm-interface")
    compile("com.hisun:lemon-swagger")
    compile("com.hisun:lemon-framework")
    compile("com.hisun:acm-interface"){
        transitive = false
        changing = true
    }
    compile("com.hisun:urm-interface"){
        transitive = false
        changing = true
    }
    compile("com.hisun:rsm-interface"){
        transitive = false
        changing = true
    }
    compile("com.hisun:jcommon")
    compile("org.springframework.boot:spring-boot-starter-thymeleaf")
    compile("org.apache.poi:poi:3.16")
    compile("org.apache.poi:poi-ooxml:3.16")
}

mainClassName = 'com.hisun.lemon.Application'

jar {
    manifest {
        attributes(
                "Implementation-Title": "Gradle",
                "Implementation-Version": "${version}",
                "Class-Path": '. config/'
        )
    }
    //exclude('config/')
}

bootRepackage {
    enabled = true
    mainClass = 'com.hisun.lemon.Application'
}

task clearTarget(type:Delete){
    delete 'build/target'
}

task release(type: Copy,dependsOn: [clearTarget,build]) {
    from('build/libs') {
        include '*.jar'
        exclude '*-sources.jar'
    }
    //from('src/main/resources') {
     //   include 'config/*'
    //}
    into ('build/target')

    into('bin') {
        from 'shell'
    }
}

task dist(type: Zip,dependsOn: [release]) {
    from ('build/target/') {
    }
}