#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1001136 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:389), pid=2128, tid=0x0000000000006908
#
# JRE version: Java(TM) SE Runtime Environment (8.0_251-b08) (build 1.8.0_251-b08)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.251-b08 mixed mode windows-amd64 compressed oops)
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#

---------------  T H R E A D  ---------------

Current thread (0x000000001757c000):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=26888, stack(0x0000000018bd0000,0x0000000018cd0000)]

Stack: [0x0000000018bd0000,0x0000000018cd0000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)


Current CompileTask:
C2:12832005 17890       4       org.codehaus.groovy.reflection.CachedMethod::compareTo (25 bytes)


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x0000000020a7e000 JavaThread "Cache worker for cache directory md-rule (D:\develop\gradle\gradle-4.10.3\caches\4.10.3\md-rule)" [_thread_blocked, id=28492, stack(0x0000000020040000,0x0000000020140000)]
  0x0000000020a83000 JavaThread "Cache worker for cache directory md-supplier (D:\develop\gradle\gradle-4.10.3\caches\4.10.3\md-supplier)" [_thread_blocked, id=25832, stack(0x000000001ee10000,0x000000001ef10000)]
  0x0000000020a82000 JavaThread "Cache worker for file hash cache (D:\work\hqneat\web3\wallet3.0\mcht-be\rsm\.gradle\4.10.3\fileHashes)" [_thread_blocked, id=29020, stack(0x000000001daf0000,0x000000001dbf0000)]
  0x0000000020a81000 JavaThread "Stdin handler" [_thread_blocked, id=26372, stack(0x000000001b550000,0x000000001b650000)]
  0x0000000020a85000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:64054 to /127.0.0.1:50329" [_thread_blocked, id=27932, stack(0x00000000010d0000,0x00000000011d0000)]
  0x0000000020a89000 JavaThread "Daemon Thread 15" [_thread_blocked, id=29084, stack(0x0000000020740000,0x0000000020840000)]
  0x0000000020a86800 JavaThread "Daemon Thread 14" [_thread_blocked, id=11840, stack(0x0000000020240000,0x0000000020340000)]
  0x0000000020a84800 JavaThread "Daemon Thread 13" [_thread_blocked, id=19272, stack(0x000000001ff40000,0x0000000020040000)]
  0x0000000020a7a800 JavaThread "Cancel handler" [_thread_blocked, id=25664, stack(0x000000001ebc0000,0x000000001ecc0000)]
  0x0000000020a79800 JavaThread "Daemon Thread 12" [_thread_blocked, id=25712, stack(0x000000001b650000,0x000000001b750000)]
  0x0000000020a7c800 JavaThread "Handler for socket connection from /127.0.0.1:64054 to /127.0.0.1:50329" [_thread_in_native, id=13280, stack(0x000000001b450000,0x000000001b550000)]
  0x0000000020a7c000 JavaThread "Daemon Thread 11" [_thread_blocked, id=28612, stack(0x000000001b350000,0x000000001b450000)]
  0x0000000020a80800 JavaThread "Daemon Thread 10" [_thread_blocked, id=3732, stack(0x000000001ae50000,0x000000001af50000)]
  0x0000000020a7b000 JavaThread "Exec process Thread 4" [_thread_blocked, id=12768, stack(0x0000000025e60000,0x0000000025f60000)]
  0x0000000020a7f000 JavaThread "Exec process Thread 3" [_thread_blocked, id=25512, stack(0x0000000025d60000,0x0000000025e60000)]
  0x0000000020a7d800 JavaThread "Exec process Thread 2" [_thread_blocked, id=24608, stack(0x0000000025c60000,0x0000000025d60000)]
  0x000000001bb01000 JavaThread "Exec process" [_thread_blocked, id=27456, stack(0x0000000025a60000,0x0000000025b60000)]
  0x000000001bafd000 JavaThread "Daemon worker Thread 9" [_thread_in_vm, id=3864, stack(0x000000001af50000,0x000000001b050000)]
  0x000000001baf5800 JavaThread "Daemon Thread 9" [_thread_blocked, id=24656, stack(0x0000000000fd0000,0x00000000010d0000)]
  0x000000001bf92800 JavaThread "Cache worker for Java compile cache (D:\develop\gradle\gradle-4.10.3\caches\4.10.3\javaCompile)" [_thread_blocked, id=27176, stack(0x0000000023990000,0x0000000023a90000)]
  0x000000001b9b8000 JavaThread "Cache worker for file content cache (D:\develop\gradle\gradle-4.10.3\caches\4.10.3\fileContent)" [_thread_blocked, id=2168, stack(0x0000000021ce0000,0x0000000021de0000)]
  0x000000001b9b5000 JavaThread "Memory manager" [_thread_blocked, id=18024, stack(0x00000000208e0000,0x00000000209e0000)]
  0x000000001b9b3000 JavaThread "Cache worker for Artifact transforms cache (D:\develop\gradle\gradle-4.10.3\caches\transforms-1)" [_thread_blocked, id=25520, stack(0x000000001fe40000,0x000000001ff40000)]
  0x000000001a467000 JavaThread "Cache worker for journal cache (D:\develop\gradle\gradle-4.10.3\caches\journal-1)" [_thread_blocked, id=27020, stack(0x000000001ed10000,0x000000001ee10000)]
  0x000000001a462800 JavaThread "File lock request listener" [_thread_in_native, id=22928, stack(0x000000001ca20000,0x000000001cb20000)]
  0x000000001a460000 JavaThread "Cache worker for file hash cache (D:\develop\gradle\gradle-4.10.3\caches\4.10.3\fileHashes)" [_thread_blocked, id=28736, stack(0x000000001c920000,0x000000001ca20000)]
  0x000000001a55c000 JavaThread "Daemon periodic checks" [_thread_blocked, id=27112, stack(0x000000001ad50000,0x000000001ae50000)]
  0x000000001a53b800 JavaThread "Incoming local TCP Connector on port 64054" [_thread_in_native, id=27828, stack(0x000000001aba0000,0x000000001aca0000)]
  0x000000001a47d000 JavaThread "Daemon health stats" [_thread_blocked, id=26572, stack(0x000000001a8a0000,0x000000001a9a0000)]
  0x0000000017610000 JavaThread "Service Thread" daemon [_thread_blocked, id=28404, stack(0x0000000018ed0000,0x0000000018fd0000)]
  0x000000001758a000 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=8308, stack(0x0000000018dd0000,0x0000000018ed0000)]
  0x0000000017589800 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=24824, stack(0x0000000018cd0000,0x0000000018dd0000)]
=>0x000000001757c000 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=26888, stack(0x0000000018bd0000,0x0000000018cd0000)]
  0x000000001751b000 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=26036, stack(0x0000000018ad0000,0x0000000018bd0000)]
  0x000000001751a000 JavaThread "Attach Listener" daemon [_thread_blocked, id=25904, stack(0x00000000189d0000,0x0000000018ad0000)]
  0x0000000017572000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=28760, stack(0x00000000188d0000,0x00000000189d0000)]
  0x0000000015e22800 JavaThread "Finalizer" daemon [_thread_blocked, id=20568, stack(0x0000000018760000,0x0000000018860000)]
  0x0000000017503000 JavaThread "Reference Handler" daemon [_thread_blocked, id=14936, stack(0x0000000018660000,0x0000000018760000)]
  0x00000000030fe800 JavaThread "main" [_thread_blocked, id=29104, stack(0x0000000002b20000,0x0000000002c20000)]

Other Threads:
  0x0000000015e16800 VMThread [stack: 0x0000000018560000,0x0000000018660000] [id=25624]
  0x0000000017629000 WatcherThread [stack: 0x0000000018fd0000,0x00000000190d0000] [id=27960]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000100000000

Heap:
 PSYoungGen      total 309248K, used 224678K [0x00000000eab00000, 0x0000000100000000, 0x0000000100000000)
  eden space 275456K, 72% used [0x00000000eab00000,0x00000000f6d06b88,0x00000000fb800000)
  from space 33792K, 76% used [0x00000000fdf00000,0x00000000ff862f48,0x0000000100000000)
  to   space 36864K, 0% used [0x00000000fb800000,0x00000000fb800000,0x00000000fdc00000)
 ParOldGen       total 330752K, used 152115K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 45% used [0x00000000c0000000,0x00000000c948cf50,0x00000000d4300000)
 Metaspace       used 63436K, capacity 65084K, committed 65280K, reserved 1105920K
  class space    used 8046K, capacity 8434K, committed 8448K, reserved 1048576K

Card table byte_map: [0x00000000124c0000,0x00000000126d0000] byte_map_base: 0x0000000011ec0000

Marking Bits: (ParMarkBitMap*) 0x00000000615aaf90
 Begin Bits: [0x0000000013230000, 0x0000000014230000)
 End Bits:   [0x0000000014230000, 0x0000000015230000)

Polling page: 0x0000000001330000

CodeCache: size=245760Kb used=52064Kb max_used=54584Kb free=193695Kb
 bounds [0x0000000003100000, 0x00000000066a0000, 0x0000000012100000]
 total_blobs=14489 nmethods=13683 adapters=717
 compilation: enabled

Compilation events (10 events):
Event: 12831.953 Thread 0x000000001758a000 nmethod 17934 0x0000000005633c50 code [0x0000000005633da0, 0x0000000005633f50]
Event: 12831.955 Thread 0x000000001751b000 nmethod 17897% 0x0000000005b4e9d0 code [0x0000000005b4eb20, 0x0000000005b4edd8]
Event: 12831.955 Thread 0x000000001751b000 17935       4       sun.util.locale.provider.LocaleProviderAdapter::getAdapter (171 bytes)
Event: 12831.988 Thread 0x000000001758a000 17936       3       java.lang.reflect.AccessibleObject::setAccessible (21 bytes)
Event: 12831.989 Thread 0x000000001758a000 nmethod 17936 0x00000000036b12d0 code [0x00000000036b1460, 0x00000000036b1758]
Event: 12831.991 Thread 0x000000001751b000 nmethod 17935 0x000000000403bc90 code [0x000000000403bf00, 0x000000000403ca68]
Event: 12831.991 Thread 0x000000001751b000 17937       4       org.codehaus.groovy.runtime.metaclass.MetaMethodIndex::addMethodToList (298 bytes)
Event: 12831.999 Thread 0x000000001758a000 17938       1       org.gradle.model.internal.registry.DefaultModelRegistry$ModelNodeGoal::getPath (5 bytes)
Event: 12831.999 Thread 0x000000001758a000 nmethod 17938 0x0000000005633950 code [0x0000000005633aa0, 0x0000000005633bb0]
Event: 12831.999 Thread 0x000000001758a000 17939       1       org.gradle.model.internal.core.ModelPath$1::toString (3 bytes)

GC Heap History (10 events):
Event: 12806.871 GC heap before
{Heap before GC invocations=68 (full 4):
 PSYoungGen      total 314880K, used 298638K [0x00000000eab00000, 0x00000000fff80000, 0x0000000100000000)
  eden space 285184K, 100% used [0x00000000eab00000,0x00000000fc180000,0x00000000fc180000)
  from space 29696K, 45% used [0x00000000fc180000,0x00000000fcea3958,0x00000000fde80000)
  to   space 28160K, 0% used [0x00000000fe400000,0x00000000fe400000,0x00000000fff80000)
 ParOldGen       total 330752K, used 121021K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 36% used [0x00000000c0000000,0x00000000c762f748,0x00000000d4300000)
 Metaspace       used 57147K, capacity 58614K, committed 59008K, reserved 1099776K
  class space    used 7308K, capacity 7676K, committed 7808K, reserved 1048576K
Event: 12806.890 GC heap after
Heap after GC invocations=68 (full 4):
 PSYoungGen      total 317440K, used 6240K [0x00000000eab00000, 0x00000000ffd80000, 0x0000000100000000)
  eden space 291328K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000fc780000)
  from space 26112K, 23% used [0x00000000fe400000,0x00000000fea18000,0x00000000ffd80000)
  to   space 27648K, 0% used [0x00000000fc780000,0x00000000fc780000,0x00000000fe280000)
 ParOldGen       total 330752K, used 126993K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 38% used [0x00000000c0000000,0x00000000c7c04668,0x00000000d4300000)
 Metaspace       used 57147K, capacity 58614K, committed 59008K, reserved 1099776K
  class space    used 7308K, capacity 7676K, committed 7808K, reserved 1048576K
}
Event: 12807.284 GC heap before
{Heap before GC invocations=69 (full 4):
 PSYoungGen      total 317440K, used 297568K [0x00000000eab00000, 0x00000000ffd80000, 0x0000000100000000)
  eden space 291328K, 100% used [0x00000000eab00000,0x00000000fc780000,0x00000000fc780000)
  from space 26112K, 23% used [0x00000000fe400000,0x00000000fea18000,0x00000000ffd80000)
  to   space 27648K, 0% used [0x00000000fc780000,0x00000000fc780000,0x00000000fe280000)
 ParOldGen       total 330752K, used 126993K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 38% used [0x00000000c0000000,0x00000000c7c04668,0x00000000d4300000)
 Metaspace       used 57148K, capacity 58614K, committed 59008K, reserved 1099776K
  class space    used 7308K, capacity 7676K, committed 7808K, reserved 1048576K
Event: 12807.300 GC heap after
Heap after GC invocations=69 (full 4):
 PSYoungGen      total 318976K, used 7216K [0x00000000eab00000, 0x00000000fff80000, 0x0000000100000000)
  eden space 291328K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000fc780000)
  from space 27648K, 26% used [0x00000000fc780000,0x00000000fce8c010,0x00000000fe280000)
  to   space 26624K, 0% used [0x00000000fe580000,0x00000000fe580000,0x00000000fff80000)
 ParOldGen       total 330752K, used 132289K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 39% used [0x00000000c0000000,0x00000000c8130668,0x00000000d4300000)
 Metaspace       used 57148K, capacity 58614K, committed 59008K, reserved 1099776K
  class space    used 7308K, capacity 7676K, committed 7808K, reserved 1048576K
}
Event: 12807.685 GC heap before
{Heap before GC invocations=70 (full 4):
 PSYoungGen      total 318976K, used 298544K [0x00000000eab00000, 0x00000000fff80000, 0x0000000100000000)
  eden space 291328K, 100% used [0x00000000eab00000,0x00000000fc780000,0x00000000fc780000)
  from space 27648K, 26% used [0x00000000fc780000,0x00000000fce8c010,0x00000000fe280000)
  to   space 26624K, 0% used [0x00000000fe580000,0x00000000fe580000,0x00000000fff80000)
 ParOldGen       total 330752K, used 132289K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 39% used [0x00000000c0000000,0x00000000c8130668,0x00000000d4300000)
 Metaspace       used 57211K, capacity 58620K, committed 59008K, reserved 1099776K
  class space    used 7308K, capacity 7677K, committed 7808K, reserved 1048576K
Event: 12807.718 GC heap after
Heap after GC invocations=70 (full 4):
 PSYoungGen      total 308224K, used 26608K [0x00000000eab00000, 0x0000000100000000, 0x0000000100000000)
  eden space 281600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000fbe00000)
  from space 26624K, 99% used [0x00000000fe580000,0x00000000fff7c050,0x00000000fff80000)
  to   space 33792K, 0% used [0x00000000fbe00000,0x00000000fbe00000,0x00000000fdf00000)
 ParOldGen       total 330752K, used 139942K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 42% used [0x00000000c0000000,0x00000000c88a9910,0x00000000d4300000)
 Metaspace       used 57211K, capacity 58620K, committed 59008K, reserved 1099776K
  class space    used 7308K, capacity 7677K, committed 7808K, reserved 1048576K
}
Event: 12807.974 GC heap before
{Heap before GC invocations=71 (full 4):
 PSYoungGen      total 308224K, used 308207K [0x00000000eab00000, 0x0000000100000000, 0x0000000100000000)
  eden space 281600K, 99% used [0x00000000eab00000,0x00000000fbdffea0,0x00000000fbe00000)
  from space 26624K, 99% used [0x00000000fe580000,0x00000000fff7c050,0x00000000fff80000)
  to   space 33792K, 0% used [0x00000000fbe00000,0x00000000fbe00000,0x00000000fdf00000)
 ParOldGen       total 330752K, used 139942K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 42% used [0x00000000c0000000,0x00000000c88a9910,0x00000000d4300000)
 Metaspace       used 57215K, capacity 58620K, committed 59008K, reserved 1099776K
  class space    used 7308K, capacity 7677K, committed 7808K, reserved 1048576K
Event: 12807.994 GC heap after
Heap after GC invocations=71 (full 4):
 PSYoungGen      total 315392K, used 448K [0x00000000eab00000, 0x0000000100000000, 0x0000000100000000)
  eden space 281600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000fbe00000)
  from space 33792K, 1% used [0x00000000fbe00000,0x00000000fbe70000,0x00000000fdf00000)
  to   space 33792K, 0% used [0x00000000fdf00000,0x00000000fdf00000,0x0000000100000000)
 ParOldGen       total 330752K, used 152011K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 45% used [0x00000000c0000000,0x00000000c9472f50,0x00000000d4300000)
 Metaspace       used 57215K, capacity 58620K, committed 59008K, reserved 1099776K
  class space    used 7308K, capacity 7677K, committed 7808K, reserved 1048576K
}
Event: 12816.130 GC heap before
{Heap before GC invocations=72 (full 4):
 PSYoungGen      total 315392K, used 282048K [0x00000000eab00000, 0x0000000100000000, 0x0000000100000000)
  eden space 281600K, 100% used [0x00000000eab00000,0x00000000fbe00000,0x00000000fbe00000)
  from space 33792K, 1% used [0x00000000fbe00000,0x00000000fbe70000,0x00000000fdf00000)
  to   space 33792K, 0% used [0x00000000fdf00000,0x00000000fdf00000,0x0000000100000000)
 ParOldGen       total 330752K, used 152011K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 45% used [0x00000000c0000000,0x00000000c9472f50,0x00000000d4300000)
 Metaspace       used 60821K, capacity 62268K, committed 62336K, reserved 1103872K
  class space    used 7668K, capacity 8035K, committed 8064K, reserved 1048576K
Event: 12816.187 GC heap after
Heap after GC invocations=72 (full 4):
 PSYoungGen      total 309248K, used 25995K [0x00000000eab00000, 0x0000000100000000, 0x0000000100000000)
  eden space 275456K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000fb800000)
  from space 33792K, 76% used [0x00000000fdf00000,0x00000000ff862f48,0x0000000100000000)
  to   space 36864K, 0% used [0x00000000fb800000,0x00000000fb800000,0x00000000fdc00000)
 ParOldGen       total 330752K, used 152115K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 45% used [0x00000000c0000000,0x00000000c948cf50,0x00000000d4300000)
 Metaspace       used 60821K, capacity 62268K, committed 62336K, reserved 1103872K
  class space    used 7668K, capacity 8035K, committed 8064K, reserved 1048576K
}

Deoptimization events (10 events):
Event: 12831.535 Thread 0x000000001bafd000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000000004c1cdfc method=com.google.common.collect.MapMakerInternalMap$Segment.copyEntry(Lcom/google/common/collect/MapMakerInternalMap$ReferenceEntry;Lcom/google/common/collect/MapMakerInternal
Event: 12831.563 Thread 0x000000001bafd000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000000004ab3bd0 method=java.lang.reflect.AccessibleObject.setAccessible0(Ljava/lang/reflect/AccessibleObject;Z)V @ 23
Event: 12831.748 Thread 0x000000001bafd000 Uncommon trap: reason=bimorphic action=maybe_recompile pc=0x0000000004f24744 method=java.io.FilterInputStream.available()I @ 4
Event: 12831.748 Thread 0x000000001bafd000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000000045e2e10 method=java.lang.CharacterDataLatin1.isJavaIdentifierPart(I)Z @ 11
Event: 12831.748 Thread 0x000000001bafd000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000000045e1bbc method=java.lang.CharacterDataLatin1.isJavaIdentifierPart(I)Z @ 11
Event: 12831.751 Thread 0x000000001bafd000 Uncommon trap: reason=bimorphic action=maybe_recompile pc=0x0000000004f24744 method=java.io.FilterInputStream.available()I @ 4
Event: 12831.754 Thread 0x000000001bafd000 Uncommon trap: reason=bimorphic action=maybe_recompile pc=0x0000000004f24744 method=java.io.FilterInputStream.available()I @ 4
Event: 12831.792 Thread 0x000000001bafd000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000000003adae6c method=groovy.lang.MetaClassImpl$1MOPIter.methodNameAction(Ljava/lang/Class;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;)V @ 354
Event: 12831.802 Thread 0x000000001bafd000 Uncommon trap: reason=predicate action=maybe_recompile pc=0x00000000043670dc method=java.util.Hashtable.putAll(Ljava/util/Map;)V @ 21
Event: 12831.858 Thread 0x000000001bafd000 Uncommon trap: reason=bimorphic action=maybe_recompile pc=0x0000000004f24744 method=java.io.FilterInputStream.available()I @ 4

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 12831.835 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': org/jetbrains/plugins/gradle/tooling/builder/ProjectExtensionsDataBuilderImplBeanInfo> (0x00000000f67ffff8) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\c
Event: 12831.836 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': org/jetbrains/plugins/gradle/tooling/builder/ProjectExtensionsDataBuilderImplCustomizer> (0x00000000f680bfb8) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm
Event: 12831.842 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': org/jetbrains/plugins/gradle/tooling/builder/IntelliJSettingsBuilderBeanInfo> (0x00000000f6851990) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\classfile\
Event: 12831.843 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': org/jetbrains/plugins/gradle/tooling/builder/IntelliJSettingsBuilderCustomizer> (0x00000000f685d270) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\classfil
Event: 12831.849 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': org/jetbrains/plugins/gradle/tooling/builder/IntelliJProjectSettingsBuilderBeanInfo> (0x00000000f68a3a60) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\cla
Event: 12831.850 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': org/jetbrains/plugins/gradle/tooling/builder/IntelliJProjectSettingsBuilderCustomizer> (0x00000000f68af8a0) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\c
Event: 12831.922 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': org/gradle/util/GradleVersionBeanInfo> (0x00000000f6a8f2c8) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\classfile\systemDictionary.cpp, line 210]
Event: 12831.923 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': org/gradle/util/GradleVersionCustomizer> (0x00000000f6ab4210) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\classfile\systemDictionary.cpp, line 210]
Event: 12831.993 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': settings_4pw89totfwdc31r51ir8fopsjBeanInfo> (0x00000000f6b64ed0) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\classfile\systemDictionary.cpp, line 210]
Event: 12831.994 Thread 0x000000001bafd000 Exception <a 'java/lang/ClassNotFoundException': settings_4pw89totfwdc31r51ir8fopsjCustomizer> (0x00000000f6b704f0) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u251\737\hotspot\src\share\vm\classfile\systemDictionary.cpp, line 210]

Events (10 events):
Event: 12831.987 loading class groovy/runtime/metaclass/settings_4pw89totfwdc31r51ir8fopsjMetaClass
Event: 12831.987 loading class groovy/runtime/metaclass/settings_4pw89totfwdc31r51ir8fopsjMetaClass done
Event: 12831.992 loading class settings_4pw89totfwdc31r51ir8fopsjBeanInfo
Event: 12831.992 loading class settings_4pw89totfwdc31r51ir8fopsjBeanInfo done
Event: 12831.993 loading class settings_4pw89totfwdc31r51ir8fopsjBeanInfo
Event: 12831.993 loading class settings_4pw89totfwdc31r51ir8fopsjBeanInfo done
Event: 12831.993 loading class settings_4pw89totfwdc31r51ir8fopsjCustomizer
Event: 12831.993 loading class settings_4pw89totfwdc31r51ir8fopsjCustomizer done
Event: 12831.994 loading class settings_4pw89totfwdc31r51ir8fopsjCustomizer
Event: 12831.994 loading class settings_4pw89totfwdc31r51ir8fopsjCustomizer done


Dynamic libraries:
0x00007ff755930000 - 0x00007ff755967000 	D:\Java\jdk1.8.0_251\bin\java.exe
0x00007ffa7f860000 - 0x00007ffa7fa69000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa7e900000 - 0x00007ffa7e9be000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa7ccf0000 - 0x00007ffa7d074000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa7e470000 - 0x00007ffa7e51f000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa7ecd0000 - 0x00007ffa7ed73000 	C:\Windows\System32\msvcrt.dll
0x00007ffa7e350000 - 0x00007ffa7e3ee000 	C:\Windows\System32\sechost.dll
0x00007ffa7e9c0000 - 0x00007ffa7eae1000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa7f060000 - 0x00007ffa7f20d000 	C:\Windows\System32\USER32.dll
0x00007ffa7d080000 - 0x00007ffa7d0a6000 	C:\Windows\System32\win32u.dll
0x00007ffa7e520000 - 0x00007ffa7e54a000 	C:\Windows\System32\GDI32.dll
0x00007ffa7d4f0000 - 0x00007ffa7d60f000 	C:\Windows\System32\gdi32full.dll
0x00007ffa7d610000 - 0x00007ffa7d6ad000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa7d120000 - 0x00007ffa7d231000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa6a5c0000 - 0x00007ffa6a865000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffa7e5e0000 - 0x00007ffa7e611000 	C:\Windows\System32\IMM32.DLL
0x0000000061630000 - 0x0000000061702000 	D:\Java\jdk1.8.0_251\jre\bin\msvcr100.dll
0x0000000060d80000 - 0x000000006162b000 	D:\Java\jdk1.8.0_251\jre\bin\server\jvm.dll
0x00007ffa7ed90000 - 0x00007ffa7ed98000 	C:\Windows\System32\PSAPI.DLL
0x00007ffa6ba70000 - 0x00007ffa6ba79000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffa76a00000 - 0x00007ffa76a33000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa74570000 - 0x00007ffa7457a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa7f330000 - 0x00007ffa7f39f000 	C:\Windows\System32\WS2_32.dll
0x00007ffa789b0000 - 0x00007ffa789bf000 	D:\Java\jdk1.8.0_251\jre\bin\verify.dll
0x00007ffa76a40000 - 0x00007ffa76a69000 	D:\Java\jdk1.8.0_251\jre\bin\java.dll
0x00007ffa755a0000 - 0x00007ffa755b6000 	D:\Java\jdk1.8.0_251\jre\bin\zip.dll
0x00007ffa7d6b0000 - 0x00007ffa7de75000 	C:\Windows\System32\SHELL32.dll
0x00007ffa7add0000 - 0x00007ffa7b632000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa7dfd0000 - 0x00007ffa7e346000 	C:\Windows\System32\combase.dll
0x00007ffa7ac60000 - 0x00007ffa7adc7000 	C:\Windows\SYSTEM32\wintypes.dll
0x00007ffa7de80000 - 0x00007ffa7df6a000 	C:\Windows\System32\SHCORE.dll
0x00007ffa7e3f0000 - 0x00007ffa7e44d000 	C:\Windows\System32\shlwapi.dll
0x00007ffa7cc20000 - 0x00007ffa7cc45000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa751b0000 - 0x00007ffa751ca000 	D:\Java\jdk1.8.0_251\jre\bin\net.dll
0x00007ffa7c300000 - 0x00007ffa7c367000 	C:\Windows\system32\mswsock.dll
0x00007ffa6eea0000 - 0x00007ffa6eeb2000 	D:\Java\jdk1.8.0_251\jre\bin\nio.dll
0x00007ffa76c00000 - 0x00007ffa76c1e000 	D:\develop\gradle\gradle-4.10.3\native\25\windows-amd64\native-platform.dll
0x00007ffa78960000 - 0x00007ffa7896d000 	D:\Java\jdk1.8.0_251\jre\bin\management.dll
0x00007ffa7c540000 - 0x00007ffa7c558000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa7bdb0000 - 0x00007ffa7bde5000 	C:\Windows\system32\rsaenh.dll
0x00007ffa7c3f0000 - 0x00007ffa7c41c000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffa7c6c0000 - 0x00007ffa7c6e7000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffa7d3b0000 - 0x00007ffa7d42f000 	C:\Windows\System32\bcryptprimitives.dll
0x00007ffa7c560000 - 0x00007ffa7c56c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffa7b970000 - 0x00007ffa7b99d000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa7e8f0000 - 0x00007ffa7e8f9000 	C:\Windows\System32\NSI.dll
0x00007ffa769e0000 - 0x00007ffa769f9000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa77090000 - 0x00007ffa770ae000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffa7b9e0000 - 0x00007ffa7bac7000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffa6c7b0000 - 0x00007ffa6c7d4000 	D:\Java\jdk1.8.0_251\jre\bin\sunec.dll
0x00007ffa56bb0000 - 0x00007ffa56bc7000 	C:\Windows\system32\napinsp.dll
0x00007ffa56530000 - 0x00007ffa5654b000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffa55900000 - 0x00007ffa55912000 	C:\Windows\System32\winrnr.dll
0x00007ffa74770000 - 0x00007ffa74785000 	C:\Windows\system32\wshbth.dll
0x00007ffa558e0000 - 0x00007ffa558ff000 	C:\Windows\system32\nlansp_c.dll
0x00007ffa6f6f0000 - 0x00007ffa6f6fa000 	C:\Windows\System32\rasadhlp.dll
0x00007ffa742e0000 - 0x00007ffa74362000 	C:\Windows\System32\fwpuclnt.dll

VM Arguments:
jvm_args: -XX:+HeapDumpOnOutOfMemoryError -Xmx1024m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 4.10.3
java_class_path (initial): D:\develop\gradle\gradle-4.10.3\lib\gradle-launcher-4.10.3.jar
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=D:\Java\jdk1.8.0_251
PATH=D:\develop\nvm-nodejs\nvm;C:\Program Files\nodejs;D:\Java\jdk1.8.0_251\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;D:\develop\mysql-8.0.42-winx64\bin;D:\maven\soft_tool\maven\apache-maven-3.6.3\bin;D:\develop\Git\cmd;D:\develop\wechat_develop_tools\dll;D:\develop\ffmpeg;C:\Program Files\dotnet\;C:\Program Files\Docker\Docker\resources\bin;D:\develop\;D:\develop\nodejs\node_global;D:\develop\gradle\gradle-4.10.3\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\develop\IDEA2024.1.4\IntelliJ IDEA 2024.1.4\bin;;D:\VScode\Microsoft VS Code\bin;D:\develop\nvm-nodejs\nvm;C:\Program Files\nodejs;D:\develop\nodejs\node_global;D:\develop\cursor\resources\app\bin
USERNAME=��־��
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD



---------------  S Y S T E M  ---------------

OS: Windows 10.0 , 64 bit Build 22000 (10.0.22000.2124)

CPU:total 12 (initial active 12) (12 cores per cpu, 1 threads per core) family 25 model 80 stepping 0, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, mmxext, 3dnowpref, lzcnt, sse4a, tsc, tscinvbit, tscinv, bmi1

Memory: 4k page, physical 14539512k(1663832k free), swap 40753908k(1204k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (25.251-b08) for windows-amd64 JRE (1.8.0_251-b08), built on Mar 12 2020 06:31:49 by "" with MS VC++ 10.0 (VS2010)

time: Wed Jul 16 18:15:13 2025
timezone: �й���׼ʱ��
elapsed time: 12832 seconds (0d 3h 33m 52s)

