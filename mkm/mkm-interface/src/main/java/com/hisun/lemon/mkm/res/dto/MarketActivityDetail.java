package com.hisun.lemon.mkm.res.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created by chen on 7/20 0020.
 */
public  class MarketActivityDetail  {

    /**
     * @Fields id 活动id
     */
    @ApiModelProperty(name = "id", value = "活动编号", required = true , dataType = "String")
    private String id;
    /**
     * @Fields atvNm 活动名称
     */
    @ApiModelProperty(name = "atvNm", value = "活动名称", required = true , dataType = "String")
    private String atvNm;
    /**
     * @Fields mkTool 营销工具 01-电子券，02-海币 03- 优惠券 04-折扣券
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具 01-电子券，02-海币 03- 优惠券 04-折扣券", required = true , dataType = "String")
    private String mkTool;
    /**
     * @Fields status 状态 01-未审核 ，02-已审核
     */
    @ApiModelProperty(name = "status", value = "状态 01-未审核 ，02-已审核", required = true , dataType = "String")
    private String status;
    /**
     * @Fields total 发放总量
     */
    @ApiModelProperty(name = "total", value = "发放总量", required = true , dataType = "Integer")
    private Integer total;
    /**
     * @Fields totalAmt 发放总金额
     */
    @ApiModelProperty(name = "totalAmt", value = "发放总金额", required = true , dataType = "BigDecimal")
    private BigDecimal totalAmt;
    /**
     * @Fields receiveTimes 单用户领取次数
     */
    @ApiModelProperty(name = "receiveTimes", value = "单用户领取次数", required = true , dataType = "Integer")
    private Integer receiveTimes;
    /**
     * @Fields amt 单券金额
     */
    @ApiModelProperty(name = "amt", value = "单券金额", required = true , dataType = "BigDecimal")
    private BigDecimal amt;
    /**
     * @Fields beginTime 活动开始时间
     */
    @ApiModelProperty(name = "beginTime", value = "活动开始时间", required = true , dataType = "LocalDateTime")
    private LocalDateTime beginTime;
    /**
     * @Fields endTime 活动结束时间
     */
    @ApiModelProperty(name = "endTime", value = "活动结束时间", required = true , dataType = "LocalDateTime")
    private LocalDateTime endTime;
    /**
     * @Fields remainNum 剩余发放总数
     */
    @ApiModelProperty(name = "remainNum", value = "剩余发放总数", required = true , dataType = "Integer")
    private Integer remainNum;
    /**
     * @Fields remainAmt 剩余发放总金额
     */
    @ApiModelProperty(name = "remainAmt", value = "剩余发放总金额", required = true , dataType = "BigDecimal")
    private BigDecimal remainAmt;
    /**
     * @Fields couponValTm 有效日期（电子券）
     */
//    private LocalDateTime couponValTm;
//    /**
//     * @Fields couponInvalTm 失效日期(电子券)
//     */
    @ApiModelProperty(name = "couponInvalTm", value = "失效日期（非海币)", required = true , dataType = "LocalDateTime")
    private LocalDateTime couponInvalTm;
    /**
     * @Fields instId 商户id （电子券）
     */
    @ApiModelProperty(name = "instId", value = "商户id)", required = true , dataType = "String")
    private String instId;
    /**
     * @Fields minAmt 可使用订单最小金额(电子券)
     */
    @ApiModelProperty(name = "minAmt", value = "可使用订单最小金额(电子券)", required = true , dataType = "BigDecimal")
    private BigDecimal minAmt;
    /**
     * @Fields maxAmt 可使用订单最大金额(电子券)
     */
    @ApiModelProperty(name = "maxAmt", value = "可使用订单最大金额(电子券)", required = true , dataType = "BigDecimal")
    private BigDecimal maxAmt;
   // private String  item;

    /**
     * 累计消费金额
     */
    @ApiModelProperty(name = "acltAmt", value = "累计消费金额", required = true , dataType = "BigDecimal")
    private BigDecimal acltAmt;

    /**
     * 折扣
     */
    @ApiModelProperty(name = "discount", value = "折扣", required = true , dataType = "BigDecimal")
    private BigDecimal discount;

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }
    /**
     * 累计消费总数
     */
    @ApiModelProperty(name = "aclt", value = "累计消费总数", required = true , dataType = "Integer")
    private Integer aclt;

    @ApiModelProperty(name = "startDays", value = "领券后券别X天生效", required = true , dataType = "Integer")
    private Integer startDays;
    /**
     * @Fields couponValDays 券别有效天数
     */
    @ApiModelProperty(name = "couponValDays", value = "券别有效天数", required = true , dataType = "Integer")
    private Integer couponValDays;

    /**
     * @Fields receiveCycle 领取次数的周期 day 天 month 月 year 年
     */
    @ApiModelProperty(name = "receiveCycle", value = "领取次数的周期 day 天 month 月 year 年", required = true , dataType = "String")
    private String receiveCycle;

    @ApiModelProperty(name = "couponName", value = "券别名称", required = true , dataType = "String")
    private String couponName;

    @ApiModelProperty(name = "getFlag", value = "是否可领取标志 1-可以领取 0-不可以领取", required = true , dataType = "String")
    private String getFlag;

    public BigDecimal getAcltAmt() {
        return acltAmt;
    }

    public void setAcltAmt(BigDecimal acltAmt) {
        this.acltAmt = acltAmt;
    }

    public Integer getAclt() {
        return aclt;
    }

    public void setAclt(Integer aclt) {
        this.aclt = aclt;
    }

//    public String getItem() {
//        return item;
//    }
//
//    public void setItem(String item) {
//        this.item = item;
//    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAtvNm() {
        return atvNm;
    }

    public void setAtvNm(String atvNm) {
        this.atvNm = atvNm;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public Integer getReceiveTimes() {
        return receiveTimes;
    }

    public void setReceiveTimes(Integer receiveTimes) {
        this.receiveTimes = receiveTimes;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public LocalDateTime getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(LocalDateTime beginTime) {
        this.beginTime = beginTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getRemainNum() {
        return remainNum;
    }

    public void setRemainNum(Integer remainNum) {
        this.remainNum = remainNum;
    }

    public BigDecimal getRemainAmt() {
        return remainAmt;
    }

    public void setRemainAmt(BigDecimal remainAmt) {
        this.remainAmt = remainAmt;
    }

//    public LocalDateTime getCouponValTm() {
//        return couponValTm;
//    }
//
//    public void setCouponValTm(LocalDateTime couponValTm) {
//        this.couponValTm = couponValTm;
//    }

    public LocalDateTime getCouponInvalTm() {
        return couponInvalTm;
    }

    public void setCouponInvalTm(LocalDateTime couponInvalTm) {
        this.couponInvalTm = couponInvalTm;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public BigDecimal getMinAmt() {
        return minAmt;
    }

    public void setMinAmt(BigDecimal minAmt) {
        this.minAmt = minAmt;
    }

    public BigDecimal getMaxAmt() {
        return maxAmt;
    }

    public void setMaxAmt(BigDecimal maxAmt) {
        this.maxAmt = maxAmt;
    }

    public Integer getStartDays() {
        return startDays;
    }

    public void setStartDays(Integer startDays) {
        this.startDays = startDays;
    }

    public Integer getCouponValDays() {
        return couponValDays;
    }

    public void setCouponValDays(Integer couponValDays) {
        this.couponValDays = couponValDays;
    }

    public String getReceiveCycle() {
        return receiveCycle;
    }

    public void setReceiveCycle(String receiveCycle) {
        this.receiveCycle = receiveCycle;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getGetFlag() {
        return getFlag;
    }

    public void setGetFlag(String getFlag) {
        this.getFlag = getFlag;
    }
}