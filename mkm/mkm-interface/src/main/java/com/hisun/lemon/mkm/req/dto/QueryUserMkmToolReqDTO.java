package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;

/**
 * Created by chen on 7/27 0027.
 */
public class QueryUserMkmToolReqDTO {
    /**
     *  用户
     */
    @ApiModelProperty(name = "userId", value = "用户", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String userId;

    /**
     *  用户
     */
    @ApiModelProperty(name = "instId", value = "商户号", required = true , dataType = "String")
    @Length(max = 32)
    private String instId;

    /**
     * 获取日期
     */
    @ApiModelProperty(name = "releaseDt", value = "获取日期", required = false , dataType = "String")
    private String releaseDt;

    /**
     * 营销工具
     * 营销工具 01-非海币 02 -海币
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具 01-非海币 02 -海币 ", required = false , dataType = "String")
    private String mkTool;

    /**
     * 优惠券
     * 01-电子券  03-优惠券 04-折扣券
     */
    @ApiModelProperty(name = "couponType", value = "优惠券种类 01-电子券 03-优惠券 04-折扣券", required = false , dataType = "String")
    private String[] couponType;

    /**
     * Y-有效 和 N-失效
     */
    @ApiModelProperty(name = "val", value = "查询是否有效电子券 Y-有效 和 N-失效", required = false , dataType = "String")
    private String val ;


    /**
     * 订单金额
     */
    @ApiModelProperty(name = "orderAmt", value = "订单金额", required = false , dataType = "BigDecimal")
    private BigDecimal orderAmt ;

    private int pageNo;

    private int pageSize;
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(String releaseDt) {
        this.releaseDt = releaseDt;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getVal() {
        return val;
    }

    public void setVal(String val) {
        this.val = val;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String[] getCouponType() {
        return couponType;
    }

    public void setCouponType(String[] couponType) {
        this.couponType = couponType;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }
}
