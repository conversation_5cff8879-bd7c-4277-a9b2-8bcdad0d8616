package com.hisun.lemon.mkm.req.innerDto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.time.LocalDateTime;

/**
 * Created by chen on 7/22 0022.
 */
public class InnerRechargeMkmToolReqDTO {

    /**
     * 流水号
     */
    @ApiModelProperty(name = "seq", value = "流水号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String seq;

    /**
     *活动id 如果交易类型为01必输
     */
    @ApiModelProperty(name = "atvId", value = "活动id 如果交易类型为01必输", required = false , dataType = "String")
    @Length(max = 25)
    private String atvId;

    /**
     * 交易类型 00-充值 01-发放 02-核销
     */
    @ApiModelProperty(name = "type", value = "交易类型 00-充值 01-发放 02-核销", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 2)
    private String type;

    /**
     * 营销工具
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具 先传02-海币", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 22)
    private String mkTool;

    /**
     * 用户id
     */

    private String userId;

    /**
     * 用户手机号
     */
    @ApiModelProperty(name = "mobile", value = "用户手机号", required = false , dataType = "String")
    private String mobile;

    /**
     * 充值海币数量
     */
    @ApiModelProperty(name = "count", value = "充值海币数量", required = true , dataType = "Integer")
    private Integer count;

    /**
     * 充值时间
     */
    @ApiModelProperty(name = "rechargeTm", value = "充值时间", required = true , dataType = "LocalDateTime")
    @NotEmpty
    @Length(min = 14,max = 14)
    private String traceTime;

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getTraceTime() {
        return traceTime;
    }

    public void setTraceTime(String traceTime) {
        this.traceTime = traceTime;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
