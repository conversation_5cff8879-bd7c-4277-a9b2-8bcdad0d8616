package com.hisun.lemon.mkm.res.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @create 2017/7/25
 */
public class CouponUsageDetailsResDTO {

    private String mblNo;

    private LocalDate releaseDt;

    private LocalTime releaseTm;

    private String atvId;

    private BigDecimal couponAmt;

    private BigDecimal balance;

    private String orderNo;

    private BigDecimal orderAmt;

    private String instId;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public LocalDate getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(LocalDate releaseDt) {
        this.releaseDt = releaseDt;
    }

    public LocalTime getReleaseTm() {
        return releaseTm;
    }

    public void setReleaseTm(LocalTime releaseTm) {
        this.releaseTm = releaseTm;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    @Override
    public String toString() {
        return "CouponUsageDetailsResDTO{" +
                "mblNo='" + mblNo + '\'' +
                ", releaseDt=" + releaseDt +
                ", releaseTm=" + releaseTm +
                ", atvId='" + atvId + '\'' +
                ", couponAmt=" + couponAmt +
                ", balance=" + balance +
                ", orderNo='" + orderNo + '\'' +
                ", orderAmt=" + orderAmt +
                ", instId='" + instId + '\'' +
                '}';
    }
}
