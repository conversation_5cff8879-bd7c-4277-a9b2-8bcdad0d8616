package com.hisun.lemon.mkm.res.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 常见问题响应DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 20:20
 */
public class FAQManagerRspDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 常见问题列表
     */
    @ApiModelProperty(name = "faqList", value = "常见问题列表", dataType = "List")
    private List<FAQManagerItemDTO> faqList;

    /**
     * 总记录数
     */
    @ApiModelProperty(name = "totNum", value = "总记录数", dataType = "Integer")
    private Integer totNum;

    /**
     * 当前页码
     */
    @ApiModelProperty(name = "currPage", value = "当前页码", dataType = "Integer")
    private Integer currPage;

    public List<FAQManagerItemDTO> getFaqList() {
        return faqList;
    }

    public void setFaqList(List<FAQManagerItemDTO> faqList) {
        this.faqList = faqList;
    }

    public Integer getTotNum() {
        return totNum;
    }

    public void setTotNum(Integer totNum) {
        this.totNum = totNum;
    }

    public Integer getCurrPage() {
        return currPage;
    }

    public void setCurrPage(Integer currPage) {
        this.currPage = currPage;
    }

    /**
     * 常见问题项DTO
     */
    public static class FAQManagerItemDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 问题ID
         */
        @ApiModelProperty(name = "id", value = "问题ID", dataType = "Integer")
        private Integer id;

        /**
         * 问题内容
         */
        @ApiModelProperty(name = "questionContent", value = "问题内容", dataType = "String")
        private String questionContent;

        /**
         * 回答内容
         */
        @ApiModelProperty(name = "answerContent", value = "回答内容", dataType = "String")
        private String answerContent;

        /**
         * 创建时间
         */
        @ApiModelProperty(name = "createdDate", value = "创建时间", dataType = "Date")
        private Date createdDate;

        /**
         * 创建人
         */
        @ApiModelProperty(name = "createdBy", value = "创建人", dataType = "String")
        private String createdBy;

        /**
         * 更新时间
         */
        @ApiModelProperty(name = "updatedDate", value = "更新时间", dataType = "Date")
        private Date updatedDate;

        /**
         * 更新人
         */
        @ApiModelProperty(name = "updatedBy", value = "更新人", dataType = "String")
        private String updatedBy;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getQuestionContent() {
            return questionContent;
        }

        public void setQuestionContent(String questionContent) {
            this.questionContent = questionContent;
        }

        public String getAnswerContent() {
            return answerContent;
        }

        public void setAnswerContent(String answerContent) {
            this.answerContent = answerContent;
        }

        public Date getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(Date createdDate) {
            this.createdDate = createdDate;
        }

        public String getCreatedBy() {
            return createdBy;
        }

        public void setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
        }

        public Date getUpdatedDate() {
            return updatedDate;
        }

        public void setUpdatedDate(Date updatedDate) {
            this.updatedDate = updatedDate;
        }

        public String getUpdatedBy() {
            return updatedBy;
        }

        public void setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
        }
    }
} 