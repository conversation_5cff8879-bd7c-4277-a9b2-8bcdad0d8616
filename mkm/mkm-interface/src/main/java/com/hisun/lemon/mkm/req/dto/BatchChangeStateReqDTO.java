package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.web.multipart.MultipartFile;

/**
 * Created by chen on 8/3 0003.
 */
public class BatchChangeStateReqDTO {
    @NotEmpty
    @Length(max = 25)
    @ApiModelProperty(name = "atvId", value = "活动id", required = true, dataType = "String")
    private String atvId;
    @ApiModelProperty(name = "upLoadFile", value = "批量文件", required = true, dataType = "MultipartFile")
    @NotBlank
    private MultipartFile upLoadFile;

    @ApiModelProperty(name = "oprTyp", value = "操作类型，0：过期；1：延期，2：冻结，3：解冻", required = true, dataType = "String")
    @NotEmpty
    private String oprTyp;

    @ApiModelProperty(name = "oprId", value = "操作员id", required = true, dataType = "String")
    private String oprId;

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public MultipartFile getUpLoadFile() {
        return upLoadFile;
    }

    public void setUpLoadFile(MultipartFile upLoadFile) {
        this.upLoadFile = upLoadFile;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }
}
