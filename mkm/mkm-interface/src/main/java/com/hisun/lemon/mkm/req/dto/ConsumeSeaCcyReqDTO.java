package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created by chen on 7/24 0024.
 */
public class ConsumeSeaCcyReqDTO {
    /**
     * 流水号
     */
    @ApiModelProperty(name = "seq", value = "流水号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String seq;


    /**
     * 交易类型 00-充值 01-发放 02-核销
     */
    @ApiModelProperty(name = "type", value = "交易类型 00-充值 01-发放 02-核销", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 02)
    private String type;

    /**
     * 工具
     */
    @NotEmpty
    @Length(max = 2)
    @ApiModelProperty(name = "mkTool", value = "工具", required = true , dataType = "String")

    private String mkTool;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String userId;

    /**
     * 用户手机号
     */
    @ApiModelProperty(name = "mobile", value = "用户手机号", required = false , dataType = "String")
    private String mobile;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderNo", value = "订单号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String orderNo;
    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderAmt", value = "订单金额", required = true , dataType = "BigDecimal")
    private BigDecimal orderAmt;

    /**
     * 商户号
     */
    @ApiModelProperty(name = "instId", value = "商户号", required = true , dataType = "String")
    private String instId;
    /**
     * 充值海币数量
     */
    @ApiModelProperty(name = "count", value = "充值海币数量", required = true , dataType = "Integer")
    private Integer count;

    /**
     * 消费时间
     */
    @ApiModelProperty(name = "consumeTm", value = "消费时间", required = true , dataType = "LocalDateTime")

    private LocalDateTime consumeTm;

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public LocalDateTime getConsumeTm() {
        return consumeTm;
    }

    public void setConsumeTm(LocalDateTime consumeTm) {
        this.consumeTm = consumeTm;
    }
    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }
}
