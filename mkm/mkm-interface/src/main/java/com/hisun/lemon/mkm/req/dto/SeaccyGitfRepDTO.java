package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.time.LocalDateTime;

/**
 * Created by chen on 8/22 0022.
 */
public class SeaccyGitfRepDTO {
    /**
     * 流水号
     */
    @ApiModelProperty(name = "seq", value = "流水号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String seq;

    /**
     * 赠送用户
     */
    @ApiModelProperty(name = "userId", value = "用户id", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String userId;

    @ApiModelProperty(name = "mobile", value = "手机号", required = true , dataType = "String")
    private String mobile;

    /**
     * 被赠送用户
     */
    @ApiModelProperty(name = "gitfUser", value = "被赠送用户", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String gitfUser;

    /**
     * 被转赠用户手机号
     */
    @ApiModelProperty(name = "gMobile", value = "被转赠用户手机号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 20)
    private String gMobile;

    /**
     * 被赠送用户
     */
    @ApiModelProperty(name = "count", value = "赠送数量", required = true , dataType = "Integer")
    private Integer count;

    private LocalDateTime traceTime;

    public LocalDateTime getTraceTime() {
        return traceTime;
    }

    public void setTraceTime(LocalDateTime traceTime) {
        this.traceTime = traceTime;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getGitfUser() {
        return gitfUser;
    }

    public void setGitfUser(String gitfUser) {
        this.gitfUser = gitfUser;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getgMobile() {
        return gMobile;
    }

    public void setgMobile(String gMobile) {
        this.gMobile = gMobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
