package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created by chen on 7/19 0019.
 */
public class AddMarketActivityReqDTO implements Serializable {

    /**
     * @Fields atvNm 活动名称
     */
    @ApiModelProperty(name = "atvNm", value = "活动名称", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 100,min = 0)
    private String atvNm;
    /**
     * @Fields mkTool 营销工具 01-电子券，02-海币
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具 01-电子券，02-海币 03-优惠券", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 2)
    private String mkTool;
    /**
     * @Fields 活动总量
     */
    @ApiModelProperty(name = "total", value = "活动总量", required = true , dataType = "Integer")
    private Integer total;
    /**
     * @Fields totalAmt 发放总金额
     */
    @ApiModelProperty(name = "totalAmt", value = "发放总金额", required = true , dataType = "BigDecimal")
    private BigDecimal totalAmt;
    /**
     * @Fields receiveTimes 单用户领取次数
     */
    @ApiModelProperty(name = "receiveTimes", value = "单用户领取次数", required = true , dataType = "Integer")
    private Integer receiveTimes;
    /**
     * @Fields amt 单券金额
     */
    @ApiModelProperty(name = "amt", value = "单券金额", required = false , dataType = "BigDecimal")
    private BigDecimal amt;
    /**
     * @Fields beginTime 活动开始时间
     */
    @ApiModelProperty(name = "beginTime", value = "活动开始时间", required = true , dataType = "String")
    @NotBlank
    private String beginTime;
    /**
     * @Fields endTime 活动结束时间
     */
    @NotBlank
    @ApiModelProperty(name = "endTime", value = "活动结束时间", required = true , dataType = "String")
    private String endTime;

    /**
     * @Fields couponValTm 有效日期（电子券）
     */
    @ApiModelProperty(name = "couponValTm", value = "有效日期（电子券）", required = false , dataType = "String")
    private String couponValTm;
    /**
     * @Fields couponInvalTm 失效日期(电子券)
     */
    @ApiModelProperty(name = "couponInvalTm", value = "失效日期(电子券)", required = false , dataType = "String")
    private String couponInvalTm;
    /**
     * @Fields instId 商户id （电子券）
     */
    @ApiModelProperty(name = "instId", value = "商户id(电子券) 多商户号用,隔开", required = false , dataType = "String")
    private String instId;
    /**
     * @Fields minAmt 可使用订单最小金额(电子券)
     */
        @ApiModelProperty(name = "minAmt", value = "可使用订单最小金额", required = false , dataType = "BigDecimal")
    private BigDecimal minAmt;
    /**
     * @Fields maxAmt 可使用订单最大金额(电子券)
     */
    @ApiModelProperty(name = "maxAmt", value = "可使用订单最大金额", required = false , dataType = "BigDecimal")
    private BigDecimal maxAmt;

    /**
     * 科目号
     */
    @NotBlank
    @Length(max = 30)
    private String item;
    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * @Fields receiveCycle 领取次数的周期 day 天 month 月 year 年
     */
    private String receiveCycle;

    /**
     * @Fields userScope 用户范围
     */
    private String userScope;
    /**
     * @Fields startDays 领用后开始生效的天数
     */
    private Integer startDays;
    /**
     * @Fields couponValDays 券别有效天数
     */
    private Integer couponValDays;

    /**
     * 费用承担方
     */
    private String costSide;

    /**
     * 券别名称
     */
    private String couponName;

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public String getItem() {
        return item;
    }

    /**
     *
     * 创建操作人员
     */
    private String crtUserOpr;

    public String getCrtUserOpr() {
        return crtUserOpr;
    }

    public void setCrtUserOpr(String crtUserOpr) {
        this.crtUserOpr = crtUserOpr;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public String getAtvNm() {
        return atvNm;
    }

    public void setAtvNm(String atvNm) {
        this.atvNm = atvNm;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public Integer getReceiveTimes() {
        return receiveTimes;
    }

    public void setReceiveTimes(Integer receiveTimes) {
        this.receiveTimes = receiveTimes;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getCouponValTm() {
        return couponValTm;
    }

    public void setCouponValTm(String couponValTm) {
        this.couponValTm = couponValTm;
    }

    public String getCouponInvalTm() {
        return couponInvalTm;
    }

    public void setCouponInvalTm(String couponInvalTm) {
        this.couponInvalTm = couponInvalTm;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public BigDecimal getMinAmt() {
        return minAmt;
    }

    public void setMinAmt(BigDecimal minAmt) {
        this.minAmt = minAmt;
    }

    public BigDecimal getMaxAmt() {
        return maxAmt;
    }

    public void setMaxAmt(BigDecimal maxAmt) {
        this.maxAmt = maxAmt;
    }

    public String getReceiveCycle() {
        return receiveCycle;
    }

    public void setReceiveCycle(String receiveCycle) {
        this.receiveCycle = receiveCycle;
    }

    public String getUserScope() {
        return userScope;
    }

    public void setUserScope(String userScope) {
        this.userScope = userScope;
    }

    public Integer getStartDays() {
        return startDays;
    }

    public void setStartDays(Integer startDays) {
        this.startDays = startDays;
    }

    public Integer getCouponValDays() {
        return couponValDays;
    }

    public void setCouponValDays(Integer couponValDays) {
        this.couponValDays = couponValDays;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getCostSide() {
        return costSide;
    }

    public void setCostSide(String costSide) {
        this.costSide = costSide;
    }
}
