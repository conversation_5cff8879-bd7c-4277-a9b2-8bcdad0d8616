package com.hisun.lemon.mkm.req.innerDto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.time.LocalDateTime;

/**
 * Created by chen on 8/29 0029.
 */
public class InSeaccyGitfRepDTO {
    /**
     * 流水号
     */
    @ApiModelProperty(name = "seq", value = "流水号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String seq;

    /**
     * 支付密码
     */
    @ApiModelProperty(name = "pwd", value = "支付密码", required = true , dataType = "String")
    @NotEmpty
    private String pwd;

    /**
     * 赠送用户
     */
    @ApiModelProperty(name = "mobile", value = "用户手机号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 20)
    private String mobile;



    /**
     * 被转赠用户手机号
     */
    @ApiModelProperty(name = "gMobile", value = "被转赠用户手机号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 20)
    private String gMobile;

    /**
     * 被赠送用户
     */
    @ApiModelProperty(name = "count", value = "赠送数量", required = true , dataType = "Integer")
    private Integer count;

    /**
     * 被赠送用户
     */
    @ApiModelProperty(name = "payPwdRandom", value = "支付密码随机数", required = true , dataType = "String")
    private String payPwdRandom;

    @NotEmpty
    @Length(min = 14,max = 14)
    private String traceTime;

    /**
     * 被赠送用户
     */
    @ApiModelProperty(name = "seaRandom", value = "密码随机数", required = true , dataType = "String")
    private String seaRandom;

    public String getSeaRandom() {
        return seaRandom;
    }

    public void setSeaRandom(String seaRandom) {
        this.seaRandom = seaRandom;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTraceTime() {
        return traceTime;
    }

    public void setTraceTime(String traceTime) {
        this.traceTime = traceTime;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }


    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getgMobile() {
        return gMobile;
    }

    public void setgMobile(String gMobile) {
        this.gMobile = gMobile;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getPayPwdRandom() {
        return payPwdRandom;
    }

    public void setPayPwdRandom(String payPwdRandom) {
        this.payPwdRandom = payPwdRandom;
    }
}
