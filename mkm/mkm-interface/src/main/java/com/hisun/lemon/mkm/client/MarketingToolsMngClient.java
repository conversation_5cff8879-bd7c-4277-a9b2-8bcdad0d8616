package com.hisun.lemon.mkm.client;

import com.hisun.lemon.framework.annotation.LemonBody;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.mkm.req.dto.BatchChangeStateReqDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2017/7/24
 */
@FeignClient("MKM")
public interface MarketingToolsMngClient {

//    @GetMapping("/mkm/toolsmng/requisition/{mblNo}")
//    GenericRspDTO queryToolRequisitionDetails(@PathVariable String mblNo, @RequestParam LocalDate begDt,
//                                              @RequestParam(defaultValue = "0") int pageNum,
//                                              @RequestParam(defaultValue = "5") int pageSize);
//
//    @GetMapping("/mkm/toolsmng/usage/{mblNo}")
//    GenericRspDTO queryToolUsageDetails(@PathVariable String mblNo, @RequestParam LocalDate begDt,
//                                     @RequestParam(defaultValue = "0") int pageNum,
//                                     @RequestParam(defaultValue = "5") int pageSize);
//
//    @PostMapping("/mkm/toolsmng/batch/status")
//    public GenericRspDTO<NoBody> batchChangeState(GenericDTO<BatchChangeStateReqDTO> reqDTO);
//
//    @GetMapping("/mkm/toolsmng/batch")
//    GenericRspDTO queryBatchList(@RequestParam String oprTyp,
//                              @RequestParam(defaultValue = "0") int pageNum,
//                              @RequestParam(defaultValue = "5") int pageSize);
//
//    @GetMapping("/mkm/toolsmng/batch/retfile/{recNo}")
//    GenericRspDTO downloadBatchFile(@PathVariable String recNo);

    @PostMapping("/mkm/toolsmng/batch" )
    public GenericRspDTO<NoBody> batchChangeState(@LemonBody GenericDTO<NoBody> genericDTO,
                                                  @RequestParam("fileNm") String fileNm,
                                                  @RequestParam("oprTyp") String oprTyp);
}
