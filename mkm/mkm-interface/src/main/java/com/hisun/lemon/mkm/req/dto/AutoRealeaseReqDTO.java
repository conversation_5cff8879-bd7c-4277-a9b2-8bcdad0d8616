package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created by chen on 8/10 0010.
 */
public class AutoRealeaseReqDTO {

    /**
     * 流水号
     */
    @ApiModelProperty(name = "seq", value = "流水号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String seq;

    /**
     * 营销工具
     */
    @ApiModelProperty(name = "mkTool", value = "别类型 01-电子券 03-优惠券 04-折扣券", required = true , dataType = "String")
    private String mkTool;

    /**
     * 商户编号
     */
    @ApiModelProperty(name = "instId", value = "商户编号", required = false , dataType = "String")

    private String instId;


    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", required = true , dataType = "String")
    private String userId;
    /**
     * 用户id
     */
    @ApiModelProperty(name = "mobile", value = "用户手机号", required = false , dataType = "String")
    private String mobile;

    /**
     * 订单编号
     */
    @ApiModelProperty(name = "orderNo", value = "订单编号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String orderNo;

    /**
     * 订单金额
     */
    @ApiModelProperty(name = "orderAmt", value = "订单金额", required = true , dataType = "BigDecimal")
    private BigDecimal orderAmt;

    @ApiModelProperty(name = "traceTime", value = "交易时间", required = true , dataType = "LocalDateTime")
    private LocalDateTime traceTime;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public LocalDateTime getTraceTime() {
        return traceTime;
    }

    public void setTraceTime(LocalDateTime traceTime) {
        this.traceTime = traceTime;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }
}
