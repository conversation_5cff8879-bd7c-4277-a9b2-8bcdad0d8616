package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * Created by chen on 7/20 0020.
 */
public class MaintainActivityReqDTO {
    /**
     * 营销活动id
     */
    @ApiModelProperty(name = "id", value = "营销活动id", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String id ;
    /**
     * 营销活动状态
     */
    @ApiModelProperty(name = "status", value = "营销活动状态", required = false , dataType = "String")
    private String status ;
    /**
     * 营销活动审核状态
     */
    @ApiModelProperty(name = "examineStatus", value = "营销活动审核状态 01-未审核 02-已审核", required = false , dataType = "String")
    private String examineStatus ;
    /**
     * 营销活动维护类型 01-审核 02-维护
     */
    @ApiModelProperty(name = "type", value = "营销活动维护类型 01-审核 02-维护", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 2)
    private String type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(String examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
