package com.hisun.lemon.mkm.res.dto;

import java.util.List;

/**
 * Created by chen on 7/27 0027.
 */
public class QueryUserMkmToolRspDTO {
    private SeaCcyDetail seaCcyDetal;

    private List<CouponDetail> couponDetail;

    private int total ;

    private int page ;

    private int pageSize;


    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public SeaCcyDetail getSeaCcyDetal() {
        return seaCcyDetal;
    }

    public void setSeaCcyDetal(SeaCcyDetail seaCcyDetal) {
        this.seaCcyDetal = seaCcyDetal;
    }

    public List<CouponDetail> getCouponDetail() {
        return couponDetail;
    }

    public void setCouponDetail(List<CouponDetail> couponDetail) {
        this.couponDetail = couponDetail;
    }
}
