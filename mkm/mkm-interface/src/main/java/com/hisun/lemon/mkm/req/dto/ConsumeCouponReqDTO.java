package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created by chen on 7/24 0024.
 */
public class ConsumeCouponReqDTO {

    /**
     * 流水号
     */
    @ApiModelProperty(name = "seq", value = "流水号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String seq;

    /**
     * 券别编号
     */

    @ApiModelProperty(name = "couponNo", value = "券别编号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String couponNo;

    /**
     * 营销工具类型
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具类型 01-电子券 03-优惠券", required = true , dataType = "String")
    private String mkTool;

    /**
     * 交易种类 02-消费
     */
    @ApiModelProperty(name = "type", value = "交易类型 02-消费", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 02)
    private String type;

    /**
     * 用户
     */
    @ApiModelProperty(name = "userId", value = "用户", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String userId;

    /**
     * 订单编号
     */
    @ApiModelProperty(name = "orderNo", value = "订单编号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String orderNo;

    /**
     * 订单金额
     */
    @ApiModelProperty(name = "orderAmt", value = "订单金额", required = true , dataType = "BigDecimal")
    private BigDecimal orderAmt;

    /**
     * 时间
     */
    @ApiModelProperty(name = "consumeTm", value = "时间", required = true , dataType = "LocalDateTime")
    private LocalDateTime consumeTm;

    /**
     * 商户编号
     */
    @ApiModelProperty(name = "instId", value = "商户编号", required = false , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String instId;

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getCouponNo() {
        return couponNo;
    }

    public void setCouponNo(String couponNo) {
        this.couponNo = couponNo;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public LocalDateTime getConsumeTm() {
        return consumeTm;
    }

    public void setConsumeTm(LocalDateTime consumeTm) {
        this.consumeTm = consumeTm;
    }
}
