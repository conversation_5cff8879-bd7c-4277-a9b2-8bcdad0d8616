package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created by chen on 7/22 0022.
 */
public class GetConponReqDTO {

    /**
     * 流水号
     */
    private String seq;
    /**
     *活动id 如果交易类型为01必输
     */
    @ApiModelProperty(name = "atvId", value = "活动id", required = false , dataType = "String")
    @NotEmpty
    @Length(max = 25)
    private String atvId;

    /**
     * 交易类型 00-充值 01-发放 02-核销
     */
    @ApiModelProperty(name = "type", value = "交易类型  01-发放 ", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 2)
    private String type;

    /**
     * 商户编号
     */
    @ApiModelProperty(name = "instId", value = "商户编号", required = false , dataType = "String")

    private String instId;

    /**
     * 营销工具
     */
    @ApiModelProperty(name = "mkTool", value = "别类型 01-电子券 03-优惠券 04-折扣券", required = true , dataType = "String")
    private String mkTool;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", required = true , dataType = "String")
    private String userId;

    /**
     * 用户手机号
     */
    @ApiModelProperty(name = "mobile", value = "用户手机号", required = false , dataType = "String")
    private String mobile;

    /**
     * 金额
     */
    @ApiModelProperty(name = "amt", value = "金额", required = true , dataType = "BigDecimal")
    private BigDecimal amt;
    /**
     * 金额
     */
    @ApiModelProperty(name = "discount", value = "折扣", required = true , dataType = "BigDecimal")
    private BigDecimal discount;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderNo", value = "订单号", required = false , dataType = "String")
    private String orderNo;
    /**
     * 订单金额
     */
    @ApiModelProperty(name = "orderAmt", value = "订单金额", required = false , dataType = "String")
    private String orderAmt;



    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }
}
