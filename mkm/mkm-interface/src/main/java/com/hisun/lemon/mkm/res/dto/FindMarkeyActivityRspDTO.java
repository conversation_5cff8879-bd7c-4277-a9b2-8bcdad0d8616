package com.hisun.lemon.mkm.res.dto;

import java.util.List;

/**
 * Created by chen on 7/20 0020.
 */
public class FindMarkeyActivityRspDTO {
    /**
     * 当前页
     */
    private int pageNo;
    /**
     * 每页记录数
     */
    private int pageSize;

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<MarketActivityDetail> getList() {
        return list;
    }

    public void setList(List<MarketActivityDetail> list) {
        this.list = list;
    }

    /**
     * 总记录数

     */
    private int total;

    /**
     * 营销活动列表
     */
    private List<MarketActivityDetail> list;

}
