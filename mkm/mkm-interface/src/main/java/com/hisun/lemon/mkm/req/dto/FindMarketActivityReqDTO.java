package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * Created by chen on 7/20 0020.
 */
public class FindMarketActivityReqDTO {
    /**

     * @Fields id 活动id
     */
    @ApiModelProperty(name = "id", value = "活动id", required = false , dataType = "String")
    private String id;
       /**
     * @Fields atvNm 活动名称
     */
    @ApiModelProperty(name = "atvNm", value = "活动名称", required = false , dataType = "String")
    private String atvNm;

    /**
     * 营销工具
     * 01-电子券 02 -海币 03-优惠券 04-折扣券
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具 01-电子券 02 -海币 03-优惠券 04-折扣券", required = false , dataType = "String")
    private String[] mkTools;

    /**
     * @Fields createDate 活动创建时间
     */
    @ApiModelProperty(name = "createDate", value = "活动创建时间", required = false , dataType = "String")
    private String createDate;

    /**
     * 商户编号
     */
    @ApiModelProperty(name = "instId", value = "商户编号", required = false , dataType = "String")
    private String instId;
    /**
     * 商户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", required = false , dataType = "String")
    private String userId;
    /**
     * 当前页
     */
    @ApiModelProperty(name = "pageNo", value = "当前页", required = false , dataType = "int")
    private int pageNo;
    /**
     * 每页记录数
     */
    @ApiModelProperty(name = "pageSize", value = "每页记录数", required = false , dataType = "int")
    private int pageSize;

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAtvNm() {
        return atvNm;
    }

    public void setAtvNm(String atvNm) {
        this.atvNm = atvNm;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String[] getMkTools() {
        return mkTools;
    }

    public void setMkTools(String[] mkTools) {
        this.mkTools = mkTools;
    }
}
