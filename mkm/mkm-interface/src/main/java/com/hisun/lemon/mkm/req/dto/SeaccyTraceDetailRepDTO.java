package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * Created by chen on 8/25 0025.
 */
public class SeaccyTraceDetailRepDTO {
    /**

     * @Fields type 交易类型
     */
    @ApiModelProperty(name = "type", value = "交易类型 00-充值 01-发放 02-消费 06-退款", required = false , dataType = "String")
    private String type;
    @ApiModelProperty(name = "mobile", value = "手机号", required = false , dataType = "String")
    private String mobile;
    /**
     * @Fields orderNo 订单号
     */
    @ApiModelProperty(name = "orderNo", value = "订单号", required = false , dataType = "String")
    private String orderNo;

    /**
     * @Fields tarceDt 交易日期
     */
    @ApiModelProperty(name = "tarceDt", value = "交易日期", required = false , dataType = "LocalDate")
    private LocalDate tarceDt;

    /**
     * 当前页
     */
    @ApiModelProperty(name = "pageNo", value = "当前页", required = false , dataType = "int")
    private int pageNo;
    /**
     * 每页记录数
     */
    @ApiModelProperty(name = "pageSize", value = "每页记录数", required = false , dataType = "int")
    private int pageSize;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDate getTarceDt() {
        return tarceDt;
    }

    public void setTarceDt(LocalDate tarceDt) {
        this.tarceDt = tarceDt;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
