package com.hisun.lemon.mkm.res.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * Created by chen on 7/22 0022.
 */
public class GetConponResDTO {
    /**
     * 结果 1-成功 0-失败 2-异常
     */
    private String result;

    /**
     * 券别编号
     */
    private String conponNo;
    /**
     * 券别编号
     */
    @ApiModelProperty(name = "getFlag", value = "是否可领取标志 1-可以领取 0-不可以领取", required = true , dataType = "String")
    private String getFlag;

    public String getConponNo() {
        return conponNo;
    }

    public void setConponNo(String conponNo) {
        this.conponNo = conponNo;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getGetFlag() {
        return getFlag;
    }

    public void setGetFlag(String getFlag) {
        this.getFlag = getFlag;
    }
}
