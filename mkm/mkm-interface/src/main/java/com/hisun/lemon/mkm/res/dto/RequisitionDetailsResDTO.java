package com.hisun.lemon.mkm.res.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 用户营销工具领用明细返回对象
 *
 * <AUTHOR>
 * @create 2017/7/24
 */
public class RequisitionDetailsResDTO {

    private String mblNo;

    private LocalDate releaseDt;

    private LocalTime releaseTm;

    private String atvId;

    private String mkmTyp;

    private BigDecimal couponAmt;

    private String couponSts;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public LocalDate getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(LocalDate releaseDt) {
        this.releaseDt = releaseDt;
    }

    public LocalTime getReleaseTm() {
        return releaseTm;
    }

    public void setReleaseTm(LocalTime releaseTm) {
        this.releaseTm = releaseTm;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getMkmTyp() {
        return mkmTyp;
    }

    public void setMkmTyp(String mkmTyp) {
        this.mkmTyp = mkmTyp;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getCouponSts() {
        return couponSts;
    }

    public void setCouponSts(String couponSts) {
        this.couponSts = couponSts;
    }

    @Override
    public String toString() {
        return "RequisitionDetailsResDTO{" +
                "mblNo='" + mblNo + '\'' +
                ", releaseDt=" + releaseDt +
                ", releaseTm=" + releaseTm +
                ", atvId='" + atvId + '\'' +
                ", mkmTyp='" + mkmTyp + '\'' +
                ", couponAmt=" + couponAmt +
                ", couponSts='" + couponSts + '\'' +
                '}';
    }
}
