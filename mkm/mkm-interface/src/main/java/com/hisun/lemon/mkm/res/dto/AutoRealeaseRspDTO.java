package com.hisun.lemon.mkm.res.dto;

import java.math.BigDecimal;

/**
 * Created by chen on 8/10 0010.
 */
public class AutoRealeaseRspDTO {
    /**
     * 结果 1-成功 0-失败 2-异常
     */
    private String result;

    /**
     * 券别编号
     */
    private String conponNo;

    /**
     * 券别类型01 -电子券 03 - 优惠券 04-折扣券
     */
    private String mkTool;

    /**
     * amt
     * @return
     */

    private BigDecimal amt;
    public String getConponNo() {
        return conponNo;
    }

    public void setConponNo(String conponNo) {
        this.conponNo = conponNo;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }
}
