package com.hisun.lemon.mkm.res.dto;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Created by chen on 7/27 0027.
 */
public class CouponDetail {

    /**
     * @Fields id
     */

    private Integer id;
    /**
     * @Fields couponNo 抵用券编号
     */
    @ApiModelProperty(name = "couponNo", value = "抵用券编号" , dataType = "String")
    private String couponNo;
    /**
     * @Fields mkTool 营销工具类型
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具类型 01-电子券 02-海币 03-优惠券 04-折扣券" , dataType = "String")
    private String mkTool;
    /**
     * @Fields atvId 活动编号
     */
    @ApiModelProperty(name = "atvId", value = "活动编号" , dataType = "String")
    private String atvId;
    /**
     * @Fields instId 商户id
     */
    @ApiModelProperty(name = "instId", value = "商户id" , dataType = "String")
    private String instId;
    /**
     * @Fields userId 用户id
     */
    @ApiModelProperty(name = "用户id", value = "用户id" , dataType = "String")
    private String userId;
    /**
     * @Fields mobile 用户手机号
     */
    @ApiModelProperty(name = "用户手机号", value = "mobile" , dataType = "String")
    private String mobile;
    /**
     * @Fields amt 券别金额
     */
    @ApiModelProperty(name = "券别金额", value = "balance" , dataType = "String")
    private BigDecimal amt;
    /**
     * @Fields balance 券别余额
     */
    private BigDecimal balance;
    /**
     * @Fields releaseTm 发放时间
     */
    @ApiModelProperty(name = "releaseTm", value = "发放时间" , dataType = "LocalTime")
    private LocalTime releaseTm;
    /**
     * @Fields releaseDt 发放日期
     */
    @ApiModelProperty(name = "releaseDt", value = "发放日期" , dataType = "LocalDate")
    private LocalDate releaseDt;
    /**
     * @Fields couponValTm 券别有效日期
     */
    @ApiModelProperty(name = "couponValTm", value = "券别有效日期" , dataType = "LocalDateTime")
    private LocalDateTime couponValTm;
    /**
     * @Fields couponInvalTm 券别失效日期
     */
    @ApiModelProperty(name = "couponInvalTm", value = "券别失效日期" , dataType = "LocalDateTime")
    private LocalDateTime couponInvalTm;
    /**
     * @Fields status 状态 01-未使用 02-已使用 03-已过期 04-冻结 05-待激活
     */
    @ApiModelProperty(name = "status", value = "状态" , dataType = "String")
    private String status;
    /**
     * @Fields orderNo 最后使用订单号
     */
    @ApiModelProperty(name = "orderNo", value = "订单号" , dataType = "String")
    private String orderNo;

    /**
     * 订单金额
     */
    @ApiModelProperty(name = "orderAmt", value = "订单金额" , dataType = "BigDecimal")
    private BigDecimal orderAmt;


    /**
     * 折扣
     * @return
     */

    private BigDecimal discount;

    /**
     * 券别名称
     */
    private String couponName;

    /**
     * 订单最小金额
     */
    @ApiModelProperty(name = "minAmt", value = "订单最小金额" , dataType = "BigDecimal")
    private BigDecimal minAmt;

    /**
     * 订单最大金额
     */
    @ApiModelProperty(name = "maxAmt", value = "订单最大金额" , dataType = "BigDecimal")
    private BigDecimal maxAmt;

    public BigDecimal getMinAmt() {
        return minAmt;
    }

    public void setMinAmt(BigDecimal minAmt) {
        this.minAmt = minAmt;
    }

    public BigDecimal getMaxAmt() {
        return maxAmt;
    }

    public void setMaxAmt(BigDecimal maxAmt) {
        this.maxAmt = maxAmt;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCouponNo() {
        return couponNo;
    }

    public void setCouponNo(String couponNo) {
        this.couponNo = couponNo;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public LocalTime getReleaseTm() {
        return releaseTm;
    }

    public void setReleaseTm(LocalTime releaseTm) {
        this.releaseTm = releaseTm;
    }

    public LocalDate getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(LocalDate releaseDt) {
        this.releaseDt = releaseDt;
    }

    public LocalDateTime getCouponValTm() {
        return couponValTm;
    }

    public void setCouponValTm(LocalDateTime couponValTm) {
        this.couponValTm = couponValTm;
    }

    public LocalDateTime getCouponInvalTm() {
        return couponInvalTm;
    }

    public void setCouponInvalTm(LocalDateTime couponInvalTm) {
        this.couponInvalTm = couponInvalTm;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }
}
