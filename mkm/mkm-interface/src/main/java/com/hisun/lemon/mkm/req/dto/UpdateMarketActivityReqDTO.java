package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * Created by chen on 7/20 0020.
 */
public class UpdateMarketActivityReqDTO {



    /**

     * @Fields id 活动id
     */
    @ApiModelProperty(name = "id", value = "活动id", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 25)
    private String id;
    /**
     * @Fields atvNm 活动名称
     */
    @ApiModelProperty(name = "atvNm", value = "活动名称", required = false , dataType = "String")
    @Length(max = 100)
    private String atvNm;
    /**
     * @Fields total 总发放数量
     */
    @ApiModelProperty(name = "total", value = "总发放数量", required = false , dataType = "Integer")
    private Integer total;
    /**
     * @Fields totalAmt 发放总金额
     */
    @ApiModelProperty(name = "totalAmt", value = "发放总金额", required = false , dataType = "BigDecimal")
    private BigDecimal totalAmt;
    /**
     * @Fields receiveTimes 单用户领取次数
     */
    @ApiModelProperty(name = "receiveTimes", value = "单用户领取次数", required = false , dataType = "Integer")
    private Integer receiveTimes;
    /**
     * @Fields amt 单券金额
     */
    @ApiModelProperty(name = "amt", value = "单券金额", required = false , dataType = "BigDecimal")
    private BigDecimal amt;

    /**
     * @Fields endTime 活动结束时间
     */
    @ApiModelProperty(name = "endTimeSr", value = "活动结束时间", required = false , dataType = "String")
    @Length(max = 14,min = 14)
    private String endTimeSr;

    /**
     * @Fields instId 商户id （电子券）
     */
    @ApiModelProperty(name = "instId", value = "instId 商户id （电子券）", required = false , dataType = "String")
    private String instId;
    /**
     * @Fields minAmt 可使用订单最小金额(电子券)
     */
    @ApiModelProperty(name = "minAmt", value = "可使用订单最小金额(电子券)", required = false , dataType = "BigDecimal")
    private BigDecimal minAmt;
    /**
     * @Fields maxAmt 可使用订单最大金额(电子券)
     */
    @ApiModelProperty(name = "maxAmt", value = " 可使用订单最大金额(电子券)", required = false , dataType = "BigDecimal")
    private BigDecimal maxAmt;

    /**
     * 科目号
     */
    @ApiModelProperty(name = "item", value = " 科目号", required = false , dataType = "String")
    @Length(max = 30)
    private String item;

    public String getItem() {
        return item;
    }
    /**
     * 折扣
     */
    private BigDecimal discount;

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    /**
     * 修改操作人员
     */
    private String mdfUserOpr;

    public String getMdfUserOpr() {
        return mdfUserOpr;
    }

    public void setMdfUserOpr(String mdfUserOpr) {
        this.mdfUserOpr = mdfUserOpr;
    }

    public void setItem(String item) {
        this.item = item;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAtvNm() {
        return atvNm;
    }

    public void setAtvNm(String atvNm) {
        this.atvNm = atvNm;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public Integer getReceiveTimes() {
        return receiveTimes;
    }

    public void setReceiveTimes(Integer receiveTimes) {
        this.receiveTimes = receiveTimes;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getEndTimeSr() {
        return endTimeSr;
    }

    public void setEndTimeSr(String endTimeSr) {
        this.endTimeSr = endTimeSr;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public BigDecimal getMinAmt() {
        return minAmt;
    }

    public void setMinAmt(BigDecimal minAmt) {
        this.minAmt = minAmt;
    }

    public BigDecimal getMaxAmt() {
        return maxAmt;
    }

    public void setMaxAmt(BigDecimal maxAmt) {
        this.maxAmt = maxAmt;
    }
}
