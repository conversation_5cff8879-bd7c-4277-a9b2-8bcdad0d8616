package com.hisun.lemon.mkm.res.innerDto;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by chen on 8/29 0029.
 */
public class QueryUserSeaCcyResDTO {


    /**
     * @Fields userId 用户id
     */
    private String userId;

    /**
     * @Fields mobile 用户手机号
     */
    private String mobile;
    /**
     * @Fields count  海币余额
     */
    private Integer count;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
