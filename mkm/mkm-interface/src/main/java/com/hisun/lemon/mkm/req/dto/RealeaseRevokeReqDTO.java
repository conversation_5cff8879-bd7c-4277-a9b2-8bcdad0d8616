package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.time.LocalDateTime;

/**
 * Created by chen on 8/10 0010.
 */
public class RealeaseRevokeReqDTO {
    /**
     * 流水号
     */
    @ApiModelProperty(name = "seq", value = "流水号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String seq;

    /**
     * 原交易流水
     */
    @ApiModelProperty(name = "oriSeq", value = "原消费交易流水 ", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String oriSeq;

    /**
     * 营销工具
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具 01-电子券 02-海币 03-优惠券", required = false , dataType = "String")
    private String mkTool;

    /**
     * 交易时间
     */
    @ApiModelProperty(name = "revokedTm", value = "交易时间", required = true , dataType = "LocalDateTime")
    private LocalDateTime revokedTm;

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getOriSeq() {
        return oriSeq;
    }

    public void setOriSeq(String oriSeq) {
        this.oriSeq = oriSeq;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public LocalDateTime getRevokedTm() {
        return revokedTm;
    }

    public void setRevokedTm(LocalDateTime revokedTm) {
        this.revokedTm = revokedTm;
    }
}
