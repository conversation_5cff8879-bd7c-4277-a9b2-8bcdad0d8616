package com.hisun.lemon.mkm.res.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/7/26
 */
public class BatchFileRecResDTO {

    private String recNo;

    private String fileNm;

    private String retFilePath;

    private String oprTyp;

    private String totNum;

    private String totAmt;

    private String batchSts;

    private String finNum;

    private LocalDate updDt;

    private String creOprId;

    private String updOprId;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;

    public String getRecNo() {
        return recNo;
    }

    public void setRecNo(String recNo) {
        this.recNo = recNo;
    }

    public String getFileNm() {
        return fileNm;
    }

    public void setFileNm(String fileNm) {
        this.fileNm = fileNm;
    }

    public String getRetFilePath() {
        return retFilePath;
    }

    public void setRetFilePath(String retFilePath) {
        this.retFilePath = retFilePath;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getTotNum() {
        return totNum;
    }

    public void setTotNum(String totNum) {
        this.totNum = totNum;
    }

    public String getTotAmt() {
        return totAmt;
    }

    public void setTotAmt(String totAmt) {
        this.totAmt = totAmt;
    }

    public String getBatchSts() {
        return batchSts;
    }

    public void setBatchSts(String batchSts) {
        this.batchSts = batchSts;
    }

    public String getFinNum() {
        return finNum;
    }

    public void setFinNum(String finNum) {
        this.finNum = finNum;
    }

    public LocalDate getUpdDt() {
        return updDt;
    }

    public void setUpdDt(LocalDate updDt) {
        this.updDt = updDt;
    }

    public String getCreOprId() {
        return creOprId;
    }

    public void setCreOprId(String creOprId) {
        this.creOprId = creOprId;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "BatchFileRecResDTO{" +
                "recNo='" + recNo + '\'' +
                ", fileNm='" + fileNm + '\'' +
                ", retFilePath='" + retFilePath + '\'' +
                ", oprTyp='" + oprTyp + '\'' +
                ", totNum='" + totNum + '\'' +
                ", totAmt='" + totAmt + '\'' +
                ", batchSts='" + batchSts + '\'' +
                ", finNum='" + finNum + '\'' +
                ", updDt=" + updDt +
                ", creOprId='" + creOprId + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
