package com.hisun.lemon.mkm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.lemon.mkm.res.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by chen on 7/19 0019.
 */
@FeignClient("MKM")
public interface MarketActivityClient {

    @PostMapping("/marketActivity/find")
    public GenericRspDTO<FindMarkeyActivityRspDTO> findMarketActivity(@Validated @RequestBody GenericDTO<FindMarketActivityReqDTO> findMarketActivityDTO);

    @PostMapping("/marketActivity/add")
    public GenericRspDTO<String> addMarketActivity(@RequestBody GenericDTO<AddMarketActivityReqDTO> addMarketActivityDTO);

    @PostMapping("/marketActivity/update")
    public GenericRspDTO<NoBody> updateMarketActivity(@Validated @RequestBody GenericDTO<UpdateMarketActivityReqDTO> updateMarketActivityDTO);

    @PostMapping("/marketActivity/delete")
    public GenericRspDTO<NoBody> deleteMarketActivity(@Validated @RequestBody GenericDTO<DeleteMarketActivityReqDTO> deleteMarketActivityDTO);

    @PostMapping("/marketActivity/maintian")
    public GenericRspDTO<NoBody> maintainMarketActivity(@Validated @RequestBody GenericDTO<MaintainActivityReqDTO> maintainActivityReqDTO);
    /**
    /**
     * 海币充值接口
     * @param rechargeMkmToolReqDTO
     * @return
     */
    @PostMapping("/mkmTool/get")
    public GenericRspDTO<RechargeMkmToolResDTO> getSeaCyy(@Validated @RequestBody GenericDTO<RechargeMkmToolReqDTO> rechargeMkmToolReqDTO);
    /**
     * 海币消费接口
     * @param consumeSeaCcyReqDTO
     * @return
     */
    @PostMapping("/mkmTool/consumeSeaCcy")
    public GenericRspDTO<ConsumeSeaCcyResDTO> consumeSeaCcy(@Validated @RequestBody GenericDTO<ConsumeSeaCcyReqDTO> consumeSeaCcyReqDTO);

    /**
     * 电子券消费接口
     * @param consumeCouponReqDTO
     * @return
     */
    @PostMapping("/mkmTool/consumeCoupon")
    public GenericRspDTO<ConsumeCouponResDTO> consumeCoupon(@Validated @RequestBody GenericDTO<ConsumeCouponReqDTO> consumeCouponReqDTO);

    /**
     * 消费撤销接口
     * @param revokedConsumeCouponReqDTO
     * @return
     */
    @PostMapping("/mkmTool/revoconsume")
    public GenericRspDTO<RevokedConsumeCouponResDTO> revokedConsume(@Validated @RequestBody GenericDTO<RevokedConsumeCouponReqDTO> revokedConsumeCouponReqDTO);

    /**
     * 折扣券和优惠券自动发放接口
     * @param req
     * @return
     */
    @PostMapping("/mkmTool/autoRealease")
    public GenericRspDTO<AutoRealeaseRspDTO> autoRealease(@Validated @RequestBody GenericDTO<AutoRealeaseReqDTO> req);
    /**
     * 折扣券和优惠券自动发放撤销接口
     * @param req
     * @return
     */
    @PostMapping("/mkmTool/realeaseRevoke")
    public GenericRspDTO<RealeaseRevokeRspDTO> realeaseRevoke(@Validated @RequestBody GenericDTO<RealeaseRevokeReqDTO> req);

    /**
     * 查询用户营销海币或者优惠券
     *
     * @param
     * @return queryUserMkmTool
     */
    @PostMapping("/mkmTool/queryUserMkmTool")
    public GenericRspDTO<QueryUserMkmToolRspDTO> queryUserMkmTool(@Validated @RequestBody GenericDTO<QueryUserMkmToolReqDTO> queryUserMkmToolReqDTO);


    /**
     * 海币转赠接口
     * @param
     * @return queryUserMkmTool
     */
    @PostMapping("/mkmTool/seaccyGitf")
    public GenericRspDTO<SeaccyGitfRspDTO> seaccyGitf(@Validated @RequestBody GenericDTO<SeaccyGitfRepDTO> req) ;
    /**
     * 海币交易记录查询
     * @param
     * @return queryUserMkmTool
     */
    @PostMapping("/mkmTool/seaccyTraceDetail")
    public GenericRspDTO<SeaccyTraceDetailRspDTO> seaccyTraceDetail(@Validated @RequestBody GenericDTO<SeaccyTraceDetailRepDTO> req)  ;
}
