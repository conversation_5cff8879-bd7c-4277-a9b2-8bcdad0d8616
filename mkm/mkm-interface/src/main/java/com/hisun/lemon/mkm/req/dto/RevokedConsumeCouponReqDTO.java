package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Created by chen on 7/24 0024.
 */
public class RevokedConsumeCouponReqDTO {

    /**
     * 流水号
     */
    @ApiModelProperty(name = "seq", value = "流水号", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String seq;

    /**
     * 原交易流水
     */
    @ApiModelProperty(name = "oriSeq", value = "原消费交易流水 ", required = true , dataType = "String")
    @NotEmpty
    @Length(max = 32)
    private String oriSeq;

    /**
     * 营销工具
     */
    @ApiModelProperty(name = "mkTool", value = "营销工具 01-电子券 02-海币 03-优惠券", required = false , dataType = "String")
    private String mkTool;
    /**
     * 交易类型
     * type
     */
    @ApiModelProperty(name = "mkTool", value = "交易类型 06-退款 09-撤销 07-退款撤销", required = false , dataType = "String")
    private String type;
    /**
     * 海币退款数量
     */
    @ApiModelProperty(name = "count", value = "海币退款数量 如果营销工具为02-海币时必输", required = false , dataType = "Integer")
    private Integer count;

    /**
     * 海币退款数量
     */
    @ApiModelProperty(name = "amt", value = "电子券退款金额", required = false , dataType = "BigDecimal")
    private BigDecimal amt;

    /**
     * 交易时间
     */
    @ApiModelProperty(name = "revokedTm", value = "交易时间", required = true , dataType = "LocalDateTime")

    private LocalDateTime revokedTm;

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getOriSeq() {
        return oriSeq;
    }

    public void setOriSeq(String oriSeq) {
        this.oriSeq = oriSeq;
    }

    public LocalDateTime getRevokedTm() {
        return revokedTm;
    }

    public void setRevokedTm(LocalDateTime revokedTm) {
        this.revokedTm = revokedTm;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }
}
