package com.hisun.lemon.csm.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 营业厅结算响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleHallApplyRspDTO {

    /** 结算订单号 */
    @ApiModelProperty(name = "orderNo", value = "平台订单号")
    private String orderNo;

    /** 营业厅结算申请订单号 */
    @ApiModelProperty(name = "busOrderNo", value = "营业厅结算申请订单号")
    private String busOrderNo;

    @ApiModelProperty(name = "orderDate", value = "结算手续费")
    private BigDecimal tradeFee;

    @ApiModelProperty(name = "settleAmt", value = "申请结算金额")
    private BigDecimal settleAmt;

    @ApiModelProperty(name = "userNo", value = "结算商户用户号")
    private String merchantId;

    /** 订单日期 */
    @ApiModelProperty(name = "orderDate", value = "交易日期")
    private LocalDate tradeDate;
    /** 订单时间 */
    @ApiModelProperty(name = "orderTime", value = "交易时间")
    private LocalTime tradeTime;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public BigDecimal getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(BigDecimal tradeFee) {
        this.tradeFee = tradeFee;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public BigDecimal getSettleAmt() {
        return settleAmt;
    }

    public void setSettleAmt(BigDecimal settleAmt) {
        this.settleAmt = settleAmt;
    }
}
