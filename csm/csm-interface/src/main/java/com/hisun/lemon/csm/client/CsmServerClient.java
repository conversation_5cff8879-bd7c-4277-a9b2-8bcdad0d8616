package com.hisun.lemon.csm.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hisun.lemon.csm.dto.SettleConfirmReqDTO;
import com.hisun.lemon.csm.dto.SettleRepairReqDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * @Description  清分结算服务接口
 * <AUTHOR>
 * @date 2017年7月7日 下午3:01:38 
 * @version V1.0
 */
@FeignClient("CSM")
public interface CsmServerClient {
    
    /**
     * @Description 结算确认
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/csm/settle/confirm")
    public GenericRspDTO<NoBody> settleConfirm(@Validated @RequestBody GenericDTO<SettleConfirmReqDTO> reqDTO);
    
    /**
     * @Description 结算对账补单
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/csm/settle/check/repair")
    public GenericRspDTO<NoBody> orderRepairProcess(@Validated @RequestBody GenericDTO<SettleRepairReqDTO> reqDTO);
    
    /**
     * @Description 结算对账撤单
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/csm/settle/check/revoke")
    public GenericRspDTO<NoBody> orderRevokeProcess(@Validated @RequestBody GenericDTO<SettleRepairReqDTO> reqDTO);

    /**
     * @Description 营业厅商户结算对账撤单处理
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/csm/settle/hall/check/revoke")
    public GenericRspDTO<NoBody> hallSettleRevokeHandler(@Validated @RequestBody GenericDTO<SettleRepairReqDTO> reqDTO);
}
