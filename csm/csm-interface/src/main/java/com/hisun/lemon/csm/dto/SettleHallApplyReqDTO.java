package com.hisun.lemon.csm.dto;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 营业厅结算请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleHallApplyReqDTO {

    /** 营业厅商户号 **/
    @ApiModelProperty(name = "merchantId", value = "营业厅商户号", required = true)
    @NotNull(message = "CSM10002")
    private String merchantId;

    /** 营业厅商户名 **/
    @ApiModelProperty(name = "merchantName", value = "营业厅商户名", required = true)
    private String merchantName;

    /** 内部用户号 */
    @ApiModelProperty(name = "userId", value = "结算商户号", required = true)
    @NotNull(message = "CSM10002")
    @Length(max = 20)
    private String userId;
    /** 结算金额 */
    @ApiModelProperty(name = "settleAmt", value = "结算金额", required = true)
    @NotNull(message = "CSM10006")
    private BigDecimal settleAmt;
    /** 业务订单号 */
    @ApiModelProperty(name = "busOrderNo", value = "业务订单号", required = true)
    private String busOrderNo;

    /** 币种 */
    @ApiModelProperty(name = "orderCcy", value = "币种(USD:美元)")
    private String orderCcy;

    /** 支付密码 */
    @ApiModelProperty(name = "payPassword", value = "支付密码", required = true)
    @NotNull(message = "CSM10008")
    private String payPassword;

    /** 支付密码随机数 */
    @ApiModelProperty(name = "payPwdRandom", value = "支付密码随机数", required = true)
    @NotNull(message = "CSM10012")
    private String payPwdRandom;

    /** 密码随机数 */
    @ApiModelProperty(name = "seaRandom", value = "密码随机数", required = true)
    private String seaRandom;

    public String getSeaRandom() {
        return seaRandom;
    }

    public void setSeaRandom(String seaRandom) {
        this.seaRandom = seaRandom;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getSettleAmt() {
        return settleAmt;
    }

    public void setSettleAmt(BigDecimal settleAmt) {
        this.settleAmt = settleAmt;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getPayPassword() {
        return payPassword;
    }

    public void setPayPassword(String payPassword) {
        this.payPassword = payPassword;
    }

    public String getPayPwdRandom() {
        return payPwdRandom;
    }

    public void setPayPwdRandom(String payPwdRandom) {
        this.payPwdRandom = payPwdRandom;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getOrderCcy() {
        return orderCcy;
    }

    public void setOrderCcy(String orderCcy) {
        this.orderCcy = orderCcy;
    }

}
