package com.hisun.lemon.csm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算确认请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleConfirmReqDTO {
    /** 订单号 */
    @ApiModelProperty(name = "orderNo", value = "订单号", required = true)
    @NotNull(message = "CSM10005")
    @Length(max = 25)
    private String orderNo;
    /** 订单号 */
    @ApiModelProperty(name = "stats", value = "订单状态(success:成功 failure:失败)", required = true)
    @NotNull(message = "CSM10009")
    @Length(max = 8)
    private String stats;
    /** 结算信息 */
    @ApiModelProperty(name = "message", value = "结算信息")
    @Length(max = 64)
    private String message;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
