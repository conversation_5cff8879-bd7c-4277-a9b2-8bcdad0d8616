package com.hisun.lemon.csm.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算余额响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleBanlanceRspDTO {

    /** 待结算账户余额 */
    @ApiModelProperty(name = "settleAccountBal", value = "待结算账户余额")
    private BigDecimal settleAccountBal;

    public BigDecimal getSettleAccountBal() {
        return settleAccountBal;
    }

    public void setSettleAccountBal(BigDecimal settleAccountBal) {
        this.settleAccountBal = settleAccountBal;
    }
}
