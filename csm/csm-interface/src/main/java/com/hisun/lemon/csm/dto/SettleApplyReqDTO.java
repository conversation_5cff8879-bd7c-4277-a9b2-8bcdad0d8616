package com.hisun.lemon.csm.dto;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算申请请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleApplyReqDTO {
    /** 结算金额 */
    @ApiModelProperty(name = "settleAmt", value = "结算金额", required = true)
    @NotNull(message = "CSM10006")
    private BigDecimal settleAmt;
    /** 结算手续费 */
    @ApiModelProperty(name = "fee", value = "结算手续费", required = true)
    @NotNull(message = "CSM10007")
    private BigDecimal fee;
    /** 支付密码 */
    @ApiModelProperty(name = "payPassword", value = "支付密码", required = true)
    @NotNull(message = "CSM10008")
    private String payPassword;
    /** 支付密码随机数 */
    @ApiModelProperty(name = "payPwdRandom", value = "支付密码随机数", required = true)
    private String payPwdRandom;
    @NotNull(message = "CSM10012")

    public BigDecimal getSettleAmt() {
        return settleAmt;
    }

    public void setSettleAmt(BigDecimal settleAmt) {
        this.settleAmt = settleAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getPayPassword() {
        return payPassword;
    }

    public void setPayPassword(String payPassword) {
        this.payPassword = payPassword;
    }

    public String getPayPwdRandom() {
        return payPwdRandom;
    }

    public void setPayPwdRandom(String payPwdRandom) {
        this.payPwdRandom = payPwdRandom;
    }
}
