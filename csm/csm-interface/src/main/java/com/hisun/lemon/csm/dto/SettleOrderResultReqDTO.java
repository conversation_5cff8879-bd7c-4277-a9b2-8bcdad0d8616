package com.hisun.lemon.csm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算订单信息结果请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleOrderResultReqDTO {
    /** 订单号 */
    @ApiModelProperty(name = "orderNo", value = "订单号", required = true)
    @NotNull(message = "CSM10005")
    @Length(max = 25)
    private String orderNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

}
