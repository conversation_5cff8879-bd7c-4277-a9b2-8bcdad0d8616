package com.hisun.lemon.csm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算账户余额请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleBanlanceReqDTO {
    /** 内部用户号 */
    @ApiModelProperty(name = "userId", value = "内部用户号")
    @NotNull(message = "CSM10002")
    @Length(max = 20)
    private String userId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

}
