package com.hisun.lemon.csm.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算订单信息结果响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleOrderResultRspDTO {

    /** 结算状态 */
    @ApiModelProperty(name = "stats", value = "结算状态(success:成功 failure:失败 none:订单不存在)")
    private String stats;

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

}
