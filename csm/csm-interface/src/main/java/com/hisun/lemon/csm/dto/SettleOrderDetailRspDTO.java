package com.hisun.lemon.csm.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算订单信息详情响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleOrderDetailRspDTO {

    /** 订单号 */
    @ApiModelProperty(name = "orderNo", value = "订单号")
    private String orderNo;
    /** 交易金额 */
    @ApiModelProperty(name = "tradeAmt", value = "交易金额")
    private BigDecimal tradeAmt;
    /** 交易手续费 */
    @ApiModelProperty(name = "tradeFee", value = "交易手续费")
    private BigDecimal tradeFee;
    /** 到账金额 */
    @ApiModelProperty(name = "receiveAmt", value = "到账金额")
    private BigDecimal receiveAmt;
    /** 交易日期 */
    @ApiModelProperty(name = "tradeDate", value = "交易日期")
    private LocalDate tradeDate;
    /** 交易时间 */
    @ApiModelProperty(name = "tradeTime", value = "交易时间")
    private LocalTime tradeTime;
    /** 业务类型 */
    @ApiModelProperty(name = "busType", value = "业务类型(0403:自主结算 0404:自动结算 0405:营业厅取现)")
    private String busType;
    /** 协议付款日期 */
    @ApiModelProperty(name = "agreePaymentDate", value = "协议付款日期")
    private LocalDate agreePaymentDate;
    /** 付款日期 */
    @ApiModelProperty(name = "paymentDate", value = "付款日期")
    private LocalDate paymentDate;
    /** 付款时间 */
    @ApiModelProperty(name = "paymentTime", value = "付款时间")
    private LocalTime paymentTime;
    /** 上上结算日 */
    @ApiModelProperty(name = "lastLastSettleDay", value = "上上结算日")
    private LocalDate lastLastSettleDay;
    /** 上一结算日 */
    @ApiModelProperty(name = "lastSettleDay", value = "上一结算日")
    private LocalDate lastSettleDay;
    /** 结算状态 */
    @ApiModelProperty(name = "stats", value = "结算状态(S1:待付款 S2:付款成功 F:款失败)")
    private String stats;
    /** 结算银行 */
    @ApiModelProperty(name = "capCorgNm", value = "结算银行")
    private String capCorgNm;
    /** 结算银行卡号 */
    @ApiModelProperty(name = "capCardNo", value = "结算银行卡号")
    private String capCardNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public LocalTime getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(LocalTime paymentTime) {
        this.paymentTime = paymentTime;
    }

    public LocalDate getLastLastSettleDay() {
        return lastLastSettleDay;
    }

    public void setLastLastSettleDay(LocalDate lastLastSettleDay) {
        this.lastLastSettleDay = lastLastSettleDay;
    }

    public LocalDate getLastSettleDay() {
        return lastSettleDay;
    }

    public void setLastSettleDay(LocalDate lastSettleDay) {
        this.lastSettleDay = lastSettleDay;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public String getCapCorgNm() {
        return capCorgNm;
    }

    public void setCapCorgNm(String capCorgNm) {
        this.capCorgNm = capCorgNm;
    }

    public String getCapCardNo() {
        return capCardNo;
    }

    public void setCapCardNo(String capCardNo) {
        this.capCardNo = capCardNo;
    }

    public BigDecimal getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(BigDecimal tradeFee) {
        this.tradeFee = tradeFee;
    }

    public BigDecimal getReceiveAmt() {
        return receiveAmt;
    }

    public void setReceiveAmt(BigDecimal receiveAmt) {
        this.receiveAmt = receiveAmt;
    }

    public LocalDate getAgreePaymentDate() {
        return agreePaymentDate;
    }

    public void setAgreePaymentDate(LocalDate agreePaymentDate) {
        this.agreePaymentDate = agreePaymentDate;
    }
}
