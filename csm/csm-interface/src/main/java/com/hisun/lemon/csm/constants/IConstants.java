package com.hisun.lemon.csm.constants;

/**
 * @Description 结算模块常量类
 * <AUTHOR>
 * @date 2017年7月14日 下午5:49:21 
 * @version V1.0
 */
public class IConstants {
    
    private IConstants() {
        throw new IllegalStateException("Utility class");
    }
    
    /** 订单状态枚举值 待付款 */
    public static final String ORDER_STATUS_ACCEPT = "accept";
    /** 订单状态枚举值 成功 */
    public static final String ORDER_STATUS_SUCCESS = "success";
    /** 订单状态枚举值 失败 */
    public static final String ORDER_STATUS_FAILURE = "failure";
    /** 订单状态枚举值 订单不存在 */
    public static final String ORDER_STATUS_NONE = "none";
}
