package com.hisun.lemon.csm.dto;

import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算列表请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleListReqDTO2 {
    /** 开始日期 */
    @ApiModelProperty(name = "stats", value = "开始日期")
    private LocalDate beginDate;
    /** 结束日期 */
    @ApiModelProperty(name = "endDate", value = "结束日期")
    private LocalDate endDate;
    /** 当前页码 */
    @ApiModelProperty(name = "pageNum", value = "当前页码", required = true)
    @NotNull(message = "CSM19998")
    private Integer pageNum;
    /** 每页大小 */
    @ApiModelProperty(name = "pageSize", value = "每页大小", required = true)
    @NotNull(message = "CSM19999")
    private Integer pageSize;

    public LocalDate getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDate beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
