package com.hisun.lemon.csm.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算信息响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleInformationRspDTO {

    /** 待结算账户余额 */
    @ApiModelProperty(name = "settleAccountBal", value = "待结算账户余额")
    private BigDecimal settleAccountBal;
    /** 结算业务类型 */
    @ApiModelProperty(name = "settleType", value = "结算业务类型 0403:自主结算 0404:自动结算 0405:营业厅提现")
    private String settleType;
    /** 计费起始金额 */
    @ApiModelProperty(name = "calculateMinAmt", value = "计费起始金额")
    private BigDecimal calculateMinAmt;
    /** 最低收取费用 */
    @ApiModelProperty(name = "minFee", value = "最低收取费用")
    private BigDecimal minFee;
    /** 最低收取费用 */
    @ApiModelProperty(name = "maxFee", value = "最高收取费用")
    private BigDecimal maxFee;
    /** 结算计费类型 */
    @ApiModelProperty(name = "calculateType", value = "结算计费类型 percent:百分比 fixed:固定费率")
    private String calculateType;
    /** 结算费率 */
    @ApiModelProperty(name = "rate", value = "结算费率")
    private BigDecimal rate;
    /** 结算费率 */
    @ApiModelProperty(name = "fixFee", value = "固定金额")
    private BigDecimal fixFee;
    /** 结算银行 */
    @ApiModelProperty(name = "capCorgNm", value = "结算银行")
    private String capCorgNm;
    /** 结算银行卡号 */
    @ApiModelProperty(name = "capCardNo", value = "结算银行卡号")
    private String capCardNo;
    /** 法人证件号 */
    @ApiModelProperty(name = "crpIdNo", value = "法人证件号")
    private String crpIdNo;
    /** 法人证件号类型 */
    @ApiModelProperty(name = "crpIdTyp", value = "法人证件号类型")
    private String crpIdTyp;

    public String getCrpIdNo() {
        return crpIdNo;
    }

    public void setCrpIdNo(String crpIdNo) {
        this.crpIdNo = crpIdNo;
    }

    public String getCrpIdTyp() {
        return crpIdTyp;
    }

    public void setCrpIdTyp(String crpIdTyp) {
        this.crpIdTyp = crpIdTyp;
    }

    public BigDecimal getSettleAccountBal() {
        return settleAccountBal;
    }

    public void setSettleAccountBal(BigDecimal settleAccountBal) {
        this.settleAccountBal = settleAccountBal;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getCapCorgNm() {
        return capCorgNm;
    }

    public void setCapCorgNm(String capCorgNm) {
        this.capCorgNm = capCorgNm;
    }

    public String getCapCardNo() {
        return capCardNo;
    }

    public void setCapCardNo(String capCardNo) {
        this.capCardNo = capCardNo;
    }

    public String getSettleType() {
        return settleType;
    }

    public void setSettleType(String settleType) {
        this.settleType = settleType;
    }

    public String getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(String calculateType) {
        this.calculateType = calculateType;
    }

    public BigDecimal getMinFee() {
        return minFee;
    }

    public void setMinFee(BigDecimal minFee) {
        this.minFee = minFee;
    }

    public BigDecimal getMaxFee() {
        return maxFee;
    }

    public void setMaxFee(BigDecimal maxFee) {
        this.maxFee = maxFee;
    }

    public BigDecimal getFixFee() {
        return fixFee;
    }

    public void setFixFee(BigDecimal fixFee) {
        this.fixFee = fixFee;
    }

    public BigDecimal getCalculateMinAmt() {
        return calculateMinAmt;
    }

    public void setCalculateMinAmt(BigDecimal calculateMinAmt) {
        this.calculateMinAmt = calculateMinAmt;
    }
}
