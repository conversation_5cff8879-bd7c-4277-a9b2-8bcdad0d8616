package com.hisun.lemon.csm.dto;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算列表请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleListReqDTO {
    /** 结算状态 */
    @ApiModelProperty(name = "stats", value = "结算状态(all:全部 accept:待付款 success:付款成功 failure:付款失败)", required = true)
    @Pattern(regexp = "all|accept|success|failure", message = "CSM10003")
    private String stats;
    /** 时间周期类型 */
    @ApiModelProperty(name = "cycleType", value = "周期类型(today:今天 weekly:一周 monthly:一月)", required = true)
    @Pattern(regexp = "today|weekly|monthly", message = "CSM10004")
    private String cycleType;
    /** 当前页码 */
    @ApiModelProperty(name = "pageNum", value = "当前页码", required = true)
    @NotNull(message = "CSM19998")
    private Integer pageNum;
    /** 每页大小 */
    @ApiModelProperty(name = "pageSize", value = "每页大小", required = true)
    @NotNull(message = "CSM19999")
    private Integer pageSize;

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public String getCycleType() {
        return cycleType;
    }

    public void setCycleType(String cycleType) {
        this.cycleType = cycleType;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
