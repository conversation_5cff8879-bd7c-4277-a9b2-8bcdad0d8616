package com.hisun.lemon.csm.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算列表响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleListRspDTO2 {

    /** 记录总数 */
    @ApiModelProperty(name = "totalNumber", value = "记录总数")
    private long totalNumber;


    /** 记录列表 */
    @ApiModelProperty(name = "recordList", value = "记录列表")
    private List<SettleOrderDetailRspDTO> recordList;


    public long getTotalNumber() {
        return totalNumber;
    }

    public void setTotalNumber(long totalNumber) {
        this.totalNumber = totalNumber;
    }

    public List<SettleOrderDetailRspDTO> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<SettleOrderDetailRspDTO> recordList) {
        this.recordList = recordList;
    }
}
