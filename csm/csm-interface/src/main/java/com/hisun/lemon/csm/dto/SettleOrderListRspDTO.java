package com.hisun.lemon.csm.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 结算订单信息对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class SettleOrderListRspDTO {

    /** 订单号 */
    @ApiModelProperty(name = "orderNo", value = "订单号")
    private String orderNo;
    /** 交易金额 */
    @ApiModelProperty(name = "tradeAmt", value = "交易金额")
    private BigDecimal tradeAmt;
    /** 交易日期 */
    @ApiModelProperty(name = "tradeDate", value = "交易日期")
    private LocalDate tradeDate;
    /** 交易时间 */
    @ApiModelProperty(name = "tradeTime", value = "交易时间")
    private LocalTime tradeTime;
    /** 业务类型 */
    @ApiModelProperty(name = "butType", value = "业务类型")
    private String busType;
    /** 业务类型描述 */
    @ApiModelProperty(name = "butTypeDesc", value = "业务类型描述")
    private String busTypeDesc;
    /** 结算状态 */
    @ApiModelProperty(name = "stats", value = "结算状态")
    private String stats;
    /** 结算状态描述 */
    @ApiModelProperty(name = "statsDesc", value = "结算状态描述")
    private String statsDesc;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public String getBusTypeDesc() {
        return busTypeDesc;
    }

    public void setBusTypeDesc(String busTypeDesc) {
        this.busTypeDesc = busTypeDesc;
    }

    public String getStatsDesc() {
        return statsDesc;
    }

    public void setStatsDesc(String statsDesc) {
        this.statsDesc = statsDesc;
    }

}
