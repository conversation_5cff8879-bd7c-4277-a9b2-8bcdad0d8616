package com.hisun.lemon.tfm;

import java.time.LocalDate;

import javax.annotation.Resource;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.hisun.lemon.csm.service.ICheckControlService;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年9月14日 下午4:19:37 
 * @version V1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ApplicationTest {

    @Resource
    ICheckControlService checkControlService;

    @Autowired
    private WebApplicationContext context;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    /**
     * 创建对账批次
     */
    @Test
    @Ignore
    public void registerChkBatNo() {
        LocalDate checkDate = LocalDate.of(2017, 9, 12);
        checkControlService.checkBatchRegister(checkDate);
    }

    /**
     * 处理对账批次
     */
    @Test
    @Ignore
    public void beginCheckAcc() throws Exception{
        LocalDate checkDate = LocalDate.of(2017, 9, 11);
        checkControlService.checkBatchProcess(checkDate);
    }
    
}
