package com.hisun.lemon.csm.mq;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csm.dto.SettleConfirmReqDTO;
import com.hisun.lemon.csm.service.ICsmService;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;

/**
 * @Description 结算确认通知消费
 * <AUTHOR>
 * @date 2017年8月29日 上午10:54:34 
 * @version V1.0
 */
@Component("settleConfirmConsumer")
public class SettleConfirmConsumer implements MessageHandler<SettleConfirmReqDTO> {
    
    private static final Logger logger = LoggerFactory.getLogger(SettleConfirmConsumer.class);
    @Resource
    private ICsmService csmService;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void onMessageReceive(GenericCmdDTO<SettleConfirmReqDTO> genericCmdDTO) {
        logger.info("Receive msg hand {}", ObjectMapperHelper.writeValueAsString(objectMapper, genericCmdDTO.getBody(), true));
        try {
            csmService.settleConfirm(genericCmdDTO.getBody());
        } catch (LemonException e) {
            logger.info("Receive msg hand error {}", e.getMsgCd());
        }
    }

}
