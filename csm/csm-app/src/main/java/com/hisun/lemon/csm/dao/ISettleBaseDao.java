/*
 * @ClassName ISettleBaseDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 15:59:57
 */
package com.hisun.lemon.csm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.csm.entity.SettleBaseDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface ISettleBaseDao extends BaseDao<SettleBaseDO> {
    
    int countByCondition(SettleBaseDO settleBaseDO);
    
    List<SettleBaseDO> getListByCondition(SettleBaseDO settleBaseDO);

    SettleBaseDO vaildSettle(String userId);

    List<SettleBaseDO> getValidSettleListByCondition(SettleBaseDO qryInSettleBaseDO);
}