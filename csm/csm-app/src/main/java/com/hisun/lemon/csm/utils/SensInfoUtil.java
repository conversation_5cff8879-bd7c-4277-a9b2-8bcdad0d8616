package com.hisun.lemon.csm.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.StringUtils;

/**
 * @Description 脱敏工具类
 * <AUTHOR>
 * @date 2017年7月10日 下午4:31:32
 * @version V1.0
 */
public class SensInfoUtil {

    private SensInfoUtil() {
        throw new IllegalStateException("Utility class");
    }

    /** 脱敏符号 */
    private static final String SENS_CHAR = "\\*";

    /**
     * @Description 银行卡/身份证脱敏
     * <AUTHOR>
     * @param cardNo
     * @return
     */
    public static String cardSensInfo(String cardNo) throws LemonException {
        StringBuilder builder = new StringBuilder();
        if (StringUtils.containsAny(SENS_CHAR, cardNo)) {
            throw new LemonException();
        }
        // 取前4位
        String pre4Char = StringUtils.substring(cardNo, 0, 4);
        // 取后4位
        String after4Char = StringUtils.substring(cardNo, cardNo.length() - 4);
        // 待脱敏字符串
        cardNo = StringUtils.substring(cardNo, 4, cardNo.length() - 4);
        // 脱敏
        Pattern pattern = Pattern.compile("[\\d]");
        Matcher matcher = pattern.matcher(cardNo);
        cardNo = matcher.replaceAll(SENS_CHAR);
        builder.append(pre4Char).append(cardNo).append(after4Char);
        return builder.toString();
    }
}
