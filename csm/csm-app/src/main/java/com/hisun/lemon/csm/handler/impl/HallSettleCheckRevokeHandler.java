package com.hisun.lemon.csm.handler.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csm.dto.SettleRepairReqDTO;
import com.hisun.lemon.csm.service.ICsmService;
import com.hisun.lemon.framework.lock.Locked;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description 商户营业厅结算对成功状态账差错撤单处理
 * <AUTHOR>
 * @date 2017年11月16日 上午11:02:52
 * @version V1.0
 */
@Component
public class HallSettleCheckRevokeHandler {
    
    @Resource
    private ICsmService csmService;
    
    @Locked(lockName = "hallSettleCheckRevokeHandlerLock", leaseTime=60, waitTime=30)
    public void hallSettleRevokeHandler(SettleRepairReqDTO settleLongReqDTO) throws LemonException {
        csmService.hallSettleRevokeHandler(settleLongReqDTO);
    }

}
