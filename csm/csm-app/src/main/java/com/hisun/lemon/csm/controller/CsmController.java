package com.hisun.lemon.csm.controller;

import javax.annotation.Resource;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csm.dto.*;
import com.hisun.lemon.csm.handler.impl.HallSettleCheckRevokeHandler;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csm.constants.MsgCdEnum;
import com.hisun.lemon.csm.service.ICsmService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;

/**
 * 清分结算管理
 * @GetMapping @PostMapping @PutMapping @DeleteMapping @PatchMapping
 * <AUTHOR>
 * @date 2017年7月5日
 * @time 上午10:46:30
 *
 */
@RestController
@RequestMapping("/csm")
@Api(tags="CsmController", description="清分结算服务")
public class CsmController extends BaseController{
    
    /**
     * 清分结算服务
     */
    @Resource
    private ICsmService csmService;

    @Resource
    HallSettleCheckRevokeHandler hallSettleCheckSuccHandler;
    
    @ApiOperation(value="结算信息查询", notes="结算信息查询")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "查询结算信息结果")
    @PostMapping("/settle/information")
    public GenericRspDTO<SettleInformationRspDTO> settleInformation(@Validated @RequestBody GenericDTO<SettleInfoReqDTO> reqDTO) {
        GenericRspDTO<SettleInformationRspDTO> rspDTO = new GenericRspDTO<>();
        SettleInformationRspDTO settleInformationRspDTO = null;
        String mercId = LemonUtils.getUserId();
        if(JudgeUtils.isBlank(mercId)){
            mercId = reqDTO.getBody().getMercId();
        }
        try {
            settleInformationRspDTO = csmService.settleInformation(mercId);
            rspDTO.setBody(settleInformationRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="结算账户余额度查询", notes="结算账户余额度查询")
    @ApiResponse(code = 200, message = "查询结算账户余额结果")
    @PostMapping("/settle/banlance")
    public GenericRspDTO<SettleBanlanceRspDTO> settleBanlance(@Validated @RequestBody GenericDTO<SettleBanlanceReqDTO> reqDTO) {
        GenericRspDTO<SettleBanlanceRspDTO> rspDTO = new GenericRspDTO<>();
        SettleBanlanceReqDTO settleBanlanceReqDTO = reqDTO.getBody();
        SettleBanlanceRspDTO settleBanlanceRspDTO = null;
        try {
            settleBanlanceRspDTO = csmService.settleBanlance(settleBanlanceReqDTO);
            rspDTO.setBody(settleBanlanceRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="结算申请", notes="结算申请")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "结算申请结果")
    @PostMapping("/settle/apply")
    public GenericRspDTO<SettleApplyRspDTO> settleApply(@Validated @RequestBody GenericDTO<SettleApplyReqDTO> reqDTO) {
        GenericRspDTO<SettleApplyRspDTO> rspDTO = new GenericRspDTO<>();
        SettleApplyReqDTO settleApplyReqDTO = reqDTO.getBody();
        SettleApplyRspDTO settleApplyRspDTO = null;
        try {
            settleApplyRspDTO = csmService.settleApply(settleApplyReqDTO);
            rspDTO.setBody(settleApplyRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="营业厅结算申请", notes="营业厅结算申请")
    @ApiResponse(code = 200, message = "营业厅结算申请结果")
    @PostMapping("/settle/hall/apply")
    public GenericRspDTO<SettleHallApplyRspDTO> hallSettleApply(@Validated @RequestBody GenericDTO<SettleHallApplyReqDTO> reqDTO) {
        GenericRspDTO<SettleHallApplyRspDTO> rspDTO = new GenericRspDTO<>();
        SettleHallApplyReqDTO settleApplyReqDTO = reqDTO.getBody();
        SettleHallApplyRspDTO settleApplyRspDTO = null;
        try {
            settleApplyRspDTO = csmService.settleHallApply(settleApplyReqDTO);
            rspDTO.setBody(settleApplyRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="结算确认", notes="结算确认")
    @ApiResponse(code = 200, message = "结算确认结果")
    @PostMapping("/settle/confirm")
    public GenericRspDTO<NoBody> settleConfirm(@Validated @RequestBody GenericDTO<SettleConfirmReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        SettleConfirmReqDTO settleConfirmReqDTO = reqDTO.getBody();
        try {
            csmService.settleConfirm(settleConfirmReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="结算补单处理", notes="结算补单处理")
    @ApiResponse(code = 200, message = "结算补单结果")
    @PostMapping("/settle/check/repair")
    public GenericRspDTO<NoBody> orderRepairProcess(@Validated @RequestBody GenericDTO<SettleRepairReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        SettleRepairReqDTO settleLongReqDTO = reqDTO.getBody();
        try {
            csmService.orderRepairProcess(settleLongReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="结算撤单处理", notes="结算撤单处理")
    @ApiResponse(code = 200, message = "结算撤单结果")
    @PostMapping("/settle/check/revoke")
    public GenericRspDTO<NoBody> orderRevokeProcess(@Validated @RequestBody GenericDTO<SettleRepairReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        SettleRepairReqDTO settleLongReqDTO = reqDTO.getBody();
        try {
            csmService.orderRevokeProcess(settleLongReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="portal结算订单列表查询", notes="portal结算订单列表查询")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "portal查询订单信息列表结果")
    @PostMapping("/portal/settle/order/list")
    public GenericRspDTO<SettleListRspDTO2> settleOrderList2(@Validated @RequestBody GenericDTO<SettleListReqDTO2> reqDTO) {
        GenericRspDTO<SettleListRspDTO2> rspDTO = new GenericRspDTO<>();
        SettleListReqDTO2 settleListReqDTO = reqDTO.getBody();
        SettleListRspDTO2 settleListRspDTO = null;
        try {
            settleListRspDTO = csmService.settleOrderList2(settleListReqDTO);
            rspDTO.setBody(settleListRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="结算订单列表查询", notes="结算订单列表查询")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "查询订单信息列表结果")
    @PostMapping("/settle/order/list")
    public GenericRspDTO<SettleListRspDTO> settleOrderList(@Validated @RequestBody GenericDTO<SettleListReqDTO> reqDTO) {
        GenericRspDTO<SettleListRspDTO> rspDTO = new GenericRspDTO<>();
        SettleListReqDTO settleListReqDTO = reqDTO.getBody();
        SettleListRspDTO settleListRspDTO = null;
        try {
            settleListRspDTO = csmService.settleOrderList(settleListReqDTO);
            rspDTO.setBody(settleListRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="结算订单详情查询", notes="结算订单详情查询")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "查询结算订单详情结果")
    @PostMapping("/settle/order/detail")
    public GenericRspDTO<SettleOrderDetailRspDTO> settleOrderDetail(@Validated @RequestBody GenericDTO<SettleOrderDetailReqDTO> reqDTO) {
        GenericRspDTO<SettleOrderDetailRspDTO> rspDTO = new GenericRspDTO<>();
        SettleOrderDetailReqDTO settleOrderDetailReqDTO = reqDTO.getBody();
        SettleOrderDetailRspDTO settleOrderDetailRspDTO = null;
        try {
            settleOrderDetailRspDTO = csmService.settleOrderDetail(settleOrderDetailReqDTO);
            rspDTO.setBody(settleOrderDetailRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value="订单状态查询", notes="订单状态查询查询")
    @ApiResponse(code = 200, message = "查询订单状态结果")
    @PostMapping("/settle/order/result")
    public GenericRspDTO<SettleOrderResultRspDTO> settleOrderResult(@Validated @RequestBody GenericDTO<SettleOrderResultReqDTO> reqDTO) {
        GenericRspDTO<SettleOrderResultRspDTO> rspDTO = new GenericRspDTO<>();
        SettleOrderResultReqDTO settleOrderResultReqDTO = reqDTO.getBody();
        SettleOrderResultRspDTO settleOrderResultRspDTO = null;
        try {
            settleOrderResultRspDTO = csmService.settleOrderResult(settleOrderResultReqDTO);
            rspDTO.setBody(settleOrderResultRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value="营业厅商户结算撤单处理", notes="营业厅商户结算撤单处理")
    @ApiResponse(code = 200, message = "营业厅商户结算撤单处理")
    @PostMapping("/settle/hall/check/revoke")
    public GenericRspDTO<NoBody> hallSettleRevokeHandler(@Validated @RequestBody GenericDTO<SettleRepairReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        SettleRepairReqDTO settleLongReqDTO = reqDTO.getBody();
        try {
            hallSettleCheckSuccHandler.hallSettleRevokeHandler(settleLongReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
}
