package com.hisun.lemon.csm.handler;

import java.time.LocalDate;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csm.entity.CheckControlDO;

/**
 * @Description 对账服务接口
 * <AUTHOR>
 * @date 2017年7月7日 上午11:07:48
 * @version V1.0
 */
public interface ICheckProcessHandler {
    
    /**
     * @Description 对账处理方法
     * <AUTHOR>
     * @param checkDate
     * @param checkControlDO
     * @throws LemonException
     */
    void checkFileProcess(LocalDate checkDate, CheckControlDO checkControlDO) throws LemonException;
}
