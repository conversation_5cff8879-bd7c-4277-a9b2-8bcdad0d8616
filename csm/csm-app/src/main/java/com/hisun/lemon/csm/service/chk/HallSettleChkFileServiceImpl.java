package com.hisun.lemon.csm.service.chk;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csm.constants.Constants;
import com.hisun.lemon.csm.dao.ISettleOrderDao;
import com.hisun.lemon.csm.entity.SettleOrderDO;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.jcommon.file.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生成营业厅商户结算对账文件，上传至服务器
 */
@Transactional
@Service
public class HallSettleChkFileServiceImpl extends AbstractChkFileService {

    @Resource
    private ISettleOrderDao settleOrderDao;

    public HallSettleChkFileServiceImpl() {
        super();
        this.chkOrderStatus=new String[]{
                Constants.ORDER_STATUS_DATABASE_SUCCESS
        };
        appCnl="HALL";
        this.lockName="CSM_HALL_CHK_FILE_LOCK";
    }

    @Transactional(readOnly = true)
    @Override
    protected void execute() {
        //获取对账日期
        LocalDate chkDate=chkFileUtil.getChkDate();
        //LocalDate chkDate=DateTimeUtils.getCurrentLocalDate();
        //对账文件名
        String  chkFileName=getChkFileName(appCnl,chkDate);
        //标志文件名
        String flagName=chkFileName+".flag";
        if(chkFileUtil.isStart(appCnl,flagName)){
            logger.info("对账文件标志文件" +flagName+"已经存在,不重复生成对账文件");
            return;
        }
        logger.info("开始生成对账文件：" +flagName);
        //生成标志文件
        chkFileUtil.createFlagFile(appCnl,flagName);
        //读取商户营业厅结算数据
        List<SettleOrderDO> orders= this.queryHallSettleData(chkDate,chkOrderStatus);

        //读取对账文件汇总信息项:
        Map<String,Object> headItemMap = new HashMap<>();
        BigDecimal totalFee = BigDecimal.valueOf(0);
        BigDecimal totalAmt = BigDecimal.valueOf(0);
        for(SettleOrderDO so : orders) {
            BigDecimal tmpFee = so.getFee();
            if(JudgeUtils.isNotNull(tmpFee)){
                totalFee = totalFee.add(tmpFee);
            }
            //申请提现金额，订单金额
            totalAmt = totalAmt.add(so.getTradeAmt());
        }
        headItemMap.put("count",orders.size());
        headItemMap.put("totalAmt",totalAmt);
        headItemMap.put("totalFee",totalFee);

        //生成文件
        writeToFile(appCnl, orders, chkFileName,headItemMap);
        logger.info("生成对账文件"+flagName+"完成，开始上传至SFTP");

        //上传服务器
        chkFileUtil.upload(appCnl, chkFileName, flagName);
        logger.info("对账文件"+flagName+"上传至SFTP完成");
    }

    /**
     * 定义写入对账文件的内容
     * @param appCnl
     * @param datas
     * @param fileName
     */
    private void writeToFile(String appCnl, List<SettleOrderDO> datas, String fileName, Map<String, Object> headItemMap) {
        final String itemSeperator = "|";
        final String lineSeparator = System.getProperty("line.separator", "\n");
        StringBuilder contextBuilder = new StringBuilder();
        //添加对账文件首行总明细(总笔数|总金额|总服务费)
        contextBuilder.append(headItemMap.get("count")).append(itemSeperator)
                .append(headItemMap.get("totalAmt")).append(itemSeperator)
                .append(headItemMap.get("totalFee")).append(lineSeparator);

        //营业厅结算订单号|平台结算订单号|结算商户id|结算金额|手续费|订单状态|订单日期|
        for (SettleOrderDO rdo : datas) {
            contextBuilder.append(rdo.getBusOrderNo()).append(itemSeperator)
                    .append(rdo.getOrderNo()).append(itemSeperator)
                    .append(rdo.getUserId()).append(itemSeperator)
                    .append(rdo.getTradeAmt()).append(itemSeperator)
                    .append(rdo.getFee()).append(itemSeperator)
                    .append(rdo.getStats()).append(itemSeperator)
                    .append(rdo.getBusOrderTime()).append(lineSeparator);
        }
        //写入文件
        try {
            String localPath = chkFileUtil.getLocalPath(appCnl);
            FileUtils.write(contextBuilder.toString(), localPath + fileName);
        } catch (Exception e) {
            LemonException.throwBusinessException("PWM40701");
        }

    }
    //自定义平台生成的营业厅提现对账的文件名
    private String getChkFileName(String appCnl,LocalDate chkDate){
        return LemonUtils.getApplicationName()+"_"+appCnl+"_SETTLE"+"_"+DateTimeUtils.formatLocalDate(chkDate,"yyyyMMdd")+".ck";
    }

    public List<SettleOrderDO> queryHallSettleData(LocalDate chkDate,String[] chkOrderStatus){
        Map queryDo=new HashMap<>();
        queryDo.put("acTm",chkDate);
        queryDo.put("statusList",chkOrderStatus);
        queryDo.put("busType","0405");
        return settleOrderDao.querySettleList(queryDo);
    }
}
