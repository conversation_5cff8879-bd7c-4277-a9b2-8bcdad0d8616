package com.hisun.lemon.csm.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csm.constants.Constants;
import com.hisun.lemon.csm.dao.ICheckControlDao;
import com.hisun.lemon.csm.dao.ICheckParamDao;
import com.hisun.lemon.csm.entity.CheckControlDO;
import com.hisun.lemon.csm.entity.CheckParamDO;
import com.hisun.lemon.csm.service.ICheckControlService;
import com.hisun.lemon.csm.service.ICheckProcessService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;

@Transactional
@Service
public class CheckControlServiceImpl implements ICheckControlService {

    @Resource
    private ICheckParamDao checkParamDao;
    @Resource
    private ICheckControlDao checkControlDao;
    @Resource
    private ICheckProcessService checkProcessService;

    @Override
    public boolean checkBatchRegister(LocalDate checkDate) throws LemonException {
        LocalDateTime tradeDateTime = DateTimeUtils.getCurrentLocalDateTime();
        String tradeDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
        boolean autoFlag = false;
        // 查询有效的对账参数
        CheckParamDO qryInCheckParamDO = new CheckParamDO();
        qryInCheckParamDO.setStats(Constants.STATUS_AVAILABLE);
        List<CheckParamDO> checkParamDOs = checkParamDao.getListByCondition(qryInCheckParamDO);
        if (JudgeUtils.isNotEmpty(checkParamDOs)) {
            for (CheckParamDO checkParamDO : checkParamDOs) {
                String checkTypeId = checkParamDO.getCheckTypeId();
                // 查询最大的对账批次序号
                int checkSeq = checkControlDao.getMaxCheckSeq(checkTypeId, checkDate);
                checkSeq = checkSeq + 1;
                // 查询对账批次是否存在
                CheckControlDO qryInCheckControlDO = new CheckControlDO();  
                qryInCheckControlDO.setCheckTypeId(checkTypeId);
                qryInCheckControlDO.setCheckDate(checkDate);
                List<CheckControlDO> checkControlDOs = checkControlDao.getListByCondition(qryInCheckControlDO);
                String multipleCheckFlg = checkParamDO.getMultipleCheckFlg();
                // 对账日期批次已经存在并且不为多次对账
                if (JudgeUtils.isNotEmpty(checkControlDOs) && multipleCheckFlg.equals("N")) {
                    continue;
                }
                // 登记对账批次
                CheckControlDO checkControlDO = new CheckControlDO();
                String checkBatchNoRandom = IdGenUtils.generateId("CSM_ORDER_NO", 8);
                String checkBatchNo = (LemonUtils.getApplicationName() + tradeDateTimeStr + checkBatchNoRandom);
                checkControlDO.setCheckBatchNo(checkBatchNo);
                checkControlDO.setCheckDate(checkDate);
                checkControlDO.setCheckSeq(checkSeq);
                checkControlDO.setCheckTypeId(checkTypeId);
                checkControlDO.setCheckBeginTime(tradeDateTime);
                checkControlDao.insert(checkControlDO);
                if("SETTLE01".equals(checkTypeId)){
                    autoFlag = true;
                }
            }
        }
        return autoFlag;
    }

    @Override
    public void checkBatchProcess(LocalDate checkDate) throws LemonException {
        LocalDateTime tradeDateTime = DateTimeUtils.getCurrentLocalDateTime();
        // 查询待处理的对账批次
        CheckControlDO qryInCheckControlDO = new CheckControlDO();
        qryInCheckControlDO.setCheckDate(checkDate);
        qryInCheckControlDO.setCheckStats(Constants.CHECK_FILE_OVER);
        List<CheckControlDO> checkControlDOs = checkControlDao.getUnfinishListByCondition(qryInCheckControlDO);
        if (JudgeUtils.isNotEmpty(checkControlDOs)) {
            for (CheckControlDO checkControlDO : checkControlDOs) {
                String checkBatchNo = checkControlDO.getCheckBatchNo();
                String checkTypeId = checkControlDO.getCheckTypeId();
                String checkStats = checkControlDO.getCheckStats();
                // 查询对账参数信息
                CheckParamDO qryInCheckParamDO = new CheckParamDO();
                qryInCheckParamDO.setCheckTypeId(checkTypeId);
                qryInCheckParamDO.setStats(Constants.STATUS_AVAILABLE);
                CheckParamDO qryOutCheckParamDO = checkParamDao.getByCondition(qryInCheckParamDO);
                if (JudgeUtils.isNull(qryOutCheckParamDO)) {
                    continue;
                }
                switch (checkStats) {
                case Constants.CHECK_FILE_INIT:
                    // 获取对账文件
                    checkStats = checkProcessService.checkFileGet(qryOutCheckParamDO, checkControlDO);
                case Constants.CHECK_FILE_GET:
                    // 文件入库
                    checkStats = checkProcessService.checkFileDatebase(qryOutCheckParamDO, checkControlDO);
                case Constants.CHECK_FILE_DATABASE:
                    // 对账处理
                    checkStats = checkProcessService.checkProcess(checkDate, qryOutCheckParamDO, checkControlDO);
                case Constants.CHECK_FILE_PROCESS:
                    // 差错处理
                    checkStats = checkProcessService.checkErrorProcess(qryOutCheckParamDO, checkControlDO);
                case Constants.CHECK_ERROR_PROCESS:
                    // 更新对账为已完成
                    CheckControlDO updateValueCheckControlDO = new CheckControlDO();
                    updateValueCheckControlDO.setCheckStats(Constants.CHECK_FILE_OVER);
                    updateValueCheckControlDO.setModifyTime(tradeDateTime);
                    updateValueCheckControlDO.setCheckEndTime(tradeDateTime);
                    CheckControlDO updateConditionCheckControlDO = new CheckControlDO();
                    updateConditionCheckControlDO.setCheckBatchNo(checkBatchNo);
                    checkControlDao.updateByCondition(updateValueCheckControlDO, updateConditionCheckControlDO);
                case Constants.CHECK_FILE_OVER:
                    break;
                default:
                    break;
                }
            }
        }
    }
}
