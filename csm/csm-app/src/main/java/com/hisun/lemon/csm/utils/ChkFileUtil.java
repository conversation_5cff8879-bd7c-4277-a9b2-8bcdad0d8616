package com.hisun.lemon.csm.utils;


import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csm.constants.MsgCdEnum;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.lemon.jcommon.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDate;

/**
 * 对账文件组件
 * 
 * <AUTHOR>
 *
 */
@Component
public class ChkFileUtil {
	private static final Logger logger = LoggerFactory.getLogger(ChkFileUtil.class);
	final String yyyyMMdd="yyyyMMdd";

	final int defaultSftpTimeout=2000;


	/**
	 * 获取对账数据日期
	 * @return
	 */
	public LocalDate getChkDate(){
		LocalDate today= DateTimeUtils.getCurrentLocalDate();
		return today.minusDays(1);
	}

	/**
	 * 获取对账文件名
	 * @param appCnl
	 * @param chkDate
	 * @return
	 */
	public String getChkFileName(String appCnl,LocalDate chkDate){
		return LemonUtils.getApplicationName()+"_"+appCnl+"_"+DateTimeUtils.formatLocalDate(chkDate,yyyyMMdd)+".ck";
	}


	/**
	 * 文件上传SFTP服务器
	 * @param chkFileName
	 * @param flagName
	 */
	public void upload(String appCnl,String chkFileName, String flagName){
		String localPath=getLocalPath(appCnl);
		String[] uploadFileNames=new String[]{localPath+chkFileName,localPath+flagName};

		String remoteIp=LemonUtils.getProperty("csm.sftp.ip");
		int remotePort=Integer.valueOf(LemonUtils.getProperty("csm.sftp.port"));
		String timeoutStr=LemonUtils.getProperty("csm.sftp.connectTimeout");
		int connectTimeout=defaultSftpTimeout;
		if(StringUtils.isNotEmpty(timeoutStr)){
			connectTimeout=Integer.valueOf(timeoutStr);
		}

		String remotePath=LemonUtils.getProperty("csm.chk.remotePath");

		String name=LemonUtils.getProperty("csm.sftp.name");
		String pwd=LemonUtils.getProperty("csm.sftp.password");

		try {
			FileSftpUtils.upload(uploadFileNames,remoteIp,remotePort,connectTimeout,remotePath,name,pwd);
		} catch (Exception e) {
			logger.error(chkFileName+"上传SFTP文件服务器失败",e);
			LemonException.throwBusinessException(MsgCdEnum.UPLOAD_CHECK_FILE_FAILURE.getMsgCd());
		}
	}


	public boolean isStart(String appCnl,String flagName){
		return new File(getLocalPath(appCnl)+flagName).exists();
	}

	public void createFlagFile(String appCnl,String flagName){
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write("flag", localPath+flagName);
		} catch (Exception e) {
			LemonException.throwBusinessException(MsgCdEnum.CHECK_FLAG_FILE_CREATE_FAILURE.getMsgCd());
		}
	}

	/**
	 * 获取本地存放对账文件的目录，没有则创建
	 * @param appCnl
	 * @return
	 */
	public String getLocalPath(String appCnl){
		String localPath=LemonUtils.getProperty("csm.chk.localPath")+appCnl+"/";
		File localPathFile=new File(localPath);
		if(localPathFile.exists()){
			if(localPathFile.isDirectory()){
				return localPath;
			}
		}
		boolean success=localPathFile.mkdirs();
		if(!success){
			logger.error(localPath+"目录创建失败，任务退出");
			LemonException.throwBusinessException(MsgCdEnum.FAIL_GET_LOCAL_PATH.getMsgCd());
		}
		return localPath;
	}

}
