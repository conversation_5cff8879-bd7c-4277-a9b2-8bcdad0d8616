package com.hisun.lemon.csm.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hisun.lemon.acm.dto.UserAccountFreezeDTO;
import com.hisun.lemon.bil.dto.CreateUserBillDTO;
import com.hisun.lemon.cmm.dto.MessageSendReqDTO;
import com.hisun.lemon.cpo.dto.WithdrawHallReqDTO;
import com.hisun.lemon.csm.mq.BillSyncHandler;
import com.hisun.lemon.csm.mq.MerchantSettleHandler;
import com.hisun.lemon.framework.i18n.LocaleMessageSource;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.urm.dto.CheckPayPwdSeaDTO;
import com.hisun.lemon.urm.dto.CprItfAuthDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.CommonEncryptReqDTO;
import com.hisun.lemon.cmm.dto.CommonEncryptRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpo.client.WithdrawClient;
import com.hisun.lemon.cpo.dto.WithdrawReqDTO;
import com.hisun.lemon.cpo.dto.WithdrawResDTO;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import com.hisun.lemon.csm.constants.Constants;
import com.hisun.lemon.csm.constants.MsgCdEnum;
import com.hisun.lemon.csm.dao.ICheckControlDao;
import com.hisun.lemon.csm.dao.ICheckParamDao;
import com.hisun.lemon.csm.dao.IFileConfigDao;
import com.hisun.lemon.csm.dao.ISettleBaseDao;
import com.hisun.lemon.csm.dao.ISettleCardDao;
import com.hisun.lemon.csm.dao.ISettleOrderDao;
import com.hisun.lemon.csm.dto.SettleApplyReqDTO;
import com.hisun.lemon.csm.dto.SettleApplyRspDTO;
import com.hisun.lemon.csm.dto.SettleBanlanceReqDTO;
import com.hisun.lemon.csm.dto.SettleBanlanceRspDTO;
import com.hisun.lemon.csm.dto.SettleConfirmReqDTO;
import com.hisun.lemon.csm.dto.SettleHallApplyReqDTO;
import com.hisun.lemon.csm.dto.SettleHallApplyRspDTO;
import com.hisun.lemon.csm.dto.SettleInformationRspDTO;
import com.hisun.lemon.csm.dto.SettleListReqDTO;
import com.hisun.lemon.csm.dto.SettleListReqDTO2;
import com.hisun.lemon.csm.dto.SettleListRspDTO;
import com.hisun.lemon.csm.dto.SettleListRspDTO2;
import com.hisun.lemon.csm.dto.SettleOrderDetailReqDTO;
import com.hisun.lemon.csm.dto.SettleOrderDetailRspDTO;
import com.hisun.lemon.csm.dto.SettleOrderListRspDTO;
import com.hisun.lemon.csm.dto.SettleOrderResultReqDTO;
import com.hisun.lemon.csm.dto.SettleOrderResultRspDTO;
import com.hisun.lemon.csm.dto.SettleRepairReqDTO;
import com.hisun.lemon.csm.entity.CheckControlDO;
import com.hisun.lemon.csm.entity.CheckParamDO;
import com.hisun.lemon.csm.entity.FileConfigDO;
import com.hisun.lemon.csm.entity.FileTotalDO;
import com.hisun.lemon.csm.entity.SettleBaseDO;
import com.hisun.lemon.csm.entity.SettleCardDO;
import com.hisun.lemon.csm.entity.SettleOrderDO;
import com.hisun.lemon.csm.service.ICsmService;
import com.hisun.lemon.csm.utils.SensInfoUtil;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.lemon.jcommon.file.FileUtils;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.tfm.client.TfmServerClient;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateReqDTO;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateRspDTO;
import com.hisun.lemon.tfm.dto.MerchantRateReqDTO;
import com.hisun.lemon.tfm.dto.MerchantRateRspDTO;
import com.hisun.lemon.urm.client.UserAuthenticationClient;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.CheckPayPwdDTO;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;

@Transactional
@Service
public class CsmServiceImpl extends BaseService implements ICsmService {

    private static final Logger logger = LoggerFactory.getLogger(CsmServiceImpl.class);

    @Resource
    private ISettleBaseDao settleBaseDao;
    @Resource
    private ISettleCardDao settleCardDao;
    @Resource
    private ISettleOrderDao settleOrderDao;
    @Resource
    private CmmServerClient cmmServerClient;
    @Resource
    private UserBasicInfClient userBasicInfClient;
    @Resource
    private UserAuthenticationClient userAuthenticationClient;
    @Resource
    private RiskCheckClient riskCheckClient;
    @Resource
    private AccountManagementClient accountManagementClient;
    @Resource
    private AccountingTreatmentClient accountingTreatmentClient;
    @Resource
    private TfmServerClient tfmServerClient;
    @Resource
    private ICheckControlDao checkControlDao;
    @Resource
    private ICheckParamDao checkParamDao;
    @Resource
    private IFileConfigDao fileConfigDao;
    @Resource
    private WithdrawClient withdrawClient;


    @Resource
    private MerchantSettleHandler merchantSettleHandler;

    @Resource
    protected WithdrawClient withdrawCpoClient;

    @Resource
    BillSyncHandler billSyncHandler;

    @Resource
    LocaleMessageSource localeMessageSource;


    @Override
    public SettleInformationRspDTO settleInformation(String mercId) throws LemonException {
        SettleInformationRspDTO settleInformationRspDTO = new SettleInformationRspDTO();
        String userId = LemonUtils.getUserId();
        if(JudgeUtils.isEmpty(userId)){
            userId = mercId;
        }
        logger.info("userId :" + userId);
        if(JudgeUtils.isEmpty(userId)){
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_INFO_NOTEXISTS.getMsgCd());
        }
        
        // 查询结算账户余额
        BigDecimal settleAccountBal = getAccountBal(userId, false);
        // 查询用户结算信息
        SettleBaseDO settleBaseDO = settleBaseDao.get(userId);
        if (JudgeUtils.isNull(settleBaseDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_INFO_NOTEXISTS.getMsgCd());
        }
        // 结算类型
        String settleType = settleBaseDO.getSettleType();
        if (settleType.equals(Constants.SETTLE_TYPE_SELF)) {
            settleType = Constants.SETTLE_BUSTYPE_SELF;
        } else if (settleType.equals(Constants.SETTLE_TYPE_AUTO)) {
            settleType = Constants.SETTLE_BUSTYPE_AUTO;
        } else {
            settleType = Constants.SETTLE_BUSTYPE_HALL;
        }
        // 自主结算查询费率信息
        if (settleType.equals(Constants.SETTLE_BUSTYPE_SELF)) {
            // 查询结算费率信息
            MerchantRateRspDTO rspDTO = getSettleRate(userId, Constants.SETTLE_BUSTYPE_SELF);
            // 计费起始金额
            BigDecimal calculateMinAmt = rspDTO.getCalculateMinAmt();
            // 最低收取手续费
            BigDecimal minFee = rspDTO.getMinFee();
            // 最高收取手续费
            BigDecimal maxFee = rspDTO.getMaxFee();
            // 计费方式
            String calculateType = rspDTO.getCaculateType();
            // 固定手续费
            BigDecimal fixFee = rspDTO.getFixFee();
            // 费率
            BigDecimal rate = rspDTO.getRate();
            settleInformationRspDTO.setCalculateType(calculateType);
            settleInformationRspDTO.setCalculateMinAmt(calculateMinAmt);
            settleInformationRspDTO.setMinFee(minFee);
            settleInformationRspDTO.setMaxFee(maxFee);
            settleInformationRspDTO.setRate(rate);
            settleInformationRspDTO.setFixFee(fixFee);
        }
        // 非营业厅提现查询结算银行卡信息
        if (!(settleType.equals(Constants.SETTLE_BUSTYPE_HALL))) {
            // 查询结算银行卡信息
            SettleCardDO qryInSettleCardDO = new SettleCardDO();
            qryInSettleCardDO.setUserId(userId);
            qryInSettleCardDO.setStats(Constants.STATUS_AVAILABLE);
            SettleCardDO qryOutSettleCardDO = settleCardDao.getByCondition(qryInSettleCardDO);
            if (JudgeUtils.isNull(qryOutSettleCardDO)) {
                LemonException.throwBusinessException(MsgCdEnum.SETTLE_CARD_NOTEXISTS.getMsgCd());
            }
            String capCorgNm = qryOutSettleCardDO.getCapCorgNm();
            String capCardNo = qryOutSettleCardDO.getCapCardNo();
            // 银行卡号解密
            capCardNo = encrypt(Constants.DECRYPT, capCardNo);
            try {
                // 银行卡号脱敏
                capCardNo = SensInfoUtil.cardSensInfo(capCardNo);
            } catch (LemonException e) {
                LemonException.throwBusinessException(MsgCdEnum.CARD_DESENS_FAIL.getMsgCd());
            }
            settleInformationRspDTO.setCapCorgNm(capCorgNm);
            settleInformationRspDTO.setCapCardNo(capCardNo);
        }
        UserBasicInfDTO userBasicInfDTO = userBasicInfClient.queryUser(userId).getBody();
        settleInformationRspDTO.setSettleAccountBal(settleAccountBal);
        settleInformationRspDTO.setSettleType(settleType);
        settleInformationRspDTO.setCrpIdNo(userBasicInfDTO.getCrpIdNo());
        settleInformationRspDTO.setCrpIdTyp(userBasicInfDTO.getCrpIdTyp());
        return settleInformationRspDTO;
    }

    @Override
    public SettleBanlanceRspDTO settleBanlance(SettleBanlanceReqDTO settleBanlanceReqDTO) throws LemonException {
        SettleBanlanceRspDTO settleBanlanceRspDTO = new SettleBanlanceRspDTO();
        String userId = settleBanlanceReqDTO.getUserId();
        // 查询结算账户余额
        BigDecimal settleAccountBal = getAccountBal(userId, false);
        settleBanlanceRspDTO.setSettleAccountBal(settleAccountBal);
        return settleBanlanceRspDTO;
    }

    @Override
    public SettleApplyRspDTO settleApply(SettleApplyReqDTO settleApplyReqDTO) throws LemonException {
        SettleApplyRspDTO settleApplyRspDTO = new SettleApplyRspDTO();
        String payPassword = settleApplyReqDTO.getPayPassword();
        String payPwdRandom = settleApplyReqDTO.getPayPwdRandom();
        String userId = LemonUtils.getUserId();
        String loginName = LemonUtils.getLoginName();
        BigDecimal tradeAmt = settleApplyReqDTO.getSettleAmt();
        BigDecimal tradeFee = settleApplyReqDTO.getFee();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();

        // 判断交易金额
        if (tradeAmt.compareTo(BigDecimal.ZERO) < 1) {
            LemonException.throwBusinessException(MsgCdEnum.TRADE_AMT_ZERO.getMsgCd());
        }
        // 查询用户基本信息
        UserBasicInfDTO userBasicInfDTO = getUserBasicInfo(userId);

        //判断商户是否有结算权限
        CprItfAuthDTO cprItfAuthDTO = new CprItfAuthDTO() ;
        cprItfAuthDTO.setItfNm(Constants.CPRITFAUTH);
        cprItfAuthDTO.setUserId(userBasicInfDTO.getUserId());
        cprItfAuthDTO.setSts("1");
        cprItfAuthDTO.setVersion("1.0");
        GenericDTO<CprItfAuthDTO> genericDTO = GenericDTO.newInstance(cprItfAuthDTO);
        GenericRspDTO<CprItfAuthDTO> genericRspDTO = userBasicInfClient.queryTradingPrivilege(genericDTO);
        cprItfAuthDTO = genericRspDTO.getBody() ;
        if ( !("URM00000".equals(genericRspDTO.getMsgCd()) && cprItfAuthDTO != null && "1".equals(cprItfAuthDTO.getSts()) )) {
            logger.info("user :" + userId + " no settle auth!");
            LemonException.throwBusinessException(MsgCdEnum.NO_SETTLE_AUTH.getMsgCd());
        }

        // 查询用户结算信息
        logger.info("结算信息查询");
        SettleBaseDO settleBaseDO = settleBaseDao.vaildSettle(userId);
        if (JudgeUtils.isNull(settleBaseDO)) {
            logger.info("结算信息为空");
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_INFO_NOTEXISTS.getMsgCd());
        }
        String userName = settleBaseDO.getUserName();
        String mblNo = settleBaseDO.getMblNo();
        String settleType = settleBaseDO.getSettleType();
        // 检查用户状态
        checkUserStatus(userId);
        // 判断结算方式
        if (!StringUtils.equals(settleType, Constants.SETTLE_TYPE_SELF)) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_TYPE_ERROR.getMsgCd());
        }
        // 查询结算账户余额
        BigDecimal settleAccountBal = getAccountBal(userId, false);
        if (tradeAmt.compareTo(settleAccountBal) > 0) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_ACOUNT_INSUFF.getMsgCd());
        }
        // 校验支付密码
        checkPayPassword(userId, payPassword, payPwdRandom, "");

        // 手续费预算
        BigDecimal fee = merchanFeeCalculate(userId, Constants.SETTLE_BUSTYPE_SELF, tradeAmt);
        if (fee.compareTo(tradeFee) != 0) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_FEE_ERROR.getMsgCd());
        }
        // 真实结算金额
        BigDecimal realTradeAmt = tradeAmt.subtract(fee);
        if (realTradeAmt.compareTo(BigDecimal.ZERO) <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.REAL_AMT_ZERO.getMsgCd());
        }
        // 查询用户结算银行卡信息
        SettleCardDO qryInSettleCardDO = new SettleCardDO();
        qryInSettleCardDO.setUserId(userId);
        qryInSettleCardDO.setStats(Constants.STATUS_AVAILABLE);
        SettleCardDO settleCardDO = settleCardDao.getByCondition(qryInSettleCardDO);
        if (JudgeUtils.isNull(settleCardDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_CARD_NOTEXISTS.getMsgCd());
        }
        String capCorgNm = settleCardDO.getCapCorgNm();
        String capCorgSnm = settleCardDO.getCapCorgSnm();
        String capCorgNo = settleCardDO.getCapCorgNo();
        String capCardNo = settleCardDO.getCapCardNo();
        String capCardNm = settleCardDO.getCapCardName();
        String bnkMblNo = settleCardDO.getBnkMblNo();
        LocalDate agreementPayDate = tradeDate;
        String agreementPayDays = settleBaseDO.getAgreementPayDays();
        if (isInteger(agreementPayDays)) {
            agreementPayDate = agreementPayDate.plusDays(Integer.parseInt(agreementPayDays));
        }
        // 登记结算订单
        SettleOrderDO settleOrderDO = new SettleOrderDO();
        String settleOrderNo = generateOrderNo();
        settleOrderDO.setOrderNo(settleOrderNo);
        settleOrderDO.setTradeDate(tradeDate);
        settleOrderDO.setTradeTime(tradeTime);
        settleOrderDO.setAgreementPayDate(agreementPayDate);
        settleOrderDO.setTradeAmt(tradeAmt);
        settleOrderDO.setFee(fee);
        settleOrderDO.setCcy("USD");
        // 0403自主结算 0404自动结算 0405营业厅结算
        settleOrderDO.setBusType(Constants.SETTLE_BUSTYPE_SELF);
        settleOrderDO.setUserId(userId);
        settleOrderDO.setUserName(userName);
        settleOrderDO.setMblNo(mblNo);
        settleOrderDO.setCapCorgNm(capCorgNm);
        settleOrderDO.setCapCorgSnm(capCorgSnm);
        settleOrderDO.setCapCorgNo(capCorgNo);
        settleOrderDO.setCapCardNo(capCardNo);
        settleOrderDO.setCapCardNm(capCardNm);
        settleOrderDO.setBnkMblNo(bnkMblNo);
        settleOrderDO.setStats("S1");
        settleOrderDO.setLastLastSettleDay(settleBaseDO.getLastSettleDay());
        settleOrderDO.setLastSettleDay(settleBaseDO.getNextSettleDay());
        settleOrderDO.setLoginName(loginName);
        // 银行卡号解密
        capCardNo = encrypt(Constants.DECRYPT, capCardNo);
        try {
            // 银行卡号脱敏
            capCardNo = SensInfoUtil.cardSensInfo(capCardNo);
        } catch (LemonException e) {
            LemonException.throwBusinessException(MsgCdEnum.CARD_DESENS_FAIL.getMsgCd());
        }
        String txJrnNo = generateOrderNo();
        // 账务处理
        settleAccountTreat(userId, Constants.AC_TXSTS_N, txJrnNo, Constants.SETTLE_TYPE_SELF, settleOrderDO);
        try {
            // 登记结算订单
            registerSettleOrder(settleOrderDO);
            // 登记提现订单
            String withdrawOrderNo = registerWithdrawOrder(realTradeAmt, userBasicInfDTO, settleBaseDO, settleCardDO,
                    settleOrderDO);
            settleOrderDO.setBusOrderNo(withdrawOrderNo);
            // 更新提现订单号
            SettleOrderDO updateValueDO = new SettleOrderDO();
            updateValueDO.setBusOrderNo(withdrawOrderNo);
            SettleOrderDO updateConditionDO = new SettleOrderDO();
            updateConditionDO.setOrderNo(settleOrderNo);
            updateSettleOrder(updateValueDO, updateConditionDO);
        } catch (LemonException e) {
            // 账务冲正处理
            settleAccountTreat(userId, Constants.AC_TXSTS_C, txJrnNo, Constants.SETTLE_TYPE_SELF, settleOrderDO);
            LemonException.throwBusinessException(e.getMsgCd());
        }
        BeanUtils.copyProperties(settleApplyRspDTO, settleOrderDO);
        settleApplyRspDTO.setTradeFee(fee);
        settleApplyRspDTO.setReceiveAmt(realTradeAmt);
        settleApplyRspDTO.setCapCardNo(capCardNo);
        settleApplyRspDTO.setAgreePaymentDate(agreementPayDate);
        settleApplyRspDTO.setStats(Constants.ORDER_STATUS_ACCEPT);
        return settleApplyRspDTO;
    }

    @Override
    public SettleHallApplyRspDTO settleHallApply(SettleHallApplyReqDTO settleHallApplyReqDTO) throws LemonException {
        SettleHallApplyRspDTO settleHallApplyRspDTO = new SettleHallApplyRspDTO();
        //营业厅商户号
        String merchantId = settleHallApplyReqDTO.getMerchantId();
        //营业厅名称
        String merchantName = settleHallApplyReqDTO.getMerchantName();
        //结算商户号
        String userId = settleHallApplyReqDTO.getUserId();
        String orderCcy = settleHallApplyReqDTO.getOrderCcy();

        SettleOrderDO queryHallSettleOrderDO = new SettleOrderDO();
        queryHallSettleOrderDO.setBusOrderNo(settleHallApplyReqDTO.getBusOrderNo());
        //重复业务订单号下单校验
        if(this.settleOrderDao.getListByCondition(queryHallSettleOrderDO).stream().filter(settleOrderDO -> settleOrderDO != null).count() > 0){
            LemonException.throwBusinessException("CSM30016");
        }

        //判断商户是否有结算权限
        CprItfAuthDTO cprItfAuthDTO = new CprItfAuthDTO() ;
        cprItfAuthDTO.setItfNm(Constants.CPRITFAUTH);
        cprItfAuthDTO.setUserId(userId);
        cprItfAuthDTO.setSts("1");
        cprItfAuthDTO.setVersion("1.0");
        GenericDTO<CprItfAuthDTO> genericDTO = GenericDTO.newInstance(cprItfAuthDTO);
        GenericRspDTO<CprItfAuthDTO> genericRspDTO = userBasicInfClient.queryTradingPrivilege(genericDTO);
        cprItfAuthDTO = genericRspDTO.getBody() ;
        if ( !("URM00000".equals(genericRspDTO.getMsgCd()) && cprItfAuthDTO != null && "1".equals(cprItfAuthDTO.getSts()) )) {
            logger.info("user :" + userId + " no settle auth!");
            LemonException.throwBusinessException(MsgCdEnum.NO_SETTLE_AUTH.getMsgCd());
        }

        String payPassword = settleHallApplyReqDTO.getPayPassword();
        String payPwdRandom = settleHallApplyReqDTO.getPayPwdRandom();
        String seaRandom = settleHallApplyReqDTO.getSeaRandom();
        //结算金额
        BigDecimal tradeAmt = settleHallApplyReqDTO.getSettleAmt();
        //结算日期
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        // 判断交易金额
        if (tradeAmt.compareTo(BigDecimal.ZERO) < 1) {
            LemonException.throwBusinessException(MsgCdEnum.TRADE_AMT_ZERO.getMsgCd());
        }
        // 查询用户结算信息
        SettleBaseDO settleBaseDO = settleBaseDao.vaildSettle(userId);
        if (JudgeUtils.isNull(settleBaseDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_INFO_NOTEXISTS.getMsgCd());
        }
        String userName = settleBaseDO.getUserName();
        String mblNo = settleBaseDO.getMblNo();
        String settleType = settleBaseDO.getSettleType();
        // 检查用户状态
        checkUserStatus(userId);
        // 判断结算方式
        if (!StringUtils.equals(settleType, Constants.SETTLE_TYPE_HALL)) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_TYPE_ERROR.getMsgCd());
        }
        // 查询结算账户余额
        BigDecimal settleAccountBal = getAccountBal(userId, false);
        if (tradeAmt.compareTo(settleAccountBal) > 0) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_ACOUNT_INSUFF.getMsgCd());
        }
        // 校验支付密码
        checkPayPassword(userId, payPassword, payPwdRandom, seaRandom);
        // 手续费预算
        BigDecimal fee = merchanFeeCalculate(userId, Constants.SETTLE_BUSTYPE_HALL, tradeAmt);
        // 真实结算金额
        BigDecimal realTradeAmt = tradeAmt.subtract(fee);
        if (realTradeAmt.compareTo(BigDecimal.ZERO) <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.REAL_AMT_ZERO.getMsgCd());
        }
        // 登记结算订单
        SettleOrderDO settleOrderDO = new SettleOrderDO();
        String settleOrderNo = generateOrderNo();
        settleOrderDO.setOrderNo(settleOrderNo);
        settleOrderDO.setTradeDate(tradeDate);
        settleOrderDO.setTradeTime(tradeTime);
        settleOrderDO.setMessage("hall:" + merchantId + " : " + merchantName);
        settleOrderDO.setAgreementPayDate(tradeDate);
        settleOrderDO.setTradeAmt(tradeAmt);
        settleOrderDO.setFee(fee);
        if(JudgeUtils.isNotNull(orderCcy)){
            settleOrderDO.setCcy(orderCcy);
        }else{
            settleOrderDO.setCcy("USD");
        }
        settleOrderDO.setBusOrderNo(settleHallApplyReqDTO.getBusOrderNo());
        settleOrderDO.setBusOrderTime(DateTimeUtils.getCurrentLocalDateTime());
        // 0403自主结算 0404自动结算 0405营业厅结算
        settleOrderDO.setBusType(Constants.SETTLE_BUSTYPE_HALL);
        settleOrderDO.setUserId(userId);
        settleOrderDO.setUserName(userName);
        settleOrderDO.setMblNo(mblNo);
        settleOrderDO.setStats(Constants.ORDER_STATUS_DATABASE_SUCCESS);
        settleOrderDO.setLastLastSettleDay(settleBaseDO.getLastSettleDay());
        settleOrderDO.setLastSettleDay(settleBaseDO.getNextSettleDay());
        settleOrderDO.setPaymentDate(tradeDate);
        settleOrderDO.setPaymentTime(tradeTime);
        String txJrnNo = generateOrderNo();
        // 账务处理
        hallSettleAccountTreat(userId, Constants.AC_TXSTS_N, txJrnNo, realTradeAmt , settleOrderDO);
        try {
            // 登记结算订单
            registerSettleOrder(settleOrderDO);
            //登记一笔不清分商户服务费
            merchantSettleHandler.registMerChantTransferFee(settleOrderDO);
            //同步订单登记到资金处理模块cpo
            GenericDTO<WithdrawHallReqDTO> genericHallDTO = new GenericDTO<>();
            WithdrawHallReqDTO withdrawHallReqDTO = new WithdrawHallReqDTO();
            withdrawHallReqDTO.setUserNo(userId);
            withdrawHallReqDTO.setUserNm(userName);
            withdrawHallReqDTO.setCapCrdNm(userName);
            withdrawHallReqDTO.setCcy(settleOrderDO.getCcy());
            withdrawHallReqDTO.setCorpBusTyp(CorpBusTyp.WITHDRAW);
            withdrawHallReqDTO.setCorpBusSubTyp(CorpBusSubTyp.MERC_HALL_WITHDRAW);
            withdrawHallReqDTO.setPsnCrpFlg("B");
            withdrawHallReqDTO.setCapTyp("1");  //1 现金
            withdrawHallReqDTO.setAgrPayDt(DateTimeUtils.getCurrentLocalDate());
            withdrawHallReqDTO.setReqOrdNo(settleOrderDO.getOrderNo());
            withdrawHallReqDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
            withdrawHallReqDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());
            withdrawHallReqDTO.setWcAplAmt(settleOrderDO.getTradeAmt());
            withdrawHallReqDTO.setWcRmk("商户号:" + userId + "用户营业厅：" + merchantName + "取现");
            genericHallDTO.setBody(withdrawHallReqDTO);
            try{
                GenericRspDTO<WithdrawResDTO> withdrawenericRspDTO = withdrawCpoClient.createHallOrder(genericHallDTO);
                if(JudgeUtils.isNotSuccess(withdrawenericRspDTO.getMsgCd())){
                    logger.error("商户营业厅结算订单号:" + settleOrderDO.getOrderNo() +",订单同步资金能力cpo失败。");
                }
            }catch (Exception e){
                logger.error("商户营业厅结算订单号:" + settleOrderDO.getOrderNo() +",订单同步资金能力cpo失败。");
            }
        } catch (LemonException e) {
            // 账务冲正处理
            hallSettleAccountTreat(userId, Constants.AC_TXSTS_C, txJrnNo, realTradeAmt, settleOrderDO);
            LemonException.throwBusinessException(e.getMsgCd());
        }

        //商户营业厅结算账单同步
        Object[] args=new Object[]{settleOrderDO.getTradeAmt()};
        String desc = getViewOrderInfo(settleOrderDO.getBusType(),args);

        syncHallSettleBil(merchantId,merchantName,desc,settleOrderDO);

        BeanUtils.copyProperties(settleHallApplyRspDTO, settleOrderDO);
        settleHallApplyRspDTO.setMerchantId(userId);
        settleHallApplyRspDTO.setTradeFee(fee);
        settleHallApplyRspDTO.setSettleAmt(settleOrderDO.getTradeAmt());
        return settleHallApplyRspDTO;
    }

    @Override
    public void settleConfirm(SettleConfirmReqDTO settleConfirmReqDTO) throws LemonException {
        String busOrderNo = settleConfirmReqDTO.getOrderNo();
        String stats = settleConfirmReqDTO.getStats();
        String message = settleConfirmReqDTO.getMessage();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        // 查询结算订单信息
        SettleOrderDO qryInsettleOrderDO = new SettleOrderDO();
        qryInsettleOrderDO.setBusOrderNo(busOrderNo);
        SettleOrderDO settleOrderDO = settleOrderDao.getByCondition(qryInsettleOrderDO);
        if (JudgeUtils.isNull(settleOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.ORDER_NOT_EXISTS.getMsgCd());
        }
        String settleOrderNo = settleOrderDO.getOrderNo();
        String userId = settleOrderDO.getUserId();
        // 转换订单状态
        if (stats.equals(Constants.ORDER_STATUS_ACCEPT)) {
            stats = Constants.ORDER_STATUS_DATABASE_ACCEPT;
        } else if (stats.equals(Constants.ORDER_STATUS_SUCCESS)) {
            stats = Constants.ORDER_STATUS_DATABASE_SUCCESS;
        } else {
            stats = Constants.ORDER_STATUS_DATABASE_FAILURE;
        }
        String txJrnNo = generateOrderNo();
        // 账务处理
        settleConfirmAccountTreat(userId, Constants.AC_TXSTS_N, txJrnNo, stats, settleOrderDO);
        try {
            // 更新订单状态
            SettleOrderDO updateValueDO = new SettleOrderDO();
            updateValueDO.setStats(stats);
            updateValueDO.setMessage(message);
            updateValueDO.setPaymentDate(LemonUtils.getAccDate());
            updateValueDO.setPaymentTime(tradeTime);
            SettleOrderDO updateConditionDO = new SettleOrderDO();
            updateConditionDO.setOrderNo(settleOrderNo);
            updateConditionDO.setStats(Constants.ORDER_STATUS_DATABASE_ACCEPT);
            updateSettleOrder(updateValueDO, updateConditionDO);
        } catch (LemonException e) {
            settleConfirmAccountTreat(userId, Constants.AC_TXSTS_C, txJrnNo, stats, settleOrderDO);
            LemonException.throwBusinessException(e.getMsgCd());
        }
        try {
            SettleOrderDO settleDO = settleOrderDao.get(settleOrderNo);
            sendMsgCenterInfo(settleDO.getLoginName(), settleDO.getTradeAmt().setScale(2, RoundingMode.DOWN) + "", settleConfirmReqDTO.getStats());
        } catch (LemonException e) {
            logger.error("Message send error !");
        }
        // 订单状态为成功，则登记商户服务费，状态为不清分
        if (stats.equals(Constants.ORDER_STATUS_DATABASE_SUCCESS)) {
            merchantSettleHandler.registMerChantTransferFee(settleOrderDO);
        }
    }

    @Override
    public void orderRepairProcess(SettleRepairReqDTO settleRepairReqDTO) throws LemonException {
        String busOrderNo = settleRepairReqDTO.getOrderNo();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        // 查询结算订单信息
        SettleOrderDO qryInsettleOrderDO = new SettleOrderDO();
        qryInsettleOrderDO.setBusOrderNo(busOrderNo);
        qryInsettleOrderDO.setStats(Constants.ORDER_STATUS_DATABASE_ACCEPT);
        SettleOrderDO settleOrderDO = settleOrderDao.getByCondition(qryInsettleOrderDO);
        if (JudgeUtils.isNull(settleOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.ORDER_NOT_EXISTS.getMsgCd());
        }
        String settleOrderNo = settleOrderDO.getOrderNo();
        String userId = settleOrderDO.getUserId();
        // 补单订单状态
        String stats = Constants.ORDER_STATUS_DATABASE_SUCCESS;
        String txJrnNo = generateOrderNo();
        // 账务处理
        settleConfirmAccountTreat(userId, Constants.AC_TXSTS_N, txJrnNo, stats, settleOrderDO);
        try {
            // 更新订单状态
            SettleOrderDO updateValueDO = new SettleOrderDO();
            updateValueDO.setStats(stats);
            updateValueDO.setPaymentDate(LemonUtils.getAccDate());
            updateValueDO.setPaymentTime(tradeTime);
            SettleOrderDO updateConditionDO = new SettleOrderDO();
            updateConditionDO.setOrderNo(settleOrderNo);
            updateConditionDO.setStats(Constants.ORDER_STATUS_DATABASE_ACCEPT);
            updateSettleOrder(updateValueDO, updateConditionDO);
            // 提现成功且为自动结算订单更新用户结算日信息
            settleInfoUpdate(settleOrderDO);
        } catch (LemonException e) {
            settleConfirmAccountTreat(userId, Constants.AC_TXSTS_C, txJrnNo, stats, settleOrderDO);
            LemonException.throwBusinessException(e.getMsgCd());
        }
    }

    @Override
    public void orderRevokeProcess(SettleRepairReqDTO settleRepairReqDTO) throws LemonException {
        String busOrderNo = settleRepairReqDTO.getOrderNo();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        // 查询结算订单信息
        SettleOrderDO qryInsettleOrderDO = new SettleOrderDO();
        qryInsettleOrderDO.setBusOrderNo(busOrderNo);
        qryInsettleOrderDO.setStats(Constants.ORDER_STATUS_DATABASE_ACCEPT);
        SettleOrderDO settleOrderDO = settleOrderDao.getByCondition(qryInsettleOrderDO);
        if (JudgeUtils.isNull(settleOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.ORDER_NOT_EXISTS.getMsgCd());
        }
        String settleOrderNo = settleOrderDO.getOrderNo();
        String userId = settleOrderDO.getUserId();
        // 撤单订单状态
        String stats = Constants.ORDER_STATUS_DATABASE_FAILURE;
        String txJrnNo = generateOrderNo();
        // 账务处理
        settleConfirmAccountTreat(userId, Constants.AC_TXSTS_N, txJrnNo, stats, settleOrderDO);
        try {
            // 更新订单状态
            SettleOrderDO updateValueDO = new SettleOrderDO();
            updateValueDO.setStats(stats);
            updateValueDO.setPaymentDate(LemonUtils.getAccDate());
            updateValueDO.setPaymentTime(tradeTime);
            SettleOrderDO updateConditionDO = new SettleOrderDO();
            updateConditionDO.setOrderNo(settleOrderNo);
            updateConditionDO.setStats(Constants.ORDER_STATUS_DATABASE_ACCEPT);
            updateSettleOrder(updateValueDO, updateConditionDO);
        } catch (LemonException e) {
            settleConfirmAccountTreat(userId, Constants.AC_TXSTS_C, txJrnNo, stats, settleOrderDO);
            LemonException.throwBusinessException(e.getMsgCd());
        }
    }

    @Override
    public SettleListRspDTO settleOrderList(SettleListReqDTO settleListReqDTO) throws LemonException {
        SettleListRspDTO settleListRspDTO = new SettleListRspDTO();
        String userId = LemonUtils.getUserId();
        String stats = settleListReqDTO.getStats();
        String cycleType = settleListReqDTO.getCycleType();
        LocalDate beginDate = DateTimeUtils.getCurrentLocalDate();
        LocalDate endDate = DateTimeUtils.getCurrentLocalDate();
        // 转换订单状态
        if (stats.equals(Constants.ORDER_STATUS_ACCEPT)) {
            stats = Constants.ORDER_STATUS_DATABASE_ACCEPT;
        } else if (stats.equals(Constants.ORDER_STATUS_SUCCESS)) {
            stats = Constants.ORDER_STATUS_DATABASE_SUCCESS;
        } else if (stats.equals(Constants.ORDER_STATUS_FAILURE)) {
            stats = Constants.ORDER_STATUS_DATABASE_FAILURE;
        } else {
            stats = null;
        }
        // 计算查询周期
        if (cycleType.equals(Constants.TIME_CYCLE_WEEK)) {
            // 一周
            beginDate = beginDate.minusWeeks(1);
        } else if (cycleType.equals(Constants.TIME_CYCLE_MONTH)) {
            // 一月
            beginDate = beginDate.minusMonths(1);
        }
        SettleOrderDO qryInSettleOrderDO = new SettleOrderDO();
        qryInSettleOrderDO.setUserId(userId);
        qryInSettleOrderDO.setStats(stats);
        qryInSettleOrderDO.setBeginDate(beginDate);
        qryInSettleOrderDO.setEndDate(endDate);
        // 查询结算订单信息列表
        List<SettleOrderDO> settleOrderDOs = PageUtils.pageQuery(settleListReqDTO.getPageNum(),
                settleListReqDTO.getPageSize(), false,
                () -> this.settleOrderDao.getListByCondition(qryInSettleOrderDO));
        if (JudgeUtils.isNull(settleOrderDOs)) {
            settleListRspDTO.setRecordNumber(0);
            settleListRspDTO.setRecordList(new ArrayList<>());
            return settleListRspDTO;
        }
        List<SettleOrderListRspDTO> settleOrderListRspDTOs = settleOrderDOs.stream().map(temp -> {
            SettleOrderListRspDTO settleOrderListRspDTO = new SettleOrderListRspDTO();
            BeanUtils.copyProperties(settleOrderListRspDTO, temp);
            // 转换订单状态
            String orderStats = settleOrderListRspDTO.getStats();
            if (orderStats.equals(Constants.ORDER_STATUS_DATABASE_ACCEPT)) {
                orderStats = Constants.ORDER_STATUS_ACCEPT;
            } else if (orderStats.equals(Constants.ORDER_STATUS_DATABASE_SUCCESS)) {
                orderStats = Constants.ORDER_STATUS_SUCCESS;
            } else {
                orderStats = Constants.ORDER_STATUS_FAILURE;
            }
            settleOrderListRspDTO.setStats(orderStats);
            return settleOrderListRspDTO;
        }).collect(Collectors.toCollection(ArrayList::new));
        settleListRspDTO.setRecordList(settleOrderListRspDTOs);
        settleListRspDTO.setRecordNumber(settleOrderListRspDTOs.size());
        return settleListRspDTO;
    }

    @Override
    public SettleOrderDetailRspDTO settleOrderDetail(SettleOrderDetailReqDTO settleOrderDetailReqDTO)
            throws LemonException {
        SettleOrderDetailRspDTO settleOrderDetailRspDTO = new SettleOrderDetailRspDTO();
        String orderNo = settleOrderDetailReqDTO.getOrderNo();
        // 查询结算订单信息详情
        SettleOrderDO settleOrderDO = settleOrderDao.get(orderNo);
        if (JudgeUtils.isNull(settleOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.ORDER_NOT_EXISTS.getMsgCd());
        }
        String settleBusType = settleOrderDO.getBusType();
        // 营业厅结算不展示结算银行卡信息
        if (!(settleBusType.equals(Constants.SETTLE_BUSTYPE_HALL))) {
            String capCardNo = settleOrderDO.getCapCardNo();
            // 银行卡号解密
            capCardNo = encrypt(Constants.DECRYPT, capCardNo);
            try {
                // 银行卡号脱敏
                capCardNo = SensInfoUtil.cardSensInfo(capCardNo);
            } catch (LemonException e) {
                LemonException.throwBusinessException(MsgCdEnum.CARD_DESENS_FAIL.getMsgCd());
            }
            settleOrderDetailRspDTO.setCapCardNo(capCardNo);
            settleOrderDetailRspDTO.setCapCorgNm(settleOrderDO.getCapCorgNm());
        }
        // 转换订单状态
        LocalDate paymentDate = settleOrderDO.getAgreementPayDate();
        String stats = settleOrderDO.getStats();
        if (stats.equals(Constants.ORDER_STATUS_DATABASE_ACCEPT)) {
            stats = Constants.ORDER_STATUS_ACCEPT;
        } else if (stats.equals(Constants.ORDER_STATUS_DATABASE_SUCCESS)) {
            stats = Constants.ORDER_STATUS_SUCCESS;
        } else {
            stats = Constants.ORDER_STATUS_FAILURE;
        }
        BigDecimal tradeAmt = settleOrderDO.getTradeAmt();
        BigDecimal tradeFee = settleOrderDO.getFee();
        BigDecimal receiveAmt = tradeAmt.subtract(tradeFee);
        settleOrderDetailRspDTO.setOrderNo(settleOrderDO.getOrderNo());
        settleOrderDetailRspDTO.setTradeAmt(tradeAmt);
        settleOrderDetailRspDTO.setTradeFee(tradeFee);
        settleOrderDetailRspDTO.setReceiveAmt(receiveAmt);
        settleOrderDetailRspDTO.setTradeDate(settleOrderDO.getTradeDate());
        settleOrderDetailRspDTO.setTradeTime(settleOrderDO.getTradeTime());
        settleOrderDetailRspDTO.setBusType(settleOrderDO.getBusType());
        settleOrderDetailRspDTO.setAgreePaymentDate(settleOrderDO.getAgreementPayDate());
        settleOrderDetailRspDTO.setPaymentDate(paymentDate);
        settleOrderDetailRspDTO.setPaymentTime(settleOrderDO.getPaymentTime());
        settleOrderDetailRspDTO.setLastLastSettleDay(settleOrderDO.getLastLastSettleDay());
        settleOrderDetailRspDTO.setLastSettleDay(settleOrderDO.getLastSettleDay());
        settleOrderDetailRspDTO.setStats(stats);
        return settleOrderDetailRspDTO;
    }
    
    @Override
    public SettleListRspDTO2 settleOrderList2(SettleListReqDTO2 settleListReqDTO) throws LemonException {
        SettleListRspDTO2 settleListRspDTO = new SettleListRspDTO2();
        String userId = LemonUtils.getUserId();
        LocalDate beginDate = settleListReqDTO.getBeginDate();
        LocalDate endDate = settleListReqDTO.getEndDate();
        // 计算查询周期
        if (beginDate == null) {
            beginDate = DateTimeUtils.getCurrentLocalDate().minusMonths(1);
        }
        if (endDate == null) {
            endDate = DateTimeUtils.getCurrentLocalDate();
        }
        SettleOrderDO qryInSettleOrderDO = new SettleOrderDO();
        qryInSettleOrderDO.setUserId(userId);
        qryInSettleOrderDO.setBeginDate(beginDate);
        qryInSettleOrderDO.setEndDate(endDate);
        // 查询结算订单信息列表
        PageInfo pageInfo = PageUtils.pageQueryWithCount(settleListReqDTO.getPageNum(),
                settleListReqDTO.getPageSize(),
                () -> this.settleOrderDao.getListByCondition(qryInSettleOrderDO));

        List<SettleOrderDO> settleOrderDOs = pageInfo.getList();
        long totalNumber = pageInfo.getTotal();
        if (JudgeUtils.isNull(settleOrderDOs)) {
            settleListRspDTO.setTotalNumber(0);
            settleListRspDTO.setRecordList(new ArrayList<>());
            return settleListRspDTO;
        }
        List<SettleOrderDetailRspDTO> settleOrderListRspDTOs = settleOrderDOs.stream().map(temp -> {
            SettleOrderDetailRspDTO settleOrderListRspDTO = new SettleOrderDetailRspDTO();
            BeanUtils.copyProperties(settleOrderListRspDTO, temp);
            String settleBusType = settleOrderListRspDTO.getBusType();
            String capCardNo = settleOrderListRspDTO.getCapCardNo();
            // 营业厅结算不展示结算银行卡信息
            if (!(settleBusType.equals(Constants.SETTLE_BUSTYPE_HALL))) {
                // 银行卡号解密
                capCardNo = encrypt(Constants.DECRYPT, capCardNo);
                try {
                    // 银行卡号脱敏
                    capCardNo = SensInfoUtil.cardSensInfo(capCardNo);
                } catch (LemonException e) {
                    LemonException.throwBusinessException(MsgCdEnum.CARD_DESENS_FAIL.getMsgCd());
                }
            }
            // 转换订单状态
            String orderStats = settleOrderListRspDTO.getStats();
            if (orderStats.equals(Constants.ORDER_STATUS_DATABASE_ACCEPT)) {
                orderStats = Constants.ORDER_STATUS_ACCEPT;
            } else if (orderStats.equals(Constants.ORDER_STATUS_DATABASE_SUCCESS)) {
                orderStats = Constants.ORDER_STATUS_SUCCESS;
            } else {
                orderStats = Constants.ORDER_STATUS_FAILURE;
            }
            settleOrderListRspDTO.setCapCardNo(capCardNo);
            settleOrderListRspDTO.setStats(orderStats);
            return settleOrderListRspDTO;
        }).collect(Collectors.toCollection(ArrayList::new));
        settleListRspDTO.setRecordList(settleOrderListRspDTOs);
        settleListRspDTO.setTotalNumber(totalNumber);
        return settleListRspDTO;
    }

    @Override
    public SettleOrderResultRspDTO settleOrderResult(SettleOrderResultReqDTO settleOrderResultReqDTO)
            throws LemonException {
        SettleOrderResultRspDTO settleOrderResultRspDTO = new SettleOrderResultRspDTO();
        String orderNo = settleOrderResultReqDTO.getOrderNo();
        // 查询结算订单结果
        SettleOrderDO settleOrderDO = settleOrderDao.get(orderNo);
        if (JudgeUtils.isNull(settleOrderDO)) {
            settleOrderResultRspDTO.setStats(Constants.ORDER_STATUS_NONE);
            return settleOrderResultRspDTO;
        }
        // 转换订单状态
        String stats = settleOrderDO.getStats();
        if (stats.equals(Constants.ORDER_STATUS_DATABASE_ACCEPT)) {
            stats = Constants.ORDER_STATUS_ACCEPT;
        } else if (stats.equals(Constants.ORDER_STATUS_DATABASE_SUCCESS)) {
            stats = Constants.ORDER_STATUS_SUCCESS;
        } else {
            stats = Constants.ORDER_STATUS_FAILURE;
        }
        settleOrderResultRspDTO.setStats(stats);
        return settleOrderResultRspDTO;
    }
    
    // 判断是否为整数
    private boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[\\+]?[\\d]+$");
        return pattern.matcher(str).matches();
    }

    // 生成订单号
    private String generateOrderNo() {
        String tradeDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
        String orderJrn = IdGenUtils.generateId("CSM_ORDER_NO", 8);
        return LemonUtils.getApplicationName() + tradeDateTimeStr + orderJrn;
    }

    // 登记订单
    private void registerSettleOrder(SettleOrderDO settleOrderDO) throws LemonException {
        int result = settleOrderDao.insert(settleOrderDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_APPLY_FAIL.getMsgCd());
        }
    }

    // 更新订单
    private void updateSettleOrder(SettleOrderDO updateValueDO, SettleOrderDO updateConditionDO) {
        int result = settleOrderDao.updateByCondition(updateValueDO, updateConditionDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_CONFIRM_FAIL.getMsgCd());
        }
    }

    // 用户基本信息查询
    private UserBasicInfDTO getUserBasicInfo(String userId) throws LemonException {
        GenericRspDTO<UserBasicInfDTO> rspDTO = userBasicInfClient.queryUser(userId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 用户基本信息查询失败
            logger.info("user :" + userId + " get basic info failure!");
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }

    // 检查用户状态
    private void checkUserStatus(String userId) throws LemonException {
        RiskCheckUserStatusReqDTO reqDTO = new RiskCheckUserStatusReqDTO();
        reqDTO.setId(userId);
        reqDTO.setIdTyp("01"); // 证件类型 01用户号/商户号 02银行卡 03身份证
        reqDTO.setTxTyp("04"); // 交易类型
        GenericRspDTO<NoBody> rspDTO = riskCheckClient.checkUserStatus(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())&&JudgeUtils.notEquals(rspDTO.getMsgCd(),"RSM30016")&&JudgeUtils.notEquals(rspDTO.getMsgCd(),"RSM30010")){
            // 用户状态检查失败
            logger.info("user :" + userId + " status check failure!");
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
    }

    // 校验支付密码
    private void checkPayPassword(String userId, String payPassword, String payPwdRandom, String seaRandom) throws LemonException {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        if(seaRandom != null && !"".equals(seaRandom)){
            GenericDTO<CheckPayPwdSeaDTO> reqDTO = new GenericDTO<>();
            CheckPayPwdSeaDTO checkPayPwdDTO = new CheckPayPwdSeaDTO();
            checkPayPwdDTO.setUserId(userId);
            checkPayPwdDTO.setPayPwd(payPassword);
            checkPayPwdDTO.setPayPwdRandom(payPwdRandom);
            checkPayPwdDTO.setSeaRandom(seaRandom);
            reqDTO.setBody(checkPayPwdDTO);
            rspDTO = userAuthenticationClient.checkPayPasswordSea(reqDTO);
        }else{
            GenericDTO<CheckPayPwdDTO> reqDTO = new GenericDTO<>();
            CheckPayPwdDTO checkPayPwdDTO = new CheckPayPwdDTO();
//            checkPayPwdDTO.setUserId(userId);
            checkPayPwdDTO.setPayPwd(payPassword);
            checkPayPwdDTO.setPayPwdRandom(payPwdRandom);
            reqDTO.setBody(checkPayPwdDTO);
            rspDTO = userAuthenticationClient.checkPayPwd(reqDTO);
        }
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 支付密码校验失败
            logger.info("user :" + userId + " paypassword check failure!");
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }

    }

    // 加解密
    private String encrypt(String encryptType, String data) {
        GenericDTO<CommonEncryptReqDTO> reqDTO = new GenericDTO<>();
        CommonEncryptReqDTO encryptReqDTO = new CommonEncryptReqDTO();
        if (encryptType.equals(Constants.ENCRYPT)) {
            encryptReqDTO.setType(Constants.ENCRYPT);
        } else {
            encryptReqDTO.setType(Constants.DECRYPT);
        }
        encryptReqDTO.setData(data);
        reqDTO.setBody(encryptReqDTO);
        GenericRspDTO<CommonEncryptRspDTO> rspDTO = cmmServerClient.encrypt(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 银行卡加解密失败
            logger.info("data :" + data + " ecnrypt&&decrypt failure!");
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        CommonEncryptRspDTO encryptRspDTO = rspDTO.getBody();
        return encryptRspDTO.getData();
    }

    // 查询账户账号
    private String getAccountNo(String userId) {
        GenericRspDTO<String> rspDTO = accountManagementClient.queryAcNo(userId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 账户账号查询失败
            logger.info("user :" + userId + " account no query failure!");
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }

    // 查询待结算账户余额
    private BigDecimal getAccountBal(String userId, boolean settleFlg) throws LemonException {
        UserAccountDTO userAccountDTO = new UserAccountDTO();
        userAccountDTO.setUserId(userId);
        userAccountDTO.setCapTyp(Constants.ACM_TYPE_SETTLE);
        GenericRspDTO<List<QueryAcBalRspDTO>> rspDTO = accountManagementClient.queryAcBal(userAccountDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 待结算账户余额查询失败
            if (logger.isDebugEnabled()) {
                logger.info("user :" + userId + " account balance query failure!");
            }
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        QueryAcBalRspDTO queryAcBalRspDTO = rspDTO.getBody().get(0);
        if(settleFlg){
            return queryAcBalRspDTO.getAcCurFreezeBal();
        }
        return queryAcBalRspDTO.getAcCurBal();
    }




    // 查询结算计费信息
    private MerchantRateRspDTO getSettleRate(String userId, String busType) {
        GenericDTO<MerchantRateReqDTO> reqDTO = new GenericDTO<>();
        MerchantRateReqDTO merchantRateReqDTO = new MerchantRateReqDTO();
        merchantRateReqDTO.setUserId(userId);
        merchantRateReqDTO.setBusType(busType);
        merchantRateReqDTO.setCcy("USD");
        reqDTO.setBody(merchantRateReqDTO);
        GenericRspDTO<MerchantRateRspDTO> rspDTO = tfmServerClient.merchantRate(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 结算费率查询失败
            logger.info("user :" + userId + " settle rate query failure!");
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }

    // 手续费预算
    private BigDecimal merchanFeeCalculate(String userId, String busType, BigDecimal tradeAmt) throws LemonException {
        GenericDTO<MerchantFeeCalculateReqDTO> reqDTO = new GenericDTO<>();
        MerchantFeeCalculateReqDTO merchantFeeCalculateReqDTO = new MerchantFeeCalculateReqDTO();
        merchantFeeCalculateReqDTO.setUserId(userId);
        merchantFeeCalculateReqDTO.setCcy("USD");
        merchantFeeCalculateReqDTO.setBusType(busType);
        merchantFeeCalculateReqDTO.setTradeAmt(tradeAmt);
        reqDTO.setBody(merchantFeeCalculateReqDTO);
        GenericRspDTO<MerchantFeeCalculateRspDTO> rspDTO = tfmServerClient.merchanFeeCalculate(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 手续费预算失败
            logger.info("user :" + userId + " fee calculate failure!");
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody().getTradeFee();
    }

    // 登记提现订单
    private String registerWithdrawOrder(BigDecimal tradeAmt, UserBasicInfDTO userBasicInfDTO,
            SettleBaseDO settleBaseDO, SettleCardDO settleCardDO, SettleOrderDO settleOrderDO) throws LemonException {
        GenericDTO<WithdrawReqDTO> reqDTO = new GenericDTO<>();
        WithdrawReqDTO withdrawReqDTO = new WithdrawReqDTO();
        withdrawReqDTO.setCapTyp("1"); // 资金类型 1:现金
        withdrawReqDTO.setCorpBusTyp(CorpBusTyp.WITHDRAW); // 业务类型 提现
        withdrawReqDTO.setCorpBusSubTyp(CorpBusSubTyp.MERC_WITHDRAW); // 业务子类型
        withdrawReqDTO.setCcy("USD");
        withdrawReqDTO.setCapCorg(settleCardDO.getCapCorgNo());
        withdrawReqDTO.setCrdAcTyp("D"); // 借记卡
        withdrawReqDTO.setAgrPayDt(LocalDate.now().plusDays(Long.parseLong(settleBaseDO.getAgreementPayDays())));
        withdrawReqDTO.setWcAplAmt(tradeAmt);
        withdrawReqDTO.setPsnCrpFlg("B"); // 商户
        withdrawReqDTO.setMblNo(settleBaseDO.getMblNo());
        withdrawReqDTO.setUserNo(userBasicInfDTO.getUserId());
        withdrawReqDTO.setUserNm(settleBaseDO.getUserName());
        withdrawReqDTO.setIdTyp(userBasicInfDTO.getIdType());
        withdrawReqDTO.setIdNoEnc(userBasicInfDTO.getIdNo());
        withdrawReqDTO.setCrdNoEnc(settleCardDO.getCapCardNo());
        withdrawReqDTO.setCapCrdNm(settleCardDO.getCapCorgNm());
        withdrawReqDTO.setCapCrdNm(settleCardDO.getCapCardName());
        withdrawReqDTO.setWcRmk(Constants.SETTLE_TYPE_AUTO);
        withdrawReqDTO.setReqOrdNo(settleOrderDO.getOrderNo());
        withdrawReqDTO.setReqOrdDt(settleOrderDO.getTradeDate());
        withdrawReqDTO.setReqOrdTm(settleOrderDO.getTradeTime());
        withdrawReqDTO.setSubbranch(settleCardDO.getSubbranch());
        reqDTO.setBody(withdrawReqDTO);
        GenericRspDTO<WithdrawResDTO> rspDTO = withdrawClient.createOrder(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 登记提现订单失败
            logger.info("user :" + settleBaseDO.getUserId() + " call cpo register withdraw order failure!");
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody().getOrdNo();
    }

    // 自主结算/自动结算申请账务处理
    private void settleAccountTreat(String userId, String txType, String txJrnNo, String settleType, SettleOrderDO settleOrderDO)
            throws LemonException {
        // 查询商户账户账号
        String accountNo = getAccountNo(userId);
        String tradeOrderNo = settleOrderDO.getOrderNo();
        BigDecimal tradeAmt = settleOrderDO.getTradeAmt();
        BigDecimal fee = settleOrderDO.getFee();
        BigDecimal realTradeAmt = tradeAmt.subtract(fee);
        LocalDate tradeDate = settleOrderDO.getTradeDate();
        LocalTime tradeTime = settleOrderDO.getTradeTime();
        GenericDTO<List<AccountingReqDTO>> reqDTO = new GenericDTO<>();
        List<AccountingReqDTO> accountingReqDTOs = new ArrayList<>();
        // 借：其他应付款-支付账户-商户结算账户
        AccountingReqDTO accountingReqDTO1 = new AccountingReqDTO();
        accountingReqDTO1.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
        accountingReqDTO1.setAcTyp("U");// 账户类型 U:用户 I：科目
        accountingReqDTO1.setAcNo(accountNo);// 交易账号
        accountingReqDTO1.setCapTyp(Constants.ACM_TYPE_SETTLE);// 资金属性
        accountingReqDTO1.setTxTyp("04");// 交易类型 04:提现
        accountingReqDTO1.setTxAmt(tradeAmt); // 交易金额
        accountingReqDTO1.setDcFlg("D"); // 借贷标志 D:借 C:贷
        accountingReqDTO1.setTxJrnNo(txJrnNo);
        accountingReqDTO1.setTxOrdNo(tradeOrderNo); // 交易订单号
        accountingReqDTO1.setTxOrdDt(tradeDate); // 交易订单日期
        accountingReqDTO1.setTxOrdTm(tradeTime); // 交易订单时间
        accountingReqDTO1.setRmk("商户结算");
        accountingReqDTOs.add(accountingReqDTO1);
        // 贷：应付账款-待结算款-批量付款/营业厅付款
        AccountingReqDTO accountingReqDTO2 = new AccountingReqDTO();
        accountingReqDTO2.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正 撤销暂不考虑
        accountingReqDTO2.setAcTyp("I");// 账户类型 U:用户 I：科目
        accountingReqDTO2.setItmNo(Constants.AC_ITEMNO_BATCH); // 科目账号
        if (settleType.equals(Constants.SETTLE_TYPE_HALL)) {
            accountingReqDTO2.setItmNo(Constants.AC_ITEMNO_HALL);
        }
        accountingReqDTO2.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
        accountingReqDTO2.setTxTyp("04");// 交易类型 04:提现
        accountingReqDTO2.setTxAmt(realTradeAmt); // 交易金额
        accountingReqDTO2.setDcFlg("C"); // 借贷标志 D:借 C:贷
        accountingReqDTO2.setTxJrnNo(txJrnNo);
        accountingReqDTO2.setTxOrdNo(tradeOrderNo); // 交易订单号
        accountingReqDTO2.setTxOrdDt(tradeDate); // 交易订单日期
        accountingReqDTO2.setTxOrdTm(tradeTime); // 交易订单时间
        accountingReqDTO2.setRmk("商户结算");
        accountingReqDTOs.add(accountingReqDTO2);
        if (fee.compareTo(BigDecimal.ZERO) > 0) {
            // 贷：手续费收入-支付账户-商户结算
            AccountingReqDTO accountingReqDTO3 = new AccountingReqDTO();
            accountingReqDTO3.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
            accountingReqDTO3.setAcTyp("I");// 账户类型 U:用户 I：科目
            accountingReqDTO3.setItmNo(Constants.AC_ITEMNO_FEE); // 科目账号
            accountingReqDTO3.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
            accountingReqDTO3.setTxTyp("04");// 交易类型 04:提现
            accountingReqDTO3.setTxAmt(fee); // 交易金额
            accountingReqDTO3.setDcFlg("C"); // 借贷标志 D:借 C:贷
            accountingReqDTO3.setTxJrnNo(txJrnNo);
            accountingReqDTO3.setTxOrdNo(tradeOrderNo); // 交易订单号
            accountingReqDTO3.setTxOrdDt(tradeDate); // 交易订单日期
            accountingReqDTO3.setTxOrdTm(tradeTime); // 交易订单时间
            accountingReqDTO3.setRmk("商户结算");
            accountingReqDTOs.add(accountingReqDTO3);
        }
        reqDTO.setBody(accountingReqDTOs);
        GenericRspDTO<NoBody> rspDTO = accountingTreatmentClient.accountingTreatment(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
    }

    // 自主结算/自动结算确认账务处理
    private void settleConfirmAccountTreat(String userId, String txType, String txJrnNo, String stats, SettleOrderDO settleOrderDO)
            throws LemonException {
        String settleOrderNo = settleOrderDO.getOrderNo();
        BigDecimal tradeAmt = settleOrderDO.getTradeAmt();
        BigDecimal fee = settleOrderDO.getFee();
        BigDecimal realTradeAmt = tradeAmt.subtract(fee);
        LocalDate orderDate = settleOrderDO.getTradeDate();
        LocalTime orderTime = settleOrderDO.getTradeTime();
        // 查询商户账户账号
        String accountNo = getAccountNo(userId);
        GenericDTO<List<AccountingReqDTO>> reqDTO = new GenericDTO<>();
        List<AccountingReqDTO> accountingReqDTOs = new ArrayList<>();
        //付款成功csm不做账，账务由cpo做账。付款失败csm反冲账务
        if (stats.equals(Constants.ORDER_STATUS_DATABASE_SUCCESS)) {

//            // 账务处理
//            // 借：应付账款-待结算款-批量付款
//            AccountingReqDTO accountingReqDTO1 = new AccountingReqDTO();
//            accountingReqDTO1.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
//            accountingReqDTO1.setAcTyp("I");// 账户类型 U:用户 I：科目
//            accountingReqDTO1.setItmNo(Constants.AC_ITEMNO_BATCH); // 科目账号
//            accountingReqDTO1.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
//            accountingReqDTO1.setTxTyp("04");// 交易类型 04:提现
//            accountingReqDTO1.setTxAmt(realTradeAmt); // 交易金额
//            accountingReqDTO1.setDcFlg("D"); // 借贷标志 D:借 C:贷
//            accountingReqDTO1.setTxJrnNo(txJrnNo);
//            accountingReqDTO1.setTxOrdNo(settleOrderNo); // 交易订单号
//            accountingReqDTO1.setTxOrdDt(orderDate); // 交易订单日期
//            accountingReqDTO1.setTxOrdTm(orderTime); // 交易订单时间
//            accountingReqDTO1.setRmk("商户结算");
//            accountingReqDTOs.add(accountingReqDTO1);
//            // 贷：银行存款-备付金账户-XX银行
//            AccountingReqDTO accountingReqDTO2 = new AccountingReqDTO();
//            accountingReqDTO2.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正 撤销暂不考虑
//            accountingReqDTO2.setAcTyp("I");// 账户类型 U:用户 I：科目
//            accountingReqDTO2.setItmNo(Constants.AC_ITEMNO_BANK);// 科目账号
//            accountingReqDTO2.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
//            accountingReqDTO2.setTxTyp("04");// 交易类型 04:提现
//            accountingReqDTO2.setTxAmt(realTradeAmt); // 交易金额
//            accountingReqDTO2.setDcFlg("C"); // 借贷标志 D:借 C:贷
//            accountingReqDTO2.setTxJrnNo(txJrnNo);
//            accountingReqDTO2.setTxOrdNo(settleOrderNo); // 交易订单号
//            accountingReqDTO2.setTxOrdDt(orderDate); // 交易订单日期
//            accountingReqDTO2.setTxOrdTm(orderTime); // 交易订单时间
//            accountingReqDTO2.setRmk("商户结算");
//            accountingReqDTOs.add(accountingReqDTO2);
//            reqDTO.setBody(accountingReqDTOs);
//            GenericRspDTO<NoBody> rspDTO = accountingTreatmentClient.accountingTreatment(reqDTO);
//            if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
//                LemonException.throwBusinessException(rspDTO.getMsgCd());
//            }
        } else {
            // 账务处理
            // 借：应付账款-待结算款-批量付款
            AccountingReqDTO accountingReqDTO1 = new AccountingReqDTO();
            accountingReqDTO1.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
            accountingReqDTO1.setAcTyp("I");// 账户类型 U:用户 I：科目
            accountingReqDTO1.setItmNo(Constants.AC_ITEMNO_BATCH);// 科目账号
            accountingReqDTO1.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
            accountingReqDTO1.setTxTyp("04");// 交易类型 04:提现
            accountingReqDTO1.setTxAmt(realTradeAmt); // 交易金额
            accountingReqDTO1.setDcFlg("D"); // 借贷标志 D:借 C:贷
            accountingReqDTO1.setTxJrnNo(txJrnNo);
            accountingReqDTO1.setTxOrdNo(settleOrderNo); // 交易订单号
            accountingReqDTO1.setTxOrdDt(orderDate); // 交易订单日期
            accountingReqDTO1.setTxOrdTm(orderTime); // 交易订单时间
            accountingReqDTO1.setRmk("商户结算");
            accountingReqDTOs.add(accountingReqDTO1);
            if (fee.compareTo(BigDecimal.ZERO) > 0) {
                // 借：手续费收入-支付账户-商户结算
                AccountingReqDTO accountingReqDTO2 = new AccountingReqDTO();
                accountingReqDTO2.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
                accountingReqDTO2.setAcTyp("I");// 账户类型 U:用户 I：科目
                accountingReqDTO2.setItmNo(Constants.AC_ITEMNO_FEE); // 科目账号
                accountingReqDTO2.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
                accountingReqDTO2.setTxTyp("04");// 交易类型 04:提现
                accountingReqDTO2.setTxAmt(fee); // 交易金额
                accountingReqDTO2.setDcFlg("D"); // 借贷标志 D:借 C:贷
                accountingReqDTO2.setTxJrnNo(txJrnNo);
                accountingReqDTO2.setTxOrdNo(settleOrderNo); // 交易订单号
                accountingReqDTO2.setTxOrdDt(orderDate); // 交易订单日期
                accountingReqDTO2.setTxOrdTm(orderTime); // 交易订单时间
                accountingReqDTO2.setRmk("商户结算");
                accountingReqDTOs.add(accountingReqDTO2);
            }
            // 贷：其他应付款-支付账户-商户结算账户
            AccountingReqDTO accountingReqDTO3 = new AccountingReqDTO();
            accountingReqDTO3.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正 撤销暂不考虑
            accountingReqDTO3.setAcTyp("U");// 账户类型 U:用户 I：科目
            accountingReqDTO3.setAcNo(accountNo);// 交易账号
            accountingReqDTO3.setCapTyp(Constants.ACM_TYPE_SETTLE);// 资金属性
            accountingReqDTO3.setTxTyp("04");// 交易类型 04:提现
            accountingReqDTO3.setTxAmt(tradeAmt); // 交易金额
            accountingReqDTO3.setDcFlg("C"); // 借贷标志 D:借 C:贷
            accountingReqDTO3.setTxJrnNo(txJrnNo);
            accountingReqDTO3.setTxOrdNo(settleOrderNo); // 交易订单号
            accountingReqDTO3.setTxOrdDt(orderDate); // 交易订单日期
            accountingReqDTO3.setTxOrdTm(orderTime); // 交易订单时间
            accountingReqDTO3.setRmk("商户结算");
            accountingReqDTOs.add(accountingReqDTO3);
            reqDTO.setBody(accountingReqDTOs);
            GenericRspDTO<NoBody> rspDTO = accountingTreatmentClient.accountingTreatment(reqDTO);
            if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
                LemonException.throwBusinessException(rspDTO.getMsgCd());
            }
        }
    }

    // 商户营业厅取现对账差错撤单账务处理
    private void hallsettleRevokeAccountTreat(String userId, String txType, String txJrnNo,SettleOrderDO settleOrderDO)
            throws LemonException {
        // 查询商户账户账号
        String accountNo = getAccountNo(userId);
        String tradeOrderNo = settleOrderDO.getOrderNo();
        BigDecimal tradeAmt = settleOrderDO.getTradeAmt();
        BigDecimal fee = settleOrderDO.getFee();
        //营业厅款为结算金额减去手续费
        BigDecimal hallAmt = tradeAmt.subtract(fee);
        LocalDate tradeDate = settleOrderDO.getTradeDate();
        LocalTime tradeTime = settleOrderDO.getTradeTime();
        GenericDTO<List<AccountingReqDTO>> reqDTO = new GenericDTO<>();
        List<AccountingReqDTO> accountingReqDTOs = new ArrayList<>();
//        贷：其他应付款-支付账户-商户结算账户
        AccountingReqDTO accountingReqDTO1 = new AccountingReqDTO();
        accountingReqDTO1.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
        accountingReqDTO1.setAcTyp("U");// 账户类型 U:用户 I：科目
        accountingReqDTO1.setAcNo(accountNo);// 交易账号
        accountingReqDTO1.setItmNo(Constants.AC_ITEMNO_M_SETTLE);
        accountingReqDTO1.setCapTyp(Constants.ACM_TYPE_SETTLE);// 资金属性
        accountingReqDTO1.setTxTyp("04");// 交易类型 04:提现
        accountingReqDTO1.setTxAmt(tradeAmt); // 交易金额
        accountingReqDTO1.setDcFlg("C"); // 借贷标志 D:借 C:贷
        accountingReqDTO1.setTxJrnNo(txJrnNo);
        accountingReqDTO1.setTxOrdNo(tradeOrderNo); // 交易订单号
        accountingReqDTO1.setTxOrdDt(tradeDate); // 交易订单日期
        accountingReqDTO1.setTxOrdTm(tradeTime); // 交易订单时间
        accountingReqDTO1.setRmk("商户结算");
        accountingReqDTOs.add(accountingReqDTO1);
//        借：应付账款-待结算款-营业厅付款
        AccountingReqDTO accountingReqDTO2 = new AccountingReqDTO();
        accountingReqDTO2.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正 撤销暂不考虑
        accountingReqDTO2.setAcTyp("I");// 账户类型 U:用户 I：科目
        accountingReqDTO2.setItmNo(Constants.AC_ITEMNO_HALL);// 科目账号
        accountingReqDTO2.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
        accountingReqDTO2.setTxTyp("04");// 交易类型 04:提现
        accountingReqDTO2.setTxAmt(hallAmt); // 交易金额
        accountingReqDTO2.setDcFlg("D"); // 借贷标志 D:借 C:贷
        accountingReqDTO2.setTxJrnNo(txJrnNo);
        accountingReqDTO2.setTxOrdNo(tradeOrderNo); // 交易订单号
        accountingReqDTO2.setTxOrdDt(tradeDate); // 交易订单日期
        accountingReqDTO2.setTxOrdTm(tradeTime); // 交易订单时间
        accountingReqDTO2.setRmk("商户结算");
        accountingReqDTOs.add(accountingReqDTO2);
        if (fee.compareTo(BigDecimal.ZERO) > 0) {
            // 借：手续费收入-支付账户-商户结算
            AccountingReqDTO accountingReqDTO3 = new AccountingReqDTO();
            accountingReqDTO3.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
            accountingReqDTO3.setAcTyp("I");// 账户类型 U:用户 I：科目
            accountingReqDTO3.setItmNo(Constants.AC_ITEMNO_FEE); // 科目账号
            accountingReqDTO3.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
            accountingReqDTO3.setTxTyp("04");// 交易类型 04:提现
            accountingReqDTO3.setTxAmt(fee); // 交易金额
            accountingReqDTO3.setDcFlg("D"); // 借贷标志 D:借 C:贷
            accountingReqDTO3.setTxJrnNo(txJrnNo);
            accountingReqDTO3.setTxOrdNo(tradeOrderNo); // 交易订单号
            accountingReqDTO3.setTxOrdDt(tradeDate); // 交易订单日期
            accountingReqDTO3.setTxOrdTm(tradeTime); // 交易订单时间
            accountingReqDTO3.setRmk("商户营业厅结算撤单");
            accountingReqDTOs.add(accountingReqDTO3);
        }
        reqDTO.setBody(accountingReqDTOs);
        GenericRspDTO<NoBody> rspDTO = accountingTreatmentClient.accountingTreatment(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
    }

    // 商户营业厅取现结算账务处理
    private void hallSettleAccountTreat(String userId, String txType, String txJrnNo,BigDecimal realTradeAmt, SettleOrderDO settleOrderDO)
            throws LemonException {
        // 查询商户账户账号
        String accountNo = getAccountNo(userId);
        String tradeOrderNo = settleOrderDO.getOrderNo();
        BigDecimal tradeAmt = settleOrderDO.getTradeAmt();
        BigDecimal fee = settleOrderDO.getFee();
        LocalDate tradeDate = settleOrderDO.getTradeDate();
        LocalTime tradeTime = settleOrderDO.getTradeTime();
        GenericDTO<List<AccountingReqDTO>> reqDTO = new GenericDTO<>();
        List<AccountingReqDTO> accountingReqDTOs = new ArrayList<>();
        // 借：其他应付款-支付账户-商户结算账户
        AccountingReqDTO accountingReqDTO1 = new AccountingReqDTO();
        accountingReqDTO1.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
        accountingReqDTO1.setAcTyp("U");// 账户类型 U:用户 I：科目
        accountingReqDTO1.setAcNo(accountNo);// 交易账号
        accountingReqDTO1.setItmNo(Constants.AC_ITEMNO_M_SETTLE);
        accountingReqDTO1.setCapTyp(Constants.ACM_TYPE_SETTLE);// 资金属性
        accountingReqDTO1.setTxTyp("04");// 交易类型 04:提现
        accountingReqDTO1.setTxAmt(tradeAmt); // 交易金额
        accountingReqDTO1.setDcFlg("D"); // 借贷标志 D:借 C:贷
        accountingReqDTO1.setTxJrnNo(txJrnNo);
        accountingReqDTO1.setTxOrdNo(tradeOrderNo); // 交易订单号
        accountingReqDTO1.setTxOrdDt(tradeDate); // 交易订单日期
        accountingReqDTO1.setTxOrdTm(tradeTime); // 交易订单时间
        accountingReqDTO1.setRmk("商户结算");
        accountingReqDTOs.add(accountingReqDTO1);
        // 贷：应付账款-待结算款-批量付款/营业厅付款
        AccountingReqDTO accountingReqDTO2 = new AccountingReqDTO();
        accountingReqDTO2.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正 撤销暂不考虑
        accountingReqDTO2.setAcTyp("I");// 账户类型 U:用户 I：科目
        accountingReqDTO2.setItmNo(Constants.AC_ITEMNO_HALL);// 科目账号
        accountingReqDTO2.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
        accountingReqDTO2.setTxTyp("04");// 交易类型 04:提现
        accountingReqDTO2.setTxAmt(realTradeAmt); // 交易金额
        accountingReqDTO2.setDcFlg("C"); // 借贷标志 D:借 C:贷
        accountingReqDTO2.setTxJrnNo(txJrnNo);
        accountingReqDTO2.setTxOrdNo(tradeOrderNo); // 交易订单号
        accountingReqDTO2.setTxOrdDt(tradeDate); // 交易订单日期
        accountingReqDTO2.setTxOrdTm(tradeTime); // 交易订单时间
        accountingReqDTO2.setRmk("商户营业厅结算");
        accountingReqDTOs.add(accountingReqDTO2);
        if (fee.compareTo(BigDecimal.ZERO) > 0) {
            // 贷：手续费收入-支付账户-商户结算
            AccountingReqDTO accountingReqDTO3 = new AccountingReqDTO();
            accountingReqDTO3.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
            accountingReqDTO3.setAcTyp("I");// 账户类型 U:用户 I：科目
            accountingReqDTO3.setItmNo(Constants.AC_ITEMNO_FEE); // 科目账号
            accountingReqDTO3.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
            accountingReqDTO3.setTxTyp("04");// 交易类型 04:提现
            accountingReqDTO3.setTxAmt(fee); // 交易金额
            accountingReqDTO3.setDcFlg("C"); // 借贷标志 D:借 C:贷
            accountingReqDTO3.setTxJrnNo(txJrnNo);
            accountingReqDTO3.setTxOrdNo(tradeOrderNo); // 交易订单号
            accountingReqDTO3.setTxOrdDt(tradeDate); // 交易订单日期
            accountingReqDTO3.setTxOrdTm(tradeTime); // 交易订单时间
            accountingReqDTO3.setRmk("商户营业厅结算");
            accountingReqDTOs.add(accountingReqDTO3);
        }
        reqDTO.setBody(accountingReqDTOs);
        GenericRspDTO<NoBody> rspDTO = accountingTreatmentClient.accountingTreatment(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
    }

    // 更新结算日信息
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void settleInfoUpdate(SettleOrderDO settleOrderDO) throws LemonException {
        String userId = settleOrderDO.getUserId();
        // 查询用户结算信息
        SettleBaseDO qryOutSettleBaseDO = settleBaseDao.get(userId);
        if (JudgeUtils.isNull(qryOutSettleBaseDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_INFO_NOTEXISTS.getMsgCd());
        }
        String setteleType = qryOutSettleBaseDO.getSettleType();
        // 非自动结算类型不更新结算日期信息
        if (!setteleType.equals(Constants.SETTLE_TYPE_AUTO)) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_CONFIRM_FAIL.getMsgCd());
        }
        String settleCycleType = qryOutSettleBaseDO.getSettleCycleType().trim();
        // 结算日期计算
        LocalDate oldNextSettleDay = qryOutSettleBaseDO.getNextSettleDay();
        if (JudgeUtils.isNull(oldNextSettleDay)) {
            oldNextSettleDay = settleOrderDO.getLastSettleDay();
        }
        LocalDate oldLastSettleDay = qryOutSettleBaseDO.getLastSettleDay();
        if (JudgeUtils.isNull(oldLastSettleDay)) {
            oldLastSettleDay = oldNextSettleDay.minusDays(1);
            if (settleCycleType.equals(Constants.TIME_CYCLE_WEEK)) {
                oldLastSettleDay = oldNextSettleDay.minusWeeks(1);
            } else if (settleCycleType.equals(Constants.TIME_CYCLE_MONTH)) {
                oldLastSettleDay = oldNextSettleDay.minusMonths(1);
            }
        }
        LocalDate nextSettleDay = oldNextSettleDay.plusDays(1);
        if (settleCycleType.equals(Constants.TIME_CYCLE_WEEK)) {
            nextSettleDay = oldNextSettleDay.plusWeeks(1);
        } else if (settleCycleType.equals(Constants.TIME_CYCLE_MONTH)) {
            nextSettleDay = oldNextSettleDay.plusMonths(1);
        }
        // 更新用户结算信息
        SettleBaseDO settleBaseDO = new SettleBaseDO();
        settleBaseDO.setUserId(userId);
        settleBaseDO.setLastLastSettleDay(oldLastSettleDay);
        settleBaseDO.setLastSettleDay(oldNextSettleDay);
        settleBaseDO.setNextSettleDay(nextSettleDay);
        int result = settleBaseDao.update(settleBaseDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.SETTLE_CONFIRM_FAIL.getMsgCd());
        }
    }

    @Override
    public void settleAutoHandler(LocalDate checkDate, CheckControlDO checkControlDO) throws LemonException {
        String checkBatchNo = checkControlDO.getCheckBatchNo();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        LocalDateTime tradeDateTime = DateTimeUtils.getCurrentLocalDateTime();
        // 查询待结算的用户信息
        SettleBaseDO qryInSettleBaseDO = new SettleBaseDO();
        qryInSettleBaseDO.setNextSettleDay(checkDate.plusDays(1));
        qryInSettleBaseDO.setSettleType(Constants.SETTLE_TYPE_AUTO);
        qryInSettleBaseDO.setStats(Constants.STATUS_AVAILABLE);
        //List<SettleBaseDO> settleBaseDOs = settleBaseDao.getListByCondition(qryInSettleBaseDO);
        List<SettleBaseDO> settleBaseDOs = settleBaseDao.getValidSettleListByCondition(qryInSettleBaseDO);
        Integer totalCount = Integer.valueOf(0);
        BigDecimal totalAmt = BigDecimal.ZERO;
        if (JudgeUtils.isNotEmpty(settleBaseDOs)) {
            // 循环处理待结算商户
            for (SettleBaseDO settleBaseDO : settleBaseDOs) {
                String userId = settleBaseDO.getUserId();

                //判断商户是否有结算权限
                CprItfAuthDTO cprItfAuthDTO = new CprItfAuthDTO() ;
                cprItfAuthDTO.setItfNm(Constants.CPRITFAUTH);
                cprItfAuthDTO.setUserId(userId);
                cprItfAuthDTO.setSts("1");
                cprItfAuthDTO.setVersion("1.0");
                GenericDTO<CprItfAuthDTO> genericDTO = GenericDTO.newInstance(cprItfAuthDTO);
                GenericRspDTO<CprItfAuthDTO> genericRspDTO = userBasicInfClient.queryTradingPrivilege(genericDTO);
                cprItfAuthDTO = genericRspDTO.getBody() ;
                if ( !("URM00000".equals(genericRspDTO.getMsgCd()) && (cprItfAuthDTO != null) )) {
                    logger.info("user :" + userId + " no settle auth!");
                    continue;
                }

                BigDecimal tradeAmt = BigDecimal.ZERO;
                SettleOrderDO settleOrderDO = new SettleOrderDO();
                settleOrderDO.setUserId(userId);
                String txJrnNo = generateOrderNo();
                try {
                    // 查询用户基本信息
                    UserBasicInfDTO userBasicInfDTO = getUserBasicInfo(userId);
                    // 查询结算账户余额
                    tradeAmt = getAccountBal(userId, true);
                    String settleCycleType = settleBaseDO.getSettleCycleType();
                    // 结算日期计算
                    LocalDate nextSettleDay = settleBaseDO.getNextSettleDay();
                    if (JudgeUtils.isNull(nextSettleDay)) {
                        nextSettleDay = tradeDate;
                    }
                    LocalDate lastSettleDay = settleBaseDO.getLastSettleDay();
                    if (JudgeUtils.isNull(lastSettleDay)) {
                        if (settleCycleType.equals(Constants.TIME_CYCLE_DAILY)) {
                            lastSettleDay = nextSettleDay.minusDays(1);
                        } else if (settleCycleType.equals(Constants.TIME_CYCLE_WEEK)) {
                            lastSettleDay = nextSettleDay.minusWeeks(1);
                        } else {
                            lastSettleDay = nextSettleDay.minusMonths(1);
                        }
                    }
                    if (tradeAmt.compareTo(BigDecimal.ZERO) < 1) {
                        settleOrderDO.setLastLastSettleDay(lastSettleDay);
                        settleInfoUpdate(settleOrderDO);
                        // 结算账户余额为0
                        logger.info("user :" + userId + " settle account balance zero!");
                        continue;
                    }
                    // 手续费预算
                    BigDecimal fee = merchanFeeCalculate(userId, Constants.SETTLE_BUSTYPE_AUTO, tradeAmt);
                    // 扣除手续费结算金额
                    BigDecimal realTradeAmt = tradeAmt.subtract(fee);
                    if (realTradeAmt.compareTo(BigDecimal.ZERO) <= 0) {
                        settleOrderDO.setLastLastSettleDay(lastSettleDay);
                        settleInfoUpdate(settleOrderDO);
                        // 结算到账金额为0
                        logger.info("user :" + userId + " receive amt zero!");
                        continue;
                    }
                    // 查询用户结算基本信息
                    String userName = settleBaseDO.getUserName();
                    String mblNo = settleBaseDO.getMblNo();
                    LocalDate agreementPayDate = tradeDate;
                    String agreementPayDays = settleBaseDO.getAgreementPayDays();
                    if (isInteger(agreementPayDays)) {
                        agreementPayDate = agreementPayDate.plusDays(Integer.parseInt(agreementPayDays));
                    }
                    String settleType = settleBaseDO.getSettleType();

                    // 判断结算方式
                    if (!settleType.equals(Constants.SETTLE_TYPE_AUTO)) {
                        // 结算方式不正确
                        logger.info("user :" + userId + " settle type is error!");
                        continue;
                    }
                    // 查询用户结算银行卡信息
                    SettleCardDO qryInSettleCardDO = new SettleCardDO();
                    qryInSettleCardDO.setUserId(userId);
                    qryInSettleCardDO.setStats(Constants.STATUS_AVAILABLE);
                    SettleCardDO settleCardDO = settleCardDao.getByCondition(qryInSettleCardDO);
                    if (JudgeUtils.isNull(settleCardDO)) {
                        // 银行卡信息不存在
                        logger.info("user :" + userId + " settle card not exists!");
                        continue;
                    }
                    String capCorgNm = settleCardDO.getCapCorgNm();
                    String capCorgSnm = settleCardDO.getCapCorgSnm();
                    String capCorgNo = settleCardDO.getCapCorgNo();
                    String capCardNo = settleCardDO.getCapCardNo();
                    String capCardNm = settleCardDO.getCapCardName();
                    String bnkMblNo = settleCardDO.getBnkMblNo();
                    // 登记结算订单
                    String settleOrderNo = generateOrderNo();
                    settleOrderDO.setOrderNo(settleOrderNo);
                    settleOrderDO.setTradeDate(tradeDate);
                    settleOrderDO.setTradeTime(tradeTime);
                    settleOrderDO.setAgreementPayDate(agreementPayDate);
                    settleOrderDO.setTradeAmt(tradeAmt);
                    settleOrderDO.setFee(fee);
                    settleOrderDO.setCcy("USD");
                    // 0403自主结算 0404自动结算 0405营业厅结算
                    settleOrderDO.setBusType(Constants.SETTLE_BUSTYPE_AUTO);
                    settleOrderDO.setUserId(userId);
                    settleOrderDO.setUserName(userName);
                    settleOrderDO.setMblNo(mblNo);
                    settleOrderDO.setCapCorgNm(capCorgNm);
                    settleOrderDO.setCapCorgSnm(capCorgSnm);
                    settleOrderDO.setCapCorgNo(capCorgNo);
                    settleOrderDO.setCapCardNo(capCardNo);
                    settleOrderDO.setCapCardNm(capCardNm);
                    settleOrderDO.setBnkMblNo(bnkMblNo);
                    settleOrderDO.setStats(Constants.ORDER_STATUS_DATABASE_ACCEPT);
                    settleOrderDO.setLastLastSettleDay(lastSettleDay);
                    settleOrderDO.setLastSettleDay(nextSettleDay);
                    // 账务处理
                    settleAccountTreat(userId, Constants.AC_TXSTS_N, txJrnNo, Constants.SETTLE_TYPE_AUTO, settleOrderDO);
                    // 登记提现订单
                    //暂时加上异常处理
                    try {
                        String withdrawOrderNo = registerWithdrawOrder(realTradeAmt, userBasicInfDTO, settleBaseDO,
                                settleCardDO, settleOrderDO);
                        settleOrderDO.setBusOrderNo(withdrawOrderNo);
                        // 登记结算订单
                        registerSettleOrder(settleOrderDO);
                        GenericDTO<UserAccountFreezeDTO>  userAccountDTO = new GenericDTO();
                        UserAccountFreezeDTO userAccountFreezeDTO = new UserAccountFreezeDTO();
                        userAccountFreezeDTO.setAcDt(checkDate);
                        userAccountFreezeDTO.setUserId(userId);
                        userAccountFreezeDTO.setCapTyp(Constants.ACM_TYPE_SETTLE);
                        userAccountDTO.setBody(userAccountFreezeDTO);
                        GenericRspDTO genericAcmRspDTO = accountManagementClient.unfreezeSettleAcBal(userAccountDTO);
                        if(JudgeUtils.isNotSuccess(genericAcmRspDTO.getMsgCd())){
                            logger.info("unfreezeSettleAcBalError : " + userId);
                        }
                    } catch (Exception e) {
                        // 提现订单登记失败和结算订单登记失败做账务冲正
                        settleAccountTreat(userId, Constants.AC_TXSTS_C, txJrnNo, Constants.SETTLE_TYPE_AUTO, settleOrderDO);
                        // 更新用户结算日信息
                        settleInfoUpdate(settleOrderDO);
                        continue;
                    }
                    // 更新用户结算日信息
                    settleInfoUpdate(settleOrderDO);
                } catch (Exception e) {
                    // 更新用户结算日信息
                    settleInfoUpdate(settleOrderDO);
                    // 继续处理后面的待结算商户
                    continue;
                }
                // 统计结算笔数和金额
                totalCount++;
                totalAmt = totalAmt.add(tradeAmt);
            }
        }
        // 更新商户自动结算批次信息
        CheckControlDO updateValueCheckControlDO = new CheckControlDO();
        updateValueCheckControlDO.setTotalCount(totalCount);
        updateValueCheckControlDO.setTotalAmt(totalAmt);
        updateValueCheckControlDO.setModifyTime(tradeDateTime);
        CheckControlDO updateConditionCheckControlDO = new CheckControlDO();
        updateConditionCheckControlDO.setCheckBatchNo(checkBatchNo);
        checkControlDao.updateByCondition(updateValueCheckControlDO, updateConditionCheckControlDO);
    }


    @Override
    public void settleAutoHandlerPre(LocalDate checkDate) throws LemonException {
        // 查询待结算的用户信息
        SettleBaseDO qryInSettleBaseDO = new SettleBaseDO();
        qryInSettleBaseDO.setNextSettleDay(checkDate);
        qryInSettleBaseDO.setSettleType(Constants.SETTLE_TYPE_AUTO);
        qryInSettleBaseDO.setStats(Constants.STATUS_AVAILABLE);
        List<SettleBaseDO> settleBaseDOs = settleBaseDao.getValidSettleListByCondition(qryInSettleBaseDO);
        if (JudgeUtils.isNotEmpty(settleBaseDOs)) {
            // 循环处理待结算商户
            for (SettleBaseDO settleBaseDO : settleBaseDOs) {
                String userId = settleBaseDO.getUserId();
                //判断商户是否有结算权限
                CprItfAuthDTO cprItfAuthDTO = new CprItfAuthDTO() ;
                cprItfAuthDTO.setItfNm(Constants.CPRITFAUTH);
                cprItfAuthDTO.setUserId(userId);
                cprItfAuthDTO.setSts("1");
                cprItfAuthDTO.setVersion("1.0");
                GenericDTO<CprItfAuthDTO> genericDTO = GenericDTO.newInstance(cprItfAuthDTO);
                GenericRspDTO<CprItfAuthDTO> genericRspDTO = userBasicInfClient.queryTradingPrivilege(genericDTO);
                cprItfAuthDTO = genericRspDTO.getBody() ;
                if ( !("URM00000".equals(genericRspDTO.getMsgCd()) && (cprItfAuthDTO != null) )) {
                    logger.info("user :" + userId + " no settle auth!");
                    continue;
                }
                BigDecimal tradeAmt = BigDecimal.ZERO;

                try {
                    // 查询结算账户余额
                    tradeAmt = getAccountBal(userId, false);
                    if (tradeAmt.compareTo(BigDecimal.ZERO) < 1) {
                        // 结算账户余额为0
                        logger.info("user :" + userId + " settle account balance zero!");
                        continue;
                    }
                    // 手续费预算
                    BigDecimal fee = merchanFeeCalculate(userId, Constants.SETTLE_BUSTYPE_AUTO, tradeAmt);
                    // 扣除手续费结算金额
                    BigDecimal realTradeAmt = tradeAmt.subtract(fee);
                    if (realTradeAmt.compareTo(BigDecimal.ZERO) <= 0) {
                        // 结算到账金额为0
                        logger.info("user :" + userId + " receive amt zero!");
                        continue;
                    }
                    // 查询用户结算基本信息
                    String settleType = settleBaseDO.getSettleType();
                    // 判断结算方式
                    if (!settleType.equals(Constants.SETTLE_TYPE_AUTO)) {
                        // 结算方式不正确
                        logger.info("user :" + userId + " settle type is error!");
                        continue;
                    }
                    // 查询用户结算银行卡信息
                    SettleCardDO qryInSettleCardDO = new SettleCardDO();
                    qryInSettleCardDO.setUserId(userId);
                    qryInSettleCardDO.setStats(Constants.STATUS_AVAILABLE);
                    SettleCardDO settleCardDO = settleCardDao.getByCondition(qryInSettleCardDO);
                    if (JudgeUtils.isNull(settleCardDO)) {
                        // 银行卡信息不存在
                        logger.info("user :" + userId + " settle card not exists!");
                        continue;
                    }
                    GenericDTO<UserAccountFreezeDTO>  userAccountDTO = new GenericDTO();
                    UserAccountFreezeDTO userAccountFreezeDTO = new UserAccountFreezeDTO();
                    userAccountFreezeDTO.setAcDt(checkDate);
                    userAccountFreezeDTO.setUserId(userId);
                    userAccountFreezeDTO.setCapTyp(Constants.ACM_TYPE_SETTLE);
                    userAccountDTO.setBody(userAccountFreezeDTO);
                    GenericRspDTO genericAcmRspDTO = accountManagementClient.freezeSettleAcBal(userAccountDTO);
                    if(JudgeUtils.isNotSuccess(genericAcmRspDTO.getMsgCd())){
                        logger.info("freezeSettleAcBalError : " + userId);
                    }
                } catch (LemonException e) {
                    continue;
                }
            }
        }

    }


    @Override
    public void settleCheckHandler(LocalDate checkDate, CheckControlDO checkControlDO, String stats)
            throws LemonException {
        String checkBatchNo = checkControlDO.getCheckBatchNo();
        LocalDateTime tradeDateTime = DateTimeUtils.getCurrentLocalDateTime();
        String checkTypeId = checkControlDO.getCheckTypeId();
        // 对账文件名称
        String checkFileName = "CPO_CSM_SUCC_" + DateTimeUtils.formatLocalDate(checkDate) + ".dat";
        if (stats.equals(Constants.ORDER_STATUS_FAILURE)) {
            checkFileName = "CPO_CSM_FAIL_" + DateTimeUtils.formatLocalDate(checkDate) + ".dat";
        }
        CheckParamDO checkParamDO = checkParamDao.get(checkTypeId);
        String checkFilePath = checkParamDO.getExportFilePath();
        String checkFile = checkFilePath + File.separator + checkFileName;
        File exportFile = new File(checkFile);
        // 本地存在对账文件则退出
        if (exportFile.exists()) {
            return;
        }
        // 查询总笔数总金额
        String dbStats = Constants.ORDER_STATUS_DATABASE_SUCCESS;
        if (stats.equals(Constants.ORDER_STATUS_FAILURE)) {
            dbStats = Constants.ORDER_STATUS_DATABASE_FAILURE;
        }
        FileTotalDO fileTotalDO = settleOrderDao.countTotalInfo(checkDate, dbStats);
        StringBuilder headerBuilder = new StringBuilder();
        headerBuilder.append(fileTotalDO.getTotalCnt()).append("|").append(fileTotalDO.getTotalAmt()).append("\n");
        try {
            FileUtils.writeAppend(headerBuilder.toString(), checkFile);
        } catch (Exception e1) {
            logger.info("checkDate:" + checkDate + " write checkFile header failure!");
            exportFile.delete();
            return;
        }

        // 查询待对账的数据
        SettleOrderDO qryInSettleOrderDO = new SettleOrderDO();
        qryInSettleOrderDO.setPaymentDate(checkDate);
        qryInSettleOrderDO.setStats(dbStats);
        List<SettleOrderDO> settleOrderDOs = settleOrderDao.getListByCondition(qryInSettleOrderDO);
        // 本地生成对账文件
        Integer totalCount = Integer.valueOf(0);
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (SettleOrderDO settleOrderDO : settleOrderDOs) {
            StringBuilder detailBuilder = new StringBuilder();
            BigDecimal withDrawAmt = settleOrderDO.getTradeAmt().subtract(settleOrderDO.getFee());
            // 生成每行数据
            detailBuilder.append(settleOrderDO.getBusOrderNo()).append("|").append(withDrawAmt).append("\n");
            // 写入对账文件
            try {
                FileUtils.writeAppend(detailBuilder.toString(), checkFile);
            } catch (Exception e) {
                logger.info("checkDate:" + checkDate + " write checkFile detail failure!");
                exportFile.delete();
                return;
            }
            totalCount++;
            totalAmt = totalAmt.add(withDrawAmt);
        }

        // 查询文件服务器参数
        String sftpSysChannel = checkParamDO.getSftpSysChannel();
        String sftpBusChannel = checkParamDO.getSftpBusChannel();
        FileConfigDO qryInFileConfigDO = new FileConfigDO();
        qryInFileConfigDO.setSftpSysChannel(sftpSysChannel);
        qryInFileConfigDO.setSftpBusChannel(sftpBusChannel);
        FileConfigDO fileConfigDO = fileConfigDao.getByCondition(qryInFileConfigDO);
        if (JudgeUtils.isNull(fileConfigDO)) {
            logger.info("checkDate:" + checkDate + " get sftp fileServer param failure!");
            exportFile.delete();
            return;
        }
        // 上传对账文件至文件服务器
        try {
            FileSftpUtils.upload(checkFile, fileConfigDO.getRemoteIp(), fileConfigDO.getRemotePort(),
                    fileConfigDO.getConnectTimeout(), fileConfigDO.getRemoteFilePath(), fileConfigDO.getRemoteName(),
                    fileConfigDO.getRemotePassword());
        } catch (Exception e) {
            logger.info("checkDate:" + checkDate + " upload checkFile failure!");
            exportFile.delete();
            return;
        }

        // 上传成功删除本地文件
        exportFile.delete();

        // 更新结算对账批次信息
        CheckControlDO updateValueCheckControlDO = new CheckControlDO();
        updateValueCheckControlDO.setTotalCount(totalCount);
        updateValueCheckControlDO.setTotalAmt(totalAmt);
        updateValueCheckControlDO.setModifyTime(tradeDateTime);
        CheckControlDO updateConditionCheckControlDO = new CheckControlDO();
        updateConditionCheckControlDO.setCheckBatchNo(checkBatchNo);
        checkControlDao.updateByCondition(updateValueCheckControlDO, updateConditionCheckControlDO);
    }

    /**
     * 商户营业厅结算对账差错撤单处理
     * @param settleRepairReqDTO
     * @throws LemonException
     */
    @Override
    public  void hallSettleRevokeHandler(SettleRepairReqDTO settleRepairReqDTO) throws LemonException{
        String settleOrderNo = settleRepairReqDTO.getOrderNo();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        // 查询结算订单信息
        SettleOrderDO settleOrderDO = settleOrderDao.get(settleOrderNo);
        if (JudgeUtils.isNull(settleOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.ORDER_NOT_EXISTS.getMsgCd());
        }
        if(JudgeUtils.notEquals(settleOrderDO.getStats(),Constants.ORDER_STATUS_DATABASE_SUCCESS)){
            LemonException.throwBusinessException(MsgCdEnum.ORDER_STATS_NOT_SUPPORT_REVOKE.getMsgCd());
        }
        String userId = settleOrderDO.getUserId();
        // 撤单订单状态
        String stats = Constants.ORDER_STATUS_DATABASE_FAILURE;
        String txJrnNo = generateOrderNo();
        // 账务处理
        hallsettleRevokeAccountTreat(userId, Constants.AC_TXSTS_N, txJrnNo,settleOrderDO);
        try {
            // 更新订单状态
            SettleOrderDO updateValueDO = new SettleOrderDO();
            updateValueDO.setStats(stats);
            updateValueDO.setPaymentDate(LemonUtils.getAccDate());
            updateValueDO.setPaymentTime(tradeTime);
            SettleOrderDO updateConditionDO = new SettleOrderDO();
            updateConditionDO.setOrderNo(settleOrderNo);
            updateConditionDO.setStats(settleOrderDO.getStats());
            updateSettleOrder(updateValueDO, updateConditionDO);
        } catch (LemonException e) {
            hallsettleRevokeAccountTreat(userId, Constants.AC_TXSTS_C, txJrnNo, settleOrderDO);
            LemonException.throwBusinessException(e.getMsgCd());
        }
    }
    //临时冲正
    @Override
    public void setTesttleAccountTreat() {
        SettleOrderDO settleOrderDO1 = new SettleOrderDO();
        SettleOrderDO settleOrderDO2 = new SettleOrderDO();
        SettleOrderDO settleOrderDO3 = new SettleOrderDO();
        SettleOrderDO settleOrderDO4 = new SettleOrderDO();
//        String tradeOrderNo = settleOrderDO.getOrderNo();
//        BigDecimal tradeAmt = settleOrderDO.getTradeAmt();
//        BigDecimal fee = settleOrderDO.getFee();
//        BigDecimal realTradeAmt = tradeAmt.subtract(fee);
//        LocalDate tradeDate = settleOrderDO.getTradeDate();
//        LocalTime tradeTime = settleOrderDO.getTradeTime();
        settleOrderDO1.setOrderNo("CSM2017122900300200023067");
        settleOrderDO1.setTradeAmt(BigDecimal.valueOf(0.90));
        settleOrderDO1.setFee(BigDecimal.valueOf(0));
        settleOrderDO1.setTradeDate(DateTimeUtils.getCurrentLocalDate());
        settleOrderDO1.setTradeTime(DateTimeUtils.getCurrentLocalTime());
        settleOrderDO2.setOrderNo("CSM2017122900300200023069");
        settleOrderDO2.setTradeAmt(BigDecimal.valueOf(4.50));
        settleOrderDO2.setFee(BigDecimal.valueOf(0));
        settleOrderDO2.setTradeDate(DateTimeUtils.getCurrentLocalDate());
        settleOrderDO2.setTradeTime(DateTimeUtils.getCurrentLocalTime());
        settleOrderDO3.setOrderNo("CSM2017122900300200023071");
        settleOrderDO3.setTradeAmt(BigDecimal.valueOf(3.60));
        settleOrderDO3.setFee(BigDecimal.valueOf(0));
        settleOrderDO3.setTradeDate(DateTimeUtils.getCurrentLocalDate());
        settleOrderDO3.setTradeTime(DateTimeUtils.getCurrentLocalTime());
        settleOrderDO4.setOrderNo("CSM2017122900300200023073");
        settleOrderDO4.setTradeAmt(BigDecimal.valueOf(20.08));
        settleOrderDO4.setFee(BigDecimal.valueOf(0));
        settleOrderDO4.setTradeDate(DateTimeUtils.getCurrentLocalDate());
        settleOrderDO4.setTradeTime(DateTimeUtils.getCurrentLocalTime());
        try {
            settleAccountTreat("****************", Constants.AC_TXSTS_C, "CSM2017122900300100023066", Constants.SETTLE_TYPE_AUTO, settleOrderDO1);
        } catch (LemonException e) {
            logger.info("错误"+e.getMessage() +e.getMsgInfo());
        }
        try {
            settleAccountTreat("****************", Constants.AC_TXSTS_C, "CSM2017122900300200023068", Constants.SETTLE_TYPE_AUTO, settleOrderDO2);
        } catch (LemonException e) {
            logger.info("错误"+e.getMessage() +e.getMsgInfo());
        }
        try {
            settleAccountTreat("****************", Constants.AC_TXSTS_C, "CSM2017122900300200023070", Constants.SETTLE_TYPE_AUTO, settleOrderDO3);
        } catch (LemonException e) {
            logger.info("错误"+e.getMessage() +e.getMsgInfo());
        }
        try {
            settleAccountTreat("****************", Constants.AC_TXSTS_C, "CSM2017122900300200023072", Constants.SETTLE_TYPE_AUTO, settleOrderDO4);
        } catch (LemonException e) {
            logger.info("错误"+e.getMessage() +e.getMsgInfo());
        }
    }


    /**
     * 消息中心推送
     * @param targetClient
     *         目标客户号
     * @param orderAmt
     *         订单金额
     * @throws LemonException
     */
    public void sendMsgCenterInfo(String targetClient, String orderAmt, String result) throws LemonException{
        String templeId = "";
        if(Constants.ORDER_STATUS_SUCCESS.equals(result)){
            templeId = Constants.TEMPLEID_SUCCESS;
        }else{
            templeId = Constants.TEMPLEID_FAIL;
        }
        logger.info("message send to userId : " + targetClient + ", templeId : " + templeId);
        String language = LemonUtils.getLocale().getLanguage();
        GenericDTO<MessageSendReqDTO> reqDTO = new GenericDTO<MessageSendReqDTO>();
        MessageSendReqDTO messageSendReqDTO = new MessageSendReqDTO();
        messageSendReqDTO.setUserId(targetClient);
        messageSendReqDTO.setMessageLanguage(language);
        messageSendReqDTO.setMessageTemplateId(templeId);
        Map<String, String> map = new HashMap<String, String>();
        map.put("userId", targetClient);
        map.put("orderAmt", orderAmt);
        messageSendReqDTO.setReplaceFieldMap(map);
        reqDTO.setBody(messageSendReqDTO);
        GenericRspDTO<NoBody> genericRspDTO = cmmServerClient.messageSend(reqDTO);
        if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
            LemonException.throwBusinessException(genericRspDTO.getMsgCd());
        }
    }

    /**
     * 国际化商品描述信息
     * @param busType
     * @param args
     * @return
     */
    public String getViewOrderInfo(String busType,Object[] args){
        try{
            String key="view.settle."+busType;
            return localeMessageSource.getMessage(key,args);
        }catch (Exception e){

        }
        return  null;
    }

    /**
     * 同步商户营业厅结算账单
     * @param merchantId
     * @param merchantName
     * @param desc
     * @param settleOrderDO
     */
    public void syncHallSettleBil(String merchantId, String merchantName, String desc,SettleOrderDO settleOrderDO){
        CreateUserBillDTO createUserBillDTO = new CreateUserBillDTO();
        BeanUtils.copyProperties(createUserBillDTO, settleOrderDO);
        createUserBillDTO.setTxTm(settleOrderDO.getBusOrderTime());
        createUserBillDTO.setOrderChannel("HALL");
        //付款方为营业厅
        createUserBillDTO.setPayerId(merchantId);
        createUserBillDTO.setPayeeId(settleOrderDO.getUserId());
        createUserBillDTO.setOrderAmt(settleOrderDO.getTradeAmt());
        createUserBillDTO.setFee(settleOrderDO.getFee());
        createUserBillDTO.setTxType("04");
        if(JudgeUtils.equals(settleOrderDO.getStats(),Constants.ORDER_STATUS_DATABASE_SUCCESS)){
            //状态映射，账单提现S1表示提现成功
            createUserBillDTO.setOrderStatus("S1");
        }else{
            createUserBillDTO.setOrderStatus(settleOrderDO.getStats());
        }
        createUserBillDTO.setGoodsInfo(desc);
        //商户名称为营业厅名称 方便商户账单表统计
        createUserBillDTO.setMercName(merchantName);
        billSyncHandler.createBill(createUserBillDTO);
    }

}
