/*
 * @ClassName SettleCardDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 15:14:00
 */
package com.hisun.lemon.csm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class SettleCardDO extends BaseDO {
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields userName 用户名称
     */
    private String userName;
    /**
     * @Fields capCorgNm 资金合作机构全称
     */
    private String capCorgNm;
    /**
     * @Fields capCorgSnm 资金合作机构简称
     */
    private String capCorgSnm;
    /**
     * @Fields capCorgNo 资金合作机构号
     */
    private String capCorgNo;
    /**
     * @Fields capCardNo 资金卡号
     */
    private String capCardNo;
    /**
     * @Fields capCardNm 资金卡户名
     */
    private String capCardName;
    /**
     * @Fields bnkMblNo 银行预留手机号码
     */
    private String bnkMblNo;
    /**
     * @Fields stats 状态0:无效 1:生效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields subbranch 分行名称
     */
    private String subbranch;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCapCorgNm() {
        return capCorgNm;
    }

    public void setCapCorgNm(String capCorgNm) {
        this.capCorgNm = capCorgNm;
    }

    public String getCapCorgSnm() {
        return capCorgSnm;
    }

    public void setCapCorgSnm(String capCorgSnm) {
        this.capCorgSnm = capCorgSnm;
    }

    public String getCapCorgNo() {
        return capCorgNo;
    }

    public void setCapCorgNo(String capCorgNo) {
        this.capCorgNo = capCorgNo;
    }

    public String getCapCardNo() {
        return capCardNo;
    }

    public void setCapCardNo(String capCardNo) {
        this.capCardNo = capCardNo;
    }

    public String getCapCardName() {
        return capCardName;
    }

    public void setCapCardName(String capCardName) {
        this.capCardName = capCardName;
    }

    public String getBnkMblNo() {
        return bnkMblNo;
    }

    public void setBnkMblNo(String bnkMblNo) {
        this.bnkMblNo = bnkMblNo;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getSubbranch() {
        return subbranch;
    }

    public void setSubbranch(String subbranch) {
        this.subbranch = subbranch;
    }
}