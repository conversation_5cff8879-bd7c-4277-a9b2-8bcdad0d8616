/*
 * @ClassName SettleOrderDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 18:53:35
 */
package com.hisun.lemon.csm.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.hisun.lemon.framework.data.BaseDO;

public class SettleOrderDO extends BaseDO {
    /**
     * @Fields orderNo 订单号
     */
    private String orderNo;
    /**
     * @Fields tradeDate 交易日期
     */
    private LocalDate tradeDate;
    /**
     * @Fields tradeTime 交易时间
     */
    private LocalTime tradeTime;
    /**
     * @Fields agreementPayDate 协议付款日期
     */
    private LocalDate agreementPayDate;
    /**
     * @Fields tradeAmt 结算金额
     */
    private BigDecimal tradeAmt;
    /**
     * @Fields fee 手续费
     */
    private BigDecimal fee;
    /**
     * @Fields ccy 币种
     */
    private String ccy;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields userId 用户号
     */
    private String userId;
    /**
     * 结算操作员
     */
    private String loginName;
    /**
     * @Fields userName 用户名称
     */
    private String userName;
    /**
     * @Fields mblNo 手机号码
     */
    private String mblNo;
    /**
     * @Fields capCorgNm 资金合作机构全称
     */
    private String capCorgNm;
    /**
     * @Fields capCorgSnm 资金合作机构简称
     */
    private String capCorgSnm;
    /**
     * @Fields capCorgNo 资金合作机构号
     */
    private String capCorgNo;
    /**
     * @Fields capCardNo 资金卡号
     */
    private String capCardNo;
    /**
     * @Fields capCardNm 资金卡户名
     */
    private String capCardNm;
    /**
     * @Fields bnkMblNo 银行预留手机号码
     */
    private String bnkMblNo;
    /**
     * @Fields stats 状态 0:初始状态 S1:待付款 S2:付款成功 F:付款失败
     */
    private String stats;
    /**
     * @Fields paymentDate 付款日期
     */
    private LocalDate paymentDate;
    /**
     * @Fields paymentTime 付款时间
     */
    private LocalTime paymentTime;
    /**
     * @Fields lastLastSettleDay 上上结算日
     */
    private LocalDate lastLastSettleDay;
    /**
     * @Fields lastSettleDay 上一结算日
     */
    private LocalDate lastSettleDay;
    /**
     * @Fields message 结算状态信息
     */
    private String message;
    /**
     * @Fields busOrderNo 业务订单号
     */
    private String busOrderNo;
    /**
     * @Fields busOrderTime 业务订单时间
     */
    private LocalDateTime busOrderTime;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields beginDate 查询开始日期
     */
    private LocalDate beginDate;
    /**
     * @Fields endDate 查询结束日期
     */
    private LocalDate endDate;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalDate getAgreementPayDate() {
        return agreementPayDate;
    }

    public void setAgreementPayDate(LocalDate agreementPayDate) {
        this.agreementPayDate = agreementPayDate;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getCapCorgNm() {
        return capCorgNm;
    }

    public void setCapCorgNm(String capCorgNm) {
        this.capCorgNm = capCorgNm;
    }

    public String getCapCorgSnm() {
        return capCorgSnm;
    }

    public void setCapCorgSnm(String capCorgSnm) {
        this.capCorgSnm = capCorgSnm;
    }

    public String getCapCorgNo() {
        return capCorgNo;
    }

    public void setCapCorgNo(String capCorgNo) {
        this.capCorgNo = capCorgNo;
    }

    public String getCapCardNo() {
        return capCardNo;
    }

    public void setCapCardNo(String capCardNo) {
        this.capCardNo = capCardNo;
    }

    public String getCapCardNm() {
        return capCardNm;
    }

    public void setCapCardNm(String capCardNm) {
        this.capCardNm = capCardNm;
    }

    public String getBnkMblNo() {
        return bnkMblNo;
    }

    public void setBnkMblNo(String bnkMblNo) {
        this.bnkMblNo = bnkMblNo;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public LocalTime getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(LocalTime paymentTime) {
        this.paymentTime = paymentTime;
    }

    public LocalDate getLastLastSettleDay() {
        return lastLastSettleDay;
    }

    public void setLastLastSettleDay(LocalDate lastLastSettleDay) {
        this.lastLastSettleDay = lastLastSettleDay;
    }

    public LocalDate getLastSettleDay() {
        return lastSettleDay;
    }

    public void setLastSettleDay(LocalDate lastSettleDay) {
        this.lastSettleDay = lastSettleDay;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public LocalDateTime getBusOrderTime() {
        return busOrderTime;
    }

    public void setBusOrderTime(LocalDateTime busOrderTime) {
        this.busOrderTime = busOrderTime;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public LocalDate getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDate beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

}