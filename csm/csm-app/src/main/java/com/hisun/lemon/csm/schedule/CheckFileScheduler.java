package com.hisun.lemon.csm.schedule;

import com.hisun.lemon.csm.service.chk.AbstractChkFileService;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 定时任务调度器 生成对账文件，上传到指定目录
 */
@Component
public class CheckFileScheduler {
	ExecutorService executorService = Executors.newFixedThreadPool(2);

	@Resource
	private List<AbstractChkFileService> scheduleService;

    @BatchScheduled(cron="0 0 0/1 * * ?")
	public void createChkFile(){
		for(AbstractChkFileService item:scheduleService){
			executorService.submit(item);
		}
	}
}
