package com.hisun.lemon.csm.service;

import java.time.LocalDate;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csm.dto.SettleApplyReqDTO;
import com.hisun.lemon.csm.dto.SettleApplyRspDTO;
import com.hisun.lemon.csm.dto.SettleBanlanceReqDTO;
import com.hisun.lemon.csm.dto.SettleBanlanceRspDTO;
import com.hisun.lemon.csm.dto.SettleConfirmReqDTO;
import com.hisun.lemon.csm.dto.SettleHallApplyReqDTO;
import com.hisun.lemon.csm.dto.SettleHallApplyRspDTO;
import com.hisun.lemon.csm.dto.SettleInformationRspDTO;
import com.hisun.lemon.csm.dto.SettleListReqDTO;
import com.hisun.lemon.csm.dto.SettleListReqDTO2;
import com.hisun.lemon.csm.dto.SettleListRspDTO;
import com.hisun.lemon.csm.dto.SettleListRspDTO2;
import com.hisun.lemon.csm.dto.SettleOrderDetailReqDTO;
import com.hisun.lemon.csm.dto.SettleOrderDetailRspDTO;
import com.hisun.lemon.csm.dto.SettleOrderResultReqDTO;
import com.hisun.lemon.csm.dto.SettleOrderResultRspDTO;
import com.hisun.lemon.csm.dto.SettleRepairReqDTO;
import com.hisun.lemon.csm.entity.CheckControlDO;

/**
 * 
 * @Description 清分结算服务接口
 * <AUTHOR>
 * @date 2017年7月7日 上午11:07:48
 * @version V1.0
 */
public interface ICsmService {
    
    /**
     * @Description 结算信息查询
     * <AUTHOR>
     * @return
     * @throws LemonException
     */
    SettleInformationRspDTO settleInformation(String mercId) throws LemonException;
    
    /**
     * @Description 结算账户余额查询
     * <AUTHOR>
     * @return
     * @throws LemonException
     */
    SettleBanlanceRspDTO settleBanlance(SettleBanlanceReqDTO settleBanlanceReqDTO) throws LemonException;
    
    /**
     * @Description 结算申请
     * <AUTHOR>
     * @param settleApplyReqDTO
     * @return
     * @throws LemonException
     */
    SettleApplyRspDTO settleApply(SettleApplyReqDTO settleApplyReqDTO) throws LemonException;
    
    /**
     * @Description 营业厅结算
     * <AUTHOR>
     * @param settleHallApplyReqDTO
     * @return
     * @throws LemonException
     */
    SettleHallApplyRspDTO settleHallApply(SettleHallApplyReqDTO settleHallApplyReqDTO) throws LemonException;
    
    /**
     * @Description 结算确认
     * <AUTHOR>
     * @param settleApplyReqDTO
     * @return
     * @throws LemonException
     */
    void settleConfirm(SettleConfirmReqDTO settleConfirmReqDTO) throws LemonException;
    
    /**
     * @Description 结算补单处理
     * <AUTHOR>
     * @param settleLongReqDTO
     * @throws LemonException
     */
    void orderRepairProcess(SettleRepairReqDTO settleRepairReqDTO) throws LemonException;
    
    /**
     * @Description 结算撤单处理
     * <AUTHOR>
     * @param settleLongReqDTO
     * @throws LemonException
     */
    void orderRevokeProcess(SettleRepairReqDTO settleLongReqDTO) throws LemonException;
    
    /**
     * @Description 结算订单列表列表
     * <AUTHOR>
     * @param settleListReqDTO
     * @return
     * @throws LemonException
     */
    SettleListRspDTO settleOrderList(SettleListReqDTO settleListReqDTO) throws LemonException;
    
    /**
     * @Description 结算订单列表列表
     * <AUTHOR>
     * @param settleListReqDTO2
     * @return
     * @throws LemonException
     */
    SettleListRspDTO2 settleOrderList2(SettleListReqDTO2 settleListReqDTO) throws LemonException;
    
    /**
     * @Description 结算订单列表详情
     * <AUTHOR>
     * @param settleOrderDetailReqDTO
     * @return
     * @throws LemonException
     */
    SettleOrderDetailRspDTO settleOrderDetail(SettleOrderDetailReqDTO settleOrderDetailReqDTO) throws LemonException;
    
    /**
     * @Description 结算订单结果查询
     * <AUTHOR>
     * @param settleOrderResultReqDTO
     * @return
     * @throws LemonException
     */
    SettleOrderResultRspDTO settleOrderResult(SettleOrderResultReqDTO settleOrderResultReqDTO) throws LemonException;
    
    /**
     * @Description 自动结算定时任务处理
     * <AUTHOR>
     * @param checkDate
     * @param checkControlDO
     * @throws LemonException
     */
    void settleAutoHandler(LocalDate checkDate, CheckControlDO checkControlDO) throws LemonException;

    /**
     * @Description 自动结算定时任务前处理
     * <AUTHOR>
     * @param checkDate
     * @throws LemonException
     */
    void settleAutoHandlerPre(LocalDate checkDate) throws LemonException;
    
    /**
     * @Description 结算对账文件定时任务处理
     * <AUTHOR>
     * @param checkDate
     * @param checkControlDO
     * @throws LemonException
     */
    void settleCheckHandler(LocalDate checkDate, CheckControlDO checkControlDO, String stats) throws LemonException;

    /**
     * 营业厅商户结算对账差错撤单处理
     * @param settleRepairReqDTO
     * @throws LemonException
     */
    void hallSettleRevokeHandler(SettleRepairReqDTO settleRepairReqDTO) throws LemonException;

    void setTesttleAccountTreat();
}
