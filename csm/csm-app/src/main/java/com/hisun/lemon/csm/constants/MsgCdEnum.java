package com.hisun.lemon.csm.constants;

/**
 * @Description 结算模块错误码枚举类
 * <AUTHOR>
 * @date 2017年7月7日 下午3:27:58
 * @version V1.0
 */
public enum MsgCdEnum {

    /** 交易成功 */
    SUCCESS("CMM00000", "transaction successfull"),
    /** 手机号码不能为空 */
    MBLNO_CANTNULL("CSM10001", "phone number cannot be empty"),
    /** 用户号不能为空 */
    USERID_CANTNULL("CSM10002", "userid cannot be empty"),
    /** 结算状态不能为空 */
    SETTLE_STATS_CANTNULL("CSM10003", "settle stats type cannot be empty"),
    /** 查询周期不能为空 */
    QRY_TIME_CANTNULL("CSM10004", "settle time type cannot be empty"),
    /** 订单号不能为空 */
    ORDER_NO_CANTNULL("CSM10005", "settle order no cannot be empty"),
    /** 结算金额不能为空 */
    SETTLE_AMT_CANTNULL("CSM10006", "settle amt cannot be empty"),
    /** 结算手续费不能为空 */
    SETTLE_FEE_CANTNULL("CSM10007", "settle fee cannot be empty"),
    /** 支付密码不能为空 */
    PAY_PASSWORD_CANTNULL("CSM10008", "pay password cannot be empty"),
    /** 订单状态不能为空 */
    ORDER_STATS_CANTNULL("CSM10009", "order stats cannot be empty"),
    /** 结算类型不能为空 */
    SETTLE_TYPE_CANTNULL("CSM10010", "settle type cannot be empty"),
    /** 对账类型不能为空 */
    CHECK_TYPE_CANTNULL("CSM10011", "check subtype cannot be empty"),
    /** 支付密码随机数不能为空 */
    PAY_PASSWORD_RANDOM_CANTNULL("CSM10012", "pay password random cannot be empty"),
    /** 当前页码 */
    PAGE_NUMBER_CANTNULL("CSM19998", "message language cannot be empty"),
    /** 每页大小 */
    PAGE_SIZE_CANTNULL("CSM19999", "message content cannot be empty"),
    /** 结算申请失败 */
    SETTLE_APPLY_FAIL("CSM30002", "settle apply failure"),
    /** 结算方式有误 */
    SETTLE_TYPE_ERROR("CSM30003", "settle type error"),
    /** 结算银行卡不存在或失效 */
    SETTLE_CARD_NOTEXISTS("CSM30004", "settle card not exists"),
    /** 结算信息不存在或失效 */
    SETTLE_INFO_NOTEXISTS("CSM30005", "settle info not exists"),
    /** 结算确认失败 */
    SETTLE_CONFIRM_FAIL("CSM30006", "settle confirm failure"),
    /** 待结算账户余额不足 */
    SETTLE_ACOUNT_INSUFF("CSM30007", "settle account balance insufficient"),
    /** 手续费有误 */
    SETTLE_FEE_ERROR("CSM30008", "trade fee error"),
    /** 银行卡解密失败 */
    CARD_DECRYPT_FAIL("CSM30009", "card decrypt failure"),
    /** 银行卡脱敏失败 */
    CARD_DESENS_FAIL("CSM30010", "card desensitization failure"),
    /** 订单不存在 */
    ORDER_NOT_EXISTS("CSM30011", "order not exists"),
    /** 交易金额不能为0 */
    TRADE_AMT_ZERO("CSM30012", "trade amt zero"),
    /** 对账补单失败 */
    CHECK_REPAIR_FAIL("CSM30013", "check order repair process failure"),
    /** 到账金额不能为0 */
    REAL_AMT_ZERO("CSM30014", "real trade amt zero"),
    /** 商户结算交易的权限 */
    NO_SETTLE_AUTH("CSM30015", "no settle auth"),
    ORDER_STATS_NOT_SUPPORT_REVOKE("CSM39994", "Order status is not allowed to withdraw orders!"),
    FAIL_GET_LOCAL_PATH("CSM39995", "Failed to get local path!"),
    CHECK_FLAG_FILE_CREATE_FAILURE("CSM39996", "check flag file create fail"),
    UPLOAD_CHECK_FILE_FAILURE("CSM39997", "upload check file to server fail"),
    /** 对账组件配置有误 */
    CHECK_COMPONENT_NOT_EXISTS("CSM39998", "check component not exists"),
    /** 对账组件生成失败 */
    CHECK_COMPONENT_GEN_FAILURE("CSM39999", "check component generate failure"),
    LAST("CMM39999", "last");

    private String msgCd;
    private String msgInfo;

    private MsgCdEnum(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    public String getMsgCd() {
        return msgCd;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

}
