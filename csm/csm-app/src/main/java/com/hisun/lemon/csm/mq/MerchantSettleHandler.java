package com.hisun.lemon.csm.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csm.constants.Constants;
import com.hisun.lemon.csm.entity.SettleOrderDO;
import com.hisun.lemon.framework.stream.MultiOutput;
import com.hisun.lemon.framework.stream.producer.Producer;
import com.hisun.lemon.framework.stream.producer.Producers;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.tfm.dto.TradeFeeReqDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 商户结算业务，登记商户服务费订单
 */
@Component
public class MerchantSettleHandler {
    protected static final Logger logger = LoggerFactory.getLogger(MerchantSettleHandler.class);

    @Resource
    ObjectMapper objectMapper;
    /**
     * 登记商户服务费
     */
    @Producers({
            @Producer(beanName= "merchantTransferTradeFeeConsumer", channelName= MultiOutput.OUTPUT_THREE)
    })
    public TradeFeeReqDTO registMerChantTransferFee(SettleOrderDO settleOrderDO){
        //商户自主结算/商户自动结算
        if(JudgeUtils.equals(settleOrderDO.getBusType(), Constants.SETTLE_BUSTYPE_SELF)
                || JudgeUtils.equals(settleOrderDO.getBusType(), Constants.SETTLE_BUSTYPE_AUTO)
                || JudgeUtils.equals(settleOrderDO.getBusType(), Constants.SETTLE_BUSTYPE_HALL)){
            TradeFeeReqDTO tradeFeeReqDTO=new TradeFeeReqDTO();
            tradeFeeReqDTO.setCcy(settleOrderDO.getCcy());
            tradeFeeReqDTO.setUserId(settleOrderDO.getUserId());
            tradeFeeReqDTO.setBusOrderNo(settleOrderDO.getOrderNo());
            tradeFeeReqDTO.setBusOrderTime(DateTimeUtils.getCurrentLocalDateTime());
            tradeFeeReqDTO.setTradeAmt(settleOrderDO.getTradeAmt());
            tradeFeeReqDTO.setBusType(settleOrderDO.getBusType());
            String data = ObjectMapperHelper.writeValueAsString(objectMapper, tradeFeeReqDTO, true);
            logger.info("登记商户结算不清分手续费写入消息队列数据：" + data);
            return tradeFeeReqDTO;
        }
        logger.info("非商户自主/自动/营业厅结算订单，不登记商户手续费：" + settleOrderDO.getOrderNo());
        return null;
    }
}
