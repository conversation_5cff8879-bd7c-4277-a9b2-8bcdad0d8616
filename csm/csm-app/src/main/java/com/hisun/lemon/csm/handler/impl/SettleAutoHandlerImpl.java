package com.hisun.lemon.csm.handler.impl;

import java.time.LocalDate;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csm.entity.CheckControlDO;
import com.hisun.lemon.csm.handler.ICheckProcessHandler;
import com.hisun.lemon.csm.service.ICsmService;
import com.hisun.lemon.framework.lock.Locked;

/**
 * @Description 自动结算定时任务处理
 * <AUTHOR>
 * @date 2017年9月9日 上午11:02:27 
 * @version V1.0
 */
@Component
public class SettleAutoHandlerImpl implements ICheckProcessHandler {
    
    @Resource
    private ICsmService csmService;
    
    @Locked(lockName = "SettleHandlerLock", leaseTime=60, waitTime=30)
    public void checkFileProcess(LocalDate checkDate, CheckControlDO checkControlDO) throws LemonException {
        csmService.settleAutoHandler(checkDate, checkControlDO);
    }

}
