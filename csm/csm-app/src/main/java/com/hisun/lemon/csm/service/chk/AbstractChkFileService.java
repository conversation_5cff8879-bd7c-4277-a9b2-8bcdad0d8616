package com.hisun.lemon.csm.service.chk;

import com.hisun.lemon.csm.utils.ChkFileUtil;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.concurrent.Callable;


/**
 * <AUTHOR>
 * @date 2017年8月5日
 * @time 下午2:54:28
 *
 */
public abstract class AbstractChkFileService extends BaseService implements Callable {
    protected static final Logger logger = LoggerFactory.getLogger(AbstractChkFileService.class);

    @Resource
    private DistributedLocker locker;

    @Resource
    protected ChkFileUtil chkFileUtil;

    protected String lockName;

    protected String[] chkOrderStatus;

    protected String appCnl;

    public AbstractChkFileService() {

    }


    @Transactional(readOnly = true)
    protected void execute() {
    }


    @Override
    public Object call() throws Exception {
        logger.info("唤起生成"+appCnl+"数据对账文件任务");
        locker.lock(lockName, 18, 22, () -> {
            execute();
            return null;
        });
        return null;
    }

}
