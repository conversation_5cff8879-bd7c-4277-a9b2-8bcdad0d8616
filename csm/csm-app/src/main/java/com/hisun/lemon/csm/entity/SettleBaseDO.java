/*
 * @ClassName SettleBaseDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 15:59:57
 */
package com.hisun.lemon.csm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class SettleBaseDO extends BaseDO {
    /**
     * @Fields userId 用户号
     */
    private String userId;
    /**
     * @Fields userName 用户名称
     */
    private String userName;
    /**
     * @Fields mblNo 手机号码
     */
    private String mblNo;
    /**
     * @Fields settleType 结算方式 auto:自动 self:自主
     */
    private String settleType;
    /**
     * @Fields settleCycleType 结算周期类型 日结算:daily 周结算:weekly 月结算:monthly
     */
    private String settleCycleType;
    /**
     * @Fields agreementPayDays 协议付款天数
     */
    private String agreementPayDays;
    /**
     * @Fields settleSites 结算地点
     */
    private String settleSites;
    /**
     * @Fields settleSitesName 结算地点名称
     */
    private String settleSitesName;
    /**
     * @Fields hallSites 营业厅网点
     */
    private String hallSites;
    /**
     * @Fields hallSitesName 营业厅网点名称
     */
    private String hallSitesName;
    /**
     * @Fields lastLastSettleDay 上上结算日
     */
    private LocalDate lastLastSettleDay;
    /**
     * @Fields lastSettleDay 上一结算日
     */
    private LocalDate lastSettleDay;
    /**
     * @Fields nextSettleDay 下一结算日
     */
    private LocalDate nextSettleDay;
    /**
     * @Fields stats 状态0:无效 1:生效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getSettleType() {
        return settleType;
    }

    public void setSettleType(String settleType) {
        this.settleType = settleType;
    }

    public String getSettleCycleType() {
        return settleCycleType;
    }

    public void setSettleCycleType(String settleCycleType) {
        this.settleCycleType = settleCycleType;
    }

    public String getAgreementPayDays() {
        return agreementPayDays;
    }

    public void setAgreementPayDays(String agreementPayDays) {
        this.agreementPayDays = agreementPayDays;
    }

    public String getSettleSites() {
        return settleSites;
    }

    public void setSettleSites(String settleSites) {
        this.settleSites = settleSites;
    }

    public String getSettleSitesName() {
        return settleSitesName;
    }

    public void setSettleSitesName(String settleSitesName) {
        this.settleSitesName = settleSitesName;
    }

    public String getHallSites() {
        return hallSites;
    }

    public void setHallSites(String hallSites) {
        this.hallSites = hallSites;
    }

    public String getHallSitesName() {
        return hallSitesName;
    }

    public void setHallSitesName(String hallSitesName) {
        this.hallSitesName = hallSitesName;
    }

    public LocalDate getLastLastSettleDay() {
        return lastLastSettleDay;
    }

    public void setLastLastSettleDay(LocalDate lastLastSettleDay) {
        this.lastLastSettleDay = lastLastSettleDay;
    }

    public LocalDate getLastSettleDay() {
        return lastSettleDay;
    }

    public void setLastSettleDay(LocalDate lastSettleDay) {
        this.lastSettleDay = lastSettleDay;
    }

    public LocalDate getNextSettleDay() {
        return nextSettleDay;
    }

    public void setNextSettleDay(LocalDate nextSettleDay) {
        this.nextSettleDay = nextSettleDay;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}