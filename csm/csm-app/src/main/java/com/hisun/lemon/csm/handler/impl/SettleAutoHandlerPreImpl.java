package com.hisun.lemon.csm.handler.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csm.entity.CheckControlDO;
import com.hisun.lemon.csm.handler.ICheckProcessHandler;
import com.hisun.lemon.csm.service.ICsmService;
import com.hisun.lemon.framework.lock.Locked;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * @Description 自动结算定时任务前处理
 * <AUTHOR>
 * @date 2018年2月24日 上午11:02:27
 * @version V1.0
 */
@Component
public class SettleAutoHandlerPreImpl implements ICheckProcessHandler {
    
    @Resource
    private ICsmService csmService;
    
    @Locked(lockName = "SettleHandlerPreLock", leaseTime=60, waitTime=30)
    public void checkFileProcess(LocalDate checkDate, CheckControlDO checkControlDO) throws LemonException {
        csmService.settleAutoHandler(checkDate, checkControlDO);
    }

}
