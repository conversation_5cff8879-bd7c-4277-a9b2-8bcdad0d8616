/*
 * @ClassName CheckControlDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-07 10:47:52
 */
package com.hisun.lemon.csm.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class CheckControlDO extends BaseDO {
    /**
     * @Fields checkBatchNo 对账批次号
     */
    private String checkBatchNo;
    /**
     * @Fields checkDate 对账清算日期
     */
    private LocalDate checkDate;
    /**
     * @Fields checkSeq 对账批次序号
     */
    private Integer checkSeq;
    /**
     * @Fields checkTypeId 对账类型
     */
    private String checkTypeId;
    /**
     * @Fields checkFileNm 对账文件名称
     */
    private String checkFileNm;
    /**
     * @Fields checkBeginTime 对账开始时间
     */
    private LocalDateTime checkBeginTime;
    /**
     * @Fields checkEndTime 对账结束时间
     */
    private LocalDateTime checkEndTime;
    /**
     * @Fields checkStats 对账状态 0:待对账 1:已获取文件 2:已入库 3:已对账 4:已差错处理 9:对账完成
     */
    private String checkStats;
    /**
     * @Fields fileReceiveCount 文件总笔数
     */
    private Integer fileReceiveCount;
    /**
     * @Fields fileReceiveAmt 文件总金额
     */
    private BigDecimal fileReceiveAmt;
    /**
     * @Fields totalCount 总笔数
     */
    private Integer totalCount;
    /**
     * @Fields totalAmt 总金额
     */
    private BigDecimal totalAmt;
    /**
     * @Fields shortCount 我方有对方无笔数
     */
    private Integer shortCount;
    /**
     * @Fields shortAmt 我方有对方无金额
     */
    private BigDecimal shortAmt;
    /**
     * @Fields longCount 对方有我方无笔数
     */
    private Integer longCount;
    /**
     * @Fields longAmt 对方有我方无金额
     */
    private BigDecimal longAmt;
    /**
     * @Fields errorCount 差错笔数
     */
    private Integer errorCount;
    /**
     * @Fields errorAmt 差错金额
     */
    private BigDecimal errorAmt;
    /**
     * @Fields doubtCount 存疑笔数
     */
    private Integer doubtCount;
    /**
     * @Fields doubtAmt 存疑金额
     */
    private BigDecimal doubtAmt;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getCheckBatchNo() {
        return checkBatchNo;
    }

    public void setCheckBatchNo(String checkBatchNo) {
        this.checkBatchNo = checkBatchNo;
    }

    public LocalDate getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(LocalDate checkDate) {
        this.checkDate = checkDate;
    }

    public Integer getCheckSeq() {
        return checkSeq;
    }

    public void setCheckSeq(Integer checkSeq) {
        this.checkSeq = checkSeq;
    }

    public String getCheckTypeId() {
        return checkTypeId;
    }

    public void setCheckTypeId(String checkTypeId) {
        this.checkTypeId = checkTypeId;
    }

    public String getCheckFileNm() {
        return checkFileNm;
    }

    public void setCheckFileNm(String checkFileNm) {
        this.checkFileNm = checkFileNm;
    }

    public LocalDateTime getCheckBeginTime() {
        return checkBeginTime;
    }

    public void setCheckBeginTime(LocalDateTime checkBeginTime) {
        this.checkBeginTime = checkBeginTime;
    }

    public LocalDateTime getCheckEndTime() {
        return checkEndTime;
    }

    public void setCheckEndTime(LocalDateTime checkEndTime) {
        this.checkEndTime = checkEndTime;
    }

    public String getCheckStats() {
        return checkStats;
    }

    public void setCheckStats(String checkStats) {
        this.checkStats = checkStats;
    }

    public Integer getFileReceiveCount() {
        return fileReceiveCount;
    }

    public void setFileReceiveCount(Integer fileReceiveCount) {
        this.fileReceiveCount = fileReceiveCount;
    }

    public BigDecimal getFileReceiveAmt() {
        return fileReceiveAmt;
    }

    public void setFileReceiveAmt(BigDecimal fileReceiveAmt) {
        this.fileReceiveAmt = fileReceiveAmt;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public Integer getShortCount() {
        return shortCount;
    }

    public void setShortCount(Integer shortCount) {
        this.shortCount = shortCount;
    }

    public BigDecimal getShortAmt() {
        return shortAmt;
    }

    public void setShortAmt(BigDecimal shortAmt) {
        this.shortAmt = shortAmt;
    }

    public Integer getLongCount() {
        return longCount;
    }

    public void setLongCount(Integer longCount) {
        this.longCount = longCount;
    }

    public BigDecimal getLongAmt() {
        return longAmt;
    }

    public void setLongAmt(BigDecimal longAmt) {
        this.longAmt = longAmt;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public BigDecimal getErrorAmt() {
        return errorAmt;
    }

    public void setErrorAmt(BigDecimal errorAmt) {
        this.errorAmt = errorAmt;
    }

    public Integer getDoubtCount() {
        return doubtCount;
    }

    public void setDoubtCount(Integer doubtCount) {
        this.doubtCount = doubtCount;
    }

    public BigDecimal getDoubtAmt() {
        return doubtAmt;
    }

    public void setDoubtAmt(BigDecimal doubtAmt) {
        this.doubtAmt = doubtAmt;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}