package com.hisun.lemon.csm.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.extension.ExtensionLoader;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.csm.constants.Constants;
import com.hisun.lemon.csm.constants.MsgCdEnum;
import com.hisun.lemon.csm.dao.ICheckControlDao;
import com.hisun.lemon.csm.dao.ICheckParamDao;
import com.hisun.lemon.csm.entity.CheckControlDO;
import com.hisun.lemon.csm.entity.CheckParamDO;
import com.hisun.lemon.csm.handler.ICheckProcessHandler;
import com.hisun.lemon.csm.service.ICheckProcessService;

@Transactional
@Service
public class CheckProcessServiceImpl implements ICheckProcessService {

    @Resource
    private ICheckParamDao checkParamDao;
    @Resource
    private ICheckControlDao checkControlDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String checkFileGet(CheckParamDO checkParamDO, CheckControlDO checkControlDO) throws LemonException {
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        // 获取对账文件
        // 更新对账状态
        CheckControlDO updateValueCheckControlDO = new CheckControlDO();
        updateValueCheckControlDO.setCheckStats(Constants.CHECK_FILE_GET);
        updateValueCheckControlDO.setModifyTime(tradeTime);
        CheckControlDO updateConditionCheckControlDO = new CheckControlDO();
        updateConditionCheckControlDO.setCheckBatchNo(checkControlDO.getCheckBatchNo());
        checkControlDao.updateByCondition(updateValueCheckControlDO, updateConditionCheckControlDO);
        return Constants.CHECK_FILE_GET;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String checkFileDatebase(CheckParamDO checkParamDO, CheckControlDO checkControlDO) throws LemonException {
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        // 对账文件入库
        // 更新对账状态
        CheckControlDO updateValueCheckControlDO = new CheckControlDO();
        updateValueCheckControlDO.setCheckStats(Constants.CHECK_FILE_DATABASE);
        updateValueCheckControlDO.setModifyTime(tradeTime);
        CheckControlDO updateConditionCheckControlDO = new CheckControlDO();
        updateConditionCheckControlDO.setCheckBatchNo(checkControlDO.getCheckBatchNo());
        checkControlDao.updateByCondition(updateValueCheckControlDO, updateConditionCheckControlDO);
        return Constants.CHECK_FILE_DATABASE;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String checkProcess(LocalDate checkDate, CheckParamDO checkParamDO, CheckControlDO checkControlDO) throws LemonException {
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        // 对账处理
        String checkPorcessComponent = checkParamDO.getCheckProcessComponent();
        String checkProcessClazz = checkParamDO.getCheckProcessClazz();
        if (StringUtils.isBlank(checkPorcessComponent)) {
            LemonException.throwBusinessException(MsgCdEnum.CHECK_COMPONENT_NOT_EXISTS.getMsgCd());
        }
        try {
            ICheckProcessHandler checkControlHandler = (ICheckProcessHandler) ExtensionLoader.getSpringBean(checkPorcessComponent, Class.forName(checkProcessClazz));
            checkControlHandler.checkFileProcess(checkDate, checkControlDO);
        } catch (ClassNotFoundException e) {
            LemonException.throwBusinessException(MsgCdEnum.CHECK_COMPONENT_GEN_FAILURE.getMsgCd());
        }
        // 更新对账状态
        CheckControlDO updateValueCheckControlDO = new CheckControlDO();
        updateValueCheckControlDO.setCheckStats(Constants.CHECK_FILE_PROCESS);
        updateValueCheckControlDO.setModifyTime(tradeTime);
        CheckControlDO updateConditionCheckControlDO = new CheckControlDO();
        updateConditionCheckControlDO.setCheckBatchNo(checkControlDO.getCheckBatchNo());
        checkControlDao.updateByCondition(updateValueCheckControlDO, updateConditionCheckControlDO);
        return Constants.CHECK_FILE_PROCESS;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String checkErrorProcess(CheckParamDO checkParamDO, CheckControlDO checkControlDO) throws LemonException {
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        // 差错处理
        // 更新对账状态
        CheckControlDO updateValueCheckControlDO = new CheckControlDO();
        updateValueCheckControlDO.setCheckStats(Constants.CHECK_ERROR_PROCESS);
        updateValueCheckControlDO.setModifyTime(tradeTime);
        CheckControlDO updateConditionCheckControlDO = new CheckControlDO();
        updateConditionCheckControlDO.setCheckBatchNo(checkControlDO.getCheckBatchNo());
        checkControlDao.updateByCondition(updateValueCheckControlDO, updateConditionCheckControlDO);
        return Constants.CHECK_ERROR_PROCESS;
    }
}
