/*
 * @ClassName ICheckParamDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-07 10:47:20
 */
package com.hisun.lemon.csm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.csm.entity.CheckParamDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface ICheckParamDao extends BaseDao<CheckParamDO> {
    
    CheckParamDO getByCondition(CheckParamDO checkParamDO);
    
    List<CheckParamDO> getListByCondition(CheckParamDO checkParamDO);
}