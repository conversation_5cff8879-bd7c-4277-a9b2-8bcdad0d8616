/*
 * @ClassName FileConfigDO
 * @Description 
 * @version 1.0
 * @Date 2017-09-10 20:41:53
 */
package com.hisun.lemon.csm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class FileConfigDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private String id;
    /**
     * @Fields sftpSysChannel 文件服务器系统渠道
     */
    private String sftpSysChannel;
    /**
     * @Fields sftpBusChannel 文件服务器业务渠道
     */
    private String sftpBusChannel;
    /**
     * @Fields remoteName 文件服务器用户名
     */
    private String remoteName;
    /**
     * @Fields remotePassword 文件服务器密码
     */
    private String remotePassword;
    /**
     * @Fields remoteIp 文件服务器IP地址
     */
    private String remoteIp;
    /**
     * @Fields remotePort 文件服务器端口
     */
    private int remotePort;
    /**
     * @Fields connectTimeout 连接超时时间 单位:毫秒
     */
    private Integer connectTimeout;
    /**
     * @Fields remoteFilePath 文件上传路径
     */
    private String remoteFilePath;
    /**
     * @Fields remoteFileName 文件上传名称
     */
    private String remoteFileName;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSftpSysChannel() {
        return sftpSysChannel;
    }

    public void setSftpSysChannel(String sftpSysChannel) {
        this.sftpSysChannel = sftpSysChannel;
    }

    public String getSftpBusChannel() {
        return sftpBusChannel;
    }

    public void setSftpBusChannel(String sftpBusChannel) {
        this.sftpBusChannel = sftpBusChannel;
    }

    public String getRemoteName() {
        return remoteName;
    }

    public void setRemoteName(String remoteName) {
        this.remoteName = remoteName;
    }

    public String getRemotePassword() {
        return remotePassword;
    }

    public void setRemotePassword(String remotePassword) {
        this.remotePassword = remotePassword;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public int getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(int remotePort) {
        this.remotePort = remotePort;
    }

    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public String getRemoteFilePath() {
        return remoteFilePath;
    }

    public void setRemoteFilePath(String remoteFilePath) {
        this.remoteFilePath = remoteFilePath;
    }

    public String getRemoteFileName() {
        return remoteFileName;
    }

    public void setRemoteFileName(String remoteFileName) {
        this.remoteFileName = remoteFileName;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}