/*
 * @ClassName ICheckControlDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-07 10:47:52
 */
package com.hisun.lemon.csm.dao;

import java.time.LocalDate;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.csm.entity.CheckControlDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface ICheckControlDao extends BaseDao<CheckControlDO> {
    
    int getMaxCheckSeq(@Param("checkTypeId")String checkTypeId, @Param("checkDate")LocalDate checkDate);
    
    CheckControlDO getByCondition(CheckControlDO checkControlDO);
    
    List<CheckControlDO> getListByCondition(CheckControlDO checkControlDO);
    
    List<CheckControlDO> getUnfinishListByCondition(CheckControlDO checkControlDO);
    
    int updateByCondition(@Param("valueDO")CheckControlDO updateValueCheckControlDO, @Param("conditionDO")CheckControlDO updateConditionCheckControlDO);
}