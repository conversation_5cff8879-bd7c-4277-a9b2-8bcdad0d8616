package com.hisun.lemon.csm.schedule;

import java.time.LocalDate;
import javax.annotation.Resource;
import com.hisun.lemon.csm.service.ICsmService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.csm.service.ICheckControlService;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;

/**
 * @Description 登记对账批次
 * <AUTHOR>
 * @date 2017年8月7日 上午10:50:32
 * @version V1.0
 */
@Component
public class CheckBatchRegisterSchedule {

    private static final Logger logger = LoggerFactory.getLogger(CheckBatchRegisterSchedule.class);

    @Resource
    private ICheckControlService checkControlService;

    @Resource
    private ICsmService iCsmService;

    /**
     * 定时任务执行方法 cronExpression表达式规则： "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者
     * JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)" "0 0 12 * * ?" 每天中午12点触发
     * “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” 
     * “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 0 * * * ?")
    public void execute() {
        logger.debug("==================CheckBatRegisterSchedule.execute() beginTime: "+ DateTimeUtils.getCurrentDateTimeStr());
        // 获取对账日期
        LocalDate checkDate = DateTimeUtils.getCurrentLocalDate();
        // 登记对账批次
        boolean autoFlg = checkControlService.checkBatchRegister(checkDate);
        if(autoFlg){
            iCsmService.settleAutoHandlerPre(checkDate);
        }
        logger.debug("==================CheckBatRegisterSchedule.execute() endTime: " + DateTimeUtils.getCurrentDateTimeStr());
    }
}
