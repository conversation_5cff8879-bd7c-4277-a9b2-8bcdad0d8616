/*
 * @ClassName ISettleOrderDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 18:53:35
 */
package com.hisun.lemon.csm.dao;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.csm.entity.FileTotalDO;
import com.hisun.lemon.csm.entity.SettleOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface ISettleOrderDao extends BaseDao<SettleOrderDO> {
    
    SettleOrderDO getByCondition(SettleOrderDO settleOrderDO);
    
    FileTotalDO countTotalInfo(@Param("checkDate")LocalDate checkDate, @Param("stats")String stats);

    List<SettleOrderDO> getListByCondition(SettleOrderDO settleOrderDO);
    
    int updateByCondition(@Param("valueDO") SettleOrderDO valueDO, @Param("conditionDO") SettleOrderDO conditionDO);

    List<SettleOrderDO> querySettleList(Map o);


}