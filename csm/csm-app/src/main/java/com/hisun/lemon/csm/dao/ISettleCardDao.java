/*
 * @ClassName ISettleCardDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 15:14:00
 */
package com.hisun.lemon.csm.dao;

import com.hisun.lemon.csm.entity.SettleCardDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ISettleCardDao extends BaseDao<SettleCardDO> {
    
    SettleCardDO getByCondition(SettleCardDO settleCardDO);
}