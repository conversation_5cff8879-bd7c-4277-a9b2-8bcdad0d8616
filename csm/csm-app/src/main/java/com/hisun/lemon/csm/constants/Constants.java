package com.hisun.lemon.csm.constants;

/**
 * @Description 结算模块常量类
 * <AUTHOR>
 * @date 2017年7月14日 下午5:49:21 
 * @version V1.0
 */
public class Constants {
    
    private Constants() {
        throw new IllegalStateException("Utility class");
    }
    
    /** 校验枚举值 初始状态 */
    public static final String CHECK_INIT = "0";
    /** 校验枚举值 验证状态 */
    public static final String CHECK_VERIFIED = "1";
    /** 校验枚举值 失效状态 */
    public static final String CHECK_EXPIRE = "2";
    /** 状态枚举值 无效状态 */
    public static final String STATUS_INVALID = "0";
    /** 状态枚举值 有效状态 */
    public static final String STATUS_AVAILABLE = "1";
    /** 时间周期枚举值 今天 */
    public static final String TIME_CYCLE_TODAY = "today";
    /** 时间周期枚举值 日 */
    public static final String TIME_CYCLE_DAILY = "daily";
    /** 时间周期枚举值 一周 */
    public static final String TIME_CYCLE_WEEK = "weekly";
    /** 时间周期枚举值 一月 */
    public static final String TIME_CYCLE_MONTH = "monthly";
    /** 订单状态枚举值 待付款 */
    public static final String ORDER_STATUS_ACCEPT = "accept";
    /** 订单状态枚举值 成功 */
    public static final String ORDER_STATUS_SUCCESS = "success";
    /** 订单状态枚举值 失败 */
    public static final String ORDER_STATUS_FAILURE = "failure";
    /** 订单状态枚举值 订单不存在 */
    public static final String ORDER_STATUS_NONE = "none";
    /** 订单状态数据库枚举值 待付款 */
    public static final String ORDER_STATUS_DATABASE_ACCEPT = "S1";
    /** 订单状态数据库枚举值 付款成功 */
    public static final String ORDER_STATUS_DATABASE_SUCCESS = "S2";
    /** 订单状态数据库枚举值 付款失败 */
    public static final String ORDER_STATUS_DATABASE_FAILURE = "F";
    /** 结算类型枚举值 自主结算 */
    public static final String SETTLE_TYPE_SELF = "self";
    /** 结算类型枚举值 自动结算 */
    public static final String SETTLE_TYPE_AUTO = "auto";
    /** 结算类型枚举值 营业厅结算 */
    public static final String SETTLE_TYPE_HALL = "hall";
    /** 结算类型枚举值 自主结算 */
    public static final String SETTLE_BUSTYPE_SELF = "0403";
    /** 结算业务类型枚举值 自动结算 */
    public static final String SETTLE_BUSTYPE_AUTO = "0404";
    /** 结算业务类型枚举值 营业厅结算 */
    public static final String SETTLE_BUSTYPE_HALL = "0405";
    /** 账务交易状态 正常*/
    public static final String AC_TXSTS_N = "N";
    /** 账务交易状态 冲正*/
    public static final String AC_TXSTS_C = "C";
    /** 账务交易状态 撤销*/
    public static final String AC_TXSTS_R = "R";
    /** 银行存款-备付金账户-XX银行  科目号 */
    public static final String AC_ITEMNO_BANK = "**********";
    /** 应付账款-待结算款-批量付款  科目号 */
    public static final String AC_ITEMNO_BATCH = "**********";
    /** 应付账款-待结算款-营业厅付款 科目号 */
    public static final String AC_ITEMNO_HALL = "**********";
    /** 手续费收入-支付账户-商户结算 科目号 */
    public static final String AC_ITEMNO_FEE = "**********";
    /** 其他应付款-支付账户-商户结算账户 科目号 */
    public static final String AC_ITEMNO_M_SETTLE = "**********";
    /** 账户类型枚举值 现金账户 */ 
    public static final String ACM_TYPE_CASH = "1";
    /** 账户类型枚举值 待结算账户 */ 
    public static final String ACM_TYPE_SETTLE = "8";
    /** 对账状态举值 初始状态 */
    public static final String CHECK_FILE_INIT = "0";
    /** 对账状态举值 获取文件 */
    public static final String CHECK_FILE_GET = "1";
    /** 对账状态举值 获取文件 */
    public static final String CHECK_FILE_DATABASE = "2";
    /** 对账状态举值 对账处理 */
    public static final String CHECK_FILE_PROCESS = "3";
    /** 对账状态举值 差错处理 */
    public static final String CHECK_ERROR_PROCESS = "4";
    /** 对账状态举值 对账结束 */
    public static final String CHECK_FILE_OVER = "9";
    /** 资金能力对账类型 成功 */
    public static final String CHECK_SUBTYPE_SUCCESS = "0701";
    /** 资金能力对账类型 失败 */
    public static final String CHECK_SUBTYPE_FAILURE = "0702";
    /** 计费方式枚举值 百分比 */
    public static final String FEE_CALCULATE_PERCENT = "percent";
    /** 计费方式枚举值 固定金额 */
    public static final String FEE_CALCULATE_FIXED = "fixed";
    /** 加解密类型枚举值  加密*/
    public static final String ENCRYPT = "encrypt";
    /** 加解密类型枚举值  解密*/
    public static final String DECRYPT = "decrypt";

    //
    public static final String TEMPLEID_SUCCESS = "00000014";
    public static final String TEMPLEID_FAIL = "0000001";

    /**
     *  商户结算操作权限
     */
    public static final String CPRITFAUTH  = "settle";

}
