<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csm.dao.ISettleOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.csm.entity.SettleOrderDO" >
        <id column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="trade_date" property="tradeDate" jdbcType="DATE" />
        <result column="trade_time" property="tradeTime" jdbcType="TIME" />
        <result column="agreement_pay_date" property="agreementPayDate" jdbcType="DATE" />
        <result column="trade_amt" property="tradeAmt" jdbcType="DECIMAL" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="CHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="mbl_no" property="mblNo" jdbcType="VARCHAR" />
        <result column="cap_corg_nm" property="capCorgNm" jdbcType="VARCHAR" />
        <result column="cap_corg_snm" property="capCorgSnm" jdbcType="VARCHAR" />
        <result column="cap_corg_no" property="capCorgNo" jdbcType="VARCHAR" />
        <result column="cap_card_no" property="capCardNo" jdbcType="VARCHAR" />
        <result column="cap_card_nm" property="capCardNm" jdbcType="VARCHAR" />
        <result column="bnk_mbl_no" property="bnkMblNo" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="payment_date" property="paymentDate" jdbcType="DATE" />
        <result column="payment_time" property="paymentTime" jdbcType="TIME" />
        <result column="last_last_settle_day" property="lastLastSettleDay" jdbcType="DATE" />
        <result column="last_settle_day" property="lastSettleDay" jdbcType="DATE" />
        <result column="message" property="message" jdbcType="VARCHAR" />
        <result column="bus_order_no" property="busOrderNo" jdbcType="VARCHAR" />
        <result column="bus_order_time" property="busOrderTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>
    
    <resultMap id="TotalInfoResultMap" type="com.hisun.lemon.csm.entity.FileTotalDO" >
        <result column="total_cnt" property="totalCnt" jdbcType="INTEGER" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        order_no, trade_date, trade_time, trade_amt, agreement_pay_date, fee, ccy, bus_type, user_id, user_name, mbl_no, 
        cap_corg_nm, cap_corg_snm, cap_corg_no, cap_card_no, cap_card_nm, bnk_mbl_no, stats, payment_date, 
        payment_time, last_last_settle_day, last_settle_day, message, bus_order_no, bus_order_time, 
        create_time, modify_time, tm_smp, login_name
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from csm_settle_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>
    
    <select id="countTotalInfo" resultMap="TotalInfoResultMap" >
        select count(1) total_cnt, ifnull(sum(trade_amt)-sum(fee), 0) total_amt
        from csm_settle_order
        <where>
            <if test="checkDate != null" >
                payment_date = #{checkDate,jdbcType=DATE}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
        </where>
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_settle_order
        <where>
            <if test="tradeDate != null" >
                and trade_date = #{tradeDate,jdbcType=DATE}
            </if>
            <if test="tradeTime != null" >
                and trade_time = #{tradeTime,jdbcType=TIME}
            </if>
            <if test="agreementPayDate != null" >
                and agreement_pay_date = #{agreementPayDate,jdbcType=DATE}
            </if>
            <if test="tradeAmt != null" >
                and trade_amt = #{tradeAmt,jdbcType=DECIMAL}
            </if>
            <if test="fee != null" >
                and fee = #{fee,jdbcType=DECIMAL}
            </if>
            <if test="ccy != null" >
                and ccy = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=CHAR}
            </if>
            <if test="userId != null" >
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="mblNo != null" >
                and mbl_no = #{mblNo,jdbcType=VARCHAR}
            </if>
            <if test="capCorgNm != null" >
                and cap_corg_nm = #{capCorgNm,jdbcType=VARCHAR}
            </if>
            <if test="capCorgSnm != null" >
                and cap_corg_snm = #{capCorgSnm,jdbcType=VARCHAR}
            </if>
            <if test="capCorgNo != null" >
                and cap_corg_no = #{capCorgNo,jdbcType=VARCHAR}
            </if>
            <if test="capCardNo != null" >
                and cap_card_no = #{capCardNo,jdbcType=VARCHAR}
            </if>
            <if test="capCardNm != null" >
                and cap_card_nm = #{capCardNm,jdbcType=VARCHAR}
            </if>
            <if test="bnkMblNo != null" >
                and bnk_mbl_no = #{bnkMblNo,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR} and stats != '0'
            </if>
            <if test="paymentDate != null" >
                and payment_date = #{paymentDate,jdbcType=DATE}
            </if>
            <if test="paymentTime != null" >
                and payment_time = #{paymentTime,jdbcType=TIME}
            </if>
            <if test="lastLastSettleDay != null" >
                and last_last_settle_day = #{lastLastSettleDay,jdbcType=DATE}
            </if>
            <if test="lastSettleDay != null" >
                and last_settle_day = #{lastSettleDay,jdbcType=DATE}
            </if>
            <if test="message != null" >
                and message = #{message,jdbcType=VARCHAR}
            </if>
            <if test="busOrderNo != null" >
                and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="busOrderTime != null" >
                and bus_order_time = #{busOrderTime,jdbcType=TIMESTAMP}
            </if>
            <if test="beginDate != null and endDate != null" >
                and trade_date between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_settle_order
        <where>
            <if test="tradeDate != null" >
                and trade_date = #{tradeDate,jdbcType=DATE}
            </if>
            <if test="tradeTime != null" >
                and trade_time = #{tradeTime,jdbcType=TIME}
            </if>
            <if test="agreementPayDate != null" >
                and agreement_pay_date = #{agreementPayDate,jdbcType=DATE}
            </if>
            <if test="tradeAmt != null" >
                and trade_amt = #{tradeAmt,jdbcType=DECIMAL}
            </if>
            <if test="fee != null" >
                and fee = #{fee,jdbcType=DECIMAL}
            </if>
            <if test="ccy != null" >
                and ccy = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=CHAR}
            </if>
            <if test="userId != null" >
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="mblNo != null" >
                and mbl_no = #{mblNo,jdbcType=VARCHAR}
            </if>
            <if test="capCorgNm != null" >
                and cap_corg_nm = #{capCorgNm,jdbcType=VARCHAR}
            </if>
            <if test="capCorgSnm != null" >
                and cap_corg_snm = #{capCorgSnm,jdbcType=VARCHAR}
            </if>
            <if test="capCorgNo != null" >
                and cap_corg_no = #{capCorgNo,jdbcType=VARCHAR}
            </if>
            <if test="capCardNo != null" >
                and cap_card_no = #{capCardNo,jdbcType=VARCHAR}
            </if>
            <if test="capCardNm != null" >
                and cap_card_nm = #{capCardNm,jdbcType=VARCHAR}
            </if>
            <if test="bnkMblNo != null" >
                and bnk_mbl_no = #{bnkMblNo,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR} and stats != '0'
            </if>
            <if test="paymentDate != null" >
                and payment_date = #{paymentDate,jdbcType=DATE}
            </if>
            <if test="paymentTime != null" >
                and payment_time = #{paymentTime,jdbcType=TIME}
            </if>
            <if test="lastLastSettleDay != null" >
                and last_last_settle_day = #{lastLastSettleDay,jdbcType=DATE}
            </if>
            <if test="lastSettleDay != null" >
                and last_settle_day = #{lastSettleDay,jdbcType=DATE}
            </if>
            <if test="message != null" >
                and message = #{message,jdbcType=VARCHAR}
            </if>
            <if test="busOrderNo != null" >
                and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="busOrderTime != null" >
                and bus_order_time = #{busOrderTime,jdbcType=TIMESTAMP}
            </if>
            <if test="beginDate != null and endDate != null" >
                and trade_date between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csm_settle_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csm.entity.SettleOrderDO" >
        insert into csm_settle_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="tradeDate != null" >
                trade_date,
            </if>
            <if test="tradeTime != null" >
                trade_time,
            </if>
            <if test="agreementPayDate != null" >
                agreement_pay_date,
            </if>
            <if test="tradeAmt != null" >
                trade_amt,
            </if>
            <if test="fee != null" >
                fee,
            </if>
            <if test="ccy != null" >
                ccy,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="loginName != null" >
                login_name,
            </if>
            <if test="userName != null" >
                user_name,
            </if>
            <if test="mblNo != null" >
                mbl_no,
            </if>
            <if test="capCorgNm != null" >
                cap_corg_nm,
            </if>
            <if test="capCorgSnm != null" >
                cap_corg_snm,
            </if>
            <if test="capCorgNo != null" >
                cap_corg_no,
            </if>
            <if test="capCardNo != null" >
                cap_card_no,
            </if>
            <if test="capCardNm != null" >
                cap_card_nm,
            </if>
            <if test="bnkMblNo != null" >
                bnk_mbl_no,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="paymentDate != null" >
                payment_date,
            </if>
            <if test="paymentTime != null" >
                payment_time,
            </if>
            <if test="lastLastSettleDay != null" >
                last_last_settle_day,
            </if>
            <if test="lastSettleDay != null" >
                last_settle_day,
            </if>
            <if test="message != null" >
                message,
            </if>
            <if test="busOrderNo != null" >
                bus_order_no,
            </if>
            <if test="busOrderTime != null" >
                bus_order_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeDate != null" >
                #{tradeDate,jdbcType=DATE},
            </if>
            <if test="tradeTime != null" >
                #{tradeTime,jdbcType=TIME},
            </if>
            <if test="agreementPayDate != null" >
                #{agreementPayDate,jdbcType=DATE},
            </if>
            <if test="tradeAmt != null" >
                #{tradeAmt,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=CHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="loginName != null" >
                #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="capCorgNm != null" >
                #{capCorgNm,jdbcType=VARCHAR},
            </if>
            <if test="capCorgSnm != null" >
                #{capCorgSnm,jdbcType=VARCHAR},
            </if>
            <if test="capCorgNo != null" >
                #{capCorgNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardNo != null" >
                #{capCardNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardNm != null" >
                #{capCardNm,jdbcType=VARCHAR},
            </if>
            <if test="bnkMblNo != null" >
                #{bnkMblNo,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="paymentDate != null" >
                #{paymentDate,jdbcType=DATE},
            </if>
            <if test="paymentTime != null" >
                #{paymentTime,jdbcType=TIME},
            </if>
            <if test="lastLastSettleDay != null" >
                #{lastLastSettleDay,jdbcType=DATE},
            </if>
            <if test="lastSettleDay != null" >
                #{lastSettleDay,jdbcType=DATE},
            </if>
            <if test="message != null" >
                #{message,jdbcType=VARCHAR},
            </if>
            <if test="busOrderNo != null" >
                #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="busOrderTime != null" >
                #{busOrderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csm.entity.SettleOrderDO" >
        update csm_settle_order
        <set >
            <if test="tradeDate != null" >
                trade_date = #{tradeDate,jdbcType=DATE},
            </if>
            <if test="tradeTime != null" >
                trade_time = #{tradeTime,jdbcType=TIME},
            </if>
            <if test="agreementPayDate != null" >
                agreement_pay_date = #{agreementPayDate,jdbcType=DATE},
            </if>
            <if test="tradeAmt != null" >
                trade_amt = #{tradeAmt,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="ccy != null" >
                ccy = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=CHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                mbl_no = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="capCorgNm != null" >
                cap_corg_nm = #{capCorgNm,jdbcType=VARCHAR},
            </if>
            <if test="capCorgSnm != null" >
                cap_corg_snm = #{capCorgSnm,jdbcType=VARCHAR},
            </if>
            <if test="capCorgNo != null" >
                cap_corg_no = #{capCorgNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardNo != null" >
                cap_card_no = #{capCardNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardNm != null" >
                cap_card_nm = #{capCardNm,jdbcType=VARCHAR},
            </if>
            <if test="bnkMblNo != null" >
                bnk_mbl_no = #{bnkMblNo,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="paymentDate != null" >
                payment_date = #{paymentDate,jdbcType=DATE},
            </if>
            <if test="paymentTime != null" >
                payment_time = #{paymentTime,jdbcType=TIME},
            </if>
            <if test="lastLastSettleDay != null" >
                last_last_settle_day = #{lastLastSettleDay,jdbcType=DATE},
            </if>
            <if test="lastSettleDay != null" >
                last_settle_day = #{lastSettleDay,jdbcType=DATE},
            </if>
            <if test="message != null" >
                message = #{message,jdbcType=VARCHAR},
            </if>
            <if test="busOrderNo != null" >
                bus_order_no = #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="busOrderTime != null" >
                bus_order_time = #{busOrderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>
    
    <update id="updateByCondition" >
        update csm_settle_order
        <set >
            <if test="valueDO.tradeDate != null" >
                trade_date = #{valueDO.tradeDate,jdbcType=DATE},
            </if>
            <if test="valueDO.tradeTime != null" >
                trade_time = #{valueDO.tradeTime,jdbcType=TIME},
            </if>
            <if test="valueDO.agreementPayDate != null" >
                agreement_pay_date = #{valueDO.agreementPayDate,jdbcType=DATE},
            </if>
            <if test="valueDO.tradeAmt != null" >
                trade_amt = #{valueDO.tradeAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.fee != null" >
                fee = #{valueDO.fee,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.ccy != null" >
                ccy = #{valueDO.ccy,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.busType != null" >
                bus_type = #{valueDO.busType,jdbcType=CHAR},
            </if>
            <if test="valueDO.userId != null" >
                user_id = #{valueDO.userId,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.userName != null" >
                user_name = #{valueDO.userName,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.mblNo != null" >
                mbl_no = #{valueDO.mblNo,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.capCorgNm != null" >
                cap_corg_nm = #{valueDO.capCorgNm,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.capCorgSnm != null" >
                cap_corg_snm = #{valueDO.capCorgSnm,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.capCorgNo != null" >
                cap_corg_no = #{valueDO.capCorgNo,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.capCardNo != null" >
                cap_card_no = #{valueDO.capCardNo,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.capCardNm != null" >
                cap_card_nm = #{valueDO.capCardNm,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.bnkMblNo != null" >
                bnk_mbl_no = #{valueDO.bnkMblNo,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.stats != null" >
                stats = #{valueDO.stats,jdbcType=CHAR},
            </if>
            <if test="valueDO.paymentDate != null" >
                payment_date = #{valueDO.paymentDate,jdbcType=DATE},
            </if>
            <if test="valueDO.paymentTime != null" >
                payment_time = #{valueDO.paymentTime,jdbcType=TIME},
            </if>
            <if test="valueDO.lastLastSettleDay != null" >
                last_last_settle_day = #{valueDO.lastLastSettleDay,jdbcType=DATE},
            </if>
            <if test="valueDO.lastSettleDay != null" >
                last_settle_day = #{valueDO.lastSettleDay,jdbcType=DATE},
            </if>
            <if test="valueDO.message != null" >
                message = #{valueDO.message,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.busOrderNo != null" >
                bus_order_no = #{valueDO.busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.busOrderTime != null" >
                bus_order_time = #{valueDO.busOrderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.modifyTime != null" >
                modify_time = #{valueDO.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <if test="conditionDO.orderNo != null" >
                order_no = #{conditionDO.orderNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.tradeDate != null" >
                and trade_date = #{conditionDO.tradeDate,jdbcType=DATE}
            </if>
            <if test="conditionDO.tradeTime != null" >
                and trade_time = #{conditionDO.tradeTime,jdbcType=TIME}
            </if>
            <if test="conditionDO.agreementPayDate != null" >
                and agreement_pay_date = #{conditionDO.agreementPayDate,jdbcType=DATE}
            </if>
            <if test="conditionDO.tradeAmt != null" >
                and trade_amt = #{conditionDO.tradeAmt,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.fee != null" >
                and fee = #{conditionDO.fee,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.ccy != null" >
                and ccy = #{conditionDO.ccy,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.busType != null" >
                and bus_type = #{conditionDO.busType,jdbcType=CHAR}
            </if>
            <if test="conditionDO.userId != null" >
                and user_id = #{conditionDO.userId,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.userName != null" >
                and user_name = #{conditionDO.userName,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.mblNo != null" >
                and mbl_no = #{conditionDO.mblNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.capCorgNm != null" >
                and cap_corg_nm = #{conditionDO.capCorgNm,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.capCorgSnm != null" >
                and cap_corg_snm = #{conditionDO.capCorgSnm,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.capCorgNo != null" >
                and cap_corg_no = #{conditionDO.capCorgNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.capCardNo != null" >
                and cap_card_no = #{conditionDO.capCardNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.capCardNm != null" >
                and cap_card_nm = #{conditionDO.capCardNm,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.bnkMblNo != null" >
                and bnk_mbl_no = #{conditionDO.bnkMblNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.stats != null" >
                and stats = #{conditionDO.stats,jdbcType=CHAR}
            </if>
            <if test="conditionDO.paymentDate != null" >
                and payment_date = #{conditionDO.paymentDate,jdbcType=DATE}
            </if>
            <if test="conditionDO.paymentTime != null" >
                and payment_time = #{conditionDO.paymentTime,jdbcType=TIME}
            </if>
            <if test="conditionDO.lastLastSettleDay != null" >
                and last_last_settle_day = #{conditionDO.lastLastSettleDay,jdbcType=DATE}
            </if>
            <if test="conditionDO.lastSettleDay != null" >
                and last_settle_day = #{conditionDO.lastSettleDay,jdbcType=DATE}
            </if>
            <if test="conditionDO.message != null" >
                and message = #{conditionDO.message,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.busOrderNo != null" >
                and bus_order_no = #{conditionDO.busOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.busOrderTime != null" >
                and bus_order_time = #{conditionDO.busOrderTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </update>

    <select id="querySettleList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csm_settle_order
        <where>
            <if test="acTm != null">
                <![CDATA[and trade_date = #{acTm}]]>
            </if>
            <if test="busType != null">
                and bus_type = #{busType}
            </if>

            <if test="statusList != null">
                and stats in
                <foreach item="item" index="index" collection="statusList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>