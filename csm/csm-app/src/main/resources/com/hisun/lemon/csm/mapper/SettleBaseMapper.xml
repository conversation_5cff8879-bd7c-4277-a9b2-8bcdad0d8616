<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csm.dao.ISettleBaseDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.csm.entity.SettleBaseDO" >
        <id column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="mbl_no" property="mblNo" jdbcType="VARCHAR" />
        <result column="settle_type" property="settleType" jdbcType="VARCHAR" />
        <result column="settle_cycle_type" property="settleCycleType" jdbcType="VARCHAR" />
        <result column="agreement_pay_days" property="agreementPayDays" jdbcType="VARCHAR" />
        <result column="settle_sites" property="settleSites" jdbcType="VARCHAR" />
        <result column="settle_sites_name" property="settleSitesName" jdbcType="VARCHAR" />
        <result column="hall_sites" property="hallSites" jdbcType="VARCHAR" />
        <result column="hall_sites_name" property="hallSitesName" jdbcType="VARCHAR" />
        <result column="last_last_settle_day" property="lastLastSettleDay" jdbcType="DATE" />
        <result column="last_settle_day" property="lastSettleDay" jdbcType="DATE" />
        <result column="next_settle_day" property="nextSettleDay" jdbcType="DATE" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        user_id, user_name, mbl_no, settle_type, settle_cycle_type, agreement_pay_days, settle_sites, settle_sites_name,
        last_last_settle_day, last_settle_day, next_settle_day, stats, eff_date, exp_date, opr_id, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from csm_settle_base
        where user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <select id="vaildSettle" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from csm_settle_base
        where user_id = #{userId,jdbcType=VARCHAR} <![CDATA[   and eff_date <= curdate() and  curdate() <= exp_date ]]>
    </select>

    <select id="countByCondition" resultType="int" >
        select count(1)
        from csm_settle_base
        <where>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="mblNo != null" >
                and mbl_no = #{mblNo,jdbcType=VARCHAR}
            </if>
            <if test="settleType != null" >
                and settle_type = #{settleType,jdbcType=VARCHAR}
            </if>
            <if test="settleCycleType != null" >
                and settle_cycle_type = #{settleCycleType,jdbcType=VARCHAR}
            </if>
            <if test="agreementPayDays != null" >
                and agreement_pay_days = #{agreementPayDays,jdbcType=VARCHAR}
            </if>
            <if test="settleSites != null" >
                and settle_sites = #{settleSites,jdbcType=VARCHAR}
            </if>
            <if test="settleSitesName != null" >
                and settle_sites_name = #{settleSitesName,jdbcType=VARCHAR}
            </if>
            <if test="hallSites != null" >
                and hall_sites = #{hallSites,jdbcType=VARCHAR}
            </if>
            <if test="hallSitesName != null" >
                and hall_sites_name = #{hallSitesName,jdbcType=VARCHAR}
            </if>
            <if test="lastLastSettleDay != null" >
                and last_last_settle_day = #{lastLastSettleDay,jdbcType=DATE}
            </if>
            <if test="lastSettleDay != null" >
                and last_settle_day = #{lastSettleDay,jdbcType=DATE}
            </if>
            <if test="nextSettleDay != null" >
                and next_settle_day = #{nextSettleDay,jdbcType=DATE}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_settle_base
        <where>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="mblNo != null" >
                and mbl_no = #{mblNo,jdbcType=VARCHAR}
            </if>
            <if test="settleType != null" >
                and settle_type = #{settleType,jdbcType=VARCHAR}
            </if>
            <if test="settleCycleType != null" >
                and settle_cycle_type = #{settleCycleType,jdbcType=VARCHAR}
            </if>
            <if test="agreementPayDays != null" >
                and agreement_pay_days = #{agreementPayDays,jdbcType=VARCHAR}
            </if>
            <if test="settleSites != null" >
                and settle_sites = #{settleSites,jdbcType=VARCHAR}
            </if>
            <if test="settleSitesName != null" >
                and settle_sites_name = #{settleSitesName,jdbcType=VARCHAR}
            </if>
            <if test="hallSites != null" >
                and hall_sites = #{hallSites,jdbcType=VARCHAR}
            </if>
            <if test="hallSitesName != null" >
                and hall_sites_name = #{hallSitesName,jdbcType=VARCHAR}
            </if>
            <if test="lastLastSettleDay != null" >
                and last_last_settle_day = #{lastLastSettleDay,jdbcType=DATE}
            </if>
            <if test="lastSettleDay != null" >
                and last_settle_day = #{lastSettleDay,jdbcType=DATE}
            </if>
            <if test="nextSettleDay != null" >
                and next_settle_day = #{nextSettleDay,jdbcType=DATE}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="getValidSettleListByCondition" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from csm_settle_base
        <where>
            <![CDATA[  eff_date <= now() and  now() <= exp_date ]]>
            <if test="userId != null" >
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="mblNo != null" >
                and mbl_no = #{mblNo,jdbcType=VARCHAR}
            </if>
            <if test="settleType != null" >
                and settle_type = #{settleType,jdbcType=VARCHAR}
            </if>
            <if test="settleCycleType != null" >
                and settle_cycle_type = #{settleCycleType,jdbcType=VARCHAR}
            </if>
            <if test="agreementPayDays != null" >
                and agreement_pay_days = #{agreementPayDays,jdbcType=VARCHAR}
            </if>
            <if test="settleSites != null" >
                and settle_sites = #{settleSites,jdbcType=VARCHAR}
            </if>
            <if test="settleSitesName != null" >
                and settle_sites_name = #{settleSitesName,jdbcType=VARCHAR}
            </if>
            <if test="hallSites != null" >
                and hall_sites = #{hallSites,jdbcType=VARCHAR}
            </if>
            <if test="hallSitesName != null" >
                and hall_sites_name = #{hallSitesName,jdbcType=VARCHAR}
            </if>
            <if test="lastLastSettleDay != null" >
                and last_last_settle_day = #{lastLastSettleDay,jdbcType=DATE}
            </if>
            <if test="lastSettleDay != null" >
                and last_settle_day = #{lastSettleDay,jdbcType=DATE}
            </if>
            <if test="nextSettleDay != null" >
                and next_settle_day = #{nextSettleDay,jdbcType=DATE}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csm_settle_base
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csm.entity.SettleBaseDO" >
        insert into csm_settle_base
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                user_id,
            </if>
            <if test="userName != null" >
                user_name,
            </if>
            <if test="mblNo != null" >
                mbl_no,
            </if>
            <if test="settleType != null" >
                settle_type,
            </if>
            <if test="settleCycleType != null" >
                settle_cycle_type,
            </if>
            <if test="agreementPayDays != null" >
                agreement_pay_days,
            </if>
            <if test="settleSites != null" >
                settle_sites,
            </if>
            <if test="settleSitesName != null" >
                settle_sites_name,
            </if>
            <if test="hallSites != null" >
                hall_sites,
            </if>
            <if test="hallSitesName != null" >
                hall_sites_name,
            </if>
            <if test="lastLastSettleDay != null" >
                last_last_settle_day,
            </if>
            <if test="lastSettleDay != null" >
                last_settle_day,
            </if>
            <if test="nextSettleDay != null" >
                next_settle_day,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="settleType != null" >
                #{settleType,jdbcType=VARCHAR},
            </if>
            <if test="settleCycleType != null" >
                #{settleCycleType,jdbcType=VARCHAR},
            </if>
            <if test="agreementPayDays != null" >
                #{agreementPayDays,jdbcType=VARCHAR},
            </if>
            <if test="settleSites != null" >
                #{settleSites,jdbcType=VARCHAR},
            </if>
            <if test="settleSitesName != null" >
                #{settleSitesName,jdbcType=VARCHAR},
            </if>
            <if test="hallSites != null" >
                #{hallSites,jdbcType=VARCHAR},
            </if>
            <if test="hallSitesName != null" >
                #{hallSitesName,jdbcType=VARCHAR},
            </if>
            <if test="lastLastSettleDay != null" >
                #{lastLastSettleDay,jdbcType=DATE},
            </if>
            <if test="lastSettleDay != null" >
                #{lastSettleDay,jdbcType=DATE},
            </if>
            <if test="nextSettleDay != null" >
                #{nextSettleDay,jdbcType=DATE},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csm.entity.SettleBaseDO" >
        update csm_settle_base
        <set >
            <if test="userName != null" >
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                mbl_no = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="settleType != null" >
                settle_type = #{settleType,jdbcType=VARCHAR},
            </if>
            <if test="settleCycleType != null" >
                settle_cycle_type = #{settleCycleType,jdbcType=VARCHAR},
            </if>
            <if test="agreementPayDays != null" >
                agreement_pay_days = #{agreementPayDays,jdbcType=VARCHAR},
            </if>
            <if test="settleSites != null" >
                settle_sites = #{settleSites,jdbcType=VARCHAR},
            </if>
            <if test="settleSitesName != null" >
                settle_sites_name = #{settleSitesName,jdbcType=VARCHAR},
            </if>
            <if test="hallSites != null" >
                hall_sites = #{hallSites,jdbcType=VARCHAR},
            </if>
            <if test="hallSitesName != null" >
                hall_sites_name = #{hallSitesName,jdbcType=VARCHAR},
            </if>
            <if test="lastLastSettleDay != null" >
                last_last_settle_day = #{lastLastSettleDay,jdbcType=DATE},
            </if>
            <if test="lastSettleDay != null" >
                last_settle_day = #{lastSettleDay,jdbcType=DATE},
            </if>
            <if test="nextSettleDay != null" >
                next_settle_day = #{nextSettleDay,jdbcType=DATE},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>
</mapper>