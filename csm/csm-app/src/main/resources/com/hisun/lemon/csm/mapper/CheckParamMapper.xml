<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csm.dao.ICheckParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.csm.entity.CheckParamDO" >
        <id column="check_type_id" property="checkTypeId" jdbcType="VARCHAR" />
        <result column="import_table" property="importTable" jdbcType="VARCHAR" />
        <result column="import_format" property="importFormat" jdbcType="VARCHAR" />
        <result column="import_file_path" property="importFilePath" jdbcType="VARCHAR" />
        <result column="export_table" property="exportTable" jdbcType="VARCHAR" />
        <result column="export_format" property="exportFormat" jdbcType="VARCHAR" />
        <result column="export_file_path" property="exportFilePath" jdbcType="VARCHAR" />
        <result column="auto_flag" property="autoFlag" jdbcType="CHAR" />
        <result column="encrypt_flag" property="encryptFlag" jdbcType="CHAR" />
        <result column="encrypt_component" property="encryptComponent" jdbcType="VARCHAR" />
        <result column="descrypt_flag" property="descryptFlag" jdbcType="CHAR" />
        <result column="descrypt_component" property="descryptComponent" jdbcType="VARCHAR" />
        <result column="check_process_component" property="checkProcessComponent" jdbcType="VARCHAR" />
        <result column="check_process_clazz" property="checkProcessClazz" jdbcType="VARCHAR" />
        <result column="error_process_component" property="errorProcessComponent" jdbcType="VARCHAR" />
        <result column="error_process_clazz" property="errorProcessClazz" jdbcType="VARCHAR" />
        <result column="multiple_check_flg" property="multipleCheckFlg" jdbcType="VARCHAR" />
        <result column="sftp_sys_channel" property="sftpSysChannel" jdbcType="VARCHAR" />
        <result column="sftp_bus_channel" property="sftpBusChannel" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        check_type_id, import_table, import_format, import_file_path, export_table, export_format, 
        export_file_path, auto_flag, encrypt_flag, encrypt_component, descrypt_flag, descrypt_component, check_process_component, error_process_component, 
        check_process_clazz, error_process_clazz, multiple_check_flg, sftp_sys_channel, sftp_bus_channel, stats, eff_date, exp_date, opr_id, create_time, 
        modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from csm_check_param
        where check_type_id = #{checkTypeId,jdbcType=VARCHAR}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_check_param
        <where>
            <if test="checkTypeId != null" >
                check_type_id = #{checkTypeId,jdbcType=VARCHAR}
            </if>
            <if test="importTable != null" >
                and import_table = #{importTable,jdbcType=VARCHAR}
            </if>
            <if test="importFormat != null" >
                and import_format = #{importFormat,jdbcType=VARCHAR}
            </if>
            <if test="importFilePath != null" >
                and import_file_path = #{importFilePath,jdbcType=VARCHAR}
            </if>
            <if test="exportTable != null" >
                and export_table = #{exportTable,jdbcType=VARCHAR}
            </if>
            <if test="exportFormat != null" >
                and export_format = #{exportFormat,jdbcType=VARCHAR}
            </if>
            <if test="exportFilePath != null" >
                and export_file_path = #{exportFilePath,jdbcType=VARCHAR}
            </if>
            <if test="autoFlag != null" >
                and auto_flag = #{autoFlag,jdbcType=CHAR}
            </if>
            <if test="encryptFlag != null" >
                and encrypt_flag = #{encryptFlag,jdbcType=CHAR}
            </if>
            <if test="encryptComponent != null" >
                and encrypt_component = #{encryptComponent,jdbcType=VARCHAR}
            </if>
            <if test="descryptFlag != null" >
                and descrypt_flag = #{descryptFlag,jdbcType=CHAR}
            </if>
            <if test="descryptComponent != null" >
                and descrypt_component = #{descryptComponent,jdbcType=VARCHAR}
            </if>
            <if test="checkProcessComponent != null" >
                and check_process_component = #{checkProcessComponent,jdbcType=VARCHAR}
            </if>
            <if test="checkProcessClazz != null" >
                and check_process_clazz = #{checkProcessClazz,jdbcType=VARCHAR}
            </if>
            <if test="errorProcessComponent != null" >
                and error_process_component = #{errorProcessComponent,jdbcType=VARCHAR}
            </if>
            <if test="errorProcessClazz != null" >
                and error_process_clazz = #{errorProcessClazz,jdbcType=VARCHAR}
            </if>
            <if test="multipleCheckFlg != null" >
                and multiple_check_flg = #{multipleCheckFlg,jdbcType=VARCHAR}
            </if>
            <if test="sftpSysChannel != null" >
                and sftp_sys_channel = #{sftpSysChannel,jdbcType=VARCHAR}
            </if>
            <if test="sftpBusChannel != null" >
                and sftp_bus_channel = #{sftpBusChannel,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_check_param
        <where>
            <if test="checkTypeId != null" >
                check_type_id = #{checkTypeId,jdbcType=VARCHAR}
            </if>
            <if test="importTable != null" >
                and import_table = #{importTable,jdbcType=VARCHAR}
            </if>
            <if test="importFormat != null" >
                and import_format = #{importFormat,jdbcType=VARCHAR}
            </if>
            <if test="importFilePath != null" >
                and import_file_path = #{importFilePath,jdbcType=VARCHAR}
            </if>
            <if test="exportTable != null" >
                and export_table = #{exportTable,jdbcType=VARCHAR}
            </if>
            <if test="exportFormat != null" >
                and export_format = #{exportFormat,jdbcType=VARCHAR}
            </if>
            <if test="exportFilePath != null" >
                and export_file_path = #{exportFilePath,jdbcType=VARCHAR}
            </if>
            <if test="autoFlag != null" >
                and auto_flag = #{autoFlag,jdbcType=CHAR}
            </if>
            <if test="encryptFlag != null" >
                and encrypt_flag = #{encryptFlag,jdbcType=CHAR}
            </if>
            <if test="encryptComponent != null" >
                and encrypt_component = #{encryptComponent,jdbcType=VARCHAR}
            </if>
            <if test="descryptFlag != null" >
                and descrypt_flag = #{descryptFlag,jdbcType=CHAR}
            </if>
            <if test="descryptComponent != null" >
                and descrypt_component = #{descryptComponent,jdbcType=VARCHAR}
            </if>
            <if test="checkProcessComponent != null" >
                and check_process_component = #{checkProcessComponent,jdbcType=VARCHAR}
            </if>
            <if test="checkProcessClazz != null" >
                and check_process_clazz = #{checkProcessClazz,jdbcType=VARCHAR}
            </if>
            <if test="errorProcessComponent != null" >
                and error_process_component = #{errorProcessComponent,jdbcType=VARCHAR}
            </if>
            <if test="errorProcessClazz != null" >
                and error_process_clazz = #{error_process_clazz,jdbcType=VARCHAR}
            </if>
            <if test="multipleCheckFlg != null" >
                and multiple_check_flg = #{multipleCheckFlg,jdbcType=VARCHAR}
            </if>
            <if test="sftpSysChannel != null" >
                and sftp_sys_channel = #{sftpSysChannel,jdbcType=VARCHAR}
            </if>
            <if test="sftpBusChannel != null" >
                and sftp_bus_channel = #{sftpBusChannel,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csm_check_param
        where check_type_id = #{checkTypeId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csm.entity.CheckParamDO" >
        insert into csm_check_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="checkTypeId != null" >
                check_type_id,
            </if>
            <if test="importTable != null" >
                import_table,
            </if>
            <if test="importFormat != null" >
                import_format,
            </if>
            <if test="importFilePath != null" >
                import_file_path,
            </if>
            <if test="exportTable != null" >
                export_table,
            </if>
            <if test="exportFormat != null" >
                export_format,
            </if>
            <if test="exportFilePath != null" >
                export_file_path,
            </if>
            <if test="autoFlag != null" >
                auto_flag,
            </if>
            <if test="encryptFlag != null" >
                encrypt_flag,
            </if>
            <if test="encryptComponent != null" >
                encrypt_component,
            </if>
            <if test="descryptFlag != null" >
                descrypt_flag,
            </if>
            <if test="descryptComponent != null" >
                descrypt_component,
            </if>
            <if test="checkProcessComponent != null" >
                check_process_component,
            </if>
            <if test="checkProcessClazz != null" >
                check_process_clazz,
            </if>
            <if test="errorProcessComponent != null" >
                error_process_component,
            </if>
            <if test="errorProcessClazz != null" >
                error_process_clazz,
            </if>
            <if test="multipleCheckFlg != null" >
                multiple_check_flg,
            </if>
            <if test="sftpSysChannel != null" >
                sftp_sys_channel,
            </if>
            <if test="sftpBusChannel != null" >
                sftp_bus_channel,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="checkTypeId != null" >
                #{checkTypeId,jdbcType=VARCHAR},
            </if>
            <if test="importTable != null" >
                #{importTable,jdbcType=VARCHAR},
            </if>
            <if test="importFormat != null" >
                #{importFormat,jdbcType=VARCHAR},
            </if>
            <if test="importFilePath != null" >
                #{importFilePath,jdbcType=VARCHAR},
            </if>
            <if test="exportTable != null" >
                #{exportTable,jdbcType=VARCHAR},
            </if>
            <if test="exportFormat != null" >
                #{exportFormat,jdbcType=VARCHAR},
            </if>
            <if test="exportFilePath != null" >
                #{exportFilePath,jdbcType=VARCHAR},
            </if>
            <if test="autoFlag != null" >
                #{autoFlag,jdbcType=CHAR},
            </if>
            <if test="encryptFlag != null" >
                #{encryptFlag,jdbcType=CHAR},
            </if>
            <if test="encryptComponent != null" >
                #{encryptComponent,jdbcType=VARCHAR},
            </if>
            <if test="descryptFlag != null" >
                #{descryptFlag,jdbcType=CHAR},
            </if>
            <if test="descryptComponent != null" >
                #{descryptComponent,jdbcType=VARCHAR},
            </if>
            <if test="checkProcessComponent != null" >
                #{checkProcessComponent,jdbcType=VARCHAR},
            </if>
            <if test="checkProcessClazz != null" >
                #{checkProcessClazz,jdbcType=VARCHAR},
            </if>
            <if test="errorProcessComponent != null" >
                #{errorProcessComponent,jdbcType=VARCHAR},
            </if>
            <if test="errorProcessClazz != null" >
                #{errorProcessClazz,jdbcType=VARCHAR},
            </if>
            <if test="multipleCheckFlg != null" >
                #{multipleCheckFlg,jdbcType=VARCHAR},
            </if>
            <if test="sftpSysChannel != null" >
                #{sftpSysChannel,jdbcType=VARCHAR},
            </if>
            <if test="sftpBusChannel != null" >
                #{sftpBusChannel,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csm.entity.CheckParamDO" >
        update csm_check_param
        <set >
            <if test="importTable != null" >
                import_table = #{importTable,jdbcType=VARCHAR},
            </if>
            <if test="importFormat != null" >
                import_format = #{importFormat,jdbcType=VARCHAR},
            </if>
            <if test="importFilePath != null" >
                import_file_path = #{importFilePath,jdbcType=VARCHAR},
            </if>
            <if test="exportTable != null" >
                export_table = #{exportTable,jdbcType=VARCHAR},
            </if>
            <if test="exportFormat != null" >
                export_format = #{exportFormat,jdbcType=VARCHAR},
            </if>
            <if test="exportFilePath != null" >
                export_file_path = #{exportFilePath,jdbcType=VARCHAR},
            </if>
            <if test="autoFlag != null" >
                auto_flag = #{autoFlag,jdbcType=CHAR},
            </if>
            <if test="encryptFlag != null" >
                encrypt_flag = #{encryptFlag,jdbcType=CHAR},
            </if>
            <if test="encryptComponent != null" >
                encrypt_component = #{encryptComponent,jdbcType=VARCHAR},
            </if>
            <if test="descryptFlag != null" >
                descrypt_flag = #{descryptFlag,jdbcType=CHAR},
            </if>
            <if test="descryptComponent != null" >
                descrypt_component = #{descryptComponent,jdbcType=VARCHAR},
            </if>
            <if test="checkProcessComponent != null" >
                check_process_component = #{checkProcessComponent,jdbcType=VARCHAR},
            </if>
            <if test="checkProcessClazz != null" >
                check_process_clazz = #{checkProcessClazz,jdbcType=VARCHAR},
            </if>
            <if test="errorProcessComponent != null" >
                error_process_component = #{errorProcessComponent,jdbcType=VARCHAR},
            </if>
            <if test="errorProcessClazz != null" >
                error_process_clazz = #{errorProcessClazz,jdbcType=VARCHAR},
            </if>
            <if test="multipleCheckFlg != null" >
                multiple_check_flg = #{multipleCheckFlg,jdbcType=VARCHAR},
            </if>
            <if test="sftpSysChannel != null" >
                sftp_sys_channel = #{sftpSysChannel,jdbcType=VARCHAR},
            </if>
            <if test="sftpBusChannel != null" >
                sftp_bus_channel = #{sftpBusChannel,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where check_type_id = #{checkTypeId,jdbcType=VARCHAR}
    </update>
</mapper>