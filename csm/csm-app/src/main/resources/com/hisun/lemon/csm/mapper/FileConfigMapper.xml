<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csm.dao.IFileConfigDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.csm.entity.FileConfigDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="sftp_sys_channel" property="sftpSysChannel" jdbcType="VARCHAR" />
        <result column="sftp_bus_channel" property="sftpBusChannel" jdbcType="VARCHAR" />
        <result column="remote_name" property="remoteName" jdbcType="VARCHAR" />
        <result column="remote_password" property="remotePassword" jdbcType="VARCHAR" />
        <result column="remote_ip" property="remoteIp" jdbcType="VARCHAR" />
        <result column="remote_port" property="remotePort" jdbcType="INTEGER" />
        <result column="connect_timeout" property="connectTimeout" jdbcType="INTEGER" />
        <result column="remote_file_path" property="remoteFilePath" jdbcType="VARCHAR" />
        <result column="remote_file_name" property="remoteFileName" jdbcType="VARCHAR" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, sftp_sys_channel, sftp_bus_channel, remote_name, remote_password, remote_ip, 
        remote_port, connect_timeout, remote_file_path, remote_file_name, opr_id, create_time, 
        modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from csm_file_config
        where id = #{id,jdbcType=VARCHAR}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_file_config
        <where>
            <if test="sftpSysChannel != null" >
                sftp_sys_channel = #{sftpSysChannel,jdbcType=VARCHAR}
            </if>
            <if test="sftpBusChannel != null" >
                and sftp_bus_channel = #{sftpBusChannel,jdbcType=VARCHAR}
            </if>
            <if test="remoteName != null" >
                and remote_name = #{remoteName,jdbcType=VARCHAR}
            </if>
            <if test="remotePassword != null" >
                and remote_password = #{remotePassword,jdbcType=VARCHAR}
            </if>
            <if test="remoteIp != null" >
                and remote_ip = #{remoteIp,jdbcType=VARCHAR}
            </if>
            <if test="connectTimeout != null" >
                and connect_timeout = #{connectTimeout,jdbcType=INTEGER}
            </if>
            <if test="remoteFilePath != null" >
                and remote_file_path = #{remoteFilePath,jdbcType=VARCHAR}
            </if>
            <if test="remoteFileName != null" >
                and remote_file_name = #{remoteFileName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csm_file_config
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csm.entity.FileConfigDO" >
        insert into csm_file_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="sftpSysChannel != null" >
                sftp_sys_channel,
            </if>
            <if test="sftpBusChannel != null" >
                sftp_bus_channel,
            </if>
            <if test="remoteName != null" >
                remote_name,
            </if>
            <if test="remotePassword != null" >
                remote_password,
            </if>
            <if test="remoteIp != null" >
                remote_ip,
            </if>
            <if test="remotePort != null" >
                remote_port,
            </if>
            <if test="connectTimeout != null" >
                connect_timeout,
            </if>
            <if test="remoteFilePath != null" >
                remote_file_path,
            </if>
            <if test="remoteFileName != null" >
                remote_file_name,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="sftpSysChannel != null" >
                #{sftpSysChannel,jdbcType=VARCHAR},
            </if>
            <if test="sftpBusChannel != null" >
                #{sftpBusChannel,jdbcType=VARCHAR},
            </if>
            <if test="remoteName != null" >
                #{remoteName,jdbcType=VARCHAR},
            </if>
            <if test="remotePassword != null" >
                #{remotePassword,jdbcType=VARCHAR},
            </if>
            <if test="remoteIp != null" >
                #{remoteIp,jdbcType=VARCHAR},
            </if>
            <if test="remotePort != null" >
                #{remotePort,jdbcType=INTEGER},
            </if>
            <if test="connectTimeout != null" >
                #{connectTimeout,jdbcType=INTEGER},
            </if>
            <if test="remoteFilePath != null" >
                #{remoteFilePath,jdbcType=VARCHAR},
            </if>
            <if test="remoteFileName != null" >
                #{remoteFileName,jdbcType=VARCHAR},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csm.entity.FileConfigDO" >
        update csm_file_config
        <set >
            <if test="sftpSysChannel != null" >
                sftp_sys_channel = #{sftpSysChannel,jdbcType=VARCHAR},
            </if>
            <if test="sftpBusChannel != null" >
                sftp_bus_channel = #{sftpBusChannel,jdbcType=VARCHAR},
            </if>
            <if test="remoteName != null" >
                remote_name = #{remoteName,jdbcType=VARCHAR},
            </if>
            <if test="remotePassword != null" >
                remote_password = #{remotePassword,jdbcType=VARCHAR},
            </if>
            <if test="remoteIp != null" >
                remote_ip = #{remoteIp,jdbcType=VARCHAR},
            </if>
            <if test="remotePort != null" >
                remote_port = #{remotePort,jdbcType=INTEGER},
            </if>
            <if test="connectTimeout != null" >
                connect_timeout = #{connectTimeout,jdbcType=INTEGER},
            </if>
            <if test="remoteFilePath != null" >
                remote_file_path = #{remoteFilePath,jdbcType=VARCHAR},
            </if>
            <if test="remoteFileName != null" >
                remote_file_name = #{remoteFileName,jdbcType=VARCHAR},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>