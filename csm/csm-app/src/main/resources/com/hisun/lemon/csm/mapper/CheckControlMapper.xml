<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csm.dao.ICheckControlDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.csm.entity.CheckControlDO" >
        <id column="check_batch_no" property="checkBatchNo" jdbcType="VARCHAR" />
        <result column="check_date" property="checkDate" jdbcType="DATE" />
        <result column="check_seq" property="checkSeq" jdbcType="INTEGER" />
        <result column="check_type_id" property="checkTypeId" jdbcType="VARCHAR" />
        <result column="check_file_nm" property="checkFileNm" jdbcType="VARCHAR" />
        <result column="check_begin_time" property="checkBeginTime" jdbcType="TIMESTAMP" />
        <result column="check_end_time" property="checkEndTime" jdbcType="TIMESTAMP" />
        <result column="check_stats" property="checkStats" jdbcType="CHAR" />
        <result column="file_receive_count" property="fileReceiveCount" jdbcType="INTEGER" />
        <result column="file_receive_amt" property="fileReceiveAmt" jdbcType="DECIMAL" />
        <result column="total_count" property="totalCount" jdbcType="INTEGER" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="short_count" property="shortCount" jdbcType="INTEGER" />
        <result column="short_amt" property="shortAmt" jdbcType="DECIMAL" />
        <result column="long_count" property="longCount" jdbcType="INTEGER" />
        <result column="long_amt" property="longAmt" jdbcType="DECIMAL" />
        <result column="error_count" property="errorCount" jdbcType="INTEGER" />
        <result column="error_amt" property="errorAmt" jdbcType="DECIMAL" />
        <result column="doubt_count" property="doubtCount" jdbcType="INTEGER" />
        <result column="doubt_amt" property="doubtAmt" jdbcType="DECIMAL" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        check_batch_no, check_date, check_seq, check_type_id, check_file_nm, check_begin_time, check_end_time, 
        check_stats, file_receive_count, file_receive_amt, total_count, total_amt, short_count, 
        short_amt, long_count, long_amt, error_count, error_amt, doubt_count, doubt_amt, 
        create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from csm_check_control
        where check_batch_no = #{checkBatchNo,jdbcType=VARCHAR}
    </select>
    
    <select id="getMaxCheckSeq" resultType="INTEGER">
        select ifnull(max(check_seq), 0) check_seq
        from csm_check_control
        where check_type_id = #{checkTypeId,jdbcType=VARCHAR}
        and check_date = #{checkDate,jdbcType=DATE}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_check_control
        <where>
            <if test="checkBatchNo != null" >
                check_batch_no = #{checkBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="checkDate != null" >
                and check_date = #{checkDate,jdbcType=DATE}
            </if>
            <if test="checkSeq != null" >
                and check_seq = #{checkSeq,jdbcType=INTEGER}
            </if>
            <if test="checkTypeId != null" >
                and check_type_id = #{checkTypeId,jdbcType=VARCHAR}
            </if>
            <if test="checkFileNm != null" >
                and check_file_nm = #{checkFileNm,jdbcType=VARCHAR}
            </if>
            <if test="checkBeginTime != null" >
                and check_begin_time = #{checkBeginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="checkEndTime != null" >
                and check_end_time = #{checkEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="checkStats != null" >
                and check_stats = #{checkStats,jdbcType=CHAR}
            </if>
            <if test="fileReceiveCount != null" >
                and file_receive_count = #{fileReceiveCount,jdbcType=INTEGER}
            </if>
            <if test="fileReceiveAmt != null" >
                and file_receive_amt = #{fileReceiveAmt,jdbcType=DECIMAL}
            </if>
            <if test="totalCount != null" >
                and total_count = #{totalCount,jdbcType=INTEGER}
            </if>
            <if test="totalAmt != null" >
                and total_amt = #{totalAmt,jdbcType=DECIMAL}
            </if>
            <if test="shortCount != null" >
                and short_count = #{shortCount,jdbcType=INTEGER}
            </if>
            <if test="shortAmt != null" >
                and short_amt = #{shortAmt,jdbcType=DECIMAL}
            </if>
            <if test="longCount != null" >
                and long_count = #{longCount,jdbcType=INTEGER}
            </if>
            <if test="longAmt != null" >
                and long_amt = #{longAmt,jdbcType=DECIMAL}
            </if>
            <if test="errorCount != null" >
                and error_count = #{errorCount,jdbcType=INTEGER}
            </if>
            <if test="errorAmt != null" >
                and error_amt = #{errorAmt,jdbcType=DECIMAL}
            </if>
            <if test="doubtCount != null" >
                and doubt_count = #{doubtCount,jdbcType=INTEGER}
            </if>
            <if test="doubtAmt != null" >
                and doubt_amt = #{doubtAmt,jdbcType=DECIMAL}
            </if>
        </where>
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_check_control
        <where>
            <if test="checkBatchNo != null" >
                check_batch_no = #{checkBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="checkDate != null" >
                and check_date = #{checkDate,jdbcType=DATE}
            </if>
            <if test="checkSeq != null" >
                and check_seq = #{checkSeq,jdbcType=INTEGER}
            </if>
            <if test="checkTypeId != null" >
                and check_type_id = #{checkTypeId,jdbcType=VARCHAR}
            </if>
            <if test="checkFileNm != null" >
                and check_file_nm = #{checkFileNm,jdbcType=VARCHAR}
            </if>
            <if test="checkBeginTime != null" >
                and check_begin_time = #{checkBeginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="checkEndTime != null" >
                and check_end_time = #{checkEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="checkStats != null" >
                and check_stats = #{checkStats,jdbcType=CHAR}
            </if>
            <if test="fileReceiveCount != null" >
                and file_receive_count = #{fileReceiveCount,jdbcType=INTEGER}
            </if>
            <if test="fileReceiveAmt != null" >
                and file_receive_amt = #{fileReceiveAmt,jdbcType=DECIMAL}
            </if>
            <if test="totalCount != null" >
                and total_count = #{totalCount,jdbcType=INTEGER}
            </if>
            <if test="totalAmt != null" >
                and total_amt = #{totalAmt,jdbcType=DECIMAL}
            </if>
            <if test="shortCount != null" >
                and short_count = #{shortCount,jdbcType=INTEGER}
            </if>
            <if test="shortAmt != null" >
                and short_amt = #{shortAmt,jdbcType=DECIMAL}
            </if>
            <if test="longCount != null" >
                and long_count = #{longCount,jdbcType=INTEGER}
            </if>
            <if test="longAmt != null" >
                and long_amt = #{longAmt,jdbcType=DECIMAL}
            </if>
            <if test="errorCount != null" >
                and error_count = #{errorCount,jdbcType=INTEGER}
            </if>
            <if test="errorAmt != null" >
                and error_amt = #{errorAmt,jdbcType=DECIMAL}
            </if>
            <if test="doubtCount != null" >
                and doubt_count = #{doubtCount,jdbcType=INTEGER}
            </if>
            <if test="doubtAmt != null" >
                and doubt_amt = #{doubtAmt,jdbcType=DECIMAL}
            </if>
        </where>
    </select>
    
    <select id="getUnfinishListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_check_control
        <where>
            <if test="checkBatchNo != null" >
                check_batch_no = #{checkBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="checkDate != null" >
                and check_date &gt;= #{checkDate,jdbcType=DATE}
            </if>
            <if test="checkSeq != null" >
                and check_seq = #{checkSeq,jdbcType=INTEGER}
            </if>
            <if test="checkTypeId != null" >
                and check_type_id = #{checkTypeId,jdbcType=VARCHAR}
            </if>
            <if test="checkFileNm != null" >
                and check_file_nm = #{checkFileNm,jdbcType=VARCHAR}
            </if>
            <if test="checkBeginTime != null" >
                and check_begin_time = #{checkBeginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="checkEndTime != null" >
                and check_end_time = #{checkEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="checkStats != null" >
                and check_stats != #{checkStats,jdbcType=CHAR}
            </if>
            <if test="fileReceiveCount != null" >
                and file_receive_count = #{fileReceiveCount,jdbcType=INTEGER}
            </if>
            <if test="fileReceiveAmt != null" >
                and file_receive_amt = #{fileReceiveAmt,jdbcType=DECIMAL}
            </if>
            <if test="totalCount != null" >
                and total_count = #{totalCount,jdbcType=INTEGER}
            </if>
            <if test="totalAmt != null" >
                and total_amt = #{totalAmt,jdbcType=DECIMAL}
            </if>
            <if test="shortCount != null" >
                and short_count = #{shortCount,jdbcType=INTEGER}
            </if>
            <if test="shortAmt != null" >
                and short_amt = #{shortAmt,jdbcType=DECIMAL}
            </if>
            <if test="longCount != null" >
                and long_count = #{longCount,jdbcType=INTEGER}
            </if>
            <if test="longAmt != null" >
                and long_amt = #{longAmt,jdbcType=DECIMAL}
            </if>
            <if test="errorCount != null" >
                and error_count = #{errorCount,jdbcType=INTEGER}
            </if>
            <if test="errorAmt != null" >
                and error_amt = #{errorAmt,jdbcType=DECIMAL}
            </if>
            <if test="doubtCount != null" >
                and doubt_count = #{doubtCount,jdbcType=INTEGER}
            </if>
            <if test="doubtAmt != null" >
                and doubt_amt = #{doubtAmt,jdbcType=DECIMAL}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csm_check_control
        where check_batch_no = #{checkBatchNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csm.entity.CheckControlDO" >
        insert into csm_check_control
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="checkBatchNo != null" >
                check_batch_no,
            </if>
            <if test="checkDate != null" >
                check_date,
            </if>
            <if test="checkSeq != null" >
                check_seq,
            </if>
            <if test="checkTypeId != null" >
                check_type_id,
            </if>
            <if test="checkFileNm != null" >
                check_file_nm,
            </if>
            <if test="checkBeginTime != null" >
                check_begin_time,
            </if>
            <if test="checkEndTime != null" >
                check_end_time,
            </if>
            <if test="checkStats != null" >
                check_stats,
            </if>
            <if test="fileReceiveCount != null" >
                file_receive_count,
            </if>
            <if test="fileReceiveAmt != null" >
                file_receive_amt,
            </if>
            <if test="totalCount != null" >
                total_count,
            </if>
            <if test="totalAmt != null" >
                total_amt,
            </if>
            <if test="shortCount != null" >
                short_count,
            </if>
            <if test="shortAmt != null" >
                short_amt,
            </if>
            <if test="longCount != null" >
                long_count,
            </if>
            <if test="longAmt != null" >
                long_amt,
            </if>
            <if test="errorCount != null" >
                error_count,
            </if>
            <if test="errorAmt != null" >
                error_amt,
            </if>
            <if test="doubtCount != null" >
                doubt_count,
            </if>
            <if test="doubtAmt != null" >
                doubt_amt,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="checkBatchNo != null" >
                #{checkBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="checkDate != null" >
                #{checkDate,jdbcType=DATE},
            </if>
            <if test="checkSeq != null" >
                #{checkSeq,jdbcType=INTEGER},
            </if>
            <if test="checkTypeId != null" >
                #{checkTypeId,jdbcType=VARCHAR},
            </if>
            <if test="checkFileNm != null" >
                #{checkFileNm,jdbcType=VARCHAR},
            </if>
            <if test="checkBeginTime != null" >
                #{checkBeginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkEndTime != null" >
                #{checkEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkStats != null" >
                #{checkStats,jdbcType=CHAR},
            </if>
            <if test="fileReceiveCount != null" >
                #{fileReceiveCount,jdbcType=INTEGER},
            </if>
            <if test="fileReceiveAmt != null" >
                #{fileReceiveAmt,jdbcType=DECIMAL},
            </if>
            <if test="totalCount != null" >
                #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="shortCount != null" >
                #{shortCount,jdbcType=INTEGER},
            </if>
            <if test="shortAmt != null" >
                #{shortAmt,jdbcType=DECIMAL},
            </if>
            <if test="longCount != null" >
                #{longCount,jdbcType=INTEGER},
            </if>
            <if test="longAmt != null" >
                #{longAmt,jdbcType=DECIMAL},
            </if>
            <if test="errorCount != null" >
                #{errorCount,jdbcType=INTEGER},
            </if>
            <if test="errorAmt != null" >
                #{errorAmt,jdbcType=DECIMAL},
            </if>
            <if test="doubtCount != null" >
                #{doubtCount,jdbcType=INTEGER},
            </if>
            <if test="doubtAmt != null" >
                #{doubtAmt,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csm.entity.CheckControlDO" >
        update csm_check_control
        <set >
            <if test="checkDate != null" >
                check_date = #{checkDate,jdbcType=DATE},
            </if>
            <if test="checkSeq != null" >
                check_seq = #{checkSeq,jdbcType=INTEGER},
            </if>
            <if test="checkTypeId != null" >
                check_type_id = #{checkTypeId,jdbcType=VARCHAR},
            </if>
            <if test="checkFileNm != null" >
                check_file_nm = #{checkFileNm,jdbcType=VARCHAR},
            </if>
            <if test="checkBeginTime != null" >
                check_begin_time = #{checkBeginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkEndTime != null" >
                check_end_time = #{checkEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkStats != null" >
                check_stats = #{checkStats,jdbcType=CHAR},
            </if>
            <if test="fileReceiveCount != null" >
                file_receive_count = #{fileReceiveCount,jdbcType=INTEGER},
            </if>
            <if test="fileReceiveAmt != null" >
                file_receive_amt = #{fileReceiveAmt,jdbcType=DECIMAL},
            </if>
            <if test="totalCount != null" >
                total_count = #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                total_amt = #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="shortCount != null" >
                short_count = #{shortCount,jdbcType=INTEGER},
            </if>
            <if test="shortAmt != null" >
                short_amt = #{shortAmt,jdbcType=DECIMAL},
            </if>
            <if test="longCount != null" >
                long_count = #{longCount,jdbcType=INTEGER},
            </if>
            <if test="longAmt != null" >
                long_amt = #{longAmt,jdbcType=DECIMAL},
            </if>
            <if test="errorCount != null" >
                error_count = #{errorCount,jdbcType=INTEGER},
            </if>
            <if test="errorAmt != null" >
                error_amt = #{errorAmt,jdbcType=DECIMAL},
            </if>
            <if test="doubtCount != null" >
                doubt_count = #{doubtCount,jdbcType=INTEGER},
            </if>
            <if test="doubtAmt != null" >
                doubt_amt = #{doubtAmt,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where check_batch_no = #{checkBatchNo,jdbcType=VARCHAR}
    </update>
    
    <update id="updateByCondition" parameterType="com.hisun.lemon.csm.entity.CheckControlDO" >
        update csm_check_control
        <set >
            <if test="valueDO.checkDate != null" >
                check_date = #{valueDO.checkDate,jdbcType=DATE},
            </if>
            <if test="valueDO.checkSeq != null" >
                check_seq = #{valueDO.checkSeq,jdbcType=INTEGER},
            </if>
            <if test="valueDO.checkTypeId != null" >
                check_type_id = #{valueDO.checkTypeId,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.checkFileNm != null" >
                check_file_nm = #{valueDO.checkFileNm,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.checkBeginTime != null" >
                check_begin_time = #{valueDO.checkBeginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.checkEndTime != null" >
                check_end_time = #{valueDO.checkEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.checkStats != null" >
                check_stats = #{valueDO.checkStats,jdbcType=CHAR},
            </if>
            <if test="valueDO.fileReceiveCount != null" >
                file_receive_count = #{valueDO.fileReceiveCount,jdbcType=INTEGER},
            </if>
            <if test="valueDO.fileReceiveAmt != null" >
                file_receive_amt = #{valueDO.fileReceiveAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.totalCount != null" >
                total_count = #{valueDO.totalCount,jdbcType=INTEGER},
            </if>
            <if test="valueDO.totalAmt != null" >
                total_amt = #{valueDO.totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.shortCount != null" >
                short_count = #{valueDO.shortCount,jdbcType=INTEGER},
            </if>
            <if test="valueDO.shortAmt != null" >
                short_amt = #{valueDO.shortAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.longCount != null" >
                long_count = #{valueDO.longCount,jdbcType=INTEGER},
            </if>
            <if test="valueDO.longAmt != null" >
                long_amt = #{valueDO.longAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.errorCount != null" >
                error_count = #{valueDO.errorCount,jdbcType=INTEGER},
            </if>
            <if test="valueDO.errorAmt != null" >
                error_amt = #{valueDO.errorAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.doubtCount != null" >
                doubt_count = #{valueDO.doubtCount,jdbcType=INTEGER},
            </if>
            <if test="valueDO.doubtAmt != null" >
                doubt_amt = #{valueDO.doubtAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.createTime != null" >
                create_time = #{valueDO.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.modifyTime != null" >
                modify_time = #{valueDO.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.tmSmp != null" >
                tm_smp = #{valueDO.tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <if test="conditionDO.checkBatchNo != null" >
                check_batch_no = #{conditionDO.checkBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.checkDate != null" >
                and check_date = #{conditionDO.checkDate,jdbcType=DATE}
            </if>
            <if test="conditionDO.checkSeq != null" >
                and check_seq = #{conditionDO.checkSeq,jdbcType=INTEGER}
            </if>
            <if test="conditionDO.checkTypeId != null" >
                and check_type_id = #{conditionDO.checkTypeId,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.checkFileNm != null" >
                and check_file_nm = #{conditionDO.checkFileNm,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.checkBeginTime != null" >
                and check_begin_time = #{conditionDO.checkBeginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="conditionDO.checkEndTime != null" >
                and check_end_time = #{conditionDO.checkEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="conditionDO.checkStats != null" >
                and check_stats = #{conditionDO.checkStats,jdbcType=CHAR}
            </if>
            <if test="conditionDO.fileReceiveCount != null" >
                and file_receive_count = #{conditionDO.fileReceiveCount,jdbcType=INTEGER}
            </if>
            <if test="conditionDO.fileReceiveAmt != null" >
                and file_receive_amt = #{conditionDO.fileReceiveAmt,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.totalCount != null" >
                and total_count = #{conditionDO.totalCount,jdbcType=INTEGER}
            </if>
            <if test="conditionDO.totalAmt != null" >
                and total_amt = #{conditionDO.totalAmt,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.shortCount != null" >
                and short_count = #{conditionDO.shortCount,jdbcType=INTEGER}
            </if>
            <if test="conditionDO.shortAmt != null" >
                and short_amt = #{conditionDO.shortAmt,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.longCount != null" >
                and long_count = #{conditionDO.longCount,jdbcType=INTEGER}
            </if>
            <if test="conditionDO.longAmt != null" >
                and long_amt = #{conditionDO.longAmt,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.errorCount != null" >
                and error_count = #{conditionDO.errorCount,jdbcType=INTEGER}
            </if>
            <if test="conditionDO.errorAmt != null" >
                and error_amt = #{conditionDO.errorAmt,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.doubtCount != null" >
                and doubt_count = #{conditionDO.doubtCount,jdbcType=INTEGER}
            </if>
            <if test="conditionDO.doubtAmt != null" >
                and doubt_amt = #{conditionDO.doubtAmt,jdbcType=DECIMAL}
            </if>
        </where>
    </update>
</mapper>