<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csm.dao.ISettleCardDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.csm.entity.SettleCardDO" >
        <id column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="cap_corg_nm" property="capCorgNm" jdbcType="VARCHAR" />
        <result column="cap_corg_snm" property="capCorgSnm" jdbcType="VARCHAR" />
        <result column="subbranch" property="subbranch" jdbcType="VARCHAR" />
        <result column="cap_corg_no" property="capCorgNo" jdbcType="VARCHAR" />
        <result column="cap_card_no" property="capCardNo" jdbcType="VARCHAR" />
        <result column="cap_card_name" property="capCardName" jdbcType="VARCHAR" />
        <result column="bnk_mbl_no" property="bnkMblNo" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        user_id, user_name, cap_corg_nm, cap_corg_snm, subbranch, cap_corg_no, cap_card_no, cap_card_name, bnk_mbl_no, stats, eff_date,
        exp_date, opr_id, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from csm_settle_card
        where user_id = #{userId,jdbcType=VARCHAR}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from csm_settle_card
        <where>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="capCorgNm != null" >
                and cap_corg_nm = #{capCorgNm,jdbcType=VARCHAR}
            </if>
            <if test="capCorgSnm != null" >
                and cap_corg_snm = #{capCorgSnm,jdbcType=VARCHAR}
            </if>
            <if test="capCorgNo != null" >
                and cap_corg_no = #{capCorgNo,jdbcType=VARCHAR}
            </if>
            <if test="capCardNo != null" >
                and cap_card_no = #{capCardNo,jdbcType=VARCHAR}
            </if>
            <if test="capCardName != null" >
                and cap_card_name = #{capCardName,jdbcType=VARCHAR}
            </if>
            <if test="bnkMblNo != null" >
                and bnk_mbl_no = #{bnkMblNo,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csm_settle_card
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csm.entity.SettleCardDO" >
        insert into csm_settle_card
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                user_id,
            </if>
            <if test="userName != null" >
                user_name,
            </if>
            <if test="capCorgNm != null" >
                cap_corg_nm,
            </if>
            <if test="capCorgSnm != null" >
                cap_corg_snm,
            </if>
            <if test="capCorgNo != null" >
                cap_corg_no,
            </if>
            <if test="capCardNo != null" >
                cap_card_no,
            </if>
            <if test="capCardName != null" >
                cap_card_name,
            </if>
            <if test="bnkMblNo != null" >
                bnk_mbl_no,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="capCorgNm != null" >
                #{capCorgNm,jdbcType=VARCHAR},
            </if>
            <if test="capCorgSnm != null" >
                #{capCorgSnm,jdbcType=VARCHAR},
            </if>
            <if test="capCorgNo != null" >
                #{capCorgNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardNo != null" >
                #{capCardNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardName != null" >
                #{capCardName,jdbcType=VARCHAR},
            </if>
            <if test="bnkMblNo != null" >
                #{bnkMblNo,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csm.entity.SettleCardDO" >
        update csm_settle_card
        <set >
            <if test="capCorgNm != null" >
                cap_corg_nm = #{capCorgNm,jdbcType=VARCHAR},
            </if>
            <if test="capCorgSnm != null" >
                cap_corg_snm = #{capCorgSnm,jdbcType=VARCHAR},
            </if>
            <if test="capCorgNo != null" >
                cap_corg_no = #{capCorgNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardNo != null" >
                cap_card_no = #{capCardNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardName != null" >
                cap_card_name = #{capCardNo,jdbcType=VARCHAR},
            </if>
            <if test="bnkMblNo != null" >
                bnk_mbl_no = #{bnkMblNo,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>
</mapper>