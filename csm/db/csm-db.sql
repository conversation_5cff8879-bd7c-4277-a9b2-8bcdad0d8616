--错误码
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM00000','zh','交易成功!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10001','zh','手机号码不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10002','zh','用户号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10003','zh','结算状态不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10004','zh','查询周期不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10005','zh','订单号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10006','zh','结算金额不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10008','zh','支付密码不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10009','zh','订单状态不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10010','zh','结算类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10011','zh','对账类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM10012','zh','支付密码随机数不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30002','zh','结算申请失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30003','zh','结算方式有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30004','zh','结算银行卡不存在或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30005','zh','结算信息不存在或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30006','zh','结算确认失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30007','zh','账户余额不足!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30008','zh','手续费有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30009','zh','银行卡解密失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30010','zh','银行卡脱敏失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30011','zh','订单不存在!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30012','zh','交易金额有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30013','zh','对账补单失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM30014','zh','到账金额不能为0!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSM30015','zh','商户没有结算的权限!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSM30016','zh','不能重复下单!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM39998','zh','对账组件配置有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSM39999','zh','对账组件生成失败!',now(),now());

--drop table csm_check_control;
--结算对账主控表
CREATE TABLE IF NOT EXISTS csm_check_control (
    check_batch_no VARCHAR(25) NOT NULL COMMENT '对账批次号',
    check_date DATE NOT NULL COMMENT '对账清算日期',
    check_seq INT NOT NULL COMMENT '对账批次序号',
    check_type_id VARCHAR(8) NOT NULL COMMENT '对账类型',
    check_file_nm VARCHAR(64) COMMENT '对账文件名称',
    check_begin_time DATETIME NOT NULL COMMENT '对账开始时间',
    check_end_time DATETIME COMMENT '对账结束时间',
    check_stats CHAR(1) DEFAULT '0' COMMENT '对账状态 0:待对账 1:已获取文件 2:已入库 3:已对账 4:已差错处理 9:对账完成',
    file_receive_count INTEGER(9) DEFAULT 0 COMMENT '文件总笔数',
    file_receive_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '文件总金额',
    total_count INTEGER(9) DEFAULT 0 COMMENT '总笔数',
    total_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '总金额',
    short_count INTEGER(9) DEFAULT 0 COMMENT '我方有对方无笔数',
    short_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '我方有对方无金额',
    long_count INTEGER(9) DEFAULT 0 COMMENT '对方有我方无笔数',
    long_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '对方有我方无金额',
    error_count INTEGER(9) DEFAULT 0 COMMENT '差错笔数',
    error_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '差错金额',
    doubt_count INTEGER(9) DEFAULT 0 COMMENT '存疑笔数',
    doubt_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '存疑金额',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE csm_check_control ADD CONSTRAINT pk_csm_check_control PRIMARY KEY(check_batch_no);
--索引
ALTER TABLE csm_check_control ADD UNIQUE idx_csm_check_control_1(check_batch_no);
--索引
ALTER TABLE csm_check_control ADD UNIQUE idx_csm_check_control_2(check_type_id, check_date, check_seq);
--索引
ALTER TABLE csm_check_control ADD INDEX idx_csm_check_control_3(check_date);

--drop table csm_check_param;
--结算对账参数表
CREATE TABLE IF NOT EXISTS csm_check_param (
    check_type_id VARCHAR(16) NOT NULL COMMENT '对账类型',
    sftp_sys_channel VARCHAR(16) COMMENT '文件服务器系统渠道',
    sftp_bus_channel VARCHAR(16) COMMENT '文件服务器业务渠道',
    import_table VARCHAR(16) COMMENT '导入数据库表名',
    import_format VARCHAR(32) COMMENT '导入数据格式',
    import_file_path VARCHAR(128) COMMENT '导入数据文件路径',
    export_table VARCHAR(16) COMMENT '导出数据库表名',
    export_format VARCHAR(32) COMMENT '导出数据格式',
    export_file_path VARCHAR(128) COMMENT '导出数据文件路径',
    auto_flag CHAR(1) DEFAULT 'S' COMMENT '自动处理标识 U:人工 S:系统',
    encrypt_flag CHAR(1) DEFAULT '0' COMMENT '加密标识 0:否 1:是',
    encrypt_component VARCHAR(32) COMMENT '加密组件名称',
    descrypt_flag CHAR(1) DEFAULT '0' COMMENT '解密标识 0:否 1:是',
    descrypt_component VARCHAR(32) COMMENT '解密组件名称',
    check_process_component VARCHAR(32) COMMENT '对账处理组件名称',
    check_process_clazz VARCHAR(64) COMMENT '对账处理组件类对象',
    error_process_component VARCHAR(32) COMMENT '差错处理组件名称',
    error_process_clazz VARCHAR(64) COMMENT '差错处理组件类对象',
    multiple_check_flg CHAR(1) DEFAULT 'Y' COMMENT '每日多次对账标识',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:生效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE csm_check_param ADD CONSTRAINT pk_csm_check_param PRIMARY KEY(check_type_id);
--索引
ALTER TABLE csm_check_param ADD UNIQUE idx_csm_check_param_1(check_type_id);


--drop table csm_file_config;
--文件服务器参数表
CREATE TABLE IF NOT EXISTS csm_file_config (
    id VARCHAR(8) NOT NULL COMMENT 'ID',
    sftp_sys_channel VARCHAR(16) COMMENT '文件服务器系统渠道',
    sftp_bus_channel VARCHAR(16) COMMENT '文件服务器业务渠道',
    remote_name VARCHAR(16) COMMENT '文件服务器用户名',
    remote_password VARCHAR(16) COMMENT '文件服务器密码',
    remote_ip VARCHAR(32) COMMENT '文件服务器IP地址',
    remote_port INT(8) COMMENT '文件服务器端口',
    connect_timeout INT(8) COMMENT '连接超时时间 单位:毫秒',
    remote_file_path VARCHAR(32) COMMENT '文件上传路径',
    remote_file_name VARCHAR(128) COMMENT '文件上传名称',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE csm_file_config ADD CONSTRAINT pk_csm_file_config PRIMARY KEY(id);
--索引
ALTER TABLE csm_file_config ADD UNIQUE idx_csm_file_config_1(id);
--索引
ALTER TABLE csm_file_config ADD UNIQUE idx_csm_file_config_2(sftp_sys_channel, sftp_bus_channel);

--DROP TABLE csm_settle_base;
--用户结算基础信息表
CREATE TABLE IF NOT EXISTS csm_settle_base (
    user_id VARCHAR(20) NOT NULL COMMENT '用户号',
    user_name VARCHAR(128) NOT NULL COMMENT '用户名称',
    mbl_no VARCHAR(20) NOT NULL COMMENT '手机号码',
    settle_type VARCHAR(8) NOT NULL COMMENT '结算方式 0404:自动 0403:自主 0405:营业厅',
    settle_cycle_type VARCHAR(8) DEFAULT 'daily' COMMENT '结算周期类型 日结算:daily 周结算:weekly 月结算:monthly',
    agreement_pay_days VARCHAR(8) DEFAULT '1' COMMENT '划款天数',
    settle_sites VARCHAR(3) COMMENT '结算地点',
    settle_sites_name VARCHAR(64) COMMENT '结算地点名称',
    hall_sites VARCHAR(8) COMMENT '营业厅网点',
    hall_sites_name VARCHAR(64) COMMENT '营业厅网点名称',
    last_last_settle_day DATE COMMENT '上上结算日',
    last_settle_day DATE COMMENT '上一结算日',
    next_settle_day DATE COMMENT '下一结算日',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:生效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE csm_settle_base ADD CONSTRAINT pk_csm_settle_base PRIMARY KEY(user_id);
--索引
ALTER TABLE csm_settle_base ADD UNIQUE idx_csm_settle_base_1(user_id);

--DROP TABLE csm_settle_card;
--用户结算银行卡信息表
CREATE TABLE IF NOT EXISTS csm_settle_card (
    user_id VARCHAR(20) NOT NULL COMMENT '用户号',
    user_name VARCHAR(128) NOT NULL COMMENT '用户名称',
    cap_corg_nm VARCHAR(64) NOT NULL COMMENT '资金合作机构全称',
    cap_corg_snm VARCHAR(32) NOT NULL COMMENT '资金合作机构简称',
    cap_corg_no VARCHAR(32) NOT NULL COMMENT '资金合作机构号',
    cap_card_no VARCHAR(30) NOT NULL COMMENT '资金卡号',
    cap_card_name VARCHAR(32) NOT NULL COMMENT '资金卡账户名',
    bnk_mbl_no VARCHAR(20) COMMENT '银行预留手机号码',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:生效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE csm_settle_card ADD CONSTRAINT pk_csm_settle_card PRIMARY KEY(user_id, cap_card_no);
--索引
ALTER TABLE csm_settle_card ADD UNIQUE idx_csm_settle_card_1(user_id, cap_card_no);
--索引
ALTER TABLE csm_settle_card ADD INDEX idx_csm_settle_card_2(bnk_mbl_no);


--drop table csm_settle_order;
--结算订单表
CREATE TABLE IF NOT EXISTS csm_settle_order (
    order_no VARCHAR(25) NOT NULL COMMENT '订单号',
    trade_date DATE NOT NULL COMMENT '交易日期',
    trade_time TIME NOT NULL COMMENT '交易时间',
    agreement_pay_date DATE NOT NULL COMMENT '协议付款日期',
    trade_amt DECIMAL(15,2) NOT NULL COMMENT '结算金额',
    fee DECIMAL(15,2) NOT NULL COMMENT '手续费',
    ccy VARCHAR(8) NOT NULL COMMENT '币种',
    bus_type CHAR(4) NOT NULL COMMENT  '业务类型',
    user_id VARCHAR(20) NOT NULL COMMENT '用户号',
    user_name VARCHAR(128) NOT NULL COMMENT '用户名称',
    mbl_no VARCHAR(20) COMMENT '手机号码',
    cap_corg_nm VARCHAR(64) DEFAULT '' COMMENT '资金合作机构全称',
    cap_corg_snm VARCHAR(32) DEFAULT '' COMMENT '资金合作机构简称',
    cap_corg_no VARCHAR(32) DEFAULT '' COMMENT '资金合作机构号',
    cap_card_no VARCHAR(128) DEFAULT '' COMMENT '资金卡号',
    cap_card_nm VARCHAR(32) DEFAULT '' COMMENT '资金卡账户名',
    bnk_mbl_no VARCHAR(20) DEFAULT '' COMMENT '银行预留手机号码',
    stats CHAR(3) DEFAULT 'S1' COMMENT '状态S1:待付款 S2:付款成功 F:付款失败',
    payment_date DATE COMMENT '付款日期',
    payment_time TIME COMMENT '付款时间',
    last_last_settle_day DATE COMMENT '上上结算日',
    last_settle_day DATE COMMENT '上一结算日',
    message VARCHAR(64) COMMENT '结算状态信息',
    bus_order_no VARCHAR(32) COMMENT '业务订单号',
    bus_order_time DATETIME COMMENT '业务订单时间',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE csm_settle_order ADD CONSTRAINT pk_csm_settle_order PRIMARY KEY(order_no);
--索引
ALTER TABLE csm_settle_order ADD UNIQUE idx_csm_settle_order_1(order_no);
--索引
ALTER TABLE csm_settle_order ADD INDEX idx_csm_settle_order_2(user_id);
--索引
ALTER TABLE csm_settle_order ADD INDEX idx_csm_settle_order_3(mbl_no);
--索引
ALTER TABLE csm_settle_order ADD INDEX idx_csm_settle_order_4(trade_date);
--索引
ALTER TABLE csm_settle_order ADD INDEX idx_csm_settle_order_5(payment_date);
--索引
ALTER TABLE csm_settle_order ADD INDEX idx_csm_settle_order_6(bus_order_no);