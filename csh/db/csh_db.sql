
CREATE TABLE `csh_refund_order` (
  `rfd_ord_no` varchar(32) NOT NULL COMMENT '退款订单号',
  `bus_rdf_ord_no` varchar(32) DEFAULT NULL COMMENT '外部模块退款订单号',
  `orgin_order_no` varchar(32) DEFAULT NULL COMMENT '原订单号',
  `fnd_rfd_order_no` varchar(32) DEFAULT NULL COMMENT '资金能力订单号',
  `user_id` varchar(32) DEFAULT '' COMMENT '内部用户号',
  `merc_id` varchar(24) DEFAULT '' COMMENT '卖家用户号',
  `merc_name` varchar(64) DEFAULT NULL COMMENT '商户名称',
  `order_status` char(2) NOT NULL DEFAULT '0' COMMENT '退款状态   U:预登记  S:成功  F：失败',
  `fee` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '服务费',
  `rfd_amt` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '申请退款金额',
  `rfd_user_amt` decimal(13,2) DEFAULT NULL COMMENT '退回到用户金额',
  `coupon_amt` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `rfd_reason` varchar(200) DEFAULT NULL COMMENT '退款原因',
  `good_info` varchar(200) DEFAULT NULL COMMENT '商品信息',
  `bus_type` varchar(4) NOT NULL DEFAULT '0601' COMMENT '业务类型',
  `rfd_type` varchar(2) NOT NULL COMMENT '原路 01   02账户',
  `tx_type` varchar(2) NOT NULL COMMENT '交易类型  06 退款  09 撤销',
  `coupon_type` varchar(2) NOT NULL COMMENT '优惠类型',
  `order_ccy` varchar(4) NOT NULL COMMENT '币种',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `modify_time` datetime NOT NULL COMMENT '修改时间',
  `ac_tm` date NOT NULL COMMENT '会计时间',
  `tx_tm` datetime NOT NULL COMMENT '交易时间',
  `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`rfd_ord_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `csh_paytype` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merc_id` varchar(255) NOT NULL,
  `app_cnl` varchar(8) NOT NULL,
  `bus_type` char(5) NOT NULL,
  `pay_types` varchar(60) NOT NULL,
  `gw_pay_types` varchar(60) NOT NULL DEFAULT '0',
  `status` char(1) NOT NULL,
  `modify_opr` varchar(255) NOT NULL,
  `modify_time` datetime NOT NULL,
  `create_time` datetime NOT NULL,
  `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

INSERT INTO `csh_paytype` VALUES ('1', '*', 'PWM', '0101', '00001100000000000', '00000000', '1', 'admin', NOW(),NOW(), NOW());
INSERT INTO `csh_paytype` VALUES ('2', '*', 'PWM', '0102', '10000000000000000', '00000000', '1', 'admin', NOW(),NOW(), NOW());
INSERT INTO `csh_paytype` VALUES ('3', '*', 'PWM', '0103', '01000000000000000', '00000000', '1', 'admin', NOW(),NOW(), NOW());
INSERT INTO `csh_paytype` VALUES ('4', '*', 'PWM', '0501', '00100', '00000', '1', 'admin', NOW(),NOW(), NOW());


INSERT INTO `csh_paytype` VALUES ('5', '*', 'TAM', '0301', '00101111111111111', '00000000', '1', 'admin', NOW(),NOW(), NOW());
INSERT INTO `csh_paytype` VALUES ('6', '*', 'TAM', '0303', '00101111111111111', '00000000', '1', 'admin', NOW(),NOW(), NOW());
INSERT INTO `csh_paytype` VALUES ('7', '*', 'TAM', '0302', '00101111111111000', '00000000', '1', 'admin', NOW(),NOW(), NOW());

INSERT INTO `csh_paytype` VALUES ('8', '*', 'CPM', '0801', '1111111111111', '1111111111111111', '1', 'admin', NOW(),NOW(), NOW());
INSERT INTO `csh_paytype` VALUES ('9', '*', 'CPM', '0802', '00111111001110000', '00000000', '1', 'admin',NOW(),NOW(), NOW());

INSERT INTO `csh_paytype` VALUES ('10', '*', 'ONR', '0203', '11111000000000000', '00000000', '1', 'admin', NOW(),NOW(), NOW());
INSERT INTO `csh_paytype` VALUES ('11', '*', 'INV', '0701', '111111111111111', '1111111111111', '1', 'admin', NOW(),NOW(), NOW());

CREATE TABLE `csh_pay_jrn` (
  `pay_jrn_no` varchar(27) NOT NULL,
  `pay_order_no` varchar(24) NOT NULL,
  `tx_tm` datetime NOT NULL,
  `tx_type` varchar(2) NOT NULL,
  `user_id` varchar(20) DEFAULT NULL,
  `pay_ord_tm` datetime NOT NULL,
  `bus_order_no` varchar(28) NOT NULL,
  `fnd_order_no` varchar(24) DEFAULT NULL,
  `order_amt` decimal(15,2) NOT NULL,
  `ccy` varchar(6) DEFAULT NULL,
  `bal_amt` decimal(15,2) NOT NULL,
  `inv_amt` decimal(15,2) NOT NULL,
  `coupon_amt` decimal(15,2) NOT NULL,
  `coupon_type` varchar(2) DEFAULT NULL COMMENT '01:海币 02:电子券',
  `coupon_no` varchar(25) DEFAULT NULL,
  `crd_pay_amt` decimal(15,2) NOT NULL,
  `crd_pay_type` int(2) NOT NULL,
  `jrn_status` varchar(2) NOT NULL,
  `create_time` datetime NOT NULL,
  `modify_time` datetime NOT NULL,
  `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`pay_jrn_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `csh_order` (
  `order_no` varchar(24) NOT NULL,
  `order_tm` datetime NOT NULL,
  `payer_id` varchar(20) DEFAULT NULL,
  `tx_type` varchar(2) NOT NULL,
  `app_cnl` varchar(8) DEFAULT NULL,
  `bus_type` varchar(4) NOT NULL,
  `bus_order_no` varchar(28) NOT NULL DEFAULT '',
  `pay_jrn_no` varchar(28) DEFAULT '',
  `jrn_tx_tm` datetime DEFAULT NULL,
  `ac_tm` date DEFAULT NULL,
  `fnd_order_no` varchar(24) DEFAULT '' COMMENT '资金能力订单号',
  `payee_id` varchar(20) DEFAULT '',
  `order_channel` varchar(5) DEFAULT '',
  `sys_channel` varchar(5) DEFAULT '',
  `pay_type` varchar(60) DEFAULT '',
  `bus_pay_type` varchar(60) DEFAULT NULL,
  `total_amt` decimal(15,2) DEFAULT NULL,
  `order_amt` decimal(15,2) NOT NULL,
  `order_status` varchar(2) NOT NULL,
  `order_exp_tm` datetime NOT NULL,
  `order_succ_tm` datetime DEFAULT NULL,
  `cap_corg_no` varchar(20) DEFAULT '',
  `cap_card_type` varchar(2) DEFAULT '',
  `crd_pay_type` varchar(2) DEFAULT '',
  `crd_pay_amt` decimal(15,2) DEFAULT NULL,
  `bal_amt` decimal(15,2) DEFAULT NULL,
  `inv_amt` decimal(15,2) DEFAULT NULL,
  `coupon_amt` decimal(15,2) DEFAULT NULL,
  `coupon_type` varchar(2) DEFAULT NULL,
  `coupon_no` varchar(25) DEFAULT NULL COMMENT '电子券编号',
  `goods_info` varchar(255) DEFAULT '',
  `left_total_amt` decimal(15,2) DEFAULT NULL,
  `left_bal_amt` decimal(15,2) DEFAULT NULL,
  `left_card_amt` decimal(15,2) DEFAULT NULL,
  `left_inv_amt` decimal(15,2) DEFAULT NULL,
  `left_coupon_amt` decimal(15,2) DEFAULT NULL,
  `fee` decimal(15,2) DEFAULT NULL,
  `left_fee` decimal(15,2) DEFAULT NULL,
  `fee_flag` varchar(2) DEFAULT NULL,
  `ccy` varchar(5) DEFAULT NULL,
  `merc_name` varchar(255) DEFAULT '',
  `remark` varchar(255) DEFAULT '',
  `create_time` datetime NOT NULL,
  `modify_time` datetime NOT NULL,
  `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`order_no`),
  KEY `csh_order_index_busno` (`bus_order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


--公共常量表初始化
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('1', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '个人快捷充值', 'CSH', '0101', '1', 'sys', '2017-08-28 15:39:07', '2017-08-28 15:39:09', '2017-08-28 20:11:06');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('2', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '个人线下充值', 'CSH', '0102', '2', 'sys', '2017-08-28 19:20:13', '2017-08-28 19:20:15', '2017-08-28 20:11:05');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('3', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '个人营业厅充值', 'CSH', '0103', '3', 'sys', '2017-08-28 19:21:10', '2017-08-28 19:21:12', '2017-08-28 20:11:03');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('4', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '企业网银充值', 'CSH', '0104', '4', 'sys', '2017-08-28 19:22:00', '2017-08-28 19:22:03', '2017-08-28 20:11:00');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('5', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '条码消费', 'CSH', '0201', '5', 'sys', '2017-08-28 19:22:33', '2017-08-28 19:22:34', '2017-08-28 20:10:58');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('6', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '扫码付消费', 'CSH', '0202', '6', 'sys', '2017-08-28 19:23:04', '2017-08-28 19:23:06', '2017-08-28 20:10:57');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('7', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', 'APP消费', 'CSH', '0203', '7', 'sys', '2017-08-28 19:23:29', '2017-08-28 19:23:31', '2017-08-28 20:10:55');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('8', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', 'POS消费', 'CSH', '0204', '8', 'sys', '2017-08-28 19:24:15', '2017-08-28 19:24:17', '2017-08-28 20:10:51');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('9', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '银行卡收单', 'CSH', '0205', '9', 'sys', '2017-08-28 19:24:22', '2017-08-28 19:24:19', '2017-08-28 20:10:49');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('10', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '转到账户', 'CSH', '0301', '10', 'sys', '2017-08-28 19:25:46', '2017-08-28 19:25:51', '2017-08-28 20:10:48');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('11', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '转银行卡', 'CSH', '0302', '11', 'sys', '2017-08-28 19:25:55', '2017-08-28 19:25:53', '2017-08-28 20:10:46');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('12', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '当面收款', 'CSH', '0303', '12', 'sys', '2017-08-28 19:27:01', '2017-08-28 19:27:03', '2017-08-28 20:10:44');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('13', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '充海币', 'CSH', '0501', '13', 'sys', '2017-08-28 19:27:36', '2017-08-28 19:27:38', '2017-08-28 20:10:43');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('14', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '理财买入', 'CSH', '0701', '14', 'sys', '2017-08-28 19:28:09', '2017-08-28 19:28:11', '2017-08-28 20:10:42');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('15', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '充流量', 'CSH', '0801', '15', 'sys', '2017-08-28 19:29:14', '2017-08-28 19:29:17', '2017-08-28 20:10:40');
INSERT INTO `seatelpay_cmm`.`cmm_constant_param` (`parm_id`, `parm_nm`, `eff_dt`, `exp_dt`, `eff_flg`, `parm_disp_nm`, `parm_cls`, `parm_val`, `rmk`, `upd_opr_id`, `create_time`, `modify_time`, `tm_smp`) VALUES ('16', 'BUS_TYPE', '2017-08-28', '9999-08-28', '1', '充话费', 'CSH', '0802', '16', 'sys', '2017-08-28 19:29:20', '2017-08-28 19:29:19', '2017-08-28 20:10:38');

--错误码表初始化
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10001','zh','请给定收银订单号!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10002','zh','付款余额必须为有效金额数值!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10003','zh','付款海币必须为有效数值!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10004','zh','付款电子券必须为有效数值!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10005','zh','收款方不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10006','zh','业务模块订单号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10007','zh','订单金额不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10008','zh','请指定交易类型!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10009','zh','请指定业务类型!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10010','zh','订单系统渠道不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10011','zh','付款理财金额必须为有效数值!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10012','zh','对不起,请输入支付密码!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10013','zh','对不起,收银流水号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10014','zh','补款金额非法!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10015','zh','补款类型不能为空!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10016','zh','资金能力订单号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10017','zh','资金能力订单状态不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10018','zh','发卡行编号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10019','zh','银行卡号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH10020','zh','手机号码不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10021','zh','订单的支付应用不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10022','zh','收银订单号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10023','zh','外部模块退款订单号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10024','zh','退款金额不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10025','zh','退款方式不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10026','zh','内部用户号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10027','zh','短信下发标识不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10028','zh','短信下发标识非法!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10029','zh','用户认证标识不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10030','zh','用户名不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10031','zh','银行持卡人名字不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10032','zh','业务类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10033','zh','请指定应用渠道!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10034','zh','请指定业务类型!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10035','zh','商户编号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10036','zh','是否直付标识非法!',now(),now());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10037','zh','资金能力订单号不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10038','zh','交易日期不是今天不能撤销!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10039','zh','已退款或全部退款不能撤销!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10040','zh','附言摘要不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10041','zh','汇款单图片不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10042','zh','请指定对账差错处理类型!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10043','zh','业务类型不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10044','zh','币种不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH10045','zh','交易金额不能为空!',NOW(),NOW());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10070','zh','配额优惠类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10071','zh','折扣券异常!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10072','zh','使用优惠类型不能为空!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10073','zh','请指定支付机构!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10074','zh','充值用户id不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10075','zh','营业厅撤销请求状态非法!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10076','zh','个企标识非空或标识非法!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10077','zh','订单状态非法!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10078','zh','只支持充值长款退款!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10079','zh','该订单支付方式不支持长款退款!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH10080','zh','资金流入方向不能为空!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20001','zh','订单支付金额非法!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20002','zh','订单状态不允许支付!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20003','zh','订单金额校验失败!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20004','zh','订单收款方校验失败!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20005','zh','理财账户支付金额必须大于0!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20006','zh','登记收银订单失败!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20007','zh','生成收银流水失败!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20008','zh','更新收银订单失败!',now(),now());  
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20009','zh','更新收银流水失败!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20010','zh','更新收银订单失败!',now(),now()); 
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20011','zh','已经存在支付方式记录!',now(),now());  
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CSH20012','zh','未找到对应的支付方式记录!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20013','zh','支付方式配置有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20014','zh','指定支付方式有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20015','zh','未找到原订单，通知业务失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20016','zh','未找到合适的支付方式!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20017','zh','只允许使用一种优惠!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20018','zh','只允许使用一种方式支付!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20019','zh','订单金额总分校验失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20020','zh','优惠使用金额超过限制额度!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20021','zh','原订单状态不是“成功”或“部分退款”!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20022','zh','原订单优惠有误，只允许使用一种优惠!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20023','zh','生成退款订单失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20024','zh','更新退款订单失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20025','zh','原退款订单不存在!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20026','zh','未找到任何的签约银行卡信息!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20027','zh','快捷预签约失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20028','zh','快捷签约失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20029','zh','快捷支付失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20030','zh','订单状态不允许撤销!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20031','zh','未找到快捷预签约银行卡信息!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20032','zh','申请退款金额不能大于剩余可退金额!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20033','zh','根据订单与付款人找不到订单信息!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20034','zh','更新订单信息失败!',now(),now());


insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20035','zh','生成对账文件，写文件失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20036','zh','生成对账标志文件失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20037','zh','上传对账文件失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20038','zh','获取本地路径失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20039','zh','不支持的差错处理操作!',now(),now());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20040','zh','订单已经过期，请重新下单!',NOW(),NOW());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20050','zh','未找到账户信息!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20051','zh','订单优惠类型不合法!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20052','zh','订单账务借贷不平衡!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20053','zh','未找到订单数据!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20054','zh','未找到流水数据!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20055','zh','收款方不能为空!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20056','zh','付款方不能为空!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20057','zh','交易类型不能为空!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20058','zh','业务类型不能为空!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20059','zh','退回方式不能为空!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20060','zh','消费退款失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20061','zh','账户余额查询失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20062','zh','生成用户商户订单信息失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20063','zh','原订单不存在!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20064','zh','更新用户商户订单信息失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20065','zh','缴费状态不正确!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20066','zh','退款或撤销更新原订单失败!',now(),now());


insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20067','zh','款汇登记失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20068','zh','申请退款金额不能大于订单金额!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20071','zh','配额金额非法!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20072','zh','请检查补款类型和支付方式是否匹配!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20073','zh','支付方式不支持该订单支付!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20074','zh','充值业务不允许使用余额!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20075','zh','充值业务不允许使用优惠!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20076','zh','请输入支付密码!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20077','zh','原订单已成功退款!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20078','zh','申请退款失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20079','zh','退款撤销计算组件异常!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20080','zh','请不要重复下单!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20081','zh','消费退款不正确!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20082','zh','消费撤销不正确!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20083','zh','理财撤销不正确!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20084','zh','缴费撤销不正确!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20085','zh','撤单失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20086','zh','余额不足，请充值后再试!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime)
values ('CSH20087','zh','转账不能使用优惠!',now(),now());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20088','zh','转账不能使用优惠!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20089','zh','该订单已经被其他用户锁定!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20090','zh','退款退回账户失败!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20091','zh','营销撤销失败!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20100','zh','使用优惠失败!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20101','zh','使用优惠失败!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20103','zh','验证支付密码失败!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20104','zh','对不起,账户状态异常,该账户已被列入黑名单!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20105','zh',' 申请金额不能大于可退金额!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20106','zh',' 金额格式有误!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20107','zh',' 订单状态不允许关闭!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('CSH20108','zh',' 该交易类型不允许关闭!',NOW(),NOW());