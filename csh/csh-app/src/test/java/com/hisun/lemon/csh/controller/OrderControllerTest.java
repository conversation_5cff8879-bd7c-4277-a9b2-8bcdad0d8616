package com.hisun.lemon.csh.controller;

import java.math.BigDecimal;

import com.hisun.lemon.csh.dto.payment.PpPaymentDTO;
import org.junit.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.hisun.lemon.csh.dto.cashier.InitCashierDTO;

import junit.framework.Assert;

//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest
public class OrderControllerTest {

	@Autowired
	private WebApplicationContext context;

//	@Autowired
//	private OrderTransactionalService orderTransactionalService;

	private MockMvc mockMvc;

	@Before
	public void setupMockMvc() throws Exception {
		mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
	}

//	@Test
	public void test() throws Exception {
		testOrderCashier();
		testPp();
		testPpReslut();
		testBackstage();

/*		Map queryMap=new HashMap<>();
*//*        if(limitCount>0){
            queryMap.put("count",limitCount);
        }*//*
		queryMap.put("nowTm", DateTimeUtils.getCurrentLocalDateTime().minusDays(1));
		List<OrderDO> list =orderTransactionalService.queryExpList(queryMap);
		System.out.println(list.size());*/

	}

	/***
	 * 测试创建收银台接口
	 * @throws Exception
	 */
	public void testOrderCashier() throws Exception {
		InitCashierDTO initCashierDTO = new InitCashierDTO();
		initCashierDTO.setExtOrderNo("100200AA");
		initCashierDTO.setOrderAmt( BigDecimal.valueOf(12.78) );
		initCashierDTO.setBusType("0201");
		initCashierDTO.setPayeeId("00125A");
		initCashierDTO.setPayeeName("JUNIT_TEST_MERCHANT");
		initCashierDTO.setOrderCcy("1");
		initCashierDTO.setSysChannel("6");
		initCashierDTO.setTxType("02");
		initCashierDTO.setPayerId("12");

		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		java.lang.String requestJson = ow.writeValueAsString(initCashierDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/chs/order/cashier")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();

		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	/***
	 * 测试收银台第三方页面支付接口
	 * @throws Exception
	 */
	public void testPp() throws Exception {
		PpPaymentDTO ppPaymentDTO=new PpPaymentDTO();

		ppPaymentDTO.setOrderNo("20170710000000000002001");
		ppPaymentDTO.setOrderCcy("01");
		ppPaymentDTO.setCrdPayAmt(BigDecimal.valueOf(12.78));
		ppPaymentDTO.setCouponAmt(new BigDecimal(0));
		ppPaymentDTO.setCouponType("");
		ppPaymentDTO.setPayPassword("1230102");
		ppPaymentDTO.setCrdPayType(13);
		ppPaymentDTO.setCapCorgNo("2231");
		ppPaymentDTO.setPayeeId("00125A");

		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		java.lang.String requestJson = ow.writeValueAsString(ppPaymentDTO);
		RequestBuilder request = MockMvcRequestBuilders.post("/csh/order/pp")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();

		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	public void testPpReslut() throws Exception {
		PpPaymentDTO ppPaymentDTO=new PpPaymentDTO();

		ppPaymentDTO.setOrderNo("20170710000000000002001");
		ppPaymentDTO.setOrderCcy("01");
		ppPaymentDTO.setCrdPayAmt(BigDecimal.valueOf(12.78).setScale(2, BigDecimal.ROUND_HALF_UP));
		ppPaymentDTO.setCouponAmt(new BigDecimal(0));
		ppPaymentDTO.setCouponType("");
		ppPaymentDTO.setPayPassword("1230102");
		ppPaymentDTO.setCrdPayType(13);
		ppPaymentDTO.setCapCorgNo("2231");
		ppPaymentDTO.setPayeeId("00125A");

		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
		java.lang.String requestJson = ow.writeValueAsString(ppPaymentDTO);
		RequestBuilder request = MockMvcRequestBuilders.patch("/csh/order/pp/result")
				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
		MvcResult mvcResult = mockMvc.perform(request).andReturn();
		int status = mvcResult.getResponse().getStatus();
		String content = mvcResult.getResponse().getContentAsString();

		Assert.assertTrue("正确", status == 200);
		Assert.assertFalse("错误", status != 200);

	}

	public void testBackstage() throws Exception {
//
//		BackstagePaymentDTO bsdto=new BackstagePaymentDTO();
//		bsdto.setAppCnl("PWM");
//		bsdto.setCrdPayType(0);
//		bsdto.setCouponType("01");
//		bsdto.setBalAmt(BigDecimal.valueOf(178.09));
//		bsdto.setBusType("0101");
//		bsdto.setCouponAmt(BigDecimal.valueOf(11.00));
//		bsdto.setExtOrderNo("20130202301001101");
//		bsdto.setFee(BigDecimal.valueOf(1.02));
//		bsdto.setOrderCcy("USD");
////		bsdto.setInvAmt(BigDecimal.valueOf(0));
//		bsdto.setCrdPayAmt(BigDecimal.valueOf(0));
//		bsdto.setTxType("01");
//		bsdto.setOrderAmt(BigDecimal.valueOf(189.09));
//		bsdto.setSysChannel("APP");
//
//		GenericDTO dto=new GenericDTO();
//		dto.setBody(bsdto);
//		ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//		java.lang.String requestJson = ow.writeValueAsString(dto);
//		RequestBuilder request = MockMvcRequestBuilders.post("/csh/order/backstage")
//				.contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
//		MvcResult mvcResult = mockMvc.perform(request).andReturn();
//		int status = mvcResult.getResponse().getStatus();
//		String content = mvcResult.getResponse().getContentAsString();
//
//		Assert.assertTrue("正确", status == 200);
//	    Assert.assertFalse("错误", status != 200);

	}

}
