package com.hisun.lemon.csh.service.impl;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dto.cashier.CashierViewDTO;
import com.hisun.lemon.csh.dto.cashier.QuotaDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.csh.enums.AcItem;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.CouponType;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class InvOrderServiceImpl extends AbstractOrderService {
	private static final Logger logger = LoggerFactory.getLogger(InvOrderServiceImpl.class);

	protected String txType= TradeType.FINANC.getType();
	protected String[] busTypes=new String[]{BussinessType.FINANC_IN.getValue(),BussinessType.FINANC_OUT.getValue()};


	public boolean match(String txType){
		return JudgeUtils.equals(this.txType,txType);
	}

	@Override
	protected void checkBeforeInitOrder(OrderDO orderDO){

		logger.debug("-------------初始化订单业务检查-------------。订单号 ["+orderDO.getOrderNo()+"]");
		String payerId= Optional.ofNullable(orderDO.getPayerId()).orElse(LemonUtils.getUserId());
        riskUserStatus(orderDO.getTxType(),payerId,"01");
		logger.debug("-------------初始化订单业务检查完成----------。订单号 ["+orderDO.getOrderNo()+"]");
	}

	protected void  caculateFee(OrderDO orderDO){
		logger.debug("-------------开始计算用户手续费-------------。订单号["+orderDO.getOrderNo()+"]");
        super.caculateFee(orderDO);
		logger.debug("-------------计算用户手续费完成-------------。订单号["+orderDO.getOrderNo()+"]");
	}

	protected  CashierViewDTO createCashierView(OrderDO orderDO){
		String payTypes=orderDO.getPayType();
		String userId= LemonUtils.getUserId();

		if(JudgeUtils.isNull(userId) || JudgeUtils.isBlank(userId)) {
			userId = orderDO.getPayerId();
		}


		CashierViewDTO cashierViewDTO=new CashierViewDTO();
		//支持账户余额
		if(paytypeService.supportBal(payTypes)){
			BigDecimal bal=acmComponent.getAccountBal(userId, CapTypEnum.CAP_TYP_CASH.getCapTyp());
			cashierViewDTO.setBalAmt(bal);
		}else{
			cashierViewDTO.setBalAmt(null);
		}
		cashierViewDTO.sethCouponCashAmt(BigDecimal.valueOf(0));
		cashierViewDTO.sethCouponUsedAmt(BigDecimal.valueOf(0));

		cashierViewDTO.sethCouponAmt(BigDecimal.ZERO);
		//数量
		cashierViewDTO.sethCouponUsedAmt(BigDecimal.ZERO);
		//金额
		cashierViewDTO.sethCouponCashAmt(BigDecimal.ZERO);
		cashierViewDTO.seteCoupons(null);
		cashierViewDTO.setDiscountCoupons(null);
		cashierViewDTO.setOrderNo(orderDO.getOrderNo());
		cashierViewDTO.setQpDCards(null);
		cashierViewDTO.setQpCCards(null);
		cashierViewDTO.setPayTypes(payTypes);
		cashierViewDTO.setOrderAmt(orderDO.getOrderAmt());
		cashierViewDTO.setFeeAmt(orderDO.getFee());
		cashierViewDTO.setGoodsDesc(orderDO.getGoodsInfo());
		cashierViewDTO.setBusType(orderDO.getBusType());

		QuotaDTO quota=new QuotaDTO();
		quota.setCouponNo(null);
		quota.setCouponAmt(BigDecimal.ZERO);
		quota.setPayAmt(orderDO.getTotalAmt());
		quota.setType(CouponType.NONE.getType());
		cashierViewDTO.setQuota(quota);
		return cashierViewDTO;
	}

	@Override
	protected void checkJrnWithBussiness(GenericDTO inputGenericDto){
	   // Do nothing because of no need.
	}


	//创建账务处理列表,具体业务实现
	protected List<AccountingReqDTO> createAccQueue(OrderDO orderDO,PayJrnDO payJrnDO){
		String txType=orderDO.getTxType();
		String balCapType= CapTypEnum.CAP_TYP_CASH.getCapTyp();
		String balAcNo=acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType);

		if(JudgeUtils.equals(txType,this.txType)){
			List<AccountingReqDTO> accountingReqDtos=new ArrayList<>();

			//借：其他应付款-支付账户-现金账户
			AccountingReqDTO cshItemReqDTO=acmComponent.createAccountingReqDTO(
					orderDO.getOrderNo(),
					payJrnDO.getPayJrnNo(),
					orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL,
					payJrnDO.getOrderAmt(),
					balAcNo,
					ACMConstants.USER_AC_TYP,
					balCapType,
					ACMConstants.AC_D_FLG,
					AcItem.O_BAL.getValue(),
					null,
					null,
					null,
					null,
					"理财转入$"+orderDO.getOrderAmt());

			//贷：其他应付款-暂收-收银台
			AccountingReqDTO incrementAccReqDTO=acmComponent.createAccountingReqDTO(
					orderDO.getOrderNo(),
					payJrnDO.getPayJrnNo(),
					orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL,
					payJrnDO.getOrderAmt(),
					null,
					ACMConstants.ITM_AC_TYP,
					balCapType,
					ACMConstants.AC_C_FLG,
					AcItem.O_CSH.getValue(),
					balAcNo,
					null,
					null,
					null,
					null);

			accountingReqDtos.add(cshItemReqDTO);
			accountingReqDtos.add(incrementAccReqDTO);
			return accountingReqDtos;
		}
		return Collections.emptyList();
	}

	@Override
	protected void notifyBussiness(OrderDO orderDO,PayJrnDO payJrn){
		logger.debug("-------------订单[{}]成功，不需要通知理财模块-------------",orderDO.getOrderNo());
	}

}
