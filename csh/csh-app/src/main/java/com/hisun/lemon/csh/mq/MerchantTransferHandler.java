package com.hisun.lemon.csh.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.framework.stream.MultiOutput;
import com.hisun.lemon.framework.stream.producer.Producer;
import com.hisun.lemon.framework.stream.producer.Producers;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.tfm.dto.TradeFeeReqDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 登记商户转账服务费(不清分)
 * <AUTHOR>
 * @date 2017年11月18日
 * @time 下午2:54:28
 * @desc
 */
@Component
public class MerchantTransferHandler {
    protected static final Logger logger = LoggerFactory.getLogger(MerchantTransferHandler.class);

    @Resource
    ObjectMapper objectMapper;
    /**
     * 登记商户服务费
     */
    @Producers({
         @Producer(beanName= "merchantTransferTradeFeeConsumer", channelName= MultiOutput.OUTPUT_THREE)
    })
    public TradeFeeReqDTO registMerChantTransferFee(OrderDO orderDO){
        //商户转账
        if(JudgeUtils.equals(orderDO.getBusType(), BussinessType.TRANSFER_M_BAL.getValue())){
            TradeFeeReqDTO tradeFeeReqDTO=new TradeFeeReqDTO();
            tradeFeeReqDTO.setCcy(orderDO.getCcy());
            tradeFeeReqDTO.setUserId(orderDO.getPayerId());
            tradeFeeReqDTO.setBusOrderNo(orderDO.getOrderNo());
            tradeFeeReqDTO.setBusOrderTime(DateTimeUtils.getCurrentLocalDateTime());
            tradeFeeReqDTO.setTradeAmt(orderDO.getOrderAmt());
            tradeFeeReqDTO.setBusType(orderDO.getBusType());
            String data = ObjectMapperHelper.writeValueAsString(objectMapper, tradeFeeReqDTO, true);
            logger.info("登记商户转账手续费写入消息队列数据：" + data);
            return tradeFeeReqDTO;
        }
        logger.info("非商户转账订单，不登记商户手续费：" + orderDO.getOrderNo());
        return null;
    }

}
