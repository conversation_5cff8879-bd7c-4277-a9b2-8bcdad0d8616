package com.hisun.lemon.csh.controller;

import javax.annotation.Resource;

import com.hisun.lemon.csh.dto.paytype.AppPaytypeResultDTO;
import com.hisun.lemon.csh.dto.paytype.PaytypeDTO;
import com.hisun.lemon.framework.controller.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.entity.PaytypeDO;
import com.hisun.lemon.csh.service.IPaytypeService;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;


@Api(value="管理收银台支付方式")
@RestController
@RequestMapping(value="/csh/paytype")
public class PayTypeController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(PayTypeController.class);
	 
    @Resource
    IPaytypeService service;
    
	@ApiOperation(value="新增支付方式配置", notes="新配置一条支付方式") 
	@ApiResponse(code = 200, message = " ")
    @PostMapping(value = "/configure")
    public GenericRspDTO<NoBody> add(@Validated @RequestBody GenericDTO<PaytypeDTO> paytypeDTO) {
		service.add(paytypeDTO.getBody());
        return GenericRspDTO.newSuccessInstance();
    }

	@ApiOperation(value="修改支付方式配置", notes="修改一条支付方式配置") 
	@ApiResponse(code = 200, message = " ")
    @PatchMapping(value = "/configure")
    public GenericRspDTO<NoBody> update(@Validated @RequestBody GenericDTO<PaytypeDTO> paytypeDTO) {
		service.update(paytypeDTO.getBody());
        return GenericRspDTO.newSuccessInstance();
    } 
	
	@ApiOperation(value="禁用支付方式配置", notes="禁用一条支付方式配置") 
	@ApiResponse(code = 200, message = " ")
    @DeleteMapping(value = "/{id}")
    public GenericRspDTO<NoBody> delete(@PathVariable Integer id) {
        logger.info("逻辑删除一条支付方式配置："+id);
		service.del(id);
        return GenericRspDTO.newSuccessInstance();
    } 
	
	@ApiOperation(value="启用支付方式配置", notes="启用一条支付方式配置") 
	@ApiResponse(code = 200, message = " ")
    @PatchMapping(value = "/invocation")
    public GenericRspDTO<NoBody> resume(@Validated @RequestBody GenericDTO<PaytypeDTO> paytypeDTO) {
		PaytypeDTO body = paytypeDTO.getBody();
		body.setStatus(CshConstants.YES);
		service.update(body);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value="查询用户可以使用的支付方式", notes="查询用户可以使用的支付方式，包括余额、理财、银行卡")
    @ApiResponse(code = 200, message = " ")
    @ApiImplicitParams({
		@ApiImplicitParam(name = "appCnl", value = "应用渠道", required = true, paramType = "path", dataType = "String"),
		@ApiImplicitParam(name = "busType", value = "业务类型", required = true, paramType = "path", dataType = "String"),
		@ApiImplicitParam(name = "mercId", value = "商户编号", required = true, paramType = "path", dataType = "String"),
		@ApiImplicitParam(name = "busPayType", value = "应用指定支付方式", required = true, paramType = "path", dataType = "String")
		})
    @GetMapping(value = "/enables/{appCnl}/{busType}/{mercId}/{busPayType}")
    public GenericRspDTO<AppPaytypeResultDTO> getUserEnables(GenericDTO sysDto,@PathVariable("appCnl") String appCnl,
           @PathVariable("busType") String busType,@PathVariable("mercId") String mercId,@PathVariable("busPayType") String busPayType) {

    	String queryMercId=mercId;
        //没有指定商户，不限定商户
        if(StringUtils.equals("none",mercId)){
        	queryMercId=CshConstants.MATCH_ALL;
        }

        String queryBusPayType=busPayType;
        //没有指定支付方式，不限定支付方式
        if(StringUtils.equals("none",busPayType)){
        	queryBusPayType=null;
        }

        PaytypeDO paytypeDO =new PaytypeDO();

        paytypeDO.setAppCnl(appCnl);
        paytypeDO.setMercId(queryMercId);
        paytypeDO.setBusType(queryBusPayType);

        AppPaytypeResultDTO appPaytypeResultDTO=service.getPayInfo(paytypeDO, busPayType);
        return GenericRspDTO.newSuccessInstance(appPaytypeResultDTO);
    }
}
