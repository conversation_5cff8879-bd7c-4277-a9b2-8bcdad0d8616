package com.hisun.lemon.csh.aop;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csh.ctx.OrderContextHolder;
import com.hisun.lemon.framework.data.GenericDTO;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @date 2017年6月28日
 * @time 下午5:11:27
 *
 */
@Aspect
@Configuration("cshControllerAspect")
@Order(value=Ordered.HIGHEST_PRECEDENCE)
public class ControllerAspect {
    private static final Logger logger = LoggerFactory.getLogger(ControllerAspect.class);

    
    @Pointcut("execution (* com.hisun..csh..Controller.*(..))")
    public void anyControllerMethod(){}
    
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController)")
    public void anyControllerMethodRest(){}
    
    @Around("anyControllerMethod()||anyControllerMethodRest()")
    public Object doAroundController(ProceedingJoinPoint pjp) throws Throwable {
        Object result = null;             //返回对象
        try {
            beforeProceed(pjp);
            result = pjp.proceed();
        } catch(Exception e) {
            throw e;
        } finally {
            afterProceed(pjp, result);
            OrderContextHolder.clear();
        }
        return result;
    }
    
    /**
     * controller 前处理
     * @param pjp
     */
    private void beforeProceed(ProceedingJoinPoint pjp) {
        List<GenericDTO<?>> requestGenericDTOs = Optional.ofNullable(pjp.getArgs()).filter(JudgeUtils::isNotEmpty).map(ControllerAspect.this::fetchGenericDTOFromArgs).orElse(null);

        if(JudgeUtils.isNotNull(requestGenericDTOs)&&JudgeUtils.isNotEmpty(requestGenericDTOs)){
            GenericDTO<?> genericDTO = requestGenericDTOs.get(0);
            if(JudgeUtils.isNotNull(genericDTO.getBody())){
                OrderContextHolder.bindRequestDto(genericDTO.getBody());
            }
        }
    }
    

    /**
     * Controller 后处理
     * @param pjp
     * @param resObject
     */
    private void afterProceed(ProceedingJoinPoint pjp, Object resObject) {

    }

    private List<GenericDTO<?>> fetchGenericDTOFromArgs(Object[] args){
        return Stream.of(args).filter(o -> o instanceof GenericDTO).map(m -> (GenericDTO<?>)m).collect(Collectors.toList());
    }
}
