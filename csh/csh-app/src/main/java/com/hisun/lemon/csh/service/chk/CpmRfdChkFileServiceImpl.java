package com.hisun.lemon.csh.service.chk;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service
public class CpmRfdChkFileServiceImpl extends AbstractChkFileService {

    public CpmRfdChkFileServiceImpl() {
        super();
        this.appCnl="CPM";
        this.chkOrderStatus=new String[]{"R3","C"};
        this.chkTxTypes=new String[]{"0605"};
        this.lockName="CSH_CPM_RFD_CHK_FILE_LOCK";
        this.refund=true;
    }
}
