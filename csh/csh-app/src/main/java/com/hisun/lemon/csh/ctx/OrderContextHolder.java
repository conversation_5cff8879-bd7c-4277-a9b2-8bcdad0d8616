package com.hisun.lemon.csh.ctx;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.ReflectionUtils;
import com.hisun.lemon.csh.entity.OrderDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.NamedThreadLocal;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 收银订单执行上下文<br/>
 * 需要手工设置和更新。线程结束的时候一定要在finally调用clear方法清除，否则有内存泄漏的可能
 * <AUTHOR>
 *
 */
public class OrderContextHolder {
	private static final Logger logger = LoggerFactory.getLogger(OrderContextHolder.class);

	/**
	 * 在线程执行过程中，保持最新的订单数据。如果订单数据有修改，及时put。<br/>
	 * 在同步账单，账务等环节调用外部接口等时候，使用该对象的最新数据，免除复杂的逻辑判断和手动set设值。
	 */
	private static final ThreadLocal<OrderDO> orderHolder = new NamedThreadLocal<OrderDO>("CshOrderHolder"){
		protected OrderDO initialValue() {
			return new OrderDO();
		}
	};

	/**
	 *  线程执行过程中需要保留的数据（订单对象以外的数据，但在整个流程中跨方法使用）。
	 */
	private static final ThreadLocal<CshExtContext<Object, Object>> contextHolder = new ThreadLocal<CshExtContext<Object, Object>>(){
		protected CshExtContext<Object,Object> initialValue() {
			return new CshExtContext<>();
		}
	};


	public static OrderDO getOrderContext() {
		return orderHolder.get();
	}

	public static void setOrderContext(OrderDO orderData) {
		orderHolder.set(orderData);
		if(logger.isDebugEnabled()) {
			logger.debug("set order data {}",orderData);
		}
	}

	public static void clear() {
		orderHolder.remove();
		contextHolder.remove();
	}

	public static void putOrderContext(String key, Object value) {
		String methodName = "set"+key.substring(0, 1).toUpperCase()+key.substring(1);
		Method method= ReflectionUtils.getAccessibleMethodByName(OrderDO.class, methodName);
		ReflectionUtils.invokeMethod(method,getOrderContext(),value);
	}

	public static void putOrderContext(OrderDO appendCtx){
		Field[] fields=ReflectionUtils.getDeclaredFields(OrderDO.class);
		for(Field field:fields){
			Object v=ReflectionUtils.getField(field,appendCtx);
			if(JudgeUtils.isNotNull(v)){
				 ReflectionUtils.setField(field,getOrderContext(),v);
			}
		}
	}

	public static CshExtContext<Object, Object> getExtContext() {
		return contextHolder.get();
	}

	public static void setExtContext(CshExtContext<Object, Object> extContext) {
		contextHolder.set(extContext);
	}

	public static void putExtContext(Object k,Object v){
		contextHolder.get().put(k,v);
	}

	public static Object getExtContext(Object k){
		return getExtContext().get(k);
	}


	public static Object getPayService(){
		return getExtContext(PAY_SERVICE);
	}

	public static Object getRequestDto(){
		return getExtContext(THREAD_REQUEST_DTO);
	}

	public static void bindRequestDto(Object dto){
		putExtContext(THREAD_REQUEST_DTO,dto);
	}

	public static void setPayService(Object service){
		putExtContext(PAY_SERVICE,service);
	}

	/**
	 * 特殊场景的额外绑定数据，自由发挥绑定
	 * @param <K>
	 * @param <V>
	 */
	public static class CshExtContext<K, V> extends ConcurrentHashMap<K, V> {
		private static final long serialVersionUID = 3729826264846718792L;

	}


	public static final String THREAD_REQUEST_DTO="requestDto";    //请求DTO
	public static final String PAY_SERVICE="payService";     //请求支付service


	public static void main(String[] args){
		OrderDO ctx= OrderContextHolder.getOrderContext();
		OrderContextHolder.putOrderContext("orderNo", "00001");
		OrderDO ctx2= OrderContextHolder.getOrderContext();

		OrderDO o=new OrderDO();
		o.setCouponNo("0001000");
		o.setOrderNo("00012222");
		o.setAcTm(DateTimeUtils.getCurrentLocalDate());
		OrderContextHolder.putOrderContext(o);
	}
}
