package com.hisun.lemon.csh.service.impl;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.csh.component.OrderCommonComponent;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dao.IOrderDao;
import com.hisun.lemon.csh.dao.IPayJrnDao;
import com.hisun.lemon.csh.dao.ITransferOrderDao;
import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.entity.TransferOrderDO;
import com.hisun.lemon.csh.enums.AcItem;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.OrderStatus;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.tam.constants.CcyAcItemEnum;
import com.hisun.lemon.tfm.client.TfmServerClient;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.dto.payment.AbstractNormalPaymentDTO;
import com.hisun.lemon.csh.dto.payment.BalPaymentDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.tam.client.TransferOrderClient;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tam.dto.ResultTransferOrderDTO;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TamOrderServiceImpl extends AbstractOrderService {
	private static final Logger logger = LoggerFactory.getLogger(TamOrderServiceImpl.class);

	protected String txType= TradeType.TRANSFER.getType();
	protected String[] busTypes=new String[]{
			BussinessType.TRANSFER_BAL.getValue(),
			BussinessType.TRANSFER_CARD.getValue(),
			BussinessType.TRANSFER_FACE.getValue()
	};

	@Resource
	private TransferOrderClient transferClient;

	@Resource
	protected OrderCommonComponent orderCommonComponent;

	@Resource
	TfmServerClient tfmServerClient;

	@Resource
	private IOrderDao orderDao;

	@Resource
	private ITransferOrderDao transferDao;

	@Resource
	private IPayJrnDao payJrnDao;

	public boolean match(String txType){
		return JudgeUtils.equals(this.txType,txType);
	}

	/**
	 * 商户转账待结算账务处理
	 * @param orderDO
	 * @param payJrnDO
	 */
	public List<AccountingReqDTO> handleSettleAccount(OrderDO orderDO, PayJrnDO payJrnDO){
		String balCapType = null;
		String balAcNo = null;
		String busType = orderDO.getBusType();
		List<AccountingReqDTO> accList = new ArrayList<>();

		if(JudgeUtils.isNotNull(busType) && JudgeUtils.equals(busType,BussinessType.TRANSFER_M_BAL.getValue())){
			//商户待结算账户
			BigDecimal settleAmt = orderDO.getOrderAmt();
			balAcNo = acmComponent.getAcmAcNo(orderDO.getPayerId(), CapTypEnum.CAP_TYP_SETTLEMENT.getCapTyp());
			balCapType = CapTypEnum.CAP_TYP_SETTLEMENT.getCapTyp();
			MerchantFeeCalculateRspDTO tradeFeeRspDTO = orderCommonComponent.caculateMerchantTradeFee(orderDO);
			BigDecimal merchantTradeFee = tradeFeeRspDTO.getTradeFee();
			String feeFlag = tradeFeeRspDTO.getCalculateMode();
			if(JudgeUtils.equals(feeFlag,"internal")){
				settleAmt = settleAmt.subtract(merchantTradeFee);
			}else if(JudgeUtils.equals(feeFlag,"external")){
				settleAmt = settleAmt.add(merchantTradeFee);
			}
			//实际支付金额校验
			if(settleAmt.compareTo(orderDO.getBalAmt()) != 0){
				LemonException.throwBusinessException("CSH20001");
			}

			//商户转账用户账务处理
			// 借：其他应付款-支付账户-商户结算账户 102
			AccountingReqDTO cshItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
					payJrnDO.getPayJrnNo(), orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, settleAmt, balAcNo,
					ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_D_FLG, AcItem.O_BAL.getValue(), null, null,
					null, null, "商户"+orderDO.getPayerId()+"转账使用余额支付");
			accList.add(cshItemReqDTO);
			// 贷：其他应付款-暂收-收银台 102
			AccountingReqDTO userAccountReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
					payJrnDO.getPayJrnNo(), orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, settleAmt, null,
					ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,AcItem.O_CSH.getValue(), balAcNo, null,
					null, null,"商户"+orderDO.getPayerId()+"转账使用余额支付");
			accList.add(userAccountReqDTO);

			GenericDTO<List<AccountingReqDTO>> userAccDto = new GenericDTO<>();
			userAccDto.setBody(accList);
			GenericRspDTO<NoBody> accountingTreatment = accountingTreatmentClient.accountingTreatment(userAccDto);
			if(!JudgeUtils.isSuccess(accountingTreatment.getMsgCd())){
				logger.error("订单"+orderDO.getOrderNo()+"账务失败："+accountingTreatment.getMsgCd());
				LemonException.throwBusinessException(accountingTreatment.getMsgCd());
			}
		}
		return accList;
	}

	@Override
	protected void checkBeforeInitOrder(OrderDO orderDO) {
		logger.debug("-------------初始化订单业务检查-------------。订单号 [" + orderDO.getOrderNo() + "]");
		String payerId= Optional.ofNullable(orderDO.getPayerId()).orElse(LemonUtils.getUserId());
		riskUserStatus(orderDO.getTxType(),payerId,"01");
		if(StringUtils.equals(orderDO.getBusType(),"01") ||StringUtils.equals(orderDO.getBusType(),"03")){
			riskUserStatus(orderDO.getTxType(),orderDO.getPayeeId(),"01");
		}
		logger.debug("-------------初始化订单业务检查完成----------。订单号 [" + orderDO.getOrderNo() + "]");
	}

	protected void caculateFee(OrderDO orderDO) {
		logger.debug("-------------开始计算用户手续费-------------。订单号[" + orderDO.getOrderNo() + "]");
		super.caculateFee(orderDO);
		logger.debug("-------------计算用户手续费完成-------------。订单号[" + orderDO.getOrderNo() + "]");
	}

	@Override
	protected void checkJrnWithBussiness(GenericDTO inputGenericDto) {
		// 转账不能使用优惠
		Object body = inputGenericDto.getBody();
		if (body instanceof AbstractNormalPaymentDTO) {
			AbstractNormalPaymentDTO normalPaymentDTO = (AbstractNormalPaymentDTO) body;
			if (body instanceof BalPaymentDTO) {
				if (!StringUtils.startsWith(normalPaymentDTO.getBusType(), TamConstants.TX_TYPE_TRANSFER)) {
					LemonException.throwBusinessException("CSH20087");
				}
			}

			BigDecimal couponAmt = normalPaymentDTO.getCouponAmt();
			if (JudgeUtils.isNotNull(couponAmt) && couponAmt.compareTo(BigDecimal.valueOf(0)) > 0) {
				LemonException.throwBusinessException("CSH20088");
			}
			if (StringUtils.isNoneBlank(normalPaymentDTO.getCouponNo())) {
				LemonException.throwBusinessException("CSH20088");
			}
		}
	}

	// 创建账务处理列表,具体业务实现
	protected List<AccountingReqDTO> createAccQueue(OrderDO orderDO, PayJrnDO payJrnDO) {
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType, orderDO.getCcy());
		BigDecimal fee = orderDO.getFee();
		BigDecimal amountSum = orderDO.getTotalAmt();
		//根据币种选择收银台科目号
		String itemNo = "";
		for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
			if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), orderDO.getCcy())) {
				itemNo = ccyAcItemEnum.getItemNo();
			}
		}

		List acList = new ArrayList<>();
		//判断使用余额支付还是快捷
		if(payJrnDO.getCrdPayAmt()!=null && payJrnDO.getCrdPayAmt().compareTo(BigDecimal.ZERO)>0){
			BigDecimal tamCrdPayAmt = payJrnDO.getCrdPayAmt();
			//借：应收账款-渠道充值-XX银行/支付宝/微信
			AccountingReqDTO cnlRechargeBnkReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, tamCrdPayAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
					AcItem.I_CNL_BANK.getValue(), null, null, null, null,"转账  使用快捷支付");
			acList.add(cnlRechargeBnkReqDTO);
			//贷：其他应付款-暂收-收银台
			AccountingReqDTO userAccountReqDTO1=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, tamCrdPayAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
					AcItem.O_CSH.getValue(), null, null, null, null, "转账  使用快捷支付");
			acList.add(userAccountReqDTO1);
		}else{
			// 借：其他应付款-支付账户-现金账户 102
			AccountingReqDTO cshItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
					payJrnDO.getPayJrnNo(), orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo,
					ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_D_FLG, AcItem.O_BAL.getValue(), null, null,
					null, null, "转账  使用余额支付");
			acList.add(cshItemReqDTO);
			// 贷：其他应付款-暂收-收银台 102
			AccountingReqDTO userAccountReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
					payJrnDO.getPayJrnNo(), orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, null,
					ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,itemNo, balAcNo, null,
					null, null,"转账  使用余额支付");
			acList.add(userAccountReqDTO);
			}
		
		return acList;
	}

	@Override
	protected void notifyBussiness(OrderDO orderDO, PayJrnDO payJrn) {
		logger.debug("-------------订单成功，通知转账模块-------------。订单号[" + orderDO.getOrderNo() + "]");

		ResultTransferOrderDTO resultTransfer = new ResultTransferOrderDTO();
		resultTransfer.setOrderSts(orderDO.getOrderStatus());
		resultTransfer.setOrderNo(orderDO.getBusOrderNo());
		resultTransfer.setTxType(orderDO.getTxType());
		resultTransfer.setBusType(orderDO.getBusType());
		resultTransfer.setAmount(orderDO.getOrderAmt());
		GenericDTO<ResultTransferOrderDTO> transferDTO = new GenericDTO<ResultTransferOrderDTO>();
		transferDTO.setBody(resultTransfer);
		GenericRspDTO req = transferClient.userTransferOrderResult(transferDTO);
		if (!"TAM00000".equals(req.getMsgCd())) {
			throw new LemonException(req.getMsgCd());
		}

		logger.debug("------------订单成功，通知转账模块完成----------。订单号[" + orderDO.getOrderNo() + "]");

	}

}
