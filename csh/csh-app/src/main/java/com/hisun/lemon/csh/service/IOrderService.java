package com.hisun.lemon.csh.service;

import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.BackstageViewDTO;
import com.hisun.lemon.csh.dto.cashier.CashierViewDTO;
import com.hisun.lemon.csh.dto.cashier.InitBackstageDTO;
import com.hisun.lemon.csh.dto.cashier.InitCashierDTO;
import com.hisun.lemon.csh.dto.order.ExchangeCoinDTO;
import com.hisun.lemon.csh.dto.order.NotifyResultDTO;
import com.hisun.lemon.csh.dto.payment.*;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.framework.data.GenericDTO;


/**
 * <AUTHOR>
 * @date 2017年6月7日
 * @time 下午2:13:58
 *
 */
public interface IOrderService {
    /**
     * 创建收银订单，返回收银台数据
     * @param initCashierDTO
     * @return
     */
    public CashierViewDTO createCashier(GenericDTO<InitCashierDTO> initCashierDTO);


    /**
     * 生成支付流水，并使用余额+优惠支付
     * @param orderDTO
     */
    public PaymentResultDTO createJrnAndBalPay(GenericDTO<BalPaymentDTO> orderDTO);


    /**
     * 生成支付流水，并使用快捷+优惠支付
     * @param orderDTO
     */
    public PaymentResultDTO createJrnAndQpPay(GenericDTO<QpPaymentDTO> orderDTO);


    /**
     * 生成支付流水，并使用页面+优惠支付
     * @param orderDTO
     */
    public PaymentResultDTO createJrnAndPagePay(GenericDTO<PpPaymentDTO> orderDTO);

    /**
     * 处理资金能力的回调通知
     * @param notifyResultDTO
     */
    public OrderDO handlePpNotice(GenericDTO<NotifyResultDTO> notifyResultDTO);

    /**
     * 处理后台无界面直接支付的情况
     * @param paymentDTO
     * @return
     */
    public BackstageViewDTO backstagePay(GenericDTO<InitBackstageDTO> paymentDTO);

    /**
     * 线下支付（非网银、快捷）
     * @param genericDTO
     * @return
     */
    public OfflinePaymentResultDTO offlinePayment(GenericDTO<OfflinePaymentDTO> genericDTO);
    
    /**
     * 修改订单状态
     * @param orderDO
     * @return
     */
    public void updateOrder(OrderDO orderDO);


    /**
     * 支付短款撤单
     * @param orderNo
     */
    public void shortErrCancel(String orderNo);
    
    /**
     * 手续费查询
     * @param orderNo
     */
   // public TradeFeeRspDTO tradeFee(GenericDTO<TradeFeeReqDTO> genericDTO);

    /**
     * Acleda银行快捷支付
     * @param genericDTO
     * @return
     */
    public AcledaPaymentResultDTO createOrderJrnAndAcledaQpPay(GenericDTO<AcledaQpPaymentDTO> genericDTO);

    /**
     * Acleda银行转账
     * @param genericDTO
     * @return
     */
    public AcledaTransferRspDTO createAcledaOrder(GenericDTO<AcledaTransferReqDTO> genericDTO);

    /**
     * 兑换订单预处理
     * @param req
     * @return
     */
    public String preCheckExchangeOrder(GenericDTO<ExchangeCoinDTO> req);

    public void completeExchangeOrder(GenericDTO<ExchangeCoinDTO> req);
}
