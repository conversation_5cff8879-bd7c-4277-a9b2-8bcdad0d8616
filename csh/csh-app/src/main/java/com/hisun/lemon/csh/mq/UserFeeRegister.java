package com.hisun.lemon.csh.mq;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.tfm.client.TfmServerClient;
import com.hisun.lemon.tfm.dto.TradeFeeReqDTO;
import com.hisun.lemon.tfm.dto.TradeFeeRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2017/8/16
 * @time 19:57
 */
@Component(UserFeeRegister.BEAN_NAME)
public class UserFeeRegister implements MessageHandler<TradeFeeReqDTO> {
    public static final String BEAN_NAME="userFeeRegister";
    private static final Logger logger = LoggerFactory.getLogger(UserFeeRegister.class);

    @Resource
    private TfmServerClient tfmServerClient;

    @Override
    public void onMessageReceive(GenericCmdDTO<TradeFeeReqDTO> genericCmdDTO) {
        logger.info("接收登记用户手续费数据 {}", genericCmdDTO.getBody());
        TradeFeeReqDTO tradeFeeReqDTO =  genericCmdDTO.getBody();
        if(JudgeUtils.isNotNull(tradeFeeReqDTO)){
            GenericDTO<TradeFeeReqDTO> reqDto=new GenericDTO();
            reqDto.setBody(tradeFeeReqDTO);
            GenericRspDTO<TradeFeeRspDTO> rspDTO=tfmServerClient.tradeFee(reqDto);
            if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
                logger.error("订单[" + tradeFeeReqDTO.getBusOrderNo() + "] 登记用户服务费失败，" + rspDTO.getMsgCd());
                LemonException.throwBusinessException(rspDTO.getMsgCd());
            }
        }
    }
}

