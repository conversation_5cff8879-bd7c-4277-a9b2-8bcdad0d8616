package com.hisun.lemon.csh.service.impl;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;

import com.hisun.lemon.csh.enums.AcItem;
import com.hisun.lemon.csh.enums.BankAcItemEnum;
import com.hisun.lemon.csh.enums.CouponType;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.framework.i18n.LocaleMessageSource;
import com.hisun.lemon.tam.constants.CcyAcItemEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dto.cashier.CashierViewDTO;
import com.hisun.lemon.csh.dto.cashier.QpCardInfoDTO;
import com.hisun.lemon.csh.dto.cashier.QuotaDTO;
import com.hisun.lemon.csh.dto.payment.AbstractNormalPaymentDTO;
import com.hisun.lemon.csh.dto.payment.BalPaymentDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.pwm.client.PwmRechargeClient;
import com.hisun.lemon.pwm.constants.PwmConstants;
import com.hisun.lemon.pwm.dto.RechargeHCouponResultDTO;
import com.hisun.lemon.pwm.dto.RechargeResultDTO;

@Service
public class PwmOrderServiceImpl extends AbstractOrderService {
	private static final Logger logger = LoggerFactory.getLogger(PwmOrderServiceImpl.class);

	@Resource
	private PwmRechargeClient pwmRechargeClient;
	@Resource
	LocaleMessageSource localeMessageSource;

	public boolean match(String txType){
		return JudgeUtils.equalsAny(txType, TradeType.RECHARGE.getType(),TradeType.RECHARGE_H.getType());
	}

	@Override
	protected void checkBeforeInitOrder(OrderDO orderDO){

		logger.debug("-------------初始化订单业务检查-------------。订单号 ["+orderDO.getOrderNo()+"]");
		if(StringUtils.isBlank(orderDO.getPayeeId())){
			LemonException.throwBusinessException("CSH20004");
		}
		riskUserStatus(orderDO.getTxType(),orderDO.getPayeeId(),"01");

		logger.debug("-------------初始化订单业务检查完成----------。订单号 ["+orderDO.getOrderNo()+"]");
	}

	protected void  caculateFee(OrderDO orderDO){
		logger.debug("-------------开始计算用户手续费-------------。订单号["+orderDO.getOrderNo()+"]");
        super.caculateFee(orderDO);
        if(orderDO.getTxType().equals(PwmConstants.TX_TYPE_RECHANGE) && JudgeUtils.equals(orderDO.getFeeFlag(),"IN")){
        	LemonException.throwBusinessException("CSH20102");
		}
		logger.debug("-------------计算用户手续费完成-------------。订单号["+orderDO.getOrderNo()+"]");
	}
	@Override
	protected CashierViewDTO createCashierView(OrderDO orderDO){
		String payTypes=orderDO.getPayType();
		String userId= LemonUtils.getUserId();

		if(JudgeUtils.isNull(userId) || JudgeUtils.isBlank(userId)) {
			userId = orderDO.getPayerId();
		}

		//充值业务，收款方就是当前账户
		if(StringUtils.equals(orderDO.getTxType(),PwmConstants.TX_TYPE_RECHANGE)){
			if(JudgeUtils.isNull(userId) || JudgeUtils.isBlank(userId)) {
				userId = orderDO.getPayeeId();
			}
		}
		if(StringUtils.equals(orderDO.getTxType(),PwmConstants.TX_TYPE_HCOUPON)){
			if(!paytypeService.supportBal(payTypes)){
				LemonException.throwBusinessException("CSH20073");
			}
		}

		CashierViewDTO cashierViewDTO=new CashierViewDTO();
		//支持账户余额
		if(paytypeService.supportBal(payTypes)){
			BigDecimal bal=acmComponent.getAccountBal(userId, CapTypEnum.CAP_TYP_CASH.getCapTyp());
			cashierViewDTO.setBalAmt(bal);

			if(StringUtils.equals(orderDO.getTxType(),PwmConstants.TX_TYPE_HCOUPON)){
				if(orderDO.getTotalAmt().compareTo(bal)>0){
					LemonException.throwBusinessException("CSH20086");
				}
			}
		}else{
			cashierViewDTO.setBalAmt(null);
		}
		cashierViewDTO.sethCouponCashAmt(BigDecimal.valueOf(0));
		cashierViewDTO.sethCouponUsedAmt(BigDecimal.valueOf(0));

		cashierViewDTO.sethCouponAmt(BigDecimal.ZERO);
		//数量
		cashierViewDTO.sethCouponUsedAmt(BigDecimal.ZERO);
		//金额
		cashierViewDTO.sethCouponCashAmt(BigDecimal.ZERO);
		cashierViewDTO.seteCoupons(null);
		cashierViewDTO.setDiscountCoupons(null);
		cashierViewDTO.setOrderNo(orderDO.getOrderNo());

		if(paytypeService.supportDQP(payTypes) || paytypeService.supportCQP(payTypes)){
			Map<String,List<AgrInfoRspDTO.CardAgrInfo>> qpCards=quickPaymentComponent.queryBindCards();
			if(paytypeService.supportDQP(payTypes)){
				List<AgrInfoRspDTO.CardAgrInfo> adQpCards= qpCards.get(quickPaymentComponent.D_CARD);
				List<QpCardInfoDTO> dQpCards = new ArrayList<>();
				for(AgrInfoRspDTO.CardAgrInfo cardInfo : adQpCards) {
					QpCardInfoDTO qpCardInfoDTO = new QpCardInfoDTO();
					BeanUtils.copyProperties(qpCardInfoDTO,cardInfo);
					dQpCards.add(qpCardInfoDTO);
				}
				cashierViewDTO.setQpDCards(dQpCards);
			}

			if(paytypeService.supportCQP(payTypes)){
				List<AgrInfoRspDTO.CardAgrInfo> acQpCards= qpCards.get(quickPaymentComponent.C_CARD);
				List<QpCardInfoDTO> cQpCards = new ArrayList<>();
				for(AgrInfoRspDTO.CardAgrInfo cardInfo : acQpCards) {
					QpCardInfoDTO qpCardInfoDTO = new QpCardInfoDTO();
					BeanUtils.copyProperties(qpCardInfoDTO,cardInfo);
					cQpCards.add(qpCardInfoDTO);
				}
				cashierViewDTO.setQpCCards(cQpCards);
			}
		}

		cashierViewDTO.setPayTypes(payTypes);
		cashierViewDTO.setOrderAmt(orderDO.getOrderAmt());
		cashierViewDTO.setFeeAmt(orderDO.getFee());
		cashierViewDTO.setGoodsDesc(orderDO.getGoodsInfo());
		cashierViewDTO.setBusType(orderDO.getBusType());
		cashierViewDTO.setFeeFlag(orderDO.getFeeFlag());

		QuotaDTO quota=new QuotaDTO();
		quota.setCouponNo(null);
		quota.setCouponAmt(BigDecimal.ZERO);
		quota.setPayAmt(orderDO.getTotalAmt());
		quota.setType(CouponType.NONE.getType());
		cashierViewDTO.setQuota(quota);
		return cashierViewDTO;
	}

	@Override
	protected void checkJrnWithBussiness(GenericDTO inputGenericDto){
		//充值不可能使用现金和优惠
		Object body=inputGenericDto.getBody();

		if(body instanceof AbstractNormalPaymentDTO){
			AbstractNormalPaymentDTO normalPaymentDTO=(AbstractNormalPaymentDTO)body;
			if(body instanceof BalPaymentDTO){
				if(!StringUtils.startsWith(normalPaymentDTO.getBusType(), PwmConstants.TX_TYPE_HCOUPON)){
					LemonException.throwBusinessException("CSH20074");
				}
				if(StringUtils.startsWith(normalPaymentDTO.getBusType(), PwmConstants.TX_TYPE_RECHANGE)){
					LemonException.throwBusinessException("CSH20074");
				}
			}

			BigDecimal couponAmt=normalPaymentDTO.getCouponAmt();
			if(JudgeUtils.isNotNull(couponAmt) && couponAmt.compareTo(BigDecimal.valueOf(0))>0){
				LemonException.throwBusinessException("CSH20075");
			}
			if(StringUtils.isNoneBlank(normalPaymentDTO.getCouponNo())){
				LemonException.throwBusinessException("CSH20075");
			}
		}

	}


	//创建账务处理列表,具体业务实现
	protected List<AccountingReqDTO> createAccQueue(OrderDO orderDO,PayJrnDO payJrnDO){
		String txType=orderDO.getTxType();
		if(!StringUtils.equals(PwmConstants.TX_TYPE_RECHANGE,txType) && !StringUtils.equals(PwmConstants.TX_TYPE_HCOUPON,txType)){
			return Collections.EMPTY_LIST;
		}
		String balCapType= CapTypEnum.CAP_TYP_CASH.getCapTyp();
		String balAcNo=acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType);

		if(StringUtils.equals(PwmConstants.TX_TYPE_HCOUPON,txType)){
			List acList=new ArrayList<>();
			//借：其他应付款-支付账户-现金账户
			AccountingReqDTO cshItemReqDTO=acmComponent.createAccountingReqDTO(
						orderDO.getOrderNo(),
						payJrnDO.getPayJrnNo(),
						orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL,
						orderDO.getTotalAmt(),
						balAcNo,
						ACMConstants.USER_AC_TYP,
						balCapType,
						ACMConstants.AC_D_FLG,
					    AcItem.O_BAL.getValue(),
						null,
						null,
						null,
						null,
						"海币充值");
			//贷：其他应付款-暂收-收银台
			AccountingReqDTO userAccountReqDTO=acmComponent.createAccountingReqDTO(
						orderDO.getOrderNo(),
						payJrnDO.getPayJrnNo(),
						orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL,
						orderDO.getTotalAmt(),
						null,
						ACMConstants.ITM_AC_TYP,
						balCapType,
						ACMConstants.AC_C_FLG,
					    AcItem.O_CSH.getValue(),
						balAcNo,
						null,
						null,
						null,
						"海币充值");
			acList.add(cshItemReqDTO);
			acList.add(userAccountReqDTO);
			return acList;
		}

		String busType=orderDO.getBusType();

		//个人快捷充值
		if(JudgeUtils.equals(busType,PwmConstants.BUS_TYPE_RECHARGE_QP)) {
			List<AccountingReqDTO> accountingReqDtos=new ArrayList<>();
			//借：应收账款-渠道充值-xx银行
			AccountingReqDTO cnlRechargeBnkReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, orderDO.getTotalAmt(), balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
					AcItem.I_CNL_BANK.getValue(), "", null, null, null, null);

			//贷：其他应付款-暂收-收银台
			AccountingReqDTO cshItemReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, orderDO.getTotalAmt(), null, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
					AcItem.O_CSH.getValue(), balAcNo, null, null, null, null);

			accountingReqDtos.add(cnlRechargeBnkReqDTO);
			accountingReqDtos.add(cshItemReqDTO);
			return accountingReqDtos;

			//个人线下汇款充值
		} else if(JudgeUtils.equals(busType,PwmConstants.BUS_TYPE_RECHARGE_OFL)) {
			List<AccountingReqDTO> accountingReqDtos=new ArrayList<>();

			//根据币种选择收银台科目号
			String itemNo = "";
			for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
				if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), orderDO.getCcy())) {
					itemNo = ccyAcItemEnum.getItemNo();
				}
			}
			//根据币种选择银行备付金科目号
			String bankItemNo = "";
			for(BankAcItemEnum bankAcItemEnum : BankAcItemEnum.values()) {
				if(JudgeUtils.equals(bankAcItemEnum.getCcy(), orderDO.getCcy())) {
					bankItemNo = bankAcItemEnum.getItemNo();
				}
			}

			//借：银行存款-备付金账户-XX银行
			AccountingReqDTO depositAccountBnkReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, orderDO.getTotalAmt(), balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
					bankItemNo, null, null, null, null, null);

			//贷：其他应付款-暂收-收银台
			AccountingReqDTO cshItemReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, orderDO.getTotalAmt(), balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
					itemNo, null, null, null, null, null);

			accountingReqDtos.add(depositAccountBnkReqDTO);
			accountingReqDtos.add(cshItemReqDTO);
			return accountingReqDtos;

			//个人营业厅充值
		} else if(JudgeUtils.equals(busType,PwmConstants.BUS_TYPE_RECHARGE_HALL)) {
			List<AccountingReqDTO> accountingReqDtos=new ArrayList<>();
			String tmpJrnNo =  LemonUtils.getApplicationName() + PwmConstants.BUS_TYPE_RECHARGE_HALL + IdGenUtils.generateIdWithDate(CshConstants.ORD_GEN_PRE,11);
			//借：应收账款-渠道充值-营业厅
			AccountingReqDTO cnlRechargeHallReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), tmpJrnNo, orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, payJrnDO.getOrderAmt(), balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
					AcItem.I_CNL_HALL.getValue(), null, null, null, null, null);

			//贷：其他应付款-暂收-收银台
			AccountingReqDTO cshItemReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), tmpJrnNo, orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, payJrnDO.getOrderAmt(), balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
					AcItem.O_CSH.getValue(), null, null, null, null, null);

			accountingReqDtos.add(cnlRechargeHallReqDTO);
			accountingReqDtos.add(cshItemReqDTO);
			return accountingReqDtos;

			//企业网银充值
		} else if(JudgeUtils.equals(busType,PwmConstants.BUS_TYPE_RECHARGE_BNB)){

		} else {

		}
		return Collections.emptyList();
	}

	@Override
	protected void notifyBussiness(OrderDO orderDO,PayJrnDO payJrn){
		logger.debug("-------------订单成功，通知充提模块-------------。订单号["+orderDO.getOrderNo()+"]");
		if(StringUtils.equals(orderDO.getTxType(), PwmConstants.TX_TYPE_HCOUPON)){
			RechargeHCouponResultDTO rechargeHResultDTO=new RechargeHCouponResultDTO();
			rechargeHResultDTO.setOrderNo(orderDO.getBusOrderNo());
			rechargeHResultDTO.setOrderAmt(orderDO.getOrderAmt());
			rechargeHResultDTO.setOrderStatus(orderDO.getOrderStatus());
			rechargeHResultDTO.setOrderCcy(orderDO.getCcy());
			GenericDTO<RechargeHCouponResultDTO> rdto=new GenericDTO<>();
			rdto.setBody(rechargeHResultDTO);
			pwmRechargeClient.completeSeaOrder(rdto);
		}
		if(StringUtils.equals(orderDO.getTxType(), PwmConstants.TX_TYPE_RECHANGE)){
			RechargeResultDTO rechargeResultDTO=new RechargeResultDTO();
			rechargeResultDTO.setExtOrderNo(orderDO.getOrderNo());
			rechargeResultDTO.setOrderNo(orderDO.getBusOrderNo());
			logger.info("收到收银台回调结果，币种：" + orderDO.getCcy());
			rechargeResultDTO.setOrderCcy(orderDO.getCcy());
			rechargeResultDTO.setAmount(orderDO.getOrderAmt());
			rechargeResultDTO.setStatus(orderDO.getOrderStatus());
			rechargeResultDTO.setBusType(orderDO.getBusType());
			rechargeResultDTO.setTxJrnNo(payJrn.getPayJrnNo());
			rechargeResultDTO.setRemark(orderDO.getRemark());
			rechargeResultDTO.setFee(orderDO.getFee());
			rechargeResultDTO.setTotalAmount(orderDO.getTotalAmt());
			String payerId = orderDO.getPayerId();
			if(JudgeUtils.isBlank(payerId)){
				payerId = LemonUtils.getUserId();
			}
			rechargeResultDTO.setPayerId(payerId);
			GenericDTO<RechargeResultDTO> rdto=new GenericDTO<>();
			rdto.setBody(rechargeResultDTO);
			pwmRechargeClient.rechargeNotify(rdto);
		}

		logger.info("------------订单成功，通知充提模块完成----------。订单号["+orderDO.getOrderNo()+"]");

	}
}
