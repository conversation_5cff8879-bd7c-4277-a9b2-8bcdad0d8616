package com.hisun.lemon.csh.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class RefundOrderDO extends BaseDO {
	 /**
     * @Fields rfdOrdNo 退款订单号
     */
    private String rfdOrdNo;
    /**
     * @Fields busRdfOrdNo 外部模块退款订单号
     */
    private String busRdfOrdNo;
    /**
     * @Fields orginOrderNo 原订单号
     */
    private String orginOrderNo;
    /**
     * @Fields fndRfdOrderNo 资金能力订单号
     */
    private String fndRfdOrderNo;
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields mercId 卖家用户号
     */
    private String mercId;
    /**
     * @Fields mercName 商户名称
     */
    private String mercName;
    /**
     * @Fields goodInfo 商品信息
     */
    private String goodInfo;
    /**
     * @Fields orderStatus 退款状态   U:预登记  S:成功  F：失败
     */
    private String orderStatus;
    /**
     * @Fields fee 服务费
     */
    private BigDecimal fee;
    /**
     * @Fields rfdAmt 申请退款金额
     */
    private BigDecimal rfdAmt;
    /**
     * @Fields rfdUserAmt 退回到用户金额
     */
    private BigDecimal rfdUserAmt;
    /**
     * @Fields rfdReason 退款原因
     */
    private String rfdReason;
    /**
     * @Fields couponAmt 优惠金额
     */
    private BigDecimal couponAmt;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields rfdType 原路 01   02账户
     */
    private String rfdType;
    /**
     * @Fields txType 交易类型  06 退款  09 撤销
     */
    private String txType;
    /**
     * @Fields couponType 优惠类型
     */
    private String couponType;
    /**
     * @Fields orderCcy 币种
     */
    private String orderCcy;
    /**
     * @Fields acTm 会计时间
     */
    private LocalDate acTm;
    /**
     * @Fields txTm 交易时间
     */
    private LocalDateTime txTm;
	public String getRfdOrdNo() {
		return rfdOrdNo;
	}
	public void setRfdOrdNo(String rfdOrdNo) {
		this.rfdOrdNo = rfdOrdNo;
	}
	public String getBusRdfOrdNo() {
		return busRdfOrdNo;
	}
	public void setBusRdfOrdNo(String busRdfOrdNo) {
		this.busRdfOrdNo = busRdfOrdNo;
	}
	public String getOrginOrderNo() {
		return orginOrderNo;
	}
	public void setOrginOrderNo(String orginOrderNo) {
		this.orginOrderNo = orginOrderNo;
	}
	public String getFndRfdOrderNo() {
		return fndRfdOrderNo;
	}
	public void setFndRfdOrderNo(String fndRfdOrderNo) {
		this.fndRfdOrderNo = fndRfdOrderNo;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getMercId() {
		return mercId;
	}
	public void setMercId(String mercId) {
		this.mercId = mercId;
	}
	public String getOrderStatus() {
		return orderStatus;
	}
	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}
	public BigDecimal getFee() {
		return fee;
	}
	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}
	public BigDecimal getRfdAmt() {
		return rfdAmt;
	}
	public void setRfdAmt(BigDecimal rfdAmt) {
		this.rfdAmt = rfdAmt;
	}
	public String getRfdReason() {
		return rfdReason;
	}
	public void setRfdReason(String rfdReason) {
		this.rfdReason = rfdReason;
	}
	public BigDecimal getCouponAmt() {
		return couponAmt;
	}
	public void setCouponAmt(BigDecimal couponAmt) {
		this.couponAmt = couponAmt;
	}
	public String getBusType() {
		return busType;
	}
	public void setBusType(String busType) {
		this.busType = busType;
	}
	public String getRfdType() {
		return rfdType;
	}
	public void setRfdType(String rfdType) {
		this.rfdType = rfdType;
	}
	public String getTxType() {
		return txType;
	}
	public void setTxType(String txType) {
		this.txType = txType;
	}
	public String getCouponType() {
		return couponType;
	}
	public String getGoodInfo() {
		return goodInfo;
	}
	public void setGoodInfo(String goodInfo) {
		this.goodInfo = goodInfo;
	}
	public void setCouponType(String couponType) {
		this.couponType = couponType;
	}
	public String getOrderCcy() {
		return orderCcy;
	}
	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}
	public LocalDate getAcTm() {
		return acTm;
	}
	public String getMercName() {
		return mercName;
	}
	public void setMercName(String mercName) {
		this.mercName = mercName;
	}
	public void setAcTm(LocalDate acTm) {
		this.acTm = acTm;
	}
	public LocalDateTime getTxTm() {
		return txTm;
	}
	public BigDecimal getRfdUserAmt() {
		return rfdUserAmt;
	}
	public void setRfdUserAmt(BigDecimal rfdUserAmt) {
		this.rfdUserAmt = rfdUserAmt;
	}
	public void setTxTm(LocalDateTime txTm) {
		this.txTm = txTm;
	}
}