package com.hisun.lemon.csh.service;

import com.hisun.lemon.csh.dto.refund.KillOrderRspDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderRspDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderUndoDTO;
import com.hisun.lemon.csh.dto.refund.RefundResultOrderDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * <AUTHOR>
 * @date 2017年8月9日
 * @time 下午21:10:58
 *
 */
public interface IRefundOrderService { 
    /**
     * 创建退款订单
     * @param refundDTO
     */
    public RefundOrderRspDTO  createRfdBill(GenericDTO<RefundOrderDTO> refundDTO);
    
    /**
     * 创建退款订单
     * @param refundDTO
     */
    public RefundOrderRspDTO createRfdUndoBill(GenericDTO<RefundOrderUndoDTO> refundDTO);
    

    
    /**
     * 退换结果处理
     * @param refundDTO
     * @return
     */
    public GenericRspDTO completeBill(GenericDTO<RefundResultOrderDTO> refundDTO);

    /**
     * 充值长款退款 只支持P W状态 业务类型是0601
     * @param refundDTO
     */
    public	GenericRspDTO<NoBody> rechargeRfund(GenericDTO<RefundOrderDTO> refundDTO);
    
    /**
	 * 退款与CPI  补单
	 * @param mblNo
	 * @return
	 */
	public void killCpoOrder(GenericDTO<KillOrderRspDTO> killOrderRspDTO);
}
