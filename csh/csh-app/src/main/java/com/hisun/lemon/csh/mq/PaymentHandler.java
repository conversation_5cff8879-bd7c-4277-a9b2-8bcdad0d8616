package com.hisun.lemon.csh.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.enums.CouponType;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.framework.stream.MultiOutput;
import com.hisun.lemon.framework.stream.producer.Producer;
import com.hisun.lemon.framework.stream.producer.Producers;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.mkm.req.dto.RealeaseRevokeReqDTO;
import com.hisun.lemon.mkm.req.dto.RevokedConsumeCouponReqDTO;
import com.hisun.lemon.tfm.dto.TradeFeeReqDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2017年8月18日
 * @time 下午2:54:28
 * @desc
 */
@Component
public class PaymentHandler {
    protected static final Logger logger = LoggerFactory.getLogger(PaymentHandler.class);

    @Resource
    ObjectMapper objectMapper;

    /**
     * 撤销营销发放
     */
    @Producers({
            @Producer(beanName= "ReleaseRevokeMessageHandler", channelName= MultiOutput.OUTPUT_FIVE)          //channelName 为将主题发送出去的通道名，如配置文件中的output
    })
    public RealeaseRevokeReqDTO cancelMkmCouponGive(String orderNo,String couponType){
        if(!StringUtils.equals(couponType, CouponType.D_COUPON.getType())
                && !StringUtils.equals(couponType, CouponType.E_COUPON.getType())){
            return null;
        }

        RealeaseRevokeReqDTO realeaseRevokeReqDTO=new RealeaseRevokeReqDTO();
        if(StringUtils.equals(couponType, CouponType.D_COUPON.getType())){
            realeaseRevokeReqDTO.setMkTool("04");
        }else if(StringUtils.equals(couponType, CouponType.E_COUPON.getType())){
            realeaseRevokeReqDTO.setMkTool("01");
        }
        realeaseRevokeReqDTO.setOriSeq(orderNo);
        realeaseRevokeReqDTO.setSeq(LemonUtils.getRequestId());
        realeaseRevokeReqDTO.setRevokedTm(DateTimeUtils.getCurrentLocalDateTime());
        String data = ObjectMapperHelper.writeValueAsString(objectMapper, realeaseRevokeReqDTO, true);
        logger.info("撤销营销发放写入消息队列数据：" + data);
        return realeaseRevokeReqDTO;
    }

    /**
     * 撤销营销消费
     */
    @Producers({
            @Producer(beanName= "ConsumeRevokeMessageHandler", channelName= MultiOutput.OUTPUT_FIVE)
    })
    public RevokedConsumeCouponReqDTO cancelMkmCoupon(String orderNo,BigDecimal amt,String couponType){
        RevokedConsumeCouponReqDTO revokedConsumeCouponReqDTO=new RevokedConsumeCouponReqDTO();
        if(StringUtils.equals(couponType,CouponType.H_COUPON.getType())){
            revokedConsumeCouponReqDTO.setCount(amt.intValue());
            revokedConsumeCouponReqDTO.setMkTool("02");
        }else if(StringUtils.equals(couponType,CouponType.D_COUPON.getType())){
            revokedConsumeCouponReqDTO.setMkTool("04");
        }else if(StringUtils.equals(couponType, CouponType.E_COUPON.getType())){
            revokedConsumeCouponReqDTO.setMkTool("01");
        }

        revokedConsumeCouponReqDTO.setSeq(LemonUtils.getRequestId());
        revokedConsumeCouponReqDTO.setOriSeq(orderNo);
        revokedConsumeCouponReqDTO.setType("09");
        String data = ObjectMapperHelper.writeValueAsString(objectMapper, revokedConsumeCouponReqDTO, true);
        logger.info("撤销营销消费写入消息队列数据：" + data);
        return revokedConsumeCouponReqDTO;
    }


    /**
     * 登记商户手续费
     */
    @Producers({
         @Producer(beanName= "merchantTradeFeeConsumer", channelName= MultiOutput.OUTPUT_THREE)
    })
    public TradeFeeReqDTO registMerChantFee(OrderDO orderDO){
        if(StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
                || StringUtils.equals(orderDO.getTxType(),TradeType.PAYMENT.getType())){
            TradeFeeReqDTO tradeFeeReqDTO=new TradeFeeReqDTO();
            tradeFeeReqDTO.setCcy(orderDO.getCcy());
            tradeFeeReqDTO.setUserId(orderDO.getPayeeId());
            tradeFeeReqDTO.setBusOrderNo(orderDO.getOrderNo());
            tradeFeeReqDTO.setBusOrderTime(DateTimeUtils.getCurrentLocalDateTime());
            tradeFeeReqDTO.setTradeAmt(orderDO.getOrderAmt());
            tradeFeeReqDTO.setBusType(orderDO.getBusType());
            //渠道
            tradeFeeReqDTO.setChannel(orderDO.getCapCorgNo());
            String data = ObjectMapperHelper.writeValueAsString(objectMapper, tradeFeeReqDTO, true);
            logger.info("登记商户手续费写入消息队列数据：" + data);
            return tradeFeeReqDTO;
        }
        logger.info("非收单和缴费订单，不登记商户手续费：" + orderDO.getOrderNo());
        return null;
    }

    /**
     * 登记用户手续费
     */
    @Producers({
        @Producer(beanName="tradeFeeConsumer", channelName= MultiOutput.OUTPUT_THREE)
    })
    public TradeFeeReqDTO registUserFee(OrderDO orderDO){
        if(orderDO.getFee().compareTo(BigDecimal.ZERO)<=0){
            return null;
        }
        TradeFeeReqDTO tradeFeeReqDTO=new TradeFeeReqDTO();
        tradeFeeReqDTO.setCcy(orderDO.getCcy());
        tradeFeeReqDTO.setUserId(orderDO.getPayerId());
        tradeFeeReqDTO.setBusOrderNo(orderDO.getOrderNo());
        tradeFeeReqDTO.setBusOrderTime(DateTimeUtils.getCurrentLocalDateTime());
        tradeFeeReqDTO.setTradeAmt(orderDO.getOrderAmt());
        tradeFeeReqDTO.setBusType(orderDO.getBusType());
        String data = ObjectMapperHelper.writeValueAsString(objectMapper, tradeFeeReqDTO, true);
        logger.info("登记用户手续费写入消息队列数据：" + data);
        return tradeFeeReqDTO;
    }
}
