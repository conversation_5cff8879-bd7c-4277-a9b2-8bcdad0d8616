package com.hisun.lemon.csh.controller;
import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hisun.lemon.csh.dto.refund.KillOrderRspDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderRspDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderUndoDTO;
import com.hisun.lemon.csh.dto.refund.RefundResultOrderDTO;
import com.hisun.lemon.csh.service.IRefundOrderService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;

@Api(value = "退款或撤销处理")
@RestController
@RequestMapping(value = "/csh/refund")
public class RefundOrderController extends BaseController {
    @Resource(name="refundService")
    IRefundOrderService refundService;
    
	@ApiOperation(value = "退款", notes = "新增一条退款订单")
	@ApiResponse(code = 200, message = " ")
	@PostMapping(value = "/order")
	public GenericRspDTO<RefundOrderRspDTO> createBill(@Validated @RequestBody GenericDTO<RefundOrderDTO> refundDTO) {
		RefundOrderRspDTO refundRsp = this.refundService.createRfdBill(refundDTO);
		
		return GenericRspDTO.newSuccessInstance(refundRsp);
	}	
	
	@ApiOperation(value = "撤销", notes = "新增一条撤销订单")
	@ApiResponse(code = 200, message = " ")
	@PostMapping(value = "/order/undo")
	public  GenericRspDTO<RefundOrderRspDTO> createRfdUndoBill(@Validated @RequestBody GenericDTO<RefundOrderUndoDTO> refundDTO) {
	    RefundOrderRspDTO refundRsp = this.refundService.createRfdUndoBill(refundDTO);
		return GenericRspDTO.newSuccessInstance(refundRsp);
	}	
	
	@ApiOperation(value = "退款结果处理", notes = "退款结果处理")
	@ApiResponse(code = 200, message = " ")
	@PatchMapping(value = "/status")
	public GenericRspDTO completeBill(
			@Validated @RequestBody GenericDTO<RefundResultOrderDTO> refundDTO) {
		return this.refundService.completeBill(refundDTO);
	}
	
	
	/**
	 * 补单 与CPI对账
	 * 
	 * @param orderNo
	 * @return
	 */
	@ApiOperation(value = "补单    与CPI对账", notes = "补单  与CPI对账")
	@ApiResponse(code = 200, message = "补单    与CPI对账")
	@PatchMapping(value = "/order/cpi/kill")
	public GenericRspDTO<NoBody> killCpoOrder(@Validated @RequestBody GenericDTO<KillOrderRspDTO> killOrderRspDTO) {
		this.refundService.killCpoOrder(killOrderRspDTO);
		return GenericRspDTO.newSuccessInstance();
	}
}
