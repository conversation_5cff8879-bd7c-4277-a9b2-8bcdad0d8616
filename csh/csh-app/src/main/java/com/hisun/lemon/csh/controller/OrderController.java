package com.hisun.lemon.csh.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.component.OrderCommonComponent;
import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.*;
import com.hisun.lemon.csh.dto.order.*;
import com.hisun.lemon.csh.dto.payment.*;
import com.hisun.lemon.csh.dto.tradeFee.TradeFeeReqDTO;
import com.hisun.lemon.csh.dto.tradeFee.TradeFeeRspDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.enums.CouponType;
import com.hisun.lemon.csh.service.IDmOrderService;
import com.hisun.lemon.csh.service.impl.AbstractOrderService;
import com.hisun.lemon.csh.service.impl.OrderTransactionalService;
import com.hisun.lemon.csh.utils.RouterUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@Api(value="收银台操作处理")
@RestController
@RequestMapping(value="/csh/order")
public class OrderController extends BaseController {

	private static final Logger log = LoggerFactory.getLogger(OrderController.class);
	@Resource
	OrderTransactionalService orderTransactionalService;

	@Resource
	private OrderCommonComponent orderCommonComponent;

	@Resource
	private IDmOrderService dmOrderService;

	@Resource
	ObjectMapper objectMapper;
    
	@ApiOperation(value="生成收银台", notes="生成收银单据，返回收银台数据")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "收银台数据")
    @PostMapping(value = "/cashier")
    public GenericRspDTO<CashierViewDTO> createOrder(@Validated @RequestBody GenericDTO<InitCashierDTO> initCashierDTO) {
		InitCashierDTO initDTO=initCashierDTO.getBody();
		CashierViewDTO cashierViewDTO= RouterUtils.select(initDTO.getBusType(), null).createCashier(initCashierDTO);
		return GenericRspDTO.newSuccessInstance(cashierViewDTO);
    }


	@ApiOperation(value="余额支付收银订单", notes="为收银订单生成操作流水，并使用余额完成支付")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "余额支付结果")
    @PostMapping(value = "/bal")
    public GenericRspDTO<PaymentResultDTO> payByBal(@Validated @RequestBody GenericDTO<BalPaymentDTO> balPaymentDTO) {
		BalPaymentDTO balDTO=balPaymentDTO.getBody();
		PaymentResultDTO cashierViewDTO=RouterUtils.select(balDTO.getBusType(), balDTO.getOrderNo()).createJrnAndBalPay(balPaymentDTO);
		return GenericRspDTO.newSuccessInstance(cashierViewDTO);
    }

	@ApiOperation(value="余额直付", notes="下单并使用余额完成支付")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "余额直付结果")
	@PostMapping(value = "/bal/directNew")
	public GenericRspDTO<PaymentResultDTO> NewPayByDirectBal(@Validated @RequestBody GenericDTO<DirectPaymentDTO> directPaymentDTO) {
		PaymentResultDTO cashierViewDTO=RouterUtils.select(directPaymentDTO.getBody().getBusType(), null).newDirectPay(directPaymentDTO);
		return GenericRspDTO.newSuccessInstance(cashierViewDTO);
	}

	@ApiOperation(value="数币转账余额直付", notes="下单并使用余额完成支付")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "余额直付结果")
	@PostMapping(value = "/bal/directDm")
	public GenericRspDTO<PaymentResultDTO> directDm(@Validated @RequestBody GenericDTO<DirectPaymentDTO> directPaymentDTO) {
		PaymentResultDTO cashierViewDTO=dmOrderService.directDm(directPaymentDTO);
		return GenericRspDTO.newSuccessInstance(cashierViewDTO);
	}

	@ApiOperation(value = "数币提现余额直付", notes = "提现并使用余额完成支付")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "数币提现余额直付结果")
	@PostMapping(value = "/bal/direct/dmWithdraw")
	public GenericRspDTO<PaymentResultDTO> directDmWithdraw(@Validated @RequestBody GenericDTO<DirectPaymentDTO> directPaymentDTO) {
		PaymentResultDTO cashierViewDTO = dmOrderService.directDmWithdraw(directPaymentDTO);
		return GenericRspDTO.newSuccessInstance(cashierViewDTO);
	}
	@ApiOperation(value="余额直付", notes="下单并使用余额完成支付")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "余额直付结果")
	@PostMapping(value = "/bal/direct")
	public GenericRspDTO<PaymentResultDTO> payByDirectBal(@Validated @RequestBody GenericDTO<DirectPaymentDTO> directPaymentDTO) {
		PaymentResultDTO cashierViewDTO=RouterUtils.select(directPaymentDTO.getBody().getBusType(), null).directPay(directPaymentDTO);
		return GenericRspDTO.newSuccessInstance(cashierViewDTO);
	}

	@ApiOperation(value="快捷支付收银订单", notes="为收银订单生成操作流水，并使用快捷支付完成支付")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "快捷支付结果")
    @PostMapping(value = "/qp")
    public GenericRspDTO<PaymentResultDTO> payByQuicklyPayment(@Validated @RequestBody GenericDTO<QpPaymentDTO> qpPaymentDTO) {
		QpPaymentDTO qpPaytDTO=qpPaymentDTO.getBody();
		PaymentResultDTO ret = RouterUtils.select(qpPaytDTO.getBusType(), qpPaytDTO.getOrderNo()).createJrnAndQpPay(qpPaymentDTO);
        return GenericRspDTO.newSuccessInstance(ret);
    } 
	
	@ApiOperation(value="第三方页面支付收银订单", notes="为收银订单登记操作流水，并使用第三方的页面完成支付。适应场景：网银支付、微信二维码、支付宝二维码等需要异步通知的支付方式")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "第三方页面支付结果")
    @PostMapping(value = "/pp")
    public GenericRspDTO<PaymentResultDTO> payByPagePayment(@Validated @RequestBody GenericDTO<PpPaymentDTO> ppPaymentDTO) {
		PpPaymentDTO body=ppPaymentDTO.getBody();
		PaymentResultDTO ret = RouterUtils.select(body.getBusType(), body.getOrderNo()).createJrnAndPagePay(ppPaymentDTO);
        return GenericRspDTO.newSuccessInstance(ret);
    }
	
	@ApiOperation(value="第三方页面支付结果通知", notes="资金能力回调通知收银订单结果")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "第三方页面支付结果")
    @PatchMapping(value = "/pp/result")
    public GenericRspDTO<NoBody> ppNotify(@Validated @RequestBody GenericDTO<NotifyResultDTO> notifyResultDTO) {
		String orderNo=notifyResultDTO.getBody().getCshOrderNo();
		RouterUtils.select(null, orderNo).handlePpNotice(notifyResultDTO);
		return GenericRspDTO.newSuccessInstance();
    }


	@ApiOperation(value="收银台线下收款", notes="为用户完成线下充值处理")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@ApiResponse(code = 200, message = "收银台数据")
	@PostMapping(value = "/offline")
	public GenericRspDTO<OfflinePaymentResultDTO> offlinePayment(@Validated @RequestBody GenericDTO<OfflinePaymentDTO> genericDTO) {
		OfflinePaymentDTO body=genericDTO.getBody();
		OfflinePaymentResultDTO offlinePaymentResultDTO = RouterUtils.select(body.getBusType(), body.getOrderNo()).offlinePayment(genericDTO);
		return GenericRspDTO.newSuccessInstance(offlinePaymentResultDTO);
   }

    @ApiOperation(value="无界面后台支付", notes="处理没有界面的支付请求")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "收银台数据")
    @PostMapping(value = "/backstage")
    public GenericRspDTO<BackstageViewDTO> backstagePayment(@Validated @RequestBody GenericDTO<InitBackstageDTO> genericDTO) {
		InitBackstageDTO body=genericDTO.getBody();
		BackstageViewDTO backstageViewDTO=RouterUtils.select(body.getBusType(), null).backstagePay(genericDTO);
        return GenericRspDTO.newSuccessInstance(backstageViewDTO);
    }

	@ApiOperation(value="配额", notes="订单实时配额")
	@ApiResponse(code = 200, message = "订单实时配额")
	@PostMapping(value = "/quota")
	public GenericRspDTO<QuotaDTO> computeQuota(@Validated @RequestBody GenericDTO<ComputeQuotaDTO> genericDTO) {
		ComputeQuotaDTO computeQuotaDTO=genericDTO.getBody();
		String type=computeQuotaDTO.getType();

		if(StringUtils.equals(CouponType.H_COUPON.getType(),type)){
			return GenericRspDTO.newSuccessInstance(orderCommonComponent.makeQuotaHCoupons(computeQuotaDTO.getTotalAmt(),computeQuotaDTO.getCouponAmt().intValue()));
		}

		if(StringUtils.equals(CouponType.E_COUPON.getType(),type)){
			return GenericRspDTO.newSuccessInstance(orderCommonComponent.makeQuotaECoupon(computeQuotaDTO.getTotalAmt(),computeQuotaDTO.getCouponAmt(),computeQuotaDTO.getCouponNo()));
		}

		if(StringUtils.equals(CouponType.D_COUPON.getType(),type)){
			return GenericRspDTO.newSuccessInstance(orderCommonComponent.makeQuotaDiscountCoupon(computeQuotaDTO.getTotalAmt(), computeQuotaDTO.getCouponAmt(), computeQuotaDTO.getCouponNo()));
		}

		return GenericRspDTO.newSuccessInstance(new QuotaDTO());
	}

	@ApiOperation(value="订单查询,根据业务订单号", notes="订单查询,根据业务订单号")
	@ApiResponse(code = 200, message = "订单查询结果")
	@GetMapping(value = "/detail/{orderNo}")
	public GenericRspDTO<OrderDTO> query(@PathVariable String orderNo) {
		OrderDO orderDO=orderTransactionalService.getOrderDao().getByBusOrder(orderNo);
		if(null == orderDO){
			throw new LemonException("CSH20053");
		}
		OrderDTO orderDTO=new OrderDTO();
		BeanUtils.copyProperties(orderDTO,orderDO);
		return GenericRspDTO.newSuccessInstance(orderDTO);
	}

	@ApiOperation(value="订单查询,根据收银台订单号", notes="订单查询,根据收银台订单号")
	@ApiResponse(code = 200, message = "订单查询结果")
	@GetMapping(value = "/detail/inner/{orderNo}")
	public GenericRspDTO<OrderDTO> queryCashierOrder(@PathVariable String orderNo) {
		OrderDO orderDO=orderTransactionalService.getOrderDao().get(orderNo);
		if(null == orderDO){
			throw new LemonException("CSH20053");
		}
		OrderDTO orderDTO=new OrderDTO();
		BeanUtils.copyProperties(orderDTO,orderDO);
		return GenericRspDTO.newSuccessInstance(orderDTO);
	}
	
	@ApiOperation(value="修改订单状态", notes="修改订单状态")
	@ApiResponse(code = 200, message = "修改订单状态")
	@GetMapping(value = "/detail/{orderNo}/{orderStatus}")
	public GenericRspDTO updateOrder(@PathVariable String orderNo,@PathVariable String orderStatus ) {
		OrderDO orderDO=new OrderDO();
		orderDO.setOrderNo(orderNo);
		orderDO.setOrderStatus(orderStatus);
		AbstractOrderService s=RouterUtils.select(null, orderNo);
		if(JudgeUtils.isNotNull(s)){
			s.updateOrder(orderDO);
		}else{
			LemonException.throwBusinessException("CSH90000");
		}
		return GenericRspDTO.newSuccessInstance();
	}
	
	
	@ApiOperation(value="根据订单返回收银台", notes="根据收银单据完成配额返回收银台数据")
	@ApiResponse(code = 200, message = "收银台数据")
	@ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
	@GetMapping(value = "/cashier/{orderNo}")
	public GenericRspDTO<CashierViewDTO> getOrderView(@PathVariable String orderNo) {
		OrderDO orderDO=orderTransactionalService.queryOrderNotNone(orderNo);
		if(null == orderDO){
			throw new LemonException("CSH20053");
		}
		CashierViewDTO cashierViewDTO=RouterUtils.select(orderDO.getBusType(), orderNo).cashierQuto(orderDO);
		return GenericRspDTO.newSuccessInstance(cashierViewDTO);
	}

	@ApiOperation(value="差错处理", notes="差错处理")
	@ApiResponse(code = 200, message = "与资金能力支付对账的差错处理")
	@PatchMapping(value = "/error/handler")
	public GenericRspDTO<NoBody> errHandler(@Validated @RequestBody GenericDTO<ErrorOrderDTO> genericDTO) {
		ErrorOrderDTO errorOrderDTO=genericDTO.getBody();
		String orderNo=errorOrderDTO.getOrderNo();
		OrderDO orderDo=orderTransactionalService.queryOrderNotNone(orderNo);

		if(errorOrderDTO.getErrOprType().intValue()==1){
			RouterUtils.select(orderDo.getBusType(), null).handleSuccess(
					errorOrderDTO.getFndOrderNo(),errorOrderDTO.getCrdPayAmt(),orderDo);

		}else if(errorOrderDTO.getErrOprType().intValue()==2){
			RouterUtils.select(orderDo.getBusType(), null).shortErrCancel(orderDo.getOrderNo());
		}else{
			LemonException.throwBusinessException("CSH20039");
		}

		return GenericRspDTO.newSuccessInstance();
	}

	@ApiOperation(value="手续费查询", notes="手续费查询")
	@ApiResponse(code = 200, message = "手续费查询")
	@GetMapping(value = "/tradeFee")
	public GenericRspDTO<TradeFeeRspDTO> tradeFee(@Validated TradeFeeReqDTO tradeFeeReqDTO) {
		TradeFeeRspDTO tradeFeeRspDTO=orderCommonComponent.tradeFee(tradeFeeReqDTO);
        return GenericRspDTO.newSuccessInstance(tradeFeeRspDTO);
	}

	@ApiOperation(value="关闭交易订单", notes="关闭交易订单")
	@ApiResponse(code = 200, message = "关闭交易订单")
	@PostMapping(value = "/close")
	public GenericRspDTO CloseOrder(@Validated @RequestBody GenericDTO<TradeOffDTO> genericDTO) {
		try{
			orderCommonComponent.handleOrderClose(genericDTO);
		}catch (LemonException e){
			LemonException.throwBusinessException(e.getMsgCd());
		}
		return GenericRspDTO.newSuccessInstance();
	}

	@ApiOperation(value="商户转账用户处理订单", notes="商户转账用户处理订单")
	@ApiResponse(code = 200, message = "处理商户转账用户订单")
	@PostMapping(value = "/transfer/merchant")
	public GenericRspDTO<PaymentResultDTO> merchantTransferHandler(@Validated @RequestBody GenericDTO<TransferPaymentDTO> genericDTO) {
		PaymentResultDTO transferResult = RouterUtils.select(genericDTO.getBody().getBusType(), null).merchantTransferPay(genericDTO);
		return GenericRspDTO.newSuccessInstance(transferResult);
	}

	@ApiOperation(value="ACLEDA快捷支付处理", notes="ACLEDA快捷支付处理")
	@ApiResponse(code = 200, message = "ACLEDA快捷支付处理")
	@PostMapping(value = "/qp/acleda")
	public GenericRspDTO<AcledaPaymentResultDTO> payByAcledaQuicklyPayment(@Validated @RequestBody GenericDTO<AcledaQpPaymentDTO> genericDTO) {
		AcledaPaymentResultDTO acledaResult = RouterUtils.select(genericDTO.getBody().getBusType(), null).createOrderJrnAndAcledaQpPay(genericDTO);
		return GenericRspDTO.newSuccessInstance(acledaResult);
	}

	@ApiOperation(value="ACLEDA转账处理", notes="ACLEDA转账处理")
	@ApiResponse(code = 200, message = "ACLEDA转账处理")
	@PostMapping(value = "/transfer/acleda")
	public GenericRspDTO<AcledaTransferRspDTO> acledaTransferOpen(@Validated @RequestBody GenericDTO<AcledaTransferReqDTO> genericDTO) {
		AcledaTransferRspDTO acledaResult = RouterUtils.select(genericDTO.getBody().getBusType(), null).createAcledaOrder(genericDTO);
		return GenericRspDTO.newSuccessInstance(acledaResult);
	}

	@ApiOperation(value="数法币兑换处理", notes="数法币兑换处理")
	@ApiResponse(code = 200, message = "数法币兑换处理")
	@PostMapping(value = "/exchange/precheck")
	public GenericRspDTO<String> preCheckExchangeOrder(@Validated @RequestBody GenericDTO<ExchangeCoinDTO> req) {
		return GenericRspDTO.newSuccessInstance(RouterUtils.select(req.getBody().getBusType(), req.getBody().getOrderNo()).preCheckExchangeOrder(req));
	}

	@ApiOperation(value="数法币兑换完成", notes="数法币兑换完成")
	@ApiResponse(code = 200, message = "数法币兑换完成")
	@PostMapping(value = "/exchange/complete")
	public GenericRspDTO<NoBody> completeExchangeOrder(@Validated @RequestBody GenericDTO<ExchangeCoinDTO> req) {
		RouterUtils.select(req.getBody().getBusType(), req.getBody().getOrderNo()).completeExchangeOrder(req);
		return GenericRspDTO.newSuccessInstance();
	}

	@ApiOperation(value="数币转账账务处理", notes="数币转账账务处理")
	@ApiResponse(code = 200, message = "数币转账账务处理成功")
	@PostMapping(value = "/handle/finance")
	public GenericRspDTO<NoBody> handleFinance(@Validated @RequestBody GenericDTO<HandleFinanceDTO> req) {
		dmOrderService.handleFinance(req);
		return GenericRspDTO.newSuccessInstance();
	}

	@ApiOperation(value="数币转账失败处理", notes="数币转账失败处理")
	@ApiResponse(code = 200, message = "数币转账账务处理成功")
	@PostMapping(value = "/handle/dm/tran/fail")
	public GenericRspDTO<NoBody> handleFailTransfer(@Validated @RequestBody GenericDTO<HandleFailTranDTO> req) {
		dmOrderService.handleFailTransfer(req);
		return GenericRspDTO.newSuccessInstance();
	}

	@ApiOperation(value="数币提现失败处理", notes="数币提现失败处理")
	@ApiResponse(code = 200, message = "数币提现账务处理成功")
	@PostMapping(value = "/handle/dm/withdraw/fail")
	public GenericRspDTO<NoBody> handleFailWithdraw(@Validated @RequestBody GenericDTO<HandleFailTranDTO> req) {
		dmOrderService.handleFailWithdraw(req);
		return GenericRspDTO.newSuccessInstance();
	}

	@ApiOperation(value="法币汇款充值订单结果处理", notes="法币汇款充值订单结果处理")
	@ApiResponse(code = 200, message = "法币汇款充值订单结果处理成功")
	@PostMapping(value = "/fund/onMessageReceive")
	public GenericRspDTO<NoBody> onMessageReceive(@Validated @RequestBody GenericDTO<NotifyResultDTO> genericCmdDTO) {
		String data = ObjectMapperHelper.writeValueAsString(objectMapper, genericCmdDTO.getBody(), true);
		log.info("接收cpi模块通知数据 {}", data);
		NotifyResultDTO notifyResultDTO = genericCmdDTO.getBody();
		if(JudgeUtils.isNotNull(notifyResultDTO)){
			//调用通知处理接口
			final AbstractOrderService service = RouterUtils.select(null, notifyResultDTO.getCshOrderNo());
			if(JudgeUtils.isNotNull(service)){
				log.info("实现类:"+service.toString());
				service.handlePpNotice(genericCmdDTO);
			}
		}
		return GenericRspDTO.newSuccessInstance();
	}
}
