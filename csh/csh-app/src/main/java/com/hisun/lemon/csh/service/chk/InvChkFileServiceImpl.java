package com.hisun.lemon.csh.service.chk;

import com.hisun.lemon.csh.enums.OrderStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.csh.constants.CshConstants;

@Transactional
@Service
public class InvChkFileServiceImpl extends AbstractChkFileService {

    public InvChkFileServiceImpl() {
        super();
        this.appCnl="INV";
        this.chkOrderStatus=new String[]{
                OrderStatus.SUCC.getValue()
        };
        this.chkTxTypes= new String[]{ "07" };
        this.lockName="CSH_INV_CHK_FILE_LOCK";
    }
}
