package com.hisun.lemon.csh.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class PaytypeDO extends BaseDO {
    /**
     * @Fields id 主键
     */
    private Integer id;
    /**
     * @Fields mercId 商户ID
     */
    private String mercId;
    /**
     * @Fields busType 业务类型
     */
    private String busType;

    /**
     * @Fields appCnl 支付应用
     */
    private String appCnl;

    /**
     * @Fields payTypes 账户支付类型
     */
    private String payTypes;
    /**
     * @Fields gwPayTypes 网关支付类型
     */
    private String gwPayTypes;
    /**
     * @Fields status 状态：是否删除
     */
    private String status;
    /**
     * @Fields modifyOpr 最后修改者
     */
    private String modifyOpr;
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getPayTypes() {
        return payTypes;
    }

    public void setPayTypes(String payTypes) {
        this.payTypes = payTypes;
    }

    public String getGwPayTypes() {
        return gwPayTypes;
    }

    public void setGwPayTypes(String gwPayTypes) {
        this.gwPayTypes = gwPayTypes;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getModifyOpr() {
        return modifyOpr;
    }

    public void setModifyOpr(String modifyOpr) {
        this.modifyOpr = modifyOpr;
    }

    public String getAppCnl() {
        return appCnl;
    }

    public void setAppCnl(String appCnl) {
        this.appCnl = appCnl;
    }
}