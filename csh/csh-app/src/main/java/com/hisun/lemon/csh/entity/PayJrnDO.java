/*
 * @ClassName PayJrnDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 15:47:39
 */
package com.hisun.lemon.csh.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class PayJrnDO extends BaseDO {
    /**
     * @Fields payJrnNo 
     */
    private String payJrnNo;
    /**
     * @Fields payOrderNo 
     */
    private String payOrderNo;
    /**
     * @Fields txTm 
     */
    private LocalDateTime txTm;
    /**
     * @Fields txType 
     */
    private String txType;
    /**
     * @Fields userId 
     */
    private String userId;
    /**
     * @Fields payOrdTm 
     */
    private LocalDateTime payOrdTm;
    /**
     * @Fields busOrderNo 
     */
    private String busOrderNo;
    /**
     * @Fields fndOrderNo 
     */
    private String fndOrderNo;
    /**
     * @Fields orderAmt 
     */
    private BigDecimal orderAmt;
    /**
     * @Fields balAmt 
     */
    private BigDecimal balAmt;
    /**
     * @Fields invAmt 
     */
    private BigDecimal invAmt;
    /**
     * @Fields couponAmt 
     */
    private BigDecimal couponAmt;
    /**
     * @Fields couponType 01:海币 02:电子券
     */
    private String couponType;
    /**
     * @Fields crdPayAmt 
     */
    private BigDecimal crdPayAmt;
    /**
     * @Fields crdPayType 
     */
    private Integer crdPayType;
    /**
     * @Fields jrnStatus 
     */
    private String jrnStatus;
    /**
     * @Fields atvId 优惠券编号
     */
    private String couponNo;

    private String ccy;

    public String getCouponNo() {
        return couponNo;
    }

    public void setCouponNo(String couponNo) {
        this.couponNo = couponNo;
    }

    public String getPayJrnNo() {
        return payJrnNo;
    }

    public void setPayJrnNo(String payJrnNo) {
        this.payJrnNo = payJrnNo;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDateTime getPayOrdTm() {
        return payOrdTm;
    }

    public void setPayOrdTm(LocalDateTime payOrdTm) {
        this.payOrdTm = payOrdTm;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getFndOrderNo() {
        return fndOrderNo;
    }

    public void setFndOrderNo(String fndOrderNo) {
        this.fndOrderNo = fndOrderNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public BigDecimal getBalAmt() {
        return balAmt;
    }

    public void setBalAmt(BigDecimal balAmt) {
        this.balAmt = balAmt;
    }

    public BigDecimal getInvAmt() {
        return invAmt;
    }

    public void setInvAmt(BigDecimal invAmt) {
        this.invAmt = invAmt;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public BigDecimal getCrdPayAmt() {
        return crdPayAmt;
    }

    public void setCrdPayAmt(BigDecimal crdPayAmt) {
        this.crdPayAmt = crdPayAmt;
    }

    public Integer getCrdPayType() {
        return crdPayType;
    }

    public void setCrdPayType(Integer crdPayType) {
        this.crdPayType = crdPayType;
    }

    public String getJrnStatus() {
        return jrnStatus;
    }

    public void setJrnStatus(String jrnStatus) {
        this.jrnStatus = jrnStatus;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }
}