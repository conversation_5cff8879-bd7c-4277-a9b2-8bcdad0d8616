package com.hisun.lemon.csh.service.impl;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.bil.dto.CreateUserBillDTO;
import com.hisun.lemon.bil.dto.UpdateUserBillDTO;
import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.MessageSendReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.client.EbankpayClient;
import com.hisun.lemon.cpi.client.RefundClient;
import com.hisun.lemon.cpi.dto.RefundReqDTO;
import com.hisun.lemon.cpi.dto.RefundRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.csh.component.AcmComponent;
import com.hisun.lemon.csh.component.RefundComponent;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dto.refund.KillOrderRspDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderRspDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderUndoDTO;
import com.hisun.lemon.csh.dto.refund.RefundResultOrderDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.RefundOrderDO;
import com.hisun.lemon.csh.enums.AcItem;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.CouponType;
import com.hisun.lemon.csh.enums.OrderStatus;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.csh.mq.BillSyncHandler;
import com.hisun.lemon.csh.service.IRefundOrderService;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.mkm.client.MarketActivityClient;
import com.hisun.lemon.mkm.req.dto.RevokedConsumeCouponReqDTO;
import com.hisun.lemon.mkm.res.dto.RevokedConsumeCouponResDTO;
import com.hisun.lemon.onr.client.NotifySendClient;
import com.hisun.lemon.onr.dto.NotifySendReqDTO;
import com.hisun.lemon.pwm.constants.PwmConstants;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import com.hisun.lemon.tfm.client.TfmServerClient;
import com.hisun.lemon.tfm.dto.MerchantRefundFeeReqDTO;
import com.hisun.lemon.tfm.dto.MerchantRefundFeeReversalReqDTO;
import com.hisun.lemon.tfm.dto.MerchantRefundFeeRspDTO;


@Service("refundService")
public class RefundOrderServiceImpl extends BaseService implements IRefundOrderService {
	private static final Logger logger = LoggerFactory.getLogger(RefundOrderServiceImpl.class);
	//撤销时必须调用退款接口的网银资金机构
	private static final String[] capcorgs = {CshConstants.PAY_TYPE_WX,CshConstants.PAY_TYPE_ALI,
			CshConstants.PAY_TYPE_BEST,CshConstants.PAY_TYPE_SEA};
	private static final Map refundBusTypeMap = new HashMap<>();
	static {
		refundBusTypeMap.put("0201", "0602");
		refundBusTypeMap.put("0202", "0602");
		refundBusTypeMap.put("0203", "0602");
		refundBusTypeMap.put("0204", "0602");
		refundBusTypeMap.put("0205", "0602");
		refundBusTypeMap.put("0801", "0605");
		refundBusTypeMap.put("0802", "0605");
		refundBusTypeMap.put("0803", "0605");
	}
	private static final Map undoBusTypeMap = new HashMap<>();
	static {
		undoBusTypeMap.put("0201", "0902");
		undoBusTypeMap.put("0202", "0902");
		undoBusTypeMap.put("0203", "0902");
		undoBusTypeMap.put("0204", "0902");
		undoBusTypeMap.put("0205", "0902");
		undoBusTypeMap.put("0801", "0905");
		undoBusTypeMap.put("0802", "0905");
		undoBusTypeMap.put("0803", "0905");
	}

	@Resource
	private RefundOrderTransactionalService refundTransactionalService;
	@Resource
	private MarketActivityClient marketActivity;
	@Resource
	private RefundClient refundClient;
	@Resource
	private MarketActivityClient mkmActivityClient;
	@Resource
	private AccountManagementClient acCountClient;
	@Resource
	private AcmComponent acmComponent;
	@Resource
	private RefundComponent refundComponent;
	@Resource
	private TfmServerClient tmfServerClient;
	@Resource
	protected RiskCheckClient riskCheckClient;
	@Resource
	BillSyncHandler billSyncHandler;
	@Resource
	NotifySendClient notifySendClient;
	@Resource
	private CmmServerClient cmmServerClient;
	@Resource
	protected DistributedLocker locker;
	@Resource
	protected EbankpayClient ebankpayClient;
	

	/**
	 * 
	 * @param refundDO
	 */
	public void createBill(RefundOrderDO refundDO, OrderDO orderDO, String refundType) {
		CreateUserBillDTO createUserDTO = new CreateUserBillDTO();
		createUserDTO.setOrderNo(refundDO.getRfdOrdNo());
		createUserDTO.setOrgOrderNo(refundDO.getOrginOrderNo());
		createUserDTO.setOrderStatus(refundDO.getOrderStatus());
		createUserDTO.setTxType(refundType);
		Object[] args = new Object[] {};
		String key = "0";
		if (StringUtils.equals(refundDO.getRfdType(), CshConstants.RFD_TYPE_U)) {
			key = "0";
		}
		if (StringUtils.equals(refundDO.getRfdType(), CshConstants.RFD_TYPE_R)) {
			if (StringUtils.equals(orderDO.getCapCardType(), CshConstants.CRD_TYPE_NB)) {
				key = "1";
			} else if (StringUtils.equals(orderDO.getCapCardType(), CshConstants.CRD_TYPE_QP)) {
				key = orderDO.getCapCorgNo();
			}
		}
		String descStr = refundComponent.getViewOrderInfo(key, args);
		createUserDTO.setOrderAmt(refundDO.getRfdAmt());
		createUserDTO.setCouponType(refundDO.getCouponType());
		BigDecimal couponAmt=refundDO.getCouponAmt();
        if(StringUtils.isNoneBlank(refundDO.getCouponType())){
        	if(StringUtils.equals(refundDO.getCouponType(), CouponType.H_COUPON.getType())){
        		couponAmt=couponAmt.multiply(BigDecimal.valueOf(PwmConstants.H_USD_RATE)).setScale(2,
                        BigDecimal.ROUND_DOWN);
        	}
        }
        createUserDTO.setCouponAmt(couponAmt);
		createUserDTO.setPayMod(descStr);
		createUserDTO.setTxTm(refundDO.getTxTm());
		createUserDTO.setMercName(refundDO.getMercName());
		createUserDTO.setBusType(refundDO.getBusType());
		createUserDTO.setRfdReason(refundDO.getRfdReason());
		// 商户编号
		createUserDTO.setPayeeId(refundDO.getMercId());
		// 用户编号
		createUserDTO.setPayerId(refundDO.getUserId());
		createUserDTO.setGoodsInfo(refundDO.getGoodInfo());
		billSyncHandler.createBill(createUserDTO);
	}

	/**
	 * 撤销
	 */
	@Override
	public RefundOrderRspDTO createRfdUndoBill(GenericDTO<RefundOrderUndoDTO> refundDTO) {
		RefundOrderUndoDTO refund = refundDTO.getBody();
		try {
			return locker.lock("CSH.REFRESH_09_" + refund.getOrginOrderNo(), 18, 16, () -> {
				RefundOrderRspDTO refundRsp = new RefundOrderRspDTO();
				// 检查商户是否为黑名单
				RiskCheckUserStatusReqDTO checkUserReq = new RiskCheckUserStatusReqDTO();
				checkUserReq.setId(refund.getMercId());
				checkUserReq.setIdTyp("01");
				checkUserReq.setTxTyp("09");
				GenericRspDTO<NoBody> genericRspDTO = riskCheckClient.checkUserStatus(checkUserReq);
				if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
					throw new LemonException("CSH20104");
				}
				RefundOrderDO refundDO = new RefundOrderDO();
				refundDO.setBusRdfOrdNo(refund.getBusRdfOrdNo());
				refundDO.setOrginOrderNo(refund.getOrginOrderNo());
				refundDO.setTxType(TradeType.UNDO.getType());
				refundDO.setMercName(refund.getMercName());
				refundDO.setGoodInfo(refund.getGoodInfo());
				refundDO.setOrderCcy(refund.getOrderCcy());
				refundDO.setRfdReason(refund.getRfdReason());
				if (JudgeUtils.isBlank(refundDO.getOrderCcy())) {
					refundDO.setOrderCcy(CshConstants.QP_PAY_CCY);
				}
				String ymd = DateTimeUtils.getCurrentDateStr();
				String rftOrdNo = TradeType.UNDO.getType() + ymd
						+ IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
				refundDO.setRfdOrdNo(rftOrdNo);
				refundDO.setAcTm(LemonUtils.getAccDate());
				refundDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
				// 订单检查
				OrderDO orderDO = this.refundTransactionalService.selectOrderInfo(refundDO.getOrginOrderNo());
				refundDO.setBusType(undoBusTypeMap.get(orderDO.getBusType()).toString());
				OrderDO orginOrder = refundComponent.checkUndoOrder(refundDO, orderDO, refund.getBusType());

				refundDO.setMercId(orderDO.getPayerId());
				if (JudgeUtils.isNotBlank(refund.getMercId())) {
					refundDO.setMercId(refund.getMercId());
				}
				refundDO.setRfdType(CshConstants.RFD_TYPE_U);
				// 根据原订单号查询 订单撤回当 若无补款时 退回到账户 否则退回到银行卡
				if (!StringUtils.equals(orginOrder.getCrdPayType(), CshConstants.CRD_TYPE_NONE)
						&& JudgeUtils.isNotEmpty(orginOrder.getCrdPayType())) {
					refundDO.setRfdType(CshConstants.RFD_TYPE_R);
					refundDO.setFndRfdOrderNo(orginOrder.getFndOrderNo());
				}
				// 撤销的金额就是原订单可退的所有金额

				refundDO.setOrderStatus(CshConstants.ORD_STS_C0);
				//原订单网银支付资金机构
				String capcorgNo=orderDO.getCapCorgNo();
				// 调用计算
				if (!JudgeUtils.equalsAny(orderDO.getOrderStatus(),OrderStatus.WAIT_PAY.getValue(),OrderStatus.PRE_PAY.getValue(),OrderStatus.EXPIRE.getValue()) ||
						Arrays.stream(capcorgs).anyMatch(cap -> cap.equalsIgnoreCase(capcorgNo))
						) {
					refundDO.setRfdAmt(orginOrder.getOrderAmt());
					RefundOrderDO compute = refundComponent.compute(refundDO, orginOrder);
					if (JudgeUtils.isNull(compute)) {
						throw new LemonException("CSH20079");
					} else {
						refundDO.setCouponAmt(compute.getCouponAmt());
						refundDO.setRfdUserAmt(compute.getRfdUserAmt());
						refundDO.setCouponType(compute.getCouponType());
					}
				} else {
					refundDO.setCouponAmt(BigDecimal.valueOf(0));
					refundDO.setRfdUserAmt(BigDecimal.valueOf(0));
					refundDO.setCouponType(CouponType.NONE.getType());
					refundDO.setRfdAmt(BigDecimal.valueOf(0));
				}
				// 退款订单
				this.refundTransactionalService.createRefundOrder(refundDO);
				createBill(refundDO, orderDO, "09");
				String merchantRfdNo = null;
				if (StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())) {
					if (!StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.WAIT_PAY.getValue())
							&& !StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.PRE_PAY.getValue())
							&& !StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.EXPIRE.getValue())) {
						// 处理商户服务费
						GenericDTO<MerchantRefundFeeReqDTO> merchanRefundFeeDTO = new GenericDTO<MerchantRefundFeeReqDTO>();
						MerchantRefundFeeReqDTO merchanReq = new MerchantRefundFeeReqDTO();
						merchanReq.setBusOrderNo(refundDO.getOrginOrderNo());
						merchanReq.setRefundAmt(orderDO.getOrderAmt());
						merchanRefundFeeDTO.setBody(merchanReq);
						logger.debug(
								"开始处理商户服务费====================================busOrderNo" + refundDO.getOrginOrderNo());
						GenericRspDTO<MerchantRefundFeeRspDTO> rspDTO = tmfServerClient
								.merchantRefundFee(merchanRefundFeeDTO);
						if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
							logger.error("商户服务费处理失败====订单号是：busOrderNo=" + refundDO.getOrginOrderNo());
							LemonException.throwBusinessException(rspDTO.getMsgCd());
						}
						merchantRfdNo = rspDTO.getBody().getOrderNo();
					}
				}
				// 撤销
				//if (!StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.PRE_PAY.getValue())
					//	&& !StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.WAIT_PAY.getValue())) {
					refundUndo(refundDO, orginOrder.getCrdPayType(), orginOrder.getOrderStatus(), merchantRfdNo);
				//}
				// 原订单交易成功才需要风控累计
				if (StringUtils.equals(orginOrder.getOrderStatus(), OrderStatus.SUCC.getValue())) {
					riskAmount(refundDO, orginOrder);
				}
				// 更新订单
				refundRsp = handleComplete(refundDO, "C", orginOrder.getOrderStatus(), orderDO.getBusOrderNo());
				return refundRsp;
			});
		} catch (LemonException e) {
			throw e;
		} catch (Exception e) {
			throw LemonException.create(e);
		}

	}

	/**
	 * 充值长款退款 只支持P W状态 业务类型是0601
	 * 
	 * @param refundDTO
	 * @return
	 */
	@Override
	public GenericRspDTO<NoBody> rechargeRfund(GenericDTO<RefundOrderDTO> refundDTO) {
		RefundOrderDTO refund = refundDTO.getBody();
		try {
			return locker.lock("CSH.REFRESH_09_" + refund.getOrginOrderNo(), 18, 16, () -> {
				RefundOrderDO refundDO = new RefundOrderDO();
				refundDO.setUserId(refund.getRefundUserId());
				refundDO.setBusRdfOrdNo(refund.getBusRdfOrdNo());
				refundDO.setOrginOrderNo(refund.getOrginOrderNo());
				refundDO.setMercName(refund.getMercName());
				refundDO.setTxType(TradeType.REFUND.getType());
				refundDO.setBusType(BussinessType.RFD_LONG.getValue());
				refundDO.setOrderCcy(CshConstants.QP_PAY_CCY);
				refundDO.setRfdReason(refund.getRfdReason());
				if (!JudgeUtils.isNull(refund.getOrderCcy())) {
					refundDO.setOrderCcy(refund.getOrderCcy());
				}
				String ymd = DateTimeUtils.getCurrentDateStr();
				String rftOrdNo = TradeType.REFUND.getType() + ymd+ IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
				refundDO.setRfdOrdNo(rftOrdNo);
				refundDO.setOrderStatus(CshConstants.RFD_ORDER_STATUS_R0);
				refundDO.setAcTm(LemonUtils.getAccDate());
				refundDO.setRfdAmt(refund.getRfdAmt());
				refundDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
				// 检查收银订单计算可退优惠
				OrderDO orderDO = this.refundTransactionalService.selectOrderInfo(refundDO.getOrginOrderNo());
				OrderDO orginOrder = refundComponent.checkChargeOrder(refundDO, orderDO);
				refundDO.setMercId(orderDO.getPayerId());
				if (JudgeUtils.isNotBlank(refund.getMercId())) {
					refundDO.setMercId(refund.getMercId());
				}
				refundDO.setRfdType(CshConstants.RFD_TYPE_U);
				refundDO.setCouponAmt(orderDO.getLeftCouponAmt());
				refundDO.setCouponType(orderDO.getCouponType());
				refundDO.setRfdUserAmt(orderDO.getOrderAmt());
				// 根据原订单号查询 订单撤回当 若无补款时 退回到账户 否则退回到银行卡
				if (!StringUtils.equals(orginOrder.getCrdPayType(), CshConstants.CRD_TYPE_NONE)
						&& JudgeUtils.isNotEmpty(orginOrder.getCrdPayType())) {
					refundDO.setRfdType(CshConstants.RFD_TYPE_R);
					refundDO.setRfdUserAmt(orderDO.getOrderAmt());
				}
				refundDO.setFndRfdOrderNo(orginOrder.getFndOrderNo());
				// 退款订单
				this.refundTransactionalService.createRefundOrder(refundDO);
				// 充值长款退款
				rechargeRefund(refundDO, orginOrder.getCrdPayType());
				// 风控累计
				riskAmount(refundDO,orginOrder);
				// 更新订单
				RefundOrderRspDTO refundRsp = rechargeComplete(refundDO,"R", orginOrder.getOrderStatus());
				return GenericRspDTO.newSuccessInstance();
			});
		} catch (LemonException e) {
			throw e;
		} catch (Exception e) {
			throw LemonException.create(e);
		}

		
		
		
	}

	/**
	 * 创建退款订单
	 */
	@Override
	public RefundOrderRspDTO createRfdBill(GenericDTO<RefundOrderDTO> refundDTO) {
		RefundOrderDTO refund = refundDTO.getBody();
		try {
		return 	locker.lock("CSH.REFRESH_06_" + refund.getOrginOrderNo(), 18, 16, () -> {
				RefundOrderRspDTO refundRsp=new RefundOrderRspDTO();
				// 检查商户是否为黑名单
				RiskCheckUserStatusReqDTO checkUserReq = new RiskCheckUserStatusReqDTO();
				checkUserReq.setId(refund.getMercId());
				checkUserReq.setIdTyp("01");
				checkUserReq.setTxTyp("06");
				GenericRspDTO<NoBody> genericRspDTO = riskCheckClient.checkUserStatus(checkUserReq);
				if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
					throw new LemonException("CSH20104");
				}

				RefundOrderDO refundDO = new RefundOrderDO();
				refundDO.setUserId(refund.getRefundUserId());
				refundDO.setBusRdfOrdNo(refund.getBusRdfOrdNo());
				refundDO.setOrginOrderNo(refund.getOrginOrderNo());
				refundDO.setMercName(refund.getMercName());
				refundDO.setTxType(TradeType.REFUND.getType());
				refundDO.setOrderCcy(CshConstants.QP_PAY_CCY);
				refundDO.setGoodInfo(refund.getGoodInfo());
				refundDO.setRfdReason(refund.getRfdReason());
				if (!JudgeUtils.isNull(refund.getOrderCcy())) {
					refundDO.setOrderCcy(refund.getOrderCcy());
				}
				String ymd = DateTimeUtils.getCurrentDateStr();
				String rftOrdNo = TradeType.REFUND.getType() + ymd
						+ IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
				refundDO.setRfdOrdNo(rftOrdNo);
				refundDO.setOrderStatus(CshConstants.RFD_ORDER_STATUS_R0);
				refundDO.setAcTm(LemonUtils.getAccDate());
				refundDO.setRfdAmt(refund.getRfdAmt());
				refundDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
				// 检查收银订单计算可退优惠
				OrderDO orderDO = this.refundTransactionalService.selectOrderInfo(refundDO.getOrginOrderNo());
				OrderDO orginOrder = refundComponent.checkOrder(refundDO, orderDO, refund.getBusType());
				refundDO.setBusType(refundBusTypeMap.get(orderDO.getBusType()).toString());
				refundDO.setMercId(orderDO.getPayerId());
				if (JudgeUtils.isNotBlank(refund.getMercId())) {
					refundDO.setMercId(refund.getMercId());
				}
				refundDO.setRfdType(CshConstants.RFD_TYPE_U);
				// 根据原订单号查询 订单撤回当 若无补款时 退回到账户 否则退回到银行卡
				if (!StringUtils.equals(orginOrder.getCrdPayType(), CshConstants.CRD_TYPE_NONE)
						&& JudgeUtils.isNotEmpty(orginOrder.getCrdPayType())) {
					refundDO.setRfdType(CshConstants.RFD_TYPE_R);
				}
				// 计算退款金额及优惠
				RefundOrderDO compute = refundComponent.compute(refundDO, orginOrder);
				if (JudgeUtils.isNull(compute)) {
					throw new LemonException("CSH20079");
				} else {
					refundDO.setCouponAmt(compute.getCouponAmt());
					refundDO.setRfdUserAmt(compute.getRfdUserAmt());
					refundDO.setCouponType(compute.getCouponType());
				}
				refundDO.setFndRfdOrderNo(orginOrder.getFndOrderNo());
				// 退款订单
				this.refundTransactionalService.createRefundOrder(refundDO);
				createBill(refundDO, orderDO, "06");
				String merchantRfdNo = null;
				// 处理商户服务费
				if (StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())) {
					GenericDTO<MerchantRefundFeeReqDTO> merchanRefundFeeDTO = new GenericDTO<MerchantRefundFeeReqDTO>();
					MerchantRefundFeeReqDTO merchanReq = new MerchantRefundFeeReqDTO();
					merchanReq.setBusOrderNo(refundDO.getOrginOrderNo());
					merchanReq.setRefundAmt(refund.getRfdAmt());
					merchanRefundFeeDTO.setBody(merchanReq);
					logger.debug("开始处理商户服务费====================================busOrderNo" + refundDO.getOrginOrderNo());
					GenericRspDTO<MerchantRefundFeeRspDTO> rspDTO = tmfServerClient
							.merchantRefundFee(merchanRefundFeeDTO);
					if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
						logger.error("商户服务费处理失败==============请求订单号+" + refundDO.getOrginOrderNo() + "===返回码"
								+ rspDTO.getMsgCd());
						LemonException.throwBusinessException(rspDTO.getMsgCd());
					}
					merchantRfdNo = rspDTO.getBody().getOrderNo();
				}

				// 1.处理优惠 2.进行账务处理 3.进行退款操作
				refund(refundDO, orginOrder.getCrdPayType(), merchantRfdNo);
				// 风控累计
				riskAmount(refundDO, orginOrder);

				// 更新订单
				 refundRsp = handleComplete(refundDO, "R", orginOrder.getOrderStatus(),orderDO.getBusOrderNo());
				return refundRsp;
			});
		} catch (LemonException e) {
			throw e;
		} catch (Exception e) {
			throw LemonException.create(e);
		}

	}

	/**
	 * 执行充值长款退款
	 *
	 * @param refundOrderDO
	 * @param capCardType
	 */
	public void rechargeRefund(RefundOrderDO refundOrderDO, String capCardType) {
		boolean syncFlag = false;
		// 退款
		if (refundOrderDO.getRfdUserAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			// 根据退回方式 可退回 账户 原路退回
			if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_R)) {
				if (JudgeUtils.isNotEmpty(capCardType)) {
					// 调用资金能力
					GenericDTO<RefundReqDTO> genericDTO = new GenericDTO<RefundReqDTO>();
					RefundReqDTO refundReqDTO = new RefundReqDTO();
					refundReqDTO.setUserNo(refundOrderDO.getMercId());
					refundReqDTO.setOrdAmt(refundOrderDO.getRfdUserAmt());
					refundReqDTO.setAgrPayDt(DateTimeUtils.getCurrentLocalDate());
					refundReqDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
					refundReqDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());
					refundReqDTO.setCorpBusTyp(CorpBusTyp.REFUND);
					if (StringUtils.equals(capCardType, CshConstants.CRD_TYPE_NB)) {
						refundReqDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANK_REFUND);
					}
					if (StringUtils.equals(capCardType, CshConstants.CRD_TYPE_QP)) {
						refundReqDTO.setCorpBusSubTyp(CorpBusSubTyp.FAST_REFUND);
					}
					refundReqDTO.setOrdCcy(refundOrderDO.getOrderCcy());
					refundReqDTO.setOrdNo(refundOrderDO.getFndRfdOrderNo());
					refundReqDTO.setReqOrdNo(refundOrderDO.getRfdOrdNo());
					genericDTO.setBody(refundReqDTO);
					GenericRspDTO<RefundRspDTO> rspDTO = refundClient.createOrder(genericDTO);
					if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
						LemonException.throwBusinessException(rspDTO.getMsgCd());
					}
					syncFlag = true;
				}
			}
		}
		// 需要等资金能力异步通知
		if (syncFlag) {
			return;
		}
	}

	/**
	 * 执行撤销
	 * 
	 * @param refundOrderDO
	 * @param capCardType
	 * @param orderStatus
	 */
	private void refundUndo(RefundOrderDO refundOrderDO, String capCardType, String orderStatus, String merchantRfdNo) {
		boolean syncFlag = false;
		boolean isDealMkm=false;
		logger.debug("1.进行退优惠处理===================seq" + refundOrderDO.getRfdOrdNo() + "===oriSeq="
				+ refundOrderDO.getOrginOrderNo());
		if (!StringUtils.equals(orderStatus, OrderStatus.PRE_PAY.getValue())
				&& !StringUtils.equals(orderStatus, OrderStatus.WAIT_PAY.getValue())) {
			if (refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
				GenericDTO<RevokedConsumeCouponReqDTO> revokedCouponReqDTO = new GenericDTO<RevokedConsumeCouponReqDTO>();
				RevokedConsumeCouponReqDTO revokeReqDTO = new RevokedConsumeCouponReqDTO();
				if (JudgeUtils.isNotEmpty(refundOrderDO.getCouponType())
						|| StringUtils.equals(refundOrderDO.getCouponType(), CouponType.NONE.getType())) {
					if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.H_COUPON.getType())) {
						revokeReqDTO.setMkTool("02");
						BigDecimal conpon = refundOrderDO.getCouponAmt();
						int count = conpon.intValue();
						revokeReqDTO.setCount(count);
					}
					if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.E_COUPON.getType())) {
						revokeReqDTO.setMkTool("01");
						revokeReqDTO.setAmt(refundOrderDO.getCouponAmt());
					}

					if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.D_COUPON.getType())) {
						revokeReqDTO.setMkTool("04");
						revokeReqDTO.setAmt(refundOrderDO.getCouponAmt());
					}
				}
				revokeReqDTO.setType(refundOrderDO.getTxType());
				revokeReqDTO.setMkTool(refundOrderDO.getCouponType());
				revokeReqDTO.setSeq(refundOrderDO.getRfdOrdNo());
				revokeReqDTO.setOriSeq(refundOrderDO.getOrginOrderNo());
				revokeReqDTO.setRevokedTm(refundOrderDO.getTxTm());
				revokedCouponReqDTO.setBody(revokeReqDTO);
				GenericRspDTO<RevokedConsumeCouponResDTO> revokRsp = mkmActivityClient
						.revokedConsume(revokedCouponReqDTO);
				if (JudgeUtils.isNotSuccess(revokRsp.getMsgCd())) {
					logger.error("优惠处理失败===============前操作处理商户服务费进行回滚==============请求订单号" + merchantRfdNo);
					GenericRspDTO<NoBody> genericDTO = merchantRefundFeeReversal(merchantRfdNo);
					if (JudgeUtils.isNotSuccess(genericDTO.getMsgCd())) {
						LemonException.throwBusinessException(genericDTO.getMsgCd());
					}
					LemonException.throwBusinessException(revokRsp.getMsgCd());
				}
				isDealMkm=true;
			}
		}

		logger.debug("2.账务处理============================");
		List<AccountingReqDTO> acList = refundComponent.account(refundOrderDO);
		if (StringUtils.equals(orderStatus, OrderStatus.SUCC.getValue())) {
			if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_R)
					|| refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
				GenericRspDTO<NoBody> rspDTO = acmComponent.requestAc(acList);
				if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
					logger.error("账务处理失败================开始调用营销撤销接口" + refundOrderDO.getRfdOrdNo()
							+ "======调用处理商户服务费回滚=========");

					GenericRspDTO<NoBody> mkmRspDTO = mkmUndo(refundOrderDO);
					if (JudgeUtils.isNotSuccess(mkmRspDTO.getMsgCd())) {
						throw new LemonException("CSH20091");
					}

					logger.error("优惠处理失败===============前操作处理商户服务费进行回滚==============请求订单号" + merchantRfdNo);
					GenericRspDTO<NoBody> genericDTO = merchantRefundFeeReversal(merchantRfdNo);
					if (JudgeUtils.isNotSuccess(genericDTO.getMsgCd())) {
						LemonException.throwBusinessException(genericDTO.getMsgCd());
					}
				}
			}
		}

		logger.debug("3.进行退款处理============================");
		if (refundOrderDO.getRfdUserAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_R)) {
				if (JudgeUtils.isNotEmpty(capCardType)) {
					// 调用资金能力
					GenericDTO<RefundReqDTO> genericDTO = new GenericDTO<RefundReqDTO>();
					RefundReqDTO refundReqDTO = new RefundReqDTO();
					refundReqDTO.setUserNo(refundOrderDO.getMercId());
					refundReqDTO.setOrdAmt(refundOrderDO.getRfdUserAmt());
					refundReqDTO.setAgrPayDt(DateTimeUtils.getCurrentLocalDate());
					refundReqDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
					refundReqDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());
					refundReqDTO.setCorpBusTyp(CorpBusTyp.REFUND);
					if (StringUtils.equals(capCardType, CshConstants.CRD_TYPE_NB)) {
						refundReqDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANK_REFUND);
					}
					if (StringUtils.equals(capCardType, CshConstants.CRD_TYPE_QP)) {
						refundReqDTO.setCorpBusSubTyp(CorpBusSubTyp.FAST_REFUND);
					}
					refundReqDTO.setOrdCcy(refundOrderDO.getOrderCcy());
					refundReqDTO.setOrdNo(refundOrderDO.getFndRfdOrderNo());
					refundReqDTO.setReqOrdNo(refundOrderDO.getRfdOrdNo());
					genericDTO.setBody(refundReqDTO);
					logger.debug("开始调用资金能力请求退款========================OrdNo=" + refundOrderDO.getFndRfdOrderNo());
					GenericRspDTO<RefundRspDTO> rspDTO = refundClient.createOrder(genericDTO);
					if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
						logger.error("调用资金能力请求退款失败=================1.回滚账务    2.回滚优惠    3.回滚商户服务费的处理");
						if (JudgeUtils.isNotEmpty(acList) && StringUtils.equals(orderStatus, OrderStatus.SUCC.getValue())) {
							for (int i = 0; i < acList.size(); i++) {
								if (JudgeUtils.isNotNull(acList.get(i))) {
									acList.get(i).setTxSts(ACMConstants.ACCOUNTING_CANCEL);
								}
							}
							logger.debug("1.开始时账务回滚============================");
							GenericRspDTO<NoBody> undoRspDTO = acmComponent.requestAc(acList);
							if (JudgeUtils.isNotSuccess(undoRspDTO.getMsgCd())) {
								LemonException.throwBusinessException(undoRspDTO.getMsgCd());
							}
						}

						
						if(isDealMkm){
							logger.debug("2.开始回滚优惠回滚============================");
							if (refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
								GenericRspDTO<NoBody> mkmRspDTO = mkmUndo(refundOrderDO);
								if (JudgeUtils.isNotSuccess(mkmRspDTO.getMsgCd())) {
									throw new LemonException("CSH20091");
								}
							}
						}
						
						
						if(StringUtils.isNotBlank(merchantRfdNo)){
							logger.debug("3.回滚商户服务费的处理");
							GenericRspDTO<NoBody> generic = merchantRefundFeeReversal(merchantRfdNo);
							if (JudgeUtils.isNotSuccess(generic.getMsgCd())) {
								LemonException.throwBusinessException(generic.getMsgCd());
							}
						}
						
						LemonException.throwBusinessException(rspDTO.getMsgCd());
					}
					syncFlag = true;
				}
			}

			// 退回账户
			if (StringUtils.equals(orderStatus, OrderStatus.SUCC.getValue())) {
				if (refundOrderDO.getRfdUserAmt().compareTo(BigDecimal.valueOf(0)) > 0
						&& StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_U)) {
					AccountingReqDTO cshUItemReqDTO = null;
					AccountingReqDTO userItemReqDTO = null;
					List<AccountingReqDTO> acUserList = new ArrayList();
					String acmJrnNo = IdGenUtils.generateIdWithDate(CshConstants.RFD_GEN_PRE, 14);
					// 资金类型为现金
					String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
					String balAcNo = acmComponent.getAcmAcNo(refundOrderDO.getUserId(), balCapType);
					// 借：其他应付款-暂收-收银台
					cshUItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
							refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getRfdUserAmt(),
							balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
							AcItem.O_CSH.getValue(), null, null, null, null, "退回账户");

					// 贷：其他应付款-支付账户-现金账户
					userItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
							refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getRfdUserAmt(),
							balAcNo, ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
							AcItem.O_BAL.getValue(), null, null, null, null, "退回账户");
					acUserList.add(cshUItemReqDTO);
					acUserList.add(userItemReqDTO);
					GenericRspDTO<NoBody> genericRspDTO = acmComponent.requestAc(acUserList);
					if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
						logger.error("资金退回账户失败=================1.回滚账务    2.回滚优惠    3.回滚商户服务费的处理");

						if (JudgeUtils.isNotEmpty(acList)) {
							for (int i = 0; i < acList.size(); i++) {
								if (JudgeUtils.isNotNull(acList.get(i))) {
									acList.get(i).setTxSts(ACMConstants.ACCOUNTING_CANCEL);
								}
							}
							logger.debug("1.开始时账务回滚============================");
							GenericRspDTO<NoBody> undoRspDTO = acmComponent.requestAc(acList);
							if (JudgeUtils.isNotSuccess(undoRspDTO.getMsgCd())) {
								LemonException.throwBusinessException(undoRspDTO.getMsgCd());
							}
						}

						logger.debug("2.开始回滚优惠回滚============================");
						if (refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
							GenericRspDTO<NoBody> mkmRspDTO = mkmUndo(refundOrderDO);
							if (JudgeUtils.isNotSuccess(mkmRspDTO.getMsgCd())) {
								throw new LemonException("CSH20091");
							}
						}

						logger.debug("3.回滚商户服务费的处理");
						GenericRspDTO<NoBody> generic = merchantRefundFeeReversal(merchantRfdNo);
						if (JudgeUtils.isNotSuccess(generic.getMsgCd())) {
							LemonException.throwBusinessException(generic.getMsgCd());
						}
						throw new LemonException("CSH20090");
					}
				}
			}
		}
		// 需要等资金能力异步通知
		if (syncFlag) {
			return;
		}
	}

	/**
	 * 执行退款
	 *
	 * @param refundOrderDO
	 * @param capCardType
	 */
	public void refund(RefundOrderDO refundOrderDO, String capCardType, String merchantRfdNo) {
		boolean syncFlag = false;
		logger.debug("1.进行退优惠处理====================请求订单号" + refundOrderDO.getRfdOrdNo());
		// 1.退优惠 优惠类型不等于00且不为空 并且 优惠额度存在
		if (refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			GenericDTO<RevokedConsumeCouponReqDTO> revokedCouponReqDTO = new GenericDTO<RevokedConsumeCouponReqDTO>();
			RevokedConsumeCouponReqDTO revokeReqDTO = new RevokedConsumeCouponReqDTO();
			if (JudgeUtils.isNotEmpty(refundOrderDO.getCouponType())
					|| !StringUtils.equals(refundOrderDO.getCouponType(), CouponType.NONE.getType())) {
				if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.H_COUPON.getType())) {
					revokeReqDTO.setMkTool("02");
					BigDecimal conpon = refundOrderDO.getCouponAmt();
					int count = conpon.intValue();
					revokeReqDTO.setCount(count);
				}
				if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.E_COUPON.getType())) {
					revokeReqDTO.setMkTool("01");
					revokeReqDTO.setAmt(refundOrderDO.getCouponAmt());
				}

				if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.D_COUPON.getType())) {
					revokeReqDTO.setMkTool("04");
					revokeReqDTO.setAmt(refundOrderDO.getCouponAmt());
				}
			}

			revokeReqDTO.setType(refundOrderDO.getTxType());
			revokeReqDTO.setMkTool(refundOrderDO.getCouponType());
			revokeReqDTO.setSeq(refundOrderDO.getRfdOrdNo());
			revokeReqDTO.setOriSeq(refundOrderDO.getOrginOrderNo());
			revokeReqDTO.setRevokedTm(refundOrderDO.getTxTm());
			revokedCouponReqDTO.setBody(revokeReqDTO);
			logger.debug("调用营销撤销电子券====================请求订单号" + refundOrderDO.getRfdOrdNo());
			GenericRspDTO<RevokedConsumeCouponResDTO> revokRsp = mkmActivityClient.revokedConsume(revokedCouponReqDTO);
			if (JudgeUtils.isNotSuccess(revokRsp.getMsgCd())) {
				logger.error("优惠处理失败===============前操作处理商户服务费进行回滚==============请求订单号" + merchantRfdNo + "===["
						+ revokRsp.getMsgCd() + "]");
				GenericRspDTO<NoBody> genericDTO = merchantRefundFeeReversal(merchantRfdNo);
				if (JudgeUtils.isNotSuccess(genericDTO.getMsgCd())) {
					LemonException.throwBusinessException(genericDTO.getMsgCd());
					logger.error("商户服务费撤销接口失败=========请求订单号" + merchantRfdNo + "===[" + revokRsp.getMsgCd() + "]");
				}
				LemonException.throwBusinessException(revokRsp.getMsgCd());
			}
		}

		logger.debug("2.账务处理============================");
		List<AccountingReqDTO> acList = refundComponent.account(refundOrderDO);
		if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_R)
				|| refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			GenericRspDTO<NoBody> rspDTO = acmComponent.requestAc(acList);
			if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
				logger.error("账务处理失败================开始调用营销撤销接口" + refundOrderDO.getRfdOrdNo()
						+ "======调用处理商户服务费回滚=========");
				GenericRspDTO<NoBody> mkmRspDTO = mkmUndo(refundOrderDO);
				if (JudgeUtils.isNotSuccess(mkmRspDTO.getMsgCd())) {
					throw new LemonException("CSH20091");
				}
				logger.error("账务处理失败===============前操作处理商户服务费进行回滚==============请求订单号" + merchantRfdNo);
				GenericRspDTO<NoBody> genericDTO = merchantRefundFeeReversal(merchantRfdNo);
				if (JudgeUtils.isNotSuccess(genericDTO.getMsgCd())) {
					LemonException.throwBusinessException(genericDTO.getMsgCd());
				}
			}
		}

		logger.debug("3.进行退款处理============================");
		if (refundOrderDO.getRfdUserAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_R)) {
				if (JudgeUtils.isNotEmpty(capCardType)) {
					// 调用资金能力
					GenericDTO<RefundReqDTO> genericDTO = new GenericDTO<RefundReqDTO>();
					RefundReqDTO refundReqDTO = new RefundReqDTO();
					refundReqDTO.setUserNo(refundOrderDO.getMercId());
					refundReqDTO.setOrdAmt(refundOrderDO.getRfdUserAmt());
					refundReqDTO.setAgrPayDt(DateTimeUtils.getCurrentLocalDate());
					refundReqDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
					refundReqDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());
					refundReqDTO.setCorpBusTyp(CorpBusTyp.REFUND);
					if (StringUtils.equals(capCardType, CshConstants.CRD_TYPE_NB)) {
						refundReqDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANK_REFUND);
					}
					if (StringUtils.equals(capCardType, CshConstants.CRD_TYPE_QP)) {
						refundReqDTO.setCorpBusSubTyp(CorpBusSubTyp.FAST_REFUND);
					}
					refundReqDTO.setOrdCcy(refundOrderDO.getOrderCcy());
					refundReqDTO.setOrdNo(refundOrderDO.getFndRfdOrderNo());
					refundReqDTO.setReqOrdNo(refundOrderDO.getRfdOrdNo());
					genericDTO.setBody(refundReqDTO);
					logger.debug("开始调用资金能力请求退款========================OrdNo=" + refundOrderDO.getFndRfdOrderNo());
					GenericRspDTO<RefundRspDTO> rspDTO = refundClient.createOrder(genericDTO);

					if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
						logger.error("调用资金能力请求退款失败=================1.回滚账务    2.回滚优惠    3.回滚商户服务费的处理");

						if (JudgeUtils.isNotEmpty(acList) ) {
							for (int i = 0; i < acList.size(); i++) {
								if (JudgeUtils.isNotNull(acList.get(i))) {
									acList.get(i).setTxSts(ACMConstants.ACCOUNTING_CANCEL);
								}
							}
							logger.debug("1.开始时账务回滚============================");
							GenericRspDTO<NoBody> undoRspDTO = acmComponent.requestAc(acList);
							if (JudgeUtils.isNotSuccess(undoRspDTO.getMsgCd())) {
								LemonException.throwBusinessException(undoRspDTO.getMsgCd());
							}
						}
						logger.debug("2.开始回滚优惠回滚============================");
						if (refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
							GenericRspDTO<NoBody> mkmRspDTO = mkmUndo(refundOrderDO);
							if (JudgeUtils.isNotSuccess(mkmRspDTO.getMsgCd())) {
								throw new LemonException("CSH20091");
							}
						}

						logger.debug("3.回滚商户服务费的处理");

						logger.debug("调用资金能力请求退款失败===============3.处理商户服务费进行回滚==============请求订单号" + merchantRfdNo);
						GenericRspDTO<NoBody> generic = merchantRefundFeeReversal(merchantRfdNo);
						if (JudgeUtils.isNotSuccess(generic.getMsgCd())) {
							logger.error("处理商户服务费回滚失败=======返回码:"+generic.getMsgCd());
							LemonException.throwBusinessException(generic.getMsgCd());
						}
						LemonException.throwBusinessException(rspDTO.getMsgCd());
					}
					syncFlag = true;
				}
			}
			// 退回账户
			if (refundOrderDO.getRfdUserAmt().compareTo(BigDecimal.valueOf(0)) > 0
					&& StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_U)) {
				AccountingReqDTO cshUItemReqDTO = null;
				AccountingReqDTO userItemReqDTO = null;
				List<AccountingReqDTO> acUserList = new ArrayList();
				String acmJrnNo = IdGenUtils.generateIdWithDate(CshConstants.RFD_GEN_PRE, 14);
				// 资金类型为现金
				String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
				String balAcNo = acmComponent.getAcmAcNo(refundOrderDO.getUserId(), balCapType);
				// 借：其他应付款-暂收-收银台
				cshUItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getRfdUserAmt(),
						balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG, AcItem.O_CSH.getValue(),
						null, null, null, null, "退回账户");

				// 贷：其他应付款-支付账户-现金账户
				userItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getRfdUserAmt(),
						balAcNo, ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_C_FLG, AcItem.O_BAL.getValue(),
						null, null, null, null, "退回账户");
				acUserList.add(cshUItemReqDTO);
				acUserList.add(userItemReqDTO);
				GenericRspDTO<NoBody> genericRspDTO = acmComponent.requestAc(acUserList);
				if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
					logger.error("资金退回账户失败=================1.回滚账务    2.回滚优惠    3.回滚商户服务费的处理==返回码:"+genericRspDTO.getMsgCd());
					if (JudgeUtils.isNotEmpty(acList)) {
						for (int i = 0; i < acList.size(); i++) {
							if (JudgeUtils.isNotNull(acList.get(i))) {
								acList.get(i).setTxSts(ACMConstants.ACCOUNTING_CANCEL);
							}
						}
						logger.debug("1.开始时账务回滚============================");
						GenericRspDTO<NoBody> undoRspDTO = acmComponent.requestAc(acList);
						if (JudgeUtils.isNotSuccess(undoRspDTO.getMsgCd())) {
							LemonException.throwBusinessException(undoRspDTO.getMsgCd());
						}
					}
					logger.debug("2.开始回滚优惠回滚============================");
					if (refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
						GenericRspDTO<NoBody> mkmRspDTO = mkmUndo(refundOrderDO);
						if (JudgeUtils.isNotSuccess(mkmRspDTO.getMsgCd())) {
							throw new LemonException("CSH20091");
						}
					}

					logger.debug("3.回滚商户服务费的处理");

					logger.debug("资金退回账户失败===============3.处理商户服务费进行回滚==============请求订单号" + merchantRfdNo);
					GenericRspDTO<NoBody> generic = merchantRefundFeeReversal(merchantRfdNo);
					if (JudgeUtils.isNotSuccess(generic.getMsgCd())) {
						logger.error("处理商户服务费回滚失败=======返回码:"+generic.getMsgCd());
						LemonException.throwBusinessException(generic.getMsgCd());
					}

					throw new LemonException("CSH20090");
				}
			}
		}

		// 需要等资金能力异步通知
		if (syncFlag) {
			return;
		}
	}

	/**
	 * 充值长款退款成功处理
	 * 
	 * @param refundOrderDO
	 */
	public RefundOrderRspDTO rechargeComplete(RefundOrderDO refundOrderDO, String orderStatus, String payStatus) {
		// 更新订单状态
		RefundOrderDO update = new RefundOrderDO();
		update.setRfdOrdNo(refundOrderDO.getRfdOrdNo());
		update.setOrderStatus("R3");
		update.setRfdAmt(refundOrderDO.getRfdAmt());
		update.setRfdUserAmt(refundOrderDO.getRfdUserAmt());
		update.setTxTm(refundOrderDO.getTxTm());
		update.setCouponType(refundOrderDO.getCouponType());
		update.setCouponAmt(refundOrderDO.getCouponAmt());
		this.refundTransactionalService.updateRefundOrder(update);

		// 更新原订单
		// 1.查询原订单 订单可退金额
		OrderDO dborderDO = this.refundTransactionalService.selectOrderInfo(refundOrderDO.getOrginOrderNo());
		if (JudgeUtils.isNull(dborderDO)) {
			throw new LemonException("CSH20063");
		}

		OrderDO orderDO = new OrderDO();
		orderDO.setOrderNo(refundOrderDO.getOrginOrderNo());
		if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_R)) {
			orderDO.setLeftCardAmt(BigDecimal.valueOf(0));
		}
		if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_U)) {
			orderDO.setLeftBalAmt(BigDecimal.valueOf(0));
		}

		orderDO.setLeftTotalAmt(BigDecimal.valueOf(0));
		orderDO.setLeftCouponAmt(BigDecimal.valueOf(0));
		if (StringUtils.equals(orderStatus, "R")) {
			orderDO.setOrderStatus(OrderStatus.REFUND.getValue());
		}
		refundTransactionalService.updateOrj(orderDO);

		RefundOrderRspDTO refundRsp = new RefundOrderRspDTO();
		refundRsp.setRfdType(refundOrderDO.getRfdType());
		refundRsp.setOrderStatus(update.getOrderStatus());
		refundRsp.setOrginOrderNo(refundOrderDO.getOrginOrderNo());
		refundRsp.setRfdOrdNo(refundOrderDO.getRfdOrdNo());
		refundRsp.setBusRdfOrdNo(refundOrderDO.getBusRdfOrdNo());
		return refundRsp;
	}

	/**
	 * 退款成功处理
	 * 
	 * @param refundOrderDO
	 */
	public RefundOrderRspDTO handleComplete(RefundOrderDO refundOrderDO, String orderStatus, String payStatus,String busOrderNo) {
		// 更新订单状态
		RefundOrderDO update = new RefundOrderDO();
		update.setRfdOrdNo(refundOrderDO.getRfdOrdNo());
		if (StringUtils.equals(orderStatus,"C")) {
			if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_U)) {
				update.setOrderStatus("C");
			}
		}
		if (StringUtils.equals(orderStatus,"R")) {
			if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_R)) {
				update.setOrderStatus(CshConstants.RFD_ORDER_STATUS_RP);
			}
			if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_U)) {
				update.setOrderStatus("R3");
			}
		}
		update.setRfdAmt(refundOrderDO.getRfdAmt());
		update.setRfdUserAmt(refundOrderDO.getRfdUserAmt());
		update.setTxTm(refundOrderDO.getTxTm());
		update.setCouponType(refundOrderDO.getCouponType());

		update.setCouponAmt(refundOrderDO.getCouponAmt());
		this.refundTransactionalService.updateRefundOrder(update);
		// 更新原订单
		// 1.查询原订单 订单可退金额
		OrderDO dborderDO = this.refundTransactionalService.selectOrderInfo(refundOrderDO.getOrginOrderNo());
		// 剩余可退总金额=剩余可退金额+剩余可退优惠
		BigDecimal leftTotalAmt = dborderDO.getLeftTotalAmt();
		if (JudgeUtils.isNull(dborderDO)) {
			throw new LemonException("CSH20063");
		}
		BigDecimal leftAmt = BigDecimal.valueOf(0);
		if (!StringUtils.equals(dborderDO.getCrdPayType(), CshConstants.CRD_TYPE_NONE)
				&& JudgeUtils.isNotEmpty(dborderDO.getCrdPayType())) {
			leftAmt = dborderDO.getLeftCardAmt();
		} else {
			leftAmt = dborderDO.getLeftBalAmt();
		}
		BigDecimal leftCouponAmt = dborderDO.getLeftCouponAmt();
		// 退款金额
		BigDecimal totalAmt = refundOrderDO.getRfdUserAmt();
		BigDecimal couponAmt = refundOrderDO.getCouponAmt();
		BigDecimal rfdAmt = refundOrderDO.getRfdAmt();

		// 计算剩余可退总金额
		BigDecimal leftTotalAmtSum = leftTotalAmt.subtract(rfdAmt);
		String orderSts = OrderStatus.REFUND.getValue();
		if (leftTotalAmtSum.compareTo(BigDecimal.valueOf(0)) > 0) {
			orderSts = OrderStatus.PART_REFUND.getValue();
		}

		BigDecimal updTotalCouponAmt = BigDecimal.valueOf(0);
		// 计算退款金额部分
		BigDecimal updLeftAmt = leftAmt.subtract(totalAmt);

		updTotalCouponAmt = leftCouponAmt.subtract(couponAmt);
		OrderDO orderDO = new OrderDO();
		orderDO.setOrderNo(refundOrderDO.getOrginOrderNo());
		if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_R)) {
			orderDO.setLeftCardAmt(updLeftAmt);
		}
		if (StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_U)) {
			orderDO.setLeftBalAmt(updLeftAmt);
		}
		orderDO.setLeftCouponAmt(updTotalCouponAmt);
		if (StringUtils.equals(orderStatus, "C")) {
			orderDO.setOrderStatus("C");
		}
		if (StringUtils.equals(orderStatus,"R")) {
			orderDO.setOrderStatus(orderSts);
		}
		orderDO.setLeftTotalAmt(leftTotalAmtSum);
		refundTransactionalService.updateOrj(orderDO);

		UpdateUserBillDTO updUserReq = new UpdateUserBillDTO();
		updUserReq.setOrderNo(refundOrderDO.getRfdOrdNo());
		updUserReq.setOrderStatus("R3");
		updUserReq.setOrgOrderNo(refundOrderDO.getOrginOrderNo());
		BigDecimal countAmout = refundOrderDO.getCouponAmt();
		if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.H_COUPON.getType())) {
			countAmout = countAmout.multiply(BigDecimal.valueOf(CshConstants.H_USD_RATE)).setScale(2,
					BigDecimal.ROUND_DOWN);
		}
		// 退款金额(如果是等待付款则不传递金额给账单)
		if (!StringUtils.equals(payStatus, OrderStatus.WAIT_PAY.getValue())) {
			updUserReq.setRfdAmt(refundOrderDO.getRfdAmt());
			updUserReq.setRfdReason(refundOrderDO.getRfdReason());
		}
		if (StringUtils.equals(orderStatus, "C")) {
			updUserReq.setOrgOrderStatus("C");
		}
		if (StringUtils.equals(orderStatus,"R")) {
			updUserReq.setOrgOrderStatus(orderSts);
		}
		if (!StringUtils.equals(orderStatus,"C")) {
			updUserReq.setRfdOrdTm(refundOrderDO.getTxTm());
		}
		updUserReq.setPayeeId(refundOrderDO.getMercId());

		billSyncHandler.updateBill(updUserReq);
        logger.debug("同步更新完账单:  OrderNo="+refundOrderDO.getRfdOrdNo());
		RefundOrderRspDTO refundRsp = new RefundOrderRspDTO();
		BigDecimal counponAmt=refundOrderDO.getCouponAmt();
        if(StringUtils.isNoneBlank(refundOrderDO.getCouponType())){
        	if(StringUtils.equals(refundOrderDO.getCouponType(), CouponType.H_COUPON.getType())){
        		counponAmt=couponAmt.multiply(BigDecimal.valueOf(PwmConstants.H_USD_RATE)).setScale(2,
                        BigDecimal.ROUND_DOWN);
        	}
        }
		BigDecimal rfdAmtSum=refundOrderDO.getRfdUserAmt().add(counponAmt);
		refundRsp.setRfdAmt(rfdAmtSum);
		refundRsp.setRfdType(refundOrderDO.getRfdType());
		if (StringUtils.equals(orderStatus,"C")) {
			refundRsp.setOrderStatus("C");
		}
		if (StringUtils.equals(orderStatus,"R")) {
			refundRsp.setOrderStatus("R3");
		}
		refundRsp.setOrginOrderNo(refundOrderDO.getOrginOrderNo());
		refundRsp.setRfdOrdNo(refundOrderDO.getRfdOrdNo());
		refundRsp.setBusRdfOrdNo(refundOrderDO.getBusRdfOrdNo());
		refundRsp.setBusOrderNo(busOrderNo);
		// 消息推送
		if (JudgeUtils.isNotBlank(refundOrderDO.getUserId())) {
			sendMessage(refundOrderDO);
		}
		return refundRsp;
	}

	/**
	 * 退款结果处理
	 */
	@Override
	public GenericRspDTO completeBill(GenericDTO<RefundResultOrderDTO> refundDTO) {
		RefundResultOrderDTO refundResultDTO = refundDTO.getBody();
		RefundOrderDO refundDO = new RefundOrderDO();
		refundDO.setRfdOrdNo(refundResultDTO.getRfdOrdNo());
		refundDO.setRfdUserAmt(refundResultDTO.getRfdSumAmt());
		refundDO.setFndRfdOrderNo(refundResultDTO.getFndRfdOrderNo());
		// 根据退款订单号查询退款订单表
		RefundOrderDO refundInfo = this.refundTransactionalService.selectRefundInfo(refundResultDTO.getRfdOrdNo());
		// 原订单不存在
		if (JudgeUtils.isNull(refundInfo)) {
			throw new LemonException("CSH20025");
		}
		// 订单已经成功
		if (StringUtils.equals(refundInfo.getOrderStatus(),"C")
				|| StringUtils.equals(refundInfo.getOrderStatus(),"R3")) {
			return null;
		}
		// 资金能力通知失败
		String status = "R3";
		if (StringUtils.equals(refundResultDTO.getOrderSataus(),OrderStatus.FAIL.getValue())) {
			throw new LemonException("CSH20078");
		} else if (StringUtils.equals(refundResultDTO.getOrderSataus(), OrderStatus.SUCC.getValue())) {
			if (StringUtils.equals(refundInfo.getBusType(), TradeType.UNDO.getType())) {
				status = "C";
			}
		}

		RefundOrderDO updateDO = new RefundOrderDO();
		updateDO.setRfdOrdNo(refundResultDTO.getRfdOrdNo());
		updateDO.setAcTm(refundDTO.getAccDate());
		updateDO.setOrderStatus(status);
		updateDO.setRfdUserAmt(refundResultDTO.getRfdSumAmt());
		// 通知线下收单商户退款
		GenericDTO<NotifySendReqDTO> genericDTO = new GenericDTO<NotifySendReqDTO>();
		NotifySendReqDTO notifyDTO = new NotifySendReqDTO();
		if (StringUtils.equals(refundInfo.getTxType(), TradeType.REFUND.getType())) {
			notifyDTO.setNotifyType("R");
		}
		if (StringUtils.equals(refundInfo.getTxType(), TradeType.UNDO.getType())) {
			notifyDTO.setNotifyType("Z");
		}
		notifyDTO.setPayAmt(refundInfo.getRfdAmt());
		notifyDTO.setOrderSts("S");
		notifyDTO.setPayDt(refundInfo.getAcTm());
		notifyDTO.setPayTm(DateTimeUtils.getCurrentLocalTime());
		notifyDTO.setOnrOrderNo(refundInfo.getBusRdfOrdNo());
		genericDTO.setBody(notifyDTO);
		GenericRspDTO genericRspDTO = notifySendClient.notifyMerchant(genericDTO);
		if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
			LemonException.throwBusinessException(genericRspDTO.getMsgCd());
		}
		// 更新退款订单表
		this.refundTransactionalService.updateRefundOrder(updateDO);

		// 消息推送
		sendMessage(refundInfo);
		return GenericRspDTO.newSuccessInstance();
	}

	/**
	 * 退款与CPI 补单
	 */
	@Override
	public void killCpoOrder(GenericDTO<KillOrderRspDTO> killOrderRspDTO) {
		KillOrderRspDTO killOrderRsp = killOrderRspDTO.getBody();
		String orderNo = killOrderRsp.getOrderNo();
		// 根据退款订单号查询退款订单表
		RefundOrderDO refundInfo = this.refundTransactionalService.selectRefundInfo(orderNo);
		// 原订单不存在
		if (JudgeUtils.isNull(refundInfo)) {
			throw new LemonException("CSH20025");
		}
		RefundOrderDO updateDO = new RefundOrderDO();
		updateDO.setRfdOrdNo(orderNo);
		updateDO.setAcTm(killOrderRspDTO.getAccDate());
		updateDO.setOrderStatus("R3");
		// 通知线下收单商户退款
		GenericDTO<NotifySendReqDTO> genericDTO = new GenericDTO<>();
		NotifySendReqDTO notifyDTO = new NotifySendReqDTO();
		if (StringUtils.equals(refundInfo.getTxType(), TradeType.REFUND.getType())) {
			notifyDTO.setNotifyType("R");
		}
		if (StringUtils.equals(refundInfo.getTxType(), TradeType.UNDO.getType())) {
			notifyDTO.setNotifyType("Z");
		}
		notifyDTO.setPayAmt(refundInfo.getRfdAmt());
		notifyDTO.setOrderSts("S");
		notifyDTO.setPayDt(refundInfo.getAcTm());
		notifyDTO.setPayTm(DateTimeUtils.getCurrentLocalTime());
		notifyDTO.setOnrOrderNo(refundInfo.getBusRdfOrdNo());
		genericDTO.setBody(notifyDTO);
		GenericRspDTO genericRspDTO = notifySendClient.notifyMerchant(genericDTO);
		if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
			LemonException.throwBusinessException(genericRspDTO.getMsgCd());
		}
		this.refundTransactionalService.updateRefundOrder(updateDO);
	}

	/**
	 * 优惠撤销
	 * 
	 * @param refundOrderDO
	 * @return
	 */
	public GenericRspDTO<NoBody> mkmUndo(RefundOrderDO refundOrderDO) {
		GenericDTO<RevokedConsumeCouponReqDTO> revokedCouponReqDTO = new GenericDTO<RevokedConsumeCouponReqDTO>();
		RevokedConsumeCouponReqDTO revokeReqDTO = new RevokedConsumeCouponReqDTO();
		if (JudgeUtils.isNotEmpty(refundOrderDO.getCouponType())
				|| StringUtils.equals(refundOrderDO.getCouponType(), CouponType.NONE.getType())) {
			if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.H_COUPON.getType())) {
				revokeReqDTO.setMkTool("02");
				BigDecimal conpon = refundOrderDO.getCouponAmt();
				int count = conpon.intValue();
				revokeReqDTO.setCount(count);
			}
			if (StringUtils.equals(refundOrderDO.getCouponType(),CouponType.E_COUPON.getType())) {
				revokeReqDTO.setMkTool("01");
				revokeReqDTO.setAmt(refundOrderDO.getCouponAmt());
			}

			if (StringUtils.equals(refundOrderDO.getCouponType(), CouponType.D_COUPON.getType())) {
				revokeReqDTO.setMkTool("04");
				revokeReqDTO.setAmt(refundOrderDO.getCouponAmt());
			}
		}
		revokeReqDTO.setType("07");
		revokeReqDTO.setMkTool(refundOrderDO.getCouponType());
		String ymd = DateTimeUtils.getCurrentDateStr();
		String seq = "rfd" + ymd + IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
		revokeReqDTO.setSeq(seq);
		revokeReqDTO.setOriSeq(refundOrderDO.getRfdOrdNo());
		revokeReqDTO.setRevokedTm(refundOrderDO.getTxTm());
		revokedCouponReqDTO.setBody(revokeReqDTO);
		GenericRspDTO<RevokedConsumeCouponResDTO> revokRsp = mkmActivityClient.revokedConsume(revokedCouponReqDTO);
		if (JudgeUtils.isNotSuccess(revokRsp.getMsgCd())) {
			LemonException.throwBusinessException(revokRsp.getMsgCd());
		}
		return GenericRspDTO.newSuccessInstance();
	}

	/**
	 * 消息推送
	 * 
	 * @param refundOrderDO
	 */
	public void sendMessage(RefundOrderDO refundOrderDO) {
		// 推送消息(原订单号)
		String orderNo = refundOrderDO.getOrginOrderNo();
		logger.debug("调用消息推送=====订单号:"+orderNo);
		String userId = refundOrderDO.getUserId();
		BigDecimal rfdAmount = refundOrderDO.getRfdAmt();
		String language = LemonUtils.getLocale().getLanguage();
		if (JudgeUtils.isBlank(language)) {
			language = "en";
		}

		GenericDTO<MessageSendReqDTO> messageReqDTO = new GenericDTO<MessageSendReqDTO>();
		MessageSendReqDTO messageReq = new MessageSendReqDTO();
		messageReq.setUserId(userId);
		messageReq.setMessageTemplateId(CshConstants.SEND_MER_TO_USER_ORDER);
		messageReq.setMessageLanguage(language);
		Map<String, String> map = new HashMap<String, String>();
		map.put("amount", rfdAmount.toString());
		map.put("orderNo", orderNo);
		messageReq.setReplaceFieldMap(map);
		messageReqDTO.setBody(messageReq);
		GenericRspDTO<NoBody> rspDto = cmmServerClient.messageSend(messageReqDTO);
	}

	/**
	 * 商户手续费退款处理撤销
	 * 
	 * @param merchantRfdNo
	 * @return
	 */
	public GenericRspDTO<NoBody> merchantRefundFeeReversal(String merchantRfdNo) {

		GenericDTO<MerchantRefundFeeReversalReqDTO> reqDTO = new GenericDTO<>();
		MerchantRefundFeeReversalReqDTO reversalReqDTO = new MerchantRefundFeeReversalReqDTO();
		reversalReqDTO.setOrderNo(merchantRfdNo);
		reqDTO.setBody(reversalReqDTO);
		return tmfServerClient.merchantRefundFeeReversal(reqDTO);

	}
	
	/**
	 * 累计风控
	 * @param refundOrderDO
	 * @return
	 */
	public  GenericRspDTO<NoBody> riskAmount(RefundOrderDO refundOrderDO,OrderDO orderDO){
		List<JrnReqDTO> jrnReqDTOList = new ArrayList<>();
		String busType = orderDO.getBusType();
		JrnReqDTO jrnReqDTO=new JrnReqDTO();
		jrnReqDTO.setCcy(orderDO.getCcy());
		jrnReqDTO.setPayCrdNo(null);
		jrnReqDTO.addTxData("orgTxTyp="+orderDO.getTxType());
		if(StringUtils.isNotBlank(orderDO.getPayeeId())){
			if(StringUtils.equals(orderDO.getBusType(), "0302")){
				jrnReqDTO.setStlUserTyp("02");
			}else{
				jrnReqDTO.setStlUserTyp("01");
			}
			jrnReqDTO.setStlUserId(orderDO.getPayeeId());
		}
		jrnReqDTO.setPayUserTyp("01");
		jrnReqDTO.setPayUserId(orderDO.getPayerId());

		jrnReqDTO.setTxAmt(refundOrderDO.getRfdUserAmt());
		jrnReqDTO.setTxCnl(CshConstants.RSK_CNL_APP);
		jrnReqDTO.setTxTyp(TradeType.REFUND.getType());
		if(JudgeUtils.equals(busType,BussinessType.RECHARGE_HALL)
				|| JudgeUtils.equals(busType,BussinessType.RECHARGE_OFFLINE)) {
			//免密支付:pswFlg=1
			jrnReqDTO.addTxData("pswFlg=1");
		}else{
			//非免密:pswFlg=0
			jrnReqDTO.addTxData("pswFlg=0");
		}

		jrnReqDTO.setTxOrdNo(refundOrderDO.getRfdOrdNo());
		jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
		jrnReqDTO.setTxSts("0");
		jrnReqDTO.setTxDate(orderDO.getAcTm());
		jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
		//补款方式
		String crdPayType = orderDO.getCrdPayType();
		if(JudgeUtils.isNull(orderDO.getCrdPayAmt())) {
			orderDO.setCrdPayAmt(BigDecimal.valueOf(0));
		}
		//海币优惠
		if(JudgeUtils.isNotNull(refundOrderDO.getCouponAmt()) && refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			if(JudgeUtils.equals(refundOrderDO.getCouponType(),CouponType.H_COUPON.getType())){
				JrnReqDTO seaJrnReqDTO=new JrnReqDTO();
				BeanUtils.copyProperties(seaJrnReqDTO,jrnReqDTO);
				seaJrnReqDTO.setPayTyp(Constants.PAY_TYP_SEATEL);
                seaJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				seaJrnReqDTO.setTxAmt(refundOrderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
                seaJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
                logger.debug("交易海币累计:"+refundOrderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
				jrnReqDTOList.add(seaJrnReqDTO);
			}
		}
		
		//无补款
		if(StringUtils.equals(refundOrderDO.getRfdType(), CshConstants.RFD_TYPE_U)) {
			//无优惠 账户余额
			if(JudgeUtils.isNotNull(refundOrderDO.getRfdUserAmt()) && refundOrderDO.getRfdUserAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
				JrnReqDTO accJrnReqDTO=new JrnReqDTO();
				BeanUtils.copyProperties(accJrnReqDTO,jrnReqDTO);
				//支付类型
				accJrnReqDTO.setPayTyp(Constants.PAY_TYP_ACCOUNT);
				accJrnReqDTO.setTxAmt(refundOrderDO.getRfdUserAmt());
                accJrnReqDTO.setStlUserId(orderDO.getPayeeId());
                accJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
                logger.debug("交易余额累计:"+orderDO.getBalAmt());
				jrnReqDTOList.add(accJrnReqDTO);

			}
		}
		//有补款
		else{
			switch (crdPayType) {
				//快捷
				case CshConstants.CRD_TYPE_QP:
					JrnReqDTO qpJrnReqDTO=new JrnReqDTO();
					BeanUtils.copyProperties(qpJrnReqDTO,jrnReqDTO);
					qpJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
                    qpJrnReqDTO.setStlUserId(orderDO.getPayeeId());
					qpJrnReqDTO.setTxAmt(refundOrderDO.getRfdUserAmt());
                    qpJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
                    logger.debug("交易补款累计:"+refundOrderDO.getRfdUserAmt());
					jrnReqDTOList.add(qpJrnReqDTO);
					break;
				//网银
				case CshConstants.CRD_TYPE_NB:
					JrnReqDTO ebankJrnReqDTO = new JrnReqDTO();
					BeanUtils.copyProperties(ebankJrnReqDTO, jrnReqDTO);
					//获取支付路径机构
					String rutCorgNo = orderDO.getCapCorgNo();
					logger.debug("网银支付合作机构:" + rutCorgNo);

					if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_WX)){
						ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_WECHAT);
					}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_ALI)){
						ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_ALIPAY);
					}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_ICBC)){
						ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
					}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_BEST)){
						ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_BESTPAY);
					}else{

					}
					ebankJrnReqDTO.setStlUserId(orderDO.getPayeeId());
					ebankJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
					ebankJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
					logger.debug("网银交易补款累计:" + refundOrderDO.getRfdUserAmt());
					jrnReqDTOList.add(ebankJrnReqDTO);
					break;
				//线下转账
				case CshConstants.CRD_TYPE_FL:
					JrnReqDTO offlieJrnReqDTO=new JrnReqDTO();
					BeanUtils.copyProperties(offlieJrnReqDTO,jrnReqDTO);
					offlieJrnReqDTO.setPayTyp(Constants.PAY_TYP_OFFLINE);
					offlieJrnReqDTO.setStlUserId(orderDO.getPayeeId());
					offlieJrnReqDTO.setPayUserId(orderDO.getPayerId());
					offlieJrnReqDTO.setTxAmt(refundOrderDO.getRfdUserAmt());
                    offlieJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
                    logger.debug("交易线下汇款累计:"+refundOrderDO.getRfdUserAmt());
					jrnReqDTOList.add(offlieJrnReqDTO);
					break;
				default:
					break;
			}
		}
		
		GenericDTO<List<JrnReqDTO>> genericDTO = new GenericDTO();
		genericDTO.setBody(jrnReqDTOList);

		GenericRspDTO<NoBody> rspDto = riskCheckClient.batchAccumulation(genericDTO);
		if(JudgeUtils.isNotSuccess(rspDto.getMsgCd())){
			logger.error("订单"+orderDO.getOrderNo()+"实时风控累计失败:"+rspDto.getMsgCd());
		}
		return  rspDto;
	}
}
