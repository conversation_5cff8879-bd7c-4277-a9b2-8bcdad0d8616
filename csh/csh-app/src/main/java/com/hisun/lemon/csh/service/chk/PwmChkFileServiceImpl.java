package com.hisun.lemon.csh.service.chk;

import com.hisun.lemon.csh.enums.OrderStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.pwm.constants.PwmConstants;


@Transactional
@Service
public class PwmChkFileServiceImpl extends AbstractChkFileService {

    public PwmChkFileServiceImpl() {
        super();
        this.appCnl="PWM";
        this.chkOrderStatus=new String[]{OrderStatus.SUCC.getValue()};
        this.chkTxTypes= new String[]{
                PwmConstants.TX_TYPE_RECHANGE,
                PwmConstants.TX_TYPE_HCOUPON
        };
        this.lockName="CSH_PWM_CHK_FILE_LOCK";
    }
}
