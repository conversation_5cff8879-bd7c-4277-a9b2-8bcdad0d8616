package com.hisun.lemon.csh.component;


import java.io.File;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.hisun.lemon.jcommon.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.dao.IOrderDao;
import com.hisun.lemon.csh.dao.IRefundOrderDao;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.RefundOrderDO;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.jcommon.file.FileSftpUtils;

/**
 * 对账文件组件
 * 
 * <AUTHOR>
 *
 */
@Component
public class ChkFileComponent {
	private static final Logger logger = LoggerFactory.getLogger(ChkFileComponent.class);
	protected static final String YYYMMDD="yyyyMMdd";

	protected static final int DEFAULT_SFTP_TIMEOUT=2000;

	@Resource
	private IOrderDao orderDao;

	@Resource
	private IRefundOrderDao refundOrderDao;

	public List<OrderDO> queryDatas(String appCnl,LocalDate date,String[] chkTxTypes,String[] chkOrderStatus){
		Map queryDo=new HashMap<>();
		queryDo.put("acTm",date);
		queryDo.put("txTypeList",chkTxTypes);
		queryDo.put("statusList",chkOrderStatus);
		queryDo.put("appCnl",appCnl);

		return orderDao.queryList(queryDo);
	}

	public List<RefundOrderDO> queryRefundDatas(LocalDate date,String[] chkTxTypes,String[] chkOrderStatus){
		Map queryDo=new HashMap<>();
		queryDo.put("acTm",date);
		queryDo.put("busTypeList",chkTxTypes);
		queryDo.put("statusList",chkOrderStatus);

		return refundOrderDao.queryList(queryDo);
	}

	/**
	 * 获取对账数据日期
	 * @return
	 */
	public LocalDate getChkDate(){
		LocalDate today= DateTimeUtils.getCurrentLocalDate();
		return today.minusDays(1);
	}

	/**
	 * 获取对账文件名
	 * @param appCnl
	 * @param chkDate
	 * @return  CSH_OPPNAME_XXXXX.ck  CSH_OPPNAME_RFD_XXXXX.ck
	 */
	public String getChkFileName(String appCnl,LocalDate chkDate,boolean refund){
		String appName=LemonUtils.getApplicationName();
		if(refund){
			return appName+"_"+appCnl+"_"+DateTimeUtils.formatLocalDate(chkDate,YYYMMDD)+"_rfd.ck";
		}
		return appName+"_"+appCnl+"_"+DateTimeUtils.formatLocalDate(chkDate,YYYMMDD)+".ck";
	}

	/**
	 * 数据写入对账文件
	 * @param datas
	 * @param fileName
	 */
	public void writeToFile(String appCnl,List<OrderDO> datas,String fileName){
		StringBuilder contextBuilder=new StringBuilder();
		for(OrderDO rec:datas){
			contextBuilder.append(recToLine(rec,appCnl));
		}

		//写入文件
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write(contextBuilder.toString(), localPath + fileName);
		} catch (Exception e) {
			LemonException.throwBusinessException("CSH20035");
		}

	}

	public void writeRefundToFile(String appCnl,List<RefundOrderDO> datas,String fileName){
		StringBuilder contextBuilder=new StringBuilder();
		for(RefundOrderDO rec:datas){
			contextBuilder.append(refundRecToLine(rec, appCnl));
		}

		//写入文件
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write(contextBuilder.toString(),localPath+fileName);
		} catch (Exception e) {
			LemonException.throwBusinessException("CSH20035");
		}

	}

	/**
	 * 文件上传SFTP服务器
	 * @param chkFileName
	 * @param flagName
	 */
	public void upload(String appCnl,String chkFileName, String flagName){
		String localPath=getLocalPath(appCnl);
		String[] uploadFileNames=new String[]{localPath+chkFileName,localPath+flagName};

		String remoteIp=LemonUtils.getProperty("csh.sftp.ip");
		int remotePort=Integer.valueOf(LemonUtils.getProperty("csh.sftp.port"));
		String timeoutStr=LemonUtils.getProperty("csh.sftp.connectTimeout");
		int connectTimeout=DEFAULT_SFTP_TIMEOUT;
		if(StringUtils.isNotEmpty(timeoutStr)){
			connectTimeout=Integer.valueOf(timeoutStr);
		}

		String remotePath=LemonUtils.getProperty("csh.chk.remotePath");

		String name=LemonUtils.getProperty("csh.sftp.name");
		String pwd=LemonUtils.getProperty("csh.sftp.password");

		try {
			FileSftpUtils.upload(uploadFileNames,remoteIp,remotePort,connectTimeout,remotePath,name,pwd);
		} catch (Exception e) {
			logger.error(chkFileName+"上传SFTP文件服务器失败",e);
			LemonException.throwBusinessException("CSH20037");
		}
	}


	public boolean isStart(String appCnl,String flagName){
		return new File(getLocalPath(appCnl)+flagName).exists();
	}

	public void createFlagFile(String appCnl,String flagName){
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write("flag", localPath+flagName);
		} catch (Exception e) {
			LemonException.throwBusinessException("CSH20036");
		}
	}

	/**
	 * 获取本地存放对账文件的目录，没有则创建
	 * @param appCnl
	 * @return
	 */
	private String getLocalPath(String appCnl){
		String localPath=LemonUtils.getProperty("csh.chk.localPath")+appCnl+"/";
		File localPathFile=new File(localPath);
		if(localPathFile.exists()){
			if(localPathFile.isDirectory()){
				return localPath;
			}else{
				logger.error(localPath+"已经存在，但不是目录，任务退出");
				LemonException.throwBusinessException("CSH20038");
			}
		}
		boolean success=localPathFile.mkdirs();
		if(!success){
			logger.error(localPath+"目录创建失败，任务退出");
			LemonException.throwBusinessException("CSH20038");

		}
		return localPath;
	}

	private StringBuilder recToLine(OrderDO rec,String appCnl){
		String fndOrder=rec.getFndOrderNo();
		if(StringUtils.isBlank(fndOrder)){
			fndOrder="";
		}
		StringBuilder lineBuilder=new StringBuilder();
		lineBuilder.append(rec.getOrderNo());
		lineBuilder.append("|");
		lineBuilder.append(rec.getOrderAmt());
		lineBuilder.append("|");
		lineBuilder.append(rec.getOrderStatus());
		lineBuilder.append("|");
		lineBuilder.append(rec.getAcTm());
		lineBuilder.append("|");
		if(StringUtils.equals(appCnl,"CPI")){
			lineBuilder.append(rec.getFndOrderNo());
		}else{
			lineBuilder.append(rec.getBusOrderNo());
		}
		lineBuilder.append("|");
		lineBuilder.append(fndOrder);
		lineBuilder.append("\n");

		return lineBuilder;
	}

	//收银退款订单号|业务/资金能力退款订单号|退款金额|状态
	private StringBuilder refundRecToLine(RefundOrderDO rec,String appCnl){
		StringBuilder lineBuilder=new StringBuilder();
		lineBuilder.append(rec.getRfdOrdNo());
		lineBuilder.append("|");
		lineBuilder.append(rec.getOrderStatus());
		if(StringUtils.equals(appCnl,"CPI")||StringUtils.equals(appCnl,"CPO")){
			lineBuilder.append("|");
			lineBuilder.append(rec.getRfdUserAmt());
		}else{
			lineBuilder.append("|");
			lineBuilder.append(rec.getBusRdfOrdNo());
			lineBuilder.append("|");
			lineBuilder.append(rec.getRfdAmt());
		}
		lineBuilder.append("\n");
		return lineBuilder;
	}
}
