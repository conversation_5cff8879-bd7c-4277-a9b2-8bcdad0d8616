package com.hisun.lemon.csh.mq;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csh.utils.RouterUtils;
import com.hisun.lemon.csh.dto.order.NotifyResultDTO;
import com.hisun.lemon.csh.service.impl.*;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.hisun.lemon.framework.stream.MessageHandler;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2017年8月28日
 * @time 下午2:54:28
 *
 */
@Component(NoticeHandler.BEAN_NAME)
public class NoticeHandler implements MessageHandler<NotifyResultDTO>{
    public static final String BEAN_NAME="noticeHandler";
    private static final Logger logger = LoggerFactory.getLogger(NoticeHandler.class);

    @Resource
    ObjectMapper objectMapper;
    @Override
    public void onMessageReceive(GenericCmdDTO<NotifyResultDTO> genericCmdDTO) {
        String data = ObjectMapperHelper.writeValueAsString(objectMapper, genericCmdDTO.getBody(), true);
        logger.info("接收cpi模块通知数据 {}", data);
        NotifyResultDTO notifyResultDTO = genericCmdDTO.getBody();
        if(JudgeUtils.isNotNull(notifyResultDTO)){
            //调用通知处理接口
            final AbstractOrderService service = RouterUtils.select(null, notifyResultDTO.getCshOrderNo());
            if(JudgeUtils.isNotNull(service)){
                logger.info("实现类:"+service.toString());
                service.handlePpNotice(genericCmdDTO);
            }
        }

    }
}
