package com.hisun.lemon.csh.schedule;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csh.component.OrderCommonComponent;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.service.chk.AbstractChkFileService;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 定时任务调度器
 */
@Component
public class Scheduler {
	private static final Logger logger = LoggerFactory.getLogger(Scheduler.class);
	ExecutorService executorService = Executors.newFixedThreadPool(1);

	@Resource
	private List<AbstractChkFileService> scheduleService;

	@Resource
	OrderCommonComponent orderCommonComponent;

	@BatchScheduled(cron="0 0 0/1 * * ?")
	public void createChkFile(){
		logger.info("=================生成对账文件===================");
		scheduleService.stream().forEach(
				item -> executorService.submit(item)
		);
	}
//	@BatchScheduled(cron="0 0/1 * * * ?")
	@BatchScheduled(cron="0 0/15 * * * ?")
	public void orderExpTask(){
		logger.info("=================过期订单处理===================");
		List<OrderDO> list=orderCommonComponent.queryExpOrderList(200);
		logger.info("=================过期订单处理条数==================="+list.size());
		list.stream().filter(it->JudgeUtils.isNotNull(it)).forEach(
				items-> orderCommonComponent.handleOrderExp(items)
		);
	}
}
