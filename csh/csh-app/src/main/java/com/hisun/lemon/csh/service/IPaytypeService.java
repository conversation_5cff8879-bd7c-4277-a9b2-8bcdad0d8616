package com.hisun.lemon.csh.service;

import java.util.List;

import com.hisun.lemon.csh.dto.paytype.AppPaytypeResultDTO;
import com.hisun.lemon.csh.dto.paytype.PaytypeDTO;
import com.hisun.lemon.csh.entity.PaytypeDO;

public interface IPaytypeService {

	public void add(PaytypeDTO paytypeDto);
	
	public void del(Integer paytypeDto);
	
	public void update(PaytypeDTO paytypeDto);
	
	public List<PaytypeDO> list(PaytypeDTO paytypeDto);

	/**
	 *  找到合适的支付方式
	 * @param paytypeDo
	 * @return
	 */
	public PaytypeDO getPaytypes(PaytypeDO paytypeDo);

	/**
	 *  找到合适的支付方式
	 * @param paytypeDo
	 * @param busPaytypes 业务指定的支付方式
	 * @return
	 */
	public String getPaytypes(PaytypeDO paytypeDo,String busPaytypes);


	/**
	 * 获取支付信息
	 * @param paytypeDo
	 * @param busPaytypes 业务指定的支付方式
	 * @return
	 */
	public AppPaytypeResultDTO getPayInfo(PaytypeDO paytypeDo,String busPaytypes);

	/**
	 * 支持借记卡快捷
	 * @param payType
	 * @return
	 */
	public boolean supportDQP(String payType);

	/**
	 * 支持贷记卡快捷
	 * @param payType
	 * @return
	 */
	public boolean supportCQP(String payType);

	/**
	 * 支持余额
	 * @param payType
	 * @return
	 */
	public boolean supportBal(String payType);

	/**
	 * 检查指定位置是否支持
	 * @param payType
	 * @param index
	 * @return
	 */
	public boolean supportBit(String payType,int index);
}
