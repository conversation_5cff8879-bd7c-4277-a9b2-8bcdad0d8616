package com.hisun.lemon.csh.component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.hisun.lemon.csh.enums.*;
import org.springframework.stereotype.Component;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.RefundOrderDO;
import com.hisun.lemon.csh.service.impl.RefundOrderServiceImpl;
import com.hisun.lemon.framework.i18n.LocaleMessageSource;
import com.hisun.lemon.framework.utils.IdGenUtils;

/**
 * 退款
 *
 * <AUTHOR>
 *
 */
@Component
public class RefundComponent {
	@Resource
	private AcmComponent acmComponent;
	@Resource
	private RefundOrderServiceImpl refundService;
	@Resource
	LocaleMessageSource localeMessageSource;

	/**
	 * 计算退款撤销金额及优惠
	 * 
	 * @param refundDO
	 * @param orginOrder
	 * @return
	 */
	public RefundOrderDO compute(RefundOrderDO refundDO, OrderDO orginOrder) {
		RefundOrderDO refund = new RefundOrderDO();
		if (JudgeUtils.isNotEmpty(orginOrder.getCouponType())) {
			//退回账户金额
			BigDecimal rfdUserAmt = BigDecimal.valueOf(0);
			//申请退款
			BigDecimal rfdAmt = refundDO.getRfdAmt();
			// 海币
			if (StringUtils.equals(orginOrder.getCouponType(), CouponType.H_COUPON.getType())) {
				BigDecimal chanageAmt = orginOrder.getLeftCouponAmt()
						.multiply(BigDecimal.valueOf(CshConstants.H_USD_RATE)).setScale(2, BigDecimal.ROUND_DOWN);
				// 海币足够退款
				if (chanageAmt.compareTo(rfdAmt) == 0) {
					// 使用海币抵扣款
					refund.setRfdUserAmt(BigDecimal.valueOf(0));
					refund.setCouponAmt(orginOrder.getLeftCouponAmt());
				}
				if (chanageAmt.compareTo(rfdAmt) > 0) {
					BigDecimal Hcount = rfdAmt.multiply(BigDecimal.valueOf(CshConstants.M_USD_RATE)).setScale(0,
							BigDecimal.ROUND_DOWN);
					refund.setCouponAmt(Hcount);
					refund.setRfdUserAmt(BigDecimal.valueOf(0));

				}
				if (rfdAmt.compareTo(chanageAmt) > 0) {
					BigDecimal getRfdAmt = refundDO.getRfdAmt();
					rfdUserAmt = getRfdAmt.subtract(chanageAmt);
					refund.setRfdUserAmt(rfdUserAmt);
					refund.setCouponAmt(orginOrder.getLeftCouponAmt());
				}
				refund.setCouponType(CouponType.H_COUPON.getType());
			}else
			{
				if (orginOrder.getLeftCouponAmt().compareTo(refundDO.getRfdAmt()) >= 0) {
					BigDecimal getRfdAmt = refundDO.getRfdAmt();
					refund.setRfdUserAmt(BigDecimal.valueOf(0));
					//BigDecimal rfdCoupon=orginOrder.getLeftCouponAmt().subtract(getRfdAmt);
					refund.setCouponAmt(getRfdAmt);
				}
				
				if (refundDO.getRfdAmt().compareTo(orginOrder.getLeftCouponAmt()) > 0) {
					BigDecimal getRfdAmt = refundDO.getRfdAmt();
					rfdUserAmt =getRfdAmt.subtract(orginOrder.getLeftCouponAmt());
					refund.setRfdUserAmt(rfdUserAmt);
					refund.setCouponAmt(orginOrder.getLeftCouponAmt());
				}
				refund.setCouponType(orginOrder.getCouponType());
			}
		}
		if (JudgeUtils.isEmpty(orginOrder.getCouponType())
				|| StringUtils.equals(orginOrder.getCouponType(), CouponType.NONE.getType())) {
			// 无优惠
			refund.setCouponAmt(BigDecimal.valueOf(0));
			refund.setRfdUserAmt(refundDO.getRfdAmt());
			refund.setCouponType(CouponType.NONE.getType());
		}
		return refund;
	}

	/**
	 * 充值长款退款订单校验
	 *
	 * @param refundDO
	 * @return 原订单对象
	 */
	public OrderDO checkChargeOrder(RefundOrderDO refundDO, OrderDO orderDO) {
		if (JudgeUtils.isNull(orderDO)) {
			throw new LemonException("CSH20063");
		}
		// 申请退款金额不能大于订单金额
		if (refundDO.getRfdAmt().compareTo(orderDO.getTotalAmt()) > 0) {
			throw new LemonException("CSH20068");
		}
		// 只支持 P状态 W状态
		if (!StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.PRE_PAY.getValue())
				&& !StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.WAIT_PAY.getValue())) {
			throw new LemonException("CSH10077");
		}

		if (StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())) {
			if (!StringUtils.equals(refundDO.getBusType(), BussinessType.RFD_LONG.getValue())) {
				throw new LemonException("CSH10078");
			}
		}

		if (JudgeUtils.isBlank(orderDO.getCrdPayType()) || StringUtils.equals(CshConstants.CRD_TYPE_NONE, orderDO.getCrdPayType())) {
			throw new LemonException("CSH10079");
		}
		return orderDO;
	}

	/**
	 * 退款订单校验
	 *
	 * @param refundDO
	 * @return 原订单对象
	 */
	public OrderDO checkOrder(RefundOrderDO refundDO, OrderDO orderDO,String busType) {
		if (JudgeUtils.isNull(orderDO)) {
			throw new LemonException("CSH20063");
		}
		// 申请退款金额不能大于订单金额
		if (refundDO.getRfdAmt().compareTo(orderDO.getTotalAmt()) > 0) {
			throw new LemonException("CSH20068");
		}
		//针对于部分退款的时候 申请金额不能大于可退金额
		if(refundDO.getRfdAmt().compareTo(orderDO.getLeftTotalAmt())>0)
		{
			throw new LemonException("CSH20105");
		}
		// 原订单状态不是“成功” 或部分退款不支持退款
		if (!StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.SUCC.getValue())
				&& !StringUtils.equals(orderDO.getOrderStatus(), OrderStatus.PART_REFUND.getValue())) {
			throw new LemonException("CSH20021");
		}

		if (StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())) {
			if (!StringUtils.equals(busType, BussinessType.RFD_CONSUME.getValue())
					&& !StringUtils.equals(busType, BussinessType.RFD_CONSUME_FAIL.getValue())) {
				throw new LemonException("CSH20081");
			}
		}

		if (StringUtils.equals(orderDO.getTxType(), TradeType.PAYMENT.getType())) {
			if (!StringUtils.equals(busType, BussinessType.RFD_PAYMENT.getValue())
					&& !StringUtils.equals(busType, BussinessType.RFD_PAYMENT_FAIL.getValue())) {
				throw new LemonException("CSH20065");
			}
		}
		return orderDO;
	}

	/**
	 * 撤销订单校验
	 *
	 * @param refundDO
	 * @return 原订单对象
	 */
	public OrderDO checkUndoOrder(RefundOrderDO refundDO, OrderDO orderDO,String busType) {
		if (JudgeUtils.isNull(orderDO)) {
			throw new LemonException("CSH20063");
		}
		// 判断订单交易时间是否为 今日 是才支持撤销 CSH10038
		LocalDate acTm = orderDO.getAcTm();
		boolean isToday = DateTimeUtils.isToday(acTm);
		if (!isToday) {
			throw new LemonException("CSH10038");
		}

		// 原订单状态 部分退款和全部退款或已撤销不能 撤销
		if (StringUtils.equals(OrderStatus.PART_REFUND.getValue(), orderDO.getOrderStatus())
				|| StringUtils.equals(OrderStatus.REFUND.getValue(), orderDO.getOrderStatus())
				|| StringUtils.equals(orderDO.getOrderStatus(),"R3")) {
			throw new LemonException("CSH10039");
		}
		// 撤销 必须撤销全部的钱
		if (StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
				&& !StringUtils.equals(busType, BussinessType.UNDO_CONSUME.getValue())) {
			throw new LemonException("CSH20082");
		}
		if (StringUtils.equals(orderDO.getTxType(),TradeType.PAYMENT.getType())
				&& !StringUtils.equals(busType, BussinessType.UNDO_PAYMENT.getValue())) {
			throw new LemonException("CSH20084");
		}
		if (StringUtils.equals(orderDO.getTxType(), TradeType.FINANC.getType())
				&& !StringUtils.equals(busType, BussinessType.RFD_FINANC.getValue())) {
			throw new LemonException("CSH20083");
		}
		return orderDO;
	}

	/**
	 * 账务处理
	 *
	 * @param refundOrderDO
	 */
	public List<AccountingReqDTO> account(RefundOrderDO refundOrderDO) {
		String rfdType = refundOrderDO.getRfdType();
		String acmJrnNo = IdGenUtils.generateIdWithDate(CshConstants.RFD_GEN_PRE, 14);
		String counponType = refundOrderDO.getCouponType();
		AccountingReqDTO cshEItemReqDTO = null;
		AccountingReqDTO rdfEcouponReqDTO = null;//电子券
		AccountingReqDTO cshDItemReqDTO = null;
		AccountingReqDTO rdfDcouponReqDTO = null;//折扣券
		AccountingReqDTO cshHItemReqDTO = null;
		AccountingReqDTO rdfHcouponReqDTO = null;//海币
		
		AccountingReqDTO cshBItemReqDTO = null;
		AccountingReqDTO bankItemReqDTO = null;
		// 资金类型为现金
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		List<AccountingReqDTO> acList=new ArrayList();
		// 退优惠 账务处理
		if (refundOrderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			switch (counponType) {
			// 借：其他应付款-暂收-收银台
			case "01":
				// 借:其他应付款-暂收-收银台
				cshEItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getCouponAmt(), null,
						ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG, AcItem.O_CSH.getValue(), null,
						null, null, null, "退优惠  电子券");
				// 贷：应收账款-电子券发放-XX公司
				rdfEcouponReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getCouponAmt(), null,
						ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG, AcItem.I_E_COUPON_COMPANY.getValue(),
						null, null, null, null, "退优惠  电子券");
				acList.add(cshEItemReqDTO);
				acList.add(rdfEcouponReqDTO);
				break;
			case "03":
				// 借:其他应付款-暂收-收银台
				cshDItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getCouponAmt(), null,
						ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG, AcItem.O_CSH.getValue(), null,
						null, null, null, "退优惠  折扣券");
				// 贷：应收账款-电子券发放-XX公司
				rdfDcouponReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getCouponAmt(), null,
						ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,AcItem.I_E_COUPON_COMPANY.getValue(),
						null, null, null, null, "退优惠 折扣券");
				acList.add(cshDItemReqDTO);
				acList.add(rdfDcouponReqDTO);
				break;

			// 海币
			case "02":
				BigDecimal rfdCoupAmt = refundOrderDO.getCouponAmt();
				BigDecimal chanageAmt = rfdCoupAmt.multiply(BigDecimal.valueOf(CshConstants.H_USD_RATE)).setScale(2,
						BigDecimal.ROUND_DOWN);
				// 借:其他应付款-暂收-收银台
				cshHItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, chanageAmt, null,
						ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG, AcItem.O_CSH.getValue(), null,
						null, null, null, "退优惠  海币");

				// 贷：其他应收款-中转挂账-海币
				rdfHcouponReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, chanageAmt, null,
						ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG, AcItem.O_H_COUPON_MID.getValue(),
						null, null, null, null, "退优惠  海币");
				acList.add(cshHItemReqDTO);
				acList.add(rdfHcouponReqDTO);
				break;
			default:
				break;
			}
		}

		// 当退回用户钱>0
		if (refundOrderDO.getRfdUserAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			switch (rfdType) {
			case CshConstants.RFD_TYPE_R:
				// 借：其他应付款-暂收-收银台
				cshBItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, refundOrderDO.getRfdUserAmt(), null,
						ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG, AcItem.O_CSH.getValue(), null,
						null, null, null, "退回到银行卡");

				// 贷：其他应付款-渠道退款-XX银行
				bankItemReqDTO = acmComponent.createAccountingReqDTO(refundOrderDO.getRfdOrdNo(), acmJrnNo,
						refundOrderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL,refundOrderDO.getRfdUserAmt(), null,
						ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,AcItem.O_CNL_BANK.getValue(), null,
						null, null, null, "退回到银行卡");
				acList.add(cshBItemReqDTO);
				acList.add(bankItemReqDTO);
				break;
			default:
				break;
			}
		}
        return acList;
	}
	
	
	/**
	 * 国际化商品描述信息
	 * 
	 * @param busType
	 * @param args
	 * @return
	 */
	public String getViewOrderInfo(String busType, Object[] args) {
		try {
			String key = "view.payMod." + busType;
			return localeMessageSource.getMessage(key, args);
		} catch (Exception e) {

		}
		return null;
	}

}
