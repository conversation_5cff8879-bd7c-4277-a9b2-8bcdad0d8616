package com.hisun.lemon.csh.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.DmAccountDetailRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.client.CregisClient;
import com.hisun.lemon.cpi.dto.CregisRsp;
import com.hisun.lemon.cpi.dto.PaymentPrepareReqDTO;
import com.hisun.lemon.cpi.dto.PaymentReqDTO;
import com.hisun.lemon.csh.component.AcmComponent;
import com.hisun.lemon.csh.component.OrderCommonComponent;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dao.IOrderDao;
import com.hisun.lemon.csh.dao.IPayJrnDao;
import com.hisun.lemon.csh.dao.ITransferOrderDao;
import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.TranOrderAuditDTO;
import com.hisun.lemon.csh.dto.cashier.TranOrderReqDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.csh.entity.TransferOrderDO;
import com.hisun.lemon.csh.enums.*;
import com.hisun.lemon.csh.service.IAuditOrderService;
import com.hisun.lemon.csh.service.IDmOrderService;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.pwm.client.PwmWithdrawClient;
import com.hisun.lemon.pwm.constants.PwmConstants;
import com.hisun.lemon.pwm.dto.GetByOrderDTO;
import com.hisun.lemon.pwm.dto.UpdateOrderDTO;
import com.hisun.lemon.pwm.dto.WithdrawOrderRspDTO;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import com.hisun.lemon.tam.client.TransferOrderClient;
import com.hisun.lemon.tam.constants.CcyAcItemEnum;
import com.hisun.lemon.tam.constants.DmPlatformEnum;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tam.constants.transferFeeAcItemEnum;
import com.hisun.lemon.tam.dto.GetDmPlatAddrReqDTO;
import com.hisun.lemon.tam.dto.GetDmPlatAddrRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AuditOrderServiceImpl implements IAuditOrderService {

	private static final Logger logger = LoggerFactory.getLogger(AuditOrderServiceImpl.class);

	@Resource
	private ObjectMapper objectMapper;

	@Resource
	private IOrderDao orderDao;

	@Resource
	private ITransferOrderDao transferDao;

	@Resource
	private IPayJrnDao payJrnDao;

	@Resource
	private AcmComponent acmComponent;

	@Resource
	protected RiskCheckClient riskCheckClient;

	@Resource
	protected OrderCommonComponent orderCommonComponent;

	@Resource
	private CregisClient cregisClient;

	@Resource
	private AccountManagementClient acmManageClient;

	@Value("${Cregis.fundFlowCode}")
	private String fundFlowCode;

	@Resource
	private IDmOrderService dmOrderService;

	@Resource
	private PwmWithdrawClient pwmWithdrawClient;

	@Resource
	private TransferOrderClient transferOrderClient;

	@Resource
	private AccountManagementClient accountManagementClient;

	/**
	 * 获取收银台转账订单信息列表
	 *
	 * @param requestDTO 查询条件
	 * @return
	 */
	@Override
	public List<OrderDO> queryCashierTranOrders(TranOrderReqDTO requestDTO) {

		logger.info("查询收银台转账订单信息列表");
		Map<Object, Object> map=new HashMap<>();
		map.put("orderNo", requestDTO.getOrderNo());
		map.put("payerId", requestDTO.getPayerId());
		map.put("payeeId", requestDTO.getPayeeId());
		map.put("busOrderNo", requestDTO.getBusOrderNo());
		map.put("orderStatus", requestDTO.getStatus());

		//根据条件查询商户账单信息，分页
		List<OrderDO> orderDTOList = null;
		PageInfo<OrderDO> pageInfo = PageUtils.pageQueryWithCount(requestDTO.getPageNo(), requestDTO.getPageSize(), ()-> orderDao.queryNotFinalAuditList(map));

		if(JudgeUtils.isNotNull(pageInfo)) {
			orderDTOList = pageInfo.getList();
		}
		return orderDTOList;
	}

	/**
	 * 转账订单审核
	 *
	 * @param req 查询条件
	 * @return
	 */
	@Override
	@Transactional
	public void audit(TranOrderAuditDTO req) {
		String status = req.getStatus();
		OrderDO orderDO = orderDao.get(req.getOrderNo());
		String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, orderDO, true);
		logger.info("订单审核开始：{}", reqData);
		if (orderDO == null) {
			logger.info("根据订单号:{}没有找到相应的csh转账订单", req.getOrderNo());
			throw new LemonException("TFM30004");
		}
		// 检验原订单状态
		if (OrderStatus.SUCC.getValue().equals(orderDO.getOrderStatus()) || OrderStatus.FAIL.getValue().equals(orderDO.getOrderStatus())) {
			logger.info("订单:{}的状态:{}有误", req.getOrderNo(), orderDO.getOrderStatus());
			throw new RuntimeException("TAM10032");
		}
		// 初审只需要更新订单状态
		if (OrderStatus.FIRST_AUDIT_PASS.getValue().equals(status)) {
			logger.info("订单:{}初审通过", req.getOrderNo());
			orderDO.setOrderStatus(status);
//			orderDO.setRemark(req.getReason());
			orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());

			orderDO.setFirstAuditOpinion(req.getReason());
			orderDO.setFirstAuditTime(DateTimeUtils.getCurrentLocalDateTime());
			orderDO.setFirstAuditUser(req.getUserId());
			orderDO.setFirstAuditResult("APPROVED");
			orderDao.update(orderDO);
			return;
		}
		// 终审通过    更新订单状态，进行账务处理
		if (OrderStatus.SUCC.getValue().equals(status)) {
			logger.info("订单:{}终审通过，执行后续转账逻辑", req.getOrderNo());
			if (!OrderStatus.FIRST_AUDIT_PASS.getValue().equals(orderDO.getOrderStatus())) {
				logger.info("订单:{}未通过初审，请先进行初审", req.getOrderNo());
				throw new LemonException("TAM10034");
			}

			// 数币网络类型转换映射
			Map<String, String> dmNetworkMapping = new HashMap<>();
			dmNetworkMapping.put(ACMConstants.NETWORK_TRON, "tron-nile");
			dmNetworkMapping.put(ACMConstants.NETWORK_ETH, "ethereum-sepolia");
			// 如果业务类型为数币转账 : DZ02,则需调用 Cregis 转账
			if (JudgeUtils.equals(TamConstants.BUS_TYPE_DM_TRANSFER02, orderDO.getBusType())) {
				// 获取转账订单
				TransferOrderDO transferOrderDO = transferDao.get(orderDO.getBusOrderNo());
				if (JudgeUtils.isNull(transferOrderDO)) {
					logger.info("订单:{}的转账订单不存在", req.getOrderNo());
					LemonException.throwBusinessException("TAM30006");
				}
				// 验证账户编号
				if (JudgeUtils.isBlank(transferOrderDO.getFkAcNo())) {
					logger.error("订单:{}的转账订单账号不存在", req.getOrderNo());
					throw new LemonException(ACMMessageCode.AC_NOT_EXIST);
				}
				// 查询双方账户信息
				// 付款方账户信息
				GenericRspDTO<DmAccountDetailRspDTO> fkRspDTO = acmManageClient.queryDmAccountDetail(transferOrderDO.getFkAcNo());
				if (JudgeUtils.isNotSuccess(fkRspDTO.getMsgCd())) {
					LemonException.throwLemonException("TAM10031");
				}
				DmAccountDetailRspDTO fkAcBalRspDTO = fkRspDTO.getBody();

				//组装cregis请求体
				PaymentReqDTO paymentReqDTO = new PaymentReqDTO();
				List<String> includes = new ArrayList<>();
				paymentReqDTO.setOrderId(orderDO.getOrderNo());
				paymentReqDTO.setCoinId(transferOrderDO.getOrderCcy());
				paymentReqDTO.setNetwork(dmNetworkMapping.getOrDefault(fkAcBalRspDTO.getNetwork(), fkAcBalRspDTO.getNetwork()));
				paymentReqDTO.setLang(TamConstants.lang);
				paymentReqDTO.setFundFlowCode(fundFlowCode);
				includes.add(fkAcBalRspDTO.getAddress());
				paymentReqDTO.setIncludes(includes);
				PaymentPrepareReqDTO.PayTo[] payToList = new PaymentPrepareReqDTO.PayTo[2];
				payToList[0] = new PaymentPrepareReqDTO.PayTo();
				payToList[0].setTo(transferOrderDO.getMblNo());
				payToList[0].setReadableAmount(transferOrderDO.getAmount().subtract(transferOrderDO.getFee()));

				GenericDTO<GetDmPlatAddrReqDTO> dtoGenericDTO = new GenericDTO<>();
				GetDmPlatAddrReqDTO getDmPlatAddrReqDTO = new GetDmPlatAddrReqDTO();
				getDmPlatAddrReqDTO.setNetwork(dmNetworkMapping.getOrDefault(fkAcBalRspDTO.getNetwork(), fkAcBalRspDTO.getNetwork()));
				getDmPlatAddrReqDTO.setCcy(fkAcBalRspDTO.getCoinId());
				getDmPlatAddrReqDTO.setType(TamConstants.F_TYPE);
				dtoGenericDTO.setBody(getDmPlatAddrReqDTO);
				GenericRspDTO<GetDmPlatAddrRspDTO> dmPlatAddr = transferOrderClient.getDmPlatAddr(dtoGenericDTO);
				if (JudgeUtils.isNotSuccess(dmPlatAddr.getMsgCd()) || JudgeUtils.isNull(dmPlatAddr.getBody())) {
					LemonException.throwLemonException("CPI80003");
				}
				payToList[1] = new PaymentPrepareReqDTO.PayTo();
				payToList[1].setTo(dmPlatAddr.getBody().getAddress());
				payToList[1].setReadableAmount(transferOrderDO.getFee());

				paymentReqDTO.setPayToList(payToList);
				CregisRsp<NoBody> transferAll = cregisClient.transferAll(paymentReqDTO);
//				transferAll.setCode("200");
				if (JudgeUtils.equals("200", transferAll.getCode())) {
					logger.info("订单：" + orderDO.getOrderNo() + " 调用Cregis转账成功");
					orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
					orderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
					orderDO.setSecondAuditUser(req.getUserId());
					orderDO.setSecondAuditResult("APPROVED");
					orderDO.setSecondAuditOpinion(req.getReason());
					orderDO.setOrderStatus(OrderStatus.WAIT_CALLBACK.getValue());
					orderDao.update(orderDO);
					// 后续处理
//					GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
//					HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
//					// csh_order表中订单编号
//					handleFinanceDTO.setOrderNo(orderDO.getOrderNo());
//					// 业务类型
//					handleFinanceDTO.setBusType(orderDO.getBusType());
//					handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
//					handleCregisOk(handleFinanceDTOGenericDTO);
					return;
				} else {
					logger.info("订单：" + orderDO.getOrderNo() + " 调用Cregis转账失败");
					orderDO.setOrderStatus(OrderStatus.FAIL.getValue());
					orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
					orderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
					orderDO.setSecondAuditUser(req.getUserId());
					orderDO.setSecondAuditResult("APPROVED");
					orderDO.setSecondAuditOpinion(req.getReason());
					orderDao.update(orderDO);
					//失败,账务冲正,更新订单状态
					GenericDTO<HandleFailTranDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
					HandleFailTranDTO handleFailTranDTO = new HandleFailTranDTO();
					// csh_order表中订单编号
					handleFailTranDTO.setOrderNo(orderDO.getOrderNo());
					// 业务类型
					handleFailTranDTO.setBusType(orderDO.getBusType());
					handleFinanceDTOGenericDTO.setBody(handleFailTranDTO);
					dmOrderService.handleFailTransfer(handleFinanceDTOGenericDTO);
					return;
				}
			}
			// 如果业务类型为数币提现 : DX01,调用 Cregis 提现
			if (JudgeUtils.equals(TamConstants.BUS_TYPE_DM_WITHDRAW, orderDO.getBusType())) {
				// 获取提现订单
				GenericDTO<GetByOrderDTO> genericDTO = new GenericDTO<>();
				GetByOrderDTO getByOrderDTO = new GetByOrderDTO();
				getByOrderDTO.setOrderNo(orderDO.getBusOrderNo());
				genericDTO.setBody(getByOrderDTO);
				GenericRspDTO<WithdrawOrderRspDTO> byOrder = pwmWithdrawClient.getByOrder(genericDTO);
				if (JudgeUtils.isNotSuccess(byOrder.getMsgCd())){
					LemonException.throwBusinessException(byOrder.getMsgCd());
				}
				if (JudgeUtils.isNull(byOrder.getBody())) {
					logger.info("订单:{}的提现订单不存在", req.getOrderNo());
					LemonException.throwBusinessException("TAM30006");
				}
				// 查询双方账户信息
				// 付款方账户信息
				WithdrawOrderRspDTO withdrawOrderRspDTO = byOrder.getBody();
				GenericRspDTO<DmAccountDetailRspDTO> fkRspDTO = acmManageClient.queryDmAccountDetail(withdrawOrderRspDTO.getFkAcNo());
				if (JudgeUtils.isNotSuccess(fkRspDTO.getMsgCd())) {
					LemonException.throwLemonException("TAM10031");
				}
				DmAccountDetailRspDTO fkAcBalRspDTO = fkRspDTO.getBody();

				//组装cregis请求体
				PaymentReqDTO paymentReqDTO = new PaymentReqDTO();
				List<String> includes = new ArrayList<>();
				paymentReqDTO.setOrderId(orderDO.getOrderNo());
				paymentReqDTO.setCoinId(withdrawOrderRspDTO.getOrderCcy());
				paymentReqDTO.setNetwork(dmNetworkMapping.getOrDefault(fkAcBalRspDTO.getNetwork(), fkAcBalRspDTO.getNetwork()));
				paymentReqDTO.setLang(TamConstants.lang);
				paymentReqDTO.setFundFlowCode(fundFlowCode);
				includes.add(fkAcBalRspDTO.getAddress());
				paymentReqDTO.setIncludes(includes);
				PaymentPrepareReqDTO.PayTo[] payToList = new PaymentPrepareReqDTO.PayTo[2];
				payToList[0] = new PaymentPrepareReqDTO.PayTo();
				payToList[0].setTo(withdrawOrderRspDTO.getAddress());
				payToList[0].setReadableAmount(withdrawOrderRspDTO.getWcApplyAmt().subtract(withdrawOrderRspDTO.getFeeAmt()));
				GenericDTO<GetDmPlatAddrReqDTO> dtoGenericDTO = new GenericDTO<>();
				GetDmPlatAddrReqDTO getDmPlatAddrReqDTO = new GetDmPlatAddrReqDTO();
				getDmPlatAddrReqDTO.setNetwork(dmNetworkMapping.getOrDefault(fkAcBalRspDTO.getNetwork(), fkAcBalRspDTO.getNetwork()));
				getDmPlatAddrReqDTO.setCcy(fkAcBalRspDTO.getCoinId());
				getDmPlatAddrReqDTO.setType(TamConstants.F_TYPE);
				dtoGenericDTO.setBody(getDmPlatAddrReqDTO);
				GenericRspDTO<GetDmPlatAddrRspDTO> dmPlatAddr = transferOrderClient.getDmPlatAddr(dtoGenericDTO);
				if (JudgeUtils.isNotSuccess(dmPlatAddr.getMsgCd()) || JudgeUtils.isNull(dmPlatAddr.getBody())) {
					LemonException.throwLemonException("CPI80003");
				}
				payToList[1] = new PaymentPrepareReqDTO.PayTo();
				payToList[1].setTo(dmPlatAddr.getBody().getAddress());
				payToList[1].setReadableAmount(withdrawOrderRspDTO.getFeeAmt());
				paymentReqDTO.setPayToList(payToList);
				logger.info("订单:{} 调用Cregis提现...", orderDO.getOrderNo());
				CregisRsp<NoBody> transferAll = cregisClient.transferAll(paymentReqDTO);
				if (JudgeUtils.equals("200", transferAll.getCode())) {
					logger.info("订单：" + orderDO.getOrderNo() + " 调用Cregis提现成功");
					orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
					orderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
					orderDO.setSecondAuditUser(req.getUserId());
					orderDO.setSecondAuditResult("APPROVED");
					orderDO.setSecondAuditOpinion(req.getReason());
					orderDO.setOrderStatus(OrderStatus.WAIT_CALLBACK.getValue());
					orderDao.update(orderDO);
					// 后续处理
//					GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
//					HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
//					// csh_order表中订单编号
//					handleFinanceDTO.setOrderNo(orderDO.getOrderNo());
//					// 业务类型
//					handleFinanceDTO.setBusType(orderDO.getBusType());
//					handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
//					handleWithdrawOk(handleFinanceDTOGenericDTO);
					return;
				} else {
					logger.info("订单：" + orderDO.getOrderNo() + " 调用Cregis提现失败");
					orderDO.setOrderStatus(OrderStatus.FAIL.getValue());
					orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
					orderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
					orderDO.setSecondAuditUser(req.getUserId());
					orderDO.setSecondAuditResult("APPROVED");
					orderDO.setSecondAuditOpinion(req.getReason());
					orderDao.update(orderDO);
					//失败,账务冲正,更新订单状态
					GenericDTO<HandleFailTranDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
					HandleFailTranDTO handleFailTranDTO = new HandleFailTranDTO();
					// csh_order表中订单编号
					handleFailTranDTO.setOrderNo(orderDO.getOrderNo());
					// 业务类型
					handleFailTranDTO.setBusType(orderDO.getBusType());
					handleFinanceDTOGenericDTO.setBody(handleFailTranDTO);
					dmOrderService.handleFailWithdraw(handleFinanceDTOGenericDTO);
					return;
				}
			}
			orderDO.setOrderStatus(OrderStatus.SUCC.getValue());
			orderDO.setRemark(req.getReason());
			orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
			orderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
			orderDO.setSecondAuditUser(req.getUserId());
			orderDO.setSecondAuditResult("APPROVED");
			orderDO.setSecondAuditOpinion(req.getReason());
			orderDao.update(orderDO);
			// 终审通过后续处理
			auditSuccessOrder(orderDO);
			return;
		}
		// 审核拒绝   更新订单状态，进行账务冲正
		if (OrderStatus.FAIL_1.getValue().equals(status) || OrderStatus.FAIL_2.getValue().equals(status) ||
				OrderStatus.FAIL.getValue().equals(status)) {
			if(OrderStatus.FAIL_1.getValue().equals(status)){
				orderDO.setOrderStatus(OrderStatus.FAIL.getValue());
				orderDO.setFirstAuditResult("REJECTED");
				orderDO.setRejectReason(req.getReason());
				orderDO.setFirstAuditTime(DateTimeUtils.getCurrentLocalDateTime());
				orderDO.setFirstAuditUser(req.getUserId());

			}
			if(OrderStatus.FAIL_2.getValue().equals(status)){
				orderDO.setOrderStatus(OrderStatus.FAIL.getValue());
				orderDO.setSecondAuditResult("REJECTED");
				orderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
				orderDO.setSecondAuditUser(req.getUserId());
				orderDO.setSecondAuditOpinion(req.getReason());
			}
			logger.info("订单:{}审核拒绝，进行账务冲正", req.getOrderNo());
			orderDO.setRejectReason(orderDO.getRejectReason());
			orderDO.setOrderStatus(status);
			orderDO.setRemark(req.getReason());
			orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
			orderDao.update(orderDO);
			if (JudgeUtils.equals(TamConstants.BUS_TYPE_DM_WITHDRAW, orderDO.getBusType())){
				//数币提现
				auditRejectWithdrawOrder(orderDO);
			}else {
				auditRejectOrder(orderDO);
			}
		}
	}

	private void auditRejectWithdrawOrder(OrderDO orderDO) {
		// 获取提现订单
		GenericDTO<GetByOrderDTO> genericDTO = new GenericDTO<>();
		GetByOrderDTO getByOrderDTO = new GetByOrderDTO();
		getByOrderDTO.setOrderNo(orderDO.getBusOrderNo());
		genericDTO.setBody(getByOrderDTO);
		GenericRspDTO<WithdrawOrderRspDTO> byOrder = pwmWithdrawClient.getByOrder(genericDTO);
		if (JudgeUtils.isNotSuccess(byOrder.getMsgCd())){
			LemonException.throwBusinessException(byOrder.getMsgCd());
		}
		WithdrawOrderRspDTO withdrawOrder = byOrder.getBody();
		// 原订单不存在
		if (JudgeUtils.isNull(withdrawOrder)) {
			throw new LemonException("TAM20001");
		}
		// 订单已经成功
		if (StringUtils.equals(withdrawOrder.getOrderStatus(), PwmConstants.WITHDRAW_ORD_S1)) {
			logger.info("订单已成功");
			throw new LemonException("TAM20011");
		}

		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		List<AccountingReqDTO> acUserList = new ArrayList();
		// 交易金额
		BigDecimal totalAmt = orderDO.getTotalAmt();
		// 流水号
		String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
		payJrnNo = orderDO.getBusType() + payJrnNo;
		// 资金类型
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		// 付款方用户id
		String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType, orderDO.getCcy());

		// 借:其他应付款-暂收-收银台 -102
		cshItemReqDTO = acmComponent.createAccountingReqDTO(withdrawOrder.getOrderNo(), payJrnNo, withdrawOrder.getTxType(),
				ACMConstants.ACCOUNTING_NOMARL, totalAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
				ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null, null, null, "转账out");

		// 贷：其他应付款-支付账户-现金账户 +102
		userAccountReqDTO = acmComponent.createAccountingReqDTO(withdrawOrder.getOrderNo(), payJrnNo,
				withdrawOrder.getTxType(), ACMConstants.ACCOUNTING_NOMARL, totalAmt, balAcNo, ACMConstants.USER_AC_TYP,
				balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "转账out");
		acUserList.add(cshItemReqDTO);
		acUserList.add(userAccountReqDTO);
		acmComponent.requestAc(acUserList);
		logger.info("账务冲正处理完成=========================");


		// 获取提现订单流水
		PayJrnDO payJrnDO = payJrnDao.getByPayOrderNo(orderDO.getOrderNo());
		if (payJrnDO == null) {
			throw new LemonException("TAM10033");
		}
		// 更新提现交易流水
		PayJrnDO updPayJrnDO = new PayJrnDO();
		updPayJrnDO.setPayJrnNo(payJrnDO.getPayJrnNo());
		updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_F);
		updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
		int num1 = payJrnDao.update(updPayJrnDO);
		if (num1 != 1) {
			throw new LemonException("CSH20009");
		}

		//设置操作员
		Map<String, Map<Object, Object>> extMap = null;
		if (JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
				&& JudgeUtils.isNotNull(withdrawOrder.getUserId())) {
			extMap = new HashMap<>();
			Map<Object, Object> map = new HashMap<>();
			map.put("loginId", withdrawOrder.getUserId());
			extMap.put(TradeType.CONSUME.getType(), map);
		}

		//同步账单
		orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL, true, "1234", extMap);

		//更新外围数币提现订单
		GenericDTO<UpdateOrderDTO> updateGenericDTO = new GenericDTO<>();
		UpdateOrderDTO updateOrderDTO = new UpdateOrderDTO();
		updateOrderDTO.setOrderNo(orderDO.getBusOrderNo());
		updateOrderDTO.setOrderStatus(PwmConstants.WITHDRAW_ORD_F1);
		updateGenericDTO.setBody(updateOrderDTO);
		GenericRspDTO<WithdrawOrderRspDTO> genericRspDTO = pwmWithdrawClient.updateOrder(updateGenericDTO);
		if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
			LemonException.throwBusinessException(genericRspDTO.getMsgCd());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void handleCregisOk(GenericDTO<HandleFinanceDTO> req) {
		String orderNo = req.getBody().getOrderNo();
		OrderDO orderDO = orderDao.get(orderNo);
		//更新订单状态
		orderDO.setOrderStatus(OrderStatus.SUCC.getValue());
		orderDO.setOrderSuccTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setTxHash(req.getBody().getTxHash());
//		orderDO.setRemark("数币链上转账成功");
		orderDao.update(orderDO);

		// 转账订单号
		String orderId = orderDO.getBusOrderNo();
		TransferOrderDO transferDB = new TransferOrderDO();
		transferDB = transferDao.get(orderId);
		// 原订单不存在
		if (JudgeUtils.isNull(transferDB)) {
			throw new LemonException("TAM20001");
		}
		// 订单已经成功
		if (StringUtils.equals(transferDB.getOrderSts(), TamConstants.ORD_STS_S)) {
			logger.info("订单已成功");
			throw new LemonException("TAM20011");
		}
		//根据币种选择内部科目号
		String itemNo = "USDT".equals(orderDO.getCcy()) ? TamConstants.DM_USDT_SK_AC_NO : TamConstants.DM_USDC_SK_AC_NO;

		BigDecimal amountSum = orderDO.getTotalAmt();
		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		List<AccountingReqDTO> acUserList = new ArrayList();
		// 实际交易金额
		BigDecimal amount = orderDO.getLeftBalAmt();
		// 流水号
		String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
		payJrnNo = orderDO.getBusType() + payJrnNo;
		// 资金类型
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		// 收款方用户id
		String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayeeId(), balCapType, orderDO.getCcy());
		// 借:其他应付款-暂收-收银台 100
		cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo, transferDB.getTxType(),
				ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
				ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null, null, null, "转账out");
		// 贷：其他应付款-支付账户-现金账户 100
		userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo,
				transferDB.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, itemNo, ACMConstants.ITM_AC_TYP,
				balCapType, ACMConstants.AC_C_FLG, itemNo, null, null, null, null, "转账out");
		acUserList.add(cshItemReqDTO);
		acUserList.add(userAccountReqDTO);
		if (orderDO.getFee().compareTo(BigDecimal.valueOf(0)) > 0) {
			// 贷：手续费收入-支付账户-转账 2
			feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo,
					transferDB.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderDO.getFee(), null, ACMConstants.ITM_AC_TYP, balCapType,
					ACMConstants.AC_C_FLG,  "USDT".equals(orderDO.getCcy()) ? TamConstants.DM_USDT_AC_ITEM_TAM_PAY : TamConstants.DM_USDC_AC_ITEM_TAM_PAY, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null, null,
					"转账out");
			acUserList.add(feeItemReqDTO);
		}
		acmComponent.requestAc(acUserList);
		logger.info("请求账务处理完成=========================");

//		logger.info("更新数币链上转账或链上白名单转账付款金额-fkAcNo:{}",transferDB.getFkAcNo());
//		GenericRspDTO<DmAccountDetailRspDTO> fkTranDTO = accountManagementClient.queryDmAccountDetail(transferDB.getFkAcNo());
//		if (JudgeUtils.isNotSuccess(fkTranDTO.getMsgCd())) {
//			logger.info("更新数币链上转账或链上白名单转账付款金额失败,付款账号编码:{}", transferDB.getFkAcNo());
//			throw new LemonException(fkTranDTO.getMsgCd());
//		}

		// 获取转账订单流水
		PayJrnDO payJrnDO = payJrnDao.getByPayOrderNo(orderDO.getOrderNo());
		if (payJrnDO == null) {
			throw new LemonException("TAM10033");
		}
		// 更新转账交易流水
		PayJrnDO updPayJrnDO = new PayJrnDO();
		updPayJrnDO.setPayJrnNo(payJrnDO.getPayJrnNo());
		updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_S);
		updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
		int num1 = payJrnDao.update(updPayJrnDO);
		if (num1 != 1) {
			throw new LemonException("CSH20009");
		}

		//设置操作员
		Map<String, Map<Object, Object>> extMap = null;
		if (JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
				&& JudgeUtils.isNotNull(transferDB.getUserId())) {
			extMap = new HashMap<>();
			Map<Object, Object> map = new HashMap<>();
			map.put("loginId", transferDB.getUserId());
			extMap.put(TradeType.CONSUME.getType(), map);
		}

		//同步账单
		orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL, true, "1234", extMap);

		// 更新外围转账订单状态
		TransferOrderDO updateTransfer = new TransferOrderDO();
		updateTransfer.setOrderNo(orderDO.getBusOrderNo());
		updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
		// 更新订单信息
		int result = this.transferDao.update(updateTransfer);
		if (result != 1) {
			throw new LemonException("TAM20003");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void handleWithdrawOk(GenericDTO<HandleFinanceDTO> req) {
		logger.info("处理数币成功提现订单:{}", req.getBody().getOrderNo());
		String orderNo = req.getBody().getOrderNo();
		OrderDO orderDO = orderDao.get(orderNo);
		//更新订单状态
		orderDO.setOrderStatus(OrderStatus.SUCC.getValue());
		orderDO.setOrderSuccTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setTxHash(req.getBody().getTxHash());
//		orderDO.setRemark("数币提现成功");
		orderDao.update(orderDO);

		// 获取提现订单
		GenericDTO<GetByOrderDTO> genericDTO = new GenericDTO<>();
		GetByOrderDTO getByOrderDTO = new GetByOrderDTO();
		getByOrderDTO.setOrderNo(orderDO.getBusOrderNo());
		genericDTO.setBody(getByOrderDTO);
		GenericRspDTO<WithdrawOrderRspDTO> byOrder = pwmWithdrawClient.getByOrder(genericDTO);
		if (JudgeUtils.isNotSuccess(byOrder.getMsgCd())){
			LemonException.throwBusinessException(byOrder.getMsgCd());
		}
		if (JudgeUtils.isNull(byOrder.getBody())) {
			logger.info("订单:{}的提现订单不存在", orderDO.getBusOrderNo());
			LemonException.throwBusinessException("TAM30006");
		}
		WithdrawOrderRspDTO withdrawOrderRspDTO = byOrder.getBody();
		// 原订单不存在
		if (JudgeUtils.isNull(withdrawOrderRspDTO)) {
			throw new LemonException("TAM20001");
		}
		// 订单已经成功
		if (StringUtils.equals(withdrawOrderRspDTO.getOrderStatus(), PwmConstants.WITHDRAW_ORD_S1)) {
			logger.info("订单已成功");
			throw new LemonException("TAM20011");
		}
		//根据币种选择内部科目号
		String itemNo = "USDT".equals(orderDO.getCcy()) ? TamConstants.DM_USDT_WITHDRAW_SK_AC_NO : TamConstants.DM_USDC_WITHDRAW_SK_AC_NO;

		BigDecimal amountSum = orderDO.getTotalAmt();
		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		List<AccountingReqDTO> acUserList = new ArrayList();
		// 实际交易金额
		BigDecimal amount = orderDO.getLeftBalAmt();
		// 流水号
		String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
		payJrnNo = orderDO.getBusType() + payJrnNo;
		// 资金类型
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		// 收款方用户id
		String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayeeId(), balCapType, orderDO.getCcy());
		// 借:其他应付款-暂收-收银台 100
		cshItemReqDTO = acmComponent.createAccountingReqDTO(withdrawOrderRspDTO.getOrderNo(), payJrnNo, withdrawOrderRspDTO.getTxType(),
				ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
				ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY,
				null, null, null, null, "数币提现out");
		// 贷：其他应付款-支付账户-现金账户 100
		userAccountReqDTO = acmComponent.createAccountingReqDTO(withdrawOrderRspDTO.getOrderNo(), payJrnNo,
				withdrawOrderRspDTO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, itemNo, ACMConstants.ITM_AC_TYP,
				balCapType, ACMConstants.AC_C_FLG, itemNo, null, null, null, null, "数币提现out");
		acUserList.add(cshItemReqDTO);
		acUserList.add(userAccountReqDTO);
		if (orderDO.getFee().compareTo(BigDecimal.valueOf(0)) > 0) {
			// 贷：手续费收入-支付账户-转账 2
			feeItemReqDTO = acmComponent.createAccountingReqDTO(withdrawOrderRspDTO.getOrderNo(), payJrnNo,
					withdrawOrderRspDTO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderDO.getFee(), null, ACMConstants.ITM_AC_TYP, balCapType,
					ACMConstants.AC_C_FLG,  "USDT".equals(orderDO.getCcy()) ? TamConstants.DM_USDT_AC_ITEM_TAM_PAY : TamConstants.DM_USDC_AC_ITEM_TAM_PAY,
					"USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null, null,
					"数币提现out");
			acUserList.add(feeItemReqDTO);
		}
		acmComponent.requestAc(acUserList);
		logger.info("数币提现请求账务处理完成=========================");

//		logger.info("更新数币提现付款金额-fkAcNo:{}",withdrawOrderRspDTO.getFkAcNo());
//		GenericRspDTO<DmAccountDetailRspDTO> fkPwmDTO = accountManagementClient.queryDmAccountDetail(withdrawOrderRspDTO.getFkAcNo());
//		if (JudgeUtils.isNotSuccess(fkPwmDTO.getMsgCd())) {
//			logger.info("更新数币提现付款金额失败,付款账号编码:{}", withdrawOrderRspDTO.getFkAcNo());
//			throw new LemonException(fkPwmDTO.getMsgCd());
//		}

		// 获取提现订单流水
		PayJrnDO payJrnDO = payJrnDao.getByPayOrderNo(orderDO.getOrderNo());
		if (payJrnDO == null) {
			throw new LemonException("TAM10033");
		}
		// 更新提现交易流水
		PayJrnDO updPayJrnDO = new PayJrnDO();
		updPayJrnDO.setPayJrnNo(payJrnDO.getPayJrnNo());
		updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_S);
		updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
		int num1 = payJrnDao.update(updPayJrnDO);
		if (num1 != 1) {
			throw new LemonException("CSH20009");
		}

		//设置操作员
		Map<String, Map<Object, Object>> extMap = null;
		if (JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
				&& JudgeUtils.isNotNull(withdrawOrderRspDTO.getUserId())) {
			extMap = new HashMap<>();
			Map<Object, Object> map = new HashMap<>();
			map.put("loginId", withdrawOrderRspDTO.getUserId());
			extMap.put(TradeType.CONSUME.getType(), map);
		}

		//同步账单
		orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL, true, "1234", extMap);

		// 更新外围提现订单状态
		logger.info("更新数币提现订单表，订单号：{}", orderDO.getBusOrderNo());
		GenericDTO<UpdateOrderDTO> updateGenericDTO = new GenericDTO<>();
		UpdateOrderDTO updateOrderDTO = new UpdateOrderDTO();
		updateOrderDTO.setOrderNo(orderDO.getBusOrderNo());
		updateOrderDTO.setOrderStatus(PwmConstants.WITHDRAW_ORD_S1);
		updateOrderDTO.setOrderSuccTm(DateTimeUtils.getCurrentLocalDateTime());
		updateOrderDTO.setRspSuccTm(DateTimeUtils.getCurrentLocalDateTime());
		updateGenericDTO.setBody(updateOrderDTO);
		GenericRspDTO<WithdrawOrderRspDTO> genericRspDTO = pwmWithdrawClient.updateOrder(updateGenericDTO);
		if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
			logger.info("更新数币提现订单表失败，订单号：{}", orderDO.getBusOrderNo());
			LemonException.throwBusinessException(genericRspDTO.getMsgCd());
		}
	}

	/**
	 * 审核拒绝后续处理
	 * @param orderDO 订单信息
	 */
	private void auditRejectOrder(OrderDO orderDO) {
		// 转账订单号
		String orderId = orderDO.getBusOrderNo();
		TransferOrderDO transferDB = new TransferOrderDO();
		transferDB = transferDao.get(orderId);
		// 原订单不存在
		if (JudgeUtils.isNull(transferDB)) {
			throw new LemonException("TAM20001");
		}
		// 订单已经成功
		if (StringUtils.equals(transferDB.getOrderSts(), TamConstants.ORD_STS_S)) {
			logger.info("订单已成功");
			throw new LemonException("TAM20011");
		}

		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		List<AccountingReqDTO> acUserList = new ArrayList();
		// 交易金额
		BigDecimal amount = transferDB.getAmount();
		// 流水号
		String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
		payJrnNo = orderDO.getBusType() + payJrnNo;
		// 资金类型
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		// 付款方用户id
		String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType, orderDO.getCcy());


		//根据币种选择收银台科目号
		String itemNo = "";
		for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
			if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), orderDO.getCcy())) {
				itemNo = ccyAcItemEnum.getItemNo();
			}
		}

		//根据币种选择手续费科目号
		String feeItemNo = "";
		for(transferFeeAcItemEnum withdrawFeeAcItemEnum : transferFeeAcItemEnum.values()) {
			if(JudgeUtils.equals(withdrawFeeAcItemEnum.getCcy(), orderDO.getCcy())) {
				feeItemNo = withdrawFeeAcItemEnum.getItemNo();
			}
		}


		if ("DZ".equals(orderDO.getTxType())) {
			// 借:其他应付款-暂收-收银台 -102
			cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo, transferDB.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
					ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null, null, null, "转账out");
		} else {
			// 借:其他应付款-暂收-收银台 -102
			cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo, transferDB.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
					ACMConstants.AC_D_FLG, itemNo, null, null, null, null, "转账out");
		}
		// 贷：其他应付款-支付账户-现金账户 +102
		userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo,
				transferDB.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.USER_AC_TYP,
				balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "转账out");
		acUserList.add(cshItemReqDTO);
		acUserList.add(userAccountReqDTO);
		acmComponent.requestAc(acUserList);
		logger.info("账务冲正处理完成=========================");


		// 获取转账订单流水
		PayJrnDO payJrnDO = payJrnDao.getByPayOrderNo(orderDO.getOrderNo());
		if (payJrnDO == null) {
			throw new LemonException("TAM10033");
		}
		// 更新转账交易流水
		PayJrnDO updPayJrnDO =new PayJrnDO();
		updPayJrnDO.setPayJrnNo(payJrnDO.getPayJrnNo());
		updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_F);
		updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
		int num1=payJrnDao.update(updPayJrnDO);
		if(num1!=1){
			throw new LemonException("CSH20009");
		}

		//设置操作员
		Map<String,Map<Object,Object>> extMap = null;
		if(JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
				&& JudgeUtils.isNotNull(transferDB.getUserId())){
			extMap = new HashMap<>();
			Map<Object,Object> map = new HashMap<>();
			map.put("loginId", transferDB.getUserId());
			extMap.put(TradeType.CONSUME.getType(), map);
		}

		//同步账单
		orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL,true,"1234", extMap);

		// 更新外围转账订单状态
		TransferOrderDO updateTransfer = new TransferOrderDO();
		updateTransfer.setOrderNo(orderDO.getBusOrderNo());
		updateTransfer.setOrderSts(TamConstants.ORD_STS_F);
		// 更新订单信息
		int result = this.transferDao.update(updateTransfer);
		if (result != 1) {
			throw new LemonException("TAM20003");
		}
	}

	/**
	 * 终审通过后续处理
	 * @param orderDO 订单信息
	 */
	@Transactional(rollbackFor = Exception.class)
	public void auditSuccessOrder(OrderDO orderDO) {
		// 转账订单号
		String orderId = orderDO.getBusOrderNo();
		TransferOrderDO transferDB = new TransferOrderDO();
		transferDB = transferDao.get(orderId);
		// 原订单不存在
		if (JudgeUtils.isNull(transferDB)) {
			throw new LemonException("TAM20001");
		}
		// 订单已经成功
		if (StringUtils.equals(transferDB.getOrderSts(), TamConstants.ORD_STS_S)) {
			logger.info("订单已成功");
			throw new LemonException("TAM20011");
		}

		BigDecimal amountSum = orderDO.getTotalAmt();
		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		List<AccountingReqDTO> acUserList = new ArrayList();
		// 实际交易金额
		BigDecimal amount = orderDO.getLeftBalAmt();
		// 流水号
		String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
		payJrnNo = orderDO.getBusType() + payJrnNo;
		// 资金类型
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		// 收款方用户id
		String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayeeId(), balCapType, orderDO.getCcy());

		//根据币种选择收银台科目号
		String itemNo = "";
		for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
			if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), orderDO.getCcy())) {
				itemNo = ccyAcItemEnum.getItemNo();
			}
		}

		//根据币种选择手续费科目号
		String feeItemNo = "";
		for(transferFeeAcItemEnum withdrawFeeAcItemEnum : transferFeeAcItemEnum.values()) {
			if(JudgeUtils.equals(withdrawFeeAcItemEnum.getCcy(), orderDO.getCcy())) {
				feeItemNo = withdrawFeeAcItemEnum.getItemNo();
			}
		}

		// 借:其他应付款-暂收-收银台 100
		cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo, transferDB.getTxType(),
				ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
				ACMConstants.AC_D_FLG, itemNo, null, null, null, null, "转账out");
		// 贷：其他应付款-支付账户-现金账户 100
		userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo,
				transferDB.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.USER_AC_TYP,
				balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "转账out");
		acUserList.add(cshItemReqDTO);
		acUserList.add(userAccountReqDTO);
		if (orderDO.getFee().compareTo(BigDecimal.valueOf(0)) > 0) {
			// 贷：手续费收入-支付账户-转账 2
			feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo,
					transferDB.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderDO.getFee(), null, ACMConstants.ITM_AC_TYP, balCapType,
					ACMConstants.AC_C_FLG, feeItemNo, feeItemNo, null, null, null,
					"转账out");
			acUserList.add(feeItemReqDTO);
		}
		acmComponent.requestAc(acUserList);
		logger.info("请求账务处理完成=========================");


		// 获取转账订单流水
		PayJrnDO payJrnDO = payJrnDao.getByPayOrderNo(orderDO.getOrderNo());
		if (payJrnDO == null) {
			throw new LemonException("TAM10033");
		}
		// 更新转账交易流水
		PayJrnDO updPayJrnDO =new PayJrnDO();
		updPayJrnDO.setPayJrnNo(payJrnDO.getPayJrnNo());
		updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_S);
		updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
		int num1=payJrnDao.update(updPayJrnDO);
		if(num1!=1){
			throw new LemonException("CSH20009");
		}

		//设置操作员
		Map<String,Map<Object,Object>> extMap = null;
		if(JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
				&& JudgeUtils.isNotNull(transferDB.getUserId())){
			extMap = new HashMap<>();
			Map<Object,Object> map = new HashMap<>();
			map.put("loginId", transferDB.getUserId());
			extMap.put(TradeType.CONSUME.getType(), map);
		}

		//同步账单
		orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL,true,"1234", extMap);

		// 更新外围转账订单状态
		TransferOrderDO updateTransfer = new TransferOrderDO();
		updateTransfer.setOrderNo(orderDO.getBusOrderNo());
		updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
		// 更新订单信息
		int result = this.transferDao.update(updateTransfer);
		if (result != 1) {
			throw new LemonException("TAM20003");
		}
	}

	//风控累计
	protected void riskAmount(OrderDO orderDO) {
		List<JrnReqDTO> jrnReqDTOList = new ArrayList<>();
		String busType = orderDO.getBusType();
		JrnReqDTO jrnReqDTO = new JrnReqDTO();
		jrnReqDTO.setCcy(orderDO.getCcy());
		jrnReqDTO.setPayCrdNo(null);
		if (StringUtils.isNotBlank(orderDO.getPayeeId())) {
			if (StringUtils.equals(orderDO.getBusType(), "0302")) {
				jrnReqDTO.setStlUserTyp("02");
			} else {
				jrnReqDTO.setStlUserTyp("01");
			}
			jrnReqDTO.setStlUserId(orderDO.getPayeeId());
		}
		jrnReqDTO.setPayUserTyp("01");
		jrnReqDTO.setPayUserId(orderDO.getPayerId());

		jrnReqDTO.setTxAmt(orderDO.getOrderAmt());
		jrnReqDTO.setTxCnl(CshConstants.RSK_CNL_APP);
		jrnReqDTO.setTxTyp(orderDO.getTxType());
		if (JudgeUtils.equalsAny(busType, BussinessType.RECHARGE_HALL.getValue(),BussinessType.RECHARGE_OFFLINE.getValue())) {
			//免密支付:pswFlg=1
			jrnReqDTO.addTxData("pswFlg=1");
		} else {
			//非免密:pswFlg=0
			jrnReqDTO.addTxData("pswFlg=0");
		}

		jrnReqDTO.setTxOrdNo(orderDO.getOrderNo());
		jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
		jrnReqDTO.setTxSts("0");
		jrnReqDTO.setTxDate(DateTimeUtils.getCurrentLocalDate());
		jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
		//补款方式
		String crdPayType = orderDO.getCrdPayType();
		if (JudgeUtils.isNull(orderDO.getCrdPayAmt())) {
			orderDO.setCrdPayAmt(BigDecimal.valueOf(0));
		}
		//无优惠 账户余额
		if (JudgeUtils.isNotNull(orderDO.getBalAmt()) && orderDO.getBalAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			JrnReqDTO accJrnReqDTO = new JrnReqDTO();
			BeanUtils.copyProperties(accJrnReqDTO, jrnReqDTO);
			//支付类型
			accJrnReqDTO.setPayTyp(Constants.PAY_TYP_ACCOUNT);
			accJrnReqDTO.setTxAmt(orderDO.getBalAmt());
			accJrnReqDTO.setStlUserId(orderDO.getPayeeId());
			accJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
			logger.debug("交易余额累计:" + orderDO.getBalAmt());
			jrnReqDTOList.add(accJrnReqDTO);

		}
		//海币优惠
		if (JudgeUtils.isNotNull(orderDO.getCouponAmt()) && orderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			if (JudgeUtils.equals(orderDO.getCouponType(), CouponType.H_COUPON.getType())) {
				JrnReqDTO seaJrnReqDTO = new JrnReqDTO();
				BeanUtils.copyProperties(seaJrnReqDTO, jrnReqDTO);
				seaJrnReqDTO.setPayTyp(Constants.PAY_TYP_SEATEL);
				seaJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				seaJrnReqDTO.setTxAmt(orderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
				seaJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
				logger.debug("交易海币累计:" + orderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
				jrnReqDTOList.add(seaJrnReqDTO);
			}
		}

		switch (crdPayType) {
			//快捷
			case CshConstants.CRD_TYPE_QP:
				JrnReqDTO qpJrnReqDTO = new JrnReqDTO();
				BeanUtils.copyProperties(qpJrnReqDTO, jrnReqDTO);
				qpJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
				qpJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				qpJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
				qpJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
				logger.debug("快捷交易补款累计:" + orderDO.getCrdPayAmt());
				jrnReqDTOList.add(qpJrnReqDTO);
				break;
			//网银
			case CshConstants.CRD_TYPE_NB:
				JrnReqDTO ebankJrnReqDTO = new JrnReqDTO();
				BeanUtils.copyProperties(ebankJrnReqDTO, jrnReqDTO);
				//获取支付路径机构
				String rutCorgNo = orderDO.getCapCorgNo();
				logger.debug("网银支付合作机构:" + rutCorgNo);

				if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_WX)){
					ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_WECHAT);
				}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_ALI)){
					ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_ALIPAY);
				}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_ICBC)){
					ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
				}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_BEST)){
					ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_BESTPAY);
				}else{

				}
				ebankJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				ebankJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
				ebankJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
				logger.debug("网银交易补款累计:" + orderDO.getCrdPayAmt());
				jrnReqDTOList.add(ebankJrnReqDTO);
				break;
			//线下转账
			case CshConstants.CRD_TYPE_FL:
				JrnReqDTO offlieJrnReqDTO = new JrnReqDTO();
				BeanUtils.copyProperties(offlieJrnReqDTO, jrnReqDTO);
				offlieJrnReqDTO.setPayTyp(Constants.PAY_TYP_OFFLINE);
				offlieJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				offlieJrnReqDTO.setPayUserId(orderDO.getPayerId());
				offlieJrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
				offlieJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
				offlieJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
				logger.debug("交易线下汇款累计:" + orderDO.getCrdPayAmt());
				jrnReqDTOList.add(offlieJrnReqDTO);
				break;
			default:
				break;
		}
		GenericDTO<List<JrnReqDTO>> genericDTO = new GenericDTO();
		genericDTO.setBody(jrnReqDTOList);

		try{
			GenericRspDTO<NoBody> rspDto =  riskCheckClient.batchAccumulation(genericDTO);
			if(JudgeUtils.isNotSuccess(rspDto.getMsgCd())){
				logger.error("订单{}实时风控累计失败:{}",orderDO.getOrderNo(),  rspDto.getMsgCd());
			}
		}catch (Exception e){
			logger.error("订单{}风控累计失败。",orderDO.getOrderNo(),e);
		}

	}


	/**
	 * 准备通知业务的最新数据
	 * @param dbOrder 数据库订单信息
	 * @param updateOrder 更新后的订单信息
	 * @return 订单信息
	 */
	protected OrderDO prepareNotifyOrder(OrderDO dbOrder,OrderDO updateOrder){
		logger.debug("========================  业务通知准备数据  ========================");
		updateOrder.setTxType(dbOrder.getTxType());
		logger.debug("交易类型："+updateOrder.getTxType());
		updateOrder.setBusType(dbOrder.getBusType());
		logger.debug("业务类型："+updateOrder.getBusType());
		updateOrder.setTotalAmt(dbOrder.getTotalAmt());
		logger.debug("支付总金额："+updateOrder.getTotalAmt());
		updateOrder.setOrderAmt(dbOrder.getOrderAmt());
		logger.debug("订单金额："+updateOrder.getOrderAmt());
		updateOrder.setCcy(dbOrder.getCcy());
		logger.debug("订单币种："+updateOrder.getCcy());

		String payerId = dbOrder.getPayerId();
		/*if(JudgeUtils.isBlank(payerId) && (JudgeUtils.equals(updateOrder.getTxType(),"02"))){
			payerId = LemonUtils.getUserId();
		}*/
		updateOrder.setPayerId(payerId);
		logger.debug("付款方ID："+updateOrder.getPayerId());
		updateOrder.setPayType(dbOrder.getPayType());
		logger.debug("支付方式："+updateOrder.getPayType());
		updateOrder.setBusOrderNo(dbOrder.getBusOrderNo());
		logger.debug("业务订单号："+updateOrder.getBusOrderNo());
		if(JudgeUtils.isNull(updateOrder.getFee())){
			updateOrder.setFee(dbOrder.getFee());
		}
		logger.debug("手续费：" +updateOrder.getFee());
		updateOrder.setOrderTm(dbOrder.getOrderTm());
		logger.debug("下单时间：" +updateOrder.getOrderTm());

		if(JudgeUtils.isNull(updateOrder.getBalAmt())){
			updateOrder.setBalAmt(dbOrder.getBalAmt());
		}
		logger.debug("余额账户支付金额："+updateOrder.getBalAmt());

		String couponType = updateOrder.getCouponType();
		if(JudgeUtils.isBlank(couponType)){
			updateOrder.setCouponType(dbOrder.getCouponType());
		}
		logger.debug("优惠类型："+updateOrder.getCouponType());

		BigDecimal couponAmt = updateOrder.getCouponAmt();
		if(JudgeUtils.isNull(couponAmt)){
			updateOrder.setCouponAmt(dbOrder.getCouponAmt());
		}
		logger.debug("优惠金额："+updateOrder.getCouponAmt());
		if(JudgeUtils.isNull(updateOrder.getCrdPayType())){
			updateOrder.setCrdPayType(dbOrder.getCrdPayType());
		}
		logger.debug("补款类型："+updateOrder.getCrdPayType());
		if(JudgeUtils.isNull(updateOrder.getCrdPayAmt())){
			updateOrder.setCrdPayAmt(dbOrder.getCrdPayAmt());
		}
		logger.debug("补款金额："+updateOrder.getCrdPayAmt());

		if(JudgeUtils.isNull(updateOrder.getCapCorg())){
			updateOrder.setCapCorg(dbOrder.getCapCorg());
		}
		logger.debug("资金机构："+updateOrder.getCapCorg());

		if(JudgeUtils.isNull(updateOrder.getCapCorgNo())){
			updateOrder.setCapCorgNo(dbOrder.getCapCorgNo());
		}
		logger.debug("网银资金合作机构："+updateOrder.getCapCorgNo());

		if(JudgeUtils.isNull(updateOrder.getLast4CardNo())){
			updateOrder.setLast4CardNo(dbOrder.getLast4CardNo());
		}
		logger.debug("卡后四位："+updateOrder.getLast4CardNo());

		if(JudgeUtils.isNull(updateOrder.getPayMod())){
			updateOrder.setPayMod(dbOrder.getPayMod());
		}
		logger.debug("支付方式payMod："+updateOrder.getPayMod());

		if(JudgeUtils.isNull(updateOrder.getMercName())){
			updateOrder.setMercName(dbOrder.getMercName());
		}

		if(JudgeUtils.isNull(updateOrder.getPayeeId())){
			updateOrder.setPayeeId(dbOrder.getPayeeId());
		}
		logger.debug("商户名称："+updateOrder.getMercName());

		if(JudgeUtils.isNull(updateOrder.getRemark())){
			updateOrder.setRemark(dbOrder.getRemark());
		}
		logger.debug("订单备注："+updateOrder.getRemark());

		logger.debug("========================业务通知准备数据 end========================");
		return updateOrder;
	}
}
