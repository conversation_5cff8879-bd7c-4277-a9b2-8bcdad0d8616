package com.hisun.lemon.csh.service.impl;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpm.client.CpmOrderAccountClient;
import com.hisun.lemon.cpm.dto.OrderAccountDTO;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.csh.enums.AcItem;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class CpmOrderServiceImpl extends AbstractOrderService {
	private static final Logger logger = LoggerFactory.getLogger(CpmOrderServiceImpl.class);
	protected String txType=TradeType.PAYMENT.getType();
	protected String[] busTypes=new String[]{BussinessType.PAYMENT_FLOW.getValue(),BussinessType.PAYMENT_PHONE.getValue()};

	@Resource
	CpmOrderAccountClient cpmOrderAccountClient;

	public boolean match(String txType){
		return JudgeUtils.equals(this.txType,txType);
	}

	@Override
	protected void checkBeforeInitOrder(OrderDO orderDO){

		logger.debug("-------------初始化订单业务检查-------------。订单号 ["+orderDO.getOrderNo()+"]");
		String payerId= Optional.ofNullable(orderDO.getPayerId()).orElse(LemonUtils.getUserId());

		riskUserStatus(orderDO.getTxType(),payerId,"01");
		riskUserStatus(orderDO.getTxType(),orderDO.getPayeeId(),"01");

		logger.debug("-------------初始化订单业务检查完成----------。订单号 ["+orderDO.getOrderNo()+"]");
	}

	protected void  caculateFee(OrderDO orderDO){
		logger.debug("-------------开始计算用户手续费-------------。订单号["+orderDO.getOrderNo()+"]");
        super.caculateFee(orderDO);
		logger.debug("-------------计算用户手续费完成-------------。订单号["+orderDO.getOrderNo()+"]");
	}

	@Override
	protected void checkJrnWithBussiness(GenericDTO inputGenericDto){
        //视以后需求情况添加检查
	}


	//创建账务处理列表,具体业务实现
	protected List<AccountingReqDTO> createAccQueue(OrderDO orderDO,PayJrnDO payJrnDO){
		//使用现金账户进行缴费，支持快捷补款
		if(JudgeUtils.equals(orderDO.getTxType(),TradeType.PAYMENT.getType())){
			int crdPayType = payJrnDO.getCrdPayType().intValue();
			List<AccountingReqDTO> accountingReqDtos=new ArrayList<>();

			//个人现金账户信息查询
			String balCapType= CapTypEnum.CAP_TYP_CASH.getCapTyp();
			String balAcNo=acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType);

			//无补款
			if(crdPayType==Integer.valueOf(CshConstants.CRD_TYPE_NONE).intValue()){
//				借：其他应付款-支付账户-现金账户
				AccountingReqDTO cshItemReqDTO=acmComponent.createAccountingReqDTO(
						orderDO.getOrderNo(),
						payJrnDO.getPayJrnNo(),
						orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL,
						payJrnDO.getBalAmt(),
						balAcNo,
						ACMConstants.USER_AC_TYP,
						balCapType,
						ACMConstants.AC_D_FLG,
						AcItem.O_BAL.getValue(),
						null,
						null,
						null,
						null,
						null);

//				贷：其他应付款-暂收-收银台
				AccountingReqDTO userAccountReqDTO=acmComponent.createAccountingReqDTO(
						orderDO.getOrderNo(),
						payJrnDO.getPayJrnNo(),
						orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL,
						payJrnDO.getBalAmt(),
						balAcNo,
						ACMConstants.ITM_AC_TYP,
						balCapType,
						ACMConstants.AC_C_FLG,
						AcItem.O_CSH.getValue(),
						null,
						null,
						null,
						null,
						null);

				accountingReqDtos.add(cshItemReqDTO);
				accountingReqDtos.add(userAccountReqDTO);

			}
			//有补款: 补款金额与补款类型校验
			if(payJrnDO.getCrdPayAmt().compareTo(BigDecimal.valueOf(0)) > 0 && Integer.valueOf(crdPayType) != 0){
//				借：应收账款-渠道充值-XX银行/支付宝/微信
				AccountingReqDTO cnlRechargeBnkReqDTO=acmComponent.createAccountingReqDTO(
						orderDO.getOrderNo(),
						payJrnDO.getPayJrnNo(),
						orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL,
						payJrnDO.getOrderAmt(),
						balAcNo,
						ACMConstants.ITM_AC_TYP,
						balCapType,
						ACMConstants.AC_D_FLG,
						AcItem.I_CNL_BANK.getValue(),
//						CshConstants.AC_ITEM_CNL_RECHARGE_BNK,
						null,
						null,
						null,
						null,
						null);

//				贷：其他应付款-暂收-收银台
				AccountingReqDTO userAccountReqDTO1=acmComponent.createAccountingReqDTO(
						orderDO.getOrderNo(),
						payJrnDO.getPayJrnNo(),
						orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL,
						payJrnDO.getOrderAmt(),
						balAcNo,
						ACMConstants.ITM_AC_TYP,
						balCapType,
						ACMConstants.AC_C_FLG,
//						CshConstants.AC_ITEM_CSH_PAY,
						AcItem.O_CSH.getValue(),
						null,
						null,
						null,
						null,
						null);

				accountingReqDtos.add(cnlRechargeBnkReqDTO);
				accountingReqDtos.add(userAccountReqDTO1);
			}
			return accountingReqDtos;
		}
		return Collections.emptyList();
	}

	@Override
	protected void notifyBussiness(OrderDO orderDO,PayJrnDO payJrn){
		logger.debug("-------------订单成功，通知缴费模块-------------。订单号[" + orderDO.getOrderNo() + "]");

		OrderAccountDTO orderAccountDTO=new OrderAccountDTO();
		orderAccountDTO.setPayType(orderDO.getPayType());
		orderAccountDTO.setCpmOrderNo(orderDO.getBusOrderNo());
		orderAccountDTO.setCshOrderNo(orderDO.getOrderNo());
		orderAccountDTO.setPayAmt(orderDO.getOrderAmt());
		GenericDTO ntyDto=new GenericDTO();
		ntyDto.setBody(orderAccountDTO);

		if(StringUtils.equals(BussinessType.PAYMENT_FLOW.getValue(),orderDO.getBusType())){
			GenericRspDTO rspDTO=cpmOrderAccountClient.flowDataOrderAccount(ntyDto);
			if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
				logger.error("订单[" + orderDO.getOrderNo() + "] 充流量通知缴费处理失败:"+rspDTO.getMsgCd());
			}
		}

		if(StringUtils.equals(BussinessType.PAYMENT_PHONE.getValue(),orderDO.getBusType())) {
			GenericRspDTO rspDTO=cpmOrderAccountClient.telFareOrderAccount(ntyDto);
			if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
				logger.error("订单[" + orderDO.getOrderNo() + "] 充话费通知缴费处理失败:"+rspDTO.getMsgCd());
			}
		}

		if(StringUtils.equals(BussinessType.PAYMENT_LIFE.getValue(),orderDO.getBusType())) {
			GenericRspDTO rspDTO=cpmOrderAccountClient.lifePayOrderAccount(ntyDto);
			if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
				logger.error("订单[" + orderDO.getOrderNo() + "] 生活缴费通知缴费处理失败:"+rspDTO.getMsgCd());
			}
		}

		logger.info("------------订单成功，通知缴费模块完成----------。订单号["+orderDO.getOrderNo()+"]");

	}

}
