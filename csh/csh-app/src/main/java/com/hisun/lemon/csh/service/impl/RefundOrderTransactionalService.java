package com.hisun.lemon.csh.service.impl;

import javax.annotation.Resource;

import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csh.dao.IOrderDao;
import com.hisun.lemon.csh.dao.IRefundOrderDao;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.RefundOrderDO;

@Transactional
@Service
public class RefundOrderTransactionalService extends BaseService {
	@Resource
	private IRefundOrderDao refundDao;

	@Resource
	private IOrderDao orderDao;
	
	/**
	 * 退款或撤销成功更新订单
	 */
	public void updateOrj(OrderDO orderDO){
		int result = this.orderDao.update(orderDO);
		if (result != 1) {
			throw new LemonException("CSH20066");
		}
	}

	/**
	 * 退款订单
	 */
	public void createRefundOrder(RefundOrderDO refundOrderDO) {
		int result = this.refundDao.insert(refundOrderDO);
		if (result != 1) {
			throw new LemonException("CSH20023");
		}
	}

	/**
	 * 查询根据原收银订单号查找原订单
	 */
	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public OrderDO selectOrderInfo(String orderNo) {
		return this.orderDao.get(orderNo);
	}
	
	/**
	 * 根据退款订单号查询退款订单信息
	 * @param rfdOrdNo
	 * @return
	 */
	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public RefundOrderDO selectRefundInfo(String rfdOrdNo){
		RefundOrderDO refundDO=this.refundDao.get(rfdOrdNo);
		return  refundDO;
	}
	
	/**
	 * 更新退款订单
	 * @param refundDO
	 */
	public void updateRefundOrder(RefundOrderDO refundDO){
		
		int result=this.refundDao.update(refundDO);
		if(result !=1){
			throw new LemonException("CSH20024");
		}
	}
}
