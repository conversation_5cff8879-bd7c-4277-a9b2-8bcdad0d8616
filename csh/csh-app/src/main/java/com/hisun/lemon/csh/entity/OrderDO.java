/*
 * @ClassName OrderDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-21 15:47:39
 */
package com.hisun.lemon.csh.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class OrderDO extends BaseDO {
    /**
     * @Fields orderNo 
     */
    private String orderNo;
    /**
     * @Fields orderTm 
     */
    private LocalDateTime orderTm;
    /**
     * @Fields payerId 
     */
    private String payerId;

    /**
     * @Fields acNo 付款方账户号码
     */
    private String acNo;

    /**
     * @Fields toAcNo 收款方账户号码或收款方地址
     */
    private String toAcNo;

    /**
     * @Fields txType 
     */
    private String txType;

    private String feeFlag;   //IN: 内扣  EX:外扣

	/**
     * @Fields appCnl 
     */
    private String appCnl;
    /**
     * @Fields busType 
     */
    private String busType;

    private String ccy;

    /**
     * @Fields busOrderNo 
     */
    private String busOrderNo;
    /**
     * @Fields payJrnNo 
     */
    private String payJrnNo;
    /**
     * @Fields jrnTxTm 
     */
    private LocalDateTime jrnTxTm;
    /**
     * @Fields acTm 
     */
    private LocalDate acTm;
    /**
     * @Fields fndOrderNo 
     */
    private String fndOrderNo;
    /**
     * @Fields payeeId 
     */
    private String payeeId;
    /**
     * @Fields orderChannel 
     */
    private String orderChannel;
    /**
     * @Fields sysChannel 
     */
    private String sysChannel;
    /**
     * @Fields payType 
     */
    private String payType;
    /**
     * @Fields couponNo 优惠券编号
     */
    private String couponNo;
	/**
     * @Fields busPayType 
     */
    private String busPayType;

    /**
     * @Fields totalAmt 总金额
     */
    private BigDecimal totalAmt;
    /**
     * @Fields orderAmt 
     */
    private BigDecimal orderAmt;
    /**
     * @Fields orderStatus 
     */
    private String orderStatus;
    /**
     * @Fields orderExpTm 
     */
    private LocalDateTime orderExpTm;
    /**
     * @Fields orderSuccTm 
     */
    private LocalDateTime orderSuccTm;
    /**
     * @Fields capCorgNo 
     */
    private String capCorgNo;
    /**
     * @Fields capCardType 
     */
    private String capCardType;
    /**
     * @Fields crdPayType 
     */
    private String crdPayType;
    /**
     * @Fields crdPayAmt 
     */
    private BigDecimal crdPayAmt;
    /**
     * @Fields balAmt 
     */
    private BigDecimal balAmt;
    /**
     * @Fields invAmt 
     */
    private BigDecimal invAmt;
    /**
     * @Fields couponAmt 
     */
    private BigDecimal couponAmt;
    /**
     * @Fields couponType 
     */
    private String couponType;
    /**
     * @Fields goodsInfo 
     */
    private String goodsInfo;
    /**
     * @Fields leftTotalAmt 
     */
    private BigDecimal leftTotalAmt;
    /**
     * @Fields leftBalAmt 
     */
    private BigDecimal leftBalAmt;
    /**
     * @Fields leftCardAmt 
     */
    private BigDecimal leftCardAmt;
    /**
     * @Fields leftInvAmt 
     */
    private BigDecimal leftInvAmt;
    /**
     * @Fields leftCouponAmt 
     */
    private BigDecimal leftCouponAmt;
    /**
     * @Fields fee 
     */
    private BigDecimal fee;
    /**
     * @Fields leftFee 
     */
    private BigDecimal leftFee;
    /**
     * @Fields mercName 
     */
    private String mercName;
    /**
     * @Fields remark
     */
    private String remark;

    /**
     * @Fields fileUrl
     */
    private String fileUrl;

    /**
     * 资金机构
     */
    private String capCorg;
    /**
     * 卡后四位
     */
    private String last4CardNo;
    /**
     * 前端显示支付方式   现金账户、网银、快捷
     */
    private String payMod;

    /**
     * 初审人
     */
    private String firstAuditUser;
    /**
     * 初审时间
     */
    private LocalDateTime firstAuditTime;
    /**
     * 初审结果：APPROVED/REJECTED
     */
    private String firstAuditResult;
    /**
     * 初审意见
     */
    private String firstAuditOpinion;
    /**
     * 复核人
     */
    private String secondAuditUser;
    /**
     * 复核时间
     */
    private LocalDateTime secondAuditTime;
    /**
     * 复核结果：APPROVED/REJECTED
     */
    private String secondAuditResult;
    /**
     * 复核意见
     */
    private String secondAuditOpinion;
    /**
     * 执行时间（成功时）
     */
    private LocalDateTime executeTime;
    /**
     * 拒绝原因（最终拒绝原因）
     */
    private String rejectReason;



    /**
     * 交易哈希（数币交易专有）
     */
    private String txHash;

    /**
     * 卖出币种
     */
    private String fromCoin;

    /**
     * 买入币种
     */
    private String toCoin;

    /**
     * 买入金额
     */
    private BigDecimal toAmount;

    /**
     * 兑换方向
     */
    private String direction;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getOrderTm() {
        return orderTm;
    }

    public void setOrderTm(LocalDateTime orderTm) {
        this.orderTm = orderTm;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getToAcNo() {
        return toAcNo;
    }

    public void setToAcNo(String toAcNo) {
        this.toAcNo = toAcNo;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public String getAppCnl() {
        return appCnl;
    }

    public void setAppCnl(String appCnl) {
        this.appCnl = appCnl;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getCouponNo() {
        return couponNo;
    }

    public void setCouponNo(String couponNo) {
        this.couponNo = couponNo;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void orderSts(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getPayJrnNo() {
        return payJrnNo;
    }

    public void setPayJrnNo(String payJrnNo) {
        this.payJrnNo = payJrnNo;
    }

    public LocalDateTime getJrnTxTm() {
        return jrnTxTm;
    }

    public void setJrnTxTm(LocalDateTime jrnTxTm) {
        this.jrnTxTm = jrnTxTm;
    }

    public LocalDate getAcTm() {
        return acTm;
    }

    public void setAcTm(LocalDate acTm) {
        this.acTm = acTm;
    }

    public String getFndOrderNo() {
        return fndOrderNo;
    }

    public void setFndOrderNo(String fndOrderNo) {
        this.fndOrderNo = fndOrderNo;
    }

    public String getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(String payeeId) {
        this.payeeId = payeeId;
    }

    public String getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(String orderChannel) {
        this.orderChannel = orderChannel;
    }

    public String getSysChannel() {
        return sysChannel;
    }

    public void setSysChannel(String sysChannel) {
        this.sysChannel = sysChannel;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getBusPayType() {
        return busPayType;
    }

    public void setBusPayType(String busPayType) {
        this.busPayType = busPayType;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public LocalDateTime getOrderExpTm() {
        return orderExpTm;
    }

    public void setOrderExpTm(LocalDateTime orderExpTm) {
        this.orderExpTm = orderExpTm;
    }

    public LocalDateTime getOrderSuccTm() {
        return orderSuccTm;
    }

    public void setOrderSuccTm(LocalDateTime orderSuccTm) {
        this.orderSuccTm = orderSuccTm;
    }

    public String getCapCorgNo() {
        return capCorgNo;
    }

    public void setCapCorgNo(String capCorgNo) {
        this.capCorgNo = capCorgNo;
    }

    public String getCapCardType() {
        return capCardType;
    }

    public void setCapCardType(String capCardType) {
        this.capCardType = capCardType;
    }

    public String getCrdPayType() {
        return crdPayType;
    }

    public void setCrdPayType(String crdPayType) {
        this.crdPayType = crdPayType;
    }

    public BigDecimal getCrdPayAmt() {
        return crdPayAmt;
    }

    public void setCrdPayAmt(BigDecimal crdPayAmt) {
        this.crdPayAmt = crdPayAmt;
    }

    public BigDecimal getBalAmt() {
        return balAmt;
    }

    public void setBalAmt(BigDecimal balAmt) {
        this.balAmt = balAmt;
    }

    public BigDecimal getInvAmt() {
        return invAmt;
    }

    public void setInvAmt(BigDecimal invAmt) {
        this.invAmt = invAmt;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getGoodsInfo() {
        return goodsInfo;
    }

    public void setGoodsInfo(String goodsInfo) {
        this.goodsInfo = goodsInfo;
    }

    public BigDecimal getLeftTotalAmt() {
        return leftTotalAmt;
    }

    public void setLeftTotalAmt(BigDecimal leftTotalAmt) {
        this.leftTotalAmt = leftTotalAmt;
    }

    public BigDecimal getLeftBalAmt() {
        return leftBalAmt;
    }

    public void setLeftBalAmt(BigDecimal leftBalAmt) {
        this.leftBalAmt = leftBalAmt;
    }

    public BigDecimal getLeftCardAmt() {
        return leftCardAmt;
    }

    public void setLeftCardAmt(BigDecimal leftCardAmt) {
        this.leftCardAmt = leftCardAmt;
    }

    public BigDecimal getLeftInvAmt() {
        return leftInvAmt;
    }

    public void setLeftInvAmt(BigDecimal leftInvAmt) {
        this.leftInvAmt = leftInvAmt;
    }

    public BigDecimal getLeftCouponAmt() {
        return leftCouponAmt;
    }

    public void setLeftCouponAmt(BigDecimal leftCouponAmt) {
        this.leftCouponAmt = leftCouponAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getLeftFee() {
        return leftFee;
    }

    public void setLeftFee(BigDecimal leftFee) {
        this.leftFee = leftFee;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public String getFeeFlag() {
        return feeFlag;
    }

    public void setFeeFlag(String feeFlag) {
        this.feeFlag = feeFlag;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getLast4CardNo() {
        return last4CardNo;
    }

    public void setLast4CardNo(String last4CardNo) {
        this.last4CardNo = last4CardNo;
    }

    public String getPayMod() {
        return payMod;
    }

    public void setPayMod(String payMod) {
        this.payMod = payMod;
    }

    public String getFirstAuditUser() {
        return firstAuditUser;
    }

    public void setFirstAuditUser(String firstAuditUser) {
        this.firstAuditUser = firstAuditUser;
    }

    public LocalDateTime getFirstAuditTime() {
        return firstAuditTime;
    }

    public void setFirstAuditTime(LocalDateTime firstAuditTime) {
        this.firstAuditTime = firstAuditTime;
    }

    public String getFirstAuditResult() {
        return firstAuditResult;
    }

    public void setFirstAuditResult(String firstAuditResult) {
        this.firstAuditResult = firstAuditResult;
    }

    public String getFirstAuditOpinion() {
        return firstAuditOpinion;
    }

    public void setFirstAuditOpinion(String firstAuditOpinion) {
        this.firstAuditOpinion = firstAuditOpinion;
    }

    public String getSecondAuditUser() {
        return secondAuditUser;
    }

    public void setSecondAuditUser(String secondAuditUser) {
        this.secondAuditUser = secondAuditUser;
    }

    public LocalDateTime getSecondAuditTime() {
        return secondAuditTime;
    }

    public void setSecondAuditTime(LocalDateTime secondAuditTime) {
        this.secondAuditTime = secondAuditTime;
    }

    public String getSecondAuditResult() {
        return secondAuditResult;
    }

    public void setSecondAuditResult(String secondAuditResult) {
        this.secondAuditResult = secondAuditResult;
    }

    public String getSecondAuditOpinion() {
        return secondAuditOpinion;
    }

    public void setSecondAuditOpinion(String secondAuditOpinion) {
        this.secondAuditOpinion = secondAuditOpinion;
    }

    public LocalDateTime getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(LocalDateTime executeTime) {
        this.executeTime = executeTime;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getTxHash() {
        return txHash;
    }

    public void setTxHash(String txHash) {
        this.txHash = txHash;
    }

    public String getFromCoin() {
        return fromCoin;
    }

    public void setFromCoin(String fromCoin) {
        this.fromCoin = fromCoin;
    }

    public String getToCoin() {
        return toCoin;
    }

    public void setToCoin(String toCoin) {
        this.toCoin = toCoin;
    }

    public BigDecimal getToAmount() {
        return toAmount;
    }

    public void setToAmount(BigDecimal toAmount) {
        this.toAmount = toAmount;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
}