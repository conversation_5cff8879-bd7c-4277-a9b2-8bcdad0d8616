package com.hisun.lemon.csh.utils;

import com.hisun.lemon.common.extension.ExtensionLoader;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.service.impl.AbstractOrderService;
import com.hisun.lemon.csh.service.impl.OrderTransactionalService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Optional;

public class RouterUtils {
	private static final Logger logger = LoggerFactory.getLogger(RouterUtils.class);

	private static Map<String,AbstractOrderService> services;

	private static OrderTransactionalService orderTransactionalService;

	/**
	 * 选择对应的业务service处理请求
	 * @param busType
	 * @param orderNo
	 * @return
	 */
	public static AbstractOrderService select(String busType,String orderNo){
		String txType=null;
		if(StringUtils.isBlank(busType)){
			if(JudgeUtils.isNull(orderTransactionalService)){
				orderTransactionalService=ExtensionLoader.getSpringBean("orderTransactionalService",OrderTransactionalService.class);
			}

			OrderDO orderDO=orderTransactionalService.queryOrderNotNone(orderNo);
			txType=orderDO.getBusType().substring(0,2);
		}else{
			txType=busType.substring(0,2);
		}

		if(JudgeUtils.isNull(services)){
			services= ExtensionLoader.getSpringBeansOfType(AbstractOrderService.class);
		}
        final String finalTxType=txType;
		Optional<AbstractOrderService> value = services.values().stream().filter(it->it.match(finalTxType)).findAny();
		if(!value.isPresent()){
			return ExtensionLoader.getSpringBean(AbstractOrderService.class);
		}
		return value.get();
	}


}
