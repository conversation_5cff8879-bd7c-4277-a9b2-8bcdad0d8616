package com.hisun.lemon.csh.component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.hisun.lemon.framework.utils.LemonUtils;
import org.springframework.stereotype.Component;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.client.CardClient;
import com.hisun.lemon.cpi.dto.AgrInfoReqDTO;
import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;

/**
 * 快捷支付组件
 * 
 * <AUTHOR>
 *
 */
@Component
public class QuickPaymentComponent {
	public static final String D_CARD="D";
	public static final String C_CARD="C";

	@Resource
	private CardClient cardClient;


	/**
	 * 查询所有绑定的快捷银行卡
	 */
	public Map<String,List<AgrInfoRspDTO.CardAgrInfo>> queryBindCards(){
		//crdAcTyp不设置，表示查询所有签约卡种银行卡
		GenericRspDTO<AgrInfoRspDTO> rspDTO=cardClient.queryCardsInfo(CorpBusTyp.SIGN,CorpBusSubTyp.FAST_SIGN,LemonUtils.getUserId(),null);

		if(!JudgeUtils.isSuccess(rspDTO.getMsgCd())){
			LemonException.throwBusinessException(rspDTO.getMsgCd());
		}
		List<AgrInfoRspDTO.CardAgrInfo> agrList=rspDTO.getBody().getList();
		//借记卡
		List<AgrInfoRspDTO.CardAgrInfo> agrDList=new ArrayList<>();
		//贷记卡
		List<AgrInfoRspDTO.CardAgrInfo> agrCList=new ArrayList<>();
		for(AgrInfoRspDTO.CardAgrInfo item:agrList){
			if(JudgeUtils.equals(item.getCrdAcTyp(),D_CARD)){
				agrDList.add(item);
			}else{
				agrCList.add(item);
			}
		}

		Map agrsCards=new HashMap();
		agrsCards.put(D_CARD,agrDList);
		agrsCards.put(C_CARD,agrCList);
		return agrsCards;
	}

	public Map<String,List<AgrInfoRspDTO.CardAgrInfo>> queryBindCardsConfirmUserId(String userId){
		//防止第三方商户下单，消费用户从当前系统获取不到id
		String cuserId = LemonUtils.getUserId();
		if(JudgeUtils.isNull(cuserId)){
			cuserId = userId;
		}
		//crdAcTyp不设置，表示查询所有签约卡种银行卡
		GenericRspDTO<AgrInfoRspDTO> rspDTO=cardClient.queryCardsInfo(CorpBusTyp.SIGN,CorpBusSubTyp.FAST_SIGN,cuserId,null);

		if(!JudgeUtils.isSuccess(rspDTO.getMsgCd())){
			LemonException.throwBusinessException(rspDTO.getMsgCd());
		}
		List<AgrInfoRspDTO.CardAgrInfo> agrList=rspDTO.getBody().getList();
		//借记卡
		List<AgrInfoRspDTO.CardAgrInfo> agrDList=new ArrayList<>();
		//贷记卡
		List<AgrInfoRspDTO.CardAgrInfo> agrCList=new ArrayList<>();
		for(AgrInfoRspDTO.CardAgrInfo item:agrList){
			if(JudgeUtils.equals(item.getCrdAcTyp(),D_CARD)){
				agrDList.add(item);
			}else{
				agrCList.add(item);
			}
		}

		Map agrsCards=new HashMap();
		agrsCards.put(D_CARD,agrDList);
		agrsCards.put(C_CARD,agrCList);
		return agrsCards;
	}

	/**
	 * 获取快捷借记卡
	 * @return
	 */
	public List<AgrInfoRspDTO.CardAgrInfo> queryBindDCards(){
		return queryBindCards().get(D_CARD);
	}

	/**
	 * 获取快捷贷记卡
	 * @return
	 */
	public List<AgrInfoRspDTO.CardAgrInfo> queryBindCCards(){
		return queryBindCards().get(C_CARD);
	}

	/**
	 * 查询用户指定卡号签约银行详细信息
	 * @param mblNo 银行预留手机号
	 * @param cardNo 银行卡号
	 * @param agrInfoReqDTO 签约银行查询入参封装对象
	 * @return
	 * */
	public AgrInfoRspDTO.CardAgrInfo queryUserCardAgrInfo(String mblNo, String cardNo, AgrInfoReqDTO agrInfoReqDTO) {
		GenericDTO<AgrInfoRspDTO> genericAgrInfoRspDTO= cardClient.queryCardsInfo(
				agrInfoReqDTO.getCorpBusTyp(),agrInfoReqDTO.getCorpBusSubTyp(),LemonUtils.getUserId(),agrInfoReqDTO.getCrdAcTyp());
		AgrInfoRspDTO agrInfoRspDTO = genericAgrInfoRspDTO.getBody();

		if(JudgeUtils.isNull(agrInfoRspDTO)) {
			throw new LemonException("CSH20026");
		}

		List<AgrInfoRspDTO.CardAgrInfo> cardAgrInfoList = agrInfoRspDTO.getList();

		//查询签约银行卡详情(卡号加密)
		AgrInfoRspDTO.CardAgrInfo cardAgrInfo = null;
		for(AgrInfoRspDTO.CardAgrInfo ecardAgrInfo : cardAgrInfoList) {
			if(JudgeUtils.equals(ecardAgrInfo.getCrdNoEnc(), cardNo) && JudgeUtils.equals(ecardAgrInfo.getMblNo(), mblNo)) {
				cardAgrInfo = new  AgrInfoRspDTO.CardAgrInfo();
				BeanUtils.copyProperties(cardAgrInfo, ecardAgrInfo);
				break;
			}
		}
		return cardAgrInfo;
	}
}
