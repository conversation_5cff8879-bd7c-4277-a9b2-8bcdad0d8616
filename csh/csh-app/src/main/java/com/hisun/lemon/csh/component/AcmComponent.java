package com.hisun.lemon.csh.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.hisun.lemon.csh.enums.CouponType;
import com.hisun.lemon.csh.enums.TradeType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dto.cashier.CouponItem;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.mq.PaymentHandler;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.mkm.client.MarketActivityClient;
import com.hisun.lemon.mkm.req.dto.AutoRealeaseReqDTO;
import com.hisun.lemon.mkm.req.dto.ConsumeCouponReqDTO;
import com.hisun.lemon.mkm.req.dto.ConsumeSeaCcyReqDTO;
import com.hisun.lemon.mkm.req.dto.QueryUserMkmToolReqDTO;
import com.hisun.lemon.mkm.res.dto.AutoRealeaseRspDTO;
import com.hisun.lemon.mkm.res.dto.ConsumeCouponResDTO;
import com.hisun.lemon.mkm.res.dto.CouponDetail;
import com.hisun.lemon.mkm.res.dto.QueryUserMkmToolRspDTO;
import com.hisun.lemon.tfm.client.TfmServerClient;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateReqDTO;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateRspDTO;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateReqDTO;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateRspDTO;

/**
 * 账务组件
 *
 * <AUTHOR>
 *
 */
@Component
public class AcmComponent {
	private static final Logger logger = LoggerFactory.getLogger(AcmComponent.class);

	private static final String H_COUPON_NAME="hcoupon";                     //海币
	private static final String E_COUPON_NAME ="ecoupon";                    //电子券
	private static final String D_COUPON_NAME = "discountCoupon";            //折扣券
	private static final String C_COUPON_NAME = "ccoupon";                   //优惠券

	@Resource
	private AccountingTreatmentClient accountingTreatmentClient;

	@Resource
	private AccountManagementClient accountManagementClient;

	@Resource
	private MarketActivityClient marketActivityClient;

	@Resource
	private TfmServerClient tfmServerClient;

	@Resource
	OrderCommonComponent orderCommonComponent;

	@Resource
	PaymentHandler paymentHandler;

	/**
	 *
	 * @param userId
	 *            用户ID
	 * @param acTye
	 *            账户类型 1 现金 2 待结算款
	 * @return
	 */
	public String getAcmAcNo(String userId, String acTye) {
		QueryAcBalRspDTO queryAcBalRspDTO=this.getAcmAcInfo(userId,acTye);
		if(JudgeUtils.isNull(queryAcBalRspDTO)){
			LemonException.throwBusinessException("CSH20050");
		}

		return queryAcBalRspDTO.getAcNo();
	}
	/**
	 *
	 * @param userId
	 *            用户ID
	 * @param acType
	 *            账户类型 1 现金 2 待结算款
	 * @return
	 */
	public BigDecimal getAccountBal(String userId,String acType){
		QueryAcBalRspDTO queryAcBalRspDTO=this.getAcmAcInfo(userId,acType);
		if(JudgeUtils.isNull(queryAcBalRspDTO)){
			LemonException.throwBusinessException("CSH20050");
		}
		return queryAcBalRspDTO.getAcCurBal();
	}

	/**
	 * 生成账务请求对象
	 *
	 * @param orderNo
	 *            订单号
	 * @param txJrnNo
	 *            订单流水号
	 * @param txType
	 *            交易类型
	 * @param txSts
	 *            交易状态
	 * @param txAmt
	 *            交易金额
	 * @param acNo
	 *            账户号
	 * @param acType
	 *            账户类型
	 * @param capType
	 *            资金属性
	 * @param dcFlag
	 *            借贷标识
	 * @param itmNo
	 *            内部科目号
	 * @param oppAcNo
	 *            对手方账号
	 * @param oppCapType
	 *            对手方资金属性
	 * @param oppUsrId
	 *            对手方用户id
	 * @param oppUsrType
	 * @param remark
	 *            备注
	 * @return 账务请求信息对象
	 */
	//TODO 改造传入ccy字段
	public AccountingReqDTO createAccountingReqDTO(String orderNo, String txJrnNo, String txType, String txSts,
												   BigDecimal txAmt, String acNo, String acType, String capType, String dcFlag, String itmNo, String oppAcNo,
												   String oppCapType, String oppUsrId, String oppUsrType, String remark) {
		AccountingReqDTO accountReqDTO = new AccountingReqDTO();
		accountReqDTO.setTxTyp(txType);
		accountReqDTO.setTxSts(txSts);
		accountReqDTO.setTxAmt(txAmt);
		accountReqDTO.setTxJrnNo(txJrnNo);
		accountReqDTO.setTxOrdDt(DateTimeUtils.getCurrentLocalDate());
		accountReqDTO.setTxOrdTm(DateTimeUtils.getCurrentLocalTime());
		accountReqDTO.setTxOrdNo(orderNo);
		accountReqDTO.setAcNo(acNo);
		accountReqDTO.setAcTyp(acType);
		accountReqDTO.setCapTyp(capType);
		accountReqDTO.setDcFlg(dcFlag);
		accountReqDTO.setItmNo(itmNo);
		accountReqDTO.setOppAcNo(oppAcNo);
		accountReqDTO.setOppCapTyp(oppCapType);
		accountReqDTO.setOppUserId(oppUsrId);
		accountReqDTO.setOppUserTyp(oppUsrType);
		accountReqDTO.setRmk(remark);
		accountReqDTO.setUsrIpAdr(null);
		return accountReqDTO;
	}

	public GenericRspDTO<NoBody> requestAc(List<AccountingReqDTO> accountingReqDTOs) {
		BigDecimal dAmt = BigDecimal.ZERO;
		BigDecimal cAmt = BigDecimal.ZERO;
		List<AccountingReqDTO> accList = new ArrayList<>();
		for (AccountingReqDTO dto : accountingReqDTOs) {
			if (JudgeUtils.isNotNull(dto)) {
				accList.add(dto);
				if (JudgeUtils.equals(dto.getDcFlg(), ACMConstants.AC_D_FLG)) {
					dAmt = dAmt.add(dto.getTxAmt());
				} else {
					cAmt = cAmt.add(dto.getTxAmt());
				}
			}
		}

		// 借贷平衡校验
		if (cAmt.compareTo(dAmt) != 0) {
			LemonException.throwBusinessException("");
		}

		GenericDTO<List<AccountingReqDTO>> userAccDto = new GenericDTO<>();
		userAccDto.setBody(accList);
		return accountingTreatmentClient.accountingTreatment(userAccDto);
	}

	private QueryAcBalRspDTO getAcmAcInfo(String userId, String acTye) {
		UserAccountDTO userDTO = new UserAccountDTO();
		userDTO.setUserId(userId);
		GenericDTO<UserAccountDTO> user=new GenericDTO<>();
		user.setBody(userDTO);
		GenericRspDTO<List<QueryAcBalRspDTO>> genericQueryAcBalRspDTO = accountManagementClient.queryAcBal(userDTO);
		if(JudgeUtils.isNotSuccess(genericQueryAcBalRspDTO.getMsgCd())){
			logger.error("查询账户信息失败："+userId);
			LemonException.throwBusinessException(genericQueryAcBalRspDTO.getMsgCd());
		}

		List<QueryAcBalRspDTO> acmAcBalInfList = genericQueryAcBalRspDTO.getBody();

		if (JudgeUtils.isNull(acmAcBalInfList) || JudgeUtils.isEmpty(acmAcBalInfList)) {
			throw new LemonException("CSH20050");
		}

		for (QueryAcBalRspDTO queryAcBalRspDTO : acmAcBalInfList) {
			if (StringUtils.equals(queryAcBalRspDTO.getCapTyp(), acTye)) {
				return queryAcBalRspDTO;
			}
		}
		return null;
	}

	private QueryAcBalRspDTO getAcmAcInfo(String userId, String acTye, String ccy) {
		UserAccountDTO userDTO = new UserAccountDTO();
		userDTO.setUserId(userId);
		userDTO.setCcy(ccy);
		GenericDTO<UserAccountDTO> user=new GenericDTO<>();
		user.setBody(userDTO);
		GenericRspDTO<List<QueryAcBalRspDTO>> genericQueryAcBalRspDTO = accountManagementClient.queryAcBal(userDTO);
		if(JudgeUtils.isNotSuccess(genericQueryAcBalRspDTO.getMsgCd())){
			logger.error("查询账户信息失败："+userId);
			LemonException.throwBusinessException(genericQueryAcBalRspDTO.getMsgCd());
		}

		List<QueryAcBalRspDTO> acmAcBalInfList = genericQueryAcBalRspDTO.getBody();

		if (JudgeUtils.isNull(acmAcBalInfList) || JudgeUtils.isEmpty(acmAcBalInfList)) {
			throw new LemonException("CSH20050");
		}

		for (QueryAcBalRspDTO queryAcBalRspDTO : acmAcBalInfList) {
			if (StringUtils.equals(queryAcBalRspDTO.getCapTyp(), acTye)) {
				return queryAcBalRspDTO;
			}
		}
		return null;
	}

	/**
	 * 查询海币
	 * @param userId
	 * @return
	 */
	public Integer queryHCoupon(String userId,String mercId,BigDecimal orderAmt){
		QueryUserMkmToolRspDTO mkmToolRspDTO=queryMkmCoupons(userId,mercId, "01",orderAmt);
		return mkmToolRspDTO.getSeaCcyDetal().getCount();

	}

	/**
	 * 查询电子券
	 * @param userId
	 * @param mercId
	 * @return
	 */
	public List<CouponDetail> queryECoupons(String userId,String mercId,BigDecimal orderAmt){
		QueryUserMkmToolRspDTO mkmToolRspDTO=queryMkmCoupons(userId,mercId,"02",orderAmt);
		List<CouponDetail> mkmEcoupons=mkmToolRspDTO.getCouponDetail();

		List eCoupons=new ArrayList<>();
		for(CouponDetail couponDetail:mkmEcoupons){
			if(StringUtils.equals(couponDetail.getInstId(),mercId)){
				eCoupons.add(couponDetail);
			}
		}
		return eCoupons;
	}

	/**
	 * 查询所有的营销优惠：海币、电子券、折扣券等
	 * @param userId
	 * @param mercId
	 * @return
	 */
	public Map queryCoupons(String userId,String mercId,String orderNo,String txType,BigDecimal orderAmt){
		if(StringUtils.isBlank(userId)){
			return new HashMap<>();
		}

		QueryUserMkmToolRspDTO mkmToolRspDTO=queryMkmCoupons(userId,mercId,null,orderAmt);
		Integer hCouponAmt= 0;

		if(JudgeUtils.isNotNull(mkmToolRspDTO) && JudgeUtils.isNotNull(mkmToolRspDTO.getSeaCcyDetal())){
			hCouponAmt=mkmToolRspDTO.getSeaCcyDetal().getCount();
		}

		Map coupons=new HashMap<>();
		coupons.put(H_COUPON_NAME,hCouponAmt);

		List<CouponItem> dList=new ArrayList<>();	//折扣券集合
		List<CouponItem> eList=new ArrayList();		//电子券集合
		List<CouponItem> cList=new ArrayList();		//优惠券集合
		/*
		 * 营销工具类型 01-电子券 02-海币 03-优惠券 04-折扣券
		 */
		if(JudgeUtils.isNotNull(mkmToolRspDTO) && JudgeUtils.isNotNull(mkmToolRspDTO.getCouponDetail())){
			List<CouponDetail> mkmEcoupons=mkmToolRspDTO.getCouponDetail();
			if(!StringUtils.isBlank(mercId)){
				for(CouponDetail couponDetail:mkmEcoupons){
					//处理折扣券
					if (StringUtils.equals(couponDetail.getMkTool(),"04")){
						if(StringUtils.equals(couponDetail.getOrderNo(),orderNo)){
							CouponItem couponItem=new CouponItem();
							BeanUtils.copyProperties(couponItem,couponDetail);
							dList.add(couponItem);
						}
					}
					//处理电子券 只有消费可以使用电子券
					if (StringUtils.equals(couponDetail.getMkTool(),"01")){
						if(JudgeUtils.equals(txType, TradeType.CONSUME.getType())){
							CouponItem couponItem=new CouponItem();
							BeanUtils.copyProperties(couponItem,couponDetail);
							eList.add(couponItem);
						}
					}
					//处理优惠券
					if (StringUtils.equals(couponDetail.getMkTool(),"03")){
						if(StringUtils.equals(couponDetail.getInstId(),mercId)){
							CouponItem couponItem=new CouponItem();
							BeanUtils.copyProperties(couponItem,couponDetail);
							cList.add(couponItem);
						}
					}
				}
			}
		}
		coupons.put(E_COUPON_NAME,eList);
		coupons.put(D_COUPON_NAME,dList);
		coupons.put(C_COUPON_NAME,cList);
		return coupons;
	}

	public int getHCoupon(Map coupons){
		if(JudgeUtils.isEmpty(coupons)){
			return 0;
		}
		return (Integer)coupons.get(H_COUPON_NAME);
	}

	public List<CouponItem> getECoupon(Map coupons){
		if(JudgeUtils.isEmpty(coupons)){
			return Collections.emptyList();
		}
		return (List)coupons.get(E_COUPON_NAME);
	}

	public List<CouponItem> getDiscountCoupon(Map coupons){
		if(JudgeUtils.isEmpty(coupons)){
			return Collections.emptyList();
		}
		return (List)coupons.get(D_COUPON_NAME);
	}

	public List<CouponItem> getCCoupon(Map coupons){
		if(JudgeUtils.isEmpty(coupons)){
			return Collections.emptyList();
		}
		return (List)coupons.get(C_COUPON_NAME);
	}

	public QueryUserMkmToolRspDTO  queryMkmCoupons(String userId,String mercId,String type, BigDecimal orderAmt){
		QueryUserMkmToolReqDTO mkmToolReqDTO =new QueryUserMkmToolReqDTO();
		if(StringUtils.isNoneBlank(type)){
			mkmToolReqDTO.setMkTool(type);
		}
		mkmToolReqDTO.setUserId(userId);
		mkmToolReqDTO.setInstId(mercId);
		mkmToolReqDTO.setOrderAmt(orderAmt);
		mkmToolReqDTO.setVal("Y");//查询有效优惠券
		GenericDTO mkmQueryDto=new GenericDTO();
		mkmQueryDto.setBody(mkmToolReqDTO);
		GenericRspDTO<QueryUserMkmToolRspDTO> rspDTO=marketActivityClient.queryUserMkmTool(mkmQueryDto);

		if(!JudgeUtils.isSuccess(rspDTO.getMsgCd())){
			throw  new LemonException(rspDTO.getMsgCd());
		}
		return rspDTO.getBody();
	}

	/**
	 * 发放折扣券
	 * @param orderDO
	 */
	public GenericRspDTO<AutoRealeaseRspDTO> giveDiscountCoupon(OrderDO orderDO){
		AutoRealeaseReqDTO mkmToolReqDTO =new AutoRealeaseReqDTO();

		mkmToolReqDTO.setOrderNo(orderDO.getOrderNo());
		mkmToolReqDTO.setInstId(orderDO.getPayeeId());
		mkmToolReqDTO.setMkTool("04");
		mkmToolReqDTO.setOrderAmt(orderDO.getOrderAmt());
		mkmToolReqDTO.setSeq(orderDO.getOrderNo());
		mkmToolReqDTO.setUserId(orderDO.getPayerId());
		mkmToolReqDTO.setTraceTime(DateTimeUtils.getCurrentLocalDateTime());
		GenericDTO mkmQueryDto=new GenericDTO();
		mkmQueryDto.setBody(mkmToolReqDTO);

		return marketActivityClient.autoRealease(mkmQueryDto);
	}

	/**
	 * 消费券
	 */
	public void useCoupon(String orderNo,String couponType,String couponNo,BigDecimal amt,String userId,String mercId,BigDecimal couponAmt){
		logger.info("=======消费营销优惠=======");
		if(StringUtils.isNoneBlank(couponType)){
			logger.info("=======couponType======="+couponType);
		}
		if(JudgeUtils.isNotNull(couponAmt)){
			logger.info("=======couponAmt======="+couponAmt);
		}
		if(JudgeUtils.isBlank(couponNo)
				&& JudgeUtils.equalsAny(couponType,CouponType.E_COUPON.getType(),CouponType.D_COUPON.getType(),CouponType.C_COUPON.getType())){
			throw  new LemonException("CSH20101");
		}

		if(StringUtils.isNoneBlank(couponNo)
				&& JudgeUtils.equalsAny(couponType,CouponType.E_COUPON.getType(),CouponType.D_COUPON.getType(),CouponType.C_COUPON.getType())){
			ConsumeCouponReqDTO consumeCouponReqDTO =new ConsumeCouponReqDTO();
			consumeCouponReqDTO.setOrderAmt(amt);
			consumeCouponReqDTO.setUserId(userId);
			consumeCouponReqDTO.setInstId(mercId);
			consumeCouponReqDTO.setOrderNo(orderNo);
			consumeCouponReqDTO.setSeq(orderNo);
			consumeCouponReqDTO.setType("02");
			consumeCouponReqDTO.setInstId(mercId);
			consumeCouponReqDTO.setConsumeTm(DateTimeUtils.getCurrentLocalDateTime());
			consumeCouponReqDTO.setCouponNo(couponNo);

			if(StringUtils.equals(couponType, CouponType.D_COUPON.getType())){
				consumeCouponReqDTO.setMkTool("04");
			}else if(StringUtils.equals(couponType, CouponType.E_COUPON.getType())){
				consumeCouponReqDTO.setMkTool("01");
			}

			GenericDTO mkmQueryDto=new GenericDTO();
			mkmQueryDto.setBody(consumeCouponReqDTO);

			GenericRspDTO<ConsumeCouponResDTO> rspDTO=marketActivityClient.consumeCoupon(mkmQueryDto);
			if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
				if(StringUtils.equals(couponType,CouponType.D_COUPON.getType())){
					logger.error("订单["+orderNo+"]消费折扣券失败:"+rspDTO.getMsgCd());
				}else{
					logger.error("订单["+orderNo+"]消费优惠券失败:"+rspDTO.getMsgCd());
				}
				LemonException.throwBusinessException(rspDTO.getMsgCd());
			}
		}

		if(StringUtils.equals(couponType,CouponType.H_COUPON.getType())){
			//海币优惠为0不调用消费海币接口
			if(JudgeUtils.isNotNull(couponAmt) && (couponAmt.compareTo(BigDecimal.valueOf(0)) >0)){
				logger.info("=======消费海币=======");
				ConsumeSeaCcyReqDTO consumeSeaCcyReqDTO=new ConsumeSeaCcyReqDTO();
				consumeSeaCcyReqDTO.setSeq(orderNo);
				consumeSeaCcyReqDTO.setCount(couponAmt.intValue());
				consumeSeaCcyReqDTO.setMkTool("02");
				consumeSeaCcyReqDTO.setUserId(userId);
				consumeSeaCcyReqDTO.setOrderNo(orderNo);
				consumeSeaCcyReqDTO.setType("02");
				consumeSeaCcyReqDTO.setInstId(mercId);
				consumeSeaCcyReqDTO.setOrderAmt(amt);
				consumeSeaCcyReqDTO.setConsumeTm(DateTimeUtils.getCurrentLocalDateTime());
				GenericDTO genericDTO=new GenericDTO();
				genericDTO.setBody(consumeSeaCcyReqDTO);
				GenericRspDTO rspDTO=marketActivityClient.consumeSeaCcy(genericDTO);
				if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
					logger.error("订单["+orderNo+"]消费海币失败:"+rspDTO.getMsgCd());
					LemonException.throwBusinessException(rspDTO.getMsgCd());
				}
			}

		}
	}


	/**
	 * 手续费预算
	 * @param busType   业务类型
	 * @param amt       金额
	 * @param ccy       币种   （默认为USD 美元）
	 * @return
	 */
	public GenericRspDTO caculateFee(String busType,BigDecimal amt,String ccy){
		TradeFeeCaculateReqDTO tradeFeeCaculateReqDTO=new TradeFeeCaculateReqDTO();
		if(StringUtils.isBlank(ccy)){
			tradeFeeCaculateReqDTO.setCcy(CshConstants.QP_PAY_CCY);
		}else{
			tradeFeeCaculateReqDTO.setCcy(ccy);
		}
		tradeFeeCaculateReqDTO.setBusType(busType);
		tradeFeeCaculateReqDTO.setTradeAmt(amt);
		GenericDTO<TradeFeeCaculateReqDTO> feeCaculateReqDTO=new GenericDTO();
		feeCaculateReqDTO.setBody(tradeFeeCaculateReqDTO);
		GenericRspDTO<TradeFeeCaculateRspDTO> rspDto= tfmServerClient.tradeFeeCaculate(feeCaculateReqDTO);
		if(!JudgeUtils.isSuccess(rspDto.getMsgCd())){
			logger.error("计算手续费失败：" + rspDto.getMsgCd());
			LemonException.throwBusinessException(rspDto.getMsgCd());
		}
		return rspDto;
	}

	/**
	 *
	 * @param userId 用户ID
	 * @param acTye 账户类型 1 现金 8 待结算款
	 * @param ccy 币种
	 * @return
	 */
	public String getAcmAcNo(String userId, String acTye, String ccy) {
		QueryAcBalRspDTO queryAcBalRspDTO=this.getAcmAcInfo(userId,acTye,ccy);
		if(JudgeUtils.isNull(queryAcBalRspDTO)){
			LemonException.throwBusinessException("CSH20050");
		}
		return queryAcBalRspDTO.getAcNo();
	}
}
