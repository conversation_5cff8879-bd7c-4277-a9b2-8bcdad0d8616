package com.hisun.lemon.csh.service.chk;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service
public class OnrRfdChkFileServiceImpl extends AbstractChkFileService {

    public OnrRfdChkFileServiceImpl() {
        super();
        this.appCnl="ONR";
        this.chkOrderStatus=new String[]{"R3","C","RP"};
        this.chkTxTypes=new String[]{"0602"};
        this.lockName="CSH_ONR_RFD_CHK_FILE_LOCK";
        this.refund=true;
    }
}
