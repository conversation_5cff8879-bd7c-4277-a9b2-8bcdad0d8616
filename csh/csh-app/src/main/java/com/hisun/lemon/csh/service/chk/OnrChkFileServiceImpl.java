package com.hisun.lemon.csh.service.chk;

import com.hisun.lemon.csh.enums.OrderStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.csh.constants.CshConstants;

@Transactional
@Service
public class OnrChkFileServiceImpl extends AbstractChkFileService {

    public OnrChkFileServiceImpl() {
        super();
        this.appCnl="ONR";
        this.chkOrderStatus=new String[]{
                OrderStatus.SUCC.getValue(),
                OrderStatus.REFUND.getValue(),
                OrderStatus.PART_REFUND.getValue()
        };
        this.chkTxTypes= new String[]{ "02" };
        this.lockName="CSH_ONR_CHK_FILE_LOCK";
    }
}
