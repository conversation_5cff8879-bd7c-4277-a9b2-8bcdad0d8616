package com.hisun.lemon.csh.service.impl;

import javax.annotation.Resource;

import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.csh.dao.IOrderDao;
import com.hisun.lemon.csh.dao.IPayJrnDao;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;

import java.util.List;
import java.util.Map;

@Transactional
@Service
public class OrderTransactionalService extends BaseService {
    @Resource
    private IOrderDao orderDao; 
    
    @Resource
    private IPayJrnDao jrnDao; 

	
	/**
	 * 退款成功后更新原订单
	 * @param orderDO
	 */
   public void updateOrj(OrderDO orderDO){
	   int  result=orderDao.update(orderDO);
	   if (result != 1) {
		   throw new LemonException("CSH20064");
	   }
   }
	public void initOrder(OrderDO orderDO){
		int nums=orderDao.insert(orderDO);
		if(nums!=1){
			throw new LemonException("CSH20006");
		}
    }
 
	public void initJrn(OrderDO updOrderDO,PayJrnDO orderDO){
		int nums=jrnDao.insert(orderDO);
		if(nums!=1){
			throw new LemonException("CSH20007");
		}
		if(updOrderDO!=null){
			int num2=orderDao.update(updOrderDO);
			if(num2!=1){
				throw new LemonException("CSH20008");
			}
		}

    }
	
	public void updateJrnAndOrder(OrderDO updOrderDO,PayJrnDO updPayJrnDO){
		int num1=jrnDao.update(updPayJrnDO);
		if(num1!=1){
			throw new LemonException("CSH20009");
		}
		int num2=orderDao.update(updOrderDO);
		if(num2!=1){
			throw new LemonException("CSH20010");
		}
	}

	public void updateOrder(OrderDO orderDO) {
		int num1= this.orderDao.update(orderDO);
		if(num1 != 1) {
			throw new LemonException("CSH20034");
		}
	}

	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public OrderDO queryOrder(String orderNo){
		return orderDao.get(orderNo);
	}

	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public PayJrnDO queryJrn(String jrnNo){
		return jrnDao.get(jrnNo);
	}

	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public OrderDO queryOrderNotNone(String orderNo){
		OrderDO orderDO=queryOrder(orderNo);
		if(null == orderDO){
			throw new LemonException("CSH20053");
		}
		return orderDO;
	}

	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public PayJrnDO queryJrnNotNone(String jrnNo) {
		PayJrnDO payJrnDO=queryJrn(jrnNo);
		if(null==payJrnDO){
			throw new LemonException("CSH20054");
		}
		return payJrnDO;
	}

	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public PayJrnDO queryJrnByOrderNo(String orderNo) {
		PayJrnDO payJrnDO=jrnDao.getJrnByOrderNo(orderNo);
		if(null==payJrnDO){
			throw new LemonException("CSH20054");
		}
		return payJrnDO;
	}

	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public OrderDO queryOrderByBusNo(String busOrderNo){
		return orderDao.getByBusOrder(busOrderNo);
	}


	@Transactional(propagation = Propagation.SUPPORTS,readOnly = true)
	public List<OrderDO> queryExpList(Map o){
		return orderDao.queryExpOrders(o);
	}


	public IOrderDao getOrderDao() {
		return orderDao;
	}

	public void setOrderDao(IOrderDao orderDao) {
		this.orderDao = orderDao;
	}

	public IPayJrnDao getJrnDao() {
		return jrnDao;
	}

	public void setJrnDao(IPayJrnDao jrnDao) {
		this.jrnDao = jrnDao;
	}

	/**
	 * 数币转账账务处理
	 * @param req
	 */
	public void handleFinance(HandleFinanceDTO req) {

	}
}
