package com.hisun.lemon.csh.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.framework.dao.BaseDao;

/**
 * <AUTHOR>
 * @date 2017年6月7日
 * @time 下午1:54:26
 *
 */
@Mapper
public interface IOrderDao extends BaseDao<OrderDO> {

    public OrderDO getOrderByOrderNoAndPayerId(@Param("orderNo")String orderNo, @Param("payerId") String payerId);

    public List<OrderDO> queryList(Map o);

    public List<OrderDO> queryNotFinalAuditList(Map o);
    public List<OrderDO> queryCpiOrder(Map o);

    public OrderDO getByBusOrder(@Param("busOrderNo")String busOrderNo);

    public List<OrderDO> queryExpOrders(Map o);
}