package com.hisun.lemon.csh.service.impl;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.csh.enums.AcItem;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.PlatFormPrinItemEnum;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.tam.constants.CcyAcItemEnum;
import com.hisun.lemon.tam.constants.TamConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class TamExchangeOrderServiceImpl extends AbstractOrderService{
    private static final Logger logger = LoggerFactory.getLogger(TamExchangeOrderServiceImpl.class);

    protected String txType = TradeType.EXCHANGE.getType();
    protected String[] busTypes = new String[]{
            BussinessType.EXCHANGE_COIN.getValue()
    };

    @Override
    public boolean match(String txType) {
        return JudgeUtils.equals(this.txType,txType);
    }

    @Override
    protected void checkBeforeInitOrder(OrderDO orderDO) {
        logger.debug("-------------初始化订单业务检查-------------。订单号 [" + orderDO.getOrderNo() + "]");
        String payerId= LemonUtils.getUserId();
        riskUserStatus(orderDO.getTxType(),payerId,"01");
        if(StringUtils.equals(orderDO.getBusType(),"01") ||StringUtils.equals(orderDO.getBusType(),"03")){
            riskUserStatus(orderDO.getTxType(),orderDO.getPayeeId(),"01");
        }
        logger.debug("-------------初始化订单业务检查完成----------。订单号 [" + orderDO.getOrderNo() + "]");
    }

    /**
     * 暂收账务处理
     * @param orderDO
     */
    public void tempReceipt(OrderDO orderDO) {
        logger.info("对提交的兑换订单进行第一步暂收账务处理");
        //根据币种选择收银台科目号
        String cshItemNo = "";
        for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
            if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), orderDO.getCcy())) {
                cshItemNo = ccyAcItemEnum.getItemNo();
            }
        }
        // 账务处理
        AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
        AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
        AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
        List<AccountingReqDTO> acUserList = new ArrayList();
        // 流水号
        String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
        payJrnNo = orderDO.getBusType() + payJrnNo;
        // 资金类型
        String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();

        BigDecimal orderAmt = orderDO.getOrderAmt();
        String acNo = orderDO.getAcNo();
        // 借：其他应付款-支付账户-现金账户 -100
        userAccountReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                payJrnNo, orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderAmt, acNo,
                ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_D_FLG, AcItem.O_BAL.getValue(), null, null,
                null, null, "兑换——收银台暂收");
        acUserList.add(userAccountReqDTO);
        // 贷：其他应付款-暂收-收银台 +100
        cshItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                payJrnNo, orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderAmt, null,
                ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG, cshItemNo, acNo, null,
                null, null,"兑换——收银台暂收");
        acUserList.add(cshItemReqDTO);
        acmComponent.requestAc(acUserList);
        logger.info("请求账务处理完成=========================");
    }

    /**
     * 冲正账务处理
     * @param orderDO
     */
    public void jrnReversal(OrderDO orderDO) {
        logger.info("失败兑换订单——账务冲正");
        //根据币种选择收银台科目号
        String cshItemNo = "";
        for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
            if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), orderDO.getCcy())) {
                cshItemNo = ccyAcItemEnum.getItemNo();
            }
        }
        // 流水号
        String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
        payJrnNo = orderDO.getBusType() + payJrnNo;
        // 账务处理
        AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
        AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
        AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
        List<AccountingReqDTO> acUserList = new ArrayList();
        // 资金类型
        String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();

        BigDecimal orderAmt = orderDO.getOrderAmt();
        String acNo = orderDO.getAcNo();

        // 借:其他应付款-暂收-收银台 -100
        cshItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnNo, orderDO.getTxType(),
                ACMConstants.ACCOUNTING_NOMARL, orderAmt, null, ACMConstants.ITM_AC_TYP, balCapType,
                ACMConstants.AC_D_FLG, cshItemNo, acNo, null, null, null, "兑换——失败冲正");
        // 贷：其他应付款-支付账户-现金账户 +100
        userAccountReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnNo,
                orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderAmt, acNo, ACMConstants.USER_AC_TYP,
                balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "兑换——失败冲正");
        acUserList.add(cshItemReqDTO);
        acUserList.add(userAccountReqDTO);
        acmComponent.requestAc(acUserList);
        logger.info("兑换冲正账务处理成功");
    }

    @Override
    protected void checkJrnWithBussiness(GenericDTO inputGenericDto) {

    }

    @Override
    protected List<AccountingReqDTO> createAccQueue(OrderDO orderDO, PayJrnDO payJrnDO) {
        // 根据币种选择收银台科目号
        String cshItemNo = "";
        for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
            if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), orderDO.getCcy())) {
                cshItemNo = ccyAcItemEnum.getItemNo();
            }
        }
        // 根据币种选择平台本金科目号
        String platFormDmPrinItemNo = "";
        String platFormFmPrinItemNo = "";
        // 手续费科目号
        String platFormDmFeeItemNo = "";

        BigDecimal fee = orderDO.getFee();
        BigDecimal usdFee = orderDO.getLeftFee();
        BigDecimal orderAmt = orderDO.getOrderAmt();
        BigDecimal toAmount = orderDO.getToAmount();
        String acNo = orderDO.getAcNo();
        // 收款方用户id
        String balAcNo = orderDO.getPayeeId();
        // 账务处理
        AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
        AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
        AccountingReqDTO platFormDmPrinReqDTO = null; // 平台数币本金账务对象
        AccountingReqDTO platFormFmPrinReqDTO = null; // 平台法币本金账务对象
        AccountingReqDTO dmFeeReqDTO = null; // 数币手续费账对象
        AccountingReqDTO feeItemReqDTO = null; // 法币手续费账务对象
        List<AccountingReqDTO> acUserList = new ArrayList();
        // 流水号
        String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
        payJrnNo = orderDO.getBusType() + payJrnNo;
        // 资金类型
        String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();

        // 借:其他应付款-暂收-收银台 -100
        cshItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnNo, orderDO.getTxType(),
                ACMConstants.ACCOUNTING_NOMARL, orderAmt, null, ACMConstants.ITM_AC_TYP, balCapType,
                ACMConstants.AC_D_FLG, cshItemNo, acNo, null, null, null, "兑换——收银台完成订单");
        acUserList.add(cshItemReqDTO);

        if(JudgeUtils.equals(orderDO.getDirection(), TamConstants.DM_TO_FM)) {
            logger.info("账务处理——数转法");
            for(PlatFormPrinItemEnum platFormPrinItemEnum : PlatFormPrinItemEnum.values()) {
                if(JudgeUtils.equals(platFormPrinItemEnum.getCcy(), orderDO.getFromCoin())) {
                    platFormDmPrinItemNo = platFormPrinItemEnum.getItemNo();
                }
                if(JudgeUtils.equals(platFormPrinItemEnum.getCcy(), orderDO.getToCoin())) {
                    platFormFmPrinItemNo = platFormPrinItemEnum.getItemNo();
                }
            }
            for(CcyAcItemEnum ccyAcItemEnum : CcyAcItemEnum.values()) {
                if(JudgeUtils.equals(ccyAcItemEnum.getCcy(), orderDO.getFromCoin())) {
                    platFormDmFeeItemNo = ccyAcItemEnum.getItemNo();
                }
            }
            // 贷:平台数币本金科目 +95
            platFormDmPrinReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                    payJrnNo, orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderAmt.subtract(fee), null,
                    ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG, platFormDmPrinItemNo, acNo, null,
                    null, null,"兑换——数转法平台数币本金入账");
            acUserList.add(platFormDmPrinReqDTO);
            // 贷:平台数币手续费科目 +5
            dmFeeReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                    payJrnNo, orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, fee, null,
                    ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG, platFormDmFeeItemNo, acNo, null,
                    null, null,"兑换——数转法平台数币手续费入账");
            acUserList.add(dmFeeReqDTO);

            // 借:银行存款-备付金账户-XX银行 -95
            platFormFmPrinReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnNo, orderDO.getTxType(),
                    ACMConstants.ACCOUNTING_NOMARL, toAmount, null, ACMConstants.ITM_AC_TYP, balCapType,
                    ACMConstants.AC_D_FLG, platFormFmPrinItemNo, balAcNo,
                    null, null, null, "兑换——数转法平台法币出账");
            // 贷：其他应付款-支付账户-现金账户 +95
            userAccountReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnNo,
                    orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, toAmount, balAcNo, ACMConstants.USER_AC_TYP,
                    balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL,
                    null, null, null, null, "兑换——数转法用户法币入账");
            acUserList.add(platFormFmPrinReqDTO);
            acUserList.add(userAccountReqDTO);
            acmComponent.requestAc(acUserList);
            logger.info("请求账务处理完成=========================");
        } else if(JudgeUtils.equals(orderDO.getDirection(), TamConstants.FM_TO_DM)) {
            logger.info("账务处理——法转数");
            for(PlatFormPrinItemEnum platFormPrinItemEnum : PlatFormPrinItemEnum.values()) {
                if(JudgeUtils.equals(platFormPrinItemEnum.getCcy(), orderDO.getFromCoin())) {
                    platFormFmPrinItemNo = platFormPrinItemEnum.getItemNo();
                }
                if(JudgeUtils.equals(platFormPrinItemEnum.getCcy(), orderDO.getToCoin())) {
                    platFormDmPrinItemNo = platFormPrinItemEnum.getItemNo();
                }
            }

            // 贷：银行存款-备付金账户-XX银行 +95
            platFormFmPrinReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                    payJrnNo, orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderAmt.subtract(fee), null,
                    ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG, platFormFmPrinItemNo, acNo, null,
                    null, null,"兑换——法转数平台法币本金入账");
            acUserList.add(platFormFmPrinReqDTO);
            // 贷：手续费收入-支付账户-转账 +5
            feeItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnNo,
                    orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, usdFee, null, ACMConstants.ITM_AC_TYP, balCapType,
                    ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAY, acNo, null, null, null,
                    "兑换——法转数平台法币手续费入账");
            acUserList.add(feeItemReqDTO);

            // 借:数币转账-收款科目账户 -95
            platFormDmPrinReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnNo, orderDO.getTxType(),
                    ACMConstants.ACCOUNTING_NOMARL, toAmount, null, ACMConstants.ITM_AC_TYP, balCapType,
                    ACMConstants.AC_D_FLG, platFormDmPrinItemNo, balAcNo,
                    null, null, null, "兑换——法转数平台数币出账");
            acUserList.add(platFormDmPrinReqDTO);
            // 贷：其他应付款-支付账户-现金账户 +95
            userAccountReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnNo,
                    orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, toAmount, balAcNo, ACMConstants.USER_AC_TYP,
                    balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL,
                    null, null, null, null, "兑换——法转数用户数币入账");
            acUserList.add(userAccountReqDTO);
            acmComponent.requestAc(acUserList);
            logger.info("请求账务处理完成=========================");
        }

        return Collections.emptyList();
    }

    @Override
    protected void notifyBussiness(OrderDO orderDO, PayJrnDO payJrnDO) {

    }
}
