package com.hisun.lemon.csh.service.chk;

import com.hisun.lemon.csh.enums.OrderStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.tam.constants.TamConstants;

@Transactional
@Service
public class TamChkFileServiceImpl extends AbstractChkFileService {

    public TamChkFileServiceImpl() {
        super();
        this.appCnl="TAM";
        this.chkOrderStatus=new String[]{OrderStatus.SUCC.getValue()};
        this.chkTxTypes= new String[]{
                TamConstants.TX_TYPE_TRANSFER
        };
        this.lockName="CSH_TAM_CHK_FILE_LOCK";
    }
}
