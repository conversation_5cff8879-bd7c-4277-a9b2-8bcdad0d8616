package com.hisun.lemon.csh.component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.csh.dto.order.TradeOffDTO;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.CouponType;
import com.hisun.lemon.csh.enums.OrderStatus;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.csh.mq.BillSyncHandler;
import com.hisun.lemon.csh.mq.PaymentHandler;
import com.hisun.lemon.csh.service.IRefundOrderService;
import com.hisun.lemon.csh.service.impl.OrderTransactionalService;
import com.hisun.lemon.framework.i18n.LocaleMessageSource;
import com.hisun.lemon.pwm.constants.OfflineBilExtConstants;
import com.hisun.lemon.pwm.constants.PwmConstants;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tfm.client.TfmServerClient;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateReqDTO;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateRspDTO;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateReqDTO;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateRspDTO;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.springframework.context.NoSuchMessageException;
import org.springframework.stereotype.Component;

import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.bil.dto.CreateUserBillDTO;
import com.hisun.lemon.bil.dto.UpdateUserBillDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpm.client.CpmOrderAccountClient;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PaytypeDO;
import com.hisun.lemon.csh.dto.cashier.CashierViewDTO;
import com.hisun.lemon.csh.dto.cashier.CouponItem;
import com.hisun.lemon.csh.dto.cashier.QpCardInfoDTO;
import com.hisun.lemon.csh.dto.cashier.QuotaDTO;
import com.hisun.lemon.csh.dto.tradeFee.TradeFeeReqDTO;
import com.hisun.lemon.csh.dto.tradeFee.TradeFeeRspDTO;
import com.hisun.lemon.csh.service.IPaytypeService;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.LemonUtils;

/**
 * 订单公共组件
 *
 * <AUTHOR>
 *
 */
@Component
public class OrderCommonComponent {
    public static final int CREATE_BIL=1;
    public static final int UPD_BIL=2;


    @Resource
    private AcmComponent acmComponent;

    @Resource
    QuickPaymentComponent quickPaymentComponent;

    @Resource
    CpmOrderAccountClient cpmOrderAccountClient;

    @Resource
    private IPaytypeService paytypeService;

    @Resource
    protected IRefundOrderService refundOrderService;

    @Resource
    BillSyncHandler billSyncHandler;

    @Resource
    protected PaymentHandler paymentHandler;

    @Resource
    protected OrderTransactionalService orderTransactionalService;
    @Resource
    LocaleMessageSource localeMessageSource;

    @Resource
    UserBasicInfClient userBasicInfClient;
    
	@Resource
	TfmServerClient tfmServerClient;

    /**
     * 配额
     * @param totalAmt   订单金额
     * @param coupons    优惠
     * @param enableHCoupon  是否允许使用海币
     * @return minAcAmt:最少要支付的现金（补款、余额等）<br/>
     *         quotaHCoupon:使用海币数量<br/>
     *         quotaAc:支付金额<br/>
     */
    public QuotaDTO makeQuota(BigDecimal totalAmt,Map coupons, boolean enableHCoupon) {

        //计算可以最大优惠的配额
        CouponItem couponItem = caculateMaxDiscount(totalAmt,coupons,enableHCoupon);
        if(JudgeUtils.isNotNull(couponItem)){
            String couponType = couponItem.getMkTool();
            //折扣券
            if(JudgeUtils.equals(CouponType.C_COUPON.getType(),couponType)){
                return this.makeQuotaDiscountCoupon(totalAmt,couponItem.getAmt(),couponItem.getCouponNo());
            //电子券
            }else if(JudgeUtils.equals(CouponType.E_COUPON.getType(),couponType)){
                return this.makeQuotaECoupon(totalAmt,couponItem.getAmt(),couponItem.getCouponNo());
            //海币
            }else if(JudgeUtils.equals(CouponType.H_COUPON.getType(),couponType)){
                return this.makeQuotaHCoupons(totalAmt,acmComponent.getHCoupon(coupons));
            }
        }

        return new QuotaDTO(BigDecimal.valueOf(0),totalAmt,null, CouponType.NONE.getType());
    }

    /**
     * 计算最大优惠配额
     * @param coupons
     * @param enableHCoupon
     * @return
     */
    private CouponItem caculateMaxDiscount(BigDecimal totalAmt,Map coupons, boolean enableHCoupon) {
        List<CouponItem> filterCouponItemList = new ArrayList<>();
        List<CouponItem> couponItemList = new ArrayList<>();
        CouponItem couponItem = null;

        List<CouponItem> dList=acmComponent.getDiscountCoupon(coupons);
        List<CouponItem> eList=acmComponent.getECoupon(coupons);

        couponItemList.addAll(dList);
        couponItemList.addAll(eList);

        filterCouponItemList = couponItemList.stream().filter(ecoupon -> (ecoupon!=null) && ecoupon.getAmt().compareTo(totalAmt) < 0).collect(Collectors.toList());
        Optional<CouponItem> optionalCoupon= filterCouponItemList.stream().max(Comparator.comparing(CouponItem::getAmt));
        if(optionalCoupon.isPresent()){
            couponItem = optionalCoupon.get();
        }
        //若是不支持海币，则直接返回
        if(!enableHCoupon){
            return couponItem;
        }

        BigDecimal hcouponAmt = BigDecimal.ZERO;
        QuotaDTO hquotaDTO = this.makeQuotaHCoupons(totalAmt,acmComponent.getHCoupon(coupons));
        //海币优惠抵扣的金额
        hcouponAmt = hquotaDTO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE));
        //若是没有电子券和折扣券，直接返回最优配额是电子券
        if(JudgeUtils.isNull(couponItem) && hcouponAmt.compareTo(BigDecimal.ZERO) > 0){
            couponItem = new CouponItem();
            couponItem.setMkTool(CouponType.H_COUPON.getType());
            return couponItem;
        }
        //若是有电子券或者折扣券，取其中优惠最大值和海币优惠金额进行比较，返回面额大的优惠
        if(JudgeUtils.isNotNull(couponItem) && hcouponAmt.compareTo(BigDecimal.ZERO) > 0){
            if(couponItem.getAmt().compareTo(hcouponAmt) < 0) {
                couponItem = new CouponItem();
                couponItem.setMkTool(CouponType.H_COUPON.getType());
                return couponItem;
            }
        }

        return couponItem;
    }

    /**
     * 配额海币
     * @return
     */
    public QuotaDTO makeQuotaHCoupons(BigDecimal amt, int hCouponAmt){
        //最大可以使用优惠抵扣的金额
        BigDecimal maxHCoupon=amt.multiply(BigDecimal.valueOf(CshConstants.USE_COUPON_RATE))
                .setScale(2, BigDecimal.ROUND_DOWN);

        //最大限度使用海币数量
        BigDecimal maxHCouponAmt=maxHCoupon.divide(BigDecimal.valueOf(CshConstants.H_USD_RATE)).setScale(0,BigDecimal.ROUND_HALF_UP);

        //海币数量足够抵扣最大优惠
        BigDecimal useHCouponAmt=BigDecimal.valueOf(hCouponAmt);
        if(maxHCouponAmt.compareTo(useHCouponAmt)<=0){
            useHCouponAmt=maxHCouponAmt;
        }
        BigDecimal couponAmt=useHCouponAmt.multiply(BigDecimal.valueOf(CshConstants.H_USD_RATE))
                .setScale(2, BigDecimal.ROUND_HALF_DOWN);
        BigDecimal payAmt=amt.subtract(couponAmt).setScale(2, BigDecimal.ROUND_HALF_UP);
        return new QuotaDTO(useHCouponAmt,payAmt,null,CouponType.H_COUPON.getType());
    }


    /**
     * 配额电子券
     * @param orderAmt
     * @param couponAmt
     * @param couponNo
     * @return
     */
    public QuotaDTO makeQuotaECoupon(BigDecimal orderAmt,BigDecimal couponAmt,String couponNo){
        BigDecimal payAmt=orderAmt.subtract(couponAmt).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal useCouponAmt=couponAmt.setScale(2, BigDecimal.ROUND_HALF_UP);
        if(payAmt.compareTo(BigDecimal.ZERO)<0){
            payAmt=BigDecimal.valueOf(0);
        }
        return new QuotaDTO(useCouponAmt,payAmt,couponNo,CouponType.E_COUPON.getType());
    }

    /**
     * 配额优惠券
     * @param orderAmt
     * @param couponAmt
     * @param couponNo
     * @return
     */
    public QuotaDTO makeQuotaCCoupon(BigDecimal orderAmt,BigDecimal couponAmt,String couponNo){
        BigDecimal payAmt=orderAmt.subtract(couponAmt).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal useCouponAmt=couponAmt.setScale(2, BigDecimal.ROUND_HALF_UP);
        if(payAmt.compareTo(BigDecimal.ZERO)<0){
            payAmt=BigDecimal.valueOf(0);
        }
        return new QuotaDTO(useCouponAmt,payAmt,couponNo,CouponType.C_COUPON.getType());
    }

    /**
     *
     * @param orderAmt
     * @param couponAmt
     * @param couponNo
     * @return
     */
    public QuotaDTO makeQuotaDiscountCoupon(BigDecimal orderAmt,BigDecimal couponAmt,String couponNo){
        if(orderAmt.compareTo(couponAmt)<0){
            LemonException.throwBusinessException("CSH10071");
        }

        BigDecimal payAmt=orderAmt.subtract(couponAmt);
        BigDecimal useCouponAmt=couponAmt;
        return new QuotaDTO(useCouponAmt,payAmt,couponNo,CouponType.D_COUPON.getType());
    }


    /**
     * 账单同步数据
     * @param orderDO  订单对象
     * @param flag     标志：1 创建订单  2 更新订单
     * @param payerIdExist   原订单付款方是否存在
     * @param extInfo 拓展字段用"|"分隔，转账时，前半部分传付款方手机后四位，后半部分传收款方手机后四位或者银行卡后四位
     *  @param extMap   账单拓展信息
     */
    public void synchronizeBil(OrderDO orderDO,int flag,boolean payerIdExist, String extInfo,Map<String,Map<Object,Object>> extMap){
        GenericDTO genericDTO = new GenericDTO<>();
        String[] extInfoStr =null;
        if(JudgeUtils.isNotBlank(extInfo)) {
            extInfoStr = extInfo.split("\\|");
        }
        switch(flag){
            case CREATE_BIL:
                CreateUserBillDTO createUserBillDTO = new CreateUserBillDTO();
                BeanUtils.copyProperties(createUserBillDTO, orderDO);

                //初始化国际化的订单信息
                createUserBillDTO = initViewOrderInfo(orderDO, createUserBillDTO, extInfoStr);

                //国际化前端显示的支付方式
                //理财转入与充海币只能用现金账户支付
                String payMod="";
                payMod = getViewPayMod(orderDO, true);

                createUserBillDTO.setPayMod(payMod);
                if(JudgeUtils.equals(createUserBillDTO.getBusType(),PwmConstants.BUS_TYPE_RECHARGE_OFL)){
                    if(JudgeUtils.isNotNull(extMap) && !extMap.isEmpty()) {
                        Map map = extMap.get(PwmConstants.BUS_TYPE_RECHARGE_OFL);
                        if(JudgeUtils.isNotNull(map) && !map.isEmpty()) {
                            String remitInfo = map.get(OfflineBilExtConstants.CRD_CORP_ORG) + "|"
                                    + map.get(OfflineBilExtConstants.PAYEE_COMPANY) + "|" + map.get(OfflineBilExtConstants.BANK_ACCOUNT_NO);
                            createUserBillDTO.setRemitInfo(remitInfo);
                        }
                    }
                }

                //设置操作员
                if(JudgeUtils.equals(createUserBillDTO.getTxType(), TradeType.CONSUME.getType())){
                    if(JudgeUtils.isNotNull(extMap) && !extMap.isEmpty()) {
                        Map map = extMap.get(TradeType.CONSUME.getType());
                        if (JudgeUtils.isNotNull(map) && !map.isEmpty()) {
                            String loginId = (String) map.get("loginId");
                            if (JudgeUtils.isNotNull(loginId)) {
                                UserBasicInfDTO userBasicInfDTO = userBasicInfClient.queryUserByLoginId(loginId).getBody();
                                if (JudgeUtils.isNotNull(userBasicInfDTO)) {
                                    createUserBillDTO.setOprName(userBasicInfDTO.getDisplayNm());
                                }
                            }
                        }
                    }
                }

                createUserBillDTO.setTxTm(orderDO.getOrderTm());
                //海币类型优惠处理成金额
                String ccouponType = orderDO.getCouponType();
                if(JudgeUtils.isNotNull(ccouponType) && JudgeUtils.equals(ccouponType,CouponType.H_COUPON.getType())){
                    BigDecimal couponAmt = orderDO.getCouponAmt();
                    if(JudgeUtils.isNotNull(couponAmt)){
                        createUserBillDTO.setCouponAmt(couponAmt.divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
                    }
                }

                genericDTO.setBody(createUserBillDTO);
                billSyncHandler.createBill(createUserBillDTO);
                break;

            case UPD_BIL:
                UpdateUserBillDTO updateUserBillDTO = new UpdateUserBillDTO();
                BeanUtils.copyProperties(updateUserBillDTO, orderDO);
                updateUserBillDTO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
                //海币类型优惠处理成金额
                String couponType = orderDO.getCouponType();
                if(JudgeUtils.equals(couponType,CouponType.H_COUPON.getType())){
                    BigDecimal couponAmt = orderDO.getCouponAmt();
                    if(JudgeUtils.isNotNull(couponAmt)){
                        updateUserBillDTO.setCouponAmt(couponAmt.divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
                    }
                }

                //更新收款方订单信息
                String extInfo2 = "";
                if(JudgeUtils.isNotNull(orderDO.getBusType())) {
                    if (orderDO.getBusType().startsWith("02") || orderDO.getBusType().startsWith("03")) {
                        if(JudgeUtils.isNotEmpty(extInfoStr)) {
                            //截取付款方手机号后四位
                        	Object[] args = null;
                        	if(extInfoStr[0].length()>=4){
                                extInfoStr[0] = extInfoStr[0].substring(extInfoStr[0].length()-4);
                        		args = new Object[]{extInfoStr[0], orderDO.getOrderAmt()};
                        	}else{
                        		args = new Object[]{"", orderDO.getOrderAmt()};
                        	} 
                            extInfo2 = getViewOrderInfo(orderDO.getBusType(), args, true);
                            updateUserBillDTO.setExtInfo(extInfo2);
                        }
                    }

                }
                //汇款充值摘要信息设置
                if(JudgeUtils.equals(orderDO.getBusType(), BussinessType.RECHARGE_OFFLINE.getValue()) &&
                        JudgeUtils.equals(orderDO.getOrderStatus(), OrderStatus.WAIT_AUDIT.getValue())){
                    if(JudgeUtils.isNotEmpty(extInfoStr)) {
                        updateUserBillDTO.setExtInfo(extInfoStr[0]);
                    }

                }

                String payMod2="";
                //国际化支付方式
                payMod2 = getViewPayMod(orderDO, false);
                updateUserBillDTO.setPayMod(payMod2);

                //原订单有付款方，直接更新账单
                if(payerIdExist){
                    billSyncHandler.updateBill(updateUserBillDTO);
                }else {
                    //原订单无付款方，判断当前用户id是否存在
                    String payerId = LemonUtils.getUserId();
                    //当前用户id不存在，直接更新账单
                    if(JudgeUtils.isBlank(payerId)){
                        //无界面支付更新收款方订单信息
                        if(orderDO.getBusType().startsWith("02")){
                            Object[] args = new Object[]{"", orderDO.getOrderAmt()};
                            String extInfo3 = getViewOrderInfo(orderDO.getBusType(), args, true);
                            updateUserBillDTO.setExtInfo(extInfo3);
                        }
                        billSyncHandler.updateBill(updateUserBillDTO);
                    }else{
                        //设置订单国际化信息
                        String goodsInfo = "";
                        if(JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())){
                            Object[] args = null;
                            if(JudgeUtils.isNotBlank(orderDO.getMercName())) {
                                args = new Object[]{orderDO.getMercName(), orderDO.getOrderAmt()};
                            }else{
                                args = new Object[]{"", orderDO.getOrderAmt()};
                            }
                            goodsInfo=getViewOrderInfo(orderDO.getBusType(),args, false);
                        }
                        if(JudgeUtils.isBlank(goodsInfo)) {
                            if (JudgeUtils.isNotBlank(orderDO.getGoodsInfo())) {
                                goodsInfo = orderDO.getGoodsInfo();
                            }
                        }

                        //设置付款方,防止商户主扫用户微信、翼支付业务类型0201情况下，付款方设置成商户id
                        if(!(JudgeUtils.equals(orderDO.getPayeeId(),payerId) && JudgeUtils.equals(orderDO.getBusType(),BussinessType.CONSUME_BAR.getValue()))){
                            //当前用户存在，创建用户账单
                            CreateUserBillDTO createUserBillDTO2 = new CreateUserBillDTO();
                            BeanUtils.copyProperties(createUserBillDTO2, orderDO);
                            createUserBillDTO2.setTxTm(orderDO.getOrderTm());
                            /*
                            * 用户主扫0202业务类型情况下，包括无账户(三方平台聚合微信，翼支付支付)和
                            * 商户下单生成收款码，平台用户扫码支付两种情况。
                            * */
                            //设置当前登录用户id，补充用户账单
                            createUserBillDTO2.setPayerId(payerId);

                            createUserBillDTO2.setGoodsInfo(goodsInfo);
                            createUserBillDTO2.setPayMod(payMod2);

                            genericDTO.setBody(createUserBillDTO2);
                            billSyncHandler.createBill(createUserBillDTO2);
                        }

                        BeanUtils.copyProperties(updateUserBillDTO, orderDO);
                        updateUserBillDTO.setGoodsInfo(goodsInfo);

                        couponType = orderDO.getCouponType();
                        if(JudgeUtils.equals(couponType,CouponType.H_COUPON.getType())){
                            BigDecimal couponAmt = orderDO.getCouponAmt();
                            if(JudgeUtils.isNotNull(couponAmt)){
                                updateUserBillDTO.setCouponAmt(couponAmt.divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
                            }
                        }

                        if(JudgeUtils.isNotBlank(payMod2)) {
                            updateUserBillDTO.setPayMod(payMod2);
                        }

                        if(JudgeUtils.isNotBlank(extInfo2)) {
                            updateUserBillDTO.setExtInfo(extInfo2);
                        }
                        billSyncHandler.updateBill(updateUserBillDTO);
                    }
                }

                break;
            default:
                break;
        }
    }

    public String getPayType(String appCnl,String busType,String merchantId,String busPayType){
        PaytypeDO paytypeFind=new PaytypeDO();
        paytypeFind.setBusType(busType);
        paytypeFind.setMercId(merchantId);
        paytypeFind.setAppCnl(appCnl);
        return this.paytypeService.getPaytypes(paytypeFind,busPayType);
    }

    /**
     * 创建收银台返回DTO
     */
    public CashierViewDTO createCashierViewDTO(OrderDO orderDO){
        String payTypes=orderDO.getPayType();
        String userId= LemonUtils.getUserId();

        if(JudgeUtils.isNull(userId) || JudgeUtils.isBlank(userId)) {
            userId = orderDO.getPayerId();
        }

        if(JudgeUtils.isNull(userId) || JudgeUtils.isBlank(userId)) {
            userId = orderDO.getPayeeId();
        }

        CashierViewDTO cashierViewDTO=new CashierViewDTO();
        cashierViewDTO.setRemark(orderDO.getRemark());
        //支持账户余额
        if(paytypeService.supportBal(payTypes)){
            BigDecimal bal=acmComponent.getAccountBal(userId, CapTypEnum.CAP_TYP_CASH.getCapTyp());
            cashierViewDTO.setBalAmt(bal.setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        cashierViewDTO.sethCouponCashAmt(BigDecimal.valueOf(0));
        cashierViewDTO.sethCouponUsedAmt(BigDecimal.valueOf(0));

        Map coupons = null;
        //理财和转账不查询优惠券
        if(JudgeUtils.equalsAny(orderDO.getTxType(),TradeType.FINANC.getType(),TradeType.TRANSFER.getType())){
            coupons = new HashMap<>();
        }else{
            coupons=acmComponent.queryCoupons(userId, orderDO.getPayeeId(),orderDO.getOrderNo(),orderDO.getTxType(),orderDO.getOrderAmt());
        }
        //支持海币
        if(paytypeService.supportBit(payTypes,3)){
            Integer hCoupon=0;
            if(JudgeUtils.isNotEmpty(coupons)){
                hCoupon=acmComponent.getHCoupon(coupons);
                BigDecimal hc=BigDecimal.valueOf(hCoupon).setScale(2, BigDecimal.ROUND_HALF_UP);
                cashierViewDTO.sethCouponAmt(hc);

                QuotaDTO quotaDTO=makeQuotaHCoupons(orderDO.getOrderAmt(),hCoupon);
                BigDecimal hCouponCash=quotaDTO.getCouponAmt().setScale(2, BigDecimal.ROUND_HALF_UP);
                //数量
                cashierViewDTO.sethCouponUsedAmt(hCouponCash);
                //金额
                cashierViewDTO.sethCouponCashAmt(hCouponCash.multiply(BigDecimal.valueOf(CshConstants.H_USD_RATE))
                        .setScale(2, BigDecimal.ROUND_HALF_DOWN));
            }
        }

        //电子券
        if(JudgeUtils.isNotEmpty(coupons)){
            //过滤可用优惠电子券(电子券状态01，优惠面额不大于订单金额)
            List<CouponItem> usableList = acmComponent.getECoupon(coupons).stream()
                                                                          .filter(ec -> (ec != null) && (ec.getAmt().compareTo(orderDO.getTotalAmt()) < 0))
                                                                          .collect(Collectors.toList());
            cashierViewDTO.seteCoupons(usableList);
        }else{
            cashierViewDTO.seteCoupons(Collections.emptyList());
        }

        //折扣券
        if(JudgeUtils.isNotEmpty(coupons)){
            cashierViewDTO.setDiscountCoupons(acmComponent.getDiscountCoupon(coupons));
        }else{
            cashierViewDTO.setDiscountCoupons(Collections.emptyList());
        }

        //优惠券
        if(JudgeUtils.isNotEmpty(coupons)){
            cashierViewDTO.setcCoupons(acmComponent.getCCoupon(coupons));
        }else{
            cashierViewDTO.setcCoupons(Collections.emptyList());
        }

        cashierViewDTO.setOrderNo(orderDO.getOrderNo());
        if(paytypeService.supportDQP(payTypes) || paytypeService.supportCQP(payTypes)){
            Map<String,List<AgrInfoRspDTO.CardAgrInfo>> qpCards=quickPaymentComponent.queryBindCardsConfirmUserId(orderDO.getPayerId());
            //支持借记卡快捷支付
            if(paytypeService.supportDQP(payTypes)){
                List<AgrInfoRspDTO.CardAgrInfo> adQpCards= qpCards.get(quickPaymentComponent.D_CARD);
                List<QpCardInfoDTO> dQpCards = new ArrayList<>();
                for(AgrInfoRspDTO.CardAgrInfo cardInfo : adQpCards) {
                    QpCardInfoDTO qpCardInfoDTO = new QpCardInfoDTO();
                    BeanUtils.copyProperties(qpCardInfoDTO,cardInfo);
                    dQpCards.add(qpCardInfoDTO);
                }
                cashierViewDTO.setQpDCards(dQpCards);
            }
            //支持贷记卡快捷支付
            if(paytypeService.supportCQP(payTypes)){
                List<AgrInfoRspDTO.CardAgrInfo> acQpCards= qpCards.get(quickPaymentComponent.C_CARD);
                List<QpCardInfoDTO> cQpCards = new ArrayList<>();
                for(AgrInfoRspDTO.CardAgrInfo cardInfo : acQpCards) {
                    QpCardInfoDTO qpCardInfoDTO = new QpCardInfoDTO();
                    BeanUtils.copyProperties(qpCardInfoDTO,cardInfo);
                    cQpCards.add(qpCardInfoDTO);
                }
                cashierViewDTO.setQpCCards(cQpCards);
            }
        }

        QuotaDTO quota=this.makeQuota(orderDO.getTotalAmt(), coupons,StringUtils.equals(payTypes.substring(3,4),CshConstants.YES));

        cashierViewDTO.setQuota(quota);
        cashierViewDTO.setPayTypes(payTypes);
        cashierViewDTO.setOrderAmt(orderDO.getOrderAmt().setScale(2, BigDecimal.ROUND_HALF_UP));

        //如果手续费外扣，手续费要展示并额外收取
        if(StringUtils.equals("EX",orderDO.getFeeFlag())){
            cashierViewDTO.setFeeAmt(orderDO.getFee().setScale(2, BigDecimal.ROUND_HALF_UP));
        }else{
            cashierViewDTO.setFeeAmt(BigDecimal.ZERO);
        }
        cashierViewDTO.setGoodsDesc(orderDO.getGoodsInfo());
        cashierViewDTO.setBusType(orderDO.getBusType());
        cashierViewDTO.setFeeFlag(orderDO.getFeeFlag());
        return cashierViewDTO;
    }

    public BigDecimal computeCoupon(BigDecimal couponAmt,String couponType){
        if(StringUtils.equals(CouponType.NONE.getType(),couponType)){
            return BigDecimal.valueOf(0);
        }
        if(StringUtils.equals(CouponType.H_COUPON.getType(),couponType)){
            return couponAmt.multiply(BigDecimal.valueOf(CshConstants.H_USD_RATE));
        }
        return couponAmt;
    }


    public void checkPayType(OrderDO orderDO,String payType){

        if(!paytypeService.supportBal(payType)){
            if(orderDO.getBalAmt().compareTo(BigDecimal.valueOf(0))>0){
                LemonException.throwBusinessException("CSH20073");
            }
        }

        //海币
        if(paytypeService.supportBit(payType,3) && StringUtils.equals(orderDO.getCouponType(),CouponType.H_COUPON.getType())){
            LemonException.throwBusinessException("CSH20073");
        }

        //快捷
        if(!(paytypeService.supportCQP(payType)|| paytypeService.supportDQP(payType))
                && StringUtils.equals(orderDO.getCrdPayType(),CshConstants.CRD_TYPE_QP)){
            LemonException.throwBusinessException("CSH20073");
        }
    }

    //处理订单过期
    public void handleOrderExp(OrderDO orderDo){
        String orginSatatus=orderDo.getOrderStatus();
        OrderDO updateOrder=new OrderDO();
        updateOrder.setOrderNo(orderDo.getOrderNo());
        updateOrder.setOrderStatus(OrderStatus.EXPIRE.getValue());
        orderTransactionalService.updateOrder(updateOrder);
        //判断原订单中付款方是否存在
        boolean payerIdExist = true;
        if(JudgeUtils.isBlank(orderDo.getPayerId())){
            payerIdExist = false;
        };
        //同步账单
        orderDo.setOrderStatus(OrderStatus.EXPIRE.getValue());
        synchronizeBil(orderDo,UPD_BIL,payerIdExist, null,null);

        //核销折扣券
        if(StringUtils.equals(orderDo.getCouponType(),CouponType.D_COUPON.getType())
                && JudgeUtils.isNotBlank(orderDo.getCouponNo())){
            if(JudgeUtils.equalsAny(orginSatatus, OrderStatus.WAIT_PAY.getValue(), OrderStatus.PRE_PAY.getValue())){
                paymentHandler.cancelMkmCouponGive(orderDo.getOrderNo(), orderDo.getCouponType());
            }

        }
    }


    public List queryExpOrderList(int limitCount){
        Map queryMap=new HashMap<>();
/*        if(limitCount>0){
            queryMap.put("count",limitCount);
        }*/
        //设置扫描订单过期时间超过24小时
        queryMap.put("nowTm",DateTimeUtils.getCurrentLocalDateTime().minusDays(1));
        return orderTransactionalService.queryExpList(queryMap);
    }

    /**
     * 国际化商品描述信息
     * @param busType
     * @param args
     * @return
     */
    public String getViewOrderInfo(String busType,Object[] args, boolean isPayee){
        try{
            String key="view.orderinfo."+busType;
            //转账到银行卡
            if(JudgeUtils.equals(TamConstants.BUS_TYPE_TRANSFER_B, busType)) {
                if(JudgeUtils.isNotBlank(String.valueOf(args[2]))) {
                    key = key + args[2];
                }
            }
            //收款方订单信息
            if(isPayee){
                key="view.orderinfo.payee";
            }
            //格式化订单金额
            if(busType.startsWith("01")){
                if(JudgeUtils.isNotNull(args[0])){
                    //toPlainString 不使用任何指数
                    args[0] = new BigDecimal(String.valueOf(args[0])).setScale(2).toPlainString();
                }
            }else{
                if(JudgeUtils.isNotNull(args[1])){
                    args[1] = new BigDecimal(String.valueOf(args[1])).setScale(2).toPlainString();
                }
            }
            return localeMessageSource.getMessage(key,args);
        }catch (Exception e){

        }
        return  null;
    }

    /**
     * 国际化页面支付方式
     * @param crdPaytype
     * @param capCorg
     * @param last4CardNo
     * @return
     */
    public String getViewPaytype(Integer crdPaytype,String capCorg,String last4CardNo){
        String message = "";
        String key="view.paytype";
        if(crdPaytype.intValue()==2){
            if(JudgeUtils.isNotBlank(capCorg)) {
                key += crdPaytype.intValue() + "." + capCorg;
            }
        }else{
            key+=crdPaytype;
        }
        Object[] args = new Object[]{""};
        if(JudgeUtils.isNotBlank(last4CardNo)) {
            args = new Object[]{last4CardNo};
        }
        try {
            message = localeMessageSource.getMessage(key, args);
        }catch(NoSuchMessageException e){

        }
        return  message;

    }

    /**
     * 获取支付方式
     */
    public String getViewPayMod(OrderDO orderDO, Boolean isCreate){
        String payMod ="";
        //如果是下单，则初始化支付方式
        if(isCreate) {
            if(JudgeUtils.isNotBlank(orderDO.getCrdPayType())) {
                if (JudgeUtils.equals(orderDO.getCrdPayType(), CshConstants.CRD_TYPE_NONE)) {
                    payMod = getViewPaytype(0, null, null);
                }
                if (JudgeUtils.equals(orderDO.getCrdPayType(), CshConstants.CRD_TYPE_NB)) {
                    if(JudgeUtils.isNotEmpty(orderDO.getCapCorgNo())) {
                        payMod = orderDO.getCapCorgNo();
                    }
                }
                if (JudgeUtils.equals(orderDO.getCrdPayType(), CshConstants.CRD_TYPE_QP)) {
                    if(JudgeUtils.isNotBlank(orderDO.getCapCorg())) {
                        payMod = getViewPaytype(2, orderDO.getCapCorg(), orderDO.getLast4CardNo());
                    }
                }
            }
            //理财支付方式只有现金账户
            if (JudgeUtils.equals(orderDO.getTxType(), BilConstants.TX_TYPE_RECHANGESEACOIN) ||
                    JudgeUtils.equals(orderDO.getTxType(), BilConstants.TX_TYPE_INV)) {
                payMod = getViewPaytype(0, null, null);
            }
            //汇款充值下单时，设支付方式
            if (JudgeUtils.equals(orderDO.getBusType(), PwmConstants.BUS_TYPE_RECHARGE_OFL)) {
                payMod = getViewPaytype(3, null, null);
            }
        }else {
            //如果是更新，原订单没有支付方式，再初始化
            if (JudgeUtils.isEmpty(orderDO.getPayMod())) {
                if (JudgeUtils.equals(orderDO.getCrdPayType(), CshConstants.CRD_TYPE_NONE)) {
                    payMod = getViewPaytype(0, null, null);
                }
                if (JudgeUtils.equals(orderDO.getCrdPayType(), CshConstants.CRD_TYPE_NB)) {
                    if(JudgeUtils.isNotEmpty(orderDO.getCapCorgNo())) {
                        payMod = orderDO.getCapCorgNo();
                    }
                }
                if (JudgeUtils.equals(orderDO.getCrdPayType(), CshConstants.CRD_TYPE_QP)) {
                    if(JudgeUtils.isNotBlank(orderDO.getCapCorg())) {
                        payMod = getViewPaytype(2, orderDO.getCapCorg(), orderDO.getLast4CardNo());
                    }
                }
                if (JudgeUtils.equals(orderDO.getCrdPayType(), CshConstants.CRD_TYPE_FL)) {
                    payMod = getViewPaytype(3, null, null);
                }
            } else {
                return orderDO.getPayMod();
            }
        }
        return payMod;
    }

    /**
     * 初始化订单信息
     */
    public CreateUserBillDTO initViewOrderInfo(OrderDO orderDO, CreateUserBillDTO createUserBillDTO, String[] extInfoStr){
        //国际化订单信息
        Object[] args=null;
        if(StringUtils.isBlank(orderDO.getGoodsInfo())){
            //充值
            if(orderDO.getBusType().startsWith(PwmConstants.TX_TYPE_RECHANGE)){
                args=new Object[]{orderDO.getOrderAmt()};
            }
            //充海币
            if(orderDO.getBusType().startsWith(PwmConstants.TX_TYPE_HCOUPON)){
                args=new Object[]{orderDO.getOrderAmt().divide(BigDecimal.valueOf(CshConstants.H_USD_RATE))};
            }
            //理财转入
            if(orderDO.getBusType().startsWith("07")){
                args=new Object[]{"",orderDO.getOrderAmt()};
            }
            //缴费
            if(orderDO.getBusType().startsWith("08")){
            	if(JudgeUtils.isNotBlank(orderDO.getPayerId()) && orderDO.getPayerId().length()>4){
            		args=new Object[]{orderDO.getPayerId().substring(orderDO.getPayerId().length() - 4),orderDO.getOrderAmt()};
            	}else{
            		args=new Object[]{"",orderDO.getOrderAmt()};
            	} 
            }
            //初始化付款方订单信息
            String goodsInfo=getViewOrderInfo(orderDO.getBusType(),args, false);
            createUserBillDTO.setGoodsInfo(goodsInfo);
        }else{
            createUserBillDTO.setGoodsInfo(orderDO.getGoodsInfo());
        }

        //转账
        if(orderDO.getBusType().startsWith(TamConstants.TX_TYPE_TRANSFER)){
            String goodsInfo = "";
            if(JudgeUtils.isNotEmpty(extInfoStr) && extInfoStr.length>1 && JudgeUtils.isNotEmpty(extInfoStr[1])) {
                //转账到银行卡
                if (JudgeUtils.equals(TamConstants.BUS_TYPE_TRANSFER_B, orderDO.getBusType())) {
                    if(JudgeUtils.isNotBlank(orderDO.getCapCorg())) {
                        args = new Object[]{extInfoStr[1], orderDO.getOrderAmt(), orderDO.getCapCorg()};
                        goodsInfo=getViewOrderInfo(orderDO.getBusType(), args, false);
                    }
                }
                if(JudgeUtils.isBlank(goodsInfo)){
                    args = new Object[]{extInfoStr[1], orderDO.getOrderAmt()};
                    goodsInfo=getViewOrderInfo(orderDO.getBusType(), args, false);
                }
                createUserBillDTO.setGoodsInfo(goodsInfo);
            }else{
                createUserBillDTO.setGoodsInfo(orderDO.getGoodsInfo());
            }
        }

        //消费
        if(orderDO.getBusType().startsWith("02")){
            String goodsInfo = "";
            if(JudgeUtils.isNotBlank(orderDO.getMercName())) {
                args = new Object[]{orderDO.getMercName(), orderDO.getOrderAmt()};
            }else{
                args = new Object[]{"", orderDO.getOrderAmt()};
            }
            goodsInfo=getViewOrderInfo(orderDO.getBusType(), args, false);
            if(JudgeUtils.isBlank(goodsInfo)) {
                if (JudgeUtils.isNotBlank(orderDO.getGoodsInfo())) {
                    goodsInfo = orderDO.getGoodsInfo();
                }
            }
            createUserBillDTO.setGoodsInfo(goodsInfo);
        }

        //初始化收款方订单信息
        if(orderDO.getBusType().startsWith("03") || orderDO.getBusType().startsWith("02")){
            if(JudgeUtils.isNotBlank(orderDO.getPayerId())) {
                if(JudgeUtils.isNotEmpty(extInfoStr)) {
                    //截取付款方手机后四位
                	if(extInfoStr[0].length()>=4){
                		extInfoStr[0] = extInfoStr[0].substring(extInfoStr[0].length()-4);
                		args = new Object[]{extInfoStr[0], orderDO.getOrderAmt()};
                	}else{
                		args = new Object[]{"", orderDO.getOrderAmt()};
                	} 
                }else{
                    args = new Object[]{"", orderDO.getOrderAmt()};
                }
                String extInfo2 = getViewOrderInfo(orderDO.getBusType(), args, true);
                createUserBillDTO.setExtInfo(extInfo2);
            }
        }

        return createUserBillDTO;
    }
    
    /**
	 * 交易手续费查询
	 */
	public TradeFeeRspDTO tradeFee(TradeFeeReqDTO tradeFeeReqDTO) {
		GenericDTO<TradeFeeCaculateReqDTO> reqDTO=new GenericDTO();
		TradeFeeCaculateReqDTO tradeFeeCaculateReqDTO=new TradeFeeCaculateReqDTO();
		tradeFeeCaculateReqDTO.setBusType(tradeFeeReqDTO.getBusType());
		tradeFeeCaculateReqDTO.setCcy(tradeFeeReqDTO.getCcy());
		tradeFeeCaculateReqDTO.setTradeAmt(tradeFeeReqDTO.getTradeAmt());
		reqDTO.setBody(tradeFeeCaculateReqDTO);
		GenericRspDTO<TradeFeeCaculateRspDTO> genericRspDTO=tfmServerClient.tradeFeeCaculate(reqDTO);
		if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
			LemonException.throwBusinessException(genericRspDTO.getMsgCd());
		}
		TradeFeeCaculateRspDTO tradeFeeCaculateRspDTO=genericRspDTO.getBody();
		TradeFeeRspDTO tradeFeeRspDTO=new TradeFeeRspDTO();
		tradeFeeRspDTO.setCalculateMode(tradeFeeCaculateRspDTO.getCalculateMode());
		tradeFeeRspDTO.setCalculateType(tradeFeeCaculateRspDTO.getCalculateType());
		tradeFeeRspDTO.setFixFee(tradeFeeCaculateRspDTO.getFixFee());
		tradeFeeRspDTO.setRate(tradeFeeCaculateRspDTO.getRate());
		tradeFeeRspDTO.setTradeAmt(tradeFeeCaculateRspDTO.getTradeAmt());
		tradeFeeRspDTO.setTradeFee(tradeFeeCaculateRspDTO.getTradeFee());
		tradeFeeRspDTO.setTradeTotalAmt(tradeFeeCaculateRspDTO.getTradeToalAmt());
		return tradeFeeRspDTO;
	}

    /**
     * 关闭订单处理
     * @param genericDTO
     */
    public void handleOrderClose(GenericDTO<TradeOffDTO> genericDTO){
        OrderDO orderDo = this.orderTransactionalService.queryOrderNotNone(genericDTO.getBody().getOrderNo());
        String orginSatatus=orderDo.getOrderStatus();

        //订单状态校验
        if(!JudgeUtils.equalsAny(orginSatatus, OrderStatus.WAIT_PAY.getValue(), OrderStatus.PRE_PAY.getValue(), OrderStatus.FAIL.getValue())){
            LemonException.throwBusinessException("CSH20107");
        }
        OrderDO updateOrder=new OrderDO();
        BeanUtils.copyProperties(updateOrder,orderDo);

        updateOrder.setOrderStatus(OrderStatus.OFF.getValue());
        updateOrder.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        orderTransactionalService.updateOrder(updateOrder);

        //判断原订单中付款方是否存在
        boolean payerIdExist = true;
        if(JudgeUtils.isBlank(orderDo.getPayerId())){
            payerIdExist = false;
        };

        //同步账单
        synchronizeBil(updateOrder,UPD_BIL,payerIdExist, null,null);

        //核销折扣券
        if(StringUtils.equals(orderDo.getCouponType(),CouponType.D_COUPON.getType())
                && JudgeUtils.isNotBlank(orderDo.getCouponNo())){
            if(JudgeUtils.equalsAny(orginSatatus, OrderStatus.WAIT_PAY.getValue(), OrderStatus.PRE_PAY.getValue())){
                paymentHandler.cancelMkmCouponGive(orderDo.getOrderNo(), orderDo.getCouponType());
            }

        }
    }

    /**
     * 商户手续费预算
     * @param orderDO
     * @return
     */
    public MerchantFeeCalculateRspDTO caculateMerchantTradeFee(OrderDO orderDO){
        GenericDTO<MerchantFeeCalculateReqDTO> tradeFeeReq = new GenericDTO<MerchantFeeCalculateReqDTO>();
        MerchantFeeCalculateReqDTO tradeFeeReqDTO = new MerchantFeeCalculateReqDTO();
        tradeFeeReqDTO.setBusType(orderDO.getBusType());
        tradeFeeReqDTO.setCcy(orderDO.getCcy());
        tradeFeeReqDTO.setTradeAmt(orderDO.getOrderAmt());
        //付款方为商户
        tradeFeeReqDTO.setUserId(orderDO.getPayerId());
        //业务类型
        tradeFeeReqDTO.setBusType(orderDO.getBusType());
        tradeFeeReqDTO.setCcy(orderDO.getCcy());
        tradeFeeReqDTO.setTradeAmt(orderDO.getOrderAmt());
        tradeFeeReq.setBody(tradeFeeReqDTO);
        // 调用tfm接口查询商户手续费
        GenericRspDTO<MerchantFeeCalculateRspDTO> tradeGenericRspDTO = tfmServerClient.merchanFeeCalculate(tradeFeeReq);
        if (JudgeUtils.isNotSuccess(tradeGenericRspDTO.getMsgCd())) {
            throw new LemonException(tradeGenericRspDTO.getMsgCd());
        }
        return tradeGenericRspDTO.getBody();
    }

}



