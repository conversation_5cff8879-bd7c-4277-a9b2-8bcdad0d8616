/*
 * @ClassName RefundOrderDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-15 12:12:57
 */
package com.hisun.lemon.csh.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.csh.entity.RefundOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface IRefundOrderDao extends BaseDao<RefundOrderDO> {

    public List<RefundOrderDO> queryList(Map qryMap);

    public List<RefundOrderDO> queryCpiOrder(Map qryMap);
}