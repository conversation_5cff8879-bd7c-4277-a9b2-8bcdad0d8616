package com.hisun.lemon.csh.service;

import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.DirectPaymentDTO;
import com.hisun.lemon.csh.dto.payment.PaymentResultDTO;
import com.hisun.lemon.framework.data.GenericDTO;

/**
 * 数币相关订单服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/24 20:00
 */
public interface IDmOrderService {


    /**
     * 数币转账账务处理
     *
     * @param req
     */
    void handleFinance(GenericDTO<HandleFinanceDTO> req);

    /**
     * 数币转账失败处理
     *
     * @param req
     */
    void handleFailTransfer(GenericDTO<HandleFailTranDTO> req);

    PaymentResultDTO directDm(GenericDTO<DirectPaymentDTO> directGenDTO);

    /**
     * 数币提现余额支付
     * @param directGenDTO
     * @return
     */
    PaymentResultDTO directDmWithdraw(GenericDTO<DirectPaymentDTO> directGenDTO);

    /**
     * 处理提现失败
     * @param handleFinanceDTOGenericDTO
     */
    void handleFailWithdraw(GenericDTO<HandleFailTranDTO> handleFinanceDTOGenericDTO);

}
