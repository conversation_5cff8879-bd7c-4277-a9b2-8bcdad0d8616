package com.hisun.lemon.csh.service.chk;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.hisun.lemon.csh.enums.OrderStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dao.IOrderDao;
import com.hisun.lemon.csh.entity.OrderDO;

@Transactional
@Service
public class CpiChkFileServiceImpl extends AbstractChkFileService {
    @Resource
    private IOrderDao orderDao;

    public CpiChkFileServiceImpl() {
        super();
        this.appCnl="CPI";
        this.chkOrderStatus=new String[]{
                OrderStatus.SUCC.getValue(),
                OrderStatus.REFUND.getValue(),
                OrderStatus.PART_REFUND.getValue()
        };
        this.lockName="CSH_CPI_CHK_FILE_LOCK";
    }

    @Transactional(readOnly = true)
    protected void execute() {
        //获取对账日期
        LocalDate chkDate=chkFileComponent.getChkDate();
        //对账文件名 CSH_CPI_XXXXX.ck  CSH_CPI_RFD_XXXXX.ck
        String  chkFileName=chkFileComponent.getChkFileName(appCnl,chkDate,refund);
        //标志文件名
        String flagName=chkFileName+".flag";
        if(chkFileComponent.isStart(appCnl,flagName)){
            logger.info("对账文件标志文件" +flagName+"已经存在,不重复生成对账文件");
            return;
        }
        logger.info("开始生成对账文件：" +flagName);
        //生成标志文件
        chkFileComponent.createFlagFile(appCnl,flagName);
        Map queryMap=new HashMap();
        queryMap.put("acTm",chkDate);
        queryMap.put("statusList",chkOrderStatus);
        //读取数据
        List<OrderDO> orders=orderDao.queryCpiOrder(queryMap);


        //生成文件
        chkFileComponent.writeToFile(appCnl,orders,chkFileName);
        logger.info("生成对账文件"+flagName+"完成，开始上传至SFTP");

        //上传服务器
        chkFileComponent.upload(appCnl, chkFileName, flagName);
        logger.info("对账文件"+flagName+"上传至SFTP完成");
    }
}
