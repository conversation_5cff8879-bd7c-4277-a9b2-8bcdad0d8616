package com.hisun.lemon.csh.controller;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.*;
import com.hisun.lemon.csh.dto.order.OrderDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.service.IAuditOrderService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Api(value="收银台审核订单处理")
@RestController
@RequestMapping(value="/csh/auditOrder")
public class AuditOrderController extends BaseController {

	@Resource
	IAuditOrderService auditOrderService;
    
	@ApiOperation(value="获取收银台转账订单信息列表", notes="获取收银台转账订单信息列表")
	@ApiResponse(code = 200, message = "转账订单信息列表")
    @PostMapping(value = "/queryCashierTranOrders")
    public GenericRspDTO<List<OrderDTO>> queryCashierTranOrders(@Validated @RequestBody GenericDTO<TranOrderReqDTO> tranAuditOrderDTO) {
		TranOrderReqDTO initDTO=tranAuditOrderDTO.getBody();
		List<OrderDO> orders = auditOrderService.queryCashierTranOrders(initDTO);
		OrderDTO orderDTO=new OrderDTO();
		List<OrderDTO> dtoList = orders.stream().map(orderDO -> {
			BeanUtils.copyProperties(orderDTO, orderDO);
			return orderDTO;
		}).collect(Collectors.toList());
		return GenericRspDTO.newSuccessInstance(dtoList);
    }

	@ApiOperation(value="转账订单审核", notes="转账订单审核")
	@ApiResponse(code = 200, message = "转账订单审核")
    @PostMapping(value = "/audit")
    public GenericRspDTO<NoBody> audit(@Validated @RequestBody GenericDTO<TranOrderAuditDTO> tranAuditOrderDTO) {
		TranOrderAuditDTO req = tranAuditOrderDTO.getBody();
		auditOrderService.audit(req);
		return GenericRspDTO.newSuccessInstance();
    }

	@ApiOperation(value="数币链上转账成功", notes="数币链上转账成功")
	@ApiResponse(code = 200, message = "数币链上转账成功")
    @PostMapping(value = "/dm/transferOk")
    public GenericRspDTO<NoBody> handleCregisOk(@Validated @RequestBody GenericDTO<HandleFinanceDTO> req) {
		auditOrderService.handleCregisOk(req);
		return GenericRspDTO.newSuccessInstance();
    }

	@ApiOperation(value="数币提现成功", notes="数币提现成功")
	@ApiResponse(code = 200, message = "数币提现成功")
	@PostMapping(value = "/dm/withdrawOk")
	public GenericRspDTO<NoBody> handleWithdrawOk(@Validated @RequestBody GenericDTO<HandleFinanceDTO> req) {
		auditOrderService.handleWithdrawOk(req);
		return GenericRspDTO.newSuccessInstance();
	}



}
