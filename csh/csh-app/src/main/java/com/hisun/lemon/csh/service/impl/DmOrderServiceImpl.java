package com.hisun.lemon.csh.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.DmAccountDetailRspDTO;
import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.CommonEncryptReqDTO;
import com.hisun.lemon.cmm.dto.CommonEncryptRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.component.AcmComponent;
import com.hisun.lemon.csh.component.OrderCommonComponent;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.ctx.OrderContextHolder;
import com.hisun.lemon.csh.dao.IOrderDao;
import com.hisun.lemon.csh.dao.IPayJrnDao;
import com.hisun.lemon.csh.dao.ITransferOrderDao;
import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.DirectPaymentDTO;
import com.hisun.lemon.csh.dto.payment.PaymentResultDTO;
import com.hisun.lemon.csh.dto.payment.QpPaymentDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.csh.entity.TransferOrderDO;
import com.hisun.lemon.csh.enums.*;
import com.hisun.lemon.csh.mq.PaymentHandler;
import com.hisun.lemon.csh.service.IDmOrderService;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.pwm.client.PwmWithdrawClient;
import com.hisun.lemon.pwm.constants.PwmConstants;
import com.hisun.lemon.pwm.dto.GetByOrderDTO;
import com.hisun.lemon.pwm.dto.UpdateOrderDTO;
import com.hisun.lemon.pwm.dto.WithdrawOrderRspDTO;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateRspDTO;
import com.hisun.lemon.urm.client.UserAuthenticationClient;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.CheckPayPwdDTO;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 数币相关订单服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/24 20:02
 */
@Service
public class DmOrderServiceImpl implements IDmOrderService {
    private static final Logger logger = LoggerFactory.getLogger(DmOrderServiceImpl.class);

    @Resource
    protected OrderCommonComponent orderCommonComponent;

    @Resource
    protected AcmComponent acmComponent;


    @Resource
    ObjectMapper objectMapper;


    @Resource
    protected AccountingTreatmentClient accountingTreatmentClient;
    @Resource
    protected RiskCheckClient riskCheckClient;

    @Resource
    protected PaymentHandler paymentHandler;

    @Resource
    private IOrderDao orderDao;

    @Resource
    private ITransferOrderDao transferDao;

    @Resource
    protected OrderTransactionalService orderTransactionalService;

    @Resource
    protected UserBasicInfClient userBasicInfClient;

    @Resource
    CmmServerClient cmmServerClient;

    @Resource
    protected UserAuthenticationClient userAuthenticationClient;

    @Resource
    private IPayJrnDao payJrnDao;

    @Resource
    private PwmWithdrawClient pwmWithdrawClient;

    @Resource
    private AccountManagementClient accountManagementClient;

    /**
     * 数币转账账务处理
     *
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFinance(GenericDTO<HandleFinanceDTO> req) {
        HandleFinanceDTO handleFinanceDTO = req.getBody();
        OrderDO orderDO = orderDao.get(handleFinanceDTO.getOrderNo());

        // 更新订单状态，进行账务处理
        orderDO.setOrderStatus(OrderStatus.SUCC.getValue());
        orderDO.setOrderSuccTm(DateTimeUtils.getCurrentLocalDateTime());
        orderDO.setTxHash(handleFinanceDTO.getTxHash());
        orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        orderDao.update(orderDO);
        // 后续处理
        // 转账订单号
        String orderId = orderDO.getBusOrderNo();
        TransferOrderDO transferDB = new TransferOrderDO();
        transferDB = transferDao.get(orderId);
        // 原订单不存在
        if (JudgeUtils.isNull(transferDB)) {
            throw new LemonException("TAM20001");
        }
        // 订单已经成功
        if (StringUtils.equals(transferDB.getOrderSts(), TamConstants.ORD_STS_S)) {
            logger.info("订单已成功");
            throw new LemonException("TAM20011");
        }

        BigDecimal amountSum = orderDO.getTotalAmt();
        // 账务处理
        AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
        AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
        AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
        List<AccountingReqDTO> acUserList = new ArrayList();
        // 实际交易金额
        BigDecimal amount = orderDO.getLeftBalAmt();
        // 流水号
        String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
        payJrnNo = orderDO.getBusType() + payJrnNo;
        // 资金类型
        String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
        // 收款方用户id
        String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayeeId(), balCapType, orderDO.getCcy());
        // 借:其他应付款-暂收-收银台 100
        cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo, transferDB.getTxType(),
                ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
                ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null, null, null, "转账out");
        // 贷：其他应付款-支付账户-现金账户 100
        userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo,
                transferDB.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.USER_AC_TYP,
                balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "转账out");
        acUserList.add(cshItemReqDTO);
        acUserList.add(userAccountReqDTO);
        if (orderDO.getFee().compareTo(BigDecimal.valueOf(0)) > 0) {
            // 贷：手续费收入-支付账户-转账 2
            feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo,
                    transferDB.getTxType(), ACMConstants.ACCOUNTING_NOMARL, orderDO.getFee(), null, ACMConstants.ITM_AC_TYP, balCapType,
                    ACMConstants.AC_C_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.DM_USDT_AC_ITEM_TAM_PAY : TamConstants.DM_USDC_AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_CSH_PAY, null, null, null,
                    "转账out");
            acUserList.add(feeItemReqDTO);
        }
        acmComponent.requestAc(acUserList);
        logger.info("请求账务处理完成=========================");

//        logger.info("更新数币站内转账付款金额-fkAcNo:{},skAcNo:{}",transferDB.getFkAcNo(),transferDB.getSkAcNo());
//        GenericRspDTO<DmAccountDetailRspDTO> fkTranDTO = accountManagementClient.queryDmAccountDetail(transferDB.getFkAcNo());
//        if (JudgeUtils.isNotSuccess(fkTranDTO.getMsgCd())) {
//            logger.info("更新数币站内转账付款金额失败,付款账号编码:{}", transferDB.getFkAcNo());
//            throw new LemonException(fkTranDTO.getMsgCd());
//        }
//        GenericRspDTO<DmAccountDetailRspDTO> skTranDTO = accountManagementClient.queryDmAccountDetail(transferDB.getSkAcNo());
//        if (JudgeUtils.isNotSuccess(skTranDTO.getMsgCd())) {
//            logger.info("更新数币站内转账收款金额失败,收款账号编码:{}", transferDB.getSkAcNo());
//            throw new LemonException(skTranDTO.getMsgCd());
//        }


        // 获取转账订单流水
        PayJrnDO payJrnDO = payJrnDao.getByPayOrderNo(orderDO.getOrderNo());
        if (payJrnDO == null) {
            throw new LemonException("TAM10033");
        }
        // 更新转账交易流水
        PayJrnDO updPayJrnDO = new PayJrnDO();
        updPayJrnDO.setPayJrnNo(payJrnDO.getPayJrnNo());
        updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_S);
        updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
        int num1 = payJrnDao.update(updPayJrnDO);
        if (num1 != 1) {
            throw new LemonException("CSH20009");
        }

        //设置操作员
        Map<String, Map<Object, Object>> extMap = null;
        if (JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
                && JudgeUtils.isNotNull(transferDB.getUserId())) {
            extMap = new HashMap<>();
            Map<Object, Object> map = new HashMap<>();
            map.put("loginId", transferDB.getUserId());
            extMap.put(TradeType.CONSUME.getType(), map);
        }

        //同步账单
        orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL, true, "1234", extMap);

        // 更新外围转账订单状态
        TransferOrderDO updateTransfer = new TransferOrderDO();
        updateTransfer.setOrderNo(orderDO.getBusOrderNo());
        updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
        // 更新订单信息
        int result = this.transferDao.update(updateTransfer);
        if (result != 1) {
            throw new LemonException("TAM20003");
        }
    }

    /**
     * 数币转账失败处理
     *
     * @param req
     */
    @Override
    public void handleFailTransfer(GenericDTO<HandleFailTranDTO> req) {
        HandleFailTranDTO handleFailTranDTO = req.getBody();
        OrderDO orderDO = orderDao.get(handleFailTranDTO.getOrderNo());
        orderDO.setTxHash(handleFailTranDTO.getTxHash());
        orderDO.setOrderStatus(OrderStatus.FAIL.getValue());
        orderDO.setOrderSuccTm(DateTimeUtils.getCurrentLocalDateTime());
        orderDao.update(orderDO);
        // 转账订单号
        String orderId = orderDO.getBusOrderNo();
        TransferOrderDO transferDB = new TransferOrderDO();
        transferDB = transferDao.get(orderId);
        // 原订单不存在
        if (JudgeUtils.isNull(transferDB)) {
            throw new LemonException("TAM20001");
        }
        // 订单已经成功
        if (StringUtils.equals(transferDB.getOrderSts(), TamConstants.ORD_STS_S)) {
            logger.info("订单已成功");
            throw new LemonException("TAM20011");
        }

        // 账务处理
        AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
        AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
        AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
        List<AccountingReqDTO> acUserList = new ArrayList();
        // 交易金额
        BigDecimal amount = transferDB.getAmount();
        // 流水号
        String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
        payJrnNo = orderDO.getBusType() + payJrnNo;
        // 资金类型
        String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
        // 付款方用户id
        String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType, orderDO.getCcy());
        // 借:其他应付款-暂收-收银台 -102
        cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo, transferDB.getTxType(),
                ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
                ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null, null, null, "转账out");
        // 贷：其他应付款-支付账户-现金账户 +102
        userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDB.getOrderNo(), payJrnNo,
                transferDB.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.USER_AC_TYP,
                balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "转账out");
        acUserList.add(cshItemReqDTO);
        acUserList.add(userAccountReqDTO);
        acmComponent.requestAc(acUserList);
        logger.info("账务冲正处理完成=========================");

        // 获取转账订单流水
        PayJrnDO payJrnDO = payJrnDao.getByPayOrderNo(orderDO.getOrderNo());
        if (payJrnDO == null) {
            throw new LemonException("TAM10033");
        }
        // 更新转账交易流水
        PayJrnDO updPayJrnDO = new PayJrnDO();
        updPayJrnDO.setPayJrnNo(payJrnDO.getPayJrnNo());
        updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_F);
        updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
        int num1 = payJrnDao.update(updPayJrnDO);
        if (num1 != 1) {
            throw new LemonException("CSH20009");
        }

        //设置操作员
        Map<String, Map<Object, Object>> extMap = null;
        if (JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
                && JudgeUtils.isNotNull(transferDB.getUserId())) {
            extMap = new HashMap<>();
            Map<Object, Object> map = new HashMap<>();
            map.put("loginId", transferDB.getUserId());
            extMap.put(TradeType.CONSUME.getType(), map);
        }

        //同步账单
        orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL, true, "1234", extMap);

        // 更新外围转账订单状态
        TransferOrderDO updateTransfer = new TransferOrderDO();
        updateTransfer.setOrderNo(orderDO.getBusOrderNo());
        updateTransfer.setOrderSts(TamConstants.ORD_STS_F);
        // 更新订单信息
        int result = this.transferDao.update(updateTransfer);
        if (result != 1) {
            throw new LemonException("TAM20003");
        }
    }

    @Override
    public PaymentResultDTO directDm(GenericDTO<DirectPaymentDTO> directGenDTO) {
        //校验支付密码
        GenericDTO<CheckPayPwdDTO> payPwdDTOGenericDTO = new GenericDTO<>();
        CheckPayPwdDTO payPwdReq = new CheckPayPwdDTO();
        payPwdReq.setPayPwd(directGenDTO.getBody().getPayPassword());
        payPwdDTOGenericDTO.setBody(payPwdReq);
        GenericRspDTO<NoBody> genericRspDTO = userAuthenticationClient.checkPayPwd(payPwdDTOGenericDTO);
        if(JudgeUtils.isNull(genericRspDTO)){
            LemonException.throwBusinessException("URM30005");
        }
        if(JudgeUtils.equals("URM30005", genericRspDTO.getMsgCd())){
            LemonException.throwBusinessException("URM30005");
        }
//        //查询支付密码错误次数是否超过5次
        if(JudgeUtils.equals("URM30011", genericRspDTO.getMsgCd())){
            LemonException.throwBusinessException("URM30011");
        }
        if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
            if(JudgeUtils.isNotBlank(genericRspDTO.getMsgInfo())) {
                LemonException.throwLemonException(genericRspDTO.getMsgCd(), genericRspDTO.getMsgInfo());
            }else{
                LemonException.throwBusinessException(genericRspDTO.getMsgCd());
            }
        }


        DirectPaymentDTO directPaymentDTO = directGenDTO.getBody();
        OrderDO orderDO = orderTransactionalService.queryOrderByBusNo(directPaymentDTO.getExtOrderNo());
        if (JudgeUtils.isNotNull(orderDO)) {
            LemonException.throwBusinessException("CSH20080");
        }
        orderDO = new OrderDO();
        BeanUtils.copyProperties(orderDO, directPaymentDTO);
        String ymd = DateTimeUtils.getCurrentDateStr();
        String orderNo = IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
        orderDO.setOrderNo(ymd + orderNo);
        orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
        orderDO.setOrderStatus(OrderStatus.PRE_PAY.getValue());
        if ("DZ01".equals(directPaymentDTO.getBusType())) {
            orderDO.setOrderStatus(OrderStatus.WAIT_CALLBACK.getValue());
        }
        if (JudgeUtils.isNotNull(directPaymentDTO.getEffTm())) {
            orderDO.setOrderExpTm(directPaymentDTO.getEffTm());
        } else {
            orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
        }
        orderDO.setBusOrderNo(directPaymentDTO.getExtOrderNo());
        //设置无补款类型
        orderDO.setCrdPayType(CshConstants.CRD_TYPE_NONE);
        orderDO.setPayerId(directPaymentDTO.getPayerId());
        orderDO.setFndOrderNo("");
        orderDO.setPayeeId(directPaymentDTO.getPayeeId());
        orderDO.setMercName(directPaymentDTO.getPayeeName());
        orderDO.setOrderChannel(directPaymentDTO.getSysChannel());
        orderDO.setBusPayType(directPaymentDTO.getBusPaytype());

        orderDO.setInvAmt(new BigDecimal(0));
        orderDO.setLeftCardAmt(new BigDecimal(0));
        orderDO.setLeftInvAmt(new BigDecimal(0));

        orderDO.setCcy(directPaymentDTO.getOrderCcy());
        orderDO.setGoodsInfo(directPaymentDTO.getGoodsDesc());
        orderDO.setRemark(directPaymentDTO.getRemark());
        orderDO.setPayJrnNo("");
        orderDO.setAppCnl(directPaymentDTO.getAppCnl());
        orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
        orderDO.setBusType(directPaymentDTO.getBusType());
//		String paytype=orderCommonComponent.getPayType(orderDO.getAppCnl(),orderDO.getBusType(),
//				orderDO.getPayeeId(), orderDO.getBusPayType());
//		//没有找到合适的支付方式
//		if(paytype.indexOf(CshConstants.YES)<0){
//			throw  new LemonException("CSH20016");
//		}
        String payType = "站内或链上白名单转账";
        if (JudgeUtils.equals(BussinessType.DM_TRANSFER02.getValue(), directPaymentDTO.getBusType())) {
            payType = "链上转账";
        }
        orderDO.setPayType(payType);
        caculateFee(orderDO);
        orderDO.setCouponNo("");
        orderDO.setCouponAmt(BigDecimal.valueOf(directPaymentDTO.gethCouponAmt()));
        orderDO.setLeftCouponAmt(BigDecimal.valueOf(directPaymentDTO.gethCouponAmt()));
        orderDO.setBalAmt(directPaymentDTO.getCashAmt());
        orderDO.setCrdPayAmt(new BigDecimal(0));
        orderDO.setLeftBalAmt(directPaymentDTO.getCashAmt());
        List<String> fileUrlList = directPaymentDTO.getFileUrl();
        String fileUrl = (fileUrlList != null) ? String.join(",", fileUrlList) : "";
        orderDO.setFileUrl(fileUrl);
        //数币转账 acNo 和 toAcNo 存双方地址
        GenericRspDTO<DmAccountDetailRspDTO> fkAccDTO = accountManagementClient.queryDmAccountDetail(directPaymentDTO.getFkAcNo());
        if (JudgeUtils.isNotSuccess(fkAccDTO.getMsgCd()) || JudgeUtils.isNull(fkAccDTO.getBody())) {
            LemonException.throwBusinessException(fkAccDTO.getMsgCd());
        }
        orderDO.setAcNo(fkAccDTO.getBody().getAddress());
        //directPaymentDTO.getToAcNo() 已经是 收款方地址
        orderDO.setToAcNo(directPaymentDTO.getToAcNo());

        checkBeforeInitOrder(orderDO);

        orderTransactionalService.initOrder(orderDO);
        boolean payerIdExist = true;
        if (JudgeUtils.isBlank(orderDO.getPayerId())) {
            payerIdExist = false;
        }
        //付款方手机后四位
        String extInfo = null;
        if (JudgeUtils.isNotBlank(directPaymentDTO.getMblNo())) {
            extInfo = directPaymentDTO.getMblNo() + "|";
        }
        //创建账单
        orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.CREATE_BIL, payerIdExist, extInfo, null);

        BigDecimal balAmt = orderDO.getBalAmt();
        String couponType = orderDO.getCouponType();

        PayJrnDO payJrnDO = saveJrn(orderDO, balAmt, Integer.valueOf(CshConstants.CRD_TYPE_NONE), BigDecimal.valueOf(0),
                couponType, orderDO.getCouponAmt(), orderDO.getCouponNo(), directPaymentDTO.getOrderCcy());

        //实时风控
        riskControl(orderDO);
        //账务处理
        handleAccAndMkm(orderDO, payJrnDO, directPaymentDTO.getFkAcNo());
        return createPaymentResultDTO(orderDO.getOrderNo(), orderDO.getOrderAmt(), orderDO.getBusOrderNo(), orderDO.getGoodsInfo(), orderDO.getPayeeId(), orderDO.getTxType());
    }

    /**
     * 数币提现余额支付
     *
     * @param directGenDTO
     * @return
     */
    @Override
    public PaymentResultDTO directDmWithdraw(GenericDTO<DirectPaymentDTO> directGenDTO) {
        DirectPaymentDTO directPaymentDTO = directGenDTO.getBody();
        OrderDO orderDO = orderTransactionalService.queryOrderByBusNo(directPaymentDTO.getExtOrderNo());
        if (JudgeUtils.isNotNull(orderDO)) {
            LemonException.throwBusinessException("CSH20080");
        }
        orderDO = new OrderDO();
        BeanUtils.copyProperties(orderDO, directPaymentDTO);
        String ymd = DateTimeUtils.getCurrentDateStr();
        String orderNo = IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
        orderDO.setOrderNo(ymd + orderNo);
        orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
        orderDO.setOrderStatus(OrderStatus.PRE_PAY.getValue());
        if (JudgeUtils.isNotNull(directPaymentDTO.getEffTm())) {
            orderDO.setOrderExpTm(directPaymentDTO.getEffTm());
        } else {
            orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
        }
        orderDO.setBusOrderNo(directPaymentDTO.getExtOrderNo());
        //设置无补款类型
        orderDO.setCrdPayType(CshConstants.CRD_TYPE_NONE);
        orderDO.setPayerId(directPaymentDTO.getPayerId());
        orderDO.setFndOrderNo("");
        orderDO.setPayeeId("");
        orderDO.setMercName(directPaymentDTO.getPayeeName());
        orderDO.setOrderChannel(directPaymentDTO.getSysChannel());
        orderDO.setBusPayType(directPaymentDTO.getBusPaytype());

        orderDO.setInvAmt(new BigDecimal(0));
        orderDO.setLeftCardAmt(new BigDecimal(0));
        orderDO.setLeftInvAmt(new BigDecimal(0));

        orderDO.setCcy(directPaymentDTO.getOrderCcy());
        orderDO.setGoodsInfo(directPaymentDTO.getGoodsDesc());
        orderDO.setRemark(directPaymentDTO.getRemark());
        orderDO.setPayJrnNo("");
        orderDO.setAppCnl(directPaymentDTO.getAppCnl());
        orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
        orderDO.setBusType(directPaymentDTO.getBusType());
//		String paytype=orderCommonComponent.getPayType(orderDO.getAppCnl(),orderDO.getBusType(),
//				orderDO.getPayeeId(), orderDO.getBusPayType());
//		//没有找到合适的支付方式
//		if(paytype.indexOf(CshConstants.YES)<0){
//			throw  new LemonException("CSH20016");
//		}
        String payType = BussinessType.DM_WITHDRAW.getValue();
        orderDO.setPayType(payType);
        caculateFee(orderDO);
        orderDO.setCouponNo("");
        orderDO.setCouponAmt(BigDecimal.valueOf(directPaymentDTO.gethCouponAmt()));
        orderDO.setLeftCouponAmt(BigDecimal.valueOf(directPaymentDTO.gethCouponAmt()));
        orderDO.setBalAmt(directPaymentDTO.getCashAmt());
        orderDO.setCrdPayAmt(new BigDecimal(0));
        orderDO.setLeftBalAmt(directPaymentDTO.getCashAmt());
        //数币转账 acNo 和 toAcNo 存双方地址
        GenericRspDTO<DmAccountDetailRspDTO> fkAccDTO = accountManagementClient.queryDmAccountDetail(directPaymentDTO.getFkAcNo());
        if (JudgeUtils.isNotSuccess(fkAccDTO.getMsgCd()) || JudgeUtils.isNull(fkAccDTO.getBody())) {
            LemonException.throwBusinessException(fkAccDTO.getMsgCd());
        }
        orderDO.setAcNo(fkAccDTO.getBody().getAddress());
        //directPaymentDTO.getToAcNo() 已经是 收款方地址
        orderDO.setToAcNo(directPaymentDTO.getToAcNo());

        checkBeforeInitOrder(orderDO);

        orderTransactionalService.initOrder(orderDO);
        boolean payerIdExist = true;
        if (JudgeUtils.isBlank(orderDO.getPayerId())) {
            payerIdExist = false;
        }
        //付款方手机后四位
        String extInfo = null;
        if (JudgeUtils.isNotBlank(directPaymentDTO.getMblNo())) {
            extInfo = directPaymentDTO.getMblNo() + "|";
        }
        //创建账单
        orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.CREATE_BIL, payerIdExist, extInfo, null);

        BigDecimal balAmt = orderDO.getBalAmt();
        String couponType = orderDO.getCouponType();

        PayJrnDO payJrnDO = saveJrn(orderDO, balAmt, Integer.valueOf(CshConstants.CRD_TYPE_NONE), BigDecimal.valueOf(0),
                couponType, orderDO.getCouponAmt(), orderDO.getCouponNo(), directPaymentDTO.getOrderCcy());

        //实时风控
        riskControl(orderDO);
        //账务处理
        handleDmWithdrawAccAndMkm(orderDO, payJrnDO, directPaymentDTO.getFkAcNo());
        return createPaymentResultDTO(orderDO.getOrderNo(), orderDO.getOrderAmt(), orderDO.getBusOrderNo(), orderDO.getGoodsInfo(), orderDO.getPayeeId(), orderDO.getTxType());
    }

    private void handleDmWithdrawAccAndMkm(OrderDO orderDO, PayJrnDO payJrnDO, String fkAcNo) {
        String dataOrder = ObjectMapperHelper.writeValueAsString(objectMapper, orderDO, true);
        logger.debug("=======================账务和营销处理 params print start=======================");
        logger.debug("orderDO::" + dataOrder);
        String dataJrn = ObjectMapperHelper.writeValueAsString(objectMapper, payJrnDO, true);
        logger.debug("payJrnDO::" + dataJrn);
        logger.debug("=======================账务和营销处理 params print over=======================");
        List<AccountingReqDTO> accList = createDmWithdrawAccQueue(orderDO, payJrnDO, fkAcNo);

        BigDecimal dAmt = BigDecimal.ZERO;
        BigDecimal cAmt = BigDecimal.ZERO;
        for (AccountingReqDTO dto : accList) {
            if (JudgeUtils.isNotNull(dto)) {
                if (JudgeUtils.equals(dto.getDcFlg(), ACMConstants.AC_D_FLG)) {
                    dAmt = dAmt.add(dto.getTxAmt());
                } else {
                    cAmt = cAmt.add(dto.getTxAmt());
                }
            }
        }

        // 借贷平衡校验
        if (cAmt.compareTo(dAmt) != 0) {
            LemonException.throwBusinessException("CSH20052");
        }

        //消费营销权
        acmComponent.useCoupon(orderDO.getOrderNo(), payJrnDO.getCouponType(), payJrnDO.getCouponNo(),
                orderDO.getTotalAmt(), payJrnDO.getUserId(), orderDO.getPayeeId(), payJrnDO.getCouponAmt());

        GenericDTO<List<AccountingReqDTO>> userAccDto = new GenericDTO<>();
        userAccDto.setBody(accList);
        //生成科目收支明细
        GenericRspDTO<NoBody> accountingTreatment = accountingTreatmentClient.accountingTreatment(userAccDto);
        if (!JudgeUtils.isSuccess(accountingTreatment.getMsgCd())) {
            logger.error("订单" + orderDO.getOrderNo() + "账务失败：" + accountingTreatment.getMsgCd());
            paymentHandler.cancelMkmCoupon(orderDO.getOrderNo(), orderDO.getOrderAmt(), orderDO.getCouponType());
            LemonException.throwBusinessException(accountingTreatment.getMsgCd());
        }

        if (StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
                || StringUtils.equals(orderDO.getTxType(), TradeType.PAYMENT.getType())) {
            //登记商户手续费
            paymentHandler.registMerChantFee(orderDO);
        }
        //登记用户手续费
        if (orderDO.getFee().compareTo(BigDecimal.ZERO) > 0) {
            paymentHandler.registUserFee(orderDO);
        }

    }

    private List<AccountingReqDTO> createDmWithdrawAccQueue(OrderDO orderDO, PayJrnDO payJrnDO, String fkAcNo) {
        String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
//        String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType, orderDO.getCcy());
        String balAcNo = fkAcNo;
        BigDecimal fee = orderDO.getFee();
        BigDecimal amountSum = orderDO.getTotalAmt();
        List acList = new ArrayList<>();
        //判断使用余额支付还是快捷
        if (payJrnDO.getCrdPayAmt() != null && payJrnDO.getCrdPayAmt().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal tamCrdPayAmt = payJrnDO.getCrdPayAmt();
            //借：应收账款-渠道充值-XX银行/支付宝/微信
            AccountingReqDTO cnlRechargeBnkReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
                    ACMConstants.ACCOUNTING_NOMARL, tamCrdPayAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
                    AcItem.I_CNL_BANK.getValue(), null, null, null, null, "转账  使用快捷支付");
            acList.add(cnlRechargeBnkReqDTO);
            //贷：其他应付款-暂收-收银台
            AccountingReqDTO userAccountReqDTO1 = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
                    ACMConstants.ACCOUNTING_NOMARL, tamCrdPayAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
                    AcItem.O_CSH.getValue(), null, null, null, null, "转账  使用快捷支付");
            acList.add(userAccountReqDTO1);
        } else {
            // 借：其他应付款-支付账户-现金账户 102
            AccountingReqDTO cshItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                    payJrnDO.getPayJrnNo(), orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo,
                    ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ?
                            TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null,
                    null, null, "数币提现使用余额支付");
            acList.add(cshItemReqDTO);
            // 贷：其他应付款-暂收-收银台 102
            AccountingReqDTO userAccountReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                    payJrnDO.getPayJrnNo(), orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, null,
                    ACMConstants.ITM_AC_TYP, balCapType,
                    ACMConstants.AC_C_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY :
                            TamConstants.USDC_AC_ITEM_CSH_PAY, balAcNo, null,
                    null, null, "数币提现使用余额支付");
            acList.add(userAccountReqDTO);
        }

        return acList;
    }

    @Override
    public void handleFailWithdraw(GenericDTO<HandleFailTranDTO> req) {
        HandleFailTranDTO handleFailTranDTO = req.getBody();
        OrderDO orderDO = orderDao.get(handleFailTranDTO.getOrderNo());
        orderDO.setTxHash(handleFailTranDTO.getTxHash());
        orderDO.setOrderStatus(OrderStatus.FAIL.getValue());
        orderDO.setOrderSuccTm(DateTimeUtils.getCurrentLocalDateTime());
        orderDO.setRemark("The withdrawal call to Cregis failed");
        orderDao.update(orderDO);
        // 获取提现订单
        GenericDTO<GetByOrderDTO> genericDTO = new GenericDTO<>();
        GetByOrderDTO getByOrderDTO = new GetByOrderDTO();
        getByOrderDTO.setOrderNo(orderDO.getBusOrderNo());
        genericDTO.setBody(getByOrderDTO);
        GenericRspDTO<WithdrawOrderRspDTO> byOrder = pwmWithdrawClient.getByOrder(genericDTO);
        if (JudgeUtils.isNotSuccess(byOrder.getMsgCd())){
            LemonException.throwBusinessException(byOrder.getMsgCd());
        }
        WithdrawOrderRspDTO withdrawOrderRspDTO = byOrder.getBody();
        // 原订单不存在
        if (JudgeUtils.isNull(withdrawOrderRspDTO)) {
            throw new LemonException("TAM20001");
        }
        // 订单已经成功
        if (StringUtils.equals(withdrawOrderRspDTO.getOrderStatus(), PwmConstants.WITHDRAW_ORD_S1)) {
            logger.info("订单已成功");
            throw new LemonException("TAM20011");
        }

        // 账务处理
        AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
        AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
        AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
        List<AccountingReqDTO> acUserList = new ArrayList();
        // 交易金额
        BigDecimal totalAmt = orderDO.getTotalAmt();
        // 流水号
        String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
        payJrnNo = orderDO.getBusType() + payJrnNo;
        // 资金类型
        String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
        // 付款方用户id
//        String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType, orderDO.getCcy());
        String balAcNo = handleFailTranDTO.getFkAcNo();
        // 借:其他应付款-暂收-收银台 -102
        cshItemReqDTO = acmComponent.createAccountingReqDTO(withdrawOrderRspDTO.getOrderNo(), payJrnNo, withdrawOrderRspDTO.getTxType(),
                ACMConstants.ACCOUNTING_NOMARL, totalAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
                ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null, null, null, "转账out");
        // 贷：其他应付款-支付账户-现金账户 +102
        userAccountReqDTO = acmComponent.createAccountingReqDTO(withdrawOrderRspDTO.getOrderNo(), payJrnNo,
                withdrawOrderRspDTO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, totalAmt, balAcNo, ACMConstants.USER_AC_TYP,
                balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "转账out");
        acUserList.add(cshItemReqDTO);
        acUserList.add(userAccountReqDTO);
        acmComponent.requestAc(acUserList);
        logger.info("账务冲正处理完成=========================");

        // 获取提现订单流水
        PayJrnDO payJrnDO = payJrnDao.getByPayOrderNo(orderDO.getOrderNo());
        if (payJrnDO == null) {
            throw new LemonException("TAM10033");
        }
        // 更新提现交易流水
        PayJrnDO updPayJrnDO = new PayJrnDO();
        updPayJrnDO.setPayJrnNo(payJrnDO.getPayJrnNo());
        updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_F);
        updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
        int num1 = payJrnDao.update(updPayJrnDO);
        if (num1 != 1) {
            throw new LemonException("CSH20009");
        }

        //设置操作员
        Map<String, Map<Object, Object>> extMap = null;
        if (JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
                && JudgeUtils.isNotNull(withdrawOrderRspDTO.getUserId())) {
            extMap = new HashMap<>();
            Map<Object, Object> map = new HashMap<>();
            map.put("loginId", withdrawOrderRspDTO.getUserId());
            extMap.put(TradeType.CONSUME.getType(), map);
        }

        //同步账单
        orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL, true, "1234", extMap);

        // 更新外围提现订单状态
        GenericDTO<UpdateOrderDTO> updateGenericDTO = new GenericDTO<>();
        UpdateOrderDTO updateOrderDTO = new UpdateOrderDTO();
        updateOrderDTO.setOrderNo(orderDO.getBusOrderNo());
        updateOrderDTO.setOrderStatus(PwmConstants.WITHDRAW_ORD_F1);
        updateGenericDTO.setBody(updateOrderDTO);
        GenericRspDTO<WithdrawOrderRspDTO> genericRspDTO = pwmWithdrawClient.updateOrder(updateGenericDTO);
        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
            LemonException.throwBusinessException(genericRspDTO.getMsgCd());
        }
    }

    protected PaymentResultDTO createPaymentResultDTO(String orderNo, BigDecimal orderAmt, String busOrderNo, String desc, String payeeId, String txType) {
        PaymentResultDTO paymentResultDTO = new PaymentResultDTO();
        paymentResultDTO.setOrderAmt(orderAmt);
        paymentResultDTO.setBusOrderNo(busOrderNo);
        paymentResultDTO.setOrderNo(orderNo);
        paymentResultDTO.setGoodsDesc(desc);

        if (JudgeUtils.isNotBlank(txType) && JudgeUtils.equals(txType, TradeType.CONSUME.getType())) {
            try {
                UserBasicInfDTO userBasicInfDTO = userBasicInfClient.queryUser(payeeId).getBody();
                if (JudgeUtils.isNotNull(userBasicInfDTO)) {
                    paymentResultDTO.setMerchantName(userBasicInfDTO.getMercName());
                    paymentResultDTO.setMblNo(userBasicInfDTO.getMblNo());
                }
            } catch (LemonException e) {
                logger.error("订单号：" + orderNo + ",查询收款方id:" + payeeId + ",信息失败" + e.getMsgCd());
            }
        }
        return paymentResultDTO;
    }

    //实时风控
    protected void riskControl(OrderDO orderDO) {
        List<JrnReqDTO> jrnReqDTOList = new ArrayList<>();
        String busType = orderDO.getBusType();

        JrnReqDTO jrnReqDTO = new JrnReqDTO();
        jrnReqDTO.setCcy(orderDO.getCcy());
        jrnReqDTO.setPayCrdNo(null);

        if (StringUtils.isNotBlank(orderDO.getPayeeId())) {
            jrnReqDTO.setStlUserId(orderDO.getPayeeId());
            if (StringUtils.equals(orderDO.getBusType(), "0302")) {
                jrnReqDTO.setStlUserTyp("02");
            } else {
                jrnReqDTO.setStlUserTyp("01");
            }
        }

        jrnReqDTO.setTxCnl(CshConstants.RSK_CNL_APP);
        jrnReqDTO.setPayUserTyp("01");

        if (JudgeUtils.isBlank(orderDO.getPayerId())) {
            if (JudgeUtils.equals(orderDO.getTxType(), PwmConstants.TX_TYPE_RECHANGE)) {
                jrnReqDTO.setPayUserId(LemonUtils.getUserId());
            }
        } else {
            jrnReqDTO.setPayUserId(orderDO.getPayerId());
        }

        jrnReqDTO.setTxAmt(orderDO.getOrderAmt());
        if (JudgeUtils.equalsAny(busType, BussinessType.RECHARGE_HALL.getValue(), BussinessType.RECHARGE_OFFLINE.getValue())) {
            //免密支付:pswFlg=1
            jrnReqDTO.addTxData("pswFlg=1");
        } else {
            //非免密:pswFlg=0
            jrnReqDTO.addTxData("pswFlg=0");
        }
        jrnReqDTO.setTxOrdNo(orderDO.getOrderNo());
        jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
        jrnReqDTO.setTxSts("0");
        jrnReqDTO.setTxDate(DateTimeUtils.getCurrentLocalDate());
        jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
        jrnReqDTO.setTxTyp(orderDO.getTxType());
        //补款方式
        String crdPayType = orderDO.getCrdPayType();

        //无补款
        if (JudgeUtils.equals(crdPayType, CshConstants.CRD_TYPE_NONE) && orderDO.getCrdPayAmt().compareTo(BigDecimal.valueOf(0)) == 0) {
            //无优惠 账户余额
            if (JudgeUtils.isNotNull(orderDO.getBalAmt()) && orderDO.getBalAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
                JrnReqDTO accJrnReqDTO = new JrnReqDTO();
                BeanUtils.copyProperties(accJrnReqDTO, jrnReqDTO);
                accJrnReqDTO.setPayTyp(Constants.PAY_TYP_ACCOUNT);
                accJrnReqDTO.setTxAmt(orderDO.getBalAmt());
                logger.debug("风控余额金额：" + orderDO.getBalAmt());
                jrnReqDTOList.add(accJrnReqDTO);
            }
            //海币优惠
            if (JudgeUtils.isNotNull(orderDO.getCouponAmt()) && orderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
                if (JudgeUtils.equals(orderDO.getCouponType(), CouponType.H_COUPON.getType())) {
                    JrnReqDTO seaJrnReqDTO = new JrnReqDTO();
                    BeanUtils.copyProperties(seaJrnReqDTO, jrnReqDTO);
                    seaJrnReqDTO.setPayTyp(Constants.PAY_TYP_SEATEL);
                    seaJrnReqDTO.setTxAmt(orderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
                    logger.debug("风控海币优惠金额：" + orderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
                    jrnReqDTOList.add(seaJrnReqDTO);
                }
            }
        }
        //有补款
        else {
            switch (crdPayType) {
                //快捷
                case CshConstants.CRD_TYPE_QP:
                    JrnReqDTO qpJrnReqDTO = new JrnReqDTO();
                    BeanUtils.copyProperties(qpJrnReqDTO, jrnReqDTO);
                    qpJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
                    qpJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
                    //快捷设置银行卡号
                    QpPaymentDTO requestDto = (QpPaymentDTO) OrderContextHolder.getRequestDto();
                    String needBind = requestDto.getNeedBind();
                    String cardNo = "";
                    if (JudgeUtils.equals(needBind, "0")) {
                        GenericDTO<CommonEncryptReqDTO> reqDTO = new GenericDTO<>();
                        CommonEncryptReqDTO req = new CommonEncryptReqDTO();
                        req.setData(requestDto.getCrdNoEnc());
                        req.setType("decrypt");
                        reqDTO.setBody(req);
                        GenericRspDTO<CommonEncryptRspDTO> resp = cmmServerClient.encrypt(reqDTO);
                        if (JudgeUtils.isNotSuccess(resp.getMsgCd())) {
                            LemonException.throwBusinessException(resp.getMsgCd());
                        }
                        CommonEncryptRspDTO commonEncryptRspDTO = resp.getBody();
                        cardNo = commonEncryptRspDTO.getData();
                    } else if (JudgeUtils.equals(needBind, "1")) {
                        cardNo = requestDto.getCardNo();
                    }

                    qpJrnReqDTO.setPayCrdNo(cardNo);
                    jrnReqDTOList.add(qpJrnReqDTO);
                    break;
                //网银
                case CshConstants.CRD_TYPE_NB:
                    JrnReqDTO ebankJrnReqDTO = new JrnReqDTO();
                    BeanUtils.copyProperties(ebankJrnReqDTO, jrnReqDTO);
                    //获取支付路径机构
                    String rutCorgNo = orderDO.getCapCorgNo();
                    logger.debug("网银支付合作机构:" + rutCorgNo);
                    if (JudgeUtils.isBlank(rutCorgNo)) {
                        break;
                    }
                    if (JudgeUtils.equals(rutCorgNo, CshConstants.PAY_TYPE_WX)) {
                        ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_WECHAT);
                    } else if (JudgeUtils.equals(rutCorgNo, CshConstants.PAY_TYPE_ALI)) {
                        ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_ALIPAY);
                    } else if (JudgeUtils.equals(rutCorgNo, CshConstants.PAY_TYPE_ICBC)) {
                        ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
                    } else if (JudgeUtils.equals(rutCorgNo, CshConstants.PAY_TYPE_BEST)) {
                        ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_BESTPAY);
                    } else {

                    }
                    ebankJrnReqDTO.setStlUserId(orderDO.getPayeeId());
                    ebankJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
                    ebankJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
                    logger.debug("网银交易风控金额:" + orderDO.getCrdPayAmt());
                    jrnReqDTOList.add(ebankJrnReqDTO);
                    break;
                //线下转账
                case CshConstants.CRD_TYPE_FL:
                    JrnReqDTO offlieJrnReqDTO = new JrnReqDTO();
                    BeanUtils.copyProperties(offlieJrnReqDTO, jrnReqDTO);
                    offlieJrnReqDTO.setPayTyp(Constants.PAY_TYP_OFFLINE);
                    offlieJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
                    logger.debug("线下汇款交易风控金额:" + orderDO.getCrdPayAmt());
                    jrnReqDTOList.add(offlieJrnReqDTO);
                    break;
                default:
                    break;
            }
        }
        //全部类型需要再添加
        JrnReqDTO allJrnReqDTO = new JrnReqDTO();
        BeanUtils.copyProperties(allJrnReqDTO, jrnReqDTO);
        allJrnReqDTO.setPayTyp(Constants.PAY_TYP_ALL);
        allJrnReqDTO.setTxAmt(orderDO.getTotalAmt());
        jrnReqDTOList.add(allJrnReqDTO);

        GenericRspDTO<Map<String, String>> rspDto = riskCheckClient.batchCheck(jrnReqDTOList);
        if (JudgeUtils.isNotSuccess(rspDto.getMsgCd())) {
            Map<String, String> jrnRspMap = rspDto.getBody();
            logger.error("订单" + orderDO.getOrderNo() + "实时风控检查失败:" + rspDto.getMsgCd());
            String throwErrCode = null;
            if (JudgeUtils.isNotNull(jrnRspMap) && JudgeUtils.isNotEmpty(jrnRspMap)) {
                for (Map.Entry<String, String> entry : jrnRspMap.entrySet()) {
                    if (JudgeUtils.isNull(throwErrCode)) {
                        throwErrCode = entry.getValue();
                    }
                    logger.error(entry.getKey() + "--->" + entry.getValue());
                }
            }
            if (JudgeUtils.isNotNull(throwErrCode)) {
                logger.error("抛出异常：{}", throwErrCode);
                LemonException.throwBusinessException(throwErrCode);
            } else {
                LemonException.throwBusinessException(rspDto.getMsgCd());
            }
        }

    }

    protected void checkPayPassword(String userId, String password, boolean check, String validateRandom, String seaRandom) {
        if (check) {
            if (StringUtils.isBlank(password)) {
                LemonException.throwBusinessException("CSH20076");
            }


            GenericRspDTO rspDTO = null;
            if (StringUtils.isBlank(userId)) {
                userId = LemonUtils.getUserId();
            }
            if (seaRandom != null && !"".equals(seaRandom)) {
//				CheckPayPwdSeaDTO checkPayPwdSeaDTO =  new CheckPayPwdSeaDTO();
//				checkPayPwdSeaDTO.setUserId(userId);
//				checkPayPwdSeaDTO.setPayPwdRandom(validateRandom);
//				checkPayPwdSeaDTO.setPayPwd(password);
//				checkPayPwdSeaDTO.setSeaRandom(seaRandom);
                GenericDTO ckPwdDto = new GenericDTO();
                //			ckPwdDto.setBody(checkPayPwdSeaDTO);
                rspDTO = userAuthenticationClient.checkPayPasswordSea(ckPwdDto);
            } else {
                CheckPayPwdDTO checkPayPwdDTO = new CheckPayPwdDTO();
//				checkPayPwdDTO.setUserId(userId);
//				checkPayPwdDTO.setPayPwdRandom(validateRandom);
                checkPayPwdDTO.setPayPwd(password);
                GenericDTO ckPwdDto = new GenericDTO();
                ckPwdDto.setBody(checkPayPwdDTO);
                rspDTO = userAuthenticationClient.checkPayPwd(ckPwdDto);
            }


            if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
                logger.error("验证支付密码失败：" + rspDTO.getMsgCd());
                String msgCd = rspDTO.getMsgCd().substring(0, 3);
                if (JudgeUtils.equals(msgCd, "SYS")) {
                    LemonException.throwBusinessException("CSH20103");
                } else {
                    LemonException.throwBusinessException(rspDTO.getMsgCd());
                }

            }
        }
    }

    protected PayJrnDO saveJrn(OrderDO dbOrderDO, BigDecimal balAmt, int crdPayType, BigDecimal crdAmt,
                               String couponType, BigDecimal couponAmt, String couponNo, String ccy) {
        PayJrnDO payJrnDO = new PayJrnDO();
        payJrnDO.setBalAmt(balAmt);
        payJrnDO.setCouponAmt(couponAmt);
        payJrnDO.setCouponType(couponType);
        if (!StringUtils.equals(couponType, CouponType.H_COUPON.getType())
                && !StringUtils.equals(couponType, CouponType.NONE.getType())) {
            payJrnDO.setCouponNo(couponNo);
        } else {
            payJrnDO.setCouponNo(null);
        }
        payJrnDO.setInvAmt(new BigDecimal(0));
        payJrnDO.setCrdPayAmt(crdAmt);
        payJrnDO.setCrdPayType(crdPayType);
        payJrnDO.setPayJrnNo(LemonUtils.getRequestId());
        payJrnDO.setPayOrderNo(dbOrderDO.getOrderNo());
        payJrnDO.setJrnStatus(CshConstants.JRN_STS_U);
        payJrnDO.setBusOrderNo(dbOrderDO.getBusOrderNo());

        payJrnDO.setTxType(dbOrderDO.getTxType());
        payJrnDO.setOrderAmt(dbOrderDO.getOrderAmt());
        payJrnDO.setFndOrderNo("");
        payJrnDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
        payJrnDO.setPayOrdTm(payJrnDO.getTxTm());
        if (StringUtils.isBlank(dbOrderDO.getPayerId())) {
            payJrnDO.setUserId(LemonUtils.getUserId());
        } else {
            payJrnDO.setUserId(dbOrderDO.getPayerId());
        }

        if (StringUtils.isBlank(ccy)) {
            payJrnDO.setCcy("USD");
        } else {
            payJrnDO.setCcy(ccy);
        }
        //填充同步更新订单时补款方式
        dbOrderDO.setCrdPayType(String.valueOf(crdPayType));

        if (StringUtils.equals(OrderStatus.PRE_PAY.getValue(), dbOrderDO.getOrderStatus())) {
            logger.debug("仅初始化流水:{}", dbOrderDO.getOrderNo());
            orderTransactionalService.initJrn(null, payJrnDO);
        } else {
            logger.debug("初始化流水且同步账单:{}", dbOrderDO.getOrderNo());
            OrderDO updOrderDO1 = new OrderDO();
            updOrderDO1.setOrderNo(dbOrderDO.getOrderNo());
            updOrderDO1.setOrderStatus(OrderStatus.PRE_PAY.getValue());
            updOrderDO1.setCapCorg(dbOrderDO.getCapCorg());
            updOrderDO1.setCapCardType(dbOrderDO.getCapCardType());
            boolean payerIdExist = true;
            if (JudgeUtils.isBlank(dbOrderDO.getPayerId())) {
                payerIdExist = false;
            }
            updOrderDO1.setPayeeId(dbOrderDO.getPayeeId());
            updOrderDO1.setJrnTxTm(DateTimeUtils.getCurrentLocalDateTime());
            orderTransactionalService.initJrn(updOrderDO1, payJrnDO);
            prepareNotifyOrder(dbOrderDO, updOrderDO1);
            orderCommonComponent.synchronizeBil(updOrderDO1, OrderCommonComponent.UPD_BIL, payerIdExist, null, null);
        }
        return payJrnDO;
    }

    protected OrderDO prepareNotifyOrder(OrderDO dbOrder, OrderDO updateOrder) {
        logger.debug("========================  业务通知准备数据  ========================");
        updateOrder.setTxType(dbOrder.getTxType());
        logger.debug("交易类型：" + updateOrder.getTxType());
        updateOrder.setBusType(dbOrder.getBusType());
        logger.debug("业务类型：" + updateOrder.getBusType());
        updateOrder.setTotalAmt(dbOrder.getTotalAmt());
        logger.debug("支付总金额：" + updateOrder.getTotalAmt());
        updateOrder.setOrderAmt(dbOrder.getOrderAmt());
        logger.debug("订单金额：" + updateOrder.getOrderAmt());
        updateOrder.setCcy(dbOrder.getCcy());
        logger.debug("订单币种：" + updateOrder.getCcy());

        String payerId = dbOrder.getPayerId();
		/*if(JudgeUtils.isBlank(payerId) && (JudgeUtils.equals(updateOrder.getTxType(),"02"))){
			payerId = LemonUtils.getUserId();
		}*/
        updateOrder.setPayerId(payerId);
        logger.debug("付款方ID：" + updateOrder.getPayerId());
        updateOrder.setPayType(dbOrder.getPayType());
        logger.debug("支付方式：" + updateOrder.getPayType());
        updateOrder.setBusOrderNo(dbOrder.getBusOrderNo());
        logger.debug("业务订单号：" + updateOrder.getBusOrderNo());
        if (JudgeUtils.isNull(updateOrder.getFee())) {
            updateOrder.setFee(dbOrder.getFee());
        }
        logger.debug("手续费：" + updateOrder.getFee());
        updateOrder.setOrderTm(dbOrder.getOrderTm());
        logger.debug("下单时间：" + updateOrder.getOrderTm());

        if (JudgeUtils.isNull(updateOrder.getBalAmt())) {
            updateOrder.setBalAmt(dbOrder.getBalAmt());
        }
        logger.debug("余额账户支付金额：" + updateOrder.getBalAmt());

        String couponType = updateOrder.getCouponType();
        if (JudgeUtils.isBlank(couponType)) {
            updateOrder.setCouponType(dbOrder.getCouponType());
        }
        logger.debug("优惠类型：" + updateOrder.getCouponType());

        BigDecimal couponAmt = updateOrder.getCouponAmt();
        if (JudgeUtils.isNull(couponAmt)) {
            updateOrder.setCouponAmt(dbOrder.getCouponAmt());
        }
        logger.debug("优惠金额：" + updateOrder.getCouponAmt());
        if (JudgeUtils.isNull(updateOrder.getCrdPayType())) {
            updateOrder.setCrdPayType(dbOrder.getCrdPayType());
        }
        logger.debug("补款类型：" + updateOrder.getCrdPayType());
        if (JudgeUtils.isNull(updateOrder.getCrdPayAmt())) {
            updateOrder.setCrdPayAmt(dbOrder.getCrdPayAmt());
        }
        logger.debug("补款金额：" + updateOrder.getCrdPayAmt());

        if (JudgeUtils.isNull(updateOrder.getCapCorg())) {
            updateOrder.setCapCorg(dbOrder.getCapCorg());
        }
        logger.debug("资金机构：" + updateOrder.getCapCorg());

        if (JudgeUtils.isNull(updateOrder.getCapCorgNo())) {
            updateOrder.setCapCorgNo(dbOrder.getCapCorgNo());
        }
        logger.debug("网银资金合作机构：" + updateOrder.getCapCorgNo());

        if (JudgeUtils.isNull(updateOrder.getLast4CardNo())) {
            updateOrder.setLast4CardNo(dbOrder.getLast4CardNo());
        }
        logger.debug("卡后四位：" + updateOrder.getLast4CardNo());

        if (JudgeUtils.isNull(updateOrder.getPayMod())) {
            updateOrder.setPayMod(dbOrder.getPayMod());
        }
        logger.debug("支付方式payMod：" + updateOrder.getPayMod());

        if (JudgeUtils.isNull(updateOrder.getMercName())) {
            updateOrder.setMercName(dbOrder.getMercName());
        }

        if (JudgeUtils.isNull(updateOrder.getPayeeId())) {
            updateOrder.setPayeeId(dbOrder.getPayeeId());
        }
        logger.debug("商户名称：" + updateOrder.getMercName());

        if (JudgeUtils.isNull(updateOrder.getRemark())) {
            updateOrder.setRemark(dbOrder.getRemark());
        }
        logger.debug("订单备注：" + updateOrder.getRemark());

        logger.debug("========================业务通知准备数据 end========================");
        return updateOrder;
    }


    protected void caculateFee(OrderDO orderDO) {
        GenericRspDTO<TradeFeeCaculateRspDTO> rspDTO = acmComponent.caculateFee(orderDO.getBusType(), orderDO.getOrderAmt(), orderDO.getCcy());
        TradeFeeCaculateRspDTO tradeFeeCaculateRspDTO = rspDTO.getBody();
        orderDO.setTotalAmt(tradeFeeCaculateRspDTO.getTradeToalAmt());
        orderDO.setLeftTotalAmt(tradeFeeCaculateRspDTO.getTradeToalAmt());
        orderDO.setFee(tradeFeeCaculateRspDTO.getTradeFee());
        orderDO.setLeftFee(tradeFeeCaculateRspDTO.getTradeFee());
        String calculateMode = tradeFeeCaculateRspDTO.getCalculateMode();
        if (StringUtils.equals(calculateMode, "internal")) {
            orderDO.setFeeFlag("IN");
        } else if (StringUtils.equals(calculateMode, "external")) {
            orderDO.setFeeFlag("EX");
        }
    }

    //账务与营销处理
    protected void handleAccAndMkm(OrderDO orderDO, PayJrnDO payJrnDO, String fkAcNo) {
        String dataOrder = ObjectMapperHelper.writeValueAsString(objectMapper, orderDO, true);
        logger.debug("=======================账务和营销处理 params print start=======================");
        logger.debug("orderDO::" + dataOrder);
        String dataJrn = ObjectMapperHelper.writeValueAsString(objectMapper, payJrnDO, true);
        logger.debug("payJrnDO::" + dataJrn);
        logger.debug("=======================账务和营销处理 params print over=======================");
        List<AccountingReqDTO> accList = createAccQueue(orderDO, payJrnDO, fkAcNo);

        BigDecimal dAmt = BigDecimal.ZERO;
        BigDecimal cAmt = BigDecimal.ZERO;
        for (AccountingReqDTO dto : accList) {
            if (JudgeUtils.isNotNull(dto)) {
                if (JudgeUtils.equals(dto.getDcFlg(), ACMConstants.AC_D_FLG)) {
                    dAmt = dAmt.add(dto.getTxAmt());
                } else {
                    cAmt = cAmt.add(dto.getTxAmt());
                }
            }
        }

        // 借贷平衡校验
        if (cAmt.compareTo(dAmt) != 0) {
            LemonException.throwBusinessException("CSH20052");
        }

        //消费营销权
        acmComponent.useCoupon(orderDO.getOrderNo(), payJrnDO.getCouponType(), payJrnDO.getCouponNo(),
                orderDO.getTotalAmt(), payJrnDO.getUserId(), orderDO.getPayeeId(), payJrnDO.getCouponAmt());

        GenericDTO<List<AccountingReqDTO>> userAccDto = new GenericDTO<>();
        userAccDto.setBody(accList);
        //生成科目收支明细
        GenericRspDTO<NoBody> accountingTreatment = accountingTreatmentClient.accountingTreatment(userAccDto);
        if (!JudgeUtils.isSuccess(accountingTreatment.getMsgCd())) {
            logger.error("订单" + orderDO.getOrderNo() + "账务失败：" + accountingTreatment.getMsgCd());
            paymentHandler.cancelMkmCoupon(orderDO.getOrderNo(), orderDO.getOrderAmt(), orderDO.getCouponType());
            LemonException.throwBusinessException(accountingTreatment.getMsgCd());
        }

        if (StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
                || StringUtils.equals(orderDO.getTxType(), TradeType.PAYMENT.getType())) {
            //登记商户手续费
            paymentHandler.registMerChantFee(orderDO);
        }
        //登记用户手续费
        if (orderDO.getFee().compareTo(BigDecimal.ZERO) > 0) {
            paymentHandler.registUserFee(orderDO);
        }

    }

    // 创建账务处理列表,具体业务实现
    protected List<AccountingReqDTO> createAccQueue(OrderDO orderDO, PayJrnDO payJrnDO, String fkAcNo) {
        String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
//        String balAcNo = acmComponent.getAcmAcNo(orderDO.getPayerId(), balCapType, orderDO.getCcy());
        String balAcNo = fkAcNo;
        BigDecimal fee = orderDO.getFee();
        BigDecimal amountSum = orderDO.getTotalAmt();
        List acList = new ArrayList<>();
        //判断使用余额支付还是快捷
        if (payJrnDO.getCrdPayAmt() != null && payJrnDO.getCrdPayAmt().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal tamCrdPayAmt = payJrnDO.getCrdPayAmt();
            //借：应收账款-渠道充值-XX银行/支付宝/微信
            AccountingReqDTO cnlRechargeBnkReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
                    ACMConstants.ACCOUNTING_NOMARL, tamCrdPayAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
                    AcItem.I_CNL_BANK.getValue(), null, null, null, null, "转账  使用快捷支付");
            acList.add(cnlRechargeBnkReqDTO);
            //贷：其他应付款-暂收-收银台
            AccountingReqDTO userAccountReqDTO1 = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
                    ACMConstants.ACCOUNTING_NOMARL, tamCrdPayAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
                    AcItem.O_CSH.getValue(), null, null, null, null, "转账  使用快捷支付");
            acList.add(userAccountReqDTO1);
        } else {
            // 借：其他应付款-支付账户-现金账户 102
            AccountingReqDTO cshItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                    payJrnDO.getPayJrnNo(), orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo,
                    ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_D_FLG, "USDT".equals(orderDO.getCcy()) ?
                            TamConstants.USDT_AC_ITEM_CSH_PAY : TamConstants.USDC_AC_ITEM_CSH_PAY, null, null,
                    null, null, "转账  使用余额支付");
            acList.add(cshItemReqDTO);
            // 贷：其他应付款-暂收-收银台 102
            AccountingReqDTO userAccountReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(),
                    payJrnDO.getPayJrnNo(), orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, null,
                    ACMConstants.ITM_AC_TYP, balCapType,
                    ACMConstants.AC_C_FLG, "USDT".equals(orderDO.getCcy()) ? TamConstants.USDT_AC_ITEM_CSH_PAY :
                            TamConstants.USDC_AC_ITEM_CSH_PAY, balAcNo, null,
                    null, null, "转账  使用余额支付");
            acList.add(userAccountReqDTO);
        }

        return acList;
    }


    protected void checkBeforeInitOrder(OrderDO orderDO) {
        logger.debug("-------------初始化订单业务检查-------------。订单号 [" + orderDO.getOrderNo() + "]");
        String payerId = Optional.ofNullable(orderDO.getPayerId()).orElse(LemonUtils.getUserId());
        riskUserStatus(orderDO.getTxType(), payerId, "01");
        if (StringUtils.equals(orderDO.getBusType(), "01") || StringUtils.equals(orderDO.getBusType(), "03")) {
            riskUserStatus(orderDO.getTxType(), orderDO.getPayeeId(), "01");
        }
        logger.debug("-------------初始化订单业务检查完成----------。订单号 [" + orderDO.getOrderNo() + "]");
    }

    protected void riskUserStatus(String txType, String userId, String idType) {
        if (StringUtils.isBlank(userId)) {
            return;
        }
        RiskCheckUserStatusReqDTO riskCheckUserStatusReqDTO = new RiskCheckUserStatusReqDTO();
        riskCheckUserStatusReqDTO.setTxTyp(txType);
        riskCheckUserStatusReqDTO.setId(userId);
        riskCheckUserStatusReqDTO.setIdTyp(idType);
        GenericRspDTO rspDTO = riskCheckClient.checkUserStatus(riskCheckUserStatusReqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            logger.error("风控用户或商户 {} 状态检查失败:{}", userId, rspDTO.getMsgCd());
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
    }
}
