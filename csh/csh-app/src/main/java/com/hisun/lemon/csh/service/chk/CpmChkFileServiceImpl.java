package com.hisun.lemon.csh.service.chk;

import com.hisun.lemon.csh.enums.OrderStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.csh.constants.CshConstants;

@Transactional
@Service
public class CpmChkFileServiceImpl extends AbstractChkFileService {

    public CpmChkFileServiceImpl() {
        super();
        this.appCnl="CPM";
        this.chkOrderStatus=new String[]{
                OrderStatus.SUCC.getValue(),
                OrderStatus.REFUND.getValue(),
                OrderStatus.PART_REFUND.getValue()
        };
        this.chkTxTypes= new String[]{ "08" };
        this.lockName="CSH_CPM_CHK_FILE_LOCK";
    }
}
