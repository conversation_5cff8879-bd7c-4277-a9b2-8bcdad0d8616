package com.hisun.lemon.csh.service.impl;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dto.payment.AbstractNormalPaymentDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.csh.enums.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.onr.client.NotifySendClient;
import com.hisun.lemon.onr.dto.NotifySendReqDTO;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class OnrOrderServiceImpl extends AbstractOrderService {
	private static final Logger logger = LoggerFactory.getLogger(OnrOrderServiceImpl.class);

	protected String txType= TradeType.CONSUME.getType();
	protected String[] busTypes=new String[]{
			BussinessType.CONSUME_BAR.getValue(),
			BussinessType.CONSUME_SCAN.getValue(),
			BussinessType.CONSUME_APP.getValue(),
			BussinessType.CONSUME_POS.getValue(),
			BussinessType.CONSUME_CARD.getValue()
	};


	@Resource
	private NotifySendClient notifySendClient;

	public boolean match(String txType){
		return JudgeUtils.equals(this.txType,txType);
	}
	@Override
	protected void checkBeforeInitOrder(OrderDO orderDO){

		logger.debug("-------------初始化订单业务检查-------------。订单号 ["+orderDO.getOrderNo()+"]");
        if(StringUtils.isBlank(orderDO.getPayeeId())){
			LemonException.throwBusinessException("CSH20004");
		}

		String payerId= Optional.ofNullable(orderDO.getPayerId()).orElse(LemonUtils.getUserId());

		riskUserStatus(orderDO.getTxType(),payerId,"01");
		riskUserStatus(orderDO.getTxType(),orderDO.getPayeeId(),"01");

		logger.debug("-------------初始化订单业务检查完成----------。订单号 ["+orderDO.getOrderNo()+"]");
	}

	protected void caculateFee(OrderDO orderDO){
		logger.debug("-------------开始计算用户手续费-------------。订单号["+orderDO.getOrderNo()+"]");
		GenericRspDTO<TradeFeeCaculateRspDTO> rspDTO= acmComponent.caculateFee(orderDO.getBusType(),
				orderDO.getOrderAmt(),orderDO.getCcy());
		TradeFeeCaculateRspDTO tradeFeeCaculateRspDTO=rspDTO.getBody();
		orderDO.setTotalAmt(tradeFeeCaculateRspDTO.getTradeToalAmt());
		orderDO.setLeftTotalAmt(tradeFeeCaculateRspDTO.getTradeToalAmt());
		orderDO.setFee(tradeFeeCaculateRspDTO.getTradeFee());
		orderDO.setLeftFee(tradeFeeCaculateRspDTO.getTradeFee());
		String calculateMode= tradeFeeCaculateRspDTO.getCalculateMode();
		if(StringUtils.equals(calculateMode, "internal")){
			orderDO.setFeeFlag("IN");
		}else if(StringUtils.equals(calculateMode,"external")){
			orderDO.setFeeFlag("EX");
		}
		logger.debug("-------------计算用户手续费完成-------------。订单号["+orderDO.getOrderNo()+"]");
	}

	@Override
	protected void checkJrnWithBussiness(GenericDTO inputGenericDto){
		 Object body=inputGenericDto.getBody();
		 if(body instanceof AbstractNormalPaymentDTO){
			 AbstractNormalPaymentDTO inputBody=(AbstractNormalPaymentDTO)body;
			 String couponNo=inputBody.getCouponNo();
			 String couponType=inputBody.getCouponType();
			 BigDecimal couponAmt=inputBody.getCouponAmt();

			 if(JudgeUtils.isNotNull(couponType) && JudgeUtils.isNull(couponAmt)){
				 throw  new LemonException("CSH20100");
			 }

			 if(JudgeUtils.isBlank(couponNo)
					 && JudgeUtils.equalsAny(couponType, CouponType.E_COUPON.getType(),CouponType.D_COUPON.getType())){
				 throw  new LemonException("CSH20101");
			 }

		 }
		 return;

	}


	//创建账务处理列表,具体业务实现
	protected List<AccountingReqDTO> createAccQueue(OrderDO orderDO,PayJrnDO payJrnDO){

		String balCapType= CapTypEnum.CAP_TYP_CASH.getCapTyp();
		String userId = LemonUtils.getUserId();
		if(JudgeUtils.isBlank(userId)){
			userId = orderDO.getPayerId();
			logger.debug("消费付款方id:" + userId);
		}
		String balAcNo=null;
		if(JudgeUtils.isNotBlank(userId)){
			balAcNo = acmComponent.getAcmAcNo(userId, balCapType);
			logger.debug("查询用户Id:" + userId +",账户号："+balAcNo);
		}
		List acList=new ArrayList<>();

		//优惠账务处理
		if(StringUtils.isNotEmpty(payJrnDO.getCouponType())&& payJrnDO.getCouponAmt().compareTo(BigDecimal.ZERO)>0){
			BigDecimal acCouponAmt=null;
			if(StringUtils.equals(CouponType.E_COUPON.getType(), payJrnDO.getCouponType())
					||StringUtils.equals(CouponType.D_COUPON.getType(), payJrnDO.getCouponType())){
				acCouponAmt=payJrnDO.getCouponAmt();
				//核销电子券

				//电子券账务
				//借：应收账款-电子券发放-XX公司
				AccountingReqDTO eCouponCoReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL, acCouponAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
						AcItem.I_E_COUPON_COMPANY.getValue(), null, null, null, null, null);

				//贷：其他应付款-暂收-收银台
				AccountingReqDTO e_cshItemReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL, acCouponAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
						AcItem.O_CSH.getValue(), null, null, null, null, null);

				acList.add(eCouponCoReqDTO);
				acList.add(e_cshItemReqDTO);

			}else if(StringUtils.equals(CouponType.H_COUPON.getType(), payJrnDO.getCouponType())){
				acCouponAmt=BigDecimal.valueOf(CshConstants.H_USD_RATE).multiply(payJrnDO.getCouponAmt()).setScale(2);
				//核销海币

				//海币账务
				//借：其他应收款-中转挂账-海币
				AccountingReqDTO h_midOnAccountHReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL, acCouponAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
						AcItem.O_H_COUPON_MID.getValue(), null, null, null, null, null);

				//贷：其他应付款-暂收-收银台
				AccountingReqDTO h_cshItemReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
						ACMConstants.ACCOUNTING_NOMARL, acCouponAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
						AcItem.O_CSH.getValue(), null, null, null, null, null);

				acList.add(h_midOnAccountHReqDTO);
				acList.add(h_cshItemReqDTO);

			}else{
				LemonException.throwBusinessException("CSH20051");
			}

		}

		//补款
		if(payJrnDO.getCrdPayAmt()!=null && payJrnDO.getCrdPayAmt().compareTo(BigDecimal.ZERO)>0){
			BigDecimal onrCrdPayAmt = payJrnDO.getCrdPayAmt();
			//借：应收账款-渠道充值-XX银行/支付宝/微信
			AccountingReqDTO cnlRechargeBnkReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, onrCrdPayAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
					AcItem.I_CNL_BANK.getValue(), null, null, null, null, null);

			//贷：其他应付款-暂收-收银台
			AccountingReqDTO userAccountReqDTO1=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, onrCrdPayAmt, balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
					AcItem.O_CSH.getValue(), null, null, null, null, null);

			acList.add(cnlRechargeBnkReqDTO);
			acList.add(userAccountReqDTO1);
		}


		//理财暂时不做，留坑待填

		//用户现金账户
		if(payJrnDO.getBalAmt()!=null && payJrnDO.getBalAmt().compareTo(BigDecimal.ZERO)>0){
			//借：其他应付款-支付账户-现金账户
			AccountingReqDTO u_cshItemReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, payJrnDO.getBalAmt(), balAcNo, ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_D_FLG,
					AcItem.O_BAL.getValue(), null, null, null, null, null);

			//贷：其他应付款-暂收-收银台
			AccountingReqDTO u_userAccountReqDTO=acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), payJrnDO.getPayJrnNo(), orderDO.getTxType(),
					ACMConstants.ACCOUNTING_NOMARL, payJrnDO.getBalAmt(), balAcNo, ACMConstants.ITM_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
					AcItem.O_CSH.getValue(), null, null, null, null, null);

			acList.add(u_cshItemReqDTO);
			acList.add(u_userAccountReqDTO);
		}

		return acList;
	}

	@Override
	protected void notifyBussiness(OrderDO orderDO,PayJrnDO payJrn){
		logger.debug("-------------订单成功，通知收单模块-------------。订单号["+orderDO.getOrderNo()+"]");
		NotifySendReqDTO notifySendReqDTO=new NotifySendReqDTO();
		notifySendReqDTO.setOnrOrderNo(orderDO.getBusOrderNo());
		notifySendReqDTO.setFee(orderDO.getFee());
		notifySendReqDTO.setOrderSts(OrderStatus.SUCC.getValue());
		notifySendReqDTO.setPayDt(DateTimeUtils.getCurrentLocalDate());
		notifySendReqDTO.setPayTm(DateTimeUtils.getCurrentLocalTime());
		
		String couponType = orderDO.getCouponType();
		notifySendReqDTO.setCouponType(couponType);
		notifySendReqDTO.setCouponAmt(orderDO.getCouponAmt());
		notifySendReqDTO.setOrderAmt(orderDO.getTotalAmt());
		
		BigDecimal cshAmt = BigDecimal.valueOf(0.0);
		if (!StringUtils.equals(orderDO.getCrdPayType(), CshConstants.CRD_TYPE_NONE)
				&& JudgeUtils.isNotEmpty(orderDO.getCrdPayType())) {
			cshAmt=orderDO.getCrdPayAmt();
		}else{
			cshAmt=orderDO.getBalAmt();
		}
		notifySendReqDTO.setPayAmt(cshAmt);
		notifySendReqDTO.setPayUserId(payJrn.getUserId());
		GenericDTO genericDTO=new GenericDTO();
		genericDTO.setBody(notifySendReqDTO);
		/*
		 * 捕获同步通知，防止通知失败影响后续账单同步
		 */
		try{
			GenericRspDTO<NoBody> rspDTO = notifySendClient.notifyMerchant(genericDTO);
			if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
				logger.error("收银台通知收单支付结果失败:" + rspDTO.getMsgCd());
			}else{
				logger.info("------------订单成功，通知收单模块完成----------。订单号["+orderDO.getOrderNo()+"]");
			}
		}catch (Exception e){
			logger.error("收银台通知收单支付结果异常：" + e.getMessage());
		}
	} 

}
