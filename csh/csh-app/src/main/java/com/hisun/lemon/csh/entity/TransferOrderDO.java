package com.hisun.lemon.csh.entity;/*
 * @ClassName TransferOrderDO
 * @Description
 * @version 1.0
 * @Date 2017-07-13 17:39:12
 */

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class TransferOrderDO extends BaseDO {
	/**
	 * 页数
	 */
	private Integer pageNum;
	/**
	 * 每页大小
	 */
	private Integer pageSize;
	/**
	 * @Fields orderNo 转账订单编号
	 */
	private String orderNo;
	/**
	 * @Fields userId 用户编号
	 */
	private String userId;
	/**
	 * @Fields crossUserId 对方内部号
	 */
	private String crossUserId;
	/**
	 * @Fields mblNo 转账账户
	 */
	private String mblNo;
	/**
	 * @Fields mblNoHid 脱敏手机号
	 */
	private String mblNoHid;
	/**
	 * @Fields busType 业务类型
	 */
	private String busType;
	/**
	 * @Fields txType 交易类型
	 */
	private String txType;
	/**
	 * @Fields orderCcy 币种
	 */
	private String orderCcy;
	/**
	 * @Fields amount 交易金额
	 */
	private BigDecimal amount;

	/**
	 * @Fields amount 实际交易金额
	 */
	private BigDecimal actualTradeAmt;
	/**
	 * @Fields fee 服务费
	 */
	private BigDecimal fee;
	/**
	 * @Fields remark 备注
	 */
	private String remark;
	/**
	 * @Fields sysChannel 渠道
	 */
	private String sysChannel;
	/**
	 * @Fields gatherName 收款人姓名
	 */
	private String gatherName;
	/**
	 * @Fields orderSts U:初始  S:成功  F：失败
	 */
	private String orderSts;
	/**
	 * @Fields acTm 会计日期
	 */
	private LocalDate acTm;
	/**
	 * @Fields txTm 交易时间
	 */
	private LocalDateTime txTm;
	/**
	 * @Fields crdAcTyp 卡种，D借记卡，C贷记卡
	 */
	private String crdAcTyp;
	/**
	 * @Fields crdCorpOrg 资金机构
	 */
	private String crdCorpOrg;
	/**
	 * @Fields corpOrgSnm 合作资金机构名称
	 */
	private String corpOrgSnm;
	/**
	 * @Fields capCrdNm 银行卡户名
	 */
	private String capCrdNm;
	/**
	 * @Fields crdNoEnc 加密银行卡号
	 */
	private String crdNoEnc;

	/**
	 * @Fields capCrdNo 银行卡号
	 */
	private String capCrdNo;

	/**
	 * @Fields busOrderBo 收银订单号
	 */
	private String busOrderNo;

	/**
	 * @Fields lastCapCrdNo 银行卡后四位
	 */
	private String lastCapCrdNo;

	/**
	 * @Fields lastMblNo 手机号的后四位
	 */
	private String lastMblNo;
	/**
	 * @Fields goodDesc 描述
	 */
	private String goodDesc;

	/**
	 * @Fields areaDesc 区号
	 */
	private String areaDesc;

	/**
	 * @Fields nationName 国家名称
	 */
	private String nationName;

	/**
	 * @Fields payerName 付款方名字
	 */
	private String payerName;
	/**
	 * @Fields payeMblNo 付款方手机号
	 */
	private String payeMblNo;

	/**
	 * @Fields capCorgNm   支行名称
	 */
	private String capCorgNm;

	/**
	 * @Fields 外部订单号
	 */
	private String extOrderNo;

	/**
	 * @Fields 付款方账号
	 */
	private String fkAcNo;

	/**
	 * @Fields 收款方账号
	 */
	private String skAcNo;

	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	public String getOrderNo() {
		return orderNo;
	}

	public String getBusOrderNo() {
		return busOrderNo;
	}
	public void setBusOrderNo(String busOrderNo) {
		this.busOrderNo = busOrderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public String getUserId() {
		return userId;
	}
	public String getLastCapCrdNo() {
		return lastCapCrdNo;
	}
	public String getLastMblNo() {
		return lastMblNo;
	}
	public void setLastMblNo(String lastMblNo) {
		this.lastMblNo = lastMblNo;
	}
	public void setLastCapCrdNo(String lastCapCrdNo) {
		this.lastCapCrdNo = lastCapCrdNo;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getCapCorgNm() {
		return capCorgNm;
	}
	public void setCapCorgNm(String capCorgNm) {
		this.capCorgNm = capCorgNm;
	}
	public String getCrossUserId() {
		return crossUserId;
	}
	public void setCrossUserId(String crossUserId) {
		this.crossUserId = crossUserId;
	}
	public String getMblNo() {
		return mblNo;
	}
	public String getPayerName() {
		return payerName;
	}
	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}
	public String getPayeMblNo() {
		return payeMblNo;
	}
	public void setPayeMblNo(String payeMblNo) {
		this.payeMblNo = payeMblNo;
	}
	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}
	public String getBusType() {
		return busType;
	}
	public void setBusType(String busType) {
		this.busType = busType;
	}

	public String getTxType() {
		return txType;
	}
	public void setTxType(String txType) {
		this.txType = txType;
	}
	public String getAreaDesc() {
		return areaDesc;
	}
	public void setAreaDesc(String areaDesc) {
		this.areaDesc = areaDesc;
	}
	public String getOrderCcy() {
		return orderCcy;
	}
	public String getNationName() {
		return nationName;
	}
	public void setNationName(String nationName) {
		this.nationName = nationName;
	}
	public String getGoodDesc() {
		return goodDesc;
	}
	public void setGoodDesc(String goodDesc) {
		this.goodDesc = goodDesc;
	}
	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public BigDecimal getFee() {
		return fee;
	}
	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getGatherName() {
		return gatherName;
	}
	public void setGatherName(String gatherName) {
		this.gatherName = gatherName;
	}
	public String getOrderSts() {
		return orderSts;
	}
	public void setOrderSts(String orderSts) {
		this.orderSts = orderSts;
	}
	public LocalDate getAcTm() {
		return acTm;
	}
	public void setAcTm(LocalDate acTm) {
		this.acTm = acTm;
	}
	public LocalDateTime getTxTm() {
		return txTm;
	}
	public void setTxTm(LocalDateTime txTm) {
		this.txTm = txTm;
	}
	public String getCrdAcTyp() {
		return crdAcTyp;
	}
	public void setCrdAcTyp(String crdAcTyp) {
		this.crdAcTyp = crdAcTyp;
	}
	public String getCrdCorpOrg() {
		return crdCorpOrg;
	}

	public void setCrdCorpOrg(String crdCorpOrg) {
		this.crdCorpOrg = crdCorpOrg;
	}
	public String getCorpOrgSnm() {
		return corpOrgSnm;
	}
	public void setCorpOrgSnm(String corpOrgSnm) {
		this.corpOrgSnm = corpOrgSnm;
	}
	public String getCapCrdNm() {
		return capCrdNm;
	}
	public void setCapCrdNm(String capCrdNm) {
		this.capCrdNm = capCrdNm;
	}
	public String getCrdNoEnc() {
		return crdNoEnc;
	}
	public String getMblNoHid() {
		return mblNoHid;
	}
	public void setMblNoHid(String mblNoHid) {
		this.mblNoHid = mblNoHid;
	}
	public void setCrdNoEnc(String crdNoEnc) {
		this.crdNoEnc = crdNoEnc;
	}
	public String getCapCrdNo() {
		return capCrdNo;
	}
	public void setCapCrdNo(String capCrdNo) {
		this.capCrdNo = capCrdNo;
	}
	public String getSysChannel() {
		return sysChannel;
	}
	public void setSysChannel(String sysChannel) {
		this.sysChannel = sysChannel;
	}

	public String getExtOrderNo() {
		return extOrderNo;
	}

	public void setExtOrderNo(String extOrderNo) {
		this.extOrderNo = extOrderNo;
	}

	public BigDecimal getActualTradeAmt() {
		return actualTradeAmt;
	}

	public void setActualTradeAmt(BigDecimal actualTradeAmt) {
		this.actualTradeAmt = actualTradeAmt;
	}

	public String getFkAcNo() {
		return fkAcNo;
	}

	public void setFkAcNo(String fkAcNo) {
		this.fkAcNo = fkAcNo;
	}

	public String getSkAcNo() {
		return skAcNo;
	}

	public void setSkAcNo(String skAcNo) {
		this.skAcNo = skAcNo;
	}
}