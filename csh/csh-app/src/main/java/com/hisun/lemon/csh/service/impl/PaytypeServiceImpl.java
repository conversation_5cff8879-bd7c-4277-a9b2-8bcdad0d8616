package com.hisun.lemon.csh.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.csh.component.QuickPaymentComponent;
import com.hisun.lemon.csh.dto.paytype.AppPaytypeResultDTO;
import com.hisun.lemon.csh.dto.paytype.PaytypeDTO;
import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dao.IPaytypeDao;
import com.hisun.lemon.csh.entity.PaytypeDO;
import com.hisun.lemon.csh.service.IPaytypeService;

@Transactional
@Service 
public class PaytypeServiceImpl extends BaseService implements IPaytypeService {

	@Resource
	IPaytypeDao dao;

	@Resource
	QuickPaymentComponent quickPaymentComponent;
	@Override
	public void add(PaytypeDTO paytypeDto) {
		PaytypeDO paytypeFind=new PaytypeDO();
		paytypeFind.setBusType(paytypeDto.getBusType()); 
		paytypeFind.setMercId(paytypeDto.getMercId());
		paytypeFind.setAppCnl(paytypeDto.getAppCnl());
		List<PaytypeDO> payTypes = dao.find(paytypeFind);
		
		if(!JudgeUtils.isNull(payTypes) && !payTypes.isEmpty()){
			throw new LemonException("CSH20011");
		} 
		  
		PaytypeDO paytypeDO=new PaytypeDO();
		BeanUtils.copyProperties(paytypeDO, paytypeDto); 
		paytypeDO.setStatus(CshConstants.YES);
		dao.insert(paytypeDO);
	}

	@Override
	public void del(Integer id) {
		PaytypeDO payTpe = this.dao.get(id);
		if(payTpe==null){
			throw new LemonException("CSH20012");
		} 
		if(StringUtils.equals(CshConstants.NO, payTpe.getStatus())){
			return;
		}
		
		PaytypeDO paytypeDO =new PaytypeDO();
		paytypeDO.setId(id);
		paytypeDO.setStatus(CshConstants.NO);
		dao.update(paytypeDO);
	}

	@Override
	public void update(PaytypeDTO paytypeDto) {
		PaytypeDO paytypeDO=new PaytypeDO();
		paytypeDO.setId(paytypeDto.getId());
		paytypeDO.setStatus(paytypeDto.getStatus());
		paytypeDO.setPayTypes(paytypeDto.getPayTypes());
		paytypeDO.setGwPayTypes(paytypeDto.getGwPayTypes());
		dao.update(paytypeDO);
	}

	@Override
	public List<PaytypeDO> list(PaytypeDTO paytypeDto) {
		PaytypeDO paytypeDO=new PaytypeDO();
		BeanUtils.copyProperties(paytypeDO, paytypeDto);
		return dao.find(paytypeDO);
	}

	@Override
	public PaytypeDO getPaytypes(PaytypeDO paytypeDO) {
		//根据给定条件查询
		paytypeDO.setStatus(CshConstants.YES);
		if(StringUtils.isBlank(paytypeDO.getMercId())){
			//商户号为空，使用通配符商户号查
			paytypeDO.setMercId(CshConstants.MATCH_ALL);
		}
		List<PaytypeDO> paytypes=dao.find(paytypeDO);

		if(paytypes==null || paytypes.isEmpty()){
			//商户号不为空，则去掉商户号再查
             if(StringUtils.isNotEmpty(paytypeDO.getMercId())
					 && !StringUtils.equals(CshConstants.MATCH_ALL,paytypeDO.getMercId())){
				 paytypeDO.setMercId(CshConstants.MATCH_ALL);
				 paytypes=dao.find(paytypeDO);
			 }
			 //去掉支付应用再查
			 if(paytypes==null || paytypes.isEmpty()){
				 paytypeDO.setAppCnl(CshConstants.MATCH_ALL);
				 paytypeDO.setMercId(CshConstants.MATCH_ALL);
				 paytypes=dao.find(paytypeDO);
			 }
		}

		//未找到支付方式，报错
		if(paytypes==null || paytypes.isEmpty()){
			throw new LemonException("CSH20012");
		}
		return paytypes.get(0);
	}

	@Override
	public String getPaytypes(PaytypeDO paytypeDo,String busPayType){
		PaytypeDO queryPaytypeDo= getPaytypes(paytypeDo);
		String queryPaytypes=queryPaytypeDo.getPayTypes();
		if(StringUtils.isEmpty(queryPaytypes)){
			throw  new LemonException("CSH20016");
		}

		//业务指定了支付方式
		if(StringUtils.isNotEmpty(busPayType)){
			//取业务传入和后台配置的最小长度和最大长度
			int length=busPayType.length()<=queryPaytypes.length()?busPayType.length():queryPaytypes.length();
			int maxLength=busPayType.length()<=queryPaytypes.length()?queryPaytypes.length():busPayType.length();
			StringBuilder buf=new StringBuilder();
			for(int i=0;i<length;i++){
				String bitBusPayType=busPayType.substring(i, i+1);
				String bitConfigPayType=queryPaytypes.substring(i, i+1);
				if(StringUtils.equals(CshConstants.YES, bitBusPayType)
						&& StringUtils.equals(CshConstants.YES, bitConfigPayType)){
					buf.append(CshConstants.YES);
				}else{
					buf.append(CshConstants.NO);
				}
			}
			String newPaytype=buf.toString();
			if(length!=maxLength){
				queryPaytypes=StringUtils.rightPad(newPaytype,maxLength,CshConstants.NO);
			}
		}
		return queryPaytypes;
	}

	public AppPaytypeResultDTO getPayInfo(PaytypeDO paytypeDo,String busPaytypes){
		String payTypes=getPaytypes(paytypeDo,busPaytypes);
		Map<String,List<AgrInfoRspDTO.CardAgrInfo>> qpCards=quickPaymentComponent.queryBindCards();
		AppPaytypeResultDTO appPaytypeResultDTO=new AppPaytypeResultDTO();
		//支持借记卡快捷支付
		if(supportDQP(payTypes)){
			List<AgrInfoRspDTO.CardAgrInfo> dQpCards= qpCards.get(quickPaymentComponent.D_CARD);
			appPaytypeResultDTO.setdCards(dQpCards);
		}
		//支持贷记卡快捷支付
		if(supportCQP(payTypes)){
			List<AgrInfoRspDTO.CardAgrInfo> cQpCards= qpCards.get(quickPaymentComponent.C_CARD);
			appPaytypeResultDTO.setcCards(cQpCards);
		}
		appPaytypeResultDTO.setPayType(payTypes);
		return appPaytypeResultDTO;
	}

	/**
	 * 支持借记卡快捷
	 *
	 * @param payType
	 * @return
	 */
	@Override
	public boolean supportDQP(String payType) {
		return supportBit(payType,4);
	}

	/**
	 * 支持贷记卡快捷
	 *
	 * @param payType
	 * @return
	 */
	@Override
	public boolean supportCQP(String payType) {
		return supportBit(payType,5);
	}

	/**
	 * 支持余额
	 *
	 * @param payType
	 * @return
	 */
	@Override
	public boolean supportBal(String payType) {
		return supportBit(payType,2);
	}

	/**
	 * 检查指定位置是否支持
	 *
	 * @param payType
	 * @param index
	 * @return
	 */
	@Override
	public boolean supportBit(String payType, int index) {
		if((payType.length()-1) <index){
			return false;
		}
		return StringUtils.equals(payType.substring(index,index+1),CshConstants.YES);
	}

	public IPaytypeDao getDao() {
		return dao;
	}

	public void setDao(IPaytypeDao dao) {
		this.dao = dao;
	}
	
}
