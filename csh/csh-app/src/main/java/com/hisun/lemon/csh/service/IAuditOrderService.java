package com.hisun.lemon.csh.service;

import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.*;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.framework.data.GenericDTO;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2017年6月7日
 * @time 下午2:13:58
 *
 */
public interface IAuditOrderService {

    /**
     * 获取收银台转账订单信息列表
     * @param initCashierDTO 查询条件
     * @return
     */
    List<OrderDO> queryCashierTranOrders(TranOrderReqDTO initCashierDTO);

    /**
     * 转账订单审核
     * @param initCashierDTO 查询条件
     * @return
     */
    void audit(TranOrderAuditDTO initCashierDTO);


    /**
     * 数币链上转账成功
     * @param req
     */
    void handleCregisOk(GenericDTO<HandleFinanceDTO> req);

    /**
     * 数币提现成功
     * @param req
     */
    void handleWithdrawOk(GenericDTO<HandleFinanceDTO> req);
}
