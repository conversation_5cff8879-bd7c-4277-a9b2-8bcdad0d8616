<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csh.dao.IPayJrnDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.csh.entity.PayJrnDO" >
        <id column="pay_jrn_no" property="payJrnNo" jdbcType="VARCHAR" />
        <result column="pay_order_no" property="payOrderNo" jdbcType="VARCHAR" />
        <result column="tx_tm" property="txTm" jdbcType="TIMESTAMP" />
        <result column="tx_type" property="txType" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="pay_ord_tm" property="payOrdTm" jdbcType="TIMESTAMP" />
        <result column="bus_order_no" property="busOrderNo" jdbcType="VARCHAR" />
        <result column="fnd_order_no" property="fndOrderNo" jdbcType="VARCHAR" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="bal_amt" property="balAmt" jdbcType="DECIMAL" />
        <result column="inv_amt" property="invAmt" jdbcType="DECIMAL" />
        <result column="coupon_amt" property="couponAmt" jdbcType="DECIMAL" />
        <result column="coupon_type" property="couponType" jdbcType="VARCHAR" />
        <result column="coupon_no" property="couponNo" jdbcType="VARCHAR" />
        <result column="crd_pay_amt" property="crdPayAmt" jdbcType="DECIMAL" />
        <result column="crd_pay_type" property="crdPayType" jdbcType="INTEGER" />
        <result column="jrn_status" property="jrnStatus" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        pay_jrn_no, pay_order_no, tx_tm, tx_type, user_id, pay_ord_tm, bus_order_no, fnd_order_no, 
        order_amt, bal_amt, inv_amt, coupon_amt, coupon_type, crd_pay_amt, crd_pay_type, coupon_no,
        jrn_status, create_time, modify_time,ccy
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from csh_pay_jrn
        where pay_jrn_no = #{payJrnNo,jdbcType=VARCHAR}
    </select>


    <select id="getByPayOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from csh_pay_jrn
        where pay_order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csh_pay_jrn
        where pay_jrn_no = #{payJrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csh.entity.PayJrnDO" >
        insert into csh_pay_jrn
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="payJrnNo != null" >
                pay_jrn_no,
            </if>
            <if test="payOrderNo != null" >
                pay_order_no,
            </if>
            <if test="txTm != null" >
                tx_tm,
            </if>
            <if test="txType != null" >
                tx_type,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="payOrdTm != null" >
                pay_ord_tm,
            </if>
            <if test="busOrderNo != null" >
                bus_order_no,
            </if>
            <if test="fndOrderNo != null" >
                fnd_order_no,
            </if>
            <if test="ccy != null" >
                ccy,
            </if>
            <if test="orderAmt != null" >
                order_amt,
            </if>
            <if test="balAmt != null" >
                bal_amt,
            </if>
            <if test="invAmt != null" >
                inv_amt,
            </if>
            <if test="couponAmt != null" >
                coupon_amt,
            </if>
            <if test="couponType != null" >
                coupon_type,
            </if>
            <if test="couponNo != null" >
                coupon_no,
            </if>

            <if test="crdPayAmt != null" >
                crd_pay_amt,
            </if>
            <if test="crdPayType != null" >
                crd_pay_type,
            </if>
            <if test="jrnStatus != null" >
                jrn_status,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="payJrnNo != null" >
                #{payJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="payOrderNo != null" >
                #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="txTm != null" >
                #{txTm,jdbcType=TIMESTAMP},
            </if>
            <if test="txType != null" >
                #{txType,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="payOrdTm != null" >
                #{payOrdTm,jdbcType=TIMESTAMP},
            </if>
            <if test="busOrderNo != null" >
                #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="fndOrderNo != null" >
                #{fndOrderNo,jdbcType=VARCHAR},
            </if>

            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>

            <if test="orderAmt != null" >
                #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="balAmt != null" >
                #{balAmt,jdbcType=DECIMAL},
            </if>
            <if test="invAmt != null" >
                #{invAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponAmt != null" >
                #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponType != null" >
                #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="couponNo != null" >
                #{couponNo,jdbcType=VARCHAR},
            </if>

            <if test="crdPayAmt != null" >
                #{crdPayAmt,jdbcType=DECIMAL},
            </if>
            <if test="crdPayType != null" >
                #{crdPayType,jdbcType=INTEGER},
            </if>
            <if test="jrnStatus != null" >
                #{jrnStatus,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csh.entity.PayJrnDO" >
        update csh_pay_jrn
        <set >
            <if test="payOrderNo != null" >
                pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="txTm != null" >
                tx_tm = #{txTm,jdbcType=TIMESTAMP},
            </if>
            <if test="txType != null" >
                tx_type = #{txType,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="payOrdTm != null" >
                pay_ord_tm = #{payOrdTm,jdbcType=TIMESTAMP},
            </if>
            <if test="busOrderNo != null" >
                bus_order_no = #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="fndOrderNo != null" >
                fnd_order_no = #{fndOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                ccy = #{ccy,jdbcType=VARCHAR},
            </if>

            <if test="orderAmt != null" >
                order_amt = #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="balAmt != null" >
                bal_amt = #{balAmt,jdbcType=DECIMAL},
            </if>
            <if test="invAmt != null" >
                inv_amt = #{invAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponAmt != null" >
                coupon_amt = #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponType != null" >
                coupon_type = #{couponType,jdbcType=VARCHAR},
            </if>

            <if test="couponNo != null" >
                coupon_no = #{couponNo,jdbcType=VARCHAR},
            </if>

            <if test="crdPayAmt != null" >
                crd_pay_amt = #{crdPayAmt,jdbcType=DECIMAL},
            </if>
            <if test="crdPayType != null" >
                crd_pay_type = #{crdPayType,jdbcType=INTEGER},
            </if>
            <if test="jrnStatus != null" >
                jrn_status = #{jrnStatus,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where pay_jrn_no = #{payJrnNo,jdbcType=VARCHAR}
    </update>

    <select id="getJrnByOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from csh_pay_jrn
        where pay_order_no = #{orderNo,jdbcType=VARCHAR}
    </select>
</mapper>