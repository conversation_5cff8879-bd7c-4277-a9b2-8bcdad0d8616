<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csh.dao.IRefundOrderDao" >
    <resultMap id="BaseResultMap" type="com.hisun.lemon.csh.entity.RefundOrderDO" >
        <id column="rfd_ord_no" property="rfdOrdNo" jdbcType="VARCHAR" />
        <result column="bus_rdf_ord_no" property="busRdfOrdNo" jdbcType="VARCHAR" />
        <result column="orgin_order_no" property="orginOrderNo" jdbcType="VARCHAR" />
        <result column="fnd_rfd_order_no" property="fndRfdOrderNo" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="merc_id" property="mercId" jdbcType="VARCHAR" />
        <result column="merc_name" property="mercName" jdbcType="VARCHAR" />
        <result column="order_status" property="orderStatus" jdbcType="CHAR" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="rfd_amt" property="rfdAmt" jdbcType="DECIMAL" />
        <result column="rfd_user_amt" property="rfdUserAmt" jdbcType="DECIMAL" />
        <result column="good_info" property="goodInfo" jdbcType="VARCHAR" />
        <result column="rfd_reason" property="rfdReason" jdbcType="VARCHAR" />
        <result column="coupon_amt" property="couponAmt" jdbcType="DECIMAL" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="rfd_type" property="rfdType" jdbcType="VARCHAR" />
        <result column="tx_type" property="txType" jdbcType="VARCHAR" />
        <result column="coupon_type" property="couponType" jdbcType="VARCHAR" />
        <result column="order_ccy" property="orderCcy" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="ac_tm" property="acTm" jdbcType="DATE" />
        <result column="tx_tm" property="txTm" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        rfd_ord_no, bus_rdf_ord_no, orgin_order_no, fnd_rfd_order_no, user_id, merc_id,merc_name,order_status, 
        fee, rfd_amt, rfd_user_amt, rfd_reason, coupon_amt,good_info, bus_type, rfd_type, tx_type, coupon_type, 
        order_ccy, create_time, modify_time, ac_tm, tx_tm
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from csh_refund_order
        where rfd_ord_no = #{rfdOrdNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csh_refund_order
        where rfd_ord_no = #{rfdOrdNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csh.entity.RefundOrderDO" >
        insert into csh_refund_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="rfdOrdNo != null" >
                rfd_ord_no,
            </if>
            <if test="busRdfOrdNo != null" >
                bus_rdf_ord_no,
            </if>
            <if test="orginOrderNo != null" >
                orgin_order_no,
            </if>
            <if test="fndRfdOrderNo != null" >
                fnd_rfd_order_no,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="mercId != null" >
                merc_id,
            </if>
            <if test="mercName != null" >
                merc_name,
            </if>
            <if test="goodInfo != null" >
                good_info,
            </if>
            <if test="orderStatus != null" >
                order_status,
            </if>
            <if test="fee != null" >
                fee,
            </if>
            <if test="rfdAmt != null" >
                rfd_amt,
            </if>
            <if test="rfdUserAmt != null" >
                rfd_user_amt,
            </if>
            <if test="rfdReason != null" >
                rfd_reason,
            </if>
            <if test="couponAmt != null" >
                coupon_amt,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="rfdType != null" >
                rfd_type,
            </if>
            <if test="txType != null" >
                tx_type,
            </if>
            <if test="couponType != null" >
                coupon_type,
            </if>
            <if test="orderCcy != null" >
                order_ccy,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="acTm != null" >
                ac_tm,
            </if>
            <if test="txTm != null" >
                tx_tm,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="rfdOrdNo != null" >
                #{rfdOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="busRdfOrdNo != null" >
                #{busRdfOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="orginOrderNo != null" >
                #{orginOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="fndRfdOrderNo != null" >
                #{fndRfdOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mercId != null" >
                #{mercId,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="goodInfo != null" >
                #{goodInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null" >
                #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="fee != null" >
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="rfdAmt != null" >
                #{rfdAmt,jdbcType=DECIMAL},
            </if>
            <if test="rfdUserAmt != null" >
                #{rfdUserAmt,jdbcType=DECIMAL},
            </if>
            <if test="rfdReason != null" >
                #{rfdReason,jdbcType=VARCHAR},
            </if>
            <if test="couponAmt != null" >
                #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="rfdType != null" >
                #{rfdType,jdbcType=VARCHAR},
            </if>
            <if test="txType != null" >
                #{txType,jdbcType=VARCHAR},
            </if>
            <if test="couponType != null" >
                #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="orderCcy != null" >
                #{orderCcy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acTm != null" >
                #{acTm,jdbcType=DATE},
            </if>
            <if test="txTm != null" >
                #{txTm,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csh.entity.RefundOrderDO" >
        update csh_refund_order
        <set >
            <if test="busRdfOrdNo != null" >
                bus_rdf_ord_no = #{busRdfOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="orginOrderNo != null" >
                orgin_order_no = #{orginOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="fndRfdOrderNo != null" >
                fnd_rfd_order_no = #{fndRfdOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mercId != null" >
                merc_id = #{mercId,jdbcType=VARCHAR},
            </if>
            <if test="goodInfo != null" >
                good_info = #{goodInfo,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                merc_name = #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null" >
                order_status = #{orderStatus,jdbcType=CHAR},
            </if>
            <if test="fee != null" >
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="rfdAmt != null" >
                rfd_amt = #{rfdAmt,jdbcType=DECIMAL},
            </if>
            <if test="rfdUserAmt != null" >
                rfd_user_amt = #{rfdUserAmt,jdbcType=DECIMAL},
            </if>
            <if test="rfdReason != null" >
                rfd_reason = #{rfdReason,jdbcType=VARCHAR},
            </if>
            <if test="couponAmt != null" >
                coupon_amt = #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="rfdType != null" >
                rfd_type = #{rfdType,jdbcType=VARCHAR},
            </if>
            <if test="txType != null" >
                tx_type = #{txType,jdbcType=VARCHAR},
            </if>
            <if test="couponType != null" >
                coupon_type = #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="orderCcy != null" >
                order_ccy = #{orderCcy,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acTm != null" >
                ac_tm = #{acTm,jdbcType=DATE},
            </if>
            <if test="txTm != null" >
                tx_tm = #{txTm,jdbcType=TIMESTAMP},
            </if>
        </set>
        where rfd_ord_no = #{rfdOrdNo,jdbcType=VARCHAR}
    </update>

    <select id="queryList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_refund_order
        <where>
            <if test="acTm != null">
                <![CDATA[and ac_tm = #{acTm}]]>
            </if>

            <if test="statusList != null">
                and order_status in
                <foreach item="item" index="index" collection="statusList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="busTypeList != null">
                and bus_type in
                <foreach item="item" index="index" collection="busTypeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryCpiOrder" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_refund_order
        <where>
            rfd_type='01' and rfd_user_amt>0
            <if test="acTm != null">
                <![CDATA[and ac_tm = #{acTm}]]>
            </if>

            <if test="statusList != null">
                and order_status in
                <foreach item="item" index="index" collection="statusList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            
        </where>
    </select>
</mapper>