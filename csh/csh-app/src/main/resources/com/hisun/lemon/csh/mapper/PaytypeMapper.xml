<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.csh.dao.IPaytypeDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.csh.entity.PaytypeDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="merc_id" property="mercId" jdbcType="VARCHAR" />
        <result column="app_cnl" property="appCnl" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="CHAR" />
        <result column="pay_types" property="payTypes" jdbcType="VARCHAR" />
        <result column="gw_pay_types" property="gwPayTypes" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="CHAR" />
        <result column="modify_opr" property="modifyOpr" jdbcType="VARCHAR" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" /> 
    </resultMap>

    <sql id="Base_Column_List" >
        id, merc_id,app_cnl, bus_type, pay_types, gw_pay_types, status, modify_opr, modify_time,
        create_time 
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from csh_paytype
        where id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from csh_paytype
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.csh.entity.PaytypeDO" >
        insert into csh_paytype
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="mercId != null" >
                merc_id,
            </if>

            <if test="appCnl != null" >
                app_cnl,
            </if>

            <if test="busType != null" >
                bus_type,
            </if>
            <if test="payTypes != null" >
                pay_types,
            </if>
            <if test="gwPayTypes != null" >
                gw_pay_types,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="modifyOpr != null" >
                modify_opr,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if> 
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="mercId != null" >
                #{mercId,jdbcType=VARCHAR},
            </if>

            <if test="appCnl != null" >
                #{appCnl,jdbcType=VARCHAR},
            </if>

            <if test="busType != null" >
                #{busType,jdbcType=CHAR},
            </if>
            <if test="payTypes != null" >
                #{payTypes,jdbcType=VARCHAR},
            </if>
            <if test="gwPayTypes != null" >
                #{gwPayTypes,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=CHAR},
            </if>
            <if test="modifyOpr != null" >
                #{modifyOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if> 
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.csh.entity.PaytypeDO" >
        update csh_paytype
        <set >
            <if test="payTypes != null" >
                pay_types = #{payTypes,jdbcType=VARCHAR},
            </if>
            <if test="gwPayTypes != null" >
                gw_pay_types = #{gwPayTypes,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=CHAR},
            </if>
            <if test="modifyOpr != null" >
                modify_opr = #{modifyOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if> 
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <select id="find" resultMap="BaseResultMap" parameterType="com.hisun.lemon.csh.entity.PaytypeDO" >
        select 
        <include refid="Base_Column_List" />
        from csh_paytype
        <where>
            <if test="mercId != null" >
                and merc_id = #{mercId,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=CHAR} 
            </if>
            <if test="appCnl != null" >
                and app_cnl = #{appCnl,jdbcType=CHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=CHAR} 
            </if> 
        </where>
    </select>
</mapper>