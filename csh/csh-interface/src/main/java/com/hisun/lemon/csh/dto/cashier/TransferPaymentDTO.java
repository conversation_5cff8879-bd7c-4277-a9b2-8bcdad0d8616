package com.hisun.lemon.csh.dto.cashier;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商户转账给用户支付请求对象
 *
 * <AUTHOR>
 * @date 2017年11月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("商户转账到用户")
public class TransferPaymentDTO {

    /**
     * @Fields 转账商户用户号
     */
    @ApiModelProperty(name = "merchantId", value = "商户号")
    @NotEmpty(message = "CSH10035")
    private String merchantId;

    /**
     * 模块业务订单号
     */
    @ApiModelProperty(name = "busOrderNo", value = "模块业务订单号")
    @NotEmpty(message="CSH10006")
    @Length(max =25)
    private String busOrderNo;

    /**
     * @Fields busType 业务类型 用户 0301 银行卡 0302 面对面 0303   商户转给用户 0304
     */
    @ApiModelProperty(name = "busType", value = "业务类型   用户 0301   银行卡  0302  面对面  0303  0304 商户转给用户")
    @NotEmpty(message = "CSH10032")
    @Length(max = 4)
    private String busType;

    /**
     * @Fields mblNo 转账账户
     */
    @ApiModelProperty(name = "mblNo", value = "转账收款用户账户手机号")
    @NotEmpty(message = "CSH10020")
    @Length(max = 20)
    private String mblNo;

    /**
     * 订单金额
     */
    @ApiModelProperty(name = "orderAmt", value = "订单金额")
    @Min(value=0, message="CSH10007")
    private BigDecimal orderAmt;

    @ApiModelProperty(name = "cashAmt", value = "支付金额")
    private BigDecimal cashAmt;

    /**
     * @Fields orderCcy 币种
     */
    @ApiModelProperty(name = "orderCcy", value = "币种")
    @Length(max = 4)
    private String orderCcy;

    /**
     * @Fields remark 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    @Length(max = 25)
    private String remark;

    @ApiModelProperty(name = "effTm", value = "订单失效时间")
    private LocalDateTime effTm;

    /**
     * 交易类型
     */
    @ApiModelProperty(name = "txType", value = "交易类型")
    @NotEmpty(message="CSH10008")
    @Length(max =2)
    private String txType;

    /**
     * 业务指定支付方式
     */
    @ApiModelProperty(name = "busPaytype", value = "业务指定支付方式")
    @Length(max =60)
    private String busPaytype;
    @ApiModelProperty(name = "goodsDesc", value = "订单商品描述")
    private String goodsDesc;

    /**
     * 订单系统渠道（来源：WEB APP）
     */
    @ApiModelProperty(name = "sysChannel", value = "订单系统渠道（来源：WEB APP）")
    @NotEmpty(message="CSH10010")
    @Length(max =5)
    private String sysChannel;

    /**
     * 付款方id
     */
    @Length(max =20)
    private String payerId;

    @ApiModelProperty(name = "payeeName", value = "付款方名称")
    private String payerName;
    /**
     * 收款方id
     */
    @Length(max =20)
    private String payeeId;

    @ApiModelProperty(name = "payeeName", value = "收款方名称")
    @Length(max =200)
    private String payeeName;

    @NotEmpty(message="CSH10021")
    private String appCnl;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getOrderCcy() {
        return orderCcy;
    }

    public void setOrderCcy(String orderCcy) {
        this.orderCcy = orderCcy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getEffTm() {
        return effTm;
    }

    public void setEffTm(LocalDateTime effTm) {
        this.effTm = effTm;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public String getBusPaytype() {
        return busPaytype;
    }

    public void setBusPaytype(String busPaytype) {
        this.busPaytype = busPaytype;
    }

    public String getGoodsDesc() {
        return goodsDesc;
    }

    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }

    public String getSysChannel() {
        return sysChannel;
    }

    public void setSysChannel(String sysChannel) {
        this.sysChannel = sysChannel;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public String getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(String payeeId) {
        this.payeeId = payeeId;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getAppCnl() {
        return appCnl;
    }

    public void setAppCnl(String appCnl) {
        this.appCnl = appCnl;
    }

    public BigDecimal getCashAmt() {
        return cashAmt;
    }

    public void setCashAmt(BigDecimal cashAmt) {
        this.cashAmt = cashAmt;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

}
