package com.hisun.lemon.csh.client;

import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.*;
import com.hisun.lemon.csh.dto.order.ErrorOrderDTO;
import com.hisun.lemon.csh.dto.order.ExchangeCoinDTO;
import com.hisun.lemon.csh.dto.order.NotifyResultDTO;
import com.hisun.lemon.csh.dto.order.OrderDTO;
import com.hisun.lemon.csh.dto.payment.*;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 收银台  收银服务接口
 * <AUTHOR>
 * @date 2017年6月27日
 * @time 下午3:06:23
 *
 */
@FeignClient("CSH")
public interface CshOrderClient {
	/**
	 * 业务模块调用收银，初始化收银订单
	 * @param genericCashierDTO
	 * @return
	 */
    @PostMapping("/csh/order/cashier")
    public GenericRspDTO<CashierViewDTO> initCashier(@Validated @RequestBody GenericDTO<InitCashierDTO> genericCashierDTO);

	/**
	 * 扫码后台直付下单
	 * @param genericDTO
	 * @return
	 */
	@PostMapping("/csh/order/backstage")
	public GenericRspDTO<BackstageViewDTO> initBackstage(@Validated @RequestBody GenericDTO<InitBackstageDTO> genericDTO);

	/**
	 * 后台直付(下单+支付)
	 * @param directPaymentDTO
	 * @return
	 */
	@PostMapping("/csh/order/bal/direct")
	public GenericRspDTO<PaymentResultDTO> payByDirectBal(@Validated @RequestBody GenericDTO<DirectPaymentDTO> directPaymentDTO);

	/**
	 * 后台直付(下单+支付)
	 * @param directPaymentDTO
	 * @return
	 */
	@PostMapping("/csh/order/bal/directNew")
	public GenericRspDTO<PaymentResultDTO> NewPayByDirectBal(@Validated @RequestBody GenericDTO<DirectPaymentDTO> directPaymentDTO);

	/**
	 *
	 * 数币转账下单
	 * @param directPaymentDTO
	 * @return
	 */
	@PostMapping("/csh/order/bal/directDm")
	public GenericRspDTO<PaymentResultDTO> directDm(@Validated @RequestBody GenericDTO<DirectPaymentDTO> directPaymentDTO);

	/**
	 * 数币提现
	 * @param directPaymentDTO
	 * @return
	 */
	@PostMapping(value = "/csh/order/bal/direct/dmWithdraw")
	public GenericRspDTO<PaymentResultDTO> directDmWithdraw(@Validated @RequestBody GenericDTO<DirectPaymentDTO> directPaymentDTO);

	/**
	 * 余额支付收银订单
	 * @param balPaymentDTO
	 * @return
	 */
	@PostMapping(value = "/csh/order/bal")
	public GenericRspDTO<PaymentResultDTO> payByBal(@Validated @RequestBody GenericDTO<BalPaymentDTO> balPaymentDTO);

	/**
	 * 线下汇款支付处理
	 * @param genericDTO
	 * @return
	 */
	@PostMapping(value = "/csh/order/offline")
	public GenericRspDTO<OfflinePaymentResultDTO> offlinePayment(@Validated @RequestBody GenericDTO<OfflinePaymentDTO> genericDTO);

	/**
	 * 根据外围业务订单号查询收银台订单详情
	 * @param orderNo
	 * @return
	 */
	@GetMapping(value = "/csh/order/detail/{orderNo}")
	public GenericRspDTO<OrderDTO> query(@PathVariable(value = "orderNo") String orderNo);

	/**
	 * 根据收银订单号查询收银台订单详情
	 * @param orderNo
	 * @return
	 */
	@GetMapping(value = "/csh/order/detail/inner/{orderNo}")
	public GenericRspDTO<OrderDTO> queryCashierOrder(@PathVariable(value = "orderNo") String orderNo);

	/**
	 * 第三方页面支付收银订单
	 * @param ppPaymentDTO
	 * @return
	 */
	@PatchMapping(value = "/csh/order/pp")
	public GenericRspDTO<PaymentResultDTO> payByPagePayment(@Validated @RequestBody GenericDTO<PpPaymentDTO> ppPaymentDTO);

	/**
	 * 第三方页面支付结果通知,资金能力回调通知收银订单结果
	 * @param notifyResultDTO
	 * @return
	 */
	@PatchMapping(value = "/csh/order/pp/result")
	public GenericRspDTO<NoBody> ppNotify(@Validated @RequestBody GenericDTO<NotifyResultDTO> notifyResultDTO);
	
	/**
	 * 修改订单状态
	 * @param orderNo
	 * @param orderStatus
	 * @return
	 */
	@GetMapping(value = "/csh/order/detail/{orderNo}/{orderStatus}")
	public  GenericRspDTO updateOrder(@PathVariable(value = "orderNo") String orderNo,@PathVariable(value = "orderStatus") String orderStatus);


	/**
	 * 资金能力对账补单撤单处理接口
	 * @param genericDTO
	 * @return
	 */
	@PatchMapping(value = "/csh/order/error/handler")
	public  GenericRspDTO<NoBody> handlerCheckErr(@Validated @RequestBody GenericDTO<ErrorOrderDTO> genericDTO);

	/**
	 * 商户转账用户处理接口
	 * @param genericDTO
	 * @return
	 */
	@PostMapping(value = "/csh/order/transfer/merchant")
	public GenericRspDTO<PaymentResultDTO> merchantTransferHandler(@Validated @RequestBody GenericDTO<TransferPaymentDTO> genericDTO);

	/**
	 * ACLEDA快捷支付处理接口
	 * @param genericDTO
	 * @return
	 */
	@PostMapping(value = "/csh/order/qp/acleda")
	public GenericRspDTO<AcledaPaymentResultDTO> payByAcledaQuicklyPayment(@Validated @RequestBody GenericDTO<AcledaQpPaymentDTO> genericDTO);

	/**
	 * ACLEDA银行转账下单和获取交易支付ID
	 * @param genericDTO
	 * @return
	 */
	@PostMapping(value = "/csh/order/transfer/acleda")
	public GenericRspDTO<AcledaTransferRspDTO> acledaTransferOpen(@Validated @RequestBody GenericDTO<AcledaTransferReqDTO> genericDTO);

	@PostMapping(value = "/csh/order/exchange/precheck")
	public GenericRspDTO<String> preCheckExchangeOrder(@Validated @RequestBody GenericDTO<ExchangeCoinDTO> req);

	@PostMapping(value = "/csh/order/exchange/complete")
	public GenericRspDTO<NoBody> completeExchangeOrder(@Validated @RequestBody GenericDTO<ExchangeCoinDTO> req);

	/**
	 * 数币转账账务处理
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/csh/order/handle/finance")
	GenericRspDTO<NoBody> handleFinance(@Validated @RequestBody GenericDTO<HandleFinanceDTO> req);

	/**
	 * 数币转账失败处理
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/csh/order/handle/dm/tran/fail")
	GenericRspDTO<NoBody> handleFailTransfer(@Validated @RequestBody GenericDTO<HandleFailTranDTO> req);

	/**
	 * 数币提现失败
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/csh/order/handle/dm/withdraw/fail")
	public GenericRspDTO<NoBody> handleFailWithdraw(@Validated @RequestBody GenericDTO<HandleFailTranDTO> req);

	/**
	 * 法币汇款充值订单结果处理
	 * @param genericCmdDTO
	 * @return
	 */
	@PostMapping(value = "/csh/order/fund/onMessageReceive")
	public GenericRspDTO<NoBody> onMessageReceive(@Validated @RequestBody GenericDTO<NotifyResultDTO> genericCmdDTO);
}
