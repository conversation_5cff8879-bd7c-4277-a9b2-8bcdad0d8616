package com.hisun.lemon.csh.dto.payment;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty; 

/**
 * 余额支付 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("余额支付")
@ClientValidated
public class BalPaymentDTO extends AbstractNormalPaymentDTO{
	@ApiModelProperty(name = "orderNo", value = "订单号")
    @NotEmpty(message="CSH10001")
	@Length(max =24)
    private String orderNo; 

    @ApiModelProperty(name = "balAmt", value = "账户金额")
    @Min(value=0, message="CSH10002")
	@Digits(integer=15,fraction=2,message="CSH10002")
    private BigDecimal balAmt; 

    @Length(max =20)
    private String payeeId; 
    @Length(max =20)
    private String payerId;
    @ApiModelProperty(name = "payPassword", value = "支付密码")
    @NotEmpty(message="CSH10012")
    private String payPassword;

	@ApiModelProperty(name = "validateRandom", value = "支付密码随机数")
	@NotEmpty(message="CSH10081")
	private String validateRandom;


	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public BigDecimal getBalAmt() {
		return balAmt;
	}

	public void setBalAmt(BigDecimal balAmt) {
		this.balAmt = balAmt;
	}

	public String getPayeeId() {
		return payeeId;
	}

	public void setPayeeId(String payeeId) {
		this.payeeId = payeeId;
	}

	public String getPayerId() {
		return payerId;
	}

	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}

	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}

	public String getValidateRandom() {
		return validateRandom;
	}

	public void setValidateRandom(String validateRandom) {
		this.validateRandom = validateRandom;
	}
}
