package com.hisun.lemon.csh.client;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hisun.lemon.csh.dto.refund.RefundOrderDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderRspDTO;
import com.hisun.lemon.csh.dto.refund.RefundOrderUndoDTO;
import com.hisun.lemon.csh.dto.refund.RefundResultOrderDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.csh.dto.refund.KillOrderRspDTO;

/**
 * 退款服务接口
 * <AUTHOR>
 * @date 2017年7月18日
 * @time 下午3:06:23
 *
 */
@FeignClient("CSH")
public interface CshRefundClient {
	/**
	 * 业务模块调用收银,退款结果处理
	 * @param refundDTO
	 * @return
	 */
    @PostMapping("/csh/refund/order")
    public GenericRspDTO<RefundOrderRspDTO> createBill(@Validated @RequestBody GenericDTO<RefundOrderDTO> refundDTO);
    
    
    /**
	 * 业务模块调用收银,撤销处理
	 * @param refundDTO
	 * @return
	 */
    @PostMapping("/csh/refund/order/undo")
    public GenericRspDTO<RefundOrderRspDTO> createRfdUndoBill(@Validated @RequestBody GenericDTO<RefundOrderUndoDTO> refundDTO);
    
    /**
	 * 撤销与退款结果处理
	 * @param refundDTO
	 * @return
	 */
    @PatchMapping("/csh/refund/status")
    public GenericRspDTO completeBill(@Validated @RequestBody GenericDTO<RefundResultOrderDTO> refundDTO);
    
    
    /**
	 * 补单    与CPI对账
	 * @param orderNo
	 * @return
	 */
	@PatchMapping("/csh/refund/order/cpi/kill")
	public GenericRspDTO<NoBody> killCpoOrder(@Validated @RequestBody GenericDTO<KillOrderRspDTO> killOrderRspDTO);
			
}
