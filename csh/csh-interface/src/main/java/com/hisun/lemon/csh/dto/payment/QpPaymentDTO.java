package com.hisun.lemon.csh.dto.payment;

import java.math.BigDecimal;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
 

/**
 * 快捷支付 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("快捷支付 传输对象")
public class QpPaymentDTO extends AbstractNormalPaymentDTO {
	@ApiModelProperty(name = "orderNo", value = "订单号")
    @NotEmpty(message="CSH10001")
	@Length(max =24)
    private String orderNo;
	@ApiModelProperty(name = "cardNo", value = "银行卡号")
    private String cardNo;
    @ApiModelProperty(name = "mblNo", value = "手机号")
    @NotEmpty(message="CSH10020")
    @Length(max =20)
    private String mblNo;

	/**
	 * 协议号
	 */
	@ApiModelProperty(name = "agrNo", value = "协议号", dataType = "String")
	private String agrNo;


	@ApiModelProperty(name = "crdPayAmt", value = "补款金额")
    @Min(value=0, message="CSH10014")
    private BigDecimal crdPayAmt;


	@ApiModelProperty(name = "smsFlag", value = "是否校验短信验证码 Y：是；N：否 ", required = true)
	@NotEmpty(message="CSH10027")
	@Pattern(regexp="Y|N",message="CSH10028")
	private String smsFlag;

	@Length(max =20)
    private String payeeId; 
	@Length(max =20)
    private String payerId;

	/**
	 * 持卡人用户名
	 */
    @ApiModelProperty(name = "crdUserNm", value = "持卡人用户名")
	private String crdUserNm;
	/**
	 * 预签约流水号
	 */
    @ApiModelProperty(name = "jrnNo", value = "预签约流水号")
	private String jrnNo;
	/**
	 * 短信验证码
	 */
    @ApiModelProperty(name = "chkNo", value = "短信验证码")
	private String chkNo;
	/**
	 * 短信流水号
	 */
    @ApiModelProperty(name = "smsJrnNo", value = "短信流水号")
	private String smsJrnNo;

	/**
	 * 卡种
	 */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种")
	private String crdAcTyp;
	/**
	 * 卡资金机构
	 */
    @ApiModelProperty(name = "crdCorpOrg", value = "卡资金机构")
	private String crdCorpOrg;

	@ApiModelProperty(name = "payPassword", value = "支付密码")
	private String payPassword;
	@ApiModelProperty(name = "needBind", value = "是否为直付(绑卡+支付)  0否  1是")
	@NotEmpty(message="CSH10036")
	private String needBind;  //是否为直付(绑卡+支付)

	@ApiModelProperty(name = "lastCardNo", value = "支付银行卡后四位")
	private String lastCardNo;

	@ApiModelProperty(name = "validateRandom", value = "支付密码随机数")
	private String validateRandom;

	@ApiModelProperty(name = "crdNoEnc", value = "银行卡加密卡号", dataType = "String")
	private String crdNoEnc;

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public BigDecimal getCrdPayAmt() {
		return crdPayAmt;
	}

	public void setCrdPayAmt(BigDecimal crdPayAmt) {
		this.crdPayAmt = crdPayAmt;
	}

	public String getSmsFlag() {
		return smsFlag;
	}

	public void setSmsFlag(String smsFlag) {
		this.smsFlag = smsFlag;
	}

	public String getPayeeId() {
		return payeeId;
	}

	public void setPayeeId(String payeeId) {
		this.payeeId = payeeId;
	}

	public String getPayerId() {
		return payerId;
	}

	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}

	public String getCrdUserNm() {
		return crdUserNm;
	}

	public void setCrdUserNm(String crdUserNm) {
		this.crdUserNm = crdUserNm;
	}

	public String getJrnNo() {
		return jrnNo;
	}

	public void setJrnNo(String jrnNo) {
		this.jrnNo = jrnNo;
	}

	public String getChkNo() {
		return chkNo;
	}

	public void setChkNo(String chkNo) {
		this.chkNo = chkNo;
	}

	public String getSmsJrnNo() {
		return smsJrnNo;
	}

	public void setSmsJrnNo(String smsJrnNo) {
		this.smsJrnNo = smsJrnNo;
	}

	public String getCrdAcTyp() {
		return crdAcTyp;
	}

	public void setCrdAcTyp(String crdAcTyp) {
		this.crdAcTyp = crdAcTyp;
	}

	public String getCrdCorpOrg() {
		return crdCorpOrg;
	}

	public void setCrdCorpOrg(String crdCorpOrg) {
		this.crdCorpOrg = crdCorpOrg;
	}

	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}

	public String getNeedBind() {
		return needBind;
	}

	public void setNeedBind(String needBind) {
		this.needBind = needBind;
	}

	public String getAgrNo() {
		return agrNo;
	}

	public void setAgrNo(String agrNo) {
		this.agrNo = agrNo;
	}

	public String getLastCardNo() {
		return lastCardNo;
	}

	public void setLastCardNo(String lastCardNo) {
		this.lastCardNo = lastCardNo;
	}

	public String getValidateRandom() {
		return validateRandom;
	}

	public void setValidateRandom(String validateRandom) {
		this.validateRandom = validateRandom;
	}

	public String getCrdNoEnc() {
		return crdNoEnc;
	}

	public void setCrdNoEnc(String crdNoEnc) {
		this.crdNoEnc = crdNoEnc;
	}
}
