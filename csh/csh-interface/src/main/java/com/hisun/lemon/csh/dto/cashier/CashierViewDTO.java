package com.hisun.lemon.csh.dto.cashier;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收银台 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
public class CashierViewDTO {
	private String orderNo;

	/**
	 * 账户余额
	 */
	@ApiModelProperty(name = "balAmt", value = "账户余额")
	private BigDecimal balAmt;
	/**
	 * 电子券
	 */
	@ApiModelProperty(name = "eCoupons", value = "电子券")
	private List<CouponItem> eCoupons;

	/**
	 * 折扣券
	 */
	@ApiModelProperty(name = "discountCoupons", value = "折扣券")
	private List<CouponItem> discountCoupons;

	/**
	 * 优惠券
	 */
	@ApiModelProperty(name = "cCoupons", value = "优惠券")
	private List<CouponItem> cCoupons;

	/**
	 * 海币余额
	 */
	@ApiModelProperty(name = "hCouponAmt", value = "海币余额")
	private BigDecimal hCouponAmt;

	@ApiModelProperty(name = "hCouponCashAmt", value = "使用海币的折现金额")
	private BigDecimal hCouponCashAmt;

	@ApiModelProperty(name = "hCouponUsedAmt", value = "使用海币的数值")
	private BigDecimal hCouponUsedAmt;

	/**
	 * 借记卡
	 */
	@ApiModelProperty(name = "qpDCards", value = "借记卡")
	private List<QpCardInfoDTO> qpDCards;
	/**
	 * 贷记卡
	 */
	@ApiModelProperty(name = "qpCCards", value = "贷记卡")
	private List<QpCardInfoDTO> qpCCards;

	/**
	 * 支付方式
	 */
	@ApiModelProperty(name = "payTypes", value = "支付方式")
	private String payTypes;


	/**
	 * 订单金额
	 */
	@ApiModelProperty(name = "orderAmt", value = "订单金额")
	private BigDecimal orderAmt;


	/**
	 * 手续费
	 */
	@ApiModelProperty(name = "feeAmt", value = "手续费")
	private BigDecimal feeAmt;

	/**
	 *商品描述
	 */
	@ApiModelProperty(name = "goodsDesc", value = "商品描述")
	private String goodsDesc;

	@ApiModelProperty(name = "quota", value = "配额方案")
	private QuotaDTO quota;

	@ApiModelProperty(name = "busType", value = "业务类型")
	private String busType;

	@ApiModelProperty(name = "feeFlag", value = "手续费扣费类型")
	private String feeFlag;

	@ApiModelProperty(name = "remark", value = "备注")
	private String remark;
	
    private String cashierUrl;

	public CashierViewDTO() {

	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public BigDecimal getBalAmt() {
		return balAmt;
	}

	public void setBalAmt(BigDecimal balAmt) {
		this.balAmt = balAmt;
	}

	public List<CouponItem> geteCoupons() {
		return eCoupons;
	}

	public void seteCoupons(List<CouponItem> eCoupons) {
		this.eCoupons = eCoupons;
	}

	public List<CouponItem> getDiscountCoupons() {
		return discountCoupons;
	}

	public void setDiscountCoupons(List<CouponItem> discountCoupons) {
		this.discountCoupons = discountCoupons;
	}

	public BigDecimal gethCouponAmt() {
		return hCouponAmt;
	}

	public void sethCouponAmt(BigDecimal hCouponAmt) {
		this.hCouponAmt = hCouponAmt;
	}

	public BigDecimal gethCouponCashAmt() {
		return hCouponCashAmt;
	}

	public void sethCouponCashAmt(BigDecimal hCouponCashAmt) {
		this.hCouponCashAmt = hCouponCashAmt;
	}

	public List<QpCardInfoDTO> getQpDCards() {
		return qpDCards;
	}

	public void setQpDCards(List<QpCardInfoDTO> qpDCards) {
		this.qpDCards = qpDCards;
	}

	public List<QpCardInfoDTO> getQpCCards() {
		return qpCCards;
	}

	public void setQpCCards(List<QpCardInfoDTO> qpCCards) {
		this.qpCCards = qpCCards;
	}

	public String getPayTypes() {
		return payTypes;
	}

	public void setPayTypes(String payTypes) {
		this.payTypes = payTypes;
	}

	public BigDecimal getOrderAmt() {
		return orderAmt;
	}

	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}

	public BigDecimal getFeeAmt() {
		return feeAmt;
	}

	public void setFeeAmt(BigDecimal feeAmt) {
		this.feeAmt = feeAmt;
	}

	public String getGoodsDesc() {
		return goodsDesc;
	}

	public void setGoodsDesc(String goodsDesc) {
		this.goodsDesc = goodsDesc;
	}

	public QuotaDTO getQuota() {
		return quota;
	}

	public void setQuota(QuotaDTO quota) {
		this.quota = quota;
	}

	public String getCashierUrl() {
		return cashierUrl;
	}

	public void setCashierUrl(String cashierUrl) {
		this.cashierUrl = cashierUrl;
	}

	public BigDecimal gethCouponUsedAmt() {
		return hCouponUsedAmt;
	}

	public void sethCouponUsedAmt(BigDecimal hCouponUsedAmt) {
		this.hCouponUsedAmt = hCouponUsedAmt;
	}

	public String getBusType() {
		return busType;
	}

	public void setBusType(String busType) {
		this.busType = busType;
	}

	public String getFeeFlag() {
		return feeFlag;
	}

	public void setFeeFlag(String feeFlag) {
		this.feeFlag = feeFlag;
	}

	public List<CouponItem> getcCoupons() {
		return cCoupons;
	}

	public void setcCoupons(List<CouponItem> cCoupons) {
		this.cCoupons = cCoupons;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
