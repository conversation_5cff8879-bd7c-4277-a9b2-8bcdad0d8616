package com.hisun.lemon.csh.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 数币转账账务处理请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/23 17:59
 */
@ClientValidated
@ApiModel(value = "HandleFinanceDTO", description = "数币转账账务处理请求DTO")
public class HandleFinanceDTO {

    @ApiModelProperty(name = "orderNo", value = "订单编号")
    @NotEmpty(message = "订单编号不能为空")
    private String orderNo;

    @ApiModelProperty(name = "busType", required = true, value = "业务类型 个人快捷充值:0101,个人线下充值:0102,个人营业厅充值:0103,企业网银充值:0104,条码消费:0201，" +
            "扫码付消费:0202,APP消费:0203,POS消费:0204,银行卡收单:0205 ,转到账户:0301,转银行卡:0302，当面收款:0303,兑换:0305,个人提现:0401,商户提现:0402,充海币:0501,充流量:0801,充话费:0802")
    @NotEmpty(message="CSH10009")
    private String busType;

    @ApiModelProperty(name = "txHash", value = "交易哈希")
    private String txHash;

    public String getTxHash() {
        return txHash;
    }

    public void setTxHash(String txHash) {
        this.txHash = txHash;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }
}
