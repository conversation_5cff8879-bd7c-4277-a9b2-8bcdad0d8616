package com.hisun.lemon.csh.dto.payment;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 线下汇款登记结果 传输对象
 * <AUTHOR>
 * @date 2017年8月10日
 * @time 上午9:27:30
 *
 */
@ApiModel("线下汇款登记结果 传输对象")
@ClientValidated
public class OfflinePaymentResultDTO {

	@ApiModelProperty(name = "orderNo", value = "充值订单号")
	@Length(max =28)
	private String orderNo;

	@ApiModelProperty(name = "orderNo", value = "收银订单号")
	@Length(max =24)
    private String cashierOrderNo;

	@ApiModelProperty(name = "orderNo", value = "汇款登记订单号")
	@Length(max =32)
	private String RemittOrderNo;

	@ApiModelProperty(name = "orderCcy", value = "币种")
	@Length(max =4)
	private String orderCcy;

	@ApiModelProperty(name = "orderStatus", value = "汇款登记订单状态")
	private String orderStatus;

    @ApiModelProperty(name = "orderAmt", value = "金额")
	@Min(value=0, message="CSH10007")
	@Digits(integer=15,fraction=2,message="CSH10003")
	private BigDecimal orderAmt;

	/**
	 * 充值用户或商户id
	 */
	@ApiModelProperty(name = "payerId", value = "充值用户或商户id")
	@Length(max = 16)
	private String payerId;

	/**
	 * 手机号
	 */
	@ApiModelProperty(name = "mblNo", value = "手机号")
	@Length(max = 16)
	private String mblNo;

	/**
	 * 附言摘要
	 */
	@ApiModelProperty(name = "remark", value = "附言摘要")
	private String remark;

	public String getOrderCcy() {
		return orderCcy;
	}

	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	public BigDecimal getOrderAmt() {
		return orderAmt;
	}

	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}


	public String getPayerId() {
		return payerId;
	}

	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getCashierOrderNo() {
		return cashierOrderNo;
	}

	public void setCashierOrderNo(String cashierOrderNo) {
		this.cashierOrderNo = cashierOrderNo;
	}

	public String getRemittOrderNo() {
		return RemittOrderNo;
	}

	public void setRemittOrderNo(String remittOrderNo) {
		RemittOrderNo = remittOrderNo;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}
}
