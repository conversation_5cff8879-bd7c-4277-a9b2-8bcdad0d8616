package com.hisun.lemon.csh.dto.cashier;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


/**
 * 审核订单信息 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("审核订单信息")
@ClientValidated
public class TranOrderReqDTO {

	/**
	 * 转账订单号
	 */
	@ApiModelProperty(name = "orderNo", value = "转账订单号")
	private String orderNo;

	/**
	 * 付款方
	 */
	@ApiModelProperty(name = "payerId", value = "付款方")
	private String payerId;

	/**
	 * 收款方
	 */
	@ApiModelProperty(name = "payeeId", value = "收款方")
	private String payeeId;

	/**
	 * 外围订单号
	 */
	@ApiModelProperty(name = "busOrderNo", value = "外围订单号")
	private String busOrderNo;

	/**
	 * 状态
	 */
	@ApiModelProperty(name = "status", value = "状态")
	private String status;

	/**
	 * 单页记录数
	 */
	@ApiModelProperty(name = "pageSize", value = "每页大小")
	@NotNull(message = "BIL10007")
	@Min(value=1, message="BIL10008")
	private Integer pageSize;
	/**
	 * 页数
	 */
	@ApiModelProperty(name = "pageNo", value = " 页码")
	@NotNull(message = "BIL10009")
	private Integer pageNo;

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNo() {
		return pageNo;
	}

	public void setPageNo(Integer pageNo) {
		this.pageNo = pageNo;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getPayerId() {
		return payerId;
	}

	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}

	public String getPayeeId() {
		return payeeId;
	}

	public void setPayeeId(String payeeId) {
		this.payeeId = payeeId;
	}

	public String getBusOrderNo() {
		return busOrderNo;
	}

	public void setBusOrderNo(String busOrderNo) {
		this.busOrderNo = busOrderNo;
	}
}
