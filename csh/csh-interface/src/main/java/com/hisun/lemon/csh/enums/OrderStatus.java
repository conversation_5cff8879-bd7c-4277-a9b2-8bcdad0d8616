package com.hisun.lemon.csh.enums;

public enum OrderStatus {
	
    /**
     * 待支付
     */
	WAIT_PAY("W"),
	/**
     * 支付待确认
     */
	PRE_PAY("P"),

	/**
	 * 审核通过
	 */
	FIRST_AUDIT_PASS("A1"),

	/**
	 * 复核通过
	 */
	FINAL_AUDIT_PASS("A2"),

	/**
     * 成功
     */
	SUCC("S"),
	/**
     * 失败
     */
	FAIL("F"),
	FAIL_1("F1"),
	FAIL_2("F2"),
	/**
     * 过期
     */
	EXPIRE("E"),
	
	/**
     * 部分退款
     */
	PART_REFUND("R1"),

	/**
     * 全部退款
     */
	REFUND("R2"),

	/**
	 * 关闭
	 */
	OFF("O"),
	/**
	 * 审核已提交
	 */
	WAIT_AUDIT("W1"),

	/**
	 * 待回调
	 */
	WAIT_CALLBACK("C");

	private String value;

	public String getValue() {
		return value;
	}

	OrderStatus(String value) {
		this.value = value;
	}
}
