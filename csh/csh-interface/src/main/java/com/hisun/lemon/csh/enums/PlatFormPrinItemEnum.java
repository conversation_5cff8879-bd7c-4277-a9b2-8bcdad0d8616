package com.hisun.lemon.csh.enums;

public enum PlatFormPrinItemEnum {

    USD_P("1002010001", "USD"),
    HKD_P("1002010002", "HKD"),
    USDT_P("3331020001","USDT"),
    USDC_P("3331020002","USDC");

    private String itemNo;
    private String ccy;

    private PlatFormPrinItemEnum(String itemNo, String ccy) {
        this.itemNo = itemNo;
        this.ccy = ccy;
    }

    public String getItemNo() {
        return this.itemNo;
    }

    public String getCcy() {
        return this.ccy;
    }
}
