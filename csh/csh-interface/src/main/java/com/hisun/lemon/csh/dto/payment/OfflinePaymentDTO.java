package com.hisun.lemon.csh.dto.payment;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 线下支付确认 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("线下支付确认 传输对象")
@ClientValidated
public class OfflinePaymentDTO {
	@ApiModelProperty(name = "orderNo", value = "充值订单号")
    @NotEmpty(message="CSH10006")
	@Length(max =28)
    private String orderNo;

	/**
	 * 充值用户或商户id
	 */
	@ApiModelProperty(name = "payerId", value = "充值用户或商户id")
	@NotEmpty(message="CSH10026")
	@Length(max = 16)
	private String payerId;

	@ApiModelProperty(name = "cashRemittUrl", value = "汇款单图片url", required = true, dataType = "String")
	@NotEmpty(message="CSH10041")
	private String cashRemittUrl;

	/**
	 * 附言摘要
	 */
	@ApiModelProperty(name = "remark", value = "附言摘要")
	private String remark;

	/**
	 * 资金机构
	 */
	@ApiModelProperty(name = "crdCorpOrg", value = "资金机构")
	private String crdCorpOrg;

	/**
	 * 加密卡号
	 */
	@ApiModelProperty(name = "crdNoEnc", value = "加密卡号")
	private String crdNoEnc;

	/**
	 * 卡种
	 */
	@ApiModelProperty(name = "crdAcTyp", value = "卡种，D借记卡，C贷记卡，U未知", required = true, dataType = "String")
	private String crdAcTyp;

	@ApiModelProperty(name = "busType", value = "业务类型")
	@NotEmpty(message="CSH10009")
	private String busType;

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getPayerId() {
		return payerId;
	}

	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}

	public String getCashRemittUrl() {
		return cashRemittUrl;
	}

	public void setCashRemittUrl(String cashRemittUrl) {
		this.cashRemittUrl = cashRemittUrl;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCrdCorpOrg() {
		return crdCorpOrg;
	}

	public void setCrdCorpOrg(String crdCorpOrg) {
		this.crdCorpOrg = crdCorpOrg;
	}

	public String getCrdNoEnc() {
		return crdNoEnc;
	}

	public void setCrdNoEnc(String crdNoEnc) {
		this.crdNoEnc = crdNoEnc;
	}

	public String getCrdAcTyp() {
		return crdAcTyp;
	}

	public void setCrdAcTyp(String crdAcTyp) {
		this.crdAcTyp = crdAcTyp;
	}

	public String getBusType() {
		return busType;
	}

	public void setBusType(String busType) {
		this.busType = busType;
	}
}
