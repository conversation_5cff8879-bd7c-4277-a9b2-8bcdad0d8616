package com.hisun.lemon.csh.dto.refund;

import java.math.BigDecimal;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 撤销与退款结果处理配置 传输对象
 * 
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 下午17:00:30
 *
 */
@ApiModel("撤销与退款结果处理")
public class RefundResultOrderDTO {
	/**
	 * @Fields rftOrdAmt 退款金额
	 */
	@ApiModelProperty(name = "rftOrdAmt", value = "退款金额")
	@NotNull(message="CSH10024")
	@DecimalMin(value ="0.01", message = "CSH10024")
	@Digits(integer=13, fraction=2,message = "CSH20106")
	private BigDecimal rfdSumAmt;
	/**
	 * 退款状态
	 */
	@ApiModelProperty(name = "orderSataus", value = "退款状态   S退款成功  F退款失败")
	@NotEmpty(message = "CSH10025")
	@Length(max =2)
	private String orderSataus;

	/**
	 * 退款订单号
	 */
	@ApiModelProperty(name = "rfdOrdNo", value = "退款订单号")
	@NotEmpty(message = "CSH10026")
	@Length(max =25)
	private String rfdOrdNo;
	
	/**
     * @Fields cpiNo 资金能力订单号
     */
	/**
	 * 资金能力订单号
	 */
	@ApiModelProperty(name = "fndRfdOrderNo", value = "资金能力订单号")
	@NotEmpty(message = "CSH10036")
    private String fndRfdOrderNo;

	public BigDecimal getRfdSumAmt() {
		return rfdSumAmt;
	}

	public String getFndRfdOrderNo() {
		return fndRfdOrderNo;
	}

	public void setFndRfdOrderNo(String fndRfdOrderNo) {
		this.fndRfdOrderNo = fndRfdOrderNo;
	}



	public void setRfdSumAmt(BigDecimal rfdSumAmt) {
		this.rfdSumAmt = rfdSumAmt;
	}

	public String getOrderSataus() {
		return orderSataus;
	}

	public void setOrderSataus(String orderSataus) {
		this.orderSataus = orderSataus;
	}

	public String getRfdOrdNo() {
		return rfdOrdNo;
	}

	public void setRfdOrdNo(String rfdOrdNo) {
		this.rfdOrdNo = rfdOrdNo;
	}

}
