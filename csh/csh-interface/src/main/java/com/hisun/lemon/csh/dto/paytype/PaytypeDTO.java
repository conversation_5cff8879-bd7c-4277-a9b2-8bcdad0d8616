package com.hisun.lemon.csh.dto.paytype;

import java.time.LocalDateTime;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
 

/**
 * 支付方式配置 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("支付方式配置")
public class PaytypeDTO{ 
	@Length(max =11)
	private int id;
	@Length(max =255)
    private String mercId;
	@NotEmpty(message="CSH10021")
	@Length(max =8)
	private String appCnl;
	@ApiModelProperty(name = "busType", value = "业务类型")
    @NotEmpty(message="CSH10032")
	@Length(max =4)
    private String busType;
	@ApiModelProperty(name = "payTypes", value = "支付类型")
	@Length(max =60)
    private String payTypes;
	@Length(max =60)
    private String gwPayTypes; 
    
	@Length(max =1)
    private String status;
    
    @ApiModelProperty(name = "modifyOpr", value = "最后修改人")
    @Length(max =255)
    private String modifyOpr;
    
    private LocalDateTime modifyTime;
    private LocalDateTime createTime;
    
	public String getMercId() {
		return mercId;
	}
	public void setMercId(String mercId) {
		this.mercId = mercId;
	}

	public String getAppCnl() {
		return appCnl;
	}

	public void setAppCnl(String appCnl) {
		this.appCnl = appCnl;
	}

	public String getBusType() {
		return busType;
	}
	public void setBusType(String busType) {
		this.busType = busType;
	}
	public String getPayTypes() {
		return payTypes;
	}
	public void setPayTypes(String payTypes) {
		this.payTypes = payTypes;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getGwPayTypes() {
		return gwPayTypes;
	}
	public void setGwPayTypes(String gwPayTypes) {
		this.gwPayTypes = gwPayTypes;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getModifyOpr() {
		return modifyOpr;
	}
	public void setModifyOpr(String modifyOpr) {
		this.modifyOpr = modifyOpr;
	}
	public LocalDateTime getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(LocalDateTime modifyTime) {
		this.modifyTime = modifyTime;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}   
}
