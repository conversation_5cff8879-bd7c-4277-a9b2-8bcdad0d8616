package com.hisun.lemon.csh.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * 差错处理传输对象
 * <AUTHOR>
 * @date 2017年8月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("差错处理传输对象")
public class ErrorOrderDTO {
	@ApiModelProperty(name = "crdPayAmt", value = "金额")
    @NotNull(message="CSH10014")
    private BigDecimal crdPayAmt;  

	@ApiModelProperty(name = "orderNo", value = "收银订单号")
    @NotEmpty(message="CSH10016")
    private String orderNo;

	@ApiModelProperty(name = "fndOrderNo", value = "资金能力订单号")
	private String fndOrderNo;

	@ApiModelProperty(name = "remark", value = "备注信息")
	private String remark;

	@ApiModelProperty(name = "fndOrderNo", value = "差错处理操作类型 1 补单 2 撤单")
	@NotNull(message="CSH10042")
	private Integer errOprType;

	public BigDecimal getCrdPayAmt() {
		return crdPayAmt;
	}

	public void setCrdPayAmt(BigDecimal crdPayAmt) {
		this.crdPayAmt = crdPayAmt;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getErrOprType() {
		return errOprType;
	}

	public void setErrOprType(Integer errOprType) {
		this.errOprType = errOprType;
	}

	public String getFndOrderNo() {
		return fndOrderNo;
	}

	public void setFndOrderNo(String fndOrderNo) {
		this.fndOrderNo = fndOrderNo;
	}
}
