package com.hisun.lemon.csh.dto.order;

import java.math.BigDecimal;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;


/**
 * 资金能力通知传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel(" 资金能力通知")
public class NotifyResultDTO { 
	/**
	 * 收银支付流水号
	 */
	@ApiModelProperty(name = "cshOrderNo", value = "收银订单号")
    @NotEmpty(message="CSH10013")
	@Length(max =24)
    private String cshOrderNo;
	@ApiModelProperty(name = "crdPayAmt", value = "补款金额")
    @NotNull(message="CSH10014")
    private BigDecimal crdPayAmt;  

	@ApiModelProperty(name = "fndOrderNo", value = "资金能力订单号")
    @NotEmpty(message="CSH10016")
	@Length(max =24)
    private String fndOrderNo;
	@ApiModelProperty(name = "orderStatus", value = "订单状态")
	@NotEmpty(message="CSH10017")
	@Length(max =2)
	private String orderStatus;

	@ApiModelProperty(name = "remark", value = "备注信息")
	private String remark;


	public String getCshOrderNo() {
		return cshOrderNo;
	}
	public void setCshOrderNo(String cshOrderNo) {
		this.cshOrderNo = cshOrderNo;
	}
	
	public BigDecimal getCrdPayAmt() {
		return crdPayAmt;
	}
	public void setCrdPayAmt(BigDecimal crdPayAmt) {
		this.crdPayAmt = crdPayAmt;
	}
	
	public String getFndOrderNo() {
		return fndOrderNo;
	}
	public void setFndOrderNo(String fndOrderNo) {
		this.fndOrderNo = fndOrderNo;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
