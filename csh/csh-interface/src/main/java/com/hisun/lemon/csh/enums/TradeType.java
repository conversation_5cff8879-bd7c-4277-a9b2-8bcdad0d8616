package com.hisun.lemon.csh.enums;

public enum TradeType {

	/**
	 * 交易类型：充值
	 */
	RECHARGE("01"),
	/**
	 * 交易类型：消费
	 */
	CONSUME("02"),

	TRANSFER("03"),

	RECHARGE_H("05"),

	/**
	 * 交易类型：退款
	 */
	REFUND("06"),

	/**
	 * 交易类型：理财
	 */
	FINANC("07"),

	/**
	 * 交易类型：缴费
	 */
	PAYMENT("08"),

	/**
	 * 交易类型：撤销
	 */
	UNDO("09"),

	/**
	 * 交易类型：数币兑换
	 */
	EXCHANGE("DH");

	private String type ;

	TradeType(String type) {
		this.type = type;
	}

	public String getType() {

		return type;
	}
}
