package com.hisun.lemon.csh.dto.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import java.math.BigDecimal;


/**
 * 支付结果DTO
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("支付结果")
public class PaymentResultDTO {
	@ApiModelProperty(name = "orderNo", value = "订单号")
    private String orderNo;  
     
	@ApiModelProperty(name = "busOrderNo", value = "业务模块订单号")
    private String busOrderNo;

	@ApiModelProperty(name = "orderAmt", value = "金额")
    private BigDecimal orderAmt;

	@ApiModelProperty(name = "goodsDesc", value = "商品描述")
	private String goodsDesc;

	@ApiModelProperty(name = "merchantName", value = "商户名称")
	private String merchantName;

	@ApiModelProperty(name = "mblNo", value = "手机号")
	private String mblNo;

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getBusOrderNo() {
		return busOrderNo;
	}

	public void setBusOrderNo(String busOrderNo) {
		this.busOrderNo = busOrderNo;
	}

	public BigDecimal getOrderAmt() {
		return orderAmt;
	}

	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}

	public String getGoodsDesc() {
		return goodsDesc;
	}

	public void setGoodsDesc(String goodsDesc) {
		this.goodsDesc = goodsDesc;
	}

	public String getMerchantName() {
		return merchantName;
	}

	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}

	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}
}
