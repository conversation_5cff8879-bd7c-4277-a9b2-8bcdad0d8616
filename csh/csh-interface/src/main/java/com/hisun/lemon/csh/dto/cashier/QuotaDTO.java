package com.hisun.lemon.csh.dto.cashier;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收银台 配额方案
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
public class QuotaDTO {

	@ApiModelProperty(name = "couponAmt", value = "优惠金额或海币数量")
	private BigDecimal couponAmt;
	@ApiModelProperty(name = "payAmt", value = "实际支付金额")
	private BigDecimal payAmt;
	@ApiModelProperty(name = "couponNo", value = "优惠券编号（海币为空）")
	private String couponNo;
	@ApiModelProperty(name = "type", value = "使用优惠类型：00 不使用优惠 01电子券 02 海币 03 折扣券")
	private String type;

	public BigDecimal getCouponAmt() {
		return couponAmt;
	}

	public void setCouponAmt(BigDecimal couponAmt) {
		this.couponAmt = couponAmt;
	}

	public BigDecimal getPayAmt() {
		return payAmt;
	}

	public void setPayAmt(BigDecimal payAmt) {
		this.payAmt = payAmt;
	}

	public String getCouponNo() {
		return couponNo;
	}

	public void setCouponNo(String couponNo) {
		this.couponNo = couponNo;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public QuotaDTO(BigDecimal couponAmt, BigDecimal payAmt, String couponNo, String type) {
		this.couponAmt = couponAmt;
		this.payAmt = payAmt;
		this.couponNo = couponNo;
		this.type = type;
	}

	public QuotaDTO( ) {
	}
}
