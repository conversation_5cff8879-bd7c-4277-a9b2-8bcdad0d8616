package com.hisun.lemon.csh.enums;

public enum AcItem {

	/**
	 * 内部科目
	 * 其他应付款-暂收-收银台
	 */
	O_CSH("**********"),

	/**
	 * 内部科目
	 * 其他应付款-渠道退款-XX银行
	 */
	O_CNL_BANK("**********"),

	/**
	 * 内部科目
	 * 贷：其他应付款-中转挂账-海币
	 */
	O_H_COUPON_MID("**********"),

	/**
	 * 内部科目
	 * 其他应付款-支付账户-现金账户
	 */
	O_BAL("**********"),

	/**
	 * 内部科目
	 * 其他应付款-支付账户-增值账户
	 */
	O_INC("**********"),

	/**
	 * 内部科目
	 * 手续费收入-支付账户-消费
	 */
	O_FEE("**********"),

	/**
	 * 其他应付款-支付账户-商户结算账户
	 */
	O_MERC("**********"),

	/**
	 * 内部科目
	 * 其他应付款-支付账户-用户海币账户
	 */
	O_H_COUPON("010101"),


	/**
	 * 内部科目
	 * 其他应付款-充值款-现金
	 */
	O_RECHARGE_AC("010101"),

	/**
	 * 内部科目
	 * 应收账款-渠道充值-XX银行
	 */
	I_CNL_BANK("**********"),

	/**
	 * 内部科目
	 * 应收账款-电子券发放-XX公司
	 */
	I_E_COUPON_COMPANY("**********"),

	/**
	 * 内部科目
	 * 应收账款-渠道充值-营业厅
	 */
	I_CNL_HALL("**********"),
	/**
	 * 内部科目
	 * 其他应收款-营销款项-电子券
	 */
	I_E_COUPON("010101"),
	/**
	 * 内部科目
	 * 银行存款-备付金账户-XX银行
	 */
	DEPOSIT_BANK("**********");

	private String value ;

	AcItem(String value) {
		this.value = value;
	}

	public String getValue() {

		return value;
	}
}
