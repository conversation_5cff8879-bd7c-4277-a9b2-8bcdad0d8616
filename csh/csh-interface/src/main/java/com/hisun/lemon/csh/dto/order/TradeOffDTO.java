package com.hisun.lemon.csh.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 交易关闭通知传输对象
 * <AUTHOR>
 * @date 2017年10月30日
 * @time 上午9:27:30
 *
 */
@ApiModel("交易关闭请求传输对象")
public class TradeOffDTO {

    @ApiModelProperty(name = "orderNo", value = "收银订单号")
    @NotEmpty(message="CSH10016")
    @Length(max =24)
    private String orderNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
