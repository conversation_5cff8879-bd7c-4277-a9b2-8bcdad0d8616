package  com.hisun.lemon.csh.dto.refund;

import java.math.BigDecimal;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;

/**
 * 撤销与退款配置 传输对象
 * 
 * <AUTHOR>
 * @date 2017年7月17日
 * @time 下午17:31:30
 *
 */
public class RefundOrderRspDTO {
	
	/**
	 * 退款订单号
	 */
	@ApiModelProperty(name = "rfdOrdNo", value = "退款订单号")
	private String rfdOrdNo;
	/**
	 * @Fields payNo 外部模块退款订单号
	 */
	@ApiModelProperty(name = "busRdfOrdNo", value = "外部模块退款订单号")
	private String busRdfOrdNo;
	/**
	 * @Fields payNo 外部模块退款订单号
	 */
	@ApiModelProperty(name = "busOrderNo", value = "外部模块订单号")
	private String busOrderNo;
	/**
	 * @Fields extRefundNo 收银订单号
	 */
	@ApiModelProperty(name = "orginOrderNo", value = "收银订单号")
	private String orginOrderNo;
	
	/**
	 * @Fields extRefundNo 状态
	 */
	@ApiModelProperty(name = "orderStatus", value = "状态")
	private String orderStatus;
	
	 /**
     * @Fields txType 交易类型
     */
	@ApiModelProperty(name = "txType", value = "交易类型   退款06")
	@NotEmpty(message = "CSH20057")
	@Length(max =2)
    private String txType;
    /**
     * @Fields busType 业务类型   0601充值长款退款     0602消费退款    0603 消费失败退款    0604借贷退款
     */
	@ApiModelProperty(name = "busType", value = "业务类型        0601充值长款退款     0602消费退款    0603 消费失败退款    0604借贷退款   0605 缴费退款    0606 缴费失败退款")
    private String busType;
	
	/**
	 * @Fields rfdType 原路 01 02账户
	 */
	@ApiModelProperty(name = "busType", value = "业务类型  01 原路        02 账户  ")
	private String rfdType;
	/**
	 * @Fields rfdReason 原因
	 */
	@ApiModelProperty(name = "rfdReason", value = "退款原因")
	private String rfdReason;
	
	/**
	 * @Fields rfdAmt 退款金额
	 */
	@ApiModelProperty(name = "rfdAmt", value = "退款金额")
	private BigDecimal rfdAmt;
	
	public String getBusRdfOrdNo() {
		return busRdfOrdNo;
	}
	public void setBusRdfOrdNo(String busRdfOrdNo) {
		this.busRdfOrdNo = busRdfOrdNo;
	}
	public String getOrginOrderNo() {
		return orginOrderNo;
	}
	public void setOrginOrderNo(String orginOrderNo) {
		this.orginOrderNo = orginOrderNo;
	}
	public String getRfdReason() {
		return rfdReason;
	}
	public void setRfdReason(String rfdReason) {
		this.rfdReason = rfdReason;
	}
	public String getRfdType() {
		return rfdType;
	}
	public void setRfdType(String rfdType) {
		this.rfdType = rfdType;
	}
	public String getBusType() {
		return busType;
	}
	
	public String getRfdOrdNo() {
		return rfdOrdNo;
	}
	public void setRfdOrdNo(String rfdOrdNo) {
		this.rfdOrdNo = rfdOrdNo;
	}
	public String getTxType() {
		return txType;
	}
	public void setTxType(String txType) {
		this.txType = txType;
	}
	
	public void setBusType(String busType) {
		this.busType = busType;
	}
	public String getOrderStatus() {
		return orderStatus;
	}
	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}
	public BigDecimal getRfdAmt() {
		return rfdAmt;
	}
	public void setRfdAmt(BigDecimal rfdAmt) {
		this.rfdAmt = rfdAmt;
	}
	public String getBusOrderNo() {
		return busOrderNo;
	}
	public void setBusOrderNo(String busOrderNo) {
		this.busOrderNo = busOrderNo;
	}
}
