package com.hisun.lemon.csh.dto.cashier;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 收银台 优惠券别明细类型
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
public class CouponItem {

	@ApiModelProperty(name = "couponNo", value = "优惠券编号")
	private String couponNo;

	@ApiModelProperty(name = "amt", value = "面额")
	private BigDecimal amt;
	@ApiModelProperty(name = "status", value = "状态 01-未使用 02-已使用 03-已过期 04-冻结 05-待激活")
	private String status;
	@ApiModelProperty(name = "mkTool", value = "营销工具类型")
	private String mkTool;


	public String getCouponNo() {
		return couponNo;
	}

	public void setCouponNo(String couponNo) {
		this.couponNo = couponNo;
	}

	public BigDecimal getAmt() {
		return amt;
	}

	public void setAmt(BigDecimal amt) {
		this.amt = amt;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMkTool() {
		return mkTool;
	}

	public void setMkTool(String mkTool) {
		this.mkTool = mkTool;
	}
}
