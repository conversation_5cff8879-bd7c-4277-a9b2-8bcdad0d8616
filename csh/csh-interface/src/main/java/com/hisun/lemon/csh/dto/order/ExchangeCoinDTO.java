package com.hisun.lemon.csh.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel("币种兑换")
public class ExchangeCoinDTO {

    @ApiModelProperty(name = "orderNo", value = "订单号")
    private String orderNo;

    @ApiModelProperty(name = "payerId", value = "支付方id")
    private String payerId;

    @ApiModelProperty(name = "payeeId", value = "收款方id")
    private String payeeId;

    @ApiModelProperty(name = "acNo", value = "账户编号")
    private String acNo;

    @ApiModelProperty(name = "orderAmt", value = "订单金额")
    private BigDecimal orderAmt;

    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    @ApiModelProperty(name = "ccy", value = "币种")
    private String ccy;

    @ApiModelProperty(name = "txType", value = "交易类型")
    @NotEmpty(message="CSH10008")
    private String txType;

    @ApiModelProperty(name = "busType", required = true, value = "业务类型 个人快捷充值:0101,个人线下充值:0102,个人营业厅充值:0103,企业网银充值:0104,条码消费:0201，" +
            "扫码付消费:0202,APP消费:0203,POS消费:0204,银行卡收单:0205 ,转到账户:0301,转银行卡:0302，当面收款:0303,兑换:0305,个人提现:0401,商户提现:0402,充海币:0501,充流量:0801,充话费:0802")
    @NotEmpty(message="CSH10009")
    private String busType;

    @ApiModelProperty(name = "holdNo", value = "冻结金额编号")
    private String holdNo;

    @ApiModelProperty(name = "orderSts", value = "订单状态")
    private String orderSts;

    @ApiModelProperty(name = "direction", value = "兑换方向")
    private String direction;

    @ApiModelProperty(name = "fromCoin", value = "卖出币种")
    private String fromCoin;

    @ApiModelProperty(name = "toCoin", value = "买入币种")
    private String toCoin;

    @ApiModelProperty(name = "toAmount", value = "买入金额")
    private BigDecimal toAmount;

    @ApiModelProperty(name = "leftFee", value = "折算成USD的手续费")
    private BigDecimal leftFee;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(String payeeId) {
        this.payeeId = payeeId;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getHoldNo() {
        return holdNo;
    }

    public void setHoldNo(String holdNo) {
        this.holdNo = holdNo;
    }

    public String getOrderSts() {
        return orderSts;
    }

    public void setOrderSts(String orderSts) {
        this.orderSts = orderSts;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getFromCoin() {
        return fromCoin;
    }

    public void setFromCoin(String fromCoin) {
        this.fromCoin = fromCoin;
    }

    public String getToCoin() {
        return toCoin;
    }

    public void setToCoin(String toCoin) {
        this.toCoin = toCoin;
    }

    public BigDecimal getToAmount() {
        return toAmount;
    }

    public void setToAmount(BigDecimal toAmount) {
        this.toAmount = toAmount;
    }

    public BigDecimal getLeftFee() {
        return leftFee;
    }

    public void setLeftFee(BigDecimal leftFee) {
        this.leftFee = leftFee;
    }
}
