package com.hisun.lemon.csh.dto.cashier;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 后台支付传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
public class BackstageViewDTO {
	@ApiModelProperty(name = "orderNo", value = "收银订单号")
	private String orderNo;

	/**
	 * 账户余额
	 */
	@ApiModelProperty(name = "balAmt", value = "账户余额")
	private BigDecimal balAmt;


	/**
	 * 订单金额
	 */
	@ApiModelProperty(name = "orderAmt", value = "订单金额")
	private BigDecimal orderAmt;

	@ApiModelProperty(name = "payAmt", value = "支付金额")
	private BigDecimal payAmt;

	/**
	 * 手续费
	 */
	@ApiModelProperty(name = "feeAmt", value = "手续费")
	private BigDecimal feeAmt;

	/**
	 * 用户扫码下单所需的返回字段
	 */
	private Map<String, String> resultMap;

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public BigDecimal getBalAmt() {
		return balAmt;
	}

	public void setBalAmt(BigDecimal balAmt) {
		this.balAmt = balAmt;
	}

	public BigDecimal getOrderAmt() {
		return orderAmt;
	}

	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}

	public BigDecimal getFeeAmt() {
		return feeAmt;
	}

	public void setFeeAmt(BigDecimal feeAmt) {
		this.feeAmt = feeAmt;
	}

	public BigDecimal getPayAmt() {
		return payAmt;
	}

	public void setPayAmt(BigDecimal payAmt) {
		this.payAmt = payAmt;
	}

	public Map<String, String> getResultMap() {
		return resultMap;
	}

	public void setResultMap(Map<String, String> resultMap) {
		this.resultMap = resultMap;
	}
}
