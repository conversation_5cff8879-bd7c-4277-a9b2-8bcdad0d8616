package com.hisun.lemon.csh.dto.payment;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

public class AcledaTransferRspDTO {

    @ApiModelProperty(name = "sessionId", value = "Acleda银行sessionId")
    private String sessionId;


    @ApiModelProperty(name = "paymentTokenId", value = "Acleda银行支付paymentTokenId")
    private String paymentTokenId;

    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    @ApiModelProperty(name = "feeFlag", value = "扣费方式")
    private String feeFlag;


    @ApiModelProperty(name = "orderNo", value = "收银台订单号")
    private String orderNo;

    @ApiModelProperty(name = "orderNo", value = "收银台订单号")
    private List<AccountReqDTO> accountReqList;

    public class AccountReqDTO {

        public AccountReqDTO(){

        }
        /**
         * txSts 交易状态 N:正常 R:撤销 C:冲正  撤销暂不考虑
         */
        @ApiModelProperty(name = "txSts", value = "交易状态 N:正常 R:撤销 C:冲正  撤销暂不考虑")
        @Length(max = 1)
        @NotBlank
        private String txSts;
        /**
         * acTyp  账户类型 U:用户 I：科目 DO对象不存在此域
         */
        @ApiModelProperty(name = "acTyp", value = "账户类型 U:用户 I：科目")
        @Length(max = 1)
        @NotBlank
        private String acTyp;
        /**
         * acNo 交易账号
         */
        @ApiModelProperty(name = "acNo", value = "交易账号")
        @Length(max = 15)
        private String acNo;
        /**
         * capTyp 账户资金属性
         */
        @ApiModelProperty(name = "capTyp", value = "账户资金属性， 1：现金，8：待结算")
        @Length(max = 1)
        private String capTyp;
        /**
         * itmNo 科目号
         */
        @ApiModelProperty(name = "itmNo", value = "科目号")
        @Length(max = 10)
        private String itmNo;
        /**
         * txTyp 交易类型
         */
        @ApiModelProperty(name = "txTyp", value = "交易类型", required = true)
        @Length(max = 4)
        @NotBlank
        private String txTyp;
        /**
         * txAmt 交易金额
         */
        @ApiModelProperty(name = "txAmt", value = "交易金额", required = true)
        @NotNull
        @Range(min = 0)
        private BigDecimal txAmt;
        /**
         * dcFlg 借贷标志
         */
        @ApiModelProperty(name = "dcFlg", value = "借贷标志 D:借， C:贷", required = true)
        @Length(max = 1)
        @NotBlank
        private String dcFlg;
        /**
         * txJrnNo 请求唯一流水号(堵重)
         */
        @ApiModelProperty(name = "txJrnNo", value = "请求唯一流水号", required = true)
        @Length(max = 32)
        @NotBlank
        private String txJrnNo;
        /**
         * txOrdNo 请求订单号
         */
        @ApiModelProperty(name = "txOrdNo", value = "请求订单号")
        @Length(max = 32)
        private String txOrdNo;
        /**
         * txOrdDt 请求订单日期
         */
        @ApiModelProperty(name = "txOrdDt", value = "请求订单日期", dataType = "java.time.LocalDate")
        private LocalDate txOrdDt;
        /**
         * txOrdTm 请求订单时间
         */
        @ApiModelProperty(name = "txOrdTm", value = "请求订单时间", dataType = "java.time.LocalTime")
        private LocalTime txOrdTm;
        /**
         * oppAcNo 账号
         */
        @ApiModelProperty(name = "oppAcNo", value = "对手账号")
        @Length(max = 15)
        private String oppAcNo;
        /**
         * oppCapTyp 资金属性
         */
        @ApiModelProperty(name = "oppCapTyp", value = "资金属性")
        @Length(max = 1)
        private String oppCapTyp;
        /**
         * oppUserId 用户号
         */
        @ApiModelProperty(name = "oppUserId", value = "对手用户号")
        @Length(max = 16)
        private String oppUserId;
        /**
         * oppUserTyp 用户类型
         */
        @ApiModelProperty(name = "oppUserTyp", value = "用户类型")
        @Length(max = 1)
        private String oppUserTyp;
        /**
         * usrIpAdr 用户IP地址
         */
        @ApiModelProperty(name = "usrIpAdr", value = "用户IP地址")
        @Length(max = 15)
        private String usrIpAdr;
        /**
         * rmk 备注
         */
        @ApiModelProperty(name = "rmk", value = "备注")
        @Length(max = 512)
        private String rmk;
    }


    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getPaymentTokenId() {
        return paymentTokenId;
    }

    public void setPaymentTokenId(String paymentTokenId) {
        this.paymentTokenId = paymentTokenId;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getFeeFlag() {
        return feeFlag;
    }

    public void setFeeFlag(String feeFlag) {
        this.feeFlag = feeFlag;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<AccountReqDTO> getAccountReqList() {
        return accountReqList;
    }

    public void setAccountReqList(List<AccountReqDTO> accountReqList) {
        this.accountReqList = accountReqList;
    }
}
