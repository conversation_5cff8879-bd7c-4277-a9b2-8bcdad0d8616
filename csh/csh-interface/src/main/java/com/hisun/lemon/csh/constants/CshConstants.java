package com.hisun.lemon.csh.constants;

public class CshConstants {
	/**
	 * 生成收银订单号的前缀
	 */
	public static final String ORD_GEN_PRE="cshOrdNo"; 
	
	/**
	 * 生成退款订单号的前缀
	 */
	public static final String RFD_GEN_PRE="cshRfeNo"; 
	
	public static final String YES="1";
	public static final String NO="0";
	
	/**
	 * 海币换算美元比率
	 */
	public static final double H_USD_RATE=0.01;
	
	/**
	 * 海币换算美元比率
	 */
	public static final double M_USD_RATE=100;

	/**
	 * 优惠最多使用金额比例
	 */
	public static final double USE_COUPON_RATE=0.5;
	
	/**
	 * 退款    状态:失败
	 */
	public static final String RFD_ORDER_STATUS_F1="F1";
	/**
     * 已撤销
     */
	public static final String ORD_STS_C1="C1";
	
	/**
     * 撤销初始
     */
	public static final String ORD_STS_C0="C0";
	
	/**
	 * 撤销    状态:失败
	 */
	public static final String RFD_ORDER_STATUS_F2="F2";

	/**
	 * 退款    状态:初始状态
	 */
	public static final String RFD_ORDER_STATUS_R0="R0";

	/**
	 * 退款处理中    状态:初始状态
	 */
	public static final String RFD_ORDER_STATUS_RP="RP";



	/**
	 * 撤销   状态   :成功撤销
	 */
	public static final String ORD_STS_C2="C2";
	
	/**
	 * 退款   状态   :成功退款
	 */
	public static final String RFD_ORDER_STATUS_S1="S1";
	
	
	/**
	 * 流水状态：初始登记
	 */
	public static final String JRN_STS_U="U";
	/**
	 * 流水状态：失败
	 */
	public static final String JRN_STS_F="F";
	/**
	 * 流水状态：成功
	 */
	public static final String JRN_STS_S="S";
	
	/**
	 * 匹配所有（比如：所有商户），多见于规则配置
	 */
	public static final String MATCH_ALL="*";


	/**
	 * 快捷支付用户类型：用户
	 */
	public static final String QP_USER_TYPE="U";
	/**
	 * 银行卡个企标识：个人
	 */
	public static final String QP_BNK_PSN_FLAG="C";
	/**
	 * 支付币种：USD 美元
	 */
	public static final String QP_PAY_CCY="USD";

	/**
	 * 补款类型：无补款
	 */
	public static final String CRD_TYPE_NONE="0";
	/**
	 * 补款类型：网银
	 */
	public static final String CRD_TYPE_NB="1";
	/**
	 * 补款类型：快捷
	 */
	public static final String CRD_TYPE_QP="2";

	/**
	 * 补款类型：线下转账补款
	 */
	public static final String CRD_TYPE_FL="3";

	/**
	 * 资金能力
	 */
	public static final String RFD_TYPE_R="01";
	/**
	 * 账户
	 */
	public static final String RFD_TYPE_U="02";
	

 
	public static final String RSK_CNL_APP="APP";
	/**
	 * 消息模版编号
	 */
	public static final String SEND_MER_TO_USER_ORDER="00000006";

	/**
	 * 资金能力个企标识：个人
	 */
	public static final String BNK_PSN_FLAG_USER="C";

	/**
	 * 资金能力个企标识：企业
	 */
	public static final String BNK_PSN_FLAG_BUSINESS="B";

	/**
	 * 系统渠道：APP
	 */
	public static final String SYS_CHANNEL_APP="APP";

	/**
	 * 系统渠道：WEB
	 */
	public static final String SYS_CHANNEL_WEB="WEB";

	/**
	 *  支付路径合作机构：Seatelpay
	 */
	public static final String PAY_TYPE_SEA = "Seatelpay";
	/**
	 *  支付路径合作机构：WeChat
	 */
	public static final String PAY_TYPE_WX = "WeChat";
	/**
	 *  支付路径合作机构：Alipay
	 */
	public static final String PAY_TYPE_ALI = "Alipay";
	/**
	 *  支付路径合作机构：ICBC
	 */
	public static final String PAY_TYPE_ICBC = "ICBC";
	/**
	 *  支付路径合作机构：BESTPAY
	 */
	public static final String PAY_TYPE_BEST = "BESTPAY";

	/** 兑换订单状态-拒绝 */
	public static final String EXCHANGE_REJECTED = "REJECTED";
	/** 兑换订单状态-成功 */
	public static final String EXCHANGE_SUCCESS = "SUCCESS";
}
