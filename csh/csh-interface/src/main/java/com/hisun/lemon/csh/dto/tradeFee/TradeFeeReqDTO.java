package com.hisun.lemon.csh.dto.tradeFee;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ClientValidated
@ApiModel("查询交易手续费")
public class TradeFeeReqDTO extends GenericDTO<NoBody> {
	 /** 业务类型 */
    @ApiModelProperty(name = "busType", value = "业务类型", required = true)
    @NotEmpty(message = "CSH10043")
    @Length(max =4)
    private String busType;
    /** 币种 */
    @ApiModelProperty(name = "ccy", value = "币种", required = true)
    @Pattern(regexp = "KHR|USD|CNY", message = "CSH10044")
    private String ccy;
    /** 交易金额 */
    @ApiModelProperty(name = "tradeAmt", value = "交易金额", required = true)
    @NotNull(message = "CSH10045")
    private BigDecimal tradeAmt;
	public String getBusType() {
		return busType;
	}
	public void setBusType(String busType) {
		this.busType = busType;
	}
	public String getCcy() {
		return ccy;
	}
	public void setCcy(String ccy) {
		this.ccy = ccy;
	}
	public BigDecimal getTradeAmt() {
		return tradeAmt;
	}
	public void setTradeAmt(BigDecimal tradeAmt) {
		this.tradeAmt = tradeAmt;
	}
}