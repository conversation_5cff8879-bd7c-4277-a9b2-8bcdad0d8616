package com.hisun.lemon.csh.dto.cashier;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 收银台 配额计算
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
public class ComputeQuotaDTO {

	@ApiModelProperty(name = "totalAmt", value = "支付总金额")
	@Min(value=0, message="CSH20071")
	private BigDecimal totalAmt;
	@ApiModelProperty(name = "couponAmt", value = "优惠数量")
	@Min(value=0, message="CSH20071")
	private BigDecimal couponAmt;

	@ApiModelProperty(name = "couponNo", value = "优惠券编号（海币为空）")
	private String couponNo;

	@ApiModelProperty(name = "type", value = "使用优惠类型：00 不使用优惠 01电子券 02 海币 03 折扣券")
	@NotEmpty(message="CSH10070")
	private String type;

	public BigDecimal getTotalAmt() {
		return totalAmt;
	}

	public void setTotalAmt(BigDecimal totalAmt) {
		this.totalAmt = totalAmt;
	}

	public String getCouponNo() {
		return couponNo;
	}

	public void setCouponNo(String couponNo) {
		this.couponNo = couponNo;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public BigDecimal getCouponAmt() {
		return couponAmt;
	}

	public void setCouponAmt(BigDecimal couponAmt) {
		this.couponAmt = couponAmt;
	}
}
