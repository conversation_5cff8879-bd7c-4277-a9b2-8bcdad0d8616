package com.hisun.lemon.csh.dto.payment;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 通用支付数据传输基础模型
 * <AUTHOR>
 * @date 2017年8月13日
 * @time 上午12:29:38
 *
 */
@ApiModel("基础传输模型")
@ClientValidated
public abstract class AbstractNormalPaymentDTO {

    @ApiModelProperty(name = "couponAmt", value = "优惠额")
	@NotNull(message="CSH10003")
    @Min(value=0, message="CSH10003")
	@Digits(integer=15,fraction=2,message="CSH10003")
	protected BigDecimal couponAmt;
    @ApiModelProperty(name = "couponType", required = true, value = "优惠类型    00 无优惠  02海币  01电子券  03优惠券")
	@NotEmpty(message="CSH10072")
    @Length(max =2)
	protected String couponType;
	@ApiModelProperty(name = "couponNo", value = "优惠券编号（海币时为空）")
	protected String couponNo;

	@ApiModelProperty(name = "busType", required = true, value = "业务类型 个人快捷充值:0101,个人线下充值:0102,个人营业厅充值:0103,企业网银充值:0104,条码消费:0201，" +
			"扫码付消费:0202,APP消费:0203,POS消费:0204,银行卡收单:0205 ,转到账户:0301,转银行卡:0302，当面收款:0303,个人提现:0401,商户提现:0402,充海币:0501,充流量:0801,充话费:0802")
	@NotEmpty(message="CSH10009")
	protected String busType;

	@ApiModelProperty(name = "orderCcy", value = "币种")
	@Length(max =6)
	protected String orderCcy;

	public String getOrderCcy() {
		return orderCcy;
	}

	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	public BigDecimal getCouponAmt() {
		return couponAmt;
	}

	public void setCouponAmt(BigDecimal couponAmt) {
		this.couponAmt = couponAmt;
	}

	public String getCouponType() {
		return couponType;
	}

	public void setCouponType(String couponType) {
		this.couponType = couponType;
	}

	public String getCouponNo() {
		return couponNo;
	}

	public void setCouponNo(String couponNo) {
		this.couponNo = couponNo;
	}

	public String getBusType() {
		return busType;
	}

	public void setBusType(String busType) {
		this.busType = busType;
	}
}
