package com.hisun.lemon.csh.dto.cashier;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
 

/**
 * 创建收银台 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("创建收银台")
@ClientValidated
public class InitCashierDTO { 
	/**
	 * 外围模块订单号
	 */
	@ApiModelProperty(name = "extOrderNo", value = "外围模块订单号")
    @NotEmpty(message="CSH10006")
	@Length(max =28)
    private String extOrderNo;
    /**
	 * 币种
	 */ 
	@ApiModelProperty(name = "orderCcy", value = "币种")
	@Length(max =4)
    private String orderCcy;
    
    /**
	 * 订单金额
	 */
	@ApiModelProperty(name = "orderAmt", value = "订单金额")
    @Min(value=0, message="CSH10007")
	@Digits(integer=15,fraction=2,message="CSH10007")
    private BigDecimal orderAmt;
	/**
	 * 服务费
	 */
	@ApiModelProperty(name = "fee", value = "服务费")
	private BigDecimal fee;
	/**
	 * 交易类型
	 */
	@ApiModelProperty(name = "txType", value = "交易类型")
    @NotEmpty(message="CSH10008")
	@Length(max =2)
    private String txType; 
    /**
	 * 业务类型
	 */
	@ApiModelProperty(name = "busType", required = true, value = "业务类型 个人快捷充值:0101,个人线下充值:0102,个人营业厅充值:0103,企业网银充值:0104,条码消费:0201，" +
			"扫码付消费:0202,APP消费:0203,POS消费:0204,银行卡收单:0205 ,转到账户:0301,转银行卡:0302，当面收款:0303,个人提现:0401,商户提现:0402,充海币:0501,充流量:0801,充话费:0802")
    @NotEmpty(message="CSH10009")
	@Length(max =4)
    private String busType; 
    
    /**
	 * 订单系统渠道（来源：WEB APP）
	 */
	@ApiModelProperty(name = "sysChannel", value = "订单系统渠道（来源：WEB APP）")
    @NotEmpty(message="CSH10010")
	@Length(max =5)
    private String sysChannel;  
    
    /**
	 * 付款方id
	 */
	@Length(max =20)
    private String payerId;

	@ApiModelProperty(name = "payerName", value = "付款方名称")
    private String payerName;
    /**
	 * 收款方id
	 */
    @Length(max =20)
    private String payeeId;

	@ApiModelProperty(name = "payeeName", value = "收款方名称")
    @Length(max =255)
    private String payeeName;

	@NotEmpty(message="CSH10021")
	private String appCnl;

	@ApiModelProperty(name = "effTm", value = "订单失效时间")
	private LocalDateTime effTm;

    /**
     * 业务指定支付方式
     */
	@ApiModelProperty(name = "busPaytype", value = "业务指定支付方式")
	@Length(max =60)
    private String busPaytype;
	@ApiModelProperty(name = "goodsDesc", value = "订单商品描述")
	private String goodsDesc;

	/**
	 * 拓展字段：资金路径机构 用于国际化
	 */
	@ApiModelProperty(name = "crdCorpOrg", value = "资金机构")
	private String crdCorpOrg;

	/**
	 * 拓展字段：用于国际化
	 */
	@ApiModelProperty(name = "extInfo", value = "拓展字段：用于国际化")
	private String extInfo;

	@ApiModelProperty(name = "extMap", value = "拓展字段")
	private Map<String,Map<Object,Object>> extMap;

	@ApiModelProperty(name = "loginId", value = "登录Id")
	private String loginId;
  
	@ApiModelProperty(name = "remark", value = "备注")
	private String remark;
	
	public String getExtOrderNo() {
		return extOrderNo;
	}
	public void setExtOrderNo(String extOrderNo) {
		this.extOrderNo = extOrderNo;
	}
	public String getOrderCcy() {
		return orderCcy;
	}
	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}
	 
	public String getTxType() {
		return txType;
	}
	public void setTxType(String txType) {
		this.txType = txType;
	}
	 
	public BigDecimal getOrderAmt() {
		return orderAmt;
	}
	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}
	
	public String getBusType() {
		return busType;
	}
	public void setBusType(String busType) {
		this.busType = busType;
	}
	public String getSysChannel() {
		return sysChannel;
	}
	public void setSysChannel(String sysChannel) {
		this.sysChannel = sysChannel;
	}
	public String getPayerId() {
		return payerId;
	}
	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}
	public String getPayerName() {
		return payerName;
	}
	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}
	public String getPayeeId() {
		return payeeId;
	}
	public void setPayeeId(String payeeId) {
		this.payeeId = payeeId;
	}
	public String getPayeeName() {
		return payeeName;
	}
	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}
	public String getBusPaytype() {
		return busPaytype;
	}
	public void setBusPaytype(String busPaytype) {
		this.busPaytype = busPaytype;
	}
	public BigDecimal getFee() {
		return fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}

	public String getAppCnl() {
		return appCnl;
	}

	public void setAppCnl(String appCnl) {
		this.appCnl = appCnl;
	}

	public String getGoodsDesc() {
		return goodsDesc;
	}

	public void setGoodsDesc(String goodsDesc) {
		this.goodsDesc = goodsDesc;
	}

	public LocalDateTime getEffTm() {
		return effTm;
	}
     
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public void setEffTm(LocalDateTime effTm) {
		this.effTm = effTm;
	}

	public String getCrdCorpOrg() {
		return crdCorpOrg;
	}

	public void setCrdCorpOrg(String crdCorpOrg) {
		this.crdCorpOrg = crdCorpOrg;
	}

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

	public Map<String, Map<Object, Object>> getExtMap() {
		return extMap;
	}

	public void setExtMap(Map<String, Map<Object, Object>> extMap) {
		this.extMap = extMap;
	}

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }
}
