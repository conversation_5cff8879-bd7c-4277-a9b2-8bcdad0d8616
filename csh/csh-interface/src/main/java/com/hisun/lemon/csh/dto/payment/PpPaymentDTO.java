package com.hisun.lemon.csh.dto.payment;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
 

/**
 * 页面支付 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("页面支付 传输对象")
public class PpPaymentDTO extends AbstractNormalPaymentDTO{
	@ApiModelProperty(name = "orderNo", value = "订单号")
    @NotEmpty(message="CSH10001")
	@Length(max =24)
    private String orderNo;  
     

	@ApiModelProperty(name = "orderAmt", value = "金额")
    @Min(value=0, message="CSH10014")
	@Digits(integer=15,fraction=2,message="CSH10014")
    private BigDecimal crdPayAmt;


	@Length(max =20)
    private String payeeId; 
	@Length(max =4)
    private String payerId;

	
    @ApiModelProperty(name = "payPassword", value = "支付密码")
    @NotEmpty(message="CSH10012")
    private String payPassword;
    
    @ApiModelProperty(name = "crdPayType", value = "补款 类型   0无补款  1.网银 2.快捷")
    @NotEmpty(message="CSH10015")
    @Length(max =2)
    private Integer crdPayType; 
     
    @ApiModelProperty(name = "capCorgNo", value = "资金合作机构编号")
    @NotEmpty(message="CSH10018")
    @Length(max =20)
    private String capCorgNo;

	@ApiModelProperty(name = "validateRandom", value = "支付密码随机数")
	private String validateRandom;

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public BigDecimal getCrdPayAmt() {
		return crdPayAmt;
	}

	public void setCrdPayAmt(BigDecimal crdPayAmt) {
		this.crdPayAmt = crdPayAmt;
	}

	public String getPayeeId() {
		return payeeId;
	}

	public void setPayeeId(String payeeId) {
		this.payeeId = payeeId;
	}

	public String getPayerId() {
		return payerId;
	}

	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}

	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}

	public Integer getCrdPayType() {
		return crdPayType;
	}

	public void setCrdPayType(Integer crdPayType) {
		this.crdPayType = crdPayType;
	}

	public String getCapCorgNo() {
		return capCorgNo;
	}

	public void setCapCorgNo(String capCorgNo) {
		this.capCorgNo = capCorgNo;
	}

	public String getValidateRandom() {
		return validateRandom;
	}

	public void setValidateRandom(String validateRandom) {
		this.validateRandom = validateRandom;
	}
}
