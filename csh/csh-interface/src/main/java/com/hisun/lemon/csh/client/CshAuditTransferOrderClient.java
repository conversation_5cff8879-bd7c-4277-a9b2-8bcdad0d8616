package com.hisun.lemon.csh.client;

import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.*;
import com.hisun.lemon.csh.dto.order.ErrorOrderDTO;
import com.hisun.lemon.csh.dto.order.NotifyResultDTO;
import com.hisun.lemon.csh.dto.order.OrderDTO;
import com.hisun.lemon.csh.dto.payment.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收银台  收银服务接口
 * <AUTHOR>
 * @date 2017年6月27日
 * @time 下午3:06:23
 *
 */
@FeignClient("CSH")
public interface CshAuditTransferOrderClient {
	/**
	 * 业务模块调用收银，初始化收银订单
	 * @param reqDTO 请求参数
	 * @return 响应参数
	 */
    @PostMapping("/csh/auditOrder/queryCashierTranOrders")
    public GenericRspDTO<List<OrderDTO>> queryCashierTranOrders(@Validated @RequestBody GenericDTO<TranOrderReqDTO> reqDTO);

	/**
	 * 转账订单审核
	 * @param tranAuditOrderDTO 请求参数
	 */
	@PostMapping("/csh/auditOrder/audit")
	public GenericRspDTO<NoBody> audit(@Validated @RequestBody GenericDTO<TranOrderAuditDTO> tranAuditOrderDTO);

	/**
	 * 数币链上转账成功
	 * @param req 请求参数
	 */
	@PostMapping(value = "/csh/auditOrder/dm/transferOk")
	GenericRspDTO<NoBody> handleCregisOk(@Validated @RequestBody GenericDTO<HandleFinanceDTO> req);


	/**
	 * 数币提现成功
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/csh/auditOrder/dm/withdrawOk")
	GenericRspDTO<NoBody> handleWithdrawOk(@Validated @RequestBody GenericDTO<HandleFinanceDTO> req);
}
