package  com.hisun.lemon.csh.dto.refund;

import java.math.BigDecimal;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 撤销与退款配置 传输对象
 * 
 * <AUTHOR>
 * @date 2017年7月17日
 * @time 下午17:31:30
 *
 */
@ApiModel("退款")
public class RefundOrderDTO {
	/**
     * @Fields userId 内部用户号
     */
	@ApiModelProperty(name = "refundUserId", value = "内部用户号")
	//@NotEmpty(message = "CSH10026")
	@Length(max =32)
    private String refundUserId;
	/**
	 * @Fields payNo 外部模块退款订单号
	 */
	@ApiModelProperty(name = "busRdfOrdNo", value = "外部模块退款订单号")
	@NotEmpty(message = "CSH10023")
	@Length(max =32)
	private String busRdfOrdNo;
	 /**
     * @Fields mercName 商户名称
     */
	@ApiModelProperty(name = "mercName", value = "商户名称")
    private String mercName;
	/**
	 * @Fields extRefundNo 收银订单号
	 */
	@ApiModelProperty(name = "orginOrderNo", value = "收银订单号")
	@NotEmpty(message = "CSH10022")
	@Length(max =32)
	private String orginOrderNo;
	
	/**
	 * @Fields extRefundNo 商户编号
	 */
	@ApiModelProperty(name = "mercId", value = "商户订单号")
    private String  mercId;
	
	/**
	 * @Fields goodInfo 订单信息
	 */
	@ApiModelProperty(name = "goodInfo", value = "订单信息")
	private String goodInfo;
     
	
	/**
	 * @Fields rftOrdAmt 申请退款金额
	 */
	@ApiModelProperty(name = "rfdAmt", value = "申请退款金额")
	@NotNull(message="CSH10024")
	@DecimalMin(value ="0.01", message = "CSH10024")
	@Digits(integer=13, fraction=2,message = "CSH20106")
	private BigDecimal rfdAmt;
	 /**
     * @Fields txType 交易类型
     */
	@ApiModelProperty(name = "txType", value = "交易类型   退款06")
	@NotEmpty(message = "CSH20057")
	@Length(max =2)
    private String txType;
    /**
     * @Fields busType 业务类型   0601充值长款退款     0602消费退款    0603 消费失败退款    0604借贷退款
     */
	@ApiModelProperty(name = "busType", value = "业务类型        0601充值长款退款     0602消费退款    0603 消费失败退款    0604借贷退款   0605 缴费退款    0606 缴费失败退款")
	@NotEmpty(message = "CSH20058")
	@Length(max =4)
    private String busType;
	
	/**
	 * @Fields rfdReason 原因
	 */
	@ApiModelProperty(name = "rfdReason", value = "退款原因")
	@Length(max =200)
	private String rfdReason;
	public String getBusRdfOrdNo() {
		return busRdfOrdNo;
	}
	public void setBusRdfOrdNo(String busRdfOrdNo) {
		this.busRdfOrdNo = busRdfOrdNo;
	}
	public String getOrginOrderNo() {
		return orginOrderNo;
	}
	public void setOrginOrderNo(String orginOrderNo) {
		this.orginOrderNo = orginOrderNo;
	}
	public String getRfdReason() {
		return rfdReason;
	}
	public void setRfdReason(String rfdReason) {
		this.rfdReason = rfdReason;
	}
	public String getBusType() {
		return busType;
	}
	public String getRefundUserId() {
		return refundUserId;
	}
	public void setRefundUserId(String refundUserId) {
		this.refundUserId = refundUserId;
	}
	/**
	 * @Fields orderCcy 币种
	 */
	@ApiModelProperty(name = "orderCcy", value = "币种 ")
	@Length(max =4)
	private String orderCcy;
	public String getTxType() {
		return txType;
	}
	public void setTxType(String txType) {
		this.txType = txType;
	}

	public String getMercId() {
		return mercId;
	}
	public void setMercId(String mercId) {
		this.mercId = mercId;
	}
	public String getOrderCcy() {
		return orderCcy;
	}
	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}
	
	public void setBusType(String busType) {
		this.busType = busType;
	}
	public String getMercName() {
		return mercName;
	}
	public String getGoodInfo() {
		return goodInfo;
	}
	public void setGoodInfo(String goodInfo) {
		this.goodInfo = goodInfo;
	}
	public void setMercName(String mercName) {
		this.mercName = mercName;
	}
	public BigDecimal getRfdAmt() {
		return rfdAmt;
	}
	public void setRfdAmt(BigDecimal rfdAmt) {
		this.rfdAmt = rfdAmt;
	}
}
