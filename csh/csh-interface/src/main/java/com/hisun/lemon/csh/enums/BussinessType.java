package com.hisun.lemon.csh.enums;

public enum BussinessType {

	/**
	 * 充值   业务类型:线下汇款充值
	 */
	RECHARGE_OFFLINE("0102"),

	/**
	 * 充值   业务类型:营业厅充值
	 */
	RECHARGE_HALL("0103"),

	/**
	 * 条码消费
	 */
	CONSUME_BAR("0201"),
	/**
	 * 扫码消费
	 */
	CONSUME_SCAN("0202"),
	/**
	 * 消费 业务类型:APP消费
	 */
	CONSUME_APP("0203"),
	/**
	 * POS消费
	 */
	CONSUME_POS("0204"),
	/**
	 * 银行卡收单
	 */
	CONSUME_CARD("0205"),

	TRANSFER_BAL("0301"),
	TRANSFER_CARD("0302"),
	TRANSFER_FACE("0303"),
	/**
	 * 商户转账给用户
	 */
	TRANSFER_M_BAL("0304"),
	/**
	 * 退款   业务类型:值长充款退款
	 */
	RFD_LONG("0601"),
	
	/**
	 * 退款   业务类型:消费退款
	 */
	RFD_CONSUME("0602"),
	
	/**
	 * 退款   业务类型:消费失败退款
	 */
	RFD_CONSUME_FAIL("0603"),
	
	/**
	 * 退款   业务类型:借贷退款
	 */
	RFD_DC("0604"),

	/**
	 * 退款   业务类型:缴费退款
	 */
	RFD_PAYMENT("0605"),
	/**
	 * 退款   业务类型:缴费失败退款
	 */
	RFD_PAYMENT_FAIL("0606"),

	/**
	 * 退款   业务类型:理财退款
	 */
	RFD_FINANC("0607"),

	FINANC_IN("0701"),
	FINANC_OUT("0702"),
	
	/**
	 * 交易类型：充流量
	 */
	PAYMENT_FLOW("0801"),
	/**
	 * 交易类型：充话费
	 */
	PAYMENT_PHONE("0802"),

	/**
	 * 交易类型：生活缴费
	 */
	PAYMENT_LIFE("0803"),
	
	/**
	 * 消费撤销
	 */
	UNDO_CONSUME("0902"),
	
	/**
	 * 借贷撤销
	 */
	UNDO_DC("0903"),
	
	/**
	 * 缴费撤销
	 */
	UNDO_PAYMENT("0904"),

	/**
	 * 数币站内转账
	 */
	DM_TRANSFER("DZ01"),

	/**
	 * 数币链上转账
	 */
	DM_TRANSFER02("DZ02"),

	/**
	 * 数币链上白名单转账
	 */
	DM_TRANSFER03("DZ03"),

	/**
	 * 数币提现
	 */
	DM_WITHDRAW("DX01"),

	/**
	 * 数币充值
	 */
	DM_RECHARGE("DC01"),

	/**
	 * 数币收款
	 */
	DM_RECEIVE("DS01"),

	/**
	 * 兑换
	 */
	EXCHANGE_COIN("DH01");

	private String value;

	public String getValue() {
		return value;
	}

	BussinessType(String value) {
		this.value = value;
	}
}
