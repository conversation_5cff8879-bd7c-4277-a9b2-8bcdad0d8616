package com.hisun.lemon.csh.dto.payment;

import java.math.BigDecimal;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
 

/**
 * 理财支付 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("理财支付 传输对象")
public class InvPaymentDTO extends AbstractNormalPaymentDTO{
	@ApiModelProperty(name = "orderNo", value = "订单号")
    @NotEmpty(message="CSH10001")
	@Length(max =24)
    private String orderNo; 

    @ApiModelProperty(name = "invAmt", value = "理财金额")
    @Min(value=0, message="CSH10011")
	@Digits(integer=15,fraction=2,message="CSH10003")
    private BigDecimal invAmt;


    @NotEmpty(message="CSH10005")
    @Length(max =20)
    private String payeeId;
    @Length(max =20)
    private String payerId;

    @NotEmpty(message="CSH10012")
    private String payPassword;

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public BigDecimal getInvAmt() {
		return invAmt;
	}

	public void setInvAmt(BigDecimal invAmt) {
		this.invAmt = invAmt;
	}

	public String getPayeeId() {
		return payeeId;
	}

	public void setPayeeId(String payeeId) {
		this.payeeId = payeeId;
	}

	public String getPayerId() {
		return payerId;
	}

	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}

	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}
}
