package  com.hisun.lemon.csh.dto.paytype;

import java.util.List;

import com.hisun.lemon.framework.data.GenericDTO;
 

/**
 * 支付方式配置 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
public class PaytypeQueryDTO extends GenericDTO{ 
    private List<PaytypeDTO> datas;

	public List<PaytypeDTO> getDatas() {
		return datas;
	}

	public void setDatas(List<PaytypeDTO> datas) {
		this.datas = datas;
	} 
}
