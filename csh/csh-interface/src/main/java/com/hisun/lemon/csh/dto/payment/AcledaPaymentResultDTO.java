package com.hisun.lemon.csh.dto.payment;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

public class AcledaPaymentResultDTO extends PaymentResultDTO {

    @ApiModelProperty(name = "sessionId", value = "Acleda银行sessionId")
    private String sessionId;


    @ApiModelProperty(name = "paymentTokenId", value = "Acleda银行支付paymentTokenId")
    private String paymentTokenId;

    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    @ApiModelProperty(name = "feeFlag", value = "扣费方式")
    private String feeFlag;


    @ApiModelProperty(name = "orderNo", value = "收银台订单号")
    private String orderNo;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getPaymentTokenId() {
        return paymentTokenId;
    }

    public void setPaymentTokenId(String paymentTokenId) {
        this.paymentTokenId = paymentTokenId;
    }


    public String getFeeFlag() {
        return feeFlag;
    }

    public void setFeeFlag(String feeFlag) {
        this.feeFlag = feeFlag;
    }

    @Override
    public String getOrderNo() {
        return orderNo;
    }

    @Override
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }
}
