package com.hisun.lemon.csh.dto.cashier;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;


/**
 * 审核订单信息 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("审核订单信息")
@ClientValidated
public class TranOrderAuditDTO {

	@ApiModelProperty(name = "orderNo", value = "订单号")
	@NotEmpty(message = "CPI10021")
	private String orderNo;

	/**
	 * 审核状态 A1：审核通过 S：复核通过  F1：审核拒绝 F2:复核拒绝
	 */
	@ApiModelProperty(name = "status", value = "审核状态")
	@NotEmpty(message = "CPI10021")
	private String status;

	@ApiModelProperty(name = "reason", value = "原因")
	private String reason;

	@ApiModelProperty(name = "userId", value = "操作人")
	@NotEmpty(message = "CPI10021")
	private String userId;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}
}
