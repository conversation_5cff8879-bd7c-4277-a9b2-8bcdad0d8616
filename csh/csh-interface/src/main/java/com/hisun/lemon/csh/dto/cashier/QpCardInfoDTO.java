package com.hisun.lemon.csh.dto.cashier;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by <PERSON><PERSON> on 2017/8/7.
 */
@ApiModel("快捷支付 银行卡信息")
public class QpCardInfoDTO {
    /**
     * 内部协议号
     * @mbggenerated
     */
    @ApiModelProperty(name = "agrNo", value = "内部协议号", dataType = "String")
    private String agrNo;


    /**
     * 用户号
     * @mbggenerated
     */
    @ApiModelProperty(name = "userId", value = "用户id", dataType = "String")
    private String userId;

    /**
     * 手机号
     * @mbggenerated
     */
    @ApiModelProperty(name = "mblNo", value = "手机号", dataType = "String")
    private String mblNo;


    /**
     * 资金机构
     * @mbggenerated
     */
    @ApiModelProperty(name = "crdCorpOrg", value = "资金机构", dataType = "String")
    private String crdCorpOrg;


    /**
     * 签约协议号
     * @mbggenerated
     */
    @ApiModelProperty(name = "signAgrno", value = "银行协议号", dataType = "String")
    private String signAgrno;

    /**
     * 卡类型，D：借记卡，C：贷记卡
     * @mbggenerated
     */
    @ApiModelProperty(name = "agrDirect", value = "卡类型，D：借记卡，C：贷记卡", dataType = "String")
    private String crdAcTyp;

    /**
     * 银行卡后四位
     * @mbggenerated
     */
    @ApiModelProperty(name = "crdNoLast", value = "银行卡后四位", dataType = "String")
    private String crdNoLast;

    /**
     * 账户名
     * @mbggenerated
     */
    @ApiModelProperty(name = "crdUsrNm", value = "账户名", dataType = "String")
    private String crdUsrNm;


    /**
     * 备注
     * @mbggenerated
     */
    @ApiModelProperty(name = "rmk", value = "备注", dataType = "String")
    private String rmk;


    @ApiModelProperty(name = "crdNoEnc", value = "银行卡加密卡号", dataType = "String")
    private String crdNoEnc;

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }


    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }


    public String getSignAgrno() {
        return signAgrno;
    }

    public void setSignAgrno(String signAgrno) {
        this.signAgrno = signAgrno;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdNoLast() {
        return crdNoLast;
    }

    public void setCrdNoLast(String crdNoLast) {
        this.crdNoLast = crdNoLast;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }
}
