package com.hisun.lemon.csh.dto.paytype;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 支付方式查询结果 传输对象
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("支付方式查询结果")
public class AppPaytypeResultDTO {

	/**
	 * 支付方式
	 */
	@ApiModelProperty(name = "payType", value = "支付方式")
    private String payType;


	/**
	 * 快捷支付  借记卡银行卡
	 */
	@ApiModelProperty(name = "dCards", value = "快捷支付  借记卡银行卡")
	private List dCards;
	/**
	 * 快捷支付  贷记卡银行卡
	 */
	@ApiModelProperty(name = "cCards", value = "快捷支付  贷记卡银行卡")
	private List cCards;


	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public List getdCards() {
		return dCards;
	}

	public void setdCards(List dCards) {
		this.dCards = dCards;
	}

	public List getcCards() {
		return cCards;
	}

	public void setcCards(List cCards) {
		this.cCards = cCards;
	}
}
