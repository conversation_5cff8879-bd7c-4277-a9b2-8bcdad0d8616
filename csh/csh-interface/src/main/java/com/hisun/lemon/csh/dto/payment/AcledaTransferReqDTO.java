package com.hisun.lemon.csh.dto.payment;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AcledaTransferReqDTO {

    @ApiModelProperty(name = "orderNo", value = "业务订单号")
    @NotEmpty(message="CSH10001")
    @Length(max =24)
    private String busOrderNo;

    @ApiModelProperty(name = "mblNo", value = "手机号")
    @NotEmpty(message="CSH10020")
    @Length(max =20)
    private String mblNo;

    @ApiModelProperty(name = "crdPayAmt", value = "补款金额")
    @Min(value=0, message="CSH10014")
    private BigDecimal crdPayAmt;

    @Length(max =20)
    private String payeeId;
    @Length(max =20)
    private String payerId;

    /**
     * 订单系统渠道（来源：WEB APP）
     */
    @ApiModelProperty(name = "sysChannel", value = "订单系统渠道（来源：WEB APP）")
    @NotEmpty(message="CSH10010")
    @Length(max =5)
    private String sysChannel;

    /**
     * 持卡人用户名
     */
    @ApiModelProperty(name = "crdUserNm", value = "持卡人用户名")
    private String crdUserNm;

    /**
     * 卡种
     */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种")
    private String crdAcTyp;

    /**
     * 卡资金机构
     */
    @ApiModelProperty(name = "crdCorpOrg", value = "卡资金机构")
    private String crdCorpOrg;

    @ApiModelProperty(name = "lastCardNo", value = "支付银行卡后四位")
    private String lastCardNo;

    /**
     * ACLEDA资金流入方向
     */
    @ApiModelProperty(name = "direction", value = "IN- 1  OUT-2", required = true, dataType = "String")
    @NotEmpty(message="CSH10080")
    private int direction;

    /**
     * 币种，USD美元
     */
    @ApiModelProperty(name = "ordCcy", value = "币种，USD美元", required = true, dataType = "String")
    private String ordCcy;

    @ApiModelProperty(name = "busType", required = true, value = "业务类型 个人快捷充值:0101,个人线下充值:0102,个人营业厅充值:0103,企业网银充值:0104,条码消费:0201，" +
            "扫码付消费:0202,APP消费:0203,POS消费:0204,银行卡收单:0205 ,转到账户:0301,转银行卡:0302，当面收款:0303,个人提现:0401,商户提现:0402,充海币:0501,充流量:0801,充话费:0802")
    @NotEmpty(message="CSH10009")
    protected String busType;

    @ApiModelProperty(name = "effTm", value = "订单失效时间")
    private LocalDateTime effTm;

    @ApiModelProperty(name = "goodsDesc", value = "商品订单信息描述")
    private String goodsDesc;

    @NotEmpty(message="CSH10021")
    private String appCnl;

    @ApiModelProperty(name = "txType", value = "交易类型   充值 01")
    @NotEmpty(message = "CSH10008")
    @Length(max =2)
    private String txType;

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public BigDecimal getCrdPayAmt() {
        return crdPayAmt;
    }

    public void setCrdPayAmt(BigDecimal crdPayAmt) {
        this.crdPayAmt = crdPayAmt;
    }

    public String getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(String payeeId) {
        this.payeeId = payeeId;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getCrdUserNm() {
        return crdUserNm;
    }

    public void setCrdUserNm(String crdUserNm) {
        this.crdUserNm = crdUserNm;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getLastCardNo() {
        return lastCardNo;
    }

    public void setLastCardNo(String lastCardNo) {
        this.lastCardNo = lastCardNo;
    }

    public int getDirection() {
        return direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public LocalDateTime getEffTm() {
        return effTm;
    }

    public void setEffTm(LocalDateTime effTm) {
        this.effTm = effTm;
    }

    public String getSysChannel() {
        return sysChannel;
    }

    public void setSysChannel(String sysChannel) {
        this.sysChannel = sysChannel;
    }

    public String getGoodsDesc() {
        return goodsDesc;
    }

    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }

    public String getAppCnl() {
        return appCnl;
    }

    public void setAppCnl(String appCnl) {
        this.appCnl = appCnl;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }
}
