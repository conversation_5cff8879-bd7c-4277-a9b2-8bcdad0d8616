buildscript {
    ext {
        springBootVersion = '1.5.4.RELEASE'
    }
    repositories {
//        mavenCentral()
//        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven {
            url "http://*************:8081/repository/maven-public/"
            credentials {
                username 'admin'
                password 'hq1q2w3e$R'
            }
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

apply plugin: 'java'
apply plugin: 'eclipse-wtp'
apply plugin: 'idea'
apply plugin: 'org.springframework.boot'
//apply plugin: 'war'
apply plugin: 'maven'  // 添加这行

version = '0.0.1-SNAPSHOT'
group = 'com.hisun.tms'
sourceCompatibility = 1.8

repositories {
//    mavenCentral()
//    maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
    maven {
        url "http://*************:8081/repository/maven-public/"
        credentials {
            username 'admin'
            password 'hq1q2w3e$R'
        }
    }
}

configurations {
    providedRuntime
}

dependencies {
    // spring boot
    compile('org.springframework.boot:spring-boot-starter-web')
    compile('org.springframework.boot:spring-boot-starter-data-jpa')
    compile('org.springframework.boot:spring-boot-starter-security')
    compile('org.springframework.boot:spring-boot-starter-tomcat')
    compile('org.springframework.boot:spring-boot-starter-actuator')
    compile('org.springframework.boot:spring-boot-starter-mail')

    // database
    runtime('mysql:mysql-connector-java')

    // thymeleaf
    compile('org.thymeleaf:thymeleaf:3.0.6.RELEASE')
    compile('org.thymeleaf:thymeleaf-spring4:3.0.6.RELEASE')
    compile('org.thymeleaf.extras:thymeleaf-extras-springsecurity4:3.0.2.RELEASE')
    compile('nz.net.ultraq.thymeleaf:thymeleaf-layout-dialect:2.2.2')

    // util
    compile('org.apache.commons:commons-lang3:3.5')
    compile("org.apache.httpcomponents:httpclient:4.5.3")
    compile("org.apache.httpcomponents:httpmime:4.5.3")
    compile("commons-codec:commons-codec:1.10")
    compile('commons-beanutils:commons-beanutils:1.9.3')
    compile('com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.8.9')
    compile('com.google.guava:guava:22.0')
    compile("com.google.code.gson:gson:2.8.1")
    compile('com.github.darrachequesne:spring-data-jpa-datatables:4.1')

    // activiti
//    compile('org.activiti:activiti-spring-boot-starter-basic:5.22.0')
//    compile ('org.activiti:activiti-spring-boot-starter-rest-api:5.22.0')
//    compile ('org.activiti:activiti-spring-boot-starter-actuator:5.22.0')
//    compile ('org.activiti:activiti-spring-boot-starter-jpa:5.22.0')

    // spring cloud
    compile('org.springframework.cloud:spring-cloud-starter-eureka:1.3.2.RELEASE')
    compile('org.springframework.cloud:spring-cloud-starter-feign:1.3.2.RELEASE')
//    compile('com.netflix.feign:feign-httpclient:8.18.0')
    compile('io.github.openfeign:feign-httpclient:9.5.1')

    // lemon
    compile("com.hisun:lemon-framework")
    compile("com.hisun:lemon-framework:1.0.1-SNAPSHOT")
    compile ("com.hisun:jcommon:1.0.1-SNAPSHOT")
    compile ("com.hisun:lemon-framework-idgen:1.0.1-SNAPSHOT")
    compile ("com.hisun:lemon-framework-core:1.0.1-SNAPSHOT")
    compile ("com.hisun:lemon-framework-schedule:1.0.1-SNAPSHOT")

    // lemon interface
    compile("com.hisun:cpi-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:bil-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:csh-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:mkm-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:rsm-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:cmm-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:cpo-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:urm-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:onr-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:chk-interface:1.0.1-SNAPSHOT")
    compile("com.hisun:acm-interface:1.0.1-SNAPSHOT")
    compile("com.hisun:tam-interface:1.0.0-SNAPSHOT")

    // test
    testCompile('org.springframework.boot:spring-boot-starter-test')

    //集成mybatise
    compile("org.mybatis.spring.boot:mybatis-spring-boot-starter:1.3.0")
    compile("org.mybatis:mybatis-typehandlers-jsr310:1.0.2")
    compile 'com.github.pagehelper:pagehelper:5.1.1'

    //poi 生成excel，支持office 2007以上版本
    compile("org.apache.poi:poi:3.16")
    compile("org.apache.poi:poi-ooxml:3.16")
    compile ("com.google.zxing:core:3.2.1")

    //jxl 生成excel，支持office 2003以下版本
    compile("net.sourceforge.jexcelapi:jxl:2.6.12")

}


uploadArchives {
    repositories {
        mavenDeployer {
            snapshotRepository(url: "http://*************:8081/repository/maven-snapshots/") {
                authentication(userName:'admin', password:'hq1q2w3e$R')
            }
            repository(url: "http://*************:8081/repository/maven-releases/") {
                authentication(userName:'admin', password:'hq1q2w3e$R')
            }
        }
    }
}
