# 后台管理系统

## 运行环境

- JDK 8
- Gradle
- MySQL

## 登录账号

- admin/admin

## 主要技术

- Spring boot && Spring security && Spring data JPA
- thymeleaf
- admin Template：Inspinia
- datatables && datatables plugin(editor、select、button)
- i18next
- activiti

## demo开发

```mysql
DROP TABLE IF EXISTS `sys_example`;
CREATE TABLE `sys_example` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '姓名',
    `position` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '位置',
    `office` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '公司',
    `start_date` DATETIME NOT NULL COMMENT '入职日期',
    `salary` DECIMAL(15 , 2 ) NOT NULL DEFAULT 0 COMMENT '薪水',
    `gmt_create` DATETIME NOT NULL COMMENT '记录生成时间',
    `gmt_modified` DATETIME NOT NULL COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
)  COMMENT='样例表';

INSERT INTO `sys_example`(name,position,office,start_date,salary,gmt_create,gmt_modified) 
VALUES
('张三','长沙','高阳',NOW(),100,NOW(),NOW()),
('李四','广州','高阳',NOW(),1000,NOW(),NOW());

#根据页面使用的权限控制key初始化resource表
INSERT INTO resource (resource_name,resource,parent_id) VALUES 
	('示例','/demo',0);
INSERT INTO resource (resource_name,resource,parent_id) VALUES 
	('datatables示例','/demo/datatables',4);
```