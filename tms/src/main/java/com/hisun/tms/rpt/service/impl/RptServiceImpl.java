package com.hisun.tms.rpt.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.ReflectionUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.tms.rpt.Utils.Utils;
import com.hisun.tms.rpt.common.Constants;
import com.hisun.tms.rpt.model.RptModel;
import com.hisun.tms.rpt.model.RptRsModel;
import com.hisun.tms.rpt.repository.DataTablesRptRepository;
import com.hisun.tms.rpt.repository.DataTablesRptRsRepository;
import com.hisun.tms.rpt.repository.RptRepository;
import com.hisun.tms.rpt.repository.RptRsRepository;
import com.hisun.tms.rpt.service.RptService;
import com.hisun.tms.specifications.Specifications;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Service
public class RptServiceImpl implements RptService {

    public static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final Logger logger = LoggerFactory.getLogger(RptServiceImpl.class);
    @Resource
    private DataTablesRptRepository dataTablesRptRepository;
    @Resource
    private DataTablesRptRsRepository dataTablesRptRsRepository;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private RptRepository rptRepository;
    @Resource
    private RptRsRepository rptRsRepository;

    @Override
    public DataTablesOutput<RptModel> findAll(DataTablesInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        DataTablesOutput<RptModel> dataTablesOutput = dataTablesRptRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    public void save(RptModel rptModel) {

        rptModel.setCreateTime(LocalDateTime.now().format(DEFAULT_DATETIME_FORMATTER));
        rptModel.setModifyTime(LocalDateTime.now().format(DEFAULT_DATETIME_FORMATTER));
        //TODO 操作员id
        //rptModel.setUpdOprId("1233");
        rptRepository.save(rptModel);
    }

    @Override
    public void update(RptModel rptModel) {
        RptModel temp = rptRepository.findOne(rptModel.getReportId());
        String userId = LemonUtils.getUserId();
        logger.info("=++++++++++++++++++++++++++= : " + userId);

        temp.setModifyTime(LocalDateTime.now().format(DEFAULT_DATETIME_FORMATTER));
        temp.setUpdateUser(userId);
        temp.setReportName(rptModel.getReportName());
        temp.setReportDate(rptModel.getReportDate());
        temp.setReportModule(rptModel.getReportModule());
        temp.setReportClass(rptModel.getReportClass());
        temp.setReportMathon(rptModel.getReportMathon());
        temp.setDenseLevel(rptModel.getDenseLevel());
        temp.setMemo(rptModel.getMemo());
        temp.setReportPath(rptModel.getReportPath());
        temp.setReportPeriod(rptModel.getReportPeriod());
        temp.setReportSts(rptModel.getReportSts());
        temp.setReportPam(rptModel.getReportPam());
        temp.setReportFilename(rptModel.getReportFilename());

        //temp.setCpnNm(rptModel.getCpnNm());
        //temp.setDcPtyFlg(rptModel.getDcPtyFlg());
        //temp.setRuleDesc(rptModel.getRuleDesc());
        //temp.setRuleNm(rptModel.getRuleNm());
        //temp.setRuleTyp(rptModel.getRuleTyp());
        //TODO 操作员id
        //temp.setUpdOprId("123");
        rptModel = rptRepository.save(temp);
        logger.info("sdf");
    }


    /**
     * 调用各模块生成报表
     */
    @Override
    @Transactional("rptTransactionManager")
    public void createRpt() {
        DataTablesInput dataTablesInput = new DataTablesInput();
        LocalDate acDt = LemonUtils.getAccDate();
        if (acDt == null) {
            acDt = DateTimeUtils.getCurrentLocalDate();
        }
        String acDtStr = DateTimeUtils.formatLocalDate(acDt);
        String flg = "";//周期标识

        //1.查询报表生成数据
        Specifications specifications = new Specifications();
        Specification<RptModel> orderQueryParam1 = findByCondition("0");
        List<RptModel> list = dataTablesRptRepository.findAll(orderQueryParam1);
        if (list == null || list.isEmpty()) {
            logger.info("No report to run");
            return;
        }

        //2.确认执行周期
        flg = createPeriod(acDt);

        //3.遍历
        for (int i = 0; i < list.size(); i++) {

            RptModel rptModel = (RptModel) list.get(i);
            String rptId = rptModel.getReportId();
            String rptPeriod = rptModel.getReportPeriod();
            String module = rptModel.getReportModule();
            String rptTime = rptModel.getReportDate().replaceAll("-", "");
            String rptClass = rptModel.getReportClass();
            String rptmathon = rptModel.getReportMathon();
            String reportPam = rptModel.getReportPam();
            String reportPath = rptModel.getReportPath();
           //String fileName = rptModel.getReportFilename();
           //rptModel.setReportFilename(rptModel.getReportFilename().replace("yyyyMMdd", acDtStr));

            Class[] pamClass = new Class[0];
            Object[] pamStr = new String[0];

            RptRsModel rptRsModel = new RptRsModel();

            BeanUtils.copyProperties(rptRsModel, rptModel);
            rptRsModel.setRunDt(acDtStr);
            Specification<RptRsModel> orderQueryParam2 = findByCondition(acDtStr, rptId);
            List<RptRsModel> listRs = dataTablesRptRsRepository.findAll(orderQueryParam2);
            if (listRs != null && !listRs.isEmpty()) {
                continue;
            }
            try {
                if (flg.indexOf("@" + rptPeriod + "@" ) > 1 || flg.indexOf(rptTime) > 1) { //是否符合执行日期条件
                    //路径创建
                    Utils.createDir(reportPath);
                    Class invokeClass = ReflectionUtils.forName(rptClass);
                    if (reportPam != null && !"".equals(reportPam)) {
                        Map map = Utils.objectToMap(rptModel);
                        pamStr = reportPam.split(",");
                        pamClass = new Class[pamStr.length];
                        for (int j = 0; j < pamStr.length; j++) {
                            pamClass[j] = String.class;
                            if ("runDt".equals(pamStr[j])) {
                                pamStr[j] = acDtStr;
                            } else {
                                pamStr[j] = ((String) map.get(pamStr[j])) == null ? "" : ((String) map.get(pamStr[j])).replace("yyyyMMdd", acDtStr);
                            }
                        }
                    }
                    //rptModel.setReportFilename(fileName);
                    String beanName = rptClass.substring(rptClass.lastIndexOf(".") + 1, rptClass.length());
                    beanName = beanName.substring(0, 1).toLowerCase() + beanName.substring(1);
                    Object beanObject = applicationContext.getBean(beanName);

                    Method invokeMathon = invokeClass.getMethod(rptmathon, pamClass);
                    invokeMathon.invoke(beanObject, pamStr);
                    //Map beans = ExtensionLoader.getSpringBeansOfType(invokeClass);
                    //Object o = beans.get(invokeClass);
                    //Method invokeMathon = invokeClass.getMethod(rptmathon, pamClass);
                    //invokeMathon.invoke(o, pamStr);
                    rptRsModel.setRunRs(Constants.RUN_SUCCESS);
                    rptRsModel.setReportFilename(rptModel.getReportFilename().replace("yyyyMMdd", acDtStr));
                    logger.info("ReportFileName1: " + rptModel.getReportFilename());
                    dataTablesRptRsRepository.save(rptRsModel);
                } else {
                    continue;
                }
            } catch (Exception e) {
                rptRsModel.setRunRs(Constants.RUN_FAIL);
                rptRsModel.setReportFilename(rptModel.getReportFilename().replace("yyyyMMdd", acDtStr));
                dataTablesRptRsRepository.save(rptRsModel);
                logger.error("");
            }
        }
    }

    private Specification<RptModel> findByCondition(String sts) {
        Specification<RptModel> orderQueryParam = new Specification<RptModel>() {
            @Override
            public Predicate toPredicate(Root<RptModel> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("reportSts"), sts));
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return orderQueryParam;
    }

    /**
     * 创建报表周期常量
     *
     * @param acDt 会计日期
     * @return
     */
    public String createPeriod(LocalDate acDt) {
        String flg = "";
        String dayStr = "";
        String monthStr = "";
        int dayVal = acDt.getDayOfMonth();
        int monthVal = acDt.getMonthValue();
        int yearVal = acDt.getYear();
        if (dayVal < 10) {
            dayStr = "0" + dayVal;
        } else {
            dayStr = dayVal + "";
        }
        if (monthVal < 10) {
            monthStr = "0" + monthVal;
        } else {
            monthStr = monthVal + "";
        }
        flg = flg + "@FM" + dayStr;//FM每月定期
        flg = flg + "@FY" + monthStr + dayStr;//FY:每年定期
        flg = flg + "@R" + yearVal + monthStr + dayStr;//R:不定期(特定日期执行一次
        if (dayVal == 1 && monthVal == 1) {//1月1日 年、半年、季、月、日
            flg = flg + "@Y@H@S@M@D@";
        } else if (dayVal == 1 && monthVal == 7) {//7月1日 半年、季、月、日
            flg = flg + "@H@S@M@D@";
        } else if (dayVal == 1 && (monthVal == 4 || monthVal == 10)) {//4月1日 10月1日 季、月、日
            flg = flg + "@S@M@D@";
        } else if (dayVal == 1) {//剩余月份1日
            flg = flg + "@M@D@";
        } else {
            flg = flg + "@D@";
        }
        return flg;
    }

    private Specification<RptRsModel> findByCondition(String acDt, String reportId) {
        Specification<RptRsModel> orderQueryParam = new Specification<RptRsModel>() {
            @Override
            public Predicate toPredicate(Root<RptRsModel> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("runDt"), acDt));
                predicates.add(cb.equal(root.get("reportId"), reportId));
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return orderQueryParam;
    }

}
