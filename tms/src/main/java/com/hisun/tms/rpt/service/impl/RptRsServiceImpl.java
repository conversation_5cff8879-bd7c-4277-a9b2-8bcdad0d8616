package com.hisun.tms.rpt.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.ReflectionUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.tms.rpt.Utils.Utils;
import com.hisun.tms.rpt.common.Constants;
import com.hisun.tms.rpt.model.RptModel;
import com.hisun.tms.rpt.model.RptRsFindInput;
import com.hisun.tms.rpt.model.RptRsModel;
import com.hisun.tms.rpt.repository.DataTablesRptRepository;
import com.hisun.tms.rpt.repository.DataTablesRptRsRepository;
import com.hisun.tms.rpt.repository.RptRepository;
import com.hisun.tms.rpt.repository.RptRsRepository;
import com.hisun.tms.rpt.service.RptRsService;
import com.hisun.tms.rpt.service.RptService;
import com.hisun.tms.specifications.Specifications;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Service
@Transactional("rptTransactionManager")
public class RptRsServiceImpl implements RptRsService {

    private static final Logger logger = LoggerFactory.getLogger(RptRsServiceImpl.class);

    @Resource
    private DataTablesRptRsRepository dataTablesRptRsRepository;

    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private RptRsRepository rptRsRepository;
    @Resource
    private RptRepository rptRepository;

    @Override
    public DataTablesOutput<RptRsModel> findAll(RptRsFindInput dataTablesInput) {

        Specification<RptRsModel> orderQueryParam = findByCondition2(dataTablesInput);
        DataTablesOutput<RptRsModel> dataTablesOutput = dataTablesRptRsRepository.findAll(dataTablesInput,
                orderQueryParam);
        // List<RptRsModel> list = dataTablesRptRsRepository.findAll(orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    public Map reRunRpt(String runDt, int rptId) {
        Map mapRes = new HashMap();
        // boolean rs = true;
        Specification<RptRsModel> orderQueryParam = findByCondition1(rptId);
        List<RptRsModel> list = dataTablesRptRsRepository.findAll(orderQueryParam);
        if (list == null || list.isEmpty()) {
            mapRes.put("sts", Constants.RUN_FAIL);
            return mapRes;
        }
        RptRsModel rptRsModel = (RptRsModel) list.get(0);
        rptRsModel.setRunRs("2");
        rptRsRepository.save(rptRsModel);
        String reportId = rptRsModel.getReportId();
        RptModel rptModelNew = new RptModel();
        RptModel rptModel = rptRepository.findOne(reportId);
        String rptClass = rptModel.getReportClass();
        String reportPam = rptModel.getReportPam();
        String rptmathon = rptModel.getReportMathon();
        String reportPath = rptModel.getReportPath();
        BeanUtils.copyProperties(rptModelNew, rptModel);

        rptModelNew.setReportFilename(rptModel.getReportFilename().replace("yyyyMMdd", runDt));
        Class[] pamClass = new Class[0];
        Object[] pamStr = new String[0];
        try {
            // 路径创建
            Utils.createDir(reportPath);
            Class invokeClass = ReflectionUtils.forName(rptClass);
            if (reportPam != null && !"".equals(reportPam)) {
                Map map = Utils.objectToMap(rptModelNew);
                pamStr = reportPam.split(",");
                pamClass = new Class[pamStr.length];
                for (int j = 0; j < pamStr.length; j++) {
                    pamClass[j] = String.class;
                    if ("runDt".equals(pamStr[j])) {
                        pamStr[j] = runDt;
                    } else {
                        pamStr[j] = ((String) map.get(pamStr[j])) == null ? "" : ((String) map.get(pamStr[j]));
                    }

                }
            }
            // Map beans = applicationContext.getBeansOfType(invokeClass);
            // String beanName = rptClass.substring(rptClass.lastIndexOf(".") + 1
            // ,rptClass.length());
            // beanName = beanName.substring(0).toLowerCase() + beanName.substring(1,
            // beanName.length());
            // Map beans = ExtensionLoader.getSpringBeansOfType(invokeClass);
            // if(beans != null && !beans.isEmpty()){
            // LinkedHashMap map = (LinkedHashMap)beans.get(0);
            // Object o = map.get(beanName);
            String beanName = rptClass.substring(rptClass.lastIndexOf(".") + 1, rptClass.length());
            beanName = beanName.substring(0, 1).toLowerCase() + beanName.substring(1);
            Object beanObject = applicationContext.getBean(beanName);

            Method invokeMathon = invokeClass.getMethod(rptmathon, pamClass);
            invokeMathon.invoke(beanObject, pamStr);
            rptRsModel.setReportFilename(rptModelNew.getReportFilename());
            rptRsModel.setReportPath(rptModelNew.getReportPath());
            rptRsModel.setRunRs(Constants.RUN_SUCCESS);

            mapRes.put("fileName", rptModelNew.getReportFilename());
            mapRes.put("sts", Constants.RUN_SUCCESS);
            /*
             * String filename =
             * rptModel.getReportPath().substring(rptModel.getReportPath().lastIndexOf("/")
             * + 1, rptModel.getReportPath().length());
             * filename = filename.replaceAll("yyyyMMdd", runDt);
             * rptRsModel.setReportFilename(filename);
             */
            // }
            // Object o = beans.get(invokeClass);
        } catch (Exception e) {
            // rs = false;
            rptRsModel.setRunRs(Constants.RUN_FAIL);
            mapRes.put("sts", Constants.RUN_FAIL);
        }
        rptRsRepository.save(rptRsModel);
        return mapRes;

    }

    private Specification<RptRsModel> findByCondition1(int id) {
        Specification<RptRsModel> orderQueryParam = new Specification<RptRsModel>() {
            @Override
            public Predicate toPredicate(Root<RptRsModel> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("id"), id));
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return orderQueryParam;
    }

    private Specification<RptRsModel> findByCondition2(RptRsFindInput dataTablesInput) {
        Specification<RptRsModel> orderQueryParam = new Specification<RptRsModel>() {
            @Override
            public Predicate toPredicate(Root<RptRsModel> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                String id = dataTablesInput.getExtra_search().get("reportId");
                String md = dataTablesInput.getExtra_search().get("md");
                String period = dataTablesInput.getExtra_search().get("period");
                String endTime = dataTablesInput.getExtra_search().get("endTime").replaceAll("-", "");
                String beginTime = dataTablesInput.getExtra_search().get("beginTime").replaceAll("-", "");
                if (id != null && !"".equals(id)) {
                    predicates.add(cb.equal(root.get("reportId"), id));
                }
                if (md != null && !"".equals(md)) {
                    predicates.add(cb.equal(root.get("reportModule"), md));
                }
                if (period != null && !"".equals(period)) {
                    predicates.add(cb.equal(root.get("reportPeriod"), period));
                }
                if (endTime != null && !"".equals(endTime)) {
                    predicates.add(cb.lessThanOrEqualTo(root.get("runDt"), endTime));
                }
                if (beginTime != null && !"".equals(beginTime)) {
                    predicates.add(cb.greaterThanOrEqualTo(root.get("runDt"), beginTime));
                }
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return orderQueryParam;
    }

    public RptRsModel getRsFile(int rptId) {
        Specification<RptRsModel> orderQueryParam = findByCondition1(rptId);
        List<RptRsModel> list = dataTablesRptRsRepository.findAll(orderQueryParam);
        if (list == null || list.isEmpty()) {
            return null;
        }
        return (RptRsModel) list.get(0);
    }
}
