package com.hisun.tms.rpt.model;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Entity
@Table(name = "l_report")
public class RptModel {

    /**
     * @Fields reportId 报表编号 模块名_报表生成周期_编号 INV_D_001
     */
    @Id
    @Column(name = "report_id")
    private String reportId;

    /**
     * @Fields reporSts 状态 0:生效, 1:失效
     */
    @Column(name = "report_sts")
    private String reportSts;

    /**
     * @Fields reportName 报表名称
     */
    @Column(name = "report_name")
    private String reportName;

    /**
     * @Fields reportModule 报表所属模块
     */
    @Column(name = "report_module")
    private String reportModule;

    /**
     * @Fields reportPeriod D:日 W:周 T:旬 M:月 Q:季 H:半年 Y:年 FM:每月定期 FY:每年定期 R:不定期(特定日期执行一次) O:其他
     */
    @Column(name = "report_period")
    private String reportPeriod;

    @Column(name = "report_date")
    private String reportDate;

    /**
     * @Fields reportFilename 报表文件名
     */
    @Column(name = "report_filename")
    private String reportFilename;

    /**
     * @Fields reportClass 接口类
     */
    @Column(name = "report_class")
    private String reportClass;

    /**
     * @Fields reportMathon 接口方法
     */
    @Column(name = "report_mathon")
    private String reportMathon;

    /**
     * @Fields reportMathon 接口参数
     */
    @Column(name = "report_pam")
    private String reportPam;

    /**
     * @Fields reportPath 请求路径 支持不同服务、应用间调用
     */
    @Column(name = "report_path")
    private String reportPath;

    /**
     * @Fields denseLevel 密级 0-高 1-中 2-低
     */
    @Column(name = "dense_level")
    private String denseLevel;

    /**
     * @Fields memo 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * @Fields createUser 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * @Fields updateUser 修改人
     */
    @Column(name = "update_user")
    private String updateUser;


    @Column(name = "create_time")
    private String createTime;

    @Column(name = "modify_time")
    private String modifyTime;

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getReportSts() {
        return reportSts;
    }

    public void setReportSts(String reportSts) {
        this.reportSts = reportSts;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public String getReportModule() {
        return reportModule;
    }

    public void setReportModule(String reportModule) {
        this.reportModule = reportModule;
    }

    public String getReportPeriod() {
        return reportPeriod;
    }

    public void setReportPeriod(String reportPeriod) {
        this.reportPeriod = reportPeriod;
    }

    public String getReportDate() {
        return reportDate;
    }

    public void setReportDate(String reportDate) {
        this.reportDate = reportDate;
    }

    public String getReportClass() {
        return reportClass;
    }

    public void setReportClass(String reportClass) {
        this.reportClass = reportClass;
    }

    public String getReportMathon() {
        return reportMathon;
    }

    public void setReportMathon(String reportMathon) {
        this.reportMathon = reportMathon;
    }

    public String getReportPath() {
        return reportPath;
    }

    public void setReportPath(String reportPath) {
        this.reportPath = reportPath;
    }

    public String getDenseLevel() {
        return denseLevel;
    }

    public void setDenseLevel(String denseLevel) {
        this.denseLevel = denseLevel;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getReportPam() {
        return reportPam;
    }

    public void setReportPam(String reportPam) {
        this.reportPam = reportPam;
    }

    public String getReportFilename() {
        return reportFilename;
    }

    public void setReportFilename(String reportFilename) {
        this.reportFilename = reportFilename;
    }
}
