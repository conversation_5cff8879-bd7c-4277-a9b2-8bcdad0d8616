package com.hisun.tms.rpt.controller;

import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.rpt.model.RptModel;
import com.hisun.tms.rpt.model.RptRsFindInput;
import com.hisun.tms.rpt.model.RptRsModel;
import com.hisun.tms.rpt.service.RptRsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Controller
@RequestMapping("/rpt/mgr/opera")
public class RptRsController {

    private static final Logger logger = LoggerFactory.getLogger(RptRsController.class);

    @Resource
    private RptRsService rptRsService;

    @GetMapping
    @PreAuthorize("hasPermission('','/rptmgr/opera') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rpt/mgr/rptOpera");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/rptmgr/opera') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<RptRsModel> findAll(@Valid @RequestBody RptRsFindInput input) {

        DataTablesOutput<RptRsModel> dataTablesOutput = rptRsService.findAll(input);
        return dataTablesOutput;
    }

    @PostMapping(value = "run")
    @PreAuthorize("hasPermission('','/rptmgr/opera') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput run(@RequestParam(value = "runDt") String runDt, @RequestParam(value = "id") int rptId) {
        DataTablesOutput dataTablesOutput = new DataTablesOutput();
        Map map = rptRsService.reRunRpt(runDt.trim(), rptId);
        List list = new ArrayList();
        list.add(map);
        dataTablesOutput.setData(list);
        return dataTablesOutput;

    }

    @GetMapping(value = "getRsFile")
    public void getRsFile(@RequestParam("id") String id, HttpServletRequest request, HttpServletResponse response) {
        RptRsModel rptRsModel = rptRsService.getRsFile(Integer.parseInt(id));

        String path = rptRsModel.getReportPath() + rptRsModel.getReportFilename();
        if(rptRsModel == null){
            logger.error("File not extis !");
        }else{
            logger.info("RsFilePath : " + path);
            //设置文件MIME类型
            response.setContentType(request.getServletContext().getMimeType(rptRsModel.getReportPath() + rptRsModel.getReportFilename()));
            //设置Content-Disposition
            response.setHeader("Content-Disposition", "attachment;filename=" + rptRsModel.getReportFilename());
        }
        File file = new File(path);
        InputStream in = null;
        OutputStream out = null ;
        try {
            in = new FileInputStream(file);
            out = response.getOutputStream();

            //写文件
            int b;
            while((b=in.read())!= -1)
            {
                out.write(b);
            }
        } catch (Exception e) {
            logger.info("获取文件失败" + path);
        } finally {
            try {
                in.close();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
