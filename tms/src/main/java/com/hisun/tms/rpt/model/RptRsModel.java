package com.hisun.tms.rpt.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Entity
@Table(name = "l_report_rs")
public class RptRsModel {


    @Id
    @GeneratedValue(strategy=GenerationType.IDENTITY)
    @Column(name = "id")
    private int id;

    /**
     * @Fields reportId 报表编号 模块名_报表生成周期_编号 INV_D_001
     */
    @Column(name = "report_id")
    private String reportId;

    /**
     * @Fields reportName 报表名称
     */
    @Column(name = "report_name")
    private String reportName;

    /**
     * @Fields reportModule 报表所属模块
     */
    @Column(name = "report_module")
    private String reportModule;

    /**
     * @Fields reportPeriod D:日 W:周 T:旬 M:月 Q:季 H:半年 Y:年 FM:每月定期 FY:每年定期 R:不定期(特定日期执行一次) O:其他
     */
    @Column(name = "report_period")
    private String reportPeriod;

    /**
     * @Fields reportFilename 报表文件名
     */
    @Column(name = "report_filename")
    private String reportFilename;

    /**
     * @Fields runDt 执行日期
     */
    @Column(name = "run_dt")
    private String runDt;

    /**
     * @Fields run_rs 执行结果 0:成功, 1:失败
     */
    @Column(name = "run_rs")
    private String runRs;

    /**
     * @Fields reportPath 报表路径
     */
    @Column(name = "report_path")
    private String reportPath;

    /**
     * @Fields runRsCode 返回码
     */
    @Column(name = "run_rs_code")
    private String runRsCode;

    /**
     * @Fields run_rs_msg 返回信息
     */
    @Column(name = "run_rs_msg")
    private String runRsMsg;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public String getReportModule() {
        return reportModule;
    }

    public void setReportModule(String reportModule) {
        this.reportModule = reportModule;
    }

    public String getReportPeriod() {
        return reportPeriod;
    }

    public void setReportPeriod(String reportPeriod) {
        this.reportPeriod = reportPeriod;
    }

    public String getRunDt() {
        return runDt;
    }

    public void setRunDt(String runDt) {
        this.runDt = runDt;
    }

    public String getRunRs() {
        return runRs;
    }

    public void setRunRs(String runRs) {
        this.runRs = runRs;
    }

    public String getRunRsCode() {
        return runRsCode;
    }

    public void setRunRsCode(String runRsCode) {
        this.runRsCode = runRsCode;
    }

    public String getReportFilename() {
        return reportFilename;
    }

    public void setReportFilename(String reportFilename) {
        this.reportFilename = reportFilename;
    }

    public String getRunRsMsg() {
        return runRsMsg;
    }

    public void setRunRsMsg(String runRsMsg) {
        this.runRsMsg = runRsMsg;
    }

    public String getReportPath() {
        return reportPath;
    }

    public void setReportPath(String reportPath) {
        this.reportPath = reportPath;
    }
}
