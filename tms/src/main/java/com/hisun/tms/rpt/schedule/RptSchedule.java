package com.hisun.tms.rpt.schedule;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.tms.rpt.service.RptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 投资理财-定时批次生成收益明细
 * <AUTHOR>
 * @date 2017年7月15日
 *
 */
@Component
public class RptSchedule {


    private static final Logger logger = LoggerFactory.getLogger(RptSchedule.class);

    @Resource
    RptService rptService;
    /**
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @Scheduled(cron = "0 12 11 ? * *")
    public void InvGrantDetail() {
        try {
            LocalDate grantDate = DateTimeUtils.getCurrentLocalDate();
            rptService.createRpt();
        } catch (Exception e) {
            //logger.info("inv schedule job error：" + e.getMessage());
        }
    }
}
