package com.hisun.tms.rpt.service;

import com.hisun.tms.rpt.model.RptModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
public interface RptService {

    DataTablesOutput<RptModel> findAll(DataTablesInput dataTablesInput);

    void save(RptModel riskRuleModel);

    void update(RptModel riskRuleModel);

    /*void update(String rptId, String sts);*/

    void createRpt();
}
