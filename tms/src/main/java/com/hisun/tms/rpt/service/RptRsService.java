package com.hisun.tms.rpt.service;

import com.hisun.tms.rpt.model.RptModel;
import com.hisun.tms.rpt.model.RptRsFindInput;
import com.hisun.tms.rpt.model.RptRsModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
public interface RptRsService {

    DataTablesOutput<RptRsModel> findAll(RptRsFindInput dataTablesInput);

    Map reRunRpt(String runDt, int rptId);

    RptRsModel getRsFile(int rptId);


}
