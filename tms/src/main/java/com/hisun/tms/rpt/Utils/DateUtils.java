/*
package com.hisun.tms.rpt.Utils;

import com.hisun.tms.rpt.common.Constants;

import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;

*/
/**
 * Created by Administrator on 2017/9/4.
 *//*

public class DateUtils {

    */
/**
     *
     * 判断当前日期是否为季度开始 月份
     *
     * @param date
     * @return
     *//*

    public static boolean isSeasonMonth(LocalDate date) {
        int monthVal = date.getMonthValue();
        if(monthVal == Constants.MONTH_JANUARY || monthVal == Constants.MONTH_APRIL  || monthVal == Constants.MONTH_JULY  || monthVal == Constants.MONTH_OCTOBER ){
            int dayVal = date.getDayOfMonth();
                return true;
        }
        return false;
    }

    */
/**
     *
     * 判断当前日期是否为月开始的第一天
     *
     * @param date
     * @return
     *//*

    public static boolean isMonthFirstDay(LocalDate date) {
        int dayVal = date.getDayOfMonth();
        if(dayVal == 1){
            return true;
        }
        return false;
    }

    */
/**
     *
     * 判断当前日期是否为周开始 日
     *
     * @param date
     * @return
     *//*

    public static boolean isWeekFirstDay(LocalDate date) {
        int weekVal = date.getDayOfWeek().getValue();
        if(weekVal == 1){
            return true;
        }
        return false;
    }

    */
/**
     *
     * 判断当前日期是否为半年开始 月份
     *
     * @param date
     * @return
     *//*

    public static boolean isHalfYear(LocalDate date) {
        int monthVal = date.getMonthValue();
        if(monthVal == Constants.MONTH_JULY || monthVal == Constants.MONTH_JANUARY){
            return true;
        }
        return false;
    }

    */
/**
     *
     * 判断当前日期是否为年开始 月份
     *
     * @param date
     * @return
     *//*

    public static boolean isYear(LocalDate date) {
        int monthVal = date.getMonthValue();
        if(monthVal == Constants.MONTH_JANUARY){
            return true;
        }
        return false;
    }



}
*/
