package com.hisun.tms.rpt.controller;

import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.rpt.model.RptModel;
import com.hisun.tms.rpt.service.RptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Controller
@RequestMapping("/rpt/mgr/define")
public class RptMngController {

    private static final Logger logger = LoggerFactory.getLogger(RptMngController.class);

    @Resource
    private RptService rptService;

    @GetMapping
    @PreAuthorize("hasPermission('','/rptmgr/define') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rpt/mgr/rptDefine");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/rptmgr/define') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<RptModel> findAll(@Valid @RequestBody DataTablesInput input) {

        DataTablesOutput<RptModel> dataTablesOutput = rptService.findAll(input);
        return dataTablesOutput;
    }

    @PostMapping(value = "add")
    @PreAuthorize("hasPermission('','/rptmgr/define') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<RptModel> add(
            @Valid @RequestBody DatatablesEditorRequest<RptModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, RptModel> data = datatablesEditorRequest.getData();
        data.forEach((key, riskRuleModel) -> {
            logger.debug(key + "..." + riskRuleModel.toString());
            rptService.save(riskRuleModel);
        });
        DatatablesEditorResponse<RptModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "edit")
    @PreAuthorize("hasPermission('','/rptmgr/define') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<RptModel> edit(
            @Valid @RequestBody DatatablesEditorRequest<RptModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, RptModel> data = datatablesEditorRequest.getData();
        data.forEach((key, rptModel) -> {
            logger.debug(key + "..." + rptModel.toString());
            rptService.update(rptModel);
        });
        DatatablesEditorResponse<RptModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    /*@GetMapping(value = "switchSts")
    @ResponseBody
    public void updateSts(@RequestParam(value = "rptId") String rptId,
                                  @RequestParam(value = "sts") String sts) {
        logger.debug("rptId : " + rptId + ", sts : "  + sts);
        rptService.update(rptId, sts);
    }*/
}
