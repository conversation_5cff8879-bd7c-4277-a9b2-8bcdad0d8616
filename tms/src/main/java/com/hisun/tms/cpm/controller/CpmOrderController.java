package com.hisun.tms.cpm.controller;


import com.hisun.tms.cpm.model.CpmOrderInfo;
import com.hisun.tms.cpm.model.CpmQueryInfParamInput;
import com.hisun.tms.cpm.service.CpmOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/cpm/orderctrl")
public class CpmOrderController {

    private static final Logger logger = LoggerFactory.getLogger(CpmOrderController.class);

    @Autowired
    private CpmOrderService cpmorderService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/cpmorder') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpm/orderinfo/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/cpmorder') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<CpmOrderInfo> findAll(@Valid @RequestBody CpmQueryInfParamInput cpmQueryInfParamInput) {
        return cpmorderService.findAll(cpmQueryInfParamInput);
    }
}
