package com.hisun.tms.cpm.service;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.cpm.model.CpmOrderInfo;
import com.hisun.tms.cpm.model.CpmQueryInfParamInput;
import com.hisun.tms.cpm.repository.DatatablesCpmOrderRepository;
import com.hisun.tms.specifications.Specifications;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.mapping.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/***
 * <AUTHOR>
 * @date 3:23 PM 2017/7/17
 */
@Service("cpmOrderService")
public class CpmOrderServiceImpl implements CpmOrderService {

    private static final Logger logger = LoggerFactory.getLogger(CpmOrderServiceImpl.class);

    @Autowired
    private DatatablesCpmOrderRepository datatablesCpmOrderRepository;

    @Override
    @Transactional("cpmTransactionManager")
    public DataTablesOutput<CpmOrderInfo> findAll(CpmQueryInfParamInput cpmQueryInfParamInput) {
        Map<String, String> extraMap = cpmQueryInfParamInput.getExtra_search();
    	String userNo = extraMap.get("userNo");
		String mblNo = extraMap.get("mblNo");
		String bossCopType = extraMap.get("bossCopType");
		String orderType = extraMap.get("orderType");
		String beginDateStr = extraMap.get("beginDate");
		String endDateStr = extraMap.get("endDate");
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
    	Specification<CpmOrderInfo> orderQueryParam = Specifications.<CpmOrderInfo>and()
    			.like(JudgeUtils.isNotBlank(userNo), "userNo", "%"+userNo+"%")
    			.like(JudgeUtils.isNotBlank(mblNo), "mblNo", "%"+mblNo+"%")
    			.like(JudgeUtils.isNotBlank(bossCopType), "bossCopType", "%"+bossCopType+"%")
    			.eq(JudgeUtils.isNotBlank(orderType), "orderType", orderType)
                .between("cpmOrderDt", new Range<>(beginDate, endDate))
                .build();
    	Order order=new Order(1,"desc");
    	List<Order> orders=new ArrayList<Order>();
    	orders.add(order);
    	cpmQueryInfParamInput.setOrder(orders);
        DataTablesOutput<CpmOrderInfo> dataTablesOutput = datatablesCpmOrderRepository.findAll(cpmQueryInfParamInput,orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }
}
