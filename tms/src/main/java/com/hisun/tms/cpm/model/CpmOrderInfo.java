package com.hisun.tms.cpm.model;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.math.BigDecimal;
import java.time.LocalDate;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "cpm_order_inf")
public class CpmOrderInfo extends AbstractEntityTmpe {
	/**
     * @Fields cpmOrderNo 缴费订单号
     */
	@Id
    @Column(name = "cpm_order_no")
    private String cpmOrderNo;
    /**
     * @Fields cpmOrderDt 缴费订单日期
     */
	@Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
	@Column(name = "cpm_order_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate cpmOrderDt;
    /**
     * @Fields orderType 订单类型
     */
	@Column(name = "order_type")
    private String orderType;
    /**
     * @Fields bossCopType 缴费单位
     */
	@Column(name = "boss_cop_type")
    private String bossCopType;
    /**
     * @Fields userId 用户编号
     */
	@Column(name = "user_id")
    private String userId;
    /**
     * @Fields userType 用户类型
     */
	@Column(name = "user_type")
    private String userType;
    /**
     * @Fields userNo 支付手机号码
     */
	@Column(name = "user_no")
    private String userNo;
    /**
     * @Fields mblNo 缴费手机号码
     */
	@Column(name = "mbl_no")
    private String mblNo;
    /**
     * @Fields orderAmt 订单金额
     */
	@Column(name = "order_amt", precision = 15, scale = 4)
    private BigDecimal orderAmt;
    /**
     * @Fields orderDesc 订单描述
     */
	@Column(name = "order_desc")
    private String orderDesc;
    /**
     * @Fields ccy 币种
     */
	@Column(name = "ccy")
    private String ccy;
    /**
     * @Fields payAmt 支付金额
     */
	@Column(name = "pay_amt", precision = 15, scale = 4)
    private BigDecimal payAmt;
    /**
     * @Fields payType 支付方式
     */
	@Column(name = "pay_type")
    private String payType;
    /**
     * 订单状态
     */
	@Column(name = "order_sts")
    private String orderSts;

    public String getCpmOrderNo() {
        return cpmOrderNo;
    }

    public void setCpmOrderNo(String cpmOrderNo) {
        this.cpmOrderNo = cpmOrderNo;
    }

    public LocalDate getCpmOrderDt() {
        return cpmOrderDt;
    }

    public void setCpmOrderDt(LocalDate cpmOrderDt) {
        this.cpmOrderDt = cpmOrderDt;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getBossCopType() {
        return bossCopType;
    }

    public void setBossCopType(String bossCopType) {
        this.bossCopType = bossCopType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getOrderDesc() {
        return orderDesc;
    }

    public void setOrderDesc(String orderDesc) {
        this.orderDesc = orderDesc;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getPayAmt() {
        return payAmt;
    }

    public void setPayAmt(BigDecimal payAmt) {
        this.payAmt = payAmt;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getOrderSts() {
        return orderSts;
    }

    public void setOrderSts(String orderSts) {
        this.orderSts = orderSts;
    }

	@Override
    public String toString() {
        return "CpmOrderInfo{" +
                "cpmOrderNo=" + cpmOrderNo + '\'' +
                ", cpmOrderDt='" + cpmOrderDt + 
                ", orderType=" + orderType + '\'' +
                ", bossCopType=" + bossCopType + '\'' +
                ", userId='" + userId + '\'' +
                ", userType='" + userType + '\'' +
                ", userNo='" + userNo + '\'' +
                ", mblNo=" + mblNo + '\'' +
                ", orderAmt='" + orderAmt +
                ", orderDesc='" + orderDesc + '\'' +
                ", ccy='" + ccy + '\'' +
                ", payAmt='" + payAmt +
                ", payType='" + payType + '\'' +
                ", orderSts='" + orderSts + '\'' +
                '}';
    }
}
