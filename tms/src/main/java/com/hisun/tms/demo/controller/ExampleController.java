package com.hisun.tms.demo.controller;

import com.hisun.tms.demo.model.Example;
import com.hisun.tms.demo.service.ExampleService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/demo/example")
public class ExampleController {

    private static final Logger logger = LoggerFactory.getLogger(ExampleController.class);

    @Autowired
    private ExampleService exampleService;

    @GetMapping
    public ModelAndView exampleList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("demo/example/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    public DataTablesOutput<Example> findAll(@Valid @RequestBody DataTablesInput input) {
        return exampleService.findAll(input);
    }

    @PostMapping(value = "/add")
    @ResponseBody
    public DatatablesEditorResponse<Example> add(@Valid @RequestBody DatatablesEditorRequest<Example> datatablesEditorRequest) {
        Iterable<Example> iterable = datatablesEditorRequest.getData().values();
        exampleService.save(iterable);

        DatatablesEditorResponse<Example> datatablesEditorResponse = new DatatablesEditorResponse<Example>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @ResponseBody
    public DatatablesEditorResponse<Example> modify(@Valid @RequestBody DatatablesEditorRequest<Example> datatablesEditorRequest) {
        Map<String, Example> exampleMap = datatablesEditorRequest.getData();
        Map<String, Example> mapData = exampleService.modify(exampleMap);

        DatatablesEditorResponse datatablesEditorResponse = new DatatablesEditorResponse();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @ResponseBody
    public DatatablesEditorResponse<Example> delete(@Valid @RequestBody DatatablesEditorRequest<Example> datatablesEditorRequest) {
        Iterable<Example> iterable = datatablesEditorRequest.getData().values();
        exampleService.delete(iterable);

        return new DatatablesEditorResponse();
    }
}
