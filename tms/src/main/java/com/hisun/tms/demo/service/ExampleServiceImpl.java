package com.hisun.tms.demo.service;

import com.hisun.tms.demo.model.Example;
import com.hisun.tms.demo.repository.DatatablesExampleRepository;
import com.hisun.tms.demo.repository.ExampleRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Locale;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("exampleService")
public class ExampleServiceImpl implements ExampleService {

    private static final Logger logger = LoggerFactory.getLogger(ExampleServiceImpl.class);

    private MessageSource messageSource;

    //    private CardClient cardClient;
    private DatatablesExampleRepository datatablesExampleRepository;
    private ExampleRepository exampleRepository;

    @Autowired
    public ExampleServiceImpl(DatatablesExampleRepository datatablesExampleRepository, ExampleRepository exampleRepository) {
        this.datatablesExampleRepository = datatablesExampleRepository;
        this.exampleRepository = exampleRepository;
    }

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }
//    @Autowired
//    public void setCardClient(CardClient cardClient) {
//        this.cardClient = cardClient;
//    }

    @Override
    public void save(Iterable<Example> entities) {
        exampleRepository.save(entities);

        // 模拟通过feign调用cpi模块查询卡bin信息
//        String cardNo = "1234567899";
//
//        GenericRspDTO<CardBinRspDTO> genericRspDTO = cardClient.queryCardBin(cardNo);
//
//        logger.debug("queryCardBin return: {}, {}", genericRspDTO.getMsgId(), genericRspDTO.getMsgCd());

    }

    @Override
    public Map<String, Example> modify(Map<String, Example> exampleMap) {

//        List<Example> examples = new ArrayList<>(exampleMap.values());
//
//        logger.debug("recive from web: {}", examples.toString());
//
//        Collection<Integer> ids = Collections2.transform(
//                examples,
//                example -> example != null ? example.getId() : 0
//        );
//        logger.debug("query params: {}", ids.toString());
//        List<Example> examplesToUpdate = exampleRepository.findAll(ids);
//        logger.debug("query from db: {}", examplesToUpdate.toString());
//
//        for (int i = 0; i < examples.size(); i++) {
//            BeanUtils.copyProperties(examples.get(i), examplesToUpdate.get(i), ExampleConstants.IGNORE_AUDIT_COLUMN);
//        }
//
//        logger.debug("after copy with audit column ignore: {}", examplesToUpdate.toString());
//
//        List<Example> list = exampleRepository.save(examplesToUpdate);
//
//        logger.debug("after update: {}", list.toString());
//
//        Map<Integer, Example> map = list.stream().collect(Collectors.toMap(Example::getId, java.util.function.Function.identity()));
//        return map.entrySet().stream().collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return null;
    }

    @Override
    public void delete(Iterable<Example> entities) {
        exampleRepository.delete(entities);
    }

    @Override
    @Transactional("demoTransactionManager")
    public DataTablesOutput<Example> findAll(DataTablesInput dataTablesInput) {
        DataTablesOutput<Example> dataTablesOutput = datatablesExampleRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);

        // test i18n
        Locale locale = LocaleContextHolder.getLocale();
        logger.debug("默认语言代码: {}", locale.getLanguage());
        logger.debug("默认地区代码: {}", locale.getCountry());
        logger.debug("默认语言地区代码: {}", locale.toString());
        logger.debug("默认语言描述: {}", locale.getDisplayLanguage());
        logger.debug("默认地区描述: {}", locale.getDisplayCountry());
        logger.debug("默认语言,地区描述: {}", locale.getDisplayName());
        String subject = messageSource.getMessage("email.activiti.subject", null, locale);
        logger.debug("subject: {}", subject);

        return dataTablesOutput;
    }
}
