package com.hisun.tms.demo.service;

import com.hisun.tms.demo.model.Example;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface ExampleService {

    void save(Iterable<Example> entities);

    Map<String, Example> modify(Map<String, Example> exampleMap);

    void delete(Iterable<Example> entities);

    DataTablesOutput<Example> findAll(DataTablesInput dataTablesInput);
}