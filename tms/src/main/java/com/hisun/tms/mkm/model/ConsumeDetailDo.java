package com.hisun.tms.mkm.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Created by chen on 8/22 0022.
 */
public class ConsumeDetailDo {
    private String mobile;
    private String instId;
    private String orderNo;
    private String id;
    private String atvNm;
    private BigDecimal orderAmt;
    private Integer count;
    private LocalDate userDt;
    private LocalTime userTm;
    private LocalDateTime consumeTime;
    private String mkTool;
    private String status;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAtvNm() {
        return atvNm;
    }

    public void setAtvNm(String atvNm) {
        this.atvNm = atvNm;
    }

    public BigDecimal getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(BigDecimal orderAmt) {
        this.orderAmt = orderAmt;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public LocalDate getUserDt() {
        return userDt;
    }

    public void setUserDt(LocalDate userDt) {
        this.userDt = userDt;
    }

    public LocalTime getUserTm() {
        return userTm;
    }

    public void setUserTm(LocalTime userTm) {
        this.userTm = userTm;
    }

    public LocalDateTime getConsumeTime() {
        return consumeTime;
    }

    public void setConsumeTime(LocalDateTime consumeTime) {
        this.consumeTime = consumeTime;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
