package com.hisun.tms.mkm.dao;

import com.hisun.tms.mkm.model.FAQManagerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 常见问题数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 19:55
 */
@Mapper
public interface IMkmFAQDao {
    /**
     * 根据查询条件查询常见问题
     * 
     * @param param 查询参数
     * @return 常见问题列表
     */
    List<FAQManagerDO> findByFindFAQDTO(Map<String, Object> param);

    /**
     * 查询总数
     * 
     * @param param 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> param);

    /**
     * 删除常见问题（逻辑删除）
     * 
     * @param id 常见问题ID
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     * 更新常见问题
     * 
     * @param faqManagerDO 常见问题对象
     * @return 影响行数
     */
    int update(FAQManagerDO faqManagerDO);

    /**
     * 新增常见问题
     * 
     * @param faqManagerDO 常见问题对象
     * @return 影响行数
     */
    int insert(FAQManagerDO faqManagerDO);

    /**
     * 根据ID获取常见问题
     * 
     * @param id 常见问题ID
     * @return 常见问题对象
     */
    FAQManagerDO getById(Integer id);
}