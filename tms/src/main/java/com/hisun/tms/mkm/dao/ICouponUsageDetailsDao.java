package com.hisun.tms.mkm.dao;

import com.hisun.tms.mkm.model.ConsumeDetailDo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户营销工具使用明细
 * 根据手机号和开始时间查询手机号，电子券使用详细信息
 *
 * <AUTHOR>
 * @create 2017/7/25
 */
public interface ICouponUsageDetailsDao {
    List<ConsumeDetailDo> selectUsageByMblNoAndBegDt(@Param("mobile") String mobile,@Param("instId") String instId,
                                                     @Param("begDt") LocalDate begDt,@Param("endDt") LocalDate endDt,
                                                     @Param("start") Integer start, @Param("length") Integer length);

    int selectUsageByMblNoAndBegDtTotal(@Param("mobile") String mobile,@Param("instId") String instId,
                                        @Param("begDt") LocalDate begDt,@Param("endDt") LocalDate endDt,
                                        @Param("start") Integer start, @Param("length") Integer length);;
}
