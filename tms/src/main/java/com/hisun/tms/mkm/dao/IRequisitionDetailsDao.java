package com.hisun.tms.mkm.dao;

import com.hisun.tms.mkm.model.RequisitionDetailsDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 查询用户营销工具领用明细
 * 使用union连接电子券/优惠券交易流水表（mkm_coupon_seq），海币交易流水表（sea_ccy_seq）查询结果
 * 根据手机号和开始时间查询手机号，营销工具领取日期、时间，活动id，营销工具类别，金额，和状态列表
 *
 * <AUTHOR>
 * @create 2017/7/24
 */
public interface IRequisitionDetailsDao {

    List<RequisitionDetailsDO> selectRequisitionByMblNoAndBegDt( @Param("mobile") String mobile, @Param("id") String id,
                                                                 @Param("begDt") LocalDate dt, @Param("endDt") LocalDate begDt,
                                                                @Param("pageBegin") Integer pageBegin,
                                                                @Param("pageEnd") Integer pageEnd);

    int selectRequisitionByMblNoAndBegDtTotal(@Param("mobile") String mobile, @Param("id") String id,
                                              @Param("begDt") LocalDate dt, @Param("endDt") LocalDate begDt);


}
