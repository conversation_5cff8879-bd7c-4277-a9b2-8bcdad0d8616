package com.hisun.tms.mkm.service;

import com.hisun.tms.mkm.model.BatchFileRecDO;
import com.hisun.tms.mkm.model.ConsumeDetailDo;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.RequisitionDetailsDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * Created by chen on 8/21 0021.
 */
public interface MkmToolManagerService {

    DataTablesOutput<RequisitionDetailsDO> releasefindAll(MartketFindInput input);

    DataTablesOutput<ConsumeDetailDo> consumefindAll(MartketFindInput input);

    DataTablesOutput<BatchFileRecDO> getBatchList(DataTablesInput input);

    void insertBatchRecFile(BatchFileRecDO batchFileRecDO);

    String oprFile(String fileName, String oprTyp);
}
