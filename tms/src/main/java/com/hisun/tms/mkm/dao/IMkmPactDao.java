package com.hisun.tms.mkm.dao;

import com.hisun.tms.mkm.model.PactManagerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 协议数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 10:35
 */
@Mapper
public interface IMkmPactDao {
    /**
     * 根据查询条件查询协议
     * 
     * @param param 查询参数
     * @return 协议列表
     */
    List<PactManagerDO> findByParam(Map<String, Object> param);

    /**
     * 查询总数
     * 
     * @param param 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> param);

    /**
     * 删除协议（逻辑删除）
     * 
     * @param id 协议ID
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     * 更新协议
     * 
     * @param pactManagerDO 协议对象
     * @return 影响行数
     */
    int update(PactManagerDO pactManagerDO);

    /**
     * 新增协议
     * 
     * @param pactManagerDO 协议对象
     * @return 影响行数
     */
    int insert(PactManagerDO pactManagerDO);

    /**
     * 根据ID获取协议
     * 
     * @param id 协议ID
     * @return 协议对象
     */
    PactManagerDO getById(Integer id);
}