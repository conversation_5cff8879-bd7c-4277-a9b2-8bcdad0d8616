package com.hisun.lemon.mkm.req.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
import java.util.Date;

/**
 * 常见问题请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 20:15
 */
public class FAQManagerReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问题ID
     */
    @ApiModelProperty(name = "id", value = "问题ID", required = false, dataType = "Integer")
    private Integer id;

    /**
     * 问题内容
     */
    @ApiModelProperty(name = "question", value = "问题内容", required = false, dataType = "String")
    @Length(max = 300)
    private String question;

    /**
     * 回答内容
     */
    @ApiModelProperty(name = "answer", value = "回答内容", required = false, dataType = "String")
    @Length(max = 300)
    private String answer;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "createdBy", value = "创建人", required = false, dataType = "String")
    private String createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updatedBy", value = "更新人", required = false, dataType = "String")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createdDate", value = "创建时间", required = false, dataType = "Date")
    private Date createdDate;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updatedDate", value = "更新时间", required = false, dataType = "Date")
    private Date updatedDate;

    /**
     * 删除标志
     */
    @ApiModelProperty(name = "deletedFlag", value = "删除标志", required = false, dataType = "String")
    private String deletedFlag;

    /**
     * 页码
     */
    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private Integer pageNum;

    /**
     * 每页条数
     */
    @ApiModelProperty(name = "pageSize", value = "每页条数", required = false, dataType = "Integer")
    private Integer pageSize;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(String deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}