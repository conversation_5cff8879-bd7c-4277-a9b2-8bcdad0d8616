package com.hisun.tms.mkm.service;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.mkm.dao.IMkmPactDao;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.PactManagerDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 协议管理服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 10:30
 */
@Service("mkmPactManagerService")
@Transactional
public class MkmPactManagerServiceImpl implements MkmPactManagerService {

    @Resource
    private IMkmPactDao iMkmPactDao;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<PactManagerDO> findAll(MartketFindInput input) {
        DataTablesOutput<PactManagerDO> dataTablesOutput = new DataTablesOutput<>();

        Map<String, Object> param = new HashMap<>();
        param.put("id", input.getExtra_search().get("id"));
        param.put("title", input.getExtra_search().get("title"));
        param.put("pageEnd", input.getLength());
        param.put("pageBegin", input.getStart());

        // 先获取总记录数，再获取分页数据
        int total = iMkmPactDao.countTotal(param);
        List<PactManagerDO> list = iMkmPactDao.findByParam(param);

        // 设置DataTablesOutput的属性
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setRecordsFiltered(total);

        // 设置数据
        if (list == null) {
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(list);
        }

        return dataTablesOutput;
    }

    @Override
    public Object get(Integer id) {
        return iMkmPactDao.getById(id);
    }

    @Override
    public String add(PactManagerDO pactManagerDO) {
        // 设置创建者和更新者
        pactManagerDO.setCreateUser(getOpr.getOperatorName());
        pactManagerDO.setUpdateUser(getOpr.getOperatorName());

        // 设置创建时间和更新时间
        pactManagerDO.setCreateTime(LocalDateTime.now());
        pactManagerDO.setUpdateTime(LocalDateTime.now());

        // 设置删除标记为未删除
        pactManagerDO.setDelFlag("N");

        int result = iMkmPactDao.insert(pactManagerDO);
        if (result == 1) {
            return "MKM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String update(PactManagerDO pactManagerDO) {
        // 设置更新者和更新时间
        pactManagerDO.setUpdateUser(getOpr.getOperatorName());
        pactManagerDO.setUpdateTime(LocalDateTime.now());

        int result = iMkmPactDao.update(pactManagerDO);
        if (result == 1) {
            return "MKM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String delete(Integer id) {
        int result = iMkmPactDao.delete(id);
        return result + "";
    }
}