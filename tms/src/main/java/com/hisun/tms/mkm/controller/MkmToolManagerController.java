package com.hisun.tms.mkm.controller;

import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.tms.mkm.model.BatchFileRecDO;
import com.hisun.tms.mkm.model.ConsumeDetailDo;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.RequisitionDetailsDO;
import com.hisun.tms.mkm.service.MkmToolManagerService;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Created by chen on 8/21 0021.
 */
@Controller
@RequestMapping("/mkm")
public class MkmToolManagerController {

    private static final Logger logger = LoggerFactory.getLogger(MkmToolManagerController.class);

    @Value("${mkm.local.upload}")
    private String batchFileLocalPath;

    @Value("${mkm.remote.path}")
    private String remotePath;

    @Value("${mkm.remote.ip}")
    private String remoteIp;

    @Value("${mkm.remote.port}")
    private String remotePort;

    @Value("${mkm.remote.user}")
    private String remoteUser;

    @Value("${mkm.remote.password}")
    private String remotePwd;

    @Value("${mkm.remote.timeout}")
    private String timeout;
    @Value("${upload.remote.url}")
    private String url;

    @Resource
    private MkmToolManagerService mkmToolManagerService;
    @GetMapping(value = "/releaseDetail")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/releaseDetail') or hasRole('ROLE_ADMIN')")
    public ModelAndView releaseDetail() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/tooldetail/releaseDetail");
        return modelAndView;
    }

    @GetMapping(value = "/consumeDetail")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/consumeDetail') or hasRole('ROLE_ADMIN')")
    public ModelAndView consumeDetail() {
        // List list = couponRepository.findDetail("select * from mkm_inst where atv_id = 0000001001");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/tooldetail/consumeDetail");
        return modelAndView;
    }
    @PostMapping(value = "/releaseDetail/findAll")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/releaseDetail') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<RequisitionDetailsDO> findAll(@Valid @RequestBody MartketFindInput input) {
        return mkmToolManagerService.releasefindAll(input);
    }
    @PostMapping(value = "/consumeDetail/findAll")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/consumeDetail') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<ConsumeDetailDo> consumefindAll(@Valid @RequestBody MartketFindInput input) {
        return mkmToolManagerService.consumefindAll(input);
    }


    /**
     * 跳转到营销工具管理页面
     * @return
     */
    @GetMapping(value = "/mkmToolBatchMng")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/mkmToolBatchMng') or hasRole('ROLE_ADMIN')")
    public ModelAndView mkmToolBatchMng() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/tooldetail/mkmToolBatchMng");
        return modelAndView;
    }
    /**
     * 批量文件列表
     */
    @PostMapping(value = "/batchList")
    @ResponseBody
    public DataTablesOutput<BatchFileRecDO> batchList(@Valid @RequestBody DataTablesInput input) {
        return mkmToolManagerService.getBatchList(input);
    }

    /**
     * 批量文件上传
     * @param file
     * @param oprTyp
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/batch/upload")
    public String uploadBatchRec(@RequestParam("file")MultipartFile file,@RequestParam("oprTyp") String oprTyp) {
        File decPath = new File(batchFileLocalPath);
        String fileName =  oprTyp + "_"+file.getOriginalFilename();
        if (!(decPath.exists())) {
            decPath.mkdir();
        }
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        String localPath = batchFileLocalPath + File.separator + fileName;

        try {
            FileUtils.writeByteArrayToFile(new File(localPath), file.getBytes());
//            FileUtils.writeByteArrayToFile(new File("D:/123.txt"), file.getBytes());
        } catch (IOException e) {
            logger.error("File operation exception：" + e.getMessage());
            return "Error";
        }

        try {
            FileSftpUtils.upload(localPath, remoteIp, Integer.valueOf(remotePort), Integer.valueOf(timeout), remotePath, remoteUser, remotePwd);
        } catch (Exception e) {
            logger.error("Upload failure" + e.getMessage());
            return "Error";
        }

        new File(localPath).delete();

        BatchFileRecDO batchFileRecDO = new BatchFileRecDO();
        batchFileRecDO.setOprTyp(oprTyp);
        batchFileRecDO.setBatchFile(remotePath+"/" + fileName);
        batchFileRecDO.setAcId(fileName.split("_")[1]);
        //批次号为文件名去掉_去掉后缀
        String recNoFileName = fileName.split("\\.")[0];
        batchFileRecDO.setRecNo(recNoFileName.replaceAll("_",""));
        mkmToolManagerService.insertBatchRecFile(batchFileRecDO);
        String rs = mkmToolManagerService.oprFile(fileName , oprTyp);
        return rs;
    }
}
