package com.hisun.tms.mkm.controller;

import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.MsgInfoDO;
import com.hisun.tms.mkm.service.MsgInfoManagerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 国际化消息信息管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/1
 */
@Controller
@RequestMapping("/mkm/msginfo")
public class MsgInfoManagerController {

    private static final Logger logger = LoggerFactory.getLogger(MsgInfoManagerController.class);

    @Resource
    private MsgInfoManagerService msgInfoManagerService;

    /**
     * 国际化消息信息列表页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/msginfo') or hasRole('ROLE_ADMIN')")
    public ModelAndView msgInfoList() {
        logger.info("访问国际化管理页面");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/msginfo/index");
        return modelAndView;
    }

    /**
     * 测试页面访问（无权限限制）
     */
    @GetMapping("/test")
    public ModelAndView testPage() {
        logger.info("测试页面访问");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/msginfo/index");
        return modelAndView;
    }

    /**
     * 简单的字符串返回测试
     */
    @GetMapping("/hello")
    @ResponseBody
    public String hello() {
        logger.info("Hello测试访问");
        return "Hello from MsgInfoManagerController!";
    }

    /**
     * 获取单个国际化消息信息
     */
    @PostMapping(value = "/getMsgInfo")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/msginfo') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public MsgInfoDO getMsgInfo(@RequestParam(value = "msgCd", required = true) String msgCd,
                                @RequestParam(value = "language", required = true) String language,
                                @RequestParam(value = "scenario", required = true) String scenario) {
        try {
            MsgInfoDO msgInfoDetail = (MsgInfoDO) msgInfoManagerService.get(msgCd, language, scenario);
            return msgInfoDetail;
        } catch (Exception e) {
            logger.error("获取国际化消息信息详情失败: ", e);
            return null;
        }
    }

    /**
     * 查询所有国际化消息信息
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/msginfo') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<MsgInfoDO> findAll(@Valid @RequestBody MartketFindInput input) {
        try {
            logger.debug("查询国际化消息信息列表，参数: {}", input);
            DataTablesOutput<MsgInfoDO> result = msgInfoManagerService.findAll(input);
            logger.debug("查询国际化消息信息列表，结果数量: {}", result.getData().size());
            return result;
        } catch (Exception e) {
            logger.error("查询国际化消息信息列表失败: ", e);
            // 返回空结果而不是null，避免前端解析错误
            DataTablesOutput<MsgInfoDO> emptyResult = new DataTablesOutput<>();
            emptyResult.setDraw(input.getDraw());
            emptyResult.setRecordsTotal(0);
            emptyResult.setRecordsFiltered(0);
            return emptyResult;
        }
    }

    /**
     * 添加国际化消息信息
     */
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/msginfo:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(MsgInfoDO msgInfoDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = msgInfoManagerService.add(msgInfoDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("添加国际化消息信息失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 修改国际化消息信息
     */
    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/msginfo:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(MsgInfoDO msgInfoDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = msgInfoManagerService.update(msgInfoDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("修改国际化消息信息失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 删除国际化消息信息
     */
    @DeleteMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/msginfo:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public String delete(@RequestParam(value = "msgCd", required = true) String msgCd,
                        @RequestParam(value = "language", required = true) String language,
                        @RequestParam(value = "scenario", required = true) String scenario) {
        String result = "";
        try {
            result = msgInfoManagerService.delete(msgCd, language, scenario);
        } catch (Exception e) {
            logger.error("删除国际化消息信息失败: ", e);
            result = "";
        }
        return result;
    }
}