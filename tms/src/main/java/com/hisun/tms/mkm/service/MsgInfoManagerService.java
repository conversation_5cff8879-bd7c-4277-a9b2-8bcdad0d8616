package com.hisun.tms.mkm.service;

import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.MsgInfoDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 国际化消息信息管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/1
 */
public interface MsgInfoManagerService {
    /**
     * 查询所有国际化消息信息
     * 
     * @param input 查询条件
     * @return 国际化消息信息列表
     */
    DataTablesOutput<MsgInfoDO> findAll(MartketFindInput input);

    /**
     * 获取单个国际化消息信息
     * 
     * @param msgCd 消息代码
     * @param language 语言
     * @param scenario 场景
     * @return 国际化消息信息对象
     */
    Object get(String msgCd, String language, String scenario);

    /**
     * 添加国际化消息信息
     * 
     * @param msgInfoDO 国际化消息信息对象
     * @return 结果
     */
    String add(MsgInfoDO msgInfoDO);

    /**
     * 更新国际化消息信息
     * 
     * @param msgInfoDO 国际化消息信息对象
     * @return 结果
     */
    String update(MsgInfoDO msgInfoDO);

    /**
     * 删除国际化消息信息
     * 
     * @param msgCd 消息代码
     * @param language 语言
     * @param scenario 场景
     * @return 结果
     */
    String delete(String msgCd, String language, String scenario);
}