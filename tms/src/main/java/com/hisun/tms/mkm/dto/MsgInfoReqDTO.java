package com.hisun.tms.mkm.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Date;

/**
 * 国际化消息信息请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/1
 */
public class MsgInfoReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息代码
     */
    @ApiModelProperty(name = "msgCd", value = "消息代码", required = false, dataType = "String")
    @Length(max = 8)
    private String msgCd;

    /**
     * 语言
     */
    @ApiModelProperty(name = "language", value = "语言", required = false, dataType = "String")
    @Length(max = 15)
    private String language;

    /**
     * 消息信息
     */
    @ApiModelProperty(name = "msgInfo", value = "消息信息", required = false, dataType = "String")
    @Length(max = 200)
    private String msgInfo;

    /**
     * 场景
     */
    @ApiModelProperty(name = "scenario", value = "场景", required = false, dataType = "String")
    @Length(max = 30)
    private String scenario;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", required = false, dataType = "Date")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "modifyTime", value = "修改时间", required = false, dataType = "Date")
    private Date modifyTime;

    /**
     * 页码
     */
    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private Integer pageNum;

    /**
     * 每页条数
     */
    @ApiModelProperty(name = "pageSize", value = "每页条数", required = false, dataType = "Integer")
    private Integer pageSize;

    public String getMsgCd() {
        return msgCd;
    }

    public void setMsgCd(String msgCd) {
        this.msgCd = msgCd;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }

    public String getScenario() {
        return scenario;
    }

    public void setScenario(String scenario) {
        this.scenario = scenario;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}