package com.hisun.tms.mkm.repository;

import com.hisun.tms.mkm.model.SeaccyDetailDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * Created by chen on 8/29 0029.
 */
public interface SeaccyDetailRepository extends JpaRepository<SeaccyDetailDo,String> {

    @Query(value = "select m.user_id,m.mk_tool , m.mobile , m.status , m.count,m.create_time ,m.modify_time,m.release_dt,m.release_tm from mkm_sea_ccy_detail m where user_id = :userId ", nativeQuery=true  )
    List<SeaccyDetailDo> findBalanceByUserId(@Param("userId") String userId);
}
