package com.hisun.tms.mkm.model;

import java.io.Serializable;

/**
 * Created by chen on 10/11 0011.
 */
public class MkmAtvUserPK implements Serializable{
    /**
     * @Fields atvId 活动编号
     */
    private String atvId;
    /**
     * @Fields userId 用户编号
     */
    private String userId;
    public MkmAtvUserPK() {
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }
}
