package com.hisun.tms.mkm.dao;

import com.hisun.tms.mkm.model.MsgInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 国际化消息信息数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/1
 */
@Mapper
public interface IMsgInfoDao {
    /**
     * 根据查询条件查询国际化消息信息
     * 
     * @param param 查询参数
     * @return 国际化消息信息列表
     */
    List<MsgInfoDO> findByCondition(Map<String, Object> param);

    /**
     * 查询总数
     * 
     * @param param 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> param);

    /**
     * 删除国际化消息信息
     * 
     * @param msgCd 消息代码
     * @param language 语言
     * @param scenario 场景
     * @return 影响行数
     */
    int delete(@Param("msgCd") String msgCd, @Param("language") String language, @Param("scenario") String scenario);

    /**
     * 更新国际化消息信息
     * 
     * @param msgInfoDO 国际化消息信息对象
     * @return 影响行数
     */
    int update(MsgInfoDO msgInfoDO);

    /**
     * 新增国际化消息信息
     * 
     * @param msgInfoDO 国际化消息信息对象
     * @return 影响行数
     */
    int insert(MsgInfoDO msgInfoDO);

    /**
     * 根据主键获取国际化消息信息
     * 
     * @param msgCd 消息代码
     * @param language 语言
     * @param scenario 场景
     * @return 国际化消息信息对象
     */
    MsgInfoDO getByPrimaryKey(@Param("msgCd") String msgCd, @Param("language") String language, @Param("scenario") String scenario);
}