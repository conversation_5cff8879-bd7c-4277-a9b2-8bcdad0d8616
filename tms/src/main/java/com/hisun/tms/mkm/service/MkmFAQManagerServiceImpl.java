package com.hisun.tms.mkm.service;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.mkm.dao.IMkmFAQDao;
import com.hisun.tms.mkm.model.FAQManagerDO;
import com.hisun.tms.mkm.model.MartketFindInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 常见问题管理服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 20:00
 */
@Service("mkmFAQManagerService")
@Transactional
public class MkmFAQManagerServiceImpl implements MkmFAQManagerService {

    @Resource
    private IMkmFAQDao iMkmFAQDao;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<FAQManagerDO> findAll(MartketFindInput input) {
        DataTablesOutput<FAQManagerDO> dataTablesOutput = new DataTablesOutput<>();

        Map<String, Object> param = new HashMap<>();
        param.put("id", input.getExtra_search().get("id"));
        param.put("questionContent", input.getExtra_search().get("questionContent"));
        param.put("language", input.getExtra_search().get("language"));
        param.put("pageEnd", input.getLength());
        param.put("pageBegin", input.getStart());

        // 先获取总记录数，再获取分页数据
        int total = iMkmFAQDao.countTotal(param);
        List<FAQManagerDO> list = iMkmFAQDao.findByFindFAQDTO(param);

        // 设置DataTablesOutput的属性
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setRecordsFiltered(total);

        // 设置数据
        if (list == null) {
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(list);
        }

        return dataTablesOutput;
    }

    @Override
    public Object get(Integer id) {
        return iMkmFAQDao.getById(id);
    }

    @Override
    public String add(FAQManagerDO faqManagerDO) {
        // 设置创建者和更新者
        faqManagerDO.setCreatedBy(getOpr.getOperatorName());
        faqManagerDO.setUpdatedBy(getOpr.getOperatorName());

        // 设置创建时间和更新时间
        Date currentDate = new Date();
        faqManagerDO.setCreatedDate(currentDate);
        faqManagerDO.setUpdatedDate(currentDate);

        // 设置删除标记为未删除
        faqManagerDO.setDeletedFlag("N");

        int result = iMkmFAQDao.insert(faqManagerDO);
        if (result == 1) {
            return "MKM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String update(FAQManagerDO faqManagerDO) {
        // 设置更新者和更新时间
        faqManagerDO.setUpdatedBy(getOpr.getOperatorName());
        faqManagerDO.setUpdatedDate(new Date());

        int result = iMkmFAQDao.update(faqManagerDO);
        if (result == 1) {
            return "MKM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String delete(Integer id) {
        int result = iMkmFAQDao.delete(id);
        return result + "";
    }
}