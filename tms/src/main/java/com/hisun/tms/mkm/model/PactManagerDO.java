package com.hisun.tms.mkm.model;

import java.time.LocalDateTime;

/**
 * TODO 此处填写功能说明
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 10:22
 */
public class PactManagerDO {
    /**
     * 协议id
     */
    private Long id;

    /**
     * 协议标题
     */
    private String title;

    /**
     * 协议内容
     */
    private String content;

    /**
     * 协议类型
     */
    private String type;

    /**
     * 协议状态
     */
    private String status;

    /**
     * 协议备注
     */
    private String remark;

    /**
     * 协议创建时间
     */
    private LocalDateTime createTime;

    /**
     * 协议更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 协议创建人
     */
    private String createUser;

    /**
     * 协议更新人
     */
    private String updateUser;

    /**
     * 协议删除标志
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "PactManagerDO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", type='" + type + '\'' +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser='" + createUser + '\'' +
                ", updateUser='" + updateUser + '\'' +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }

}
