package com.hisun.tms.mkm.service;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.mkm.client.MarketingToolsMngClient;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.mkm.dao.ICouponUsageDetailsDao;
import com.hisun.tms.mkm.dao.IRequisitionDetailsDao;
import com.hisun.tms.mkm.model.BatchFileRecDO;
import com.hisun.tms.mkm.model.ConsumeDetailDo;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.RequisitionDetailsDO;
import com.hisun.tms.mkm.repository.BatchFileRecRepository;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by chen on 8/21 0021.
 */
@Service
public class MkmToolManagerServiceImpl implements MkmToolManagerService {

    @Resource
    private IRequisitionDetailsDao iRequisitionDetailsDao;

    @Resource
    private ICouponUsageDetailsDao iCouponUsageDetailsDao;

    @Resource
    private BatchFileRecRepository batchFileRecRepository;

    @Resource
    private GetOpr getOpr;

    @Resource
    private MarketingToolsMngClient marketingToolsMngClient;

    @Override
    public DataTablesOutput<RequisitionDetailsDO> releasefindAll(MartketFindInput input) {
        DataTablesOutput<RequisitionDetailsDO> dataTablesOutput = new DataTablesOutput();
        List<RequisitionDetailsDO> list = new ArrayList<RequisitionDetailsDO>() ;
        int total = 0;
        String id = input.getExtra_search().get("id");
        String mobile = input.getExtra_search().get("mobile");
        String releaseTmS = input.getExtra_search().get("releaseTmS");
        String releaseTmE = input.getExtra_search().get("releaseTmE");
        LocalDate begDt = null ;
        LocalDate endDt = null ;



        try {
            if (JudgeUtils.isNotBlank(releaseTmS)){
                releaseTmS = releaseTmS.replaceAll("-","").trim();
                begDt = DateTimeUtils.parseLocalDate(releaseTmS);
            }
            if (JudgeUtils.isNotBlank(releaseTmE)){
                endDt = DateTimeUtils.parseLocalDate(releaseTmE.replaceAll("-","").trim());
            }
            list = iRequisitionDetailsDao.selectRequisitionByMblNoAndBegDt(mobile, id,begDt,endDt
                    ,input.getStart(),input.getLength());
            total = iRequisitionDetailsDao.selectRequisitionByMblNoAndBegDtTotal(mobile, id,begDt,endDt);
        } catch (Exception e){
            e.printStackTrace();
        }
        if (list == null){
            dataTablesOutput.setData(new ArrayList<RequisitionDetailsDO>());
        } else {
            dataTablesOutput.setData(list);
        }
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(total);
        return dataTablesOutput;
    }

    @Override
    public DataTablesOutput<ConsumeDetailDo> consumefindAll(MartketFindInput input) {
        DataTablesOutput<ConsumeDetailDo> dataTablesOutput = new DataTablesOutput();
        List<ConsumeDetailDo> list = new ArrayList<ConsumeDetailDo>() ;
        int total = 0;
        String instId = input.getExtra_search().get("instId");
        String mobile = input.getExtra_search().get("mobile");
        String releaseTmS = input.getExtra_search().get("userS");
        String releaseTmE = input.getExtra_search().get("userE");
        LocalDate begDt = null ;
        LocalDate endDt = null ;



        try {
            if (JudgeUtils.isNotBlank(releaseTmS)){
                releaseTmS = releaseTmS.replaceAll("-","").trim();
                begDt = DateTimeUtils.parseLocalDate(releaseTmS);
            }
            if (JudgeUtils.isNotBlank(releaseTmE)){
                endDt = DateTimeUtils.parseLocalDate(releaseTmE.replaceAll("-","").trim());
            }
            list = iCouponUsageDetailsDao.selectUsageByMblNoAndBegDt(mobile, instId,begDt,endDt
                    ,input.getStart(),input.getLength());
            total = iCouponUsageDetailsDao.selectUsageByMblNoAndBegDtTotal(mobile, instId,begDt,endDt
                    ,input.getStart(),input.getLength());
        } catch (Exception e){
            e.printStackTrace();
        }
        if (list == null){
            dataTablesOutput.setData(new ArrayList<ConsumeDetailDo>());
        } else {
            dataTablesOutput.setData(list);
        }
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(total);
        return dataTablesOutput;
    }

    @Override
    public DataTablesOutput<BatchFileRecDO> getBatchList(DataTablesInput input) {
        DataTablesOutput<BatchFileRecDO> dataTablesOutput = new DataTablesOutput();
        dataTablesOutput = batchFileRecRepository.findAll(input);
        return dataTablesOutput;
    }

    @Override
    public void insertBatchRecFile(BatchFileRecDO batchFileRecDO) {
        batchFileRecDO.setProcessSts("0");
        batchFileRecDO.setCreateTime(LocalDateTime.now());
        batchFileRecDO.setModifyTime(LocalDateTime.now());
        batchFileRecDO.setCreOprId(getOpr.getOperatorName());
        batchFileRecDO.setUpdOprId(getOpr.getOperatorName());
        batchFileRecRepository.save(batchFileRecDO);
    }

    @Override
    public String oprFile(String fileName, String oprTyp) {
        GenericDTO <NoBody> genericDTO = GenericDTO.newInstance();
        GenericRspDTO<NoBody> genericRspDTO = GenericRspDTO.newInstance();
        genericRspDTO = marketingToolsMngClient.batchChangeState(genericDTO ,fileName,oprTyp) ;
        if ("MKM00000".equals(genericRspDTO.getMsgCd())) {
            return "Success";
        } else {
            return "Failure" ;
        }
    }


}
