package com.hisun.tms.mkm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/7/26
 */


@Entity
@Table(name = "mkm_batch_file_rec")
public class BatchFileRecDO {

    @Id
    @Column(name = "rec_no")
    private String recNo;

    @Column(name = "batch_file")
    private String batchFile;

    @Column(name = "ac_id")
    private String acId;

    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "process_dt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processDt;

    @Column(name = "tot_num")
    private Integer totNum;

    @Column(name = "tot_amt")
    private BigDecimal totAmt;

    @Column(name = "success_num")
    private Integer successNum;

    @Column(name = "success_amt")
    private BigDecimal successAmt;

    @Column(name = "failure_num")
    private Integer failureNum;

    @Column(name = "failure_amt")
    private BigDecimal failureAmt;

    @Column(name = "opr_typ")
    private String oprTyp;

    @Column(name = "process_sts")
    private String processSts;

    @Column(name = "result_file")
    private String resultFile;

    @Column(name = "cre_opr_id")
    private String creOprId;

    @Column(name = "upd_opr_id")
    private String updOprId;

    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "modify_time")
    private LocalDateTime modifyTime;


    public String getRecNo() {
        return recNo;
    }

    public void setRecNo(String recNo) {
        this.recNo = recNo;
    }

    public String getBatchFile() {
        return batchFile;
    }

    public void setBatchFile(String batchFile) {
        this.batchFile = batchFile;
    }

    public String getAcId() {
        return acId;
    }

    public void setAcId(String acId) {
        this.acId = acId;
    }

    public LocalDateTime getProcessDt() {
        return processDt;
    }

    public void setProcessDt(LocalDateTime processDt) {
        this.processDt = processDt;
    }

    public Integer getTotNum() {
        return totNum;
    }

    public void setTotNum(Integer totNum) {
        this.totNum = totNum;
    }

    public BigDecimal getTotAmt() {
        return totAmt;
    }

    public void setTotAmt(BigDecimal totAmt) {
        this.totAmt = totAmt;
    }

    public Integer getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(Integer successNum) {
        this.successNum = successNum;
    }

    public BigDecimal getSuccessAmt() {
        return successAmt;
    }

    public void setSuccessAmt(BigDecimal successAmt) {
        this.successAmt = successAmt;
    }

    public Integer getFailureNum() {
        return failureNum;
    }

    public void setFailureNum(Integer failureNum) {
        this.failureNum = failureNum;
    }

    public BigDecimal getFailureAmt() {
        return failureAmt;
    }

    public void setFailureAmt(BigDecimal failureAmt) {
        this.failureAmt = failureAmt;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getProcessSts() {
        return processSts;
    }

    public void setProcessSts(String processSts) {
        this.processSts = processSts;
    }

    public String getResultFile() {
        return resultFile;
    }

    public void setResultFile(String resultFile) {
        this.resultFile = resultFile;
    }

    public String getCreOprId() {
        return creOprId;
    }

    public void setCreOprId(String creOprId) {
        this.creOprId = creOprId;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}
