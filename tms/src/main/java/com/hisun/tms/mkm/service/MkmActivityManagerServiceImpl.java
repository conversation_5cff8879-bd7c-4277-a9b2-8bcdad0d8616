package com.hisun.tms.mkm.service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;

import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.mkm.req.dto.*;
import com.hisun.tms.common.util.GetOpr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.mkm.client.MarketActivityClient;
import com.hisun.lemon.mkm.req.dto.AddMarketActivityReqDTO;
import com.hisun.lemon.mkm.req.dto.DeleteMarketActivityReqDTO;
import com.hisun.lemon.mkm.req.dto.FindMarketActivityReqDTO;
import com.hisun.lemon.mkm.req.dto.UpdateMarketActivityReqDTO;
import com.hisun.tms.bil.model.ExcelCommonTempleModel;
import com.hisun.tms.bil.service.BaseReportService;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.mkm.dao.IMkmActivityDao;
import com.hisun.tms.mkm.dao.IMkmInstDao;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.MkmActivityDO;
import com.hisun.tms.mkm.model.MkmAtvUserDO;
import com.hisun.tms.mkm.model.MkmInstDO;
import com.hisun.tms.mkm.repository.MkmAtvUserRespository;
import com.hisun.tms.util.ExcelUtil;
import com.hisun.tms.util.HexUtil;

/**
 * Created by chen on 8/3 0003.
 */
@Service("mkmActivityManagerService")
@Transactional
public class MkmActivityManagerServiceImpl extends BaseReportService implements MkmActivityManagerService {
    @Resource
    private MarketActivityClient marketActivityClient;

    @Resource
    private IMkmActivityDao iMkmActivityDao;

    @Resource
    private IMkmInstDao iMkmInstDao;

    @Resource
    private MkmAtvUserRespository mkmAtvUserRespository;
    
    @Autowired
    private MessageSource messageSource;

    @Resource
    private GetOpr getOpr ;
    
    @Override
    public DataTablesOutput<MkmActivityDO> findAll(MartketFindInput input) {
        DataTablesOutput<MkmActivityDO> dataTablesOutput = new DataTablesOutput();

        GenericDTO<FindMarketActivityReqDTO> findMarketActivityReqDTOGenericDTO = GenericDTO.newInstance(new FindMarketActivityReqDTO());
        findMarketActivityReqDTOGenericDTO.getBody().setPageSize(input.getLength());

        findMarketActivityReqDTOGenericDTO.getBody().setPageNo(input.getStart()/input.getLength()+1);
        findMarketActivityReqDTOGenericDTO.getBody().setId( input.getExtra_search().get("id"));
        findMarketActivityReqDTOGenericDTO.getBody().setAtvNm(input.getExtra_search().get("atvNm"));

        Map<String ,Object> param = new HashMap<String ,Object>();
        param.put("id",input.getExtra_search().get("id"));
        param.put("atvNm",input.getExtra_search().get("atvNm"));
        param.put("status",input.getExtra_search().get("status"));
        param.put("noExmineStatus",input.getExtra_search().get("noExmineStatus"));
        param.put("examimeStatus",input.getExtra_search().get("examimeStatus"));
        param.put("endTime",input.getExtra_search().get("endTime").replaceAll("-","").trim());
        param.put("beginTime",input.getExtra_search().get("beginTime").replaceAll("-","").trim());
        param.put("pageEnd",input.getLength());
        param.put("pageBegin",input.getStart());

        List<MkmActivityDO> list= iMkmActivityDao.findByfindMarketActivityDTO(param);

        for ( MkmActivityDO mkmActivityDO : list ) {
            if (mkmActivityDO.getTotal() != null && mkmActivityDO.getRemainNum() != null ) {
                mkmActivityDO.setRemainNum(mkmActivityDO.getTotal() - mkmActivityDO.getRemainNum());
            }
            if (mkmActivityDO.getTotalAmt() != null && mkmActivityDO.getRemainAmt() != null ) {
                mkmActivityDO.setRemainAmt(mkmActivityDO.getTotalAmt().subtract(mkmActivityDO.getRemainAmt()));
            }
        }
        int total= iMkmActivityDao.counTotal(param);
        dataTablesOutput.setData(list);
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(total);
        return dataTablesOutput;
    }

    @Override
    public Object get(String id) {
        Map<String ,Object> param = new HashMap<String ,Object>();
        param.put("id",id);
        List<MkmActivityDO> list= iMkmActivityDao.findByfindMarketActivityDTO(param);
        for (MkmActivityDO marketActivityDO: list) {
            if (marketActivityDO.getDiscount() != null) {
                marketActivityDO.setDiscount(marketActivityDO.getDiscount().multiply(new BigDecimal(10)));
            }
            List<MkmInstDO> mkmInstDOList = iMkmInstDao.getAllInstIdByActId(marketActivityDO.getId());
            if (mkmInstDOList != null){
                StringBuilder s = new StringBuilder("");
                int i = 0;
                for (MkmInstDO mkmInstDO : mkmInstDOList) {
                    s.append(mkmInstDO.getInstId());
                    if (i != mkmInstDOList.size() -1) {
                        s.append(",");
                    }
                    i++;
                }
                marketActivityDO.setInstId(s.toString());
            }
        }

        return list.get(0);
    }

    @Override
    public String add(AddMarketActivityReqDTO addMarketActivityReqDTO, String filePath) {
        MkmActivityDO mkmActivityDO = new MkmActivityDO();
        GenericDTO<AddMarketActivityReqDTO> genericDTO = GenericDTO.newInstance(addMarketActivityReqDTO);
        String beginTime = parseDateTimeString(addMarketActivityReqDTO.getBeginTime());
        String endTime =  parseDateTimeString(addMarketActivityReqDTO.getEndTime());
      //  String valTm =  parseDateTimeString(addMarketActivityReqDTO.getCouponValTm());
        String invalTm =  parseDateTimeString(addMarketActivityReqDTO.getCouponInvalTm());
        addMarketActivityReqDTO.setBeginTime(beginTime);
        addMarketActivityReqDTO.setEndTime(endTime);
        //addMarketActivityReqDTO.setCouponValTm(valTm);
        addMarketActivityReqDTO.setCouponInvalTm(invalTm);
        addMarketActivityReqDTO.setCrtUserOpr(getOpr.getOperatorName());
        GenericRspDTO res= marketActivityClient.addMarketActivity(genericDTO);
        String atvId = (String) res.getBody();
        if ("MKM00000".equals(res.getMsgCd()) && JudgeUtils.isNotBlank(atvId) && JudgeUtils.isNotBlank(filePath)) {
            //处理上传的excle文件
            ExcelUtil excelUtil = null ;
            try {
                excelUtil = new ExcelUtil(HexUtil.hexStr2Str(filePath));

               List<List<String>> list = excelUtil.read(0,1,0);
               List<MkmAtvUserDO> userAtvMap  = new ArrayList<MkmAtvUserDO>();
               for (List<String> list1 : list ) {
                   MkmAtvUserDO mkmAtvUserDO = new MkmAtvUserDO();
                   mkmAtvUserDO.setAtvId(atvId);
                   mkmAtvUserDO.setUserId(list1.get(0));
                   mkmAtvUserDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
                   mkmAtvUserDO.setModifyTime(mkmAtvUserDO.getCreateTime());
                   userAtvMap.add(mkmAtvUserDO);
               }
               if (userAtvMap.size() > 0) {
                   List<MkmAtvUserDO> result = mkmAtvUserRespository.save(userAtvMap);
                   if (result.size() != userAtvMap.size()) {
                       mkmAtvUserRespository.deleteInBatch(result);
                       return "userFail";
                   }
               }

            } catch (Exception e) {
                e.printStackTrace();
                iMkmActivityDao.delete(atvId);
                return "0";
            } finally {
                if (excelUtil!=null) {
                    try {
                        File file = new File(filePath);
                        //删除文件
                        if (file.exists()) {
                            file.delete();
                        }
                        excelUtil.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

        }
        return res.getMsgCd();
    }

    @Override
    public String update(UpdateMarketActivityReqDTO updateMarketActivityReqDTO) {
        GenericDTO<UpdateMarketActivityReqDTO> genericDTO = GenericDTO.newInstance(updateMarketActivityReqDTO);
        if (updateMarketActivityReqDTO.getEndTimeSr() != null) {
            String endTime = parseDateTimeString(updateMarketActivityReqDTO.getEndTimeSr());
            updateMarketActivityReqDTO.setEndTimeSr(endTime);
        }
        updateMarketActivityReqDTO.setMdfUserOpr(getOpr.getOperatorName());
        GenericRspDTO res= marketActivityClient.updateMarketActivity(genericDTO);
        return res.getMsgCd();
    }

    @Override
    public String delete(String id) {
        GenericDTO<DeleteMarketActivityReqDTO> genericDTO = GenericDTO.newInstance(new DeleteMarketActivityReqDTO());
        genericDTO.getBody().setId(id);
        int i = iMkmActivityDao.delete(id);
        return i + "";
    }

    @Override
    public String manager( MkmActivityDO mkmActivityDO) {
        int i = iMkmActivityDao.update(mkmActivityDO);
        if(i == 1) {
            return "MKM00000";
        }
        return "";
    }


    String parseDateTimeString (String ds) {
        String beginTime = ds.replace("-","");
        beginTime = beginTime.replace(":","");
        beginTime = beginTime.replace(" ","");
        if (beginTime.indexOf("T") > 0) {
            beginTime = beginTime.replace("T" , "");
            if (beginTime.length() == 12) {
                beginTime = beginTime+"00";
            }
        }
        return beginTime;
    }
    
    /**
     * 生成月报表
     * @param reportPath
     * @param reportFilename
     * <AUTHOR> reportPath,reportFilename
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public void monthlyReport(String reportPath, String reportFilename){
		// 查询出审核通过并且在活动时间内的数据
        List<MkmActivityDO> mad = iMkmActivityDao.getEventsDuringDate();
        
        // 接收得到的值
        List<Object[]> datas = new ArrayList<>();
        mad.forEach(item ->{
        	String count = "0";
        	// 如果是海币
        	if ("02".equals(item.getMkTool())) {
        		// 根据活动id查询海币费用
        		count =  iMkmActivityDao.getHBCount(item.getId());
			}else if ("04".equals(item.getMkTool()) || "01".equals(item.getMkTool()) || "03".equals(item.getMkTool())) {
				// 表示是用券
				count = iMkmActivityDao.getVoucherCount(item.getId());
			}
        	datas.add(new String[]{item.getCostSide(), item.getId(), count == null ? "0.00" : count});
        });       
        
        // 取到费用总计
        double total=0;
        for (int i = 0; i < datas.size(); i++) {
        	String[] data =  (String[]) datas.get(i);
        	total += data[2] == null ? 0 : Double.parseDouble(data[2]);
		}


        Locale locale = LocaleContextHolder.getLocale();
        // 表头
        String title = messageSource .getMessage("mkm.monthlyReport.title", null, locale);
        // 时间格式化
        String timeFormat = messageSource .getMessage("mkm.monthlyReport.timeFormat", null, locale);
        // 报表月份
        String time = messageSource .getMessage("mkm.monthlyReport.time", null, locale);
        // 金额单位
        String monetaryUnit  = messageSource.getMessage("mkm.monthlyReport.monetaryUnit", null, locale);
        // 费用承当方
        String bear = messageSource.getMessage("mkm.monthlyReport.bear", null, locale);
        // 活动编号
        String activityId = messageSource.getMessage("mkm.monthlyReport.activityId", null, locale);
        // 营销费用
        String cost = messageSource.getMessage("mkm.monthlyReport.cost", null, locale);
        // 总金额
        String totalSum = messageSource.getMessage("mkm.monthlyReport.totalSum", null, locale);
        // 小计
        String subtotal = messageSource.getMessage("mkm.monthlyReport.subtotal", null, locale);

		// 对日期进行格式化
        Date d = new Date();  
        SimpleDateFormat sdf = new SimpleDateFormat(timeFormat);
        String dateNowStr = sdf.format(d);  
        
		// 设置表头信息
        LinkedHashMap titleMap = new LinkedHashMap<>();
        titleMap.put(time + dateNowStr, "");
        titleMap.put(monetaryUnit, "");
        
        // 设置各列内容与列宽
        LinkedHashMap columHeader = new LinkedHashMap<>();
        columHeader.put(bear, 7200);
        columHeader.put(activityId, 7200);
        columHeader.put(cost, 7200);
        
        //汇总信息
        Map sumaryItem = new LinkedHashMap<>();
        Map sumaryMap = new HashMap<>();
        sumaryItem.put(2, total);// 小计
        sumaryMap.put(totalSum, sumaryItem);

        // 对相同名称进行排序
        Collections.sort(datas, (Object[] o1, Object[] o2)-> o1[0].toString().compareTo(o2[0].toString()));
        
        // 对各公司费用进行小计
        Map map = new HashMap<>();
        double totals = 0.00;
        for (int i = 0; i < datas.size(); i++) {
        	// 判断map是否有这个key
        	if(! map.containsKey(datas.get(i)[0].toString())){
        		// 判断是否是第一次执行map，如果不是则把计算的金额(totals)插入datas该list中
		        if(map.size() > 0){
			        datas.add(i, new String[]{datas.get(i-1)[0].toString() + subtotal, "", totals+""});
			        i++;
			        totals = 0.00;// 金额清0
		        }
		        // 将公司名存入map中
		        map.put(datas.get(i)[0].toString(), datas.get(i)[0].toString());
		        totals += Double.parseDouble(datas.get(i)[2].toString());
	        }else{
	        	// else是该map已经有这个key(公司名),所以清空公司名
		        datas.get(i)[0] = "";
		        totals += Double.parseDouble(datas.get(i)[2].toString());
	        }
        }
        // 最后一个公司名的金额totals已经计算好了，只需获取插入datas
        datas.add(new String[]{ subtotal, "", totals+""});
        
        ExcelCommonTempleModel excelCommonTempleModel = new ExcelCommonTempleModel(reportPath, reportFilename, title, title, titleMap, columHeader, sumaryMap);
        super.createExcel(datas, excelCommonTempleModel);
    }

    //活动审核
    @Override
    public String maintian(MkmActivityDO mkmActivityDO) {
        MaintainActivityReqDTO maintainActivityReqDTO = new MaintainActivityReqDTO();
        maintainActivityReqDTO.setExamineStatus(mkmActivityDO.getExamineStatus());
        maintainActivityReqDTO.setType("01");
        maintainActivityReqDTO.setId(mkmActivityDO.getId());
        GenericDTO<MaintainActivityReqDTO> genericDTO = GenericDTO.newInstance(maintainActivityReqDTO);
        MkmActivityDO uM = new MkmActivityDO();
        uM.setId(mkmActivityDO.getId());
        uM.setExamineReson(mkmActivityDO.getExamineReson());
        iMkmActivityDao.update(uM) ;
        GenericRspDTO<NoBody>  genericRspDTO = marketActivityClient.maintainMarketActivity(genericDTO);
        if ("MKM00000".equals(genericRspDTO.getMsgCd())) {
            return genericRspDTO.getMsgCd() ;
        }
        return genericRspDTO.getMsgInfo();
    }
}
