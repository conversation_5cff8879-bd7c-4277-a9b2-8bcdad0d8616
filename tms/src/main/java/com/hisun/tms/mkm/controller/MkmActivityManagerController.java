package com.hisun.tms.mkm.controller;

import com.hisun.lemon.mkm.req.dto.AddMarketActivityReqDTO;
import com.hisun.lemon.mkm.req.dto.UpdateMarketActivityReqDTO;
import com.hisun.tms.common.util.BeanUtils;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.mkm.model.AddMarketActivityReqDO;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.MkmActivityDO;
import com.hisun.tms.mkm.service.MkmActivityManagerService;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by chen on 8/3 0003.
 */
@Controller
@RequestMapping("/mkm/activity")
public class MkmActivityManagerController {

    @Resource
    private MkmActivityManagerService mkmActivityManagerService ;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/activity') or hasRole('ROLE_ADMIN')")
    public ModelAndView activityList() {
       // List list = couponRepository.findDetail("select * from mkm_inst where atv_id = 0000001001");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/activity/index");
        return modelAndView;
    }

//    @GetMapping(value = "/form")
//    public ModelAndView form(@RequestParam(value = "id", required = false) String id) {
//        ModelAndView modelAndView = new ModelAndView();
//        modelAndView.setViewName("mkm/activity/form");
//        String api = "/mkm/activity/add";
//        modelAndView.addObject("mkTool", "");
//        if (StringUtils.isNotBlank(id)) {
//            MkmActivityDO marketActivityDetail = (MkmActivityDO) mkmActivityManagerService.get(id);
//           if (marketActivityDetail != null && marketActivityDetail.getDiscount() != null){
//               marketActivityDetail.setDiscount(marketActivityDetail.getDiscount().multiply(BigDecimal.valueOf(10)));
//           }
//            modelAndView.addObject("mkActivity", marketActivityDetail);
//            modelAndView.addObject("mkTool", marketActivityDetail.getMkTool());
//            api = "/mkm/activity/modify/" + id;
//        }
//        modelAndView.addObject("api", api);
//        return modelAndView;
//    }

    @PostMapping(value = "/getActivity")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/activity') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public MkmActivityDO getActivity(@RequestParam(value = "id", required = false) String id){
        MkmActivityDO marketActivityDetail = (MkmActivityDO) mkmActivityManagerService.get(id);
        return marketActivityDetail;
    };
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/activity') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<MkmActivityDO> findAll(@Valid @RequestBody MartketFindInput input) {
        return mkmActivityManagerService.findAll(input);
    }
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/activity:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> add(AddMarketActivityReqDO addMarketActivityReqDO) {
        Map<String,String> map =  new HashMap<String,String>();
        AddMarketActivityReqDTO dto = new AddMarketActivityReqDTO();
        BeanUtils.copyProperties(dto ,addMarketActivityReqDO);
        String result =  mkmActivityManagerService.add(dto ,addMarketActivityReqDO.getFilePath() );

        map.put("result",result);
        return map;
    }
    @PostMapping(value = "/modify/{id}")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/activity:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> modify(UpdateMarketActivityReqDTO updateMarketActivityReqDTO, String endTime) {
        Map<String,String> map =  new HashMap<String,String>();
        updateMarketActivityReqDTO.setEndTimeSr(endTime);
        String result = mkmActivityManagerService.update(updateMarketActivityReqDTO);
        map.put("result",result);
        return map;
    }
    @DeleteMapping(value = "/delete/{id}")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/activity:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        String result ="";
        try {
            result = mkmActivityManagerService.delete(id);
        }catch (Exception e) {
            result = "";
        }
       return result;
    }
    @PostMapping(value = "/manager")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/activity') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> manager(MkmActivityDO mkmActivityDO ) {
        Map<String,String> map =  new HashMap<String,String>();
        mkmActivityDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        String result = mkmActivityManagerService.manager(mkmActivityDO);
        map.put("result",result);
        return map;
    }
    @PostMapping(value = "/maintian")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/activity') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> maintian(MkmActivityDO mkmActivityDO ) {
        Map<String,String> map =  new HashMap<String,String>();
        mkmActivityDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        String result = mkmActivityManagerService.maintian(mkmActivityDO);
        map.put("result",result);
        return map;
    }
    @GetMapping(value = "/examine")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/examine') or hasRole('ROLE_ADMIN')")
    public ModelAndView examine() {
        // List list = couponRepository.findDetail("select * from mkm_inst where atv_id = 0000001001");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/activity/examine");
        return modelAndView;
    }
}
