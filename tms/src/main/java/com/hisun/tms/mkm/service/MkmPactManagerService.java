package com.hisun.tms.mkm.service;

import com.hisun.tms.mkm.model.PactManagerDO;
import com.hisun.tms.mkm.model.MartketFindInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 协议管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 10:25
 */
public interface MkmPactManagerService {
    /**
     * 查询所有协议
     * 
     * @param input 查询条件
     * @return 协议列表
     */
    DataTablesOutput<PactManagerDO> findAll(MartketFindInput input);

    /**
     * 获取单个协议
     * 
     * @param id 协议ID
     * @return 协议对象
     */
    Object get(Integer id);

    /**
     * 添加协议
     * 
     * @param pactManagerDO 协议对象
     * @return 结果
     */
    String add(PactManagerDO pactManagerDO);

    /**
     * 更新协议
     * 
     * @param pactManagerDO 协议对象
     * @return 结果
     */
    String update(PactManagerDO pactManagerDO);

    /**
     * 删除协议
     * 
     * @param id 协议ID
     * @return 结果
     */
    String delete(Integer id);
}