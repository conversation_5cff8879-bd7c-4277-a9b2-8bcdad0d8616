package com.hisun.tms.mkm.controller;

import com.hisun.tms.mkm.model.FAQManagerDO;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.service.MkmFAQManagerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 常见问题管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 19:44
 */
@Controller
@RequestMapping("/mkm/faq")
public class MkmFAQManagerController {

    private static final Logger logger = LoggerFactory.getLogger(MkmFAQManagerController.class);

    @Resource
    private MkmFAQManagerService mkmFAQManagerService;

    /**
     * 常见问题列表页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/faq') or hasRole('ROLE_ADMIN')")
    public ModelAndView faqList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/faq/index");
        return modelAndView;
    }

    /**
     * 获取单个常见问题
     */
    @PostMapping(value = "/getFAQ")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/faq') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public FAQManagerDO getFAQ(@RequestParam(value = "id", required = false) Integer id) {
        try {
            FAQManagerDO faqDetail = (FAQManagerDO) mkmFAQManagerService.get(id);
            return faqDetail;
        } catch (Exception e) {
            logger.error("获取常见问题详情失败: ", e);
            return null;
        }
    }

    /**
     * 查询所有常见问题
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/faq') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<FAQManagerDO> findAll(@Valid @RequestBody MartketFindInput input) {
        try {
            logger.debug("查询常见问题列表，参数: {}", input);
            DataTablesOutput<FAQManagerDO> result = mkmFAQManagerService.findAll(input);
            logger.debug("查询常见问题列表，结果数量: {}", result.getData().size());
            return result;
        } catch (Exception e) {
            logger.error("查询常见问题列表失败: ", e);
            // 返回空结果而不是null，避免前端解析错误
            DataTablesOutput<FAQManagerDO> emptyResult = new DataTablesOutput<>();
            emptyResult.setDraw(input.getDraw());
            emptyResult.setRecordsTotal(0);
            emptyResult.setRecordsFiltered(0);
            return emptyResult;
        }
    }

    /**
     * 添加常见问题
     */
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/faq:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(FAQManagerDO faqManagerDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = mkmFAQManagerService.add(faqManagerDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("添加常见问题失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 修改常见问题
     */
    @PostMapping(value = "/modify/{id}")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/faq:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(FAQManagerDO faqManagerDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = mkmFAQManagerService.update(faqManagerDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("修改常见问题失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 删除常见问题
     */
    @DeleteMapping(value = "/delete/{id}")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/faq:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public String delete(@PathVariable("id") Integer id) {
        String result = "";
        try {
            result = mkmFAQManagerService.delete(id);
        } catch (Exception e) {
            logger.error("删除常见问题失败: ", e);
            result = "";
        }
        return result;
    }
}
