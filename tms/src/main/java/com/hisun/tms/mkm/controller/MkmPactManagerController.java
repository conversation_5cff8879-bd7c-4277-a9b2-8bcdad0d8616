package com.hisun.tms.mkm.controller;

import com.hisun.tms.mkm.model.PactManagerDO;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.service.MkmPactManagerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 协议管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 10:21
 */
@Controller
@RequestMapping("/mkm/pact")
public class MkmPactManagerController {

    private static final Logger logger = LoggerFactory.getLogger(MkmPactManagerController.class);

    @Resource
    private MkmPactManagerService mkmPactManagerService;

    /**
     * 协议列表页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/pact') or hasRole('ROLE_ADMIN')")
    public ModelAndView pactList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("mkm/pact/index");
        return modelAndView;
    }

    /**
     * 获取单个协议
     */
    @PostMapping(value = "/getPact")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/pact') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public PactManagerDO getPact(@RequestParam(value = "id", required = false) Integer id) {
        try {
            PactManagerDO pactDetail = (PactManagerDO) mkmPactManagerService.get(id);
            return pactDetail;
        } catch (Exception e) {
            logger.error("获取协议详情失败: ", e);
            return null;
        }
    }

    /**
     * 查询所有协议
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/pact') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<PactManagerDO> findAll(@Valid @RequestBody MartketFindInput input) {
        try {
            logger.debug("查询协议列表，参数: {}", input);
            DataTablesOutput<PactManagerDO> result = mkmPactManagerService.findAll(input);
            logger.debug("查询协议列表，结果数量: {}", result.getData().size());
            return result;
        } catch (Exception e) {
            logger.error("查询协议列表失败: ", e);
            // 返回空结果而不是null，避免前端解析错误
            DataTablesOutput<PactManagerDO> emptyResult = new DataTablesOutput<>();
            emptyResult.setDraw(input.getDraw());
            emptyResult.setRecordsTotal(0);
            emptyResult.setRecordsFiltered(0);
            return emptyResult;
        }
    }

    /**
     * 添加协议
     */
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/pact:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(PactManagerDO pactManagerDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = mkmPactManagerService.add(pactManagerDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("添加协议失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 修改协议
     */
    @PostMapping(value = "/modify/{id}")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/pact:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(PactManagerDO pactManagerDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = mkmPactManagerService.update(pactManagerDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("修改协议失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 删除协议
     */
    @DeleteMapping(value = "/delete/{id}")
    @PreAuthorize("hasPermission('','/busmgr/mkmmgr/pact:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public String delete(@PathVariable("id") Integer id) {
        String result = "";
        try {
            result = mkmPactManagerService.delete(id);
        } catch (Exception e) {
            logger.error("删除协议失败: ", e);
            result = "";
        }
        return result;
    }
}
