package com.hisun.tms.mkm.service;

import com.hisun.tms.mkm.model.FAQManagerDO;
import com.hisun.tms.mkm.model.MartketFindInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 常见问题管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 19:50
 */
public interface MkmFAQManagerService {
    /**
     * 查询所有常见问题
     * 
     * @param input 查询条件
     * @return 常见问题列表
     */
    DataTablesOutput<FAQManagerDO> findAll(MartketFindInput input);

    /**
     * 获取单个常见问题
     * 
     * @param id 常见问题ID
     * @return 常见问题对象
     */
    Object get(Integer id);

    /**
     * 添加常见问题
     * 
     * @param faqManagerDO 常见问题对象
     * @return 结果
     */
    String add(FAQManagerDO faqManagerDO);

    /**
     * 更新常见问题
     * 
     * @param faqManagerDO 常见问题对象
     * @return 结果
     */
    String update(FAQManagerDO faqManagerDO);

    /**
     * 删除常见问题
     * 
     * @param id 常见问题ID
     * @return 结果
     */
    String delete(Integer id);
}