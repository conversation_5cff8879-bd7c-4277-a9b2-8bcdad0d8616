package com.hisun.tms.mkm.service;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.mkm.dao.IMsgInfoDao;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.MsgInfoDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 国际化消息信息管理服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/1
 */
@Service("msgInfoManagerService")
@Transactional
public class MsgInfoManagerServiceImpl implements MsgInfoManagerService {

    @Resource
    private IMsgInfoDao iMsgInfoDao;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<MsgInfoDO> findAll(MartketFindInput input) {
        DataTablesOutput<MsgInfoDO> dataTablesOutput = new DataTablesOutput<>();

        Map<String, Object> param = new HashMap<>();
        param.put("msgCd", input.getExtra_search().get("msgCd"));
        param.put("language", input.getExtra_search().get("language"));
        param.put("msgInfo", input.getExtra_search().get("msgInfo"));
        param.put("scenario", input.getExtra_search().get("scenario"));
        param.put("pageEnd", input.getLength());
        param.put("pageBegin", input.getStart());

        // 先获取总记录数，再获取分页数据
        int total = iMsgInfoDao.countTotal(param);
        List<MsgInfoDO> list = iMsgInfoDao.findByCondition(param);

        // 设置DataTablesOutput的属性
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setRecordsFiltered(total);

        // 设置数据
        if (list == null) {
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(list);
        }

        return dataTablesOutput;
    }

    @Override
    public Object get(String msgCd, String language, String scenario) {
        return iMsgInfoDao.getByPrimaryKey(msgCd, language, scenario);
    }

    @Override
    public String add(MsgInfoDO msgInfoDO) {
        // 设置创建时间和修改时间
        Date currentDate = new Date();
        msgInfoDO.setCreateTime(currentDate);
        msgInfoDO.setModifyTime(currentDate);

        // 如果场景为空，设置默认值
        if (msgInfoDO.getScenario() == null || msgInfoDO.getScenario().trim().isEmpty()) {
            msgInfoDO.setScenario("*");
        }

        int result = iMsgInfoDao.insert(msgInfoDO);
        if (result == 1) {
            return "MKM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String update(MsgInfoDO msgInfoDO) {
        // 设置修改时间
        msgInfoDO.setModifyTime(new Date());

        int result = iMsgInfoDao.update(msgInfoDO);
        if (result == 1) {
            return "MKM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String delete(String msgCd, String language, String scenario) {
        int result = iMsgInfoDao.delete(msgCd, language, scenario);
        return result + "";
    }
}