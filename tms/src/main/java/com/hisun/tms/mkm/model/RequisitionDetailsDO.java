package com.hisun.tms.mkm.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 用户营销工具领用明细数据对象
 *
 * <AUTHOR>
 * @create 2017/7/24
 */
public class RequisitionDetailsDO {

    private String mobile;

    private LocalDate releaseDt;

    private LocalTime releaseTm;

    private String atvId;
    private String atvNm;

    private String mkTool;
    private Integer releaseNum;
    private Integer useredNum;
    private Integer overdueNum;
    private Integer count;

    private BigDecimal couponAmt;

    private String status;

    private String mkmType;

    public String getMkmType() {
        return mkmType;
    }

    public void setMkmType(String mkmType) {
        this.mkmType = mkmType;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public LocalDate getReleaseDt() {
        return releaseDt;
    }

    public void setReleaseDt(LocalDate releaseDt) {
        this.releaseDt = releaseDt;
    }

    public LocalTime getReleaseTm() {
        return releaseTm;
    }

    public void setReleaseTm(LocalTime releaseTm) {
        this.releaseTm = releaseTm;
    }

    public String getAtvId() {
        return atvId;
    }

    public void setAtvId(String atvId) {
        this.atvId = atvId;
    }

    public String getAtvNm() {
        return atvNm;
    }

    public void setAtvNm(String atvNm) {
        this.atvNm = atvNm;
    }

    public String getMkTool() {
        return mkTool;
    }

    public void setMkTool(String mkTool) {
        this.mkTool = mkTool;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getReleaseNum() {
        return releaseNum;
    }

    public void setReleaseNum(Integer releaseNum) {
        this.releaseNum = releaseNum;
    }

    public Integer getUseredNum() {
        return useredNum;
    }

    public void setUseredNum(Integer useredNum) {
        this.useredNum = useredNum;
    }

    public Integer getOverdueNum() {
        return overdueNum;
    }

    public void setOverdueNum(Integer overdueNum) {
        this.overdueNum = overdueNum;
    }
}
