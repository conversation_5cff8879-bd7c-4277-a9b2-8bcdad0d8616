package com.hisun.tms.mkm.service;

import com.hisun.lemon.mkm.req.dto.AddMarketActivityReqDTO;
import com.hisun.lemon.mkm.req.dto.UpdateMarketActivityReqDTO;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.MkmActivityDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * Created by chen on 8/3 0003.
 */
public interface MkmActivityManagerService {
    DataTablesOutput<MkmActivityDO> findAll(MartketFindInput input);

    Object get(String id);

    String add(AddMarketActivityReqDTO addMarketActivityReqDTO, String filePath);

    String update(UpdateMarketActivityReqDTO updateMarketActivityReqDTO);

    String delete(String id);

    String manager(MkmActivityDO mkmActivityDO);

    /**
     * 生成营销工具月报表
     * @return
     */
    void monthlyReport(String reportPath, String reportFilename);

    /**
     * 活动审核
     * @param mkmActivityDO
     * @return
     */
    String maintian(MkmActivityDO mkmActivityDO);
}