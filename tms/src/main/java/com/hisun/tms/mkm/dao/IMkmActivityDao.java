package com.hisun.tms.mkm.dao;

import com.hisun.tms.mkm.model.MkmActivityDO;

import java.util.List;
import java.util.Map;

/**
 * Created by chen on 8/25 0025.
 */
public interface IMkmActivityDao {
    List<MkmActivityDO> findByfindMarketActivityDTO(Map<String,Object> param);
    int counTotal(Map<String,Object> param);

    int delete(String id);

    int update(MkmActivityDO mkmActivityDO);
    
    /**
     * 查询审核通过并且在活动时间内的数据
     * @return
     */
    List<MkmActivityDO> getEventsDuringDate();
    
    /**
     * 根据atvId查询海币费用
     * @param atvId
     * @return
     */
    String getHBCount(String atvId);
    
    /**
     * 根据atvId查询券费用
     * @param atvId
     * @return
     */
    String getVoucherCount(String atvId);
}
