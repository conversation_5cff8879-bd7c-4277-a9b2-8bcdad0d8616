package com.hisun.tms.urm.service;


import com.hisun.lemon.common.utils.JudgeUtils;

import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.urm.model.UserHistoryInfo;
import com.hisun.tms.urm.model.UserParamInput;
import com.hisun.tms.urm.repository.DatatablesUserHistoryRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("userHistoryServiceImpl")
public class UserHistoryServiceImpl implements UserHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(UserHistoryServiceImpl.class);

    @Autowired
    private DatatablesUserHistoryRepository datatablesUserHistoryRepository;


    @Override
    @Transactional("urmTransactionManager")
    public DataTablesOutput<UserHistoryInfo> findAll(UserParamInput input) {
    	String userId = input.getExtra_search().get("userId");
		String mblNo = input.getExtra_search().get("mblNo");
    	Specification<UserHistoryInfo> orderQueryParam=Specifications.<UserHistoryInfo>and()
    			.eq(JudgeUtils.isNotBlank(userId), "userId", userId)
    			.like(JudgeUtils.isNotBlank(mblNo), "mblNo", "%"+mblNo)
    			.eq("usrLvl", "0")
                .build();
        DataTablesOutput<UserHistoryInfo> dataTablesOutput = datatablesUserHistoryRepository.findAll(input, orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }
}
