package com.hisun.tms.urm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Created by chen on 9/2 0002.
 */

@Entity
@Table(name = "urm_safe_inf")
public class SafeInfoDO {

    @Column(name = "user_id")
    private String userId;

    @Column(name = "MBL_NO")
    private String mblNo;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "OPR_TYP")
    private String oprTyp;

    @Id
    @Column(name = "SAFE_ID")
    private String safeId;

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }
}
