package com.hisun.tms.urm.controller;

import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.tms.urm.model.UserKybDetail;
import com.hisun.tms.urm.model.dto.KybAuditReq;
import com.hisun.tms.urm.model.dto.KybDetailReq;
import com.hisun.tms.urm.model.dto.QueryKybDTO;
import com.hisun.tms.urm.model.dto.QueryResultKybDTO;
import com.hisun.tms.urm.service.UserKybInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.List;

/**
 * KYB复核Controller
 */
@Api(value = "处理KYB复核")
@Controller
@RequestMapping(value = "/urm/mermgr/kyb/review")
@PreAuthorize("hasPermission('','/busmgr/mermgr/kybreview') or hasRole('ROLE_ADMIN')")
public class UserKybReviewController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(UserKybReviewController.class);

    @Resource
    UserKybInfoService userKybInfoService;

    /**
     * 获取KYB复核列表
     */
    @ResponseBody
    @PostMapping(value = "/list")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kybreview') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "获取KYB复核列表")
    @ApiOperation(value = "获取KYB复核列表", notes = "获取KYB复核列表")
    public GenericRspDTO<QueryResultKybDTO> queryKybReview(@RequestBody GenericDTO<QueryKybDTO> req) {
        QueryKybDTO queryDTO = req.getBody();
        // 设置auditFlag为1，表示查询已初审的数据
        queryDTO.setAuditFlag("1");
        List<QueryResultKybDTO.QueryKyb> queryList = this.userKybInfoService.queryKybAudit(queryDTO);
        QueryResultKybDTO query = new QueryResultKybDTO();
        query.setQueryKybList(queryList);
        GenericRspDTO<QueryResultKybDTO> dto = new GenericRspDTO<>();
        dto.setBody(query);
        return dto;
    }

    /**
     * 查询KYB详情
     */
    @ResponseBody
    @PostMapping(value = "/detail")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kybreview') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "查询KYB详情")
    @ApiOperation(value = "查询KYB详情", notes = "查询KYB详情")
    public GenericRspDTO<UserKybDetail> queryKybDetail(@RequestBody GenericDTO<KybDetailReq> req) {
        String userId = req.getBody().getUserId();
        Integer kybId = req.getBody().getKybId();
        UserKybDetail kybDetail = userKybInfoService.detail(userId, kybId);
        GenericRspDTO<UserKybDetail> dto = new GenericRspDTO<>();
        dto.setBody(kybDetail);
        return dto;
    }

    /**
     * 复核KYB
     */
    @ResponseBody
    @PostMapping(value = "/audit")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kybreview') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "复核KYB")
    @ApiOperation(value = "复核KYB", notes = "复核KYB")
    public GenericRspDTO<NoBody> reviewKyb(@RequestBody GenericDTO<KybAuditReq> req) {
        KybAuditReq kybAuditReq = req.getBody();
        // 设置审核次数为1，表示复核
        kybAuditReq.setAuditTimes(1);
        userKybInfoService.kybAudit(kybAuditReq);
        return new GenericRspDTO<>();
    }

    /**
     * 返回KYB复核管理页面
     */
    @RequestMapping(value = "/page")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kybreview') or hasRole('ROLE_ADMIN')")
    public ModelAndView reviewPage() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/kyb/review");
        return modelAndView;
    }
}