package com.hisun.tms.urm.controller;



import com.hisun.tms.urm.service.MercLoginPswdResetService;
import com.hisun.tms.urm.service.MercPayPswdResetService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/busmgr/mercloginpswdresetctrl")
public class MercLoginPswdResetController {

    private static final Logger logger = LoggerFactory.getLogger(KeyResetController.class);

    @Autowired
    private MercLoginPswdResetService mercLoginPswdResetService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercloginpswdreset') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mercctrlinfo/LoginPswdForm");
        return modelAndView;
    }

    @PostMapping(value = "query")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercloginpswdreset') or hasRole('ROLE_ADMIN')")
    public Map<String,String> getUserInf(@RequestParam(value = "loginId", required = true) String loginId) {
        return mercLoginPswdResetService.getUserInf(loginId);
    }
    
    @PostMapping(value = "keyreset")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercloginpswdreset') or hasRole('ROLE_ADMIN')")
    public Map<String,String> restLoginPswd(@RequestParam(value = "loginId", required = true) String loginId) {
        return mercLoginPswdResetService.restLoginPswd(loginId);
    }
}
