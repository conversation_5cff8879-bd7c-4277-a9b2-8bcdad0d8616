package com.hisun.tms.urm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDate;

/**
 * Created by chen on 8/30 0030.
 */

@Entity
@Table(name = "urm_user_basic_inf")
public class UserBaseInfo {
    @Id
    @Column(name = "user_id")
    private String userId;

    @Column(name = "MBL_NO")
    private String mblNo;

    @Column(name = "USR_STS")
    private String useSts ;


    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "USR_REG_DT")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate useRegDt ;

    public String getUseSts() {
        return useSts;
    }

    public void setUseSts(String useSts) {
        this.useSts = useSts;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public LocalDate getUseRegDt() {
        return useRegDt;
    }

    public void setUseRegDt(LocalDate useRegDt) {
        this.useRegDt = useRegDt;
    }
}
