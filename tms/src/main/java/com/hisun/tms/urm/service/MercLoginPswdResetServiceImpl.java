package com.hisun.tms.urm.service;
import java.util.HashMap;


import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.client.UserPasswordClient;
import com.hisun.lemon.urm.dto.ChangPwdDTO;
import com.hisun.tms.urm.model.SafeLoginDO;
import com.hisun.tms.urm.model.UrmCprExtInfDO;
import com.hisun.tms.urm.model.UserBasicInfo;
import com.hisun.tms.urm.model.UserSafeInfo;
import com.hisun.tms.urm.repository.MercInfoRepository;
import com.hisun.tms.urm.repository.MercSafelInfoRepository;
import com.hisun.tms.urm.repository.SafeLoginRespository;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("mercLoginPswdResetService")
public class MercLoginPswdResetServiceImpl implements MercLoginPswdResetService {

    private static final Logger logger = LoggerFactory.getLogger(MercLoginPswdResetServiceImpl.class);
    
    @Autowired
    private UserPasswordClient userPasswordClient;
    @Autowired
    private MercSafelInfoRepository mercSafelInfoRepository;
    @Autowired
    private SafeLoginRespository safeLoginRespository;
    
	@Override
	public Map<String, String> getUserInf(String loginId) {
		SafeLoginDO safeLoginInfo=safeLoginRespository.getInfoByLoginId(loginId);
		UserSafeInfo userSafeInfo=mercSafelInfoRepository.getInfoBySafeId(safeLoginInfo.getSafeId());
		Map<String,String> map =  new HashMap<String,String>();
		map.put("usrNm", safeLoginInfo.getDiplayNm());
		map.put("mblNo", userSafeInfo.getMblNo());
		return map;
	}

	@Override
	public Map<String,String> restLoginPswd(String loginId) {
		GenericRspDTO<NoBody> genericRspDTO=userPasswordClient.resetRandomLoginPwd(loginId);
		Map<String,String> map =  new HashMap<String,String>();
		map.put("msgCd", genericRspDTO.getMsgCd());
		map.put("msgInfo", genericRspDTO.getMsgInfo());
		return map;
	}

}
