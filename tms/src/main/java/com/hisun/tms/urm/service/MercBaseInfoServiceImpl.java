package com.hisun.tms.urm.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.cpi.client.EbankpayClient;
import com.hisun.lemon.cpi.dto.SubMercReqDTO;
import com.hisun.lemon.cpi.dto.SubMercRspDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.jcommon.phonenumber.PhoneNumberUtils;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.MerRegisterDTO;
import com.hisun.tms.acm.model.AcmAcBal;
import com.hisun.tms.acm.repository.AcmAcBalRepository;
import com.hisun.tms.common.util.BeanUtils;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.model.CpiSubMercCastDO;
import com.hisun.tms.cpt.repository.CpiSubMercCastRepository;
import com.hisun.tms.csm.model.SettleBaseDO;
import com.hisun.tms.csm.model.SettleCardDO;
import com.hisun.tms.csm.repository.SettleBaseDORespository;
import com.hisun.tms.csm.repository.SettleCardRespository;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.sys.common.Constants;
import com.hisun.tms.sys.model.MercRegisterSeq;
import com.hisun.tms.sys.repository.DataTableMercRegisterRepository;
import com.hisun.tms.sys.repository.MercRegisterSeqRepository;
import com.hisun.tms.tfm.model.MerchantRateRuleDO;
import com.hisun.tms.tfm.model.MerchantRateRulePK;
import com.hisun.tms.tfm.repository.MerchantRateRuleRespository;
import com.hisun.tms.urm.dao.MercItfInfoDao;
import com.hisun.tms.urm.model.*;
import com.hisun.tms.urm.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.jpa.datatables.mapping.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
@Service
@Transactional("urmTransactionManager")
public class MercBaseInfoServiceImpl implements MercBaseInfoService {

    private static final Logger logger = LoggerFactory.getLogger(MercBaseInfoService.class);

    private final static String DATE_FORMAT = "yyyy-MM-dd" ;

    @Resource
    private DataTablesMercExtInfoRepository dataTablesMercExtInfoRepository;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Resource
    private MercRegisterSeqRepository mercRegisterSeqRepository;

    @Resource
    private DataTableMercRegisterRepository dataTableMercRegisterRepository;

    @Resource
    private SettleBaseDORespository settleBaseDORespository;

    @Resource
    private SettleCardRespository settleCardRespository;

    @Resource
    private MerchantRateRuleRespository merchantRateRuleRespository;

    @Resource
    private UrmCprExtInfRespository urmCprExtInfRespository;

    @Resource
    private CprResInfRespository cprResInfRespository;

    @Resource
    private SafeInfoRespository safeInfoRespository;

    @Resource
    private SafeLoginRespository safeLoginRespository;

    @Resource
    private GetOpr getOpr;

    @Resource
    private MessageSource messageSource;

    @Resource
    private CmmServerClient cmmServerClient;

    @Resource
    private ConstantParamClient constantParamClient;


    @Resource
    private EbankpayClient ebankpayClient;

    @Resource
    private DatatablesMercInfoRepository datatablesMercInfoRepository;
    @Resource
    private CpiSubMercCastRepository cpiSubMercCastRepository;

    @Resource
    private AcmAcBalRepository acmAcBalRepository;

    @Resource
    private MercItfInfoDao mercItfInfoDao ;

    @Override
    public DataTablesOutput<MercExtInfoModel> findAll(DataTablesInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        String sts = dataTablesInput.getColumns().get(2).getSearch().getValue();
        String merName = dataTablesInput.getColumns().get(1).getSearch().getValue();
        //审核不通过数据
        DataTablesOutput<MercExtInfoModel> dataTablesOutput = new  DataTablesOutput<MercExtInfoModel>();
        if ("2".equals(sts) || "3".equals(sts)) {
            DataTablesOutput<MercRegisterSeq> output = new  DataTablesOutput<MercRegisterSeq>();
            DataTablesInput input = new DataTablesInput();
            BeanUtils.copyProperties(input ,dataTablesInput);
            List<Column> columns = new ArrayList<Column>();
            List<Order> orders = new ArrayList<Order>();
            Order order = new Order();
            orders.add(order);
            order.setColumn(0);
            order.setDir("desc");
            input.setColumns(columns);
            input.setOrder(orders);
            Column id = new Column();
            id.setData("id");
            id.setSearchable(true);
            id.setName("");
            id.setOrderable(true);
            id.setSearch(new Search());
            id.getSearch().setValue("");
            id.getSearch().setRegex(false);
            columns.add(id);

            Column examinStatus = new Column();
            examinStatus.setData("examinStatus");
            examinStatus.setSearchable(true);
            examinStatus.setSearch(new Search());
            examinStatus.setName("");
            examinStatus.setOrderable(true);
            if ("2".equals(sts)) {
                examinStatus.getSearch().setValue(Constants.EXAMINE_STATUS_NO_PASS);
            } else {
                examinStatus.getSearch().setValue(Constants.EXAMINE_STATUS_REJECT_PASS);
            }
            examinStatus.getSearch().setRegex(false);
            columns.add(examinStatus);

            Column examinNm = new Column();
            examinNm.setData("examinNm");
            examinNm.setSearchable(true);
            examinNm.setSearch(new Search());
            examinNm.getSearch().setValue(merName);
            examinNm.getSearch().setRegex(false);
            examinNm.setName("");
            examinNm.setOrderable(true);
            columns.add(examinNm);

            Column createTime = new Column();
            createTime.setData("id");
            createTime.setSearchable(true);
            createTime.setSearch(new Search());
            createTime.getSearch().setValue("");
            createTime.getSearch().setRegex(false);
            createTime.setName("");
            createTime.setOrderable(true);
            columns.add(createTime);

            output = dataTableMercRegisterRepository.findAll(input);
            List<MercExtInfoModel> mercExtInfoModels = new ArrayList<MercExtInfoModel>();
            for (MercRegisterSeq mercRegisterSeq : output.getData()) {
                MercExtInfoModel mercExtInfoModel = new MercExtInfoModel();
                mercExtInfoModels.add(mercExtInfoModel);
                mercExtInfoModel.setMercId(mercRegisterSeq.getId()+"");
                mercExtInfoModel.setCreateTime(DateTimeUtils.parseLocalDateTime(mercRegisterSeq.getCreateTime().replaceAll("-","")
                        .replaceAll(":","")
                        .replace(" ","").trim()));
                mercExtInfoModel.setMercNm(mercRegisterSeq.getExaminNm());
                if ("00".equals(mercRegisterSeq.getExaminStatus())) {
                    mercExtInfoModel.setMercSts("2");
                } else {
                    mercExtInfoModel.setMercSts("3");
                }
            }
            dataTablesOutput.setRecordsTotal(output.getRecordsTotal());
            dataTablesOutput.setData(mercExtInfoModels);
            dataTablesOutput.setRecordsFiltered(output.getRecordsFiltered());
            dataTablesOutput.setDraw(input.getDraw());

        } else {
             //审核通过数据
            dataTablesOutput = dataTablesMercExtInfoRepository.findAll(dataTablesInput);
            logger.debug("datatable server-side return {}", dataTablesOutput);
        }

        return dataTablesOutput;
    }

    @Override
    public void updateMercStatus(String userId) {
        GenericRspDTO genericRspDTO = userBasicInfClient.cancelUser(userId);
        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
            throw new LemonException(genericRspDTO.getMsgCd());
        }
    }

    /**
     *  商户注册
     */

    @Override
    public Map<String, String> merRegister(MerRegister merRegister, String status) {
        Map<String ,String > result = new HashMap<String,String>();
        //测试01：对象--json
        String merjson= null; //将对象转换成json
        try {
            ObjectMapper mapper = new ObjectMapper(); //转换器
            merjson = mapper.writeValueAsString(merRegister);
            MercRegisterSeq mercRegisterSeq = new MercRegisterSeq();
            logger.info(merjson);
            mercRegisterSeq.setMercInfo(merjson);
            mercRegisterSeq.setExaminNm(merRegister.getMercName());
            mercRegisterSeq.setExaminStatus(status);
            mercRegisterSeq.setCreateTime(DateTimeUtils.getCurrentDateTimeStr());
            mercRegisterSeq.setModifyTime(DateTimeUtils.getCurrentDateTimeStr());
            mercRegisterSeqRepository.save(mercRegisterSeq);
            result.put("msgCd","1");
        } catch (Exception e) {
            logger.info("异常:{}" ,e.getMessage());
            result.put("msgCd","0");
        }
        return result;
    }

    @Override
    public DataTablesOutput<MercRegisterSeq> merExamine(DataTablesInput input) {
        Specification<MercRegisterSeq> orderQueryParam= Specifications.<MercRegisterSeq>and()
                .in("examinStatus","00","01","03")
                .build();
        return dataTableMercRegisterRepository.findAll(input, orderQueryParam );
    }

    /**
     * 商户信息审核
     * @param mercRegisterSeq
     * @return
     */
    @Override
    public Map<String, String> examine(MercRegisterSeq mercRegisterSeq) {
        Locale locale = LocaleContextHolder.getLocale();
        Map<String, String> result = new HashMap<String, String >();
        String json = "";
        MercRegisterSeq examineSeq = new MercRegisterSeq();
        result.put("msgCd","1");
        try {
            //更新流水表的状态
            examineSeq = mercRegisterSeqRepository.findOne(mercRegisterSeq.getId());
            if (Constants.EXAMINE_STATUS_PASS.equals(examineSeq.getExaminStatus())
                    && (Constants.EXAMINE_STATUS_PASS.equals(mercRegisterSeq.getExaminStatus()))) {
                result.put("msgCd","0");
                return result;
            }
            examineSeq.setModifyTime(DateTimeUtils.getCurrentDateTimeStr());
            examineSeq.setExaminStatus(mercRegisterSeq.getExaminStatus());
            examineSeq.setRemark(mercRegisterSeq.getRemark());
            json = examineSeq.getMercInfo();

            ObjectMapper mapper = new ObjectMapper();


            //如果审核不通过则结束流程，否则调商户开户接口进行开户
            if (!Constants.EXAMINE_STATUS_PASS.equals(mercRegisterSeq.getExaminStatus())) {
                mercRegisterSeqRepository.save(examineSeq);
                return result;
            }
        }catch (Exception e) {
            logger.info("更新流水审表失败:{}",e.getMessage());
            result.put("msgCd","0");
            return result;
        }
            //流水表中的json字符串转对象
        MerRegister merRegister = new MerRegister();
        try {
            ObjectMapper mapper = new ObjectMapper();
            merRegister = mapper.readValue(json, MerRegister.class);
        } catch (Exception e) {
            logger.info("json转换失败:{}" ,e.getMessage());
            String msgInfo = messageSource.getMessage("urm.manager.addsettle.failed", null, locale);
            result.put("msgCd", "0");
            result.put("msgInfo", msgInfo);
            return result;
        }
        //测试用
        //调用urm商户注册
        MerRegisterDTO merRegisterDTO = new MerRegisterDTO();
        BeanUtils.copyProperties(merRegisterDTO, merRegister);
        if (JudgeUtils.isNotBlank(merRegister.getOpnBusDtSr())) {
            merRegisterDTO.setBusLicExpDt(DateTimeUtils.parseLocalDate(merRegister.getOpnBusDtSr().replaceAll("-", "").trim()));
        }
        merRegisterDTO.setDisplayNm(merRegister.getDiplayNm());
        GenericDTO<MerRegisterDTO> genericDTO = GenericDTO.newInstance(merRegisterDTO);
        //调用用户中心的商户开户接口开户
        GenericRspDTO<String> merRegisterRsq = userBasicInfClient.openMerUser(genericDTO);
        if ("URM00000".equals(merRegisterRsq.getMsgCd())) {
            if (JudgeUtils.isNotBlank(merRegisterRsq.getBody())) {
                //开户成功更新状态
                merRegister.setUserId(merRegisterRsq.getBody());
                mercRegisterSeqRepository.save(examineSeq);
            } else {
                //商户号为空
                result.put("msgCd", merRegisterRsq.getMsgCd());
                result.put("msgInfo", "return userid is null");
                return result;
            }
        } else {
            result.put("msgCd", merRegisterRsq.getMsgCd());
            result.put("msgInfo", merRegisterRsq.getMsgInfo());
            return result;
        }

        //登记商户结算信息
//        try {
//            SettleBaseDO settleBaseDO = new SettleBaseDO();
//            copyToSettleBaseDO(merRegister, settleBaseDO);
//            settleBaseDORespository.save(settleBaseDO);
//            if ("001".equals(settleBaseDO.getSettleSite())) {
//                SettleCardDO settleCardDO = new SettleCardDO();
//                copyToSettleCardDO(merRegister, settleCardDO);
//                //加密卡号
//                if (JudgeUtils.isNotBlank(settleCardDO.getCapCardNo())) {
//                    String cardNo = commonDecryptAndEncrypt(settleCardDO.getCapCardNo(), "encrypt");
//                    if (JudgeUtils.isBlank(cardNo)) {
//                        String msgInfo = messageSource.getMessage("urm.manager.addsettle.failed", null, locale);
//                        result.put("msgCd", "0");
//                        result.put("msgInfo", msgInfo);
//                        return result;
//                    }
//                    settleCardDO.setCapCardNo(cardNo);
//                }
//                settleCardRespository.save(settleCardDO);
//            }
//        } catch (Exception e) {
//            logger.info("异常:{}" ,e.getMessage());
//            result.put("msgCd", "0");
//            String msgInfo = messageSource.getMessage("urm.manager.addsettle.failed", null, locale);
//            result.put("msgInfo", msgInfo);
//            return result;
//        }
//        //登记商户费率
//        try {
//            List<MerchantRateRuleDO> merchantRateRuleDOList = new ArrayList<MerchantRateRuleDO>();
//            String busType = "";
//            //根据商户结算信息判断结算提现的业务类型
//            /**
//             * 0403 自主结算
//             0404 自动结算
//             0405 营业厅提现
//             */
//            if ("001".equals(merRegister.getSettleSite())) {
//                if ("auto".equals(merRegister.getSettleType())) {
//                    busType = "0404";
//                }
//
//                if ("self".equals(merRegister.getSettleType())) {
//                    busType = "0403";
//                }
//            } else {
//                busType = "0405";
//            }
//            copyTomerchantRateRuleDO(merRegister, merchantRateRuleDOList, busType);
//            for (MerchantRateRuleDO merchantRateRuleDO : merchantRateRuleDOList) {
//                merchantRateRuleRespository.save(merchantRateRuleDO);
//            }
//        } catch (Exception e) {
//            logger.info("异常:{}",e.getMessage());
//            result.put("msgCd", "0");
//            String msgInfo = messageSource.getMessage("urm.manager.addrate.failed", null, locale);
//            result.put("msgInfo", msgInfo);
//            return result;
//        }

        //登记商户信息资源表
        try {
            CprResInf cprResInf = new CprResInf();
            BeanUtils.copyProperties(cprResInf, merRegister);
            cprResInfRespository.save(cprResInf);
        } catch (Exception e) {
            logger.info("异常" +e.getMessage());
            result.put("msgCd", "0");
            String msgInfo = messageSource.getMessage("urm.manager.addres.failed", null, locale);
            result.put("msgInfo", msgInfo);
            return result;
        }
//        try {
//            if ("open".equals(merRegister.getOpenWechatMerflag())) {
//               this.openMerSub(merRegister);
//            }
//        }catch (Exception e) {
//            logger.info("开通微信商户异常:{}",e.getMessage());
//            result.put("msgInfo","开户成功，开通微信子商户失败");
//        }
        return result;
    }

    //查询修改参数
    @Override
    public MerRegister modifyRegister(String mercId, String mercSts) {
        MerRegister merRegister = new MerRegister();
        List<RateInfo> rateInfoList = new ArrayList<RateInfo>() ;
        merRegister.setRateInfolist(rateInfoList);
        //如果状态为审核中或者审核拒绝则查流水表,否则则商户查信息表
        try{
            if ("2".equals(mercSts) || "3".equals(mercSts)) {
                MercRegisterSeq examineSeq = mercRegisterSeqRepository.findOne(Integer.valueOf(mercId));
                ObjectMapper mapper = new ObjectMapper();
                merRegister = mapper.readValue( examineSeq.getMercInfo() , MerRegister.class );
                merRegister.setRefuseReson(examineSeq.getRemark());
            } else {
                UrmCprExtInfDO urmCprExtInfDO = urmCprExtInfRespository.findOne(mercId);
                if (urmCprExtInfDO != null) {
                    BeanUtils.copyProperties(merRegister, urmCprExtInfDO);
                    if (urmCprExtInfDO.getBusLicExpDt() != null ) {

                        merRegister.setOpnBusDtSr(DateTimeUtils.formatLocalDate(urmCprExtInfDO.getBusLicExpDt(),DATE_FORMAT));
                    }
                    SafeInfoDO safeInfoDO = safeInfoRespository.byMercIdAndOprTyp(mercId ,"2");
                    if(safeInfoDO != null) {
                        merRegister.setEmail(safeInfoDO.getEmail());
                        merRegister.setMblNo(safeInfoDO.getMblNo());
                        SafeLoginDO safeLoginDO = safeLoginRespository.findOneBySafeId(safeInfoDO.getSafeId());
                        if (safeLoginDO != null) {
                            merRegister.setLoginId(safeLoginDO.getLoginId());
                            merRegister.setDiplayNm(safeLoginDO.getDiplayNm());
                        }
                    }
                    CpiSubMercCastDO cpiSubMercCastDO = cpiSubMercCastRepository.findOne(mercId) ;
                    if ( cpiSubMercCastDO != null && "1".equals(cpiSubMercCastDO.getEffFlg()) ) {
                        merRegister.setOpenWechatMerflag("open");
                    } else {
                        merRegister.setOpenWechatMerflag("no");
                    }
                } else {
                    return merRegister;
                }

                SettleBaseDO settleBaseDO = settleBaseDORespository.findOne(mercId);
                if (settleBaseDO != null) {
                    BeanUtils.copyProperties( merRegister ,settleBaseDO );
                    if (settleBaseDO.getEffDate() != null) {
                        merRegister.setSettleEffDate(DateTimeUtils.formatLocalDate(settleBaseDO.getEffDate(),DATE_FORMAT));
                    }
                    if (settleBaseDO.getExpDate() != null) {
                        merRegister.setSettleExpDate(DateTimeUtils.formatLocalDate(settleBaseDO.getExpDate(),DATE_FORMAT));
                    }
                }
                SettleCardDO settleCardDO = settleCardRespository.findOneByUserID(mercId);
                if (settleCardDO != null) {

                    settleCardDO.setCapCardNo(commonDecryptAndEncrypt(settleCardDO.getCapCardNo(),"decrypt"));
                    BeanUtils.copyProperties(merRegister, settleCardDO);
                }
                List<MerchantRateRuleDO> merchantRateRuleDOList = merchantRateRuleRespository.findAllByMercIdOderyByBusType(mercId);
                boolean cashFlg = false ;
                boolean consume = false ;
                for (MerchantRateRuleDO merchantRateRuleDO : merchantRateRuleDOList ) {
                    RateInfo info = new RateInfo();
                    copyToRateInfo(info ,merchantRateRuleDO);
                    if (("0201" .equals(merchantRateRuleDO.getBusType()) || "0202" .equals(merchantRateRuleDO.getBusType()) || "0203" .equals(merchantRateRuleDO.getBusType())
                            || "0801" .equals(merchantRateRuleDO.getBusType()) || "0802" .equals(merchantRateRuleDO.getBusType())) &&  "Seatelpay".equals(merchantRateRuleDO.getChannel())) {
                        if (consume) {
                            continue;
                        }
                        info.setBusType("1010");
                        consume = true ;
                    }

                    if (("0403".equals(merchantRateRuleDO.getBusType()) || "0404".equals(merchantRateRuleDO.getBusType()) || "0405".equals(merchantRateRuleDO.getBusType()))
                            &&  "Seatelpay".equals(merchantRateRuleDO.getChannel())) {
                        info.setBusType("0403");
                        if (cashFlg) {
                            continue;
                        }
                        cashFlg = true ;
                    }
                    rateInfoList.add(info);
                }

                //查询商户图片
                CprResInf cprResInf = cprResInfRespository.findOne(mercId);
                if (cprResInf != null ){
                    BeanUtils.copyProperties(merRegister,cprResInf);
                }
            }
        } catch (Exception e){
            logger.info("异常:{}",e.getMessage());
        }
        return merRegister;
    }

    private String commonDecryptAndEncrypt(String capCardNo ,String type) {
        CommonEncryptReqDTO commonEncryptReqDTO = new CommonEncryptReqDTO();
        commonEncryptReqDTO.setData(capCardNo);
        commonEncryptReqDTO.setType(type);
        GenericDTO<CommonEncryptReqDTO> genericDTO = GenericDTO.newInstance(commonEncryptReqDTO);
        GenericRspDTO<CommonEncryptRspDTO> genericRspDTO = null;
        try {
           genericRspDTO = cmmServerClient.encrypt(genericDTO);
           if ( genericRspDTO.getBody() !=null && JudgeUtils.isNotBlank(genericRspDTO.getBody().getData())) {
               if(JudgeUtils.isSuccess(genericRspDTO.getMsgCd())){
                   return genericRspDTO.getBody().getData();
               }
           }
        }catch (Exception e) {
            logger.debug("卡号加解密异常");
            logger.info("异常:{}",e.getMessage());

        }
        return "";
    }

    /**
     * 修改商户信息
     * @param merRegister
     * @return
     */
    @Override
    public Map<String, String> modify(MerRegister merRegister) {
        Locale locale = LocaleContextHolder.getLocale();
        Map<String ,String > result = new HashMap<String , String>();
        result.put("msgCd","1");
        //如果状态为审核中或者审核拒绝则修改流水表,否则则商户查信息表
        if (!"2".equals(merRegister.getMercSts()) && !"3".equals(merRegister.getMercSts())) {
            //修改商户信息
            try {
                UrmCprExtInfDO urmCprExtInfDO = new UrmCprExtInfDO();
                BeanUtils.copyProperties(urmCprExtInfDO, merRegister);
                if (JudgeUtils.isNotBlank(merRegister.getOpnBusDtSr())) {
                    urmCprExtInfDO.setBusLicExpDt(DateTimeUtils.parseLocalDate(merRegister.getOpnBusDtSr().replaceAll("-","").trim()));
                }
                UrmCprExtInfDO updateUrmCprExtInfDO = urmCprExtInfRespository.findOne(merRegister.getUserId());
                if (urmCprExtInfDO != null) {
                    BeanUtils.copyProperties(updateUrmCprExtInfDO, urmCprExtInfDO);
                    updateUrmCprExtInfDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                    urmCprExtInfRespository.save(updateUrmCprExtInfDO);

                    SafeInfoDO safeInfoDO = safeInfoRespository.byMercIdAndOprTyp(merRegister.getUserId(),"2");
                    if(safeInfoDO != null) {
                        safeInfoDO.setEmail(merRegister.getEmail());
                        safeInfoDO.setMblNo(merRegister.getMblNo());
                        safeInfoRespository.save(safeInfoDO);
                        SafeLoginDO safeLoginDO = safeLoginRespository.findOneBySafeId(safeInfoDO.getSafeId());
                        if (safeLoginDO !=null) {
                            safeLoginDO.setSafeId(safeInfoDO.getSafeId());
                            safeLoginDO.setLoginId(merRegister.getLoginId());
                            safeLoginDO.setDiplayNm(merRegister.getDiplayNm());
                            safeLoginRespository.updateBySafeLoginDO(safeLoginDO.getLoginId(), safeLoginDO.getDiplayNm(), safeLoginDO.getSafeId());
                        }
                    }
                }else {
                    result.put("msgCd","0");
                    String  msgInfo= messageSource.getMessage("urm.manager.noUser", null, locale);
                    result.put("msgInfo", msgInfo);
                }
            }catch (Exception e){
                logger.info("异常:{}",e.getMessage());
                result.put("msgCd","0");
                String  msgInfo= messageSource.getMessage("urm.manager.update.failed", null, locale);
                result.put("msgInfo", msgInfo);
                return result;
            }
            try {
                SettleBaseDO settleBaseDO = new SettleBaseDO();
                SettleCardDO settleCardDO = new SettleCardDO();
                copyToSettleBaseDO(merRegister, settleBaseDO);
                copyToSettleCardDO(merRegister, settleCardDO);
                //加密卡号
                if (JudgeUtils.isNotBlank(settleCardDO.getCapCardNo())) {
                    String cardNo = commonDecryptAndEncrypt(settleCardDO.getCapCardNo(), "encrypt");
                    if (JudgeUtils.isBlank(cardNo)) {
                        result.put("msgCd", "0");
                        String  msgInfo= messageSource.getMessage("urm.manager.card.enscrypt", null, locale);
                        result.put("msgInfo", msgInfo);
                        return result;
                    }
                    settleCardDO.setCapCardNo(cardNo);
                }
                //settleCardDO.setCapCardNo( EncryptionUtils.encrypt(settleCardDO.getCapCardNo()));
                SettleBaseDO updateSettleBaseDO = settleBaseDORespository.findOne(merRegister.getUserId());

                if (updateSettleBaseDO != null) {
                    //如果为自动结算则需要初始化下一个结算日期
                    LocalDate nextSettleDate = updateSettleBaseDO.getNextSettleDay();
                    BeanUtils.copyProperties(updateSettleBaseDO, settleBaseDO);
                    if ("auto".equals(merRegister.getSettleType()) && nextSettleDate != null ) {
                        updateSettleBaseDO.setNextSettleDay(nextSettleDate);
                    }
                    settleBaseDORespository.save(updateSettleBaseDO);
                } else {
                    settleBaseDORespository.save(settleBaseDO);
                }
                //如果结算地点为银行则需要添加银行卡信息,否则不需要.如果修改前为银行，修改后为网点。则删除银行卡信息
                if ("001".equals(settleBaseDO.getSettleSite())) {
                    SettleCardDO updateSettleCardDO = settleCardRespository.findOneByUserID(merRegister.getUserId());
                    if (updateSettleCardDO != null) {
                        if (!updateSettleCardDO.getCapCardNo().equals(settleCardDO.getCapCardNo())){
                            settleCardRespository.delete(updateSettleCardDO);
                        }
                        BeanUtils.copyProperties(updateSettleCardDO, settleCardDO);
                        settleCardRespository.save(updateSettleCardDO);
                    } else {
                        settleCardRespository.save(settleCardDO);
                    }
                } else {
                    SettleCardDO updateSettleCardDO = settleCardRespository.findOneByUserID(merRegister.getUserId());
                    if (updateSettleCardDO != null) {
                        settleCardRespository.delete(updateSettleCardDO);
                    }
                }
            } catch (Exception e) {
                logger.info("异常:{}",e.getMessage());
                result.put("msgCd", "0");
                String  msgInfo= messageSource.getMessage("urm.manager.update.settle.failed", null, locale);
                result.put("msgInfo", msgInfo);
                return result;
            }
            //登记商户费率
            try {
                List<MerchantRateRuleDO> merchantRateRuleDOList = new ArrayList<MerchantRateRuleDO>();
                List<MerchantRateRuleDO> updateMerchantRateList = merchantRateRuleRespository.findAllByMercId(merRegister.getUserId());
                String busType = "" ;
                //根据商户结算信息判断结算提现的业务类型
                /**
                 * 0403 自主结算
                 0404 自动结算
                 0405 营业厅提现
                 */
                if ("001".equals(merRegister.getSettleSite())) {
                    if ("auto".equals(merRegister.getSettleType())) {
                        busType = "0404" ;
                    }

                    if ("self".equals(merRegister.getSettleType())) {
                        busType = "0403" ;
                    }
                } else {
                    busType = "0405" ;
                }
                copyTomerchantRateRuleDO(merRegister, merchantRateRuleDOList , busType);
                for (MerchantRateRuleDO merchantRateRuleDO : merchantRateRuleDOList) {
                    MerchantRateRulePK pk = new MerchantRateRulePK();
                    pk.setBusType(merchantRateRuleDO.getBusType());
                    pk.setUserId(merchantRateRuleDO.getUserId());
                    pk.setChannel(merchantRateRuleDO.getChannel());
                    MerchantRateRuleDO updateMerchantRateRuleDO = merchantRateRuleRespository.findOne(pk);
                    if (updateMerchantRateRuleDO != null) {
                        BeanUtils.copyProperties(updateMerchantRateRuleDO, merchantRateRuleDO);
                        merchantRateRuleRespository.save(updateMerchantRateRuleDO);
                    } else {
                        merchantRateRuleRespository.save(merchantRateRuleDO);
                    }
                }
                //遍历查询修改后的利率里面含有表中的数据，如果没有则删除表的数据
                for (MerchantRateRuleDO update : updateMerchantRateList) {
                    boolean flag = true;
                    for (MerchantRateRuleDO ruleDO : merchantRateRuleDOList) {
                        if (update.getUserId().equals(ruleDO.getUserId()) && update.getBusType().equals(ruleDO.getBusType()) && update.getChannel().equals(ruleDO.getChannel())) {
                            flag = false;
                            break;
                        }
                    }
                    if (flag) {
                        merchantRateRuleRespository.delete(update);
                    }
                }
            } catch (Exception e) {
                logger.info("异常:{}",e.getMessage());
                e.printStackTrace();
                result.put("msgCd", "0");
                String  msgInfo= messageSource.getMessage("urm.manager.update.rate.failed", null, locale);
                result.put("msgInfo", msgInfo);
                return result;
            }

            //修改商户信息资源表
            try {
                CprResInf cprResInf = new CprResInf();
                BeanUtils.copyProperties( cprResInf , merRegister);
                CprResInf upCprResInf1 = cprResInfRespository.findOne(merRegister.getUserId());
                if (upCprResInf1 != null) {
                    BeanUtils.copyProperties(upCprResInf1, cprResInf);
                    cprResInfRespository.save(cprResInf);
                } else {
                    cprResInfRespository.save(cprResInf);
                }
                //判断商户是否开通微信子商户，如果开通标志为是则调用cpi开通微信子商户
            } catch (Exception e) {
                logger.info("异常:{}" ,e.getMessage());
                result.put("msgCd" ,"0");
                String  msgInfo= messageSource.getMessage("urm.manager.update.settle.failed", null, locale);
                result.put("msgInfo", msgInfo);
            }
            try{
                if ("open".equals(merRegister.getOpenWechatMerflag())) {
                    //检查是否已经开通
                    CpiSubMercCastDO cpiSubMercCastDO =  cpiSubMercCastRepository.findOne(merRegister.getUserId()) ;
                    //如果子商户的记录为null或者已经失效则调用cpi接口开通子商户
                    if ( cpiSubMercCastDO == null || "0".equals(cpiSubMercCastDO.getEffFlg()) ) {
                        this.openMerSub(merRegister);
                    }
                }
            }catch (Exception e){
                result.put("msgInfo","商户信息修改成功，开通微信子商户失败");
            }
        } else {
            //修改审批流水表
            try {
                MercRegisterSeq examineSeq ;
                String json = "";
                ObjectMapper mapper = new ObjectMapper(); //转换器
                json = mapper.writeValueAsString(merRegister);
                examineSeq = mercRegisterSeqRepository.findOne(Integer.valueOf(merRegister.getUserId()));
                if (examineSeq == null) {
                    result.put("msgCd" ,"0");
                    String  msgInfo= messageSource.getMessage("urm.manager.noUser", null, locale);
                    result.put("msgInfo" , msgInfo);
                    return result;
                }
                examineSeq.setModifyTime(DateTimeUtils.getCurrentDateTimeStr());
                examineSeq.setMercInfo(json);
                examineSeq.setExaminStatus(Constants.EXAMINE_STATUS_NO_PASS);
                mercRegisterSeqRepository.save(examineSeq);
            } catch (Exception e){
                result.put("msgCd" ,"0");
                String  msgInfo= messageSource.getMessage("urm.manager.update.seq.failed", null, locale);
                result.put("msgInfo" ,msgInfo);
                logger.info("异常:{}" ,e.getMessage());
            }
        }
        return result;
    }

    @Override
    public Map<String, String> modifyByExamine(MercRegisterSeq mercRegisterSeq) {
        Locale locale = LocaleContextHolder.getLocale();
        Map<String, String> result = new HashMap<String, String >();
        String json = "";
        MercRegisterSeq examineSeq = new MercRegisterSeq();
        result.put("msgCd","1");
        try {
            examineSeq = mercRegisterSeqRepository.findOne(mercRegisterSeq.getId());
            if (Constants.EXAMINE_STATUS_PASS.equals(examineSeq.getExaminStatus())
                    && (Constants.EXAMINE_STATUS_PASS.equals(mercRegisterSeq.getExaminStatus()))) {
                result.put("msgCd","0");
                return result;
            }
            examineSeq.setExaminStatus(mercRegisterSeq.getExaminStatus());
            examineSeq.setModifyTime(DateTimeUtils.getCurrentDateTimeStr());
            examineSeq.setRemark(mercRegisterSeq.getRemark());
            json = examineSeq.getMercInfo();
            ObjectMapper mapper = new ObjectMapper();
            if (!Constants.EXAMINE_STATUS_PASS.equals(mercRegisterSeq.getExaminStatus())) {
                mercRegisterSeqRepository.save(examineSeq);
                return result;
            }
        }catch (Exception e) {
            logger.info("更新流水审表失败:{}",e.getMessage());
            result.put("msgCd","0");
            return result;
        }
        //流水表中的json字符串转对象
        MerRegister merRegister = new MerRegister();
        try {
            ObjectMapper mapper = new ObjectMapper();
            merRegister = mapper.readValue(json, MerRegister.class);
        } catch (Exception e) {
            logger.info("json转换失败:{}" ,e.getMessage());
            String msgInfo = messageSource.getMessage("urm.manager.addsettle.failed", null, locale);
            result.put("msgInfo", msgInfo);
            result.put("msgCd", "0");
            return result;
        }
        Map<String, String> r = this.modify(merRegister);
        if (JudgeUtils.equals(r.get("msgCd"), "0")) {
            return r;
        }
        examineSeq.setExaminStatus(Constants.EXAMINE_STATUS_PASS);
        mercRegisterSeqRepository.save(examineSeq);
        return r;
    }

    @Override
    public Map<String, String> checkMngAcc(String mngAcc, String merId) {
        Map<String,String> map = new HashMap<String,String >();
        map.put("result" ,"0");
        if (JudgeUtils.isBlank(merId)) {
            SafeLoginDO safeLoginDO = safeLoginRespository.findOne(mngAcc);
            if (safeLoginDO == null) {
                map.put("result" ,"1");
            }
        } else {
            SafeLoginDO safeLoginDO = safeLoginRespository.findOne(mngAcc);
            if (safeLoginDO != null) {
                SafeInfoDO safeInfoDO = safeInfoRespository.byMercIdAndOprTyp(merId , "2");
                if (safeInfoDO != null && safeInfoDO.getSafeId().equals(safeLoginDO.getSafeId())) {
                    map.put("result" ,"1");
                }
            } else {
                map.put("result" ,"1");
            }
        }
        return map;
    }

    /**
     * 检查手机号是否合法
     * @param mobile
     * @return
     */
    @Override
    public Map<String, String> checkMobile(String mobile) {
        Map<String ,String > result = new HashMap<String ,String >();
        SegementCheckReqDTO body = new SegementCheckReqDTO();
        body.setMblNo(mobile);
        GenericDTO<SegementCheckReqDTO> genericDTO = GenericDTO.newInstance(body);
//        if (!PhoneNumberUtils.isValidNumber(mobile) ) {
//            result.put("result", "0");
//            return  result;
//        }
        try {
            result.put("result", "1");
//            GenericRspDTO<SegementCheckRspDTO> genericRspDTO = cmmServerClient.segementCheck(genericDTO);
//            if ("CMM00000".equals(genericRspDTO.getMsgCd())) {
//                result.put("result", "1");
//            } else {
//                result.put("result", "0");
//            }
        } catch (Exception e) {
            logger.info("异常:{}" ,e.getMessage());
            result.put("result", "0");
        }
        return result;
    }

    /**
     * 查询商户级别
     * @return
     */
    public Map<String, List<ConstantParamRspDTO>> getMerLevel() {
        Map<String, List<ConstantParamRspDTO>> map = new HashMap<String, List<ConstantParamRspDTO>>();
        try {
            GenericRspDTO<List<ConstantParamRspDTO>> genericRspDTO = constantParamClient.paramsGroup("MER_LEVEL");
            List<ConstantParamRspDTO> list = genericRspDTO.getBody();
            map.put("list", list);
        }catch (Exception e) {
            logger.info("查询商户级别异常:{}" ,e.getMessage());
        }
        return map;
    }


    //查询商户账户余额
    @Override
    public DataTablesOutput<UrmMercBalanceDO> queryUserBalance(MartketFindInput input) {
        DataTablesOutput<UrmMercBalanceDO> output = new DataTablesOutput<UrmMercBalanceDO>() ;
        input.setDraw(input.getDraw()+1);
        UserBaseInfo userBaseInfoslist = new UserBaseInfo();
        String userId = input.getExtra_search().get("userId");
        String mobile = input.getExtra_search().get("mobile");
        if(JudgeUtils.isBlank(userId) && JudgeUtils.isBlank(mobile)) {
            return output ;
        }
        DataTablesInput tablesInput = new DataTablesInput();
        tablesInput.addColumn("userId",true ,false ,"");
        tablesInput.addColumn("mblNo",true ,false ,"");
        tablesInput.setDraw(input.getDraw());
        tablesInput.setOrder(input.getOrder());
        tablesInput.setLength(input.getLength());
        Search search = new Search();
        search.setValue("");
        search.setRegex(false);
        tablesInput.setSearch(search);
        Specification<UserBasicInfo> orderQueryParam= Specifications.<UserBasicInfo>and()
                .eq(com.hisun.lemon.common.utils.JudgeUtils.isNotBlank(userId), "userId", userId)
                .like(com.hisun.lemon.common.utils.JudgeUtils.isNotBlank(mobile), "mblNo", '%'+mobile+'%')
                .in("usrLvl","3","4")
                .build();
        DataTablesOutput<UserBasicInfo> out = datatablesMercInfoRepository.findAll(tablesInput, orderQueryParam);
        if (out.getData().size() == 0 ) {
            return output;
        } else {
            List<UrmMercBalanceDO> list = new ArrayList<UrmMercBalanceDO>();
            List<UserBasicInfo> userBasicInfos =  out.getData() ;
            for (UserBasicInfo u : userBasicInfos) {
                UrmMercBalanceDO urmMercBalanceDO = new UrmMercBalanceDO();

                userId = u.getUserId();
                mobile = u.getMblNo();
                UrmCprExtInfDO urmCprExtInfDO = urmCprExtInfRespository.findOne(userId) ;
                if (urmCprExtInfDO != null ) {
                    urmMercBalanceDO.setUserNm(urmCprExtInfDO.getMercName());
                }
                urmMercBalanceDO.setUserId(userId);
                urmMercBalanceDO.setMobile(mobile);
                list.add(urmMercBalanceDO) ;
                //账户余额

                List<AcmAcBal> listac = acmAcBalRepository.findAllByMerId(userId);
                for (AcmAcBal acmAcBal : listac) {
                    if ("1".equals(acmAcBal.getCapTyp())) {
                        urmMercBalanceDO.setCashAcCurBal(acmAcBal.getAcCurBal());
                        urmMercBalanceDO.setCashAcUavaBal(acmAcBal.getAcUavaBal());
                        urmMercBalanceDO.setCashAcc(acmAcBal.getAC_NO());
                    }
                    if ("8".equals(acmAcBal.getCapTyp())) {
                        urmMercBalanceDO.setSettleAcCurBal(acmAcBal.getAcCurBal());
                        urmMercBalanceDO.setSettleAcUavaBal(acmAcBal.getAcUavaBal());
                        urmMercBalanceDO.setSettleAcc(acmAcBal.getAC_NO());
                    }
                }
            }
            output.setData(list);
            output.setRecordsFiltered(out.getRecordsFiltered());
            output.setRecordsTotal(out.getRecordsTotal());
            output.setDraw(out.getDraw());
        }
        return output;
    }

    @Override
    public Map<String, String> checkComercReg(String comercRwg, String merId) {
        Map<String,String> map = new HashMap<String,String >();
        map.put("result" ,"0");
        Integer i  = mercItfInfoDao.checkComercReg(comercRwg , merId);

        if (i>0) {
            return map;
        } else {
            map.put("result" ,"1");
        }
        return map;
    }

    @Override
    public Map<String, String> checkMercShortName(String mercShortName, String merId) {
        Map<String,String> map = new HashMap<String,String >();
        map.put("result" ,"0");
        Integer i  = mercItfInfoDao.checkMercShortName(mercShortName , merId);

        if (i>0) {
            return map;
        } else {
            map.put("result" ,"1");
        }
        return map;
    }

    //赋值到rateinfo
    private void copyToRateInfo(RateInfo info, MerchantRateRuleDO merchantRateRuleDO ) {
        BeanUtils.copyProperties(info, merchantRateRuleDO);
        if (merchantRateRuleDO.getEffDateL() != null){
            info.setEffDate(DateTimeUtils.formatLocalDate(merchantRateRuleDO.getEffDateL() ,DATE_FORMAT));
        }
        if (merchantRateRuleDO.getExpDateL() != null) {
            info.setExpDate(DateTimeUtils.formatLocalDate(merchantRateRuleDO.getExpDateL() ,DATE_FORMAT));
        }

        if (merchantRateRuleDO.getRateb() != null && "percent".equals(merchantRateRuleDO.getCalculateType())) {
            info.setRate(String.valueOf(merchantRateRuleDO.getRateb().multiply(BigDecimal.valueOf(100))));
        }

        if (merchantRateRuleDO.getFixFeeb() != null && "fixed".equals(merchantRateRuleDO.getCalculateType())) {
            info.setRate(String.valueOf(merchantRateRuleDO.getFixFeeb()));
        }
        if (merchantRateRuleDO.getMaxFeeb() != null) {
            info.setMaxFee(String.valueOf(merchantRateRuleDO.getMaxFeeb()));
        }
        if (merchantRateRuleDO.getMinFeeb() != null) {
            info.setMinFee(String.valueOf(merchantRateRuleDO.getMinFeeb()));

        }
        if (merchantRateRuleDO.getBeginCalFeeB() != null) {
            info.setBeginCalFee(String.valueOf(merchantRateRuleDO.getBeginCalFeeB()));

        }
    }

    public void copyTomerchantRateRuleDO(MerRegister merRegister, List<MerchantRateRuleDO> merchantRateRuleDOList,String busType) {
        Locale locale = LocaleContextHolder.getLocale();
        for (RateInfo rateInfo:merRegister.getRateInfolist()) {
            MerchantRateRuleDO merchantRateRuleDO = new MerchantRateRuleDO();
            BeanUtils.copyProperties(merchantRateRuleDO, rateInfo);
            //结算信息
            if ("0403" .equals(merchantRateRuleDO.getBusType())) {
                merchantRateRuleDO.setBusType(busType);
            }
            merchantRateRuleDO.setUserId(merRegister.getUserId());
            merchantRateRuleDO.setUserName(merRegister.getMercName());
            merchantRateRuleDO.setOprId(getOpr.getOperatorName());
            merchantRateRuleDO.setTmSmp(DateTimeUtils.getCurrentLocalDateTime());
            merchantRateRuleDO.setCcy("USD");
            merchantRateRuleDO.setStats("1");
            merchantRateRuleDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
            merchantRateRuleDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
            if (JudgeUtils.isNotBlank(rateInfo.getEffDate().trim())){
                merchantRateRuleDO.setEffDateL(DateTimeUtils.parseLocalDate(rateInfo.getEffDate().replaceAll("-","").trim()));
            }
            if (JudgeUtils.isNotBlank(rateInfo.getExpDate().trim())) {
                merchantRateRuleDO.setExpDateL(DateTimeUtils.parseLocalDate(rateInfo.getExpDate().replaceAll("-","").trim()));
            }

            if (JudgeUtils.isNotBlank(rateInfo.getRate().trim())) {
                if ("percent".equals(rateInfo.getCalculateType())) {
                    merchantRateRuleDO.setRateb(new BigDecimal(rateInfo.getRate()).divide(BigDecimal.valueOf(100),4,BigDecimal.ROUND_HALF_DOWN));
                    merchantRateRuleDO.setFixFeeb(BigDecimal.valueOf(0));
                }
                if ("fixed".equals(rateInfo.getCalculateType())) {
                    merchantRateRuleDO.setFixFeeb(new BigDecimal(rateInfo.getRate()));
                    merchantRateRuleDO.setRateb(BigDecimal.valueOf(0));
                }
            }
            if (JudgeUtils.isNotBlank(rateInfo.getMaxFee().trim())) {
                merchantRateRuleDO.setMaxFeeb(new BigDecimal(rateInfo.getMaxFee()));
            } else {
                merchantRateRuleDO.setMaxFeeb(new BigDecimal("9999999999999.99"));
            }
            if (JudgeUtils.isNotBlank(rateInfo.getMinFee().trim())) {
                merchantRateRuleDO.setMinFeeb(new BigDecimal(rateInfo.getMinFee()));
            } else {
                merchantRateRuleDO.setMinFeeb(new BigDecimal("0"));
            }
            if (JudgeUtils.isNotBlank(rateInfo.getBeginCalFee().trim())) {
                merchantRateRuleDO.setBeginCalFeeB(new BigDecimal(rateInfo.getBeginCalFee()));

            } else {
                merchantRateRuleDO.setBeginCalFeeB(new BigDecimal("0"));
            }

            //如果为消费支付的费率1010，则需要映射三条利率
            /**
             * 0201 条码支付
             * 0202 扫码付支付
             0801 充流量
             0802 充话费
             */
            if ("1010" .equals(merchantRateRuleDO.getBusType())) {


                MerchantRateRuleDO merchantRateRuleOrder = new MerchantRateRuleDO() ;
                BeanUtils.copyProperties(merchantRateRuleOrder , merchantRateRuleDO);
                merchantRateRuleOrder.setBusType("0201");
                //"收单消费"
                String pay = messageSource.getMessage("settle.busType.pay", null, locale);
                merchantRateRuleOrder.setBusTypeDesc(pay);
                merchantRateRuleDOList.add(merchantRateRuleOrder);

                MerchantRateRuleDO merchantRateRuleQrComsume = new MerchantRateRuleDO() ;
                BeanUtils.copyProperties(merchantRateRuleQrComsume , merchantRateRuleDO);
                merchantRateRuleQrComsume.setBusType("0202");
                //"扫码付消费"
                String qrConsume = messageSource.getMessage("settle.busType.qrConsume", null, locale);
                merchantRateRuleQrComsume.setBusTypeDesc(qrConsume);
                merchantRateRuleDOList.add(merchantRateRuleQrComsume);

                MerchantRateRuleDO merchantRateRuleFlow = new MerchantRateRuleDO() ;
                BeanUtils.copyProperties(merchantRateRuleFlow , merchantRateRuleDO);
                merchantRateRuleFlow.setBusType("0801");
                //"充流量"
                String flow = messageSource.getMessage("settle.busType.flow", null, locale);
                merchantRateRuleFlow.setBusTypeDesc(flow);
                merchantRateRuleDOList.add(merchantRateRuleFlow);

                MerchantRateRuleDO merchantRateRuleTelFare = new MerchantRateRuleDO() ;
                BeanUtils.copyProperties(merchantRateRuleTelFare , merchantRateRuleDO);
                merchantRateRuleTelFare.setBusType("0802");
                //充话费
                String telFee = messageSource.getMessage("settle.busType.telFee", null, locale);
                merchantRateRuleTelFare.setBusTypeDesc(telFee);
                merchantRateRuleDOList.add(merchantRateRuleTelFare);

            } else  {
                if ("0403".equals(merchantRateRuleDO.getBusType())) {
                    //"自主结算"
                    String self = messageSource.getMessage("settle.busType.self", null, locale);
                    merchantRateRuleDO.setBusTypeDesc(self);
                }
                if ("0404".equals(merchantRateRuleDO.getBusType())) {
                    //"自动结算"
                    String auto = messageSource.getMessage("settle.busType.auto", null, locale);
                    merchantRateRuleDO.setBusTypeDesc(auto);
                }
                if ("0405".equals(merchantRateRuleDO.getBusType())) {
                    //"营业厅提现"
                    String draw = messageSource.getMessage("settle.busType.draw", null, locale);
                    merchantRateRuleDO.setBusTypeDesc(draw);
                }
                if ("0201".equals(merchantRateRuleDO.getBusType())) {
                    //"条码支付
                    String draw = messageSource.getMessage("settle.busType.pay", null, locale);
                    merchantRateRuleDO.setBusTypeDesc(draw);
                }
                if ("0202".equals(merchantRateRuleDO.getBusType())) {
                    //扫码支付
                    String draw = messageSource.getMessage("settle.busType.qrConsume", null, locale);
                    merchantRateRuleDO.setBusTypeDesc(draw);
                }
                merchantRateRuleDOList.add(merchantRateRuleDO);
            }

        }

    }

    //赋值结算基础信息
    public void copyToSettleBaseDO (MerRegister merRegister , SettleBaseDO settleBaseDO ) {
        BeanUtils.copyProperties(settleBaseDO , merRegister);
        settleBaseDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        settleBaseDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        settleBaseDO.setUserName(merRegister.getMercName());
        settleBaseDO.setTmSmp(DateTimeUtils.getCurrentLocalDateTime());
        if (JudgeUtils.isNotBlank(merRegister.getSettleEffDate())) {
            settleBaseDO.setEffDate(DateTimeUtils.parseLocalDate(merRegister.getSettleEffDate().replaceAll("-","").trim()));
        }
        if (JudgeUtils.isNotBlank(merRegister.getSettleExpDate())) {
            settleBaseDO.setExpDate(DateTimeUtils.parseLocalDate(merRegister.getSettleExpDate().replaceAll("-","").trim()));
        }
        //如果为自动结算则需要初始化下一个结算日期
        if ("auto".equals(merRegister.getSettleType())) {
            if ("daily".equals(merRegister.getSettleCycleType())) {
                settleBaseDO.setNextSettleDay(DateTimeUtils.getCurrentLocalDate().plusDays(1));
            }
            if ("weekly".equals(merRegister.getSettleCycleType())) {
                settleBaseDO.setNextSettleDay(DateTimeUtils.getCurrentLocalDate().plusWeeks(1));
            }
            if ("monthly".equals(merRegister.getSettleCycleType())) {
                settleBaseDO.setNextSettleDay(DateTimeUtils.getCurrentLocalDate().plusMonths(1));
            }
        }
        settleBaseDO.setOprId(getOpr.getOperatorName());
        //设置为生效
        settleBaseDO.setStats("1");
    }
    //赋值结算卡信息
    public void copyToSettleCardDO (MerRegister merRegister , SettleCardDO settleCardDO ) {
        BeanUtils.copyProperties(settleCardDO , merRegister);
        settleCardDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        settleCardDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        settleCardDO.setOprId(getOpr.getOperatorName());
        settleCardDO.setUserName(merRegister.getMercName());
        settleCardDO.setSubbranch(merRegister.getSubbranch());
        settleCardDO.setCapCorgSnm(settleCardDO.getCapCorgNm());
        if (JudgeUtils.isNotBlank(merRegister.getSettleEffDate())) {
            settleCardDO.setEffDate(DateTimeUtils.parseLocalDate(merRegister.getSettleEffDate().replaceAll("-","").trim()));
        }
        if (JudgeUtils.isNotBlank(merRegister.getSettleExpDate())) {
            settleCardDO.setExpDate(DateTimeUtils.parseLocalDate(merRegister.getSettleExpDate().replaceAll("-","").trim()));
        }
        settleCardDO.setUserName(merRegister.getMercName());
        settleCardDO.setTmSmp(DateTimeUtils.getCurrentLocalDateTime());
        //设置为生效
        settleCardDO.setStats("1");
    }

    //开通子商户商户
    public void openMerSub(MerRegister merRegister) {
            //检查是否已经开通
            SubMercReqDTO subMercReqDTO = new SubMercReqDTO();
            subMercReqDTO.setMerchantId(merRegister.getUserId());
            subMercReqDTO.setMerchantName(merRegister.getMercName());
            subMercReqDTO.setMerchantShortname(merRegister.getMercShortName());
            subMercReqDTO.setContactPhone(merRegister.getMblNo());
            subMercReqDTO.setOfficePhone(merRegister.getMblNo());
            subMercReqDTO.setContactEmail(merRegister.getEmail());
            subMercReqDTO.setMerchantRemark(merRegister.getMercShortName());
            GenericDTO<SubMercReqDTO> gensubDTO = GenericDTO.newInstance(subMercReqDTO);
            GenericRspDTO<SubMercRspDTO> genericRspDTO = ebankpayClient.applySubMerc(gensubDTO);
            if (!"CPI00000".equals(genericRspDTO.getMsgCd())) {
                throw new LemonException();
            }
    }
}
