package com.hisun.tms.urm.model.dto;

/**
 * 用户注册查询DTO
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class UserRegisterQueryDTO {
    
    /** 用户ID */
    private String userId;
    
    /** 分页起始位置 */
    private int start;
    
    /** 分页长度 */
    private int length;
    
    /** 绘制次数 */
    private int draw;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public int getDraw() {
        return draw;
    }

    public void setDraw(int draw) {
        this.draw = draw;
    }
}