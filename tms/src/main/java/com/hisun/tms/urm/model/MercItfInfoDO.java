package com.hisun.tms.urm.model;


import com.hisun.lemon.framework.data.BaseDO;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public class MercItfInfoDO extends BaseDO {
	/**
     * @Fields 用户号
     */
    private String userId;
	/**
     * @Fields 接口名称
     */
    private String itfNm;
	/**
     * @Fields 版本号
     */
    private String version;
	/**
     * @Fields 接口签名类型 MD5
     */
    private String verifyType;
	/**
     * @Fields 接口密钥
     */
    private String secretKey;
	/**
     * @Fields 接口状态  1:生效 0:失效
     */
    private String sts;
	/**
     * @Fields 请求签名字段串
     */
    private String macItem;
	/**
     * @Fields 返回签名字段串
     */
    private String rspMacItem;
	/**
     * @Fields 最后更新柜员
     */
    private String lastUpdOpr;
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getItfNm() {
		return itfNm;
	}
	public void setItfNm(String itfNm) {
		this.itfNm = itfNm;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getVerifyType() {
		return verifyType;
	}
	public void setVerifyType(String verifyType) {
		this.verifyType = verifyType;
	}
	public String getSecretKey() {
		return secretKey;
	}
	public void setSecretKey(String secretKey) {
		this.secretKey = secretKey;
	}
	public String getSts() {
		return sts;
	}
	public void setSts(String sts) {
		this.sts = sts;
	}
	public String getMacItem() {
		return macItem;
	}
	public void setMacItem(String macItem) {
		this.macItem = macItem;
	}
	public String getRspMacItem() {
		return rspMacItem;
	}
	public void setRspMacItem(String rspMacItem) {
		this.rspMacItem = rspMacItem;
	}
	public String getLastUpdOpr() {
		return lastUpdOpr;
	}
	public void setLastUpdOpr(String lastUpdOpr) {
		this.lastUpdOpr = lastUpdOpr;
	}
}
