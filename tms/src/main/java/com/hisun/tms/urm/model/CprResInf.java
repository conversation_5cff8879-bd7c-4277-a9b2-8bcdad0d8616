package com.hisun.tms.urm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Created by chen on 9/2 0002.
 */
@Entity
@Table(name = "urm_cpr_res_inf")
public class CprResInf {
    @Id
    @Column(name = "user_id")
    private String userId;

    //商户logo照片路径
    @Column(name = "logo_path")
    private String mercLogoPath;

    //商户营业执照
    @Column(name = "busLic_img_path")
    private String busLicImgPath;

    //商户银行卡照片
    @Column(name = "card_img_a")
    private String cardImgPath;

    @Column(name = "card_img_b")
    private String cardImgPathB;

    //商户身份证照片
    @Column(name = "cert_img_a")
    private String certImgPath;

    @Column(name = "cert_img_b")
    private String certImgPathB;

    //商户商户协议证书照片
    @Column(name = "potocol_img_path")
    private String merPotocolImgPath;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMercLogoPath() {
        return mercLogoPath;
    }

    public void setMercLogoPath(String mercLogoPath) {
        this.mercLogoPath = mercLogoPath;
    }

    public String getBusLicImgPath() {
        return busLicImgPath;
    }

    public void setBusLicImgPath(String busLicImgPath) {
        this.busLicImgPath = busLicImgPath;
    }

    public String getCardImgPath() {
        return cardImgPath;
    }

    public void setCardImgPath(String cardImgPath) {
        this.cardImgPath = cardImgPath;
    }

    public String getCardImgPathB() {
        return cardImgPathB;
    }

    public void setCardImgPathB(String cardImgPathB) {
        this.cardImgPathB = cardImgPathB;
    }

    public String getCertImgPath() {
        return certImgPath;
    }

    public void setCertImgPath(String certImgPath) {
        this.certImgPath = certImgPath;
    }

    public String getCertImgPathB() {
        return certImgPathB;
    }

    public void setCertImgPathB(String certImgPathB) {
        this.certImgPathB = certImgPathB;
    }

    public String getMerPotocolImgPath() {
        return merPotocolImgPath;
    }

    public void setMerPotocolImgPath(String merPotocolImgPath) {
        this.merPotocolImgPath = merPotocolImgPath;
    }
}
