package com.hisun.tms.urm.model;

import java.math.BigDecimal;

/**
 * Created by chen on 10/26 0026.
 */
public class UrmMercBalanceDO {
    private String userId ;

    private String mobile;

    private String userNm;

    /**
     * acCurBal 现金账户当前可用余额
     */
    private BigDecimal cashAcCurBal;
    /**
     * acUavaBal 现金账户当前不可用余额
     */
    private BigDecimal cashAcUavaBal;
    /**
     * acCurBal 结算账户当前可用余额
     */
    private BigDecimal settleAcCurBal;
    /**
     * acUavaBal 结算账户当前不可用余额
     */
    private BigDecimal settleAcUavaBal;

    /**
     * 现金账户
     * @return
     */
    private String cashAcc;

    /**
     * 结算账户
     * @return
     */
    private String settleAcc;

    public String getCashAcc() {
        return cashAcc;
    }

    public void setCashAcc(String cashAcc) {
        this.cashAcc = cashAcc;
    }

    public String getSettleAcc() {
        return settleAcc;
    }

    public void setSettleAcc(String settleAcc) {
        this.settleAcc = settleAcc;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getCashAcCurBal() {
        return cashAcCurBal;
    }

    public void setCashAcCurBal(BigDecimal cashAcCurBal) {
        this.cashAcCurBal = cashAcCurBal;
    }

    public BigDecimal getCashAcUavaBal() {
        return cashAcUavaBal;
    }

    public void setCashAcUavaBal(BigDecimal cashAcUavaBal) {
        this.cashAcUavaBal = cashAcUavaBal;
    }

    public BigDecimal getSettleAcCurBal() {
        return settleAcCurBal;
    }

    public void setSettleAcCurBal(BigDecimal settleAcCurBal) {
        this.settleAcCurBal = settleAcCurBal;
    }

    public BigDecimal getSettleAcUavaBal() {
        return settleAcUavaBal;
    }

    public void setSettleAcUavaBal(BigDecimal settleAcUavaBal) {
        this.settleAcUavaBal = settleAcUavaBal;
    }

    public String getUserNm() {
        return userNm;
    }

    public void setUserNm(String userNm) {
        this.userNm = userNm;
    }
}
