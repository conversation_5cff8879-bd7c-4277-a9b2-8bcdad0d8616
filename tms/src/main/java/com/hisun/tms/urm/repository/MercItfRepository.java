package com.hisun.tms.urm.repository;

import com.hisun.tms.urm.model.MercItfInfo;


import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface MercItfRepository extends JpaRepository<MercItfInfo, String> {
//	@Query("select userId,itfNm,version,verifyType,secretKey,sts,macItem,rspMacItem,lastUpdOpr from MercItfInfo t where t.userId = :userId")
//	List<MercItfInfo> getByUserId(String userId);
	@Modifying
    @Query("update MercItfInfo mercItfInfo set mercItfInfo.sts = :sts,mercItfInfo.secretKey = :secretKey,mercItfInfo.lastUpdOpr = :lastUpdOpr where mercItfInfo.userId = :userId and mercItfInfo.itfNm = :itfNm")
    void modifyMercItfInfo(@Param("sts") String sts, @Param("secretKey") String secretKey, @Param("lastUpdOpr") String lastUpdOpr, @Param("userId") String userId, @Param("itfNm") String itfNm);
	
	List<MercItfInfo> findByUserId(String userId);

	MercItfInfo getInfoByUserIdAndItfNm(String mercId, String itfNm);
}
