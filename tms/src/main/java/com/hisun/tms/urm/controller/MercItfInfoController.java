package com.hisun.tms.urm.controller;


import com.hisun.tms.urm.service.MercItfSetService;
import com.hisun.tms.urm.service.MercKeyResetService;
import com.hisun.tms.urm.service.MercPayPswdResetService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/busmgr/mercitfInfoctrl")
public class MercItfInfoController {

    private static final Logger logger = LoggerFactory.getLogger(KeyResetController.class);

    @Autowired
    private MercItfSetService mercItfSetService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercitf') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mercitf/mercitfinfo");
        return modelAndView;
    }

    @PostMapping(value = "query")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercitf') or hasRole('ROLE_ADMIN')")
    public Map<String,String> getUserInf(@RequestParam(value = "mercId", required = true) String mercId,@RequestParam(value = "usrNm", required = true) String usrNm) {
        return mercItfSetService.getUserInf(mercId,usrNm);
    }
    
    @PostMapping(value = "itfset")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercitf') or hasRole('ROLE_ADMIN')")
    public Map<String,String> itfSet(@RequestParam(value = "mercId", required = true) String mercId,@RequestParam(value = "itfNm", required = true) String itfNm,@RequestParam(value = "itfSts", required = true) boolean itfSts) {
        return mercItfSetService.itfSet(mercId,itfNm,itfSts);
    }
}
