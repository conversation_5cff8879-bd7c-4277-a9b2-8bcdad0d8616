package com.hisun.tms.urm.controller;

import com.hisun.tms.urm.model.UrmUserBasicInf;
import com.hisun.tms.urm.model.dto.UserRegisterQueryDTO;
import com.hisun.tms.urm.service.UserRegisterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户注册管理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Controller
@RequestMapping("/urm/mermgr/register")
public class UserRegisterController {

    private static final Logger logger = LoggerFactory.getLogger(UserRegisterController.class);

    @Resource
    private UserRegisterService userRegisterService;

    /**
     * 用户注册管理页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/register') or hasRole('ROLE_ADMIN')")
    public ModelAndView registerList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/register");
        return modelAndView;
    }

    /**
     * 获取单个用户注册信息
     */
    @PostMapping(value = "/getUser")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/register') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public UrmUserBasicInf getUser(@RequestParam(value = "userId", required = false) String userId) {
        try {
            UrmUserBasicInf userDetail = userRegisterService.get(userId);
            return userDetail;
        } catch (Exception e) {
            logger.error("获取用户注册详情失败: ", e);
            return null;
        }
    }

    /**
     * 查询所有用户注册信息
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/register') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<UrmUserBasicInf> findAll(@Valid @RequestBody UserRegisterQueryDTO input) {
        try {
            logger.debug("查询用户注册列表，参数: {}", input);
            DataTablesOutput<UrmUserBasicInf> result = userRegisterService.findAll(input);
            logger.debug("查询用户注册列表，结果数量: {}", result.getData().size());
            return result;
        } catch (Exception e) {
            logger.error("查询用户注册列表失败: ", e);
            // 返回空结果而不是null，避免前端解析错误
            DataTablesOutput<UrmUserBasicInf> emptyResult = new DataTablesOutput<>();
            emptyResult.setDraw(input.getDraw());
            emptyResult.setRecordsTotal(0);
            emptyResult.setRecordsFiltered(0);
            return emptyResult;
        }
    }

    /**
     * 添加用户注册信息
     */
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/register:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(UrmUserBasicInf urmUserBasicInf) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = userRegisterService.add(urmUserBasicInf);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("添加用户注册信息失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 修改用户注册信息
     */
    @PostMapping(value = "/modify/{userId}")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/register:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(UrmUserBasicInf urmUserBasicInf) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = userRegisterService.update(urmUserBasicInf);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("修改用户注册信息失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 删除用户注册信息
     */
    @DeleteMapping(value = "/delete/{userId}")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/register:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public String delete(@PathVariable("userId") String userId) {
        String result = "";
        try {
            result = userRegisterService.delete(userId);
        } catch (Exception e) {
            logger.error("删除用户注册信息失败: ", e);
            result = "";
        }
        return result;
    }
}