package com.hisun.tms.urm.model;

import java.math.BigDecimal;

/**
 * Created by chen on 8/29 0029.
 */
public class UrmUserBalanceDO {

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 现金账户总余额
     */
    private BigDecimal cash;

    /**
     * 现金账户可用余额
     */
    private BigDecimal cashBalance;

    /**
     * seaccy
     */
    private Integer seaccy;

    /**
     * seaccyBalance
     */
    private Integer seaccyBalance;

    /**
     * financeBalance
     */
    private BigDecimal financeBalance;

    /**
     * 现金账户
     * @return
     */
    private String cashAcc ;

    /**
     * 结算账户
     * @return
     */
    public BigDecimal getCash() {
        return cash;
    }

    public void setCash(BigDecimal cash) {
        this.cash = cash;
    }

    public BigDecimal getCashBalance() {
        return cashBalance;
    }

    public void setCashBalance(BigDecimal cashBalance) {
        this.cashBalance = cashBalance;
    }

    public Integer getSeaccy() {
        return seaccy;
    }

    public void setSeaccy(Integer seaccy) {
        this.seaccy = seaccy;
    }

    public Integer getSeaccyBalance() {
        return seaccyBalance;
    }

    public void setSeaccyBalance(Integer seaccyBalance) {
        this.seaccyBalance = seaccyBalance;
    }

    public BigDecimal getFinanceBalance() {
        return financeBalance;
    }

    public void setFinanceBalance(BigDecimal financeBalance) {
        this.financeBalance = financeBalance;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCashAcc() {
        return cashAcc;
    }

    public void setCashAcc(String cashAcc) {
        this.cashAcc = cashAcc;
    }
}
