package com.hisun.tms.urm.repository;

import com.hisun.tms.urm.model.UrmCprExtInfDO;
import com.hisun.tms.urm.model.UserBasicInfo;

import org.springframework.data.jpa.repository.JpaRepository;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface MercInfoRepository extends JpaRepository<UrmCprExtInfDO, String> {
	UrmCprExtInfDO getInfoByUserId(String userId);

	UrmCprExtInfDO getInfoByUserIdAndMercName(String mercId, String mercName);

	UrmCprExtInfDO getInfoByMercName(String mercName);
}
