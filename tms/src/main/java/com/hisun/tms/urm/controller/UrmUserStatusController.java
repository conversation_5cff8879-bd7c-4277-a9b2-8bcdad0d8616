package com.hisun.tms.urm.controller;

import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.urm.model.UserStatus;
import com.hisun.tms.urm.service.UserStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/10/29
 */
@Controller
@RequestMapping("/urm/usrmgr/status")
public class UrmUserStatusController {

    private static final Logger logger = LoggerFactory.getLogger(UrmUserStatusController.class);

    @Resource
    private UserStatusService userStatusService;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/status') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/usrmgr/status");
        return modelAndView;
    }

    @ResponseBody
    @GetMapping("/{mblNo}/{userId}")
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/status') or hasRole('ROLE_ADMIN')")
    public UserStatus search(@PathVariable("mblNo") String mblNo ,@PathVariable("userId") String userId) {
        if(StringUtils.isNotBlank(mblNo) && !"blank".equals(mblNo)) {
            GenericRspDTO<UserBasicInfDTO> genericRspDTO = userBasicInfClient.queryUserByLoginId(mblNo);
            if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
                return null;
            }
            if (StringUtils.isNotBlank(userId) && !"blank".equals(userId) && !userId.equals(genericRspDTO.getBody().getUserId())) {
                return null;
            }
            userId = genericRspDTO.getBody().getUserId();
        } else {
            //获取手机号
            GenericRspDTO<UserBasicInfDTO> genericRspDTO = userBasicInfClient.queryUser(userId);
            if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
                return null;
            } else {
                mblNo = genericRspDTO.getBody().getMblNo();
            }
        }
        UserStatus userStatus = userStatusService.search(userId);
        userStatus.setMblNo(mblNo);
        userStatus.setUserId(userId);
        return userStatus;
    }

    @ResponseBody
    @PostMapping("/change")
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/status') or hasRole('ROLE_ADMIN')")
    public String search(UserStatus userStatus) {
        logger.debug(userStatus.toString());
        userStatusService.save(userStatus.getUserId(), userStatus.getUserSts());
        return "0";
    }
}
