package com.hisun.tms.urm.service;
import java.util.HashMap;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.client.UserPasswordClient;
import com.hisun.tms.urm.dao.MercItfInfoDao;
import com.hisun.tms.urm.model.MercItfInfoDO;
import com.hisun.tms.urm.model.UrmCprExtInfDO;
import com.hisun.tms.urm.repository.MercInfoRepository;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("mercItfSetService")
public class MercItfSetServiceImpl implements MercItfSetService {

    private static final Logger logger = LoggerFactory.getLogger(MercItfSetServiceImpl.class);
    
    @Autowired
    private MercInfoRepository mercInfoRepository;
    @Autowired
    private UserPasswordClient userPasswordClient;
    @Resource
    private MercItfInfoDao mercItfInfoDao;
    
    
	@Override
	public Map<String, String> getUserInf(String mercId, String usrNm) {
		UrmCprExtInfDO mercBasicInfo=new UrmCprExtInfDO();
		Map<String,String> map =  new HashMap<String,String>();
		if(JudgeUtils.isNotBlank(mercId)&&JudgeUtils.isNotBlank(usrNm)){
			mercBasicInfo=mercInfoRepository.getInfoByUserIdAndMercName(mercId,usrNm);
		}else if(JudgeUtils.isNotBlank(mercId)){
			mercBasicInfo=mercInfoRepository.getInfoByUserId(mercId);
		}else if(JudgeUtils.isNotBlank(usrNm)){
			try{
				mercBasicInfo=mercInfoRepository.getInfoByMercName(usrNm);
			}catch(IncorrectResultSizeDataAccessException incorrectResultSizeDataAccessException){
				//存在多个用户
				map.put("result", "mercMoreThanOne");
				return map;
			}
		}
		if(JudgeUtils.isNull(mercBasicInfo)){
			map.put("result", "mercIsNull");
		}else{
			mercId=mercBasicInfo.getUserId();
			usrNm=mercBasicInfo.getMercName();
			List<MercItfInfoDO> mercItfInfos=mercItfInfoDao.findByUserId(mercId);
			if(!CollectionUtils.isEmpty(mercItfInfos)){
				String itfNm=null;
				String itfSts=null;
				MercItfInfoDO mercItfInfo=null;
				Iterator<MercItfInfoDO> iterator = mercItfInfos.iterator(); 
		        while(iterator.hasNext()){ 
		            mercItfInfo =  iterator.next(); 
		            itfSts=mercItfInfo.getSts();
		            if("1".equals(itfSts)){
						itfNm=mercItfInfo.getItfNm();
						map.put(itfNm, itfNm);
					}
		        }
			}
			map.put("usrNm", usrNm);
			map.put("mercId", mercId);
			map.put("result", "URM00000");
		}
		return map;
	}


	@Override
	public Map<String, String> itfSet(String mercId, String itfNm, boolean itfSts) {
		logger.info("mercId"+mercId+"itfNm"+itfNm+"itfSts"+itfSts);
		MercItfInfoDO mercItfInfo=mercItfInfoDao.getInfoByUserIdAndItfNm(mercId,itfNm);
		Map<String,String> map =  new HashMap<String,String>();
		String secretKey=null;
		if(itfSts){//如果是开启接口权限则查询密钥
			GenericRspDTO<String> genericRspDTO=userPasswordClient.resetCprTradingKey(mercId);
			if (!JudgeUtils.isSuccess(genericRspDTO.getMsgCd())) {
				map.put("result", genericRspDTO.getMsgCd()+":"+genericRspDTO.getMsgInfo());
				return map;
			}else{
				secretKey=genericRspDTO.getBody();
			}
		}
		if(JudgeUtils.isNull(mercItfInfo)){
			//
			mercItfInfo=new MercItfInfoDO();
			mercItfInfo.setItfNm(itfNm);
			if(itfSts){
				mercItfInfo.setSts("1");
			}else{
				mercItfInfo.setSts("0");
			}
			mercItfInfo.setSecretKey(secretKey);
			mercItfInfo.setLastUpdOpr(getOperatorName());
			mercItfInfo.setUserId(mercId);
			mercItfInfo.setVersion("1.0");
			mercItfInfo.setVerifyType("MD5");
			mercItfInfoDao.insert(mercItfInfo);
		}else{
			if(itfSts){
				mercItfInfo.setSts("1");
				mercItfInfo.setSecretKey(secretKey);
			}else{
				mercItfInfo.setSts("0");
			}
			mercItfInfo.setLastUpdOpr(getOperatorName());
			mercItfInfoDao.updateMercItfInfo(mercItfInfo.getSts(), mercItfInfo.getSecretKey(), mercItfInfo.getLastUpdOpr(), mercItfInfo.getUserId(), mercItfInfo.getItfNm());
		}
		map.put("result", "URM00000");
		return map;
	}
	
	
	private String getOperatorName(){
        org.springframework.security.core.userdetails.User securityUser = (org.springframework.security.core.userdetails.User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return securityUser.getUsername();
    }

}
