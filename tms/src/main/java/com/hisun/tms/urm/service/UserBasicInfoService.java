package com.hisun.tms.urm.service;

import com.hisun.tms.urm.model.UserBasicInfo;
import com.hisun.tms.urm.model.UserParamInput;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface UserBasicInfoService {
	/**
	 * 用户资料查询
	 * @param userParamInput
	 * @return
	 */
    DataTablesOutput<UserBasicInfo> findAll(UserParamInput userParamInput);
    /**
     * 支付密码重置中的信息查询
     * @param input
     * @return
     */
	DataTablesOutput<UserBasicInfo> findOne(UserParamInput input);

    Map<String,String> cancelUser(String userId);
}