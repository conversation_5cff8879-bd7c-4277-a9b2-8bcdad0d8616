package com.hisun.tms.urm.model;

import java.util.Date;

/**
 * 用户基本信息实体类
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class UrmUserBasicInf {
    
    /** 内部用户号 */
    private String userId;
    
    /** 手机号码 */
    private String mblNo;
    
    /** 用户状态 0:开户 1:销户 */
    private String usrSts;
    
    /** 用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家 */
    private String usrLvl;
    
    /** 实名标志 0：非实名 1：实名 */
    private String idChkFlg;
    
    /** 认证流水号 */
    private String idChkNo;
    
    /** 认证日期 */
    private Date idChkDt;
    
    /** 认证时间 */
    private String idChkTm;
    
    /** 证件类型 */
    private String idType;
    
    /** 证件号码 */
    private String idNo;
    
    /** 脱敏证件号码 */
    private String idNoHid;
    
    /** 用户姓名 */
    private String usrNm;
    
    /** 脱敏用户姓名 */
    private String usrNmHid;
    
    /** 用户性别 M：男 F：女 */
    private String usrGender;
    
    /** 用户归属国家 */
    private String usrNation;
    
    /** 出生日期 */
    private String usrBirthDt;
    
    /** 签发机关 */
    private String issuAuth;
    
    /** 证件有效期起始 */
    private String idEffDt;
    
    /** 证件有效期截止 */
    private String idExpDt;
    
    /** 注册渠道 */
    private String usrRegCnl;
    
    /** 注册IP */
    private String usrRegIp;
    
    /** 注册日期 */
    private Date usrRegDt;
    
    /** 注册时间 */
    private String usrRegTm;
    
    /** 销户日期 */
    private Date usrClsDt;
    
    /** 销户时间 */
    private String usrClsTm;
    
    /** 更新时间 */
    private Date modifyTime;
    
    /** 创建时间 */
    private Date createTime;
    
    /** KYB认证 */
    private String kybCert;

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getUsrSts() {
        return usrSts;
    }

    public void setUsrSts(String usrSts) {
        this.usrSts = usrSts;
    }

    public String getUsrLvl() {
        return usrLvl;
    }

    public void setUsrLvl(String usrLvl) {
        this.usrLvl = usrLvl;
    }

    public String getIdChkFlg() {
        return idChkFlg;
    }

    public void setIdChkFlg(String idChkFlg) {
        this.idChkFlg = idChkFlg;
    }

    public String getIdChkNo() {
        return idChkNo;
    }

    public void setIdChkNo(String idChkNo) {
        this.idChkNo = idChkNo;
    }

    public Date getIdChkDt() {
        return idChkDt;
    }

    public void setIdChkDt(Date idChkDt) {
        this.idChkDt = idChkDt;
    }

    public String getIdChkTm() {
        return idChkTm;
    }

    public void setIdChkTm(String idChkTm) {
        this.idChkTm = idChkTm;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdNoHid() {
        return idNoHid;
    }

    public void setIdNoHid(String idNoHid) {
        this.idNoHid = idNoHid;
    }

    public String getUsrNm() {
        return usrNm;
    }

    public void setUsrNm(String usrNm) {
        this.usrNm = usrNm;
    }

    public String getUsrNmHid() {
        return usrNmHid;
    }

    public void setUsrNmHid(String usrNmHid) {
        this.usrNmHid = usrNmHid;
    }

    public String getUsrGender() {
        return usrGender;
    }

    public void setUsrGender(String usrGender) {
        this.usrGender = usrGender;
    }

    public String getUsrNation() {
        return usrNation;
    }

    public void setUsrNation(String usrNation) {
        this.usrNation = usrNation;
    }

    public String getUsrBirthDt() {
        return usrBirthDt;
    }

    public void setUsrBirthDt(String usrBirthDt) {
        this.usrBirthDt = usrBirthDt;
    }

    public String getIssuAuth() {
        return issuAuth;
    }

    public void setIssuAuth(String issuAuth) {
        this.issuAuth = issuAuth;
    }

    public String getIdEffDt() {
        return idEffDt;
    }

    public void setIdEffDt(String idEffDt) {
        this.idEffDt = idEffDt;
    }

    public String getIdExpDt() {
        return idExpDt;
    }

    public void setIdExpDt(String idExpDt) {
        this.idExpDt = idExpDt;
    }

    public String getUsrRegCnl() {
        return usrRegCnl;
    }

    public void setUsrRegCnl(String usrRegCnl) {
        this.usrRegCnl = usrRegCnl;
    }

    public String getUsrRegIp() {
        return usrRegIp;
    }

    public void setUsrRegIp(String usrRegIp) {
        this.usrRegIp = usrRegIp;
    }

    public Date getUsrRegDt() {
        return usrRegDt;
    }

    public void setUsrRegDt(Date usrRegDt) {
        this.usrRegDt = usrRegDt;
    }

    public String getUsrRegTm() {
        return usrRegTm;
    }

    public void setUsrRegTm(String usrRegTm) {
        this.usrRegTm = usrRegTm;
    }

    public Date getUsrClsDt() {
        return usrClsDt;
    }

    public void setUsrClsDt(Date usrClsDt) {
        this.usrClsDt = usrClsDt;
    }

    public String getUsrClsTm() {
        return usrClsTm;
    }

    public void setUsrClsTm(String usrClsTm) {
        this.usrClsTm = usrClsTm;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getKybCert() {
        return kybCert;
    }

    public void setKybCert(String kybCert) {
        this.kybCert = kybCert;
    }
}