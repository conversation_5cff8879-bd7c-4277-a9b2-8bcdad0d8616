package com.hisun.tms.urm.controller;

import com.hisun.tms.urm.model.UserBasicInfo;
import com.hisun.tms.urm.model.UserHistoryInfo;
import com.hisun.tms.urm.model.UserParamInput;
import com.hisun.tms.urm.service.UserBasicInfoService;
import com.hisun.tms.urm.service.UserHistoryService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/busmgr/userhistoryctrl")
public class UserHistoryController {

    private static final Logger logger = LoggerFactory.getLogger(UserHistoryController.class);

    @Autowired
    private UserHistoryService userHistoryService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/userhistory') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/userhistory/overheadhistory");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/userhistory') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<UserHistoryInfo> findAll(@Valid @RequestBody UserParamInput input) {
        return userHistoryService.findAll(input);
    }
}
