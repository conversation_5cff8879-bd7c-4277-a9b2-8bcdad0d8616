package com.hisun.tms.urm.service;

import com.hisun.tms.urm.model.UrmKybSeq;
import com.hisun.tms.urm.model.UserKybDetail;
import com.hisun.tms.urm.model.dto.KybAuditReq;
import com.hisun.tms.urm.model.dto.QueryKybDTO;
import com.hisun.tms.urm.model.dto.QueryResultKybDTO;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.List;
import java.util.Map;

public interface UserKybInfoService {

    /**
     * 获取KYB信息列表（旧方法，保留兼容）
     */
    List<UrmKybSeq> getKybInfo(String userId, String examineStatus);

    /**
     * KYB审核（旧方法，保留兼容）
     */
    Map<String, String> examine(UrmKybSeq urmKybSeq);

    /**
     * 获取KYB详情（旧方法，保留兼容）
     */
    UserKybDetail getKybDetail(String userId);

    /**
     * 获取KYB审核列表
     */
    List<QueryResultKybDTO.QueryKyb> queryKybAudit(QueryKybDTO queryDTO);

    /**
     * 查询KYB详情
     */
    UserKybDetail detail(String userId, Integer kybId);

    /**
     * 审核KYB
     */
    void kybAudit(KybAuditReq req);

}
