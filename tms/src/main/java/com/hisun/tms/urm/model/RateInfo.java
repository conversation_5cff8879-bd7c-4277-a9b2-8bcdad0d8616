package com.hisun.tms.urm.model;

import java.math.BigDecimal;

/**
 * Created by chen on 8/31 0031.
 */
public class RateInfo {
    //产品类型
    private String busType;

    //收费方式
    private String chargeType;

    //计费方式
    private String calculateType;

    //最低收取金额
    private String beginCalAmt ;

    //利率
    private String rate ;

    //最低收取金额
    private String  minFee ;

    //最高收取金额
    private String maxFee ;

    //生效日期
    private String expDate ;

    //失效日期
    private String effDate ;
    //计费开始金额
    private String beginCalFee ;

    //支付渠道
    private String channel ;

    /**
     * @Fields calculateMod 计费模式 internal:内扣 external:外扣
     */
    private String calculateMod;


    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public String getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(String calculateType) {
        this.calculateType = calculateType;
    }

    public String getExpDate() {
        return expDate;
    }

    public void setExpDate(String expDate) {
        this.expDate = expDate;
    }

    public String getEffDate() {
        return effDate;
    }

    public void setEffDate(String effDate) {
        this.effDate = effDate;
    }

    public String getBeginCalAmt() {
        return beginCalAmt;
    }

    public void setBeginCalAmt(String beginCalAmt) {
        this.beginCalAmt = beginCalAmt;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getMinFee() {
        return minFee;
    }

    public void setMinFee(String minFee) {
        this.minFee = minFee;
    }

    public String getMaxFee() {
        return maxFee;
    }

    public void setMaxFee(String maxFee) {
        this.maxFee = maxFee;
    }

    public String getBeginCalFee() {
        return beginCalFee;
    }

    public void setBeginCalFee(String beginCalFee) {
        this.beginCalFee = beginCalFee;
    }

    public String getCalculateMod() {
        return calculateMod;
    }

    public void setCalculateMod(String calculateMod) {
        this.calculateMod = calculateMod;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    @Override
    public String toString() {
        return "RateInfo{" +
                "busType='" + busType + '\'' +
                ", chargeType='" + chargeType + '\'' +
                ", calculateType='" + calculateType + '\'' +
                ", beginCalAmt='" + beginCalAmt + '\'' +
                ", rate='" + rate + '\'' +
                ", minFee='" + minFee + '\'' +
                ", maxFee='" + maxFee + '\'' +
                ", expDate='" + expDate + '\'' +
                ", effDate='" + effDate + '\'' +
                ", beginCalFee='" + beginCalFee + '\'' +
                ", channel='" + channel + '\'' +
                ", calculateMod='" + calculateMod + '\'' +
                '}';
    }
}
