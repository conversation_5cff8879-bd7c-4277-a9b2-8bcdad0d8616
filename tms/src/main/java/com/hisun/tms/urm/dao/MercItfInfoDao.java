package com.hisun.tms.urm.dao;

import com.hisun.tms.urm.model.MercItfInfoDO;

import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 理财收益
 * <AUTHOR>
 * @date 2017年7月7日
 * @time 上午 11:39:21
 *
 */
public interface MercItfInfoDao {

	List<MercItfInfoDO> findByUserId(@Param("userId") String userId);
	
	MercItfInfoDO getInfoByUserIdAndItfNm(@Param("userId") String userId,@Param("itfNm") String itfNm);

	int updateMercItfInfo(@Param("sts")String sts, @Param("secretKey")String secretKey, @Param("lastUpdOpr")String lastUpdOpr, @Param("userId")String userId, @Param("itfNm")String itfNm);

	int insert(MercItfInfoDO mercItfInfo);

    Integer checkComercReg(@Param("comercReg") String comercReg,@Param("merId") String merId);

    Integer checkMercShortName(@Param("mercShortName") String mercShortName,@Param("merId") String merId);
}