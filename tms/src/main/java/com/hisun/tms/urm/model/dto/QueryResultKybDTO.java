package com.hisun.tms.urm.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * KYB查询结果DTO
 */
@ApiModel("KYB查询结果DTO")
public class QueryResultKybDTO {

    @ApiModelProperty(name = "queryKybList", value = "KYB审核列表")
    private List<QueryKyb> queryKybList;

    public List<QueryKyb> getQueryKybList() {
        return queryKybList;
    }

    public void setQueryKybList(List<QueryKyb> queryKybList) {
        this.queryKybList = queryKybList;
    }

    @ApiModel("KYB审核信息")
    public static class QueryKyb {
        @ApiModelProperty(name = "kybId", value = "KYB审核ID")
        private Integer kybId;

        @ApiModelProperty(name = "userId", value = "用户ID")
        private String userId;

        @ApiModelProperty(name = "examineStatus", value = "审核状态")
        private String examineStatus;

        @ApiModelProperty(name = "firstAuditUser", value = "初审人")
        private String firstAuditUser;

        @ApiModelProperty(name = "firstAuditTime", value = "初审时间")
        private Date firstAuditTime;

        @ApiModelProperty(name = "firstAuditResult", value = "初审结果")
        private String firstAuditResult;

        @ApiModelProperty(name = "secondAuditUser", value = "复核人")
        private String secondAuditUser;

        @ApiModelProperty(name = "secondAuditTime", value = "复核时间")
        private Date secondAuditTime;

        @ApiModelProperty(name = "secondAuditResult", value = "复核结果")
        private String secondAuditResult;

        @ApiModelProperty(name = "createTime", value = "创建时间")
        private Date createTime;

        public Integer getKybId() {
            return kybId;
        }

        public void setKybId(Integer kybId) {
            this.kybId = kybId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getExamineStatus() {
            return examineStatus;
        }

        public void setExamineStatus(String examineStatus) {
            this.examineStatus = examineStatus;
        }

        public String getFirstAuditUser() {
            return firstAuditUser;
        }

        public void setFirstAuditUser(String firstAuditUser) {
            this.firstAuditUser = firstAuditUser;
        }

        public Date getFirstAuditTime() {
            return firstAuditTime;
        }

        public void setFirstAuditTime(Date firstAuditTime) {
            this.firstAuditTime = firstAuditTime;
        }

        public String getFirstAuditResult() {
            return firstAuditResult;
        }

        public void setFirstAuditResult(String firstAuditResult) {
            this.firstAuditResult = firstAuditResult;
        }

        public String getSecondAuditUser() {
            return secondAuditUser;
        }

        public void setSecondAuditUser(String secondAuditUser) {
            this.secondAuditUser = secondAuditUser;
        }

        public Date getSecondAuditTime() {
            return secondAuditTime;
        }

        public void setSecondAuditTime(Date secondAuditTime) {
            this.secondAuditTime = secondAuditTime;
        }

        public String getSecondAuditResult() {
            return secondAuditResult;
        }

        public void setSecondAuditResult(String secondAuditResult) {
            this.secondAuditResult = secondAuditResult;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }
    }
}