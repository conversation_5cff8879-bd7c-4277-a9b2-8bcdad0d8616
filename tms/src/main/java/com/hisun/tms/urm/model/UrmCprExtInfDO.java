/*
 * @ClassName UrmCprExtInfDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.tms.urm.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "urm_cpr_ext_inf")
public class UrmCprExtInfDO {
    /**
     *  userId 内部用户号
     */
    @Id
    @Column(name = "USER_ID")
    private String userId;
    /**
     *  mercName 商户名称
     */
    @Column(name = "MERC_NAME")
    private String mercName;
    /**
     *  mercShortName 商户简称
     */
    @Column(name = "MERC_SHORT_NAME")
    private String mercShortName;
    /**
     *  cprRegNmCn 注册名称(中文)
     */
    @Column(name = "CPR_REG_NM_CN")
    private String cprRegNmCn;
    /**
     *  cprOperNmCn 经营名称（中文）
     */
    @Column(name = "CPR_OPER_NM_CN")
    private String cprOperNmCn;
    /**
     *  prinNm 负责人名称
     */
    @Column(name = "PRIN_NM")
    private String prinNm;

    /**
     *  crpNm 法人名称
     */
    @Column(name = "CRP_NM")
    private String crpNm;

    /**
     * crpIdTyp 法人证件类型
     */
    @Column(name = "CRP_ID_TYP")
    private String crpIdTyp;

    /**
     * crpIdNo 法人证件号
     */
    @Column(name = "CRP_ID_NO")
    private String crpIdNo;

    /**
     *  comercReg 工商注册号
     */
    @Column(name = "COMERC_REG")
    private String comercReg;
    /**
     *  socialCrdCd 社会信用代码
     */
    @Column(name = "SOCIAL_CRD_CD")
    private String socialCrdCd;
    /**
     *  orgCd 组织机构代码
     */
    @Column(name = "ORG_CD")
    private String orgCd;
    /**
     *  busiLisc 营业执照
     */
    @Column(name = "BUSI_LISC")
    private String busiLisc;
    /**
     *  taxCertId 税务证明
     */
    @Column(name = "TAX_CERT_ID")
    private String taxCertId;
    /**
     *  webNm 网站名称
     */
    @Column(name = "WEB_NM")
    private String webNm;
    /**
     *  webUrl 网站地址
     */
    @Column(name = "WEB_URL")
    private String webUrl;
    /**
     *  merRegAddr 公司注册地址
     */
    @Column(name = "MER_REG_ADDR")
    private String merRegAddr;
    /**
     *  merAddrLongitude 公司地址的所在经度
     */
    @Column(name = "MER_ADDR_LONGITUDE")
    private BigDecimal merAddrLongitude;
    /**
     *  merAddrLatitude 公司地址的所在纬度
     */
    @Column(name = "MER_ADDR_LATITUDE")
    private BigDecimal merAddrLatitude;
    /**
     *  mgtScp 经营范围
     */
    @Column(name = "MGT_SCP")
    private String mgtScp;
    /**
     *  needInvFlg 是否开具发票 Y：需要；N：不需要；
     */
    @Column(name = "NEED_INV_FLG")
    private String needInvFlg;
    /**
     *  invMod 开具发票方式 0 - 按季度开；1 - 按月开；2 –按年开；
     */
    @Column(name = "INV_MOD")
    private String invMod;
    /**
     *  invTit 发票抬头
     */
    @Column(name = "INV_TIT")
    private String invTit;
    /**
     *  invMailAddr 发票邮寄地址
     */
    @Column(name = "INV_MAIL_ADDR")
    private String invMailAddr;
    /**
     *  invMailZip 发票邮寄邮编
     */
    @Column(name = "INV_MAIL_ZIP")
    private String invMailZip;
    /**
     *  mercTrdCls 商户行业类别
     */
    @Column(name = "MERC_TRD_CLS")
    private String mercTrdCls;
    /**
     *  mercTrdDesc 商户行业描述
     */
    @Column(name = "MERC_TRD_DESC")
    private String mercTrdDesc;
    /**
     *  cprTyp 商户类别 01-国有，02-私有，03-外资，04-合资 08-个人，10-公司，11-个人独资
     */
    @Column(name = "CPR_TYP")
    private String cprTyp;
    /**
     *  csTelNo 商户客服电话
     */
    @Column(name = "CS_TEL_NO")
    private String csTelNo;
    /**
     *  mercHotLin 商户热线
     */
    @Column(name = "MERC_HOT_LIN")
    private String mercHotLin;
    /**
     *  cusMgr 客户经理编号
     */
    @Column(name = "CUS_MGR")
    private String cusMgr;
    /**
     *  cusMgrNm 客户经理名称
     */
    @Column(name = "CUS_MGR_NM")
    private String cusMgrNm;
    /**
     *  rcvMagAmt 应收商户保证金
     */
    @Column(name = "RCV_MAG_AMT")
    private BigDecimal rcvMagAmt;
    /**
     *  lastUpdOpr 最后更新柜员
     */
    @Column(name = "LAST_UPD_OPR")
    private String lastUpdOpr;

    //最后修改时间

    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "MODIFY_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;
    //创建时间
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;



    /**
     * opnBusDt 开业日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "OPN_BUS_DT")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate opnBusDt;

    /**
     * refereeMblNo 推荐人手机号
     */
    @Column(name = "REFEREE_MBL_NO")
    private String refereeMblNo;


    /**
     * opnBusDt 期限
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "BUS_LIC_EXP_DT")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate busLicExpDt;

    /**
     * 商户级别
     * @return
     */
    @Column(name = "MER_LVL")
    private String merLvl;

    @Column(name = "BEL_MERC")
    private String belongMerc;

    public String getMerLvl() {
        return merLvl;
    }

    public void setMerLvl(String merLvl) {
        this.merLvl = merLvl;
    }

    public LocalDate getOpnBusDt() {
        return opnBusDt;
    }

    public void setOpnBusDt(LocalDate opnBusDt) {
        this.opnBusDt = opnBusDt;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getMercShortName() {
        return mercShortName;
    }

    public void setMercShortName(String mercShortName) {
        this.mercShortName = mercShortName;
    }

    public String getCprRegNmCn() {
        return cprRegNmCn;
    }

    public void setCprRegNmCn(String cprRegNmCn) {
        this.cprRegNmCn = cprRegNmCn;
    }

    public String getCprOperNmCn() {
        return cprOperNmCn;
    }

    public void setCprOperNmCn(String cprOperNmCn) {
        this.cprOperNmCn = cprOperNmCn;
    }

    public String getPrinNm() {
        return prinNm;
    }

    public void setPrinNm(String prinNm) {
        this.prinNm = prinNm;
    }

    public String getCrpNm() {
        return crpNm;
    }

    public void setCrpNm(String crpNm) {
        this.crpNm = crpNm;
    }

    public String getCrpIdTyp() {
        return crpIdTyp;
    }

    public void setCrpIdTyp(String crpIdTyp) {
        this.crpIdTyp = crpIdTyp;
    }

    public String getCrpIdNo() {
        return crpIdNo;
    }

    public void setCrpIdNo(String crpIdNo) {
        this.crpIdNo = crpIdNo;
    }

    public String getComercReg() {
        return comercReg;
    }

    public void setComercReg(String comercReg) {
        this.comercReg = comercReg;
    }

    public String getSocialCrdCd() {
        return socialCrdCd;
    }

    public void setSocialCrdCd(String socialCrdCd) {
        this.socialCrdCd = socialCrdCd;
    }

    public String getOrgCd() {
        return orgCd;
    }

    public void setOrgCd(String orgCd) {
        this.orgCd = orgCd;
    }

    public String getBusiLisc() {
        return busiLisc;
    }

    public void setBusiLisc(String busiLisc) {
        this.busiLisc = busiLisc;
    }

    public String getTaxCertId() {
        return taxCertId;
    }

    public void setTaxCertId(String taxCertId) {
        this.taxCertId = taxCertId;
    }

    public String getWebNm() {
        return webNm;
    }

    public void setWebNm(String webNm) {
        this.webNm = webNm;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getMerRegAddr() {
        return merRegAddr;
    }

    public void setMerRegAddr(String merRegAddr) {
        this.merRegAddr = merRegAddr;
    }

    public BigDecimal getMerAddrLongitude() {
        return merAddrLongitude;
    }

    public void setMerAddrLongitude(BigDecimal merAddrLongitude) {
        this.merAddrLongitude = merAddrLongitude;
    }

    public BigDecimal getMerAddrLatitude() {
        return merAddrLatitude;
    }

    public void setMerAddrLatitude(BigDecimal merAddrLatitude) {
        this.merAddrLatitude = merAddrLatitude;
    }

    public String getMgtScp() {
        return mgtScp;
    }

    public void setMgtScp(String mgtScp) {
        this.mgtScp = mgtScp;
    }

    public String getNeedInvFlg() {
        return needInvFlg;
    }

    public void setNeedInvFlg(String needInvFlg) {
        this.needInvFlg = needInvFlg;
    }

    public String getInvMod() {
        return invMod;
    }

    public void setInvMod(String invMod) {
        this.invMod = invMod;
    }

    public String getInvTit() {
        return invTit;
    }

    public void setInvTit(String invTit) {
        this.invTit = invTit;
    }

    public String getInvMailAddr() {
        return invMailAddr;
    }

    public void setInvMailAddr(String invMailAddr) {
        this.invMailAddr = invMailAddr;
    }

    public String getInvMailZip() {
        return invMailZip;
    }

    public void setInvMailZip(String invMailZip) {
        this.invMailZip = invMailZip;
    }

    public String getMercTrdCls() {
        return mercTrdCls;
    }

    public void setMercTrdCls(String mercTrdCls) {
        this.mercTrdCls = mercTrdCls;
    }

    public String getMercTrdDesc() {
        return mercTrdDesc;
    }

    public void setMercTrdDesc(String mercTrdDesc) {
        this.mercTrdDesc = mercTrdDesc;
    }

    public String getCprTyp() {
        return cprTyp;
    }

    public void setCprTyp(String cprTyp) {
        this.cprTyp = cprTyp;
    }

    public String getCsTelNo() {
        return csTelNo;
    }

    public void setCsTelNo(String csTelNo) {
        this.csTelNo = csTelNo;
    }

    public String getMercHotLin() {
        return mercHotLin;
    }

    public void setMercHotLin(String mercHotLin) {
        this.mercHotLin = mercHotLin;
    }

    public String getCusMgr() {
        return cusMgr;
    }

    public void setCusMgr(String cusMgr) {
        this.cusMgr = cusMgr;
    }

    public String getCusMgrNm() {
        return cusMgrNm;
    }

    public void setCusMgrNm(String cusMgrNm) {
        this.cusMgrNm = cusMgrNm;
    }

    public BigDecimal getRcvMagAmt() {
        return rcvMagAmt;
    }

    public void setRcvMagAmt(BigDecimal rcvMagAmt) {
        this.rcvMagAmt = rcvMagAmt;
    }

    public String getLastUpdOpr() {
        return lastUpdOpr;
    }

    public void setLastUpdOpr(String lastUpdOpr) {
        this.lastUpdOpr = lastUpdOpr;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getRefereeMblNo() {
        return refereeMblNo;
    }

    public void setRefereeMblNo(String refereeMblNo) {
        this.refereeMblNo = refereeMblNo;
    }

    public LocalDate getBusLicExpDt() {
        return busLicExpDt;
    }

    public void setBusLicExpDt(LocalDate busLicExpDt) {
        this.busLicExpDt = busLicExpDt;
    }

    public String getBelongMerc() {
        return belongMerc;
    }

    public void setBelongMerc(String belongMerc) {
        this.belongMerc = belongMerc;
    }
}