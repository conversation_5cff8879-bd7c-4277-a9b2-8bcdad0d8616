package com.hisun.tms.urm.controller;



import com.hisun.tms.urm.service.MercPayPswdResetService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/busmgr/mercpaypswdresetctrl")
public class MercPayPswdResetController {

    private static final Logger logger = LoggerFactory.getLogger(KeyResetController.class);

    @Autowired
    private MercPayPswdResetService mercPayPswdResetService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercpaypswdreset') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mercctrlinfo/keyform");
        return modelAndView;
    }

    @PostMapping(value = "query")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercpaypswdreset') or hasRole('ROLE_ADMIN')")
    public Map<String,String> getUserInf(@RequestParam(value = "mercId", required = true) String mercId) {
        return mercPayPswdResetService.getUserInf(mercId);
    }
    
    @PostMapping(value = "keyreset")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/mercpaypswdreset') or hasRole('ROLE_ADMIN')")
    public Map<String,String> restPayPswd(@RequestParam(value = "mercId", required = true) String mercId) {
        return mercPayPswdResetService.restPayPswd(mercId);
    }
}
