package com.hisun.tms.urm.controller;

import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.BatchOpenUserDTO;
import com.hisun.tms.urm.model.UserBasicInfo;
import com.hisun.tms.urm.model.UserParamInput;
import com.hisun.tms.urm.service.UserBasicInfoService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/busmgr/userbasicinfoctrl")
public class UserBasicInfoController {

    private static final Logger logger = LoggerFactory.getLogger(UserBasicInfoController.class);

    @Autowired
    private UserBasicInfoService userBasicInfoService;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/userbasicinfo') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/userbasicinfo/basicinfindex");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/userbasicinfo') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<UserBasicInfo> findAll(@Valid @RequestBody UserParamInput input) {
        return userBasicInfoService.findAll(input);
    }
    @PostMapping(value = "cancelUser")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/userbasicinfo') or hasRole('ROLE_ADMIN')")
    public Map<String ,String > cancelUser(  String userId) {
        return userBasicInfoService.cancelUser(userId);
    }

    @GetMapping(value = "/batch")
    @ResponseBody
    public String justDoIt() {
        BatchOpenUserDTO batchOpenUserDTO = new BatchOpenUserDTO();
        batchOpenUserDTO.setLoginPwd("a1234a1234");
        batchOpenUserDTO.setPayPwd("123321");
        batchOpenUserDTO.setSafeAns1("2018");
        batchOpenUserDTO.setSafeQues1("What is your school/work number?");
        GenericRspDTO<BatchOpenUserDTO> genericRspDTO = new GenericRspDTO<>();
        genericRspDTO.setBody(batchOpenUserDTO);
        userBasicInfClient.batchOpenUser(genericRspDTO);
        return "Success";
    }
}
