package com.hisun.tms.urm.repository;

import com.hisun.tms.urm.model.SafeLoginDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Created by chen on 9/2 0002.
 */
public interface SafeLoginRespository extends JpaRepository<SafeLoginDO,String > {
    @Query(value = "select LOGIN_ID ,SAFE_ID ,DISPLAY_NM from urm_safe_login where SAFE_ID = :safeId limit 0,1 ", nativeQuery=true  )
    SafeLoginDO findOneBySafeId( @Param("safeId") String safeId);

    @Modifying
    @Query(value = "update urm_safe_login set LOGIN_ID =:#{#safeLoginDO.loginId} ,DISPLAY_NM = :#{#safeLoginDO.diplayNm}  where SAFE_ID = :#{#safeLoginDO.safeId} ", nativeQuery=true  )
    void updateBySafeLoginDO(@Param("safeLoginDO") SafeLoginDO safeLoginDO);

    @Modifying
    @Query(value = "update urm_safe_login set LOGIN_ID =:loginId ,DISPLAY_NM = :diplayNm  where SAFE_ID = :safeId ", nativeQuery=true  )
    void updateBySafeLoginDO(@Param("loginId")String loginId, @Param("diplayNm")String diplayNm, @Param("safeId")String safeId);

	SafeLoginDO getInfoBySafeId(String safeId);
	
	SafeLoginDO getInfoByLoginId(String loginId);
}
