package com.hisun.tms.urm.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.tms.sys.common.Constants;
import com.hisun.tms.urm.dao.IUrmUserBasicInfDao;
import com.hisun.tms.urm.dao.UserKybSeqDao;
import com.hisun.tms.urm.model.UrmKybCertInf;
import com.hisun.tms.urm.model.UrmKybSeq;
import com.hisun.tms.urm.model.UserKybDetail;
import com.hisun.tms.urm.model.dto.KybAuditReq;
import com.hisun.tms.urm.model.dto.QueryKybDTO;
import com.hisun.tms.urm.model.dto.QueryResultKybDTO;
import com.hisun.tms.urm.repository.UrmKybCertInfRepository;
import com.hisun.tms.urm.repository.UrmKybSeqRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Service
public class UserKybInfoServiceImpl implements UserKybInfoService {

    private static final Logger logger = LoggerFactory.getLogger(UserKybInfoServiceImpl.class);

    private static final ObjectMapper mapper = new ObjectMapper();

    @Resource
    private UrmKybSeqRepository urmKybSeqRepository;

    @Resource
    private UrmKybCertInfRepository urmKybCertInfRepository;

    @Resource
    private IUrmUserBasicInfDao iUrmUserBasicInfDao;

    @Resource
    private UserKybSeqDao userKybSeqDao;

    @Override
    public List<UrmKybSeq> getKybInfo(String userId, String examineStatus) {
        return userKybSeqDao.findByUserIdAndExamineStatus(userId, examineStatus);
    }

    @Override
    public Map<String, String> examine(UrmKybSeq urmKybSeq) {
        Map<String, String> result = new HashMap<String, String>();
        result.put("msgCd", "1");
        String json = "";
        UrmKybSeq examineKyb = new UrmKybSeq();
        try {
            examineKyb = urmKybSeqRepository.findOne(urmKybSeq.getKybId());
            if (Constants.KYB_STATUS_PASS.equals(examineKyb.getExamineStatus())) {
                result.put("msgCd", "0");
                result.put("msgInfo", "该用户已经通过Kyb认证");
                return result;
            }

            examineKyb.setExamineStatus(urmKybSeq.getExamineStatus());
            examineKyb.setExamineNm(urmKybSeq.getExamineNm());
            examineKyb.setRemark(urmKybSeq.getRemark());

            json = examineKyb.getKybInfo();
            json = json.replaceAll("\"examineStatus\":\\s*\".*?\",?", "");

            json = processJson(json);

            if (Constants.KYB_STATUS_REJECT_PASS.equals(urmKybSeq.getExamineStatus())) {
                urmKybSeqRepository.save(examineKyb);
                result.put("msgInfo", "认证不通过");
                return result;
            } else if (Constants.KYB_STATUS_PASS.equals(urmKybSeq.getExamineStatus())) {
                urmKybSeqRepository.save(examineKyb);
                UrmKybCertInf urmKybCertInf = mapper.readValue(json, UrmKybCertInf.class);
                urmKybCertInfRepository.save(urmKybCertInf);
                iUrmUserBasicInfDao.updateUserKybCert(examineKyb.getUserId());
                result.put("msgInfo", "认证通过");
                return result;
            }

        } catch (Exception e) {
            logger.info("更新流水审表失败:{}", e.getMessage());
            result.put("msgCd", "0");
            result.put("msgInfo", "审核失败");
            return result;
        }
        return result;
    }

    @Override
    public UserKybDetail getKybDetail(String userId) {
        UrmKybSeq seq = userKybSeqDao.findByUserId(userId);
        String kybJson;
        if (seq == null) {
            return null;
        }
        try {
            kybJson = seq.getKybInfo();
            UserKybDetail result = new ObjectMapper().readValue(kybJson, UserKybDetail.class);
            result.setExamineStatus(seq.getExamineStatus());
            logger.info(result.toString());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<QueryResultKybDTO.QueryKyb> queryKybAudit(QueryKybDTO queryDTO) {
        UrmKybSeq urmKybSeq = new UrmKybSeq();
        Integer pageSize = queryDTO.getPageSize();
        Integer pageNum = queryDTO.getPageNum();
        urmKybSeq.setPageNum(pageNum);
        urmKybSeq.setPageSize(pageSize);
        urmKybSeq.setExamineStatus(queryDTO.getExamineStatus());

        if (queryDTO.getUserId() != null && !queryDTO.getUserId().isEmpty()) {
            urmKybSeq.setUserId(queryDTO.getUserId());
        }

        // 查询KYB审核列表
        List<UrmKybSeq> kybSeqList = null;
        if ("0".equals(queryDTO.getAuditFlag())) {
            // 未初审的数据
            kybSeqList = this.userKybSeqDao.findFirstAudit(urmKybSeq);
        } else {
            // 已初审的数据
            kybSeqList = this.userKybSeqDao.findSecondAudit(urmKybSeq);
        }

        // 将kybSeqList转换为QueryKyb列表
        List<QueryResultKybDTO.QueryKyb> queryList = new ArrayList<>();
        if (kybSeqList != null && !kybSeqList.isEmpty()) {
            for (UrmKybSeq kybSeq : kybSeqList) {
                QueryResultKybDTO.QueryKyb queryKyb = new QueryResultKybDTO.QueryKyb();
                BeanUtils.copyProperties(kybSeq, queryKyb);
                queryList.add(queryKyb);
            }
        }

        return queryList;
    }

    @Override
    public UserKybDetail detail(String userId, Integer kybId) {
        UrmKybSeq kybSeq = null;
        if (kybId != null) {
            kybSeq = this.userKybSeqDao.get(kybId);
        } else if (userId != null && !userId.isEmpty()) {
            kybSeq = this.userKybSeqDao.findByUserId(userId);
        }

        if (kybSeq == null) {
            logger.error("KYB信息不存在");
            return null;
        }

        try {
            String kybJson = kybSeq.getKybInfo();
            UserKybDetail detail = new ObjectMapper().readValue(kybJson, UserKybDetail.class);
            
            // 设置基本信息
            detail.setKybId(kybSeq.getKybId());
            detail.setUserId(kybSeq.getUserId());
            detail.setExamineStatus(kybSeq.getExamineStatus());
            detail.setCreateTime(kybSeq.getCreateTime());
            
            // 设置初审信息
            detail.setFirstAuditUser(kybSeq.getFirstAuditUser());
            detail.setFirstAuditTime(kybSeq.getFirstAuditTime());
            detail.setFirstAuditOpinion(kybSeq.getFirstAuditOpinion());
            
            return detail;
        } catch (Exception e) {
            logger.error("解析KYB信息失败", e);
            return null;
        }
    }

    @Override
    public void kybAudit(KybAuditReq req) {
        // 根据ID查询KYB信息
        Integer kybId = req.getKybId();
        UrmKybSeq kybSeq = this.userKybSeqDao.get(kybId);
        if (kybSeq == null) {
            logger.error("KYB信息不存在,ID:{}", kybId);
            throw new RuntimeException("KYB信息不存在");
        }

        // 检查审核状态
        if (!Constants.KYB_STATUS_PENDING.equals(kybSeq.getExamineStatus())
                && !Constants.KYB_STATUS_FIRST_AUDIT.equals(kybSeq.getExamineStatus())
                && !Constants.KYB_STATUS_SECOND_AUDIT.equals(kybSeq.getExamineStatus())) {
            logger.error("KYB信息已审核,ID:{}", kybId);
            throw new RuntimeException("KYB信息已审核");
        }

        // 获取当前用户信息
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String userId = String.valueOf(user.getUsername());

        // 初审（待审核/初审中）
        if (req.getAuditTimes().equals(0) && (Constants.KYB_STATUS_PENDING.equals(kybSeq.getExamineStatus())
                || Constants.KYB_STATUS_FIRST_AUDIT.equals(kybSeq.getExamineStatus()))) {
            // 审核拒绝
            if (req.getAuditResult() == 1) {
                kybSeq.setExamineStatus(Constants.KYB_STATUS_REJECTED);
                kybSeq.setFirstAuditOpinion(req.getAuditOpinion());
                kybSeq.setRejectReason(req.getRejectReason());
                kybSeq.setFirstAuditTime(new Date());
                kybSeq.setFirstAuditResult(Constants.AUDIT_RESULT_REJECTED);
                kybSeq.setFirstAuditUser(userId);
            } else {
                // 审核通过
                kybSeq.setExamineStatus(Constants.KYB_STATUS_SECOND_AUDIT);
                kybSeq.setFirstAuditOpinion(req.getAuditOpinion());
                kybSeq.setFirstAuditTime(new Date());
                kybSeq.setFirstAuditResult(Constants.AUDIT_RESULT_APPROVED);
                kybSeq.setFirstAuditUser(userId);
            }
        } else if (req.getAuditTimes().equals(1) && Constants.KYB_STATUS_SECOND_AUDIT.equals(kybSeq.getExamineStatus())
                && Constants.AUDIT_RESULT_APPROVED.equals(kybSeq.getFirstAuditResult())) {
            // 复核（复核中）
            // 检查初审人和复核人不能是同一人
//            if (userId.equals(kybSeq.getFirstAuditUser())) {
//                logger.error("初审人和复核人不能是同一人");
//                throw new RuntimeException("初审人和复核人不能是同一人");
//            }

            if (req.getAuditResult() == 1) {
                // 审核拒绝
                kybSeq.setExamineStatus(Constants.KYB_STATUS_REJECTED);
                kybSeq.setSecondAuditOpinion(req.getAuditOpinion());
                kybSeq.setRejectReason(req.getRejectReason());
                kybSeq.setSecondAuditTime(new Date());
                kybSeq.setSecondAuditResult(Constants.AUDIT_RESULT_REJECTED);
                kybSeq.setSecondAuditUser(userId);
            } else {
                // 审核通过
                kybSeq.setExamineStatus(Constants.KYB_STATUS_APPROVED);
                kybSeq.setSecondAuditOpinion(req.getAuditOpinion());
                kybSeq.setSecondAuditTime(new Date());
                kybSeq.setSecondAuditResult(Constants.AUDIT_RESULT_APPROVED);
                kybSeq.setSecondAuditUser(userId);
                kybSeq.setExecuteTime(new Date());

                // 审核通过后，将KYB信息保存到UrmKybCertInf表
                try {
                    String json = kybSeq.getKybInfo();
                    json = processJson(json);
                    UrmKybCertInf urmKybCertInf = mapper.readValue(json, UrmKybCertInf.class);
                    urmKybCertInfRepository.save(urmKybCertInf);
                    // 更新用户KYB认证状态
                    iUrmUserBasicInfDao.updateUserKybCert(kybSeq.getUserId());
                } catch (Exception e) {
                    logger.error("保存KYB信息失败", e);
                    throw new RuntimeException("保存KYB信息失败");
                }
            }
        } else {
            logger.error("KYB信息审核状态错误,ID:{}", kybId);
            throw new RuntimeException("KYB信息审核状态错误");
        }

        // 更新KYB信息
        kybSeq.setModifyTime(new Date());
        userKybSeqDao.update(kybSeq);
    }

    public static String processJson(String originalJson) throws IOException {
        // 解析原JSON为JsonNode
        JsonNode kybJson = mapper.readTree(originalJson);

        // 移除examineStatus字段，因为UrmKybCertInf中没有这个字段
        if (kybJson.has("examineStatus")) {
            ((com.fasterxml.jackson.databind.node.ObjectNode) kybJson).remove("examineStatus");
        }

        // 需要处理的数组字段名（与实体类List<String>字段对应）
        String[] listFields = {
                "certificateOfIncorporationCi",
                "businessRegistrationBr",
                "nnc1Nar1",
                "articleOfAssociation",
                "structureOfMembers",
                "ceoIdentification",
                "additional",
                "ultimateBeneficialOwners"
        };

        // 处理普通List<String>对应的数组字段
        for (String field : listFields) {
            if (kybJson.has(field) && kybJson.get(field).isArray()) {
                // 将数组转为JSON字符串（如["a","b"] → "[\"a\",\"b\"]"）
                String arrayStr = mapper.writeValueAsString(kybJson.get(field));
                // 替换原数组为字符串
                ((com.fasterxml.jackson.databind.node.ObjectNode) kybJson).put(field, arrayStr);
            }
        }

        // 特殊处理对象数组字段ultimateBeneficialOwners
        if (kybJson.has("ultimateBeneficialOwners") && kybJson.get("ultimateBeneficialOwners").isArray()) {
            String uboFields = "ultimateBeneficialOwnersFilePath";
            if (kybJson.has(uboFields) && kybJson.get(uboFields).isArray()) {
                // 将数组转为JSON字符串（如["a","b"] → "[\"a\",\"b\"]"）
                String arrayStr = mapper.writeValueAsString(kybJson.get(uboFields));
                // 替换原数组为字符串
                ((com.fasterxml.jackson.databind.node.ObjectNode) kybJson).put("ultimateBeneficialOwners", arrayStr);
            }
            // String uboStr =
            // mapper.writeValueAsString(kybJson.get("ultimateBeneficialOwners"));
            // ((com.fasterxml.jackson.databind.node.ObjectNode)
            // kybJson).put("ultimateBeneficialOwners", uboStr);
        }

        // 转换回JSON字符串
        return mapper.writeValueAsString(kybJson);
    }
}
