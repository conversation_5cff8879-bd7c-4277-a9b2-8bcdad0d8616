package com.hisun.tms.urm.service;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.client.UserPasswordClient;
import com.hisun.tms.common.email.Email;
import com.hisun.tms.common.email.EmailService;
import com.hisun.tms.urm.model.SafeLoginDO;
import com.hisun.tms.urm.model.UserSafeInfo;
import com.hisun.tms.urm.repository.MercSafelInfoRepository;
import com.hisun.tms.urm.repository.SafeLoginRespository;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("mercKeyResetService")
public class MercKeyResetServiceImpl implements MercKeyResetService {

    private static final Logger logger = LoggerFactory.getLogger(MercKeyResetServiceImpl.class);
    
    @Autowired
    private SafeLoginRespository safeLoginRespository;
    @Autowired
    private MercSafelInfoRepository mercSafelInfoRepository;
    @Autowired
    private UserPasswordClient userPasswordClient;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private EmailService emailService;
    
    
	@Override
	public Map<String, String> getUserInf(String userId) {
		UserSafeInfo userSafeInfo=mercSafelInfoRepository.getInfoByUserId(userId);
		SafeLoginDO safeLoginInfo=safeLoginRespository.getInfoBySafeId(userSafeInfo.getSafeId());
		Map<String,String> map =  new HashMap<String,String>();
		map.put("usrNm", safeLoginInfo.getDiplayNm());
		map.put("email", userSafeInfo.getEmail());
		return map;
	}

	@Override
	public Map<String,String> keyReset(String userId,String usrNm,String usremail){
		GenericRspDTO<String> genericRspDTO=userPasswordClient.resetCprTradingKey(userId);
		Map<String,String> map =  new HashMap<String,String>();
		map.put("msgCd", genericRspDTO.getMsgCd());
		map.put("msgInfo", genericRspDTO.getMsgInfo());
		if (JudgeUtils.isSuccess(genericRspDTO.getMsgCd())){
			//TODO 给用户发邮件
			Locale locale = LocaleContextHolder.getLocale();
			String subject = messageSource.getMessage("email.resetkey.subject", null, locale);
	        String name = usrNm;
	        String content = messageSource.getMessage("email.resetkey.content", null, locale)+genericRspDTO.getBody();
	        String end = messageSource.getMessage("email.resetkey.end", null, locale);
	        Email email = new Email();
	        email.setSubject(subject);
	        email.setTo(new String[]{usremail});
	        email.setTemplate("email/resetkey");

	        Map<String, Object> variables = new HashMap<>();
	        variables.put("name", name);
	        variables.put("content", content);
	        variables.put("end", end);
	        logger.info("subject: {}", subject);
	        logger.info("name: {}", name);
	        logger.info("content: {}", content);
	        logger.info("end: {}", end);
	        try{
	        	emailService.sendEmail(email, null, variables, locale);
	        }catch(Exception e){
	        	map.put("msgCd", e.getMessage());
				map.put("msgInfo", e.getLocalizedMessage());
	        }
	        
		}
		return map;
	}

}
