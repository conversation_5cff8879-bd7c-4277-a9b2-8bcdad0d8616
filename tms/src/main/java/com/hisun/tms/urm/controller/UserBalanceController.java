package com.hisun.tms.urm.controller;

import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.urm.model.UrmUserBalanceDO;
import com.hisun.tms.urm.service.UserBalanceService;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Created by chen on 8/29 0029.
 */
@Controller
@RequestMapping("/urm")
public class UserBalanceController {

    @Resource
    private UserBalanceService userBalanceService;

    @GetMapping(value = "/queryBalanceView")
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/usrBalance') or hasRole('ROLE_ADMIN')")
    public ModelAndView releaseDetail() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/accquery/accbalance");
        return modelAndView;
    }

    @PostMapping(value = "/queryUserBalance")
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/usrBalance') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<UrmUserBalanceDO> queryUserBalance(@Valid @RequestBody MartketFindInput input) {

        return userBalanceService.queryUserBalance(input);
    }
}
