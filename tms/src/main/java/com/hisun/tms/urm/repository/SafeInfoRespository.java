package com.hisun.tms.urm.repository;

import com.hisun.tms.urm.model.SafeInfoDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Created by chen on 9/2 0002.
 */
public interface SafeInfoRespository extends JpaRepository <SafeInfoDO,String >{
    @Query(value = "select * from urm_safe_inf where user_id = :mercId and OPR_TYP = :oprTyp order by CREATE_TIME ASC limit 0,1 ", nativeQuery=true  )
    SafeInfoDO byMercIdAndOprTyp(@Param("mercId") String mercId ,@Param("oprTyp") String oprTyp );

}
