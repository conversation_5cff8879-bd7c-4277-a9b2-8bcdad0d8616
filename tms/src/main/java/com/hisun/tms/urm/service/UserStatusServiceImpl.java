package com.hisun.tms.urm.service;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.rsm.model.RsmUserStatusModel;
import com.hisun.tms.rsm.repository.RsmUserStatusRepository;
import com.hisun.tms.urm.model.UserStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/10/30
 */
@Service
public class UserStatusServiceImpl implements UserStatusService {

    @Resource
    private RsmUserStatusRepository rsmUserStatusRepository;

    @Resource
    private GetOpr getOpr;

    @Override
    public UserStatus search(String userId) {
        UserStatus userStatus = new UserStatus();
        try {
            RsmUserStatusModel rsmUserStatusModel = rsmUserStatusRepository.findOne(userId);
            if (rsmUserStatusModel != null && rsmUserStatusModel.getUserId() != null) {
                userStatus.setUserSts(rsmUserStatusModel.getUserSts());
            } else {
                userStatus.setUserSts("0");
            }
        } catch (Exception e) {
            userStatus.setUserSts("0");
        }
        return userStatus;
    }

    @Override
    public void save(String userId, String sts) {
        RsmUserStatusModel rsmUserStatusModel;
        rsmUserStatusModel = rsmUserStatusRepository.findOne(userId);
        if (rsmUserStatusModel == null) {
            rsmUserStatusModel = new RsmUserStatusModel();
            rsmUserStatusModel.setCreateTime(LocalDateTime.now());
        }
        rsmUserStatusModel.setUserSts(sts);
        rsmUserStatusModel.setUserId(userId);
        rsmUserStatusModel.setUpdOprId(getOpr.getOperatorName());
        rsmUserStatusModel.setModifyTime(LocalDateTime.now());
        rsmUserStatusRepository.save(rsmUserStatusModel);
    }
}
