package com.hisun.tms.urm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Created by chen on 9/2 0002.
 */

@Entity
@Table(name = "urm_safe_login")
public class SafeLoginDO {

    @Column(name = "SAFE_ID")
    private String safeId ;

    @Column(name = "DISPLAY_NM")
    private String diplayNm ;

    @Id
    @Column(name = "LOGIN_ID")
    private String loginId ;

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getDiplayNm() {
        return diplayNm;
    }

    public void setDiplayNm(String diplayNm) {
        this.diplayNm = diplayNm;
    }
}
