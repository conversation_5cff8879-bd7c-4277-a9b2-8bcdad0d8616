package com.hisun.tms.urm.model;



import com.hisun.tms.common.audit.AbstractEntityTmpe;


import javax.persistence.*;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "urm_safe_inf")
public class UserSafeInfo extends AbstractEntityTmpe {
	/**
     * @Fields 用户号
     */
	@Id
    @Column(name = "user_id")
    private String userId;
	/**
	 * @Fields 安全ID
	 */
	@Column(name = "safe_id")
    private String safeId;
	/**
     * @Fields 手机号
     */
    @Column(name = "mbl_no")
    private String mblNo;
	/**
     * @Fields 邮箱
     */
	@Column(name = "email")
    private String email;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getSafeId() {
		return safeId;
	}

	public void setSafeId(String safeId) {
		this.safeId = safeId;
	}

	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Override
    public String toString() {
        return "UserSafeInfo{" +
                "userId='" + userId + '\'' +
                "mblNo='" + mblNo + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
