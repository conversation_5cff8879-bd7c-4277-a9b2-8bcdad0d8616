package com.hisun.tms.urm.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * KYB审核请求DTO
 */
@ApiModel("KYB审核请求")
public class KybAuditReq {

    @ApiModelProperty(name = "kybId", value = "KYB审核ID")
    private Integer kybId;

    @ApiModelProperty(name = "auditTimes", value = "审核次数(0:初审,1:复核)")
    private Integer auditTimes;

    @ApiModelProperty(name = "auditResult", value = "审核结果(0:审核通过-APPROVED,1:审核拒绝-REJECTED)")
    private Integer auditResult;

    @ApiModelProperty(name = "auditOpinion", value = "审核意见")
    private String auditOpinion;

    @ApiModelProperty(name = "rejectReason", value = "拒绝原因")
    private String rejectReason;

    public Integer getKybId() {
        return kybId;
    }

    public void setKybId(Integer kybId) {
        this.kybId = kybId;
    }

    public Integer getAuditTimes() {
        return auditTimes;
    }

    public void setAuditTimes(Integer auditTimes) {
        this.auditTimes = auditTimes;
    }

    public Integer getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(Integer auditResult) {
        this.auditResult = auditResult;
    }

    public String getAuditOpinion() {
        return auditOpinion;
    }

    public void setAuditOpinion(String auditOpinion) {
        this.auditOpinion = auditOpinion;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }
}