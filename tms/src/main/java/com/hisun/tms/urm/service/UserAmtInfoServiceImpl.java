package com.hisun.tms.urm.service;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import com.hisun.tms.acm.model.AcmAcBal;
import com.hisun.tms.acm.model.AcmAcDetail;
import com.hisun.tms.acm.repository.AcmAcBalRepository;
import com.hisun.tms.acm.repository.DataTablesAcmAcDetailRepository;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.urm.model.UserParamInput;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.mapping.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/8/30
 */
@Service
public class UserAmtInfoServiceImpl implements UserAmtInfoService {

    private static final Logger logger = LoggerFactory.getLogger(UserAmtInfoService.class);

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Resource
    private AcmAcBalRepository acmAcBalRepository;

    @Resource
    private DataTablesAcmAcDetailRepository acmAcDetailRepository;

    @Override
    public DataTablesOutput<AcmAcDetail> findAll(UserParamInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        String areaCore = dataTablesInput.getExtra_search().get("areaCore").trim();
        String mblNo = dataTablesInput.getExtra_search().get("mblNo").trim();
        String txTyp = dataTablesInput.getExtra_search().get("txTyp").trim();
        String begDt = dataTablesInput.getExtra_search().get("begDt").trim();
        String endDt = dataTablesInput.getExtra_search().get("endDt").trim();
        DataTablesOutput<AcmAcDetail> dataTablesOutput = new DataTablesOutput<>();
        GenericRspDTO<UserBasicInfDTO> genericRspDTO;
        String acNo = null;
        if (JudgeUtils.isNotBlank(mblNo)) {
            genericRspDTO = userBasicInfClient.queryUserByLoginId(areaCore + mblNo);
            if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
                dataTablesOutput.setError(genericRspDTO.getMsgCd() + " : The user does not exist.");
                return dataTablesOutput;
            }

            UserBasicInfDTO userBasicInfDTO = genericRspDTO.getBody();
            List<AcmAcBal> list = acmAcBalRepository.findAllById(userBasicInfDTO.getUserId());
            acNo = list.get(0).getAC_NO();
        }

        LocalDate beginDate = null;
        LocalDate endDate = null;
        if (JudgeUtils.isNotBlank(begDt)){
            begDt = begDt.replaceAll("-","").trim();
            beginDate = DateTimeUtils.parseLocalDate(begDt);
        }
        if (JudgeUtils.isNotBlank(endDt)){
            endDt = endDt.replaceAll("-","").trim();
            endDate = DateTimeUtils.parseLocalDate(endDt);
        }

        if (JudgeUtils.isNull(beginDate) && JudgeUtils.isNotNull(endDate)) {
            beginDate = LocalDate.of(2000, 1, 1);
        }
        if (JudgeUtils.isNotNull(beginDate) && JudgeUtils.isNull(endDate)) {
            endDate = LocalDate.of(2999, 12, 31);
        }


        Specification<AcmAcDetail> orderQueryParam = Specifications.<AcmAcDetail>and()
                .eq(JudgeUtils.isNotBlank(acNo), "acNo", acNo)
                .eq(JudgeUtils.isNotBlank(txTyp), "txTyp", txTyp)
                .between(!JudgeUtils.isNullAny(beginDate, endDate), "txOrdDt", new Range<>(beginDate, endDate))
                .eq("capTyp", "1")
                .build();

        Order order0 = new Order(9, "desc");
        Order order1 = new Order(10, "desc");
        List<Order> orders = new ArrayList<>();
        orders.add(order0);
        orders.add(order1);
        dataTablesInput.setOrder(orders);
        dataTablesOutput = acmAcDetailRepository.findAll(dataTablesInput, orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }
}
