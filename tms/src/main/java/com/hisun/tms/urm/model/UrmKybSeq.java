package com.hisun.tms.urm.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "urm_kyb_seq")
public class UrmKybSeq {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "kyb_id", nullable = false)
    private Integer kybId;

    @Column(name = "create_time", nullable = false)
    private Date createTime;

    @Column(name = "modify_time", nullable = false)
    private Date modifyTime;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "examine_nm")
    private String examineNm;

    @Column(name = "examine_status")
    private String examineStatus;

    @Column(name = "first_audit_user")
    private String firstAuditUser;

    @Column(name = "first_audit_time")
    private Date firstAuditTime;

    @Column(name = "first_audit_result")
    private String firstAuditResult;

    @Column(name = "first_audit_opinion")
    private String firstAuditOpinion;

    @Column(name = "second_audit_user")
    private String secondAuditUser;

    @Column(name = "second_audit_time")
    private Date secondAuditTime;

    @Column(name = "second_audit_result")
    private String secondAuditResult;

    @Column(name = "second_audit_opinion")
    private String secondAuditOpinion;

    @Column(name = "execute_time")
    private Date executeTime;

    @Column(name = "reject_reason")
    private String rejectReason;

    @Column(name = "kyb_info", columnDefinition = "text")
    private String kybInfo;

    @Column(name = "remark")
    private String remark;

    /**
     * 分页参数，非数据库字段
     */
    @Transient
    private Integer pageNum;

    /**
     * 分页参数，非数据库字段
     */
    @Transient
    private Integer pageSize;

    public Integer getKybId() {
        return kybId;
    }

    public void setKybId(Integer kybId) {
        this.kybId = kybId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getExamineNm() {
        return examineNm;
    }

    public void setExamineNm(String examineNm) {
        this.examineNm = examineNm;
    }

    public String getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(String examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getFirstAuditUser() {
        return firstAuditUser;
    }

    public void setFirstAuditUser(String firstAuditUser) {
        this.firstAuditUser = firstAuditUser;
    }

    public Date getFirstAuditTime() {
        return firstAuditTime;
    }

    public void setFirstAuditTime(Date firstAuditTime) {
        this.firstAuditTime = firstAuditTime;
    }

    public String getFirstAuditResult() {
        return firstAuditResult;
    }

    public void setFirstAuditResult(String firstAuditResult) {
        this.firstAuditResult = firstAuditResult;
    }

    public String getFirstAuditOpinion() {
        return firstAuditOpinion;
    }

    public void setFirstAuditOpinion(String firstAuditOpinion) {
        this.firstAuditOpinion = firstAuditOpinion;
    }

    public String getSecondAuditUser() {
        return secondAuditUser;
    }

    public void setSecondAuditUser(String secondAuditUser) {
        this.secondAuditUser = secondAuditUser;
    }

    public Date getSecondAuditTime() {
        return secondAuditTime;
    }

    public void setSecondAuditTime(Date secondAuditTime) {
        this.secondAuditTime = secondAuditTime;
    }

    public String getSecondAuditResult() {
        return secondAuditResult;
    }

    public void setSecondAuditResult(String secondAuditResult) {
        this.secondAuditResult = secondAuditResult;
    }

    public String getSecondAuditOpinion() {
        return secondAuditOpinion;
    }

    public void setSecondAuditOpinion(String secondAuditOpinion) {
        this.secondAuditOpinion = secondAuditOpinion;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getKybInfo() {
        return kybInfo;
    }

    public void setKybInfo(String kybInfo) {
        this.kybInfo = kybInfo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
