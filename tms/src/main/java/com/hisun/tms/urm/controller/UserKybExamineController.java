package com.hisun.tms.urm.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.tms.urm.model.UrmKybSeq;
import com.hisun.tms.urm.model.UserKybDetail;
import com.hisun.tms.urm.model.dto.KybAuditReq;
import com.hisun.tms.urm.model.dto.KybDetailReq;
import com.hisun.tms.urm.model.dto.QueryKybDTO;
import com.hisun.tms.urm.model.dto.QueryResultKybDTO;
import com.hisun.tms.urm.service.UserKybInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Api(value = "处理KYB审核")
@Controller
@RequestMapping("/urm/mermgr/kyb")
@PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
public class UserKybExamineController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(UserKybExamineController.class);

    @Resource
    UserKybInfoService userKybInfoService;

    @Resource
    private ObjectMapper objectMapper;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/kyb");
        return modelAndView;
    }

    /**
     * 获取KYB信息列表（旧方法，保留兼容）
     */
    @PostMapping(value = "kybList")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
    public List<UrmKybSeq> getKyb(@Valid @RequestParam(value = "userId", required = false) String userId,
            @Valid @RequestParam(value = "examineStatus", required = false) String examineStatus) {
        return userKybInfoService.getKybInfo(userId, examineStatus);
    }

    /**
     * KYB审核（旧方法，保留兼容）
     */
    @PostMapping(value = "kybExamine")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
    public Map<String, String> kybExamine(@Valid @RequestBody UrmKybSeq urmKybSeq) throws JsonProcessingException {
        logger.info("-----" + objectMapper.writeValueAsString(urmKybSeq));
        return userKybInfoService.examine(urmKybSeq);
    }

    /**
     * 获取KYB详情（旧方法，保留兼容）
     */
    @PostMapping(value = "detail")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
    public UserKybDetail getKybDetail(@Valid @RequestParam(value = "userId") String userId) {
        return userKybInfoService.getKybDetail(userId);
    }

    /**
     * 获取KYB审核列表
     */
    @ResponseBody
    @PostMapping(value = "/audit/list")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "获取KYB审核列表")
    @ApiOperation(value = "获取KYB审核列表", notes = "获取KYB审核列表")
    public GenericRspDTO<QueryResultKybDTO> queryKybAudit(@RequestBody GenericDTO<QueryKybDTO> req) {
        QueryKybDTO queryDTO = req.getBody();
        // 设置auditFlag为0，表示查询未初审的数据
        queryDTO.setAuditFlag("0");
        List<QueryResultKybDTO.QueryKyb> queryList = this.userKybInfoService.queryKybAudit(queryDTO);
        QueryResultKybDTO query = new QueryResultKybDTO();
        query.setQueryKybList(queryList);
        GenericRspDTO<QueryResultKybDTO> dto = new GenericRspDTO<>();
        dto.setBody(query);
        return dto;
    }

    /**
     * 查询KYB详情
     */
    @ResponseBody
    @PostMapping(value = "/audit/detail")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "查询KYB详情")
    @ApiOperation(value = "查询KYB详情", notes = "查询KYB详情")
    public GenericRspDTO<UserKybDetail> queryKybDetail(@RequestBody GenericDTO<KybDetailReq> req) {
        String userId = req.getBody().getUserId();
        Integer kybId = req.getBody().getKybId();
        UserKybDetail kybDetail = userKybInfoService.detail(userId, kybId);
        GenericRspDTO<UserKybDetail> dto = new GenericRspDTO<>();
        dto.setBody(kybDetail);
        return dto;
    }

    /**
     * 审核KYB
     */
    @ResponseBody
    @PostMapping(value = "/audit")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "审核KYB")
    @ApiOperation(value = "审核KYB", notes = "审核KYB")
    public GenericRspDTO<NoBody> auditKyb(@RequestBody GenericDTO<KybAuditReq> req) {
        KybAuditReq kybAuditReq = req.getBody();
        userKybInfoService.kybAudit(kybAuditReq);
        return new GenericRspDTO<>();
    }

    /**
     * 返回KYB审核管理页面
     */
    @RequestMapping(value = "/audit/page")
    @PreAuthorize("hasPermission('','/busmgr/mermgr/kyb') or hasRole('ROLE_ADMIN')")
    public ModelAndView auditPage() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/kyb/audit");
        return modelAndView;
    }
}
