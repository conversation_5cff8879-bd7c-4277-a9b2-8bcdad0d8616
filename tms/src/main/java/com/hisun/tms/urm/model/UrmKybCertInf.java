package com.hisun.tms.urm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "urm_kyb_cert_inf")
public class UrmKybCertInf {

    /**
     * 内部用户号（主键）
     */
    @Id
    @Column(name = "uesr_id", nullable = false, length = 255)
    private String userId;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time", nullable = false)
    private Date modifyTime;

    /**
     * 注册国家/地区
     */
    @Column(name = "regist_region", nullable = false, length = 20)
    private String registRegion;

    /**
     * 企业全称中文
     */
    @Column(name = "corp_full_name_cn", length = 40)
    private String corpFullNameCn;

    /**
     * 企业全称英文
     */
    @Column(name = "corp_full_name_eng", nullable = false, length = 50)
    private String corpFullNameEng;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name", nullable = false, length = 50)
    private String contactName;

    /**
     * 联系人邮箱
     */
    @Column(name = "contact_email", nullable = false, length = 50)
    private String contactEmail;

    /**
     * 联系人电话及区号
     */
    @Column(name = "contact_number", length = 20)
    private String contactNumber;

    /**
     * 企业法定性质
     */
    @Column(name = "entity_legal_form", nullable = false, length = 50)
    private String entityLegalForm;

    /**
     * 企业业务性质
     */
    @Column(name = "entity_business_form", nullable = false, length = 50)
    private String entityBusinessForm;

    /**
     * 企业网站
     */
    @Column(name = "web_url", length = 100)
    private String webUrl;

    /**
     * 注册日期
     */
    @Column(name = "regist_time", nullable = false)
    private Date registTime;

    /**
     * 注册地址
     */
    @Column(name = "regist_addr", nullable = false, length = 200)
    private String registAddr;

    /**
     * 实际地址
     */
    @Column(name = "actual_addr", length = 200)
    private String actualAddr;

    /**
     * 注册号码
     */
    @Column(name = "regist_code", nullable = false, length = 50)
    private String registCode;

    /**
     * 公司注册证明书CI
     */
    @Column(name = "certificate_of_incorporation_ci", nullable = false, columnDefinition = "text")
    private String certificateOfIncorporationCi;

    /**
     * 商业登记证BR
     */
    @Column(name = "business_registration_br", nullable = false, columnDefinition = "text")
    private String businessRegistrationBr;

    /**
     * NNC1/NAR1
     */
    @Column(name = "nnc1_nar1", nullable = false, columnDefinition = "text")
    private String nnc1Nar1;

    /**
     * 公司章程
     */
    @Column(name = "article_of_association", columnDefinition = "text")
    private String articleOfAssociation;

    /**
     * 股权证明
     */
    @Column(name = "structure_of_members", columnDefinition = "text")
    private String structureOfMembers;

    /**
     * 董事/同等职位人员
     */
    @Column(name = "ceo_identification", nullable = false, columnDefinition = "text")
    private String ceoIdentification;

    /**
     * 实际受益人
     */
    @Column(name = "ultimate_beneficial_owners", columnDefinition = "text")
    private String ultimateBeneficialOwners;


    /**
     * 企业主要业务描述
     */
    @Column(name = "business_desc", nullable = false, length = 200)
    private String businessDesc;

    /**
     * 预计月交易额
     */
    @Column(name = "expected_month_amt", nullable = false, precision = 18, scale = 2)
    private BigDecimal expectedMonthAmt;

    /**
     * 主要资金来源
     */
    @Column(name = "source_of_fund", nullable = false, length = 100)
    private String sourceOfFund;

    /**
     * 是否存在敏感身份
     */
    @Column(name = "is_sensitive", nullable = false, length = 2)
    private String isSensitive;

    /**
     * 附加说明/补充材料
     */
    @Column(name = "additional", columnDefinition = "text")
    private String additional;

    public UrmKybCertInf() {}

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRegistRegion() {
        return registRegion;
    }

    public void setRegistRegion(String registRegion) {
        this.registRegion = registRegion;
    }

    public String getCorpFullNameCn() {
        return corpFullNameCn;
    }

    public void setCorpFullNameCn(String corpFullNameCn) {
        this.corpFullNameCn = corpFullNameCn;
    }

    public String getCorpFullNameEng() {
        return corpFullNameEng;
    }

    public void setCorpFullNameEng(String corpFullNameEng) {
        this.corpFullNameEng = corpFullNameEng;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getEntityLegalForm() {
        return entityLegalForm;
    }

    public void setEntityLegalForm(String entityLegalForm) {
        this.entityLegalForm = entityLegalForm;
    }

    public String getEntityBusinessForm() {
        return entityBusinessForm;
    }

    public void setEntityBusinessForm(String entityBusinessForm) {
        this.entityBusinessForm = entityBusinessForm;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public Date getRegistTime() {
        return registTime;
    }

    public void setRegistTime(Date registTime) {
        this.registTime = registTime;
    }

    public String getRegistAddr() {
        return registAddr;
    }

    public void setRegistAddr(String registAddr) {
        this.registAddr = registAddr;
    }

    public String getActualAddr() {
        return actualAddr;
    }

    public void setActualAddr(String actualAddr) {
        this.actualAddr = actualAddr;
    }

    public String getRegistCode() {
        return registCode;
    }

    public void setRegistCode(String registCode) {
        this.registCode = registCode;
    }

    public String getCertificateOfIncorporationCi() {
        return certificateOfIncorporationCi;
    }

    public void setCertificateOfIncorporationCi(String certificateOfIncorporationCi) {
        this.certificateOfIncorporationCi = certificateOfIncorporationCi;
    }

    public String getBusinessRegistrationBr() {
        return businessRegistrationBr;
    }

    public void setBusinessRegistrationBr(String businessRegistrationBr) {
        this.businessRegistrationBr = businessRegistrationBr;
    }

    public String getNnc1Nar1() {
        return nnc1Nar1;
    }

    public void setNnc1Nar1(String nnc1Nar1) {
        this.nnc1Nar1 = nnc1Nar1;
    }

    public String getArticleOfAssociation() {
        return articleOfAssociation;
    }

    public void setArticleOfAssociation(String articleOfAssociation) {
        this.articleOfAssociation = articleOfAssociation;
    }

    public String getStructureOfMembers() {
        return structureOfMembers;
    }

    public void setStructureOfMembers(String structureOfMembers) {
        this.structureOfMembers = structureOfMembers;
    }

    public String getCeoIdentification() {
        return ceoIdentification;
    }

    public void setCeoIdentification(String ceoIdentification) {
        this.ceoIdentification = ceoIdentification;
    }

    public String getUltimateBeneficialOwners() {
        return ultimateBeneficialOwners;
    }

    public void setUltimateBeneficialOwners(String ultimateBeneficialOwners) {
        this.ultimateBeneficialOwners = ultimateBeneficialOwners;
    }

    public String getBusinessDesc() {
        return businessDesc;
    }

    public void setBusinessDesc(String businessDesc) {
        this.businessDesc = businessDesc;
    }

    public BigDecimal getExpectedMonthAmt() {
        return expectedMonthAmt;
    }

    public void setExpectedMonthAmt(BigDecimal expectedMonthAmt) {
        this.expectedMonthAmt = expectedMonthAmt;
    }

    public String getSourceOfFund() {
        return sourceOfFund;
    }

    public void setSourceOfFund(String sourceOfFund) {
        this.sourceOfFund = sourceOfFund;
    }

    public String getIsSensitive() {
        return isSensitive;
    }

    public void setIsSensitive(String isSensitive) {
        this.isSensitive = isSensitive;
    }

    public String getAdditional() {
        return additional;
    }

    public void setAdditional(String additional) {
        this.additional = additional;
    }
}
