package com.hisun.tms.urm.service;

import com.hisun.tms.urm.model.UrmUserBasicInf;
import com.hisun.tms.urm.model.dto.UserRegisterQueryDTO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 用户注册管理服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface UserRegisterService {

    /**
     * 查询所有用户注册信息
     */
    DataTablesOutput<UrmUserBasicInf> findAll(UserRegisterQueryDTO input);

    /**
     * 根据用户ID获取用户注册详情
     */
    UrmUserBasicInf get(String userId);

    /**
     * 新增用户注册信息
     */
    String add(UrmUserBasicInf urmUserBasicInf);

    /**
     * 更新用户注册信息
     */
    String update(UrmUserBasicInf urmUserBasicInf);

    /**
     * 删除用户注册信息
     */
    String delete(String userId);
}