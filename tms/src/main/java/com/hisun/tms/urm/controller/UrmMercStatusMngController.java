package com.hisun.tms.urm.controller;

import com.hisun.tms.urm.model.MercStatus;
import com.hisun.tms.urm.service.MercStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Controller
@RequestMapping("/urm/mermgr/status")
public class UrmMercStatusMngController {

    private static final Logger logger = LoggerFactory.getLogger(UrmMercStatusMngController.class);

    @Resource
    private MercStatusService mercStatusService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/status') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/status");
        return modelAndView;
    }

    @GetMapping("/{mercId}")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/status') or hasRole('ROLE_ADMIN')")
    public MercStatus search(@PathVariable("mercId") String mercId) {
        return mercStatusService.search(mercId);
    }

    @PostMapping("/change")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/status') or hasRole('ROLE_ADMIN')")
    public String search(MercStatus mercStatus) {
        logger.debug(mercStatus.getMercId());
        logger.debug(mercStatus.getMercSts());
        mercStatusService.save(mercStatus.getMercId(), mercStatus.getMercSts());
        return "0";
    }
}
