package com.hisun.tms.urm.model;




import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.time.LocalDate;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "urm_user_basic_inf")
public class UserBasicInfo extends AbstractEntityTmpe {
	/**
     * @Fields 用户号
     */
	@Id
    @Column(name = "user_id")
    private String userId;
	/**
     * @Fields 用户名称
     */
	@Column(name = "usr_nm")
    private String usrNm;
	/**
     * @Fields 用户级别
     */
	@Column(name = "usr_lvl")
    private String usrLvl;
	/**
	 * @Fields 用户手机号
	 */
	@Column(name = "mbl_no")
    private String mblNo;
	/**
     * @Fields idNoHid 证件号脱敏
     */
	@Column(name = "id_no")
    private String idNo;
    /**
     * @Fields idNoHid 证件号脱敏
     */
	@Column(name = "id_no_hid")
    private String idNoHid;

    /**
     * @Fields idType 证件类型
     */
	@Column(name = "id_type")
    private String idType;

    /**
     * @Fields usrRegDt 注册日期
     */
	@Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
	@Column(name = "usr_reg_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate usrRegDt;

    /**
     * @Fields idChkFlg 实名类型
     */
	@Column(name = "id_chk_flg")
    private String idChkFlg;

    /**
     * @Fields usrSts 用户状态 0:开户 1:销户
     */
	@Column(name = "usr_sts")
    private String usrSts;
    /**
     * @Fields usrClsDt 销户日期
     */
	@Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
	@Column(name = "usr_cls_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate usrClsDt;

    /**
     * @Fields usrRegCnl 注册渠道
     */
	@Column(name = "usr_reg_cnl")
    private String usrRegCnl;


	public String getIdNo() {
		return idNo;
	}


	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}


	public String getUserId() {
		return userId;
	}


	public void setUserId(String userId) {
		this.userId = userId;
	}


	public String getUsrNm() {
		return usrNm;
	}


	public String getUsrLvl() {
		return usrLvl;
	}


	public void setUsrLvl(String usrLvl) {
		this.usrLvl = usrLvl;
	}


	public void setUsrNm(String usrNm) {
		this.usrNm = usrNm;
	}


	public String getMblNo() {
		return mblNo;
	}


	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}


	public String getIdNoHid() {
		return idNoHid;
	}


	public void setIdNoHid(String idNoHid) {
		this.idNoHid = idNoHid;
	}


	public String getIdType() {
		return idType;
	}


	public void setIdType(String idType) {
		this.idType = idType;
	}


	public LocalDate getUsrRegDt() {
		return usrRegDt;
	}


	public void setUsrRegDt(LocalDate usrRegDt) {
		this.usrRegDt = usrRegDt;
	}


	public String getIdChkFlg() {
		return idChkFlg;
	}


	public void setIdChkFlg(String idChkFlg) {
		this.idChkFlg = idChkFlg;
	}


	public String getUsrSts() {
		return usrSts;
	}


	public void setUsrSts(String usrSts) {
		this.usrSts = usrSts;
	}


	public LocalDate getUsrClsDt() {
		return usrClsDt;
	}


	public void setUsrClsDt(LocalDate usrClsDt) {
		this.usrClsDt = usrClsDt;
	}


	public String getUsrRegCnl() {
		return usrRegCnl;
	}


	public void setUsrRegCnl(String usrRegCnl) {
		this.usrRegCnl = usrRegCnl;
	}


	@Override
    public String toString() {
        return "UserBasicInfo{" +
                "userId='" + userId + '\'' +
                ", usrNm='" + usrNm + '\'' +
                ", mblNo='" + mblNo + '\'' +
                ", idType='" + idType + '\'' +
                ", idNoHid='" + idNoHid + '\'' +
                ", idNo='" + idNo + '\'' +
                ", usrRegDt=" + usrRegDt +
                ", idChkFlg='" + idChkFlg + '\'' +
                ", usrSts='" + usrSts + '\'' +
                ", usrClsDt=" + usrClsDt + 
                ", usrRegCnl='" + usrRegCnl + '\'' +
                '}';
    }
}
