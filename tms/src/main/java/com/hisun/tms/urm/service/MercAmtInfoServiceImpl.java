package com.hisun.tms.urm.service;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.bil.model.MercBillOrderDO;
import com.hisun.tms.bil.model.UrmMerchantOrderInfoModel;
import com.hisun.tms.bil.model.UrmUserOrderInfoModel;
import com.hisun.tms.bil.repository.DataTablesMerchantOrderInfoRepository;
import com.hisun.tms.bil.repository.DataTablesOrderInfoRepository;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.urm.model.UserBaseInfo;
import com.hisun.tms.urm.model.UserParamInput;
import com.hisun.tms.urm.repository.UserBaseInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2017/8/30
 */
@Service
public class MercAmtInfoServiceImpl implements MercAmtInfoService {

    private static final Logger logger = LoggerFactory.getLogger(UserAmtInfoService.class);

    @Resource
    private DataTablesMerchantOrderInfoRepository dataTablesMerchantOrderInfoRepository;
    @Resource
    private UserBaseInfoRepository userBaseInfoRepository;

    @Override
    public DataTablesOutput<UrmMerchantOrderInfoModel> findAll(UserParamInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        String mercId=dataTablesInput.getExtra_search().get("mercId");
        String txType=dataTablesInput.getExtra_search().get("txType");
        UserBaseInfo userBaseInfo=null;

        Specification<UrmMerchantOrderInfoModel> orderQueryParam=Specifications.<UrmMerchantOrderInfoModel>and()
    			.eq(JudgeUtils.isNotBlank(mercId), "mercId", mercId)
    			.eq(JudgeUtils.isNotBlank(txType), "txTyp", txType)
    			.in("orderStatus", "S","S1","S2","R1","R2","R3")
                .build();
        DataTablesOutput<UrmMerchantOrderInfoModel> dataTablesOutput = dataTablesMerchantOrderInfoRepository.findAll(dataTablesInput, orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }
}
