package com.hisun.tms.urm.service;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.urm.dao.IUrmUserBasicInfDao;
import com.hisun.tms.urm.model.UrmUserBasicInf;
import com.hisun.tms.urm.model.dto.UserRegisterQueryDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户注册管理服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Service("userRegisterService")
@Transactional
public class UserRegisterServiceImpl implements UserRegisterService {

    private static final Logger logger = LoggerFactory.getLogger(UserRegisterServiceImpl.class);

    @Resource
    private IUrmUserBasicInfDao urmUserBasicInfDao;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<UrmUserBasicInf> findAll(UserRegisterQueryDTO input) {
        DataTablesOutput<UrmUserBasicInf> dataTablesOutput = new DataTablesOutput<>();

        Map<String, Object> param = new HashMap<>();
        param.put("userId", input.getUserId());
        param.put("start", input.getStart());
        param.put("length", input.getLength());

        // 先获取总记录数，再获取分页数据
        int total = urmUserBasicInfDao.countUserRegisterTotal(param);
        List<UrmUserBasicInf> list = urmUserBasicInfDao.findUserRegisterList(param);

        // 设置DataTablesOutput的属性
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setRecordsFiltered(total);
        dataTablesOutput.setData(list);

        return dataTablesOutput;
    }

    @Override
    public UrmUserBasicInf get(String userId) {
        return urmUserBasicInfDao.getUserRegisterById(userId);
    }

    @Override
    public String add(UrmUserBasicInf urmUserBasicInf) {
        try {
            // 设置创建时间和更新时间
            Date currentDate = new Date();
            urmUserBasicInf.setCreateTime(currentDate);
            urmUserBasicInf.setModifyTime(currentDate);
            
            // 设置默认值
            if (urmUserBasicInf.getUsrSts() == null) {
                urmUserBasicInf.setUsrSts("0"); // 默认开户状态
            }
            if (urmUserBasicInf.getIdChkFlg() == null) {
                urmUserBasicInf.setIdChkFlg("0"); // 默认非实名
            }
            if (urmUserBasicInf.getKybCert() == null) {
                urmUserBasicInf.setKybCert("N"); // 默认未认证
            }

            int result = urmUserBasicInfDao.insertUserRegister(urmUserBasicInf);
            if (result == 1) {
                return "SUCCESS"; // 成功代码
            }
            return "FAILED";
        } catch (Exception e) {
            logger.error("新增用户注册信息失败", e);
            return "ERROR";
        }
    }

    @Override
    public String update(UrmUserBasicInf urmUserBasicInf) {
        try {
            // 设置更新时间
            urmUserBasicInf.setModifyTime(new Date());

            int result = urmUserBasicInfDao.updateUserRegister(urmUserBasicInf);
            if (result == 1) {
                return "SUCCESS"; // 成功代码
            }
            return "FAILED";
        } catch (Exception e) {
            logger.error("更新用户注册信息失败", e);
            return "ERROR";
        }
    }

    @Override
    public String delete(String userId) {
        try {
            int result = urmUserBasicInfDao.deleteUserRegister(userId);
            return result + "";
        } catch (Exception e) {
            logger.error("删除用户注册信息失败", e);
            return "0";
        }
    }
}