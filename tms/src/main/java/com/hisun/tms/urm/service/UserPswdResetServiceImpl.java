package com.hisun.tms.urm.service;
import java.util.HashMap;


import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.client.UserPasswordClient;
import com.hisun.lemon.urm.dto.ChangPwdDTO;
import com.hisun.tms.urm.model.SafeInfoDO;
import com.hisun.tms.urm.model.SafeLoginDO;
import com.hisun.tms.urm.model.UrmCprExtInfDO;
import com.hisun.tms.urm.model.UserBasicInfo;
import com.hisun.tms.urm.model.UserSafeInfo;
import com.hisun.tms.urm.repository.MercInfoRepository;
import com.hisun.tms.urm.repository.MercSafelInfoRepository;
import com.hisun.tms.urm.repository.SafeInfoRespository;
import com.hisun.tms.urm.repository.SafeLoginRespository;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("userPswdResetServiceImpl")
public class UserPswdResetServiceImpl implements UserPswdResetService {

    private static final Logger logger = LoggerFactory.getLogger(UserPswdResetServiceImpl.class);
    
    @Autowired
    private UserPasswordClient userPasswordClient;
    @Autowired
    private SafeLoginRespository safeLoginRespository;
    @Autowired
    private SafeInfoRespository safeInfoRespository;

	@Override
	public Map<String,String> restPswd(String userId,String restFlg) {
		Map<String,String> map =  new HashMap<String,String>();
		SafeInfoDO safeInfoDO=safeInfoRespository.byMercIdAndOprTyp(userId , "0");
		if(JudgeUtils.isNull(safeInfoDO)){
			map.put("msgCd", "UserIsNull");
			map.put("msgInfo", "user does not exist;");
		}else{
			GenericRspDTO<NoBody> genericRspDTO=null;
			if(restFlg.equals("LOGIN")){
				SafeLoginDO safeLoginDO=safeLoginRespository.findOneBySafeId(safeInfoDO.getSafeId());
				genericRspDTO=userPasswordClient.resetRandomLoginPwd(safeLoginDO.getLoginId());
			}else if(restFlg.equals("PAY")){
				genericRspDTO=userPasswordClient.resetRandomPayPwd(userId);
			}
			map.put("msgCd", genericRspDTO.getMsgCd());
			map.put("msgInfo", genericRspDTO.getMsgInfo());
		}
		return map;
	}

}
