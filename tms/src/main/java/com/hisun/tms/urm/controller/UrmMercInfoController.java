package com.hisun.tms.urm.controller;

import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.sys.common.Constants;
import com.hisun.tms.urm.model.*;
import com.hisun.tms.urm.service.MerQrCodeService;
import com.hisun.tms.urm.service.MercBaseInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Controller
@RequestMapping("/urm/mermgr/info")
public class UrmMercInfoController {

    private static final Logger logger = LoggerFactory.getLogger(UrmMercInfoController.class);

    @Resource
    private MercBaseInfoService mercBaseInfoService;
    @Resource
    private MerQrCodeService merQrCodeService;

    @Value("${merc.upload.depath}")
    private String localPath;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/info') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/info");
        return modelAndView;
    }

    @GetMapping("balance")
    public ModelAndView showBalance() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/accquery/merAccbalance");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/info') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<MercExtInfoModel> findAll(@Valid @RequestBody DataTablesInput input) {
        return mercBaseInfoService.findAll(input);
    }

    @PostMapping(value = "status")
    @ResponseBody
    public DatatablesEditorResponse<MercExtInfoModel> add(
            @Valid @RequestBody DatatablesEditorRequest<MercExtInfoModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, MercExtInfoModel> data = datatablesEditorRequest.getData();
        DatatablesEditorResponse<MercExtInfoModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        data.forEach((key, mercBaseInfo) -> {
            logger.debug(key + "..." + mercBaseInfo.toString());
            try {
                mercBaseInfoService.updateMercStatus(mercBaseInfo.getMercId());
            } catch (LemonException e) {
                datatablesEditorResponse.setError(e.getMsgCd());
                if (JudgeUtils.equals(e.getMsgCd(), "ACM30001")) {
                    datatablesEditorResponse.setError("The account balance is not 0, cannot cancel the account.");
                }
            }
        });
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "merRegister")
    @ResponseBody
    public Map<String, String> merRegister(@RequestBody MerRegister merRegister) {
        Map<String, String> result = new HashMap<String, String>();
        result = mercBaseInfoService.merRegister(merRegister, Constants.EXAMINE_STATUS_NO_PASS);
        return result;
    }

    @PostMapping(value = "modify")
    @ResponseBody
    public Map<String, String> modifyRegister(@RequestBody MerRegister merRegister) {
        return mercBaseInfoService.merRegister(merRegister, Constants.EXAMINE_STATUS_MODIFY_PASS);
//        return mercBaseInfoService.modify(merRegister);
    }

    @PostMapping(value = "modifyView")
    @ResponseBody
    public MerRegister modifyView(@RequestParam String mercId, @RequestParam String mercSts) {
        return mercBaseInfoService.modifyRegister(mercId, mercSts);
    }

    @PostMapping(value = "checkMngAcc")
    @ResponseBody
    public Map<String, String> checkMngAcc(@RequestParam String mngAcc, @RequestParam String merId) {
        return mercBaseInfoService.checkMngAcc(mngAcc, merId);
    }
    @PostMapping(value = "checkComercReg")
    @ResponseBody
    public Map<String, String> checkComercReg(@RequestParam String comercReg, @RequestParam String merId) {
        return mercBaseInfoService.checkComercReg(comercReg, merId);
    }
    @PostMapping(value = "checkMercShortName")
    @ResponseBody
    public Map<String, String> checkMercShortName(@RequestParam String mercShortName, @RequestParam String merId) {
        return mercBaseInfoService.checkMercShortName(mercShortName, merId);
    }

    @PostMapping(value = "checkMobile")
    @ResponseBody
    public Map<String, String> checkMobile(@RequestParam String mobile) {
        return mercBaseInfoService.checkMobile(mobile);
    }

    @PostMapping(value = "getMerLevel")
    @ResponseBody
    public Map<String, List<ConstantParamRspDTO>> getMerLevel() {
        return mercBaseInfoService.getMerLevel();
    }

    @PostMapping(value = "queryUserBalance")
    @ResponseBody
    public DataTablesOutput<UrmMercBalanceDO> queryUserBalance(@Valid @RequestBody MartketFindInput input) {

        return mercBaseInfoService.queryUserBalance(input);
    }

    @PostMapping(value = "merQrCode")
    @ResponseBody
    public String enCoderMerQrCode(@RequestParam("loginId") String loginId, @RequestParam("userId") String userId) {

        return merQrCodeService.enCoderMerQrCode(loginId, userId);
    }
    @GetMapping(value = "getQRCode")
    public void getQRCode(@RequestParam("filename") String filename , HttpServletRequest request, HttpServletResponse response) {

        //获得请求文件名
        String path = localPath + "/" + filename;
        //设置文件MIME类型
        response.setContentType(request.getServletContext().getMimeType(path));
        logger.info("类型"+response.getContentType());
        //设置Content-Disposition
        response.setHeader("Content-Disposition", "attachment;filename="+filename);
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        logger.info("头"+response.getHeader("Content-Disposition"));
        //读取目标文件，通过response将目标文件写到客户端
        //获取目标文件的绝对路径
        //System.out.println(fullFileName);
        //读取文件
        File file = new File(path);
        InputStream in = null;
        OutputStream out = null ;
        try {
            in = new FileInputStream(file);
            out = response.getOutputStream();

            //写文件
            int b;
            while((b=in.read())!= -1)
            {
                out.write(b);
            }
            out.flush();
        } catch (Exception e) {
            logger.info("获取文件失败"+path);
        } finally {
            try {
                in.close();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }



    }

}
