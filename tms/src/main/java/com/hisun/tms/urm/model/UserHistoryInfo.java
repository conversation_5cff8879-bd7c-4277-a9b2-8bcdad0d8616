package com.hisun.tms.urm.model;




import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.time.LocalDate;
import java.time.LocalTime;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "urm_user_reg_history")
public class UserHistoryInfo extends AbstractEntityTmpe {
	/**
     * @Fields 用户号
     */
	@Id
    @Column(name = "user_id")
    private String userId;
	
	/**
	 * @Fields 用户手机号
	 */
	@Column(name = "mbl_no")
    private String mblNo;
	
	/**
     * @Fields  '用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家',
     */
	@Column(name = "usr_lvl")
    private String usrLvl;
	
	/**
     * @Fields idChkFlg 实名标志 0：非实名 1：实名
     */
	@Column(name = "id_chk_flg")
    private String idChkFlg;
	
	/**
     * @Fields usrRegCnl 注册渠道
     */
	@Column(name = "usr_reg_cnl")
    private String usrRegCnl;
	
	/**
     * @Fields usrRegIp 注册IP
     */
	@Column(name = "usr_reg_ip")
    private String usrRegIp;
	
	/**
     * @Fields usrRegDt 注册日期
     */
	@Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
	@Column(name = "usr_reg_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate usrRegDt;
	
	/**
     * @Fields usrRegTm 注册时间
     */
	@Convert(converter = Jsr310JpaConverters.LocalTimeConverter.class)
	@Column(name = "usr_reg_tm")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime usrRegTm;
	
	/**
     * @Fields usrClsCnl 销户渠道
     */
	@Column(name = "usr_cls_cnl")
    private String usrClsCnl;
	
	/**
     * @Fields usrClsCnl 销户IP
     */
	@Column(name = "usr_cls_ip")
    private String usrClsIp;
	
	/**
     * @Fields usrClsDt 注册日期
     */
	@Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
	@Column(name = "usr_cls_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate usrClsDt;
	
	/**
     * @Fields usrClsTm 销户时间
     */
	@Convert(converter = Jsr310JpaConverters.LocalTimeConverter.class)
	@Column(name = "usr_cls_tm")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime usrClsTm;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public String getUsrLvl() {
		return usrLvl;
	}

	public void setUsrLvl(String usrLvl) {
		this.usrLvl = usrLvl;
	}

	public String getIdChkFlg() {
		return idChkFlg;
	}

	public void setIdChkFlg(String idChkFlg) {
		this.idChkFlg = idChkFlg;
	}

	public String getUsrRegCnl() {
		return usrRegCnl;
	}

	public void setUsrRegCnl(String usrRegCnl) {
		this.usrRegCnl = usrRegCnl;
	}

	public String getUsrRegIp() {
		return usrRegIp;
	}

	public void setUsrRegIp(String usrRegIp) {
		this.usrRegIp = usrRegIp;
	}

	public LocalDate getUsrRegDt() {
		return usrRegDt;
	}

	public void setUsrRegDt(LocalDate usrRegDt) {
		this.usrRegDt = usrRegDt;
	}

	public LocalTime getUsrRegTm() {
		return usrRegTm;
	}

	public void setUsrRegTm(LocalTime usrRegTm) {
		this.usrRegTm = usrRegTm;
	}

	public String getUsrClsCnl() {
		return usrClsCnl;
	}

	public void setUsrClsCnl(String usrClsCnl) {
		this.usrClsCnl = usrClsCnl;
	}

	public String getUsrClsIp() {
		return usrClsIp;
	}

	public void setUsrClsIp(String usrClsIp) {
		this.usrClsIp = usrClsIp;
	}

	public LocalDate getUsrClsDt() {
		return usrClsDt;
	}

	public void setUsrClsDt(LocalDate usrClsDt) {
		this.usrClsDt = usrClsDt;
	}

	public LocalTime getUsrClsTm() {
		return usrClsTm;
	}

	public void setUsrClsTm(LocalTime usrClsTm) {
		this.usrClsTm = usrClsTm;
	}

	@Override
	public String toString() {
		return "UserBasicInfo {" +
		"userId='" + userId + '\'' +
		", mblNo='" + mblNo + '\'' +
		", usrLvl='" + usrLvl + '\'' +
		", idChkFlg='" + idChkFlg +'\'' + 
		", usrRegCnl='" + usrRegCnl + '\'' +
		", usrRegIp='" + usrRegIp + '\'' +
		", usrRegDt=" + usrRegDt + 
		", usrRegTm="+ usrRegTm + 
		", usrClsCnl='" + usrClsCnl + '\'' +
		", usrClsIp='" + usrClsIp + '\'' +
		", usrClsDt=" + usrClsDt + 
	    ", usrClsTm=" + usrClsTm + 
	    '}';
	}
}
