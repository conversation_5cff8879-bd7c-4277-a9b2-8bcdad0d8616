package com.hisun.tms.urm.model;



import com.hisun.tms.common.audit.AbstractEntityTmpe;


import javax.persistence.*;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "urm_cpr_itf_auth")
public class MercItfInfo extends AbstractEntityTmpe {
	/**
     * @Fields 用户号
     */
	@Id
    @Column(name = "user_id")
    private String userId;
	/**
     * @Fields 接口名称
     */
	@Column(name = "itf_nm")
    private String itfNm;
	/**
     * @Fields 版本号
     */
	@Column(name = "version")
    private String version;
	/**
     * @Fields 接口签名类型 MD5
     */
	@Column(name = "verify_type")
    private String verifyType;
	/**
     * @Fields 接口密钥
     */
	@Column(name = "secret_key")
    private String secretKey;
	/**
     * @Fields 接口状态  1:生效 0:失效
     */
	@Column(name = "sts")
    private String sts;
	/**
     * @Fields 请求签名字段串
     */
	@Column(name = "mac_item")
    private String macItem;
	/**
     * @Fields 返回签名字段串
     */
	@Column(name = "rsp_mac_item")
    private String rspMacItem;
	/**
     * @Fields 最后更新柜员
     */
	@Column(name = "last_upd_opr")
    private String lastUpdOpr;
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getItfNm() {
		return itfNm;
	}
	public void setItfNm(String itfNm) {
		this.itfNm = itfNm;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getVerifyType() {
		return verifyType;
	}
	public void setVerifyType(String verifyType) {
		this.verifyType = verifyType;
	}
	public String getSecretKey() {
		return secretKey;
	}
	public void setSecretKey(String secretKey) {
		this.secretKey = secretKey;
	}
	public String getSts() {
		return sts;
	}
	public void setSts(String sts) {
		this.sts = sts;
	}
	public String getMacItem() {
		return macItem;
	}
	public void setMacItem(String macItem) {
		this.macItem = macItem;
	}
	public String getRspMacItem() {
		return rspMacItem;
	}
	public void setRspMacItem(String rspMacItem) {
		this.rspMacItem = rspMacItem;
	}
	public String getLastUpdOpr() {
		return lastUpdOpr;
	}
	public void setLastUpdOpr(String lastUpdOpr) {
		this.lastUpdOpr = lastUpdOpr;
	}
	@Override
    public String toString() {
        return "MercItfInfo{" +
                "userId='" + userId + '\'' +
                ", itfNm='" + itfNm + '\'' +
                ", version='" + version + '\'' +
                ", verifyType='" + verifyType + '\'' +
                ", secretKey='" + secretKey + '\'' +
                ", sts='" + sts + '\'' +
                ", macItem='" + macItem + '\'' +
                ", rspMacItem='" + rspMacItem + '\'' +
                ", lastUpdOpr='" + lastUpdOpr + '\'' +
                '}';
    }
}
