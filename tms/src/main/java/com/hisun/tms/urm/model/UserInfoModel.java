package com.hisun.tms.urm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Entity
@Table(name = "urm_user_basic_inf")
public class UserInfoModel {

    @Id
    @Column(name = "user_id")
    private String userId;

    @Column(name = "usr_sts")
    private String usrSts;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsrSts() {
        return usrSts;
    }

    public void setUsrSts(String usrSts) {
        this.usrSts = usrSts;
    }

    @Override
    public String toString() {
        return "UserInfoModel{" +
                "userId='" + userId + '\'' +
                ", usrSts='" + usrSts + '\'' +
                '}';
    }
}
