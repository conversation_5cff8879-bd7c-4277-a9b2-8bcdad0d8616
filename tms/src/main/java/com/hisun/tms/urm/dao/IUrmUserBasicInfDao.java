package com.hisun.tms.urm.dao;

import com.hisun.tms.urm.model.UrmUserBasicInf;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function IUrmUserBasicInfDao
 * @description 用户信息数据访问层
 * @date 10/24/2017 Tue
 * @time 6:03 PM
 */
@Mapper
public interface IUrmUserBasicInfDao {
    List<Map> countUser(@Param("month") String month);
    int sumUserNumber(@Param("acDt") LocalDate acDt);
    List<Map> countMerc(@Param("month") String month);
    int sumMercNumber(@Param("acDt") LocalDate acDt);

    int updateUserKybCert(@Param("userId") String userId);

    /**
     * 根据查询条件查询用户注册信息
     */
    List<UrmUserBasicInf> findUserRegisterList(Map<String, Object> params);

    /**
     * 统计用户注册信息总数
     */
    int countUserRegisterTotal(Map<String, Object> params);

    /**
     * 根据用户ID获取用户注册详情
     */
    UrmUserBasicInf getUserRegisterById(@Param("userId") String userId);

    /**
     * 新增用户注册信息
     */
    int insertUserRegister(UrmUserBasicInf urmUserBasicInf);

    /**
     * 更新用户注册信息
     */
    int updateUserRegister(UrmUserBasicInf urmUserBasicInf);

    /**
     * 删除用户注册信息（逻辑删除）
     */
    int deleteUserRegister(@Param("userId") String userId);
}
