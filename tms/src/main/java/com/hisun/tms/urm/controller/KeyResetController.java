package com.hisun.tms.urm.controller;


import com.hisun.tms.urm.service.MercKeyResetService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/busmgr/keyresetctrl")
public class KeyResetController {

    private static final Logger logger = LoggerFactory.getLogger(KeyResetController.class);

    @Autowired
    private MercKeyResetService mercKeyResetService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/merckeyreset') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mercctrlinfo/merckeyform");
        return modelAndView;
    }

    @PostMapping(value = "query")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/merckeyreset') or hasRole('ROLE_ADMIN')")
    public Map<String,String> getUserInf(@RequestParam(value = "userId", required = true) String userId) {
        return mercKeyResetService.getUserInf(userId);
    }
    
    @PostMapping(value = "keyreset")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/merckeyreset') or hasRole('ROLE_ADMIN')")
    public Map<String,String> getUserInf(@RequestParam(value = "userId", required = true) String userId,@RequestParam(value = "usrNm", required = true) String usrNm,@RequestParam(value = "email", required = true) String email) {
        return mercKeyResetService.keyReset(userId,usrNm,email);
    }
}
