package com.hisun.tms.urm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/29
 */
@Entity
@Table(name = "tms_merc_info_mng")
public class MercExtInfoModel {

    @Id
    @Column(name = "user_id")
    private String mercId;

    @Column(name = "merc_name")
    private String mercNm;

    @Column(name = "usr_sts")
    private String mercSts;

    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public String getMercNm() {
        return mercNm;
    }

    public void setMercNm(String mercNm) {
        this.mercNm = mercNm;
    }

    public String getMercSts() {
        return mercSts;
    }

    public void setMercSts(String mercSts) {
        this.mercSts = mercSts;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "MercBaseInfo{" +
                "mercId='" + mercId + '\'' +
                ", mercNm='" + mercNm + '\'' +
                ", mercSts='" + mercSts + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
