package com.hisun.tms.urm.service;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.tms.acm.model.AcmAcBal;
import com.hisun.tms.acm.repository.AcmAcBalRepository;
import com.hisun.tms.chk.model.ChkControl;
import com.hisun.tms.inv.model.InvUserInfo;
import com.hisun.tms.inv.repository.InvUserInfoRepository;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.mkm.model.SeaccyDetailDo;
import com.hisun.tms.mkm.repository.SeaccyDetailRepository;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.urm.model.UrmUserBalanceDO;
import com.hisun.tms.urm.model.UserBaseInfo;
import com.hisun.tms.urm.model.UserBasicInfo;
import com.hisun.tms.urm.repository.DatatablesMercInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.mapping.Search;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.persistence.criteria.Predicate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by chen on 8/29 0029.
 */
@Service
public class UserBalanceServiceImpl implements UserBalanceService {

    @Resource
    private AcmAcBalRepository acmAcBalRepository;
    @Resource
    private SeaccyDetailRepository seaccyDetailRepository;

    @Resource
    private InvUserInfoRepository invUserInfoRepository;

    @Resource(name = "urmEntityManager")
    private EntityManager entityManagerFactory;
    @Autowired
    private DatatablesMercInfoRepository datatablesMercInfoRepository;
    @Override
    public DataTablesOutput<UrmUserBalanceDO> queryUserBalance(MartketFindInput input) {
        DataTablesOutput<UrmUserBalanceDO> dataTablesOutput = new DataTablesOutput<UrmUserBalanceDO>();
        input.setDraw(input.getDraw()+1);
        UserBaseInfo userBaseInfoslist = new UserBaseInfo();
        String userId = input.getExtra_search().get("userId");
        String mobile = input.getExtra_search().get("mobile");
        if(JudgeUtils.isBlank(userId) && JudgeUtils.isBlank(mobile)) {
            return dataTablesOutput ;
        }
        DataTablesInput tablesInput = new DataTablesInput();
        tablesInput.addColumn("userId",true ,false ,"");
        tablesInput.addColumn("mblNo",true ,false ,"");
        tablesInput.setDraw(input.getDraw());
        tablesInput.setOrder(input.getOrder());
        tablesInput.setLength(input.getLength());
        Search search = new Search();
        search.setValue("");
        search.setRegex(false);
        tablesInput.setSearch(search);
        Specification<UserBasicInfo> orderQueryParam= Specifications.<UserBasicInfo>and()
                .eq(JudgeUtils.isNotBlank(userId), "userId", userId)
                .like(JudgeUtils.isNotBlank(mobile), "mblNo", '%'+mobile+'%')
                .in("usrLvl","0","1")
                .build();
        DataTablesOutput<UserBasicInfo> out = datatablesMercInfoRepository.findAll(tablesInput, orderQueryParam);
        if (out.getData().size() == 0 ) {
            return dataTablesOutput;
        } else {
            List<UrmUserBalanceDO> list = new ArrayList<UrmUserBalanceDO>();
            List<UserBasicInfo> userBasicInfos =  out.getData() ;
            for (UserBasicInfo u : userBasicInfos) {
                userId = u.getUserId();
                mobile = u.getMblNo();
                UrmUserBalanceDO urmUserBalanceDO = new UrmUserBalanceDO();
                urmUserBalanceDO.setMobile(mobile);
                //账户余额
                List<AcmAcBal> listac = acmAcBalRepository.findAllById(userId);
                if (listac != null && listac.size() > 0) {
                    AcmAcBal acmAcBal = listac.get(0);
                    urmUserBalanceDO.setCash(acmAcBal.getAcCurBal().add(acmAcBal.getAcUavaBal()));
                    urmUserBalanceDO.setCashBalance(acmAcBal.getAcCurBal());
                    urmUserBalanceDO.setCashAcc(acmAcBal.getAC_NO());
                } else {
                    urmUserBalanceDO.setCash(BigDecimal.ZERO);
                    urmUserBalanceDO.setCashBalance(BigDecimal.ZERO);
                }
                //海币余额
                List<SeaccyDetailDo> listSe = seaccyDetailRepository.findBalanceByUserId(userId);
                if (listSe != null && listSe.size() > 0) {
                    SeaccyDetailDo seaccyDetailDo = listSe.get(0);
                    urmUserBalanceDO.setSeaccy(seaccyDetailDo.getCount());
                    urmUserBalanceDO.setSeaccyBalance(seaccyDetailDo.getCount());
                } else {
                    urmUserBalanceDO.setSeaccy(0);
                    urmUserBalanceDO.setSeaccyBalance(0);
                }
                //理财余额
                List<InvUserInfo> listIn = invUserInfoRepository.findBalanceByUserId(userId);
                if (listIn != null && listIn.size() > 0) {
                    InvUserInfo invUserInfo = listIn.get(0);
                    urmUserBalanceDO.setFinanceBalance(invUserInfo.getToltalCurrentAmt());
                } else {
                    urmUserBalanceDO.setFinanceBalance(BigDecimal.ZERO);
                }

                list.add(urmUserBalanceDO);
            }
            dataTablesOutput.setData(list);
            dataTablesOutput.setRecordsFiltered(out.getRecordsFiltered());
            dataTablesOutput.setRecordsTotal(out.getRecordsTotal());
            dataTablesOutput.setDraw(out.getDraw());
        }
        return dataTablesOutput;
    }
}
