package com.hisun.tms.urm.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.rsm.service.BlackListService;
import com.hisun.tms.sys.common.Constants;
import com.hisun.tms.sys.model.MercRegisterSeq;
import com.hisun.tms.urm.model.MerRegister;
import com.hisun.tms.urm.model.UrmKybSeq;
import com.hisun.tms.urm.service.MercBaseInfoService;
import com.hisun.tms.urm.service.UserKybInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Created by chen on 9/1 0001.
 */
@Controller
@RequestMapping("/urm/mermgr/examine")
public class UrmMercExamineController {
    @Resource
    private MercBaseInfoService mercBaseInfoService;

    @Resource
    private UserKybInfoService userKybInfoService;

    @Resource
    private ObjectMapper objectMapper;

    private static final Logger logger = LoggerFactory.getLogger(UrmMercExamineController.class);

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/examine') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/examine");
        return modelAndView;
    }


    @PostMapping(value = "list")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/examine') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<MercRegisterSeq> merExamine(@Valid @RequestBody DataTablesInput input) {
        return  mercBaseInfoService.merExamine(input);
    }

    @PostMapping(value = "examine")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/examine') or hasRole('ROLE_ADMIN')")
    public Map<String,String> merExamine(@Valid @RequestBody MercRegisterSeq mercRegisterSeq) throws JsonProcessingException {
        logger.info("-----" + objectMapper.writeValueAsString(mercRegisterSeq) );
        if (JudgeUtils.equals(mercRegisterSeq.getExaminStatus(), Constants.EXAMINE_STATUS_MODIFY_PASS)) {
            mercRegisterSeq.setExaminStatus(Constants.EXAMINE_STATUS_PASS);
            return mercBaseInfoService.modifyByExamine(mercRegisterSeq);
        }
        return  mercBaseInfoService.examine(mercRegisterSeq);
    }



}
