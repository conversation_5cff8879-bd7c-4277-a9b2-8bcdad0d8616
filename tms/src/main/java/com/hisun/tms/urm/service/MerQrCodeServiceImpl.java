package com.hisun.tms.urm.service;

import com.hisun.lemon.cmm.client.QRCodeServerClient;
import com.hisun.lemon.cmm.dto.TmsMakeQrcodeReqDTO;
import com.hisun.lemon.cmm.dto.TmsMakeQrcodeRspDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.urm.controller.UrmMercInfoController;
import com.hisun.tms.urm.model.CprResInf;
import com.hisun.tms.urm.repository.CprResInfRespository;
import com.hisun.tms.util.ImageUploadUtil;
import com.hisun.tms.util.QRCodeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;

/**
 * Created by chen on 10/27 0027.
 */
@Component
public class MerQrCodeServiceImpl implements MerQrCodeService {
    private static final Logger logger = LoggerFactory.getLogger(MerQrCodeServiceImpl.class);
    @Value("${merc.upload.depath}")
    private String localPath;

//    @Value("${merc.remote.path}")
//    private String remotePath;

    @Value("${mkm.remote.ip}")
    private String remoteIp;

    @Value("${mkm.remote.port}")
    private String remotePort;

    @Value("${mkm.remote.user}")
    private String remoteUser;

    @Value("${mkm.remote.password}")
    private String remotePwd;

    @Value("${mkm.remote.timeout}")
    private String timeout;

    @Value("${upload.remote.url}")
    private String url;
    @Resource
    private QRCodeServerClient qrCodeServerClient;

    @Resource
    private CprResInfRespository cprResInfRespository;
    @Autowired
    private Environment env;
    @Override
    public String enCoderMerQrCode(String loginId, String userId) {

        String qrImageName = userId + "qrcode.jpg" ;
        String qrImagePath = localPath +"/"+ qrImageName;
//        //判断是否已经存在改商户的二维码图片 如果存在则删除
        File oriQrCodeFile = new File(qrImagePath);
        if (oriQrCodeFile.exists()) {
            oriQrCodeFile.delete();
        }
        //调用cmm获取二维码文本
        TmsMakeQrcodeReqDTO tmsMakeQrcodeReqDTO = new TmsMakeQrcodeReqDTO();
        tmsMakeQrcodeReqDTO.setLoginId(loginId);
        tmsMakeQrcodeReqDTO.setUserId(userId);
        //商户账号
        tmsMakeQrcodeReqDTO.setQrCodeType("MA");
        GenericDTO<TmsMakeQrcodeReqDTO> reqDTO = GenericDTO.newInstance(tmsMakeQrcodeReqDTO);
        GenericRspDTO<TmsMakeQrcodeRspDTO> genericRspDTO = qrCodeServerClient.tmsMakeQrcode(reqDTO);
        if ("CMM00000".equals(genericRspDTO.getMsgCd()) && JudgeUtils.isNotBlank(genericRspDTO.getBody().getQRCode()) ) {
            String qrcode = genericRspDTO.getBody().getQRCode() ;
            //查询商户logo
           // CprResInf cprResInf = cprResInfRespository.findOne(userId);
           // if (cprResInf != null && JudgeUtils.isNotBlank(cprResInf.getMercLogoPath()) && cprResInf.getMercLogoPath().indexOf("http")>=0) {
//                String logoPath = cprResInf.getMercLogoPath();
//                String remotePath = cprResInf.getMercLogoPath().substring(logoPath.indexOf("/" , 7) ,logoPath.lastIndexOf("/") -1);
//                String name = logoPath.substring(logoPath.lastIndexOf("/") +1 ,logoPath.length()) ;
                try {
//                    FileSftpUtils.download(remoteIp, Integer.valueOf(remotePort), Integer.valueOf(timeout), remotePath,
//                            name, localPath, remoteUser, remotePwd);
//
//                    File file = new File(localPath + name) ;
//                    String[] ext = name.split("\\.");
//                    String qrlogoPath =localPath + userId + "logo" + "." + ext[1] ;
//                    if (file.exists()) {
//                        File defindFile = new File(qrlogoPath);
//                        if (defindFile.exists()) {
//                            defindFile.delete();
//                        }
//                        file.renameTo(defindFile);
//                    }
                    //生成二维码

                    QRCodeUtil.encode(qrcode ,"" ,localPath ,false ,qrImageName );
                    //String path = localPath + "/"+qrImageName ;
//                    try {
//                        FileSftpUtils.upload(path, remoteIp, Integer.valueOf(remotePort), Integer.valueOf(timeout), remotePath, remoteUser, remotePwd);
//                    } catch (Exception e) {
//                        logger.error("Upload failure" + e.getMessage());
//                        return "";
//                    }
//                    String baseUrl = env.getProperty("upload.remote.url");
//                    String key = env.getProperty("upload.remote.banner.key");
//                    String bucket = env.getProperty("upload.remote.banner.bucket");
//                    String fileUrl = ImageUploadUtil.uploadImage(baseUrl, key, bucket, path);
//                    File file = new File(qrImagePath) ;
//                    if (file.exists()) {
//                        file.delete();
//                    }
                    return  qrImageName ;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

     //   }

        return "";

    }
}
