package com.hisun.tms.urm.service;

import com.hisun.tms.bil.model.MercBillOrderDO;
import com.hisun.tms.bil.model.UrmMerchantOrderInfoModel;
import com.hisun.tms.bil.model.UrmUserOrderInfoModel;
import com.hisun.tms.urm.model.UserParamInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * <AUTHOR>
 * @create 2017/8/30
 */
public interface MercAmtInfoService {

    DataTablesOutput<UrmMerchantOrderInfoModel> findAll(UserParamInput userParamInput);
}
