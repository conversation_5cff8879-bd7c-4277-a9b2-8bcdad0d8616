package com.hisun.tms.urm.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @function MerRegister
 * @description 商户注册传输对象
 * @date 7/27/2017 Thu
 * @time 10:42 AM
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MerRegister implements Serializable{
    //商户扩展信息

    //----------------商户信息---------------
    /**
     * userId
     */
    private String userId;
    /**
     *  mercName 商户名称
     */
    private String mercName;
    /**
     *  mercShortName 商户简称
     */
    private String mercShortName;
    /**
     *  cprRegNmCn 注册名称(中文)
     */
    private String cprRegNmCn;
    /**
     *  cprOperNmCn 经营名称（中文）
     */
    private String cprOperNmCn;
    /**
     *  prinNm 负责人名称
     */
    private String prinNm;

    /**
     *  crpNm 法人名称
     */
    private String crpNm;

    /**
     *  cprIdTyp 法人证件类型
     */
    private String crpIdTyp;

    /**
     *  crpIdNo 法人证件号
     */
    private String crpIdNo;

    /**
     *  comercReg 工商注册号
     */
    private String comercReg;
    /**
     *  socialCrdCd 社会信用代码
     */
    private String socialCrdCd;
    /**
     *  orgCd 组织机构代码
     */
    private String orgCd;
    /**
     *  busiLisc 营业执照
     */
    private String busiLisc;
    /**
     *  taxCertId 税务证明
     */
    private String taxCertId;
    /**
     *  webNm 网站名称
     */
    private String webNm;
    /**
     *  webUrl 网站地址
     */
    private String webUrl;
    /**
     *  merRegAddr 公司注册地址
     */
    private String merRegAddr;
    /**
     *  merAddrLongitude 公司地址的所在经度
     */
    private BigDecimal merAddrLongitude;
    /**
     *  merAddrLatitude 公司地址的所在纬度
     */
    private BigDecimal merAddrLatitude;
    /**
     *  mgtScp 经营范围
     */
    private String mgtScp;
    /**
     *  needInvFlg 是否开具发票 Y：需要；N：不需要；
     */
    private String needInvFlg;
    /**
     *  invMod 开具发票方式 0 - 按季度开；1 - 按月开；2 –按年开；
     */
    private String invMod;
    /**
     *  invTit 发票抬头
     */
    private String invTit;
    /**
     *  invMailAddr 发票邮寄地址
     */
    private String invMailAddr;
    /**
     *  invMailZip 发票邮寄邮编
     */
    private String invMailZip;
    /**
     *  mercTrdCls 商户行业类别
     */
    private String mercTrdCls;
    /**
     *  mercTrdDesc 商户行业描述
     */
    private String mercTrdDesc;
    /**
     *  cprTyp 商户类别 01-国有，02-私有，03-外资，04-合资 08-个人，10-公司，11-个人独资
     */
    private String cprTyp;
    /**
     *  csTelNo 商户客服电话
     */
    private String csTelNo;
    /**
     *  mercHotLin 商户热线
     */
    private String mercHotLin;
    /**
     *  cusMgr 客户经理编号
     */
    private String cusMgr;
    /**
     *  cusMgrNm 客户经理名称
     */
    private String cusMgrNm;
    /**
     *  rcvMagAmt 应收商户保证金
     */
    private BigDecimal rcvMagAmt;

    /**
     * mblNo 管理员手机号
     */
    private String mblNo;

    /**
     * displayNm 管理员姓名
     */
    private String diplayNm;

    /**
     * email 管理员邮箱
     */
    private String email;

    /**
     * loginId 管理员账号
     */
    private String loginId;

    //开通微信子商户标志 open-开通 no-不开通
    private String openWechatMerflag;

    //商户logo照片路径
    private String mercLogoPath;

    //商户营业执照
    private String busLicImgPath;

    //商户银行卡照片
    private String cardImgPath;

    //商户银行卡张片反面
    private String cardImgPathB;

    //商户身份证照片
    private String certImgPath;

    //身份证照片反面
    private String certImgPathB;

    //商户商户协议证书照片
    private String merPotocolImgPath;

    /**
     * opnBusDt 开业日期
     */
    private String opnBusDtSr;

    /**
     * refereeMblNo 推荐人手机号
     */
    private String refereeMblNo;

    /**
     * belongMerc 上级商户
     */
    private String belongMerc;

    /**
     * 商户级别
     */
    private String merLvl;
    //----------------商户信息end ---------------
    //-------------------------------------结算信息--------------------------------------------------
    //结算银行卡
    private String capCardNo;

    //卡户名
    private String capCardName ;

    //开户银行
    private String capCorgNm;

    //开户银行编号
    private String capCorgNo;
    //支行名称
    private String subbranch;

    //结算模式
    private String settleType;

    //结算周期
    private String settleCycleType;

    //状态mercSts
    private String mercSts;

    //结算有效生效日期
    private String settleExpDate ;

    //结算失效日期
    private String settleEffDate ;

    //结算地址
    private String settleSite;

    //营业网点
    private String hallSites;

    //划款天数
    private String drawDays;

    private String refuseReson;
    //-------------------------------------结算信息 end --------------------------------------------------

    //------------------------------------- 商户利率信息 --------------------------------------------------
    private List<RateInfo> rateInfolist = new ArrayList<RateInfo>();

    //------------------------------------- 商户利率信息 end --------------------------------------------------


    public String getSettleSite() {
        return settleSite;
    }

    public void setSettleSite(String settleSite) {
        this.settleSite = settleSite;
    }

    public String getHallSites() {
        return hallSites;
    }

    public void setHallSites(String hallSites) {
        this.hallSites = hallSites;
    }

    public void setRateInfolist(List<RateInfo> rateInfolist) {
        this.rateInfolist = rateInfolist;
    }

    public String getCapCorgNo() {
        return capCorgNo;
    }

    public void setCapCorgNo(String capCorgNo) {
        this.capCorgNo = capCorgNo;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getMercShortName() {
        return mercShortName;
    }

    public void setMercShortName(String mercShortName) {
        this.mercShortName = mercShortName;
    }

    public String getCprRegNmCn() {
        return cprRegNmCn;
    }

    public void setCprRegNmCn(String cprRegNmCn) {
        this.cprRegNmCn = cprRegNmCn;
    }

    public String getCprOperNmCn() {
        return cprOperNmCn;
    }

    public void setCprOperNmCn(String cprOperNmCn) {
        this.cprOperNmCn = cprOperNmCn;
    }

    public String getPrinNm() {
        return prinNm;
    }

    public void setPrinNm(String prinNm) {
        this.prinNm = prinNm;
    }

    public String getCrpNm() {
        return crpNm;
    }

    public void setCrpNm(String crpNm) {
        this.crpNm = crpNm;
    }

    public String getCrpIdTyp() {
        return crpIdTyp;
    }

    public void setCrpIdTyp(String crpIdTyp) {
        this.crpIdTyp = crpIdTyp;
    }

    public String getCrpIdNo() {
        return crpIdNo;
    }

    public void setCrpIdNo(String crpIdNo) {
        this.crpIdNo = crpIdNo;
    }

    public String getComercReg() {
        return comercReg;
    }

    public void setComercReg(String comercReg) {
        this.comercReg = comercReg;
    }

    public String getSocialCrdCd() {
        return socialCrdCd;
    }

    public void setSocialCrdCd(String socialCrdCd) {
        this.socialCrdCd = socialCrdCd;
    }

    public String getOrgCd() {
        return orgCd;
    }

    public void setOrgCd(String orgCd) {
        this.orgCd = orgCd;
    }

    public String getBusiLisc() {
        return busiLisc;
    }

    public void setBusiLisc(String busiLisc) {
        this.busiLisc = busiLisc;
    }

    public String getTaxCertId() {
        return taxCertId;
    }

    public void setTaxCertId(String taxCertId) {
        this.taxCertId = taxCertId;
    }

    public String getWebNm() {
        return webNm;
    }

    public void setWebNm(String webNm) {
        this.webNm = webNm;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getMerRegAddr() {
        return merRegAddr;
    }

    public void setMerRegAddr(String merRegAddr) {
        this.merRegAddr = merRegAddr;
    }

    public BigDecimal getMerAddrLongitude() {
        return merAddrLongitude;
    }

    public void setMerAddrLongitude(BigDecimal merAddrLongitude) {
        this.merAddrLongitude = merAddrLongitude;
    }

    public BigDecimal getMerAddrLatitude() {
        return merAddrLatitude;
    }

    public void setMerAddrLatitude(BigDecimal merAddrLatitude) {
        this.merAddrLatitude = merAddrLatitude;
    }

    public String getMgtScp() {
        return mgtScp;
    }

    public void setMgtScp(String mgtScp) {
        this.mgtScp = mgtScp;
    }

    public String getNeedInvFlg() {
        return needInvFlg;
    }

    public void setNeedInvFlg(String needInvFlg) {
        this.needInvFlg = needInvFlg;
    }

    public String getInvMod() {
        return invMod;
    }

    public void setInvMod(String invMod) {
        this.invMod = invMod;
    }

    public String getInvTit() {
        return invTit;
    }

    public void setInvTit(String invTit) {
        this.invTit = invTit;
    }

    public String getInvMailAddr() {
        return invMailAddr;
    }

    public void setInvMailAddr(String invMailAddr) {
        this.invMailAddr = invMailAddr;
    }

    public String getInvMailZip() {
        return invMailZip;
    }

    public void setInvMailZip(String invMailZip) {
        this.invMailZip = invMailZip;
    }

    public String getMercTrdCls() {
        return mercTrdCls;
    }

    public void setMercTrdCls(String mercTrdCls) {
        this.mercTrdCls = mercTrdCls;
    }

    public String getMercTrdDesc() {
        return mercTrdDesc;
    }

    public void setMercTrdDesc(String mercTrdDesc) {
        this.mercTrdDesc = mercTrdDesc;
    }

    public String getCprTyp() {
        return cprTyp;
    }

    public void setCprTyp(String cprTyp) {
        this.cprTyp = cprTyp;
    }

    public String getCsTelNo() {
        return csTelNo;
    }

    public void setCsTelNo(String csTelNo) {
        this.csTelNo = csTelNo;
    }

    public String getMercHotLin() {
        return mercHotLin;
    }

    public void setMercHotLin(String mercHotLin) {
        this.mercHotLin = mercHotLin;
    }

    public String getCusMgr() {
        return cusMgr;
    }

    public void setCusMgr(String cusMgr) {
        this.cusMgr = cusMgr;
    }

    public String getCusMgrNm() {
        return cusMgrNm;
    }

    public void setCusMgrNm(String cusMgrNm) {
        this.cusMgrNm = cusMgrNm;
    }

    public BigDecimal getRcvMagAmt() {
        return rcvMagAmt;
    }

    public void setRcvMagAmt(BigDecimal rcvMagAmt) {
        this.rcvMagAmt = rcvMagAmt;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getDiplayNm() {
        return diplayNm;
    }

    public void setDiplayNm(String diplayNm) {
        this.diplayNm = diplayNm;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public List<RateInfo> getRateInfolist() {
        return rateInfolist;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCapCardNo() {
        return capCardNo;
    }

    public void setCapCardNo(String capCardNo) {
        this.capCardNo = capCardNo;
    }

    public String getCapCardName() {
        return capCardName;
    }

    public void setCapCardName(String capCardName) {
        this.capCardName = capCardName;
    }

    public String getCapCorgNm() {
        return capCorgNm;
    }

    public void setCapCorgNm(String capCorgNm) {
        this.capCorgNm = capCorgNm;
    }

    public String getSubbranch() {
        return subbranch;
    }

    public void setSubbranch(String subbranch) {
        this.subbranch = subbranch;
    }

    public String getSettleType() {
        return settleType;
    }

    public void setSettleType(String settleType) {
        this.settleType = settleType;
    }

    public String getDrawDays() {
        return drawDays;
    }

    public void setDrawDays(String drawDays) {
        this.drawDays = drawDays;
    }

    public String getSettleCycleType() {
        return settleCycleType;
    }

    public void setSettleCycleType(String settleCycleType) {
        this.settleCycleType = settleCycleType;
    }

    public String getMercLogoPath() {
        return mercLogoPath;
    }

    public void setMercLogoPath(String mercLogoPath) {
        this.mercLogoPath = mercLogoPath;
    }

    public String getBusLicImgPath() {
        return busLicImgPath;
    }

    public void setBusLicImgPath(String busLicImgPath) {
        this.busLicImgPath = busLicImgPath;
    }

    public String getCardImgPath() {
        return cardImgPath;
    }

    public void setCardImgPath(String cardImgPath) {
        this.cardImgPath = cardImgPath;
    }

    public String getCertImgPath() {
        return certImgPath;
    }

    public void setCertImgPath(String certImgPath) {
        this.certImgPath = certImgPath;
    }

    public String getMerPotocolImgPath() {
        return merPotocolImgPath;
    }

    public void setMerPotocolImgPath(String merPotocolImgPath) {
        this.merPotocolImgPath = merPotocolImgPath;
    }

    public String getMercSts() {
        return mercSts;
    }

    public void setMercSts(String mercSts) {
        this.mercSts = mercSts;
    }

    public String getOpnBusDtSr() {
        return opnBusDtSr;
    }

    public void setOpnBusDtSr(String opnBusDtSr) {
        this.opnBusDtSr = opnBusDtSr;
    }

    public String getRefereeMblNo() {
        return refereeMblNo;
    }

    public void setRefereeMblNo(String refereeMblNo) {
        this.refereeMblNo = refereeMblNo;
    }

    public void setSettleExpDate(String settleExpDate) {
        this.settleExpDate = settleExpDate;
    }

    public String getSettleEffDate() {

        return settleEffDate;
    }

    public void setSettleEffDate(String settleEffDate) {
        this.settleEffDate = settleEffDate;
    }

    public String getSettleExpDate() {
        return settleExpDate;
    }

    public String getCardImgPathB() {
        return cardImgPathB;
    }

    public void setCardImgPathB(String cardImgPathB) {
        this.cardImgPathB = cardImgPathB;
    }

    public String getCertImgPathB() {
        return certImgPathB;
    }

    public void setCertImgPathB(String certImgPathB) {
        this.certImgPathB = certImgPathB;
    }

    public String getMerLvl() {
        return merLvl;
    }

    public String getRefuseReson() {
        return refuseReson;
    }

    public void setRefuseReson(String refuseReson) {
        this.refuseReson = refuseReson;
    }

    public void setMerLvl(String merLvl) {
        this.merLvl = merLvl;
    }


    public String getOpenWechatMerflag() {
        return openWechatMerflag;
    }

    public void setOpenWechatMerflag(String openWechatMerflag) {
        this.openWechatMerflag = openWechatMerflag;
    }

    public String getBelongMerc() {
        return belongMerc;
    }

    public void setBelongMerc(String belongMerc) {
        this.belongMerc = belongMerc;
    }

    @Override
    public String toString() {
        return "MerRegister{" +
                "userId='" + userId + '\'' +
                ", mercName='" + mercName + '\'' +
                ", mercShortName='" + mercShortName + '\'' +
                ", cprRegNmCn='" + cprRegNmCn + '\'' +
                ", cprOperNmCn='" + cprOperNmCn + '\'' +
                ", prinNm='" + prinNm + '\'' +
                ", crpNm='" + crpNm + '\'' +
                ", crpIdTyp='" + crpIdTyp + '\'' +
                ", crpIdNo='" + crpIdNo + '\'' +
                ", comercReg='" + comercReg + '\'' +
                ", socialCrdCd='" + socialCrdCd + '\'' +
                ", orgCd='" + orgCd + '\'' +
                ", busiLisc='" + busiLisc + '\'' +
                ", taxCertId='" + taxCertId + '\'' +
                ", webNm='" + webNm + '\'' +
                ", webUrl='" + webUrl + '\'' +
                ", merRegAddr='" + merRegAddr + '\'' +
                ", merAddrLongitude=" + merAddrLongitude +
                ", merAddrLatitude=" + merAddrLatitude +
                ", mgtScp='" + mgtScp + '\'' +
                ", needInvFlg='" + needInvFlg + '\'' +
                ", invMod='" + invMod + '\'' +
                ", invTit='" + invTit + '\'' +
                ", invMailAddr='" + invMailAddr + '\'' +
                ", invMailZip='" + invMailZip + '\'' +
                ", mercTrdCls='" + mercTrdCls + '\'' +
                ", mercTrdDesc='" + mercTrdDesc + '\'' +
                ", cprTyp='" + cprTyp + '\'' +
                ", csTelNo='" + csTelNo + '\'' +
                ", mercHotLin='" + mercHotLin + '\'' +
                ", cusMgr='" + cusMgr + '\'' +
                ", cusMgrNm='" + cusMgrNm + '\'' +
                ", rcvMagAmt=" + rcvMagAmt +
                ", mblNo='" + mblNo + '\'' +
                ", diplayNm='" + diplayNm + '\'' +
                ", email='" + email + '\'' +
                ", loginId='" + loginId + '\'' +
                ", openWechatMerflag='" + openWechatMerflag + '\'' +
                ", mercLogoPath='" + mercLogoPath + '\'' +
                ", busLicImgPath='" + busLicImgPath + '\'' +
                ", cardImgPath='" + cardImgPath + '\'' +
                ", cardImgPathB='" + cardImgPathB + '\'' +
                ", certImgPath='" + certImgPath + '\'' +
                ", certImgPathB='" + certImgPathB + '\'' +
                ", merPotocolImgPath='" + merPotocolImgPath + '\'' +
                ", opnBusDtSr='" + opnBusDtSr + '\'' +
                ", refereeMblNo='" + refereeMblNo + '\'' +
                ", belongMerc='" + belongMerc + '\'' +
                ", merLvl='" + merLvl + '\'' +
                ", capCardNo='" + capCardNo + '\'' +
                ", capCardName='" + capCardName + '\'' +
                ", capCorgNm='" + capCorgNm + '\'' +
                ", capCorgNo='" + capCorgNo + '\'' +
                ", subbranch='" + subbranch + '\'' +
                ", settleType='" + settleType + '\'' +
                ", settleCycleType='" + settleCycleType + '\'' +
                ", mercSts='" + mercSts + '\'' +
                ", settleExpDate='" + settleExpDate + '\'' +
                ", settleEffDate='" + settleEffDate + '\'' +
                ", settleSite='" + settleSite + '\'' +
                ", hallSites='" + hallSites + '\'' +
                ", drawDays='" + drawDays + '\'' +
                ", refuseReson='" + refuseReson + '\'' +
                ", rateInfolist=" + rateInfolist +
                '}';
    }
}
