package com.hisun.tms.urm.dao;

import com.hisun.tms.urm.model.UrmKybSeq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserKybSeqDao {
    /**
     * 根据用户ID和审核状态查询KYB信息
     */
    List<UrmKybSeq> findByUserIdAndExamineStatus(@Param("userId") String userId,
            @Param("examineStatus") String examineStatus);

    /**
     * 根据用户ID查询KYB信息
     */
    UrmKybSeq findByUserId(@Param("userId") String userId);

    /**
     * 查询未初审的KYB信息
     */
    List<UrmKybSeq> findFirstAudit(UrmKybSeq urmKybSeq);

    /**
     * 查询已初审的KYB信息
     */
    List<UrmKybSeq> findSecondAudit(UrmKybSeq urmKybSeq);

    /**
     * 根据ID获取KYB信息
     */
    UrmKybSeq get(@Param("kybId") Integer kybId);

    /**
     * 更新KYB信息
     */
    void update(UrmKybSeq urmKybSeq);
}
