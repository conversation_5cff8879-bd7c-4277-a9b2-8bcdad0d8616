package com.hisun.tms.urm.service;

import com.hisun.tms.acm.dao.IAcmAcDetailDao;
import com.hisun.tms.acm.utils.ExcelFileUtil;
import com.hisun.tms.bil.model.ExcelCommonTempleModel;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.urm.dao.IUrmUserBasicInfDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @function UrmReportService
 * @description 用户报表服务
 * @date 10/24/2017 Tue
 * @time 5:53 PM
 */
@Service
public class UrmReportService {
    private final static Logger logger = LoggerFactory.getLogger(UrmReportService.class);

    // private static String path = "/data/urm/";
    private static String path = "D:/web3/";

    @Resource
    private IUrmUserBasicInfDao userBasicInfDao;

    @Resource
    private IAcmAcDetailDao acmAcDetailDao;

    /**
     * 商户注册月报表
     *
     * @param acDt
     */
    public void merchantRegMonthReport(String acDt) {
        // 将YYYYMMDD格式转换为LocalDate对象
        LocalDate localDate;
        if (JudgeUtils.isNull(acDt)) {
            localDate = LocalDate.now();
        } else {
            // 将YYYYMMDD格式转换为YYYY-MM-DD格式
            localDate = LocalDate.parse(acDt.substring(0, 4) + "-" + acDt.substring(4, 6) + "-" + acDt.substring(6, 8));
        }

        String title = "商户接入统计报表";
        String fileName = title + acDt + ".xls";
        LinkedHashMap titleMap = new LinkedHashMap<>();
        titleMap.put("机构名称：", "Mpay支付平台");
        // 格式化月份为YYYY-MM格式，以便与SQL查询匹配
        String month = acDt.substring(0, 4) + "-" + acDt.substring(4, 6);
        titleMap.put("统计时间：", month);
        LinkedHashMap columHeader = new LinkedHashMap<>();
        columHeader.put("日期", 5000);
        columHeader.put("新增商户数", 5000);
        columHeader.put("商户到达数", 5000);
        columHeader.put("月活跃商户数", 5000);
        List mercNumber = userBasicInfDao.countMerc(month);
        List<Object[]> mercNumberList = new ArrayList<>();

        // 汇总信息
        Map sumaryItem = new LinkedHashMap<>();
        Map sumaryMap = new HashMap<>();

        for (int i = 0; i < mercNumber.size(); ++i) {
            Map map = (Map) mercNumber.get(i);
            LocalDate regDt = LocalDate.parse(((Date) map.get("reg_dt")).toString());
            int mercTotalNum = userBasicInfDao.sumMercNumber(localDate); // 使用传入的日期
            int activeMercNum = acmAcDetailDao.countMerc(localDate); // 使用传入的日期
            Object[] obj = new Object[] { map.get("reg_dt").toString(), map.get("new_user_num").toString(),
                    Integer.toString(mercTotalNum), Integer.toString(activeMercNum) };
            mercNumberList.add(obj);
            if (i + 1 == mercNumber.size()) {
                sumaryItem.put(1, "");
                sumaryItem.put(2, Integer.toString(mercTotalNum));
                sumaryItem.put(3, Integer.toString(activeMercNum));
            }
        }

        sumaryMap.put("小计", sumaryItem);
        ExcelCommonTempleModel excelCommonTempleModel = new ExcelCommonTempleModel(path, fileName, title, title,
                titleMap, columHeader, sumaryMap);
        ExcelFileUtil.createExcel(mercNumberList, excelCommonTempleModel);
    }

    /**
     * 用户注册月报表
     *
     * @param acDt
     */
    public void userRegMonthReport(String acDt) {
        // 将YYYYMMDD格式转换为LocalDate对象
        LocalDate localDate;
        if (JudgeUtils.isNull(acDt)) {
            localDate = LocalDate.now();
        } else {
            // 将YYYYMMDD格式转换为YYYY-MM-DD格式
            localDate = LocalDate.parse(acDt.substring(0, 4) + "-" + acDt.substring(4, 6) + "-" + acDt.substring(6, 8));
        }

        String title = "用户注册统计报表";
        String fileName = title + acDt + ".xls";
        LinkedHashMap titleMap = new LinkedHashMap<>();
        titleMap.put("机构名称：", "Mpay支付平台");
        // 格式化月份为YYYY-MM格式，以便与SQL查询匹配
        String month = acDt.substring(0, 4) + "-" + acDt.substring(4, 6);
        titleMap.put("统计时间：", month);
        LinkedHashMap columHeader = new LinkedHashMap<>();
        columHeader.put("日期", 5000);
        columHeader.put("新增用户数", 5000);
        columHeader.put("用户到达数", 5000);
        columHeader.put("月活跃用户数", 5000);
        List userNumber = userBasicInfDao.countUser(month);
        List<Object[]> userNumberList = new ArrayList<>();

        // 汇总信息
        Map sumaryItem = new LinkedHashMap<>();
        Map sumaryMap = new HashMap<>();

        for (int i = 0; i < userNumber.size(); ++i) {
            Map map = (Map) userNumber.get(i);
            LocalDate regDt = LocalDate.parse(((Date) map.get("reg_dt")).toString());
            int userTotalNum = userBasicInfDao.sumUserNumber(localDate); // 使用传入的日期
            int activeUserNum = acmAcDetailDao.countUser(localDate); // 使用传入的日期
            Object[] obj = new Object[] { map.get("reg_dt").toString(), map.get("new_user_num").toString(),
                    Integer.toString(userTotalNum), Integer.toString(activeUserNum) };
            userNumberList.add(obj);
            if (i + 1 == userNumber.size()) {
                sumaryItem.put(1, "");
                sumaryItem.put(2, Integer.toString(userTotalNum));
                sumaryItem.put(3, Integer.toString(activeUserNum));
            }
        }

        sumaryMap.put("小计", sumaryItem);
        ExcelCommonTempleModel excelCommonTempleModel = new ExcelCommonTempleModel(path, fileName, title, title,
                titleMap, columHeader, sumaryMap);
        ExcelFileUtil.createExcel(userNumberList, excelCommonTempleModel);
    }
}
