package com.hisun.tms.urm.controller;


import com.hisun.tms.urm.model.UserBasicInfo;
import com.hisun.tms.urm.model.UserParamInput;
import com.hisun.tms.urm.service.UserBasicInfoService;
import com.hisun.tms.urm.service.UserPswdResetService;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Created by zh on 11/01
 */
@Controller
@RequestMapping("/urm")
public class UserPswdRstController {

    @Resource
    private UserBasicInfoService userBasicInfoService;
    
    @Resource
    private UserPswdResetService userPswdResetService;

    @GetMapping(value = "/userPswdRst")
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/pswdRst') or hasRole('ROLE_ADMIN')")
    public ModelAndView releaseDetail() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/usrmgr/pswdrst");
        return modelAndView;
    }

    @PostMapping(value = "/queryBasicInfo")
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/pswdRst') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<UserBasicInfo> findOne(@Valid @RequestBody UserParamInput input) {
        return userBasicInfoService.findOne(input);
    }
    
    @PostMapping(value = "/pswdreset")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/usrmgr/pswdRst') or hasRole('ROLE_ADMIN')")
    public Map<String,String> restPswd(@RequestParam(value = "userId", required = true) String userId,@RequestParam(value = "restFlg", required = true) String restFlg) {
        return userPswdResetService.restPswd(userId,restFlg);
    }
}
