package com.hisun.tms.urm.model.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * KYB查询DTO
 */
@ClientValidated
@ApiModel("KYB查询DTO")
public class QueryKybDTO {

    @ApiModelProperty(name = "pageNum", value = "当前页")
    private Integer pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页大小")
    private Integer pageSize;

    @ApiModelProperty(name = "userId", value = "用户ID")
    private String userId;

    @ApiModelProperty(name = "examineStatus", value = "审核状态：PENDING（待审核）/ FIRST_AUDIT（初审中）/ SECOND_AUDIT（复核中）/ APPROVED（审核通过）/ REJECTED（拒绝）")
    private String examineStatus;

    @ApiModelProperty(name = "auditFlag", value = "复核标志：0-未初审，1-已初审")
    private String auditFlag;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(String examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getAuditFlag() {
        return auditFlag;
    }

    public void setAuditFlag(String auditFlag) {
        this.auditFlag = auditFlag;
    }
}