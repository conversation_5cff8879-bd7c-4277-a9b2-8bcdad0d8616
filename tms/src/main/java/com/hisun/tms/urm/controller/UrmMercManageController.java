package com.hisun.tms.urm.controller;

import com.hisun.tms.urm.model.MercExtInfoModel;
import com.hisun.tms.urm.service.MercBaseInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @create 2017/8/30
 */
@Controller
@RequestMapping("/urm/mermgr/manage")
public class UrmMercManageController {

    private static final Logger logger = LoggerFactory.getLogger(UrmMercManageController.class);

    @Resource
    private MercBaseInfoService mercBaseInfoService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/manage') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/manage");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/manage') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<MercExtInfoModel> findAll(@Valid @RequestBody DataTablesInput input) {
        return mercBaseInfoService.findAll(input);
    }
}
