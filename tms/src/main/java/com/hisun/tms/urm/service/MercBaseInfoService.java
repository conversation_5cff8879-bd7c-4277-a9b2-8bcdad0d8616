package com.hisun.tms.urm.service;

import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.tms.mkm.model.MartketFindInput;
import com.hisun.tms.sys.model.MercRegisterSeq;
import com.hisun.tms.urm.model.MerRegister;
import com.hisun.tms.urm.model.MercExtInfoModel;
import com.hisun.tms.urm.model.UrmMercBalanceDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
public interface MercBaseInfoService {

    DataTablesOutput<MercExtInfoModel> findAll(DataTablesInput dataTablesInput);

    void updateMercStatus(String userId);

    /**
     * 商户注册
     * @param merRegister
     * @return
     */
    Map<String,String> merRegister(MerRegister merRegister, String status) ;

    DataTablesOutput<MercRegisterSeq> merExamine(DataTablesInput input);

    Map<String,String> examine(MercRegisterSeq mercRegisterSeq);

    MerRegister modifyRegister(String mercId, String mercSts);

    Map<String,String> modify(MerRegister merRegister);

    Map<String,String> modifyByExamine(MercRegisterSeq mercRegisterSeq);

    Map<String,String> checkMngAcc(String mngAcc, String merId);

    Map<String,String> checkMobile(String mobile);

    Map<String,List<ConstantParamRspDTO>> getMerLevel();


    /**
     * 查询商户账户余额
     * @param input
     * @return
     */
    DataTablesOutput<UrmMercBalanceDO> queryUserBalance(MartketFindInput input);

    Map<String,String> checkComercReg(String mngAcc, String merId);

    Map<String,String> checkMercShortName(String mercShortName, String merId);
}
