package com.hisun.tms.urm.service;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.rsm.model.RsmUserStatusModel;
import com.hisun.tms.rsm.repository.RsmUserStatusRepository;
import com.hisun.tms.urm.model.*;
import com.hisun.tms.urm.repository.MercExtInfoRepository;
import com.hisun.tms.urm.repository.UrmCprExtInfRespository;
import com.hisun.tms.urm.repository.UserBaseInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/30
 */
@Service
public class MercStatusServiceImpl implements MercStatusService {

    private static final Logger logger = LoggerFactory.getLogger(MercStatusService.class);

//    @Resource(name = "urmEntityManager")
//    private EntityManager urmEntityManager;
//
//    @Resource(name = "rsmEntityManager")
//    private EntityManager rsmEntityManager;

    @Resource
    private UrmCprExtInfRespository urmCprExtInfRespository;

    @Resource
    private RsmUserStatusRepository rsmUserStatusRepository;

    @Resource
    private UserBaseInfoRepository userBaseInfoRepository;

    @Resource
    private GetOpr getOpr;

    @Override
    public MercStatus search(String mercId) {
        MercStatus mercStatus = new MercStatus();
        UrmCprExtInfDO urmCprExtInfDO = urmCprExtInfRespository.findOne(mercId);
        if (urmCprExtInfDO != null && urmCprExtInfDO.getUserId() != null) {
            mercStatus.setMercId(mercId);
            mercStatus.setMercNm(urmCprExtInfDO.getMercName());
            mercStatus.setMercShortNm(urmCprExtInfDO.getMercShortName());

            UserBaseInfo userBaseInfo = userBaseInfoRepository.findOne(mercId);
            mercStatus.setUseRegDt(userBaseInfo.getUseRegDt());
            if (userBaseInfo != null && "1".equals(userBaseInfo.getUseSts())) {
                mercStatus.setMercSts("-1");

            }
            try {
                RsmUserStatusModel rsmUserStatusModel = rsmUserStatusRepository.findOne(mercId);
                if (rsmUserStatusModel != null && rsmUserStatusModel.getUserId() != null) {
                    mercStatus.setMercSts(rsmUserStatusModel.getUserSts());
                } else {
                    mercStatus.setMercSts("0");
                }
            } catch (Exception e) {
                mercStatus.setMercSts("0");
            }

        }
//        String sql = "SELECT a.USER_ID, a.MERC_NAME, a.MERC_SHORT_NAME, b.USR_STS FROM urm_cpr_ext_inf a " +
//                "LEFT JOIN urm_user_basic_inf b ON a.USER_ID = b.USER_ID WHERE a.USER_ID = ?";
//        Query query0 = urmEntityManager.createNativeQuery(sql);
//        query0.setParameter(1, mercId);
//        Object[] obj1;
//        try {
//            obj1 = (Object[]) query0.getSingleResult();
//        } catch (NoResultException e) {
//            return null;
//        }
//        mercStatus.setMercId((String) obj1[0]);
//        mercStatus.setMercNm((String) obj1[1]);
//        mercStatus.setMercShortNm((String) obj1[2]);
//        Character sts = (Character) obj1[3];
//        if (obj1[3].equals('1')) {
//            mercStatus.setMercSts("-1");
//            return mercStatus;
//        }
//
//        Query query1 = rsmEntityManager.createNativeQuery("SELECT USER_ID, USER_STS FROM rsm_user_status WHERE USER_ID = ?");
//        query1.setParameter(1, mercId);
//        try {
//            mercStatus.setMercSts((String) ((Object[]) query1.getSingleResult())[1]);
//        } catch (NoResultException e) {
//            mercStatus.setMercSts("0");
//        }
        return mercStatus;
    }

    @Override
    public void save(String id, String sts) {
        RsmUserStatusModel rsmUserStatusModel;
        rsmUserStatusModel = rsmUserStatusRepository.findOne(id);
        if (rsmUserStatusModel == null) {
            rsmUserStatusModel = new RsmUserStatusModel();
            rsmUserStatusModel.setCreateTime(LocalDateTime.now());
        }
        rsmUserStatusModel.setModifyTime(LocalDateTime.now());
        rsmUserStatusModel.setUpdOprId(getOpr.getOperatorName());
        rsmUserStatusModel.setUserSts(sts);
        rsmUserStatusModel.setUserId(id);
        rsmUserStatusRepository.save(rsmUserStatusModel);
    }


}
