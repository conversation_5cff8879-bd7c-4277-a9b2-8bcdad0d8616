package com.hisun.tms.urm.controller;

import com.hisun.tms.bil.model.MercBillOrderDO;
import com.hisun.tms.bil.model.UrmMerchantOrderInfoModel;
import com.hisun.tms.bil.model.UrmUserOrderInfoModel;
import com.hisun.tms.urm.model.UserParamInput;
import com.hisun.tms.urm.service.MercAmtInfoService;
import com.hisun.tms.urm.service.UserAmtInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @create 2017/8/30
 */
@Controller
@RequestMapping("/urm/mermgr/amtinfo")
public class UrmMerAmtInfoControlller {

    private static final Logger logger = LoggerFactory.getLogger(UrmMerAmtInfoControlller.class);

    @Resource
    private MercAmtInfoService mercAmtInfoService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/mermgr/amtinfo') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("urm/mermgr/amtinfo");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/mermgr/amtinfo') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<UrmMerchantOrderInfoModel> findAll(@Valid @RequestBody UserParamInput input) {
        return mercAmtInfoService.findAll(input);
    }

}
