package com.hisun.tms.urm.service;


import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.CommonEncryptReqDTO;
import com.hisun.lemon.cmm.dto.CommonEncryptRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.tms.cpt.model.WithdrawOrderDO;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.urm.common.Constants;
import com.hisun.tms.urm.model.UserBasicInfo;
import com.hisun.tms.urm.model.UserParamInput;
import com.hisun.tms.urm.repository.DatatablesMercInfoRepository;
import com.hisun.tms.util.EncryptUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("userBasicInfoService")
public class UserBasicInfoServiceImpl implements UserBasicInfoService {

    private static final Logger logger = LoggerFactory.getLogger(UserBasicInfoServiceImpl.class);

    @Autowired
    private DatatablesMercInfoRepository datatablesMercInfoRepository;
    @Autowired
    private CmmServerClient cmmServerClient;

    @Autowired
    private UserBasicInfClient userBasicInfClient;

    @Override
    @Transactional(value="urmTransactionManager", readOnly = true)
    public DataTablesOutput<UserBasicInfo> findAll(UserParamInput input) {
    	String userId = input.getExtra_search().get("userId").trim();
		String mblNo = input.getExtra_search().get("mblNo").trim();
		String idNo = input.getExtra_search().get("idNo").trim();
		/*
		 * 如果证件号不为空则加密
		 */
		String idNoEnc=null;
		if(JudgeUtils.isNotBlank(idNo)){
			CommonEncryptReqDTO commonEncryptReqDTO=new CommonEncryptReqDTO();
			commonEncryptReqDTO.setData(idNo);
			commonEncryptReqDTO.setType(Constants.ENCRYPT);
			GenericDTO<CommonEncryptReqDTO> reqDTO=GenericDTO.newInstance(commonEncryptReqDTO);
			GenericRspDTO<CommonEncryptRspDTO> rspDTO=cmmServerClient.encrypt(reqDTO);//证件号密文
			if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
				idNoEnc=idNo;
			}else{
				idNoEnc=rspDTO.getBody().getData();
			}
		}
    	Specification<UserBasicInfo> orderQueryParam=Specifications.<UserBasicInfo>and()
    			.eq(JudgeUtils.isNotBlank(userId), "userId", userId)
    			.like(JudgeUtils.isNotBlank(mblNo), "mblNo", "%"+mblNo)
    			.eq(JudgeUtils.isNotBlank(idNoEnc), "idNo", idNoEnc)
    			.in("usrLvl", "0","1")
                .build();
        DataTablesOutput<UserBasicInfo> dataTablesOutput = datatablesMercInfoRepository.findAll(input, orderQueryParam);
        //判断列表查询结果是否为空
        List<UserBasicInfo> userBasicInfoList=dataTablesOutput.getData();
	        if (!CollectionUtils.isEmpty(userBasicInfoList)){
        	//证件号解密
        	CommonEncryptReqDTO commonEncryptReqDTO=new CommonEncryptReqDTO();
        	commonEncryptReqDTO.setType(Constants.DECRYPT);
        	GenericDTO<CommonEncryptReqDTO> reqDTO=null;
        	GenericRspDTO<CommonEncryptRspDTO> rspDTO=null;
        	for(UserBasicInfo userBasicInfo:userBasicInfoList){
        		String idNoDsc=userBasicInfo.getIdNo();
        		if(JudgeUtils.isNotBlank(idNoDsc)){
	                try {
	        			commonEncryptReqDTO.setData(idNoDsc);
	        			reqDTO=GenericDTO.newInstance(commonEncryptReqDTO);
	        			rspDTO=cmmServerClient.encrypt(reqDTO);//证件号明文
	                	if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
	                		//解密失败则展示脱敏信息
	                		userBasicInfo.setIdNo(userBasicInfo.getIdNoHid());
	        			}else{
	        				userBasicInfo.setIdNo(rspDTO.getBody().getData());
	        			}
	                } catch (LemonException e) {
	                    e.printStackTrace();
	                }
        		}
        	}
            dataTablesOutput.setData(userBasicInfoList);
        }
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

	@Override
	public DataTablesOutput<UserBasicInfo> findOne(UserParamInput input) {
		DataTablesOutput<UserBasicInfo> dataTablesOutput=new DataTablesOutput<UserBasicInfo>();
		String userId = input.getExtra_search().get("userId").trim();
		String mblNo = input.getExtra_search().get("mobile").trim();
		if(JudgeUtils.isBlank(userId) && JudgeUtils.isBlank(mblNo)) {
            return dataTablesOutput ;
        }
    	Specification<UserBasicInfo> orderQueryParam=Specifications.<UserBasicInfo>and()
    			.eq(JudgeUtils.isNotBlank(userId), "userId", userId)
    			.eq(JudgeUtils.isNotBlank(mblNo), "mblNo", mblNo)
    			.in("usrLvl", "0","1")
                .build();
        dataTablesOutput = datatablesMercInfoRepository.findAll(input, orderQueryParam);
        //判断列表查询结果是否为空
        List<UserBasicInfo> userBasicInfoList=dataTablesOutput.getData();
	        if (!CollectionUtils.isEmpty(userBasicInfoList)){
        	//证件号解密
        	CommonEncryptReqDTO commonEncryptReqDTO=new CommonEncryptReqDTO();
        	commonEncryptReqDTO.setType(Constants.DECRYPT);
        	GenericDTO<CommonEncryptReqDTO> reqDTO=null;
        	GenericRspDTO<CommonEncryptRspDTO> rspDTO=null;
        	for(UserBasicInfo userBasicInfo:userBasicInfoList){
        		String idNoDsc=userBasicInfo.getIdNo();
        		if(JudgeUtils.isNotBlank(idNoDsc)){
	                try {
	        			commonEncryptReqDTO.setData(idNoDsc);
	        			reqDTO=GenericDTO.newInstance(commonEncryptReqDTO);
	        			rspDTO=cmmServerClient.encrypt(reqDTO);//证件号明文
	                	if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
	                		//解密失败则展示脱敏信息
	                		userBasicInfo.setIdNo(userBasicInfo.getIdNoHid());
	        			}else{
	        				userBasicInfo.setIdNo(rspDTO.getBody().getData());
	        			}
	                } catch (LemonException e) {
	                    e.printStackTrace();
	                }
        		}
        	}
            dataTablesOutput.setData(userBasicInfoList);
        }
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
	}

	@Override
	public Map<String, String> cancelUser(String userId) {
    	Map<String,String > map = new HashMap<String,String>();
		map.put("msgCd" , "0" );
    	try {
			GenericRspDTO<NoBody> genericDTO = userBasicInfClient.cancelUser(userId);
			if (genericDTO != null) {
				map.put("msgCd", genericDTO.getMsgCd());
				map.put("msgInfo", genericDTO.getMsgInfo());

			}
		} catch (Exception e) {
    		logger.info(e.getMessage());
		}
		return map;
	}
}
