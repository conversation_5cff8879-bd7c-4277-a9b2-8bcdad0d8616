package com.hisun.tms.bil.controller;

import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.bil.model.DmAccountReceiptsReqDTO;
import com.hisun.tms.bil.model.DmAccountReceiptsRspDTO;
import com.hisun.tms.bil.model.ReceiptRecord;
import com.hisun.tms.bil.service.IDmBillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;

/**
 * 数币账户收款/充值记录管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19 15:34
 */
@Api(value = "数币账户收款/充值记录管理")
@Controller
@RequestMapping(value = "/bil/dmbill")
@PreAuthorize("hasPermission('','/busmgr/trdmgr/dmbill') or hasRole('ROLE_ADMIN')")
public class DmBillController extends BaseController {

    @Resource(name = "dmBillService")
    private IDmBillService dmBillService;

    /**
     * 获取数币账户收款/充值记录列表
     */
    @ResponseBody
    @PostMapping(value = "/list")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/dmbill') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "获取数币账户收款/充值记录列表")
    @ApiOperation(value = "获取数币账户收款/充值记录列表", notes = "获取数币账户收款/充值记录列表")
    public GenericRspDTO<DmAccountReceiptsRspDTO> getDmAccountReceiptsList(
            @RequestBody GenericDTO<DmAccountReceiptsReqDTO> req) {
        DmAccountReceiptsReqDTO queryDTO = req.getBody();
        DmAccountReceiptsRspDTO receiptsRspDTO = dmBillService.getDmAccountReceiptsList(queryDTO);
        GenericRspDTO<DmAccountReceiptsRspDTO> dto = new GenericRspDTO<>();
        dto.setBody(receiptsRspDTO);
        return dto;
    }

    /**
     * 查询数币账户收款/充值记录详情
     */
    @ResponseBody
    @PostMapping(value = "/detail")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/dmbill') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "查询数币账户收款/充值记录详情")
    @ApiOperation(value = "查询数币账户收款/充值记录详情", notes = "查询数币账户收款/充值记录详情")
    public GenericRspDTO<ReceiptRecord> getDmAccountReceiptDetail(
            @RequestBody GenericDTO<String> req) {
        String orderId = req.getBody();
        ReceiptRecord receiptRecord = dmBillService.getDmAccountReceiptDetail(orderId);
        GenericRspDTO<ReceiptRecord> dto = new GenericRspDTO<>();
        dto.setBody(receiptRecord);
        return dto;
    }

    /**
     * 返回数币账户收款/充值记录管理页面
     */
    @RequestMapping(value = "/page")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/dmbill') or hasRole('ROLE_ADMIN')")
    public ModelAndView dmBillPage() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("bil/dmbill/index");
        return modelAndView;
    }

    /**
     * 返回数币收款管理页面
     */
    @RequestMapping(value = "/DSindex")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/dmbill') or hasRole('ROLE_ADMIN')")
    public ModelAndView dmBillDSPage() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("bil/dmbill/DSindex");
        return modelAndView;
    }
}
