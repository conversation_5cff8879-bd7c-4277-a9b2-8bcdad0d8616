package com.hisun.tms.bil.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.tms.bil.model.ReceiptRecord;
import com.hisun.tms.bil.model.entity.DmBillDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数币账户收款/充值记录DAO接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19 15:50
 */
@Mapper
public interface IDmBillDao extends BaseDao<DmBillDO> {

        /**
         * 查询账单编号集合
         *
         * @param acNo      账户号码
         * @param txType    交易类型
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 账单编号集合
         */
        List<String> findOrderNos(@Param("acNo") String acNo,
                        @Param("txType") String txType,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 获取收款/充值记录列表
         *
         * @param orderNos 订单编号集合
         * @param pageNo   页码
         * @param pageSize 每页记录数
         * @param offset   偏移量
         * @return 收款/充值记录列表
         */
        List<ReceiptRecord> getReceiptRecords(@Param("orderNos") List<String> orderNos,
                        @Param("pageNo") int pageNo,
                        @Param("pageSize") int pageSize,
                        @Param("offset") int offset);

        /**
         * 获取收款/充值记录详情
         *
         * @param orderId 订单ID
         * @return 收款/充值记录详情
         */
        ReceiptRecord getReceiptRecordDetail(@Param("orderId") String orderId);
}