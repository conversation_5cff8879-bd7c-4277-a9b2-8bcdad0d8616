package com.hisun.tms.bil.controller;

import com.hisun.lemon.bil.client.DataBoardClient;
import com.hisun.lemon.bil.dto.AgcyInfoListRspDTO;
import com.hisun.lemon.bil.dto.BusinessIndicatorsRspDTO;
import com.hisun.lemon.bil.dto.TransactionDataList;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.bil.service.MerchantReportService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/bil")
public class DataBoardController {

    private static final Logger logger = LoggerFactory.getLogger(DataBoardController.class);

    @Autowired
    private DataBoardClient dataBoardClient;


    /**
     * 获取业务核心指标
     */
    @GetMapping(value = "/DataBoard/business/indicators")
    @ResponseBody
    public GenericRspDTO<BusinessIndicatorsRspDTO> getBusinessIndicators(@RequestParam(value = "type", required = true) String type) {
        return dataBoardClient.getBusinessIndicators(type);
    }


    /**
     * 获取业务数据趋势
     */
    @GetMapping(value = "/DataBoard/business/dataTrend")
    @ResponseBody
    public GenericRspDTO<List<Map<String, String>>> getBusinessDataTrends(
            @RequestParam(value = "startDate", required = true) String startDate,
            @RequestParam(value = "endDate", required = true) String endDate) {
        return dataBoardClient.getBusinessDataTrends(startDate, endDate);
    }

    /**
     * 获取财务数据
     */
    @GetMapping(value = "/DataBoard/business/financialData")
    @ResponseBody
    public GenericRspDTO<Map<String, BigDecimal>> getFinancialData() {
        return dataBoardClient.getFinancialData();
    }

    @ApiOperation(value = "获取数据统计", notes = "获取数据统计")
    @GetMapping(value = "/DataBoard/dhDataStatistics")
    @ResponseBody
    public GenericRspDTO<List<TransactionDataList>> getDhDataStatistics() {
        return dataBoardClient.getDhDataStatistics();
    }

    @ApiOperation(value = "获取合作机构列表", notes = "获取合作机构列表")
    @GetMapping(value = "/DataBoard/agcnInfo")
    @ResponseBody
    public GenericRspDTO<List<AgcyInfoListRspDTO>> getAgcnInfo() {
        return dataBoardClient.getAgcnInfo();
    }


}
