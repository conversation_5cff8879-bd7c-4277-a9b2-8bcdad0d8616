package com.hisun.tms.bil.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户收支明细
 *
 * <AUTHOR>
 * @create 2017/8/30
 */
@Entity
@Table(name = "bil_user_order")
public class UrmUserOrderInfoModel {

    @Id
    @Column(name = "order_no")
    private String orderNo;

    @Column(name = "org_order_no")
    private String orgOrderNo;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "tx_tm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime txTm;

    @Column(name = "tx_type")
    private String txTyp;

    @Column(name = "order_amt")
    private String orderAmt;

    @Column(name = "coupon_amt")
    private BigDecimal couponAmt;

    @Column(name = "coupon_type")
    private BigDecimal couponType;

    @Column(name = "merc_id")
    private String mercId;

    @Column(name = "order_status")
    private String orderStatus;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrgOrderNo() {
        return orgOrderNo;
    }

    public void setOrgOrderNo(String orgOrderNo) {
        this.orgOrderNo = orgOrderNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public BigDecimal getCouponType() {
        return couponType;
    }

    public void setCouponType(BigDecimal couponType) {
        this.couponType = couponType;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    @Override
    public String toString() {
        return "UrmUserOrderInfoModel{" +
                "orderNo='" + orderNo + '\'' +
                ", orgOrderNo='" + orgOrderNo + '\'' +
                ", userId='" + userId + '\'' +
                ", txTm=" + txTm +
                ", txTyp='" + txTyp + '\'' +
                ", orderAmt='" + orderAmt + '\'' +
                ", couponAmt=" + couponAmt +
                ", couponType=" + couponType +
                ", mercId='" + mercId + '\'' +
                ", orderStatus='" + orderStatus + '\'' +
                '}';
    }
}
