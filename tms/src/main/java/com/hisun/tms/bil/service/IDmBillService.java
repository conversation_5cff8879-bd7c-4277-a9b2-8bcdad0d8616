package com.hisun.tms.bil.service;

import com.hisun.tms.bil.model.DmAccountReceiptsReqDTO;
import com.hisun.tms.bil.model.DmAccountReceiptsRspDTO;
import com.hisun.tms.bil.model.ReceiptRecord;

/**
 * 数币账户收款/充值记录服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19 15:40
 */
public interface IDmBillService {

    /**
     * 获取数币账户收款/充值记录列表
     * 
     * @param req 查询条件
     * @return 收款/充值记录列表
     */
    DmAccountReceiptsRspDTO getDmAccountReceiptsList(DmAccountReceiptsReqDTO req);

    /**
     * 获取数币账户收款/充值记录详情
     * 
     * @param orderId 订单ID
     * @return 收款/充值记录详情
     */
    ReceiptRecord getDmAccountReceiptDetail(String orderId);
}