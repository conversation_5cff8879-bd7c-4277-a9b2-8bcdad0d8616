package com.hisun.tms.bil.controller;

import com.hisun.tms.bil.service.MerchantReportService;
import com.hisun.tms.chk.model.ChkControl;
import com.hisun.tms.chk.model.ChkError;
import com.hisun.tms.chk.service.ChkErrorService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.cpt.model.GenericParamInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/bil")
public class BilController {

    private static final Logger logger = LoggerFactory.getLogger(BilController.class);

    @Autowired
    private MerchantReportService merchantReportService;



    @GetMapping(value = "/export/{dayStr}")
    @ResponseBody
    public Map export(@PathVariable String dayStr) {
        merchantReportService.summaryDayExport(dayStr,"e:/","日报表2.xls","cssh");
        return null;
    }


    @GetMapping(value = "/hall/recharge/month/{dayStr}")
    @ResponseBody
    public Map hallRechargeExport(@PathVariable String dayStr) {
        merchantReportService.summaryHallRechargeMonthExport(dayStr,"e:/","营业厅10月充值报表.xls","Mpay支付平台","营业厅");
        return null;
    }

    @GetMapping(value = "/hall/withdraw/month/{dayStr}")
    @ResponseBody
    public Map hallWithdrawExport(@PathVariable String dayStr) {
        merchantReportService.summaryHallWithdrawMonthExport(dayStr,"e:/","营业厅11月提现报表.xls","Mpay支付平台","营业厅");
        return null;
    }


}
