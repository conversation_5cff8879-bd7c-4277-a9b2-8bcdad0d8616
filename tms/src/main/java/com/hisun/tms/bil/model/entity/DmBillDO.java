package com.hisun.tms.bil.model.entity;

import com.hisun.lemon.framework.data.BaseDO;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 数币账户收款/充值记录实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19 15:55
 */
public class DmBillDO extends BaseDO {

    /**
     * 订单编号
     */
    @ApiModelProperty(name = "orderId", value = "订单编号")
    private String orderId;

    /**
     * 交易时间
     */
    @ApiModelProperty(name = "txTime", value = "交易时间")
    private LocalDateTime txTime;

    /**
     * 交易币种
     */
    @ApiModelProperty(name = "coinId", value = "交易币种")
    private String coinId;

    /**
     * 交易金额
     */
    @ApiModelProperty(name = "amount", value = "交易金额")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    /**
     * 收款地址/账户号
     */
    @ApiModelProperty(name = "receiveAddress", value = "收款地址/账户号")
    private String receiveAddress;

    /**
     * 付款地址/账户号
     */
    @ApiModelProperty(name = "payAddress", value = "付款地址/账户号")
    private String payAddress;

    /**
     * 交易哈希
     */
    @ApiModelProperty(name = "txHash", value = "交易哈希")
    private String txHash;

    /**
     * 交易状态
     */
    @ApiModelProperty(name = "status", value = "交易状态")
    private String status;

    /**
     * 附言
     */
    @ApiModelProperty(name = "memo", value = "附言")
    private String memo;

    /**
     * 交易渠道
     */
    @ApiModelProperty(name = "channel", value = "交易渠道")
    private String channel;

    /**
     * 账户号码
     */
    @ApiModelProperty(name = "acNo", value = "账户号码")
    private String acNo;

    /**
     * 交易类型
     */
    @ApiModelProperty(name = "txType", value = "交易类型")
    private String txType;

    /**
     * 区块链网络
     */
    @ApiModelProperty(name = "network", value = "区块链网络")
    private String network;

    /**
     * 区块ID
     */
    @ApiModelProperty(name = "blockId", value = "区块ID")
    private String blockId;

    /**
     * 错误码
     */
    @ApiModelProperty(name = "ecode", value = "错误码")
    private String ecode;

    /**
     * 组ID
     */
    @ApiModelProperty(name = "groupId", value = "组ID")
    private String groupId;

    /**
     * 区块哈希
     */
    @ApiModelProperty(name = "blockHash", value = "区块哈希")
    private String blockHash;

    /**
     * 程序ID
     */
    @ApiModelProperty(name = "programId", value = "程序ID")
    private String programId;

    /**
     * 计算单元消耗
     */
    @ApiModelProperty(name = "computeUnitsConsumed", value = "计算单元消耗")
    private Long computeUnitsConsumed;

    /**
     * 金库编码
     */
    @ApiModelProperty(name = "vaultCode", value = "金库编码")
    private String vaultCode;

    /**
     * 账户ID
     */
    @ApiModelProperty(name = "accountId", value = "账户ID")
    private String accountId;

    /**
     * 账户类型
     */
    @ApiModelProperty(name = "accountType", value = "账户类型")
    private String accountType;

    /**
     * 账单订单号
     */
    @ApiModelProperty(name = "bilOrderNo", value = "账单订单号")
    private String bilOrderNo;

    /**
     * 交易方向
     */
    @ApiModelProperty(name = "direction", value = "交易方向")
    private String direction;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页记录数
     */
    private Integer pageSize;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public LocalDateTime getTxTime() {
        return txTime;
    }

    public void setTxTime(LocalDateTime txTime) {
        this.txTime = txTime;
    }

    public String getCoinId() {
        return coinId;
    }

    public void setCoinId(String coinId) {
        this.coinId = coinId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getReceiveAddress() {
        return receiveAddress;
    }

    public void setReceiveAddress(String receiveAddress) {
        this.receiveAddress = receiveAddress;
    }

    public String getPayAddress() {
        return payAddress;
    }

    public void setPayAddress(String payAddress) {
        this.payAddress = payAddress;
    }

    public String getTxHash() {
        return txHash;
    }

    public void setTxHash(String txHash) {
        this.txHash = txHash;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getBlockId() {
        return blockId;
    }

    public void setBlockId(String blockId) {
        this.blockId = blockId;
    }

    public String getEcode() {
        return ecode;
    }

    public void setEcode(String ecode) {
        this.ecode = ecode;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getBlockHash() {
        return blockHash;
    }

    public void setBlockHash(String blockHash) {
        this.blockHash = blockHash;
    }

    public String getProgramId() {
        return programId;
    }

    public void setProgramId(String programId) {
        this.programId = programId;
    }

    public Long getComputeUnitsConsumed() {
        return computeUnitsConsumed;
    }

    public void setComputeUnitsConsumed(Long computeUnitsConsumed) {
        this.computeUnitsConsumed = computeUnitsConsumed;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getBilOrderNo() {
        return bilOrderNo;
    }

    public void setBilOrderNo(String bilOrderNo) {
        this.bilOrderNo = bilOrderNo;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
}