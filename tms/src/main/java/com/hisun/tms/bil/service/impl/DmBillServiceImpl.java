package com.hisun.tms.bil.service.impl;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.tms.bil.dao.IDmBillDao;
import com.hisun.tms.bil.model.DmAccountReceiptsReqDTO;
import com.hisun.tms.bil.model.DmAccountReceiptsRspDTO;
import com.hisun.tms.bil.model.ReceiptRecord;
import com.hisun.tms.bil.model.dto.ReceiptRecordReqDTO;
import com.hisun.tms.bil.service.IDmBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 数币账户收款/充值记录服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19 15:45
 */
@Service("dmBillService")
public class DmBillServiceImpl extends BaseService implements IDmBillService {
    private static final Logger logger = LoggerFactory.getLogger(DmBillServiceImpl.class);

    @Autowired
    private IDmBillDao dmBillDao;

    /**
     * 获取数币账户收款/充值记录列表
     *
     * @param req 查询条件
     * @return 收款/充值记录列表
     */
    @Override
    public DmAccountReceiptsRspDTO getDmAccountReceiptsList(DmAccountReceiptsReqDTO req) {
        // 查询账单编号集合 - 不再设置默认时间，如果时间为空则查询全部
        List<String> orderNos = dmBillDao.findOrderNos(
                req.getAcNo(), req.getTxType(), req.getStartTime(), req.getEndTime());

        // 总记录数
        long totalRecords = orderNos.size();
        if (totalRecords == 0) {
            logger.info("无符合条件的记录，acNo: {}, txType: {}", req.getAcNo(), req.getTxType());
            DmAccountReceiptsRspDTO result = new DmAccountReceiptsRspDTO();
            result.setRecords(new ArrayList<>());
            result.setTotalRecords(0);
            result.setTotalPages(0);
            result.setPageNo(req.getPageNo());
            result.setPageSize(req.getPageSize());
            return result;
        }

        // 计算偏移量
        int offset = (req.getPageNo() - 1) * req.getPageSize();

        // 获取分页收款/充值记录数据
        List<ReceiptRecord> records = dmBillDao.getReceiptRecords(
                orderNos, req.getPageNo(), req.getPageSize(), offset);

        // 组装响应
        DmAccountReceiptsRspDTO result = new DmAccountReceiptsRspDTO();
        result.setRecords(records);
        result.setTotalRecords(totalRecords);
        result.setTotalPages((int) Math.ceil((double) totalRecords / req.getPageSize()));
        result.setPageNo(req.getPageNo());
        result.setPageSize(req.getPageSize());

        logger.info("查询用户数币账户收款/充值记录完成，acNo: {}, txType: {}, totalRecords: {}",
                req.getAcNo(), req.getTxType(), totalRecords);
        return result;
    }

    /**
     * 获取数币账户收款/充值记录详情
     *
     * @param orderId 订单ID
     * @return 收款/充值记录详情
     */
    @Override
    public ReceiptRecord getDmAccountReceiptDetail(String orderId) {
        logger.info("查询数币账户收款/充值记录详情，orderId: {}", orderId);
        return dmBillDao.getReceiptRecordDetail(orderId);
    }
}