package com.hisun.tms.bil.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;

public class ExcelCommonTempleModel {
    private String savePath;

    private String saveFileName;

    private String sheetName;

    protected String title;

    //表头  行名称  行值
    protected LinkedHashMap<String,String> tableHeader;


    //列  名称，列宽
    protected LinkedHashMap<String,Long> rowName;

    //汇总信息，第几列 是啥值
    protected Map<String,Map<Integer,Object>> sumaryData;

    public ExcelCommonTempleModel(String savePath, String saveFileName, String sheetName, String title, LinkedHashMap<String, String> tableHeader, LinkedHashMap<String, Long> rowName, Map<String, Map<Integer, Object>> sumaryData) {
        this.savePath = savePath;
        this.saveFileName = saveFileName;
        this.sheetName = sheetName;
        this.title = title;
        this.tableHeader = tableHeader;
        this.rowName = rowName;
        this.sumaryData = sumaryData;
    }

    public String getSavePath() {
        return savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }

    public String getSaveFileName() {
        return saveFileName;
    }

    public void setSaveFileName(String saveFileName) {
        this.saveFileName = saveFileName;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Map<String, String> getTableHeader() {
        return tableHeader;
    }

    public void setTableHeader(LinkedHashMap<String, String> tableHeader) {
        this.tableHeader = tableHeader;
    }

    public Map<String, Long> getRowName() {
        return rowName;
    }

    public void setRowName(LinkedHashMap<String, Long> rowName) {
        this.rowName = rowName;
    }

    public Map<String, Map<Integer, Object>> getSumaryData() {
        return sumaryData;
    }

    public void setSumaryData(Map<String, Map<Integer, Object>> sumaryData) {
        this.sumaryData = sumaryData;
    }
}