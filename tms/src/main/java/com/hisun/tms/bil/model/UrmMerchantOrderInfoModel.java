package com.hisun.tms.bil.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 商户收支明细
 *
 * <AUTHOR>
 * @create 2017/8/30
 */
@Entity
@Table(name = "bil_merc_order")
public class UrmMerchantOrderInfoModel {

    @Id
    @Column(name = "order_no")
    private String orderNo;

    @Column(name = "org_order_no")
    private String orgOrderNo;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "tx_tm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime txTm;

    @Column(name = "tx_type")
    private String txTyp;

    @Column(name = "order_amt")
    private String orderAmt;

    @Column(name = "merc_id")
    private String mercId;

    @Column(name = "order_status")
    private String orderStatus;

    @Column(name = "merc_name")
    private String mercName;

    @Column(name = "bus_type")
    private String busType;

    @Column(name = "fee")
    private String fee;

    @Column(name = "serve_fee")
    private String serveFee;

    @Column(name = "order_channel")
    private String orderChannel;

    @Column(name = "coupon_amt")
    private String couponAmt;

    @Column(name = "coupon_type")
    private String couponType;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrgOrderNo() {
        return orgOrderNo;
    }

    public void setOrgOrderNo(String orgOrderNo) {
        this.orgOrderNo = orgOrderNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getServeFee() {
        return serveFee;
    }

    public void setServeFee(String serveFee) {
        this.serveFee = serveFee;
    }

    public String getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(String orderChannel) {
        this.orderChannel = orderChannel;
    }

    public String getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(String couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }
}
