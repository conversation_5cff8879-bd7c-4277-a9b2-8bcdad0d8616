package com.hisun.tms.bil.model;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * bil模块-数币账户收款/充值记录列表请求传输对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 10:21
 */
@ClientValidated
@ApiModel(value = "DmAccountReceiptsReqDTO", description = "数币账户收款/充值记录列表请求传输对象")
public class DmAccountReceiptsReqDTO extends GenericDTO<NoBody> {

    /**
     * 账户号码
     */
    @ApiModelProperty(name = "acNo", value = "账户号码")
    private String acNo;

    /**
     * 交易类型（DS.数币收款,DC.数币充值）
     */
    @ApiModelProperty(name = "txType", value = "交易类型（DS:数币收款,DC:数币充值）", required = true)
    private String txType;

    /**
     * 开始时间
     */
    @ApiModelProperty(name = "startTime", value = "查询开始时间（默认当日）")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(name = "endTime", value = "查询结束时间（默认当日）")
    private LocalDateTime endTime;

    /**
     * 页码
     */
    @ApiModelProperty(name = "pageNo", value = "页码，从1开始")
    private int pageNo = 1;

    /**
     * 每页记录数
     */
    @ApiModelProperty(name = "pageSize", value = "每页记录数，默认20")
    private int pageSize = 20;

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
