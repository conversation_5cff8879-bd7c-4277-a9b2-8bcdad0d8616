package com.hisun.tms.bil.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 收款/充值记录请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19 16:00
 */
@ApiModel(value = "ReceiptRecordReqDTO", description = "收款/充值记录请求DTO")
public class ReceiptRecordReqDTO {

    /**
     * 订单编号集合
     */
    @ApiModelProperty(name = "orderNos", value = "订单编号集合")
    private List<String> orderNos;

    /**
     * 页码
     */
    @ApiModelProperty(name = "pageNo", value = "页码")
    private int pageNo = 1;

    /**
     * 每页记录数
     */
    @ApiModelProperty(name = "pageSize", value = "每页记录数")
    private int pageSize = 20;

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}