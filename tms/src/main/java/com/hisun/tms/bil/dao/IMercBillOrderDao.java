package com.hisun.tms.bil.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/7/12
 * @time 17:30
 */
@Mapper
public interface IMercBillOrderDao{

    List<Map> tradeGroupByTxBustype(@Param(value="startTime") LocalDateTime startTime,@Param(value="endTime") LocalDateTime endTime);

    public List<Map> hallRechargeStatisticsByMonth(@Param(value="monthStr") String monthStr,@Param("busType") String busType,@Param("orderStatus") String orderStatus);

    public List<Map> hallWithdrawStatisticsByMonth(@Param(value="monthStr") String monthStr,@Param("orderStatus") String orderStatus);
}
