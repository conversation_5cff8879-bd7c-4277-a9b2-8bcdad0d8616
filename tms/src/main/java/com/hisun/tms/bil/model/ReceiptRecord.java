package com.hisun.tms.bil.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 收款/充值记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/16 11:02
 */
@ApiModel(value = "ReceiptRecord", description = "收款/充值记录明细")
public class ReceiptRecord {

    /**
     * 交易时间
     */
    @ApiModelProperty(name = "txTime", value = "交易时间")
    private LocalDateTime txTime;

    /**
     * 交易币种
     */
    @ApiModelProperty(name = "coinId", value = "交易币种")
    private String coinId;

    /**
     * 交易金额
     */
    @ApiModelProperty(name = "amount", value = "交易金额")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    /**
     * 收款地址/账户号
     */
    @ApiModelProperty(name = "receiveAddress", value = "收款地址/账户号")
    private String receiveAddress;

    /**
     * 付款地址/账户号
     */
    @ApiModelProperty(name = "payAddress", value = "付款地址/账户号")
    private String payAddress;

    /**
     * 交易流水号
     */
    @ApiModelProperty(name = "orderId", value = "交易流水号")
    private String orderId;

    /**
     * 交易哈希
     */
    @ApiModelProperty(name = "txHash", value = "交易哈希（非链上交易为空）")
    private String txHash;

    /**
     * 交易状态
     */
    @ApiModelProperty(name = "status", value = "交易状态（SUCCESS/FAILED）")
    private String status;

    /**
     * 附言
     */
    @ApiModelProperty(name = "memo", value = "附言（无则为--）")
    private String memo;

    /**
     * 交易渠道
     */
    @ApiModelProperty(name = "channel", value = "交易渠道（如SWIFT）")
    private String channel;

    /**
     * 交易类型
     */
    @ApiModelProperty(name = "txType", value = "交易类型（DS数币收款/DC数币充值/DH数币兑换/DZ数币转账/DX数币提现等）")
    private String txType;

    /**
     * 网络标识
     */
    @ApiModelProperty(name = "network", value = "网络标识（如solana-testnet）")
    private String network;

    /**
     * 区块ID
     */
    @ApiModelProperty(name = "blockId", value = "区块ID")
    private Long blockId;

    /**
     * 错误码
     */
    @ApiModelProperty(name = "ecode", value = "错误码（成功时为空）")
    private String ecode;

    /**
     * 交易组ID
     */
    @ApiModelProperty(name = "groupId", value = "交易组ID")
    private String groupId;

    /**
     * 区块哈希
     */
    @ApiModelProperty(name = "blockHash", value = "区块哈希")
    private String blockHash;

    /**
     * 合约程序ID
     */
    @ApiModelProperty(name = "programId", value = "合约程序ID")
    private String programId;

    /**
     * 消耗的计算单元
     */
    @ApiModelProperty(name = "computeUnitsConsumed", value = "消耗的计算单元")
    private Long computeUnitsConsumed;

    /**
     * 所属金库编码
     */
    @ApiModelProperty(name = "vaultCode", value = "所属金库编码")
    private String vaultCode;

    /**
     * 关联账户ID
     */
    @ApiModelProperty(name = "accountId", value = "关联账户ID")
    private Long accountId;

    /**
     * 账户类型
     */
    @ApiModelProperty(name = "accountType", value = "账户类型（PS:平台, 商户:MA）")
    private String accountType;

    /**
     * 账单编号
     */
    @ApiModelProperty(name = "bilOrderNo", value = "账单编号(关联bil_user_order表order_no)")
    private String bilOrderNo;

    /**
     * 交易方向
     */
    @ApiModelProperty(name = "direction", value = "交易方向（IN：入账；OUT：出账）")
    private String direction;

    // Getters and Setters for new fields
    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public Long getBlockId() {
        return blockId;
    }

    public void setBlockId(Long blockId) {
        this.blockId = blockId;
    }

    public String getEcode() {
        return ecode;
    }

    public void setEcode(String ecode) {
        this.ecode = ecode;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getBlockHash() {
        return blockHash;
    }

    public void setBlockHash(String blockHash) {
        this.blockHash = blockHash;
    }

    public String getProgramId() {
        return programId;
    }

    public void setProgramId(String programId) {
        this.programId = programId;
    }

    public Long getComputeUnitsConsumed() {
        return computeUnitsConsumed;
    }

    public void setComputeUnitsConsumed(Long computeUnitsConsumed) {
        this.computeUnitsConsumed = computeUnitsConsumed;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getBilOrderNo() {
        return bilOrderNo;
    }

    public void setBilOrderNo(String bilOrderNo) {
        this.bilOrderNo = bilOrderNo;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    // Original Getters and Setters
    public LocalDateTime getTxTime() {
        return txTime;
    }

    public void setTxTime(LocalDateTime txTime) {
        this.txTime = txTime;
    }

    public String getCoinId() {
        return coinId;
    }

    public void setCoinId(String coinId) {
        this.coinId = coinId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getReceiveAddress() {
        return receiveAddress;
    }

    public void setReceiveAddress(String receiveAddress) {
        this.receiveAddress = receiveAddress;
    }

    public String getPayAddress() {
        return payAddress;
    }

    public void setPayAddress(String payAddress) {
        this.payAddress = payAddress;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTxHash() {
        return txHash;
    }

    public void setTxHash(String txHash) {
        this.txHash = txHash;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }
}
