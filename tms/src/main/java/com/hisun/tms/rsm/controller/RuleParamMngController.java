package com.hisun.tms.rsm.controller;

import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.rsm.model.RuleParamModel;
import com.hisun.tms.rsm.service.RuleParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/21
 */
@Controller
@RequestMapping("/rsm/check/param")
public class RuleParamMngController {

    private static final Logger logger = LoggerFactory.getLogger(RuleParamMngController.class);

    @Resource
    private RuleParamService ruleParamService;

    @Resource
    private ConstantParamClient constantParamClient;

    @GetMapping
    @PreAuthorize("hasPermission('','/rsmmgr/check/param') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rsm/check/param");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/param') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<RuleParamModel> findAll(@Valid @RequestBody DataTablesInput input) {
        return ruleParamService.findAll(input);
    }

    @PostMapping(value = "delete")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/param') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<RuleParamModel> del(
            @Valid @RequestBody DatatablesEditorRequest<RuleParamModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, RuleParamModel> data = datatablesEditorRequest.getData();
        data.forEach((key, ruleParamModel) -> {
            logger.debug(key + "..." + ruleParamModel.toString());
            ruleParamService.delete(ruleParamModel.getParmId());
        });

        DatatablesEditorResponse<RuleParamModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "add")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/param') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<RuleParamModel> add(
            @Valid @RequestBody DatatablesEditorRequest<RuleParamModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, RuleParamModel> data = datatablesEditorRequest.getData();
        DatatablesEditorResponse<RuleParamModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        data.forEach((key, ruleParamModel) -> {
            try {
                ruleParamService.save(ruleParamModel);
            } catch (Exception e) {
                datatablesEditorResponse.setError("Error: This record already exists!");
            }
        });
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @GetMapping(value = "mercLvl")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/param') or hasRole('ROLE_ADMIN')")
    public Map queryMercLvlJson() {
        GenericRspDTO<List<ConstantParamRspDTO>> listGenericRspDTO = constantParamClient.paramsGroup("MER_LEVEL");
        Map<String, String> list = new LinkedHashMap<>();
        list.put("Lv.0", "0");
        for (ConstantParamRspDTO constantParamRspDTO : listGenericRspDTO.getBody()) {
            String lv = constantParamRspDTO.getParmVal();
            list.put("Lv." + lv, lv);
        }
        return list;
    }
}
