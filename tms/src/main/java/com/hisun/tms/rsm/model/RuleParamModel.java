package com.hisun.tms.rsm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/9/7
 */
@Entity
@Table(name = "rsm_rule_parm")
public class RuleParamModel {

    @Id
    @Column(name = "parm_id")
    private String parmId;

    @Column(name = "ac_typ")
    private String acTyp;

    @Column(name = "lmt_lvl")
    private String lmtLvl;

    @Column(name = "dc_pty_flg")
    private String dcPtyFlg;

    @Column(name = "tx_typ")
    private String txTyp;

    @Column(name = "pay_typ")
    private String payTyp;

    @Column(name = "min_amt_lmt")
    private BigDecimal minAmtLmt;

    @Column(name = "max_amt_lmt")
    private BigDecimal maxAmtLmt;

    @Column(name = "dly_amt_lmt")
    private BigDecimal dlyAmtLmt;

    @Column(name = "dly_cnt_lmt")
    private String dlyCntLmt;

    @Column(name = "mly_amt_lmt")
    private BigDecimal mlyAmtLmt;

    @Column(name = "mly_cnt_lmt")
    private String mlyCntLmt;

    @Column(name = "upd_opr_id")
    private String updOprId;

    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime modifyTime;

    public String getParmId() {
        return parmId;
    }

    public void setParmId(String parmId) {
        this.parmId = parmId;
    }

    public String getAcTyp() {
        return acTyp;
    }

    public void setAcTyp(String acTyp) {
        this.acTyp = acTyp;
    }

    public String getLmtLvl() {
        return lmtLvl;
    }

    public void setLmtLvl(String lmtLvl) {
        this.lmtLvl = lmtLvl;
    }

    public String getDcPtyFlg() {
        return dcPtyFlg;
    }

    public void setDcPtyFlg(String dcPtyFlg) {
        this.dcPtyFlg = dcPtyFlg;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getPayTyp() {
        return payTyp;
    }

    public void setPayTyp(String payTyp) {
        this.payTyp = payTyp;
    }

    public BigDecimal getMinAmtLmt() {
        return minAmtLmt;
    }

    public void setMinAmtLmt(BigDecimal minAmtLmt) {
        this.minAmtLmt = minAmtLmt;
    }

    public BigDecimal getMaxAmtLmt() {
        return maxAmtLmt;
    }

    public void setMaxAmtLmt(BigDecimal maxAmtLmt) {
        this.maxAmtLmt = maxAmtLmt;
    }

    public BigDecimal getDlyAmtLmt() {
        return dlyAmtLmt;
    }

    public void setDlyAmtLmt(BigDecimal dlyAmtLmt) {
        this.dlyAmtLmt = dlyAmtLmt;
    }

    public String getDlyCntLmt() {
        return dlyCntLmt;
    }

    public void setDlyCntLmt(String dlyCntLmt) {
        this.dlyCntLmt = dlyCntLmt;
    }

    public BigDecimal getMlyAmtLmt() {
        return mlyAmtLmt;
    }

    public void setMlyAmtLmt(BigDecimal mlyAmtLmt) {
        this.mlyAmtLmt = mlyAmtLmt;
    }

    public String getMlyCntLmt() {
        return mlyCntLmt;
    }

    public void setMlyCntLmt(String mlyCntLmt) {
        this.mlyCntLmt = mlyCntLmt;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RuleParamModel{" +
                "parmId='" + parmId + '\'' +
                ", acTyp='" + acTyp + '\'' +
                ", lmtLvl='" + lmtLvl + '\'' +
                ", dcPtyFlg='" + dcPtyFlg + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", payTyp='" + payTyp + '\'' +
                ", minAmtLmt=" + minAmtLmt +
                ", maxAmtLmt=" + maxAmtLmt +
                ", dlyAmtLmt=" + dlyAmtLmt +
                ", dlyCntLmt='" + dlyCntLmt + '\'' +
                ", mlyAmtLmt=" + mlyAmtLmt +
                ", mlyCntLmt='" + mlyCntLmt + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
