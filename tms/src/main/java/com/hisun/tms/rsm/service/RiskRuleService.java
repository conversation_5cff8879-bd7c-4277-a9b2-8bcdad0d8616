package com.hisun.tms.rsm.service;

import com.hisun.tms.rsm.model.RiskRuleModel;
import com.hisun.tms.rsm.model.RuleListModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * <AUTHOR>
 * @create 2017/8/24
 */
public interface RiskRuleService {

    DataTablesOutput<RiskRuleModel> findAll(DataTablesInput dataTablesInput);

    void save(RiskRuleModel riskRuleModel);

    void update(RiskRuleModel riskRuleModel);

    DataTablesOutput<RuleListModel> showList(DataTablesInput dataTablesInput);
}
