package com.hisun.tms.rsm.controller;

import com.hisun.tms.rsm.model.HighRiskModel;
import com.hisun.tms.rsm.service.HighRiskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@RequestMapping("/rsm/highrisk")
public class HighRiskController {

    private static final Logger logger = LoggerFactory.getLogger(HighRiskController.class);

    @Resource
    private HighRiskService highRiskService;

    @GetMapping
    @PreAuthorize("hasPermission('','/rsmmgr/highrisk') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rsm/highRisk/index");
        return modelAndView;
    }

    @GetMapping("/edit")
    @PreAuthorize("hasPermission('','/rsmmgr/highrisk') or hasRole('ROLE_ADMIN')")
    public ModelAndView showRile() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rsm/highRisk/edit");
        return modelAndView;
    }

    @PostMapping("/edit/upload")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/highrisk') or hasRole('ROLE_ADMIN')")
    public String editParam(@RequestParam("arg1") String parm1, @RequestParam("arg2") String parm2,
                            @RequestParam("arg3") String parm3, @RequestParam("arg4") String parm4,
                            @RequestParam("no") String no) {
        logger.info("parm: " + parm1 + "|" + parm2 + "|"+ parm3 + "|"+ parm4);
        highRiskService.updateArgs(parm1, parm2, parm3, parm4, no);
        return highRiskService.showArgs(no);
    }

    @GetMapping("/edit/reload")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/highrisk') or hasRole('ROLE_ADMIN')")
    public String loadParm(@RequestParam("no") String no) {
        return highRiskService.showArgs(no);
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/highrisk') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<HighRiskModel> findAll(@Valid @RequestBody DataTablesInput input) {
        return highRiskService.findAll(input);
    }
}