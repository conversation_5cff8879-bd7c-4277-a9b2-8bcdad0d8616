package com.hisun.tms.rsm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.rsm.model.WhiteListModel;
import com.hisun.tms.rsm.service.WhiteListService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

@Controller
@RequestMapping("/rsm/risklist/white")
public class WhiteListController {

    private static final Logger logger = LoggerFactory.getLogger(WhiteListController.class);

    @Resource
    private WhiteListService whiteListService;

    @GetMapping
    @PreAuthorize("hasPermission('','/rsmmgr/riskList/white') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rsm/risklist/white");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/rsmmgr/riskList/white') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<WhiteListModel> findAll(@Valid @RequestBody DataTablesInput input) {
        return whiteListService.findAll(input);
    }

    @PostMapping(value = "add")
    @PreAuthorize("hasPermission('','/rsmmgr/riskList/white') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<WhiteListModel> add(
            @Valid @RequestBody DatatablesEditorRequest<WhiteListModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, WhiteListModel> data = datatablesEditorRequest.getData();
        DatatablesEditorResponse<WhiteListModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        try {
            data.forEach((key, whiteListModel) -> {
                whiteListService.save(whiteListModel);
            });
        } catch (LemonException e) {
            if (JudgeUtils.isNotNull(e.getMsgInfo())) {
                datatablesEditorResponse.setError(e.getMsgInfo());
            } else {
                datatablesEditorResponse.setError(e.getMsgCd());
            }
        }
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "del")
    @PreAuthorize("hasPermission('','/rsmmgr/riskList/white') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<WhiteListModel> del(
            @Valid @RequestBody DatatablesEditorRequest<WhiteListModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, WhiteListModel> data = datatablesEditorRequest.getData();
        data.forEach((key, whiteListModel) -> {
            logger.debug(key + "..." + whiteListModel.toString());
            whiteListService.update(whiteListModel);
        });

        DatatablesEditorResponse<WhiteListModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }
}