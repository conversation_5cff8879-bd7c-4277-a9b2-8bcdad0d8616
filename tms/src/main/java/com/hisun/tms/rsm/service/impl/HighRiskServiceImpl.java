package com.hisun.tms.rsm.service.impl;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.rsm.model.HighRiskModel;
import com.hisun.tms.rsm.model.HighRiskParmModel;
import com.hisun.tms.rsm.repository.DataTablesHighRiskRepository;
import com.hisun.tms.rsm.repository.HighRiskRepository;
import com.hisun.tms.rsm.service.HighRiskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/9/2
 */
@Service
@Transactional("rsmTransactionManager")
public class HighRiskServiceImpl implements HighRiskService {

    private static final Logger logger = LoggerFactory.getLogger(HighRiskService.class);

    @Resource
    private DataTablesHighRiskRepository dataTablesHighRiskRepository;

    @Resource
    private HighRiskRepository highRiskRepository;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<HighRiskModel> findAll(DataTablesInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        DataTablesOutput<HighRiskModel> dataTablesOutput = dataTablesHighRiskRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    public void updateArgs(String arg1, String arg2, String arg3, String arg4, String name) {
        HighRiskParmModel highRiskParmModel = new HighRiskParmModel();
        highRiskParmModel.setParm1(arg1);
        highRiskParmModel.setParm2(arg2);
        highRiskParmModel.setParm3(arg3);
        highRiskParmModel.setParm4(arg4);
        highRiskParmModel.setUpdOprId(getOpr.getOperatorName());
        highRiskParmModel.setCreateTime(LocalDateTime.now());
        highRiskParmModel.setModifyTime(LocalDateTime.now());
        switch (name) {
            case "1":
                highRiskParmModel.setParmId("RsmRules001");
                highRiskParmModel.setCpnNm("RsmRules001");
                highRiskParmModel.setRiskDesc("RsmRules001");
                break;
            case "2":
                highRiskParmModel.setParmId("RsmRules002");
                highRiskParmModel.setCpnNm("RsmRules002");
                highRiskParmModel.setRiskDesc("RsmRules002");
                break;
            case "3":
                highRiskParmModel.setParmId("RsmRules004");
                highRiskParmModel.setCpnNm("RsmRules004");
                highRiskParmModel.setRiskDesc("RsmRules004");
                break;
            case "4":
                highRiskParmModel.setParmId("RsmRules005");
                highRiskParmModel.setCpnNm("RsmRules005");
                highRiskParmModel.setRiskDesc("RsmRules005");
                break;
            case "5":
                highRiskParmModel.setParmId("RsmRules006");
                highRiskParmModel.setCpnNm("RsmRules006");
                highRiskParmModel.setRiskDesc("RsmRules006");
                break;
            default:
                break;
        }
        highRiskRepository.save(highRiskParmModel);
    }

    @Override
    public String showArgs(String name) {
        HighRiskParmModel highRiskParmModel;
        if ("1".equals(name) || "2".equals(name)) {
            highRiskParmModel = highRiskRepository.findOne("RsmRules00" + name);
        } else {
            highRiskParmModel = highRiskRepository.findOne("RsmRules00" + String.valueOf(Integer.valueOf(name) + 1));
        }
        StringBuilder r = new StringBuilder();
        r = r.append(highRiskParmModel.getParm1()).append("|").append(highRiskParmModel.getParm2()).append("|")
                .append(highRiskParmModel.getParm3()).append("|").append(highRiskParmModel.getParm4());
        return r.toString();
    }
}
