package com.hisun.tms.rsm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.jcommon.encrypt.EncryptionUtils;
import com.hisun.lemon.jcommon.exception.EncryptException;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.rsm.common.RsmUtil;
import com.hisun.tms.rsm.model.WhiteListModel;
import com.hisun.tms.rsm.repository.DataTablesWhiteListRepository;
import com.hisun.tms.rsm.repository.WhiteListRepository;
import com.hisun.tms.rsm.service.BlackListService;
import com.hisun.tms.rsm.service.WhiteListService;
import com.hisun.tms.util.EncryptUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
@Service
@Transactional("rsmTransactionManager")
public class WhiteListServiceImpl implements WhiteListService {

    private static final Logger logger = LoggerFactory.getLogger(BlackListService.class);

    @Resource
    private DataTablesWhiteListRepository dataTablesWhiteListRepository;

    @Resource
    private WhiteListRepository whiteListRepository;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Resource
    private GetOpr getOpr;

    @Resource
    private EncryptUtils encryptUtils;

    @Override
    public DataTablesOutput<WhiteListModel> findAll(DataTablesInput dataTablesInput) {

        whiteListRepository.updateFlg();

        DataTablesOutput<WhiteListModel> dataTablesOutput = dataTablesWhiteListRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    public void save(WhiteListModel whiteListModel) {
        whiteListModel.setCreateTime(LocalDateTime.now());
        whiteListModel.setModifyTime(LocalDateTime.now());

        if (whiteListModel.getBeginDt().compareTo(whiteListModel.getEndDt()) > 0) {
            throw new LemonException("Error:The start time is later than the end time");
        }

        //判断证件类型，获取脱敏值
        whiteListModel.setIdHid(whiteListModel.getId());
        whiteListModel.setEffFlg(Constants.EFF_FLG_EFF);
        whiteListModel.setListSorc("UI");
        //插入前检索是否存在类型id以及生效的数据
        if (whiteListModel.getIdTyp().equals(Constants.ID_TYP_USER_NO)) {
            GenericRspDTO<UserBasicInfDTO> userInfo = userBasicInfClient.queryUser(whiteListModel.getId());
            if (JudgeUtils.isNotSuccess(userInfo.getMsgCd())) {
                throw new LemonException(userInfo.getMsgCd(), userInfo.getMsgInfo());
            }
        }

        //如果是银行卡时填充卡末4位
        if (whiteListModel.getIdTyp().equals(Constants.ID_TYP_CARD)) {
            whiteListModel.setCrdNoLast(RsmUtil.getCrdLastNo(whiteListModel.getId()));
        }
        //id字段身份证加密
        if (whiteListModel.getIdTyp().equals(Constants.ID_TYP_ID_NO)) {
            whiteListModel.setId(encryptUtils.encrypt(whiteListModel.getId(), "encrypt"));
        }

        whiteListModel.setWhtListId(RsmUtil.getUUID());
        whiteListModel.setUpdOprId(getOpr.getOperatorName());
        whiteListRepository.save(whiteListModel);
    }

    @Override
    public void update(WhiteListModel whiteListModel) {
        String rsn = whiteListModel.getListRsn();
        whiteListModel = whiteListRepository.findOne(whiteListModel.getWhtListId());
        whiteListModel.setEffFlg(Constants.EFF_FLG_EXP);
        whiteListModel.setListRsn(rsn);
        whiteListModel.setModifyTime(LocalDateTime.now());
        whiteListModel.setUpdOprId(getOpr.getOperatorName());
        whiteListRepository.save(whiteListModel);
    }
}
