package com.hisun.tms.rsm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/24
 */
@Entity
@Table(name = "rsm_rule_info")
public class RiskRuleModel {

    @Id
    @Column(name = "rule_id")
    private String ruleId;

    @Column(name = "rule_nm")
    private String ruleNm;

    @Column(name = "rule_desc")
    private String ruleDesc;

    @Column(name = "rule_typ")
    private String ruleTyp;

    @Column(name = "dc_pty_flg")
    private String dcPtyFlg;

    @Column(name = "cpn_nm")
    private String cpnNm;

    @Column(name = "upd_opr_id")
    private String updOprId;

    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime modifyTime;

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleNm() {
        return ruleNm;
    }

    public void setRuleNm(String ruleNm) {
        this.ruleNm = ruleNm;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getRuleTyp() {
        return ruleTyp;
    }

    public void setRuleTyp(String ruleTyp) {
        this.ruleTyp = ruleTyp;
    }

    public String getDcPtyFlg() {
        return dcPtyFlg;
    }

    public void setDcPtyFlg(String dcPtyFlg) {
        this.dcPtyFlg = dcPtyFlg;
    }

    public String getCpnNm() {
        return cpnNm;
    }

    public void setCpnNm(String cpnNm) {
        this.cpnNm = cpnNm;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "RiskRuleModel{" +
                "ruleId='" + ruleId + '\'' +
                ", ruleNm='" + ruleNm + '\'' +
                ", ruleDesc='" + ruleDesc + '\'' +
                ", ruleTyp='" + ruleTyp + '\'' +
                ", dcPtyFlg='" + dcPtyFlg + '\'' +
                ", cpnNm='" + cpnNm + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
