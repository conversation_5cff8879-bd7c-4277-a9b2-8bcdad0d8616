package com.hisun.tms.rsm.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.rsm.model.BlackListModel;
import com.hisun.tms.rsm.service.BlackListService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;
import java.util.jar.JarEntry;

@Controller
@RequestMapping("/rsm/risklist/black")
public class BlackListController {

    private static final Logger logger = LoggerFactory.getLogger(BlackListController.class);

    @Resource
    private BlackListService blackListService;

    @GetMapping
    @PreAuthorize("hasPermission('','/rsmmgr/riskList/black') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rsm/risklist/black");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/riskList/black') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<BlackListModel> findAll(@Valid @RequestBody DataTablesInput input) {
        return blackListService.findAll(input);
    }

    @PostMapping(value = "add")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/riskList/black') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<BlackListModel> add(
            @Valid @RequestBody DatatablesEditorRequest<BlackListModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, BlackListModel> data = datatablesEditorRequest.getData();
        DatatablesEditorResponse<BlackListModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        try {
            data.forEach((key, blackListModel) -> {
                blackListService.save(blackListModel);
            });
        } catch (LemonException e) {
            if (JudgeUtils.isNull(e.getMsgInfo())) {
                datatablesEditorResponse.setError(e.getMsgCd());
            } else {
                datatablesEditorResponse.setError(e.getMsgInfo());
            }
        }
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "del")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/riskList/black') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<BlackListModel> del(
            @Valid @RequestBody DatatablesEditorRequest<BlackListModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, BlackListModel> data = datatablesEditorRequest.getData();
        data.forEach((key, blackListModel) -> {
            logger.debug(key + "..." + blackListModel.toString());
            blackListService.update(blackListModel);
        });

        DatatablesEditorResponse<BlackListModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }
}