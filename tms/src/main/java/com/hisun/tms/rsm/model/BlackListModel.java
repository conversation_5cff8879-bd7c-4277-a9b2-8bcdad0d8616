package com.hisun.tms.rsm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
@Entity
@Table(name = "rsm_blk_list")
public class BlackListModel {

    @Id
    @Column(name = "blk_list_id")
    private String blkListId;

    @Column(name = "id_typ")
    private String idTyp;

    @Column(name = "id")
    private String id;

    @Column(name = "id_hid")
    private String idHid;

    @Column(name = "crd_no_last")
    private String crdNoLast;

    @Column(name = "tx_typ")
    private String txTyp;

    @Column(name = "begin_dt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime beginDt;

    @Column(name = "end_dt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime endDt;

    @Column(name = "eff_flg")
    private String effFlg;

    @Column(name = "blk_list_sorc")
    private String listSorc;

    @Column(name = "blk_list_rsn")
    private String listRsn;

    @Column(name = "upd_opr_id")
    private String updOprId;

    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime modifyTime;

    public String getBlkListId() {
        return blkListId;
    }

    public void setBlkListId(String blkListId) {
        this.blkListId = blkListId;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdHid() {
        return idHid;
    }

    public void setIdHid(String idHid) {
        this.idHid = idHid;
    }

    public String getCrdNoLast() {
        return crdNoLast;
    }

    public void setCrdNoLast(String crdNoLast) {
        this.crdNoLast = crdNoLast;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public LocalDateTime getBeginDt() {
        return beginDt;
    }

    public void setBeginDt(LocalDateTime beginDt) {
        this.beginDt = beginDt;
    }

    public LocalDateTime getEndDt() {
        return endDt;
    }

    public void setEndDt(LocalDateTime endDt) {
        this.endDt = endDt;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getListSorc() {
        return listSorc;
    }

    public void setListSorc(String listSorc) {
        this.listSorc = listSorc;
    }

    public String getListRsn() {
        return listRsn;
    }

    public void setListRsn(String listRsn) {
        this.listRsn = listRsn;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "BlackListModel{" +
                "blkListId='" + blkListId + '\'' +
                ", idTyp='" + idTyp + '\'' +
                ", id='" + id + '\'' +
                ", idHid='" + idHid + '\'' +
                ", crdNoLast='" + crdNoLast + '\'' +
                ", txTyp='" + txTyp + '\'' +
                ", beginDt=" + beginDt +
                ", endDt=" + endDt +
                ", effFlg='" + effFlg + '\'' +
                ", listSorc='" + listSorc + '\'' +
                ", listRsn='" + listRsn + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
