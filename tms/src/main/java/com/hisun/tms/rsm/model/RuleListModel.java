package com.hisun.tms.rsm.model;

import javax.persistence.*;
/**
 * <AUTHOR>
 * @create 2017/8/24
 */
@Entity
@Table(name = "rsm_rule_info")
public class RuleListModel {

    @Id
    @Column(name = "rule_id")
    private String recId;

    @Column(name = "rule_nm")
    private String ruleNm;

    public String getRecId() {
        return recId;
    }

    public void setRecId(String recId) {
        this.recId = recId;
    }

    public String getRuleNm() {
        return ruleNm;
    }

    public void setRuleNm(String ruleNm) {
        this.ruleNm = ruleNm;
    }

    @Override
    public String toString() {
        return "RuleListModel{" +
                "recId='" + recId + '\'' +
                ", ruleNm='" + ruleNm + '\'' +
                '}';
    }
}
