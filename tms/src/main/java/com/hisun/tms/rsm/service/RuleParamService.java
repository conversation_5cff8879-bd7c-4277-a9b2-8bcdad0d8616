package com.hisun.tms.rsm.service;

import com.hisun.tms.rsm.model.RuleParamModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
public interface RuleParamService {

    DataTablesOutput<RuleParamModel> findAll(DataTablesInput dataTablesInput);

    void save(RuleParamModel ruleParamModel);

    void delete(String parmId);
}
