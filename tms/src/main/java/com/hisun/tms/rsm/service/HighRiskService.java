package com.hisun.tms.rsm.service;

import com.hisun.tms.rsm.model.HighRiskModel;
import com.hisun.tms.rsm.model.HighRiskParmModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * <AUTHOR>
 * @create 2017/9/2
 */
public interface HighRiskService {

    DataTablesOutput<HighRiskModel> findAll(DataTablesInput dataTablesInput);

    void updateArgs(String arg1, String arg2, String arg3, String arg4, String name);

    String showArgs(String name);
}
