package com.hisun.tms.rsm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.rsm.common.RsmUtil;
import com.hisun.tms.rsm.model.BlackListModel;
import com.hisun.tms.rsm.repository.BlackListRepository;
import com.hisun.tms.rsm.repository.DataTablesBlackListRepository;
import com.hisun.tms.rsm.service.BlackListService;
import com.hisun.tms.util.EncryptUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
@Service
@Transactional("rsmTransactionManager")
public class BlackListServiceImpl implements BlackListService {

    private static final Logger logger = LoggerFactory.getLogger(BlackListService.class);

    @Resource
    private DataTablesBlackListRepository dataTablesBlackListRepository;

    @Resource
    private BlackListRepository blackListRepository;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Resource
    private GetOpr getOpr;

    @Resource
    private EncryptUtils encryptUtils;

    @Override
    public DataTablesOutput<BlackListModel> findAll(DataTablesInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);

        blackListRepository.updateFlg();

        DataTablesOutput<BlackListModel> dataTablesOutput = dataTablesBlackListRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    public void save(BlackListModel blackListModel) {
        blackListModel.setCreateTime(LocalDateTime.now());
        blackListModel.setModifyTime(LocalDateTime.now());

        if (blackListModel.getBeginDt().compareTo(blackListModel.getEndDt()) > 0) {
            throw new LemonException("Error:The start time is later than the end time");
        }

        //判断证件类型，获取脱敏值
        blackListModel.setIdHid(blackListModel.getId());
        blackListModel.setListSorc("UI");
        blackListModel.setEffFlg(Constants.EFF_FLG_EFF);
        //插入前检索是否存在类型id以及生效的数据
        if (blackListModel.getIdTyp().equals(Constants.ID_TYP_USER_NO)) {
            GenericRspDTO<UserBasicInfDTO> userInfo = userBasicInfClient.queryUser(blackListModel.getId());
            if (JudgeUtils.isNotSuccess(userInfo.getMsgCd())) {
                logger.error(userInfo.getMsgCd() + userInfo.getMsgInfo());
                throw new LemonException(userInfo.getMsgCd(), userInfo.getMsgInfo());
            }
        }

        //如果是银行卡时填充卡末4位
        if (blackListModel.getIdTyp().equals(Constants.ID_TYP_CARD)) {
            blackListModel.setCrdNoLast(RsmUtil.getCrdLastNo(blackListModel.getId()));
        }
        //id字段身份证加密
        if (blackListModel.getIdTyp().equals(Constants.ID_TYP_ID_NO)) {
            blackListModel.setId(encryptUtils.encrypt(blackListModel.getId(), "encrypt"));
        }

        blackListModel.setBlkListId(RsmUtil.getUUID());
        blackListModel.setUpdOprId(getOpr.getOperatorName());
        blackListRepository.save(blackListModel);
    }

    @Override
    public void update(BlackListModel blackListModel) {
        String rsn = blackListModel.getListRsn();
        blackListModel = blackListRepository.findOne(blackListModel.getBlkListId());
        blackListModel.setListRsn(rsn);
        blackListModel.setEffFlg(Constants.EFF_FLG_EXP);
        blackListModel.setModifyTime(LocalDateTime.now());
        blackListModel.setUpdOprId(getOpr.getOperatorName());
        blackListRepository.save(blackListModel);
    }
}
