package com.hisun.tms.rsm.service.impl;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.rsm.model.CheckRuleBo;
import com.hisun.tms.rsm.model.CheckRuleModel;
import com.hisun.tms.rsm.repository.CheckRuleRepository;
import com.hisun.tms.rsm.repository.DataTablesCheckRuleRepository;
import com.hisun.tms.rsm.service.CheckRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/25
 */
@Service
@Transactional("rsmTransactionManager")
public class CheckRuleServiceImpl implements CheckRuleService {

    private static final Logger logger = LoggerFactory.getLogger(CheckRuleService.class);

    @Resource
    private DataTablesCheckRuleRepository dataTablesCheckRuleRepository;

    @Resource
    private CheckRuleRepository checkRuleRepository;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<CheckRuleModel> findAll(DataTablesInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        DataTablesOutput<CheckRuleModel> dataTablesOutput = dataTablesCheckRuleRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    public void save(List<CheckRuleBo> list) {
        String txTyp = list.get(0).getTxTyp();
        String lmtLvl = list.get(0).getLmtLvl();
        StringBuilder desc = new StringBuilder();
        for (CheckRuleBo checkRuleBo : list) {
            logger.debug(checkRuleBo.toString());
            desc.append(checkRuleBo.getRuleId());
            desc.append(",");
            desc.append(checkRuleBo.getOprTyp());
            desc.append("|");
        }
        CheckRuleModel checkRuleModel = checkRuleRepository.findOne(txTyp);
        if (null == checkRuleModel) {
            checkRuleModel = new CheckRuleModel();
            checkRuleModel.setCheckId(txTyp);
            checkRuleModel.setCreateTime(LocalDateTime.now());
        }
        checkRuleModel.setTxTyp(txTyp);
        checkRuleModel.setLmtLvl(lmtLvl);
        checkRuleModel.setOprTyp("1");//废字段，已无用
        checkRuleModel.setModifyTime(LocalDateTime.now());
        checkRuleModel.setUpdOprId(getOpr.getOperatorName());
        checkRuleModel.setRuleDesc(desc.toString());
        checkRuleRepository.save(checkRuleModel);
    }

    @Override
    public void delete(String checkId) {
        checkRuleRepository.delete(checkId);
    }


    @Override
    public String findRuleDesc(String checkId) {
        CheckRuleModel checkRuleModel = checkRuleRepository.findOne(checkId);
        return checkRuleModel.getRuleDesc();
    }
}
