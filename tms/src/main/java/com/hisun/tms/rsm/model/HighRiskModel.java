package com.hisun.tms.rsm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
@Entity
@Table(name = "rsm_high_risk_user")
public class HighRiskModel {

    @Id
    @Column(name = "rec_id")
    private Integer recId;

    @Column(name = "tx_typ")
    private String txTyp;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "risk_opr")
    private String riskOpr;

    @Column(name = "risk_desc")
    private String riskDesc;

    @Column(name = "risk_parm")
    private String riskParm;

    @Column(name = "opr_sts")
    private String oprSts;

    @Column(name = "riskSorc")
    private String risk_sorc;

    @Column(name = "tx_ord_no")
    private String txOrdNo;

    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime modifyTime;

    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRiskOpr() {
        return riskOpr;
    }

    public void setRiskOpr(String riskOpr) {
        this.riskOpr = riskOpr;
    }

    public String getRiskDesc() {
        return riskDesc;
    }

    public void setRiskDesc(String riskDesc) {
        this.riskDesc = riskDesc;
    }

    public String getRiskParm() {
        return riskParm;
    }

    public void setRiskParm(String riskParm) {
        this.riskParm = riskParm;
    }

    public String getOprSts() {
        return oprSts;
    }

    public void setOprSts(String oprSts) {
        this.oprSts = oprSts;
    }

    public String getRisk_sorc() {
        return risk_sorc;
    }

    public void setRisk_sorc(String risk_sorc) {
        this.risk_sorc = risk_sorc;
    }

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "HighRiskModel{" +
                "recId=" + recId +
                ", txTyp='" + txTyp + '\'' +
                ", userId='" + userId + '\'' +
                ", riskOpr='" + riskOpr + '\'' +
                ", riskDesc='" + riskDesc + '\'' +
                ", riskParm='" + riskParm + '\'' +
                ", oprSts='" + oprSts + '\'' +
                ", risk_sorc='" + risk_sorc + '\'' +
                ", txOrdNo='" + txOrdNo + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
