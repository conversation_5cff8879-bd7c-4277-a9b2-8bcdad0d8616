package com.hisun.tms.rsm.controller;

import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.rsm.model.CheckRuleBo;
import com.hisun.tms.rsm.model.CheckRuleModel;
import com.hisun.tms.rsm.model.RuleListModel;
import com.hisun.tms.rsm.service.CheckRuleService;
import com.hisun.tms.rsm.service.RiskRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/21
 */
@Controller
@RequestMapping("/rsm/check/censor")
public class CheckRuleMngController {

    private static final Logger logger = LoggerFactory.getLogger(CheckRuleMngController.class);

    @Resource
    private RiskRuleService riskRuleService;

    @Resource
    private CheckRuleService checkRuleService;

    @GetMapping
    @PreAuthorize("hasPermission('','/rsmmgr/check/censor') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rsm/check/censor");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/censor') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<CheckRuleModel> findAll(@Valid @RequestBody DataTablesInput input) {
        return checkRuleService.findAll(input);
    }

    @GetMapping("/add")
    @PreAuthorize("hasPermission('','/rsmmgr/check/censor') or hasRole('ROLE_ADMIN')")
    public ModelAndView addCheckRule(String checkId, String txTyp, String lmtLvl) {
        logger.debug("checkId: " + checkId);
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rsm/check/editCheckRule");
        modelAndView.getModel().put("checkRuleId", checkId);
        modelAndView.getModel().put("txTyp", txTyp);
        modelAndView.getModel().put("lmtLvl", lmtLvl);
        return modelAndView;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/censor') or hasRole('ROLE_ADMIN')")
    public String saveCheckRule(@RequestBody List<CheckRuleBo> bo) {
        checkRuleService.save(bo);
        return "success";
    }

    @GetMapping(value = "/info")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/censor') or hasRole('ROLE_ADMIN')")
    public String info(String checkId) {
        return checkRuleService.findRuleDesc(checkId);
    }

    @PostMapping(value = "findList")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/censor') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<RuleListModel> showAllList(@Valid @RequestBody DataTablesInput input) {
        return riskRuleService.showList(input);
    }

    @PostMapping(value = "delete")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/censor') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<CheckRuleModel> del(
            @Valid @RequestBody DatatablesEditorRequest<CheckRuleModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, CheckRuleModel> data = datatablesEditorRequest.getData();
        data.forEach((key, checkRuleModel) -> {
            logger.debug(key + "..." + checkRuleModel.toString());
            checkRuleService.delete(checkRuleModel.getCheckId());
        });

        DatatablesEditorResponse<CheckRuleModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }
}
