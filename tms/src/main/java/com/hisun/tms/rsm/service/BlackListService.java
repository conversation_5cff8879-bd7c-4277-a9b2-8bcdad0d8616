package com.hisun.tms.rsm.service;

import com.hisun.lemon.jcommon.exception.EncryptException;
import com.hisun.tms.rsm.model.BlackListModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
public interface BlackListService {

    DataTablesOutput<BlackListModel> findAll(DataTablesInput dataTablesInput);

    void save(BlackListModel blackListModel);

    void update(BlackListModel blackListModel);
}
