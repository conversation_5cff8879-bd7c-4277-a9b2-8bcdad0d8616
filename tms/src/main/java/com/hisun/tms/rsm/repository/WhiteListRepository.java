package com.hisun.tms.rsm.repository;

import com.hisun.tms.rsm.model.WhiteListModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface WhiteListRepository extends JpaRepository<WhiteListModel, String> {

    @Modifying
    @Query("update WhiteListModel list set list.effFlg = '1' where list.endDt < current_date")
    void updateFlg();
}
