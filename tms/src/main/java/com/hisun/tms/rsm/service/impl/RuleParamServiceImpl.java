package com.hisun.tms.rsm.service.impl;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.rsm.common.RsmUtil;
import com.hisun.tms.rsm.model.RuleParamModel;
import com.hisun.tms.rsm.repository.DataTablesRuleParamRepository;
import com.hisun.tms.rsm.repository.RuleParamRepository;
import com.hisun.tms.rsm.service.RuleParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
@Service
@Transactional("rsmTransactionManager")
public class RuleParamServiceImpl implements RuleParamService {

    private static final Logger logger = LoggerFactory.getLogger(RuleParamService.class);

    @Resource
    private DataTablesRuleParamRepository dataTablesRuleParamRepository;

    @Resource
    private RuleParamRepository ruleParamRepository;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<RuleParamModel> findAll(DataTablesInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        DataTablesOutput<RuleParamModel> dataTablesOutput = dataTablesRuleParamRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    public void save(RuleParamModel ruleParamModel) {
        RuleParamModel temp = ruleParamRepository.findOne(ruleParamModel.getParmId());
        if (temp != null) {
            ruleParamModel.setCreateTime(temp.getCreateTime());
        }
        if (ruleParamModel.getParmId() == null || ruleParamModel.getParmId().isEmpty()) {
            ruleParamModel.setParmId(RsmUtil.getUUID());
            ruleParamModel.setCreateTime(LocalDateTime.now());
        }
        ruleParamModel.setModifyTime(LocalDateTime.now());
        ruleParamModel.setUpdOprId(getOpr.getOperatorName());
        ruleParamRepository.save(ruleParamModel);
    }

    @Override
    public void delete(String parmId) {
        ruleParamRepository.delete(parmId);
    }

}
