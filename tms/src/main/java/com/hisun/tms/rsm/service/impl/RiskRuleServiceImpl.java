package com.hisun.tms.rsm.service.impl;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.rsm.model.RiskRuleModel;
import com.hisun.tms.rsm.model.RuleListModel;
import com.hisun.tms.rsm.repository.DataTablesRiskRuleRepository;
import com.hisun.tms.rsm.repository.DataTablesRuleListRepository;
import com.hisun.tms.rsm.repository.RiskRuleRepository;
import com.hisun.tms.rsm.service.RiskRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/24
 */
@Service
@Transactional("rsmTransactionManager")
public class RiskRuleServiceImpl implements RiskRuleService {

    private static final Logger logger = LoggerFactory.getLogger(RiskRuleService.class);

    @Resource
    private DataTablesRiskRuleRepository dataTablesRiskRuleRepository;

    @Resource
    private DataTablesRuleListRepository dataTablesRuleListRepository;

    @Resource
    private RiskRuleRepository riskRuleRepository;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<RiskRuleModel> findAll(DataTablesInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        DataTablesOutput<RiskRuleModel> dataTablesOutput = dataTablesRiskRuleRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    public void save(RiskRuleModel riskRuleModel) {
        riskRuleModel.setCreateTime(LocalDateTime.now());
        riskRuleModel.setModifyTime(LocalDateTime.now());
        riskRuleModel.setUpdOprId(getOpr.getOperatorName());
        riskRuleRepository.save(riskRuleModel);
    }

    @Override
    public void update(RiskRuleModel riskRuleModel) {
        RiskRuleModel temp = riskRuleRepository.findOne(riskRuleModel.getRuleId());
        temp.setModifyTime(LocalDateTime.now());
        temp.setCpnNm(riskRuleModel.getCpnNm());
        temp.setDcPtyFlg(riskRuleModel.getDcPtyFlg());
        temp.setRuleDesc(riskRuleModel.getRuleDesc());
        temp.setRuleNm(riskRuleModel.getRuleNm());
        temp.setRuleTyp(riskRuleModel.getRuleTyp());
        temp.setUpdOprId(getOpr.getOperatorName());
        riskRuleRepository.save(temp);
    }

    @Override
    public DataTablesOutput<RuleListModel> showList(DataTablesInput dataTablesInput) {
        logger.debug("datatable server-side input {}", dataTablesInput);
        DataTablesOutput<RuleListModel> dataTablesOutput = dataTablesRuleListRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }
}
