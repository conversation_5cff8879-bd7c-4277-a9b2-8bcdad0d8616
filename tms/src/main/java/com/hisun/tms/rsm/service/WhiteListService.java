package com.hisun.tms.rsm.service;

import com.hisun.lemon.jcommon.exception.EncryptException;
import com.hisun.tms.rsm.model.WhiteListModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * <AUTHOR>
 * @create 2017/8/18
 */
public interface WhiteListService {

    DataTablesOutput<WhiteListModel> findAll(DataTablesInput dataTablesInput);

    void save(WhiteListModel whiteListModel);

    void update(WhiteListModel whiteListModel);
}
