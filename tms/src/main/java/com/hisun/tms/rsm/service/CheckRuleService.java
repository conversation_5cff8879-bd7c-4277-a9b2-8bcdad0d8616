package com.hisun.tms.rsm.service;

import com.hisun.tms.rsm.model.CheckRuleBo;
import com.hisun.tms.rsm.model.CheckRuleModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/8/25
 */
public interface CheckRuleService {

    DataTablesOutput<CheckRuleModel> findAll(DataTablesInput dataTablesInput);

    void save(List<CheckRuleBo> list);

    void delete(String checkId);

    String findRuleDesc(String checkId);
}
