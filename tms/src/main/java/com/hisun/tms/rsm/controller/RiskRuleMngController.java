package com.hisun.tms.rsm.controller;

import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.rsm.model.RiskRuleModel;
import com.hisun.tms.rsm.service.RiskRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/21
 */
@Controller
@RequestMapping("/rsm/check/rule")
public class RiskRuleMngController {

    private static final Logger logger = LoggerFactory.getLogger(RiskRuleMngController.class);

    @Resource
    private RiskRuleService riskRuleService;

    @GetMapping
    @PreAuthorize("hasPermission('','/rsmmgr/check/rule') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("rsm/check/riskRule");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/rule') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<RiskRuleModel> findAll(@Valid @RequestBody DataTablesInput input) {
        return riskRuleService.findAll(input);
    }

    @PostMapping(value = "add")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/rule') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<RiskRuleModel> add(
            @Valid @RequestBody DatatablesEditorRequest<RiskRuleModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, RiskRuleModel> data = datatablesEditorRequest.getData();
        data.forEach((key, riskRuleModel) -> {
            logger.debug(key + "..." + riskRuleModel.toString());
            riskRuleService.save(riskRuleModel);
        });
        DatatablesEditorResponse<RiskRuleModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "edit")
    @ResponseBody
    @PreAuthorize("hasPermission('','/rsmmgr/check/rule') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<RiskRuleModel> edit(
            @Valid @RequestBody DatatablesEditorRequest<RiskRuleModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, RiskRuleModel> data = datatablesEditorRequest.getData();
        data.forEach((key, riskRuleModel) -> {
            logger.debug(key + "..." + riskRuleModel.toString());
            riskRuleService.update(riskRuleModel);
        });
        DatatablesEditorResponse<RiskRuleModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }
}
