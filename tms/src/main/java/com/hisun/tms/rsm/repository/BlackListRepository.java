package com.hisun.tms.rsm.repository;

import com.hisun.tms.rsm.model.BlackListModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface BlackListRepository extends JpaRepository<BlackListModel, String> {

    @Modifying
    @Query("update BlackListModel list set list.effFlg = '1' where list.endDt < current_timestamp")
    void updateFlg();
}
