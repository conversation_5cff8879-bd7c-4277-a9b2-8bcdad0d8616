package com.hisun.tms.rsm.common;

import com.hisun.lemon.rsm.Constants;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 2017/8/19
 */
public class RsmUtil {
    /**
     * 获取手机号码脱敏值
     *
     * @param mblNo 手机号码
     * @return 手机号码脱敏值
     */
    public static String getMblNoHid(String mblNo) {
        String[] str = mblNo.split("-");
        StringBuffer mblHid = new StringBuffer();
        mblHid.append(str[0]).append("-").append(str[1].substring(0, 3))
                .append("****").append(str[1].substring(7));
        return mblHid.toString();
    }

    /**
     * 获取银行卡号，身份证脱敏值
     *
     * @param crdNoOrIdNo 卡号
     * @return
     */
    public static String getcrdNoOrIdNoHid(String crdNoOrIdNo) {
        StringBuffer hid = new StringBuffer();
        int len = crdNoOrIdNo.length();
        hid.append(crdNoOrIdNo.substring(0, 4));
        for (int i = 0; i < len - 8; i++) {
            hid.append("*");
        }
        hid.append(crdNoOrIdNo.substring(len - 4));
        return hid.toString();
    }

    /**
     * 获取银行卡号倒数4位
     *
     * @param crdNo
     * @return
     */
    public static String getCrdLastNo(String crdNo) {
        int len = crdNo.length();
        return crdNo.substring(len - 4);
    }


    /**
     * 根据id和idtyp获取脱敏值
     *
     * @param id
     * @param idTyp
     * @return
     */
    public static String getIdHid(String id, String idTyp) {
        if (Constants.ID_TYP_MBL_NO.equals(idTyp)) {
            return RsmUtil.getMblNoHid(id);
        } else if (Constants.ID_TYP_CARD.equals(idTyp)) {
            return RsmUtil.getcrdNoOrIdNoHid(id);
        } else if (Constants.ID_TYP_ID_NO.equals(idTyp)) {
            return RsmUtil.getcrdNoOrIdNoHid(id);
        } else {
            return id;
        }
    }

    /**
     * 获取UUID作为主键
     *
     * @return
     */
    public static String getUUID() {
        String s = UUID.randomUUID().toString();
        return s.substring(0, 8) + s.substring(9, 13) + s.substring(14, 18) + s.substring(19, 23) + s.substring(24);
    }
}
