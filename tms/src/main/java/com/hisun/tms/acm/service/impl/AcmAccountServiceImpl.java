package com.hisun.tms.acm.service.impl;

import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.tms.acm.dao.IAcmAcBalDao;
import com.hisun.tms.acm.model.AcmAcBal;
import com.hisun.tms.acm.model.AcmAcInf;
import com.hisun.tms.acm.repository.AcmAcBalRepository;
import com.hisun.tms.acm.repository.AcmAcInfRepository;
import com.hisun.tms.acm.service.AcmAccountService;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.demo.service.BalanceTagUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("acmAccountServiceImpl")
public class AcmAccountServiceImpl implements AcmAccountService {

    private static final Logger logger = LoggerFactory.getLogger(AcmAccountServiceImpl.class);

    @Resource
    AcmAcInfRepository acmAcInfRepository;

    @Resource
    AcmAcBalRepository acmAcBalRepository;

    @Resource
    IAcmAcBalDao iAcmAcBalDao;

    @Override
    public List<AcmAcInf> getAllPendingAcc() {
        return acmAcInfRepository.getAllPendingAcc();
    }
    
    @Override
    public List<AcmAcInf> findByCondition(String acNo, String acSts) {
        return acmAcInfRepository.findByCondition(acNo, acSts);
    }

    @Override
    public Map<String, String> checkAccount(String checkResult, String acNo) {
        Map<String, String> result = new HashMap<String, String>();
        result.put("msgCd","1");
        AcmAcInf acmAcInf = acmAcInfRepository.findOne(acNo);
        if(JudgeUtils.isNull(acmAcInf)){
            result.put("msgCd","0");
            result.put("msgInfo","不存在该开户申请");
            return result;
        }
        acmAcInf.setAcNo(acNo);
        acmAcInf.setAcSts(checkResult);
        acmAcInfRepository.save(acmAcInf);
        if(JudgeUtils.equals(checkResult, ACMConstants.AC_REJECT_STS)) {
            result.put("msgInfo","法币开户申请不通过");
        } else if(JudgeUtils.equals(checkResult, ACMConstants.AC_OPEN_STS)) {
            result.put("msgInfo","法币开户申请通过");
            createUserAccountBalance(acmAcInf);
        }
        return result;
    }

    public void createUserAccountBalance(AcmAcInf acmAcInf) {

        logger.info(acmAcInf.toString());
        AcmAcBal acmAcBal = new AcmAcBal();
        acmAcBal.setAC_NO(acmAcInf.getAcNo());
        acmAcBal.setCcy(acmAcInf.getCcy());
        acmAcBal.setUserId(acmAcInf.getUserId());
        acmAcBal.setAcCurBal(BigDecimal.valueOf(0, 2));
        acmAcBal.setAcUavaBal(BigDecimal.valueOf(0, 2));
        acmAcBal.setAcLastBal(BigDecimal.valueOf(0, 2));
        acmAcBal.setAcLastUavaBal(BigDecimal.valueOf(0, 2));
        acmAcBal.setAcUpdDt(DateTimeUtils.getCurrentLocalDate());
        acmAcBal.setAcUpdTm(DateTimeUtils.getCurrentLocalTime());
        for (CapTypEnum capTypEnum : CapTypEnum.values()) {
            acmAcBal.setCapTyp(capTypEnum.getCapTyp());
            String balTag = BalanceTagUtil.createBalanceTag(acmAcBal.getAC_NO(),
                    capTypEnum.getCapTyp(), BigDecimal.valueOf(0, 2));
            acmAcBal.setAcBalTag(balTag);
            iAcmAcBalDao.insert(acmAcBal);
        }
    }
}
