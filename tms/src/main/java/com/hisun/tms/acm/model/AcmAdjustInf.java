package com.hisun.tms.acm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @function AcmAdjustInf
 * @description 手工调账汇总信息表
 * @date 8/29/2017 Tue
 * @time 8:50 PM
 */
@Entity
@Table(name = "acm_adjust_inf")
public class AcmAdjustInf {

    @Id
    @Column(name = "jrn_no")
    private String jrnNo;

    @Column(name = "ac_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    private LocalDate acDt;

    @Column(name = "reg_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    private LocalDate regDt;

    @Column(name = "tot_tx_amt")
    private BigDecimal totTxAmt;

    @Column(name = "tot_tx_num")
    private int totTxNum;

    @Column(name = "audit_sts")
    private String auditSts;

    @Column(name = "result")
    private String result;

    @Column(name = "reg_opr")
    private String regOpr;

    @Column(name = "upd_opr")
    private String updOpr;

    @Column(name = "rmk")
    private String rmk;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime modifyTime;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public LocalDate getRegDt() {
        return regDt;
    }

    public void setRegDt(LocalDate regDt) {
        this.regDt = regDt;
    }

    public BigDecimal getTotTxAmt() {
        return totTxAmt;
    }

    public void setTotTxAmt(BigDecimal totTxAmt) {
        this.totTxAmt = totTxAmt;
    }

    public int getTotTxNum() {
        return totTxNum;
    }

    public void setTotTxNum(int totTxNum) {
        this.totTxNum = totTxNum;
    }

    public String getAuditSts() {
        return auditSts;
    }

    public void setAuditSts(String auditSts) {
        this.auditSts = auditSts;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRegOpr() {
        return regOpr;
    }

    public void setRegOpr(String regOpr) {
        this.regOpr = regOpr;
    }

    public String getUpdOpr() {
        return updOpr;
    }

    public void setUpdOpr(String updOpr) {
        this.updOpr = updOpr;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}
