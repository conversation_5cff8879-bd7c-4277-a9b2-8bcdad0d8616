package com.hisun.tms.acm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @function AcmAdjustDetail
 * @description 手工调账明细表
 * @date 8/29/2017 Tue
 * @time 8:49 PM
 */
@Entity
@Table(name = "acm_adjust_detail")
@IdClass(PrimaryKeyAcmAdjustDetail.class)
public class AcmAdjustDetail implements Serializable {
    @Id
    private String jrnNo;

    @Id
    private String jrnSeq;

    @Column(name = "seq")
    private String seq;

    @Column(name = "reg_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    private LocalDate regDt;

    @Column(name = "ac_typ")
    private String acTyp;

    @Column(name = "ac_no")
    private String acNo;

    @Column(name = "ac_nm")
    private String acNm;

    @Column(name = "cap_typ")
    private String capTyp;

    @Column(name = "dc_flg")
    private String dcFlg;

    @Column(name = "tx_amt")
    private BigDecimal txAmt;

    @Column(name = "cur_bal")
    private BigDecimal curBal;

    @Column(name = "rmk")
    private String rmk;

    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime modifyTime;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getJrnSeq() {
        return jrnSeq;
    }

    public void setJrnSeq(String jrnSeq) {
        this.jrnSeq = jrnSeq;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public LocalDate getRegDt() {
        return regDt;
    }

    public void setRegDt(LocalDate regDt) {
        this.regDt = regDt;
    }

    public String getAcTyp() {
        return acTyp;
    }

    public void setAcTyp(String acTyp) {
        this.acTyp = acTyp;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getAcNm() {
        return acNm;
    }

    public void setAcNm(String acNm) {
        this.acNm = acNm;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getDcFlg() {
        return dcFlg;
    }

    public void setDcFlg(String dcFlg) {
        this.dcFlg = dcFlg;
    }

    public BigDecimal getTxAmt() {
        return txAmt;
    }

    public void setTxAmt(BigDecimal txAmt) {
        this.txAmt = txAmt;
    }

    public BigDecimal getCurBal() {
        return curBal;
    }

    public void setCurBal(BigDecimal curBal) {
        this.curBal = curBal;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}
