package com.hisun.tms.acm.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.hisun.tms.acm.model.AcmItmInf;
import com.hisun.tms.acm.model.AcmItmProperty;
import com.hisun.tms.acm.model.AcmItmWithProperty;
import com.hisun.tms.acm.repository.AcmItmInfRepository;
import com.hisun.tms.acm.repository.AcmItmPropertyRepository;
import com.hisun.tms.acm.repository.AdjustInfInput;
import com.hisun.tms.acm.repository.DatatablesAcmItmPropertyRepository;
import com.hisun.tms.acm.service.AcmItmPropertyService;
import com.hisun.tms.cmm.common.Constants;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.specifications.Specifications;

@Service("acmItmPropertyService")
public class AcmItmPropertyServiceImpl implements AcmItmPropertyService {
	
	private static final Logger logger = LoggerFactory.getLogger(AcmItmPropertyServiceImpl.class);

    @Autowired
    private DatatablesAcmItmPropertyRepository datatablesAcmItmPropertyRepository;
    @Autowired
    private AcmItmPropertyRepository acmItmPropertyRepository;
    @Autowired
    private AcmItmInfRepository acmItmInfRepository;
	@Override
	public void save(Iterable<AcmItmWithProperty> iterable) {
		User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    	AcmItmWithProperty acmItmWithProperty = iterable.iterator().next();
    	acmItmWithProperty.setUpdOpr(user.getUsername());
        AcmItmInf acmItmInf=acmItmInfRepository.getOne(iterable.iterator().next().getItmNo());
        if(JudgeUtils.isNotNull(acmItmInf)){
        	acmItmInf.setUpdOpr(user.getUsername());
        	acmItmInf.setBalDrt(acmItmWithProperty.getBalDrt());
        	acmItmInf.setBalOdFlg(acmItmWithProperty.getBalOdFlg());
        	acmItmInf.setUpdBalFlg(acmItmWithProperty.getUpdBalFlg());
        	acmItmInfRepository.save(acmItmInf);
        }
        AcmItmProperty acmItmProperty=new AcmItmProperty();
        BeanUtils.copyProperties(acmItmWithProperty, acmItmProperty);
		acmItmPropertyRepository.save(acmItmProperty);
	}

	@Override
	public Map<String, AcmItmProperty> modify(Map<String, AcmItmWithProperty> exampleMap) {
		User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		List<AcmItmWithProperty> acmItmInfs = exampleMap.values().stream().collect(Collectors.toList());
        logger.debug("recive from web: {}", acmItmInfs.toString());
        Collection<String> ids = Collections2.transform(acmItmInfs, new Function<AcmItmWithProperty, String>() {
            @Override
            public String apply(final AcmItmWithProperty acmItmWithProperty) {
                return acmItmWithProperty.getItmNo();
            }
        });
        logger.debug("query params: {}", ids.toString());
        List<AcmItmProperty> acmItmInfToUpdate = acmItmPropertyRepository.findAll(ids);
        logger.debug("query from db: {}", acmItmInfToUpdate.toString());
        for (int i = 0; i < acmItmInfs.size(); i++) {
            BeanUtils.copyProperties(acmItmInfs.get(i), acmItmInfToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
            acmItmInfToUpdate.get(i).setUpdOpr(user.getUsername());
            AcmItmInf acmItmInf=acmItmInfRepository.getOne(acmItmInfToUpdate.get(i).getItmNo());
            if(JudgeUtils.isNotNull(acmItmInf)){
            	acmItmInf.setUpdOpr(user.getUsername());
            	acmItmInf.setBalDrt(acmItmInfs.get(i).getBalDrt());
            	acmItmInf.setBalOdFlg(acmItmInfs.get(i).getBalOdFlg());
            	acmItmInf.setUpdBalFlg(acmItmInfs.get(i).getUpdBalFlg());
            	acmItmInfRepository.save(acmItmInf);
            }
        }
        logger.debug("after copy with audit column ignore: {}", acmItmInfToUpdate.toString());
        List<AcmItmProperty> list = acmItmPropertyRepository.save(acmItmInfToUpdate);
        logger.debug("after update: {}", list.toString());
        Map<String, AcmItmProperty> map = list.stream()
                .collect(Collectors.toMap(AcmItmProperty::getItmNo, java.util.function.Function.identity()));
        Map<String, AcmItmProperty> mapData = map.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
	}

	@Override
	public Map<String, String> delete(Iterable<AcmItmProperty> entities) {
		Map<String,String> map=new HashMap<String,String>();
		acmItmPropertyRepository.delete(entities);
		return map;
	}

	@Override
	@Transactional(value="acmTransactionManager",readOnly=true)
	public DataTablesOutput<AcmItmWithProperty> findAll(AdjustInfInput input) {
		String itmNo = input.getExtra_search().get("itmNo");
        String capTyp = input.getExtra_search().get("capTyp");
        Specification<AcmItmProperty> orderQueryParam=Specifications.<AcmItmProperty>and()
                .eq(JudgeUtils.isNotBlank(itmNo), "itmNo", itmNo)
                .eq(JudgeUtils.isNotBlank(capTyp), "capTyp", capTyp)
                .build();
        DataTablesOutput<AcmItmProperty> dataTablesAcmItmOutput = datatablesAcmItmPropertyRepository.findAll(input, orderQueryParam);
        List<AcmItmProperty> acmItmPropertyList=dataTablesAcmItmOutput.getData();
        AcmItmInf acmItmInf=null;
        List<AcmItmWithProperty> acmItmWithPropertyList=new ArrayList<AcmItmWithProperty>();
        AcmItmWithProperty acmItmWithProperty=null;
        if(JudgeUtils.isNotEmpty(acmItmPropertyList)){
        	for(AcmItmProperty acmItmProperty:acmItmPropertyList){
        		acmItmInf=acmItmInfRepository.getOne(acmItmProperty.getItmNo());
        		acmItmWithProperty=new AcmItmWithProperty();
        		BeanUtils.copyProperties(acmItmProperty, acmItmWithProperty);
        		acmItmWithProperty.setBalDrt(acmItmInf.getBalDrt());
        		acmItmWithProperty.setBalOdFlg(acmItmInf.getBalOdFlg());
        		acmItmWithProperty.setUpdBalFlg(acmItmInf.getUpdBalFlg());
        		acmItmWithPropertyList.add(acmItmWithProperty);
        		
        	}
        }
        DataTablesOutput<AcmItmWithProperty> dataTablesOutput=new DataTablesOutput<AcmItmWithProperty>();
        dataTablesOutput.setData(acmItmWithPropertyList);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
	}

	@Override
	public String checkItmNo(String itmNoVal) {
		String result="SUCCESS";
		AcmItmInf acmItmInf=acmItmInfRepository.findOne(itmNoVal);
		if(JudgeUtils.isNull(acmItmInf)){
			result="ITMINFNULL";
		}
		AcmItmProperty acmItmProperty=acmItmPropertyRepository.findOne(itmNoVal);
		if(JudgeUtils.isNotNull(acmItmProperty)){
			result="ITMPROEXIT";
		}
		return result;
	}

}
