package com.hisun.tms.acm.controller;





import com.hisun.tms.acm.model.AcmItmInf;
import com.hisun.tms.acm.repository.AdjustInfInput;
import com.hisun.tms.acm.service.AcmItmInfService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;
import java.util.Map;

/**
 * Created by zh on 2017/7/17.
 */
@Controller
@RequestMapping("/acm/item/iteminf")
public class AcmItmInfController {

    private static final Logger logger = LoggerFactory.getLogger(AcmItmInfController.class);

    @Autowired
    private AcmItmInfService acmItmInfService;

    @GetMapping
    @PreAuthorize("hasPermission('','/csmmgr/item/iteminf') or hasRole('ROLE_ADMIN')")
    public ModelAndView AcmItmInfList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("acm/item/iteminf/iteminf");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/csmmgr/item/iteminf') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<AcmItmInf> findAll(@Valid @RequestBody AdjustInfInput input) {
        return acmItmInfService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/csmmgr/item/iteminf') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<AcmItmInf> add(@Valid @RequestBody DatatablesEditorRequest<AcmItmInf> datatablesEditorRequest) {
        Iterable<AcmItmInf> iterable = datatablesEditorRequest.getData().values();
        acmItmInfService.save(iterable);
        DatatablesEditorResponse<AcmItmInf> datatablesEditorResponse = new DatatablesEditorResponse<AcmItmInf>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/csmmgr/item/iteminf') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<AcmItmInf> modify(@Valid @RequestBody DatatablesEditorRequest<AcmItmInf> datatablesEditorRequest) {
        Map<String, AcmItmInf> AcmItmInfMap = datatablesEditorRequest.getData();
        Map<String, AcmItmInf> mapData = acmItmInfService.modify(AcmItmInfMap);
        DatatablesEditorResponse<AcmItmInf> datatablesEditorResponse = new DatatablesEditorResponse<AcmItmInf>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/csmmgr/item/iteminf') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> delete(@Valid @RequestBody DatatablesEditorRequest<AcmItmInf> datatablesEditorRequest) {
        Iterable<AcmItmInf> iterable = datatablesEditorRequest.getData().values();
        return acmItmInfService.delete(iterable);
    }
    
    @PostMapping(value = "checkItmNo")
    @ResponseBody
    public String  checkItmNo(@RequestParam ("itmNoVal") String itmNoVal,@RequestParam ("upItmNoVal") String upItmNoVal ) {
        return acmItmInfService.checkItmNo(itmNoVal,upItmNoVal);
    }
    
}
