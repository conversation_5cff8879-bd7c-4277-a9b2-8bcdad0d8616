package com.hisun.tms.acm.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function IAcmTotChkInfDao
 * @description 总分不平数据访问层
 * @date 10/24/2017 Tue
 * @time 3:52 PM
 */
@Mapper
public interface IAcmTotChkInfDao {
    public List<Map> queryTotChkInf(@Param("acDt") LocalDate acDt);
}
