package com.hisun.tms.acm.service;



import java.util.Map;


import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.acm.model.AcmItmProperty;
import com.hisun.tms.acm.model.AcmItmWithProperty;
import com.hisun.tms.acm.repository.AdjustInfInput;


/**
 * Created by zh on 2017/7/17.
 */
public interface AcmItmPropertyService {

    void save(Iterable<AcmItmWithProperty> iterable);

    Map<String, AcmItmProperty> modify(Map<String, AcmItmWithProperty> exampleMap);

    Map<String, String> delete(Iterable<AcmItmProperty> entities);

    DataTablesOutput<AcmItmWithProperty> findAll(AdjustInfInput input);

	String checkItmNo(String itmNoVal);

}