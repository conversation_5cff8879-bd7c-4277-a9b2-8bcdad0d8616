package com.hisun.tms.acm.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import com.hisun.tms.acm.dao.IAcmVoucherInfDao;
import com.hisun.tms.acm.model.AcmVoucherInf;
import com.hisun.tms.acm.repository.VoucherParamInput;
import com.hisun.tms.acm.service.IAcmVoucherService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @function AcmVoucherServiceImpl
 * @description 传票服务实现
 * @date 11/6/2017 Mon
 * @time 5:02 PM
 */
@Service("AcmVoucherQuery")
@Transactional(rollbackFor = Exception.class, readOnly = true)
public class AcmVoucherServiceImpl implements IAcmVoucherService {
    public static final Logger logger = LoggerFactory.getLogger(AcmVoucherServiceImpl.class);
    @Resource
    private IAcmVoucherInfDao iAcmVoucherInfDao;

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Resource
    private AccountManagementClient accountManagementClient;

    @Override
    public DataTablesOutput findAll(VoucherParamInput input) {
        DataTablesOutput<AcmVoucherInf> dataTablesOutput = new DataTablesOutput();
        List<AcmVoucherInf> list = new ArrayList<>();
        int total = 0;
        String userId = input.getExtra_search().get("userId").trim();
        String mblNo = input.getExtra_search().get("mblNo").trim();
        String jrnNo = input.getExtra_search().get("jrnNo").trim();
        String queryDtS = input.getExtra_search().get("queryDtS").trim();
        String queryDtE = input.getExtra_search().get("queryDtE").trim();
        LocalDate startDt = null;
        LocalDate endDt = null;
        String acNo = null;
        if (JudgeUtils.isNotBlank(mblNo)) {
            GenericRspDTO<UserBasicInfDTO> basicInfDTO = userBasicInfClient.queryUserByLoginId(mblNo);
            if (JudgeUtils.isNotNull(basicInfDTO)) {
                if(JudgeUtils.isSuccess(basicInfDTO.getMsgCd())){
                    if (JudgeUtils.isNotBlank(userId)) {
                        if (JudgeUtils.notEquals(basicInfDTO.getBody().getUserId(), userId)) {
                            dataTablesOutput.setError("");
                        }
                    } else {
                        userId = basicInfDTO.getBody().getUserId();
                    }
                    if (JudgeUtils.isNotBlank(userId)) {
                        acNo = accountManagementClient.queryAcNo(userId).getBody();
                    }
                    if (JudgeUtils.isNotBlank(queryDtS)) {
                        startDt = LocalDate.parse(queryDtS);
                    } else {
                        startDt = LocalDate.now();
                    }
                    if (JudgeUtils.isNotBlank(queryDtE)) {
                        endDt = LocalDate.parse(queryDtE);
                    } else {
                        endDt = LocalDate.now();
                    }
                    list = iAcmVoucherInfDao.findVoucherInf(jrnNo, acNo, startDt, endDt, input.getStart(), input.getLength());
                    total = iAcmVoucherInfDao.countVoucherNum(jrnNo, acNo, startDt, endDt);
                }
            }
        }
        if (list == null || list.size() <= 0 ) {
            dataTablesOutput.setData(new ArrayList<AcmVoucherInf>());
        } else {
            dataTablesOutput.setData(list);
        }
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(total);
        return dataTablesOutput;
    }
}
