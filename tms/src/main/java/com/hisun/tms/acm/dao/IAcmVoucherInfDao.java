package com.hisun.tms.acm.dao;

import com.hisun.tms.acm.model.AcmVoucherInf;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function IAcmVoucherInfDao
 * @description 传票汇总访问数据对象
 * @date 10/24/2017 Tue
 * @time 4:48 PM
 */
@Mapper
public interface IAcmVoucherInfDao {
    /**
     * 根据会计日查询传票信息
     * @param acDt
     * @return
     */
    public List<Map> queryVoucherInf(@Param("acDt") LocalDate acDt);

    /**
     * 查询传票信息
     * @param jrnNo
     * @param acNo
     * @param startDt
     * @param endDt
     * @param pageBeg
     * @param pageEnd
     * @return
     */
    public List<AcmVoucherInf> findVoucherInf(@Param("jrnNo") String jrnNo, @Param("acNo") String acNo,
                                              @Param("startDt") LocalDate startDt, @Param("endDt") LocalDate endDt,
                                              @Param("pageBeg") Integer pageBeg, @Param("pageEnd") Integer pageEnd);

    /**
     * 统计传票总数
     * @param jrnNo
     * @param acNo
     * @param startDt
     * @param endDt
     * @return
     */
    public int countVoucherNum(@Param("jrnNo") String jrnNo, @Param("acNo") String acNo,
                               @Param("startDt") LocalDate startDt, @Param("endDt") LocalDate endDt);
}
