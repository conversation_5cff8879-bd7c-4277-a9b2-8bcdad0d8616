package com.hisun.tms.acm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Date;

@Entity
@Table(name = "acm_ac_inf")
public class AcmAcInf {
    @Id
    @Column(name = "AC_NO", nullable = false)
    private String acNo; // 账号

    @Column(name = "CCY", nullable = false)
    private String ccy; // 币种

    @Column(name = "CCY_TYPE", nullable = false)
    private String ccyType; // 币种类型FM-法币DM-数币

    @Column(name = "USER_ID", nullable = false)
    private String userId; // 用户ID

    @Column(name = "AC_STS", nullable = false)
    private String acSts; // 账户状态 0:开户 1:销户

    @Column(name = "AC_CRE_DT", nullable = false)
    private Date acCreDt; // 账户创建日期

    @Column(name = "AC_CRE_TM", nullable = false)
    private Time acCreTm; // 账户创建时间

    @Column(name = "AC_CLS_DT")
    private Date acClsDt; // 账户销户日期

    @Column(name = "AC_CLS_TM")
    private Time acClsTm; // 账户销户时间

    @Column(name = "MODIFY_TIME", nullable = false)
    private Timestamp modifyTime; // 更新时间

    @Column(name = "CREATE_TIME", nullable = false)
    private Timestamp createTime; // 创建时间

    @Column(name = "BANK")
    private String bank; // 开户行/机构

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCcyType() {
        return ccyType;
    }

    public void setCcyType(String ccyType) {
        this.ccyType = ccyType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAcSts() {
        return acSts;
    }

    public void setAcSts(String acSts) {
        this.acSts = acSts;
    }

    public Date getAcCreDt() {
        return acCreDt;
    }

    public void setAcCreDt(Date acCreDt) {
        this.acCreDt = acCreDt;
    }

    public Time getAcCreTm() {
        return acCreTm;
    }

    public void setAcCreTm(Time acCreTm) {
        this.acCreTm = acCreTm;
    }

    public Date getAcClsDt() {
        return acClsDt;
    }

    public void setAcClsDt(Date acClsDt) {
        this.acClsDt = acClsDt;
    }

    public Time getAcClsTm() {
        return acClsTm;
    }

    public void setAcClsTm(Time acClsTm) {
        this.acClsTm = acClsTm;
    }

    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }
}
