package com.hisun.tms.acm.utils;

import com.hisun.tms.bil.model.ExcelCommonTempleModel;
import com.hisun.tms.common.util.JudgeUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2017/7/12
 * @time 17:30
 */
public class ExcelFileUtil {
    public static void createExcel(List<Object[]> datas,ExcelCommonTempleModel model){
        String  path=model.getSavePath();
        String fileName=model.getSaveFileName();
        File file= new File(path+fileName);
        OutputStream out=  null;
        try {
            HSSFWorkbook workbook = new HSSFWorkbook(); // 创建工作簿对象
            HSSFSheet sheet = workbook.createSheet(model.getSheetName()); // 创建工作表
            // 产生表格标题行
            if(datas != null && !datas.isEmpty()){
                sheet.addMergedRegion(new CellRangeAddress(0, 1, 0,datas.get(0).length - 1));
            }
            HSSFRow rowm = sheet.createRow(0);
            rowm.setHeightInPoints(40);
            HSSFCell cellTiltle = rowm.createCell(0);

            HSSFCellStyle columnTopStyle = getColumnTopStyle(workbook,true,true);
            HSSFCellStyle style = getTitleStyle(workbook);
            cellTiltle.setCellStyle(style);
            cellTiltle.setCellValue(model.getTitle());

            //增加表头
            if(JudgeUtils.isNotNull(model.getTableHeader())){
                int i=0;
                for (String key : model.getTableHeader().keySet()) {
                    if(datas != null && !datas.isEmpty()){
                        sheet.addMergedRegion(new CellRangeAddress(2+i, 2+i, 1,datas.get(0).length - 1));
                    }
                    HSSFRow headRow = sheet.createRow(2+i);
                    headRow.setHeightInPoints(25);
                    HSSFCell headRowCell = headRow.createCell(0);
                    headRowCell.setCellValue(key);
                    HSSFCell headValueCell = headRow.createCell(1);
                    headValueCell.setCellValue(model.getTableHeader().get(key));
                    i++;
                }
            }

            int startRowNum=2+model.getTableHeader().size();    //标题2行 + 表头行数

            // 定义所需列数
            HSSFRow rowRowName = sheet.createRow(startRowNum); // 在索引2的位置创建行(最顶端的行开始的第二行)
            rowRowName.setHeightInPoints(25);
            // 将列头设置到sheet的单元格中
            int n=0;
            for (String key : model.getRowName().keySet()) {
                HSSFCell cellRowName = rowRowName.createCell(n); // 创建列头对应个数的单元格
                cellRowName.setCellType(CellType.STRING); // 设置列头单元格的数据类型
                HSSFRichTextString text = new HSSFRichTextString(key);
                cellRowName.setCellValue(text); // 设置列头单元格的值

                cellRowName.setCellStyle(columnTopStyle); // 设置列头单元格样式
                sheet.setColumnWidth(n, Integer.valueOf(model.getRowName().get(key)+""));
                n++;
            }
            startRowNum++;

            HSSFCellStyle dataCellStyle  = getColumnTopStyle(workbook,false,false);
            // 将查询出的数据设置到sheet对应的单元格中
            for (int i = 0; i < datas.size(); i++) {
                Object[] obj = datas.get(i);// 遍历每个对象
                HSSFRow row = sheet.createRow(i + startRowNum);// 创建所需的行数（从第三行开始写数据）
                row.setHeightInPoints(25);
                for (int j = 0; j < obj.length; j++) {
                    HSSFCell cell;
                    if (obj[j] != null) {
                        if(obj[j] instanceof  String){
                            cell = row.createCell(j, CellType.STRING);
                            cell.setCellValue(obj[j].toString());
                        }else if(obj[j] instanceof BigDecimal){
                            cell = row.createCell(j, CellType.NUMERIC);
                            cell.setCellValue(((BigDecimal)obj[j]).intValue());
                        }else{
                            cell = row.createCell(j, CellType.NUMERIC);
                            cell.setCellValue((double)obj[j]);
                        }
                        cell.setCellStyle(dataCellStyle);
                    }

                }
            }

            startRowNum+=datas.size();
//            HSSFCellStyle sumaryCellStyle = workbook.createCellStyle();
//            sumaryCellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            HSSFCellStyle sumaryCellStyle  = getColumnTopStyle(workbook,false,true);
            //汇总信息
            if(JudgeUtils.isNotNull(model.getSumaryData())){

                for (String key : model.getSumaryData().keySet()) {
                    HSSFRow sumaryRow = sheet.createRow(startRowNum++);
                    HSSFCell sumaryCell = sumaryRow.createCell(0);
                    sumaryCell.setCellValue(key);
                    sumaryRow.setHeightInPoints(25);
                    sumaryCell.setCellStyle(dataCellStyle);
                    Map<Integer,Object> d=model.getSumaryData().get(key);
                    for (Integer key2 : d.keySet()) {
                        HSSFCell sCell=sumaryRow.createCell(key2);
                        d.get(key2).getClass().cast(d.get(key2));
                        if( d.get(key2) instanceof  BigDecimal){
                            sCell.setCellValue(((BigDecimal) d.get(key2)).doubleValue());
                        }else if(d.get(key2) instanceof  String){
                            sCell.setCellValue((String) d.get(key2));
                        }else{
                            sCell.setCellValue((Double) d.get(key2));
                        }
                    }

                    for(int i=0;i<n;i++){
                        HSSFCell cell=sumaryRow.getCell(i);
                        if(cell==null){
                            sumaryRow.createCell(i);
                        }
                        sumaryRow.getCell(i).setCellStyle(sumaryCellStyle);
                        sumaryCellStyle.setFillForegroundColor(HSSFColor.LEMON_CHIFFON.index);
                        sumaryCellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
                    }
                }

            }

            if (workbook != null) {
                try {
                   out= new FileOutputStream(file);
                    workbook.write(out);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try{
                out.close();
            }catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    /*
     * 列头单元格样式
     */
    private static HSSFCellStyle getColumnTopStyle(HSSFWorkbook workbook,boolean setFont,boolean backColor) {
        // 设置样式;
        HSSFCellStyle style = workbook.createCellStyle();
//        style.setBorderBottom(BorderStyle.);
        // 设置底边框;
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        // 设置左边框;
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        // 设置右边框;
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(HSSFColor.BLACK.index);
        // 设置顶边框;
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(HSSFColor.BLACK.index);

        if(setFont){
            // 设置字体
            HSSFFont font = workbook.createFont();
            // 设置字体大小
            font.setFontHeightInPoints((short) 11);
            // 字体加粗
            font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
            // 设置字体名字
            font.setFontName("Courier New");
            style.setFont(font);
        }

        // 设置自动换行;
        style.setWrapText(false);
        // 设置水平对齐的样式为居中对齐;
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 设置垂直对齐的样式为居中对齐;
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        if(backColor){
            style.setFillForegroundColor(HSSFColor.LEMON_CHIFFON.index);
            style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        }


        return style;

    }

    private static HSSFCellStyle getTitleStyle(HSSFWorkbook workbook) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        // 设置字体大小
        font.setFontHeightInPoints((short)18);
        // 字体加粗
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 设置字体名字
        font.setFontName("Courier New");
        // 设置样式;
        HSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(HSSFColor.BLACK.index);
        // 设置左边框;
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(HSSFColor.BLACK.index);
        // 设置右边框;
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(HSSFColor.BLACK.index);
        // 设置顶边框;
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(HSSFColor.BLACK.index);
        // 在样式用应用设置的字体;
        style.setFont(font);
        // 设置自动换行;
        style.setWrapText(false);
        // 设置水平对齐的样式为居中对齐;
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 设置垂直对齐的样式为居中对齐;
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        return style;

    }

    private static void main(String[] args){
        Map map=new HashMap();
        map.put(2,20.02);
        map.put(3,20.12);

        Map ds=new HashMap<>();
        ds.put("汇总",map);
        ds.put("小计",map);

        LinkedHashMap titleMap=new LinkedHashMap<>();
        titleMap.put("机构名称","AAA");
        titleMap.put("统计日期","标题行2");

        LinkedHashMap columHeader=new LinkedHashMap<>();
        columHeader.put("业务类型",3200);
        columHeader.put("交易类型",3300);
        columHeader.put("订单总笔数",4200);
        columHeader.put("成功总笔数",4200);

        columHeader.put("订单失败总笔数",4200);
        columHeader.put("订单转化率",3900);

        ExcelCommonTempleModel excelCommonTempleModel=new ExcelCommonTempleModel(
                "e:/","日报表2.xls","测试SheetName","测试日报表",titleMap, columHeader,ds
        );
        ExcelFileUtil baseReportService=new ExcelFileUtil();


        List data=new ArrayList();
        data.add(new String[]{"ASD","SSSA","11","23","11","11"});
        baseReportService.createExcel(data,excelCommonTempleModel);
    }
}
