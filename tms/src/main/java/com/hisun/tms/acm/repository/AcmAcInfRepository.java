package com.hisun.tms.acm.repository;

import com.hisun.tms.acm.model.AcmAcInf;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AcmAcInfRepository extends JpaRepository<AcmAcInf,String> {

    @Query(value = "select * from acm_ac_inf where AC_STS = '2' and CCY_TYPE = 'FM'", nativeQuery = true)
    List<AcmAcInf> getAllPendingAcc();
    
    @Query(value = "select * from acm_ac_inf where (:acNo is null or AC_NO like CONCAT('%',:acNo,'%')) and (:acSts is null or :acSts = '' or AC_STS = :acSts)", nativeQuery = true)
    List<AcmAcInf> findByCondition(@Param("acNo") String acNo, @Param("acSts") String acSts);

}
