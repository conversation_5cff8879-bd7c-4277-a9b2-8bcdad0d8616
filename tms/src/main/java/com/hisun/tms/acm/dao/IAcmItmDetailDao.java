package com.hisun.tms.acm.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function IAcmItmDetailDao
 * @description 科目明细数据访问对象
 * @date 10/24/2017 Tue
 * @time 2:51 PM
 */
@Mapper
public interface IAcmItmDetailDao {
    public List<Map> queryItmDetail(@Param("acDt") LocalDate acDt);
}
