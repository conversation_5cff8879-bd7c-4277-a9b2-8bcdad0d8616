package com.hisun.tms.acm.model;




/**
 * Created by zh on 2017/11/03.
 * (底层科目属性)
 */
public class AcmItmWithProperty {
	/**
	 * itmNo 科目号
	 */
    private String itmNo;

    /**
     * @Fields capTyp 资金类型
     */
    private String capTyp;

    /**
     * @Fields CCY 币种
     */
    private String ccy;
    
    /**
     * @Fields balDrt 余额方向 （A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）
     */
    private String balDrt;
    
    
    /**
     * @Fields updOpr 更新操作员
     */
    private String updOpr;
    
    /**
     * @Fields balOdFlg 余额是否允许透支标识
     */
    private String balOdFlg;
    
    /**
     * @Fields updBalFlg 余额更新方式 0：实时更新1：批量更新
     */
    private String updBalFlg;


	public String getItmNo() {
		return itmNo;
	}


	public void setItmNo(String itmNo) {
		this.itmNo = itmNo;
	}


	public String getCapTyp() {
		return capTyp;
	}


	public void setCapTyp(String capTyp) {
		this.capTyp = capTyp;
	}


	public String getCcy() {
		return ccy;
	}


	public void setCcy(String ccy) {
		this.ccy = ccy;
	}


	public String getBalDrt() {
		return balDrt;
	}


	public void setBalDrt(String balDrt) {
		this.balDrt = balDrt;
	}


	public String getUpdOpr() {
		return updOpr;
	}


	public void setUpdOpr(String updOpr) {
		this.updOpr = updOpr;
	}


	public String getBalOdFlg() {
		return balOdFlg;
	}


	public void setBalOdFlg(String balOdFlg) {
		this.balOdFlg = balOdFlg;
	}


	public String getUpdBalFlg() {
		return updBalFlg;
	}


	public void setUpdBalFlg(String updBalFlg) {
		this.updBalFlg = updBalFlg;
	}

}
