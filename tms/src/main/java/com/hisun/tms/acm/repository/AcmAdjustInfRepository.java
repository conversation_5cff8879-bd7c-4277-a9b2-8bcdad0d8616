package com.hisun.tms.acm.repository;

import com.hisun.tms.acm.model.AcmAdjustInf;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @function AcmAdjustInfRepository
 * @description 调账汇总信息
 * @date 8/29/2017 Tue
 * @time 9:19 PM
 */
public interface AcmAdjustInfRepository extends JpaRepository<AcmAdjustInf, String> {
//    List<AcmAdjustInf> findAllByRegDtBetween(LocalDate startDate, LocalDate endDate);

//    List<AcmAdjustInf> findAllByRegOprAndRegDtBetween(String regOpr, LocalDate startDate, LocalDate endDate);
}
