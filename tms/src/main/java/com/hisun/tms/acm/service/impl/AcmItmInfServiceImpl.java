package com.hisun.tms.acm.service.impl;

import java.util.Collection;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.hisun.tms.acm.model.AcmItmInf;
import com.hisun.tms.acm.model.AcmItmProperty;
import com.hisun.tms.acm.repository.AcmItmInfRepository;
import com.hisun.tms.acm.repository.AdjustInfInput;
import com.hisun.tms.acm.repository.DatatablesAcmItmInfRepository;
import com.hisun.tms.acm.service.AcmItmInfService;
import com.hisun.tms.cmm.common.Constants;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.specifications.Specifications;
@Service("acmItmInfService")
public class AcmItmInfServiceImpl implements AcmItmInfService {
	
	private static final Logger logger = LoggerFactory.getLogger(AcmItmInfServiceImpl.class);

    @Autowired
    private DatatablesAcmItmInfRepository datatablesAcmItmInfRepository;
    @Autowired
    private AcmItmInfRepository acmItmInfRepository;
	@Override
	public void save(Iterable<AcmItmInf> iterable) {
		User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        for (Iterator<AcmItmInf> iterator = iterable.iterator(); iterator.hasNext();) {
        	AcmItmInf acmItmInf = (AcmItmInf) iterator.next();
        	acmItmInf.setUpdOpr(user.getUsername());
        	acmItmInf.setUpdBalFlg("0");
        }
		acmItmInfRepository.save(iterable);
	}

	@Override
	public Map<String, AcmItmInf> modify(Map<String, AcmItmInf> exampleMap) {
		User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		List<AcmItmInf> acmItmInfs = exampleMap.values().stream().collect(Collectors.toList());
        logger.debug("recive from web: {}", acmItmInfs.toString());
        Collection<String> ids = Collections2.transform(acmItmInfs, new Function<AcmItmInf, String>() {
            @Override
            public String apply(final AcmItmInf acmItmInf) {
                return acmItmInf.getItmNo();
            }
        });
        logger.debug("query params: {}", ids.toString());
        List<AcmItmInf> acmItmInfToUpdate = acmItmInfRepository.findAll(ids);
        logger.debug("query from db: {}", acmItmInfToUpdate.toString());
        for (int i = 0; i < acmItmInfs.size(); i++) {
            BeanUtils.copyProperties(acmItmInfs.get(i), acmItmInfToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
            acmItmInfToUpdate.get(i).setUpdOpr(user.getUsername());
        }
        logger.debug("after copy with audit column ignore: {}", acmItmInfToUpdate.toString());
        List<AcmItmInf> list = acmItmInfRepository.save(acmItmInfToUpdate);
        logger.debug("after update: {}", list.toString());
        Map<String, AcmItmInf> map = list.stream()
                .collect(Collectors.toMap(AcmItmInf::getItmNo, java.util.function.Function.identity()));
        Map<String, AcmItmInf> mapData = map.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
	}

	@Override
	public Map<String, String> delete(Iterable<AcmItmInf> entities) {
		Map<String,String> map=new HashMap<String,String>();
		acmItmInfRepository.delete(entities);
		return map;
	}

	@Override
	@Transactional("acmTransactionManager")
	public DataTablesOutput<AcmItmInf> findAll(AdjustInfInput input) {
		String itmNo = input.getExtra_search().get("itmNo");
        String itmCnm = input.getExtra_search().get("itmCnm");
        String itmTyp = input.getExtra_search().get("itmTyp");
        String itmCls = input.getExtra_search().get("itmCls");
        Specification<AcmItmInf> orderQueryParam=Specifications.<AcmItmInf>and()
                .eq(JudgeUtils.isNotBlank(itmNo), "itmNo", itmNo)
                .eq(JudgeUtils.isNotBlank(itmCnm), "itmCnm", itmCnm)
                .eq(JudgeUtils.isNotBlank(itmTyp), "itmTyp", itmTyp)
                .eq(JudgeUtils.isNotBlank(itmCls), "itmCls", itmCls)
                .build();
        DataTablesOutput<AcmItmInf> dataTablesOutput = datatablesAcmItmInfRepository.findAll(input, orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
	}
	
	@Override
	public String checkItmNo(String itmNoVal, String upItmNoVal) {
		String result="SUCCESS";
		AcmItmInf acmItmInf=acmItmInfRepository.findOne(itmNoVal);
		if(JudgeUtils.isNotNull(acmItmInf)){
			result="ITMINFEXIT";
		}
		if(JudgeUtils.isNotBlank(upItmNoVal)){
			AcmItmInf uAcmItmInf=acmItmInfRepository.findOne(upItmNoVal);
			if(JudgeUtils.isNull(uAcmItmInf)){
				result="UITMINFNOTEXIT";
			}
		}
		return result;
	}

}
