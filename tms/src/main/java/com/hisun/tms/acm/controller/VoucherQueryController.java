package com.hisun.tms.acm.controller;

import com.hisun.tms.acm.model.AcmVoucherInf;
import com.hisun.tms.acm.repository.VoucherParamInput;
import com.hisun.tms.acm.service.IAcmVoucherService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @function VoucherQueryController
 * @description 传票记录查询
 * @date 11/6/2017 Mon
 * @time 3:02 PM
 */
@Controller
@RequestMapping("/acm/voucherquery")
public class VoucherQueryController {
    private static final Logger logger = LoggerFactory.getLogger(VoucherQueryController.class);
    @Resource
    private IAcmVoucherService voucherService;

    @GetMapping
    @PreAuthorize("hasPermission('','/csmmgr/voucherquery') or hasRole('ROLE_ADMIN')")
    public ModelAndView voucherQuery() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("acm/voucherquery/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/csmmgr/voucherquery') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<AcmVoucherInf> findAll(@Valid @RequestBody VoucherParamInput input) {
        return voucherService.findAll(input);
    }
}
