package com.hisun.tms.acm.service.impl;

import com.hisun.tms.acm.dao.IAccountAddressDao;
import com.hisun.tms.acm.model.AccountAddressDO;
import com.hisun.tms.acm.service.AccountAddressService;
import com.hisun.tms.common.datatables.QueryFindInput;
import com.hisun.tms.common.util.GetOpr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账户地址管理服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/26
 */
@Service("accountAddressService")
@Transactional
public class AccountAddressServiceImpl implements AccountAddressService {

    private static final Logger logger = LoggerFactory.getLogger(AccountAddressServiceImpl.class);

    @Resource
    private IAccountAddressDao accountAddressDao;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<AccountAddressDO> findAll(QueryFindInput input) {
        logger.debug("查询账户地址列表，参数: {}", input);
        
        DataTablesOutput<AccountAddressDO> dataTablesOutput = new DataTablesOutput<>();

        try {
            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            
            // 处理搜索条件
            if (input.getExtraSearch() != null) {
                Map<String, String> extraSearch = input.getExtraSearch();
                
                // 金库编码
                if (StringUtils.hasText(extraSearch.get("vaultCode"))) {
                    params.put("vaultCode", extraSearch.get("vaultCode").trim());
                }
                
                // 地址
                if (StringUtils.hasText(extraSearch.get("address"))) {
                    params.put("address", extraSearch.get("address").trim());
                }
                
                // 网络
                if (StringUtils.hasText(extraSearch.get("network"))) {
                    params.put("network", extraSearch.get("network").trim());
                }
                
                // 状态
                if (StringUtils.hasText(extraSearch.get("status"))) {
                    params.put("status", extraSearch.get("status").trim());
                }
            }

            // 分页参数
            params.put("start", input.getStart());
            params.put("length", input.getLength());

            // 查询总记录数
            int total = accountAddressDao.countAll(params);
            
            // 查询分页数据
            List<AccountAddressDO> list = accountAddressDao.findAll(params);

            // 设置DataTablesOutput属性
            dataTablesOutput.setDraw(input.getDraw());
            dataTablesOutput.setRecordsTotal(total);
            dataTablesOutput.setRecordsFiltered(total);
            dataTablesOutput.setData(list != null ? list : new ArrayList<>());

            logger.debug("查询账户地址列表成功，总记录数: {}, 当前页记录数: {}", total, list != null ? list.size() : 0);

        } catch (Exception e) {
            logger.error("查询账户地址列表失败", e);
            dataTablesOutput.setDraw(input.getDraw());
            dataTablesOutput.setRecordsTotal(0);
            dataTablesOutput.setRecordsFiltered(0);
            dataTablesOutput.setData(new ArrayList<>());
        }

        return dataTablesOutput;
    }

    @Override
    public AccountAddressDO getDetail(Long id) {
        logger.debug("查询账户地址详情，ID: {}", id);
        
        if (id == null) {
            logger.warn("查询账户地址详情失败：ID为空");
            return null;
        }

        try {
            AccountAddressDO result = accountAddressDao.selectById(id);
            if (result != null) {
                logger.debug("查询账户地址详情成功，ID: {}, 地址: {}", id, result.getAddress());
            } else {
                logger.warn("未找到账户地址记录，ID: {}", id);
            }
            return result;
        } catch (Exception e) {
            logger.error("查询账户地址详情失败，ID: {}", id, e);
            return null;
        }
    }

    @Override
    public String add(AccountAddressDO accountAddress) {
        logger.debug("添加账户地址，地址: {}", accountAddress != null ? accountAddress.getAddress() : "null");
        
        if (accountAddress == null) {
            logger.warn("添加账户地址失败：参数为空");
            return "ACM10001"; // 参数错误
        }

        try {
            // 验证必填字段
            String validationResult = validateRequiredFields(accountAddress);
            if (!"ACM00000".equals(validationResult)) {
                return validationResult;
            }

            // 验证字段格式
            validationResult = validateFieldFormats(accountAddress);
            if (!"ACM00000".equals(validationResult)) {
                return validationResult;
            }

            // 验证字段长度
            validationResult = validateFieldLengths(accountAddress);
            if (!"ACM00000".equals(validationResult)) {
                return validationResult;
            }

            // 验证状态值
            if (!isValidStatus(accountAddress.getStatus())) {
                logger.warn("添加账户地址失败：状态值无效: {}", accountAddress.getStatus());
                return "ACM10007"; // 状态值无效
            }

            // 验证用途类型（如果提供）
            if (StringUtils.hasText(accountAddress.getUseType()) && !isValidUseType(accountAddress.getUseType())) {
                logger.warn("添加账户地址失败：用途类型无效: {}", accountAddress.getUseType());
                return "ACM10008"; // 用途类型无效
            }

            // 验证地址唯一性
            if (!validateAddress(accountAddress.getAddress())) {
                logger.warn("添加账户地址失败：地址已存在: {}", accountAddress.getAddress());
                return "ACM10009"; // 地址已存在
            }

            // 设置创建时间和更新时间
            Date currentDate = new Date();
            accountAddress.setCreateTime(currentDate);
            accountAddress.setUpdateTime(currentDate);

            // 执行插入操作
            int result = accountAddressDao.insert(accountAddress);
            
            if (result == 1) {
                logger.info("添加账户地址成功，地址: {}", accountAddress.getAddress());
                return "ACM00000"; // 成功
            } else {
                logger.error("添加账户地址失败：数据库操作返回结果: {}", result);
                return "ACM10010"; // 数据库操作失败
            }

        } catch (Exception e) {
            logger.error("添加账户地址失败，地址: {}", accountAddress.getAddress(), e);
            return "ACM10011"; // 系统错误
        }
    }

    @Override
    public boolean validateAddress(String address) {
        logger.debug("验证地址唯一性，地址: {}", address);
        
        if (!StringUtils.hasText(address)) {
            logger.debug("地址为空，验证失败");
            return false;
        }

        try {
            AccountAddressDO existing = accountAddressDao.selectByAddress(address.trim());
            boolean isValid = (existing == null);
            
            logger.debug("地址唯一性验证结果，地址: {}, 可用: {}", address, isValid);
            return isValid;
            
        } catch (Exception e) {
            logger.error("验证地址唯一性失败，地址: {}", address, e);
            return false;
        }
    }

    /**
     * 验证必填字段
     * 
     * @param accountAddress 账户地址对象
     * @return 验证结果代码
     */
    private String validateRequiredFields(AccountAddressDO accountAddress) {
        if (!StringUtils.hasText(accountAddress.getVaultCode())) {
            logger.warn("添加账户地址失败：金库编码为空");
            return "ACM10002"; // 金库编码不能为空
        }
        
        if (!StringUtils.hasText(accountAddress.getGroupCode())) {
            logger.warn("添加账户地址失败：账户组编码为空");
            return "ACM10003"; // 账户组编码不能为空
        }
        
        if (accountAddress.getAccountId() == null) {
            logger.warn("添加账户地址失败：账户ID为空");
            return "ACM10004"; // 账户ID不能为空
        }
        
        if (!StringUtils.hasText(accountAddress.getAddress())) {
            logger.warn("添加账户地址失败：地址为空");
            return "ACM10005"; // 地址不能为空
        }
        
        if (!StringUtils.hasText(accountAddress.getStatus())) {
            logger.warn("添加账户地址失败：状态为空");
            return "ACM10006"; // 状态不能为空
        }

        return "ACM00000"; // 验证通过
    }

    /**
     * 验证字段格式
     * 
     * @param accountAddress 账户地址对象
     * @return 验证结果代码
     */
    private String validateFieldFormats(AccountAddressDO accountAddress) {
        // 验证金库编码格式
        if (!accountAddress.getVaultCode().matches("^[a-zA-Z0-9_-]+$")) {
            logger.warn("添加账户地址失败：金库编码格式无效: {}", accountAddress.getVaultCode());
            return "ACM10012"; // 金库编码格式无效
        }

        // 验证账户组编码格式
        if (!accountAddress.getGroupCode().matches("^[a-zA-Z0-9_-]+$")) {
            logger.warn("添加账户地址失败：账户组编码格式无效: {}", accountAddress.getGroupCode());
            return "ACM10013"; // 账户组编码格式无效
        }

        // 验证账户ID范围
        if (accountAddress.getAccountId() <= 0) {
            logger.warn("添加账户地址失败：账户ID必须大于0: {}", accountAddress.getAccountId());
            return "ACM10014"; // 账户ID必须大于0
        }

        // 验证地址格式（不能包含空格）
        if (accountAddress.getAddress().contains(" ")) {
            logger.warn("添加账户地址失败：地址不能包含空格: {}", accountAddress.getAddress());
            return "ACM10015"; // 地址格式无效
        }

        return "ACM00000"; // 验证通过
    }

    /**
     * 验证字段长度
     * 
     * @param accountAddress 账户地址对象
     * @return 验证结果代码
     */
    private String validateFieldLengths(AccountAddressDO accountAddress) {
        // 验证金库编码长度
        if (accountAddress.getVaultCode().length() > 64) {
            logger.warn("添加账户地址失败：金库编码过长: {}", accountAddress.getVaultCode().length());
            return "ACM10016"; // 金库编码过长
        }

        // 验证账户组编码长度
        if (accountAddress.getGroupCode().length() > 64) {
            logger.warn("添加账户地址失败：账户组编码过长: {}", accountAddress.getGroupCode().length());
            return "ACM10017"; // 账户组编码过长
        }

        // 验证地址长度
        if (accountAddress.getAddress().length() < 10 || accountAddress.getAddress().length() > 100) {
            logger.warn("添加账户地址失败：地址长度无效: {}", accountAddress.getAddress().length());
            return "ACM10018"; // 地址长度无效
        }

        // 验证网络长度
        if (StringUtils.hasText(accountAddress.getNetwork()) && accountAddress.getNetwork().length() > 64) {
            logger.warn("添加账户地址失败：网络名称过长: {}", accountAddress.getNetwork().length());
            return "ACM10019"; // 网络名称过长
        }

        // 验证对应账号长度
        if (StringUtils.hasText(accountAddress.getAcmAcNo()) && accountAddress.getAcmAcNo().length() > 64) {
            logger.warn("添加账户地址失败：对应账号过长: {}", accountAddress.getAcmAcNo().length());
            return "ACM10020"; // 对应账号过长
        }

        // 验证客户ID长度
        if (StringUtils.hasText(accountAddress.getUserId()) && accountAddress.getUserId().length() > 64) {
            logger.warn("添加账户地址失败：客户ID过长: {}", accountAddress.getUserId().length());
            return "ACM10021"; // 客户ID过长
        }

        // 验证二维码Base64长度
        if (StringUtils.hasText(accountAddress.getQrcodeBase64()) && accountAddress.getQrcodeBase64().length() > 65535) {
            logger.warn("添加账户地址失败：二维码Base64数据过长: {}", accountAddress.getQrcodeBase64().length());
            return "ACM10022"; // 二维码Base64数据过长
        }

        return "ACM00000"; // 验证通过
    }

    /**
     * 验证状态值是否有效
     * 
     * @param status 状态值
     * @return true表示有效，false表示无效
     */
    private boolean isValidStatus(String status) {
        return "ENABLED".equals(status) || "DISABLED".equals(status) || "FROZEN".equals(status);
    }

    /**
     * 验证用途类型是否有效
     * 
     * @param useType 用途类型
     * @return true表示有效，false表示无效
     */
    private boolean isValidUseType(String useType) {
        return "DS".equals(useType) || "DC".equals(useType);
    }
}