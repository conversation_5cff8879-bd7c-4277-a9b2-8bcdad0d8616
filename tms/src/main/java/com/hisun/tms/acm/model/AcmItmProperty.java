package com.hisun.tms.acm.model;



import com.hisun.tms.common.audit.AbstractEntityTmpe;


import javax.persistence.*;


/**
 * Created by zh on 2017/11/03.
 * (底层科目属性)
 */
@Entity
@Table(name = "acm_itm_property")
public class AcmItmProperty extends AbstractEntityTmpe {
	/**
	 * itmNo 科目号
	 */
    @Id
    @Column(name = "ITM_NO")
    private String itmNo;

    /**
     * @Fields capTyp 资金类型
     */
    @Column(name = "CAP_TYP")
    private String capTyp;

    /**
     * @Fields CCY 币种
     */
    @Column(name = "CCY")
    private String ccy;
    
    /**
     * @Fields balDrt 余额方向 （A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）
     */
    @Column(name = "BAL_DRT")
    private String balDrt;
    
    
    /**
     * @Fields updOpr 更新操作员
     */
    @Column(name = "UPD_OPR")
    private String updOpr;


	public String getItmNo() {
		return itmNo;
	}


	public void setItmNo(String itmNo) {
		this.itmNo = itmNo;
	}


	public String getCapTyp() {
		return capTyp;
	}


	public void setCapTyp(String capTyp) {
		this.capTyp = capTyp;
	}

	public String getCcy() {
		return ccy;
	}


	public void setCcy(String ccy) {
		this.ccy = ccy;
	}


	public String getBalDrt() {
		return balDrt;
	}


	public void setBalDrt(String balDrt) {
		this.balDrt = balDrt;
	}


	public String getUpdOpr() {
		return updOpr;
	}


	public void setUpdOpr(String updOpr) {
		this.updOpr = updOpr;
	}

}
