package com.hisun.tms.acm.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by chen on 8/29 0029.
 */
@Entity
@Table(name = "acm_ac_bal")
@IdClass(PrimaryKeyAcmAcBal.class)
public class AcmAcBal {

    @Column(name = "USER_ID")
    private String userId;


    @Column(name = "CCY", nullable = false)
    private String ccy; // 币种

    @Id
    @Column(name = "AC_NO")
    private String  AC_NO;

    @Column(name = "AC_CUR_BAL")
    private BigDecimal acCurBal;

    @Column(name = "AC_UAVA_BAL")
    private BigDecimal acUavaBal;

    @Column(name = "CAP_TYP")
    private String capTyp ;

    @Column(name = "AC_LAST_BAL", nullable = false)
    private BigDecimal acLastBal; // 账户上一日余额

    @Column(name = "AC_LAST_UAVA_BAL", nullable = false)
    private BigDecimal acLastUavaBal; // 账户上日不可用余额

    @Column(name = "AC_BAL_TAG", nullable = false)
    private String acBalTag; // 余额tag

    @Column(name = "AC_UPD_DT", nullable = false)
    private LocalDate acUpdDt; // 余额最新变动日期

    @Column(name = "AC_UPD_TM", nullable = false)
    private LocalTime acUpdTm; // 余额最新变动时间

    @Column(name = "AC_FRZ_DT")
    private LocalDate acFrzDt; // 账户余额冻结日期

    @Column(name = "AC_FRZ_TM")
    private LocalTime acFrzTm; // 账户余额冻结时间

    @Column(name = "RMK")
    private String rmk; // 备注

    @Column(name = "AC_CUR_FREEZE_BAL", nullable = false)
    private BigDecimal acCurFreezeBal; // 结算冻结金额



    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAC_NO() {
        return AC_NO;
    }

    public void setAC_NO(String AC_NO) {
        this.AC_NO = AC_NO;
    }

    public BigDecimal getAcCurBal() {
        return acCurBal;
    }

    public void setAcCurBal(BigDecimal acCurBal) {
        this.acCurBal = acCurBal;
    }

    public BigDecimal getAcUavaBal() {
        return acUavaBal;
    }

    public void setAcUavaBal(BigDecimal acUavaBal) {
        this.acUavaBal = acUavaBal;
    }


    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getAcLastBal() {
        return acLastBal;
    }

    public void setAcLastBal(BigDecimal acLastBal) {
        this.acLastBal = acLastBal;
    }

    public BigDecimal getAcLastUavaBal() {
        return acLastUavaBal;
    }

    public void setAcLastUavaBal(BigDecimal acLastUavaBal) {
        this.acLastUavaBal = acLastUavaBal;
    }

    public String getAcBalTag() {
        return acBalTag;
    }

    public void setAcBalTag(String acBalTag) {
        this.acBalTag = acBalTag;
    }

    public LocalDate getAcUpdDt() {
        return acUpdDt;
    }

    public void setAcUpdDt(LocalDate acUpdDt) {
        this.acUpdDt = acUpdDt;
    }

    public LocalTime getAcUpdTm() {
        return acUpdTm;
    }

    public void setAcUpdTm(LocalTime acUpdTm) {
        this.acUpdTm = acUpdTm;
    }

    public LocalDate getAcFrzDt() {
        return acFrzDt;
    }

    public void setAcFrzDt(LocalDate acFrzDt) {
        this.acFrzDt = acFrzDt;
    }

    public LocalTime getAcFrzTm() {
        return acFrzTm;
    }

    public void setAcFrzTm(LocalTime acFrzTm) {
        this.acFrzTm = acFrzTm;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public BigDecimal getAcCurFreezeBal() {
        return acCurFreezeBal;
    }

    public void setAcCurFreezeBal(BigDecimal acCurFreezeBal) {
        this.acCurFreezeBal = acCurFreezeBal;
    }
}
