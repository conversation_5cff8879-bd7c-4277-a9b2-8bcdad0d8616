package com.hisun.tms.acm.controller;

import com.google.gson.Gson;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.tms.acm.common.TMSACMMessageCode;
import com.hisun.tms.acm.model.AcmAdjustInf;
import com.hisun.tms.acm.repository.AcmAdjustInfRepository;
import com.hisun.tms.acm.repository.AdjustInfInput;
import com.hisun.tms.acm.service.IAcmAdjustmentService;
import com.hisun.tms.common.util.BeanUtils;
import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.common.util.JudgeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @function AdjustAuditController
 * @description 调账审核控制层
 * @date 8/29/2017 Tue
 * @time 8:24 PM
 */
@Controller
@RequestMapping("/acm/adjust/audit")
public class AdjustAuditController {
    private static final Logger logger = LoggerFactory.getLogger(AdjustAuditController.class);

    @Autowired
    private AcmAdjustInfRepository acmAdjustInfRepository;

    @Autowired
    private IAcmAdjustmentService acmAdjustmentService;

    @GetMapping
    @PreAuthorize("hasPermission('','/csmmgr/adjust/audit') or hasRole('ROLE_ADMIN')")
    public ModelAndView auditRecordList(@RequestParam(value = "audited", required = false) String audited) {
        ModelAndView modelAndView = new ModelAndView();
        if (JudgeUtils.isNotBlank(audited)) {
            modelAndView.setViewName("acm/adjust/audit/audited");
        } else {
            modelAndView.setViewName("acm/adjust/audit/index");
        }
        return modelAndView;
    }

    @PostMapping("/findAll")
    @PreAuthorize("hasPermission('','/csmmgr/adjust/audit') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<AcmAdjustInf> findAdjustInf(@Valid @RequestBody AdjustInfInput input) {
        return acmAdjustmentService.findAdjustInf(input);
    }

    @PutMapping
    @ResponseBody
    @PreAuthorize("hasPermission('','/csmmgr/adjust/audit') or hasRole('ROLE_ADMIN')")
    public Map<String, String> update(@Validated @RequestBody AcmAdjustInf acmAdjustInf) {
        Map<String, String> map = new HashMap<>();
        String auditSts = acmAdjustInf.getAuditSts();
        String rmk = acmAdjustInf.getRmk();
        if (JudgeUtils.notEquals(auditSts, "1") && JudgeUtils.notEquals(auditSts, "2")) {
            map.put("msgCd", ACMMessageCode.PARAM_IS_NULL);
            return map;
        }
        String jrnNo = acmAdjustInf.getJrnNo();
        AcmAdjustInf acmAdjustInf1 = acmAdjustInfRepository.findOne(jrnNo);
        if (JudgeUtils.isNotBlank(acmAdjustInf1.getResult()) && JudgeUtils.isSuccess(acmAdjustInf1.getResult())) {
            map.put("msgCd", TMSACMMessageCode.ADJUST_TX_ALREADY_HANDLE);
            return map;
        }
        BeanUtils.copyProperties(acmAdjustInf, acmAdjustInf1);
        acmAdjustInf.setAuditSts(auditSts);
        GetOpr getOpr = new GetOpr();
        acmAdjustInf.setUpdOpr(getOpr.getOperatorName());
        acmAdjustInf.setRmk(rmk);
        if (JudgeUtils.equals(auditSts, "1")) {
            logger.info("*****ACCOUNTING START******");
            Map<String, String> msgMap = acmAdjustmentService.adjustAccounting(jrnNo);
            logger.info("*****MsgCd:" + msgMap.get("msgCd") + "******");
            acmAdjustInf.setResult(msgMap.get("msgCd"));
            map.put("msgCd", msgMap.get("msgCd"));
            map.put("msgInfo", msgMap.get("msgInfo"));
            acmAdjustInf.setAcDt(LocalDate.now());
        }
        Gson gson = new Gson();
        logger.info("ADJUST:" + gson.toJson(acmAdjustInf));
        insert(acmAdjustInf);
        if (JudgeUtils.isBlank(map.get("msgCd"))) {
            map.put("msgCd", ACMMessageCode.ACM_SUCC);
            map.put("msgInfo", "调账处理成功");
        }
        return map;
    }

    @PostMapping
    @ResponseBody
    @PreAuthorize("hasPermission('','/csmmgr/adjust/audit') or hasRole('ROLE_ADMIN')")
    public Map<String, String> save(@Validated @RequestBody AcmAdjustInf acmAdjustInf) {
        Map<String, String> map = new HashMap<>();
        String jrnNo = acmAdjustInf.getJrnNo();
        String rmk = acmAdjustInf.getRmk();
        AcmAdjustInf acmAdjustInf1 = acmAdjustInfRepository.findOne(jrnNo);
        BeanUtils.copyProperties(acmAdjustInf, acmAdjustInf1);
        acmAdjustInf.setRmk(rmk);
        insert(acmAdjustInf);
        map.put("msgCd", ACMMessageCode.ACM_SUCC);
        return map;
    }

    private void insert(AcmAdjustInf acmAdjustInf) {
        if (JudgeUtils.isNotNull(acmAdjustInf)){
            acmAdjustInfRepository.save(acmAdjustInf);
        }
    }
}
