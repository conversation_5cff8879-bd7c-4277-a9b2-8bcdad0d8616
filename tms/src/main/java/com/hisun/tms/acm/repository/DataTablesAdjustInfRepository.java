package com.hisun.tms.acm.repository;

import com.hisun.tms.acm.model.AcmAdjustInf;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.repository.DataTablesRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;


/**
 * <AUTHOR>
 * @function DataTablesAdjustInfRepository
 * @description 调账汇总信息
 * @date 8/29/2017 Tue
 * @time 9:17 PM
 */
public interface DataTablesAdjustInfRepository extends DataTablesRepository<AcmAdjustInf, String> {
}
