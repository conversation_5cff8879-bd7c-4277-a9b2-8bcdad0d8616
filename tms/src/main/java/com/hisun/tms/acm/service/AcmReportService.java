package com.hisun.tms.acm.service;

import com.hisun.tms.acm.dao.IAcmItmBalDao;
import com.hisun.tms.acm.dao.IAcmItmDetailDao;
import com.hisun.tms.acm.dao.IAcmTotChkInfDao;
import com.hisun.tms.acm.dao.IAcmVoucherInfDao;
import com.hisun.tms.acm.utils.ExcelFileUtil;
import com.hisun.tms.bil.model.ExcelCommonTempleModel;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @function IAcmReportService
 * @description 账务报表服务
 * @date 9/25/2017 Mon
 * @time 4:25 PM
 */
@Service
public class AcmReportService {
    private static final Logger logger = LoggerFactory.getLogger(AcmReportService.class);

    //private static String path = "/data/acm/";

    @Resource
    private IAcmItmBalDao itmBalDao;

    @Resource
    private IAcmItmDetailDao itmDetailDao;

    @Resource
    private IAcmTotChkInfDao totChkInfDao;

    @Resource
    private IAcmVoucherInfDao voucherDao;

    /**
     * 总账日报表
     *
     * @param runDt
     */
    public void generalLedgerDayReport(String runDt, String path, String fileName) {
        LocalDate acDt = DateTimeUtils.parseLocalDate(runDt);
        String title = "总账日报表";
        if (JudgeUtils.isNull(acDt)) {
            acDt = LocalDate.now();
        }
        //String fileName = title + acDt.toString() + ".xls";
        LinkedHashMap titleMap=new LinkedHashMap<>();
        titleMap.put("机构名称：", "Mpay支付平台");
        titleMap.put("统计时间：", acDt.toString());

        LinkedHashMap columHeader=new LinkedHashMap<>();
        columHeader.put("科目代码", 4200);
        columHeader.put("科目名称", 7000);
        columHeader.put("期初余额", 4200);
        columHeader.put("本期借方发生额", 4200);
        columHeader.put("本期贷方发生额", 4200);
        columHeader.put("余额方向", 3000);
        columHeader.put("期末余额", 4200);
        List itmBalDOS = itmBalDao.queryItmBal(acDt);
        List<Object[]> itmBalS = new ArrayList<>();
        for (int i = 0; i < itmBalDOS.size(); ++i) {
            Map map = (Map) itmBalDOS.get(i);
            Object[] obj = new Object[] {map.get("itm_no"), map.get("itm_cnm"), map.get("last_bal").toString(),
                    map.get("td_dr_amt").toString(), map.get("td_cr_amt").toString(), map.get("bal_drt"),
                    map.get("td_bal").toString() };
            itmBalS.add(obj);
        }

        ExcelCommonTempleModel excelCommonTempleModel=new ExcelCommonTempleModel(path, fileName, title, title,
                titleMap, columHeader, null);
        ExcelFileUtil.createExcel(itmBalS, excelCommonTempleModel);
    }

    /**
     * 会计分录统计报表
     *
     * @param runDt
     */
    public void itmSumDayReport(String runDt, String path, String fileName) {
        LocalDate acDt = DateTimeUtils.parseLocalDate(runDt);
        String title = "会计分录统计表";
        if (JudgeUtils.isNull(acDt)) {
            acDt = LocalDate.now();
        }
        //String fileName = title + acDt.toString() + ".xls";
        LinkedHashMap titleMap=new LinkedHashMap<>();
        titleMap.put("机构名称：", "Mpay支付平台");
        titleMap.put("统计时间：", acDt.toString());

        LinkedHashMap columHeader=new LinkedHashMap<>();
        columHeader.put("借贷标志", 3000);
        columHeader.put("科目代码", 4200);
        columHeader.put("科目名称", 7000);
        columHeader.put("金额", 4200);
        columHeader.put("交易名称", 4200);
        columHeader.put("交易码", 4200);
        List itmDetailDOS = itmDetailDao.queryItmDetail(acDt);
        List<Object[]> itmDetailS = new ArrayList<>();
        for (int i = 0; i < itmDetailDOS.size(); ++i) {
            Map map = (Map) itmDetailDOS.get(i);
            Object[] obj = new Object[] {map.get("dc_flg"), map.get("itm_no"), map.get("itm_cnm"),
                    map.get("tx_amt").toString(), map.get("tx_cd").toString(), map.get("tx_typ")};
            itmDetailS.add(obj);
        }

        ExcelCommonTempleModel excelCommonTempleModel=new ExcelCommonTempleModel(path, fileName, title, title,
                titleMap, columHeader, null);
        ExcelFileUtil.createExcel(itmDetailS, excelCommonTempleModel);
    }

    /**
     * 总分不平报表
     *
     * @param runDt
     */
    public void generalLedgerChkFalseDayReport(String runDt, String path, String fileName) {
        LocalDate acDt = DateTimeUtils.parseLocalDate(runDt);
        String title = "总分不平日报表";
        if (JudgeUtils.isNull(acDt)) {
            acDt = LocalDate.now();
        }
        //String fileName = title + acDt.toString() + ".xls";
        LinkedHashMap titleMap=new LinkedHashMap<>();
        titleMap.put("机构名称：", "Mpay支付平台");
        titleMap.put("统计时间：", acDt.toString());

        LinkedHashMap columHeader=new LinkedHashMap<>();
        columHeader.put("总账科目", 4200);
        columHeader.put("总账科目名称", 7000);
        columHeader.put("分账户余额", 4200);
        columHeader.put("总账户余额", 4200);
        columHeader.put("差额", 4200);
        List totChkInfList = totChkInfDao.queryTotChkInf(acDt);
        List<Object[]> totChkInfS = new ArrayList<>();
        BigDecimal totNetAmt = BigDecimal.valueOf(0, 2);
        for (int i = 0; i < totChkInfList.size(); ++i) {
            Map map = (Map) totChkInfList.get(i);
            BigDecimal glBal =(BigDecimal) map.get("gl_bal");
            BigDecimal ldgBal =(BigDecimal) map.get("ldg_bal");
            BigDecimal netAmt = BigDecimal.valueOf(0,2);
            if (JudgeUtils.isNotNull(glBal) && JudgeUtils.isNotNull(ldgBal)) {
                netAmt = glBal.subtract(ldgBal);
            }
            Object[] obj = new Object[] {map.get("itm_no"), map.get("itm_cnm"), map.get("ldg_bal").toString(),
                    map.get("gl_bal").toString(), netAmt.toString()};
            totNetAmt = totNetAmt.add(netAmt); 
            totChkInfS.add(obj);
        }

        //汇总信息
        Map sumaryItem=new LinkedHashMap<>();
        Map sumaryMap=new HashMap<>();
        sumaryItem.put(1,"");
        sumaryItem.put(2,"");
        sumaryItem.put(3,"");
        sumaryItem.put(4,totNetAmt);

        sumaryMap.put("合计",sumaryItem);
        ExcelCommonTempleModel excelCommonTempleModel=new ExcelCommonTempleModel(path, fileName, title, title,
                titleMap, columHeader, sumaryMap);
        ExcelFileUtil.createExcel(totChkInfS, excelCommonTempleModel);
    }

    /**
     * 借贷不平日报表
     *
     * @param runDt
     */
    public void dcNotBalanceDayReport(String runDt, String path, String fileName) {
        LocalDate acDt = DateTimeUtils.parseLocalDate(runDt);
        String title = "借贷不平日报表";
        if (JudgeUtils.isNull(acDt)) {
            acDt = LocalDate.now();
        }
        //String fileName = title + acDt.toString() + ".xls";
        LinkedHashMap titleMap=new LinkedHashMap<>();
        titleMap.put("机构名称：", "Mpay支付平台");
        titleMap.put("统计时间：", acDt.toString());

        LinkedHashMap columHeader=new LinkedHashMap<>();
        columHeader.put("传票编号", 10000);
        columHeader.put("帐号", 4200);
        columHeader.put("账户名称", 8000);
        columHeader.put("资金类型", 3000);
        columHeader.put("借贷方向", 3000);
        columHeader.put("交易金额", 4200);
        columHeader.put("交易码", 2000);
        List voucherInfList = voucherDao.queryVoucherInf(acDt);
        List<Object[]> voucherInfS = new ArrayList<>();
        for (int i = 0; i < voucherInfList.size(); ++i) {
            Map map = (Map) voucherInfList.get(i);
            Object[] obj = new Object[] {map.get("jrn_no"), map.get("ac_no"), map.get("ac_nm"), map.get("cap_typ").toString(),
                    map.get("dc_flg"), map.get("tx_amt").toString(), map.get("tx_typ")};
            voucherInfS.add(obj);
        }

        //汇总信息
        Map sumaryItem=new LinkedHashMap<>();
        Map sumaryMap=new HashMap<>();
        sumaryItem.put(1,"");
        sumaryItem.put(2,"");
        sumaryItem.put(3,"");
        sumaryItem.put(4,"");

        sumaryMap.put("合计",sumaryItem);
        ExcelCommonTempleModel excelCommonTempleModel=new ExcelCommonTempleModel(path, fileName, title, title,
                titleMap, columHeader, sumaryMap);
        ExcelFileUtil.createExcel(voucherInfS, excelCommonTempleModel);
    }
}
