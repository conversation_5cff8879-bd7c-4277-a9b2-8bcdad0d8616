package com.hisun.tms.acm.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @function IAcmAcDetailDao
 * @description 账户收支明细数据访问层
 * @date 10/26/2017 Thu
 * @time 10:22 AM
 */
@Mapper
public interface IAcmAcDetailDao {
    int countUser(@Param("acDt") LocalDate acDt);
    int countMerc(@Param("acDt") LocalDate acDt);
}
