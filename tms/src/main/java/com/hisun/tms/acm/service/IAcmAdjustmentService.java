package com.hisun.tms.acm.service;

import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.tms.acm.model.AcmAdjustDetail;
import com.hisun.tms.acm.model.AcmAdjustInf;
import com.hisun.tms.acm.repository.AdjustInfInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function IAcmAdjustmentService
 * @description 账户信息服务
 * @date 8/24/2017 Thu
 * @time 2:54 PM
 */
public interface IAcmAdjustmentService {
    DataTablesOutput<AcmAdjustInf> findAdjustInf(AdjustInfInput input);

    List<AcmAdjustDetail> findAdjustDetail(String jrnNo);

    Map<String, String> queryAcBal(String acNo, String capTyp, String acTyp);

    Map<String, String> adjustAccounting(String jrnNo);
}
