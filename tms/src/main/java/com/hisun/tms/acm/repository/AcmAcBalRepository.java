package com.hisun.tms.acm.repository;

import com.hisun.tms.acm.model.AcmAcBal;
import com.hisun.tms.cmm.model.AreaInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * Created by chen on 8/29 0029.
 */
public interface AcmAcBalRepository extends JpaRepository<AcmAcBal, String> {

    @Query(value = "select AC_NO , AC_CUR_BAL ,USER_ID ,AC_UAVA_BAL ,CAP_TYP from acm_ac_bal where user_id = :userId and CAP_TYP = '1' ", nativeQuery=true  )
    List<AcmAcBal> findAllById(@Param("userId") String userId);

    @Query(value = "select AC_NO , AC_CUR_BAL ,USER_ID ,AC_UAVA_BAL ,CAP_TYP from acm_ac_bal where user_id = :userId ", nativeQuery=true  )
    List<AcmAcBal> findAllByMerId(@Param("userId") String userId);
}
