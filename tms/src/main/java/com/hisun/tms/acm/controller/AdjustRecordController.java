package com.hisun.tms.acm.controller;

import com.google.gson.Gson;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.tms.acm.model.AcmAdjustDetail;
import com.hisun.tms.acm.model.AcmAdjustInf;
import com.hisun.tms.acm.repository.AcmAdjustDetailRepository;
import com.hisun.tms.acm.repository.AcmAdjustInfRepository;
import com.hisun.tms.acm.service.IAcmAdjustmentService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.common.util.JudgeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function AdjustRecordController
 * @description 调账录入控制层
 * @date 8/29/2017 Tue
 * @time 8:23 PM
 */
@Controller
@RequestMapping("/acm/adjust/record")
public class AdjustRecordController {
    private static final Logger logger = LoggerFactory.getLogger(AdjustRecordController.class);

    @Autowired
    private AcmAdjustDetailRepository acmAdjustDetailRepository;

    @Autowired
    private AcmAdjustInfRepository acmAdjustInfRepository;

    @Autowired
    private IAcmAdjustmentService iAcmAdjustmentService;

    @GetMapping
    @PreAuthorize("hasPermission('','/csmmgr/adjust/record') or hasRole('ROLE_ADMIN')")
    public ModelAndView adjustRecordList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("acm/adjust/record/index");
        return modelAndView;
    }

    @GetMapping("/findAll")
    @PreAuthorize("hasPermission('','/csmmgr/adjust/record') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public ModelAndView findAdjustDetail(@Validated @RequestParam("jrnNo") String jrnNo) {
        Gson gson = new Gson();
        logger.info("JrnNo:" + gson.toJson(jrnNo));
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("acm/adjust/query/index");
        List<AcmAdjustDetail> acmAdjustDetails = iAcmAdjustmentService.findAdjustDetail(jrnNo);
        for (int i = 0; i < acmAdjustDetails.size(); ++i) {
            modelAndView.addObject("details" + new Integer(i + 1).toString(), acmAdjustDetails.get(i));
        }
        return modelAndView;
    }

    @GetMapping("/bal")
    @PreAuthorize("hasPermission('','/csmmgr/adjust/record') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> queryAcBal(@Validated @RequestParam("acNo") String acNo,
                                          @Validated @RequestParam("capTyp") String capTyp,
                                          @Validated @RequestParam("acTyp") String acTyp) {
        return iAcmAdjustmentService.queryAcBal(acNo, capTyp, acTyp);
    }

    @PostMapping
    @ResponseBody
    @PreAuthorize("hasPermission('','/csmmgr/adjust/record') or hasRole('ROLE_ADMIN')")
    public Map<String, String> save(@Validated @RequestBody List<AcmAdjustDetail> acmAdjustDetails) {
        String jrnNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
//                IdGenUtils.generateId("ADJUST_JRN_NO", 8);
        logger.info("JRN_NO:" + jrnNo);
        int i = 0;
        Gson gson = new Gson();
        BigDecimal totDrAmt = BigDecimal.valueOf(0, 2);
        BigDecimal totCrAmt = BigDecimal.valueOf(0, 2);
        List<AcmAdjustDetail> adjustDetails = new ArrayList<>();
        for (AcmAdjustDetail acmAdjustDetail : acmAdjustDetails) {
            logger.info("AcmAdjustDetail:" + gson.toJson(acmAdjustDetail));
            acmAdjustDetail.setJrnNo(jrnNo);
            acmAdjustDetail.setJrnSeq(new Integer(i++).toString());
            acmAdjustDetail.setRegDt(LocalDate.now());
            String dcFlg = acmAdjustDetail.getDcFlg();
            if (JudgeUtils.equals(dcFlg, ACMConstants.AC_D_FLG)) {
                totDrAmt = totDrAmt.add(acmAdjustDetail.getTxAmt());
            } else if (JudgeUtils.equals(dcFlg, ACMConstants.AC_C_FLG)) {
                totCrAmt = totCrAmt.add(acmAdjustDetail.getTxAmt());
            } else {
                LemonException.throwLemonException(ACMMessageCode.DC_FLG_IS_NULL);
            }
            adjustDetails.add(acmAdjustDetail);
        }
        logger.info("totDrAmt:" + totDrAmt +" totCrAmt:" + totCrAmt);
        Map<String, String> map = new HashMap<>();
        if (totCrAmt.compareTo(totDrAmt) != 0 || totCrAmt.compareTo(BigDecimal.ZERO) == 0) {
            map.put("msgCd", ACMMessageCode.DR_NOT_EQUAL_CR);
            return map;
        }
        acmAdjustDetailRepository.save(adjustDetails);
        GetOpr getOpr = new GetOpr();
        AcmAdjustInf acmAdjustInf = new AcmAdjustInf();
        acmAdjustInf.setJrnNo(jrnNo);
        acmAdjustInf.setRegDt(LocalDate.now());
        acmAdjustInf.setTotTxAmt(totDrAmt);
        acmAdjustInf.setTotTxNum(acmAdjustDetails.size());
        acmAdjustInf.setAuditSts("0");
        acmAdjustInf.setRegOpr(getOpr.getOperatorName());
        acmAdjustInf.setRmk("");
        acmAdjustInfRepository.save(acmAdjustInf);
        map.put("jrnNo", jrnNo);
        map.put("msgCd", ACMMessageCode.ACM_SUCC);
        return map;
    }
}
