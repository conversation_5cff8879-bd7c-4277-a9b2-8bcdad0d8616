package com.hisun.tms.acm.service.impl;

import com.google.gson.Gson;
import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.ItemAccountDTO;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import com.hisun.tms.acm.model.AcmAdjustDetail;
import com.hisun.tms.acm.model.AcmAdjustInf;
import com.hisun.tms.acm.repository.*;
import com.hisun.tms.acm.service.IAcmAdjustmentService;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.specifications.Specifications;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function AcmAdjustmentServiceImpl
 * @description 账户信息
 * @date 8/24/2017 Thu
 * @time 2:53 PM
 */
@Service("Adjustment")
public class AcmAdjustmentServiceImpl implements IAcmAdjustmentService {
    private static final Logger logger = LoggerFactory.getLogger(AcmAdjustmentServiceImpl.class);

    @Resource
    private AcmAdjustInfRepository acmAdjustInfRepository;

    @Resource
    private AcmAdjustDetailRepository acmAdjustDetailRepository;

    @Resource
    private DataTablesAdjustInfRepository dataTablesAdjustInfRepository;

    @Resource
    private DataTablesAdjustDetailRepository dataTablesAdjustDetailRepository;

    @Resource
    private AccountManagementClient accountManagementClient;

    @Resource
    private AccountingTreatmentClient accountingTreatmentClient;

    @Resource
    private UserBasicInfClient basicInfClient;

    @Resource(name = "acmEntityManager")
    private EntityManager entityManagerFactory;

    @Override
    public Map<String, String> queryAcBal(String acNo, String capTyp, String acTyp) {
        logger.info("queryAcBal:" + "AC_NO:" + acNo + ", CAP_TYP:" + capTyp + ", AC_TYP:" + acTyp);
        if (JudgeUtils.isBlank(acNo)) {
            LemonException.throwLemonException(ACMMessageCode.ILLEGAL_AC_NO);
        }
        if (JudgeUtils.isBlank(capTyp)) {
            LemonException.throwLemonException(ACMMessageCode.CAP_TYP_IS_NULL);
        }
        if (JudgeUtils.isNotNull(acTyp) && JudgeUtils.equals(acTyp.toUpperCase(), ACMConstants.ITM_AC_TYP)) {
            Map<String, String> map = new HashMap<>();
            GenericRspDTO<ItemAccountDTO> itemAccountDTO = accountManagementClient.queryItemAccountInf(acNo);
            if (JudgeUtils.isNotNull(itemAccountDTO)) {
                map.put("acNm", itemAccountDTO.getBody().getItmEnm());
            }
            map.put("acCurBal", BigDecimal.valueOf(0, 2).toString());
            map.put("msgCd", itemAccountDTO.getMsgCd());
            map.put("msgInfo", itemAccountDTO.getMsgInfo());
            return map;
        }
        UserAccountDTO userAccountDTO = new UserAccountDTO();
        userAccountDTO.setAcNo(acNo);
        userAccountDTO.setCapTyp(capTyp);
        GenericRspDTO<List<QueryAcBalRspDTO>> genericRspDTO = accountManagementClient.queryAcBal(userAccountDTO);
        List<QueryAcBalRspDTO> acBalList = genericRspDTO.getBody();
        Map<String, String> map = new HashMap<>();
        String usrNm = "";
        if (JudgeUtils.isNotEmpty(acBalList)) {
            GenericRspDTO<String> genericRspDTO1 = accountManagementClient.queryUser(acNo);
            if (JudgeUtils.isSuccess(genericRspDTO1.getMsgCd())) {
                String userId = genericRspDTO1.getBody();
                if (JudgeUtils.isNotBlank(userId)) {
                    GenericRspDTO<UserBasicInfDTO> basicInfDTO = basicInfClient.queryUser(userId);
                    if (JudgeUtils.isSuccess(basicInfDTO.getMsgCd())) {
                        usrNm = basicInfDTO.getBody().getUsrNm();
                    } else {
                        map.put("msgCd", basicInfDTO.getMsgCd());
                        map.put("msgInfo", basicInfDTO.getMsgInfo());
                        return map;
                    }
                }
            } else {
                map.put("msgCd", genericRspDTO1.getMsgCd());
                map.put("msgInfo", genericRspDTO1.getMsgInfo());
                return map;
            }
            map.put("acCurBal", acBalList.get(0).getAcCurBal().toString());
            map.put("acNm", usrNm);
        }
        map.put("msgCd", genericRspDTO.getMsgCd());
        map.put("msgInfo", genericRspDTO.getMsgInfo());
        return map;
    }

    @Override
    public DataTablesOutput<AcmAdjustInf> findAdjustInf(AdjustInfInput input) {
        String regOpr = input.getExtra_search().get("regOpr").trim();
        String regDtS = input.getExtra_search().get("regDtS").trim();
        String regDtE = input.getExtra_search().get("regDtE").trim();
        String dtType = input.getExtra_search().get("dtType");
        if (JudgeUtils.isBlank(dtType)) {
            dtType = "regDt";
        }
        String auditSts = input.getExtra_search().get("auditSts");
        logger.info("extra_search:" + input.getExtra_search().toString());
        LocalDate startDate = null;
        LocalDate endDate = null;
        if (JudgeUtils.isBlank(regDtS)) {
            startDate = LocalDate.now();
        } else {
            startDate = LocalDate.parse(regDtS);
        }
        if (JudgeUtils.isBlank(regDtE)) {
            endDate = LocalDate.now();
        } else {
            endDate = LocalDate.parse(regDtE);
        }
        if (JudgeUtils.isBlank(auditSts)) {
            auditSts = "0"; //未审核状态
        }
        Specification<AcmAdjustInf> adjustInfSpecification = Specifications.<AcmAdjustInf>and()
                .eq(JudgeUtils.isNotBlank(regOpr), "regOpr", regOpr)
                .between(dtType, new Range<>(startDate, endDate))
                .eq(JudgeUtils.isNotBlank(auditSts), "auditSts", auditSts)
                .build();
        Gson gson = new Gson();
        DataTablesOutput<AcmAdjustInf> dataTablesOutput = dataTablesAdjustInfRepository.findAll(input, adjustInfSpecification);
        logger.info("TEST1: " + gson.toJson(dataTablesOutput));
        return dataTablesOutput;
    }

    @Override
    public Map<String, String> adjustAccounting(String jrnNo) {
        List<AcmAdjustDetail> acmAdjustDetails = acmAdjustDetailRepository.findAllByJrnNo(jrnNo);
        List<AccountingReqDTO> accountingReqDTOS = new ArrayList<>();
        for (AcmAdjustDetail acmAdjustDetail : acmAdjustDetails) {
            AccountingReqDTO accountingReqDTO = new AccountingReqDTO();
            BeanUtils.copyProperties(accountingReqDTO, acmAdjustDetail);
            if (JudgeUtils.equals(acmAdjustDetail.getAcTyp(),ACMConstants.ITM_AC_TYP)) {
                String itmNo = acmAdjustDetail.getAcNo();
                accountingReqDTO.setItmNo(itmNo);
            }
            accountingReqDTO.setTxJrnNo("ADJ" + acmAdjustDetail.getJrnNo());
            accountingReqDTO.setTxSts(ACMConstants.ACCOUNTING_NOMARL);
            accountingReqDTO.setTxTyp("00"); //交易类型：手工调账
            accountingReqDTOS.add(accountingReqDTO);
        }
        GenericDTO<List<AccountingReqDTO>> reqDTO = new GenericDTO<>();
        reqDTO.setBody(accountingReqDTOS);
        Map<String, String> msgMap = new HashMap<>();
        try {
            GenericRspDTO<NoBody> rspDTO = accountingTreatmentClient.accountingTreatment(reqDTO);
            msgMap.put("msgCd", rspDTO.getMsgCd());
            msgMap.put("msgInfo", rspDTO.getMsgInfo());
        } catch (Exception e) {
            msgMap.put("msgCd", "SYS00001");
            msgMap.put("msgInfo", "系统忙");
        }
        return msgMap;
    }

    @Override
    public List<AcmAdjustDetail> findAdjustDetail(String jrnNo) {
        return acmAdjustDetailRepository.findAllByJrnNo(jrnNo);
    }
}
