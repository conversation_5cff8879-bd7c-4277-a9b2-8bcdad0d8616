package com.hisun.tms.acm.model;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @function PrimaryKeyAcmAdjustDetail
 * @description 调账流水明细主键类
 * @date 9/11/2017 Mon
 * @time 10:36 PM
 */
@Embeddable
public class PrimaryKeyAcmAdjustDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    public PrimaryKeyAcmAdjustDetail() {
    }

    public PrimaryKeyAcmAdjustDetail(String jrnNo, String jrnSeq) {
        this.jrnNo = jrnNo;
        this.jrnSeq = jrnSeq;
    }

    @Column(name = "jrn_no", insertable = false, updatable = false)
    private String jrnNo;

    @Column(name = "jrn_seq", insertable = false, updatable = false)
    private String jrnSeq;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getJrnSeq() {
        return jrnSeq;
    }

    public void setJrnSeq(String jrnSeq) {
        this.jrnSeq = jrnSeq;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PrimaryKeyAcmAdjustDetail other = (PrimaryKeyAcmAdjustDetail) obj;
        if ((this.jrnNo == null) ? (other.jrnNo != null) : !this.jrnNo.equals(other.jrnNo)) {
            return false;
        }
        if ((this.jrnSeq == null) ? (other.jrnSeq != null) : !this.jrnSeq.equals(
                other.jrnSeq)) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 41 * hash + (this.jrnNo != null ? this.jrnNo.hashCode() : 0);
        hash = 41 * hash + (this.jrnSeq != null ? this.jrnSeq.hashCode() : 0);
        return hash;
    }
}
