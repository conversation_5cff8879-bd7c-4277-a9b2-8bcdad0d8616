package com.hisun.tms.acm.repository;

import com.hisun.tms.acm.model.AcmAdjustDetail;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @function AcmAdjustDetailRepository
 * @description 调账明细对象
 * @date 8/29/2017 Tue
 * @time 9:18 PM
 */
public interface AcmAdjustDetailRepository extends JpaRepository<AcmAdjustDetail, String> {
    List<AcmAdjustDetail> findAllByJrnNo(String jrnNo);
}
