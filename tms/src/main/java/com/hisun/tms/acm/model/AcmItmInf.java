package com.hisun.tms.acm.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Created by zh on 2017/11/03.
 * (科目基本信息)
 */
@Entity
@Table(name = "acm_itm_inf")
public class AcmItmInf extends AbstractEntityTmpe {
	/**
	 * itmNo 科目号
	 */
    @Id
    @Column(name = "ITM_NO")
    private String itmNo;

    /**
     * @Fields itmEnm 科目英文名称
     */
    @Column(name = "ITM_ENM")
    private String itmEnm;

    /**
     * @Fields itmCnm 科目中文名称
     */
    @Column(name = "ITM_CNM")
    private String itmCnm;
    
    /**
     * @Fields itmLvl 科目级别（1：一级科目、2：二级科目、3：三级科目）
     */
    @Column(name = "ITM_LVL")
    private String itmLvl;
    
    /**
     * @Fields upItmNo 上级科目号
     */
    @Column(name = "UP_ITM_NO")
    private String upItmNo;

    /**
     * @Fields btmItmFlg 最底层科目标志（Y：最底层、N：非最底层）
     */
    @Column(name = "BTM_ITM_FLG")
    private String btmItmFlg;

    /**
     * @Fields itmTyp 科目类别（A-资产类、L-负债类、C-所有者权益类、I-收入类、E-支出类、O-表外类、S-往来账）
     */
    @Column(name = "ITM_TYP")
    private String itmTyp;
    
    /**
     * @Fields itmCls 科目分类（1-存放银行资金池账户、2-差错/争议挂账账户、3-其他内部账户）
     */
    @Column(name = "ITM_CLS")
    private String itmCls;

    /**
     * @Fields balOdFlg 余额是否允许透支标识
     */
    @Column(name = "BAL_OD_FLG")
    private String balOdFlg;
    
    /**
     * @Fields balDrt 余额方向（A-借贷双方反映、B-借贷轧差反映、C-贷方反映、D-借方反映）
     */
    @Column(name = "BAL_DRT")
    private String balDrt;
    
    /**
     * @Fields updBalFlg 余额更新方式 0：实时更新1：批量更新
     */
    @Column(name = "UPD_BAL_FLG")
    private String updBalFlg;
    
    /**
     * @Fields itmSts 科目状态（0-生效、1-失效）
     */
    @Column(name = "ITM_STS")
    private String itmSts;
    
    /**
     * @Fields itmZbalFlg 科目余额零标志 D-科目的余额必须日终为零;M-科目的余额必须月终为零;Y-科目的余额必须年终为零;N-科目的余额不需要检查是否为零;
     */
    @Column(name = "ITM_ZBAL_FLG")
    private String itmZbalFlg;
    
    /**
     * @Fields lpBfFlg 损益结转标志  Y-需做损益结转N-不需做损益结转
     */
    @Column(name = "LP_BF_FLG")
    private String lpBfFlg;
    

    /**
     * @Fields effDt 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "EFF_DT")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDt;
    
    /**
     * @Fields expDt 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "EXP_DT")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDt;
    
    /**
     * @Fields updOpr 更新操作员
     */
    @Column(name = "UPD_OPR")
    private String updOpr;

	public String getItmNo() {
		return itmNo;
	}

	public void setItmNo(String itmNo) {
		this.itmNo = itmNo;
	}

	public String getItmEnm() {
		return itmEnm;
	}

	public void setItmEnm(String itmEnm) {
		this.itmEnm = itmEnm;
	}

	public String getItmCnm() {
		return itmCnm;
	}

	public void setItmCnm(String itmCnm) {
		this.itmCnm = itmCnm;
	}

	public String getItmLvl() {
		return itmLvl;
	}

	public void setItmLvl(String itmLvl) {
		this.itmLvl = itmLvl;
	}

	public String getUpItmNo() {
		return upItmNo;
	}

	public void setUpItmNo(String upItmNo) {
		this.upItmNo = upItmNo;
	}

	public String getBtmItmFlg() {
		return btmItmFlg;
	}

	public void setBtmItmFlg(String btmItmFlg) {
		this.btmItmFlg = btmItmFlg;
	}

	public String getItmTyp() {
		return itmTyp;
	}

	public void setItmTyp(String itmTyp) {
		this.itmTyp = itmTyp;
	}

	public String getItmCls() {
		return itmCls;
	}

	public void setItmCls(String itmCls) {
		this.itmCls = itmCls;
	}

	public String getBalOdFlg() {
		return balOdFlg;
	}

	public void setBalOdFlg(String balOdFlg) {
		this.balOdFlg = balOdFlg;
	}

	public String getBalDrt() {
		return balDrt;
	}

	public void setBalDrt(String balDrt) {
		this.balDrt = balDrt;
	}

	public String getUpdBalFlg() {
		return updBalFlg;
	}

	public void setUpdBalFlg(String updBalFlg) {
		this.updBalFlg = updBalFlg;
	}

	public String getItmSts() {
		return itmSts;
	}

	public void setItmSts(String itmSts) {
		this.itmSts = itmSts;
	}

	public String getItmZbalFlg() {
		return itmZbalFlg;
	}

	public void setItmZbalFlg(String itmZbalFlg) {
		this.itmZbalFlg = itmZbalFlg;
	}

	public String getLpBfFlg() {
		return lpBfFlg;
	}

	public void setLpBfFlg(String lpBfFlg) {
		this.lpBfFlg = lpBfFlg;
	}

	public LocalDate getEffDt() {
		return effDt;
	}

	public void setEffDt(LocalDate effDt) {
		this.effDt = effDt;
	}

	public LocalDate getExpDt() {
		return expDt;
	}

	public void setExpDt(LocalDate expDt) {
		this.expDt = expDt;
	}

	public String getUpdOpr() {
		return updOpr;
	}

	public void setUpdOpr(String updOpr) {
		this.updOpr = updOpr;
	}
    


}
