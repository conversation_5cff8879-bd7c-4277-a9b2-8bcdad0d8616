package com.hisun.tms.acm.dao;

import com.hisun.tms.acm.model.AccountAddressDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 账户地址信息数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/26
 */
@Mapper
public interface IAccountAddressDao {

    /**
     * 根据查询条件查询账户地址列表（支持分页和搜索）
     * 
     * @param params 查询参数，包含分页和搜索条件
     * @return 账户地址列表
     */
    List<AccountAddressDO> findAll(Map<String, Object> params);

    /**
     * 查询符合条件的记录总数
     * 
     * @param params 查询参数
     * @return 总记录数
     */
    int countAll(Map<String, Object> params);

    /**
     * 根据ID查询单个账户地址记录
     * 
     * @param id 账户地址ID
     * @return 账户地址对象
     */
    AccountAddressDO selectById(Long id);

    /**
     * 插入新的账户地址记录
     * 
     * @param accountAddress 账户地址对象
     * @return 影响行数
     */
    int insert(AccountAddressDO accountAddress);

    /**
     * 根据地址查询记录（用于唯一性验证）
     * 
     * @param address 区块链地址
     * @return 账户地址对象，如果不存在则返回null
     */
    AccountAddressDO selectByAddress(String address);
}