package com.hisun.tms.acm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @create 2018/1/23
 */
@Entity
@Table(name = "acm_ac_detail")
public class AcmAcDetail {

    @EmbeddedId
    private PrimaryKeyAcmAdjustDetail id;

    public PrimaryKeyAcmAdjustDetail getId() {
        return id;
    }

    public void setId(PrimaryKeyAcmAdjustDetail id) {
        this.id = id;
    }

    @Column(name = "ac_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    private LocalDate acDt;

    @Column(name = "cap_typ")
    private String capTyp;

    @Column(name = "tx_typ")
    private String txTyp;

    @Column(name = "tx_sts")
    private String txSts;

    @Column(name = "ac_no")
    private String acNo;

    @Column(name = "dc_flg")
    private String dcFlg;

    @Column(name = "tx_amt")
    private BigDecimal txAmt;

    @Column(name = "ccy")
    private String ccy;

    @Column(name = "rvs_jrn_no")
    private String rvsJrnNo;

    @Column(name = "rvs_jrn_seq")
    private String rvsJrnSeq;

    @Column(name = "dr_amt")
    private BigDecimal drAmt;

    @Column(name = "cr_amt")
    private BigDecimal crAmt;

    @Column(name = "od_amt")
    private BigDecimal odAmt;

    @Column(name = "cur_bal")
    private BigDecimal curBal;

    @Column(name = "tx_jrn_no")
    private String txJrnNo;

    @Column(name = "tx_ord_no")
    private String txOrdNo;

    @Column(name = "tx_ord_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    private LocalDate txOrdDt;

    @Column(name = "tx_ord_tm")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalTimeConverter.class)
    private LocalTime txOrdTm;

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getTxTyp() {
        return txTyp;
    }

    public void setTxTyp(String txTyp) {
        this.txTyp = txTyp;
    }

    public String getTxSts() {
        return txSts;
    }

    public void setTxSts(String txSts) {
        this.txSts = txSts;
    }

    public String getAcNo() {
        return acNo;
    }

    public void setAcNo(String acNo) {
        this.acNo = acNo;
    }

    public String getDcFlg() {
        return dcFlg;
    }

    public void setDcFlg(String dcFlg) {
        this.dcFlg = dcFlg;
    }

    public BigDecimal getTxAmt() {
        return txAmt;
    }

    public void setTxAmt(BigDecimal txAmt) {
        this.txAmt = txAmt;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getRvsJrnNo() {
        return rvsJrnNo;
    }

    public void setRvsJrnNo(String rvsJrnNo) {
        this.rvsJrnNo = rvsJrnNo;
    }

    public String getRvsJrnSeq() {
        return rvsJrnSeq;
    }

    public void setRvsJrnSeq(String rvsJrnSeq) {
        this.rvsJrnSeq = rvsJrnSeq;
    }

    public BigDecimal getDrAmt() {
        return drAmt;
    }

    public void setDrAmt(BigDecimal drAmt) {
        this.drAmt = drAmt;
    }

    public BigDecimal getCrAmt() {
        return crAmt;
    }

    public void setCrAmt(BigDecimal crAmt) {
        this.crAmt = crAmt;
    }

    public BigDecimal getOdAmt() {
        return odAmt;
    }

    public void setOdAmt(BigDecimal odAmt) {
        this.odAmt = odAmt;
    }

    public BigDecimal getCurBal() {
        return curBal;
    }

    public void setCurBal(BigDecimal curBal) {
        this.curBal = curBal;
    }

    public String getTxJrnNo() {
        return txJrnNo;
    }

    public void setTxJrnNo(String txJrnNo) {
        this.txJrnNo = txJrnNo;
    }

    public String getTxOrdNo() {
        return txOrdNo;
    }

    public void setTxOrdNo(String txOrdNo) {
        this.txOrdNo = txOrdNo;
    }

    public LocalDate getTxOrdDt() {
        return txOrdDt;
    }

    public void setTxOrdDt(LocalDate txOrdDt) {
        this.txOrdDt = txOrdDt;
    }

    public LocalTime getTxOrdTm() {
        return txOrdTm;
    }

    public void setTxOrdTm(LocalTime txOrdTm) {
        this.txOrdTm = txOrdTm;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }

    @Override
    public String toString() {
        return "AcmAcDetail{" +
                "id=" + id.getJrnNo() +
                ", id=" + id.getJrnSeq() +
                ", acDt=" + acDt +
                ", txTyp='" + txTyp + '\'' +
                ", txSts='" + txSts + '\'' +
                ", acNo='" + acNo + '\'' +
                ", dcFlg='" + dcFlg + '\'' +
                ", txAmt=" + txAmt +
                ", ccy='" + ccy + '\'' +
                ", rvsJrnNo='" + rvsJrnNo + '\'' +
                ", rvsJrnSeq='" + rvsJrnSeq + '\'' +
                ", drAmt=" + drAmt +
                ", crAmt=" + crAmt +
                ", odAmt=" + odAmt +
                ", curBal=" + curBal +
                ", txJrnNo='" + txJrnNo + '\'' +
                ", txOrdNo='" + txOrdNo + '\'' +
                ", txOrdDt='" + txOrdDt + '\'' +
                ", txOrdTm=" + txOrdTm +
                '}';
    }
}
