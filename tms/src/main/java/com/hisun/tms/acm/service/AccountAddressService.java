package com.hisun.tms.acm.service;

import com.hisun.tms.acm.model.AccountAddressDO;
import com.hisun.tms.common.datatables.QueryFindInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 账户地址管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/26
 */
public interface AccountAddressService {

    /**
     * 查询所有账户地址（支持分页和搜索）
     * 
     * @param input 查询条件，包含分页参数和搜索条件
     * @return 分页结果
     */
    DataTablesOutput<AccountAddressDO> findAll(QueryFindInput input);

    /**
     * 获取单个账户地址详情
     * 
     * @param id 账户地址ID
     * @return 账户地址对象
     */
    AccountAddressDO getDetail(Long id);

    /**
     * 添加新的账户地址
     * 
     * @param accountAddress 账户地址对象
     * @return 操作结果代码
     */
    String add(AccountAddressDO accountAddress);

    /**
     * 验证地址唯一性
     * 
     * @param address 区块链地址
     * @return true表示地址可用，false表示地址已存在
     */
    boolean validateAddress(String address);
}