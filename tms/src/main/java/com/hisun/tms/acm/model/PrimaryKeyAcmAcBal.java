package com.hisun.tms.acm.model;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @function PrimaryKeyAcmAdjustDetail
 * @description 调账流水明细主键类
 * @date 9/11/2017 Mon
 * @time 10:36 PM
 */
@Embeddable
public class PrimaryKeyAcmAcBal implements Serializable {
    private static final long serialVersionUID = 1L;

    private String userId;


    private String capTyp;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCapTyp() {
        return capTyp;
    }

    public void setCapTyp(String capTyp) {
        this.capTyp = capTyp;
    }
}
