package com.hisun.tms.acm.model;

import java.util.Date;

/**
 * 账户地址信息DO
 * 对应数据库表: dm_account_address
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/26
 */
public class AccountAddressDO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 所属金库编码
     */
    private String vaultCode;

    /**
     * 所属账户组编码
     */
    private String groupCode;

    /**
     * 所属账户ID
     */
    private Long accountId;

    /**
     * 链上地址
     */
    private String address;

    /**
     * 所属区块链网络
     */
    private String network;

    /**
     * 地址状态 (ENABLED/DISABLED/FROZEN)
     */
    private String status;

    /**
     * 对应的账号
     */
    private String acmAcNo;

    /**
     * 归属客户ID
     */
    private String userId;

    /**
     * 收款二维码Base64编码
     */
    private String qrcodeBase64;

    /**
     * 用途类型 (DS/DC)
     */
    private String useType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAcmAcNo() {
        return acmAcNo;
    }

    public void setAcmAcNo(String acmAcNo) {
        this.acmAcNo = acmAcNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getQrcodeBase64() {
        return qrcodeBase64;
    }

    public void setQrcodeBase64(String qrcodeBase64) {
        this.qrcodeBase64 = qrcodeBase64;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AccountAddressDO{" +
                "id=" + id +
                ", vaultCode='" + vaultCode + '\'' +
                ", groupCode='" + groupCode + '\'' +
                ", accountId=" + accountId +
                ", address='" + address + '\'' +
                ", network='" + network + '\'' +
                ", status='" + status + '\'' +
                ", acmAcNo='" + acmAcNo + '\'' +
                ", userId='" + userId + '\'' +
                ", qrcodeBase64='" + qrcodeBase64 + '\'' +
                ", useType='" + useType + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}