package com.hisun.tms.acm.service;



import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.acm.model.AcmItmInf;
import com.hisun.tms.acm.repository.AdjustInfInput;


/**
 * Created by zh on 2017/7/17.
 */
public interface AcmItmInfService {

    void save(Iterable<AcmItmInf> iterable);

    Map<String, AcmItmInf> modify(Map<String, AcmItmInf> exampleMap);

    Map<String, String> delete(Iterable<AcmItmInf> entities);

    DataTablesOutput<AcmItmInf> findAll(AdjustInfInput input);

	String checkItmNo(String itmNoVal, String upItmNoVal);

}