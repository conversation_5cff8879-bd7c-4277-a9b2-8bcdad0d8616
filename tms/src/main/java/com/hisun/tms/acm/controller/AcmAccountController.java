package com.hisun.tms.acm.controller;

import com.hisun.tms.acm.model.AcmAcInf;
import com.hisun.tms.acm.service.AcmAccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/acm/acc/manage")
@PreAuthorize("hasPermission('','/csmmgr/acc/manage') or hasRole('ROLE_ADMIN')")
public class AcmAccountController {

    private static final Logger logger = LoggerFactory.getLogger(AcmAccountController.class);

    @Resource
    AcmAccountService acmAccountService;

    @GetMapping
    @PreAuthorize("hasPermission('','/csmmgr/acc/manage') or hasRole('ROLE_ADMIN')")
    public ModelAndView AccManage() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("acm/acc/manage");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/csmmgr/acc/manage') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public List<AcmAcInf> getAllPendingAcc(@RequestParam(value = "acNo", required = false) String acNo,
                                          @RequestParam(value = "acSts", required = false) String acSts) {
        if ((acNo != null && !acNo.isEmpty()) || (acSts != null && !acSts.isEmpty())) {
            logger.info("按条件查询法币开户申请: acNo={}, acSts={}", acNo, acSts);
            return acmAccountService.findByCondition(acNo, acSts);
        } else {
            logger.info("查询所有待审核法币开户申请");
            return acmAccountService.getAllPendingAcc();
        }
    }

    @PostMapping(value = "check")
    @PreAuthorize("hasPermission('','/csmmgr/acc/manage') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> checkAcc(@Validated @RequestParam(value = "checkResult") String checkResult,
                                       @Validated @RequestParam(value = "acNo") String acNo) {
        return acmAccountService.checkAccount(checkResult, acNo);
    }
}
