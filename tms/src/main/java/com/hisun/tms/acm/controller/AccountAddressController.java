package com.hisun.tms.acm.controller;

import com.hisun.tms.acm.model.AccountAddressDO;
import com.hisun.tms.acm.service.AccountAddressService;
import com.hisun.tms.common.datatables.QueryFindInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 账户地址管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/26
 */
@Controller
@RequestMapping("/acm/accountaddress")
public class AccountAddressController {

    private static final Logger logger = LoggerFactory.getLogger(AccountAddressController.class);

    @Resource
    private AccountAddressService accountAddressService;

    /**
     * 账户地址管理主页面
     * 
     * @return 主页面视图
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/acmmgr/accountaddress') or hasRole('ROLE_ADMIN')")
    public ModelAndView index() {
        logger.debug("访问账户地址管理主页面");
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("acm/accountaddress/index");
        return modelAndView;
    }

    /**
     * 查询所有账户地址（支持分页和搜索）
     * 
     * @param input 查询条件，包含分页参数和搜索条件
     * @return 分页结果
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/acmmgr/accountaddress') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<AccountAddressDO> findAll(@Valid @RequestBody QueryFindInput input) {
        try {
            logger.debug("查询账户地址列表，参数: {}", input);
            DataTablesOutput<AccountAddressDO> result = accountAddressService.findAll(input);
            logger.debug("查询账户地址列表，结果数量: {}", result.getData().size());
            return result;
        } catch (Exception e) {
            logger.error("查询账户地址列表失败: ", e);
            // 返回空结果而不是null，避免前端解析错误
            DataTablesOutput<AccountAddressDO> emptyResult = new DataTablesOutput<>();
            emptyResult.setDraw(input.getDraw());
            emptyResult.setRecordsTotal(0);
            emptyResult.setRecordsFiltered(0);
            return emptyResult;
        }
    }

    /**
     * 获取单个账户地址详情
     * 
     * @param id 账户地址ID
     * @return 账户地址对象
     */
    @PostMapping(value = "/getDetail")
    @PreAuthorize("hasPermission('','/acmmgr/accountaddress') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public AccountAddressDO getDetail(@RequestParam(value = "id", required = false) Long id) {
        try {
            logger.debug("获取账户地址详情，ID: {}", id);
            AccountAddressDO result = accountAddressService.getDetail(id);
            if (result != null) {
                logger.debug("获取账户地址详情成功，ID: {}, 地址: {}", id, result.getAddress());
            } else {
                logger.warn("未找到账户地址记录，ID: {}", id);
            }
            return result;
        } catch (Exception e) {
            logger.error("获取账户地址详情失败，ID: {}", id, e);
            return null;
        }
    }

    /**
     * 添加新的账户地址
     * 
     * @param accountAddress 账户地址对象
     * @return 操作结果
     */
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/acmmgr/accountaddress:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(AccountAddressDO accountAddress) {
        Map<String, String> response = new HashMap<>();
        try {
            logger.debug("添加账户地址，地址: {}", accountAddress != null ? accountAddress.getAddress() : "null");
            String result = accountAddressService.add(accountAddress);
            response.put("result", result);
            
            if ("ACM00000".equals(result)) {
                logger.info("添加账户地址成功，地址: {}", accountAddress.getAddress());
            } else {
                logger.warn("添加账户地址失败，结果代码: {}, 地址: {}", result, accountAddress != null ? accountAddress.getAddress() : "null");
            }
        } catch (Exception e) {
            logger.error("添加账户地址失败: ", e);
            response.put("result", "ACM10011"); // 系统错误
        }
        return response;
    }

    /**
     * 验证地址唯一性
     * 
     * @param address 区块链地址
     * @return 验证结果
     */
    @PostMapping(value = "/validateAddress")
    @PreAuthorize("hasPermission('','/acmmgr/accountaddress') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, Boolean> validateAddress(@RequestParam(value = "address", required = false) String address) {
        Map<String, Boolean> response = new HashMap<>();
        try {
            logger.debug("验证地址唯一性，地址: {}", address);
            boolean isValid = accountAddressService.validateAddress(address);
            response.put("valid", isValid);
            logger.debug("地址唯一性验证结果，地址: {}, 可用: {}", address, isValid);
        } catch (Exception e) {
            logger.error("验证地址唯一性失败，地址: {}", address, e);
            response.put("valid", false);
        }
        return response;
    }
}