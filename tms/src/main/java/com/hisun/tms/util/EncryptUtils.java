package com.hisun.tms.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.CommonEncryptReqDTO;
import com.hisun.lemon.cmm.dto.CommonEncryptRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.urm.model.MerRegister;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 银行卡号、证件号加密和解密工具类
 */
@Component
public class EncryptUtils {

    @Resource
    private CmmServerClient cmmServerClient;

    /**
     * 加密和解密
     * 加解密类型 encrypt:加密  decrypt:解密
     */
    public String encrypt(String data, String type) {
        //发送请求
        GenericDTO<CommonEncryptReqDTO> genericDTO = new GenericDTO<>();
        CommonEncryptReqDTO commonEncryptReqDTO = new CommonEncryptReqDTO();
        commonEncryptReqDTO.setData(data);
        commonEncryptReqDTO.setType(type);
        genericDTO.setBody(commonEncryptReqDTO);

        //返回结果
        GenericRspDTO<CommonEncryptRspDTO> genericRspDTO = cmmServerClient.encrypt(genericDTO);
        CommonEncryptRspDTO commonEncryptRspDTO = genericRspDTO.getBody();
        if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
            LemonException.throwBusinessException(genericRspDTO.getMsgCd());
        }
        return commonEncryptRspDTO.getData();
    }


    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String passwd = "1234qwer";
        String passEn = encoder.encode(passwd);


        System.out.println(passEn);
        System.out.println(encoder.encode(passEn));

        String str = "{\"userId\":\"\",\"mercName\":\"新月大饭店\",\"mercShortName\":\"新月\",\"cprRegNmCn\":null,\"cprOperNmCn\":null,\"prinNm\":null,\"crpNm\":\"张三\",\"crpIdTyp\":\"0\",\"crpIdNo\":\"A100001\",\"comercReg\":\"1001998\",\"socialCrdCd\":null,\"orgCd\":null,\"busiLisc\":null,\"taxCertId\":null,\"webNm\":null,\"webUrl\":null,\"merRegAddr\":\"jinBian_金边\",\"merAddrLongitude\":null,\"merAddrLatitude\":null,\"mgtScp\":\"all\",\"needInvFlg\":null,\"invMod\":null,\"invTit\":null,\"invMailAddr\":null,\"invMailZip\":null,\"mercTrdCls\":\"5812\",\"mercTrdDesc\":null,\"cprTyp\":\"02\",\"csTelNo\":null,\"mercHotLin\":null,\"cusMgr\":null,\"cusMgrNm\":null,\"rcvMagAmt\":null,\"mblNo\":\"13510376811\",\"diplayNm\":\"李四\",\"email\":\"<EMAIL>\",\"loginId\":\"A100001\",\"openWechatMerflag\":\"no\",\"mercLogoPath\":\"/img/uploadBut.png\",\"busLicImgPath\":\"/img/uploadBut.png\",\"cardImgPath\":\"/img/uploadBut.png\",\"cardImgPathB\":\"/img/uploadBut.png\",\"certImgPath\":\"/img/uploadBut.png\",\"certImgPathB\":\"/img/uploadBut.png\",\"merPotocolImgPath\":\"/img/uploadBut.png\",\"opnBusDtSr\":\"2021-02-05 \",\"refereeMblNo\":\"\",\"belongMerc\":\"\",\"merLvl\":null,\"capCardNo\":\"\",\"capCardName\":\"\",\"capCorgNm\":\"corg.null\",\"capCorgNo\":null,\"subbranch\":\"\",\"settleType\":\"hall\",\"settleCycleType\":\"daily\",\"mercSts\":\"\",\"settleExpDate\":\"2021-05-28 \",\"settleEffDate\":\"2021-02-05 \",\"settleSite\":\"002\",\"hallSites\":\"no\",\"drawDays\":\"0\",\"refuseReson\":null,\"rateInfolist\":[{\"busType\":\"1010\",\"chargeType\":\"single\",\"calculateType\":\"percent\",\"beginCalAmt\":null,\"rate\":\"10\",\"minFee\":\"\",\"maxFee\":\"\",\"expDate\":\"2021-05-22 \",\"effDate\":\"2021-02-05 \",\"beginCalFee\":\"\",\"channel\":\"Seatelpay\",\"calculateMod\":\"internal\"},{\"busType\":\"0403\",\"chargeType\":\"single\",\"calculateType\":\"percent\",\"beginCalAmt\":null,\"rate\":\"10\",\"minFee\":\"\",\"maxFee\":\"\",\"expDate\":\"2021-08-31 \",\"effDate\":\"2021-02-05 \",\"beginCalFee\":\"\",\"channel\":\"Seatelpay\",\"calculateMod\":\"internal\"},{\"busType\":\"0201\",\"chargeType\":\"single\",\"calculateType\":\"percent\",\"beginCalAmt\":null,\"rate\":\"0\",\"minFee\":\"\",\"maxFee\":\"\",\"expDate\":\"2021-07-31 \",\"effDate\":\"2021-02-05 \",\"beginCalFee\":\"\",\"channel\":\"WeChat\",\"calculateMod\":\"internal\"},{\"busType\":\"0202\",\"chargeType\":\"single\",\"calculateType\":\"percent\",\"beginCalAmt\":null,\"rate\":\"0\",\"minFee\":\"\",\"maxFee\":\"\",\"expDate\":\"2021-04-30 \",\"effDate\":\"2021-02-05 \",\"beginCalFee\":\"\",\"channel\":\"WeChat\",\"calculateMod\":\"internal\"},{\"busType\":\"0201\",\"chargeType\":\"single\",\"calculateType\":\"percent\",\"beginCalAmt\":null,\"rate\":\"0\",\"minFee\":\"\",\"maxFee\":\"\",\"expDate\":\"2021-08-28 \",\"effDate\":\"2021-02-05 \",\"beginCalFee\":\"\",\"channel\":\"Alipay\",\"calculateMod\":\"internal\"},{\"busType\":\"0202\",\"chargeType\":\"single\",\"calculateType\":\"percent\",\"beginCalAmt\":null,\"rate\":\"0\",\"minFee\":\"\",\"maxFee\":\"\",\"expDate\":\"2021-06-30 \",\"effDate\":\"2021-02-05 \",\"beginCalFee\":\"\",\"channel\":\"Alipay\",\"calculateMod\":\"internal\"},{\"busType\":\"0201\",\"chargeType\":\"single\",\"calculateType\":\"percent\",\"beginCalAmt\":null,\"rate\":\"0\",\"minFee\":\"\",\"maxFee\":\"\",\"expDate\":\"2021-06-30 \",\"effDate\":\"2021-02-05 \",\"beginCalFee\":\"\",\"channel\":\"BESTPAY\",\"calculateMod\":\"internal\"},{\"busType\":\"0202\",\"chargeType\":\"single\",\"calculateType\":\"percent\",\"beginCalAmt\":null,\"rate\":\"0\",\"minFee\":\"\",\"maxFee\":\"\",\"expDate\":\"2021-09-30 \",\"effDate\":\"2021-02-05 \",\"beginCalFee\":\"\",\"channel\":\"BESTPAY\",\"calculateMod\":\"internal\"}]}\n";
        ObjectMapper mapper = new ObjectMapper();
        try {
            MerRegister merRegister = mapper.readValue(str, MerRegister.class);
            System.out.println(merRegister.toString());

            BigDecimal normalTopUp = new BigDecimal("2000");
            BigDecimal midTopUp = new BigDecimal("5000");

            BigDecimal single = new BigDecimal("100");
            BigDecimal normalDiscount = new BigDecimal("0.95");
            BigDecimal midDiscount = new BigDecimal("0.9");

            BigDecimal cnt = new BigDecimal("1.5");

            BigDecimal normalCnt = normalTopUp.divide(single.multiply(normalDiscount).multiply(cnt),BigDecimal.ROUND_DOWN).setScale(2);
            System.out.println("普通会员充值￥"+normalTopUp + "可打" + normalCnt + "周");

            BigDecimal midCnt = midTopUp.divide(single.multiply(midDiscount).multiply(cnt),BigDecimal.ROUND_DOWN).setScale(2);
            System.out.println("青铜卡会员充值￥"+midTopUp + "可打" + midCnt + "周");


        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
