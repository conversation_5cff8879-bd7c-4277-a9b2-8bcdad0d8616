package com.hisun.tms.util;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Date;
import java.util.zip.Deflater;
import java.util.zip.Inflater;

import org.apache.commons.codec.binary.Base64;


public class Base64Utils {

//	public void test(){
//		String s = "iVBORw0KGgoAAAANSUhEUgAAAHYAAABUCAYAAABXw8MsAAABYElEQVR4nO3cqxHCUBgF4T8MJdAGLoYOMBSBwaGicg0uDkURFIChA0wcjhrogUcEkwIgd9jsZ/JQZ2YniUvxeAnhFHVdGxakaZruOO1f6L+llD7nk4w79EOGhTIslGGhDAtlWCjDQhkWyrBQhoUyLJRhoQwLZVgow0IZFsqwUIaFMiyUYaEMC2VYKMNCGRbKsFCGhTIslGGhDAtlWCjDQhkWyrBQhoUyLJRhoUYR9n7aRrm/5p4xqFGEHSPDQk1zD/iZ6z7K9bF34xJld7mI3fkQq1mmXQPhhp1X0bZVd/r+xi5vm2ireeZRw/FVDGVYKO6ruGe2OkSbe8TAfGKhDAtlWCjDQhkWyrBQhoUyLJRhoQwLZVgow0IZFsqwUIaFMiyUYaEMC2VYKMNCGRbKsFCGhTIslGGhDAtlWCjDQhkWyrBQhoUyLJRhoQwLZVio7h8UKaXcO/RlxeMl9wh93xOuJiS/UuS/dgAAAABJRU5ErkJggg==";
//		deCodeFileContentNotInflater(s,"D:\\merc"+File.separator+String.valueOf(new Date().getTime())+".png","UTF-8");
//	}
	/**
	 * 功能：将文件内容使用DEFLATE压缩算法压缩，Base64编码生成字符串并返回<br>
	 * @param filePathName 文件-全路径文件名<br>
	 * @return
	 */
	public static String enCodeFileContent(String filePathName,String encoding){
		String baseFileContent = "";
		
		File file = new File(filePathName);
		if (!file.exists()) {
			try {
				file.createNewFile();
			} catch (IOException e) {
				//logger.info(e);
			}
		}
		InputStream in = null;
		try {
			in = new FileInputStream(file);
			int fl = in.available();
			if (null != in) {
				byte[] s = new byte[fl];
				in.read(s, 0, fl);
				// 压缩编码.
				baseFileContent = new String(base64Encode(deflater(s)),encoding);
			}
		} catch (Exception e) {
		} finally {
			if (null != in) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
		}
		return baseFileContent;
	}
	/**
	 * 功能：将文件内容使用DEFLATE压缩算法压缩，Base64编码生成字符串并返回<br>
	 * @param filePathName 文件-全路径文件名<br>
	 * @return
	 */
	public static String enCodeFileContentNotDeflate(String filePathName,String encoding){
		String baseFileContent = "";
		
		File file = new File(filePathName);
		if (!file.exists()) {
			try {
				file.createNewFile();
			} catch (IOException e) {
				//logger.info(e);
			}
		}
		InputStream in = null;
		try {
			in = new FileInputStream(file);
			int fl = in.available();
			if (null != in) {
				byte[] s = new byte[fl];
				in.read(s, 0, fl);
				// 压缩编码.
				//baseFileContent = new String(base64Encode(deflater(s)),encoding);
				baseFileContent = new String(base64Encode(s),encoding);
			}
		} catch (Exception e) {
		} finally {
			if (null != in) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
		}
		return baseFileContent;
	}
	
	/**
	 * 功能：解析fileContent字符串并落地 （ 解base64，解DEFLATE压缩并落地）<br>
	 * @param fileContent 返回报文中得文件内容<br>
	 * @param filePathName 落地的文件路径（绝对路径）
	 * @param encoding 内容encoding字段的值<br>	
	 */
	public static String deCodeFileContent(String fileContent,String filePathName, String encoding)  {
		// 解析文件
		FileOutputStream out=null;
		if (null != fileContent && !"".equals(fileContent)) {
			try {
				//fileContent=URLDecoder.decode(fileContent, "UTF-8");
				byte[] fileArray = inflater(base64Decode(fileContent.getBytes(encoding)));
				File file = new File(filePathName);
				if (file.exists()) {
					file.delete();
				}
				file.createNewFile();
				out = new FileOutputStream(file);
				out.write(fileArray, 0, fileArray.length);
				out.flush();
				out.close();
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			} finally {
				if (out!=null){
					try{
						out.close();
					}catch(Exception e){
					}
				}
			}
		}
		return filePathName;
	}
	
	/**
	 * 功能：解析fileContent字符串并落地 （ 解base64，解DEFLATE压缩并落地）<br>
	 * @param fileContent 返回报文中得文件内容<br>
	 * @param filePathName 落地的文件路径（绝对路径）
	 * @param encoding 内容encoding字段的值<br>	
	 */
	public static String deCodeFileContentNotInflater(String fileContent,String filePathName, String encoding)  {
		// 解析文件
		FileOutputStream out=null;
		if (null != fileContent && !"".equals(fileContent)) {
			try {
				//fileContent=URLDecoder.decode(fileContent, "UTF-8");
				byte[] fileArray = base64Decode(fileContent.getBytes(encoding));
				File file = new File(filePathName);
				if (file.exists()) {
					file.delete();
				}
				file.createNewFile();
				out = new FileOutputStream(file);
				out.write(fileArray, 0, fileArray.length);
				out.flush();
				out.close();
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			} finally {
				if (out!=null){
					try{
						out.close();
					}catch(Exception e){
						e.printStackTrace();
					}
				}
			}
		}
		return filePathName;
	}

	/**
	 * 将内容 转换成明文字符串：解base64,解压缩<br>
	 * @param content 经过base64编码，经过压缩的内容<br>
	 * @return 内容明文<br>
	 */
	public static String getRelContent(String content,String encoding) {
		String fc = "";
		try {
			fc = new String(inflater(base64Decode(content.getBytes(encoding))),encoding);
		} catch (UnsupportedEncodingException e) {
		} catch (IOException e) {
		}catch(Exception e){
			e.printStackTrace();
		}
		return fc;
	}
	
	/**
	 * BASE64编码
	 * 
	 * @param inputByte
	 *            待编码数据
	 * @return 解码后的数据
	 * @throws IOException
	 */
	public static byte[] base64Encode(byte[] inputByte) throws IOException {
		return Base64.encodeBase64(inputByte);
	}
	
	/**
	 * BASE64解码
	 * 
	 * @param inputByte
	 *            待解码数据
	 * @return 解码后的数据
	 * @throws IOException
	 */
	public static byte[] base64Decode(byte[] inputByte) throws IOException {
		return Base64.decodeBase64(inputByte);
	}
	
	/**
	 * BASE64编码
	 * 
	 * @param inputByte
	 *            待编码数据
	 * @return 解码后的数据
	 * @throws IOException
	 */
	public static String base64EncodeStr(String inputByte) throws IOException {
		return new String(Base64.encodeBase64(inputByte.getBytes("UTF-8")),"UTF-8");
	}
	
	/**
	 * BASE64解码
	 * 
	 * @param inputByte
	 *            待解码数据
	 * @return 解码后的数据
	 * @throws IOException
	 */
	public static String base64DecodeStr(String inputByte) throws IOException {
		return new String(Base64.decodeBase64(inputByte.getBytes("UTF-8")),"UTF-8");
	}
	/**
	 * 压缩.
	 * 
	 * @param inputByte
	 *            需要解压缩的byte[]数组
	 * @return 压缩后的数据
	 * @throws IOException
	 */
	public static byte[] deflater(final byte[] inputByte) throws IOException {
		int compressedDataLength = 0;
		Deflater compresser = new Deflater();
		compresser.setInput(inputByte);
		compresser.finish();
		ByteArrayOutputStream o = new ByteArrayOutputStream(inputByte.length);
		byte[] result = new byte[1024];
		try {
			while (!compresser.finished()) {
				compressedDataLength = compresser.deflate(result);
				o.write(result, 0, compressedDataLength);
			}
		} finally {
			o.close();
		}
		compresser.end();
		return o.toByteArray();
	}
	
	/**
	 * 解压缩.
	 * 
	 * @param inputByte
	 *            byte[]数组类型的数据
	 * @return 解压缩后的数据
	 * @throws Exception 
	 * @throws IOException
	 */
	public static byte[] inflater(final byte[] inputByte) throws Exception{
		int compressedDataLength = 0;
		Inflater compresser = new Inflater(false);
		compresser.setInput(inputByte, 0, inputByte.length);
		ByteArrayOutputStream o = new ByteArrayOutputStream(inputByte.length);
		byte[] result = new byte[1024];
		try {
			while (!compresser.finished()) {
				compressedDataLength = compresser.inflate(result);
				if (compressedDataLength == 0) {
					break;
				}
				o.write(result, 0, compressedDataLength);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			throw ex;
		} finally {
			try {
				o.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		compresser.end();
		return o.toByteArray();
	}
	
	public static String getMD5(byte[] source){
		String result = null;
		char hexDigits[] = { // 用来将字节转换成 16 进制表示的字符
		'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',
				'e', 'f' };
		try{
			java.security.MessageDigest md = java.security.MessageDigest
					.getInstance("MD5");
			md.update(source);
			byte tmp[] = md.digest(); // MD5 的计算结果是一个 128 位的长整数，
			// 用字节表示就是 16 个字节
			char str[] = new char[16 * 2]; // 每个字节用 16 进制表示的话，使用两个字符，
			// 所以表示成 16 进制需要 32 个字符
			int k = 0; // 表示转换结果中对应的字符位置
			for (int i = 0; i < 16; i++)
			{ // 从第一个字节开始，对 MD5 的每一个字节
				// 转换成 16 进制字符的转换
				byte byte0 = tmp[i]; // 取第 i 个字节
				str[k++] = hexDigits[byte0 >>> 4 & 0xf]; // 取字节中高 4 位的数字转换,
				// >>> 为逻辑右移，将符号位一起右移
				str[k++] = hexDigits[byte0 & 0xf]; // 取字节中低 4 位的数字转换
			}
			result = new String(str); // 换后的结果转换为字符串

		}catch (Exception e){
		}
		return result;
	}
}
