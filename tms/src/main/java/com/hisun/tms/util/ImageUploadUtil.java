package com.hisun.tms.util;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.hisun.tms.rsm.service.BlackListService;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description 图片服务器工具类
 * <AUTHOR>
 * @date 2017年8月30日 下午7:09:35 
 * @version V1.0
 */
public class ImageUploadUtil {
    private static final Logger logger = LoggerFactory.getLogger(ImageUploadUtil.class);
    private ImageUploadUtil() {
        
    }
    
    static Charset charset = StandardCharsets.UTF_8;
    static String separator = "&";
    static String uploadUrl_V0 = "/upload/v0";
    
    public static String uploadImage(String baseUrl, String key, String bucket, String fileName) {
        JsonObject object = new JsonObject();
        object.addProperty("bucket", bucket);
        object.addProperty("timestamp", TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
        object.addProperty("secret", "public");
        String jsonStr = object.toString();
        byte[] b = Base64.encodeBase64(jsonStr.getBytes());
        String policy = new String(b);
        List<String> list = new ArrayList<>();
        list.add(policy);
        list.add(key);
        String joinStr = StringUtils.join(list, separator);
        String signature = DigestUtils.md5Hex(joinStr);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        File file = new File(fileName);
        FileBody fileBody = new FileBody(file);
        StringBody policyBody = new StringBody(policy, ContentType.TEXT_PLAIN);
        StringBody signatureBody = new StringBody(signature, ContentType.TEXT_PLAIN);
        HttpEntity httpEntity = MultipartEntityBuilder.create().setCharset(charset).addPart("file", fileBody)
                .addPart("policy", policyBody).addPart("signature", signatureBody).build();
        HttpPost httpPost = new HttpPost(baseUrl + uploadUrl_V0);
        httpPost.setEntity(httpEntity);
        CloseableHttpResponse response = null;
        String returnString = null;
        JsonObject returnObject = null;
        JsonElement resultElement = null;
        try {
            response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            returnString = EntityUtils.toString(entity, charset);
            logger.info("返回字符串"+returnString + "HttpCode" +response.getStatusLine().getStatusCode());
            returnObject = new JsonParser().parse(returnString).getAsJsonObject();
            resultElement = returnObject.get("file_urls");
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return resultElement.getAsString();
    }

    public class Result {
        public String msg_inf;
        public String msg_cd;
        public String access_token;
        public String file_urls;

        public String getMsg_inf() {
            return msg_inf;
        }

        public void setMsg_inf(String msg_inf) {
            this.msg_inf = msg_inf;
        }

        public String getMsg_cd() {
            return msg_cd;
        }

        public void setMsg_cd(String msg_cd) {
            this.msg_cd = msg_cd;
        }

        public String getAccess_token() {
            return access_token;
        }

        public void setAccess_token(String access_token) {
            this.access_token = access_token;
        }

        public String getFile_urls() {
            return file_urls;
        }

        public void setFile_urls(String file_urls) {
            this.file_urls = file_urls;
        }

    }

}
