package com.hisun.tms.onr.model;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.math.BigDecimal;
import java.time.LocalDate;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "onr_order_inf")
public class OnrOrderInfo extends AbstractEntityTmpe {
    /**
     * @Fields orderNo 订单号
     */
	@Id
    @Column(name = "onr_order_no")
    private String onrOrderNo;
	
	@Column(name = "csh_order_no")
    private String cshOrderNo;
    /**
     * 商户号
     */
    @Column(name = "merc_id")
    private String mercId;
    /**
     * 用户号
     */
    @Column(name = "user_id")
    private String userId;
    /**
     * 订单金额
     */
    @Column(name = "order_amt", precision = 15, scale = 4)
    private BigDecimal orderAmt;
    /**
     * 订单状态
     */
    @Column(name = "order_stat")
    private String orderStat;
    /**
     * 币种
     */
    @Column(name = "ccy")
    private String ccy;
    /**
     * 支付金额
     */
    @Column(name = "pay_amt", precision = 15, scale = 4)
    private BigDecimal payAmt;
    /**
     * 优惠类型 00不使用优惠,01电子券,02海币,03折扣券,04优惠券
     */
    @Column(name = "coupon_type")
    private String couponType;
    /**
     * 优惠金额
     */
    @Column(name = "coupon_amt")
    private BigDecimal couponAmt;
    /**
     * 已退款金额
     */
    @Column(name = "ref_amt", precision = 15, scale = 4)
    private BigDecimal refAmt;
    /**
     * 订单描述
     */
    @Column(name = "order_desc")
    private String orderDesc;
    /**
     * @Fields tradeDate 交易日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "csh_order_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate cshOrderDt;

	/**
	 * 支付方式
	 */
	@Column(name = "pay_type")
	private String payTyp;
    
    public BigDecimal getRefAmt() {
		return refAmt;
	}

	public void setRefAmt(BigDecimal refAmt) {
		this.refAmt = refAmt;
	}

	public String getOnrOrderNo() {
		return onrOrderNo;
	}

	public void setOnrOrderNo(String onrOrderNo) {
		this.onrOrderNo = onrOrderNo;
	}

    public String getCshOrderNo() {
		return cshOrderNo;
	}

	public void setCshOrderNo(String cshOrderNo) {
		this.cshOrderNo = cshOrderNo;
	}

	public String getMercId() {
		return mercId;
	}

	public void setMercId(String mercId) {
		this.mercId = mercId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public BigDecimal getOrderAmt() {
		return orderAmt;
	}

	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}

	public String getOrderStat() {
		return orderStat;
	}

	public void setOrderStat(String orderStat) {
		this.orderStat = orderStat;
	}

	public String getCcy() {
		return ccy;
	}

	public void setCcy(String ccy) {
		this.ccy = ccy;
	}

	public BigDecimal getPayAmt() {
		return payAmt;
	}

	public void setPayAmt(BigDecimal payAmt) {
		this.payAmt = payAmt;
	}

	public String getOrderDesc() {
		return orderDesc;
	}

	public void setOrderDesc(String orderDesc) {
		this.orderDesc = orderDesc;
	}

	public LocalDate getCshOrderDt() {
		return cshOrderDt;
	}

	public void setCshOrderDt(LocalDate cshOrderDt) {
		this.cshOrderDt = cshOrderDt;
	}

	public String getCouponType() {
		return couponType;
	}

	public void setCouponType(String couponType) {
		this.couponType = couponType;
	}

	public BigDecimal getCouponAmt() {
		return couponAmt;
	}

	public void setCouponAmt(BigDecimal couponAmt) {
		this.couponAmt = couponAmt;
	}

	public String getPayTyp() {
		return payTyp;
	}

	public void setPayTyp(String payTyp) {
		this.payTyp = payTyp;
	}
}
