package com.hisun.tms.onr.controller;



import com.hisun.lemon.onr.dto.MerchantOrderRefundReqDTO;

import com.hisun.tms.onr.model.OnrOrderInfo;

import com.hisun.tms.onr.model.OnrOrderParamInput;
import com.hisun.tms.onr.service.OnrOrderService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.Map;

import javax.validation.Valid;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/onr/orderqueryctrl")
public class OnrOrderController {

    private static final Logger logger = LoggerFactory.getLogger(OnrOrderController.class);

    @Autowired
    private OnrOrderService onrOrderService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/orderquery') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("onr/onrorderinfo/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/orderquery') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<OnrOrderInfo> findAll(@Valid @RequestBody OnrOrderParamInput onrOrderParamInput) {
        return onrOrderService.findAll(onrOrderParamInput);
    }
    
    @GetMapping(value = "/rfdform")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/orderquery/rfdform') or hasRole('ROLE_ADMIN')")
    public ModelAndView form(@RequestParam(value = "id", required = true) String id) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("onr/onrorderinfo/rfdform");
        OnrOrderInfo onrOrderInfo = onrOrderService.get(id);
        modelAndView.addObject("rfdActivity", onrOrderInfo);
        String api = "/onr/orderqueryctrl/refund";
        modelAndView.addObject("api", api);
        return modelAndView;
    }
    
    @PostMapping(value = "refund")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/orderquery/rfdform') or hasRole('ROLE_ADMIN')")
    public Map<String,String> orderRefund(MerchantOrderRefundReqDTO merchantOrderRefundReqDTO) {
    	return onrOrderService.orderRefund(merchantOrderRefundReqDTO);
    }
    
}
