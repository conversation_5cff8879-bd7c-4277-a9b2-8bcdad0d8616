package com.hisun.tms.onr.model;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.math.BigDecimal;
import java.time.LocalDate;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "onr_rfd_order_inf")
public class OnrRfdOrderInfo extends AbstractEntityTmpe {
    /**
     * @Fields orderNo 订单号
     */
	@Id
	@Column(name = "onr_rfd_no")
    private String onrRfdNo;
	
    @Column(name = "onr_order_no")
    private String onrOrderNo;
	
	@Column(name = "csh_order_no")
    private String cshOrderNo;
    /**
     * 商户号
     */
    @Column(name = "merc_id")
    private String mercId;
    /**
     * 用户号
     */
    @Column(name = "user_id")
    private String userId;
    /**
     * 退款金额
     */
    @Column(name = "rfd_amt", precision = 15, scale = 4)
    private BigDecimal rfdAmt;
    /**
     * 退款状态
     */
    @Column(name = "rfd_stat")
    private String rfdStat;
    /**
     * 币种
     */
    @Column(name = "ccy")
    private String ccy;
    /**
     * 订单描述
     */
    @Column(name = "rfd_rmk")
    private String rfdRmk;
    /**
     * @Fields cshRfdDt 退款日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "csh_rfd_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate cshRfdDt;
    

	public String getOnrOrderNo() {
		return onrOrderNo;
	}


	public void setOnrOrderNo(String onrOrderNo) {
		this.onrOrderNo = onrOrderNo;
	}


	public String getCshOrderNo() {
		return cshOrderNo;
	}


	public void setCshOrderNo(String cshOrderNo) {
		this.cshOrderNo = cshOrderNo;
	}


	public String getOnrRfdNo() {
		return onrRfdNo;
	}


	public void setOnrRfdNo(String onrRfdNo) {
		this.onrRfdNo = onrRfdNo;
	}


	public String getMercId() {
		return mercId;
	}


	public void setMercId(String mercId) {
		this.mercId = mercId;
	}


	public String getUserId() {
		return userId;
	}


	public void setUserId(String userId) {
		this.userId = userId;
	}


	public BigDecimal getRfdAmt() {
		return rfdAmt;
	}


	public void setRfdAmt(BigDecimal rfdAmt) {
		this.rfdAmt = rfdAmt;
	}


	public String getRfdStat() {
		return rfdStat;
	}


	public void setRfdStat(String rfdStat) {
		this.rfdStat = rfdStat;
	}


	public String getCcy() {
		return ccy;
	}


	public void setCcy(String ccy) {
		this.ccy = ccy;
	}


	public String getRfdRmk() {
		return rfdRmk;
	}


	public void setRfdRmk(String rfdRmk) {
		this.rfdRmk = rfdRmk;
	}


	public LocalDate getCshRfdDt() {
		return cshRfdDt;
	}


	public void setCshRfdDt(LocalDate cshRfdDt) {
		this.cshRfdDt = cshRfdDt;
	}


	@Override
    public String toString() {
        return "OnrRfdOrderInfo{" +
        		"onrOrderNo='" + onrOrderNo + '\'' +
        		"cshOrderNo='" + cshOrderNo + '\'' +
                "onrRfdNo='" + onrRfdNo + '\'' +
                ", mercId='" + mercId + '\'' +
                ", userId='" + userId + '\'' +
                ", rfdAmt=" + rfdAmt +
                ", ccy='" + ccy + '\'' +
                ", rfdStat='" + rfdStat + '\'' +
                ", cshRfdDt=" + cshRfdDt +
                ", rfdRmk='" + rfdRmk + '\'' +
                '}';
    }
}
