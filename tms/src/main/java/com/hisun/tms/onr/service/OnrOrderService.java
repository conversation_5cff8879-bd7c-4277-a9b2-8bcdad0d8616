package com.hisun.tms.onr.service;

import com.hisun.lemon.onr.dto.MerchantOrderRefundReqDTO;

import com.hisun.tms.onr.model.OnrOrderInfo;

import com.hisun.tms.onr.model.OnrOrderParamInput;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface OnrOrderService {

    DataTablesOutput<OnrOrderInfo> findAll(OnrOrderParamInput onrOrderParamInput);

    Map<String,String> orderRefund(MerchantOrderRefundReqDTO merchantOrderRefundReqDTO);

	OnrOrderInfo get(String id);
}