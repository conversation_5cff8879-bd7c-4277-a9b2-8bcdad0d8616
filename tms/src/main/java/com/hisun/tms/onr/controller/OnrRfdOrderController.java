package com.hisun.tms.onr.controller;




import com.hisun.tms.onr.model.OnrOrderParamInput;
import com.hisun.tms.onr.model.OnrRfdOrderInfo;
import com.hisun.tms.onr.service.OnrRfdOrderService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;


import javax.validation.Valid;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/onr/onrrfdordctrl")
public class OnrRfdOrderController {

    private static final Logger logger = LoggerFactory.getLogger(OnrRfdOrderController.class);

    @Autowired
    private OnrRfdOrderService onrRfdOrderService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/onrrfdord') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("onr/onrorderinfo/rfdindex");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/onrrfdord') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<OnrRfdOrderInfo> findAll(@Valid @RequestBody OnrOrderParamInput onrOrderParamInput) {
        return onrRfdOrderService.findAll(onrOrderParamInput);
    }
}
