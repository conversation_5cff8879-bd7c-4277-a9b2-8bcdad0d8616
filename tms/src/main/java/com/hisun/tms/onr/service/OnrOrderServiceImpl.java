package com.hisun.tms.onr.service;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.onr.client.OrderRefundClient;
import com.hisun.lemon.onr.dto.MerchantOrderRefundReqDTO;
import com.hisun.tms.onr.model.OnrOrderInfo;
import com.hisun.tms.onr.model.OnrOrderParamInput;
import com.hisun.tms.onr.repository.DatatablesOnrOrderRepository;
import com.hisun.tms.onr.repository.OnrOrderRepository;
import com.hisun.tms.specifications.Specifications;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.mapping.Order;
import org.springframework.data.jpa.domain.Specification;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("onrOrderService")
public class OnrOrderServiceImpl implements OnrOrderService {

    private static final Logger logger = LoggerFactory.getLogger(OnrOrderServiceImpl.class);

    @Autowired
    private DatatablesOnrOrderRepository datatablesOnrOrderRepository;
    @Autowired
    private OrderRefundClient orderRefundClient;
    @Autowired
    private OnrOrderRepository onrOrderRepository;

    @Override
    @Transactional(value="onrTransactionManager",readOnly=true)
    public DataTablesOutput<OnrOrderInfo> findAll(OnrOrderParamInput input) {
    	String cshOrderNo = input.getExtra_search().get("cshOrderNo");
		String mercId = input.getExtra_search().get("mercId");
		String orderStat = input.getExtra_search().get("orderStat");
		String beginDateStr = input.getExtra_search().get("beginDate");
		String endDateStr = input.getExtra_search().get("endDate");
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
    	Specification<OnrOrderInfo> orderQueryParam=Specifications.<OnrOrderInfo>and()
    			.like(JudgeUtils.isNotBlank(cshOrderNo), "cshOrderNo", "%"+cshOrderNo+"%")
    			.like(JudgeUtils.isNotBlank(mercId), "mercId", "%"+mercId+"%")
    			.eq(JudgeUtils.isNotBlank(orderStat), "orderStat", orderStat)
                .between("cshOrderDt", new Range<>(beginDate, endDate))
                .build();
    	Order order=new Order(0,"desc");
    	List<Order> orders=new ArrayList<Order>();
    	orders.add(order);
    	input.setOrder(orders);
        DataTablesOutput<OnrOrderInfo> dataTablesOutput = datatablesOnrOrderRepository.findAll(input, orderQueryParam);
        List<OnrOrderInfo> onrOrderInfos=dataTablesOutput.getData();
        BigDecimal oneHundred=BigDecimal.valueOf(100);
        for(OnrOrderInfo onrOrderInfo:onrOrderInfos){
        	//如果优惠类型为海币 则优惠金额除一百
        	if(JudgeUtils.isNotBlank(onrOrderInfo.getCouponType())){
        		if(onrOrderInfo.getCouponType().equals("02")&&!onrOrderInfo.getCouponAmt().equals(BigDecimal.ZERO)){
            		onrOrderInfo.setCouponAmt(onrOrderInfo.getCouponAmt().divide(oneHundred));
            	}
        	}
        }
        dataTablesOutput.setData(onrOrderInfos);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }


	@Override
	public Map<String,String> orderRefund(MerchantOrderRefundReqDTO merchantOrderRefundReqDTO) {
		GenericDTO<MerchantOrderRefundReqDTO> genericDTO=GenericDTO.newInstance(merchantOrderRefundReqDTO);
		GenericRspDTO<NoBody> genericRspDTO=orderRefundClient.merchantOrderRefund(genericDTO);
		Map<String,String> map =  new HashMap<String,String>();
		map.put("msgCd", genericRspDTO.getMsgCd());
		map.put("msgInfo", genericRspDTO.getMsgInfo());
		return map;
	}


	@Override
	public OnrOrderInfo get(String id) {
		return onrOrderRepository.findByOnrOrderNo(id);
	}
}
