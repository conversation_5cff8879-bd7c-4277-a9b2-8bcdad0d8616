package com.hisun.tms.onr.service;



import com.hisun.lemon.common.utils.DateTimeUtils;


import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.onr.model.OnrOrderParamInput;
import com.hisun.tms.onr.model.OnrRfdOrderInfo;
import com.hisun.tms.onr.repository.DatatablesOnrRfdOrderRepository;
import com.hisun.tms.specifications.Specifications;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.mapping.Order;
import org.springframework.data.jpa.domain.Specification;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("onrRfdOrderService")
public class OnrRfdOrderServiceImpl implements OnrRfdOrderService {

    private static final Logger logger = LoggerFactory.getLogger(OnrRfdOrderServiceImpl.class);

    @Autowired
    private DatatablesOnrRfdOrderRepository datatablesOnrRfdOrderRepository;

    @Override
    @Transactional("onrTransactionManager")
    public DataTablesOutput<OnrRfdOrderInfo> findAll(OnrOrderParamInput input) {
    	String onrOrderNo = input.getExtra_search().get("onrOrderNo");
    	String cshOrderNo = input.getExtra_search().get("cshOrderNo");
		String mercId = input.getExtra_search().get("mercId");
		String rfdStat = input.getExtra_search().get("rfdStat");
		String beginDateStr = input.getExtra_search().get("beginDate");
		String endDateStr = input.getExtra_search().get("endDate");
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
    	Specification<OnrRfdOrderInfo> orderQueryParam=Specifications.<OnrRfdOrderInfo>and()
    			.like(JudgeUtils.isNotBlank(onrOrderNo), "onrOrderNo", "%"+onrOrderNo+"%")
    			.like(JudgeUtils.isNotBlank(cshOrderNo), "cshOrderNo", "%"+cshOrderNo+"%")
    			.like(JudgeUtils.isNotBlank(mercId), "mercId", "%"+mercId+"%")
    			.eq(JudgeUtils.isNotBlank(rfdStat), "rfdStat", rfdStat)
                .between("cshRfdDt", new Range<>(beginDate, endDate))
                .build();
    	Order order=new Order(3,"desc");
    	List<Order> orders=new ArrayList<Order>();
    	orders.add(order);
    	input.setOrder(orders);
    	input.addOrder("rfdOrderNo", true);
        DataTablesOutput<OnrRfdOrderInfo> dataTablesOutput = datatablesOnrRfdOrderRepository.findAll(input, orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

}
