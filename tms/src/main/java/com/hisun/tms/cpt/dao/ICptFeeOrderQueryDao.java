package com.hisun.tms.cpt.dao;

import com.hisun.tms.cpt.model.FeeOrderQueryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 手续费订单查询数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
@Mapper
public interface ICptFeeOrderQueryDao {
    
    /**
     * 根据查询条件查询手续费订单
     * 
     * @param param 查询参数
     * @return 手续费订单列表
     */
    List<FeeOrderQueryDO> findByQueryCondition(Map<String, Object> param);

    /**
     * 查询总数
     * 
     * @param param 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> param);

    /**
     * 根据订单号获取手续费订单
     * 
     * @param orderNo 订单号
     * @return 手续费订单对象
     */
    FeeOrderQueryDO getByOrderNo(String orderNo);

    /**
     * 新增手续费订单
     * 
     * @param feeOrderQueryDO 手续费订单对象
     * @return 影响行数
     */
    int insert(FeeOrderQueryDO feeOrderQueryDO);

    /**
     * 更新手续费订单
     * 
     * @param feeOrderQueryDO 手续费订单对象
     * @return 影响行数
     */
    int update(FeeOrderQueryDO feeOrderQueryDO);

    /**
     * 删除手续费订单
     * 
     * @param orderNo 订单号
     * @return 影响行数
     */
    int delete(String orderNo);
}