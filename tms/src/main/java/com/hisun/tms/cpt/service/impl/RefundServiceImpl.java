package com.hisun.tms.cpt.service.impl;

import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.dao.cpi.IRefundOrderDao;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.RefundOrderDO;
import com.hisun.tms.cpt.service.IRefundService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 运营系统-资金出入模块-退款订单查询功能
 */
@Service("refundService")
public class RefundServiceImpl implements IRefundService {

    private static final Logger logger = LoggerFactory.getLogger(RefundServiceImpl.class);

    @Resource
    private IRefundOrderDao refundOrderDao;

    /**
     * 根据条件查询列表
     */
    @Override
    public DataTablesOutput<RefundOrderDO> findAll(GenericParamInput input) {
        DataTablesOutput<RefundOrderDO> dataTablesOutput = new DataTablesOutput<>();
        List<RefundOrderDO> refundOrderDOList = new ArrayList<>() ;

        String userId = input.getExtra_search().get("userId");
        String fndOrdNo = input.getExtra_search().get("fndOrdNo");
        String rfdOrdNo = input.getExtra_search().get("rfdOrdNo");
        String rutCorpOrg = input.getExtra_search().get("rutCorpOrg");
        String corpBusSubTyp = input.getExtra_search().get("corpBusSubTyp");
        String ordSts = input.getExtra_search().get("ordSts");
        String orgJrnNo = input.getExtra_search().get("orgJrnNo");
        String beginDateStr = input.getExtra_search().get("beginDate");
        String endDateStr = input.getExtra_search().get("endDate");

        //从第几条记录开始获取数据
        Integer pageBegin = input.getStart();
        //获取多少条记录
        Integer pageNum = input.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;
        //订单开始日期
        LocalDate beginDate = null;
        //订单结束日期
        LocalDate endDate = null;

        try {
            if (JudgeUtils.isNotBlank(beginDateStr)){
                beginDateStr = beginDateStr.replaceAll("-","").trim();
                beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
            }
            if (JudgeUtils.isNotBlank(endDateStr)){
                endDateStr = endDateStr.replaceAll("-","").trim();
                endDate = DateTimeUtils.parseLocalDate(endDateStr);
            }
            refundOrderDOList = refundOrderDao.getRefundOrderList(userId, fndOrdNo, rfdOrdNo, corpBusSubTyp, rutCorpOrg,
                    ordSts, orgJrnNo, beginDate, endDate, pageBegin, pageNum);

            totNum = refundOrderDao.getRefundOrderListTotNum(userId, fndOrdNo, rfdOrdNo, corpBusSubTyp, rutCorpOrg,
                    ordSts, orgJrnNo, beginDate, endDate, pageBegin, pageNum);
        } catch (Exception e){
            logger.error("退款订单查询列表失败，异常为{}", e);
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(refundOrderDOList)){
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(refundOrderDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }
}
