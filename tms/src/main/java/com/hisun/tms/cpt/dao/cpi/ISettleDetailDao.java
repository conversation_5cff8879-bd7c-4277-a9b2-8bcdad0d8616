/*
 * @ClassName ISettleDetailDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-11 10:17:03
 */
package com.hisun.tms.cpt.dao.cpi;

import com.hisun.tms.cpt.model.RefundOrderDO;
import com.hisun.tms.cpt.model.SettleDetailDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface ISettleDetailDao {

    /**
     * @param stlFlg 结算标识
     * @param rutCorg 外部机构（路由机构）
     * @param beginDate 退款提交开始日期
     * @param endDate 退款提交截止日期
     * @param pageBegin 分页从第几条开始
     * @param pageNum 取多少条数据
     * @return
     * 根据条件，查询退款订单列表
     */
    List<SettleDetailDO> getSettleList(@Param("stlFlg") String userId,
                                           @Param("rutCorg") String fndOrdNo,
                                           @Param("beginDate") LocalDate beginDate,
                                           @Param("endDate") LocalDate endDate,
                                           @Param("pageBegin") Integer pageBegin,
                                           @Param("pageNum") Integer pageNum);

    /**
     * @param stlFlg 结算标识
     * @param rutCorg 外部机构（路由机构）
     * @param beginDate 退款提交开始日期
     * @param endDate 退款提交截止日期
     * @param pageBegin 分页从第几条开始
     * @param pageNum 取多少条数据
     * @return
     * 根据条件，查询结算明细总数
     */
    int getSettleListTotNum(@Param("stlFlg") String userId,
                            @Param("rutCorg") String fndOrdNo,
                                 @Param("beginDate") LocalDate beginDate,
                                 @Param("endDate") LocalDate endDate,
                                 @Param("pageBegin") Integer pageBegin,
                                 @Param("pageNum") Integer pageNum);
}