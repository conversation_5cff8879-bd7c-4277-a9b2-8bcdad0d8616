package com.hisun.tms.cpt.dao.cpo;

import com.hisun.tms.cpt.model.OrgnRouteDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资金流出模块，合作机构路由管理
 */
@Mapper
public interface ICpoOrgnRouteDao {
    /**
     * 新增资金机构路由信息
     */
    int insert(OrgnRouteDO orgnRouteDO);

    /**
     * 修改资金机构路由信息
     */
    int update(OrgnRouteDO orgnRouteDO);

    /**
     * 资金流出模块，查询资金机构路由信息列表
     * @param crdCorpOrg 资金机构编号
     * @param corpBusTyp 业务类型
     * @param corpBusSubTyp 业务子类型
     * @param rutCorpOrg 路由机构编号
     * @param crdAcTyp 银行卡类型，D借记卡，C贷记卡
     * @param rutEffFlg 路由生效标志，0失效，1生效
     * @param pageBegin 分页开始
     * @param pageNum 每页最大数目
     * @return
     */
    List<OrgnRouteDO> getOrgnBusiList(@Param("crdCorpOrg") String crdCorpOrg,
                                      @Param("corpBusTyp") String corpBusTyp,
                                      @Param("corpBusSubTyp") String corpBusSubTyp,
                                      @Param("rutCorpOrg") String rutCorpOrg,
                                      @Param("crdAcTyp") String crdAcTyp,
                                      @Param("rutEffFlg") String rutEffFlg,
                                      @Param("pageBegin") Integer pageBegin,
                                      @Param("pageNum") Integer pageNum);

    /**
     * 资金流出模块，查询资金机构路由信息列表总笔数
     */
    int getOrgnRouteListTotNum(@Param("crdCorpOrg") String crdCorpOrg,
                               @Param("corpBusTyp") String corpBusTyp,
                               @Param("corpBusSubTyp") String corpBusSubTyp,
                               @Param("rutCorpOrg") String rutCorpOrg,
                               @Param("crdAcTyp") String crdAcTyp,
                               @Param("rutEffFlg") String rutEffFlg);

    /**
     * 查询资金机构路由信息
     * @param crdCorpOrg 合作机构编号
     * @param corpBusTyp 业务类型
     * @param corpBusSubTyp 业务子类型
     * @param rutCorpOrg 路由机构编号
     * @param crdAcTyp 银行卡类型，D借记卡，C贷记卡
     */
    OrgnRouteDO getOrgnRouteDO(@Param("crdCorpOrg") String crdCorpOrg,
                               @Param("corpBusTyp") String corpBusTyp,
                               @Param("corpBusSubTyp") String corpBusSubTyp,
                               @Param("rutCorpOrg") String rutCorpOrg,
                               @Param("crdAcTyp") String crdAcTyp);

}
