package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.FeeOrderQueryDO;
import com.hisun.tms.cpt.model.QueryFindInput;
import com.hisun.tms.cpt.service.CptFeeOrderQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 手续费查询管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
@Controller
@RequestMapping("/cpt/param/feeorder")
public class CptFeeOrderQueryController {

    private static final Logger logger = LoggerFactory.getLogger(CptFeeOrderQueryController.class);

    @Resource
    private CptFeeOrderQueryService cptFeeOrderQueryService;

    /**
     * 手续费查询列表页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/param/fee') or hasRole('ROLE_ADMIN')")
    public ModelAndView feeOrderList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/param/feeorder/index");
        return modelAndView;
    }

    /**
     * 获取单个手续费订单
     */
    @PostMapping(value = "/getFeeOrder")
    @PreAuthorize("hasPermission('','/cptmgr/param/fee') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public FeeOrderQueryDO getFeeOrder(@RequestParam(value = "orderNo", required = false) String orderNo) {
        try {
            FeeOrderQueryDO feeOrderDetail = (FeeOrderQueryDO) cptFeeOrderQueryService.get(orderNo);
            return feeOrderDetail;
        } catch (Exception e) {
            logger.error("获取手续费订单详情失败: ", e);
            return null;
        }
    }

    /**
     * 查询所有手续费订单
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/param/fee') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<FeeOrderQueryDO> findAll(@Valid @RequestBody QueryFindInput input) {
        try {
            logger.debug("查询手续费订单列表，参数: {}", input);
            DataTablesOutput<FeeOrderQueryDO> result = cptFeeOrderQueryService.findAll(input);
            logger.debug("查询手续费订单列表，结果数量: {}", result.getData().size());
            return result;
        } catch (Exception e) {
            logger.error("查询手续费订单列表失败: ", e);
            // 返回空结果而不是null，避免前端解析错误
            DataTablesOutput<FeeOrderQueryDO> emptyResult = new DataTablesOutput<>();
            emptyResult.setDraw(input.getDraw());
            emptyResult.setRecordsTotal(0);
            emptyResult.setRecordsFiltered(0);
            return emptyResult;
        }
    }

    /**
     * 添加手续费订单
     */
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/cptmgr/param/fee:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(FeeOrderQueryDO feeOrderQueryDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = cptFeeOrderQueryService.add(feeOrderQueryDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("添加手续费订单失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 修改手续费订单
     */
    @PostMapping(value = "/modify/{orderNo}")
    @PreAuthorize("hasPermission('','/cptmgr/param/fee:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(FeeOrderQueryDO feeOrderQueryDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = cptFeeOrderQueryService.update(feeOrderQueryDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("修改手续费订单失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 删除手续费订单
     */
    @DeleteMapping(value = "/delete/{orderNo}")
    @PreAuthorize("hasPermission('','/cptmgr/param/fee:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public String delete(@PathVariable("orderNo") String orderNo) {
        String result = "";
        try {
            result = cptFeeOrderQueryService.delete(orderNo);
        } catch (Exception e) {
            logger.error("删除手续费订单失败: ", e);
            result = "";
        }
        return result;
    }
}