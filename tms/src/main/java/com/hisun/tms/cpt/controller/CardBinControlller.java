package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.CardBinDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.ICpiCardBinService;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 资金流入模块，卡bin信息管理
 */
@Controller
@RequestMapping("/cpt/param/cardbin")
public class CardBinControlller {

    @Resource
    private ICpiCardBinService cpiCardBinService;

    /**
     * 卡bin信息管理主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/param/cardbin') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/param/cardbin/cardbin");
        return modelAndView;
    }

    /**
     * 分页查询卡bin信息
     */
    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cptmgr/param/cardbin') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<CardBinDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return cpiCardBinService.findAll(input);
    }

    /**
     * 增加卡bin信息
     */
    @PostMapping(value = "add")
    @PreAuthorize("hasPermission('','/cptmgr/param/cardbin') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(@RequestBody CardBinDO cardBinDO) {
        return cpiCardBinService.add(cardBinDO);
    }

    /**
     * 修改卡bin信息
     */
    @PostMapping(value = "modify")
    @PreAuthorize("hasPermission('','/cptmgr/param/cardbin') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(@RequestBody CardBinDO cardBinDO) {
        return cpiCardBinService.modify(cardBinDO);
    }

    /**
     * 删除卡bin信息
     */
    @PostMapping(value = "delete")
    @PreAuthorize("hasPermission('','/cptmgr/param/cardbin') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> delete(@RequestBody CardBinDO cardBinDO) {
        return cpiCardBinService.delete(cardBinDO.getBinId());
    }
}
