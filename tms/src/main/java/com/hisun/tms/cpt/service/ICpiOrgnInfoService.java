package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnInfoDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.List;
import java.util.Map;

/**
 * 资金流出模块：资金机构信息管理
 */
public interface ICpiOrgnInfoService {

    /**
     * 新增合作资金机构基本信息
     */
    Map<String, String> add(OrgnInfoDO orgnInfoDO);

    /**
     * 修改合作资金机构基本信息
     */
    Map<String, String> modify(OrgnInfoDO orgnInfoDO);

    /**
     * 查询所有的合作资金机构基本信息
     */
    DataTablesOutput<OrgnInfoDO> findAll(GenericParamInput input);

    /**
     * 查询资金流出模块所有的合作机构基本信息
     */
    List<OrgnInfoDO> findAllOrgnInfo();

    /**
     * 根据机构编号，查询合作机构基本信息
     */
    OrgnInfoDO findOrgnInfo(String corpOrgId);

}
