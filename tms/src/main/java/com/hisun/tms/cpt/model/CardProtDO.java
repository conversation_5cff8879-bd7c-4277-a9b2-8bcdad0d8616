package com.hisun.tms.cpt.model;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 用户银行卡信息管理
 */
public class CardProtDO {
    /**
     * @Fields agrNo 内部协议号
     */
    private String agrNo;

    /**
     * @Fields agrEffFlg 生效标志，W：初登记，Y：生效，N：失效
     */
    private String agrEffFlg;

    /**
     * @Fields signDt 签约日期
     */
    private LocalDate signDt;

    /**
     * @Fields signTm 签约时间
     */
    private LocalTime signTm;

    /**
     * @Fields unsignDt 解约日期
     */
    private LocalDate unsignDt;

    /**
     * @Fields unsignTm 解约时间
     */
    private LocalTime unsignTm;

    /**
     * @Fields userId 内部用户号
     */
    private String userId;

    /**
     * @Fields mblNo 手机号
     */
    private String mblNo;

    /**
     * @Fields bnkPsnFlg 对公对私标志，B：对公，C：对私
     */
    private String bnkPsnFlg;

    /**
     * @Fields crdCorpOrg 资金机构
     */
    private String crdCorpOrg;

    /**
     * @Fields rutCorpOrg 路径机构
     */
    private String rutCorpOrg;

    /**
     * @Fields signAgrno 机构协议号
     */
    private String signAgrno;

    /**
     * @Fields agrDirect 协议方向，0：双向协议 1：我方单侧协议
     */
    private String agrDirect;

    /**
     * @Fields crdAcTyp 卡类型，D：借记卡，C：贷记卡
     */
    private String crdAcTyp;

    /**
     * 证件类型
     */
    private String idTyp;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 银行卡号
     */
    private String crdNo;

    /**
     * 银行卡户名
     */
    private String crdUsrNm;

    /**
     * CVV2
     */
    private String crdCvv2;

    /**
     * 有效期
     */
    private String crdExpDt;

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public String getAgrEffFlg() {
        return agrEffFlg;
    }

    public void setAgrEffFlg(String agrEffFlg) {
        this.agrEffFlg = agrEffFlg;
    }

    public LocalDate getSignDt() {
        return signDt;
    }

    public void setSignDt(LocalDate signDt) {
        this.signDt = signDt;
    }

    public LocalTime getSignTm() {
        return signTm;
    }

    public void setSignTm(LocalTime signTm) {
        this.signTm = signTm;
    }

    public LocalDate getUnsignDt() {
        return unsignDt;
    }

    public void setUnsignDt(LocalDate unsignDt) {
        this.unsignDt = unsignDt;
    }

    public LocalTime getUnsignTm() {
        return unsignTm;
    }

    public void setUnsignTm(LocalTime unsignTm) {
        this.unsignTm = unsignTm;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getBnkPsnFlg() {
        return bnkPsnFlg;
    }

    public void setBnkPsnFlg(String bnkPsnFlg) {
        this.bnkPsnFlg = bnkPsnFlg;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getSignAgrno() {
        return signAgrno;
    }

    public void setSignAgrno(String signAgrno) {
        this.signAgrno = signAgrno;
    }

    public String getAgrDirect() {
        return agrDirect;
    }

    public void setAgrDirect(String agrDirect) {
        this.agrDirect = agrDirect;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getCrdNo() {
        return crdNo;
    }

    public void setCrdNo(String crdNo) {
        this.crdNo = crdNo;
    }

    public String getCrdCvv2() {
        return crdCvv2;
    }

    public void setCrdCvv2(String crdCvv2) {
        this.crdCvv2 = crdCvv2;
    }

    public String getCrdExpDt() {
        return crdExpDt;
    }

    public void setCrdExpDt(String crdExpDt) {
        this.crdExpDt = crdExpDt;
    }
}
