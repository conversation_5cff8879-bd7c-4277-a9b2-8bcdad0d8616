package com.hisun.tms.cpt.service.impl;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.cpt.dao.ICptFeeOrderQueryDao;
import com.hisun.tms.cpt.model.FeeOrderQueryDO;
import com.hisun.tms.cpt.model.QueryFindInput;
import com.hisun.tms.cpt.service.CptFeeOrderQueryService;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 手续费订单查询服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
@Service("cptFeeOrderQueryService")
@Transactional
public class CptFeeOrderQueryServiceImpl implements CptFeeOrderQueryService {

    @Resource
    private ICptFeeOrderQueryDao iCptFeeOrderQueryDao;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<FeeOrderQueryDO> findAll(QueryFindInput input) {
        DataTablesOutput<FeeOrderQueryDO> dataTablesOutput = new DataTablesOutput<>();

        Map<String, Object> param = new HashMap<>();
        
        // 从extra_search中获取查询条件
        if (input.getExtra_search() != null) {
            param.put("orderNo", input.getExtra_search().get("orderNo"));
            param.put("busOrderNo", input.getExtra_search().get("busOrderNo"));
            param.put("busType", input.getExtra_search().get("busType"));
            param.put("userId", input.getExtra_search().get("userId"));
            param.put("userName", input.getExtra_search().get("userName"));
            param.put("ccy", input.getExtra_search().get("ccy"));
        }
        
        // 设置分页参数
        param.put("pageEnd", input.getLength());
        param.put("pageBegin", input.getStart());

        // 先获取总记录数，再获取分页数据
        int total = iCptFeeOrderQueryDao.countTotal(param);
        List<FeeOrderQueryDO> list = iCptFeeOrderQueryDao.findByQueryCondition(param);

        // 设置DataTablesOutput的属性
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setRecordsFiltered(total);

        // 设置数据
        if (list == null) {
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(list);
        }

        return dataTablesOutput;
    }

    @Override
    public Object get(String orderNo) {
        return iCptFeeOrderQueryDao.getByOrderNo(orderNo);
    }

    @Override
    public String add(FeeOrderQueryDO feeOrderQueryDO) {
        // 设置创建时间和修改时间
        Date currentDate = new Date();
        feeOrderQueryDO.setCreateTime(currentDate);
        feeOrderQueryDO.setModifyTime(currentDate);

        int result = iCptFeeOrderQueryDao.insert(feeOrderQueryDO);
        if (result == 1) {
            return "CPT00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String update(FeeOrderQueryDO feeOrderQueryDO) {
        // 设置修改时间
        feeOrderQueryDO.setModifyTime(new Date());

        int result = iCptFeeOrderQueryDao.update(feeOrderQueryDO);
        if (result == 1) {
            return "CPT00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String delete(String orderNo) {
        int result = iCptFeeOrderQueryDao.delete(orderNo);
        return result + "";
    }
}