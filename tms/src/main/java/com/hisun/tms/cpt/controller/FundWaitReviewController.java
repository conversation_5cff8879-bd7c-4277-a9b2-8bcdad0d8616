package com.hisun.tms.cpt.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

/**
 * 待复核充值订单基本操作，包括快捷订单、网银(微信、支付宝、翼支付)订单、汇款订单
 */
@Controller
@RequestMapping("/cpt/order/fundWaitReview")
public class FundWaitReviewController {
    private static final Logger logger = LoggerFactory.getLogger(FundWaitReviewController.class);

    /**
     * 返回退款订单查询功能主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/order/waitReviewFund') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/order/fundWaitReview/fund");
        return modelAndView;
    }
}
