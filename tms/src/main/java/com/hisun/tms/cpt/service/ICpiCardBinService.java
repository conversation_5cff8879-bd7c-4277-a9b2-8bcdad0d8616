package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.model.CardBinDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnBusiDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * 资金流入模块：卡bin信息管理
 */
public interface ICpiCardBinService {
    /**
     * 增加卡bin信息
     */
    Map<String, String> add(CardBinDO cardBinDO);

    /**
     * 修改卡bin信息
     */
    Map<String, String> modify(CardBinDO cardBinDO);

    /**
     * 删除卡bin信息
     */
    Map<String, String> delete(String binId);

    /**
     * 查询所有的卡bin信息
     */
    DataTablesOutput<CardBinDO> findAll(GenericParamInput input);
}
