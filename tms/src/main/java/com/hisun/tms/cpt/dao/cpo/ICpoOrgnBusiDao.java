package com.hisun.tms.cpt.dao.cpo;

import com.hisun.tms.cpt.model.OrgnBusiDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资金流出模块，合作机构业务管理
 */
@Mapper
public interface ICpoOrgnBusiDao {
    /**
     * 新增合作资金机构业务信息
     */
    int insert(OrgnBusiDO orgnBusiDO);

    /**
     * 修改合作资金机构业务信息
     */
    int update(OrgnBusiDO orgnBusiDO);

    /**
     * 资金流出模块，查询合作机构业务信息列表
     * @param corpOrgId 合作机构编号
     * @param corpBusTyp 业务类型
     * @param corpBusSubTyp 业务子类型
     * @param busEffFlg 业务生效标志
     * @param pageBegin 分页开始
     * @param pageNum 每页最大数目
     * @return
     */
    List<OrgnBusiDO> getOrgnBusiList(@Param("corpOrgId") String corpOrgId,
                                     @Param("corpBusTyp") String corpBusTyp,
                                     @Param("corpBusSubTyp") String corpBusSubTyp,
                                     @Param("busEffFlg") String busEffFlg,
                                     @Param("pageBegin") Integer pageBegin,
                                     @Param("pageNum") Integer pageNum);

    /**
     * 资金流出模块，查询合作机构业务信息列表总笔数
     */
    int getOrgnBusiListTotNum(@Param("corpOrgId") String corpOrgId,
                              @Param("corpBusTyp") String corpBusTyp,
                              @Param("corpBusSubTyp") String corpBusSubTyp,
                              @Param("busEffFlg") String busEffFlg);

    /**
     * 查询合作机构业务信息
     * @param corpOrgId 合作机构编号
     * @param corpBusTyp 业务类型
     * @param corpBusSubTyp 业务子类型
     */
    OrgnBusiDO getOrgnBusiDO(@Param("corpOrgId") String corpOrgId,
                             @Param("corpBusTyp") String corpBusTyp,
                             @Param("corpBusSubTyp") String corpBusSubTyp,
                             @Param("busEffFlg") String busEffFlg);

}
