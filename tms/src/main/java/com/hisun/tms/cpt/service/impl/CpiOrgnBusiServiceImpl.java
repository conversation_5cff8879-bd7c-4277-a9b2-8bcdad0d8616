package com.hisun.tms.cpt.service.impl;

import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.common.CptConstants;
import com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao;
import com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnBusiDO;
import com.hisun.tms.cpt.model.OrgnInfoDO;
import com.hisun.tms.cpt.service.ICpiOrgnBusiService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资金流出模块：资金机构业务管理
 */
@Service("cpiOrgnBusiService")
public class CpiOrgnBusiServiceImpl implements ICpiOrgnBusiService {
    private static final Logger logger = LoggerFactory.getLogger(CpiOrgnBusiServiceImpl.class);

    @Resource
    private ICpiOrgnInfoDao cpiOrgnInfoDao;

    @Resource
    private ICpiOrgnBusiDao cpiOrgnBusiDao;

    @Override
    public Map<String, String> add(OrgnBusiDO orgnBusiDO) {
        Map<String, String> resultMap = new HashMap<>();
        String corpOrgId = orgnBusiDO.getCorpOrgId();
        String corpBusTyp = orgnBusiDO.getCorpBusTyp();
        String corpBusSubTyp = orgnBusiDO.getCorpBusSubTyp();

        //判断合作机构信息是否存在
        OrgnInfoDO orgnInfoDO1 = cpiOrgnInfoDao.getOrgnInfoByOrgId(corpOrgId);
        if(JudgeUtils.isNull(orgnInfoDO1)) {
            //合作机构信息不存在
            resultMap.put("msgCd", CptConstants.ORG_INF_NOT_EXISTS);
            return resultMap;
        }

        //判断合作机构业务是否存在
        OrgnBusiDO orgnBusiDO1 = cpiOrgnBusiDao.getOrgnBusiDO(corpOrgId, corpBusTyp, corpBusSubTyp, null);
        if(JudgeUtils.isNotNull(orgnBusiDO1)) {
            //合作机构业务已存在
            resultMap.put("msgCd", CptConstants.ORG_BUSI_EXISTS);
            return resultMap;
        }

        //插入数据，CPO00000-成功
        String orgBusId = DateTimeUtils.formatLocalDateTime(LocalDateTime.now(), "yyyyMMddHHmmssSSS");
        orgnBusiDO.setOrgBusId(orgBusId);
        try {
            int num = cpiOrgnBusiDao.insert(orgnBusiDO);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
            logger.error("增加合作机构业务失败，异常为{}", e);

        }
        return resultMap;
    }

    @Override
    public Map<String, String> modify(OrgnBusiDO orgnBusiDO) {
        //修改数据，CPO00000-成功
        Map<String, String> resultMap = new HashMap<>();

        //判断合作机构信息是否存在
        String corpOrgId = orgnBusiDO.getCorpOrgId();
        OrgnInfoDO orgnInfoDO1 = cpiOrgnInfoDao.getOrgnInfoByOrgId(corpOrgId);
        if(JudgeUtils.isNull(orgnInfoDO1)) {
            //合作机构信息不存在
            resultMap.put("msgCd", CptConstants.ORG_INF_NOT_EXISTS);
            return resultMap;
        }

        //更新合作机构业务
        try {
            int num = cpiOrgnBusiDao.update(orgnBusiDO);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
            logger.error("修改合作机构业务失败，异常为{}", e);
        }
        return resultMap;
    }

    @Override
    public DataTablesOutput<OrgnBusiDO> findAll(GenericParamInput input) {
        DataTablesOutput<OrgnBusiDO> dataTablesOutput = new DataTablesOutput();
        List<OrgnBusiDO> orgnBusiDOList = new ArrayList<>();

        //查询条件
        String corpOrgId = input.getExtra_search().get("corpOrgId");
        String corpBusTyp = input.getExtra_search().get("corpBusTyp");
        String corpBusSubTyp = input.getExtra_search().get("corpBusSubTyp");
        String busEffFlg = input.getExtra_search().get("busEffFlg");

        //从第几条记录开始获取数据，以及每页最大笔数
        Integer pageBegin = input.getStart();
        Integer pageNum = input.getLength();

        //根据条件查询得到记录的总笔数
        int totNum = 0;
        try {
            //分页查询订单列表
            orgnBusiDOList = cpiOrgnBusiDao.getOrgnBusiList(corpOrgId, corpBusTyp, corpBusSubTyp, busEffFlg, pageBegin, pageNum);

            //查询满足条件的订单总笔数
            totNum = cpiOrgnBusiDao.getOrgnBusiListTotNum(corpOrgId, corpBusTyp, corpBusSubTyp, busEffFlg);
        } catch (Exception e){
            logger.error("资金流出合作机构业务查询列表失败，异常为{}", e);
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(orgnBusiDOList)){
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(orgnBusiDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }

}
