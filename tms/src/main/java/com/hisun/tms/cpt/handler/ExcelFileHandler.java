package com.hisun.tms.cpt.handler;

import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.model.FileConfigDO;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 生成excel文件，后缀为 .xls 或 .xlsx
 */
@Component
public class ExcelFileHandler {
    /**
     * 生成后缀为.xls的excel文件
     */
    public void createXlsExcel(FileConfigDO fileConfigDO, String fullFileName) throws Exception {
        //获取文件列标题名称
        String[] titleNames = fileConfigDO.getTitleNames();
        //获取文件列对应的字段名
        String[] columnNames = fileConfigDO.getColumnNames();
        //获取文件列对应字段的get方法名
        String[] getMethodNames = fileConfigDO.getMethodNames();
        //获取需写入文件的数据
        List<Object> objectList = fileConfigDO.getObjectList();
        //objectList中的每个元素实际需映射到的 DO类
        String objectClassStr = fileConfigDO.getObjectClass();

        //第一步，初始化DO类
        Class dataClass = Class.forName(objectClassStr);
        Object dataObject = null;

        //第二步，创建可写入的excel工作簿，创建输出流
        OutputStream outputStream = new FileOutputStream(fullFileName);
        WritableWorkbook writableWorkbook = Workbook.createWorkbook(outputStream);
        //WritableWorkbook writableWorkbook = Workbook.createWorkbook(new File(fullFileName));

        //第三步，创建工作表
        WritableSheet writableSheet = writableWorkbook.createSheet("example", 0);

        //第四步，工作表第一行添加标题单元格
        for(int i = 0; i < titleNames.length; i++) {
            Label cellLabel = new Label(i, 0, titleNames[i]);
            writableSheet.addCell(cellLabel);
        }

        //第五步，工作表第二行开始添加列表数据
        Label cellLabel = null;
        Field field = null;
        for(int row = 0; row < objectList.size(); row++) {
            dataObject = objectList.get(row);
            for(int i = 0; i < columnNames.length; i++) {
                field = dataObject.getClass().getDeclaredField(columnNames[i]);
                field.setAccessible(true);
                cellLabel = new Label(i, row + 1, getFieldValueByType(field, dataObject));
                writableSheet.addCell(cellLabel);
            }
        }

        //创建文件，关闭输出流
        writableWorkbook.write();
        writableWorkbook.close();
        outputStream.close();
    }

    /**
     * 生成后缀为.xlsx的excel文件
     */
    public void createXlsxExcel(FileConfigDO fileConfigDO, String fullFileName) {

    }

    /**
     * 根据实例的属性名和类型，获取格式化后的值
     */
    public String getFieldValueByType(Field field, Object dataObject) throws Exception {
        Object columnValue = field.get(dataObject);
        String fieldType = field.getType().toString();
        String result = null;
        if(fieldType.endsWith("String")) {
            result = JudgeUtils.isNotNull(columnValue) ? (String)columnValue : "";

        } else if(fieldType.endsWith("LocalDate")) {
            result = JudgeUtils.isNotNull(columnValue) ? DateTimeUtils.formatLocalDate((LocalDate)columnValue) : "";

        } else if(fieldType.endsWith("LocalTime")) {
            result = JudgeUtils.isNotNull(columnValue) ? DateTimeUtils.formatLocalTime((LocalTime)columnValue) : "";

        } else if(fieldType.endsWith("BigDecimal")) {
            result = JudgeUtils.isNotNull(columnValue) ? ((BigDecimal)columnValue).toString() : "";

        } else if(fieldType.endsWith("Integer")) {
            result = JudgeUtils.isNotNull(columnValue) ? String.valueOf((Integer)columnValue) : "";
        }
        System.out.println("field.name--->" + field.getName() + "  field.type--->" + field.getType().toString() + "  result--->" + result);
        return result;
    }

}
