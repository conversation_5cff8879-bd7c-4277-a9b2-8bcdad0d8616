package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnBusiDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * 资金流出模块：合作机构业务管理
 */
public interface ICpoOrgnBusiService {
    /**
     * 增加合作机构业务信息
     */
    Map<String, String> add(OrgnBusiDO orgnBusiDO);

    /**
     * 修改合作机构业务信息
     */
    Map<String, String> modify(OrgnBusiDO orgnBusiDO);

    /**
     * 查询所有的合作资金机构业务信息
     */
    DataTablesOutput<OrgnBusiDO> findAll(GenericParamInput input);
}
