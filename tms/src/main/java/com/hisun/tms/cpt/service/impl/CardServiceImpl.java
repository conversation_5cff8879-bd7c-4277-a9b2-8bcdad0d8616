package com.hisun.tms.cpt.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.client.FastpayClient;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.dto.CardUnBindReqDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.dao.cpi.ICardProtDao;
import com.hisun.tms.cpt.model.CardProtDO;
import com.hisun.tms.cpt.model.CardProtJrnDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.ICardService;
import com.hisun.tms.util.EncryptUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

/**
 * 用户银行卡信息管理
 */
@Service("cardService")
public class CardServiceImpl implements ICardService {

    private static final Logger logger = LoggerFactory.getLogger(CardServiceImpl.class);

    @Resource
    private ICardProtDao cardProtDao;

    @Resource
    private FastpayClient fastpayClient;

    @Resource
    private EncryptUtils encryptUtils;

    /**
     * 用户已绑定的银行卡信息查询
     * @param input
     * @return
     */
    @Override
    public DataTablesOutput<CardProtDO> findCardProtList(GenericParamInput input) {
        DataTablesOutput<CardProtDO> dataTablesOutput = new DataTablesOutput();
        List<CardProtDO> refundOrderDOList = new ArrayList<>() ;

        String mblNo = input.getExtra_search().get("mblNo");
        String userId = input.getExtra_search().get("userId");
        String crdAcTyp = input.getExtra_search().get("crdAcTyp");
        String agrEffFlg = input.getExtra_search().get("agrEffFlg");
        String corpBusSubTyp = input.getExtra_search().get("corpBusSubTyp");
        String bnkPsnFlg = input.getExtra_search().get("bnkPsnFlg");
        String beginDateStr = input.getExtra_search().get("beginDate");
        String endDateStr = input.getExtra_search().get("endDate");

        //从第几条记录开始获取数据
        Integer pageBegin = input.getStart();
        //获取多少条记录
        Integer pageNum = input.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;
        //订单开始日期
        LocalDate beginDate = null;
        //订单结束日期
        LocalDate endDate = null;
        try {
            if (JudgeUtils.isNotBlank(beginDateStr)){
                beginDateStr = beginDateStr.replaceAll("-","").trim();
                beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
            }
            if (JudgeUtils.isNotBlank(endDateStr)){
                endDateStr = endDateStr.replaceAll("-","").trim();
                endDate = DateTimeUtils.parseLocalDate(endDateStr);
            }

            //分页查询订单列表
            refundOrderDOList = cardProtDao.getCardProtList(mblNo, userId, crdAcTyp, agrEffFlg, bnkPsnFlg, beginDate, endDate, pageBegin, pageNum);

            //查询满足条件的订单总笔数
            totNum = cardProtDao.getCardProtListTotNum(mblNo, userId, crdAcTyp, agrEffFlg, bnkPsnFlg, beginDate, endDate, pageBegin, pageNum);
        } catch (Exception e){
            logger.error("用户已绑定的银行卡信息查询列表失败，异常为{}", e);
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(refundOrderDOList)) {
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(refundOrderDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }

    @Override
    public CardProtDO getCardProtInfo(String agrNo) {
        CardProtDO cardProtDO = cardProtDao.getCardProtInfo(agrNo);

        //获取密文
        String crdNoEnc = cardProtDO.getCrdNo();

        //获取明文
        String crdNo = "";
        try {
            crdNo = encryptUtils.encrypt(crdNoEnc, "decrypt");
            cardProtDO.setCrdNo("**** **** **** " + crdNo.substring(crdNo.length() - 4));
        } catch (LemonException e) {
            logger.error("用户已绑定的银行卡解密失败，异常为{}", e);
        }
        return cardProtDO;
    }

    /**
     * 用户解除绑定的银行卡
     * 0-我方解绑，不调用银行接口解绑，平台更新用户签约信息失效
     * 1-双方解绑，调用银行接口解绑，更新用户签约信息失效
     */
    @Override
    public Map<String, String> unbindCard(String agrNo, String agrDirect) {
        String msgCd = null;
        if(StringUtils.equals(agrDirect, "0")) {
            //双向协议，调用银行接口解除银行卡签约
            msgCd = bothUnbind(agrNo);

        } else if(StringUtils.equals(agrDirect, "1")) {
            //我方单侧协议，不调用银行接口解除银行卡签约
            msgCd = platUnbind(agrNo);
        }
        Map<String, String> resultMap = new HashMap<String, String>();
        resultMap.put("msgCd", msgCd);
        return resultMap;
    }

    //双向协议
    private String bothUnbind(String agrNo) {
        CardUnBindReqDTO cardUnBindReqDTO = new CardUnBindReqDTO();
        cardUnBindReqDTO.setAgrNo(agrNo);
        //UI调用此接口则不需要检查userId，用户登录mapy解绑需要检查userId是否为空
        cardUnBindReqDTO.setChkUserId(CpiConstants.CHK_USER_ID_NO);
        GenericDTO<CardUnBindReqDTO> genericDTO = new GenericDTO<>();
        genericDTO.setBody(cardUnBindReqDTO);
        GenericRspDTO genericRspDTO = fastpayClient.unBindCard(genericDTO);
        return genericRspDTO.getMsgCd();
    }

    //我方单侧协议
    private String platUnbind(String agrNo) {
        CardProtDO cardProtDO = cardProtDao.getCardProtInfo(agrNo);
        LocalDate unsignDt = LocalDate.now();
        LocalTime unsignTm = LocalTime.now();

        //插入流水表
        String jrnNo = "TMS"+new Date().getTime() + new Random().nextInt(100);//IdGenUtils.generateIdWithDateTime("JRN_NO", "TMS", 6);
        CardProtJrnDO unbindCardJrnDo = new CardProtJrnDO();
        BeanUtils.copyProperties(unbindCardJrnDo, cardProtDO);
        unbindCardJrnDo.setUserId(cardProtDO.getUserId());
        unbindCardJrnDo.setBndFlg(CpiConstants.SIGN_CANCEL);//解绑
        unbindCardJrnDo.setCorpBusTyp(CorpBusTyp.SIGN.getType());
        unbindCardJrnDo.setCorpBusSubTyp(CorpBusSubTyp.FAST_UNSIGN.getType());
        unbindCardJrnDo.setTxDt(unsignDt);
        unbindCardJrnDo.setTxTm(unsignTm);
        unbindCardJrnDo.setJrnNo(jrnNo);
        cardProtDao.insertCardProtJrn(unbindCardJrnDo);

        //用户银行卡协议状态改为失效
        cardProtDao.updateCardProtDO(agrNo, unsignDt, unsignTm);
        return "CPI00000";
    }

}
