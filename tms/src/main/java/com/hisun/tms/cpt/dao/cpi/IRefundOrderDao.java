package com.hisun.tms.cpt.dao.cpi;

import com.hisun.tms.cpt.model.RefundOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.List;

/**
 * 运营系统-资金出入模块-退款订单查询功能，数据库操作
 */
@Mapper
public interface IRefundOrderDao {
    /**
     * @param userId 用户号/商户号
     * @param fndOrdNo 充值订单号
     * @param rfdOrdNo 退款订单号
     * @param corpBusSubTyp 退款类型
     * @param rutCorpOrg 退款银行
     * @param ordSts 订单状态
     * @param orgJrnNo 银行流水号
     * @param beginDate 退款提交开始日期
     * @param endDate 退款提交截止日期
     * @param pageBegin 分页从第几条开始
     * @param pageNum 取多少条数据
     * @return
     * 根据条件，查询退款订单列表
     */
    List<RefundOrderDO> getRefundOrderList(@Param("userId") String userId,
                                           @Param("fndOrdNo") String fndOrdNo,
                                           @Param("rfdOrdNo") String rfdOrdNo,
                                           @Param("corpBusSubTyp") String corpBusSubTyp,
                                           @Param("rutCorpOrg") String rutCorpOrg,
                                           @Param("ordSts") String ordSts,
                                           @Param("orgJrnNo") String orgJrnNo,
                                           @Param("beginDate") LocalDate beginDate,
                                           @Param("endDate") LocalDate endDate,
                                           @Param("pageBegin") Integer pageBegin,
                                           @Param("pageNum") Integer pageNum);

    /**
     * @param userId 用户号/商户号
     * @param fndOrdNo 充值订单号
     * @param rfdOrdNo 退款订单号
     * @param corpBusSubTyp 退款类型
     * @param rutCorpOrg 退款银行
     * @param ordSts 订单状态
     * @param orgJrnNo 银行流水号
     * @param beginDate 退款提交开始日期
     * @param endDate 退款提交截止日期
     * @param pageBegin 分页从第几条开始
     * @param pageNum 取多少条数据
     * @return
     * 根据条件，查询退款订单总笔数
     */
    int getRefundOrderListTotNum(@Param("userId") String userId,
                                 @Param("fndOrdNo") String fndOrdNo,
                                 @Param("rfdOrdNo") String rfdOrdNo,
                                 @Param("corpBusSubTyp") String corpBusSubTyp,
                                 @Param("rutCorpOrg") String rutCorpOrg,
                                 @Param("ordSts") String ordSts,
                                 @Param("orgJrnNo") String orgJrnNo,
                                 @Param("beginDate") LocalDate beginDate,
                                 @Param("endDate") LocalDate endDate,
                                 @Param("pageBegin") Integer pageBegin,
                                 @Param("pageNum") Integer pageNum);
}
