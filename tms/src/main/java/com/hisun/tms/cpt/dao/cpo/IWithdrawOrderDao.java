package com.hisun.tms.cpt.dao.cpo;

import com.hisun.tms.cpt.model.WithdrawOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 运营系统-资金出入模块-付款订单查询功能，数据库操作dao
 */
@Mapper
public interface IWithdrawOrderDao {
    /**
     * @param reqOrdNo 请求订单号
     * @param wcOrdNo 付款内部订单号
     * @param rutCorgJrn 银行流水号
     * @param rutCorg 付款银行
     * @param corpBusSubTyp 付款类型，即业务子类型
     * @param ordSts 订单状态
     * @param userId 用户号/商户号
     * @param beginDt 订单提交开始日期
     * @param endDt 订单提交结束日期
     * @param agrPayDt 付款协议日期
     * @param pageBegin 获取记录从第几条开始
     * @param pageEnd  获取记录直到第几条结束
     * @return
     * 根据条件，查询付款订单列表，分页
     */
    List<WithdrawOrderDO> getWithdrawOrderList(@Param("reqOrdNo") String reqOrdNo,
                                               @Param("wcOrdNo") String wcOrdNo,
                                               @Param("rutCorgJrn") String rutCorgJrn,
                                               @Param("rutCorg") String rutCorg,
                                               @Param("corpBusSubTyp") String corpBusSubTyp,
                                               @Param("ordSts") String ordSts,
                                               @Param("userId") String userId,
                                               @Param("beginDt") LocalDate beginDt,
                                               @Param("endDt") LocalDate endDt,
                                               @Param("agrPayDt") LocalDate agrPayDt,
                                               @Param("pageBegin") Integer pageBegin,
                                               @Param("pageEnd") Integer pageEnd);

    List<WithdrawOrderDO> getDCWithdrawOrderList(@Param("reqOrdNo") String reqOrdNo,
                                               @Param("wcOrdNo") String wcOrdNo,
                                               @Param("rutCorgJrn") String rutCorgJrn,
                                               @Param("rutCorg") String rutCorg,
                                               @Param("corpBusSubTyp") String corpBusSubTyp,
                                               @Param("ordSts") String ordSts,
                                               @Param("userId") String userId,
                                               @Param("beginDt") LocalDate beginDt,
                                               @Param("endDt") LocalDate endDt,
                                               @Param("agrPayDt") LocalDate agrPayDt,
                                               @Param("pageBegin") Integer pageBegin,
                                               @Param("pageEnd") Integer pageEnd);

    /**
     * @param reqOrdNo 请求订单号
     * @param wcOrdNo 付款内部订单号
     * @param rutCorgJrn 银行流水号
     * @param rutCorg 付款银行
     * @param corpBusSubTyp 付款类型，即业务子类型
     * @param ordSts 订单状态
     * @param userId 用户号/商户号
     * @param beginDt 订单提交开始日期
     * @param endDt 订单提交结束日期
     * @param agrPayDt 付款协议日期
     * @param pageBegin 获取记录从第几条开始
     * @param pageEnd  获取记录直到第几条结束
     * @return
     * 根据条件，查询付款订单列表，分页
     */
    List<WithdrawOrderDO> getWithdrawOrderListOnlyWaitReview(@Param("reqOrdNo") String reqOrdNo,
                                               @Param("wcOrdNo") String wcOrdNo,
                                               @Param("rutCorgJrn") String rutCorgJrn,
                                               @Param("rutCorg") String rutCorg,
                                               @Param("corpBusSubTyp") String corpBusSubTyp,
                                               @Param("ordSts") String ordSts,
                                               @Param("userId") String userId,
                                               @Param("beginDt") LocalDate beginDt,
                                               @Param("endDt") LocalDate endDt,
                                               @Param("agrPayDt") LocalDate agrPayDt,
                                               @Param("pageBegin") Integer pageBegin,
                                               @Param("pageEnd") Integer pageEnd);

    List<WithdrawOrderDO> getDCWithdrawOrderListOnlyWaitReview(@Param("reqOrdNo") String reqOrdNo,
                                                             @Param("wcOrdNo") String wcOrdNo,
                                                             @Param("rutCorgJrn") String rutCorgJrn,
                                                             @Param("rutCorg") String rutCorg,
                                                             @Param("corpBusSubTyp") String corpBusSubTyp,
                                                             @Param("ordSts") String ordSts,
                                                             @Param("userId") String userId,
                                                             @Param("beginDt") LocalDate beginDt,
                                                             @Param("endDt") LocalDate endDt,
                                                             @Param("agrPayDt") LocalDate agrPayDt,
                                                             @Param("pageBegin") Integer pageBegin,
                                                             @Param("pageEnd") Integer pageEnd);


    /**
     * @param reqOrdNo 请求订单号
     * @param wcOrdNo 付款内部订单号
     * @param rutCorgJrn 银行流水号
     * @param rutCorg 付款银行
     * @param corpBusSubTyp 付款类型，即业务子类型
     * @param ordSts 订单状态
     * @param userId 用户号/商户号
     * @param beginDt 订单提交开始日期
     * @param endDt 订单提交结束日期
     * @param agrPayDt 付款协议日期
     * @param pageBegin 获取记录从第几条开始
     * @param pageEnd  获取记录直到第几条结束
     * @return
     * 根据条件，查询付款订单总笔数
     */
    int getWithdrawOrderListTotNum(@Param("reqOrdNo") String reqOrdNo,
                                   @Param("wcOrdNo") String wcOrdNo,
                                   @Param("rutCorgJrn") String rutCorgJrn,
                                   @Param("rutCorg") String rutCorg,
                                   @Param("corpBusSubTyp") String corpBusSubTyp,
                                   @Param("ordSts") String ordSts,
                                   @Param("userId") String userId,
                                   @Param("beginDt") LocalDate beginDt,
                                   @Param("endDt") LocalDate endDt,
                                   @Param("agrPayDt") LocalDate agrPayDt,
                                   @Param("pageBegin") Integer pageBegin,
                                   @Param("pageEnd") Integer pageEnd);

    int getDCWithdrawOrderListTotNum(@Param("reqOrdNo") String reqOrdNo,
                                   @Param("wcOrdNo") String wcOrdNo,
                                   @Param("rutCorgJrn") String rutCorgJrn,
                                   @Param("rutCorg") String rutCorg,
                                   @Param("corpBusSubTyp") String corpBusSubTyp,
                                   @Param("ordSts") String ordSts,
                                   @Param("userId") String userId,
                                   @Param("beginDt") LocalDate beginDt,
                                   @Param("endDt") LocalDate endDt,
                                   @Param("agrPayDt") LocalDate agrPayDt,
                                   @Param("pageBegin") Integer pageBegin,
                                   @Param("pageEnd") Integer pageEnd);

 /**
     * @param reqOrdNo 请求订单号
     * @param wcOrdNo 付款内部订单号
     * @param rutCorgJrn 银行流水号
     * @param rutCorg 付款银行
     * @param corpBusSubTyp 付款类型，即业务子类型
     * @param ordSts 订单状态
     * @param userId 用户号/商户号
     * @param beginDt 订单提交开始日期
     * @param endDt 订单提交结束日期
     * @param agrPayDt 付款协议日期
     * @param pageBegin 获取记录从第几条开始
     * @param pageEnd  获取记录直到第几条结束
     * @return
     * 根据条件，查询付款订单总笔数
     */
    int getWithdrawOrderListTotNumOnlyWaitReview(@Param("reqOrdNo") String reqOrdNo,
                                   @Param("wcOrdNo") String wcOrdNo,
                                   @Param("rutCorgJrn") String rutCorgJrn,
                                   @Param("rutCorg") String rutCorg,
                                   @Param("corpBusSubTyp") String corpBusSubTyp,
                                   @Param("ordSts") String ordSts,
                                   @Param("userId") String userId,
                                   @Param("beginDt") LocalDate beginDt,
                                   @Param("endDt") LocalDate endDt,
                                   @Param("agrPayDt") LocalDate agrPayDt,
                                   @Param("pageBegin") Integer pageBegin,
                                   @Param("pageEnd") Integer pageEnd);

    int getDCWithdrawOrderListTotNumOnlyWaitReview(@Param("reqOrdNo") String reqOrdNo,
                                                 @Param("wcOrdNo") String wcOrdNo,
                                                 @Param("rutCorgJrn") String rutCorgJrn,
                                                 @Param("rutCorg") String rutCorg,
                                                 @Param("corpBusSubTyp") String corpBusSubTyp,
                                                 @Param("ordSts") String ordSts,
                                                 @Param("userId") String userId,
                                                 @Param("beginDt") LocalDate beginDt,
                                                 @Param("endDt") LocalDate endDt,
                                                 @Param("agrPayDt") LocalDate agrPayDt,
                                                 @Param("pageBegin") Integer pageBegin,
                                                 @Param("pageEnd") Integer pageEnd);

    /**
     * 根据付款内部订单号，查询付款订单详细信息
     * @param wcOrdNo 付款内部订单号
     * @return
     */
    WithdrawOrderDO getWithdrawOrder(@Param("wcOrdNo") String wcOrdNo);

    /**
     * @param reqOrdNo 请求订单号
     * @param wcOrdNo 付款内部订单号
     * @param rutCorgJrn 银行流水号
     * @param rutCorg 付款银行
     * @param corpBusSubTyp 付款类型，即业务子类型
     * @param ordSts 订单状态
     * @param userId 用户号/商户号
     * @param beginDt 订单提交开始日期
     * @param endDt 订单提交结束日期
     * @param agrPayDt 付款协议日期
     * @return
     * 根据条件，查询付款订单列表，不分页
     */
    List<WithdrawOrderDO> getWithdrawOrderListNew(@Param("reqOrdNo") String reqOrdNo,
                                               @Param("wcOrdNo") String wcOrdNo,
                                               @Param("rutCorgJrn") String rutCorgJrn,
                                               @Param("rutCorg") String rutCorg,
                                               @Param("corpBusSubTyp") String corpBusSubTyp,
                                               @Param("ordSts") String ordSts,
                                               @Param("userId") String userId,
                                               @Param("beginDt") LocalDate beginDt,
                                               @Param("endDt") LocalDate endDt,
                                               @Param("agrPayDt") LocalDate agrPayDt);

}
