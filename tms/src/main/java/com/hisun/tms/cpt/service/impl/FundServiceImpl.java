package com.hisun.tms.cpt.service.impl;

import com.hisun.lemon.cpi.client.RemittanceClient;
import com.hisun.lemon.cpi.dto.RemittanceConfirmDTO;
import com.hisun.lemon.framework.data.DataHelper;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.dao.cpi.IFundOrderDao;
import com.hisun.tms.cpt.model.FundOrderDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.RemitOrderDO;
import com.hisun.tms.cpt.service.IFundService;
import com.hisun.tms.csh.model.CshOderDo;
import com.hisun.tms.csh.repository.CshOrderRepository;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运营系统-资金出入模块-退款订单查询功能
 */
@Service("fundService")
public class FundServiceImpl implements IFundService {

    private static final Logger logger = LoggerFactory.getLogger(FundServiceImpl.class);

    @Resource
    private IFundOrderDao fundOrderDao;

    @Resource
    private RemittanceClient remittanceClient;

    @Resource
    private CshOrderRepository cshOrderRepository ;

    /**
     * 根据条件，查询充值订单列表
     */
    @Override
    public DataTablesOutput<FundOrderDO> findAll(GenericParamInput input) {
        DataTablesOutput<FundOrderDO> dataTablesOutput = new DataTablesOutput();
        List<FundOrderDO> refundOrderDOList = new ArrayList<>() ;

        String mblNo = input.getExtra_search().get("mblNo");
        String reqOrdNo = input.getExtra_search().get("reqOrdNo");
        String fudOrdNo = input.getExtra_search().get("fudOrdNo");
        String corpBusTyp = input.getExtra_search().get("corpBusTyp");
        String corpBusSubTyp = input.getExtra_search().get("corpBusSubTyp");
        String rutCorpOrg = input.getExtra_search().get("rutCorpOrg");
        String crdCorpOrg = input.getExtra_search().get("crdCorpOrg");
        String ordSts = input.getExtra_search().get("ordSts");
        String beginDateStr = input.getExtra_search().get("beginDate");
        String endDateStr = input.getExtra_search().get("endDate");
        // 复核标识
        String reviewFlag = input.getExtra_search().get("reviewFlag");

        //从第几条记录开始获取数据
        Integer pageBegin = input.getStart();
        //获取多少条记录
        Integer pageNum = input.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;
        //订单开始日期
        LocalDate beginDate = null;
        //订单结束日期
        LocalDate endDate = null;
        try {
            if (JudgeUtils.isNotBlank(beginDateStr)){
                beginDateStr = beginDateStr.replaceAll("-","").trim();
                beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
            }
            if (JudgeUtils.isNotBlank(endDateStr)){
                endDateStr = endDateStr.replaceAll("-","").trim();
                endDate = DateTimeUtils.parseLocalDate(endDateStr);
            }

            if ("0".equals(reviewFlag)) {
                //分页查询订单列表
                refundOrderDOList = fundOrderDao.getFundOrderListOnlyWaitReview(mblNo, reqOrdNo, fudOrdNo, corpBusTyp, corpBusSubTyp,
                        rutCorpOrg, crdCorpOrg, ordSts, beginDate, endDate, pageBegin, pageNum);

                //查询满足条件的订单总笔数
                totNum = fundOrderDao.getFundOrderListTotNumOnlyWaitReview(mblNo, reqOrdNo, fudOrdNo, corpBusTyp, corpBusSubTyp,
                        rutCorpOrg, crdCorpOrg, ordSts, beginDate, endDate, pageBegin, pageNum);
            } else {
                //分页查询订单列表
                refundOrderDOList = fundOrderDao.getFundOrderList(mblNo, reqOrdNo, fudOrdNo, corpBusTyp, corpBusSubTyp,
                        rutCorpOrg, crdCorpOrg, ordSts, beginDate, endDate, pageBegin, pageNum);

                //查询满足条件的订单总笔数
                totNum = fundOrderDao.getFundOrderListTotNum(mblNo, reqOrdNo, fudOrdNo, corpBusTyp, corpBusSubTyp,
                        rutCorpOrg, crdCorpOrg, ordSts, beginDate, endDate, pageBegin, pageNum);
            }
        } catch (Exception e){
            logger.error("充值订单查询列表失败，异常为{}", e);
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(refundOrderDOList)){
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            //通过订单号查询收银的充值手续费
            for (FundOrderDO fundOrderDO : refundOrderDOList){
                CshOderDo cshOderDo = cshOrderRepository.findOne(fundOrderDO.getReqOrdNo());
                if (cshOderDo != null ) {
                    fundOrderDO.setFee(cshOderDo.getFee());
                }
            }
            dataTablesOutput.setData(refundOrderDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }

    /**
     * 根据充值订单号，查询汇款订单详细信息
     */
    @Override
    public RemitOrderDO getRemitOrderDetail(String fndOrdNo) {
        return fundOrderDao.getRemitOrderDetail(fndOrdNo);
    }

    /**
     * 汇款充值确认，或退回
     */
    @Override
    public Map<String, String> remitHandler(String fndOrdNo, String ordSts, String reason) {
        //请求参数
        RemittanceConfirmDTO remittanceConfirmDTO = new RemittanceConfirmDTO();
        remittanceConfirmDTO.setFndOrdNo(fndOrdNo);
        remittanceConfirmDTO.setOrdSts(ordSts);
        remittanceConfirmDTO.setReason(reason);

        //请求报文
        GenericDTO<RemittanceConfirmDTO> genericDTO = new GenericDTO<>();
        genericDTO.setBody(remittanceConfirmDTO);
        DataHelper.setGenericDTOPropertyValue(genericDTO,"source","TMS");
        GenericRspDTO genericRspDTO = remittanceClient.payment(genericDTO);

        //返回码
        Map<String, String> resultMap = new HashMap<>();
        String msgCd = genericRspDTO.getMsgCd();
        resultMap.put("msgCd", msgCd);
        return resultMap;
    }
}
