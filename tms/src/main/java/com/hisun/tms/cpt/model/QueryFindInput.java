package com.hisun.tms.cpt.model;

import java.util.Map;

/**
 * 统一分页查询输入参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
public class QueryFindInput extends GenericParamInput {
    
    // 继承GenericParamInput的所有功能
    // 可以在这里添加特定的查询字段如果需要的话
    
    /**
     * 获取额外查询条件
     * 
     * @return 查询条件Map
     */
    @Override
    public Map<String, String> getExtra_search() {
        return super.getExtra_search();
    }

    /**
     * 设置额外查询条件
     * 
     * @param extra_search 查询条件Map
     */
    @Override
    public void setExtra_search(Map<String, String> extra_search) {
        super.setExtra_search(extra_search);
    }
}