package com.hisun.tms.cpt.service.impl;

import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.common.CptConstants;
import com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao;
import com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao;
import com.hisun.tms.cpt.dao.cpi.ICpiOrgnRouteDao;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnBusiDO;
import com.hisun.tms.cpt.model.OrgnInfoDO;
import com.hisun.tms.cpt.model.OrgnRouteDO;
import com.hisun.tms.cpt.service.ICpiOrgnRouteService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资金流出模块：资金机构业务管理
 */
@Service("cpiOrgnRouteService")
public class CpiOrgnRouteServiceImpl implements ICpiOrgnRouteService {
    private static final Logger logger = LoggerFactory.getLogger(CpiOrgnRouteServiceImpl.class);

    @Resource
    private ICpiOrgnInfoDao cpiOrgnInfoDao;

    @Resource
    private ICpiOrgnBusiDao cpiOrgnBusiDao;

    @Resource
    private ICpiOrgnRouteDao cpiOrgnRouteDao;

    @Override
    public Map<String, String> add(OrgnRouteDO orgnRouteDO) {
        Map<String, String> resultMap = new HashMap<>();
        String corpOrgId = orgnRouteDO.getCrdCorpOrg();
        String corpBusTyp = orgnRouteDO.getCorpBusTyp();
        String corpBusSubTyp = orgnRouteDO.getCorpBusSubTyp();
        String rutCorpOrg = orgnRouteDO.getRutCorpOrg();
        String crdAcTyp = orgnRouteDO.getCrdAcTyp();

        //判断合作机构信息是否存在
        OrgnInfoDO orgnInfoDO1 = cpiOrgnInfoDao.getOrgnInfoByOrgId(corpOrgId);
        if(JudgeUtils.isNull(orgnInfoDO1)) {
            //合作机构信息不存在
            resultMap.put("msgCd", CptConstants.ORG_INF_NOT_EXISTS);
            return resultMap;
        }

        //判断合作机构是否有生效的业务
        OrgnBusiDO orgnBusiDO1 = cpiOrgnBusiDao.getOrgnBusiDO(corpOrgId, corpBusTyp, corpBusSubTyp, CptConstants.EFF_FLG_YES);
        if(JudgeUtils.isNull(orgnBusiDO1)) {
            //合作机构业务不存在
            resultMap.put("msgCd", CptConstants.ORG_BUSI_NOT_EXISTS);
            return resultMap;
        }

        //判断合作机构路由是否存在
        OrgnRouteDO orgnRouteDO1 = cpiOrgnRouteDao.getOrgnRouteDO(corpOrgId, corpBusTyp, corpBusSubTyp, rutCorpOrg, crdAcTyp);
        if(JudgeUtils.isNotNull(orgnRouteDO1)) {
            //资金机构路由已存在
            resultMap.put("msgCd", CptConstants.ORG_ROUTE_EXISTS);
            return resultMap;
        }

        //插入数据，CPO00000-成功
        String rutInfId = DateTimeUtils.formatLocalDateTime(LocalDateTime.now(), "yyyyMMddHHmmssSSS");
        orgnRouteDO.setRutInfId(rutInfId);
        try {
            int num = cpiOrgnRouteDao.insert(orgnRouteDO);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
        }
        return resultMap;
    }

    @Override
    public Map<String, String> modify(OrgnRouteDO orgnRouteDO) {
        Map<String, String> resultMap = new HashMap<>();
        String corpOrgId = orgnRouteDO.getCrdCorpOrg();
        String corpBusTyp = orgnRouteDO.getCorpBusTyp();
        String corpBusSubTyp = orgnRouteDO.getCorpBusSubTyp();

        //判断合作机构信息是否存在
        OrgnInfoDO orgnInfoDO1 = cpiOrgnInfoDao.getOrgnInfoByOrgId(corpOrgId);
        if(JudgeUtils.isNull(orgnInfoDO1)) {
            //合作机构信息不存在
            resultMap.put("msgCd", CptConstants.ORG_INF_NOT_EXISTS);
            return resultMap;
        }

        //判断合作机构是否有生效的业务
        OrgnBusiDO orgnBusiDO1 = cpiOrgnBusiDao.getOrgnBusiDO(corpOrgId, corpBusTyp, corpBusSubTyp, CptConstants.EFF_FLG_YES);
        if(JudgeUtils.isNull(orgnBusiDO1)) {
            //合作机构业务不存在
            resultMap.put("msgCd", CptConstants.ORG_BUSI_NOT_EXISTS);
            return resultMap;
        }

        //修改数据，CPO00000-成功
        try {
            int num = cpiOrgnRouteDao.update(orgnRouteDO);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
        }
        return resultMap;
    }

    @Override
    public DataTablesOutput<OrgnRouteDO> findAll(GenericParamInput input) {
        DataTablesOutput<OrgnRouteDO> dataTablesOutput = new DataTablesOutput();
        List<OrgnRouteDO> orgnRouteDOList = new ArrayList<>();

        //查询条件
        String crdCorpOrg = input.getExtra_search().get("crdCorpOrg");
        String corpBusTyp = input.getExtra_search().get("corpBusTyp");
        String corpBusSubTyp = input.getExtra_search().get("corpBusSubTyp");
        String rutCorpOrg = input.getExtra_search().get("rutCorpOrg");
        String crdAcTyp = input.getExtra_search().get("crdAcTyp");
        String rutEffFlg = input.getExtra_search().get("rutEffFlg");

        //从第几条记录开始获取数据，以及每页最大笔数
        Integer pageBegin = input.getStart();
        Integer pageNum = input.getLength();

        //根据条件查询得到记录的总笔数
        int totNum = 0;
        try {
            //分页查询订单列表
            orgnRouteDOList = cpiOrgnRouteDao.getOrgnBusiList(crdCorpOrg, corpBusTyp, corpBusSubTyp, rutCorpOrg, crdAcTyp, rutEffFlg, pageBegin, pageNum);

            //查询满足条件的订单总笔数
            totNum = cpiOrgnRouteDao.getOrgnRouteListTotNum(crdCorpOrg, corpBusTyp, corpBusSubTyp, rutCorpOrg, crdAcTyp, rutEffFlg);
        } catch (Exception e){
            logger.error("资金流出合作机构业务查询列表失败，异常为{}", e);
        }

        //判断列表查询结果是否为空
        if(CollectionUtils.isEmpty(orgnRouteDOList)) {
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(orgnRouteDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }

}
