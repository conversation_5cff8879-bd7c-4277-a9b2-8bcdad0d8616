package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnRouteDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * 资金流出模块：资金机构路由管理
 */
public interface ICpoOrgnRouteService {
    /**
     * 增加资金机构路由信息
     */
    Map<String, String> add(OrgnRouteDO orgnRouteDO);

    /**
     * 修改资金机构路由信息
     */
    Map<String, String> modify(OrgnRouteDO orgnRouteDO);

    /**
     * 查询所有的资金机构路由信息
     */
    DataTablesOutput<OrgnRouteDO> findAll(GenericParamInput input);
}
