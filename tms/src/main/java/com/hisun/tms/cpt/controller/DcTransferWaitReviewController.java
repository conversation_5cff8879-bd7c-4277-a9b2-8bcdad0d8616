package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.ITransferService;
import com.hisun.tms.csh.entity.OrderDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 数币待复核转账订单基本操作
 */
@Controller
@RequestMapping("/cpt/dcorder/transferWaitReview")
public class DcTransferWaitReviewController {
    private static final Logger logger = LoggerFactory.getLogger(DcTransferWaitReviewController.class);

    @Resource
    private ITransferService transferService;

    /**
     * 返回数币转账订单查询功能主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/transferWaitReview') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/dcorder/transferWaitReview/transfer");
        return modelAndView;
    }

    /**
     * 数币转账订单列表查询
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/transferWaitReview') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<OrderDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return transferService.findAll(input);
    }

    /**
     * 根据数币转账订单号，查询转账订单详细信息
     */
    @PostMapping(value = "/findOrderInfo")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/transferWaitReview') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public OrderDO findOrderInfo(@RequestParam String orderNo) {
        logger.debug("tms DcTransferWaitReviewController.findOrderInfo() orderNo = " + orderNo);
        return transferService.getOrderDetail(orderNo);
    }

    /**
     * 审核数币转账订单
     */
    @PostMapping(value = "/auditHandler")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/transferWaitReview') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> remitHandler(@RequestParam String orderNo, @RequestParam String reason,
            @RequestParam String ordSts) {
        logger.debug("tms DcTransferWaitReviewController.auditHandler() orderNo = " + orderNo + "; ordSts = " + ordSts
                + "; reason = " + reason);
        return transferService.remitHandler(orderNo, ordSts, reason);
    }
}