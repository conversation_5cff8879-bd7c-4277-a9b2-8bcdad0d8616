package com.hisun.tms.cpt.service.impl;

import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.MessageSendReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpo.client.WithdrawClient;
import com.hisun.lemon.cpo.dto.WdcProcessReqDTO;
import com.hisun.lemon.framework.data.DataHelper;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.tms.cpt.common.CptConstants;
import com.hisun.tms.cpt.dao.cpo.IWithdrawOrderDao;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.WithdrawOrderDO;
//import com.hisun.tms.cpt.repository.DatatablesWithdrawRepository;
//import com.hisun.tms.cpt.repository.WithdrawRepository;
import com.hisun.tms.cpt.service.IWithdrawService;
import com.hisun.tms.util.EncryptUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;

/**
 * 运营系统-资金出入模块-付款订单查询功能
 */
@Service("withdrawService")
public class WithdrawServiceImpl implements IWithdrawService {
    private static final Logger logger = LoggerFactory.getLogger(WithdrawServiceImpl.class);

    @Autowired
    private WithdrawClient withdrawClient;

    @Resource
    private IWithdrawOrderDao withdrawOrderDao;

    @Resource
    private EncryptUtils encryptUtils;

    @Resource
    private CmmServerClient cmmServerClient;

    /**
     * 根据条件查询订单，分页
     * @param input
     * @return
     */
    @Override
    public DataTablesOutput<WithdrawOrderDO> findAll(GenericParamInput input) {
        DataTablesOutput<WithdrawOrderDO> dataTablesOutput = new DataTablesOutput();
        List<WithdrawOrderDO> withdrawOrderDOList = new ArrayList<>() ;

        String dcFlag = input.getExtra_search().get("dcFlag");
        String reqOrdNo = input.getExtra_search().get("reqOrdNo");
        String wcOrdNo = input.getExtra_search().get("wcOrdNo");
        String rutCorgJrn = input.getExtra_search().get("rutCorgJrn");
        String rutCorg = input.getExtra_search().get("rutCorg");
        String corpBusSubTyp = input.getExtra_search().get("corpBusSubTyp");
        String ordSts = input.getExtra_search().get("ordSts");
        String userId = input.getExtra_search().get("userId");
        String beginDateStr = input.getExtra_search().get("beginDate");
        String endDateStr = input.getExtra_search().get("endDate");
        String agrPayDtStr = input.getExtra_search().get("agrPayDt");
        // 复核标识
        String reviewFlag = input.getExtra_search().get("reviewFlag");

        //从第几条记录开始获取数据
        Integer pageBegin = input.getStart();
        //获取多少条记录
        Integer pageEnd = input.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;

        //订单开始日期
        LocalDate beginDate = null;
        //订单结束日期
        LocalDate endDate = null;
        //付款协议日期
        LocalDate agrPayDt = null;

        try {
            if (JudgeUtils.isNotBlank(beginDateStr)){
                beginDateStr = beginDateStr.replaceAll("-","").trim();
                beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
            }
            if (JudgeUtils.isNotBlank(endDateStr)){
                endDateStr = endDateStr.replaceAll("-","").trim();
                endDate = DateTimeUtils.parseLocalDate(endDateStr);
            }
            if (JudgeUtils.isNotBlank(agrPayDtStr)){
                agrPayDtStr = agrPayDtStr.replaceAll("-","").trim();
                agrPayDt = DateTimeUtils.parseLocalDate(agrPayDtStr);
            }
            if ("0".equals(reviewFlag)) {
                if ("1".equals(dcFlag)){
                    //查询数币交易
                    withdrawOrderDOList = withdrawOrderDao.getDCWithdrawOrderListOnlyWaitReview(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId,
                            beginDate, endDate, agrPayDt, pageBegin, pageEnd);

                    totNum = withdrawOrderDao.getDCWithdrawOrderListTotNumOnlyWaitReview(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId,
                            beginDate, endDate, agrPayDt, pageBegin, pageEnd);
                }else{
                    withdrawOrderDOList = withdrawOrderDao.getWithdrawOrderListOnlyWaitReview(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId,
                            beginDate, endDate, agrPayDt, pageBegin, pageEnd);

                    totNum = withdrawOrderDao.getWithdrawOrderListTotNumOnlyWaitReview(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId,
                            beginDate, endDate, agrPayDt, pageBegin, pageEnd);
                }

            } else {

                if ("1".equals(dcFlag)){
                    //查询数币交易
                    withdrawOrderDOList = withdrawOrderDao.getDCWithdrawOrderList(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId,
                            beginDate, endDate, agrPayDt, pageBegin, pageEnd);

                    totNum = withdrawOrderDao.getDCWithdrawOrderListTotNum(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId,
                            beginDate, endDate, agrPayDt, pageBegin, pageEnd);
                }else {
                    withdrawOrderDOList = withdrawOrderDao.getWithdrawOrderList(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId,
                            beginDate, endDate, agrPayDt, pageBegin, pageEnd);

                    totNum = withdrawOrderDao.getWithdrawOrderListTotNum(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId,
                            beginDate, endDate, agrPayDt, pageBegin, pageEnd);
                }
            }

        } catch (Exception e){
            e.printStackTrace();
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(withdrawOrderDOList)){
            dataTablesOutput.setData(new ArrayList<WithdrawOrderDO>());
        } else {
        	//银行卡号解密
            String crdNoEnc = "";
        	for(WithdrawOrderDO withdrawOrderDo:withdrawOrderDOList){
                try {
                	crdNoEnc = withdrawOrderDo.getCrdNo();
                	if(JudgeUtils.isNotBlank(crdNoEnc)) {
//                	    String crdNo = encryptUtils.encrypt(crdNoEnc, "decrypt");
                	    String crdNo = crdNoEnc;

                        withdrawOrderDo.setCrdNo(crdNo.replaceAll("\\d{4}(?!$)", "$0 "));
                    }
                } catch (LemonException e) {
                    e.printStackTrace();
                }catch (Exception e) {
                    e.printStackTrace();
                }
        	}
            dataTablesOutput.setData(withdrawOrderDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }


    /**
     * 根据条件查询订单，不分页
     * @param input
     * @return
     */
    @Override
    public List<WithdrawOrderDO> findAllNew(GenericParamInput input) {
        List<WithdrawOrderDO> withdrawOrderDOList = new ArrayList<>() ;

        //查询条件
        String dcFlag = input.getExtra_search().get("dcFlag");
        String reqOrdNo = input.getExtra_search().get("reqOrdNo");
        String wcOrdNo = input.getExtra_search().get("wcOrdNo");
        String rutCorgJrn = input.getExtra_search().get("rutCorgJrn");
        String rutCorg = input.getExtra_search().get("rutCorg");
        String corpBusSubTyp = input.getExtra_search().get("corpBusSubTyp");
        String ordSts = input.getExtra_search().get("ordSts");
        String userId = input.getExtra_search().get("userId");
        String beginDateStr = input.getExtra_search().get("beginDate");
        String endDateStr = input.getExtra_search().get("endDate");
        String agrPayDtStr = input.getExtra_search().get("agrPayDt");

        //订单开始日期
        LocalDate beginDate = null;
        //订单结束日期
        LocalDate endDate = null;
        //付款协议日期
        LocalDate agrPayDt = null;
        try {
            if (JudgeUtils.isNotBlank(beginDateStr)){
                beginDateStr = beginDateStr.replaceAll("-","").trim();
                beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
            }
            if (JudgeUtils.isNotBlank(endDateStr)){
                endDateStr = endDateStr.replaceAll("-","").trim();
                endDate = DateTimeUtils.parseLocalDate(endDateStr);
            }
            if (JudgeUtils.isNotBlank(agrPayDtStr)){
                agrPayDtStr = agrPayDtStr.replaceAll("-","").trim();
                agrPayDt = DateTimeUtils.parseLocalDate(agrPayDtStr);
            }
            withdrawOrderDOList = withdrawOrderDao.getWithdrawOrderListNew(reqOrdNo, wcOrdNo, rutCorgJrn, rutCorg, corpBusSubTyp, ordSts, userId, beginDate, endDate, agrPayDt);
        } catch (Exception e){
            e.printStackTrace();
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isNotEmpty(withdrawOrderDOList)) {
            //银行卡号解密
            for(WithdrawOrderDO withdrawOrderDo:withdrawOrderDOList){
                String crdNoDec = null;
                String crdNoEnc = null;
                try {
                    crdNoEnc = withdrawOrderDo.getCrdNo();
                    crdNoDec = encryptUtils.encrypt(crdNoEnc, "decrypt");
                    withdrawOrderDo.setCrdNo(crdNoDec);
                } catch (LemonException e) {
                    e.printStackTrace();
                }
            }
        }
        return withdrawOrderDOList;
    }

    /**
     * 汇款成功，汇款确认
     */
    @Override
    public Map<String, String> payHandler(String wcOrdNo, String ordSts, String reason) {
        Map<String, String> resultMap = new HashMap<>();
        GenericDTO<WdcProcessReqDTO> genericDTO = new GenericDTO<>();
        // 获取当前用户信息
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String userId = String.valueOf(user.getUsername());

        WdcProcessReqDTO wdcProcessReqDTO = new WdcProcessReqDTO();
        wdcProcessReqDTO.setWcOrdNo(wcOrdNo);
        wdcProcessReqDTO.setOrdSts(ordSts);
        wdcProcessReqDTO.setReason(reason);
        wdcProcessReqDTO.setUserId(userId);

        genericDTO.setBody(wdcProcessReqDTO);
        DataHelper.setGenericDTOPropertyValue(genericDTO,"source","TMS");
        GenericRspDTO genericRspDTO = withdrawClient.payOrder(genericDTO);

        //消息推送
        //if() {  //付款类型为 商户结算
            try {
                if (JudgeUtils.isSuccess(genericRspDTO.getMsgCd())) {
                    //sendMsgCenterInfo()
                } else {
                    //sendMsgCenterInfo()
                }
            } catch (Exception e) {
                logger.error("Send MERC MESSAGE ERROR ! ");
            }
        //}


        logger.debug("tms WithdrawServiceImpl.payHandler() msgcd = " + genericRspDTO.getMsgCd() + "; msgInfo = " + genericRspDTO.getMsgInfo());
        resultMap.put("msgCd", genericRspDTO.getMsgCd());
        resultMap.put("msgInfo", genericRspDTO.getMsgInfo());
        return resultMap;
    }

    /**
     * 根据内部订单号，查询付款订单明细信息
     */
    @Override
    public WithdrawOrderDO findOrderInfo(String wcOrdNo) {
        WithdrawOrderDO withdrawOrderDO = withdrawOrderDao.getWithdrawOrder(wcOrdNo);
//        if(JudgeUtils.isNotNull(withdrawOrderDO)) {
//            String crdNoEnc = withdrawOrderDO.getCrdNo();
//            String crdNoDec = null;
//            try {
//                crdNoDec = encryptUtils.encrypt(crdNoEnc, "decrypt");
//            } catch (LemonException e) {
//                e.printStackTrace();
//            }
//            String crdNo = crdNoDec;
//            withdrawOrderDO.setCrdNo(crdNoEnc);
//        }
        return withdrawOrderDO;
    }


    /**
     * 消息中心推送
     * @param targetClient
     *         目标客户号
     * @param orderAmt
     *         订单金额
     * @throws LemonException
     */
    public void sendMsgCenterInfo(String targetClient, String orderAmt, String result) throws LemonException{
        String templeId = "";
        if(CptConstants.SUCCESS.equals(result)){
            templeId = CptConstants.MERC_BALANCE_TEMPLETEID_S;
        }
        if(CptConstants.FAIL.equals(result)){
            templeId = CptConstants.MERC_BALANCE_TEMPLETEID_S;
        }
        logger.info("message send to userId : {}, templeId : {}" ,targetClient ,templeId);
        String language = LemonUtils.getLocale().getLanguage();
        GenericDTO<MessageSendReqDTO> reqDTO = new GenericDTO<MessageSendReqDTO>();
        MessageSendReqDTO messageSendReqDTO = new MessageSendReqDTO();
        messageSendReqDTO.setUserId(targetClient);
        messageSendReqDTO.setMessageLanguage(language);
        messageSendReqDTO.setMessageTemplateId(templeId);
        Map<String, String> map = new HashMap<String, String>();
        map.put("userId", targetClient);
        map.put("orderAmt", orderAmt);
        messageSendReqDTO.setReplaceFieldMap(map);
        reqDTO.setBody(messageSendReqDTO);
        GenericRspDTO<NoBody> genericRspDTO = cmmServerClient.messageSend(reqDTO);
        if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
            LemonException.throwBusinessException(genericRspDTO.getMsgCd());
        }
    }

}
