package com.hisun.tms.cpt.service;

import com.hisun.lemon.csh.dto.order.OrderDTO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.RemitOrderDO;
import com.hisun.tms.csh.entity.OrderDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;


/**
 * 运营系统-资金出入模块-退款订单查询功能
 */
public interface ITransferService {

    /**
     * 根据条件查询充值订单列表
     *
     */
    DataTablesOutput<OrderDO> findAll(GenericParamInput input);


    DataTablesOutput<OrderDO> findAllWithdraw(GenericParamInput input);

    /**
     * 根据充值订单号，查询汇款订单详细信息
     */
    OrderDO getOrderDetail(String ordNo);

//    /**
//     * 转账审核
//     */
    Map<String, String> remitHandler(String fndOrdNo, String ordSts, String reason);
}
