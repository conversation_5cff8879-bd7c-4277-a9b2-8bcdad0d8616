package com.hisun.tms.cpt.service.impl;

import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.common.CptConstants;
import com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnInfoDO;
import com.hisun.tms.cpt.service.ICpiOrgnInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资金流出模块：合作机构信息管理
 */
@Service("cpiOrgnInfoService")
public class CpiOrgnInfoServiceImpl implements ICpiOrgnInfoService {
    private static final Logger logger = LoggerFactory.getLogger(CpiOrgnInfoServiceImpl.class);

    @Resource
    private ICpiOrgnInfoDao cpiOrgnInfoDao;

    @Override
    public Map<String, String> add(OrgnInfoDO orgnInfoDO) {
        //根据合作机构编号ID，查询机构基本信息
        Map<String, String> resultMap = new HashMap<>();
        String corpOrgId = orgnInfoDO.getCorpOrgId();
        OrgnInfoDO orgnInfoDO1 = cpiOrgnInfoDao.getOrgnInfoByOrgId(corpOrgId);
        if(JudgeUtils.isNotNull(orgnInfoDO1)) {
            //合作机构信息已存在
            resultMap.put("msgCd", CptConstants.ORG_INF_EXISTS);
            return resultMap;
        }

        //插入数据，CPO00000-成功
        String orgInfId = DateTimeUtils.formatLocalDateTime(LocalDateTime.now(), "yyyyMMddHHmmssSSS");
        orgnInfoDO.setOrgInfId(orgInfId);
        try {
            int num = cpiOrgnInfoDao.insert(orgnInfoDO);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
            logger.error("增加合作机构信息失败，异常为{}", e);
        }
        return resultMap;
    }

    @Override
    public Map<String, String> modify(OrgnInfoDO orgnInfoDO) {
        //修改数据，CPO00000-成功
        Map<String, String> resultMap = new HashMap<>();
        try {
            int num = cpiOrgnInfoDao.update(orgnInfoDO);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
            logger.error("修改合作机构信息失败，异常为{}", e);
        }
        return resultMap;
    }

    /**
     * 根据条件，分页查询
     */
    @Override
    public DataTablesOutput<OrgnInfoDO> findAll(GenericParamInput input) {
        DataTablesOutput<OrgnInfoDO> dataTablesOutput = new DataTablesOutput();
        List<OrgnInfoDO> orgnInfoDOList = new ArrayList<>();

        //查询条件
        String corpOrgId = input.getExtra_search().get("corpOrgId").trim();
        String corpOrgTyp = input.getExtra_search().get("corpOrgTyp").trim();

        //从第几条记录开始获取数据，以及每页最大笔数
        Integer pageBegin = input.getStart();
        Integer pageNum = input.getLength();

        //根据条件查询得到记录的总笔数
        int totNum = 0;
        try {
            //分页查询订单列表
            orgnInfoDOList = cpiOrgnInfoDao.getOrgnInfoList(corpOrgId, corpOrgTyp, pageBegin, pageNum);

            //查询满足条件的订单总笔数
            totNum = cpiOrgnInfoDao.getOrgnInfoListTotNum(corpOrgId, corpOrgTyp);
        } catch (Exception e){
            logger.error("资金流出合作机构基本信息查询列表失败，异常为{}", e);
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(orgnInfoDOList)){
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(orgnInfoDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }

    /**
     * 查询资金流出模块所有的合作机构基本信息
     */
    @Override
    public List<OrgnInfoDO> findAllOrgnInfo() {
        return cpiOrgnInfoDao.findAllOrgnInfo();
    }

    /**
     * 根据机构编号，查询合作机构基本信息
     */
    @Override
    public OrgnInfoDO findOrgnInfo(String corpOrgId) {
        return cpiOrgnInfoDao.findOrgnInfo(corpOrgId);
    }

}
