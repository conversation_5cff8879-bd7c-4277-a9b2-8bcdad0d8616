package com.hisun.tms.cpt.service.impl;

import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.cpt.common.CptConstants;
import com.hisun.tms.cpt.handler.ExcelFileHandler;
import com.hisun.tms.cpt.model.FileConfigDO;
import com.hisun.tms.cpt.service.IExcelFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 简单的交易查询功能，文件生成与下载
 */
@Service("excelFileService")
public class ExcelFileServiceImpl implements IExcelFileService {

    private static final Logger logger = LoggerFactory.getLogger(ExcelFileServiceImpl.class);
    @Resource
    private ExcelFileHandler excelFileHandler;

    /**
     * 根据文件导出参数，生成文件，返回文件的绝对地址
     */
    @Override
    public String exportFile(FileConfigDO fileConfigDO) {
        //文件全名 + 加上时间戳 + 文件后缀，避免覆盖其他人生成的文件
        String fullFileName = fileConfigDO.getFileName() + "_" + DateTimeUtils.getCurrentDateTimeStr() + fileConfigDO.getFileSuffix();
        //根据文件后缀，调用不同的方法生成文件
        try {
            if (CptConstants.EXCEL_XLS.equals(fileConfigDO.getFileSuffix())) {
                excelFileHandler.createXlsExcel(fileConfigDO, fullFileName);
            }
            if (CptConstants.EXCEL_XLSX.equals(fileConfigDO.getFileSuffix())) {
                excelFileHandler.createXlsxExcel(fileConfigDO, fullFileName);
            }
        } catch (Exception e) {
            logger.info("==================create file failed==================");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据文件导出参数，返回文件至客户端下载
     */
    @Override
    public void downloadFile(String absolutePath) {
        return;
    }
}
