package com.hisun.tms.cpt.service.impl;

import com.hisun.lemon.cpi.client.RemittanceClient;
import com.hisun.lemon.csh.client.CshAuditTransferOrderClient;
import com.hisun.lemon.csh.dto.cashier.TranOrderAuditDTO;
import com.hisun.lemon.framework.data.DataHelper;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.ITransferService;
import com.hisun.tms.csh.dao.IOrderDao;
import com.hisun.tms.csh.entity.OrderDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运营系统-转账订单查询功能
 */
@Service("transferService")
public class TransferServiceImpl implements ITransferService {

    private static final Logger logger = LoggerFactory.getLogger(TransferServiceImpl.class);


    @Resource
    private RemittanceClient remittanceClient;

    @Resource
    private IOrderDao orderDao;

    @Resource
    private CshAuditTransferOrderClient auditTransferOrderClient;

    /**
     * 根据条件，查询转账订单列表
     */
    @Override
    public DataTablesOutput<OrderDO> findAll(GenericParamInput input) {
        DataTablesOutput<OrderDO> dataTablesOutput = new DataTablesOutput();

        String dcFlag = input.getExtra_search().get("dcFlag");
        String orderNo = input.getExtra_search().get("orderNo");
        String payerId = input.getExtra_search().get("payerId");
        String payeeId = input.getExtra_search().get("payeeId");
        String busOrderNo = input.getExtra_search().get("busOrderNo");
        String orderStatus = input.getExtra_search().get("orderStatus");
        // 复核标识
        String reviewFlag = input.getExtra_search().get("reviewFlag");
        // 获取当前用户信息
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String userId = String.valueOf(user.getUsername());

        //从第几条记录开始获取数据
        Integer pageBegin = input.getStart();
        //获取多少条记录
        Integer pageNum = input.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;

        //根据条件查询商户账单信息，分页
        Map<Object, Object> map = new HashMap<>();
        map.put("orderNo", orderNo);
        map.put("payerId", payerId);
        map.put("payeeId", payeeId);
        map.put("busOrderNo", busOrderNo);
        map.put("orderStatus", orderStatus);
        map.put("pageBegin", pageBegin);
        map.put("pageNum", pageNum);

        List<OrderDO> orderDTOList = new ArrayList<>();
        try {
            if ("0".equals(reviewFlag)) {
                if ("1".equals(dcFlag)){
                    //查询数币交易

                    orderDTOList = orderDao.DCqueryWaitReviewList(map);
                    totNum = orderDao.DCqueryListCount(map);
                }else{
                    //分页查询订单列表
                    orderDTOList = orderDao.queryWaitReviewList(map);
                    //查询满足条件的订单总笔数
                    totNum = orderDao.queryListCount(map);
                }
            } else {
                if("1".equals(dcFlag)){
                    //数币
                    //分页查询订单列表
                    orderDTOList = orderDao.DCqueryNotFinalAuditList(map);
                    //查询满足条件的订单总笔数
                    totNum = orderDao.DCqueryNotFinalAuditListCount(map);
                }else{
                    //分页查询订单列表
                    orderDTOList = orderDao.queryNotFinalAuditList(map);
                    //查询满足条件的订单总笔数
                    totNum = orderDao.queryNotFinalAuditListCount(map);
                }
            }
        } catch (Exception e) {
            logger.error("充值订单查询列表失败，异常为:", e);
        }

        dataTablesOutput.setData(orderDTOList);
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }

    /**
     * 根据条件，查询提现订单列表
     */
    @Override
    public DataTablesOutput<OrderDO> findAllWithdraw(GenericParamInput input) {
        DataTablesOutput<OrderDO> dataTablesOutput = new DataTablesOutput();

        String dcFlag = input.getExtra_search().get("dcFlag");
        String orderNo = input.getExtra_search().get("orderNo");
        String payerId = input.getExtra_search().get("payerId");
        String payeeId = input.getExtra_search().get("payeeId");
        String busOrderNo = input.getExtra_search().get("busOrderNo");
        String orderStatus = input.getExtra_search().get("orderStatus");
        // 复核标识
        String reviewFlag = input.getExtra_search().get("reviewFlag");
        // 获取当前用户信息
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String userId = String.valueOf(user.getUsername());

        //从第几条记录开始获取数据
        Integer pageBegin = input.getStart();
        //获取多少条记录
        Integer pageNum = input.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;

        //根据条件查询商户账单信息，分页
        Map<Object, Object> map = new HashMap<>();
        map.put("orderNo", orderNo);
        map.put("payerId", payerId);
        map.put("payeeId", payeeId);
        map.put("busOrderNo", busOrderNo);
        map.put("orderStatus", orderStatus);
        map.put("pageBegin", pageBegin);
        map.put("pageNum", pageNum);

        List<OrderDO> orderDTOList = new ArrayList<>();
        try {
            if ("0".equals(reviewFlag)) {
                if ("1".equals(dcFlag)){
                    //查询数币交易

                    orderDTOList = orderDao.DXqueryWaitReviewList(map);
                    totNum = orderDao.DXqueryListCount(map);
                }
            } else {
                if("1".equals(dcFlag)){
                    //数币
                    //分页查询订单列表
                    orderDTOList = orderDao.DXqueryNotFinalAuditList(map);
                    //查询满足条件的订单总笔数
                    totNum = orderDao.DXqueryNotFinalAuditListCount(map);
                }
            }
        } catch (Exception e) {
            logger.error("充值订单查询列表失败，异常为:", e);
        }

        dataTablesOutput.setData(orderDTOList);
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }


    /**
     * 根据充值订单号，查询汇款订单详细信息
     *
     * @param ordNo
     */
    @Override
    public OrderDO getOrderDetail(String ordNo) {
        return orderDao.get(ordNo);
    }

    /**
     * 转账审核
     */
    @Override
    public Map<String, String> remitHandler(String fndOrdNo, String ordSts, String reason) {
        //请求参数
        TranOrderAuditDTO auditDTO = new TranOrderAuditDTO();

        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String userId = String.valueOf(user.getUsername());

        auditDTO.setUserId(userId);
        auditDTO.setStatus(ordSts);
        auditDTO.setOrderNo(fndOrdNo);
        auditDTO.setReason(reason);

        //请求报文
        GenericDTO<TranOrderAuditDTO> genericDTO = new GenericDTO<>();
        genericDTO.setBody(auditDTO);
        DataHelper.setGenericDTOPropertyValue(genericDTO, "source", "TMS");
        GenericRspDTO genericRspDTO = auditTransferOrderClient.audit(genericDTO);

        //返回码
        Map<String, String> resultMap = new HashMap<>();
        String msgCd = genericRspDTO.getMsgCd();
        resultMap.put("msgCd", msgCd);
        return resultMap;
    }
}
