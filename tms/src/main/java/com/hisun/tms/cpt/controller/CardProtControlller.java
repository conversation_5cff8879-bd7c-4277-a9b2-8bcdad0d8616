package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.CardProtDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.ICardService;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 用户银行卡信息管理
 */
@Controller
@RequestMapping("/cpt/param/card")
public class CardProtControlller {

    @Resource
    private ICardService cardService;

    /**
     * 返回退款订单查询功能主页面/cptmgr/param/card
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/param/card') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/param/card/card");
        return modelAndView;
    }

    /**
     * 查询用户已绑定的银行卡信息
     */
    @PostMapping(value = "findCardProtList")
    @PreAuthorize("hasPermission('','/cptmgr/param/card') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<CardProtDO> findCardProtList(@Valid @RequestBody GenericParamInput input) {
        return cardService.findCardProtList(input);
    }

    /**
     * 查询用户已绑定的银行卡详细信息
     */
    @PostMapping(value = "findCardProtDetail")
    @PreAuthorize("hasPermission('','/cptmgr/param/card') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public CardProtDO findCardProtDetail(@RequestParam String agrNo) {
        return cardService.getCardProtInfo(agrNo);
    }

    /**
     * 用户解除绑定的银行卡
     * 0-我方解绑，不调用银行接口解绑，平台更新用户签约信息失效
     * 1-双方解绑，调用银行接口解绑，更新用户签约信息失效
     */
    @PostMapping(value = "unbindCard")
    @PreAuthorize("hasPermission('','/cptmgr/param/card') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> unbindCard(@RequestParam String agrNo, @RequestParam String agrDirect) {
        return cardService.unbindCard(agrNo, agrDirect);
    }
}
