package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.FundOrderDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.RemitOrderDO;
import com.hisun.tms.cpt.service.IFundService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 充值订单基本操作，包括快捷订单、网银(微信、支付宝、翼支付)订单、汇款订单
 */
@Controller
@RequestMapping("/cpt/order/fund")
public class FundController {
    private static final Logger logger = LoggerFactory.getLogger(FundController.class);

    @Resource
    private IFundService fundService;

    /**
     * 返回退款订单查询功能主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/order/fund') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/order/fund/fund");
        return modelAndView;
    }

    /**
     * 退款订单列表查询
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/order/fund') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<FundOrderDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return fundService.findAll(input);
    }

    /**
     * 根据充值订单号，查询汇款订单详细信息
     */
    @PostMapping(value = "/findRemitDetail")
    @PreAuthorize("hasPermission('','/cptmgr/order/fund') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public RemitOrderDO findRemitDetail(@RequestParam String fndOrdNo) {
        logger.debug("tms FundController.findRemitDetail() fndOrdNo = " + fndOrdNo);
        return fundService.getRemitOrderDetail(fndOrdNo);
    }

    /**
     * 确认汇款充值订单，或退回
     */
    @PostMapping(value = "/remitHandler")
    @PreAuthorize("hasPermission('','/cptmgr/order/fund') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> remitHandler(@RequestParam String fndOrdNo, @RequestParam String ordSts, @RequestParam String reason) {
        logger.debug("tms FundController.remitHandler() fndOrdNo = " + fndOrdNo + "; ordSts = " + ordSts + "; reason = " + reason);
        return fundService.remitHandler(fndOrdNo, ordSts, reason);
    }


}
