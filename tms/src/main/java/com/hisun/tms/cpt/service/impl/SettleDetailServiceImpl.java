package com.hisun.tms.cpt.service.impl;

import com.hisun.lemon.cpi.client.EbankpayClient;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.dao.cpi.ISettleDetailDao;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.SettleDetailDO;
import com.hisun.tms.cpt.service.ISettleDetailService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运营系统-清结算模块-结算明细信息查询功能
 */
@Service("settleDetailService")
public class SettleDetailServiceImpl implements ISettleDetailService{

    private static final Logger logger = LoggerFactory.getLogger(SettleDetailServiceImpl.class);

    @Resource
    private ISettleDetailDao iSettleDetailDao;

    @Resource
    private EbankpayClient ebankpayClient;

    /**
     * 根据条件查询列表
     */
    @Override
    public DataTablesOutput<SettleDetailDO> findAll(GenericParamInput input) {
        DataTablesOutput<SettleDetailDO> dataTablesOutput = new DataTablesOutput<>();
        List<SettleDetailDO> settleDetailDOList = new ArrayList<>() ;

        String stlFlg = input.getExtra_search().get("stlFlg");
        String rutCorg = input.getExtra_search().get("rutCorg");
        String beginDateStr = input.getExtra_search().get("beginDate");
        String endDateStr = input.getExtra_search().get("endDate");

        //从第几条记录开始获取数据
        Integer pageBegin = input.getStart();
        //获取多少条记录
        Integer pageNum = input.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;
        //订单开始日期
        LocalDate beginDate = null;
        //订单结束日期
        LocalDate endDate = null;

        try {
            if (JudgeUtils.isNotBlank(beginDateStr)){
                beginDateStr = beginDateStr.replaceAll("-","").trim();
                beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
            }
            if (JudgeUtils.isNotBlank(endDateStr)){
                endDateStr = endDateStr.replaceAll("-","").trim();
                endDate = DateTimeUtils.parseLocalDate(endDateStr);
            }
            settleDetailDOList = iSettleDetailDao.getSettleList(stlFlg, rutCorg, beginDate, endDate, pageBegin, pageNum);

            totNum = iSettleDetailDao.getSettleListTotNum(stlFlg, rutCorg, beginDate, endDate, pageBegin, pageNum);
        } catch (Exception e){
            logger.error("结算明细信息查询列表失败，异常为{}", e);
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(settleDetailDOList)){
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(settleDetailDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }

    /**
     * 查询商户结算信息
     */
    @Override
    public Map<String, String> refresh(String stlFlg, String rutCorg, String startDate, String endDate) {
        //请求参数
        Map<String, String> resultMap = new HashMap<>();
        int intStlFlg = 0;
        LocalDate beginDate = null;
        LocalDate dateEnd = null;
        if(!stlFlg.isEmpty()){
            intStlFlg = Integer.parseInt(stlFlg);
        }else{
            intStlFlg = 2;
        }
        if(rutCorg.isEmpty()){
            rutCorg = "WeChat";
        }
        try{

            ebankpayClient.settlementQuery(intStlFlg, rutCorg, startDate, endDate);
            resultMap.put("msgCd", "0");
        }catch (Exception e){
            logger.error("查询商户结算信息失败，异常为{}", e);
            resultMap.put("msgCd", "1");
            return resultMap;
        }
        //返回码
        return resultMap;
    }
}
