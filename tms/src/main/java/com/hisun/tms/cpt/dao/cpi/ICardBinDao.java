package com.hisun.tms.cpt.dao.cpi;

import com.hisun.tms.cpt.model.CardBinDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 卡bin信息
 */
@Mapper
public interface ICardBinDao {

    /**
     * 增加卡bin信息
     */
    int insert(CardBinDO cardBinDO);

    /**
     * 修改卡bin信息
     */
    int update(CardBinDO cardBinDO);

    /**
     * 删除卡bin信息
     */
    int delete(String binId);

    /**
     * 资金流入模块，分页查询卡bin信息
     * @param crdBin 卡bin
     * @param capCorg 资金机构
     * @param crdAcTyp 卡类型
     * @param crdLth 卡号长度
     * @param pageBegin 分页开始
     * @param pageNum 每页最大数目
     * @return
     */
    List<CardBinDO> getCardBinList(@Param("crdBin") String crdBin,
                                   @Param("capCorg") String capCorg,
                                   @Param("crdAcTyp") String crdAcTyp,
                                   @Param("crdLth") String crdLth,
                                   @Param("belongingState") String belongingState,
                                   @Param("ascriptionArea") String ascriptionArea,
                                   @Param("pageBegin") Integer pageBegin,
                                   @Param("pageNum") Integer pageNum);

    /**
     * 资金流入模块，查询卡bin信息列表总笔数
     */
    int getCardBinListTotNum(@Param("crdBin") String crdBin,
                             @Param("capCorg") String capCorg,
                             @Param("crdAcTyp") String crdAcTyp,
                             @Param("crdLth") String crdLth,
                             @Param("belongingState") String belongingState,
                             @Param("ascriptionArea") String ascriptionArea);

    /**
     * 根据卡bin查询
     */
    CardBinDO getByCrdBin(String crdBin);
}
