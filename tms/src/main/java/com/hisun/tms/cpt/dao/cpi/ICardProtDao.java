package com.hisun.tms.cpt.dao.cpi;

import com.hisun.tms.cpt.model.CardProtDO;
import com.hisun.tms.cpt.model.CardProtJrnDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 用户银行卡信息管理
 */
@Mapper
public interface ICardProtDao {

    /**
     * 用户已绑定的银行卡信息查询
     * @param mblNo 手机号
     * @param userId 用户号/商户号
     * @param crdAcTyp 卡类型
     * @param agrEffFlg 生效标志
     * @param bnkPsnFlg 对公/对私标志
     * @param beginDate 签约开始日期
     * @param endDate 签约截止日期
     * @param pageBegin 从第几行开始取数据
     * @param pageNum 取多少条数据
     * @return
     */
    List<CardProtDO> getCardProtList(@Param("mblNo") String mblNo,
                                      @Param("userId") String userId,
                                      @Param("crdAcTyp") String crdAcTyp,
                                      @Param("agrEffFlg") String agrEffFlg,
                                      @Param("bnkPsnFlg") String bnkPsnFlg,
                                      @Param("beginDate") LocalDate beginDate,
                                      @Param("endDate") LocalDate endDate,
                                      @Param("pageBegin") Integer pageBegin,
                                      @Param("pageNum") Integer pageNum);
    /**
     * 用户已绑定的银行卡信息查询列表总笔数
     * @param mblNo 手机号
     * @param userId 用户号/商户号
     * @param crdAcTyp 卡类型
     * @param agrEffFlg 生效标志
     * @param bnkPsnFlg 对公/对私标志
     * @param beginDate 签约开始日期
     * @param endDate 签约截止日期
     * @param pageBegin 从第几行开始取数据
     * @param pageNum 取多少条数据
     * @return
     */
    int getCardProtListTotNum(@Param("mblNo") String mblNo,
                               @Param("userId") String userId,
                               @Param("crdAcTyp") String crdAcTyp,
                               @Param("agrEffFlg") String agrEffFlg,
                               @Param("bnkPsnFlg") String bnkPsnFlg,
                               @Param("beginDate") LocalDate beginDate,
                               @Param("endDate") LocalDate endDate,
                               @Param("pageBegin") Integer pageBegin,
                               @Param("pageNum") Integer pageNum);

    /**
     * 根据内部协议号，查询用户绑卡详细信息
     */
    CardProtDO getCardProtInfo(@Param("agrNo") String agrNo);

    /**
     * 插入银行卡签约流水信息
     */
    int insertCardProtJrn(CardProtJrnDO cardProtJrnDO);

    /**
     * 更新用户签约信息为失效
     */
    int updateCardProtDO(String agrNo, LocalDate unsignDt, LocalTime unsignTm);

}
