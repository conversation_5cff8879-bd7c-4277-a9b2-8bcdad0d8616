package com.hisun.tms.cpt.dao.cpi;

import com.hisun.tms.cpt.model.FundOrderDO;
import com.hisun.tms.cpt.model.RemitOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 充值订单，包括快捷订单、网银(微信、支付宝、翼支付)订单、汇款订单
 */
@Mapper
public interface IFundOrderDao {

    /**
     * 根据条件，查询充值订单列表
     * @param mblNo 手机号
     * @param reqOrdNo 请求订单号
     * @param fudOrdNo 充值订单号
     * @param corpBusTyp 交易类型
     * @param corpBusSubTyp 充值类型
     * @param rutCorpOrg 路径机构
     * @param crdCorpOrg 资金机构
     * @param ordSts 订单状态
     * @param beginDate 订单提交开始日期
     * @param endDate 订单提交结束日期
     * @param pageBegin 分页从第几条开始
     * @param pageNum 取多少条数据
     * @return
     */
    List<FundOrderDO> getFundOrderList(@Param("mblNo") String mblNo,
                                       @Param("reqOrdNo") String reqOrdNo,
                                       @Param("fudOrdNo") String fudOrdNo,
                                       @Param("corpBusTyp") String corpBusTyp,
                                       @Param("corpBusSubTyp") String corpBusSubTyp,
                                       @Param("rutCorpOrg") String rutCorpOrg,
                                       @Param("crdCorpOrg") String crdCorpOrg,
                                       @Param("ordSts") String ordSts,
                                       @Param("beginDate") LocalDate beginDate,
                                       @Param("endDate") LocalDate endDate,
                                       @Param("pageBegin") Integer pageBegin,
                                       @Param("pageNum") Integer pageNum);

    /**
     * 根据条件，查询充值订单列表
     * @param mblNo 手机号
     * @param reqOrdNo 请求订单号
     * @param fudOrdNo 充值订单号
     * @param corpBusTyp 交易类型
     * @param corpBusSubTyp 充值类型
     * @param rutCorpOrg 路径机构
     * @param crdCorpOrg 资金机构
     * @param ordSts 订单状态
     * @param beginDate 订单提交开始日期
     * @param endDate 订单提交结束日期
     * @param pageBegin 分页从第几条开始
     * @param pageNum 取多少条数据
     * @return
     */
    List<FundOrderDO> getFundOrderListOnlyWaitReview(@Param("mblNo") String mblNo,
                                       @Param("reqOrdNo") String reqOrdNo,
                                       @Param("fudOrdNo") String fudOrdNo,
                                       @Param("corpBusTyp") String corpBusTyp,
                                       @Param("corpBusSubTyp") String corpBusSubTyp,
                                       @Param("rutCorpOrg") String rutCorpOrg,
                                       @Param("crdCorpOrg") String crdCorpOrg,
                                       @Param("ordSts") String ordSts,
                                       @Param("beginDate") LocalDate beginDate,
                                       @Param("endDate") LocalDate endDate,
                                       @Param("pageBegin") Integer pageBegin,
                                       @Param("pageNum") Integer pageNum);

    /**
     * 根据条件，查询充值订单总笔数
     * @param mblNo 手机号
     * @param reqOrdNo 请求订单号
     * @param fudOrdNo 充值订单号
     * @param corpBusTyp 交易类型
     * @param corpBusSubTyp 充值类型
     * @param rutCorpOrg 路径机构
     * @param crdCorpOrg 资金机构
     * @param ordSts 订单状态
     * @param beginDate 订单提交开始日期
     * @param endDate 订单提交结束日期
     * @param pageBegin 分页从第几条开始
     * @param pageNum 取多少条数据
     * @return
     */
    int getFundOrderListTotNum(@Param("mblNo") String mblNo,
                               @Param("reqOrdNo") String reqOrdNo,
                               @Param("fudOrdNo") String fudOrdNo,
                               @Param("corpBusTyp") String corpBusTyp,
                               @Param("corpBusSubTyp") String corpBusSubTyp,
                               @Param("rutCorpOrg") String rutCorpOrg,
                               @Param("crdCorpOrg") String crdCorpOrg,
                               @Param("ordSts") String ordSts,
                               @Param("beginDate") LocalDate beginDate,
                               @Param("endDate") LocalDate endDate,
                               @Param("pageBegin") Integer pageBegin,
                               @Param("pageNum") Integer pageNum);

    /**
     * 根据条件，查询充值订单总笔数
     * @param mblNo 手机号
     * @param reqOrdNo 请求订单号
     * @param fudOrdNo 充值订单号
     * @param corpBusTyp 交易类型
     * @param corpBusSubTyp 充值类型
     * @param rutCorpOrg 路径机构
     * @param crdCorpOrg 资金机构
     * @param ordSts 订单状态
     * @param beginDate 订单提交开始日期
     * @param endDate 订单提交结束日期
     * @param pageBegin 分页从第几条开始
     * @param pageNum 取多少条数据
     * @return
     */
    int getFundOrderListTotNumOnlyWaitReview(@Param("mblNo") String mblNo,
                               @Param("reqOrdNo") String reqOrdNo,
                               @Param("fudOrdNo") String fudOrdNo,
                               @Param("corpBusTyp") String corpBusTyp,
                               @Param("corpBusSubTyp") String corpBusSubTyp,
                               @Param("rutCorpOrg") String rutCorpOrg,
                               @Param("crdCorpOrg") String crdCorpOrg,
                               @Param("ordSts") String ordSts,
                               @Param("beginDate") LocalDate beginDate,
                               @Param("endDate") LocalDate endDate,
                               @Param("pageBegin") Integer pageBegin,
                               @Param("pageNum") Integer pageNum);

    /**
     * 根据充值订单号，查询汇款订单详细信息
     * @param fndOrdNo 充值内部订单号
     * @return
     */
    RemitOrderDO getRemitOrderDetail(@Param("fndOrdNo") String fndOrdNo);

}
