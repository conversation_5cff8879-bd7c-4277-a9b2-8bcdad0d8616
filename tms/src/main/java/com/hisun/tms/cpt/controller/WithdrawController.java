package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.WithdrawOrderDO;
import com.hisun.tms.cpt.service.IWithdrawService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 付款订单基本操作
 */
@Controller
@RequestMapping("/cpt/order/withdraw")
public class WithdrawController {
    private static final Logger logger = LoggerFactory.getLogger(WithdrawController.class);

    @Resource
    private IWithdrawService withdrawService;

    /**
     * 返回UI首页
     * @return
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/order/withdraw') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/order/withdraw/withdraw");
        return modelAndView;
    }

    /**
     * 查询所有付款订单信息
     * @param input
     * @return
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/order/withdraw') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<WithdrawOrderDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return withdrawService.findAll(input);
    }

    /**
     * 根据内部订单号，查询付款订单明细信息
     * @param wcOrdNo
     * @return
     */
    @PostMapping(value = "/findOrderInfo")
    @PreAuthorize("hasPermission('','/cptmgr/order/withdraw') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public WithdrawOrderDO findOrderInfo(@RequestParam String wcOrdNo) {
        logger.debug("tms WithdrawController.findOrderInfo() wcOrdNo = " + wcOrdNo);
        return withdrawService.findOrderInfo(wcOrdNo);
    }

    /**
     * 确认汇款成功，或退回
     */
    @PostMapping(value = "/payHandler")
    @PreAuthorize("hasPermission('','/cptmgr/order/withdraw') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> payHandler(@RequestParam String wcOrdNo, @RequestParam String ordSts, @RequestParam String reason) {
        logger.debug("tms WithdrawController.payHandler() wcOrdNo = " + wcOrdNo + "; ordSts = " + ordSts + "; reason = " + reason);
        return withdrawService.payHandler(wcOrdNo, ordSts, reason);
    }
    /**
     * 批量处理订单，或退回
     */
    @PostMapping(value = "/payHandlerBatch")
    @PreAuthorize("hasPermission('','/cptmgr/order/withdraw') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> payHandlerBach(@RequestParam(value = "wcOrdNos[]") String[] wcOrdNos, @RequestParam String ordSts, @RequestParam String reason) {
        logger.debug("tms WithdrawController.payHandler() wcOrdNo = "  + "; ordSts = " + ordSts + "; reason = " + reason);
        Map<String, String> map = new HashMap<String,String>() ;
        int t = 0 ;
        for (String wcOrdNo :wcOrdNos) {

            Map<String, String> rs = withdrawService.payHandler(wcOrdNo, ordSts, reason);
            if ("CPO00000".equals(rs.get("msgCd"))) {
                t += 1 ;
            }
        }
        if (t == wcOrdNos.length) {
            map.put("msgCd", "CPO00000") ;
        } else if ( t == 0 ) {
            map.put("msgCd", "0") ;
        } else {
            //部分成功
            map.put("msgCd", "1") ;
            map.put("successNum", t+"") ;
        }
        return map;
    }

}
