package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.FundOrderDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.SettleDetailDO;
import com.hisun.tms.cpt.service.ISettleDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.Map;

/**
 * 支付通道，结算明细信息查询
 */
@Controller
@RequestMapping("/cpt/payment/settle")
public class SettleDetailController {
    private static final Logger logger = LoggerFactory.getLogger(SettleDetailController.class);

    @Resource
    private ISettleDetailService iSettleDetailService;

    /**
     * 返回退款订单查询功能主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/csmmgr/settle/record') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/settle/settle");
        return modelAndView;
    }

    /**
     * 结算信息列表查询
     * @param input
     * @return
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/csmmgr/settle/record') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<SettleDetailDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return iSettleDetailService.findAll(input);
    }

    /**
     * 确认汇款充值订单，或退回
     */
    @PostMapping(value = "/refresh")
    @PreAuthorize("hasPermission('','/csmmgr/settle/record') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> refresh(@RequestParam(value = "stlFlg") String stlFlg,
                                            @RequestParam(value = "rutCorg") String rutCorg,
                                            @RequestParam(value = "startDate") String startDate,
                                            @RequestParam(value = "endDate") String endDate) {
        logger.debug("tms SettleDetailController.refresh() stlFlg = " + stlFlg + "; rutCorg = " + rutCorg + "; startDate = " + startDate+ "; endDate = " + endDate);
        return iSettleDetailService.refresh(stlFlg, rutCorg, startDate, endDate);
    }


}
