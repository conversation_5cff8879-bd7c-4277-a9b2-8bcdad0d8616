package com.hisun.tms.cpt.model;

import java.math.BigDecimal;

/**
 * 充值订单，包括快捷订单、网银(微信、支付宝、翼支付)订单、汇款订单
 */
public class FundOrderDO {
    /**
     * 手机号
     */
    private String mblNo;

    /**
     * 用户号/商户号
     */
    private String userId;

    /**
     * 交易类型
     */
    private String corpBusTyp;

    /**
     *充值类型
     */
    private String corpBusSubTyp;

    /**
     * 路由机构
     */
    private String rutCorpOrg;

    /**
     * 资金机构
     */
    private String crdCorpOrg;

    /**
     * 卡种
     */
    private String crdAcTyp;

    /**
     * 充值金额
     */
    private BigDecimal ordAmt;

    /**
     * 已退款金额
     */
    private BigDecimal ordRfdAmt;

    /**
     * 订单状态
     */
    private String ordSts;

    /**
     * 订单提交日期
     */
    private String ordDt;

    /**
     * 订单提交时间
     */
    private String ordTm;

    /**
     * 请求订单号
     */
    private String reqOrdNo;

    /**
     * 充值订单号
     */
    private String fudOrdNo;

    /**
     * 处理理由
     */
    private String rmk;

    /**
     * 手续费
     */

    private BigDecimal fee ;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public String getFudOrdNo() {
        return fudOrdNo;
    }

    public void setFudOrdNo(String fudOrdNo) {
        this.fudOrdNo = fudOrdNo;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public BigDecimal getOrdRfdAmt() {
        return ordRfdAmt;
    }

    public void setOrdRfdAmt(BigDecimal ordRfdAmt) {
        this.ordRfdAmt = ordRfdAmt;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(String ordDt) {
        this.ordDt = ordDt;
    }

    public String getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(String ordTm) {
        this.ordTm = ordTm;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }
}