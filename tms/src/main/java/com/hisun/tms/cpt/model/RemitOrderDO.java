package com.hisun.tms.cpt.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 汇款订单
 */
public class RemitOrderDO {
    /**
     * 手机号
     */
    private String mblNo;

    /**
     * 用户号/商户号
     */
    private String userId;

    /**
     * 提交日期
     */
    private LocalDate ordDt;

    /**
     * 提交时间
     */
    private LocalTime ordTm;

    /**
     * 订单状态
     */
    private String ordSts;

    /**
     * 汇款银行
     */
    private String rutCorpOrg;

    /**
     * 收款银行
     */
    private String crdCorpOrg;

    /**
     * 汇款凭证图片URL
     */
    private String picUrl;

    /**
     * 摘要
     */
    private String rmk;

    /**
     * 处理理由
     */
    private String reason;

    /**
     * 充值金额
     */
    private BigDecimal ordAmt;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }
}
