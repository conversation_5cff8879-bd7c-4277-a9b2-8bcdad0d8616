package com.hisun.tms.cpt.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 运营系统-资金出入模块-付款订单查询功能，列表查询DO
 */
public class WithdrawOrderDO {
    /**
     * @Fields reqOrdNo 请求订单号
     */
    private String reqOrdNo;

    /**
     * @Fields wcOrdNo 付款订单号
     */
    private String wcOrdNo;

    /**
     * @Fields userId 用户号/商户号
     */
    private String userId;

    /**
     * @Fields userNm 用户/商户名称
     */
    private String userNm;

    /**
     * @Fields ordDt 提交日期
     */
    private LocalDate ordDt;

    /**
     * @Fields postDt 提交时间
     */
    private LocalTime ordTm;

    /**
     * @Fields agrPayDt 付款日期
     */
    private LocalDate agrPayDt;

    /**
     * @Fields corpBusSubTyp 付款类型，即业务子类型
     */
    private String corpBusSubTyp;

    /**
     * @Fields rutCorg 付款银行
     */
    private String rutCorg;

    /**
     * @Fields wcAplAmt 付款金额
     */
    private BigDecimal wcAplAmt;

    /**
     * @Fields capCorg 收款银行
     */
    private String capCorg;

    /**
     * @Fields capCrdNm 收款户名
     */
    private String capCrdNm;

    /**
     * @Fields crdNo 收款卡号 **** **** **** 1234
     */
    private String crdNo;

    /**
     * @Fields ordSts 订单状态 S1：成功，F1：失败，W3：处理中
     */
    private String ordSts;

    /**
     * @Fields rutCorgJrn 银行返回的流水号
     */
    private String rutCorgJrn;

    /**
     * @Fields orgRspMsg 银行返回处理信息
     */
    private String orgRspMsg;

    /**
     * rmk 备注，处理理由
     */
    private String rmk;

    /**
     * 实际付款时间
     */
    private LocalDate ordSuccDt ;

    /**
     * 分行名称
     */
    private String subBranch;

    public String getSubBranch() {
        return subBranch;
    }

    public void setSubBranch(String subBranch) {
        this.subBranch = subBranch;
    }

    public LocalDate getOrdSuccDt() {
        return ordSuccDt;
    }

    public void setOrdSuccDt(LocalDate ordSuccDt) {
        this.ordSuccDt = ordSuccDt;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public String getWcOrdNo() {
        return wcOrdNo;
    }

    public void setWcOrdNo(String wcOrdNo) {
        this.wcOrdNo = wcOrdNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public LocalDate getAgrPayDt() {
        return agrPayDt;
    }

    public void setAgrPayDt(LocalDate agrPayDt) {
        this.agrPayDt = agrPayDt;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public BigDecimal getWcAplAmt() {
        return wcAplAmt;
    }

    public void setWcAplAmt(BigDecimal wcAplAmt) {
        this.wcAplAmt = wcAplAmt;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getCapCrdNm() {
        return capCrdNm;
    }

    public void setCapCrdNm(String capCrdNm) {
        this.capCrdNm = capCrdNm;
    }

    public String getCrdNo() {
        return crdNo;
    }

    public void setCrdNo(String crdNo) {
        this.crdNo = crdNo;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getRutCorgJrn() {
        return rutCorgJrn;
    }

    public void setRutCorgJrn(String rutCorgJrn) {
        this.rutCorgJrn = rutCorgJrn;
    }

    public String getOrgRspMsg() {
        return orgRspMsg;
    }

    public void setOrgRspMsg(String orgRspMsg) {
        this.orgRspMsg = orgRspMsg;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getUserNm() {
        return userNm;
    }

    public void setUserNm(String userNm) {
        this.userNm = userNm;
    }

}
