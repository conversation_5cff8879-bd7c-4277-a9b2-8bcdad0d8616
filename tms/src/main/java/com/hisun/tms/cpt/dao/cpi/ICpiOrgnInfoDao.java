package com.hisun.tms.cpt.dao.cpi;

import com.hisun.tms.cpt.model.OrgnInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资金流入模块，合作机构信息管理
 */
@Mapper
public interface ICpiOrgnInfoDao {
    /**
     * 新增合作资金机构基本信息
     */
    int insert(OrgnInfoDO orgnInfoDO);

    /**
     * 修改合作资金机构基本信息
     */
    int update(OrgnInfoDO orgnInfoDO);

    /**
     * 资金流入模块，分页查询合作机构基本信息列表
     * @param corpOrgId 合作机构id
     * @param corpOrgTyp 合作机构类型
     * @param pageBegin 分页开始
     * @param pageNum 每页最大数目
     */
    List<OrgnInfoDO> getOrgnInfoList(@Param("corpOrgId") String corpOrgId,
                                     @Param("corpOrgTyp") String corpOrgTyp,
                                     @Param("pageBegin") Integer pageBegin,
                                     @Param("pageNum") Integer pageNum);

    /**
     * 资金流入模块，查询合作机构基本信息列表总笔数
     * @param corpOrgId
     * @param corpOrgTyp
     */
    int getOrgnInfoListTotNum(@Param("corpOrgId") String corpOrgId,
                              @Param("corpOrgTyp") String corpOrgTyp);

    /**
     * 根据合作机构编号ID，查询机构基本信息
     */
    OrgnInfoDO getOrgnInfoByOrgId(@Param("corpOrgId") String corpOrgId);

    /**
     * 查询资金流入模块所有的合作机构基本信息
     */
    List<OrgnInfoDO> findAllOrgnInfo();

    /**
     * 根据机构编号，查询合作机构基本信息
     */
    OrgnInfoDO findOrgnInfo(@Param("corpOrgId") String corpOrgId);
}
