package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnRouteDO;
import com.hisun.tms.cpt.service.ICpiOrgnRouteService;
import com.hisun.tms.cpt.service.ICpoOrgnRouteService;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 资金流入模块：资金机构路由管理
 */
@Controller
@RequestMapping("/cpt/cpiorg/route")
public class CpiOrgnRouteController {

    @Resource
    private ICpiOrgnRouteService cpiOrgnRouteService;

    /**
     * 显示资金机构路由管理主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/route') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/cpiorg/route/route");
        return modelAndView;
    }

    /**
     * 查询资金流入模块所有的合作机构路由信息
     */
    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/route') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<OrgnRouteDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return cpiOrgnRouteService.findAll(input);
    }

    /**
     * 增加合作机构路由信息
     */
    @PostMapping(value = "add")
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/route') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(@RequestBody OrgnRouteDO orgnRouteDO) {
        return cpiOrgnRouteService.add(orgnRouteDO);
    }

    /**
     * 修改合作机构路由信息
     */
    @PostMapping(value = "modify")
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/route') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(@RequestBody OrgnRouteDO orgnRouteDO) {
        return cpiOrgnRouteService.modify(orgnRouteDO);
    }
}
