package com.hisun.tms.cpt.service.impl;

import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.cpt.common.CptConstants;
import com.hisun.tms.cpt.dao.cpi.ICardBinDao;
import com.hisun.tms.cpt.model.CardBinDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.ICpiCardBinService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资金流入模块：卡bin信息管理
 */
@Service("cpiCardBinService")
public class CpiCardBinServiceImpl implements ICpiCardBinService {
    private static final Logger logger = LoggerFactory.getLogger(CpiCardBinServiceImpl.class);

    @Resource
    private ICardBinDao cardBinDao;

    @Override
    public Map<String, String> add(CardBinDO cardBinDO) {
        Map<String, String> resultMap = new HashMap<>();
        String cardBin = cardBinDO.getCrdBin();
        CardBinDO cardBinDO1 = cardBinDao.getByCrdBin(cardBin);
        if(JudgeUtils.isNotNull(cardBinDO1)) {
            //卡bin信息已存在
            resultMap.put("msgCd", CptConstants.CRD_BIN_EXISTS);
            return resultMap;
        }

        //增加卡bin信息
        String binId = DateTimeUtils.formatLocalDateTime(LocalDateTime.now(), "yyyyMMddHHmmssSSS");
        cardBinDO.setBinId(binId);
        try {
            cardBinDO.setCreateTime(LocalDateTime.now());
            cardBinDO.setModifyTime(LocalDateTime.now());
            int num = cardBinDao.insert(cardBinDO);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
            logger.error("增加卡bin信息失败，异常为{}", e);
        }
        return resultMap;
    }

    @Override
    public Map<String, String> modify(CardBinDO cardBinDO) {
        Map<String, String> resultMap = new HashMap<>();
        String cardBin = cardBinDO.getCrdBin();
        CardBinDO cardBinDO1 = cardBinDao.getByCrdBin(cardBin);
        if(JudgeUtils.isNull(cardBinDO1)) {
            //卡bin信息不存在
            resultMap.put("msgCd", CptConstants.CRD_BIN_NOT_EXISTS);
            return resultMap;
        }

        //修改卡bin信息
        try {
            int num = cardBinDao.update(cardBinDO);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
            logger.error("修改卡bin信息失败，异常为{}", e);
        }
        return resultMap;
    }

    @Override
    public Map<String, String> delete(String binId) {
        Map<String, String> resultMap = new HashMap<>();
        try {
            int num = cardBinDao.delete(binId);
            if(num > 0) {
                resultMap.put("msgCd", CptConstants.CPT_SUCC);
            } else {
                resultMap.put("msgCd", CptConstants.CPT_FAIL);
            }
        } catch(Exception e) {
            resultMap.put("msgCd", CptConstants.CPT_FAIL);
            logger.error("删除卡bin信息失败，异常为{}", e);
        }
        return resultMap;
    }

    @Override
    public DataTablesOutput<CardBinDO> findAll(GenericParamInput input) {
        DataTablesOutput<CardBinDO> dataTablesOutput = new DataTablesOutput();
        List<CardBinDO> cardBinDOList = new ArrayList<>();

        //查询条件
        String crdBin = input.getExtra_search().get("crdBin");
        String capCorg = input.getExtra_search().get("capCorg");
        String crdAcTyp = input.getExtra_search().get("crdAcTyp");
        String crdLth = input.getExtra_search().get("crdLth");
        String belongingState = input.getExtra_search().get("belongingState");
        String ascriptionArea = input.getExtra_search().get("ascriptionArea");

        //从第几条记录开始获取数据，以及每页最大笔数
        Integer pageBegin = input.getStart();
        Integer pageNum = input.getLength();

        //根据条件查询得到记录的总笔数
        int totNum = 0;
        try {
            //分页查询订单列表
            cardBinDOList = cardBinDao.getCardBinList(crdBin, capCorg, crdAcTyp, crdLth, belongingState, ascriptionArea, pageBegin, pageNum);

            //查询满足条件的订单总笔数
            totNum = cardBinDao.getCardBinListTotNum(crdBin, capCorg, crdAcTyp, crdLth, belongingState, ascriptionArea);
        } catch (Exception e){
            logger.error("资金流入模块卡bin信息查询列表失败，异常为{}", e);
        }

        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(cardBinDOList)){
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(cardBinDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        return dataTablesOutput;
    }
}
