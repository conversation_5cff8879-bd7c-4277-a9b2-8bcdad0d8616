package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.RefundOrderDO;
import com.hisun.tms.cpt.model.WithdrawOrderDO;
import com.hisun.tms.cpt.service.IRefundService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 退款订单基本操作
 */
@Controller
@RequestMapping("/cpt/order/refund")
public class RefundController {
    private static final Logger logger = LoggerFactory.getLogger(RefundController.class);

    @Resource
    private IRefundService refundService;

    /**
     * 返回退款订单查询功能主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/order/refund') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/order/refund/refund");
        return modelAndView;
    }

    /**
     * 退款订单列表查询
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/order/refund') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<RefundOrderDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return refundService.findAll(input);
    }


}
