package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnInfoDO;
import com.hisun.tms.cpt.service.ICpiOrgnInfoService;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 资金流入模块：合作机构基本信息管理
 */
@Controller
@RequestMapping("/cpt/cpiorg/info")
public class CpiOrgnInfoController {
    @Resource
    private ICpiOrgnInfoService cpiOrgnInfoService;

    /**
     * 合作机构信息管理主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/info') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/cpiorg/info/info");
        return modelAndView;
    }

    /**
     * 分页查询资金流入模块所有的合作机构基本信息
     */
    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/info') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<OrgnInfoDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return cpiOrgnInfoService.findAll(input);
    }

    /**
     * 查询资金资金流入模块所有的合作机构基本信息
     */
    @PostMapping(value = "findAllOrgnInfo")
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/info') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public List<OrgnInfoDO> findAllOrgnInfo() {
        return cpiOrgnInfoService.findAllOrgnInfo();
    }

    /**
     * 根据机构编号，查询合作机构基本信息
     */
    @PostMapping(value = "findOrgnInfo")
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/info') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public OrgnInfoDO findOrgnInfo(@RequestParam String corpOrgId) {
        return cpiOrgnInfoService.findOrgnInfo(corpOrgId);
    }

    /**
     * 增加合作机构基本信息
     */
    @PostMapping(value = "add")
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/info') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(@RequestBody OrgnInfoDO orgnInfoDO) {
        return cpiOrgnInfoService.add(orgnInfoDO);
    }

    /**
     * 修改合作资金机构信息
     */
    @PostMapping(value = "modify")
    @PreAuthorize("hasPermission('','/cptmgr/cpiorg/info') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(@RequestBody OrgnInfoDO orgnInfoDO) {
        return cpiOrgnInfoService.modify(orgnInfoDO);
    }

}
