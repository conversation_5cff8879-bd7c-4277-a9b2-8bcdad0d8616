package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.WithdrawOrderDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.List;
import java.util.Map;

/**
 * 运营系统-资金出入模块-付款订单查询功能
 */
public interface IWithdrawService {

    /**
     * 根据条件查询列表，分页
     */
    DataTablesOutput<WithdrawOrderDO> findAll(GenericParamInput input);

    /**
     * 根据条件查询列表，不分页
     */
    List<WithdrawOrderDO> findAllNew(GenericParamInput input);

    /**
     * 根据内部订单号，查询付款订单明细信息
     */
    WithdrawOrderDO findOrderInfo(String wcOrdNo);

    /**
     * 确认汇款成功，或退回
     */
    Map<String, String> payHandler(String wcOrdNo, String ordSts, String reason);

}
