/*
 * @ClassName CardProtJrnDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.tms.cpt.model;



import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class CardProtJrnDO extends BaseDO {
    /**
     * @Fields jrnNo 流水号
     */
    private String jrnNo;
    /**
     * @Fields txDt 签约日期
     */
    private LocalDate txDt;
    /**
     * @Fields txTm 签约时间
     */
    private LocalTime txTm;
    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields crdCorpOrg 合作机构号
     */
    private String crdCorpOrg;
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields userNm 用户名
     */
    private String userNm;
    /**
     * @Fields idTyp 证件类型
     */
    private String idTyp;
    /**
     * @Fields idNoEnc 加密证件号
     */
    private String idNoEnc;
    /**
     * @Fields crdNoEnc 加密卡号
     */
    private String crdNoEnc;
    /**
     * @Fields rutCorpOrg 路径机构号
     */
    private String rutCorpOrg;
    /**
     * @Fields bndFlg 绑定类型，P-预签约，S-绑定 C-解绑
     */
    private String bndFlg;
    /**
     * @Fields mblNo 手机号
     */
    private String mblNo;
    /**
     * @Fields crdAcTyp 卡类型，D：借记卡，C：贷记卡
     */
    private String crdAcTyp;
    /**
     * @Fields crdUsrNm 账户名
     */
    private String crdUsrNm;
    /**
     * @Fields crdCvv2Enc 加密的cvv2
     */
    private String crdCvv2Enc;
    /**
     * @Fields crdExpDtEnc 加密的有限期
     */
    private String crdExpDtEnc;
    /**
     * @Fields signAgrno 机构协议号
     */
    private String signAgrno;
    /**
     * @Fields orgJrnNo 机构返回流水号
     */
    private String orgJrnNo;
    /**
     * @Fields orgRspCd 机构返回码
     */
    private String orgRspCd;
    /**
     * @Fields orgRspMsg 机构返回信息
     */
    private String orgRspMsg;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public LocalDate getTxDt() {
        return txDt;
    }

    public void setTxDt(LocalDate txDt) {
        this.txDt = txDt;
    }

    public LocalTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalTime txTm) {
        this.txTm = txTm;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserNm() {
        return userNm;
    }

    public void setUserNm(String userNm) {
        this.userNm = userNm;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNoEnc() {
        return idNoEnc;
    }

    public void setIdNoEnc(String idNoEnc) {
        this.idNoEnc = idNoEnc;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getBndFlg() {
        return bndFlg;
    }

    public void setBndFlg(String bndFlg) {
        this.bndFlg = bndFlg;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getCrdCvv2Enc() {
        return crdCvv2Enc;
    }

    public void setCrdCvv2Enc(String crdCvv2Enc) {
        this.crdCvv2Enc = crdCvv2Enc;
    }

    public String getCrdExpDtEnc() {
        return crdExpDtEnc;
    }

    public void setCrdExpDtEnc(String crdExpDtEnc) {
        this.crdExpDtEnc = crdExpDtEnc;
    }

    public String getSignAgrno() {
        return signAgrno;
    }

    public void setSignAgrno(String signAgrno) {
        this.signAgrno = signAgrno;
    }

    public String getOrgJrnNo() {
        return orgJrnNo;
    }

    public void setOrgJrnNo(String orgJrnNo) {
        this.orgJrnNo = orgJrnNo;
    }

    public String getOrgRspCd() {
        return orgRspCd;
    }

    public void setOrgRspCd(String orgRspCd) {
        this.orgRspCd = orgRspCd;
    }

    public String getOrgRspMsg() {
        return orgRspMsg;
    }

    public void setOrgRspMsg(String orgRspMsg) {
        this.orgRspMsg = orgRspMsg;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}