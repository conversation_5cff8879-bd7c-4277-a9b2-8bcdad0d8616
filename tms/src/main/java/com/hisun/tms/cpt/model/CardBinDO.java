package com.hisun.tms.cpt.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 卡bin信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardBinDO implements Serializable {
    /**
     * @Fields binId 主键
     */
    private String binId;

    /**
     * @Fields crdBin 卡BIN
     */
    private String crdBin;

    /**
     * @Fields capCorg 资金机构
     */
    private String capCorg;

    /**
     * @Fields crdAcTyp 卡类型，D借记卡，C贷记卡
     */
    private String crdAcTyp;

    /**
     * @Fields crdLth 卡号长度
     */
    private Integer crdLth;

    /**
     * @Fields oprId 操作员
     */
    private String oprId;
    
    /**
     * @Fields cmmOperId 归属国家
     */
    private String belongingState;
	
    /**
     * @Fields cmmOperId 归属地区
     */
    private String ascriptionArea;

    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;

    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getBinId() {
        return binId;
    }

    public void setBinId(String binId) {
        this.binId = binId;
    }

    public String getCrdBin() {
        return crdBin;
    }

    public void setCrdBin(String crdBin) {
        this.crdBin = crdBin;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public Integer getCrdLth() {
        return crdLth;
    }

    public void setCrdLth(Integer crdLth) {
        this.crdLth = crdLth;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

	public String getBelongingState() {
		return belongingState;
	}

	public void setBelongingState(String belongingState) {
		this.belongingState = belongingState;
	}

	public String getAscriptionArea() {
		return ascriptionArea;
	}

	public void setAscriptionArea(String ascriptionArea) {
		this.ascriptionArea = ascriptionArea;
	}

    public LocalDateTime getCreateTime() { return createTime; }

    public LocalDateTime getModifyTime() { return modifyTime; }

    public void setModifyTime(LocalDateTime modifyTime) { this.modifyTime = modifyTime; }

    public LocalDateTime getTmSmp() { return tmSmp; }

    public void setTmSmp(LocalDateTime tmSmp) { this.tmSmp = tmSmp; }

    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }

}
