package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.model.CardProtDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.List;
import java.util.Map;

/**
 * 用户银行卡信息管理
 */
public interface ICardService {
    /**
     * 用户已绑定的银行卡信息查询
     * @param input
     * @return
     */
    DataTablesOutput<CardProtDO> findCardProtList(GenericParamInput input);

    /**
     * 根据内部协议号，查询具体的银行卡签约信息
     * @param agrNo
     * @return
     */
    CardProtDO getCardProtInfo(String agrNo);

    /**
     * 用户解除绑定的银行卡
     * 0-我方解绑，不调用银行接口解绑，平台更新用户签约信息失效
     * 1-双方解绑，调用银行接口解绑，更新用户签约信息失效
     */
    Map<String, String> unbindCard(String agrNo, String agrDirect);
}
