package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.model.FeeOrderQueryDO;
import com.hisun.tms.cpt.model.QueryFindInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 手续费订单查询服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
public interface CptFeeOrderQueryService {
    
    /**
     * 分页查询手续费订单
     * 
     * @param input 查询条件
     * @return 分页结果
     */
    DataTablesOutput<FeeOrderQueryDO> findAll(QueryFindInput input);

    /**
     * 根据订单号获取单个手续费订单
     * 
     * @param orderNo 订单号
     * @return 手续费订单对象
     */
    Object get(String orderNo);

    /**
     * 新增手续费订单
     * 
     * @param feeOrderQueryDO 手续费订单对象
     * @return 结果代码
     */
    String add(FeeOrderQueryDO feeOrderQueryDO);

    /**
     * 更新手续费订单
     * 
     * @param feeOrderQueryDO 手续费订单对象
     * @return 结果代码
     */
    String update(FeeOrderQueryDO feeOrderQueryDO);

    /**
     * 删除手续费订单
     * 
     * @param orderNo 订单号
     * @return 结果代码
     */
    String delete(String orderNo);
}