/*
 * @ClassName SettleDetailDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-11 10:17:02
 */
package com.hisun.tms.cpt.model;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 结算明细查询
 */
public class SettleDetailDO extends BaseDO {
    /**
     * @Fields jrnNo 平台内部流水号
     */
    private String jrnNo;
    /**
     * @Fields updateDate 更新日期
     */
    private LocalDate updateDate;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalTime updateTime;
    /**
     * @Fields payBatchNo 外部机构付款批次号
     */
    private String payBatchNo;
    /**
     * @Fields settleDate 外部机构结算日期
     */
    private LocalDate settleDate;
    /**
     * @Fields settleDatetime 结算日期时间
     */
    private LocalDateTime settleDatetime;
    /**
     * @Fields startDate 交易开始日期
     */
    private LocalDate startDate;
    /**
     * @Fields endDate 交易结束日期
     */
    private LocalDate endDate;
    /**
     * @Fields settleFee 结算金额，以元为单位
     */
    private BigDecimal settleFee;
    /**
     * @Fields unsettleFee 未结算金额，以元为单位
     */
    private BigDecimal unsettleFee;
    /**
     * @Fields settleFeeType 结算币种
     */
    private String settleFeeType;
    /**
     * @Fields payFee 支付金额，以元为单位
     */
    private BigDecimal payFee;
    /**
     * @Fields refundFee 退款金额，以元为单位
     */
    private BigDecimal refundFee;
    /**
     * @Fields payNetFee 支付净额，以元为单位
     */
    private BigDecimal payNetFee;
    /**
     * @Fields poundageFee 手续费金额，以元为单位
     */
    private BigDecimal poundageFee;
    /**
     * @Fields totSettleFee 截止当前结算日期已结算总金额，以元为单位
     */
    private BigDecimal totSettleFee;
    /**
     * @Fields totUnsettleFee 截止当前结算日期未结算总金额，以元为单位
     */
    private BigDecimal totUnsettleFee;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields appid 公众号id
     */
    private String appid;
    /**
     * @Fields mchId 平台在外部机构的商户号
     */
    private String mchId;
    /**
     * @Fields subMchId 平台商家在外部机构的字商户号
     */
    private String subMchId;
    /**
     * @Fields stlFlg 结算标志1-已结算；2-未结算
     */
    private String stlFlg;
    /**
     * @Fields rutCorg 外部机构（路由机构）
     */
    private String rutCorg;

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public LocalDate getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDate updateDate) {
        this.updateDate = updateDate;
    }

    public LocalTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getPayBatchNo() {
        return payBatchNo;
    }

    public void setPayBatchNo(String payBatchNo) {
        this.payBatchNo = payBatchNo;
    }

    public LocalDate getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(LocalDate settleDate) {
        this.settleDate = settleDate;
    }

    public LocalDateTime getSettleDatetime() {
        return settleDatetime;
    }

    public void setSettleDatetime(LocalDateTime settleDatetime) {
        this.settleDatetime = settleDatetime;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getSettleFee() {
        return settleFee;
    }

    public void setSettleFee(BigDecimal settleFee) {
        this.settleFee = settleFee;
    }

    public BigDecimal getUnsettleFee() {
        return unsettleFee;
    }

    public void setUnsettleFee(BigDecimal unsettleFee) {
        this.unsettleFee = unsettleFee;
    }

    public String getSettleFeeType() {
        return settleFeeType;
    }

    public void setSettleFeeType(String settleFeeType) {
        this.settleFeeType = settleFeeType;
    }

    public BigDecimal getPayFee() {
        return payFee;
    }

    public void setPayFee(BigDecimal payFee) {
        this.payFee = payFee;
    }

    public BigDecimal getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    public BigDecimal getPayNetFee() {
        return payNetFee;
    }

    public void setPayNetFee(BigDecimal payNetFee) {
        this.payNetFee = payNetFee;
    }

    public BigDecimal getPoundageFee() {
        return poundageFee;
    }

    public void setPoundageFee(BigDecimal poundageFee) {
        this.poundageFee = poundageFee;
    }

    public BigDecimal getTotSettleFee() {
        return totSettleFee;
    }

    public void setTotSettleFee(BigDecimal totSettleFee) {
        this.totSettleFee = totSettleFee;
    }

    public BigDecimal getTotUnsettleFee() {
        return totUnsettleFee;
    }

    public void setTotUnsettleFee(BigDecimal totUnsettleFee) {
        this.totUnsettleFee = totUnsettleFee;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getSubMchId() {
        return subMchId;
    }

    public void setSubMchId(String subMchId) {
        this.subMchId = subMchId;
    }

    public String getStlFlg() {
        return stlFlg;
    }

    public void setStlFlg(String stlFlg) {
        this.stlFlg = stlFlg;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }
}