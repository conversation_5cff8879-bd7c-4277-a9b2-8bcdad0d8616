package com.hisun.tms.cpt.controller;

import com.hisun.lemon.csh.dto.order.OrderDTO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.RemitOrderDO;
import com.hisun.tms.cpt.service.ITransferService;
import com.hisun.tms.csh.entity.OrderDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 转账订单基本操作，包括快捷订单、网银(微信、支付宝、翼支付)订单、汇款订单
 */
@Controller
@RequestMapping("/cpt/order/transfer")
public class TransferController {
    private static final Logger logger = LoggerFactory.getLogger(TransferController.class);

    @Resource
    private ITransferService transferService;

    /**
     * 返回转账订单查询功能主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/order/transfer') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/order/transfer/transfer");
        return modelAndView;
    }

    /**
     * 转账订单列表查询
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/order/transfer') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<OrderDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return transferService.findAll(input);
    }

    /**
     * 根据转账订单号，查询转账订单详细信息
     */
    @PostMapping(value = "/findOrderInfo")
    @PreAuthorize("hasPermission('','/cptmgr/order/transfer') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public OrderDO findOrderInfo(@RequestParam String orderNo) {
        logger.debug("tms transferController.findOrderInfo() orderNo = " + orderNo);
        return transferService.getOrderDetail(orderNo);
    }

    /**
     * 审核转账订单
     */
    @PostMapping(value = "/auditHandler")
    @PreAuthorize("hasPermission('','/cptmgr/order/transfer') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> remitHandler(@RequestParam String orderNo, @RequestParam String reason, @RequestParam String ordSts) {
        logger.debug("tms transferController.auditHandler() orderNo = " + orderNo + "; ordSts = " + ordSts + "; reason = " + reason);
        return transferService.remitHandler(orderNo, ordSts, reason);
    }


}
