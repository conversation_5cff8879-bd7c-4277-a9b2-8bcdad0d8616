package com.hisun.tms.cpt.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

/**
 * 合作机构基本信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrgnInfoDO implements Serializable {
    /**
     * 主键
     */
    private String orgInfId;

    /**
     * 合作机构编号
     */
    private String corpOrgId;

    /**
     * 合作机构名称
     */
    private String corpOrgNm;

    /**
     * 合作机构简称
     */
    private String corpOrgSnm;

    /**
     * 合作机构类型，0银行，1非银行
     */
    private String corpOrgTyp;

    /**
     * 合作机构账户名
     */
    private String corpAccNm;

    /**
     * 合作机构卡号
     */
    private String corpAccNo;

    /**
     * 备注
     */
    private String rmk;

    /**
     * @Fields creOprId 创建柜员ID
     */
    private String creOprId;

    /**
     * @Fields updOprId 修改柜员ID
     */
    private String updOprId;

    public String getOrgInfId() {
        return orgInfId;
    }

    public void setOrgInfId(String orgInfId) {
        this.orgInfId = orgInfId;
    }

    public String getCorpOrgId() {
        return corpOrgId;
    }

    public void setCorpOrgId(String corpOrgId) {
        this.corpOrgId = corpOrgId;
    }

    public String getCorpOrgNm() {
        return corpOrgNm;
    }

    public void setCorpOrgNm(String corpOrgNm) {
        this.corpOrgNm = corpOrgNm;
    }

    public String getCorpOrgSnm() {
        return corpOrgSnm;
    }

    public void setCorpOrgSnm(String corpOrgSnm) {
        this.corpOrgSnm = corpOrgSnm;
    }

    public String getCorpOrgTyp() {
        return corpOrgTyp;
    }

    public void setCorpOrgTyp(String corpOrgTyp) {
        this.corpOrgTyp = corpOrgTyp;
    }

    public String getCorpAccNm() {
        return corpAccNm;
    }

    public void setCorpAccNm(String corpAccNm) {
        this.corpAccNm = corpAccNm;
    }

    public String getCorpAccNo() {
        return corpAccNo;
    }

    public void setCorpAccNo(String corpAccNo) {
        this.corpAccNo = corpAccNo;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getCreOprId() {
        return creOprId;
    }

    public void setCreOprId(String creOprId) {
        this.creOprId = creOprId;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }
}
