package com.hisun.tms.cpt.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 运营系统-资金出入模块-退款订单查询功能，列表查询DO
 */
public class RefundOrderDO {
    /**
     * 用户号/商户号
     */
    private String userId;

    /**
     * 退款申请日期
     */
    private LocalDate ordDt;

    /**
     * 退款申请时间
     */
    private LocalTime ordTm;

    /**
     * 退款完成日期
     */
    private LocalDate ordSuccDt;

    /**
     * 退款类型
     */
    private String corpBusSubTyp;

    /**
     * 退款银行
     */
    private String rutCorpOrg;

    /**
     * 退款金额
     */
    private BigDecimal ordAmt;

    /**
     * 退款状态
     */
    private String ordSts;

    /**
     * 充值订单号
     */
    private String fndOrdNo;

    /**
     * 退款订单号
     */
    private String rfdOrdNo;

    /**
     * 银行流水号
     */
    private String orgJrnNo;

    /**
     * 银行处理结果
     */
    private String orgRspMsg;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public LocalDate getOrdSuccDt() {
        return ordSuccDt;
    }

    public void setOrdSuccDt(LocalDate ordSuccDt) {
        this.ordSuccDt = ordSuccDt;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getFndOrdNo() {
        return fndOrdNo;
    }

    public void setFndOrdNo(String fndOrdNo) {
        this.fndOrdNo = fndOrdNo;
    }

    public String getRfdOrdNo() {
        return rfdOrdNo;
    }

    public void setRfdOrdNo(String rfdOrdNo) {
        this.rfdOrdNo = rfdOrdNo;
    }

    public String getOrgJrnNo() {
        return orgJrnNo;
    }

    public void setOrgJrnNo(String orgJrnNo) {
        this.orgJrnNo = orgJrnNo;
    }

    public String getOrgRspMsg() {
        return orgRspMsg;
    }

    public void setOrgRspMsg(String orgRspMsg) {
        this.orgRspMsg = orgRspMsg;
    }
}
