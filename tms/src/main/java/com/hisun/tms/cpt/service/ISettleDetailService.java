package com.hisun.tms.cpt.service;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.SettleDetailDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * 运营系统-清结算模块-结算明细信息查询功能
 */
public interface ISettleDetailService {

    /**
     * 根据条件查询列表
     */
    DataTablesOutput<SettleDetailDO> findAll(GenericParamInput input);

    /**
     * 查询银行商户结算信息
     */
    Map<String, String> refresh(String stlFlg, String rutCorg, String startDate, String endDate);
}
