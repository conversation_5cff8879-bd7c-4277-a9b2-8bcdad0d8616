package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.OrgnBusiDO;
import com.hisun.tms.cpt.service.ICpoOrgnBusiService;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 资金流出模块：合作机构业务管理
 */
@Controller
@RequestMapping("/cpt/cpoorg/busi")
public class CpoOrgnBusiController {
    @Resource
    private ICpoOrgnBusiService cpoOrgnBusiService;

    /**
     * 显示合作机构业务管理主页面
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/cpoorg/busi') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/cpoorg/busi/busi");
        return modelAndView;
    }

    /**
     * 查询资金流出模块所有的合作机构业务信息
     */
    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cptmgr/cpoorg/busi') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<OrgnBusiDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return cpoOrgnBusiService.findAll(input);
    }

    /**
     * 增加合作机构业务信息
     */
    @PostMapping(value = "add")
    @PreAuthorize("hasPermission('','/cptmgr/cpoorg/busi') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(@RequestBody OrgnBusiDO orgnBusiDO) {
        return cpoOrgnBusiService.add(orgnBusiDO);
    }

    /**
     * 修改合作机构业务信息
     */
    @PostMapping(value = "modify")
    @PreAuthorize("hasPermission('','/cptmgr/cpoorg/busi') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(@RequestBody OrgnBusiDO orgnBusiDO) {
        return cpoOrgnBusiService.modify(orgnBusiDO);
    }

}
