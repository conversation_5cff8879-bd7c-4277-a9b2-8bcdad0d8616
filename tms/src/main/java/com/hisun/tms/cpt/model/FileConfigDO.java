package com.hisun.tms.cpt.model;

import java.util.List;

/**
 * 文件相关参数
 */
public class FileConfigDO {
    /**
     * 文件名称，没有后缀
     */
    private String fileName;

    /**
     * 文件后缀，默认后缀为 .xlsx
     */
    private String fileSuffix;

    /**
     * 文件存放的相对路径，在当前项目的tmp目录下
     */
    private String relativePath;

    /**
     * 文件列标题名称，与页面列表标题顺序一致
     */
    private String[] titleNames;

    /**
     * 文件列对应的字段名，与页面列表标题顺序一致
     */
    private String[] columnNames;

    /**
     * 文件列对应字段的get方法名，与页面列表标题顺序一致
     */
    private String[] methodNames;

    /**
     * 需导出的数据，对应的是页面上的列表
     */
    private List<Object> objectList;

    /**
     * objectList中的每个元素实际需映射到的 DO类
     */
    private String objectClass;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileSuffix() {
        return fileSuffix;
    }

    public void setFileSuffix(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    public String getRelativePath() {
        return relativePath;
    }

    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }

    public String[] getTitleNames() {
        return titleNames;
    }

    public void setTitleNames(String[] titleNames) {
        this.titleNames = titleNames;
    }

    public String[] getColumnNames() {
        return columnNames;
    }

    public void setColumnNames(String[] columnNames) {
        this.columnNames = columnNames;
    }

    public List<Object> getObjectList() {
        return objectList;
    }

    public void setObjectList(List<Object> objectList) {
        this.objectList = objectList;
    }

    public String getObjectClass() {
        return objectClass;
    }

    public void setObjectClass(String objectClass) {
        this.objectClass = objectClass;
    }

    public String[] getMethodNames() {
        return methodNames;
    }

    public void setMethodNames(String[] methodNames) {
        this.methodNames = methodNames;
    }
}
