package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.ITransferService;
import com.hisun.tms.csh.entity.OrderDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 数币付款订单基本操作
 */
@Controller
@RequestMapping("/cpt/dcorder/withdraw")
public class DcWithdrawController {
    private static final Logger logger = LoggerFactory.getLogger(DcWithdrawController.class);

    @Resource
    private ITransferService transferService;

    /**
     * 返回数币付款订单查询功能主页面
     * 
     * @return
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/withdraw') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/dcorder/withdraw/withdraw");
        return modelAndView;
    }

    /**
     * 数币付款订单列表查询
     * 
     * @param input
     * @return
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/withdraw') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<OrderDO> findAll(@Valid @RequestBody GenericParamInput input) {
        return transferService.findAllWithdraw(input);
    }

    /**
     * 根据数币付款订单号，查询付款订单详细信息
     * 
     * @param wcOrdNo
     * @return
     */
    @PostMapping(value = "/findOrderInfo")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/withdraw') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public OrderDO findOrderInfo(@RequestParam String wcOrdNo) {
        logger.debug("tms DcWithdrawController.findOrderInfo() wcOrdNo = " + wcOrdNo);
        return transferService.getOrderDetail(wcOrdNo);
    }

    /**
     * 审核数币付款订单
     */
    @PostMapping(value = "/payHandler")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/withdraw') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> payHandler(@RequestParam String wcOrdNo, @RequestParam String ordSts,
            @RequestParam String reason) {
        logger.debug("tms DcWithdrawController.payHandler() wcOrdNo = " + wcOrdNo + "; ordSts = " + ordSts
                + "; reason = " + reason);
        return transferService.remitHandler(wcOrdNo, ordSts, reason);
    }

    /**
     * 批量处理订单，或退回
     */
    @PostMapping(value = "/payHandlerBatch")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/withdraw') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> payHandlerBach(@RequestParam(value = "wcOrdNos[]") String[] wcOrdNos,
            @RequestParam String ordSts, @RequestParam String reason) {
        logger.debug(
                "tms DcWithdrawController.payHandler() wcOrdNo = " + "; ordSts = " + ordSts + "; reason = " + reason);
        Map<String, String> map = new HashMap<String, String>();
        int t = 0;
        for (String wcOrdNo : wcOrdNos) {
            Map<String, String> rs = transferService.remitHandler(wcOrdNo, ordSts, reason);
            if ("CPO00000".equals(rs.get("msgCd"))) {
                t += 1;
            }
        }
        if (t == wcOrdNos.length) {
            map.put("msgCd", "CPO00000");
        } else if (t == 0) {
            map.put("msgCd", "0");
        } else {
            // 部分成功
            map.put("msgCd", "1");
            map.put("successNum", t + "");
        }
        return map;
    }
}