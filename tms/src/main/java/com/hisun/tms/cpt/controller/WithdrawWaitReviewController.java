package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.WithdrawOrderDO;
import com.hisun.tms.cpt.service.IWithdrawService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 付款订单基本操作
 */
@Controller
@RequestMapping("/cpt/order/withdrawWaitReview")
public class WithdrawWaitReviewController {
    private static final Logger logger = LoggerFactory.getLogger(WithdrawWaitReviewController.class);

    /**
     * 返回UI首页
     * @return
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/order/withdrawWaitReview') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantsList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/order/withdrawWaitReview/withdrawWaitReview");
        return modelAndView;
    }

}
