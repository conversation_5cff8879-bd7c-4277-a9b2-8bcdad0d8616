package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.model.FundOrderDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.model.RemitOrderDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * 运营系统-资金出入模块-退款订单查询功能
 */
public interface IFundService {

    /**
     * 根据条件查询充值订单列表
     */
    DataTablesOutput<FundOrderDO> findAll(GenericParamInput input);


    /**
     * 根据充值订单号，查询汇款订单详细信息
     */
    RemitOrderDO getRemitOrderDetail(String fndOrdNo);

    /**
     * 汇款充值确认，或退回
     */
    Map<String, String> remitHandler(String fndOrdNo, String ordSts, String reason);
}
