package com.hisun.tms.chk.service;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.chk.model.ChkControl;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface ChkControlService {

    DataTablesOutput<ChkControl> findAll(GenericParamInput input);

    Map<String,String> modify(String chkBatNo, String chkFilSts, String rmk);
}