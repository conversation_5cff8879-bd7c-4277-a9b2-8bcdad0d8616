package com.hisun.tms.chk.service.impl;

import com.google.common.collect.Collections2;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.chk.model.ChkControl;
import com.hisun.tms.chk.repository.DatatablesChkControlRepository;
import com.hisun.tms.chk.repository.ChkControlRepository;
import com.hisun.tms.chk.service.ChkControlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service
public class ChkControlServiceImpl implements ChkControlService {

    private static final Logger logger = LoggerFactory.getLogger(ChkControlServiceImpl.class);

    @Resource
    private DatatablesChkControlRepository datatablesChkControlRepository;
    @Resource
    private ChkControlRepository chkControlRepository;

    @Override
    @Transactional("chkTransactionManager")
    public DataTablesOutput<ChkControl> findAll(GenericParamInput input) {

        String mainNo = input.getExtra_search().get("mainNo");
        String oppoNo = input.getExtra_search().get("oppoNo");
        String chkBusTyp = input.getExtra_search().get("chkBusTyp");
        String chkFilSts = input.getExtra_search().get("chkFilSts");
        String beginDate = input.getExtra_search().get("beginDate");
        String endDate = input.getExtra_search().get("endDate");

        input.getColumn("mainNo").setSearchValue(mainNo);
        input.getColumn("oppoNo").setSearchValue(oppoNo);
        input.getColumn("chkBusTyp").setSearchValue(chkBusTyp);
        input.getColumn("chkFilSts").setSearchValue(chkFilSts);

        Specification<ChkControl> specification = (root, query, cb) ->  {
            List<Predicate> list = new ArrayList<>();
            if(StringUtils.isNotEmpty(beginDate)){
                Predicate p1 = cb.greaterThanOrEqualTo(root.get("chkFilDt").as(String.class), beginDate);
                list.add(p1);
            }
            if(StringUtils.isNotEmpty(endDate)){
                Predicate p2 = cb.lessThanOrEqualTo(root.get("chkFilDt").as(String.class), endDate);
                list.add(p2);
            }
            Predicate[] p = new Predicate[list.size()];
            query.where(cb.and(list.toArray(p)));
            return query.getRestriction();
        };

        DataTablesOutput<ChkControl> dataTablesOutput = datatablesChkControlRepository.findAll(input,specification);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    @Transactional("chkTransactionManager")
    public Map<String,String> modify(String chkBatNo, String chkFilSts, String rmk) {
        Map<String,String> map = new HashMap<>();
        try {
            ChkControl chkControl = datatablesChkControlRepository.findOne(chkBatNo);
            chkControl.setChkFilSts(chkFilSts);
            chkControl.setRmk(rmk);
            datatablesChkControlRepository.save(chkControl);
            map.put("msgCd","TMS00000");
        }catch (Exception e){
            logger.error("更新数据库失败：", e);
            map.put("msgCd","TMS99999");
        }
        return map;
    }

}
