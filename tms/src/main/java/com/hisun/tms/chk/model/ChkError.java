package com.hisun.tms.chk.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

/**
 * User : Rui
 * Date : 2017/8/22
 * Time : 10:40
 **/
@Entity
@Table(name = "chk_error", schema = "seatelpay_chk", catalog = "")
public class ChkError {
    private String chkErId;
    private String mainNo;
    private String oppoNo;
    private String chkBusTyp;
    private String chkBusSubTyp;
    private String errKeyId;
    private Date chkErrDt;
    private Time chkErrTm;
    private String chkBatNo;
    private String splAbleFlg;
    private String canAbleFlg;
    private String chkErrTyp;
    private String errSts;
    private BigDecimal mainTxAmt;
    private BigDecimal oppoTxAmt;
    private String oprId;
    private String rmk;
    private Timestamp createTime;
    private Timestamp modifyTime;
    private Timestamp tmSmp;

    @Id
    @Column(name = "CHK_ER_ID")
    public String getChkErId() {
        return chkErId;
    }

    public void setChkErId(String chkErId) {
        this.chkErId = chkErId;
    }

    @Basic
    @Column(name = "MAIN_NO")
    public String getMainNo() {
        return mainNo;
    }

    public void setMainNo(String mainNo) {
        this.mainNo = mainNo;
    }

    @Basic
    @Column(name = "OPPO_NO")
    public String getOppoNo() {
        return oppoNo;
    }

    public void setOppoNo(String oppoNo) {
        this.oppoNo = oppoNo;
    }

    @Basic
    @Column(name = "CHK_BUS_TYP")
    public String getChkBusTyp() {
        return chkBusTyp;
    }

    public void setChkBusTyp(String chkBusTyp) {
        this.chkBusTyp = chkBusTyp;
    }

    @Basic
    @Column(name = "CHK_BUS_SUB_TYP")
    public String getChkBusSubTyp() {
        return chkBusSubTyp;
    }

    public void setChkBusSubTyp(String chkBusSubTyp) {
        this.chkBusSubTyp = chkBusSubTyp;
    }

    @Basic
    @Column(name = "ERR_KEY_ID")
    public String getErrKeyId() {
        return errKeyId;
    }

    public void setErrKeyId(String errKeyId) {
        this.errKeyId = errKeyId;
    }

    @Basic
    @Column(name = "CHK_ERR_DT")
    public Date getChkErrDt() {
        return chkErrDt;
    }

    public void setChkErrDt(Date chkErrDt) {
        this.chkErrDt = chkErrDt;
    }

    @Basic
    @Column(name = "CHK_ERR_TM")
    public Time getChkErrTm() {
        return chkErrTm;
    }

    public void setChkErrTm(Time chkErrTm) {
        this.chkErrTm = chkErrTm;
    }

    @Basic
    @Column(name = "CHK_BAT_NO")
    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    @Basic
    @Column(name = "SPL_ABLE_FLG")
    public String getSplAbleFlg() {
        return splAbleFlg;
    }

    public void setSplAbleFlg(String splAbleFlg) {
        this.splAbleFlg = splAbleFlg;
    }

    @Basic
    @Column(name = "CAN_ABLE_FLG")
    public String getCanAbleFlg() {
        return canAbleFlg;
    }

    public void setCanAbleFlg(String canAbleFlg) {
        this.canAbleFlg = canAbleFlg;
    }

    @Basic
    @Column(name = "CHK_ERR_TYP")
    public String getChkErrTyp() {
        return chkErrTyp;
    }

    public void setChkErrTyp(String chkErrTyp) {
        this.chkErrTyp = chkErrTyp;
    }

    @Basic
    @Column(name = "ERR_STS")
    public String getErrSts() {
        return errSts;
    }

    public void setErrSts(String errSts) {
        this.errSts = errSts;
    }

    @Basic
    @Column(name = "MAIN_TX_AMT")
    public BigDecimal getMainTxAmt() {
        return mainTxAmt;
    }

    public void setMainTxAmt(BigDecimal mainTxAmt) {
        this.mainTxAmt = mainTxAmt;
    }

    @Basic
    @Column(name = "OPPO_TX_AMT")
    public BigDecimal getOppoTxAmt() {
        return oppoTxAmt;
    }

    public void setOppoTxAmt(BigDecimal oppoTxAmt) {
        this.oppoTxAmt = oppoTxAmt;
    }

    @Basic
    @Column(name = "OPR_ID")
    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    @Basic
    @Column(name = "RMK")
    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    @Basic
    @Column(name = "CREATE_TIME")
    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "MODIFY_TIME")
    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Basic
    @Column(name = "TM_SMP")
    public Timestamp getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(Timestamp tmSmp) {
        this.tmSmp = tmSmp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ChkError that = (ChkError) o;

        if (chkErId != null ? !chkErId.equals(that.chkErId) : that.chkErId != null) return false;
        if (mainNo != null ? !mainNo.equals(that.mainNo) : that.mainNo != null) return false;
        if (oppoNo != null ? !oppoNo.equals(that.oppoNo) : that.oppoNo != null) return false;
        if (chkBusTyp != null ? !chkBusTyp.equals(that.chkBusTyp) : that.chkBusTyp != null) return false;
        if (chkBusSubTyp != null ? !chkBusSubTyp.equals(that.chkBusSubTyp) : that.chkBusSubTyp != null) return false;
        if (errKeyId != null ? !errKeyId.equals(that.errKeyId) : that.errKeyId != null) return false;
        if (chkErrDt != null ? !chkErrDt.equals(that.chkErrDt) : that.chkErrDt != null) return false;
        if (chkErrTm != null ? !chkErrTm.equals(that.chkErrTm) : that.chkErrTm != null) return false;
        if (chkBatNo != null ? !chkBatNo.equals(that.chkBatNo) : that.chkBatNo != null) return false;
        if (splAbleFlg != null ? !splAbleFlg.equals(that.splAbleFlg) : that.splAbleFlg != null) return false;
        if (canAbleFlg != null ? !canAbleFlg.equals(that.canAbleFlg) : that.canAbleFlg != null) return false;
        if (chkErrTyp != null ? !chkErrTyp.equals(that.chkErrTyp) : that.chkErrTyp != null) return false;
        if (errSts != null ? !errSts.equals(that.errSts) : that.errSts != null) return false;
        if (mainTxAmt != null ? !mainTxAmt.equals(that.mainTxAmt) : that.mainTxAmt != null) return false;
        if (oppoTxAmt != null ? !oppoTxAmt.equals(that.oppoTxAmt) : that.oppoTxAmt != null) return false;
        if (oprId != null ? !oprId.equals(that.oprId) : that.oprId != null) return false;
        if (rmk != null ? !rmk.equals(that.rmk) : that.rmk != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (modifyTime != null ? !modifyTime.equals(that.modifyTime) : that.modifyTime != null) return false;
        if (tmSmp != null ? !tmSmp.equals(that.tmSmp) : that.tmSmp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = chkErId != null ? chkErId.hashCode() : 0;
        result = 31 * result + (mainNo != null ? mainNo.hashCode() : 0);
        result = 31 * result + (oppoNo != null ? oppoNo.hashCode() : 0);
        result = 31 * result + (chkBusTyp != null ? chkBusTyp.hashCode() : 0);
        result = 31 * result + (chkBusSubTyp != null ? chkBusSubTyp.hashCode() : 0);
        result = 31 * result + (errKeyId != null ? errKeyId.hashCode() : 0);
        result = 31 * result + (chkErrDt != null ? chkErrDt.hashCode() : 0);
        result = 31 * result + (chkErrTm != null ? chkErrTm.hashCode() : 0);
        result = 31 * result + (chkBatNo != null ? chkBatNo.hashCode() : 0);
        result = 31 * result + (splAbleFlg != null ? splAbleFlg.hashCode() : 0);
        result = 31 * result + (canAbleFlg != null ? canAbleFlg.hashCode() : 0);
        result = 31 * result + (chkErrTyp != null ? chkErrTyp.hashCode() : 0);
        result = 31 * result + (errSts != null ? errSts.hashCode() : 0);
        result = 31 * result + (mainTxAmt != null ? mainTxAmt.hashCode() : 0);
        result = 31 * result + (oppoTxAmt != null ? oppoTxAmt.hashCode() : 0);
        result = 31 * result + (oprId != null ? oprId.hashCode() : 0);
        result = 31 * result + (rmk != null ? rmk.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (modifyTime != null ? modifyTime.hashCode() : 0);
        result = 31 * result + (tmSmp != null ? tmSmp.hashCode() : 0);
        return result;
    }
}
