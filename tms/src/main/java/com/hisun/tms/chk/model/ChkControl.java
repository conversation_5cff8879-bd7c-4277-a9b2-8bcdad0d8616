package com.hisun.tms.chk.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

/**
 * User : Rui
 * Date : 2017/8/22
 * Time : 10:40
 **/
@Entity
@Table(name = "chk_control", schema = "seatelpay_chk", catalog = "")
public class ChkControl {
    private String chkBatNo;
    private String mainNo;
    private String oppoNo;
    private String chkBusTyp;
    private String chkBusSubTyp;
    private Integer priLvl;
    private Date chkFilDt;
    private String mainFilNm;
    private String oppoFilNm;
    private String chkFilSts;
    private Date mainRcvDt;
    private Date oppoRcvDt;
    private Time chkBegTm;
    private Time chkEndTm;
    private Date chkDt;
    private BigDecimal mainTotAmt;
    private int mainTotCnt;
    private BigDecimal oppoTotAmt;
    private int oppoTotCnt;
    private BigDecimal totMchAmt;
    private int totMchCnt;
    private int errTotCnt;
    private BigDecimal errTotAmt;
    private BigDecimal longAmt;
    private int longCnt;
    private BigDecimal shortAmt;
    private int shortCnt;
    private BigDecimal doubtAmt;
    private int doubtCnt;
    private BigDecimal dbtErrAmt;
    private int dbtErrCnt;
    private String oprId;
    private String rmk;
    private String mchNtfFlag;
    private int mchNftCount;
    private String mchNftResult;
    private Timestamp createTime;
    private Timestamp modifyTime;
    private Timestamp tmSmp;

    @Id
    @Column(name = "CHK_BAT_NO")
    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    @Basic
    @Column(name = "MAIN_NO")
    public String getMainNo() {
        return mainNo;
    }

    public void setMainNo(String mainNo) {
        this.mainNo = mainNo;
    }

    @Basic
    @Column(name = "OPPO_NO")
    public String getOppoNo() {
        return oppoNo;
    }

    public void setOppoNo(String oppoNo) {
        this.oppoNo = oppoNo;
    }

    @Basic
    @Column(name = "CHK_BUS_TYP")
    public String getChkBusTyp() {
        return chkBusTyp;
    }

    public void setChkBusTyp(String chkBusTyp) {
        this.chkBusTyp = chkBusTyp;
    }

    @Basic
    @Column(name = "CHK_BUS_SUB_TYP")
    public String getChkBusSubTyp() {
        return chkBusSubTyp;
    }

    public void setChkBusSubTyp(String chkBusSubTyp) {
        this.chkBusSubTyp = chkBusSubTyp;
    }

    @Basic
    @Column(name = "PRI_LVL")
    public Integer getPriLvl() {
        return priLvl;
    }

    public void setPriLvl(Integer priLvl) {
        this.priLvl = priLvl;
    }

    @Basic
    @Column(name = "CHK_FIL_DT")
    public Date getChkFilDt() {
        return chkFilDt;
    }

    public void setChkFilDt(Date chkFilDt) {
        this.chkFilDt = chkFilDt;
    }

    @Basic
    @Column(name = "MAIN_FIL_NM")
    public String getMainFilNm() {
        return mainFilNm;
    }

    public void setMainFilNm(String mainFilNm) {
        this.mainFilNm = mainFilNm;
    }

    @Basic
    @Column(name = "OPPO_FIL_NM")
    public String getOppoFilNm() {
        return oppoFilNm;
    }

    public void setOppoFilNm(String oppoFilNm) {
        this.oppoFilNm = oppoFilNm;
    }

    @Basic
    @Column(name = "CHK_FIL_STS")
    public String getChkFilSts() {
        return chkFilSts;
    }

    public void setChkFilSts(String chkFilSts) {
        this.chkFilSts = chkFilSts;
    }

    @Basic
    @Column(name = "MAIN_RCV_DT")
    public Date getMainRcvDt() {
        return mainRcvDt;
    }

    public void setMainRcvDt(Date mainRcvDt) {
        this.mainRcvDt = mainRcvDt;
    }

    @Basic
    @Column(name = "OPPO_RCV_DT")
    public Date getOppoRcvDt() {
        return oppoRcvDt;
    }

    public void setOppoRcvDt(Date oppoRcvDt) {
        this.oppoRcvDt = oppoRcvDt;
    }

    @Basic
    @Column(name = "CHK_BEG_TM")
    public Time getChkBegTm() {
        return chkBegTm;
    }

    public void setChkBegTm(Time chkBegTm) {
        this.chkBegTm = chkBegTm;
    }

    @Basic
    @Column(name = "CHK_END_TM")
    public Time getChkEndTm() {
        return chkEndTm;
    }

    public void setChkEndTm(Time chkEndTm) {
        this.chkEndTm = chkEndTm;
    }

    @Basic
    @Column(name = "CHK_DT")
    public Date getChkDt() {
        return chkDt;
    }

    public void setChkDt(Date chkDt) {
        this.chkDt = chkDt;
    }

    @Basic
    @Column(name = "MAIN_TOT_AMT")
    public BigDecimal getMainTotAmt() {
        return mainTotAmt;
    }

    public void setMainTotAmt(BigDecimal mainTotAmt) {
        this.mainTotAmt = mainTotAmt;
    }

    @Basic
    @Column(name = "MAIN_TOT_CNT")
    public int getMainTotCnt() {
        return mainTotCnt;
    }

    public void setMainTotCnt(int mainTotCnt) {
        this.mainTotCnt = mainTotCnt;
    }

    @Basic
    @Column(name = "OPPO_TOT_AMT")
    public BigDecimal getOppoTotAmt() {
        return oppoTotAmt;
    }

    public void setOppoTotAmt(BigDecimal oppoTotAmt) {
        this.oppoTotAmt = oppoTotAmt;
    }

    @Basic
    @Column(name = "OPPO_TOT_CNT")
    public int getOppoTotCnt() {
        return oppoTotCnt;
    }

    public void setOppoTotCnt(int oppoTotCnt) {
        this.oppoTotCnt = oppoTotCnt;
    }

    @Basic
    @Column(name = "TOT_MCH_AMT")
    public BigDecimal getTotMchAmt() {
        return totMchAmt;
    }

    public void setTotMchAmt(BigDecimal totMchAmt) {
        this.totMchAmt = totMchAmt;
    }

    @Basic
    @Column(name = "TOT_MCH_CNT")
    public int getTotMchCnt() {
        return totMchCnt;
    }

    public void setTotMchCnt(int totMchCnt) {
        this.totMchCnt = totMchCnt;
    }

    @Basic
    @Column(name = "ERR_TOT_CNT")
    public int getErrTotCnt() {
        return errTotCnt;
    }

    public void setErrTotCnt(int errTotCnt) {
        this.errTotCnt = errTotCnt;
    }

    @Basic
    @Column(name = "ERR_TOT_AMT")
    public BigDecimal getErrTotAmt() {
        return errTotAmt;
    }

    public void setErrTotAmt(BigDecimal errTotAmt) {
        this.errTotAmt = errTotAmt;
    }

    @Basic
    @Column(name = "LONG_AMT")
    public BigDecimal getLongAmt() {
        return longAmt;
    }

    public void setLongAmt(BigDecimal longAmt) {
        this.longAmt = longAmt;
    }

    @Basic
    @Column(name = "LONG_CNT")
    public int getLongCnt() {
        return longCnt;
    }

    public void setLongCnt(int longCnt) {
        this.longCnt = longCnt;
    }

    @Basic
    @Column(name = "SHORT_AMT")
    public BigDecimal getShortAmt() {
        return shortAmt;
    }

    public void setShortAmt(BigDecimal shortAmt) {
        this.shortAmt = shortAmt;
    }

    @Basic
    @Column(name = "SHORT_CNT")
    public int getShortCnt() {
        return shortCnt;
    }

    public void setShortCnt(int shortCnt) {
        this.shortCnt = shortCnt;
    }

    @Basic
    @Column(name = "DOUBT_AMT")
    public BigDecimal getDoubtAmt() {
        return doubtAmt;
    }

    public void setDoubtAmt(BigDecimal doubtAmt) {
        this.doubtAmt = doubtAmt;
    }

    @Basic
    @Column(name = "DOUBT_CNT")
    public int getDoubtCnt() {
        return doubtCnt;
    }

    public void setDoubtCnt(int doubtCnt) {
        this.doubtCnt = doubtCnt;
    }

    @Basic
    @Column(name = "DBT_ERR_AMT")
    public BigDecimal getDbtErrAmt() {
        return dbtErrAmt;
    }

    public void setDbtErrAmt(BigDecimal dbtErrAmt) {
        this.dbtErrAmt = dbtErrAmt;
    }

    @Basic
    @Column(name = "DBT_ERR_CNT")
    public int getDbtErrCnt() {
        return dbtErrCnt;
    }

    public void setDbtErrCnt(int dbtErrCnt) {
        this.dbtErrCnt = dbtErrCnt;
    }

    @Basic
    @Column(name = "OPR_ID")
    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    @Basic
    @Column(name = "RMK")
    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    @Basic
    @Column(name = "MCH_NTF_FLAG")
    public String getMchNtfFlag() {
        return mchNtfFlag;
    }

    public void setMchNtfFlag(String mchNtfFlag) {
        this.mchNtfFlag = mchNtfFlag;
    }

    @Basic
    @Column(name = "MCH_NFT_COUNT")
    public int getMchNftCount() {
        return mchNftCount;
    }

    public void setMchNftCount(int mchNftCount) {
        this.mchNftCount = mchNftCount;
    }

    @Basic
    @Column(name = "MCH_NFT_RESULT")
    public String getMchNftResult() {
        return mchNftResult;
    }

    public void setMchNftResult(String mchNftResult) {
        this.mchNftResult = mchNftResult;
    }

    @Basic
    @Column(name = "CREATE_TIME")
    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "MODIFY_TIME")
    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Basic
    @Column(name = "TM_SMP")
    public Timestamp getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(Timestamp tmSmp) {
        this.tmSmp = tmSmp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ChkControl that = (ChkControl) o;

        if (mainTotCnt != that.mainTotCnt) return false;
        if (oppoTotCnt != that.oppoTotCnt) return false;
        if (totMchCnt != that.totMchCnt) return false;
        if (errTotCnt != that.errTotCnt) return false;
        if (longCnt != that.longCnt) return false;
        if (shortCnt != that.shortCnt) return false;
        if (doubtCnt != that.doubtCnt) return false;
        if (dbtErrCnt != that.dbtErrCnt) return false;
        if (mchNftCount != that.mchNftCount) return false;
        if (chkBatNo != null ? !chkBatNo.equals(that.chkBatNo) : that.chkBatNo != null) return false;
        if (mainNo != null ? !mainNo.equals(that.mainNo) : that.mainNo != null) return false;
        if (oppoNo != null ? !oppoNo.equals(that.oppoNo) : that.oppoNo != null) return false;
        if (chkBusTyp != null ? !chkBusTyp.equals(that.chkBusTyp) : that.chkBusTyp != null) return false;
        if (chkBusSubTyp != null ? !chkBusSubTyp.equals(that.chkBusSubTyp) : that.chkBusSubTyp != null) return false;
        if (priLvl != null ? !priLvl.equals(that.priLvl) : that.priLvl != null) return false;
        if (chkFilDt != null ? !chkFilDt.equals(that.chkFilDt) : that.chkFilDt != null) return false;
        if (mainFilNm != null ? !mainFilNm.equals(that.mainFilNm) : that.mainFilNm != null) return false;
        if (oppoFilNm != null ? !oppoFilNm.equals(that.oppoFilNm) : that.oppoFilNm != null) return false;
        if (chkFilSts != null ? !chkFilSts.equals(that.chkFilSts) : that.chkFilSts != null) return false;
        if (mainRcvDt != null ? !mainRcvDt.equals(that.mainRcvDt) : that.mainRcvDt != null) return false;
        if (oppoRcvDt != null ? !oppoRcvDt.equals(that.oppoRcvDt) : that.oppoRcvDt != null) return false;
        if (chkBegTm != null ? !chkBegTm.equals(that.chkBegTm) : that.chkBegTm != null) return false;
        if (chkEndTm != null ? !chkEndTm.equals(that.chkEndTm) : that.chkEndTm != null) return false;
        if (chkDt != null ? !chkDt.equals(that.chkDt) : that.chkDt != null) return false;
        if (mainTotAmt != null ? !mainTotAmt.equals(that.mainTotAmt) : that.mainTotAmt != null) return false;
        if (oppoTotAmt != null ? !oppoTotAmt.equals(that.oppoTotAmt) : that.oppoTotAmt != null) return false;
        if (totMchAmt != null ? !totMchAmt.equals(that.totMchAmt) : that.totMchAmt != null) return false;
        if (errTotAmt != null ? !errTotAmt.equals(that.errTotAmt) : that.errTotAmt != null) return false;
        if (longAmt != null ? !longAmt.equals(that.longAmt) : that.longAmt != null) return false;
        if (shortAmt != null ? !shortAmt.equals(that.shortAmt) : that.shortAmt != null) return false;
        if (doubtAmt != null ? !doubtAmt.equals(that.doubtAmt) : that.doubtAmt != null) return false;
        if (dbtErrAmt != null ? !dbtErrAmt.equals(that.dbtErrAmt) : that.dbtErrAmt != null) return false;
        if (oprId != null ? !oprId.equals(that.oprId) : that.oprId != null) return false;
        if (rmk != null ? !rmk.equals(that.rmk) : that.rmk != null) return false;
        if (mchNtfFlag != null ? !mchNtfFlag.equals(that.mchNtfFlag) : that.mchNtfFlag != null) return false;
        if (mchNftResult != null ? !mchNftResult.equals(that.mchNftResult) : that.mchNftResult != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (modifyTime != null ? !modifyTime.equals(that.modifyTime) : that.modifyTime != null) return false;
        if (tmSmp != null ? !tmSmp.equals(that.tmSmp) : that.tmSmp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = chkBatNo != null ? chkBatNo.hashCode() : 0;
        result = 31 * result + (mainNo != null ? mainNo.hashCode() : 0);
        result = 31 * result + (oppoNo != null ? oppoNo.hashCode() : 0);
        result = 31 * result + (chkBusTyp != null ? chkBusTyp.hashCode() : 0);
        result = 31 * result + (chkBusSubTyp != null ? chkBusSubTyp.hashCode() : 0);
        result = 31 * result + (priLvl != null ? priLvl.hashCode() : 0);
        result = 31 * result + (chkFilDt != null ? chkFilDt.hashCode() : 0);
        result = 31 * result + (mainFilNm != null ? mainFilNm.hashCode() : 0);
        result = 31 * result + (oppoFilNm != null ? oppoFilNm.hashCode() : 0);
        result = 31 * result + (chkFilSts != null ? chkFilSts.hashCode() : 0);
        result = 31 * result + (mainRcvDt != null ? mainRcvDt.hashCode() : 0);
        result = 31 * result + (oppoRcvDt != null ? oppoRcvDt.hashCode() : 0);
        result = 31 * result + (chkBegTm != null ? chkBegTm.hashCode() : 0);
        result = 31 * result + (chkEndTm != null ? chkEndTm.hashCode() : 0);
        result = 31 * result + (chkDt != null ? chkDt.hashCode() : 0);
        result = 31 * result + (mainTotAmt != null ? mainTotAmt.hashCode() : 0);
        result = 31 * result + mainTotCnt;
        result = 31 * result + (oppoTotAmt != null ? oppoTotAmt.hashCode() : 0);
        result = 31 * result + oppoTotCnt;
        result = 31 * result + (totMchAmt != null ? totMchAmt.hashCode() : 0);
        result = 31 * result + totMchCnt;
        result = 31 * result + errTotCnt;
        result = 31 * result + (errTotAmt != null ? errTotAmt.hashCode() : 0);
        result = 31 * result + (longAmt != null ? longAmt.hashCode() : 0);
        result = 31 * result + longCnt;
        result = 31 * result + (shortAmt != null ? shortAmt.hashCode() : 0);
        result = 31 * result + shortCnt;
        result = 31 * result + (doubtAmt != null ? doubtAmt.hashCode() : 0);
        result = 31 * result + doubtCnt;
        result = 31 * result + (dbtErrAmt != null ? dbtErrAmt.hashCode() : 0);
        result = 31 * result + dbtErrCnt;
        result = 31 * result + (oprId != null ? oprId.hashCode() : 0);
        result = 31 * result + (rmk != null ? rmk.hashCode() : 0);
        result = 31 * result + (mchNtfFlag != null ? mchNtfFlag.hashCode() : 0);
        result = 31 * result + mchNftCount;
        result = 31 * result + (mchNftResult != null ? mchNftResult.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (modifyTime != null ? modifyTime.hashCode() : 0);
        result = 31 * result + (tmSmp != null ? tmSmp.hashCode() : 0);
        return result;
    }
}
