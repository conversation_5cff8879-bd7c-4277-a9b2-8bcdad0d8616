package com.hisun.tms.chk.controller;

import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.chk.model.ChkControl;
import com.hisun.tms.chk.service.ChkControlService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/chk/controller")
public class ChkControlController {

    private static final Logger logger = LoggerFactory.getLogger(ChkControlController.class);

    @Autowired
    private ChkControlService controlService;
    
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/check/controller') or hasRole('ROLE_ADMIN')")
    public ModelAndView exampleList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("chk/controller/index");
        return modelAndView;
    }

    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/check/controller') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<ChkControl> findAll(@Valid @RequestBody GenericParamInput input) {
        return controlService.findAll(input);
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/cptmgr/check/controller') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> modify(@Valid @RequestParam(value = "chkBatNo") String chkBatNo, @Valid @RequestParam(value = "chkFilSts") String chkFilSts,
                                     @RequestParam(value = "rmk") String rmk) {
        return controlService.modify(chkBatNo,chkFilSts,rmk);
    }
}
