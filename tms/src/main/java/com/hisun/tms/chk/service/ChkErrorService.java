package com.hisun.tms.chk.service;

import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.tms.chk.model.ChkControl;
import com.hisun.tms.chk.model.ChkError;
import com.hisun.tms.cpt.model.GenericParamInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface ChkErrorService {

    /**
     * 查找记录并分页
     * @param input
     * @return
     */

    DataTablesOutput<ChkError> findAll(GenericParamInput input);

    /**
     *补单
     * @param chkErId
     * @return
     */

    GenericRspDTO<NoBody> additionalOrder(String chkErId, String rmk);

    /**
     * 撤单
     * @param chkErId
     * @return
     */

    GenericRspDTO<NoBody> cancelOrder(String chkErId, String rmk);

    /**
     * 差错取消
     * @param chkErId
     * @return
     */

    GenericRspDTO<NoBody> cancelError(String chkErId, String rmk);
}