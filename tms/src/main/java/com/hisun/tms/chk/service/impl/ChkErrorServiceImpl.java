package com.hisun.tms.chk.service.impl;

import com.hisun.lemon.chk.client.ChkClient;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.tms.chk.model.ChkError;
import com.hisun.tms.chk.repository.ChkErrorRepository;
import com.hisun.tms.chk.repository.DatatablesChkErrorRepository;
import com.hisun.tms.chk.service.ChkErrorService;
import com.hisun.tms.common.exception.CustomException;
import com.hisun.tms.cpt.model.GenericParamInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service
public class ChkErrorServiceImpl implements ChkErrorService {

    private static final Logger logger = LoggerFactory.getLogger(ChkErrorServiceImpl.class);

    @Resource
    private ChkClient chkClient;
    @Resource
    private DatatablesChkErrorRepository datatablesChkErrorRepository;
    @Resource
    private ChkErrorRepository chkErrorRepository;

    @Override
    @Transactional("chkTransactionManager")
    public DataTablesOutput<ChkError> findAll(GenericParamInput input) {

        String mainNo = input.getExtra_search().get("mainNo");
        String oppoNo = input.getExtra_search().get("oppoNo");
        String chkBusTyp = input.getExtra_search().get("chkBusTyp");
        String chkErrTyp = input.getExtra_search().get("chkErrTyp");
        String errSts = input.getExtra_search().get("errSts");
        String beginDate = input.getExtra_search().get("beginDate");
        String endDate = input.getExtra_search().get("endDate");

        input.getColumn("mainNo").setSearchValue(mainNo);
        input.getColumn("oppoNo").setSearchValue(oppoNo);
        input.getColumn("chkBusTyp").setSearchValue(chkBusTyp);
        input.getColumn("chkErrTyp").setSearchValue(chkErrTyp);
        input.getColumn("errSts").setSearchValue(errSts);

        Specification<ChkError> specification = (root, query, cb) ->  {
            List<Predicate> list = new ArrayList<>();
            if(StringUtils.isNotEmpty(beginDate)){
                Predicate p1 = cb.greaterThanOrEqualTo(root.get("chkErrDt").as(String.class), beginDate);
                list.add(p1);
            }
            if(StringUtils.isNotEmpty(endDate)){
                Predicate p2 = cb.lessThanOrEqualTo(root.get("chkErrDt").as(String.class), endDate);
                list.add(p2);
            }
            Predicate[] p = new Predicate[list.size()];
            query.where(cb.and(list.toArray(p)));
            return query.getRestriction();
        };

        DataTablesOutput<ChkError> dataTablesOutput = datatablesChkErrorRepository.findAll(input,specification);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    @Transactional(value = "chkTransactionManager", rollbackFor = Exception.class)
    public GenericRspDTO<NoBody> additionalOrder(String chkErId, String rmk) {
        try {
            GenericRspDTO<NoBody> genericRspDTO = chkClient.additionalOrder(chkErId);
            if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
                throw new LemonException(genericRspDTO.getMsgCd());
            }
            ChkError chkError = datatablesChkErrorRepository.findOne(chkErId);
            chkError.setRmk(rmk);
            datatablesChkErrorRepository.save(chkError);
            return GenericRspDTO.newInstance("TMS00000");
        } catch (LemonException e) {
            logger.error("调用模块异常：", e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
    }

    @Override
    @Transactional(value = "chkTransactionManager", rollbackFor = Exception.class)
    public GenericRspDTO<NoBody> cancelOrder(String chkErId, String rmk) {
        try {
            GenericRspDTO<NoBody> genericRspDTO = chkClient.cancelOrder(chkErId);
            if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
                throw new LemonException(genericRspDTO.getMsgCd());
            }
            ChkError chkError = datatablesChkErrorRepository.findOne(chkErId);
            chkError.setRmk(rmk);
            datatablesChkErrorRepository.save(chkError);
            return GenericRspDTO.newInstance("TMS00000");
        } catch (LemonException e) {
            logger.error("调用模块异常：", e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
    }

    @Override
    @Transactional(value = "chkTransactionManager", rollbackFor = Exception.class)
    public GenericRspDTO<NoBody> cancelError(String chkErId, String rmk) {
        try {
            GenericRspDTO<NoBody> genericRspDTO = chkClient.cancelError(chkErId);
            if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
                throw new LemonException(genericRspDTO.getMsgCd());
            }
            ChkError chkError = datatablesChkErrorRepository.findOne(chkErId);
            chkError.setRmk(rmk);
            datatablesChkErrorRepository.save(chkError);
            return GenericRspDTO.newInstance("TMS00000");
        } catch (LemonException e) {
            logger.error("调用模块异常：", e);
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
    }
}
