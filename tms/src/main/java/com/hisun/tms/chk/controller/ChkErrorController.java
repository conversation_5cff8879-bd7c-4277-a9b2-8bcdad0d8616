package com.hisun.tms.chk.controller;

import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.tms.chk.model.ChkControl;
import com.hisun.tms.chk.model.ChkError;
import com.hisun.tms.chk.service.ChkControlService;
import com.hisun.tms.chk.service.ChkErrorService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.cpt.model.GenericParamInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/chk/error")
public class ChkErrorController {

    private static final Logger logger = LoggerFactory.getLogger(ChkErrorController.class);

    @Autowired
    private ChkErrorService chkErrorService;

    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/check/error') or hasRole('ROLE_ADMIN')")
    public ModelAndView exampleList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("chk/error/index");
        return modelAndView;
    }

    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/check/error') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<ChkError> findAll(@Valid @RequestBody GenericParamInput input) {
        return chkErrorService.findAll(input);
    }

    @PostMapping(value = "/additionalOrder")
    @PreAuthorize("hasPermission('','/cptmgr/check/error') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public GenericRspDTO<NoBody> additionalOrder(@RequestParam(value = "chkErId") String chkErId,
                                                 @RequestParam(value = "rmk") String rmk){
        return chkErrorService.additionalOrder(chkErId,rmk);
    }

    @PostMapping(value = "/cancelOrder")
    @PreAuthorize("hasPermission('','/cptmgr/check/error') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public GenericRspDTO<NoBody> cancelOrder(@RequestParam(value = "chkErId") String chkErId,
                                             @RequestParam(value = "rmk") String rmk){
        return chkErrorService.cancelOrder(chkErId,rmk);
    }

    @PostMapping(value = "/cancelError")
    @PreAuthorize("hasPermission('','/cptmgr/check/error') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public GenericRspDTO<NoBody> cancelError(@RequestParam(value = "chkErId") String chkErId,
                                             @RequestParam(value = "rmk") String rmk){
        return chkErrorService.cancelError(chkErId,rmk);
    }
}
