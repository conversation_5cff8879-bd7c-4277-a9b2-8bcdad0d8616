package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/07/25.
 */
@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "chkEntityManagerFactory",
        transactionManagerRef = "chkTransactionManager",
        basePackages = {"com.hisun.tms.chk.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class ChkConfig {
    @Autowired
    @Qualifier("chkDataSource")
    private DataSource chkDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "chkoEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return demoEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "chkEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean demoEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(chkDataSource)
                .properties(getVendorProperties(chkDataSource))
                .packages("com.hisun.tms.chk.model") //设置实体类所在位置
                .persistenceUnit("chkPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "chkTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(demoEntityManagerFactory(builder).getObject());
    }
}
