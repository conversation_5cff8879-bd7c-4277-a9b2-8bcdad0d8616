package com.hisun.tms.common.mybatiesConfig;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * TAM模块MyBatis配置
 */
@Configuration
@MapperScan(basePackages = "com.hisun.tms.tam.dao", sqlSessionTemplateRef = "tamSqlSessionTemplate")
public class TamMyBatisConfig {

    @Resource
    @Qualifier("tamDataSource")
    DataSource dataSource;

    @Bean(name = "tamSqlSessionFactory")
    public SqlSessionFactory tamSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:com/hisun/**/mapper/tam/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "tamTransactionManager")
    public DataSourceTransactionManager tamTransactionManager() {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "tamSqlSessionTemplate")
    public SqlSessionTemplate tamSqlSessionTemplate(
            @Qualifier("tamSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}