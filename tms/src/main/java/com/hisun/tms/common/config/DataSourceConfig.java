package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * Created by 吕海龙 on 2017/07/25.
 */
@Configuration
public class DataSourceConfig {
    @Bean(name = "tmsDataSource")
    @Qualifier("tmsDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.tms")
    public DataSource tmsDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "demoDataSource")
    @Qualifier("demoDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.demo")
    public DataSource demoDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "invDataSource")
    @Qualifier("invDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.inv")
    public DataSource invDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "cpmDataSource")
    @Qualifier("cpmDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cpm")
    public DataSource cpmDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "cshDataSource")
    @Qualifier("cshDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.csh")
    public DataSource cshDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "mkmDataSource")
    @Qualifier("mkmDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.mkm")
    public DataSource mkmDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "acmDataSource")
    @Qualifier("acmDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.acm")
    public DataSource acmDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "urmDataSource")
    @Qualifier("urmDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.urm")
    public DataSource urmDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "chkDataSource")
    @Qualifier("chkDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.chk")
    public DataSource chkDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "cpoDataSource")
    @Qualifier("cpoDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cpo")
    public DataSource cpoDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "cpiDataSource")
    @Qualifier("cpiDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cpi")
    public DataSource cpiDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "cmmDataSource")
    @Qualifier("cmmDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cmm")
    public DataSource cmmDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "csmDataSource")
    @Qualifier("csmDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.csm")
    public DataSource csmDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "tfmDataSource")
    @Qualifier("tfmDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.tfm")
    public DataSource tfmDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "rsmDataSource")
    @Qualifier("rsmDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.rsm")
    public DataSource rsmDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "bilDataSource")
    @Qualifier("bilDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.bil")
    public DataSource bilDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "onrDataSource")
    @Qualifier("onrDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.onr")
    public DataSource onrDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "rptDataSource")
    @Qualifier("rptDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.rpt")
    public DataSource rptDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "tamDataSource")
    @Qualifier("tamDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.tam")
    public DataSource tamDataSource() {
        return DataSourceBuilder.create().build();
    }
}
