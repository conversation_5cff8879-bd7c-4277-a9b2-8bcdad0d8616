package com.hisun.tms.common.mybatiesConfig;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * CSH模块的MyBatis配置
 * 
 * <AUTHOR>
 * @date 2025/7/17 17:15
 */
@Configuration
@MapperScan(basePackages = "com.hisun.tms.csh.dao", sqlSessionTemplateRef = "cshSqlSessionTemplate")
public class CshMyBatisConfig {

    @Autowired
    @Qualifier("cshDataSource")
    private DataSource cshDataSource;

    @Bean(name = "cshSqlSessionFactory")
    public SqlSessionFactory cshSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(cshDataSource);
        bean.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:com/hisun/**/mapper/csh/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "cshBTransactionManager")
    public DataSourceTransactionManager cshTransactionManager() {
        return new DataSourceTransactionManager(cshDataSource);
    }

    @Bean(name = "cshSqlSessionTemplate")
    public SqlSessionTemplate cshSqlSessionTemplate(
            @Qualifier("cshSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}