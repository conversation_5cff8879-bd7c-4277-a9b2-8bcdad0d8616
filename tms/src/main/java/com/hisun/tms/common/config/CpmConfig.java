package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/07/25.
 */
@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "cpmEntityManagerFactory",
        transactionManagerRef = "cpmTransactionManager",
        basePackages = {"com.hisun.tms.cpm.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class CpmConfig {
    @Autowired
    @Qualifier("cpmDataSource")
    private DataSource cpmDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "cpmEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return cpmEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "cpmEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean cpmEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(cpmDataSource)
                .properties(getVendorProperties(cpmDataSource))
                .packages("com.hisun.tms.cpm.model") //设置实体类所在位置
                .persistenceUnit("cpmPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "cpmTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(cpmEntityManagerFactory(builder).getObject());
    }
}
