package com.hisun.tms.common.util;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.StringUtils;

import java.util.Collection;
import java.util.Map;

/**
 * 判断
 * 
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:42:59
 *
 */
public class JudgeUtils {
    public static final String successfulMsgCode = "00000";
    
    public static <T> boolean isNull(T t) {
        return null == t;
    }
    
    public static <T> boolean isNotNull(T t) {
        return null != t;
    }
    
    @SafeVarargs
    public static <T> boolean isNullAny(T... ts) {
        if(null == ts) {
            return true;
        }
        
        for(T t: ts) {
            if(null == t) {
                return true;
            }
        }
        return false;
    }
    
    public static <T, L> boolean equals(T t, L l) {
        return t.equals(l);
    }
    
    public static <T, L> boolean notEquals(T t, L l) {
        return ! equals(t, l);
    }
    
    public static boolean equals(String str1, String str2) {
        return StringUtils.equals(str1, str2);
    }
    
    public static boolean notEquals(String str1, String str2) {
        return ! equals(str1, str2);
    }
    
    public static boolean isEmpty(String str) {
        return StringUtils.isEmpty(str);
    }
    
    public static boolean isNotEmpty(String str) {
        return ! isEmpty(str);
    }
    
    public static boolean isBlank(String str) {
        return StringUtils.isBlank(str);
    }
    
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }
    
    /**
     * 集合是否为空
     * @param c
     * @return
     */
    public static boolean isEmpty(Collection<?> c) {
        if(null == c) {
            return true;
        }
        if(c.size() <= 0) {
            return true;
        }
        return false;
    }
    
    /**
     * 集合是否不为空
     * @param c
     * @return
     */
    public static boolean isNotEmpty(Collection<?> c) {
        return !isEmpty(c);
    }
    
    /**
     * 判断map是否为空
     * @param map
     * @return
     */
    public static boolean isEmpty(Map<?, ?> map) {
        if(null == map) {
            return true;
        }
        if(map.size() <= 0) {
            return true;
        }
        return false;
    }
    
    /**
     * 判断map是否不为空
     * @param map
     * @return
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }
    
    /**
     * 判断消息是否成功
     * @param msgCd
     * @return
     */
    public static boolean isSuccess(String msgCd) {
        if(StringUtils.isEmpty(msgCd)) {
            throw new LemonException("MsgCd is null.");
        }
        int len = msgCd.length();
        return StringUtils.equals(msgCd.substring(len - 5), successfulMsgCode);
    }
    
    /**
     * 交易执行失败
     * @param msgCd
     * @return
     */
    public static boolean isNotSuccess(String msgCd) {
        return ! isSuccess(msgCd);
    }
    
    public static boolean not (boolean flag) {
        return ! flag;
    }
    
    public static boolean isTrue(Boolean flag, boolean defaultFlag) {
        if(null == flag) {
            return defaultFlag;
        }
        return flag;
    }
}
