package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/07/25.
 */
@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "acmEntityManagerFactory",
        transactionManagerRef = "acmTransactionManager",
        basePackages = {"com.hisun.tms.acm.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class AcmConfig {
    @Autowired
    @Qualifier("acmDataSource")
    private DataSource acmDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "acmEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return demoEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "acmEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean demoEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(acmDataSource)
                .properties(getVendorProperties(acmDataSource))
                .packages("com.hisun.tms.acm.model") //设置实体类所在位置
                .persistenceUnit("acmPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "acmTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(demoEntityManagerFactory(builder).getObject());
    }
}
