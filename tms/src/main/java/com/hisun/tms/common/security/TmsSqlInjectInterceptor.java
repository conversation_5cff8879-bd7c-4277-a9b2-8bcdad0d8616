package com.hisun.tms.common.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.tms.urm.model.MerRegister;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/9/8 10:51
 */
@Component
public class TmsSqlInjectInterceptor extends HandlerInterceptorAdapter {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String path  = request.getServletPath();
        if (request.getServletPath().indexOf("/mkm/batch/upload") >=0 || request.getServletPath().indexOf("/file/upload/base64Img") >=0 || request.getServletPath().indexOf("/file/upload") >=0
                || request.getServletPath().indexOf("/urm/mermgr/info/checkMobile") >=0 || request.getServletPath().indexOf("/file/downloadByFilePath") >=0) {
            return true;
        }
        Enumeration<String> names = request.getParameterNames();

        String contentType = request.getContentType();

        if (contentType !=null && contentType.indexOf("application/json") >= 0) {
//            StringBuilder sb = new StringBuilder();
//            try{
//                char[]buff = new char[1024];
//                int len;
//                BufferedReader reader = request.getReader();
//                while((len = reader.read(buff)) != -1) {
//                    sb.append(buff,0, len);
//                }
//                ObjectMapper mapper = new ObjectMapper();
//                Map<String,Object> paramterMap = mapper.readValue(sb.toString(), Map.class);
//                System.out.println(sb.toString());
//                //递归处理参数值
//                parseParamterMap(paramterMap) ;
//            }catch (IOException e) {
//                e.printStackTrace();
//                response.sendRedirect("/500.html");
//                return false;
//            }
        } else {
            while (names.hasMoreElements()) {
                String name = names.nextElement();
                String[] values = request.getParameterValues(name);
                for (String value : values) {
                    if (!StringUtils.isBlank(value)) {
                        value = clearXss(value);
                        if (!StringUtils.isBlank(value)) {
                            if (judgeXSS(value.toLowerCase())) {
                                response.sendRedirect("/500.html");
                                return false;
                            }
                        }
                    }
                }
            }
        }

        return true;

    }

    private void parseParamterMap(Map<String, Object> paramterMap) {
        for (Map.Entry<String ,Object> entry : paramterMap.entrySet()) {
            if (entry.getValue() instanceof Map) {
                parseParamterMap((Map<String, Object>) entry.getValue());
            }
            if (entry.getValue() instanceof String) {
                String value  = (String) entry.getValue();
                if (!StringUtils.isBlank(value)) {
                    value = clearXss(value);
                    if (!StringUtils.isBlank(value)) {
                        if (judgeXSS(value.toLowerCase())) {
                            throw new LemonException("xssException");
                        }
                    }
                }
            }
            if (entry.getValue() instanceof ArrayList ) {
                if (entry.getValue() != null ) {
                    List list = (List) entry.getValue();
                    for (Object o : list ) {
                        if (o instanceof Map) {
                            parseParamterMap((Map<String, Object>) o);
                        }
                        if (o instanceof String ) {
                            String value  = (String) o;
                            if (!StringUtils.isBlank(value)) {
                                value = clearXss(value);
                                if (!StringUtils.isBlank(value)) {
                                    if (judgeXSS(value.toLowerCase())) {
                                        throw new LemonException("xssException");
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 判断参数是否含有攻击串
     *
     * @param value
     * @return
     */
    public boolean judgeXSS(String value) {
        if (value == null || "".equals(value)) {
            return false;
        }
        String xssStr = "and|or|select|update|delete|drop|truncate|%20|=|--|;|'|%|#|//|/| |\\|!=|(|)|>|<|";
        String[] xssArr = xssStr.split("\\|");
        for (int i = 0; i < xssArr.length; i++) {
            if (value.indexOf(xssArr[i]) > -1) {
                return true;
            }
        }
        return false;

    }

    /**
     * 处理字符转义
     *
     * @param value
     * @return
     */
    private String clearXss(String value) {
        if (value == null || "".equals(value)) {
            return value;
        }
        value = value.replaceAll("<", "<").replaceAll(">", ">");
        value = value.replaceAll("\\(", "(").replace("\\)", ")");
        value = value.replaceAll("'", "'");
        value = value.replaceAll("eval\\((.*)\\)", "");
        value = value.replaceAll("[\\\"\\\'][\\s]*javascript:(.*)[\\\"\\\']", "\"\"");
        value = value.replace("script", "");
        return value;
    }

}
