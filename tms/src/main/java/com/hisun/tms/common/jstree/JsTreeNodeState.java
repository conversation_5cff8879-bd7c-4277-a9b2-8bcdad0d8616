package com.hisun.tms.common.jstree;

/**
 * Created by 吕海龙 on 2017/7/10.
 * Alternative format of the node (id & parent are required)
 */
public class JsTreeNodeState {
    private boolean opened;// is the node open
    private boolean disabled;// is the node disabled
    private boolean selected;// is the node selected

    public JsTreeNodeState() {
    }

    public JsTreeNodeState(boolean opened, boolean disabled, boolean selected) {
        this.opened = opened;
        this.disabled = disabled;
        this.selected = selected;
    }

    public boolean isOpened() {
        return opened;
    }

    public void setOpened(boolean opened) {
        this.opened = opened;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

}
