package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:22:10 
 * @version V1.0
 */
@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "cmmEntityManagerFactory",
        transactionManagerRef = "cmmTransactionManager",
        basePackages = {"com.hisun.tms.cmm.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class CmmConfig {
    @Autowired
    @Qualifier("cmmDataSource")
    private DataSource cmmDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "cmmEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return cmmEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "cmmEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean cmmEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(cmmDataSource)
                .properties(getVendorProperties(cmmDataSource))
                .packages("com.hisun.tms.cmm.model") //设置实体类所在位置
                .persistenceUnit("cmmPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "cmmTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(cmmEntityManagerFactory(builder).getObject());
    }
}
