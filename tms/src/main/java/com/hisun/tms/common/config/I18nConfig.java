package com.hisun.tms.common.config;

import com.hisun.lemon.framework.i18n.LocaleMessageSource;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

import javax.annotation.Resource;

/**
 * 国际化配置类
 */
@Configuration
public class I18nConfig {

    @Resource
    private MessageSource messageSource;

    /**
     * 配置LocaleMessageSource Bean
     * 这里使用已有的MessageSource来创建LocaleMessageSource
     */
    @Bean
    public LocaleMessageSource localeMessageSource() {
        return new LocaleMessageSource() {
            @Override
            public String getMessage(String code, Object[] args) {
                return messageSource.getMessage(code, args, null);
            }

            @Override
            public String getMessage(String code, Object[] args, String defaultMessage) {
                return messageSource.getMessage(code, args, defaultMessage, null);
            }
        };
    }
}