package com.hisun.tms.common.security;

import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.sys.common.Constants;
import com.hisun.tms.sys.model.TmsOprMapper;
import com.hisun.tms.sys.model.TmsUserOprLogDo;
import com.hisun.tms.sys.repository.TmsOprMapperRespository;
import com.hisun.tms.sys.repository.TmsUserOprLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * Created by chen on 12/21 0021.
 */
public class TmsUserOprLogInterceptor extends HandlerInterceptorAdapter {
    private static final Logger logger = LoggerFactory.getLogger(HandlerInterceptorAdapter.class);

    @Autowired
    private TmsOprMapperRespository tmsOprMapperRespository ;

    @Autowired
    private TmsUserOprLogRepository tmsUserOprLogRepository ;
    @Resource
    private GetOpr getOpr;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try{
            String path = request.getServletPath() ;
            String method = request.getMethod() ;
            String sUrl = specillyUrl(path) ;
            if (StringUtils.isNotBlank(sUrl)) {
                path = sUrl ;
            }
            TmsOprMapper tmsOprMapper = tmsOprMapperRespository.findByOprUrl(method , path ) ;
            if (tmsOprMapper != null ) {
                TmsUserOprLogDo tmsUserOprLogDo = new TmsUserOprLogDo() ;
                tmsUserOprLogDo.setIp(request.getRemoteAddr());
                tmsUserOprLogDo.setLv(tmsOprMapper.getLv());
                tmsUserOprLogDo.setOprNm(tmsOprMapper.getOprNm());
                tmsUserOprLogDo.setUserId(getOpr.getOperatorName());
                tmsUserOprLogDo.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
                tmsUserOprLogDo.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                tmsUserOprLogRepository.save(tmsUserOprLogDo) ;
            }
        } catch (Exception e) {
            logger.info("登记用户操作日志失败" +e.getMessage()) ;
        }
        return true ;
    }

    /**
     * 带有路径参数的特殊处理
     * @param url
     * @return
     */
    private String specillyUrl(String url) {
        Map<String,String> specillyMap = Constants.specillyUrlMap ;
        url = url.substring(0 , url.lastIndexOf("/"));
        if (specillyMap.containsKey(url)) {
            return specillyMap.get(url) ;
        }
        return "" ;
    }
}


