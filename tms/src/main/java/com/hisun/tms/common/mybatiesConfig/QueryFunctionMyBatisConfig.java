package com.hisun.tms.common.mybatiesConfig;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * 查询功能专用MyBatis配置
 * 用于手续费查询和汇率查询功能，避免与现有TfmConfig冲突
 * 
 * 此配置专门处理查询相关的DAO接口：
 * - com.hisun.tms.cpt.dao.ICptFeeOrderQueryDao (手续费查询)
 * - com.hisun.tms.tfm.dao.ITfmExchangeRateDao (汇率查询)
 * 
 * 注意：此配置使用独立的SqlSessionFactory和SqlSessionTemplate，
 * 与现有的TfmMyBatisConfig分离，避免冲突
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Configuration
@MapperScan(
    basePackages = {"com.hisun.tms.cpt.dao", "com.hisun.tms.tfm.dao"}, 
    sqlSessionTemplateRef = "queryFunctionSqlSessionTemplate",
    annotationClass = Mapper.class
)
public class QueryFunctionMyBatisConfig {

    @Resource
    @Qualifier("tfmDataSource")
    DataSource dataSource;

    @Bean(name = "queryFunctionSqlSessionFactory")
    public SqlSessionFactory queryFunctionSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        
        // 设置MyBatis配置文件
        bean.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:mybatis/mybatis-query-config.xml"));
        
        // 扫描手续费查询和汇率查询的mapper XML文件
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:com/hisun/**/mapper/{cpt,tfm}/*.xml"));
        
        return bean.getObject();
    }

    @Bean(name = "queryFunctionTransactionManager")
    public DataSourceTransactionManager queryFunctionTransactionManager() {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "queryFunctionSqlSessionTemplate")
    public SqlSessionTemplate queryFunctionSqlSessionTemplate(@Qualifier("queryFunctionSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}