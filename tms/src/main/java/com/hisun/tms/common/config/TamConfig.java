package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * TAM模块JPA配置
 */
@Configuration
@EnableJpaRepositories(entityManagerFactoryRef = "tamEntityManagerFactory", transactionManagerRef = "tamTransactionManager", basePackages = {
        "com.hisun.tms.tam.repository" }, repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class)
public class TamConfig {
    @Autowired
    @Qualifier("tamDataSource")
    private DataSource tamDataSource;

    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "tamEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return tamEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "tamEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean tamEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(tamDataSource)
                .properties(getVendorProperties(tamDataSource))
                .packages("com.hisun.tms.tam.model")
                .persistenceUnit("tamPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "tamTransactionManager")
    PlatformTransactionManager tamTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(tamEntityManagerFactory(builder).getObject());
    }
}