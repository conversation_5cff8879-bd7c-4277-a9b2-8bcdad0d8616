package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/07/25.
 */
@Configuration
@EnableJpaRepositories(entityManagerFactoryRef = "tfmEntityManagerFactory", transactionManagerRef = "tfmTransactionManager", basePackages = {
        "com.hisun.tms.tfm.repository" }, // 设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class)
public class TfmConfig {
    @Autowired
    @Qualifier("tfmDataSource")
    private DataSource tfmDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "tfmEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return tfmEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "tfmEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean tfmEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(tfmDataSource)
                .properties(getVendorProperties(tfmDataSource))
                .packages("com.hisun.tms.tfm.model") // 设置实体类所在位置
                .persistenceUnit("tfmPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "tfmTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(tfmEntityManagerFactory(builder).getObject());
    }
}
