package com.hisun.tms.common.config;

import com.hisun.tms.common.security.TmsPermissionEvaluator;
import com.hisun.tms.common.security.TmsSqlInjectInterceptor;
import com.hisun.tms.common.security.TmsUserOprLogInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.access.expression.DefaultWebSecurityExpressionHandler;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import org.springframework.web.servlet.i18n.CookieLocaleResolver;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.thymeleaf.extras.springsecurity4.dialect.SpringSecurityDialect;

/**
 * Created by 吕海龙 on 2017/4/6.
 */
@Configuration
public class WebMvcConfig extends WebMvcConfigurerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(WebMvcConfig.class);

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        return bCryptPasswordEncoder;
    }

    @Bean
    public SpringSecurityDialect securityDialect() {
        return new SpringSecurityDialect();
    }

    @Bean
    public PermissionEvaluator getPermissionEvaluator() {
        return new TmsPermissionEvaluator();
    }

    @Bean
    public DefaultMethodSecurityExpressionHandler getExpressionHandler() {
        DefaultMethodSecurityExpressionHandler handler = new DefaultMethodSecurityExpressionHandler();
        handler.setPermissionEvaluator(getPermissionEvaluator());
        logger.info("DefaultMethodSecurityExpressionHandler has benn set custom TMSPermissionEvaluator.");
        return handler;
    }

    @Bean
    public DefaultWebSecurityExpressionHandler getWebExpressionHandler() {
        DefaultWebSecurityExpressionHandler handler = new DefaultWebSecurityExpressionHandler();
        handler.setPermissionEvaluator(getPermissionEvaluator());
        logger.info("DefaultWebSecurityExpressionHandler has benn set custom TmsPermissionEvaluator.");
        return handler;
    }

    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor localeChangeInterceptor = new LocaleChangeInterceptor();
        localeChangeInterceptor.setParamName("lang");
        return localeChangeInterceptor;
    }

    @Bean(name = "localeResolver")
    public CookieLocaleResolver localeResolver() {
        CookieLocaleResolver localeResolver = new CookieLocaleResolver();
        localeResolver.setCookieName("lang");
        return localeResolver;
    }

    // spring boot 默认配置未生效，原因暂未定位
    @Bean
    public MessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename("classpath:messages");
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }

    @Bean
    public TmsSqlInjectInterceptor getTmsSqlInjectInterceptor() {
        return new TmsSqlInjectInterceptor();
    }

    @Bean
    public TmsUserOprLogInterceptor getTmsUserOprLogInterceptor() {
        return new TmsUserOprLogInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getTmsSqlInjectInterceptor()).addPathPatterns("/**");
        registry.addInterceptor(getTmsUserOprLogInterceptor()).addPathPatterns("/**");
        registry.addInterceptor(localeChangeInterceptor());
    }

}
