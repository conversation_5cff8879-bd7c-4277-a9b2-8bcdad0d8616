package com.hisun.tms.common.audit;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;
import java.time.LocalDateTime;

/**
 * Created by 吕海龙 on 2017/7/17.
 * Spring Data JPA 审计抽象，目前只有@CreatedDate、@LastModifiedDate
 */
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class AbstractEntity {
//    @Id
//    @GeneratedValue
//    Long id;

    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "gmt_create", nullable = false, updatable = false)
    @CreatedDate
    private LocalDateTime createTime;

    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "gmt_modified")
    @LastModifiedDate
    private LocalDateTime lastmodifiedTime;

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getLastmodifiedTime() {
        return lastmodifiedTime;
    }

    public void setLastmodifiedTime(LocalDateTime lastmodifiedTime) {
        this.lastmodifiedTime = lastmodifiedTime;
    }
}
