package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "urmEntityManagerFactory",
        transactionManagerRef = "urmTransactionManager",
        basePackages = {"com.hisun.tms.urm.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class urmConfig {
    @Autowired
    @Qualifier("urmDataSource")
    private DataSource urmDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "urmEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return urmEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "urmEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean urmEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(urmDataSource)
                .properties(getVendorProperties(urmDataSource))
                .packages("com.hisun.tms.urm.model") //设置实体类所在位置
                .persistenceUnit("urmPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "urmTransactionManager")
    PlatformTransactionManager urmTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(urmEntityManagerFactory(builder).getObject());
    }
}
