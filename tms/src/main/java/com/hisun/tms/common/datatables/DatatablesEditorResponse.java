package com.hisun.tms.common.datatables;

import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/16.
 * https://editor.datatables.net/manual/server
 */
public class DatatablesEditorResponse<T> {
    private Map<String, T> data;
    private String error;
    private DatatablesEditorFieldErrors fieldErrors;

    public Map<String, T> getData() {
        return data;
    }

    public void setData(Map<String, T> data) {
        this.data = data;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public DatatablesEditorFieldErrors getFieldErrors() {
        return fieldErrors;
    }

    public void setFieldErrors(DatatablesEditorFieldErrors fieldErrors) {
        this.fieldErrors = fieldErrors;
    }
}
