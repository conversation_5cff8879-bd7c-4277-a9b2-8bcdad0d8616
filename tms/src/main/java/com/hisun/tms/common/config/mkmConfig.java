package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/07/25.
 */
@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "mkmEntityManagerFactory",
        transactionManagerRef = "mkmTransactionManager",
        basePackages = {"com.hisun.tms.mkm.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class mkmConfig {
    @Autowired
    @Qualifier("mkmDataSource")
    private DataSource mkmDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "mkmEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return mkmEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "mkmEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean mkmEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(mkmDataSource)
                .properties(getVendorProperties(mkmDataSource))
                .packages("com.hisun.tms.mkm.model") //设置实体类所在位置
                .persistenceUnit("mkmPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "mkmTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(mkmEntityManagerFactory(builder).getObject());
    }
}
