package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/07/25.
 */
@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "cshEntityManagerFactory",
        transactionManagerRef = "cshTransactionManager",
        basePackages = {"com.hisun.tms.csh.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class CshConfig {
    @Autowired
    @Qualifier("cshDataSource")
    private DataSource cshDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "cshEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return cshEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "cshEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean cshEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(cshDataSource)
                .properties(getVendorProperties(cshDataSource))
                .packages("com.hisun.tms.csh.model") //设置实体类所在位置
                .persistenceUnit("tamPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "cshTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(cshEntityManagerFactory(builder).getObject());
    }
}
