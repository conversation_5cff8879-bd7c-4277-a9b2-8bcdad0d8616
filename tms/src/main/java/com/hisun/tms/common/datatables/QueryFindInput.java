package com.hisun.tms.common.datatables;

import org.springframework.data.jpa.datatables.mapping.DataTablesInput;

import java.util.Map;

/**
 * 统一分页查询输入参数
 * 用于DataTables分页查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
public class QueryFindInput extends DataTablesInput {
    
    /**
     * 额外查询条件
     */
    private Map<String, String> extraSearch;

    public Map<String, String> getExtraSearch() {
        return extraSearch;
    }

    public void setExtraSearch(Map<String, String> extraSearch) {
        this.extraSearch = extraSearch;
    }
}