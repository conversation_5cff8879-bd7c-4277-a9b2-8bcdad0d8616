package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/07/25.
 */
@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "tmsEntityManagerFactory",
        transactionManagerRef = "tmsTransactionManager",
        basePackages = {"com.hisun.tms.sys.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class)
public class TmsConfig {

    @Autowired
    @Qualifier("tmsDataSource")
    private DataSource tmsDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "tmsEntityManager")
    @Primary
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return tmsEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "tmsEntityManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean tmsEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(tmsDataSource)
                .properties(getVendorProperties(tmsDataSource))
                .packages("com.hisun.tms.sys.model") //设置实体类所在位置
                .persistenceUnit("tmsPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "tmsTransactionManager")
    @Primary
    public PlatformTransactionManager tmsTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(tmsEntityManagerFactory(builder).getObject());
    }

}
