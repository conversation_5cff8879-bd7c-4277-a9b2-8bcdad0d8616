package com.hisun.tms.common.security;

import com.hisun.tms.sys.model.Role;
import com.hisun.tms.sys.model.User;
import com.hisun.tms.sys.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Set;

/**
 * Created by 吕海龙 on 2017/4/16.
 */
@Component
public class TmsPermissionEvaluator implements PermissionEvaluator {
    private static final Logger logger = LoggerFactory.getLogger(TmsPermissionEvaluator.class);
    private UserRepository userRepository;
    @Autowired
    public void setUserRepository(UserRepository userRepository) {
        System.out.println("能装配吗"+userRepository);
        this.userRepository = userRepository;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        String username = authentication.getName();
        logger.info("登录用户：" + username);
        User user = userRepository.findByName1(username);
        if (user == null) {
            logger.warn("用户不存在：{}", username);
            return false;
        }
        Set<Role> roles = user.getRoles();
        logger.info("用户角色数量={}", roles.size());
        roles.forEach(role -> {
            logger.info("role:id={},name={}", role.getId(), role.getRoleName());
        });
        logger.info("权限：{}", permission.toString());
        // 为兼容jstree构造的权限菜单
        return roles.stream().anyMatch(role -> role.getResources().stream().anyMatch(resource -> resource.getResource().startsWith(permission.toString())));
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        // not supported
        return false;
    }
}
