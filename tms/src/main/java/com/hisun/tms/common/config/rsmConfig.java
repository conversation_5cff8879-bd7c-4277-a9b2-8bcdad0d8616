package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "rsmEntityManagerFactory",
        transactionManagerRef = "rsmTransactionManager",
        basePackages = {"com.hisun.tms.rsm.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class rsmConfig {
    @Autowired
    @Qualifier("rsmDataSource")
    private DataSource rsmDataSource;
    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "rsmEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return invEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "rsmEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean invEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(rsmDataSource)
                .properties(getVendorProperties(rsmDataSource))
                .packages("com.hisun.tms.rsm.model") //设置实体类所在位置
                .persistenceUnit("rsmPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "rsmTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(invEntityManagerFactory(builder).getObject());
    }
}
