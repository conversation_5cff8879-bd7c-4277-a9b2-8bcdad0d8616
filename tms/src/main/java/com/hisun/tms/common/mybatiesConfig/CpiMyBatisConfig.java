package com.hisun.tms.common.mybatiesConfig;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * MyBatis基础配置
 *
 * <AUTHOR>
 * @since 2015-12-19 10:11
 */
@Configuration
@MapperScan(basePackages = "com.hisun.tms.cpt.dao.cpi", sqlSessionTemplateRef  = "cpiSqlSessionTemplate")
public class CpiMyBatisConfig {

    @Resource
    @Qualifier("cpiDataSource")
    DataSource dataSource;


    @Bean(name = "cpiSqlSessionFactory")
    public SqlSessionFactory cpoSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:com/hisun/**/mapper/cpi/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "cpiTransactionManager")
    public DataSourceTransactionManager cpoTransactionManager() {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "cpiSqlSessionTemplate")
    public SqlSessionTemplate cpoSqlSessionTemplate(@Qualifier("cpiSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}