//package com.hisun.tms.common.config;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
//import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
//import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
//import org.springframework.orm.jpa.JpaTransactionManager;
//import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
//import org.springframework.transaction.PlatformTransactionManager;
//
//import javax.persistence.EntityManager;
//import javax.sql.DataSource;
//import java.util.Map;
//
//@Configuration
//@EnableJpaRepositories(
//        entityManagerFactoryRef = "cpoEntityManagerFactory",
//        transactionManagerRef = "cpoTransactionManager",
//        basePackages = {"com.hisun.tms.cpt.repository"},//设置Repository所在位置
//        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
//)
//public class CpoConfig {
//
//    @Autowired
//    @Qualifier("cpoDataSource")
//    private DataSource cpoDataSource;
//
//    @Autowired
//    private JpaProperties jpaProperties;
//
//    @Bean(name = "cpoEntityManager")
//    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
//        return cpoEntityManagerFactory(builder).getObject().createEntityManager();
//    }
//
//    @Bean(name = "cpoEntityManagerFactory")
//    public LocalContainerEntityManagerFactoryBean cpoEntityManagerFactory(EntityManagerFactoryBuilder builder) {
//        return builder
//                .dataSource(cpoDataSource)
//                .properties(getVendorProperties(cpoDataSource))
//                .packages("com.hisun.tms.cpt.model") //设置实体类所在位置
//                .persistenceUnit("cpoPersistenceUnit")
//                .build();
//    }
//
//    private Map<String, String> getVendorProperties(DataSource dataSource) {
//        return jpaProperties.getHibernateProperties(dataSource);
//    }
//
//    @Bean(name = "cpoTransactionManager")
//    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
//        return new JpaTransactionManager(cpoEntityManagerFactory(builder).getObject());
//    }
//}
