package com.hisun.tms.common.mybatiesConfig;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * MyBatis基础配置
 *
 * <AUTHOR>
 * @since 2015-12-19 10:11
 */
@Configuration
@MapperScan(basePackages = "com.hisun.tms.bil.dao", sqlSessionTemplateRef  = "bilSqlSessionTemplate")
public class BilMyBatisConfig {

    @Resource
    @Qualifier("bilDataSource")
    DataSource dataSource;


    @Bean(name = "bilSqlSessionFactory")
    public SqlSessionFactory cpoSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:com/hisun/**/mapper/bil/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "bilTransactionManager")
    public DataSourceTransactionManager cpoTransactionManager() {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "bilSqlSessionTemplate")
    public SqlSessionTemplate cpoSqlSessionTemplate(@Qualifier("bilSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}