package com.hisun.tms.common.security;

import com.hisun.tms.sys.model.Resource;
import com.hisun.tms.sys.model.Role;
import com.hisun.tms.sys.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * Created by 吕海龙 on 2017/4/11.
 */
@Service
public class TmsFilterInvocationSecurityMetadataSource implements
        FilterInvocationSecurityMetadataSource {
    private static Map<String, Collection<ConfigAttribute>> resourceMap = null;

    @Autowired
    private RoleRepository roleRepository;

    @PostConstruct
    private void loadResourceDefine() {
        List<Role> roleList = roleRepository.findAll();

        resourceMap = new HashMap<String, Collection<ConfigAttribute>>();

        for (Role role : roleList) {
            ConfigAttribute configAttribute = new SecurityConfig(role.getRole());
            for (Resource resource : role.getResources()) {
                String url = resource.getResource();
                if (resourceMap.containsKey(url)) {
                    Collection<ConfigAttribute> value = resourceMap.get(url);
                    value.add(configAttribute);
                    resourceMap.put(url, value);
                } else {
                    Collection<ConfigAttribute> atts = new ArrayList<>();
                    atts.add(configAttribute);
                    resourceMap.put(url, atts);
                }

            }
        }

    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return new ArrayList<ConfigAttribute>();
    }

    // 访问某个资源object需要什么角色
    @Override
    public Collection<ConfigAttribute> getAttributes(Object object)
            throws IllegalArgumentException {
        FilterInvocation filterInvocation = (FilterInvocation) object;// 对于http资源来说，object是FilterInvocation
        if (resourceMap == null) {
            loadResourceDefine();
        }
        Iterator<String> ite = resourceMap.keySet().iterator();
        while (ite.hasNext()) {
            String resURL = ite.next();
            RequestMatcher requestMatcher = new AntPathRequestMatcher(resURL);
            if (requestMatcher.matches(filterInvocation.getHttpRequest())) {
                return resourceMap.get(resURL);
            }
        }

        return null;
    }

    @Override
    public boolean supports(Class<?> arg0) {

        return true;
    }

}
