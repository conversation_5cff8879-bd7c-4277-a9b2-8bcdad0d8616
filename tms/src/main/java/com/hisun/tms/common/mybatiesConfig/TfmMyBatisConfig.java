package com.hisun.tms.common.mybatiesConfig;

import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Properties;

/**
 * MyBatis基础配置
 *
 * <AUTHOR>
 * @since 2015-12-19 10:11
 */
@Configuration
@MapperScan(basePackages = "com.hisun.tms.tfm.mapper", sqlSessionTemplateRef  = "tfmSqlSessionTemplate")
public class TfmMyBatisConfig  {

    @Resource
    @Qualifier("tfmDataSource")
    DataSource dataSource;

    @Bean(name = "tfmSqlSessionFactory")
    @Primary
    public SqlSessionFactory tfmSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:com/hisun/**/mapper/tfm/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "tfmTransactionManager")
    public DataSourceTransactionManager tfmTransactionManager() {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "tfmSqlSessionTemplate")
    public SqlSessionTemplate tfmSqlSessionTemplate(@Qualifier("tfmSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}