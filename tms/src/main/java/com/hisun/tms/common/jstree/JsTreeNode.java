package com.hisun.tms.common.jstree;

/**
 * Created by 吕海龙 on 2017/7/10.
 * Alternative format of the node (id & parent are required)
 */
public class JsTreeNode {
    private String id;// required
    private String parent;// required
    private String text;// node text
    private String icon;// string for custom
    private JsTreeNodeState state;

    public JsTreeNode() {
    }

    public JsTreeNode(String id, String parent, String text, String icon, JsTreeNodeState state) {
        this.id = id;
        this.parent = parent;
        this.text = text;
        this.icon = icon;
        this.state = state;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public JsTreeNodeState getState() {
        return state;
    }

    public void setState(JsTreeNodeState state) {
        this.state = state;
    }
}
