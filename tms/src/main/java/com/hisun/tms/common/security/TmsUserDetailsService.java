package com.hisun.tms.common.security;

import com.hisun.tms.sys.model.Role;
import com.hisun.tms.sys.model.User;
import com.hisun.tms.sys.repository.RoleRepository;
import com.hisun.tms.sys.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by 吕海龙 on 2017/4/10.
 */
@Component
public class TmsUserDetailsService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(TmsUserDetailsService.class);
    @Autowired
    protected UserRepository userRepository;

    @Autowired
    protected RoleRepository roleRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByName(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户信息不存在");
        }
        org.springframework.security.core.userdetails.User userDetails = new org.springframework.security.core.userdetails.User(username, user.getPassword(), user.isActived(), true, true, true, grantedAuthorities(user.getId()));
        return userDetails;
    }

    protected Collection<GrantedAuthority> grantedAuthorities(int id) {
        User user = userRepository.findById(id);
        Set<Role> roles = user.getRoles();
        logger.info("role size={}",roles.size());
        if (CollectionUtils.isEmpty(roles)) {
            return new ArrayList<>();
        }
        Collection<GrantedAuthority> authorities = new HashSet<>();

        roles.stream().forEach((role -> {
            logger.info("role:id={},name={}",role.getId(),role.getRoleName());
            authorities.add(new SimpleGrantedAuthority(role.getRole()));
        }));
        return authorities;
    }
}
