package com.hisun.tms.common.mybatiesConfig;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * MyBatis基础配置
 *
 * <AUTHOR>
 * @since 2015-12-19 10:11
 */
@Configuration
@MapperScan(basePackages = "com.hisun.tms.acm.dao", sqlSessionTemplateRef  = "acmSqlSessionTemplate")
public class AcmMyBatisConfig {

    @Resource
    @Qualifier("acmDataSource")
    DataSource dataSource;


    @Bean(name = "acmSqlSessionFactory")
    public SqlSessionFactory cpoSqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources
                ("classpath*:com/hisun/**/mapper/acm/*.xml"));
        bean.setConfigLocation(new PathMatchingResourcePatternResolver().getResource
                ("classpath:mybatis/mybatis-query-config.xml"));
        return bean.getObject();
    }

    @Bean(name = "acmBTransactionManager")
    public DataSourceTransactionManager cpoTransactionManager() {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "acmSqlSessionTemplate")
    public SqlSessionTemplate cpoSqlSessionTemplate(@Qualifier("acmSqlSessionFactory") SqlSessionFactory
                                                                sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}