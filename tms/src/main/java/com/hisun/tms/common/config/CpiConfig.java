package com.hisun.tms.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.sql.DataSource;
import java.util.Map;

@Configuration
@EnableJpaRepositories(
        entityManagerFactoryRef = "cpiEntityManagerFactory",
        transactionManagerRef = "cpiTransactionManager",
        basePackages = {"com.hisun.tms.cpt.repository"},//设置Repository所在位置
        repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class
)
public class CpiConfig {
    @Autowired
    @Qualifier("cpiDataSource")
    private DataSource cpiDataSource;

    @Autowired
    private JpaProperties jpaProperties;

    @Bean(name = "cpiEntityManager")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return cpiEntityManagerFactory(builder).getObject().createEntityManager();
    }

    @Bean(name = "cpiEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean cpiEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(cpiDataSource)
                .properties(getVendorProperties(cpiDataSource))
                .packages("com.hisun.tms.cpt.model") //设置实体类所在位置
                .persistenceUnit("cpiPersistenceUnit")
                .build();
    }

    private Map<String, String> getVendorProperties(DataSource dataSource) {
        return jpaProperties.getHibernateProperties(dataSource);
    }

    @Bean(name = "cpiJTransactionManager")
    PlatformTransactionManager demoTransactionManager(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(cpiEntityManagerFactory(builder).getObject());
    }
}
