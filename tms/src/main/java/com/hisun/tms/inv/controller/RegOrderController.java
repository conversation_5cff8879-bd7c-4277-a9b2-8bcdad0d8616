package com.hisun.tms.inv.controller;


import com.hisun.tms.inv.model.OrderInf;
import com.hisun.tms.inv.model.InvQueryInfParamInput;
import com.hisun.tms.inv.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/inv/regorderctrl")
public class RegOrderController {

    private static final Logger logger = LoggerFactory.getLogger(RegOrderController.class);

    @Autowired
    private OrderService orderService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/invmgr/regorderctrl') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("inv/orderinfo/regindex");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/regorderctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<OrderInf> findAll(@Valid @RequestBody InvQueryInfParamInput invQueryInfParamInput) {
        return orderService.regfindAll(invQueryInfParamInput);
    }
}
