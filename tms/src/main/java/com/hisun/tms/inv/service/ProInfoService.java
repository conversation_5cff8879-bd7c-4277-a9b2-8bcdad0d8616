package com.hisun.tms.inv.service;

import com.hisun.tms.inv.model.InvQueryInfParamInput;
import com.hisun.tms.inv.model.ProInfo;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface ProInfoService {

    void save(ProInfo proInfo);

    Map<String, ProInfo> modify(Map<String, ProInfo> exampleMap);

    Map<String, String> delete(Iterable<ProInfo> entities);

    DataTablesOutput<ProInfo> findAll(InvQueryInfParamInput input);

	DataTablesOutput<ProInfo> regfindAll(InvQueryInfParamInput input);
}