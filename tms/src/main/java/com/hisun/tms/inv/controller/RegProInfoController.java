package com.hisun.tms.inv.controller;


import com.hisun.tms.inv.model.InvQueryInfParamInput;
import com.hisun.tms.inv.model.ProInfo;


import com.hisun.tms.inv.service.ProInfoService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/inv/regproctrl")
public class RegProInfoController {

    private static final Logger logger = LoggerFactory.getLogger(RegProInfoController.class);

    @Autowired
    private ProInfoService proInfoService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/invmgr/regproctrl') or hasRole('ROLE_ADMIN')")
    public ModelAndView ProInfoList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("inv/proinfo/regindex");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/regproctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<ProInfo> findAll(@Valid @RequestBody InvQueryInfParamInput input) {
        return proInfoService.regfindAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/regproctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<ProInfo> add(@Valid @RequestBody DatatablesEditorRequest<ProInfo> datatablesEditorRequest) {
        Iterable<ProInfo> iterable = datatablesEditorRequest.getData().values();
        proInfoService.save(iterable.iterator().next());
        DatatablesEditorResponse<ProInfo> datatablesEditorResponse = new DatatablesEditorResponse<ProInfo>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/regproctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<ProInfo> modify(@Valid @RequestBody DatatablesEditorRequest<ProInfo> datatablesEditorRequest) {
        Map<String, ProInfo> ProInfoMap = datatablesEditorRequest.getData();
        Map<String, ProInfo> mapData = proInfoService.modify(ProInfoMap);
        DatatablesEditorResponse<ProInfo> datatablesEditorResponse = new DatatablesEditorResponse<ProInfo>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/regproctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> delete(@Valid @RequestBody DatatablesEditorRequest<ProInfo> datatablesEditorRequest) {
        Iterable<ProInfo> iterable = datatablesEditorRequest.getData().values();
        return proInfoService.delete(iterable);
    }
}
