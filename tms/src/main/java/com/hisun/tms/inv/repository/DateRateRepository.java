package com.hisun.tms.inv.repository;

import com.hisun.tms.inv.model.DateRateInfo;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface DateRateRepository extends JpaRepository<DateRateInfo, String> {

	List<DateRateInfo> findByProIdAndRateDateBetween(@Param("proId") String proId,@Param("ubeginDate") LocalDate ubeginDate,@Param("uendDate") LocalDate uendDate);
}
