package com.hisun.tms.inv.dao;

import com.hisun.tms.inv.model.InvFeeDetailDO;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 理财收益
 * <AUTHOR>
 * @date 2017年7月7日
 * @time 上午 11:39:21
 *
 */
public interface InvFeeDetailDao{

    /**
     * 理财利息发放日明细查询
     * @param invGrantDt
     *         利息发放日
     * @param proId
     *         理财产品编号
     * @return
     */
    List<InvFeeDetailDO> getInvFeeDetailDOList(@Param("beginDt") LocalDate beginDt,
            @Param("endDt") LocalDate endDt,
            @Param("pageBegin") Integer pageBegin,
            @Param("pageEnd") Integer pageEnd);
    
    int getInvFeeDetailDOListTotNum(
            @Param("beginDt") LocalDate beginDt,
            @Param("endDt") LocalDate endDt);

	BigDecimal queryTotalFeeAmt(@Param("beginDt") LocalDate beginDt,
            @Param("endDt") LocalDate endDt);
	
	InvFeeDetailDO getProInfo(@Param("proId") String proId,@Param("queryDt") LocalDate queryDt);

	BigDecimal queryRegTotalFeeAmt(@Param("beginDt") LocalDate beginDt,
            @Param("endDt") LocalDate endDt);

	List<InvFeeDetailDO> getRegInvFeeDetailDOList(@Param("beginDt") LocalDate beginDt,
            @Param("endDt") LocalDate endDt,
            @Param("pageBegin") Integer pageBegin,
            @Param("pageEnd") Integer pageEnd);

	int getRegInvFeeDetailDOListTotNum(@Param("beginDt") LocalDate beginDt,
            @Param("endDt") LocalDate endDt);
}