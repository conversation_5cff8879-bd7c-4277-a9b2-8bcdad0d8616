package com.hisun.tms.inv.service;

import com.hisun.tms.inv.model.DateRateInfo;
import com.hisun.tms.inv.model.InvQueryInfParamInput;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface DateRateService {

    DataTablesOutput<DateRateInfo> findAll(InvQueryInfParamInput invQueryInfParamInput);

	Map<String, String> queryProInfo(String proId);

	Map<String, String> modifyProInfo(String proId, String ubeginDate, String uendDate,String rate);
}