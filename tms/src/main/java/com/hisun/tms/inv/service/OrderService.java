package com.hisun.tms.inv.service;

import com.hisun.tms.inv.model.OrderInf;
import com.hisun.tms.inv.model.InvQueryInfParamInput;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface OrderService {

    DataTablesOutput<OrderInf> findAll(InvQueryInfParamInput invQueryInfParamInput);

	DataTablesOutput<OrderInf> regfindAll(InvQueryInfParamInput invQueryInfParamInput);
}