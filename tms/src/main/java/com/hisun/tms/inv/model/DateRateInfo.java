package com.hisun.tms.inv.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.math.BigDecimal;
import java.time.LocalDate;
import javax.persistence.*;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "inv_pro_date_rate")
public class DateRateInfo extends AbstractEntityTmpe {
	/**
	 * 用户理财id
	 */
    @Id
    @Column(name = "pro_id")
    private String proId;
    /**
     * 理财产品名称
     */
    @Column(name = "pro_name")
    private String proName;
    /**
     * @Fields rate 利率
     */
    @Column(name = "rate", precision = 15, scale = 4)
    private BigDecimal rate;
    /**
     * @Fields rateDate 利率日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "rate_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate rateDate;

    /**
     * @Fields 操作员
     */
    @Column(name = "create_user_id")
    private String createUserId;

	public String getProId() {
		return proId;
	}

	public void setProId(String proId) {
		this.proId = proId;
	}

	public String getProName() {
		return proName;
	}

	public void setProName(String proName) {
		this.proName = proName;
	}

	public BigDecimal getRate() {
		return rate;
	}

	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	public LocalDate getRateDate() {
		return rateDate;
	}

	public void setRateDate(LocalDate rateDate) {
		this.rateDate = rateDate;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	@Override
    public String toString() {
        return "DateRateInfo{" +
                "proId='" + proId + '\'' +
                ", proName='" + proName + '\'' +
                ", rate=" + rate + 
                ", rateDate=" + rateDate + 
                ", createUserId='" + createUserId + '\'' +
                '}';
    }
}
