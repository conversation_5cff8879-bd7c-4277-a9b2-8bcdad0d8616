package com.hisun.tms.inv.service;

import com.google.common.base.Function;



import com.google.common.collect.Collections2;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.inv.common.ProInfoConstants;
import com.hisun.tms.inv.dao.IInvProInfoDao;
import com.hisun.tms.inv.model.InvProInfoDO;
import com.hisun.tms.inv.model.InvQueryInfParamInput;
import com.hisun.tms.inv.model.ProInfo;
import com.hisun.tms.inv.repository.DatatablesProInfoRepository;
import com.hisun.tms.inv.repository.ProInfoRepository;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.urm.model.UserBasicInfo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("proInfoService")
public class ProInfoServiceImpl implements ProInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProInfoServiceImpl.class);

    @Autowired
    private DatatablesProInfoRepository datatablesProInfoRepository;
    @Resource
    private IInvProInfoDao iInvProInfoDao;
    @Autowired
    private ProInfoRepository proInfoRepository;

    @Override
    public void save(ProInfo proInfo) {
    	InvProInfoDO invProInfoDO=new InvProInfoDO();
    	BeanUtils.copyProperties(proInfo,invProInfoDO);
    	LocalDateTime nowTime=LocalDateTime.now();
    	invProInfoDO.setCreateTime(nowTime);
    	invProInfoDO.setModifyTime(nowTime);
    	iInvProInfoDao.insert(invProInfoDO);
    }
//
    @Override
    public Map<String, ProInfo> modify(Map<String, ProInfo> proInfoMap) {

        List<ProInfo> proInfos = proInfoMap.values().stream().collect(Collectors.toList());

        logger.debug("recive from web: {}", proInfos.toString());

        Collection<String> ids = Collections2.transform(
        		proInfos,
                new Function<ProInfo, String>() {
                    @Override
                    public String apply(final ProInfo proInfo) {
                        return proInfo.getProId();
                    }
                }
        );
        logger.debug("query params: {}", ids.toString());
        List<ProInfo> proInfoToUpdate = proInfoRepository.findAll(ids);
        logger.debug("query from db: {}", proInfoToUpdate.toString());

        for (int i = 0; i < proInfos.size(); i++) {
            BeanUtils.copyProperties(proInfos.get(i), proInfoToUpdate.get(i), ProInfoConstants.IGNORE_AUDIT_COLUMN);
        }

        logger.debug("after copy with audit column ignore: {}", proInfoToUpdate.toString());
        InvProInfoDO invProInfoDO=new InvProInfoDO();
        BeanUtils.copyProperties(proInfoToUpdate.iterator().next(),invProInfoDO);
        iInvProInfoDao.update(invProInfoDO);
        List<ProInfo> list = proInfoToUpdate;

        logger.debug("after update: {}", list.toString());

        Map<String, ProInfo> map = list.stream().collect(Collectors.toMap(ProInfo::getProId, java.util.function.Function.identity()));
        Map<String, ProInfo> mapData = map.entrySet().stream().collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
    }

    @Override
    public Map<String, String> delete(Iterable<ProInfo> entities) {
    	Map<String,String> map = new HashMap<String,String>();
    	//如果产品已上架则不允许删除
    	LocalDateTime now = LocalDateTime.now();
    	if (entities.iterator().next().getProEffTm().isBefore(now) && entities.iterator().next().getCollectCnt().intValue() > 0) {
    		map.put("result", "FAIL");
        }else{
        	iInvProInfoDao.delete(entities.iterator().next().getProId());
        }
    	return map;
    }

    @Override
    @Transactional("invTransactionManager")
    public DataTablesOutput<ProInfo> findAll(InvQueryInfParamInput input) {
    	String proId = input.getExtra_search().get("proId").trim();
		String proName = input.getExtra_search().get("proName").trim();
		String beginDateStr = input.getExtra_search().get("beginDate").trim();
		String endDateStr = input.getExtra_search().get("endDate").trim();
		String rate = input.getExtra_search().get("rate").trim();
		LocalDateTime beginDate = null;
		//订单结束日期
		LocalDateTime endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim().concat("000000");
		    beginDate = DateTimeUtils.parseLocalDateTime(beginDateStr);
		}else{
			beginDate = LocalDateTime.now().minusDays(90); //查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim().concat("000000");
		    endDate = DateTimeUtils.parseLocalDateTime(endDateStr);
		}else{
			endDate = LocalDateTime.now().plusHours(2);
		}
    	Specification<ProInfo> proInfoQueryParam=Specifications.<ProInfo>and()
    			.eq(JudgeUtils.isNotBlank(proId), "proId", proId)
    			.like(JudgeUtils.isNotBlank(proName), "proName", "%"+proName+"%")
    			.eq("proType", "D")
    			.eq(JudgeUtils.isNotBlank(rate), "rate", rate)
    			.between("proEffTm", new Range<>(beginDate, endDate))
                .build();
    	DataTablesOutput<ProInfo> dataTablesOutput = datatablesProInfoRepository.findAll(input,proInfoQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }
    /**
     * 查询定期理财产品
     */
	@Override
	public DataTablesOutput<ProInfo> regfindAll(InvQueryInfParamInput input) {
		String proId = input.getExtra_search().get("proId").trim();
		String proName = input.getExtra_search().get("proName").trim();
		String beginDateStr = input.getExtra_search().get("beginDate").trim();
		String endDateStr = input.getExtra_search().get("endDate").trim();
		String rate = input.getExtra_search().get("rate").trim();
		LocalDateTime beginDate = null;
		//订单结束日期
		LocalDateTime endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim().concat("000000");
		    beginDate = DateTimeUtils.parseLocalDateTime(beginDateStr);
		}else{
			beginDate = LocalDateTime.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim().concat("000000");
		    endDate = DateTimeUtils.parseLocalDateTime(endDateStr);
		}else{
			endDate = LocalDateTime.now().plusHours(2);
		}
    	Specification<ProInfo> proInfoQueryParam=Specifications.<ProInfo>and()
    			.eq(JudgeUtils.isNotBlank(proId), "proId", proId)
    			.like(JudgeUtils.isNotBlank(proName), "proName", "%"+proName+"%")
    			.ne("proType", "D")
    			.eq(JudgeUtils.isNotBlank(rate), "rate", rate)
    			.between("proEffTm", new Range<>(beginDate, endDate))
                .build();
    	DataTablesOutput<ProInfo> dataTablesOutput = datatablesProInfoRepository.findAll(input,proInfoQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
	}
}
