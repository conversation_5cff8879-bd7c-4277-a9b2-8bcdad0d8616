package com.hisun.tms.inv.controller;


import com.hisun.tms.inv.model.InvFeeDetailDO;
import com.hisun.tms.inv.model.InvQueryInfParamInput;
import com.hisun.tms.inv.service.FeeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

import javax.validation.Valid;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/inv/feectrl")
public class FeeController {

    private static final Logger logger = LoggerFactory.getLogger(FeeController.class);

    @Autowired
    private FeeService feeService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/invmgr/feectrl') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("inv/feeinfo/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/feectrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<InvFeeDetailDO> findAll(@Valid @RequestBody InvQueryInfParamInput invQueryInfParamInput) {
        return feeService.findAll(invQueryInfParamInput);
    }
    
    @PostMapping(value = "query")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/feectrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> queryTotalFeeAmt(@RequestParam(value = "beginDateStr", required = false) String beginDateStr,@RequestParam(value = "endDateStr", required = false) String endDateStr) {
        return feeService.queryTotalFeeAmt(beginDateStr,endDateStr);
    }
}
