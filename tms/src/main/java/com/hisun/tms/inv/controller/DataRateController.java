package com.hisun.tms.inv.controller;


import com.hisun.tms.inv.model.DateRateInfo;
import com.hisun.tms.inv.model.InvFeeDetailDO;
import com.hisun.tms.inv.model.InvQueryInfParamInput;
import com.hisun.tms.inv.service.DateRateService;
import com.hisun.tms.inv.service.FeeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

import javax.validation.Valid;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/inv/dataratectrl")
public class DataRateController {

    private static final Logger logger = LoggerFactory.getLogger(DataRateController.class);

    @Autowired
    private DateRateService dateRateService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/invmgr/dataratectrl') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("inv/daterate/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/dataratectrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<DateRateInfo> findAll(@Valid @RequestBody InvQueryInfParamInput invQueryInfParamInput) {
        return dateRateService.findAll(invQueryInfParamInput);
    }
    
    @PostMapping(value = "/query")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/dataratectrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> queryProInfo(@RequestParam(value = "proId", required = true) String proId) {
        return dateRateService.queryProInfo(proId);
    }
    
    @PostMapping(value = "setrate")
    @PreAuthorize("hasPermission('','/busmgr/invmgr/dataratectrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> modifyProInfo(@RequestParam(value = "proId", required = true) String proId,@RequestParam(value = "ubeginDate", required = true) String ubeginDate,@RequestParam(value = "uendDate", required = true) String uendDate,@RequestParam(value = "rate", required = true) String rate) {
        return dateRateService.modifyProInfo(proId,ubeginDate,uendDate,rate);
    }
}
