/*
 * @ClassName IDateRateInDao
 * @Description 
 * @version 1.0
 * @Date 2017-09-06 11:27:28
 */
package com.hisun.tms.inv.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.tms.inv.model.DateRateInfo;
import com.hisun.tms.inv.model.DateRateInfoDO;

import java.time.LocalDate;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IDateRateInDao extends BaseDao<DateRateInfoDO> {

	int deleteByProIdAndRateDateBetween(@Param("proId")String proId,@Param("beginDt") LocalDate beginDate,@Param("endDt") LocalDate endDate);

	List<DateRateInfo> getDateRateList(@Param("proId") String proId, @Param("beginDt") LocalDate beginDate,@Param("endDt") LocalDate endDate,@Param("pageBegin") Integer pageBegin,@Param("pageEnd") Integer pageEnd);

	int getDateRateTotNum(@Param("proId") String proId,@Param("beginDt") LocalDate beginDate,@Param("endDt") LocalDate endDate);
}