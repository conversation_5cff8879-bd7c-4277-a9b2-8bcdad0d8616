/*
 * @ClassName InvProInfoDO
 * @Description 
 * @version 1.0
 * @Date 2017-09-13 14:37:44
 */
package com.hisun.tms.inv.model;


import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
public class InvProInfoDO extends BaseDO {
    /**
     * @Fields proId 理财产品编号
     */
    private String proId;
    /**
     * @Fields proName 理财产品名称
     */
    private String proName;
    /**
     * @Fields proType 计息类型 D:按日计息,M:按月计息,S:按季计息,Y:按年计息(与利率关联)
     */
    private String proType;
    /**
     * @Fields totalAmt 总募集金额
     */
    private BigDecimal totalAmt;
    /**
     * @Fields collectAmt 已募集金额
     */
    private BigDecimal collectAmt;
    /**
     * @Fields collectCnt 已募集笔数
     */
    private Integer collectCnt;
    /**
     * @Fields rate 利率
     */
    private BigDecimal rate;
    /**
     * @Fields invTerm 投资期限
     */
    private Integer invTerm;
    /**
     * @Fields proEffTm 理财产品上架时间
     */
    private LocalDateTime proEffTm;
    /**
     * @Fields proExpTm 理财产品下架时间
     */
    private LocalDateTime proExpTm;
    /**
     * @Fields proDesc 产品描述
     */
    private String proDesc;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    
    /**
     * @Fields riskDesc 风险提示
     */
    private String riskDesc;
    
    /**
     * @Fields rulesDesc 交易规则
     */
    private String rulesDesc;
    
    /**
     * @Fields cloPerBegin 封闭期开始
     */
    private LocalDateTime cloPerBegin;
    
    /**
     * @Fields cloPerEnd 封闭期结束
     */
    private LocalDateTime cloPerEnd;

    /**
     * @Fields subscriptRate 认购期赎回费率
     */
    private BigDecimal subscriptRate;
    
    /**
     * @Fields cloPerRate 封闭期赎回费率
     */
    private BigDecimal cloPerRate;
    
    /**
     * @Fields investAmt 起投金额
     */
    private BigDecimal investAmt;
    
    /**
     * @Fields earnTm 收益日期
     */
    private LocalDateTime earnTm;

    public String getProId() {
        return proId;
    }

    public void setProId(String proId) {
        this.proId = proId;
    }

    public String getProName() {
        return proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getProType() {
        return proType;
    }

    public void setProType(String proType) {
        this.proType = proType;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getCollectAmt() {
        return collectAmt;
    }

    public void setCollectAmt(BigDecimal collectAmt) {
        this.collectAmt = collectAmt;
    }

    public Integer getCollectCnt() {
        return collectCnt;
    }

    public void setCollectCnt(Integer collectCnt) {
        this.collectCnt = collectCnt;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Integer getInvTerm() {
        return invTerm;
    }

    public void setInvTerm(Integer invTerm) {
        this.invTerm = invTerm;
    }

    public LocalDateTime getProEffTm() {
        return proEffTm;
    }

    public void setProEffTm(LocalDateTime proEffTm) {
        this.proEffTm = proEffTm;
    }

    public LocalDateTime getProExpTm() {
        return proExpTm;
    }

    public void setProExpTm(LocalDateTime proExpTm) {
        this.proExpTm = proExpTm;
    }

    public String getProDesc() {
        return proDesc;
    }

    public void setProDesc(String proDesc) {
        this.proDesc = proDesc;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

	public String getRiskDesc() {
		return riskDesc;
	}

	public void setRiskDesc(String riskDesc) {
		this.riskDesc = riskDesc;
	}

	public String getRulesDesc() {
		return rulesDesc;
	}

	public void setRulesDesc(String rulesDesc) {
		this.rulesDesc = rulesDesc;
	}

	public LocalDateTime getCloPerBegin() {
		return cloPerBegin;
	}

	public void setCloPerBegin(LocalDateTime cloPerBegin) {
		this.cloPerBegin = cloPerBegin;
	}

	public LocalDateTime getCloPerEnd() {
		return cloPerEnd;
	}

	public void setCloPerEnd(LocalDateTime cloPerEnd) {
		this.cloPerEnd = cloPerEnd;
	}

	public BigDecimal getSubscriptRate() {
		return subscriptRate;
	}

	public void setSubscriptRate(BigDecimal subscriptRate) {
		this.subscriptRate = subscriptRate;
	}

	public BigDecimal getCloPerRate() {
		return cloPerRate;
	}

	public void setCloPerRate(BigDecimal cloPerRate) {
		this.cloPerRate = cloPerRate;
	}

	public BigDecimal getInvestAmt() {
		return investAmt;
	}

	public void setInvestAmt(BigDecimal investAmt) {
		this.investAmt = investAmt;
	}

	public LocalDateTime getEarnTm() {
		return earnTm;
	}

	public void setEarnTm(LocalDateTime earnTm) {
		this.earnTm = earnTm;
	}
}