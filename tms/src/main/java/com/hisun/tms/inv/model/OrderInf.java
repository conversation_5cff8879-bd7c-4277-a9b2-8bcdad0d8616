package com.hisun.tms.inv.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "inv_order")
public class OrderInf extends AbstractEntityTmpe {
	/**
     * @Fields invOrderNo 订单号
     */
    @Id
    @Column(name = "inv_order_no")
    private String invOrderNo;

    /**
     * @Fields mobileNo 手机号
     */
    @Column(name = "mobile_no")
    private String mobileNo;
    
    /**
     * @Fields invOrderDt 订单日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "inv_order_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate invOrderDt;

    /**
     * @Fields invOrderTm 订单时间
     */
    @Convert(converter = Jsr310JpaConverters.LocalTimeConverter.class)
    @Column(name = "inv_order_tm")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime invOrderTm;
    /**
     * @Fields userId 内部用户号
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * @Fields proId 理财产品编码
     */
    @Column(name = "pro_id")
    private String proId;

    /**
     * @Fields proRate 产品利率
     */
    @Column(name = "pro_rate", precision = 15, scale = 4)
    private BigDecimal proRate;

    /**
     * @Fields proType 计息类型 D:按日计息,M:按月计息,S:按季计息,Y:按年计息(与利率关联)
     */
    @Column(name = "pro_type")
    private String proType;
    
    /**
     * @Fields ccy 币种
     */
    @Column(name = "ccy")
    private String ccy;

    /**
     * @Fields orderAmt 订单金额
     */
    @Column(name = "order_amt", precision = 15, scale = 2)
    private BigDecimal orderAmt;

    /**
     * @Fields orderType 订单类型 1:购入,2:自主转出,3:到期赎回
     */
    @Column(name = "order_type")
    private String orderType;

    /**
     * @Fields orderSts 订单状态 S：成功,F：失败
     */
    @Column(name = "order_sts")
    private String orderSts;
    
    /**
     * @Fields proName产品名称
     */
    @Column(name = "pro_name")
    private String proName;
    
    /**
     * @Fields invTerm产品周期
     */
    @Column(name = "inv_term")
    private String invTerm;
    
    /**
     * @Fields cloPerBegin 结束时间
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "clo_per_begin")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate cloPerBegin;
    
    /**
     * @Fields cloPerEnd 结束时间
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "clo_per_end")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate cloPerEnd;

	public String getInvOrderNo() {
		return invOrderNo;
	}

	public void setInvOrderNo(String invOrderNo) {
		this.invOrderNo = invOrderNo;
	}

	public LocalDate getInvOrderDt() {
		return invOrderDt;
	}

	public void setInvOrderDt(LocalDate invOrderDt) {
		this.invOrderDt = invOrderDt;
	}

	public LocalTime getInvOrderTm() {
		return invOrderTm;
	}

	public void setInvOrderTm(LocalTime invOrderTm) {
		this.invOrderTm = invOrderTm;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getProId() {
		return proId;
	}

	public void setProId(String proId) {
		this.proId = proId;
	}

	public BigDecimal getProRate() {
		return proRate;
	}

	public void setProRate(BigDecimal proRate) {
		this.proRate = proRate;
	}

	public String getProType() {
		return proType;
	}

	public void setProType(String proType) {
		this.proType = proType;
	}

	public String getCcy() {
		return ccy;
	}

	public void setCcy(String ccy) {
		this.ccy = ccy;
	}

	public BigDecimal getOrderAmt() {
		return orderAmt;
	}

	public void setOrderAmt(BigDecimal orderAmt) {
		this.orderAmt = orderAmt;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getOrderSts() {
		return orderSts;
	}

	public void setOrderSts(String orderSts) {
		this.orderSts = orderSts;
	}

	
	public String getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}

	public String getProName() {
		return proName;
	}

	public void setProName(String proName) {
		this.proName = proName;
	}

	public String getInvTerm() {
		return invTerm;
	}

	public void setInvTerm(String invTerm) {
		this.invTerm = invTerm;
	}

	public LocalDate getCloPerBegin() {
		return cloPerBegin;
	}

	public void setCloPerBegin(LocalDate cloPerBegin) {
		this.cloPerBegin = cloPerBegin;
	}

	public LocalDate getCloPerEnd() {
		return cloPerEnd;
	}

	public void setCloPerEnd(LocalDate cloPerEnd) {
		this.cloPerEnd = cloPerEnd;
	}

	@Override
    public String toString() {
        return "OrderInf{" +
                "invOrderNo='" + invOrderNo + '\'' +
                "mobileNo='" + mobileNo + '\'' +
                ", invOrderDt=" + invOrderDt +
                ", invOrderTm=" + invOrderTm +
                ", userId='" + userId + '\'' +
                ", proRate=" + proRate +
                ", proType='" + proType + '\'' +
                ", ccy='" + ccy + '\'' +
                ", orderAmt=" + orderAmt +
                ", orderType='" + orderType + '\'' +
                ", orderSts='" + orderSts + '\'' +
                '}';
    }
}
