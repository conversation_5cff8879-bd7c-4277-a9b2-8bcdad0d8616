package com.hisun.tms.inv.service;



import com.hisun.lemon.common.utils.DateTimeUtils;



import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.inv.dao.IDateRateInDao;
import com.hisun.tms.inv.dao.InvFeeDetailDao;
import com.hisun.tms.inv.model.DateRateInfo;
import com.hisun.tms.inv.model.DateRateInfoDO;
import com.hisun.tms.inv.model.InvFeeDetailDO;
import com.hisun.tms.inv.model.InvQueryInfParamInput;
import com.hisun.tms.inv.model.ProInfo;
import com.hisun.tms.inv.repository.DateRateRepository;
import com.hisun.tms.inv.repository.ProInfoRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("dateRateService")
public class DateRateServiceImpl implements DateRateService {

    private static final Logger logger = LoggerFactory.getLogger(DateRateServiceImpl.class);

    @Resource
    private InvFeeDetailDao invFeeDetailDao;
    @Autowired
    private ProInfoRepository proInfoRepository;
    @Autowired
    private DateRateRepository dateRateRepository;
    @Resource
    private IDateRateInDao iDateRateInDao;

    @Override
    @Transactional("invTransactionManager")
    public DataTablesOutput<DateRateInfo> findAll(InvQueryInfParamInput invQueryInfParamInput) {
    	DataTablesOutput<DateRateInfo> dataTablesOutput = new DataTablesOutput<DateRateInfo>();
        List<DateRateInfo> dateRateInfoList = new ArrayList<>() ;
    	String proId = invQueryInfParamInput.getExtra_search().get("proId").trim();
		String beginDateStr = invQueryInfParamInput.getExtra_search().get("beginDate");
		String endDateStr = invQueryInfParamInput.getExtra_search().get("endDate");
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if(JudgeUtils.isBlank(proId)){
			proId=null;
		}
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
		//从第几条记录开始获取数据
        Integer pageBegin = invQueryInfParamInput.getStart();
        //获取多少条记录
        Integer pageEnd = invQueryInfParamInput.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;
        try {
        	dateRateInfoList = iDateRateInDao.getDateRateList(proId,beginDate, endDate, pageBegin, pageEnd);
            totNum = iDateRateInDao.getDateRateTotNum(proId,beginDate, endDate);
        } catch (Exception e){
            e.printStackTrace();
        }
        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(dateRateInfoList)){
            dataTablesOutput.setData(new ArrayList<DateRateInfo>());
        } else {
            dataTablesOutput.setData(dateRateInfoList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(invQueryInfParamInput.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

	@Override
	public Map<String, String> queryProInfo(String proId) {
		LocalDate queryDt=LocalDate.now().minusDays(1);
		InvFeeDetailDO invFeeDetailDO=invFeeDetailDao.getProInfo(proId, queryDt);
		Map<String,String> map =  new HashMap<String,String>();
		if(JudgeUtils.isNull(invFeeDetailDO)){
			invFeeDetailDO=new InvFeeDetailDO();
			invFeeDetailDO.setProRate(BigDecimal.valueOf(0));
			invFeeDetailDO.setSuminvAmt(BigDecimal.valueOf(0));
			invFeeDetailDO.setSumfeeAmt(BigDecimal.valueOf(0));
		}
		map.put("proRate", invFeeDetailDO.getProRate().toString());
		map.put("suminvAmt", invFeeDetailDO.getSuminvAmt().toString());
		map.put("totalFeeAmt", invFeeDetailDO.getSumfeeAmt().toString());
		return map;
	}

	@Override
	public Map<String, String> modifyProInfo(String proId, String ubeginDate, String uendDate,String rate) {
		//检查理财产品是否存在
		Map<String,String> map =  new HashMap<String,String>();
		ProInfo proinfo=proInfoRepository.getOne(proId);
		logger.info(proinfo.toString());
		if(JudgeUtils.isNull(proinfo)){
			map.put("result","proinfoIsNull");
		}else{
			LocalDate beginDate=null;
			LocalDate endDate=null;
			if (JudgeUtils.isNotBlank(ubeginDate)){
				ubeginDate = ubeginDate.replaceAll("-","").trim();
			    beginDate = DateTimeUtils.parseLocalDate(ubeginDate);
			}
			if (JudgeUtils.isNotBlank(uendDate)){
				uendDate = uendDate.replaceAll("-","").trim();
			    endDate = DateTimeUtils.parseLocalDate(uendDate);
			}
			//先删除再插入
			List<DateRateInfo> dateRateInfos=dateRateRepository.findByProIdAndRateDateBetween(proId,beginDate,endDate);
			if(!JudgeUtils.isEmpty(dateRateInfos)){
				int result=iDateRateInDao.deleteByProIdAndRateDateBetween(proId,beginDate,endDate);
			}
			//循环插入数据
			LocalDate numDate=beginDate;
			DateRateInfoDO dateRateInfoDO=new DateRateInfoDO();
			dateRateInfoDO.setProId(proId);
			dateRateInfoDO.setProName(proinfo.getProName());
			dateRateInfoDO.setRate(BigDecimal.valueOf(Double.parseDouble(rate)));
			dateRateInfoDO.setCreateUserId(getOperatorName());
			dateRateInfoDO.setCreateTime(LocalDateTime.now());
			dateRateInfoDO.setModifyTime(LocalDateTime.now());
		    while(numDate.isBefore(endDate.plusDays(1))){
		    	dateRateInfoDO.setRateDate(numDate);
		    	int iresult=iDateRateInDao.insert(dateRateInfoDO);
		    	numDate=numDate.plusDays(1);
		    }
		    map.put("msgCd","INV00000");
	    }
		return map;
	}
	private String getOperatorName(){
        org.springframework.security.core.userdetails.User securityUser = (org.springframework.security.core.userdetails.User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return securityUser.getUsername();
    }
}
