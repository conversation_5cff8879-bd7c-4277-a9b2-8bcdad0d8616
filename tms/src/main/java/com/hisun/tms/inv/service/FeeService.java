package com.hisun.tms.inv.service;

import com.hisun.tms.inv.model.InvFeeDetailDO;
import com.hisun.tms.inv.model.InvQueryInfParamInput;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface FeeService {

    DataTablesOutput<InvFeeDetailDO> findAll(InvQueryInfParamInput invQueryInfParamInput);

	Map<String, String> queryTotalFeeAmt(String beginDateStr, String endDateStr);

	DataTablesOutput<InvFeeDetailDO> findRegAll(InvQueryInfParamInput invQueryInfParamInput);

	Map<String, String> queryRegTotalFeeAmt(String beginDateStr, String endDateStr);
}