package com.hisun.tms.inv.model;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * InterestDO 收益明细
 * <AUTHOR>
 * @LocalDateTime 2017年7月7日
 * @time 上午 11:39:21
 *
 */
public class InvFeeDetailDO{

    /**
     * 内部用户号
     */
    private String userId;

    /**
     * 理财产品编码
     */
    private String proId;

    /**
     * 产品利率
     */
    private BigDecimal proRate;

    /**
     * 理财金额
     */
    private BigDecimal invAmt;
    /**
     * 总理财金额
     */
    private BigDecimal suminvAmt;

    /**
     * 理财收益金额(D-1)
     */
    private BigDecimal feeAmt;
    /**
     * 总收益金额
     */
    private BigDecimal sumfeeAmt;
    /**
     * 时间段内总收益金额
     */
    private BigDecimal totalFeeAmt;

    /**
     * 收益发放日期
     */
    private LocalDate invGrantDt;

    /**
     * 收益发放确认日期
     */
    private LocalDate invConfirmDt;

    /**
     * 理财收益发放标志1:未发放，2:已发放
     */
    private String invGrantFlag;

    /**
     * 时间戳
     */
    private LocalDateTime tmSmp;

    private Integer cnt;

    public BigDecimal getTotalFeeAmt() {
		return totalFeeAmt;
	}

	public void setTotalFeeAmt(BigDecimal totalFeeAmt) {
		this.totalFeeAmt = totalFeeAmt;
	}

	public BigDecimal getSuminvAmt() {
		return suminvAmt;
	}

	public void setSuminvAmt(BigDecimal suminvAmt) {
		this.suminvAmt = suminvAmt;
	}

	public BigDecimal getSumfeeAmt() {
		return sumfeeAmt;
	}

	public void setSumfeeAmt(BigDecimal sumfeeAmt) {
		this.sumfeeAmt = sumfeeAmt;
	}

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getProId() {
        return proId;
    }

    public void setProId(String proId) {
        this.proId = proId;
    }

    public BigDecimal getProRate() {
        return proRate;
    }

    public void setProRate(BigDecimal proRate) {
        this.proRate = proRate;
    }

    public BigDecimal getInvAmt() {
        return invAmt;
    }

    public void setInvAmt(BigDecimal invAmt) {
        this.invAmt = invAmt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public LocalDate getInvGrantDt() {
        return invGrantDt;
    }

    public void setInvGrantDt(LocalDate invGrantDt) {
        this.invGrantDt = invGrantDt;
    }

    public LocalDate getInvConfirmDt() {
        return invConfirmDt;
    }

    public void setInvConfirmDt(LocalDate invConfirmDt) {
        this.invConfirmDt = invConfirmDt;
    }

    public String getInvGrantFlag() {
        return invGrantFlag;
    }

    public void setInvGrantFlag(String invGrantFlag) {
        this.invGrantFlag = invGrantFlag;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public Integer getCnt() {
        return cnt;
    }

    public void setCnt(Integer cnt) {
        this.cnt = cnt;
    }
}