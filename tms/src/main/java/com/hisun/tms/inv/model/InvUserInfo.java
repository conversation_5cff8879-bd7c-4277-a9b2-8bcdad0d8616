package com.hisun.tms.inv.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * Created by chen on 8/29 0029.
 */
@Entity
@Table(name = "inv_user_info")
public class InvUserInfo {
    @Id
    @Column(name = "user_id")
    private String userId;
    @Column(name = "total_current_amt")
    private BigDecimal toltalCurrentAmt;

    public BigDecimal getToltalCurrentAmt() {
        return toltalCurrentAmt;
    }

    public void setToltalCurrentAmt(BigDecimal toltalCurrentAmt) {
        this.toltalCurrentAmt = toltalCurrentAmt;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
