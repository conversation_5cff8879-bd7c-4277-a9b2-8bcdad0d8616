package com.hisun.tms.inv.repository;

import com.hisun.tms.inv.model.InvUserInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface InvUserInfoRepository extends JpaRepository<InvUserInfo, String> {

    @Query(value = "select user_id,total_current_amt from inv_user_info m where user_id = :userId ", nativeQuery=true  )
    List<InvUserInfo> findBalanceByUserId(@Param("userId") String userId);
}
