package com.hisun.tms.inv.service;
import com.hisun.lemon.common.utils.DateTimeUtils;


import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.inv.dao.InvFeeDetailDao;
import com.hisun.tms.inv.model.InvFeeDetailDO;
import com.hisun.tms.inv.model.InvQueryInfParamInput;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Transactional
@Service("feeService")
public class FeeServiceImpl implements FeeService {

    private static final Logger logger = LoggerFactory.getLogger(FeeServiceImpl.class);

    @Resource
    private InvFeeDetailDao invFeeDetailDao;

    @Override
    public DataTablesOutput<InvFeeDetailDO> findAll(InvQueryInfParamInput invQueryInfParamInput) {
    	DataTablesOutput<InvFeeDetailDO> dataTablesOutput = new DataTablesOutput<InvFeeDetailDO>();
        List<InvFeeDetailDO> invFeeDetailDOList = new ArrayList<>() ;
    	String beginDateStr = invQueryInfParamInput.getExtra_search().get("beginDate");
		String endDateStr = invQueryInfParamInput.getExtra_search().get("endDate");
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
		//从第几条记录开始获取数据
        Integer pageBegin = invQueryInfParamInput.getStart();
        //获取多少条记录
        Integer pageEnd = invQueryInfParamInput.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;
        try {
        	invFeeDetailDOList = invFeeDetailDao.getInvFeeDetailDOList(beginDate, endDate, pageBegin, pageEnd);
            totNum = invFeeDetailDao.getInvFeeDetailDOListTotNum(beginDate, endDate);
        } catch (Exception e){
            e.printStackTrace();
        }
        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(invFeeDetailDOList)){
            dataTablesOutput.setData(new ArrayList<InvFeeDetailDO>());
        } else {
            dataTablesOutput.setData(invFeeDetailDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(invQueryInfParamInput.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

	@Override
	public Map<String, String> queryTotalFeeAmt(String beginDateStr, String endDateStr) {
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
		String totalFeeAmt=BigDecimal.valueOf(0).toString();
		totalFeeAmt=invFeeDetailDao.queryTotalFeeAmt(beginDate,endDate).toString();
		Map<String,String> map =  new HashMap<String,String>();
		map.put("totalFeeAmt", totalFeeAmt);
		return map;
	}

	@Override
	public DataTablesOutput<InvFeeDetailDO> findRegAll(InvQueryInfParamInput invQueryInfParamInput) {
		DataTablesOutput<InvFeeDetailDO> dataTablesOutput = new DataTablesOutput<InvFeeDetailDO>();
        List<InvFeeDetailDO> invFeeDetailDOList = new ArrayList<>() ;
    	String beginDateStr = invQueryInfParamInput.getExtra_search().get("beginDate");
		String endDateStr = invQueryInfParamInput.getExtra_search().get("endDate");
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
		//从第几条记录开始获取数据
        Integer pageBegin = invQueryInfParamInput.getStart();
        //获取多少条记录
        Integer pageEnd = invQueryInfParamInput.getLength();
        //根据条件查询得到记录的总笔数
        int totNum = 0;
        try {
        	invFeeDetailDOList = invFeeDetailDao.getRegInvFeeDetailDOList(beginDate, endDate, pageBegin, pageEnd);
            totNum = invFeeDetailDao.getRegInvFeeDetailDOListTotNum(beginDate, endDate);
        } catch (Exception e){
            e.printStackTrace();
        }
        //判断列表查询结果是否为空
        if (CollectionUtils.isEmpty(invFeeDetailDOList)){
            dataTablesOutput.setData(new ArrayList<InvFeeDetailDO>());
        } else {
            dataTablesOutput.setData(invFeeDetailDOList);
        }
        dataTablesOutput.setRecordsTotal(totNum);
        dataTablesOutput.setDraw(invQueryInfParamInput.getDraw());
        dataTablesOutput.setRecordsFiltered(totNum);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
	}

	@Override
	public Map<String, String> queryRegTotalFeeAmt(String beginDateStr, String endDateStr) {
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
		String totalFeeAmt=BigDecimal.valueOf(0).toString();
		totalFeeAmt=invFeeDetailDao.queryRegTotalFeeAmt(beginDate,endDate).toString();
		Map<String,String> map =  new HashMap<String,String>();
		map.put("totalFeeAmt", totalFeeAmt);
		return map;
	}
}
    
