package com.hisun.tms.inv.service;



import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.inv.model.OrderInf;
import com.hisun.tms.inv.model.InvQueryInfParamInput;
import com.hisun.tms.inv.repository.DatatablesOrderRepository;
import com.hisun.tms.specifications.Specifications;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.mapping.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("orderService")
public class OrderServiceImpl implements OrderService {

    private static final Logger logger = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Autowired
    private DatatablesOrderRepository datatablesOrderRepository;

    @Override
    @Transactional("invTransactionManager")
    public DataTablesOutput<OrderInf> findAll(InvQueryInfParamInput invQueryInfParamInput) {
    	String mobileNo = invQueryInfParamInput.getExtra_search().get("mobileNo");
		String userId = invQueryInfParamInput.getExtra_search().get("userId");
		String txType = invQueryInfParamInput.getExtra_search().get("txType");
		String beginDateStr = invQueryInfParamInput.getExtra_search().get("beginDate");
		String endDateStr = invQueryInfParamInput.getExtra_search().get("endDate");
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
    	Specification<OrderInf> orderQueryParam=Specifications.<OrderInf>and()
    			.like(JudgeUtils.isNotBlank(mobileNo), "mobileNo", "%"+mobileNo+"%")
    			.like(JudgeUtils.isNotBlank(userId), "userId", "%"+userId+"%")
    			.eq(JudgeUtils.isNotBlank(txType), "orderType", txType)
    			.eq("proType", "D")//按日记息（活期）
                .between("invOrderDt", new Range<>(beginDate, endDate))
                .build();
    	Order orderDt=new Order(5,"desc");
    	Order orderTm=new Order(6,"desc");
    	List<Order> orders=new ArrayList<Order>();
    	orders.add(orderDt);
    	orders.add(orderTm);
    	invQueryInfParamInput.setOrder(orders);
        DataTablesOutput<OrderInf> dataTablesOutput = datatablesOrderRepository.findAll(invQueryInfParamInput,orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

	@Override
	public DataTablesOutput<OrderInf> regfindAll(InvQueryInfParamInput invQueryInfParamInput) {
		String mobileNo = invQueryInfParamInput.getExtra_search().get("mobileNo");
		String userId = invQueryInfParamInput.getExtra_search().get("userId");
		String proId = invQueryInfParamInput.getExtra_search().get("proId");
		String proName = invQueryInfParamInput.getExtra_search().get("proName");
		String orderSts = invQueryInfParamInput.getExtra_search().get("orderSts");
		String invTerm = invQueryInfParamInput.getExtra_search().get("invTerm");
		String proRate = invQueryInfParamInput.getExtra_search().get("proRate");
		String cloPerBeginb = invQueryInfParamInput.getExtra_search().get("cloPerBeginb");
		String cloPerBegine = invQueryInfParamInput.getExtra_search().get("cloPerBegine");
		String cloPerEndb = invQueryInfParamInput.getExtra_search().get("cloPerEndb");
		String cloPerEnde = invQueryInfParamInput.getExtra_search().get("cloPerEnde");
		//开始日期
		LocalDate bbeginDate = null;
		LocalDate bbendDate = null;
		//结束日期
		LocalDate ebeginDate = null;
		LocalDate ebendDate = null;
		//开始日期
		if (JudgeUtils.isNotBlank(cloPerBeginb)){
			cloPerBeginb = cloPerBeginb.replaceAll("-","").trim();
			bbeginDate = DateTimeUtils.parseLocalDate(cloPerBeginb);
		}else{
			bbeginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(cloPerBegine)){
			cloPerBegine = cloPerBegine.replaceAll("-","").trim();
		    bbendDate = DateTimeUtils.parseLocalDate(cloPerBegine);
		}else{
			bbendDate = LocalDate.now();
		}
		//结束日期
		if (JudgeUtils.isNotBlank(cloPerEndb)){
			cloPerEndb = cloPerEndb.replaceAll("-","").trim();
			ebeginDate = DateTimeUtils.parseLocalDate(cloPerEndb);
		}else{
			ebeginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(cloPerEnde)){
			cloPerEnde = cloPerEnde.replaceAll("-","").trim();
			ebendDate = DateTimeUtils.parseLocalDate(cloPerEnde);
		}else{
			ebendDate = LocalDate.now();
		}
    	Specification<OrderInf> orderQueryParam=Specifications.<OrderInf>and()
    			.like(JudgeUtils.isNotBlank(mobileNo), "mobileNo", "%"+mobileNo+"%")
    			.like(JudgeUtils.isNotBlank(userId), "userId", "%"+userId+"%")
    			.eq(JudgeUtils.isNotBlank(proId), "proId", proId)
    			.like(JudgeUtils.isNotBlank(proName), "proName", "%"+proName+"%")
    			.eq(JudgeUtils.isNotBlank(invTerm), "invTerm", invTerm)
    			.eq(JudgeUtils.isNotBlank(orderSts), "orderSts", orderSts)
    			.eq(JudgeUtils.isNotBlank(proRate), "proRate", proRate)
    			.ne("proType", "D")//不为按日记息（则为定期）
                .between("cloPerBegin", new Range<>(bbeginDate, bbendDate))
                .between("cloPerEnd", new Range<>(ebeginDate, ebendDate))
                .build();
    	Order orderDt=new Order(5,"desc");
    	Order orderTm=new Order(6,"desc");
    	List<Order> orders=new ArrayList<Order>();
    	orders.add(orderDt);
    	orders.add(orderTm);
    	invQueryInfParamInput.setOrder(orders);
        DataTablesOutput<OrderInf> dataTablesOutput = datatablesOrderRepository.findAll(invQueryInfParamInput,orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
	}
}
