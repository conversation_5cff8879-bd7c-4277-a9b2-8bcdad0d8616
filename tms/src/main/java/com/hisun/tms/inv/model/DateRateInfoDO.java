/*
 * @ClassName DateRateInfo
 * @Description 
 * @version 1.0
 * @Date 2017-09-06 11:27:28
 */
package com.hisun.tms.inv.model;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class DateRateInfoDO extends BaseDO {
    /**
     * @Fields rate 利率
     */
    private BigDecimal rate;
    /**
     * @Fields rateDate 利率日期
     */
    private LocalDate rateDate;
    /**
     * @Fields proId 理财产品编号
     */
    private String proId;
    /**
     * @Fields proName 理财产品名称
     */
    private String proName;
    /**
     * @Fields createUserId 创建人用户号
     */
    private String createUserId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public LocalDate getRateDate() {
        return rateDate;
    }

    public void setRateDate(LocalDate rateDate) {
        this.rateDate = rateDate;
    }

    public String getProId() {
        return proId;
    }

    public void setProId(String proId) {
        this.proId = proId;
    }

    public String getProName() {
        return proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}