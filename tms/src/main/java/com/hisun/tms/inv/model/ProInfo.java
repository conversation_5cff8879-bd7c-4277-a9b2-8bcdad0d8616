package com.hisun.tms.inv.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "inv_pro_info")
public class ProInfo extends AbstractEntityTmpe {
	/**
	 * 理财产品编号
	 */
    @Id
    @Column(name = "pro_id")
    private String proId;

    /**
     * @Fields proName 理财产品名称
     */
    @Column(name = "pro_name")
    private String proName;

    /**
     * @Fields proType 计息类型 D:按日计息,M:按月计息,S:按季计息,Y:按年计息(与利率关联)
     */
    @Column(name = "pro_type")
    private String proType;

    /**
     * @Fields totalAmt 总募集金额
     */
    @Column(name = "total_amt", precision = 15, scale = 2)
    private BigDecimal totalAmt;

    /**
     * @Fields collectAmt 已募集金额
     */
    @Column(name = "collect_amt", precision = 15, scale = 2)
    private BigDecimal collectAmt;

    /**
     * @Fields collectCnt 已募集笔数
     */
    @Column(name = "collect_cnt")
    private Integer collectCnt;

    /**
     * @Fields rate 利率
     */
    @Column(name = "rate", precision = 15, scale = 4)
    private BigDecimal rate;

    /**
     * @Fields invTerm 投资期限
     */
    @Column(name = "inv_term")
    private Integer invTerm;

    /**
     * @Fields proEffTm 理财产品上架时间
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "pro_eff_tm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime proEffTm;
    
    /**
     * @Fields proExpTm 理财产品下架时间
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "pro_exp_tm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime proExpTm;
    
    /**
     * @Fields proDesc 产品描述
     */
    @Column(name = "pro_desc")
    private String proDesc;
    
    /**
     * @Fields riskDesc 风险提示
     */
    @Column(name = "risk_desc")
    private String riskDesc;
    
    /**
     * @Fields rulesDesc 交易规则
     */
    @Column(name = "rules_desc")
    private String rulesDesc;
    
    /**
     * @Fields cloPerBegin 封闭期开始
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "clo_per_begin")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cloPerBegin;
    
    /**
     * @Fields cloPerEnd 封闭期结束
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "clo_per_end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cloPerEnd;

    /**
     * @Fields subscriptRate 认购期赎回费率
     */
    @Column(name = "subscript_rate", precision = 15, scale = 4)
    private BigDecimal subscriptRate;
    
    /**
     * @Fields cloPerRate 封闭期赎回费率
     */
    @Column(name = "clo_per_rate", precision = 15, scale = 4)
    private BigDecimal cloPerRate;
    
    /**
     * @Fields investAmt 起投金额
     */
    @Column(name = "invest_amt", precision = 15, scale = 4)
    private BigDecimal investAmt;
    
    /**
     * @Fields earnTm 收益日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "earn_tm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime earnTm;
    
	public String getProId() {
		return proId;
	}


	public void setProId(String proId) {
		this.proId = proId;
	}


	public String getProName() {
		return proName;
	}


	public void setProName(String proName) {
		this.proName = proName;
	}


	public String getProType() {
		return proType;
	}


	public void setProType(String proType) {
		this.proType = proType;
	}


	public BigDecimal getTotalAmt() {
		return totalAmt;
	}


	public void setTotalAmt(BigDecimal totalAmt) {
		this.totalAmt = totalAmt;
	}


	public BigDecimal getCollectAmt() {
		return collectAmt;
	}


	public void setCollectAmt(BigDecimal collectAmt) {
		this.collectAmt = collectAmt;
	}



	public BigDecimal getRate() {
		return rate;
	}


	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}


	public Integer getCollectCnt() {
		return collectCnt;
	}


	public void setCollectCnt(Integer collectCnt) {
		this.collectCnt = collectCnt;
	}


	public Integer getInvTerm() {
		return invTerm;
	}


	public void setInvTerm(Integer invTerm) {
		this.invTerm = invTerm;
	}


	public LocalDateTime getProEffTm() {
		return proEffTm;
	}


	public void setProEffTm(LocalDateTime proEffTm) {
		this.proEffTm = proEffTm;
	}


	public LocalDateTime getProExpTm() {
		return proExpTm;
	}


	public void setProExpTm(LocalDateTime proExpTm) {
		this.proExpTm = proExpTm;
	}


	public String getProDesc() {
		return proDesc;
	}


	public void setProDesc(String proDesc) {
		this.proDesc = proDesc;
	}

	public String getRiskDesc() {
		return riskDesc;
	}


	public void setRiskDesc(String riskDesc) {
		this.riskDesc = riskDesc;
	}


	public String getRulesDesc() {
		return rulesDesc;
	}


	public void setRulesDesc(String rulesDesc) {
		this.rulesDesc = rulesDesc;
	}


	public LocalDateTime getCloPerBegin() {
		return cloPerBegin;
	}


	public void setCloPerBegin(LocalDateTime cloPerBegin) {
		this.cloPerBegin = cloPerBegin;
	}


	public LocalDateTime getCloPerEnd() {
		return cloPerEnd;
	}


	public void setCloPerEnd(LocalDateTime cloPerEnd) {
		this.cloPerEnd = cloPerEnd;
	}


	public BigDecimal getSubscriptRate() {
		return subscriptRate;
	}


	public void setSubscriptRate(BigDecimal subscriptRate) {
		this.subscriptRate = subscriptRate;
	}


	public BigDecimal getCloPerRate() {
		return cloPerRate;
	}


	public void setCloPerRate(BigDecimal cloPerRate) {
		this.cloPerRate = cloPerRate;
	}


	public BigDecimal getInvestAmt() {
		return investAmt;
	}


	public void setInvestAmt(BigDecimal investAmt) {
		this.investAmt = investAmt;
	}


	public LocalDateTime getEarnTm() {
		return earnTm;
	}


	public void setEarnTm(LocalDateTime earnTm) {
		this.earnTm = earnTm;
	}


}
