package com.hisun.tms.tfm.repository;

import com.hisun.tms.csm.model.SettleBaseDO;
import com.hisun.tms.tfm.model.MerchantRateRuleDO;
import com.hisun.tms.tfm.model.MerchantRateRulePK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * Created by chen on 9/1 0001.
 */
public interface MerchantRateRuleRespository extends JpaRepository<MerchantRateRuleDO, MerchantRateRulePK> {
    @Query(value = "select * from tfm_merchant_rate_rule where user_id = :mercId ", nativeQuery=true  )
    List<MerchantRateRuleDO> findAllByMercId(@Param("mercId") String mercId);

    @Query(value = "select * from tfm_merchant_rate_rule where user_id = :mercId ORDER by bus_type ASC  ", nativeQuery=true  )
    List<MerchantRateRuleDO> findAllByMercIdOderyByBusType(@Param("mercId") String mercId);
}
