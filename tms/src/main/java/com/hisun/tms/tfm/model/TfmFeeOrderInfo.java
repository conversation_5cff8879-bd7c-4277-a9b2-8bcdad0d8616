package com.hisun.tms.tfm.model;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

import javax.persistence.*;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "tfm_fee_order")
public class TfmFeeOrderInfo extends AbstractEntityTmpe {
	/**
     * @Fields orderNo 订单号
     */
	@Id
    @Column(name = "order_no")
    private String orderNo;
	/**
	 * 交易原始订单号
	 */
	@Column(name = "bus_order_no")
    private String busOrderNo;
	
    /**
     * @Fields tradeDate 清分日期
     */
	@Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
	@Column(name = "trade_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate tradeDate;
	
	/**
     * @Fields tradeTime 入账时间
     */
	@Convert(converter = Jsr310JpaConverters.LocalTimeConverter.class)
	@Column(name = "trade_Time")
    @JsonFormat(pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime tradeTime;
	
	/**
     * @Fields busType 业务类型
     */
	@Column(name = "bus_type")
    private String busType;
    /**
     * @Fields busTypeDesc 业务类型描述
     */
	@Column(name = "bus_type_desc")
    private String busTypeDesc;
    /**
     * @Fields userId 用户编号
     */
	@Column(name = "user_id")
    private String userId;
    /**
     * @Fields tradeTotalAmt 交易总金额
     */
	@Column(name = "trade_total_amt", precision = 15, scale = 4)
    private BigDecimal tradeTotalAmt;
    /**
     * @Fields userNo 交易手续费
     */
	@Column(name = "fee" , precision = 15, scale = 4)
    private BigDecimal fee;
    /**
     * @Fields tradeAmt 交易金额
     */
	@Column(name = "trade_amt", precision = 15, scale = 4)
    private BigDecimal tradeAmt;
    /**
     * @Fields clearStats 清分状态 0:待清分 1:已清分 2:退款 9:不需清分
     */
	@Column(name = "clear_stats")
    private String clearStats;

	public String getBusType() {
		return busType;
	}

	public void setBusType(String busType) {
		this.busType = busType;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public LocalDate getTradeDate() {
		return tradeDate;
	}

	public void setTradeDate(LocalDate tradeDate) {
		this.tradeDate = tradeDate;
	}

	public String getBusTypeDesc() {
		return busTypeDesc;
	}

	public void setBusTypeDesc(String busTypeDesc) {
		this.busTypeDesc = busTypeDesc;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public BigDecimal getTradeTotalAmt() {
		return tradeTotalAmt;
	}

	public void setTradeTotalAmt(BigDecimal tradeTotalAmt) {
		this.tradeTotalAmt = tradeTotalAmt;
	}

	public BigDecimal getFee() {
		return fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}

	public BigDecimal getTradeAmt() {
		return tradeAmt;
	}

	public void setTradeAmt(BigDecimal tradeAmt) {
		this.tradeAmt = tradeAmt;
	}

	public String getClearStats() {
		return clearStats;
	}

	public void setClearStats(String clearStats) {
		this.clearStats = clearStats;
	}

	public String getBusOrderNo() {
		return busOrderNo;
	}

	public void setBusOrderNo(String busOrderNo) {
		this.busOrderNo = busOrderNo;
	}

	public LocalTime getTradeTime() {
		return tradeTime;
	}

	public void setTradeTime(LocalTime tradeTime) {
		this.tradeTime = tradeTime;
	}

	@Override
    public String toString() {
        return "CpmOrderInfo{" +
                "orderNo='" + orderNo + '\'' +
                ", userId='" + userId + '\'' +
                ", busType'=" + busType + '\'' +
                ", busTypeDesc'=" + busTypeDesc + '\'' +
                ", tradeDate=" + tradeDate + 
                ", tradeTotalAmt=" + tradeTotalAmt +
                ", fee=" + fee +
                ", tradeAmt=" + tradeAmt + 
                ", clearStats='" + clearStats + '\'' +
                '}';
    }
}
