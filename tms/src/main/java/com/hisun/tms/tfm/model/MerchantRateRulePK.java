package com.hisun.tms.tfm.model;

import java.io.Serializable;

/**
 * Created by chen on 9/1 0001.
 */
public class MerchantRateRulePK implements Serializable {
    private String userId;

    private String busType;

    private String channel;

    public MerchantRateRulePK() {
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

}
