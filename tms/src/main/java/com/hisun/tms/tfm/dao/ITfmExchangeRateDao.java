package com.hisun.tms.tfm.dao;

import com.hisun.tms.tfm.model.ExchangeRateDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 汇率数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
@Mapper
public interface ITfmExchangeRateDao {
    
    /**
     * 根据查询条件查询汇率信息
     * 
     * @param param 查询参数
     * @return 汇率信息列表
     */
    List<ExchangeRateDO> findByQueryCondition(Map<String, Object> param);

    /**
     * 查询总数
     * 
     * @param param 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> param);

    /**
     * 根据ID获取汇率信息
     * 
     * @param id 汇率ID
     * @return 汇率信息对象
     */
    ExchangeRateDO getById(Long id);

    /**
     * 新增汇率信息
     * 
     * @param exchangeRateDO 汇率信息对象
     * @return 影响行数
     */
    int insert(ExchangeRateDO exchangeRateDO);

    /**
     * 更新汇率信息
     * 
     * @param exchangeRateDO 汇率信息对象
     * @return 影响行数
     */
    int update(ExchangeRateDO exchangeRateDO);

    /**
     * 删除汇率信息
     * 
     * @param id 汇率ID
     * @return 影响行数
     */
    int delete(Long id);
}