package com.hisun.tms.tfm.service.impl;

import com.hisun.tms.tfm.dto.FeeOrderDTO;
import com.hisun.tms.tfm.mapper.FeeOrderMapper;
import com.hisun.tms.tfm.service.FeeOrderService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class FeeOrderServiceImpl implements FeeOrderService {

    @Resource
    private FeeOrderMapper feeOrderMapper;

    @Override
    public Page<FeeOrderDTO> findPage(Pageable pageable) {
        long total = feeOrderMapper.count();
        List<FeeOrderDTO> list = feeOrderMapper.findPage(pageable);
        return new PageImpl<>(list, pageable, total);
    }
}
