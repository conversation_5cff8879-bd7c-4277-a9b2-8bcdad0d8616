package com.hisun.tms.tfm.service;

import com.hisun.tms.common.datatables.QueryFindInput;
import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.tfm.dao.ITfmExchangeRateDao;
import com.hisun.tms.tfm.model.ExchangeRateDO;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 汇率查询服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
@Service("tfmExchangeRateService")
@Transactional
public class TfmExchangeRateServiceImpl implements TfmExchangeRateService {

    @Resource
    private ITfmExchangeRateDao iTfmExchangeRateDao;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<ExchangeRateDO> findAll(QueryFindInput input) {
        DataTablesOutput<ExchangeRateDO> dataTablesOutput = new DataTablesOutput<>();

        Map<String, Object> param = new HashMap<>();
        if (input.getExtraSearch() != null) {
            param.put("id", input.getExtraSearch().get("id"));
            param.put("rateType", input.getExtraSearch().get("rateType"));
            param.put("sourceCode", input.getExtraSearch().get("sourceCode"));
            param.put("targetCode", input.getExtraSearch().get("targetCode"));
            param.put("network", input.getExtraSearch().get("network"));
            param.put("rateSource", input.getExtraSearch().get("rateSource"));
        }
        param.put("pageEnd", input.getLength());
        param.put("pageBegin", input.getStart());

        // 先获取总记录数，再获取分页数据
        int total = iTfmExchangeRateDao.countTotal(param);
        List<ExchangeRateDO> list = iTfmExchangeRateDao.findByQueryCondition(param);

        // 设置DataTablesOutput的属性
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setRecordsFiltered(total);

        // 设置数据
        if (list == null) {
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(list);
        }

        return dataTablesOutput;
    }

    @Override
    public Object get(Long id) {
        return iTfmExchangeRateDao.getById(id);
    }

    @Override
    public String add(ExchangeRateDO exchangeRateDO) {
        // 设置创建者和更新者
        exchangeRateDO.setUpdateBy(getOpr.getOperatorName());

        // 设置创建时间和更新时间
        Date currentDate = new Date();
        exchangeRateDO.setCreateTime(currentDate);
        exchangeRateDO.setUpdateTime(currentDate);

        int result = iTfmExchangeRateDao.insert(exchangeRateDO);
        if (result == 1) {
            return "TFM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String update(ExchangeRateDO exchangeRateDO) {
        // 设置更新者和更新时间
        exchangeRateDO.setUpdateBy(getOpr.getOperatorName());
        exchangeRateDO.setUpdateTime(new Date());

        int result = iTfmExchangeRateDao.update(exchangeRateDO);
        if (result == 1) {
            return "TFM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String delete(Long id) {
        int result = iTfmExchangeRateDao.delete(id);
        return result + "";
    }
}