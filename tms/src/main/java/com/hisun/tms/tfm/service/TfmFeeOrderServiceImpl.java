package com.hisun.tms.tfm.service;
import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.common.utils.DateTimeUtils;



import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.tfm.model.TfmFeeOrderInfo;
import com.hisun.tms.tfm.model.TfmFeeParamInput;
import com.hisun.tms.tfm.model.TfmRateRule;
import com.hisun.tms.tfm.repository.DatatablesTfmFeeOrderRepository;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.mapping.Order;
import org.springframework.data.jpa.domain.Specification;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("tfmFeeOrderService")
public class TfmFeeOrderServiceImpl implements TfmFeeOrderService {

    private static final Logger logger = LoggerFactory.getLogger(TfmFeeOrderServiceImpl.class);

    @Autowired
    private DatatablesTfmFeeOrderRepository datatablesTfmFeeOrderRepository;
    @Autowired
    private ConstantParamClient constantParamClient;

    @Override
    @Transactional("tfmTransactionManager")
    public DataTablesOutput<TfmFeeOrderInfo> findAll(TfmFeeParamInput input) {
    	String busType = input.getExtra_search().get("busType");
		String userId = input.getExtra_search().get("userId");
		String beginDateStr = input.getExtra_search().get("beginDate");
		String endDateStr = input.getExtra_search().get("endDate");
		//订单开始日期
		LocalDate beginDate = null;
		//订单结束日期
		LocalDate endDate = null;
		if (JudgeUtils.isNotBlank(beginDateStr)){
		    beginDateStr = beginDateStr.replaceAll("-","").trim();
		    beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
		}else{
			beginDate = LocalDate.now().minusDays(90);//默认查过去90天的数据
		}
		if (JudgeUtils.isNotBlank(endDateStr)){
		    endDateStr = endDateStr.replaceAll("-","").trim();
		    endDate = DateTimeUtils.parseLocalDate(endDateStr);
		}else{
			endDate = LocalDate.now();
		}
    	Specification<TfmFeeOrderInfo> orderQueryParam=Specifications.<TfmFeeOrderInfo>and()
    			.eq("clearStats", "1")//只查询已经清分的记录
    			.like(JudgeUtils.isNotBlank(userId), "userId", "%"+userId+"%")
    			.eq(JudgeUtils.isNotBlank(busType), "busType", busType)
                .between("tradeDate", new Range<>(beginDate, endDate))
                .build();
    	Order order=new Order(2,"desc");
    	List<Order> orders=new ArrayList<Order>();
    	orders.add(order);
    	input.setOrder(orders);
        DataTablesOutput<TfmFeeOrderInfo> dataTablesOutput = datatablesTfmFeeOrderRepository.findAll(input, orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }


    @Override
	public List<TfmRateRule> findBusType() {
    	String type="TFM_BUS_TYPE";
	    GenericRspDTO<List<ConstantParamRspDTO>> genericRspDTO = constantParamClient.paramsGroup(type);
        List<ConstantParamRspDTO> constantParamRspDTOList =genericRspDTO.getBody();
	    List<TfmRateRule> newList = new ArrayList<TfmRateRule>();
	    TfmRateRule tfmRateRule=null;
	    for(ConstantParamRspDTO constantParamRspDTO : constantParamRspDTOList) {
            if(JudgeUtils.equals(constantParamRspDTO.getEffFlg(),"1")) {
				tfmRateRule=new TfmRateRule();
				tfmRateRule.setBusType(constantParamRspDTO.getParmVal());  
				tfmRateRule.setBusTypeDesc(constantParamRspDTO.getParmDispNm());  
				tfmRateRule.setStats(constantParamRspDTO.getEffFlg());
				newList.add(tfmRateRule);
            }
        }
	    return newList;
	}

}
