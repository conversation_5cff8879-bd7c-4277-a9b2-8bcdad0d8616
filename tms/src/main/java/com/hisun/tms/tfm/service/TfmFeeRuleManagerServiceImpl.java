package com.hisun.tms.tfm.service;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.tfm.dao.ITfmFeeRuleDao;
import com.hisun.tms.tfm.model.FeeRuleManagerDO;
import com.hisun.tms.tfm.model.TfmFindInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 手续费参数管理服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/28
 */
@Service("tfmFeeRuleManagerService")
@Transactional
public class TfmFeeRuleManagerServiceImpl implements TfmFeeRuleManagerService {

    @Resource
    private ITfmFeeRuleDao iTfmFeeRuleDao;

    @Resource
    private GetOpr getOpr;

    @Override
    public DataTablesOutput<FeeRuleManagerDO> findAll(TfmFindInput input) {
        DataTablesOutput<FeeRuleManagerDO> dataTablesOutput = new DataTablesOutput<>();

        Map<String, Object> param = new HashMap<>();
        if (input.getExtra_search() != null) {
            param.put("busType", input.getExtra_search().get("busType"));
            param.put("busTypeDesc", input.getExtra_search().get("busTypeDesc"));
            param.put("ccy", input.getExtra_search().get("ccy"));
        }
        param.put("pageEnd", input.getLength());
        param.put("pageBegin", input.getStart());

        // 先获取总记录数，再获取分页数据
        int total = iTfmFeeRuleDao.countTotal(param);
        List<FeeRuleManagerDO> list = iTfmFeeRuleDao.findByFindFeeRuleDTO(param);

        // 设置DataTablesOutput的属性
        dataTablesOutput.setDraw(input.getDraw());
        dataTablesOutput.setRecordsTotal(total);
        dataTablesOutput.setRecordsFiltered(total);

        // 设置数据
        if (list == null) {
            dataTablesOutput.setData(new ArrayList<>());
        } else {
            dataTablesOutput.setData(list);
        }

        return dataTablesOutput;
    }

    @Override
    public Object get(String busType) {
        return iTfmFeeRuleDao.getByBusType(busType);
    }

    @Override
    public String add(FeeRuleManagerDO feeRuleManagerDO) {
        // 设置操作员ID
        feeRuleManagerDO.setOprId(getOpr.getOperatorName());

        // 设置创建时间和修改时间
        Date currentDate = new Date();
        feeRuleManagerDO.setCreateTime(currentDate);
        feeRuleManagerDO.setModifyTime(currentDate);

        // 设置状态为生效
        if (feeRuleManagerDO.getStats() == null) {
            feeRuleManagerDO.setStats("1");
        }

        int result = iTfmFeeRuleDao.insert(feeRuleManagerDO);
        if (result == 1) {
            return "TFM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String update(FeeRuleManagerDO feeRuleManagerDO) {
        // 设置操作员ID和修改时间
        feeRuleManagerDO.setOprId(getOpr.getOperatorName());
        feeRuleManagerDO.setModifyTime(new Date());

        int result = iTfmFeeRuleDao.update(feeRuleManagerDO);
        if (result == 1) {
            return "TFM00000"; // 成功代码
        }
        return ""; // 失败返回空
    }

    @Override
    public String delete(String busType) {
        int result = iTfmFeeRuleDao.delete(busType);
        return result + "";
    }
}