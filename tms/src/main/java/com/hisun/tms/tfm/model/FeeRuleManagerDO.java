package com.hisun.tms.tfm.model;

import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 手续费参数管理DO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/28
 */
public class FeeRuleManagerDO {

    private String busType;
    private String busTypeDesc;
    private String ccy;
    private String calculateMod;
    private String calculateType;
    private BigDecimal rate;
    private BigDecimal fixFee;
    private String chargeType;
    private BigDecimal calculateMinAmt;
    private BigDecimal minFee;
    private BigDecimal maxFee;
    private String stats;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expDate;
    private String oprId;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date modifyTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date tmSmp;

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getBusTypeDesc() {
        return busTypeDesc;
    }

    public void setBusTypeDesc(String busTypeDesc) {
        this.busTypeDesc = busTypeDesc;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCalculateMod() {
        return calculateMod;
    }

    public void setCalculateMod(String calculateMod) {
        this.calculateMod = calculateMod;
    }

    public String getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(String calculateType) {
        this.calculateType = calculateType;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getFixFee() {
        return fixFee;
    }

    public void setFixFee(BigDecimal fixFee) {
        this.fixFee = fixFee;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public BigDecimal getCalculateMinAmt() {
        return calculateMinAmt;
    }

    public void setCalculateMinAmt(BigDecimal calculateMinAmt) {
        this.calculateMinAmt = calculateMinAmt;
    }

    public BigDecimal getMinFee() {
        return minFee;
    }

    public void setMinFee(BigDecimal minFee) {
        this.minFee = minFee;
    }

    public BigDecimal getMaxFee() {
        return maxFee;
    }

    public void setMaxFee(BigDecimal maxFee) {
        this.maxFee = maxFee;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public Date getEffDate() {
        return effDate;
    }

    public void setEffDate(Date effDate) {
        this.effDate = effDate;
    }

    public Date getExpDate() {
        return expDate;
    }

    public void setExpDate(Date expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(Date tmSmp) {
        this.tmSmp = tmSmp;
    }
}