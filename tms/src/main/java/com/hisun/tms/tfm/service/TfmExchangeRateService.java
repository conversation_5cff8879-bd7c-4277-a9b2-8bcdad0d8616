package com.hisun.tms.tfm.service;

import com.hisun.tms.tfm.model.ExchangeRateDO;
import com.hisun.tms.common.datatables.QueryFindInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 汇率查询服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
public interface TfmExchangeRateService {
    
    /**
     * 查询所有汇率信息
     * 
     * @param input 查询条件
     * @return 汇率信息列表
     */
    DataTablesOutput<ExchangeRateDO> findAll(QueryFindInput input);

    /**
     * 获取单个汇率信息
     * 
     * @param id 汇率ID
     * @return 汇率信息对象
     */
    Object get(Long id);

    /**
     * 添加汇率信息
     * 
     * @param exchangeRateDO 汇率信息对象
     * @return 结果
     */
    String add(ExchangeRateDO exchangeRateDO);

    /**
     * 更新汇率信息
     * 
     * @param exchangeRateDO 汇率信息对象
     * @return 结果
     */
    String update(ExchangeRateDO exchangeRateDO);

    /**
     * 删除汇率信息
     * 
     * @param id 汇率ID
     * @return 结果
     */
    String delete(Long id);
}