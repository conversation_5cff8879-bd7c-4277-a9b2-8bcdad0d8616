package com.hisun.tms.tfm.controller;

import com.hisun.tms.common.datatables.QueryFindInput;
import com.hisun.tms.tfm.model.ExchangeRateDO;
import com.hisun.tms.tfm.service.TfmExchangeRateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 汇率查询管理控制器
 * 提供汇率信息的增删改查功能
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25
 */
@Controller
@RequestMapping("/tfm/exchangeRate")
public class TfmExchangeRateController {

    private static final Logger logger = LoggerFactory.getLogger(TfmExchangeRateController.class);

    @Resource
    private TfmExchangeRateService tfmExchangeRateService;

    /**
     * 汇率查询列表页面
     */
    @GetMapping("/index")
    @PreAuthorize("hasPermission('','/cptmgr/param/exchangerate') or hasRole('ROLE_ADMIN')")
    public ModelAndView exchangeRateList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("tfm/exchangeRate/index");
        return modelAndView;
    }

    /**
     * 获取单个汇率信息
     */
    @PostMapping(value = "/getExchangeRate")
    @PreAuthorize("hasPermission('','/cptmgr/param/exchangerate') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public ExchangeRateDO getExchangeRate(@RequestParam(value = "id", required = false) Long id) {
        try {
            ExchangeRateDO exchangeRateDetail = (ExchangeRateDO) tfmExchangeRateService.get(id);
            return exchangeRateDetail;
        } catch (Exception e) {
            logger.error("获取汇率信息详情失败: ", e);
            return null;
        }
    }

    /**
     * 查询所有汇率信息
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/param/exchangerate') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<ExchangeRateDO> findAll(@Valid @RequestBody QueryFindInput input) {
        try {
            logger.debug("查询汇率信息列表，参数: {}", input);
            DataTablesOutput<ExchangeRateDO> result = tfmExchangeRateService.findAll(input);
            logger.debug("查询汇率信息列表，结果数量: {}", result.getData().size());
            return result;
        } catch (Exception e) {
            logger.error("查询汇率信息列表失败: ", e);
            // 返回空结果而不是null，避免前端解析错误
            DataTablesOutput<ExchangeRateDO> emptyResult = new DataTablesOutput<>();
            emptyResult.setDraw(input.getDraw());
            emptyResult.setRecordsTotal(0);
            emptyResult.setRecordsFiltered(0);
            return emptyResult;
        }
    }

    /**
     * 添加汇率信息
     */
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/cptmgr/param/exchangerate:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(ExchangeRateDO exchangeRateDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = tfmExchangeRateService.add(exchangeRateDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("添加汇率信息失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 修改汇率信息
     */
    @PostMapping(value = "/modify/{id}", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @PreAuthorize("hasPermission('','/cptmgr/param/exchangerate:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(
            @PathVariable("id") Long id,
            @ModelAttribute ExchangeRateDO exchangeRateDO) {  // 改为@ModelAttribute接收表单数据
        Map<String, String> map = new HashMap<String, String>();
        try {
            exchangeRateDO.setId(id);  // 设置ID
            String result = tfmExchangeRateService.update(exchangeRateDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("修改汇率信息失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 删除汇率信息
     */
    @DeleteMapping(value = "/delete/{id}")
    @PreAuthorize("hasPermission('','/cptmgr/param/exchangerate:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public String delete(@PathVariable("id") Long id) {
        String result = "";
        try {
            result = tfmExchangeRateService.delete(id);
        } catch (Exception e) {
            logger.error("删除汇率信息失败: ", e);
            result = "";
        }
        return result;
    }
}