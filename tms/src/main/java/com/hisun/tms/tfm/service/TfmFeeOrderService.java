package com.hisun.tms.tfm.service;

import com.hisun.tms.tfm.model.TfmFeeOrderInfo;
import com.hisun.tms.tfm.model.TfmFeeParamInput;
import com.hisun.tms.tfm.model.TfmRateRule;

import java.util.List;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
public interface TfmFeeOrderService {

    DataTablesOutput<TfmFeeOrderInfo> findAll(TfmFeeParamInput tfmFeeParamInput);

    List<TfmRateRule> findBusType();
}