package com.hisun.tms.tfm.service.impl;

import com.hisun.tms.tfm.dto.ExchangeRateDTO;
import com.hisun.tms.tfm.mapper.ExchangeRateMapper;
import com.hisun.tms.tfm.service.ExchangeRateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ExchangeRateServiceImpl implements ExchangeRateService {

    @Resource
    private ExchangeRateMapper exchangeRateMapper;

    @Override
    public Page<ExchangeRateDTO> findPage(Pageable pageable) {
        long total = exchangeRateMapper.count();
        List<ExchangeRateDTO> list = exchangeRateMapper.findPage(pageable);
        return new PageImpl<>(list, pageable, total);
    }
}
