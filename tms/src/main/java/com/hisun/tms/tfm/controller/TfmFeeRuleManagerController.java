package com.hisun.tms.tfm.controller;

import com.hisun.tms.tfm.model.FeeRuleManagerDO;
import com.hisun.tms.tfm.model.TfmFindInput;
import com.hisun.tms.tfm.service.TfmFeeRuleManagerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 手续费参数管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/28
 */
@Controller
@RequestMapping("/tfm/feerule")
public class TfmFeeRuleManagerController {

    private static final Logger logger = LoggerFactory.getLogger(TfmFeeRuleManagerController.class);

    @Resource
    private TfmFeeRuleManagerService tfmFeeRuleManagerService;

    /**
     * 手续费参数管理列表页面
     */
    @GetMapping
    // @PreAuthorize("hasPermission('','/cptmgr/param/feerule') or hasRole('ROLE_ADMIN')")
    public ModelAndView feeRuleList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("tfm/feerule/index");
        return modelAndView;
    }

    /**
     * 测试页面
     */
    @GetMapping("/test")
    public ModelAndView testPage() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("tfm/feerule/test");
        return modelAndView;
    }

    /**
     * 获取单个手续费参数
     */
    @PostMapping(value = "/getFeeRule")
    @PreAuthorize("hasPermission('','/cptmgr/param/feerule') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public FeeRuleManagerDO getFeeRule(@RequestParam(value = "busType", required = false) String busType) {
        try {
            FeeRuleManagerDO feeRuleDetail = (FeeRuleManagerDO) tfmFeeRuleManagerService.get(busType);
            return feeRuleDetail;
        } catch (Exception e) {
            logger.error("获取手续费参数详情失败: ", e);
            return null;
        }
    }

    /**
     * 查询所有手续费参数
     */
    @PostMapping(value = "/findAll")
    // @PreAuthorize("hasPermission('','/cptmgr/param/feerule') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<FeeRuleManagerDO> findAll(@Valid @RequestBody TfmFindInput input) {
        try {
            logger.info("收到手续费参数查询请求，参数: {}", input);
            DataTablesOutput<FeeRuleManagerDO> result = tfmFeeRuleManagerService.findAll(input);
            logger.info("查询手续费参数列表成功，结果数量: {}", result.getData().size());
            return result;
        } catch (Exception e) {
            logger.error("查询手续费参数列表失败: ", e);
            // 返回空结果而不是null，避免前端解析错误
            DataTablesOutput<FeeRuleManagerDO> emptyResult = new DataTablesOutput<>();
            emptyResult.setDraw(input.getDraw());
            emptyResult.setRecordsTotal(0);
            emptyResult.setRecordsFiltered(0);
            return emptyResult;
        }
    }

    /**
     * 添加手续费参数
     */
    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/cptmgr/param/feerule:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> add(FeeRuleManagerDO feeRuleManagerDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = tfmFeeRuleManagerService.add(feeRuleManagerDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("添加手续费参数失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 修改手续费参数
     */
    @PostMapping(value = "/modify/{busType}")
    @PreAuthorize("hasPermission('','/cptmgr/param/feerule:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String, String> modify(FeeRuleManagerDO feeRuleManagerDO) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String result = tfmFeeRuleManagerService.update(feeRuleManagerDO);
            map.put("result", result);
        } catch (Exception e) {
            logger.error("修改手续费参数失败: ", e);
            map.put("result", "ERROR");
        }
        return map;
    }

    /**
     * 删除手续费参数
     */
    @DeleteMapping(value = "/delete/{busType}")
    @PreAuthorize("hasPermission('','/cptmgr/param/feerule:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public String delete(@PathVariable("busType") String busType) {
        String result = "";
        try {
            result = tfmFeeRuleManagerService.delete(busType);
        } catch (Exception e) {
            logger.error("删除手续费参数失败: ", e);
            result = "";
        }
        return result;
    }
}