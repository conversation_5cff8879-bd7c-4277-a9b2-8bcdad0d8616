package com.hisun.tms.tfm.dao;

import com.hisun.tms.tfm.model.FeeRuleManagerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 手续费参数管理数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/28
 */
@Mapper
public interface ITfmFeeRuleDao {
    /**
     * 根据查询条件查询手续费参数
     * 
     * @param param 查询参数
     * @return 手续费参数列表
     */
    List<FeeRuleManagerDO> findByFindFeeRuleDTO(Map<String, Object> param);

    /**
     * 查询总数
     * 
     * @param param 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> param);

    /**
     * 删除手续费参数（逻辑删除）
     * 
     * @param busType 业务类型
     * @return 影响行数
     */
    int delete(String busType);

    /**
     * 更新手续费参数
     * 
     * @param feeRuleManagerDO 手续费参数对象
     * @return 影响行数
     */
    int update(FeeRuleManagerDO feeRuleManagerDO);

    /**
     * 新增手续费参数
     * 
     * @param feeRuleManagerDO 手续费参数对象
     * @return 影响行数
     */
    int insert(FeeRuleManagerDO feeRuleManagerDO);

    /**
     * 根据业务类型获取手续费参数
     * 
     * @param busType 业务类型
     * @return 手续费参数对象
     */
    FeeRuleManagerDO getByBusType(String busType);
}