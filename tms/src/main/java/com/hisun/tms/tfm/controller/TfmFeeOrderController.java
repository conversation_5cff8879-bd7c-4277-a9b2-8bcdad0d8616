package com.hisun.tms.tfm.controller;

import com.hisun.tms.tfm.model.TfmFeeOrderInfo;
import com.hisun.tms.tfm.model.TfmFeeParamInput;
import com.hisun.tms.tfm.model.TfmRateRule;
import com.hisun.tms.tfm.service.TfmFeeOrderService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

import javax.validation.Valid;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/tfm/disqueryctrl")
public class TfmFeeOrderController {

    private static final Logger logger = LoggerFactory.getLogger(TfmFeeOrderController.class);

    @Autowired
    private TfmFeeOrderService tfmFeeOrderService;

    @GetMapping
    @PreAuthorize("hasPermission('','/csmmgr/disqueryctrl') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("tfm/tfmfeeorderinfo/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/csmmgr/disqueryctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<TfmFeeOrderInfo> findAll(@Valid @RequestBody TfmFeeParamInput input) {
        return tfmFeeOrderService.findAll(input);
    }

    @GetMapping(value = "findBusType")
    @ResponseBody
    @PreAuthorize("hasPermission('','/csmmgr/disqueryctrl') or hasRole('ROLE_ADMIN')")
    public List<TfmRateRule> findBusType() {
        return tfmFeeOrderService.findBusType();
    }
}
