/*
 * @ClassName MerchantRateRuleDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-05 14:33:00
 */
package com.hisun.tms.tfm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "tfm_merchant_rate_rule")
@IdClass(MerchantRateRulePK.class)
public class MerchantRateRuleDO {
    /**
     * @Fields busType 业务类型
     */
    @Id
    @Column(name = "bus_type")
    private String busType;
    /**
     * @Fields userId 内部用户号
     */
    @Id
    @Column(name = "user_id")
    private String userId;

    /**
     * @Fields channel 渠道 Mpay
     */
    @Id
    @Column(name = "channel")
    private String channel;
    /**
     * @Fields busTypeDesc 业务类型描述
     */
    @Column(name = "bus_type_desc")
    private String busTypeDesc;
    /**
     * @Fields userName 用户名称
     */
    @Column(name = "user_name")
    private String userName;
    /**
     * @Fields ccy 币种
     */
    @Column(name = "ccy")
    private String ccy;
    /**
     * @Fields calculateMod 计费模式 internal:内扣 external:外扣
     */
    @Column(name = "calculate_mod")
    private String calculateMod;
    /**
     * @Fields calculateType 计费方式 percent:百分比 fixed:固定手续费
     */
    @Column(name = "calculate_type")
    private String calculateType;
    /**
     * @Fields rate 费率
     */
    @Column(name = "rate")
    private BigDecimal rateb;
    /**
     * @Fields fixFee 固定手续费
     */
    @Column(name = "fix_fee")
    private BigDecimal fixFeeb;
    /**
     * @Fields chargeType 收费方式 single:单笔 cycle:固定周期
     */
    @Column(name = "charge_type")
    private String chargeType;
    /**
     * @Fields minFee 最低收取费用
     */
    @Column(name = "min_fee")
    private BigDecimal minFeeb;
    /**
     * @Fields maxFee 最高收取费用
     */
    @Column(name = "max_fee")
    private BigDecimal maxFeeb;

    /**
     * 计费起始金额
     */
    @Column(name = "calculate_min_amt")
    private BigDecimal beginCalFeeB;
    /**
     * @Fields stats 状态0:无效 1:生效
     */
    @Column(name = "stats")
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "eff_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDateL;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDateL;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    //最后修改时间
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;
    //创建时间
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;



    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBusTypeDesc() {
        return busTypeDesc;
    }

    public void setBusTypeDesc(String busTypeDesc) {
        this.busTypeDesc = busTypeDesc;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCalculateMod() {
        return calculateMod;
    }

    public void setCalculateMod(String calculateMod) {
        this.calculateMod = calculateMod;
    }

    public String getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(String calculateType) {
        this.calculateType = calculateType;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }


    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public BigDecimal getRateb() {
        return rateb;
    }

    public void setRateb(BigDecimal rateb) {
        this.rateb = rateb;
    }

    public BigDecimal getFixFeeb() {
        return fixFeeb;
    }

    public void setFixFeeb(BigDecimal fixFeeb) {
        this.fixFeeb = fixFeeb;
    }

    public BigDecimal getMinFeeb() {
        return minFeeb;
    }

    public void setMinFeeb(BigDecimal minFeeb) {
        this.minFeeb = minFeeb;
    }

    public BigDecimal getMaxFeeb() {
        return maxFeeb;
    }

    public void setMaxFeeb(BigDecimal maxFeeb) {
        this.maxFeeb = maxFeeb;
    }

    public LocalDate getEffDateL() {
        return effDateL;
    }

    public void setEffDateL(LocalDate effDateL) {
        this.effDateL = effDateL;
    }

    public LocalDate getExpDateL() {
        return expDateL;
    }

    public void setExpDateL(LocalDate expDateL) {
        this.expDateL = expDateL;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getBeginCalFeeB() {
        return beginCalFeeB;
    }

    public void setBeginCalFeeB(BigDecimal beginCalFeeB) {
        this.beginCalFeeB = beginCalFeeB;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}