package com.hisun.tms.tfm.service;

import com.hisun.tms.tfm.model.FeeRuleManagerDO;
import com.hisun.tms.tfm.model.TfmFindInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 手续费参数管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/28
 */
public interface TfmFeeRuleManagerService {
    /**
     * 查询所有手续费参数
     * 
     * @param input 查询条件
     * @return 手续费参数列表
     */
    DataTablesOutput<FeeRuleManagerDO> findAll(TfmFindInput input);

    /**
     * 获取单个手续费参数
     * 
     * @param busType 业务类型
     * @return 手续费参数对象
     */
    Object get(String busType);

    /**
     * 添加手续费参数
     * 
     * @param feeRuleManagerDO 手续费参数对象
     * @return 结果
     */
    String add(FeeRuleManagerDO feeRuleManagerDO);

    /**
     * 更新手续费参数
     * 
     * @param feeRuleManagerDO 手续费参数对象
     * @return 结果
     */
    String update(FeeRuleManagerDO feeRuleManagerDO);

    /**
     * 删除手续费参数
     * 
     * @param busType 业务类型
     * @return 结果
     */
    String delete(String busType);
}