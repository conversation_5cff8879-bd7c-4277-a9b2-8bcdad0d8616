package com.hisun.tms.sys.repository;

import com.hisun.tms.sys.model.UserRoleDo;
import com.hisun.tms.sys.model.UserRoleDoPK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Created by chen on 10/30 0030.
 */
public interface UserRoleRespository extends JpaRepository<UserRoleDo, UserRoleDoPK> {

    @Query(value = "select count(*) from user_role where role_Id = :roleId ", nativeQuery=true  )
    Integer countByRoleId(@Param("roleId") int roleId);
}
