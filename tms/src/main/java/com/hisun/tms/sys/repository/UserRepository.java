package com.hisun.tms.sys.repository;

import com.hisun.tms.sys.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by 吕海龙 on 2017/4/6.
 */
public interface UserRepository extends JpaRepository<User, Integer> {
    User findByName(String name);

    @Query(value="select * from user  where name = :name", nativeQuery = true)
    User findByName1(@Param("name") String name);

    User findById(int id);

    @Modifying
    @Transactional
    @Query("update User user set user.active = :active where user.id = :id")
    void switchStatus(@Param("id") int id, @Param("active") boolean active);

//    @Modifying
//    @Query("update User user set user.password = :password where user.id = :id")
//    void modify(User user);

    @Query(
            value = "SELECT * FROM user WHERE user_id IN " +
                    "   (SELECT DISTINCT user_id FROM user_role WHERE role_id IN " +
                    "       (SELECT role_id FROM role_resource WHERE resource_id = " +
                    "           (SELECT resource_id FROM resource WHERE resource = ?1)))",
            nativeQuery = true
    )
    List<User> listByResource(String resource);
    
    @Query(
            value = "SELECT * FROM user WHERE bra_id = :id",
            nativeQuery = true
    )
	List<User> findByBraId(@Param("id") String braId);

    @Query(
            value = "SELECT name FROM user WHERE name = :name " ,
            nativeQuery = true
    )
    String checkUserName(@Param("name") String name);
}
