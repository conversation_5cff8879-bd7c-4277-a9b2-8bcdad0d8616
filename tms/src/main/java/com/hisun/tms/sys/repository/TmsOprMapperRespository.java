package com.hisun.tms.sys.repository;

import com.hisun.tms.sys.model.TmsOprMapper;
import com.hisun.tms.sys.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Created by chen on 12/21 0021.
 */
public interface TmsOprMapperRespository extends JpaRepository<TmsOprMapper, Integer> {
    @Query(value = "select * from tms_opr_mapper where opr_url = :path and method = :method limit 0,1 ", nativeQuery=true  )
    TmsOprMapper findByOprUrl(@Param("method") String method, @Param("path") String path);
}
