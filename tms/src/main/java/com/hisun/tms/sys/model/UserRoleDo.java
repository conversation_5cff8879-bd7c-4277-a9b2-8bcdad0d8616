package com.hisun.tms.sys.model;

import com.hisun.tms.csm.model.SettleCardPK;

import javax.persistence.*;

/**
 * Created by chen on 10/30 0030.
 */

@Entity
@Table(name = "user_role")
@IdClass(UserRoleDoPK.class)
public class UserRoleDo {

    @Id
    @Column(name = "user_Id")
    private int userId ;
    @Column(name = "role_Id")
    private int roleId;

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }
}
