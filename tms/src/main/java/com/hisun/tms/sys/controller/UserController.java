package com.hisun.tms.sys.controller;

import com.hisun.tms.sys.model.User;
import com.hisun.tms.sys.service.UserService;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;

import java.util.Arrays;
import java.util.Date;
import java.util.Random;

/**
 * Created by 吕海龙 on 2017/4/18.
 */
@Controller
@RequestMapping("/system/user")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;
    @GetMapping(value = "/form")
    @PreAuthorize("hasPermission('','/cmmmgr/user/user:add') or hasRole('ROLE_ADMIN')")
    public ModelAndView form(@RequestParam(value = "id", required = false) String id) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("system/user/form");
        String api = "/system/user/add";
        if (StringUtils.isNotBlank(id)) {
            modelAndView.addObject("user", userService.get(Integer.valueOf(id)));
            api = "/system/user/modify/" + id;
        }
        modelAndView.addObject("api", api);
        return modelAndView;
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/cmmmgr/user/user:add') or hasRole('ROLE_ADMIN')")
    public String create(User user) {
        userService.saveUser(user);
        return "redirect:/system/user/";
    }

    @PutMapping(value = "/status/{id}")
    @ResponseBody
    @PreAuthorize("hasPermission('','/cmmmgr/user/user:status') or hasRole('ROLE_ADMIN')")
    public void switchStatus(@PathVariable("id") String id, @RequestParam("active") boolean active) {
        userService.switchStatus(Integer.valueOf(id), active);
    }

    @PostMapping(value = "/modify/{id}")
    @PreAuthorize("hasPermission('','/cmmmgr/user/user:modify') or hasRole('ROLE_ADMIN')")
    public String modify(@PathVariable("id") String id, User user) {
        userService.modify(Integer.valueOf(id), user);
        return "redirect:/system/user/";
    }

    @GetMapping(value = "/select-role/{id}")
    @PreAuthorize("hasPermission('','/cmmmgr/user/user:role') or hasRole('ROLE_ADMIN')")
    public ModelAndView selectRole(@PathVariable("id") String id) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("system/user/grant-role");
        modelAndView.addObject("list", userService.selectRoles(Integer.valueOf(id)));
        modelAndView.addObject("api", "/system/user/grant-role/" + id);
        return modelAndView;
    }

    @PostMapping(value = "/grant-role/{id}")
    @PreAuthorize("hasPermission('','/cmmmgr/user/user:role') or hasRole('ROLE_ADMIN')")
    public String grantRole(@PathVariable("id") String id, String[] rid) {
        if (rid == null) {
            rid = new String[0];
        }
        userService.grantRole(Integer.valueOf(id), Arrays.asList(rid));
        return "redirect:/system/user";
    }

    @DeleteMapping(value = "/delete/{id}")
    @ResponseBody
    @PreAuthorize("hasPermission('','/cmmmgr/user/user:delete') or hasRole('ROLE_ADMIN')")
    public void delete(@PathVariable("id") String id) {
        userService.delete(Integer.valueOf(id));
    }

    @GetMapping
    @PreAuthorize("hasPermission('', '/cmmmgr/user/user') or hasRole('ROLE_ADMIN')")
    public ModelAndView userList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("system/user/index");
//        modelAndView.addObject("list", userService.list());
        return modelAndView;
    }

//    @RequestMapping(value = "findAll", method = RequestMethod.POST)
//    public Page<User> findAll(@PageableDefault(value = 15, sort = {"id"}, direction = Sort.Direction.ASC)
//                                      Pageable pageable) {
//        return userService.findAll(pageable);
//    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('', '/cmmmgr/user/user') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<User> findAll(@Valid @RequestBody DataTablesInput input) {
        return userService.findAll(input);
    }

    @PostMapping(value = "checkUserName")
    @ResponseBody
    public String  checkUserName(@RequestParam ("name") String name ) {
        return userService.checkUserName(name);
    }


    @PostMapping(value = "getRandom")
    @ResponseBody
    public String  getRandom() {
        long time = new Date().getTime();
        Random random = new Random();
        int r = random.nextInt(9)*100+random.nextInt(9)*10 +random.nextInt(9);
        String radomStr = ""+r + time;
        return radomStr;
    }

}
