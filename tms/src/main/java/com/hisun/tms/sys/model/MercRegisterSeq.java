package com.hisun.tms.sys.model;

import org.hibernate.annotations.Table;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Created by chen on 8/31 0031.
 */
@Entity
@javax.persistence.Table(name = "merc_register_seq")
public class MercRegisterSeq {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private Integer id ;

    @Column(name = "examin_status")
    private String examinStatus;

    @Column(name = "examin_Nm")
    private String examinNm;

    @Column(name = "remark")
    private String remark;

    @Column(name = "mercInfo")
    private String mercInfo;

    @Column(name = "settle_info")
    private String settleInfo;

    @Column(name = "rate_info")
    private String rateInfo;

    @Column(name = "modify_time")
    private String  modifyTime ;

    @Column(name = "create_time")
    private String  createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getExaminStatus() {
        return examinStatus;
    }

    public void setExaminStatus(String examinStatus) {
        this.examinStatus = examinStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMercInfo() {
        return mercInfo;
    }

    public void setMercInfo(String mercInfo) {
        this.mercInfo = mercInfo;
    }

    public String getSettleInfo() {
        return settleInfo;
    }

    public void setSettleInfo(String settleInfo) {
        this.settleInfo = settleInfo;
    }

    public String getRateInfo() {
        return rateInfo;
    }

    public void setRateInfo(String rateInfo) {
        this.rateInfo = rateInfo;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getExaminNm() {
        return examinNm;
    }

    public void setExaminNm(String examinNm) {
        this.examinNm = examinNm;
    }
}
