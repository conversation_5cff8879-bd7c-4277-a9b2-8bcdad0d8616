package com.hisun.tms.sys.service;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.cmm.model.BranchInfo;
import com.hisun.tms.cmm.repository.BranchInfoRepository;
import com.hisun.tms.common.email.Email;
import com.hisun.tms.common.email.EmailService;
import com.hisun.tms.sys.model.Role;
import com.hisun.tms.sys.model.SelectRole;
import com.hisun.tms.sys.model.User;
import com.hisun.tms.sys.repository.DatatablesUserRepository;
import com.hisun.tms.sys.repository.RoleRepository;
import com.hisun.tms.sys.repository.UserRepository;

import com.hisun.tms.util.HexUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * Created by 吕海龙 on 2017/4/6.
 */
@Service("userService")
public class UserServiceImpl implements UserService {

    @Autowired
    protected RoleSelectService roleSelectService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private DatatablesUserRepository datatablesUserRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private BCryptPasswordEncoder bCryptPasswordEncoder;
    @Autowired
    private BranchInfoRepository branchInfoRepository;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private EmailService emailService;


    @Override
    public void saveUser(User user) {
        /* 增加成功发送邮件 */
        Locale locale = LocaleContextHolder.getLocale();
        String subject = messageSource.getMessage("email.user.subject", null, locale);
        String content = messageSource.getMessage("email.user.content", null, locale);
        String end = messageSource.getMessage("email.user.end", null, locale);

        Email email = new Email();
        // 邮件主题
        email.setSubject(subject);
        // 邮件地址
        email.setTo(new String[]{user.getEmail()});
        // 邮件模板
        email.setTemplate("email/adduser");

        // 邮件内容
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", user.getName());
        variables.put("content", content);
        // 明文密码
        variables.put("pwd", user.getPassword());
        variables.put("end", end);
        //密码解密
        String password = decodePassword(user.getPassword()) ;
        // 新增柜员
        user.setPassword(bCryptPasswordEncoder.encode(password));
        user.setActive(true);
        userRepository.save(user);

        // 新增的柜员编号
        variables.put("id", user.getId());
        // 发送邮件
   //     emailService.sendEmail(email, null, variables, locale);
    }

    private String decodePassword(String password) {
        String[] strArray = password.split("k");
        String len  = strArray[0] ;
        int ilen =  Integer.valueOf(len);
        String p = strArray[1].substring(6,6+ilen/2) + strArray[1].substring(12+ilen/2 ,12+ilen);
        //0x6368656e
        String hex16 = HexUtil.hexStr2Str(p) ;
        return hex16 ;
    }

//    public static void main(String[] args) {
//        UserServiceImpl userService = new UserServiceImpl() ;
//        userService.decodePassword("18k11010131326368611010156E3132331101101513343018831");
//    }

    @Override
    public User get(int id) {
        return userRepository.findById(id);
    }

    @Override
    public User findByName(String name) {
        return userRepository.findByName(name);
    }

    @Override
    public List<User> list() {
        return userRepository.findAll();
    }

    @Override
    public void switchStatus(int id, boolean active) {
        userRepository.switchStatus(id, active);
    }

    @Override
    public void modify(int id, User user) {
        //getOne返回引用，findOne返回实体
        User old = userRepository.getOne(id);
        if (StringUtils.isNotBlank(user.getPassword())) {
            //密码解密
            String password = decodePassword(user.getPassword()) ;
            old.setPassword(bCryptPasswordEncoder.encode(password));
        }
        if (StringUtils.isNotBlank(user.getEmail())) {
            old.setEmail(user.getEmail());
        }
        if (StringUtils.isNotBlank(user.getMblNo())) {
            old.setMblNo(user.getMblNo());
        }
        if (StringUtils.isNotBlank(user.getOfficeId())) {
            old.setOfficeId(user.getOfficeId());
        }
        if (StringUtils.isNotBlank(user.getBraId())) {
            old.setBraId(user.getBraId());
        }
        userRepository.save(old);
        try{
	        /* 修改成功发送邮件 */
            Locale locale = LocaleContextHolder.getLocale();
            String subject = messageSource.getMessage("email.resetLoginPswd.subject", null, locale);
            String content = messageSource.getMessage("email.resetLoginPswd.content", null, locale)+user.getPassword();
            String end = messageSource.getMessage("email.resetLoginPswd.end", null, locale);

            Email email = new Email();
            // 邮件主题
            email.setSubject(subject);
            // 邮件地址
            email.setTo(new String[]{user.getEmail()});
            // 邮件模板
            email.setTemplate("email/resetLoginPswd");

            // 邮件内容
            Map<String, Object> variables = new HashMap<>();
            variables.put("name", old.getName());
            variables.put("content", content);
            // 明文密码
            variables.put("end", end);
            // 发送邮件
            emailService.sendEmail(email, null, variables, locale);
        }catch (Exception e){

        }
    }

    @Override
    public List<SelectRole> selectRoles(int id) {
        return roleSelectService.mergeRole(roleRepository.findAll(), new ArrayList<Role>(userRepository.findById(id).getRoles()));
    }

    @Override
    public void grantRole(int uid, List<String> roleIds) {
        User user = userRepository.findById(uid);
        Set<Role> set = new HashSet<>();
        roleIds.stream().forEach(id -> set.add(roleRepository.findOne(Integer.valueOf(id))));
        user.setRoles(set);
        userRepository.save(user);
    }

    @Override
    public void delete(int id) {
        userRepository.delete(id);
    }

//    @Override
//    public Page<User> findAll(Pageable pageable) {
//        return datatablesUserRepository.findAll(pageable);
//    }

    @Override
    public DataTablesOutput<User> findAll(DataTablesInput dataTablesInput) {
        DataTablesOutput<User> users=null;
        users=datatablesUserRepository.findAll(dataTablesInput);
        List<User> userList=users.getData();
        BranchInfo branchInfo=null;
        for(User user:userList){
            if(StringUtils.isNotBlank(user.getBraId())){
                branchInfo=branchInfoRepository.getByBraId(user.getBraId());
                if(JudgeUtils.isNotNull(branchInfo)){
                    user.setBraNm(branchInfo.getBraNm());
                    user.setOfficeNm(branchInfo.getOfficeNm());
                    user.setOfficeId(branchInfo.getOfficeId());
                }
            }
        }
        return users;
    }

    @Override
    public List<User> listByResource(String resource) {
        return userRepository.listByResource(resource);
    }

    @Override
    public String checkUserName(String name) {
        String username= userRepository.checkUserName(name) ;
        if (JudgeUtils.isBlank(username)) {
            return "success" ;
        }
        return "failure" ;
    }

}
