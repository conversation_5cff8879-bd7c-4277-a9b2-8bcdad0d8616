package com.hisun.tms.sys.model;

/**
 * Created by chen on 8/31 0031.
 */
public class UploadFile {
    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 种类
     */
    private String type;

    /**
     * base64String
     */
    private String base64String;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 是否上传到文件服务器
     * @return
     */
    private String uploadToFileServer = "Y" ;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBase64String() {
        return base64String;
    }

    public void setBase64String(String base64String) {
        this.base64String = base64String;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getUploadToFileServer() {
        return uploadToFileServer;
    }

    public void setUploadToFileServer(String uploadToFileServer) {
        this.uploadToFileServer = uploadToFileServer;
    }
}
