package com.hisun.tms.sys.controller;

import com.hisun.tms.common.util.GetOpr;
import com.hisun.tms.sys.model.Role;
import com.hisun.tms.sys.model.TmsUserOprLogDo;
import com.hisun.tms.sys.service.UserOprService;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;

/**
 * Created by chen on 12/21 0021.
 */
@Controller
@RequestMapping("/system/userOpr")
public class UserOprController {
    @Resource
    private UserOprService userOprService ;

    @Resource
    private GetOpr getOpr ;
    @GetMapping
    @PreAuthorize("hasPermission('', '/cmmmgr/sysmgr/userOpr') or hasRole('ROLE_ADMIN')")
    public ModelAndView userList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("system/userOpr/index");
//        modelAndView.addObject("list", userService.list());
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('', '/cmmmgr/user/user') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<TmsUserOprLogDo> findAll(@Valid @RequestBody DataTablesInput input) {

        UserDetails details = (UserDetails) SecurityContextHolder.getContext()
                .getAuthentication().getPrincipal() ;
        String userName =  details.getUsername();
        Collection<? extends GrantedAuthority> auths = details.getAuthorities();
        Object[] ats = auths.toArray();
        boolean flag = true ;
        for(int i=0; i<ats.length; i++){
            GrantedAuthority o = (GrantedAuthority)ats[i];
            String role =  o.getAuthority();
           if ("ROLE_ADMIN".equals(role)) {
               flag = false ;
               break;
           }
        }
        if (flag) {
            input.getColumn("userId").getSearch().setValue(userName);
        }
        return userOprService.findAll(input);
    }
}
