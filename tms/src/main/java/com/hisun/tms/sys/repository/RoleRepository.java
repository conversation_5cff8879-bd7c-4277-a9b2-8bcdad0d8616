package com.hisun.tms.sys.repository;

import com.hisun.tms.sys.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Created by 吕海龙 on 2017/4/6.
 */
public interface RoleRepository extends JpaRepository<Role, Integer> {
    @Query(value = "select count(*) from role where role = :role  ", nativeQuery=true  )
    int checkRoleType(@Param("role") String role);


}