package com.hisun.tms.sys.service;

import com.hisun.tms.common.jstree.JsTreeNode;
import com.hisun.tms.sys.model.Resource;
import com.hisun.tms.sys.model.Role;
import com.hisun.tms.sys.repository.ResourceRepository;
import com.hisun.tms.sys.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;

/**
 * Created by 吕海龙 on 2017/7/10.
 */
@Service("ResourceService")
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    protected JsTreeService jsTreeService;
    @Autowired
    private ResourceRepository resourceRepository;
    @Autowired
    private RoleRepository roleRepository;

    @Override
    public List<JsTreeNode> getResourceTree(int roleId) {
        return jsTreeService.mergeResource(resourceRepository.findAll(), new ArrayList<Resource>(Optional.ofNullable(roleRepository.findOne(roleId)).map(Role::getResources).orElse(new HashSet<Resource>())));
    }
}
