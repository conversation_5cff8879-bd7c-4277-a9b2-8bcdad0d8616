package com.hisun.tms.sys.common;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by chen on 8/31 0031.
 */
public class Constants {
    /**
     * 商户审核信息
     * 00-新建待审核
     * 01-审核不通过
     * 02-审核通过
     * 03-修改待审核
     */
    public static final String EXAMINE_STATUS_NO_PASS = "00";
    public static final String EXAMINE_STATUS_REJECT_PASS = "01";
    public static final String EXAMINE_STATUS_PASS = "02";
    public static final String EXAMINE_STATUS_MODIFY_PASS = "03";

    public static final Map<String, String> specillyUrlMap = new HashMap<String, String>();
    static {
        specillyUrlMap.put("/system/role/saveResource", "/system/role/saveResource");
        specillyUrlMap.put("/mkm/activity/add", "/mkm/activity/add");
        specillyUrlMap.put("/mkm/activity/delete", "/mkm/activity/delete");
        specillyUrlMap.put("/system/role/delete", "/system/role/delete");
        specillyUrlMap.put("/system/user/status", "/system/user/status");
        specillyUrlMap.put("/system/user/modify", "/system/user/modify");
        specillyUrlMap.put("/system/user/grant-role", "/system/user/grant-role");
        specillyUrlMap.put("/system/user/delete", "/system/user/delete");
    }

    /**
     * Kyb审核信息
     * 00-新建待审核
     * 01-审核不通过
     * 02-审核通过
     * 03-暂存
     */
    public static final String KYB_STATUS_WAIT_PASS = "00";
    public static final String KYB_STATUS_REJECT_PASS = "01";
    public static final String KYB_STATUS_PASS = "02";
    public static final String KYB_STATUS_TEMP = "03";

    /**
     * KYB审核状态
     */
    public static final String KYB_STATUS_PENDING = "00"; // 待审核
    public static final String KYB_STATUS_FIRST_AUDIT = "02"; // 初审中
    public static final String KYB_STATUS_SECOND_AUDIT = "03"; // 复核中
    public static final String KYB_STATUS_APPROVED = "04"; // 审核通过
    public static final String KYB_STATUS_REJECTED = "05"; // 拒绝

    /**
     * KYB审核结果
     */
    public static final String AUDIT_RESULT_APPROVED = "00"; // 审核通过
    public static final String AUDIT_RESULT_REJECTED = "01"; // 审核拒绝
}
