package com.hisun.tms.sys.service;

import com.hisun.lemon.jcommon.file.FileSftpUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * Created by chen on 10/31 0031.
 */

@Service
public class FileServiceImpl implements FileService {
    @Value("${merc.upload.depath}")
    private String uploadPath ;

    @Value("${mkm.local.upload}")
    private String batchFileLocalPath;

    @Value("${mkm.remote.path}")
    private String remotePath;

    @Value("${mkm.remote.ip}")
    private String remoteIp;

    @Value("${mkm.remote.port}")
    private String remotePort;

    @Value("${mkm.remote.user}")
    private String remoteUser;

    @Value("${mkm.remote.password}")
    private String remotePwd;

    @Value("${mkm.remote.timeout}")
    private String timeout;

    @Override
    public String downloadRemoteFile(String fileName , String filePath) throws Exception {
        FileSftpUtils.download(remoteIp, Integer.valueOf(remotePort), Integer.valueOf(timeout),filePath ,
                fileName, uploadPath, remoteUser, remotePwd);
        return uploadPath + "/" + fileName;
    }
}
