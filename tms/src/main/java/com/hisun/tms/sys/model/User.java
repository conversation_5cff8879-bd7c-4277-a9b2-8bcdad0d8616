package com.hisun.tms.sys.model;

import com.fasterxml.jackson.annotation.JsonManagedReference;


import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.persistence.*;
import java.util.Set;

/**
 * Created by 吕海龙 on 2017/4/6.
 */
@Entity
@Table(name = "user")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "user_id")
    private int id;
    @Column(name = "name", unique = true)
    @NotBlank(message = "*Please provide a name")
    private String name;
    @Column(name = "password")
    @Length(min = 5, message = "*Your password must have at least 5 characters")
    @NotBlank(message = "*Please provide your password")
    private String password;
    @Column(name = "active")
    private boolean active;
    @Column(name = "email")
    @NotBlank
    @Email
    private String email;
    @Column(name = "office_id")
    @NotBlank
    private String officeId;
    @Column(name = "bra_id")
    @NotBlank
    private String braId;
    @Column(name = "mbl_no")
    @NotBlank(message = "*Please provide your mblNo")
    private String mblNo;
    @ManyToMany(cascade = CascadeType.REFRESH, fetch = FetchType.LAZY)
    @JoinTable(name = "user_role", joinColumns = @JoinColumn(name = "user_id"), inverseJoinColumns = @JoinColumn(name = "role_id"))
    @JsonManagedReference
    private Set<Role> roles;
    private String braNm;
    private String officeNm;
	public String getBraId() {
		return braId;
	}

	public String getMblNo() {
		return mblNo;
	}

	public String getOfficeId() {
		return officeId;
	}

	public void setOfficeId(String officeId) {
		this.officeId = officeId;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public String getBraNm() {
		return braNm;
	}

	public void setBraNm(String braNm) {
		this.braNm = braNm;
	}

	public String getOfficeNm() {
		return officeNm;
	}

	public void setOfficeNm(String officeNm) {
		this.officeNm = officeNm;
	}

	public void setBraId(String braId) {
		this.braId = braId;
	}

	public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean getActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public boolean isActived() {
        return active;
    }


    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Set<Role> getRoles() {
        return roles;
    }

    public void setRoles(Set<Role> roles) {
        this.roles = roles;
    }
}
