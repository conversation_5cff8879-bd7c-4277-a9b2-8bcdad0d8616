package com.hisun.tms.sys.service;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.tms.sys.model.Resource;
import com.hisun.tms.sys.model.Role;
import com.hisun.tms.sys.repository.DatatablesRoleRepository;
import com.hisun.tms.sys.repository.ResourceRepository;
import com.hisun.tms.sys.repository.RoleRepository;
import com.hisun.tms.sys.repository.UserRoleRespository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by 吕海龙 on 2017/7/6.
 */
@Service("roleService")
public class RoleServiceImpl implements RoleService {

    private static final Logger logger = LoggerFactory.getLogger(RoleServiceImpl.class);

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private DatatablesRoleRepository datatablesRoleRepository;
    @Autowired
    private UserRoleRespository userRoleRespository;

    @Override
    public DataTablesOutput<Role> findAll(DataTablesInput dataTablesInput) {
        return datatablesRoleRepository.findAll(dataTablesInput);
    }

    @Override
    public void saveResource(int roleId, List<Integer> resourceIds) {
        logger.debug("role (id={}) has been grant resources (id={})", roleId, resourceIds);
        Role role = roleRepository.findOne(roleId);
        Set<Resource> set = new HashSet<>();
        resourceIds.stream().forEach(resourceId -> set.add(resourceRepository.findOne(resourceId)));
        role.setResources(set);
        roleRepository.save(role);
    }

    /*
    * 外键表删除需外键ON DELETE CASCADE
    * */
    @Override
    public void delete(int roleId) {
        //检查角色下是否有用户。如果没有则删除,否则返回
        Integer count = userRoleRespository.countByRoleId(roleId);
        if (count>0) {
            throw new LemonException();
        }
        roleRepository.delete(roleId);
    }

    @Override
    public void saveRole(Role roleModel) {
        //检查是否角色类型是否重复
        int i = roleRepository.checkRoleType(roleModel.getRole());
        if (i >0 ) {
            throw new LemonException("repeat");
        }
    	Role roles = new Role();
    	roles.setId(roleModel.getId());
    	roles.setRole(roleModel.getRole());
    	roles.setRoleName(roleModel.getRoleName());
    	roles.setOfficeId(roleModel.getOfficeId());
    	roles.setBranchId(roleModel.getBranchId());
    	roles.setStatus(roleModel.getStatus());
        roleRepository.save(roles);
    }

	@Override
	public void updateRole(Role roleModel) {
		Role temp = roleRepository.findOne(roleModel.getId());
		temp.setRole(roleModel.getRole());
		temp.setBranchId(roleModel.getBranchId());
		temp.setOfficeId(roleModel.getOfficeId());
		temp.setRoleName(roleModel.getRoleName());
		temp.setStatus(roleModel.getStatus());
		roleRepository.save(temp);
	}
}
