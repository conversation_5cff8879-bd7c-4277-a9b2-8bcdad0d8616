package com.hisun.tms.sys.service;

import com.hisun.tms.sys.model.Role;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import java.util.List;

/**
 * Created by 吕海龙 on 2017/7/6.
 */
public interface RoleService {

    DataTablesOutput<Role> findAll(DataTablesInput dataTablesInput);

    void saveResource(int roleId, List<Integer> resourceIds);

    void delete(int roleId);

    void saveRole(Role roleModel);
    
    void updateRole(Role roleModel);
}