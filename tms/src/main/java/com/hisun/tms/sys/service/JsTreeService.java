package com.hisun.tms.sys.service;

import com.hisun.tms.common.jstree.JsTreeNode;
import com.hisun.tms.common.jstree.JsTreeNodeState;
import com.hisun.tms.sys.model.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class JsTreeService {

    public List<JsTreeNode> mergeResource(List<Resource> all, List<Resource> part) {
        if (CollectionUtils.isEmpty(all)) {
            return null;
        }

        if (CollectionUtils.isEmpty(part)) {
            return all.stream().map(resource -> new JsTreeNode(String.valueOf(resource.getId()), resource.getParent_id() == 0 ? "#" :
                    String.valueOf(resource.getParent_id()), resource.getName(), null, new JsTreeNodeState(false, false, false))).
                    collect(Collectors.toList());
        }

        return all.stream().map(resource -> {
            if (part.contains(resource)) {
                return new JsTreeNode(String.valueOf(resource.getId()), resource.getParent_id() == 0 ? "#" :
                        String.valueOf(resource.getParent_id()), resource.getName(), null, new JsTreeNodeState(false, false, true));
            }
            return new JsTreeNode(String.valueOf(resource.getId()), resource.getParent_id() == 0 ? "#" :
                    String.valueOf(resource.getParent_id()), resource.getName(), null, new JsTreeNodeState(false, false, false));
        }).collect(Collectors.toList());
    }
}
