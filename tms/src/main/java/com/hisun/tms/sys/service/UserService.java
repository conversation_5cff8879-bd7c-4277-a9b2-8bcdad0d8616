package com.hisun.tms.sys.service;

import com.hisun.tms.sys.model.SelectRole;
import com.hisun.tms.sys.model.User;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by 吕海龙 on 2017/4/6.
 */
public interface UserService {
    void saveUser(User user);

    User get(int id);

    User findByName(String name);

    List<User> list();

    void switchStatus(int id, boolean active);

    @Transactional
    void modify(int id, User user);

    List<SelectRole> selectRoles(int id);

    void grantRole(int uid, List<String> roleIds);

    void delete(int id);

    //    Page<User> findAll(Pageable pageable);
    DataTablesOutput<User> findAll(DataTablesInput dataTablesInput);

    List<User> listByResource(String resource);

    String checkUserName(String name);
}