package com.hisun.tms.sys.service;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.sys.model.TmsUserOprLogDo;
import com.hisun.tms.sys.repository.TmsUserOprLogRepository;
import com.hisun.tms.urm.model.UserBasicInfo;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by chen on 12/21 0021.
 */
@Service
public class UserOprServiceImpl implements UserOprService {

    @Resource
    private TmsUserOprLogRepository tmsUserOprLogRepository ;
    @Override
    public DataTablesOutput<TmsUserOprLogDo> findAll(DataTablesInput input) {
        String userId = input.getColumn("userId").getSearch().getValue();
        Specification<TmsUserOprLogDo> orderQueryParam= Specifications.<TmsUserOprLogDo>and()
                .eq(JudgeUtils.isNotBlank(userId), "userId", userId)
                .build();
        return tmsUserOprLogRepository.findAll(input, orderQueryParam);
    }
}
