package com.hisun.tms.sys.controller;

import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.sys.model.UploadFile;
import com.hisun.tms.sys.service.FileService;
import com.hisun.tms.util.Base64Utils;
import com.hisun.tms.util.HexUtil;
import com.hisun.tms.util.ImageUploadUtil;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.security.access.method.P;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by chen on 8/31 0031.
 */
@Controller
@RequestMapping("/file")
public class FileController {
    private static final Logger logger = LoggerFactory.getLogger(FileController.class);
    @Autowired
    private Environment env;
    @Value("${merc.upload.depath}")
    private String uploadPath ;


    @Resource
    private FileService fileService;

    @PostMapping(value = "upload/base64Img")
    @ResponseBody
    public Map<String,String> uploadImgBybase64(UploadFile file) throws IOException {
        Map<String , String > result = new HashMap< String , String > ();
        //String fileContent,String filePathName, String encoding
        File decPath = new File(uploadPath);
        if (!(decPath.exists())){
            decPath.mkdir();
        }
        String sourpath =uploadPath+ File.separator + String.valueOf(new Date().getTime()) + file.getName();
        System.out.println(file.getBase64String());
        file.setPath(sourpath);
        String path = Base64Utils.deCodeFileContentNotInflater(file.getBase64String(),file.getPath(),"UTF-8");
        result.put("localPath", HexUtil.str2HexStr(path));
        // 上传图片到文件服务器
        if (file.getUploadToFileServer().equals("Y")) {
//            String baseUrl = env.getProperty("upload.remote.url");
//            String key = env.getProperty("upload.remote.banner.key");
//            String bucket = env.getProperty("upload.remote.banner.bucket");
//            String fileUrl = ImageUploadUtil.uploadImage(baseUrl, key, bucket, path);
            String fileUrl = uploadToFileServer(path) ;
            result.put("path", fileUrl);

            if (JudgeUtils.isNotBlank(path)) {
                logger.info("删除本地图片"+path);
                File localFile =  new File(path);
                if (localFile.exists()) {
                    localFile.delete();
                }
            }
        }
        return result;
    }


    @ResponseBody
    @PostMapping(value = "/upload")
    public String uploadBatchRec(@RequestParam("file")MultipartFile file, @RequestParam("uploadFlag") String uploadFlag) {
        return uploadFile(file ,uploadFlag ) ;
    }

    @ResponseBody
    @PostMapping(value = "/uploadNuploadFlag" )
    public String uploadNuploadFlag(@RequestParam("file")MultipartFile file) {
        String flag = uploadFile(file ,"N" ) ;
        // Map<String,String> map = new HashMap<String ,String >();
        //map.put("path" , flag);
        return flag;
    }


    @GetMapping(value = "/downloadByFilePath")
    public void downloadByFilePath(@RequestParam("filePath") String filePath ,@RequestParam("downloadRemotFlg") String downloadRemotFlg , HttpServletRequest request, HttpServletResponse response) throws Exception {

        //获得请求文件名
        String filename = filePath.substring(filePath.lastIndexOf("/")+1 , filePath.length());
        String path = filePath.substring( 0,filePath.lastIndexOf("/"));
        //设置文件MIME类型
        response.setContentType(request.getServletContext().getMimeType(filename));
        //设置Content-Disposition
        response.setHeader("Content-Disposition", "attachment;filename="+filename);
        //判断文件是否从文件服务器获取 Y则需要在文件服务器获取
        if ("Y".equals(downloadRemotFlg)) {
            filePath = fileService.downloadRemoteFile(filename , path);
        }

        //读取目标文件，通过response将目标文件写到客户端
        //获取目标文件的绝对路径
        //System.out.println(fullFileName);
        //读取文件
        File file = new File(filePath);
        InputStream in = null;
        OutputStream out = null ;
        try {
            in = new FileInputStream(file);
            out = response.getOutputStream();

            //写文件
            int b;
            while((b=in.read())!= -1)
            {
                out.write(b);
            }
        } catch (Exception e) {
            logger.info("获取文件失败"+filePath);
        } finally {
            try {
                in.close();
                out.close();
                //如果问上传文件服务器的文件最后要把本地文件删除了
                if("Y".equals(downloadRemotFlg) && file.exists()) {
                    file.delete();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }



    }

    public String uploadFile(MultipartFile file, String uploadFlag) {
        File decPath = new File(uploadPath);
        String fileName =  String.valueOf(new Date().getTime())+file.getOriginalFilename();
        if (!(decPath.exists())) {
            decPath.mkdir();
        }
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        String localPath = uploadPath + File.separator + fileName;

        try {
            FileUtils.writeByteArrayToFile(new File(localPath), file.getBytes());
//            FileUtils.writeByteArrayToFile(new File("D:/123.txt"), file.getBytes());
        } catch (IOException e) {
            logger.error("File operation exception：" + e.getMessage());
            return "";
        }

        // 上传图片到文件服务器
        if (uploadFlag.equals("Y")) {

            String fileUrl = uploadToFileServer(localPath) ;
            if (JudgeUtils.isNotBlank(fileUrl)) {
                new File(localPath).delete();
            }
            return fileUrl;
        } else {
            return localPath;
        }
    }


    public String uploadToFileServer(String localPath ) {
        String baseUrl = env.getProperty("upload.remote.url");
        String key = env.getProperty("upload.remote.banner.key");
        String bucket = env.getProperty("upload.remote.banner.bucket");
        String fileUrl = ImageUploadUtil.uploadImage(baseUrl, key, bucket, localPath);
        return fileUrl ;
    }


}
