package com.hisun.tms.sys.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.common.jstree.JsTreeNode;
import com.hisun.tms.common.util.BeanUtils;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.sys.model.Role;
import com.hisun.tms.sys.model.RoleExt;
import com.hisun.tms.sys.service.ResourceService;
import com.hisun.tms.sys.service.RoleService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/6.
 */
@Controller
@RequestMapping("/system/role")
public class RoleController {

    private static final Logger logger = LoggerFactory.getLogger(RoleController.class);

    @Autowired
    private RoleService roleService;

    @Autowired
    private ResourceService resourceService;

    @GetMapping
    @PreAuthorize("hasPermission('','/cmmmgr/user/role') or hasRole('ROLE_ADMIN')")
    public ModelAndView roleList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("system/role/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @ResponseBody
    @PreAuthorize("hasPermission('','/cmmmgr/user/role') or hasRole('ROLE_ADMIN')")
    public DataTablesOutput<Role> findAll(@Valid @RequestBody DataTablesInput input) {
        return roleService.findAll(input);
    }

    @GetMapping(value = "getResourceTree")
    @ResponseBody
    @PreAuthorize("hasPermission('','/cmmmgr/user/role:resource') or hasRole('ROLE_ADMIN')")
    public List<JsTreeNode> getResourceTree(int roleId) {
        return resourceService.getResourceTree(roleId);
    }

    @PostMapping(value = "saveResource/{roleId}")
    @ResponseBody
    @PreAuthorize("hasPermission('','/cmmmgr/user/role:resource') or hasRole('ROLE_ADMIN')")
    public void saveResource(@PathVariable("roleId") int roleId, @RequestParam(value = "array[]", required = false) List<Integer> resourceIds) {
        logger.debug("role (id={}) will be grant resources (id={})", roleId, resourceIds);
        if (resourceIds == null) {
            resourceIds = new ArrayList<Integer>();
        }
        roleService.saveResource(roleId, resourceIds);
    }

    @GetMapping(value = "/form")
    @PreAuthorize("hasPermission('','/cmmmgr/user/role:add') or hasRole('ROLE_ADMIN')")
    public ModelAndView form() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("system/role/form");
        String action = "/system/role/add";
        modelAndView.addObject("action", action);
        return modelAndView;
    }

    @PostMapping(value = "/add")
    @ResponseBody
    @PreAuthorize("hasPermission('','/cmmmgr/user/role:add') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<RoleExt> create( @Valid @RequestBody DatatablesEditorRequest<RoleExt> datatablesEditorRequest) {
    	Map<String, RoleExt> data = datatablesEditorRequest.getData();
        DatatablesEditorResponse<RoleExt> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        data.forEach((key, roleModel) -> {
            logger.debug(key + "..." + roleModel.toString());
            Role role = new Role();
            BeanUtils.copyProperties(role , roleModel);

            if (JudgeUtils.isBlank(role.getRole()) || JudgeUtils.isBlank(role.getRoleName())) {
                datatablesEditorResponse.setError("Parameter can not be empty!");
                return;
            }
            try {
                roleService.saveRole(role);
            } catch (LemonException e){
                if ("repeat".equals(e.getMessage())) {
                    datatablesEditorResponse.setError("角色类型已存在");
                    return ;
                }
            }
        });

        return datatablesEditorResponse;
    }

    @DeleteMapping(value = "/delete/{roleId}")
    @ResponseBody
    @PreAuthorize("hasPermission('','/cmmmgr/user/role:delete') or hasRole('ROLE_ADMIN')")
    public void delete(@PathVariable("roleId") int roleId) {
        roleService.delete(roleId);
    }
    
    @PostMapping(value = "/edit")
    @ResponseBody
    @PreAuthorize("hasPermission('','/cmmmgr/user/role:add') or hasRole('ROLE_ADMIN')")
    public DatatablesEditorResponse<RoleExt> update( @Valid @RequestBody DatatablesEditorRequest<RoleExt> datatablesEditorRequest) {
    	Map<String, RoleExt> data = datatablesEditorRequest.getData();
        data.forEach((key, roleModel) -> {
            logger.debug(key + "..." + roleModel.toString());
            Role role = new Role();
            BeanUtils.copyProperties(role , roleModel);
            roleService.updateRole(role);
        });
        DatatablesEditorResponse<RoleExt> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

}
