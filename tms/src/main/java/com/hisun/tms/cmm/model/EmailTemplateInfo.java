package com.hisun.tms.cmm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 邮件模板实体类
 * 
 * <AUTHOR>
 * @date 2025/7/17 16:14
 * @version 1.0
 */
@Entity
@Table(name = "cmm_email_template")
public class EmailTemplateInfo extends AbstractEntityTmpe {
    /**
     * @Fields id ID
     */
    @Id
    @Column(name = "id")
    private String id;
    /**
     * @Fields type 邮件类型
     */
    @Column(name = "type")
    private String type;
    /**
     * @Fields lvl 邮件级别
     */
    @Column(name = "lvl")
    private String lvl;
    /**
     * @Fields replaceField 替换变量
     */
    @Column(name = "replace_field")
    private String replaceField;
    /**
     * @Fields noticeContentCn 中文邮件模版
     */
    @Column(name = "template_content_cn")
    private String templateContentCn;
    /**
     * @Fields templateContentEn 英文邮件模版
     */
    @Column(name = "template_content_en")
    private String templateContentEn;
    /**
     * @Fields subject 邮件主题
     */
    @Column(name = "subject")
    private String subject;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    @Column(name = "stats")
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "eff_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLvl() {
        return lvl;
    }

    public void setLvl(String lvl) {
        this.lvl = lvl;
    }

    public String getReplaceField() {
        return replaceField;
    }

    public void setReplaceField(String replaceField) {
        this.replaceField = replaceField;
    }

    public String getTemplateContentCn() {
        return templateContentCn;
    }

    public void setTemplateContentCn(String templateContentCn) {
        this.templateContentCn = templateContentCn;
    }

    public String getTemplateContentEn() {
        return templateContentEn;
    }

    public void setTemplateContentEn(String templateContentEn) {
        this.templateContentEn = templateContentEn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}
