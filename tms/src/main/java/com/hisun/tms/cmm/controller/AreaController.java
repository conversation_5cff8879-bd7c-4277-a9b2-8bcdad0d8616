package com.hisun.tms.cmm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.hisun.tms.cmm.model.AreaInfo;
import com.hisun.tms.cmm.service.AreaService;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/areactrl")
public class AreaController {

    @Autowired
    private AreaService areaService;

    @GetMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/phsctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public List<AreaInfo> findAll(@RequestParam("parentId") String parentId) {
        return areaService.findAll(parentId);
    }
    
    @GetMapping(value = "get")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/phsctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public AreaInfo get(@RequestParam("parentId") String parentId) {
        return areaService.get(parentId);
    }
}
