package com.hisun.tms.cmm.service;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.MessageTemplateInfo;


/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:55:51 
 * @version V1.0
 */
public interface MessageTemplateService {

    void save(Iterable<MessageTemplateInfo> entities);

    Map<String, MessageTemplateInfo> modify(Map<String, MessageTemplateInfo> exampleMap);

    void delete(Iterable<MessageTemplateInfo> entities);

    DataTablesOutput<MessageTemplateInfo> findAll(GenericParamInput dataTablesInput);
}