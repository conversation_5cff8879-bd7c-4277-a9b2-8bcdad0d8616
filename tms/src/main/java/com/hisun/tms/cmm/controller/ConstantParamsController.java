package com.hisun.tms.cmm.controller;

import com.hisun.tms.cmm.model.ConstantParamsModel;
import com.hisun.tms.cmm.service.ConstantParamsService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2017/8/31
 */
@Controller
@RequestMapping("/cmm/constants")
public class ConstantParamsController {

    private static final Logger logger = LoggerFactory.getLogger(ConstantParamsController.class);

    @Resource
    private ConstantParamsService constantParamsService;

    @GetMapping
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/constants') or hasRole('ROLE_ADMIN')")
    public ModelAndView show() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/constants/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/constants') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<ConstantParamsModel> findAll(@Valid @RequestBody DataTablesInput input) {
        logger.debug("datatable server-side input {}", input);
        return constantParamsService.findAll(input);
    }

    @PostMapping(value = "add")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/constants') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<ConstantParamsModel> add(
            @Valid @RequestBody DatatablesEditorRequest<ConstantParamsModel> datatablesEditorRequest) {
        logger.debug(datatablesEditorRequest.getData().toString());
        Map<String, ConstantParamsModel> data = datatablesEditorRequest.getData();
        data.forEach((key, constantParamsModel) -> {
            logger.debug(key + "..." + constantParamsModel.toString());
            constantParamsService.save(constantParamsModel);
        });
        DatatablesEditorResponse<ConstantParamsModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "modify")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/constants') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<ConstantParamsModel> modify(
            @Valid @RequestBody DatatablesEditorRequest<ConstantParamsModel> datatablesEditorRequest) {
        Map<String, ConstantParamsModel> data = datatablesEditorRequest.getData();
        data.forEach((key, constantParamsModel) -> {
            constantParamsService.update(constantParamsModel);
        });
        DatatablesEditorResponse<ConstantParamsModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "delete")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/constants') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<ConstantParamsModel> delete(
            @Valid @RequestBody DatatablesEditorRequest<ConstantParamsModel> datatablesEditorRequest) {
        Map<String, ConstantParamsModel> data = datatablesEditorRequest.getData();
        data.forEach((key, constantParamsModel) -> {
            constantParamsService.delete(constantParamsModel);
        });
        DatatablesEditorResponse<ConstantParamsModel> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }
}
