package com.hisun.tms.cmm.model;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月30日 下午8:32:53 
 * @version V1.0
 */
public class FileUploadOutput<T> {
    
    private List<T> data = Collections.emptyList();

    /** 上传文件Map */
    private Map<String, Map<String, FileUploadInfo>> files;

    /** 上传文件Id Map */
    private Map<String, String> upload;
    
    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public Map<String, Map<String, FileUploadInfo>> getFiles() {
        return files;
    }

    public void setFiles(Map<String, Map<String, FileUploadInfo>> files) {
        this.files = files;
    }

    public Map<String, String> getUpload() {
        return upload;
    }

    public void setUpload(Map<String, String> upload) {
        this.upload = upload;
    }

}
