package com.hisun.tms.cmm.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Convert;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:41:24
 * @version V1.0
 */
public class PhoneSegementInfoDto extends AbstractEntityTmpe {
    /**
     * @Fields prefix_number 号段
     */
    private String prefixNumber;
    /**
     * @Fields numberLength 手机号长度
     */
	/**
     * @Fields carrier 运营商
     */
    private String carrier;
    /**
     * @Fields area_code 国家区域代码
     */
    private String areaCode;
    /**
     * @Fields contry 国家代码
     */
    private String countryCode;
    /**
     * @Fields contry 国家
     */
    private String country;
    /**
     * @Fields contry 国家
     */
    private String countryKh;
    /**
     * @Fields contry 国家
     */
    private String countryZh;
    /**
     * @Fields contry 国家
     */
    private String countryEn;
    /**
     * @Fields provinceCode 省份代码
     */
    private String provinceCode;
    /**
     * @Fields province 省份
     */
    private String province;
    /**
     * @Fields province 省份
     */
    private String provinceKh;
    /**
     * @Fields province 省份
     */
    private String provinceZh;
    /**
     * @Fields province 省份
     */
    private String provinceEn;
    /**
     * @Fields cityCode 地市代码
     */
    private String cityCode;
    /**
     * @Fields city 地市
     */
    private String city;
    /**
     * @Fields city 地市
     */
    private String cityKh;
    /**
     * @Fields city 地市
     */
    private String cityZh;
    /**
     * @Fields city 地市
     */
    private String cityEn;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    /**
     * @Fields number_length 号码长度
     */
    private int numberLength;

    public String getPrefixNumber() {
        return prefixNumber;
    }

    public void setPrefixNumber(String prefixNumber) {
        this.prefixNumber = prefixNumber;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getCountryKh() {
        return countryKh;
    }

    public void setCountryKh(String countryKh) {
        this.countryKh = countryKh;
    }

    public String getCountryZh() {
        return countryZh;
    }

    public void setCountryZh(String countryZh) {
        this.countryZh = countryZh;
    }

    public String getCountryEn() {
        return countryEn;
    }

    public void setCountryEn(String countryEn) {
        this.countryEn = countryEn;
    }

    public String getProvinceKh() {
        return provinceKh;
    }

    public void setProvinceKh(String provinceKh) {
        this.provinceKh = provinceKh;
    }

    public String getProvinceZh() {
        return provinceZh;
    }

    public void setProvinceZh(String provinceZh) {
        this.provinceZh = provinceZh;
    }

    public String getProvinceEn() {
        return provinceEn;
    }

    public void setProvinceEn(String provinceEn) {
        this.provinceEn = provinceEn;
    }

    public String getCityKh() {
        return cityKh;
    }

    public void setCityKh(String cityKh) {
        this.cityKh = cityKh;
    }

    public String getCityZh() {
        return cityZh;
    }

    public void setCityZh(String cityZh) {
        this.cityZh = cityZh;
    }

    public String getCityEn() {
        return cityEn;
    }

    public void setCityEn(String cityEn) {
        this.cityEn = cityEn;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public int getNumberLength() {
        return numberLength;
    }

    public void setNumberLength(int numberLength) {
        this.numberLength = numberLength;
    }
}
