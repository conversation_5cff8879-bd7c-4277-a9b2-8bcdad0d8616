package com.hisun.tms.cmm.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:41:24
 * @version V1.0
 */
@Entity
@Table(name = "cmm_sms_template")
public class SmsTemplateInfo extends AbstractEntityTmpe {
    /**
     * @Fields id ID
     */
    @Id
    @Column(name = "id")
    private String id;
    /**
     * @Fields type 短信类型
     */
    @Column(name = "type")
    private String type;
    /**
     * @Fields lvl 短信级别
     */
    @Column(name = "lvl")
    private String lvl;
    /**
     * @Fields replaceField 替换变量
     */
    @Column(name = "replace_field")
    private String replaceField;
    /**
     * @Fields templateContentKh 柬文短信模版
     */
    @Column(name = "template_content_kh")
    private String templateContentKh;
    /**
     * @Fields noticeContentCn 中文短信模版
     */
    @Column(name = "template_content_cn")
    private String templateContentCn;
    /**
     * @Fields templateContentEn 英文短信模版
     */
    @Column(name = "template_content_en")
    private String templateContentEn;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    @Column(name = "stats")
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "eff_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLvl() {
        return lvl;
    }

    public void setLvl(String lvl) {
        this.lvl = lvl;
    }

    public String getReplaceField() {
        return replaceField;
    }

    public void setReplaceField(String replaceField) {
        this.replaceField = replaceField;
    }

    public String getTemplateContentKh() {
        return templateContentKh;
    }

    public void setTemplateContentKh(String templateContentKh) {
        this.templateContentKh = templateContentKh;
    }

    public String getTemplateContentCn() {
        return templateContentCn;
    }

    public void setTemplateContentCn(String templateContentCn) {
        this.templateContentCn = templateContentCn;
    }

    public String getTemplateContentEn() {
        return templateContentEn;
    }

    public void setTemplateContentEn(String templateContentEn) {
        this.templateContentEn = templateContentEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

}
