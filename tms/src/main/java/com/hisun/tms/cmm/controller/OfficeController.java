package com.hisun.tms.cmm.controller;


import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.cmm.model.AreaInfo;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.OfficeInfo;
import com.hisun.tms.cmm.service.OfficeService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.common.exception.CustomException;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03 
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/officectrl")
public class OfficeController {

    @Autowired
    private OfficeService officeService;

    @GetMapping
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/office') or hasRole('ROLE_ADMIN')")
    public ModelAndView OfficeList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/office/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/office') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<OfficeInfo> findAll(@Valid @RequestBody GenericParamInput input) {
        return officeService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/office:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<OfficeInfo> add(@Valid @RequestBody DatatablesEditorRequest<OfficeInfo> datatablesEditorRequest) throws CustomException {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<OfficeInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<OfficeInfo> iterator = iterable.iterator(); iterator.hasNext();) {
        	OfficeInfo officeInfo = (OfficeInfo) iterator.next();
        	officeInfo.setOperId(user.getUsername());
        }
        officeService.save(iterable);
        DatatablesEditorResponse<OfficeInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/office:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<OfficeInfo> modify(@Valid @RequestBody DatatablesEditorRequest<OfficeInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, OfficeInfo> MerchantMap = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, OfficeInfo>> iterator = MerchantMap.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<String, OfficeInfo> entry = iterator.next();
            entry.getValue().setOperId(user.getUsername());
        }
        Map<String, OfficeInfo> map = datatablesEditorRequest.getData();
        Map<String, OfficeInfo> mapData = officeService.modify(map);
        DatatablesEditorResponse<OfficeInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/office:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> delete(@Valid @RequestBody DatatablesEditorRequest<OfficeInfo> datatablesEditorRequest) {
        Iterable<OfficeInfo> iterable = datatablesEditorRequest.getData().values();
        return officeService.delete(iterable);
    }
    
    @GetMapping(value = "findAll")
    @ResponseBody
    public List<OfficeInfo> findAllInf() {
        return officeService.findAllInf();
    }
}
