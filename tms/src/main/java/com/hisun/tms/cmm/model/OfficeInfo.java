package com.hisun.tms.cmm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年9月8日 上午11:01:24
 * @version V1.0
 */
@Entity
@Table(name = "cmm_office_info")
public class OfficeInfo extends AbstractEntityTmpe {
    /**
     * @Fields id ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private int id;
    /**
     * @Fields officeId 分公司ID
     */
    @Column(name = "office_id")
    private String officeId;
    /**
     * @Fields officeNm 分公司名称
     */
    @Column(name = "office_nm")
    private String officeNm;
    /**
     * @Fields operId 操作员ID
     */
    @Column(name = "oper_id")
    private String operId;
    
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getOfficeId() {
		return officeId;
	}

	public void setOfficeId(String officeId) {
		this.officeId = officeId;
	}

	public String getOfficeNm() {
		return officeNm;
	}

	public void setOfficeNm(String officeNm) {
		this.officeNm = officeNm;
	}

	public String getOperId() {
		return operId;
	}

	public void setOperId(String operId) {
		this.operId = operId;
	}

	@Override
	public String toString() {
		return "OfficeInfo{" +
				"id=" + id +
				 ", officeId='" + officeId + '\'' + 
				 ", officeNm='" + officeNm + '\'' + 
				 ", operId='" + operId + '\'' +
				 "}";
	}

}
