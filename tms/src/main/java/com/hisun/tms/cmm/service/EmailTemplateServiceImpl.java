package com.hisun.tms.cmm.service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.hisun.tms.cmm.common.Constants;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.EmailTemplateInfo;
import com.hisun.tms.cmm.repository.DatatablesEmailTemplateInfoRepository;
import com.hisun.tms.cmm.repository.EmailTemplateInfoRepository;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;

/**
 * 邮件模板服务实现类
 * 
 * <AUTHOR>
 * @date 2025/7/17 16:18
 * @version 1.0
 */
@Service("emailTemplateService")
public class EmailTemplateServiceImpl implements EmailTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(EmailTemplateServiceImpl.class);

    @Autowired
    private DatatablesEmailTemplateInfoRepository datatablesEmailTemplateInfoRepository;

    @Autowired
    private EmailTemplateInfoRepository emailTemplateInfoRepository;

    @Override
    public void save(Iterable<EmailTemplateInfo> entities) {
        emailTemplateInfoRepository.save(entities);
    }

    @Override
    public Map<String, EmailTemplateInfo> modify(Map<String, EmailTemplateInfo> templateInfoMap) {
        List<EmailTemplateInfo> templateInfos = templateInfoMap.values().stream().collect(Collectors.toList());
        logger.debug("recive from web: {}", templateInfos.toString());
        Collection<String> ids = Collections2.transform(templateInfos, new Function<EmailTemplateInfo, String>() {
            @Override
            public String apply(final EmailTemplateInfo templateInfo) {
                return templateInfo.getId();
            }
        });
        logger.debug("query params: {}", ids.toString());
        List<EmailTemplateInfo> templateInfoToUpdate = emailTemplateInfoRepository.findAll(ids);
        logger.debug("query from db: {}", templateInfoToUpdate.toString());
        for (int i = 0; i < templateInfos.size(); i++) {
            BeanUtils.copyProperties(templateInfos.get(i), templateInfoToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
        }
        logger.debug("after copy with audit column ignore: {}", templateInfoToUpdate.toString());
        List<EmailTemplateInfo> list = emailTemplateInfoRepository.save(templateInfoToUpdate);
        logger.debug("after update: {}", list.toString());
        Map<String, EmailTemplateInfo> map = list.stream()
                .collect(Collectors.toMap(EmailTemplateInfo::getId, java.util.function.Function.identity()));
        Map<String, EmailTemplateInfo> mapData = map.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
    }

    @Override
    public void delete(Iterable<EmailTemplateInfo> entities) {
        emailTemplateInfoRepository.delete(entities);
    }

    @Override
    @Transactional("cmmTransactionManager")
    public DataTablesOutput<EmailTemplateInfo> findAll(GenericParamInput dataTablesInput) {
        String id = dataTablesInput.getExtra_search().get("id");
        String content = dataTablesInput.getExtra_search().get("content");
        String beginDateStr = dataTablesInput.getExtra_search().get("beginDate");
        String endDateStr = dataTablesInput.getExtra_search().get("endDate");
        // 开始日期
        LocalDate beginDate = null;
        // 结束日期
        LocalDate endDate = null;
        if (JudgeUtils.isNotBlank(beginDateStr)) {
            beginDateStr = beginDateStr.replaceAll("-", "").trim();
            beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
        } else {
            beginDate = LocalDate.now().minusDays(90);
        }
        if (JudgeUtils.isNotBlank(endDateStr)) {
            endDateStr = endDateStr.replaceAll("-", "").trim();
            endDate = DateTimeUtils.parseLocalDate(endDateStr);
        } else {
            endDate = LocalDate.now();
        }
        Specification<EmailTemplateInfo> queryParam = findByCondition(id, content, beginDate, endDate);
        DataTablesOutput<EmailTemplateInfo> dataTablesOutput = datatablesEmailTemplateInfoRepository.findAll(
                dataTablesInput,
                queryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    private Specification<EmailTemplateInfo> findByCondition(String id, String content, LocalDate beginDate,
            LocalDate endDate) {
        Specification<EmailTemplateInfo> queryParam = new Specification<EmailTemplateInfo>() {
            @Override
            public Predicate toPredicate(Root<EmailTemplateInfo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                if (JudgeUtils.isNotBlank(id)) {
                    predicates.add(cb.like(root.get("id"), "%" + id + "%"));
                }
                if (JudgeUtils.isNotBlank(content)) {
                    predicates.add(cb.or(cb.like(root.get("templateContentCn"), "%" + content + "%"),
                            cb.like(root.get("templateContentEn"), "%" + content + "%"),
                            cb.like(root.get("subject"), "%" + content + "%")));
                }
                predicates.add(cb.between(root.get("effDate"), beginDate, endDate));
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return queryParam;
    }
}