package com.hisun.tms.cmm.service;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.EmailTemplateInfo;

/**
 * 邮件模板服务接口
 * 
 * <AUTHOR>
 * @date 2025/7/17 16:17
 * @version 1.0
 */
public interface EmailTemplateService {

    void save(Iterable<EmailTemplateInfo> entities);

    Map<String, EmailTemplateInfo> modify(Map<String, EmailTemplateInfo> exampleMap);

    void delete(Iterable<EmailTemplateInfo> entities);

    DataTablesOutput<EmailTemplateInfo> findAll(GenericParamInput dataTablesInput);
}