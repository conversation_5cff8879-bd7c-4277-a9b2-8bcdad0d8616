package com.hisun.tms.cmm.controller;


import java.util.Iterator;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.MessageTemplateInfo;
import com.hisun.tms.cmm.service.MessageTemplateService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03 
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/messagectrl")
public class MessageTemplateController {

    @Autowired
    private MessageTemplateService messageTemplateService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/message') or hasRole('ROLE_ADMIN')")
    public ModelAndView bannerList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/messaegtemplate/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/message') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<MessageTemplateInfo> findAll(@Valid @RequestBody GenericParamInput input) {
        return messageTemplateService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/message') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<MessageTemplateInfo> add(@Valid @RequestBody DatatablesEditorRequest<MessageTemplateInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<MessageTemplateInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<MessageTemplateInfo> iterator = iterable.iterator(); iterator.hasNext();) {
            MessageTemplateInfo messageTemplateInfo = (MessageTemplateInfo) iterator.next();
            messageTemplateInfo.setOprId(user.getUsername());
        }
        messageTemplateService.save(iterable);
        DatatablesEditorResponse<MessageTemplateInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/message') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<MessageTemplateInfo> modify(@Valid @RequestBody DatatablesEditorRequest<MessageTemplateInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, MessageTemplateInfo> MerchantMap = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, MessageTemplateInfo>> iterator = MerchantMap.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<String, MessageTemplateInfo> entry = iterator.next();
            entry.getValue().setOprId(user.getUsername());
        }
        Map<String, MessageTemplateInfo> mapData = messageTemplateService.modify(MerchantMap);
        DatatablesEditorResponse<MessageTemplateInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/message') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<MessageTemplateInfo> delete(@Valid @RequestBody DatatablesEditorRequest<MessageTemplateInfo> datatablesEditorRequest) {
        Iterable<MessageTemplateInfo> iterable = datatablesEditorRequest.getData().values();
        messageTemplateService.delete(iterable);
        return new DatatablesEditorResponse<MessageTemplateInfo>();
    }
}
