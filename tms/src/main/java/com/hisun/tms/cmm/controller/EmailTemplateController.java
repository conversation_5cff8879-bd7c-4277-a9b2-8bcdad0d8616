package com.hisun.tms.cmm.controller;

import com.hisun.tms.cmm.model.EmailTemplateInfo;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.service.EmailTemplateService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.Valid;
import java.util.Iterator;
import java.util.Map;

/**
 * 邮件模板控制器
 * 
 * <AUTHOR>
 * @date 2025/7/17 16:13
 * @version 1.0
 */
@Controller
@RequestMapping("/cmm/emailtemplatectrl")
public class EmailTemplateController {

    @Autowired
    private EmailTemplateService emailTemplateService;

    @GetMapping
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/emailtemplate') or hasRole('ROLE_ADMIN')")
    public ModelAndView templateList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/emailtemplate/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/emailtemplate') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<EmailTemplateInfo> findAll(@Valid @RequestBody GenericParamInput input) {
        return emailTemplateService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/emailtemplate') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<EmailTemplateInfo> add(
            @Valid @RequestBody DatatablesEditorRequest<EmailTemplateInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<EmailTemplateInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<EmailTemplateInfo> iterator = iterable.iterator(); iterator.hasNext();) {
            EmailTemplateInfo emailTemplateInfo = (EmailTemplateInfo) iterator.next();
            emailTemplateInfo.setLvl("0");
            emailTemplateInfo.setOprId(user.getUsername());
        }
        emailTemplateService.save(iterable);
        DatatablesEditorResponse<EmailTemplateInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/emailtemplate') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<EmailTemplateInfo> modify(
            @Valid @RequestBody DatatablesEditorRequest<EmailTemplateInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, EmailTemplateInfo> templateMap = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, EmailTemplateInfo>> iterator = templateMap.entrySet().iterator(); iterator
                .hasNext();) {
            Map.Entry<String, EmailTemplateInfo> entry = iterator.next();
            entry.getValue().setOprId(user.getUsername());
        }
        Map<String, EmailTemplateInfo> mapData = emailTemplateService.modify(templateMap);
        DatatablesEditorResponse<EmailTemplateInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/emailtemplate') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<EmailTemplateInfo> delete(
            @Valid @RequestBody DatatablesEditorRequest<EmailTemplateInfo> datatablesEditorRequest) {
        Iterable<EmailTemplateInfo> iterable = datatablesEditorRequest.getData().values();
        emailTemplateService.delete(iterable);
        return new DatatablesEditorResponse<EmailTemplateInfo>();
    }
}
