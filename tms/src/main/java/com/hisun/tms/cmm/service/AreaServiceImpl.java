package com.hisun.tms.cmm.service;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.Column;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.datatables.mapping.Search;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.tms.cmm.model.AreaInfo;
import com.hisun.tms.cmm.repository.DatatablesAreaInfoRepository;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:56:37
 * @version V1.0
 */
@Service("areaService")
public class AreaServiceImpl implements AreaService {

    private static final Logger logger = LoggerFactory.getLogger(AreaServiceImpl.class);

    @Autowired
    private DatatablesAreaInfoRepository datatablesAreaInfoRepository;
    
    @Override
    @Transactional("cmmTransactionManager")
    public List<AreaInfo> findAll(String parentId) {
        Specification<AreaInfo> orderQueryParam = findByCondition(parentId);
        DataTablesInput dataTablesInput = new DataTablesInput();
        dataTablesInput.setDraw(50);
        dataTablesInput.setLength(50);
        List<Column> columns = new ArrayList<Column>();
        Search search = new Search(null, false);
        Column column = new Column(parentId, null, true, true, search);
        columns.add(column);
        dataTablesInput.setColumns(columns);
        DataTablesOutput<AreaInfo> dataTablesOutput = datatablesAreaInfoRepository.findAll(dataTablesInput,
                orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput.getData();
    }
    
    private Specification<AreaInfo> findByCondition(String parentId) {
        Specification<AreaInfo> orderQueryParam = new Specification<AreaInfo>() {
            @Override
            public Predicate toPredicate(Root<AreaInfo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("parentId"), parentId));
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return orderQueryParam;
    }
    
    @Override
    public AreaInfo get(String id) {
        Specification<AreaInfo> orderQueryParam = getByCondition(id);
        DataTablesInput dataTablesInput = new DataTablesInput();
        dataTablesInput.setDraw(1);
        dataTablesInput.setLength(1);
        List<Column> columns = new ArrayList<Column>();
        Search search = new Search(null, false);
        Column column = new Column(id, null, true, true, search);
        columns.add(column);
        dataTablesInput.setColumns(columns);
        DataTablesOutput<AreaInfo> dataTablesOutput = datatablesAreaInfoRepository.findAll(dataTablesInput,
                orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput.getData().get(0);
    }

    private Specification<AreaInfo> getByCondition(String id) {
        Specification<AreaInfo> orderQueryParam = new Specification<AreaInfo>() {
            @Override
            public Predicate toPredicate(Root<AreaInfo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("id"), id));
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return orderQueryParam;
    }

}
