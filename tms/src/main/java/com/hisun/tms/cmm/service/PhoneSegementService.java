package com.hisun.tms.cmm.service;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.PhoneSegementInfo;
import com.hisun.tms.cmm.model.PhoneSegementInfoDto;


/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:55:51 
 * @version V1.0
 */
public interface PhoneSegementService {

    void save(Iterable<PhoneSegementInfo> entities);

    Map<String, PhoneSegementInfo> modify(Map<String, PhoneSegementInfo> exampleMap);

    void delete(Iterable<PhoneSegementInfo> entities);

    DataTablesOutput<PhoneSegementInfoDto> findAll(String lang, GenericParamInput dataTablesInput);
}