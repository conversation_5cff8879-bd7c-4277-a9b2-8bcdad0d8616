package com.hisun.tms.cmm.service;

import com.hisun.tms.cmm.model.ConstantParamsModel;
import com.hisun.tms.cmm.repository.ConstantParamsRepository;
import com.hisun.tms.cmm.repository.DatatablesConstantParamsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/31
 */
@Service
public class ConstantParamsServiceImpl implements ConstantParamsService {

    private static final Logger logger = LoggerFactory.getLogger(ConstantParamsService.class);

    @Resource
    private DatatablesConstantParamsRepository datatablesConstantParamsRepository;

    @Resource
    private ConstantParamsRepository constantParamsRepository;

    @Override
    public DataTablesOutput<ConstantParamsModel> findAll(DataTablesInput dataTablesInput) {
        DataTablesOutput<ConstantParamsModel> dataTablesOutput = datatablesConstantParamsRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    @Override
    public void save(ConstantParamsModel constantParamsModel) {
        constantParamsModel.setCreateTime(LocalDateTime.now());
        constantParamsModel.setModifyTime(LocalDateTime.now());
        //TODO 操作员id
        constantParamsModel.setUpdOprId("sys");
        constantParamsRepository.save(constantParamsModel);
    }

    @Override
    public void update(ConstantParamsModel constantParamsModel) {
        ConstantParamsModel temp = constantParamsRepository.findOne(constantParamsModel.getParmId());
        constantParamsModel.setCreateTime(temp.getCreateTime());
        constantParamsModel.setModifyTime(LocalDateTime.now());
        //TODO 操作员id
        constantParamsModel.setUpdOprId("sys");
        constantParamsRepository.save(constantParamsModel);
    }

    @Override
    public void delete(ConstantParamsModel constantParamsModel) {
        constantParamsRepository.delete(constantParamsModel);
    }
}