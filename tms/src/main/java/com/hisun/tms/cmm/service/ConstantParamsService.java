package com.hisun.tms.cmm.service;

import com.hisun.tms.cmm.model.ConstantParamsModel;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * <AUTHOR>
 * @create 2017/8/31
 */
public interface ConstantParamsService {

    DataTablesOutput<ConstantParamsModel> findAll(DataTablesInput dataTablesInput);

    void save(ConstantParamsModel constantParamsModel);

    void update(ConstantParamsModel constantParamsModel);

    void delete(ConstantParamsModel constantParamsModel);
}
