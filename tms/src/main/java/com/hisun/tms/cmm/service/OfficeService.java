package com.hisun.tms.cmm.service;

import java.util.List;
import java.util.Map;


import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.OfficeInfo;
import com.hisun.tms.common.exception.CustomException;


/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:55:51 
 * @version V1.0
 */
public interface OfficeService {

    void save(Iterable<OfficeInfo> entities) throws CustomException;

    Map<String, OfficeInfo> modify(Map<String, OfficeInfo> exampleMap);

    Map<String,String> delete(Iterable<OfficeInfo> entities);

    DataTablesOutput<OfficeInfo> findAll(GenericParamInput dataTablesInput);

	List<OfficeInfo> findAllInf();
}