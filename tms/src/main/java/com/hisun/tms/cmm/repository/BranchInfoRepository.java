package com.hisun.tms.cmm.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.hisun.tms.cmm.model.BranchInfo;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:54:10 
 * @version V1.0
 */
public interface BranchInfoRepository extends JpaRepository<BranchInfo, String> {

	BranchInfo getByBraId(String braId);

	@Query(
            value = "SELECT * FROM cmm_branch_info WHERE office_id = :id",
            nativeQuery = true
    )
	List<BranchInfo> findByOfficeId(@Param("id") String officeId);

	BranchInfo findByBraId(String braId);
}
