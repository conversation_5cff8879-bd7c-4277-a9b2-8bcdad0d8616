package com.hisun.tms.cmm.controller;


import java.util.Iterator;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.NoticeInfo;
import com.hisun.tms.cmm.service.NoticeService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03 
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/noticctrl")
public class NoticeController {

    @Autowired
    private NoticeService noticService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/notic') or hasRole('ROLE_ADMIN')")
    public ModelAndView bannerList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/notic/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/notic') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<NoticeInfo> findAll(@Valid @RequestBody GenericParamInput input) {
        return noticService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/notic') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<NoticeInfo> add(@Valid @RequestBody DatatablesEditorRequest<NoticeInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<NoticeInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<NoticeInfo> iterator = iterable.iterator(); iterator.hasNext();) {
            NoticeInfo noticInfo = (NoticeInfo) iterator.next();
            noticInfo.setOprId(user.getUsername());
        }
        noticService.save(iterable);
        DatatablesEditorResponse<NoticeInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/notic') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<NoticeInfo> modify(@Valid @RequestBody DatatablesEditorRequest<NoticeInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, NoticeInfo> MerchantMap = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, NoticeInfo>> iterator = MerchantMap.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<String, NoticeInfo> entry = iterator.next();
            entry.getValue().setOprId(user.getUsername());
        }
        Map<String, NoticeInfo> mapData = noticService.modify(MerchantMap);
        DatatablesEditorResponse<NoticeInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/notic') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<NoticeInfo> delete(@Valid @RequestBody DatatablesEditorRequest<NoticeInfo> datatablesEditorRequest) {
        Iterable<NoticeInfo> iterable = datatablesEditorRequest.getData().values();
        noticService.delete(iterable);
        return new DatatablesEditorResponse<NoticeInfo>();
    }
}
