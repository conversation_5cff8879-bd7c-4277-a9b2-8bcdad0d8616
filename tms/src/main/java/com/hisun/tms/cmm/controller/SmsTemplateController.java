package com.hisun.tms.cmm.controller;


import java.util.Iterator;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.SmsTemplateInfo;
import com.hisun.tms.cmm.service.SmsTemplateService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03 
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/smstempaltectrl")
public class SmsTemplateController {

    @Autowired
    private SmsTemplateService smsTemplateService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/smstempalte') or hasRole('ROLE_ADMIN')")
    public ModelAndView bannerList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/smstemplate/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/smstempalte') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<SmsTemplateInfo> findAll(@Valid @RequestBody GenericParamInput input) {
        return smsTemplateService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/smstempalte') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<SmsTemplateInfo> add(@Valid @RequestBody DatatablesEditorRequest<SmsTemplateInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<SmsTemplateInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<SmsTemplateInfo> iterator = iterable.iterator(); iterator.hasNext();) {
            SmsTemplateInfo smsTemplateInfo = (SmsTemplateInfo) iterator.next();
            smsTemplateInfo.setLvl("0");
            smsTemplateInfo.setOprId(user.getUsername());
        }
        smsTemplateService.save(iterable);
        DatatablesEditorResponse<SmsTemplateInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/smstempalte') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<SmsTemplateInfo> modify(@Valid @RequestBody DatatablesEditorRequest<SmsTemplateInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, SmsTemplateInfo> MerchantMap = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, SmsTemplateInfo>> iterator = MerchantMap.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<String, SmsTemplateInfo> entry = iterator.next();
            entry.getValue().setOprId(user.getUsername());
        }
        Map<String, SmsTemplateInfo> mapData = smsTemplateService.modify(MerchantMap);
        DatatablesEditorResponse<SmsTemplateInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/smstempalte') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<SmsTemplateInfo> delete(@Valid @RequestBody DatatablesEditorRequest<SmsTemplateInfo> datatablesEditorRequest) {
        Iterable<SmsTemplateInfo> iterable = datatablesEditorRequest.getData().values();
        smsTemplateService.delete(iterable);
        return new DatatablesEditorResponse<SmsTemplateInfo>();
    }
}
