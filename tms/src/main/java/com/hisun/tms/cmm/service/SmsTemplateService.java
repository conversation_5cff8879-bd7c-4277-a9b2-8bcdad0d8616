package com.hisun.tms.cmm.service;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.SmsTemplateInfo;


/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:55:51 
 * @version V1.0
 */
public interface SmsTemplateService {

    void save(Iterable<SmsTemplateInfo> entities);

    Map<String, SmsTemplateInfo> modify(Map<String, SmsTemplateInfo> exampleMap);

    void delete(Iterable<SmsTemplateInfo> entities);

    DataTablesOutput<SmsTemplateInfo> findAll(GenericParamInput dataTablesInput);
}