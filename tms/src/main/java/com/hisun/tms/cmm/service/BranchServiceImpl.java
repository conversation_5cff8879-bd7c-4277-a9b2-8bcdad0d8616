package com.hisun.tms.cmm.service;


import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.hisun.tms.cmm.common.Constants;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.BranchInfo;
import com.hisun.tms.cmm.repository.DatatablesBranchInfoRepository;
import com.hisun.tms.cmm.repository.BranchInfoRepository;
import com.hisun.tms.common.exception.CustomException;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.sys.model.User;
import com.hisun.tms.sys.repository.UserRepository;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:56:37
 * @version V1.0
 */
@Service("branchService")
public class BranchServiceImpl implements BranchService {

    private static final Logger logger = LoggerFactory.getLogger(BranchServiceImpl.class);

    @Autowired
    private DatatablesBranchInfoRepository datatablesBranchInfoRepository;

    @Autowired
    private BranchInfoRepository branchInfoRepository;
    
    @Autowired
    private UserRepository userRepository;

    @Override
    public void save(Iterable<BranchInfo> entities) throws CustomException{
    	//判断是否存在相同部门编号的部门
    	BranchInfo branchInfo=branchInfoRepository.findByBraId(entities.iterator().next().getBraId());
    	if(JudgeUtils.isNotNull(branchInfo)){
    		throw new CustomException("部门编号已存在");
    	}else{
    		branchInfoRepository.save(entities);
    	}
    }

    @Override
    public Map<String, BranchInfo> modify(Map<String, BranchInfo> noticInfoMap) {
        List<BranchInfo> BranchInfos = noticInfoMap.values().stream().collect(Collectors.toList());
        logger.debug("recive from web: {}", BranchInfos.toString());
        Collection<String> ids = Collections2.transform(BranchInfos, new Function<BranchInfo, String>() {
            @Override
            public String apply(final BranchInfo BranchInfo) {
                return String.valueOf(BranchInfo.getId());
            }
        });
        logger.debug("query params: {}", ids.toString());
        List<BranchInfo> BranchInfoToUpdate = branchInfoRepository.findAll(ids);
        logger.debug("query from db: {}", BranchInfoToUpdate.toString());
        for (int i = 0; i < BranchInfos.size(); i++) {
            BeanUtils.copyProperties(BranchInfos.get(i), BranchInfoToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
        }
        logger.debug("after copy with audit column ignore: {}", BranchInfoToUpdate.toString());
        List<BranchInfo> list = branchInfoRepository.save(BranchInfoToUpdate);
        logger.debug("after update: {}", list.toString());
        Map<Integer, BranchInfo> map = list.stream()
                .collect(Collectors.toMap(BranchInfo::getId, java.util.function.Function.identity()));
        Map<String, BranchInfo> mapData = map.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
    }

    @Override
    public Map<String,String> delete(Iterable<BranchInfo> entities) {
    	String braId=entities.iterator().next().getBraId();
    	//判断是否有归属于这个部门的用户
    	List<User> users=null;
    	if(JudgeUtils.isNotBlank(braId)){
    		users=userRepository.findByBraId(braId);
    	}
    	Map<String,String> map=new HashMap<String,String>();
    	if(JudgeUtils.isNotEmpty(users)){
    		map.put("PREUSER", "YES");
    	}else{
    		branchInfoRepository.delete(entities);
    		map.put("PREUSER", "NO");
    	}
    	return map;
    }

    @Override
    @Transactional("cmmTransactionManager")
    public DataTablesOutput<BranchInfo> findAll(GenericParamInput dataTablesInput) {
    	String braId = dataTablesInput.getExtra_search().get("braId");
        String braNm = dataTablesInput.getExtra_search().get("braNm");
        String officeId = dataTablesInput.getExtra_search().get("officeId");
        Specification<BranchInfo> orderQueryParam=Specifications.<BranchInfo>and()
                .eq(JudgeUtils.isNotBlank(braId), "braId", braId)
                .eq(JudgeUtils.isNotBlank(braNm), "braNm", braNm)
                .eq(JudgeUtils.isNotBlank(officeId), "officeId", officeId)
                .build();
        DataTablesOutput<BranchInfo> dataTablesOutput = datatablesBranchInfoRepository.findAll(dataTablesInput, orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

	@Override
	@Transactional("cmmTransactionManager")
	public List<BranchInfo> findAllInf(String officeId) {
		DataTablesInput dataTablesInput = new DataTablesInput();
        dataTablesInput.setDraw(50);
        dataTablesInput.setLength(50);
        Specification<BranchInfo> orderQueryParam=Specifications.<BranchInfo>and()
                .eq(JudgeUtils.isNotBlank(officeId), "officeId", officeId)
                .build();
		DataTablesOutput<BranchInfo> dataTablesOutput = datatablesBranchInfoRepository.findAll(dataTablesInput,orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput.getData();
	}
	
	@Override
	@Transactional("cmmTransactionManager")
	public List<BranchInfo> findByName(String officeNm) {
		DataTablesInput dataTablesInput = new DataTablesInput();
        dataTablesInput.setDraw(50);
        dataTablesInput.setLength(50);
        Specification<BranchInfo> orderQueryParam=Specifications.<BranchInfo>and()
                .eq(JudgeUtils.isNotBlank(officeNm), "officeNm", officeNm)
                .build();
		DataTablesOutput<BranchInfo> dataTablesOutput = datatablesBranchInfoRepository.findAll(dataTablesInput,orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput.getData();
	}

}
