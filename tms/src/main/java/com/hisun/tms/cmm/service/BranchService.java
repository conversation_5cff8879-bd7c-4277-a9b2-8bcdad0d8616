package com.hisun.tms.cmm.service;

import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.common.exception.CustomException;
import com.hisun.tms.cmm.model.BranchInfo;


/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:55:51 
 * @version V1.0
 */
public interface BranchService {

    void save(Iterable<BranchInfo> entities) throws CustomException;

    Map<String, BranchInfo> modify(Map<String, BranchInfo> exampleMap);

    Map<String,String> delete(Iterable<BranchInfo> entities);

    DataTablesOutput<BranchInfo> findAll(GenericParamInput dataTablesInput);

	List<BranchInfo> findAllInf(String officeId);
	
	List<BranchInfo> findByName(String officeNm);
}