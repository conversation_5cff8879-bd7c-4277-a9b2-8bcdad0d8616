package com.hisun.tms.cmm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/8/31
 */
@Entity
@Table(name = "cmm_constant_param")
public class ConstantParamsModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "parm_id")
    private int parmId;

    @Column(name = "parm_nm")
    private String parmNm;

    @Column(name = "parm_val")
    private String parmVal;

    @Column(name = "eff_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    private LocalDate effDt;

    @Column(name = "exp_dt")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    private LocalDate expDt;

    @Column(name = "eff_flg")
    private String effFlg;

    @Column(name = "parm_disp_nm")
    private String parmDispNm;

    @Column(name = "parm_cls")
    private String parmCls;

    @Column(name = "rmk")
    private String rmk;

    @Column(name = "upd_opr_id")
    private String updOprId;

    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime modifyTime;

    public int getParmId() {
        return parmId;
    }

    public void setParmId(int parmId) {
        this.parmId = parmId;
    }

    public String getParmNm() {
        return parmNm;
    }

    public void setParmNm(String parmNm) {
        this.parmNm = parmNm;
    }

    public String getParmVal() {
        return parmVal;
    }

    public void setParmVal(String parmVal) {
        this.parmVal = parmVal;
    }

    public LocalDate getEffDt() {
        return effDt;
    }

    public void setEffDt(LocalDate effDt) {
        this.effDt = effDt;
    }

    public LocalDate getExpDt() {
        return expDt;
    }

    public void setExpDt(LocalDate expDt) {
        this.expDt = expDt;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getParmDispNm() {
        return parmDispNm;
    }

    public void setParmDispNm(String parmDispNm) {
        this.parmDispNm = parmDispNm;
    }

    public String getParmCls() {
        return parmCls;
    }

    public void setParmCls(String parmCls) {
        this.parmCls = parmCls;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "ConstantParamsModel{" +
                "parmId=" + parmId +
                ", parmNm='" + parmNm + '\'' +
                ", parmVal='" + parmVal + '\'' +
                ", effDt=" + effDt +
                ", expDt=" + expDt +
                ", effFlg='" + effFlg + '\'' +
                ", parmDispNm='" + parmDispNm + '\'' +
                ", parmCls='" + parmCls + '\'' +
                ", rmk='" + rmk + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}