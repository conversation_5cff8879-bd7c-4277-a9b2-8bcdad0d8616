package com.hisun.tms.cmm.service;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.NoticeInfo;


/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:55:51 
 * @version V1.0
 */
public interface NoticeService {

    void save(Iterable<NoticeInfo> entities);

    Map<String, NoticeInfo> modify(Map<String, NoticeInfo> exampleMap);

    void delete(Iterable<NoticeInfo> entities);

    DataTablesOutput<NoticeInfo> findAll(GenericParamInput dataTablesInput);
}