package com.hisun.tms.cmm.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:41:24
 * @version V1.0
 */
@Entity
@Table(name = "cmm_banner_public")
public class BannerInfo extends AbstractEntityTmpe {
    /**
     * @Fields id ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private String id;
    /**
     * @Fields title 标题
     */
    @Column(name = "title")
    private String title;
    /**
     * @Fields bannerUrlKh 柬语图片url
     */
    @Column(name = "banner_url_kh")
    private String bannerUrlKh;
    /**
     * @Fields bannerUrlCn 中文图片url
     */
    @Column(name = "banner_url_cn")
    private String bannerUrlCn;
    /**
     * @Fields bannerUrlEn 英文图片url
     */
    @Column(name = "banner_url_en")
    private String bannerUrlEn;
    /**
     * @Fields detailUrlKh 柬语详情url
     */
    @Column(name = "detail_url_kh")
    private String detailUrlKh;
    /**
     * @Fields detailUrlCn 中文详情url
     */
    @Column(name = "detail_url_cn")
    private String detailUrlCn;
    /**
     * @Fields detailUrlEn 英文详情url
     */
    @Column(name = "detail_url_en")
    private String detailUrlEn;
    /**
     * @Fields channel 渠道类型 portal:官网 app:用户app mportal:商户官网 mapp:商户app
     */
    @Column(name = "channel")
    private String channel;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    @Column(name = "stats")
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "eff_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getBannerUrlKh() {
        return bannerUrlKh;
    }

    public void setBannerUrlKh(String bannerUrlKh) {
        this.bannerUrlKh = bannerUrlKh;
    }

    public String getBannerUrlCn() {
        return bannerUrlCn;
    }

    public void setBannerUrlCn(String bannerUrlCn) {
        this.bannerUrlCn = bannerUrlCn;
    }

    public String getBannerUrlEn() {
        return bannerUrlEn;
    }

    public void setBannerUrlEn(String bannerUrlEn) {
        this.bannerUrlEn = bannerUrlEn;
    }

    public String getDetailUrlKh() {
        return detailUrlKh;
    }

    public void setDetailUrlKh(String detailUrlKh) {
        this.detailUrlKh = detailUrlKh;
    }

    public String getDetailUrlCn() {
        return detailUrlCn;
    }

    public void setDetailUrlCn(String detailUrlCn) {
        this.detailUrlCn = detailUrlCn;
    }

    public String getDetailUrlEn() {
        return detailUrlEn;
    }

    public void setDetailUrlEn(String detailUrlEn) {
        this.detailUrlEn = detailUrlEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

}
