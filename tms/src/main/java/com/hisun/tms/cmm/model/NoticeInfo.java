package com.hisun.tms.cmm.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:41:24
 * @version V1.0
 */
@Entity
@Table(name = "cmm_notice_public")
public class NoticeInfo extends AbstractEntityTmpe {
    /**
     * @Fields id ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private String id;
    /**
     * @Fields noticeTitleKh 柬文标题
     */
    @Column(name = "notice_title_kh")
    private String noticeTitleKh;
    /**
     * @Fields noticeTitleCn 中文标题
     */
    @Column(name = "notice_title_cn")
    private String noticeTitleCn;
    /**
     * @Fields noticeTitleEn 英文标题
     */
    @Column(name = "notice_title_en")
    private String noticeTitleEn;
    /**
     * @Fields noticeContentKh 柬文公告内容
     */
    @Column(name = "notice_content_kh")
    private String noticeContentKh;
    /**
     * @Fields noticeContentCn 中文公告内容
     */
    @Column(name = "notice_content_cn")
    private String noticeContentCn;
    /**
     * @Fields noticeContentEn 英文公告内容
     */
    @Column(name = "notice_content_en")
    private String noticeContentEn;
    /**
     * @Fields channel 渠道类型 portal:官网 app:用户app mportal:商户官网 mapp:商户app
     */
    @Column(name = "channel")
    private String channel;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    @Column(name = "stats")
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "eff_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNoticeTitleKh() {
        return noticeTitleKh;
    }

    public void setNoticeTitleKh(String noticeTitleKh) {
        this.noticeTitleKh = noticeTitleKh;
    }

    public String getNoticeTitleCn() {
        return noticeTitleCn;
    }

    public void setNoticeTitleCn(String noticeTitleCn) {
        this.noticeTitleCn = noticeTitleCn;
    }

    public String getNoticeTitleEn() {
        return noticeTitleEn;
    }

    public void setNoticeTitleEn(String noticeTitleEn) {
        this.noticeTitleEn = noticeTitleEn;
    }

    public String getNoticeContentKh() {
        return noticeContentKh;
    }

    public void setNoticeContentKh(String noticeContentKh) {
        this.noticeContentKh = noticeContentKh;
    }

    public String getNoticeContentCn() {
        return noticeContentCn;
    }

    public void setNoticeContentCn(String noticeContentCn) {
        this.noticeContentCn = noticeContentCn;
    }

    public String getNoticeContentEn() {
        return noticeContentEn;
    }

    public void setNoticeContentEn(String noticeContentEn) {
        this.noticeContentEn = noticeContentEn;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

}
