package com.hisun.tms.cmm.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:41:24
 * @version V1.0
 */
@Entity
@Table(name = "cmm_message_template")
public class MessageTemplateInfo extends AbstractEntityTmpe {
    /**
     * @Fields id ID
     */
    @Id
    @Column(name = "id")
    private String id;
    /**
     * @Fields passPushFlg 透出标识
     */
    @Column(name = "pass_push_flg")
    private String passPushFlg;
    /**
     * @Fields userType 用户类型
     */
    @Column(name = "user_type")
    private String userType;
    /**
     * @Fields type 消息类型
     */
    @Column(name = "type")
    private String type;
    /**
     * @Fields messageTitleKh 柬文消息标题
     */
    @Column(name = "message_title_kh")
    private String messageTitleKh;
    /**
     * @Fields messageTitleCn 中文消息标题
     */
    @Column(name = "message_title_cn")
    private String messageTitleCn;
    /**
     * @Fields messageTitleEn 英文消息标题
     */
    @Column(name = "message_title_en")
    private String messageTitleEn;
    /**
     * @Fields replaceField 替换变量
     */
    @Column(name = "replace_field")
    private String replaceField;
    /**
     * @Fields templateContentKh 柬文短信模版
     */
    @Column(name = "template_content_kh")
    private String templateContentKh;
    /**
     * @Fields noticeContentCn 中文短信模版
     */
    @Column(name = "template_content_cn")
    private String templateContentCn;
    /**
     * @Fields templateContentEn 英文短信模版
     */
    @Column(name = "template_content_en")
    private String templateContentEn;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    @Column(name = "stats")
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "eff_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPassPushFlg() {
        return passPushFlg;
    }

    public void setPassPushFlg(String passPushFlg) {
        this.passPushFlg = passPushFlg;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getMessageTitleKh() {
        return messageTitleKh;
    }

    public void setMessageTitleKh(String messageTitleKh) {
        this.messageTitleKh = messageTitleKh;
    }

    public String getMessageTitleCn() {
        return messageTitleCn;
    }

    public void setMessageTitleCn(String messageTitleCn) {
        this.messageTitleCn = messageTitleCn;
    }

    public String getMessageTitleEn() {
        return messageTitleEn;
    }

    public void setMessageTitleEn(String messageTitleEn) {
        this.messageTitleEn = messageTitleEn;
    }

    public String getReplaceField() {
        return replaceField;
    }

    public void setReplaceField(String replaceField) {
        this.replaceField = replaceField;
    }

    public String getTemplateContentKh() {
        return templateContentKh;
    }

    public void setTemplateContentKh(String templateContentKh) {
        this.templateContentKh = templateContentKh;
    }

    public String getTemplateContentCn() {
        return templateContentCn;
    }

    public void setTemplateContentCn(String templateContentCn) {
        this.templateContentCn = templateContentCn;
    }

    public String getTemplateContentEn() {
        return templateContentEn;
    }

    public void setTemplateContentEn(String templateContentEn) {
        this.templateContentEn = templateContentEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

}
