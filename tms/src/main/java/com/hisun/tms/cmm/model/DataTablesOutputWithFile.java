package com.hisun.tms.cmm.model;

import java.util.HashMap;
import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

public class DataTablesOutputWithFile<T> extends DataTablesOutput<T> {

    /** 上传文件Map */
    private Map<String, Map<String, FileUploadInfo>> files = new HashMap<String, Map<String, FileUploadInfo>>();

    public Map<String, Map<String, FileUploadInfo>> getFiles() {
        return files;
    }

    public void setFiles(Map<String, Map<String, FileUploadInfo>> files) {
        this.files = files;
    }

}
