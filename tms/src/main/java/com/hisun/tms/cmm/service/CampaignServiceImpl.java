package com.hisun.tms.cmm.service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.hisun.tms.cmm.common.Constants;
import com.hisun.tms.cmm.model.CampaignInfo;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.repository.CampaignInfoRepository;
import com.hisun.tms.cmm.repository.DatatablesCampaignInfoRepository;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:56:37
 * @version V1.0
 */
@Service("campaignService")
public class CampaignServiceImpl implements CampaignService {

    private static final Logger logger = LoggerFactory.getLogger(CampaignServiceImpl.class);

    @Autowired
    private DatatablesCampaignInfoRepository datatablesCampaignInfoRepository;

    @Autowired
    private CampaignInfoRepository campaignInfoRepository;

    @Override
    public void save(Iterable<CampaignInfo> entities) {
        campaignInfoRepository.save(entities);
    }

    @Override
    public Map<String, CampaignInfo> modify(Map<String, CampaignInfo> noticInfoMap) {
        List<CampaignInfo> noticInfos = noticInfoMap.values().stream().collect(Collectors.toList());
        logger.debug("recive from web: {}", noticInfos.toString());
        Collection<String> ids = Collections2.transform(noticInfos, new Function<CampaignInfo, String>() {
            @Override
            public String apply(final CampaignInfo noticInfo) {
                return noticInfo.getId();
            }
        });
        logger.debug("query params: {}", ids.toString());
        List<CampaignInfo> noticInfoToUpdate = campaignInfoRepository.findAll(ids);
        logger.debug("query from db: {}", noticInfoToUpdate.toString());
        for (int i = 0; i < noticInfos.size(); i++) {
            BeanUtils.copyProperties(noticInfos.get(i), noticInfoToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
        }
        logger.debug("after copy with audit column ignore: {}", noticInfoToUpdate.toString());
        List<CampaignInfo> list = campaignInfoRepository.save(noticInfoToUpdate);
        logger.debug("after update: {}", list.toString());
        Map<String, CampaignInfo> map = list.stream()
                .collect(Collectors.toMap(CampaignInfo::getId, java.util.function.Function.identity()));
        Map<String, CampaignInfo> mapData = map.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
    }

    @Override
    public void delete(Iterable<CampaignInfo> entities) {
        campaignInfoRepository.delete(entities);
    }

    @Override
    @Transactional("cmmTransactionManager")
    public DataTablesOutput<CampaignInfo> findAll(GenericParamInput dataTablesInput) {
        String title = dataTablesInput.getExtra_search().get("title");
        String beginDateStr = dataTablesInput.getExtra_search().get("beginDate");
        String endDateStr = dataTablesInput.getExtra_search().get("endDate");
        // 订单开始日期
        LocalDate beginDate = null;
        // 订单结束日期
        LocalDate endDate = null;
        if (JudgeUtils.isNotBlank(beginDateStr)) {
            beginDateStr = beginDateStr.replaceAll("-", "").trim();
            beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
        } else {
            beginDate = LocalDate.now().minusDays(90);
        }
        if (JudgeUtils.isNotBlank(endDateStr)) {
            endDateStr = endDateStr.replaceAll("-", "").trim();
            endDate = DateTimeUtils.parseLocalDate(endDateStr);
        } else {
            endDate = LocalDate.now();
        }
        Specification<CampaignInfo> orderQueryParam = findByCondition(title, beginDate, endDate);
        DataTablesOutput<CampaignInfo> dataTablesOutput = datatablesCampaignInfoRepository.findAll(dataTablesInput,
                orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    private Specification<CampaignInfo> findByCondition(String title, LocalDate beginDate, LocalDate endDate) {
        Specification<CampaignInfo> orderQueryParam = new Specification<CampaignInfo>() {
            @Override
            public Predicate toPredicate(Root<CampaignInfo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                if (JudgeUtils.isNotBlank(title)) {
                    predicates.add(cb.or(cb.like(root.get("campaignTitleKh"), "%" + title + "%"),
                            cb.like(root.get("campaignTitleCn"), "%" + title + "%"),
                            cb.like(root.get("campaignTitleEn"), "%" + title + "%")));
                }
                predicates.add(cb.between(root.get("effDate"), beginDate, endDate));
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return orderQueryParam;
    }
}
