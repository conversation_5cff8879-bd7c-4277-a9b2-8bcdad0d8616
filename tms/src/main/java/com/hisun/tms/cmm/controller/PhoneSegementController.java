package com.hisun.tms.cmm.controller;


import java.util.Iterator;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.PhoneSegementInfo;
import com.hisun.tms.cmm.model.PhoneSegementInfoDto;
import com.hisun.tms.cmm.service.PhoneSegementService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03 
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/phsctrl")
public class PhoneSegementController {

    @Autowired
    private PhoneSegementService phoneSegementService;

    @GetMapping
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/phsctrl') or hasRole('ROLE_ADMIN')")
    public ModelAndView phoneSegementList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/phonesegement/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/phsctrl') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<PhoneSegementInfoDto> findAll(@Valid @CookieValue("lang") String lang, @RequestBody GenericParamInput input) {
        return phoneSegementService.findAll(lang, input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/phsctrl:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<PhoneSegementInfo> add(@Valid @RequestBody DatatablesEditorRequest<PhoneSegementInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<PhoneSegementInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<PhoneSegementInfo> iterator = iterable.iterator(); iterator.hasNext();) {
            PhoneSegementInfo noticInfo = (PhoneSegementInfo) iterator.next();
            noticInfo.setOprId(user.getUsername());
        }
        phoneSegementService.save(iterable);
        DatatablesEditorResponse<PhoneSegementInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/phsctrl:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<PhoneSegementInfo> modify(@Valid @RequestBody DatatablesEditorRequest<PhoneSegementInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, PhoneSegementInfo> map = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, PhoneSegementInfo>> iterator = map.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<String, PhoneSegementInfo> entry = iterator.next();
            entry.getValue().setOprId(user.getUsername());
        }
        Map<String, PhoneSegementInfo> mapData = phoneSegementService.modify(map);
        DatatablesEditorResponse<PhoneSegementInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/cmmmgr/sysmgr/phsctrl:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<PhoneSegementInfo> delete(@Valid @RequestBody DatatablesEditorRequest<PhoneSegementInfo> datatablesEditorRequest) {
        Iterable<PhoneSegementInfo> iterable = datatablesEditorRequest.getData().values();
        phoneSegementService.delete(iterable);
        return new DatatablesEditorResponse<PhoneSegementInfo>();
    }
}
