package com.hisun.tms.cmm.model;

import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.tms.common.audit.AbstractEntityTmpe;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:41:24
 * @version V1.0
 */
@Entity
@Table(name = "cmm_area_public")
public class AreaInfo extends AbstractEntityTmpe {
    /**
     * @Fields id
     */
    @Id
    @Column(name = "id")
    private String id;
    /**
     * @Fields parentId 父ID
     */
    @Column(name = "parent_id")
    private String parentId;
    /**
     * @Fields area_name_kh 区域名称
     */
    @Column(name = "area_name_kh")
    private String areaNameKh;
    /**
     * @Fields area_name_zh 区域名称
     */
    @Column(name = "area_name_zh")
    private String areaNameZh;
    /**
     * @Fields area_name_en 区域名称
     */
    @Column(name = "area_name_en")
    private String areaNameEn;
    /**
     * @Fields sort 排序
     */
    @Column(name = "sort")
    private String sort;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getAreaNameKh() {
        return areaNameKh;
    }

    public void setAreaNameKh(String areaNameKh) {
        this.areaNameKh = areaNameKh;
    }

    public String getAreaNameZh() {
        return areaNameZh;
    }

    public void setAreaNameZh(String areaNameZh) {
        this.areaNameZh = areaNameZh;
    }

    public String getAreaNameEn() {
        return areaNameEn;
    }

    public void setAreaNameEn(String areaNameEn) {
        this.areaNameEn = areaNameEn;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

}
