package com.hisun.tms.cmm.service;

import java.util.Map;

import com.hisun.tms.cmm.model.BannerInfo;
import com.hisun.tms.cmm.model.DataTablesOutputWithFile;
import com.hisun.tms.cmm.model.GenericParamInput;


/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:55:51 
 * @version V1.0
 */
public interface BannerService {

    void save(Iterable<BannerInfo> entities);

    Map<String, BannerInfo> modify(Map<String, BannerInfo> exampleMap);

    void delete(Iterable<BannerInfo> entities);

    DataTablesOutputWithFile<BannerInfo> findAll(GenericParamInput dataTablesInput);
}