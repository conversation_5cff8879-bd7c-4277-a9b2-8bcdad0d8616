package com.hisun.tms.cmm.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.hisun.tms.cmm.common.Constants;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.PhoneSegementInfo;
import com.hisun.tms.cmm.model.PhoneSegementInfoDto;
import com.hisun.tms.cmm.repository.DatatablesPhoneSegementInfoRepository;
import com.hisun.tms.cmm.repository.PhoneSegementInfoRepository;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.specifications.Specifications;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:56:37
 * @version V1.0
 */
@Service("phoneSegementInfoService")
public class PhoneSegementServiceImpl implements PhoneSegementService {

    private static final Logger logger = LoggerFactory.getLogger(PhoneSegementServiceImpl.class);

    @Autowired
    private DatatablesPhoneSegementInfoRepository datatablesPhoneSegementInfoRepository;

    @Autowired
    private PhoneSegementInfoRepository phoneSegementInfoRepository;

    @Override
    public void save(Iterable<PhoneSegementInfo> entities) {
        phoneSegementInfoRepository.save(entities);
    }

    @Override
    public Map<String, PhoneSegementInfo> modify(Map<String, PhoneSegementInfo> noticInfoMap) {
        List<PhoneSegementInfo> phoneSegementInfos = noticInfoMap.values().stream().collect(Collectors.toList());
        logger.debug("recive from web: {}", phoneSegementInfos.toString());
        Collection<String> ids = Collections2.transform(phoneSegementInfos, new Function<PhoneSegementInfo, String>() {
            @Override
            public String apply(final PhoneSegementInfo phoneSegementInfo) {
                return phoneSegementInfo.getPrefixNumber();
            }
        });
        logger.debug("query params: {}", ids.toString());
        List<PhoneSegementInfo> phoneSegementInfoToUpdate = phoneSegementInfoRepository.findAll(ids);
        logger.debug("query from db: {}", phoneSegementInfoToUpdate.toString());
        for (int i = 0; i < phoneSegementInfos.size(); i++) {
            BeanUtils.copyProperties(phoneSegementInfos.get(i), phoneSegementInfoToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
        }
        logger.debug("after copy with audit column ignore: {}", phoneSegementInfoToUpdate.toString());
        List<PhoneSegementInfo> list = phoneSegementInfoRepository.save(phoneSegementInfoToUpdate);
        logger.debug("after update: {}", list.toString());
        Map<String, PhoneSegementInfo> map = list.stream()
                .collect(Collectors.toMap(PhoneSegementInfo::getPrefixNumber, java.util.function.Function.identity()));
        Map<String, PhoneSegementInfo> mapData = map.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
    }

    @Override
    public void delete(Iterable<PhoneSegementInfo> entities) {
        phoneSegementInfoRepository.delete(entities);
    }

    @Override
    @Transactional("cmmTransactionManager")
    public DataTablesOutput<PhoneSegementInfoDto> findAll(String lang, GenericParamInput dataTablesInput) {
        String prefixNumber = dataTablesInput.getExtra_search().get("prefixNumber");
        String carrier = dataTablesInput.getExtra_search().get("carrier");
        String countryCode = dataTablesInput.getExtra_search().get("countryCode");
        String provinceCode = dataTablesInput.getExtra_search().get("provinceCode");
        Specification<PhoneSegementInfo> orderQueryParam=Specifications.<PhoneSegementInfo>and()
                .eq(JudgeUtils.isNotBlank(prefixNumber), "prefixNumber", prefixNumber)
                .like(JudgeUtils.isNotBlank(carrier), "carrier", "%"+ carrier +"%")
                .eq(JudgeUtils.isNotBlank(countryCode), "countryCode", countryCode)
                .eq(JudgeUtils.isNotBlank(provinceCode), "provinceCode", provinceCode)
                .build();
        DataTablesOutput<PhoneSegementInfo> dataTablesOutput = datatablesPhoneSegementInfoRepository.findAll(dataTablesInput, orderQueryParam);
        DataTablesOutput<PhoneSegementInfoDto> dataTablesOutput2 = new DataTablesOutput<>();
        List<PhoneSegementInfoDto> phoneSegementInfoDtos = new ArrayList<>();
        List<PhoneSegementInfo> phoneSegementInfos =  dataTablesOutput.getData();
        for (PhoneSegementInfo phoneSegementInfo : phoneSegementInfos) {
            // 根据语言环境转换
            PhoneSegementInfoDto phoneSegementInfoDto = new PhoneSegementInfoDto();
            BeanUtils.copyProperties(phoneSegementInfo, phoneSegementInfoDto);
            phoneSegementInfoDto.setCountry(phoneSegementInfo.getCountryEn());
            phoneSegementInfoDto.setProvince(phoneSegementInfo.getProvinceEn());
            phoneSegementInfoDto.setCity(phoneSegementInfo.getCityEn());
            if (lang.equals("km")) {
                phoneSegementInfoDto.setCountry(phoneSegementInfo.getCountryKh());
                phoneSegementInfoDto.setProvince(phoneSegementInfo.getProvinceKh());
                phoneSegementInfoDto.setCity(phoneSegementInfo.getCityKh());
            } else if (lang.equals("zh")) {
                phoneSegementInfoDto.setCountry(phoneSegementInfo.getCountryZh());
                phoneSegementInfoDto.setProvince(phoneSegementInfo.getProvinceZh());
                phoneSegementInfoDto.setCity(phoneSegementInfo.getCityZh());
            }
            phoneSegementInfoDtos.add(phoneSegementInfoDto);
        }
        dataTablesOutput2.setData(phoneSegementInfoDtos);
        dataTablesOutput2.setDraw(dataTablesOutput.getDraw());
        dataTablesOutput2.setError(dataTablesOutput.getError());
        dataTablesOutput2.setRecordsFiltered(dataTablesOutput.getRecordsFiltered());
        dataTablesOutput2.setRecordsTotal(dataTablesOutput.getRecordsTotal());
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput2;
    }
}
