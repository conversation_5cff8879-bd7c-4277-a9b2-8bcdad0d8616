package com.hisun.tms.cmm.controller;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.cmm.model.BannerInfo;
import com.hisun.tms.cmm.model.DataTablesOutputWithFile;
import com.hisun.tms.cmm.model.FileUploadInfo;
import com.hisun.tms.cmm.model.FileUploadOutput;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.service.BannerService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.util.ImageUploadUtil;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/bannerctrl")
public class BannerController {

    @Autowired
    private Environment env;

    @Autowired
    private BannerService bannerService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/banner') or hasRole('ROLE_ADMIN')")
    public ModelAndView bannerList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/banner/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/banner') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutputWithFile<BannerInfo> findAll(@Valid @RequestBody GenericParamInput input) {
        return bannerService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/banner') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<BannerInfo> add(
            @Valid @RequestBody DatatablesEditorRequest<BannerInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<BannerInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<BannerInfo> iterator = iterable.iterator(); iterator.hasNext();) {
            BannerInfo bannerInfo = (BannerInfo) iterator.next();
            bannerInfo.setOprId(user.getUsername());
        }
        bannerService.save(iterable);
        DatatablesEditorResponse<BannerInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/banner') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<BannerInfo> modify(
            @Valid @RequestBody DatatablesEditorRequest<BannerInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, BannerInfo> MerchantMap = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, BannerInfo>> iterator = MerchantMap.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<String, BannerInfo> entry = iterator.next();
            entry.getValue().setOprId(user.getUsername());
        }
        Map<String, BannerInfo> mapData = bannerService.modify(MerchantMap);
        DatatablesEditorResponse<BannerInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/banner') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<BannerInfo> delete(
            @Valid @RequestBody DatatablesEditorRequest<BannerInfo> datatablesEditorRequest) {
        Iterable<BannerInfo> iterable = datatablesEditorRequest.getData().values();
        bannerService.delete(iterable);
        return new DatatablesEditorResponse<BannerInfo>();
    }

    @PostMapping(value = "/upload")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/banner') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public ResponseEntity<FileUploadOutput<FileUploadInfo>> uploadFile(@RequestParam("upload") MultipartFile uploadfile) {
        FileUploadOutput<FileUploadInfo> fileDataTablesOutput = new FileUploadOutput<FileUploadInfo>();
        ResponseEntity<FileUploadOutput<FileUploadInfo>> responseEntity;
        try {
            // 上传图片到本地
            String id = UUID.randomUUID().toString();
            String filename = uploadfile.getOriginalFilename();
            String[] fileNameArray = filename.split("\\.");
            String tranFileName = id + "." + fileNameArray[1];
            String directory = env.getProperty("upload.native.paths");
            File dir = new File(directory);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filepath = Paths.get(directory, tranFileName).toString();
            BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream(new File(filepath)));
            stream.write(uploadfile.getBytes());
            stream.close();

            Long filesize = uploadfile.getSize();
            // 上传图片到文件服务器
            String baseUrl = env.getProperty("upload.remote.url");
            String key = env.getProperty("upload.remote.banner.key");
            String bucket = env.getProperty("upload.remote.banner.bucket");
            String fileUrl = ImageUploadUtil.uploadImage(baseUrl, key, bucket, filepath);
            // 上传图片Map
            Map<String, Map<String, FileUploadInfo>> subFiles = new HashMap<String, Map<String, FileUploadInfo>>();
            Map<String, FileUploadInfo> subSubFiles = new HashMap<String, FileUploadInfo>();
            FileUploadInfo fileUploadInfo = new FileUploadInfo();
            fileUploadInfo.setId(fileUrl);
            fileUploadInfo.setFilename(filename);
            fileUploadInfo.setFilesize(filesize);
            fileUploadInfo.setWeb_path(fileUrl);
            fileUploadInfo.setSystem_path("");
            subSubFiles.put(fileUrl, fileUploadInfo);
            subFiles.put("files", subSubFiles);
            fileDataTablesOutput.setFiles(subFiles);
            // 上传图片IDMap
            Map<String, String> upload = new HashMap<String, String>();
            upload.put("id", fileUrl);
            fileDataTablesOutput.setUpload(upload);
            responseEntity = new ResponseEntity<FileUploadOutput<FileUploadInfo>>(fileDataTablesOutput,
                    HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return responseEntity;
    }
}
