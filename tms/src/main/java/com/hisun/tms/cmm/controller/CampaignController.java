package com.hisun.tms.cmm.controller;


import java.util.Iterator;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.CampaignInfo;
import com.hisun.tms.cmm.service.CampaignService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03 
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/campaignctrl")
public class CampaignController {

    @Autowired
    private CampaignService campaignService;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/campaign') or hasRole('ROLE_ADMIN')")
    public ModelAndView bannerList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/campaign/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/campaign') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<CampaignInfo> findAll(@Valid @RequestBody GenericParamInput input) {
        return campaignService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/campaign') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<CampaignInfo> add(@Valid @RequestBody DatatablesEditorRequest<CampaignInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<CampaignInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<CampaignInfo> iterator = iterable.iterator(); iterator.hasNext();) {
            CampaignInfo noticInfo = (CampaignInfo) iterator.next();
            noticInfo.setOprId(user.getUsername());
        }
        campaignService.save(iterable);
        DatatablesEditorResponse<CampaignInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/campaign') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<CampaignInfo> modify(@Valid @RequestBody DatatablesEditorRequest<CampaignInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, CampaignInfo> MerchantMap = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, CampaignInfo>> iterator = MerchantMap.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<String, CampaignInfo> entry = iterator.next();
            entry.getValue().setOprId(user.getUsername());
        }
        Map<String, CampaignInfo> mapData = campaignService.modify(MerchantMap);
        DatatablesEditorResponse<CampaignInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/busmgr/oprmgr/campaign') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<CampaignInfo> delete(@Valid @RequestBody DatatablesEditorRequest<CampaignInfo> datatablesEditorRequest) {
        Iterable<CampaignInfo> iterable = datatablesEditorRequest.getData().values();
        campaignService.delete(iterable);
        return new DatatablesEditorResponse<CampaignInfo>();
    }
}
