package com.hisun.tms.cmm.service;

import java.util.Map;

import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.cmm.model.CampaignInfo;
import com.hisun.tms.cmm.model.GenericParamInput;


/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:55:51 
 * @version V1.0
 */
public interface CampaignService {

    void save(Iterable<CampaignInfo> entities);

    Map<String, CampaignInfo> modify(Map<String, CampaignInfo> exampleMap);

    void delete(Iterable<CampaignInfo> entities);

    DataTablesOutput<CampaignInfo> findAll(GenericParamInput dataTablesInput);
}