package com.hisun.tms.cmm.controller;


import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.BranchInfo;
import com.hisun.tms.cmm.service.BranchService;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.common.exception.CustomException;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:34:03 
 * @version V1.0
 */
@Controller
@RequestMapping("/cmm/branchctrl")
public class BranchController {

    @Autowired
    private BranchService branchService;

    @GetMapping
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/branch') or hasRole('ROLE_ADMIN')")
    public ModelAndView BranchList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cmm/branch/index");
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/branch') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<BranchInfo> findAll(@Valid @RequestBody GenericParamInput input) {
        return branchService.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/branch:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<BranchInfo> add(@Valid @RequestBody DatatablesEditorRequest<BranchInfo> datatablesEditorRequest) throws CustomException {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Iterable<BranchInfo> iterable = datatablesEditorRequest.getData().values();
        for (Iterator<BranchInfo> iterator = iterable.iterator(); iterator.hasNext();) {
        	BranchInfo branchInfo = (BranchInfo) iterator.next();
        	branchInfo.setOperId(user.getUsername());
        }
        branchService.save(iterable);
        DatatablesEditorResponse<BranchInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/branch:modify') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<BranchInfo> modify(@Valid @RequestBody DatatablesEditorRequest<BranchInfo> datatablesEditorRequest) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Map<String, BranchInfo> MerchantMap = datatablesEditorRequest.getData();
        for (Iterator<Map.Entry<String, BranchInfo>> iterator = MerchantMap.entrySet().iterator(); iterator.hasNext();) {
            Map.Entry<String, BranchInfo> entry = iterator.next();
            entry.getValue().setOperId(user.getUsername());
        }
        Map<String, BranchInfo> map = datatablesEditorRequest.getData();
        Map<String, BranchInfo> mapData = branchService.modify(map);
        DatatablesEditorResponse<BranchInfo> datatablesEditorResponse = new DatatablesEditorResponse<>();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/cmmmgr/orgmgr/branch:delete') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public Map<String,String> delete(@Valid @RequestBody DatatablesEditorRequest<BranchInfo> datatablesEditorRequest) {
        Iterable<BranchInfo> iterable = datatablesEditorRequest.getData().values();
        return branchService.delete(iterable);
    }
    
    @GetMapping(value = "findAll")
    @ResponseBody
    public List<BranchInfo> findAllInf(@RequestParam("officeId") String officeId) {
        return branchService.findAllInf(officeId);
    }
    
    @GetMapping(value = "findByName")
    @ResponseBody
    public List<BranchInfo> findByName(@RequestParam("officeNm") String officeNm) {
        return branchService.findByName(officeNm);
    }
}
