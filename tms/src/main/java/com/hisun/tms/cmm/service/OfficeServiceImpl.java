package com.hisun.tms.cmm.service;


import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.hisun.tms.cmm.common.Constants;
import com.hisun.tms.cmm.model.AreaInfo;
import com.hisun.tms.cmm.model.BranchInfo;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.model.OfficeInfo;
import com.hisun.tms.cmm.repository.BranchInfoRepository;
import com.hisun.tms.cmm.repository.DatatablesOfficeInfoRepository;
import com.hisun.tms.cmm.repository.OfficeInfoRepository;
import com.hisun.tms.common.exception.CustomException;
import com.hisun.tms.common.util.JudgeUtils;
import com.hisun.tms.specifications.Specifications;
import com.hisun.tms.sys.model.User;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:56:37
 * @version V1.0
 */
@Service("officeService")
public class OfficeServiceImpl implements OfficeService {

    private static final Logger logger = LoggerFactory.getLogger(OfficeServiceImpl.class);

    @Autowired
    private DatatablesOfficeInfoRepository datatablesOfficeInfoRepository;

    @Autowired
    private OfficeInfoRepository officeInfoRepository;
    
    @Autowired
    private BranchInfoRepository branchInfoRepository;

    @Override
    public void save(Iterable<OfficeInfo> entities) throws CustomException{
    	//判断是否存在相同公司编号的公司
    	OfficeInfo officeInfo=officeInfoRepository.findByOfficeId(entities.iterator().next().getOfficeId());
    	if(JudgeUtils.isNotNull(officeInfo)){
    		throw new CustomException("公司编号已存在");
    	}else{
    		officeInfoRepository.save(entities);
    	}
    }

    @Override
    public Map<String, OfficeInfo> modify(Map<String, OfficeInfo> noticInfoMap) {
        List<OfficeInfo> OfficeInfos = noticInfoMap.values().stream().collect(Collectors.toList());
        logger.debug("recive from web: {}", OfficeInfos.toString());
        Collection<String> ids = Collections2.transform(OfficeInfos, new Function<OfficeInfo, String>() {
            @Override
            public String apply(final OfficeInfo OfficeInfo) {
                return String.valueOf(OfficeInfo.getId());
            }
        });
        logger.debug("query params: {}", ids.toString());
        List<OfficeInfo> OfficeInfoToUpdate = officeInfoRepository.findAll(ids);
        logger.debug("query from db: {}", OfficeInfoToUpdate.toString());
        for (int i = 0; i < OfficeInfos.size(); i++) {
            BeanUtils.copyProperties(OfficeInfos.get(i), OfficeInfoToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
        }
        logger.debug("after copy with audit column ignore: {}", OfficeInfoToUpdate.toString());
        List<OfficeInfo> list = officeInfoRepository.save(OfficeInfoToUpdate);
        logger.debug("after update: {}", list.toString());
        Map<Integer, OfficeInfo> map = list.stream()
                .collect(Collectors.toMap(OfficeInfo::getId, java.util.function.Function.identity()));
        Map<String, OfficeInfo> mapData = map.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
    }

    @Override
    public Map<String,String> delete(Iterable<OfficeInfo> entities) {
    	String officeId=entities.iterator().next().getOfficeId();
    	//判断是否有归属于这个公司的部门
    	List<BranchInfo> branchInfos=null;
    	if(JudgeUtils.isNotBlank(officeId)){
    		branchInfos=branchInfoRepository.findByOfficeId(officeId);
    	}
    	Map<String,String> map=new HashMap<String,String>();
    	if(JudgeUtils.isNotEmpty(branchInfos)){
    		map.put("PREBRA", "YES");
    	}else{
    		officeInfoRepository.delete(entities);
    		map.put("PREBRA", "NO");
    	}
    	return map;
    }

    @Override
    @Transactional("cmmTransactionManager")
    public DataTablesOutput<OfficeInfo> findAll(GenericParamInput dataTablesInput) {
        String officeId = dataTablesInput.getExtra_search().get("officeId");
        String officeNm = dataTablesInput.getExtra_search().get("officeNm");
        Specification<OfficeInfo> orderQueryParam=Specifications.<OfficeInfo>and()
                .eq(JudgeUtils.isNotBlank(officeId), "officeId", officeId)
                .eq(JudgeUtils.isNotBlank(officeNm), "officeNm", officeNm)
                .build();
        DataTablesOutput<OfficeInfo> dataTablesOutput = datatablesOfficeInfoRepository.findAll(dataTablesInput, orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

	@Override
	@Transactional("cmmTransactionManager")
	public List<OfficeInfo> findAllInf() {
		DataTablesInput dataTablesInput = new DataTablesInput();
        dataTablesInput.setDraw(50);
        dataTablesInput.setLength(50);
		DataTablesOutput<OfficeInfo> dataTablesOutput = datatablesOfficeInfoRepository.findAll(dataTablesInput);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput.getData();
	}
}
