package com.hisun.tms.cmm.service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.hisun.tms.cmm.common.Constants;
import com.hisun.tms.cmm.model.BannerInfo;
import com.hisun.tms.cmm.model.DataTablesOutputWithFile;
import com.hisun.tms.cmm.model.FileUploadInfo;
import com.hisun.tms.cmm.model.GenericParamInput;
import com.hisun.tms.cmm.repository.BannerInfoRepository;
import com.hisun.tms.cmm.repository.DatatablesBannerInfoRepository;
import com.hisun.tms.common.util.DateTimeUtils;
import com.hisun.tms.common.util.JudgeUtils;

/**
 * @Description
 * <AUTHOR>
 * @date 2017年8月23日 上午10:56:37
 * @version V1.0
 */
@Service("bannerService")
public class BannerServiceImpl implements BannerService {

    private static final Logger logger = LoggerFactory.getLogger(BannerServiceImpl.class);

    @Autowired
    private DatatablesBannerInfoRepository datatablesBannerInfoRepository;

    @Autowired
    private BannerInfoRepository bannerInfoRepository;

    @Override
    public void save(Iterable<BannerInfo> entities) {
        bannerInfoRepository.save(entities);
    }

    @Override
    public Map<String, BannerInfo> modify(Map<String, BannerInfo> bannerInfoMap) {
        List<BannerInfo> bannerInfos = bannerInfoMap.values().stream().collect(Collectors.toList());
        logger.debug("recive from web: {}", bannerInfos.toString());
        Collection<String> ids = Collections2.transform(bannerInfos, new Function<BannerInfo, String>() {
            @Override
            public String apply(final BannerInfo proInfo) {
                return proInfo.getId();
            }
        });
        logger.debug("query params: {}", ids.toString());
        List<BannerInfo> proInfoToUpdate = bannerInfoRepository.findAll(ids);
        logger.debug("query from db: {}", proInfoToUpdate.toString());
        for (int i = 0; i < bannerInfos.size(); i++) {
            BeanUtils.copyProperties(bannerInfos.get(i), proInfoToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
        }
        logger.debug("after copy with audit column ignore: {}", proInfoToUpdate.toString());
        List<BannerInfo> list = bannerInfoRepository.save(proInfoToUpdate);
        logger.debug("after update: {}", list.toString());
        Map<String, BannerInfo> map = list.stream()
                .collect(Collectors.toMap(BannerInfo::getId, java.util.function.Function.identity()));
        Map<String, BannerInfo> mapData = map.entrySet().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
    }

    @Override
    public void delete(Iterable<BannerInfo> entities) {
        bannerInfoRepository.delete(entities);
    }

    @Override
    @Transactional("cmmTransactionManager")
    public DataTablesOutputWithFile<BannerInfo> findAll(GenericParamInput dataTablesInput) {
        String title = dataTablesInput.getExtra_search().get("title");
        String beginDateStr = dataTablesInput.getExtra_search().get("beginDate");
        String endDateStr = dataTablesInput.getExtra_search().get("endDate");
        // 订单开始日期
        LocalDate beginDate = null;
        // 订单结束日期
        LocalDate endDate = null;
        if (JudgeUtils.isNotBlank(beginDateStr)) {
            beginDateStr = beginDateStr.replaceAll("-", "").trim();
            beginDate = DateTimeUtils.parseLocalDate(beginDateStr);
        } else {
            beginDate = LocalDate.now().minusDays(90);
        }
        if (JudgeUtils.isNotBlank(endDateStr)) {
            endDateStr = endDateStr.replaceAll("-", "").trim();
            endDate = DateTimeUtils.parseLocalDate(endDateStr);
        } else {
            endDate = LocalDate.now();
        }
        Specification<BannerInfo> orderQueryParam = findByCondition(title, beginDate, endDate);
        DataTablesOutput<BannerInfo> dataTablesOutput = datatablesBannerInfoRepository.findAll(dataTablesInput,
                orderQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        DataTablesOutputWithFile<BannerInfo> dataTablesOutputWithFile = new DataTablesOutputWithFile<>();
        dataTablesOutputWithFile.setData(dataTablesOutput.getData());
        dataTablesOutputWithFile.setDraw(dataTablesOutput.getDraw());
        dataTablesOutputWithFile.setError(dataTablesOutput.getError());
        dataTablesOutputWithFile.setRecordsTotal(dataTablesOutput.getRecordsTotal());
        dataTablesOutputWithFile.setRecordsFiltered(dataTablesOutput.getRecordsFiltered());
        Map<String, Map<String, FileUploadInfo>> subFiles = new HashMap<String, Map<String, FileUploadInfo>>();
        Map<String, FileUploadInfo> subSubFiles = new HashMap<String, FileUploadInfo>();
        List<BannerInfo> bannerInfos = dataTablesOutput.getData();
        Long fileSize = 0L;
        for (BannerInfo bannerInfo : bannerInfos) {
            FileUploadInfo bannerUrlKh = new FileUploadInfo();
            bannerUrlKh.setId(bannerInfo.getBannerUrlKh());
            bannerUrlKh.setFilename("bannerKh.jpg");
            bannerUrlKh.setFilesize(fileSize);
            bannerUrlKh.setWeb_path(bannerInfo.getBannerUrlKh());
            bannerUrlKh.setSystem_path("");
            subSubFiles.put(bannerInfo.getBannerUrlKh(), bannerUrlKh);
            FileUploadInfo bannerUrlCn = new FileUploadInfo();
            bannerUrlCn.setId(bannerInfo.getBannerUrlCn());
            bannerUrlCn.setFilename("bannerCn.jpg");
            bannerUrlCn.setFilesize(fileSize);
            bannerUrlCn.setWeb_path(bannerInfo.getBannerUrlCn());
            bannerUrlCn.setSystem_path("");
            subSubFiles.put(bannerInfo.getBannerUrlCn(), bannerUrlCn);
            FileUploadInfo bannerUrlEn = new FileUploadInfo();
            bannerUrlEn.setId(bannerInfo.getBannerUrlEn());
            bannerUrlEn.setFilename("bannerEn.jpg");
            bannerUrlEn.setFilesize(fileSize);
            bannerUrlEn.setWeb_path(bannerInfo.getBannerUrlEn());
            bannerUrlEn.setSystem_path("");
            subSubFiles.put(bannerInfo.getBannerUrlEn(), bannerUrlEn);
        }
        subFiles.put("files", subSubFiles);
        dataTablesOutputWithFile.setFiles(subFiles);
        return dataTablesOutputWithFile;
    }

    private Specification<BannerInfo> findByCondition(String title, LocalDate beginDate, LocalDate endDate) {
        Specification<BannerInfo> orderQueryParam = new Specification<BannerInfo>() {
            @Override
            public Predicate toPredicate(Root<BannerInfo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<>();
                if (JudgeUtils.isNotBlank(title)) {
                    predicates.add(cb.like(root.get("title"), "%" + title + "%"));
                }
                predicates.add(cb.between(root.get("effDate"), beginDate, endDate));
                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return orderQueryParam;
    }
}
