package com.hisun.tms.csh.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * Created by chen on 12/25 0025.
 */
@Entity
@Table(name = "csh_order")
public class CshOderDo {

    @Id
    @Column(name = "orderNo")
    private String orderNo ;

    @Column(name = "fee")
    private BigDecimal fee ;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }
}
