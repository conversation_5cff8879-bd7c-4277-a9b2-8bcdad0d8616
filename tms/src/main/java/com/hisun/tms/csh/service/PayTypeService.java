package com.hisun.tms.csh.service;

import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;

import com.hisun.tms.common.exception.CustomException;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

import com.hisun.tms.csh.model.CshQueryInfParamInput;
import com.hisun.tms.csh.model.PayType;

import java.util.List;
import java.util.Map;


/**
 * Created by tone on 2017/7/17.
 */
public interface PayTypeService {


    void save(Iterable<PayType> entities);

    Map<String, PayType> modify(Map<Integer, PayType> paytypeMap);

    void delete(Iterable<PayType> entities);

    DataTablesOutput<PayType> findAll(CshQueryInfParamInput dataTablesInput);

    public void checkExists(PayType payType) throws CustomException;

    PayType get(Integer id);

    public List<ConstantParamRspDTO> init(String type);

}