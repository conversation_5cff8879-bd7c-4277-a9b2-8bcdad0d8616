package com.hisun.tms.csh.controller;


import javax.validation.Valid;


import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.tms.common.datatables.DatatablesEditorRequest;
import com.hisun.tms.common.datatables.DatatablesEditorResponse;
import com.hisun.tms.common.exception.CustomException;
import com.hisun.tms.csh.common.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import com.hisun.tms.csh.model.CshQueryInfParamInput;
import com.hisun.tms.csh.model.PayType;
import com.hisun.tms.csh.service.PayTypeService;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Controller
@RequestMapping("/csh/paytype")
public class PayTypeController {

    private static final Logger logger = LoggerFactory.getLogger(PayTypeController.class);

    @Autowired
    private  PayTypeService service;

    @GetMapping
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/paytype') or hasRole('ROLE_ADMIN')")
    public ModelAndView MerchantList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName(Constants.VIEW_PAYTYPE_MNG);
        return modelAndView;
    }

    @PostMapping(value = "findAll")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/paytype') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<PayType> findAll(@Valid @RequestBody CshQueryInfParamInput input) {
        return service.findAll(input);
    }

    @PostMapping(value = "/add")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/paytype:add') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<PayType> add(@Valid @RequestBody DatatablesEditorRequest<PayType> datatablesEditorRequest) throws CustomException {
        Iterable<PayType> iterable = datatablesEditorRequest.getData().values();
        Iterator<PayType> itr=iterable.iterator();
        while (itr.hasNext()){
            service.checkExists(itr.next());
        }

        service.save(iterable);

        DatatablesEditorResponse<PayType> datatablesEditorResponse = new DatatablesEditorResponse<PayType>();
        datatablesEditorResponse.setData(datatablesEditorRequest.getData());
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/modify")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/paytype') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<PayType> modify(@Valid @RequestBody DatatablesEditorRequest<PayType> datatablesEditorRequest) {
        Map<String, PayType> payTypes = datatablesEditorRequest.getData();

        Map<Integer,PayType> ptMap=new HashMap<>();

        for (Map.Entry<String, PayType> entry : payTypes.entrySet()) {
            ptMap.put(Integer.valueOf(entry.getKey()), entry.getValue());
        }

        Map<String, PayType> mapData = service.modify(ptMap);

        DatatablesEditorResponse datatablesEditorResponse = new DatatablesEditorResponse();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    @PostMapping(value = "/delete")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/paytype') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<PayType> delete(@Valid @RequestBody DatatablesEditorRequest<PayType> datatablesEditorRequest) {
        Iterable<PayType> iterable = datatablesEditorRequest.getData().values();
        service.delete(iterable);

        return new DatatablesEditorResponse();
    }

    @PostMapping(value = "/status")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/paytype') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DatatablesEditorResponse<PayType> remove(@Valid @RequestBody  Map map) {
        Integer id=(int)map.get("id");
        PayType item=service.get(id);
        Map<Integer,PayType> ptMap=new HashMap<>();
        item.setStatus((String)map.get("status"));
        ptMap.put(id, item);
        Map<String, PayType> mapData = service.modify(ptMap);

        DatatablesEditorResponse datatablesEditorResponse = new DatatablesEditorResponse();
        datatablesEditorResponse.setData(mapData);
        return datatablesEditorResponse;
    }

    /**
     * 初始化payType
     * @return
     */
    @GetMapping(value = "/init")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/paytype') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public List<ConstantParamRspDTO> init(@RequestParam(value = "type") String type) {
        return service.init(type);
    }

}
