package com.hisun.tms.csh.model;


import javax.persistence.*;

import com.hisun.tms.common.audit.AbstractEntityTmpe;

/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Entity
@Table(name = "csh_paytype")
public class PayType extends AbstractEntityTmpe {
	/**
	 * id 主键
	 */
    @Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * @Fields mercId 商户ID
     */
    @Column(name = "mercId")
    private String mercId;
    /**
     * @Fields busType 业务类型
     */
    @Column(name = "busType")
    private String busType;

    /**
     * @Fields appCnl 支付应用
     */
    @Column(name = "appCnl")
    private String appCnl;

    /**
     * @Fields payTypes 账户支付类型
     */
    @Column(name = "payTypes")
    private String payTypes;
    /**
     * @Fields gwPayTypes 网关支付类型
     */
    @Column(name = "gwPayTypes")
    private String gwPayTypes;
    /**
     * @Fields status 状态：是否删除
     */
    @Column(name = "status")
    private String status;
    /**
     * @Fields modifyOpr 最后修改者
     */
    @Column(name = "modifyOpr")
    private String modifyOpr;


	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getMercId() {
		return mercId;
	}




	public void setMercId(String mercId) {
		this.mercId = mercId;
	}




	public String getBusType() {
		return busType;
	}




	public void setBusType(String busType) {
		this.busType = busType;
	}




	public String getAppCnl() {
		return appCnl;
	}




	public void setAppCnl(String appCnl) {
		this.appCnl = appCnl;
	}




	public String getPayTypes() {
		return payTypes;
	}




	public void setPayTypes(String payTypes) {
		this.payTypes = payTypes;
	}




	public String getGwPayTypes() {
		return gwPayTypes;
	}




	public void setGwPayTypes(String gwPayTypes) {
		this.gwPayTypes = gwPayTypes;
	}




	public String getStatus() {
		return status;
	}




	public void setStatus(String status) {
		this.status = status;
	}




	public String getModifyOpr() {
		return modifyOpr;
	}




	public void setModifyOpr(String modifyOpr) {
		this.modifyOpr = modifyOpr;
	}


	public void check(){

	}


	@Override
    public String toString() {
        return "PayType{" +
                "id='" + id + '\'' +
                ", mercId='" + mercId + '\'' +
                ", busType='" + busType + '\'' +
                ", appCnl='" + appCnl + '\'' +
                ", payTypes='" + payTypes + '\'' +
                ", gwPayTypes=" + gwPayTypes +
                ", status=" + status +'\'' +
                ", modifyOpr='" + modifyOpr +
                '}';
    }
}
