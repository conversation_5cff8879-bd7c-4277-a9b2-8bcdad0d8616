package com.hisun.tms.csh.dao;


import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.tms.csh.entity.OrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017年6月7日
 * @time 下午1:54:26
 *
 */
@Mapper
public interface IOrderDao extends BaseDao<OrderDO> {

    public OrderDO getOrderByOrderNoAndPayerId(@Param("orderNo")String orderNo, @Param("payerId") String payerId);

    public List<OrderDO> queryList(Map o);

    public List<OrderDO> queryNotFinalAuditList(Map o);
    public List<OrderDO> queryWaitReviewList(Map o);
    public List<OrderDO> queryCpiOrder(Map o);

    public OrderDO getByBusOrder(@Param("busOrderNo")String busOrderNo);

    public List<OrderDO> queryExpOrders(Map o);

    int queryNotFinalAuditListCount(Map<Object, Object> map);
    int queryListCount(Map<Object, Object> map);

    List<OrderDO> DCqueryWaitReviewList(Map<Object, Object> map);

    int DCqueryListCount(Map<Object, Object> map);

    List<OrderDO> DCqueryNotFinalAuditList(Map<Object, Object> map);

    int DCqueryNotFinalAuditListCount(Map<Object, Object> map);

    List<OrderDO> DXqueryWaitReviewList(Map<Object, Object> map);

    int DXqueryListCount(Map<Object, Object> map);

    List<OrderDO> DXqueryNotFinalAuditList(Map<Object, Object> map);

    int DXqueryNotFinalAuditListCount(Map<Object, Object> map);

}