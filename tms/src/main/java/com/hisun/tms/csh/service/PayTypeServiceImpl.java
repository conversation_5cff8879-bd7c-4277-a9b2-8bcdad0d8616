package com.hisun.tms.csh.service;


import com.google.common.base.Function;

import com.google.common.collect.Collections2;
import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.common.exception.CustomException;
import com.hisun.tms.csh.common.Constants;
import com.hisun.tms.csh.repository.PaytypeRepository;
import com.hisun.tms.specifications.Specifications;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;

import com.hisun.tms.csh.model.CshQueryInfParamInput;
import com.hisun.tms.csh.model.PayType;
import com.hisun.tms.csh.repository.DatatablesPayTypeRepository;


/**
 * Created by 吕海龙 on 2017/7/17.
 */
@Service("payTypeService")
public class PayTypeServiceImpl implements PayTypeService {

    private static final Logger logger = LoggerFactory.getLogger(PayTypeServiceImpl.class);

    @Autowired
    private DatatablesPayTypeRepository datatablesPayTypeRepository;

    @Autowired
    private PaytypeRepository paytypeRepository;

    @Autowired
    private ConstantParamClient constantParamClient;

    @Override
    public void save(Iterable<PayType> entities) {
        String userName=getOperatorName();
        Iterator<PayType> itr=entities.iterator();
        while (itr.hasNext()){
            itr.next().setModifyOpr(userName);
          //  itr.next().setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
           // itr.next().setLastmodifiedTime(DateTimeUtils.getCurrentLocalDateTime());
        }
        paytypeRepository.save(entities);
    }
//
    @Override
    public Map<String, PayType> modify(Map<Integer, PayType> patypeMap) {

        List<PayType> payTypes = patypeMap.values().stream().collect(Collectors.toList());

        logger.debug("recive from web: {}", payTypes.toString());

        Collection<Integer> ids = Collections2.transform(
                payTypes,
                new Function<PayType, Integer>() {
                    @Override
                    public Integer apply(final PayType paytype) {
                        return paytype.getId();
                    }
                }
        );
        logger.debug("query params: {}", ids.toString());
        List<PayType> paytypeToUpdate = paytypeRepository.findAll(ids);

        logger.debug("query from db: {}", paytypeToUpdate.toString());

        for (int i = 0; i < payTypes.size(); i++) {
            BeanUtils.copyProperties(payTypes.get(i), paytypeToUpdate.get(i), Constants.IGNORE_AUDIT_COLUMN);
        }

        logger.debug("after copy with audit column ignore: {}", paytypeToUpdate.toString());

        String userName=getOperatorName();
        Iterator<PayType> itr=paytypeToUpdate.iterator();
        while (itr.hasNext()){
            itr.next().setModifyOpr(userName);
          //  itr.next().setLastmodifiedTime(DateTimeUtils.getCurrentLocalDateTime());
        }

        List<PayType> list = paytypeRepository.save(paytypeToUpdate);

        logger.debug("after update: {}", list.toString());

        Map<Integer, PayType> map = list.stream().collect(Collectors.toMap(PayType::getId, java.util.function.Function.identity()));
        Map<String, PayType> mapData = map.entrySet().stream().collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> e.getValue()));
        return mapData;
    }

    @Override
    public void delete(Iterable<PayType> entities) {
        paytypeRepository.delete(entities);
    }

    @Override
    @Transactional("cshTransactionManager")
    public DataTablesOutput<PayType> findAll(CshQueryInfParamInput dataTablesInput) {
    	String mercId = dataTablesInput.getExtra_search().get("userId");
    	Specification<PayType> payTypeQueryParam=Specifications.<PayType>and()
    			.eq(JudgeUtils.isNotBlank(mercId), "mercId", mercId)
                .build();
        DataTablesOutput<PayType> dataTablesOutput = datatablesPayTypeRepository.findAll(dataTablesInput,payTypeQueryParam);
        logger.debug("datatable server-side return {}", dataTablesOutput);
        return dataTablesOutput;
    }

    public void checkExists(PayType payType) throws CustomException {
       List lst= paytypeRepository.findByAppCnlAndBusTypeAndMercId(payType.getAppCnl(), payType.getBusType(), payType.getMercId());
       if(lst!=null && !lst.isEmpty()){
           throw new CustomException("已经存在该配置，不要重复添加配置");
       }
    }

    private String getOperatorName(){
        org.springframework.security.core.userdetails.User securityUser = (org.springframework.security.core.userdetails.User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return securityUser.getUsername();
    }

    @Override
    public PayType get(Integer id) {
        return paytypeRepository.findOne(id);
    }

    @Override
    public List<ConstantParamRspDTO> init(String type) {
        GenericRspDTO genericRspDTO = constantParamClient.paramsGroup(type);
        List<ConstantParamRspDTO> constantParamRspDTOList = (List<ConstantParamRspDTO>) genericRspDTO.getBody();
        List<ConstantParamRspDTO> list = new ArrayList<>();
        for(int i =1;i<=constantParamRspDTOList.size();i++){
            for(ConstantParamRspDTO constantParamRspDTO : constantParamRspDTOList) {
                if (JudgeUtils.equals(constantParamRspDTO.getRmk(),String.valueOf(i))) {
                    list.add(constantParamRspDTO);
                }
            }
        }
        return list;
    }

}
