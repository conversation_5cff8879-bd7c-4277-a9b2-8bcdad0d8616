package com.hisun.tms.tam.model.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 获取兑换交易列表
 *
 */
@ClientValidated
@ApiModel("获取兑换交易列表")
public class QueryExchangeAuditDTO {

	@ApiModelProperty(name = "pageNum", value = "当前页")
	private Integer pageNum;
	
	@ApiModelProperty(name = "pageSize", value = "每页大小")
	private Integer pageSize;
	
	@ApiModelProperty(name = "userId", value = "用户ID")
	private String userId;
	
	@ApiModelProperty(name = "orderNo", value = "订单号")
	private String orderNo;
	
	@ApiModelProperty(name = "status", value = "订单状态：PENDING（待审核）/ FIRST_AUDIT（初审中）/ SECOND_AUDIT（复核中）/ APPROVED（审核通过）/ REJECTED（拒绝）/ SUCCESS（成功）/ FAILED（失败）")
	private String status;
	
	@ApiModelProperty(name = "direction", value = "兑换方向：S2F（数兑法）/ F2S（法兑数）/ F2F (法兑法）")
	private String direction;

	@ApiModelProperty(name = "auditFlag", value = "复核标志：0-未初审，1-已初审")
	private String auditFlag;

	public String getAuditFlag() {
		return auditFlag;
	}

	public void setAuditFlag(String auditFlag) {
		this.auditFlag = auditFlag;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getDirection() {
		return direction;
	}

	public void setDirection(String direction) {
		this.direction = direction;
	}
}
