package com.hisun.tms.tam.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 审核兑换交易
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15 14:09
 */
@ApiModel(" 审核兑换交易")
public class ExchangeAuditReq {

    @ApiModelProperty(name = "id", value = "订单ID")
    private String id;

    @ApiModelProperty(name = "auditTimes", value = "审核次数(0:初审,1:复核)")
    private Integer auditTimes;

    @ApiModelProperty(name = "auditResult", value = "审核结果(0:审核通过-APPROVED,1:审核拒绝-REJECTED)")
    private Integer auditResult;

    @ApiModelProperty(name = "auditOpinion", value = "审核意见")
    private String auditOpinion;

    @ApiModelProperty(name = "rejectReason", value = "拒绝原因")
    private String rejectReason;


    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public Integer getAuditResult() {
        return auditResult;
    }
    public void setAuditResult(Integer auditResult) {
        this.auditResult = auditResult;
    }
    public String getAuditOpinion() {
        return auditOpinion;
    }
    public void setAuditOpinion(String auditOpinion) {
        this.auditOpinion = auditOpinion;
    }
    public String getRejectReason() {
        return rejectReason;
    }
    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public Integer getAuditTimes() {
        return auditTimes;
    }
    public void setAuditTimes(Integer auditTimes) {
        this.auditTimes = auditTimes;
    }

}
