package com.hisun.tms.tam.service.Impl;

import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.tam.client.ExchangeOrderClient;
import com.hisun.lemon.tam.dto.UserExchangeOrderDTO;
import com.hisun.tms.tam.constants.AuditStatus;
import com.hisun.tms.tam.constants.TAMMessageCode;
import com.hisun.tms.tam.dao.IExchangeAuditDao;
import com.hisun.tms.tam.model.dto.ExchangeAuditDetailDTO;
import com.hisun.tms.tam.model.dto.ExchangeAuditReq;
import com.hisun.tms.tam.model.dto.QueryExchangeAuditDTO;
import com.hisun.tms.tam.model.dto.QueryResultExchangeAuditDTO;
import com.hisun.tms.tam.model.entity.ExchangeAuditDO;
import com.hisun.tms.tam.service.IExchangeAuditService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service("exchangeAuditService")
public class ExchangeAuditServiceImpl extends BaseService implements IExchangeAuditService {
	private static final Logger logger = LoggerFactory.getLogger(ExchangeAuditServiceImpl.class);

	@Autowired
	IExchangeAuditDao exchangeAuditDao;

	@Resource
	ExchangeOrderClient exchangeOrderClient;

	@Resource
	AccountingTreatmentClient accountingTreatmentClient;

	@Override
	public List<QueryResultExchangeAuditDTO.queryExchangeAudit> queryExchangeAudit(QueryExchangeAuditDTO queryDTO) {
		ExchangeAuditDO exchangeAuditDO = new ExchangeAuditDO();
		Integer pageSize = queryDTO.getPageSize();
		Integer pageNum = (queryDTO.getPageNum()-1)*pageSize;
		exchangeAuditDO.setPageNum(pageNum);
		exchangeAuditDO.setPageSize(pageSize);
		exchangeAuditDO.setUserId(queryDTO.getUserId());
		exchangeAuditDO.setOrderNo(queryDTO.getOrderNo());
		exchangeAuditDO.setStatus(queryDTO.getStatus());
		exchangeAuditDO.setDirection(queryDTO.getDirection());
		if (JudgeUtils.isNull(pageNum)) {
			exchangeAuditDO.setPageNum(0);
		}
		if (JudgeUtils.isNull(pageSize)) {
			exchangeAuditDO.setPageSize(10);
		}
		// 修改这里，直接调用dao层的find方法，不再使用PageUtils
		List<ExchangeAuditDO> auditDOs = null;
		if("0".equals(queryDTO.getAuditFlag())){
			auditDOs = this.exchangeAuditDao.find(exchangeAuditDO);
		}else{
			auditDOs = this.exchangeAuditDao.findSecondAudit(exchangeAuditDO);
		}

		// 将auditDOs复制到queryList
		List<QueryResultExchangeAuditDTO.queryExchangeAudit> queryList = new ArrayList<>();

		if (auditDOs != null && !auditDOs.isEmpty()) {
			for (ExchangeAuditDO auditDO : auditDOs) {
				QueryResultExchangeAuditDTO.queryExchangeAudit queryItem = new QueryResultExchangeAuditDTO.queryExchangeAudit();
				BeanUtils.copyProperties(auditDO, queryItem);
				queryList.add(queryItem);
			}
		}

		return queryList;
	}

	/**
	 * 查询兑换交易详情
	 *
	 * @param id
	 * @return
	 */
	@Override
	public ExchangeAuditDetailDTO detail(String id) {
		ExchangeAuditDO exchangeAuditDO = this.exchangeAuditDao.get(id);
		ExchangeAuditDetailDTO exchangeAuditDetailDTO = new ExchangeAuditDetailDTO();
		BeanUtils.copyProperties(exchangeAuditDO, exchangeAuditDetailDTO);
		return exchangeAuditDetailDTO;
	}

	/**
	 * 审核兑换交易
	 *
	 * @param req
	 */
	@Override
	public void exchangeAudit(ExchangeAuditReq req) {
		// 根据id查询兑换交易
		String id = req.getId();
		ExchangeAuditDO exchangeAuditDO = this.exchangeAuditDao.get(id);
		if (exchangeAuditDO == null) {
			logger.error("兑换交易不存在,ID:{}", id);
			LemonException.throwBusinessException(TAMMessageCode.ORDER_NOT_EXIST);
		}
		if (!exchangeAuditDO.getStatus().equals(AuditStatus.PENDING)
				&& !exchangeAuditDO.getStatus().equals(AuditStatus.FIRST_AUDIT)
				&& !exchangeAuditDO.getStatus().equals(AuditStatus.SECOND_AUDIT)) {
			logger.error("兑换交易已审核,ID:{}", id);
			LemonException.throwBusinessException(TAMMessageCode.ORDER_HAS_AUDIT);
		}
		// 获取当前用户信息
		User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		String userId = String.valueOf(user.getUsername());


		// todo 判断当前用户是否是审核人？

		// 初审（待审核/初审中）
		if (req.getAuditTimes().equals(0) && (exchangeAuditDO.getStatus().equals(AuditStatus.PENDING)
				|| exchangeAuditDO.getStatus().equals(AuditStatus.FIRST_AUDIT))) {
			// 审核拒绝
			if (req.getAuditResult() == 1) {
				exchangeAuditDO.setStatus(AuditStatus.REJECTED);
				exchangeAuditDO.setFirstAuditOpinion(req.getAuditOpinion());
				exchangeAuditDO.setRejectReason(req.getRejectReason());
				exchangeAuditDO.setFirstAuditTime(LocalDateTime.now());
				exchangeAuditDO.setFirstAuditResult(AuditStatus.REJECTED);
				exchangeAuditDO.setFirstAuditUser(userId);
			} else {
				// 审核通过
				exchangeAuditDO.setStatus(AuditStatus.SECOND_AUDIT);
				exchangeAuditDO.setFirstAuditOpinion(req.getAuditOpinion());
				exchangeAuditDO.setFirstAuditTime(LocalDateTime.now());
				exchangeAuditDO.setFirstAuditResult(AuditStatus.APPROVED);
				exchangeAuditDO.setFirstAuditUser(userId);
				exchangeAuditDO.setExecuteTime(LocalDateTime.now());
			}

		} else if (exchangeAuditDO.getStatus().equals(AuditStatus.SECOND_AUDIT) && req.getAuditTimes().equals(1)
				&& exchangeAuditDO.getFirstAuditResult().equals(AuditStatus.APPROVED)) {
			// 复核（复核中）
			if (req.getAuditResult() == 1) {
				exchangeAuditDO.setStatus(AuditStatus.REJECTED);
				exchangeAuditDO.setSecondAuditOpinion(req.getAuditOpinion());
				exchangeAuditDO.setRejectReason(req.getRejectReason());
				exchangeAuditDO.setSecondAuditTime(LocalDateTime.now());
				exchangeAuditDO.setSecondAuditResult(AuditStatus.REJECTED);
				exchangeAuditDO.setSecondAuditUser(userId);
			} else {
				// 审核通过
				exchangeAuditDO.setStatus(AuditStatus.APPROVED);
				exchangeAuditDO.setSecondAuditOpinion(req.getAuditOpinion());
				exchangeAuditDO.setSecondAuditTime(LocalDateTime.now());
				exchangeAuditDO.setSecondAuditResult(AuditStatus.APPROVED);
				exchangeAuditDO.setSecondAuditUser(userId);
				exchangeAuditDO.setExecuteTime(LocalDateTime.now());
			}

		} else {
			logger.error("兑换交易已审核,ID:{}", id);
			LemonException.throwBusinessException(TAMMessageCode.ORDER_HAS_AUDIT);
		}

		// 更新兑换交易信息
		exchangeAuditDO.setUpdateTime(LocalDateTime.now());
		exchangeAuditDao.update(exchangeAuditDO);

		if(JudgeUtils.equals(exchangeAuditDO.getStatus(), AuditStatus.APPROVED)
				|| JudgeUtils.equals(exchangeAuditDO.getStatus(), AuditStatus.REJECTED)) {
			//调用tam完成兑换订单
			finishExchangeOrder(exchangeAuditDO.getOrderNo());
		}
	}

	private void finishExchangeOrder(String orderNo) {
		UserExchangeOrderDTO orderDTO = new UserExchangeOrderDTO();
		orderDTO.setOrderNo(orderNo);
		GenericDTO<UserExchangeOrderDTO> orderReq = GenericDTO.newInstance(orderDTO);
		exchangeOrderClient.executeExchangeOrder(orderReq);
	}

	/**
	 * 获取兑换交易总条数
	 *
	 * @param queryDTO
	 * @return
	 */
	@Override
	public int getExchangeAuditCount(QueryExchangeAuditDTO queryDTO) {
		ExchangeAuditDO exchangeAuditDO = new ExchangeAuditDO();
		exchangeAuditDO.setUserId(queryDTO.getUserId());
		exchangeAuditDO.setOrderNo(queryDTO.getOrderNo());
		exchangeAuditDO.setStatus(queryDTO.getStatus());
		exchangeAuditDO.setDirection(queryDTO.getDirection());
		
		// 根据auditFlag区分审核和复核
		if("0".equals(queryDTO.getAuditFlag())){
			return this.exchangeAuditDao.count(exchangeAuditDO);
		}else{
			return this.exchangeAuditDao.countSecondAudit(exchangeAuditDO);
		}
	}

}
