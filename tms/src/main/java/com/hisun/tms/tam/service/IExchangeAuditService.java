package com.hisun.tms.tam.service;


import com.hisun.tms.tam.model.dto.ExchangeAuditDetailDTO;
import com.hisun.tms.tam.model.dto.ExchangeAuditReq;
import com.hisun.tms.tam.model.dto.QueryExchangeAuditDTO;
import com.hisun.tms.tam.model.dto.QueryResultExchangeAuditDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月7日
 * @time 下午2:13:58
 *
 */
public interface IExchangeAuditService {

    /**
     * 获取兑换交易列表
     * @param queryDTO
     * @return
     */
    List<QueryResultExchangeAuditDTO.queryExchangeAudit> queryExchangeAudit(QueryExchangeAuditDTO queryDTO);

    /**
     * 查询兑换交易详情
     * @param id
     * @return
     */
    ExchangeAuditDetailDTO detail(String id);

    /**
     * 审核兑换交易
     * @param
     */
    void exchangeAudit(ExchangeAuditReq req);

    /**
     * 获取兑换交易总条数
     * @param queryDTO
     * @return
     */
    int getExchangeAuditCount(QueryExchangeAuditDTO queryDTO);
}