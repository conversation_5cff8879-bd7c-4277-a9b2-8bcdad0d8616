/*
 * @ClassName transferOrderDDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:44:54
 */
package com.hisun.tms.tam.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.tms.tam.model.entity.ExchangeAuditDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IExchangeAuditDao extends BaseDao<ExchangeAuditDO> {

    public List<ExchangeAuditDO> queryList(Map map);

    public List<ExchangeAuditDO> queryListByType(Map map);

    public List<ExchangeAuditDO> queryListByExtOrderNo(@Param("extOrderNo") String extOrderNo);

    List<ExchangeAuditDO> findSecondAudit(ExchangeAuditDO exchangeAuditDO);

    /**
     * 获取兑换审核记录总数
     * @param exchangeAuditDO
     * @return
     */
    int count(ExchangeAuditDO exchangeAuditDO);

    /**
     * 获取兑换复核记录总数
     * @param exchangeAuditDO
     * @return
     */
    int countSecondAudit(ExchangeAuditDO exchangeAuditDO);
}