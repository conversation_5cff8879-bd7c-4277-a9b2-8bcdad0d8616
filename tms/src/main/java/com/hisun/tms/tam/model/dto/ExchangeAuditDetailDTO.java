package com.hisun.tms.tam.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 获取兑换交易列表
 *
 * <AUTHOR>
 * @date 2017年7月13日
 * @time 下午20:21:30
 */
@ApiModel("获取兑换交易列表")
public class ExchangeAuditDetailDTO {

    @ApiModelProperty(name = "id", value = "订单ID")
    private String id;

    @ApiModelProperty(name = "orderNo", value = "订单号（唯一）")
    private String orderNo;

    @ApiModelProperty(name = "userId", value = "用户ID")
    private String userId;

    @ApiModelProperty(name = "direction", value = "兑换方向：S2F（数兑法）/ F2S（法兑数）/ F2F (法兑法）")
    private String direction;

    @ApiModelProperty(name = "fromCoin", value = "转出币种：USDT/HKD")
    private String fromCoin;

    @ApiModelProperty(name = "fromAmount", value = "转出金额")
    private BigDecimal fromAmount;

    @ApiModelProperty(name = "toCoin", value = "转入币种：HKD/USDT")
    private String toCoin;

    @ApiModelProperty(name = "toAmount", value = "转入金额（含手续费后）")
    private BigDecimal toAmount;

    @ApiModelProperty(name = "feeCoin", value = "手续费币种")
    private String feeCoin;

    @ApiModelProperty(name = "feeAmount", value = "手续费金额")
    private BigDecimal feeAmount;

    @ApiModelProperty(name = "exchangeRate", value = "兑换汇率（from_coin/to_coin）")
    private BigDecimal exchangeRate;

    @ApiModelProperty(name = "fromUsdRate", value = "转出币种对USD的汇率（如USDT/USD=1.0000）")
    private BigDecimal fromUsdRate;

    @ApiModelProperty(name = "toUsdRate", value = "转入币种对USD的汇率（如HKD/USD=0.1282）")
    private BigDecimal toUsdRate;

    @ApiModelProperty(name = "fromAmountUsd", value = "转出金额等值USD（from_amount × from_coin_usd_rate）")
    private BigDecimal fromAmountUsd;

    @ApiModelProperty(name = "toAmountUsd", value = "转入金额等值USD（to_amount × to_coin_usd_rate）")
    private BigDecimal toAmountUsd;

    @ApiModelProperty(name = "status", value = "订单状态：PENDING（待审核）/ FIRST_AUDIT（初审中）/ SECOND_AUDIT（复核中）/ APPROVED（审核通过）/ REJECTED（拒绝）/ SUCCESS（成功）/ FAILED（失败）")
    private String status;

    @ApiModelProperty(name = "firstAuditUser", value = "初审人")
    private String firstAuditUser;

    @ApiModelProperty(name = "firstAuditTime", value = "初审时间")
    private LocalDateTime firstAuditTime;

    @ApiModelProperty(name = "firstAuditResult", value = "初审结果：APPROVED/REJECTED")
    private String firstAuditResult;

    @ApiModelProperty(name = "firstAuditOpinion", value = "初审意见")
    private String firstAuditOpinion;

    @ApiModelProperty(name = "secondAuditUser", value = "复核人")
    private String secondAuditUser;

    @ApiModelProperty(name = "secondAuditTime", value = "复核时间")
    private LocalDateTime secondAuditTime;

    @ApiModelProperty(name = "secondAuditResult", value = "复核结果：APPROVED/REJECTED")
    private String secondAuditResult;

    @ApiModelProperty(name = "secondAuditOpinion", value = "复核意见")
    private String secondAuditOpinion;

    @ApiModelProperty(name = "executeTime", value = "执行时间（成功时）")
    private LocalDateTime executeTime;

    @ApiModelProperty(name = "rejectReason", value = "拒绝原因（最终拒绝原因）")
    private String rejectReason;

    @ApiModelProperty(name = "createTime", value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private LocalDateTime updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getFromCoin() {
        return fromCoin;
    }

    public void setFromCoin(String fromCoin) {
        this.fromCoin = fromCoin;
    }

    public BigDecimal getFromAmount() {
        return fromAmount;
    }

    public void setFromAmount(BigDecimal fromAmount) {
        this.fromAmount = fromAmount;
    }

    public String getToCoin() {
        return toCoin;
    }

    public void setToCoin(String toCoin) {
        this.toCoin = toCoin;
    }

    public BigDecimal getToAmount() {
        return toAmount;
    }

    public void setToAmount(BigDecimal toAmount) {
        this.toAmount = toAmount;
    }

    public String getFeeCoin() {
        return feeCoin;
    }

    public void setFeeCoin(String feeCoin) {
        this.feeCoin = feeCoin;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFirstAuditUser() {
        return firstAuditUser;
    }

    public void setFirstAuditUser(String firstAuditUser) {
        this.firstAuditUser = firstAuditUser;
    }

    public LocalDateTime getFirstAuditTime() {
        return firstAuditTime;
    }

    public void setFirstAuditTime(LocalDateTime firstAuditTime) {
        this.firstAuditTime = firstAuditTime;
    }

    public String getFirstAuditResult() {
        return firstAuditResult;
    }

    public void setFirstAuditResult(String firstAuditResult) {
        this.firstAuditResult = firstAuditResult;
    }

    public String getFirstAuditOpinion() {
        return firstAuditOpinion;
    }

    public void setFirstAuditOpinion(String firstAuditOpinion) {
        this.firstAuditOpinion = firstAuditOpinion;
    }

    public String getSecondAuditUser() {
        return secondAuditUser;
    }

    public void setSecondAuditUser(String secondAuditUser) {
        this.secondAuditUser = secondAuditUser;
    }

    public LocalDateTime getSecondAuditTime() {
        return secondAuditTime;
    }

    public void setSecondAuditTime(LocalDateTime secondAuditTime) {
        this.secondAuditTime = secondAuditTime;
    }

    public String getSecondAuditResult() {
        return secondAuditResult;
    }

    public void setSecondAuditResult(String secondAuditResult) {
        this.secondAuditResult = secondAuditResult;
    }

    public String getSecondAuditOpinion() {
        return secondAuditOpinion;
    }

    public void setSecondAuditOpinion(String secondAuditOpinion) {
        this.secondAuditOpinion = secondAuditOpinion;
    }

    public LocalDateTime getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(LocalDateTime executeTime) {
        this.executeTime = executeTime;
    }

    public String getExecuteUser() {
        return rejectReason;
    }

    public void setExecuteUser(String executeUser) {
        this.rejectReason = executeUser;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getFromUsdRate() {
        return fromUsdRate;
    }

    public void setFromUsdRate(BigDecimal fromUsdRate) {
        this.fromUsdRate = fromUsdRate;
    }

    public BigDecimal getToUsdRate() {
        return toUsdRate;
    }

    public void setToUsdRate(BigDecimal toUsdRate) {
        this.toUsdRate = toUsdRate;
    }

    public BigDecimal getFromAmountUsd() {
        return fromAmountUsd;
    }

    public void setFromAmountUsd(BigDecimal fromAmountUsd) {
        this.fromAmountUsd = fromAmountUsd;
    }

    public BigDecimal getToAmountUsd() {
        return toAmountUsd;
    }

    public void setToAmountUsd(BigDecimal toAmountUsd) {
        this.toAmountUsd = toAmountUsd;
    }
}
