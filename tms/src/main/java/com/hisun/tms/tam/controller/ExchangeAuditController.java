package com.hisun.tms.tam.controller;

import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.tms.tam.model.dto.*;
import com.hisun.tms.tam.service.IExchangeAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "处理兑换审核")
@Controller
@RequestMapping(value = "/tam/exchange")
@PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeaudit') or hasRole('ROLE_ADMIN')")
public class ExchangeAuditController extends BaseController {
	@Resource(name = "exchangeAuditService")
	IExchangeAuditService exchangeService;

	/**
	 * 获取兑换交易列表
	 */
	@ResponseBody
	@PostMapping(value = "/audit/list")
	@PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeaudit') or hasRole('ROLE_ADMIN')")
	@ApiResponse(code = 200, message = "获取兑换交易列表")
	@ApiOperation(value = "获取兑换交易列表", notes = "获取兑换交易列表")
	public GenericRspDTO<QueryResultExchangeAuditDTO> queryExchangeAudit(
			@RequestBody GenericDTO<QueryExchangeAuditDTO> req) {
		QueryExchangeAuditDTO queryDTO = req.getBody();
		// 设置auditFlag为0，表示查询未初审的数据
		queryDTO.setAuditFlag("0");
		List<QueryResultExchangeAuditDTO.queryExchangeAudit> queryList = this.exchangeService
				.queryExchangeAudit(queryDTO);
		QueryResultExchangeAuditDTO query = new QueryResultExchangeAuditDTO();
		query.setQueryExchangeAuditList(queryList);
		GenericRspDTO<QueryResultExchangeAuditDTO> dto = new GenericRspDTO<>();
		dto.setBody(query);
		return dto;
	}

	/**
	 * 查询兑换交易详情
	 */
	@ResponseBody
	@PostMapping(value = "/audit/detail")
	@PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeaudit') or hasRole('ROLE_ADMIN')")
	@ApiResponse(code = 200, message = "查询兑换交易详情")
	@ApiOperation(value = "查询兑换交易详情", notes = "查询兑换交易详情")
	public GenericRspDTO<ExchangeAuditDetailDTO> queryExchangeAuditDetail(
			@RequestBody GenericDTO<ExchangeAuditDetailReq> req) {

		String id = req.getBody().getId();
		ExchangeAuditDetailDTO exchangeAuditDetailDTO = exchangeService.detail(id);
		GenericRspDTO<ExchangeAuditDetailDTO> dto = new GenericRspDTO<>();
		dto.setBody(exchangeAuditDetailDTO);
		return dto;
	}

	/**
	 * 审核兑换交易
	 */
	@ResponseBody
	@PostMapping(value = "/audit")
	@PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeaudit') or hasRole('ROLE_ADMIN')")
	@ApiResponse(code = 200, message = "审核兑换交易")
	@ApiOperation(value = "审核兑换交易", notes = "审核兑换交易")
	public GenericRspDTO<NoBody> auditExchange(@RequestBody GenericDTO<ExchangeAuditReq> req) {
		ExchangeAuditReq exchangeAuditReq = req.getBody();
		exchangeService.exchangeAudit(exchangeAuditReq);
		return new GenericRspDTO<>();
	}

	/**
	 * 获取兑换审核交易总条数
	 */
	@ResponseBody
	@PostMapping(value = "/audit/count")
	@PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeaudit') or hasRole('ROLE_ADMIN')")
	@ApiResponse(code = 200, message = "获取兑换审核交易总条数")
	@ApiOperation(value = "获取兑换审核交易总条数", notes = "获取兑换审核交易总条数")
	public GenericRspDTO<Integer> getExchangeAuditCount(
			@RequestBody GenericDTO<QueryExchangeAuditDTO> req) {
		QueryExchangeAuditDTO queryDTO = req.getBody();
		// 设置auditFlag为0，表示查询未初审的数据
		queryDTO.setAuditFlag("0");
		int count = this.exchangeService.getExchangeAuditCount(queryDTO);
		GenericRspDTO<Integer> dto = new GenericRspDTO<>();
		dto.setBody(count);
		return dto;
	}

	/**
	 * 返回兑换审核管理页面
	 */
	@RequestMapping(value = "/audit/page")
	@PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeaudit') or hasRole('ROLE_ADMIN')")
	public ModelAndView auditPage() {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("tam/exchange/audit/index");
		return modelAndView;
	}

}
