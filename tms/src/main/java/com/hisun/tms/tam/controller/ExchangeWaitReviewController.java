package com.hisun.tms.tam.controller;

import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.tms.tam.model.dto.*;
import com.hisun.tms.tam.service.IExchangeAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.List;

/**
 * 兑换复核查询Controller
 */
@Api(value = "处理兑换复核查询")
@Controller
@RequestMapping(value = "/tam/exchange/waitReview")
@PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeWaitReview') or hasRole('ROLE_ADMIN')")
public class ExchangeWaitReviewController extends BaseController {
    @Resource(name = "exchangeAuditService")
    IExchangeAuditService exchangeService;

    /**
     * 获取兑换复核交易列表
     */
    @ResponseBody
    @PostMapping(value = "/list")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeWaitReview') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "获取兑换复核交易列表")
    @ApiOperation(value = "获取兑换复核交易列表", notes = "获取兑换复核交易列表")
    public GenericRspDTO<QueryResultExchangeAuditDTO> queryExchangeAudit(
            @RequestBody GenericDTO<QueryExchangeAuditDTO> req) {
        QueryExchangeAuditDTO queryDTO = req.getBody();
        // 设置auditFlag为1，表示查询已初审的数据
        queryDTO.setAuditFlag("1");
        List<QueryResultExchangeAuditDTO.queryExchangeAudit> queryList = this.exchangeService
                .queryExchangeAudit(queryDTO);
        QueryResultExchangeAuditDTO query = new QueryResultExchangeAuditDTO();
        query.setQueryExchangeAuditList(queryList);
        GenericRspDTO<QueryResultExchangeAuditDTO> dto = new GenericRspDTO<>();
        dto.setBody(query);
        return dto;
    }

    /**
     * 获取兑换复核交易总条数
     */
    @ResponseBody
    @PostMapping(value = "/count")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeWaitReview') or hasRole('ROLE_ADMIN')")
    @ApiResponse(code = 200, message = "获取兑换复核交易总条数")
    @ApiOperation(value = "获取兑换复核交易总条数", notes = "获取兑换复核交易总条数")
    public GenericRspDTO<Integer> getExchangeAuditCount(
            @RequestBody GenericDTO<QueryExchangeAuditDTO> req) {
        QueryExchangeAuditDTO queryDTO = req.getBody();
        // 设置auditFlag为1，表示查询已初审的数据
        queryDTO.setAuditFlag("1");
        int count = this.exchangeService.getExchangeAuditCount(queryDTO);
        GenericRspDTO<Integer> dto = new GenericRspDTO<>();
        dto.setBody(count);
        return dto;
    }

    /**
     * 返回兑换复核管理页面
     */
    @RequestMapping(value = "/page")
    @PreAuthorize("hasPermission('','/busmgr/trdmgr/exchangeWaitReview') or hasRole('ROLE_ADMIN')")
    public ModelAndView auditPage() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("tam/exchange/waitReview/index");
        return modelAndView;
    }
}