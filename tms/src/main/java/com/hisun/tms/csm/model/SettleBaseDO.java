/*
 * @ClassName SettleBaseDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 15:59:57
 */
package com.hisun.tms.csm.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "csm_settle_base")
public class SettleBaseDO  {
    /**
     * @Fields userId 用户号
     */
    @Id
    @Column(name = "user_id")
    private String userId;
    /**
     * @Fields userName 用户名称
     */
    @Column(name = "user_name")
    private String userName;
    /**
     * @Fields mblNo 手机号码
     */
    @Column(name = "mbl_no")
    private String mblNo;
    /**
     * @Fields settleType 结算方式 auto:自动 self:自主
     */
    @Column(name = "settle_type")
    private String settleType;
    /**
     * @Fields 结算周期类型 日结算:daily 周结算:weekly 月结算:monthly
     */
    @Column(name = "settle_cycle_type")
    private String settleCycleType;
    /**
     * @Fields lastLastSettleDay 上上结算日
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "last_last_settle_day")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastLastSettleDay;
    /**
     * @Fields lastSettleDay 上一结算日
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "last_settle_day")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastSettleDay;
    /**
     * @Fields nextSettleDay 下一结算日
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "next_settle_day")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate nextSettleDay;
    /**
     * @Fields stats 状态0:无效 1:生效
     */
    @Column(name = "stats")
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "eff_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;

    //最后修改时间
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;
    //创建时间
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;


    //结算地址
    @Column(name = "settle_sites")
    private String settleSite;

    //营业网点
    @Column(name = "hall_sites")
    private String hallSites;

    //划款天数
    @Column(name = "agreement_pay_days")
    private String drawDays;

    public String getSettleSite() {
        return settleSite;
    }

    public void setSettleSite(String settleSite) {
        this.settleSite = settleSite;
    }

    public String getHallSites() {
        return hallSites;
    }

    public void setHallSites(String hallSites) {
        this.hallSites = hallSites;
    }

    public String getDrawDays() {
        return drawDays;
    }

    public void setDrawDays(String drawDays) {
        this.drawDays = drawDays;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }



    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getSettleType() {
        return settleType;
    }

    public void setSettleType(String settleType) {
        this.settleType = settleType;
    }

    public LocalDate getLastLastSettleDay() {
        return lastLastSettleDay;
    }

    public void setLastLastSettleDay(LocalDate lastLastSettleDay) {
        this.lastLastSettleDay = lastLastSettleDay;
    }

    public LocalDate getLastSettleDay() {
        return lastSettleDay;
    }

    public void setLastSettleDay(LocalDate lastSettleDay) {
        this.lastSettleDay = lastSettleDay;
    }

    public LocalDate getNextSettleDay() {
        return nextSettleDay;
    }

    public void setNextSettleDay(LocalDate nextSettleDay) {
        this.nextSettleDay = nextSettleDay;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getSettleCycleType() {
        return settleCycleType;
    }

    public void setSettleCycleType(String settleCycleType) {
        this.settleCycleType = settleCycleType;
    }
}