package com.hisun.tms.csm.repository;

import com.hisun.tms.csm.model.SettleBaseDO;
import com.hisun.tms.demo.model.Example;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Created by chen on 9/1 0001.
 */
public interface SettleBaseDORespository extends JpaRepository<SettleBaseDO, String> {
    @Query(value = "select * from csm_settle_base where user_id = :mercId limit 0,1 ", nativeQuery=true  )
    SettleBaseDO findOneByUserID(@Param("mercId") String mercId);
}
