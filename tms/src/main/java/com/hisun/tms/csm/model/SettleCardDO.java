/*
 * @ClassName SettleCardDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-02 15:14:00
 */
package com.hisun.tms.csm.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "csm_settle_card")
@IdClass(SettleCardPK.class)
public class SettleCardDO {
    /**
     * @Fields userId 内部用户号
     */
    @Id
    @Column(name = "user_id")
    private String userId;
    /**
     * @Fields userName 用户名称
     */
    @Column(name = "user_name")
    private String userName;
    /**
     * @Fields capCorgNm 资金合作机构全称
     */
    @Column(name = "cap_corg_nm")
    private String capCorgNm;
    /**
     * @Fields capCorgSnm 资金合作机构简称
     */
    @Column(name = "cap_corg_snm")
    private String capCorgSnm;
    /**
     * @Fields capCorgNo 资金合作机构号
     */
    @Column(name = "cap_corg_no")
    private String capCorgNo;
    /**
     * @Fields subbranch 支行信息
     */
    @Column(name = "subbranch")
    private String subbranch;
    /**
     * @Fields capCardNo 资金卡号
     */
    @Id
    @Column(name = "cap_card_no")
    private String capCardNo;
    /**
     * @Fields capCardNm 资金卡户名
     */
    @Column(name = "cap_card_name")
    private String capCardName;
    /**
     * @Fields bnkMblNo 银行预留手机号码
     */
    @Column(name = "bnk_mbl_no")
    private String bnkMblNo;
    /**
     * @Fields stats 状态0:无效 1:生效
     */
    @Column(name = "stats")
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "eff_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateConverter.class)
    @Column(name = "exp_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    @Column(name = "opr_id")
    private String oprId;
    //最后修改时间
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "modify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;
    //创建时间
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * @Fields tmSmp 时间戳
     */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @Column(name = "tm_smp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tmSmp;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCapCorgNm() {
        return capCorgNm;
    }

    public void setCapCorgNm(String capCorgNm) {
        this.capCorgNm = capCorgNm;
    }

    public String getCapCorgSnm() {
        return capCorgSnm;
    }

    public void setCapCorgSnm(String capCorgSnm) {
        this.capCorgSnm = capCorgSnm;
    }

    public String getCapCorgNo() {
        return capCorgNo;
    }

    public void setCapCorgNo(String capCorgNo) {
        this.capCorgNo = capCorgNo;
    }

    public String getCapCardNo() {
        return capCardNo;
    }

    public void setCapCardNo(String capCardNo) {
        this.capCardNo = capCardNo;
    }

    public String getCapCardName() {
        return capCardName;
    }

    public void setCapCardName(String capCardName) {
        this.capCardName = capCardName;
    }

    public String getBnkMblNo() {
        return bnkMblNo;
    }

    public void setBnkMblNo(String bnkMblNo) {
        this.bnkMblNo = bnkMblNo;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getSubbranch() {
        return subbranch;
    }

    public void setSubbranch(String subbranch) {
        this.subbranch = subbranch;
    }
}