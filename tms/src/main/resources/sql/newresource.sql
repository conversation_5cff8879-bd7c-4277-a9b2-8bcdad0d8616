--公共管理
INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (801, '公共管理', '/cmmmgr', 0);

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80101,
        '柜员管理',
        '/cmmmgr/user',
        801
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8010101,
        '柜员管理',
        '/cmmmgr/user/user',
        80101
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801010101,
        '新增',
        '/cmmmgr/user/user:add',
        8010101
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801010102,
        '状态变更',
        '/cmmmgr/user/user:status',
        8010101
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801010103,
        '新增角色',
        '/cmmmgr/user/user:modify',
        8010101
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801010104,
        '分配角色',
        '/cmmmgr/user/user:role',
        8010101
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801010105,
        '删除角色',
        '/cmmmgr/user/user:delete',
        8010101
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8010102,
        '角色管理',
        '/cmmmgr/user/role',
        80101
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801010201,
        '分配权限',
        '/cmmmgr/user/role:resource',
        8010102
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801010202,
        '添加',
        '/cmmmgr/user/role:add',
        8010102
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801010203,
        '删除角色',
        '/cmmmgr/user/role:delete',
        8010102
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80103,
        '机构管理',
        '/cmmmgr/orgmgr',
        801
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8010301,
        '分公司管理',
        '/cmmmgr/orgmgr/office',
        80103
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801030101,
        '分公司添加',
        '/cmmmgr/orgmgr/office:add',
        8010301
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801030102,
        '分公司修改',
        '/cmmmgr/orgmgr/office:modify',
        8010301
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801030103,
        '分公司删除',
        '/cmmmgr/orgmgr/office:delete',
        8010301
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8010302,
        '部门管理',
        '/cmmmgr/orgmgr/branch',
        80103
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801030201,
        '分公司添加',
        '/cmmmgr/orgmgr/branch:add',
        8010302
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801030202,
        '分公司修改',
        '/cmmmgr/orgmgr/branch:modify',
        8010302
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801030203,
        '分公司删除',
        '/cmmmgr/orgmgr/branch:delete',
        8010302
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80104,
        '系统管理',
        '/cmmmgr/sysmgr',
        801
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8010401,
        '手机号段管理',
        '/cmmmgr/sysmgr/phsctrl',
        80104
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801040101,
        '手机号段添加',
        '/cmmmgr/sysmgr/phsctrl:add',
        8010401
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801040102,
        '手机号段修改',
        '/cmmmgr/sysmgr/phsctrl:modify',
        8010401
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        801040103,
        '手机号段删除',
        '/cmmmgr/sysmgr/phsctrl:delete',
        8010401
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8010402,
        '公共常量管理',
        '/cmmmgr/sysmgr/constants',
        80104
    );
--清结算管理
INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (802, '清结算管理', '/csmmgr', 0);

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80201,
        '手工调账',
        '/csmmgr/adjust',
        802
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8020101,
        '手工调账录入',
        '/csmmgr/adjust/record',
        80201
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8020102,
        '手工调账审核',
        '/csmmgr/adjust/audit',
        80201
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        802010201,
        '审核',
        '/csmmgr/adjust/audit/handle',
        8020102
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        802010202,
        '查看',
        '/csmmgr/adjust/audit/result',
        8020102
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8020103,
        '手工调账记录查询',
        '/csmmgr/adjust/query',
        80201
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80202,
        '交易清分查询',
        '/csmmgr/disqueryctrl',
        802
    );
--业务管理
INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (803, '业务管理', '/busmgr', 0);

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80301,
        '用户管理',
        '/busmgr/usrmgr',
        803
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030101,
        '用户信息查询',
        '/busmgr/usrmgr/userbasicinfo',
        80301
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030102,
        '用户余额查询',
        '/busmgr/usrmgr/usrBalance',
        80301
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030103,
        '用户收支明细',
        '/busmgr/usrmgr/amtinfo',
        80301
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030104,
        '用户开销户历史查询',
        '/busmgr/usrmgr/userhistory',
        80301
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030106,
        '用户密码重置',
        '/busmgr/usrmgr/pswdRst',
        80301
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80302,
        '运营管理',
        '/busmgr/oprmgr',
        803
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030201,
        'banner图展示维护',
        '/busmgr/oprmgr/banner',
        80302
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030202,
        '活动信息维护',
        '/busmgr/oprmgr/campaign',
        80302
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030203,
        '公告信息维护',
        '/busmgr/oprmgr/notic',
        80302
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030204,
        '短信模板管理',
        '/busmgr/oprmgr/smstempalte',
        80302
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030205,
        '消息模板管理',
        '/busmgr/oprmgr/message',
        80302
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80303,
        '商户管理',
        '/busmgr/mermgr',
        803
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030301,
        '商户资料维护',
        '/busmgr/mermgr/manage',
        80303
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030302,
        '商户资料浏览',
        '/busmgr/mermgr/info',
        80303
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030303,
        '商户审核',
        '/busmgr/mermgr/examine',
        80303
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030304,
        '商户状态管理',
        '/busmgr/mermgr/status',
        80303
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030305,
        '商户支付密码重置',
        '/busmgr/mermgr/mercpaypswdreset',
        80303
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030306,
        '商户交易权限管理',
        '/busmgr/mermgr/mercitf',
        80303
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030307,
        '商户登录密码重置',
        '/busmgr/mermgr/mercloginpswdreset',
        80303
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030308,
        '商户密钥重置',
        '/busmgr/mermgr/merckeyreset',
        80303
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80304,
        '交易管理',
        '/busmgr/trdmgr',
        803
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030401,
        '缴费记录查询',
        '/busmgr/trdmgr/cpmorder',
        80304
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030402,
        '商户订单查询',
        '/busmgr/trdmgr/orderquery',
        80304
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        803040201,
        '商户订单退款',
        '/busmgr/trdmgr/orderquery/rfdform',
        8030402
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030403,
        '退款订单查询',
        '/busmgr/trdmgr/onrrfdord',
        80304
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030404,
        '支付方式管理',
        '/busmgr/trdmgr/paytype',
        80304
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        803040401,
        '增加配置',
        '/busmgr/trdmgr/paytype:add',
        8030404
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        803040402,
        '查询配置',
        '/busmgr/trdmgr/paytype:query',
        8030404
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80305,
        '理财管理',
        '/busmgr/invmgr',
        803
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030501,
        '理财产品管理',
        '/busmgr/invmgr/proctrl',
        80305
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030507,
        '理财产品管理（定期）',
        '/busmgr/invmgr/regproctrl',
        80305
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030502,
        '理财收益查询',
        '/busmgr/invmgr/feectrl',
        80305
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030503,
        '理财交易查询',
        '/busmgr/invmgr/orderctrl',
        80305
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030504,
        '收益费率参数维护',
        '/busmgr/invmgr/dataratectrl',
        80305
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030505,
        '理财交易查询（定期）',
        '/busmgr/invmgr/regorderctrl',
        80305
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030506,
        '理财收益查询（定期）',
        '/busmgr/invmgr/regfeectrl',
        80305
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80306,
        '营销管理',
        '/busmgr/mkmmgr',
        803
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030601,
        '营销活动管理',
        '/busmgr/mkmmgr/activity',
        80306
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        803060101,
        '增加活动',
        '/busmgr/mkmmgr/activity:add',
        8030601
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        803060102,
        '修改营销活动',
        '/busmgr/mkmmgr/activity:modify',
        8030601
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        803060103,
        '删除营销活动',
        '/busmgr/mkmmgr/activity:delete',
        8030601
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030602,
        '营销活动审核',
        '/busmgr/mkmmgr/examine',
        80306
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030603,
        '用户营销工具领用明细',
        '/busmgr/mkmmgr/releaseDetail',
        80306
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8030604,
        '用户营销工具使用明细',
        '/busmgr/mkmmgr/consumeDetail',
        80306
    );
--资金出入
INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (804, '资金出入', '/cptmgr', 0);

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80401,
        '参数管理',
        '/cptmgr/param',
        804
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8040101,
        '银行卡管理',
        '/cptmgr/param/card',
        80401
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        804010101,
        '银行卡查询',
        '/cptmgr/param/card/card',
        8040101
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80402,
        '订单管理',
        '/cptmgr/order',
        804
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8040201,
        '付款订单查询',
        '/cptmgr/order/withdraw',
        80402
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8040202,
        '退款款订单查询',
        '/cptmgr/order/refund',
        80402
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8040203,
        '充值订单查询',
        '/cptmgr/order/fund',
        80402
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80403,
        '对账管理',
        '/cptmgr/check',
        804
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8040301,
        '对账主控管理',
        '/cptmgr/check/controller',
        80403
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8040302,
        '对账差错管理',
        '/cptmgr/check/error',
        80403
    );
--风控管理
INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (805, '风控管理', '/rsmmgr', 0);

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80501,
        '黑白名单管理',
        '/rsmmgr/riskList',
        805
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8050101,
        '黑名单管理',
        '/rsmmgr/riskList/black',
        80501
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8050102,
        '白名单管理',
        '/rsmmgr/riskList/white',
        80501
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80502,
        '实时风控管理',
        '/rsmmgr/check',
        805
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8050201,
        '风控规则管理',
        '/rsmmgr/check/rule',
        80502
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8050202,
        '风控检查规则管理',
        '/rsmmgr/check/censor',
        80502
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        8050203,
        '风控参数管理',
        '/rsmmgr/check/param',
        80502
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80503,
        '可疑交易查询',
        '/rsmmgr/highrisk',
        805
    );
--报表管理
INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (806, '报表管理', '/rptmgr', 0);

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80601,
        '报表定义',
        '/rptmgr/define',
        806
    );

INSERT INTO
    `resource` (
        `resource_id`,
        `resource_name`,
        `resource`,
        `parent_id`
    )
VALUES (
        80602,
        '报表批次操作',
        '/rptmgr/opera',
        806
    );
--更新商户退款的权限路径
update resource
set
    resource = '/busmgr/trdmgr/orderquery/rfdform'
where
    resource_id = '803040201'