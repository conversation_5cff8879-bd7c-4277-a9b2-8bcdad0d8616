/*1分公司信息表*/
CREATE TABLE cmm_office_info (
      id          INT(11)     NOT NULL AUTO_INCREMENT,
      office_id   VARCHAR(20) NOT NULL COMMENT  '分公司编号',
      office_nm   VARCHAR(50) NOT NULL COMMENT  '分公司名称',
      oper_id     VARCHAR(20) NOT NULL COMMENT  '操作员ID',
	  modify_time DATETIME    NOT NULL COMMENT  '修改时间',
	  create_time DATETIME    NOT NULL COMMENT  '创建时间',
	  tm_smp      TIMESTAMP   NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
	  CONSTRAINT pk_cmm_office_info PRIMARY KEY(id)
	  ) COMMENT = '分公司信息表' DEFAULT CHARSET=utf8;
	  ALTER TABLE cmm_office_info ADD INDEX idx_cmm_office_info_office_id (office_id);
/*2部门信息表*/
CREATE TABLE cmm_branch_info (
      id           int(11)     NOT NULL ,
      bra_id       VARCHAR(20) NOT NULL COMMENT '部门编号',
      bra_nm       VARCHAR(50) NOT NULL COMMENT '部门名称',
      office_id    VARCHAR(20) NOT NULL COMMENT '归属公司编号',
      office_nm    VARCHAR(50) NOT NULL COMMENT '归属公司名称',
      oper_id      VARCHAR(20) NOT NULL COMMENT '操作员ID',
      modify_time  DATETIME    NOT NULL COMMENT '修改时间',
      create_time  DATETIME    NOT NULL COMMENT '创建时间',
      tm_smp       TIMESTAMP   NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
      CONSTRAINT pk_cmm_branch_info PRIMARY KEY(id)
      ) COMMENT = '部门信息表' DEFAULT CHARSET=utf8;
      ALTER TABLE cmm_branch_info ADD INDEX idx_cmm_branch_info_bra_id (office_id,bra_id);