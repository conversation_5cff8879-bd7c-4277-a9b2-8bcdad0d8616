-- 邮件模版表
CREATE TABLE IF NOT EXISTS cmm_email_template (
    id CHAR(8) NOT NULL COMMENT '邮件模版ID',
    type VARCHAR(3) NOT NULL COMMENT '邮件类型',
    lvl VARCHAR(3) NOT NULL COMMENT '邮件级别',
    replace_field VARCHAR(256) NOT NULL COMMENT '替换变量',
    subject VARCHAR(256) NOT NULL COMMENT '邮件主题',
    template_content_cn VARCHAR(1024) NOT NULL COMMENT '中文邮件模版内容',
    template_content_en VARCHAR(1024) NOT NULL COMMENT '英文邮件模版内容',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
);

-- 主键
ALTER TABLE cmm_email_template
ADD CONSTRAINT pk_cmm_email_template PRIMARY KEY (id);
-- 索引
ALTER TABLE cmm_email_template
ADD UNIQUE idx_cmm_email_template_1 (id);
-- 索引
ALTER TABLE cmm_email_template
ADD INDEX idx_cmm_email_template_2 (eff_date, exp_date);

-- 初始化示例数据
INSERT INTO
    `cmm_email_template` (
        `id`,
        `type`,
        `lvl`,
        `replace_field`,
        `subject`,
        `template_content_cn`,
        `template_content_en`,
        `stats`,
        `eff_date`,
        `exp_date`,
        `opr_id`,
        `create_time`,
        `modify_time`
    )
VALUES (
        'CMM00001',
        '1',
        '0',
        'code|',
        '账户注册验证码',
        '您的验证码是：[$]，请在5分钟内完成注册。',
        'Your verification code is: [$], please complete registration within 5 minutes.',
        '1',
        '2025-07-17',
        '9999-12-31',
        'admin',
        NOW(),
        NOW()
    );