CREATE TABLE merc_register_seq (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  examin_Nm VARCHAR(100) COMMENT '商户名称',
  examin_status VARCHAR(2) COMMENT '审批状态',
  remark VARCHAR(400) COMMENT '审批意见',
  merc_Info VARCHAR(3000) COMMENT '商户信息',
  settle_Info VARCHAR(500) COMMENT '结算信息',
  rate_Info VARCHAR(1000) COMMENT '费率',
  modify_time VARCHAR(20) COMMENT '修改时间',
  create_time VARCHAR(20) COMMENT '创建时间'
);

CREATE TABLE tms_user_opr_log (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(16) COMMENT '用户id',
  opr_nm VARCHAR(50) COMMENT '操作名称',
  ip VARCHAR(20) COMMENT '操作IP',
  lv VARCHAR(2) COMMENT '操作级别',
  modify_time datetime COMMENT '修改时间',
  create_time datetime COMMENT '创建时间'
);
ALTER TABLE tms_user_opr_log COMMENT '用户操作日志表';

drop table tms_opr_mapper;
CREATE TABLE tms_opr_mapper (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  opr_url VARCHAR(50) COMMENT '操作url',
  opr_nm VARCHAR(50) COMMENT '操作名称',
  method VARCHAR(10) COMMENT '方法',
  lv VARCHAR(2) COMMENT '操作级别',
  modify_time datetime COMMENT '修改时间',
  create_time datetime COMMENT '创建时间'
);
ALTER TABLE tms_opr_mapper COMMENT '操作映射表';

ALTER TABLE tms_user_opr_log ADD INDEX query_opr_log ( user_id );
ALTER TABLE tms_opr_mapper ADD INDEX query_opr_opr_mapper  ( opr_url );


insert into resource (resource_id, resource_name, resource, parent_id, resource_range, is_floor, resource_type) values (8010403, '柜员操作记录', '/cmmmgr/sysmgr/userOpr', 80104, null, null, null);
