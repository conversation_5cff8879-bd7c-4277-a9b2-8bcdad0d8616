<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpo.IWithdrawOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.WithdrawOrderDO" >
        <result column="REQ_ORD_NO" property="reqOrdNo" jdbcType="VARCHAR" />
        <result column="WC_ORD_NO" property="wcOrdNo" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="USER_NM" property="userNm" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="AGR_PAY_DT" property="agrPayDt" jdbcType="DATE" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="WC_APL_AMT" property="wcAplAmt" jdbcType="DECIMAL" />
        <result column="CAP_CORG" property="capCorg" jdbcType="VARCHAR" />
        <result column="CAP_CRD_NM" property="capCrdNm" jdbcType="VARCHAR" />
        <result column="SUB_BRANCH" property="subBranch" jdbcType="VARCHAR" />
        <result column="CRD_NO_ENC" property="crdNo" jdbcType="VARCHAR" />
        <result column="ORD_STS" property="ordSts" jdbcType="VARCHAR" />
        <result column="RUT_CORG_JRN" property="rutCorgJrn" jdbcType="VARCHAR" />
        <result column="ORG_RSP_MSG" property="orgRspMsg" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="ORD_SUCC_DT" property="ordSuccDt" jdbcType="DATE" />
    </resultMap>

    <!--根据条件查询付款订单列表，默认按照订单日期、订单时间排序-->
    <select id="getWithdrawOrderList" resultMap="BaseResultMap">
        select t1.USER_NM,
        t1.REQ_ORD_NO, t1.WC_ORD_NO, t1.USER_ID, t1.ORD_DT, t1.ORD_TM, t1.AGR_PAY_DT, t1.CORP_BUS_SUB_TYP, t1.RUT_CORG,
        t1.WC_APL_AMT, t1.CAP_CORG, t1.CAP_CRD_NM, t1.CRD_NO_ENC, t1.ORD_STS,
        t2.RUT_CORG_JRN, t2.ORG_RSP_MSG, t1.RMK , t1.ORD_SUCC_DT, t1.SUB_BRANCH
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="ordSts != null and ordSts != '' and ordSts != 'A1'" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[ and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        and t1.ORD_STS != 'A1'
        and t1.CORP_BUS_TYP = '06'
        order by t1.ord_dt desc, t1.ord_tm desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageEnd,jdbcType=INTEGER}
    </select>

    <select id="getDCWithdrawOrderList" resultMap="BaseResultMap">
        select t1.USER_NM,
        t1.REQ_ORD_NO, t1.WC_ORD_NO, t1.USER_ID, t1.ORD_DT, t1.ORD_TM, t1.AGR_PAY_DT, t1.CORP_BUS_SUB_TYP, t1.RUT_CORG,
        t1.WC_APL_AMT, t1.CAP_CORG, t1.CAP_CRD_NM, t1.CRD_NO_ENC, t1.ORD_STS,
        t2.RUT_CORG_JRN, t2.ORG_RSP_MSG, t1.RMK , t1.ORD_SUCC_DT, t1.SUB_BRANCH
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="ordSts != null and ordSts != '' and ordSts != 'A1'" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[ and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        and t1.ORD_STS != 'A1'
        and t1.CORP_BUS_TYP = 'DX'
        order by t1.ord_dt desc, t1.ord_tm desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageEnd,jdbcType=INTEGER}
    </select>

    <!--根据条件查询付款订单列表，默认按照订单日期、订单时间排序-->
    <select id="getWithdrawOrderListOnlyWaitReview" resultMap="BaseResultMap">
        select t1.USER_NM,
        t1.REQ_ORD_NO, t1.WC_ORD_NO, t1.USER_ID, t1.ORD_DT, t1.ORD_TM, t1.AGR_PAY_DT, t1.CORP_BUS_SUB_TYP, t1.RUT_CORG,
        t1.WC_APL_AMT, t1.CAP_CORG, t1.CAP_CRD_NM, t1.CRD_NO_ENC, t1.ORD_STS,
        t2.RUT_CORG_JRN, t2.ORG_RSP_MSG, t1.RMK , t1.ORD_SUCC_DT, t1.SUB_BRANCH
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[ and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        and t1.ORD_STS = 'A1'
        and t1.CORP_BUS_TYP = '06'
        order by t1.ord_dt desc, t1.ord_tm desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageEnd,jdbcType=INTEGER}
    </select>

    <select id="getDCWithdrawOrderListOnlyWaitReview" resultMap="BaseResultMap">
        select t1.USER_NM,
        t1.REQ_ORD_NO, t1.WC_ORD_NO, t1.USER_ID, t1.ORD_DT, t1.ORD_TM, t1.AGR_PAY_DT, t1.CORP_BUS_SUB_TYP, t1.RUT_CORG,
        t1.WC_APL_AMT, t1.CAP_CORG, t1.CAP_CRD_NM, t1.CRD_NO_ENC, t1.ORD_STS,
        t2.RUT_CORG_JRN, t2.ORG_RSP_MSG, t1.RMK , t1.ORD_SUCC_DT, t1.SUB_BRANCH
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[ and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        and t1.ORD_STS = 'A1'
        and t1.CORP_BUS_TYP = 'DX'
        order by t1.ord_dt desc, t1.ord_tm desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageEnd,jdbcType=INTEGER}
    </select>

    <!--根据条件查询付款订单列表总笔数-->
    <select id="getWithdrawOrderListTotNum" resultType="java.lang.Integer">
        select count(1)
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="ordSts != null and ordSts != ''" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[  and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[  and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        and t1.CORP_BUS_TYP = '06'
        and t1.ORD_STS != 'A1'

    </select>


    <select id="getDCWithdrawOrderListTotNum" resultType="java.lang.Integer">
        select count(1)
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="ordSts != null and ordSts != ''" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[  and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[  and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        and t1.CORP_BUS_TYP = 'DX'
        and t1.ORD_STS != 'A1'

    </select>


    <!--根据条件查询付款订单列表总笔数-->
    <select id="getWithdrawOrderListTotNumOnlyWaitReview" resultType="java.lang.Integer">
        select count(1)
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="ordSts != null and ordSts != '' and ordSts != 'A1'" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[  and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[  and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        and t1.ORD_STS = 'A1'
        and t1.CORP_BUS_TYP = '06'
    </select>

    <select id="getDCWithdrawOrderListTotNumOnlyWaitReview" resultType="java.lang.Integer">
        select count(1)
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="ordSts != null and ordSts != '' and ordSts != 'A1'" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[  and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[  and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        and t1.ORD_STS = 'A1'
        and t1.CORP_BUS_TYP = 'DX'
    </select>

    <!--根据付款内部订单号，查询付款订单详细信息-->
    <select id="getWithdrawOrder" resultMap="BaseResultMap">
        select
        t1.REQ_ORD_NO, t1.WC_ORD_NO, t1.USER_ID, t1.ORD_DT, t1.ORD_TM, t1.AGR_PAY_DT, t1.CORP_BUS_SUB_TYP, t1.RUT_CORG,
        t1.WC_APL_AMT, t1.CAP_CORG, t1.CAP_CRD_NM, t1.CRD_NO_ENC, t1.ORD_STS,
        t2.RUT_CORG_JRN, t2.ORG_RSP_MSG, T1.RMK
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        and t1.WC_ORD_NO = #{wcOrdNo, jdbcType=VARCHAR}
    </select>

    <!--根据条件查询付款订单列表，默认按照订单日期、订单时间排序-->
    <select id="getWithdrawOrderListNew" resultMap="BaseResultMap">
        select
        t1.REQ_ORD_NO, t1.WC_ORD_NO, t1.USER_ID, t1.ORD_DT, t1.ORD_TM, t1.AGR_PAY_DT, t1.CORP_BUS_SUB_TYP, t1.RUT_CORG,
        t1.WC_APL_AMT, t1.CAP_CORG, t1.CAP_CRD_NM, t1.CRD_NO_ENC, t1.ORD_STS,
        t2.RUT_CORG_JRN, t2.ORG_RSP_MSG, t1.RMK
        from cpo_withdraw_order t1,
        cpo_withdraw_suborder t2
        where t1.WC_ORD_NO = t2.WC_ORD_NO
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and t1.REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="wcOrdNo != null and wcOrdNo != ''" >
            and t1.WC_ORD_NO = #{wcOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rutCorgJrn != null and rutCorgJrn != ''" >
            and t2.RUT_CORG_JRN = #{rutCorgJrn,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and t1.RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="ordSts != null and ordSts != ''" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[ and t1.ORD_DT >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and t1.ORD_DT <= #{endDt,jdbcType=DATE} ]]>
        </if>
        <if test="agrPayDt != null" >
            <![CDATA[ and t1.AGR_PAY_DT = #{agrPayDt,jdbcType=DATE} ]]>
        </if>
        order by t1.ord_dt desc, t1.ord_tm desc
    </select>


</mapper>