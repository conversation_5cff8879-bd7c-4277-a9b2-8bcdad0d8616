<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.mkm.dao.IMsgInfoDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.mkm.model.MsgInfoDO" >
        <id column="msg_cd" property="msgCd" jdbcType="CHAR" />
        <id column="language" property="language" jdbcType="VARCHAR" />
        <id column="scenario" property="scenario" jdbcType="VARCHAR" />
        <result column="msg_info" property="msgInfo" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modifyTime" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List">
        msg_cd, language, msg_info, create_time, modifyTime, scenario
    </sql>
    
    <!-- 根据条件查询国际化消息信息列表 -->
    <select id="findByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM lemon_msg_info
        WHERE 1=1
        <if test="msgCd != null and msgCd != ''">
            AND msg_cd LIKE CONCAT('%', #{msgCd}, '%')
        </if>
        <if test="language != null and language != ''">
            AND language = #{language}
        </if>
        <if test="msgInfo != null and msgInfo != ''">
            AND msg_info LIKE CONCAT('%', #{msgInfo}, '%')
        </if>
        <if test="scenario != null and scenario != ''">
            AND scenario = #{scenario}
        </if>
        ORDER BY create_time DESC
        <if test="pageBegin != null and pageEnd != null">
            LIMIT #{pageBegin}, #{pageEnd}
        </if>
    </select>
    
    <!-- 查询总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM lemon_msg_info
        WHERE 1=1
        <if test="msgCd != null and msgCd != ''">
            AND msg_cd LIKE CONCAT('%', #{msgCd}, '%')
        </if>
        <if test="language != null and language != ''">
            AND language = #{language}
        </if>
        <if test="msgInfo != null and msgInfo != ''">
            AND msg_info LIKE CONCAT('%', #{msgInfo}, '%')
        </if>
        <if test="scenario != null and scenario != ''">
            AND scenario = #{scenario}
        </if>
    </select>
    
    <!-- 根据主键获取国际化消息信息 -->
    <select id="getByPrimaryKey" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List" />
        FROM lemon_msg_info
        WHERE msg_cd = #{msgCd} AND language = #{language} AND scenario = #{scenario}
    </select>
    
    <!-- 插入国际化消息信息 -->
    <insert id="insert" parameterType="com.hisun.tms.mkm.model.MsgInfoDO">
        INSERT INTO lemon_msg_info (
            msg_cd, language, msg_info, create_time, modifyTime, scenario
        ) VALUES (
            #{msgCd}, #{language}, #{msgInfo}, #{createTime}, #{modifyTime}, #{scenario}
        )
    </insert>
    
    <!-- 更新国际化消息信息 -->
    <update id="update" parameterType="com.hisun.tms.mkm.model.MsgInfoDO">
        UPDATE lemon_msg_info
        <set>
            <if test="msgInfo != null">
                msg_info = #{msgInfo},
            </if>
            <if test="modifyTime != null">
                modifyTime = #{modifyTime},
            </if>
        </set>
        WHERE msg_cd = #{msgCd} AND language = #{language} AND scenario = #{scenario}
    </update>
    
    <!-- 删除国际化消息信息 -->
    <delete id="delete">
        DELETE FROM lemon_msg_info
        WHERE msg_cd = #{msgCd} AND language = #{language} AND scenario = #{scenario}
    </delete>
</mapper>