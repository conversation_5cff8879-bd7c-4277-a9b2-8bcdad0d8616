<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.tam.dao.IExchangeAuditDao">
	<resultMap id="BaseResultMap" type="com.hisun.tms.tam.model.entity.ExchangeAuditDO">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="order_no" property="orderNo" jdbcType="VARCHAR" />
		<result column="user_id" property="userId" jdbcType="VARCHAR" />
		<result column="direction" property="direction" jdbcType="VARCHAR" />
		<result column="from_coin" property="fromCoin" jdbcType="VARCHAR" />
		<result column="from_amount" property="fromAmount" jdbcType="DECIMAL" />
		<result column="to_coin" property="toCoin" jdbcType="VARCHAR" />
		<result column="to_amount" property="toAmount" jdbcType="DECIMAL" />
		<result column="fee_coin" property="feeCoin" jdbcType="VARCHAR" />
		<result column="fee_amount" property="feeAmount" jdbcType="DECIMAL" />
		<result column="exchange_rate" property="exchangeRate" jdbcType="DECIMAL" />
		<result column="from_usd_rate" property="fromUsdRate" jdbcType="DECIMAL" />
		<result column="to_usd_rate" property="toUsdRate" jdbcType="DECIMAL" />
		<result column="from_amount_usd" property="fromAmountUsd" jdbcType="DECIMAL" />
		<result column="to_amount_usd" property="toAmountUsd" jdbcType="DECIMAL" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="first_audit_user" property="firstAuditUser" jdbcType="VARCHAR" />
		<result column="first_audit_time" property="firstAuditTime" jdbcType="TIMESTAMP" />
		<result column="first_audit_result" property="firstAuditResult" jdbcType="VARCHAR" />
		<result column="first_audit_opinion" property="firstAuditOpinion" jdbcType="VARCHAR" />
		<result column="second_audit_user" property="secondAuditUser" jdbcType="VARCHAR" />
		<result column="second_audit_time" property="secondAuditTime" jdbcType="TIMESTAMP" />
		<result column="second_audit_result" property="secondAuditResult" jdbcType="VARCHAR" />
		<result column="second_audit_opinion" property="secondAuditOpinion" jdbcType="VARCHAR" />
		<result column="execute_time" property="executeTime" jdbcType="TIMESTAMP" />
		<result column="reject_reason" property="rejectReason" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
	</resultMap>

	<sql id="Base_Column_List">
		id, order_no, user_id, direction, from_coin, from_amount, to_coin, to_amount, fee_coin, fee_amount,
		exchange_rate, from_usd_rate, to_usd_rate, from_amount_usd, to_amount_usd, status, first_audit_user, 
		first_audit_time, first_audit_result, first_audit_opinion, second_audit_user, second_audit_time, 
		second_audit_result, second_audit_opinion, execute_time, reject_reason, create_time, update_time
	</sql>

	<select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from tam_exchange_order
		where id = #{id,jdbcType=VARCHAR}
	</select>

	<select id="getByOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from tam_exchange_order
		where order_no = #{orderNo,jdbcType=VARCHAR}
	</select>

	<delete id="delete" parameterType="java.lang.Long">
		delete from tam_exchange_order
		where id = #{id,jdbcType=VARCHAR}
	</delete>

	<insert id="insert" parameterType="com.hisun.tms.tam.model.entity.ExchangeAuditDO">
		insert into tam_exchange_order
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="orderNo != null">
				order_no,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="direction != null">
				direction,
			</if>
			<if test="fromCoin != null">
				from_coin,
			</if>
			<if test="fromAmount != null">
				from_amount,
			</if>
			<if test="toCoin != null">
				to_coin,
			</if>
			<if test="toAmount != null">
				to_amount,
			</if>
			<if test="feeCoin != null">
				fee_coin,
			</if>
			<if test="feeAmount != null">
				fee_amount,
			</if>
			<if test="exchangeRate != null">
				exchange_rate,
			</if>
			<if test="fromUsdRate != null">
				from_usd_rate,
			</if>
			<if test="toUsdRate != null">
				to_usd_rate,
			</if>
			<if test="fromAmountUsd != null">
				from_amount_usd,
			</if>
			<if test="toAmountUsd != null">
				to_amount_usd,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="firstAuditUser != null">
				first_audit_user,
			</if>
			<if test="firstAuditTime != null">
				first_audit_time,
			</if>
			<if test="firstAuditResult != null">
				first_audit_result,
			</if>
			<if test="firstAuditOpinion != null">
				first_audit_opinion,
			</if>
			<if test="secondAuditUser != null">
				second_audit_user,
			</if>
			<if test="secondAuditTime != null">
				second_audit_time,
			</if>
			<if test="secondAuditResult != null">
				second_audit_result,
			</if>
			<if test="secondAuditOpinion != null">
				second_audit_opinion,
			</if>
			<if test="executeTime != null">
				execute_time,
			</if>
			<if test="rejectReason != null">
				reject_reason,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=VARCHAR},
			</if>
			<if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
				#{userId,jdbcType=VARCHAR},
			</if>
			<if test="direction != null">
				#{direction,jdbcType=VARCHAR},
			</if>
			<if test="fromCoin != null">
				#{fromCoin,jdbcType=VARCHAR},
			</if>
			<if test="fromAmount != null">
				#{fromAmount,jdbcType=DECIMAL},
			</if>
			<if test="toCoin != null">
				#{toCoin,jdbcType=VARCHAR},
			</if>
			<if test="toAmount != null">
				#{toAmount,jdbcType=DECIMAL},
			</if>
			<if test="feeCoin != null">
				#{feeCoin,jdbcType=VARCHAR},
			</if>
			<if test="feeAmount != null">
				#{feeAmount,jdbcType=DECIMAL},
			</if>
			<if test="exchangeRate != null">
				#{exchangeRate,jdbcType=DECIMAL},
			</if>
			<if test="fromUsdRate != null">
				#{fromUsdRate,jdbcType=DECIMAL},
			</if>
			<if test="toUsdRate != null">
				#{toUsdRate,jdbcType=DECIMAL},
			</if>
			<if test="fromAmountUsd != null">
				#{fromAmountUsd,jdbcType=DECIMAL},
			</if>
			<if test="toAmountUsd != null">
				#{toAmountUsd,jdbcType=DECIMAL},
			</if>
			<if test="status != null">
				#{status,jdbcType=VARCHAR},
			</if>
			<if test="firstAuditUser != null">
				#{firstAuditUser,jdbcType=VARCHAR},
			</if>
			<if test="firstAuditTime != null">
				#{firstAuditTime,jdbcType=TIMESTAMP},
			</if>
			<if test="firstAuditResult != null">
				#{firstAuditResult,jdbcType=VARCHAR},
			</if>
			<if test="firstAuditOpinion != null">
				#{firstAuditOpinion,jdbcType=VARCHAR},
			</if>
			<if test="secondAuditUser != null">
				#{secondAuditUser,jdbcType=VARCHAR},
			</if>
			<if test="secondAuditTime != null">
				#{secondAuditTime,jdbcType=TIMESTAMP},
			</if>
			<if test="secondAuditResult != null">
				#{secondAuditResult,jdbcType=VARCHAR},
			</if>
			<if test="secondAuditOpinion != null">
				#{secondAuditOpinion,jdbcType=VARCHAR},
			</if>
			<if test="executeTime != null">
				#{executeTime,jdbcType=TIMESTAMP},
			</if>
			<if test="rejectReason != null">
				#{rejectReason,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>

	<update id="update" parameterType="com.hisun.tms.tam.model.entity.ExchangeAuditDO">
		update tam_exchange_order
		<set>
			<if test="orderNo != null">
				order_no = #{orderNo,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
				user_id = #{userId,jdbcType=VARCHAR},
			</if>
			<if test="direction != null">
				direction = #{direction,jdbcType=VARCHAR},
			</if>
			<if test="fromCoin != null">
				from_coin = #{fromCoin,jdbcType=VARCHAR},
			</if>
			<if test="fromAmount != null">
				from_amount = #{fromAmount,jdbcType=DECIMAL},
			</if>
			<if test="toCoin != null">
				to_coin = #{toCoin,jdbcType=VARCHAR},
			</if>
			<if test="toAmount != null">
				to_amount = #{toAmount,jdbcType=DECIMAL},
			</if>
			<if test="feeCoin != null">
				fee_coin = #{feeCoin,jdbcType=VARCHAR},
			</if>
			<if test="feeAmount != null">
				fee_amount = #{feeAmount,jdbcType=DECIMAL},
			</if>
			<if test="exchangeRate != null">
				exchange_rate = #{exchangeRate,jdbcType=DECIMAL},
			</if>
			<if test="fromUsdRate != null">
				from_usd_rate = #{fromUsdRate,jdbcType=DECIMAL},
			</if>
			<if test="toUsdRate != null">
				to_usd_rate = #{toUsdRate,jdbcType=DECIMAL},
			</if>
			<if test="fromAmountUsd != null">
				from_amount_usd = #{fromAmountUsd,jdbcType=DECIMAL},
			</if>
			<if test="toAmountUsd != null">
				to_amount_usd = #{toAmountUsd,jdbcType=DECIMAL},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="firstAuditUser != null">
				first_audit_user = #{firstAuditUser,jdbcType=VARCHAR},
			</if>
			<if test="firstAuditTime != null">
				first_audit_time = #{firstAuditTime,jdbcType=TIMESTAMP},
			</if>
			<if test="firstAuditResult != null">
				first_audit_result = #{firstAuditResult,jdbcType=VARCHAR},
			</if>
			<if test="firstAuditOpinion != null">
				first_audit_opinion = #{firstAuditOpinion,jdbcType=VARCHAR},
			</if>
			<if test="secondAuditUser != null">
				second_audit_user = #{secondAuditUser,jdbcType=VARCHAR},
			</if>
			<if test="secondAuditTime != null">
				second_audit_time = #{secondAuditTime,jdbcType=TIMESTAMP},
			</if>
			<if test="secondAuditResult != null">
				second_audit_result = #{secondAuditResult,jdbcType=VARCHAR},
			</if>
			<if test="secondAuditOpinion != null">
				second_audit_opinion = #{secondAuditOpinion,jdbcType=VARCHAR},
			</if>
			<if test="executeTime != null">
				execute_time = #{executeTime,jdbcType=TIMESTAMP},
			</if>
			<if test="rejectReason != null">
				reject_reason = #{rejectReason,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where id = #{id,jdbcType=VARCHAR}
	</update>

	<select id="find" resultMap="BaseResultMap" parameterType="com.hisun.tms.tam.model.entity.ExchangeAuditDO">
		SELECT
		<include refid="Base_Column_List" />
		FROM tam_exchange_order
		<where>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP}
			</if>
			<if test="userId != null and userId != ''">
				AND user_id = #{userId,jdbcType=VARCHAR}
			</if>
			<if test="orderNo != null and orderNo != ''">
				AND order_no like concat('%', #{orderNo,jdbcType=VARCHAR}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status,jdbcType=VARCHAR}
				AND status != 'SECOND_AUDIT'
			</if>
			<if test="direction != null and direction != ''">
				AND direction = #{direction,jdbcType=VARCHAR}
			</if>
			<if test="status == null or status == ''">
				AND status != 'SECOND_AUDIT'
			</if>
		</where>
		ORDER BY create_time DESC
		<if test="pageNum != null and pageSize != null">
			limit #{pageNum}, #{pageSize}
		</if>
	</select>


	<select id="findSecondAudit" resultMap="BaseResultMap" parameterType="com.hisun.tms.tam.model.entity.ExchangeAuditDO">
		SELECT
		<include refid="Base_Column_List" />
		FROM tam_exchange_order
		<where>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP}
			</if>
			<if test="userId != null and userId != ''">
				AND user_id = #{userId,jdbcType=VARCHAR}
			</if>
			<if test="orderNo != null and orderNo != ''">
				AND order_no like concat('%', #{orderNo,jdbcType=VARCHAR}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status,jdbcType=VARCHAR}
			</if>
			<if test="direction != null and direction != ''">
				AND direction = #{direction,jdbcType=VARCHAR}
			</if>
			AND status = 'SECOND_AUDIT'
		</where>
		ORDER BY create_time DESC
		<if test="pageNum != null and pageSize != null">
			limit #{pageNum}, #{pageSize}
		</if>
	</select>



	<select id="queryList" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from tam_exchange_order
		<where>
			<if test="status != null and status != ''">
				status = #{status,jdbcType=VARCHAR}
			</if>
			<if test="statusList != null">
				and status in
				<foreach item="item" index="index" collection="statusList"
					open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="userId != null and userId != ''">
				and user_id = #{userId,jdbcType=VARCHAR}
			</if>
			<if test="orderNo != null and orderNo != ''">
				and order_no like concat('%', #{orderNo,jdbcType=VARCHAR}, '%')
			</if>
			<if test="direction != null and direction != ''">
				and direction = #{direction,jdbcType=VARCHAR}
			</if>
		</where>
		order by create_time desc
		<if test="pageNum != null and pageSize != null">
			limit #{pageNum}, #{pageSize}
		</if>
	</select>

	<select id="queryByPage" resultMap="BaseResultMap" parameterType="com.hisun.tms.tam.model.entity.ExchangeAuditDO">
		select
		<include refid="Base_Column_List" />
		from tam_exchange_order
		<where>
			<if test="status != null and status != ''">
				status = #{status,jdbcType=VARCHAR}
			</if>
			<if test="userId != null and userId != ''">
				and user_id = #{userId,jdbcType=VARCHAR}
			</if>
			<if test="orderNo != null and orderNo != ''">
				and order_no like concat('%', #{orderNo,jdbcType=VARCHAR}, '%')
			</if>
			<if test="direction != null and direction != ''">
				and direction = #{direction,jdbcType=VARCHAR}
			</if>
		</where>
		order by create_time desc
		<if test="pageNum != null and pageSize != null">
			limit #{pageNum}, #{pageSize}
		</if>
	</select>

	<select id="count" resultType="int" parameterType="com.hisun.tms.tam.model.entity.ExchangeAuditDO">
		SELECT COUNT(1)
		FROM tam_exchange_order
		<where>
			<if test="userId != null and userId != ''">
				AND user_id = #{userId,jdbcType=VARCHAR}
			</if>
			<if test="orderNo != null and orderNo != ''">
				AND order_no like concat('%', #{orderNo,jdbcType=VARCHAR}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status,jdbcType=VARCHAR}
				AND status != 'SECOND_AUDIT'
			</if>
			<if test="direction != null and direction != ''">
				AND direction = #{direction,jdbcType=VARCHAR}
			</if>
			<if test="status == null or status == ''">
				AND status != 'SECOND_AUDIT'
			</if>
		</where>
	</select>

	<select id="countSecondAudit" resultType="int" parameterType="com.hisun.tms.tam.model.entity.ExchangeAuditDO">
		SELECT COUNT(1)
		FROM tam_exchange_order
		<where>
			<if test="userId != null and userId != ''">
				AND user_id = #{userId,jdbcType=VARCHAR}
			</if>
			<if test="orderNo != null and orderNo != ''">
				AND order_no like concat('%', #{orderNo,jdbcType=VARCHAR}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status,jdbcType=VARCHAR}
			</if>
			<if test="direction != null and direction != ''">
				AND direction = #{direction,jdbcType=VARCHAR}
			</if>
			AND status = 'SECOND_AUDIT'
		</where>
	</select>
</mapper>
