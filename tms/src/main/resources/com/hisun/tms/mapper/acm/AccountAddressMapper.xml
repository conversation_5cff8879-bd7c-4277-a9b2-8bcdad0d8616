<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.acm.dao.IAccountAddressDao">

    <resultMap id="BaseResultMap" type="com.hisun.tms.acm.model.AccountAddressDO">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="vault_code" property="vaultCode" jdbcType="VARCHAR" />
        <result column="group_code" property="groupCode" jdbcType="VARCHAR" />
        <result column="account_id" property="accountId" jdbcType="BIGINT" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="network" property="network" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="acm_ac_no" property="acmAcNo" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="qrcode_base64" property="qrcodeBase64" jdbcType="LONGVARCHAR" />
        <result column="use_type" property="useType" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List">
        id, vault_code, group_code, account_id, address, network, status, 
        acm_ac_no, user_id, qrcode_base64, use_type, create_time, update_time
    </sql>

    <!-- 根据条件查询账户地址列表（支持分页和搜索） -->
    <select id="findAll" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM dm_account_address
        WHERE 1=1
        <if test="vaultCode != null and vaultCode != ''">
            AND vault_code LIKE CONCAT('%', #{vaultCode}, '%')
        </if>
        <if test="address != null and address != ''">
            AND address LIKE CONCAT('%', #{address}, '%')
        </if>
        <if test="network != null and network != ''">
            AND network LIKE CONCAT('%', #{network}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="groupCode != null and groupCode != ''">
            AND group_code LIKE CONCAT('%', #{groupCode}, '%')
        </if>
        <if test="accountId != null">
            AND account_id = #{accountId}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id LIKE CONCAT('%', #{userId}, '%')
        </if>
        ORDER BY create_time DESC
        <if test="start != null and length != null">
            LIMIT #{start}, #{length}
        </if>
    </select>

    <!-- 查询符合条件的记录总数 -->
    <select id="countAll" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM dm_account_address
        WHERE 1=1
        <if test="vaultCode != null and vaultCode != ''">
            AND vault_code LIKE CONCAT('%', #{vaultCode}, '%')
        </if>
        <if test="address != null and address != ''">
            AND address LIKE CONCAT('%', #{address}, '%')
        </if>
        <if test="network != null and network != ''">
            AND network LIKE CONCAT('%', #{network}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="groupCode != null and groupCode != ''">
            AND group_code LIKE CONCAT('%', #{groupCode}, '%')
        </if>
        <if test="accountId != null">
            AND account_id = #{accountId}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id LIKE CONCAT('%', #{userId}, '%')
        </if>
    </select>

    <!-- 根据ID查询单个账户地址记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM dm_account_address
        WHERE id = #{id}
    </select>

    <!-- 根据地址查询记录（用于唯一性验证） -->
    <select id="selectByAddress" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM dm_account_address
        WHERE address = #{address}
        LIMIT 1
    </select>

    <!-- 插入新的账户地址记录 -->
    <insert id="insert" parameterType="com.hisun.tms.acm.model.AccountAddressDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dm_account_address (
            vault_code, group_code, account_id, address, network, status,
            acm_ac_no, user_id, qrcode_base64, use_type, create_time, update_time
        ) VALUES (
            #{vaultCode}, #{groupCode}, #{accountId}, #{address}, #{network}, #{status},
            #{acmAcNo}, #{userId}, #{qrcodeBase64}, #{useType}, #{createTime}, #{updateTime}
        )
    </insert>

</mapper>