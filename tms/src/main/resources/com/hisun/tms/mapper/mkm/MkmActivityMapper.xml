<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.mkm.dao.IMkmActivityDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.mkm.model.MkmActivityDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="atv_nm" property="atvNm" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="examine_status" property="examineStatus" jdbcType="CHAR" />
        <result column="status" property="status" jdbcType="CHAR" />
        <result column="total" property="total" jdbcType="INTEGER" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="discount" property="discount" jdbcType="DECIMAL" />
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="remain_num" property="remainNum" jdbcType="INTEGER" />
        <result column="remain_amt" property="remainAmt" jdbcType="DECIMAL" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="crt_user_opr" property="crtUserOpr" jdbcType="VARCHAR" />
        <result column="mdf_user_opr" property="mdfUserOpr" jdbcType="VARCHAR" />
        <result column="aclt_amt" property="acltAmt" jdbcType="DECIMAL" />
        <result column="aclt" property="aclt" jdbcType="INTEGER" />
        <result column="item" property="item" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="coupon_name" property="couponName" jdbcType="VARCHAR" />
        <result column="examine_reson" property="examineReson" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="JoinResultMap" type="com.hisun.tms.mkm.model.MkmActivityDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="atv_nm" property="atvNm" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="examine_status" property="examineStatus" jdbcType="CHAR" />
        <result column="status" property="status" jdbcType="CHAR" />
        <result column="total" property="total" jdbcType="INTEGER" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="amt" property="amt" jdbcType="DECIMAL" />
        <result column="discount" property="discount" jdbcType="DECIMAL" />
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="remain_num" property="remainNum" jdbcType="INTEGER" />
        <result column="remain_amt" property="remainAmt" jdbcType="DECIMAL" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="crt_user_opr" property="crtUserOpr" jdbcType="VARCHAR" />
        <result column="mdf_user_opr" property="mdfUserOpr" jdbcType="VARCHAR" />
        <result column="aclt_amt" property="acltAmt" jdbcType="DECIMAL" />
        <result column="aclt" property="aclt" jdbcType="INTEGER" />
        <result column="item" property="item" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="coupon_name" property="couponName" jdbcType="VARCHAR" />
        <result column="cost_side" property="costSide" jdbcType="VARCHAR" />

        <result column="receive_times" property="receiveTimes" jdbcType="INTEGER" />
        <result column="receive_cycle" property="receiveCycle" jdbcType="VARCHAR" />
        <result column="user_scope" property="userScope" jdbcType="VARCHAR" />
        <result column="start_days" property="startDays" jdbcType="INTEGER" />
        <result column="coupon_val_days" property="couponValDays" jdbcType="INTEGER" />
        <result column="coupon_inval_tm" property="couponInvalTm" jdbcType="TIMESTAMP" />
        <result column="min_amt" property="minAmt" jdbcType="DECIMAL" />
        <result column="max_amt" property="maxAmt" jdbcType="DECIMAL" />
        <result column="examine_reson" property="examineReson" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, atv_nm, mk_tool, examine_status, status, total, total_amt,  amt,
        begin_time, end_time, remain_num, remain_amt, modify_time, create_time, crt_user_opr, 
        mdf_user_opr, aclt_amt, aclt,max_amt ,discount,item,inst_id
    </sql>

    <select id="findByfindMarketActivityDTO" resultMap="JoinResultMap" parameterType="java.util.Map" >
        select
        m.id ,m.atv_nm ,m.mk_tool ,m.examine_status ,m.status ,m.total ,m.total_amt ,m.amt ,m.coupon_name ,m.discount ,m.begin_time ,m.end_time ,m.remain_num ,m.remain_amt
        ,m.modify_time ,m.create_time ,m.crt_user_opr,m.mdf_user_opr ,m.aclt_amt ,m.aclt ,m.item ,m.inst_id, r.receive_times ,r.receive_cycle
        ,r.user_scope,r.start_days,r.coupon_val_days,r.coupon_inval_tm,r.min_amt,r.max_amt,m.cost_side ,m.examine_reson
        from mkm_activity m left join mkm_atv_rule r on m.id = r.atv_id
        where 1 = 1
        <if test=" id !=null and  id != ''">
           and  m.id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="atvNm != null and atvNm != ''">
            and m.atv_nm = #{atvNm,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and m.status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="examimeStatus != null and examimeStatus != ''">
            and examine_status = #{examimeStatus,jdbcType=VARCHAR}
        </if>
        <if test="noExmineStatus != null and noExmineStatus != ''">
            and examine_status != #{noExmineStatus,jdbcType=VARCHAR}
        </if>

        <if test="beginTime != null and beginTime != ''">
            and DATE_FORMAT( m.create_time, '%Y%m%d') >= #{beginTime,jdbcType=VARCHAR}
        </if>
        <if test="endTime != null and endTime != ''">
        <![CDATA[ and DATE_FORMAT( m.create_time, '%Y%m%d') <= #{endTime,jdbcType=VARCHAR}]]>
        </if>
         ORDER BY m.modify_time DESC
        <if test="pageBegin != null ">
        limit #{pageBegin,jdbcType=INTEGER},#{pageEnd,jdbcType=INTEGER}
        </if>
    </select>

    <select id="counTotal" resultType="java.lang.Integer" parameterType="java.util.Map" >
        select
        count(*)
        from mkm_activity
        where 1 = 1
        <if test=" id !=null and  id != ''">
            and  id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="atvNm != null and atvNm != '' ">
            and atv_nm = #{atvNm,jdbcType=VARCHAR}
        </if>

        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>

        <if test="examimeStatus != null and examimeStatus != ''">
            and examine_status = #{examimeStatus,jdbcType=VARCHAR}
        </if>

        <if test="noExmineStatus != null and noExmineStatus != ''">
            and examine_status != #{noExmineStatus,jdbcType=VARCHAR}
        </if>
        <if test="beginTime != null and beginTime != ''">
            and DATE_FORMAT( create_time, '%Y%m%d') >= #{beginTime,jdbcType=VARCHAR}
        </if>
        <if test="endTime != null and endTime != ''">
            <![CDATA[ and DATE_FORMAT( create_time, '%Y%m%d') <= #{endTime,jdbcType=VARCHAR}]]>
        </if>
    </select>
    <select id="get" resultMap="JoinResultMap" parameterType="java.lang.String" >
        select 
        m.id ,m.atv_nm ,m.mk_tool ,m.examine_status ,m.status ,m.total ,m.total_amt ,m.amt ,m.discount ,m.coupon_name ,m.begin_time ,m.end_time ,m.remain_num ,m.remain_amt
        ,m.modify_time ,m.create_time ,m.crt_user_opr,m.mdf_user_opr ,m.aclt_amt ,m.aclt ,m.item ,m.inst_id, r.receive_times ,r.receive_cycle
        ,r.user_scope,r.start_days,r.coupon_val_days,r.coupon_inval_tm,r.min_amt,r.max_amt
        from mkm_activity m left join mkm_atv_rule r on m.id = r.atv_id
        where m.id = #{id,jdbcType=VARCHAR}
    </select>



    <select id="getValid" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />

        from mkm_activity
        where id = #{id,jdbcType=VARCHAR} and status = '00' and examine_status = '02'
        <![CDATA[   and begin_time<now() and  now()<end_time ]]>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from mkm_activity
        where id = #{id,jdbcType=VARCHAR} and status != '00'
    </delete>


    <update id="update" parameterType="com.hisun.tms.mkm.model.MkmActivityDO" >
        update mkm_activity
        <set >
            <if test="atvNm != null" >
                atv_nm = #{atvNm,jdbcType=VARCHAR},
            </if>
            <if test="mkTool != null" >
                mk_tool = #{mkTool,jdbcType=VARCHAR},
            </if>
            <if test="examineStatus != null" >
                examine_status = #{examineStatus,jdbcType=CHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=CHAR},
            </if>
            <if test="total != null" >
                total = #{total,jdbcType=INTEGER},
            </if>
            <if test="totalAmt != null" >
                total_amt = #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="amt != null" >
                amt = #{amt,jdbcType=DECIMAL},
            </if>
            <if test="beginTime != null" >
                begin_time = #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remainNum != null" >
                remain_num = #{remainNum,jdbcType=INTEGER},
            </if>
            <if test="remainAmt != null" >
                remain_amt = #{remainAmt,jdbcType=DECIMAL},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="crtUserOpr != null" >
                crt_user_opr = #{crtUserOpr,jdbcType=VARCHAR},
            </if>
            <if test="mdfUserOpr != null" >
                mdf_user_opr = #{mdfUserOpr,jdbcType=VARCHAR},
            </if>

            <if test="acltAmt != null" >
                aclt_amt = #{acltAmt,jdbcType=DECIMAL},
            </if>
            <if test="aclt != null" >
                aclt = #{aclt,jdbcType=INTEGER},
            </if>
            <if test="discount != null" >
                discount = #{discount,jdbcType=DECIMAL},
            </if>
            <if test="instId != null" >
                inst_id = #{instId,jdbcType=VARCHAR},
            </if>
            <if test="examineReson != null" >
                examine_reson = #{examineReson,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 查询审核通过并且在活动时间内的数据 -->
    <select id="getEventsDuringDate" resultMap="JoinResultMap">
    SELECT id,atv_nm,cost_side,mk_tool FROM mkm_activity
    WHERE examine_status = '02' 
  	<![CDATA[AND begin_time <= DATE_FORMAT(NOW(),'%Y-%m-%d') AND end_time >= DATE_FORMAT(NOW(),'%Y-%m-%d')]]> 
    </select>
    
    <!-- 返回海币营销费用 -->
	<select id="getHBCount" resultType="string" parameterType="com.hisun.tms.mkm.model.MkmActivityDO">
	    SELECT 
	  	  SUM(COUNT)
		FROM
	  	  mkm_sea_ccy_seq
		WHERE atv_id = #{atvId,jdbcType=VARCHAR}
		  AND TYPE = '01' 
		  AND STATUS = '1' 
		  AND DATE_FORMAT(modify_time,'%Y%m') = DATE_FORMAT(NOW(),'%Y%m')
    </select>

	<!-- 返回用券营销费用 计算流水中消费金额-退款金额-撤销金额+退款撤销金额-->
	<select id="getVoucherCount" resultType="string" parameterType="com.hisun.tms.mkm.model.MkmActivityDO">

		select (b.consumeAmt - b.refundAmt - b.revokeCAmt + b.revokeRAmt) as countAmt from (
          select
           (case when consumeAmt is null then 0 else consumeAmt end) as consumeAmt ,
           (case when refundAmt is null then 0 else refundAmt end ) as refundAmt ,
           (case when revokeCAmt is null then 0 else revokeCAmt end ) as revokeCAmt ,
           (case when revokeRAmt is null then 0 else revokeRAmt end )  as revokeRAmt from (
             select
                   (select sum(amt) from mkm_coupon_seq where status in ('1','3','4','5') and type = '02' and DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(NOW(), '%Y%m') ) as consumeAmt ,
                   (select sum(amt)  from mkm_coupon_seq where status in ('1','3') and type = '06' and DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(NOW(), '%Y%m') ) as refundAmt,
                   (select sum(amt)   from mkm_coupon_seq where status = '1' and type = '09' and DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(NOW(), '%Y%m') ) as revokeCAmt,
                   (select sum(amt)   from mkm_coupon_seq where status = '1' and type = '07' and DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(NOW(), '%Y%m') ) as revokeRAmt
             from  mkm_coupon_seq limit 0 ,1
           ) as a
        ) as b
    </select>

</mapper>