<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.urm.dao.MercItfInfoDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.urm.model.MercItfInfoDO" >
        <id column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <id column="ITF_NM" property="itfNm" jdbcType="VARCHAR" />
        <id column="VERSION" property="version" jdbcType="VARCHAR" />
        <result column="VERIFY_TYPE" property="verifyType" jdbcType="VARCHAR" />
        <result column="SECRET_KEY" property="secretKey" jdbcType="VARCHAR" />
        <result column="STS" property="sts" jdbcType="VARCHAR" />
        <result column="MAC_ITEM" property="macItem" jdbcType="VARCHAR" />
        <result column="RSP_MAC_ITEM" property="rspMacItem" jdbcType="VARCHAR" />
        <result column="LAST_UPD_OPR" property="lastUpdOpr" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        USER_ID, ITF_NM, VERSION, VERIFY_TYPE, SECRET_KEY, STS, MAC_ITEM, RSP_MAC_ITEM, LAST_UPD_OPR
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from urm_cpr_itf_auth
        where USER_ID = #{userId,jdbcType=VARCHAR}
          and ITF_NM = #{itfNm,jdbcType=VARCHAR}
          and VERSION = #{version,jdbcType=VARCHAR}
    </select>
    <select id="checkComercReg" resultType="java.lang.Integer" parameterType="java.lang.String" >
        select
        count(*)
        from urm_cpr_ext_inf
        where 1=1
        <if test='comercReg != null and comercReg != "" '>
            and COMERC_REG = #{comercReg,jdbcType=VARCHAR}
        </if>
        <if test='merId != null and merId != "" '>
            and USER_ID != #{merId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="checkMercShortName" resultType="java.lang.Integer" parameterType="java.lang.String" >
        select
        count(*)
        from urm_cpr_ext_inf
        where 1=1
        <if test='mercShortName != null and mercShortName != "" '>
            and MERC_SHORT_NAME = #{mercShortName,jdbcType=VARCHAR}
        </if>
        <if test='merId != null and merId != "" '>
            and USER_ID != #{merId,jdbcType=VARCHAR}
        </if>
    </select>

    <delete id="delete" parameterType="map" >
        delete from urm_cpr_itf_auth
        where USER_ID = #{userId,jdbcType=VARCHAR}
          and ITF_NM = #{itfNm,jdbcType=VARCHAR}
          and VERSION = #{version,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.tms.urm.model.MercItfInfoDO" >
        insert into urm_cpr_itf_auth
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="itfNm != null" >
                ITF_NM,
            </if>
            <if test="version != null" >
                VERSION,
            </if>
            <if test="verifyType != null" >
                VERIFY_TYPE,
            </if>
            <if test="secretKey != null" >
                SECRET_KEY,
            </if>
            <if test="sts != null" >
                STS,
            </if>
            <if test="macItem != null" >
                MAC_ITEM,
            </if>
            <if test="rspMacItem != null" >
                RSP_MAC_ITEM,
            </if>
            <if test="lastUpdOpr != null" >
                LAST_UPD_OPR,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="itfNm != null" >
                #{itfNm,jdbcType=VARCHAR},
            </if>
            <if test="version != null" >
                #{version,jdbcType=VARCHAR},
            </if>
            <if test="verifyType != null" >
                #{verifyType,jdbcType=VARCHAR},
            </if>
            <if test="secretKey != null" >
                #{secretKey,jdbcType=VARCHAR},
            </if>
            <if test="sts != null" >
                #{sts,jdbcType=VARCHAR},
            </if>
            <if test="macItem != null" >
                #{macItem,jdbcType=VARCHAR},
            </if>
            <if test="rspMacItem != null" >
                #{rspMacItem,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdOpr != null" >
                #{lastUpdOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateMercItfInfo" parameterType="com.hisun.tms.urm.model.MercItfInfoDO" >
        update urm_cpr_itf_auth
        <set >
            <if test="secretKey != null" >
                SECRET_KEY = #{secretKey,jdbcType=VARCHAR},
            </if>
            <if test="sts != null" >
                STS = #{sts,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdOpr != null" >
                LAST_UPD_OPR = #{lastUpdOpr,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            USER_ID = #{userId,jdbcType=VARCHAR}
                and ITF_NM = #{itfNm,jdbcType=VARCHAR}
        </where>
    </update>
    <select id="findByUserId" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from urm_cpr_itf_auth
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByUserIdAndItfNm" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from urm_cpr_itf_auth
        where USER_ID = #{userId,jdbcType=VARCHAR}
          and ITF_NM = #{itfNm,jdbcType=VARCHAR}
    </select>
</mapper>