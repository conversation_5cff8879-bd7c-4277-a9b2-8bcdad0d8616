<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.inv.dao.IInvProInfoDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.inv.model.InvProInfoDO" >
        <id column="pro_id" property="proId" jdbcType="VARCHAR" />
        <result column="pro_name" property="proName" jdbcType="VARCHAR" />
        <result column="pro_type" property="proType" jdbcType="CHAR" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="collect_amt" property="collectAmt" jdbcType="DECIMAL" />
        <result column="collect_cnt" property="collectCnt" jdbcType="INTEGER" />
        <result column="rate" property="rate" jdbcType="DECIMAL" />
        <result column="inv_term" property="invTerm" jdbcType="INTEGER" />
        <result column="pro_eff_tm" property="proEffTm" jdbcType="TIMESTAMP" />
        <result column="pro_exp_tm" property="proExpTm" jdbcType="TIMESTAMP" />
        <result column="pro_desc" property="proDesc" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="risk_desc" property="riskDesc" jdbcType="VARCHAR" />
        <result column="rules_desc" property="rulesDesc" jdbcType="VARCHAR" />
        <result column="clo_per_begin" property="cloPerBegin" jdbcType="TIMESTAMP" />
        <result column="clo_per_end" property="cloPerEnd" jdbcType="TIMESTAMP" />
        <result column="subscript_rate" property="subscriptRate" jdbcType="DECIMAL" />
        <result column="clo_per_rate" property="cloPerRate" jdbcType="DECIMAL" />
        <result column="invest_amt" property="investAmt" jdbcType="DECIMAL" />
        <result column="earn_tm" property="earnTm" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        pro_id, pro_name, pro_type, total_amt, collect_amt, collect_cnt, rate, inv_term, 
        pro_eff_tm, pro_exp_tm, pro_desc, create_time, modify_time, tm_smp, risk_desc, 
        rules_desc, clo_per_begin, clo_per_end, subscript_rate, clo_per_rate, invest_amt, 
        earn_tm
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from inv_pro_info
        where pro_id = #{proId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from inv_pro_info
        where pro_id = #{proId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.tms.inv.model.InvProInfoDO" >
        insert into inv_pro_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="proId != null" >
                pro_id,
            </if>
            <if test="proName != null" >
                pro_name,
            </if>
            <if test="proType != null" >
                pro_type,
            </if>
            <if test="totalAmt != null" >
                total_amt,
            </if>
            <if test="collectAmt != null" >
                collect_amt,
            </if>
            <if test="collectCnt != null" >
                collect_cnt,
            </if>
            <if test="rate != null" >
                rate,
            </if>
            <if test="invTerm != null" >
                inv_term,
            </if>
            <if test="proEffTm != null" >
                pro_eff_tm,
            </if>
            <if test="proExpTm != null" >
                pro_exp_tm,
            </if>
            <if test="proDesc != null" >
                pro_desc,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
            <if test="riskDesc != null" >
                risk_desc,
            </if>
            <if test="rulesDesc != null" >
                rules_desc,
            </if>
            <if test="cloPerBegin != null" >
                clo_per_begin,
            </if>
            <if test="cloPerEnd != null" >
                clo_per_end,
            </if>
            <if test="subscriptRate != null" >
                subscript_rate,
            </if>
            <if test="cloPerRate != null" >
                clo_per_rate,
            </if>
            <if test="investAmt != null" >
                invest_amt,
            </if>
            <if test="earnTm != null" >
                earn_tm,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="proId != null" >
                #{proId,jdbcType=VARCHAR},
            </if>
            <if test="proName != null" >
                #{proName,jdbcType=VARCHAR},
            </if>
            <if test="proType != null" >
                #{proType,jdbcType=CHAR},
            </if>
            <if test="totalAmt != null" >
                #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="collectAmt != null" >
                #{collectAmt,jdbcType=DECIMAL},
            </if>
            <if test="collectCnt != null" >
                #{collectCnt,jdbcType=INTEGER},
            </if>
            <if test="rate != null" >
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="invTerm != null" >
                #{invTerm,jdbcType=INTEGER},
            </if>
            <if test="proEffTm != null" >
                #{proEffTm,jdbcType=TIMESTAMP},
            </if>
            <if test="proExpTm != null" >
                #{proExpTm,jdbcType=TIMESTAMP},
            </if>
            <if test="proDesc != null" >
                #{proDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="riskDesc != null" >
                #{riskDesc,jdbcType=VARCHAR},
            </if>
            <if test="rulesDesc != null" >
                #{rulesDesc,jdbcType=VARCHAR},
            </if>
            <if test="cloPerBegin != null" >
                #{cloPerBegin,jdbcType=TIMESTAMP},
            </if>
            <if test="cloPerEnd != null" >
                #{cloPerEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="subscriptRate != null" >
                #{subscriptRate,jdbcType=DECIMAL},
            </if>
            <if test="cloPerRate != null" >
                #{cloPerRate,jdbcType=DECIMAL},
            </if>
            <if test="investAmt != null" >
                #{investAmt,jdbcType=DECIMAL},
            </if>
            <if test="earnTm != null" >
                #{earnTm,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.tms.inv.model.InvProInfoDO" >
        update inv_pro_info
        <set >
            <if test="proName != null" >
                pro_name = #{proName,jdbcType=VARCHAR},
            </if>
            <if test="proType != null" >
                pro_type = #{proType,jdbcType=CHAR},
            </if>
            <if test="totalAmt != null" >
                total_amt = #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="collectAmt != null" >
                collect_amt = #{collectAmt,jdbcType=DECIMAL},
            </if>
            <if test="collectCnt != null" >
                collect_cnt = #{collectCnt,jdbcType=INTEGER},
            </if>
            <if test="rate != null" >
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="invTerm != null" >
                inv_term = #{invTerm,jdbcType=INTEGER},
            </if>
            <if test="proEffTm != null" >
                pro_eff_tm = #{proEffTm,jdbcType=TIMESTAMP},
            </if>
            <if test="proExpTm != null" >
                pro_exp_tm = #{proExpTm,jdbcType=TIMESTAMP},
            </if>
            <if test="proDesc != null" >
                pro_desc = #{proDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="riskDesc != null" >
                risk_desc = #{riskDesc,jdbcType=VARCHAR},
            </if>
            <if test="rulesDesc != null" >
                rules_desc = #{rulesDesc,jdbcType=VARCHAR},
            </if>
            <if test="cloPerBegin != null" >
                clo_per_begin = #{cloPerBegin,jdbcType=TIMESTAMP},
            </if>
            <if test="cloPerEnd != null" >
                clo_per_end = #{cloPerEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="subscriptRate != null" >
                subscript_rate = #{subscriptRate,jdbcType=DECIMAL},
            </if>
            <if test="cloPerRate != null" >
                clo_per_rate = #{cloPerRate,jdbcType=DECIMAL},
            </if>
            <if test="investAmt != null" >
                invest_amt = #{investAmt,jdbcType=DECIMAL},
            </if>
            <if test="earnTm != null" >
                earn_tm = #{earnTm,jdbcType=TIMESTAMP},
            </if>
        </set>
        where pro_id = #{proId,jdbcType=VARCHAR}
    </update>
</mapper>