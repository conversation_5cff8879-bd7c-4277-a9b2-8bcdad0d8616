<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.mkm.dao.IRequisitionDetailsDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.mkm.model.RequisitionDetailsDO" >
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="atv_nm" property="atvNm" jdbcType="VARCHAR" />
        <result column="mkmType" property="mkmType" jdbcType="VARCHAR" />
        <result column="mk_tool" property="mkTool" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="release_tm" property="releaseTm" jdbcType="TIME" />
        <result column="release_dt" property="releaseDt" jdbcType="DATE" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="count" property="count" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectRequisitionByMblNoAndBegDt" resultMap="BaseResultMap">
        select result.* from (
         (select
        c.atv_id, m.atv_nm ,1 as mkmType, c.mk_tool,c.mobile, c.release_dt, c.release_tm, c.status, 1 as count
        from
        mkm_coupon_detail c left join mkm_activity m on c.atv_id = m.id
        where
        <if test="mobile != null and mobile != ''" >
            c.mobile = #{mobile,jdbcType=VARCHAR} and
        </if>
        <if test="id != null and id != ''" >
            c.atv_id = #{id,jdbcType=VARCHAR} and
        </if>
        <if test="begDt != null" >
            c.release_dt > #{begDt,jdbcType=DATE} and
        </if>
        <if test="endDt != null" >
            <![CDATA[    c.release_dt < #{endDt,jdbcType=DATE} and ]]>
        </if>
        1 = 1
        )
        union
        (    select
        s.atv_id,m.atv_nm , 2 as mkmType,s.mk_tool, s.mobile, s.release_dt, s.release_tm, s.status, s.count
        from
        mkm_sea_ccy_seq s left join  mkm_activity m on s.atv_id = m.id
        where
        type = '01' and s.status = '1' and
        <if test="mobile != null and mobile != ''" >
            s.mobile = #{mobile,jdbcType=VARCHAR} and
        </if>
        <if test="id != null and id !='' " >
            s.atv_id = #{id,jdbcType=VARCHAR} and
        </if>
        <if test="begDt != null" >
            s.release_dt > #{begDt,jdbcType=DATE} and
        </if>
        <if test="endDt != null" >
            <![CDATA[s.release_dt < #{endDt,jdbcType=DATE} and ]]>
        </if>
        1 = 1
        )) as result
        order by release_dt desc
        limit #{pageBegin,jdbcType=INTEGER},#{pageEnd,jdbcType=INTEGER}
    </select>
    <select id="selectRequisitionByMblNoAndBegDtTotal" resultType="java.lang.Integer">
        select count(*) from (
         (select
        c.atv_id, m.atv_nm ,1 as mkmType, c.mk_tool,c.mobile, c.release_dt, c.release_tm, c.status, 1 as count
        from
        mkm_coupon_detail c left join mkm_activity m on c.atv_id = m.id
        where
        <if test="mobile != null and mobile != '' " >
            c.mobile = #{mobile,jdbcType=VARCHAR} and
        </if>
        <if test="id != null and id !='' " >
            c.atv_id = #{id,jdbcType=VARCHAR} and
        </if>
        <if test="begDt != null" >
            c.release_dt > #{begDt,jdbcType=DATE} and
        </if>
        <if test="endDt != null" >
            <![CDATA[    c.release_dt < #{endDt,jdbcType=DATE} and ]]>
        </if>
        1 = 1
        )
        union
        (    select
        s.atv_id,m.atv_nm , 2 as mkmType,s.mk_tool, s.mobile, s.release_dt, s.release_tm, s.status, s.count
        from
        mkm_sea_ccy_seq s left join  mkm_activity m on s.atv_id = m.id
        where
        type = '01' and s.status = '1' and
        <if test="mobile != null and mobile != '' "  >
            s.mobile = #{mobile,jdbcType=VARCHAR} and
        </if>
        <if test="id != null and id != '' " >
            s.atv_id = #{id,jdbcType=VARCHAR} and
        </if>
        <if test="begDt != null" >
            s.release_dt > #{begDt,jdbcType=DATE} and
        </if>
        <if test="endDt != null" >
            <![CDATA[s.release_dt < #{endDt,jdbcType=DATE} and ]]>
        </if>
        1 = 1
        )) as result
    </select>


</mapper>