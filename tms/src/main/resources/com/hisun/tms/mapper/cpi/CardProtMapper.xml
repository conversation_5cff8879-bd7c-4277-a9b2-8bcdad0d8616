<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpi.ICardProtDao" >

    <!--用户绑定的银行卡列表显示信息-->
    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.CardProtDO" >
        <result column="AGR_NO" property="agrNo" jdbcType="VARCHAR" />
        <result column="AGR_EFF_FLG" property="agrEffFlg" jdbcType="CHAR" />
        <result column="SIGN_DT" property="signDt" jdbcType="DATE" />
        <result column="SIGN_TM" property="signTm" jdbcType="TIME" />
        <result column="UNSIGN_DT" property="unsignDt" jdbcType="DATE" />
        <result column="UNSIGN_TM" property="unsignTm" jdbcType="TIME" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="BNK_PSN_FLG" property="bnkPsnFlg" jdbcType="CHAR" />
        <result column="SIGN_AGRNO" property="signAgrno" jdbcType="VARCHAR" />
        <result column="AGR_DIRECT" property="agrDirect" jdbcType="CHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
    </resultMap>

    <!--用户绑定的银行卡详细信息-->
    <resultMap id="ResultDetailMap" type="com.hisun.tms.cpt.model.CardProtDO" >
        <result column="AGR_NO" property="agrNo" jdbcType="VARCHAR" />
        <result column="AGR_EFF_FLG" property="agrEffFlg" jdbcType="CHAR" />
        <result column="SIGN_DT" property="signDt" jdbcType="DATE" />
        <result column="SIGN_TM" property="signTm" jdbcType="TIME" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="BNK_PSN_FLG" property="bnkPsnFlg" jdbcType="CHAR" />
        <result column="AGR_DIRECT" property="agrDirect" jdbcType="CHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="ID_TYP" property="idTyp" jdbcType="VARCHAR" />
        <result column="ID_NO_ENC" property="idNo" jdbcType="VARCHAR" />
        <result column="CRD_NO_ENC" property="crdNo" jdbcType="VARCHAR" />
        <result column="CRD_USR_NM" property="crdUsrNm" jdbcType="VARCHAR" />
        <result column="CRD_CVV2_ENC" property="crdCvv2" jdbcType="VARCHAR" />
        <result column="CRD_EXP_DT_ENC" property="crdExpDt" jdbcType="VARCHAR" />
    </resultMap>

    <!--银行签约或绑卡流水-->
    <resultMap id="CardProtJrnDOMap" type="com.hisun.tms.cpt.model.CardProtJrnDO" >
        <id column="JRN_NO" property="jrnNo" jdbcType="VARCHAR" />
        <result column="TX_DT" property="txDt" jdbcType="DATE" />
        <result column="TX_TM" property="txTm" jdbcType="TIME" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="USER_NM" property="userNm" jdbcType="VARCHAR" />
        <result column="ID_TYP" property="idTyp" jdbcType="CHAR" />
        <result column="ID_NO_ENC" property="idNoEnc" jdbcType="VARCHAR" />
        <result column="CRD_NO_ENC" property="crdNoEnc" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="BND_FLG" property="bndFlg" jdbcType="CHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_USR_NM" property="crdUsrNm" jdbcType="VARCHAR" />
        <result column="CRD_CVV2_ENC" property="crdCvv2Enc" jdbcType="VARCHAR" />
        <result column="CRD_EXP_DT_ENC" property="crdExpDtEnc" jdbcType="VARCHAR" />
        <result column="SIGN_AGRNO" property="signAgrno" jdbcType="VARCHAR" />
        <result column="ORG_JRN_NO" property="orgJrnNo" jdbcType="VARCHAR" />
        <result column="ORG_RSP_CD" property="orgRspCd" jdbcType="VARCHAR" />
        <result column="ORG_RSP_MSG" property="orgRspMsg" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <!--插入银行卡签约或绑卡流水-->
    <insert id="insertCardProtJrn" parameterType="com.hisun.tms.cpt.model.CardProtJrnDO" >
        insert into cpi_card_prot_jrn
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                JRN_NO,
            </if>
            <if test="txDt != null" >
                TX_DT,
            </if>
            <if test="txTm != null" >
                TX_TM,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="userNm != null" >
                USER_NM,
            </if>
            <if test="idTyp != null" >
                ID_TYP,
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC,
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="bndFlg != null" >
                BND_FLG,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM,
            </if>
            <if test="crdCvv2Enc != null" >
                CRD_CVV2_ENC,
            </if>
            <if test="crdExpDtEnc != null" >
                CRD_EXP_DT_ENC,
            </if>
            <if test="signAgrno != null" >
                SIGN_AGRNO,
            </if>
            <if test="orgJrnNo != null" >
                ORG_JRN_NO,
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD,
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="txDt != null" >
                #{txDt,jdbcType=DATE},
            </if>
            <if test="txTm != null" >
                #{txTm,jdbcType=TIME},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userNm != null" >
                #{userNm,jdbcType=VARCHAR},
            </if>
            <if test="idTyp != null" >
                #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdNoEnc != null" >
                #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="bndFlg != null" >
                #{bndFlg,jdbcType=CHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdUsrNm != null" >
                #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="crdCvv2Enc != null" >
                #{crdCvv2Enc,jdbcType=VARCHAR},
            </if>
            <if test="crdExpDtEnc != null" >
                #{crdExpDtEnc,jdbcType=VARCHAR},
            </if>
            <if test="signAgrno != null" >
                #{signAgrno,jdbcType=VARCHAR},
            </if>
            <if test="orgJrnNo != null" >
                #{orgJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgRspCd != null" >
                #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!--根据条件，查询用户的银行卡信息-->
    <select id="getCardProtList" resultMap="BaseResultMap">
        select
            AGR_NO, AGR_EFF_FLG, SIGN_DT, SIGN_TM, UNSIGN_DT, UNSIGN_TM, USER_ID, MBL_NO,
            BNK_PSN_FLG, SIGN_AGRNO, AGR_DIRECT, CRD_AC_TYP, RUT_CORP_ORG, CRD_CORP_ORG
        from cpi_card_prot
        where 1 = 1
        <if test="mblNo != null and mblNo != ''" >
            and MBL_NO = #{mblNo,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="crdAcTyp != null and crdAcTyp != ''" >
            and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
        </if>
        <if test="agrEffFlg != null and agrEffFlg != ''" >
            and AGR_EFF_FLG = #{agrEffFlg,jdbcType=CHAR}
        </if>
        <if test="bnkPsnFlg != null and bnkPsnFlg != ''" >
            and BNK_PSN_FLG = #{bnkPsnFlg,jdbcType=CHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and SIGN_DT >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and SIGN_DT <= #{endDate,jdbcType=DATE} ]]>
        </if>
        order by SIGN_DT desc, SIGN_TM desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--根据条件，查询退款订单总笔数-->
    <select id="getCardProtListTotNum" resultType="java.lang.Integer">
        select count(1)
        from cpi_card_prot
        where 1 = 1
        <if test="mblNo != null and mblNo != ''" >
            and MBL_NO = #{mblNo,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''" >
            and USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="crdAcTyp != null and crdAcTyp != ''" >
            and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
        </if>
        <if test="agrEffFlg != null and agrEffFlg != ''" >
            and AGR_EFF_FLG = #{agrEffFlg,jdbcType=CHAR}
        </if>
        <if test="bnkPsnFlg != null and bnkPsnFlg != ''" >
            and BNK_PSN_FLG = #{bnkPsnFlg,jdbcType=CHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and SIGN_DT >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and SIGN_DT <= #{endDate,jdbcType=DATE} ]]>
        </if>
    </select>

    <!--根据内部协议号，查询用户的银行卡信息-->
    <select id="getCardProtInfo" resultMap="ResultDetailMap">
        select
            AGR_NO, AGR_EFF_FLG, SIGN_DT, SIGN_TM, USER_ID,MBL_NO, BNK_PSN_FLG, AGR_DIRECT,
            CRD_AC_TYP, RUT_CORP_ORG, CRD_CORP_ORG, ID_TYP, ID_NO_ENC, CRD_NO_ENC,
            CRD_USR_NM, CRD_CVV2_ENC, CRD_EXP_DT_ENC
        from cpi_card_prot
        where 1 = 1
        <if test="agrNo != null and agrNo != ''" >
            and AGR_NO = #{agrNo,jdbcType=VARCHAR}
        </if>
    </select>

    <!--根据内部协议号，更新用户签约信息为失效-->
    <update id="updateCardProtDO" >
        update cpi_card_prot
        set AGR_EFF_FLG = ‘N’,
            UNSIGN_DT = #{unsignDt, jdbcType=DATE},
            UNSIGN_TM = #{unsignTm, jdbcType=TIME}
        where AGR_NO = #{agrNo, jdbcType=VARCHAR}
            and AGR_EFF_FLG='Y'
    </update>

</mapper>