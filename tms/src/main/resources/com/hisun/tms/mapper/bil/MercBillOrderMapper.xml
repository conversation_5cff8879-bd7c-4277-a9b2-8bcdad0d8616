<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.bil.dao.IMercBillOrderDao">

    <select id="tradeGroupByTxBustype" resultType="java.util.Map">
        select tx_type ,bus_type,
        COUNT(*) AS total_cnt,
        SUM(case when order_status = 'S' then 1 else 0 end) as succ_cnt,
        SUM(case when <![CDATA[order_status <> 'S']]> then 1 else 0 end) as fail_cnt
        from bil_merc_order
        where create_time BETWEEN  #{startTime} and #{endTime}
        group by tx_type,bus_type
    </select>

    <select id="hallRechargeStatisticsByMonth" resultType="java.util.Map">
        SELECT
            merc_id,merc_name,
            sum( order_amt ) sum_Amt,
            count( order_no ) sum_Count
        FROM
            bil_merc_order
        WHERE
            bus_type = #{busType}
            AND order_status = #{orderStatus}
            AND date_format( tx_tm, '%Y%m' ) = #{monthStr}
        GROUP BY
            merc_id,merc_name;
    </select>

    <select id="hallWithdrawStatisticsByMonth" resultType="java.util.Map">
        SELECT merc_id,merc_name,sum(sum_amt) total_amt,
			sum(sum_count) total_count,sum(sum_fee) total_fee,
			sum(count_fee) total_fee_count
			from
			 (SELECT
              user_id as merc_id,merc_name,
              sum( order_amt ) sum_amt,
              count( order_no ) sum_count,
			  sum( fee ) sum_fee,
			  count( fee ) count_fee
            FROM
                bil_merc_order
             WHERE
              bus_type = '0405'
              AND order_status = #{orderStatus}
              AND date_format( tx_tm, '%Y%m' ) = #{monthStr}
            GROUP BY
            user_id,merc_name
			union
			SELECT
            merc_id,merc_name,
            sum( order_amt ) sum_Amt,
            count( order_no ) sum_Count,
		    		sum( fee ) sum_fee,
		    		count( fee ) count_fee
            FROM
            bil_merc_order
            WHERE
            bus_type = '0406'
            AND order_status = #{orderStatus}
            AND date_format( tx_tm, '%Y%m' ) = #{monthStr}
            GROUP BY
            merc_id,merc_name ) t GROUP BY merc_id,merc_name;
    </select>

</mapper>