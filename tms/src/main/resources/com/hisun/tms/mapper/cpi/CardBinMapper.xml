<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpi.ICardBinDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.CardBinDO" >
        <id column="BIN_ID" property="binId" jdbcType="VARCHAR" />
        <result column="CRD_BIN" property="crdBin" jdbcType="VARCHAR" />
        <result column="CAP_CORG" property="capCorg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_LTH" property="crdLth" jdbcType="INTEGER" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="ascription_area" property="ascriptionArea" jdbcType="VARCHAR" />
        <result column="belonging_state" property="belongingState" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        BIN_ID, CRD_BIN, CAP_CORG, CRD_AC_TYP, CRD_LTH, OPR_ID, ascription_area, belonging_state
    </sql>

    <select id="getByCrdBin" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpi_card_bin
        where CRD_BIN = #{crdBin,jdbcType=VARCHAR}
    </select>

    <insert id="insert" parameterType="com.hisun.tms.cpt.model.CardBinDO" >
        insert into cpi_card_bin
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="binId != null" >
                BIN_ID,
            </if>
            <if test="crdBin != null" >
                CRD_BIN,
            </if>
            <if test="capCorg != null" >
                CAP_CORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdLth != null" >
                CRD_LTH,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="belongingState != null" >
                belonging_state,
            </if>
            <if test="ascriptionArea != null" >
                ascription_area,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="binId != null" >
                #{binId,jdbcType=VARCHAR},
            </if>
            <if test="crdBin != null" >
                #{crdBin,jdbcType=VARCHAR},
            </if>
            <if test="capCorg != null" >
                #{capCorg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdLth != null" >
                #{crdLth,jdbcType=INTEGER},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="belongingState != null" >
                #{belongingState,jdbcType=VARCHAR},
            </if>
            <if test="ascriptionArea != null" >
                #{ascriptionArea,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.tms.cpt.model.CardBinDO" >
        update cpi_card_bin
        <set >
            <if test="crdBin != null" >
                CRD_BIN = #{crdBin,jdbcType=VARCHAR},
            </if>
            <if test="capCorg != null" >
                CAP_CORG = #{capCorg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdLth != null" >
                CRD_LTH = #{crdLth,jdbcType=INTEGER},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="belongingState != null" >
                belonging_state = #{belongingState,jdbcType=VARCHAR},
            </if>
            <if test="ascriptionArea != null" >
                ascription_area = #{ascriptionArea,jdbcType=VARCHAR},
            </if>
        </set>
        where BIN_ID = #{binId,jdbcType=VARCHAR}
    </update>

    <!--资金流入模块，分页查询卡bin信息列表-->
    <select id="getCardBinList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpi_card_bin
        where 1 = 1
        <if test="crdBin != null and crdBin != ''" >
            and (locate(CRD_BIN, #{crdBin,jdbcType=VARCHAR}) &gt; 0
                  or locate(#{crdBin,jdbcType=VARCHAR},CRD_BIN) &gt; 0)
        </if>
        <if test="capCorg != null and capCorg != ''" >
            and CAP_CORG = #{capCorg,jdbcType=VARCHAR}
        </if>
        <if test="crdAcTyp != null and crdAcTyp != ''" >
            and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
        </if>
        <if test="crdLth != null and crdLth != ''" >
            and CRD_LTH = #{crdLth,jdbcType=INTEGER}
        </if>
        <if test="belongingState != null and belongingState != ''" >
            and belonging_state = #{belongingState,jdbcType=INTEGER}
        </if>
        <if test="ascriptionArea != null and ascriptionArea != ''" >
            and ascription_area = #{ascriptionArea,jdbcType=INTEGER}
        </if>
        order by CAP_CORG, CRD_BIN, CRD_AC_TYP
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--资金流入模块，查询卡bin信息列表总笔数-->
    <select id="getCardBinListTotNum" resultType="java.lang.Integer">
        select count(1)
        from cpi_card_bin
        where 1 = 1
        <if test="crdBin != null and crdBin != ''" >
            and (locate(CRD_BIN, #{crdBin,jdbcType=VARCHAR}) &gt; 0
                  or locate(#{crdBin,jdbcType=VARCHAR},CRD_BIN) &gt; 0)
        </if>
        <if test="capCorg != null and capCorg != ''" >
            and CAP_CORG = #{capCorg,jdbcType=VARCHAR}
        </if>
        <if test="crdAcTyp != null and crdAcTyp != ''" >
            and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
        </if>
        <if test="crdLth != null and crdLth != ''" >
            and CRD_LTH = #{crdLth,jdbcType=INTEGER}
        </if>
        <if test="belongingState != null and belongingState != ''" >
            and belonging_state = #{belongingState,jdbcType=INTEGER}
        </if>
        <if test="ascriptionArea != null and ascriptionArea != ''" >
            and ascription_area = #{ascriptionArea,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 删除卡Bin信息 -->
    <delete id="delete" parameterType="java.lang.String">
        delete from cpi_card_bin WHERE BIN_ID = #{binId,jdbcType=VARCHAR}
    </delete>
</mapper>