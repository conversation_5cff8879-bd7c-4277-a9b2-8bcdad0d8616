<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.acm.dao.IAcmAcDetailDao" >

    <select id="countUser" resultType="java.lang.Integer" parameterType="java.time.LocalDate">
        SELECT COUNT(DISTINCT(a.ac_no))
        FROM acm_ac_detail a LEFT JOIN acm_ac_inf b ON a.AC_NO = b.AC_NO
        WHERE a.ac_dt BETWEEN CONCAT(LEFT(#{acDt,jdbcType=DATE} , 7), '-01') AND #{acDt,jdbcType=DATE} AND  b.user_id NOT
        LIKE '888888%'
    </select>

    <select id="countMerc" resultType="java.lang.Integer" parameterType="java.time.LocalDate">
        SELECT COUNT(DISTINCT(a.ac_no))
        FROM acm_ac_detail a LEFT JOIN acm_ac_inf b ON a.AC_NO = b.AC_NO
        WHERE a.ac_dt BETWEEN CONCAT(LEFT(#{acDt,jdbcType=DATE} , 7), '-01') AND #{acDt,jdbcType=DATE} AND  b.user_id
        LIKE '888888%'
    </select>

</mapper>