<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.mkm.dao.IMkmPactDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.mkm.model.PactManagerDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="title" property="title" jdbcType="VARCHAR" />
        <result column="content" property="content" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="VARCHAR" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, title, content, type, status, remark, create_time, update_time, create_user, update_user, del_flag
    </sql>
    
    <!-- 根据条件查询协议列表 -->
    <select id="findByParam" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM tms_pact_manager
        WHERE del_flag = 'N'
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        ORDER BY update_time DESC
        <if test="pageBegin != null and pageEnd != null">
            LIMIT #{pageBegin}, #{pageEnd}
        </if>
    </select>
    
    <!-- 查询总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM tms_pact_manager
        WHERE del_flag = 'N'
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
    </select>
    
    <!-- 根据ID获取协议 -->
    <select id="getById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM tms_pact_manager
        WHERE id = #{id} AND del_flag = 'N'
    </select>
    
    <!-- 插入协议 -->
    <insert id="insert" parameterType="com.hisun.tms.mkm.model.PactManagerDO">
        INSERT INTO tms_pact_manager (
            title, content, type, status, remark, create_time, update_time, create_user, update_user, del_flag
        ) VALUES (
            #{title}, #{content}, #{type}, #{status}, #{remark}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser}, #{delFlag}
        )
    </insert>
    
    <!-- 更新协议 -->
    <update id="update" parameterType="com.hisun.tms.mkm.model.PactManagerDO">
        UPDATE tms_pact_manager
        <set>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        WHERE id = #{id} AND del_flag = 'N'
    </update>
    
    <!-- 删除协议（逻辑删除） -->
    <update id="delete" parameterType="java.lang.Integer">
        UPDATE tms_pact_manager
        SET del_flag = 'Y'
        WHERE id = #{id}
    </update>
</mapper> 