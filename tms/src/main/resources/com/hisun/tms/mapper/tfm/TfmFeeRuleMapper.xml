<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.tfm.dao.ITfmFeeRuleDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.tfm.model.FeeRuleManagerDO" >
        <id column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="bus_type_desc" property="busTypeDesc" jdbcType="VARCHAR" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="calculate_mod" property="calculateMod" jdbcType="VARCHAR" />
        <result column="calculate_type" property="calculateType" jdbcType="VARCHAR" />
        <result column="rate" property="rate" jdbcType="DECIMAL" />
        <result column="fix_fee" property="fixFee" jdbcType="DECIMAL" />
        <result column="charge_type" property="chargeType" jdbcType="VARCHAR" />
        <result column="calculate_min_amt" property="calculateMinAmt" jdbcType="DECIMAL" />
        <result column="min_fee" property="minFee" jdbcType="DECIMAL" />
        <result column="max_fee" property="maxFee" jdbcType="DECIMAL" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>
    
    <sql id="Base_Column_List">
        bus_type, bus_type_desc, ccy, calculate_mod, calculate_type, rate, fix_fee, charge_type, 
        calculate_min_amt, min_fee, max_fee, stats, eff_date, exp_date, opr_id, create_time, modify_time, tm_smp
    </sql>
    
    <!-- 根据条件查询手续费参数列表 -->
    <select id="findByFindFeeRuleDTO" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM tfm_rate_rule
        WHERE stats = '1'
        <if test="busType != null and busType != ''">
            AND bus_type = #{busType}
        </if>
        <if test="busTypeDesc != null and busTypeDesc != ''">
            AND bus_type_desc LIKE CONCAT('%', #{busTypeDesc}, '%')
        </if>
        <if test="ccy != null and ccy != ''">
            AND ccy = #{ccy}
        </if>
        ORDER BY modify_time DESC
        <if test="pageBegin != null and pageEnd != null">
            LIMIT #{pageBegin}, #{pageEnd}
        </if>
    </select>
    
    <!-- 查询总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM tfm_rate_rule
        WHERE stats = '1'
        <if test="busType != null and busType != ''">
            AND bus_type = #{busType}
        </if>
        <if test="busTypeDesc != null and busTypeDesc != ''">
            AND bus_type_desc LIKE CONCAT('%', #{busTypeDesc}, '%')
        </if>
        <if test="ccy != null and ccy != ''">
            AND ccy = #{ccy}
        </if>
    </select>
    
    <!-- 根据业务类型获取手续费参数 -->
    <select id="getByBusType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM tfm_rate_rule
        WHERE bus_type = #{busType} AND stats = '1'
    </select>
    
    <!-- 插入手续费参数 -->
    <insert id="insert" parameterType="com.hisun.tms.tfm.model.FeeRuleManagerDO">
        INSERT INTO tfm_rate_rule (
            bus_type, bus_type_desc, ccy, calculate_mod, calculate_type, rate, fix_fee, charge_type,
            calculate_min_amt, min_fee, max_fee, stats, eff_date, exp_date, opr_id, create_time, modify_time
        ) VALUES (
            #{busType}, #{busTypeDesc}, #{ccy}, #{calculateMod}, #{calculateType}, #{rate}, #{fixFee}, #{chargeType},
            #{calculateMinAmt}, #{minFee}, #{maxFee}, #{stats}, #{effDate}, #{expDate}, #{oprId}, #{createTime}, #{modifyTime}
        )
    </insert>
    
    <!-- 更新手续费参数 -->
    <update id="update" parameterType="com.hisun.tms.tfm.model.FeeRuleManagerDO">
        UPDATE tfm_rate_rule
        <set>
            <if test="busTypeDesc != null">
                bus_type_desc = #{busTypeDesc},
            </if>
            <if test="ccy != null">
                ccy = #{ccy},
            </if>
            <if test="calculateMod != null">
                calculate_mod = #{calculateMod},
            </if>
            <if test="calculateType != null">
                calculate_type = #{calculateType},
            </if>
            <if test="rate != null">
                rate = #{rate},
            </if>
            <if test="fixFee != null">
                fix_fee = #{fixFee},
            </if>
            <if test="chargeType != null">
                charge_type = #{chargeType},
            </if>
            <if test="calculateMinAmt != null">
                calculate_min_amt = #{calculateMinAmt},
            </if>
            <if test="minFee != null">
                min_fee = #{minFee},
            </if>
            <if test="maxFee != null">
                max_fee = #{maxFee},
            </if>
            <if test="stats != null">
                stats = #{stats},
            </if>
            <if test="effDate != null">
                eff_date = #{effDate},
            </if>
            <if test="expDate != null">
                exp_date = #{expDate},
            </if>
            <if test="oprId != null">
                opr_id = #{oprId},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
        </set>
        WHERE bus_type = #{busType} AND stats = '1'
    </update>
    
    <!-- 删除手续费参数（逻辑删除） -->
    <update id="delete" parameterType="java.lang.String">
        UPDATE tfm_rate_rule
        SET stats = '0'
        WHERE bus_type = #{busType}
    </update>
</mapper> 