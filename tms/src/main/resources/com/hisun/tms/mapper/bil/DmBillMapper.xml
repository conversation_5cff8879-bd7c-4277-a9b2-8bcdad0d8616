<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.tms.bil.dao.IDmBillDao">

    <!-- 查询账单编号集合 -->
    <select id="findOrderNos" resultType="java.lang.String">
        SELECT ORDER_NO
        FROM BIL_USER_ORDER
        <where>
            <if test="acNo != null and acNo != ''">
                AND AC_NO = #{acNo}
            </if>
            <if test="txType != null and txType != ''">
                AND TX_TYPE = #{txType}
            </if>
            <if test="txType == null or txType == ''">
                AND TX_TYPE IN ('DS', 'DC', 'DH', 'DZ', 'DX')
            </if>
            <if test="startTime != null">
                AND TX_TM &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND TX_TM &lt;= #{endTime}
            </if>
        </where>
        ORDER BY TX_TM DESC
    </select>

    <!-- 获取收款/充值记录列表 -->
    <select id="getReceiptRecords" resultType="com.hisun.tms.bil.model.ReceiptRecord">
        SELECT 
            b.TX_TM as txTime,
            b.CCY as coinId,
            b.ORDER_AMT as amount,
            b.FEE as fee,
            b.AC_NO as receiveAddress,
            t.CP_ADDRESS as payAddress,
            b.ORDER_NO as orderId,
            t.TX_BASE_TX_ID as txHash,
            b.ORDER_STATUS as status,
            b.REMARK as memo,
            t.CHANNEL as channel,
            b.TX_TYPE as txType,
            t.TX_BASE_NETWORK as network,
            t.TX_BASE_BLOCK_ID as blockId,
            t.tx_base_ecode as ecode,
            t.tx_base_group_id as groupId,
            t.tx_base_block_hash as blockHash,
            t.tx_base_program_id as programId,
            t.tx_base_compute_units_consumed as computeUnitsConsumed,
            t.VAULT_CODE as vaultCode,
            t.ACCOUNT_ID as accountId,
            t.ACCOUNT_TYPE as accountType,
            t.BIL_ORDER_NO as bilOrderNo,
            t.DIRECTION as direction
        FROM BIL_USER_ORDER b
        LEFT JOIN DM_TRANSFER_CALLBACK t ON b.ORDER_NO = t.BIL_ORDER_NO
        WHERE b.ORDER_NO IN
        <foreach item="item" collection="orderNos" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY b.TX_TM DESC
        <if test="pageNo != null and pageSize != null">
            LIMIT #{pageSize} OFFSET #{offset}
        </if>
    </select>

    <!-- 获取收款/充值记录详情 -->
    <select id="getReceiptRecordDetail" resultType="com.hisun.tms.bil.model.ReceiptRecord">
        SELECT 
            b.TX_TM as txTime,
            b.CCY as coinId,
            b.ORDER_AMT as amount,
            b.FEE as fee,
            b.AC_NO as receiveAddress,
            t.CP_ADDRESS as payAddress,
            b.ORDER_NO as orderId,
            t.TX_BASE_TX_ID as txHash,
            b.ORDER_STATUS as status,
            b.REMARK as memo,
            t.CHANNEL as channel,
            b.TX_TYPE as txType,
            t.TX_BASE_NETWORK as network,
            t.TX_BASE_BLOCK_ID as blockId,
            t.tx_base_ecode as ecode,
            t.tx_base_group_id as groupId,
            t.tx_base_block_hash as blockHash,
            t.tx_base_program_id as programId,
            t.tx_base_compute_units_consumed as computeUnitsConsumed,
            t.VAULT_CODE as vaultCode,
            t.ACCOUNT_ID as accountId,
            t.ACCOUNT_TYPE as accountType,
            t.BIL_ORDER_NO as bilOrderNo,
            t.DIRECTION as direction
        FROM BIL_USER_ORDER b
        LEFT JOIN DM_TRANSFER_CALLBACK t ON b.ORDER_NO = t.BIL_ORDER_NO
        WHERE b.ORDER_NO = #{orderId}
    </select>

</mapper>