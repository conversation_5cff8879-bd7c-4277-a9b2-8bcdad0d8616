<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.inv.dao.InvFeeDetailDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.inv.model.InvFeeDetailDO" >
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="pro_id" property="proId" jdbcType="VARCHAR" />
        <result column="pro_rate" property="proRate" jdbcType="DECIMAL" />
        <result column="inv_amt" property="invAmt" jdbcType="DECIMAL" />
        <result column="sum_fee_amt" property="sumfeeAmt" jdbcType="DECIMAL" />
        <result column="sum_inv_amt" property="suminvAmt" jdbcType="DECIMAL" />
        <result column="fee_amt" property="feeAmt" jdbcType="DECIMAL" />
        <result column="total_fee_amt" property="totalFeeAmt" jdbcType="DECIMAL" />
        <result column="inv_grant_dt" property="invGrantDt" jdbcType="DATE" />
        <result column="inv_confirm_dt" property="invConfirmDt" jdbcType="CHAR" />
        <result column="inv_grant_flag" property="invGrantFlag" jdbcType="CHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        inv_id, buy_dt, user_id, pro_id, pro_rate, inv_amt, fee_amt, inv_grant_dt, inv_confirm_dt, 
        inv_grant_flag, create_time, modify_time, tm_smp
    </sql>

    <!--根据条件查询付款订单列表，默认按照订单日期、订单时间排序(活期)-->
    <select id="getInvFeeDetailDOList" resultMap="BaseResultMap">
        select 
        pro_id, inv_grant_dt, pro_rate, sum(inv_amt) as sum_inv_amt, sum(fee_amt) as sum_fee_amt
        from inv_fee_detail 
        where pro_type='D'
        <if test="beginDt != null" >
            <![CDATA[and inv_grant_dt >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and inv_grant_dt <= #{endDt,jdbcType=DATE} ]]>
        </if>
        group by pro_id, inv_grant_dt, pro_rate
        order by inv_grant_dt desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageEnd,jdbcType=INTEGER}
    </select>
    
    <!--根据条件查询订单列表总笔数(活期)-->
    <select id="getInvFeeDetailDOListTotNum" resultType="java.lang.Integer">
        select count(inv_grant_dt) from(
	        select pro_id, inv_grant_dt, pro_rate, sum(inv_amt) as sum_inv_amt, sum(fee_amt) as sum_fee_amt
	        from inv_fee_detail 
	        where pro_type='D'
	        <if test="beginDt != null" >
	            <![CDATA[and inv_grant_dt >= #{beginDt,jdbcType=DATE} ]]>
	        </if>
	        <if test="endDt != null" >
	            <![CDATA[ and inv_grant_dt <= #{endDt,jdbcType=DATE} ]]>
	        </if>
	        group by pro_id, inv_grant_dt, pro_rate
        ) t
    </select>
    <!--根据条件查询利息发放合计(活期)-->
    <select id="queryTotalFeeAmt" resultType="java.math.BigDecimal">
        select sum(fee_amt) as totalFeeAmt
        from inv_fee_detail
        where pro_type='D'
        <if test="beginDt != null" >
            <![CDATA[and inv_grant_dt >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and inv_grant_dt <= #{endDt,jdbcType=DATE} ]]>
        </if>
    </select>
    
    <!--根据条件查询付款订单列表，默认按照订单日期、订单时间排序(定期)-->
    <select id="getRegInvFeeDetailDOList" resultMap="BaseResultMap">
        select 
        pro_id, inv_grant_dt, pro_rate, sum(inv_amt) as sum_inv_amt, sum(fee_amt) as sum_fee_amt
        from inv_fee_detail 
        where pro_type in ('M','S','Y')
        <if test="beginDt != null" >
            <![CDATA[and inv_grant_dt >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and inv_grant_dt <= #{endDt,jdbcType=DATE} ]]>
        </if>
        group by pro_id, inv_grant_dt, pro_rate
        order by inv_grant_dt desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageEnd,jdbcType=INTEGER}
    </select>
    
    <!--根据条件查询订单列表总笔数(定期)-->
    <select id="getRegInvFeeDetailDOListTotNum" resultType="java.lang.Integer">
        select count(inv_grant_dt) from(
	        select pro_id, inv_grant_dt, pro_rate, sum(inv_amt) as sum_inv_amt, sum(fee_amt) as sum_fee_amt
	        from inv_fee_detail 
	        where pro_type in ('M','S','Y')
	        <if test="beginDt != null" >
	            <![CDATA[and inv_grant_dt >= #{beginDt,jdbcType=DATE} ]]>
	        </if>
	        <if test="endDt != null" >
	            <![CDATA[ and inv_grant_dt <= #{endDt,jdbcType=DATE} ]]>
	        </if>
	        group by pro_id, inv_grant_dt, pro_rate
        ) t
    </select>
    <!--根据条件查询利息发放合计(定期)-->
    <select id="queryRegTotalFeeAmt" resultType="java.math.BigDecimal">
        select sum(fee_amt) as totalFeeAmt
        from inv_fee_detail
        where pro_type in ('M','S','Y')
        <if test="beginDt != null" >
            <![CDATA[and inv_grant_dt >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and inv_grant_dt <= #{endDt,jdbcType=DATE} ]]>
        </if>
    </select>
    
    <!--根据条件查询付款订单列表，默认按照订单日期、订单时间排序-->
    <select id="getProInfo" resultMap="BaseResultMap">
        select 
        pro_id,inv_grant_dt,pro_rate, sum(inv_amt) as sum_inv_amt, sum(fee_amt) as sum_fee_amt
        from inv_fee_detail 
        where pro_type='D'
        <if test="proId != null" >
            and pro_id = #{proId,jdbcType=VARCHAR}
        </if>
        <if test="queryDt != null" >
            <![CDATA[ and inv_grant_dt = #{queryDt,jdbcType=DATE} ]]>
        </if>
        group by pro_id, inv_grant_dt,pro_rate
    </select>
    
    
</mapper>