<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpi.ISettleDetailDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.SettleDetailDO" >
        <id column="jrn_no" property="jrnNo" jdbcType="VARCHAR" />
        <result column="update_date" property="updateDate" jdbcType="DATE" />
        <result column="update_time" property="updateTime" jdbcType="TIME" />
        <result column="pay_batch_no" property="payBatchNo" jdbcType="VARCHAR" />
        <result column="settle_date" property="settleDate" jdbcType="DATE" />
        <result column="settle_datetime" property="settleDatetime" jdbcType="TIMESTAMP" />
        <result column="start_date" property="startDate" jdbcType="DATE" />
        <result column="end_date" property="endDate" jdbcType="DATE" />
        <result column="settle_fee" property="settleFee" jdbcType="DECIMAL" />
        <result column="unsettle_fee" property="unsettleFee" jdbcType="DECIMAL" />
        <result column="settle_fee_type" property="settleFeeType" jdbcType="VARCHAR" />
        <result column="pay_fee" property="payFee" jdbcType="DECIMAL" />
        <result column="refund_fee" property="refundFee" jdbcType="DECIMAL" />
        <result column="pay_net_fee" property="payNetFee" jdbcType="DECIMAL" />
        <result column="poundage_fee" property="poundageFee" jdbcType="DECIMAL" />
        <result column="tot_settle_fee" property="totSettleFee" jdbcType="DECIMAL" />
        <result column="tot_unsettle_fee" property="totUnsettleFee" jdbcType="DECIMAL" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="appid" property="appid" jdbcType="VARCHAR" />
        <result column="mch_id" property="mchId" jdbcType="VARCHAR" />
        <result column="sub_mch_id" property="subMchId" jdbcType="VARCHAR" />
        <result column="stl_flg" property="stlFlg" jdbcType="VARCHAR" />
        <result column="rut_corg" property="rutCorg" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        jrn_no, update_date, update_time, pay_batch_no, settle_date, settle_datetime, start_date, 
        end_date, settle_fee, unsettle_fee, settle_fee_type, pay_fee, refund_fee, pay_net_fee, 
        poundage_fee, tot_settle_fee, tot_unsettle_fee, CREATE_TIME, MODIFY_TIME, TM_SMP, 
        appid, mch_id, sub_mch_id, stl_flg, rut_corg
    </sql>

    <!--根据条件，查询结算明细列表，默认按照订单日期、订单时间排序-->
    <select id="getSettleList" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from cpi_settle_detail
        where 1 = 1
        <if test="stlFlg != null and stlFlg != ''" >
            and stl_flg = #{stlFlg,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and rut_corg = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and settle_date >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and settle_date <= #{endDate,jdbcType=DATE} ]]>
        </if>
        order by settle_date desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--根据条件，查询结算明细总数，默认按照订单日期、订单时间排序-->
    <select id="getSettleListTotNum" resultType="java.lang.Integer" >
        select count(1)
        from cpi_settle_detail
        where 1 = 1
        <if test="stlFlg != null and stlFlg != ''" >
            and stl_flg = #{stlFlg,jdbcType=VARCHAR}
        </if>
        <if test="rutCorg != null and rutCorg != ''" >
            and rut_corg = #{rutCorg,jdbcType=VARCHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and settle_date >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and settle_date <= #{endDate,jdbcType=DATE} ]]>
        </if>
    </select>

</mapper>