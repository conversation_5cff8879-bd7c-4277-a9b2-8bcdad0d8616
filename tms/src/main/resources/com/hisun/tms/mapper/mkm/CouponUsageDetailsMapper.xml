<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.mkm.dao.ICouponUsageDetailsDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.mkm.model.ConsumeDetailDo" >
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="atv_id" property="id" jdbcType="VARCHAR" />
        <result column="atv_nm" property="atvNm" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="count" property="count" jdbcType="INTEGER" />
        <result column="modify_time" property="consumeTime" jdbcType="TIMESTAMP" />
        <result column="userDt" property="userDt" jdbcType="DATE" />
        <result column="userTm" property="userTm" jdbcType="TIME" />
        <result column="mkTool" property="mkTool" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectUsageByMblNoAndBegDt" resultMap="BaseResultMap">
        select result.* from (
        (select
        c.atv_id, m.atv_nm ,c.mk_Tool , c.inst_id,c.mobile, date(c.modify_time) as userDt,time(c.modify_time) as userTm, c.order_amt ,c.order_no,c.status,  1 as count
        from
        mkm_coupon_detail c left join mkm_activity m on c.atv_id = m.id
        where (c.status = '02' or c.status = '07')
        <if test="mobile != null and mobile != ''" >
            and c.mobile = #{mobile,jdbcType=VARCHAR}
        </if>
        <if test="instId != null and instId != ''" >
            and c.inst_id = #{instId,jdbcType=VARCHAR}
        </if>
        <if test="begDt != null" >
            and c.modify_time > #{begDt,jdbcType=DATE}
        </if>
        <if test="endDt != null" >
            <![CDATA[    and c.modify_time < #{endDt,jdbcType=DATE}  ]]>
        </if>
        and 1 = 1
        )
        union
        (    select
        s.atv_id, m.atv_nm ,s.mk_Tool , s.inst_id,s.mobile,date(s.modify_time) as userDt,time(s.modify_time) as userTm, s.order_amt ,s.order_no,s.status,  s.val_refund as count
        from
        mkm_sea_ccy_seq s left join  mkm_activity m on s.atv_id = m.id
        where
        type = '02' and (s.status = '1' or s.status = '5') and
        <if test="mobile != null and mobile != ''" >
            s.mobile = #{mobile,jdbcType=VARCHAR} and
        </if>
        <if test="instId != null and instId != ''" >
             s.inst_id = #{instId,jdbcType=VARCHAR} and
        </if>
        <if test="begDt != null" >
            s.modify_time > #{begDt,jdbcType=DATE} and
        </if>
        <if test="endDt != null" >
            <![CDATA[s.modify_time < #{endDt,jdbcType=DATE} and ]]>
        </if>
        1 = 1
        )) as result
        order by userDt desc
        limit #{start,jdbcType=INTEGER},#{length,jdbcType=INTEGER}
    </select>

    <select id="selectUsageByMblNoAndBegDtTotal" resultType="java.lang.Integer">
        select count(*) from (
        (select
        c.atv_id, m.atv_nm ,c.mk_Tool , c.inst_id,c.mobile, c.order_amt ,c.order_no,c.status,  1 as count
        from
        mkm_coupon_detail c left join mkm_activity m on c.atv_id = m.id
        where (c.status = '02' or c.status = '07')
        <if test="mobile != null and mobile != ''" >
            and c.mobile = #{mobile,jdbcType=VARCHAR}
        </if>
        <if test="instId != null and instId != ''" >
            and c.inst_id = #{instId,jdbcType=VARCHAR}
        </if>
        <if test="begDt != null" >
            and c.modify_time > #{begDt,jdbcType=DATE}
        </if>
        <if test="endDt != null" >
            <![CDATA[    and c.modify_time < #{endDt,jdbcType=DATE}  ]]>
        </if>
        and 1 = 1
        )
        union
        (    select
        s.atv_id, m.atv_nm ,s.mk_Tool , s.inst_id,s.mobile, s.order_amt ,s.order_no,s.status,  s.val_refund as count
        from
        mkm_sea_ccy_seq s left join  mkm_activity m on s.atv_id = m.id
        where
        type = '02' and (s.status = '1' or s.status = '06') and
        <if test="mobile != null and mobile != ''" >
            s.mobile = #{mobile,jdbcType=VARCHAR} and
        </if>
        <if test="instId != null and instId != ''" >
             s.inst_id = #{instId,jdbcType=VARCHAR} and
        </if>
        <if test="begDt != null" >
            s.modify_time > #{begDt,jdbcType=DATE} and
        </if>
        <if test="endDt != null" >
            <![CDATA[s.modify_time < #{endDt,jdbcType=DATE} and ]]>
        </if>
        1 = 1
        )) as result
    </select>

</mapper>