<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.mkm.dao.IMkmFAQDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.mkm.model.FAQManagerDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="question_content" property="questionContent" jdbcType="VARCHAR" />
        <result column="answer_content" property="answerContent" jdbcType="VARCHAR" />
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="updated_date" property="updatedDate" jdbcType="TIMESTAMP" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="deleted_flag" property="deletedFlag" jdbcType="VARCHAR" />
        <result column="language" property="language" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        id, question_content, answer_content, created_date, created_by, updated_date, updated_by, deleted_flag,language
    </sql>
    
    <!-- 根据条件查询常见问题列表 -->
    <select id="findByFindFAQDTO" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM question_answer_info
        WHERE deleted_flag = 'N'
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="questionContent != null and questionContent != ''">
            AND question_content LIKE CONCAT('%', #{questionContent}, '%')
        </if>
        ORDER BY updated_date DESC
        <if test="pageBegin != null and pageEnd != null">
            LIMIT #{pageBegin}, #{pageEnd}
        </if>
    </select>
    
    <!-- 查询总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM question_answer_info
        WHERE deleted_flag = 'N'
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="questionContent != null and questionContent != ''">
            AND question_content LIKE CONCAT('%', #{questionContent}, '%')
        </if>
    </select>
    
    <!-- 根据ID获取常见问题 -->
    <select id="getById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM question_answer_info
        WHERE id = #{id} AND deleted_flag = 'N'
    </select>
    
    <!-- 插入常见问题 -->
    <insert id="insert" parameterType="com.hisun.tms.mkm.model.FAQManagerDO">
        INSERT INTO question_answer_info (
            question_content, answer_content, created_date, created_by, updated_date, updated_by, deleted_flag,language
        ) VALUES (
            #{questionContent}, #{answerContent}, #{createdDate}, #{createdBy}, #{updatedDate}, #{updatedBy}, #{deletedFlag},#{language}
        )
    </insert>
    
    <!-- 更新常见问题 -->
    <update id="update" parameterType="com.hisun.tms.mkm.model.FAQManagerDO">
        UPDATE question_answer_info
        <set>
            <if test="questionContent != null">
                question_content = #{questionContent},
            </if>
            <if test="answerContent != null">
                answer_content = #{answerContent},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="language != null">
                language = #{language},
            </if>
        </set>
        WHERE id = #{id} AND deleted_flag = 'N'
    </update>
    
    <!-- 删除常见问题（逻辑删除） -->
    <update id="delete" parameterType="java.lang.Integer">
        UPDATE question_answer_info
        SET deleted_flag = 'Y'
        WHERE id = #{id}
    </update>
</mapper> 