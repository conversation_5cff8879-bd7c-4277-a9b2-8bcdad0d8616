<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.acm.dao.IAcmVoucherInfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.acm.model.AcmVoucherInf">
        <result column="JRN_NO" property="jrnNo" jdbcType="VARCHAR"/>
        <result column="JRN_SEQ" property="jrnSeq" jdbcType="VARCHAR"/>
        <result column="AC_DT" property="acDt" jdbcType="DATE"/>
        <result column="TX_TYP" property="txTyp" jdbcType="VARCHAR"/>
        <result column="TX_STS" property="txSts" jdbcType="VARCHAR"/>
        <result column="AC_NO" property="acNo" jdbcType="VARCHAR"/>
        <result column="CAP_TYP" property="capTyp" jdbcType="VARCHAR"/>
        <result column="DC_FLG" property="dcFlg" jdbcType="VARCHAR"/>
        <result column="TX_AMT" property="txAmt" jdbcType="DECIMAL"/>
        <result column="CCY" property="ccy" jdbcType="VARCHAR"/>
        <result column="HLD_NO" property="hldNo" jdbcType="VARCHAR"/>
        <result column="RVS_JRN_NO" property="rvsJrnNo" jdbcType="VARCHAR"/>
        <result column="RVS_JRN_SEQ" property="rvsJrnSeq" jdbcType="VARCHAR"/>
        <result column="DR_AMT" property="drAmt" jdbcType="DECIMAL"/>
        <result column="CR_AMT" property="crAmt" jdbcType="DECIMAL"/>
        <result column="OD_AMT" property="odAmt" jdbcType="DECIMAL"/>
        <result column="CUR_BAL" property="curBal" jdbcType="DECIMAL"/>
        <result column="UPD_BAL_FLG" property="updBalFlg" jdbcType="VARCHAR"/>
        <result column="TX_JRN_NO" property="txJrnNo" jdbcType="VARCHAR"/>
        <result column="TX_ORD_NO" property="txOrdNo" jdbcType="VARCHAR"/>
        <result column="TX_ORD_DT" property="txOrdDt" jdbcType="DATE"/>
        <result column="TX_ORD_TM" property="txOrdTm" jdbcType="TIME"/>
        <result column="OPP_AC_NO" property="oppAcNo" jdbcType="VARCHAR"/>
        <result column="OPP_CAP_TYP" property="oppCapTyp" jdbcType="VARCHAR"/>
        <result column="OPP_USER_ID" property="oppUserId" jdbcType="VARCHAR"/>
        <result column="OPP_USER_TYP" property="oppUserTyp" jdbcType="VARCHAR"/>
        <result column="USR_IP_ADR" property="usrIpAdr" jdbcType="VARCHAR"/>
        <result column="RMK" property="rmk" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryVoucherInf" resultType="java.util.Map" parameterType="java.time.LocalDate">
        SELECT jrn_no, ac_no, " " ac_nm, cap_typ, dc_flg, tx_amt, tx_typ
        FROM acm_ac_detail
        WHERE ac_dt = #{acDt,jdbcType=DATE} AND jrn_no IN (SELECT vch_no FROM acm_voucher_sum
        WHERE ac_dt = #{acDt,jdbcType=DATE} AND net_amt != '0.00')
        UNION
        SELECT a.jrn_no jrn_no, a.itm_no ac_no, b.itm_cnm ac_nm, "1" cap_typ, a.dc_flg dc_flg, a.tx_amt tx_amt,
        a.tx_typ tx_typ
        FROM acm_itm_detail a LEFT JOIN acm_itm_inf b ON a.itm_no = b.itm_no
        WHERE ac_dt = #{acDt,jdbcType=DATE} AND jrn_no IN (SELECT vch_no FROM acm_voucher_sum
        WHERE ac_dt = #{acDt,jdbcType=DATE}AND net_amt != '0.00')
    </select>

    <select id="findVoucherInf" resultMap="BaseResultMap">
        SELECT result.* from (
        select
        JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,AC_NO,
        CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
        RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
        UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
        TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
        OPP_USER_TYP,USR_IP_ADR,RMK,CREATE_TIME
        from acm_ac_detail where AC_DT BETWEEN #{startDt,jdbcType=DATE} AND #{endDt, jdbcType=DATE}
        <if test="acNo != null and acNo !=''">
            and AC_NO = #{acNo, jdbcType=VARCHAR}
        </if>
        <if test="jrnNo != null and jrnNo != ''">
            and JRN_NO = #{jrnNo, jdbcType=VARCHAR}
        </if>
        UNION
        select
        JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,ITM_NO AS AC_NO,
        CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
        RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
        UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
        TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
        OPP_USER_TYP,USR_IP_ADR,RMK,CREATE_TIME
        from acm_itm_detail where AC_DT BETWEEN #{startDt,jdbcType=DATE} AND #{endDt, jdbcType=DATE}
        <if test="acNo != null and acNo !=''">
            and ITM_NO = #{acNo,jdbcType=VARCHAR}
        </if>
        <if test="jrnNo != null and jrnNo != ''">
            and JRN_NO = #{jrnNo,jdbcType=VARCHAR}
        </if>
        ) as result
        order by CREATE_TIME desc
        limit #{pageBeg,jdbcType=INTEGER},#{pageEnd,jdbcType=INTEGER}
    </select>

    <select id="countVoucherNum" resultType="java.lang.Integer">
        SELECT count(1) from (
        select
        JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,AC_NO,
        CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
        RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
        UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
        TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
        OPP_USER_TYP,USR_IP_ADR,RMK,CREATE_TIME
        from acm_ac_detail where AC_DT BETWEEN #{startDt,jdbcType=DATE} AND #{endDt, jdbcType=DATE}
        <if test="acNo != null and acNo !=''">
            and AC_NO = #{acNo, jdbcType=VARCHAR}
        </if>
        <if test="jrnNo != null and jrnNo !=''">
            and JRN_NO = #{jrnNo, jdbcType=VARCHAR}
        </if>
        UNION
        select JRN_NO,JRN_SEQ,AC_DT,TX_TYP,TX_STS,ITM_NO AS AC_NO,
        CAP_TYP,DC_FLG,TX_AMT,CCY,HLD_NO,RVS_JRN_NO,
        RVS_JRN_SEQ,DR_AMT,CR_AMT,OD_AMT,CUR_BAL,
        UPD_BAL_FLG,TX_JRN_NO,TX_ORD_NO,TX_ORD_DT,
        TX_ORD_TM,OPP_AC_NO,OPP_CAP_TYP,OPP_USER_ID,
        OPP_USER_TYP,USR_IP_ADR,RMK,CREATE_TIME
        from acm_itm_detail where AC_DT BETWEEN #{startDt,jdbcType=DATE} AND #{endDt, jdbcType=DATE}
        <if test="acNo != null and acNo !=''">
            and ITM_NO = #{acNo, jdbcType=VARCHAR}
        </if>
        <if test="jrnNo != null and jrnNo !=''">
            and JRN_NO = #{jrnNo, jdbcType=VARCHAR}
        </if>
        ) as result
    </select>

</mapper>