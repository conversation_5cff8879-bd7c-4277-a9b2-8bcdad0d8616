<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.acm.dao.IAcmItmBalDao" >

    <select id="queryItmBal" resultType="java.util.Map">
        select itm_no, itm_cnm,
        (case when bal_drt = 'D' then last_dr_bal else last_cr_bal end) last_bal,
        td_dr_amt, td_cr_amt, bal_drt,
        (case when bal_drt = 'D' then td_dr_bal else td_cr_bal end) td_bal
        from acm_itm_bal
    </select>

</mapper>