<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.mkm.dao.IMkmInstDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.mkm.model.MkmInstDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="atv_id" property="atvId" jdbcType="VARCHAR" />
        <result column="inst_id" property="instId" jdbcType="VARCHAR" />
        <result column="total" property="total" jdbcType="INTEGER" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="tot_iss_amt" property="totIssAmt" jdbcType="DECIMAL" />
        <result column="tot_iss_cnt" property="totIssCnt" jdbcType="INTEGER" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, atv_id, inst_id, total, total_amt, tot_iss_amt, tot_iss_cnt, modify_time, create_time
    </sql>


    <select id="getAllInstIdByActId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select inst_id,id from mkm_inst where atv_id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="findByinstIdAndActId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List" /> from mkm_inst where atv_id = #{atvId,jdbcType=VARCHAR} and inst_id = #{instId,jdbcType=VARCHAR}
    </select>
</mapper>