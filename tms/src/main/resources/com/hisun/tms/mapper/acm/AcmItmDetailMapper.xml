<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.acm.dao.IAcmItmDetailDao" >

    <select id="queryItmDetail" resultType="java.util.Map" parameterType="java.time.LocalDate">
        select  a.dc_flg, a.itm_no, b.itm_cnm, a.tx_amt, "" tx_cd, a.tx_typ
        from acm_itm_detail A LEFT JOIN acm_itm_inf B on a.itm_no = b.itm_no
        where a.ac_dt = #{acDt,jdbcType=DATE}
    </select>

</mapper>