<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.ICptFeeOrderQueryDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.FeeOrderQueryDO" >
        <id column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="bus_order_no" property="busOrderNo" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="bus_type_desc" property="busTypeDesc" jdbcType="VARCHAR" />
        <result column="clear_stats" property="clearStats" jdbcType="VARCHAR" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="trade_amt" property="tradeAmt" jdbcType="DECIMAL" />
        <result column="trade_date" property="tradeDate" jdbcType="DATE" />
        <result column="trade_time" property="tradeTime" jdbcType="VARCHAR" />
        <result column="trade_total_amt" property="tradeTotalAmt" jdbcType="DECIMAL" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="calculate_mod" property="calculateMod" jdbcType="VARCHAR" />
        <result column="calculate_type" property="calculateType" jdbcType="VARCHAR" />
        <result column="rate" property="rate" jdbcType="DECIMAL" />
        <result column="bus_order_time" property="busOrderTime" jdbcType="TIMESTAMP" />
    </resultMap>
    
    <sql id="Base_Column_List">
        order_no, create_time, modify_time, bus_order_no, bus_type, bus_type_desc, 
        clear_stats, fee, trade_amt, trade_date, trade_time, trade_total_amt, 
        user_id, user_name, ccy, calculate_mod, calculate_type, rate, bus_order_time
    </sql>
    
    <!-- 根据条件查询手续费订单列表 -->
    <select id="findByQueryCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM tfm_fee_order
        WHERE 1=1
        <if test="orderNo != null and orderNo != ''">
            AND order_no = #{orderNo}
        </if>
        <if test="busOrderNo != null and busOrderNo != ''">
            AND bus_order_no LIKE CONCAT('%', #{busOrderNo}, '%')
        </if>
        <if test="busType != null and busType != ''">
            AND bus_type = #{busType}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id LIKE CONCAT('%', #{userId}, '%')
        </if>
        <if test="userName != null and userName != ''">
            AND user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="ccy != null and ccy != ''">
            AND ccy = #{ccy}
        </if>
        <if test="clearStats != null and clearStats != ''">
            AND clear_stats = #{clearStats}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND create_time &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
        </if>
        ORDER BY create_time DESC
        <if test="pageBegin != null and pageEnd != null">
            LIMIT #{pageBegin}, #{pageEnd}
        </if>
    </select>
    
    <!-- 查询总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM tfm_fee_order
        WHERE 1=1
        <if test="orderNo != null and orderNo != ''">
            AND order_no = #{orderNo}
        </if>
        <if test="busOrderNo != null and busOrderNo != ''">
            AND bus_order_no LIKE CONCAT('%', #{busOrderNo}, '%')
        </if>
        <if test="busType != null and busType != ''">
            AND bus_type = #{busType}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id LIKE CONCAT('%', #{userId}, '%')
        </if>
        <if test="userName != null and userName != ''">
            AND user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="ccy != null and ccy != ''">
            AND ccy = #{ccy}
        </if>
        <if test="clearStats != null and clearStats != ''">
            AND clear_stats = #{clearStats}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND create_time &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
        </if>
    </select>
    
    <!-- 根据订单号获取手续费订单 -->
    <select id="getByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM tfm_fee_order
        WHERE order_no = #{orderNo}
    </select>
    
    <!-- 插入手续费订单 -->
    <insert id="insert" parameterType="com.hisun.tms.cpt.model.FeeOrderQueryDO">
        INSERT INTO tfm_fee_order (
            order_no, create_time, modify_time, bus_order_no, bus_type, bus_type_desc,
            clear_stats, fee, trade_amt, trade_date, trade_time, trade_total_amt,
            user_id, user_name, ccy, calculate_mod, calculate_type, rate, bus_order_time
        ) VALUES (
            #{orderNo}, #{createTime}, #{modifyTime}, #{busOrderNo}, #{busType}, #{busTypeDesc},
            #{clearStats}, #{fee}, #{tradeAmt}, #{tradeDate}, #{tradeTime}, #{tradeTotalAmt},
            #{userId}, #{userName}, #{ccy}, #{calculateMod}, #{calculateType}, #{rate}, #{busOrderTime}
        )
    </insert>
    
    <!-- 更新手续费订单 -->
    <update id="update" parameterType="com.hisun.tms.cpt.model.FeeOrderQueryDO">
        UPDATE tfm_fee_order
        <set>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="busOrderNo != null">
                bus_order_no = #{busOrderNo},
            </if>
            <if test="busType != null">
                bus_type = #{busType},
            </if>
            <if test="busTypeDesc != null">
                bus_type_desc = #{busTypeDesc},
            </if>
            <if test="clearStats != null">
                clear_stats = #{clearStats},
            </if>
            <if test="fee != null">
                fee = #{fee},
            </if>
            <if test="tradeAmt != null">
                trade_amt = #{tradeAmt},
            </if>
            <if test="tradeDate != null">
                trade_date = #{tradeDate},
            </if>
            <if test="tradeTime != null">
                trade_time = #{tradeTime},
            </if>
            <if test="tradeTotalAmt != null">
                trade_total_amt = #{tradeTotalAmt},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="ccy != null">
                ccy = #{ccy},
            </if>
            <if test="calculateMod != null">
                calculate_mod = #{calculateMod},
            </if>
            <if test="calculateType != null">
                calculate_type = #{calculateType},
            </if>
            <if test="rate != null">
                rate = #{rate},
            </if>
            <if test="busOrderTime != null">
                bus_order_time = #{busOrderTime},
            </if>
        </set>
        WHERE order_no = #{orderNo}
    </update>
    
    <!-- 删除手续费订单 -->
    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM tfm_fee_order
        WHERE order_no = #{orderNo}
    </delete>
</mapper>