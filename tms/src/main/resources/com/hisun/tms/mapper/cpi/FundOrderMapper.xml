<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpi.IFundOrderDao" >

    <!--充值订单信息-->
    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.FundOrderDO" >
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
        <result column="ORD_RFD_AMT" property="ordRfdAmt" jdbcType="DECIMAL" />
        <result column="ORD_STS" property="ordSts" jdbcType="VARCHAR" />
        <result column="REQ_ORD_NO" property="reqOrdNo" jdbcType="VARCHAR" />
        <result column="FUD_ORD_NO" property="fudOrdNo" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
    </resultMap>

    <!--汇款订单信息-->
    <resultMap id="RemitBaseResultMap" type="com.hisun.tms.cpt.model.RemitOrderDO" >
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="ORD_STS" property="ordSts" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="PIC_URL" property="picUrl" jdbcType="VARCHAR" />
        <result column="REASON" property="reason" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
    </resultMap>

    <!--根据条件，查询退款订单列表，默认按照订单日期、订单时间排序-->
    <select id="getFundOrderList" resultMap="BaseResultMap">
        select
            MBL_NO,USER_ID,ORD_DT,ORD_TM,CORP_BUS_TYP,CORP_BUS_SUB_TYP,RUT_CORP_ORG,CRD_CORP_ORG,
            CRD_AC_TYP,ORD_AMT,ORD_RFD_AMT,ORD_STS,REQ_ORD_NO,FUD_ORD_NO,RMK
        from cpi_fund_order
        where 1 = 1
        <if test="mblNo != null and mblNo != ''" >
            and MBL_NO = #{mblNo,jdbcType=VARCHAR}
        </if>
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="fudOrdNo != null and fudOrdNo != ''" >
            and FUD_ORD_NO = #{fudOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="corpBusTyp != null and corpBusTyp != ''" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="rutCorpOrg != null and rutCorpOrg != ''" >
            and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="crdCorpOrg != null and crdCorpOrg != ''" >
            and CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="ordSts != null and ordSts != '' and ordSts != 'A1'" >
            and ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and ORD_DT >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and ORD_DT <= #{endDate,jdbcType=DATE} ]]>
        </if>
        and ORD_STS != 'A1'
        order by ord_dt desc, ord_tm desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--根据条件，查询退款订单列表，默认按照订单日期、订单时间排序-->
    <select id="getFundOrderListOnlyWaitReview" resultMap="BaseResultMap">
        select
            MBL_NO,USER_ID,ORD_DT,ORD_TM,CORP_BUS_TYP,CORP_BUS_SUB_TYP,RUT_CORP_ORG,CRD_CORP_ORG,
            CRD_AC_TYP,ORD_AMT,ORD_RFD_AMT,ORD_STS,REQ_ORD_NO,FUD_ORD_NO,RMK
        from cpi_fund_order
        where 1 = 1
        <if test="mblNo != null and mblNo != ''" >
            and MBL_NO = #{mblNo,jdbcType=VARCHAR}
        </if>
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="fudOrdNo != null and fudOrdNo != ''" >
            and FUD_ORD_NO = #{fudOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="corpBusTyp != null and corpBusTyp != ''" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="rutCorpOrg != null and rutCorpOrg != ''" >
            and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="crdCorpOrg != null and crdCorpOrg != ''" >
            and CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and ORD_DT >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and ORD_DT <= #{endDate,jdbcType=DATE} ]]>
        </if>
        and ORD_STS = 'A1'
        order by ord_dt desc, ord_tm desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--根据条件，查询退款订单总笔数-->
    <select id="getFundOrderListTotNum" resultType="java.lang.Integer">
        select count(1)
        from cpi_fund_order
        where 1 = 1
        <if test="mblNo != null and mblNo != ''" >
            and MBL_NO = #{mblNo,jdbcType=VARCHAR}
        </if>
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="fudOrdNo != null and fudOrdNo != ''" >
            and FUD_ORD_NO = #{fudOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="corpBusTyp != null and corpBusTyp != ''" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="rutCorpOrg != null and rutCorpOrg != ''" >
            and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="crdCorpOrg != null and crdCorpOrg != ''" >
            and CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="ordSts != null and ordSts != ''" >
            and ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and ORD_DT >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and ORD_DT <= #{endDate,jdbcType=DATE} ]]>
        </if>
    </select>

    <!--根据条件，查询退款订单总笔数-->
    <select id="getFundOrderListTotNumOnlyWaitReview" resultType="java.lang.Integer">
        select count(1)
        from cpi_fund_order
        where 1 = 1
        <if test="mblNo != null and mblNo != ''" >
            and MBL_NO = #{mblNo,jdbcType=VARCHAR}
        </if>
        <if test="reqOrdNo != null and reqOrdNo != ''" >
            and REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="fudOrdNo != null and fudOrdNo != ''" >
            and FUD_ORD_NO = #{fudOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="corpBusTyp != null and corpBusTyp != ''" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="rutCorpOrg != null and rutCorpOrg != ''" >
            and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="crdCorpOrg != null and crdCorpOrg != ''" >
            and CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="ordSts != null and ordSts != ''" >
            and ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and ORD_DT >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and ORD_DT <= #{endDate,jdbcType=DATE} ]]>
        </if>
        and ORD_STS = 'A1'
    </select>

    <!--根据充值订单号，查询汇款订单详细信息-->
    <select id="getRemitOrderDetail" resultMap="RemitBaseResultMap">
        select
             t1.MBL_NO,t1.USER_ID,t1.ORD_DT,t1.ORD_TM,t1.RUT_CORP_ORG,t1.CRD_CORP_ORG,
             t1.ORD_STS,t2.PIC_URL,t2.REASON,t2.RMK,t2.ORD_AMT
        from cpi_fund_order t1,
             cpi_remit_order t2
        where t1.FUD_ORD_NO = t2.FND_ORD_NO
        and t2.FND_ORD_NO = #{fndOrdNo,jdbcType=VARCHAR}
    </select>


</mapper>