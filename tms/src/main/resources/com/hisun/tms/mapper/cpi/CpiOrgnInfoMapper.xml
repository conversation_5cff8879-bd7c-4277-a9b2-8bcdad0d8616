<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.OrgnInfoDO" >
        <id column="ORG_INF_ID" property="orgInfId" jdbcType="VARCHAR" />
        <result column="CORP_ORG_ID" property="corpOrgId" jdbcType="VARCHAR" />
        <result column="CORP_ORG_NM" property="corpOrgNm" jdbcType="VARCHAR" />
        <result column="CORP_ORG_SNM" property="corpOrgSnm" jdbcType="VARCHAR" />
        <result column="CORP_ORG_TYP" property="corpOrgTyp" jdbcType="CHAR" />
        <result column="CRE_OPR_ID" property="creOprId" jdbcType="VARCHAR" />
        <result column="UPD_OPR_ID" property="updOprId" jdbcType="VARCHAR" />
        <result column="CORP_ACC_NM" property="corpAccNm" jdbcType="VARCHAR" />
        <result column="CORP_ACC_NO" property="corpAccNo" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ORG_INF_ID, CORP_ORG_ID, CORP_ORG_NM, CORP_ORG_SNM, CORP_ORG_TYP,
        CORP_ACC_NM, CORP_ACC_NO, RMK, CRE_OPR_ID, UPD_OPR_ID
    </sql>

    <sql id="Base_Column_List_NM" >
        CORP_ORG_ID, CORP_ORG_NM, CORP_ORG_SNM
    </sql>

    <insert id="insert" parameterType="com.hisun.tms.cpt.model.OrgnInfoDO" >
        insert into cpi_cop_agcy_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orgInfId != null" >
                ORG_INF_ID,
            </if>
            <if test="corpOrgId != null" >
                CORP_ORG_ID,
            </if>
            <if test="corpOrgNm != null" >
                CORP_ORG_NM,
            </if>
            <if test="corpOrgSnm != null" >
                CORP_ORG_SNM,
            </if>
            <if test="corpOrgTyp != null" >
                CORP_ORG_TYP,
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID,
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID,
            </if>
            <if test="corpAccNm != null" >
                CORP_ACC_NM,
            </if>
            <if test="corpAccNo != null" >
                CORP_ACC_NO,
            </if>
            <if test="rmk != null" >
                RMK
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orgInfId != null" >
                #{orgInfId,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgId != null" >
                #{corpOrgId,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgNm != null" >
                #{corpOrgNm,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgSnm != null" >
                #{corpOrgSnm,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgTyp != null" >
                #{corpOrgTyp,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                #{creOprId,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null" >
                #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="corpAccNm != null" >
                #{corpAccNm,jdbcType=VARCHAR},
            </if>
            <if test="corpAccNo != null" >
                #{corpAccNo,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.tms.cpt.model.OrgnInfoDO" >
        update cpi_cop_agcy_info
        <set >
            <if test="corpOrgId != null" >
                CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgNm != null" >
                CORP_ORG_NM = #{corpOrgNm,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgSnm != null" >
                CORP_ORG_SNM = #{corpOrgSnm,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgTyp != null" >
                CORP_ORG_TYP = #{corpOrgTyp,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID = #{creOprId,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="corpAccNm != null" >
                CORP_ACC_NM = #{corpAccNm,jdbcType=VARCHAR},
            </if>
            <if test="corpAccNo != null" >
                CORP_ACC_NO = #{corpAccNo,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR}
            </if>
        </set>
        where ORG_INF_ID = #{orgInfId,jdbcType=VARCHAR}
    </update>

    <!--资金流出模块，查询合作机构基本信息列表-->
    <select id="getOrgnInfoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpi_cop_agcy_info
        where 1 = 1
        <if test="corpOrgId != null and corpOrgId != ''" >
            and CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR}
        </if>
        <if test="corpOrgTyp != null and corpOrgTyp != ''" >
            and CORP_ORG_TYP = #{corpOrgTyp,jdbcType=CHAR}
        </if>
        order by CORP_ORG_ID
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--资金流出模块，查询合作机构基本信息列表总笔数-->
    <select id="getOrgnInfoListTotNum" resultType="java.lang.Integer">
        select count(1)
        from cpi_cop_agcy_info
        where 1 = 1
        <if test="corpOrgId != null and corpOrgId != '' " >
            and CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR}
        </if>
        <if test="corpOrgTyp != null and corpOrgTyp != '' " >
            and CORP_ORG_TYP = #{corpOrgTyp,jdbcType=CHAR}
        </if>
    </select>

    <!--根据合作机构编号ID，查询机构基本信息-->
    <select id="getOrgnInfoByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpi_cop_agcy_info
        where CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR}
    </select>

    <!--资金流出模块，查询资金流出模块所有的合作机构基本信息-->
    <select id="findAllOrgnInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_NM" />
        from cpi_cop_agcy_info
        order by CORP_ORG_ID
    </select>

    <!--根据机构编号，查询合作机构基本信息-->
    <select id="findOrgnInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_NM" />
        from cpi_cop_agcy_info
        where CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR}
    </select>

</mapper>