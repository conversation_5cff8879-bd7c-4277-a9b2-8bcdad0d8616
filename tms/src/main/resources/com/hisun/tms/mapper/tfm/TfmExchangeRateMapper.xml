<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.tfm.dao.ITfmExchangeRateDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.tfm.model.ExchangeRateDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="rate_type" property="rateType" jdbcType="VARCHAR" />
        <result column="source_code" property="sourceCode" jdbcType="VARCHAR" />
        <result column="target_code" property="targetCode" jdbcType="VARCHAR" />
        <result column="network" property="network" jdbcType="VARCHAR" />
        <result column="base_rate" property="baseRate" jdbcType="DECIMAL" />
        <result column="manual_point" property="manualPoint" jdbcType="DECIMAL" />
        <result column="final_rate" property="finalRate" jdbcType="DECIMAL" />
        <result column="rate_source" property="rateSource" jdbcType="VARCHAR" />
        <result column="effective_time" property="effectiveTime" jdbcType="TIMESTAMP" />
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP" />
        <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
        <result column="approved_by" property="approvedBy" jdbcType="VARCHAR" />
        <result column="update_note" property="updateNote" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, rate_type, source_code, target_code, network, base_rate, manual_point, 
        final_rate, rate_source, effective_time, expire_time, update_by, approved_by, 
        update_note, create_time, update_time
    </sql>
    
    <!-- 根据条件查询汇率信息列表 -->
    <select id="findByQueryCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM tfm_exchange_rate
        WHERE 1=1
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="rateType != null and rateType != ''">
            AND rate_type = #{rateType}
        </if>
        <if test="sourceCode != null and sourceCode != ''">
            AND source_code = #{sourceCode}
        </if>
        <if test="targetCode != null and targetCode != ''">
            AND target_code = #{targetCode}
        </if>
        <if test="network != null and network != ''">
            AND network = #{network}
        </if>
        <if test="rateSource != null and rateSource != ''">
            AND rate_source = #{rateSource}
        </if>
        <if test="updateBy != null and updateBy != ''">
            AND update_by LIKE CONCAT('%', #{updateBy}, '%')
        </if>
        <if test="approvedBy != null and approvedBy != ''">
            AND approved_by LIKE CONCAT('%', #{approvedBy}, '%')
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND create_time &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="effectiveStartDate != null and effectiveStartDate != ''">
            AND effective_time &gt;= #{effectiveStartDate}
        </if>
        <if test="effectiveEndDate != null and effectiveEndDate != ''">
            AND effective_time &lt;= #{effectiveEndDate}
        </if>
        ORDER BY create_time DESC
        <if test="pageBegin != null and pageEnd != null">
            LIMIT #{pageBegin}, #{pageEnd}
        </if>
    </select>
    
    <!-- 查询总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM tfm_exchange_rate
        WHERE 1=1
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="rateType != null and rateType != ''">
            AND rate_type = #{rateType}
        </if>
        <if test="sourceCode != null and sourceCode != ''">
            AND source_code = #{sourceCode}
        </if>
        <if test="targetCode != null and targetCode != ''">
            AND target_code = #{targetCode}
        </if>
        <if test="network != null and network != ''">
            AND network = #{network}
        </if>
        <if test="rateSource != null and rateSource != ''">
            AND rate_source = #{rateSource}
        </if>
        <if test="updateBy != null and updateBy != ''">
            AND update_by LIKE CONCAT('%', #{updateBy}, '%')
        </if>
        <if test="approvedBy != null and approvedBy != ''">
            AND approved_by LIKE CONCAT('%', #{approvedBy}, '%')
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(create_time) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND create_time &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="effectiveStartDate != null and effectiveStartDate != ''">
            AND effective_time &gt;= #{effectiveStartDate}
        </if>
        <if test="effectiveEndDate != null and effectiveEndDate != ''">
            AND effective_time &lt;= #{effectiveEndDate}
        </if>
    </select>
    
    <!-- 根据ID获取汇率信息 -->
    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM tfm_exchange_rate
        WHERE id = #{id}
    </select>
    
    <!-- 插入汇率信息 -->
    <insert id="insert" parameterType="com.hisun.tms.tfm.model.ExchangeRateDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tfm_exchange_rate (
            rate_type, source_code, target_code, network, base_rate, manual_point,
            final_rate, rate_source, effective_time, expire_time, update_by, approved_by,
            update_note, create_time, update_time
        ) VALUES (
            #{rateType}, #{sourceCode}, #{targetCode}, #{network}, #{baseRate}, #{manualPoint},
            #{finalRate}, #{rateSource}, #{effectiveTime}, #{expireTime}, #{updateBy}, #{approvedBy},
            #{updateNote}, #{createTime}, #{updateTime}
        )
    </insert>
    
    <!-- 更新汇率信息 -->
    <update id="update" parameterType="com.hisun.tms.tfm.model.ExchangeRateDO">
        UPDATE tfm_exchange_rate
        <set>
            <if test="rateType != null">
                rate_type = #{rateType},
            </if>
            <if test="sourceCode != null">
                source_code = #{sourceCode},
            </if>
            <if test="targetCode != null">
                target_code = #{targetCode},
            </if>
            <if test="network != null">
                network = #{network},
            </if>
            <if test="baseRate != null">
                base_rate = #{baseRate},
            </if>
            <if test="manualPoint != null">
                manual_point = #{manualPoint},
            </if>
            <if test="finalRate != null">
                final_rate = #{finalRate},
            </if>
            <if test="rateSource != null">
                rate_source = #{rateSource},
            </if>
            <if test="effectiveTime != null">
                effective_time = #{effectiveTime},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="approvedBy != null">
                approved_by = #{approvedBy},
            </if>
            <if test="updateNote != null">
                update_note = #{updateNote},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 删除汇率信息 -->
    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM tfm_exchange_rate
        WHERE id = #{id}
    </delete>
</mapper>