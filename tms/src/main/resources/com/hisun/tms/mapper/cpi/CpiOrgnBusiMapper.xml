<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.OrgnBusiDO" >
        <id column="ORG_BUS_ID" property="orgBusId" jdbcType="VARCHAR" />
        <result column="CORP_ORG_ID" property="corpOrgId" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="BUS_EFF_FLG" property="busEffFlg" jdbcType="CHAR" />
        <result column="CRE_OPR_ID" property="creOprId" jdbcType="CHAR" />
        <result column="UPD_OPR_ID" property="updOprId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ORG_BUS_ID, CORP_ORG_ID, CORP_BUS_TYP, CORP_BUS_SUB_TYP, BUS_EFF_FLG, CRE_OPR_ID, UPD_OPR_ID
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpi_cop_agcy_biz
        where ORG_BUS_ID = #{orgBusId,jdbcType=VARCHAR}
    </select>

    <insert id="insert" parameterType="com.hisun.tms.cpt.model.OrgnBusiDO" >
        insert into cpi_cop_agcy_biz
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orgBusId != null" >
                ORG_BUS_ID,
            </if>
            <if test="corpOrgId != null" >
                CORP_ORG_ID,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="busEffFlg != null" >
                BUS_EFF_FLG,
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID,
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orgBusId != null" >
                #{orgBusId,jdbcType=VARCHAR},
            </if>
            <if test="corpOrgId != null" >
                #{corpOrgId,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="busEffFlg != null" >
                #{busEffFlg,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                #{creOprId,jdbcType=CHAR},
            </if>
            <if test="updOprId != null" >
                #{updOprId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.tms.cpt.model.OrgnBusiDO" >
        update cpi_cop_agcy_biz
        <set >
            <if test="corpOrgId != null" >
                CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="busEffFlg != null" >
                BUS_EFF_FLG = #{busEffFlg,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID = #{creOprId,jdbcType=CHAR},
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
        </set>
        where ORG_BUS_ID = #{orgBusId,jdbcType=VARCHAR}
    </update>

    <!--资金流出模块，查询合作机构业务信息列表-->
    <select id="getOrgnBusiList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpi_cop_agcy_biz
        where 1 = 1
        <if test="corpOrgId != null and corpOrgId != ''" >
            and CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR}
        </if>
        <if test="corpBusTyp != null and corpBusTyp != ''" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="busEffFlg != null and busEffFlg != ''" >
            and BUS_EFF_FLG = #{busEffFlg,jdbcType=CHAR}
        </if>
        order by CORP_ORG_ID,CORP_BUS_TYP,CORP_BUS_SUB_TYP
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--资金流出模块，查询合作机构业务信息列表总笔数-->
    <select id="getOrgnBusiListTotNum" resultType="java.lang.Integer">
        select count(1)
        from cpi_cop_agcy_biz
        where 1 = 1
        <if test="corpOrgId != null and corpOrgId != ''" >
            and CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR}
        </if>
        <if test="corpBusTyp != null and corpBusTyp != ''" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="busEffFlg != null and busEffFlg != ''" >
            and BUS_EFF_FLG = #{busEffFlg,jdbcType=CHAR}
        </if>
    </select>

    <!--查询详细的合作机构业务信息-->
    <select id="getOrgnBusiDO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpi_cop_agcy_biz
        where CORP_ORG_ID = #{corpOrgId,jdbcType=VARCHAR}
          and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
          and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        <if test="busEffFlg != null and busEffFlg != ''" >
            and BUS_EFF_FLG = #{busEffFlg,jdbcType=CHAR}
        </if>
    </select>

</mapper>