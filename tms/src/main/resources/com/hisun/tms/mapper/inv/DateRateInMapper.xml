<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.inv.dao.IDateRateInDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.inv.model.DateRateInfoDO" >
        <id column="rate" property="rate" jdbcType="DECIMAL" />
        <id column="rate_date" property="rateDate" jdbcType="DATE" />
        <result column="pro_id" property="proId" jdbcType="VARCHAR" />
        <result column="pro_name" property="proName" jdbcType="VARCHAR" />
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        rate, rate_date, pro_id, pro_name, create_user_id, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from inv_pro_date_rate
        where rate = #{rate,jdbcType=DECIMAL}
          and rate_date = #{rateDate,jdbcType=DATE}
    </select>

    <delete id="delete" parameterType="map" >
        delete from inv_pro_date_rate
        where rate = #{rate,jdbcType=DECIMAL}
          and rate_date = #{rateDate,jdbcType=DATE}
    </delete>
    
    <delete id="deleteByProIdAndRateDateBetween" parameterType="map" >
        delete from inv_pro_date_rate
        where pro_id = #{proId,jdbcType=VARCHAR}
          <if test="beginDt != null" >
            <![CDATA[and rate_date >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and rate_date <= #{endDt,jdbcType=DATE} ]]>
        </if>
    </delete>

    <insert id="insert" parameterType="com.hisun.tms.inv.model.DateRateInfoDO" >
        insert into inv_pro_date_rate
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="rate != null" >
                rate,
            </if>
            <if test="rateDate != null" >
                rate_date,
            </if>
            <if test="proId != null" >
                pro_id,
            </if>
            <if test="proName != null" >
                pro_name,
            </if>
            <if test="createUserId != null" >
                create_user_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="rate != null" >
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="rateDate != null" >
                #{rateDate,jdbcType=DATE},
            </if>
            <if test="proId != null" >
                #{proId,jdbcType=VARCHAR},
            </if>
            <if test="proName != null" >
                #{proName,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null" >
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.tms.inv.model.DateRateInfoDO" >
        update inv_pro_date_rate
        <set >
            <if test="proId != null" >
                pro_id = #{proId,jdbcType=VARCHAR},
            </if>
            <if test="proName != null" >
                pro_name = #{proName,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null" >
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where rate = #{rate,jdbcType=DECIMAL}
          and rate_date = #{rateDate,jdbcType=DATE}
    </update>
    
    <!--根据条件查询付款订单列表，默认按照订单日期、订单时间排序-->
    <select id="getDateRateList" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from inv_pro_date_rate
        where 1=1
        <if test="proId != null" >
            and pro_id = #{proId,jdbcType=VARCHAR}
        </if>
        <if test="beginDt != null" >
            <![CDATA[and rate_date >= #{beginDt,jdbcType=DATE} ]]>
        </if>
        <if test="endDt != null" >
            <![CDATA[ and rate_date <= #{endDt,jdbcType=DATE} ]]>
        </if>
        order by rate_date desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageEnd,jdbcType=INTEGER}
    </select>
    
    <!--根据条件查询订单列表总笔数-->
    <select id="getDateRateTotNum" resultType="java.lang.Integer">
            select count(1) s
            from inv_pro_date_rate
            where 1=1
            <if test="proId != null" >
	            and pro_id = #{proId,jdbcType=VARCHAR}
	        </if>
            <if test="beginDt != null" >
                <![CDATA[and rate_date >= #{beginDt,jdbcType=DATE} ]]>
            </if>
            <if test="endDt != null" >
                <![CDATA[ and rate_date <= #{endDt,jdbcType=DATE} ]]>
            </if>
    </select>
</mapper>