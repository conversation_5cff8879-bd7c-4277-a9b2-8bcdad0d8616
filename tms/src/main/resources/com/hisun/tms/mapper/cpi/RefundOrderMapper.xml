<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpi.IRefundOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.RefundOrderDO" >
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="ORD_SUCC_DT" property="ordSuccDt" jdbcType="DATE" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
        <result column="ORD_STS" property="ordSts" jdbcType="VARCHAR" />
        <result column="FND_ORD_NO" property="fndOrdNo" jdbcType="VARCHAR" />
        <result column="RFD_ORD_NO" property="rfdOrdNo" jdbcType="VARCHAR" />
        <result column="ORG_JRN_NO" property="orgJrnNo" jdbcType="VARCHAR" />
        <result column="ORG_RSP_MSG" property="orgRspMsg" jdbcType="VARCHAR" />
    </resultMap>

    <!--根据条件，查询退款订单列表，默认按照订单日期、订单时间排序-->
    <select id="getRefundOrderList" resultMap="BaseResultMap">
        select
            t1.USER_ID,t1.ORD_DT,t1.ORD_TM,t1.ORD_SUCC_DT,t1.CORP_BUS_SUB_TYP,t1.RUT_CORP_ORG,t1.ORD_AMT,
            t1.ORD_STS,t2.FND_ORD_NO,t2.RFD_ORD_NO,t2.ORG_JRN_NO,t2.ORG_RSP_MSG
        from
            cpi_refund_order t1,
            cpi_refund_suborder t2
        where t1.RFD_ORD_NO = t2.RFD_ORD_NO
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="rutCorpOrg != null and rutCorpOrg != ''" >
            and t1.RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="ordSts != null and ordSts != ''" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and t1.ORD_DT >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and t1.ORD_DT <= #{endDate,jdbcType=DATE} ]]>
        </if>
        <if test="fndOrdNo != null and fndOrdNo != ''" >
            and t2.FND_ORD_NO = #{fndOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rfdOrdNo != null and rfdOrdNo != ''" >
            and t2.RFD_ORD_NO = #{rfdOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="orgJrnNo != null and orgJrnNo != ''" >
            and t2.ORG_JRN_NO = #{orgJrnNo,jdbcType=VARCHAR}
        </if>
        order by t1.ord_dt desc, t1.ord_tm desc
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--根据条件，查询退款订单总笔数-->
    <select id="getRefundOrderListTotNum" resultType="java.lang.Integer">
        select count(1)
        from
            cpi_refund_order t1,
            cpi_refund_suborder t2
        where t1.RFD_ORD_NO = t2.RFD_ORD_NO
        <if test="userId != null and userId != ''" >
            and t1.USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and t1.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="rutCorpOrg != null and rutCorpOrg != ''" >
            and t1.RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="ordSts != null and ordSts != ''" >
            and t1.ORD_STS = #{ordSts,jdbcType=VARCHAR}
        </if>
        <if test="beginDate != null" >
            <![CDATA[ and t1.ORD_DT >= #{beginDate,jdbcType=DATE} ]]>
        </if>
        <if test="endDate != null" >
            <![CDATA[ and t1.ORD_DT <= #{endDate,jdbcType=DATE} ]]>
        </if>
        <if test="fndOrdNo != null and fndOrdNo != ''" >
            and t2.FND_ORD_NO = #{fndOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="rfdOrdNo != null and rfdOrdNo != ''" >
            and t2.RFD_ORD_NO = #{rfdOrdNo,jdbcType=VARCHAR}
        </if>
        <if test="orgJrnNo != null and orgJrnNo != ''" >
            and t2.ORG_JRN_NO = #{orgJrnNo,jdbcType=VARCHAR}
        </if>
    </select>


</mapper>