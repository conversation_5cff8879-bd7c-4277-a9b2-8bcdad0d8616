<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.urm.dao.UserKybSeqDao">

    <resultMap id="BaseResultMap" type="com.hisun.tms.urm.model.UrmKybSeq">
        <id column="kyb_id" property="kybId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="examine_nm" property="examineNm" jdbcType="VARCHAR"/>
        <result column="examine_status" property="examineStatus" jdbcType="VARCHAR"/>
        <result column="kyb_info" property="kybInfo" jdbcType="LONGVARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="first_audit_user" property="firstAuditUser" jdbcType="VARCHAR"/>
        <result column="first_audit_time" property="firstAuditTime" jdbcType="TIMESTAMP"/>
        <result column="first_audit_result" property="firstAuditResult" jdbcType="VARCHAR"/>
        <result column="first_audit_opinion" property="firstAuditOpinion" jdbcType="VARCHAR"/>
        <result column="second_audit_user" property="secondAuditUser" jdbcType="VARCHAR"/>
        <result column="second_audit_time" property="secondAuditTime" jdbcType="TIMESTAMP"/>
        <result column="second_audit_result" property="secondAuditResult" jdbcType="VARCHAR"/>
        <result column="second_audit_opinion" property="secondAuditOpinion" jdbcType="VARCHAR"/>
        <result column="execute_time" property="executeTime" jdbcType="TIMESTAMP"/>
        <result column="reject_reason" property="rejectReason" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findByUserIdAndExamineStatus" resultMap="BaseResultMap">
        SELECT * FROM urm_kyb_seq
        <where>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="examineStatus != null and examineStatus != ''">
                AND examine_status = #{examineStatus}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT * FROM urm_kyb_seq
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>
    
    <select id="findFirstAudit" resultMap="BaseResultMap">
        SELECT * FROM urm_kyb_seq
        <where>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="examineStatus != null and examineStatus != ''">
                AND examine_status = #{examineStatus}
            </if>
            <if test="examineStatus == null or examineStatus == ''">
                AND (examine_status not in ('03','06'))

            </if>
        </where>
        ORDER BY create_time DESC
        <choose>
            <when test="pageNum != null and pageSize != null">
                <bind name="offset" value="(pageNum - 1) * pageSize"/>
                LIMIT ${offset}, #{pageSize}
            </when>
            <otherwise>
                LIMIT 1000
            </otherwise>
        </choose>
    </select>

    <select id="findSecondAudit" resultMap="BaseResultMap">
        SELECT * FROM urm_kyb_seq
        <where>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            AND examine_status = '03'
            AND first_audit_result = '00'
        </where>
        ORDER BY create_time DESC
        <choose>
            <when test="pageNum != null and pageSize != null">
                <bind name="offset" value="(pageNum - 1) * pageSize"/>
                LIMIT ${offset}, #{pageSize}
            </when>
            <otherwise>
                LIMIT 1000
            </otherwise>
        </choose>
    </select>

    <select id="get" resultMap="BaseResultMap">
        SELECT * FROM urm_kyb_seq
        WHERE kyb_id = #{kybId}
    </select>

    <update id="update" parameterType="com.hisun.tms.urm.model.UrmKybSeq">
        UPDATE urm_kyb_seq
        <set>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="examineStatus != null">examine_status = #{examineStatus},</if>
            <if test="examineNm != null">examine_nm = #{examineNm},</if>
            <if test="firstAuditUser != null">first_audit_user = #{firstAuditUser},</if>
            <if test="firstAuditTime != null">first_audit_time = #{firstAuditTime},</if>
            <if test="firstAuditResult != null">first_audit_result = #{firstAuditResult},</if>
            <if test="firstAuditOpinion != null">first_audit_opinion = #{firstAuditOpinion},</if>
            <if test="secondAuditUser != null">second_audit_user = #{secondAuditUser},</if>
            <if test="secondAuditTime != null">second_audit_time = #{secondAuditTime},</if>
            <if test="secondAuditResult != null">second_audit_result = #{secondAuditResult},</if>
            <if test="secondAuditOpinion != null">second_audit_opinion = #{secondAuditOpinion},</if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="kybInfo != null">kyb_info = #{kybInfo}</if>
        </set>
        WHERE kyb_id = #{kybId}
    </update>
</mapper>