<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.acm.dao.IAcmTotChkInfDao" >

    <select id="queryTotChkInf" resultType="java.util.Map" parameterType="java.time.LocalDate">
        select  a.itm_no, b.itm_cnm,
        (case when a.bal_drt = 'D' then a.LDG_DR_BAL else a.LDG_CR_BAL end) ldg_bal,
        (case when a.bal_drt = 'D' then a.GL_DR_BL else a.GL_CR_BL end) gl_bal
        from acm_tot_chkinf A LEFT JOIN acm_itm_inf B on a.itm_no = b.itm_no
        where a.ac_dt = #{acDt,jdbcType=DATE} AND CHK_RSL = '1'
    </select>

</mapper>