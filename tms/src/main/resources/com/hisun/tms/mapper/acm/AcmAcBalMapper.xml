<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.acm.dao.IAcmAcBalDao">
    <resultMap id="BaseResultMap" type="com.hisun.tms.acm.model.AcmAcBal">
        <id column="AC_NO" property="AC_NO" jdbcType="VARCHAR"/>
        <id column="CAP_TYP" property="capTyp" jdbcType="VARCHAR"/>
        <result column="CCY" property="ccy" jdbcType="VARCHAR"/>
        <result column="USER_ID" property="userId" jdbcType="VARCHAR"/>
        <result column="AC_CUR_BAL" property="acCurBal" jdbcType="DECIMAL"/>
        <result column="AC_UAVA_BAL" property="acUavaBal" jdbcType="DECIMAL"/>
        <result column="AC_LAST_BAL" property="acLastBal" jdbcType="DECIMAL"/>
        <result column="AC_LAST_UAVA_BAL" property="acLastUavaBal" jdbcType="DECIMAL"/>
        <result column="AC_BAL_TAG" property="acBalTag" jdbcType="VARCHAR"/>
        <result column="AC_UPD_DT" property="acUpdDt" jdbcType="DATE"/>
        <result column="AC_UPD_TM" property="acUpdTm" jdbcType="TIME"/>
        <result column="AC_FRZ_DT" property="acFrzDt" jdbcType="DATE"/>
        <result column="AC_FRZ_TM" property="acFrzTm" jdbcType="TIME"/>
        <result column="RMK" property="rmk" jdbcType="VARCHAR"/>
        <result column="AC_CUR_FREEZE_BAL" property="acCurFreezeBal" jdbcType="DECIMAL"/>
    </resultMap>

    <insert id="insert" parameterType="com.hisun.tms.acm.model.AcmAcBal">
        insert into acm_ac_bal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="AC_NO != null">
                AC_NO,
            </if>
            <if test="capTyp!= null">
                CAP_TYP,
            </if>
            <if test="ccy != null">
                CCY,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="acCurBal != null">
                AC_CUR_BAL,
            </if>
            <if test="acUavaBal != null">
                AC_UAVA_BAL,
            </if>
            <if test="acLastBal != null">
                AC_LAST_BAL,
            </if>
            <if test="acLastUavaBal != null">
                AC_LAST_UAVA_BAL,
            </if>
            <if test="acBalTag != null">
                AC_BAL_TAG,
            </if>
            <if test="acUpdDt != null">
                AC_UPD_DT,
            </if>
            <if test="acUpdTm != null">
                AC_UPD_TM,
            </if>
            <if test="acFrzDt != null">
                AC_FRZ_DT,
            </if>
            <if test="acFrzTm != null">
                AC_FRZ_TM,
            </if>
            <if test="rmk != null">
                RMK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="AC_NO != null">
                #{AC_NO,jdbcType=VARCHAR},
            </if>
            <if test="capTyp!= null">
                #{capTyp,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null">
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="acCurBal != null">
                #{acCurBal,jdbcType=DECIMAL},
            </if>
            <if test="acUavaBal != null">
                #{acUavaBal,jdbcType=DECIMAL},
            </if>
            <if test="acLastBal != null">
                #{acLastBal,jdbcType=DECIMAL},
            </if>
            <if test="acLastUavaBal != null">
                #{acLastUavaBal,jdbcType=DECIMAL},
            </if>
            <if test="acBalTag != null">
                #{acBalTag,jdbcType=VARCHAR},
            </if>
            <if test="acUpdDt != null">
                #{acUpdDt,jdbcType=DATE},
            </if>
            <if test="acUpdTm != null">
                #{acUpdTm,jdbcType=TIME},
            </if>
            <if test="acFrzDt != null">
                #{acFrzDt,jdbcType=DATE},
            </if>
            <if test="acFrzTm != null">
                #{acFrzTm,jdbcType=TIME},
            </if>
            <if test="rmk != null">
                #{rmk,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

</mapper>