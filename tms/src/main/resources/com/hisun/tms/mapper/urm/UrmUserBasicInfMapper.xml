<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.urm.dao.IUrmUserBasicInfDao" >

    <select id="countUser" resultType="java.util.Map" parameterType="java.lang.String">
        select usr_reg_dt reg_dt, count(1) new_user_num
        from urm_user_basic_inf
        where usr_reg_dt LIKE concat(#{month},'%') and usr_lvl in ('0', '1') group by usr_reg_dt
    </select>

    <select id="sumUserNumber" resultType="java.lang.Integer" parameterType="java.time.LocalDate">
        select count(1)
        from urm_user_basic_inf
        where usr_reg_dt &lt;= #{acDt} and usr_sts = '0' and usr_lvl in ('0', '1')
    </select>

    <select id="countMerc" resultType="java.util.Map" parameterType="java.lang.String">
        select usr_reg_dt reg_dt, count(1) new_user_num
        from urm_user_basic_inf
        where usr_reg_dt LIKE concat(#{month},'%') and usr_lvl in ('2', '3') group by usr_reg_dt
    </select>

    <select id="sumMercNumber" resultType="java.lang.Integer" parameterType="java.time.LocalDate">
        select count(1)
        from urm_user_basic_inf
        where usr_reg_dt &lt;= #{acDt} and usr_sts = '0' and usr_lvl in ('2', '3')
    </select>

    <update id="updateUserKybCert" parameterType="java.lang.String">
        update urm_user_basic_inf
        set KYB_CERT = 'Y'
        where USER_ID = #{userId}
    </update>

    <!-- 用户注册管理相关SQL -->
    <resultMap id="UrmUserBasicInfResultMap" type="com.hisun.tms.urm.model.UrmUserBasicInf">
        <id column="USER_ID" property="userId" jdbcType="VARCHAR"/>
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR"/>
        <result column="USR_STS" property="usrSts" jdbcType="CHAR"/>
        <result column="USR_LVL" property="usrLvl" jdbcType="CHAR"/>
        <result column="ID_CHK_FLG" property="idChkFlg" jdbcType="CHAR"/>
        <result column="ID_CHK_NO" property="idChkNo" jdbcType="VARCHAR"/>
        <result column="ID_CHK_DT" property="idChkDt" jdbcType="DATE"/>
        <result column="ID_CHK_TM" property="idChkTm" jdbcType="TIME"/>
        <result column="ID_TYPE" property="idType" jdbcType="VARCHAR"/>
        <result column="ID_NO" property="idNo" jdbcType="VARCHAR"/>
        <result column="ID_NO_HID" property="idNoHid" jdbcType="VARCHAR"/>
        <result column="USR_NM" property="usrNm" jdbcType="VARCHAR"/>
        <result column="USR_NM_HID" property="usrNmHid" jdbcType="VARCHAR"/>
        <result column="USR_GENDER" property="usrGender" jdbcType="CHAR"/>
        <result column="USR_NATION" property="usrNation" jdbcType="VARCHAR"/>
        <result column="USR_BIRTH_DT" property="usrBirthDt" jdbcType="VARCHAR"/>
        <result column="ISSU_AUTH" property="issuAuth" jdbcType="VARCHAR"/>
        <result column="ID_EFF_DT" property="idEffDt" jdbcType="VARCHAR"/>
        <result column="ID_EXP_DT" property="idExpDt" jdbcType="VARCHAR"/>
        <result column="USR_REG_CNL" property="usrRegCnl" jdbcType="VARCHAR"/>
        <result column="USR_REG_IP" property="usrRegIp" jdbcType="VARCHAR"/>
        <result column="USR_REG_DT" property="usrRegDt" jdbcType="DATE"/>
        <result column="USR_REG_TM" property="usrRegTm" jdbcType="TIME"/>
        <result column="USR_CLS_DT" property="usrClsDt" jdbcType="DATE"/>
        <result column="USR_CLS_TM" property="usrClsTm" jdbcType="TIME"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="KYB_CERT" property="kybCert" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findUserRegisterList" resultMap="UrmUserBasicInfResultMap" parameterType="java.util.Map">
        SELECT * FROM urm_user_basic_inf
        <where>
            <if test="userId != null and userId != ''">
                AND USER_ID LIKE CONCAT('%', #{userId}, '%')
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
        <if test="start != null and length != null">
            LIMIT #{start}, #{length}
        </if>
    </select>

    <select id="countUserRegisterTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT COUNT(1) FROM urm_user_basic_inf
        <where>
            <if test="userId != null and userId != ''">
                AND USER_ID LIKE CONCAT('%', #{userId}, '%')
            </if>
        </where>
    </select>

    <select id="getUserRegisterById" resultMap="UrmUserBasicInfResultMap" parameterType="java.lang.String">
        SELECT * FROM urm_user_basic_inf WHERE USER_ID = #{userId}
    </select>

    <insert id="insertUserRegister" parameterType="com.hisun.tms.urm.model.UrmUserBasicInf">
        INSERT INTO urm_user_basic_inf (
            USER_ID, MBL_NO, USR_STS, USR_LVL, ID_CHK_FLG, ID_CHK_NO, ID_CHK_DT, ID_CHK_TM,
            ID_TYPE, ID_NO, ID_NO_HID, USR_NM, USR_NM_HID, USR_GENDER, USR_NATION, USR_BIRTH_DT,
            ISSU_AUTH, ID_EFF_DT, ID_EXP_DT, USR_REG_CNL, USR_REG_IP, USR_REG_DT, USR_REG_TM,
            USR_CLS_DT, USR_CLS_TM, MODIFY_TIME, CREATE_TIME, KYB_CERT
        ) VALUES (
            #{userId}, #{mblNo}, #{usrSts}, #{usrLvl}, #{idChkFlg}, #{idChkNo}, #{idChkDt}, #{idChkTm},
            #{idType}, #{idNo}, #{idNoHid}, #{usrNm}, #{usrNmHid}, #{usrGender}, #{usrNation}, #{usrBirthDt},
            #{issuAuth}, #{idEffDt}, #{idExpDt}, #{usrRegCnl}, #{usrRegIp}, #{usrRegDt}, #{usrRegTm},
            #{usrClsDt}, #{usrClsTm}, #{modifyTime}, #{createTime}, #{kybCert}
        )
    </insert>

    <update id="updateUserRegister" parameterType="com.hisun.tms.urm.model.UrmUserBasicInf">
        UPDATE urm_user_basic_inf SET
            MBL_NO = #{mblNo},
            USR_STS = #{usrSts},
            USR_LVL = #{usrLvl},
            ID_CHK_FLG = #{idChkFlg},
            ID_CHK_NO = #{idChkNo},
            ID_CHK_DT = #{idChkDt},
            ID_CHK_TM = #{idChkTm},
            ID_TYPE = #{idType},
            ID_NO = #{idNo},
            ID_NO_HID = #{idNoHid},
            USR_NM = #{usrNm},
            USR_NM_HID = #{usrNmHid},
            USR_GENDER = #{usrGender},
            USR_NATION = #{usrNation},
            USR_BIRTH_DT = #{usrBirthDt},
            ISSU_AUTH = #{issuAuth},
            ID_EFF_DT = #{idEffDt},
            ID_EXP_DT = #{idExpDt},
            USR_REG_CNL = #{usrRegCnl},
            USR_REG_IP = #{usrRegIp},
            USR_REG_DT = #{usrRegDt},
            USR_REG_TM = #{usrRegTm},
            USR_CLS_DT = #{usrClsDt},
            USR_CLS_TM = #{usrClsTm},
            MODIFY_TIME = #{modifyTime},
            KYB_CERT = #{kybCert}
        WHERE USER_ID = #{userId}
    </update>

    <update id="deleteUserRegister" parameterType="java.lang.String">
        UPDATE urm_user_basic_inf SET USR_STS = '1', MODIFY_TIME = NOW() WHERE USER_ID = #{userId}
    </update>

</mapper>
