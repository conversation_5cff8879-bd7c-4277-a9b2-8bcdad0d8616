<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.csh.dao.IOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.csh.entity.OrderDO" >
        <id column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="order_tm" property="orderTm" jdbcType="TIMESTAMP" />
        <result column="payer_id" property="payerId" jdbcType="VARCHAR" />
        <result column="tx_type" property="txType" jdbcType="VARCHAR" />
        <result column="app_cnl" property="appCnl" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="bus_order_no" property="busOrderNo" jdbcType="VARCHAR" />
        <result column="pay_jrn_no" property="payJrnNo" jdbcType="VARCHAR" />
        <result column="jrn_tx_tm" property="jrnTxTm" jdbcType="TIMESTAMP" />
        <result column="ac_tm" property="acTm" jdbcType="DATE" />
        <result column="fnd_order_no" property="fndOrderNo" jdbcType="VARCHAR" />
        <result column="payee_id" property="payeeId" jdbcType="VARCHAR" />
        <result column="order_channel" property="orderChannel" jdbcType="VARCHAR" />
        <result column="sys_channel" property="sysChannel" jdbcType="VARCHAR" />
        <result column="pay_type" property="payType" jdbcType="VARCHAR" />
        <result column="bus_pay_type" property="busPayType" jdbcType="VARCHAR" />
        <result column="order_amt" property="orderAmt" jdbcType="DECIMAL" />
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR" />
        <result column="order_exp_tm" property="orderExpTm" jdbcType="TIMESTAMP" />
        <result column="order_succ_tm" property="orderSuccTm" jdbcType="TIMESTAMP" />
        <result column="coupon_no" property="couponNo" jdbcType="VARCHAR" />
        <result column="cap_corg_no" property="capCorgNo" jdbcType="VARCHAR" />
        <result column="cap_card_type" property="capCardType" jdbcType="VARCHAR" />
        <result column="crd_pay_type" property="crdPayType" jdbcType="VARCHAR" />
        <result column="crd_pay_amt" property="crdPayAmt" jdbcType="DECIMAL" />
        <result column="bal_amt" property="balAmt" jdbcType="DECIMAL" />
        <result column="inv_amt" property="invAmt" jdbcType="DECIMAL" />
        <result column="coupon_amt" property="couponAmt" jdbcType="DECIMAL" />
        <result column="coupon_type" property="couponType" jdbcType="VARCHAR" />
        <result column="goods_info" property="goodsInfo" jdbcType="VARCHAR" />
        <result column="left_total_amt" property="leftTotalAmt" jdbcType="DECIMAL" />
        <result column="left_bal_amt" property="leftBalAmt" jdbcType="DECIMAL" />
        <result column="left_card_amt" property="leftCardAmt" jdbcType="DECIMAL" />
        <result column="left_inv_amt" property="leftInvAmt" jdbcType="DECIMAL" />
        <result column="left_coupon_amt" property="leftCouponAmt" jdbcType="DECIMAL" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="fee_flag" property="feeFlag" jdbcType="VARCHAR" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL" />
        <result column="left_fee" property="leftFee" jdbcType="DECIMAL" />
        <result column="merc_name" property="mercName" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tx_hash" property="txHash" jdbcType="VARCHAR" />
        <result column="file_url" property="fileUrl" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        order_no, order_tm, payer_id, tx_type, app_cnl, bus_type, bus_order_no, pay_jrn_no, ccy,
        jrn_tx_tm, ac_tm, fnd_order_no, payee_id, order_channel, sys_channel, pay_type, bus_pay_type,
        order_amt, order_status, order_exp_tm, order_succ_tm, cap_corg_no, cap_card_type,
        crd_pay_type, crd_pay_amt, bal_amt, inv_amt, coupon_amt, coupon_type, goods_info,
        left_total_amt, left_bal_amt, left_card_amt, left_inv_amt,coupon_no, left_coupon_amt, fee,total_amt,fee_flag,
        left_fee, merc_name, remark, create_time, modify_time, tx_hash, file_url
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="getByBusOrder" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        where bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from csh_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.tms.csh.entity.OrderDO" >
        insert into csh_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="orderTm != null" >
                order_tm,
            </if>
            <if test="payerId != null" >
                payer_id,
            </if>
            <if test="txType != null" >
                tx_type,
            </if>
            <if test="appCnl != null" >
                app_cnl,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="couponNo != null" >
                coupon_no,
            </if>
            <if test="busOrderNo != null" >
                bus_order_no,
            </if>
            <if test="payJrnNo != null" >
                pay_jrn_no,
            </if>
            <if test="jrnTxTm != null" >
                jrn_tx_tm,
            </if>
            <if test="acTm != null" >
                ac_tm,
            </if>
            <if test="fndOrderNo != null" >
                fnd_order_no,
            </if>
            <if test="payeeId != null" >
                payee_id,
            </if>
            <if test="orderChannel != null" >
                order_channel,
            </if>
            <if test="sysChannel != null" >
                sys_channel,
            </if>
            <if test="payType != null" >
                pay_type,
            </if>
            <if test="busPayType != null" >
                bus_pay_type,
            </if>
            <if test="orderAmt != null" >
                order_amt,
            </if>
            <if test="totalAmt != null" >
                total_amt,
            </if>
            <if test="orderStatus != null" >
                order_status,
            </if>
            <if test="orderExpTm != null" >
                order_exp_tm,
            </if>
            <if test="orderSuccTm != null" >
                order_succ_tm,
            </if>
            <if test="capCorgNo != null" >
                cap_corg_no,
            </if>
            <if test="capCardType != null" >
                cap_card_type,
            </if>
            <if test="crdPayType != null" >
                crd_pay_type,
            </if>
            <if test="crdPayAmt != null" >
                crd_pay_amt,
            </if>
            <if test="balAmt != null" >
                bal_amt,
            </if>
            <if test="invAmt != null" >
                inv_amt,
            </if>
            <if test="couponAmt != null" >
                coupon_amt,
            </if>
            <if test="couponType != null" >
                coupon_type,
            </if>
            <if test="goodsInfo != null" >
                goods_info,
            </if>
            <if test="leftTotalAmt != null" >
                left_total_amt,
            </if>
            <if test="leftBalAmt != null" >
                left_bal_amt,
            </if>
            <if test="leftCardAmt != null" >
                left_card_amt,
            </if>
            <if test="leftInvAmt != null" >
                left_inv_amt,
            </if>
            <if test="leftCouponAmt != null" >
                left_coupon_amt,
            </if>
            <if test="fee != null" >
                fee,
            </if>
            <if test="leftFee != null" >
                left_fee,
            </if>

            <if test="feeFlag != null" >
                fee_flag,
            </if>
            <if test="ccy != null" >
                ccy,
            </if>
            <if test="mercName != null" >
                merc_name,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderTm != null" >
                #{orderTm,jdbcType=TIMESTAMP},
            </if>
            <if test="payerId != null" >
                #{payerId,jdbcType=VARCHAR},
            </if>
            <if test="txType != null" >
                #{txType,jdbcType=VARCHAR},
            </if>
            <if test="appCnl != null" >
                #{appCnl,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="couponNo != null" >
                #{couponNo,jdbcType=VARCHAR},
            </if>
            <if test="busOrderNo != null" >
                #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payJrnNo != null" >
                #{payJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="jrnTxTm != null" >
                #{jrnTxTm,jdbcType=TIMESTAMP},
            </if>
            <if test="acTm != null" >
                #{acTm,jdbcType=DATE},
            </if>
            <if test="fndOrderNo != null" >
                #{fndOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payeeId != null" >
                #{payeeId,jdbcType=VARCHAR},
            </if>
            <if test="orderChannel != null" >
                #{orderChannel,jdbcType=VARCHAR},
            </if>
            <if test="sysChannel != null" >
                #{sysChannel,jdbcType=VARCHAR},
            </if>
            <if test="payType != null" >
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="busPayType != null" >
                #{busPayType,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                #{orderAmt,jdbcType=DECIMAL},
            </if>

            <if test="totalAmt != null" >
                #{totalAmt,jdbcType=DECIMAL},
            </if>

            <if test="orderStatus != null" >
                #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="orderExpTm != null" >
                #{orderExpTm,jdbcType=TIMESTAMP},
            </if>
            <if test="orderSuccTm != null" >
                #{orderSuccTm,jdbcType=TIMESTAMP},
            </if>
            <if test="capCorgNo != null" >
                #{capCorgNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardType != null" >
                #{capCardType,jdbcType=VARCHAR},
            </if>
            <if test="crdPayType != null" >
                #{crdPayType,jdbcType=VARCHAR},
            </if>
            <if test="crdPayAmt != null" >
                #{crdPayAmt,jdbcType=DECIMAL},
            </if>
            <if test="balAmt != null" >
                #{balAmt,jdbcType=DECIMAL},
            </if>
            <if test="invAmt != null" >
                #{invAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponAmt != null" >
                #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponType != null" >
                #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null" >
                #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="leftTotalAmt != null" >
                #{leftTotalAmt,jdbcType=DECIMAL},
            </if>
            <if test="leftBalAmt != null" >
                #{leftBalAmt,jdbcType=DECIMAL},
            </if>
            <if test="leftCardAmt != null" >
                #{leftCardAmt,jdbcType=DECIMAL},
            </if>
            <if test="leftInvAmt != null" >
                #{leftInvAmt,jdbcType=DECIMAL},
            </if>
            <if test="leftCouponAmt != null" >
                #{leftCouponAmt,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="leftFee != null" >
                #{leftFee,jdbcType=DECIMAL},
            </if>
            <if test="feeFlag != null" >
                #{feeFlag,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.tms.csh.entity.OrderDO" >
        update csh_order
        <set >
            <if test="orderTm != null" >
                order_tm = #{orderTm,jdbcType=TIMESTAMP},
            </if>
            <if test="payerId != null" >
                payer_id = #{payerId,jdbcType=VARCHAR},
            </if>
            <if test="txType != null" >
                tx_type = #{txType,jdbcType=VARCHAR},
            </if>
            <if test="appCnl != null" >
                app_cnl = #{appCnl,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="busOrderNo != null" >
                bus_order_no = #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payJrnNo != null" >
                pay_jrn_no = #{payJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="jrnTxTm != null" >
                jrn_tx_tm = #{jrnTxTm,jdbcType=TIMESTAMP},
            </if>
            <if test="acTm != null" >
                ac_tm = #{acTm,jdbcType=DATE},
            </if>
            <if test="couponNo != null" >
                coupon_no = #{couponNo,jdbcType=VARCHAR},
            </if>
            <if test="fndOrderNo != null" >
                fnd_order_no = #{fndOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payeeId != null" >
                payee_id = #{payeeId,jdbcType=VARCHAR},
            </if>
            <if test="orderChannel != null" >
                order_channel = #{orderChannel,jdbcType=VARCHAR},
            </if>
            <if test="sysChannel != null" >
                sys_channel = #{sysChannel,jdbcType=VARCHAR},
            </if>
            <if test="payType != null" >
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="busPayType != null" >
                bus_pay_type = #{busPayType,jdbcType=VARCHAR},
            </if>
            <if test="orderAmt != null" >
                order_amt = #{orderAmt,jdbcType=DECIMAL},
            </if>
            <if test="totalAmt != null" >
                total_amt = #{totalAmt,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null" >
                order_status = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="orderExpTm != null" >
                order_exp_tm = #{orderExpTm,jdbcType=TIMESTAMP},
            </if>
            <if test="orderSuccTm != null" >
                order_succ_tm = #{orderSuccTm,jdbcType=TIMESTAMP},
            </if>
            <if test="capCorgNo != null" >
                cap_corg_no = #{capCorgNo,jdbcType=VARCHAR},
            </if>
            <if test="capCardType != null" >
                cap_card_type = #{capCardType,jdbcType=VARCHAR},
            </if>
            <if test="crdPayType != null" >
                crd_pay_type = #{crdPayType,jdbcType=VARCHAR},
            </if>
            <if test="crdPayAmt != null" >
                crd_pay_amt = #{crdPayAmt,jdbcType=DECIMAL},
            </if>
            <if test="balAmt != null" >
                bal_amt = #{balAmt,jdbcType=DECIMAL},
            </if>
            <if test="invAmt != null" >
                inv_amt = #{invAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponAmt != null" >
                coupon_amt = #{couponAmt,jdbcType=DECIMAL},
            </if>
            <if test="couponType != null" >
                coupon_type = #{couponType,jdbcType=VARCHAR},
            </if>
            <if test="goodsInfo != null" >
                goods_info = #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="leftTotalAmt != null" >
                left_total_amt = #{leftTotalAmt,jdbcType=DECIMAL},
            </if>
            <if test="leftBalAmt != null" >
                left_bal_amt = #{leftBalAmt,jdbcType=DECIMAL},
            </if>
            <if test="leftCardAmt != null" >
                left_card_amt = #{leftCardAmt,jdbcType=DECIMAL},
            </if>
            <if test="leftInvAmt != null" >
                left_inv_amt = #{leftInvAmt,jdbcType=DECIMAL},
            </if>
            <if test="leftCouponAmt != null" >
                left_coupon_amt = #{leftCouponAmt,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="leftFee != null" >
                left_fee = #{leftFee,jdbcType=DECIMAL},
            </if>

            <if test="feeFlag != null" >
                fee_flag = #{feeFlag,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                ccy = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                merc_name = #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>

    <select id="getOrderByOrderNoAndPayerId" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        where order_no = #{orderNo,jdbcType=VARCHAR} and payer_id = #{payerId,jdbcType=VARCHAR}
    </select>

    <select id="queryList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
<where>
            <if test="acTm != null">
                <![CDATA[and ac_tm = #{acTm}]]>
            </if>

            <if test="txTypeList != null">
                and tx_type in
                <foreach item="item" index="index" collection="txTypeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="statusList != null">
                and order_status in
                <foreach item="item" index="index" collection="statusList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="appCnl != null">
                <![CDATA[and app_cnl = #{appCnl}]]>
            </if>

        </where>
    </select>

    <select id="queryNotFinalAuditList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        <where>
            tx_type='03' and order_status != 'A1'
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="payerId != null and payerId != ''">
                and payer_id = #{payerId}
            </if>
            <if test="payeeId != null and payeeId != ''">
                and payee_id = #{payeeId}
            </if>
            <if test="busOrderNo != null and busOrderNo != ''">
                and bus_order_no = #{busOrderNo}
            </if>
            <if test="orderStatus != null and orderStatus != '' and orderStatus != 'A1'">
                and order_status = #{orderStatus}
            </if>
        </where>
        order by order_tm desc
        <if test="pageBegin != null and pageNum != null">
            limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
        </if>
    </select>
    <select id="DCqueryNotFinalAuditList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        <where>
            tx_type='DZ' and order_status != 'A1'
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="payerId != null and payerId != ''">
                and payer_id = #{payerId}
            </if>
            <if test="payeeId != null and payeeId != ''">
                and payee_id = #{payeeId}
            </if>
            <if test="busOrderNo != null and busOrderNo != ''">
                and bus_order_no = #{busOrderNo}
            </if>
            <if test="orderStatus != null and orderStatus != '' and orderStatus != 'A1'">
                and order_status = #{orderStatus}
            </if>
        </where>
        order by order_tm desc
        <if test="pageBegin != null and pageNum != null">
            limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
        </if>
    </select>

    <select id="DXqueryNotFinalAuditList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        <where>
            tx_type='DX' and order_status != 'A1'
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="payerId != null and payerId != ''">
                and payer_id = #{payerId}
            </if>
            <if test="payeeId != null and payeeId != ''">
                and payee_id = #{payeeId}
            </if>
            <if test="busOrderNo != null and busOrderNo != ''">
                and bus_order_no = #{busOrderNo}
            </if>
            <if test="orderStatus != null and orderStatus != '' and orderStatus != 'A1'">
                and order_status = #{orderStatus}
            </if>
        </where>
        order by order_tm desc
        <if test="pageBegin != null and pageNum != null">
            limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
        </if>
    </select>

    <select id="queryWaitReviewList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        <where>
            tx_type='03' and order_status = 'A1'
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="payerId != null and payerId != ''">
                and payer_id = #{payerId}
            </if>
            <if test="payeeId != null and payeeId != ''">
                and payee_id = #{payeeId}
            </if>
            <if test="busOrderNo != null and busOrderNo != ''">
                and bus_order_no = #{busOrderNo}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus}
            </if>
        </where>
        order by order_tm desc
        <if test="pageBegin != null and pageNum != null">
            limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
        </if>
    </select>

    <select id="DCqueryWaitReviewList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        <where>
            tx_type='DZ' and order_status = 'A1'
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="payerId != null and payerId != ''">
                and payer_id = #{payerId}
            </if>
            <if test="payeeId != null and payeeId != ''">
                and payee_id = #{payeeId}
            </if>
            <if test="busOrderNo != null and busOrderNo != ''">
                and bus_order_no = #{busOrderNo}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus}
            </if>
        </where>
        order by order_tm desc
        <if test="pageBegin != null and pageNum != null">
            limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
        </if>
    </select>

    <select id="DXqueryWaitReviewList" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        <where>
            tx_type='DX' and order_status = 'A1'
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="payerId != null and payerId != ''">
                and payer_id = #{payerId}
            </if>
            <if test="payeeId != null and payeeId != ''">
                and payee_id = #{payeeId}
            </if>
            <if test="busOrderNo != null and busOrderNo != ''">
                and bus_order_no = #{busOrderNo}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus}
            </if>
        </where>
        order by order_tm desc
        <if test="pageBegin != null and pageNum != null">
            limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
        </if>
    </select>


    <select id="queryNotFinalAuditListCount" resultType="java.lang.Integer" parameterType="java.util.Map" >
        select
        count(1)

        from csh_order
        where tx_type='03' and order_status != 'A1'
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="payerId != null and payerId != ''">
            and payer_id = #{payerId,jdbcType=VARCHAR}
        </if>
        <if test="payeeId != null and payeeId != ''">
            and payee_id = #{payeeId,jdbcType=VARCHAR}
        </if>
        <if test="busOrderNo != null and busOrderNo != ''">
            and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus != null and orderStatus != ''and orderStatus != 'A1'">
            and order_status = #{orderStatus,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="DCqueryNotFinalAuditListCount" resultType="java.lang.Integer" parameterType="java.util.Map" >
        select
        count(1)

        from csh_order
        where tx_type='DZ' and order_status != 'A1'
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="payerId != null and payerId != ''">
            and payer_id = #{payerId,jdbcType=VARCHAR}
        </if>
        <if test="payeeId != null and payeeId != ''">
            and payee_id = #{payeeId,jdbcType=VARCHAR}
        </if>
        <if test="busOrderNo != null and busOrderNo != ''">
            and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus != null and orderStatus != ''and orderStatus != 'A1'">
            and order_status = #{orderStatus,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="DXqueryNotFinalAuditListCount" resultType="java.lang.Integer" parameterType="java.util.Map" >
        select
        count(1)

        from csh_order
        where tx_type='DX' and order_status != 'A1'
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="payerId != null and payerId != ''">
            and payer_id = #{payerId,jdbcType=VARCHAR}
        </if>
        <if test="payeeId != null and payeeId != ''">
            and payee_id = #{payeeId,jdbcType=VARCHAR}
        </if>
        <if test="busOrderNo != null and busOrderNo != ''">
            and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus != null and orderStatus != ''and orderStatus != 'A1'">
            and order_status = #{orderStatus,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="queryListCount" resultType="java.lang.Integer" parameterType="java.util.Map" >
        select
        count(1)

        from csh_order
        where tx_type='03' and order_status = 'A1'
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="payerId != null and payerId != ''">
            and payer_id = #{payerId,jdbcType=VARCHAR}
        </if>
        <if test="payeeId != null and payeeId != ''">
            and payee_id = #{payeeId,jdbcType=VARCHAR}
        </if>
        <if test="busOrderNo != null and busOrderNo != ''">
            and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            and order_status = #{orderStatus,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="DCqueryListCount" resultType="java.lang.Integer" parameterType="java.util.Map" >
        select
        count(1)

        from csh_order
        where tx_type='DZ' and order_status = 'A1'
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="payerId != null and payerId != ''">
            and payer_id = #{payerId,jdbcType=VARCHAR}
        </if>
        <if test="payeeId != null and payeeId != ''">
            and payee_id = #{payeeId,jdbcType=VARCHAR}
        </if>
        <if test="busOrderNo != null and busOrderNo != ''">
            and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            and order_status = #{orderStatus,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="DXqueryListCount" resultType="java.lang.Integer" parameterType="java.util.Map" >
        select
        count(1)

        from csh_order
        where tx_type='DX' and order_status = 'A1'
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="payerId != null and payerId != ''">
            and payer_id = #{payerId,jdbcType=VARCHAR}
        </if>
        <if test="payeeId != null and payeeId != ''">
            and payee_id = #{payeeId,jdbcType=VARCHAR}
        </if>
        <if test="busOrderNo != null and busOrderNo != ''">
            and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus != null and orderStatus != ''">
            and order_status = #{orderStatus,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="queryCpiOrder" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        <where>
            (crd_pay_type='1' or crd_pay_type='2' or crd_pay_type='3')
            <if test="acTm != null">
                <![CDATA[and ac_tm = #{acTm}]]>
            </if>

            <if test="txTypeList != null">
                and tx_type in
                <foreach item="item" index="index" collection="txTypeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="statusList != null">
                and order_status in
                <foreach item="item" index="index" collection="statusList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryExpOrders" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from csh_order
        <where>
            (order_status='W' or order_status='P')
            <if test="nowTm != null">
              and order_exp_tm <![CDATA[ <= ]]> #{nowTm}
            </if>

        </where>
    </select>
</mapper>