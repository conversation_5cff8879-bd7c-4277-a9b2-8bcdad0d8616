<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.cpo.ICpoOrgnRouteDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.model.OrgnRouteDO" >
        <id column="RUT_INF_ID" property="rutInfId" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="RUT_EFF_FLG" property="rutEffFlg" jdbcType="CHAR" />
        <result column="PRI_LVL" property="priLvl" jdbcType="INTEGER" />
        <result column="LOW_AMT" property="lowAmt" jdbcType="DECIMAL" />
        <result column="HIGH_AMT" property="highAmt" jdbcType="DECIMAL" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        RUT_INF_ID, CRD_CORP_ORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, RUT_CORP_ORG, CRD_AC_TYP,
        RUT_EFF_FLG, PRI_LVL, LOW_AMT, HIGH_AMT, OPR_ID, RMK
    </sql>

    <insert id="insert" parameterType="com.hisun.tms.cpt.model.OrgnRouteDO" >
        insert into cpo_cop_biz_route
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="rutInfId != null" >
                RUT_INF_ID,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="rutEffFlg != null" >
                RUT_EFF_FLG,
            </if>
            <if test="priLvl != null" >
                PRI_LVL,
            </if>
            <if test="lowAmt != null" >
                LOW_AMT,
            </if>
            <if test="highAmt != null" >
                HIGH_AMT,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="rutInfId != null" >
                #{rutInfId,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="rutEffFlg != null" >
                #{rutEffFlg,jdbcType=CHAR},
            </if>
            <if test="priLvl != null" >
                #{priLvl,jdbcType=INTEGER},
            </if>
            <if test="lowAmt != null" >
                #{lowAmt,jdbcType=DECIMAL},
            </if>
            <if test="highAmt != null" >
                #{highAmt,jdbcType=DECIMAL},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.tms.cpt.model.OrgnRouteDO" >
        update cpo_cop_biz_route
        <set >
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="rutEffFlg != null" >
                RUT_EFF_FLG = #{rutEffFlg,jdbcType=CHAR},
            </if>
            <if test="priLvl != null" >
                PRI_LVL = #{priLvl,jdbcType=INTEGER},
            </if>
            <if test="lowAmt != null" >
                LOW_AMT = #{lowAmt,jdbcType=DECIMAL},
            </if>
            <if test="highAmt != null" >
                HIGH_AMT = #{highAmt,jdbcType=DECIMAL},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
        </set>
        where RUT_INF_ID = #{rutInfId,jdbcType=VARCHAR}
    </update>

    <!--资金流出模块，查询资金机构路由信息列表-->
    <select id="getOrgnBusiList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpo_cop_biz_route
        where 1 = 1
        <if test="crdCorpOrg != null and crdCorpOrg != ''" >
            and CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusTyp != null and corpBusTyp != ''" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="rutCorpOrg != null and rutCorpOrg != ''" >
            and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="crdAcTyp != null and crdAcTyp != ''" >
            and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
        </if>
        <if test="rutEffFlg != null and rutEffFlg != ''" >
            and RUT_EFF_FLG = #{rutEffFlg,jdbcType=CHAR}
        </if>
        order by CRD_CORP_ORG,CORP_BUS_TYP,CORP_BUS_SUB_TYP
        limit #{pageBegin,jdbcType=INTEGER}, #{pageNum,jdbcType=INTEGER}
    </select>

    <!--资金流出模块，查询资金机构路由信息列表总笔数-->
    <select id="getOrgnRouteListTotNum" resultType="java.lang.Integer">
        select count(1)
        from cpo_cop_biz_route
        where 1 = 1
        <if test="crdCorpOrg != null and crdCorpOrg != ''" >
            and CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="corpBusTyp != null and corpBusTyp != ''" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        </if>
        <if test="corpBusSubTyp != null and corpBusSubTyp != ''" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        </if>
        <if test="rutCorpOrg != null and rutCorpOrg != ''" >
            and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="crdAcTyp != null and crdAcTyp != ''" >
            and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
        </if>
        <if test="rutEffFlg != null and rutEffFlg != ''" >
            and RUT_EFF_FLG = #{rutEffFlg,jdbcType=CHAR}
        </if>
    </select>

    <!-- 根据资金机构和业务类型，查询路由信息 -->
    <select id="getOrgnRouteDO" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpo_cop_biz_route
        WHERE CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
        and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
        and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
        and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
    </select>

</mapper>