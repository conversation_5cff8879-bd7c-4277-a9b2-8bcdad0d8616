<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="exchangeWaitReview.title">兑换复核查询</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="exchangeReview.content">兑换复核查询</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="dcorder.transferReview.dcOrderManagement">数币订单管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="exchangeReview.content">兑换复核查询</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="userId"
                                                data-i18n="exchangeReview.userId">用户ID</label>
                                            <input name="userId" id="userId" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="orderNo"
                                                data-i18n="exchangeReview.orderNo">订单号</label>
                                            <input name="orderNo" id="orderNo" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="direction"
                                                data-i18n="exchangeReview.direction">兑换方向</label>
                                            <select name="direction" id="direction" class="form-control">
                                                <option value="" data-i18n="exchangeReview.selectAll">全部</option>
                                                <option value="S2F" data-i18n="exchangeReview.directionS2F">数兑法</option>
                                                <option value="F2S" data-i18n="exchangeReview.directionF2S">法兑数</option>
                                                <option value="F2F" data-i18n="exchangeReview.directionF2F">法兑法</option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="status"
                                                data-i18n="exchangeReview.status">订单状态</label>
                                            <select name="status" id="status" class="form-control">
                                                <option value="" data-i18n="exchangeReview.selectAll">全部</option>
                                                <option value="SECOND_AUDIT"
                                                    data-i18n="exchangeReview.statusSecondAudit" selected>复核中</option>
                                                <option value="APPROVED" data-i18n="exchangeReview.statusApproved">审核通过
                                                </option>
                                                <option value="REJECTED" data-i18n="exchangeReview.statusRejected">拒绝
                                                </option>
                                                <option value="SUCCESS" data-i18n="exchangeReview.statusSuccess">成功
                                                </option>
                                                <option value="FAILED" data-i18n="exchangeReview.statusFailed">失败
                                                </option>
                                            </select>
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="exchangeReview.search">查询</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="exchangeAuditTable"
                                        class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="exchangeReview.userId">用户ID</th>
                                                <th data-i18n="exchangeReview.orderNo">订单号</th>
                                                <th data-i18n="exchangeReview.direction">兑换方向</th>
                                                <th data-i18n="exchangeReview.fromCoin">转出币种</th>
                                                <th data-i18n="exchangeReview.fromAmount">转出金额</th>
                                                <th data-i18n="exchangeReview.toCoin">转入币种</th>
                                                <th data-i18n="exchangeReview.toAmount">转入金额</th>
                                                <th data-i18n="exchangeReview.status">状态</th>
                                                <th data-i18n="exchangeReview.createTime">创建时间</th>
                                                <th data-i18n="exchangeReview.operations">操作</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="exchangeReview.exchangeDetail">兑换交易详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.userId">用户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-userId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.orderNo">订单号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-orderNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.direction">兑换方向</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-direction"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.fromCoin">转出币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fromCoin"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.fromAmount">转出金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fromAmount"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.toCoin">转入币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-toCoin"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.toAmount">转入金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-toAmount"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.feeCoin">手续费币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-feeCoin"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.feeAmount">手续费金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-feeAmount"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.exchangeRate">兑换汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-exchangeRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.status">状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-status"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.firstAuditInfo">初审信息</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">
                                    <span id="detail-firstAuditUser"></span>
                                    <span id="detail-firstAuditTime"></span>
                                    <span id="detail-firstAuditResult"></span>
                                </p>
                                <p class="form-control-static" id="detail-firstAuditOpinion"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="exchangeReview.secondAuditInfo">复核信息</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">
                                    <span id="detail-secondAuditUser"></span>
                                    <span id="detail-secondAuditTime"></span>
                                    <span id="detail-secondAuditResult"></span>
                                </p>
                                <p class="form-control-static" id="detail-secondAuditOpinion"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.rejectReason">拒绝原因</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-rejectReason"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.id">订单ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-id"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="exchangeReview.fromUsdRate">转出币种USD汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fromUsdRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.toUsdRate">转入币种USD汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-toUsdRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="exchangeReview.fromAmountUsd">转出金额USD等值</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fromAmountUsd"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="exchangeReview.toAmountUsd">转入金额USD等值</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-toAmountUsd"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.executeTime">执行时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-executeTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.createTime">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-createTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.updateTime">更新时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-updateTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="exchangeReview.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="auditModal" tabindex="-1" role="dialog" aria-labelledby="auditModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="auditModalLabel" data-i18n="exchangeReview.audit">审核兑换交易</h4>
                </div>
                <div class="modal-body">
                    <form id="auditForm" class="form-horizontal">
                        <input type="hidden" id="audit-id" name="id">
                        <input type="hidden" id="audit-times" name="auditTimes">

                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.auditResult">审核结果</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="audit-result" name="auditResult">
                                    <option value="0" data-i18n="exchangeReview.auditApproved">审核通过</option>
                                    <option value="1" data-i18n="exchangeReview.auditRejected">审核拒绝</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.auditOpinion">审核意见</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="audit-opinion" name="auditOpinion"
                                    rows="3"></textarea>
                            </div>
                        </div>

                        <div class="form-group reject-reason-group" style="display:none;">
                            <label class="col-sm-3 control-label" data-i18n="exchangeReview.rejectReason">拒绝原因</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="audit-reject-reason" name="rejectReason"
                                    rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="exchangeReview.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="submitAudit"
                        data-i18n="exchangeReview.submit">提交</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            // 初始化draw值
            $('input[name="draw"]').remove();
            $('body').append('<input type="hidden" name="draw" value="1">');

            // 处理DataTables处理中指示器问题
            $(document).ajaxStop(function () {
                $('.dataTables_processing').hide();
            });

            // 初始化DataTables
            table = $('#exchangeAuditTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/tam/exchange/waitReview/list',
                    type: 'post',
                    data: function (d) {
                        // 更新draw值供dataFilter使用
                        $('input[name="draw"]').val(d.draw);

                        var params = {
                            pageNum: d.start / d.length + 1,
                            pageSize: d.length,
                            userId: $('#userId').val(),
                            orderNo: $('#orderNo').val(),
                            direction: $('#direction').val(),
                            status: $('#status').val()
                        };

                        console.log("发送请求参数:", params);

                        return JSON.stringify({
                            body: params
                        });
                    },
                    dataFilter: function (data) {
                        var json = JSON.parse(data);
                        console.log("接收到服务器响应:", json);

                        // 获取当前页记录
                        var records = [];
                        if (json.body && json.body.queryExchangeAuditList) {
                            records = json.body.queryExchangeAuditList;
                        }

                        console.log("当前页记录数:", records.length);

                        // 异步获取总条数
                        var totalCount = 0;
                        $.ajax({
                            url: '/tam/exchange/waitReview/count',
                            type: 'post',
                            contentType: 'application/json',
                            async: false,
                            data: JSON.stringify({
                                body: {
                                    userId: $('#userId').val(),
                                    orderNo: $('#orderNo').val(),
                                    direction: $('#direction').val(),
                                    status: $('#status').val()
                                }
                            }),
                            success: function (response) {
                                if (response.body !== undefined) {
                                    totalCount = response.body;
                                }
                            },
                            error: function () {
                                totalCount = records.length;
                            }
                        });

                        console.log("总记录数:", totalCount);

                        var returnData = {
                            draw: parseInt($('input[name="draw"]').val()) || 1,
                            recordsTotal: totalCount,
                            recordsFiltered: totalCount,
                            data: records
                        };

                        console.log("返回给DataTables的数据:", returnData);
                        return JSON.stringify(returnData);
                    },
                    error: function (xhr, error, thrown) {
                        console.error('数据加载错误:', error);
                        toastr.error(i18n.t('exchangeReview.dataLoadFailed'));
                        // 确保处理中指示器消失
                        $('.dataTables_processing').hide();
                    },
                    complete: function () {
                        // 确保处理中指示器消失
                        $('.dataTables_processing').hide();
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl,
                    processing: i18n.t('exchangeReview.processing'),
                    paginate: {
                        first: i18n.t('exchangeReview.first'),
                        last: i18n.t('exchangeReview.last'),
                        next: i18n.t('exchangeReview.next'),
                        previous: i18n.t('exchangeReview.previous')
                    },
                    info: i18n.t('exchangeReview.info'),
                    infoEmpty: i18n.t('exchangeReview.infoEmpty'),
                    infoFiltered: i18n.t('exchangeReview.infoFiltered'),
                    lengthMenu: i18n.t('exchangeReview.lengthMenu')
                },
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                pagingType: "simple_numbers",
                drawCallback: function (settings) {
                    console.log("表格绘制完成, 页面信息:", settings._iDisplayStart, "至", settings._iDisplayStart + settings._iDisplayLength, "共", settings._iRecordsTotal, "条");
                    $('.dataTables_processing').hide();
                },
                columns: [
                    { data: 'userId' },
                    { data: 'orderNo' },
                    {
                        data: 'direction',
                        render: function (data) {
                            if (data === 'S2F') return i18n.t('exchangeReview.directionS2F');
                            if (data === 'F2S') return i18n.t('exchangeReview.directionF2S');
                            if (data === 'F2F') return i18n.t('exchangeReview.directionF2F');
                            return data;
                        }
                    },
                    { data: 'fromCoin' },
                    { data: 'fromAmount' },
                    { data: 'toCoin' },
                    { data: 'toAmount' },
                    {
                        data: 'status',
                        render: function (data) {
                            if (data === 'PENDING') return i18n.t('exchangeReview.statusPending');
                            if (data === 'FIRST_AUDIT') return i18n.t('exchangeReview.statusFirstAudit');
                            if (data === 'SECOND_AUDIT') return i18n.t('exchangeReview.statusSecondAudit');
                            if (data === 'APPROVED') return i18n.t('exchangeReview.statusApproved');
                            if (data === 'REJECTED') return i18n.t('exchangeReview.statusRejected');
                            if (data === 'SUCCESS') return i18n.t('exchangeReview.statusSuccess');
                            if (data === 'FAILED') return i18n.t('exchangeReview.statusFailed');
                            return data;
                        }
                    },
                    {
                        data: 'createTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '" data-i18n="exchangeReview.detail">详情</button> ';

                            // 只有复核中状态才显示复核按钮
                            if (row.status === 'SECOND_AUDIT') {
                                buttons += '<button type="button" class="btn btn-xs btn-warning second-audit" data-id="' + row.id + '" data-i18n="exchangeReview.review">复核</button>';
                            }

                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: i18n.t('exchangeReview.exportTitle') }
                ]
            });

            // 给表格添加绘制完成事件处理
            $('#exchangeAuditTable').on('draw.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面长度变化事件监听
            $('#exchangeAuditTable').on('length.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面变化事件监听
            $('#exchangeAuditTable').on('page.dt', function () {
                setTimeout(function () {
                    $('.dataTables_processing').hide();
                }, 500);
            });

            // 查看详情
            $('#exchangeAuditTable').on('click', '.view-detail', function () {
                var id = $(this).data('id');
                $.ajax({
                    url: '/tam/exchange/audit/detail',
                    type: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        body: {
                            id: id
                        }
                    }),
                    success: function (response) {
                        var data = response.body;

                        // 填充详情数据
                        $('#detail-userId').text(data.userId || '');
                        $('#detail-orderNo').text(data.orderNo || '');

                        // 兑换方向转义显示
                        var directionText = data.direction || '';
                        if (data.direction === 'S2F') directionText = i18n.t('exchangeReview.directionS2F');
                        else if (data.direction === 'F2S') directionText = i18n.t('exchangeReview.directionF2S');
                        else if (data.direction === 'F2F') directionText = i18n.t('exchangeReview.directionF2F');
                        $('#detail-direction').text(directionText);

                        $('#detail-fromCoin').text(data.fromCoin || '');
                        $('#detail-fromAmount').text(data.fromAmount || '');
                        $('#detail-toCoin').text(data.toCoin || '');
                        $('#detail-toAmount').text(data.toAmount || '');
                        $('#detail-feeCoin').text(data.feeCoin || '');
                        $('#detail-feeAmount').text(data.feeAmount || '');
                        $('#detail-exchangeRate').text(data.exchangeRate || '');

                        // 状态转义显示
                        var statusText = data.status || '';
                        if (data.status === 'PENDING') statusText = i18n.t('exchangeReview.statusPending');
                        else if (data.status === 'FIRST_AUDIT') statusText = i18n.t('exchangeReview.statusFirstAudit');
                        else if (data.status === 'SECOND_AUDIT') statusText = i18n.t('exchangeReview.statusSecondAudit');
                        else if (data.status === 'APPROVED') statusText = i18n.t('exchangeReview.statusApproved');
                        else if (data.status === 'REJECTED') statusText = i18n.t('exchangeReview.statusRejected');
                        else if (data.status === 'SUCCESS') statusText = i18n.t('exchangeReview.statusSuccess');
                        else if (data.status === 'FAILED') statusText = i18n.t('exchangeReview.statusFailed');
                        $('#detail-status').text(statusText);
                        $('#detail-id').text(data.id || '');
                        $('#detail-fromUsdRate').text(data.fromUsdRate || '');
                        $('#detail-toUsdRate').text(data.toUsdRate || '');
                        $('#detail-fromAmountUsd').text(data.fromAmountUsd || '');
                        $('#detail-toAmountUsd').text(data.toAmountUsd || '');

                        // 初审信息
                        var firstAuditInfo = '';
                        if (data.firstAuditUser) {
                            firstAuditInfo += i18n.t('exchangeReview.auditor') + ': ' + data.firstAuditUser + ' ';
                        }
                        if (data.firstAuditTime) {
                            firstAuditInfo += i18n.t('exchangeReview.auditTime') + ': ' + new Date(data.firstAuditTime).toLocaleString() + ' ';
                        }
                        if (data.firstAuditResult) {
                            firstAuditInfo += i18n.t('exchangeReview.auditResult') + ': ' + data.firstAuditResult;
                        }
                        $('#detail-firstAuditUser').text(firstAuditInfo);
                        $('#detail-firstAuditOpinion').text(i18n.t('exchangeReview.auditOpinion') + ': ' + (data.firstAuditOpinion || ''));

                        // 复核信息
                        var secondAuditInfo = '';
                        if (data.secondAuditUser) {
                            secondAuditInfo += i18n.t('exchangeReview.auditor') + ': ' + data.secondAuditUser + ' ';
                        }
                        if (data.secondAuditTime) {
                            secondAuditInfo += i18n.t('exchangeReview.auditTime') + ': ' + new Date(data.secondAuditTime).toLocaleString() + ' ';
                        }
                        if (data.secondAuditResult) {
                            secondAuditInfo += i18n.t('exchangeReview.auditResult') + ': ' + data.secondAuditResult;
                        }
                        $('#detail-secondAuditUser').text(secondAuditInfo);
                        $('#detail-secondAuditOpinion').text(i18n.t('exchangeReview.auditOpinion') + ': ' + (data.secondAuditOpinion || ''));

                        // 拒绝原因
                        $('#detail-rejectReason').text(data.rejectReason || '');

                        // 时间相关字段
                        $('#detail-executeTime').text(data.executeTime ? new Date(data.executeTime).toLocaleString() : '');
                        $('#detail-createTime').text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                        $('#detail-updateTime').text(data.updateTime ? new Date(data.updateTime).toLocaleString() : '');

                        // 显示模态框
                        $('#detailModal').modal('show');
                    },
                    error: function (xhr, status, error) {
                        toastr.error(i18n.t('exchangeReview.getDetailFailed') + ': ' + error);
                    }
                });
            });

            // 复核
            $('#exchangeAuditTable').on('click', '.second-audit', function () {
                var id = $(this).data('id');
                $('#audit-id').val(id);
                $('#audit-times').val(1); // 复核
                $('#auditModalLabel').text(i18n.t('exchangeReview.reviewExchangeTransaction'));
                $('#auditModal').modal('show');
            });

            // 审核结果改变时显示/隐藏拒绝原因字段
            $('#audit-result').change(function () {
                if ($(this).val() === '1') {
                    $('.reject-reason-group').show();
                } else {
                    $('.reject-reason-group').hide();
                }
            });

            // 提交审核
            $('#submitAudit').click(function () {
                var formData = {
                    id: $('#audit-id').val(),
                    auditTimes: parseInt($('#audit-times').val()),
                    auditResult: parseInt($('#audit-result').val()),
                    auditOpinion: $('#audit-opinion').val(),
                    rejectReason: $('#audit-reject-reason').val()
                };

                $.ajax({
                    url: '/tam/exchange/audit',
                    type: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        body: formData
                    }),
                    success: function (response) {
                        $('#auditModal').modal('hide');
                        toastr.success(i18n.t('exchangeReview.auditSuccess'));
                        // 先隐藏处理中指示器（如果存在）
                        $('.dataTables_processing').hide();
                        // 刷新表格
                        table.ajax.reload(function () {
                            // 回调中再次确保处理中指示器消失
                            $('.dataTables_processing').hide();
                        }, false);
                    },
                    error: function (xhr, status, error) {
                        toastr.error(i18n.t('exchangeReview.auditFailed') + ': ' + error);
                    }
                });
            });
        });

        // 搜索方法
        function search() {
            console.log("执行搜索操作");
            // 先隐藏处理中指示器（如果存在）
            $('.dataTables_processing').hide();
            // 刷新表格
            table.ajax.reload(function () {
                console.log("表格刷新完成");
                // 回调中再次确保处理中指示器消失
                $('.dataTables_processing').hide();
            }, false);
        }
    </script>
</body>

</html>