<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="user.title"></title>

    <div th:replace="head"></div>
</head>

<body>

    <div id="wrapper">

        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="user.head"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.cmmmgr"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="user.head"></strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2"></div>
            </div>

            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="box-header">
                                    <form id="queryForm" class="form-horizontal">
                                        <div class="form-group col-sm-12 query-grid3">
                                            <div class="query-field">
                                                <label class="control-label" data-i18n="user.userid"></label>
                                                <input class="form-control" name="id" />
                                            </div>
                                            <div class="query-field">
                                                <label class="control-label" data-i18n="user.username"></label>
                                                <input class="form-control" name="name" />
                                            </div>
                                            <div class="query-field">
                                                <label class="control-label" data-i18n="user.status"></label>
                                                <select class="form-control" name="status">
                                                    <option value="" data-i18n="role.constant.all"></option>
                                                    <option value="0" data-i18n="role.normal"></option>
                                                    <option value="1" data-i18n="role.lapse"></option>
                                                </select>
                                            </div>
                                            <div class="query-field">
                                                <label class="control-label" data-i18n="user.officeNm"></label>
                                                <select name="officeId" id="officeId" required
                                                    class="form-control"></select>
                                            </div>
                                            <div class="query-field">
                                                <label class="control-label" data-i18n="user.braNm"></label>
                                                <select name="braId" id="braId" required class="form-control"></select>
                                            </div>
                                            <div class="query-actions">
                                                <a class="btn btn-primary" th:href="@{/system/user/form}"
                                                    sec:authorize="hasPermission('','/cmmmgr/user/user:add') or hasRole('ROLE_ADMIN')">
                                                     <span data-i18n="user.add"></span>
                                                </a>
                                                <input type="hidden" id="ofId" th:value="${user}? ${user.officeId}">
                                                <input type="hidden" id="brId" th:value="${user}? ${user.braId}">
                                                <button type="button" class="btn btn-primary" data-i18n="role.search"
                                                    onclick="searchButton()"></button>
                                            </div>
                                        </div>
                                    </form>
                                    <div class="clearfix"></div>
                                    <hr />



                                    <div class="table-responsive">
                                        <table id="dataTables" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th data-i18n="user.username"></th>
                                                    <th data-i18n="user.status"></th>
                                                    <th data-i18n="user.email"></th>
                                                    <th data-i18n="user.mblNo"></th>
                                                    <th data-i18n="user.officeNm"></th>
                                                    <th data-i18n="user.braNm"></th>
                                                    <th sec:authorize="hasPermission('','/cmmmgr/user/user:status') or hasRole('ROLE_ADMIN')"
                                                        data-i18n="user.switch"></th>
                                                    <th sec:authorize="hasPermission('','/cmmmgr/user/user:modify') or hasRole('ROLE_ADMIN')"
                                                        data-i18n="user.modify"></th>
                                                    <th sec:authorize="hasPermission('','/cmmmgr/user/user:role') or hasRole('ROLE_ADMIN')"
                                                        data-i18n="user.assign"></th>
                                                    <th sec:authorize="hasPermission('','/cmmmgr/user/user:delete') or hasRole('ROLE_ADMIN')"
                                                        data-i18n="user.delete"></th>
                                                    <th style="display:none"></th>
                                                    <th style="display:none"></th>
                                                    <th style="display:none"></th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>

            </div>
        </div>
        <div th:replace="script"></div>
        <!-- Page-Level Scripts -->
        <script type="text/javascript" th:inline="javascript">
            var table;
            $(document).ready(function () {
                initOfficeNm();
                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                        break;
                    case 'en':
                        languageUrl = '/datatables/plugins/i18n/English.lang';
                        break;
                    case 'km':
                        languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                        break;
                }
                i18nLoad.then(function () {
                    table = $('#dataTables').DataTable({
                        ajax: {
                            contentType: 'application/json',
                            url: '/system/user/findAll',
                            type: 'POST',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        serverSide: true,
                        responsive: true,
                        processing: true,
                        language: {
                            url: languageUrl
                            //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                        },
                        columns: [{
                            data: 'name'
                        }, {
                            data: 'active',
                            render: function (data, type, row) {
                                switch (data) {
                                    case true:
                                        return $.i18n.t("user.active_on");
                                    case false:
                                        return $.i18n.t("user.active_off");
                                    default:
                                        return "";
                                }
                            }
                        }, {
                            data: 'email'
                        }, {
                            data: 'mblNo'
                        }, {
                            data: 'officeNm'
                        }, {
                            data: 'braNm'
                        },
                        /*[# sec:authorize="hasPermission('','/cmmmgr/user/user:status') or hasRole('ROLE_ADMIN')"]*/
                        {
                            data: 'active',
                            orderable: false,
                            searchable: false,
                            render: function (data, type, row) {
                                return '<td><span name="lock" id="' + row.id +
                                    '" onclick="ajaxClick(\'lock\', \'PUT\', \'' + row.id + '\')" title="' + $.i18n.t("user.status") + '" data="/system/user/status/' +
                                    row.id + '?active=' + !data + '&id=' + row.id + '" style="cursor: pointer" class="fa fa-lock"></span></td>';
                            }
                        },
                        /*[/]*/
                        /*[# sec:authorize="hasPermission('','/cmmmgr/user/user:modify') or hasRole('ROLE_ADMIN')"]*/
                        {
                            data: 'id',
                            orderable: false,
                            searchable: false,
                            render: function (data, type, row) {
                                return '<td><a href="/system/user/form?id=' + data +
                                    '"><span class="fa fa-edit" title="' + $.i18n.t("user.switch") + '"></span></a></td>';
                            }
                        },
                        /*[/]*/
                        /*[# sec:authorize="hasPermission('','/cmmmgr/user/user:role') or hasRole('ROLE_ADMIN')"]*/
                        {
                            data: 'id',
                            orderable: false,
                            searchable: false,
                            render: function (data, type, row) {
                                return '<th><a href="/system/user/select-role/' + data + '?id=' + data +
                                    '"><span style="cursor: pointer" class="fa fa-users" title="' + $.i18n.t("user.assign") + '"></span></a></th>';
                            }
                        },
                        /*[/]*/
                        /*[# sec:authorize="hasPermission('','/cmmmgr/user/user:delete') or hasRole('ROLE_ADMIN')"]*/
                        {
                            data: 'id',
                            orderable: false,
                            searchable: false,
                            render: function (data, type, row) {
                                return '<span name="trash" title="' + $.i18n.t("user.delete") + '" id="' + row.id + '" onclick="ajaxClick(\'trash\',\'DELETE\',\'' + row.id + '\')" ' +
                                    'data="/system/user/delete/' + data + '" ' + 'style="cursor: pointer" class="fa fa-trash"></span></td>';
                            }
                        }, {
                            data: 'officeId',
                            className: "hidden"
                        }, {
                            data: 'braId',
                            className: "hidden"
                        }, {
                            data: 'id',
                            className: "hidden"
                        }
                            /*[/]*/
                        ]
                    });
                });
            });

            $.ajaxSetup({ headers: { 'X-CSRF-TOKEN': $("#csrf_token").attr("content") } });

            function ajaxClick(name, type, id) {
                var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
                if (name == "lock") {
                    $.ajax({
                        type: type,
                        url: url,
                        success: function (data) {
                            if (data) {
                                alert(data);
                            } else {
                                window.location.reload();
                            }
                        }
                    })
                } else if (name == "trash") {
                    swal({
                        title: $.i18n.t("user.swal-title"),
                        text: "",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: $.i18n.t("user.swal-confirm"),
                        cancelButtonText: $.i18n.t("user.swal-cancel"),
                        closeOnConfirm: false
                    }, function (isConfirm) {
                        if (!isConfirm) return;
                        $.ajax({
                            url: url,
                            type: type,
                            success: function () {
                                swal($.i18n.t("user.swal-sucess"), "", "success");
                                $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                                    location.reload();
                                });
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                            }
                        });
                    });
                }
            }


            // 初始化公司
            function initOfficeNm() {
                $.getJSON('/cmm/officectrl/findAll', {
                    ajax: 'true'
                }, function (data) {
                    var html = '<option value="">' + $.i18n.t("cmm.ddl.choose") + '</option>';
                    var len = data.length;
                    for (var i = 0; i < len; i++) {
                        html += '<option value="' + data[i].officeId + '">' + data[i].officeNm + '</option>';
                    }
                    $('#officeId').html(html);
                    setofficeinfo();
                    initbranch();
                    setbranchinfo();
                });
            };
            function setofficeinfo() {
                var checkValue = document.getElementById("ofId").value;
                var select = document.getElementById("officeId");
                for (var i = 0; i < select.options.length; i++) {
                    if (select.options[i].value == checkValue) {
                        select.options[i].selected = true;
                        break;
                    }
                }
            };
            function setbranchinfo() {
                var checkValue = document.getElementById("brId").value;
                var select = document.getElementById("braId");
                for (var i = 0; i < select.options.length; i++) {
                    if (select.options[i].value == checkValue) {
                        select.options[i].selected = true;
                        break;
                    }
                }
            };
            //根据选择的公司确定部门
            $('#officeId').change(
                function () {
                    var officeId = $(this).val();
                    if (officeId.length == 0) {
                        $('#braId').html('<option value="">' + $.i18n.t("cmm.ddl.choose") + '</option>');
                    } else {
                        $.getJSON('/cmm/branchctrl/findAll', {
                            officeId: officeId,
                            ajax: 'true'
                        }, function (data) {
                            var html = '';
                            var len = data.length;
                            for (var i = 0; i < len; i++) {
                                html += '<option value="' + data[i].braId + '">' + data[i].braNm + '</option>';
                            }
                            $('#braId').html(html);
                        });
                    }
                });
            //根据选择的公司加载出部门
            function initbranch() {
                var officeId = document.getElementById("officeId").value;
                if (officeId.length == 0) {
                    $('#braId').html('<option value="">' + $.i18n.t("cmm.ddl.choose") + '</option>');
                } else {
                    $.getJSON('/cmm/branchctrl/findAll', {
                        officeId: officeId,
                        ajax: 'true'
                    }, function (data) {
                        var html = '';
                        var len = data.length;
                        for (var i = 0; i < len; i++) {
                            html += '<option value="' + data[i].braId + '">' + data[i].braNm + '</option>';
                        }
                        $('#braId').html(html);
                    });
                }
            };

            //*!* 表头搜索框 *!/*/
            function searchButton() {
                var id = $("input[name='id']").val();
                var name = $("input[name='name']").val();
                var braId = $("select[name='braId']").val();
                var officeId = $("select[name='officeId']").val();
                var status = $("select[name='status']").val();

                var selectId = document.getElementById("braId");
                var selectValue = selectId.options[0].value;
                if (selectValue == "") {
                    /*如果所属公司为“请选择”*/
                    officeNm = "";
                    braNm = "";
                };
                table.column(0).search(name)
                    .column(1).search(status)
                    .column(10).search(officeId)
                    .column(11).search(braId)
                    .column(12).search(id)
                    .draw();
            }
        </script>
</body>

</html>