<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="nav.cmmsub.userOprSub.title"></title>

    <div th:replace="head"></div>
</head>

<body>

    <div id="wrapper">

        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.cmmsub.userOprSub.phsmgr"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.cmmmgr"></a>
                        </li>

                        <li class="active">
                            <strong data-i18n="nav.cmmsub.userOprSub.phsmgr"></strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field" sec:authorize="hasRole('ROLE_ADMIN')">
                                            <label class="control-label" data-i18n="userOpr.userId"></label>
                                            <input class="form-control" name="userId" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="userOpr.oprNm"></label>
                                            <input class="form-control" name="oprNm" />
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary" data-i18n="userOpr.search"
                                                onclick="searchButton()"></button>
                                        </div>
                                    </div>
                                </form>
                                <div class="clearfix"></div>
                                <hr />

                                <div class="table-responsive">
                                    <table id="dataTables" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="userOpr.userId"></th>
                                                <th data-i18n="userOpr.oprNm"></th>
                                                <th data-i18n="userOpr.lv"></th>
                                                <th data-i18n="userOpr.ip"></th>
                                                <th data-i18n="userOpr.tm"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>

        </div>
    </div>
    <div th:replace="script"></div>
    <!-- Page-Level Scripts -->
    <script type="text/javascript" th:inline="javascript">
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }
            i18nLoad.then(function () {
                table = $('#dataTables').DataTable({
                    ajax: {
                        contentType: 'application/json',
                        url: '/system/userOpr/findAll',
                        type: 'POST',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                        //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                    },
                    columns: [{
                        data: 'userId'
                    }, {
                        data: 'oprNm'
                    }, {
                        data: 'lv'
                    }, {
                        data: 'ip'
                    }, {
                        data: 'createTime'
                    }
                    ]
                });
            });
        });

        $.ajaxSetup({ headers: { 'X-CSRF-TOKEN': $("#csrf_token").attr("content") } });



        //*!* 表头搜索框 *!/*/
        function searchButton() {
            var userId = $("input[name='userId']").val();
            var oprNm = $("input[name='oprNm']").val();

            table.column(0).search(userId)
                .column(1).search(oprNm)
                .draw();
        }
    </script>
</body>

</html>