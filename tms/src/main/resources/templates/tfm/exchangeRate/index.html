<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="exchangeRate.title">汇率查询</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="exchangeRate.title">汇率查询</h2>
                    <ol class="breadcrumb">

                        <li>
                            <a data-i18n="nav.parammgr">参数管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="exchangeRate.title">汇率查询</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">

                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="searchRateType"
                                                data-i18n="exchangeRate.rateType">汇率类型</label>
                                            <input name="rateType" id="searchRateType" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchSourceCode"
                                                data-i18n="exchangeRate.sourceCode">源币种代码</label>
                                            <input name="sourceCode" id="searchSourceCode" class="form-control"
                                                value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchTargetCode"
                                                data-i18n="exchangeRate.targetCode">目标币种代码</label>
                                            <input name="targetCode" id="searchTargetCode" class="form-control"
                                                value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchNetwork"
                                                data-i18n="exchangeRate.network">网络</label>
                                            <input name="network" id="searchNetwork" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchRateSource"
                                                data-i18n="exchangeRate.rateSource">汇率来源</label>
                                            <input name="rateSource" id="searchRateSource" class="form-control"
                                                value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchUpdateBy"
                                                data-i18n="exchangeRate.updateBy">调整人</label>
                                            <input name="updateBy" id="searchUpdateBy" class="form-control" value="" />
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary" id="btnAdd">
                                                 <span data-i18n="exchangeRate.add">新增</span>
                                            </button>
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="exchangeRate.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()" data-i18n="exchangeRate.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="exchangeRateTable"
                                        class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="exchangeRate.id">ID</th>
                                                <th data-i18n="exchangeRate.rateType">汇率类型</th>
                                                <th data-i18n="exchangeRate.sourceCode">源币种代码</th>
                                                <th data-i18n="exchangeRate.targetCode">目标币种代码</th>
                                                <th data-i18n="exchangeRate.network">网络</th>
                                                <th data-i18n="exchangeRate.baseRate">基准汇率</th>
                                                <th data-i18n="exchangeRate.manualPoint">人工调整点数</th>
                                                <th data-i18n="exchangeRate.finalRate">最终生效汇率</th>
                                                <th data-i18n="exchangeRate.rateSource">汇率来源</th>
                                                <th data-i18n="exchangeRate.effectiveTime">生效时间</th>
                                                <th data-i18n="exchangeRate.expireTime">过期时间</th>
                                                <th data-i18n="exchangeRate.updateBy">调整人</th>
                                                <th data-i18n="exchangeRate.operations">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="exchangeRateModal" tabindex="-1" role="dialog" aria-labelledby="exchangeRateModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="exchangeRateModalLabel" data-i18n="exchangeRate.addRateInfo">新增汇率信息</h4>
                </div>
                <div class="modal-body">
                    <form id="exchangeRateForm" class="form-horizontal">
                        <input type="hidden" id="id" name="id" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="rateType"
                                data-i18n="exchangeRate.rateType">汇率类型</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="rateType" name="rateType" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="sourceCode"
                                data-i18n="exchangeRate.sourceCode">源币种代码</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="sourceCode" name="sourceCode" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="targetCode"
                                data-i18n="exchangeRate.targetCode">目标币种代码</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="targetCode" name="targetCode" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="network"
                                data-i18n="exchangeRate.network">网络</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="network" name="network" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="baseRate"
                                data-i18n="exchangeRate.baseRate">基准汇率</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="baseRate" name="baseRate" type="number" step="0.000001"
                                    required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="manualPoint"
                                data-i18n="exchangeRate.manualPoint">人工调整点数</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="manualPoint" name="manualPoint" type="number"
                                    step="0.000001" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="finalRate"
                                data-i18n="exchangeRate.finalRate">最终生效汇率</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="finalRate" name="finalRate" type="number"
                                    step="0.000001" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="rateSource"
                                data-i18n="exchangeRate.rateSource">汇率来源</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="rateSource" name="rateSource" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="effectiveTime"
                                data-i18n="exchangeRate.effectiveTime">生效时间</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="effectiveTime" name="effectiveTime"
                                    type="datetime-local" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="expireTime"
                                data-i18n="exchangeRate.expireTime">过期时间</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="expireTime" name="expireTime" type="datetime-local" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="updateBy"
                                data-i18n="exchangeRate.updateBy">调整人</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="updateBy" name="updateBy" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="approvedBy"
                                data-i18n="exchangeRate.approvedBy">审批人</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="approvedBy" name="approvedBy" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="exchangeRate.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="exchangeRate.save">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="exchangeRate.rateInfoDetail">汇率信息详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.id">ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.rateType">汇率类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailRateType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.sourceCode">源币种代码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailSourceCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.targetCode">目标币种代码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailTargetCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.network">网络</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailNetwork"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.baseRate">基准汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailBaseRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.manualPoint">人工调整点数</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailManualPoint"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.finalRate">最终生效汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailFinalRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.rateSource">汇率来源</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailRateSource"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.effectiveTime">生效时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailEffectiveTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.expireTime">过期时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailExpireTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.updateBy">调整人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdateBy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="exchangeRate.approvedBy">审批人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailApprovedBy"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="exchangeRate.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            i18nLoad.then(function () {
                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/js/Chinese.json';
                        break;
                    case 'en':
                        languageUrl = '/js/English.json';
                        break;
                    default:
                        languageUrl = '/js/Chinese.json';
                }

                // 初始化DataTables
                table = $('#exchangeRateTable').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/tfm/exchangeRate/findAll',
                        type: 'post',
                        data: function (d) {
                            // 添加额外的查询参数
                            d.extra_search = {
                                "rateType": $("#searchRateType").val() || "",
                                "sourceCode": $("#searchSourceCode").val() || "",
                                "targetCode": $("#searchTargetCode").val() || "",
                                "network": $("#searchNetwork").val() || "",
                                "rateSource": $("#searchRateSource").val() || "",
                                "updateBy": $("#searchUpdateBy").val() || ""
                            };
                            return JSON.stringify(d);
                        },
                        dataSrc: function (json) {
                            // 确保返回的是数组
                            return json.data || [];
                        },
                        error: function (xhr, error, thrown) {
                            console.error('DataTables AJAX error:', error, thrown);
                            toastr.error((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.loadDataFailed') : '加载数据失败，请刷新页面重试');
                        }
                    },
                    serverSide: true,
                    searching: false, // 禁用内置搜索
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    columns: [
                        { data: 'id' },
                        { data: 'rateType' },
                        { data: 'sourceCode' },
                        { data: 'targetCode' },
                        { data: 'network' },
                        {
                            data: 'baseRate',
                            render: function (data) {
                                return data ? parseFloat(data).toFixed(6) : '0.000000';
                            }
                        },
                        {
                            data: 'manualPoint',
                            render: function (data) {
                                return data ? parseFloat(data).toFixed(6) : '0.000000';
                            }
                        },
                        {
                            data: 'finalRate',
                            render: function (data) {
                                return data ? parseFloat(data).toFixed(6) : '0.000000';
                            }
                        },
                        { data: 'rateSource' },
                        {
                            data: 'effectiveTime',
                            render: function (data) {
                                if (!data) return '';
                                return new Date(data).toLocaleString();
                            }
                        },
                        {
                            data: 'expireTime',
                            render: function (data) {
                                if (!data) return '';
                                return new Date(data).toLocaleString();
                            }
                        },
                        { data: 'updateBy' },
                        {
                            data: null,
                            orderable: false,
                            render: function (data, type, row) {
                                var detailText = (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.detail') : '详情';
                                var editText = (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.edit') : '编辑';
                                var deleteText = (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.delete') : '删除';
                                var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">' + detailText + '</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.id + '">' + editText + '</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.id + '">' + deleteText + '</button>';
                                return buttons;
                            }
                        }
                    ],
                    buttons: [
                        { extend: 'copyHtml5' },
                        { extend: 'csvHtml5' },
                        { extend: 'excelHtml5', title: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.rateInfoList') : '汇率信息列表' }
                    ]
                });

                // 新增按钮
                $("#btnAdd").click(function () {
                    $("#exchangeRateModalLabel").text((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.addRateInfo') : '新增汇率信息');
                    $("#exchangeRateForm")[0].reset();
                    $("#id").val("");
                    $("#exchangeRateModal").modal("show");
                });

                // 保存按钮
                $("#btnSave").click(function () {
                    if (!$("#exchangeRateForm").valid()) {
                        toastr.error('请填写必填项');
                        return;
                    }

                    var id = $("#id").val();
                    var formData = $("#exchangeRateForm").serialize();
                    var url = id ? "/tfm/exchangeRate/modify/" + id : "/tfm/exchangeRate/add";

                    $.ajax({
                        url: url,
                        type: "POST",
                        data: formData,
                        success: function (res) {
                            if (res.result === "TFM00000") {
                                toastr.success((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.operationSuccess') : '操作成功');
                                $("#exchangeRateModal").modal("hide");
                                table.ajax.reload();
                            } else {
                                toastr.error(((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.operationFailed') : '操作失败') + '：' + res.result);
                            }
                        },
                        error: function (xhr, status, error) {
                            toastr.error(((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.operationFailed') : '操作失败') + '：' + error);
                        }
                    });
                });

                // 查看详情
                $(document).on("click", ".view-detail", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "/tfm/exchangeRate/getExchangeRate",
                        type: "POST",
                        data: { id: id },
                        success: function (data) {
                            $("#detailId").text(data.id || '');
                            $("#detailRateType").text(data.rateType || '');
                            $("#detailSourceCode").text(data.sourceCode || '');
                            $("#detailTargetCode").text(data.targetCode || '');
                            $("#detailNetwork").text(data.network || '');
                            $("#detailBaseRate").text(data.baseRate ? parseFloat(data.baseRate).toFixed(6) : '0.000000');
                            $("#detailManualPoint").text(data.manualPoint ? parseFloat(data.manualPoint).toFixed(6) : '0.000000');
                            $("#detailFinalRate").text(data.finalRate ? parseFloat(data.finalRate).toFixed(6) : '0.000000');
                            $("#detailRateSource").text(data.rateSource || '');
                            $("#detailEffectiveTime").text(data.effectiveTime ? new Date(data.effectiveTime).toLocaleString() : '');
                            $("#detailExpireTime").text(data.expireTime ? new Date(data.expireTime).toLocaleString() : '');
                            $("#detailUpdateBy").text(data.updateBy || '');
                            $("#detailApprovedBy").text(data.approvedBy || '');
                            $("#detailModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error('获取详情失败：' + error);
                        }
                    });
                });

                // 编辑按钮
                $(document).on("click", ".btn-edit", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "/tfm/exchangeRate/getExchangeRate",
                        type: "POST",
                        data: { id: id },
                        success: function (data) {
                            $("#exchangeRateModalLabel").text((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.editRateInfo') : '编辑汇率信息');
                            $("#id").val(data.id);
                            $("#rateType").val(data.rateType);
                            $("#sourceCode").val(data.sourceCode);
                            $("#targetCode").val(data.targetCode);
                            $("#network").val(data.network);
                            $("#baseRate").val(data.baseRate);
                            $("#manualPoint").val(data.manualPoint);
                            $("#finalRate").val(data.finalRate);
                            $("#rateSource").val(data.rateSource);

                            // 处理日期时间格式
                            if (data.effectiveTime) {
                                var effectiveDate = new Date(data.effectiveTime);
                                $("#effectiveTime").val(effectiveDate.toISOString().slice(0, 16));
                            }
                            if (data.expireTime) {
                                var expireDate = new Date(data.expireTime);
                                $("#expireTime").val(expireDate.toISOString().slice(0, 16));
                            }

                            $("#updateBy").val(data.updateBy);
                            $("#approvedBy").val(data.approvedBy);
                            $("#exchangeRateModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error('获取详情失败：' + error);
                        }
                    });
                });

                // 删除按钮
                $(document).on("click", ".btn-delete", function () {
                    var id = $(this).data("id");
                    swal({
                        title: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.confirmDelete') : "确定要删除该汇率信息吗？",
                        text: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.deleteWarning') : "删除后将无法恢复！",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.confirmDeleteBtn') : "确定删除",
                        cancelButtonText: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.cancel') : "取消",
                        closeOnConfirm: false
                    }, function () {
                        $.ajax({
                            url: "/tfm/exchangeRate/delete/" + id,
                            type: "DELETE",
                            success: function (res) {
                                if (res === "1" || res.result === "TFM00000") {
                                    swal((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.deleteSuccess') : "删除成功！", (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.deleteSuccessMsg') : "汇率信息已被删除。", "success");
                                    table.ajax.reload();
                                } else {
                                    swal((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.deleteFailed') : "删除失败", (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.retryLater') : "请稍后重试", "error");
                                }
                            },
                            error: function (xhr, status, error) {
                                swal((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.deleteFailed') : "删除失败", ((typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.error') : "错误") + ": " + error, "error");
                            }
                        });
                    });
                });

                // 表单验证
                $("#exchangeRateForm").validate({
                    rules: {
                        rateType: {
                            required: true,
                            maxlength: 50
                        },
                        sourceCode: {
                            required: true,
                            maxlength: 10
                        },
                        targetCode: {
                            required: true,
                            maxlength: 10
                        },
                        baseRate: {
                            required: true,
                            number: true,
                            min: 0
                        },
                        finalRate: {
                            required: true,
                            number: true,
                            min: 0
                        },
                        manualPoint: {
                            number: true
                        }
                    },
                    messages: {
                        rateType: {
                            required: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.rateTypeRequired') : "请输入汇率类型",
                            maxlength: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.rateTypeMaxLength') : "汇率类型不能超过50个字符"
                        },
                        sourceCode: {
                            required: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.sourceCodeRequired') : "请输入源币种代码",
                            maxlength: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.sourceCodeMaxLength') : "源币种代码不能超过10个字符"
                        },
                        targetCode: {
                            required: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.targetCodeRequired') : "请输入目标币种代码",
                            maxlength: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.targetCodeMaxLength') : "目标币种代码不能超过10个字符"
                        },
                        baseRate: {
                            required: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.baseRateRequired') : "请输入基准汇率",
                            number: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.validNumber') : "请输入有效的数字",
                            min: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.baseRateMin') : "基准汇率不能为负数"
                        },
                        finalRate: {
                            required: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.finalRateRequired') : "请输入最终生效汇率",
                            number: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.validNumber') : "请输入有效的数字",
                            min: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.finalRateMin') : "最终生效汇率不能为负数"
                        },
                        manualPoint: {
                            number: (typeof $.i18n !== 'undefined') ? $.i18n.t('exchangeRate.validNumber') : "请输入有效的数字"
                        }
                    }
                });
            });
        });

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>