<!DOCTYPE html>
<html xmlns:th = "http://www.thymeleaf.org">

    <head>
        <title data-i18n = "index.title">Plunex | Dashboard</title>
        <div th:replace = "head"></div>
        <link rel = "stylesheet" href = "/css/plunex/element-plus.css">
        <link rel = "stylesheet" href = "/css/plunex/tailwind.css">
        <style>

            [v-cloak] {
                display: none;
            }

            th, td {
                color: black;
                padding: 10px;
                font-size: 12px;
                text-align: center;
            }

            :root {
                --el-color-primary: #8247E5;
                --el-color-primary-light-1: #8F59E8;
                --el-color-primary-light-2: #9B6CEA;
                --el-color-primary-light-3: #A87EED;
                --el-color-primary-light-4: #B491EF;
                --el-color-primary-light-5: #C1A3F2;
                --el-color-primary-light-6: #CDB5F5;
                --el-color-primary-light-7: #DAC8F7;
                --el-color-primary-light-8: #E6DAFA;
                --el-color-primary-light-9: #F3EDFC;
            }

        </style>

    </head>

    <body>
        <div v-cloak id = "wrapper">
            <div th:replace = "nav"></div>
            <div id = "page-wrapper">
                <div th:replace = "top"></div>
                <div class = "flex py-15 gap-20">
                    <div class = "w-2/3 flex flex-col gap-20">
                        <div>
                            <div class = "flex justify-between items-center mb-10">
                                <p data-i18n = "业务核心指标" class = "text-[20px] font-bold text-black mb-0!">业务核心指标</p>
                                <el-radio-group v-model = "type">
                                    <el-radio-button :label = "t('当日')" value = "day"></el-radio-button>
                                    <el-radio-button :label = "t('最近一周')" value = "week"></el-radio-button>
                                    <el-radio-button :label = "t('最近一月')" value = "month"></el-radio-button>
                                </el-radio-group>
                            </div>
                            <div class = "flex gap-10">
                                <div class = "w-300 h-150 border border-gray-300 rounded-[10px] flex flex-col p-20">
                                    <p data-i18n = "交易总额" class = "text-[16px] font-bold text-black">交易总额</p>
                                    <p class = "text-[32px] font-bold text-black">
                                        {{format(indicators?.amt)}}
                                        <span class = "text-[16px]">USD</span>
                                    </p>
                                    <p :class = "amtCompare.state?'text-green-400':'text-red-400'"
                                       class = "text-[12px] font-bold">{{amtCompare.text}}</p>
                                </div>
                                <div class = "w-300 h-150 border border-gray-300 rounded-[10px] flex flex-col p-20">
                                    <p data-i18n = "交易笔数" class = "text-[16px] font-bold text-black">交易笔数</p>
                                    <p class = "text-[32px] font-bold text-black">{{format(indicators?.num, 0)}}</p>
                                    <p :class = "numCompare.state?'text-green-400':'text-red-400'"
                                       class = "text-[12px] font-bold">{{numCompare.text}}</p>
                                </div>
                                <div class = "w-300 h-150 border border-gray-300 rounded-[10px] flex flex-col p-20">
                                    <p data-i18n = "交易成功率" class = "text-[16px] font-bold text-black">交易成功率</p>
                                    <p class = "text-[32px] font-bold text-black">{{indicators?.successRate}}%</p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <p data-i18n = "业务数据趋势" class = "text-[20px] font-bold text-black">业务数据趋势</p>
                            <div class = "border border-gray-300 rounded-[10px] p-20">
                                <div class = "flex justify-between items-center">
                                    <span data-i18n = "交易总额" class = "text-[16px] text-black font-bold">交易总额</span>
                                    <el-date-picker
                                            v-model = "range"
                                            type = "monthrange"
                                            start-placeholder = "from"
                                            end-placeholder = "to"
                                            value-format = "YYYY-MM"
                                            :disabled-date = "disabledDate"
                                            class = "grow-0!">
                                    </el-date-picker>
                                </div>
                                <div id = "chart" class = "h-300"></div>
                            </div>
                        </div>

                        <div class = "border border-gray-300 rounded-[10px] p-20">
                            <p data-i18n = "流动性监控" class = "text-[20px] font-bold text-black">流动性监控</p>
                            <table class = "w-full">
                                <thead class = "bg-gray-100">
                                    <tr>
                                        <th data-i18n = "合作机构名称">合作机构名称</th>
                                        <th data-i18n = "账户数量">账户数量</th>
                                        <th data-i18n = "账户总额">账户总额</th>
                                        <th data-i18n = "可用金额">可用金额</th>
                                        <th data-i18n = "冻结金额">冻结金额</th>
                                    </tr>
                                </thead>
                                <tbody class = "*:border-b *:border-gray-300">
                                    <tr v-for = "item in agcnInfo">
                                        <td>{{item.corpOrgNm}}</td>
                                        <td>{{format(item.count, 0)}}</td>
                                        <td>{{format(item.amount)}} USD</td>
                                        <td>{{format(item.acAmount)}} USD</td>
                                        <td>{{format(item.unAmount)}} USD</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class = "w-1/3 flex flex-col gap-20">
                        <div>
                            <p data-i18n = "财务数据" class = "text-[20px] font-bold text-black">财务数据</p>
                            <div class = "text-black font-bold flex flex-col gap-20 p-20 bg-[url(/img/plunex/bg.png)] object-cover rounded-[10px]">
                                <div>
                                    <p data-i18n = "数币头寸可用余额" class = "text-[16px] mb-0!">数币头寸可用余额</p>
                                    <p class = "text-[24px] mb-0!">{{format(financialData?.USDT, 6)}}
                                        <span class = "text-[16px]">USDT</span>
                                    </p>
                                </div>
                                <div>
                                    <p data-i18n = "法币头寸可用余额" class = "text-[16px] mb-0!">法币头寸可用余额</p>
                                    <p class = "text-[24px] mb-0!">{{format(financialData?.USD)}}
                                        <span class = "text-[16px]">USD</span>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class = "flex justify-between items-center mb-10">
                                <p data-i18n = "交易数据" class = "text-[20px] font-bold text-black mb-0!">交易数据</p>
                                <el-dropdown trigger = "click" @command = "command">
                                    <el-button color = "#8247E5" class = "px-10 min-w-100">
                                        <div class = "flex items-center gap-10">
                                            <span>{{map[data?.txType]}}</span>
                                            <span>▼</span>
                                        </div>
                                    </el-button>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item
                                                    v-for = "(value,key) in map"
                                                    :command = "key"
                                                    class = "justify-center">
                                                {{value}}
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                            <div class = "text-black font-bold flex flex-col gap-20 p-20 bg-[#F9F8FF] rounded-[10px]">
                                <div>
                                    <p class = "text-[16px] mb-0!">
                                        <span class = "text-green-400">●</span>
                                        <span data-i18n = "今日入账" class = "ml-5"> 今日入账</span>
                                    </p>
                                    <p class = "text-[24px] mb-0!">{{format(data?.inAmt)}}
                                        <span class = "text-[16px]">USD</span>
                                    </p>
                                </div>
                                <div>
                                    <p class = "text-[16px] mb-0!">
                                        <span class = "text-red-400">●</span>
                                        <span data-i18n = "今日出账" class = "ml-5"> 今日出账</span>
                                    </p>
                                    <p class = "text-[24px] mb-0!">{{format(data?.outAmt)}}
                                        <span class = "text-[16px]">USD</span>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class = "border border-gray-300 rounded-[10px] p-20">
                            <p data-i18n = "异常警报" class = "text-[20px] font-bold text-black mb-0!">异常警报</p>
                            <div class = "h-250 flex items-center justify-center">
                                <span data-i18n = "暂无警报" class = "text-[12px] font-bold text-gray-500">暂无警报</span>
                            </div>
                            <!-- <div class = "text-[14px] text-black p-10 border-b border-gray-300 flex gap-10"> -->
                            <!--     <span class = "text-red-400">⚠</span> -->
                            <!--     <span class = "line-clamp-2">异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报</span> -->
                            <!-- </div> -->
                            <!-- <div class = "text-[14px] text-black p-10 border-b border-gray-300 flex gap-10"> -->
                            <!--     <span class = "text-red-400">⚠</span> -->
                            <!--     <span class = "line-clamp-2">异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报</span> -->
                            <!-- </div> -->
                            <!-- <div class = "text-[14px] text-black p-10 border-b border-gray-300 flex gap-10"> -->
                            <!--     <span class = "text-red-400">⚠</span> -->
                            <!--     <span class = "line-clamp-2">异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报异常警报</span> -->
                            <!-- </div> -->
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div th:replace = "script"></div>
        <script src = "/js/plunex/dayjs.js"></script>
        <script src = "/js/plunex/echarts.min.js"></script>
        <script src = "/js/plunex/vue.js"></script>
        <script src = "/js/plunex/element-plus.js"></script>

        <script>

            i18nLoad.then(() => {
                const {ElMessage} = ElementPlus

                const app = Vue.createApp({
                    data: () => ({
                        type: 'day',
                        indicators: undefined,
                        financialData: undefined,
                        range: [dayjs().startOf('year').format('YYYY-MM'), dayjs().format('YYYY-MM')],
                        dataTrend: undefined,
                        agcnInfo: undefined,
                        dhDataStatistics: undefined,
                        data: undefined,
                        map: {},
                        currentLang: $.cookie('lang') || 'zh' // 添加响应式的语言状态
                    }),

                    computed: {

                        amtCompare() {
                            const compare = this.indicators?.amtCompareYesterday
                            if (!compare) return {text: '', state: false}
                            if (compare.startsWith('-')) {
                                return {
                                    text: ` ↘ 比昨日下降 ${compare.replace('-', '')}`,
                                    state: false
                                }
                            } else {
                                return {
                                    text: `↗ ${this.t('比昨日上升')} ${compare}`,
                                    state: true
                                }
                            }
                        },

                        numCompare() {
                            const compare = this.indicators?.numCompareYesterday
                            if (!compare) return {text: '', state: false}
                            if (compare.startsWith('-')) {
                                return {
                                    text: ` ↘ ${this.t('比昨日下降')} ${compare.replace('-', '')}`,
                                    state: false
                                }
                            } else {
                                return {
                                    text: `↗ ${this.t('比昨日上升')} ${compare}`,
                                    state: true
                                }
                            }
                        },

                        option() {

                            if (!this.dataTrend) return

                            const months = [
                                'January', 'February', 'March', 'April', 'May', 'June',
                                'July', 'August', 'September', 'October', 'November', 'December'
                            ];

                            const label = this.dataTrend.map(item => $.cookie('lang') === 'zh' ? `${item.yearMonth.at(-1)}月` : months[item.yearMonth.at(-1) - 1])

                            const value = this.dataTrend.map(item => Number(item.totalAmount))

                            return {
                                tooltip: {
                                    trigger: 'axis'
                                },
                                grid: {
                                    top: '10%',
                                    left: '0%',
                                    right: '0%',
                                    bottom: '0%',
                                    containLabel: true
                                },
                                xAxis: {
                                    type: 'category',
                                    data: label,
                                    axisLine: {
                                        lineStyle: {
                                            color: '#BDC3C7'
                                        }
                                    },
                                    axisLabel: {
                                        color: '#7F8C8D'
                                    }
                                },
                                yAxis: {
                                    type: 'value',
                                    axisLine: {
                                        lineStyle: {
                                            color: '#BDC3C7'
                                        }
                                    },
                                    axisLabel: {
                                        color: '#7F8C8D'
                                    },
                                    splitLine: {
                                        lineStyle: {
                                            color: '#ECF0F1'
                                        }
                                    }
                                },
                                series: [{
                                    name: this.t('交易总额'),
                                    type: 'line',
                                    smooth: true,
                                    data: value,
                                    lineStyle: {
                                        color: '#8247E5',
                                        width: 3
                                    },
                                    areaStyle: {
                                        color: {
                                            type: 'linear',
                                            x: 0,
                                            y: 0,
                                            x2: 0,
                                            y2: 1,
                                            colorStops: [{
                                                offset: 0, color: 'rgba(155, 89, 182, 0.3)'
                                            }, {
                                                offset: 1, color: 'rgba(155, 89, 182, 0.1)'
                                            }]
                                        }
                                    },
                                    itemStyle: {
                                        color: '#8247E5'
                                    }
                                }]
                            }
                        }

                    },

                    watch: {

                        type: {
                            handler(newValue) {
                                $.get('/bil/DataBoard/business/indicators', {type: newValue}, ({body}) => this.indicators = body)
                            },
                            immediate: true,
                        },

                        range: {
                            handler(newValue, oldValue) {
                                if (JSON.stringify(newValue) === JSON.stringify(oldValue)) return
                                if (dayjs(newValue[1]).diff(dayjs(newValue[0]), 'month') > 12) {
                                    ElMessage.primary(this.t('范围不得超过12个月'))
                                    this.range = oldValue
                                    return;
                                }
                                if (newValue[0] === newValue[1]) {
                                    ElMessage.primary(this.t('请至少选择两个月'))
                                    this.range = oldValue
                                    return;
                                }
                                $.get('/bil/DataBoard/business/dataTrend',
                                        {startDate: newValue[0], endDate: newValue[1]},
                                        ({body}) => this.dataTrend = body
                                )
                            },
                            immediate: true
                        }

                    },

                    created() {
                        this.map = {
                            DS: this.t('收款'),
                            DC: this.t('充值'),
                            DH: this.t('兑换'),
                            DZ: this.t('转账'),
                            DX: this.t('提现'),
                            SXF: this.t('手续费')
                        }
                        $.get('/bil/DataBoard/business/financialData', ({body}) => this.financialData = body)
                        $.get('/bil/DataBoard/agcnInfo', ({body}) => this.agcnInfo = body)
                        $.get('/bil/DataBoard/dhDataStatistics', ({body}) => {
                            this.dhDataStatistics = body
                            this.data = this.dhDataStatistics.find(item => item.txType === 'DS')
                        })
                    },

                    mounted() {

                        const chart = echarts.init($('#chart').get(0))

                        this.$watch('option', (newValue) => {
                            chart.resize()
                            chart.setOption(newValue)
                        }, {deep: true})

                        window.addEventListener('resize', () => chart.resize());

                    },

                    methods: {
                        format(number, fraction = 2) {
                            return number?.toLocaleString(undefined, {
                                minimumFractionDigits: fraction,
                                maximumFractionDigits: fraction
                            })
                        },

                        disabledDate(t) {
                            return dayjs(t).isAfter(dayjs().startOf('month'))
                        },

                        command(txType) {
                            this.data = this.dhDataStatistics.find(item => item.txType === txType)
                        },

                        t(key) {
                            // 确保在语言变化时重新获取翻译
                            this.currentLang; // 触发响应式依赖
                            return $.i18n.t(key)
                        },

                        // 添加语言更新方法
                        updateLanguage() {
                            this.currentLang = $.cookie('lang') || 'zh';
                            // 重新创建map对象
                            this.map = {
                                DS: this.t('收款'),
                                DC: this.t('充值'),
                                DH: this.t('兑换'),
                                DZ: this.t('转账'),
                                DX: this.t('提现'),
                                SXF: this.t('手续费')
                            };
                        }
                    },
                });

                // 挂载Vue应用并保存实例到全局变量
                window.vueApp = app.use(ElementPlus).mount("#wrapper");
            })

        </script>

    </body>
</html>
