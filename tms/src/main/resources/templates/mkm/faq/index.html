<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="faq.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="faq.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.cmmmgr"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="faq.content"></strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2"></div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- search form -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="searchId" data-i18n="faq.id"></label>
                                            <input name="id" id="searchId" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchQuestionContent"
                                                data-i18n="faq.questionContent"></label>
                                            <input name="questionContent" id="searchQuestionContent"
                                                class="form-control" value="" />
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary" id="btnAdd">
                                                <span data-i18n="faq.add"></span>
                                            </button>
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="faq.search"></button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()" data-i18n="faq.reset"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- data table -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="faqTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="faq.id"></th>
                                                <th data-i18n="faq.questionContent"></th>
                                                <th data-i18n="faq.language"></th>
                                                <th data-i18n="faq.createdBy"></th>
                                                <th data-i18n="faq.createdDate"></th>
                                                <th data-i18n="faq.operations"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- add/edit modal -->
    <div class="modal fade" id="faqModal" tabindex="-1" role="dialog" aria-labelledby="faqModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="faqModalLabel" data-i18n="faq.addTitle"></h4>
                </div>
                <div class="modal-body">
                    <form id="faqForm" class="form-horizontal">
                        <input type="hidden" id="id" name="id" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="questionContent"
                                data-i18n="faq.questionContent"></label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="questionContent" name="questionContent" rows="3"
                                    required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="answerContent"
                                data-i18n="faq.answerContent"></label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="answerContent" name="answerContent" rows="5"
                                    required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="language" data-i18n="faq.language"></label>
                            <div class="col-sm-9">
                                <select class="form-control" id="language" name="language" required>
                                    <option value="" data-i18n="faq.selectLanguage"></option>
                                    <option value="zh" data-i18n="faq.chinese"></option>
                                    <option value="en" data-i18n="faq.english"></option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" data-i18n="faq.cancel"></button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="faq.save"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- detail modal -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="faq.detailTitle"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="faq.questionContent"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailQuestionContent"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="faq.answerContent"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAnswerContent"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="faq.createdBy"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreatedBy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="faq.createdDate"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreatedDate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="faq.updatedBy"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdatedBy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="faq.updatedDate"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdatedDate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="faq.language"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailLanguage"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" data-i18n="faq.close"></button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            i18nLoad.then(function () {
                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/js/Chinese.json';
                        break;
                    case 'en':
                        languageUrl = '/js/English.json';
                        break;
                    default:
                        languageUrl = '/js/Chinese.json';
                }

                // init DataTables
                table = $('#faqTable').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/mkm/faq/findAll',
                        type: 'post',
                        data: function (d) {
                            // add extra query params
                            d.extra_search = {
                                "id": $("#searchId").val() || "",
                                "questionContent": $("#searchQuestionContent").val() || "",
                                "language": $("#searchLanguage").val() || ""
                            };
                            return JSON.stringify(d);
                        },
                        dataSrc: function (json) {
                            // make sure return array
                            return json.data || [];
                        },
                        error: function (xhr, error, thrown) {
                            console.error('DataTables AJAX error:', error, thrown);
                            toastr.error(i18n.t('faq.loadDataError'));
                        }
                    },
                    serverSide: true,
                    searching: false, // disable built-in search
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    columns: [
                        { data: 'id' },
                        { data: 'questionContent' },
                        {
                            data: 'language',
                            render: function (data) {
                                if (data === 'zh') return i18n.t('faq.chinese');
                                if (data === 'en') return i18n.t('faq.english');
                                return data || '';
                            }
                        },
                        { data: 'createdBy' },
                        {
                            data: 'createdDate',
                            render: function (data) {
                                if (!data) return '';
                                return new Date(data).toLocaleString();
                            }
                        },
                        {
                            data: null,
                            orderable: false,
                            render: function (data, type, row) {
                                var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">' + i18n.t('faq.detail') + '</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.id + '">' + i18n.t('faq.edit') + '</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.id + '">' + i18n.t('faq.delete') + '</button>';
                                return buttons;
                            }
                        }
                    ],
                    buttons: [
                        { extend: 'copyHtml5' },
                        { extend: 'csvHtml5' },
                        { extend: 'excelHtml5', title: i18n.t('faq.listTitle') }
                    ]
                });

                // add button
                $("#btnAdd").click(function () {
                    $("#faqModalLabel").text(i18n.t('faq.addTitle'));
                    $("#faqForm")[0].reset();
                    $("#id").val("");
                    $("#faqModal").modal("show");
                });

                // save button
                $("#btnSave").click(function () {
                    if (!$("#faqForm").valid()) {
                        toastr.error(i18n.t('faq.requiredError'));
                        return;
                    }

                    var id = $("#id").val();
                    var formData = $("#faqForm").serialize();
                    var url = id ? "/mkm/faq/modify/" + id : "/mkm/faq/add";

                    $.ajax({
                        url: url,
                        type: "POST",
                        data: formData,
                        success: function (res) {
                            if (res.result === "MKM00000") {
                                toastr.success(i18n.t('faq.opSuccess'));
                                $("#faqModal").modal("hide");
                                table.ajax.reload();
                            } else {
                                toastr.error(i18n.t('faq.opError') + res.result);
                            }
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('faq.opError') + error);
                        }
                    });
                });

                // view detail
                $(document).on("click", ".view-detail", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "/mkm/faq/getFAQ",
                        type: "POST",
                        data: { id: id },
                        success: function (data) {
                            $("#detailQuestionContent").text(data.questionContent || '');
                            $("#detailAnswerContent").text(data.answerContent || '');
                            $("#detailCreatedBy").text(data.createdBy || '');
                            $("#detailCreatedDate").text(data.createdDate ? new Date(data.createdDate).toLocaleString() : '');
                            $("#detailUpdatedBy").text(data.updatedBy || '');
                            $("#detailUpdatedDate").text(data.updatedDate ? new Date(data.updatedDate).toLocaleString() : '');
                            $("#detailLanguage").text(data.language === 'zh' ? i18n.t('faq.chinese') : data.language === 'en' ? i18n.t('faq.english') : (data.language || ''));
                            $("#detailModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('faq.getDetailError') + error);
                        }
                    });
                });

                // edit button
                $(document).on("click", ".btn-edit", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "/mkm/faq/getFAQ",
                        type: "POST",
                        data: { id: id },
                        success: function (data) {
                            $("#faqModalLabel").text(i18n.t('faq.editTitle'));
                            $("#id").val(data.id);
                            $("#questionContent").val(data.questionContent);
                            $("#answerContent").val(data.answerContent);
                            $("#language").val(data.language);
                            $("#faqModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('faq.getDetailError') + error);
                        }
                    });
                });

                // delete button
                $(document).on("click", ".btn-delete", function () {
                    var id = $(this).data("id");
                    swal({
                        title: i18n.t('faq.deleteConfirm'),
                        text: i18n.t('faq.deleteConfirmText'),
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: i18n.t('faq.confirmDelete'),
                        cancelButtonText: i18n.t('faq.cancel'),
                        closeOnConfirm: false
                    }, function () {
                        $.ajax({
                            url: "/mkm/faq/delete/" + id,
                            type: "DELETE",
                            success: function (res) {
                                if (res === "1") {
                                    swal(i18n.t('faq.deleteSuccess'), i18n.t('faq.deleted'), "success");
                                    table.ajax.reload();
                                } else {
                                    swal(i18n.t('faq.deleteError'), i18n.t('faq.tryAgain'), "error");
                                }
                            },
                            error: function (xhr, status, error) {
                                swal(i18n.t('faq.deleteError'), i18n.t('faq.error') + error, "error");
                            }
                        });
                    });
                });
            });

            // form validation
            $("#faqForm").validate({
                rules: {
                    questionContent: {
                        required: true,
                        maxlength: 300
                    },
                    answerContent: {
                        required: true,
                        maxlength: 300
                    },
                    language: {
                        required: true
                    }
                },
                messages: {
                    questionContent: {
                        required: i18n.t('faq.questionRequired'),
                        maxlength: i18n.t('faq.questionMaxLength')
                    },
                    answerContent: {
                        required: i18n.t('faq.answerRequired'),
                        maxlength: i18n.t('faq.answerMaxLength')
                    },
                    language: {
                        required: i18n.t('faq.languageRequired')
                    }
                }
            });
        });

        // search method
        function search() {
            table.ajax.reload();
        }

        // reset form
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>