<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="msginfo.title">国际化管理</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="msginfo.content">国际化管理</h2>
                    <ol class="breadcrumb">

                        <li>
                            <a data-i18n="nav.parammgr">营销管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="msginfo.content">国际化管理</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">

                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="searchMsgCd"
                                                data-i18n="msginfo.msgCd">消息代码</label>
                                            <input name="msgCd" id="searchMsgCd" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchLanguage"
                                                data-i18n="msginfo.language">语言</label>
                                            <select name="language" id="searchLanguage" class="form-control">
                                                <option value="" data-i18n="msginfo.selectLanguage">请选择语言</option>
                                                <option value="zh" data-i18n="msginfo.chinese">中文</option>
                                                <option value="en" data-i18n="msginfo.english">英文</option>
                                                <option value="km" data-i18n="msginfo.khmer">高棉语</option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchMsgInfo"
                                                data-i18n="msginfo.msgInfo">消息信息</label>
                                            <input name="msgInfo" id="searchMsgInfo" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchScenario"
                                                data-i18n="msginfo.scenario">场景</label>
                                            <input name="scenario" id="searchScenario" class="form-control" value="" />
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary" id="btnAdd">
                                                 <span data-i18n="msginfo.add">新增</span>
                                            </button>
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="msginfo.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()" data-i18n="msginfo.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="msgInfoTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="msginfo.msgCd">消息代码</th>
                                                <th data-i18n="msginfo.language">语言</th>
                                                <th data-i18n="msginfo.msgInfo">消息信息</th>
                                                <th data-i18n="msginfo.scenario">场景</th>
                                                <th data-i18n="msginfo.createTime">创建时间</th>
                                                <th data-i18n="msginfo.operations">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="msgInfoModal" tabindex="-1" role="dialog" aria-labelledby="msgInfoModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="msgInfoModalLabel" data-i18n="msginfo.addTitle">新增国际化消息</h4>
                </div>
                <div class="modal-body">
                    <form id="msgInfoForm" class="form-horizontal">
                        <input type="hidden" id="originalMsgCd" name="originalMsgCd" />
                        <input type="hidden" id="originalLanguage" name="originalLanguage" />
                        <input type="hidden" id="originalScenario" name="originalScenario" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="msgCd" data-i18n="msginfo.msgCd">消息代码</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="msgCd" name="msgCd" maxlength="8" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="language" data-i18n="msginfo.language">语言</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="language" name="language" required>
                                    <option value="" data-i18n="msginfo.selectLanguage">请选择语言</option>
                                    <option value="zh" data-i18n="msginfo.chinese">中文</option>
                                    <option value="en" data-i18n="msginfo.english">英文</option>
                                    <option value="km" data-i18n="msginfo.khmer">高棉语</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="msgInfo" data-i18n="msginfo.msgInfo">消息信息</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="msgInfo" name="msgInfo" rows="3" maxlength="200"
                                    required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="scenario" data-i18n="msginfo.scenario">场景</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="scenario" name="scenario" maxlength="30"
                                    value="*">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="msginfo.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="msginfo.save">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="msginfo.detailTitle">国际化消息详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="msginfo.msgCd">消息代码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailMsgCd"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="msginfo.language">语言</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailLanguage"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="msginfo.msgInfo">消息信息</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailMsgInfo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="msginfo.scenario">场景</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailScenario"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="msginfo.createTime">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="msginfo.modifyTime">修改时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailModifyTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="msginfo.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        var isEditMode = false;
        $(document).ready(function () {
            i18nLoad.then(function () {
                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/js/Chinese.json';
                        break;
                    case 'en':
                        languageUrl = '/js/English.json';
                        break;
                    default:
                        languageUrl = '/js/Chinese.json';
                }

                // 初始化DataTables
                table = $('#msgInfoTable').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/mkm/msginfo/findAll',
                        type: 'post',
                        data: function (d) {
                            // 添加额外的查询参数
                            d.extra_search = {
                                "msgCd": $("#searchMsgCd").val() || "",
                                "language": $("#searchLanguage").val() || "",
                                "msgInfo": $("#searchMsgInfo").val() || "",
                                "scenario": $("#searchScenario").val() || ""
                            };
                            return JSON.stringify(d);
                        },
                        dataSrc: function (json) {
                            // 确保返回的是数组
                            return json.data || [];
                        },
                        error: function (xhr, error, thrown) {
                            console.error('DataTables AJAX error:', error, thrown);
                            toastr.error(i18n.t('msginfo.loadDataError'));
                        }
                    },
                    serverSide: true,
                    searching: false, // 禁用内置搜索
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    columns: [
                        { data: 'msgCd' },
                        {
                            data: 'language',
                            render: function (data) {
                                if (data === 'zh') return i18n.t('msginfo.chinese');
                                if (data === 'en') return i18n.t('msginfo.english');
                                if (data === 'km') return i18n.t('msginfo.khmer');
                                return data || '';
                            }
                        },
                        {
                            data: 'msgInfo',
                            render: function (data) {
                                if (!data) return '';
                                return data.length > 50 ? data.substring(0, 50) + '...' : data;
                            }
                        },
                        { data: 'scenario' },
                        {
                            data: 'createTime',
                            render: function (data) {
                                if (!data) return '';
                                return new Date(data).toLocaleString();
                            }
                        },
                        {
                            data: null,
                            orderable: false,
                            render: function (data, type, row) {
                                var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-msgcd="' + row.msgCd + '" data-language="' + row.language + '" data-scenario="' + row.scenario + '" data-i18n="msginfo.detail">详情</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-msgcd="' + row.msgCd + '" data-language="' + row.language + '" data-scenario="' + row.scenario + '" data-i18n="msginfo.edit">编辑</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-msgcd="' + row.msgCd + '" data-language="' + row.language + '" data-scenario="' + row.scenario + '" data-i18n="msginfo.delete">删除</button>';
                                return buttons;
                            }
                        }
                    ],
                    buttons: [
                        { extend: 'copyHtml5' },
                        { extend: 'csvHtml5' },
                        { extend: 'excelHtml5', title: i18n.t('msginfo.messageList') }
                    ]
                });

                // 新增按钮
                $("#btnAdd").click(function () {
                    isEditMode = false;
                    $("#msgInfoModalLabel").text(i18n.t('msginfo.addTitle'));
                    $("#msgInfoForm")[0].reset();
                    $("#originalMsgCd").val("");
                    $("#originalLanguage").val("");
                    $("#originalScenario").val("");
                    $("#scenario").val("*");
                    $("#msgInfoModal").modal("show");
                });

                // 保存按钮
                $("#btnSave").click(function () {
                    if (!$("#msgInfoForm").valid()) {
                        toastr.error(i18n.t('msginfo.fillRequired'));
                        return;
                    }

                    var formData = $("#msgInfoForm").serialize();
                    var url = isEditMode ? "/mkm/msginfo/modify" : "/mkm/msginfo/add";

                    $.ajax({
                        url: url,
                        type: "POST",
                        data: formData,
                        success: function (res) {
                            if (res.result === "MKM00000") {
                                toastr.success(i18n.t('msginfo.operationSuccess'));
                                $("#msgInfoModal").modal("hide");
                                table.ajax.reload();
                            } else {
                                toastr.error(i18n.t('msginfo.operationFailed') + ': ' + res.result);
                            }
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('msginfo.operationFailed') + ': ' + error);
                        }
                    });
                });

                // 查看详情
                $(document).on("click", ".view-detail", function () {
                    var msgCd = $(this).data("msgcd");
                    var language = $(this).data("language");
                    var scenario = $(this).data("scenario");
                    $.ajax({
                        url: "/mkm/msginfo/getMsgInfo",
                        type: "POST",
                        data: { msgCd: msgCd, language: language, scenario: scenario },
                        success: function (data) {
                            $("#detailMsgCd").text(data.msgCd || '');
                            $("#detailLanguage").text(getLanguageText(data.language) || '');
                            $("#detailMsgInfo").text(data.msgInfo || '');
                            $("#detailScenario").text(data.scenario || '');
                            $("#detailCreateTime").text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                            $("#detailModifyTime").text(data.modifyTime ? new Date(data.modifyTime).toLocaleString() : '');
                            $("#detailModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('msginfo.getDetailFailed') + ': ' + error);
                        }
                    });
                });

                // 编辑按钮
                $(document).on("click", ".btn-edit", function () {
                    isEditMode = true;
                    var msgCd = $(this).data("msgcd");
                    var language = $(this).data("language");
                    var scenario = $(this).data("scenario");
                    $.ajax({
                        url: "/mkm/msginfo/getMsgInfo",
                        type: "POST",
                        data: { msgCd: msgCd, language: language, scenario: scenario },
                        success: function (data) {
                            $("#msgInfoModalLabel").text(i18n.t('msginfo.editTitle'));
                            $("#originalMsgCd").val(data.msgCd);
                            $("#originalLanguage").val(data.language);
                            $("#originalScenario").val(data.scenario);
                            $("#msgCd").val(data.msgCd);
                            $("#language").val(data.language);
                            $("#msgInfo").val(data.msgInfo);
                            $("#scenario").val(data.scenario);
                            $("#msgInfoModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('msginfo.getDetailFailed') + ': ' + error);
                        }
                    });
                });

                // 删除按钮
                $(document).on("click", ".btn-delete", function () {
                    var msgCd = $(this).data("msgcd");
                    var language = $(this).data("language");
                    var scenario = $(this).data("scenario");
                    swal({
                        title: i18n.t('msginfo.confirmDelete'),
                        text: i18n.t('msginfo.deleteWarning'),
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: i18n.t('msginfo.confirmDeleteBtn'),
                        cancelButtonText: i18n.t('msginfo.cancel'),
                        closeOnConfirm: false
                    }, function () {
                        $.ajax({
                            url: "/mkm/msginfo/delete",
                            type: "DELETE",
                            data: { msgCd: msgCd, language: language, scenario: scenario },
                            success: function (res) {
                                if (res === "1") {
                                    swal(i18n.t('msginfo.deleteSuccess'), i18n.t('msginfo.deleteSuccessMsg'), "success");
                                    table.ajax.reload();
                                } else {
                                    swal(i18n.t('msginfo.deleteFailed'), i18n.t('msginfo.retryLater'), "error");
                                }
                            },
                            error: function (xhr, status, error) {
                                swal(i18n.t('msginfo.deleteFailed'), i18n.t('msginfo.error') + ': ' + error, "error");
                            }
                        });
                    });
                });

                // 表单验证
                $("#msgInfoForm").validate({
                    rules: {
                        msgCd: {
                            required: true,
                            maxlength: 8
                        },
                        language: {
                            required: true
                        },
                        msgInfo: {
                            required: true,
                            maxlength: 200
                        },
                        scenario: {
                            maxlength: 30
                        }
                    },
                    messages: {
                        msgCd: {
                            required: i18n.t('msginfo.msgCdRequired'),
                            maxlength: i18n.t('msginfo.msgCdMaxLength')
                        },
                        language: {
                            required: i18n.t('msginfo.languageRequired')
                        },
                        msgInfo: {
                            required: i18n.t('msginfo.msgInfoRequired'),
                            maxlength: i18n.t('msginfo.msgInfoMaxLength')
                        },
                        scenario: {
                            maxlength: i18n.t('msginfo.scenarioMaxLength')
                        }
                    }
                });
            });
        });

        // 获取语言显示文本
        function getLanguageText(language) {
            if (language === 'zh') return i18n.t('msginfo.chinese');
            if (language === 'en') return i18n.t('msginfo.english');
            if (language === 'km') return i18n.t('msginfo.khmer');
            return language || '';
        }

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>