<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="pact.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="pact.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.cmmmgr"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="pact.content"></strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2"></div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- search form -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="searchId" data-i18n="pact.id"></label>
                                            <input name="id" id="searchId" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchTitle"
                                                data-i18n="pact.pactTitle"></label>
                                            <input name="title" id="searchTitle" class="form-control" value="" />
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary" id="btnAdd">
                                                 <span data-i18n="pact.add"></span>
                                            </button>
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="pact.search"></button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()" data-i18n="pact.reset"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- data table -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="pactTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="pact.id"></th>
                                                <th data-i18n="pact.pactTitle"></th>
                                                <th data-i18n="pact.type"></th>
                                                <th data-i18n="pact.status"></th>
                                                <th data-i18n="pact.createUser"></th>
                                                <th data-i18n="pact.createTime"></th>
                                                <th data-i18n="pact.operations"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- add/edit modal -->
    <div class="modal fade" id="pactModal" tabindex="-1" role="dialog" aria-labelledby="pactModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="pactModalLabel" data-i18n="pact.addTitle"></h4>
                </div>
                <div class="modal-body">
                    <form id="pactForm" class="form-horizontal">
                        <input type="hidden" id="id" name="id" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="title" data-i18n="pact.pactTitle"></label>
                            <div class="col-sm-9">
                                <input class="form-control" id="title" name="title" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="type" data-i18n="pact.type"></label>
                            <div class="col-sm-9">
                                <select class="form-control" id="type" name="type" required>
                                    <option value="USER" data-i18n="pact.userPact"></option>
                                    <option value="MERCHANT" data-i18n="pact.merchantPact"></option>
                                    <option value="PAYMENT" data-i18n="pact.paymentPact"></option>
                                    <option value="OTHER" data-i18n="pact.other"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="status" data-i18n="pact.status"></label>
                            <div class="col-sm-9">
                                <select class="form-control" id="status" name="status" required>
                                    <option value="ACTIVE" data-i18n="pact.active"></option>
                                    <option value="INACTIVE" data-i18n="pact.inactive"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="content" data-i18n="pact.pactContent"></label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="content" name="content" rows="10"
                                    required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="remark" data-i18n="pact.remark"></label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="remark" name="remark" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" data-i18n="pact.cancel"></button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="pact.save"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- detail modal -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="pact.detailTitle"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.pactTitle"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailTitle"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.type"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.status"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailStatus"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.pactContent"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailContent" style="white-space: pre-line;"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.remark"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailRemark"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.createUser"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateUser"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.createTime"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.updateUser"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdateUser"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="pact.updateTime"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdateTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" data-i18n="pact.close"></button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            i18nLoad.then(function () {
                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/js/Chinese.json';
                        break;
                    case 'en':
                        languageUrl = '/js/English.json';
                        break;
                    default:
                        languageUrl = '/js/Chinese.json';
                }

                // init DataTables
                table = $('#pactTable').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/mkm/pact/findAll',
                        type: 'post',
                        data: function (d) {
                            // add extra query params
                            d.extra_search = {
                                "id": $("#searchId").val() || "",
                                "title": $("#searchTitle").val() || ""
                            };
                            return JSON.stringify(d);
                        },
                        dataSrc: function (json) {
                            // make sure return array
                            return json.data || [];
                        },
                        error: function (xhr, error, thrown) {
                            console.error('DataTables AJAX error:', error, thrown);
                            toastr.error(i18n.t('pact.loadDataError'));
                        }
                    },
                    serverSide: true,
                    searching: false, // disable built-in search
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    columns: [
                        { data: 'id' },
                        { data: 'title' },
                        {
                            data: 'type',
                            render: function (data) {
                                switch (data) {
                                    case 'USER': return i18n.t('pact.userPact');
                                    case 'MERCHANT': return i18n.t('pact.merchantPact');
                                    case 'PAYMENT': return i18n.t('pact.paymentPact');
                                    case 'OTHER': return i18n.t('pact.other');
                                    default: return data;
                                }
                            }
                        },
                        {
                            data: 'status',
                            render: function (data) {
                                if (data === 'ACTIVE') {
                                    return '<span class="label label-primary">' + i18n.t('pact.active') + '</span>';
                                } else if (data === 'INACTIVE') {
                                    return '<span class="label label-default">' + i18n.t('pact.inactive') + '</span>';
                                } else {
                                    return data;
                                }
                            }
                        },
                        { data: 'createUser' },
                        {
                            data: 'createTime',
                            render: function (data) {
                                if (!data) return '';
                                return new Date(data).toLocaleString();
                            }
                        },
                        {
                            data: null,
                            orderable: false,
                            render: function (data, type, row) {
                                var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">' + i18n.t('pact.detail') + '</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.id + '">' + i18n.t('pact.edit') + '</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.id + '">' + i18n.t('pact.delete') + '</button>';
                                return buttons;
                            }
                        }
                    ],
                    buttons: [
                        { extend: 'copyHtml5' },
                        { extend: 'csvHtml5' },
                        { extend: 'excelHtml5', title: i18n.t('pact.listTitle') }
                    ]
                });

                // add button
                $("#btnAdd").click(function () {
                    $("#pactModalLabel").text(i18n.t('pact.addTitle'));
                    $("#pactForm")[0].reset();
                    $("#id").val("");
                    $("#pactModal").modal("show");
                });

                // save button
                $("#btnSave").click(function () {
                    if (!$("#pactForm").valid()) {
                        toastr.error(i18n.t('pact.requiredError'));
                        return;
                    }

                    var id = $("#id").val();
                    var formData = $("#pactForm").serialize();
                    var url = id ? "/mkm/pact/modify/" + id : "/mkm/pact/add";

                    $.ajax({
                        url: url,
                        type: "POST",
                        data: formData,
                        success: function (res) {
                            if (res.result === "MKM00000") {
                                toastr.success(i18n.t('pact.opSuccess'));
                                $("#pactModal").modal("hide");
                                table.ajax.reload();
                            } else {
                                toastr.error(i18n.t('pact.opError') + res.result);
                            }
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('pact.opError') + error);
                        }
                    });
                });

                // view detail
                $(document).on("click", ".view-detail", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "/mkm/pact/getPact",
                        type: "POST",
                        data: { id: id },
                        success: function (data) {
                            $("#detailTitle").text(data.title || '');

                            // type conversion
                            var typeText = '';
                            switch (data.type) {
                                case 'USER': typeText = i18n.t('pact.userPact'); break;
                                case 'MERCHANT': typeText = i18n.t('pact.merchantPact'); break;
                                case 'PAYMENT': typeText = i18n.t('pact.paymentPact'); break;
                                case 'OTHER': typeText = i18n.t('pact.other'); break;
                                default: typeText = data.type;
                            }
                            $("#detailType").text(typeText);

                            // status conversion
                            var statusText = data.status === 'ACTIVE' ? i18n.t('pact.active') : i18n.t('pact.inactive');
                            $("#detailStatus").text(statusText);

                            $("#detailContent").text(data.content || '');
                            $("#detailRemark").text(data.remark || '');
                            $("#detailCreateUser").text(data.createUser || '');
                            $("#detailCreateTime").text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                            $("#detailUpdateUser").text(data.updateUser || '');
                            $("#detailUpdateTime").text(data.updateTime ? new Date(data.updateTime).toLocaleString() : '');
                            $("#detailModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('pact.getDetailError') + error);
                        }
                    });
                });

                // edit button
                $(document).on("click", ".btn-edit", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "/mkm/pact/getPact",
                        type: "POST",
                        data: { id: id },
                        success: function (data) {
                            $("#pactModalLabel").text(i18n.t('pact.editTitle'));
                            $("#id").val(data.id);
                            $("#title").val(data.title);
                            $("#type").val(data.type);
                            $("#status").val(data.status);
                            $("#content").val(data.content);
                            $("#remark").val(data.remark);
                            $("#pactModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('pact.getDetailError') + error);
                        }
                    });
                });

                // delete button
                $(document).on("click", ".btn-delete", function () {
                    var id = $(this).data("id");
                    swal({
                        title: i18n.t('pact.deleteConfirm'),
                        text: i18n.t('pact.deleteConfirmText'),
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: i18n.t('pact.confirmDelete'),
                        cancelButtonText: i18n.t('pact.cancel'),
                        closeOnConfirm: false
                    }, function () {
                        $.ajax({
                            url: "/mkm/pact/delete/" + id,
                            type: "DELETE",
                            success: function (res) {
                                if (res === "1") {
                                    swal(i18n.t('pact.deleteSuccess'), i18n.t('pact.deleted'), "success");
                                    table.ajax.reload();
                                } else {
                                    swal(i18n.t('pact.deleteError'), i18n.t('pact.tryAgain'), "error");
                                }
                            },
                            error: function (xhr, status, error) {
                                swal(i18n.t('pact.deleteError'), i18n.t('pact.error') + error, "error");
                            }
                        });
                    });
                });

                // form validation
                $("#pactForm").validate({
                    rules: {
                        title: {
                            required: true,
                            maxlength: 100
                        },
                        type: {
                            required: true
                        },
                        status: {
                            required: true
                        },
                        content: {
                            required: true,
                            maxlength: 2000
                        },
                        remark: {
                            maxlength: 500
                        }
                    },
                    messages: {
                        title: {
                            required: i18n.t('pact.titleRequired'),
                            maxlength: i18n.t('pact.titleMaxLength')
                        },
                        type: {
                            required: i18n.t('pact.typeRequired')
                        },
                        status: {
                            required: i18n.t('pact.statusRequired')
                        },
                        content: {
                            required: i18n.t('pact.contentRequired'),
                            maxlength: i18n.t('pact.contentMaxLength')
                        },
                        remark: {
                            maxlength: i18n.t('pact.remarkMaxLength')
                        }
                    }
                });
            });
        });

        // search method
        function search() {
            table.ajax.reload();
        }

        // reset form
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>