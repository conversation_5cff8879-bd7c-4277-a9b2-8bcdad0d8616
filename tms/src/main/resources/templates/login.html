<!DOCTYPE html>
<html xmlns:th = "http://www.thymeleaf.org">

    <head id = "login-head">

        <meta charset = "utf-8" />
        <meta name = "viewport" content = "width=device-width, initial-scale=1.0" />

        <title data-i18n = "login.title"></title>

        <link th:href = "@{/css/bootstrap.min.css}" rel = "stylesheet" />
        <link th:href = "@{/font-awesome/css/font-awesome.css}" rel = "stylesheet" />

        <link th:href = "@{/css/animate.css}" rel = "stylesheet" />
        <link th:href = "@{/css/style.css}" rel = "stylesheet" />
        <link th:href = "@{/css/plunex/login.css}" rel = "stylesheet" />

    </head>

    <body class = "login-page">

        <div class = "login-container">
            <div class = "login-card">
                <!-- Logo Section -->
                <div class = "logo-section">
                    <img th:src = "@{/img/plunex/plunex-logo.png}" alt = "Plunex Logo" class = "logo-image">
                </div>

                <!-- Login Form -->
                <div id = "login-body">
                    <!-- Welcome Text -->
                    <div class = "welcome-text">
                        <h2 data-i18n = "login.welcome">Welcome to Plunex Portal !</h2>
                        <p data-i18n = "login.subtitle">Please sign-in to your account.</p>
                    </div>

                    <!-- Login Form -->
                    <form class = "login-form" role = "form" th:action = "@{/login}" method = "post">
                        <!-- Username Field -->
                        <div class = "form-group">
                            <div class = "input-wrapper">
                                <input type = "text"
                                       id = "username"
                                       name = "username"
                                       class = "form-control"
                                       data-i18n = "[placeholder]login.username"
                                       placeholder = "User name"
                                       required>
                            </div>
                        </div>

                        <!-- Password Field -->
                        <div class = "form-group">
                            <div class = "input-wrapper">
                                <input type = "password"
                                       id = "password"
                                       name = "password"
                                       class = "form-control"
                                       data-i18n = "[placeholder]login.password"
                                       placeholder = "Password"
                                       required>
                            </div>
                        </div>

                        <!-- Error Message -->
                        <div th:if = "${param.error}" class = "error-message">
                            <span data-i18n = "login.error">Invalid username or password.</span>
                        </div>

                        <!-- Login Button -->
                        <button type = "submit" class = "login-button" data-i18n = "login.button">Login</button>
                    </form>
                </div>

            </div>

            <!-- Language Selector -->
            <div class = "language-selector">
                <div class = "language-toggle">
                    <img id = "current-flag" th:src = "@{/img/flags/16/China.png}" alt = "Language">
                    <span class = "current-language">中文</span>
                    <i class = "fa fa-chevron-down"></i>
                    <div class = "language-dropdown">
                        <button type = "button" class = "language-option set_zh active" data-lang = "zh">
                            <img th:src = "@{/img/flags/16/China.png}" alt = "中文"> 中文
                        </button>
                        <button type = "button" class = "language-option set_en" data-lang = "en">
                            <img th:src = "@{/img/flags/16/United-States.png}" alt = "EN"> English
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script th:src = "@{/js/jquery-2.1.1.js}"></script>
        <script th:src = "@{/js/plugins/cookie/jquery.cookie.js}"></script>
        <script th:src = "@{/js/bootstrap.min.js}"></script>
        <script th:src = "@{/js/plugins/i18next/i18next.min.js}"></script>

        <script>
            $(document).ready(function () {

                var currentLang = $.cookie('lang') || 'en';

                $.i18n.init({
                    resGetPath: '/locales/__lng__.json',
                    load: 'unspecific',
                    lng: currentLang,
                    fallbackLng: 'en',
                    cookieName: 'lang'
                });

                // Language selector functionality
                $('.language-option').click(function (e) {
                    e.preventDefault();
                    const lang = $(this).data('lang');
                    const langText = $(this).text().trim();
                    const flagSrc = $(this).find('img').attr('src');

                    // Update current language display
                    $('.current-language').text(langText);
                    $('#current-flag').attr('src', flagSrc);

                    // Update active state
                    $('.language-option').removeClass('active');
                    $(this).addClass('active');

                    // Close dropdown
                    $('.language-toggle').removeClass('active');

                    // Change language
                    i18n.setLng(lang, function () {
                        $('#login-head').i18n();
                        $('#login-body').i18n();
                    });
                });

                // Language dropdown toggle
                $('.language-toggle').click(function () {
                    $(this).toggleClass('active');
                });

                // Close dropdown when clicking outside
                $(document).click(function (e) {
                    if (!$(e.target).closest('.language-selector').length) {
                        $('.language-toggle').removeClass('active');
                    }
                });

                // Input focus effects
                $('.form-control').on('focus blur', function () {
                    $(this).closest('.input-wrapper').toggleClass('focused', $(this).is(':focus') || $(this).val().length > 0);
                });

                // Initialize input states
                $('.form-control').each(function () {
                    if ($(this).val().length > 0) {
                        $(this).closest('.input-wrapper').addClass('focused');
                    }
                });

                // Form validation
                $('form').submit(function (e) {
                    const username = $('#username').val().trim();
                    const password = $('#password').val().trim();

                    if (!username || !password) {
                        e.preventDefault();
                        // Show error message
                        if (!$('.error-message').length) {
                            $('<div class="error-message"><span data-i18n="login.validation.required">Please enter both username and password.</span></div>')
                            .insertBefore('.login-button').i18n();
                        }
                    }
                });

                // Language-specific handlers for backward compatibility
                $('.set_en').on('click', function () {
                    updateLanguage('en', 'English', '/img/flags/16/United-States.png');
                });

                $('.set_zh').on('click', function () {
                    updateLanguage('zh', '中文', '/img/flags/16/China.png');
                });

                // Helper function to update language
                function updateLanguage(lang, langText, flagSrc) {

                    i18n.setLng(lang, function () {
                        $('#login-head').i18n();
                        $('#login-body').i18n();

                        // Update UI
                        $('.current-language').text(langText);
                        $('#current-flag').attr('src', flagSrc);

                        // Update active states
                        $('.language-option').removeClass('active');
                        $('.set_' + lang).addClass('active');

                        // Close dropdown
                        $('.language-toggle').removeClass('active');
                    });
                }

                $(window).on('load', function () {
                    const currentLang = i18n.lng() || 'en';
                    if (currentLang === 'en') {
                        updateLanguage('en', 'English', '/img/flags/16/United-States.png');
                    } else {
                        updateLanguage('zh', '中文', '/img/flags/16/China.png');
                    }
                });
            });
        </script>

    </body>

</html>
