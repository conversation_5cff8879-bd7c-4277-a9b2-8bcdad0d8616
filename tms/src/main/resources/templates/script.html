<!-- Mainly scripts -->
<script th:src = "@{/js/jquery-2.1.1.js}"></script>
<script th:src = "@{/js/bootstrap.min.js}"></script>
<script th:src = "@{/js/plugins/metisMenu/jquery.metisMenu.js}"></script>
<script th:src = "@{/js/plugins/slimscroll/jquery.slimscroll.min.js}"></script>

<!-- Custom and plugin javascript -->
<script th:src = "@{/js/inspinia.js}"></script>
<script th:src = "@{/js/plugins/pace/pace.min.js}"></script>

<!-- Flot -->
<script th:src = "@{/js/plugins/flot/jquery.flot.js}"></script>
<script th:src = "@{/js/plugins/flot/jquery.flot.tooltip.min.js}"></script>
<script th:src = "@{/js/plugins/flot/jquery.flot.spline.js}"></script>
<script th:src = "@{/js/plugins/flot/jquery.flot.resize.js}"></script>
<script th:src = "@{/js/plugins/flot/jquery.flot.pie.js}"></script>

<!-- Peity -->
<script th:src = "@{/js/plugins/peity/jquery.peity.min.js}"></script>
<script th:src = "@{/js/demo/peity-demo.js}"></script>

<!-- jQuery UI -->
<script th:src = "@{/js/plugins/jquery-ui/jquery-ui.min.js}"></script>

<!-- GITTER -->
<script th:src = "@{/js/plugins/gritter/jquery.gritter.min.js}"></script>

<!-- Sparkline -->
<script th:src = "@{/js/plugins/sparkline/jquery.sparkline.min.js}"></script>

<!-- Sparkline demo data  -->
<script th:src = "@{/js/demo/sparkline-demo.js}"></script>

<!-- ChartJS-->
<script th:src = "@{/js/plugins/chartJs/Chart.min.js}"></script>

<!-- Toastr -->
<script th:src = "@{/js/plugins/toastr/toastr.min.js}"></script>

<!-- jqGrid -->
<script th:src = "@{/js/plugins/jqGrid/i18n/grid.locale-cn.js}"></script>
<script th:src = "@{/js/plugins/jqGrid/jquery.jqGrid.min.js}"></script>

<!-- dataTables -->
<script th:src = "@{/js/plugins/dataTables/datatables.min.js}"></script>
<script th:src = "@{/js/plugins/dataTables/dataTables.editor.min.js}"></script>

<!-- Sweet alert -->
<script th:src = "@{/js/plugins/sweetalert/sweetalert.min.js}"></script>

<!-- Dual Listbox -->
<script th:src = "@{/js/plugins/dualListbox/jquery.bootstrap-duallistbox.js}"></script>

<!-- jsTree-->
<script th:src = "@{/js/plugins/jsTree/jstree.min.js}"></script>

<!-- jQuery Cookie-->
<script th:src = "@{/js/plugins/cookie/jquery.cookie.js}"></script>

<!-- moment -->
<script th:src = "@{/js/plugins/moment/moment.min.js}"></script>

<!-- i18next -->
<script th:src = "@{/js/plugins/i18next/i18next.min.js}"></script>

<!-- Jquery Validate -->
<script th:src = "@{/js/plugins/validate/jquery.validate.min.js}"></script>

<!-- Data picker -->
<script th:src = "@{/js/plugins/datapicker/jquery.datetimepicker.js}"></script>

<!--ViewerJS-->
<script th:src = "@{/js/plugins/viewer/viewer.min.js}"></script>
<!--webuploader-->
<script th:src = "@{/js/webuploader/webuploader.js}"></script>

<!-- i18n Initialization -->
<script>

    var i18nLoad = new Promise(function (resolve, reject) {
        $(document).ready(function () {

            var currentLang = $.cookie('lang') || 'en';

            $.i18n.init({
                resGetPath: '/locales/__lng__.json',
                load: 'unspecific',
                lng: currentLang,
                fallbackLng: 'en',
                cookieName: 'lang'
            }, function (t) {

                $('head').i18n();
                $('#wrapper').i18n();
                $('#side-menu').i18n();
                $('.navbar-top-links').i18n();
                $('[data-i18n]').i18n();

                resolve(t);
            });
        });
    });

</script>

<!--Plunex Top Navbar-->
<script th:src = "@{/js/plunex/top-navbar.js}"></script>

<!--Plunex Navigation Sidebar-->
<script th:src = "@{/js/plunex/nav-sidebar.js}"></script>

<!--Plunex I18n Manager-->
<script th:src = "@{/js/plunex/i18n-manager.js}"></script>