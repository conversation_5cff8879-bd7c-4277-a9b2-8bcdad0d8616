<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="register.title">商户注册管理</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">x
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="register.title">商户注册管理</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="register.mermgr">商户管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="register.title">商户注册管理</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="searchUserId"
                                                data-i18n="register.userId">用户ID</label>
                                            <input name="userId" id="searchUserId" class="form-control" value="" />
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="register.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()" data-i18n="register.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="registerTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="register.userId">用户ID</th>
                                                <th data-i18n="register.mblNo">手机号码</th>
                                                <th data-i18n="register.usrNm">用户姓名</th>
                                                <th data-i18n="register.usrSts.title">用户状态</th>
                                                <th data-i18n="register.usrLvl.title">用户级别</th>
                                                <th data-i18n="register.crtTm">创建时间</th>
                                                <th data-i18n="register.opt">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1" role="dialog" aria-labelledby="registerModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">

                <div class="modal-body">
                    <form id="registerForm" class="form-horizontal">
                        <input type="hidden" id="userId" name="userId" />

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="userIdInput"
                                data-i18n="register.userId">用户ID</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="userIdInput" name="userId" required />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="mblNo" data-i18n="register.mblNo">手机号码</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="mblNo" name="mblNo" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="usrNm" data-i18n="register.usrNm">用户姓名</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="usrNm" name="usrNm" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="usrSts"
                                data-i18n="register.usrSts.title">用户状态</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="usrSts" name="usrSts">
                                    <option value="0" data-i18n="urm.info.usrSts.0">开户</option>
                                    <option value="1" data-i18n="urm.info.usrSts.1">销户</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="usrLvl"
                                data-i18n="register.usrLvl.title">用户级别</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="usrLvl" name="usrLvl">
                                    <option value="0" data-i18n="register.usrLvl.normal">普通用户</option>
                                    <option value="1" data-i18n="register.usrLvl.enterprise">企业用户</option>
                                    <option value="2" data-i18n="register.usrLvl.personal">个人商家</option>
                                    <option value="3" data-i18n="register.usrLvl.enterpriseMerchant">企业商家</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="idType"
                                data-i18n="register.idType.title">证件类型</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="idType" name="idType" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="idNo"
                                data-i18n="register.idNo.title">证件号码</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="idNo" name="idNo" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="usrGender"
                                data-i18n="register.usrGender.title">用户性别</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="usrGender" name="usrGender">
                                    <option value="" data-i18n="register.usrGender.pleaseSelect">请选择</option>
                                    <option value="M" data-i18n="register.usrGender.male">男</option>
                                    <option value="F" data-i18n="register.usrGender.female">女</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="usrNation"
                                data-i18n="register.usrNation.title">用户归属国家</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="usrNation" name="usrNation" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="usrBirthDt"
                                data-i18n="register.usrBirthDt">出生日期</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="usrBirthDt" name="usrBirthDt" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="usrRegCnl"
                                data-i18n="register.usrRegCnl.title">注册渠道</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="usrRegCnl" name="usrRegCnl" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="usrRegIp"
                                data-i18n="register.usrRegIp">注册IP</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="usrRegIp" name="usrRegIp" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="register.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="register.save">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="register.detailTitle">用户注册详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.userId">用户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUserId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.mblNo">手机号码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailMblNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.usrNm">用户姓名</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUsrNm"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.usrSts.title">用户状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUsrSts"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.usrLvl.title">用户级别</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUsrLvl"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.idChkFlg.title">实名标志</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailIdChkFlg"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.idType.title">证件类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailIdType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.idNo.title">证件号码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailIdNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.usrGender.title">用户性别</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUsrGender"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.usrNation.title">用户归属国家</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUsrNation"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.usrBirthDt">出生日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUsrBirthDt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.usrRegCnl.title">注册渠道</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUsrRegCnl"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.usrRegIp">注册IP</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUsrRegIp"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.createTime">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="register.modifyTime">更新时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailModifyTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="register.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            i18nLoad.then(function () {
                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/js/Chinese.json';
                        break;
                    case 'en':
                        languageUrl = '/js/English.json';
                        break;
                    default:
                        languageUrl = '/js/Chinese.json';
                }

                // 初始化DataTables
                table = $('#registerTable').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/urm/mermgr/register/findAll',
                        type: 'post',
                        data: function (d) {
                            // 添加额外的查询参数
                            d.userId = $("#searchUserId").val() || "";
                            return JSON.stringify(d);
                        },
                        dataSrc: function (json) {
                            // 确保返回的是数组
                            return json.data || [];
                        },
                        error: function (xhr, error, thrown) {
                            console.error('DataTables AJAX error:', error, thrown);
                            toastr.error('加载数据失败，请刷新页面重试');
                        }
                    },
                    serverSide: true,
                    searching: false, // 禁用内置搜索
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    columns: [
                        { data: 'userId' },
                        { data: 'mblNo' },
                        { data: 'usrNm' },
                        {
                            data: 'usrSts',
                            render: function (data) {
                                switch (data) {
                                    case '0': return '开户';
                                    case '1': return '销户';
                                    default: return data;
                                }
                            }
                        },
                        {
                            data: 'usrLvl',
                            render: function (data) {
                                switch (data) {
                                    case '0': return '普通用户';
                                    case '1': return '企业用户';
                                    case '2': return '个人商家';
                                    case '3': return '企业商家';
                                    default: return data;
                                }
                            }
                        },
                        {
                            data: 'createTime',
                            render: function (data) {
                                if (!data) return '';
                                return new Date(data).toLocaleString();
                            }
                        },
                        {
                            data: null,
                            orderable: false,
                            render: function (data, type, row) {
                                var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.userId + '" data-i18n="register.detail">详情</button>';
                                return buttons;
                            }
                        }
                    ],
                    buttons: [
                        { extend: 'copyHtml5' },
                        { extend: 'csvHtml5' },
                        { extend: 'excelHtml5', title: '用户注册列表' }
                    ]
                });

                // 新增按钮
                $("#btnAdd").click(function () {
                    $("#registerModalLabel").text("新增用户注册");
                    $("#registerForm")[0].reset();
                    $("#userId").val("");
                    $("#userIdInput").prop("disabled", false);
                    $("#registerModal").modal("show");
                });

                // 保存按钮
                $("#btnSave").click(function () {
                    if (!$("#registerForm").valid()) {
                        toastr.error('请填写必填项');
                        return;
                    }

                    var userId = $("#userId").val();
                    var formData = $("#registerForm").serialize();
                    var url = userId ? "/urm/mermgr/register/modify/" + userId : "/urm/mermgr/register/add";

                    $.ajax({
                        url: url,
                        type: "POST",
                        data: formData,
                        success: function (res) {
                            if (res.result === "SUCCESS") {
                                toastr.success('操作成功');
                                $("#registerModal").modal("hide");
                                table.ajax.reload();
                            } else {
                                toastr.error('操作失败：' + res.result);
                            }
                        },
                        error: function (xhr, status, error) {
                            toastr.error('操作失败：' + error);
                        }
                    });
                });

                // 查看详情
                $(document).on("click", ".view-detail", function () {
                    var userId = $(this).data("id");
                    $.ajax({
                        url: "/urm/mermgr/register/getUser",
                        type: "POST",
                        data: { userId: userId },
                        success: function (data) {
                            $("#detailUserId").text(data.userId || '');
                            $("#detailMblNo").text(data.mblNo || '');
                            $("#detailUsrNm").text(data.usrNm || '');
                            $("#detailUsrSts").text(data.usrSts === '0' ? '开户' : data.usrSts === '1' ? '销户' : data.usrSts);
                            $("#detailUsrLvl").text(data.usrLvl === '0' ? '普通用户' : data.usrLvl === '1' ? '企业用户' : data.usrLvl === '2' ? '个人商家' : data.usrLvl === '3' ? '企业商家' : data.usrLvl);
                            $("#detailIdChkFlg").text(data.idChkFlg === '0' ? '非实名' : data.idChkFlg === '1' ? '实名' : data.idChkFlg);
                            $("#detailIdType").text(data.idType || '');
                            $("#detailIdNo").text(data.idNo || '');
                            $("#detailUsrGender").text(data.usrGender === 'M' ? '男' : data.usrGender === 'F' ? '女' : data.usrGender);
                            $("#detailUsrNation").text(data.usrNation || '');
                            $("#detailUsrBirthDt").text(data.usrBirthDt || '');
                            $("#detailUsrRegCnl").text(data.usrRegCnl || '');
                            $("#detailUsrRegIp").text(data.usrRegIp || '');
                            $("#detailCreateTime").text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                            $("#detailModifyTime").text(data.modifyTime ? new Date(data.modifyTime).toLocaleString() : '');
                            $("#detailModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error('获取详情失败：' + error);
                        }
                    });
                });



                // 表单验证
                $("#registerForm").validate({
                    rules: {
                        userId: {
                            required: true,
                            maxlength: 16
                        },
                        mblNo: {
                            maxlength: 20
                        },
                        usrNm: {
                            maxlength: 64
                        }
                    },
                    messages: {
                        userId: {
                            required: "请输入用户ID",
                            maxlength: "用户ID不能超过16个字符"
                        },
                        mblNo: {
                            maxlength: "手机号码不能超过20个字符"
                        },
                        usrNm: {
                            maxlength: "用户姓名不能超过64个字符"
                        }
                    }
                });
            });
        });

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>