<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.bussub.mersub.amtInfo.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright {
            display: inline;
            float: right;
            padding-left: 20px;
            padding-right: 20px;
        }

        ._right {
            display: inline;
            float: right;
            padding-right: 15px;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.bussub.mersub.amtInfo.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.busmgr"></a>
                        </li>
                        <li>
                            <a data-i18n="nav.bussub.mermgr"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.bussub.mersub.amtInfo.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">

                                <div class="box-header">
                                    <table style="width: 80%">
                                        <tr>
                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.mermgr.mercId"></label>
                                            </td>
                                            <td>
                                                <input class="form-control" name="mercId" id="mercId" />
                                            </td>

                                            <td align="center">
                                                <label class="control-label" data-i18n="rsm.riskList.txTyp"></label>
                                            </td>
                                            <td>
                                                <select class="form-control" name="txType" id="txType">
                                                    <option value="" data-i18n="rsm.constant.all"></option>
                                                    <option value="01" data-i18n="rsm.constant.txTyp.recharge"></option>
                                                    <option value="02" data-i18n="rsm.constant.txTyp.consume"></option>
                                                    <option value="03" data-i18n="rsm.constant.txTyp.transfer"></option>
                                                    <option value="04" data-i18n="rsm.constant.txTyp.withdraw"></option>
                                                    <option value="05" data-i18n="rsm.constant.txTyp.seatel"></option>
                                                    <option value="06" data-i18n="rsm.constant.txTyp.refunds"></option>
                                                    <option value="07" data-i18n="rsm.constant.txTyp.interest"></option>
                                                    <option value="08" data-i18n="rsm.constant.txTyp.payment"></option>
                                                    <option value="09" data-i18n="rsm.constant.txTyp.revoke"></option>
                                                </select>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-w-m btn-primary _right"
                                                    data-i18n="urm.search" onclick="search()">
                                                </button>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <hr />

                                <div class="table-responsive">
                                    <table id="example" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="urm.merAmtinfo.orderNo"></th>
                                                <th data-i18n="urm.merAmtinfo.mercId"></th>
                                                <th data-i18n="urm.merAmtinfo.userId"></th>
                                                <th data-i18n="urm.merAmtinfo.txTm"></th>
                                                <th data-i18n="urm.merAmtinfo.txTyp"></th>
                                                <th data-i18n="urm.merAmtinfo.orderAmt"></th>
                                                <th data-i18n="urm.merAmtinfo.fee"></th>
                                                <th data-i18n="urm.merAmtinfo.couponAmt"></th>
                                                <th data-i18n="urm.merAmtinfo.couponType"></th>
                                                <!--<th data-i18n="urm.merAmtinfo.oprName"></th>-->
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <div th:replace="script"></div>
    <script>
        var table;
        var order = [1, 'asc'];
        <!--查询按钮-->
        function search() {
            table.ajax.reload();
        }

        $(document).ready(function () {

            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {

                table = $('#example').DataTable({
                    // dom: '<"floatright"l>rtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/urm/mermgr/amtinfo/findAll',
                        type: 'POST',
                        data: function (d) {
                            d.extra_search = {
                                "mercId": $("#mercId").val(),
                                "txType": $("#txType").val()
                            }
                            return JSON.stringify(d);
                        }
                    },
                    searching: true,
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    columns: [
                        {
                            data: 'orderNo'
                        }, {
                            data: 'mercId'
                        }, {
                            data: 'userId'
                        }, {
                            data: 'txTm',
                            type: "datetime",
                            def: function () {
                                return new Date();
                            },
                            format: "YYYY-MM-DD HH:mm:ss"
                        }, {
                            data: 'txTyp',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '00':
                                        return $.i18n.t("rsm.constant.txTyp.all");
                                    case '01':
                                        return $.i18n.t("rsm.constant.txTyp.recharge");
                                    case '02':
                                        return $.i18n.t("rsm.constant.txTyp.consume");
                                    case '03':
                                        return $.i18n.t("rsm.constant.txTyp.transfer");
                                    case '04':
                                        return $.i18n.t("rsm.constant.txTyp.withdraw");
                                    case '05':
                                        return $.i18n.t("rsm.constant.txTyp.seatel");
                                    case '06':
                                        return $.i18n.t("rsm.constant.txTyp.refunds");
                                    case '07':
                                        return $.i18n.t("rsm.constant.txTyp.interest");
                                    case '08':
                                        return $.i18n.t("rsm.constant.txTyp.payment");
                                    case '09':
                                        return $.i18n.t("rsm.constant.txTyp.revoke");
                                }
                            }
                        }, {
                            data: 'orderAmt'
                        }, {
                            data: 'fee'
                        }, {
                            data: 'couponAmt'
                        }, {
                            data: 'couponType',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '':
                                        return $.i18n.t("urm.merAmtinfo.null");
                                    case '00':
                                        return $.i18n.t("urm.merAmtinfo.null");
                                    case '01':
                                        return $.i18n.t("urm.merAmtinfo.coupon");
                                    case '02':
                                        return $.i18n.t("urm.merAmtinfo.seacoin");
                                    case '03':
                                        return $.i18n.t("urm.merAmtinfo.discount");
                                    default: return $.i18n.t("urm.merAmtinfo.null");
                                }
                            }
                        }
                        //                            , {
                        //                                data: 'oprName'
                        //                            }
                    ]
                });
            });

        });

        //     function searchButton() {
        //         var id = $("input[name='searchId']").val();
        //         var txTyp = $("select[name='searchTxTyp']").val();
        //         console.log(id);
        //         console.log(txTyp);
        //         table.column(1).search(id)
        //             .column(3).search(txTyp)
        //             .draw();
        //     }
    </script>
</body>

</html>