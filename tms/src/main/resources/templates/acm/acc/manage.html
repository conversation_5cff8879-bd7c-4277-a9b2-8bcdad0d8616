<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="accManage.page_title">Plunex | 法币开户审核</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="accManage.page_heading">法币开户审核</h2>
                    <ol class="breadcrumb">

                        <li>
                            <a data-i18n="accManage.breadcrumb_merchant">商户管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="accManage.breadcrumb_acc_audit">法币开户审核</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- <span data-i18n="accManage.comment_query_form">查询表单</span> -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="acNo"
                                                data-i18n="accManage.label_account_no">账户号</label>
                                            <input name="acNo" id="acNo" class="form-control" value="" />
                                        </div>
<!--                                        <div class="query-field">-->
<!--                                            <label class="control-label" for="userId"-->
<!--                                                data-i18n="accManage.label_user_id">用户ID</label>-->
<!--                                            <input name="userId" id="userId" class="form-control" value="" />-->
<!--                                        </div>-->
<!--                                        <div class="query-field">-->
<!--                                            <label class="control-label" for="ccy"-->
<!--                                                data-i18n="accManage.label_currency">币种</label>-->
<!--                                            <input name="ccy" id="ccy" class="form-control" value="" />-->
<!--                                        </div>-->
<!--&lt;!&ndash;                                        <div class="query-field">&ndash;&gt;-->
<!--                                            <label class="control-label" for="ccyType"-->
<!--                                                data-i18n="accManage.label_currency_type">币种类型</label>-->
<!--                                            <select name="ccyType" id="ccyType" class="form-control">-->
<!--                                                <option value="" data-i18n="accManage.option_all">全部</option>-->
<!--                                                <option value="FM" data-i18n="accManage.option_fiat">法币</option>-->
<!--                                                <option value="DM" data-i18n="accManage.option_digital">数币</option>-->
<!--                                            </select>-->
<!--                                        </div>-->
                                        <div class="query-field">
                                            <label class="control-label" for="acSts"
                                                data-i18n="accManage.label_account_status">账户状态</label>
                                            <select name="acSts" id="acSts" class="form-control">
                                                <option value="" data-i18n="accManage.option_all">全部</option>
                                                <option value="0" data-i18n="accManage.option_opened">开户</option>
                                                <option value="1" data-i18n="accManage.option_closed">销户</option>
                                                <option value="2" selected data-i18n="accManage.option_pending">待审核
                                                </option>
                                                <option value="3" data-i18n="accManage.option_rejected">审核不通过</option>
                                            </select>
                                        </div>
<!--                                        <div class="query-field">-->
<!--                                            <label class="control-label" for="bank"-->
<!--                                                data-i18n="accManage.label_bank">开户行/机构</label>-->
<!--                                            <input name="bank" id="bank" class="form-control" value="" />-->
<!--                                        </div>-->
                                        <div class="query-actions">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="accManage.btn_search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-white"
                                                onclick="resetForm()" data-i18n="accManage.btn_reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- <span data-i18n="accManage.comment_data_table">数据表格</span> -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="accountTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="accManage.th_account_no">账户号</th>
                                                <th data-i18n="accManage.th_user_id">用户ID</th>
                                                <th data-i18n="accManage.th_currency">币种</th>
                                                <th data-i18n="accManage.th_currency_type">币种类型</th>
                                                <th data-i18n="accManage.th_account_status">账户状态</th>
                                                <th data-i18n="accManage.th_bank">开户行/机构</th>
                                                <th data-i18n="accManage.th_account_create_date">账户创建日期</th>
                                                <th data-i18n="accManage.th_account_create_time">账户创建时间</th>
                                                <th data-i18n="accManage.th_account_close_date">账户销户日期</th>
                                                <th data-i18n="accManage.th_account_close_time">账户销户时间</th>
                                                <th data-i18n="accManage.th_create_time">创建时间</th>
                                                <th data-i18n="accManage.th_update_time">更新时间</th>
                                                <th data-i18n="accManage.th_operation">操作</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- <span data-i18n="accManage.comment_detail_modal">详情模态框</span> -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="accManage.modal_title_detail">账户详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accManage.detail_account_no">账户号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accManage.detail_user_id">用户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-userId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accManage.detail_currency">币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-ccy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="accManage.detail_currency_type">币种类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-ccyType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="accManage.detail_account_status">账户状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acSts"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accManage.detail_bank">开户行/机构</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-bank"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="accManage.detail_account_create_date">账户创建日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acCreDt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="accManage.detail_account_create_time">账户创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acCreTm"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="accManage.detail_account_close_date">账户销户日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acClsDt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="accManage.detail_account_close_time">账户销户时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acClsTm"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accManage.detail_create_time">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-createTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accManage.detail_update_time">更新时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-modifyTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="accManage.btn_close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- <span data-i18n="accManage.comment_audit_modal">审核模态框</span> -->
    <div class="modal fade" id="auditModal" tabindex="-1" role="dialog" aria-labelledby="auditModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="auditModalLabel" data-i18n="accManage.modal_title_audit">法币开户审核</h4>
                </div>
                <div class="modal-body">
                    <form id="auditForm" class="form-horizontal">
                        <input type="hidden" id="audit-acNo" name="acNo">

                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accManage.audit_result">审核结果</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="audit-result" name="checkResult">
                                    <option value="0" data-i18n="accManage.audit_pass">审核通过</option>
                                    <option value="3" data-i18n="accManage.audit_reject">审核拒绝</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accManage.audit_opinion">审核意见</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="audit-opinion" name="auditOpinion" rows="3"
                                    placeholder="请输入审核意见" data-i18n="accManage.placeholder_audit_opinion"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="accManage.btn_cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="submitAudit"
                        data-i18n="accManage.btn_submit">提交</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden elements for JavaScript i18n -->
    <div style="display: none;">
        <span data-i18n="accManage.currency_fiat">法币</span>
        <span data-i18n="accManage.currency_digital">数币</span>
        <span data-i18n="accManage.status_opened">开户</span>
        <span data-i18n="accManage.status_closed">销户</span>
        <span data-i18n="accManage.status_pending">待审核</span>
        <span data-i18n="accManage.status_rejected">审核不通过</span>
        <span data-i18n="accManage.btn_detail">详情</span>
        <span data-i18n="accManage.btn_audit">审核</span>
        <span data-i18n="accManage.warning_opinion_required">请输入审核意见</span>
        <span data-i18n="accManage.success_audit">审核成功</span>
        <span data-i18n="accManage.error_audit">审核失败</span>
        <span data-i18n="accManage.unknown_error">未知错误</span>
        <span data-i18n="accManage.error_data_load">数据加载失败</span>
        <span data-i18n="accManage.excel_title">法币开户审核列表</span>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            // 初始化draw值
            $('input[name="draw"]').remove();
            $('body').append('<input type="hidden" name="draw" value="1">');

            // 处理DataTables处理中指示器问题
            $(document).ajaxStop(function () {
                $('.dataTables_processing').hide();
            });

            // 初始化DataTables
            table = $('#accountTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    url: '/acm/acc/manage/findAll',
                    type: 'post',
                    data: function (d) {
                        // 添加额外的查询参数
                        var searchData = {
                            acNo: $('#acNo').val(),
                            userId: $('#userId').val(),
                            ccy: $('#ccy').val(),
                            ccyType: $('#ccyType').val(),
                            acSts: $('#acSts').val(),
                            bank: $('#bank').val()
                        };
                        // 将DataTables的参数和查询参数合并
                        return $.extend({}, d, searchData);
                    },
                    dataFilter: function (data) {
                        try {
                            var json = JSON.parse(data);
                            console.log("接收到服务器响应:", json);

                            // 检查返回的数据是否已经是DataTables格式
                            if (json.data && typeof json.recordsTotal !== 'undefined') {
                                return data;
                            }
                            // 如果不是，则手动封装
                            var returnData = {
                                draw: parseInt($('input[name="draw"]').val()) || 1,
                                recordsTotal: json.length,
                                recordsFiltered: json.length,
                                data: json
                            };
                            console.log("返回给DataTables的数据:", returnData);
                            return JSON.stringify(returnData);
                        } catch (e) {
                            console.error("解析服务器响应失败:", e);
                            return JSON.stringify({
                                draw: parseInt($('input[name="draw"]').val()) || 1,
                                recordsTotal: 0,
                                recordsFiltered: 0,
                                data: []
                            });
                        }
                    },
                    error: function (xhr, error, thrown) {
                        console.error('数据加载错误:', error);
                        var errorText = $('[data-i18n="accManage.error_data_load"]').text() || '数据加载失败';
                        toastr.error(errorText);
                        $('.dataTables_processing').hide();
                    },
                    complete: function () {
                        $('.dataTables_processing').hide();
                    }
                },
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl,
                    processing: "处理中...",
                    paginate: {
                        first: "首页",
                        last: "末页",
                        next: "下一页",
                        previous: "上一页"
                    },
                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                    infoFiltered: "(由 _MAX_ 项结果过滤)",
                    lengthMenu: "显示 _MENU_ 项结果"
                },
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                pagingType: "simple_numbers",
                drawCallback: function (settings) {
                    $('.dataTables_processing').hide();
                },
                columns: [
                    { data: 'acNo' },
                    { data: 'userId' },
                    { data: 'ccy' },
                    {
                        data: 'ccyType',
                        render: function (data) {
                            if (data === 'FM') return $('[data-i18n="accManage.currency_fiat"]').text() || '法币';
                            if (data === 'DM') return $('[data-i18n="accManage.currency_digital"]').text() || '数币';
                            return data || '';
                        }
                    },
                    {
                        data: 'acSts',
                        render: function (data) {
                            var openedText = $('[data-i18n="accManage.status_opened"]').text() || '开户';
                            var closedText = $('[data-i18n="accManage.status_closed"]').text() || '销户';
                            var pendingText = $('[data-i18n="accManage.status_pending"]').text() || '待审核';
                            var rejectedText = $('[data-i18n="accManage.status_rejected"]').text() || '审核不通过';
                            if (data === '0') return '<span class="label label-primary">' + openedText + '</span>';
                            if (data === '1') return '<span class="label label-default">' + closedText + '</span>';
                            if (data === '2') return '<span class="label label-warning">' + pendingText + '</span>';
                            if (data === '3') return '<span class="label label-danger">' + rejectedText + '</span>';
                            return data || '';
                        }
                    },
                    { data: 'bank' },
                    {
                        data: 'acCreDt',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleDateString();
                        }
                    },
                    {
                        data: 'acCreTm',
                        render: function (data) {
                            if (!data) return '';
                            return data;
                        }
                    },
                    {
                        data: 'acClsDt',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleDateString();
                        }
                    },
                    {
                        data: 'acClsTm',
                        render: function (data) {
                            if (!data) return '';
                            return data;
                        }
                    },
                    {
                        data: 'createTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: 'modifyTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var detailText = $('[data-i18n="accManage.btn_detail"]').text() || '详情';
                            var auditText = $('[data-i18n="accManage.btn_audit"]').text() || '审核';
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-row=\'' + JSON.stringify(row) + '\'>' + detailText + '</button> ';

                            // 只有待审核状态可以进行审核操作
                            if (row.acSts === '2') {
                                buttons += '<button type="button" class="btn btn-xs btn-warning audit-btn" data-acno="' + row.acNo + '">' + auditText + '</button>';
                            }

                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: $('[data-i18n="accManage.excel_title"]').text() || '法币开户审核列表' }
                ]
            });

            // 给表格添加绘制完成事件处理
            $('#accountTable').on('draw.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面长度变化事件监听
            $('#accountTable').on('length.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面变化事件监听
            $('#accountTable').on('page.dt', function () {
                setTimeout(function () {
                    $('.dataTables_processing').hide();
                }, 500);
            });

            // 查看详情
            $('#accountTable').on('click', '.view-detail', function () {
                var rowData = JSON.parse($(this).attr('data-row'));

                // 填充详情数据
                $('#detail-acNo').text(rowData.acNo || '');
                $('#detail-userId').text(rowData.userId || '');
                $('#detail-ccy').text(rowData.ccy || '');

                var ccyTypeText = rowData.ccyType || '';
                if (rowData.ccyType === 'FM') ccyTypeText = $('[data-i18n="accManage.currency_fiat"]').text() || '法币';
                else if (rowData.ccyType === 'DM') ccyTypeText = $('[data-i18n="accManage.currency_digital"]').text() || '数币';
                $('#detail-ccyType').text(ccyTypeText);

                var acStsText = rowData.acSts || '';
                if (rowData.acSts === '0') acStsText = $('[data-i18n="accManage.status_opened"]').text() || '开户';
                else if (rowData.acSts === '1') acStsText = $('[data-i18n="accManage.status_closed"]').text() || '销户';
                else if (rowData.acSts === '2') acStsText = $('[data-i18n="accManage.status_pending"]').text() || '待审核';
                else if (rowData.acSts === '3') acStsText = $('[data-i18n="accManage.status_rejected"]').text() || '审核不通过';
                $('#detail-acSts').text(acStsText);

                $('#detail-bank').text(rowData.bank || '');
                $('#detail-acCreDt').text(rowData.acCreDt ? new Date(rowData.acCreDt).toLocaleDateString() : '');
                $('#detail-acCreTm').text(rowData.acCreTm || '');
                $('#detail-acClsDt').text(rowData.acClsDt ? new Date(rowData.acClsDt).toLocaleDateString() : '');
                $('#detail-acClsTm').text(rowData.acClsTm || '');
                $('#detail-createTime').text(rowData.createTime ? new Date(rowData.createTime).toLocaleString() : '');
                $('#detail-modifyTime').text(rowData.modifyTime ? new Date(rowData.modifyTime).toLocaleString() : '');

                $('#detailModal').modal('show');
            });

            // 审核操作
            $('#accountTable').on('click', '.audit-btn', function () {
                var acNo = $(this).data('acno');
                $('#audit-acNo').val(acNo);
                $('#audit-result').val('0'); // 默认通过
                $('#audit-opinion').val('');
                $('#auditModal').modal('show');
            });

            // 提交审核
            $('#submitAudit').click(function () {
                var acNo = $('#audit-acNo').val();
                var checkResult = $('#audit-result').val();
                var auditOpinion = $('#audit-opinion').val();

                if (!auditOpinion.trim()) {
                    var warningText = $('[data-i18n="accManage.warning_opinion_required"]').text() || '请输入审核意见';
                    toastr.warning(warningText);
                    return;
                }

                $.ajax({
                    url: '/acm/acc/manage/check',
                    type: 'post',
                    data: {
                        acNo: acNo,
                        checkResult: checkResult,
                        auditOpinion: auditOpinion
                    },
                    success: function (response) {
                        $('#auditModal').modal('hide');
                        if (response && response.msgCd === '1') {
                            var successText = $('[data-i18n="accManage.success_audit"]').text() || '审核成功';
                            toastr.success(successText);
                        } else {
                            var errorText = $('[data-i18n="accManage.error_audit"]').text() || '审核失败';
                            var unknownErrorText = $('[data-i18n="accManage.unknown_error"]').text() || '未知错误';
                            toastr.error(errorText + ': ' + (response.msgInfo || unknownErrorText));
                        }
                        // 刷新表格
                        table.ajax.reload(function () {
                            $('.dataTables_processing').hide();
                        }, false);
                    },
                    error: function (xhr, status, error) {
                        var errorText = $('[data-i18n="accManage.error_audit"]').text() || '审核失败';
                        toastr.error(errorText + ': ' + error);
                    }
                });
            });
        });

        // 查询函数
        function search() {
            table.ajax.reload();
        }

        // 重置表单函数
        function resetForm() {
            $('#queryForm')[0].reset();
            // 重新设置默认选中的待审核状态
            $('#acSts').val('2');
            table.ajax.reload();
        }
    </script>
</body>

</html>