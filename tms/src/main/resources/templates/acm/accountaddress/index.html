<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="accountaddress.title"></title>
    <div th:replace="head"></div>
    <!-- add required JavaScript dependencies -->
    <script src="/js/plugins/validate/jquery.validate.min.js"></script>
    <script src="/js/plugins/validate/messages_zh.min.js"></script>
    <script src="/js/plugins/toastr/toastr.min.js"></script>
    <link href="/css/plugins/toastr/toastr.min.css" rel="stylesheet">
    <style>
        /* status label style enhancement */
        .label {
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 3px;
            font-weight: 500;
        }

        .label i {
            margin-right: 3px;
        }

        .label-success {
            background-color: #5cb85c;
            border: 1px solid #4cae4c;
        }

        .label-warning {
            background-color: #f0ad4e;
            border: 1px solid #eea236;
        }

        .label-default {
            background-color: #777;
            border: 1px solid #666;
        }

        .label-info {
            background-color: #5bc0de;
            border: 1px solid #46b8da;
        }

        .label-primary {
            background-color: #337ab7;
            border: 1px solid #2e6da4;
        }

        /* timestamp style enhancement */
        .timestamp-display {
            color: #666;
            font-size: 12px;
        }

        .timestamp-display i {
            margin-right: 4px;
            color: #999;
        }

        /* status and time display in detail modal */
        .form-control-static .label {
            font-size: 12px;
            padding: 5px 10px;
        }

        .form-control-static .timestamp-display {
            font-size: 13px;
        }

        /* center align status column in table */
        #accountAddressTable tbody td:nth-child(7) {
            text-align: center;
        }

        /* tooltip style optimization */
        .tooltip-inner {
            max-width: 300px;
            text-align: left;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="accountaddress.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="accManage.breadcrumb_merchant">商户管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="accountaddress.content"></strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">

                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- search form -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="searchVaultCode"
                                                data-i18n="accountaddress.vaultCode"></label>
                                            <input name="vaultCode" id="searchVaultCode" class="form-control"
                                                value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchAddress"
                                                data-i18n="accountaddress.address"></label>
                                            <input name="address" id="searchAddress" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchNetwork"
                                                data-i18n="accountaddress.network"></label>
                                            <input name="network" id="searchNetwork" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="searchStatus"
                                                data-i18n="accountaddress.status"></label>
                                            <select name="status" id="searchStatus" class="form-control">
                                                <option value="" data-i18n="accountaddress.allStatus"></option>
                                                <option value="ENABLED" data-i18n="accountaddress.enabled"></option>
                                                <option value="DISABLED" data-i18n="accountaddress.disabled"></option>
                                                <option value="FROZEN" data-i18n="accountaddress.frozen"></option>
                                            </select>
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary" id="btnAdd">
                                               <span data-i18n="accountaddress.add"></span>
                                            </button>
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                data-i18n="accountaddress.search"></button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                data-i18n="accountaddress.reset"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- data table -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="accountAddressTable"
                                        class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="accountaddress.id"></th>
                                                <th data-i18n="accountaddress.vaultCode"></th>
                                                <th data-i18n="accountaddress.groupCode"></th>
                                                <th data-i18n="accountaddress.accountId"></th>
                                                <th data-i18n="accountaddress.address"></th>
                                                <th data-i18n="accountaddress.network"></th>
                                                <th data-i18n="accountaddress.status"></th>
                                                <th data-i18n="accountaddress.createTime"></th>
                                                <th data-i18n="accountaddress.operations"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- add modal -->
    <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="addModalLabel" data-i18n="accountaddress.addTitle"></h4>
                </div>
                <div class="modal-body">
                    <form id="addForm" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="vaultCode"
                                data-i18n="accountaddress.vaultCode"></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="vaultCode" name="vaultCode" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="groupCode"
                                data-i18n="accountaddress.groupCode"></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="groupCode" name="groupCode" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="accountId"
                                data-i18n="accountaddress.accountId"></label>
                            <div class="col-sm-9">
                                <input type="number" class="form-control" id="accountId" name="accountId" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="address"
                                data-i18n="accountaddress.address"></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="address" name="address" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="network"
                                data-i18n="accountaddress.network"></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="network" name="network" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="status"
                                data-i18n="accountaddress.status"></label>
                            <div class="col-sm-9">
                                <select class="form-control" id="status" name="status" required>
                                    <option value="ENABLED" data-i18n="accountaddress.enabled"></option>
                                    <option value="DISABLED" selected data-i18n="accountaddress.disabled"></option>
                                    <option value="FROZEN" data-i18n="accountaddress.frozen"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="acmAcNo"
                                data-i18n="accountaddress.acmAcNo"></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="acmAcNo" name="acmAcNo" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="userId"
                                data-i18n="accountaddress.userId"></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="userId" name="userId" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="useType"
                                data-i18n="accountaddress.useType"></label>
                            <div class="col-sm-9">
                                <select class="form-control" id="useType" name="useType">
                                    <option value="" data-i18n="accountaddress.selectUseType"></option>
                                    <option value="DS" data-i18n="accountaddress.useTypeDS"></option>
                                    <option value="DC" data-i18n="accountaddress.useTypeDC"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="qrcodeBase64"
                                data-i18n="accountaddress.qrcodeBase64"></label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="qrcodeBase64" name="qrcodeBase64"
                                    rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="accountaddress.cancel"></button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="accountaddress.save"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- detail modal -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="accountaddress.detailTitle"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.id"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.vaultCode"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailVaultCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.groupCode"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailGroupCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.accountId"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAccountId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.address"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAddress"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.network"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailNetwork"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.status"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailStatus"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.acmAcNo"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAcmAcNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.userId"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUserId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.useType"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUseType"></p>
                            </div>
                        </div>
                        <div class="form-group" id="qrcodeGroup" style="display: none;">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.qrcode"></label>
                            <div class="col-sm-9">
                                <img id="detailQrcode" class="img-responsive" style="max-width: 200px;" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.createTime"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="accountaddress.updateTime"></label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdateTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="accountaddress.close"></button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            i18nLoad.then(function () {
                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/js/Chinese.json';
                        break;
                    case 'en':
                        languageUrl = '/js/English.json';
                        break;
                    default:
                        languageUrl = '/js/Chinese.json';
                }

                // init DataTables
                table = $('#accountAddressTable').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/acm/accountaddress/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extraSearch = {
                                "vaultCode": $("#searchVaultCode").val() || "",
                                "address": $("#searchAddress").val() || "",
                                "network": $("#searchNetwork").val() || "",
                                "status": $("#searchStatus").val() || ""
                            };
                            return JSON.stringify(d);
                        },
                        dataSrc: function (json) {
                            return json.data || [];
                        },
                        error: function (xhr, error, thrown) {
                            console.error('DataTables AJAX error:', error, thrown);
                            toastr.error(i18n.t('accountaddress.loadDataError'));
                        }
                    },
                    serverSide: true,
                    searching: false,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    columns: [
                        { data: 'id' },
                        { data: 'vaultCode' },
                        { data: 'groupCode' },
                        { data: 'accountId' },
                        { data: 'address' },
                        { data: 'network' },
                        {
                            data: 'status',
                            render: function (data) {
                                var label = '';
                                switch (data) {
                                    case 'ENABLED':
                                        label = '<span class="label label-success">' + i18n.t('accountaddress.enabled') + '</span>';
                                        break;
                                    case 'DISABLED':
                                        label = '<span class="label label-default">' + i18n.t('accountaddress.disabled') + '</span>';
                                        break;
                                    case 'FROZEN':
                                        label = '<span class="label label-warning">' + i18n.t('accountaddress.frozen') + '</span>';
                                        break;
                                    default:
                                        label = '<span class="label label-default">' + data + '</span>';
                                }
                                return label;
                            }
                        },
                        {
                            data: 'createTime',
                            render: function (data) {
                                return data ? new Date(data).toLocaleString() : '';
                            }
                        },
                        {
                            data: null,
                            orderable: false,
                            render: function (data, type, row) {
                                var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">' + i18n.t('accountaddress.detail') + '</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.id + '">' + i18n.t('accountaddress.edit') + '</button> ';
                                buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.id + '">' + i18n.t('accountaddress.delete') + '</button>';
                                return buttons;
                            }
                        }
                    ],
                    buttons: [
                        { extend: 'copyHtml5' },
                        { extend: 'csvHtml5' },
                        { extend: 'excelHtml5', title: i18n.t('accountaddress.listTitle') }
                    ]
                });

                // add button
                $("#btnAdd").click(function () {
                    $("#addModalLabel").text(i18n.t('accountaddress.addTitle'));
                    $("#addForm")[0].reset();
                    $("#addModal").modal("show");
                });

                // save button
                $("#btnSave").click(function () {
                    if (!$("#addForm").valid()) {
                        toastr.error(i18n.t('accountaddress.requiredError'));
                        return;
                    }

                    var formData = $("#addForm").serialize();
                    $.ajax({
                        url: "/acm/accountaddress/add",
                        type: "POST",
                        data: formData,
                        success: function (res) {
                            if (res.result === "ACM00000") {
                                toastr.success(i18n.t('accountaddress.opSuccess'));
                                $("#addModal").modal("hide");
                                table.ajax.reload();
                            } else {
                                var errorMsg = getErrorMessage(response && response.result ? response.result : 'ACM10011');
                                showErrorMessage(errorMsg);
                            }
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('accountaddress.opError') + error);
                        }
                    });
                });

                // view detail
                $(document).on("click", ".view-detail", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "/acm/accountaddress/getDetail",
                        type: "POST",
                        data: { id: id },
                        success: function (data) {
                            $("#detailId").text(data.id || '');
                            $("#detailVaultCode").text(data.vaultCode || '');
                            $("#detailGroupCode").text(data.groupCode || '');
                            $("#detailAccountId").text(data.accountId || '');
                            $("#detailAddress").text(data.address || '');
                            $("#detailNetwork").text(data.network || '');
                            $("#detailStatus").text(data.status || '');
                            $("#detailAcmAcNo").text(data.acmAcNo || '');
                            $("#detailUserId").text(data.userId || '');
                            $("#detailUseType").text(data.useType || '');
                            $("#detailModal").modal("show");
                        },
                        error: function (xhr, status, error) {
                            toastr.error(i18n.t('accountaddress.getDetailError') + error);
                        }
                    });
                });

                // form validation
                $("#addForm").validate({
                    rules: {
                        vaultCode: {
                            required: true,
                            maxlength: 50
                        },
                        groupCode: {
                            required: true,
                            maxlength: 50
                        },
                        accountId: {
                            required: true,
                            number: true
                        },
                        address: {
                            required: true,
                            maxlength: 100
                        },
                        network: {
                            maxlength: 50
                        },
                        status: {
                            required: true
                        }
                    },
                    messages: {
                        vaultCode: {
                            required: i18n.t('accountaddress.vaultCodeRequired'),
                            maxlength: i18n.t('accountaddress.vaultCodeMaxLength')
                        },
                        groupCode: {
                            required: i18n.t('accountaddress.groupCodeRequired'),
                            maxlength: i18n.t('accountaddress.groupCodeMaxLength')
                        },
                        accountId: {
                            required: i18n.t('accountaddress.accountIdRequired'),
                            number: i18n.t('accountaddress.accountIdNumber')
                        },
                        address: {
                            required: i18n.t('accountaddress.addressRequired'),
                            maxlength: i18n.t('accountaddress.addressMaxLength')
                        },
                        network: {
                            maxlength: i18n.t('accountaddress.networkMaxLength')
                        },
                        status: {
                            required: i18n.t('accountaddress.statusRequired')
                        }
                    }
                });

                // address validation
                $("#address").blur(function () {
                    var address = $(this).val();
                    if (address) {
                        $.ajax({
                            url: "/acm/accountaddress/validateAddress",
                            type: "POST",
                            data: { address: address },
                            success: function (res) {
                                if (!res.valid) {
                                    toastr.warning(i18n.t('accountaddress.addressExists'));
                                }
                            }
                        });
                    }
                });
            });
        });

        // search method
        function search() {
            table.ajax.reload();
        }

        // reset form
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }

        // bind search and reset button events
        $("#searchBtn").click(function () {
            search();
        });

        $("#resetBtn").click(function () {
            resetForm();
        });
    </script>
</body>

</html>