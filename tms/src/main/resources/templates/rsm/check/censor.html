<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="rsm.check.censor.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright {
            display: inline;
            float: right;
            padding-left: 20px;
            padding-right: 20px;
        }

        ._right {
            display: inline;
            float: right;
            padding-right: 15px;
        }

        .dt-buttons {
            display: none;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="rsm.check.censor.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.rsm"></a>
                        </li>

                        <li class="active">
                            <strong data-i18n="rsm.check.censor.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">

                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.riskList.txTyp"></label>
                                            <select class="form-control" name="searchTxTyp">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="00" data-i18n="rsm.constant.txTyp.all"></option>
                                                <option value="01" data-i18n="rsm.constant.txTyp.recharge"></option>
                                                <option value="02" data-i18n="rsm.constant.txTyp.consume"></option>
                                                <option value="03" data-i18n="rsm.constant.txTyp.transfer"></option>
                                                <option value="04" data-i18n="rsm.constant.txTyp.withdraw"></option>
                                                <option value="05" data-i18n="rsm.constant.txTyp.seatel"></option>
                                                <option value="07" data-i18n="rsm.constant.txTyp.interest"></option>
                                                <option value="08" data-i18n="rsm.constant.txTyp.payment"></option>
                                            </select>
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary"
                                                onclick="$('.dt-button:contains(' + $.i18n.t('rsm.add') + ')').click()"
                                                data-i18n="rsm.add"></button>
                                            <button type="button" class="btn btn-primary"
                                                onclick="$('.dt-button:contains(' + $.i18n.t('rsm.edit') + ')').click()"
                                                data-i18n="rsm.edit"></button>
                                            <button type="button" class="btn btn-danger"
                                                onclick="$('.dt-button.buttons-remove').click()"
                                                data-i18n="rsm.delete"></button>
                                            <button type="button" class="btn btn-primary" data-i18n="rsm.search"
                                                onclick="searchButton()"></button>
                                        </div>
                                    </div>
                                </form>
                                <div class="clearfix"></div>
                                <hr />

                                <div class="table-responsive">
                                    <table id="example" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="rsm.check.censor.checkId"></th>
                                                <th data-i18n="rsm.check.censor.txTyp"></th>
                                                <th data-i18n="rsm.check.censor.lmtLvl"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <div th:replace="script"></div>
    <script>
        var table;
        var editor;
        var order = [1, 'asc'];
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        remove: {
                            type: 'POST',
                            url: '/rsm/check/censor/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#example",
                    idSrc: 'checkId',
                    i18n: {
                        remove: {
                            button: $.i18n.t("rsm.delete"), title: $.i18n.t("rsm.delete"), submit: $.i18n.t("rsm.delete"),
                            confirm: {
                                _: $.i18n.t("rsm.multi-delete"),
                                1: $.i18n.t("rsm.single-delete")
                            }
                        }
                    }
                });

                var table = $('#example').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/rsm/check/censor/findAll',
                        type: 'POST',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        {
                            text: $.i18n.t("rsm.add"),
                            action: function (e, dt, node, config) {
                                window.location.href = "/rsm/check/censor/add"
                            }
                        }, {
                            text: $.i18n.t("rsm.edit"),
                            action: function (e, dt, node, config) {
                                var rowData = table.row('.selected').data();
                                window.location.href = "/rsm/check/censor/add?checkId="
                                    + rowData["checkId"] + "&txTyp=" + rowData["txTyp"] + "&lmtLvl=" + rowData["lmtLvl"];
                            }
                        }, {
                            extend: "remove", editor: editor
                        }
                    ],
                    columns: [
                        {
                            data: 'checkId',
                            visible: false
                        }, {
                            data: 'txTyp',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '00':
                                        return $.i18n.t("rsm.constant.txTyp.all");
                                    case '01':
                                        return $.i18n.t("rsm.constant.txTyp.recharge");
                                    case '02':
                                        return $.i18n.t("rsm.constant.txTyp.consume");
                                    case '03':
                                        return $.i18n.t("rsm.constant.txTyp.transfer");
                                    case '04':
                                        return $.i18n.t("rsm.constant.txTyp.withdraw");
                                    case '05':
                                        return $.i18n.t("rsm.constant.txTyp.seatel");
                                    case '06':
                                        return $.i18n.t("rsm.constant.txTyp.refunds");
                                    case '07':
                                        return $.i18n.t("rsm.constant.txTyp.interest");
                                    case '08':
                                        return $.i18n.t("rsm.constant.txTyp.payment");
                                }
                            }
                        }, {
                            data: 'lmtLvl',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '1':
                                        return $.i18n.t("rsm.constant.lmtLvl.user");
                                }
                            }
                        }, {
                            data: 'ruleDesc',
                            visible: false
                        }
                    ],
                    initComplete: function () {
                        table.button(1).disable();
                    }
                });

                $('#example').on('select.dt deselect.dt', function () {
                    table.buttons(1).enable(table.rows({ selected: true }).indexes().length === 0 ? false : true)
                })

            });

        });

        function searchButton() {
            var txTyp = $("select[name='searchTxTyp']").val();
            console.log(txTyp);

            table.column(0).search(txTyp)
                .draw();
        }
    </script>
</body>

</html>