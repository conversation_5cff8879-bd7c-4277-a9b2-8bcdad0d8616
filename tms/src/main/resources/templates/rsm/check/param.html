<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="rsm.check.param.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright {
            display: inline;
            float: right;
            padding-left: 20px;
            padding-right: 20px;
        }

        ._right {
            display: inline;
            float: right;
            padding-right: 15px;
        }

        .dt-buttons {
            display: none;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="rsm.check.param.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.rsm"></a>
                        </li>

                        <li class="active">
                            <strong data-i18n="rsm.check.param.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">

                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.check.param.acTyp"></label>
                                            <select class="form-control" name="searchAcTyp">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="1" data-i18n="rsm.constant.acTyp.main"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.check.param.lmtLvl"></label>
                                            <select class="form-control" name="searchLmtLvl" id="searchLmtLvl">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.check.param.dcPtyFlg"></label>
                                            <select class="form-control" name="searchDcPtyFlg">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="1" data-i18n="rsm.constant.dcPtyFlg.stl"></option>
                                                <option value="2" data-i18n="rsm.constant.dcPtyFlg.pay"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.check.param.txTyp"></label>
                                            <select class="form-control" name="searchTxTyp">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="00" data-i18n="rsm.constant.txTyp.all"></option>
                                                <option value="01" data-i18n="rsm.constant.txTyp.recharge"></option>
                                                <option value="02" data-i18n="rsm.constant.txTyp.consume"></option>
                                                <option value="03" data-i18n="rsm.constant.txTyp.transfer"></option>
                                                <option value="04" data-i18n="rsm.constant.txTyp.withdraw"></option>
                                                <option value="05" data-i18n="rsm.constant.txTyp.seatel"></option>
                                                <option value="07" data-i18n="rsm.constant.txTyp.interest"></option>
                                                <option value="08" data-i18n="rsm.constant.txTyp.payment"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.check.param.payTyp"></label>
                                            <select class="form-control" name="searchPayTyp">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="00" data-i18n="rsm.constant.payTyp.all"></option>
                                                <option value="01" data-i18n="rsm.constant.payTyp.account"></option>
                                                <option value="02" data-i18n="rsm.constant.payTyp.quickPay"></option>
                                                <option value="03" data-i18n="rsm.constant.payTyp.seatel"></option>
                                                <option value="04" data-i18n="rsm.constant.payTyp.wechat"></option>
                                                <option value="05" data-i18n="rsm.constant.payTyp.alipay"></option>
                                                <option value="06" data-i18n="rsm.constant.payTyp.bestpay"></option>
                                                <option value="07" data-i18n="rsm.constant.payTyp.offline"></option>
                                            </select>
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary"
                                                onclick="$('.dt-button.buttons-create').click()"
                                                data-i18n="rsm.add"></button>
                                            <button type="button" class="btn btn-primary"
                                                onclick="$('.dt-button.buttons-edit').click()"
                                                data-i18n="rsm.edit"></button>
                                            <button type="button" class="btn btn-danger"
                                                onclick="$('.dt-button.buttons-remove').click()"
                                                data-i18n="rsm.delete"></button>
                                            <button type="button" class="btn btn-primary" data-i18n="rsm.search"
                                                onclick="searchButton()"></button>
                                        </div>
                                    </div>
                                </form>
                                <div class="clearfix"></div>
                                <hr />

                                <div class="table-responsive">
                                    <table id="example" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th data-i18n="rsm.check.param.acTyp"></th>
                                                <th data-i18n="rsm.check.param.lmtLvl"></th>
                                                <th data-i18n="rsm.check.param.dcPtyFlg"></th>
                                                <th data-i18n="rsm.check.param.txTyp"></th>
                                                <th data-i18n="rsm.check.param.payTyp"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <div th:replace="script"></div>
    <script>
        var table;
        var editor;
        var order = [1, 'asc'];

        var d = [];

        function getStateList() {
            return $.ajax({
                type: 'get',
                url: '/rsm/check/param/mercLvl',
                cache: false,
                dataType: 'json'
            });
        }

        function initSearchLmtLvl() {
            getStateList().then(function (data) {
                var map = eval(data);
                for (var key in map) {
                    $("#searchLmtLvl").append("<option value='" + map[key] + "'>" + key + "</option>");
                }
            });
        }

        $(document).ready(function () {

            initSearchLmtLvl();

            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {

                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/rsm/check/param/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/rsm/check/param/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/rsm/check/param/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#example",
                    idSrc: 'parmId',
                    fields: [
                        {
                            name: "parmId", type: "hidden"
                        }, {
                            label: $.i18n.t("rsm.check.param.acTyp"),
                            name: "acTyp",
                            type: "select",
                            options: [
                                { label: $.i18n.t("rsm.constant.acTyp.main"), value: "1" }
                            ]
                        }, {
                            label: $.i18n.t("rsm.check.param.lmtLvl"),
                            name: "lmtLvl",
                            dataProp: "1",
                            type: "select"
                        }, {
                            label: $.i18n.t("rsm.check.param.dcPtyFlg"),
                            name: "dcPtyFlg",
                            type: "select",
                            options: [
                                { label: $.i18n.t("rsm.constant.dcPtyFlg.stl"), value: "1" },
                                { label: $.i18n.t("rsm.constant.dcPtyFlg.pay"), value: "2" }
                            ]
                        }, {
                            label: $.i18n.t("rsm.check.param.txTyp"),
                            name: "txTyp",
                            type: "select",
                            options: [
                                { label: $.i18n.t("rsm.constant.txTyp.all"), value: "00" },
                                { label: $.i18n.t("rsm.constant.txTyp.recharge"), value: "01" },
                                { label: $.i18n.t("rsm.constant.txTyp.consume"), value: "02" },
                                { label: $.i18n.t("rsm.constant.txTyp.transfer"), value: "03" },
                                { label: $.i18n.t("rsm.constant.txTyp.withdraw"), value: "04" },
                                { label: $.i18n.t("rsm.constant.txTyp.seatel"), value: "05" },
                                { label: $.i18n.t("rsm.constant.txTyp.interest"), value: "07" },
                                { label: $.i18n.t("rsm.constant.txTyp.payment"), value: "08" }
                            ]
                        }, {
                            label: $.i18n.t("rsm.check.param.payTyp"),
                            name: "payTyp",
                            type: "select",
                            options: [
                                { label: $.i18n.t("rsm.constant.payTyp.all"), value: "00" },
                                { label: $.i18n.t("rsm.constant.payTyp.account"), value: "01" },
                                { label: $.i18n.t("rsm.constant.payTyp.quickPay"), value: "02" },
                                { label: $.i18n.t("rsm.constant.payTyp.seatel"), value: "03" },
                                { label: $.i18n.t("rsm.constant.payTyp.wechat"), value: "04" },
                                { label: $.i18n.t("rsm.constant.payTyp.alipay"), value: "05" },
                                { label: $.i18n.t("rsm.constant.payTyp.bestpay"), value: "06" },
                                { label: $.i18n.t("rsm.constant.payTyp.offline"), value: "07" }
                            ]
                        }, {
                            label: $.i18n.t("rsm.check.param.minAmtLmt"),
                            name: "minAmtLmt"
                        }, {
                            label: $.i18n.t("rsm.check.param.maxAmtLmt"),
                            name: "maxAmtLmt"
                        }, {
                            label: $.i18n.t("rsm.check.param.dlyAmtLmt"),
                            name: "dlyAmtLmt"
                        }, {
                            label: $.i18n.t("rsm.check.param.dlyCntLmt"),
                            name: "dlyCntLmt"
                        }, {
                            label: $.i18n.t("rsm.check.param.mlyAmtLmt"),
                            name: "mlyAmtLmt"
                        }, {
                            label: $.i18n.t("rsm.check.param.mlyCntLmt"),
                            name: "mlyCntLmt"
                        }
                    ],
                    i18n: {
                        create: { button: $.i18n.t("rsm.add"), title: $.i18n.t("rsm.add"), submit: $.i18n.t("rsm.create") },
                        edit: { button: $.i18n.t("rsm.edit"), title: $.i18n.t("rsm.edit"), submit: $.i18n.t("rsm.update") },
                        remove: {
                            button: $.i18n.t("rsm.delete"), title: $.i18n.t("rsm.delete"), submit: $.i18n.t("rsm.delete"),
                            confirm: {
                                _: $.i18n.t("rsm.multi-delete"),
                                1: $.i18n.t("rsm.single-delete")
                            }
                        }
                    }
                });

                editor.on('preSubmit', function (e, o, action) {
                    if (action !== 'remove') {
                        var minAmtLmt = this.field('minAmtLmt');
                        var maxAmtLmt = this.field('maxAmtLmt');
                        var dlyAmtLmt = this.field('dlyAmtLmt');
                        var dlyCntLmt = this.field('dlyCntLmt');
                        var mlyAmtLmt = this.field('mlyAmtLmt');
                        var mlyCntLmt = this.field('mlyCntLmt');
                        if (!minAmtLmt.isMultiValue()) {
                            if (!minAmtLmt.val()) {
                                minAmtLmt.error($.i18n.t("rsm.verify.notNull"));
                            }
                            if (parseInt(minAmtLmt.val()) > parseInt(maxAmtLmt.val())) {
                                minAmtLmt.error($.i18n.t("rsm.verify.sigMinGtMax"));
                            }
                        }
                        if (!maxAmtLmt.isMultiValue()) {
                            if (!maxAmtLmt.val()) {
                                maxAmtLmt.error($.i18n.t("rsm.verify.notNull"));
                            }
                            if (maxAmtLmt.val().length >= 14) {
                                maxAmtLmt.error($.i18n.t("rsm.verify.maxLen16"));
                            }
                        }
                        if (!dlyAmtLmt.isMultiValue()) {
                            if (!dlyAmtLmt.val()) {
                                dlyAmtLmt.error($.i18n.t("rsm.verify.notNull"));
                            }
                            if (dlyAmtLmt.val().length >= 14) {
                                dlyAmtLmt.error($.i18n.t("rsm.verify.maxLen16"));
                            }
                            if (parseInt(dlyAmtLmt.val()) < parseInt(maxAmtLmt.val())) {
                                dlyAmtLmt.error($.i18n.t("rsm.verify.dlyAmtGtSigMax"));
                            }
                        }
                        if (!dlyCntLmt.isMultiValue()) {
                            if (!dlyCntLmt.val()) {
                                dlyCntLmt.error($.i18n.t("rsm.verify.notNull"));
                            }
                            if (dlyCntLmt.val() <= 0) {
                                dlyCntLmt.error($.i18n.t("rsm.verify.errTyp"));
                            }
                            if (dlyCntLmt.val().length >= 10) {
                                dlyCntLmt.error($.i18n.t("rsm.verify.maxLen10"));
                            }
                        }
                        if (!mlyAmtLmt.isMultiValue()) {
                            if (!mlyAmtLmt.val()) {
                                mlyAmtLmt.error($.i18n.t("rsm.verify.notNull"));
                            }
                            if (mlyAmtLmt.val().length >= 14) {
                                mlyAmtLmt.error($.i18n.t("rsm.verify.maxLen16"));
                            }
                            if (parseInt(mlyAmtLmt.val()) < parseInt(dlyAmtLmt.val())) {
                                mlyAmtLmt.error($.i18n.t("rsm.verify.mlyAmtLtDlyAmt"));
                            }
                        }
                        if (!mlyCntLmt.isMultiValue()) {
                            if (!mlyCntLmt.val()) {
                                mlyCntLmt.error($.i18n.t("rsm.verify.notNull"));
                            }
                            if (mlyCntLmt.val() <= 0) {
                                mlyCntLmt.error($.i18n.t("rsm.verify.errTyp"));
                            }
                            if (mlyCntLmt.val().length >= 10) {
                                mlyCntLmt.error($.i18n.t("rsm.verify.maxLen10"));
                            }
                            if (parseInt(mlyCntLmt.val()) < parseInt(dlyCntLmt.val())) {
                                mlyCntLmt.error($.i18n.t("rsm.verify.mlyCntLtDlyCnt"));
                            }
                        }
                        if (this.inError()) {
                            return false;
                        }
                    }
                });

                table = $('#example').DataTable({
                    dom: 'B<"floatright"l>rtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/rsm/check/param/findAll',
                        type: 'POST',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    searching: true,
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        { extend: "create", editor: editor },
                        { extend: "edit", editor: editor },
                        { extend: "remove", editor: editor }
                        //                            {extend: "edit", editor: editor}
                    ],
                    columns: [
                        {
                            data: 'parmId',
                            visible: false
                        }, {
                            data: 'acTyp',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '1':
                                        return $.i18n.t("rsm.constant.acTyp.main");
                                }
                            }
                        }, {
                            data: 'lmtLvl'
                            //                                render: function (data, type, row) {
                            //                                    switch (data) {
                            //                                        case '0':
                            //                                            return $.i18n.t("rsm.constant.lmtLvl.user");
                            //                                    }
                            //                                }
                        }, {
                            data: 'dcPtyFlg',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '0':
                                        return $.i18n.t("rsm.constant.dcPtyFlg.all");
                                    case '1':
                                        return $.i18n.t("rsm.constant.dcPtyFlg.stl");
                                    case '2':
                                        return $.i18n.t("rsm.constant.dcPtyFlg.pay");
                                }
                            }
                        }, {
                            data: 'txTyp',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '00':
                                        return $.i18n.t("rsm.constant.txTyp.all");
                                    case '01':
                                        return $.i18n.t("rsm.constant.txTyp.recharge");
                                    case '02':
                                        return $.i18n.t("rsm.constant.txTyp.consume");
                                    case '03':
                                        return $.i18n.t("rsm.constant.txTyp.transfer");
                                    case '04':
                                        return $.i18n.t("rsm.constant.txTyp.withdraw");
                                    case '05':
                                        return $.i18n.t("rsm.constant.txTyp.seatel");
                                    case '06':
                                        return $.i18n.t("rsm.constant.txTyp.refunds");
                                    case '07':
                                        return $.i18n.t("rsm.constant.txTyp.interest");
                                    case '08':
                                        return $.i18n.t("rsm.constant.txTyp.payment");
                                }
                            }
                        }, {
                            data: 'payTyp',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '00':
                                        return $.i18n.t("rsm.constant.payTyp.all");
                                    case '01':
                                        return $.i18n.t("rsm.constant.payTyp.account");
                                    case '02':
                                        return $.i18n.t("rsm.constant.payTyp.quickPay");
                                    case '03':
                                        return $.i18n.t("rsm.constant.payTyp.seatel");
                                    case '04':
                                        return $.i18n.t("rsm.constant.payTyp.wechat");
                                    case '05':
                                        return $.i18n.t("rsm.constant.payTyp.alipay");
                                    case '06':
                                        return $.i18n.t("rsm.constant.payTyp.bestpay");
                                    case '07':
                                        return $.i18n.t("rsm.constant.payTyp.offline");
                                }
                            }
                        }
                    ]
                });

                editor.on('open', function (e, json, data) {

                    getStateList().then(function (data) {

                        var map = eval(data), d = [];
                        for (var key in map) {
                            d.push({
                                value: map[key],
                                label: key
                            });
                        }
                        editor.field('lmtLvl').update(d);
                    });

                    var orgLvl = table.rows('.selected').data()[0].lmtLvl;

                    if (data == 'edit') {
                        editor.field('lmtLvl').val(orgLvl);
                    }
                });
            });
        });

        function searchButton() {
            var acTyp = $("select[name='searchAcTyp']").val();
            var lmtLvl = $("select[name='searchLmtLvl']").val();
            var dcPtyFlg = $("select[name='searchDcPtyFlg']").val();
            var txTyp = $("select[name='searchTxTyp']").val();
            var payTyp = $("select[name='searchPayTyp']").val();

            table.column(1).search(acTyp)
                .column(2).search(lmtLvl)
                .column(3).search(dcPtyFlg)
                .column(4).search(txTyp)
                .column(5).search(payTyp)
                .draw();
        }
    </script>
</body>

</html>