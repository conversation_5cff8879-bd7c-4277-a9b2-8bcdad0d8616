<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="rsm.check.rule.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright {
            display: inline;
            float: right;
            padding-left: 20px;
            padding-right: 20px;
        }

        ._right {
            display: inline;
            float: right;
            padding-right: 15px;
        }

        .dt-buttons {
            display: none;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="rsm.check.rule.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.rsm"></a>
                        </li>

                        <li class="active">
                            <strong data-i18n="rsm.check.rule.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">

                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.check.rule.ruleId"></label>
                                            <input class="form-control" name="searchId" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.check.rule.ruleTyp"></label>
                                            <select class="form-control" name="searchRuleTyp">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="01" data-i18n="rsm.constant.ruleTyp.cnt"></option>
                                                <option value="02" data-i18n="rsm.constant.ruleTyp.amt"></option>
                                                <option value="03" data-i18n="rsm.constant.ruleTyp.cnl"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="rsm.check.rule.dcPtyFlg"></label>
                                            <select class="form-control" name="searchDcPtyFlg">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="1" data-i18n="rsm.constant.dcPtyFlg.stl"></option>
                                                <option value="2" data-i18n="rsm.constant.dcPtyFlg.pay"></option>
                                            </select>
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary"
                                                onclick="$('.dt-button.buttons-create').click()"
                                                data-i18n="rsm.add"></button>
                                            <button type="button" class="btn btn-primary"
                                                onclick="$('.dt-button.buttons-edit').click()"
                                                data-i18n="rsm.edit"></button>
                                            <button type="button" class="btn btn-primary" data-i18n="rsm.search"
                                                onclick="searchButton()"></button>
                                        </div>
                                    </div>
                                </form>
                                <div class="clearfix"></div>
                                <hr />

                                <div class="table-responsive">
                                    <table id="example" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="rsm.check.rule.ruleId"></th>
                                                <th data-i18n="rsm.check.rule.ruleNm"></th>
                                                <th data-i18n="rsm.check.rule.ruleTyp"></th>
                                                <th data-i18n="rsm.check.rule.dcPtyFlg"></th>
                                                <th data-i18n="rsm.check.rule.ruleDesc"></th>
                                                <th data-i18n="rsm.check.rule.cpnNm"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <div th:replace="script"></div>
    <script>
        var table;
        var editor;
        var order = [1, 'asc'];
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {

                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/rsm/check/rule/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/rsm/check/rule/edit',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#example",
                    idSrc: 'ruleId',
                    fields: [
                        { label: $.i18n.t("rsm.check.rule.ruleId"), name: "ruleId" },
                        { label: $.i18n.t("rsm.check.rule.ruleNm"), name: "ruleNm" },
                        {
                            label: $.i18n.t("rsm.check.rule.ruleTyp"), name: "ruleTyp",
                            type: "select",
                            options: [
                                { label: $.i18n.t("rsm.constant.ruleTyp.cnt"), value: "01" },
                                { label: $.i18n.t("rsm.constant.ruleTyp.amt"), value: "02" },
                                { label: $.i18n.t("rsm.constant.ruleTyp.cnl"), value: "03" }
                            ]
                        },
                        {
                            label: $.i18n.t("rsm.check.rule.dcPtyFlg"), name: "dcPtyFlg",
                            type: "select",
                            options: [
                                { label: $.i18n.t("rsm.constant.dcPtyFlg.stl"), value: "1" },
                                { label: $.i18n.t("rsm.constant.dcPtyFlg.pay"), value: "2" }
                            ]
                        },
                        { label: $.i18n.t("rsm.check.rule.ruleDesc"), name: "ruleDesc" },
                        { label: $.i18n.t("rsm.check.rule.cpnNm"), name: "cpnNm" }
                    ],
                    i18n: {
                        create: {
                            button: $.i18n.t("rsm.add"),
                            title: $.i18n.t("rsm.check.rule.addRule"),
                            submit: $.i18n.t("rsm.add")
                        },
                        edit: {
                            button: $.i18n.t("rsm.edit"),
                            title: $.i18n.t("rsm.check.rule.updRule"),
                            submit: $.i18n.t("rsm.edit")
                        }
                    }
                });

                editor.on('preSubmit', function (e, o, action) {
                    var id = editor.field('id');
                    //                        o.id = id.val();
                });

                table = $('#example').DataTable({
                    dom: 'B<"floatright"l>rtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/rsm/check/rule/findAll',
                        type: 'POST',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    searching: true,
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        { extend: "create", editor: editor },
                        { extend: "edit", editor: editor }
                    ],
                    columns: [
                        {
                            data: 'ruleId'
                        }, {
                            data: 'ruleNm'
                        }, {
                            data: 'ruleTyp',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '01':
                                        return $.i18n.t("rsm.constant.ruleTyp.cnt");
                                    case '02':
                                        return $.i18n.t("rsm.constant.ruleTyp.amt");
                                    case '03':
                                        return $.i18n.t("rsm.constant.ruleTyp.cnl");
                                }
                            }
                        }, {
                            data: 'dcPtyFlg',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '0':
                                        return $.i18n.t("rsm.constant.dcPtyFlg.all");
                                    case '1':
                                        return $.i18n.t("rsm.constant.dcPtyFlg.stl");
                                    case '2':
                                        return $.i18n.t("rsm.constant.dcPtyFlg.pay");
                                }
                            }
                        }, {
                            data: 'ruleDesc'
                        }, {
                            data: 'cpnNm'
                        }
                    ]
                });
            });

        });

        function searchButton() {
            var id = $("input[name='searchId']").val();
            var ruleTyp = $("select[name='searchRuleTyp']").val();
            var dcPtyFlg = $("select[name='searchDcPtyFlg']").val();
            console.log(id);
            console.log(ruleTyp);
            console.log(dcPtyFlg);

            table.column(0).search(id)
                .column(2).search(ruleTyp)
                .column(3).search(dcPtyFlg)
                .draw();
        }
    </script>
</body>

</html>