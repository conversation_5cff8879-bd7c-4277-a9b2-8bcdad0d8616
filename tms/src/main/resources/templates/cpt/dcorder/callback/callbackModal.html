<!-- Cregis回调记录详情模态框 -->
<div class="modal fade" id="callbackDetailModal" tabindex="-1" role="dialog" aria-labelledby="callbackDetailModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="callbackDetailModalLabel" data-i18n="dcorder.callback.detailTitle">
                    Cregis回调记录详情</h4>
            </div>
            <style>
    #callbackDetailModal .form-control-static {
        word-wrap: break-word;
        word-break: break-all;
    }
</style>
<div class="modal-body">
                <div class="form-horizontal">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title" data-i18n="dcorder.callback.basicInfo"></h5>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.id"></label>
                                <div class="col-md-4">
                                    <p id="detail_id" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.platformOrderId"></label>
                                <div class="col-md-4">
                                    <p id="detail_orderId" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.bilOrderNo"></label>
                                <div class="col-md-4">
                                    <p id="detail_bilOrderNo" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.callbackReceiveTime"></label>
                                <div class="col-md-4">
                                    <p id="detail_callbackTime" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.isProcessed"></label>
                                <div class="col-md-4">
                                    <p id="detail_isProcessed" class="form-control-static"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title" data-i18n="dcorder.callback.transactionBaseInfo"></h5>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.txHash"></label>
                                <div class="col-md-10">
                                    <p id="detail_txBaseTxId" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.blockHash"></label>
                                <div class="col-md-10">
                                    <p id="detail_txBaseBlockHash" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.txBaseNetwork"></label>
                                <div class="col-md-4">
                                    <p id="detail_txBaseNetwork" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.blockId"></label>
                                <div class="col-md-4">
                                    <p id="detail_txBaseBlockId" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.errorCode"></label>
                                <div class="col-md-4">
                                    <p id="detail_txBaseEcode" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.txGroupId"></label>
                                <div class="col-md-4">
                                    <p id="detail_txBaseGroupId" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.txFee"></label>
                                <div class="col-md-4">
                                    <p id="detail_txBaseFee" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.txStatus"></label>
                                <div class="col-md-4">
                                    <p id="detail_txBaseStatus" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.txCreateTime"></label>
                                <div class="col-md-4">
                                    <p id="detail_txBaseCreateTime" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.computeUnitsConsumed"></label>
                                <div class="col-md-4">
                                    <p id="detail_txBaseComputeUnitsConsumed" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.contractProgramId"></label>
                                <div class="col-md-10">
                                    <p id="detail_txBaseProgramId" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.txMemo"></label>
                                <div class="col-md-10">
                                    <p id="detail_txBaseMemo" class="form-control-static"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title" data-i18n="dcorder.callback.accountTransactionInfo"></h5>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.platformAddress"></label>
                                <div class="col-md-10">
                                    <p id="detail_address" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.counterpartyAddress"></label>
                                <div class="col-md-10">
                                    <p id="detail_cpAddress" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.vaultCode"></label>
                                <div class="col-md-4">
                                    <p id="detail_vaultCode" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.accountId"></label>
                                <div class="col-md-4">
                                    <p id="detail_accountId" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.accountType"></label>
                                <div class="col-md-4">
                                    <p id="detail_accountType" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.coinId"></label>
                                <div class="col-md-4">
                                    <p id="detail_coinId" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.txBaseNetwork"></label>
                                <div class="col-md-4">
                                    <p id="detail_network" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.channel"></label>
                                <div class="col-md-4">
                                    <p id="detail_channel" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.amount"></label>
                                <div class="col-md-4">
                                    <p id="detail_amount" class="form-control-static"></p>
                                </div>
                                <label class="col-md-2 control-label" data-i18n="dcorder.callback.direction"></label>
                                <div class="col-md-4">
                                    <p id="detail_direction" class="form-control-static"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"
                    data-i18n="dcorder.callback.close">关闭</button>
            </div>
        </div>
    </div>
</div>