<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="cregis.callback.title">Cregis回调记录查询</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="cregis.callback.title">Cregis回调记录查询</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="dcorder.transfer.dcOrderManagement">数币订单管理</a>
                        </li>
                        <li>
                            <strong data-i18n="cregis.callback.title">Cregis回调记录查询</strong>
                        </li>
                    </ol>
                </div>
            </div>



            <!--回调记录详细信息弹窗-->
            <div th:replace="cpt/dcorder/callback/callbackModal"></div>

            <!--数据表格-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="callbackTable"
                                    class="table table-striped table-bordered table-hover dataTables-example">
                                    <thead>
                                        <tr>
                                            <th data-i18n="cregis.callback.id"></th>
                                            <th data-i18n="cregis.callback.txBaseTxId"></th>
                                            <th data-i18n="cregis.callback.txBaseStatus"></th>
                                            <th data-i18n="cregis.callback.orderId"></th>
                                            <th data-i18n="cregis.callback.bilOrderNo"></th>
                                            <th data-i18n="cregis.callback.coinId"></th>
                                            <th data-i18n="cregis.callback.amount"></th>
                                            <th data-i18n="cregis.callback.direction"></th>
                                            <th data-i18n="cregis.callback.callbackTime"></th>
                                            <th data-i18n="cregis.callback.isProcessed.title"></th>
                                            <th data-i18n="common.operations"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <div th:replace="script"></div>

    <script>
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                // 初始化数据表格
                var table = $('#callbackTable').DataTable({
                    processing: true,
                    serverSide: true,
                    searching: false,
                    ordering: false,
                    lengthChange: false,
                    pageLength: 10,
                    responsive: true,
                    language: {
                        url: languageUrl
                    },
                    ajax: {
                        contentType: 'application/json',
                        url: '/cpt/dcorder/callback/findAll',
                        type: 'post',
                        data: function (d) {
                            var queryData = {
                                draw: d.draw,
                                start: d.start,
                                length: d.length,
                                extra_search: {
                                    "orderId": $("#orderId").val(),
                                    "txBaseTxId": $("#txBaseTxId").val(),
                                    "coinId": $("#coinId").val(),
                                    "txBaseStatus": $("#txBaseStatus").val(),
                                    "isProcessed": $("#isProcessed").val()
                                }
                            };
                            return JSON.stringify(queryData);
                        },
                        dataSrc: function (json) {
                            if (json && json.data) {
                                return json.data;
                            } else {
                                console.error('Invalid response format:', json);
                                return [];
                            }
                        },
                        error: function (xhr, error, thrown) {
                            console.error('Ajax error:', error);
                            console.error('Response:', xhr.responseText);
                        }
                    },
                    columns: [
                        { data: 'id' },
                        { data: 'txBaseTxId' },
                        { data: 'txBaseStatus' },
                        { data: 'orderId' },
                        { data: 'bilOrderNo' },
                        { data: 'coinId' },
                        { data: 'amount' },
                        {
                            data: 'direction',
                            render: function (data, type, row) {
                                if (data === 'IN') {
                                    return $.i18n.t('dcorder.callback.inbound');
                                } else if (data === 'OUT') {
                                    return $.i18n.t('dcorder.callback.outbound');
                                }
                                return data;
                            }
                        },
                        {
                            data: 'callbackTime',
                            render: function (data, type, row) {
                                if (data) {
                                    return new Date(data).toLocaleString();
                                }
                                return '';
                            }
                        },
                        {
                            data: 'isProcessed',
                            render: function (data, type, row) {
                                if (data === 1) {
                                    return $.i18n.t('cregis.callback.isProcessed.yes');
                                } else if (data === 0) {
                                    return $.i18n.t('cregis.callback.isProcessed.no');
                                }
                                return data;
                            }
                        },
                        {
                            data: null,
                            render: function (data, type, row) {
                                return '<button type="button" class="btn btn-primary btn-sm" onclick="showDetail(' + row.id + ')">' + $.i18n.t('cpo.viewModal') + '</button>';
                            }
                        }
                    ]
                });
            });
        });

        // 查询按钮
        function search() {
            $('#callbackTable').DataTable().ajax.reload();
        }

        // 显示详情
        function showDetail(id) {
            $.ajax({
                type: "POST",
                url: "/cpt/dcorder/callback/findDetail",
                data: { "id": id },
                dataType: "json",
                success: function (data) {
                    if (data) {
                        // 填充详情数据
                        $('#detail_id').text(data.id || '');
                        $('#detail_txBaseNetwork').text(data.txBaseNetwork || '');
                        $('#detail_txBaseBlockId').text(data.txBaseBlockId || '');
                        $('#detail_txBaseTxId').text(data.txBaseTxId || '');
                        $('#detail_txBaseEcode').text(data.txBaseEcode || '');
                        $('#detail_txBaseGroupId').text(data.txBaseGroupId || '');
                        $('#detail_txBaseFee').text(data.txBaseFee || '');
                        $('#detail_txBaseStatus').text(data.txBaseStatus || '');
                        $('#detail_txBaseCreateTime').text(data.txBaseCreateTime ? new Date(data.txBaseCreateTime).toLocaleString() : '');
                        $('#detail_txBaseBlockHash').text(data.txBaseBlockHash || '');
                        $('#detail_txBaseProgramId').text(data.txBaseProgramId || '');
                        $('#detail_txBaseComputeUnitsConsumed').text(data.txBaseComputeUnitsConsumed || '');
                        $('#detail_txBaseMemo').text(data.txBaseMemo || '');
                        $('#detail_vaultCode').text(data.vaultCode || '');
                        $('#detail_accountId').text(data.accountId || '');
                        $('#detail_accountType').text(data.accountType || '');
                        $('#detail_orderId').text(data.orderId || '');
                        $('#detail_bilOrderNo').text(data.bilOrderNo || '');
                        $('#detail_coinId').text(data.coinId || '');
                        $('#detail_network').text(data.network || '');
                        $('#detail_address').text(data.address || '');
                        $('#detail_cpAddress').text(data.cpAddress || '');
                        $('#detail_amount').text(data.amount || '');
                        $('#detail_direction').text(data.direction === 'IN' ? $.i18n.t('dcorder.callback.inbound') : (data.direction === 'OUT' ? $.i18n.t('dcorder.callback.outbound') : data.direction));
                        $('#detail_channel').text(data.channel || '');
                        $('#detail_callbackTime').text(data.callbackTime ? new Date(data.callbackTime).toLocaleString() : '');
                        $('#detail_isProcessed').text(data.isProcessed === 1 ? $.i18n.t('cregis.callback.isProcessed.yes') : (data.isProcessed === 0 ? $.i18n.t('cregis.callback.isProcessed.no') : data.isProcessed));

                        // 显示模态框
                        $('#callbackDetailModal').modal('show');
                    } else {
                        alert($.i18n.t('dcorder.callback.alert.getDetailFailed'));
                    }
                },
                error: function () {
                    alert($.i18n.t('dcorder.callback.alert.getDetailFailed'));
                }
            });
        }
    </script>
</body>

</html>