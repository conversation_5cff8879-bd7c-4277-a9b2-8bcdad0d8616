<!--转账订单详细信息-->
<div class="modal inmodal" id="handleModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content animated">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close</span></button>
                <h4 class="modal-title" data-i18n="dcorder.transfer.orderDetail"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="order_no"
                            data-i18n="dcorder.transfer.orderNo">订单号</label>
                        <div class="col-sm-6">
                            <input type="text" id="order_no" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="tx_type"
                            data-i18n="dcorder.transfer.txType">交易类型</label>
                        <div class="col-sm-6">
                            <input type="text" id="tx_type" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="bus_type"
                            data-i18n="dcorder.transfer.busType">业务类型</label>
                        <div class="col-sm-6">
                            <input type="text" id="bus_type" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="payer_id"
                            data-i18n="dcorder.transfer.payerId">付款方</label>
                        <div class="col-sm-6">
                            <input type="text" id="payer_id" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="payee_id"
                            data-i18n="dcorder.transfer.payeeId">收款方</label>
                        <div class="col-sm-6">
                            <input type="text" id="payee_id" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="bus_order_no"
                            data-i18n="dcorder.transfer.busOrderNo">外围订单号</label>
                        <div class="col-sm-6">
                            <input type="text" id="bus_order_no" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="order_amt"
                            data-i18n="dcorder.transfer.orderAmt">交易金额</label>
                        <div class="col-sm-6">
                            <input type="text" id="order_amt" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="fee" data-i18n="dcorder.transfer.fee">手续费</label>
                        <div class="col-sm-6">
                            <input type="text" id="fee" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="order_status"
                            data-i18n="dcorder.transfer.orderStatus">交易状态</label>
                        <div class="col-sm-6">
                            <input type="text" id="order_status" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="order_tm"
                            data-i18n="dcorder.transfer.orderTm">交易时间</label>
                        <div class="col-sm-6">
                            <input type="text" id="order_tm" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="tx_hash"
                            data-i18n="dcorder.transfer.txHash">交易哈希</label>
                        <div class="col-sm-6">
                            <input type="text" id="tx_hash" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label" for="rmk" data-i18n="dcorder.transfer.remark">备注</label>
                        <div class="col-sm-6">
                            <input type="text" id="rmk" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group" id="fileUrlGroup" style="display: none;">
                        <label class="col-sm-4 control-label" data-i18n="dcorder.transferReview.fileUrl">交易材料</label>
                        <div class="col-sm-6">
                            <div id="imageContainer">
                                <img id="fileUrlImage" src="" alt="交易材料"
                                    style="max-width: 300px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"
                                    onerror="this.style.display='none'; document.getElementById('imageError').style.display='block';">
                                <div id="imageError" style="display: none; color: #999; font-style: italic;">
                                    图片加载失败或格式不支持</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="payConfirm" class="btn btn-primary" onclick="payConfirm()"
                    data-i18n="dcorder.transfer.payConfirm"></button>
                <button type="button" id="payCancel" class="btn btn-primary" onclick="payCancel()"
                    data-i18n="dcorder.transfer.payCancel"></button>
                <button type="button" class="btn btn-primary" data-dismiss="modal"
                    data-i18n="dcorder.transfer.close"></button>
            </div>
        </div>
    </div>
</div>