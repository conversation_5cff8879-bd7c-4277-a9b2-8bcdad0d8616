<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="cpi.fundHead.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="cpi.fundHead.content"></h2>
                    <ol class="breadcrumb">

                        <li>
                            <a data-i18n="cpi.secondLevelOrderMgr"></a>
                        </li>
                        <li>
                            <strong data-i18n="cpi.fundHead.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="mblNo" data-i18n="cpi.mblNo"></label>
                                            <input name="mblNo" id="mblNo" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="corpBusTyp"
                                                data-i18n="cpi.corpBusTyp"></label>
                                            <select name="corpBusTyp" id="corpBusTyp" class="form-control" value="">
                                                <option value="" data-i18n="cpi.selectByNull" selected></option>
                                                <option value="02" data-i18n="cpi.txTyp-fastpay">快捷</option>
                                                <option value="03" data-i18n="cpi.txTyp-ebankpay">网银</option>
                                                <option value="04" data-i18n="cpi.txTyp-remittance">汇款</option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="crdCorpOrg"
                                                data-i18n="cpi.crdCorpOrg"></label>
                                            <select name="crdCorpOrg" id="crdCorpOrg" class="form-control" value="">
                                                <option value="" data-i18n="cpi.selectByNull" selected></option>
                                                <option value="ICBC" data-i18n="corg.ICBC"></option>
                                                <option value="ABC" data-i18n="corg.ABC"></option>
                                                <option value="CCB" data-i18n="corg.CCB"></option>
                                                <option value="BCM" data-i18n="corg.BCM"></option>
                                                <option value="BOC" data-i18n="corg.BOC"></option>
                                                <option value="BEA" data-i18n="corg.BEA"></option>
                                                <option value="WeChat" data-i18n="corg.WeChat"></option>
                                                <option value="BESTPAY" data-i18n="corg.BESTPAY"></option>
                                                <option value="CBP" data-i18n="corg.CBP"></option>
                                                <option value="ABA" data-i18n="corg.ABA"></option>
                                                <option value="ACLEDA" data-i18n="corg.ACLEDA"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="reqOrdNo"
                                                data-i18n="cpi.reqOrdNo"></label>
                                            <input name="reqOrdNo" id="reqOrdNo" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="corpBusSubTyp"
                                                data-i18n="cpi.corpBusSubTyp"></label>
                                            <select name="corpBusSubTyp" id="corpBusSubTyp" class="form-control"
                                                value="">
                                                <option value="" data-i18n="cpi.selectByNull" selected></option>
                                                <option value="0201" data-i18n="cpi.fastpay"></option>
                                                <option value="0202" data-i18n="cpi.fastpayConsume"></option>
                                                <option value="0301" data-i18n="cpi.ebankpay"></option>
                                                <option value="0302" data-i18n="cpi.ebankpayConsume"></option>
                                                <option value="0303" data-i18n="cpi.ebankpayMercScan"></option>
                                                <option value="0304" data-i18n="cpi.ebankpayUserScan"></option>
                                                <option value="0401" data-i18n="cpi.remittance"></option>
                                                <option value="0402" data-i18n="cpi.bussinessRemittance"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="ordSts" data-i18n="cpi.ordSts"></label>
                                            <select name="ordSts" id="ordSts" class="form-control" value="">
                                                <option value="" data-i18n="cpi.selectByNull" selected></option>
                                                <option value="S1" data-i18n="cpi.fund-S1"></option>
                                                <option value="F1" data-i18n="cpi.fund-F1"></option>
                                                <option value="W1" data-i18n="cpi.fund-W1"></option>
                                                <option value="W2" data-i18n="cpi.fund-W2"></option>
                                                <option value="R1" data-i18n="cpi.fund-R1"></option>
                                                <option value="R2" data-i18n="cpi.fund-R2"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="fudOrdNo"
                                                data-i18n="cpi.fudOrdNo"></label>
                                            <input name="fudOrdNo" id="fudOrdNo" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="rutCorpOrg"
                                                data-i18n="cpi.rutCorpOrg"></label>
                                            <select name="rutCorpOrg" id="rutCorpOrg" class="form-control" value="">
                                                <option value="" data-i18n="cpi.selectByNull" selected></option>
                                                <option value="ICBC" data-i18n="corg.ICBC"></option>
                                                <option value="ABC" data-i18n="corg.ABC"></option>
                                                <option value="CCB" data-i18n="corg.CCB"></option>
                                                <option value="BCM" data-i18n="corg.BCM"></option>
                                                <option value="BOC" data-i18n="corg.BOC"></option>
                                                <option value="BEA" data-i18n="corg.BEA"></option>
                                                <option value="WeChat" data-i18n="corg.WeChat"></option>
                                                <option value="BESTPAY" data-i18n="corg.BESTPAY"></option>
                                                <option value="CBP" data-i18n="corg.CBP"></option>
                                                <option value="ABA" data-i18n="corg.ABA"></option>
                                                <option value="ACLEDA" data-i18n="corg.ACLEDA"></option>
                                                <option value="ALIPAY" data-i18n="corg.ALIPAY"></option>
                                            </select>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="beginDate"
                                                data-i18n="cpi.submitDate"></label>
                                            <input name="beginDate" id="beginDate" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="endDate"
                                                data-i18n="cpi.submitDate"></label>
                                            <input name="endDate" id="endDate" class="form-control" value="" />
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" id="searchBtn" onclick="search()"
                                                class="btn btn-primary" data-i18n="cpi.search"></button>
                                            <button type="button" id="viewDetailBtn" onclick="viewRemitOrder()"
                                                class="btn btn-primary" data-toggle="modal"
                                                data-i18n="cpi.viewRemitOrder"></button>
                                            <button type="button" id="orderHandleBtn" onclick="handleRemitOrder()"
                                                class="btn btn-primary" data-toggle="modal"
                                                data-i18n="cpi.handleRemitOrder"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!--汇款确认，以及查看汇款详细信息-->
                <div th:replace="cpt/order/fund/fundModal"></div>

                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="orderInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="cpi.userId"></th>
                                                <th data-i18n="cpi.mblNo"></th>
                                                <th data-i18n="cpi.corpBusTyp"></th>
                                                <th data-i18n="cpi.corpBusSubTyp"></th>
                                                <th data-i18n="cpi.rutCorpOrg"></th>
                                                <th data-i18n="cpi.crdCorpOrg"></th>
                                                <th data-i18n="cpi.crdAcTyp"></th>
                                                <th data-i18n="cpi.ordAmt"></th>
                                                <th data-i18n="cpi.fee"></th>
                                                <th data-i18n="cpi.ordSts"></th>
                                                <th data-i18n="cpi.fudOrdNo"></th>
                                                <th data-i18n="cpi.reqOrdNo"></th>
                                                <th data-i18n="cpi.ordDt"></th>
                                                <th data-i18n="cpi.ordTm"></th>
                                                <th data-i18n="cpi.reason"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    table: "#orderInf",
                    idSrc: 'fudOrdNo',
                    fields: [
                        { name: "id", type: "hidden" },
                        { label: $.i18n.t("cpi.userId"), name: "userId" },
                        { label: $.i18n.t("cpi.mblNo"), name: "mblNo" },
                        { label: $.i18n.t("cpi.corpBusTyp"), name: "corpBusTyp" },
                        { label: $.i18n.t("cpi.corpBusSubTyp"), name: "corpBusSubTyp" },
                        { label: $.i18n.t("cpi.rutCorpOrg"), name: "rutCorpOrg" },
                        { label: $.i18n.t("cpi.crdCorpOrg"), name: "crdCorpOrg" },
                        { label: $.i18n.t("cpi.crdAcTyp"), name: "crdAcTyp" },
                        { label: $.i18n.t("cpi.ordAmt"), name: "ordAmt" },
                        { label: $.i18n.t("cpi.ordSts"), name: "ordSts" },
                        { label: $.i18n.t("cpi.fudOrdNo"), name: "fudOrdNo" },
                        { label: $.i18n.t("cpi.reqOrdNo"), name: "reqOrdNo" },
                        { label: $.i18n.t("cpi.ordDt"), name: "ordDt" },
                        { label: $.i18n.t("cpi.ordTm"), name: "ordTm" },
                        { label: $.i18n.t("cpi.reason"), name: "rmk" }
                    ]
                });

                table = $('#orderInf').DataTable({
                    dom: 'Blfrtip',
                    columnDefs: [{
                        targets: [0, 1, 2, 9, 10],//指定哪几列
                        render: function (data) {
                            return "\u200C" + data;
                        }
                    }],
                    ajax: {
                        contentType: 'application/json',
                        url: '/cpt/order/fund/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "mblNo": $("#mblNo").val(),
                                "reqOrdNo": $("#reqOrdNo").val(),
                                "fudOrdNo": $("#fudOrdNo").val(),
                                "corpBusTyp": $("#corpBusTyp").val(),
                                "corpBusSubTyp": $("#corpBusSubTyp").val(),
                                "rutCorpOrg": $("#rutCorpOrg").val(),
                                "crdCorpOrg": $("#crdCorpOrg").val(),
                                "ordSts": $("#ordSts").val(),
                                "beginDate": $("#beginDate").val(),
                                "endDate": $("#endDate").val()
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        //                        {extend: "create", editor: editor},
                        //                        {extend: "edit", editor: editor},
                        //                        {extend: "remove", editor: editor},
                        { extend: 'copyHtml5' },
                        { extend: 'csvHtml5' },
                        { extend: 'excelHtml5', title: '充值订单列表' },
                        //                        'copy',
                        //                        'csv',
                        //                        'excel',
                        //                        'pdf',
                        //                        'print'
                    ],
                    columns: [{
                        data: 'userId'
                    }, {
                        data: 'mblNo'
                    }, {
                        data: 'corpBusTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case "02":
                                    return $.i18n.t("cpi.txTyp-fastpay");
                                case "03":
                                    return $.i18n.t("cpi.txTyp-ebankpay");
                                case "04":
                                    return $.i18n.t("cpi.txTyp-remittance");
                                default:
                                    return data;
                            }
                        }
                    }, {
                        data: 'corpBusSubTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case "0201":
                                    return $.i18n.t("cpi.fastpay");
                                case "0202":
                                    return $.i18n.t("cpi.fastpayConsume");
                                case "0301":
                                    return $.i18n.t("cpi.ebankpay");
                                case "0302":
                                    return $.i18n.t("cpi.ebankpayConsume");
                                case "0303":
                                    return $.i18n.t("cpi.ebankpayMercScan");
                                case "0304":
                                    return $.i18n.t("cpi.ebankpayUserScan");
                                case "0401":
                                    return $.i18n.t("cpi.remittance");
                                case "0402":
                                    return $.i18n.t("cpi.bussinessRemittance");
                                default:
                                    return data;
                            }
                        }
                    }, {
                        data: 'rutCorpOrg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "ICBC":
                                    return $.i18n.t("corg.ICBC");
                                case "CMBC":
                                    return $.i18n.t("corg.CMBC");
                                case "CMB":
                                    return $.i18n.t("corg.CMB");
                                case "BEA":
                                    return $.i18n.t("corg.BEA");
                                case "BESTPAY":
                                    return $.i18n.t("corg.BESTPAY");
                                case "WeChat":
                                    return $.i18n.t("corg.WeChat");
                                case "CBP":
                                    return $.i18n.t("corg.CBP");
                                case "ABA":
                                    return $.i18n.t("corg.ABA");
                                case "ACLEDA":
                                    return $.i18n.t("corg.ACLEDA");
                                default:
                                    return data;
                            }
                        }
                    }, {
                        data: 'crdCorpOrg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "ICBC":
                                    return $.i18n.t("corg.ICBC");
                                case "CMBC":
                                    return $.i18n.t("corg.CMBC");
                                case "CMB":
                                    return $.i18n.t("corg.CMB");
                                case "BEA":
                                    return $.i18n.t("corg.BEA");
                                case "BESTPAY":
                                    return $.i18n.t("corg.BESTPAY");
                                case "WeChat":
                                    return $.i18n.t("corg.WeChat");
                                case "CBP":
                                    return $.i18n.t("corg.CBP");
                                case "ABA":
                                    return $.i18n.t("corg.ABA");
                                case "ACLEDA":
                                    return $.i18n.t("corg.ACLEDA");
                                default:
                                    return data;
                            }
                        }
                    }, {
                        data: 'crdAcTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case "D":
                                    return $.i18n.t("cpi.cadTyp-D");
                                case "C":
                                    return $.i18n.t("cpi.cadTyp-C");
                                case "U":
                                    return $.i18n.t("cpi.cadTyp-U");
                                default:
                                    return data;
                            }
                        }
                    }, {
                        data: 'ordAmt'
                    }, {
                        data: 'fee'
                    }, {
                        data: 'ordSts',
                        render: function (data, type, row) {
                            switch (data) {
                                case "S1":
                                    return $.i18n.t("cpi.fund-S1");
                                case "F1":
                                    return $.i18n.t("cpi.fund-F1");
                                case "E1":
                                    return $.i18n.t("cpi.fund-E1");
                                case "W1":
                                    return $.i18n.t("cpi.fund-W1");
                                case "W2":
                                    return $.i18n.t("cpi.fund-W2");
                                case "R1":
                                    return $.i18n.t("cpi.fund-R1");
                                case "R2":
                                    return $.i18n.t("cpi.fund-R2");
                                case "A1":
                                    return $.i18n.t("cpi.fund-A1");
                                default:
                                    return data;
                            }
                        }
                    }, {
                        data: 'fudOrdNo'
                    }, {
                        data: 'reqOrdNo'
                    }, {
                        data: 'ordDt'
                    }, {
                        data: 'ordTm'
                    }, {
                        data: 'rmk'
                    }]
                });
            });

            //查看汇款凭证图片，设置zIndex:9999，显示
            // $('#pic_url').viewer({zIndex:9999}); // 已移除，现在使用pic_container

            //设置模态框backdrop:static时,空白处不关闭；keyboard:false时,esc键盘不关闭
            $('#handleModal').modal({ backdrop: "static", keyboard: false, show: false });
        });

        <!--初始化日期控件-->
        var beginTimePick = $('#beginDate').datetimepicker({
            lang: 'en',
            timepicker: false,
            validateOnBlur: false,
            format: 'Y-m-d ',
            formatDate: 'Y-m-d',
            onChangeDate: function (dateText, inst) {
                endTimePick.datetimepicker('minDate', new Date(dateText.replace("-", "-")));
            }
        });
        var endTimePick = $('#endDate').datetimepicker({
            lang: 'en',
            timepicker: false,
            validateOnBlur: false,
            format: 'Y-m-d ',
            formatDate: 'Y-m-d'
            // maxDate:'+1970/01/01',
        });

        //清空 handleModal 模态框中元素的值
        function clearModalValues() {
            $("#mbl_no").val("");
            $("#user_id").val("");
            $("#ord_dt").val("");
            $("#ord_tm").val("");
            $("#ord_sts").val("");
            $("#rut_corp_corg").val("");
            $("#crd_corp_corg").val("");
            $("#handleReason").val("");
            $("#rmk").val("");
            $("#ord_amt").val("");
            $("#pic_container").empty(); // 清空图片容器
        }

        // 显示图片的函数
        function displayImages(picUrl) {
            var container = $("#pic_container");
            container.empty(); // 清空容器

            if (!picUrl || picUrl.trim() === "") {
                return;
            }

            // 分割图片URL（支持逗号分隔）
            var imageUrls = picUrl.split(',').map(function (url) {
                return url.trim();
            }).filter(function (url) {
                return url.length > 0;
            });

            // 为每个图片URL创建img元素
            imageUrls.forEach(function (url, index) {
                var imgElement = $('<img>', {
                    src: url,
                    class: 'img-responsive',
                    style: 'margin-bottom: 10px; max-width: 100%; height: auto;'
                });
                container.append(imgElement);
            });

            // 初始化viewer.js
            if (container.data('viewer')) {
                container.data('viewer').destroy();
            }
            container.viewer({ zIndex: 9999 });
        }

        //查询按钮
        function search() {
            table.ajax.reload();
        }

        // 查看汇款订单
        function viewRemitOrder() {
            clearModalValues();
            $("#remitConfirm").hide();
            $("#remitCancel").hide();
            $("#handleReason").attr("disabled", "disabled");
            $("#handleReason").removeAttr("placeholder");

            //获取表格中选中的单行
            var row = table.row('.selected');
            if (row.length == 0) {
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.shouldSelectOne"), "error");
                return;
            }

            //获取选中行的内容
            var rowData = table.row(row).data();
            var fudOrdNo = rowData["fudOrdNo"];
            var corpBusTyp = rowData["corpBusTyp"];
            if (corpBusTyp != "04") {
                //交易类型不是04-汇款，则不能查看明细
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.typNotSupport"), "error");
                return;
            }

            //异步调用后台服务
            $.ajax({
                url: "/cpt/order/fund/findRemitDetail",
                data: {
                    "fndOrdNo": fudOrdNo
                },
                dataType: "json",
                type: "post",
                success: function (data) {
                    if (data != null) {
                        $("#handleModal").modal("show");
                        $(".modal-backdrop").remove();//删除class值为modal-backdrop的标签，可去除阴影
                        $("#mbl_no").val(data.mblNo);
                        $("#user_id").val(data.userId);
                        $("#ord_dt").val(data.ordDt);
                        $("#ord_tm").val(data.ordTm);
                        $("#ord_sts").val(convertOrdSts(data.ordSts));
                        $("#rut_corp_corg").val(convertCorpOrg(data.rutCorpOrg));
                        $("#crd_corp_corg").val(convertCorpOrg(data.crdCorpOrg));
                        $("#handleReason").val(data.reason);
                        $("#fnd_ord_no").val(data.fndOrdNo);
                        $("#rmk").val(data.rmk);
                        $("#ord_amt").val(data.ordAmt);
                        // 使用新的图片显示函数
                        displayImages(data.picUrl);
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.detailNull"), "error");
                    }
                },
                error: function () {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        }

        //处理汇款订单
        function handleRemitOrder() {
            //获取表格中选中的单行
            var row = table.row('.selected');
            if (row.length == 0) {
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.shouldSelectOne"), "error");
                return;
            }
            clearModalValues();
            $("#handleReason").removeAttr("disabled");
            $("#handleReason").attr("placeholder", $.i18n.t("cpi.lessThan20words"));
            $("#remitConfirm").show();
            $("#remitCancel").show();

            //获取选中行的内容
            var rowData = table.row(row).data();
            var fudOrdNo = rowData["fudOrdNo"];
            var ordSts = rowData["ordSts"];
            var corpBusTyp = rowData["corpBusTyp"];
            if (corpBusTyp != "04") {
                //交易类型不是 04-汇款，则不能查看明细
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.typNotSupport"), "error");
                return;
            }
            if (ordSts == "S1" || ordSts == "F1") {
                //订单状态为 S1-交易成功 、F1-交易失败，则不能处理汇款订单
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.ordStsError"), "error");
                return;
            }

            //异步调用后台服务
            $.ajax({
                url: "/cpt/order/fund/findRemitDetail",
                data: {
                    "fndOrdNo": fudOrdNo
                },
                dataType: "json",
                type: "post",
                success: function (data) {
                    if (data != null) {
                        $("#handleModal").modal("show");
                        $(".modal-backdrop").remove();//删除class值为modal-backdrop的标签，可去除阴影
                        //为模态框中的元素赋值
                        $("#mbl_no").val(data.mblNo);
                        $("#user_id").val(data.userId);
                        $("#ord_dt").val(data.ordDt);
                        $("#ord_tm").val(data.ordTm);
                        $("#ord_sts").val(convertOrdSts(data.ordSts));
                        $("#rut_corp_corg").val(convertCorpOrg(data.rutCorpOrg));
                        $("#crd_corp_corg").val(convertCorpOrg(data.crdCorpOrg));
                        $("#handleReason").val("");
                        // 使用新的图片显示函数
                        displayImages(data.picUrl);
                        $("#fnd_ord_no").val(fudOrdNo);
                        $("#rmk").val(data.rmk);
                        $("#ord_amt").val(data.ordAmt);
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.detailNull"), "error");
                    }
                },
                error: function () {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        }

        //汇款确认
        function remitConfirm() {
            var handleReason = $.trim($("#handleReason").val());
            if (handleReason == "" || handleReason == null) {
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.reasonNull"), "error");
                return;
            }
            //确认提交弹出框
            swal({
                title: $.i18n.t("cpi.remitConfirmTitle1"),
                // text: $.i18n.t("cpi.remitConfirmText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("cpi.swal-cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("cpi.swal-confirm"),
                closeOnConfirm: true
            }, function () {
                //隐藏模态框
                var ordSts = "A1";
                var fnd_ord_no = $("#fnd_ord_no").val();
                $.ajax({
                    url: "/cpt/order/fund/remitHandler",
                    data: {
                        "fndOrdNo": fnd_ord_no,
                        "ordSts": ordSts,
                        "reason": handleReason
                    },
                    dataType: "json",
                    type: "post",
                    success: function (data) {
                        var msgCd = data.msgCd;
                        $("#handleModal").modal("hide");
                        if (msgCd == "CPI00000") {
                            swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.remitConfirmSuss"), "success");
                            table.ajax.reload();
                        } else {
                            swal($.i18n.t("cpi.swal-fail"), msgCd + $.i18n.t("cpi.remitConfirmFail"), "error");
                        }
                    },
                    error: function () {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                        $("#handleModal").modal("hide");
                    }
                });
            });
        }

        //汇款退回
        function remitCancel() {
            var handleReason = $.trim($("#handleReason").val());
            if (handleReason == "" || handleReason == null) {
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.reasonNull"), "error");
                return;
            }
            //汇款退回提交弹出框
            swal({
                title: $.i18n.t("cpi.remitConfirmTitle1"),
                // text: $.i18n.t("cpi.remitCancelText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("cpi.swal-cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("cpi.swal-confirm"),
                closeOnConfirm: true
            }, function () {
                //隐藏模态框
                $("#handleModal").modal("hide");
                var ordSts = "F2";
                var fnd_ord_no = $("#fnd_ord_no").val();
                $.ajax({
                    url: "/cpt/order/fund/remitHandler",
                    data: {
                        "fndOrdNo": fnd_ord_no,
                        "reason": handleReason,
                        "ordSts": ordSts
                    },
                    dataType: "json",
                    type: "post",
                    success: function (data) {
                        var msgCd = data.msgCd;
                        if (msgCd == "CPI00000") {
                            swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.remitCancelSucc"), "success");
                            table.ajax.reload();
                        } else {
                            swal($.i18n.t("cpi.swal-fail"), msgCd + $.i18n.t("cpi.remitCancelFail"), "error");
                        }
                    },
                    error: function () {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                    }
                });
            });
        }

        //银行名称转换
        function convertCorpOrg(corgId) {
            switch (corgId) {
                case "ICBC":
                    return $.i18n.t("corg.ICBC");
                case "CMBC":
                    return $.i18n.t("corg.CMBC");
                default:
                    return corgId;
            }
        }

        //订单状态转换
        function convertOrdSts(ordSts) {
            switch (ordSts) {
                case "S1":
                    return $.i18n.t("cpi.fund-S1");
                case "F1":
                    return $.i18n.t("cpi.fund-F1");
                case "E1":
                    return $.i18n.t("cpi.fund-E1");
                case "W1":
                    return $.i18n.t("cpi.fund-W1");
                case "W2":
                    return $.i18n.t("cpi.fund-W2");
                case "R1":
                    return $.i18n.t("cpi.fund-R1");
                case "R2":
                    return $.i18n.t("cpi.fund-R2");
                default:
                    return ordSts;
            }
        }

    </script>
</body>

</html>