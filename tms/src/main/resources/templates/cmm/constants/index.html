<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.cmmsub.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright {
            display: inline;
            float: right;
            padding-left: 20px;
            padding-right: 20px;
        }

        ._right {
            display: inline;
            float: right;
            padding-right: 15px;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.cmmsub.constants"></h2>
                    <ol class="breadcrumb">

                        <li>
                            <a data-i18n="feerule.paramManagement">参数管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.cmmsub.constants"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">

                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="cmm.constant.parmNm"></label>
                                            <input class="form-control" name="searchNm" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" data-i18n="cmm.constant.effFlg"></label>
                                            <select class="form-control" name="searchFlg">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="0" data-i18n="cmm.constant.expired"></option>
                                                <option value="1" data-i18n="cmm.constant.effective"></option>
                                            </select>
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" class="btn btn-primary" data-i18n="rsm.search"
                                                onclick="searchButton()"></button>
                                        </div>
                                    </div>
                                </form>
                                <div class="clearfix"></div>
                                <hr />

                                <div class="table-responsive">
                                    <table id="example" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th data-i18n="cmm.constant.parmNm"></th>
                                                <th data-i18n="cmm.constant.parmVal"></th>
                                                <th data-i18n="cmm.constant.parmDispNm"></th>
                                                <th data-i18n="cmm.constant.parmCls"></th>
                                                <th data-i18n="cmm.constant.effFlg"></th>
                                                <th data-i18n="cmm.constant.effDt"></th>
                                                <th data-i18n="cmm.constant.expDt"></th>
                                                <th data-i18n="cmm.constant.rmk"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <div th:replace="script"></div>
    <script>
        var table;
        var editor;
        var order = [1, 'asc'];

        $(document).ready(function () {

            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }


            i18nLoad.then(function () {

                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/cmm/constants/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/cmm/constants/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/cmm/constants/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#example",
                    idSrc: 'parmId',
                    fields: [
                        { name: "parmId", type: "hidden" },
                        { label: $.i18n.t("cmm.constant.parmNm"), name: "parmNm" },
                        { label: $.i18n.t("cmm.constant.parmVal"), name: "parmVal" },
                        { label: $.i18n.t("cmm.constant.parmDispNm"), name: "parmDispNm" },
                        { label: $.i18n.t("cmm.constant.parmCls"), name: "parmCls" },
                        {
                            label: $.i18n.t("cmm.constant.effFlg"),
                            name: "effFlg",
                            type: "select",
                            options: [
                                { label: $.i18n.t("cmm.constant.expired"), value: "0" },
                                { label: $.i18n.t("cmm.constant.effective"), value: "1" }
                            ]
                        }, {
                            label: $.i18n.t("cmm.constant.effDt"),
                            name: "effDt",
                            type: "datetime"
                        }, {
                            label: $.i18n.t("cmm.constant.expDt"),
                            name: "expDt",
                            type: "datetime"
                        }, {
                            label: $.i18n.t("cmm.constant.rmk"),
                            name: "rmk",
                            type: "textarea"
                        }
                    ],
                    i18n: {
                        create: {
                            button: $.i18n.t("cmm.constant.addParam"),
                            title: $.i18n.t("cmm.constant.addParam"),
                            submit: $.i18n.t("cmm.constant.addParam")
                        },
                        edit: {
                            button: $.i18n.t("cmm.constant.editParam"),
                            title: $.i18n.t("cmm.constant.editParam"),
                            submit: $.i18n.t("cmm.constant.editParam")
                        },
                        remove: {
                            button: $.i18n.t("cmm.constant.deleteParam"),
                            title: $.i18n.t("cmm.constant.deleteParam"),
                            submit: $.i18n.t("cmm.constant.deleteParam"),
                            confirm: {
                                _: $.i18n.t("demo.multi-delete"),
                                1: $.i18n.t("demo.single-delete")
                            }
                        }
                    }
                });

                table = $('#example').DataTable({
                    dom: 'B<"floatright"l>rtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/cmm/constants/findAll',
                        type: 'POST',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        {
                            extend: "create", editor: editor
                        }, {
                            extend: "edit", editor: editor
                        }, {
                            extend: "remove", editor: editor
                        }
                    ],
                    columns: [
                        {
                            data: 'parmId',
                            visible: false
                        }, {
                            data: 'parmNm'
                        }, {
                            data: 'parmVal'
                        }, {
                            data: 'parmDispNm'
                        }, {
                            data: 'parmCls'
                        }, {
                            data: 'effFlg',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '0':
                                        return $.i18n.t("cmm.constant.expired");
                                    case '1':
                                        return $.i18n.t("cmm.constant.effective");
                                }
                            }
                        }, {
                            data: 'effDt'
                        }, {
                            data: 'expDt'
                        }, {
                            data: 'rmk'
                        }
                    ]
                });
            });

        });

        function searchButton() {
            var nm = $("input[name='searchNm']").val();
            var flg = $("select[name='searchFlg']").val();
            table.column(1).search(nm)
                .column(5).search(flg)
                .draw();
        }
    </script>
</body>

</html>