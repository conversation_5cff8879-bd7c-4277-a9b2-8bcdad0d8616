<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="dmbill.DSindex.page_title">数币收款管理</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="dmbill.DSindex.page_title">数币收款管理</h2>
                    <ol class="breadcrumb">

                        <li>
                            <a data-i18n="nav.dcorder">数币订单管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="dmbill.DSindex.page_title">数币收款管理</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">

                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="acNo"
                                                data-i18n="dmbill.DSindex.label_account_no">账户号码</label>
                                            <input name="acNo" id="acNo" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="startTime"
                                                data-i18n="dmbill.DSindex.label_start_time">开始时间</label>
                                            <div class="input-group date">
                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                                <input name="startTime" id="startTime"
                                                    class="form-control datetimepicker" type="text" />
                                            </div>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="endTime"
                                                data-i18n="dmbill.DSindex.label_end_time">结束时间</label>
                                            <div class="input-group date">
                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                                <input name="endTime" id="endTime" class="form-control datetimepicker"
                                                    type="text" />
                                            </div>
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="dmbill.DSindex.btn_search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-white"
                                                onclick="resetForm()" data-i18n="dmbill.DSindex.btn_reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">

                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="dmBillTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="dmbill.DSindex.th_tx_time">交易时间</th>
                                                <th data-i18n="dmbill.DSindex.th_coin_id">交易币种</th>
                                                <th data-i18n="dmbill.DSindex.th_amount">交易金额</th>
                                                <th data-i18n="dmbill.DSindex.th_fee">手续费</th>
                                                <th data-i18n="dmbill.DSindex.th_receive_address">收款地址/账户号</th>
                                                <th data-i18n="dmbill.DSindex.th_pay_address">付款地址/账户号</th>
                                                <th data-i18n="dmbill.DSindex.th_tx_type">交易类型</th>
                                                <th data-i18n="dmbill.DSindex.th_order_id">交易流水号</th>
                                                <th data-i18n="dmbill.DSindex.th_status">交易状态</th>
                                                <th data-i18n="dmbill.DSindex.th_operations">操作</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="dmbill.DSindex.modal_title_detail">交易详情
                    </h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_tx_time">交易时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-txTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_coin_id">交易币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-coinId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_amount">交易金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-amount"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_fee">手续费</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fee"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_receive_address">收款地址/账户号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-receiveAddress"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_pay_address">付款地址/账户号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-payAddress"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_order_id">交易流水号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-orderId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_tx_type">交易类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-txType"></p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_status">交易状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-status"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_memo">附言</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-memo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_channel">交易渠道</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-channel"></p>
                            </div>
                        </div>
                        <!-- 新增字段 -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_network">网络标识</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-network"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_block_id">区块ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-blockId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.DSindex.detail_ecode">错误码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-ecode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_group_id">交易组ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-groupId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_block_hash">区块哈希</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-blockHash"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_program_id">合约程序ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-programId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_compute_units_consumed">消耗的计算单元</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-computeUnitsConsumed"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_vault_code">所属金库编码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-vaultCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_account_id">关联账户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-accountId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_account_type">账户类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-accountType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_bil_order_no">账单编号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-bilOrderNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.DSindex.detail_direction">交易方向</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-direction"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="dmbill.DSindex.btn_close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden elements for JavaScript i18n -->
    <div style="display: none;">
        <span data-i18n="dmbill.DSindex.btn_copy">复制</span>
        <span data-i18n="dmbill.DSindex.excel_title">数币交易管理</span>
        <span data-i18n="dmbill.DSindex.tx_type_ds">数币收款</span>
        <span data-i18n="dmbill.DSindex.tx_type_dc">数币充值</span>
        <span data-i18n="dmbill.DSindex.tx_type_dh">数币兑换</span>
        <span data-i18n="dmbill.DSindex.tx_type_dz">数币转账</span>
        <span data-i18n="dmbill.DSindex.tx_type_dx">数币提现</span>
        <span data-i18n="dmbill.DSindex.tx_type_01">充值</span>
        <span data-i18n="dmbill.DSindex.tx_type_02">消费</span>
        <span data-i18n="dmbill.DSindex.tx_type_03">转账</span>
        <span data-i18n="dmbill.DSindex.tx_type_04">提现</span>
        <span data-i18n="dmbill.DSindex.tx_type_05">充海币</span>
        <span data-i18n="dmbill.DSindex.tx_type_06">退款</span>
        <span data-i18n="dmbill.DSindex.tx_type_07">理财</span>
        <span data-i18n="dmbill.DSindex.tx_type_08">缴费</span>
        <span data-i18n="dmbill.DSindex.status_success">成功</span>
        <span data-i18n="dmbill.DSindex.status_failed">失败</span>
        <span data-i18n="dmbill.DSindex.status_waiting_pay">待支付</span>
        <span data-i18n="dmbill.DSindex.status_processing">系统受理中</span>
        <span data-i18n="dmbill.DSindex.status_pending_confirm">支付待确认</span>
        <span data-i18n="dmbill.DSindex.status_partial_refund">部分退款</span>
        <span data-i18n="dmbill.DSindex.status_timeout">超时</span>
        <span data-i18n="dmbill.DSindex.status_cancelled">撤销</span>
        <span data-i18n="dmbill.DSindex.status_paying">缴费中</span>
        <span data-i18n="dmbill.DSindex.status_pay_failed">缴费失败</span>
        <span data-i18n="dmbill.DSindex.status_pay_success">缴费成功</span>
        <span data-i18n="dmbill.DSindex.btn_view_detail">查看详情</span>
        <span data-i18n="dmbill.DSindex.error_get_detail">获取详情失败</span>
        <span data-i18n="dmbill.DSindex.error_data_load">数据加载失败</span>
    </div>

    <div th:replace="script"></div>

    <script type="text/javascript">
        var table;
        $(document).ready(function () {
            // 初始化日期时间选择器
            $('.datetimepicker').datetimepicker({
                format: 'YYYY-MM-DD HH:mm:ss',
                locale: 'zh-cn',
                showClear: true,
                showClose: true,
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-screenshot',
                    clear: 'fa fa-trash',
                    close: 'fa fa-remove'
                }
            });

            initTable();

            // 初始化国际化
            $("[data-i18n]").i18n();

            // 处理DataTables处理中指示器问题
            $(document).ajaxStop(function () {
                $('.dataTables_processing').hide();
            });

            // 给表格添加绘制完成事件处理
            $('#dmBillTable').on('draw.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面长度变化事件监听
            $('#dmBillTable').on('length.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面变化事件监听
            $('#dmBillTable').on('page.dt', function () {
                setTimeout(function () {
                    $('.dataTables_processing').hide();
                }, 500);
            });
        });

        // 初始化表格
        function initTable() {
            // 初始化draw值
            $('input[name="draw"]').remove();
            $('body').append('<input type="hidden" name="draw" value="1">');

            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
                default:
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
            }

            table = $('#dmBillTable').DataTable({
                dom: 'Blfrtip',
                buttons: [
                    { extend: 'copyHtml5', text: 'Copy' },
                    { extend: 'csvHtml5', text: 'CSV' },
                    { extend: 'excelHtml5', title: $('[data-i18n="dmbill.DSindex.excel_title"]').text() || '数币充值管理', text: 'Excel' }
                ],
                language: {
                    url: languageUrl
                },
                responsive: true,
                serverSide: true,
                searchDelay: 1000,
                searching: false,
                processing: true,
                ordering: false,
                paging: true,
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                pagingType: "simple_numbers",
                ajax: {
                    url: '/bil/dmbill/list',
                    type: 'POST',
                    contentType: 'application/json',
                    data: function (d) {
                        // 更新draw值供dataFilter使用
                        $('input[name="draw"]').val(d.draw);

                        var param = {
                            body: {
                                acNo: $('#acNo').val(),
                                txType: 'DS',
                                startTime: $('#startTime').val() ? new Date($('#startTime').val()).toISOString() : null,
                                endTime: $('#endTime').val() ? new Date($('#endTime').val()).toISOString() : null,
                                pageNo: Math.floor(d.start / d.length) + 1,
                                pageSize: d.length
                            }
                        };
                        console.log("发送请求参数:", param);
                        return JSON.stringify(param);
                    },
                    dataFilter: function (data) {
                        var json = JSON.parse(data);
                        console.log("接收到服务器响应:", json);

                        // 获取总记录数和当前页记录
                        var total = json.body.totalRecords || 0;
                        var records = json.body.records || [];

                        console.log("总记录数:", total);
                        console.log("当前页记录数:", records.length);

                        var returnData = {
                            draw: parseInt($('input[name="draw"]').val()) || 1,
                            recordsTotal: total,
                            recordsFiltered: total,
                            data: records
                        };

                        return JSON.stringify(returnData);
                    },
                    error: function (xhr, error, thrown) {
                        console.error('数据加载错误:', error);
                        var errorText = $('[data-i18n="dmbill.DSindex.error_data_load"]').text() || '数据加载失败';
                        toastr.error(errorText);
                        // 确保处理中指示器消失
                        $('.dataTables_processing').hide();
                    },
                    complete: function () {
                        // 确保处理中指示器消失
                        $('.dataTables_processing').hide();
                    }
                },
                drawCallback: function (settings) {
                    console.log("表格绘制完成, 页面信息:", settings._iDisplayStart, "至", settings._iDisplayStart + settings._iDisplayLength, "共", settings._iRecordsTotal, "条");
                    $('.dataTables_processing').hide();
                },
                columns: [
                    {
                        data: 'txTime',
                        render: function (data) {
                            return data ? new Date(data).toLocaleString() : '--';
                        }
                    },
                    { data: 'coinId' },
                    { data: 'amount' },
                    { data: 'fee' },
                    { data: 'receiveAddress' },
                    { data: 'payAddress' },
                    {
                        data: 'txType',
                        render: function (data) {
                            switch (data) {
                                case 'DS': return $('[data-i18n="dmbill.DSindex.tx_type_ds"]').text() || '数币收款';
                                case 'DC': return $('[data-i18n="dmbill.DSindex.tx_type_dc"]').text() || '数币充值';
                                case 'DH': return $('[data-i18n="dmbill.DSindex.tx_type_dh"]').text() || '数币兑换';
                                case 'DZ': return $('[data-i18n="dmbill.DSindex.tx_type_dz"]').text() || '数币转账';
                                case 'DX': return $('[data-i18n="dmbill.DSindex.tx_type_dx"]').text() || '数币提现';
                                case '01': return $('[data-i18n="dmbill.DSindex.tx_type_01"]').text() || '充值';
                                case '02': return $('[data-i18n="dmbill.DSindex.tx_type_02"]').text() || '消费';
                                case '03': return $('[data-i18n="dmbill.DSindex.tx_type_03"]').text() || '转账';
                                case '04': return $('[data-i18n="dmbill.DSindex.tx_type_04"]').text() || '提现';
                                case '05': return $('[data-i18n="dmbill.DSindex.tx_type_05"]').text() || '充海币';
                                case '06': return $('[data-i18n="dmbill.DSindex.tx_type_06"]').text() || '退款';
                                case '07': return $('[data-i18n="dmbill.DSindex.tx_type_07"]').text() || '理财';
                                case '08': return $('[data-i18n="dmbill.DSindex.tx_type_08"]').text() || '缴费';
                                default: return data || '--';
                            }
                        }
                    },
                    { data: 'orderId' },
                    {
                        data: 'status',
                        render: function (data) {
                            switch (data) {
                                case 'SUCCESS':
                                case 'S':
                                case 'S1':
                                    var successText = $('[data-i18n="dmbill.DSindex.status_success"]').text() || '成功';
                                    return '<span class="label label-primary">' + successText + '</span>';
                                case 'FAILED':
                                case 'F':
                                case 'R2':
                                    var failedText = $('[data-i18n="dmbill.DSindex.status_failed"]').text() || '失败';
                                    return '<span class="label label-danger">' + failedText + '</span>';
                                case 'W':
                                    var waitingPayText = $('[data-i18n="dmbill.DSindex.status_waiting_pay"]').text() || '待支付';
                                    return '<span class="label label-warning">' + waitingPayText + '</span>';
                                case 'W1':
                                    var processingText = $('[data-i18n="dmbill.DSindex.status_processing"]').text() || '系统受理中';
                                    return '<span class="label label-warning">' + processingText + '</span>';
                                case 'P':
                                    var pendingConfirmText = $('[data-i18n="dmbill.DSindex.status_pending_confirm"]').text() || '支付待确认';
                                    return '<span class="label label-info">' + pendingConfirmText + '</span>';
                                case 'R1':
                                    var partialRefundText = $('[data-i18n="dmbill.DSindex.status_partial_refund"]').text() || '部分退款';
                                    return '<span class="label label-info">' + partialRefundText + '</span>';
                                case 'E':
                                    var timeoutText = $('[data-i18n="dmbill.DSindex.status_timeout"]').text() || '超时';
                                    return '<span class="label label-danger">' + timeoutText + '</span>';
                                case 'C':
                                    var cancelledText = $('[data-i18n="dmbill.DSindex.status_cancelled"]').text() || '撤销';
                                    return '<span class="label label-default">' + cancelledText + '</span>';
                                case 'W2':
                                    var payingText = $('[data-i18n="dmbill.DSindex.status_paying"]').text() || '缴费中';
                                    return '<span class="label label-warning">' + payingText + '</span>';
                                case 'F2':
                                    var payFailedText = $('[data-i18n="dmbill.DSindex.status_pay_failed"]').text() || '缴费失败';
                                    return '<span class="label label-danger">' + payFailedText + '</span>';
                                case 'S2':
                                    var paySuccessText = $('[data-i18n="dmbill.DSindex.status_pay_success"]').text() || '缴费成功';
                                    return '<span class="label label-primary">' + paySuccessText + '</span>';
                                default:
                                    return data || '--';
                            }
                        }
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            var viewDetailText = $('[data-i18n="dmbill.DSindex.btn_view_detail"]').text() || '查看详情';
                            return '<button type="button" class="btn btn-xs btn-info view-detail" onclick="viewDetail(\'' + row.orderId + '\')"><i class="fa fa-search"></i> ' + viewDetailText + '</button>';
                        }
                    }
                ]
            });
        }

        // 查询
        function search() {
            console.log("执行搜索操作");
            // 先隐藏处理中指示器（如果存在）
            $('.dataTables_processing').hide();
            // 刷新表格
            table.ajax.reload(function () {
                console.log("表格刷新完成");
                // 回调中再次确保处理中指示器消失
                $('.dataTables_processing').hide();
            }, false);
        }

        // 重置表单
        function resetForm() {
            $('#queryForm')[0].reset();
            // 先隐藏处理中指示器（如果存在）
            $('.dataTables_processing').hide();
            // 刷新表格
            table.ajax.reload(function () {
                // 回调中再次确保处理中指示器消失
                $('.dataTables_processing').hide();
            }, false);
        }

        // 查看详情
        function viewDetail(orderId) {
            $.ajax({
                url: '/bil/dmbill/detail',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    body: orderId
                }),
                success: function (res) {
                    if (res && res.body) {
                        var data = res.body;
                        $('#detail-txTime').text(data.txTime ? new Date(data.txTime).toLocaleString() : '--');
                        $('#detail-coinId').text(data.coinId || '--');
                        $('#detail-amount').text(data.amount || '--');
                        $('#detail-fee').text(data.fee || '--');
                        $('#detail-receiveAddress').text(data.receiveAddress || '--');
                        $('#detail-payAddress').text(data.payAddress || '--');
                        $('#detail-orderId').text(data.orderId || '--');
                        $('#detail-txType').text(formatTxType(data.txType) || '--');
                        $('#detail-txHash').text(data.txHash || '--');
                        $('#detail-status').text(formatOrderStatus(data.status) || '--');
                        $('#detail-memo').text(data.memo || '--');
                        $('#detail-channel').text(data.channel || '--');
                        $('#detailModal').modal('show');
                    } else {
                        var errorText = $('[data-i18n="dmbill.DSindex.error_get_detail"]').text() || '获取详情失败';
                        toastr.error(errorText);
                    }
                },
                error: function () {
                    var errorText = $('[data-i18n="dmbill.DSindex.error_get_detail"]').text() || '获取详情失败';
                    toastr.error(errorText);
                }
            });
        }

        // 格式化交易类型
        function formatTxType(txType) {
            switch (txType) {
                case 'DS': return $('[data-i18n="dmbill.DSindex.tx_type_ds"]').text() || '数币收款';
                case 'DC': return $('[data-i18n="dmbill.DSindex.tx_type_dc"]').text() || '数币充值';
                case 'DH': return $('[data-i18n="dmbill.DSindex.tx_type_dh"]').text() || '数币兑换';
                case 'DZ': return $('[data-i18n="dmbill.DSindex.tx_type_dz"]').text() || '数币转账';
                case 'DX': return $('[data-i18n="dmbill.DSindex.tx_type_dx"]').text() || '数币提现';
                case '01': return $('[data-i18n="dmbill.DSindex.tx_type_01"]').text() || '充值';
                case '02': return $('[data-i18n="dmbill.DSindex.tx_type_02"]').text() || '消费';
                case '03': return $('[data-i18n="dmbill.DSindex.tx_type_03"]').text() || '转账';
                case '04': return $('[data-i18n="dmbill.DSindex.tx_type_04"]').text() || '提现';
                case '05': return $('[data-i18n="dmbill.DSindex.tx_type_05"]').text() || '充海币';
                case '06': return $('[data-i18n="dmbill.DSindex.tx_type_06"]').text() || '退款';
                case '07': return $('[data-i18n="dmbill.DSindex.tx_type_07"]').text() || '理财';
                case '08': return $('[data-i18n="dmbill.DSindex.tx_type_08"]').text() || '缴费';
                default: return txType;
            }
        }

        // 格式化订单状态
        function formatOrderStatus(status) {
            switch (status) {
                case 'SUCCESS':
                case 'S':
                case 'S1':
                    return $('[data-i18n="dmbill.DSindex.status_success"]').text() || '成功';
                case 'FAILED':
                case 'F':
                case 'R2':
                    return $('[data-i18n="dmbill.DSindex.status_failed"]').text() || '失败';
                case 'W':
                    return $('[data-i18n="dmbill.DSindex.status_waiting_pay"]').text() || '待支付';
                case 'W1':
                    return $('[data-i18n="dmbill.DSindex.status_processing"]').text() || '系统受理中';
                case 'P':
                    return $('[data-i18n="dmbill.DSindex.status_pending_confirm"]').text() || '支付待确认';
                case 'R1':
                    return $('[data-i18n="dmbill.DSindex.status_partial_refund"]').text() || '部分退款';
                case 'E':
                    return $('[data-i18n="dmbill.DSindex.status_timeout"]').text() || '超时';
                case 'C':
                    return $('[data-i18n="dmbill.DSindex.status_cancelled"]').text() || '撤销';
                case 'W2':
                    return $('[data-i18n="dmbill.DSindex.status_paying"]').text() || '缴费中';
                case 'F2':
                    return $('[data-i18n="dmbill.DSindex.status_pay_failed"]').text() || '缴费失败';
                case 'S2':
                    return $('[data-i18n="dmbill.DSindex.status_pay_success"]').text() || '缴费成功';
                default:
                    return status;
            }
        }
    </script>
</body>

</html>