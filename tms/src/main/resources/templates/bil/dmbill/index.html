<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="dmbill.title">数币充值管理</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="dmbill.title">数币充值管理</h2>
                    <ol class="breadcrumb">

                        <li>
                            <a data-i18n="dmbill.dcOrderManagement">数币订单管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="dmbill.title">数币充值管理</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">

                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12 query-grid3">
                                        <div class="query-field">
                                            <label class="control-label" for="acNo" data-i18n="dmbill.acNo">账户号码</label>
                                            <input name="acNo" id="acNo" class="form-control" value="" />
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="startTime"
                                                data-i18n="dmbill.startTime">开始时间</label>
                                            <div class="input-group date">
                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                                <input name="startTime" id="startTime"
                                                    class="form-control datetimepicker" type="text" />
                                            </div>
                                        </div>
                                        <div class="query-field">
                                            <label class="control-label" for="endTime"
                                                data-i18n="dmbill.endTime">结束时间</label>
                                            <div class="input-group date">
                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                                <input name="endTime" id="endTime" class="form-control datetimepicker"
                                                    type="text" />
                                            </div>
                                        </div>
                                        <div class="query-actions">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="dmbill.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-white"
                                                onclick="resetForm()" data-i18n="dmbill.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">

                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="dmBillTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="dmbill.txTime">交易时间</th>
                                                <th data-i18n="dmbill.coinId">交易币种</th>
                                                <th data-i18n="dmbill.amount">交易金额</th>
                                                <th data-i18n="dmbill.fee">手续费</th>
                                                <th data-i18n="dmbill.receiveAddress">收款地址/账户号</th>
                                                <th data-i18n="dmbill.payAddress">付款地址/账户号</th>
                                                <th data-i18n="dmbill.txType">交易类型</th>
                                                <th data-i18n="dmbill.orderId">交易流水号</th>
                                                <th data-i18n="dmbill.status">交易状态</th>
                                                <th data-i18n="dmbill.operations">操作</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="dmbill.transactionDetail">交易详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.txTime">交易时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-txTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.coinId">交易币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-coinId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.amount">交易金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-amount"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.fee">手续费</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fee"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.receiveAddress">收款地址/账户号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-receiveAddress"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.payAddress">付款地址/账户号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-payAddress"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.orderId">交易流水号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-orderId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.txType">交易类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-txType"></p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.status">交易状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-status"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.memo">附言</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-memo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.channel">交易渠道</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-channel"></p>
                            </div>
                        </div>
                        <!-- 新增字段 -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.network">网络标识</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-network"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.blockId">区块ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-blockId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.ecode">错误码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-ecode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.groupId">交易组ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-groupId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.blockHash">区块哈希</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-blockHash"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.programId">合约程序ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-programId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"
                                data-i18n="dmbill.computeUnitsConsumed">消耗的计算单元</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-computeUnitsConsumed"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.vaultCode">所属金库编码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-vaultCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.accountId">关联账户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-accountId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.accountType">账户类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-accountType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.bilOrderNo">账单编号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-bilOrderNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="dmbill.direction">交易方向</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-direction"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="dmbill.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <script type="text/javascript">
        var table;
        $(document).ready(function () {
            // 初始化日期时间选择器
            $('.datetimepicker').datetimepicker({
                format: 'YYYY-MM-DD HH:mm:ss',
                locale: 'zh-cn',
                showClear: true,
                showClose: true,
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-screenshot',
                    clear: 'fa fa-trash',
                    close: 'fa fa-remove'
                }
            });

            initTable();

            // 初始化国际化
            $("[data-i18n]").i18n();

            // 处理DataTables处理中指示器问题
            $(document).ajaxStop(function () {
                $('.dataTables_processing').hide();
            });

            // 给表格添加绘制完成事件处理
            $('#dmBillTable').on('draw.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面长度变化事件监听
            $('#dmBillTable').on('length.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面变化事件监听
            $('#dmBillTable').on('page.dt', function () {
                setTimeout(function () {
                    $('.dataTables_processing').hide();
                }, 500);
            });
        });

        // 初始化表格
        function initTable() {
            // 初始化draw值
            $('input[name="draw"]').remove();
            $('body').append('<input type="hidden" name="draw" value="1">');

            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
                default:
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
            }

            table = $('#dmBillTable').DataTable({
                dom: 'Blfrtip',
                buttons: [
                    { extend: 'copyHtml5', text: 'copy' },
                    { extend: 'csvHtml5', text: 'CSV' },
                    { extend: 'excelHtml5', title: i18n.t('dmbill.exportTitle'), text: 'Excel' }
                ],
                language: {
                    url: languageUrl
                },
                responsive: true,
                serverSide: true,
                searchDelay: 1000,
                searching: false,
                processing: true,
                ordering: false,
                paging: true,
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                pagingType: "simple_numbers",
                ajax: {
                    url: '/bil/dmbill/list',
                    type: 'POST',
                    contentType: 'application/json',
                    data: function (d) {
                        // 更新draw值供dataFilter使用
                        $('input[name="draw"]').val(d.draw);

                        var param = {
                            body: {
                                acNo: $('#acNo').val(),
                                txType: 'DC',
                                startTime: $('#startTime').val() ? new Date($('#startTime').val()).toISOString() : null,
                                endTime: $('#endTime').val() ? new Date($('#endTime').val()).toISOString() : null,
                                pageNo: Math.floor(d.start / d.length) + 1,
                                pageSize: d.length
                            }
                        };
                        console.log("发送请求参数:", param);
                        return JSON.stringify(param);
                    },
                    dataFilter: function (data) {
                        var json = JSON.parse(data);
                        console.log("接收到服务器响应:", json);

                        // 获取总记录数和当前页记录
                        var total = json.body.totalRecords || 0;
                        var records = json.body.records || [];

                        console.log("总记录数:", total);
                        console.log("当前页记录数:", records.length);

                        var returnData = {
                            draw: parseInt($('input[name="draw"]').val()) || 1,
                            recordsTotal: total,
                            recordsFiltered: total,
                            data: records
                        };

                        return JSON.stringify(returnData);
                    },
                    error: function (xhr, error, thrown) {
                        console.error('数据加载错误:', error);
                        toastr.error(i18n.t('dmbill.dataLoadFailed'));
                        // 确保处理中指示器消失
                        $('.dataTables_processing').hide();
                    },
                    complete: function () {
                        // 确保处理中指示器消失
                        $('.dataTables_processing').hide();
                    }
                },
                drawCallback: function (settings) {
                    console.log("表格绘制完成, 页面信息:", settings._iDisplayStart, "至", settings._iDisplayStart + settings._iDisplayLength, "共", settings._iRecordsTotal, "条");
                    $('.dataTables_processing').hide();
                },
                columns: [
                    {
                        data: 'txTime',
                        render: function (data) {
                            return data ? new Date(data).toLocaleString() : '--';
                        }
                    },
                    { data: 'coinId' },
                    { data: 'amount' },
                    { data: 'fee' },
                    { data: 'receiveAddress' },
                    { data: 'payAddress' },
                    {
                        data: 'txType',
                        render: function (data) {
                            switch (data) {
                                case 'DS': return i18n.t('dmbill.txTypeDS');
                                case 'DC': return i18n.t('dmbill.txTypeDC');
                                case 'DH': return i18n.t('dmbill.txTypeDH');
                                case 'DZ': return i18n.t('dmbill.txTypeDZ');
                                case 'DX': return i18n.t('dmbill.txTypeDX');
                                case '01': return i18n.t('dmbill.txType01');
                                case '02': return i18n.t('dmbill.txType02');
                                case '03': return i18n.t('dmbill.txType03');
                                case '04': return i18n.t('dmbill.txType04');
                                case '05': return i18n.t('dmbill.txType05');
                                case '06': return i18n.t('dmbill.txType06');
                                case '07': return i18n.t('dmbill.txType07');
                                case '08': return i18n.t('dmbill.txType08');
                                default: return data || '--';
                            }
                        }
                    },
                    { data: 'orderId' },
                    {
                        data: 'status',
                        render: function (data) {
                            switch (data) {
                                case 'SUCCESS':
                                case 'S':
                                case 'S1':
                                    return '<span class="label label-primary">' + i18n.t('dmbill.statusSuccess') + '</span>';
                                case 'FAILED':
                                case 'F':
                                case 'R2':
                                    return '<span class="label label-danger">' + i18n.t('dmbill.statusFailed') + '</span>';
                                case 'W':
                                    return '<span class="label label-warning">' + i18n.t('dmbill.statusWaitPay') + '</span>';
                                case 'W1':
                                    return '<span class="label label-warning">' + i18n.t('dmbill.statusProcessing') + '</span>';
                                case 'P':
                                    return '<span class="label label-info">' + i18n.t('dmbill.statusPayConfirm') + '</span>';
                                case 'R1':
                                    return '<span class="label label-info">' + i18n.t('dmbill.statusPartialRefund') + '</span>';
                                case 'E':
                                    return '<span class="label label-danger">' + i18n.t('dmbill.statusTimeout') + '</span>';
                                case 'C':
                                    return '<span class="label label-default">' + i18n.t('dmbill.statusCancelled') + '</span>';
                                case 'W2':
                                    return '<span class="label label-warning">' + i18n.t('dmbill.statusPaymentProcessing') + '</span>';
                                case 'F2':
                                    return '<span class="label label-danger">' + i18n.t('dmbill.statusPaymentFailed') + '</span>';
                                case 'S2':
                                    return '<span class="label label-primary">' + i18n.t('dmbill.statusPaymentSuccess') + '</span>';
                                default:
                                    return data || '--';
                            }
                        }
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return '<button type="button" class="btn btn-xs btn-info view-detail" onclick="viewDetail(\'' + row.orderId + '\')"><i class="fa fa-search"></i> ' + i18n.t('dmbill.viewDetail') + '</button>';
                        }
                    }
                ]
            });
        }

        // 查询
        function search() {
            console.log("执行搜索操作");
            // 先隐藏处理中指示器（如果存在）
            $('.dataTables_processing').hide();
            // 刷新表格
            table.ajax.reload(function () {
                console.log("表格刷新完成");
                // 回调中再次确保处理中指示器消失
                $('.dataTables_processing').hide();
            }, false);
        }

        // 重置表单
        function resetForm() {
            $('#queryForm')[0].reset();
            // 先隐藏处理中指示器（如果存在）
            $('.dataTables_processing').hide();
            // 刷新表格
            table.ajax.reload(function () {
                // 回调中再次确保处理中指示器消失
                $('.dataTables_processing').hide();
            }, false);
        }

        // 查看详情
        function viewDetail(orderId) {
            $.ajax({
                url: '/bil/dmbill/detail',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    body: orderId
                }),
                success: function (res) {
                    if (res && res.body) {
                        var data = res.body;
                        $('#detail-txTime').text(data.txTime ? new Date(data.txTime).toLocaleString() : '--');
                        $('#detail-coinId').text(data.coinId || '--');
                        $('#detail-amount').text(data.amount || '--');
                        $('#detail-fee').text(data.fee || '--');
                        $('#detail-receiveAddress').text(data.receiveAddress || '--');
                        $('#detail-payAddress').text(data.payAddress || '--');
                        $('#detail-orderId').text(data.orderId || '--');
                        $('#detail-txType').text(formatTxType(data.txType) || '--');
                        $('#detail-txHash').text(data.txHash || '--');
                        $('#detail-status').text(formatOrderStatus(data.status) || '--');
                        $('#detail-memo').text(data.memo || '--');
                        $('#detail-channel').text(data.channel || '--');
                        $('#detailModal').modal('show');
                    } else {
                        toastr.error(i18n.t('dmbill.getDetailFailed'));
                    }
                },
                error: function () {
                    toastr.error(i18n.t('dmbill.getDetailFailed'));
                }
            });
        }

        // 格式化交易类型
        function formatTxType(txType) {
            switch (txType) {
                case 'DS': return i18n.t('dmbill.txTypeDS');
                case 'DC': return i18n.t('dmbill.txTypeDC');
                case 'DH': return i18n.t('dmbill.txTypeDH');
                case 'DZ': return i18n.t('dmbill.txTypeDZ');
                case 'DX': return i18n.t('dmbill.txTypeDX');
                case '01': return i18n.t('dmbill.txType01');
                case '02': return i18n.t('dmbill.txType02');
                case '03': return i18n.t('dmbill.txType03');
                case '04': return i18n.t('dmbill.txType04');
                case '05': return i18n.t('dmbill.txType05');
                case '06': return i18n.t('dmbill.txType06');
                case '07': return i18n.t('dmbill.txType07');
                case '08': return i18n.t('dmbill.txType08');
                default: return txType;
            }
        }

        // 格式化订单状态
        function formatOrderStatus(status) {
            switch (status) {
                case 'SUCCESS':
                case 'S':
                case 'S1':
                    return i18n.t('dmbill.statusSuccess');
                case 'FAILED':
                case 'F':
                case 'R2':
                    return i18n.t('dmbill.statusFailed');
                case 'W':
                    return i18n.t('dmbill.statusWaitPay');
                case 'W1':
                    return i18n.t('dmbill.statusProcessing');
                case 'P':
                    return i18n.t('dmbill.statusPayConfirm');
                case 'R1':
                    return i18n.t('dmbill.statusPartialRefund');
                case 'E':
                    return i18n.t('dmbill.statusTimeout');
                case 'C':
                    return i18n.t('dmbill.statusCancelled');
                case 'W2':
                    return i18n.t('dmbill.statusPaymentProcessing');
                case 'F2':
                    return i18n.t('dmbill.statusPaymentFailed');
                case 'S2':
                    return i18n.t('dmbill.statusPaymentSuccess');
                default:
                    return status;
            }
        }
    </script>
</body>

</html>