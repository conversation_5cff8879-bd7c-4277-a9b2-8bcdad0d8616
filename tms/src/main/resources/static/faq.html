<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>INSPINIA | FAQ</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="css/animate.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <div id="wrapper">
        <nav class="navbar-default navbar-static-side" role="navigation">
            <div class="sidebar-collapse">
                <ul class="nav metismenu" id="side-menu">
                    <li class="nav-header">
                        <div class="dropdown profile-element">
                            <span>
                                <img alt="image" class="img-circle" src="img/profile_small.jpg" />
                            </span>
                            <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                                <span class="clear">
                                    <span class="block m-t-xs">
                                        <strong class="font-bold"><PERSON></strong>
                                    </span>
                                    <span class="text-muted text-xs block">Art Director <b class="caret"></b></span>
                                </span>
                            </a>
                            <ul class="dropdown-menu animated fadeInRight m-t-xs">
                                <li><a href="profile.html">Profile</a></li>
                                <li><a href="contacts.html">Contacts</a></li>
                                <li><a href="mailbox.html">Mailbox</a></li>
                                <li class="divider"></li>
                                <li><a href="login.html">Logout</a></li>
                            </ul>
                        </div>
                        <div class="logo-element">
                            IN+
                        </div>
                    </li>
                    <!-- Navigation menu items -->
                    <li>
                        <a href="index.html"><i class="fa fa-th-large"></i> <span class="nav-label">Dashboards</span>
                            <span class="fa arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li><a href="index.html">Dashboard v.1</a></li>
                            <li><a href="dashboard_2.html">Dashboard v.2</a></li>
                            <li><a href="dashboard_3.html">Dashboard v.3</a></li>
                            <li><a href="dashboard_4_1.html">Dashboard v.4</a></li>
                            <li><a href="dashboard_5.html">Dashboard v.5 </a></li>
                        </ul>
                    </li>
                    <li class="active">
                        <a href="#"><i class="fa fa-desktop"></i> <span class="nav-label">App Views</span> <span
                                class="pull-right label label-primary">SPECIAL</span></a>
                        <ul class="nav nav-second-level">
                            <li><a href="contacts.html">Contacts</a></li>
                            <li><a href="profile.html">Profile</a></li>
                            <li class="active"><a href="faq.html">FAQ</a></li>
                            <li><a href="timeline.html">Timeline</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </nav>

        <div id="page-wrapper" class="gray-bg">
            <div class="row border-bottom">
                <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                    <div class="navbar-header">
                        <a class="navbar-minimalize minimalize-styl-2 btn btn-primary" href="#"><i
                                class="fa fa-bars"></i></a>
                        <form role="search" class="navbar-form-custom" action="search_results.html">
                            <div class="form-group">
                                <input type="text" placeholder="Search for something..." class="form-control"
                                    name="top-search" id="top-search">
                            </div>
                        </form>
                    </div>
                    <ul class="nav navbar-top-links navbar-right">
                        <li>
                            <span class="m-r-sm text-muted welcome-message">Welcome to INSPINIA+ Admin Theme.</span>
                        </li>
                        <li>
                            <a href="login.html">
                                <i class="fa fa-sign-out"></i> Log out
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-sm-4">
                    <h2>FAQ</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a href="index.html">Home</a>
                        </li>
                        <li class="active">
                            <strong>Frequently asked questions</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-sm-8">
                    <div class="title-action">
                        <a href="#" class="btn btn-primary btn-sm" id="btnAdd">Add question</a>
                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- Search Form -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group">
                                        <div class="col-sm-3">
                                            <label class="control-label" for="searchId">ID</label>
                                            <input name="id" id="searchId" class="form-control" value="" />
                                        </div>
                                        <div class="col-sm-3">
                                            <label class="control-label" for="searchQuestionContent">Question
                                                Content</label>
                                            <input name="questionContent" id="searchQuestionContent"
                                                class="form-control" value="" />
                                        </div>
                                        <div class="col-sm-3">
                                            <label class="control-label" for="searchLanguage">Language</label>
                                            <select name="language" id="searchLanguage" class="form-control">
                                                <option value="">All Languages</option>
                                                <option value="zh-CN">简体中文</option>
                                                <option value="zh-HK">繁體中文</option>
                                                <option value="en_US">English</option>
                                            </select>
                                        </div>
                                        <div class="col-sm-3">
                                            <label class="control-label">&nbsp;</label>
                                            <div>
                                                <button type="button" id="searchBtn" class="btn btn-primary"
                                                    onclick="search()">Search</button>
                                                <button type="button" id="resetBtn" class="btn btn-default"
                                                    onclick="resetForm()">Reset</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- FAQ List -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="wrapper wrapper-content animated fadeInRight">
                            <div class="ibox-content m-b-sm border-bottom">
                                <div class="text-center p-lg">
                                    <h2>If you don't find the answer to your question</h2>
                                    <span>add your question by selecting </span>
                                    <button title="Create new question" class="btn btn-primary btn-sm"
                                        id="btnAddQuestion">
                                        <i class="fa fa-plus"></i> <span class="bold">Add question</span>
                                    </button> button
                                </div>
                            </div>

                            <div id="faqList">
                                <!-- FAQ items will be dynamically loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer">
                <div class="pull-right">
                    10GB of <strong>250GB</strong> Free.
                </div>
                <div>
                    <strong>Copyright</strong> Example Company &copy; 2014-2017
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="faqModal" tabindex="-1" role="dialog" aria-labelledby="faqModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="faqModalLabel">Add FAQ</h4>
                </div>
                <div class="modal-body">
                    <form id="faqForm" class="form-horizontal">
                        <input type="hidden" id="id" name="id" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="questionContent">Question Content</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="questionContent" name="questionContent" rows="3"
                                    required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="answerContent">Answer Content</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="answerContent" name="answerContent" rows="5"
                                    required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="language">Language</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="language" name="language" required>
                                    <option value="">Select Language</option>
                                    <option value="zh-CN">简体中文</option>
                                    <option value="zh-HK">繁體中文</option>
                                    <option value="en_US">English</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="btnSave">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Detail Modal -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="detailModalLabel">FAQ Detail</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">Question Content</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailQuestionContent"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">Answer Content</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAnswerContent"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">Language</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailLanguage"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">Created By</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreatedBy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">Created Date</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreatedDate"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/jquery-2.1.1.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/plugins/metisMenu/jquery.metisMenu.js"></script>
    <script src="js/plugins/slimscroll/jquery.slimscroll.min.js"></script>
    <script src="js/inspinia.js"></script>
    <script src="js/plugins/pace/pace.min.js"></script>

    <script>
        var currentPage = 1;
        var pageSize = 10;
        var totalPages = 1;

        $(document).ready(function () {
            loadFAQList();

            // Add button click
            $("#btnAdd, #btnAddQuestion").click(function () {
                $("#faqModalLabel").text("Add FAQ");
                $("#faqForm")[0].reset();
                $("#id").val("");
                $("#faqModal").modal("show");
            });

            // Save button click
            $("#btnSave").click(function () {
                if (!validateForm()) {
                    return;
                }

                var formData = {
                    id: $("#id").val(),
                    questionContent: $("#questionContent").val(),
                    answerContent: $("#answerContent").val(),
                    language: $("#language").val()
                };

                var url = formData.id ? "/mkm/faq/modify/" + formData.id : "/mkm/faq/add";

                $.ajax({
                    url: url,
                    type: "POST",
                    contentType: "application/json",
                    data: JSON.stringify(formData),
                    success: function (response) {
                        if (response.result === "MKM00000" || response.success) {
                            alert("Operation successful!");
                            $("#faqModal").modal("hide");
                            loadFAQList();
                        } else {
                            alert("Operation failed: " + (response.message || response.result));
                        }
                    },
                    error: function (xhr, status, error) {
                        alert("Operation failed: " + error);
                    }
                });
            });
        });

        // Load FAQ list
        function loadFAQList() {
            var searchParams = {
                page: currentPage,
                size: pageSize,
                id: $("#searchId").val() || "",
                questionContent: $("#searchQuestionContent").val() || "",
                language: $("#searchLanguage").val() || ""
            };

            $.ajax({
                url: "/mkm/faq/findAll",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(searchParams),
                success: function (response) {
                    renderFAQList(response.data || response);
                    updatePagination(response.totalPages || 1);
                },
                error: function (xhr, status, error) {
                    console.error("Failed to load FAQ list:", error);
                    $("#faqList").html('<div class="alert alert-danger">Failed to load FAQ list</div>');
                }
            });
        }

        // Render FAQ list
        function renderFAQList(faqData) {
            var html = '';

            if (!faqData || faqData.length === 0) {
                html = '<div class="alert alert-info">No FAQ items found</div>';
            } else {
                faqData.forEach(function (faq, index) {
                    var languageDisplay = getLanguageDisplay(faq.language);
                    var createdDate = faq.createdDate ? new Date(faq.createdDate).toLocaleDateString() : '';

                    html += `
                        <div class="faq-item">
                            <div class="row">
                                <div class="col-md-7">
                                    <a data-toggle="collapse" href="#faq${faq.id}" class="faq-question">${faq.questionContent || ''}</a>
                                    <small>Added by <strong>${faq.createdBy || 'Unknown'}</strong> 
                                    <i class="fa fa-clock-o"></i> ${createdDate}</small>
                                </div>
                                <div class="col-md-3">
                                    <span class="small font-bold">${faq.createdBy || 'Unknown'}</span>
                                    <div class="tag-list">
                                        <span class="tag-item">${languageDisplay}</span>
                                    </div>
                                </div>
                                <div class="col-md-2 text-right">
                                    <button type="button" class="btn btn-xs btn-info" onclick="viewDetail(${faq.id})">Detail</button>
                                    <button type="button" class="btn btn-xs btn-primary" onclick="editFAQ(${faq.id})">Edit</button>
                                    <button type="button" class="btn btn-xs btn-danger" onclick="deleteFAQ(${faq.id})">Delete</button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div id="faq${faq.id}" class="panel-collapse collapse">
                                        <div class="faq-answer">
                                            <p>${faq.answerContent || ''}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }

            $("#faqList").html(html);
        }

        // Get language display text
        function getLanguageDisplay(language) {
            switch (language) {
                case 'zh-CN': return '简体中文';
                case 'zh-HK': return '繁體中文';
                case 'en_US': return 'English';
                default: return language || '';
            }
        }

        // View detail
        function viewDetail(id) {
            $.ajax({
                url: "/mkm/faq/getFAQ",
                type: "POST",
                data: { id: id },
                success: function (data) {
                    $("#detailQuestionContent").text(data.questionContent || '');
                    $("#detailAnswerContent").text(data.answerContent || '');
                    $("#detailLanguage").text(getLanguageDisplay(data.language));
                    $("#detailCreatedBy").text(data.createdBy || '');
                    $("#detailCreatedDate").text(data.createdDate ? new Date(data.createdDate).toLocaleString() : '');
                    $("#detailModal").modal("show");
                },
                error: function (xhr, status, error) {
                    alert("Failed to get FAQ details: " + error);
                }
            });
        }

        // Edit FAQ
        function editFAQ(id) {
            $.ajax({
                url: "/mkm/faq/getFAQ",
                type: "POST",
                data: { id: id },
                success: function (data) {
                    $("#faqModalLabel").text("Edit FAQ");
                    $("#id").val(data.id);
                    $("#questionContent").val(data.questionContent);
                    $("#answerContent").val(data.answerContent);
                    $("#language").val(data.language);
                    $("#faqModal").modal("show");
                },
                error: function (xhr, status, error) {
                    alert("Failed to get FAQ details: " + error);
                }
            });
        }

        // Delete FAQ
        function deleteFAQ(id) {
            if (confirm("Are you sure you want to delete this FAQ?")) {
                $.ajax({
                    url: "/mkm/faq/delete/" + id,
                    type: "DELETE",
                    success: function (response) {
                        if (response === "1" || response.success) {
                            alert("FAQ deleted successfully!");
                            loadFAQList();
                        } else {
                            alert("Failed to delete FAQ");
                        }
                    },
                    error: function (xhr, status, error) {
                        alert("Failed to delete FAQ: " + error);
                    }
                });
            }
        }

        // Search
        function search() {
            currentPage = 1;
            loadFAQList();
        }

        // Reset form
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }

        // Form validation
        function validateForm() {
            var questionContent = $("#questionContent").val().trim();
            var answerContent = $("#answerContent").val().trim();
            var language = $("#language").val();

            if (!questionContent) {
                alert("Question content is required");
                return false;
            }
            if (!answerContent) {
                alert("Answer content is required");
                return false;
            }
            if (!language) {
                alert("Language is required");
                return false;
            }
            return true;
        }

        // Update pagination (if needed)
        function updatePagination(total) {
            totalPages = total;
            // Add pagination controls here if needed
        }
    </script>
</body>

</html>