/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
    :root, :host {
        --font-sans: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
        'Noto Color Emoji';
        --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
        monospace;
        --color-red-400: oklch(70.4% 0.191 22.216);
        --color-green-400: oklch(79.2% 0.209 151.711);
        --color-gray-100: oklch(96.7% 0.003 264.542);
        --color-gray-300: oklch(87.2% 0.01 258.338);
        --color-gray-500: oklch(55.1% 0.027 264.364);
        --color-black: #000;
        --spacing: 1px;
        --font-weight-bold: 700;
        --default-font-family: var(--font-sans);
        --default-mono-font-family: var(--font-mono);
    }
}
@layer base {
    *, ::after, ::before, ::backdrop, ::file-selector-button {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        border: 0 solid;
    }

    html, :host {
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        tab-size: 4;
        font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji');
        font-feature-settings: var(--default-font-feature-settings, normal);
        font-variation-settings: var(--default-font-variation-settings, normal);
        -webkit-tap-highlight-color: transparent;
    }

    hr {
        height: 0;
        color: inherit;
        border-top-width: 1px;
    }

    abbr:where([title]) {
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
    }

    h1, h2, h3, h4, h5, h6 {
        font-size: inherit;
        font-weight: inherit;
    }

    a {
        color: inherit;
        -webkit-text-decoration: inherit;
        text-decoration: inherit;
    }

    b, strong {
        font-weight: bolder;
    }

    code, kbd, samp, pre {
        font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace);
        font-feature-settings: var(--default-mono-font-feature-settings, normal);
        font-variation-settings: var(--default-mono-font-variation-settings, normal);
        font-size: 1em;
    }

    small {
        font-size: 80%;
    }

    sub, sup {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
    }

    sub {
        bottom: -0.25em;
    }

    sup {
        top: -0.5em;
    }

    table {
        text-indent: 0;
        border-color: inherit;
        border-collapse: collapse;
    }

    :-moz-focusring {
        outline: auto;
    }

    progress {
        vertical-align: baseline;
    }

    summary {
        display: list-item;
    }

    ol, ul, menu {
        list-style: none;
    }

    img, svg, video, canvas, audio, iframe, embed, object {
        display: block;
        vertical-align: middle;
    }

    img, video {
        max-width: 100%;
        height: auto;
    }

    button, input, select, optgroup, textarea, ::file-selector-button {
        font: inherit;
        font-feature-settings: inherit;
        font-variation-settings: inherit;
        letter-spacing: inherit;
        color: inherit;
        border-radius: 0;
        background-color: transparent;
        opacity: 1;
    }

    :where(select:is([multiple], [size])) optgroup {
        font-weight: bolder;
    }

    :where(select:is([multiple], [size])) optgroup option {
        padding-inline-start: 20px;
    }

    ::file-selector-button {
        margin-inline-end: 4px;
    }

    ::placeholder {
        opacity: 1;
    }

    @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
        ::placeholder {
            color: currentcolor;
            @supports (color: color-mix(in lab, red, red)) {
                color: color-mix(in oklab, currentcolor 50%, transparent);
            }
        }
    }
    textarea {
        resize: vertical;
    }

    ::-webkit-search-decoration {
        -webkit-appearance: none;
    }

    ::-webkit-date-and-time-value {
        min-height: 1lh;
        text-align: inherit;
    }

    ::-webkit-datetime-edit {
        display: inline-flex;
    }

    ::-webkit-datetime-edit-fields-wrapper {
        padding: 0;
    }

    ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
        padding-block: 0;
    }

    :-moz-ui-invalid {
        box-shadow: none;
    }

    button, input:where([type='button'], [type='reset'], [type='submit']), ::file-selector-button {
        appearance: button;
    }

    ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
        height: auto;
    }

    [hidden]:where(:not([hidden='until-found'])) {
        display: none !important;
    }
}
@layer utilities {
    .visible {
        visibility: visible;
    }

    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
    }

    .fixed {
        position: fixed;
    }

    .static {
        position: static;
    }

    .container {
        width: 100%;
        @media (width >= 40rem) {
            max-width: 40rem;
        }
        @media (width >= 48rem) {
            max-width: 48rem;
        }
        @media (width >= 64rem) {
            max-width: 64rem;
        }
        @media (width >= 80rem) {
            max-width: 80rem;
        }
        @media (width >= 96rem) {
            max-width: 96rem;
        }
    }

    .mt-1 {
        margin-top: calc(var(--spacing) * 1);
    }

    .mr-1 {
        margin-right: calc(var(--spacing) * 1);
    }

    .mb-0\! {
        margin-bottom: calc(var(--spacing) * 0) !important;
    }

    .mb-2 {
        margin-bottom: calc(var(--spacing) * 2);
    }

    .mb-3 {
        margin-bottom: calc(var(--spacing) * 3);
    }

    .mb-10 {
        margin-bottom: calc(var(--spacing) * 10);
    }

    .ml-3 {
        margin-left: calc(var(--spacing) * 3);
    }

    .ml-5 {
        margin-left: calc(var(--spacing) * 5);
    }

    .line-clamp-2 {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }

    .block {
        display: block;
    }

    .flex {
        display: flex;
    }

    .grid {
        display: grid;
    }

    .hidden {
        display: none;
    }

    .table {
        display: table;
    }

    .h-100 {
        height: calc(var(--spacing) * 100);
    }

    .h-150 {
        height: calc(var(--spacing) * 150);
    }

    .h-200 {
        height: calc(var(--spacing) * 200);
    }

    .h-250 {
        height: calc(var(--spacing) * 250);
    }

    .h-300 {
        height: calc(var(--spacing) * 300);
    }

    .w-1\/3 {
        width: calc(1 / 3 * 100%);
    }

    .w-2\/3 {
        width: calc(2 / 3 * 100%);
    }

    .w-100 {
        width: calc(var(--spacing) * 100);
    }

    .w-300 {
        width: calc(var(--spacing) * 300);
    }

    .w-full {
        width: 100%;
    }

    .min-w-100 {
        min-width: calc(var(--spacing) * 100);
    }

    .grow-0\! {
        flex-grow: 0 !important;
    }

    .border-collapse {
        border-collapse: collapse;
    }

    .resize {
        resize: both;
    }

    .flex-col {
        flex-direction: column;
    }

    .items-center {
        align-items: center;
    }

    .justify-between {
        justify-content: space-between;
    }

    .justify-center {
        justify-content: center;
    }

    .gap-10 {
        gap: calc(var(--spacing) * 10);
    }

    .gap-20 {
        gap: calc(var(--spacing) * 20);
    }

    .rounded-\[10px\] {
        border-radius: 10px;
    }

    .border {
        border-style: var(--tw-border-style);
        border-width: 1px;
    }

    .border-b {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 1px;
    }

    .border-gray-300 {
        border-color: var(--color-gray-300);
    }

    .bg-\[\#F9F8FF\] {
        background-color: #F9F8FF;
    }

    .bg-gray-100 {
        background-color: var(--color-gray-100);
    }

    .bg-\[url\(\/img\/plunex\/bg\.png\)\] {
        background-image: url(/img/plunex/bg.png);
    }

    .object-cover {
        object-fit: cover;
    }

    .p-2 {
        padding: calc(var(--spacing) * 2);
    }

    .p-10 {
        padding: calc(var(--spacing) * 10);
    }

    .p-20 {
        padding: calc(var(--spacing) * 20);
    }

    .px-10 {
        padding-inline: calc(var(--spacing) * 10);
    }

    .py-15 {
        padding-block: calc(var(--spacing) * 15);
    }

    .text-center {
        text-align: center;
    }

    .text-\[12px\] {
        font-size: 12px;
    }

    .text-\[14px\] {
        font-size: 14px;
    }

    .text-\[16px\] {
        font-size: 16px;
    }

    .text-\[20px\] {
        font-size: 20px;
    }

    .text-\[24px\] {
        font-size: 24px;
    }

    .text-\[32px\] {
        font-size: 32px;
    }

    .font-bold {
        --tw-font-weight: var(--font-weight-bold);
        font-weight: var(--font-weight-bold);
    }

    .text-black {
        color: var(--color-black);
    }

    .text-gray-500 {
        color: var(--color-gray-500);
    }

    .text-green-400 {
        color: var(--color-green-400);
    }

    .text-red-400 {
        color: var(--color-red-400);
    }

    .blur {
        --tw-blur: blur(8px);
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
    }

    .\*\:border-b {
        :is(& > *) {
            border-bottom-style: var(--tw-border-style);
            border-bottom-width: 1px;
        }
    }

    .\*\:border-gray-300 {
        :is(& > *) {
            border-color: var(--color-gray-300);
        }
    }
}

@property --tw-border-style {
    syntax: '*';
    inherits: false;
    initial-value: solid;
}

@property --tw-font-weight {
    syntax: '*';
    inherits: false;
}

@property --tw-blur {
    syntax: '*';
    inherits: false;
}

@property --tw-brightness {
    syntax: '*';
    inherits: false;
}

@property --tw-contrast {
    syntax: '*';
    inherits: false;
}

@property --tw-grayscale {
    syntax: '*';
    inherits: false;
}

@property --tw-hue-rotate {
    syntax: '*';
    inherits: false;
}

@property --tw-invert {
    syntax: '*';
    inherits: false;
}

@property --tw-opacity {
    syntax: '*';
    inherits: false;
}

@property --tw-saturate {
    syntax: '*';
    inherits: false;
}

@property --tw-sepia {
    syntax: '*';
    inherits: false;
}

@property --tw-drop-shadow {
    syntax: '*';
    inherits: false;
}

@property --tw-drop-shadow-color {
    syntax: '*';
    inherits: false;
}

@property --tw-drop-shadow-alpha {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 100%;
}

@property --tw-drop-shadow-size {
    syntax: '*';
    inherits: false;
}

@layer properties {
    @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
        *, ::before, ::after, ::backdrop {
            --tw-border-style: solid;
            --tw-font-weight: initial;
            --tw-blur: initial;
            --tw-brightness: initial;
            --tw-contrast: initial;
            --tw-grayscale: initial;
            --tw-hue-rotate: initial;
            --tw-invert: initial;
            --tw-opacity: initial;
            --tw-saturate: initial;
            --tw-sepia: initial;
            --tw-drop-shadow: initial;
            --tw-drop-shadow-color: initial;
            --tw-drop-shadow-alpha: 100%;
            --tw-drop-shadow-size: initial;
        }
    }
}
