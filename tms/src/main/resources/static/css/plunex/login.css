/* Figma-based Login Page Styles */
body.login-page {
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    height: 100vh;
    overflow: hidden;
    background: url('/img/plunex/login-bg.png') no-repeat center center, 
                url('/img/plunex/login-bg.svg') no-repeat center center,
                linear-gradient(135deg, #8247E5 0%, #682FC7 100%);
    background-size: cover;
    position: relative;
}

/* Fallback background pattern if image doesn't load */
body.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.login-container {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 24px;
}

.login-card {
    background: #FFFFFF;
    border-radius: 6px;
    box-shadow: 0px 2px 10px 0px rgba(58, 53, 65, 0.1);
    padding: 48px 28px 36px;
    text-align: center;
    width: 100%;
    max-width: 450px;
    position: relative;
}

.logo-section {
    margin-bottom: 32px;
}

.logo-image {
    height: 36px;
    width: auto;
    max-width: 204px;
}

.welcome-text {
    margin-bottom: 24px;
    text-align: left;
}

.welcome-text h2 {
    font-size: 20px;
    font-weight: 500;
    line-height: 1.6;
    letter-spacing: 0.75%;
    color: rgba(58, 53, 65, 0.87);
    margin: 0 0 8px 0;
}

.welcome-text p {
    font-size: 14px;
    font-weight: 400;
    line-height: 1.43;
    letter-spacing: 1.07%;
    color: rgba(58, 53, 65, 0.68);
    margin: 0;
}

.login-form {
    text-align: left;
}

.form-group {
    margin-bottom: 16px;
    position: relative;
}

.input-wrapper {
    position: relative;
    border: 1px solid rgba(58, 53, 65, 0.23);
    border-radius: 6px;
    transition: border-color 0.2s ease;
}

.input-wrapper.focused {
    border-color: #9155FD;
}

.form-control {
    width: 100%;
    padding: 16px 12px 16px 17px;
    border: none;
    background: transparent;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    letter-spacing: 0.94%;
    color: rgba(58, 53, 65, 0.87);
    outline: none;
    border-radius: 6px;
    box-shadow: none;
}

.form-control::placeholder {
    color: rgba(58, 53, 65, 0.38);
    opacity: 1;
}

.form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

.input-label {
    position: absolute;
    top: 50%;
    left: 17px;
    transform: translateY(-50%);
    font-size: 16px;
    color: rgba(58, 53, 65, 0.38);
    pointer-events: none;
    transition: all 0.2s ease;
    background: white;
    padding: 0 4px;
}

.input-wrapper.focused .input-label,
.form-control:focus + .input-label {
    top: 0;
    font-size: 12px;
    color: #9155FD;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(58, 53, 65, 0.54);
    cursor: pointer;
    padding: 4px;
    font-size: 16px;
    z-index: 3;
}

.password-toggle:hover {
    color: rgba(58, 53, 65, 0.87);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.checkbox-input {
    display: none;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.43;
    letter-spacing: 1.07%;
    color: rgba(58, 53, 65, 0.87);
    gap: 8px;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 1px solid rgba(58, 53, 65, 0.68);
    border-radius: 2px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-label .checkbox-custom {
    background-color: #9155FD;
    border-color: #9155FD;
}

.checkbox-input:checked + .checkbox-label .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    font-size: 14px;
    font-weight: 400;
    line-height: 1.43;
    letter-spacing: 1.07%;
    color: #9155FD;
    text-decoration: none;
    text-align: right;
}

.forgot-password:hover {
    text-decoration: underline;
    color: #9155FD;
}

.login-button {
    width: 100%;
    padding: 8px 26px;
    background: #9155FD;
    border: none;
    border-radius: 5px;
    color: #FFFFFF;
    font-size: 15px;
    font-weight: 500;
    line-height: 1.73;
    letter-spacing: 3.07%;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0px 4px 8px -4px rgba(58, 53, 65, 0.42);
    margin-bottom: 16px;
}

.login-button:hover {
    background: #7c3aed;
    box-shadow: 0px 6px 12px -4px rgba(58, 53, 65, 0.5);
}

.login-button:active {
    transform: translateY(1px);
}

.error-message {
    padding: 12px;
    background: #fee2e2;
    border: 1px solid #fecaca;
    border-radius: 4px;
    color: #dc2626;
    font-size: 14px;
    margin-bottom: 16px;
}

.language-selector {
    position: absolute;
    top: 24px;
    right: 24px;
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    color: #FFFFFF;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
    min-width: 100px;
}

.language-toggle:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
}

.language-toggle i {
    font-size: 14px;
}

.current-language {
    flex: 1;
    text-align: left;
}

.language-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    z-index: 10;
    overflow: hidden;
}

.language-toggle.active .language-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    color: rgba(58, 53, 65, 0.87);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.language-option:hover {
    background-color: rgba(145, 85, 253, 0.1);
    color: #9155FD;
    text-decoration: none;
}

.language-option.active {
    background-color: #9155FD;
    color: white;
}

.language-option.active:hover {
    background-color: #7c3aed;
}

.language-option img {
    width: 18px;
    height: 14px;
    object-fit: cover;
    border-radius: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
    .login-container {
        padding: 16px;
    }
    
    .login-card {
        padding: 32px 20px 24px;
    }
    
    .welcome-text h2 {
        font-size: 18px;
    }
    
    .language-selector {
        top: 16px;
        right: 16px;
    }
    
    .language-toggle {
        padding: 6px 10px;
        font-size: 13px;
        min-width: 90px;
    }
}

@media (max-width: 480px) {
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .forgot-password {
        text-align: left;
    }
    
    .language-selector {
        top: 12px;
        right: 12px;
    }
    
    .language-toggle {
        padding: 5px 8px;
        font-size: 12px;
        min-width: 80px;
    }
    
    .language-option {
        padding: 8px 10px;
        font-size: 13px;
    }
}

/* Hide original styles */
.gray-bg {
    background: none !important;
}

.middle-box {
    position: static !important;
    width: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
}

.loginscreen {
    background: none !important;
}
