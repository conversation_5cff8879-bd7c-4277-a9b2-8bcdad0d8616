/* Plunex Top Navbar Styles */

/* 调试样式 - 确保CSS被加载 */
.plunex-top-navbar {
    position: relative;
}

.plunex-top-navbar::before {
    content: "Plunex CSS Loaded";
    position: absolute;
    top: -20px;
    right: 10px;
    font-size: 10px;
    color: #8247E5;
    display: none; /* 隐藏调试信息 */
}

/* 顶部导航栏样式 */
.plunex-top-navbar {
    background: #FFFFFF;
    border-bottom: 1px solid #F0F1F3;
    position: fixed !important;
    top: 0 !important;
    left: 264px !important;
    right: 0 !important;
    z-index: 999 !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* 确保导航栏链接对齐 */
.plunex-top-navbar .navbar-top-links {
    display: flex;
    align-items: center;
    height: 60px;
    margin: 0;
    padding: 0;
}

.plunex-top-navbar .navbar-top-links > li {
    display: flex;
    align-items: center;
    height: 60px;
}

.plunex-top-navbar .navbar-top-links > li > a {
    padding: 0;
    display: flex;
    align-items: center;
    height: 60px;
}

/* 覆盖原有的navbar-top-links hover样式 */
.plunex-top-navbar.navbar-static-top .nav.navbar-top-links > li > a:hover,
.plunex-top-navbar.navbar-static-top .nav.navbar-top-links > li > a:focus {
    background-color: initial !important;
}

/* 语言选择器样式 */
.plunex-language-selector {
    position: relative;
    display: inline-block;
    margin-right: 15px;
}

.plunex-language-toggle {
    display: flex;
    align-items: center;
    gap: 6px;
    background: transparent;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    min-width: 100px;
    font-size: 13px;
    color: #495057;
    height: 50px; /* 与导航栏高度一致 */
    border-radius: 0;
}

.plunex-language-toggle:hover {
    background: rgba(130, 71, 229, 0.08);
    color: #8247E5;
}

.plunex-language-toggle i {
    font-size: 12px;
    margin-left: auto;
}

.current-language {
    flex: 1;
    text-align: left;
    font-weight: normal;
}

.plunex-language-dropdown {
    position: absolute;
    top: calc(100% - 1px);
    left: 0;
    right: 0;
    background: white;
    border: 1px solid rgba(130, 71, 229, 0.2);
    border-top: none;
    box-shadow: 0 4px 12px rgba(130, 71, 229, 0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-5px);
    transition: all 0.2s ease;
    z-index: 1000;
    overflow: hidden;
    min-width: 120px;
    border-radius: 0 0 4px 4px;
}

.plunex-language-toggle.active .plunex-language-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.plunex-language-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    color: #495057;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 13px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    font-weight: normal;
}

.plunex-language-option:last-child {
    border-bottom: none;
}

.plunex-language-option:hover {
    background-color: rgba(130, 71, 229, 0.1);
    color: #8247E5;
    text-decoration: none;
}

.plunex-language-option.active {
    background-color: #8247E5;
    color: white;
    font-weight: 500;
}

.plunex-language-option.active:hover {
    background-color: #7239d3;
    color: white;
}

.plunex-language-option img {
    width: 16px;
    height: 12px;
    object-fit: cover;
    border-radius: 2px;
}

/* 语言选择器图标样式 */
.plunex-language-toggle img {
    width: 16px;
    height: 12px;
    object-fit: cover;
    border-radius: 2px;
}

/* 登出按钮样式 */
.plunex-top-navbar .navbar-top-links li .plunex-logout-link {
    color: #495057 !important;
    text-decoration: none !important;
    padding: 10px 15px !important;
    font-size: 13px !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    height: 50px !important; /* 与导航栏高度一致 */
    line-height: 1 !important;
    border-radius: 0 !important;
    background: transparent !important;
}

.plunex-top-navbar .navbar-top-links li .plunex-logout-link:hover,
.plunex-top-navbar .navbar-top-links li .plunex-logout-link:focus,
.plunex-top-navbar .nav.navbar-top-links > li > .plunex-logout-link:hover,
.plunex-top-navbar .nav.navbar-top-links > li > .plunex-logout-link:focus {
    color: #dc3545 !important;
    text-decoration: none !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.plunex-top-navbar .navbar-top-links li .plunex-logout-link i {
    margin-right: 5px !important;
}

/* 额外的注销按钮hover样式确保生效 */
a.plunex-logout-link:hover {
    color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
    text-decoration: none !important;
}

/* 最高优先级的注销按钮样式 */
.navbar-top-links .plunex-logout-link:hover {
    color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
    text-decoration: none !important;
}

/* 隐藏原有的侧边栏收缩功能 */
.navbar-minimalize,
.minimalize-styl-2 {
    display: none !important;
}

/* 调整页面布局，因为移除了侧边栏收缩功能 */
#page-wrapper {
    margin-left: 264px !important;
    padding-top: 60px !important;
}

body.mini-navbar #page-wrapper {
    margin-left: 264px !important;
    padding-top: 60px !important;
}

/* 确保侧边栏始终显示 */
.navbar-static-side {
    width: 264px !important;
}

body.mini-navbar .navbar-static-side {
    width: 264px !important;
}

body.mini-navbar .nav-label {
    display: inline !important;
}

body.mini-navbar .fa.arrow {
    display: inline !important;
}

/* 强制移除mini-navbar类的效果 */
body.mini-navbar .navbar-static-side .nav li a span.nav-label {
    display: inline !important;
}

body.mini-navbar .navbar-static-side .nav li a span.fa.arrow {
    display: inline !important;
}

/* 确保侧边栏内容始终可见 */
body.mini-navbar .nav-header {
    padding: 33px 25px !important;
}

body.mini-navbar .nav-header a {
    display: block !important;
}

body.mini-navbar .nav-header .dropdown-toggle {
    display: block !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .plunex-top-navbar {
        left: 0 !important;
    }

    #page-wrapper {
        margin-left: 0 !important;
        padding-top: 60px !important;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .plunex-navbar-container {
        flex-direction: column;
        height: auto;
        padding: 10px;
        gap: 10px;
    }
    
    .plunex-navbar-right {
        width: 100%;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    
    .plunex-timezone-selector,
    .plunex-country-selector {
        flex: 1;
        min-width: 120px;
    }
}

/* 隐藏原有的侧边栏收缩功能 */
.navbar-minimalize,
.minimalize-styl-2 {
    display: none !important;
}

/* 调整页面布局，因为移除了侧边栏收缩功能 */
#page-wrapper {
    margin-left: 220px !important;
}

body.mini-navbar #page-wrapper {
    margin-left: 220px !important;
}

/* 确保侧边栏始终显示 */
.navbar-static-side {
    width: 220px !important;
}

body.mini-navbar .navbar-static-side {
    width: 220px !important;
}

body.mini-navbar .nav-label {
    display: inline !important;
}

body.mini-navbar .fa.arrow {
    display: inline !important;
}

/* 强制移除mini-navbar类的效果 */
body.mini-navbar .navbar-static-side .nav li a span.nav-label {
    display: inline !important;
}

body.mini-navbar .navbar-static-side .nav li a span.fa.arrow {
    display: inline !important;
}

/* 确保侧边栏内容始终可见 */
body.mini-navbar .nav-header {
    padding: 33px 25px !important;
}

body.mini-navbar .nav-header a {
    display: block !important;
}

body.mini-navbar .nav-header .dropdown-toggle {
    display: block !important;
}

/* 修改页面加载进度条颜色为主题色 */
.pace .pace-progress {
    background: #8247E5 !important;
}

.landing-page.pace .pace-progress {
    background: #8247E5 !important;
}

/* 确保所有可能的进度条样式都使用主题色 */
.pace-running .pace-progress {
    background: #8247E5 !important;
}

.pace-inactive .pace-progress {
    background: #8247E5 !important;
}
