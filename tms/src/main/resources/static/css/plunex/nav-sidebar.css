/* Plunex Navigation Sidebar Styles - Based on Figma Design */

/* 主导航容器 */
.navbar-static-side {
    background: #FFF !important;
    border-right: 1px solid #F0F1F3 !important;
    width: 280px !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1000 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
}

/* 隐藏侧边栏加载状态 */
.navbar-static-side .loading,
.navbar-static-side .spinner,
.navbar-static-side .pace,
.navbar-static-side .pace-progress {
    display: none !important;
}

/* 确保侧边栏内容始终可见 */
.navbar-static-side #side-menu {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Logo容器 */
.plunex-logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 24px 20px;
    border-bottom: 1px solid #F0F1F3;
}

.plunex-logo {
    width: 204px;
    height: 36px;
    object-fit: contain;
}

/* 侧边栏折叠容器 */
.sidebar-collapse {
    padding: 0 !important;
    background: transparent !important;
}

/* 主菜单容器 */
#side-menu {
    background: transparent !important;
    margin: 0 !important;
    padding: 16px 0 !important;
    border: none !important;
}

/* 菜单项基础样式 */
#side-menu li {
    margin: 0 !important;
    border: none !important;
    background: transparent !important;
}

#side-menu li a {
    color: #4A4C56 !important;
    background: transparent !important;
    border: none !important;
    border-radius: 8px !important;
    margin: 0 24px 8px 24px !important;
    padding: 12px 24px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    line-height: 1.43 !important;
    letter-spacing: 0.5% !important;
    transition: all 0.15s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    text-decoration: none !important;
}

/* 菜单项图标 */
#side-menu li a i {
    color: #858D9D !important;
    font-size: 18px !important;
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 0 !important;
}

/* 菜单项文字 */
#side-menu li a .nav-label {
    color: #4A4C56 !important;
    flex: 1 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
}

/* 箭头图标 */
#side-menu li a .fa.arrow {
    color: #858D9D !important;
    font-size: 12px !important;
    margin-left: auto !important;
    transition: transform 0.15s ease !important;
}

/* 悬停效果 - 但不影响已激活的菜单项 */
#side-menu li:not(.menu-active) a:hover {
    background: rgba(130, 71, 229, 0.08) !important;
    color: #8247E5 !important;
    text-decoration: none !important;
}

#side-menu li:not(.menu-active) a:hover i,
#side-menu li:not(.menu-active) a:hover .nav-label,
#side-menu li:not(.menu-active) a:hover .fa.arrow {
    color: #8247E5 !important;
}

/* 激活状态的菜单项悬停时保持原样 */
#side-menu li.menu-active a:hover,
#side-menu li.menu-active a:hover i,
#side-menu li.menu-active a:hover .nav-label,
#side-menu li.menu-active a:hover .fa.arrow {
    background: #8247E5 !important;
    color: #FFF !important;
}

/* 菜单激活状态（当前选中的菜单项） */
#side-menu li.menu-active > a,
#side-menu li.menu-active > a:hover,
#side-menu li.menu-active > a:focus {
    background: #8247E5 !important;
    color: #FFF !important;
    font-weight: 700 !important;
}

#side-menu li.menu-active > a i,
#side-menu li.menu-active > a .nav-label,
#side-menu li.menu-active > a .fa.arrow {
    color: #FFF !important;
}

/* 二级菜单激活状态 */
#side-menu .nav-second-level li.menu-active > a,
#side-menu .nav-second-level li.menu-active > a:hover,
#side-menu .nav-second-level li.menu-active > a:focus {
    background: #8247E5 !important;
    color: #FFF !important;
    font-weight: 700 !important;
}

#side-menu .nav-second-level li.menu-active > a i,
#side-menu .nav-second-level li.menu-active > a .nav-label {
    color: #FFF !important;
}

/* 三级菜单激活状态 */
#side-menu .nav-third-level li.menu-active > a,
#side-menu .nav-third-level li.menu-active > a:hover,
#side-menu .nav-third-level li.menu-active > a:focus {
    background: #8247E5 !important;
    color: #FFF !important;
    font-weight: 700 !important;
}

#side-menu .nav-third-level li.menu-active > a i {
    color: #FFF !important;
}

/* 强制覆盖任何可能的冲突样式 */
#side-menu li:not(.menu-active) > a {
    background: transparent !important;
}

#side-menu li:not(.menu-active) > a:not(:hover) {
    color: #4A4C56 !important;
}

#side-menu li:not(.menu-active) > a:not(:hover) i {
    color: #858D9D !important;
}

#side-menu .nav-second-level li:not(.menu-active) > a:not(:hover) {
    color: #858D9D !important;
}

#side-menu .nav-third-level li:not(.menu-active) > a:not(:hover) {
    color: #B0B7C3 !important;
}

/* 展开状态（有子菜单的父级菜单） */
#side-menu li.active > a .fa.arrow {
    transform: rotate(90deg) !important;
    transition: transform 0.2s ease !important;
}

/* 收缩状态的箭头 */
#side-menu li:not(.active) > a .fa.arrow {
    transform: rotate(0deg) !important;
    transition: transform 0.2s ease !important;
}

/* 确保箭头图标的基础样式 */
#side-menu .fa.arrow {
    display: inline-block !important;
    font-family: FontAwesome !important;
    font-style: normal !important;
    font-weight: normal !important;
    line-height: 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

#side-menu .fa.arrow:before {
    content: '\f105' !important; /* fa-angle-right */
}

/* 二级菜单容器 */
#side-menu .nav-second-level {
    background: transparent !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    display: block !important;
    overflow: hidden !important;
    height: 0 !important;
    interpolate-size: allow-keywords !important;
    transition: height 0.2s ease-out !important;
}

/* 二级菜单项 */
#side-menu .nav-second-level li {
    margin: 0 !important;
    background: transparent !important;
}

#side-menu .nav-second-level li a {
    color: #858D9D !important;
    background: transparent !important;
    margin: 0 24px 4px 48px !important;
    padding: 8px 16px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

#side-menu .nav-second-level li a i {
    font-size: 14px !important;
    color: #858D9D !important;
}

#side-menu .nav-second-level li:not(.menu-active) a:hover {
    background: rgba(130, 71, 229, 0.05) !important;
    color: #8247E5 !important;
}

#side-menu .nav-second-level li:not(.menu-active) a:hover i {
    color: #8247E5 !important;
}

/* 二级菜单激活状态悬停保持原样 */
#side-menu .nav-second-level li.menu-active a:hover,
#side-menu .nav-second-level li.menu-active a:hover i,
#side-menu .nav-second-level li.menu-active a:hover .nav-label {
    background: #8247E5 !important;
    color: #FFF !important;
}

/* 移除原有的二级菜单active样式，使用menu-active代替 */

/* 三级菜单 */
#side-menu .nav-third-level {
    background: transparent !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block !important;
    overflow: hidden !important;
    height: 0 !important;
    interpolate-size: allow-keywords !important;
    transition: height 0.2s ease-out !important;
}

/* 三级菜单项 */
#side-menu .nav-third-level li {
    margin: 0 !important;
    background: transparent !important;
}

#side-menu .nav-third-level li a {
    margin: 0 24px 4px 72px !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
    color: #B0B7C3 !important;
    transition: all 0.2s ease !important;
}

#side-menu .nav-third-level li:not(.menu-active) a:hover {
    background: rgba(130, 71, 229, 0.03) !important;
    color: #8247E5 !important;
}

/* 三级菜单激活状态悬停保持原样 */
#side-menu .nav-third-level li.menu-active a:hover,
#side-menu .nav-third-level li.menu-active a:hover i {
    background: #8247E5 !important;
    color: #FFF !important;
}

/* 展开状态的菜单显示 - 使用height: auto */
nav.navbar-static-side #side-menu li.active > .nav-second-level,
#side-menu li.active > .nav-second-level {
    height: auto !important;
}

nav.navbar-static-side #side-menu .nav-second-level li.active > .nav-third-level,
#side-menu .nav-second-level li.active > .nav-third-level {
    height: auto !important;
}

/* 滚动条样式 */
.navbar-static-side::-webkit-scrollbar {
    width: 4px;
}

.navbar-static-side::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
}

.navbar-static-side::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.navbar-static-side::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* 移除原有的边框和阴影 */
.navbar-static-side,
.sidebar-collapse,
#side-menu,
#side-menu li,
#side-menu li a {
    box-shadow: none !important;
    border: none !important;
}

/* 确保在mini-navbar状态下也保持样式 */
body.mini-navbar .navbar-static-side {
    width: 280px !important;
}

body.mini-navbar #side-menu li a .nav-label {
    display: inline !important;
}

body.mini-navbar #side-menu li a .fa.arrow {
    display: inline !important;
}

/* 主内容区域调整 */
#page-wrapper {
    margin-left: 280px !important;
    background: #FFF !important;
    min-height: 100vh !important;
    padding-top: 60px !important;
}

body.mini-navbar #page-wrapper {
    margin-left: 280px !important;
    padding-top: 60px !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .navbar-static-side {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .navbar-static-side.show {
        transform: translateX(0);
    }

    #page-wrapper {
        margin-left: 0 !important;
    }
}

/* 强制确保侧边栏阴影显示 */
nav.navbar-static-side,
.navbar-default.navbar-static-side {
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
    -webkit-box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
    -moz-box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
}

/* 强制菜单展开规则 - 最高优先级 */
body nav.navbar-static-side #side-menu li.active > .nav-second-level {
    display: block !important;
    height: auto !important;
    interpolate-size: allow-keywords !important;
}

body nav.navbar-static-side #side-menu .nav-second-level li.active > .nav-third-level {
    display: block !important;
    height: auto !important;
    interpolate-size: allow-keywords !important;
}

/* 浏览器兼容性回退方案 */
@supports not (interpolate-size: allow-keywords) {
    #side-menu .nav-second-level {
        max-height: 0 !important;
        transition: max-height 0.2s ease-out !important;
    }

    #side-menu .nav-third-level {
        max-height: 0 !important;
        transition: max-height 0.2s ease-out !important;
    }

    #side-menu li.active > .nav-second-level {
        max-height: 800px !important;
    }

    #side-menu .nav-second-level li.active > .nav-third-level {
        max-height: 500px !important;
    }
}
