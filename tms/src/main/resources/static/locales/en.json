{"top": {"welcome": "Welcome to PlunexPay Admin,", "logout": "Log out"}, "nav": {"index": "Home", "system": "System management", "systemsub": {"user": "User management", "role": "Role management"}, "demo": "demo一级菜单", "demosub": {"example": "example二级菜单", "activiti": "activiti二级菜单"}, "mkm": "Marketing management", "activity": "Activity management", "releaseDetail": {"title": "Plunex | User marketing tools use detailed query", "content": "User marketing tools use detailed query"}, "consumeDetail": {"title": "Plunex | User marketing tools consumption details query", "content": "User marketing tools consumption details query"}, "examine": {"title": "Plunex | Audit of marketing activities", "content": "Audit of marketing activities"}, "mkmToolBatchMng": "Marketing tool batch processing", "example": "example二级菜单", "csh": "Cash register management", "paytype": "Payment method", "chk": "Reconciliation management", "controller": "Reconciliation details query", "error": "Reconciliation error management", "cptmgr": "Capital access", "parammgr": "Parameter management", "ordermgr": "Order management", "checkmgr": "Reconciliation management", "approvalmgr": "Financial review", "cardmgr": "Bank card management", "cardctrl": "Bank card query", "withdrawctrl": "Withdrawal transaction [preliminary review]", "withdrawWaitReview": "Withdrawal transaction [review]", "refundctrl": "Refund order inquiry", "fundctrl": "Recharge transaction [preliminary review]", "fundctrlWaitReview": "Recharge transaction [review]", "transfer": "Transfer transaction [preliminary review]", "transferWaitReview": "Transfer transaction [review]", "dcorder": "Management of fiat currency transactions", "Transaction": "Transaction Query", "callback": "Cregis callback record query", "dmwithdraw": "Payment in cryptocurrency [preliminary review]", "dmwithdrawWaitReview": "Payment in cryptocurrency [review]", "dmtransfer": "Digital currency transfer [preliminary review]", "dmtransferWaitReview": "Digital currency transfer [review]", "cpoorgmgr": "Capital outflow institution management", "cpiorgmgr": "Capital inflow institution management", "orginfo": {"title": "Plunex | Cooperative agency information", "content": "Cooperative agency information"}, "orgbusi": {"title": "Plunex | Cooperative agency business", "content": "Cooperative agency business"}, "orgroute": {"title": "Plunex | Cooperative mechanism routing", "content": "Cooperative mechanism routing"}, "cardbin": {"title": "Plunex | Card bin management", "constants": "Card bin management", "cmmOrgName": "Bank name", "cmmOrgNo": "Banking institution code", "cmmCardName": "Card name", "cmmBankCode": "Bank Code", "belongingState": "country", "ascriptionArea": "area", "cmmCardType": "Card species"}, "cmmmgr": "Public administration", "cmmsub": {"title": "Plunex | Public constant parameter maintenance", "constants": "Public constant parameter maintenance", "sysmgr": "System management", "userOpr": "Operation Log", "userOprSub": {"title": "Plunex | Operation Log", "phsmgr": "Operation Log"}, "usrmgr": "Teller management", "syssub": {"title": "Plunex | Mobile phone number section management", "phsmgr": "Mobile phone number section management"}, "orgmgr": "Institutional management", "orgsub": {"title": "Plunex | Branch management", "officemgr": "Branch management", "offsub": {"officeid": "branch number", "officenm": "branch name", "exitbra": "The department is exist", "deletefailed": "Failed to delete", "exitofficeid": "The branch number is exist", "addfailed": "Failed to delete"}, "branchmgr": "Departmental management", "brasub": {"title": "Plunex | Departmental management", "braid": "department number", "branm": "department name", "officenm": "attribution", "exituser": "The department is exist", "deletefailed": "Failed to delete", "exitbraid": "The branch number is exist", "addfailed": "Failed to add"}}}, "csmmgr": "Clear and settlement management", "csmsub": {"item": "Subject management", "itemsub": {"title": "Plunex | Subject management", "select": "-- Please choose --", "iteminf": "Account attribute management", "infsub": {"itmTyp": "Subject category", "itmTypsub": {"A": "<PERSON><PERSON>", "L": "Liabilities", "C": "Owners' equity", "I": "Income", "E": "Expenditure", "O": "Off-balance sheet", "S": "Account"}, "itmCls": "Subject classification", "itmClssub": {"1": "Deposit bank fund pool account", "2": "Error account/ dispute Account", "3": "Other Account"}, "itmSts": "Subject status", "itmStssub": {"0": "Effective", "1": "Invalid"}, "itmNo": "Account number", "itmCnm": "Account Chinese name", "itmEnm": "Account English name", "itmLvl": "Subject level", "itmLvlsub": {"1": "First-level subject", "2": "Secondary subject", "3": "Tertiary subject"}, "upItmNo": "Superior subject", "btmItmFlg": "Bottom subject flag", "btmItmFlgsub": {"Y": "Bottom", "N": "Not Bottom"}, "balOdFlg": "Overdraw flag", "balOdFlgsub": {"Y": "Yes", "N": "No"}, "balDrt": "Balance direction", "balDrtsub": {"A": "借贷双方反映", "B": "借贷轧差反映", "C": "贷方反映", "D": "借方反映"}, "updBalFlg": "Balance Update method", "updBalFlgsub": {"0": "Real time", "1": "<PERSON><PERSON>"}, "itmZbalFlg": "科目余额零标志 ", "itmZbalFlgsub": {"D": "科目的余额必须日终为零", "M": "科目的余额必须月终为零", "Y": "科目的余额必须年终为零", "N": "科目的余额不需要检查是否为零"}, "lpBfFlg": "损益结转标志 ", "lpBfFlgsub": {"Y": "需做损益结转", "N": "不需做损益结转"}, "effDt": "Effective date", "expDt": "Expiration date", "updOpr": "Operation"}, "itemproperty": {"title": "Plunex | Subject relationship management", "content": "Subject relationship management"}, "itempropertysub": {"capTyp": "Capital type ", "capTypsub": {"1": "Cash", "8": "Pending settlement capital"}, "CCY": "<PERSON><PERSON><PERSON><PERSON>"}, "button": {"query": "View", "detail": "Detail", "close": "Off"}, "message": {"null": {"itmNo": "The account number cannot be empty!", "effDt": "Effective date cannot be empty!", "expDt": "Expiration date cannot be empty!", "itmCnm": "Subject Chinese name cannot be empty!", "itmEnm": "Subject English name cannot be empty!", "upItmNo": "Superior subject cannot be empty!"}, "length": {"itmNo": "The upper account number is incorrect in length！", "upItmNo": "The upper account number is incorrect in length！"}, "err": {"itmNull": "Account attribute does not exist！", "proexit": "There is already a relationship in this subject！", "error": "Sorry，the system is busy，please try it again later！", "itmexist": "There is already a relationship in this subject！", "effDt": "Effective date is incorrect ！", "expDt": "Expiration date is incorrect ！", "itminfexit": "There is already a Subject in this subject！！", "uitminfnotexit": "The superior subject does not exist！"}}}, "adjust": "Manual transfer account", "adjustsub": {"title": "Plunex | Manual transfer account entry", "record": "Manual transfer account entry", "audit": "Manual transfer account review", "auditsub": {"title": "Plunex | Manual transfer account review", "handle": "Audit", "result": "View"}, "query": {"title": "Plunex | Manual transfer record query", "content": "Manual transfer record query"}}, "inneraccount": "Internal account management", "voucherquery": "Accounting voucher management"}, "disqueryctrl": {"title": "Plunex | Transaction clearing query", "content": "Transaction clearing query"}, "busmgr": "Business management", "bussub": {"usrmgr": "User management", "usrsub": {"userbasicinfoctrl": {"title": "Plunex | User data query", "content": "User data query"}, "userhistoryctrl": {"title": "Plunex | Open account history query", "content": "Open account history query"}}, "oprmgr": "Operation management", "usrBalance": "User balance query", "oprsub": {"bannermgr": {"title": "Plunex | Banner graph showing maintenance", "content": "Banner graph showing maintenance"}, "campaignmgr": {"title": "Plunex | Activity information maintenance", "content": "Activity information maintenance"}, "noticmgr": {"title": "Plunex | Bulletin information maintenance", "content": "Bulletin information maintenance"}, "smsmgr": {"title": "Plunex | SMS template management", "content": "SMS template management"}, "messagemgr": {"title": "Plunex | Message template management", "content": "Message template management"}}, "mermgr": "Registration Center", "mersub": {"mercpaypswdresetctrl": {"title": "Plunex | Merchant payment password reset", "content": "Merchant payment password reset"}, "amtInfo": {"title": "Plunex | Merchant income and expenditure details ", "content": "Merchant income and expenditure details"}, "mercitfInfoctrl": {"title": "Plunex | Merchant transaction privilege settings", "content": "Merchant transaction privilege settings"}, "mercloginpswdresetctrl": {"title": "Plunex | Merchant operator password reset", "content": "Merchant operator password reset"}, "merckeyresetctrl": {"title": "Plunex | Merchant key reset", "content": "Merchant key reset"}}, "trdmgr": "Transaction management", "trdsub": {"cpmquery": {"title": "Plunex | Withdrawal transaction [preliminary review]", "content": "Withdrawal transaction [preliminary review]"}, "orderqueryctrl": "Merchant order query", "onrrfdordctrl": {"title": "Plunex | Refund order inquiry", "content": "Refund order inquiry"}, "exchangeaudit": {"title": "Plunex | Exchange transaction [preliminary review]", "content": "Exchange transaction [preliminary review]", "dto": {"id": "Order ID", "orderNo": "Order Number", "userId": "User ID", "direction": "Exchange Direction", "directionType": {"S2F": "Crypto to Fiat", "F2S": "Fiat to Crypto", "F2F": "Fiat to Fiat"}, "fromCoin": "From Currency", "fromAmount": "From Amount", "toCoin": "To <PERSON><PERSON><PERSON><PERSON>", "toAmount": "To Amount", "feeCoin": "<PERSON><PERSON>", "feeAmount": "<PERSON><PERSON> Amount", "exchangeRate": "Exchange Rate", "fromUsdRate": "From Currency/USD Rate", "toUsdRate": "To Currency/USD Rate", "fromAmountUsd": "From Amount in USD", "toAmountUsd": "To Amount in USD", "status": "Status", "statusType": {"PENDING": "Pending", "FIRST_AUDIT": "First Audit", "SECOND_AUDIT": "Second Audit", "APPROVED": "Approved", "REJECTED": "Rejected", "SUCCESS": "Success", "FAILED": "Failed"}, "firstAuditUser": "First Auditor", "firstAuditTime": "First Audit Time", "firstAuditResult": "First Audit Result", "firstAuditOpinion": "First Audit Opinion", "secondAuditUser": "Second Auditor", "secondAuditTime": "Second Audit Time", "secondAuditResult": "Second Audit Result", "secondAuditOpinion": "Second Audit Opinion", "executeTime": "Execution Time", "rejectReason": "Rejection Reason", "createTime": "Create Time", "updateTime": "Update Time"}, "button": {"search": "Search", "detail": "Details", "firstAudit": "First Audit", "secondAudit": "Second Audit", "approve": "Approve", "reject": "Reject", "close": "Close", "submit": "Submit"}, "message": {"firstAuditSuccess": "First audit completed successfully", "secondAuditSuccess": "Second audit completed successfully", "auditFailed": "Audit operation failed", "confirmFirstAudit": "Are you sure to perform first audit on this exchange order?", "confirmSecondAudit": "Are you sure to perform second audit on this exchange order?", "confirmApprove": "Are you sure to approve this exchange order?", "confirmReject": "Are you sure to reject this exchange order?", "reasonRequired": "Reason is required when rejecting an order", "selectOrder": "Please select an exchange order first"}}}, "invmgr": "Financial management", "invsub": {"proinf": {"title": "Plunex | Current financial product management", "content": "Current financial product management"}, "regproinf": {"title": "Plunex | Regular financial product management", "content": "Regular financial product management"}, "datarate": {"title": "Plunex | Yield parameter maintenance", "content": "Yield parameter maintenance"}, "fee": {"title": "Plunex | Current financial income query", "content": "Current financial income query"}, "regfee": {"title": "Plunex | Regular financial income query", "content": "Regular financial income query"}, "order": {"title": "Plunex | Current financial transaction inquiry", "content": "Current financial transaction inquiry"}, "regorder": {"title": "Plunex | Regular financial transaction inquiry", "content": "Regular financial transaction inquiry"}}, "mkmmgr": "Marketing management"}, "rsm": "Wind control management", "rpt": "Report management"}, "footer": {"copyright": "<PERSON><PERSON> Gao Yang Tong Information Technology Co., Ltd"}, "index": {"title": "Plunex Portal | Dashboard"}, "login": {"title": "Plunex Portal | Login", "logo": "Plunex", "welcome": "Welcome to Plunex Portal ！", "username": "Please enter your username", "subtitle": "Please login to your account.", "password": "Please enter your password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "error": "Invalid username or password", "button": "<PERSON><PERSON>", "validation": {"required": "Please enter both username and password."}, "copyright": "<PERSON> Ⓒ 2017"}, "cmm": {"search": "Search", "ddl": {"choose": "--Please select--", "carrier": {"Mobile": "China Mobile", "Unicom": "China Unicom", "cellcard": "cellcard", "Smart": "Smart", "metfene": "metfene", "qb": "qb", "CooTel": "CooTel", "seatel": "Seatel"}, "country": {"china": "China", "cambodia": "Cambodia"}, "area": {"china": "China", "cambodia": "Cambodia"}, "smsType": {"common": "Generic", "register": "Registration", "login": "Landing", "resetpwd": "Reset the login password", "resetLoginPwd": "Reset payment password", "sign": "Sign", "pay": "Pay"}}, "label": {"title": "Theme", "date": "Date"}, "button": {"query": "Query", "add": "Add", "delete": "Delete", "modify": "Modify", "create": "Create", "update": "Update", "multi-delete": "Are you sure you want to delete %d rows?", "single-delete": "Are you sure you want to delete that line?"}, "action": {"cofirm": {"delete": "Are you sure you want to delete that line?"}}, "msg": {"banner": {"null": {"title": "Banner title can not be empty!", "bannerUrlKh": "Banner picture can not be empty!", "bannerUrlCn": "Banner Chinese picture can not be empty!", "bannerUrlEn": "Banner English picture can not be empty!", "detailUrlKh": "Banner Cambodian propaganda link can not be empty!", "detailUrlCn": "Banner Chinese promotion link can not be empty!", "detailUrlEn": "Banner English promotion link can not be empty!", "effDate": "Effective date can not be empty!", "expDate": "Expiration date can not be empty!"}, "length": {"title": "Banner title length can not exceed 64 characters!", "detailUrlKh": "Banner Cambodian propaganda link length can not exceed 256 characters!", "detailUrlCn": "Banner Chinese promotional link length can not exceed 256 characters!", "detailUrlEn": "Banner English promotional link length can not exceed 256 characters!"}}, "sms": {"null": {"id": "SMS encoding can not be empty!", "replaceField": "SMS replacement variable can not be empty!", "templateContentKh": "Cambodia template content can not be empty!", "templateContentCn": "Chinese template content can not be empty!", "templateContentEn": "English template content can not be empty!", "effDate": "Effective date can not be empty!", "expDate": "Expiration date can not be empty!"}, "length": {"id": "The encoding length can not exceed 8 characters!", "replaceField": "SMS replacement variable length can not exceed 256 characters!", "templateContentKh": "Cambodia template content length can not exceed 1024 characters!", "templateContentCn": "Chinese template content can not exceed 1024 characters!", "templateContentEn": "English template content can not exceed 1024 characters!"}}, "message": {"null": {"id": "Message encoding can not be empty!", "replaceField": "Message replacement variable can not be empty!", "templateTitleKh": "Cambodian message title can not be empty!", "templateTitleCn": "Chinese message title can not be empty!", "templateTitleEn": "English message title can not be empty!", "templateContentKh": "Cambodia template content can not be empty!", "templateContentCn": "Chinese template content can not be empty!", "templateContentEn": "English template content can not be empty!", "effDate": "Effective date can not be empty!", "expDate": "Expiration date can not be empty!"}, "length": {"id": "The encoding length can not exceed 8 characters!", "replaceField": "message replacement variable length can not exceed 256 characters!", "templateTitleKh": "Cambodian message header length can not exceed 64 characters!", "templateTitleCn": "Chinese message title length can not exceed 64 characters!", "templateTitleEn": "English message title length can not exceed 64 characters!", "templateContentKh": "Cambodia template content length can not exceed 1024 characters!", "templateContentCn": "Chinese template content can not exceed 1024 characters!", "templateContentEn": "English template content can not exceed 1024 characters!"}}, "campaign": {"null": {"campaignTitleKh": "Cambodian activity title can not be empty!", "campaignTitleCn": "Chinese activity title can not be empty!", "campaignTitleEn": "English activity title can not be empty!", "campaignContentKh": "Cambodian activities can not be empty!", "campaignContentCn": "Chinese activities can not be empty!", "campaignContentEn": "English activities can not be empty!", "effDate": "Effective date can not be empty!", "expDate": "Expiration date can not be empty!"}, "length": {"campaignTitleKh": "Cambodian activity title length can not exceed 64 characters!", "campaignTitleCn": "Chinese activity title length can not exceed 64 characters!", "campaignTitleEn": "English activity title length can not exceed 64 characters!", "campaignContentKh": "Cambodian activity content length can not exceed 1024 characters!", "campaignContentCn": "Chinese activity content length can not exceed 1024 characters!", "campaignContentEn": "English activity content can not exceed 1024 characters!"}}, "notice": {"null": {"noticeTitleKh": "Cambodian announcement title can not be empty!", "noticeTitleCn": "Chinese bulletin title can not be empty!", "noticeTitleEn": "English bulletin title can not be empty!", "noticeContentKh": "Cambodia bulletin content can not be empty!", "noticeContentCn": "Chinese announcement content can not be empty!", "noticeContentEn": "English announcement content can not be empty!", "effDate": "Effective date can not be empty!", "expDate": "Expiration date can not be empty!"}, "length": {"noticeTitleKh": "Cambodian announcement title length can not exceed 64 characters!", "noticTitleCn": "Chinese bulletin title length can not exceed 64 characters!", "noticeTitleEn": "English bulletin title length can not exceed 64 characters!", "noticeContentKh": "Cambodia bulletin content length can not exceed 1024 characters!", "noticeContentCn": "Chinese bulletin content length can not exceed 1024 characters!", "noticeContentEn": "The length of the English bulletin can not exceed 1024 characters!"}}, "phs": {"null": {"prefixNumber": "Phone number can not be empty!", "numberLength": "The length of the phone number can not be empty!", "carrier": "Attributable operators can not be empty!", "areaCode": "Country area can not be empty!", "country": "Attributable state can not be empty!", "province": "Attributable provinces can not be empty!", "city": "Attributable to the city can not be empty!", "effDate": "Effective date can not be empty!", "expDate": "Expiration date can not be empty!"}, "length": {"prefixNumber": "The length of the phone number can not exceed 16 characters!", "numberLength": "The length of the phone number can not exceed 20 characters!", "carrier": "Attributable operator length can not exceed 32 characters!", "areaCode": "Country zone length can not exceed 16 characters!", "country": "Attributable country length can not exceed 16 characters!", "province": "The length of the vested province can not exceed 16 characters!", "city": "Attributable city length can not exceed 16 characters!", "effDate": "Effective date can not be empty!", "expDate": "Expiration date can not be empty!"}}, "branch": {"null": {"braId": "Branch number is not be null", "braNm": "Branch name is not be null！"}}, "office": {"null": {"officeId": "Office number is not be null！", "officeNm": "Office name is not be null！"}}}, "statsEnum": {"online": "Online", "offline": "OOffline", "valid": "<PERSON><PERSON>", "invalid": "Invalid"}, "channelEnum": {"portal": "Official website", "app": "User app", "mportal": "Merchant official website", "mapp": "Merchant app"}, "userTypeEnum": {"common": "Generic", "personal": "Personal", "merchant": "Merchant"}, "trueEnum": {"choose": "--Please select--", "true": "Yes", "false": "No", "common": "Generic", "personal": "Personal", "merchant": "Merchant", "center": "Message center", "pay": "Payment request", "order": "Order result"}, "phonesegement": {"prefixNumber": "Phone number segment", "numberLength": "Phone number length", "areaCode": "Country code", "carrier": "Attributable operator", "country": "Country", "province": "Province", "city": "City", "effDate": "Effective date", "expDate": "Expiry date", "oprId": "Operator", "stats": "State", "tmSmp": "Modify time"}, "banner": {"bannerUrlKh": "Cambodian picture", "bannerUrlCn": "Chinese picture", "bannerUrlEn": "English picture", "title": "Theme", "detailUrlKh": "Cambodian text link", "detailUrlCn": "Chinese link", "detailUrlEn": "English link", "channel": "Channel", "effDate": "Effective date", "expDate": "Expiry date", "stats": "State", "oprId": "Operator", "tmSmp": "modify time"}, "campaign": {"title": "theme", "campaignTitleKh": "Cambodian theme", "campaignTitleCn": "Chinese theme", "campaignTitleEn": "English theme", "campaignContentKh": "Cambodian content", "campaignContentCn": "Chinese content", "campaignContentEn": "English content", "channel": "Channel", "effDate": "Effective date", "expDate": "Expiry date", "oprId": "Operator", "stats": "State", "tmSmp": "Modify time"}, "notice": {"title": "Theme", "noticeTitleKh": "Cambodian theme", "noticeTitleCn": "Chinese theme", "noticeTitleEn": "English theme", "noticeContentKh": "Cambodian content", "noticeContentCn": "Chinese content", "noticeContentEn": "English content", "channel": "channel", "effDate": "Effective date", "expDate": "Expiry date", "oprId": "Operator", "stats": "State", "tmSmp": "Modify time"}, "smsTempalte": {"id": "SMS coding", "type": "SMS type", "content": "Template content", "replaceField": "Replace variable", "templateContentKh": "Cambodian text template content", "templateContentCn": "Chinese template content", "templateContentEn": "English template content", "effDate": "Effective date", "expDate": "Expiry date", "oprId": "Operator", "stats": "State", "tmSmp": "Modify time"}, "messageTempalte": {"id": "Message code", "title": "Message title", "content": "Template content", "passPushFlg": "Transparent logo", "userType": "User type", "type": "Message type", "replaceField": "Replace variable", "templateTitleKh": "Cambodian message title", "templateTitleCn": "Chinese message title", "templateTitleEn": "English message title", "templateContentKh": "Cambodian text template content", "templateContentCn": "Chinese template content", "templateContentEn": "English template content", "effDate": "Effective date", "expDate": "Expiry date", "oprId": "Operator", "stats": "State", "tmSmp": "Modify time"}, "constant": {"addParam": "New", "editParam": "Maintenance", "deleteParam": "Delete", "parmNm": "Constant name", "effDt": "Effective time", "expDt": "Expiry time", "effFlg": "Effective sign", "parmDispNm": "Constant description", "parmCls": "Constant belongs to the module", "parmVal": "Constant value", "rmk": "Remarks", "effective": "Effective", "expired": "Expired"}}, "manrecinput": {"submitBtn": "Submit", "allBwAmt": "Total debit amount:", "allloanAmt": "total credit amount:", "allSum": "pen count:", "digest": "Summary:", "checkAmt": "Check the amount of balance", "queryBtn": "Query", "tableTh": {"number": "Set number", "acType": "Account type", "amtType": "Fund type", "acnum": "Account", "usernm": "Account name", "realAmt": "Balance", "loaned": "Borrowing", "amt": "Amount", "notices": "Remarks"}, "acType": {"payone": "User account", "paytwo": "Account account"}, "amtType": {"cash": "Cash", "beway": "Pending settlement"}, "loaned": {"borrow": "Borrow", "lend": "Credit"}}, "user": {"title": "Plunex | User management", "head": "User management", "sysmanage": "System management", "usermanage": "User management", "password": "Password", "userid": "UserId", "add": "New", "username": "Username", "status": "Status", "email": "Email", "mblNo": "Phone", "officeNm": "Attribution", "braNm": "Department", "placeholder": {"email": "Enter the Email", "mblNo": "Enter phone number", "password": "Password", "username": "UserName", "again": "Lose again"}, "switch": "Switch status", "modify": "Modify", "assign": "Assign role", "delete": "Delete", "save": "Save", "nonSelectedListLabel": "Unassigned", "selectedListLabel": "Assigned", "active_on": "Open", "active_off": "Off", "swal-title": "ok delete ?", "swal-confirm": "OK", "swal-cancel": "Cancel", "swal-sucess": "Success delete", "swal-error": "Failed delete", "repeatUserName": "User already exists, please re-enter", "swal-error-tips": "Please try it again"}, "role": {"title": "Plunex | Role management", "head": "Role management", "sysmanage": "System management", "rolemanage": "Role management", "add": "New", "edit": "Modify", "search": "Search", "roleId": "Role Id", "role": "Role type", "rolename": "Role name", "officeid": "Office", "branchid": "Department", "status": "Status", "assign": "Allocate resources", "delete": "Delete", "modal": "Allocate resources for roles", "close": "Close", "save": "Safe", "normal": "Normal", "lapse": "Invalid", "addrole": "Add Role", "editrole": "Modify Role", "swal-title": "ok delete?", "swal-confirm": "OK", "swal-cancel": "Cancel", "swal-sucess": "success delete", "swal-error": "Failed delete", "swal-error-tips": "Please try it again", "constant": {"all": "Please choose"}, "placeholder": {"role": "Enter role, such as ROLE_AMDIN, ROLE_USER", "rolename": "Enter character description"}}, "demo": {"title": "Plunex | demo", "head": "example二级菜单 h2标题", "firstLevel": "demo一级菜单", "secondLevel": "example二级菜单", "username": "姓名", "position": "位置", "office": "公司", "startDate": "时间", "salary": "薪水", "add": "新增", "create": "创建", "modify": "修改", "update": "更新", "delete": "删除", "multi-delete": "你确定要删除%d行?", "single-delete": "你确定要删除该行？"}, "activiti": {"title": "Plunex | activiti", "head": "activiti二级菜单 h2标题", "firstLevel": "demo一级菜单", "secondLevel": "activiti二级菜单", "status": "状态:"}, "activity": {"title": "Plunex | Activity management", "toolTitle": "Plunex | Marketing tools batch", "head": "Activity management", "sysmanage": "Marketing management", "activitymanage": "Activity management", "toolmanage": "Marketing tools batch", "id": "Id", "atvNm": "Activity name", "mkTool": "Marketing tools", "tatol": "total amount issued", "tatolAmt": "the total amount paid", "amt": "single coupon amount", "receiveTimes": "Number of collections", "receiveCycle": "Calculate the collection cycle", "receiveCycleOption": {"N": "Unlimited", "day": "Daily", "month": "per month", "year": "Per year"}, "startTm": "AActivity start time", "endTm": "Event end time", "add": "New", "modify": "Modify", "delete": "Delete", "search": "Search", "examine": "Audit", "success": "Successful operation", "eleCoupon": "Electronic coupons", "seaCyy": "Coin", "coupon": "Coupon", "discount": "Discount coupons", "orther": "Other", "disc": "Discount", "dis": "Fold", "save": "Submit", "item": "Subject number", "couponInvalTm": "Expiry time", "couponValDays": "Term of validity", "couponName": "Coupon name", "userAppoint": "Designated user", "costSide": "Cost bearer", "couponValTm": "Effective time", "couponValTmRemark": {"head": "Since Collected", "unit": "day"}, "instId": "Merchant number", "minAmt": "Minimum amount of order", "maxAmt": "Maximum amount of order", "multi-delete": "Are you sure you want to delete %d rows?", "releaseNum": "Collar number", "releaseAmt": "Total amount paid", "aclt": "Cumulative total amount", "acltAmt": "Accrued total amount", "statusN": "Status", "all": "All", "N": "Normal", "E": "Abnormal", "F": "Freeze", "S": "Pause", "TE": "Book transaction exception", "TF": "Accounting transaction failed", "mobile": "Phone number", "releaseTm": "Take time", "releaseDt": "Take date", "useredNum": "Used quantity", "overdueNum": "Expired quantity", "sUser": "Not used", "sUnUser": "Used", "sOverDue": "Expired", "sFreeze": "Freeze", "sActivity": "To be activated", "sRefund": "Refund", "sRevoked": "Revoked", "orderNo": "Order NO", "orderAmt": "Order amount", "userNm": "Use quantity", "userDt": "Use date", "userTm": "Use time", "sBatchInVal": "Batch failure", "addConfirmTitle": "add confirmation", "modifyConfirmTitle": "modify confirmation", "deleteConfirmTitle": "delete confirmation", "addConfirmText": "Confirm add?", "modifyConfirmText": "Confirm modify?", "deleteConfirmText": "Confirm delete?", "confirm": "Confirm", "cancel": "Cancel", "swal-sucess": "Successful operation", "swal-fail": "Operation failed", "detailNull": "Marketing activity does not exist", "stop": "Pause", "recover": "Recover", "examimeStatus": "Examination status", "examimeNo": "To be Examination", "examimePass": "Through the audit", "examimeNoPass": "<PERSON><PERSON> does not pass", "shouldSelectOne": "Please select a marketing campaign first!", "single-delete": "Are you sure you want to delete that line?", "fileUploadSuccess": "File upload succeeded", "fileUploadfail": "File upload failed", "examineReson": "Examine reason", "batch": {"recNo": "Batch serial number", "batchFile": "Batch file name", "processDt": "Processing time", "totNum": "Total transactions", "totAmt": "Total transaction amount", "successNum": "Number of successful treatments", "successAmt": "Success amount", "failureNum": "Number of processing failures", "failureAmt": "Failure amount", "oprTyp": "Operation type", "updOprId": "Operator id", "createTime": "Creation time", "processSts": "Processing status", "delay": "Delay", "overdue": "Expired", "freeze": "Freeze", "unfreeze": "<PERSON>haw", "downloadResultFile": "Download the return file", "downloadBatchFile": "Download the batch file", "uploadDelayFile": "Deferred batch file", "uploadBatchFile": "Upload batch file", "uploadOverdueFile": "Expired batch file", "uploadFreezeFile": "Freeze batch files", "uploadUnfreezeFile": "Thaw batch files", "selected": "--please choose--", "selectedOprType": "Please select the type of operation", "modifyTime": "Modify time"}, "check": {"atvNmIsNull": "Activity name cannot be empty", "repeat": "repeated submit", "mkToolIsNull": "Marketing tools can't be empty", "beginTimeIsNull": "Activity start time cannot be empty", "endTimeIsNull": "Activity end time cannot be empty", "totalIsNull": "The total amount of activity issued cannot be empty", "totalAmtIsNull": "The total amount of activity is not empty", "receiveTimesIsNull": "Single user pickup cannot be empty", "costSideIsNull": "The issue cost side cannot be empty", "couponInvalTmIsNull": "The expiration time of the voucher cannot be empty.", "discountTmIsNull": "When the marketing tool is a discount coupon, the discount cannot be empty", "amtIsNull": "When the marketing tool is an electronic coupon or coupon, the amount of the coupon cannot be empty.", "startDaysIsNull": "The number of days to take effect cannot be empty", "couponValDaysIsNull": "Effective days cannot be empty", "couponNameIsNull": "Voucher name cannot be empty", "minAmtIsNull": "The minimum amount of the order cannot be empty", "maxAmtIsNull": "The maximum amount of the order cannot be empty", "amtMoreThanMinAmt": "The single coupon amount cannot be greater than the order minimum amount", "amtNoEquelsTolAmt": "The total amount sent in the activity, the amount of the single ticket, the total amount of the distribution is incorrectly configured.", "discountTmFormat": "Incorrect discount format"}}, "inv": {"message": {"proIdNull": "financial product ID can not be empty", "dtNull": "The date of submission can not be empty", "rateNull": "Interest rate can not be empty", "update": {"success": "Success update"}, "proinfo": {"null": {"proId": "financial product ID is empty!", "rate": "interest rate is empty!", "collectAmt": "Total raise amount is empty!", "collectCnt": "The total number of recruits is empty!", "invTerm": "The investment term is empty!", "proName": "financial product name is empty!", "totalAmt": "The amount raised has been empty!", "investAmt": "The amount of the investment is empty！", "subscriptRate": "The redemption rate for the subscription period is empty.！", "cloPerRate": "Closed period redemption rate is empty！"}, "err": {"proExpTm": "financial products off the shelf time is wrong!", "rate": "Interest rate input is incorrect!", "collectAmt": "Total raise amount is empty!", "collectCnt": "The total number of recruits is empty!", "invTerm": "The investment period is wrong!", "totalAmt": "The amount has been raised!", "investAmt": "Wrong amount！", "subscriptRate": "The redemption rate in the subscription period is incorrect.！", "cloPerRate": "The redemption rate in the closed period is incorrect.！", "cloPerBegin": "The start time of the closure period is incorrect！", "cloPerEnd": "The closing time of the closure period is incorrect！", "earnTm": "Incorrect time of return！"}, "length": {"proId": "financial product ID length is wrong!", "proName": "Financial product name length is wrong!"}}}, "subscriptRateRadio": "Support subscription period redemption", "cloPerRateRadio": "Support closed period redemption", "txType": "transaction type", "select": "--- Please choose ---", "tradeDate": "Date", "search": "Query", "mobileNo": "Phone number", "proId": "Financial product number", "rate": "Interest rate", "view": "View", "close": "Close", "accday": "Daily interest", "accmonth": "Monthly interest", "accseason": "Quarterly interest", "accyear": "Annual interest (associated with interest rates)", "invId": "User management id", "buyDt": "Financial buy date", "suminvAmt": "Last day balance", "sumfeeAmt": "Issue interest on the same day", "invGrantDt": "Revenue date (D-1)", "invOrderNo": "Order number", "invJrnNo": "Order serial number", "invOrderDt": "Order date", "invOrderTm": "Order time", "userId": "Internal user number", "proRate": "Product rate", "totalFeeAmt": "Total interest payments", "ccy": "<PERSON><PERSON><PERSON><PERSON>", "orderAmt": "Order amount", "orderType": "Order type", "orderSts": "Order Status", "orderStsSub": {"S": "Success", "F": "Fail"}, "buy": "Buy", "voluntaryRollOut": "Self-transfer", "redemption": "Due redemption", "add": "New", "modify": "Modify", "delete": "Delete", "update": "Update", "create": "Create", "proType": "Interest type", "totalAmt": "Total amount raised", "collectAmt": "Raised amount", "collectCnt": "Raised number", "invTerm": "product life", "proEffTm": "Financial products shelves time", "proExpTm": "Financial products off the shelf time", "proDesc": "Product description", "riskDesc": "Risk description", "rulesDesc": "Trading Rules", "cloPerBegin": "Closure period start time", "cloPerEnd": "End of closure period", "subscriptRate": "Subscription redemption rate", "cloPerRate": "Closed period redemption rate", "investAmt": "Starting amount", "earnTm": "Income date", "rateDate": "Date", "proName": "Financial product name", "createUserId": "Operator", "dayRate": "Day interest rate", "demBal": "The balance of the last day", "daySettle": "Settlement of interest on the same day", "curDayRate": "Current rate", "proYearRate": "Expected annualized rate of return", "filDate": "Submit date", "deleteFail": "Failed to delete", "proEffTmErr": "The wealth management product has been put on the shelves", "multi-delete": "Are you sure you want to delete %d rows?", "single-delete": "Are you sure you want to delete that line?"}, "cpm": {"select": "--- Please choose ---", "tradeDate": "Date", "TEL": "Charge charges", "FLOW": "Fill flow", "search": "Query", "mblNo": "Payment number", "userNo": "Payment mobile phone number", "cpmOrderDt": "Payment date", "cpmOrderNo": "Payment order number", "bossCopType": "Payment unit", "orderType": "Payment business type", "orderAmt": "Payment amount", "orderSts": "Payment status", "orderStsSub": {"U": "pre-registration", "W": "Payment processing", "P": "Payment processing", "S": "Payment success", "F": "Payment failed", "T": "Payment overtime", "D": "Refund success"}, "payType": "Payment method", "payAmt": "Payment amount", "add": "New", "create": "Create", "modify": "Modify", "update": "Update", "delete": "Delete", "multi-delete": "Are you sure you want to delete %d rows?", "single-delete": "Are you sure you want to delete that line?"}, "onr": {"select": "--- Please choose ---", "search": "Query", "cshOrderNo": "Order number", "mercId": "Merchant number", "userId": "User number", "onrRfdNo": "Receipt Refund Order Number", "rfdAmt": "Refund amount", "cshRfdDt": "Refund date", "cshOrderDt": "Order date", "orderAmt": "Order amount", "payAmt": "Payment amount", "refAmt": "Refund amount", "ccy": "<PERSON><PERSON><PERSON><PERSON>", "paySeacoupon": "Coin", "couponType": "Offer type", "coupon": {"00": "No discount", "01": "Electronic coupons", "02": "Coin", "03": "Discount coupon", "04": "Coupon"}, "couponAmt": "Discounted price", "orderStat": "Order status", "statsub": {"U": "Order pre-registration", "W": "Order to be paid", "P": "Payment processing", "S": "Pay success", "F": "Payment failed", "PR": "Refund processing", "RB": "Partial refund successful", "R": "Refund successful", "PZ": "Withdrawal of single processing", "Z": "With<PERSON>wal of a single success", "D": "Order expired"}, "orderDesc": "Order description", "refund": "refund processing", "refundSuc": "refund processing success", "rfdRmk": "Reasons for Refund", "operate": "operation", "save": "refund", "needRfdAmt": "Refund amount", "refRmk": "Refund Remarks", "refundetail": "Refund details", "rfdStat": "Refund Status", "rfdStatSub": {"U": "initial", "P": "Refund processing", "F": "failure", "S": "successful", "T": "Refund notification timeout", "D": "Refund failed to end"}}, "csh": {"title": "Cash register management", "pttitle": "Payment way view", "busType-init-fail": "Service type initialization failed", "payType-init-fail": "Payment type initialization failed", "button": {"add": "new", "del": "disabled", "modify": "modify", "close": "off", "query": "query"}, "operator": {"create": "Create", "update": "Modify", "delete": "Update", "lookDetail": "View all", "multi-delete": "Are you sure you want to update% d configuration?", "single-delete": "Are you sure you want to update the configuration?"}, "paytype": {"title": "Plunex | Payment management", "content": "Payment management", "id": "ID", "mercId": "Merchant ID", "busType": "Business type", "appCnl": "Payment application", "payTypes": "Account payment type", "gwPayTypes": "Gateway payment type", "status": "Status", "lastmodifiedTime": "Last modified time", "modifyOpr": "Last modifier"}}, "chk": {"title": "Plunex | Reconciliation management", "head": "Reconciliation management", "controllerLevel": {"title": "Plunex | Reconciliation detail query", "content": "Reconciliation detail query"}, "errorLevel": {"title": "Plunex | Reconciliation error query", "content": "Reconciliation error query"}, "swal-fail": "Operation failed", "swal-confirm": "OK", "swal-cancel": "Cancel", "swal-sucess": "Successful operation", "shouldSelectOne": "Please select a record to be modified!", "chkFilStsNull": "Reconciliation status can not be empty", "updConfirmTitle": "Confirm", "updConfirmText": "Confirm changes?", "updHandleText": "Confirm processing?", "updateSuss": "Successfully modified", "updateFail": "Failed modified", "systemException": "System exception", "chkBusTyp": "Business type", "mainNo": "Main body", "oppoNo": "The other party", "selectByNull": "- Please select -", "capICBC": "Industrial and Commercial Bank", "capBESTPAY": "Wing payment", "capCPI": "Capital inflows", "capCSH": "Cashier", "capPWM": "Fill", "capHALL": "Business hall", "capTAM": "Transfer", "capCPM": "Payment", "capONR": "Receipt", "capCPO": "Capital outflow", "capCSM": "Clear settlement", "capINV": "Financial management", "fastpayChk": "Fast recharge reconciliation", "ebankChk": "Online banking recharge reconciliation,", "refundChk": "Refund reconciliation", "fundChk": "Recharge reconciliation", "invChk": "Financial reconciliation", "tamChk": "Transfer reconciliation", "withdrawChk": "Mention reconciliation", "chkFilSts0": "Not reconciled", "chkFilSts1": "Main party reconciliation file download", "chkFilSts2": "Opponent reconciliation file download", "chkFilSts3": "The main document details storage", "chkFilSts4": "Opponent file details storage", "chkFilSts5": "End of reconciliation", "errSts0": "Pending", "errSts1": "Has been paid", "errSts2": "Has been withdrawn", "errSts3": "Manual cancellation", "errSts4": "System error", "chkErrTyp2": "Short paragraph error", "chkErrTyp3": "Long paragraph error", "chkErrTyp4": "The amount does not match", "modify": "Modify", "search": "Query", "close": "Off", "oprId": "Operator Id", "rmk": "Remarks", "errorHandle": "Error handling", "forbidAdd": "Not allowed to fill orders!", "forbidCancel": "Not allowed to withdraw!", "controller": {"name": "Username", "mainRcvDt": "Reconciliation file date", "mainTotCnt": "The total number of reconciliation,", "mainTotAmt": "The total amount of reconciliation", "chkFilSts": "Reconciliation status", "chkEndTm": "Reconciliation completion time", "totMchCnt": "On the number of flat", "totMchAmt": "Flat amount", "longAmt": "Long money", "longCnt": "Long pen number", "shortAmt": "Short amount", "shortCnt": "Short pens", "doubtAmt": "Doubt amount", "doubtCnt": "Doubt the number of words", "chkFilDt": "Reconciliation file date", "updateSts": "Modify reconciliation status", "confirmUpdate": "Confirm changes"}, "error": {"name": "Username", "modify": "Modify", "update": "Modify", "chkErrTyp": "Error type", "errKeyId": "Error key value", "chkErrDt": "Error generation date", "chkErrTm": "Error generation time", "errSts": "Error state", "mainTxAmt": "Main party amount", "oppoTxAmt": "The amount of the other party", "additionalOrder": "Pay orders", "cancelOrder": "Cancel single processing", "cancelError": "Error cancel"}}, "CCY": {"CNY": "RMB", "USD": "Dollar", "KHR": "Cambodian Riel"}, "cpo": {"title": "Plunex | capital outflow", "title1": "Plunex | Withdrawal transaction [review]", "head": "Withdrawal transaction [preliminary review]", "head1": "Withdrawal transaction [review]", "firstLevel": "Capital access", "secondLevelOrderMgr": "Order management", "mblNo": "Phone number", "userId": "User / Merchant number", "userNm": "User / Merchant name", "agrPayDt": "Payment date", "corpBusTyp": "Payment type", "corpBusSubTyp": "Business type", "rutCorg": "Payment bank", "wcAplAmt": "Payment amount", "capCorg": "Collection bank", "capCrdNm": "Receiving account name", "subBranch": "Collection bank branch", "crdNo": "Collection card number", "reqOrdNo": "Request order number", "wcOrdNo": "Payment order number", "rutCorgJrn": "Bank serial number", "ordSts": "Order status", "ordDt": "Create date", "ordTm": "Create time", "ordDtTm": "Submit time", "postDt": "Billing date", "orgRspMsg": "Handling reason", "fastpay": "Quick payment", "fastpayConsume": "Fast consumption", "ebankpay": "Online banking payment", "ebankpayConsume": "Online banking consumption", "ebankpayMercScan": "Merchant scanning collection", "ebankpayUserScan": "User sweep payment", "remittance": "Remittance recharge", "bussinessRemittance": "Business hall recharge", "fastRefund": "Quick refund", "ebankRefund": "Online banking refund", "remittanceRefund": "Remittance refund", "withdraw": "Withdraw", "perWithdraw": "Personal withdraw", "ordSuccDt": "Actual payment time", "cardWithdraw": "Transfer to bank card", "mercWithdraw": "Merchant settlement", "perHallWithdraw": "Personal business office withdrawal", "mercHallWithdraw": "Merchant business office withdrawal", "paymentSucc": "Successful payment", "paymentFail": "Failed to Payment", "paymentNum": "Payment processing part of the success, the number of successful:", "paymentProcessing": "Payment processing", "partRefund": "Partial refund", "totalRefund": "Full refund", "payConfirm": "Payment confirmation", "payCancel": "Payment back", "searchList": "Query list", "viewModal": "View details", "handleModal": "Deal with orders", "handleModal1": "Review Order", "close": "Off", "orderDetail": "Payment order (payment confirmation or return to be completed)", "handleReason": "Reason", "personHandle": "Manual processing", "submitDate": "Submit date", "systemException": "System exception, please contact the administrator", "payConfirmTitle": "Payment confirmation", "payConfirmText": "Determine the payment from the payment of the payment to the user's bank account? Confirmation will be deducted after the user account balance.", "payConfirmSuss": "Payment confirmation is successful", "payConfirmFail": "Payment confirmation failed, please contact the administrator", "payCancelTitle": "payment back", "payCancelText": "Make sure the payment needs to be returned? Confirm the order will be updated for the transaction failed.", "payCancelSucc": "Payment returned successfully", "payCancelFail": "Payment returns failed, please contact the administrator", "reasonNull": "Cause can not be empty, please re-enter", "detailNull": "Order information does not exist", "swal-success": "Successful operation", "swal-fail": "Operation failed", "swal-confirm": "OK", "swal-cancel": "Cancel", "shouldSelectOne": "Please select a record first!", "lessThan20words": "no more than 20 Chinese characters", "ordStsError": "Can not process a transaction order for a successful or failed transaction", "selectByNull": "- Please select -", "secondLevelOrgMgr": "Cooperative organization information management", "orgInfId": "System number", "corpOrgId": "Cooperative number", "corpOrgNm": "Cooperative name", "corpOrgSnm": "Cooperative agency", "corpOrgTyp": "Type of partner", "corpOrgTyp-0": "Bank", "corpOrgTyp-1": "Not bank", "creOprId": "Create teller ID", "updOprId": "Update teller ID", "corpAccNm": "Partner institution account name", "corpAccNo": "Partner institution card number", "rmk": "Remark", "reason": "Reason for handling", "modalDetail": "Details", "addModal": "Add", "modifyModal": "Update", "addDetail": "Add", "modifyDetail": "Update", "corpOrgIdNull": "Partner number cannot be empty", "corpOrgNmNull": "Partner name cannot be empty", "corpOrgSnmNull": "Partner short name cannot be empty", "corpOrgTypNull": "Partner type cannot be empty", "corpBusTypNull": "Transaction type cannot be empty", "corpBusSubTypNull": "Business type cannot be empty", "busEffFlgNull": "Effective flag cannot be empty", "crdAcTypNull": "Bank card type cannot be empty", "priLvlNull": "Priority cannot be empty", "lowAmtNull": "The minimum amount cannot be empty or less than 0", "highAmtNull": "The maximum amount cannot be empty or less than 0, and cannot be less than the minimum amount", "corpOrgInfNotExists": "Partner information does not exist", "corpOrgInfExists": "Partner information already exists", "corpOrgBusNotExists": "Cooperative organization business does not exist", "corpOrgBusExists": "Partner institution business already exists", "corpOrgRouteExists": "Partner route already exists", "addConfirmTitle": "Increase confirmation", "addConfirmText": "Determine to add new partner information？", "addBusinessText": "Determine to add new partner business？", "addRouteText": "Determine to add new partner routing？", "addConfirmSuss": "Increase success", "addConfirmFail": "Increase failure", "modifyConfirmTitle": "Modification confirmation", "modifyConfirmText": "Determine to modify existing partner organization information？", "modifyBusinessText": "Determine to modify the existing partner business？", "modifyRouteText": "Make sure to modify the existing partner routing？", "modifyConfirmSuss": "Successfully modified", "modifyConfirmFail": "Fail to edit", "deleteConfirmSuss": "Successfully delete", "deleteConfirmFail": "Fail to delete", "busEffFlg": "Effective sign", "busEffFlg-0": "Effective", "busEffFlg-1": "Invalid", "orgBusId": "System number", "rutCorpOrg": "Routing partner", "crdCorpOrg": "Capital cooperation agency", "rutEffFlg": "Effective sign", "rutEffFlg-0": "Effective", "rutEffFlg-1": "Invalid", "crdAcTyp": "Bank card type", "crdAcTyp-D": "Debit card", "crdAcTyp-C": "Credit card", "crdAcTyp-U": "unknown", "priLvl": "Priority", "lowAmt": "Minimum amount", "highAmt": "Maximum amount", "rutInfId": "System number"}, "corg": {"ICBC": "Industrial and Commercial Bank of China", "ABC": "Agricultural Bank of China", "CCB": "China Construction Bank", "BOC": "Bank of China", "CMBC": "China Minsheng Bank", "CMB": "China Merchants Bank", "CIB": "Industrial Bank", "BCM": "Bank of Communications", "CEB": "China Everbright Bank", "GDB": "Guangdong Development Bank", "BEA": "Bank of Southeast Asia", "BESTPAY": "Yi payment", "WeChat": "WeChat", "ALIPAY": "Alipay", "CBP": "Bank of Canada", "ACLEDA": "ACLEDA Bank", "ABA": "ABA Bank", "HALL": "Hall"}, "rsm": {"title": "Plunex | Wind control management", "submit": "Submit", "return": "Return", "add": "Add", "edit": "Modify", "delete": "Delete", "multi-delete": "Are you sure you want to delete the %d selected records?", "single-delete": "Are you sure you want to delete this record?", "cancer": "Lift", "search": "Search", "constant": {"all": "Please select", "effective": "Effective", "invalid": "Failure", "idTyp": {"userId": "User number / Merchant number", "card": "Bank card", "idNo": "Identity card"}, "txTyp": {"all": "All items", "recharge": "Recharge", "consume": "Consumption", "transfer": "Transfer", "withdraw": "Mention", "seatel": "Rechargeable currency", "refunds": "Refund", "interest": "Financial management", "payment": "Payment", "revoke": "Revoke"}, "ruleTyp": {"cnt": "Frequency limit", "amt": "Limit limit", "cnl": "Channel restrictions"}, "dcPtyFlg": {"all": "All", "stl": "Recipient", "pay": "Payee"}, "lmtLvl": {"user": "User level"}, "payTyp": {"all": "All", "account": "Account balance", "quickPay": "Quick payment", "seatel": "Coin", "wechat": "WeChat payment", "alipay": "Alipay", "bestpay": "Yi pay", "offline": "Offline payment"}, "acTyp": {"main": "Main account"}}, "riskList": {"title": "Black and white list management", "blackList": {"title": "Plunex | Black and white list management理", "content": "Black list management"}, "whiteList": {"title": "Plunex | White list management", "content": "White list management"}, "addBlack": "Add blacklist", "addWhite": "Add whitelist", "delBlack": "Cancel blacklist", "delWhite": "Cancel whitelist", "liskId": "List number", "idTyp": "Document type", "id": "Document number", "idHid": "User id", "crdNoLast": "Card end", "txTyp": "Transaction type", "beginDt": "Effective", "endDt": "Expiry time", "effFlg": "Effective sign", "listSorc": "List source", "listRsn": "Cause of operation"}, "check": {"title": "Real-time wind control management", "rule": {"title": "Plunex | Real-time wind control management", "content": "Wind control management", "addRule": "Add the wind control rules", "updRule": "Maintenance of wind control rules", "ruleId": "Rule ID", "ruleTyp": "Rule category", "dcPtyFlg": "Payment sign", "ruleDesc": "Rule description", "ruleNm": "Rule name", "cpnNm": "Component name"}, "censor": {"title": "Plunex | Wind control inspection rules management", "content": "Wind control inspection rules management", "txTyp": "Transaction type", "lmtLvl": "Limit level", "ruleId": "Rule ID", "ruleNm": "Rule", "select": "Select", "oprTyp": "Operation", "oprTypRefuse": "Refuse to trade", "lmtLvlUser": "User level"}, "param": {"title": "Plunex | Wind control parameter management", "content": "Wind control parameter management", "acTyp": "Account type", "lmtLvl": "Limit level", "dcPtyFlg": "Payment and payment mark", "txTyp": "Transaction type", "payTyp": "Payment method", "minAmtLmt": "Single minimum amount", "maxAmtLmt": "Single maximum amount", "dlyAmtLmt": "Daily cumulative limit", "dlyCntLmt": "Cumulative number of days", "mlyAmtLmt": "Monthly cumulative limit", "mlyCntLmt": "Monthly cumulative number of times"}}, "highrisk": {"title": "Plunex | Suspicious transaction query", "content": "Suspicious transaction query", "recId": "Event number", "txTyp": "Event name", "userId": "Suspicious subject", "riskOpr": "Suspicious behavior", "riskDesc": "Suspicious behavior description", "oprSts": "Event handling status", "risk_sorc": "Risk source", "createTime": "Alarm time", "oprStsRisk": "Risk event", "riskOprAmt": "Big deal", "riskOprCnt": "Frequent transactions", "riskOprSps": "Suspicious transaction", "riskParam": "Modify suspicious transaction alert parameters", "days": "Days", "times": "Times", "tThou": "Ten thousand yuan", "psc": "Per", "rule1": "Individual customers will be transferred to X or more yuan through centralized repayment (less than X times) within X days, and will be transferred out to X or more different attribution accounts by means of cash withdrawal and other means.", "rule2": "Individual customers accumulate more than X million yuan in more than X accounts in a single account within X days", "rule3": "The merchant account X originated from the same IP address, the transaction is frequent (more than X times), and the transaction amount exceeds X million yuan.", "rule4": "In the current month, the merchant has a transaction with a single amount of more than X yuan.", "rule5": "Individual customers X frequently (more than X times) happen to close (plus or minus X) limit transactions, and the total transaction amount exceeds X million", "count": "Count"}, "verify": {"notNull": "Input can not be empty", "errTyp": "Illegal input", "maxLen16": "Beyond the maximum length (16 bits)", "maxLen10": "Beyond the maximum length (10 bits)", "sigMinGtMax": "Single minimum amount must be less than the maximum amount of a single", "dlyAmtGtSigMax": "The cumulative amount of less than a single maximum amount of", "mlyAmtLtDlyAmt": "Monthly cumulative amount less than the cumulative amount of", "mlyCntLtDlyCnt": "Monthly cumulative number less than the cumulative number of days"}, "updOprId": "Operator", "createTime": "Entry time", "modifyTime": "Last modified time"}, "rpt": {"title": "Plunex | Report management", "add": "Add", "edit": "modify", "cancer": "Switch state", "search": "Query", "constant": {"all": "Please selete", "sts": {"effective": "Effective", "invalid": "Invalid"}, "period": {"date": "Day", "week": "Week", "month": "Month", "season": "Season", "halfYear": "Half year", "year": "Year", "fixMonth": "Monthly regular", "fixYear": "Annual regular", "fixDate": "From time to time", "other": "Other"}, "level": {"all": "All items", "high": "High", "middle": "Middle", "low": "Low"}, "runSts": {"success": "Success", "fail": "Fail"}, "operaSts": {"download": "Download", "run": "Execution", "running": "In execution"}}, "rptRun": {"title": "Report operation management", "mgrOpr": {"title": "Plunex | Report batch operation", "content": "Report batch operation"}, "mgrOpera": {"title": "Business report", "startTm": "Start time", "endTm": "End time"}, "mgrDef": {"add": "Add report", "edit": "Edit report", "title": "Plunex | Report definition", "content": "Report definition", "rptId": "Report number", "rptNm": "Report name", "rptMd": "Belongs to module", "rptPeriod": "Cycle", "rptDate": "From time to time", "rptClass": "Interface class", "rptMathon": "Interface method", "rptPam": "Variable parameter", "rptPath": "Report path", "rptFileName": "Report file name", "rptRunDt": "Report generation time", "rptRunRs": "Report generation results", "level": "Secret level", "memo": "Remarks", "sts": "State", "rptOpera": "State"}, "createUserId": "Creator", "createTime": "Create time", "modifyUserId": "Last modified person", "modifyTime": "Last modified time"}}, "acm": {"mobile": "Phone number", "query": "Query", "cashAcc": "Cash amount", "settleAcc": "Settle amount", "userId": "Internal user number", "cash": "Total cash account balance", "cashBalance": "Cash account available balance", "seaccy": "Total balance of ocean account", "seaccyBalance": "Balance of available ocean account", "financeBalance": "Financial account balance"}, "cpi": {"title": "Plunex | Capital outflow", "head": "Refund order inquiry", "fundHead": {"title": "Plunex | Recharge transaction [preliminary review]", "title1": "Plunex | Recharge transaction [review]", "content": "Recharge transaction [preliminary review]", "content1": "Recharge transaction [review]"}, "cardHead": {"title": "Plunex | Bank card query", "content": "Bank card query"}, "firstLevel": "Capital access", "secondLevelOrderMgr": "Order management", "secondLevelCardMgr": "Bank card management", "mblNo": "Phone number", "userId": "User number / Merchant number", "fndOrdNo": "Recharge order number", "fudOrdNo": "Recharge order number", "rfdOrdNo": "Refund order number", "corpBusTyp": "Transaction type", "corpBusSubTyp": "Business type", "rutCorpOrg": "Routing agency", "crdCorpOrg": "Financial institution", "orgJrnNo": "Bank serial number", "ordAmt": "Order amount", "ordRfdAmt": "Order refund amount", "reqOrdNo": "Request order number", "ordSts": "Order status", "fee": "Fee", "ordDt": "Submit date", "ordTm": "Submit time", "picUrl": "Remittance voucher", "ordSuccDt": "Order completion date", "orgRspMsg": "Processing results", "agrNo": "Internal protocol number", "agrEffFlg": "Signed a sign of entry into force", "agrEffFlg-W": "Initial registration", "agrEffFlg-y": "Effective", "agrEffFlg-N": "Release", "signDt": "Signing date", "signTm": "Signing time", "unsignDt": "Release date", "unsignTm": "Release time", "bnkPsnFlg": "Public and private signs", "bnkPsnFlg-B": "To the public", "bnkPsnFlg-C": "For private", "signAgrno": "Organization agreement number", "agrDirect": "Protocol type", "agrDirect-0": "Bidirectional protocol", "agrDirect-1": "Our unilateral agreement", "txTyp-sign": "Sign", "prefastSign": "Quick pre-signing", "fastSign": "Quick signing", "fastUnsign": "Quick release", "withdrawSign": "Cash withdrawal", "withdrawUnsign": "<PERSON><PERSON><PERSON>", "txTyp-fastpay": "Fast", "fastpay": "Quick payment", "fastpayConsume": "Fast consumption", "txTyp-ebankpay": "Online banking", "ebankpay": "Online banking payment", "ebankpayConsume": "Online banking consumption", "ebankpayMercScan": "Merchant scanning collection", "ebankpayUserScan": "User sweep payment", "txTyp-remittance": "Remittance", "remittance": "Remittance recharge", "bussinessRemittance": "Business hall recharge", "fastRefund": "Quick refund", "ebankRefund": "Online banking refund", "remittanceRefund": "Remittance refund", "perWithdraw": "Personal mention", "cardWithdraw": "Transfer to bank card", "mercWithdraw": "Merchant settlement", "close": "Off", "submitDate": "Submit date", "refund-w1": "Refund received", "refund-w2": "Refund processing", "refund-S1": "Transaction success", "refund-F1": "Transaction failed", "refund-E1": "Transaction overdue", "crdAcTyp": "Bank card type", "crdAcTyp-D": "Debit card", "crdAcTyp-C": "Credit card", "crdAcTyp-U": "Unknown", "search": "Query", "searchList": "Query list", "viewRemitOrder": "view remittance details", "handleRemitOrder": "process remittance order", "reason": "reason", "orderDetail": "Remittance order (remittance confirmation or return to be completed)", "remitConfirm": "Remittance order confirmation", "remitCancel": "Remittance order return", "lessThan20words": "No more than 20 Chinese characters", "shouldSelectOne": "Please select a record first!", "systemException": "System exception, please contact the administrator", "remitConfirmTitle": "Receipt confirmation", "remitConfirmText": "To determine the existence of the deposit bank in the payment of the receipt of the record? After confirmation of electronic cash will be filled into the user account.", "remitConfirmSuss": "Receipt confirmation is successful", "remitConfirmFail": "Receipt confirmation failed, please contact the administrator", "remitCancelTitle": "Receipt Returns", "remitCancelText": "Make sure the payment is required to be returned? Confirm that the order will be updated.", "remitCancelSucc": "Remittance returned successfully", "remitCancelFail": "Remittance failed, please contact the administrator", "unbindConfirmTitle": "Unbundled confirmation", "unbindConfirmText": "Determine the release of the user-bound bank card after the user will not be able to continue to use the bank card.", "unbindConfirmSuss": "Unbundled success", "unbindConfirmFail": "Unpacking failed, please contact the administrator", "reasonNull": "The reason can not be empty, please re-enter", "detailNull": "Order information does not exist", "typNotSupport": "Can not handle orders for non-remittance type", "ordStsError": "Can not process a transaction order for a successful or failed transaction", "agrEffFlgError": "User bank card is not bound, can not be unbundled", "swal-success": "Successful operation", "swal-fail": "Operation failed", "swal-confirm": "OK", "swal-cancel": "Cancel", "fund-W1": "Waiting for recharge", "fund-W2": "Recharge processing", "fund-S1": "Transaction success", "fund-F1": "Transaction failed", "fund-E1": "Overdue transaction", "fund-R1": "Partial refund", "fund-R2": "All refund", "cardDetail": "User bank card information", "crdExpDt": "<PERSON><PERSON>", "crdCvv2": "CVV2", "crdUsrNm": "Bank card name", "crdNo": "Bank card number", "idNo": "Document number", "idTyp": "Document type", "idTyp-0": "ID", "idTyp-1": "Military card", "idTyp-2": "Passport", "platUnbind": "Platform unbundled", "bothUnbind": "Organization unbundled", "unbindCard": "Bank card unbundled", "cadTyp-D": "Debit card", "cadTyp-C": "Credit card", "cadTyp-U": "Unknown", "selectByNull": "- Please select -", "orgInfId": "System number", "corpOrgId": "Partner number", "corpOrgNm": "Partner name", "corpOrgSnm": "Cooperative agency", "corpOrgTyp": "Type of partner", "corpOrgTyp-0": "Bank", "corpOrgTyp-1": "Not bank", "creOprId": "Create teller ID", "updOprId": "Update teller ID", "corpAccNm": "Partner institution account name", "corpAccNo": "Partner institution card number", "rmk": "Remark", "remark": "Summary", "modalDetail": "Details", "addModal": "Add", "modifyModal": "Modify", "deleteModal": "Delete", "addDetail": "Add", "modifyDetail": "Modify", "deleteDetail": "Delete", "corpOrgIdNull": "Partner number cannot be empty", "corpOrgNmNull": "Partner name cannot be empty", "corpOrgSnmNull": "Cooperative agency short name cannot be empty", "corpOrgTypNull": "Partner type cannot be empty", "corpBusTypNull": "Transaction type cannot be empty", "corpBusSubTypNull": "Business type cannot be empty", "busEffFlgNull": "Effective flag cannot be empty", "crdAcTypNull": "Bank card type cannot be empty", "priLvlNull": "Priority cannot be empty", "lowAmtNull": "The minimum amount cannot be empty or less than 0", "highAmtNull": "The maximum amount cannot be empty or less than 0, and cannot be less than the minimum amount", "capCorgNull": "The financial institution cannot be empty", "crdBinNull": "Card bin cannot be empty", "crdLthNull": "Card number length cannot be empty", "corpOrgInfNotExists": "Partner information does not exist", "corpOrgInfExists": "Partner information already exists", "corpOrgBusNotExists": "Cooperative organization business does not exist", "corpOrgBusExists": "Partner institution business already exists", "corpOrgRouteExists": "Partner route already exists", "cardBinNotExists": "Card bin information does not exist", "cardBinExists": "Card bin information already exists", "addConfirmTitle": "Increase confirmation", "addConfirmText": "Determine to add new partner information？", "addBusinessText": "Determine to add new partner business？", "addRouteText": "Determine to add new partner routing？", "addCardBinText": "Make sure to add new card Bin information？", "addConfirmSuss": "Increase success", "addConfirmFail": "Increase fail", "modifyConfirmTitle": "Modification confirmation", "modifyConfirmText": "Determine to modify existing partner organization information？", "modifyBusinessText": "Determine to modify the existing partner business？", "modifyRouteText": "Make sure to modify the existing partner routing？", "modifyCardBinText": "Make sure to modify the existing card Bin information？", "modifyConfirmSuss": "Modify success", "modifyConfirmFail": "Modify fail", "deleteConfirmTitle": "Modify delete？", "deleteConfirmText": "Confirm delete？", "deleteConfirmSuss": "Successfully deleted", "deleteConfirmFail": "Failed to delete", "busEffFlg": "Effective sign", "busEffFlg-0": "Invalid", "busEffFlg-1": "Effective", "orgBusId": "System number", "capCorg": "Financial institution", "rutEffFlg": "Effective sign", "rutEffFlg-0": "Invalid", "rutEffFlg-1": "Effective", "priLvl": "Priority", "lowAmt": "Minimum amount", "highAmt": "Maximum amount", "rutInfId": "System number", "crdBin": "Card bin", "crdLth": "Card length", "binId": "System number", "refresh": "Refresh", "refreshConfirmText": "OK refresh list record? Merchant billing information may change after refresh.", "refreshConfirmSuss": "Refresh successfully", "refreshConfirmFail": "Refresh failed, please contact the administrator"}, "urm": {"detail": "View", "cancelUser": "Account cancellation", "cancelUserSuc": "Successful account cancellation", "cancelUserConfirmTitle": "User account", "cancelUserConfirmText": "Whether to cancel the account", "cancelUsered": "Has been sold", "cancel": "Cancel", "confirm": "Confirm", "all": "ALL", "beginDt": "Start date", "endDt": "End date", "search": "Query", "submit": "sSubmit", "success": "successful operation", "merAmtinfo": {"amtInfo": "User income and expenditure details", "userId": "Trading partners", "fee": "Fee", "mercId": "Merchant number", "orderNo": "order number", "txTm": "transaction hour", "txTyp": "Transaction Type", "orderAmt": "Amount of the transaction", "couponAmt": "Discounted price", "couponType": "Offer type", "null": "no discount", "seacoin": "coin", "coupon": "coupon", "discount": "discount", "oprName": "Operation name", "mblno": "Phone number"}, "mermgr": {"title": "Plunex | Registration Center", "infoMng": {"title": "Plunex | Business data browsing", "content": "Business data browsing"}, "mercId": "Merchant ID", "examineId": "Audit number", "mercNm": "Business name", "mercSts": "Merchant status", "createTime": "Entry time", "add": "Entry", "edit": "Modify", "delete": "Delete", "review": "Audit", "status": "Set the state", "authority": "Set transaction rights", "passed": "Through the audit", "underReview": "Audit", "reviewRejected": "Audit rejected", "cancelAccount": "Sellers", "normalAccount": "Normal", "statusMng": {"title": "Plunex | Merchant Status Management", "content": "Merchant Status Management"}, "freeze": "Freeze", "pause": "Pause", "mercShortNm": "Business abbreviation", "baseInfo": "Basic information", "settleInfo": "Settle information", "rate": "Rate / Fixed amount", "comercReg": "Business registration number", "mercTrdCls": "Belongs to the industry", "openAccTime": "Open accoumt time", "qrcode": "Qrcode", "downqrcode": "Export", "cprTyp": "Merchant type", "cprTypEnum": {"nationalized": "State-owned", "private": "Private", "foreign": "Foreign", "joinVentrue": "Joint venture", "person": "Personal", "company": "Company", "pVentrue": "Individual sole proprietorship"}, "mgtScp": "Business scope", "merRsgaddr": "Place of business address", "detailAddr": "Address", "merRsgaddrS": "Province", "merRsgaddrI": "City", "fixedaddrBut": "Map positioning", "term": "Operating period", "prinNm": "Legal person name", "certTyp": "Legal document type", "certNo": "Legal document number", "mangerNm": "Administrator name", "mngMoblie": "Administrator phone number", "mngAcc": "Administrator account", "mngEmail": "Admin mailbox", "recMobile": "Recommended mobile phone number", "merevel": "Merchant level", "mercLogo": "Upload business LOGO", "busLicImg": "Upload business license", "certImg": "Upload identity card positive", "certImgB": "Upload ID card negative", "cardImg": "Upload bank card front", "cardImgB": "Upload bank card negative", "merPotocolImg": "Upload business agreement scan", "openWechatMerflag": "Open Wechat Merchant", "belongMerc": "Superior Merchant", "openWechatMerflagEnum": {"open": "Open", "no": "Not open"}, "settleSite": "Settlement take place", "settleSiteEnum": {"bank": "Bank", "hallSite": "Business outlets"}, "hallSites": "Take the existing business outlets", "hallSitesEnum": {"no": "Not limited"}, "capCardNo": "Settlement bank card", "capCardName": "Account name", "capCorgNm": "Bank of account", "subbranch": "Account sub-branch", "settleType": "Settlement mode", "settleTypeEnum": {"auto": "Automatic", "self": "Autonomy", "hall": "Business hall to mention"}, "settleCycleType": "Billing cycle", "settleCycleTypeEnum": {"daily": "Daily settlement", "monthly": "Monthly settlement", "weekly": "Weekly settlement"}, "drawDays": "Number of days", "busType": "Product type", "busTypeEnum": {"consume": "Consumer payments", "draw": "Cash settlement", "scan": "Scan code consumption", "barCode": "Bar code consumption"}, "chargeType": "Charges", "chargeTypeEnum": {"single": "Single", "cycle": "Fixed cycle"}, "calculateType": "Billing method", "calculateTypeEnum": {"percent": "Percentage", "fixed": "Fixed fee"}, "beginCalAmt": "Billing start amount", "minFee": "Minimum amount received", "maxFee": "The maximum amount charged", "expDate": "Expiry date", "effDate": "Effective date", "remark": "Audit opinion", "refuseReson": "The reason of refuse", "beginCalFee": "Billing start amount", "calculateMod": "Billing mode", "calculateModEnum": {"internal": "<PERSON><PERSON>", "external": "Outside the buckle"}, "rate1": "Rate1", "rate2": "Rate2", "rate3": "微信条码消费费率", "rate4": "微信扫码消费费率", "rate5": "支付宝条码消费费率", "rate6": "支付宝扫码消费费率", "rate7": "翼支付条码消费费率", "rate8": "翼支付扫码消费费率", "sumbit": "Confirm submission", "save": "Save", "examine": {"title": "Plunex | Business review", "content": "Business review"}, "examinStatus": "Audit status", "examineWait": "New pending review", "examinePass": "<PERSON><PERSON> pass", "examineNoPass": "Audit rejected", "examineModify": "Modify pending review", "close": "Off", "success": "Successful operation", "failed": "Operation failed", "checkMngAcc": "The administrator account must contain letters and numbers", "rateCheck": "The rate must be less than one hundred percent", "shouldSelectOne": "Please select a record to be modified!", "examinePassAdmit": "The audit has passed the check that is not allowed to be reviewed again", "checkMercNm": "Business name can not be empty", "checkMercShortName": "Business simply can not be empty", "checkComercReg": "Industrial and commercial registration number can not be empty", "checkPrinNm": "Legal person name can not be empty", "checkCertNo": "Legal document number can not be empty", "checkMangerNm": "Administrator name can not be empty", "checkMngMoblie": "Administrator phone number can not be empty", "checkMoblieFormat": "Please follow the + international area code - phone number such as\" + 855-*********** \"format fill", "checkMngEmail": "Administrator mailbox can not be empty", "checkMngEmailFormat": "Administrator Email is Incorrect format", "mngAccExist": "The administrator account already exists", "checkCapCardNo": "Bank card can not be empty", "checkCapCardName": "Bank card name can not be empty", "checkSettleEffDate": "Valid date can not be empty", "checkSettleExpDate": "Expiration date can not be empty", "checkSettleType": "Settlement location for the bank, the billing model can not be made for the business hall, please re-select", "checkDrawDays": "The number of days can only be an integer", "checkRate": "Rate can not be empty", "repeat": "Repeat submission", "innitMerLevelFailed": "Initialize business level drop-down selection box failed", "manage": {"title": "Plunex | Merchant data maintenance", "content": "Merchant data maintenance"}, "checkMercNmLength": "商户名称长度不能超过60个字符", "mercShortNameLength": "商户简称长度不能超过60个字符", "comercRegLength": "工商注册号长度不能超过60个字符", "mgtScpLength": "经营范围长度不能超过200个字符", "prinNmLength": "法人姓名长度不能超过20个字符", "certNoLength": "证件号长度不能超过20个字符", "mangerNmLength": "管理员姓名不能超过20个字符", "mngAccLength": "管理员账号不能超过20个字符", "mngMoblieLength": "管理员手机号不能超过20个字符", "mngEmailLength": "管理员邮箱不能超过20个字符", "recMobileLength": "推荐人手机号不能超过20个字符", "checkrefuseReson": "审核拒绝理由长度不能大于100", "testMercShortName": "商户简称必须为字母", "comercRegExist": "工商注册号已经存在,请重新输入", "mercShortNameExist": "商户简称已经存在,请重新输入"}, "usrmgr": {"title": "Plunex | User management", "amtInfo": "User management", "userId": "Internal user number", "orderNo": "order number", "txTm": "Trading time", "txTyp": "Transaction type", "orderAmt": "Order amount", "couponAmt": "coupon amount", "couponType": "couponType", "null": "no discount", "seacoin": "seacoin", "coupon": "coupon", "discount": "discount", "mercId": "Transaction object", "mblno": "Phone number", "userSts": "User Status", "normal": "Normal", "freeze": "Freeze", "acNo": "Account", "txSts": "Trading Status", "txStsN": "Normal", "txStsC": "<PERSON><PERSON><PERSON>", "dcFlg": "Revenue Signs", "dcFlgD": "Disburse", "dcFlgC": "Credit", "drAmt": "Disburse Amount", "crAmt": "Credit Amount", "txDt": "Trading Date", "status": {"title": "Plunex | User status management", "content": "User status management"}, "pswdrst": {"title": "Plunex | User password reset", "content": "User password reset"}, "query": "Query", "restLoginPswd": "Reset login password", "restPayPswd": "Reset payment password"}, "balance": {"title": "Plunex | User balance query", "manage": "User management", "userId": "Internal user number", "balance": "User balance query", "cashAcc": "Account", "merTitle": "Plunex | Merchant balance management", "mercBalance": "Merchant balance management", "merManage": "Registration Center", "merNm": "Merchant name", "cashBal": "Cash account available balance", "cashUnBal": "Cash account unavailable balance", "settleBal": "Settlement account available balance", "settleUnBal": "Settlement account unavailable balance"}, "info": {"userId": "User number", "mercId": "Merchant number", "mblNo": "Phone number", "usrNm": "User name", "idNo": "Document number", "email": "E-mail", "loginId": "Merchant operator number", "conNm": "Business operator name", "idType": "Document type", "idTypesub": {"0": "Identity card", "1": "Passport"}, "idNoHid": "Document number", "usrRegDt": "Registration date", "idChkFlg": "Real name type", "idChksub": {"0": "Non-real name", "1": "Real name"}, "usrRegCnl": "Registration channel", "proNm": "Business administrator name", "usrRegIp": "Registered IP", "usrRegTm": "Registration time", "usrClsCnl": "Sales channel", "usrClsIp": "Sales IP", "usrClsDt": "Sales date", "usrClsTm": "Sales time", "usrLvl": "User level", "lvlsub": {"0": "Ordinary users", "1": "Business users", "2": "Personal business", "3": "Business business"}, "usrSts": {"title": "User status", "0": "Normal", "1": "Sales"}, "notexit": "Business does not exist", "searchsuc": "Query successful", "moreThanOne": "Exist merchant with the same name, please use the merchant number to query！"}, "mercITF": {"recharge": "Recharge", "settle": "Settlement", "APPpay": "APP payment", "barcodepay": "Barcode payment", "scancodepay": "Sweep payment", "IPOSpay": "IPOS payment", "bankcard": "Bank card receipt", "transMpay": "Transfer to Mpay", "transbankcard": "Transfer to bank card", "payMpay": "Pay to <PERSON><PERSON>y", "paybankcard": "Pay to the bank card"}, "mercinfo": {"basicAuthority": "Basic authority", "collectionAuthority": "Collection rights", "payAuthority": "Payment authority"}, "operate": "Pperation", "operatetype": {"overhistory": "Mobile phone sales account history information", "acmbal": "Account balance information"}, "reset": "Reset", "setpaytype": "Confirm and set the payment method", "close": "Off", "error": "The system is busy", "uimessage": {"success": "Success!", "itfsetsuc": "Confirm to set the payment method page?", "yes": "Yes!", "no": "No!"}, "keyreset": {"success": "Reset success", "tips": "Merchant payment password has been sent to the phone:", "emailtips": "The Merchant key has been sent to the Email："}, "loginpswreset": {"success": "Reset success", "tips": "Operator login password has been sent to the phone:"}, "userloginpswreset": {"success": "Reset success", "tips": "User payment login has been sent to the phone:", "userIsNull": "User is not exist!"}, "userpaypswreset": {"success": "Reset success", "tips": "User payment password has been sent to the phone:", "userIsNull": "User is not exist!"}}, "cls": {"7395": "Wedding photo studio", "7407": "Network instant messaging, mail, Internet phone", "7408": "Digital entertainment", "7409": "SP / CP, recharge providers, mobile phone recharge", "7420": "Portal class", "7421": "Exchange class", "7422": "B2B other", "7423": "Online recruitment", "7424": "Search", "7425": "Internet marketing", "7426": "Video class", "7427": "Enterprise consumption", "7428": "Commercial software applications", "7429": "Office supplies", "7430": "Other", "7431": "System provider", "7519": "Car rental, traffic fines", "7523": "Parking lot", "7538": "Car service store (non-dealer)", "7832": "Movie tickets, all kinds of exhibition hall tickets", "7991": "Travel companies, travel industry portal", "7993": "Games and props", "7994": "Internet cafes, video game stores", "7995": "gambling and lottery", "8050": "Convalescence, rescue service", "8062": "Hospitals, community clinics, physical examination, epidemic prevention agencies", "8211": "Tuition", "8398": "All kinds of welfare agencies", "8931": "Registration fee", "9399": "Online payment (tax, business, etc.)", "4111": "Motor transportation and subway", "4112": "Railway passenger transport", "4121": "Taxi", "4131": "Bus", "4214": "Logistics freight, storage, handling", "4215": "Courier service", "4411": "Shipping", "4511": "Air transport", "4582": "Airport Service", "4722": "Travel ticket", "4814": "Telecommunications services", "4816": "Industry information class website", "4899": "Cable and other pay TV services", "4900": "Utilities", "5172": "Oil, gasoline and other petroleum products", "5192": "Bookstores, periodicals and newspapers", "5199": "Traditional enterprise products", "5200": "Household goods, maternal and child supplies", "5311": "Integrated online mall", "5399": "Chain", "5442": "Nutrition and health products, adult supplies", "5451": "Dairy sales", "5511": "Car sales", "5531": "Automotive supplies", "5621": "Fashion store", "5655": "Sports and leisure outdoors", "5714": "Home building materials", "5722": "Home appliances", "5732": "Digital electronic products", "5734": "Computer software sales", "5812": "Hotel, restaurant", "5912": "Medical supplies", "5931": "Secondhand store", "5942": "Book audio and video", "5943": "Office supplies", "5944": "Clothing / jewelry / cosmetics / jewelry", "5964": "TV shopping", "5992": "Flowers gifts, arts and crafts", "5994": "Newspaper and magazine subscription", "5995": "Pet supplies", "5999": "Grocery store / small supermarket", "6012": "Securities / fund company", "6211": "Financial management, analysis and billing software", "6212": "Other financial products", "6300": "Insurance companies and insurance agents", "7011": "Chain hotel, hotel industry portal", "7210": "Laundry service", "7295": "Intermediary institutions (real estate, matchmaking, home economics)", "7298": "All kinds of leisure, fitness, health and health places", "7299": "Other personal services not included in other codes", "7372": "Computer Integration Services", "7375": "Online community, membership application"}, "certType": {"0": "Identity card", "1": "Passport", "2": "Officer card", "3": "Soldier card", "4": "Hong Kong, Macao and Taiwan residents pass", "5": "Temporary identity card", "6": "Account of this", "7": "Other", "8": "EMPL work permit", "9": "Police officer", "10": "Identification card", "11": "Driving license", "12": "Permanent residence permit", "13": "Insurance card", "14": "Birth certificate", "15": "Social security card"}, "tfm": {"select": "--- Please choose ---", "userId": "Merchant number / Phone number", "busTypeDesc": "Business type", "orderNo": "Order number", "tradeDate": "Order date", "tradeTime": "Trade Time", "busOrderNo": "Business order number", "tradeTotalAmt": "Order amount", "fee": "Fee", "tradeAmt": "Credited amount"}, "kampucheaCity": {"baiLin": "Pailin (municipality)", "jinBian": "Phnom Penh (municipality)", "xiHaNuKe": "Sihanouk City (municipality)", "baiMa": "White Horse City (municipality)", "banDieMianJi": "Bangui cotton province", "maDeWang": "Battambang province", "bangZhan": "Pound Zhan province", "bangQingYang": "Pound Chhnang province", "bangSshiBei": "Pound kingdom", "bangTong": "Pound with the province", "gongBu": "Kampot province", "ganDan": "Dry Dan province", "GeGong": "Ge Gong province", "jiJin": "Orange province", "mengDuoJiLi": "Montundic province", "aoDuoMianJi": "Otto cotton province", "baiWeiXia": "Preah Vihear province", "puSa": "Bodhisattva province", "boLuoMian": "Poromian province", "laTaNaJiLi": "Rata Nakiri province", "xianLi": "Siem Reap province", "shangDing": "On the province", "caiZhen": "Chai Ching Province", "chaJiao": "Tea province"}, "csm": {"title": "Plunex | Clearing management", "head": "Clear settlement management", "adjust": "Manual transfer account", "audit": "Manual transfer account review", "record": "Manual transfer account entry", "query": "Manual payment inquiry", "recorddetail": "Manual transfer records", "look": "View", "adjustaudit": "Audit", "regOpr": "Operator number", "regDt": "Date", "adjustquery": "Query", "dateType": "Date type", "dateTypeVal": {"acDt": "Billing date", "regDt": "Entry date"}, "auditSts": "State", "auditStsVal": {"one": "Through the audit", "two": "Unaudited", "three": "<PERSON><PERSON> refused"}, "adjustinf": {"select": "Select", "regOpr": "Operator number", "jrnNo": "Serial number", "regDt": "Entry time", "acDt": "Billing date", "totTxAmt": "Amount", "totTxNum": "Pen", "rmk": "Remarks"}, "fail": "Processing failed", "success": "Transaction success", "audited": "Agree", "rejected": "Refused", "confirm": "OK", "cancel": "Cancel", "shouldSelectOne": "Please select a record", "vouchertitle": "Plunex | Accounting voucher inquiry", "voucherhead": "Accounting voucher inquiry", "voucher": {"jrnNo": "Voucher number", "mblNo": "Phone number", "userId": "Merchant number", "queryDt": "Date", "acTyp": "Account type", "payAc": "Payment account", "query": "Query", "acDt": "Account Date", "sysTm": "System Time", "jrnSeq": "NO", "acNo": "Account number", "capTyp": "Capital type", "drAmt": "Debit amount", "crAmt": "Credit amount", "txTyp": "Transaction type", "rmk": "Remark"}}, "settle": {"conment": "Payment channel management", "title": "Plunex | Channel settlement inquiry", "head": "Channel settlement inquiry", "firstLevel": "Clear settlement management", "secondLevel": "Payment channel management", "thirtLevel": "Channel settlement inquiry", "selectByNull": "-- Please choose --", "agrEffFlg-Y": "Settled", "agrEffFlg-N": "Unsettlement", "settlesub": {"jrnNo": "jrnNo", "updateDate": "Updated", "updateTime": "Updated time", "payBatchNo": "External agency payment batch number", "settleDate": "Settlement date", "settleDatetime": "Settlement date and time", "startDate": "Transaction start date", "endDate": "Transaction end date", "settleFee": "Settlement amount", "unsettleFee": "Unsettled amount", "settleFeeType": "Settlement currency", "payFee": "amount", "refundFee": "Refund amount", "payNetFee": "Pay the net", "poundageFee": "Fee amount", "totSettleFee": "The total amount has been settled", "totUnsettleFee": "Unsettled total amount", "tmSmp": "Time stamp", "appid": "Public number id", "mchId": "Platform business number in an external agency", "subMchId": "Sub merchant number", "stlFlg": "Settlement sign", "rutCorg": "Routing agency"}}, "userOpr": {"userId": "Operation name", "oprNm": "Operation context", "lv": "Operation level", "ip": "ip", "tm": "time", "search": "Query"}, "cregis": {"callback": {"title": "C<PERSON>gis Callback Record Query", "id": "ID", "txBaseNetwork": "Network ID", "txBaseBlockId": "Block ID", "txBaseTxId": "Transaction Hash", "txBaseEcode": "Error Code", "txBaseGroupId": "Transaction Group ID", "txBaseFee": "Transaction Fee", "txBaseStatus": "Transaction Status", "txBaseCreateTime": "Transaction Create Time", "txBaseBlockHash": "Block Hash", "txBaseProgramId": "Contract Program ID", "txBaseComputeUnitsConsumed": "Compute Units Consumed", "txBaseMemo": "Transaction Memo", "vaultCode": "Vault Code", "accountId": "Account ID", "accountType": "Account Type", "orderId": "Platform Order ID", "bilOrderNo": "Bill Order Number", "coinId": "Coin ID", "network": "Network ID", "address": "Platform Address", "cpAddress": "Counterparty Address", "amount": "Transaction Amount", "direction": "Transaction Direction", "channel": "Transaction Channel", "callbackTime": "Callback Time", "isProcessed": {"title": "Is Processed", "yes": "Yes", "no": "No"}}}, "common": {"search": "Search", "reset": "Reset", "operations": "Operations", "detail": "View Details", "close": "Close"}, "exchangeWaitReview": {"title": "Exchange transaction [review]", "content": "Exchange transaction [review]", "table": {"exchangeId": "Exchange ID", "exchangeType": "Exchange Type", "exchangeTime": "Exchange Time", "exchangeAmount": "Exchange Amount", "exchangeStatus": "Exchange Status", "exchangeMemo": "Exchange Memo", "exchangeReviewTime": "Exchange Review Time", "exchangeReviewStatus": "Exchange Review Status", "exchangeReviewMemo": "Exchange Review Memo"}}, "kybAudit": {"title": "Merchant KYB [Initial Review]", "content": "Merchant KYB [Initial Review]"}, "register": {"idChkFlg": "Real Name Logo", "detail": "Detail", "addTitle": "Add User Register", "title": "Register Management", "content": "Register Management", "mermgr": "Registration Center", "userId": "User ID", "mblNo": "Mobile Number", "usrNm": "User Name", "crtTm": "Create Time", "opt": "Operation", "add": "Add", "edit": "Edit", "del": "Delete", "search": "Search", "reset": "Reset", "usrLvl": {"title": "User Level", "normal": "Normal User", "enterprise": "Enterprise User", "personal": "Personal Merchant", "enterpriseMerchant": "Enterprise Merchant"}, "usrSts": {"title": "User Status", "open": "Open", "close": "Close"}, "usrGender": {"title": "User Gender", "pleaseSelect": "Please Select", "male": "Male", "female": "Female"}, "usrNation": {"title": "User Nation", "content": "User Nation", "pleaseSelect": "Please Select"}, "idType": {"title": "ID Type", "pleaseSelect": "Please Select"}, "idNo": {"title": "ID Number"}, "usrRegCnl": {"title": "Register Channel"}, "save": "Save", "cancel": "Cancel", "usrBirthDt": "User Birth Date", "usrRegIp": "User Register IP", "createTime": "Create Time", "modifyTime": "Modify Time"}, "exchangerate": {"title": "Exchange Rate", "content": "Exchange Rate"}, "feeorder": {"title": "Fee Order", "content": "Fee Order"}, "kyb": {"page_title": "Plunex | Merchant KYB [Initial Review]", "page_heading": "Merchant KYB [Initial Review]", "breadcrumb_business": "Business Management", "breadcrumb_merchant": "Registration Center", "breadcrumb_kyb_audit": "Merchant KYB [Initial Review]", "comment_query_form": "Query Form", "comment_user_id": "User ID", "label_user_id": "User ID", "comment_examine_status": "Review Status", "label_examine_status": "Review Status", "option_all": "All", "option_pending": "Pending", "option_reviewing": "Under Initial Review", "option_rejected": "Rejected", "btn_search": "Search", "btn_reset": "Reset", "comment_data_table": "Data Table", "th_kyb_id": "KYB ID", "th_user_id": "User ID", "th_examine_status": "Review Status", "th_create_time": "Created At", "th_operation": "Action", "comment_detail_modal": "Detail <PERSON>", "modal_title_detail": "KYB Details", "detail_kyb_id": "KYB Review ID", "detail_user_id": "User ID", "detail_examine_status": "Review Status", "detail_create_time": "Created At", "comment_company_info": "Company Information", "detail_corp_name_cn": "Company Name (Chinese)", "detail_corp_name_eng": "Company Name (English)", "detail_regist_region": "Country/Region of Registration", "detail_contact_name": "Contact Person Name", "detail_contact_email": "Contact Email", "detail_contact_number": "Contact Phone", "detail_entity_legal_form": "Legal Form of Entity", "detail_entity_business_form": "Business Nature of Entity", "detail_web_url": "Company Website", "detail_regist_time": "Registration Date", "detail_regist_addr": "Registered Address", "detail_actual_addr": "Actual Address", "detail_regist_code": "Registration Number", "detail_certificate_ci": "Certificate of Incorporation (CI)", "detail_business_registration_br": "Business Registration (BR)", "detail_nnc1_nar1": "NNC1/NAR1", "detail_article_of_association": "Articles of Association", "detail_structure_of_members": "Shareholding Certificate", "detail_ceo_identification": "Director/Equivalent Position", "detail_ultimate_beneficial_owners": "Ultimate Beneficial Owner(s)", "comment_file_dynamic": "File links will be dynamically added via JS", "comment_ubo_dynamic": "UBO information will be dynamically added via JS", "detail_business_desc": "Main Business Description", "detail_expected_month_amt": "Expected Monthly Transaction Volume", "detail_source_of_fund": "Primary Source of Funds", "detail_is_sensitive": "Sensitive Identity Involved", "detail_additional": "Additional Notes / Supporting Documents", "btn_close": "Close", "comment_audit_modal": "Review Modal", "modal_title_audit": "Merchant KYB [Initial Review]", "audit_result": "Review Result", "audit_pass": "Approve", "audit_reject": "Reject", "audit_opinion": "Review Comments", "reject_reason": "Reason for Rejection", "btn_cancel": "Cancel", "btn_submit": "Submit", "status_pending": "Pending", "status_reviewing": "Under Initial Review", "status_second_review": "Under Final Review", "status_approved": "Approved", "status_rejected": "Rejected", "btn_detail": "Details", "btn_audit": "Review", "yes": "Yes", "no": "No", "none": "None", "role_desc": "Role Description:", "related_files": "Related Files:", "error_get_detail": "Failed to load details", "warning_opinion_required": "Please provide review comments", "warning_reason_required": "Please provide reason for rejection", "success_audit": "Review completed successfully", "error_audit": "Review failed", "error_data_load": "Failed to load data"}, "kybReview": {"title": "Merchant KYB [Review]", "content": "Merchant KYB [Review]", "page_title": "Plunex | Merchant KYB [Review]", "page_heading": "Merchant KYB [Review]", "breadcrumb_business": "Business Management", "breadcrumb_merchant": "Registration Center", "breadcrumb_kyb_review": "Merchant KYB [Review]", "comment_query_form": "Query Form", "comment_user_id": "User ID", "label_user_id": "User ID", "btn_search": "Search", "btn_reset": "Reset", "comment_data_table": "Data Table", "th_kyb_id": "KYB ID", "th_user_id": "User ID", "th_examine_status": "Review Status", "th_first_auditor": "First Auditor", "th_first_audit_time": "First Audit Time", "th_create_time": "Create Time", "th_operation": "Operation", "comment_detail_modal": "Detail <PERSON>", "modal_title_detail": "KYB Information Details", "detail_kyb_id": "KYB Review ID", "detail_user_id": "User ID", "detail_examine_status": "Review Status", "detail_create_time": "Create Time", "comment_company_info": "Company Information", "detail_corp_name_cn": "Company Full Name (Chinese)", "detail_corp_name_eng": "Company Full Name (English)", "detail_regist_region": "Registration Country/Region", "detail_contact_name": "Contact Name", "detail_contact_email": "Contact Email", "detail_contact_number": "Contact Phone", "detail_entity_legal_form": "Entity Legal Form", "detail_entity_business_form": "Entity Business Form", "detail_web_url": "Company Website", "detail_regist_time": "Registration Date", "detail_regist_addr": "Registration Address", "detail_actual_addr": "Actual Address", "detail_regist_code": "Registration Number", "detail_certificate_ci": "Certificate of Incorporation CI", "detail_business_registration_br": "Business Registration BR", "detail_nnc1_nar1": "NNC1/NAR1", "detail_article_of_association": "Articles of Association", "detail_structure_of_members": "Structure of Members", "detail_ceo_identification": "CEO/Director Identification", "detail_ultimate_beneficial_owners": "Ultimate Beneficial Owners", "comment_file_dynamic": "File links will be added dynamically via JS", "comment_ubo_dynamic": "UBO information will be added dynamically via JS", "detail_business_desc": "Main Business Description", "detail_expected_month_amt": "Expected Monthly Transaction Amount", "detail_source_of_fund": "Source of Funds", "detail_is_sensitive": "Has Sensitive Identity", "detail_additional": "Additional Notes/Supplementary Materials", "comment_first_audit_info": "First Audit Information", "detail_first_auditor": "First Auditor", "detail_first_audit_time": "First Audit Time", "detail_first_audit_opinion": "First Audit Opinion", "btn_close": "Close", "comment_review_modal": "Review Modal", "modal_title_review": "Merchant KYB [Review]", "review_result": "Review Result", "review_pass": "Review Approved", "review_reject": "Review Rejected", "review_opinion": "Review Opinion", "reject_reason": "Rejection Reason", "btn_cancel": "Cancel", "btn_submit": "Submit", "status_pending": "Pending", "status_reviewing": "Under Review", "status_second_review": "Second Review", "status_approved": "Approved", "status_rejected": "Rejected", "btn_detail": "Details", "btn_review": "Review", "yes": "Yes", "no": "No", "none": "None", "ubo_header": "Ultimate Beneficial Owner", "role_desc": "Role Description:", "related_files": "Related Files:", "error_get_detail": "Failed to get details", "warning_opinion_required": "Please fill in review opinion", "warning_reason_required": "Please fill in rejection reason", "success_review": "Review successful", "error_review": "Review failed", "error_data_load": "Data loading failed"}, "accManage": {"title": "Merchant account opening review", "content": "Merchant account opening review", "page_title": "Plunex | Merchant account opening review", "page_heading": "Merchant account opening review", "breadcrumb_business": "Business Management", "breadcrumb_merchant": "Registration Center", "breadcrumb_acc_audit": "Merchant account opening review", "comment_query_form": "Query Form", "comment_account_no": "Account Number", "label_account_no": "Account Number", "comment_user_id": "User ID", "label_user_id": "User ID", "comment_currency": "<PERSON><PERSON><PERSON><PERSON>", "label_currency": "<PERSON><PERSON><PERSON><PERSON>", "comment_currency_type": "Currency Type", "label_currency_type": "Currency Type", "option_all": "All", "option_fiat": "Fiat", "option_digital": "Digital", "comment_account_status": "Account Status", "label_account_status": "Account Status", "option_opened": "Opened", "option_closed": "Closed", "option_pending": "Pending <PERSON>t", "option_rejected": "<PERSON><PERSON> Rejected", "comment_bank": "Bank/Institution", "label_bank": "Bank/Institution", "btn_search": "Search", "btn_reset": "Reset", "comment_data_table": "Data Table", "th_account_no": "Account Number", "th_user_id": "User ID", "th_currency": "<PERSON><PERSON><PERSON><PERSON>", "th_currency_type": "Currency Type", "th_account_status": "Account Status", "th_bank": "Bank/Institution", "th_account_create_date": "Account Create Date", "th_account_create_time": "Account Create Time", "th_account_close_date": "Account Close Date", "th_account_close_time": "Account Close Time", "th_create_time": "Create Time", "th_update_time": "Update Time", "th_operation": "Operation", "comment_detail_modal": "Detail <PERSON>", "modal_title_detail": "Account Details", "detail_account_no": "Account Number", "detail_user_id": "User ID", "detail_currency": "<PERSON><PERSON><PERSON><PERSON>", "detail_currency_type": "Currency Type", "detail_account_status": "Account Status", "detail_bank": "Bank/Institution", "detail_account_create_date": "Account Create Date", "detail_account_create_time": "Account Create Time", "detail_account_close_date": "Account Close Date", "detail_account_close_time": "Account Close Time", "detail_create_time": "Create Time", "detail_update_time": "Update Time", "btn_close": "Close", "comment_audit_modal": "<PERSON><PERSON>", "modal_title_audit": "Merchant account opening review", "audit_result": "<PERSON>t Result", "audit_pass": "Audit Approved", "audit_reject": "<PERSON><PERSON> Rejected", "audit_opinion": "Audit Opinion", "placeholder_audit_opinion": "Please enter audit opinion", "btn_cancel": "Cancel", "btn_submit": "Submit", "currency_fiat": "Fiat", "currency_digital": "Digital", "status_opened": "Opened", "status_closed": "Closed", "status_pending": "Pending <PERSON>t", "status_rejected": "<PERSON><PERSON> Rejected", "btn_detail": "Details", "btn_audit": "Audit", "warning_opinion_required": "Please enter audit opinion", "success_audit": "Audit successful", "error_audit": "Audit failed", "unknown_error": "Unknown error", "error_data_load": "Data loading failed", "excel_title": "Merchant account opening review List"}, "exchangeReview": {"title": "Exchange transaction [review]", "content": "Exchange transaction [review]", "userId": "User ID", "orderNo": "Order No", "direction": "Exchange Direction", "status": "Order Status", "selectAll": "All", "directionS2F": "Crypto to Fiat", "directionF2S": "Fiat to Crypto", "directionF2F": "Fiat to Fiat", "statusSecondAudit": "Under Review", "statusApproved": "Approved", "statusRejected": "Rejected", "statusSuccess": "Success", "statusFailed": "Failed", "statusPending": "Pending", "statusFirstAudit": "First Audit", "search": "Search", "fromCoin": "From Currency", "fromAmount": "From Amount", "toCoin": "To <PERSON><PERSON><PERSON><PERSON>", "toAmount": "To Amount", "createTime": "Create Time", "operations": "Operations", "detail": "Detail", "review": "Review", "exchangeDetail": "Exchange Transaction Detail", "feeCoin": "<PERSON><PERSON>", "feeAmount": "<PERSON><PERSON> Amount", "exchangeRate": "Exchange Rate", "firstAuditInfo": "First Audit Info", "secondAuditInfo": "Second Audit Info", "rejectReason": "Reject Reason", "id": "Order ID", "fromUsdRate": "From Currency USD Rate", "toUsdRate": "To Currency USD Rate", "fromAmountUsd": "From Amount USD Equivalent", "toAmountUsd": "To Amount USD Equivalent", "executeTime": "Execute Time", "updateTime": "Update Time", "close": "Close", "audit": "Audit Exchange Transaction", "auditResult": "<PERSON>t Result", "auditApproved": "Audit Approved", "auditRejected": "<PERSON><PERSON> Rejected", "auditOpinion": "Audit Opinion", "rejectReasonInput": "Reject Reason", "cancel": "Cancel", "submit": "Submit", "processing": "Processing...", "first": "First", "last": "Last", "next": "Next", "previous": "Previous", "info": "Showing _START_ to _END_ of _TOTAL_ entries", "infoEmpty": "Showing 0 to 0 of 0 entries", "infoFiltered": "(filtered from _MAX_ total entries)", "lengthMenu": "Show _MENU_ entries", "exportTitle": "Exchange Review List", "auditor": "Auditor", "auditTime": "Audit Time", "reviewExchangeTransaction": "Review Exchange Transaction", "auditSuccess": "Audit Success", "dataLoadFailed": "Data Load Failed", "getDetailFailed": "Get Detail Failed", "auditFailed": "Audit Failed"}, "exchangeAudit": {"page_title": "Exchange transaction [preliminary review]", "page_heading": "Exchange transaction [preliminary review]", "breadcrumb_business": "Business Management", "breadcrumb_transaction": "Transaction Management", "breadcrumb_exchange_audit": "Exchange transaction [preliminary review]", "comment_query_form": "Query Form", "comment_user_id": "User ID", "label_user_id": "User ID", "comment_order_no": "Order Number", "label_order_no": "Order Number", "comment_direction": "Exchange Direction", "label_direction": "Exchange Direction", "option_all": "All", "option_s2f": "Digital to Fiat", "option_f2s": "Fiat to Digital", "option_f2f": "Fiat to Fiat", "comment_status": "Order Status", "label_status": "Order Status", "option_pending": "Pending", "option_first_audit": "First Audit", "option_second_audit": "Second Audit", "option_approved": "Approved", "option_rejected": "Rejected", "option_success": "Success", "option_failed": "Failed", "btn_search": "Search", "comment_data_table": "Data Table", "th_user_id": "User ID", "th_order_no": "Order Number", "th_direction": "Exchange Direction", "th_from_coin": "From Currency", "th_from_amount": "From Amount", "th_to_coin": "To <PERSON><PERSON><PERSON><PERSON>", "th_to_amount": "To Amount", "th_status": "Status", "th_create_time": "Create Time", "th_operations": "Operations", "comment_detail_modal": "Detail <PERSON>", "modal_title_detail": "Exchange Transaction Details", "detail_user_id": "User ID", "detail_order_no": "Order Number", "detail_direction": "Exchange Direction", "detail_from_coin": "From Currency", "detail_from_amount": "From Amount", "detail_to_coin": "To <PERSON><PERSON><PERSON><PERSON>", "detail_to_amount": "To Amount", "detail_fee_coin": "<PERSON><PERSON>", "detail_fee_amount": "<PERSON><PERSON> Amount", "detail_exchange_rate": "Exchange Rate", "detail_status": "Status", "detail_first_audit_info": "First Audit Information", "detail_second_audit_info": "Second Audit Information", "detail_reject_reason": "Rejection Reason", "detail_id": "Order ID", "detail_from_usd_rate": "From Currency USD Rate", "detail_to_usd_rate": "To Currency USD Rate", "detail_from_amount_usd": "From Amount USD Equivalent", "detail_to_amount_usd": "To Amount USD Equivalent", "detail_execute_time": "Execute Time", "detail_create_time": "Create Time", "detail_update_time": "Update Time", "btn_close": "Close", "comment_audit_modal": "<PERSON><PERSON>", "modal_title_audit": "Audit Exchange Transaction", "audit_result": "<PERSON>t Result", "audit_approved": "Audit Approved", "audit_rejected": "<PERSON><PERSON> Rejected", "audit_opinion": "Audit Opinion", "reject_reason": "Rejection Reason", "btn_cancel": "Cancel", "btn_submit": "Submit", "direction_s2f": "Digital to Fiat", "direction_f2s": "Fiat to Digital", "direction_f2f": "Fiat to Fiat", "status_pending": "Pending", "status_first_audit": "First Audit", "status_second_audit": "Second Audit", "status_approved": "Approved", "status_rejected": "Rejected", "status_success": "Success", "status_failed": "Failed", "btn_detail": "Details", "btn_first_audit": "First Audit", "btn_second_audit": "Second Audit", "auditor": "Auditor", "audit_time": "Audit Time", "error_get_detail": "Failed to get details", "first_audit_title": "First Audit Exchange Transaction", "second_audit_title": "Second Audit Exchange Transaction", "success_audit": "Audit successful", "error_audit": "Audit failed", "error_data_load": "Data loading failed", "excel_title": "Exchange Audit List"}, "dmbill": {"title": "Digital Currency Deposit Management", "pageTitle": "Management of fiat currency transactions", "dcOrderManagement": "Management of fiat currency transactions", "queryConditions": "Query Conditions", "transactionRecords": "Transaction Records", "acNo": "Account Number", "startTime": "Start Time", "endTime": "End Time", "search": "Search", "reset": "Reset", "txTime": "Transaction Time", "coinId": "Transaction Currency", "amount": "Transaction Amount", "fee": "Fee", "receiveAddress": "Receiving Address/Account", "payAddress": "Payment Address/Account", "txType": "Transaction Type", "orderId": "Transaction Serial Number", "status": "Transaction Status", "operations": "Operations", "transactionDetail": "Transaction Details", "memo": "Memo", "channel": "Transaction Channel", "network": "Network Identifier", "blockId": "Block ID", "ecode": "Error Code", "groupId": "Transaction Group ID", "blockHash": "Block Hash", "programId": "Contract Program ID", "computeUnitsConsumed": "Compute Units Consumed", "vaultCode": "Vault Code", "accountId": "Associated Account ID", "accountType": "Account Type", "bilOrderNo": "Bill Order Number", "direction": "Transaction Direction", "close": "Close", "copy": "Copy", "exportTitle": "Digital Currency Transaction Management", "viewDetail": "View Details", "dataLoadFailed": "Data Load Failed", "getDetailFailed": "Get Details Failed", "statusSuccess": "Success", "statusFailed": "Failed", "statusWaitPay": "Waiting for Payment", "statusProcessing": "System Processing", "statusPayConfirm": "Payment Confirmation Pending", "statusPartialRefund": "Partial Refund", "statusTimeout": "Timeout", "statusCancelled": "Cancelled", "statusPaymentProcessing": "Payment Processing", "statusPaymentFailed": "Payment Failed", "statusPaymentSuccess": "Payment Success", "txTypeDS": "Digital Currency Receipt", "txTypeDC": "Digital Currency Deposit", "txTypeDH": "Digital Currency Exchange", "txTypeDZ": "Digital Currency Transfer", "txTypeDX": "Digital Currency <PERSON>", "txType01": "<PERSON><PERSON><PERSON><PERSON>", "txType02": "Consumption", "txType03": "Transfer", "txType04": "<PERSON><PERSON><PERSON>", "txType05": "Sea Coin Deposit", "txType06": "Refund", "txType07": "Investment", "txType08": "Payment", "content": "Digital currency recharge management", "dscontent": "Digital currency receive payment management", "DSindex": {"page_title": "Digital Currency Receipt Management", "page_heading": "Digital Currency Transaction Management", "breadcrumb_business": "Business Management", "breadcrumb_transaction": "Transaction Management", "breadcrumb_dmbill": "Digital Currency Transaction Management", "comment_query_form": "Query Form", "query_conditions": "Query Conditions", "comment_account_no": "Account Number", "label_account_no": "Account Number", "comment_start_time": "Start Time", "label_start_time": "Start Time", "comment_end_time": "End Time", "label_end_time": "End Time", "btn_search": "Search", "btn_reset": "Reset", "comment_data_table": "Data Table", "transaction_records": "Transaction Records", "th_tx_time": "Transaction Time", "th_coin_id": "<PERSON><PERSON><PERSON><PERSON>", "th_amount": "Amount", "th_fee": "Fee", "th_receive_address": "Receive Address/Account", "th_pay_address": "Pay Address/Account", "th_tx_type": "Transaction Type", "th_order_id": "Transaction ID", "th_status": "Status", "th_operations": "Operations", "comment_detail_modal": "Detail <PERSON>", "modal_title_detail": "Transaction Details", "detail_tx_time": "Transaction Time", "detail_coin_id": "<PERSON><PERSON><PERSON><PERSON>", "detail_amount": "Amount", "detail_fee": "Fee", "detail_receive_address": "Receive Address/Account", "detail_pay_address": "Pay Address/Account", "detail_order_id": "Transaction ID", "detail_tx_type": "Transaction Type", "detail_status": "Status", "detail_memo": "Memo", "detail_channel": "Channel", "comment_additional_fields": "Additional Fields", "detail_network": "Network", "detail_block_id": "Block ID", "detail_ecode": "Error Code", "detail_group_id": "Group ID", "detail_block_hash": "Block Hash", "detail_program_id": "Program ID", "detail_compute_units_consumed": "Compute Units Consumed", "detail_vault_code": "Vault Code", "detail_account_id": "Account ID", "detail_account_type": "Account Type", "detail_bil_order_no": "Bill Order Number", "detail_direction": "Direction", "btn_close": "Close", "btn_copy": "Copy", "excel_title": "Digital Currency Transaction Management", "tx_type_ds": "Digital Receipt", "tx_type_dc": "Digital Deposit", "tx_type_dh": "Digital Exchange", "tx_type_dz": "Digital Transfer", "tx_type_dx": "Digital Withdrawal", "tx_type_01": "<PERSON><PERSON><PERSON><PERSON>", "tx_type_02": "Consumption", "tx_type_03": "Transfer", "tx_type_04": "<PERSON><PERSON><PERSON>", "tx_type_05": "Sea Coin Deposit", "tx_type_06": "Refund", "tx_type_07": "Investment", "tx_type_08": "Payment", "status_success": "Success", "status_failed": "Failed", "status_waiting_pay": "Waiting Payment", "status_processing": "Processing", "status_pending_confirm": "Pending Confirmation", "status_partial_refund": "Partial Refund", "status_timeout": "Timeout", "status_cancelled": "Cancelled", "status_paying": "Paying", "status_pay_failed": "Payment Failed", "status_pay_success": "Payment Success", "btn_view_detail": "View Details", "error_get_detail": "Failed to get details", "error_data_load": "Data loading failed"}, "page": {"queryConditions": "Query Conditions", "transactionRecords": "Transaction Records", "copy": "Copy", "exportTitle": "Digital Currency Transaction Management", "viewDetail": "View Detail", "dataLoadFailed": "Data Load Failed", "getDetailFailed": "Get Detail Failed", "statusSuccess": "Success", "statusFailed": "Failed", "statusWaitPay": "Waiting for Payment", "statusProcessing": "System Processing", "statusPayConfirm": "Payment Confirmation Pending", "statusPartialRefund": "Partial Refund", "statusTimeout": "Timeout", "statusCancelled": "Cancelled", "statusPaymentProcessing": "Payment Processing", "statusPaymentFailed": "Payment Failed", "statusPaymentSuccess": "Payment Success", "txTypeDS": "Digital Currency Receipt", "txTypeDC": "Digital Currency Deposit", "txTypeDH": "Digital Currency Exchange", "txTypeDZ": "Digital Currency Transfer", "txTypeDX": "Digital Currency <PERSON>", "txType01": "<PERSON><PERSON><PERSON><PERSON>", "txType02": "Consumption", "txType03": "Transfer", "txType04": "<PERSON><PERSON><PERSON>", "txType05": "Sea Coin Deposit", "txType06": "Refund", "txType07": "Investment", "txType08": "Payment"}}, "dcorder": {"withdrawWaitReview": {"title": "Payment in cryptocurrency [review]", "fundInOut": "Fund In/Out", "dcOrderManagement": "Management of fiat currency transactions", "exportTitle": "Digital Currency Pending Review Payment Order List", "dcPayment": "Digital Currency Payment", "dcTransfer": "Digital Currency Transfer", "orderNo": "Order No", "txType": "Transaction Type", "busType": "Business Type", "payerId": "Payer", "payeeId": "Payee", "busOrderNo": "External Order No", "orderAmt": "Transaction Amount", "fee": "Fee", "orderStatus": "Transaction Status", "orderTm": "Transaction Time", "txHash": "Transaction Hash", "remark": "Remark", "searchList": "Search", "viewModal": "View Details", "handleModal": "Handle Order", "handleModal1": "Review Order", "selectByNull": "All", "withdrawSucc": "<PERSON><PERSON>wal Success", "withdrawFail": "Withdrawal Failed", "waitPay": "Waiting for Payment", "waitReview": "Pending Review", "waitCallback": "Waiting for <PERSON><PERSON>", "accountTransfer": "Account Transfer", "cardTransfer": "Bank Card Transfer", "faceTransfer": "Face-to-Face Transfer", "swalFail": "Failed", "swalSuccess": "Success", "swalCancel": "Cancel", "swalConfirm": "Confirm", "shouldSelectOne": "Please select one record", "detailNull": "Detail is empty", "systemException": "System Exception", "reasonNull": "Reason cannot be empty", "ordStsError": "Order status error", "lessThan20words": "Less than 20 words", "payConfirmTitle1": "Confirm Payment", "payConfirmSuss": "Payment confirmation successful", "payConfirmFail": "Payment confirmation failed", "paymentNum": "Payment number", "payCancelTitle": "Cancel Payment", "payCancelText": "Cancel payment text", "payConfirmText": "Confirm payment text", "payCancel": "Cancel Payment", "payConfirm": "Confirm Payment", "close": "Close", "payCancelSucc": "Payment cancellation successful", "payCancelFail": "Payment cancellation failed"}, "withdraw": {"title": "Payment in cryptocurrency [preliminary review]", "fundInOut": "Fund In/Out", "dcOrderManagement": "Management of fiat currency transactions", "orderList": "Digital Currency Payment Order List", "dcPayment": "Digital Currency Payment", "orderNo": "Order No.", "txType": "Transaction Type", "busType": "Business Type", "payerId": "Payer", "payeeId": "Payee", "busOrderNo": "External Order No.", "orderAmt": "Transaction Amount", "fee": "Fee", "orderStatus": "Transaction Status", "orderTm": "Transaction Time", "txHash": "Transaction Hash", "remark": "Remark", "selectByNull": "Please Select", "withdrawSucc": "Transaction Success", "withdrawFail": "Transaction Failed", "waitPay": "Pending Payment", "waitCallback": "Pending Callback", "waitReview": "Pending Review", "searchList": "Search", "viewModal": "View Details", "handleModal": "<PERSON><PERSON>", "accountTransfer": "Account Transfer", "cardTransfer": "Card Transfer", "faceTransfer": "Face-to-Face Transfer"}, "transfer": {"title": "Digital currency transfer [preliminary review]", "fundInOut": "Fund In/Out", "dcOrderManagement": "Management of fiat currency transactions", "orderNo": "Order No", "busOrderNo": "External Order No", "payerId": "Payer", "payeeId": "Payee", "orderStatus": "Order Status", "selectByNull": "All", "tranSucc": "Transfer Success", "tranFail": "Transfer Failed", "waitPay": "Waiting for Payment", "waitCallback": "Waiting for <PERSON><PERSON>", "waitReview": "Pending Review", "searchList": "Search", "viewModal": "View Details", "handleModal": "Handle Order", "txType": "Transaction Type", "busType": "Business Type", "orderAmt": "Transaction Amount", "fee": "Fee", "orderTm": "Transaction Time", "exportTitle": "Digital Currency Transfer Order List", "dcTransfer": "Digital Currency Transfer", "accountTransfer": "Account Transfer", "cardTransfer": "Bank Card Transfer", "faceTransfer": "Face-to-Face Transfer", "orderDetail": "Order Details", "txHash": "Transaction Hash", "remark": "Remark", "payConfirm": "Confirm Payment", "payCancel": "Cancel Payment", "close": "Close", "lessThan20words": "Less than 20 words", "swalFail": "Failed", "swalSuccess": "Success", "swalCancel": "Cancel", "swalConfirm": "Confirm", "ordStsError": "Order status error", "systemException": "System Exception", "reasonNull": "Reason cannot be empty", "payCancelSucc": "Payment cancellation successful", "paymentNum": "Payment number", "shouldSelectOne": "Please select one record", "payCancelTitle": "Cancel Payment", "payConfirmTitle": "Confirm Payment", "remitConfirmTitle": "Confirm Remittance", "remitConfirmText": "Confirm remittance text", "remitCancelTitle": "Cancel Remittance", "remitCancelText": "Cancel remittance text", "remitConfirmSuss": "Remittance confirmation successful", "remitCancelSucc": "Remittance cancellation successful", "remitConfirmFail": "Remittance confirmation failed", "remitCancelFail": "Remittance cancellation failed", "payConfirmSuss": "Payment confirmation successful", "payConfirmFail": "Payment confirmation failed", "payCancelFail": "Payment cancellation failed", "detailNull": "Detail is empty"}, "transferReview": {"title": "Digital currency transfer [review]", "fundInOut": "Fund In/Out", "fileUrl": "Transaction Material", "dcOrderManagement": "Management of fiat currency transactions", "orderList": "Digital Currency Transfer Order Review List", "orderDetail": "Order Details", "orderNo": "Order No.", "txType": "Transaction Type", "busType": "Business Type", "payerId": "Transferor", "payeeId": "Payee", "busOrderNo": "External Order No.", "orderAmt": "Transaction Amount", "fee": "Fee", "orderStatus": "Order Status", "orderTm": "Transaction Time", "txHash": "Transaction Hash", "remark": "Remark", "selectByNull": "Please Select", "tranSucc": "Transaction Success", "tranFail": "Transaction Failed", "waitPay": "Pending Payment", "waitCallback": "Pending Callback", "waitReview": "Pending Review", "searchList": "Search", "viewModal": "View Details", "handleModal": "<PERSON><PERSON>", "payConfirm": "Approve", "payCancel": "Reject Transfer", "close": "Close", "accountTransfer": "Account Transfer", "cardTransfer": "Card Transfer", "faceTransfer": "Face-to-Face Transfer", "dcTransfer": "Digital Currency Transfer"}, "callback": {"title": "C<PERSON>gis Callback Record Query", "detailTitle": "<PERSON><PERSON>gis <PERSON>back Record Details", "transactionQuery": "Transaction Query", "queryConditions": "Query Conditions", "callbackRecords": "Callback Records", "basicInfo": "Basic Information", "transactionBaseInfo": "Transaction Base Information", "accountTransactionInfo": "Account and Transaction Information", "id": "ID", "txHash": "Transaction Hash", "txStatus": "Transaction Status", "orderId": "Order ID", "bilOrderNo": "Bill Order No", "coinId": "Coin", "amount": "Amount", "direction": "Direction", "callbackTime": "Callback Time", "processStatus": "Process Status", "operations": "Operations", "all": "All", "processed": "Processed", "unprocessed": "Unprocessed", "inbound": "Inbound", "outbound": "Outbound", "detail": "Detail", "copy": "Copy", "close": "Close", "getDetailFailed": "Failed to get details", "platformOrderId": "Platform Order ID", "callbackReceiveTime": "Callback Receive Time", "isProcessed": "Is Processed", "networkId": "Network ID", "blockId": "Block ID", "errorCode": "Error Code", "txGroupId": "Transaction Group ID", "txFee": "Transaction Fee", "txCreateTime": "Transaction Create Time", "computeUnitsConsumed": "Compute Units Consumed", "blockHash": "Block Hash", "contractProgramId": "Contract Program ID", "txMemo": "Transaction Memo", "vaultCode": "Vault Code", "accountId": "Account ID", "accountType": "Account Type", "channel": "Channel", "platformAddress": "Platform Address", "counterpartyAddress": "Counterparty Address"}}, "order": {"transfer": {"title": "Transfer transaction [preliminary review]", "head": "Transfer transaction [preliminary review]", "secondLevelOrderMgr": "Order Management", "orderNo": "Order No.", "busOrderNo": "External Order No.", "payerId": "Payer", "payeeId": "Payee", "orderStatus": "Order Status", "txType": "Transaction Type", "busType": "Business Type", "orderAmt": "Transaction Amount", "fee": "Fee", "orderTm": "Transaction Time", "selectByNull": "Please Select", "tranSucc": "Transaction Success", "tranFail": "Transaction Failed", "waitPay": "Pending Payment", "waitReview": "Pending Review", "searchList": "Search", "viewModal": "View Details", "handleModal": "<PERSON><PERSON>", "exportTitle": "Transfer Order List", "accountTransfer": "Account Transfer", "cardTransfer": "Card Transfer", "faceTransfer": "Face-to-Face Transfer", "lessThan20words": "Less than 20 words", "swalFail": "Failed", "swalSuccess": "Success", "swalCancel": "Cancel", "swalConfirm": "Confirm", "shouldSelectOne": "Please select one row", "detailNull": "Detail is null", "systemException": "System Exception", "ordStsError": "Order status error", "reasonNull": "Reason cannot be empty", "payConfirmTitle": "Payment Confirmation", "payConfirmSuss": "Payment confirmation successful", "payConfirmFail": "Payment confirmation failed", "payCancelTitle": "Payment Cancellation", "payCancelSucc": "Payment cancellation successful", "payCancelFail": "Payment cancellation failed", "paymentNum": "Payment Number: "}, "transferReview": {"title": "Transfer transaction [review]", "fundInOut": "Fund In/Out", "orderManagement": "Order Management", "orderList": "Transfer Order List", "orderDetail": "Order Details", "orderNo": "Order No.", "txType": "Transaction Type", "busType": "Business Type", "payerId": "Transferor", "payeeId": "Payee", "busOrderNo": "External Order No.", "orderAmt": "Transaction Amount", "fee": "Fee", "orderStatus": "Order Status", "orderTm": "Transaction Time", "remark": "Remark", "selectByNull": "Please Select", "tranSucc": "Transaction Success", "tranFail": "Transaction Failed", "waitPay": "Pending Payment", "waitReview": "Pending Review", "searchList": "Search", "viewModal": "View Details", "handleModal": "<PERSON><PERSON>", "payConfirm": "Approve", "payCancel": "Reject Transfer", "close": "Close", "accountTransfer": "Account Transfer", "cardTransfer": "Card Transfer", "faceTransfer": "Face-to-Face Transfer"}}, "exchangeRate": {"title": "Exchange Rate Query", "transactionManagement": "Transaction Management", "rateManagement": "Rate Management", "add": "Add", "search": "Search", "reset": "Reset", "rateType": "Rate Type", "sourceCode": "Source Currency Code", "targetCode": "Target Currency Code", "network": "Network", "rateSource": "Rate Source", "updateBy": "Updated By", "id": "ID", "baseRate": "Base Rate", "manualPoint": "Manual Adjustment Points", "finalRate": "Final Effective Rate", "effectiveTime": "Effective Time", "expireTime": "Expire Time", "operations": "Operations", "addRateInfo": "Add Rate Information", "editRateInfo": "Edit Rate Information", "rateInfoDetail": "Rate Information Details", "rateInfoList": "Rate Information List", "approvedBy": "Approved By", "cancel": "Cancel", "save": "Save", "close": "Close", "detail": "Detail", "edit": "Edit", "delete": "Delete", "operationSuccess": "Operation Successful", "operationFailed": "Operation Failed", "getDetailFailed": "Failed to get details", "loadDataFailed": "Failed to load data, please refresh and try again", "fillRequired": "Please fill in required fields", "confirmDelete": "Are you sure you want to delete this exchange rate information?", "deleteWarning": "It cannot be recovered after deletion!", "confirmDeleteBtn": "Confirm Delete", "deleteSuccess": "Delete Successful!", "deleteSuccessMsg": "Exchange rate information has been deleted.", "deleteFailed": "Delete Failed", "retryLater": "Please try again later", "error": "Error", "validNumber": "Please enter a valid number", "rateTypeRequired": "Please enter rate type", "rateTypeMaxLength": "Rate type cannot exceed 50 characters", "sourceCodeRequired": "Please enter source currency code", "sourceCodeMaxLength": "Source currency code cannot exceed 10 characters", "targetCodeRequired": "Please enter target currency code", "targetCodeMaxLength": "Target currency code cannot exceed 10 characters", "baseRateRequired": "Please enter base rate", "baseRateMin": "Base rate cannot be negative", "finalRateRequired": "Please enter final effective rate", "finalRateMin": "Final effective rate cannot be negative"}, "msginfo": {"title": "Internationalization Management", "content": "Internationalization Management", "businessManagement": "Business Management", "marketingManagement": "Marketing Management", "add": "Add", "addTitle": "Add Internationalization Message", "editTitle": "Edit Internationalization Message", "detailTitle": "Internationalization Message Details", "msgCd": "Message Code", "language": "Language", "msgInfo": "Message Information", "scenario": "<PERSON><PERSON><PERSON>", "createTime": "Create Time", "modifyTime": "Modify Time", "operations": "Operations", "selectLanguage": "Please select language", "chinese": "Chinese", "english": "English", "khmer": "Khmer", "search": "Search", "reset": "Reset", "save": "Save", "cancel": "Cancel", "close": "Close", "detail": "Detail", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this internationalization message?", "deleteWarning": "It cannot be recovered after deletion!", "confirmDeleteBtn": "Confirm Delete", "deleteSuccess": "Delete Success!", "deleteSuccessMsg": "The internationalization message has been deleted.", "deleteFailed": "Delete Failed", "retryLater": "Please try again later", "error": "Error", "operationSuccess": "Operation Successful", "operationFailed": "Operation Failed", "fillRequired": "Please fill in required fields", "getDetailFailed": "Failed to get details", "loadDataError": "Failed to load data, please refresh and try again", "messageList": "Internationalization Message List", "msgCdRequired": "Please enter message code", "msgCdMaxLength": "Message code cannot exceed 8 characters", "languageRequired": "Please select language", "msgInfoRequired": "Please enter message information", "msgInfoMaxLength": "Message information cannot exceed 200 characters", "scenarioMaxLength": "<PERSON><PERSON><PERSON> cannot exceed 30 characters", "queryFormComment": "Query Form", "dataTableComment": "Data Table", "addEditModalComment": "Add/Edit <PERSON>", "detailModalComment": "Detail <PERSON>", "pageLevelScriptsComment": "Page-<PERSON>", "msgCdComment": "Message Code", "languageComment": "Language", "msgInfoComment": "Message Information", "scenarioComment": "<PERSON><PERSON><PERSON>", "initDataTablesComment": "Initialize DataTables", "addExtraSearchParamsComment": "Add extra search parameters", "ensureArrayReturnComment": "Ensure array return", "disableBuiltinSearchComment": "Disable built-in search", "addButtonComment": "<PERSON>d <PERSON>", "saveButtonComment": "Save <PERSON><PERSON>", "viewDetailComment": "View Detail", "editButtonComment": "<PERSON>", "deleteButtonComment": "Delete Button", "formValidationComment": "Form Validation", "getLanguageTextComment": "Get Language Display Text", "searchMethodComment": "Search Method", "resetFormComment": "Reset Form"}, "feerule": {"content": "Transaction fee management", "title": "Transaction fee management", "paramManagement": "Parameter Management", "add": "Add", "search": "Search", "reset": "Reset", "busType": "Business Type", "busTypeDesc": "Business Type Description", "ccy": "<PERSON><PERSON><PERSON><PERSON>", "calculateMod": "Calculation Mode", "calculateType": "Calculation Type", "rate": "Rate", "fixFee": "Fixed Fee", "chargeType": "Charge Type", "calculateMinAmt": "Minimum Calculation Amount", "minFee": "Minimum Fee", "maxFee": "Maximum Fee", "effDate": "Effective Date", "expDate": "Expiry Date", "oprId": "Operator", "createTime": "Create Time", "modifyTime": "Modify Time", "operations": "Operations", "addTitle": "Add Fee Rule", "editTitle": "<PERSON> <PERSON>e <PERSON>", "detailTitle": "Fee Rule Details", "feeRuleList": "Fee Rule List", "cancel": "Cancel", "save": "Save", "close": "Close", "detail": "Detail", "edit": "Edit", "delete": "Delete", "operationSuccess": "Operation Successful", "operationFailed": "Operation Failed", "loadDataFailed": "Failed to load data, please refresh and try again", "fillRequired": "Please fill in required fields", "getDetailFailed": "Failed to get details", "confirmDelete": "Are you sure you want to delete this fee rule?", "deleteWarning": "It cannot be recovered after deletion!", "confirmDeleteBtn": "Confirm Delete", "deleteSuccess": "Delete Successful!", "deleteSuccessMsg": "Fee rule has been deleted.", "deleteFailed": "Delete Failed", "retryLater": "Please try again later", "error": "Error", "stats": {"content": "Status", "active": "Active", "inactive": "Inactive"}, "validNumber": "Please enter a valid number", "busTypeDescRequired": "Please enter business type description", "busTypeDescMaxLength": "Business type description cannot exceed 100 characters", "ccyRequired": "Please enter currency", "ccyMaxLength": "Currency cannot exceed 3 characters", "calculateModRequired": "Please enter calculation mode", "calculateModMaxLength": "Calculation mode cannot exceed 20 characters", "calculateTypeRequired": "Please enter calculation type", "calculateTypeMaxLength": "Calculation type cannot exceed 20 characters", "rateRequired": "Please enter rate", "rateMin": "Rate cannot be less than 0", "fixFeeRequired": "Please enter fixed fee", "fixFeeMin": "Fixed fee cannot be less than 0", "chargeTypeRequired": "Please enter charge type", "chargeTypeMaxLength": "Charge type cannot exceed 20 characters", "calculateMinAmtRequired": "Please enter minimum calculation amount", "calculateMinAmtMin": "Minimum calculation amount cannot be less than 0", "minFeeRequired": "Please enter minimum fee", "minFeeMin": "Minimum fee cannot be less than 0", "maxFeeRequired": "Please enter maximum fee", "maxFeeMin": "Maximum fee cannot be less than 0", "statsRequired": "Please select status", "effDateRequired": "Please select effective date", "expDateRequired": "Please select expiry date"}, "pact": {"title": "Pact Management", "content": "Pact Management", "add": "Add", "id": "ID", "pactTitle": "Pact Title", "search": "Search", "reset": "Reset", "type": "Type", "status": "Status", "createUser": "Created By", "createTime": "Created Date", "operations": "Operations", "addTitle": "Add Pact", "userPact": "User Pact", "merchantPact": "Merchant Pact", "paymentPact": "Payment Pact", "other": "Other", "active": "Active", "inactive": "Inactive", "pactContent": "Pact Content", "remark": "Remark", "cancel": "Cancel", "save": "Save", "detailTitle": "Pact Detail", "updateUser": "Updated By", "updateTime": "Updated Date", "close": "Close", "loadDataError": "Failed to load data, please refresh and try again", "detail": "Detail", "edit": "Edit", "delete": "Delete", "listTitle": "Pact List", "requiredError": "Please fill in the required fields", "opSuccess": "Operation successful", "opError": "Operation failed: ", "getDetailError": "Failed to get details: ", "editTitle": "Edit Pact", "deleteConfirm": "Are you sure you want to delete this pact?", "deleteConfirmText": "This action cannot be undone!", "confirmDelete": "Confirm Delete", "deleteSuccess": "Delete successful!", "deleted": "The pact has been deleted.", "deleteError": "Delete failed", "tryAgain": "Please try again later", "error": "Error: ", "titleRequired": "Please enter the pact title", "titleMaxLength": "Pact title cannot exceed 100 characters", "typeRequired": "Please select a pact type", "statusRequired": "Please select a pact status", "contentRequired": "Please enter the pact content", "contentMaxLength": "Pact content cannot exceed 2000 characters", "remarkMaxLength": "Remark cannot exceed 500 characters"}, "faq": {"title": "FAQ Management", "content": "FAQ Management", "add": "Add", "id": "ID", "questionContent": "Question Content", "search": "Search", "reset": "Reset", "language": "Language", "createdBy": "Created By", "createdDate": "Created Date", "operations": "Operations", "addTitle": "Add FAQ", "answerContent": "Answer Content", "selectLanguage": "Select Language", "chinese": "Chinese", "english": "English", "cancel": "Cancel", "save": "Save", "detailTitle": "FAQ Detail", "updatedBy": "Updated By", "updatedDate": "Updated Date", "close": "Close", "loadDataError": "Failed to load data, please refresh and try again", "detail": "Detail", "edit": "Edit", "delete": "Delete", "listTitle": "FAQ List", "requiredError": "Please fill in the required fields", "opSuccess": "Operation successful", "opError": "Operation failed: ", "getDetailError": "Failed to get details: ", "editTitle": "Edit FAQ", "deleteConfirm": "Are you sure you want to delete this FAQ?", "deleteConfirmText": "This action cannot be undone!", "confirmDelete": "Confirm Delete", "deleteSuccess": "Delete successful!", "deleted": "The FAQ has been deleted.", "deleteError": "Delete failed", "tryAgain": "Please try again later", "error": "Error: ", "questionRequired": "Please enter the question content", "questionMaxLength": "Question content cannot exceed 300 characters", "answerRequired": "Please enter the answer content", "answerMaxLength": "Answer content cannot exceed 300 characters", "languageRequired": "Please select a language"}, "accountaddress": {"title": "Merchant cryptocurrency address management", "content": "Merchant cryptocurrency address management", "add": "Add", "vaultCode": "Vault Code", "address": "Address", "network": "Network", "status": "Status", "allStatus": "All Statuses", "enabled": "Enabled", "disabled": "Disabled", "frozen": "Frozen", "search": "Search", "reset": "Reset", "id": "ID", "groupCode": "Group Code", "accountId": "Account ID", "createTime": "Create Time", "operations": "Operations", "addTitle": "Add Merchant cryptocurrency address management", "acmAcNo": "Correlated Account", "userId": "User ID", "useType": "Use Type", "selectUseType": "Please select use type", "useTypeDS": "Receivable", "useTypeDC": "Recharge", "qrcodeBase64": "QR Code (Base64)", "cancel": "Cancel", "save": "Save", "detailTitle": "Merchant cryptocurrency address management Details", "qrcode": "QR Code", "updateTime": "Update Time", "close": "Close", "loadDataError": "Failed to load data, please refresh and try again.", "listTitle": "Merchant cryptocurrency address management List", "requiredError": "Please fill in the required fields.", "opSuccess": "Operation successful.", "opError": "Operation failed: ", "getDetailError": "Failed to get details: ", "vaultCodeRequired": "Please enter the vault code.", "vaultCodeMaxLength": "Vault code cannot exceed 50 characters.", "groupCodeRequired": "Please enter the group code.", "groupCodeMaxLength": "Group code cannot exceed 50 characters.", "accountIdRequired": "Please enter the account ID.", "accountIdNumber": "Please enter a valid number.", "addressRequired": "Please enter the address.", "addressMaxLength": "Address cannot exceed 100 characters.", "networkMaxLength": "Network cannot exceed 50 characters.", "statusRequired": "Please select a status.", "addressExists": "This address already exists.", "detail": "Detail", "edit": "Edit", "delete": "Delete"}, "暂无警报": "No alerts", "异常警报": "Abnormal alerts", "今日出账": "Today's Outgoing", "今日入账": "Today's Incoming", "交易数据": "Transaction Data", "法币头寸可用余额": "Fiat Currency Available Balance", "数币头寸可用余额": "Digital Currency Available Balance", "财务数据": "Financial Data", "合作机构名称": "Partner Institution Name", "账户数量": "Account Quantity", "账户总额": "Total Account Balance", "可用金额": "Available Balance", "冻结金额": "Frozen Balance", "流动性监控": "Liquidity Monitoring", "交易总额": "Total Transaction Amount", "业务数据趋势": "Business Data Trends", "交易成功率": "Transaction Success Rate", "交易笔数": "Number of Transactions", "业务核心指标": "Core Business Metrics", "收款": "Receiving", "充值": "Top-up", "兑换": "Exchange", "转账": "Transfer", "提现": "<PERSON><PERSON><PERSON>", "手续费": "Handling Fee", "当日": "Today", "最近一周": "Last 7 Days", "最近一月": "Last 30 Days", "比昨日上升": "Up from yesterday", "比昨日下降": "Down from yesterday", "范围不得超过12个月": "The range cannot exceed 12 months.", "请至少选择两个月": "Please select at least two months."}