/**
 * Plunex Navigation Sidebar JavaScript
 * 处理侧边栏导航菜单的交互功能
 */

$(document).ready(function() {

    // 全局变量
    let currentActiveMenuItem = null;

    // 初始化导航菜单
    function initNavigation() {
        // 移除所有现有的事件监听器
        $('#side-menu a').off('click.plunex');

        // 处理所有菜单项的点击事件
        $('#side-menu a').on('click.plunex', function(e) {
            const $this = $(this);
            const href = $this.attr('href');
            const $parent = $this.parent();
            const $submenu = $parent.find('> .nav-second-level, > .nav-third-level');

            // 如果有子菜单，处理展开/收缩
            if ($submenu.length > 0) {
                e.preventDefault();
                toggleSubmenu($parent, $submenu);
            } else if (href && href !== '#') {
                // 如果是有效链接的叶子节点，设置为激活状态
                setActiveMenuItem($this);
                // 让链接正常跳转
            } else {
                // 如果是无效链接，阻止跳转
                e.preventDefault();
            }
        });
    }

    // 切换子菜单显示/隐藏
    function toggleSubmenu($parent, $submenu) {
        const isActive = $parent.hasClass('active');
        const level = $parent.closest('.nav-second-level').length > 0 ? 'third' : 'second';

        if (level === 'second') {
            // 一级菜单：关闭其他一级菜单
            $('#side-menu > li').not($parent).each(function() {
                collapseMenu($(this));
            });
        } else {
            // 二级菜单：关闭同级的其他三级菜单
            $parent.siblings().each(function() {
                collapseMenu($(this));
            });
        }

        // 切换当前菜单状态
        if (isActive) {
            collapseMenu($parent);
        } else {
            expandMenu($parent);
        }

    }

    // 展开菜单
    function expandMenu($menuItem) {
        if ($menuItem.hasClass('active')) {
            return;
        }

        // 添加active类，CSS会自动处理动画
        $menuItem.addClass('active');
    }

    // 收缩菜单
    function collapseMenu($menuItem) {
        if (!$menuItem.hasClass('active')) {
            return;
        }

        // 移除active类，CSS会自动处理动画
        $menuItem.removeClass('active');
    }

    // 清除所有激活状态
    function clearAllActiveStates() {
        // 移除所有选中状态
        $('#side-menu li').removeClass('menu-active');

        // 重置当前激活项
        currentActiveMenuItem = null;
    }

    // 设置激活菜单项
    function setActiveMenuItem($menuItem) {
        if (!$menuItem || $menuItem.length === 0) {
            return;
        }

        const href = $menuItem.attr('href');

        // 清除所有现有的激活状态
        clearAllActiveStates();

        // 设置新的激活状态
        const $targetLi = $menuItem.closest('li');
        $targetLi.addClass('menu-active');
        currentActiveMenuItem = $menuItem;

        // 确保父级菜单展开
        $menuItem.parents('li').each(function() {
            expandMenu($(this));
        });

        // 保存状态到localStorage
        if (href && href !== '#') {
            localStorage.setItem('plunex_active_menu', href);
        }

    }

    // 根据当前URL设置激活菜单
    function setActiveMenuByUrl() {
        const currentPath = window.location.pathname;

        // 首先尝试从localStorage恢复
        const savedActiveMenu = localStorage.getItem('plunex_active_menu');
        if (savedActiveMenu && savedActiveMenu === currentPath) {
            const $savedMenuItem = $('#side-menu a[href="' + savedActiveMenu + '"]').first();
            if ($savedMenuItem.length > 0) {
                setActiveMenuItem($savedMenuItem);
                return;
            }
        }

        // 查找精确匹配的菜单项
        let $exactMatch = null;
        let $bestMatch = null;
        let bestMatchLength = 0;

        $('#side-menu a').each(function() {
            const $this = $(this);
            const href = $this.attr('href');

            if (href && href !== '#' && href.length > 1) {
                // 精确匹配
                if (currentPath === href) {
                    $exactMatch = $this;
                    return false; // 停止循环
                }

                // 路径前缀匹配（但要确保是完整的路径段）
                if (currentPath.startsWith(href + '/') ||
                    (href.length > 1 && currentPath.startsWith(href) && currentPath.charAt(href.length) === '/')) {
                    if (href.length > bestMatchLength) {
                        $bestMatch = $this;
                        bestMatchLength = href.length;
                    }
                }
            }
        });

        // 应用匹配结果
        if ($exactMatch) {
            setActiveMenuItem($exactMatch);
        } else if ($bestMatch) {
            setActiveMenuItem($bestMatch);
        }
    }
    
    // 平滑滚动到激活菜单项
    function scrollToActiveMenu() {
        const $activeMenu = $('#side-menu li.menu-active').first();
        if ($activeMenu.length > 0) {
            const $sidebar = $('.navbar-static-side');
            const activeMenuTop = $activeMenu.offset().top;
            const sidebarTop = $sidebar.offset().top;
            const scrollTop = activeMenuTop - sidebarTop - 100;

            $sidebar.animate({ scrollTop: scrollTop }, 300);
        }
    }

    // 处理菜单项悬停效果
    function initHoverEffects() {
        // CSS已经处理了悬停效果，这里不需要额外处理
    }

    // 响应式菜单处理
    function initResponsiveMenu() {
        // 移动端菜单切换
        $('.navbar-toggle').on('click', function() {
            $('.navbar-static-side').toggleClass('show');
        });

        // 点击遮罩关闭菜单
        $(document).on('click', function(e) {
            if ($(window).width() <= 768) {
                if (!$(e.target).closest('.navbar-static-side, .navbar-toggle').length) {
                    $('.navbar-static-side').removeClass('show');
                }
            }
        });
    }

    // 菜单搜索功能（预留）
    function initMenuSearch() {
        // 预留功能，暂不实现
    }
    
    // 页面加载完成后的初始化
    function initOnPageLoad() {
        // 等待DOM完全加载
        setTimeout(function() {
            // 根据URL设置激活菜单
            setActiveMenuByUrl();
        }, 100);
    }

    // 初始化所有功能
    function init() {
        // 基础功能初始化
        initNavigation();
        initHoverEffects();
        initResponsiveMenu();
        initMenuSearch();

        // 页面加载时初始化菜单状态
        initOnPageLoad();

        // 监听页面变化（如果是SPA）
        $(window).on('popstate', function() {
            setTimeout(initOnPageLoad, 50);
        });
    }

    // 等待i18n加载完成后再初始化
    if (typeof i18nLoad !== 'undefined') {
        i18nLoad.then(function() {
            init();
            
            // 监听语言切换事件，重新国际化侧边栏
            $(document).on('languageChanged', function() {
                setTimeout(function() {
                    // 重新国际化侧边栏菜单
                    $('#side-menu').i18n();
                    
                    // 重新设置激活菜单状态
                    setActiveMenuByUrl();
                }, 50);
            });
        });
    } else {
        // 如果没有i18n，直接初始化
        init();
    }
    
    // 手动激活菜单项（供外部调用）
    function activateMenuByHref(href) {
        const $menuItem = $('#side-menu a[href="' + href + '"]').first();
        if ($menuItem.length > 0) {
            setActiveMenuItem($menuItem);
            return true;
        }
        return false;
    }



    // 导出功能供外部使用
    window.PlunexNavigation = {
        setActiveMenuItem: setActiveMenuItem,
        setActiveMenuByUrl: setActiveMenuByUrl,
        activateMenuByHref: activateMenuByHref,
        scrollToActiveMenu: scrollToActiveMenu,
        clearAllActiveStates: clearAllActiveStates,
        expandMenu: expandMenu,
        collapseMenu: collapseMenu,
        toggleSubmenu: toggleSubmenu
    };
});