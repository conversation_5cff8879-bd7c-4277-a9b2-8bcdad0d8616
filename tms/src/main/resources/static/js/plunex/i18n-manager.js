/**
 * Plunex I18n 管理器
 * 处理动态内容的国际化
 */
(function($) {
    'use strict';

    // I18n管理器对象
    var I18nManager = {
        
        // 初始化
        init: function() {
            this.bindEvents();
            this.setupGlobalRefresh();
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 监听语言切换事件
            $(document).on('languageChanged', function() {
                self.refreshAll();
            });
            
            // 监听AJAX完成事件，处理新加载的内容
            $(document).ajaxComplete(function() {
                self.refreshNewContent();
            });
        },

        // 设置全局刷新函数
        setupGlobalRefresh: function() {
            var self = this;
            
            // 如果window.refreshI18n不存在，创建默认的
            if (typeof window.refreshI18n !== 'function') {
                window.refreshI18n = function() {
                    self.refreshAll();
                };
            }
        },

        // 刷新所有内容
        refreshAll: function() {
            if (typeof i18n === 'undefined') {
                console.log('i18n not available');
                return;
            }

            // 处理标准元素
            this.refreshStandardElements();
            
            // 处理特殊元素
            this.refreshSpecialElements();
            
            // 处理动态表单
            this.refreshForms();
            
            // 处理数据表格
            this.refreshDataTables();
            
            console.log('All content refreshed for i18n');
        },

        // 刷新标准元素
        refreshStandardElements: function() {
            // 处理所有data-i18n属性
            $('[data-i18n]').each(function() {
                var $el = $(this);
                var key = $el.data('i18n');
                if (key) {
                    var translated = $.t(key);
                    if ($el.is('input[type="button"], input[type="submit"], button')) {
                        $el.val(translated);
                    } else if ($el.is('input[type="reset"]')) {
                        $el.val(translated);
                    } else {
                        $el.text(translated);
                    }
                }
            });
        },

        // 刷新特殊元素
        refreshSpecialElements: function() {
            // 处理placeholder属性
            $('input[placeholder], textarea[placeholder]').each(function() {
                var $el = $(this);
                var placeholder = $el.attr('placeholder');
                if (placeholder && placeholder.indexOf('.') > 0) {
                    // 假设placeholder是i18n key
                    try {
                        var translated = $.t(placeholder);
                        $el.attr('placeholder', translated);
                    } catch (e) {
                        // 如果不是有效的i18n key，跳过
                    }
                }
            });

            // 处理title属性
            $('[title]').each(function() {
                var $el = $(this);
                var title = $el.attr('title');
                if (title && title.indexOf('.') > 0) {
                    try {
                        var translated = $.t(title);
                        $el.attr('title', translated);
                    } catch (e) {
                        // 如果不是有效的i18n key，跳过
                    }
                }
            });
        },

        // 刷新表单元素
        refreshForms: function() {
            // 处理表单标签
            $('label[for]').each(function() {
                var $label = $(this);
                var forAttr = $label.attr('for');
                if (forAttr) {
                    // 检查是否有对应的data-i18n属性
                    var $input = $('#' + forAttr);
                    if ($input.length && $input.data('i18n')) {
                        var key = $input.data('i18n') + '_label';
                        try {
                            var translated = $.t(key);
                            $label.text(translated);
                        } catch (e) {
                            // 如果key不存在，跳过
                        }
                    }
                }
            });
        },

        // 刷新数据表格
        refreshDataTables: function() {
            // 检查是否存在DataTable
            if (typeof $.fn.DataTable !== 'undefined') {
                $('.dataTable').each(function() {
                    var table = $(this).DataTable();
                    if (table) {
                        // 重新绘制表格以更新国际化内容
                        table.draw(false);
                    }
                });
            }
        },

        // 刷新新加载的内容
        refreshNewContent: function() {
            var self = this;
            
            // 延迟执行，确保内容已加载
            setTimeout(function() {
                self.refreshStandardElements();
                self.refreshSpecialElements();
            }, 100);
        },

        // 强制刷新特定区域
        refreshRegion: function(selector) {
            var $region = $(selector);
            if ($region.length && typeof i18n !== 'undefined') {
                $region.i18n();
                this.refreshSpecialElements();
            }
        }
    };

    // 初始化
    $(document).ready(function() {
        I18nManager.init();
    });

    // 导出到全局
    window.I18nManager = I18nManager;

})(jQuery);