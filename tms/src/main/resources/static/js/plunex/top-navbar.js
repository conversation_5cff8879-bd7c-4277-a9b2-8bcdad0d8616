/**
 * Plunex Top Navbar JavaScript
 * 处理语言选择功能
 */

$(document).ready(function () {

    // 语言选择器功能
    $('.plunex-language-option').click(function (e) {
        e.preventDefault();
        const lang = $(this).data('lang');
        const langText = $(this).text().trim();
        const flagSrc = $(this).find('img').attr('src');

        // 更新当前语言显示
        $('.current-language').text(langText);
        $('#current-flag').attr('src', flagSrc);

        // 更新激活状态
        $('.plunex-language-option').removeClass('active');
        $(this).addClass('active');

        // 关闭下拉菜单
        $('.plunex-language-toggle').removeClass('active');

        // 切换语言
        if (typeof i18n !== 'undefined') {
            i18n.setLng(lang, function () {
                // 使用I18nManager或全局刷新函数
                if (typeof window.I18nManager !== 'undefined') {
                    window.I18nManager.refreshAll();
                } else if (typeof window.refreshI18n === 'function') {
                    window.refreshI18n();
                }
                console.log('Language switched to:', lang);
            });
        } else {
            console.log('i18n not available');
        }

        console.log('Language changed to:', lang);
    });

    // 语言下拉菜单切换
    $('.plunex-language-toggle').click(function () {
        console.log(1)
        $(this).toggleClass('active');
    });

    // 点击外部关闭下拉菜单
    $(document).click(function (e) {
        if (!$(e.target).closest('.plunex-language-selector').length) {
            $('.plunex-language-toggle').removeClass('active');
        }
    });

    // 兼容原有的语言切换方式
    $('.set_en').on('click', function () {
        updateLanguage('en', 'English', '/img/flags/16/United-States.png');
    });

    $('.set_zh').on('click', function () {
        updateLanguage('zh', '中文', '/img/flags/16/China.png');
    });

    // 更新语言的辅助函数
    function updateLanguage(lang, langText, flagSrc) {
        if (typeof i18n !== 'undefined') {
            i18n.setLng(lang, function () {
                $('head').i18n();
                $('#wrapper').i18n();
                $('.navbar-top-links').i18n();
                $('#side-menu').i18n();

                // 更新UI
                $('.current-language').text(langText);
                $('#current-flag').attr('src', flagSrc);

                // 更新激活状态
                $('.plunex-language-option').removeClass('active');
                $('.set_' + lang).addClass('active');

                // 关闭下拉菜单
                $('.plunex-language-toggle').removeClass('active');

                // 语言切换完成后刷新页面
                // window.location.reload();
            });
        } else {
            // 如果没有i18n，直接刷新页面
            window.location.reload();
        }
    }

    // 页面加载时初始化语言显示（不刷新页面）
    $(window).on('load', function () {
        if (typeof i18n !== 'undefined') {
            const currentLang = i18n.lng() || 'zh';
            if (currentLang === 'en') {
                // 仅更新UI显示，不刷新页面
                $('.current-language').text('English');
                $('#current-flag').attr('src', '/img/flags/16/United-States.png');
                $('.plunex-language-option').removeClass('active');
                $('.set_en').addClass('active');
            } else {
                // 仅更新UI显示，不刷新页面
                $('.current-language').text('中文');
                $('#current-flag').attr('src', '/img/flags/16/China.png');
                $('.plunex-language-option').removeClass('active');
                $('.set_zh').addClass('active');
            }
        }
    });

    // 禁用原有的侧边栏收缩功能
    $('.navbar-minimalize').off('click').on('click', function (e) {
        e.preventDefault();
        return false;
    });

    // 确保侧边栏始终展开
    $('body').removeClass('mini-navbar');
});