/**
 * Created by chen on 12/15 0015.
 */
function myjs(p) {
    //获取随机串
    var rps = "";
    $.ajax({
        url:"/system/user/getRandom",
        data:{},
        type:"post",
        async: false ,
        success:function(data){
            if (data !='') {
                var r = data.substring(0 ,3);
                var r1 = r.substring(0,1).charCodeAt().toString(2);
                var r2 = r.substring(1,2).charCodeAt().toString(2);
                var r3 = r.substring(2,3).charCodeAt().toString(2);
                //0: 0000 1: 0001 2: 0010 3: 0011 4: 0100 5: 0101 6: 0110 7: 0111 8: 1000 9: 1001
                var ps = strToHexCharCode(p);
                var len = ps.length;
                rps =  len + "k" +r3 + ps.substring(0 ,len/2) + r1 + ps.substring(len/2 ,len) +r2 +data.substring(3 ,data.length);
            } else {
                alert("get the radom fail")
            }

        },
        error: function(){
            alert("get the random fail")
        }
    });
    console.log(rps)
    return rps;
}
function strToHexCharCode(str) {
    if(str === "")
        return "";
    var hexCharCode = [];
    hexCharCode.push("");
    for(var i = 0; i < str.length; i++) {
        console.info(str.charCodeAt(i).toString(16));
        hexCharCode.push((str.charCodeAt(i)).toString(16).toUpperCase());
    }
    return hexCharCode.join("");
}