/*!
 * Viewer v0.6.0
 * https://github.com/fengyuanchen/viewer
 *
 * Copyright (c) 2014-2017 <PERSON><PERSON>
 * Released under the MIT license
 *
 * Date: 2017-10-07T09:53:36.889Z
 */
!function(i,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e(i.jQuery)}(this,function(i){"use strict";function e(i){return"string"==typeof i}function t(i){return"number"==typeof i&&!C(i)}function s(i){return void 0===i}function n(i,e){for(var t=arguments.length,s=Array(t>2?t-2:0),n=2;n<t;n++)s[n-2]=arguments[n];return function(){for(var t=arguments.length,n=Array(t),a=0;a<t;a++)n[a]=arguments[a];return i.apply(e,s.concat(n))}}function a(i){var e=i.rotate,s=i.scaleX,n=i.scaleY,a=[];return t(e)&&0!==e&&a.push("rotate("+e+"deg)"),t(s)&&1!==s&&a.push("scaleX("+s+")"),t(n)&&1!==n&&a.push("scaleY("+n+")"),a.length>0?a.join(" "):"none"}function o(i){return e(i)?i.replace(/^.*\//,"").replace(/[?&#].*$/,""):""}function r(i,e){if(i.naturalWidth)e(i.naturalWidth,i.naturalHeight);else{var t=document.createElement("img");t.onload=function(){e(t.width,t.height)},t.src=i.src}}function h(i){switch(i){case 2:return g;case 3:return w;case 4:return m;default:return""}}function l(e){var t=i.extend({},e),s=[];return i.each(e,function(e,n){delete t[e],i.each(t,function(i,e){var t=Math.abs(n.startX-e.startX),a=Math.abs(n.startY-e.startY),o=Math.abs(n.endX-e.endX),r=Math.abs(n.endY-e.endY),h=Math.sqrt(t*t+a*a),l=(Math.sqrt(o*o+r*r)-h)/h;s.push(l)})}),s.sort(function(i,e){return Math.abs(i)<Math.abs(e)}),s[0]}function d(e,t){var s=e.pageX,n=e.pageY,a={endX:s,endY:n};return t?a:i.extend({startX:s,startY:n},a)}function c(e){var t=0,s=0,n=0;return i.each(e,function(i,e){var a=e.startX,o=e.startY;t+=a,s+=o,n+=1}),t/=n,s/=n,{pageX:t,pageY:s}}function u(i,e){if(!(i instanceof e))throw new TypeError("Cannot call a class as a function")}i=i&&i.hasOwnProperty("default")?i.default:i;var v={inline:!1,button:!0,navbar:!0,title:!0,toolbar:!0,tooltip:!0,movable:!0,zoomable:!0,rotatable:!0,scalable:!0,transition:!0,fullscreen:!0,keyboard:!0,interval:5e3,minWidth:200,minHeight:100,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,zIndex:2015,zIndexInline:0,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null},f=window.PointerEvent,m="viewer-hide-md-down",w="viewer-hide-sm-down",g="viewer-hide-xs-down",p="viewer-in",b="viewer-transition",y=f?"pointerdown":"touchstart mousedown",x=f?"pointermove":"mousemove touchmove",$=f?"pointerup pointercancel":"touchend touchcancel mouseup",C=Number.isNaN||window.isNaN,z=Object.keys||function(e){var t=[];return i.each(e,function(i){t.push(i)}),t},k={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initContainer:function(){var e=i(window);this.container={width:e.innerWidth(),height:e.innerHeight()}},initViewer:function(){var e=this.options,t=this.$parent,s=void 0;e.inline&&(s={width:Math.max(t.width(),e.minWidth),height:Math.max(t.height(),e.minHeight)},this.parent=s),!this.fulled&&s||(s=this.container),this.viewer=i.extend({},s)},renderViewer:function(){this.options.inline&&!this.fulled&&this.$viewer.css(this.viewer)},initList:function(){var t=this.$element,s=this.options,n=this.$list,a=[];this.$images.each(function(t,n){var r=n.alt||o(n),h=n.src,l=s.url;h&&(e(l)?l=n.getAttribute(l):i.isFunction(l)&&(l=l.call(n,n)),a.push('<li><img src="'+h+'" data-action="view" data-index="'+t+'" data-original-url="'+(l||h)+'" alt="'+r+'"></li>'))}),n.html(a.join("")).find("img").one("load",{filled:!0},i.proxy(this.loadImage,this)),this.$items=n.children(),s.transition&&t.one("viewed",function(){n.addClass(b)})},renderList:function(i){var e=i||this.index,t=this.$items.eq(e).width(),s=t+1;this.$list.css({width:s*this.length,marginLeft:(this.viewer.width-t)/2-s*e})},resetList:function(){this.$list.empty().removeClass(b).css("margin-left",0)},initImage:function(e){var t=this,s=this.options,n=this.$image,a=this.viewer,o=this.$footer.height(),h=a.width,l=Math.max(a.height-o,o),d=this.image||{};r(n[0],function(n,a){var o=n/a,r=h,c=l;l*o>h?c=h/o:r=l*o;var u={naturalWidth:n,naturalHeight:a,aspectRatio:o,ratio:(r=Math.min(.9*r,n))/n,width:r,height:c=Math.min(.9*c,a),left:(h-r)/2,top:(l-c)/2},v=i.extend({},u);s.rotatable&&(u.rotate=d.rotate||0,v.rotate=0),s.scalable&&(u.scaleX=d.scaleX||1,u.scaleY=d.scaleY||1,v.scaleX=1,v.scaleY=1),t.image=u,t.initialImage=v,i.isFunction(e)&&e()})},renderImage:function(e){var t=this.image,s=this.$image;s.css({width:t.width,height:t.height,marginLeft:t.left,marginTop:t.top,transform:a(t)}),i.isFunction(e)&&(this.transitioning?s.one("transitionend",e):e())},resetImage:function(){this.$image&&(this.$image.remove(),this.$image=null)}},I={bind:function(){var e=this.$element,t=this.options;i.isFunction(t.view)&&e.on("view",t.view),i.isFunction(t.viewed)&&e.on("viewed",t.viewed),this.$viewer.on("click",i.proxy(this.click,this)).on("wheel mousewheel DOMMouseScroll",i.proxy(this.wheel,this)).on("dragstart",i.proxy(this.dragstart,this)),this.$canvas.on(y,i.proxy(this.pointerdown,this)),i(document).on(x,this.onPointerMove=n(this.pointermove,this)).on($,this.onPointerUp=n(this.pointerup,this)).on("keydown",this.onKeyDown=n(this.keydown,this)),i(window).on("resize",this.onResize=n(this.resize,this))},unbind:function(){var e=this.$element,t=this.options;i.isFunction(t.view)&&e.off("view",t.view),i.isFunction(t.viewed)&&e.off("viewed",t.viewed),this.$viewer.off("click",this.click).off("wheel mousewheel DOMMouseScroll",this.wheel).off("dragstart",this.dragstart),this.$canvas.off(y,this.pointerdown),i(document).off(x,this.onPointerMove).off($,this.onPointerUp).off("keydown",this.onKeyDown),i(window).off("resize",this.onResize)}},F={click:function(e){var t=i(e.target),s=t.data("action"),n=this.image;switch(s){case"mix":this.played?this.stop():this.options.inline?this.fulled?this.exit():this.full():this.hide();break;case"view":this.view(t.data("index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev();break;case"play":this.play();break;case"next":this.next();break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-n.scaleX||-1);break;case"flip-vertical":this.scaleY(-n.scaleY||-1);break;default:this.played&&this.stop()}},dragstart:function(e){i(e.target).is("img")&&e.preventDefault()},keydown:function(i){var e=this.options;if(this.fulled&&e.keyboard)switch(i.which){case 27:this.played?this.stop():e.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.prev();break;case 38:i.preventDefault(),this.zoom(e.zoomRatio,!0);break;case 39:this.next();break;case 40:i.preventDefault(),this.zoom(-e.zoomRatio,!0);break;case 48:case 49:(i.ctrlKey||i.shiftKey)&&(i.preventDefault(),this.toggle())}},load:function(){var i=this,e=this.options,t=this.viewer,s=this.$image;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1),s&&(s.removeClass("viewer-invisible").css("cssText","width:0;height:0;margin-left:"+t.width/2+"px;margin-top:"+t.height/2+"px;max-width:none!important;visibility:visible;"),this.initImage(function(){s.toggleClass(b,e.transition).toggleClass("viewer-move",e.movable),i.renderImage(function(){i.viewed=!0,i.trigger("viewed")})}))},loadImage:function(e){var t=e.target,s=i(t),n=s.parent(),a=n.width(),o=n.height(),h=e.data&&e.data.filled;r(t,function(i,e){var t=i/e,n=a,r=o;o*t>a?h?n=o*t:r=a/t:h?r=a/t:n=o*t,s.css({width:n,height:r,marginLeft:(a-n)/2,marginTop:(o-r)/2})})},pointerdown:function(e){if(this.viewed&&!this.transitioning){var t=this.options,s=this.pointers,n=e.originalEvent;n&&n.changedTouches?i.each(n.changedTouches,function(i,e){s[e.identifier]=d(e)}):s[n&&n.pointerId||0]=d(n||e);var a=!!t.movable&&"move";z(s).length>1?a="zoom":"touch"!==e.pointerType&&"touchmove"!==e.type||!this.isSwitchable()||(a="switch"),this.action=a}},pointermove:function(e){var t=this.$image,s=this.action,n=this.pointers;if(this.viewed&&s){e.preventDefault();var a=e.originalEvent;a&&a.changedTouches?i.each(a.changedTouches,function(e,t){i.extend(n[t.identifier],d(t,!0))}):i.extend(n[a&&a.pointerId||0],d(e,!0)),"move"===s&&this.options.transition&&t.hasClass(b)&&t.removeClass(b),this.change(e)}},pointerup:function(e){if(this.viewed){var t=this.action,s=this.pointers,n=e.originalEvent;n&&n.changedTouches?i.each(n.changedTouches,function(i,e){delete s[e.identifier]}):delete s[n&&n.pointerId||0],t&&("move"===t&&this.options.transition&&this.$image.addClass(b),this.action=!1)}},resize:function(){var e=this;if(this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){e.renderImage()}),this.played){if(this.options.fullscreen&&this.fulled&&!document.fullscreenElement&&!document.mozFullScreenElement&&!document.webkitFullscreenElement&&!document.msFullscreenElement)return void this.stop();this.$player.find("img").one("load",i.proxy(this.loadImage,this)).trigger("load")}},start:function(e){var t=e.target;i(t).is("img")&&(this.target=t,this.show())},wheel:function(i){var e=this;if(this.viewed&&(i.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout(function(){e.wheeling=!1},50);var t=i.originalEvent||i,s=1;t.deltaY?s=t.deltaY>0?1:-1:t.wheelDelta?s=-t.wheelDelta/120:t.detail&&(s=t.detail>0?1:-1),this.zoom(-s*(Number(this.options.zoomRatio)||.1),!0,i)}}},T={show:function(){var e=this,t=this.$element,s=this.options;if(!s.inline&&!this.transitioning){this.ready||this.build();var n=this.$viewer;i.isFunction(s.show)&&t.one("show",s.show),this.trigger("show").isDefaultPrevented()||(this.$body.addClass("viewer-open"),n.removeClass("viewer-hide"),t.one("shown",function(){e.view(e.target?e.$images.index(e.target):e.index),e.target=!1}),s.transition?(this.transitioning=!0,n.addClass(b),n[0].offsetWidth,n.one("transitionend",i.proxy(this.shown,this)).addClass(p)):(n.addClass(p),this.shown()))}},hide:function(){var e=this,t=this.options,s=this.$viewer;t.inline||this.transitioning||!this.visible||(i.isFunction(t.hide)&&this.$element.one("hide",t.hide),this.trigger("hide").isDefaultPrevented()||(this.viewed&&t.transition?(this.transitioning=!0,this.$image.one("transitionend",function(){s.one("transitionend",i.proxy(e.hidden,e)).removeClass(p)}),this.zoomTo(0,!1,!1,!0)):(s.removeClass(p),this.hidden())))},view:function(e){var t=this;if(e=Number(e)||0,!(!this.visible||this.played||e<0||e>=this.length||this.viewed&&e===this.index||this.trigger("view").isDefaultPrevented())){var s=this.$items.eq(e),n=s.find("img"),a=n.attr("alt"),o=i('<img src="'+n.data("originalUrl")+'" alt="'+a+'">');this.$image=o,this.$items.eq(this.index).removeClass("viewer-active"),s.addClass("viewer-active"),this.viewed=!1,this.index=e,this.image=null,this.$canvas.html(o.addClass("viewer-invisible")),this.renderList();var r=this.$title;r.empty(),this.$element.one("viewed",function(){var i=t.image,e=i.naturalWidth,s=i.naturalHeight;r.html(a+" ("+e+" &times; "+s+")")}),o[0].complete?this.load():(o.one("load",i.proxy(this.load,this)),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout(function(){o.removeClass("viewer-invisible"),t.timeout=!1},1e3))}},prev:function(){this.view(Math.max(this.index-1,0))},next:function(){this.view(Math.min(this.index+1,this.length-1))},move:function(i,e){var t=this.image,n=t.left,a=t.top;this.moveTo(s(i)?i:n+Number(i),s(e)?e:a+Number(e))},moveTo:function(i){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;if(this.viewed&&!this.played&&this.options.movable){var s=this.image,n=!1;i=Number(i),e=Number(e),t(i)&&(s.left=i,n=!0),t(e)&&(s.top=e,n=!0),n&&this.renderImage()}},zoom:function(i){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,s=this.image;i=(i=Number(i))<0?1/(1-i):1+i,this.zoomTo(s.width*i/s.naturalWidth,e,t)},zoomTo:function(i){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=this.options,o=this.image,r=this.pointers;if(i=Math.max(0,i),t(i)&&this.viewed&&!this.played&&(n||a.zoomable)){if(!n){var h=Math.max(.01,a.minZoomRatio),l=Math.min(100,a.maxZoomRatio);i=Math.min(Math.max(i,h),l)}s&&i>.95&&i<1.05&&(i=1);var d=o.naturalWidth*i,u=o.naturalHeight*i;if(s&&s.originalEvent){var v=this.$viewer.offset(),f=r&&z(r).length>0?c(r):{pageX:s.pageX||s.originalEvent.pageX||0,pageY:s.pageY||s.originalEvent.pageY||0};o.left-=(d-o.width)*((f.pageX-v.left-o.left)/o.width),o.top-=(u-o.height)*((f.pageY-v.top-o.top)/o.height)}else o.left-=(d-o.width)/2,o.top-=(u-o.height)/2;o.width=d,o.height=u,o.ratio=i,this.renderImage(),e&&this.tooltip()}},rotate:function(i){this.rotateTo((this.image.rotate||0)+Number(i))},rotateTo:function(i){var e=this.image;t(i=Number(i))&&this.viewed&&!this.played&&this.options.rotatable&&(e.rotate=i,this.renderImage())},scaleX:function(i){this.scale(i,this.image.scaleY)},scaleY:function(i){this.scale(this.image.scaleX,i)},scale:function(i){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;if(this.viewed&&!this.played&&this.options.scalable){var s=this.image,n=!1;i=Number(i),e=Number(e),t(i)&&(s.scaleX=i,n=!0),t(e)&&(s.scaleY=e,n=!0),n&&this.renderImage()}},play:function(){var e=this;if(this.visible&&!this.played){var s=this.options,n=this.$items,a=this.$player;s.fullscreen&&this.requestFullscreen(),this.played=!0,a.addClass("viewer-show");var o=[],r=0;if(n.each(function(t,n){var h=i(n),l=h.find("img"),d=i('<img src="'+l.data("originalUrl")+'" alt="'+l.attr("alt")+'">');d.addClass("viewer-fade").toggleClass(b,s.transition),h.hasClass("viewer-active")&&(d.addClass(p),r=t),o.push(d),d.one("load",{filled:!1},i.proxy(e.loadImage,e)),a.append(d)}),t(s.interval)&&s.interval>0){var h=n.length;h>1&&function i(){e.playing=setTimeout(function(){o[r].removeClass(p),o[r=(r+=1)<h?r:0].addClass(p),i()},s.interval)}()}}},stop:function(){this.played&&(this.options.fullscreen&&this.exitFullscreen(),this.played=!1,clearTimeout(this.playing),this.$player.removeClass("viewer-show").empty())},full:function(){var e=this,t=this.options,s=this.$image,n=this.$list;this.visible&&!this.played&&!this.fulled&&t.inline&&(this.fulled=!0,this.$body.addClass("viewer-open"),this.$button.addClass("viewer-fullscreen-exit"),t.transition&&(s.removeClass(b),n.removeClass(b)),this.$viewer.addClass("viewer-fixed").removeAttr("style").css("z-index",t.zIndex),this.initContainer(),this.viewer=i.extend({},this.container),this.renderList(),this.initImage(function(){e.renderImage(function(){t.transition&&setTimeout(function(){s.addClass(b),n.addClass(b)},0)})}))},exit:function(){var e=this;if(this.fulled){var t=this.options,s=this.$image,n=this.$list;this.fulled=!1,this.$body.removeClass("viewer-open"),this.$button.removeClass("viewer-fullscreen-exit"),t.transition&&(s.removeClass(b),n.removeClass(b)),this.$viewer.removeClass("viewer-fixed").css("z-index",t.zIndexInline),this.viewer=i.extend({},this.parent),this.renderViewer(),this.renderList(),this.initImage(function(){e.renderImage(function(){t.transition&&setTimeout(function(){s.addClass(b),n.addClass(b)},0)})})}},tooltip:function(){var i=this,e=this.options,t=this.$tooltip,s=this.image,n=["viewer-show","viewer-fade",b].join(" ");this.viewed&&!this.played&&e.tooltip&&(t.text(Math.round(100*s.ratio)+"%"),this.tooltiping?clearTimeout(this.tooltiping):e.transition?(this.fading&&t.trigger("transitionend"),t.addClass(n),t[0].offsetWidth,t.addClass(p)):t.addClass("viewer-show"),this.tooltiping=setTimeout(function(){e.transition?(t.one("transitionend",function(){t.removeClass(n),i.fading=!1}).removeClass(p),i.fading=!0):t.removeClass("viewer-show"),i.tooltiping=!1},1e3))},toggle:function(){1===this.image.ratio?this.zoomTo(this.initialImage.ratio,!0):this.zoomTo(1,!0)},reset:function(){this.viewed&&!this.played&&(this.image=i.extend({},this.initialImage),this.renderImage())},update:function(){var e=this.$element,t=this.$images;if(this.isImg){if(!e.parent().length)return void this.destroy()}else t=e.find("img"),this.$images=t,this.length=t.length;if(this.ready){var s=[],n=void 0;i.each(this.$items,function(e,n){var a=i(n).find("img")[0],o=t[e];o?o.src!==a.src&&s.push(e):s.push(e)}),this.$list.width("auto"),this.initList(),this.visible&&(this.length?this.viewed&&((n=i.inArray(this.index,s))>=0?(this.viewed=!1,this.view(Math.max(this.index-(n+1),0))):this.$items.eq(this.index).addClass("viewer-active")):(this.$image=null,this.viewed=!1,this.index=0,this.image=null,this.$canvas.empty(),this.$title.empty()))}},destroy:function(){var i=this.$element;this.options.inline?this.unbind():(this.visible&&this.unbind(),i.off("click",this.start)),this.unbuild(),i.removeData("viewer")}},Y=window.document,M={trigger:function(e,t){var s=i.Event(e,t);return this.$element.trigger(s),s},shown:function(){var e=this.options;this.transitioning=!1,this.fulled=!0,this.visible=!0,this.render(),this.bind(),i.isFunction(e.shown)&&this.$element.one("shown",e.shown),this.trigger("shown")},hidden:function(){var e=this.options;this.transitioning=!1,this.viewed=!1,this.fulled=!1,this.visible=!1,this.unbind(),this.$body.removeClass("viewer-open"),this.$viewer.addClass("viewer-hide"),this.resetList(),this.resetImage(),i.isFunction(e.hidden)&&this.$element.one("hidden",e.hidden),this.trigger("hidden")},requestFullscreen:function(){if(this.fulled&&!Y.fullscreenElement&&!Y.mozFullScreenElement&&!Y.webkitFullscreenElement&&!Y.msFullscreenElement){var i=Y.documentElement;i.requestFullscreen?i.requestFullscreen():i.msRequestFullscreen?i.msRequestFullscreen():i.mozRequestFullScreen?i.mozRequestFullScreen():i.webkitRequestFullscreen&&i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)}},exitFullscreen:function(){this.fulled&&(Y.exitFullscreen?Y.exitFullscreen():Y.msExitFullscreen?Y.msExitFullscreen():Y.mozCancelFullScreen?Y.mozCancelFullScreen():Y.webkitExitFullscreen&&Y.webkitExitFullscreen())},change:function(e){var t=this.pointers,s=t[Object.keys(t)[0]],n=s.endX-s.startX,a=s.endY-s.startY;switch(this.action){case"move":this.move(n,a);break;case"zoom":this.zoom(l(t),!1,e),this.startX2=this.endX2,this.startY2=this.endY2;break;case"switch":this.action="switched",Math.abs(n)>Math.abs(a)&&(n>1?this.prev():n<-1&&this.next())}i.each(t,function(i,e){e.startX=e.endX,e.startY=e.endY})},isSwitchable:function(){var i=this.image,e=this.viewer;return i.left>=0&&i.top>=0&&i.width<=e.width&&i.height<=e.height}},X=function(){function i(i,e){for(var t=0;t<e.length;t++){var s=e[t];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(i,s.key,s)}}return function(e,t,s){return t&&i(e.prototype,t),s&&i(e,s),e}}(),E=function(){function e(t){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(u(this,e),!t||1!==t.nodeType)throw new Error("The first argument is required and must be an element.");this.element=t,this.$element=i(t),this.options=i.extend({},v,i.isPlainObject(s)&&s),this.action="",this.target=null,this.timeout=null,this.index=0,this.length=0,this.ready=!1,this.fading=!1,this.fulled=!1,this.isImg=!1,this.played=!1,this.playing=!1,this.tooltiping=!1,this.transitioning=!1,this.viewed=!1,this.visible=!1,this.wheeling=!1,this.pointers={},this.init()}return X(e,[{key:"init",value:function(){var e=this,t=this.$element,s=this.options,n=t.is("img"),a=n?t:t.find("img"),o=a.length;o&&(void 0===document.createElement("viewer").style.transition&&(s.transition=!1),this.isImg=n,this.length=o,this.count=0,this.$images=a,this.$body=i("body"),s.inline?(t.one("ready",function(){e.view()}),a.each(function(t,s){s.complete?e.progress():i(s).one("load",i.proxy(e.progress,e))})):t.on("click",i.proxy(this.start,this)))}},{key:"progress",value:function(){this.count+=1,this.count===this.length&&this.build()}},{key:"build",value:function(){var e=this.$element,t=this.options;if(!this.ready){var s=e.parent(),n=i('<div class="viewer-container"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><ul class="viewer-toolbar"><li role="button" class="viewer-zoom-in" data-action="zoom-in"></li><li role="button" class="viewer-zoom-out" data-action="zoom-out"></li><li role="button" class="viewer-one-to-one" data-action="one-to-one"></li><li role="button" class="viewer-reset" data-action="reset"></li><li role="button" class="viewer-prev" data-action="prev"></li><li role="button" class="viewer-play" data-action="play"></li><li role="button" class="viewer-next" data-action="next"></li><li role="button" class="viewer-rotate-left" data-action="rotate-left"></li><li role="button" class="viewer-rotate-right" data-action="rotate-right"></li><li role="button" class="viewer-flip-horizontal" data-action="flip-horizontal"></li><li role="button" class="viewer-flip-vertical" data-action="flip-vertical"></li></ul><div class="viewer-navbar"><ul class="viewer-list"></ul></div></div><div class="viewer-tooltip"></div><div role="button" class="viewer-button" data-action="mix"></div><div class="viewer-player"></div></div>'),a=n.find(".viewer-button"),o=n.find(".viewer-navbar"),r=n.find(".viewer-title"),l=n.find(".viewer-toolbar");this.$parent=s,this.$viewer=n,this.$button=a,this.$navbar=o,this.$title=r,this.$toolbar=l,this.$canvas=n.find(".viewer-canvas"),this.$footer=n.find(".viewer-footer"),this.$list=n.find(".viewer-list"),this.$player=n.find(".viewer-player"),this.$tooltip=n.find(".viewer-tooltip"),r.addClass(t.title?h(t.title):"viewer-hide"),l.addClass(t.toolbar?h(t.toolbar):"viewer-hide"),l.find("li[class*=zoom]").toggleClass("viewer-invisible",!t.zoomable),l.find("li[class*=flip]").toggleClass("viewer-invisible",!t.scalable),t.rotatable||l.find("li[class*=rotate]").addClass("viewer-invisible").appendTo(l),o.addClass(t.navbar?h(t.navbar):"viewer-hide"),a.toggleClass("viewer-hide",!t.button),t.inline?(a.addClass("viewer-fullscreen"),n.css("z-index",t.zIndexInline),"static"===s.css("position")&&s.css("position","relative"),e.after(n)):(a.addClass("viewer-close"),n.css("z-index",t.zIndex).addClass(["viewer-fixed","viewer-fade","viewer-hide"].join(" ")).appendTo("body")),t.inline&&(this.render(),this.bind(),this.visible=!0),this.ready=!0,i.isFunction(t.ready)&&e.one("ready",t.ready),this.trigger("ready")}}},{key:"unbuild",value:function(){this.ready&&(this.ready=!1,this.$viewer.remove())}}],[{key:"setDefaults",value:function(e){i.extend(v,e)}}]),e}();i.extend(E.prototype,k,I,F,T,M);var q=i.fn.viewer;i.fn.viewer=function(t){for(var n=arguments.length,a=Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];var r=void 0;return this.each(function(s,n){var o=i(n),h=o.data("viewer");if(!h){if(/destroy/.test(t))return;var l=i.extend({},o.data(),i.isPlainObject(t)&&t);h=new E(n,l),o.data("viewer",h)}if(e(t)){var d=h[t];i.isFunction(d)&&(r=d.apply(h,a))}}),s(r)?this:r},i.fn.viewer.Constructor=E,i.fn.viewer.setDefaults=E.setDefaults,i.fn.viewer.noConflict=function(){return i.fn.viewer=q,this}});