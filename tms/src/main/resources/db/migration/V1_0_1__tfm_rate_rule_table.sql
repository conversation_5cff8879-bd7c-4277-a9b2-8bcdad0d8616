-- 创建手续费参数管理表
CREATE TABLE IF NOT EXISTS `tfm_rate_rule` (
    `bus_type` varchar(8) NOT NULL COMMENT '业务类型',
    `bus_type_desc` varchar(16) NOT NULL COMMENT '业务类型描述',
    `ccy` varchar(8) NOT NULL COMMENT '币种',
    `calculate_mod` varchar(8) NOT NULL COMMENT '计费模式 internal:内扣 external:外扣',
    `calculate_type` varchar(8) NOT NULL COMMENT '计费方式 percent:百分比 fixed:固定手续费',
    `rate` decimal(15,4) DEFAULT '0.0000' COMMENT '费率',
    `fix_fee` decimal(15,2) DEFAULT '0.00' COMMENT '固定手续费',
    `charge_type` varchar(16) NOT NULL COMMENT '收费方式 single:单笔 cycle:固定周期',
    `calculate_min_amt` decimal(15,2) DEFAULT '0.00' COMMENT '计费起始金额',
    `min_fee` decimal(15,2) DEFAULT '0.00' COMMENT '最低收取费用',
    `max_fee` decimal(15,2) DEFAULT '9999999999999.99' COMMENT '最高收取费用',
    `stats` char(1) DEFAULT '1' COMMENT '状态0:无效 1:生效',
    `eff_date` date NOT NULL COMMENT '生效日期',
    `exp_date` date NOT NULL COMMENT '失效日期',
    `opr_id` varchar(8) NOT NULL COMMENT '操作员ID',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `modify_time` datetime NOT NULL COMMENT '修改时间',
    `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
    PRIMARY KEY (`bus_type`) USING BTREE,
    UNIQUE KEY `idx_tfm_rate_rule_1` (`bus_type`) USING BTREE,
    KEY `idx_tfm_rate_rule_2` (`eff_date`,`exp_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='基础费率规则表';