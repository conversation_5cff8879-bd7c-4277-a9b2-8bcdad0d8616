-- 创建常见问题表
CREATE TABLE IF NOT EXISTS `question_answer_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `question_content` varchar(300) NOT NULL COMMENT '问题内容',
    `answer_content` varchar(300) NOT NULL COMMENT '答案内容',
    `questino_title` varchar(300) NOT NULL COMMENT '问题标题',
    `created_date` datetime DEFAULT NULL,
    `created_by` varchar(30) DEFAULT NULL,
    `updated_date` datetime DEFAULT NULL,
    `updated_by` varchar(30) DEFAULT NULL,
    `deleted_flag` varchar(1) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8 ROW_FORMAT = DYNAMIC COMMENT = '常见问题信息表';