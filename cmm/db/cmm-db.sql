--错误码
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10001','zh','手机号码不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10002','zh','短信验证码类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10003','zh','短信验证码不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10004','zh','短信类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10005','zh','短信模版编号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10006','zh','语种不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10007','zh','用户号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10008','zh','付款码不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10009','zh','订单号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10011','zh','短信内容不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10012','zh','二维码类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10013','zh','交易金额不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10015','zh','二维码参数信息不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10017','zh','短信验证码token不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10018','zh','消息类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10019','zh','消息模版编号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10021','zh','消息内容不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10022','zh','用户类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10023','zh','密钥索引不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10024','zh','消息推送绑定ID不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10025','zh','加解密数据不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM10026','zh','加解密类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30001','zh','短信验证码下发失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30002','zh','短信下发失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30003','zh','短信验证码校验失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30005','zh','短信模版不存在或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30006','zh','短信模版配置有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30007','zh','二维码生成失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30010','zh','付款码有误或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30011','zh','收款码有误或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30015','zh','手机号段信息不存在或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30016','zh','短信参数信息不存在或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30017','zh','短信下发次数超限!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30018','zh','消息模版不存在或失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30019','zh','消息模版配置有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30020','zh','消息推送失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30021','zh','消息列表查询失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30027','zh','密钥不存在或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30028','zh','消息推送绑定ID设置失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30029','zh','消息推送绑定ID不存在或失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30030','zh','暂不支持此种支付方式!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30031','zh','手机号无效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('CMM30032','zh','加解密失败!',now(),now());

--drop table cmm_area_public
--区域参数表
CREATE TABLE IF NOT EXISTS cmm_area_public (
    id INT NOT NULL COMMENT '地区ID',
    parent_id INT NOT NULL COMMENT '父级ID',
    area_name_kh VARCHAR(128) NOT NULL COMMENT '柬文名称',
    area_name_zh VARCHAR(128) NOT NULL COMMENT '中文名称',
    area_name_en VARCHAR(128) NOT NULL COMMENT '英文名称',
    sort INT NOT NULL COMMENT '排序规则',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_area_public ADD CONSTRAINT pk_cmm_area_public PRIMARY KEY(id);
--索引
ALTER TABLE cmm_area_public ADD UNIQUE idx_cmm_area_public_1(id);

--区域sql
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (0, -1, '中国', 0, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (100, 0, '安徽', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (101, 100, '合肥', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (102, 100, '安庆', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (103, 100, '毫州', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (104, 100, '蚌埠', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (105, 100, '滁州', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (106, 100, '巢湖', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (107, 100, '池州', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (108, 100, '阜阳', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (109, 100, '淮北', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (110, 100, '淮南', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (111, 100, '黄山站', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (112, 100, '六安', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (113, 100, '马鞍山', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (114, 100, '宿州', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (115, 100, '铜陵', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (116, 100, '芜湖', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (117, 100, '宣城', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (200, 0, '澳门', 2, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (201, 200, '澳门', 2, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (300, 0, '北京', 3, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (301, 300, '北京', 3, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (400, 0, '重庆', 4, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (401, 400, '重庆', 4, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (500, 0, '福建', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (501, 500, '福州', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (502, 500, '龙岩', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (503, 500, '南平', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (504, 500, '宁德', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (505, 500, '莆田', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (506, 500, '泉州', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (507, 500, '三明', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (508, 500, '厦门', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (509, 500, '漳州', 5, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (600, 0, '甘肃', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (601, 600, '兰州', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (602, 600, '白银', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (603, 600, '定西', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (604, 600, '合作', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (605, 600, '金昌', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (606, 600, '酒泉', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (607, 600, '嘉峪关', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (608, 600, '临夏', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (609, 600, '平凉', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (610, 600, '庆阳', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (611, 600, '天水', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (612, 600, '武威', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (613, 600, '武都', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (614, 600, '张掖', 6, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (700, 0, '广东', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (701, 700, '广州', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (702, 700, '潮州', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (703, 700, '东莞', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (704, 700, '佛山', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (705, 700, '河源', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (706, 700, '惠州', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (707, 700, '江门', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (708, 700, '揭阳', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (709, 700, '梅州', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (710, 700, '茂名', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (711, 700, '清远', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (712, 700, '深圳', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (713, 700, '汕头', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (714, 700, '韶关', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (715, 700, '汕尾', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (716, 700, '阳江', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (717, 700, '云浮', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (718, 700, '珠海', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (719, 700, '中山', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (720, 700, '湛江', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (721, 700, '肇庆', 7, 'sys', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '2017-8-28 16:42:52', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (800, 0, '广西', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (801, 800, '南宁', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (802, 800, '北海', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (803, 800, '白色', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (804, 800, '崇左', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (805, 800, '防城港', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (806, 800, '桂林', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (807, 800, '贵港', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (808, 800, '贺州', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (809, 800, '河池', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (810, 800, '柳州', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (811, 800, '来宾', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (812, 800, '钦州', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (813, 800, '梧州', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (814, 800, '玉林', 8, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (900, 0, '贵州', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (901, 900, '贵阳', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (902, 900, '安顺', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (903, 900, '毕节', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (904, 900, '都匀', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (905, 900, '凯里', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (906, 900, '六盘水', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (907, 900, '晴隆', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (908, 900, '铜仁', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (909, 900, '兴义', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (910, 900, '遵义', 9, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1000, 0, '海南', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1001, 1000, '海口', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1002, 1000, '白沙', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1003, 1000, '保亭', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1004, 1000, '澄迈', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1005, 1000, '昌江', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1006, 1000, '儋州', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1007, 1000, '定安', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1008, 1000, '东方', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1009, 1000, '临高', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1010, 1000, '陵水', 10, 'sys', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '2017-8-28 16:42:53', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1011, 1000, '乐东', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1012, 1000, '南沙岛', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1013, 1000, '琼海', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1014, 1000, '琼中', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1015, 1000, '三亚', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1016, 1000, '屯昌', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1017, 1000, '五指山', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1018, 1000, '文昌', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1019, 1000, '万宁', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1020, 1000, '西沙', 10, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1100, 0, '河北', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1101, 1100, '石家庄', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1102, 1100, '保定', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1103, 1100, '承德', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1104, 1100, '沧州', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1105, 1100, '衡水', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1106, 1100, '邯郸', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1107, 1100, '廊坊', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1108, 1100, '秦皇岛', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1109, 1100, '唐山', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1110, 1100, '邢台', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1111, 1100, '张家口', 11, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1200, 0, '河南', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1201, 1200, '郑州', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1202, 1200, '安阳', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1203, 1200, '鹤壁', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1204, 1200, '焦作', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1205, 1200, '济源', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1206, 1200, '开封', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1207, 1200, '洛阳', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1208, 1200, '漯河', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1209, 1200, '南阳', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1210, 1200, '濮阳', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1211, 1200, '平顶山', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1212, 1200, '三门峡', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1213, 1200, '商丘', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1214, 1200, '新乡', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1215, 1200, '许昌', 12, 'sys', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '2017-8-28 16:42:54', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1216, 1200, '信阳', 12, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1217, 1200, '周口', 12, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1218, 1200, '驻马店', 12, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1300, 0, '黑龙江', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1301, 1300, '哈尔滨', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1302, 1300, '大庆', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1303, 1300, '大兴安岭', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1304, 1300, '鹤岗', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1305, 1300, '黑河', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1306, 1300, '佳木斯', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1307, 1300, '鸡西', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1308, 1300, '牡丹江', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1309, 1300, '齐齐哈尔', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1310, 1300, '七台河', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1311, 1300, '双鸭山', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1312, 1300, '绥化', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1313, 1300, '伊春', 13, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1400, 0, '湖北', 14, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1401, 1400, '武汉', 14, 'sys', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '2017-8-28 16:42:55', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1402, 1400, '鄂州', 14, 'sys', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1403, 1400, '恩施', 14, 'sys', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1404, 1400, '黄石', 14, 'sys', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1405, 1400, '黄冈', 14, 'sys', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1406, 1400, '荆州', 14, 'sys', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1407, 1400, '荆门', 14, 'sys', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '2017-8-28 16:42:56', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1408, 1400, '潜江', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1409, 1400, '十堰', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1410, 1400, '随州', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1411, 1400, '神农架', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1412, 1400, '天门', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1413, 1400, '襄阳', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1414, 1400, '孝感', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1415, 1400, '咸宁', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1416, 1400, '仙桃', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1417, 1400, '宜昌', 14, 'sys', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '2017-8-28 16:42:57', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1500, 0, '湖南', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1501, 1500, '长沙', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1502, 1500, '常德', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1503, 1500, '郴州', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1504, 1500, '衡阳', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1505, 1500, '怀化', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1506, 1500, '吉首', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1507, 1500, '娄底', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1508, 1500, '黔阳', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1509, 1500, '邵阳', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1510, 1500, '湘潭', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1511, 1500, '岳阳', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1512, 1500, '益阳', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1513, 1500, '永州', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1514, 1500, '株洲', 15, 'sys', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '2017-8-28 16:42:58', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1515, 1500, '张家界', 15, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1600, 0, '吉林', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1601, 1600, '长春', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1602, 1600, '白山', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1603, 1600, '白城', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1604, 1600, '吉林', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1605, 1600, '辽源', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1606, 1600, '四平', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1607, 1600, '松原', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1608, 1600, '通化', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1609, 1600, '延吉', 16, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1700, 0, '江苏', 17, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1701, 1700, '南京', 17, 'sys', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '2017-8-28 16:42:59', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1702, 1700, '常州', 17, 'sys', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1703, 1700, '淮安', 17, 'sys', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1704, 1700, '连云港', 17, 'sys', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1705, 1700, '南通', 17, 'sys', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1706, 1700, '苏州', 17, 'sys', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '2017-8-28 16:43:00', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1707, 1700, '宿迁', 17, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1708, 1700, '秦州', 17, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1709, 1700, '无锡', 17, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1710, 1700, '徐州', 17, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1711, 1700, '盐城', 17, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1712, 1700, '扬州', 17, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1713, 1700, '镇江', 17, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1800, 0, '江西', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1801, 1800, '南昌', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1802, 1800, '抚州', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1803, 1800, '赣州', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1804, 1800, '九江', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1805, 1800, '景德镇', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1806, 1800, '吉安', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1807, 1800, '萍乡', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1808, 1800, '上饶', 18, 'sys', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '2017-8-28 16:43:01', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1809, 1800, '新余', 18, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1810, 1800, '鹰潭', 18, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1811, 1800, '宜春', 18, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1900, 0, '辽宁', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1901, 1900, '沈阳', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1902, 1900, '鞍山', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1903, 1900, '本溪', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1904, 1900, '朝阳', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1905, 1900, '大连', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1906, 1900, '丹东', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1907, 1900, '抚顺', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1908, 1900, '阜新', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1909, 1900, '葫芦岛', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1910, 1900, '锦州', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1911, 1900, '辽阳', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1912, 1900, '盘锦', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1913, 1900, '铁岭', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1914, 1900, '营口', 19, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2000, 0, '内蒙古', 20, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2001, 2000, '呼和浩特', 20, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2002, 2000, '阿拉善左旗', 20, 'sys', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '2017-8-28 16:43:02', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2003, 2000, '包头', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2004, 2000, '赤峰', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2005, 2000, '鄂尔多斯', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2006, 2000, '呼伦贝尔', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2007, 2000, '集宁', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2008, 2000, '临河', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2009, 2000, '通辽', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2010, 2000, '乌兰浩特', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2011, 2000, '乌海', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2012, 2000, '锡林浩特', 20, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2100, 0, '宁夏', 21, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2101, 2100, '银川', 21, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2102, 2100, '固原', 21, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2103, 2100, '石嘴山', 21, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2104, 2100, '吴忠', 21, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2105, 2100, '中卫', 21, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2200, 0, '青海', 22, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2201, 2200, '西宁', 22, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2202, 2200, '果洛', 22, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2203, 2200, '海东', 22, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2204, 2200, '海南', 22, 'sys', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '2017-8-28 16:43:03', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2205, 2200, '海北', 22, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2206, 2200, '海西', 22, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2207, 2200, '黄南', 22, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2208, 2200, '玉树', 22, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2300, 0, '山东', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2301, 2300, '济南', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2302, 2300, '滨州', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2303, 2300, '东营', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2304, 2300, '德州', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2305, 2300, '菏泽', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2306, 2300, '济宁', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2307, 2300, '莱芜', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2308, 2300, '临沂', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2309, 2300, '聊城', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2310, 2300, '青岛', 23, 'sys', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '2017-8-28 16:43:04', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2311, 2300, '日照', 23, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2312, 2300, '泰安', 23, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2313, 2300, '潍坊', 23, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2314, 2300, '威海', 23, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2315, 2300, '烟台', 23, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2316, 2300, '淄博', 23, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2317, 2300, '枣庄', 23, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2400, 0, '山西', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2401, 2400, '太原', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2402, 2400, '长治', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2403, 2400, '大同', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2404, 2400, '晋城', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2405, 2400, '晋中', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2406, 2400, '临汾', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2407, 2400, '吕梁', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2408, 2400, '朔州', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2409, 2400, '忻州', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2410, 2400, '阳泉', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2411, 2400, '运城', 24, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2500, 0, '陕西', 25, 'sys', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '2017-8-28 16:43:05', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2501, 2500, '西安', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2502, 2500, '宝康', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2503, 2500, '宝鸡', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2504, 2500, '陈仓', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2505, 2500, '汉中', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2506, 2500, '商洛', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2507, 2500, '铜川', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2508, 2500, '渭南', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2509, 2500, '咸阳', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2510, 2500, '延安', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2511, 2500, '榆林', 25, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2600, 0, '上海', 26, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2601, 2600, '上海', 26, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2700, 0, '四川', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2701, 2700, '成都', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2702, 2700, '阿贝', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2703, 2700, '巴中', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2704, 2700, '德阳', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2705, 2700, '达州', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2706, 2700, '广元', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2707, 2700, '广安', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2708, 2700, '甘孜', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2709, 2700, '泸州', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2710, 2700, '乐山', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2711, 2700, '凉山', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2712, 2700, '绵阳', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2713, 2700, '眉山', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2714, 2700, '内江', 27, 'sys', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '2017-8-28 16:43:06', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2715, 2700, '南充', 27, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2716, 2700, '攀枝花', 27, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2717, 2700, '遂宁', 27, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2718, 2700, '宜宾', 27, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2719, 2700, '雅安', 27, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2720, 2700, '自贡', 27, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2721, 2700, '资阳', 27, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2800, 0, '天津', 28, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2801, 2800, '天津', 28, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2900, 0, '台湾', 29, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (2901, 2900, '台湾', 29, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3000, 0, '西藏', 30, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3001, 3000, '拉萨', 30, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3002, 3000, '阿里', 30, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3003, 3000, '昌都', 30, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3004, 3000, '林芝', 30, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3005, 3000, '那曲', 30, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3006, 3000, '日喀则', 30, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3007, 3000, '山南', 30, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3100, 0, '香港', 31, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3101, 3100, '香港', 31, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3200, 0, '新疆', 32, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3201, 3200, '乌鲁木齐', 32, 'sys', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '2017-8-28 16:43:07', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3202, 3200, '阿克苏', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3203, 3200, '阿图什', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3204, 3200, '阿勒泰', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3205, 3200, '阿拉尔', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3206, 3200, '博乐', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3207, 3200, '昌吉', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3208, 3200, '哈密', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3209, 3200, '和田', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3210, 3200, '克拉玛依', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3211, 3200, '喀什', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3212, 3200, '库尔勒', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3213, 3200, '石河子', 32, 'sys', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '2017-8-28 16:43:08', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3214, 3200, '吐鲁番', 32, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3215, 3200, '塔城', 32, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3216, 3200, '伊宁', 32, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3300, 0, '云南', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3301, 3300, '昆明', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3302, 3300, '保山', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3303, 3300, '楚雄', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3304, 3300, '大理', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3305, 3300, '德宏', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3306, 3300, '红河', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3307, 3300, '景洪', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3308, 3300, '丽江', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3309, 3300, '临沧', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3310, 3300, '怒江', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3311, 3300, '曲靖', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3312, 3300, '思茅', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3313, 3300, '文山', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3314, 3300, '香格里拉', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3315, 3300, '玉溪', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3316, 3300, '昭通', 33, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3400, 0, '浙江', 34, 'sys', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '2017-8-28 16:43:09', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3401, 3400, '杭州', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3402, 3400, '湖州', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3403, 3400, '嘉兴', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3404, 3400, '金华', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3405, 3400, '丽水', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3406, 3400, '宁波', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3407, 3400, '衢州', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3408, 3400, '绍兴', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3409, 3400, '台州', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3410, 3400, '温州', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (3411, 3400, '舟山', 34, 'sys', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '2017-8-28 16:43:10', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (1, -1, '柬埔寨', 1, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (10100, 1, '柬埔寨', 10, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');
INSERT INTO cmm_area_public(id, parent_id, area_name_zh, sort, opr_id, create_time, modify_time, tm_smp, area_name_kh, area_name_en) VALUES (10101, 10100, '柬埔寨', 10, 'sys', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '2017-8-28 16:42:51', '', '');

--drop table cmm_banner_public;
--banner广告信息表
CREATE TABLE IF NOT EXISTS cmm_banner_public (
    id CHAR(8) NOT NULL COMMENT 'ID',
    banner_url_kh VARCHAR(256) NOT NULL COMMENT '柬语图片url',
    banner_url_cn VARCHAR(256) NOT NULL COMMENT '中文图片url',
    banner_url_en VARCHAR(256) NOT NULL COMMENT '英文图片url',
    detail_url_kh VARCHAR(256) NOT NULL COMMENT '柬语详情url',
    detail_url_cn VARCHAR(256) NOT NULL COMMENT '中文详情url',
    detail_url_en VARCHAR(256) NOT NULL COMMENT '英文详情url',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_banner_public ADD CONSTRAINT pk_cmm_banner_public PRIMARY KEY(id);
--索引
ALTER TABLE cmm_banner_public ADD UNIQUE idx_cmm_banner_public_1(id);
--索引
ALTER TABLE cmm_banner_public ADD INDEX idx_cmm_banner_public_2(eff_date, exp_date);

CREATE TABLE cmm_branch_info (
    id           INT(11)     NOT NULL AUTO_INCREMENT,
    bra_id       VARCHAR(20) NOT NULL COMMENT '部门编号',
    bra_nm       VARCHAR(50) NOT NULL COMMENT '部门名称',
    office_id    VARCHAR(20) NOT NULL COMMENT '归属公司编号',
    office_nm    VARCHAR(50) NOT NULL COMMENT '归属公司名称',
    oper_id      VARCHAR(20) NOT NULL COMMENT '操作员ID',
    modify_time  DATETIME    NOT NULL COMMENT '修改时间',
    create_time  DATETIME    NOT NULL COMMENT '创建时间',
    tm_smp       TIMESTAMP   NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    CONSTRAINT pk_cmm_branch_info PRIMARY KEY(id)
    ) COMMENT = '部门信息表' DEFAULT CHARSET=utf8;
--索引
ALTER TABLE cmm_branch_info ADD INDEX idx_cmm_branch_info_bra_id (office_id,bra_id);

--drop table cmm_campaign_public;
--活动信息表
CREATE TABLE IF NOT EXISTS cmm_campaign_public (
    id INT(8) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '活动ID',
    campaign_title_kh VARCHAR(64) NOT NULL COMMENT '柬语活动标题',
    campaign_title_cn VARCHAR(64) NOT NULL COMMENT '中文活动标题',
    campaign_title_en VARCHAR(64) NOT NULL COMMENT '英文活动标题',
    campaign_content_kh VARCHAR(1024) NOT NULL COMMENT '柬语公告内容',
    campaign_content_cn VARCHAR(1024) NOT NULL COMMENT '中文公告内容',
    campaign_content_en VARCHAR(1024) NOT NULL COMMENT '英文公告内容',
    channel VARCHAR(8) NOT NULL COMMENT '渠道信息 portal:官网 app:用户app mportal:商户官网 mapp:商户app',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--索引
ALTER TABLE cmm_campaign_public ADD UNIQUE idx_cmm_campaign_public_1(id);
--索引
ALTER TABLE cmm_campaign_public ADD INDEX idx_cmm_campaign_public_2(eff_date, exp_date);

--drop table cmm_key_param
--系统密钥参数表
CREATE TABLE IF NOT EXISTS cmm_key_param (
    id VARCHAR(16) NOT NULL COMMENT '密钥ID',
    tkey VARCHAR(16) NOT NULL COMMENT '密钥',
    algorithms VARCHAR(16) DEFAULT 'MD5' COMMENT '算法',
    channel VARCHAR(8) NOT NULL COMMENT '渠道信息',
    versions VARCHAR(8) NOT NULL COMMENT '版本信息',
    stats CHAR(1) DEFAULT 0 COMMENT '状态 0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_key_param ADD CONSTRAINT pk_cmm_key_param PRIMARY KEY(id);
--索引
ALTER TABLE cmm_key_param ADD UNIQUE idx_cmm_key_param_1(id);
--索引
ALTER TABLE cmm_key_param ADD INDEX idx_cmm_key_param_2(channel);
--索引
ALTER TABLE cmm_key_param ADD INDEX idx_cmm_key_param_3(versions);

--参数初始化
insert into `seatelpay_cmm`.`cmm_key_param` ( `id`, `stats`, `tkey`, `eff_date`, `channel`, `opr_id`, `versions`, `tm_smp`, `exp_date`, `algorithms`, `create_time`, `modify_time`) values ( 'ASsGRgBYJhjpN7tb', '1', 'UhMFLPErFHAYucbb', '2017-08-22', 'IGW', 'sys', '1.0', '2017-08-22 11:41:45', '2018-08-22', 'MD5', '2017-08-22 11:41:44', '2017-08-22 11:41:47');
insert into `seatelpay_cmm`.`cmm_key_param` ( `id`, `stats`, `tkey`, `eff_date`, `channel`, `opr_id`, `versions`, `tm_smp`, `exp_date`, `algorithms`, `create_time`, `modify_time`) values ( 'DUsGRgBYJhjpN7tb', '1', 'YhMFLPErFHAYucJI', '2017-08-11', 'USRA', 'sys', '1.0', '2017-08-31 15:08:35', '2018-08-11', 'MD5', '2017-08-11 11:37:24', '2017-08-11 11:37:28');
insert into `seatelpay_cmm`.`cmm_key_param` ( `id`, `stats`, `tkey`, `eff_date`, `channel`, `opr_id`, `versions`, `tm_smp`, `exp_date`, `algorithms`, `create_time`, `modify_time`) values ( 'ITGGRgBYJhjpN7Ft', '1', 'YhMFLPErFHAYucJI', '2017-08-11', 'MERA', 'sys', '1.0', '2017-08-31 15:17:51', '2018-08-11', 'MD5', '2017-08-11 11:37:24', '2017-08-11 11:37:28');
insert into `seatelpay_cmm`.`cmm_key_param` ( `id`, `stats`, `tkey`, `eff_date`, `channel`, `opr_id`, `versions`, `tm_smp`, `exp_date`, `algorithms`, `create_time`, `modify_time`) values ( 'LVGGRgDFJhjpN7F', '1', 'YhMFLPErFHAYucJI', '2017-08-11', 'UI', 'sys', '1.0', '2017-08-31 15:08:56', '2018-08-11', 'MD5', '2017-08-11 11:37:24', '2017-08-11 11:37:28');
insert into `seatelpay_cmm`.`cmm_key_param` ( `id`, `stats`, `tkey`, `eff_date`, `channel`, `opr_id`, `versions`, `tm_smp`, `exp_date`, `algorithms`, `create_time`, `modify_time`) values ( 'NBGGRgBYJhjpN7F', '1', 'YhMFLPErFHAYucJI', '2017-08-11', 'USRI', 'sys', '1.0', '2017-08-31 15:27:20', '2018-08-11', 'MD5', '2017-08-11 11:37:24', '2017-08-11 11:37:28');
insert into `seatelpay_cmm`.`cmm_key_param` ( `id`, `stats`, `tkey`, `eff_date`, `channel`, `opr_id`, `versions`, `tm_smp`, `exp_date`, `algorithms`, `create_time`, `modify_time`) values ( 'NBGGRgDFJhjpN7F', '1', 'YhMFLPErFHAYucJI', '2017-08-11', 'MERI', 'sys', '1.0', '2017-08-31 15:08:56', '2018-08-11', 'MD5', '2017-08-11 11:37:24', '2017-08-11 11:37:28');
insert into `seatelpay_cmm`.`cmm_key_param` ( `id`, `stats`, `tkey`, `eff_date`, `channel`, `opr_id`, `versions`, `tm_smp`, `exp_date`, `algorithms`, `create_time`, `modify_time`) values ( 'NBGGRgDFJhjpN7FI', '1', 'YhMFLPErFHAYucJI', '2017-08-11', 'IPOS', 'sys', '1.0', '2017-08-31 15:08:56', '2018-08-11', 'MD5', '2017-08-11 11:37:24', '2017-08-11 11:37:28');

--drop table cmm_log_app_info
--登陆信息流水表
CREATE TABLE IF NOT EXISTS cmm_log_app_info (
    login_jrn_no VARCHAR(25) NOT NULL COMMENT '登录流水号',
    user_id VARCHAR(20) NOT NULL COMMENT '内部用户号',
    user_type VARCHAR(8) NOT NULL COMMENT '用户类型',
    versions VARCHAR(8) NOT NULL COMMENT '版本号',
    download_channel VARCHAR(128) NOT NULL COMMENT '渠道信息',
    os_version VARCHAR(128) NOT NULL COMMENT '操作系统版本',
    term_id VARCHAR(64) NOT NULL COMMENT '终端ID',
    term_type VARCHAR(16) NOT NULL COMMENT '终端类型',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_log_app_info ADD CONSTRAINT pk_cmm_log_app_info PRIMARY KEY(login_jrn_no);
--索引
ALTER TABLE cmm_log_app_info ADD UNIQUE idx_cmm_log_app_info_1(login_jrn_no);

--新增字段
alter table cmm_log_app_info add column channel VARCHAR(8) NOT NULL COMMENT '交易渠道信息';
alter table cmm_log_app_info add column login_name VARCHAR(32) NOT NULL COMMENT '用户登陆名称';

--drop table cmm_message_send;
--消息推送流水表
CREATE TABLE IF NOT EXISTS cmm_message_send (
    message_jrn_no VARCHAR(25) NOT NULL COMMENT '消息流水号',
    trade_date DATE NOT NULL COMMENT '交易日期',
    trade_time TIME NOT NULL COMMENT '交易时间',
    user_id VARCHAR(20) NOT NULL COMMENT '内部用户号',
    pass_push_flg VARCHAR(1) NOT NULL COMMENT '是否透传 0:非透传 1:透传',
    user_type VARCHAR(8) NOT NULL COMMENT '用户类型 common:通用 personal:个人的 merchant:商户的',
    type VARCHAR(8) NOT NULL COMMENT '消息类型 center:消息中心 pay:支付请求 order:订单结果',
    message_title VARCHAR(64) NOT NULL COMMENT '消息标题',
    message_content VARCHAR(1024) COMMENT '消息内容',
    stats CHAR(1) DEFAULT '0' COMMENT '状态0:未读 1:已读',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_message_send ADD CONSTRAINT pk_cmm_sms_send PRIMARY KEY(message_jrn_no);
--索引
ALTER TABLE cmm_message_send ADD UNIQUE idx_cmm_sms_send_1(message_jrn_no);
--索引
ALTER TABLE cmm_message_send ADD INDEX idx_cmm_sms_send_2(user_id);
--索引
ALTER TABLE cmm_message_send ADD INDEX idx_cmm_sms_send_3(type);
--索引
ALTER TABLE cmm_message_send ADD INDEX idx_cmm_sms_send_4(trade_date);

--drop table cmm_message_template;
--消息模版表
CREATE TABLE IF NOT EXISTS cmm_message_template (
    id CHAR(8) NOT NULL COMMENT '消息模版ID',
    pass_push_flg VARCHAR(1) NOT NULL COMMENT '是否透传 0:非透传 1:透传',
    user_type VARCHAR(8) NOT NULL COMMENT '用户类型 common:通用 personal:个人的 merchant:商户的',
    type VARCHAR(8) NOT NULL COMMENT '消息类型 center:消息中心 pay:支付请求 order:订单结果',
    message_title_kh VARCHAR(64) NOT NULL COMMENT '柬语消息标题',
    message_title_cn VARCHAR(64) NOT NULL COMMENT '中文消息标题',
    message_title_en VARCHAR(64) NOT NULL COMMENT '英语消息标题',
    replace_field VARCHAR(256) NOT NULL COMMENT '替换变量',
    template_content_kh VARCHAR(1024) NOT NULL COMMENT '柬语消息模版内容',
    template_content_cn VARCHAR(1024) NOT NULL COMMENT '中文消息模版内容',
    template_content_en VARCHAR(1024) NOT NULL COMMENT '英语消息模版内容',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_message_template ADD CONSTRAINT pk_cmm_message_template PRIMARY KEY(id);
--索引
ALTER TABLE cmm_message_template ADD UNIQUE idx_cmm_message_template_1(id);
--索引
ALTER TABLE cmm_message_template ADD INDEX idx_cmm_message_template_2(eff_date, exp_date);

--drop table cmm_notice_public;
--公告信息表
CREATE TABLE IF NOT EXISTS cmm_notice_public (
    id INT(8) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '公告ID',
    notice_title_kh VARCHAR(64) NOT NULL COMMENT '柬语公告标题',
    notice_title_cn VARCHAR(64) NOT NULL COMMENT '中文公告标题',
    notice_title_en VARCHAR(64) NOT NULL COMMENT '英文公告标题',
    notice_content_kh VARCHAR(1024) NOT NULL COMMENT '柬语公告内容',
    notice_content_cn VARCHAR(1024) NOT NULL COMMENT '中文公告内容',
    notice_content_en VARCHAR(1024) NOT NULL COMMENT '英文公告内容',
    channel VARCHAR(8) NOT NULL COMMENT '渠道信息 portal:官网 app:用户app mportal:商户官网 mapp:商户app',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--索引
ALTER TABLE cmm_notice_public ADD UNIQUE idx_cmm_notice_public_1(id);
--索引
ALTER TABLE cmm_notice_public ADD INDEX idx_cmm_notice_public_2(eff_date, exp_date);

/*1分公司信息表*/
CREATE TABLE cmm_office_info (
    id          INT(11)     NOT NULL AUTO_INCREMENT,
    office_id   VARCHAR(20) NOT NULL COMMENT  '分公司编号',
    office_nm   VARCHAR(50) NOT NULL COMMENT  '分公司名称',
    oper_id     VARCHAR(20) NOT NULL COMMENT  '操作员ID',
    modify_time DATETIME    NOT NULL COMMENT  '修改时间',
    create_time DATETIME    NOT NULL COMMENT  '创建时间',
    tm_smp      TIMESTAMP   NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    CONSTRAINT pk_cmm_office_info PRIMARY KEY(id)
  ) COMMENT = '分公司信息表' DEFAULT CHARSET=utf8;
  
--索引
ALTER TABLE cmm_office_info ADD INDEX idx_cmm_office_info_office_id (office_id);

--drop table cmm_phone_segement
--手机号段参数表
CREATE TABLE IF NOT EXISTS cmm_phone_segement (
    prefix_number VARCHAR(8) NOT NULL COMMENT '号段前缀',
    carrier VARCHAR(32) NOT NULL COMMENT '运营商',
    area_code VARCHAR(16) NOT NULL COMMENT '国家区域代码',
    country_code VARCHAR(16) NOT NULL COMMENT '国家代码',
    country_kh VARCHAR(32) NOT NULL COMMENT '柬文国家名称',
    country_zh VARCHAR(32) NOT NULL COMMENT '中文国家名称',
    country_en VARCHAR(32) NOT NULL COMMENT '英文国家名称',
    province_code VARCHAR(16) NOT NULL COMMENT '省份代码',
    province_kh VARCHAR(32) NOT NULL COMMENT '柬文省份名称',
    province_zh VARCHAR(32) NOT NULL COMMENT '中文省份名称',
    province_en VARCHAR(32) NOT NULL COMMENT '英文省份名称',
    city_code VARCHAR(16) NOT NULL COMMENT '地市代码',
    city_kh VARCHAR(32) NOT NULL COMMENT '柬文地市名称',
    city_zh VARCHAR(32) NOT NULL COMMENT '中文地市名称',
    city_en VARCHAR(32) NOT NULL COMMENT '英文地市名称',
    stats CHAR(1) DEFAULT 0 COMMENT '号段状态 0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_phone_segement ADD CONSTRAINT pk_cmm_phone_segement PRIMARY KEY(prefix_number);
--索引
ALTER TABLE cmm_phone_segement ADD UNIQUE idx_cmm_phone_segement_1(prefix_number);
--索引
ALTER TABLE cmm_phone_segement ADD INDEX idx_cmm_phone_segement_2(area_code);
--索引
ALTER TABLE cmm_phone_segement ADD INDEX idx_cmm_phone_segement_3(country_code);
--索引
ALTER TABLE cmm_phone_segement ADD INDEX idx_cmm_phone_segement_4(province_code);
--索引
ALTER TABLE cmm_phone_segement ADD INDEX idx_cmm_phone_segement_5(city_code);

--新增字段
alter table cmm_phone_segement add number_length int not null  comment '号码长度';
update cmm_phone_segement set number_length = 10  where prefix_number in ('076','096','031','071','097','038','018');
update cmm_phone_segement set number_length = 9  where number_length = 0;

--参数初始化
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:46:14', '', '1', '2017-09-08 17:47:10', '010', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:47:10', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:37:48', '', '1', '2017-09-08 17:38:43', '011', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:38:43', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:41:48', '', '1', '2017-09-08 17:42:43', '012', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:42:43', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:53:51', '', '1', '2017-09-08 17:54:46', '013', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'qb', '', '2017-09-08 17:54:46', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:46:30', '', '1', '2017-09-06 10:16:26', '015', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:47:25', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:46:52', '', '1', '2017-09-08 17:47:48', '016', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:47:48', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:42:00', '', '1', '2017-09-08 17:42:55', '017', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:42:55', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:56:10', '', '1', '2017-09-05 21:48:41', '018', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'seatel', '', '2017-09-08 17:57:05', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:50:19', '', '1', '2017-09-08 17:51:14', '031', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:51:14', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:55:54', '', '1', '2017-09-08 17:56:50', '038', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'CooTel', '', '2017-09-08 17:56:50', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:50:28', '', '1', '2017-09-08 17:51:24', '060', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:51:24', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:42:26', '', '1', '2017-09-08 17:43:21', '061', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:43:21', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:50:42', '', '1', '2017-09-08 17:51:38', '066', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:51:38', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:50:54', '', '1', '2017-09-08 17:51:49', '067', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:51:49', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:51:38', '', '1', '2017-09-08 17:52:34', '068', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:52:34', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:47:08', '', '1', '2017-09-08 17:48:03', '069', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:48:03', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:47:25', '', '1', '2017-09-08 17:48:20', '070', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:48:20', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:51:58', '', '1', '2017-09-08 17:52:54', '071', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:52:54', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:42:57', '', '1', '2017-09-08 17:43:52', '076', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:43:52', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:43:13', '', '1', '2017-09-08 17:44:08', '077', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:44:08', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:43:23', '', '1', '2017-09-08 17:44:19', '078', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:44:19', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:43:38', '', '1', '2017-09-08 17:44:34', '079', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:44:34', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:54:01', '', '1', '2017-09-08 17:54:57', '080', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'qb', '', '2017-09-08 17:54:57', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:48:26', '', '1', '2017-09-08 17:49:21', '081', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:49:21', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:54:13', '', '1', '2017-09-08 17:55:09', '083', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'qb', '', '2017-09-08 17:55:09', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 18:13:49', '', '1', '2017-09-08 17:55:45', '084', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'qb', '', '2017-09-08 17:56:25', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:43:58', '', '1', '2017-09-08 17:44:53', '085', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:44:53', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:48:37', '', '1', '2017-09-08 17:49:33', '086', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:49:33', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:48:50', '', '1', '2017-09-08 17:49:46', '087', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:49:46', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:52:59', '', '1', '2017-09-08 17:53:05', '088', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:53:54', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 18:14:00', '', '1', '2017-09-08 17:54:20', '090', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:54:20', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:45:05', '', '1', '2017-09-08 17:45:06', '092', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:46:01', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:49:16', '', '1', '2017-09-08 17:50:12', '093', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:50:12', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:45:39', '', '1', '2017-09-08 17:46:35', '095', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:46:35', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:49:59', '', '1', '2017-09-08 17:50:54', '096', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:50:54', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:53:36', '', '1', '2017-09-08 17:54:31', '097', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:54:31', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:49:44', '', '1', '2017-09-08 17:50:39', '098', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:50:39', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:46:00', '', '1', '2017-09-08 17:46:56', '099', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:46:56', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:46:14', '', '1', '2017-09-08 17:47:10', '10', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:47:10', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:37:48', '', '1', '2017-09-08 17:38:43', '11', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:38:43', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:41:48', '', '1', '2017-09-08 17:42:43', '12', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:42:43', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:53:51', '', '1', '2017-09-08 17:54:46', '13', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'qb', '', '2017-09-08 17:54:46', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:46:30', '', '1', '2017-09-06 10:16:26', '15', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:47:25', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:46:52', '', '1', '2017-09-08 17:47:48', '16', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:47:48', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:42:00', '', '1', '2017-09-08 17:42:55', '17', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:42:55', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:56:10', '', '1', '2017-09-05 21:48:41', '18', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'seatel', '', '2017-09-08 17:57:05', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:50:19', '', '1', '2017-09-08 17:51:14', '31', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:51:14', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:55:54', '', '1', '2017-09-08 17:56:50', '38', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'CooTel', '', '2017-09-08 17:56:50', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:50:28', '', '1', '2017-09-08 17:51:24', '60', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:51:24', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:42:26', '', '1', '2017-09-08 17:43:21', '61', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:43:21', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:50:42', '', '1', '2017-09-08 17:51:38', '66', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:51:38', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:50:54', '', '1', '2017-09-08 17:51:49', '67', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:51:49', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:51:38', '', '1', '2017-09-08 17:52:34', '68', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:52:34', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:47:08', '', '1', '2017-09-08 17:48:03', '69', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:48:03', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:47:25', '', '1', '2017-09-08 17:48:20', '70', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:48:20', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:51:58', '', '1', '2017-09-08 17:52:54', '71', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:52:54', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:42:57', '', '1', '2017-09-08 17:43:52', '76', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:43:52', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:43:13', '', '1', '2017-09-08 17:44:08', '77', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:44:08', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:43:23', '', '1', '2017-09-08 17:44:19', '78', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:44:19', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:43:38', '', '1', '2017-09-08 17:44:34', '79', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:44:34', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:54:01', '', '1', '2017-09-08 17:54:57', '80', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'qb', '', '2017-09-08 17:54:57', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:48:26', '', '1', '2017-09-08 17:49:21', '81', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:49:21', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:54:13', '', '1', '2017-09-08 17:55:09', '83', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'qb', '', '2017-09-08 17:55:09', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 18:14:17', '', '1', '2017-09-08 17:55:45', '84', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'qb', '', '2017-09-08 17:56:25', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:43:58', '', '1', '2017-09-08 17:44:53', '85', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:44:53', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:48:37', '', '1', '2017-09-08 17:49:33', '86', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:49:33', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:48:50', '', '1', '2017-09-08 17:49:46', '87', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:49:46', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:52:59', '', '1', '2017-09-08 17:53:05', '88', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:53:54', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 18:14:28', '', '1', '2017-09-08 17:54:20', '90', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:54:20', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:45:05', '', '1', '2017-09-08 17:45:06', '92', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:46:01', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:49:16', '', '1', '2017-09-08 17:50:12', '93', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:50:12', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:45:39', '', '1', '2017-09-08 17:46:35', '95', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:46:35', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:49:59', '', '1', '2017-09-08 17:50:54', '96', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:50:54', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:53:36', '', '1', '2017-09-08 17:54:31', '97', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'metfene', '', '2017-09-08 17:54:31', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:49:44', '', '1', '2017-09-08 17:50:39', '98', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'Smart', '', '2017-09-08 17:50:39', '1', '2017-09-08', '');
insert into `seatelpay_cmm`.`cmm_phone_segement` ( `province_en`, `province_code`, `city_zh`, `tm_smp`, `city_en`, `country_code`, `create_time`, `prefix_number`, `city_code`, `opr_id`, `province_zh`, `province_kh`, `area_code`, `country_zh`, `exp_date`, `country_en`, `carrier`, `country_kh`, `modify_time`, `stats`, `eff_date`, `city_kh`) values ( '', '10100', '柬埔寨', '2017-09-08 17:46:00', '', '1', '2017-09-08 17:46:56', '99', '10101', 'admin', '柬埔寨', '', '855', '柬埔寨', '9999-12-31', '', 'cellcard', '', '2017-09-08 17:46:56', '1', '2017-09-08', '');

--drop table cmm_push_client;
--消息推送clientid信息表
CREATE TABLE IF NOT EXISTS cmm_push_client (
    user_id VARCHAR(20) NOT NULL COMMENT '内部用户号',
    user_type VARCHAR(8) NOT NULL COMMENT '用户类型 personal:个人的 merchant:商户的',
    client_id VARCHAR(32) NOT NULL COMMENT '消息推送id',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:有效',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_push_client ADD CONSTRAINT pk_cmm_push_client PRIMARY KEY(user_id, user_type);
--索引
ALTER TABLE cmm_push_client ADD UNIQUE idx_cmm_push_client_1(user_id, user_type);

--drop table cmm_push_param;
--消息推送参数表
CREATE TABLE IF NOT EXISTS cmm_push_param (
    id VARCHAR(16) NOT NULL COMMENT 'ID',
    type VARCHAR(8) NOT NULL COMMENT '应用类型',
    app_id VARCHAR(32) NOT NULL COMMENT '应用ID',
    app_key VARCHAR(32) NOT NULL COMMENT '应用KEY',
    master_secret VARCHAR(32) NOT NULL COMMENT '应用密钥',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:有效',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_push_param ADD CONSTRAINT pk_cmm_push_param PRIMARY KEY(id);
--索引
ALTER TABLE cmm_push_param ADD UNIQUE idx_cmm_push_param_1(id);

--参数初始化
insert into `seatelpay_cmm`.`cmm_push_param` ( `app_id`, `modify_time`, `tm_smp`, `id`, `app_key`, `stats`, `create_time`, `type`, `master_secret`) values ( 'Sd6gj7cFQU8RABsSobFmS4', '2017-08-11 16:36:04', '2017-08-17 16:15:16', 'MERCHANT', '1TGcVqSmfDA38qQXj2gAe2', '1', '2017-08-11 16:36:02', 'merchant', '4SfBcrVrYW7KAJOTsgS6NA');
insert into `seatelpay_cmm`.`cmm_push_param` ( `app_id`, `modify_time`, `tm_smp`, `id`, `app_key`, `stats`, `create_time`, `type`, `master_secret`) values ( 'tK6ZQGuIYl8WYxgbfA7iL6', '2017-08-11 16:36:04', '2017-08-17 16:15:16', 'PERSONAL', 'A3U9gMIDmT7NArEbNj6K95', '1', '2017-08-11 16:36:02', 'personal', 'qvL9u7Y6MW7b8eTQTBpfT8');

--drop table cmm_qrcode_param
--二维码参数信息表
CREATE TABLE IF NOT EXISTS cmm_qrcode_param (
    id VARCHAR(16) NOT NULL COMMENT '参数ID',
    payment_code_prefix VARCHAR(8) NOT NULL COMMENT '付款码前缀',
    aggregate_url VARCHAR(256) NOT NULL COMMENT '聚合支付url',
    tkey VARCHAR(1024) NOT NULL COMMENT '加解密密钥',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    stats CHAR(1) DEFAULT 0 COMMENT '状态 0:无效 1:有效',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_qrcode_param ADD CONSTRAINT pk_cmm_qrcode_param PRIMARY KEY(id);
--索引
ALTER TABLE cmm_qrcode_param ADD UNIQUE idx_cmm_qrcode_param_1(id);

--参数初始化
insert into `seatelpay_cmm`.`cmm_qrcode_param` ( `opr_id`, `modify_time`, `tm_smp`, `id`, `tkey`, `aggregate_url`, `stats`, `exp_date`, `payment_code_prefix`, `create_time`, `eff_date`) values ( 'sys', '2017-08-09 12:33:21', '2017-08-28 11:07:43', 'QRCODEPARAM', 'SjeAM8FpJ5vJFop1IRTXMw==', 'http://219.135.153.39:9001/aggregation/index.html?urlParams=', '1', '9999-12-31', '11', '2017-08-09 12:33:18', '2017-08-09');

--drop table cmm_qrcode_payment
--付款码流水表
CREATE TABLE IF NOT EXISTS cmm_qrcode_payment (
    code_jrn_no VARCHAR(25) NOT NULL COMMENT '付款码流水号',
    user_id VARCHAR(20) NOT NULL COMMENT '内部用户号',
    code_info CHAR(18) NOT NULL COMMENT '付款码信息',
    type VARCHAR(8) NOT NULL COMMENT '付款码类型 barCode:条码 qrCode:二维码',
    code_stats CHAR(1) DEFAULT 0 COMMENT '付款码状态 0:初始化 1:已验证 2:已失效',
    eff_time DATETIME NOT NULL COMMENT '生效时间',
    exp_time DATETIME NOT NULL COMMENT '失效时间',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_qrcode_payment ADD CONSTRAINT pk_cmm_qrcode_payment PRIMARY KEY(code_jrn_no);
--索引
ALTER TABLE cmm_qrcode_payment ADD UNIQUE idx_cmm_qrcode_payment_1(code_jrn_no);
--索引
ALTER TABLE cmm_qrcode_payment ADD INDEX idx_cmm_qrcode_payment_2(user_id);
--索引
ALTER TABLE cmm_qrcode_payment ADD INDEX idx_cmm_qrcode_payment_3(code_info);
--索引
ALTER TABLE cmm_qrcode_payment ADD INDEX idx_cmm_qrcode_payment_4(eff_time, exp_time);

--drop table cmm_qrcode_secret;
--付款码用户token信息表
CREATE TABLE IF NOT EXISTS cmm_qrcode_secret (
    user_id VARCHAR(20) NOT NULL COMMENT '内部用户号',
    barcode_secret_key CHAR(128) NOT NULL COMMENT '条码生成密钥',
    qrcode_secret_key CHAR(128) NOT NULL COMMENT '二维码生成密钥',
    secret_key_status CHAR(1) DEFAULT 1 COMMENT '密钥状态 0:失效 1:有效',
    eff_time DATETIME NOT NULL COMMENT '生效时间',
    exp_time DATETIME NOT NULL COMMENT '失效时间',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_qrcode_secret ADD CONSTRAINT pk_cmm_qrcode_secret PRIMARY KEY(user_id);
--索引
ALTER TABLE cmm_qrcode_secret ADD UNIQUE idx_cmm_qrcode_secret_1(user_id);

--drop table cmm_qrcode_segement
--付款码号段参数表
CREATE TABLE IF NOT EXISTS cmm_qrcode_segement (
    prefix_number VARCHAR(8) NOT NULL COMMENT '号段前缀',
    pay_type VARCHAR(16) NOT NULL COMMENT '付款码类型',
    stats CHAR(1) DEFAULT 0 COMMENT '号段状态 0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_qrcode_segement ADD CONSTRAINT pk_cmm_qrcode_segement PRIMARY KEY(prefix_number);
--索引
ALTER TABLE cmm_qrcode_segement ADD UNIQUE idx_cmm_qrcode_segement_1(prefix_number);

--参数初始化
insert into `seatelpay_cmm`.`cmm_qrcode_segement` ( `opr_id`, `modify_time`, `tm_smp`, `pay_type`, `stats`, `exp_date`, `prefix_number`, `create_time`, `eff_date`) values ( 'sys', '2017-08-22 10:04:46', '2017-08-22 10:04:48', 'Seatelpay', '1', '9999-12-31', '11', '2017-08-22 10:04:44', '2017-08-22');
insert into `seatelpay_cmm`.`cmm_qrcode_segement` ( `opr_id`, `modify_time`, `tm_smp`, `pay_type`, `stats`, `exp_date`, `prefix_number`, `create_time`, `eff_date`) values ( 'sys', '2017-08-22 10:03:40', '2017-09-11 14:51:38', 'WeChat', '1', '9999-12-31', '13', '2017-08-22 10:03:37', '2017-08-22');
insert into `seatelpay_cmm`.`cmm_qrcode_segement` ( `opr_id`, `modify_time`, `tm_smp`, `pay_type`, `stats`, `exp_date`, `prefix_number`, `create_time`, `eff_date`) values ( 'sys', '2017-01-01 00:00:00', '2017-08-22 10:03:58', 'Alipay', '1', '9999-12-31', '28', '2017-01-01 00:00:00', '2017-08-29');
insert into `seatelpay_cmm`.`cmm_qrcode_segement` ( `opr_id`, `modify_time`, `tm_smp`, `pay_type`, `stats`, `exp_date`, `prefix_number`, `create_time`, `eff_date`) values ( 'sys', '2017-08-22 10:04:14', '2017-09-11 14:53:17', 'BESTPAY', '1', '9999-12-31', '51', '2017-08-22 10:04:11', '2017-08-22');


--drop table cmm_sms_code;
--短信验证码流水表
CREATE TABLE IF NOT EXISTS cmm_sms_code (
    code_jrn_no VARCHAR(25) NOT NULL COMMENT '短信验证码流水号',
    type CHAR(3) NOT NULL COMMENT '短信验证码类型',
    mbl_no VARCHAR(20) NOT NULL COMMENT '手机号码',
    sms_code VARCHAR(8) NOT NULL COMMENT '短信验证码',
    token VARCHAR(32) NOT NULL COMMENT '短信验证码token',
    code_stats CHAR(1) DEFAULT 0 COMMENT '短信验证码状态',
    eff_time DATETIME NOT NULL COMMENT '生效时间',
    exp_time DATETIME NOT NULL COMMENT '失效时间',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_sms_code ADD CONSTRAINT pk_cmm_sms_code PRIMARY KEY(code_jrn_no);
--索引
ALTER TABLE cmm_sms_code ADD UNIQUE idx_cmm_sms_code_1(code_jrn_no);
--索引
ALTER TABLE cmm_sms_code ADD INDEX idx_cmm_sms_code_2(mbl_no);


--drop table cmm_sms_param
--短信参数表信息表
CREATE TABLE IF NOT EXISTS cmm_sms_param (
    id CHAR(8) NOT NULL COMMENT '参数ID',
    dly_cnt_lmt INT(8) NOT NULL COMMENT '短信验证码日累计控制',
    dly_sms_cnt_lmt INT(8) NOT NULL COMMENT '短信日累计控制',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_sms_param ADD CONSTRAINT pk_cmm_sms_param PRIMARY KEY(id);
--索引
ALTER TABLE cmm_sms_param ADD UNIQUE idx_cmm_sms_param_1(id);

--参数初始化
insert into `seatelpay_cmm`.`cmm_sms_param` ( `opr_id`, `tm_smp`, `modify_time`, `id`, `dly_sms_cnt_lmt`, `create_time`, `dly_cnt_lmt`) values ( 'sys', '2017-09-14 09:43:06', '2017-08-10 10:33:00', 'SMSPARAM', '10', '2017-08-10 10:32:58', '10');

--drop table cmm_sms_send;
--短信下行流水表
CREATE TABLE IF NOT EXISTS cmm_sms_send (
    sms_jrn_no VARCHAR(25) NOT NULL COMMENT '短信流水号',
    trade_date DATE NOT NULL COMMENT '交易日期',
    trade_time TIME NOT NULL COMMENT '交易时间',
    type VARCHAR(3) NOT NULL COMMENT '短信类型',
    lvl VARCHAR(3) NOT NULL COMMENT '短信级别',
    mbl_no VARCHAR(20) NOT NULL COMMENT '手机号码',
    sms_content VARCHAR(1024) COMMENT '短信内容',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_sms_send ADD CONSTRAINT pk_cmm_sms_send PRIMARY KEY(sms_jrn_no);
--索引
ALTER TABLE cmm_sms_send ADD UNIQUE idx_cmm_sms_send_1(sms_jrn_no);
--索引
ALTER TABLE cmm_sms_send ADD INDEX idx_cmm_sms_send_2(mbl_no);
--索引
ALTER TABLE cmm_sms_send ADD INDEX idx_cmm_sms_send_3(type);
--索引
ALTER TABLE cmm_sms_send ADD INDEX idx_cmm_sms_send_4(trade_date);

--drop table cmm_sms_template;
--短信模版表
CREATE TABLE IF NOT EXISTS cmm_sms_template (
    id CHAR(8) NOT NULL COMMENT '短信模版ID',
    type VARCHAR(3) NOT NULL COMMENT '短信类型',
    lvl VARCHAR(3) NOT NULL COMMENT '短信级别',
    replace_field VARCHAR(256) NOT NULL COMMENT '替换变量',
    template_content_kh VARCHAR(1024) NOT NULL COMMENT '柬语短信模版内容',
    template_content_cn VARCHAR(1024) NOT NULL COMMENT '中文短信模版内容',
    template_content_en VARCHAR(1024) NOT NULL COMMENT '英语短信模版内容',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:有效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_sms_template ADD CONSTRAINT pk_cmm_sms_template PRIMARY KEY(id);
--索引
ALTER TABLE cmm_sms_template ADD UNIQUE idx_cmm_sms_template_1(id);
--索引
ALTER TABLE cmm_sms_template ADD INDEX idx_cmm_sms_template_2(eff_date, exp_date);

--参数初始化
insert into `seatelpay_cmm`.`cmm_sms_template` ( `id`, `template_content_cn`, `stats`, `eff_date`, `replace_field`, `lvl`, `template_content_en`, `type`, `opr_id`, `template_content_kh`, `tm_smp`, `exp_date`, `create_time`, `modify_time`) values ( 'CMM00001', '您此次注册的验证码为[$]。', '1', '2017-09-07', 'smsCode|', '0', '您此次注册的验证码为[$]。', '1', 'admin', '您此次注册的验证码为[$]。', '2017-09-11 15:51:47', '9999-12-31', '2017-09-07 10:55:10', '2017-09-07 11:20:16');
insert into `seatelpay_cmm`.`cmm_sms_template` ( `id`, `template_content_cn`, `stats`, `eff_date`, `replace_field`, `lvl`, `template_content_en`, `type`, `opr_id`, `template_content_kh`, `tm_smp`, `exp_date`, `create_time`, `modify_time`) values ( 'CMM00002', '您此次登陆的验证码为[$]。', '1', '2017-09-07', 'smsCode|', '0', '您此次登陆的验证码为[$]。', '2', 'admin', '您此次登陆的验证码为[$]。', '2017-09-11 15:51:50', '9999-12-31', '2017-09-07 10:55:10', '2017-09-07 11:20:16');
insert into `seatelpay_cmm`.`cmm_sms_template` ( `id`, `template_content_cn`, `stats`, `eff_date`, `replace_field`, `lvl`, `template_content_en`, `type`, `opr_id`, `template_content_kh`, `tm_smp`, `exp_date`, `create_time`, `modify_time`) values ( 'CMM00003', '您此次修改登陆密码的验证码为[$]。', '1', '2017-09-07', 'smsCode|', '0', '您此次修改登陆密码的验证码为[$]。', '3', 'admin', '您此次修改登陆密码的验证码为[$]。', '2017-09-11 15:51:53', '9999-12-31', '2017-09-07 10:55:10', '2017-09-07 11:20:16');
insert into `seatelpay_cmm`.`cmm_sms_template` ( `id`, `template_content_cn`, `stats`, `eff_date`, `replace_field`, `lvl`, `template_content_en`, `type`, `opr_id`, `template_content_kh`, `tm_smp`, `exp_date`, `create_time`, `modify_time`) values ( 'CMM00004', '您此次修改支付密码的验证码为[$]。', '1', '2017-09-07', 'smsCode|', '0', '您此次修改支付密码的验证码为[$]。', '4', 'admin', '您此次修改支付密码的验证码为[$]。', '2017-09-11 15:51:55', '9999-12-31', '2017-09-07 10:55:10', '2017-09-07 11:20:16');
insert into `seatelpay_cmm`.`cmm_sms_template` ( `id`, `template_content_cn`, `stats`, `eff_date`, `replace_field`, `lvl`, `template_content_en`, `type`, `opr_id`, `template_content_kh`, `tm_smp`, `exp_date`, `create_time`, `modify_time`) values ( 'CMM00005', '您此次支付签约的验证码为[$]。', '1', '2017-09-07', 'smsCode|', '0', '您此次支付签约的验证码为[$]。', '5', 'admin', '您此次支付签约的验证码为[$]。', '2017-09-11 15:51:57', '9999-12-31', '2017-09-07 10:55:10', '2017-09-07 11:20:16');
insert into `seatelpay_cmm`.`cmm_sms_template` ( `id`, `template_content_cn`, `stats`, `eff_date`, `replace_field`, `lvl`, `template_content_en`, `type`, `opr_id`, `template_content_kh`, `tm_smp`, `exp_date`, `create_time`, `modify_time`) values ( 'CMM00006', '您此次支付的验证码为[$]。', '1', '2017-09-07', 'smsCode|', '0', '您此次支付的验证码为[$]。', '6', 'admin', '您此次支付的验证码为[$]。', '2017-09-11 15:51:59', '9999-12-31', '2017-09-07 10:55:10', '2017-09-07 11:20:16');


--drop table cmm_sms_traffic;
--短信流量控制记录表
CREATE TABLE IF NOT EXISTS cmm_sms_traffic (
    mbl_no VARCHAR(20) NOT NULL COMMENT '手机号码',
    trade_date DATE NOT NULL COMMENT '交易日期',
    dly_cnt INT(8) DEFAULT 0 COMMENT '短信验证码日累计次数',
    dly_sms_cnt INT(8) DEFAULT 0 NOT NULL COMMENT '短信日累计日数',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE cmm_sms_traffic ADD CONSTRAINT pk_cmm_sms_traffic PRIMARY KEY(mbl_no, trade_date);
--索引
ALTER TABLE cmm_sms_traffic ADD UNIQUE idx_cmm_sms_traffic_1(mbl_no, trade_date);
