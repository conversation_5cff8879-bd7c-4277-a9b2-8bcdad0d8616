apply plugin: 'application'

dependencies {
    implementation 'org.projectlombok:lombok:1.18.26'
    compile project(":cmm-interface")
    //compile("com.hisun:lemon-swagger:1.0.1-SNAPSHOT"){ changing = true }
    //compile("com.hisun:lemon-framework:1.0.1-SNAPSHOT"){ changing = true }
    compile("com.hisun:lemon-swagger")
    compile("com.hisun:lemon-framework")
    compile("com.hisun:lemon-framework-lock")
    compile("com.hisun:lemon-gateway-dto")
    compile("com.hisun:jcommon")
    compile("com.hisun:urm-interface")
    compile("com.hisun:cpi-interface")
    compile('org.springframework.boot:spring-boot-starter-mail')
    compile('com.sun.mail:javax.mail')
}

mainClassName = 'com.hisun.lemon.Application'

jar {
    manifest {
        attributes(
                   "Implementation-Title": "Gradle",
                   "Implementation-Version": "${version}",
                   "Class-Path": '. config/'
                  )
    }
    //exclude('config/')
}

bootRepackage {
    enabled = true
    mainClass = 'com.hisun.lemon.Application'
}

task clearTarget(type:Delete){  
   delete 'build/target'  
}  

task release(type: Copy,dependsOn: [clearTarget,build]) {  
    from('build/libs') {  
        include '*.jar'
        exclude '*-sources.jar'  
    }  
    //from('src/main/resources') {
    //    include 'config/*'
    //}
    into ('build/target') 
    
    into('bin') {
        from 'shell'
    } 
} 

task dist(type: Zip,dependsOn: [release]) {  
    from ('build/target/') {
    } 
}