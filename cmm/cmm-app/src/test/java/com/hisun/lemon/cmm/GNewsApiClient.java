package com.hisun.lemon.cmm;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.client.HttpClient;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

/**
 * @version 1.0
 * <AUTHOR>
 * @date 2025/7/9 20:18
 */
public class GNewsApiClient {
    private static final String API_KEY = "2c9467d0a1d353d56e1de25b66f4d465";
    private static final String API_URL = "https://gnews.io/api/v4/search";

    public static void main(String[] args) {
        try {
            String response = searchNews();
            System.out.println(response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String searchNews() throws IOException {
        HttpClient httpClient = HttpClients.createDefault();
        String query = "加密货币";
        String language = "zh";

        // 构建请求URL，包含查询参数
        String requestUrl = String.format(
                "%s?q=%s&lang=%s&apikey=%s",
                API_URL,
                query,
                language,
                API_KEY
        );

        HttpGet httpGet = new HttpGet(requestUrl);

        // 执行HTTP请求
        HttpResponse response = httpClient.execute(httpGet);
        HttpEntity entity = response.getEntity();

        // 处理响应
        if (entity != null) {
            return EntityUtils.toString(entity);
        }

        return null;
    }
}
