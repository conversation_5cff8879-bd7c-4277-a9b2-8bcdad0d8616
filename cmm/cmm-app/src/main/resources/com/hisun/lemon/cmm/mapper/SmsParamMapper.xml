<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.ISmsParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.SmsParamDO" >
        <id column="id" property="id" jdbcType="CHAR" />
        <result column="dly_cnt_lmt" property="dlyCntLmt" jdbcType="INTEGER" />
        <result column="dly_sms_cnt_lmt" property="dlySmsCntLmt" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, dly_cnt_lmt, dly_sms_cnt_lmt, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_sms_param
        where id = #{id,jdbcType=CHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_sms_param
        where id = #{id,jdbcType=CHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.SmsParamDO" >
        insert into cmm_sms_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="dlyCntLmt != null" >
                dly_cnt_lmt,
            </if>
            <if test="dlySmsCntLmt != null" >
                dly_sms_cnt_lmt,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=CHAR},
            </if>
            <if test="dlyCntLmt != null" >
                #{dlyCntLmt,jdbcType=INTEGER},
            </if>
            <if test="dlySmsCntLmt != null" >
                #{dlySmsCntLmt,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.SmsParamDO" >
        update cmm_sms_param
        <set >
            <if test="dlyCntLmt != null" >
                dly_cnt_lmt = #{dlyCntLmt,jdbcType=INTEGER},
            </if>
            <if test="dlySmsCntLmt != null" >
                dly_sms_cnt_lmt = #{dlySmsCntLmt,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>
</mapper>