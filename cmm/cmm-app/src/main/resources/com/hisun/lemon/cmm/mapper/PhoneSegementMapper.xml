<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IPhoneSegementDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.PhoneSegementDO" >
        <id column="prefix_number" property="prefixNumber" jdbcType="VARCHAR" />
        <result column="carrier" property="carrier" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        prefix_number, carrier, stats, eff_date, exp_date, opr_id, create_time, modify_time, 
        tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_phone_segement
        where prefix_number = #{prefixNumber,jdbcType=VARCHAR}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from cmm_phone_segement
        where prefix_number = left(#{mblNo,jdbcType=VARCHAR}, length(prefix_number))
        and area_code = #{countryCode,jdbcType=VARCHAR}
        and number_length = #{numberLength,jdbcType=NUMERIC}
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from cmm_phone_segement
        <where>
        	<if test="prefixNumber != null" >
                prefix_number = #{prefixNumber,jdbcType=VARCHAR}
            </if>
        	<if test="carrier != null" >
                and carrier = #{carrier,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_phone_segement
        where prefix_number = #{prefixNumber,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.PhoneSegementDO" >
        insert into cmm_phone_segement
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="prefixNumber != null" >
                prefix_number,
            </if>
            <if test="carrier != null" >
                carrier,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="prefixNumber != null" >
                #{prefixNumber,jdbcType=VARCHAR},
            </if>
            <if test="carrier != null" >
                #{carrier,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.PhoneSegementDO" >
        update cmm_phone_segement
        <set >
            <if test="carrier != null" >
                carrier = #{carrier,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where prefix_number = #{prefixNumber,jdbcType=VARCHAR}
    </update>
</mapper>