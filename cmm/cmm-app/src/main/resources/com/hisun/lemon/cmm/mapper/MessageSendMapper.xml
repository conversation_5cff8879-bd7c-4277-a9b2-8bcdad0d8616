<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IMessageSendDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.MessageSendDO" >
        <id column="message_jrn_no" property="messageJrnNo" jdbcType="VARCHAR" />
        <result column="trade_date" property="tradeDate" jdbcType="DATE" />
        <result column="trade_time" property="tradeTime" jdbcType="TIME" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="pass_push_flg" property="passPushFlg" jdbcType="VARCHAR" />
        <result column="user_type" property="userType" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="message_title" property="messageTitle" jdbcType="VARCHAR" />
        <result column="message_content" property="messageContent" jdbcType="VARCHAR" />
        <result column="message_title_en" property="messageTitleEn" jdbcType="VARCHAR" />
        <result column="message_content_en" property="messageContentEn" jdbcType="VARCHAR" />
        <result column="message_title_kh" property="messageTitleKh" jdbcType="VARCHAR" />
        <result column="message_content_kh" property="messageContentKh" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        message_jrn_no, trade_date, trade_time, user_id, pass_push_flg, user_type, type, message_title_en, message_title_kh, message_content_en, message_content_kh,
        message_title, message_content, stats, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_message_send
        where message_jrn_no = #{messageJrnNo,jdbcType=VARCHAR}
    </select>
    
    <select id="countByCondition" resultType="int" >
       select count(1)
         from cmm_message_send
       <where>
           <if test="messageJrnNo != null" >
               message_jrn_no = #{messageJrnNo,jdbcType=VARCHAR}
           </if>
           <if test="tradeDate != null" >
               and trade_date = #{tradeDate,jdbcType=DATE}
           </if>
           <if test="tradeTime != null" >
               and trade_time = #{tradeTime,jdbcType=TIME}
           </if>
           <if test="userId != null" >
               and (user_id = #{userId,jdbcType=VARCHAR} or user_id = #{loginName,jdbcType=VARCHAR})
           </if>
           <if test="passPushFlg != null" >
               and pass_push_flg = #{passPushFlg,jdbcType=VARCHAR}
           </if>
           <if test="userType != null" >
               and user_type = #{userType,jdbcType=VARCHAR}
           </if>
           <if test="type != null" >
               and type = #{type,jdbcType=VARCHAR}
           </if>
           <if test="messageTitle != null" >
               and message_title = #{messageTitle,jdbcType=VARCHAR}
           </if>
           <if test="messageContent != null" >
               and message_content = #{messageContent,jdbcType=VARCHAR}
           </if>
           <if test="stats != null" >
               and stats = #{stats,jdbcType=CHAR}
           </if>
       </where>
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
       select
       <include refid="Base_Column_List" />
       from cmm_message_send
       <where>
           <if test="messageJrnNo != null" >
               message_jrn_no = #{messageJrnNo,jdbcType=VARCHAR}
           </if>
           <if test="tradeDate != null" >
               and trade_date = #{tradeDate,jdbcType=DATE}
           </if>
           <if test="tradeTime != null" >
               and trade_time = #{tradeTime,jdbcType=TIME}
           </if>
           <if test="userId != null" >
               and (user_id = #{userId,jdbcType=VARCHAR} or user_id = #{loginName,jdbcType=VARCHAR})
           </if>
           <if test="passPushFlg != null" >
               and pass_push_flg = #{passPushFlg,jdbcType=VARCHAR}
           </if>
           <if test="userType != null" >
               and user_type = #{userType,jdbcType=VARCHAR}
           </if>
           <if test="type != null" >
               and type = #{type,jdbcType=VARCHAR}
           </if>
           <if test="messageTitle != null" >
               and message_title = #{messageTitle,jdbcType=VARCHAR}
           </if>
           <if test="messageContent != null" >
               and message_content = #{messageContent,jdbcType=VARCHAR}
           </if>
           <if test="stats != null" >
               and stats = #{stats,jdbcType=CHAR}
           </if>
           order by message_jrn_no desc
       </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_message_send
        where message_jrn_no = #{messageJrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.MessageSendDO" >
        insert into cmm_message_send
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="messageJrnNo != null" >
                message_jrn_no,
            </if>
            <if test="tradeDate != null" >
                trade_date,
            </if>
            <if test="tradeTime != null" >
                trade_time,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="passPushFlg != null" >
                pass_push_flg,
            </if>
            <if test="userType != null" >
                user_type,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="messageTitle != null" >
                message_title,
            </if>
            <if test="messageContent != null" >
                message_content,
            </if>

            <if test="messageTitleEn != null" >
                message_title_en,
            </if>
            <if test="messageContentEn != null" >
                message_content_en,
            </if>
            <if test="messageTitleKh != null" >
                message_title_kh,
            </if>
            <if test="messageContentKh != null" >
                message_content_kh,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="messageJrnNo != null" >
                #{messageJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeDate != null" >
                #{tradeDate,jdbcType=DATE},
            </if>
            <if test="tradeTime != null" >
                #{tradeTime,jdbcType=TIME},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="passPushFlg != null" >
                #{passPushFlg,jdbcType=VARCHAR},
            </if>
            <if test="userType != null" >
                #{userType,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="messageTitle != null" >
                #{messageTitle,jdbcType=VARCHAR},
            </if>
            <if test="messageContent != null" >
                #{messageContent,jdbcType=VARCHAR},
            </if>

            <if test="messageTitleEn != null" >
                #{messageTitleEn,jdbcType=VARCHAR},
            </if>
            <if test="messageContentEn != null" >
                #{messageContentEn,jdbcType=VARCHAR},
            </if>
            <if test="messageTitleKh != null" >
                #{messageTitleKh,jdbcType=VARCHAR},
            </if>
            <if test="messageContentKh != null" >
                #{messageContentKh,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.MessageSendDO" >
        update cmm_message_send
        <set >
            <if test="tradeDate != null" >
                trade_date = #{tradeDate,jdbcType=DATE},
            </if>
            <if test="tradeTime != null" >
                trade_time = #{tradeTime,jdbcType=TIME},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="passPushFlg != null" >
                pass_push_flg = #{passPushFlg,jdbcType=VARCHAR},
            </if>
            <if test="userType != null" >
                user_type = #{userType,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="messageTitle != null" >
                message_title = #{messageTitle,jdbcType=VARCHAR},
            </if>
            <if test="messageContent != null" >
                message_content = #{messageContent,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where message_jrn_no = #{messageJrnNo,jdbcType=VARCHAR}
    </update>
    
    <update id="updateByCondition" parameterType="com.hisun.lemon.cmm.entity.MessageSendDO" >
        update cmm_message_send
        <set >
            <if test="valueDO.tradeDate != null" >
                trade_date = #{valueDO.tradeDate,jdbcType=DATE},
            </if>
            <if test="valueDO.tradeTime != null" >
                trade_time = #{valueDO.tradeTime,jdbcType=TIME},
            </if>
            <if test="valueDO.userId != null" >
                user_id = #{valueDO.userId,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.passPushFlg != null" >
                pass_push_flg = #{valueDO.passPushFlg,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.userType != null" >
                user_type = #{valueDO.userType,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.type != null" >
                type = #{valueDO.type,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.messageTitle != null" >
                message_title = #{valueDO.messageTitle,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.messageContent != null" >
                message_content = #{valueDO.messageContent,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.stats != null" >
                stats = #{valueDO.stats,jdbcType=CHAR},
            </if>
            <if test="valueDO.modifyTime != null" >
                modify_time = #{valueDO.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
           <if test="conditionDO.messageJrnNo != null and conditionDO.messageJrnNo != ''" >
               message_jrn_no = #{conditionDO.messageJrnNo,jdbcType=VARCHAR}
           </if>
           <if test="conditionDO.tradeDate != null" >
               and trade_date = #{conditionDO.tradeDate,jdbcType=DATE}
           </if>
           <if test="conditionDO.tradeTime != null" >
               and trade_time = #{conditionDO.tradeTime,jdbcType=TIME}
           </if>
           <if test="conditionDO.userId != null" >
               and (user_id = #{conditionDO.userId,jdbcType=VARCHAR} or user_id = #{conditionDO.loginName,jdbcType=VARCHAR})
           </if>
           <if test="conditionDO.passPushFlg != null" >
               and pass_push_flg = #{conditionDO.passPushFlg,jdbcType=VARCHAR}
           </if>
           <if test="conditionDO.userType != null" >
               and user_type = #{conditionDO.userType,jdbcType=VARCHAR}
           </if>
           <if test="conditionDO.type != null" >
               and type = #{conditionDO.type,jdbcType=VARCHAR}
           </if>
           <if test="conditionDO.messageTitle != null" >
               and message_title = #{conditionDO.messageTitle,jdbcType=VARCHAR}
           </if>
           <if test="conditionDO.messageContent != null" >
               and message_content = #{conditionDO.messageContent,jdbcType=VARCHAR}
           </if>
           <if test="conditionDO.stats != null" >
               and stats = #{conditionDO.stats,jdbcType=CHAR}
           </if>
       </where>
    </update>
</mapper>