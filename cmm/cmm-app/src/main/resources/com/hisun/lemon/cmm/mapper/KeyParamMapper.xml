<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IKeyParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.KeyParamDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="tkey" property="tkey" jdbcType="VARCHAR" />
        <result column="algorithms" property="algorithms" jdbcType="VARCHAR" />
        <result column="channel" property="channel" jdbcType="VARCHAR" />
        <result column="versions" property="versions" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tkey, algorithms, channel, versions, stats, eff_date, exp_date, opr_id, create_time, modify_time, 
        tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_key_param
        where id = #{id,jdbcType=VARCHAR}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from cmm_key_param
        <where>
            <if test="id != null" >
                id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="tkey != null" >
                and tkey = #{tkey,jdbcType=VARCHAR}
            </if>
            <if test="algorithms != null" >
                and algorithms = #{algorithms,jdbcType=VARCHAR}
            </if>
            <if test="channel != null" >
                and channel = #{channel,jdbcType=VARCHAR}
            </if>
            <if test="versions != null" >
                and versions = #{versions,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_key_param
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.KeyParamDO" >
        insert into cmm_key_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="tkey != null" >
                tkey,
            </if>
            <if test="algorithms != null" >
                algorithms,
            </if>
            <if test="channel != null" >
                channel,
            </if>
            <if test="versions != null" >
                versions,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="tkey != null" >
                #{tkey,jdbcType=VARCHAR},
            </if>
            <if test="algorithms != null" >
                #{algorithms,jdbcType=VARCHAR},
            </if>
            <if test="channel != null" >
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="versions != null" >
                #{versions,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.KeyParamDO" >
        update cmm_key_param
        <set >
            <if test="tkey != null" >
                tkey = #{tkey,jdbcType=VARCHAR},
            </if>
            <if test="algorithms != null" >
                algorithms = #{algorithms,jdbcType=VARCHAR},
            </if>
            <if test="channel != null" >
                channel = #{channel,jdbcType=VARCHAR},
            </if>
            <if test="versions != null" >
                versions = #{versions,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>