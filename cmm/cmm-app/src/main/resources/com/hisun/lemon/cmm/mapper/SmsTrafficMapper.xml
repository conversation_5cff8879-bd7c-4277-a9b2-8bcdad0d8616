<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.ISmsTrafficDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.SmsTrafficDO" >
        <id column="mbl_no" property="mblNo" jdbcType="VARCHAR" />
        <id column="trade_date" property="tradeDate" jdbcType="DATE" />
        <result column="dly_cnt" property="dlyCnt" jdbcType="INTEGER" />
        <result column="dly_sms_cnt" property="dlySmsCnt" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        mbl_no, trade_date, dly_cnt, dly_sms_cnt, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from cmm_sms_traffic
        where mbl_no = #{mblNo,jdbcType=VARCHAR}
          and trade_date = #{tradeDate,jdbcType=DATE}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from cmm_sms_traffic
        <where>
        	<if test="mblNo != null" >
                mbl_no = #{mblNo,jdbcType=VARCHAR}
            </if>
        	<if test="tradeDate != null" >
                and trade_date = #{tradeDate,jdbcType=DATE}
            </if>
            <if test="dlyCnt != null" >
                and dly_cnt = #{dlyCnt,jdbcType=INTEGER}
            </if>
            <if test="dlySmsCnt != null" >
                and dly_sms_cnt = #{dlySmsCnt,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="map" >
        delete from cmm_sms_traffic
        where mbl_no = #{mblNo,jdbcType=VARCHAR}
          and trade_date = #{tradeDate,jdbcType=DATE}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.SmsTrafficDO" >
        insert into cmm_sms_traffic
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="mblNo != null" >
                mbl_no,
            </if>
            <if test="tradeDate != null" >
                trade_date,
            </if>
            <if test="dlyCnt != null" >
                dly_cnt,
            </if>
            <if test="dlySmsCnt != null" >
                dly_sms_cnt,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeDate != null" >
                #{tradeDate,jdbcType=DATE},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="dlyCnt != null" >
                #{dlyCnt,jdbcType=INTEGER},
            </if>
            <if test="dlySmsCnt != null" >
                #{dlySmsCnt,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    
    <insert id="insertOrUpdate" parameterType="com.hisun.lemon.cmm.entity.SmsTrafficDO" >
        insert into cmm_sms_traffic
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="mblNo != null" >
                mbl_no,
            </if>
            <if test="tradeDate != null" >
                trade_date,
            </if>
            <if test="dlyCnt != null" >
                dly_cnt,
            </if>
            <if test="dlySmsCnt != null" >
                dly_sms_cnt,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeDate != null" >
                #{tradeDate,jdbcType=DATE},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="dlyCnt != null" >
                #{dlyCnt,jdbcType=INTEGER},
            </if>
            <if test="dlySmsCnt != null" >
                #{dlySmsCnt,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update 
        <if test="dlyCnt != null" >
            dly_cnt = dly_cnt + 1, modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        </if>
        <if test="dlySmsCnt != null" >
            dly_sms_cnt = dly_sms_cnt + 1, modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        </if>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.SmsTrafficDO" >
        update cmm_sms_traffic
        <set >
            <if test="dlyCnt != null" >
                dly_cnt = #{dlyCnt,jdbcType=INTEGER},
            </if>
            <if test="dlySmsCnt != null" >
                dly_sms_cnt = #{dlySmsCnt,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where mbl_no = #{mblNo,jdbcType=VARCHAR}
          and trade_date = #{tradeDate,jdbcType=DATE}
    </update>
</mapper>