<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IBannerPulblicDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.BannerPulblicDO" >
        <id column="id" property="id" jdbcType="CHAR" />
        <result column="banner_url_kh" property="bannerUrlKh" jdbcType="VARCHAR" />
        <result column="banner_url_cn" property="bannerUrlCn" jdbcType="VARCHAR" />
        <result column="banner_url_en" property="bannerUrlEn" jdbcType="VARCHAR" />
        <result column="detail_url_kh" property="detailUrlKh" jdbcType="VARCHAR" />
        <result column="detail_url_cn" property="detailUrlCn" jdbcType="VARCHAR" />
        <result column="detail_url_en" property="detailUrlEn" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, banner_url_kh, banner_url_cn, banner_url_en, detail_url_kh, detail_url_cn, detail_url_en, 
        stats, eff_date, exp_date, opr_id, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_banner_public
        where id = #{id,jdbcType=CHAR}
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from cmm_banner_public
        <where>
            <if test="id != null" >
                id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="bannerUrlKh != null" >
                and banner_url_kh = #{bannerUrlKh,jdbcType=VARCHAR}
            </if>
            <if test="bannerUrlCn != null" >
                and banner_url_cn = #{bannerUrlCn,jdbcType=VARCHAR}
            </if>
            <if test="bannerUrlEn != null" >
                and banner_url_en = #{bannerUrlEn,jdbcType=VARCHAR}
            </if>
            <if test="detailUrlKh != null" >
                and detail_url_kh = #{detailUrlKh,jdbcType=VARCHAR}
            </if>
            <if test="detailUrlCn != null" >
                and detail_url_cn = #{detailUrlCn,jdbcType=VARCHAR}
            </if>
            <if test="detailUrlEn != null" >
                and detail_url_en = #{detailUrlEn,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and #{effDate,jdbcType=DATE} between eff_date and exp_date
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
        order by id desc
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_banner_public
        where id = #{id,jdbcType=CHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.BannerPulblicDO" >
        insert into cmm_banner_public
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="bannerUrlKh != null" >
                banner_url_kh,
            </if>
            <if test="bannerUrlCn != null" >
                banner_url_cn,
            </if>
            <if test="bannerUrlEn != null" >
                banner_url_en,
            </if>
            <if test="detailUrlKh != null" >
                detail_url_kh,
            </if>
            <if test="detailUrlCn != null" >
                detail_url_cn,
            </if>
            <if test="detailUrlEn != null" >
                detail_url_en,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=CHAR},
            </if>
            <if test="bannerUrlKh != null" >
                #{bannerUrlKh,jdbcType=VARCHAR},
            </if>
            <if test="bannerUrlCn != null" >
                #{bannerUrlCn,jdbcType=VARCHAR},
            </if>
            <if test="bannerUrlEn != null" >
                #{bannerUrlEn,jdbcType=VARCHAR},
            </if>
            <if test="detailUrlKh != null" >
                #{detailUrlKh,jdbcType=VARCHAR},
            </if>
            <if test="detailUrlCn != null" >
                #{detailUrlCn,jdbcType=VARCHAR},
            </if>
            <if test="detailUrlEn != null" >
                #{detailUrlEn,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.BannerPulblicDO" >
        update cmm_banner_public
        <set >
            <if test="bannerUrlKh != null" >
                banner_url_kh = #{bannerUrlKh,jdbcType=VARCHAR},
            </if>
            <if test="bannerUrlCn != null" >
                banner_url_cn = #{bannerUrlCn,jdbcType=VARCHAR},
            </if>
            <if test="bannerUrlEn != null" >
                banner_url_en = #{bannerUrlEn,jdbcType=VARCHAR},
            </if>
            <if test="detailUrlKh != null" >
                detail_url_kh = #{detailUrlKh,jdbcType=VARCHAR},
            </if>
            <if test="detailUrlCn != null" >
                detail_url_cn = #{detailUrlCn,jdbcType=VARCHAR},
            </if>
            <if test="detailUrlEn != null" >
                detail_url_en = #{detailUrlEn,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>
</mapper>