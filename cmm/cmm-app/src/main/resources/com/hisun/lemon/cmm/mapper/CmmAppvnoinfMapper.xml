<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.ICmmAppvnoinfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.CmmAppvnoinfDO" >
        <id column="app_no" property="appNo" jdbcType="VARCHAR" />
        <result column="app_typ" property="appTyp" jdbcType="VARCHAR" />
        <result column="app_sys" property="appSys" jdbcType="VARCHAR" />
        <result column="app_size" property="appSize" jdbcType="VARCHAR" />
        <result column="app_version" property="appVersion" jdbcType="VARCHAR" />
        <result column="updateinf" property="updateinf" jdbcType="VARCHAR" />
        <result column="app_url" property="appUrl" jdbcType="VARCHAR" />
        <result column="is_true" property="isTrue" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        app_no, app_typ, app_sys, app_size, app_version, updateinf, app_url, is_true, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cmm_appvnoinf
        where app_no = #{appNo,jdbcType=VARCHAR}
    </select>

    <select id="getAppvnoinfo" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cmm_appvnoinf
        <where>
            <if test="appTyp != null" >
                app_typ = #{appTyp,jdbcType=VARCHAR}
            </if>
            <if test="appSys != null" >
                and app_sys = #{appSys,jdbcType=VARCHAR}
            </if>
            <if test="appSize != null" >
                and app_size = #{appSize,jdbcType=VARCHAR}
            </if>
            <if test="appVersion != null" >
                and app_version = #{appVersion,jdbcType=VARCHAR}
            </if>
            <if test="updateinf != null" >
                and updateinf = #{updateinf,jdbcType=VARCHAR}
            </if>
            <if test="appUrl != null" >
                and app_url = #{appUrl,jdbcType=VARCHAR}
            </if>
            <if test="isTrue != null" >
                and is_true = #{isTrue,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
        </where>
        order by app_no desc limit 1
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_appvnoinf
        where app_no = #{appNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.CmmAppvnoinfDO" >
        insert into cmm_appvnoinf
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="appNo != null" >
                app_no,
            </if>
            <if test="appTyp != null" >
                app_typ,
            </if>
            <if test="appSys != null" >
                app_sys,
            </if>
            <if test="appSize != null" >
                app_size,
            </if>
            <if test="appVersion != null" >
                app_version,
            </if>
            <if test="updateinf != null" >
                updateinf,
            </if>
            <if test="appUrl != null" >
                app_url,
            </if>
            <if test="isTrue != null" >
                is_true,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="appNo != null" >
                #{appNo,jdbcType=VARCHAR},
            </if>
            <if test="appTyp != null" >
                #{appTyp,jdbcType=VARCHAR},
            </if>
            <if test="appSys != null" >
                #{appSys,jdbcType=VARCHAR},
            </if>
            <if test="appSize != null" >
                #{appSize,jdbcType=VARCHAR},
            </if>
            <if test="appVersion != null" >
                #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="updateinf != null" >
                #{updateinf,jdbcType=VARCHAR},
            </if>
            <if test="appUrl != null" >
                #{appUrl,jdbcType=VARCHAR},
            </if>
            <if test="isTrue != null" >
                #{isTrue,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.CmmAppvnoinfDO" >
        update cmm_appvnoinf
        <set >
            <if test="appTyp != null" >
                app_typ = #{appTyp,jdbcType=VARCHAR},
            </if>
            <if test="appSys != null" >
                app_sys = #{appSys,jdbcType=VARCHAR},
            </if>
            <if test="appSize != null" >
                app_size = #{appSize,jdbcType=VARCHAR},
            </if>
            <if test="appVersion != null" >
                app_version = #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="updateinf != null" >
                updateinf = #{updateinf,jdbcType=VARCHAR},
            </if>
            <if test="appUrl != null" >
                app_url = #{appUrl,jdbcType=VARCHAR},
            </if>
            <if test="isTrue != null" >
                is_true = #{isTrue,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
        </set>
        where app_no = #{appNo,jdbcType=VARCHAR}
    </update>
</mapper>