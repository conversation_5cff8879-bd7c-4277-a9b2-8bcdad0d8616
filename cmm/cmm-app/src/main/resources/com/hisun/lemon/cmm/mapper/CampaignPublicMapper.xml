<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.ICampaignPublicDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.CampaignPublicDO" >
        <id column="id" property="id" jdbcType="CHAR" />
        <result column="campaign_title_kh" property="campaignTitleKh" jdbcType="VARCHAR" />
        <result column="campaign_title_cn" property="campaignTitleCn" jdbcType="VARCHAR" />
        <result column="campaign_title_en" property="campaignTitleEn" jdbcType="VARCHAR" />
        <result column="campaign_content_kh" property="campaignContentKh" jdbcType="VARCHAR" />
        <result column="campaign_content_cn" property="campaignContentCn" jdbcType="VARCHAR" />
        <result column="campaign_content_en" property="campaignContentEn" jdbcType="VARCHAR" />
        <result column="channel" property="channel" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, campaign_title_kh, campaign_title_cn, campaign_title_en, campaign_content_kh, 
        campaign_content_cn, campaign_content_en, stats, eff_date, exp_date, opr_id, create_time, channel,
        modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_campaign_public
        where id = #{id,jdbcType=CHAR}
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from cmm_campaign_public
        <where>
            <if test="id != null" >
                id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="campaignTitleKh != null" >
                campaign_title_kh = #{campaignTitleKh,jdbcType=VARCHAR}
            </if>
            <if test="campaignTitleCn != null" >
                campaign_title_cn = #{campaignTitleCn,jdbcType=VARCHAR}
            </if>
            <if test="campaignTitleEn != null" >
                campaign_title_en = #{campaignTitleEn,jdbcType=VARCHAR}
            </if>
            <if test="campaignContentKh != null" >
                campaign_content_kh = #{campaignContentKh,jdbcType=VARCHAR}
            </if>
            <if test="campaignContentCn != null" >
                campaign_content_cn = #{campaignContentCn,jdbcType=VARCHAR}
            </if>
            <if test="campaignContentEn != null" >
                campaign_content_en = #{campaignContentEn,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and #{effDate,jdbcType=DATE} between eff_date and exp_date
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
            <if test="channel != null" >
                and channel = #{channel,jdbcType=VARCHAR}
            </if>
        </where>
        order by id desc
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_campaign_public
        where id = #{id,jdbcType=CHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.CampaignPublicDO" >
        insert into cmm_campaign_public
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="campaignTitleKh != null" >
                campaign_title_kh,
            </if>
            <if test="campaignTitleCn != null" >
                campaign_title_cn,
            </if>
            <if test="campaignTitleEn != null" >
                campaign_title_en,
            </if>
            <if test="campaignContentKh != null" >
                campaign_content_kh,
            </if>
            <if test="campaignContentCn != null" >
                campaign_content_cn,
            </if>
            <if test="campaignContentEn != null" >
                campaign_content_en,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=CHAR},
            </if>
            <if test="campaignTitleKh != null" >
                #{campaignTitleKh,jdbcType=VARCHAR},
            </if>
            <if test="campaignTitleCn != null" >
                #{campaignTitleCn,jdbcType=VARCHAR},
            </if>
            <if test="campaignTitleEn != null" >
                #{campaignTitleEn,jdbcType=VARCHAR},
            </if>
            <if test="campaignContentKh != null" >
                #{campaignContentKh,jdbcType=VARCHAR},
            </if>
            <if test="campaignContentCn != null" >
                #{campaignContentCn,jdbcType=VARCHAR},
            </if>
            <if test="campaignContentEn != null" >
                #{campaignContentEn,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.CampaignPublicDO" >
        update cmm_campaign_public
        <set >
            <if test="campaignTitleKh != null" >
                campaign_title_kh = #{campaignTitleKh,jdbcType=VARCHAR},
            </if>
            <if test="campaignTitleCn != null" >
                campaign_title_cn = #{campaignTitleCn,jdbcType=VARCHAR},
            </if>
            <if test="campaignTitleEn != null" >
                campaign_title_en = #{campaignTitleEn,jdbcType=VARCHAR},
            </if>
            <if test="campaignContentKh != null" >
                campaign_content_kh = #{campaignContentKh,jdbcType=VARCHAR},
            </if>
            <if test="campaignContentCn != null" >
                campaign_content_cn = #{campaignContentCn,jdbcType=VARCHAR},
            </if>
            <if test="campaignContentEn != null" >
                campaign_content_en = #{campaignContentEn,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>
</mapper>