<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.ISmsSendDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.SmsSendDO" >
        <id column="sms_jrn_no" property="smsJrnNo" jdbcType="VARCHAR" />
        <result column="trade_date" property="tradeDate" jdbcType="DATE" />
        <result column="trade_time" property="tradeTime" jdbcType="TIME" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="lvl" property="lvl" jdbcType="VARCHAR" />
        <result column="mbl_no" property="mblNo" jdbcType="VARCHAR" />
        <result column="sms_content" property="smsContent" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        sms_jrn_no, trade_date, trade_time, type, lvl, mbl_no, sms_content, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_sms_send
        where sms_jrn_no = #{smsJrnNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_sms_send
        where sms_jrn_no = #{smsJrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.SmsSendDO" >
        insert into cmm_sms_send
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="smsJrnNo != null" >
                sms_jrn_no,
            </if>
            <if test="tradeDate != null" >
                trade_date,
            </if>
            <if test="tradeTime != null" >
                trade_time,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="lvl != null" >
                lvl,
            </if>
            <if test="mblNo != null" >
                mbl_no,
            </if>
            <if test="smsContent != null" >
                sms_content,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="smsJrnNo != null" >
                #{smsJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeDate != null" >
                #{tradeDate,jdbcType=DATE},
            </if>
            <if test="tradeTime != null" >
                #{tradeTime,jdbcType=TIME},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="lvl != null" >
                #{lvl,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="smsContent != null" >
                #{smsContent,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.SmsSendDO" >
        update cmm_sms_send
        <set >
        	<if test="tradeDate != null" >
                trade_date = #{tradeDate,jdbcType=DATE},
            </if>
            <if test="tradeTime != null" >
                trade_time = #{tradeTime,jdbcType=TIME},
            </if>
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="lvl != null" >
                lvl = #{lvl,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                mbl_no = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="smsContent != null" >
                sms_content = #{smsContent,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where sms_jrn_no = #{smsJrnNo,jdbcType=VARCHAR}
    </update>
</mapper>