<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.INoticPulblicDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.NoticPulblicDO" >
        <id column="id" property="id" jdbcType="CHAR" />
        <result column="notice_title_kh" property="noticeTitleKh" jdbcType="VARCHAR" />
        <result column="notice_title_cn" property="noticeTitleCn" jdbcType="VARCHAR" />
        <result column="notice_title_en" property="noticeTitleEn" jdbcType="VARCHAR" />
        <result column="notice_content_kh" property="noticeContentKh" jdbcType="VARCHAR" />
        <result column="notice_content_cn" property="noticeContentCn" jdbcType="VARCHAR" />
        <result column="notice_content_en" property="noticeContentEn" jdbcType="VARCHAR" />
        <result column="channel" property="channel" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, notice_title_kh, notice_title_cn, notice_title_en, notice_content_kh, notice_content_cn, 
        notice_content_en, stats, eff_date, exp_date, opr_id, create_time, modify_time, tm_smp, channel
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_notice_public
        where id = #{id,jdbcType=CHAR}
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from cmm_notice_public
        <where>
            <if test="id != null" >
                id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="noticeTitleKh != null" >
                and notice_title_kh = #{noticeTitleKh,jdbcType=VARCHAR}
            </if>
            <if test="noticeTitleCn != null" >
                and notice_title_cn = #{noticeTitleCn,jdbcType=VARCHAR}
            </if>
            <if test="noticeTitleEn != null" >
                and notice_title_en = #{noticeTitleEn,jdbcType=VARCHAR}
            </if>
            <if test="noticeContentKh != null" >
                and notice_content_kh = #{noticeContentKh,jdbcType=VARCHAR}
            </if>
            <if test="noticeContentCn != null" >
                and notice_content_cn = #{noticeContentCn,jdbcType=VARCHAR}
            </if>
            <if test="noticeContentEn != null" >
                and notice_content_en = #{noticeContentEn,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and #{effDate,jdbcType=DATE} between eff_date and exp_date
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="channel != null" >
                and channel = #{channel,jdbcType=VARCHAR}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
        order by id desc
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_notice_public
        where id = #{id,jdbcType=CHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.NoticPulblicDO" >
        insert into cmm_notice_public
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="noticeTitleKh != null" >
                notice_title_kh,
            </if>
            <if test="noticeTitleCn != null" >
                notice_title_cn,
            </if>
            <if test="noticeTitleEn != null" >
                notice_title_en,
            </if>
            <if test="noticeContentKh != null" >
                notice_content_kh,
            </if>
            <if test="noticeContentCn != null" >
                notice_content_cn,
            </if>
            <if test="noticeContentEn != null" >
                notice_content_en,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=CHAR},
            </if>
            <if test="noticeTitleKh != null" >
                #{noticeTitleKh,jdbcType=VARCHAR},
            </if>
            <if test="noticeTitleCn != null" >
                #{noticeTitleCn,jdbcType=VARCHAR},
            </if>
            <if test="noticeTitleEn != null" >
                #{noticeTitleEn,jdbcType=VARCHAR},
            </if>
            <if test="noticeContentKh != null" >
                #{noticeContentKh,jdbcType=VARCHAR},
            </if>
            <if test="noticeContentCn != null" >
                #{noticeContentCn,jdbcType=VARCHAR},
            </if>
            <if test="noticeContentEn != null" >
                #{noticeContentEn,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.NoticPulblicDO" >
        update cmm_notice_public
        <set >
            <if test="noticeTitleKh != null" >
                notice_title_kh = #{noticeTitleKh,jdbcType=VARCHAR},
            </if>
            <if test="noticeTitleCn != null" >
                notice_title_cn = #{noticeTitleCn,jdbcType=VARCHAR},
            </if>
            <if test="noticeTitleEn != null" >
                notice_title_en = #{noticeTitleEn,jdbcType=VARCHAR},
            </if>
            <if test="noticeContentKh != null" >
                notice_content_kh = #{noticeContentKh,jdbcType=VARCHAR},
            </if>
            <if test="noticeContentCn != null" >
                notice_content_cn = #{noticeContentCn,jdbcType=VARCHAR},
            </if>
            <if test="noticeContentEn != null" >
                notice_content_en = #{noticeContentEn,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>
</mapper>