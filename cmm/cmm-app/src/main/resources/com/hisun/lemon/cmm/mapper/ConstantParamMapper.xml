<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IConstantParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.ConstantParamDo" >
        <id column="parm_nm" property="parmNm" jdbcType="VARCHAR" />
        <result column="eff_dt" property="effDt" jdbcType="DATE" />
        <result column="exp_dt" property="expDt" jdbcType="DATE" />
        <result column="eff_flg" property="effFlg" jdbcType="CHAR" />
        <result column="parm_disp_nm" property="parmDispNm" jdbcType="VARCHAR" />
        <result column="parm_cls" property="parmCls" jdbcType="VARCHAR" />
        <result column="parm_val" property="parmVal" jdbcType="VARCHAR" />
        <result column="rmk" property="rmk" jdbcType="VARCHAR" />
        <result column="upd_opr_id" property="updOprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        parm_nm, eff_dt, exp_dt, eff_flg, parm_disp_nm, parm_cls, parm_val, rmk, upd_opr_id, create_time, modify_time
    </sql>

    <select id="selectParam" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from cmm_constant_param
        where parm_nm = #{parmNm,jdbcType=VARCHAR} and eff_dt &lt; now() and exp_dt &gt; now()
    </select>

    <select id="selectAllParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cmm_constant_param
    </select>

    <insert id="insertParam" parameterType="com.hisun.lemon.cmm.entity.ConstantParamDo">
        insert into cmm_constant_param (
            parm_nm, eff_dt, exp_dt, eff_flg, parm_disp_nm, parm_cls,
            parm_val, rmk, upd_opr_id, create_time, modify_time) values (
            #{parmNm,jdbcType=VARCHAR},#{effDt,jdbcType=DATE},#{expDt,jdbcType=DATE},
            #{effFlg,jdbcType=CHAR},#{parmDispNm,jdbcType=VARCHAR},#{parmCls,jdbcType=VARCHAR},
            #{parmVal,jdbcType=VARCHAR},#{rmk,jdbcType=VARCHAR},#{updOprId,jdbcType=VARCHAR},
            #{createTime,jdbcType=VARCHAR},#{modifyTime,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateParam" parameterType="com.hisun.lemon.cmm.entity.ConstantParamDo">
        update cmm_constant_param
        <set >
            <if test="effDt != null" >
                eff_dt = #{effDt,jdbcType=DATE},
            </if>
            <if test="expDt != null" >
                exp_dt = #{expDt,jdbcType=DATE},
            </if>
            <if test="effFlg != null" >
                eff_flg = #{effFlg,jdbcType=CHAR},
            </if>
            <if test="parmDispNm != null" >
                parm_disp_nm = #{parmDispNm,jdbcType=VARCHAR},
            </if>
            <if test="parmCls != null" >
                parm_cls = #{parmCls,jdbcType=VARCHAR},
            </if>
            <if test="parmVal != null" >
                parm_val = #{parmVal,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                rmk = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null" >
                upd_opr_id = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where parm_nm = #{parmNm,jdbcType=VARCHAR}
    </update>

    <delete id="deleteParam" parameterType="java.lang.String">
        delete from cmm_constant_param where parm_nm = #{parmNm,jdbcType=VARCHAR}
    </delete>

    <select id="selectParamGroup" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cmm_constant_param where parm_nm = #{parmNm,jdbcType=VARCHAR}
        and eff_flg = '1' and eff_dt &lt; now() and exp_dt &gt; now()
    </select>

</mapper>