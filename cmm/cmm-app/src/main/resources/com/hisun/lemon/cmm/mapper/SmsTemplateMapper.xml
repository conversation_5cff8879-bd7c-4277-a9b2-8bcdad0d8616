<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.ISmsTemplateDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.SmsTemplateDO" >
        <id column="id" property="id" jdbcType="CHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="lvl" property="lvl" jdbcType="VARCHAR" />
        <result column="replace_field" property="replaceField" jdbcType="VARCHAR" />
        <result column="template_content_kh" property="templateContentKh" jdbcType="VARCHAR" />
        <result column="template_content_cn" property="templateContentCn" jdbcType="VARCHAR" />
        <result column="template_content_en" property="templateContentEn" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, type, lvl, replace_field, template_content_kh, template_content_cn, template_content_en, 
        stats, eff_date, exp_date, opr_id, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_sms_template
        where id = #{id,jdbcType=CHAR}
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cmm.entity.SmsTemplateDO" >
        select 
        <include refid="Base_Column_List" />
        from cmm_sms_template
        <where>
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="lvl != null" >
                and lvl = #{lvl,jdbcType=VARCHAR}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
            order by tm_smp desc
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_sms_template
        where id = #{id,jdbcType=CHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.SmsTemplateDO" >
        insert into cmm_sms_template
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="lvl != null" >
                lvl,
            </if>
            <if test="replaceField != null" >
                replace_field,
            </if>
            <if test="templateContentKh != null" >
                template_content_kh,
            </if>
            <if test="templateContentCn != null" >
                template_content_cn,
            </if>
            <if test="templateContentEn != null" >
                template_content_en,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=CHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="lvl != null" >
                #{lvl,jdbcType=VARCHAR},
            </if>
            <if test="replaceField != null" >
                #{replaceField,jdbcType=VARCHAR},
            </if>
            <if test="templateContentKh != null" >
                #{templateContentKh,jdbcType=VARCHAR},
            </if>
            <if test="templateContentCn != null" >
                #{templateContentCn,jdbcType=VARCHAR},
            </if>
            <if test="templateContentEn != null" >
                #{templateContentEn,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.SmsTemplateDO" >
        update cmm_sms_template
        <set >
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="lvl != null" >
                lvl = #{lvl,jdbcType=VARCHAR},
            </if>
            <if test="replaceField != null" >
                replace_field = #{replaceField,jdbcType=VARCHAR},
            </if>
            <if test="templateContentKh != null" >
                template_content_kh = #{templateContentKh,jdbcType=VARCHAR},
            </if>
            <if test="templateContentCn != null" >
                template_content_cn = #{templateContentCn,jdbcType=VARCHAR},
            </if>
            <if test="templateContentEn != null" >
                template_content_en = #{templateContentEn,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>
</mapper>