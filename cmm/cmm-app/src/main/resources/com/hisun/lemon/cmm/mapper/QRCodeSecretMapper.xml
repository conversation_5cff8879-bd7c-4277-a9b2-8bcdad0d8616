<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IQRCodeSecretDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.QRCodeSecretDO" >
        <id column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="barcode_secret_key" property="barcodeSecretKey" jdbcType="CHAR" />
        <result column="qrcode_secret_key" property="qrcodeSecretKey" jdbcType="CHAR" />
        <result column="secret_key_status" property="secretKeyStatus" jdbcType="CHAR" />
        <result column="eff_time" property="effTime" jdbcType="TIMESTAMP" />
        <result column="exp_time" property="expTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        user_id, barcode_secret_key, qrcode_secret_key, secret_key_status, eff_time, exp_time, 
        create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_qrcode_secret
        where user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_qrcode_secret
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.QRCodeSecretDO" >
        insert into cmm_qrcode_secret
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                user_id,
            </if>
            <if test="barcodeSecretKey != null" >
                barcode_secret_key,
            </if>
            <if test="qrcodeSecretKey != null" >
                qrcode_secret_key,
            </if>
            <if test="secretKeyStatus != null" >
                secret_key_status,
            </if>
            <if test="effTime != null" >
                eff_time,
            </if>
            <if test="expTime != null" >
                exp_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="barcodeSecretKey != null" >
                #{barcodeSecretKey,jdbcType=CHAR},
            </if>
            <if test="qrcodeSecretKey != null" >
                #{qrcodeSecretKey,jdbcType=CHAR},
            </if>
            <if test="secretKeyStatus != null" >
                #{secretKeyStatus,jdbcType=CHAR},
            </if>
            <if test="effTime != null" >
                #{effTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expTime != null" >
                #{expTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.QRCodeSecretDO" >
        update cmm_qrcode_secret
        <set >
            <if test="barcodeSecretKey != null" >
                barcode_secret_key = #{barcodeSecretKey,jdbcType=CHAR},
            </if>
            <if test="qrcodeSecretKey != null" >
                qrcode_secret_key = #{qrcodeSecretKey,jdbcType=CHAR},
            </if>
            <if test="secretKeyStatus != null" >
                secret_key_status = #{secretKeyStatus,jdbcType=CHAR},
            </if>
            <if test="effTime != null" >
                eff_time = #{effTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expTime != null" >
                exp_time = #{expTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>
</mapper>