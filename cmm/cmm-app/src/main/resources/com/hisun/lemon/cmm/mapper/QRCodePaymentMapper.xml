<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IQRCodePaymentDao">

	<resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.QRCodePaymentDO">
		<id column="code_jrn_no" property="codeJrnNo" jdbcType="VARCHAR" />
		<result column="user_id" property="userId" jdbcType="VARCHAR" />
		<result column="code_info" property="codeInfo" jdbcType="CHAR" />
		<result column="type" property="type" jdbcType="VARCHAR" />
		<result column="code_stats" property="codeStats" jdbcType="CHAR" />
		<result column="eff_time" property="effTime" jdbcType="TIMESTAMP" />
		<result column="exp_time" property="expTime" jdbcType="TIMESTAMP" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
		<result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
	</resultMap>

	<sql id="Base_Column_List">
		code_jrn_no, user_id, code_info, type, code_stats, eff_time, exp_time,
		create_time,
		modify_time, tm_smp
	</sql>

	<select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from cmm_qrcode_payment
		where code_jrn_no = #{codeJrnNo,jdbcType=VARCHAR}
	</select>

	<select id="getByCondition" resultMap="BaseResultMap"
		parameterType="com.hisun.lemon.cmm.entity.QRCodePaymentDO">
		select
		<include refid="Base_Column_List" />
		from cmm_qrcode_payment
		<where>
			<if test="codeJrnNo != null">
				code_jrn_no = #{codeJrnNo,jdbcType=VARCHAR}
			</if>
			<if test="userId != null">
				and user_id = #{userId,jdbcType=VARCHAR}
			</if>
			<if test="codeInfo != null">
				and code_info = #{codeInfo,jdbcType=CHAR}
			</if>
			<if test="type != null">
				and type = #{type,jdbcType=VARCHAR}
			</if>
			<if test="codeStats != null">
				and code_stats = #{codeStats,jdbcType=CHAR}
			</if>
			<if test="effTime != null">
				and eff_time = #{effTime,jdbcType=TIMESTAMP}
			</if>
			<if test="expTime != null">
				and exp_time &gt;= #{expTime,jdbcType=TIMESTAMP}
			</if>
		</where>
	</select>

	<delete id="delete" parameterType="java.lang.String">
		delete from cmm_qrcode_payment
		where code_jrn_no = #{codeJrnNo,jdbcType=VARCHAR}
	</delete>

	<insert id="insert" parameterType="com.hisun.lemon.cmm.entity.QRCodePaymentDO">
		insert into cmm_qrcode_payment
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="codeJrnNo != null">
				code_jrn_no,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="codeInfo != null">
				code_info,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="codeStats != null">
				code_stats,
			</if>
			<if test="effTime != null">
				eff_time,
			</if>
			<if test="expTime != null">
				exp_time,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="modifyTime != null">
				modify_time,
			</if>
			<if test="tmSmp != null">
				tm_smp,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="codeJrnNo != null">
				#{codeJrnNo,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
				#{userId,jdbcType=VARCHAR},
			</if>
			<if test="codeInfo != null">
				#{codeInfo,jdbcType=CHAR},
			</if>
			<if test="type != null">
				#{type,jdbcType=VARCHAR},
			</if>
			<if test="codeStats != null">
				#{codeStats,jdbcType=CHAR},
			</if>
			<if test="effTime != null">
				#{effTime,jdbcType=TIMESTAMP},
			</if>
			<if test="expTime != null">
				#{expTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyTime != null">
				#{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="tmSmp != null">
				#{tmSmp,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>

	<update id="update" parameterType="com.hisun.lemon.cmm.entity.QRCodePaymentDO">
		update cmm_qrcode_payment
		<set>
			<if test="userId != null">
				user_id = #{userId,jdbcType=VARCHAR},
			</if>
			<if test="codeInfo != null">
				code_info = #{codeInfo,jdbcType=CHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=VARCHAR},
			</if>
			<if test="codeStats != null">
				code_stats = #{codeStats,jdbcType=CHAR},
			</if>
			<if test="effTime != null">
				eff_time = #{effTime,jdbcType=TIMESTAMP},
			</if>
			<if test="expTime != null">
				exp_time = #{expTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyTime != null">
				modify_time = #{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="tmSmp != null">
				tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
			</if>
		</set>
		where code_jrn_no = #{codeJrnNo,jdbcType=VARCHAR}
	</update>
	
	<update id="updateByCondition">
		update cmm_qrcode_payment
		<set>
			<if test="valueDO.userId != null">
				user_id = #{valueDO.userId,jdbcType=VARCHAR},
			</if>
			<if test="valueDO.codeInfo != null">
				code_info = #{valueDO.codeInfo,jdbcType=CHAR},
			</if>
			<if test="valueDO.type != null">
				type = #{type,jdbcType=VARCHAR},
			</if>
			<if test="valueDO.codeStats != null">
				code_stats = #{valueDO.codeStats,jdbcType=CHAR},
			</if>
			<if test="valueDO.effTime != null">
				eff_time = #{valueDO.effTime,jdbcType=TIMESTAMP},
			</if>
			<if test="valueDO.expTime != null">
				exp_time = #{valueDO.expTime,jdbcType=TIMESTAMP},
			</if>
			<if test="valueDO.createTime != null">
				create_time = #{valueDO.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="valueDO.modifyTime != null">
				modify_time = #{valueDO.modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="valueDO.tmSmp != null">
				tm_smp = #{valueDO.tmSmp,jdbcType=TIMESTAMP},
			</if>
		</set>
		<where>
			<if test="condtionDO.codeJrnNo != null">
				code_jrn_no = #{condtionDO.codeJrnNo,jdbcType=VARCHAR}
			</if>
			<if test="condtionDO.userId != null">
				and user_id = #{condtionDO.userId,jdbcType=VARCHAR}
			</if>
			<if test="condtionDO.codeInfo != null">
				and code_info = #{condtionDO.codeInfo,jdbcType=CHAR}
			</if>
			<if test="condtionDO.type != null">
				and type = #{condtionDO.type,jdbcType=VARCHAR}
			</if>
			<if test="condtionDO.codeStats != null">
				and code_stats = #{condtionDO.codeStats,jdbcType=CHAR}
			</if>
			<if test="condtionDO.effTime != null">
				and eff_time = #{condtionDO.effTime,jdbcType=TIMESTAMP}
			</if>
			<if test="condtionDO.expTime != null">
				and exp_time &gt;= #{condtionDO.expTime,jdbcType=TIMESTAMP}
			</if>
		</where>
	</update>
</mapper>