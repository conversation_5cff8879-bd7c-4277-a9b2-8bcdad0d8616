<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.ISmsCodeDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.SmsCodeDO" >
        <id column="code_jrn_no" property="codeJrnNo" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="mbl_no" property="mblNo" jdbcType="VARCHAR" />
        <result column="sms_code" property="smsCode" jdbcType="VARCHAR" />
        <result column="token" property="token" jdbcType="VARCHAR" />
        <result column="code_stats" property="codeStats" jdbcType="CHAR" />
        <result column="eff_time" property="effTime" jdbcType="TIMESTAMP" />
        <result column="exp_time" property="expTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        code_jrn_no, type, mbl_no, sms_code, token, code_stats, eff_time, exp_time, create_time, 
        modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_sms_code
        where code_jrn_no = #{codeJrnNo,jdbcType=VARCHAR}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cmm.entity.SmsCodeDO" >
        select 
        <include refid="Base_Column_List" />
        from cmm_sms_code
        <where>
        	<if test="codeJrnNo != null" >
                code_jrn_no = #{codeJrnNo,jdbcType=VARCHAR}
            </if>
            <if test="type != null" >
                and type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="mblNo != null" >
                and mbl_no = #{mblNo,jdbcType=VARCHAR}
            </if>
            <if test="smsCode != null" >
                and sms_code = #{smsCode,jdbcType=VARCHAR}
            </if>
            <if test="token != null" >
                and token = #{token,jdbcType=VARCHAR}
            </if>
            <if test="codeStats != null" >
                and code_stats = #{codeStats,jdbcType=CHAR}
            </if>
            <if test="effTime != null" >
                and eff_time = #{effTime,jdbcType=TIMESTAMP}
            </if>
            <if test="expTime != null" >
                and exp_time &gt;= #{expTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_sms_code
        where code_jrn_no = #{codeJrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.SmsCodeDO" >
        insert into cmm_sms_code
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="codeJrnNo != null" >
                code_jrn_no,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="mblNo != null" >
                mbl_no,
            </if>
            <if test="smsCode != null" >
                sms_code,
            </if>
            <if test="token != null" >
                token,
            </if>
            <if test="codeStats != null" >
                code_stats,
            </if>
            <if test="effTime != null" >
                eff_time,
            </if>
            <if test="expTime != null" >
                exp_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="codeJrnNo != null" >
                #{codeJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="smsCode != null" >
                #{smsCode,jdbcType=VARCHAR},
            </if>
            <if test="token != null" >
                #{token,jdbcType=VARCHAR},
            </if>
            <if test="codeStats != null" >
                #{codeStats,jdbcType=CHAR},
            </if>
            <if test="effTime != null" >
                #{effTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expTime != null" >
                #{expTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.SmsCodeDO" >
        update cmm_sms_code
        <set >
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                mbl_no = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="smsCode != null" >
                sms_code = #{smsCode,jdbcType=VARCHAR},
            </if>
            <if test="token != null" >
                token = #{token,jdbcType=VARCHAR},
            </if>
            <if test="codeStats != null" >
                code_stats = #{codeStats,jdbcType=CHAR},
            </if>
            <if test="effTime != null" >
                eff_time = #{effTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expTime != null" >
                exp_time = #{expTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where code_jrn_no = #{codeJrnNo,jdbcType=VARCHAR}
    </update>
    
    <update id="updateByCondition" >
    	update cmm_sms_code
        <set >
            <if test="valueDO.type != null" >
                type = #{valueDO.type,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.mblNo != null" >
                mbl_no = #{valueDO.mblNo,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.smsCode != null" >
                sms_code = #{valueDO.smsCode,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.token != null" >
                token = #{valueDO.token,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.codeStats != null" >
                code_stats = #{valueDO.codeStats,jdbcType=CHAR},
            </if>
            <if test="valueDO.effTime != null" >
                eff_time = #{valueDO.effTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.expTime != null" >
                exp_time = #{valueDO.expTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
        	<if test="conditionDO.codeJrnNo != null" >
                code_jrn_no = #{conditionDO.codeJrnNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.type != null" >
                and type = #{conditionDO.type,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.mblNo != null" >
                and mbl_no = #{conditionDO.mblNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.smsCode != null" >
                and sms_code = #{conditionDO.smsCode,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.token != null" >
                and token = #{conditionDO.token,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.codeStats != null" >
                and code_stats = #{conditionDO.codeStats,jdbcType=CHAR}
            </if>
            <if test="conditionDO.effTime != null" >
                and eff_time = #{conditionDO.effTime,jdbcType=TIMESTAMP}
            </if>
            <if test="conditionDO.expTime != null" >
                and exp_time &gt;= #{conditionDO.expTime,jdbcType=TIMESTAMP}
            </if>
        </where>
	</update>
</mapper>