<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IQRCodeParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.QRCodeParamDO" >
        <id column="id" property="id" jdbcType="CHAR" />
        <result column="payment_code_prefix" property="paymentCodePrefix" jdbcType="VARCHAR" />
        <result column="aggregate_url" property="aggregateUrl" jdbcType="VARCHAR" />
        <result column="tkey" property="tkey" jdbcType="VARCHAR" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, payment_code_prefix, aggregate_url, tkey, opr_id, eff_date, exp_date, stats, 
        create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_qrcode_param
        where id = #{id,jdbcType=CHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_qrcode_param
        where id = #{id,jdbcType=CHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.QRCodeParamDO" >
        insert into cmm_qrcode_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="paymentCodePrefix != null" >
                payment_code_prefix,
            </if>
            <if test="aggregateUrl != null" >
                aggregate_url,
            </if>
            <if test="tkey != null" >
                tkey,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=CHAR},
            </if>
            <if test="paymentCodePrefix != null" >
                #{paymentCodePrefix,jdbcType=VARCHAR},
            </if>
            <if test="aggregateUrl != null" >
                #{aggregateUrl,jdbcType=VARCHAR},
            </if>
            <if test="tkey != null" >
                #{tkey,jdbcType=VARCHAR},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.QRCodeParamDO" >
        update cmm_qrcode_param
        <set >
            <if test="paymentCodePrefix != null" >
                payment_code_prefix = #{paymentCodePrefix,jdbcType=VARCHAR},
            </if>
            <if test="aggregateUrl != null" >
                aggregate_url = #{aggregateUrl,jdbcType=VARCHAR},
            </if>
            <if test="tkey != null" >
                tkey = #{tkey,jdbcType=VARCHAR},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>
</mapper>