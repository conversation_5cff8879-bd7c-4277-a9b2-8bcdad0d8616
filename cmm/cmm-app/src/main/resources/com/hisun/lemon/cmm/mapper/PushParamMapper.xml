<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IPushParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.PushParamDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="app_id" property="appId" jdbcType="VARCHAR" />
        <result column="app_key" property="appKey" jdbcType="VARCHAR" />
        <result column="env" property="env" jdbcType="VARCHAR" />
        <result column="master_secret" property="masterSecret" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, type, app_id, app_key, master_secret, stats, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cmm_push_param
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="getPushParamInfo" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cmm_push_param
        where id = #{id,jdbcType=VARCHAR}
        and env =  #{env,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cmm_push_param
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.PushParamDO" >
        insert into cmm_push_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="appId != null" >
                app_id,
            </if>
            <if test="appKey != null" >
                app_key,
            </if>
            <if test="masterSecret != null" >
                master_secret,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="appId != null" >
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="appKey != null" >
                #{appKey,jdbcType=VARCHAR},
            </if>
            <if test="masterSecret != null" >
                #{masterSecret,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.PushParamDO" >
        update cmm_push_param
        <set >
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="appId != null" >
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="appKey != null" >
                app_key = #{appKey,jdbcType=VARCHAR},
            </if>
            <if test="masterSecret != null" >
                master_secret = #{masterSecret,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>