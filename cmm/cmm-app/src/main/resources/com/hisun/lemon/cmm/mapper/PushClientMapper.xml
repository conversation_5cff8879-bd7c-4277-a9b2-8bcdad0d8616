<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cmm.dao.IPushClientDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cmm.entity.PushClientDO" >
        <id column="user_id" property="userId" jdbcType="VARCHAR" />
        <id column="user_type" property="userType" jdbcType="VARCHAR" />
        <result column="client_id" property="clientId" jdbcType="VARCHAR" />
        <result column="login_id" property="loginId" jdbcType="VARCHAR" />
        <result column="pre_client_id" property="preClientId" jdbcType="VARCHAR" />
        <result column="language" property="language" jdbcType="VARCHAR" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="login_flg" property="loginFlg" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        user_id, user_type, client_id, stats, create_time, modify_time, tm_smp, language, login_flg, pre_client_id, login_id
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from cmm_push_client
        where 1 = 1
            and user_id = #{userId,jdbcType=VARCHAR}
        <if test="userType != null" >
           and user_type = #{userType,jdbcType=VARCHAR}
        </if>
        and (login_flg = '0' or login_flg is null)
    </select>

    <select id="getLoginTime" resultMap="BaseResultMap" parameterType="map" >
        select
        max(modify_time) modify_time
        from cmm_push_client
        where 1 = 1
           and user_type = #{userType,jdbcType=VARCHAR}
            and client_id = #{clientId,jdbcType=VARCHAR}
    </select>

    <select id="getClientInfo" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cmm_push_client
        where 1 = 1
        and user_id = #{userId,jdbcType=VARCHAR}
        <if test="clientId != null" >
            and client_id = #{clientId,jdbcType=VARCHAR}
        </if>

    </select>




    <delete id="delete" parameterType="map" >
        delete from cmm_push_client
        where user_id = #{userId,jdbcType=VARCHAR}
          and user_type = #{userType,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cmm.entity.PushClientDO" >
        insert into cmm_push_client
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                user_id,
            </if>
            <if test="userType != null" >
                user_type,
            </if>
            <if test="clientId != null" >
                client_id,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
            <if test="language != null" >
                language,
            </if>
            <if test="loginFlg != null" >
                login_flg,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userType != null" >
                #{userType,jdbcType=VARCHAR},
            </if>
            <if test="clientId != null" >
                #{clientId,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="language != null" >
                #{language,jdbcType=VARCHAR},
            </if>
            <if test="loginFlg != null" >
                #{loginFlg,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    
    <insert id="insertOrUpdate" parameterType="com.hisun.lemon.cmm.entity.PushClientDO" >
        insert into cmm_push_client
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                user_id,
            </if>
            <if test="userType != null" >
                user_type,
            </if>
            <if test="clientId != null" >
                client_id,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
            <if test="loginFlg != null" >
                login_flg,
            </if>
            <if test="language != null" >
                language,
            </if>
            <if test="preClientId != null" >
                pre_client_id,
            </if>
            <if test="loginId != null" >
                login_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userType != null" >
                #{userType,jdbcType=VARCHAR},
            </if>
            <if test="clientId != null" >
                #{clientId,jdbcType=VARCHAR},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="loginFlg != null" >
                #{loginFlg,jdbcType=VARCHAR},
            </if>
            <if test="language != null" >
                #{language,jdbcType=VARCHAR},
            </if>
            <if test="preClientId != null" >
                #{preClientId,jdbcType=VARCHAR},
            </if>
            <if test="loginId != null" >
                #{loginId,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update 
        <if test="clientId != null" >
            client_id = #{clientId,jdbcType=VARCHAR},
        </if>
        <if test="preClientId != null" >
            pre_client_id = #{preClientId,jdbcType=VARCHAR},
        </if>
        <if test="loginId != null" >
            login_id = #{loginId,jdbcType=VARCHAR},
        </if>
        <if test="modifyTime != null" >
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        </if>
        <if test="loginFlg != null" >
            login_flg = #{loginFlg,jdbcType=VARCHAR},
        </if>
        <if test="language != null" >
            language = #{language,jdbcType=VARCHAR}
        </if>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cmm.entity.PushClientDO" >
        update cmm_push_client
        <set >
            <if test="clientId != null" >
                client_id = #{clientId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="language != null" >
                language = #{language,jdbcType=VARCHAR},
            </if>
            <if test="loginFlg != null" >
                login_flg = #{loginFlg,jdbcType=VARCHAR},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
          and user_type = #{userType,jdbcType=VARCHAR}
    </update>

    <update id="updateClientInfo" parameterType="com.hisun.lemon.cmm.entity.PushClientDO" >
        update cmm_push_client
        <set >
            <if test="preClientId != null" >
                pre_client_id = #{preClientId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="language != null" >
                language = #{language,jdbcType=VARCHAR},
            </if>
        </set>
        where login_id = #{loginId,jdbcType=VARCHAR}
    </update>
</mapper>