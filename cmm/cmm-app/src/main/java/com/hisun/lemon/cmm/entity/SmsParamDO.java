/*
 * @ClassName SmsParamDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-27 15:10:12
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SmsParamDO extends BaseDO {
    /**
     * @Fields id å‚æ•°ID
     */
    private String id;
    /**
     * @Fields dlyCntLmt çŸ­ä¿¡éªŒè¯ç æ—¥ç´¯è®¡æŽ§åˆ¶
     */
    private Integer dlyCntLmt;
    /**
     * @Fields dlySmsCntLmt çŸ­ä¿¡æ—¥ç´¯è®¡æŽ§åˆ¶
     */
    private Integer dlySmsCntLmt;
    /**
     * @Fields tmSmp æ—¶é—´æˆ³
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getDlyCntLmt() {
        return dlyCntLmt;
    }

    public void setDlyCntLmt(Integer dlyCntLmt) {
        this.dlyCntLmt = dlyCntLmt;
    }

    public Integer getDlySmsCntLmt() {
        return dlySmsCntLmt;
    }

    public void setDlySmsCntLmt(Integer dlySmsCntLmt) {
        this.dlySmsCntLmt = dlySmsCntLmt;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}