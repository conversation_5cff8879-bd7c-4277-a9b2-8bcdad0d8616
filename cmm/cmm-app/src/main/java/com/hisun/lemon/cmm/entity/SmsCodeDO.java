/*
 * @ClassName SmsCodeDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-18 11:44:11
 */
package com.hisun.lemon.cmm.entity;

import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class SmsCodeDO extends BaseDO {
    /**
     * @Fields codeJrnNo 短信验证码流水号
     */
    private String codeJrnNo;
    /**
     * @Fields type 短信验证码类型
     */
    private String type;
    /**
     * @Fields mblNo 手机号码
     */
    private String mblNo;
    /**
     * @Fields smsCode 短信验证码
     */
    private String smsCode;
    /**
     * @Fields smsCode 短信验证码token
     */
    private String token;
    /**
     * @Fields codeStats 短信验证码状态
     */
    private String codeStats;
    /**
     * @Fields effTime 生效时间
     */
    private LocalDateTime effTime;
    /**
     * @Fields expTime 失效时间
     */
    private LocalDateTime expTime;
    /**
     * @Fields 时间戳
     */
    private LocalDateTime tmSmp;

    public String getCodeJrnNo() {
        return codeJrnNo;
    }

    public void setCodeJrnNo(String codeJrnNo) {
        this.codeJrnNo = codeJrnNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getCodeStats() {
        return codeStats;
    }

    public void setCodeStats(String codeStats) {
        this.codeStats = codeStats;
    }

    public LocalDateTime getEffTime() {
        return effTime;
    }

    public void setEffTime(LocalDateTime effTime) {
        this.effTime = effTime;
    }

    public LocalDateTime getExpTime() {
        return expTime;
    }

    public void setExpTime(LocalDateTime expTime) {
        this.expTime = expTime;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}