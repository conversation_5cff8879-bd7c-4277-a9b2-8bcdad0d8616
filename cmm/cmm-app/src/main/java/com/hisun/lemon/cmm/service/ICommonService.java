package com.hisun.lemon.cmm.service;

import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.gateway.dto.AppInfoDTO;

/**
 * 
 * @Description 公共服务接口
 * <AUTHOR>
 * @date 2017年7月7日 上午11:07:48
 * @version V1.0
 */
public interface ICommonService {

    /**
     * @Description 手机号段检查
     * <AUTHOR>
     * @param segementCheckReqDTO
     * @throws LemonException
     */
    SegementCheckRspDTO phoneSegementCheck(SegementCheckReqDTO segementCheckReqDTO) throws LemonException;
    
    /**
     * @Description 手机号合法性校验
     * <AUTHOR>
     * @param segementCheckReqDTO
     * @throws LemonException
     */
    void phoneNumberCheck(SegementCheckReqDTO segementCheckReqDTO) throws LemonException;
    
    /**
     * @Description 公告信息
     * <AUTHOR>
     * @return
     * @throws LemonException
     */
    NoticeListRspDTO noticeList() throws LemonException;
    
    /**
     * @Description Banner列表
     * <AUTHOR>
     * @param
     * @return
     * @throws LemonException
     */
    BannerListRspDTO bannerList() throws LemonException;
    
    /**
     * @Description 活动列表
     * <AUTHOR>
     * @param campaignListReqDTO
     * @return
     * @throws LemonException
     */
    CampaignListRspDTO campaignList(CampaignListReqDTO campaignListReqDTO) throws LemonException;
    
    /**
     * @Description 消息推送
     * <AUTHOR>
     * @param messageSendReqDTO
     * @throws LemonException
     */
    void messageSend(MessageSendReqDTO messageSendReqDTO) throws LemonException;
    
    /**
     * @Description 消息列表
     * <AUTHOR>
     * @param messageListReqDTO
     * @return
     * @throws LemonException
     */
    MessageListRspDTO messageList(MessageListReqDTO messageListReqDTO) throws LemonException;
    
    /**
     * @Description 消息确认
     * <AUTHOR>
     * @param acknowledgeReqDTO
     * @throws LemonException
     */
    void messageAcknowledge(MessageAcknowledgeReqDTO acknowledgeReqDTO) throws LemonException;
    
    /**
     * @Description 消息推送ClientID设置
     * <AUTHOR>
     * @param messageClientReqDTO
     * @param flg 0:预设置, 1:设置
     *
     * @throws LemonException
     */
    void messageClient(MessageClientReqDTO messageClientReqDTO, String flg) throws LemonException;
    
    /**
     * @Description 终端信息登记
     * <AUTHOR>
     * @param appInfoDTO
     * @throws LemonException
     */
    void logAppInfo(String channel, String loginName, AppInfoDTO appInfoDTO) throws LemonException;
    
    /**
     * @Description 公共加解密
     * <AUTHOR>
     * @param data
     * @return
     * @throws LemonException
     */
    CommonEncryptRspDTO encrypt(CommonEncryptReqDTO encryptReqDTO) throws LemonException;
    
    /**
     * @Description 登陆信息查询
     * <AUTHOR>
     * @param appInfoReqDTO
     * @return
     * @throws LemonException
     */
    AppInfoRspDTO logInfo(AppInfoReqDTO appInfoReqDTO) throws LemonException;
    
    /**
     * @Description 系统密钥查询
     * <AUTHOR>
     * @param systemKeyReqDTO
     * @return
     * @throws LemonException
     */
    SystemKeyRspDTO key(SystemKeyReqDTO systemKeyReqDTO) throws LemonException;

    /**
     * @Description app信息查询
     * <AUTHOR> @param
     * @return
     * @throws LemonException
     */
    CmmAppvnoinfRspDTO getAppvnoinfo(String channel, String version) throws LemonException;

    /**
     * @Description 检查用户登记设备与当前设备是否一致
     * @pabram messageClientSwitchReqDTO
     * @return
     */
    Boolean messageClientSwitch(MessageClientSwitchReqDTO messageClientSwitchReqDTO);
}
