/*
 * @ClassName QRCodePaymentDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-20 18:51:37
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class QRCodePaymentDO extends BaseDO {
    /**
     * @Fields codeJrnNo 付款码流水号
     */
    private String codeJrnNo;
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields codeInfo 付款码信息
     */
    private String codeInfo;
    /**
     * @Fields type 付款码类型 barCode:条码 QRCode:二维码
     */
    private String type;
    /**
     * @Fields codeStats 付款码状态 0:初始化 1:已验证 2:已失效
     */
    private String codeStats;
    /**
     * @Fields effTime 生效时间
     */
    private LocalDateTime effTime;
    /**
     * @Fields expTime 失效时间
     */
    private LocalDateTime expTime;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getCodeJrnNo() {
        return codeJrnNo;
    }

    public void setCodeJrnNo(String codeJrnNo) {
        this.codeJrnNo = codeJrnNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCodeInfo() {
        return codeInfo;
    }

    public void setCodeInfo(String codeInfo) {
        this.codeInfo = codeInfo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCodeStats() {
        return codeStats;
    }

    public void setCodeStats(String codeStats) {
        this.codeStats = codeStats;
    }

    public LocalDateTime getEffTime() {
        return effTime;
    }

    public void setEffTime(LocalDateTime effTime) {
        this.effTime = effTime;
    }

    public LocalDateTime getExpTime() {
        return expTime;
    }

    public void setExpTime(LocalDateTime expTime) {
        this.expTime = expTime;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}