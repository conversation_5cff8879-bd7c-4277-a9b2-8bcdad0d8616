/*
 * @ClassName ISmsTemplateDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-18 14:04:36
 */
package com.hisun.lemon.cmm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.cmm.entity.SmsTemplateDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface ISmsTemplateDao extends BaseDao<SmsTemplateDO> {
    
    List<SmsTemplateDO> getListByCondition(SmsTemplateDO smsTemplateDO);
}