package com.hisun.lemon.cmm.service.impl;

//import com.hisun.lemon.cmm.rsp.TokenRsp;
//import com.hisun.lemon.cmm.api.wechat.WeChatPayApi;
import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.dao.*;
import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.cmm.entity.*;
//import com.hisun.lemon.cmm.req.TokenReq;
import com.hisun.lemon.cmm.service.IPayMentService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.client.EbankpayClient;
import com.hisun.lemon.cpi.dto.WeChatTokenReq;
import com.hisun.lemon.cpi.dto.WeChatTokenRsp;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Transactional
@Service
public class PayMentServiceImpl extends BaseService implements IPayMentService {
    private static final Logger logger = LoggerFactory.getLogger(PayMentServiceImpl.class);

    //@Resource
    //WeChatPayApi weChatPayApi;
    @Resource
    ICmmPayuserInfoDao iCmmPayuserInfoDao;

    @Resource
    EbankpayClient ebankpayClient;

    @Override
    public void weChatGrantInfo(String code, String state) throws LemonException {
        //TokenReq tokenReq = new TokenReq();
        //tokenReq.setCode(code);
        //TokenRsp tokenRsp = new TokenRsp();
        GenericDTO<WeChatTokenReq> genericDTO = new GenericDTO<>();
        WeChatTokenReq weChatTokenReq = new WeChatTokenReq();
        weChatTokenReq.setCode(code);
        genericDTO.setBody(weChatTokenReq);
        GenericRspDTO<WeChatTokenRsp>  weChatTokenRspGenericRspDTO = ebankpayClient.applyToken(genericDTO);
        if(JudgeUtils.isNotSuccess(weChatTokenRspGenericRspDTO.getMsgCd())){
            logger.info("CPIGRANT-ERROR" + code);
            return;
        }
        WeChatTokenRsp weChatTokenRsp = weChatTokenRspGenericRspDTO.getBody();
        //tokenRsp = weChatPayApi.tokenApply(tokenReq);
        CmmPayuserInfoDO cmmPayuserInfoDO = new CmmPayuserInfoDO();
        cmmPayuserInfoDO.setOpenId(weChatTokenRsp.getOpenid());
        cmmPayuserInfoDO.setParamJrnNo(state);
        try {
            int num = iCmmPayuserInfoDao.insertOrUpdate(cmmPayuserInfoDO);
            if(num != 1){
                logger.info("WeChatGrantInfo order error ! ");
            }
        }catch (Exception e){
            e.printStackTrace();
        }


    }

    @Override
    public GenericRspDTO<PayWeChatInfoRspDTO> getWeChatGrantInfo(String state) throws LemonException {
        CmmPayuserInfoDO cmmPayuserInfoDO = iCmmPayuserInfoDao.get(state);
        if(cmmPayuserInfoDO == null){
            logger.info("WeChatGrantInfo is not exist!");
            LemonException.throwBusinessException(MsgCdEnum.WECHATINFO_QUERY_FAIL.getMsgCd());
        }
        PayWeChatInfoRspDTO payWeChatInfoRspDTO = new PayWeChatInfoRspDTO();
        BeanUtils.copyProperties(payWeChatInfoRspDTO, cmmPayuserInfoDO);
        GenericRspDTO<PayWeChatInfoRspDTO> genericRspDTO = GenericRspDTO.newSuccessInstance(payWeChatInfoRspDTO);
        return genericRspDTO;
    }
}
