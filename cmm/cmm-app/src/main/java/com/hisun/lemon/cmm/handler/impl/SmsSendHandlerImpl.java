package com.hisun.lemon.cmm.handler.impl;

import java.io.IOException;

import javax.annotation.Resource;

import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cmm.bo.SmsBo;
import com.hisun.lemon.cmm.constants.Constants;
import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.handler.ISmsSendHandler;
import com.hisun.lemon.cmm.utils.HttpClientUtil;
import com.hisun.lemon.common.exception.LemonException;

@Component
public class SmsSendHandlerImpl implements ISmsSendHandler {
    
    @Resource
    private Environment env;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void smsSend(String mblNo, String content) throws LemonException{
        try {
            // 下发短信验证码
            SmsBo smsBo = new SmsBo(Constants.SMS_SEND_SRCID, mblNo, content);
            String smsjson = objectMapper.writeValueAsString(smsBo);
            HttpClientUtil.doPostJson(env.getProperty("cmm.sms.url"), Base64.encodeBase64String(smsjson.getBytes()));
        } catch (JsonProcessingException e) {
            LemonException.throwBusinessException(MsgCdEnum.SMS_SEND_FAIL.getMsgCd());
        } catch (IOException e) {
            LemonException.throwBusinessException(MsgCdEnum.SMS_SEND_FAIL.getMsgCd());
        }
    }
}
