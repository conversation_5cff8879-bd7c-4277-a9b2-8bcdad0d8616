package com.hisun.lemon.cmm.service;

import com.hisun.lemon.cmm.dto.SmsCheckReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeRspDTO;
import com.hisun.lemon.cmm.dto.SmsSendReqDTO;
import com.hisun.lemon.common.exception.LemonException;

/**
 * 
 * @Description 短信服务接口
 * <AUTHOR>
 * @date 2017年7月7日 上午11:07:48
 * @version V1.0
 */
public interface ISmsService {

    /**
     * @Description 短信验证码下发
     * <AUTHOR>
     * @param smsCodeReqDTO
     * @return
     * @throws LemonException
     */
    SmsCodeRspDTO smsCodeSend(SmsCodeReqDTO smsCodeReqDTO) throws LemonException;

    /**
     * @Description 短信验证码校验
     * <AUTHOR>
     * @param smsCheckReqDTO
     * @throws LemonException
     */
    void smsCodeCheck(SmsCheckReqDTO smsCheckReqDTO) throws LemonException;

    /**
     * @Description 短信验下发
     * <AUTHOR>
     * @param smsCheckReqDTO
     * @throws LemonException
     */
    void smsSend(SmsSendReqDTO smsReqDTO) throws LemonException;

}
