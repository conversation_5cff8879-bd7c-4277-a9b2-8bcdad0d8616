package com.hisun.lemon.cmm.controller;

import javax.annotation.Resource;
import javax.validation.constraints.Pattern;

import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.cmm.service.GetNewsService;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.service.ICommonService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;

/**
 * 公共管理
 * 
 * @GetMapping @PostMapping @PutMapping @DeleteMapping @PatchMapping
 * <AUTHOR>
 * @date 2017年7月5日
 * @time 上午10:46:30
 *
 */
@RestController
@RequestMapping("/cmm")
@Api(tags = "CmmController", description = "公共服务")
public class CmmController extends BaseController{

    /**
     * 公共服务
     */
    @Resource
    private ICommonService commonService;

    @Resource
    private GetNewsService getNewsService;

    @ApiOperation(value = "手机号段检查", notes = "手机号段检查")
    @ApiResponse(code = 200, message = "手机号段检查结果")
    @PostMapping("/segement/check")
    public GenericRspDTO<SegementCheckRspDTO> phoneSegementCheck(@Validated @RequestBody GenericDTO<SegementCheckReqDTO> reqDTO) {
        GenericRspDTO<SegementCheckRspDTO> rspDTO = new GenericRspDTO<SegementCheckRspDTO>();
        SegementCheckReqDTO segementCheckReqDTO = reqDTO.getBody();
        SegementCheckRspDTO segementCheckRspDTO = null;
        try {
      //      segementCheckRspDTO = commonService.phoneSegementCheck(segementCheckReqDTO);
            rspDTO.setBody(segementCheckRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value = "手机号合法性校验", notes = "手机号合法性校验")
    @ApiResponse(code = 200, message = "手机号合法性校验结果")
    @PostMapping("/phonenumber/check")
    public GenericRspDTO<NoBody> phoneNumberCheck(@Validated @RequestBody GenericDTO<SegementCheckReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        SegementCheckReqDTO segementCheckReqDTO = reqDTO.getBody();
        try {
            commonService.phoneNumberCheck(segementCheckReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "公告信息查询", notes = "公告信息查询")
    @ApiResponse(code = 200, message = "查询公告信息结果")
    @PostMapping("/notice/list")
    public GenericRspDTO<NoticeListRspDTO> noticeList() {
        GenericRspDTO<NoticeListRspDTO> rspDTO = new GenericRspDTO<NoticeListRspDTO>();
        NoticeListRspDTO noticeListRspDTO = null;
        try {
            noticeListRspDTO = commonService.noticeList();
            rspDTO.setBody(noticeListRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "Banner列表查询", notes = "Banner列表查询")
    @ApiResponse(code = 200, message = "查询Banner列表结果")
    @PostMapping("/banner/list")
    public GenericRspDTO<BannerListRspDTO> bannerList() {
        GenericRspDTO<BannerListRspDTO> rspDTO = new GenericRspDTO<BannerListRspDTO>();
        BannerListRspDTO bannerListRspDTO = null;
        try {
            bannerListRspDTO = commonService.bannerList();
            rspDTO.setBody(bannerListRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "消息推送", notes = "消息推送")
    @ApiResponse(code = 200, message = "消息推送结果")
    @PostMapping("/message/send")
    public GenericRspDTO<NoBody> messageSend(@Validated @RequestBody GenericDTO<MessageSendReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        MessageSendReqDTO messageSendReqDTO = reqDTO.getBody();
        try {
            commonService.messageSend(messageSendReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "消息列表查询", notes = "消息列表查询")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "查询消息列表结果")
    @PostMapping("/message/list")
    public GenericRspDTO<MessageListRspDTO> messageList(@Validated @RequestBody GenericDTO<MessageListReqDTO> reqDTO) {
        GenericRspDTO<MessageListRspDTO> rspDTO = new GenericRspDTO<MessageListRspDTO>();
        MessageListReqDTO messageListReqDTO = reqDTO.getBody();
        MessageListRspDTO messageListRspDTO = null;
        try {
            messageListRspDTO = commonService.messageList(messageListReqDTO);
            rspDTO.setBody(messageListRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value = "活动列表查询", notes = "活动列表查询")
    @ApiResponse(code = 200, message = "查询活动列表结果")
    @PostMapping("/campaign/list")
    public GenericRspDTO<CampaignListRspDTO> campaignList(@Validated @RequestBody GenericDTO<CampaignListReqDTO> reqDTO) {
        GenericRspDTO<CampaignListRspDTO> rspDTO = new GenericRspDTO<CampaignListRspDTO>();
        CampaignListReqDTO campaignListReqDTO = reqDTO.getBody();
        CampaignListRspDTO campaignListRspDTO = null;
        try {
            campaignListRspDTO = commonService.campaignList(campaignListReqDTO);
            rspDTO.setBody(campaignListRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "消息确认", notes = "消息确认")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "消息确认结果")
    @PostMapping("/message/acknowledge")
    public GenericRspDTO<NoBody> messageAcknowledge(
            @Validated @RequestBody GenericDTO<MessageAcknowledgeReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        MessageAcknowledgeReqDTO acknowledgeReqDTO = reqDTO.getBody();
        try {
            commonService.messageAcknowledge(acknowledgeReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value = "消息推送ID设置", notes = "消息推送ID设置")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header"),
        @ApiImplicitParam(name = "x-lemon-loginnm", value = "用户登录名", paramType = "header"),
    })
    @ApiResponse(code = 200, message = "设置消息推送ID结果")
    @PostMapping("/message/push/client")
    public GenericRspDTO<NoBody> messageClient(@Validated @RequestBody GenericDTO<MessageClientReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        String loginId = LemonUtils.getLoginName();
        MessageClientReqDTO messageClientReqDTO = reqDTO.getBody();
        messageClientReqDTO.setLoginId(loginId);
        try {
            commonService.messageClient(messageClientReqDTO, "1");
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "公共加解密", notes = "公共加解密")
    @ApiResponse(code = 200, message = "公共加解密结果")
    @PostMapping("/encrypt")
    public GenericRspDTO<CommonEncryptRspDTO> encrypt(@Validated @RequestBody GenericDTO<CommonEncryptReqDTO> reqDTO) {
        GenericRspDTO<CommonEncryptRspDTO> rspDTO = new GenericRspDTO<>();
        CommonEncryptReqDTO encryptReqDTO = reqDTO.getBody();
        CommonEncryptRspDTO encryptRspDTO = null;
        try {
            encryptRspDTO = commonService.encrypt(encryptReqDTO);
            rspDTO.setBody(encryptRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value = "登陆信息查询", notes = "登陆信息查询")
    @ApiResponse(code = 200, message = "登陆信息查询结果")
    @PostMapping("/loginfo")
    public GenericRspDTO<AppInfoRspDTO> logInfo(@Validated @RequestBody GenericDTO<AppInfoReqDTO> reqDTO) {
        GenericRspDTO<AppInfoRspDTO> rspDTO = new GenericRspDTO<>();
        AppInfoReqDTO infoReqDTO = reqDTO.getBody();
        AppInfoRspDTO infoRspDTO = null;
        try {
            infoRspDTO = commonService.logInfo(infoReqDTO);
            rspDTO.setBody(infoRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value = "系统密钥信息查询", notes = "系统密钥信息查询")
    @ApiResponse(code = 200, message = "查询系统密钥结果")
    @PostMapping("/system/key")
    public GenericRspDTO<SystemKeyRspDTO> key(@Validated @RequestBody GenericDTO<SystemKeyReqDTO> reqDTO) {
        GenericRspDTO<SystemKeyRspDTO> rspDTO = new GenericRspDTO<SystemKeyRspDTO>();
        SystemKeyReqDTO systemKeyReqDTO = reqDTO.getBody();
        SystemKeyRspDTO systemKeyRspDTO = null;
        try {
            systemKeyRspDTO = commonService.key(systemKeyReqDTO);
            rspDTO.setBody(systemKeyRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "app信息查询", notes = "app信息查询")
    @ApiResponse(code = 200, message = "app信息查询结果")
    @PostMapping("/appvnoinfo")
    public GenericRspDTO<CmmAppvnoinfRspDTO> getAppInfo(@Validated @RequestBody GenericDTO<CmmAppvnoinfReqDTO> reqDTO) {
        CmmAppvnoinfReqDTO cmmAppvnoinfReqDTO = reqDTO.getBody();
        String channel = cmmAppvnoinfReqDTO.getChannel();
        String appVersion = cmmAppvnoinfReqDTO.getAppVersion();
        CmmAppvnoinfRspDTO cmmAppvnoinfDTO = commonService.getAppvnoinfo(channel, appVersion);
        return GenericRspDTO.newSuccessInstance(cmmAppvnoinfDTO);
    }

    @ApiOperation(value = "消息推送ID预登记", notes = "消息推送ID预登记")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header"),
            @ApiImplicitParam(name = "x-lemon-loginnm", value = "用户登录名", paramType = "header"),
    })
    @ApiResponse(code = 200, message = "设置消息推送ID结果")
    @PostMapping("/message/push/clientpre")
    public GenericRspDTO<NoBody> messageClientPre(@Validated @RequestBody GenericDTO<MessageClientReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        MessageClientReqDTO messageClientReqDTO = reqDTO.getBody();
        try {
            commonService.messageClient(messageClientReqDTO, "0");
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "查询用户是否切换设备", notes = "查询用户是否切换设备")
    @ApiResponse(code = 200, message = "返回设备检查结果 ")
    @PostMapping("/message/push/client/switch")
    public GenericRspDTO<Boolean> messageClientSwitch(@Validated @RequestBody GenericDTO<MessageClientSwitchReqDTO> reqDTO) {
        MessageClientSwitchReqDTO messageClientSwitchReqDTO = reqDTO.getBody();
        Boolean switchC = commonService.messageClientSwitch(messageClientSwitchReqDTO);
        return GenericRspDTO.newSuccessInstance(switchC);
    }

    @ApiOperation(value = "获取金融新闻", notes = "获取金融新闻")
    @ApiResponse(code = 200, message = "返回金融新闻 ")
    @PostMapping("/getNews")
    public GenericRspDTO<NewsRspDTO> getNews(@Validated @RequestParam(value = "fromDate", required = false) String fromDate,
                                             @Validated @RequestParam(value = "toDate", required = false) String toDate) {
        NewsRspDTO newsRspDTO = getNewsService.getNews(fromDate, toDate);
        return GenericRspDTO.newSuccessInstance(newsRspDTO);
    }
}
