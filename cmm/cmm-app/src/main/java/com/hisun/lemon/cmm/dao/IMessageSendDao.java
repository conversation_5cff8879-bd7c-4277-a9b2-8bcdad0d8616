/*
 * @ClassName IMessageSendDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 16:50:03
 */
package com.hisun.lemon.cmm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.cmm.entity.MessageSendDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface IMessageSendDao extends BaseDao<MessageSendDO> {
    
    int countByCondition(MessageSendDO messageSendDO);
    
    List<MessageSendDO> getListByCondition(MessageSendDO messageSendDO);
    
    int updateByCondition(@Param("valueDO")MessageSendDO updateValueMessageSendDO, @Param("conditionDO")MessageSendDO updateConditionMessageSendDO);
}