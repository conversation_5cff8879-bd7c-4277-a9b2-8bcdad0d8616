/*
 * @ClassName CampaignPublicDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 18:23:08
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class CampaignPublicDO extends BaseDO {
    /**
     * @Fields id 活动ID
     */
    private String id;
    /**
     * @Fields campaignTitleKh 柬语活动标题
     */
    private String campaignTitleKh;
    /**
     * @Fields campaignTitleCn 中文活动标题
     */
    private String campaignTitleCn;
    /**
     * @Fields campaignTitleEn 英文活动标题
     */
    private String campaignTitleEn;
    /**
     * @Fields campaignContentKh 柬语公告内容
     */
    private String campaignContentKh;
    /**
     * @Fields campaignContentCn 中文公告内容
     */
    private String campaignContentCn;
    /**
     * @Fields campaignContentEn 英文公告内容
     */
    private String campaignContentEn;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    /**
     * @Fields channel 渠道
     */
    public String channel;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCampaignTitleKh() {
        return campaignTitleKh;
    }

    public void setCampaignTitleKh(String campaignTitleKh) {
        this.campaignTitleKh = campaignTitleKh;
    }

    public String getCampaignTitleCn() {
        return campaignTitleCn;
    }

    public void setCampaignTitleCn(String campaignTitleCn) {
        this.campaignTitleCn = campaignTitleCn;
    }

    public String getCampaignTitleEn() {
        return campaignTitleEn;
    }

    public void setCampaignTitleEn(String campaignTitleEn) {
        this.campaignTitleEn = campaignTitleEn;
    }

    public String getCampaignContentKh() {
        return campaignContentKh;
    }

    public void setCampaignContentKh(String campaignContentKh) {
        this.campaignContentKh = campaignContentKh;
    }

    public String getCampaignContentCn() {
        return campaignContentCn;
    }

    public void setCampaignContentCn(String campaignContentCn) {
        this.campaignContentCn = campaignContentCn;
    }

    public String getCampaignContentEn() {
        return campaignContentEn;
    }

    public void setCampaignContentEn(String campaignContentEn) {
        this.campaignContentEn = campaignContentEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}