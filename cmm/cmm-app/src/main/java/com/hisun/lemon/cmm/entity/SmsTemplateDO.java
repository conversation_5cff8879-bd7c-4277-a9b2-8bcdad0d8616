/*
 * @ClassName SmsTemplateDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-18 14:04:36
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class SmsTemplateDO extends BaseDO {
    /**
     * @Fields id  短信模版ID
     */
    private String id;
    /**
     * @Fields type 短信类型
     */
    private String type;
    /**
     * @Fields lvl 短信级别
     */
    private String lvl;
    /**
     * @Fields replaceField 替换变量
     */
    private String replaceField;
    /**
     * @Fields templateContentKh 柬文短信模版
     */
    private String templateContentKh;
    /**
     * @Fields templateContentCn 中文短信模版
     */
    private String templateContentCn;
    /**
     * @Fields templateContentEn 英文短信模版
     */
    private String templateContentEn;
    /**
     * @Fields stats 模版状态
     */
    private String stats;
    /**
     * @Fields effDate 模版生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 模版失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLvl() {
        return lvl;
    }

    public void setLvl(String lvl) {
        this.lvl = lvl;
    }

    public String getReplaceField() {
        return replaceField;
    }

    public void setReplaceField(String replaceField) {
        this.replaceField = replaceField;
    }

    public String getTemplateContentKh() {
        return templateContentKh;
    }

    public void setTemplateContentKh(String templateContentKh) {
        this.templateContentKh = templateContentKh;
    }

    public String getTemplateContentCn() {
        return templateContentCn;
    }

    public void setTemplateContentCn(String templateContentCn) {
        this.templateContentCn = templateContentCn;
    }

    public String getTemplateContentEn() {
        return templateContentEn;
    }

    public void setTemplateContentEn(String templateContentEn) {
        this.templateContentEn = templateContentEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}