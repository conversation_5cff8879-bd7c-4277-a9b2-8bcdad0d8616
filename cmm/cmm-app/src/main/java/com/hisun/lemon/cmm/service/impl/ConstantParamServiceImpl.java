package com.hisun.lemon.cmm.service.impl;

import com.hisun.lemon.cmm.constants.Constants;
import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.dao.IConstantParamDao;
import com.hisun.lemon.cmm.entity.ConstantParamDo;
import com.hisun.lemon.cmm.service.IConstantParamService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.cache.redis.RedisCacheable;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.PageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/31
 */
@Service
@Transactional
public class ConstantParamServiceImpl extends BaseService implements IConstantParamService {

    private static final Logger logger = LoggerFactory.getLogger(IConstantParamService.class);

    @Resource
    private IConstantParamDao constantParamDao;

    @Override
    public void addConstantParam(ConstantParamDo constantParamDo) {
        constantParamDo.setCreateTime(LocalDateTime.now());
        constantParamDo.setModifyTime(LocalDateTime.now());
        int result = constantParamDao.insertParam(constantParamDo);
        if (result <= 0) {
            logger.error("Insert Constant Param Failure");
            LemonException.throwBusinessException(MsgCdEnum.INSERT_CONSTANT_PARAM_FAILURE.getMsgCd());
        }
    }

    @Override
    @CachePut(cacheNames = "${lemon.cache.cacheName.prefix}.constantParam", cacheResolver = "redisCacheResolver",
            key = "'CACHE.' + #root.targetClass + 'queryConstantParam' + #p0.parmNm")
    public void updateConstantParam(ConstantParamDo constantParamDo) {
        constantParamDo.setModifyTime(LocalDateTime.now());
        int result = constantParamDao.updateParam(constantParamDo);
        if (result <= 0) {
            logger.error("Update Constant Param Failure");
            LemonException.throwBusinessException(MsgCdEnum.UPDATE_CONSTANT_PARAM_FAILURE.getMsgCd());
        }
    }

    @Override
    @CacheEvict(value = "${lemon.cache.cacheName.prefix}.constantParam", cacheResolver = "redisCacheResolver",
            key = "'CACHE.' + #root.targetClass + 'queryConstantParam' + #parmNm")
    public void deleteConstantParam(String parmNm) {
        int result = constantParamDao.deleteParam(parmNm);
        if (result <= 0) {
            logger.error("Delete Constant Param Failure");
            LemonException.throwBusinessException(MsgCdEnum.DELETE_CONSTANT_PARAM_FAILURE.getMsgCd());
        }
    }

    @Override
    @Transactional(readOnly = true)
    @RedisCacheable(cacheNames = "${lemon.cache.cacheName.prefix}.constantParam",
            key = "'CACHE.' + #root.targetClass + 'queryConstantParam' + #parmNm")
    public ConstantParamDo queryConstantParam(String parmNm) {
        ConstantParamDo constantParamDo = constantParamDao.selectParam(parmNm);
        if (JudgeUtils.isNull(constantParamDo)) {
            logger.error("Select Constant Param Failure");
            LemonException.throwBusinessException(MsgCdEnum.SELECT_CONSTANT_PARAM_FAILURE.getMsgCd());
        }
        if (JudgeUtils.equals(constantParamDo.getEffFlg(), Constants.PARAM_EFF_FLG_EXPIRED)) {
            logger.error("Constants param is expired");
            LemonException.throwBusinessException(MsgCdEnum.CONSTANT_PARAM_EXPIRED.getMsgCd());
        }

        return constantParamDo;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ConstantParamDo> queryAllConstantParams(int pageNum, int pageSize) {
        List<ConstantParamDo> list = PageUtils.pageQuery(pageNum, pageSize, () -> constantParamDao.selectAllParams());
        if (list.isEmpty()) {
            logger.error("Select All Constant Params Failure");
            LemonException.throwBusinessException(MsgCdEnum.SELECT_ALL_CONSTANT_PARAMS_FAILURE.getMsgCd());
        }
        return list;
    }

    @Override
    public List<ConstantParamDo> queryConstantParamGroup(String parmNm) {
        return constantParamDao.selectParamGroup(parmNm);
    }

    @Scheduled(fixedRateString = "${cmm.constantParam.clearExpired}", initialDelay = 3600000)
    @CacheEvict(cacheNames = "${lemon.cache.cacheName.prefix}.constantParam", cacheResolver = "redisCacheResolver",
            allEntries = true)
    public void clearExpiredConstantParam() {
    }
}
