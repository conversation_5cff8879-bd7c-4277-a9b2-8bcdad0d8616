/*
 * @ClassName IBannerPulblicDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 12:39:12
 */
package com.hisun.lemon.cmm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.cmm.entity.BannerPulblicDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface IBannerPulblicDao extends BaseDao<BannerPulblicDO> {
    
    List<BannerPulblicDO> getListByCondition(BannerPulblicDO bannerPulblicDO);
}