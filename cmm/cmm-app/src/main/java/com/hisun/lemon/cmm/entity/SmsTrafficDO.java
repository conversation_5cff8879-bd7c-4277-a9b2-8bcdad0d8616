/*
 * @ClassName SmsTrafficDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-27 15:09:42
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class SmsTrafficDO extends BaseDO {
    /**
     * @Fields mblNo æ‰‹æœºå·ç 
     */
    private String mblNo;
    /**
     * @Fields tradeDate äº¤æ˜“æ—¥æœŸ
     */
    private LocalDate tradeDate;
    /**
     * @Fields userId å†…éƒ¨ç”¨æˆ·å·
     */
    private String userId;
    /**
     * @Fields dlyCnt çŸ­ä¿¡éªŒè¯ç æ—¥ç´¯è®¡æ¬¡æ•°
     */
    private Integer dlyCnt;
    /**
     * @Fields dlySmsCnt çŸ­ä¿¡æ—¥ç´¯è®¡æ—¥æ•°
     */
    private Integer dlySmsCnt;
    /**
     * @Fields tmSmp æ—¶é—´æˆ³
     */
    private LocalDateTime tmSmp;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getDlyCnt() {
        return dlyCnt;
    }

    public void setDlyCnt(Integer dlyCnt) {
        this.dlyCnt = dlyCnt;
    }

    public Integer getDlySmsCnt() {
        return dlySmsCnt;
    }

    public void setDlySmsCnt(Integer dlySmsCnt) {
        this.dlySmsCnt = dlySmsCnt;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}