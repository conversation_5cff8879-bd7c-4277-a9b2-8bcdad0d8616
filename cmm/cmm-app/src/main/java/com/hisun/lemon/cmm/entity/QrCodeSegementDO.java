/*
 * @ClassName QrCodeSegementDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-21 15:07:44
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class QrCodeSegementDO extends BaseDO {
    /**
     * @Fields prefixNumber 号段前缀
     */
    private String prefixNumber;
    /**
     * @Fields payType 付款码类型
     */
    private String payType;
    /**
     * @Fields stats 号段状态 0:无效 1:有效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getPrefixNumber() {
        return prefixNumber;
    }

    public void setPrefixNumber(String prefixNumber) {
        this.prefixNumber = prefixNumber;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}