package com.hisun.lemon.cmm.dao;

import com.hisun.lemon.cmm.entity.ConstantParamDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/31
 */
@Mapper
public interface IConstantParamDao {

    /**
     * 增
     *
     * @param constantParamDo 参数对象
     * @return
     */
    int insertParam(ConstantParamDo constantParamDo);

    /**
     * 改
     *
     * @param constantParamDo 参数对象
     * @return
     */
    int updateParam(ConstantParamDo constantParamDo);

    /**
     * 删
     *
     * @param parmNm 参数名
     * @return
     */
    int deleteParam(String parmNm);

    /**
     * 查
     *
     * @param parmNm 参数名
     * @return
     */
    ConstantParamDo selectParam(String parmNm);

    /**
     * 查列表
     *
     * @return
     */
    List<ConstantParamDo> selectAllParams();

    /**
     * 查询常量组
     *
     * @return
     */
    List<ConstantParamDo> selectParamGroup(String parmNm);
}
