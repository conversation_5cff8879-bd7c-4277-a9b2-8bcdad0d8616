package com.hisun.lemon.cmm.controller;

import com.hisun.lemon.cmm.rsp.ImageRspDTO;
import com.hisun.lemon.cmm.rsp.ImagesRspDTO;
import com.hisun.lemon.cmm.service.ImgUploadService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericRspDTO;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文件上传Controller
 *
 * <AUTHOR>
 * @GetMapping @PostMapping @PutMapping @DeleteMapping @PatchMapping
 * @date 2017年7月5日
 * @time 上午10:46:30
 */
@RestController
@RequestMapping("/cmm/upload")
@Api(tags = "uploadController", description = "文件上传公共服务")
public class uploadController extends BaseController {

    @Autowired
    private Environment env;

    @Resource
    private ImgUploadService imgUploadService;

    /**
     * @Desc: 上传单张图片接口
     * <AUTHOR>
     * @date 2018/1/19 17:36
     * @version V1.0
     */
    @ApiOperation(value = "上传单张图片接口", notes = "上传单张图片接口")
    @ApiResponse(code = 200, message = "上传单张图片接口")
    @PostMapping("/imageUpload")
    @ResponseBody
    public GenericRspDTO<ImageRspDTO> imageUpload(@RequestParam(value = "file") MultipartFile file) {
        return imgUploadService.imageUpload(file);
    }

    /**
     * @Desc: 上传单张图片接口
     * <AUTHOR>
     * @date 2018/1/19 17:36
     * @version V1.0
     */
    @ApiOperation(value = "上传多张图片接口", notes = "上传多张图片接口")
    @ApiResponse(code = 200, message = "上传多张图片接口")
    @PostMapping("/imagesUpload")
    @ResponseBody
    public GenericRspDTO<ImagesRspDTO> imagesUpload(@RequestParam(value = "files") List<MultipartFile> file) {
        return imgUploadService.imagesUpload(file);
    }

}
