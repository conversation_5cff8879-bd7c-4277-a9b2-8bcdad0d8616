package com.hisun.lemon.cmm.service.impl;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.hisun.lemon.urm.constants.URMMessageCode;
import org.apache.tomcat.util.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cmm.bo.SmsBo;
import com.hisun.lemon.cmm.constants.Constants;
import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.dao.IPhoneSegementDao;
import com.hisun.lemon.cmm.dao.ISmsCodeDao;
import com.hisun.lemon.cmm.dao.ISmsParamDao;
import com.hisun.lemon.cmm.dao.ISmsSendDao;
import com.hisun.lemon.cmm.dao.ISmsTemplateDao;
import com.hisun.lemon.cmm.dao.ISmsTrafficDao;
import com.hisun.lemon.cmm.dto.SmsCheckReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeRspDTO;
import com.hisun.lemon.cmm.dto.SmsSendReqDTO;
import com.hisun.lemon.cmm.entity.PhoneSegementDO;
import com.hisun.lemon.cmm.entity.SmsCodeDO;
import com.hisun.lemon.cmm.entity.SmsParamDO;
import com.hisun.lemon.cmm.entity.SmsSendDO;
import com.hisun.lemon.cmm.entity.SmsTemplateDO;
import com.hisun.lemon.cmm.entity.SmsTrafficDO;
import com.hisun.lemon.cmm.service.ISmsService;
import com.hisun.lemon.cmm.utils.HttpClientUtil;
import com.hisun.lemon.cmm.utils.SmsTemplateResolveUtil;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.RandomUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.jcommon.phonenumber.PhoneNumberUtils;

@Transactional
@Service
public class SmsServiceImpl extends BaseService implements ISmsService {
    
    private static final Logger logger = LoggerFactory.getLogger(SmsServiceImpl.class);
    
    @Resource
    private Environment env;
    @Resource
    private ISmsCodeDao smsCodeDao;
    @Resource
    private ISmsTemplateDao smsTemplateDao;
    @Resource
    private ISmsSendDao smsSendDao;
    @Resource
    private ISmsParamDao smsParamDao;
    @Resource
    private ISmsTrafficDao smsTrafficDao;
    @Resource
    private IPhoneSegementDao phoneSegementDao;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public SmsCodeRspDTO smsCodeSend(SmsCodeReqDTO smsCodeReqDTO) throws LemonException {
        SmsCodeRspDTO smsCodeRspDTO = new SmsCodeRspDTO();
        String mblNo = smsCodeReqDTO.getMblNo();
        String smsCodeType = smsCodeReqDTO.getSmsCodeType();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        LocalDateTime tradeTimeAfter3Minute = tradeTime.plusMinutes(3);
        String language = LemonUtils.getLocale().getLanguage();
        // 查询短信验证码流量控制日累积(考虑redis存储)
        SmsParamDO qryOutSmsParamDO = smsParamDao.get(Constants.SMS_PARAM_ID);
        if (JudgeUtils.isNull(qryOutSmsParamDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SMSPARAM_NOT_EXISTS.getMsgCd());
        }
        int dlyCntLmt = qryOutSmsParamDO.getDlyCntLmt();
        // 短信验证码流量控制
        SmsTrafficDO qryInSmsTrafficDO = new SmsTrafficDO();
        qryInSmsTrafficDO.setMblNo(mblNo);
        qryInSmsTrafficDO.setTradeDate(tradeDate);
        SmsTrafficDO qryOutSmsTrafficDO = smsTrafficDao.getByCondition(qryInSmsTrafficDO);
        if (JudgeUtils.isNotNull(qryOutSmsTrafficDO) && qryOutSmsTrafficDO.getDlyCnt() >= dlyCntLmt) {
            LemonException.throwBusinessException(MsgCdEnum.SMS_SEND_OVERLIMIT.getMsgCd());
        }
        SmsCodeDO smsCodeDO = new SmsCodeDO();
        // 生成短信验证码
        String smsCode = RandomUtils.randomNumeric(6);
        // 生成token值
        String token = RandomUtils.randomStringFixLength(32);
        
        // 查询短信模版
        SmsTemplateDO qryInSmsTemplateDO = new SmsTemplateDO();
        qryInSmsTemplateDO.setType(smsCodeType);
        List<SmsTemplateDO> smsTemplateDOs = smsTemplateDao.getListByCondition(qryInSmsTemplateDO);
        if (JudgeUtils.isEmpty(smsTemplateDOs)) {
            LemonException.throwBusinessException(MsgCdEnum.SMSTEMP_NOT_EXISTS.getMsgCd());
        }
        SmsTemplateDO  smsTemplateDO = smsTemplateDOs.get(0);
        // 判断语种
        logger.info("SmsServiceImpl.smsCodeSend() 下发短信验证码，语种: " + language);
        String templateContent = smsTemplateDO.getTemplateContentEn();
        if (language.equals("km")) {
            templateContent = smsTemplateDO.getTemplateContentKh();
        } else if (language.equals("zh")) {
            templateContent = smsTemplateDO.getTemplateContentCn();
        }
        
        // 解析短信模版
        Map<String, String> replaceFieldMap = new HashMap<>();
        replaceFieldMap.put("smsCode", smsCode);
        String smsContent = null;
        try {
            smsContent = SmsTemplateResolveUtil.resolveTemplate(replaceFieldMap, smsTemplateDO.getReplaceField(), templateContent);
        } catch (LemonException e) {
            LemonException.throwBusinessException(e.getMsgCd());
        }
        
        // 登记短信验证码流水
        smsCodeDO.setCodeJrnNo(generateOrderNo());
        smsCodeDO.setType(smsCodeType);
        smsCodeDO.setMblNo(mblNo);
        smsCodeDO.setSmsCode(smsCode);
        smsCodeDO.setToken(token);
        smsCodeDO.setCodeStats(Constants.CHECK_INIT); // 0:初始化 1:已验证 2:已失效
        smsCodeDO.setEffTime(tradeTime);
        smsCodeDO.setExpTime(tradeTimeAfter3Minute);
        int result = smsCodeDao.insert(smsCodeDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.SMSCODE_SEND_FAIL.getMsgCd());
        }
        // 登记短信流量控制记录表(考虑异步登记)
        SmsTrafficDO smsTrafficDO = new SmsTrafficDO();
        smsTrafficDO.setMblNo(mblNo);
        smsTrafficDO.setTradeDate(tradeDate);
        smsTrafficDO.setDlyCnt(1);
        smsTrafficDO.setCreateTime(tradeTime);
        smsTrafficDO.setModifyTime(tradeTime);
        result = smsTrafficDao.insertOrUpdate(smsTrafficDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.SMSCODE_SEND_FAIL.getMsgCd());
        }
        try {
            String countryCode = PhoneNumberUtils.getCountryCode(mblNo);
            String phoneNumber = PhoneNumberUtils.getPhoneNumber(mblNo);
            // 判断是否为柬埔寨号码
            if (countryCode.equals(Constants.ARECODE_KH)) {
                PhoneSegementDO phoneSegementDO = phoneSegementDao.getByCondition(countryCode, phoneNumber, phoneNumber.length());
                if (JudgeUtils.isNotNull(phoneSegementDO) && phoneNumber.startsWith("0")) {
                    // 柬埔寨0开始号码去0
                    phoneNumber = phoneNumber.substring(1);
                } else if(JudgeUtils.isNull(phoneSegementDO) || JudgeUtils.isBlank(phoneNumber)) {
                    // 号段表不匹配直接报错
                    LemonException.throwBusinessException(MsgCdEnum.PHONENUMBER_INVALID.getMsgCd());
                }
            }
            // 调用第三方短信平台下发短信验证码(国家区号+手机号码)
            String smsMblNo =  countryCode + phoneNumber;
            // 下发短信验证码
            logger.info("smsMblNo : " + smsMblNo);
            SmsBo smsBo = new SmsBo(Constants.SMS_SEND_SRCID, smsMblNo, smsContent);
            String smsjson = objectMapper.writeValueAsString(smsBo);
            String sendUrl = env.getProperty("cmm.sms.url");
            HttpClientUtil.doPostJson(sendUrl, Base64.encodeBase64String(smsjson.getBytes()));
        } catch (JsonProcessingException e) {
            logger.debug("JsonProcess err occured at sms send");
        } catch (IOException e) {
            logger.debug("Network err occured at sms send", e);
        }
        smsCodeRspDTO.setToken(token);
        return smsCodeRspDTO;
    }

    @Override
    public void smsCodeCheck(SmsCheckReqDTO smsCheckReqDTO) throws LemonException {
        String mblNo = smsCheckReqDTO.getMblNo();
        String type = smsCheckReqDTO.getType();
        String smsCode = smsCheckReqDTO.getSmsCode();
        String token = smsCheckReqDTO.getToken();
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        // 校验短信验证码
        SmsCodeDO qryInSmsCodeDO = new SmsCodeDO();
        qryInSmsCodeDO.setMblNo(mblNo);
        qryInSmsCodeDO.setType(type);
        qryInSmsCodeDO.setSmsCode(smsCode);
        qryInSmsCodeDO.setToken(token);
        qryInSmsCodeDO.setCodeStats(Constants.CHECK_INIT);
        qryInSmsCodeDO.setExpTime(tradeTime);
        SmsCodeDO qryOutSmsCodeDO = smsCodeDao.getByCondition(qryInSmsCodeDO);
        if (JudgeUtils.isNull(qryOutSmsCodeDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SMS_CHECK_FAIL.getMsgCd());
        }
        // 更新短信验证码
        SmsCodeDO updValueSmsCodeDO = new SmsCodeDO();
        updValueSmsCodeDO.setCodeStats(Constants.CHECK_VERIFIED);
        SmsCodeDO updConditionSmsCodeDO = new SmsCodeDO();
        updConditionSmsCodeDO.setMblNo(mblNo);
        updConditionSmsCodeDO.setType(type);
        updConditionSmsCodeDO.setSmsCode(smsCode);
        updConditionSmsCodeDO.setCodeStats(Constants.CHECK_INIT);
        int result = smsCodeDao.updateByCondition(updValueSmsCodeDO, updConditionSmsCodeDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.SMS_CHECK_FAIL.getMsgCd());
        }
    }

    @Override
    public void smsSend(SmsSendReqDTO smsSendReqDTO) throws LemonException {
        String mblNo = smsSendReqDTO.getMblNo();
        String type = smsSendReqDTO.getSmsType();
        String smsTemplateId = smsSendReqDTO.getSmsTemplateId();
        String smsLanguage = smsSendReqDTO.getSmsLanguage();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        LocalDateTime tradeDateTime = DateTimeUtils.getCurrentLocalDateTime();
        // 查询短信流量控制日累积(考虑redis存储)
        SmsParamDO qryOutSmsParamDO = smsParamDao.get(Constants.SMS_PARAM_ID);
        if (JudgeUtils.isNull(qryOutSmsParamDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SMSPARAM_NOT_EXISTS.getMsgCd());
        }
        int dlySmsCntLmt = qryOutSmsParamDO.getDlySmsCntLmt();
        // 短信流量控制
        SmsTrafficDO qryInSmsTrafficDO = new SmsTrafficDO();
        qryInSmsTrafficDO.setMblNo(mblNo);
        qryInSmsTrafficDO.setTradeDate(tradeDate);
        SmsTrafficDO qryOutSmsTrafficDO = smsTrafficDao.getByCondition(qryInSmsTrafficDO);
        if (JudgeUtils.isNotNull(qryOutSmsTrafficDO) && qryOutSmsTrafficDO.getDlySmsCnt() >= dlySmsCntLmt) {
            LemonException.throwBusinessException(MsgCdEnum.SMS_SEND_OVERLIMIT.getMsgCd());
        }
        // 查询短信模版
        SmsTemplateDO smsTemplateDO = smsTemplateDao.get(smsTemplateId);
        if (JudgeUtils.isNull(smsTemplateDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SMSTEMP_NOT_EXISTS.getMsgCd());
        }
        String replaceField = smsTemplateDO.getReplaceField();
        String templateContentKh = smsTemplateDO.getTemplateContentKh();
        String templateContentCn = smsTemplateDO.getTemplateContentCn();
        String templateContentEn = smsTemplateDO.getTemplateContentEn();
        // 判断语种
        logger.info("SmsServiceImpl.smsSend() 下发短信，语种: " + smsLanguage);
        String templateContent = templateContentEn;
        if (smsLanguage.equals("km")) {
            templateContent = templateContentKh;
        } else if (smsLanguage.equals("zh")) {
            templateContent = templateContentCn;
        }
        // 解析短信模版
        String smsContent = null;
        try {
            smsContent = SmsTemplateResolveUtil.resolveTemplate(smsSendReqDTO.getReplaceFieldMap(), replaceField,
                    templateContent);
        } catch (LemonException e) {
            LemonException.throwBusinessException(e.getMsgCd());
        }
        if (StringUtils.isBlank(templateContent)) {
            LemonException.throwBusinessException(MsgCdEnum.SMSTEMP_RESOLVE_ERROR.getMsgCd());
        }
        // 登记短信下行流水表
        SmsSendDO smsSendDO = new SmsSendDO();
        smsSendDO.setSmsJrnNo(generateOrderNo());
        smsSendDO.setTradeDate(tradeDate);
        smsSendDO.setTradeTime(tradeTime);
        smsSendDO.setType(type);
        smsSendDO.setLvl("0"); // 短信级别(待定义)
        smsSendDO.setMblNo(mblNo);
        smsSendDO.setSmsContent(smsContent);
        int result = smsSendDao.insert(smsSendDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.SMS_SEND_FAIL.getMsgCd());
        }

        // 登记短信流量控制记录表(考虑异步登记)
        SmsTrafficDO smsTrafficDO = new SmsTrafficDO();
        smsTrafficDO.setMblNo(mblNo);
        smsTrafficDO.setTradeDate(tradeDate);
        smsTrafficDO.setDlySmsCnt(1);
        smsTrafficDO.setCreateTime(tradeDateTime);
        smsTrafficDO.setModifyTime(tradeDateTime);
        result = smsTrafficDao.insertOrUpdate(smsTrafficDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.SMSCODE_SEND_FAIL.getMsgCd());
        }
        try {
            String countryCode = PhoneNumberUtils.getCountryCode(mblNo);
            String phoneNumber = PhoneNumberUtils.getPhoneNumber(mblNo);
            // 判断是否为柬埔寨号码
            if (countryCode.equals(Constants.ARECODE_KH)) {
                PhoneSegementDO phoneSegementDO = phoneSegementDao.getByCondition(countryCode, phoneNumber, phoneNumber.length());
                if (JudgeUtils.isNotNull(phoneSegementDO) && phoneNumber.startsWith("0")) {
                    // 柬埔寨0开始号码去0
                    phoneNumber = phoneNumber.substring(1);
                } else {
                    // 号段表不匹配直接报错
                    LemonException.throwBusinessException(MsgCdEnum.PHONENUMBER_INVALID.getMsgCd());
                }
            }
            // 调用第三方短信平台下发短信验证码(国家区号+手机号码)
            String smsMblNo =  countryCode + phoneNumber;
            // 下发短信验证码
            logger.info("smsMblNo : " + smsMblNo);
            SmsBo smsBo = new SmsBo(Constants.SMS_SEND_SRCID, smsMblNo, smsContent);
            String smsjson = objectMapper.writeValueAsString(smsBo);
            String sendUrl = env.getProperty("cmm.sms.url");
            HttpClientUtil.doPostJson(sendUrl, Base64.encodeBase64String(smsjson.getBytes()));
        } catch (JsonProcessingException e) {
            logger.debug("JsonProcess err occured at sms send");
        } catch (IOException e) {
            logger.debug("Network err occured at sms send", e);
        }
    }
    
    // 生成订单号
    private String generateOrderNo() {
        String tradeDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
        String orderJrn = IdGenUtils.generateId("CMM_ORDER_NO", 8);
        return LemonUtils.getApplicationName() + tradeDateTimeStr + orderJrn;
    }
}
