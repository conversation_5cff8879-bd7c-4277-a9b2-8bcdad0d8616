/*
 * @ClassName BannerPulblicDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 12:39:12
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class BannerPulblicDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private String id;
    /**
     * @Fields bannerUrlKh 柬语图片url
     */
    private String bannerUrlKh;
    /**
     * @Fields bannerUrlCn 中文图片url
     */
    private String bannerUrlCn;
    /**
     * @Fields bannerUrlEn 英文图片url
     */
    private String bannerUrlEn;
    /**
     * @Fields detailUrlKh 柬语详情url
     */
    private String detailUrlKh;
    /**
     * @Fields detailUrlCn 中文详情url
     */
    private String detailUrlCn;
    /**
     * @Fields detailUrlEn 英文详情url
     */
    private String detailUrlEn;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBannerUrlKh() {
        return bannerUrlKh;
    }

    public void setBannerUrlKh(String bannerUrlKh) {
        this.bannerUrlKh = bannerUrlKh;
    }

    public String getBannerUrlCn() {
        return bannerUrlCn;
    }

    public void setBannerUrlCn(String bannerUrlCn) {
        this.bannerUrlCn = bannerUrlCn;
    }

    public String getBannerUrlEn() {
        return bannerUrlEn;
    }

    public void setBannerUrlEn(String bannerUrlEn) {
        this.bannerUrlEn = bannerUrlEn;
    }

    public String getDetailUrlKh() {
        return detailUrlKh;
    }

    public void setDetailUrlKh(String detailUrlKh) {
        this.detailUrlKh = detailUrlKh;
    }

    public String getDetailUrlCn() {
        return detailUrlCn;
    }

    public void setDetailUrlCn(String detailUrlCn) {
        this.detailUrlCn = detailUrlCn;
    }

    public String getDetailUrlEn() {
        return detailUrlEn;
    }

    public void setDetailUrlEn(String detailUrlEn) {
        this.detailUrlEn = detailUrlEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}