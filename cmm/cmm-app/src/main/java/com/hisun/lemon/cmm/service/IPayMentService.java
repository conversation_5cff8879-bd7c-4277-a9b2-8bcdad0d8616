package com.hisun.lemon.cmm.service;

import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.gateway.dto.AppInfoDTO;

/**
 * 
 * @Description 支付信息接口
 * <AUTHOR>
 * @date 2017年11月17日 上午12:07:48
 * @version V1.0
 */
public interface IPayMentService {

    /**
     * @Description 微信用户授权
     * <AUTHOR>
     * @param Code
     * @throws LemonException
     */
    void weChatGrantInfo(String Code, String state) throws LemonException;

    GenericRspDTO<PayWeChatInfoRspDTO> getWeChatGrantInfo(String state) throws LemonException;

    
}
