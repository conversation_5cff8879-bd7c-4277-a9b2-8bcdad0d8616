package com.hisun.lemon.cmm.constants;

/**
 * @Description 公共模块错误码枚举类
 * <AUTHOR>
 * @date 2017年7月7日 下午3:27:58
 * @version V1.0
 */
public enum MsgCdEnum {

    /** 交易成功 */
    SUCCESS("CMM00000", "transaction successfull"),
    /** 手机号码不能为空 */
    MBLNO_CANTNULL("CMM10001", "phone number cannot be empty"),
    /** 短信验证码类型不能为空 */
    SMSCODETYPE_CANTNULL("CMM10002", "sms captcha type cannot be empty"),
    /** 短信验证码不能为空 */
    SMSCODE_CANTNULL("CMM10003", "sms captcha cannot be empty"),
    /** 短信类型不能为空 */
    SMSTYPE_CANTNULL("CMM10004", "sms type cannot be empty"),
    /** 短信模版编号不能为空 */
    SMSTEMPID_CANTNULL("CMM10005", "sms template number cannot be empty"),
    /** 短信语言不能为空 */
    SMSLANGUAGE_CANTNULL("CMM10006", "sms lang type cannot be empty"),
    /** 用户号不能为空 */
    USERID_CANTNULL("CMM10007", "userid cannot be empty"),
    /** 付款码不能为空 */
    PAYMENT_CODE_CANTNULL("CMM10008", "payment code cannot be empty"),
    /** 订单号不能为空 */
    ORDER_NO_CANTNULL("CMM10009", "orderno cannot be empty"),
    /** 短信内容不能为空 */
    SMS_CONTENT_CANTNULL("CMM10011", "sms content cannot be empty"),
    /** 二维码类型不能为空 */
    QRCODE_TYPE_CANTNULL("CMM10012", "qrcode type cannot be empty"),
    /** 二维码参数信息不能为空 */
    QRCODE_PARAMS_CANTNULL("CMM10015", "qrcode url params cannot be empty"),
    /** 短信验证码token不能为空 */
    SMS_TOKEN_CANTNULL("CMM10017", "sms code token cannot be empty"),
    /** 消息类型不能为空 */
    MESSAGE_TYPE_CANTNULL("CMM10018", "message type cannot be empty"),
    /** 消息模版不能为空 */
    MESSAGE_TEMPLATE_CANTNULL("CMM10019", "message template cannot be empty"),
    /** 消息内容不能为空 */
    MESSAGE_CONTENT_CANTNULL("CMM10021", "message content cannot be empty"),
    /** 用户类型不能为空 */
    USER_TYPE_CANTNULL("CMM10022", "user type cannot be empty"),
    /** 系统key索引不能为空 */
    KEY_INDEX_CANTNULL("CMM10023", "system key index cannot be empty"),
    /** 消息ClientID不能为空 */
    MESSAGE_CLIENTID_CANTNULL("CMM10024", "message client id cannot be empty"),
    /** 加解密数据不能为空 */
    ENCRYPT_DATA_CANTNULL("CMM10025", "encrypt&decrypt data cannot be empty"),
    /** 加解密类型不能为空 */
    ENCRYPT_TYPE_CANTNULL("CMM10026", "encrypt&decrypt type cannot be empty"),
    /** 当前页码 */
    PAGE_NUMBER_CANTNULL("CMM19998", "message language cannot be empty"),
    /** 每页大小 */
    PAGE_SIZE_CANTNULL("CMM19999", "message content cannot be empty"),
    /** 短信验证码下发失败 */
    SMSCODE_SEND_FAIL("CMM30001", "sms verification code send failed"),
    /** 短信下发失败 */
    SMS_SEND_FAIL("CMM30002", "sms send failed"),
    /** 短信验证码校验失败 */
    SMS_CHECK_FAIL("CMM30003", "sms code check failed"),
    /** 短信模版失效或不存在 */
    SMSTEMP_NOT_EXISTS("CMM30005", "sms template not exists"),
    /** 短信模版配置有误 */
    SMSTEMP_RESOLVE_ERROR("CMM30006", "sms template resolve error"),
    /** 二维码生成失败 */
    RCPQRCODE_GEN_FAIL("CMM30007", "qrcode generate failed"),
    /** 账户二维码生成失败 */
    ACQRCODE_GEN_FAIL("CMM30008", "qrcode generate failed"),
    /** 付款二维码生成失败 */
    PAYQRCODE_GEN_FAIL("CMM30009", "qrcode generate failed"),
    /** 付款码有误或已失效 */
    PAYQRCODE_RESOLVE_ERROR("CMM30010", "payment code resolve failed"),
    /** 收款码有误或已失效 */
    RCPQRCODE_RESOLVE_ERROR("CMM30011", "qrcode resolve failed"),
    /** 短信模版删除失败 */
    SMSTEMP_DEL_FAIL("CMM30012", "sms template delete failed"),
    /** 短信模版查询失败 */
    SMSTEMP_QRY_FAIL("CMM30013", "sms template query failed"),
    /** 短信模版更新失败 */
    SMSTEMP_UPD_FAIL("CMM30014", "sms template update failed"),
    /** 手机号段信息未配置 */
    SEGEMENT_NOT_EXISTS("CMM30015", "segement not exists"),
    /** 短信参数信息不存在 */
    SMSPARAM_NOT_EXISTS("CMM30016", "segement check failed"),
    /** 短信下发次数超限 */
    SMS_SEND_OVERLIMIT("CMM30017", "sms code over daily limit"),
    /** 消息模版不存在或失效 */
    MESSAGETEMP_NOT_EXISTS("CMM30018", "message template not exists"),
    /** 消息模版配置有误 */
    MESSAGETEMP_RESOLVE_ERROR("CMM30019", "message template resolve error"),
    /** 消息推送失败 */
    MESSAGE_SEND_FAIL("CMM30020", "message push failed"),
    /** 消息列表查询失败 */
    MESSAGELIST_QRY_FAIL("CMM30021", "message list query failure"),
    /** 公共常量插入失败 **/
    INSERT_CONSTANT_PARAM_FAILURE("CMM30022", "insert constant param failed"),
    /** 公共常量更新失败 **/
    UPDATE_CONSTANT_PARAM_FAILURE("CMM30023", "update constant param failed"),
    /** 公共常量删除失败 **/
    DELETE_CONSTANT_PARAM_FAILURE("CMM30024", "delete constant param failed"),
    /** 公共常量查询失败 **/
    SELECT_CONSTANT_PARAM_FAILURE("CMM30025", "select constant param failed"),
    /** 公共常量列表查询失败 **/
    SELECT_ALL_CONSTANT_PARAMS_FAILURE("CMM30026", "select all constant params failed"),
    /** 密钥不存在或已失效 */
    SYSTEMKEY_NOT_EXISTS("CMM30027", "system key not exists"),
    /** 消息推送绑定ID设置失败 */
    MESSAGE_CLIENT_FAIL("CMM30028", "message push client id set failed"),
    /** 消息推送绑定ID不存在或失效 */
    MESSAGE_CLIENT_NOT_EXISTS("CMM30029", "message push client id not exists"),
    /** 暂不支持此种支付方式 */
    PAYQRCODE_RESOLVE_FAIL("CMM30030", "payment qrcode resolve failed"),
    /** 手机号无效 */
    PHONENUMBER_INVALID("CMM30031", "phone number invalid"),
    /** 加解密失败 */
    ENCRYPT_DECRYPT_FAIL("CMM30032", "encrypt&decrypt failed"),
    /** 登陆信息查询失败 */
    LOGINFO_QRY_FAIL("CMM30033", "loginfo query failed"),
    LAST("CMM39999", "last"),
    /** 常量参数过期 */
    CONSTANT_PARAM_EXPIRED("CMM30030", "Constant param is already expired"),


    //微信授权通讯异常
    WECHAT_CONNECT_FAIL("CMM40001", "weChat connect timeout"),
    //微信授权信息丢失或获取异常
    WECHATINFO_QUERY_FAIL("CMM40002", "weChatInfo query error!"),
    //微信授权code 为空
    WECHAT_CODE_ISNULL("CMM40003", "weChat code is null"),
    //微信授权参数为空
    WECHAT_STATE_ISNULL("CMM40004", "weChat state is null"),

    //
    APP_CHANNEL_IS_NULL("CMM50001", "app channel is null"),
    APP_CHANNEL_IS_ERROR("CMM50002", "app channel is error"),
    APP_VERSION_IS_NULL("CMM50003", "app version is null"),

    ;

    private String msgCd;
    private String msgInfo;

    private MsgCdEnum(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    public String getMsgCd() {
        return msgCd;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

}
