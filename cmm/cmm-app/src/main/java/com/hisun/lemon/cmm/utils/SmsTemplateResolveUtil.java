package com.hisun.lemon.cmm.utils;

import java.util.Map;

import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.StringUtils;

/**
 * @Description 短信模版解析工具
 * <AUTHOR>
 * @date 2017年7月10日 下午4:31:32
 * @version V1.0
 */
public class SmsTemplateResolveUtil {
    
    private SmsTemplateResolveUtil() {
        throw new IllegalStateException("Utility class");
    }

    /** 短信模版替换变量 */
    private static final String REPL_VARIABLE = "[$]";
    /** 竖线分隔符 */
    private static final String VERTICALLINE = "\\|";

    /**
     * @Description 解析短信模版
     * <AUTHOR>
     * @param replaceFieldMap
     * @param replaceField
     * @param smsTemplateConent
     * @return
     * @throws LemonException
     */
    public static String resolveTemplate(Map<String, String> replaceFieldMap, String replaceField, String smsTemplateConent) throws LemonException {
        String[] replaceFieldArray = replaceField.split(VERTICALLINE); 
        for (int i = 0; i < replaceFieldArray.length; i++) {
            String key = replaceFieldArray[i];
            String value = replaceFieldMap.get(key);
            if (StringUtils.isBlank(value)) {
                LemonException.throwBusinessException(MsgCdEnum.SMSTEMP_RESOLVE_ERROR.getMsgCd());
            }
            smsTemplateConent = StringUtils.replaceOnce(smsTemplateConent, REPL_VARIABLE, value);
        }
        return smsTemplateConent;
    }
}
