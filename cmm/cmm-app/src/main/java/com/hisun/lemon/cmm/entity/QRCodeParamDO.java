/*
 * @ClassName QRCodeParamDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-09 12:39:34
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class QRCodeParamDO extends BaseDO {
    /**
     * @Fields id 参数ID
     */
    private String id;
    /**
     * @Fields paymentCodePrefix 付款码前缀
     */
    private String paymentCodePrefix;
    /**
     * @Fields aggregateUrl 聚合支付url
     */
    private String aggregateUrl;
    /**
     * @Fields tkey 加解密密钥
     */
    private String tkey;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields stats 状态 0:无效 1:有效
     */
    private String stats;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPaymentCodePrefix() {
        return paymentCodePrefix;
    }

    public void setPaymentCodePrefix(String paymentCodePrefix) {
        this.paymentCodePrefix = paymentCodePrefix;
    }

    public String getAggregateUrl() {
        return aggregateUrl;
    }

    public void setAggregateUrl(String aggregateUrl) {
        this.aggregateUrl = aggregateUrl;
    }

    public String getTkey() {
        return tkey;
    }

    public void setTkey(String tkey) {
        this.tkey = tkey;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}