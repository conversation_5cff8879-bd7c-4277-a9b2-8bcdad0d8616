/*
package com.hisun.lemon.cmm.api.wechat;

import com.hisun.channel.client.IChannelClient;
import com.hisun.channel.data.Request;
import com.hisun.channel.data.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

*/
/**
 * User : Rui Date : 2017/8/28 Time : 17:12
 **//*

@Component
public class WeChatApi {

	@Resource
	private IChannelClient channelClient;

	@SuppressWarnings("unchecked")
	public <T> T doSend(Object obj,  String sourceName) {

		Request request = new Request();
		request.setRoute("WEIXIN");
		request.setBusiType(sourceName);
		request.setSource(sourceName);
		request.setTarget(obj);
		request.setRequestId("1");
		Response response = channelClient.request(request);
		Object t =  response.getResult();
		return (T)t;
	}
}
*/
