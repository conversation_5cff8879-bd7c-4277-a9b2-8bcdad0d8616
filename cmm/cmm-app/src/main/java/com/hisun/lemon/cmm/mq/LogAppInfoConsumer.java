package com.hisun.lemon.cmm.mq;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.hisun.lemon.cmm.service.ICommonService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.gateway.dto.AppInfoDTO;

/**
 * @Description 终端信息登记
 * <AUTHOR>
 * @date 2017年9月7日 下午5:23:20 
 * @version V1.0
 */
@Component("logAppInfoHandler")
public class LogAppInfoConsumer implements MessageHandler<AppInfoDTO> {
    
    private static final Logger logger = LoggerFactory.getLogger(LogAppInfoConsumer.class);
    
    /**
     * 公共服务
     */
    @Resource
    private ICommonService commonService;

    @Override
    public void onMessageReceive(GenericCmdDTO<AppInfoDTO> genericCmdDTO) {
        logger.info("Receive msg hand {}", genericCmdDTO.getBody());
        try {
            String channel = genericCmdDTO.getChannel();
            String loginName = genericCmdDTO.getLoginName();
            commonService.logAppInfo(channel, loginName, genericCmdDTO.getBody());
        } catch (LemonException e) {
            logger.debug("Receive msg hand error {}", e.getMsgCd());
        }
    }

}
