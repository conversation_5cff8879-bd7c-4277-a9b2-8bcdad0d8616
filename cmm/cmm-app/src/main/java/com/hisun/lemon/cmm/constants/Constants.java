package com.hisun.lemon.cmm.constants;

/**
 * @Description 公共模块常量类
 * <AUTHOR>
 * @date 2017年7月14日 下午5:49:21
 * @version V1.0
 */
public class Constants {
    
    private Constants() {
        throw new IllegalStateException("Utility class");
    }

    /** 校验枚举值 初始状态 */
    public static final String CHECK_INIT = "0";
    /** 校验枚举值 验证状态 */
    public static final String CHECK_VERIFIED = "1";
    /** 校验枚举值 失效状态 */
    public static final String CHECK_EXPIRE = "2";
    /** 状态枚举值 无效状态 */
    public static final String STATUS_INVALID = "0";
    /** 状态枚举值 有效状态 */
    public static final String STATUS_AVAILABLE = "1";
    /** 短信验证码类型 注册 */
    public static final String SMSCODE_TYPE_REGISTER = "0";
    /** 短信验证码类型 登陆 */
    public static final String SMSCODE_TYPE_LOGIN = "1";
    /** 短信验证码类型 重置登陆密码 */
    public static final String SMSCODE_TYPE_LOGINPWDRESET = "2";
    /** 短信验证码类型 重置支付密码 */
    public static final String SMSCODE_TYPE_PAYPWDRESET = "3";
    /** 短信验证码类型 预签约 */
    public static final String SMSCODE_TYPE_SIGN = "4";
    /** 短信验证码类型 支付 */
    public static final String SMSCODE_TYPE_PAY = "5";
    /** 短信下发srcid */
    public static final String SMS_SEND_SRCID = "Mpay";
    /** 语种枚举值 英文 */
    public static final String LANG_EN = "en";
    /** 语种枚举值 中文 */
    public static final String LANG_ZH = "zh";
    /** 语种枚举值 柬文 */
    public static final String LANG_KM= "km";
    /** 运营商默认值 */
    public static final String CARRIER_SEATEL = "seatel";
    /** 付款方式枚举值 */
    public static final String PAYTYPE_SEATEL = "Mpay";
    /** 付款方式枚举值 */
    public static final String PAYTYPE_BESTPAY = "Bestpay";
    /** 付款方式枚举值 */
    public static final String PAYTYPE_ALIPAY = "Alipay";
    /** 短信 */
    public static final String SMS = "sms";
    /** 消息 */
    public static final String MESSAGE = "message";
    /** 消息类型 */
    public static final String MESSAGE_TYPE_CENTER = "center";
    /** 消息类型 */
    public static final String MESSAGE_TYPE_PAY = "pay";
    /** 消息类型 */
    public static final String MESSAGE_TYPE_ORDER = "order";
    /** 活动 */
    public static final String CAMPAIGN = "campaign";
    /** 很久以后 */
    public static final String LONGAFTERWARD = "20991231235959";
    /** 条码 */
    public static final String BARCODE = "barCode";
    /** 二维码 */
    public static final String QRCODE = "qrCode";
    /** 二维码参数ID */
    public static final String QRCODE_PARAM_ID = "QRCODEPARAM";
    /** 短信参数ID */
    public static final String SMS_PARAM_ID = "SMSPARAM";
    /** 消息推送参数ID */
    public static final String PUSH_PARAMID_MERCHANT = "MERCHANT";
    /** 消息推送参数ID */
    public static final String PUSH_PARAMID_PERSONAL = "PERSONAL";
    /** 二维码url分隔符 */
    public static final String QRCODE_URLPARAM_SEPARATOR = "=";
    /** 二维码参数信息分隔符 */
    public static final String QRCODE_PARAM_SEPARATOR = "|";
    /** 二维码参数信息分隔符 */
    public static final String QRCODE_RESOLVE_SEPARATOR = "\\|";
    /** 二维码类型 个人账户 */
    public static final String PERSONAL_ACCOUNT = "PA";
    /** 二维码类型 商户账户 */
    public static final String MERCHANT_ACCOUNT = "MA";
    /** 二维码类型 商户订单 */
    public static final String MERCHANT_ORDER = "MO";
    /** 二维码类型 聚合支付 */
    public static final String AGGREGATE = "AG";
    /** 用户类型枚举值 */
    public static final String USER_TYPE_PERSONAL = "personal";
    /** 用户类型枚举值 */
    public static final String USER_TYPE_MERCHANT = "merchant";
    /** TOTP初始时间 */
    public static final long T0 = 0;
    /** TOTP口令变化周期 */
    public static final long X = 30;
    /** 口令长度 */
    public static final String TOKENLEN = "8";
    /** TOTP HmacSHA1 */
    public static final String HMACSHA1 = "HmacSHA1";
    /** TOTP HmacSHA256 */
    public static final String HMACSHA256 = "HmacSHA256";
    /** TOTP HmacSHA512 */
    public static final String HMACSHA512 = "HmacSHA512";
    /** TOTP 密钥长度 */
    public static final int SEEDSIZE = 128;
    /** 加解密类型枚举值  加密*/
    public static final String ENCRYPT = "encrypt";
    /** 加解密类型枚举值  解密*/
    public static final String DECRYPT = "decrypt";
    /** 区域代码枚举值  柬埔寨*/
    public static final String ARECODE_KH = "855";

    /** 公共常量无效 **/
    public static final String PARAM_EFF_FLG_EXPIRED = "0";
    /** 公共常量有效 **/
    public static final String PARAM_EFF_FLG_EFFECTIVE = "1";

    /** 透传 **/
    public static final String PASS_PUSH_FLG_Y = "1";
    /** 非透传 **/
    public static final String PASS_PUSH_FLG_N = "0";

    //渠道常量
    public static final String TERM_TYPE_I = "iOS";
    public static final String TERM_TYPE_A = "android";

    //柬埔寨手机区号 855
    public static final String COUNTRY_CODE_855 = "855";

    //环境常量
    public static final String ENV_UAT = "dev";
    public static final String ENV_PRO = "pro";

    //个推ios版本
    public static final String GETUI_IOS_VERSION = "8.2";

    //安卓用户app
    public static final String A_CHANNEL_USER = "USRA";
    //苹果用户app
    public static final String I_CHANNEL_USER = "USRI";
    //安卓商户app
    public static final String A_CHANNEL_MERC = "MERA";
    //苹果商户app
    public static final String I_CHANNEL_MERC = "MERI";
    //商户app
    public static final String APP_TYPE_MERC = "1";
    //用户app
    public static final String APP_TYPE_USER = "0";



    //安卓用户app版本
    public static final String A_UAPP_VERSION = "3";
    public static final String A_UAPP_TYPE = "USRA";
    //安卓商户app版本
    public static final String A_MAPP_VERSION = "2.0.0";
    public static final String A_MAPP_TYPE = "mer";


    /** 文件后缀截取分隔符 */
    public static final String FILE_SPLIT_VAL = "\\.";
    public static final String FILE_SPLIT_VAL2 = ".";

    /** 图片格式 */
    public static final String JPG = "jpg";
    public static final String PNG = "png";
    public static final String GIF = "gif";
    public static final String JPEG = "jpeg";
    public static final String TIF = "tif";
    public static final String PDF = "pdf";
    public static final String BMP = "bmp";

}
