/*
 * @ClassName QRCodeSecretDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-20 20:01:30
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class QRCodeSecretDO extends BaseDO {
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields barcodeSecretKey 条码生成密钥
     */
    private String barcodeSecretKey;
    /**
     * @Fields qrcodeSecretKey 二维码生成密钥
     */
    private String qrcodeSecretKey;
    /**
     * @Fields secretKeyStatus 密钥状态 0:失效 1:有效
     */
    private String secretKeyStatus;
    /**
     * @Fields effTime 生效时间
     */
    private LocalDateTime effTime;
    /**
     * @Fields expTime 失效时间
     */
    private LocalDateTime expTime;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBarcodeSecretKey() {
        return barcodeSecretKey;
    }

    public void setBarcodeSecretKey(String barcodeSecretKey) {
        this.barcodeSecretKey = barcodeSecretKey;
    }

    public String getQrcodeSecretKey() {
        return qrcodeSecretKey;
    }

    public void setQrcodeSecretKey(String qrcodeSecretKey) {
        this.qrcodeSecretKey = qrcodeSecretKey;
    }

    public String getSecretKeyStatus() {
        return secretKeyStatus;
    }

    public void setSecretKeyStatus(String secretKeyStatus) {
        this.secretKeyStatus = secretKeyStatus;
    }

    public LocalDateTime getEffTime() {
        return effTime;
    }

    public void setEffTime(LocalDateTime effTime) {
        this.effTime = effTime;
    }

    public LocalDateTime getExpTime() {
        return expTime;
    }

    public void setExpTime(LocalDateTime expTime) {
        this.expTime = expTime;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}