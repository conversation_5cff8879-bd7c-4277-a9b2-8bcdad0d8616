package com.hisun.lemon.cmm.service.impl;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cmm.dto.NewsRspDTO;
import com.hisun.lemon.cmm.service.GetNewsService;
import com.hisun.lemon.cmm.utils.HttpClientUtil;
import com.hisun.lemon.framework.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service("getNewsServiceImpl")
public class GetNewsServiceImpl extends BaseService implements GetNewsService {

    private final static Logger logger = LoggerFactory.getLogger(GetNewsServiceImpl.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${gnews.key}")
    private String API_KEY;

    @Value("${gnews.url}")
    private String API_URL;

    @Override
    public NewsRspDTO getNews(String fromDate, String toDate) {
        NewsRspDTO newsRspDTO = new NewsRspDTO();
        String query = "business";
        String language = "en";

        //组装日期参数
        fromDate = fromDate!=null?fromDate + "T00:00:00Z":null;
        toDate = toDate!=null?toDate + "T23:59:59Z":null;

        // 构建请求URL，包含查询参数
        StringBuilder urlBuilder = new StringBuilder(API_URL);
        urlBuilder.append("?q=").append(query);
        urlBuilder.append("&lang=").append(language);
        if(fromDate != null || toDate != null) {
            urlBuilder.append("&from=").append(fromDate);
            urlBuilder.append("&to=").append(toDate);
        }
        urlBuilder.append("&max=").append("5");
        urlBuilder.append("&token=").append(API_KEY);
        String requestUrl = urlBuilder.toString();

        // 执行HTTP请求
        try{
            String result = HttpClientUtil.doGet(requestUrl);
            newsRspDTO = parseResponse(result);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return newsRspDTO;
    }

    public static NewsRspDTO parseResponse(String result) throws IOException {
        if (result == null) {
            return null;
        }


        // 使用Jackson将JSON字符串映射到DTO对象
        return objectMapper.readValue(result, NewsRspDTO.class);
    }
}
