/*
 * @ClassName ISmsTrafficDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-27 15:09:42
 */
package com.hisun.lemon.cmm.dao;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.cmm.entity.SmsTrafficDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface ISmsTrafficDao extends BaseDao<SmsTrafficDO> {
    
    SmsTrafficDO getByCondition(SmsTrafficDO smsTrafficDO);
    
    int insertOrUpdate(SmsTrafficDO smsTrafficDO);
}