package com.hisun.lemon.cmm.mq;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.hisun.lemon.cmm.dto.SmsSendReqDTO;
import com.hisun.lemon.cmm.service.ISmsService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.stream.MessageHandler;

/**
 * @Description 短信异步下发
 * <AUTHOR>
 * @date 2017年9月7日 下午5:23:20 
 * @version V1.0
 */
@Component("smsSendHandler")
public class SmsSendConsumer implements MessageHandler<SmsSendReqDTO> {
    
    private static final Logger logger = LoggerFactory.getLogger(SmsSendConsumer.class);
    
    /**
     * 短信服务
     */
    @Resource
    private ISmsService smsService;
    
    @Override
    public void onMessageReceive(GenericCmdDTO<SmsSendReqDTO> genericCmdDTO) {
        logger.info("Receive msg hand {}", genericCmdDTO.getBody());
        try {
            smsService.smsSend(genericCmdDTO.getBody());
        } catch (LemonException e) {
            logger.debug("Receive msg hand error {}", e.getMsgCd());
        }
    }


}
