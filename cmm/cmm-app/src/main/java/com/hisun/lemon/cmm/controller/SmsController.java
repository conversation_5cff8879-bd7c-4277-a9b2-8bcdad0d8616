package com.hisun.lemon.cmm.controller;

import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.dto.SmsCheckReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeRspDTO;
import com.hisun.lemon.cmm.dto.SmsSendReqDTO;
import com.hisun.lemon.cmm.service.ISmsService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
 
/**
 * 短信管理
 * 
 * @GetMapping @PostMapping @PutMapping @DeleteMapping @PatchMapping
 * <AUTHOR>
 * @date 2017年7月5日
 * @time 上午10:46:30
 *
 */
@RestController
@RequestMapping("/sms")
@Api(tags = "SmsController", description = "短信服务")
public class SmsController extends BaseController{

    /**
     * 短信服务
     */
    @Resource
    private ISmsService smsService;

    @ApiOperation(value = "短信验证码下发", notes = "短信验证码下发")
    @ApiResponse(code = 200, message = "短信验证码下发结果")
    @PostMapping("/code")
    public GenericRspDTO<SmsCodeRspDTO> smsCodeSend(@Validated @RequestBody GenericDTO<SmsCodeReqDTO> reqDTO) {
        GenericRspDTO<SmsCodeRspDTO> rspDTO = new GenericRspDTO<>();
        SmsCodeReqDTO smsCodeReqDTO = reqDTO.getBody();
        SmsCodeRspDTO smsCodeRspDTO = null;
        try {
            smsCodeRspDTO = smsService.smsCodeSend(smsCodeReqDTO);
            rspDTO.setBody(smsCodeRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "短信验证码校验", notes = "短信验证码校验")
    @ApiResponse(code = 200, message = "短信验证码校验结果")
    @PostMapping("/code/check")
    public GenericRspDTO<NoBody> smsCodeCheck(@Validated @RequestBody GenericDTO<SmsCheckReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        SmsCheckReqDTO smsCheckReqDTO = reqDTO.getBody();
        try {
            smsService.smsCodeCheck(smsCheckReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "短信下发", notes = "短信下发")
    @ApiResponse(code = 200, message = "短信下发结果")
    @PostMapping("/send")
    public GenericRspDTO<NoBody> smsSend(@Validated @RequestBody GenericDTO<SmsSendReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        SmsSendReqDTO smsSendReqDTO = reqDTO.getBody();
        try {
            smsService.smsSend(smsSendReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
}
