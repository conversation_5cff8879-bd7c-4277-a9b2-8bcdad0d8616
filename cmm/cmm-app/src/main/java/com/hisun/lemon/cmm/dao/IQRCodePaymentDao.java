/*
 * @ClassName IQRCodePaymentDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-20 18:51:37
 */
package com.hisun.lemon.cmm.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.cmm.entity.QRCodePaymentDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface IQRCodePaymentDao extends BaseDao<QRCodePaymentDO> {
    
    QRCodePaymentDO getByCondition(QRCodePaymentDO qryInQRCodePaymentDO);
    
    int updateByCondition(@Param("valueDO")QRCodePaymentDO updValueQRCodePaymentDO, @Param("condtionDO")QRCodePaymentDO updConditionQRCodePaymentDO);
}