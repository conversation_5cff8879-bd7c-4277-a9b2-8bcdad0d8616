package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class ConstantParamDo extends BaseDO {
    /**
     * @Fields parmNm 常量名
     */
    private String parmNm;
    /**
     * @Fields effDt 生效日期
     */
    private LocalDate effDt;
    /**
     * @Fields expDt 失效日期
     */
    private LocalDate expDt;
    /**
     * @Fields effFlg 生效标志
     */
    private String effFlg;
    /**
     * @Fields parmDispNm 常量显示名
     */
    private String parmDispNm;
    /**
     * @Fields parmCls 常量归属模块
     */
    private String parmCls;
    /**
     * @Fields parmVal 常量值
     */
    private String parmVal;
    /**
     * @Fields rmk 备注信息
     */
    private String rmk;
    /**
     * @Fields updOprId 修改操作员
     */
    private String updOprId;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;

    public String getParmNm() {
        return parmNm;
    }

    public void setParmNm(String parmNm) {
        this.parmNm = parmNm;
    }

    public LocalDate getEffDt() {
        return effDt;
    }

    public void setEffDt(LocalDate effDt) {
        this.effDt = effDt;
    }

    public LocalDate getExpDt() {
        return expDt;
    }

    public void setExpDt(LocalDate expDt) {
        this.expDt = expDt;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getParmDispNm() {
        return parmDispNm;
    }

    public void setParmDispNm(String parmDispNm) {
        this.parmDispNm = parmDispNm;
    }

    public String getParmCls() {
        return parmCls;
    }

    public void setParmCls(String parmCls) {
        this.parmCls = parmCls;
    }

    public String getParmVal() {
        return parmVal;
    }

    public void setParmVal(String parmVal) {
        this.parmVal = parmVal;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    @Override
    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "ConstantParamDo{" +
                "parmNm='" + parmNm + '\'' +
                ", effDt=" + effDt +
                ", expDt=" + expDt +
                ", effFlg='" + effFlg + '\'' +
                ", parmDispNm='" + parmDispNm + '\'' +
                ", parmCls='" + parmCls + '\'' +
                ", parmVal='" + parmVal + '\'' +
                ", rmk='" + rmk + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}