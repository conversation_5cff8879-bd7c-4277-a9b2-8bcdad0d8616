package com.hisun.lemon.cmm.service;

import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.common.exception.LemonException;

/**
 * @Description 条码服务接口
 * <AUTHOR>
 * @date 2017年7月12日 下午2:17:48 
 * @version V1.0
 */
public interface IQRCodeService {
    
    PaymentCodeRspDTO paymentCodeGenerate() throws LemonException;
    
    PaymentCodeCheckRspDTO paymentCodeCheck(PaymentCodeCheckReqDTO paymentCodeCheckReqDTO) throws LemonException;
    
    QRCodeGenerateRspDTO qrCodeGenerate(QRCodeGenerateReqDTO generateReqDTO) throws LemonException;
    
    QRCodeResolveRspDTO qrCodeResolve(QRCodeResolveReqDTO qrCodeResolveReqDTO) throws LemonException;


    /**
     * 生成商户tms二维码
     * @param generateReqDTO
     * @return
     */
    TmsMakeQrcodeRspDTO tmsMakeQrcode(TmsMakeQrcodeReqDTO generateReqDTO);
}
