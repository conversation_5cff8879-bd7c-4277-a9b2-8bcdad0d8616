/*
 * @ClassName AppInfoDO
 * @Description 
 * @version 1.0
 * @Date 2017-09-07 16:59:53
 */
package com.hisun.lemon.cmm.entity;

import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class AppInfoDO extends BaseDO {
    /**
     * @Fields loginJrnNo 登录流水号
     */
    private String loginJrnNo;
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields userId 用户号登陆名称
     */
    private String loginName;
    /**
     * @Fields userType 用户类型
     */
    private String userType;
    /**
     * @Fields versions 版本号
     */
    private String versions;
    /**
     * @Fields hannel 交易渠道信息
     */
    private String channel;
    /**
     * @Fields downloadChannel 渠道信息
     */
    private String downloadChannel;
    /**
     * @Fields osVersion 操作系统版本
     */
    private String osVersion;
    /**
     * @Fields termId 终端ID
     */
    private String termId;
    /**
     * @Fields termType 终端类型
     */
    private String termType;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getLoginJrnNo() {
        return loginJrnNo;
    }

    public void setLoginJrnNo(String loginJrnNo) {
        this.loginJrnNo = loginJrnNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getVersions() {
        return versions;
    }

    public void setVersions(String versions) {
        this.versions = versions;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDownloadChannel() {
        return downloadChannel;
    }

    public void setDownloadChannel(String downloadChannel) {
        this.downloadChannel = downloadChannel;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getTermId() {
        return termId;
    }

    public void setTermId(String termId) {
        this.termId = termId;
    }

    public String getTermType() {
        return termType;
    }

    public void setTermType(String termType) {
        this.termType = termType;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}