/*
 * @ClassName IPushParamDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-11 16:37:30
 */
package com.hisun.lemon.cmm.dao;

import com.hisun.lemon.cmm.entity.MessageSendDO;
import com.hisun.lemon.cmm.entity.PushParamDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IPushParamDao extends BaseDao<PushParamDO> {

    PushParamDO getPushParamInfo(@Param("id")String id, @Param("env")String env);
}