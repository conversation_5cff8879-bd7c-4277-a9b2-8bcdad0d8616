package com.hisun.lemon.cmm.controller;

import javax.annotation.Resource;

import com.hisun.lemon.cmm.dto.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.service.IQRCodeService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;

/**
 * @Description 条码及二维码服务
 * <AUTHOR>
 * @date 2017年7月12日 下午2:16:30
 * @version V1.0
 */
@RestController
@RequestMapping("/qrcode")
@Api(tags = "QRCodeController", description = "条码及二维码服务")
public class QRCodeController extends BaseController {

    /**
     * 条码及二维码服务
     */
    @Resource
    private IQRCodeService iqrCodeService;

    @ApiOperation(value = "付款码生成", notes = "付款码生成")
    @ApiResponse(code = 200, message = "生成付款码结果")
    @PostMapping("/payment")
    public GenericRspDTO<PaymentCodeRspDTO> paymentCodeGenerate(@Validated @RequestBody GenericDTO<NoBody> reqDTO) {
        GenericRspDTO<PaymentCodeRspDTO> rspDTO = new GenericRspDTO<PaymentCodeRspDTO>();
        PaymentCodeRspDTO paymentCodeRspDTO = null;
        try {
            paymentCodeRspDTO = iqrCodeService.paymentCodeGenerate();
            rspDTO.setBody(paymentCodeRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "付款码校验", notes = "付款码校验")
    @ApiResponse(code = 200, message = "校验付款码结果")
    @PostMapping("/payment/check")
    public GenericRspDTO<PaymentCodeCheckRspDTO> paymentCodeCheck(@Validated @RequestBody GenericDTO<PaymentCodeCheckReqDTO> reqDTO) {
        GenericRspDTO<PaymentCodeCheckRspDTO> rspDTO = new GenericRspDTO<PaymentCodeCheckRspDTO>();
        PaymentCodeCheckReqDTO paymentCodeCheckReqDTO = reqDTO.getBody();
        PaymentCodeCheckRspDTO paymentCodeCheckRspDTO = null;
        try {
            paymentCodeCheckRspDTO = iqrCodeService.paymentCodeCheck(paymentCodeCheckReqDTO);
            rspDTO.setBody(paymentCodeCheckRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "二维码生成", notes = "二维码生成")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header"),
        @ApiImplicitParam(name = "x-lemon-loginnm", value = "用户登录名", paramType = "header"),
    })
    @ApiResponse(code = 200, message = "生成二维码结果")
    @PostMapping("/generate")
    public GenericRspDTO<QRCodeGenerateRspDTO> qrCodeGenerate(@Validated @RequestBody GenericDTO<QRCodeGenerateReqDTO> reqDTO) {
        GenericRspDTO<QRCodeGenerateRspDTO> rspDTO = new GenericRspDTO<QRCodeGenerateRspDTO>();
        QRCodeGenerateReqDTO generateReqDTO = reqDTO.getBody();
        QRCodeGenerateRspDTO generateRspDTO = null;
        try {
            generateRspDTO = iqrCodeService.qrCodeGenerate(generateReqDTO);
            rspDTO.setBody(generateRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "tms生成商户qrcode", notes = "tms生成商户qrcode")
    @ApiResponse(code = 200, message = "生成二维码结果")
    @PostMapping("/tmsMakeQrcode")
    public GenericRspDTO<TmsMakeQrcodeRspDTO> tmsMakeQrcode(@Validated @RequestBody GenericDTO<TmsMakeQrcodeReqDTO> reqDTO) {
        GenericRspDTO<TmsMakeQrcodeRspDTO> rspDTO = new GenericRspDTO<TmsMakeQrcodeRspDTO>();
        TmsMakeQrcodeReqDTO generateReqDTO = reqDTO.getBody();
        TmsMakeQrcodeRspDTO generateRspDTO = null;
        try {
            generateRspDTO = iqrCodeService.tmsMakeQrcode(generateReqDTO);
            rspDTO.setBody(generateRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "二维码解析", notes = "二维码解析")
    @ApiResponse(code = 200, message = "解析二维码结果")
    @PostMapping("/resolve")
    public GenericRspDTO<QRCodeResolveRspDTO> qrCodeResolve(@Validated @RequestBody GenericDTO<QRCodeResolveReqDTO> reqDTO) {
        GenericRspDTO<QRCodeResolveRspDTO> rspDTO = new GenericRspDTO<QRCodeResolveRspDTO>();
        QRCodeResolveReqDTO qrCodeResolveReqDTO = reqDTO.getBody();
        QRCodeResolveRspDTO rRCodeResolveRspDTO = null;
        try {
            rRCodeResolveRspDTO = iqrCodeService.qrCodeResolve(qrCodeResolveReqDTO);
            rspDTO.setBody(rRCodeResolveRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value = "聚合支付二维码解析", notes = "聚合支付二维码解析")
    @ApiResponse(code = 200, message = "解析聚合支付二维码结果")
    @PostMapping("/aggregate/resolve")
    public GenericRspDTO<QRCodeResolveRspDTO> qrCodeAgResolve(@Validated @RequestBody GenericDTO<QRCodeResolveReqDTO> reqDTO) {
        GenericRspDTO<QRCodeResolveRspDTO> rspDTO = new GenericRspDTO<QRCodeResolveRspDTO>();
        QRCodeResolveReqDTO qrCodeResolveReqDTO = reqDTO.getBody();
        QRCodeResolveRspDTO rRCodeResolveRspDTO = null;
        try {
            rRCodeResolveRspDTO = iqrCodeService.qrCodeResolve(qrCodeResolveReqDTO);
            rspDTO.setBody(rRCodeResolveRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
}
