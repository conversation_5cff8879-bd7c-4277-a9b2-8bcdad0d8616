/*
 * @ClassName MessageTemplateDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 16:49:32
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class MessageTemplateDO extends BaseDO {
    /**
     * @Fields id 消息模版ID
     */
    private String id;
    /**
     * @Fields passPushFlg 是否透传 0:非透传 1:透传
     */
    private String passPushFlg;
    /**
     * @Fields userType 用户类型 common:通用 personal:个人的 merchant:商户的
     */
    private String userType;
    /**
     * @Fields type 消息类型 center:消息中心 pay:支付请求 order:订单结果
     */
    private String type;
    /**
     * @Fields messageTitleKh 柬语消息标题
     */
    private String messageTitleKh;
    /**
     * @Fields messageTitleCn 中文消息标题
     */
    private String messageTitleCn;
    /**
     * @Fields messageTitleEn 英语消息标题
     */
    private String messageTitleEn;
    /**
     * @Fields replaceField 替换变量
     */
    private String replaceField;
    /**
     * @Fields templateContentKh 柬语消息模版内容
     */
    private String templateContentKh;
    /**
     * @Fields templateContentCn 中文消息模版内容
     */
    private String templateContentCn;
    /**
     * @Fields templateContentEn 英语消息模版内容
     */
    private String templateContentEn;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPassPushFlg() {
        return passPushFlg;
    }

    public void setPassPushFlg(String passPushFlg) {
        this.passPushFlg = passPushFlg;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMessageTitleKh() {
        return messageTitleKh;
    }

    public void setMessageTitleKh(String messageTitleKh) {
        this.messageTitleKh = messageTitleKh;
    }

    public String getMessageTitleCn() {
        return messageTitleCn;
    }

    public void setMessageTitleCn(String messageTitleCn) {
        this.messageTitleCn = messageTitleCn;
    }

    public String getMessageTitleEn() {
        return messageTitleEn;
    }

    public void setMessageTitleEn(String messageTitleEn) {
        this.messageTitleEn = messageTitleEn;
    }

    public String getReplaceField() {
        return replaceField;
    }

    public void setReplaceField(String replaceField) {
        this.replaceField = replaceField;
    }

    public String getTemplateContentKh() {
        return templateContentKh;
    }

    public void setTemplateContentKh(String templateContentKh) {
        this.templateContentKh = templateContentKh;
    }

    public String getTemplateContentCn() {
        return templateContentCn;
    }

    public void setTemplateContentCn(String templateContentCn) {
        this.templateContentCn = templateContentCn;
    }

    public String getTemplateContentEn() {
        return templateContentEn;
    }

    public void setTemplateContentEn(String templateContentEn) {
        this.templateContentEn = templateContentEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}