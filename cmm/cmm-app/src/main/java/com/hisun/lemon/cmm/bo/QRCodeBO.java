package com.hisun.lemon.cmm.bo;

import java.math.BigDecimal;

/**
 * @Description 二维码信息传输对象
 * <AUTHOR>
 * @date 2017年7月15日 下午3:00:50
 * @version V1.0
 */
public class QRCodeBO {
    /** 二维码类型 PA:个人账户 MA:商户账户 MO:商户订单 AG:聚合支付*/
    private String qrCodeType;
    /** 内部用户号 */
    private String userId;
    /** 用户登陆名 */
    private String loginNm;
    /** 用户名称 */
    private String userName;
    /** 用户头像 */
    private String headPicUrl;
    /** 交易金额 */
    private BigDecimal tradeAmt;
    /** 交易订单号 */
    private String orderNo;
    /** 备注 */
    private String remark;

    public String getQrCodeType() {
        return qrCodeType;
    }

    public void setQrCodeType(String qrCodeType) {
        this.qrCodeType = qrCodeType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoginNm() {
        return loginNm;
    }

    public void setLoginNm(String loginNm) {
        this.loginNm = loginNm;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getHeadPicUrl() {
        return headPicUrl;
    }

    public void setHeadPicUrl(String headPicUrl) {
        this.headPicUrl = headPicUrl;
    }
}
