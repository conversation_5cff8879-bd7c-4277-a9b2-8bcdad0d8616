/*
 * @ClassName IPhoneSegementDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-27 09:46:30
 */
package com.hisun.lemon.cmm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.cmm.entity.PhoneSegementDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface IPhoneSegementDao extends BaseDao<PhoneSegementDO> {
    
    PhoneSegementDO getByCondition(@Param("countryCode") String countryCode, @Param("mblNo") String mblNo, @Param("numberLength") int numberLength);
    
    List<PhoneSegementDO> getListByCondition(PhoneSegementDO phoneSegementDao);
    
}