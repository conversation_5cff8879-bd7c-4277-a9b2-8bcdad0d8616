package com.hisun.lemon.cmm.service;

import com.hisun.lemon.cmm.rsp.ImageRspDTO;
import com.hisun.lemon.cmm.rsp.ImagesRspDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ImgUploadService {
    /**
     * 上传图片
     *
     * @param file 文件信息
     * @date 2018/9/6 16:36
     * @version V1.0
     * @return: GenericRspDTO<ImageRspDTO>
     */
    GenericRspDTO<ImageRspDTO> imageUpload(MultipartFile file);


    GenericRspDTO<ImagesRspDTO> imagesUpload(List<MultipartFile> file);

}
