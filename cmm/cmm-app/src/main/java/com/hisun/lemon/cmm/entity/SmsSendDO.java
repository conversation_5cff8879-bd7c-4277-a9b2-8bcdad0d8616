/*
 * @ClassName SmsSendDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-18 15:34:43
 */
package com.hisun.lemon.cmm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.hisun.lemon.framework.data.BaseDO;

public class SmsSendDO extends BaseDO {
    /**
     * @Fields smsJrnNo 短信流水号
     */
    private String smsJrnNo;
    /**
     * @Fields tradeDate 交易日期
     */
    private LocalDate tradeDate;
    /**
     * @Fields tradeTime 交易时间
     */
    private LocalTime tradeTime;
    /**
     * @Fields type 短信类型
     */
    private String type;
    /**
     * @Fields lvl 短信级别
     */
    private String lvl;
    /**
     * @Fields mblNo 手机号码
     */
    private String mblNo;
    /**
     * @Fields smsContent 短信内容
     */
    private String smsContent;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getSmsJrnNo() {
        return smsJrnNo;
    }

    public void setSmsJrnNo(String smsJrnNo) {
        this.smsJrnNo = smsJrnNo;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLvl() {
        return lvl;
    }

    public void setLvl(String lvl) {
        this.lvl = lvl;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getSmsContent() {
        return smsContent;
    }

    public void setSmsContent(String smsContent) {
        this.smsContent = smsContent;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}