package com.hisun.lemon.cmm.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.Resource;
import com.hisun.lemon.cmm.dto.*;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cmm.constants.Constants;
import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.dao.IQRCodeParamDao;
import com.hisun.lemon.cmm.dao.IQRCodePaymentDao;
import com.hisun.lemon.cmm.dao.IQRCodeSecretDao;
import com.hisun.lemon.cmm.dao.IQrCodeSegementDao;
import com.hisun.lemon.cmm.entity.QRCodeParamDO;
import com.hisun.lemon.cmm.entity.QRCodePaymentDO;
import com.hisun.lemon.cmm.entity.QRCodeSecretDO;
import com.hisun.lemon.cmm.entity.QrCodeSegementDO;
import com.hisun.lemon.cmm.service.IQRCodeService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.RandomUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.jcommon.cryptology.base.encrption.symmetric.AESCoder;
import com.hisun.lemon.jcommon.qrcode.TOTP;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;

@Transactional
@Service
public class QRCodeServiceImpl extends BaseService implements IQRCodeService {
    
    private static final Logger logger = LoggerFactory.getLogger(QRCodeServiceImpl.class);
    
    @Resource
    private IQRCodeParamDao qrCodeParamDao;
    @Resource
    private IQRCodeSecretDao qrCodeSecretDao;
    @Resource
    private IQRCodePaymentDao qrCodePaymentDao;
    @Resource
    private IQrCodeSegementDao qrCodeSegementDao;
    @Resource
    private UserBasicInfClient userBasicInfClient;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public PaymentCodeRspDTO paymentCodeGenerate() throws LemonException {
        PaymentCodeRspDTO paymentCodeRspDTO = new PaymentCodeRspDTO();
        String userId = LemonUtils.getUserId();
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        LocalDateTime tradeTimeAfter1Minute = tradeTime.plusMinutes(1);
        LocalDateTime longAfterward = DateTimeUtils.parseLocalDateTime(Constants.LONGAFTERWARD);
        String tradeDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
        // 更新付款码(0:初始化 1:已验证 2:已失效)
        QRCodePaymentDO updValueQRCodePaymentDO = new QRCodePaymentDO();
        updValueQRCodePaymentDO.setCodeStats(Constants.CHECK_EXPIRE);
        QRCodePaymentDO updConditionQRCodePaymentDO = new QRCodePaymentDO();
        updConditionQRCodePaymentDO.setUserId(userId);
        updConditionQRCodePaymentDO.setCodeStats(Constants.CHECK_INIT);
        updConditionQRCodePaymentDO.setExpTime(tradeTime);
        int result = qrCodePaymentDao.updateByCondition(updValueQRCodePaymentDO, updConditionQRCodePaymentDO);
        if (result < 0) {
            LemonException.throwBusinessException(MsgCdEnum.RCPQRCODE_GEN_FAIL.getMsgCd());
        }
        // 查询参数信息
        QRCodeParamDO qryOutQRCodeParamDO = qrCodeParamDao.get(Constants.QRCODE_PARAM_ID);
        if (JudgeUtils.isNull(qryOutQRCodeParamDO)) {
            LemonException.throwBusinessException(MsgCdEnum.RCPQRCODE_GEN_FAIL.getMsgCd());
        }
        // 查询密钥信息
        QRCodeSecretDO qryOutQRCodeSecretDO = qrCodeSecretDao.get(userId);
        String barCodeSecret = null;
        String qrCodeSecret = null;
        if (JudgeUtils.isNull(qryOutQRCodeSecretDO)) {
            // 生成条码密钥
            barCodeSecret = RandomUtils.randomNumeric(128);
            // 生成二维码密钥
            qrCodeSecret = RandomUtils.randomNumeric(128);
            QRCodeSecretDO inQRCodeSecretDO = new QRCodeSecretDO();
            inQRCodeSecretDO.setUserId(userId);
            inQRCodeSecretDO.setBarcodeSecretKey(barCodeSecret);
            inQRCodeSecretDO.setQrcodeSecretKey(qrCodeSecret);
            inQRCodeSecretDO.setEffTime(tradeTime);
            inQRCodeSecretDO.setExpTime(longAfterward);
            inQRCodeSecretDO.setCreateTime(tradeTime);
            inQRCodeSecretDO.setModifyTime(tradeTime);
            result = qrCodeSecretDao.insert(inQRCodeSecretDO);
            if (result <= 0) {
                LemonException.throwBusinessException(MsgCdEnum.PAYQRCODE_GEN_FAIL.getMsgCd());
            }
        } else {
            barCodeSecret = qryOutQRCodeSecretDO.getBarcodeSecretKey();
            qrCodeSecret = qryOutQRCodeSecretDO.getQrcodeSecretKey();
        }
        // 付款码格式(付款码前缀(2位) + TOTP口令(8位) + 付款码流水(8位))
        long tradeTimeSeconds = (tradeTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()) / 1000;
        long T = (tradeTimeSeconds - Constants.T0) / Constants.X;
        String steps = Long.toHexString(T).toUpperCase();
        while (steps.length() < 16) {
            steps = "0" + steps;
        }
        // 付款码前缀
        String paymentCodePrefix = qryOutQRCodeParamDO.getPaymentCodePrefix();
        // 付款条流水号
        String barCodeJrn = RandomUtils.randomNumeric(8);
        // 生成时间一次性口令
        String barCodeToken = TOTP.generateTOTP(barCodeSecret, steps, Constants.TOKENLEN, Constants.HMACSHA512);
        // 生成付款条码
        StringBuilder barCoderBuilder = new StringBuilder();
        barCoderBuilder.append(paymentCodePrefix).append(barCodeToken).append(barCodeJrn);
        String payBarCode = barCoderBuilder.toString();
        // 登记付款条码
        QRCodePaymentDO inBarCodePaymentDO = new QRCodePaymentDO();
        String barPayCodeJrnRandom = IdGenUtils.generateId("CMM_ORDER_NO", 8);
        inBarCodePaymentDO.setCodeJrnNo(LemonUtils.getApplicationName() + tradeDateTimeStr + barPayCodeJrnRandom);
        inBarCodePaymentDO.setUserId(userId);
        inBarCodePaymentDO.setCodeInfo(payBarCode);
        inBarCodePaymentDO.setType(Constants.BARCODE);
        inBarCodePaymentDO.setEffTime(tradeTime);
        inBarCodePaymentDO.setExpTime(tradeTimeAfter1Minute);
        inBarCodePaymentDO.setCreateTime(tradeTime);
        inBarCodePaymentDO.setModifyTime(tradeTime);
        result = qrCodePaymentDao.insert(inBarCodePaymentDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.PAYQRCODE_GEN_FAIL.getMsgCd());
        }
        paymentCodeRspDTO.setPayBarCode(payBarCode);
        // 付款二维码流水号
        String qrCodeJrn = RandomUtils.randomNumeric(8);
        // 生成时间一次性口令
        String qrCodeToken = TOTP.generateTOTP(qrCodeSecret, steps, Constants.TOKENLEN, Constants.HMACSHA512);
        // 生成付款条码
        StringBuilder qrCoderBuilder = new StringBuilder();
        qrCoderBuilder.append(paymentCodePrefix).append(qrCodeToken).append(qrCodeJrn);
        String payQRCode = qrCoderBuilder.toString();
        // 登记付款二维码
        QRCodePaymentDO inQRCodePaymentDO = new QRCodePaymentDO();
        String payCodeJrnRandom = IdGenUtils.generateId("CMM_ORDER_NO", 8);
        inQRCodePaymentDO.setCodeJrnNo(LemonUtils.getApplicationName() + tradeDateTimeStr + payCodeJrnRandom);
        inQRCodePaymentDO.setUserId(userId);
        inQRCodePaymentDO.setCodeInfo(payQRCode);
        inQRCodePaymentDO.setType(Constants.QRCODE);
        inQRCodePaymentDO.setEffTime(tradeTime);
        inQRCodePaymentDO.setExpTime(tradeTimeAfter1Minute);
        inQRCodePaymentDO.setCreateTime(tradeTime);
        inQRCodePaymentDO.setModifyTime(tradeTime);
        result = qrCodePaymentDao.insert(inQRCodePaymentDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.PAYQRCODE_GEN_FAIL.getMsgCd());
        }
        paymentCodeRspDTO.setPayQRCode(payQRCode);
        return paymentCodeRspDTO;
    }

    @Override
    public PaymentCodeCheckRspDTO paymentCodeCheck(PaymentCodeCheckReqDTO paymentCodeCheckReqDTO)
            throws LemonException {
        PaymentCodeCheckRspDTO paymentCodeCheckRspDTO = new PaymentCodeCheckRspDTO();
        String paymentCode = paymentCodeCheckReqDTO.getPaymentCode();
        String prefixNumber = paymentCode.substring(0, 2);
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        // 判断付款方式
        QrCodeSegementDO qryInQrCodeSegementDO = new QrCodeSegementDO();
        qryInQrCodeSegementDO.setPrefixNumber(prefixNumber);
        QrCodeSegementDO qryOutQrCodeSegementDO = qrCodeSegementDao.getByCondition(qryInQrCodeSegementDO);
        if (JudgeUtils.isNull(qryOutQrCodeSegementDO)) {
            LemonException.throwBusinessException(MsgCdEnum.PAYQRCODE_RESOLVE_FAIL.getMsgCd());
        }
        String payType = qryOutQrCodeSegementDO.getPayType();
        logger.info("payType:" + payType + ", Constant:" + Constants.PAYTYPE_SEATEL);
        if (payType.equals(Constants.PAYTYPE_SEATEL)) {
            // 查询付款码
            QRCodePaymentDO qryInQRCodePaymentDO = new QRCodePaymentDO();
            qryInQRCodePaymentDO.setCodeInfo(paymentCode);
            qryInQRCodePaymentDO.setCodeStats(Constants.CHECK_INIT);
            qryInQRCodePaymentDO.setExpTime(tradeTime);
            QRCodePaymentDO qryOutQrCodePaymentDO = qrCodePaymentDao.getByCondition(qryInQRCodePaymentDO);
            if (JudgeUtils.isNull(qryOutQrCodePaymentDO)) {
                LemonException.throwBusinessException(MsgCdEnum.PAYQRCODE_RESOLVE_ERROR.getMsgCd());
            }
            // 更新付款码(0:初始化 1:已验证 2:已失效)
            QRCodePaymentDO updValueQRCodePaymentDO = new QRCodePaymentDO();
            updValueQRCodePaymentDO.setCodeStats(Constants.CHECK_VERIFIED);
            QRCodePaymentDO updConditionQRCodePaymentDO = new QRCodePaymentDO();
            updConditionQRCodePaymentDO.setCodeInfo(paymentCode);
            updConditionQRCodePaymentDO.setCodeStats(Constants.CHECK_INIT);
            updConditionQRCodePaymentDO.setExpTime(tradeTime);
            int result = qrCodePaymentDao.updateByCondition(updValueQRCodePaymentDO, updConditionQRCodePaymentDO);
            if (result <= 0) {
                LemonException.throwBusinessException(MsgCdEnum.PAYQRCODE_RESOLVE_ERROR.getMsgCd());
            }
            String paymentUserId = qryOutQrCodePaymentDO.getUserId();
            paymentCodeCheckRspDTO.setPaymentUserId(paymentUserId);
        }
        paymentCodeCheckRspDTO.setPaymentType(payType);
        paymentCodeCheckRspDTO.setPaymentCode(paymentCode);
        return paymentCodeCheckRspDTO;
    }

    @Override
    public QRCodeGenerateRspDTO qrCodeGenerate(QRCodeGenerateReqDTO generateReqDTO) throws LemonException {
        QRCodeGenerateRspDTO generateRspDTO = new QRCodeGenerateRspDTO();
        String qrCodeType = generateReqDTO.getQrCodeType();
        String userId = LemonUtils.getUserId();
        String loginNm = LemonUtils.getLoginName();
        String headPirc = "";


        String orderNo = generateReqDTO.getOrderNo();
        BigDecimal tradeAmt = generateReqDTO.getTradeAmt();
        String remark = generateReqDTO.getRemark();
        // 查询参数信息
        QRCodeParamDO qryOutQRCodeParamDO = qrCodeParamDao.get(Constants.QRCODE_PARAM_ID);
        if (JudgeUtils.isNull(qryOutQRCodeParamDO)) {
            LemonException.throwBusinessException(MsgCdEnum.RCPQRCODE_GEN_FAIL.getMsgCd());
        }
        // 聚合支付url信息
        String aggregateUrl = qryOutQRCodeParamDO.getAggregateUrl();
        // 二维码参数密钥信息
        String paramEncriptKeyStr = qryOutQRCodeParamDO.getTkey();
        // 二维码信息builder
        StringBuilder qrCodeUrlBuilder = new StringBuilder();
        // 二维码参数信息builder
        StringBuilder paramBuilder = new StringBuilder();
        if (qrCodeType.equals(Constants.MERCHANT_ORDER)) {
            // 商户订单二维码
            qrCodeUrlBuilder.append(Constants.MERCHANT_ORDER);
            paramBuilder.append(orderNo);
        } else {
            qrCodeUrlBuilder.append(aggregateUrl);
            if (qrCodeType.equals(Constants.PERSONAL_ACCOUNT)) {
                // 用户帐户二维码
                qrCodeUrlBuilder.append(Constants.PERSONAL_ACCOUNT);
            } else if (qrCodeType.equals(Constants.MERCHANT_ACCOUNT)) {
                // 商户账户二维码
                qrCodeUrlBuilder.append(Constants.MERCHANT_ACCOUNT);
            } else {
                // 聚合支付二维码
                qrCodeUrlBuilder.append(Constants.AGGREGATE);
            }
            //获取用户信息
            GenericRspDTO<UserBasicInfDTO> genericUserBasicInfDTO = userBasicInfClient.queryUserByLoginId(loginNm);
            if(JudgeUtils.isSuccess(genericUserBasicInfDTO.getMsgCd())){
                headPirc = genericUserBasicInfDTO.getBody().getAvatarPath();
            }
            paramBuilder.append(userId);
            paramBuilder.append(Constants.QRCODE_PARAM_SEPARATOR).append(loginNm);
            paramBuilder.append(Constants.QRCODE_PARAM_SEPARATOR).append(headPirc);
            // 金额信息
            if (JudgeUtils.isNotNull(tradeAmt)) {
                paramBuilder.append(Constants.QRCODE_PARAM_SEPARATOR).append(tradeAmt);
            }
            // 备注信息
            if (StringUtils.isNotBlank(remark)) {
                paramBuilder.append(Constants.QRCODE_PARAM_SEPARATOR).append(remark);
            }
        }
        // AES加密后UrlBase64处理
        try {
            String paramStr = paramBuilder.toString();
            byte[] paramEncriptKey = Base64.decodeBase64(paramEncriptKeyStr);
            byte[] paramStrAesEncrypt = AESCoder.encrypt(paramStr.getBytes("UTF-8"), paramEncriptKey);
            String paramStrBase64 = Base64.encodeBase64URLSafeString(paramStrAesEncrypt);
            generateRspDTO.setQRCode(qrCodeUrlBuilder.append(paramStrBase64).toString());
        } catch (Exception e) {
            LemonException.throwBusinessException(MsgCdEnum.RCPQRCODE_GEN_FAIL.getMsgCd());
        }
        return generateRspDTO;
    }

    @Override
    public QRCodeResolveRspDTO qrCodeResolve(QRCodeResolveReqDTO qrCodeResolveReqDTO) throws LemonException {
        QRCodeResolveRspDTO qrCodeResolveRspDTO = new QRCodeResolveRspDTO();
        String urlParams = qrCodeResolveReqDTO.getUrlParams();
        // 查询参数信息
        QRCodeParamDO qryOutQRCodeParamDO = qrCodeParamDao.get(Constants.QRCODE_PARAM_ID);
        if (JudgeUtils.isNull(qryOutQRCodeParamDO)) {
            LemonException.throwBusinessException(MsgCdEnum.RCPQRCODE_RESOLVE_ERROR.getMsgCd());
        }
        // 二维码参数密钥信息
        String paramEncriptKeyStr = qryOutQRCodeParamDO.getTkey();
        String params = urlParams;
        if (urlParams.indexOf(Constants.QRCODE_URLPARAM_SEPARATOR) != -1) {
            params = urlParams.substring(urlParams.indexOf(Constants.QRCODE_URLPARAM_SEPARATOR) + 1);
        } 
        String qrCodeType = params.substring(0, 2);
        String paramStrBase64 = params.substring(2);
        // UrlBase64解码AES解密
        try {
            byte[] paramEncriptKey = Base64.decodeBase64(paramEncriptKeyStr);
            byte[] paramStrAesDecrypt = Base64.decodeBase64(paramStrBase64);
            String headPircture = "";
            params = new String(AESCoder.decrypt(paramStrAesDecrypt, paramEncriptKey));
            if (qrCodeType.equals(Constants.MERCHANT_ORDER)) {
                // 商户订单二维码
                qrCodeResolveRspDTO.setOrderNo(params);
            } else {
                String[] paramsArray = params.split(Constants.QRCODE_RESOLVE_SEPARATOR);
                String userId = paramsArray[0];
                String loginName = paramsArray[1];

                if (qrCodeType.equals(Constants.MERCHANT_ACCOUNT)) {
                    if(paramsArray.length == 3){
                        headPircture = paramsArray[2];
                    }
                }
                qrCodeResolveRspDTO.setUserId(userId);
                qrCodeResolveRspDTO.setLoginNm(loginName);
                qrCodeResolveRspDTO.setHeadPicUrl(headPircture);
                if (paramsArray.length == 4) {
                    qrCodeResolveRspDTO.setTradeAmt(BigDecimal.valueOf(Double.parseDouble(paramsArray[3])));
                } else if (paramsArray.length == 5) {
                    qrCodeResolveRspDTO.setTradeAmt(BigDecimal.valueOf(Double.parseDouble(paramsArray[3])));
                    qrCodeResolveRspDTO.setRemark(paramsArray[4]);
                }
                // 查询用户基本信息
                UserBasicInfDTO userBasicInfDTO = getUserBasicInfo(userId);
                String extendQueryId = userBasicInfDTO.getMblNo();
                if (qrCodeType.equals(Constants.PERSONAL_ACCOUNT)) {
                    qrCodeResolveRspDTO.setUserName(userBasicInfDTO.getUsrNm());
                } else {
                    extendQueryId = loginName; 
                    qrCodeResolveRspDTO.setUserName(userBasicInfDTO.getMercName());
                }
                // 查询用户扩展信息
                UserBasicInfDTO userBasicInfDTO2 = getUserExtendInfo(extendQueryId);
                qrCodeResolveRspDTO.setHeadPicUrl(userBasicInfDTO2.getAvatarPath());
            }
            qrCodeResolveRspDTO.setQrCodeType(qrCodeType);
        } catch (Exception e) {
            LemonException.throwBusinessException(MsgCdEnum.RCPQRCODE_RESOLVE_ERROR.getMsgCd());
        }
        return qrCodeResolveRspDTO;
    }

    @Override
    public TmsMakeQrcodeRspDTO tmsMakeQrcode(TmsMakeQrcodeReqDTO generateReqDTO) {
        {
            TmsMakeQrcodeRspDTO generateRspDTO = new TmsMakeQrcodeRspDTO();
            String qrCodeType = generateReqDTO.getQrCodeType();
            String userId = generateReqDTO.getUserId();
            String loginNm = generateReqDTO.getLoginId();
            String remark = generateReqDTO.getRemark();
            String headPirc = "";
            // 查询参数信息
            QRCodeParamDO qryOutQRCodeParamDO = qrCodeParamDao.get(Constants.QRCODE_PARAM_ID);
            if (JudgeUtils.isNull(qryOutQRCodeParamDO)) {
                LemonException.throwBusinessException(MsgCdEnum.RCPQRCODE_GEN_FAIL.getMsgCd());
            }

            //获取用户信息
            GenericRspDTO<UserBasicInfDTO> genericUserBasicInfDTO = userBasicInfClient.queryUserByLoginId(loginNm);
            if(JudgeUtils.isSuccess(genericUserBasicInfDTO.getMsgCd())){
                headPirc = genericUserBasicInfDTO.getBody().getAvatarPath();
            }

            // 聚合支付url信息
            String aggregateUrl = qryOutQRCodeParamDO.getAggregateUrl();
            // 二维码参数密钥信息
            String paramEncriptKeyStr = qryOutQRCodeParamDO.getTkey();
            // 二维码信息builder
            StringBuilder qrCodeUrlBuilder = new StringBuilder();
            // 二维码参数信息builder
            StringBuilder paramBuilder = new StringBuilder();
            qrCodeUrlBuilder.append(aggregateUrl);
            qrCodeUrlBuilder.append(Constants.MERCHANT_ACCOUNT);
            paramBuilder.append(userId);
            paramBuilder.append(Constants.QRCODE_PARAM_SEPARATOR).append(loginNm);
            paramBuilder.append(Constants.QRCODE_PARAM_SEPARATOR).append(headPirc);

            // 备注信息
            if (StringUtils.isNotBlank(remark)) {
                paramBuilder.append(Constants.QRCODE_PARAM_SEPARATOR).append(remark);
            }
            // AES加密后UrlBase64处理
            try {
                String paramStr = paramBuilder.toString();
                byte[] paramEncriptKey = Base64.decodeBase64(paramEncriptKeyStr);
                byte[] paramStrAesEncrypt = AESCoder.encrypt(paramStr.getBytes("UTF-8"), paramEncriptKey);
                String paramStrBase64 = Base64.encodeBase64URLSafeString(paramStrAesEncrypt);
                generateRspDTO.setQRCode(qrCodeUrlBuilder.append(paramStrBase64).toString());
            } catch (Exception e) {
                LemonException.throwBusinessException(MsgCdEnum.RCPQRCODE_GEN_FAIL.getMsgCd());
            }
            return generateRspDTO;
        }
    }

    // 用户基本信息查询
    private UserBasicInfDTO getUserBasicInfo(String userId) throws LemonException{
        GenericRspDTO<UserBasicInfDTO> rspDTO = userBasicInfClient.queryUser(userId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 用户基本信息查询失败
            if (logger.isDebugEnabled()) {
                logger.debug("user :" + userId + " basic info query failure!");
            }
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }
    
    // 用户扩展信息查询
    private UserBasicInfDTO getUserExtendInfo(String loginId) throws LemonException{
        GenericRspDTO<UserBasicInfDTO> rspDTO = userBasicInfClient.queryUserByLoginId(loginId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            // 用户扩展信息查询失败
            if (logger.isDebugEnabled()) {
                logger.debug("user :" + loginId + " extend info query failure!");
            }
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }
}