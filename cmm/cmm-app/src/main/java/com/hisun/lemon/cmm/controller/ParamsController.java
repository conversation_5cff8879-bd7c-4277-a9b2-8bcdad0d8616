package com.hisun.lemon.cmm.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hisun.lemon.cmm.dto.ConstantParamListReqDTO;
import com.hisun.lemon.cmm.dto.ConstantParamReqDTO;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.cmm.entity.ConstantParamDo;
import com.hisun.lemon.cmm.service.IConstantParamService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;

/**
 * 公共模块
 *
 * <AUTHOR>
 * @create 2017/7/5
 */
@Api(tags = "ParamsController", description = "公共参数")
@RestController
@RequestMapping(value = "/cmm-service")
public class ParamsController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ParamsController.class);

    @Resource
    private IConstantParamService constantParamService;

    @ApiOperation(value = "常量参数新增", notes = "常量参数新增")
    @ApiResponse(code = 200, message = "常量参数新增操作返回")
    @PostMapping(value = "/params")
    public GenericRspDTO<NoBody> paramsAdd(@Validated @RequestBody GenericDTO<ConstantParamReqDTO> reqDTO) {
        GenericRspDTO<NoBody> genericDTO = GenericRspDTO.newSuccessInstance();
        ConstantParamReqDTO constantParamReqDTO = reqDTO.getBody();
        ConstantParamDo constantParamDo = new ConstantParamDo();
        BeanUtils.copyProperties(constantParamDo, constantParamReqDTO);
        logger.debug(constantParamDo.toString());
        try {
            constantParamService.addConstantParam(constantParamDo);
        } catch (LemonException e) {
            genericDTO.setMsgCd(e.getMsgCd());
        }
        return genericDTO;
    }

    @ApiOperation(value = "常量参数删除", notes = "常量参数删除")
    @ApiResponse(code = 200, message = "常量参数删除操作返回")
    @DeleteMapping(value = "/params")
    public GenericRspDTO<NoBody> paramsDel(@RequestBody GenericDTO<String> reqDTO) {
        GenericRspDTO<NoBody> genericDTO = GenericRspDTO.newSuccessInstance();
        String parmNm = reqDTO.getBody();
        try {
            constantParamService.deleteConstantParam(parmNm);
        } catch (LemonException e) {
            genericDTO.setMsgCd(e.getMsgCd());
        }
        return genericDTO;
    }

    @ApiOperation(value = "常量参数修改", notes = "常量参数修改")
    @ApiResponse(code = 200, message = "常量参数修改操作返回")
    @PutMapping(value = "/params")
    public GenericRspDTO<NoBody> paramsUpd(@Validated @RequestBody GenericDTO<ConstantParamReqDTO> reqDTO) {
        GenericRspDTO<NoBody> genericDTO = GenericRspDTO.newSuccessInstance();
        ConstantParamReqDTO constantParamReqDTO = reqDTO.getBody();
        ConstantParamDo constantParamDo = new ConstantParamDo();
        BeanUtils.copyProperties(constantParamDo, constantParamReqDTO);
        try {
            constantParamService.updateConstantParam(constantParamDo);
        } catch (LemonException e) {
            genericDTO.setMsgCd(e.getMsgCd());
        }
        return genericDTO;
    }

    @ApiOperation(value = "常量参数查询", notes = "常量参数查询")
    @ApiResponse(code = 200, message = "常量参数查询操作返回")
    @GetMapping(value = "/params/{parmNm}")
    public GenericRspDTO<ConstantParamRspDTO> params(@PathVariable(value = "parmNm") String parmNm) {
        GenericRspDTO<ConstantParamRspDTO> genericDTO = new GenericRspDTO<>();
        ConstantParamRspDTO rspDTO = new ConstantParamRspDTO();
        try {
            ConstantParamDo constantParamDo = constantParamService.queryConstantParam(parmNm);
            if (JudgeUtils.isNotNull(constantParamDo)){
                BeanUtils.copyProperties(rspDTO, constantParamDo);
            }
            genericDTO.setBody(rspDTO);
        } catch (LemonException e) {
            genericDTO.setMsgCd(e.getMsgCd());
            return genericDTO;
        }
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        return genericDTO;
    }

    @ApiOperation(value = "常量参数列表查询", notes = "常量参数列表查询")
    @ApiResponse(code = 200, message = "常量参数列表查询操作返回")
    @GetMapping(value = "/params")
    public GenericRspDTO<List<ConstantParamRspDTO>> allParams(@Validated @RequestBody GenericDTO<ConstantParamListReqDTO> reqDTO) {
        GenericRspDTO<List<ConstantParamRspDTO>> genericDTO = new GenericRspDTO<>();
        List<ConstantParamRspDTO> dtoList = new ArrayList<>();
        ConstantParamListReqDTO constantParamListReqDTO = reqDTO.getBody();
        int pageNum = constantParamListReqDTO.getPageNum();
        int pageSize = constantParamListReqDTO.getPageSize();
        try {
            List<ConstantParamDo> doList;
            doList = constantParamService.queryAllConstantParams(pageNum, pageSize);
            for (ConstantParamDo constantParamDo : doList) {
                ConstantParamRspDTO constantParamRspDTO = new ConstantParamRspDTO();
                BeanUtils.copyProperties(constantParamRspDTO, constantParamDo);
                dtoList.add(constantParamRspDTO);
            }
        } catch (LemonException e) {
            genericDTO.setMsgCd(e.getMsgCd());
        }
        genericDTO.setBody(dtoList);
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        return genericDTO;
    }

    @ApiOperation(value = "常量参数组查询", notes = "常量参数组查询")
    @ApiResponse(code = 200, message = "常量参数列表查询操作返回")
    @GetMapping(value = "/params/group/{parmNm}")
    public GenericRspDTO<List<ConstantParamRspDTO>> paramsGroup(@PathVariable(value = "parmNm") String parmNm) {
        GenericRspDTO<List<ConstantParamRspDTO>> genericDTO = new GenericRspDTO<>();
        List<ConstantParamRspDTO> dtoList = new ArrayList<>();
        List<ConstantParamDo> doList;
        try {
            doList = constantParamService.queryConstantParamGroup(parmNm);
            for (ConstantParamDo constantParamDo : doList) {
                ConstantParamRspDTO constantParamRspDTO = new ConstantParamRspDTO();
                BeanUtils.copyProperties(constantParamRspDTO, constantParamDo);
                dtoList.add(constantParamRspDTO);
            }
        } catch (LemonException e) {
            genericDTO.setMsgCd(e.getMsgCd());
        }
        genericDTO.setBody(dtoList);
        genericDTO.setMsgCd(LemonUtils.getApplicationName() + JudgeUtils.successfulMsgCode);
        return genericDTO;
    }
}
