package com.hisun.lemon.cmm.controller;

//import com.hisun.lemon.cmm.api.wechat.WeChatPayApi;
import com.hisun.lemon.cmm.dto.PayWeChatInfoRspDTO;
import com.hisun.lemon.cmm.dto.PayWeChatReqDTO;
import com.hisun.lemon.cmm.service.IPayMentService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * Created by Administrator on 2017/11/13.
 */
@RestController
@RequestMapping("/cmm")
@Api(tags = "GrantController", description = "公共授权服务")
public class GrantController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(GrantController.class);

    @Resource
    IPayMentService iPayMentService;


    @ApiOperation(value = "微信授权回调", notes = "微信授权回调")
    @ApiResponse(code = 200, message = "微信授权回调结果")
    @PostMapping(value = "/grant")
    public GenericRspDTO<NoBody> getGrant(@Validated @RequestBody GenericDTO<PayWeChatReqDTO> reqDTO) {
        PayWeChatReqDTO payWeChatReqDTO = reqDTO.getBody();
        String code = payWeChatReqDTO.getCode();
        String state = payWeChatReqDTO.getState();
        logger.info("回调 code : " + code);
        if(code == null || "".equals(code)){
            return GenericRspDTO.newSuccessInstance();
        }
        iPayMentService.weChatGrantInfo(code, state);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "微信授权信息查询", notes = "微信授权信息查询")
    @ApiResponse(code = 200, message = "微信授权信息返回")
    @GetMapping(value = "/getGrantInfo/{state}")
    public GenericRspDTO<PayWeChatInfoRspDTO> getGrantInfo(@PathVariable("state") String state) {
        return iPayMentService.getWeChatGrantInfo(state);
    }

}
