package com.hisun.lemon.cmm.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.hisun.lemon.cmm.dao.*;
import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.cmm.entity.*;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.jcommon.messagepush.MessagePushUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cmm.bo.MessagePushBO;
import com.hisun.lemon.cmm.constants.Constants;
import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.service.ICommonService;
import com.hisun.lemon.cmm.utils.SmsTemplateResolveUtil;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.gateway.dto.AppInfoDTO;
import com.hisun.lemon.jcommon.encrypt.EncryptionUtils;
import com.hisun.lemon.jcommon.exception.EncryptException;
import com.hisun.lemon.jcommon.phonenumber.PhoneNumberUtils;

@Transactional
@Service
public class CommonServiceImpl extends BaseService implements ICommonService {
    private static final Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);

    @Resource
    private Environment env;
    @Resource
    private IKeyParamDao keyParamDao;
    @Resource
    private IPhoneSegementDao phoneSegementDao;
    @Resource
    private IMessageTemplateDao messageTemplateDao;
    @Resource
    private IMessageSendDao messageSendDao;
    @Resource
    private INoticPulblicDao noticPulblicDao;
    @Resource
    private IBannerPulblicDao bannerPulblicDao;
    @Resource
    private ICampaignPublicDao campaignPublicDao;
    @Resource
    private IPushClientDao pushClientDao;
    @Resource
    private IPushParamDao pushParamDao;
    @Resource
    private IAppInfoDao appInfoDao;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private ICmmAppvnoinfDao cmmAppvnoinfDao;

    @Override
    public SegementCheckRspDTO phoneSegementCheck(SegementCheckReqDTO segementCheckReqDTO) throws LemonException {
        SegementCheckRspDTO segementCheckRspDTO = new SegementCheckRspDTO();
        String phoneNumber = segementCheckReqDTO.getMblNo();
        String mblNo = PhoneNumberUtils.getPhoneNumber(phoneNumber);
        String countryCode = PhoneNumberUtils.getCountryCode(phoneNumber);
        // 检查手机号段表是否存在
        PhoneSegementDO phoneSegementDO = phoneSegementDao.getByCondition(countryCode, mblNo, mblNo.length());
        String carrier = "";
        if (JudgeUtils.isNotNull(phoneSegementDO)) {
            carrier = phoneSegementDO.getCarrier();
        }
        segementCheckRspDTO.setCarrier(carrier);
        return segementCheckRspDTO;
    }


    @Override
    public void phoneNumberCheck(SegementCheckReqDTO segementCheckReqDTO) throws LemonException {
        String phoneNumber = segementCheckReqDTO.getMblNo();
        // 检查手机号段表是否存在
        String mblNo = PhoneNumberUtils.getPhoneNumber(phoneNumber);
        String countryCode = PhoneNumberUtils.getCountryCode(phoneNumber);
        PhoneSegementDO phoneSegementDO = phoneSegementDao.getByCondition(countryCode, mblNo, mblNo.length());
        if (JudgeUtils.isNull(phoneSegementDO)) {
            // 手机区号为855，则返回手机号码不合法
            if(Constants.COUNTRY_CODE_855.equals(countryCode)) {
                LemonException.throwBusinessException(MsgCdEnum.PHONENUMBER_INVALID.getMsgCd());
            }
            // 手机号段表不存在com.google.i18n.phonenumbers.PhoneNumberUtil校验
            else if(!PhoneNumberUtils.isValidNumber(phoneNumber)) {
                LemonException.throwBusinessException(MsgCdEnum.PHONENUMBER_INVALID.getMsgCd());
            }
        }
    }

    @Override
    public NoticeListRspDTO noticeList() throws LemonException {
        NoticeListRspDTO noticeListRspDTO = new NoticeListRspDTO();
        String channel = LemonUtils.getChannel();
        List<NoticeRspDTO> recordList = new ArrayList<NoticeRspDTO>();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        String language = LemonUtils.getLocale().getLanguage();

        // 查询公告信息
        NoticPulblicDO qryIntNoticPulblicDO = new NoticPulblicDO();
        qryIntNoticPulblicDO.setEffDate(tradeDate);
        qryIntNoticPulblicDO.setStats(Constants.STATUS_AVAILABLE);
        //判断终端
        if(channel != null || !"".equals(channel)){
            if(Constants.A_CHANNEL_USER.equals(channel) || Constants.I_CHANNEL_USER.equals(channel)){
                qryIntNoticPulblicDO.setChannel("app");
            }
            if(Constants.A_CHANNEL_MERC.equals(channel) || Constants.I_CHANNEL_MERC.equals(channel)){
                qryIntNoticPulblicDO.setChannel("mapp");
            }
        }
        List<NoticPulblicDO> noticPulblicDOs = noticPulblicDao.getListByCondition(qryIntNoticPulblicDO);
        if (JudgeUtils.isEmpty(noticPulblicDOs)) {
            noticeListRspDTO.setRecordNumber(0);
            noticeListRspDTO.setRecordList(recordList);
            return noticeListRspDTO;
        }
        //语言过滤
        List<NoticeRspDTO> noticeRspDTOList = noticPulblicDOs.stream().map(temp -> {
            NoticeRspDTO noticeRspDTO = new NoticeRspDTO();
            String noticeId = temp.getId();
            String noticeTitle = temp.getNoticeContentEn();
            String noticeContent = temp.getNoticeContentEn();
            if (language.equals(Constants.LANG_ZH)) {
                noticeTitle = temp.getNoticeTitleCn();
                noticeContent = temp.getNoticeContentCn();
            }else if (language.equals(Constants.LANG_EN)) {
                noticeTitle = temp.getNoticeTitleEn();
                noticeContent = temp.getNoticeContentEn();
            }else {
                noticeTitle = temp.getNoticeTitleKh();
                noticeContent = temp.getNoticeContentKh();
            }
            noticeRspDTO.setNoticeId(noticeId);
            noticeRspDTO.setNoticeTitle(noticeTitle);
            noticeRspDTO.setNoticeContent(noticeContent);
            return noticeRspDTO;
        }).collect(Collectors.toCollection(ArrayList::new));
        noticeListRspDTO.setRecordNumber(noticeRspDTOList.size());
        noticeListRspDTO.setRecordList(noticeRspDTOList);
        return noticeListRspDTO;
    }

    @Override
    public BannerListRspDTO bannerList() throws LemonException {
        BannerListRspDTO bannerListRspDTO = new BannerListRspDTO();
        String language = LemonUtils.getLocale().getLanguage();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        // 查询Banner列表
        BannerPulblicDO qryInBannerPulblicDO = new BannerPulblicDO();
        qryInBannerPulblicDO.setEffDate(tradeDate);
        qryInBannerPulblicDO.setStats(Constants.STATUS_AVAILABLE);
        List<BannerPulblicDO> BannerPulblicDOs = bannerPulblicDao.getListByCondition(qryInBannerPulblicDO);
        if (JudgeUtils.isEmpty(BannerPulblicDOs)) {
            bannerListRspDTO.setRecordNumber(0);
            bannerListRspDTO.setRecordList(new ArrayList<BannerRspDTO>());
            return bannerListRspDTO;
        }
        List<BannerRspDTO> bannerRspDTOs = BannerPulblicDOs.stream().map(temp -> {
            BannerRspDTO bannerRspDTO = new BannerRspDTO();
            bannerRspDTO.setId(temp.getId());
            // 语种转换
            String bannerUrl = temp.getBannerUrlEn();
            String detailUrl = temp.getDetailUrlEn();
            if (language.equals(Constants.LANG_ZH)) {
                bannerUrl = temp.getBannerUrlCn();
                detailUrl = temp.getDetailUrlCn();
            }else if (language.equals(Constants.LANG_EN)) {
                bannerUrl = temp.getBannerUrlEn();
                detailUrl = temp.getDetailUrlEn();
            } else {
                bannerUrl = temp.getBannerUrlKh();
                detailUrl = temp.getDetailUrlKh();
            }
            bannerRspDTO.setBannerUrl(bannerUrl);
            bannerRspDTO.setDetailUrl(detailUrl);
            return bannerRspDTO;
        }).collect(Collectors.toCollection(ArrayList::new));
        bannerListRspDTO.setRecordList(bannerRspDTOs);
        bannerListRspDTO.setRecordNumber(bannerRspDTOs.size());
        return bannerListRspDTO;
    }

    @Override
    public CampaignListRspDTO campaignList(CampaignListReqDTO campaignListReqDTO) throws LemonException {
        CampaignListRspDTO campaignListRspDTO = new CampaignListRspDTO();
        String language = LemonUtils.getLocale().getLanguage();
        String channel = LemonUtils.getChannel();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        // 活动列表
        CampaignPublicDO qryiNCampaignPublicDO = new CampaignPublicDO();
        //判断终端
        if(channel != null || !"".equals(channel)){
            if(Constants.A_CHANNEL_USER.equals(channel) || Constants.I_CHANNEL_USER.equals(channel)){
                qryiNCampaignPublicDO.setChannel("app");
            }
            if(Constants.A_CHANNEL_MERC.equals(channel) || Constants.I_CHANNEL_MERC.equals(channel)){
                qryiNCampaignPublicDO.setChannel("mapp");
            }
        }
        qryiNCampaignPublicDO.setEffDate(tradeDate);
        qryiNCampaignPublicDO.setStats(Constants.STATUS_AVAILABLE);
        List<CampaignPublicDO> campaignPublicDOs = PageUtils.pageQuery(campaignListReqDTO.getPageNum(),
                campaignListReqDTO.getPageSize(), false, () -> {
                    return this.campaignPublicDao.getListByCondition(qryiNCampaignPublicDO);
                });
        if (JudgeUtils.isNull(campaignPublicDOs)) {
            campaignListRspDTO.setRecordNumber(0);
            campaignListRspDTO.setRecordList(new ArrayList<CampaignDTO>());
            return campaignListRspDTO;
        }
        List<CampaignPublicDO> campaignPublicList = this.campaignPublicDao.getListByCondition(qryiNCampaignPublicDO);

        List<CampaignDTO> campaignDTOs = campaignPublicDOs.stream().map(temp -> {
            CampaignDTO campaignDTO = new CampaignDTO();
            // 语种转换
            String title = temp.getCampaignTitleEn();
            String content = temp.getCampaignContentEn();
            if (language.equals(Constants.LANG_ZH)) {
                title = temp.getCampaignTitleCn();
                content = temp.getCampaignContentCn();
            }else if (language.equals(Constants.LANG_EN)) {
                title = temp.getCampaignTitleEn();
                content = temp.getCampaignContentEn();
            } else {
                title = temp.getCampaignTitleKh();
                content = temp.getCampaignContentKh();
            }
            campaignDTO.setId(temp.getId());
            campaignDTO.setTitle(title);
            campaignDTO.setContent(content);
            campaignDTO.setType(Constants.CAMPAIGN);
            campaignDTO.setUserType(Constants.USER_TYPE_PERSONAL);
            campaignDTO.setStats(temp.getStats());
            campaignDTO.setSystemDateTime(temp.getCreateTime());
            return campaignDTO;
        }).collect(Collectors.toCollection(ArrayList::new));
        campaignListRspDTO.setRecordList(campaignDTOs);
        campaignListRspDTO.setRecordNumber(campaignPublicList.size());
        return campaignListRspDTO;
    }

    @Override
    public void messageSend(MessageSendReqDTO messageSendReqDTO) throws LemonException {
        String userId = messageSendReqDTO.getUserId();
        String messageTemplateId = messageSendReqDTO.getMessageTemplateId();
        String messageLanguage = messageSendReqDTO.getMessageLanguage();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        LocalDateTime tradeDateTime = DateTimeUtils.getCurrentLocalDateTime();
        String tradeDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
        String env = LemonUtils.getProperty("sendEnv.env");
        boolean isDiction = false;
        String version = "";
        String userTypeLog = "";

        String termType = Constants.TERM_TYPE_I;//默认是透传设备
        // 查询消息模版
        MessageTemplateDO messageTemplateDO = messageTemplateDao.get(messageTemplateId);
        if (JudgeUtils.isNull(messageTemplateDO)) {
            LemonException.throwBusinessException(MsgCdEnum.MESSAGETEMP_NOT_EXISTS.getMsgCd());
        }
        String type = messageTemplateDO.getType();
        String userType = messageTemplateDO.getUserType();
        String passPushFlag = Constants.PASS_PUSH_FLG_Y;//默认非透传
        String messageTitle = "";
        String messageContentCn = "";
        String messageContentEn = "";
        String messageContentKh = "";
        String messageContent = "";

        String replaceField = messageTemplateDO.getReplaceField();
        //String templateContent = "";

        // 解析消息模版
        try {
            messageContentCn = SmsTemplateResolveUtil.resolveTemplate(messageSendReqDTO.getReplaceFieldMap(),
                    replaceField, messageTemplateDO.getTemplateContentCn());
            messageContentEn = SmsTemplateResolveUtil.resolveTemplate(messageSendReqDTO.getReplaceFieldMap(),
                    replaceField, messageTemplateDO.getTemplateContentEn());
            messageContentKh = SmsTemplateResolveUtil.resolveTemplate(messageSendReqDTO.getReplaceFieldMap(),
                    replaceField, messageTemplateDO.getTemplateContentKh());
        } catch (Exception e) {
            LemonException.throwBusinessException(MsgCdEnum.MESSAGETEMP_RESOLVE_ERROR.getMsgCd());
        }

        // 登记消息推送流水表
        MessageSendDO messageSendDO = new MessageSendDO();
        String messageSendJrnRanDom = IdGenUtils.generateId("CMM_ORDER_NO", 8);
        String messageJrnNo = LemonUtils.getApplicationName() + tradeDateTimeStr + messageSendJrnRanDom;
        messageSendDO.setMessageJrnNo(messageJrnNo);
        messageSendDO.setTradeDate(tradeDate);
        messageSendDO.setTradeTime(tradeTime);
        messageSendDO.setUserId(userId);
        messageSendDO.setPassPushFlg(messageTemplateDO.getPassPushFlg());
        messageSendDO.setUserType(userType);
        messageSendDO.setType(type);
        messageSendDO.setMessageTitle(messageTemplateDO.getMessageTitleCn());
        messageSendDO.setMessageContent(messageContentCn);
        messageSendDO.setMessageTitleEn(messageTemplateDO.getMessageTitleEn());
        messageSendDO.setMessageContentEn(messageContentEn);
        messageSendDO.setMessageTitleKh(messageTemplateDO.getMessageTitleKh());
        messageSendDO.setMessageContentKh(messageContentKh);
        messageSendDO.setStats(Constants.CHECK_INIT);
        int result = messageSendDao.insert(messageSendDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.MESSAGE_SEND_FAIL.getMsgCd());
        }
        // 查询消息推送ClientID信息
        Map<String, String> keyMap = new HashMap<String, String>();
        keyMap.put("userId", userId);
        keyMap.put("userType", userType);
        PushClientDO pushClientDO = pushClientDao.get(keyMap);
        if (JudgeUtils.isNull(pushClientDO)) {
            logger.error(MsgCdEnum.MESSAGE_CLIENT_NOT_EXISTS.getMsgCd() + ", " + MsgCdEnum.MESSAGE_CLIENT_NOT_EXISTS.getMsgInfo() );
            return ;
            //LemonException.throwBusinessException(MsgCdEnum.MESSAGE_CLIENT_NOT_EXISTS.getMsgCd());
        }

        //按照目标终端当前环境语言来选择推送语种
        String lang = pushClientDO.getLanguage();
        if(lang != null && !"".equals(lang)){
            messageLanguage = lang;
        }

        // 判断语种
        if (messageLanguage.equals(Constants.LANG_ZH)) {
            messageTitle = messageTemplateDO.getMessageTitleCn();
            messageContent = messageContentCn;
            //templateContent = messageTemplateDO.getTemplateContentCn();
        } else if(messageLanguage.equals(Constants.LANG_EN)){
            messageTitle = messageTemplateDO.getMessageTitleEn();
            messageContent = messageContentEn;
            //templateContent = messageTemplateDO.getTemplateContentEn();
        } else {
            messageTitle = messageTemplateDO.getMessageTitleKh();
            messageContent = messageContentKh;
            //templateContent = messageTemplateDO.getTemplateContentKh();
        }

        //检查当前账号是否在设备中登录
        keyMap = new HashMap<String, String>();
        keyMap.put("clientId", pushClientDO.getClientId());
        keyMap.put("userType", userType);
        PushClientDO pushClientDO1 = pushClientDao.getLoginTime(keyMap);
        if(pushClientDO.getModifyTime().compareTo(pushClientDO1.getModifyTime()) < 0){
            logger.error("userId is be replace" + userId + " , " + pushClientDO.getModifyTime() + " , " + pushClientDO1.getModifyTime());
            return ;
        }
        String clientId = pushClientDO.getClientId();
        // 查询消息推送参数
        String paramId = Constants.PUSH_PARAMID_PERSONAL;
        if (userType.equals(Constants.USER_TYPE_MERCHANT)) {
            paramId = Constants.PUSH_PARAMID_MERCHANT;
        }

        //设备信息获取
        AppInfoDO qryInAppInfoDO = new AppInfoDO();
        qryInAppInfoDO.setUserId(userId);
        AppInfoDO appInfoDO = appInfoDao.getRecentByCondition(qryInAppInfoDO);
        //设备信息获取失败，默认走透传
        //如果获取不到用户登录信息，说明那么将不给用户做消息推送
        if (JudgeUtils.isNull(appInfoDO)) {
            logger.error("error Code : " + MsgCdEnum.LOGINFO_QRY_FAIL.getMsgCd() + ", error info : " + MsgCdEnum.LOGINFO_QRY_FAIL.getMsgInfo());
            return ;
        }else{
            termType = appInfoDO.getTermType();
            version = appInfoDO.getVersions();
            userTypeLog = appInfoDO.getUserType();

            //String osVersion =  appInfoDO.getOsVersion();
            //识别版本情况;iphone os 8.2 以上，使用数据字典，以下不使用数据字典
            /*if(Constants.GETUI_IOS_VERSION.compareTo(osVersion) > 0){
                isDiction = true;
            }else{
                isDiction = false;
            }*/
        }

        //临时代码，等安卓同步个推密钥后可删除 WTF
        if(Constants.ENV_PRO.equals(env) && (compareVersion(Constants.A_UAPP_VERSION, version) > 0)  && Constants.A_UAPP_TYPE.equals(userTypeLog)){
            env = Constants.ENV_UAT;
        }
        if(Constants.ENV_PRO.equals(env) && ( "1027".equals(version) || "1030".equals(version) || "1036".equals(version)
                || "1037".equals(version) || "1038".equals(version) || "1039".equals(version) || "1040".equals(version) || compareVersion(Constants.A_MAPP_VERSION, version) > 0 )  && Constants.A_MAPP_TYPE.equals(userTypeLog)){
            env = Constants.ENV_UAT;
        }


        logger.info("APPVERSION: "  + version + " , env : " + env);
        //PushParamDO pushParamDO = pushParamDao.get(paramId);
        /*if(Constants.TERM_TYPE_A.equals(termType)){
            env = Constants.ENV_UAT;
        }*/
        PushParamDO pushParamDO = pushParamDao.getPushParamInfo(paramId, env);
        String appId = pushParamDO.getAppId();
        String appKey = pushParamDO.getAppKey();
        String master = pushParamDO.getMasterSecret();
        // 调用第三方消息推送平台推送消息
        MessagePushBO messagePushBO = new MessagePushBO();
        Object messageContentObject = null;
        String messageJson = null;
        try {
            messagePushBO.setType(type);
            if (!type.equals(Constants.MESSAGE_TYPE_CENTER)) {
                messageContentObject = objectMapper.readValue(messageContent, Object.class);
            } else {
                MessageDTO messageDTO = new MessageDTO();
                messageDTO.setId(messageJrnNo);
                messageDTO.setType(Constants.MESSAGE);
                messageDTO.setUserType(userType);
                messageDTO.setTitle(messageTitle);
                messageDTO.setContent(messageContent);
                messageDTO.setStats(Constants.CHECK_INIT);
                messageDTO.setSystemDateTime(tradeDateTime);
                messageContentObject = messageDTO;
            }
            messagePushBO.setBody(messageContentObject);
            messagePushBO.setTime(tradeDateTime.toEpochSecond(ZoneOffset.UTC));
            messageJson = objectMapper.writeValueAsString(messagePushBO);
            //change by cjw 20171013 将消息中心推送调整为按照设备区分透传 非透传
            //iOS的消息中心非透传，其余的统一透传
            //if (type.equals(Constants.MESSAGE_TYPE_CENTER) && passPushFlag.equals("0")) {
                // 非透传消息
            //    MessagePushUtils.MessagePush(appId, appKey, master, "0", clientId, messageTitle, messageContent,
            //            messageJson);
            //} else {
                // 透传消息
            //    MessagePushUtils.MessagePush(appId, appKey, master, "1", clientId, null, null, messageJson);
            //}
            logger.info("userId : " + userId + ", type : " + type);
            if (type.equals(Constants.MESSAGE_TYPE_CENTER)){
                logger.info("termType : " + termType);
                if(Constants.TERM_TYPE_I.equals(termType)){
                    passPushFlag = Constants.PASS_PUSH_FLG_Y;
                    logger.info(appId + ", " + appKey + ", " + master + ", " + passPushFlag + ", " + clientId + ", " + messageTitle + ", " + messageContent + ", " + messageJson + ", " +  isDiction);
           //         MessagePushUtils.MessagePush(appId, appKey, master, passPushFlag, clientId, messageTitle, messageContent, messageJson, isDiction, messageJrnNo, DateTimeUtils.formatLocalDateTime(tradeDateTime, "yyyyMMddHHmmss"));
                    logger.info("send over pass !" + "  passPushFlag : " + passPushFlag);
                    //passPushFlag = Constants.PASS_PUSH_FLG_N;
                    ///logger.info("send over pass !" + "  passPushFlag : " + passPushFlag);
                    //MessagePushUtils.MessagePush(appId, appKey, master, passPushFlag, clientId, messageTitle, messageContent, messageJson, isDiction, messageJrnNo, DateTimeUtils.formatLocalDateTime(tradeDateTime, "yyyyMMddHHmmss"));

                    return;
                }else{
                    passPushFlag = Constants.PASS_PUSH_FLG_N;
                    logger.info(appId + ", " + appKey + ", " + master + ", " + passPushFlag + ", " + clientId + ", " + messageTitle + ", " + messageContent + ", " + messageJson + ", " +  isDiction);
        //            MessagePushUtils.MessagePush(appId, appKey, master, passPushFlag, clientId, messageTitle, messageContent, messageJson, isDiction, messageJrnNo, DateTimeUtils.formatLocalDateTime(tradeDateTime, "yyyyMMddHHmmss"));
                    logger.info("send over pass !" + "  passPushFlag : " + passPushFlag);
                    return;
                }
            }else {
                logger.info(appId + ", " + appKey + ", " + master + ", " + passPushFlag + ", " + clientId + ", " + messageTitle + ", " + messageContent + ", " + messageJson + ", " + isDiction);
     //           MessagePushUtils.MessagePush(appId, appKey, master, passPushFlag, clientId, messageTitle, messageContent, messageJson, false, messageJrnNo, DateTimeUtils.formatLocalDateTime(tradeDateTime, "yyyyMMddHHmmss"));
                logger.info("send over nopass!" + "  passPushFlag : " + passPushFlag);
            }
        } catch (Exception e) {
            LemonException.throwBusinessException(MsgCdEnum.MESSAGE_SEND_FAIL.getMsgCd());
        }
    }

    @Override
    public MessageListRspDTO messageList(MessageListReqDTO messageListReqDTO) throws LemonException {
        MessageListRspDTO messageListRspDTO = new MessageListRspDTO();
        String userId = LemonUtils.getUserId();
        String loginName = LemonUtils.getLoginName();
        String userType = messageListReqDTO.getUserType();
        // 查询未读消息记录总数
        MessageSendDO countInMessageSendDO = new MessageSendDO();
        countInMessageSendDO.setUserId(userId);
        countInMessageSendDO.setLoginName(loginName);
        countInMessageSendDO.setType(Constants.MESSAGE_TYPE_CENTER);
        countInMessageSendDO.setUserType(userType);
        countInMessageSendDO.setStats(Constants.CHECK_INIT);
        int unacknowledgeNumber = messageSendDao.countByCondition(countInMessageSendDO);
        // 消息列表
        MessageSendDO qryInMessageSendDO = new MessageSendDO();
        qryInMessageSendDO.setType(Constants.MESSAGE_TYPE_CENTER);
        qryInMessageSendDO.setUserId(userId);
        qryInMessageSendDO.setUserType(userType);
        qryInMessageSendDO.setLoginName(loginName);

        /*List<MessageSendDO> messageSendDOs = PageUtils.pageQuery(messageListReqDTO.getPageNum(),
                messageListReqDTO.getPageSize(), false, () -> {
                    return this.messageSendDao.getListByCondition(qryInMessageSendDO);
                });*/


        PageInfo pageInfo = PageUtils.pageQueryWithCount(messageListReqDTO.getPageNum(),
                messageListReqDTO.getPageSize(), () -> {
                    return this.messageSendDao.getListByCondition(qryInMessageSendDO);
                });
        int pages = pageInfo.getPages();
        long total = pageInfo.getTotal();
        int pageNum = pageInfo.getPageNum();
        List<MessageSendDO> messageSendDOs = pageInfo.getList();
        if (JudgeUtils.isEmpty(messageSendDOs)) {
            messageListRspDTO.setRecordPgNumber(pages);
            messageListRspDTO.setCurPgNumber(pageNum);
            messageListRspDTO.setTotalNumber(total);
            messageListRspDTO.setRecordNumber(total);
            messageListRspDTO.setRecordList(new ArrayList<MessageDTO>());
            return messageListRspDTO;
        }
        String language = LemonUtils.getLocale().getLanguage();
        logger.info("language : {}",language);
        List<MessageDTO> messageDTOs = messageSendDOs.stream().map(temp -> {
            MessageDTO messageDTO = new MessageDTO();
            if((Constants.LANG_ZH).equals(language)){
                messageDTO.setTitle(temp.getMessageTitle());
                messageDTO.setContent(temp.getMessageContent());
            }else if(Constants.LANG_KM.equals(language)){
                messageDTO.setTitle(temp.getMessageTitleKh());
                messageDTO.setContent(temp.getMessageContentKh());
            }else{
                messageDTO.setTitle(temp.getMessageTitleEn());
                messageDTO.setContent(temp.getMessageContentEn());
            }
            messageDTO.setId(temp.getMessageJrnNo());
            messageDTO.setType(Constants.MESSAGE);
            messageDTO.setUserType(temp.getUserType());
            messageDTO.setStats(temp.getStats());
            messageDTO.setSystemDateTime(temp.getCreateTime());
            return messageDTO;
        }).collect(Collectors.toCollection(ArrayList::new));
        messageListRspDTO.setRecordList(messageDTOs);
        messageListRspDTO.setRecordPgNumber(pages);
        messageListRspDTO.setCurPgNumber(pageNum);
        messageListRspDTO.setTotalNumber(total);
        messageListRspDTO.setRecordNumber(total);
        messageListRspDTO.setUnacknowledgeNumber(unacknowledgeNumber);
        return messageListRspDTO;
    }

    @Override
    public void messageAcknowledge(MessageAcknowledgeReqDTO acknowledgeReqDTO) throws LemonException {
        String loginName = LemonUtils.getLoginName();
        String userId = LemonUtils.getUserId();
        logger.info("loginName " + loginName + ", userId " + userId);
        String id = acknowledgeReqDTO.getId();
        String userType = acknowledgeReqDTO.getUserType();
        MessageSendDO valueMessageSendDO = new MessageSendDO();
        valueMessageSendDO.setStats(Constants.CHECK_VERIFIED);
        MessageSendDO conditionMessageSendDO = new MessageSendDO();
        conditionMessageSendDO.setMessageJrnNo(id);
        conditionMessageSendDO.setUserId(userId);
        conditionMessageSendDO.setType(Constants.MESSAGE_TYPE_CENTER);
        conditionMessageSendDO.setUserType(userType);
        conditionMessageSendDO.setStats(Constants.CHECK_INIT);
        conditionMessageSendDO.setLoginName(loginName);
        // 忽略消息状态更新失败错误
        messageSendDao.updateByCondition(valueMessageSendDO, conditionMessageSendDO);
    }

    @Override
    public void messageClient(MessageClientReqDTO messageClientReqDTO, String flg) throws LemonException {
        LocalDateTime tradeDateTime = DateTimeUtils.getCurrentLocalDateTime();
        String userId = LemonUtils.getUserId();
        String loginNm = LemonUtils.getLoginName();
        String userType = messageClientReqDTO.getUserType();
        String clientId = messageClientReqDTO.getClientId();
        String language = LemonUtils.getLocale().getLanguage();
        String loginFlg = messageClientReqDTO.getLoginFlg() == null? "0":messageClientReqDTO.getLoginFlg();
        String loginId = messageClientReqDTO.getLoginId();
        int result = 0;
        PushClientDO pushClientDO = new PushClientDO();
        pushClientDO.setModifyTime(tradeDateTime);
        pushClientDO.setLoginId(loginId);
        if (userType.equals(Constants.USER_TYPE_MERCHANT)) {
            pushClientDO.setUserId(loginNm);
        } else {
            pushClientDO.setUserId(userId);
        }
        if("0".equals(flg)){
            pushClientDO.setPreClientId(clientId);
            result = pushClientDao.updateClientInfo(pushClientDO);
        }else{
            pushClientDO.setClientId(clientId);
            logger.info("languageIn : " + language);
            pushClientDO.setLanguage(language);
            pushClientDO.setUserType(userType);
            pushClientDO.setLoginFlg(loginFlg);
            result = pushClientDao.insertOrUpdate(pushClientDO);
            if (result <= 0) {
                LemonException.throwBusinessException(MsgCdEnum.MESSAGE_CLIENT_FAIL.getMsgCd());
            }
        }
    }

    @Override
    public CommonEncryptRspDTO encrypt(CommonEncryptReqDTO encryptReqDTO) throws LemonException {
        CommonEncryptRspDTO encryptRspDTO = new CommonEncryptRspDTO();
        String key = env.getProperty("cmm.encrypt.key");
        String data = encryptReqDTO.getData();
        String type = encryptReqDTO.getType();
        String resultData = "";
        try {
            if (type.equals(Constants.ENCRYPT)) {
                resultData = EncryptionUtils.encrypt(key, data);
            } else {
                resultData = EncryptionUtils.decrypt(key, data);
            }
        } catch (EncryptException e) {
            LemonException.throwBusinessException(MsgCdEnum.ENCRYPT_DECRYPT_FAIL.getMsgCd());
        }
        encryptRspDTO.setData(resultData);
        return encryptRspDTO;
    }

    @Override
    public AppInfoRspDTO logInfo(AppInfoReqDTO infoReqDTO) throws LemonException {
        AppInfoRspDTO appInfoRspDTO = new AppInfoRspDTO();
        AppInfoDO qryInAppInfoDO = new AppInfoDO();
        qryInAppInfoDO.setUserId(infoReqDTO.getUserId());
        AppInfoDO appInfoDO = appInfoDao.getRecentByCondition(qryInAppInfoDO);
        if (JudgeUtils.isNull(appInfoDO)) {
            LemonException.throwBusinessException(MsgCdEnum.LOGINFO_QRY_FAIL.getMsgCd());
        }
        BeanUtils.copyProperties(appInfoRspDTO, appInfoDO);
        // 查询消息推送ClientID信息
        Map<String, String> keyMap = new HashMap<String, String>();
        keyMap.put("userId", infoReqDTO.getUserId());
        PushClientDO pushClientDO = pushClientDao.get(keyMap);
        appInfoRspDTO.setLanguage(pushClientDO.getLanguage());
        return appInfoRspDTO;
    }

    @Override
    public SystemKeyRspDTO key(SystemKeyReqDTO systemKeyReqDTO) throws LemonException {
        SystemKeyRspDTO systemKeyRspDTO = new SystemKeyRspDTO();
        String keyIndex = systemKeyReqDTO.getKeyIndex();
        KeyParamDO qryInKeyParamDO = new KeyParamDO();
        qryInKeyParamDO.setId(keyIndex);
        qryInKeyParamDO.setStats(Constants.STATUS_AVAILABLE);
        // 查询密钥信息
        KeyParamDO qryOutKeyParamDO = keyParamDao.getByCondition(qryInKeyParamDO);
        if (JudgeUtils.isNull(qryOutKeyParamDO)) {
            LemonException.throwBusinessException(MsgCdEnum.SYSTEMKEY_NOT_EXISTS.getMsgCd());
        }
        systemKeyRspDTO.setKey(qryOutKeyParamDO.getTkey());
        systemKeyRspDTO.setAlgorithm(qryOutKeyParamDO.getAlgorithms());
        systemKeyRspDTO.setChannel(qryOutKeyParamDO.getChannel());
        qryOutKeyParamDO.setVersions(qryOutKeyParamDO.getVersions());
        return systemKeyRspDTO;
    }

    @Override
    public void logAppInfo(String channel, String loginName, AppInfoDTO appInfoDTO) throws LemonException {
        AppInfoDO appInfoDO = new AppInfoDO();
        BeanUtils.copyProperties(appInfoDO, appInfoDTO);
        appInfoDO.setLoginJrnNo(generateOrderNo());
        appInfoDO.setChannel(channel);
        appInfoDO.setLoginName(loginName);
        appInfoDO.setVersions(appInfoDTO.getVersion());
        appInfoDao.insert(appInfoDO);
    }

    /**
     * 查询app 信息(用于强制更新)
     * @return
     * @throws LemonException
     */
    @Override
    public CmmAppvnoinfRspDTO getAppvnoinfo(String channel, String version) throws LemonException {
        CmmAppvnoinfRspDTO cmmAppvnoinfDTO = new CmmAppvnoinfRspDTO();
        CmmAppvnoinfDO cmmAppvnoinfDO = new CmmAppvnoinfDO();
        CmmAppvnoinfDO cmmAppvnoinfDORs1 = new CmmAppvnoinfDO();
        CmmAppvnoinfDO cmmAppvnoinfDORs2 = new CmmAppvnoinfDO();

        logger.info("channelVal： " + channel + " , versionVal ：" + version);
        //String userId = LemonUtils.getUserId();
        //查询最新强制更新版本信息
        //安卓商户
        if(Constants.A_CHANNEL_MERC.equals(channel)){
            cmmAppvnoinfDO.setAppSys(Constants.TERM_TYPE_A);
            cmmAppvnoinfDO.setAppTyp(Constants.APP_TYPE_MERC);
            //苹果商户
        }else if(Constants.I_CHANNEL_MERC.equals(channel)){
            cmmAppvnoinfDO.setAppSys(Constants.TERM_TYPE_I);
            cmmAppvnoinfDO.setAppTyp(Constants.APP_TYPE_MERC);
            //安卓用户
        }else if(Constants.A_CHANNEL_USER.equals(channel)){
            cmmAppvnoinfDO.setAppSys(Constants.TERM_TYPE_A);
            cmmAppvnoinfDO.setAppTyp(Constants.APP_TYPE_USER);
            //苹果用户
        }else if(Constants.I_CHANNEL_USER.equals(channel)){
            cmmAppvnoinfDO.setAppSys(Constants.TERM_TYPE_I);
            cmmAppvnoinfDO.setAppTyp(Constants.APP_TYPE_USER);
        }else{
            LemonException.throwBusinessException(MsgCdEnum.APP_CHANNEL_IS_ERROR.getMsgCd());
        }

        cmmAppvnoinfDO.setIsTrue(null);
        cmmAppvnoinfDORs2 = cmmAppvnoinfDao.getAppvnoinfo(cmmAppvnoinfDO);

        if(cmmAppvnoinfDORs2 == null){
            return null;
        }

        //查询当前用户登录信息
        if(compareVersion(version, cmmAppvnoinfDORs2.getAppVersion()) < 0) {
            BeanUtils.copyProperties(cmmAppvnoinfDTO, cmmAppvnoinfDORs2);
        }else{
            return null;
        }

        //是否强制更新 0强制,1非强制
        cmmAppvnoinfDO.setIsTrue("0");
        cmmAppvnoinfDORs1 = cmmAppvnoinfDao.getAppvnoinfo(cmmAppvnoinfDO);
        if(cmmAppvnoinfDORs1 == null){
            return cmmAppvnoinfDTO;
        }
        if(compareVersion(version, cmmAppvnoinfDORs1.getAppVersion()) < 0){
            cmmAppvnoinfDTO.setIsTrue("0");
        }
        return cmmAppvnoinfDTO;
    }

    @Override
    public Boolean messageClientSwitch(MessageClientSwitchReqDTO messageClientSwitchReqDTO){
        String userId = messageClientSwitchReqDTO.getUserId();
        String clientId = messageClientSwitchReqDTO.getClientId();
        PushClientDO pushClientDO = pushClientDao.getClientInfo(userId, null);

        if(pushClientDO == null){
            return false;
        }
        if(clientId != null )
        {
            if(clientId.equals(pushClientDO.getClientId())) {
                return true;
            }else{
                return false;
            }
        }

        /*if(pushClientDO.getPreClientId() == null || "".equals(pushClientDO.getPreClientId())){
            return true;
        }*/

        if(!pushClientDO.getClientId().equals(pushClientDO.getPreClientId())){
            return false;
        }
        return true;
    }

    // 生成订单号
    private String generateOrderNo() {
        String tradeDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
        String orderJrn = IdGenUtils.generateId("CMM_ORDER_NO", 8);
        return LemonUtils.getApplicationName() + tradeDateTimeStr + orderJrn;
    }

    public static int compareVersion(String version1, String version2) {

        String[] versionArray1 = version1.split("\\.");//注意此处为正则匹配，不能用.；
        String[] versionArray2 = version2.split("\\.");
        int idx = 0;
        int minLength = Math.min(versionArray1.length, versionArray2.length);//取最小长度值
        int diff = 0;
        while (idx < minLength
                && (diff = versionArray1[idx].length() - versionArray2[idx].length()) == 0//先比较长度
                && (diff = versionArray1[idx].compareTo(versionArray2[idx])) == 0) {//再比较字符
            ++idx;
        }
        //如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大；
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }
}
