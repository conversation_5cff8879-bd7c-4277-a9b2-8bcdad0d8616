package com.hisun.lemon.cmm.service.impl;


import com.hisun.lemon.cmm.dto.CmmMsgCd;
import com.hisun.lemon.cmm.rsp.ImageRspDTO;
import com.hisun.lemon.cmm.rsp.ImagesRspDTO;
import com.hisun.lemon.cmm.service.ImgUploadService;
import com.hisun.lemon.cmm.utils.UploadUtil;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("imgUploadServiceImpl")
public class ImgUploadServiceImpl implements ImgUploadService {

    private final static Logger logger = LoggerFactory.getLogger(ImgUploadServiceImpl.class);

    @Value("${upload.win-file-path}")
    private String windowsFilePath;

    @Value("${upload.linux-file-path}")
    private String linuxFilePath;

    @Value("${upload.url-prefix}")
    private String urlPrefix;

    public String getUploadPath() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            return windowsFilePath;
        } else {
            return linuxFilePath;
        }
    }

    @Override
    public GenericRspDTO<ImageRspDTO> imageUpload(MultipartFile file) {
        //获取图片存放地址
        String path = getUploadPath();
        ImageRspDTO imageRspDTO = new ImageRspDTO();
        GenericRspDTO<ImageRspDTO> responseDTO = new GenericRspDTO<>();
        try {
            //上传图片
//            String url = sysParmConDao.getSysParmDO(ParmKeyConstant.ECP_BASE_URL).getParmVal();
            String url = urlPrefix;
            String adv = UploadUtil.uploadFlie(file, path, url);
            //判断文件是否上传成功
            if (JudgeUtils.isBlank(adv)) {
                logger.info(CmmMsgCd.MSG_MOC52001.getMsgInfo());
                responseDTO.setBody(imageRspDTO);
                responseDTO.setMsgCd(CmmMsgCd.MSG_MOC52001.getMsgCd());
                responseDTO.setMsgInfo(CmmMsgCd.MSG_MOC52001.getMsgInfo());
                return responseDTO;
            }
            logger.info("adv:" + adv);
            //设置返回路径
            imageRspDTO.setImgHerf(adv);
        } catch (Exception e) {
            logger.error("图片上传失败：" + e.getMessage());
            responseDTO.setBody(imageRspDTO);
            responseDTO.setMsgCd(CmmMsgCd.MSG_MOC52001.getMsgCd());
            responseDTO.setMsgInfo(CmmMsgCd.MSG_MOC52001.getMsgInfo());
            return responseDTO;
        }


        responseDTO.setBody(imageRspDTO);
        responseDTO.setMsgCd(CmmMsgCd.MSG_MOC52111.getMsgCd());
        responseDTO.setMsgInfo(CmmMsgCd.MSG_MOC52111.getMsgInfo());
        return responseDTO;
    }


    @Override
    public GenericRspDTO<ImagesRspDTO> imagesUpload(List<MultipartFile> files) {
        GenericRspDTO<ImagesRspDTO> responseDTO = new GenericRspDTO<>();
        ImagesRspDTO imageRspDTO = new ImagesRspDTO();
        ArrayList<String> list = new ArrayList<>();
        //获取图片存放地址
        String path = getUploadPath();
        for (MultipartFile file : files) {
            try {
                //上传图片
//            String url = sysParmConDao.getSysParmDO(ParmKeyConstant.ECP_BASE_URL).getParmVal();
                String url = urlPrefix;
                String adv = UploadUtil.uploadFlie(file, path, url);
                //判断文件是否上传成功
                if (JudgeUtils.isBlank(adv)) {
                    logger.info(CmmMsgCd.MSG_MOC52001.getMsgInfo());
                    responseDTO.setBody(imageRspDTO);
                    responseDTO.setMsgCd(CmmMsgCd.MSG_MOC52001.getMsgCd());
                    responseDTO.setMsgInfo(CmmMsgCd.MSG_MOC52001.getMsgInfo());
                    return responseDTO;
                }
                logger.info("adv:" + adv);
                //设置返回路径
                list.add(adv);
            } catch (Exception e) {
                logger.error("图片上传失败：" + e.getMessage());
                responseDTO.setBody(imageRspDTO);
                responseDTO.setMsgCd(CmmMsgCd.MSG_MOC52001.getMsgCd());
                responseDTO.setMsgInfo(CmmMsgCd.MSG_MOC52001.getMsgInfo());
                return responseDTO;
            }
        }
        imageRspDTO.setImgHerfs(list);
        responseDTO.setBody(imageRspDTO);
        responseDTO.setMsgCd(CmmMsgCd.MSG_MOC52111.getMsgCd());
        responseDTO.setMsgInfo(CmmMsgCd.MSG_MOC52111.getMsgInfo());
        return responseDTO;
    }
}
