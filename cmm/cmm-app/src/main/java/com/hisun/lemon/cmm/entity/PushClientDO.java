/*
 * @ClassName PushClientDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-11 11:19:30
 */
package com.hisun.lemon.cmm.entity;

import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class PushClientDO extends BaseDO {
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields userType 用户类型 personal:个人的 merchant:商户的
     */
    private String userType;
    /**
     * @Fields clientId 消息推送id
     */
    private String clientId;

    /**
     * @Fields preClientId 消息推送预登记id
     */
    private String preClientId;

    /**
     * @Fields stats 状态
     */
    private String stats;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields language 语种
     */
    private String language;

    /**
     * 在线标志  0:登入 1：登出
     */
    private String loginFlg;

    /**
     * 登录Id
     */
    private String loginId;



    public String getLoginFlg() {
        return loginFlg;
    }

    public void setLoginFlg(String loginFlg) {
        this.loginFlg = loginFlg;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getPreClientId() {
        return preClientId;
    }

    public void setPreClientId(String preClientId) {
        this.preClientId = preClientId;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }
}