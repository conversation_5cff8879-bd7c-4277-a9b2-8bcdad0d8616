/*
 * @ClassName MessageSendDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 16:50:03
 */
package com.hisun.lemon.cmm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.hisun.lemon.framework.data.BaseDO;

public class MessageSendDO extends BaseDO {
    /**
     * @Fields messageJrnNo 消息流水号
     */
    private String messageJrnNo;
    /**
     * @Fields tradeDate 交易日期
     */
    private LocalDate tradeDate;
    /**
     * @Fields tradeTime 交易时间
     */
    private LocalTime tradeTime;
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields loginName 用户登陆名称
     */
    private String loginName;
    /**
     * @Fields passPushFlg 是否透传 0:非透传 1:透传
     */
    private String passPushFlg;
    /**
     * @Fields userType 用户类型 common:通用 personal:个人的 merchant:商户的
     */
    private String userType;
    /**
     * @Fields type 消息类型 center:消息中心 pay:支付请求 order:订单结果
     */
    private String type;
    /**
     * @Fields messageTitle 消息标题 中文
     */
    private String messageTitle;
    /**
     * @Fields messageContent 消息内容 中文
     */
    private String messageContent;
    /**
     * @Fields messageTitle 消息标题 英文
     */
    private String messageTitleEn;
    /**
     * @Fields messageContent 消息内容 英文
     */
    private String messageContentEn;
    /**
     * @Fields messageTitle 消息标题 柬语
     */
    private String messageTitleKh;
    /**
     * @Fields messageContent 消息内容 柬语
     */
    private String messageContentKh;

    /**
     * @Fields stats 状态0:未读 1:已读
     */
    private String stats;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getMessageJrnNo() {
        return messageJrnNo;
    }

    public void setMessageJrnNo(String messageJrnNo) {
        this.messageJrnNo = messageJrnNo;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassPushFlg() {
        return passPushFlg;
    }

    public void setPassPushFlg(String passPushFlg) {
        this.passPushFlg = passPushFlg;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getMessageTitleEn() {
        return messageTitleEn;
    }

    public void setMessageTitleEn(String messageTitleEn) {
        this.messageTitleEn = messageTitleEn;
    }

    public String getMessageContentEn() {
        return messageContentEn;
    }

    public void setMessageContentEn(String messageContentEn) {
        this.messageContentEn = messageContentEn;
    }

    public String getMessageTitleKh() {
        return messageTitleKh;
    }

    public void setMessageTitleKh(String messageTitleKh) {
        this.messageTitleKh = messageTitleKh;
    }

    public String getMessageContentKh() {
        return messageContentKh;
    }

    public void setMessageContentKh(String messageContentKh) {
        this.messageContentKh = messageContentKh;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}