/*
 * @ClassName NoticPulblicDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 10:27:11
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class NoticPulblicDO extends BaseDO {
    /**
     * @Fields id 公告ID
     */
    private String id;
    /**
     * @Fields noticeTitleKh 柬语公告标题
     */
    private String noticeTitleKh;
    /**
     * @Fields noticeTitleCn 中文公告标题
     */
    private String noticeTitleCn;
    /**
     * @Fields noticeTitleEn 英文公告标题
     */
    private String noticeTitleEn;
    /**
     * @Fields noticeContentKh 柬语公告内容
     */
    private String noticeContentKh;
    /**
     * @Fields noticeContentCn 中文公告内容
     */
    private String noticeContentCn;
    /**
     * @Fields noticeContentEn 英文公告内容
     */
    private String noticeContentEn;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields channel 渠道
     */
    private String channel;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNoticeTitleKh() {
        return noticeTitleKh;
    }

    public void setNoticeTitleKh(String noticeTitleKh) {
        this.noticeTitleKh = noticeTitleKh;
    }

    public String getNoticeTitleCn() {
        return noticeTitleCn;
    }

    public void setNoticeTitleCn(String noticeTitleCn) {
        this.noticeTitleCn = noticeTitleCn;
    }

    public String getNoticeTitleEn() {
        return noticeTitleEn;
    }

    public void setNoticeTitleEn(String noticeTitleEn) {
        this.noticeTitleEn = noticeTitleEn;
    }

    public String getNoticeContentKh() {
        return noticeContentKh;
    }

    public void setNoticeContentKh(String noticeContentKh) {
        this.noticeContentKh = noticeContentKh;
    }

    public String getNoticeContentCn() {
        return noticeContentCn;
    }

    public void setNoticeContentCn(String noticeContentCn) {
        this.noticeContentCn = noticeContentCn;
    }

    public String getNoticeContentEn() {
        return noticeContentEn;
    }

    public void setNoticeContentEn(String noticeContentEn) {
        this.noticeContentEn = noticeContentEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}