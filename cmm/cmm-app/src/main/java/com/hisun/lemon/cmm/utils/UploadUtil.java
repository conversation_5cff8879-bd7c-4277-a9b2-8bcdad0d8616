package com.hisun.lemon.cmm.utils;


import com.hisun.lemon.cmm.constants.Constants;
import com.hisun.lemon.common.utils.JudgeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Base64.Decoder;
import java.util.Date;
import java.util.List;

/**
 * 文件上传工具类
 *
 * <AUTHOR>
 * @version 1.0
 * 20160706 admin 新增
 */
public class UploadUtil {
    private static Logger logger = LoggerFactory.getLogger(UploadUtil.class);

    private static final long FILE_SIZE = 4194304;
    private static final String FILE_SIZE_ERR = "上传文件不能大于4M";
    private static final String DATE_FORMAT2 = "YYYYMMDDHHmmss";
    private static final String FILE_ERR = "文件为空";

    /**
     * 保存文件
     *
     * @param filename  MultipartFile
     * @param uploadURL String
     * @return String 保存成功:路径; 保存失败：错误信息
     */
    public static String saveFile(MultipartFile filename, String uploadURL) throws Exception {
        if (!filename.isEmpty()) {
            try {
                // 使用更精确的时间戳，加上随机数确保唯一性
                String fileurl = "";
                String date = System.currentTimeMillis() + "";

                String myfile = filename.getOriginalFilename();
                String[] strArr = myfile.split("\\."); // 注意转义
                SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT2);
                String strName = sdf.format(new Date()) + System.nanoTime() % 1000000; // 添加纳秒部分
                myfile = strName + "." + strArr[strArr.length-1];

                File saveDir = new File(uploadURL + File.separator + date + File.separator + myfile);
                if (!saveDir.getParentFile().exists()) {
                    saveDir.getParentFile().mkdirs();
                }
                filename.transferTo(saveDir);
                fileurl = File.separator + "upload" + File.separator + date + File.separator + myfile;
                return fileurl;
            } catch (Exception e) {
                logger.error("保存文件失败[{}]",e.getMessage(),e);
            }
        }
        return FILE_ERR;
    }


    /**
     * 批量上传图片
     *
     * @param myfiles   文件
     * @param uploadURL 地址
     * @return boolean true:成功;false:失败
     * @throws Exception 异常
     */
    public static List<String> filesUpload(MultipartFile[] myfiles, String uploadURL, String baseURL) throws Exception {
        List<String> list = new ArrayList<>();
        if (myfiles != null && myfiles.length > 0) {
            for (int i = 0; i < myfiles.length; i++) {
                MultipartFile file = myfiles[i];
                if (file.getSize() > FILE_SIZE) {
                    throw new Exception(FILE_SIZE_ERR);
                }
                // 保存文件
                String filePath = baseURL + saveFile(file, uploadURL);
                list.add(filePath);
            }
        }
        return list;
    }

    /**
     * 上传图片
     *
     * @param file      文件
     * @param uploadURL 地址
     * @param name      文件名
     * @param date      日期
     * @return boolean
     * @throws Exception 异常
     */
    public static boolean filesUpload(MultipartFile file, String uploadURL, String name, String date) throws Exception {
        // 保存文件
        if (!file.isEmpty()) {
            // 保存文件
            File saveDir = new File(uploadURL + File.separator + date + File.separator + name);
            if (!saveDir.getParentFile().exists()) {
                saveDir.getParentFile().mkdirs();
            }
            // 转存文件
            file.transferTo(saveDir);
            return true;
        }
        return true;
    }

    /**
     * 上传文件方法
     *
     * @param filename 文件名称
     * @param path     文件路径
     * @return 完整路径
     * @throws Exception
     */
    public static String uploadFlie(MultipartFile filename, String path, String baseURL) throws Exception {

        //1.获取文件夹路径 日期
        String fileurl = "";
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
        String date = sdf2.format(new Date());

        //2.生成新的文件名 时间戳（添加纳秒确保唯一性）
        String myfile = filename.getOriginalFilename();
        String[] strArr = myfile.split(Constants.FILE_SPLIT_VAL);
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT2);
        String strName = sdf.format(new Date()) + (System.nanoTime() % 1000000); // 添加纳秒部分确保唯一性
        String suffix = strArr[strArr.length - 1].toLowerCase();
        if(strArr.length < 1 || !JudgeUtils.equalsAny(suffix, Constants.JPG, Constants.PNG, Constants.GIF, Constants.JPEG, Constants.PDF)){
            suffix = Constants.PNG;
        }
        myfile = strName + Constants.FILE_SPLIT_VAL2 + suffix;

        //3.上传文件
        filesUpload(filename, path, myfile, date);
        fileurl = File.separator + date + File.separator + myfile;

        return fileurl;
    }


    /**
     * 对字节数组字符串进行Base64解码并生成图片
     *
     * @param baseURL 访问地址前缀
     * @param path    文件路径
     * @param imgStr  base64编码字符串
     * @param suffix  文件后缀
     * @return 完整路径
     * @throws Exception
     */
    public static String generateImage(String imgStr, String suffix, String path, String baseURL) throws Exception {
        //图像数据为空
        if (imgStr != null) {
            Decoder decoder = Base64.getDecoder();
            OutputStream out = null;
            try {
                //Base64解码
                byte[] b = decoder.decode(imgStr);
                for (int i = 0; i < b.length; ++i) {
                    System.out.println(b[i]);
                    //调整异常数据
                    if (b[i] < 0) {
                        b[i] += 256;
                    }
                }
                //生成图片 1.获取文件夹路径 日期
                String date = System.currentTimeMillis() + "";
                //2.生成新的文件名 时间戳
                SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT2);
                String strName = sdf.format(new Date());
                String myfile = strName + Constants.FILE_SPLIT_VAL2 + suffix;
                String filePath = path + File.separator + date;
                File file = new File(filePath);
                if(!file.exists()){
                    new File(filePath).mkdir();
                }
                out = new FileOutputStream(filePath + File.separator + myfile);
                out.write(b);
                out.flush();
                String fileUrl = File.separator + "upload" + File.separator + date + File.separator + myfile;
                return baseURL + fileUrl;
            } catch (Exception e) {
                logger.error(e.getMessage(),e);
            }finally {
                if(JudgeUtils.isNotNull(out)){
                    out.close();
                }
            }
        }
        return imgStr;
    }

    public static byte[] hexString2Byte(String src) {
        if (src.length() % 2 != 0) {
            src = src + "0";
        }
        byte[] ret = new byte[src.length() / 2];
        byte[] tmp = src.getBytes();
        for (int i = 0; i < (src.length() / 2); i++) {
            ret[i] = uniteBytes(tmp[i * 2], tmp[i * 2 + 1]);
        }
        return ret;
    }

    /**
     * 将两个ASCII字符合成�?个字节； 如："EF"--> 0xEF
     *
     * @param src0
     *            byte
     * @param src1
     *            byte
     * @return byte
     */
    public static byte uniteBytes(byte src0, byte src1) {
        byte _b0 = Byte.decode("0x" + new String(new byte[] { src0 })).byteValue();
        _b0 = (byte) (_b0 << 4);
        byte _b1 = Byte.decode("0x" + new String(new byte[] { src1 })).byteValue();
        byte ret = (byte) (_b0 ^ _b1);
        return ret;
    }
}
