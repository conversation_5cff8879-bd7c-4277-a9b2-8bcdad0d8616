/*
 * @ClassName ICampaignPublicDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 18:23:08
 */
package com.hisun.lemon.cmm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.cmm.entity.CampaignPublicDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface ICampaignPublicDao extends BaseDao<CampaignPublicDO> {
    
    List<CampaignPublicDO> getListByCondition(CampaignPublicDO campaignPublicDO);
}