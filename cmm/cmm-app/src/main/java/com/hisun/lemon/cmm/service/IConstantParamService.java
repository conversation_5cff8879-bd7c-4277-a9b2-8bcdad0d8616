package com.hisun.lemon.cmm.service;

import com.hisun.lemon.cmm.entity.ConstantParamDo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/7/31
 */
public interface IConstantParamService {

    /**
     * 新增公共参数
     *
     * @param constantParamDo 参数对象
     */
    void addConstantParam(ConstantParamDo constantParamDo);

    /**
     * 更新公共参数
     *
     * @param constantParamDo 参数对象
     */
    void updateConstantParam(ConstantParamDo constantParamDo);

    /**
     * 删除公共参数
     *
     * @param parmNm 参数名
     */
    void deleteConstantParam(String parmNm);

    /**
     * 查询公共参数
     *
     * @param parmNm 参数名
     * @return
     */
    ConstantParamDo queryConstantParam(String parmNm);

    /**
     * 查询公共参数列表
     *
     * @param pageNum  页码数
     * @param pageSize 页面展示数
     * @return
     */
    List<ConstantParamDo> queryAllConstantParams(int pageNum, int pageSize);

    /**
     * 查询常量组
     *
     * @param parmNm
     * @return
     */
    List<ConstantParamDo> queryConstantParamGroup(String parmNm);
}
