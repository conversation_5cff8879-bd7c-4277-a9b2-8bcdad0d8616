/*
 * @ClassName INoticPulblicDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-10 10:27:11
 */
package com.hisun.lemon.cmm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.cmm.entity.NoticPulblicDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface INoticPulblicDao extends BaseDao<NoticPulblicDO> {
    
    List<NoticPulblicDO> getListByCondition(NoticPulblicDO noticPulblicDO);
}