/*
 * @ClassName IPushClientDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-11 11:19:30
 */
package com.hisun.lemon.cmm.dao;

import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.cmm.entity.PushClientDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IPushClientDao extends BaseDao<PushClientDO> {
    
    PushClientDO get(Map<String, String> map);
    
    int insertOrUpdate(PushClientDO pushClientDO);

    PushClientDO getLoginTime(Map<String, String> map);

    PushClientDO getClientInfo(@Param("userId") String userId, @Param("clientId") String clientId);

    int updateClientInfo(PushClientDO pushClientDO);
}