/*
 * @ClassName PushParamDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-11 16:37:30
 */
package com.hisun.lemon.cmm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class PushParamDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private String id;
    /**
     * @Fields type 应用类型
     */
    private String type;
    /**
     * @Fields appId 应用ID
     */
    private String appId;
    /**
     * @Fields appKey 应用KEY
     */
    private String appKey;
    /**
     * @Fields masterSecret 应用密钥
     */
    private String masterSecret;
    /**
     * @Fields stats 状态0:无效 1:有效
     */
    private String stats;
    /**
     * @Fields env 参数环境
     */
    private String env;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getMasterSecret() {
        return masterSecret;
    }

    public void setMasterSecret(String masterSecret) {
        this.masterSecret = masterSecret;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

}