/*
package com.hisun.lemon.cmm.api.wechat;

import com.hisun.lemon.cmm.Properties.WeChatProperties;
import com.hisun.lemon.cmm.rsp.TokenRsp;
import com.hisun.lemon.cmm.constants.MsgCdEnum;
import com.hisun.lemon.cmm.req.TokenReq;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

*/
/**
 * 微信支付接口，新接口
 *//*

@Component
public class WeChatPayApi {
    @Resource
    private WeChatApi weChatApi;
    @Resource
    private WeChatProperties weChatProperties;

    private static final Logger logger = LoggerFactory.getLogger(WeChatPayApi.class);



    */
/**
     * 页面授权
     *//*

    public TokenRsp tokenApply(TokenReq tokenReq) {
        //初始化请求报文
        //TokenReq tokenReq1 = new TokenReq();
        tokenReq.setAppid(weChatProperties.getAppId());
        tokenReq.setCode(tokenReq.getCode());
        tokenReq.setSecret(weChatProperties.getSecret());

        //调用微信接口
        try {
            TokenRsp tokenRsp = weChatApi.doSend(tokenReq, "Token");
            if (JudgeUtils.isNull(tokenRsp)) {
                logger.info("WeChatPayNewApi.tokenApply() 微信页面授权接口返回空报文");
                return null;
            }
            String openId = tokenRsp.getOpenid();
            if (JudgeUtils.isNotNull(openId)) {
                return tokenRsp;
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.tokenApply() Exception : ", e);
            LemonException.throwBusinessException(MsgCdEnum.WECHAT_CONNECT_FAIL.getMsgCd());
        }
        return null;
    }



}
*/
