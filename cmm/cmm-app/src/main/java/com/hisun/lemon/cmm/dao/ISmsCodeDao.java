/*
 * @ClassName ISmsCodeDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-18 11:44:11
 */
package com.hisun.lemon.cmm.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.cmm.entity.SmsCodeDO;
import com.hisun.lemon.framework.dao.BaseDao;

@Mapper
public interface ISmsCodeDao extends BaseDao<SmsCodeDO> {
    
    SmsCodeDO getByCondition(SmsCodeDO smsCodeDO);
    
    int updateByCondition(@Param("valueDO")SmsCodeDO updValueSmsCodeDO, @Param("conditionDO")SmsCodeDO updConditionSmsCodeDO);
}