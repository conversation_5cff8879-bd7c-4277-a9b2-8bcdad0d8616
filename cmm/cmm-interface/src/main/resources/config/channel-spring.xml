<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:channel="http://www.hisun.com/schema/channel"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.hisun.com/schema/channel http://www.hisun.com/schema/channel/channel.xsd">
        

    
    <channel:service name="WEIXIN" charset="UTF-8" >
        <!-- 
        <channel:connector protocol="tcp_long" address="127.0.0.1:7778" timeout="60000" waitTimeout="100000" heartbeatCheck-ref="icbcHeartbeat" poolMaxConnections="5" />
         -->
        <!-- http://*************/vsapi/VspApiServlet -->
        
        <!-- 页面授权  -->
        <channel:connector id="token" protocol="http" url="https://api.weixin.qq.com/sns/oauth2/access_token" default="false" 
        dateTypeName="QUERY_STRING" requestMode="GET" charset="UTF-8" />

        <channel:container>
            <!-- 页面授权 -->    
            <channel:processor name="Token" marshal-class="com.hisun.lemon.cmm.req.TokenReq"
                               unmarshal-class="com.hisun.lemon.cmm.rsp.TokenRsp" connector-id="token" />
            <!-- 此处的handler-filter对全部processor生效 -->
            <channel:handler-filter class="com.hisun.channel.service.child.filter.MonitorHandlerFilter" />
            <!-- <channel:handler-filter class="com.hisun.channel.service.child.filter.MessageContext" /> --> 
        </channel:container>
    </channel:service>
</beans>