package com.hisun.lemon.cmm.dto;

/**
 * 
 * @Description 系统密钥查询响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午2:33:36
 * @version V1.0
 */
public class SystemKeyRspDTO {
    /** 密钥信息 */
    private String key;
    /** 算法 */
    private String algorithm;
    /** 渠道信息 */
    private String channel;
    /** 版本信息 */
    private String versions;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getVersions() {
        return versions;
    }

    public void setVersions(String versions) {
        this.versions = versions;
    }
}
