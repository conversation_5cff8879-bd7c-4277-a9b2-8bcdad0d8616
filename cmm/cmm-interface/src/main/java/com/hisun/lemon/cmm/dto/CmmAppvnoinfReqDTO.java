/*
 * @ClassName CmmAppvnoinfDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-06 11:54:08
 */
package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

public class CmmAppvnoinfReqDTO {
    /**
     * @Fields channel 渠道信息
     */
    @ApiModelProperty(name = "channel", value = "渠道信息 USRA:安卓用户app, USRI:苹果用户app , MERA:安卓商户app, MERI:苹果商户app")
    @Pattern(regexp = "USRA|USRI|MERA|MERI", message = "CMM50002")
    @NotNull(message = "CMM50001")
    private String channel;

    /**
     * @Fields appVersion 应用版本号
     */
    @ApiModelProperty(name = "appVersion", value = "当前版本号")
    @NotNull(message = "CMM50003")
    private String appVersion;

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }
}