package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;


public class ArticleDTO {

    @ApiModelProperty(name = "id", value = "id", dataType = "String")
    private String id;

    @ApiModelProperty(name = "title", value = "标题", dataType = "String")
    private String title;

    @ApiModelProperty(name = "description", value = "描述", dataType = "String")
    private String description;

    @ApiModelProperty(name = "content", value = "内容详情", dataType = "String")
    private String content;

    @ApiModelProperty(name = "url", value = "链接", dataType = "String")
    private String url;

    @ApiModelProperty(name = "image", value = "图片", dataType = "String")
    private String image;

    @ApiModelProperty(name = "publishedAt", value = "发布于", dataType = "String")
    private String publishedAt;

    @ApiModelProperty(name = "source", value = "来源", dataType = "String")
    private SourceDTO source;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(String publishedAt) {
        this.publishedAt = publishedAt;
    }

    public SourceDTO getSource() {
        return source;
    }

    public void setSource(SourceDTO source) {
        this.source = source;
    }
}
