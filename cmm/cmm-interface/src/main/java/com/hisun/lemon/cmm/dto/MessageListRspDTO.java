package com.hisun.lemon.cmm.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 消息列表响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class MessageListRspDTO {

    /** 记录总数 */
    @ApiModelProperty(name = "totalNumber", value = "记录总数")
    private long totalNumber;

    /** 记录总数 */
    @ApiModelProperty(name = "recordNumber", value = "记录总数")
    private long recordNumber;

    /** 记录列表 */
    @ApiModelProperty(name = "recordList", value = "记录列表")
    private List<MessageDTO> recordList;
    /** 未确认总数 */
    @ApiModelProperty(name = "unacknowledgeNumber", value = "未确认总数")
    private int unacknowledgeNumber;

    /** 记录总页数 */
    @ApiModelProperty(name = "recordPgNumber", value = "记录总页数")
    private int recordPgNumber;


    /** 记录总页数 */
    @ApiModelProperty(name = "curPgNumber", value = "当前页面")
    private int curPgNumber;

    public int getRecordPgNumber() {
        return recordPgNumber;
    }

    public void setRecordPgNumber(int recordPgNumber) {
        this.recordPgNumber = recordPgNumber;
    }

    public int getCurPgNumber() {
        return curPgNumber;
    }

    public void setCurPgNumber(int curPgNumber) {
        this.curPgNumber = curPgNumber;
    }

    public long getTotalNumber() {
        return totalNumber;
    }

    public void setTotalNumber(long totalNumber) {
        this.totalNumber = totalNumber;
    }

    public List<MessageDTO> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<MessageDTO> recordList) {
        this.recordList = recordList;
    }

    public int getUnacknowledgeNumber() {
        return unacknowledgeNumber;
    }

    public void setUnacknowledgeNumber(int unacknowledgeNumber) {
        this.unacknowledgeNumber = unacknowledgeNumber;
    }

    public long getRecordNumber() {
        return recordNumber;
    }

    public void setRecordNumber(long recordNumber) {
        this.recordNumber = recordNumber;
    }
}
