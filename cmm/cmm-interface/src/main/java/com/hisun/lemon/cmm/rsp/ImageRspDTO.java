package com.hisun.lemon.cmm.rsp;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/1/22 15:16
 */
public class ImageRspDTO {
	
    @ApiModelProperty(name = "imgHerf",value = "图片地址", dataType = "String")
    private String imgHerf;

    public String getImgHerf() {
        return imgHerf;
    }

    public void setImgHerf(String imgHerf) {
        this.imgHerf = imgHerf;
    }

    @Override
    public String toString() {
        return "ImageRspDTO{" +
                "imgHerf='" + imgHerf + '\'' +
                '}';
    }
}
