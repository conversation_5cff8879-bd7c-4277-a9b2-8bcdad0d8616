package com.hisun.lemon.cmm.dto;

import java.time.LocalDate;

import javax.validation.constraints.NotNull;

/**
 * @Description 短信模版传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class SmsTemplateReqDTO {

    /** 短信模版ID */
    @NotNull(message = "CMM10005")
    private String id;
    /** 短信类型 */
    private String type;
    /** 短信级别 */
    private String lvl;
    /** 替换变量 */
    private String replaceField;
    /** 柬文短信模版 */
    private String templateContentKh;
    /** 中文短信模版 */
    private String templateContentCn;
    /** 英文短信模版 */
    private String templateContentEn;
    /** 模版状态 */
    private String stats;
    /** 模版生效日期 */
    private LocalDate effDate;
    /** 模版失效日期 */
    private LocalDate expDate;
    /** 操作员ID */
    private String oprId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLvl() {
        return lvl;
    }

    public void setLvl(String lvl) {
        this.lvl = lvl;
    }

    public String getReplaceField() {
        return replaceField;
    }

    public void setReplaceField(String replaceField) {
        this.replaceField = replaceField;
    }

    public String getTemplateContentKh() {
        return templateContentKh;
    }

    public void setTemplateContentKh(String templateContentKh) {
        this.templateContentKh = templateContentKh;
    }

    public String getTemplateContentCn() {
        return templateContentCn;
    }

    public void setTemplateContentCn(String templateContentCn) {
        this.templateContentCn = templateContentCn;
    }

    public String getTemplateContentEn() {
        return templateContentEn;
    }

    public void setTemplateContentEn(String templateContentEn) {
        this.templateContentEn = templateContentEn;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

}
