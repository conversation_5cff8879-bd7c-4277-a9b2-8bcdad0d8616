package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * @Description 消息推送ClientID设置请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午2:33:36
 * @version V1.0
 */
public class MessageClientReqDTO {
    /** 用户类型 */
    @ApiModelProperty(name = "userType", value = "用户类型(personal:用户的 merchant：商户的)", required = true)
    @Pattern(regexp = "personal|merchant", message = "CMM10022")
    private String userType;
    /** 消息ClientID */
    @ApiModelProperty(name = "clientId", value = "消息推送ClientID", required = true)
    @NotNull(message = "CMM10024")
    @Length(max = 32)
    private String clientId;

    @ApiModelProperty(name = "loginId", value = "登录id", required = true)
    private String loginId;

    /**
     * 在线标志  0:登入 1：登出
     */
    @ApiModelProperty(name = "loginFlg", value = "在线标志  0:登入(为空默认) 1：登出", required = true)
    @Length(max = 1)
    private String loginFlg;

    public String getLoginFlg() {
        return loginFlg;
    }

    public void setLoginFlg(String loginFlg) {
        if(loginFlg == null || "".equals(loginFlg)){
            this.loginFlg = "0";
        }
        this.loginFlg = loginFlg;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }
}
