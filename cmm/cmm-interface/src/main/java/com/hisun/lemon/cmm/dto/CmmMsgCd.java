package com.hisun.lemon.cmm.dto;

/**
 * 错误码
 * <AUTHOR>
 * @date 2019/1/10 15:35
 * @version V1.0
 */
public enum CmmMsgCd {
	/**
     * MOC操作成功
     */
    MSG_MOC_SUCCESS("MOC00000", "操作成功"),
	/**
	 * MOC操作失败
	 */
	MSG_MOC_FILD("SYS00001", "操作失败"),
	/**
	 * 操作员账号不符合要求
	 */
	MSG_MOC11001("MOC11001", "操作员账号不符合要求"),

	/**
	 * 操作员姓名不符合要求
	 */
	MSG_MOC11002("MOC11002", "操作员姓名不符合要求"),

	/**
	 * 机构编码不符合要求
	 */
	MSG_MOC11003("MOC11003", "机构编码不符合要求"),

	/**
	 * 操作员手机号码不符合要求
	 */
	MSG_MOC11004("MOC11004", "操作员手机号码不符合要求"),

	/**
	 * 图片地址不符合要求
	 */
	MSG_MOC11005("MOC11005", "操作员办公电话不符合要求"),

	/**
	 * 操作员邮箱不符合要求
	 */
	MSG_MOC11006("MOC11006", "操作员邮箱不符合要求"),

	/**
	 * 操作员密码不符合要求
	 */
	MSG_MOC11007("MOC11007", "操作员密码不符合要求"),

	/**
	 * 操作员ID不符合要求
	 */
	MSG_MOC11008("MOC11008", "操作员ID不符合要求"),
	/**
	 * 操作员旧密码不符合要求
	 */
	MSG_MOC11009("MOC11009", "操作员旧密码不符合要求"),
	/**
	 * 操作员ID集合不符合要求
	 */
	MSG_MOC11010("MOC11010", "操作员ID集合不符合要求"),
	/**
	 * 角色ID不符合要求
	 */
	MSG_MOC13001("MOC13001", "角色ID不符合要求"),
	/**
	 * 角色ID集合不符合要求
	 */
	MSG_MOC13002("MOC13002", "角色ID集合不符合要求"),
	/**
	 * 角色名称不符合要求
	 */
	MSG_MOC13003("MOC13003", "角色名称不符合要求"),
	/**
	 * 权限类型不符合要求
	 */
	MSG_MOC13004("MOC13004", "权限类型不符合要求"),
	/**
	 * 部门ID不符合要求
	 */
	MSG_MOC14001("MOC14001", "部门ID不符合要求"),
	/**
	 * 部门编号不符合要求
	 */
	MSG_MOC14002("MOC14002", "部门编号不符合要求"),
	/**
	 * 部门名称不符合要求
	 */
	MSG_MOC14003("MOC14003", "部门名称不符合要求"),
    /**
	 * 权限类型不符合要求
	 */
	MSG_MOC14004("MOC14004", "上级部门ID不符合要求"),
	/**
	 * 权限类型不符合要求
	 */
	MSG_MOC14005("MOC14005", "所属机构不符合要求"),
    /**
     * 权限类型不符合要求
     */
    MSG_MOC14006("MOC14006", "该部门存在下级部门"),
	/**
	 * 岗位ID集合不符合要求
	 */
	MSG_MOC15001("MOC15001", "岗位ID集合不符合要求"),
	/**
	 * 岗位名称不符合要求
	 */
	MSG_MOC15002("MOC15002", "岗位名称不符合要求"),
	/**
	 * 日志ID不符合要求
	 */
	MSG_MOC16001("MOC16001", "日志ID不符合要求"),
	/**
	 * 操作前内容不符合要求
	 */
	MSG_MOC16002("MOC16002", "操作前内容不符合要求"),
	/**
	 * 操作后内容不符合要求
	 */
	MSG_MOC16003("MOC16003", "操作后内容不符合要求"),
	/**
	 * 权限类型不符合要求
	 */
	MSG_MOC16004("MOC16004", "权限类型不符合要求"),
	/**
	 * 模块名不符合要求
	 */
	MSG_MOC16005("MOC16005", "模块名不符合要求"),
	/**
	 * 操作类型不符合要求
	 */
	MSG_MOC16006("MOC16006", "操作类型不符合要求"),

    /**
     * 操作类型不符合要求
     */
    MSG_MOC16007("MOC16007", "操作员无此权限"),
	/**
	 * 不存在该操作员
	 */
	MSG_MOC21001("MOC21001", "不存在该操作员"),
	/**
	 * 操作员账号重复
	 */
	MSG_MOC21002("MOC21002", "操作员账号重复"),
	/**
	 * 密码错误次数超过五次
	 */
	MSG_MOC21003("MOC21003", "密码错误次数超过五次"),
	/**
	 * 新旧密码不一致
	 */
	MSG_MOC21004("MOC21004", "旧密码验证失败"),
	/**
	 * 密码错误
	 */
	MSG_MOC21005("MOC21005", "密码错误"),
	/**
	 * 操作员邮箱重复
	 */
	MSG_MOC21006("MOC21006", "操作员邮箱重复"),
    /**
     * 操作员已被锁定
     */
    MSG_MOC21007("MOC21007", "操作员已被锁定"),
    /**
     * 操作员已失效
     */
    MSG_MOC21008("MOC21008", "操作员已失效"),
	/**
	 * 角色信息不存在
	 */
	MSG_MOC23001("MOC23001", "角色信息不存在"),
	/**
	 * 该角色下存在权限
	 */
	MSG_MOC23002("MOC23002", "该角色下存在权限"),
	/**
	 * 部门编号重复
	 */
	MSG_MOC24001("MOC24001", "部门编号重复"),
    /**
     * 部门名称已存在
     */
    MSG_MOC24003("MOC24003", "部门名称已存在"),
	/**
	 * 不存在该部门信息
	 */
	MSG_MOC24002("MOC24002", "不存在该部门信息"),
	/**
	 * 不存在该岗位信息
	 */
	MSG_MOC25001("MOC25001", "不存在该岗位信息"),
    /**
     * 岗位编号已存在
     */
    MSG_MOC25002("MOC25002", "岗位编号已存在"),
    /**
     * 岗位名称已存在
     */
    MSG_MOC25003("MOC25003", "岗位名称已存在"),

	MSG_MOC25004("MOC25004", "该部门岗位已存在"),
	/**
	 * 不存在该日志信息
	 */
	MSG_MOC26001("MOC26001", "不存在该日志信息"),
	/**
	 * 不存在该数据字典信息
	 */
	MSG_MOC27001("MOC27001","不存在该数据字典信息" ),

	MSG_MOC52001("MOC52001","图片上传失败"),
	MSG_MOC52111("MOC52111","图片上传成功"),

	MSG_MOC52002("MOC52002","修改手工入金流水信息失败"),
	MSG_MOC52003("MOC52003","该附件已被删除"),
	MSG_MOC52004("MOC52004","已被发起,不允许重复发起"),
	MSG_MOC52005("MOC52005","验证码为空"),
	MSG_MOC52006("MOC52006","验证码不正确"),
	MSG_MOC52007("MOC52007", "密码连续输错五次,当前帐号已被锁定"),
	MSG_MOC52008("MOC52008", "当前帐号已被锁定"),
	MSG_MOC52009("MOC52009", "原密码不正确"),
	MSG_MOC52010("MOC52010", "当前密码已输错三次,输错5次将被锁定"),
	MSG_MOC52011("MOC52011", "当前操作员被锁定,请联系管理员"),
	MSG_MOC52012("MOC52012", "当前密码已输错四次,输错5次将被锁定"),
	MSG_MOC25013("MOC25013", "帐号或密码错误"),
	MSG_MOC52014("MOC52014","文件格式错误"),

	MSG_MOC62001("MOC62001", "模版信息不存在"),
	MSG_MOC62002("MOC62002", "导出Excel失败"),
    MSG_MOC62003("MOC62003", "建行公钥同步系统异常"),

	MSG_MOC72001("MOC72001", "入金凭证信息不存在"),
	MSG_MOC72002("MOC72002", "出金订单信息不存在"),

	MSG_MOC72003("MOC72003", "设备ID重复"),

	MSG_MOC72004("MOC72004", "该笔订单正处于系统自动轮询中，请稍后再试！"),

	MSG_MOC99999("MOC99999", "系统异常");
	/**
	 * 错误码
	 */
	private String msgCd;

	/**
	 * 错误码描述
	 */
	private String msgInfo;

	private CmmMsgCd(String msgCd, String msgInfo) {
	    this.msgCd = msgCd;
	    this.msgInfo = msgInfo;
	}
	
	public String getMsgCd() {
	        return msgCd;
	}
	
	public String getMsgInfo() {
	        return msgInfo;
	}

    public static String getMsg(String msgCd) {
        for(CmmMsgCd mocMsgCd:CmmMsgCd.values()) {
            if(msgCd.equals(mocMsgCd.msgCd)) {
                return mocMsgCd.msgInfo;
            }
        }
        return "";
    }
}
