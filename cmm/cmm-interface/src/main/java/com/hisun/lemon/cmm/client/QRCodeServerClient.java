package com.hisun.lemon.cmm.client;

import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description  二维码服务接口
 * <AUTHOR>
 * @date 2017年7月7日 下午3:01:38 
 * @version V1.0
 */
@FeignClient("CMM")
public interface QRCodeServerClient {
    
    /**
     * @Description 付款码校验
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/qrcode/payment/check")
    public GenericRspDTO<PaymentCodeCheckRspDTO> paymentCodeCheck(@Validated @RequestBody GenericDTO<PaymentCodeCheckReqDTO> reqDTO);

    @PostMapping("/qrcode/tmsMakeQrcode")
    public GenericRspDTO<TmsMakeQrcodeRspDTO> tmsMakeQrcode(@Validated @RequestBody GenericDTO<TmsMakeQrcodeReqDTO> reqDTO) ;

    /**
     * 生成订单二维码
     * @param reqDTO
     * @return
     */
    @PostMapping("/qrcode/generate")
    public GenericRspDTO<QRCodeGenerateRspDTO> qrCodeGenerate(@Validated @RequestBody GenericDTO<QRCodeGenerateReqDTO> reqDTO) ;
}
