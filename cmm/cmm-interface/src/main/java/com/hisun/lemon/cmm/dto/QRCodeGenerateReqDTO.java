package com.hisun.lemon.cmm.dto;

import java.math.BigDecimal;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 二维码生成请求传输对象
 * <AUTHOR>
 * @date 2017年7月12日 下午2:29:16
 * @version V1.0
 */
public class QRCodeGenerateReqDTO {
    /** 二维码类型 */
    @ApiModelProperty(name = "qrCodeType", value = "二维码类型(PA:个人账户 MA:商户账户 MO:商户订单 AG:聚合支付)", required = true)
    @Pattern(regexp = "PA|MA|MO|AG", message = "CMM10012")
    private String qrCodeType;
    /** 交易订单 */
    @ApiModelProperty(name = "orderNo", value = "交易订单")
    @Length(max = 32)
    private String orderNo;
    /** 交易金额 */
    @ApiModelProperty(name = "tradeAmt", value = "交易金额")
    private BigDecimal tradeAmt;
    /** 备注 */
    @ApiModelProperty(name = "remark", value = "备注")
    @Length(max = 20)
    private String remark;

    public String getQrCodeType() {
        return qrCodeType;
    }

    public void setQrCodeType(String qrCodeType) {
        this.qrCodeType = qrCodeType;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
