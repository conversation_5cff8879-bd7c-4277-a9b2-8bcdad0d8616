package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * @Description 短信验证码下发请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午2:33:36
 * @version V1.0
 */
public class SmsCodeReqDTO {
    /** 手机号码 */
    @ApiModelProperty(name = "mblNo", value = "手机号码", required = true)
    @NotNull(message = "CMM30001")
    @Length(max = 20)
    private String mblNo;
    /** 短信验证码类型 */
    @ApiModelProperty(name = "smsCodeType", value = "短信类型", required = true)
    @NotNull(message = "CMM30002")
    @Length(max = 3)
    private String smsCodeType;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getSmsCodeType() {
        return smsCodeType;
    }

    public void setSmsCodeType(String smsCodeType) {
        this.smsCodeType = smsCodeType;
    }

}
