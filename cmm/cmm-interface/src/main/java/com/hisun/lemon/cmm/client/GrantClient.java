package com.hisun.lemon.cmm.client;

import com.hisun.lemon.cmm.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description  授权信息查询
 * <AUTHOR>
 * @date 2017年11月20日 上午 10:01:38
 * @version V1.0
 */
@FeignClient("CMM")
public interface GrantClient {
    
    /**
     * @Description 微信授权信息查询
     * @param state
     * @return
     */
    @GetMapping("/cmm/getGrantInfo/{state}")
    public GenericRspDTO<PayWeChatInfoRspDTO> queryWeChatInfo(@PathVariable("state") String state);

    @PostMapping("/cmm/grant")
    public GenericRspDTO<NoBody> getGrant(@Validated @RequestBody GenericDTO<PayWeChatReqDTO> reqDTO);
}
