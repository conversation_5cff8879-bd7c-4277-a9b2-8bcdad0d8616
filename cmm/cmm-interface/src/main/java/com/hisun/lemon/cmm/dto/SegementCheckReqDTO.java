package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 号段检查请求参数对象
 * <AUTHOR>
 * @date 2017年7月27日 上午9:51:34
 * @version V1.0
 */
public class SegementCheckReqDTO {
    /** 手机号码 */
    @ApiModelProperty(name = "mblNo", value = "手机号码(+86-18684830733)", required = true)
    @NotNull(message = "CMM10001")
    @Length(max = 20)
    private String mblNo;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }
}
