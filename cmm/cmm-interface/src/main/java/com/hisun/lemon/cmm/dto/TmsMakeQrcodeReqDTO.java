package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Pattern;

/**
 * Created by chen on 10/27 0027.
 */
public class TmsMakeQrcodeReqDTO {
    /** 二维码类型 */
    @ApiModelProperty(name = "qrCodeType", value = " MA:商户账户", required = true)
    @Pattern(regexp = "PA|MA|MO|AG", message = "CMM10012")
    private String qrCodeType;

    @ApiModelProperty(name = "userId", value = "商户id", required = true)
    private String userId;

    @ApiModelProperty(name = "loginId", value = "登录id", required = true)
    private String loginId;

    /** 备注 */
    @ApiModelProperty(name = "remark", value = "备注")
    @Length(max = 20)
    private String remark;

    public String getQrCodeType() {
        return qrCodeType;
    }

    public void setQrCodeType(String qrCodeType) {
        this.qrCodeType = qrCodeType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
