package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * @Description 登陆信息查询响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class AppInfoRspDTO {

    /** 登陆流水号 */
    @ApiModelProperty(name = "loginJrnNo", value = "登陆流水号")
    private String loginJrnNo;
    /** 内部用户号 */
    @ApiModelProperty(name = "userId", value = "内部用户号")
    private String userId;
    /** 用户类型 */
    @ApiModelProperty(name = "userType", value = "用户类型")
    private String userType;
    /** 客户端版本 */
    @ApiModelProperty(name = "versions", value = "客户端版本")
    private String versions;
    /** 客户端版本 */
    @ApiModelProperty(name = "language", value = "客户端当前语言")
    private String language;
    /** 交易渠道 */
    @ApiModelProperty(name = "channel", value = "交易渠道")
    private String channel;
    /** 下载渠道 */
    @ApiModelProperty(name = "downloadChannel", value = "下载渠道")
    private String downloadChannel;
    /** 操作系统版本 */
    @ApiModelProperty(name = "osVersion", value = "操作系统版本")
    private String osVersion;
    /** 终端ID */
    @ApiModelProperty(name = "termId", value = "终端ID")
    private String termId;
    /** 终端类型 */
    @ApiModelProperty(name = "termType", value = "终端类型")
    private String termType;
    /** 登录时间戳 */
    @ApiModelProperty(name = "tmSmp", value = "登录时间戳")
    private LocalDateTime tmSmp;


    public String getLoginJrnNo() {
        return loginJrnNo;
    }

    public void setLoginJrnNo(String loginJrnNo) {
        this.loginJrnNo = loginJrnNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getVersions() {
        return versions;
    }

    public void setVersions(String versions) {
        this.versions = versions;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDownloadChannel() {
        return downloadChannel;
    }

    public void setDownloadChannel(String downloadChannel) {
        this.downloadChannel = downloadChannel;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getTermId() {
        return termId;
    }

    public void setTermId(String termId) {
        this.termId = termId;
    }

    public String getTermType() {
        return termType;
    }

    public void setTermType(String termType) {
        this.termType = termType;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

}
