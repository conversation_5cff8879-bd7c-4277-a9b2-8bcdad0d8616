package com.hisun.lemon.cmm.client;

/**
 * TODO 此处填写功能说明
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/9 19:50
 */
public interface EmailServerClient {

    boolean sendGeneralEmail(String subject, String content, String... to);

    boolean sendHtmlEmail(String subject, String content, String... to);

    boolean sendAttachmentsEmail(String subject, String content, String[] to, String[] filePaths);

    boolean sendInlineResourceEmail(String subject, String content, String to, String rscPath, String rscId);
}
