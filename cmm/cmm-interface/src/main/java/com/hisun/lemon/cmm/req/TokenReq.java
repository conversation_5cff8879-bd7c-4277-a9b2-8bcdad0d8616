/*
package com.hisun.lemon.cmm.req;


import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Plain.Form;



*/
/**
 * 网页授权凭证请求报文
 * 
 * <AUTHOR>
 *
 *//*

@Plain(form = Form.QUERY_STRING)
public class TokenReq {

	*/
/**
	 * 公共平台标识
	 *//*

	@Item
	private String appid;

	*/
/**
	 * 密钥
	 *//*

	@Item
	private String secret;

	@Item
	private String code;

	@Item
	private String grant_type;

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getSecret() {
		return secret;
	}

	public void setSecret(String secret) {
		this.secret = secret;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getGrant_type() {
		if (StringUtils.isEmpty(grant_type)) {
			grant_type = "authorization_code";
		}
		return grant_type;
	}

	public void setGrant_type(String grant_type) {
		this.grant_type = grant_type;
	}

}
*/
