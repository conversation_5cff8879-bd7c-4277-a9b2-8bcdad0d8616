package com.hisun.lemon.cmm.dto;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 消息响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class MessageDTO {

    /** 消息ID */
    @ApiModelProperty(name = "id", value = "ID")
    private String id;
    /** 类型 */
    @ApiModelProperty(name = "type", value = "类型(message:消息 campaign:活动)")
    private String type;
    /** 用户类型 */
    @ApiModelProperty(name = "userType", value = "用户类型(personal:用户的 merchant:商户的)")
    private String userType;
    /** 消息状态 */
    @ApiModelProperty(name = "stats", value = "状态")
    private String stats;
    /** 消息标题 */
    @ApiModelProperty(name = "title", value = "标题")
    private String title;
    /** 消息标题 */
    @ApiModelProperty(name = "content", value = "内容")
    private String content;
    /** 系统时间 */
    @ApiModelProperty(name = "systemDateTime", value = "系统时间")
    private LocalDateTime systemDateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public LocalDateTime getSystemDateTime() {
        return systemDateTime;
    }

    public void setSystemDateTime(LocalDateTime systemDateTime) {
        this.systemDateTime = systemDateTime;
    }
}
