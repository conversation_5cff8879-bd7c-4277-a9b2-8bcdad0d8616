package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 登陆信息查询请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class AppInfoReqDTO {

    /** 用户号 **/
    @ApiModelProperty(name = "userId", value = "用户号", required = true)
    @NotNull(message = "CMM10007")
    @Length(max = 20)
    private String userId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
    
}
