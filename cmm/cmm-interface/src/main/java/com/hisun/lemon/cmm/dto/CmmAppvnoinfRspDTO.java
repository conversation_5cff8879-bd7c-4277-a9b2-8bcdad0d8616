/*
 * @ClassName CmmAppvnoinfDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-06 11:54:08
 */
package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;

public class CmmAppvnoinfRspDTO {
    /**
     * @Fields appNo 版本号
     */
    @ApiModelProperty(name = "appNo", value = "版本号")
    private String appNo;
    /**
     * @Fields appTyp app 类型 0用户,1商户
     */
    @ApiModelProperty(name = "appTyp", value = "app 类型 0用户,1商户")
    private String appTyp;
    /**
     * @Fields appSys 应用类型 iOS, android
     */
    @ApiModelProperty(name = "appSys", value = "appSys 应用类型 iOS, android")
    private String appSys;
    /**
     * @Fields appSize 应用文件大小
     */
    @ApiModelProperty(name = "appSize", value = "应用文件大小")
    private String appSize;
    /**
     * @Fields appVersion 应用版本号
     */
    @ApiModelProperty(name = "appVersion", value = "应用版本号")
    private String appVersion;
    /**
     * @Fields updateinf 应用版本更新内容
     */
    @ApiModelProperty(name = "updateinf", value = "应用版本更新内容")
    private String updateinf;
    /**
     * @Fields appUrl 应用下载路径
     */
    @ApiModelProperty(name = "appUrl", value = "应用下载路径")
    private String appUrl;
    /**
     * @Fields isTrue 是否强制更新 0强制,1非强制
     */
    @ApiModelProperty(name = "isTrue", value = "是否强制更新 0强制,1非强制")
    private String isTrue;
    /**
     * @Fields tmSmp 时间戳
     */
    @ApiModelProperty(name = "tmSmp", value = "时间戳")
    private String tmSmp;

    public String getAppNo() {
        return appNo;
    }

    public void setAppNo(String appNo) {
        this.appNo = appNo;
    }

    public String getAppTyp() {
        return appTyp;
    }

    public void setAppTyp(String appTyp) {
        this.appTyp = appTyp;
    }

    public String getAppSys() {
        return appSys;
    }

    public void setAppSys(String appSys) {
        this.appSys = appSys;
    }

    public String getAppSize() {
        return appSize;
    }

    public void setAppSize(String appSize) {
        this.appSize = appSize;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getUpdateinf() {
        return updateinf;
    }

    public void setUpdateinf(String updateinf) {
        this.updateinf = updateinf;
    }

    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }

    public String getIsTrue() {
        return isTrue;
    }

    public void setIsTrue(String isTrue) {
        this.isTrue = isTrue;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }
}