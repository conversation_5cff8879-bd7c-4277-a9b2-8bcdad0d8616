package com.hisun.lemon.cmm.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description Banner列表响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class BannerListRspDTO {

    /** 记录总数 */
    @ApiModelProperty(name = "recordNumber", value = "记录总数")
    private int recordNumber;
    /** 记录列表 */
    @ApiModelProperty(name = "recordList", value = "记录列表")
    private List<BannerRspDTO> recordList;

    public int getRecordNumber() {
        return recordNumber;
    }

    public void setRecordNumber(int recordNumber) {
        this.recordNumber = recordNumber;
    }

    public List<BannerRspDTO> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<BannerRspDTO> recordList) {
        this.recordList = recordList;
    }
}
