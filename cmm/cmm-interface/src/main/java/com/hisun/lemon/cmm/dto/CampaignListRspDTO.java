package com.hisun.lemon.cmm.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 活动列表响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class CampaignListRspDTO {

    /** 记录总数 */
    @ApiModelProperty(name = "recordNumber", value = "记录总数")
    private int recordNumber;
    /** 记录列表 */
    @ApiModelProperty(name = "recordList", value = "记录列表")
    private List<CampaignDTO> recordList;
    /** 未确认总数 */
    @ApiModelProperty(name = "unacknowledgeNumber", value = "未确认总数")
    private int unacknowledgeNumber;

    public int getRecordNumber() {
        return recordNumber;
    }

    public void setRecordNumber(int recordNumber) {
        this.recordNumber = recordNumber;
    }

    public List<CampaignDTO> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<CampaignDTO> recordList) {
        this.recordList = recordList;
    }

    public int getUnacknowledgeNumber() {
        return unacknowledgeNumber;
    }

    public void setUnacknowledgeNumber(int unacknowledgeNumber) {
        this.unacknowledgeNumber = unacknowledgeNumber;
    }

}
