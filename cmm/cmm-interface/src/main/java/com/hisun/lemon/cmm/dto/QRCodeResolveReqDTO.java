package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 二维码解析请求传输对象
 * <AUTHOR>
 * @date 2017年7月15日 下午3:00:50
 * @version V1.0
 */
public class QRCodeResolveReqDTO {
    /** 二维码参数 */
    @ApiModelProperty(name = "urlParams", value = "二维码参数信息", required = true)
    @NotNull(message = "CMM10015")
    @Length(max = 1024)
    private String urlParams;

    public String getUrlParams() {
        return urlParams;
    }

    public void setUrlParams(String urlParams) {
        this.urlParams = urlParams;
    }
}
