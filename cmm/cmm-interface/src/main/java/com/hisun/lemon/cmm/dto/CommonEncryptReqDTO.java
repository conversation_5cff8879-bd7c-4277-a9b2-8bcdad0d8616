package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * @Description 公共加解密请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午2:33:36
 * @version V1.0
 */
public class CommonEncryptReqDTO {

    /** 加解密数据 */
    @ApiModelProperty(name = "data", value = "加解密数据", required = true)
    @NotNull(message = "CMM10025")
    private String data;
    /** 加解密类型 */
    @ApiModelProperty(name = "type", value = "加解密类型 encrypt:加密 decrypt:解密", required = true)
    @NotNull(message = "CMM10026")
    private String type;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}
