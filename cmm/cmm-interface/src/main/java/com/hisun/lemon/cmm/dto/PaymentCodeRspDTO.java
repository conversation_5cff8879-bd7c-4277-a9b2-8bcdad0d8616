package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 付款码生成响应传输对象
 * <AUTHOR>
 * @date 2017年7月12日 下午2:29:16
 * @version V1.0
 */
@ApiModel(value = "PaymentCodeRspDTO", description = "付款码生成响应参数对象")
public class PaymentCodeRspDTO {
    /** 付款条码信息 */
    @ApiModelProperty(name = "payBarCode", value = "付款条码")
    private String payBarCode;
    @ApiModelProperty(name = "payQRCode", value = "付款二维码")
    /** 付款二维码信息 */
    private String payQRCode;

    public String getPayBarCode() {
        return payBarCode;
    }

    public void setPayBarCode(String payBarCode) {
        this.payBarCode = payBarCode;
    }

    public String getPayQRCode() {
        return payQRCode;
    }

    public void setPayQRCode(String payQRCode) {
        this.payQRCode = payQRCode;
    }
}
