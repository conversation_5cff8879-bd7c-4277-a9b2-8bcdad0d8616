package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 公告信息响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class NoticeRspDTO {

    /** 公告ID */
    @ApiModelProperty(name = "noticeId", value = "公告ID")
    private String noticeId;
    /** 公告标题 */
    @ApiModelProperty(name = "noticeTitle", value = "公告标题")
    private String noticeTitle;
    /** 公告内容 */
    @ApiModelProperty(name = "noticeContent", value = "公告内容")
    private String noticeContent;

    public String getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(String noticeId) {
        this.noticeId = noticeId;
    }

    public String getNoticeTitle() {
        return noticeTitle;
    }

    public void setNoticeTitle(String noticeTitle) {
        this.noticeTitle = noticeTitle;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }
}
