package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 活动列表请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class CampaignListReqDTO {
    /** 当前页码 */
    @ApiModelProperty(name = "pageNum", value = "当前页码", required = true)
    @NotNull(message = "CMM19998")
    private Integer pageNum;
    /** 每页大小 */
    @ApiModelProperty(name = "pageSize", value = "每页大小", required = true)
    @NotNull(message = "CMM19999")
    private Integer pageSize;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

}
