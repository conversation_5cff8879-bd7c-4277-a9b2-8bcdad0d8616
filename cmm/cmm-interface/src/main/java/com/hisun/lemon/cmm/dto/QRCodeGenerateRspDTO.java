package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 二维码生成响应传输对象
 * <AUTHOR>
 * @date 2017年7月12日 下午2:29:16
 * @version V1.0
 */
public class QRCodeGenerateRspDTO {
    /** 二维码信息 */
    @ApiModelProperty(name = "QRCode", value = "二维码信息")
    private String QRCode;

    public String getQRCode() {
        return QRCode;
    }

    public void setQRCode(String qRCode) {
        QRCode = qRCode;
    }

}
