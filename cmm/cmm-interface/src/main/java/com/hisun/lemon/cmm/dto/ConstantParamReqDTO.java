package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/7/31
 */
public class ConstantParamReqDTO {
    @ApiModelProperty(name = "parmNm", value = "常量参数名", required = true)
    private String parmNm;
    @ApiModelProperty(name = "effDt", value = "生效时间", required = true)
    private LocalDate effDt;
    @ApiModelProperty(name = "effDt", value = "生效时间", required = true)
    private LocalDate expDt;
    @ApiModelProperty(name = "effFlg", value = "生效标志", required = true)
    @Pattern(regexp = "0|1", message = "")
    private String effFlg;
    @ApiModelProperty(name = "parmDispNm", value = "常量参数显示名", required = true)
    private String parmDispNm;
    @ApiModelProperty(name = "parmCls", value = "常量参数所属模块", required = true)
    private String parmCls;
    @ApiModelProperty(name = "parmVal", value = "常量参数值", required = true)
    private String parmVal;
    @ApiModelProperty(name = "rmk", value = "备注", required = true)
    private String rmk;
    @ApiModelProperty(name = "updOprId", value = "更新操作员", required = true)
    private String updOprId;

    public String getParmNm() {
        return parmNm;
    }

    public void setParmNm(String parmNm) {
        this.parmNm = parmNm;
    }

    public LocalDate getEffDt() {
        return effDt;
    }

    public void setEffDt(LocalDate effDt) {
        this.effDt = effDt;
    }

    public LocalDate getExpDt() {
        return expDt;
    }

    public void setExpDt(LocalDate expDt) {
        this.expDt = expDt;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getParmDispNm() {
        return parmDispNm;
    }

    public void setParmDispNm(String parmDispNm) {
        this.parmDispNm = parmDispNm;
    }

    public String getParmCls() {
        return parmCls;
    }

    public void setParmCls(String parmCls) {
        this.parmCls = parmCls;
    }

    public String getParmVal() {
        return parmVal;
    }

    public void setParmVal(String parmVal) {
        this.parmVal = parmVal;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    @Override
    public String toString() {
        return "ConstantParamReqDTO{" +
                "parmNm='" + parmNm + '\'' +
                ", effDt=" + effDt +
                ", expDt=" + expDt +
                ", effFlg='" + effFlg + '\'' +
                ", parmDispNm='" + parmDispNm + '\'' +
                ", parmCls='" + parmCls + '\'' +
                ", parmVal='" + parmVal + '\'' +
                ", rmk='" + rmk + '\'' +
                ", updOprId='" + updOprId + '\'' +
                '}';
    }
}
