package com.hisun.lemon.cmm.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hisun.lemon.cmm.dto.SmsCheckReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeRspDTO;
import com.hisun.lemon.cmm.dto.SmsSendReqDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * @Description  短信服务接口
 * <AUTHOR>
 * @date 2017年7月7日 下午3:01:38 
 * @version V1.0
 */
@FeignClient("CMM")
public interface SmsServerClient {
    
    /**
     * @Description 短信验证码下发
     * <AUTHOR>
     * @param smsCodeReqDTO
     * @return
     */
    @PostMapping("/sms/code")
    public GenericRspDTO<SmsCodeRspDTO> smsCodeSend(@Validated @RequestBody GenericDTO<SmsCodeReqDTO> reqDTO);
    
    /**
     * @Description 短信验证码校验
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/sms/code/check")
    public GenericRspDTO<NoBody> smsCodeCheck(@Validated @RequestBody GenericDTO<SmsCheckReqDTO> reqDTO);
    
    /**
     * @Description 短信下发
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/sms/send")
    public GenericRspDTO<NoBody> smsSend(@Validated @RequestBody GenericDTO<SmsSendReqDTO> reqDTO);
}
