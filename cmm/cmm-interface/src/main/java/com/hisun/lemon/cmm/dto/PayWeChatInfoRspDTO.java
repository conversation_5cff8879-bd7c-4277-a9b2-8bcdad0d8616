package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 
 * @Description 公共微信授权信息查询
 * <AUTHOR>
 * @date 2017年11月20日 上午09:33:36
 * @version V1.0
 */
public class PayWeChatInfoRspDTO {

    /**
     * @Fields paramJrnNo 流水号
     */
    private String paramJrnNo;
    /**
     * @Fields openId 第三方id
     */
    private String openId;
    /**
     * @Fields ninckName 昵称
     */
    private String ninckName;
    /**
     * @Fields sex 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    private String sex;
    /**
     * @Fields province 用户个人资料填写的省份
     */
    private String province;
    /**
     * @Fields city 城市
     */
    private String city;
    /**
     * @Fields country 国家
     */
    private String country;
    /**
     * @Fields headimgurl 用户头像,用户没有头像时该项为空。若用户更换头像，原有头像URL将失效
     */
    private String headimgurl;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getParamJrnNo() {
        return paramJrnNo;
    }

    public void setParamJrnNo(String paramJrnNo) {
        this.paramJrnNo = paramJrnNo;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getNinckName() {
        return ninckName;
    }

    public void setNinckName(String ninckName) {
        this.ninckName = ninckName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getHeadimgurl() {
        return headimgurl;
    }

    public void setHeadimgurl(String headimgurl) {
        this.headimgurl = headimgurl;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }


}
