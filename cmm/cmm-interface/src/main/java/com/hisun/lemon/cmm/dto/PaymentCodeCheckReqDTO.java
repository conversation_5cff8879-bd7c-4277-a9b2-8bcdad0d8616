package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 付款码校验请求传输对象
 * <AUTHOR>
 * @date 2017年7月15日 下午2:08:32
 * @version V1.0
 */
@ApiModel(value = "PaymentCodeCheckReqDTO", description = "付款码校验请求传输对象")
public class PaymentCodeCheckReqDTO {
    /** 付款码信息 */
    @ApiModelProperty(name = "paymentCode", value = "付款码", required = true, dataType = "string")
    @NotNull(message = "CMM10008")
    @Length(max = 18)
    private String paymentCode;

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }
}
