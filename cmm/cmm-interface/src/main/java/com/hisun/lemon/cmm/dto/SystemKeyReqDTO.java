package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * @Description 系统密钥查询请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午2:33:36
 * @version V1.0
 */
public class SystemKeyReqDTO {
    /** 密钥索引 */
    @ApiModelProperty(name = "keyIndex", value = "密钥索引", required = true)
    @NotNull(message = "CMM10023")
    @Length(max = 16)
    private String keyIndex;

    public String getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }
}
