package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * @Description 公共加解密响应传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午2:33:36
 * @version V1.0
 */
public class CommonEncryptRspDTO {

    /** 加解密数据 */
    @ApiModelProperty(name = "data", value = "加解密数据", required = true)
    @NotNull(message = "CMM10025")
    private String data;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

}
