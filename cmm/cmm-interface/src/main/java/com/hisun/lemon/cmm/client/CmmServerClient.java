package com.hisun.lemon.cmm.client;

import com.hisun.lemon.cmm.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * @Description  公共服务接口
 * <AUTHOR>
 * @date 2017年7月7日 下午3:01:38 
 * @version V1.0
 */
@FeignClient("CMM")
public interface CmmServerClient {
    
    /**
     * @Description 号段检查
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/cmm/segement/check")
    public GenericRspDTO<SegementCheckRspDTO> segementCheck(@Validated @RequestBody GenericDTO<SegementCheckReqDTO> reqDTO);
    
    /**
     * @Description 消息推送
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/cmm/message/send")
    public GenericRspDTO<NoBody> messageSend(@Validated @RequestBody GenericDTO<MessageSendReqDTO> reqDTO);
    
    /**
     * @Description 公共加解密
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/cmm/encrypt")
    public GenericRspDTO<CommonEncryptRspDTO> encrypt(@Validated @RequestBody GenericDTO<CommonEncryptReqDTO> reqDTO);
    
    /**
     * @Description 登陆信息查询
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/cmm/loginfo")
    public GenericRspDTO<AppInfoRspDTO> logInfo(@Validated @RequestBody GenericDTO<AppInfoReqDTO> reqDTO);
    
    /**
     * @Description 系统密钥查询
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/cmm/system/key")
    public GenericRspDTO<SystemKeyRspDTO> key(@Validated @RequestBody GenericDTO<SystemKeyReqDTO> reqDTO);

    /**
     * 检查设备是否与原登记不一致
     * @param reqDTO
     * @return
     */
    @PostMapping("/cmm/message/push/client/switch")
    public GenericRspDTO<Boolean> messageClientSwitch(@Validated @RequestBody GenericDTO<MessageClientSwitchReqDTO> reqDTO);
}
