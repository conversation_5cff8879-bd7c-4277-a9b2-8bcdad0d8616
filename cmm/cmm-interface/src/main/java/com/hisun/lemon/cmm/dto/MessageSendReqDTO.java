package com.hisun.lemon.cmm.dto;

import java.util.Map;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 消息推送请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class MessageSendReqDTO {
    /** 内部用户号 */
    @ApiModelProperty(name = "userId", value = "内部用户号", required = true)
    @NotNull(message = "CMM10007")
    @Length(max = 20)
    private String userId;
    /** 消息模版编号 */
    @ApiModelProperty(name = "messageTemplateId", value = "消息模版ID", required = true)
    @NotNull(message = "CMM10019")
    @Length(max = 8)
    private String messageTemplateId;
    /** 消息语种 */
    @ApiModelProperty(name = "messageLanguage", value = "消息语种", required = true)
    @Pattern(regexp = "km|zh|en", message = "CMM10006")
    private String messageLanguage;
    /** 消息模版变量替换值 */
    @ApiModelProperty(name = "replaceFieldMap", value = "消息替换变量", required = true)
    @NotNull(message = "CMM10021")
    private Map<String, String> replaceFieldMap;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMessageTemplateId() {
        return messageTemplateId;
    }

    public void setMessageTemplateId(String messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    public String getMessageLanguage() {
        return messageLanguage;
    }

    public void setMessageLanguage(String messageLanguage) {
        this.messageLanguage = messageLanguage;
    }

    public Map<String, String> getReplaceFieldMap() {
        return replaceFieldMap;
    }

    public void setReplaceFieldMap(Map<String, String> replaceFieldMap) {
        this.replaceFieldMap = replaceFieldMap;
    }
}
