package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * 
 * @Description 公共微信授权凭证
 * <AUTHOR>
 * @date 2017年11月20日 上午09:33:36
 * @version V1.0
 */
public class PayWeChatReqDTO {

    /** 回调code */
    @ApiModelProperty(name = "code", value = "回调code", required = true)
    @NotNull(message = "CMM40003")
    private String code;

    /** 回调code */
    @ApiModelProperty(name = "state", value = "回调参数", required = true)
    @NotNull(message = "CMM40004")
    private String state;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


}
