package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 付款码校验响应传输对象
 * <AUTHOR>
 * @date 2017年7月15日 下午2:08:32
 * @version V1.0
 */
@ApiModel(value = "PaymentCodeCheckRspDTO", description = "付款码校验响应传输对象")
public class PaymentCodeCheckRspDTO {
    /** 付款方式 */
    @ApiModelProperty(name = "paymentType", value = "付款方式(Seatelpay:Mpay支付 BESTPAY:翼支付 Alipay:支付宝 WeChat:微信支付)")
    private String paymentType;
    private String type;
    /** 付款码信息 */
    @ApiModelProperty(name = "paymentCode", value = "付款码信息")
    private String paymentCode;
    /** 付款内部用户号 */
    @ApiModelProperty(name = "paymentUserId", value = "付款用户号")
    private String paymentUserId;

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    public String getPaymentUserId() {
        return paymentUserId;
    }

    public void setPaymentUserId(String paymentUserId) {
        this.paymentUserId = paymentUserId;
    }

}
