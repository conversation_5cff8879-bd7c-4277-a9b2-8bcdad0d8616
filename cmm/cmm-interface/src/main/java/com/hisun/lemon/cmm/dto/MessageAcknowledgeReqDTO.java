package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.Pattern;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 消息确认请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:13:57
 * @version V1.0
 */
public class MessageAcknowledgeReqDTO {
    /** 用户类型 */
    @ApiModelProperty(name = "userType", value = "用户类型", required = true)
    @Pattern(regexp = "personal|merchant", message = "CMM10022")
    private String userType;
    /** 消息ID */
    @ApiModelProperty(name = "id", value = "消息ID")
    private String id;

    public String getId() {
        return id;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public void setId(String id) {
        this.id = id;
    }
}
