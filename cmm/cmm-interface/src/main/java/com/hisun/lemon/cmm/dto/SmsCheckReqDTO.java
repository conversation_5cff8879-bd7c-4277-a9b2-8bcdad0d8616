package com.hisun.lemon.cmm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 短信验证码验证请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午4:00:10
 * @version V1.0
 */
public class SmsCheckReqDTO {
    /** 手机号码 */
    @ApiModelProperty(name = "mblNo", value = "手机号码", required = true)
    @NotNull(message = "CMM10001")
    @Length(max = 20)
    private String mblNo;
    /** 短信验证码类型 */
    @ApiModelProperty(name = "type", value = "短信验证码类型", required = true)
    @NotNull(message = "CMM10002")
    @Length(max = 3)
    private String type;
    /** 短信验证码 */
    @ApiModelProperty(name = "smsCode", value = "短信验证码", required = true)
    @NotNull(message = "CMM10003")
    @Length(max = 8)
    private String smsCode;
    /** 短信验证码token */
    @ApiModelProperty(name = "token", value = "短信验证码token", required = true)
    @NotNull(message = "CMM10017")
    @Length(max = 32)
    private String token;
    
    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

}
