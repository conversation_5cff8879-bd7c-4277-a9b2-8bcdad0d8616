package com.hisun.lemon.cmm.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 
 * @Description 消息推送ClientID设置请求传输对象
 * <AUTHOR>
 * @date 2017年7月7日 下午2:33:36
 * @version V1.0
 */
public class MessageClientSwitchReqDTO {
    /** 用户Id */
    @ApiModelProperty(name = "userId", value = "用户Id", required = true)
    private String userId;

    /** 消息推送ClientID */
    @ApiModelProperty(name = "clientId", value = "消息推送ClientID(设备ID)", required = true)
    private String clientId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }
}
