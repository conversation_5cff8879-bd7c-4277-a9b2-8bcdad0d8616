/*
package com.hisun.lemon.cmm.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Json;
import com.hisun.channel.parse.annotation.VerifyValue;

*/
/**
 * 网页授权凭证响应报文
 * <AUTHOR>
 *
 *//*

@Json
@VerifyValue
public class TokenRsp {
	
	*/
/**
	 * 错误码
	 *//*

	@Item(alias="errcode")
	private String errcode;
	
	*/
/**
	 * 错误信息
	 *//*

	@Item(alias="errmsg")
	private String errmsg;

	*/
/**
	 * 接口调用凭证
	 *//*

	@Item(alias="access_token")
	private String access_token;
	
	*/
/**
	 * 凭证超时时间,单位秒
	 *//*

	@Item(alias="expires_in")
	private String expires_in;
	
	*/
/**
	 * 用户刷新access_token
	 *//*

	@Item(alias="refresh_token")
	private String refresh_token;
	
	*/
/**
	 * 用户唯一标识
	 *//*

	@Item(alias="openid")
	private String openid;
	
	*/
/**
	 * 用户授权作用域
	 *//*

	@Item(alias="scope")
	private String scope;
	
	*/
/**
	 * 当且仅当该网站应用已获得该用户的userinfo授权时,才会出现该字段
	 *//*

	@Item(alias="unionid")
	private String unionid;



	*/
/**
	 * 错误码
	 *//*

	public String getErrcode() {
		return errcode;
	}

	*/
/**
	 * 错误码
	 *//*

	public void setErrcode(String errcode) {
		this.errcode = errcode;
	}

	*/
/**
	 * 错误信息
	 *//*

	public String getErrmsg() {
		return errmsg;
	}

	*/
/**
	 * 错误信息
	 *//*

	public void setErrmsg(String errmsg) {
		this.errmsg = errmsg;
	}

	*/
/**
	 * 接口调用凭证
	 *//*

	public String getAccess_token() {
		return access_token;
	}

	*/
/**
	 * 接口调用凭证
	 *//*

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	*/
/**
	 * 凭证超时时间,单位秒
	 *//*

	public String getExpires_in() {
		return expires_in;
	}

	*/
/**
	 * 凭证超时时间,单位秒
	 *//*

	public void setExpires_in(String expires_in) {
		this.expires_in = expires_in;
	}

	*/
/**
	 * 用户刷新access_token
	 *//*

	public String getRefresh_token() {
		return refresh_token;
	}

	*/
/**
	 * 用户刷新access_token
	 *//*

	public void setRefresh_token(String refresh_token) {
		this.refresh_token = refresh_token;
	}

	*/
/**
	 * 用户唯一标识
	 *//*

	public String getOpenid() {
		return openid;
	}

	*/
/**
	 * 用户唯一标识
	 *//*

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	*/
/**
	 * 用户授权作用域
	 *//*

	public String getScope() {
		return scope;
	}

	*/
/**
	 * 用户授权作用域
	 *//*

	public void setScope(String scope) {
		this.scope = scope;
	}

	*/
/**
	 * 当且仅当该网站应用已获得该用户的userinfo授权时,才会出现该字段
	 *//*

	public String getUnionid() {
		return unionid;
	}
	
	*/
/**
	 * 当且仅当该网站应用已获得该用户的userinfo授权时,才会出现该字段
	 *//*

	public void setUnionid(String unionid) {
		this.unionid = unionid;
	}
	
}
*/
