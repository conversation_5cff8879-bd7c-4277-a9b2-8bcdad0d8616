package com.hisun.lemon.cmm.client;

import java.util.List;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hisun.lemon.cmm.dto.ConstantParamListReqDTO;
import com.hisun.lemon.cmm.dto.ConstantParamReqDTO;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * <AUTHOR>
 * @create 2017/8/1
 */
@FeignClient("CMM")
public interface ConstantParamClient {
    /**
     * 新增公共参数
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/cmm-service/params")
    GenericRspDTO<NoBody> paramsAdd(@Validated @RequestBody GenericDTO<ConstantParamReqDTO> reqDTO);

    /**
     * 删除公共参数
     *
     * @param reqDTO
     * @return
     */
    @DeleteMapping(value = "/cmm-service/params")
    GenericRspDTO<NoBody> paramsDel(@RequestBody GenericDTO<String> reqDTO);

    /**
     * 更新公共参数
     *
     * @param reqDTO
     * @return
     */
    @PutMapping(value = "/cmm-service/params")
    GenericRspDTO<NoBody> paramsUpd(@Validated @RequestBody GenericDTO<ConstantParamReqDTO> reqDTO);

    /**
     * 查询公共参数详情
     *
     * @param parmNm
     * @return
     */
    @GetMapping(value = "/cmm-service/params/{parmNm}")
    GenericRspDTO<ConstantParamRspDTO> params(@PathVariable(value = "parmNm") String parmNm);

    /**
     * 查询公共参数列表
     *
     * @param reqDTO
     * @return
     */
    @GetMapping(value = "/cmm-service/params")
    GenericRspDTO<List<ConstantParamRspDTO>> allParams(@Validated @RequestBody GenericDTO<ConstantParamListReqDTO> reqDTO);

    /**
     * 查询公共参数组
     *
     * @param parmNm
     * @return
     */
    @GetMapping(value = "/cmm-service/params/group/{parmNm}")
    GenericRspDTO<List<ConstantParamRspDTO>> paramsGroup(@PathVariable(value = "parmNm") String parmNm);
}
