package com.hisun.lemon.tam.dto;

import java.math.BigDecimal;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 创建转账到用户对象
 * 
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("转账到用户")
public class UserTransferOrderDTO {
	
	/**
	 * @Fields remark 备注
	 */
	@ApiModelProperty(name = "remark", value = "备注")
	@Length(max =25)
	private String remark;
	 public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
    /**
     * @Fields busType 业务类型  用户 0301   银行卡  0302  面对面  0303
     */
	@NotEmpty(message = "TAM10003")
	@ApiModelProperty(name = "busType", value = "业务类型   用户 0301   银行卡  0302  面对面  0303")
	@Length(max =4)
    private String busType;

	/**
	 * @Fields mblNo 转账账户
	 */
	@ApiModelProperty(name = "mblNo", value = "转账账户")
	@NotEmpty(message = "TAM10007")
	@Length(max =20)
	private String mblNo;
	/**
	 * 金额
	 */
	@ApiModelProperty(name = "amount", value = "金额")
	@NotNull(message = "TAM10001")
	@Min(value = 0, message = "TAM10001")
	private BigDecimal amount;

	/**
	 * @Fields orderCcy 币种
	 */
	@ApiModelProperty(name = "orderCcy", value = "币种")
	@Length(max =4)
	private String orderCcy;

	/**
     * @Fields areaDesc 区号
     */
	@ApiModelProperty(name = "areaDesc", value = "区号")
	@NotEmpty(message = "TAM10019")
    private String areaDesc;
    
    /**
     * @Fields nationName 国家名称
     */
	@ApiModelProperty(name = "nationName", value = "国家名称")
	@NotEmpty(message = "TAM10018")
    private String nationName;
	
	/**
	 * 订单来源渠道
	 * WEB:WEB站点 <br/>
	 * APP:手机APP<br/>
	 * HALL:营业厅<br/>
	 * OTHER:其他渠道<br/>
	 */
	@ApiModelProperty(name = "sysChannel", value = "订单来源渠道(WEB:web站点|APP:APP手机|HALL:营业厅|OTHER:其他渠道)")
	@Length(max = 5)
    private String sysChannel;
	
    
	public String getOrderCcy() {
		return orderCcy;
	}

	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public String getBusType() {
		return busType;
	}

	public void setBusType(String busType) {
		this.busType = busType;
	}

	public String getAreaDesc() {
		return areaDesc;
	}

	public void setAreaDesc(String areaDesc) {
		this.areaDesc = areaDesc;
	}

	public String getNationName() {
		return nationName;
	}

	public void setNationName(String nationName) {
		this.nationName = nationName;
	}

	public String getSysChannel() {
		return sysChannel;
	}

	public void setSysChannel(String sysChannel) {
		this.sysChannel = sysChannel;
	}
}
