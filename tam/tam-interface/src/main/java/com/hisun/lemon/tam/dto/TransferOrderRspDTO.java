package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 转账订单响应
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/3 18:20
 */
@ApiModel(value = "TransferOrderRspDTO", description = "转账订单响应")
public class TransferOrderRspDTO {
    /**
     * 转账订单编号
     */
    private String orderNo;

    /**
     * 用户编号
     */
    private String userId;

    /**
     * 对方内部号
     */
    private String crossUserId;

    /**
     * 转账账户
     */
    private String mblNo;

    /**
     * 脱敏手机号
     */
    private String mblNoHid;

    /**
     * 业务类型
     */
    private String busType;

    /**
     * 交易类型
     */
    private String txType;

    /**
     * 币种
     */
    private String orderCcy;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 实际交易金额
     */
    private BigDecimal actualTradeAmt;

    /**
     * 服务费
     */
    private BigDecimal fee;

    /**
     * 备注
     */
    private String remark;

    /**
     * 渠道
     */
    private String sysChannel;

    /**
     * 收款人姓名
     */
    private String gatherName;

    /**
     * 订单状态 U:初始 S:成功 F:失败
     */
    private String orderSts;

    /**
     * 会计日期
     */
    private LocalDate acTm;

    /**
     * 交易时间
     */
    private LocalDateTime txTm;

    /**
     * 卡种，D借记卡，C贷记卡
     */
    private String crdAcTyp;

    /**
     * 资金机构
     */
    private String crdCorpOrg;

    /**
     * 合作资金机构名称
     */
    private String corpOrgSnm;

    /**
     * 银行卡户名
     */
    private String capCrdNm;

    /**
     * 加密银行卡号
     */
    private String crdNoEnc;

    /**
     * 银行卡号
     */
    private String capCrdNo;

    /**
     * 收银订单号
     */
    private String busOrderNo;

    /**
     * 银行卡后四位
     */
    private String lastCapCrdNo;

    /**
     * 手机号的后四位
     */
    private String lastMblNo;

    /**
     * 描述
     */
    private String goodDesc;

    /**
     * 区号
     */
    private String areaDesc;

    /**
     * 国家名称
     */
    private String nationName;

    /**
     * 付款方名字
     */
    private String payerName;

    /**
     * 付款方手机号
     */
    private String payeMblNo;

    /**
     * 支行名称
     */
    private String capCorgNm;

    /**
     * 外部订单号
     */
    private String extOrderNo;

    /**
     * 付款方账号
     */
    private String fkAcNo;

    /**
     * 收款方账号
     */
    private String skAcNo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后修改时间
     */
    private LocalDateTime modifyTime;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCrossUserId() {
        return crossUserId;
    }

    public void setCrossUserId(String crossUserId) {
        this.crossUserId = crossUserId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getMblNoHid() {
        return mblNoHid;
    }

    public void setMblNoHid(String mblNoHid) {
        this.mblNoHid = mblNoHid;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getTxType() {
        return txType;
    }

    public void setTxType(String txType) {
        this.txType = txType;
    }

    public String getOrderCcy() {
        return orderCcy;
    }

    public void setOrderCcy(String orderCcy) {
        this.orderCcy = orderCcy;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getActualTradeAmt() {
        return actualTradeAmt;
    }

    public void setActualTradeAmt(BigDecimal actualTradeAmt) {
        this.actualTradeAmt = actualTradeAmt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSysChannel() {
        return sysChannel;
    }

    public void setSysChannel(String sysChannel) {
        this.sysChannel = sysChannel;
    }

    public String getGatherName() {
        return gatherName;
    }

    public void setGatherName(String gatherName) {
        this.gatherName = gatherName;
    }

    public String getOrderSts() {
        return orderSts;
    }

    public void setOrderSts(String orderSts) {
        this.orderSts = orderSts;
    }

    public LocalDate getAcTm() {
        return acTm;
    }

    public void setAcTm(LocalDate acTm) {
        this.acTm = acTm;
    }

    public LocalDateTime getTxTm() {
        return txTm;
    }

    public void setTxTm(LocalDateTime txTm) {
        this.txTm = txTm;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCorpOrgSnm() {
        return corpOrgSnm;
    }

    public void setCorpOrgSnm(String corpOrgSnm) {
        this.corpOrgSnm = corpOrgSnm;
    }

    public String getCapCrdNm() {
        return capCrdNm;
    }

    public void setCapCrdNm(String capCrdNm) {
        this.capCrdNm = capCrdNm;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getCapCrdNo() {
        return capCrdNo;
    }

    public void setCapCrdNo(String capCrdNo) {
        this.capCrdNo = capCrdNo;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public String getLastCapCrdNo() {
        return lastCapCrdNo;
    }

    public void setLastCapCrdNo(String lastCapCrdNo) {
        this.lastCapCrdNo = lastCapCrdNo;
    }

    public String getLastMblNo() {
        return lastMblNo;
    }

    public void setLastMblNo(String lastMblNo) {
        this.lastMblNo = lastMblNo;
    }

    public String getGoodDesc() {
        return goodDesc;
    }

    public void setGoodDesc(String goodDesc) {
        this.goodDesc = goodDesc;
    }

    public String getAreaDesc() {
        return areaDesc;
    }

    public void setAreaDesc(String areaDesc) {
        this.areaDesc = areaDesc;
    }

    public String getNationName() {
        return nationName;
    }

    public void setNationName(String nationName) {
        this.nationName = nationName;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public String getPayeMblNo() {
        return payeMblNo;
    }

    public void setPayeMblNo(String payeMblNo) {
        this.payeMblNo = payeMblNo;
    }

    public String getCapCorgNm() {
        return capCorgNm;
    }

    public void setCapCorgNm(String capCorgNm) {
        this.capCorgNm = capCorgNm;
    }

    public String getExtOrderNo() {
        return extOrderNo;
    }

    public void setExtOrderNo(String extOrderNo) {
        this.extOrderNo = extOrderNo;
    }

    public String getFkAcNo() {
        return fkAcNo;
    }

    public void setFkAcNo(String fkAcNo) {
        this.fkAcNo = fkAcNo;
    }

    public String getSkAcNo() {
        return skAcNo;
    }

    public void setSkAcNo(String skAcNo) {
        this.skAcNo = skAcNo;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}