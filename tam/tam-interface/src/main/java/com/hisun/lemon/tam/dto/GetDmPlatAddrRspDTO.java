package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 获取数币平台地址响应DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31 20:38
 */
@ApiModel(value = "GetDmPlatAddrRspDTO", description = "获取数币平台地址响应DTO")
public class GetDmPlatAddrRspDTO {

    @ApiModelProperty(value = "链上地址")
    private String address;

    @ApiModelProperty(value = "对应平台账号")
    private String platformAcNo;

    @ApiModelProperty(value = "所属区块链网络", example = "tron-nile")
    private String network;

    @ApiModelProperty(value = "地址状态（ENABLED:已启用, DISABLED:未启用, FROZEN:冻结）", example = "ENABLED")
    private String status;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPlatformAcNo() {
        return platformAcNo;
    }

    public void setPlatformAcNo(String platformAcNo) {
        this.platformAcNo = platformAcNo;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}