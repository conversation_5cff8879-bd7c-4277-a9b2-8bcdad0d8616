package com.hisun.lemon.tam.client;

import com.hisun.lemon.tam.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * 转账服务接口
 * 
 * <AUTHOR>
 * @date 2017年7月7日
 * @time 下午19:36:23
 *
 */
@FeignClient("TAM")
public interface TransferOrderClient {
	
	/**
	 * 转账到账户(对外接口   免密)
	 * @param resultTransferOrderDTO
	 * @return
	 */
	@PostMapping("/tam/transfer/order/user/out")
	public GenericRspDTO<UserTransferRspDTO> createOutUserOrder(
			@Validated @RequestBody GenericDTO<UserOutTransferOrderDTO> userOutTransferOrderDTO);
	
	
	/**
	 * 转账到账户(对外接口  加密)
	 * @param resultTransferOrderDTO
	 * @return
	 */
	@PostMapping("/tam/transfer/order/user/encrypt/out")
	public GenericRspDTO<UserTransferRspDTO> outUserOrderEncrypt(
			@Validated @RequestBody GenericDTO<UserOutTransferOrderDTO> userOutTransferOrderDTO);
	
	/**
	 * 补单   与收银对账
	 * @param orderNo
	 * @return
	 */
	@PatchMapping("/tam/transfer/order/kill")
	public GenericRspDTO<NoBody> killOrder(@Validated @RequestBody GenericDTO<KillOrderRspDTO> killOrderRspDTO);
	
	/**
	 * 补单    与CPO对账
	 * @param orderNo
	 * @return
	 */
	@PatchMapping("/tam/transfer/order/cpo/kill")
	public GenericRspDTO<NoBody> killCpoOrder(@Validated @RequestBody GenericDTO<KillOrderRspDTO> killOrderRspDTO);
			
	
	/**
	 * 转账结果通知
	 * @param resultTransferOrderDTO
	 * @return
	 */
	@PatchMapping("/tam/transfer/result/order")
	public GenericRspDTO<NoBody> userTransferOrderResult(
			@Validated @RequestBody GenericDTO<ResultTransferOrderDTO> resultTransferOrderDTO);
	
	
	/**
	 * 资金能力结果通知
	 * @param resultTransferOrderDTO
	 * @return
	 */
	@PatchMapping("/tam/transfer/result/order/bank")
	public GenericRspDTO<NoBody> transferOrderResult(
			@Validated @RequestBody GenericDTO<ResultTransferOrderDTO> resultTransferOrderDTO);

	/**
	 * 数币转账
	 *
	 * @param dmOutTransferOrderDTO
	 * @return
	 */
	@PostMapping(value = "/tam/transfer/order/dm/encrypt/out")
	GenericRspDTO<DmTransferRspDTO> outDmOrder(@Validated @RequestBody GenericDTO<DmOutTransferOrderDTO> dmOutTransferOrderDTO);

	/**
	 * 获取数币平台地址
	 *
	 * @param getDmPlatAddrReqDTO
	 * @return
	 */
	@PostMapping(value = "/tam/transfer/get/dm/plat/addr")
	public GenericRspDTO<GetDmPlatAddrRspDTO> getDmPlatAddr(@Validated @RequestBody GenericDTO<GetDmPlatAddrReqDTO> getDmPlatAddrReqDTO);

	/**
	 * 根据订单号查询转账订单
	 * @param orderNo
	 * @return
	 */
	@GetMapping(value = "/tam/transfer/detail/{orderNo}")
	public GenericRspDTO<TransferOrderRspDTO> getTransferOrder(@PathVariable(value = "orderNo") String orderNo);
}
