package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 数币转账响应DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 16:06
 */
@ApiModel(value = "DmTransferRspDTO", description = "数币转账响应DTO")
public class DmTransferRspDTO {

    /**
     * 转账金额
     */
    @ApiModelProperty(name = "amount", value = "转账金额")
    private BigDecimal amount;
    /**
     * 转账订单号
     */
    @ApiModelProperty(name = "orderNo", value = "转账订单号")
    private String orderNo;

    /**
     * 状态
     */
    @ApiModelProperty(name = "orderSts", value = "状态   S:成功   F：失败   U:转账初始状态  P:转账待确认 ")
    private String orderSts;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    /**
     * 转账类型
     */
    @ApiModelProperty(name = "transferType", value = "转账类型：站内转账：DZ121、链上转账：DZ122")
    private String transferType;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderSts() {
        return orderSts;
    }

    public void setOrderSts(String orderSts) {
        this.orderSts = orderSts;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getTransferType() {
        return transferType;
    }

    public void setTransferType(String transferType) {
        this.transferType = transferType;
    }

}
