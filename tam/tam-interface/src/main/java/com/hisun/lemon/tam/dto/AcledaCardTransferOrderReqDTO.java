package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

public class AcledaCardTransferOrderReqDTO {

    /**
     * @Fields busType 业务类型 用户 0301 银行卡 0302 面对面 0303
     */
    @NotEmpty(message = "TAM10003")
    @ApiModelProperty(name = "busType", value = "业务类型  用户 0301   银行卡  0302  面对面  0303 ")
    @Length(max = 4)
    private String busType;

    /**
     * @Fields orderCcy 币种
     */
    @ApiModelProperty(name = "orderCcy", value = "币种")
    @Length(max = 4)
    private String orderCcy;
    /**
     * 金额
     */
    @ApiModelProperty(name = "amount", value = "金额")
    @NotNull(message = "TAM10001")
    private BigDecimal amount;
    /**
     * @Fields remark 备注
     */
    @ApiModelProperty(name = "remark", value = "备注  (可传可不传)")
    @Length(max = 25)
    private String remark;
    /**
     * @Fields capCrdNo 银行卡号
     */
    @ApiModelProperty(name = "capCrdNo", value = "银行卡号")
    @Length(max = 30)
    private String capCrdNo;
    /**
     * @Fields capCorgNm 银行名称
     */
    @ApiModelProperty(name = "capCorgNm", value = "支行名称")
    private String capCorgNm;

    /**
     * 资金机构
     */
    @ApiModelProperty(name = "capCorg", value = "资金机构", required = true, dataType = "String")
    @NotNull(message = "TAM10012")
    private String capCorg;

    /**
     * @Fields corpOrgSnm 合作资金机构名称
     */
    @ApiModelProperty(name = "corpOrgSnm", value = "资金机构名称", required = true, dataType = "String")
    @NotNull(message = "TAM10020")
    private String corpOrgSnm;

    /**
     * 卡种，C贷记卡，D借记卡
     */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种", required = true, dataType = "String")
    @Pattern(regexp = "D|C", message = "TAM10013")
    private String crdAcTyp;


    /**
     * 银行卡户名
     */
    @ApiModelProperty(name = "capCrdNm", value = "银行卡户名", required = true, dataType = "String")
    private String capCrdNm;

    /**
     * acleda银行资金流方向
     */
    @ApiModelProperty(name = "direction", value = "direction", required = true, dataType = "int")
    private int direction;

    /**
     * 订单来源渠道
     * WEB:WEB站点 <br/>
     * APP:手机APP<br/>
     * HALL:营业厅<br/>
     * OTHER:其他渠道<br/>
     */
    @ApiModelProperty(name = "sysChannel", value = "订单来源渠道(WEB:web站点|APP:APP手机|HALL:营业厅|ACLEDA: ACLEDA银行|OTHER:其他渠道)")
    @Length(max = 5)
    private String sysChannel;

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getOrderCcy() {
        return orderCcy;
    }

    public void setOrderCcy(String orderCcy) {
        this.orderCcy = orderCcy;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCapCrdNo() {
        return capCrdNo;
    }

    public void setCapCrdNo(String capCrdNo) {
        this.capCrdNo = capCrdNo;
    }

    public String getCapCorgNm() {
        return capCorgNm;
    }

    public void setCapCorgNm(String capCorgNm) {
        this.capCorgNm = capCorgNm;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getCorpOrgSnm() {
        return corpOrgSnm;
    }

    public void setCorpOrgSnm(String corpOrgSnm) {
        this.corpOrgSnm = corpOrgSnm;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCapCrdNm() {
        return capCrdNm;
    }

    public void setCapCrdNm(String capCrdNm) {
        this.capCrdNm = capCrdNm;
    }

    public String getSysChannel() {
        return sysChannel;
    }

    public void setSysChannel(String sysChannel) {
        this.sysChannel = sysChannel;
    }

    public int getDirection() {
        return direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }
}
