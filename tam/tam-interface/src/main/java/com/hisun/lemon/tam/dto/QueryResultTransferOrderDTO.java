package com.hisun.lemon.tam.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 查询历史转账记录
 * 
 * <AUTHOR>
 * @date 2017年7月13日
 * @time 下午20:21:30
 *
 */
@ApiModel("历史转账记录")
public class QueryResultTransferOrderDTO {
	private List<queryOrder> queryOrderList;

	public List<queryOrder> getQueryOrderList() {
		return queryOrderList;
	}

	public void setQueryOrderList(List<queryOrder> queryOrderList) {
		this.queryOrderList = queryOrderList;
	}

	public static class queryOrder {
		
		/**
		 * @Fields mblNo 转账账户
		 */
		@ApiModelProperty(name = "mblNo", value = "转账账户")
		private String mblNo;
		
		/**
		 * @Fields mblNoHid 脱敏手机号
		 */
		@ApiModelProperty(name = "mblNoHid", value = "脱敏手机号")
		private String mblNoHid;

		 /**
	     * @Fields crossUserId 对方内部号
	     */
		@ApiModelProperty(name = "crossUserId", value = "对方内部号")
	    private String crossUserId;
		
		/**
	     * @Fields areaDesc 区号
	     */
		@ApiModelProperty(name = "areaDesc", value = "区号")
	    private String areaDesc;
	    
	    /**
	     * @Fields nationName 国家名称
	     */
		@ApiModelProperty(name = "nationName", value = "国家名称")
	    private String nationName;
		
		   /**
	     * @Fields capCrdNm 银行卡户名
	     */
		@ApiModelProperty(name = "capCrdNm", value = "银行卡户名")
	    private String capCrdNm;
		
		
		/**
		 * @Fields capCrdNo 银行卡号
		 */
		@ApiModelProperty(name = "capCrdNo", value = "银行卡号")
		private String capCrdNo;
		/**
	     * @Fields crdAcTyp 卡种，D借记卡，C贷记卡
	     */
		@ApiModelProperty(name = "crdAcTyp", value = "卡种")
	    private String crdAcTyp;
	    /**
	     * @Fields crdCorpOrg 资金机构
	     */
		@ApiModelProperty(name = "crdCorpOrg", value = "资金机构")
	    private String crdCorpOrg;
	    /**
	     * @Fields corpOrgSnm 合作资金机构名称
	     */
		@ApiModelProperty(name = "corpOrgSnm", value = "合作资金机构名称")
	    private String corpOrgSnm;
		/**
		 * @Fields capCorgNm 支行名称
		 */
		@ApiModelProperty(name = "capCorgNm", value = "支行名称")
		private String capCorgNm;
		/**
		 * @Fields gatherName 收款人姓名
		 */
		@ApiModelProperty(name = "gatherName", value = "收款人姓名")
		private String gatherName;

		/**
		 * @Fields gatherName 收款人头像
		 */
		@ApiModelProperty(name = "gatherAvatar", value = "收款人头像")
		private String gatherAvatar;
		
		@ApiModelProperty(name = "busType", value = "业务类型   用户 0301   银行卡  0302  面对面  0303")
		private String busType;

		/**
		 * @Fields txType 交易类型
		 */
		@ApiModelProperty(name = "txType", value = "交易类型   转账03")
		private String txType;
		
		

		public String getMblNo() {
			return mblNo;
		}

		public void setMblNo(String mblNo) {
			this.mblNo = mblNo;
		}

		public String getBusType() {
			return busType;
		}

		public void setBusType(String busType) {
			this.busType = busType;
		}

		public String getTxType() {
			return txType;
		}

		public void setTxType(String txType) {
			this.txType = txType;
		}

		public String getCrossUserId() {
			return crossUserId;
		}

		public void setCrossUserId(String crossUserId) {
			this.crossUserId = crossUserId;
		}

		public String getAreaDesc() {
			return areaDesc;
		}

		public void setAreaDesc(String areaDesc) {
			this.areaDesc = areaDesc;
		}

		public String getNationName() {
			return nationName;
		}

		public String getCapCrdNm() {
			return capCrdNm;
		}

		public void setCapCrdNm(String capCrdNm) {
			this.capCrdNm = capCrdNm;
		}

		public String getCapCrdNo() {
			return capCrdNo;
		}

		public void setCapCrdNo(String capCrdNo) {
			this.capCrdNo = capCrdNo;
		}

		public String getCrdAcTyp() {
			return crdAcTyp;
		}

		public void setCrdAcTyp(String crdAcTyp) {
			this.crdAcTyp = crdAcTyp;
		}

		public String getCrdCorpOrg() {
			return crdCorpOrg;
		}

		public void setCrdCorpOrg(String crdCorpOrg) {
			this.crdCorpOrg = crdCorpOrg;
		}

		public String getCorpOrgSnm() {
			return corpOrgSnm;
		}

		public void setCorpOrgSnm(String corpOrgSnm) {
			this.corpOrgSnm = corpOrgSnm;
		}

		public String getCapCorgNm() {
			return capCorgNm;
		}

		public void setCapCorgNm(String capCorgNm) {
			this.capCorgNm = capCorgNm;
		}

		public String getGatherName() {
			return gatherName;
		}

		public String getMblNoHid() {
			return mblNoHid;
		}

		public void setMblNoHid(String mblNoHid) {
			this.mblNoHid = mblNoHid;
		}

		public void setGatherName(String gatherName) {
			this.gatherName = gatherName;
		}

		public void setNationName(String nationName) {
			this.nationName = nationName;
		}

		public String getGatherAvatar() {
			return gatherAvatar;
		}

		public void setGatherAvatar(String gatherAvatar) {
			this.gatherAvatar = gatherAvatar;
		}
	}
}
