package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;


/**
 * 获取数币平台地址请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31 20:38
 */
@ApiModel(value = "GetDmPlatAddrReqDTO", description = "获取数币平台地址请求DTO")
public class GetDmPlatAddrReqDTO {

    @ApiModelProperty(value = "区块链网络", required = true, example = "tron-nile")
    @NotBlank(message = "网络不能为空")
    private String network;

    @ApiModelProperty(value = "账户类型（P:本金账户, F:手续费账户）", required = true, example = "F")
    @NotBlank(message = "账户类型不能为空")
    private String type;

    @ApiModelProperty(value = "币种", required = true, example = "USDT")
    @NotBlank(message = "币种不能为空")
    private String ccy;

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }
}