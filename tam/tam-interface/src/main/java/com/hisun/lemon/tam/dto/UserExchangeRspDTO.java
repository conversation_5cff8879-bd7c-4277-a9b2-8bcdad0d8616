package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("兑换响应")
public class UserExchangeRspDTO {

    /**
     * 转出金额
     */
    @ApiModelProperty(name = "fromAmount", value = "转出金额")
    private BigDecimal fromAmount;
    /**
     * 转入金额（含手续费后）
     */
    @ApiModelProperty(name = "toAmount", value = "转入金额（含手续费后）")
    private BigDecimal toAmount;
    /**
     * 兑换订单号
     */
    @ApiModelProperty(name = "orderNo", value = "兑换订单号")
    private String orderNo;
    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "PENDING（待审核）/ FIRST_AUDIT（初审中）/ SECOND_AUDIT（复核中）/ APPROVED（审核通过）/ REJECTED（拒绝）/ SUCCESS（成功）/ FAILED（失败）")
    private String status;
    /**
     * 手续费
     */
    @ApiModelProperty(name = "feeAmount", value = "手续费")
    private BigDecimal feeAmount;
    /**
     * 兑换方向
     */
    @ApiModelProperty(name = "direction", value = "兑换方向")
    private String direction;
    /**
     * 实际转出金额
     */
    @ApiModelProperty(name = "actualFromAmount", value = "实际转出金额")
    private BigDecimal actualFromAmount;

    public BigDecimal getFromAmount() {
        return fromAmount;
    }

    public void setFromAmount(BigDecimal fromAmount) {
        this.fromAmount = fromAmount;
    }

    public BigDecimal getToAmount() {
        return toAmount;
    }

    public void setToAmount(BigDecimal toAmount) {
        this.toAmount = toAmount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public BigDecimal getActualFromAmount() {
        return actualFromAmount;
    }

    public void setActualFromAmount(BigDecimal actualFromAmount) {
        this.actualFromAmount = actualFromAmount;
    }
}
