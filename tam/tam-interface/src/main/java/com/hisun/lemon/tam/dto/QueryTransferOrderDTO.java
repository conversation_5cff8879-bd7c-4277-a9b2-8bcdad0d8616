package com.hisun.lemon.tam.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 查询历史转账记录
 * 
 * <AUTHOR>
 * @date 2017年7月13日
 * @time 下午20:21:30
 *
 */
@ClientValidated
@ApiModel("查询历史转账记录")
public class QueryTransferOrderDTO extends GenericDTO<NoBody> {
	
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	@ApiModelProperty(name = "pageNum", value = "当前页")
	private Integer pageNum;
	@ApiModelProperty(name = "pageSize", value = "每页大小")
	private Integer pageSize;
}
