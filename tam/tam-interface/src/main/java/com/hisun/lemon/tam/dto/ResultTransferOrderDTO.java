package com.hisun.lemon.tam.dto;

import java.math.BigDecimal;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 结果通知对象
 * 
 * <AUTHOR>
 * @date 2017年7月13日
 * @time 下午20:21:30
 *
 */
@ApiModel("结果通知对象")
public class ResultTransferOrderDTO {
	/**
	 * @Fields orderSts 状态
	 */
	@ApiModelProperty(name = "orderSts", value = "状态 S:成功    F:失败")
	@NotEmpty(message = "TAM10004")
	private String orderSts;

	/**
	 * @Fields orderNo 原订单号
	 */
	@ApiModelProperty(name = "orderNo", value = "订单号")
	@NotEmpty(message = "TAM10005")
	private String orderNo;
	
	/**
	 * 金额
	 */
	@ApiModelProperty(name = "amount", value = "金额")
	@NotNull(message = "TAM10001")
	@Min(value = 0, message = "TAM10001")
	private BigDecimal amount;


	
	 /**
     * @Fields txType 交易类型
     */
	@ApiModelProperty(name = "txType", value = "交易类型")
	@NotEmpty(message = "TAM10002")
    private String txType;
    /**
     * @Fields busType 业务类型  用户 0301   银行卡  0302  面对面  0303
     */
	@ApiModelProperty(name = "busType", value = "业务类型  用户 0301   银行卡  0302  面对面  0303")
	@NotEmpty(message = "TAM10003")
    private String busType;
	
	/**
	 * @Fields orderCcy 币种
	 */
	@ApiModelProperty(name = "orderCcy", value = "币种")
	private String orderCcy;
	
	
	public String getOrderCcy() {
		return orderCcy;
	}
	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}
	public String getOrderSts() {
		return orderSts;
	}
	public void setOrderSts(String orderSts) {
		this.orderSts = orderSts;
	}

	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public String getTxType() {
		return txType;
	}
	public void setTxType(String txType) {
		this.txType = txType;
	}
	public String getBusType() {
		return busType;
	}
	public void setBusType(String busType) {
		this.busType = busType;
	}
}
