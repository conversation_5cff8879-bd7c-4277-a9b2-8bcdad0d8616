package com.hisun.lemon.tam.constants;

public enum CcyAcItemEnum {

    USD_CSH("2241030001","USD"),
    HKD_CSH("2331020001","HKD"),
    USDT_CSH("3241030001","USDT"),
    USDC_CSH("3241030002","USDC");

    private String itemNo;

    private String ccy;

    private CcyAcItemEnum(String itemNo, String ccy) {
        this.itemNo = itemNo;
        this.ccy = ccy;
    }

    public String getItemNo() {
        return itemNo;
    }

    public String getCcy() {
        return ccy;
    }
}
