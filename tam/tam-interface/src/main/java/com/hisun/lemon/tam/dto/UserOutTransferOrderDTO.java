package com.hisun.lemon.tam.dto;

import java.math.BigDecimal;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 创建转账到用户对象(对外接口)
 * 
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("创建转账到用户对象(对外接口)")
public class UserOutTransferOrderDTO {

	/**
	 * @Fields userMblNo用户手机号
	 */
	@ApiModelProperty(name = "userMblNo", value = "用户手机号")
	@NotEmpty(message = "TAM10023")
	private String userMblNo;

	/**
	 * @Fields remark 备注
	 */
	@ApiModelProperty(name = "remark", value = "备注")
	@Length(max = 25)
	private String remark;
	/**
	 * @Fields busType 业务类型 用户 0301 银行卡 0302 面对面 0303
	 */
	@NotEmpty(message = "TAM10003")
	@ApiModelProperty(name = "busType", value = "业务类型   用户 0301   银行卡  0302  面对面  0303")
	@Length(max = 4)
	private String busType;

	/**
	 * @Fields mblNo 转账账户
	 */
	@ApiModelProperty(name = "mblNo", value = "转账账户")
	@NotEmpty(message = "TAM10007")
	@Length(max = 20)
	private String mblNo;
	/**
	 * 金额
	 */
	@ApiModelProperty(name = "amount", value = "金额")
	@NotNull(message = "TAM10001")
	@Min(value = 0, message = "TAM10001")
	private BigDecimal amount;

	/**
	 * @Fields orderCcy 币种
	 */
	@ApiModelProperty(name = "orderCcy", value = "币种")
	@Length(max = 4)
	private String orderCcy;

	@ApiModelProperty(name = "payPassword", value = "支付密码(密文)")
	private String payPassword;
	
	@ApiModelProperty(name = "validateRandom", value = "支付密码随机数")
	private String validateRandom;

	@ApiModelProperty(name = "seaRandom", value = "密码随机数")
	private String seaRandom;

	public String getSeaRandom() {
		return seaRandom;
	}

	public void setSeaRandom(String seaRandom) {
		this.seaRandom = seaRandom;
	}

	public String getOrderCcy() {
		return orderCcy;
	}

	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}

	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public String getBusType() {
		return busType;
	}

	public String getUserMblNo() {
		return userMblNo;
	}

	public void setUserMblNo(String userMblNo) {
		this.userMblNo = userMblNo;
	}

	public void setBusType(String busType) {
		this.busType = busType;
	}

	public String getRemark() {
		return remark;
	}

	public String getValidateRandom() {
		return validateRandom;
	}

	public void setValidateRandom(String validateRandom) {
		this.validateRandom = validateRandom;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
