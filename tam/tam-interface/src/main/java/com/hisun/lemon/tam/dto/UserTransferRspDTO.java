package com.hisun.lemon.tam.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 转账到账户响应
 * 
 * <AUTHOR>
 * @date 2017年7月21日
 * @time 上午9:27:30
 *
 */
@ApiModel("转账到账户响应")
public class UserTransferRspDTO {
	/**
	 * 转账金额
	 */
	@ApiModelProperty(name = "amount", value = "转账金额")
	private BigDecimal amount;
	/**
	 * 转账订单号
	 */
	@ApiModelProperty(name = "orderNo", value = "转账订单号")
	private String orderNo;

	/**
	 * 状态
	 */
	@ApiModelProperty(name = "orderSts", value = "状态   S:成功   F：失败   U:转账初始状态")
	private String orderSts;

	/**
	 * 手续费
	 */
	@ApiModelProperty(name = "fee", value = "手续费")
	private BigDecimal fee;

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getOrderSts() {
		return orderSts;
	}

	public void setOrderSts(String orderSts) {
		this.orderSts = orderSts;
	}

	public BigDecimal getFee() {
		return fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}

}
