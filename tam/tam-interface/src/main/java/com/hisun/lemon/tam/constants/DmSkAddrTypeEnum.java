package com.hisun.lemon.tam.constants;

/**
 * 数币收款地址类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 17:46
 */
public enum DmSkAddrTypeEnum {

    PLATFORM_INTERNAL("Internal"),
    WHITELIST_ONCHAIN("Whitelisted"),
    EXTERNAL_ONCHAIN("External");

    private final String description;

    DmSkAddrTypeEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
