package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("获取汇率响应")
public class ExchangeRateRspDTO {

    @ApiModelProperty(name = "源币种代码", value = "fromCoin")
    private String fromCoin;

    @ApiModelProperty(name = "目标币种代码", value = "toCoin")
    private String toCoin;

    @ApiModelProperty(name = "汇率", value = "exchangeRate")
    private BigDecimal exchangeRate;

    public String getFromCoin() {
        return fromCoin;
    }

    public void setFromCoin(String fromCoin) {
        this.fromCoin = fromCoin;
    }

    public String getToCoin() {
        return toCoin;
    }

    public void setToCoin(String toCoin) {
        this.toCoin = toCoin;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }
}
