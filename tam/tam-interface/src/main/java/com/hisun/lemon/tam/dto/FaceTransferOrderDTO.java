package com.hisun.lemon.tam.dto;

import java.math.BigDecimal;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 创建面对面对象
 * 
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("面对面收款对象")
public class FaceTransferOrderDTO {
	
	/**
	 * @Fields remark 备注
	 */
	@ApiModelProperty(name = "remark", value = "备注")
	@Length(max =25)
	private String remark;
	
	 public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
     * @Fields crossUserId 对方内部号
     */
	@ApiModelProperty(name = "crossUserId", value = "对方用户号")
	@NotEmpty(message = "TAM10011")
	@Length(max =20)
    private String crossUserId;

	public String getCrossUserId() {
		return crossUserId;
	}

	public void setCrossUserId(String crossUserId) {
		this.crossUserId = crossUserId;
	}

	/**
	 * 金额
	 */
	@ApiModelProperty(name = "amount", value = "金额")
	private BigDecimal amount;
	/**
	 * @Fields txType 交易类型
	 */
	@ApiModelProperty(name = "txType", value = "交易类型   转账03")
	@NotEmpty(message = "TAM10002")
	@Length(max =2)
	private String txType;
	/**
	 * @Fields busType 业务类型 用户 0301 银行卡 0302 面对面 0303
	 */
	@ApiModelProperty(name = "busType", value = "业务类型   用户 0301   银行卡  0302  面对面  0303")
	@NotEmpty(message = "TAM10003")
	@Length(max =4)
	private String busType;

	/**
	 * @Fields orderCcy 币种
	 */
	@ApiModelProperty(name = "orderCcy", value = "币种")
	@Length(max =4)
	private String orderCcy;
	
	/**
	 * 订单来源渠道
	 * WEB:WEB站点 <br/>
	 * APP:手机APP<br/>
	 * HALL:营业厅<br/>
	 * OTHER:其他渠道<br/>
	 */
	@ApiModelProperty(name = "sysChannel", value = "订单来源渠道(WEB:web站点|APP:APP手机|HALL:营业厅|OTHER:其他渠道)")
	@Length(max = 5)
    private String sysChannel;

	public String getTxType() {
		return txType;
	}

	public void setTxType(String txType) {
		this.txType = txType;
	}

	public String getBusType() {
		return busType;
	}

	public void setBusType(String busType) {
		this.busType = busType;
	}

	public String getOrderCcy() {
		return orderCcy;
	}

	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getSysChannel() {
		return sysChannel;
	}

	public void setSysChannel(String sysChannel) {
		this.sysChannel = sysChannel;
	}
}
