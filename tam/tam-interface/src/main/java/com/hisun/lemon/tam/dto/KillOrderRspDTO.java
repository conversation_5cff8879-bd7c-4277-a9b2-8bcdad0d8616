package com.hisun.lemon.tam.dto;

import org.hibernate.validator.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 对账数据撤单补单接口数据响应
 * 
 * <AUTHOR>
 * @date 2017年7月21日
 * @time 上午9:27:30
 *
 */
@ApiModel("对账数据撤单补单接口数据响应")
public class KillOrderRspDTO {
	/**
	 * 状态
	 */
	@ApiModelProperty(name = "type", value = "0602 对成功订单     0603对失败订单")
	private String type;
	/**
	 * 订单号
	 */
	@ApiModelProperty(name = "orderNo", value = "订单号")
	@NotEmpty(message = "TAM10005")
	private String orderNo;
	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
}
