package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("兑换申请对象")
public class UserExchangeOrderDTO {

    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    @ApiModelProperty(name = "fromAcNo", value = "转出账户编号")
    private String fromAcNo;

    @ApiModelProperty(name = "toAcNo", value = "转入账户编号")
    private String toAcNo;

    @ApiModelProperty(name = "fromCoin", value = "转出币种")
    private String fromCoin;

    @ApiModelProperty(name = "toCoin", value = "转入币种")
    private String toCoin;

    @ApiModelProperty(name = "fromAmount", value = "转出金额")
    private BigDecimal fromAmount;

    @ApiModelProperty(name = "toAmount", value = "转入金额")
    private BigDecimal toAmount;

    @ApiModelProperty(name = "actualFromAmount", value = "实际转出金额（减去手续费）")
    private BigDecimal actualFromAmount;

    @ApiModelProperty(name = "exchangeRate", value = "汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(name = "exchangeFee", value = "手续费")
    private BigDecimal exchangeFee;

    @ApiModelProperty(name = "orderNo", value = "订单编号")
    private String orderNo;

    @ApiModelProperty(name = "direction", value = "兑换方向")
    private String direction;

    @ApiModelProperty(name = "payPwd", value = "支付密码")
    private String payPwd;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFromAcNo() {
        return fromAcNo;
    }

    public void setFromAcNo(String fromAcNo) {
        this.fromAcNo = fromAcNo;
    }

    public String getToAcNo() {
        return toAcNo;
    }

    public void setToAcNo(String toAcNo) {
        this.toAcNo = toAcNo;
    }

    public String getFromCoin() {
        return fromCoin;
    }

    public void setFromCoin(String fromCoin) {
        this.fromCoin = fromCoin;
    }

    public String getToCoin() {
        return toCoin;
    }

    public void setToCoin(String toCoin) {
        this.toCoin = toCoin;
    }

    public BigDecimal getFromAmount() {
        return fromAmount;
    }

    public void setFromAmount(BigDecimal fromAmount) {
        this.fromAmount = fromAmount;
    }


    public BigDecimal getActualFromAmount() {
        return actualFromAmount;
    }

    public void setActualFromAmount(BigDecimal actualFromAmount) {
        this.actualFromAmount = actualFromAmount;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getExchangeFee() {
        return exchangeFee;
    }

    public void setExchangeFee(BigDecimal exchangeFee) {
        this.exchangeFee = exchangeFee;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public BigDecimal getToAmount() {
        return toAmount;
    }

    public void setToAmount(BigDecimal toAmount) {
        this.toAmount = toAmount;
    }
}
