package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

public class ExchangeRecordDTO {

    @ApiModelProperty(name = "userId", value = "用户id")
    private String userId;

    @ApiModelProperty(name = "accountNo", value = "账户号码")
    private String accountNo;

    /**
     * 交易起始（时间类型）
     */
    @ApiModelProperty(name = "txTmBeginStr", value = "交易起始（时间类型）yyyyMMddHHmmss")
    private String txTmBeginStr;
    @ApiModelProperty(hidden = true)
    private LocalDateTime txTmBegin;
    /**
     * 交易结束（时间类型）
     */
    @ApiModelProperty(name = "txTmEndStr", value = "交易结束（时间类型）yyyyMMddHHmmss")
    private String txTmEndStr;
    @ApiModelProperty(hidden = true)
    private LocalDateTime txTmEnd;

    /**
     * 单页记录数
     */
    @ApiModelProperty(name = "pageSize", value = "每页大小")
    @Min(value=1, message="BIL10008")
    @NotNull(message = "BIL10007")
    private Integer pageSize;
    /**
     * 页数
     */
    @ApiModelProperty(name = "pageNo", value = "页数")
    @Min(value=1, message="BIL10010")
    @NotNull(message = "BIL10009")
    private Integer pageNo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getTxTmBeginStr() {
        return txTmBeginStr;
    }

    public void setTxTmBeginStr(String txTmBeginStr) {
        this.txTmBeginStr = txTmBeginStr;
    }

    public LocalDateTime getTxTmBegin() {
        return txTmBegin;
    }

    public void setTxTmBegin(LocalDateTime txTmBegin) {
        this.txTmBegin = txTmBegin;
    }

    public String getTxTmEndStr() {
        return txTmEndStr;
    }

    public void setTxTmEndStr(String txTmEndStr) {
        this.txTmEndStr = txTmEndStr;
    }

    public LocalDateTime getTxTmEnd() {
        return txTmEnd;
    }

    public void setTxTmEnd(LocalDateTime txTmEnd) {
        this.txTmEnd = txTmEnd;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }
}
