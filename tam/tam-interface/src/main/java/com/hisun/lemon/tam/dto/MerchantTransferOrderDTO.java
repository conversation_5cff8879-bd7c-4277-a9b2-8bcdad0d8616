package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商户转账给用户请求对象
 * 
 * <AUTHOR>
 * @date 2017年11月14日
 * @time 上午9:27:30
 *
 */
@ApiModel("商户转账到用户")
public class MerchantTransferOrderDTO {

	/**
	 * @Fields 转账商户用户号
	 */
	@ApiModelProperty(name = "merchantId", value = "商户号")
	@NotEmpty(message = "TAM10026")
	private String merchantId;

	/**
	 * 外围模块订单号
	 */
	@ApiModelProperty(name = "extOrderNo", value = "外围模块订单号")
	@NotEmpty(message="TAM10027")
	@Length(max =28)
	private String extOrderNo;

	/**
	 * @Fields remark 备注
	 */
	@ApiModelProperty(name = "remark", value = "备注")
	@Length(max = 25)
	private String remark;
	/**
	 * @Fields busType 业务类型 用户 0301 银行卡 0302 面对面 0303   商户转给用户 0304
	 */
	@NotEmpty(message = "TAM10003")
	@ApiModelProperty(name = "busType", value = "业务类型0304 商户转给用户")
	@Length(max = 4)
	private String busType;

	/**
	 * @Fields mblNo 转账账户
	 */
	@ApiModelProperty(name = "mblNo", value = "转账收款用户账户手机号")
	@NotEmpty(message = "TAM10007")
	@Length(max = 20)
	private String mblNo;
	/**
	 * 金额
	 */
	@ApiModelProperty(name = "amount", value = "转账金额")
	@NotNull(message = "TAM10001")
	@Min(value = 0, message = "TAM10001")
	private BigDecimal amount;

	/**
	 * @Fields orderCcy 币种
	 */
	@ApiModelProperty(name = "orderCcy", value = "币种")
	@Length(max = 4)
	private String orderCcy;

	@ApiModelProperty(name = "version", value = "版本号")
	@Length(max = 10)
	private String version;

	//@ApiModelProperty(name = "payPassword", value = "支付密码(密文)")
	//private String payPassword;

	//@ApiModelProperty(name = "validateRandom", value = "支付密码随机数")
	//private String validateRandom;

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getBusType() {
		return busType;
	}

	public void setBusType(String busType) {
		this.busType = busType;
	}

	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getOrderCcy() {
		return orderCcy;
	}

	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	public String getExtOrderNo() {
		return extOrderNo;
	}

	public void setExtOrderNo(String extOrderNo) {
		this.extOrderNo = extOrderNo;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}
}
