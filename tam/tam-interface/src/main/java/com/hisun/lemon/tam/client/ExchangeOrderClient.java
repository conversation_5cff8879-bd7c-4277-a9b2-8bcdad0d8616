package com.hisun.lemon.tam.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.tam.dto.ExchangeOrderDetailRspDTO;
import com.hisun.lemon.tam.dto.ExchangeRateRspDTO;
import com.hisun.lemon.tam.dto.UserExchangeOrderDTO;
import com.hisun.lemon.tam.dto.UserExchangeRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient("TAM")
public interface ExchangeOrderClient {

    @PostMapping(value = "/tam/exchange/rate")
    public GenericRspDTO<ExchangeRateRspDTO> queryExchangeRate(@Validated @RequestParam(name = "fromCoin") String fromCoin, @Validated @RequestParam(name = "toCoin") String toCoin);

    @PostMapping(value = "/tam/exchange/order")
    public GenericRspDTO<UserExchangeRspDTO> exchangeOrder(@Validated @RequestBody GenericDTO<UserExchangeOrderDTO> req);

    @PostMapping(value = "/tam/exchange/order/execute")
    public GenericRspDTO<NoBody> executeExchangeOrder(@Validated @RequestBody GenericDTO<UserExchangeOrderDTO> req);

    @PostMapping(value = "/tam/exchange/order/detail")
    public GenericRspDTO<ExchangeOrderDetailRspDTO> queryOrderDetail(@Validated @RequestParam(name = "orderNo") String orderNo);

    @PostMapping(value = "/tam/exchange/updateOrderSts")
    public GenericRspDTO<NoBody> updateOrderSts(@Validated @RequestParam(name = "orderNo") String orderNo, @Validated @RequestParam(name = "status") String status);

    @PostMapping(value = "/tam/exchange/order/list")
    public GenericRspDTO<List<ExchangeRateRspDTO>> queryExchangeOrderList(@Validated @RequestParam(name = "fromCoin") String fromCoin);
}
