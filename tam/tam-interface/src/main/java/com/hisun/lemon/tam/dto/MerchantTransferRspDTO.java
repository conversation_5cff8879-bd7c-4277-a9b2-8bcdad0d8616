package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;


/**
 * 商户转账到账户响应
 * 
 * <AUTHOR>
 * @date 2017年7月21日
 * @time 上午9:27:30
 *
 */
@ApiModel("商户转账到账户响应")
public class MerchantTransferRspDTO {
	/**
	 * 转账金额
	 */
	@ApiModelProperty(name = "amount", value = "转账金额")
	private BigDecimal amount;

	/**
	 * 平台系统订单号
	 */
	@ApiModelProperty(name = "orderNo", value = "平台系统订单号")
	private String orderNo;

	/**
	 * 外围业务订单号
	 */
	@ApiModelProperty(name = "extOrderNo", value = "外围系统订单号")
	private String extOrderNo;

	/**
	 * 状态
	 */
	@ApiModelProperty(name = "orderSts", value = "状态   S:成功   F：失败   U:转账初始状态")
	private String orderSts;

	/**
	 * 收款用户手机号
	 */
	@ApiModelProperty(name = "mblNo", value = "收款用户手机号")
	@Length(max = 20)
	private String mblNo;

	/**
	 * 手续费
	 */
	@ApiModelProperty(name = "fee", value = "手续费")
	private BigDecimal fee;

	/** 订单日期 */
	@ApiModelProperty(name = "orderDate", value = "交易日期")
	private String tradeDate;

	/** 订单时间 */
	@ApiModelProperty(name = "orderTime", value = "交易时间")
	private String tradeTime;


	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getOrderSts() {
		return orderSts;
	}

	public void setOrderSts(String orderSts) {
		this.orderSts = orderSts;
	}

	public BigDecimal getFee() {
		return fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}

	public String getExtOrderNo() {
		return extOrderNo;
	}

	public void setExtOrderNo(String extOrderNo) {
		this.extOrderNo = extOrderNo;
	}

	public String getMblNo() {
		return mblNo;
	}

	public void setMblNo(String mblNo) {
		this.mblNo = mblNo;
	}

	public String getTradeDate() {
		return tradeDate;
	}

	public void setTradeDate(String tradeDate) {
		this.tradeDate = tradeDate;
	}

	public String getTradeTime() {
		return tradeTime;
	}

	public void setTradeTime(String tradeTime) {
		this.tradeTime = tradeTime;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
}
