DROP TABLE IF EXISTS `tam_transfer_ord`;
CREATE TABLE `tam_transfer_ord` (
  `order_no` varchar(28) NOT NULL COMMENT '转账订单编号',
  `user_id` varchar(20) NOT NULL COMMENT '用户编号',
  `cross_user_id` varchar(20) DEFAULT NULL COMMENT '对方内部号',
  `mbl_no` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `mbl_no_hid` varchar(20) DEFAULT NULL COMMENT '脱敏手机号',
  `bus_type` varchar(4) NOT NULL COMMENT '业务类型',
  `tx_type` varchar(2) NOT NULL DEFAULT '03' COMMENT '交易类型',
  `order_ccy` varchar(4) NOT NULL COMMENT '币种',
  `amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `fee` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '服务费',
  `order_sts` varchar(2) NOT NULL DEFAULT 'U' COMMENT 'U:初始  S:成功  F：失败',
  `crd_ac_typ` varchar(2) DEFAULT NULL COMMENT '卡种，D借记卡，C贷记卡',
  `ac_tm` date NOT NULL COMMENT '会计日期',
  `tx_tm` datetime NOT NULL COMMENT '交易时间',
  `modify_time` datetime NOT NULL COMMENT '最后修改时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `crd_corp_org` varchar(16) DEFAULT NULL COMMENT '资金机构',
  `corp_org_snm` varchar(64) DEFAULT NULL COMMENT '合作资金机构名称',
  `cap_crd_nm` varchar(64) DEFAULT NULL COMMENT '银行卡户名',
  `crd_no_enc` varchar(128) DEFAULT NULL COMMENT '加密银行卡号',
  `cap_crd_no` varchar(30) DEFAULT NULL COMMENT '银行卡号',
  `cap_corg_nm` varchar(255) DEFAULT NULL COMMENT '支行名称',
  `last_cap_crd_no` varchar(4) DEFAULT NULL COMMENT '银行卡后四位',
  `bus_order_no` varchar(25) DEFAULT NULL COMMENT '收银订单号',
  `last_mbl_no` varchar(4) DEFAULT NULL COMMENT '手机号的后四位',
  `good_desc` varchar(20) DEFAULT NULL COMMENT '描述',
  `remark` varchar(20) DEFAULT NULL COMMENT '备注',
  `area_desc` varchar(10) DEFAULT NULL COMMENT '区号',
  `nation_name` varchar(20) DEFAULT NULL COMMENT '国家名称',
  `gather_name` varchar(64) NOT NULL DEFAULT '' COMMENT '收款人姓名',
  `payer_name` varchar(64) DEFAULT NULL COMMENT '付款方名字',
  `paye_mbl_no` varchar(20) DEFAULT NULL COMMENT '付款方手机号',
  `tm_smp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10001','zh','转账金额不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10002','zh','交易类型不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10003','zh','业务类型不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10004','zh','状态不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10005','zh','转账订单号不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10006','zh','内部用户号不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10007','zh','转账账户不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10008','zh','银行卡号不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10009','zh','支行名称不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10010','zh','收款人姓名不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10011','zh','对方用户号不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10012','zh','资金机构不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10013','zh','卡种不能为空!',NOW(),NOW());
INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10014','zh','银行卡户名不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10016','zh','原订单号不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10017','zh','手续费不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10018','zh','国家名称不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10019','zh','区号不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10021','zh','转账金额必须大于0!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10020','zh','资金合作机构名称不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10022','zh','银行卡位数不正确!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10023','zh','用户手机号不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10024','zh','支付密码不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM10025','zh','支付密码随机数不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('TAM10026','zh','商户号不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('TAM10027','zh','业务订单号不能为空!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('TAM10028','zh','不支持用户转账!',NOW(),NOW());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20001','zh','未找到对应的转账记录!',now(),now()); 

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20002','zh','转账金额有误!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20003','zh','更新转账流水失败!',now(),now()); 

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20004','zh','转账下单失败!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20006','zh','历史转账记录为空!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20007','zh','账户余额不足!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20008','zh','借贷不平衡!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20009','zh','账户信息不存在!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20010','zh','不能给自己转账!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20011','zh','订单已经成功!',now(),now());

insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TAM20012','zh','撤单失败!',now(),now());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime) 
VALUES ('TAM20013','zh','对不起,账户状态异常,收款账户已被列入黑名单!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('TAM20014','zh','生成对账文件，写文件失败!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('TAM20015','zh','上传对账文件失败!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('TAM20016','zh','生成对账标志文件失败!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('TAM20017','zh','获取本地路径失败!',NOW(),NOW());

INSERT INTO lemon_msg_info(msg_cd,LANGUAGE,msg_info,create_time,modifyTime)
VALUES ('TAM20018','zh','不能重复下单!',NOW(),NOW());
 