<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.tam.dao.ITransferOrderDao">
	<resultMap id="BaseResultMap" type="com.hisun.lemon.tam.entity.TransferOrderDO">
		<id column="order_no" property="orderNo" jdbcType="VARCHAR" />
		<result column="user_id" property="userId" jdbcType="VARCHAR" />
		<result column="cross_user_id" property="crossUserId" jdbcType="VARCHAR" />
		<result column="mbl_no" property="mblNo" jdbcType="VARCHAR" />
		<result column="mbl_no_hid" property="mblNoHid" jdbcType="VARCHAR" />
		<result column="bus_type" property="busType" jdbcType="VARCHAR" />
		<result column="tx_type" property="txType" jdbcType="VARCHAR" />
		<result column="order_ccy" property="orderCcy" jdbcType="VARCHAR" />
		<result column="amount" property="amount" jdbcType="DECIMAL" />
		<result column="actual_trade_amt" property="actualTradeAmt" jdbcType="DECIMAL" />
		<result column="fee" property="fee" jdbcType="DECIMAL" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="sys_channel" property="sysChannel" jdbcType="VARCHAR" />
		<result column="gather_name" property="gatherName" jdbcType="VARCHAR" />
		<result column="order_sts" property="orderSts" jdbcType="VARCHAR" />
		<result column="ac_tm" property="acTm" jdbcType="DATE" />
		<result column="tx_tm" property="txTm" jdbcType="TIMESTAMP" />
		<result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="crd_ac_typ" property="crdAcTyp" jdbcType="VARCHAR" />
		<result column="crd_corp_org" property="crdCorpOrg" jdbcType="VARCHAR" />
		<result column="corp_org_snm" property="corpOrgSnm" jdbcType="VARCHAR" />
		<result column="cap_crd_nm" property="capCrdNm" jdbcType="VARCHAR" />
		<result column="crd_no_enc" property="crdNoEnc" jdbcType="VARCHAR" />
		<result column="cap_crd_no" property="capCrdNo" jdbcType="VARCHAR" />
		<result column="cap_corg_nm" property="capCorgNm" jdbcType="VARCHAR" />
		<result column="bus_order_no" property="busOrderNo" jdbcType="VARCHAR" />
		<result column="ext_order_no" property="extOrderNo" jdbcType="VARCHAR" />
		<result column="last_cap_crd_no" property="lastCapCrdNo"
			jdbcType="VARCHAR" />
		<result column="last_mbl_no" property="lastMblNo" jdbcType="VARCHAR" />
		<result column="good_desc" property="goodDesc" jdbcType="VARCHAR" />
		<result column="area_desc" property="areaDesc" jdbcType="VARCHAR" />
		<result column="nation_name" property="nationName" jdbcType="VARCHAR" />
		<result column="payer_name" property="payerName" jdbcType="VARCHAR" />
		<result column="paye_mbl_no" property="payeMblNo" jdbcType="VARCHAR" />
		<result column="fk_ac_no" property="fkAcNo" jdbcType="VARCHAR" />
		<result column="sk_ac_no" property="skAcNo" jdbcType="VARCHAR" />
	</resultMap>

	<sql id="Base_Column_List">
		cross_user_id,order_no, user_id,mbl_no,mbl_no_hid, bus_type, tx_type, order_ccy, amount, fee,
		remark,sys_channel, gather_name, order_sts, ac_tm, tx_tm, modify_time, create_time,
		tm_smp, crd_ac_typ,
		crd_corp_org, corp_org_snm, cap_crd_nm, crd_no_enc, cap_crd_no, cap_corg_nm,
		bus_order_no,ext_order_no,
		last_cap_crd_no, last_mbl_no, good_desc, area_desc, nation_name,payer_name,
		paye_mbl_no, fk_ac_no, sk_ac_no
	</sql>
	<sql id="History_Transfer_List">
		DISTINCT
		cross_user_id,mbl_no,area_desc,nation_name,cap_crd_no,cap_corg_nm,crd_corp_org,corp_org_snm,crd_ac_typ,gather_name,cap_crd_nm,bus_type,tx_type
	</sql>
	<select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from tam_transfer_ord
		where order_no = #{orderNo,jdbcType=VARCHAR}
	</select>

	<delete id="delete" parameterType="java.lang.String">
		delete from tam_transfer_ord
		where order_no = #{orderNo,jdbcType=VARCHAR}
	</delete>

	<insert id="insert" parameterType="com.hisun.lemon.tam.entity.TransferOrderDO">
		insert into tam_transfer_ord
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="orderNo != null">
				order_no,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="crossUserId != null">
				cross_user_id,
			</if>
			<if test="mblNo != null">
				mbl_no,
			</if>
			<if test="mblNoHid != null" >
                mbl_no_hid,
            </if>
			<if test="busType != null">
				bus_type,
			</if>
			<if test="txType != null">
				tx_type,
			</if>
			<if test="orderCcy != null">
				order_ccy,
			</if>
			<if test="amount != null">
				amount,
			</if>
			<if test="actualTradeAmt != null">
				actual_trade_amt,
			</if>
			<if test="fee != null">
				fee,
			</if>
			<if test="remark != null">
				remark,
			</if>
			<if test="sysChannel != null" >
                sys_channel,
            </if>
			<if test="gatherName != null">
				gather_name,
			</if>
			<if test="orderSts != null">
				order_sts,
			</if>
			<if test="acTm != null">
				ac_tm,
			</if>
			<if test="txTm != null">
				tx_tm,
			</if>
			<if test="modifyTime != null">
				modify_time,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="crdAcTyp != null">
				crd_ac_typ,
			</if>
			<if test="crdCorpOrg != null">
				crd_corp_org,
			</if>
			<if test="corpOrgSnm != null">
				corp_org_snm,
			</if>
			<if test="capCrdNm != null">
				cap_crd_nm,
			</if>
			<if test="capCorgNm != null">
				cap_corg_nm,
			</if>
			<if test="crdNoEnc != null">
				crd_no_enc,
			</if>
			<if test="capCrdNo != null">
				cap_crd_no,
			</if>
			<if test="busOrderNo != null">
				bus_order_no,
			</if>
			<if test="extOrderNo != null">
				ext_order_no,
			</if>
			<if test="lastCapCrdNo != null">
				last_cap_crd_no,
			</if>
			<if test="lastMblNo != null">
				last_mbl_no,
			</if>
			<if test="goodDesc != null">
				good_desc,
			</if>
			<if test="areaDesc != null">
				area_desc,
			</if>
			<if test="nationName != null">
				nation_name,
			</if>
			<if test="payerName != null">
				payer_name,
			</if>
			<if test="payeMblNo != null">
				paye_mbl_no,
			</if>
			<if test="fkAcNo != null">
				fk_ac_no,
			</if>
			<if test="skAcNo != null">
				sk_ac_no,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
				#{userId,jdbcType=VARCHAR},
			</if>
			<if test="crossUserId != null">
				#{crossUserId,jdbcType=VARCHAR},
			</if>
			<if test="mblNo != null">
				#{mblNo,jdbcType=VARCHAR},
			</if>
			<if test="mblNoHid != null" >
                #{mblNoHid,jdbcType=VARCHAR},
            </if>
			<if test="busType != null">
				#{busType,jdbcType=VARCHAR},
			</if>
			<if test="txType != null">
				#{txType,jdbcType=VARCHAR},
			</if>
			<if test="orderCcy != null">
				#{orderCcy,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				#{amount,jdbcType=DECIMAL},
			</if>
			<if test="actualTradeAmt != null">
				#{actualTradeAmt,jdbcType=DECIMAL},
			</if>
			<if test="fee != null">
				#{fee,jdbcType=DECIMAL},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="sysChannel != null" >
                #{sysChannel,jdbcType=VARCHAR},
            </if>
			<if test="gatherName != null">
				#{gatherName,jdbcType=VARCHAR},
			</if>
			<if test="orderSts != null">
				#{orderSts,jdbcType=VARCHAR},
			</if>
			<if test="acTm != null">
				#{acTm,jdbcType=DATE},
			</if>
			<if test="txTm != null">
				#{txTm,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyTime != null">
				#{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="crdAcTyp != null">
				#{crdAcTyp,jdbcType=VARCHAR},
			</if>
			<if test="crdCorpOrg != null">
				#{crdCorpOrg,jdbcType=VARCHAR},
			</if>
			<if test="corpOrgSnm != null">
				#{corpOrgSnm,jdbcType=VARCHAR},
			</if>
			<if test="capCrdNm != null">
				#{capCrdNm,jdbcType=VARCHAR},
			</if>
			<if test="capCorgNm != null">
				#{capCorgNm,jdbcType=VARCHAR},
			</if>
			<if test="crdNoEnc != null">
				#{crdNoEnc,jdbcType=VARCHAR},
			</if>
			<if test="capCrdNo != null">
				#{capCrdNo,jdbcType=VARCHAR},
			</if>
			<if test="busOrderNo != null">
				#{busOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="extOrderNo != null">
				#{extOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="lastCapCrdNo != null">
				#{lastCapCrdNo,jdbcType=VARCHAR},
			</if>
			<if test="lastMblNo != null">
				#{lastMblNo,jdbcType=VARCHAR},
			</if>
			<if test="goodDesc != null">
				#{goodDesc,jdbcType=VARCHAR},
			</if>
			<if test="areaDesc != null">
				#{areaDesc,jdbcType=VARCHAR},
			</if>
			<if test="nationName != null">
				#{nationName,jdbcType=VARCHAR},
			</if>
			<if test="payerName != null">
				#{payerName,jdbcType=VARCHAR},
			</if>
			<if test="payeMblNo != null">
				#{payeMblNo,jdbcType=VARCHAR},
			</if>
			<if test="fkAcNo != null">
				#{fkAcNo,jdbcType=VARCHAR},
			</if>
			<if test="skAcNo != null">
				#{skAcNo,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>

	<update id="update" parameterType="com.hisun.lemon.tam.entity.TransferOrderDO">
		update tam_transfer_ord
		<set>
			<if test="userId != null">
				user_id = #{userId,jdbcType=VARCHAR},
			</if>
			<if test="crossUserId != null">
				cross_user_id = #{crossUserId,jdbcType=VARCHAR},
			</if>
			<if test="mblNo != null">
				mbl_no = #{mblNo,jdbcType=VARCHAR},
			</if>
			<if test="mblNoHid != null" >
                mbl_no_hid = #{mblNoHid,jdbcType=VARCHAR},
            </if>
			<if test="busType != null">
				bus_type = #{busType,jdbcType=VARCHAR},
			</if>
			<if test="txType != null">
				tx_type = #{txType,jdbcType=VARCHAR},
			</if>
			<if test="orderCcy != null">
				order_ccy = #{orderCcy,jdbcType=VARCHAR},
			</if>
			<if test="amount != null">
				amount = #{amount,jdbcType=DECIMAL},
			</if>
			<if test="fee != null">
				fee = #{fee,jdbcType=DECIMAL},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="sysChannel != null" >
                sys_channel = #{sysChannel,jdbcType=VARCHAR},
            </if>
			<if test="gatherName != null">
				gather_name = #{gatherName,jdbcType=VARCHAR},
			</if>
			<if test="orderSts != null">
				order_sts = #{orderSts,jdbcType=VARCHAR},
			</if>
			<if test="acTm != null">
				ac_tm = #{acTm,jdbcType=DATE},
			</if>
			<if test="txTm != null">
				tx_tm = #{txTm,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyTime != null">
				modify_time = #{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="crdAcTyp != null">
				crd_ac_typ = #{crdAcTyp,jdbcType=VARCHAR},
			</if>
			<if test="crdCorpOrg != null">
				crd_corp_org = #{crdCorpOrg,jdbcType=VARCHAR},
			</if>
			<if test="corpOrgSnm != null">
				corp_org_snm = #{corpOrgSnm,jdbcType=VARCHAR},
			</if>
			<if test="capCrdNm != null">
				cap_crd_nm = #{capCrdNm,jdbcType=VARCHAR},
			</if>
			<if test="crdNoEnc != null">
				crd_no_enc = #{crdNoEnc,jdbcType=VARCHAR},
			</if>
			<if test="capCorgNm != null">
				cap_corg_nm = #{capCorgNm,jdbcType=VARCHAR},
			</if>
			<if test="capCrdNo != null">
				cap_crd_no = #{capCrdNo,jdbcType=VARCHAR},
			</if>
			<if test="busOrderNo != null">
				bus_order_no = #{busOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="extOrderNo != null">
				ext_order_no = #{extOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="lastCapCrdNo != null">
				last_cap_crd_no = #{lastCapCrdNo,jdbcType=VARCHAR},
			</if>
			<if test="lastMblNo != null">
				last_mbl_no = #{lastMblNo,jdbcType=VARCHAR},
			</if>
			<if test="goodDesc != null">
				good_desc = #{goodDesc,jdbcType=VARCHAR},
			</if>
			<if test="areaDesc != null">
				area_desc = #{areaDesc,jdbcType=VARCHAR},
			</if>
			<if test="nationName != null">
				nation_name = #{nationName,jdbcType=VARCHAR},
			</if>
			<if test="payerName != null">
				payer_name = #{payerName,jdbcType=VARCHAR},
			</if>
			<if test="payeMblNo != null">
				paye_mbl_no = #{payeMblNo,jdbcType=VARCHAR},
			</if>
			<if test="fkAcNo != null">
                fk_ac_no = #{fkAcNo,jdbcType=VARCHAR},
            </if>
			<if test="skAcNo != null">
				sk_ac_no = #{skAcNo,jdbcType=VARCHAR},
			</if>
		</set>
		where order_no = #{orderNo,jdbcType=VARCHAR}
	</update>
	<select id="find" resultMap="BaseResultMap"
		parameterType="com.hisun.lemon.tam.entity.TransferOrderDO">
		SELECT
		<include refid="Base_Column_List" />
		FROM tam_transfer_ord WHERE create_time
		IN
		(
		SELECT MAX(create_time) FROM tam_transfer_ord
		WHERE user_id = #{userId,jdbcType=VARCHAR} AND order_sts IN ('S','P')  AND bus_type IN('0301','0302')  GROUP BY
		cross_user_id,nation_name,cap_crd_no
		)
		ORDER BY create_time DESC
	</select>

	<select id="queryList" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from tam_transfer_ord
		<where>
			<if test="acTm != null">
                <![CDATA[and ac_tm = #{acTm}]]>
			</if>

			<if test="statusList != null">
				and order_sts in
				<foreach item="item" index="index" collection="statusList"
					open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>
	
	<select id="queryListByType" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List" />
        from tam_transfer_ord
        <where>
            <if test="acTm != null">
                <![CDATA[and ac_tm = #{acTm}]]>
            </if>

            <if test="statusList != null">
                and order_sts in
                <foreach item="item" index="index" collection="statusList"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and bus_type='0302'
        </where>
    </select>

	<select id="queryListByExtOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from tam_transfer_ord
		where ext_order_no = #{extOrderNo,jdbcType=VARCHAR}
	</select>
</mapper>