<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.tam.dao.DmWhtAddrDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.tam.entity.DmWhtAddrDO">
        <id column="wht_addr_id" property="whtAddrId" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="network" property="network" jdbcType="VARCHAR"/>
        <result column="eff_flg" property="effFlg" jdbcType="VARCHAR"/>
        <result column="begin_dt" property="beginDt" jdbcType="TIMESTAMP"/>
        <result column="end_dt" property="endDt" jdbcType="TIMESTAMP"/>
        <result column="wht_reason" property="whtReason" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="opr_id" property="oprId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        wht_addr_id, address, network, eff_flg, begin_dt, end_dt, wht_reason, create_time, modify_time, opr_id
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_wht_addr_list
        WHERE address = #{address}
    </select>

    <select id="find" resultMap="BaseResultMap" parameterType="com.hisun.lemon.tam.entity.DmWhtAddrDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dm_wht_addr_list
        <where>
            <if test="whtAddrId != null">
                AND wht_addr_id = #{whtAddrId}
            </if>
            <if test="address != null">
                AND address = #{address}
            </if>
            <if test="network != null">
                AND network = #{network}
            </if>
            <if test="effFlg != null">
                AND eff_flg = #{effFlg}
            </if>
            <if test="beginDt != null">
                AND begin_dt &lt;= #{beginDt}
            </if>
            <if test="endDt != null">
                AND end_dt &gt;= #{endDt}
            </if>
            <if test="whtReason != null">
                AND wht_reason = #{whtReason}
            </if>
            <if test="oprId != null">
                AND opr_id = #{oprId}
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="com.hisun.lemon.tam.entity.DmWhtAddrDO">
        INSERT INTO dm_wht_addr_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="whtAddrId != null">wht_addr_id,</if>
            <if test="address != null">address,</if>
            <if test="network != null">network,</if>
            <if test="effFlg != null">eff_flg,</if>
            <if test="beginDt != null">begin_dt,</if>
            <if test="endDt != null">end_dt,</if>
            <if test="whtReason != null">wht_reason,</if>
            <if test="createTime != null">create_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="oprId != null">opr_id,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="whtAddrId != null">#{whtAddrId},</if>
            <if test="address != null">#{address},</if>
            <if test="network != null">#{network},</if>
            <if test="effFlg != null">#{effFlg},</if>
            <if test="beginDt != null">#{beginDt},</if>
            <if test="endDt != null">#{endDt},</if>
            <if test="whtReason != null">#{whtReason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="oprId != null">#{oprId},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.tam.entity.DmWhtAddrDO">
        UPDATE dm_wht_addr_list
        <set>
            <if test="address != null">address = #{address},</if>
            <if test="network != null">network = #{network},</if>
            <if test="effFlg != null">eff_flg = #{effFlg},</if>
            <if test="beginDt != null">begin_dt = #{beginDt},</if>
            <if test="endDt != null">end_dt = #{endDt},</if>
            <if test="whtReason != null">wht_reason = #{whtReason},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="oprId != null">opr_id = #{oprId},</if>
        </set>
        WHERE wht_addr_id = #{whtAddrId}
    </update>

    <delete id="delete" parameterType="java.lang.String">
        DELETE FROM dm_wht_addr_list
        WHERE wht_addr_id = #{whtAddrId}
    </delete>

</mapper>