<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.tam.dao.ExchangeOrderDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.tam.entity.TamExchangeOrderDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="direction" property="direction" jdbcType="VARCHAR"/>
        <result column="from_coin" property="fromCoin" jdbcType="VARCHAR"/>
        <result column="from_amount" property="fromAmount" jdbcType="DECIMAL"/>
        <result column="to_coin" property="toCoin" jdbcType="VARCHAR"/>
        <result column="to_amount" property="toAmount" jdbcType="DECIMAL"/>
        <result column="fee_coin" property="feeCoin" jdbcType="VARCHAR"/>
        <result column="fee_amount" property="feeAmount" jdbcType="DECIMAL"/>
        <result column="exchange_rate" property="exchangeRate" jdbcType="DECIMAL"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="first_audit_user" property="firstAuditUser" jdbcType="VARCHAR"/>
        <result column="first_audit_time" property="firstAuditTime" jdbcType="TIMESTAMP"/>
        <result column="first_audit_result" property="firstAuditResult" jdbcType="VARCHAR"/>
        <result column="first_audit_opinion" property="firstAuditOpinion" jdbcType="VARCHAR"/>
        <result column="second_audit_user" property="secondAuditUser" jdbcType="VARCHAR"/>
        <result column="second_audit_time" property="secondAuditTime" jdbcType="TIMESTAMP"/>
        <result column="second_audit_result" property="secondAuditResult" jdbcType="VARCHAR"/>
        <result column="second_audit_opinion" property="secondAuditOpinion" jdbcType="VARCHAR"/>
        <result column="execute_time" property="executeTime" jdbcType="TIMESTAMP"/>
        <result column="reject_reason" property="rejectReason" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="from_usd_rate" property="fromUsdRate" jdbcType="DECIMAL"/>
        <result column="to_usd_rate" property="toUsdRate" jdbcType="DECIMAL"/>
        <result column="from_amount_usd" property="fromAmountUsd" jdbcType="DECIMAL"/>
        <result column="to_amount_usd" property="toAmountUsd" jdbcType="DECIMAL"/>
        <result column="from_ac_no" property="fromAcNo" jdbcType="VARCHAR"/>
        <result column="to_ac_no" property="toAcNo" jdbcType="VARCHAR"/>
        <result column="hold_no" property="holdNo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_no, user_id, direction, from_coin, from_amount, to_coin, to_amount, fee_coin, fee_amount, exchange_rate,
        status, first_audit_user, first_audit_time, first_audit_result, first_audit_opinion,
        second_audit_user, second_audit_time, second_audit_result, second_audit_opinion, execute_time,
        reject_reason, create_time, update_time, from_amount_usd, to_amount_usd, from_ac_no, to_ac_no, hold_no
    </sql>

    <insert id="insert" parameterType="com.hisun.lemon.tam.entity.TamExchangeOrderDO">
        insert into tam_exchange_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="direction != null">direction,</if>
            <if test="fromCoin != null">from_coin,</if>
            <if test="fromAmount != null">from_amount,</if>
            <if test="toCoin != null">to_coin,</if>
            <if test="toAmount != null">to_amount,</if>
            <if test="feeCoin != null">fee_coin,</if>
            <if test="feeAmount != null">fee_amount,</if>
            <if test="exchangeRate != null">exchange_rate,</if>
            <if test="status != null">status,</if>
            <if test="firstAuditUser != null">first_audit_user,</if>
            <if test="firstAuditTime != null">first_audit_time,</if>
            <if test="firstAuditResult != null">first_audit_result,</if>
            <if test="firstAuditOpinion != null">first_audit_opinion,</if>
            <if test="secondAuditUser != null">second_audit_user,</if>
            <if test="secondAuditTime != null">second_audit_time,</if>
            <if test="secondAuditResult != null">second_audit_result,</if>
            <if test="secondAuditOpinion != null">second_audit_opinion,</if>
            <if test="executeTime != null">execute_time,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="fromUsdRate != null">from_usd_rate,</if>
            <if test="toUsdRate != null">to_usd_rate,</if>
            <if test="fromAmountUsd != null">from_amount_usd,</if>
            <if test="toAmountUsd != null">to_amount_usd,</if>
            <if test="fromAcNo != null">from_ac_no,</if>
            <if test="toAcNo != null">to_ac_no,</if>
            <if test="holdNo != null">hold_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="direction != null">#{direction},</if>
            <if test="fromCoin != null">#{fromCoin},</if>
            <if test="fromAmount != null">#{fromAmount},</if>
            <if test="toCoin != null">#{toCoin},</if>
            <if test="toAmount != null">#{toAmount},</if>
            <if test="feeCoin != null">#{feeCoin},</if>
            <if test="feeAmount != null">#{feeAmount},</if>
            <if test="exchangeRate != null">#{exchangeRate},</if>
            <if test="status != null">#{status},</if>
            <if test="firstAuditUser != null">#{firstAuditUser},</if>
            <if test="firstAuditTime != null">#{firstAuditTime},</if>
            <if test="firstAuditResult != null">#{firstAuditResult},</if>
            <if test="firstAuditOpinion != null">#{firstAuditOpinion},</if>
            <if test="secondAuditUser != null">#{secondAuditUser},</if>
            <if test="secondAuditTime != null">#{secondAuditTime},</if>
            <if test="secondAuditResult != null">#{secondAuditResult},</if>
            <if test="secondAuditOpinion != null">#{secondAuditOpinion},</if>
            <if test="executeTime != null">#{executeTime},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="fromUsdRate != null">#{fromUsdRate},</if>
            <if test="toUsdRate != null">#{toUsdRate},</if>
            <if test="fromAmountUsd != null">#{fromAmountUsd},</if>
            <if test="toAmountUsd != null">#{toAmountUsd},</if>
            <if test="fromAcNo != null">#{fromAcNo},</if>
            <if test="toAcNo != null">#{toAcNo},</if>
            <if test="holdNo != null">#{holdNo},</if>
        </trim>
    </insert>

    <select id="queryByOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
            <include refid="Base_Column_List"/>
        from tam_exchange_order where order_no = #{orderNo}
    </select>

    <update id="updateOrderSts" parameterType="java.lang.String">
        update tam_exchange_order
        set status = #{status} where order_no = #{orderNo}
    </update>

</mapper>