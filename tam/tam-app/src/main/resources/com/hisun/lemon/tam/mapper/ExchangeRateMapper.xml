<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hisun.lemon.tam.dao.ExchangeRateDao">
    <resultMap id="BaseResultMap" type="com.hisun.lemon.tam.entity.ExchangeRateDO">
        <result column="source_code" property="fromCoin" jdbcType="VARCHAR"/>
        <result column="target_code" property="toCoin" jdbcType="VARCHAR"/>
        <result column="final_rate" property="exchangeRate" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        source_code, target_code, final_rate
    </sql>

    <select id="get" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tfm_exchange_rate where target_code = 'USD'
    </select>

    <select id="queryExchangeRate" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from tfm_exchange_rate where source_code = #{fromCoin} and target_code = #{toCoin}
    </select>

    <select id="getExchangeRateListByFromCoin" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tfm_exchange_rate t1
        WHERE
        t1.target_code = #{fromCoin}
        AND t1.create_time = (
            SELECT MAX(create_time)
            FROM tfm_exchange_rate t2
            WHERE
            t2.source_code = t1.source_code
            AND t2.target_code = t1.target_code
        )
    </select>
</mapper>