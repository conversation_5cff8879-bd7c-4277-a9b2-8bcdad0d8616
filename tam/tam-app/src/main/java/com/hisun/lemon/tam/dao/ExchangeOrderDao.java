package com.hisun.lemon.tam.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.tam.entity.TamExchangeOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ExchangeOrderDao extends BaseDao<TamExchangeOrderDO> {

    TamExchangeOrderDO queryByOrderNo(@Param("orderNo") String orderNo);

    int updateOrderSts(@Param("orderNo") String orderNo, @Param("status") String status);
}
