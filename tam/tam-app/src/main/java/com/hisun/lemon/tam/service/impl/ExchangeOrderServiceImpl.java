package com.hisun.lemon.tam.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.dto.DmAccountDetailRspDTO;
import com.hisun.lemon.acm.dto.DmCcyListRspDTO;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.client.CregisClient;
import com.hisun.lemon.cpi.dto.PaymentPrepareReqDTO;
import com.hisun.lemon.cpi.dto.PaymentReqDTO;
import com.hisun.lemon.csh.client.CshOrderClient;
import com.hisun.lemon.csh.dto.order.ExchangeCoinDTO;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.tam.constants.DmPlatformEnum;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tam.dao.DmPlatFormDao;
import com.hisun.lemon.tam.dao.ExchangeOrderDao;
import com.hisun.lemon.tam.dao.ExchangeRateDao;
import com.hisun.lemon.tam.dto.ExchangeOrderDetailRspDTO;
import com.hisun.lemon.tam.dto.ExchangeRateRspDTO;
import com.hisun.lemon.tam.dto.UserExchangeOrderDTO;
import com.hisun.lemon.tam.dto.UserExchangeRspDTO;
import com.hisun.lemon.tam.entity.DmPlatFormDO;
import com.hisun.lemon.tam.entity.ExchangeRateDO;
import com.hisun.lemon.tam.entity.TamExchangeOrderDO;
import com.hisun.lemon.tam.service.ExchangeOrderService;
import com.hisun.lemon.tfm.client.TfmServerClient;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateReqDTO;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateRspDTO;
import com.hisun.lemon.tfm.dto.TradeRateReqDTO;
import com.hisun.lemon.tfm.dto.TradeRateRspDTO;
import com.hisun.lemon.urm.client.UserAuthenticationClient;
import com.hisun.lemon.urm.dto.CheckPayPwdDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Service("exchangeOrderServiceImpl")
public class ExchangeOrderServiceImpl extends BaseService implements ExchangeOrderService {

    private static final Logger logger = LoggerFactory.getLogger(ExchangeOrderServiceImpl.class);

    @Value("${Cregis.fundFlowCode}")
    private String fundFlowCode;

    // 平台地址转出金额时使用
    @Value("${Cregis.platFundFlowCode}")
    private String platFundFlowCode;

    @Resource
    private ExchangeRateDao exchangeRateDao;

    @Resource
    private ExchangeOrderDao exchangeOrderDao;

    @Resource
    private AccountManagementClient accountManagementClient;

    @Resource
    private CshOrderClient cshOrderClient;

    @Resource
    private CregisClient cregisClient;

    @Resource
    private TfmServerClient tfmServerClient;

    @Resource
    private DmPlatFormDao dmPlatFormDao;

    @Resource
    private UserAuthenticationClient userAuthenticationClient;

    @Override
    public ExchangeRateRspDTO queryExchangeRate(String fromCoin, String toCoin) {
        ExchangeRateDO exchangeRateDO = exchangeRateDao.queryExchangeRate(fromCoin, toCoin);
        ExchangeRateRspDTO result = new ExchangeRateRspDTO();
        BeanUtils.copyProperties(result,exchangeRateDO);
        return result;
    }

    @Override
    public List<ExchangeRateRspDTO> getExchangeRateList() {
        List<ExchangeRateDO> rateDOList = exchangeRateDao.get();
        List<ExchangeRateRspDTO> rspDTOList = new ArrayList<ExchangeRateRspDTO>();
        for(ExchangeRateDO rateDO : rateDOList) {
            ExchangeRateRspDTO rspDTO = new ExchangeRateRspDTO();
            BeanUtils.copyProperties(rspDTO,rateDO);
            rspDTOList.add(rspDTO);
        }
        return rspDTOList;
    }

    @Override
    public UserExchangeRspDTO exchangeOrder(UserExchangeOrderDTO userExchangeOrderDTO) {
        //同币种不走兑换
        if(JudgeUtils.equals(userExchangeOrderDTO.getFromCoin(), userExchangeOrderDTO.getToCoin())) {
            throw new LemonException("TAM30003");
        }
        //手续费计算
        UserExchangeRspDTO feeResult = this.calExchangeFee(userExchangeOrderDTO);
        if(feeResult.getToAmount().compareTo(BigDecimal.ZERO) <= 0 ) {
            throw new LemonException("TAM30007");
        }
        //校验支付密码
        //查询用户支付密码，校验支付密码，错误则抛异常
        CheckPayPwdDTO checkPayPwdDTO =new CheckPayPwdDTO();
        GenericDTO genericDTO = new GenericDTO();
        GenericRspDTO genericRspDTO = null;
//        checkPayPwdDTO.setUserId(userId);
        checkPayPwdDTO.setPayPwd(userExchangeOrderDTO.getPayPwd());
//        checkPayPwdDTO.setPayPwdRandom(withdrawDTO.getPayPassWordRand());
        genericDTO.setBody(checkPayPwdDTO);
        genericRspDTO = userAuthenticationClient.checkPayPwd(genericDTO);
        if(JudgeUtils.isNull(genericRspDTO)){
            LemonException.throwBusinessException("URM30005");
        }
        if(JudgeUtils.equals("URM30005", genericRspDTO.getMsgCd())){
            LemonException.throwBusinessException("URM30005");
        }
//        //查询支付密码错误次数是否超过5次
        if(JudgeUtils.equals("URM30011", genericRspDTO.getMsgCd())){
            LemonException.throwBusinessException("URM30011");
        }
        if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
            if(JudgeUtils.isNotBlank(genericRspDTO.getMsgInfo())) {
                LemonException.throwLemonException(genericRspDTO.getMsgCd(), genericRspDTO.getMsgInfo());
            }else{
                LemonException.throwBusinessException(genericRspDTO.getMsgCd());
            }
        }

        //校验金额
        GenericRspDTO<List<QueryAcBalRspDTO>> acBal = accountManagementClient.queryAcBal(userExchangeOrderDTO.getFromAcNo(),
                userExchangeOrderDTO.getUserId(), "1", GenericDTO.newInstance());
        BigDecimal curBal = acBal.getBody().get(0).getAcCurBal();
        if(userExchangeOrderDTO.getFromAmount().compareTo(curBal) > 0) {
            LemonException.throwLemonException(ACMMessageCode.BAL_NOT_ENOUGH);
        }
        logger.info("校验金额通过");
        //风控检查与冻结金额
        String ymd = DateTimeUtils.getCurrentDateStr();
        ExchangeCoinDTO exchangeCoinDTO = new ExchangeCoinDTO();
        exchangeCoinDTO.setOrderNo(TamConstants.EXCHANGE_ORDER + ymd
                + IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 12));
        exchangeCoinDTO.setPayerId(userExchangeOrderDTO.getFromAcNo());
        exchangeCoinDTO.setPayeeId(userExchangeOrderDTO.getToAcNo());
        exchangeCoinDTO.setAcNo(userExchangeOrderDTO.getFromAcNo());
        exchangeCoinDTO.setOrderAmt(userExchangeOrderDTO.getFromAmount());
        exchangeCoinDTO.setFee(feeResult.getFeeAmount());
        exchangeCoinDTO.setCcy(userExchangeOrderDTO.getFromCoin());
        exchangeCoinDTO.setTxType(TradeType.EXCHANGE.getType());
        exchangeCoinDTO.setBusType(BussinessType.EXCHANGE_COIN.getValue());
        GenericDTO<ExchangeCoinDTO> coinOrder = GenericDTO.newInstance(exchangeCoinDTO);
        String holdNo = cshOrderClient.preCheckExchangeOrder(coinOrder).getBody();
        logger.info("风控检查与冻结金额成功 orderNo:{}",exchangeCoinDTO.getOrderNo());

        //将转出币种换算成USDT再换算成转入币种
        BigDecimal actualToAmt = calActualToAmt(userExchangeOrderDTO.getFromCoin(), userExchangeOrderDTO.getToCoin(),
                userExchangeOrderDTO.getFromAmount().subtract(feeResult.getFeeAmount()));
        //手续费一律收u 要先换算
        BigDecimal usdFee = BigDecimal.ZERO;
        if(JudgeUtils.equals(userExchangeOrderDTO.getFromCoin(), TamConstants.QP_PAY_CCY)) {
            usdFee = feeResult.getFeeAmount();
        } else {
            BigDecimal usdRate = this.queryExchangeRate(userExchangeOrderDTO.getFromCoin(), TamConstants.QP_PAY_CCY).getExchangeRate();
            usdFee = feeResult.getFeeAmount().multiply(usdRate);
        }

        BigDecimal fromUSDRate = BigDecimal.ONE;
        BigDecimal toUSDRate = BigDecimal.ONE;
        if(JudgeUtils.notEquals(userExchangeOrderDTO.getFromCoin(), TamConstants.QP_PAY_CCY)) {
            fromUSDRate = this.queryExchangeRate(userExchangeOrderDTO.getFromCoin(), TamConstants.QP_PAY_CCY).getExchangeRate();
        }
        if(JudgeUtils.notEquals(userExchangeOrderDTO.getToCoin(), TamConstants.QP_PAY_CCY)) {
            toUSDRate = this.queryExchangeRate(userExchangeOrderDTO.getToCoin(), TamConstants.QP_PAY_CCY).getExchangeRate();
        }
        BigDecimal exchangeRate = this.queryExchangeRate(userExchangeOrderDTO.getFromCoin(), userExchangeOrderDTO.getToCoin()).getExchangeRate();
//        BigDecimal fromAmtUSD = userExchangeOrderDTO.getFromAmount().multiply(fromUSDRate);
//        BigDecimal toAmtUSD = userExchangeOrderDTO.getActualFromAmount().multiply(toUSDRate);

        //组装兑换订单信息
        TamExchangeOrderDO order = new TamExchangeOrderDO();
        BigDecimal toAmount = feeResult.getToAmount();
        BigDecimal fee = feeResult.getFeeAmount();
        order.setId(IdGenUtils.generateId(TamConstants.ORD_GEN_PRE, 14));
        order.setOrderNo(exchangeCoinDTO.getOrderNo());
        order.setUserId(userExchangeOrderDTO.getUserId());
        order.setDirection(defineExchangeType(userExchangeOrderDTO));
        order.setFromCoin(userExchangeOrderDTO.getFromCoin());
        order.setFromAmount(userExchangeOrderDTO.getFromAmount());
        order.setToCoin(userExchangeOrderDTO.getToCoin());
        order.setToAmount(toAmount);
        order.setFeeCoin(userExchangeOrderDTO.getFromCoin());
        order.setFeeAmount(fee);
        order.setExchangeRate(exchangeRate);
        order.setStatus(TamConstants.PENDING);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        order.setFromAcNo(userExchangeOrderDTO.getFromAcNo());
        order.setToAcNo(userExchangeOrderDTO.getToAcNo());
        order.setFromUsdRate(fromUSDRate);
        order.setToUsdRate(toUSDRate);
        order.setFromAmountUsd(usdFee);
        order.setToAmountUsd(actualToAmt);
        //order.setHoldNo(holdNo);
        exchangeOrderDao.insert(order);
        //返回响应
        UserExchangeRspDTO response = new UserExchangeRspDTO();
        BeanUtils.copyProperties(response, order);
        return response;
    }

    @Override
    public void executeExchangeOrder(UserExchangeOrderDTO userExchangeOrderDTO) {
        TamExchangeOrderDO order = exchangeOrderDao.queryByOrderNo(userExchangeOrderDTO.getOrderNo());
        List<String> includes = new ArrayList<>();
        DmPlatFormDO platFormDO = null;
        //订单不存在
        if(JudgeUtils.isNull(order)) {
            throw new LemonException("TFM30004");
        }
        if(JudgeUtils.equals(order.getStatus(), TamConstants.REJECTED)) {
            //审核不通过处理
            logger.info("订单审核不通过！");
            GenericDTO<ExchangeCoinDTO> rejectOrder = this.genRejectCoinReq(order);
            cshOrderClient.completeExchangeOrder(rejectOrder);
            return;
        }
        if(JudgeUtils.equals(order.getDirection(), TamConstants.DM_TO_FM)) {
            //当前数币只有USDT
            logger.info("数币转法币 orderNo:{}",order.getOrderNo());
            //获取转出账户数币链上信息
            DmAccountDetailRspDTO userDmAcc = accountManagementClient.queryDmAccountDetail(order.getFromAcNo()).getBody();
            //网络类型转换映射
            Map<String, String> networkMapping = new HashMap<>();
            networkMapping.put(ACMConstants.NETWORK_TRON, "tron-nile");
            networkMapping.put(ACMConstants.NETWORK_ETH, "ethereum-sepolia");
            userDmAcc.setNetwork(networkMapping.getOrDefault(userDmAcc.getNetwork(), userDmAcc.getNetwork()));
            //组装cregis请求体 调用cregis转账从用户转到平台
            PaymentReqDTO cregisReq = new PaymentReqDTO();
            PaymentPrepareReqDTO.PayTo[] payToList = new PaymentPrepareReqDTO.PayTo[2];
            payToList[0] = new PaymentPrepareReqDTO.PayTo();
            payToList[1] = new PaymentPrepareReqDTO.PayTo();
            cregisReq.setCoinId(order.getFromCoin());
            cregisReq.setNetwork(userDmAcc.getNetwork());
            for(DmPlatformEnum dmPlatformEnum : DmPlatformEnum.values()) {
                if(JudgeUtils.equals(dmPlatformEnum.getNetwork(), userDmAcc.getNetwork())
                        && JudgeUtils.equals(dmPlatformEnum.getCcy(), order.getFromCoin())) {
                    if(JudgeUtils.equals(dmPlatformEnum.getType(), TamConstants.P_TYPE)) {
                        platFormDO = dmPlatFormDao.getByPlatformNo(dmPlatformEnum.getPlatformId());
                        payToList[0].setTo(platFormDO.getAddress());
                        payToList[0].setReadableAmount((order.getFromAmount().subtract(order.getFeeAmount())));
                    } else if(JudgeUtils.equals(dmPlatformEnum.getType(), TamConstants.F_TYPE)) {
                        platFormDO = dmPlatFormDao.getByPlatformNo(dmPlatformEnum.getPlatformId());
                        payToList[1].setTo(platFormDO.getAddress());
                        payToList[1].setReadableAmount(order.getFeeAmount());
                    }
                }
            }
            cregisReq.setPayToList(payToList);
            cregisReq.setLang(TamConstants.lang);
            cregisReq.setFundFlowCode(fundFlowCode);
            cregisReq.setOrderId(order.getOrderNo());
            includes.add(userDmAcc.getAddress());
            cregisReq.setIncludes(includes);
            if(JudgeUtils.notEquals("200",cregisClient.transferAll(cregisReq).getCode())) {
                logger.info("调用cregis失败 orderNo:{}",order.getOrderNo());
                exchangeOrderDao.updateOrderSts(order.getOrderNo(), TamConstants.FAILED);
                GenericDTO<ExchangeCoinDTO> rejectOrder = this.genRejectCoinReq(order);
                cshOrderClient.completeExchangeOrder(rejectOrder);
                throw new LemonException("CPI80001");
            }
            logger.info("调用cregi成功 orderNo:{}",order.getOrderNo());
            //将转出币种换算成USDT再换算成转入币种
            //平台内转给用户（账务处理）
            //借:其他应付款-暂收-收银台 100 贷：其他应付款-支付账户-现金账户 100
        } else if(JudgeUtils.equals(order.getDirection(), TamConstants.FM_TO_DM)) {
            logger.info("法币转数币 orderNo:{}",order.getOrderNo());
            //将转出币种换算成USD再换算成转入币种
            BigDecimal actualToAmt = calActualToAmt(order.getFromCoin(), order.getToCoin(),
                    order.getFromAmount().subtract(order.getFeeAmount()));
            //调用cregis转账从平台转到用户
            DmAccountDetailRspDTO userDmAcc = accountManagementClient.queryDmAccountDetail(order.getToAcNo()).getBody();
            //网络类型转换映射
            Map<String, String> networkMapping = new HashMap<>();
            networkMapping.put(ACMConstants.NETWORK_TRON, "tron-nile");
            networkMapping.put(ACMConstants.NETWORK_ETH, "ethereum-sepolia");
            userDmAcc.setNetwork(networkMapping.getOrDefault(userDmAcc.getNetwork(), userDmAcc.getNetwork()));
            PaymentReqDTO cregisReq = new PaymentReqDTO();
            PaymentPrepareReqDTO.PayTo[] payToList = new PaymentPrepareReqDTO.PayTo[1];
            payToList[0] = new PaymentPrepareReqDTO.PayTo();
            cregisReq.setCoinId(order.getToCoin());
            cregisReq.setNetwork(userDmAcc.getNetwork());
            for(DmPlatformEnum dmPlatformEnum : DmPlatformEnum.values()) {
                if(JudgeUtils.equals(dmPlatformEnum.getNetwork(), userDmAcc.getNetwork())
                        && JudgeUtils.equals(dmPlatformEnum.getType(), TamConstants.P_TYPE)
                        && JudgeUtils.equals(dmPlatformEnum.getCcy(), order.getToCoin())) {
                    platFormDO = dmPlatFormDao.getByPlatformNo(dmPlatformEnum.getPlatformId());
                    payToList[0].setReadableAmount(actualToAmt);
                    payToList[0].setTo(userDmAcc.getAddress());
                }
            }
            cregisReq.setPayToList(payToList);
            cregisReq.setLang(TamConstants.lang);
            cregisReq.setFundFlowCode(platFundFlowCode);
            cregisReq.setOrderId(order.getOrderNo());
            includes.add(platFormDO.getAddress());
            cregisReq.setIncludes(includes);
            if(JudgeUtils.notEquals("200",cregisClient.transferAll(cregisReq).getCode())) {
                logger.info("调用cregis失败 orderNo:{}",order.getOrderNo());
                exchangeOrderDao.updateOrderSts(order.getOrderNo(), TamConstants.FAILED);
                GenericDTO<ExchangeCoinDTO> rejectOrder = this.genRejectCoinReq(order);
                cshOrderClient.completeExchangeOrder(rejectOrder);
                throw new LemonException("CPI80001");
            }
            logger.info("调用cregi成功 orderNo:{}",order.getOrderNo());
            //账务处理 生成科目
            //借：其他应付款-支付账户-现金账户 102 贷：其他应付款-暂收-收银台 100 贷：手续费收入-支付账户-转账 2
            //手续费一律收u 要先换算
        }
    }

    @Override
    public ExchangeOrderDetailRspDTO queryExchangeOrderDetail(String orderNo) {
        TamExchangeOrderDO order = exchangeOrderDao.queryByOrderNo(orderNo);
        ExchangeOrderDetailRspDTO result = new ExchangeOrderDetailRspDTO();
        BeanUtils.copyProperties(result,order);
        return result;
    }

    @Override
    public UserExchangeRspDTO calExchangeFee(UserExchangeOrderDTO req) {
        BigDecimal rate = BigDecimal.ZERO;
        BigDecimal fromAmt = req.getFromAmount();
        BigDecimal toAmt = req.getToAmount();
        BigDecimal fee = BigDecimal.ZERO;
        BigDecimal actualFromAmt = BigDecimal.ZERO;
        BigDecimal actualToAmt = BigDecimal.ZERO;
        TradeFeeCaculateReqDTO feeReq = new TradeFeeCaculateReqDTO();
        TradeRateReqDTO feeRateReq = new TradeRateReqDTO();
        UserExchangeRspDTO rspDTO = new UserExchangeRspDTO();
        //卖出金额或买入金额应该只传其中一个 如果两个都传 则默认按卖出金额来计算
        if(JudgeUtils.isNotNull(fromAmt)) {
            //只输入卖出金额
            //查询手续费 不传币种 默认USD
            //因为费率表业务类型是主键且不能重复 所以不管币种都只用同一条费率规则
            feeReq.setBusType(BussinessType.EXCHANGE_COIN.getValue());
            feeReq.setTradeAmt(fromAmt);
            TradeFeeCaculateRspDTO calRsp = tfmServerClient.tradeFeeCaculate(GenericDTO.newInstance(feeReq)).getBody();
            if(JudgeUtils.equals(req.getFromCoin(), TamConstants.QP_PAY_CCY) || JudgeUtils.equals(calRsp.getCalculateType(), TamConstants.FEE_CALCULATE_PERCENT)) {
                //如果卖出金额就是USD 或 类型是百分比 手续费直接取返回的金额 无需换算
                fee = calRsp.getTradeFee();
            } else if(JudgeUtils.equals(calRsp.getCalculateType(), TamConstants.FEE_CALCULATE_FIXED)) {
                //如果卖出金额不是USD 手续费类型为固定值 则将返回的金额换算成卖出币种作为手续费
                rate = this.queryExchangeRate(TamConstants.QP_PAY_CCY, req.getFromCoin()).getExchangeRate();
                fee = calRsp.getTradeFee().multiply(rate);
            }
            actualFromAmt = fromAmt.subtract(fee);
            actualToAmt = this.calActualToAmt(req.getFromCoin(), req.getToCoin(), actualFromAmt);
            rspDTO.setFeeAmount(fee);
            rspDTO.setActualFromAmount(actualFromAmt);
            rspDTO.setToAmount(actualToAmt);
            if(fromAmt.compareTo(fee) < 0) {
                throw new LemonException("TAM30004");
            }
            return rspDTO;
        }
        if(JudgeUtils.isNotNull(toAmt)) {
            //只输入买入金额 反推出实际卖出金额
            feeRateReq.setBusType(BussinessType.EXCHANGE_COIN.getValue());
            //实际卖出金额
            actualFromAmt = this.calActualToAmt(req.getToCoin(), req.getFromCoin(), toAmt);
            //手续费费率
            feeRateReq.setBusType(BussinessType.EXCHANGE_COIN.getValue());
            //feeRateReq.setCcy(req.getFromCoin());
            TradeRateRspDTO rateRsp = tfmServerClient.tradeRate(GenericDTO.newInstance(feeRateReq)).getBody();
            if(JudgeUtils.equals(rateRsp.getCaculateType(), TamConstants.FEE_CALCULATE_FIXED)) {
                //如果是固定费率 换算成卖出币种 实际卖出金额+手续费=卖出金额
                rate = this.queryExchangeRate(TamConstants.QP_PAY_CCY, req.getFromCoin()).getExchangeRate();
                fee = rateRsp.getFixFee().multiply(rate);
                fromAmt = actualFromAmt.add(fee);
            } else {
                //如果是百分比费率 实际卖出金额/(1-费率)=卖出金额
                fromAmt = actualFromAmt.divide(BigDecimal.ONE.subtract(rateRsp.getRate()));
                fee = fromAmt.subtract(actualFromAmt);
            }
            rspDTO.setFromAmount(fromAmt);
            rspDTO.setActualFromAmount(actualFromAmt);
            rspDTO.setFeeAmount(fee);
            if(fromAmt.compareTo(fee) < 0) {
                throw new LemonException("TAM30004");
            }
            return rspDTO;
        }
        return rspDTO;
    }

    @Override
    public void updateOrderSts(String orderNo, String status) {
        exchangeOrderDao.updateOrderSts(orderNo, status);
    }

    @Override
    public List<ExchangeRateRspDTO> getExchangeRateListByFromCoin(String fromCoin) {
        List<ExchangeRateDO> list = exchangeRateDao.getExchangeRateListByFromCoin(fromCoin);
        List<ExchangeRateRspDTO> rspDTOList = new ArrayList<ExchangeRateRspDTO>();
        for(ExchangeRateDO rateDO : list) {
            ExchangeRateRspDTO rspDTO = new ExchangeRateRspDTO();
            BeanUtils.copyProperties(rspDTO,rateDO);
            rspDTOList.add(rspDTO);
        }
        return rspDTOList;
    }

    private String defineExchangeType(UserExchangeOrderDTO userExchangeOrderDTO) {
        String flag = null;
        GenericDTO<NoBody> req = GenericDTO.newInstance();
        List<DmCcyListRspDTO> dmCcyList = accountManagementClient.getCcyList(req).getBody();
        for(DmCcyListRspDTO dmCcy : dmCcyList) {
            if(JudgeUtils.equals(userExchangeOrderDTO.getFromCoin(), dmCcy.getCoinId())) {
                flag = TamConstants.DM_TO_FM;
            } else if(JudgeUtils.equals(userExchangeOrderDTO.getToCoin(), dmCcy.getCoinId())) {
                flag = TamConstants.FM_TO_DM;
            }
        }
        return flag;
    }

    private BigDecimal calActualToAmt(String fromCoin, String toCoin, BigDecimal actualFromAmt) {
        BigDecimal rate = BigDecimal.ZERO;
        BigDecimal actualToAmt = BigDecimal.ZERO;
        //转出币种先换算成USD
        if(JudgeUtils.notEquals(fromCoin, TamConstants.QP_PAY_CCY)) {
            rate = this.queryExchangeRate(fromCoin, TamConstants.QP_PAY_CCY).getExchangeRate();
            actualToAmt = actualFromAmt.multiply(rate);
        } else {
            actualToAmt = actualToAmt.add(actualFromAmt);
        }
        //USD再换算成转入币种
        if(JudgeUtils.notEquals(toCoin, TamConstants.QP_PAY_CCY)) {
            rate = this.queryExchangeRate(TamConstants.QP_PAY_CCY, toCoin).getExchangeRate();
            actualToAmt = actualToAmt.multiply(rate);
        }
        return actualToAmt;
    }

    private GenericDTO<ExchangeCoinDTO> genRejectCoinReq(TamExchangeOrderDO order) {
        ExchangeCoinDTO rejectDTO = new ExchangeCoinDTO();
        rejectDTO.setOrderNo(order.getOrderNo());
        rejectDTO.setTxType(TradeType.EXCHANGE.getType());
        rejectDTO.setBusType(BussinessType.EXCHANGE_COIN.getValue());
        rejectDTO.setCcy(order.getFromCoin());
        rejectDTO.setOrderAmt(order.getFromAmount());
        rejectDTO.setAcNo(order.getFromAcNo());
        //rejectDTO.setHoldNo(order.getHoldNo());
        rejectDTO.setOrderSts(TamConstants.REJECTED);
        GenericDTO<ExchangeCoinDTO> rejectOrder = GenericDTO.newInstance(rejectDTO);
        return rejectOrder;
    }
}
