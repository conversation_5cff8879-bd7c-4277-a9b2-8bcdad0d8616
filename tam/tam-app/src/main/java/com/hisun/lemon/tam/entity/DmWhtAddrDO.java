package com.hisun.lemon.tam.entity;

import com.hisun.lemon.framework.data.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 数币白名单地址实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 18:20
 */
@ApiModel(value = "DmWhtAddrDO",description = "数币白名单地址实体类")
public class DmWhtAddrDO extends BaseDO {

    @ApiModelProperty(name = "whtAddrId", value = "白名单地址ID")
    private String whtAddrId;

    @ApiModelProperty(name = "address", value = "白名单地址")
    private String address;

    @ApiModelProperty(name = "network", value = "所属区块链网络")
    private String network;

    @ApiModelProperty(name = "effFlg", value = "有效标志（Y=有效，N=无效）")
    private String effFlg;

    @ApiModelProperty(name = "beginDt", value = "生效开始时间")
    private LocalDateTime beginDt;

    @ApiModelProperty(name = "endDt", value = "失效时间")
    private LocalDateTime endDt;

    @ApiModelProperty(name = "whtReason", value = "白名单原因")
    private String whtReason;

    @ApiModelProperty(name = "oprId", value = "操作员ID")
    private String oprId;

    public String getWhtAddrId() {
        return whtAddrId;
    }

    public void setWhtAddrId(String whtAddrId) {
        this.whtAddrId = whtAddrId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public LocalDateTime getBeginDt() {
        return beginDt;
    }

    public void setBeginDt(LocalDateTime beginDt) {
        this.beginDt = beginDt;
    }

    public LocalDateTime getEndDt() {
        return endDt;
    }

    public void setEndDt(LocalDateTime endDt) {
        this.endDt = endDt;
    }

    public String getWhtReason() {
        return whtReason;
    }

    public void setWhtReason(String whtReason) {
        this.whtReason = whtReason;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }
}
