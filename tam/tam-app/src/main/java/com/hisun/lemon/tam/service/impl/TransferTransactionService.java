package com.hisun.lemon.tam.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.tam.dao.ITransferOrderDao;
import com.hisun.lemon.tam.entity.TransferOrderDO;

@Transactional
@Service
public class TransferTransactionService {
	@Resource
	private ITransferOrderDao transferDao;

	/**
	 * 查询历史转账记录
	 */
	@Transactional(readOnly = true)
	public List<TransferOrderDO> historyTransferOrder(TransferOrderDO transderDo) {
		List<TransferOrderDO> list = this.transferDao.find(transderDo);
		return list;
	}

	/**
	 * 转账下单
	 */
	public void createOrder(TransferOrderDO transferDO) {
		int result = this.transferDao.insert(transferDO);
		if (result != 1) {
			throw new LemonException("TAM20003");
		}

	}

	/**
	 * 查询订单信息
	 * 
	 * @param orderNo
	 * @return
	 */
	@Transactional(readOnly = true)
	public TransferOrderDO tansferInfo(String orderNo) {
		return this.transferDao.get(orderNo);
	}


	/**
	 * 根据外部订单号查询订单信息
	 *
	 * @param extOrderNo
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TransferOrderDO> queryByExtOrderNo(String extOrderNo) {
		return this.transferDao.queryListByExtOrderNo(extOrderNo);
	}

	public void updateTransferOrder(TransferOrderDO transferDO) {

		int result = this.transferDao.update(transferDO);
		if (result != 1) {
			throw new LemonException("TAM20003");
		}
	}
}
