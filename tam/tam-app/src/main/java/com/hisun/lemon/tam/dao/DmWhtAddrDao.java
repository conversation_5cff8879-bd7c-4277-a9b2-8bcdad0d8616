package com.hisun.lemon.tam.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.tam.entity.DmWhtAddrDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数币白名单地址 DAO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 18:22
 */
@Mapper
public interface DmWhtAddrDao extends BaseDao<DmWhtAddrDO> {
    /**
     * 检查地址是否在白名单中
     * @param address 白名单地址
     * @return 是否存在
     */
//    boolean existsInWhitelist(@Param("address") String address);
}
