package com.hisun.lemon.tam.service.chk;

import com.hisun.lemon.tam.constants.TamConstants;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Transactional
@Service
public class CshChkFileServiceImpl extends AbstractChkFileService {

    public CshChkFileServiceImpl() {
        super();
        this.appCnl="CSH";
        this.chkOrderStatus=new String[]{
                TamConstants.ORD_STS_S,
                TamConstants.ORD_STS_P
        };
        this.lockName="TAM_CSH_CHK_FILE_LOCK";
    }
}
