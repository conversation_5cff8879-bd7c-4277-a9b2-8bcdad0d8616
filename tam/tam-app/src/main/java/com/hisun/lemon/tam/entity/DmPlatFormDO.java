package com.hisun.lemon.tam.entity;

import com.hisun.lemon.framework.data.BaseDO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class DmPlatFormDO extends BaseDO {

    @ApiModelProperty(name = "id", value = "自增主键")
    private Long id;

    @ApiModelProperty(name = "vaultCode", value = "所属金库编码（关联金库）")
    private String vaultCode;

    @ApiModelProperty(name = "groupCode", value = "所属账户组编码")
    private String groupCode;

    @ApiModelProperty(name = "accountId", value = "所属账户ID（关联账户管理中的账户）")
    private Long accountId;

    @ApiModelProperty(name = "address", value = "链上地址")
    private String address;

    @ApiModelProperty(name = "network", value = "所属区块链网络")
    private String network;

    @ApiModelProperty(name = "status", value = "地址状态（ENABLED:已启用, DISABLED:未启用, FROZEN:冻结）")
    private String status;

    @ApiModelProperty(name = "platformAcNo", value = "对应平台账号")
    private String platformAcNo;

    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    @ApiModelProperty(name = "createTime", value = "创建时间（精确到微秒）")
    private Date createTime;

    @ApiModelProperty(name = "updateTime", value = "更新时间（精确到微秒）")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPlatformAcNo() {
        return platformAcNo;
    }

    public void setPlatformAcNo(String platformAcNo) {
        this.platformAcNo = platformAcNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
