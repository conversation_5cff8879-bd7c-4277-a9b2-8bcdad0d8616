package com.hisun.lemon.tam.entity;

import java.math.BigDecimal;

public class ExchangeRateDO {

    /**
     * 源币种代码
     */
    private String fromCoin;

    /**
     * 目标币种代码
     */
    private String toCoin;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;

    public String getFromCoin() {
        return fromCoin;
    }

    public void setFromCoin(String fromCoin) {
        this.fromCoin = fromCoin;
    }

    public String getToCoin() {
        return toCoin;
    }

    public void setToCoin(String toCoin) {
        this.toCoin = toCoin;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }
}
