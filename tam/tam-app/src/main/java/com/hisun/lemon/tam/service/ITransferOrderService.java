package com.hisun.lemon.tam.service;

import com.hisun.lemon.csh.dto.cashier.CashierViewDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.tam.dao.GetDmSkAddrTypeReqDTO;
import com.hisun.lemon.tam.dto.*;
import com.hisun.lemon.tam.dto.QueryResultTransferOrderDTO.queryOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月7日
 * @time 下午2:13:58
 *
 */
public interface ITransferOrderService {
	/**
	 * 查询历史转账记录
	 * 
	 * @param queryDTO
	 * @return
	 */
	public List<queryOrder> historyTransferOrder(QueryTransferOrderDTO queryDTO);
	/**
	 * 面对面转账申请
	 * @param faceTransferOrderDTO
	 */
    public GenericRspDTO<CashierViewDTO> createFaceOrder(GenericDTO<FaceTransferOrderDTO> faceTransferOrderDTO);

	/**
	 * 转账到账户订单申请
	 * @param userTransferOrderDTO
	 * @return
	 */
	public GenericRspDTO<CashierViewDTO> createUserOrder(GenericDTO<UserTransferOrderDTO> userTransferOrderDTO);
	
	/**
	 * 转账到账户订单申请(对外接口)
	 * @param userOutTransferOrderDTO
	 * @return
	 */
	public GenericRspDTO<UserTransferRspDTO> createUserOutOrder(GenericDTO<UserOutTransferOrderDTO> userOutTransferOrderDTO);


	/**
	 * 转账到账户订单申请(对外接口)
	 * @param userOutTransferOrderDTO
	 * @return
	 */
	public GenericRspDTO<UserTransferRspDTO> createUserOutOrderNew(GenericDTO<NewUserOutTransferOrderDTO> userOutTransferOrderDTO);

	/**
	 * 转账到银行卡订单申请
	 * @param cardTransferOrderDTO
	 * @return
	 */
	public GenericRspDTO<CashierViewDTO> createCardOrder(GenericDTO<CardTransferOrderDTO> cardTransferOrderDTO);
	
	/**
	 * 转账账户结果通知
	 * @param cardTransferOrderDTO
	 * @return
	 */
	public GenericRspDTO<NoBody> userTransferOrderResult(GenericDTO<ResultTransferOrderDTO> cardTransferOrderDTO);
	
	/**
	 * 银行结果通知
	 * @param resultTransferOrderDTO
	 * @return
	 */
	public GenericRspDTO<NoBody> transferOrderResult(GenericDTO<ResultTransferOrderDTO> resultTransferOrderDTO);
	
	/**
	 * 查询账户信息
	 * @param mblNo
	 * @return
	 */
	public UserInfoRspDTO userInfo(String mblNo);
	
	/**
	 * 补单  0601  与收银台对账
	 * @param killOrderRspDTO
	 * @return
	 */
	public void killOrder(GenericDTO<KillOrderRspDTO> killOrderRspDTO);
	
	
	/**
	 * 补单  0602  与CPO对账
	 * @param killOrderRspDTO
	 * @return
	 */
	public void killCpoOrder(GenericDTO<KillOrderRspDTO> killOrderRspDTO);

	/**
	 * 商户转账到用户账户
	 * @param genericMerchantTransferOrderDTO
	 * @return
	 */
	public GenericRspDTO<MerchantTransferRspDTO> createMerchantOutOrder(GenericDTO<MerchantTransferOrderDTO> genericMerchantTransferOrderDTO);

	/**
	 * 转账到ACLEDA银行卡订单申请
	 * @param cardTransferOrderDTO
	 * @return
	 */
	public GenericRspDTO<AcledaCardTransferOrderRspDTO>  createAcledaCardOrder(GenericDTO<AcledaCardTransferOrderReqDTO> cardTransferOrderDTO);

	/**
	 * ACLEDA银行转账成功通知
	 * @param cardTransferNotifyDTO
	 * @return
	 */
	public GenericRspDTO<NoBody> acledaCardSuccessNotify(GenericDTO<AcledaCardTransferNotifyDTO> cardTransferNotifyDTO);

	/**
	 * 数币转账订单申请
	 * @param dmOutTransferOrderDTO
	 * @return
	 */
	GenericRspDTO<DmTransferRspDTO> createDmOutOrder(GenericDTO<DmOutTransferOrderDTO> dmOutTransferOrderDTO);

	/**
	 * 获取数币收款地址类型
	 * @param getDmSkAddrTypeReqDTO
	 * @return
	 */
	GenericRspDTO<String> getDmSkAddrType(GenericDTO<GetDmSkAddrTypeReqDTO> getDmSkAddrTypeReqDTO);

	/**
	 * 获取数币平台地址
	 * @param getDmPlatAddrReqDTO
	 * @return
	 */
	GenericRspDTO<GetDmPlatAddrRspDTO> getDmPlatAddr(GenericDTO<GetDmPlatAddrReqDTO> getDmPlatAddrReqDTO);

	/**
	 * 根据订单号查询转账订单
	 * @param orderNo
	 * @return
	 */
	TransferOrderRspDTO getTransferOrder(String orderNo);
}