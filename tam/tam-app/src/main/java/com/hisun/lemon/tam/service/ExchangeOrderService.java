package com.hisun.lemon.tam.service;

import com.hisun.lemon.tam.dto.ExchangeOrderDetailRspDTO;
import com.hisun.lemon.tam.dto.ExchangeRateRspDTO;
import com.hisun.lemon.tam.dto.UserExchangeOrderDTO;
import com.hisun.lemon.tam.dto.UserExchangeRspDTO;

import java.util.List;

public interface ExchangeOrderService {

    /**
     * 获取汇率
     */
    ExchangeRateRspDTO queryExchangeRate(String fromCoin, String toCoin);

    /**
     * 获取汇率列表
     */
    List<ExchangeRateRspDTO> getExchangeRateList();

    /**
     * 数法币兑换
     */
    UserExchangeRspDTO exchangeOrder(UserExchangeOrderDTO userExchangeOrderDTO);

    /**
     * 执行数法币兑换订单
     */
    void executeExchangeOrder(UserExchangeOrderDTO userExchangeOrderDTO);

    /**
     * 查询兑换订单详情
     */
    ExchangeOrderDetailRspDTO queryExchangeOrderDetail(String orderNo);

    /**
     * 计算兑换订单手续费
     */
    UserExchangeRspDTO calExchangeFee(UserExchangeOrderDTO userExchangeOrderDTO);

    /**
     * 更新兑换订单表状态
     */
    void updateOrderSts(String orderNo, String status);

    /**
     * 根据币种获取汇率列表
     * @param fromCoin
     * @return
     */
    List<ExchangeRateRspDTO> getExchangeRateListByFromCoin(String fromCoin);
}
