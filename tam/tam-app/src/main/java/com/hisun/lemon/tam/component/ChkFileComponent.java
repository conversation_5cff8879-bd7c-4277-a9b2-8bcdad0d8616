package com.hisun.lemon.tam.component;


import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.lemon.jcommon.file.FileUtils;
import com.hisun.lemon.tam.dao.ITransferOrderDao;
import com.hisun.lemon.tam.entity.TransferOrderDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对账文件组件
 * 
 * <AUTHOR>
 *
 */
@Component
public class ChkFileComponent {
	private static final Logger logger = LoggerFactory.getLogger(ChkFileComponent.class);
	final String yyyyMMdd="yyyyMMdd";

	final int defaultSftpTimeout=2000;


	@Resource
	private ITransferOrderDao dao;

	public List<TransferOrderDO> queryDatas(LocalDate date,String[] chkOrderStatus){
		Map queryDo=new HashMap();
		queryDo.put("acTm",date);
		queryDo.put("statusList",chkOrderStatus);

		return dao.queryList(queryDo);
	}
	
	public List<TransferOrderDO> queryByType(LocalDate date,String[] chkOrderStatus){
		Map queryDo=new HashMap();
		queryDo.put("acTm",date);
		queryDo.put("statusList",chkOrderStatus);

		return dao.queryListByType(queryDo);
	}

	/**
	 * 获取对账数据日期
	 * @return
	 */
	public LocalDate getChkDate(){
		LocalDate today= DateTimeUtils.getCurrentLocalDate();
		return today.minusDays(1);
	}

	/**
	 * 获取对账文件名
	 * @param appCnl
	 * @param chkDate
	 * @return
	 */
	public String getChkFileName(String appCnl,LocalDate chkDate){
		return LemonUtils.getApplicationName()+"_"+appCnl+"_"+DateTimeUtils.formatLocalDate(chkDate,yyyyMMdd)+".ck";
	}

	/**
	 * 数据写入对账文件
	 * @param datas
	 * @param fileName
	 */
	public void writeToFile(String appCnl,List<TransferOrderDO> datas,String fileName){
		StringBuilder contextBuilder=new StringBuilder();
		for(TransferOrderDO rec:datas){
			contextBuilder.append(recToLine(rec));
		}

		//写入文件
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write(contextBuilder.toString(), localPath + fileName);
		} catch (Exception e) {
			LemonException.throwBusinessException("TAM20014");
		}

	}
	
	
	public void writeToFileByType(String appCnl,List<TransferOrderDO> datas,String fileName){
		StringBuilder contextBuilder=new StringBuilder();
		for(TransferOrderDO rec:datas){
			contextBuilder.append(recToLineByType(rec));
		}

		//写入文件
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write(contextBuilder.toString(), localPath + fileName);
		} catch (Exception e) {
			LemonException.throwBusinessException("TAM20014");
		}

	}
	

	/**
	 * 文件上传SFTP服务器
	 * @param chkFileName
	 * @param flagName
	 */
	public void upload(String appCnl,String chkFileName, String flagName){
		String localPath=getLocalPath(appCnl);
		String[] uploadFileNames=new String[]{localPath+chkFileName,localPath+flagName};

		String remoteIp=LemonUtils.getProperty("tam.sftp.ip");
		int remotePort=Integer.valueOf(LemonUtils.getProperty("tam.sftp.port"));
		String timeoutStr=LemonUtils.getProperty("tam.sftp.connectTimeout");
		int connectTimeout=defaultSftpTimeout;
		if(StringUtils.isNotEmpty(timeoutStr)){
			connectTimeout=Integer.valueOf(timeoutStr);
		}

		String remotePath=LemonUtils.getProperty("tam.chk.remotePath");

		String name=LemonUtils.getProperty("tam.sftp.name");
		String pwd=LemonUtils.getProperty("tam.sftp.password");

		try {
			FileSftpUtils.upload(uploadFileNames,remoteIp,remotePort,connectTimeout,remotePath,name,pwd);
		} catch (Exception e) {
			logger.error(chkFileName+"上传SFTP文件服务器失败",e);
			LemonException.throwBusinessException("TAM20015");
		}
	}


	public boolean isStart(String appCnl,String flagName){
		return new File(getLocalPath(appCnl)+flagName).exists();
	}

	public void createFlagFile(String appCnl,String flagName){
		try {
			String localPath=getLocalPath(appCnl);
			FileUtils.write("flag", localPath+flagName);
		} catch (Exception e) {
			LemonException.throwBusinessException("TAM20016");
		}
	}

	/**
	 * 获取本地存放对账文件的目录，没有则创建
	 * @param appCnl
	 * @return
	 */
	private String getLocalPath(String appCnl){
		String localPath=LemonUtils.getProperty("tam.chk.localPath")+appCnl+"/";
		File localPathFile=new File(localPath);
		if(localPathFile.exists()){
			if(localPathFile.isDirectory()){
				return localPath;
			}else{
				logger.error(localPath+"已经存在，但不是目录，任务退出");
				LemonException.throwBusinessException("TAM20017");
			}
		}
		boolean success=localPathFile.mkdirs();
		if(!success){
			logger.error(localPath+"目录创建失败，任务退出");
			LemonException.throwBusinessException("TAM20017");

		}
		return localPath;
	}

	private StringBuilder recToLine(TransferOrderDO rec){
		StringBuilder lineBuilder=new StringBuilder();
		lineBuilder.append(rec.getOrderNo());
		lineBuilder.append("|");
		lineBuilder.append(rec.getOrderSts());
		lineBuilder.append("|");
		lineBuilder.append(rec.getAmount());
		lineBuilder.append("|");
		lineBuilder.append(rec.getBusOrderNo());
		lineBuilder.append("\n");

		return lineBuilder;
	}
	
	private StringBuilder recToLineByType(TransferOrderDO rec){
		StringBuilder lineBuilder=new StringBuilder();
		lineBuilder.append(rec.getOrderNo());
		lineBuilder.append("|");
		lineBuilder.append(rec.getOrderSts());
		lineBuilder.append("|");
		lineBuilder.append(rec.getAmount());
		lineBuilder.append("\n");

		return lineBuilder;
	}
}
