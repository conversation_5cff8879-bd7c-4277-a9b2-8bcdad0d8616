package com.hisun.lemon.tam.dao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 获取数币收款地址类型请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 17:40
 */
@ApiModel(value = "GetDmSkAddrTypeReqDTO",description = "获取数币收款地址类型请求参数")
public class GetDmSkAddrTypeReqDTO {

    @ApiModelProperty(name = "address",value = "地址",required = true)
    @NotBlank(message = "地址不能为空")
    private String address;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
