package com.hisun.lemon.tam.mq;


import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.tam.dto.ResultTransferOrderDTO;
import com.hisun.lemon.tam.service.ITransferOrderService;

/**
 * <AUTHOR>
 * @date 2017年8月28日
 * @time 下午2:54:28
 *
 */
@Component(TransferResultHandler.BEAN_NAME)
public class TransferResultHandler implements MessageHandler<ResultTransferOrderDTO>{
    public static final String BEAN_NAME="transferResultHandler";
    private static final Logger logger = LoggerFactory.getLogger(TransferResultHandler.class);
    @Resource
    private ITransferOrderService transferServer;
    @Resource
    ObjectMapper objectMapper;
    
    
    @Override
    public void onMessageReceive(GenericCmdDTO<ResultTransferOrderDTO> genericCmdDTO) {
    	 logger.info("接收银行结果通知可执行消息对象GenericCmdDTO：" + genericCmdDTO.toString());
    	 ResultTransferOrderDTO transferDTO=genericCmdDTO.getBody();
    	 GenericDTO<ResultTransferOrderDTO> resultGeneric=new GenericDTO<ResultTransferOrderDTO>();
    	 resultGeneric.setBody(transferDTO);
         this.transferServer.transferOrderResult(resultGeneric);

    }
}
