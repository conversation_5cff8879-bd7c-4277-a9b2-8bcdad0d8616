package com.hisun.lemon.tam.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.*;
import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.CommonEncryptReqDTO;
import com.hisun.lemon.cmm.dto.CommonEncryptRspDTO;
import com.hisun.lemon.cmm.dto.MessageSendReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.client.CregisClient;
import com.hisun.lemon.cpi.dto.CregisRsp;
import com.hisun.lemon.cpi.dto.PaymentPrepareReqDTO;
import com.hisun.lemon.cpi.dto.PaymentReqDTO;
import com.hisun.lemon.cpo.client.RouteClient;
import com.hisun.lemon.cpo.client.WithdrawClient;
import com.hisun.lemon.cpo.common.CpoConstants;
import com.hisun.lemon.cpo.dto.WdcProcessReqDTO;
import com.hisun.lemon.cpo.dto.WithdrawReqDTO;
import com.hisun.lemon.cpo.dto.WithdrawResDTO;
import com.hisun.lemon.cpo.enums.CorpBusSubTyp;
import com.hisun.lemon.cpo.enums.CorpBusTyp;
import com.hisun.lemon.csh.client.CshOrderClient;
import com.hisun.lemon.csh.client.CshRefundClient;
import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.CashierViewDTO;
import com.hisun.lemon.csh.dto.cashier.DirectPaymentDTO;
import com.hisun.lemon.csh.dto.cashier.InitCashierDTO;
import com.hisun.lemon.csh.dto.cashier.TransferPaymentDTO;
import com.hisun.lemon.csh.dto.order.OrderDTO;
import com.hisun.lemon.csh.dto.payment.AcledaTransferReqDTO;
import com.hisun.lemon.csh.dto.payment.AcledaTransferRspDTO;
import com.hisun.lemon.csh.dto.payment.PaymentResultDTO;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.OrderStatus;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.i18n.LocaleMessageSource;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.tam.component.AcmComponent;
import com.hisun.lemon.tam.constants.DmPlatformEnum;
import com.hisun.lemon.tam.constants.DmSkAddrTypeEnum;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tam.dao.DmPlatFormDao;
import com.hisun.lemon.tam.dao.DmWhtAddrDao;
import com.hisun.lemon.tam.dao.GetDmSkAddrTypeReqDTO;
import com.hisun.lemon.tam.dto.*;
import com.hisun.lemon.tam.dto.QueryResultTransferOrderDTO.queryOrder;
import com.hisun.lemon.tam.entity.DmPlatFormDO;
import com.hisun.lemon.tam.entity.DmWhtAddrDO;
import com.hisun.lemon.tam.entity.TransferOrderDO;
import com.hisun.lemon.tam.service.ITransferOrderService;
import com.hisun.lemon.tfm.client.TfmServerClient;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateReqDTO;
import com.hisun.lemon.tfm.dto.MerchantFeeCalculateRspDTO;
import com.hisun.lemon.tfm.dto.TradeFeeReqDTO;
import com.hisun.lemon.tfm.dto.TradeFeeRspDTO;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("transferService")
public class TransferServiceImpl extends BaseService implements ITransferOrderService {
	private static final Logger logger = LoggerFactory.getLogger(TransferServiceImpl.class);
	@Resource
	TransferTransactionService transactionalService;
	@Resource
	CshOrderClient cshOrderClient;
	@Resource
	UserBasicInfClient userInfoClient;
	@Resource
	private AcmComponent acmComponent;
	@Resource
	private RouteClient routeClient;
	@Resource
	private WithdrawClient withdrawClien;
	@Resource
	private CmmServerClient cmmServerClient;
	@Resource
	private TfmServerClient tfmServerClient;
	@Resource
	LocaleMessageSource localeMessageSource;
	@Resource
	CshRefundClient cshRefundClient;
	@Resource
	RiskCheckClient riskCheckClient;
	@Resource
	protected DistributedLocker locker;
	@Resource
	private AccountManagementClient acmManageClient;

	@Resource
	private CregisClient cregisClient;

	@Value("${Cregis.fundFlowCode}")
	private String fundFlowCode;

	@Resource
	private DmWhtAddrDao dmWhtAddrDao;

	@Resource
	private DmPlatFormDao dmPlatFormDao;

	/**
	 * 补单 CPO对账
	 */
	@Override
	public void killCpoOrder(GenericDTO<KillOrderRspDTO> killOrderRspDTO) {
		KillOrderRspDTO killRspDTO = killOrderRspDTO.getBody();
		String orderNo = killRspDTO.getOrderNo();
		TransferOrderDO transferDO = this.transactionalService.tansferInfo(orderNo);
		BigDecimal amount = transferDO.getAmount();
		BigDecimal fee = transferDO.getFee();
		logger.error("订单" + orderNo + "补单  与CPO对账");
		if (JudgeUtils.isBlank(killRspDTO.getType())) {
			throw new LemonException("TAM10004");
		}
		if (StringUtils.equals(killRspDTO.getType(), "0603")) {
			List<AccountingReqDTO> acUserList = new ArrayList();
			logger.info("====================通知失败=================");
			AccountingReqDTO userAccountReqDTO = null;
			AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
			AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
			String acmJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
			acmJrnNo = transferDO.getBusType() + acmJrnNo;
			String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
			BigDecimal amountSum = amount.add(fee);
			String balAcNo = acmComponent.getAcmAcNo(transferDO.getUserId(), balCapType);
			// 借：应付账款-待结算款-批量付款 100
			cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.ITM_AC_TYP,
					balCapType, ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_TAM_PAYMENT, null, null, null, null,
					"转账资金能力通知F");
			// 贷：其他应付款-支付账户-现金账户 102
			userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo,
					ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null,
					null, null, null, "转账资金能力通知F");
			acUserList.add(cshItemReqDTO);
			acUserList.add(userAccountReqDTO);
			if (fee.compareTo(BigDecimal.valueOf(0)) > 0) {
				// 借：手续费收入-支付账户-转账 2
				feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
						transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, fee, null, ACMConstants.ITM_AC_TYP,
						balCapType, ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_TAM_PAY,
						null, null, null, "转账资金能力通知F");
				acUserList.add(feeItemReqDTO);

			}
			acmComponent.requestAc(acUserList);
			// 调用收银台更新原订单状态为转账失败 更新账单
			cshOrderClient.updateOrder(transferDO.getBusOrderNo(), OrderStatus.FAIL.getValue());
			// 更新转账订单表状态为失败
			TransferOrderDO transDO = new TransferOrderDO();
			transDO.setOrderNo(transferDO.getOrderNo());
			transDO.setOrderSts(TamConstants.ORD_STS_F);
			transactionalService.updateTransferOrder(transDO);
		}

		if (StringUtils.equals(killRspDTO.getType(), "0602")) {
			TransferOrderDO updateTransfer = new TransferOrderDO();
			updateTransfer.setOrderNo(transferDO.getOrderNo());
			updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
			updateTransfer.setAmount(transferDO.getAmount());
			updateTransfer.setTxType(transferDO.getTxType());
			updateTransfer.setBusType(updateTransfer.getBusType());
			updateTransfer.setAcTm(killOrderRspDTO.getAccDate());
			// 更新订单信息
			this.transactionalService.updateTransferOrder(updateTransfer);
		}
	}

	/**
	 * 补单 0601 与收银台对账
	 */
	@Override
	public void killOrder(GenericDTO<KillOrderRspDTO> killOrderRspDTO) {
		KillOrderRspDTO killRspDTO = killOrderRspDTO.getBody();
		String orderNo = killRspDTO.getOrderNo();
		TransferOrderDO transferDO = this.transactionalService.tansferInfo(orderNo);
		BigDecimal amount = transferDO.getAmount();
		BigDecimal fee = transferDO.getFee();
		BigDecimal amountSum = amount.add(fee);

		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		String balAcNo = acmComponent.getAcmAcNo(transferDO.getCrossUserId(), balCapType);

		List<AccountingReqDTO> acUserList = new ArrayList();
		String acmJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
		acmJrnNo = transferDO.getBusType() + acmJrnNo;
		logger.info("转账到账户======================账务处理" + acmJrnNo);
		// 借:其他应付款-暂收-收银台 100
		cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo, transferDO.getTxType(),
				ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
				ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_CSH_PAY, null, null, null, null, "TAM转账");
		// 贷：其他应付款-支付账户-现金账户 100
		userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
				transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.USER_AC_TYP,
				balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "TAM转账");
		acUserList.add(cshItemReqDTO);
		acUserList.add(userAccountReqDTO);
		if (fee.compareTo(BigDecimal.valueOf(0)) > 0) {
			// 贷：手续费收入-支付账户-转账 2
			feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, fee, null, ACMConstants.ITM_AC_TYP,
					balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_TAM_PAY, null,
					null, null, "TAM转账");
			acUserList.add(feeItemReqDTO);
			logger.info("请求账务处理========================" + transferDO.getOrderNo());
		}
		acmComponent.requestAc(acUserList);
		logger.info("请求账务处理完成=========================");
		TransferOrderDO updateTransfer = new TransferOrderDO();
		updateTransfer.setOrderNo(transferDO.getOrderNo());
		updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
		updateTransfer.setAmount(transferDO.getAmount());
		updateTransfer.setTxType(transferDO.getTxType());
		updateTransfer.setBusType(updateTransfer.getBusType());
		updateTransfer.setAcTm(killOrderRspDTO.getAccDate());
		// 更新订单信息
		this.transactionalService.updateTransferOrder(updateTransfer);
	}

	/**
	 * 用户基本信息查询
	 * 
	 * @param userId
	 * @return
	 */
	public GenericRspDTO<UserBasicInfDTO> queryUser(String userId) {
		GenericRspDTO<UserBasicInfDTO> userInfoDTO = userInfoClient.queryUser(userId);
		if (!JudgeUtils.isSuccess(userInfoDTO.getMsgCd())) {
			throw new LemonException(userInfoDTO.getMsgCd());
		}
		return userInfoDTO;
	}

	/**
	 * 调用收银台公共
	 * 
	 * @param transferDO
	 * @return
	 */
	public GenericRspDTO<CashierViewDTO> cashierView(TransferOrderDO transferDO) {
		// 调用收银
		InitCashierDTO initCashierDTO = new InitCashierDTO();
		initCashierDTO.setBusPaytype(null);
		initCashierDTO.setBusType(transferDO.getBusType());
		initCashierDTO.setExtOrderNo(transferDO.getOrderNo());
		initCashierDTO.setSysChannel(transferDO.getSysChannel());
		// 付款方
		initCashierDTO.setPayerId(transferDO.getUserId());
		initCashierDTO.setPayeeId(transferDO.getCrossUserId());
		initCashierDTO.setExtInfo(transferDO.getPayeMblNo() + "|" + transferDO.getLastMblNo());
		// 收款方
		if (StringUtils.equals(transferDO.getBusType(), TamConstants.BUS_TYPE_TRANSFER_B)) {
			initCashierDTO.setPayeeId(transferDO.getCapCrdNo());
			initCashierDTO.setCrdCorpOrg(transferDO.getCrdCorpOrg());
			initCashierDTO.setExtInfo(transferDO.getPayeMblNo() + "|" + transferDO.getLastCapCrdNo());
		}
		initCashierDTO.setGoodsDesc(transferDO.getGoodDesc());
		initCashierDTO.setAppCnl(LemonUtils.getApplicationName());
		initCashierDTO.setTxType(transferDO.getTxType());
		initCashierDTO.setOrderAmt(transferDO.getAmount());
		initCashierDTO.setRemark(transferDO.getRemark());
		GenericDTO<InitCashierDTO> genericDTO = new GenericDTO<>();
		genericDTO.setBody(initCashierDTO);
		logger.info("订单：" + transferDO.getOrderNo() + " 请求收银台");
		GenericRspDTO<CashierViewDTO> rspDTO = new GenericRspDTO<>();
		rspDTO = cshOrderClient.initCashier(genericDTO);
		if (!JudgeUtils.isSuccess(rspDTO.getMsgCd())) {
			logger.error("收银台下单失败");
			throw new LemonException(rspDTO.getMsgCd());
		}
		// 收银下单成功保存收银订单号
		String busOrderNo = rspDTO.getBody().getOrderNo();
		BigDecimal fee = rspDTO.getBody().getFeeAmt();
		TransferOrderDO transfer = new TransferOrderDO();
		transfer.setBusOrderNo(busOrderNo);
		transfer.setOrderNo(transferDO.getOrderNo());
		transfer.setFee(fee);
		this.transactionalService.updateTransferOrder(transfer);
		return rspDTO;
	}

	/**
	 * 查询账户信息
	 */
	@Override
	public UserInfoRspDTO userInfo(String mblNo) {
		GenericRspDTO<UserBasicInfDTO> infoDTO = userInfoClient.queryUserByLoginId(mblNo);
		if (!JudgeUtils.isSuccess(infoDTO.getMsgCd())) {
			logger.error("查询账户信息失败:" + mblNo);
			throw new LemonException(infoDTO.getMsgCd());
		}
		UserInfoRspDTO userInfo = new UserInfoRspDTO();
		userInfo.setUserId(infoDTO.getBody().getUserId());
		userInfo.setMblNo(infoDTO.getBody().getMblNo());
		userInfo.setDisplayNm(infoDTO.getBody().getDisplayNm());
		userInfo.setAvatarPath(infoDTO.getBody().getAvatarPath());
		userInfo.setUsrSts(infoDTO.getBody().getUsrSts());
		userInfo.setUsrLvl(infoDTO.getBody().getUsrLvl());
		userInfo.setIdChkFlg(infoDTO.getBody().getIdChkFlg());
		userInfo.setUsrNmHid(infoDTO.getBody().getUsrNmHid());
		userInfo.setIdType(infoDTO.getBody().getIdType());
		userInfo.setIdNo(infoDTO.getBody().getIdNo());
		userInfo.setUsrNm(infoDTO.getBody().getUsrNm());
		userInfo.setUsrGender(infoDTO.getBody().getUsrGender());
		userInfo.setUsrNation(infoDTO.getBody().getUsrNation());
		userInfo.setUsrBirthDt(infoDTO.getBody().getUsrBirthDt());
		return userInfo;
	}
	
	/**
	 * 历史转账记录
	 */
	@Override
	public List<queryOrder> historyTransferOrder(QueryTransferOrderDTO queryDTO) {
		TransferOrderDO transferDo = new TransferOrderDO();
		transferDo.setUserId(LemonUtils.getUserId());
		Integer pageSize = queryDTO.getPageSize();
		Integer pageNum = queryDTO.getPageNum();
		transferDo.setPageNum(pageNum);
		transferDo.setPageSize(pageSize);
		if (JudgeUtils.isNull(pageNum)) {
			transferDo.setPageNum(PageUtils.getDefaultPageNum());
		}
		if (JudgeUtils.isNull(pageSize)) {
			transferDo.setPageSize(PageUtils.getDefaultPageSize());
			;
		}
		List<TransferOrderDO> tranferDOs = PageUtils.pageQuery(transferDo.getPageNum(), transferDo.getPageSize(), false,
				() -> {
					return this.transactionalService.historyTransferOrder(transferDo);
				});
		List<queryOrder> queryList = null;
		if (JudgeUtils.isNotNull(tranferDOs)) {
			queryList = tranferDOs.stream().map(userDO -> {
				queryOrder user = new QueryResultTransferOrderDTO.queryOrder();
				BeanUtils.copyProperties(user, userDO);
				if(JudgeUtils.isNotNull(user.getMblNo())){
					UserInfoRspDTO userInfoRspDTO =userInfo(user.getMblNo());
					user.setGatherAvatar(userInfoRspDTO.getAvatarPath());
				}
				return user;
			}).collect(Collectors.toCollection(ArrayList::new));
		} else {
			throw new LemonException("TAM20006");
		}
		return queryList;
	}

	/**
	 * 面对面转账
	 */
	@Override
	public GenericRspDTO<CashierViewDTO> createFaceOrder(GenericDTO<FaceTransferOrderDTO> faceTransferOrderDTO) {
		FaceTransferOrderDTO orderDTO = faceTransferOrderDTO.getBody();
		TransferOrderDO transferDO = new TransferOrderDO();
		String userId = LemonUtils.getUserId();
		String crossUserId = orderDTO.getCrossUserId();
		transferDO.setOrderCcy(TamConstants.QP_PAY_CCY);
		if (!JudgeUtils.isNull(orderDTO.getOrderCcy())) {
			transferDO.setOrderCcy(orderDTO.getOrderCcy());
		}
		if (StringUtils.equals(userId, crossUserId)) {
			throw new LemonException("TAM20010");
		}
		String sysChannel=orderDTO.getSysChannel();
		if(JudgeUtils.isBlank(sysChannel)){
			sysChannel="APP";
		}
		transferDO.setSysChannel(sysChannel);
		//检查被扫用户是否为黑名单(收款方)
		RiskCheckUserStatusReqDTO checkUserReq=new RiskCheckUserStatusReqDTO();
		checkUserReq.setId(crossUserId);
		checkUserReq.setIdTyp("01");
		checkUserReq.setTxTyp("03");
		GenericRspDTO<NoBody>   genericRspDTO=riskCheckClient.checkUserStatus(checkUserReq);
		if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
			LemonException.throwBusinessException("TAM20013");
		}
		
		// 根据用户编号查询 用户名字 手机号码
		GenericRspDTO<UserBasicInfDTO> queryUser = queryUser(crossUserId);
		UserBasicInfDTO user = queryUser.getBody();
		String gatherName = user.getUsrNm();
		GenericRspDTO<UserBasicInfDTO> queryPayUser = queryUser(userId);
		UserBasicInfDTO userPay = queryPayUser.getBody();
		String payerName = userPay.getUsrNm();
		transferDO.setGatherName(gatherName);
		transferDO.setPayerName(payerName);
		transferDO.setPayeMblNo(userPay.getMblNo());
		transferDO.setMblNo(user.getMblNo());
		transferDO.setCrossUserId(crossUserId);
		transferDO.setAmount(orderDTO.getAmount());
		transferDO.setUserId(userId);
		transferDO.setRemark(orderDTO.getRemark());
		transferDO.setTxType(orderDTO.getTxType());
		transferDO.setBusType(orderDTO.getBusType());
		transferDO.setOrderSts(TamConstants.ORD_STS_U);
		String ymd = DateTimeUtils.getCurrentDateStr();
		String orderNo = TamConstants.BUS_TYPE_TRANSFER_U + ymd
				+ IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 12);
		transferDO.setOrderNo(orderNo);
		transferDO.setAcTm(faceTransferOrderDTO.getAccDate());
		transferDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
		String mblNo = transferDO.getMblNo();

		if (mblNo.length() > 4) {
			String mblNoStr = mblNo.substring(mblNo.length() - 4, mblNo.length());
			String mblNoDesc = mblNo.substring(mblNo.length() - 2, mblNo.length());
			String topMblNoStr = mblNo.substring(mblNo.indexOf("-") + 1, mblNo.length());
			String topMblNo = topMblNoStr.substring(0, 3);
			String mblNoHid = topMblNo + "****" + mblNoStr;
			transferDO.setMblNoHid(mblNoHid);
			transferDO.setLastMblNo(mblNoStr);
			String goodesc=topMblNo+"***"+mblNoDesc;
			Object[] args = new Object[] { goodesc };
			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_F, args);
			transferDO.setGoodDesc(descStr);
		} else {
			Object[] args = new Object[] { mblNo };
			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_F, args);
			transferDO.setGoodDesc(descStr);
			transferDO.setLastMblNo(mblNo);
		}
		// 生成转账订单
		this.transactionalService.createOrder(transferDO);
		// 调用收银
		GenericRspDTO<CashierViewDTO> userRspDTO = cashierView(transferDO);
		return userRspDTO;
	}

	/**
	 * 转账到用户下单(对外接口)
	 */
	@Override
	public GenericRspDTO<UserTransferRspDTO> createUserOutOrder(
			GenericDTO<UserOutTransferOrderDTO> userOutTransferOrderDTO) {
		UserOutTransferOrderDTO orderDTO = userOutTransferOrderDTO.getBody();
		TransferOrderDO transferDO = new TransferOrderDO();
		// 对方用户内部号
		String mnlNo = orderDTO.getMblNo();
		UserInfoRspDTO userInfo = userInfo(mnlNo);
		String crossUserId = userInfo.getUserId();
		String userMblNo = orderDTO.getUserMblNo();
		UserInfoRspDTO userInfos = userInfo(userMblNo);
		String userId = userInfos.getUserId();
		if (StringUtils.equals(userId, crossUserId)) {
			throw new LemonException("TAM20010");
		}
		BigDecimal amount = orderDTO.getAmount();
		if (BigDecimal.valueOf(0).compareTo(amount) >= 0) {
			throw new LemonException("TAM10021");
		}
		transferDO.setOrderCcy(TamConstants.QP_PAY_CCY);
		if (!JudgeUtils.isNull(orderDTO.getOrderCcy())) {
			transferDO.setOrderCcy(orderDTO.getOrderCcy());
		}
		transferDO.setCrossUserId(crossUserId);
		// 根据用户编号查询 用户名字 手机号码
		GenericRspDTO<UserBasicInfDTO> queryUser = queryUser(crossUserId);
		UserBasicInfDTO user = queryUser.getBody();
		String gatherName = user.getUsrNm();
		transferDO.setGatherName(gatherName);

		GenericRspDTO<UserBasicInfDTO> queryPayUser = queryUser(userId);
		UserBasicInfDTO queryPay = queryPayUser.getBody();
		transferDO.setPayerName(queryPay.getUsrNm());
		transferDO.setPayeMblNo(queryPay.getMblNo());

		transferDO.setAmount(orderDTO.getAmount());
		transferDO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		transferDO.setBusType(orderDTO.getBusType());
		transferDO.setUserId(userId);
		transferDO.setMblNo(orderDTO.getMblNo());
		transferDO.setRemark(orderDTO.getRemark());
		transferDO.setOrderSts(TamConstants.ORD_STS_U);
		String ymd = DateTimeUtils.getCurrentDateStr();
		String orderNo = TamConstants.BUS_TYPE_TRANSFER_U + ymd
				+ IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 12);
		transferDO.setOrderNo(orderNo);
		transferDO.setAcTm(userOutTransferOrderDTO.getAccDate());
		transferDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());

		String mblNo = transferDO.getMblNo();
		if (mblNo.length() > 4) {
			String mblNoStr = mblNo.substring(mblNo.length() - 4, mblNo.length());
			String mblNoDesc = mblNo.substring(mblNo.length() - 2, mblNo.length());
			String topMblNoStr = mblNo.substring(mblNo.indexOf("-") + 1, mblNo.length());
			String topMblNo = topMblNoStr.substring(0, 3);
			String mblNoHid = topMblNo + "****" + mblNoStr;
			transferDO.setMblNoHid(mblNoHid);
			transferDO.setLastMblNo(mblNoStr);
			String goodesc=topMblNo+"***"+mblNoDesc;
			Object[] args = new Object[] { goodesc };
			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
			transferDO.setGoodDesc(descStr);
		} else {
			Object[] args = new Object[] { mblNo };
			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
			transferDO.setGoodDesc(descStr);
			transferDO.setLastMblNo(mblNo);
		}
		// 填充查询手续费数据
		GenericDTO<TradeFeeReqDTO> TradeFeeReq = new GenericDTO<TradeFeeReqDTO>();
		TradeFeeReqDTO tradeFeeReqDTO = new TradeFeeReqDTO();
		tradeFeeReqDTO.setBusOrderNo(transferDO.getOrderNo());
		tradeFeeReqDTO.setBusOrderTime(DateTimeUtils.getCurrentLocalDateTime());
		tradeFeeReqDTO.setBusType(TamConstants.BUS_TYPE_TRANSFER_U);
		tradeFeeReqDTO.setCcy(transferDO.getOrderCcy());
		tradeFeeReqDTO.setTradeAmt(transferDO.getAmount());
		tradeFeeReqDTO.setUserId(transferDO.getUserId());
		TradeFeeReq.setBody(tradeFeeReqDTO);
		logger.info("开始调用计费===============" + transferDO.getOrderNo());
		// 调用tfm接口查询手续费
		GenericRspDTO<TradeFeeRspDTO> tradeGenericRspDTO = tfmServerClient.tradeFee(TradeFeeReq);
		if (JudgeUtils.isNotSuccess(tradeGenericRspDTO.getMsgCd())) {
			logger.error("调用计费失败");
			throw new LemonException(tradeGenericRspDTO.getMsgCd());
		}
		// 手续费
		BigDecimal fee = tradeGenericRspDTO.getBody().getTradeFee();
		transferDO.setFee(fee);
		// 生成转账订单
		this.transactionalService.createOrder(transferDO);

		// 调用收银台 直付接口 进行转账
		DirectPaymentDTO directPaymentDTO = new DirectPaymentDTO();
		directPaymentDTO.setExtOrderNo(transferDO.getOrderNo());
		directPaymentDTO.setOrderCcy(transferDO.getOrderCcy());
		directPaymentDTO.setOrderAmt(transferDO.getAmount());
		directPaymentDTO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		directPaymentDTO.setBusType(TamConstants.BUS_TYPE_TRANSFER_U);
		directPaymentDTO.setSysChannel("OTHER");
		directPaymentDTO.setPayerId(transferDO.getUserId());
		directPaymentDTO.setPayerName(transferDO.getPayerName());
		directPaymentDTO.setPayeeId(transferDO.getCrossUserId());
		directPaymentDTO.setPayPassword(orderDTO.getPayPassword());
		directPaymentDTO.setValidateRandom(orderDTO.getValidateRandom());
		directPaymentDTO.setSeaRandom(orderDTO.getSeaRandom());
		directPaymentDTO.setAppCnl(LemonUtils.getApplicationName());
		directPaymentDTO.setBusPaytype(TamConstants.BUS_PAY_TYPE);
		directPaymentDTO.setGoodsDesc(transferDO.getGoodDesc());
		directPaymentDTO.sethCouponAmt(0);
		// 支付金额 =转账金额+手续费
		BigDecimal feeAmout = transferDO.getFee();
		BigDecimal cashAmt = transferDO.getAmount().add(feeAmout);
		directPaymentDTO.setCashAmt(cashAmt);
		logger.info("订单：" + transferDO.getOrderNo() + " 请求收银台");
		GenericDTO<DirectPaymentDTO> DirectPayment = new GenericDTO<DirectPaymentDTO>();
		DirectPayment.setBody(directPaymentDTO);
		GenericRspDTO<PaymentResultDTO> rspDTO = cshOrderClient.payByDirectBal(DirectPayment);
		if (!JudgeUtils.isSuccess(rspDTO.getMsgCd())) {
			logger.error("调用收银台后台直付接口失败");
			throw new LemonException(rspDTO.getMsgCd());
		}
		PaymentResultDTO paymentResultDTO = rspDTO.getBody();

		// 订单号
		String orderId = paymentResultDTO.getBusOrderNo();
		TransferOrderDO transferDB = new TransferOrderDO();
		transferDB = this.transactionalService.tansferInfo(orderId);
		// 原订单不存在
		if (JudgeUtils.isNull(transferDB)) {
			throw new LemonException("TAM20001");
		}
		// 订单已经成功
		if (StringUtils.equals(transferDB.getOrderSts(), TamConstants.ORD_STS_S)) {
			logger.info("订单已成功");
			throw new LemonException("TAM20011");
		}

		BigDecimal orderAmount = transferDB.getAmount();
		BigDecimal amountSum = orderAmount.add(fee);
		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		List<AccountingReqDTO> acUserList = new ArrayList();
		// 流水号
		String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
		payJrnNo = transferDO.getBusType() + payJrnNo;
		// 资金类型
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		// 对手方用户id
		String balAcNo = acmComponent.getAcmAcNo(crossUserId, balCapType);
		// 借:其他应付款-暂收-收银台 100
		cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), payJrnNo, transferDO.getTxType(),
				ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo, ACMConstants.ITM_AC_TYP, balCapType,
				ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_CSH_PAY, null, null, null, null, "转账out");
		// 贷：其他应付款-支付账户-现金账户 100
		userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), payJrnNo,
				transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.USER_AC_TYP,
				balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "转账out");
		acUserList.add(cshItemReqDTO);
		acUserList.add(userAccountReqDTO);
		if (fee.compareTo(BigDecimal.valueOf(0)) > 0) {
			// 贷：手续费收入-支付账户-转账 2
			feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), payJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, fee, null, ACMConstants.ITM_AC_TYP, null,
					ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_TAM_PAY, null, null, null,
					"转账out");
			acUserList.add(feeItemReqDTO);
		}
		acmComponent.requestAc(acUserList);
		logger.info("请求账务处理完成=========================");

		// 账务成功更新订单状态
		TransferOrderDO updateTransfer = new TransferOrderDO();
		updateTransfer.setBusOrderNo(paymentResultDTO.getOrderNo());
		updateTransfer.setOrderNo(paymentResultDTO.getBusOrderNo());
		updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
		updateTransfer.setAmount(paymentResultDTO.getOrderAmt());
		updateTransfer.setGoodDesc(paymentResultDTO.getGoodsDesc());
		// 更新订单信息
		this.transactionalService.updateTransferOrder(updateTransfer);

		// 组装对外传输数据
		GenericRspDTO<UserTransferRspDTO> userRsp = new GenericRspDTO<UserTransferRspDTO>();
		UserTransferRspDTO userOut = new UserTransferRspDTO();
		userOut.setAmount(paymentResultDTO.getOrderAmt());
		userOut.setFee(fee);
		userOut.setOrderSts(TamConstants.ORD_STS_S);
		userOut.setOrderNo(paymentResultDTO.getOrderNo());
		// 消息推送
		sendMessage(transferDO);
		return userRsp.newSuccessInstance(userOut);
	}

	/**
	 * 转账到账户订单申请(对外接口) -  新版本
	 *
	 * @param userOutTransferOrderDTO
	 * @return
	 */
	@Override
	public GenericRspDTO<UserTransferRspDTO> createUserOutOrderNew(GenericDTO<NewUserOutTransferOrderDTO> userOutTransferOrderDTO) {
		NewUserOutTransferOrderDTO transferOrder = userOutTransferOrderDTO.getBody();
		BigDecimal amount = transferOrder.getAmount();
		BigDecimal reqFee = transferOrder.getFee();

		// 信息校验
		HashMap<String, QueryAcBalRspDTO> map = checkAccountInfo(transferOrder);
		QueryAcBalRspDTO fkAcBalRspDTO = map.get("fkAcBalRspDTO");
		QueryAcBalRspDTO skAcBalRspDTO = map.get("skAcBalRspDTO");
		String userId = fkAcBalRspDTO.getUserId();
		TransferOrderDO transferDO = new TransferOrderDO();
		transferDO.setOrderCcy(transferOrder.getOrderCcy());
		transferDO.setCrossUserId(skAcBalRspDTO.getUserId());
		// 根据用户编号查询 用户名字 手机号码
		GenericRspDTO<UserBasicInfDTO> queryUser = queryUser(skAcBalRspDTO.getUserId());
		UserBasicInfDTO user = queryUser.getBody();
		String gatherName = user.getUsrNm();
		transferDO.setGatherName(gatherName);

		GenericRspDTO<UserBasicInfDTO> queryPayUser = queryUser(userId);
		UserBasicInfDTO queryPay = queryPayUser.getBody();
		transferDO.setPayerName(queryPay.getUsrNm());
		transferDO.setPayeMblNo(queryPay.getMblNo());

		transferDO.setFkAcNo(transferOrder.getUserMblNo());
		transferDO.setSkAcNo(transferOrder.getMblNo());
		transferDO.setAmount(transferOrder.getAmount());
		transferDO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		transferDO.setBusType(transferOrder.getBusType());
		transferDO.setUserId(userId);
		transferDO.setMblNo(transferOrder.getMblNo());
		transferDO.setRemark(transferOrder.getRemark());
		transferDO.setOrderSts(TamConstants.ORD_STS_U);
		String ymd = DateTimeUtils.getCurrentDateStr();
		String orderNo = TamConstants.BUS_TYPE_TRANSFER_U + ymd
				+ IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 12);
		transferDO.setOrderNo(orderNo);
		transferDO.setAcTm(userOutTransferOrderDTO.getAccDate());
		transferDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());

//		String mblNo = transferDO.getMblNo();
//		if (mblNo.length() > 4) {
//			String mblNoStr = mblNo.substring(mblNo.length() - 4, mblNo.length());
//			String mblNoDesc = mblNo.substring(mblNo.length() - 2, mblNo.length());
//			String topMblNoStr = mblNo.substring(mblNo.indexOf("-") + 1, mblNo.length());
//			String topMblNo = topMblNoStr.substring(0, 3);
//			String mblNoHid = topMblNo + "****" + mblNoStr;
//			transferDO.setMblNoHid(mblNoHid);
//			transferDO.setLastMblNo(mblNoStr);
//			String goodesc=topMblNo+"***"+mblNoDesc;
//			Object[] args = new Object[] { goodesc };
//			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
//			transferDO.setGoodDesc(descStr);
//		} else {
//			Object[] args = new Object[] { mblNo };
//			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
//			transferDO.setGoodDesc(descStr);
//			transferDO.setLastMblNo(mblNo);
//		}
		// 填充查询手续费数据
		GenericDTO<TradeFeeReqDTO> TradeFeeReq = new GenericDTO<TradeFeeReqDTO>();
		TradeFeeReqDTO tradeFeeReqDTO = new TradeFeeReqDTO();
		tradeFeeReqDTO.setBusOrderNo(transferDO.getOrderNo());
		tradeFeeReqDTO.setBusOrderTime(DateTimeUtils.getCurrentLocalDateTime());
		tradeFeeReqDTO.setBusType(TamConstants.BUS_TYPE_TRANSFER_U);
		tradeFeeReqDTO.setCcy(transferDO.getOrderCcy());
		tradeFeeReqDTO.setTradeAmt(transferDO.getAmount());
		tradeFeeReqDTO.setUserId(transferDO.getUserId());
		TradeFeeReq.setBody(tradeFeeReqDTO);
		logger.info("开始调用计费===============" + transferDO.getOrderNo());
		// 调用tfm接口查询手续费
		GenericRspDTO<TradeFeeRspDTO> tradeGenericRspDTO = tfmServerClient.tradeFee(TradeFeeReq);
		if (JudgeUtils.isNotSuccess(tradeGenericRspDTO.getMsgCd())) {
			logger.error("调用计费失败");
			throw new LemonException(tradeGenericRspDTO.getMsgCd());
		}
		TradeFeeRspDTO feeRspDTO = tradeGenericRspDTO.getBody();
		// 手续费
		BigDecimal fee = feeRspDTO.getTradeFee();
		// 交易金额
		BigDecimal tradeAmt = feeRspDTO.getTradeAmt();
		if(reqFee.compareTo(fee) != 0){
			logger.info("手续费不一致,请求的手续费:{},计算的手续费:{}",reqFee,fee);
			throw new LemonException("PWM30006");
		}
		tradeGenericRspDTO.getBody().getCalculateMode();
		transferDO.setFee(fee);
		transferDO.setActualTradeAmt(tradeAmt);
		// 生成转账订单
		this.transactionalService.createOrder(transferDO);

		// 调用收银台 直付接口 进行转账
		DirectPaymentDTO directPaymentDTO = new DirectPaymentDTO();
		directPaymentDTO.setExtOrderNo(transferDO.getOrderNo());
		directPaymentDTO.setOrderCcy(transferDO.getOrderCcy());
		directPaymentDTO.setOrderAmt(transferDO.getAmount());
		directPaymentDTO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		directPaymentDTO.setBusType(TamConstants.BUS_TYPE_TRANSFER_U);
		directPaymentDTO.setSysChannel("OTHER");
		directPaymentDTO.setPayerId(transferDO.getUserId());
		directPaymentDTO.setPayerName(transferDO.getPayerName());
		directPaymentDTO.setPayeeId(transferDO.getCrossUserId());
		directPaymentDTO.setPayPassword(transferOrder.getPayPassword());
		directPaymentDTO.setAppCnl(LemonUtils.getApplicationName());
		directPaymentDTO.setBusPaytype(TamConstants.BUS_PAY_TYPE);
		directPaymentDTO.setGoodsDesc(transferDO.getGoodDesc());
		directPaymentDTO.sethCouponAmt(0);
		directPaymentDTO.setCashAmt(tradeAmt);
		logger.info("订单：" + transferDO.getOrderNo() + " 请求收银台");
		GenericDTO<DirectPaymentDTO> DirectPayment = new GenericDTO<DirectPaymentDTO>();
		DirectPayment.setBody(directPaymentDTO);
		GenericRspDTO<PaymentResultDTO> rspDTO = cshOrderClient.NewPayByDirectBal(DirectPayment);
		if (!JudgeUtils.isSuccess(rspDTO.getMsgCd())) {
			logger.error("调用收银台后台直付接口失败");
			throw new LemonException(rspDTO.getMsgCd());
		}
		PaymentResultDTO paymentResultDTO = rspDTO.getBody();

		// 调用收银台后台直付接口成功 更新订单状态
		TransferOrderDO updateTransfer = new TransferOrderDO();
		updateTransfer.setBusOrderNo(paymentResultDTO.getOrderNo());
		updateTransfer.setOrderNo(paymentResultDTO.getBusOrderNo());
		// 更新订单信息
		transactionalService.updateTransferOrder(updateTransfer);

		// 组装对外传输数据
		GenericRspDTO<UserTransferRspDTO> userRsp = new GenericRspDTO<UserTransferRspDTO>();
		UserTransferRspDTO userOut = new UserTransferRspDTO();
		userOut.setAmount(paymentResultDTO.getOrderAmt());
		userOut.setFee(fee);
		userOut.setOrderSts(TamConstants.ORD_STS_P);
		userOut.setOrderNo(paymentResultDTO.getOrderNo());
		// 消息推送
//		sendMessage(transferDO);
		return userRsp.newSuccessInstance(userOut);
	}

	/**
	 * 校验收款方账户是否存在且为相同币种  信息校验
	 * @param userOutTransferOrderDTO  用户转账参数
	 */
	private HashMap<String, QueryAcBalRspDTO> checkAccountInfo(NewUserOutTransferOrderDTO userOutTransferOrderDTO) {

		HashMap<String, QueryAcBalRspDTO> map = new HashMap<>();

		String userMblNo = userOutTransferOrderDTO.getUserMblNo();
		String ccy = userOutTransferOrderDTO.getOrderCcy();
		String mblNo = userOutTransferOrderDTO.getMblNo();
		BigDecimal amount = userOutTransferOrderDTO.getAmount();

		if (userMblNo.equals(mblNo)) {
			LemonException.throwLemonException("TAM20010");
		}
		if (BigDecimal.valueOf(0).compareTo(amount) >= 0) {
			throw new LemonException("TAM10021");
		}

		// 付款方账户信息
		UserAccountDTO fkUser = new UserAccountDTO();
		fkUser.setAcNo(userMblNo);
		fkUser.setCcy(ccy);
		fkUser.setCapTyp(CapTypEnum.CAP_TYP_CASH.getCapTyp());
		GenericRspDTO<List<QueryAcBalRspDTO>> fkRspDTO = acmManageClient.queryAcBal(fkUser);
		if (JudgeUtils.isNotSuccess(fkRspDTO.getMsgCd())) {
			LemonException.throwLemonException("TAM10031");
		}
		QueryAcBalRspDTO fkAcBalRspDTO = fkRspDTO.getBody().get(0);
		map.put("fkAcBalRspDTO", fkAcBalRspDTO);

		// 收款方账户信息
		UserAccountDTO user = new UserAccountDTO();
		user.setAcNo(mblNo);
		user.setCapTyp(CapTypEnum.CAP_TYP_CASH.getCapTyp());
		GenericRspDTO<List<QueryAcBalRspDTO>> rspDTO = acmManageClient.queryAcBal(user);
		if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
			LemonException.throwLemonException("TAM10029");
		}
		QueryAcBalRspDTO skAcBalRspDTO = rspDTO.getBody().get(0);
		if (!skAcBalRspDTO.getCcy().equals(ccy)) {
			LemonException.throwLemonException("TAM10030");
		}
		map.put("skAcBalRspDTO", skAcBalRspDTO);

		// 校验用户账户余额是否足够转账
		BigDecimal balance = new BigDecimal(0);
		//查询用户账户余额
		UserAccountDTO userAccountDTO = new UserAccountDTO();
		userAccountDTO.setUserId(fkAcBalRspDTO.getUserId());
		userAccountDTO.setCcy(ccy);
		//调用账户接口，查询账户余额
		GenericRspDTO<List<QueryAcBalRspDTO>> genericRspDTO = acmManageClient.queryAcBal(userAccountDTO);
		List<QueryAcBalRspDTO> queryAcBalRspDTO = genericRspDTO.getBody();
		if(JudgeUtils.isNull(queryAcBalRspDTO)){
			LemonException.throwBusinessException("PWM30009");
		}
		//判断资金类型为现金，则填充账户余额
		for(QueryAcBalRspDTO acBalRspDTO: queryAcBalRspDTO) {
			if(JudgeUtils.equals(CapTypEnum.CAP_TYP_CASH.getCapTyp(),acBalRspDTO.getCapTyp())) {
				balance = acBalRspDTO.getAcCurBal();
			}
		}
		//校验提现金额加手续费大于用户账户余额,则抛出异常
		if(amount.compareTo(balance) > 0){
			LemonException.throwBusinessException("PWM30002");
		}

		return map;
	}

	/**
	 * 转账到用户下单
	 */
	@Override
	public GenericRspDTO<CashierViewDTO> createUserOrder(GenericDTO<UserTransferOrderDTO> userTransferOrderDTO) {
		UserTransferOrderDTO orderDTO = userTransferOrderDTO.getBody();
		String userId = LemonUtils.getUserId();
		TransferOrderDO transferDO = new TransferOrderDO();
		// 对方用户内部号
		String mnlNo = orderDTO.getMblNo();
		UserInfoRspDTO userInfo = userInfo(mnlNo);
		String crossUserId = userInfo.getUserId();

		if (StringUtils.equals(userId, crossUserId)) {
			throw new LemonException("TAM20010");
		}
		BigDecimal amount = orderDTO.getAmount();
		if (BigDecimal.valueOf(0).compareTo(amount) >= 0) {
			throw new LemonException("TAM10021");
		}
		transferDO.setOrderCcy(TamConstants.QP_PAY_CCY);
		if (!JudgeUtils.isNull(orderDTO.getOrderCcy())) {
			transferDO.setOrderCcy(orderDTO.getOrderCcy());
		}
		String sysChannel=orderDTO.getSysChannel();
		if(JudgeUtils.isBlank(sysChannel)){
			sysChannel="APP";
		}
		transferDO.setSysChannel(sysChannel);
		transferDO.setCrossUserId(crossUserId);
		// 根据用户编号查询 用户名字 手机号码
		GenericRspDTO<UserBasicInfDTO> queryUser = queryUser(crossUserId);
		UserBasicInfDTO user = queryUser.getBody();
		String gatherName = user.getUsrNm();
		transferDO.setGatherName(gatherName);

		GenericRspDTO<UserBasicInfDTO> queryPayUser = queryUser(userId);
		UserBasicInfDTO queryPay = queryPayUser.getBody();
		transferDO.setPayerName(queryPay.getUsrNm());
		transferDO.setPayeMblNo(queryPay.getMblNo());

		transferDO.setAmount(orderDTO.getAmount());
		transferDO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		transferDO.setBusType(orderDTO.getBusType());
		transferDO.setUserId(userId);
		transferDO.setMblNo(orderDTO.getMblNo());
		transferDO.setNationName(orderDTO.getNationName());
		transferDO.setAreaDesc(orderDTO.getAreaDesc());
		transferDO.setRemark(orderDTO.getRemark());
		transferDO.setOrderSts(TamConstants.ORD_STS_U);
		String ymd = DateTimeUtils.getCurrentDateStr();
		String orderNo = TamConstants.BUS_TYPE_TRANSFER_U + ymd
				+ IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 12);
		transferDO.setOrderNo(orderNo);
		transferDO.setAcTm(userTransferOrderDTO.getAccDate());
		transferDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
		String mblNo = transferDO.getMblNo();
		String language = LemonUtils.getLocale().getLanguage();
		if (StringUtils.isBlank(language)) {
			language = "en";
		}
		if (mblNo.length() > 4) {
			String mblNoStr = mblNo.substring(mblNo.length() - 4, mblNo.length());
			String mblNoDesc = mblNo.substring(mblNo.length() - 2, mblNo.length());
			String topMblNoStr = mblNo.substring(mblNo.indexOf("-") + 1, mblNo.length());
			String topMblNo = topMblNoStr.substring(0, 3);
			String mblNoHid = topMblNo + "****" + mblNoStr;
			transferDO.setMblNoHid(mblNoHid);
			transferDO.setLastMblNo(mblNoStr);
			String goodesc=topMblNo+"***"+mblNoDesc;
			Object[] args = new Object[] { goodesc };
			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
			transferDO.setGoodDesc(descStr);
		} else {
			Object[] args = new Object[] { mblNo };
			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
			transferDO.setGoodDesc(descStr);
		}

		// 生成转账订单
		this.transactionalService.createOrder(transferDO);
		// 调用收银
		GenericRspDTO<CashierViewDTO> userRspDTO = cashierView(transferDO);
		return userRspDTO;
	}

	/**
	 * 转账到银行卡
	 */
	@Override
	public GenericRspDTO<CashierViewDTO> createCardOrder(GenericDTO<CardTransferOrderDTO> cardTransferOrderDTO) {
		String userId = LemonUtils.getUserId();
		CardTransferOrderDTO cardTransferDTO = cardTransferOrderDTO.getBody();
		TransferOrderDO transferDO = new TransferOrderDO();
		BigDecimal amount = cardTransferDTO.getAmount();
		if (BigDecimal.valueOf(0).compareTo(amount) >= 0) {
			throw new LemonException("TAM10021");
		}
		transferDO.setOrderCcy(TamConstants.QP_PAY_CCY);
		if (!JudgeUtils.isNull(cardTransferDTO.getOrderCcy())) {
			transferDO.setOrderCcy(cardTransferDTO.getOrderCcy());
		}
		transferDO.setUserId(userId);
		String ymd = DateTimeUtils.getCurrentDateStr();
		String orderNo = TamConstants.BUS_TYPE_TRANSFER_B + ymd
				+ IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 11);
		transferDO.setOrderNo(orderNo);
		String sysChannel=cardTransferDTO.getSysChannel();
		if(JudgeUtils.isBlank(sysChannel)){
			sysChannel="APP";
		}
		transferDO.setSysChannel(sysChannel);
		// 根据用户编号查询 用户名字 手机号码
		GenericRspDTO<UserBasicInfDTO> userInfo = queryUser(userId);
		UserBasicInfDTO user = userInfo.getBody();
		transferDO.setPayerName(user.getUsrNm());
		transferDO.setPayeMblNo(user.getMblNo());
		transferDO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		transferDO.setBusType(cardTransferDTO.getBusType());
		transferDO.setAmount(cardTransferDTO.getAmount());
		transferDO.setOrderSts(TamConstants.ORD_STS_U);
		transferDO.setRemark(cardTransferDTO.getRemark());
		transferDO.setCrdAcTyp(cardTransferDTO.getCrdAcTyp());
		transferDO.setCrdCorpOrg(cardTransferDTO.getCapCorg());
		transferDO.setCorpOrgSnm(cardTransferDTO.getCorpOrgSnm());
		transferDO.setCapCrdNm(cardTransferDTO.getCapCrdNm());
		transferDO.setCapCrdNo(cardTransferDTO.getCapCrdNo());
		transferDO.setGatherName(cardTransferDTO.getCapCrdNm());
		transferDO.setCapCorgNm(cardTransferDTO.getCapCorgNm());
		String capCrdNo = cardTransferDTO.getCapCrdNo();

		GenericDTO<CommonEncryptReqDTO> reqDTO = new GenericDTO<>();
		CommonEncryptReqDTO commonEncryptReqDTO = new CommonEncryptReqDTO();
		commonEncryptReqDTO.setData(capCrdNo);
		commonEncryptReqDTO.setType("encrypt");
		reqDTO.setBody(commonEncryptReqDTO);
		logger.info("====================银行卡号加密=====" +capCrdNo+"=====类型"+commonEncryptReqDTO.getType());
		GenericRspDTO<CommonEncryptRspDTO> commonEncrtyRspDTO = cmmServerClient.encrypt(reqDTO);
		if (JudgeUtils.isNotSuccess(commonEncrtyRspDTO.getMsgCd())) {
			LemonException.throwBusinessException(commonEncrtyRspDTO.getMsgCd());
		}
		String crdNoEnc = commonEncrtyRspDTO.getBody().getData();
		transferDO.setCrdNoEnc(crdNoEnc);

		String cardNo = transferDO.getCapCrdNo();
		if (cardNo.length() < 4) {
			throw new LemonException("TAM10022");
		}
		String cardNoStr = cardNo.substring(cardNo.length() - 4, cardNo.length());
		transferDO.setLastCapCrdNo(cardNoStr);
		Object[] args = new Object[] {cardNoStr};
		String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_B, args);
		transferDO.setGoodDesc(descStr);
		transferDO.setAcTm(cardTransferOrderDTO.getAccDate());
		transferDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
		this.transactionalService.createOrder(transferDO);
		// 调用收银
		GenericRspDTO<CashierViewDTO> userRspDTO = cashierView(transferDO);
		if(JudgeUtils.isNotSuccess(userRspDTO.getMsgCd())){
			LemonException.throwBusinessException(userRspDTO.getMsgCd());
		}
		return userRspDTO;
	}

	/**
	 * 银行结果通知
	 */
	@Override
	public GenericRspDTO<NoBody> transferOrderResult(GenericDTO<ResultTransferOrderDTO> resultTransferOrderDTO) {

		ResultTransferOrderDTO resultTransferDTO = resultTransferOrderDTO.getBody();
		logger.info("====================开始接收资金能力通知=====" + resultTransferDTO.getOrderNo());
		TransferOrderDO transferDO = new TransferOrderDO();
		String orderNo = resultTransferDTO.getOrderNo();
		transferDO = this.transactionalService.tansferInfo(orderNo);
		BigDecimal fee = transferDO.getFee();
		BigDecimal amount = resultTransferDTO.getAmount();
		if (StringUtils.equals(resultTransferDTO.getOrderSts(), TamConstants.ORD_STS_S)) {
			if (JudgeUtils.isNull(transferDO)) {
				throw new LemonException("TAM20001");
			}
			if (StringUtils.equals(transferDO.getOrderSts(), TamConstants.ORD_STS_S)) {
				throw new LemonException("TAM20011");
			}
			if (!StringUtils.equals(resultTransferDTO.getOrderSts(), TamConstants.ORD_STS_S)) {
				TransferOrderDO updateOrderDO = new TransferOrderDO();
				updateOrderDO.setOrderNo(resultTransferDTO.getOrderNo());
				updateOrderDO.setOrderSts(TamConstants.ORD_STS_F);
				updateOrderDO.setAcTm(resultTransferOrderDTO.getAccDate());
				this.transactionalService.updateTransferOrder(updateOrderDO);
			}
			if (transferDO.getAmount().compareTo(amount) != 0) {
				throw new LemonException("TAM20002");
			}
			TransferOrderDO updateTransfer = new TransferOrderDO();
			updateTransfer.setOrderNo(resultTransferDTO.getOrderNo());
			updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
			updateTransfer.setAmount(resultTransferDTO.getAmount());
			updateTransfer.setTxType(resultTransferDTO.getTxType());
			updateTransfer.setBusType(updateTransfer.getBusType());
			updateTransfer.setAcTm(resultTransferOrderDTO.getAccDate());
			// 更新订单信息
			this.transactionalService.updateTransferOrder(updateTransfer);
			// 消息推送
			sendMessage(transferDO);
		}
		// 资金能力返回失败 做账务处理 同时需要去修改收银订单表的状态改为转账失败 收银台通知账单模块修改订单状态
		if (!StringUtils.equals(resultTransferDTO.getOrderSts(), TamConstants.ORD_STS_S)) {
			List<AccountingReqDTO> acUserList = new ArrayList();
			logger.info("====================通知失败=================");
			AccountingReqDTO userAccountReqDTO = null;
			AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
			AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
			String acmJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
			acmJrnNo = transferDO.getBusType() + acmJrnNo;
			String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
			BigDecimal amountSum = amount.add(fee);
			String balAcNo = acmComponent.getAcmAcNo(transferDO.getUserId(), balCapType);
			// 借：应付账款-待结算款-批量付款 100
			cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.ITM_AC_TYP,
					balCapType, ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_TAM_PAYMENT, null, null, null, null,
					"转账资金能力通知F");
			// 贷：其他应付款-支付账户-现金账户 102
			userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo,
					ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null,
					null, null, null, "转账资金能力通知F");
			acUserList.add(cshItemReqDTO);
			acUserList.add(userAccountReqDTO);
			if (fee.compareTo(BigDecimal.valueOf(0)) > 0) {
				// 借：手续费收入-支付账户-转账 2
				feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
						transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, fee, null, ACMConstants.ITM_AC_TYP,
						balCapType, ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_TAM_PAY,
						null, null, null, "转账资金能力通知F");
				acUserList.add(feeItemReqDTO);
			}
			acmComponent.requestAc(acUserList);
			// 调用收银台更新原订单状态为转账失败 更新账单
			cshOrderClient.updateOrder(transferDO.getBusOrderNo(),OrderStatus.FAIL.getValue());
			// 更新转账订单表状态为失败
			TransferOrderDO transDO = new TransferOrderDO();
			transDO.setOrderNo(transferDO.getOrderNo());
			transDO.setOrderSts(TamConstants.ORD_STS_F);
			transactionalService.updateTransferOrder(transDO);
		}
		return GenericRspDTO.newSuccessInstance();
	}

	/**
	 * 转账结果通知(到银行卡 结果通知需要调资金能力)
	 */
	@Override
	public GenericRspDTO<NoBody> userTransferOrderResult(GenericDTO<ResultTransferOrderDTO> resultTransferOrderDTO) {
		ResultTransferOrderDTO resultTransferDTO = resultTransferOrderDTO.getBody();
		TransferOrderDO transferDO = new TransferOrderDO();
		logger.info("收银台通知=========================" + resultTransferDTO.getOrderNo() + "订单状态:"
				+ resultTransferDTO.getOrderSts());
		String orderNo = resultTransferDTO.getOrderNo();
		transferDO = this.transactionalService.tansferInfo(orderNo);
		String branchName = transferDO.getCapCorgNm();
		BigDecimal fee = transferDO.getFee();
		if (JudgeUtils.isNull(transferDO)) {
			throw new LemonException("TAM20001");
		}
		if (StringUtils.equals(transferDO.getOrderSts(), TamConstants.ORD_STS_S)) {
			logger.info("订单已成功");
			throw new LemonException("TAM20011");
		}
		if (!StringUtils.equals(resultTransferDTO.getOrderSts(), TamConstants.ORD_STS_S)) {
			TransferOrderDO updateOrderDO = new TransferOrderDO();
			updateOrderDO.setOrderNo(resultTransferDTO.getOrderNo());
			updateOrderDO.setOrderSts(TamConstants.ORD_STS_F);
			updateOrderDO.setAcTm(resultTransferOrderDTO.getAccDate());
			this.transactionalService.updateTransferOrder(updateOrderDO);
		}
		// 比较金额
		BigDecimal amount = resultTransferDTO.getAmount();
		if (transferDO.getAmount().compareTo(amount) != 0) {
			throw new LemonException("TAM20002");
		}
		BigDecimal amountSum = amount.add(transferDO.getFee());

		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		// 转账到银行卡 先做账务处理 调用资金能力
		if (StringUtils.equals(transferDO.getBusType(), TamConstants.BUS_TYPE_TRANSFER_B)) {
			String acmJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
			acmJrnNo = transferDO.getBusType() + acmJrnNo;
			List<AccountingReqDTO> backUserList = new ArrayList();
			logger.info("转账到银行卡======================账务处理" + acmJrnNo);
			// 借： 其他应付款-暂收-收银台 102
			cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, null, ACMConstants.ITM_AC_TYP,
					balCapType, ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_CSH_PAY, TamConstants.AC_ITEM_CSH_PAY, null,
					null, null, "转账到银行卡");
			// 贷：应付账款-待结算-批量付款 100
			userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, null, ACMConstants.ITM_AC_TYP,
					balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAYMENT,
					TamConstants.AC_ITEM_TAM_PAYMENT, null, null, null, "转账到银行卡");
			backUserList.add(cshItemReqDTO);
			backUserList.add(userAccountReqDTO);
			if (fee.compareTo(BigDecimal.valueOf(0)) > 0) {
				// 贷：手续费收入-支付账户-转账 2
				feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
						transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, fee, null, ACMConstants.ITM_AC_TYP,
						balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_TAM_PAY,
						null, null, null, "转账到银行卡");
				backUserList.add(feeItemReqDTO);
			}
			acmComponent.requestAc(backUserList);
			// 调用资金能力
			logger.info("调用资金能力=======================");
			WithdrawReqDTO withdrawReqDTO = new WithdrawReqDTO();
            withdrawReqDTO.setUserNo(transferDO.getUserId());
			withdrawReqDTO.setCapTyp("1");
			withdrawReqDTO.setCorpBusTyp(CorpBusTyp.WITHDRAW);
			withdrawReqDTO.setCorpBusSubTyp(CorpBusSubTyp.CARD_WITHDRAW);
			withdrawReqDTO.setCcy(transferDO.getOrderCcy());
			withdrawReqDTO.setCapCorg(transferDO.getCrdCorpOrg());
			withdrawReqDTO.setCrdAcTyp(transferDO.getCrdAcTyp());
			withdrawReqDTO.setWcAplAmt(transferDO.getAmount());
			withdrawReqDTO.setCapCrdNm(transferDO.getCapCrdNm());
			withdrawReqDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());
			withdrawReqDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
			withdrawReqDTO.setCrdNoEnc(transferDO.getCrdNoEnc());
			withdrawReqDTO.setPsnCrpFlg("C");
			// 收银订单号
			withdrawReqDTO.setReqOrdNo(transferDO.getOrderNo());
			// 用户信息查询
			GenericRspDTO<UserBasicInfDTO> userInfo = queryUser(transferDO.getUserId());
			UserBasicInfDTO user = userInfo.getBody();
			withdrawReqDTO.setUserNm(user.getUsrNm());
			withdrawReqDTO.setMblNo(user.getMblNo());
			withdrawReqDTO.setWcRmk(transferDO.getRemark());
			withdrawReqDTO.setSubbranch(branchName);
			GenericRspDTO<WithdrawReqDTO> genericDTO = new GenericRspDTO<WithdrawReqDTO>();
			genericDTO.setBody(withdrawReqDTO);
			logger.error("调用资金能力转账申请==========================");
			GenericRspDTO<WithdrawResDTO> createOrder = withdrawClien.createOrder(genericDTO);
			if (JudgeUtils.isNotSuccess(createOrder.getMsgCd())) {
				logger.error("调用资金能力接口失败=================1.回滚账务 =====2.调用收银台更新原订单状态为转账失败 更新账单 ====3.更新转账订单表状态为失败");
				for (int i = 0; i < backUserList.size(); i++) {
					if (JudgeUtils.isNotNull(backUserList.get(i))) {
						backUserList.get(i).setTxSts(ACMConstants.ACCOUNTING_CANCEL);
					}
				}
				logger.info("1.开始时账务回滚============================");
				GenericRspDTO<NoBody> undoRspDTO = acmComponent.requestAc(backUserList);
				if (JudgeUtils.isNotSuccess(undoRspDTO.getMsgCd())) {
					LemonException.throwBusinessException(undoRspDTO.getMsgCd());
				}

				logger.info("2.调用收银台更新原订单状态为转账失败 更新账单 ============================orderNo=" + transferDO.getBusOrderNo()
						+ "===status=" + OrderStatus.FAIL.getValue());
				cshOrderClient.updateOrder(transferDO.getBusOrderNo(), OrderStatus.FAIL.getValue());
				logger.info("3.更新转账订单表状态为失败============================orderNo=" + transferDO.getOrderNo());
				TransferOrderDO transDO = new TransferOrderDO();
				transDO.setOrderNo(transferDO.getOrderNo());
				transDO.setOrderSts(TamConstants.ORD_STS_F);
				transactionalService.updateTransferOrder(transDO);
				throw new LemonException(createOrder.getMsgCd());
			} else {
				// 更新转账订单表状态为成功
				TransferOrderDO transDO = new TransferOrderDO();
				transDO.setOrderNo(transferDO.getOrderNo());
				transDO.setOrderSts(TamConstants.ORD_STS_P);
				transactionalService.updateTransferOrder(transDO);
			}
		} else {
			String balAcNo = acmComponent.getAcmAcNo(transferDO.getCrossUserId(), balCapType);
			List<AccountingReqDTO> acUserList = new ArrayList();
			String acmJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
			acmJrnNo = transferDO.getBusType() + acmJrnNo;
			logger.info("转账到账户======================账务处理" + acmJrnNo);
			// 借:其他应付款-暂收-收银台 100
			cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo, ACMConstants.ITM_AC_TYP,
					balCapType, ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_CSH_PAY, null, null, null, null, "TAM转账");
			// 贷：其他应付款-支付账户-现金账户 100
			userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, balAcNo, ACMConstants.USER_AC_TYP,
					balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "TAM转账");
			acUserList.add(cshItemReqDTO);
			acUserList.add(userAccountReqDTO);
			if (fee.compareTo(BigDecimal.valueOf(0)) > 0) {
				// 贷：手续费收入-支付账户-转账 2
				feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
						transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, fee, null, ACMConstants.ITM_AC_TYP,
						balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_TAM_PAY,
						null, null, null, "TAM转账");
				acUserList.add(feeItemReqDTO);
				logger.info("请求账务处理========================" + transferDO.getOrderNo());
			}
			acmComponent.requestAc(acUserList);
			logger.info("请求账务处理完成=========================");
			TransferOrderDO updateTransfer = new TransferOrderDO();
			updateTransfer.setOrderNo(resultTransferDTO.getOrderNo());
			updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
			updateTransfer.setAmount(resultTransferDTO.getAmount());
			updateTransfer.setTxType(resultTransferDTO.getTxType());
			updateTransfer.setBusType(updateTransfer.getBusType());
			updateTransfer.setAcTm(resultTransferOrderDTO.getAccDate());
			// 更新订单信息
			this.transactionalService.updateTransferOrder(updateTransfer);
			// 消息推送
			sendMessage(transferDO);
		}
		return GenericRspDTO.newSuccessInstance();
	}

	/**
	 * 消息推送
	 * 
	 * @param transferDO
	 */
	public void sendMessage(TransferOrderDO transferDO) {
		logger.info("消息推送========================");
		String payerName=transferDO.getPayerName();
		if(JudgeUtils.isBlank(payerName)){
			Object[] args = new Object[] { };
			payerName= getViewOrderInfo("0", args);
		}
		String gatherName=transferDO.getGatherName();
		if(JudgeUtils.isBlank(gatherName)){
			Object[] args = new Object[] { };
			gatherName= getViewOrderInfo("0", args);
		}
		// 推送消息
		String userId = transferDO.getCrossUserId();
		logger.info("message send to userId : " + userId);

		String language = LemonUtils.getLocale().getLanguage();
		if (StringUtils.isBlank(language)) {
			logger.info("language  ======  " + language);
			language = "en";
		}
		String busType = transferDO.getBusType();
		// 面对面需要透传
		if (StringUtils.equals(busType, TamConstants.BUS_TYPE_TRANSFER_F)) {
			GenericDTO<MessageSendReqDTO> messageReqDTO = new GenericDTO<MessageSendReqDTO>();
			MessageSendReqDTO messageReq = new MessageSendReqDTO();
			messageReq.setUserId(userId);
			messageReq.setMessageTemplateId(TamConstants.SEND_MER_ORDER);
			messageReq.setMessageLanguage(language);
			Map<String, String> map = new HashMap<String, String>();
			map.put("amount", transferDO.getAmount().toString());
			map.put("payerName",payerName);
			map.put("gatherName", gatherName);
			map.put("payerMblNo", transferDO.getPayeMblNo());
			if (StringUtils.equals(transferDO.getBusType(), TamConstants.BUS_TYPE_TRANSFER_B)) {
				map.put("gatherMblNo",transferDO.getMblNo());
			} else {
				map.put("gatherMblNo", transferDO.getMblNo());
			}
			messageReq.setReplaceFieldMap(map);
			messageReqDTO.setBody(messageReq);
			GenericRspDTO<NoBody> rspDto = cmmServerClient.messageSend(messageReqDTO);
		}

		GenericDTO<MessageSendReqDTO> messageDTO = new GenericDTO<MessageSendReqDTO>();
		MessageSendReqDTO message = new MessageSendReqDTO();
		message.setUserId(userId);
		message.setMessageTemplateId(TamConstants.SEND_MER_TO_USER_ORDER);
		message.setMessageLanguage(language);
		Map<String, String> map = new HashMap<String, String>();
		map.put("amount", transferDO.getAmount().toString());
		map.put("payerName",payerName);
		map.put("gatherName", gatherName);
		map.put("payerMblNo", transferDO.getPayeMblNo());
		if (StringUtils.equals(transferDO.getBusType(), TamConstants.BUS_TYPE_TRANSFER_B)) {
			message.setMessageTemplateId(TamConstants.SEND_MER_TO_BANK_ORDER);
			map.put("gatherMblNo", transferDO.getLastCapCrdNo());
		} else {
			map.put("gatherMblNo", transferDO.getLastMblNo());
		}
		message.setReplaceFieldMap(map);
		messageDTO.setBody(message);
		GenericRspDTO<NoBody> rspDto = cmmServerClient.messageSend(messageDTO);
	}

	/**
	 * 国际化商品描述信息
	 * 
	 * @param busType
	 * @param args
	 * @return
	 */
	public String getViewOrderInfo(String busType, Object[] args) {
		try {
			String key = "view.orderinfo." + busType;
			return localeMessageSource.getMessage(key, args);
		} catch (Exception e) {

		}
		return null;
	}

	/**
	 *  商户转账到用户
	 * @param genericMerchantTransferOrderDTO
	 * @return
	 */
	@Override
	public GenericRspDTO<MerchantTransferRspDTO> createMerchantOutOrder(GenericDTO<MerchantTransferOrderDTO> genericMerchantTransferOrderDTO){
		MerchantTransferOrderDTO merchantTransferOrderDTO = genericMerchantTransferOrderDTO.getBody();
		//收款方用户手机号
		String payeeUserMblNo = merchantTransferOrderDTO.getMblNo();
		//付款方商户id
		String merchantId = merchantTransferOrderDTO.getMerchantId();
		String remark = merchantTransferOrderDTO.getRemark();
		BigDecimal transferAmt = merchantTransferOrderDTO.getAmount();
		String busType = merchantTransferOrderDTO.getBusType();
		String orderCcy = merchantTransferOrderDTO.getOrderCcy();
		String extOrderNo = merchantTransferOrderDTO.getExtOrderNo();

		//收款付款方校验
		UserInfoRspDTO crossUserInfo = userInfo(payeeUserMblNo);
		String crossUserId = crossUserInfo.getUserId();
		if (StringUtils.equals(crossUserId, merchantId)) {
			throw new LemonException("TAM20010");
		}
		//订单金额校验
		if (BigDecimal.valueOf(0).compareTo(transferAmt) >= 0) {
			throw new LemonException("TAM10021");
		}
		//外围系统重复下单校验
		if(this.transactionalService.queryByExtOrderNo(extOrderNo).stream().filter(e -> e != null).count() > 0) {
			LemonException.throwBusinessException("TAM20018");
		}
		//收款方风控
		checkUserStatus(crossUserId);
		checkUserStatus(merchantId);

		//商户信息查询
		GenericRspDTO<UserBasicInfDTO> queryUser = queryUser(merchantId);
		UserBasicInfDTO merchantInfo = queryUser.getBody();
		//判断付款方是否是商户 普通用户:0  企业用户:1
		String userLevel = merchantInfo.getUsrLvl();
		if(JudgeUtils.equals(userLevel,"0") || JudgeUtils.equals(userLevel,"1")){
			LemonException.throwBusinessException("TAM10028");
		}
		//创建订单
		TransferOrderDO transferDO = new TransferOrderDO();
		if(JudgeUtils.isNull(orderCcy)) {
			transferDO.setOrderCcy(TamConstants.QP_PAY_CCY);
		}else{
			transferDO.setOrderCcy(orderCcy);
		}
		transferDO.setPayerName(merchantInfo.getMercName());
		transferDO.setPayeMblNo(merchantInfo.getMblNo());
		transferDO.setGatherName(crossUserInfo.getUsrNm());
		transferDO.setCrossUserId(crossUserId);
		transferDO.setAmount(transferAmt);
		transferDO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		//设置外围系统业务订单号
		transferDO.setExtOrderNo(extOrderNo);
		transferDO.setBusType(busType);
		transferDO.setUserId(merchantInfo.getUserId());
		transferDO.setMblNo(crossUserInfo.getMblNo());
		transferDO.setRemark(remark);
		transferDO.setOrderSts(TamConstants.ORD_STS_U);
		String ymd = DateTimeUtils.getCurrentDateStr();
		String orderNo = TamConstants.BUS_TYPE_M_TRANSFER_U + ymd
				+ IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 12);
		transferDO.setOrderNo(orderNo);
		transferDO.setAcTm(genericMerchantTransferOrderDTO.getAccDate());
		transferDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());

		String mblNo = transferDO.getMblNo();
		if (mblNo.length() > 4) {
			String mblNoStr = mblNo.substring(mblNo.length() - 4, mblNo.length());
			String mblNoDesc = mblNo.substring(mblNo.length() - 2, mblNo.length());
			String topMblNoStr = mblNo.substring(mblNo.indexOf("-") + 1, mblNo.length());
			String topMblNo = topMblNoStr.substring(0, 3);
			String mblNoHid = topMblNo + "****" + mblNoStr;
			transferDO.setMblNoHid(mblNoHid);
			transferDO.setLastMblNo(mblNoStr);
			String goodesc=topMblNo+"***"+mblNoDesc;
			Object[] args = new Object[] { goodesc };
			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
			transferDO.setGoodDesc(descStr);
		} else {
			Object[] args = new Object[] { mblNo };
			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
			transferDO.setGoodDesc(descStr);
			transferDO.setLastMblNo(mblNo);
		}
		// 填充查询手续费数据
		GenericDTO<MerchantFeeCalculateReqDTO> tradeFeeReq = new GenericDTO<MerchantFeeCalculateReqDTO>();
		MerchantFeeCalculateReqDTO tradeFeeReqDTO = new MerchantFeeCalculateReqDTO();
		tradeFeeReqDTO.setTradeAmt(transferDO.getAmount());
		//业务类型0304 商户转账到用户
		tradeFeeReqDTO.setBusType(TamConstants.BUS_TYPE_M_TRANSFER_U);
		tradeFeeReqDTO.setCcy(transferDO.getOrderCcy());
		tradeFeeReqDTO.setTradeAmt(transferDO.getAmount());
		tradeFeeReqDTO.setUserId(transferDO.getUserId());
		tradeFeeReq.setBody(tradeFeeReqDTO);
		logger.info("开始调用商户手续费预算===============" + transferDO.getOrderNo());
		// 调用tfm接口查询手续费
		GenericRspDTO<MerchantFeeCalculateRspDTO> tradeGenericRspDTO = tfmServerClient.merchanFeeCalculate(tradeFeeReq);
		if (JudgeUtils.isNotSuccess(tradeGenericRspDTO.getMsgCd())) {
			logger.error("调用计费失败");
			throw new LemonException(tradeGenericRspDTO.getMsgCd());
		}
		// 商户手续费
		BigDecimal fee = tradeGenericRspDTO.getBody().getTradeFee();
		String feeFlag = tradeGenericRspDTO.getBody().getCalculateMode();
		if(JudgeUtils.equals(feeFlag,"internal")){
			transferAmt = transferAmt.subtract(fee);
		}else if(JudgeUtils.equals(feeFlag,"external")){
			transferAmt = transferAmt.add(fee);
		}
		transferDO.setFee(fee);
		// 生成转账订单
		this.transactionalService.createOrder(transferDO);

		// 调用收银台 直付接口 进行转账
		TransferPaymentDTO transferPaymentDTO = new TransferPaymentDTO();
		transferPaymentDTO.setMerchantId(merchantId);
		transferPaymentDTO.setBusOrderNo(transferDO.getOrderNo());
		transferPaymentDTO.setOrderCcy(transferDO.getOrderCcy());
		transferPaymentDTO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		transferPaymentDTO.setBusType(TamConstants.BUS_TYPE_M_TRANSFER_U);
		transferPaymentDTO.setRemark(remark);
		transferPaymentDTO.setSysChannel("OTHER");
		transferPaymentDTO.setPayerId(transferDO.getUserId());
		//截取手机号尾号后4位
		if(payeeUserMblNo.length() > 4){
			transferPaymentDTO.setMblNo(payeeUserMblNo.substring(payeeUserMblNo.length() - 4, payeeUserMblNo.length()));
		}else{
			transferPaymentDTO.setMblNo(payeeUserMblNo);
		}
		transferPaymentDTO.setPayerName(transferDO.getPayerName());
		transferPaymentDTO.setPayeeId(transferDO.getCrossUserId());
		transferPaymentDTO.setAppCnl(LemonUtils.getApplicationName());
		transferPaymentDTO.setBusPaytype(TamConstants.BUS_PAY_TYPE);
		transferPaymentDTO.setGoodsDesc(transferDO.getGoodDesc());
		// 支付金额 =转账金额+(-)手续费
		transferPaymentDTO.setCashAmt(transferAmt);
		transferPaymentDTO.setOrderAmt(transferDO.getAmount());
		logger.info("订单：" + transferDO.getOrderNo() + " 请求收银台");
		GenericDTO<TransferPaymentDTO> genericTransferPayment = new GenericDTO<TransferPaymentDTO>();
		genericTransferPayment.setBody(transferPaymentDTO);
		GenericRspDTO<PaymentResultDTO> rspDTO = cshOrderClient.merchantTransferHandler(genericTransferPayment);
		if (!JudgeUtils.isSuccess(rspDTO.getMsgCd())) {
			logger.error("调用收银台商户转账接口失败");
			throw new LemonException(rspDTO.getMsgCd());
		}

		PaymentResultDTO paymentResultDTO = rspDTO.getBody();
		// 订单号
		String orderId = paymentResultDTO.getBusOrderNo();
		TransferOrderDO transferDB = this.transactionalService.tansferInfo(orderId);
		// 原订单不存在
		if (JudgeUtils.isNull(transferDB)) {
			throw new LemonException("TAM20001");
		}
		// 原订单状态已经成功
		if (StringUtils.equals(transferDB.getOrderSts(), TamConstants.ORD_STS_S)) {
			logger.info("该笔订单状态已为成功状态");
			throw new LemonException("TAM20011");
		}

		BigDecimal orderAmount = transferDB.getAmount();
		BigDecimal amountSum = orderAmount.add(fee);
		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		List<AccountingReqDTO> acUserList = new ArrayList();
		// 流水号
		String payJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
		payJrnNo = transferDO.getBusType() + payJrnNo;
		// 资金类型
		String balCapTypeUser = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		String balCapTypeSettle = CapTypEnum.CAP_TYP_SETTLEMENT.getCapTyp();
		// 对手方用户id
		String balAcNo = acmComponent.getAcmAcNo(crossUserId, balCapTypeUser);
		// 借:其他应付款-暂收-收银台 100
		cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), payJrnNo, transferDO.getTxType(),
				ACMConstants.ACCOUNTING_NOMARL, amountSum, balAcNo, ACMConstants.ITM_AC_TYP, balCapTypeSettle,
				ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_CSH_PAY, null, null, null, null, "商户"+merchantId+"转账手机号:" + payeeUserMblNo);
		// 贷：其他应付款-支付账户-现金账户 100
		userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), payJrnNo,
				transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, transferDO.getAmount(), balAcNo, ACMConstants.USER_AC_TYP,
				balCapTypeUser, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_PAY_BAL, null, null, null, null, "商户"+merchantId+"转账手机号:" + payeeUserMblNo);
		acUserList.add(cshItemReqDTO);
		acUserList.add(userAccountReqDTO);
		if (fee.compareTo(BigDecimal.valueOf(0)) > 0) {
			// 贷：手续费收入-支付账户-转账 2
			feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), payJrnNo,
					transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, fee, null, ACMConstants.ITM_AC_TYP, null,
					ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_TAM_PAY, null, null, null,
					"商户"+merchantId+"转账手机号:" + payeeUserMblNo);
			acUserList.add(feeItemReqDTO);
		}
		acmComponent.requestAc(acUserList);
		logger.debug("请求账务处理完成=========================");

		// 账务成功更新订单状态
		TransferOrderDO updateTransfer = new TransferOrderDO();
		updateTransfer.setOrderNo(paymentResultDTO.getBusOrderNo());
		updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
		//设置收银订单号
		updateTransfer.setBusOrderNo(paymentResultDTO.getOrderNo());
		updateTransfer.setAmount(paymentResultDTO.getOrderAmt());
		updateTransfer.setGoodDesc(paymentResultDTO.getGoodsDesc());
		// 更新订单信息
		this.transactionalService.updateTransferOrder(updateTransfer);

		// 组装对外传输数据
		GenericRspDTO<MerchantTransferRspDTO> merchantRsp = new GenericRspDTO<MerchantTransferRspDTO>();
		MerchantTransferRspDTO merchantOut = new MerchantTransferRspDTO();
		merchantOut.setAmount(paymentResultDTO.getOrderAmt());
		merchantOut.setFee(fee);
		merchantOut.setOrderSts(TamConstants.ORD_STS_S);
		merchantOut.setOrderNo(updateTransfer.getBusOrderNo());
		merchantOut.setExtOrderNo(transferDO.getExtOrderNo());
		merchantOut.setMblNo(payeeUserMblNo);
		merchantOut.setTradeDate(DateTimeUtils.getCurrentDateStr("yyyy-MM-dd"));
		merchantOut.setTradeTime(DateTimeUtils.getCurrentDateTimeStr("yyyy-MM-dd HH:mm:ss"));
		try{
			// 消息推送
			sendMessage(transferDO);
		} catch (LemonException e){
			logger.error("商户转账用户订单号:" + orderNo +" ,消息信息推送失败");
		}

		return merchantRsp.newSuccessInstance(merchantOut);
	}

	/**
	 * 转账到ACLEDA银行卡账户
	 * @param cardTransferOrderDTO
	 * @return
	 */
	@Override
	public GenericRspDTO<AcledaCardTransferOrderRspDTO> createAcledaCardOrder(GenericDTO<AcledaCardTransferOrderReqDTO> cardTransferOrderDTO) {
		AcledaCardTransferOrderReqDTO acledaCardReq = cardTransferOrderDTO.getBody();
		String userId = LemonUtils.getUserId();

		////创建转账订单
		TransferOrderDO transferDO = new TransferOrderDO();
		BigDecimal amount = acledaCardReq.getAmount();
		if (BigDecimal.valueOf(0).compareTo(amount) >= 0) {
			throw new LemonException("TAM10021");
		}
		transferDO.setOrderCcy(TamConstants.QP_PAY_CCY);
		if (!JudgeUtils.isNull(acledaCardReq.getOrderCcy())) {
			transferDO.setOrderCcy(acledaCardReq.getOrderCcy());
		}
		transferDO.setUserId(userId);
		String ymd = DateTimeUtils.getCurrentDateStr();
		String orderNo = TamConstants.BUS_TYPE_TRANSFER_B + ymd
				+ IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 11);
		transferDO.setOrderNo(orderNo);
		String sysChannel = acledaCardReq.getSysChannel();
		if (JudgeUtils.isBlank(sysChannel)) {
			sysChannel = "APP";
		}
		transferDO.setSysChannel(sysChannel);
		// 根据用户编号查询 用户名字 手机号码
		GenericRspDTO<UserBasicInfDTO> userInfo = queryUser(userId);
		UserBasicInfDTO user = userInfo.getBody();
		transferDO.setPayerName(user.getUsrNm());
		transferDO.setPayeMblNo(user.getMblNo());
		transferDO.setTxType(TamConstants.TX_TYPE_TRANSFER);
		transferDO.setBusType(acledaCardReq.getBusType());
		transferDO.setAmount(acledaCardReq.getAmount());
		transferDO.setOrderSts(TamConstants.ORD_STS_U);
		transferDO.setRemark(acledaCardReq.getRemark());
		transferDO.setCrdAcTyp(acledaCardReq.getCrdAcTyp());
		transferDO.setCrdCorpOrg(acledaCardReq.getCapCorg());
		transferDO.setCorpOrgSnm(acledaCardReq.getCorpOrgSnm());
		transferDO.setCapCrdNm(acledaCardReq.getCapCrdNm());
		transferDO.setCapCrdNo(acledaCardReq.getCapCrdNo());
		transferDO.setGatherName(acledaCardReq.getCapCrdNm());
		transferDO.setCapCorgNm(acledaCardReq.getCapCorgNm());
		transferDO.setCrdNoEnc("");
		Object[] args = new Object[]{"ACLEDA"};
		String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_B, args);
		transferDO.setGoodDesc(descStr);
		transferDO.setAcTm(cardTransferOrderDTO.getAccDate());
		transferDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
		this.transactionalService.createOrder(transferDO);

		//// 调用收银,获取支付交易ID
		GenericDTO<AcledaTransferReqDTO> genericDTO = new GenericDTO<>();
		AcledaTransferReqDTO acledaTransferReqDTO = new AcledaTransferReqDTO();
		BeanUtils.copyProperties(acledaTransferReqDTO, acledaCardReq);
		//设置请求值
		acledaTransferReqDTO.setDirection(acledaCardReq.getDirection());
		acledaTransferReqDTO.setGoodsDesc(descStr);
		acledaTransferReqDTO.setPayerId(transferDO.getUserId());
		acledaTransferReqDTO.setAppCnl(LemonUtils.getApplicationName());
		acledaTransferReqDTO.setBusType(TamConstants.BUS_TYPE_TRANSFER_B);
		acledaTransferReqDTO.setCrdPayAmt(transferDO.getAmount().add(transferDO.getFee()));
		//acledaQpPaymentDTO.setEffTm();
		acledaTransferReqDTO.setOrdCcy(transferDO.getOrderCcy());
		acledaTransferReqDTO.setMblNo(transferDO.getMblNo());

		genericDTO.setBody(acledaTransferReqDTO);
		GenericRspDTO<AcledaTransferRspDTO> rspDTO = cshOrderClient.acledaTransferOpen(genericDTO);
		AcledaTransferRspDTO acledaTransferRspDTO = rspDTO.getBody();

		////调用CPO下单
		// 账务处理
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		AccountingReqDTO feeItemReqDTO = null; // 手续费账务对象
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		String feeFlag = acledaTransferRspDTO.getFeeFlag();
		BigDecimal amountSum = BigDecimal.ZERO;
		BigDecimal transferFee = transferDO.getFee();

		//根据扣费标识处理账务金额
		if(JudgeUtils.equalsIgnoreCase("IN",feeFlag)){
			amountSum = transferDO.getAmount().subtract(transferFee);
		}else if(JudgeUtils.equalsIgnoreCase("EX",feeFlag)){
			amountSum = transferDO.getAmount().add(transferFee);
		}
		// 转账到银行卡 先做账务处理 调用资金能力
		if (StringUtils.equals(transferDO.getBusType(), TamConstants.BUS_TYPE_TRANSFER_B)) {

			// 调用资金能力
			logger.info("调用资金能力=======================");
			WithdrawReqDTO withdrawReqDTO = new WithdrawReqDTO();
			withdrawReqDTO.setUserNo(transferDO.getUserId());
			withdrawReqDTO.setCapTyp("1");
			withdrawReqDTO.setCorpBusTyp(CorpBusTyp.WITHDRAW);
			withdrawReqDTO.setCorpBusSubTyp(CorpBusSubTyp.CARD_WITHDRAW);
			withdrawReqDTO.setCcy(transferDO.getOrderCcy());
			withdrawReqDTO.setCapCorg(transferDO.getCrdCorpOrg());
			withdrawReqDTO.setCrdAcTyp(transferDO.getCrdAcTyp());
			withdrawReqDTO.setWcAplAmt(transferDO.getAmount());
			withdrawReqDTO.setCapCrdNm(transferDO.getCapCrdNm());
			withdrawReqDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());
			withdrawReqDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
			withdrawReqDTO.setCrdNoEnc(transferDO.getCrdNoEnc());
			withdrawReqDTO.setPsnCrpFlg("C");
			// 收银订单号
			withdrawReqDTO.setReqOrdNo(acledaTransferRspDTO.getOrderNo());
			// 用户信息查询
			withdrawReqDTO.setUserNm(user.getUsrNm());
			withdrawReqDTO.setMblNo(user.getMblNo());
			withdrawReqDTO.setWcRmk(transferDO.getRemark());
			withdrawReqDTO.setSubbranch("");
			GenericRspDTO<WithdrawReqDTO> genericWithdrawDTO = new GenericRspDTO<WithdrawReqDTO>();
			genericWithdrawDTO.setBody(withdrawReqDTO);
			logger.error("调用资金能力转账申请==========================");
			GenericRspDTO<WithdrawResDTO> createOrder = withdrawClien.createOrder(genericWithdrawDTO);
			if (JudgeUtils.isNotSuccess(createOrder.getMsgCd())) {
				logger.error("调用资金能力接口失败 1. 冲正收银台账务 2.调用收银台更新原订单状态为转账失败 更新账单 ====3.更新转账订单表状态为失败");
				logger.info("1. 冲正收银台账务");
				//冲正收银台账务
				List<AcledaTransferRspDTO.AccountReqDTO> accList = acledaTransferRspDTO.getAccountReqList();
				AccountingReqDTO account1 = new AccountingReqDTO();
				BeanUtils.copyProperties(account1,accList.get(0));
				account1.setTxSts("C");
				AccountingReqDTO account2 = new AccountingReqDTO();
				BeanUtils.copyProperties(account1,accList.get(1));
				account1.setTxSts("C");
				List<AccountingReqDTO> cancelAccountList = new ArrayList<>();
				cancelAccountList.add(account1);
				cancelAccountList.add(account2);
				acmComponent.requestAc(cancelAccountList);

				logger.info("2.调用收银台更新原订单状态为转账失败 更新账单 ============================orderNo=" + transferDO.getBusOrderNo()
						+ "===status=" + OrderStatus.FAIL.getValue());
				cshOrderClient.updateOrder(transferDO.getBusOrderNo(), OrderStatus.FAIL.getValue());
				logger.info("3.更新转账订单表状态为失败============================orderNo=" + transferDO.getOrderNo());
				TransferOrderDO transDO = new TransferOrderDO();
				transDO.setOrderNo(transferDO.getOrderNo());
				transDO.setOrderSts(TamConstants.ORD_STS_F);
				transactionalService.updateTransferOrder(transDO);
				throw new LemonException(createOrder.getMsgCd());
			} else {

				//账务处理
				String acmJrnNo = IdGenUtils.generateIdWithDate(TamConstants.ORD_GEN_PRE, 14);
				acmJrnNo = transferDO.getBusType() + acmJrnNo;
				List<AccountingReqDTO> backUserList = new ArrayList();
				logger.info("转账到银行卡======================账务处理" + acmJrnNo);
				// 借： 其他应付款-暂收-收银台 102
				cshItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
						transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amountSum, null, ACMConstants.ITM_AC_TYP,
						balCapType, ACMConstants.AC_D_FLG, TamConstants.AC_ITEM_CSH_PAY, TamConstants.AC_ITEM_CSH_PAY, null,
						null, null, "转账到ACLEDA银行卡");
				// 贷：应付账款-待结算-批量付款 100
				userAccountReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
						transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, amount, null, ACMConstants.ITM_AC_TYP,
						balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAYMENT,
						TamConstants.AC_ITEM_TAM_PAYMENT, null, null, null, "转账到银行卡");
				backUserList.add(cshItemReqDTO);
				backUserList.add(userAccountReqDTO);
				if (transferFee.compareTo(BigDecimal.valueOf(0)) > 0) {
					// 贷：手续费收入-支付账户-转账 2
					feeItemReqDTO = acmComponent.createAccountingReqDTO(transferDO.getOrderNo(), acmJrnNo,
							transferDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, transferFee, null, ACMConstants.ITM_AC_TYP,
							balCapType, ACMConstants.AC_C_FLG, TamConstants.AC_ITEM_TAM_PAY, TamConstants.AC_ITEM_TAM_PAY,
							null, null, null, "转账到ACLEDA银行卡");
					backUserList.add(feeItemReqDTO);
				}
				acmComponent.requestAc(backUserList);

				// 更新转账订单表状态为成功
				TransferOrderDO transDO = new TransferOrderDO();
				transDO.setOrderNo(transferDO.getOrderNo());
				transDO.setOrderSts(TamConstants.ORD_STS_P);
				transactionalService.updateTransferOrder(transDO);
			}
		}
		if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
			LemonException.throwBusinessException(rspDTO.getMsgCd());
		}

		//更新转账订单
		TransferOrderDO updateOrder = new TransferOrderDO();
		updateOrder.setOrderNo(transferDO.getOrderNo());
		updateOrder.setBusOrderNo(acledaTransferRspDTO.getOrderNo());
		updateOrder.setExtOrderNo(acledaTransferRspDTO.getSessionId());
		updateOrder.setOrderSts(TamConstants.ORD_STS_P);
		this.transactionalService.updateTransferOrder(updateOrder);

		AcledaCardTransferOrderRspDTO resultTransferDTO = new AcledaCardTransferOrderRspDTO();
		BeanUtils.copyProperties(resultTransferDTO,acledaTransferRspDTO);
		GenericRspDTO<AcledaCardTransferOrderRspDTO> rspAcledaTransferGenericDTO = new GenericRspDTO<>();
		rspAcledaTransferGenericDTO.setBody(resultTransferDTO);
		return rspAcledaTransferGenericDTO;
	}

	@Override
	public GenericRspDTO<NoBody> acledaCardSuccessNotify(GenericDTO<AcledaCardTransferNotifyDTO> cardTransferNotifyDTO){
		AcledaCardTransferNotifyDTO acledaTransferDTO = cardTransferNotifyDTO.getBody();

		//更新转账订单状态
		List<TransferOrderDO> resultList = this.transactionalService.queryByExtOrderNo(acledaTransferDTO.getSessionId());
		TransferOrderDO updateTransfer = resultList.get(0);
		updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
		transactionalService.updateTransferOrder(updateTransfer);

		//更新cpo提现订单状态
		GenericDTO<WdcProcessReqDTO> genericDTO = new GenericDTO<>();
		WdcProcessReqDTO wdcProcessReqDTO = new WdcProcessReqDTO();
		wdcProcessReqDTO.setWcOrdNo(acledaTransferDTO.getOrderNo());
		wdcProcessReqDTO.setOrdSts(CpoConstants.ORD_SUCCESS);
		wdcProcessReqDTO.setReason("ACLEDA:" + acledaTransferDTO.getPaymentTokenId() +" | " + acledaTransferDTO.getSessionId());
		genericDTO.setBody(wdcProcessReqDTO);
		GenericRspDTO<NoBody> rspDTO = withdrawClien.payOrder(genericDTO);
		if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
			logger.info("ACLEDA 银行转账通知资金流出处理失败...");
			LemonException.throwBusinessException(rspDTO.getMsgCd());
		}
		return GenericRspDTO.newInstance();
	}

	/**
	 * 数币转账
	 *
	 * @param dmOutTransferOrderDTO
	 * @return
	 */
	@Override
//	@Transactional
	public GenericRspDTO<DmTransferRspDTO> createDmOutOrder(GenericDTO<DmOutTransferOrderDTO> dmOutTransferOrderDTO) {

		DmOutTransferOrderDTO transferOrder = dmOutTransferOrderDTO.getBody();
		BigDecimal amount = transferOrder.getAmount();
		BigDecimal reqFee = transferOrder.getFee();
		String skAddress = transferOrder.getMblAddress();

		//判断收款方地址是否存在表中并且是可用状态
		GenericRspDTO<DmAccountAddressRspDTO> booleanGenericRspDTO = acmManageClient.getByAddress(skAddress);
		if (JudgeUtils.isNotSuccess(booleanGenericRspDTO.getMsgCd())) {
			LemonException.throwBusinessException(booleanGenericRspDTO.getMsgCd());
		}
		DmAccountAddressRspDTO skDmAcc = booleanGenericRspDTO.getBody();
		String busType = "";
		String skUserId = "";
		String gatherName = "";
		String fkUserId = "";
		DmAccountDetailRspDTO fkAcBalRspDTO = null;

		//获取转账类型
		GenericDTO<GetDmSkAddrTypeReqDTO> skAddrTypeDTO = new GenericDTO<>();
		GetDmSkAddrTypeReqDTO getDmSkAddrTypeReqDTO = new GetDmSkAddrTypeReqDTO();
		getDmSkAddrTypeReqDTO.setAddress(skAddress);
		skAddrTypeDTO.setBody(getDmSkAddrTypeReqDTO);
		GenericRspDTO<String> dmSkAddrType = this.getDmSkAddrType(skAddrTypeDTO);
		if (JudgeUtils.isNotSuccess(dmSkAddrType.getMsgCd())) {
			LemonException.throwBusinessException(dmSkAddrType.getMsgCd());
		}

		TransferOrderDO transferDO = new TransferOrderDO();
		//转账类型
		String tranType = dmSkAddrType.getBody();
		if (JudgeUtils.equals(DmSkAddrTypeEnum.PLATFORM_INTERNAL.getDescription(), tranType)) {
			//站内转账
			busType = BussinessType.DM_TRANSFER.getValue();
			logger.info("acNo:{}", skDmAcc.getAcmAcNo());
			transferOrder.setMblNo(skDmAcc.getAcmAcNo());
			transferDO.setSkAcNo(skDmAcc.getAcmAcNo());

			// 校验双方数币账号是否为相同币种、网络等
			HashMap<String, DmAccountDetailRspDTO> map = checkDmAccount(transferOrder);
			fkAcBalRspDTO = map.get("fkAcBalRspDTO");
			DmAccountDetailRspDTO skAcBalRspDTO = map.get("skAcBalRspDTO");

			// 查询 用户id
			//付款方 userId
			fkUserId = fkAcBalRspDTO.getUserId();
			logger.info("付款方 userId: " + fkUserId);

			//收款方 userId
			skUserId = skAcBalRspDTO.getUserId();

			// 根据用户编号查询 用户名字 手机号码
			GenericRspDTO<UserBasicInfDTO> queryUser = queryUser(skUserId);
			UserBasicInfDTO user = queryUser.getBody();
			gatherName = user.getUsrNm();
			logger.info("收款方 userId: " + skUserId);
		} else if (JudgeUtils.equals(DmSkAddrTypeEnum.WHITELIST_ONCHAIN.getDescription(), tranType)) {
			//链上白名单链上转账
			busType = BussinessType.DM_TRANSFER03.getValue();
			// 根据付款账号获取付款用户id
			fkAcBalRspDTO = getUserIdByFkAcNo(transferOrder.getUserMblNo(), transferOrder.getAmount());
			fkUserId = fkAcBalRspDTO.getUserId();
			logger.info("付款方 userId: " + fkUserId);
		} else {
			//链上转账
			busType = BussinessType.DM_TRANSFER02.getValue();
			// 根据付款账号获取付款用户id
			fkAcBalRspDTO = getUserIdByFkAcNo(transferOrder.getUserMblNo(), transferOrder.getAmount());
			fkUserId = fkAcBalRspDTO.getUserId();
			logger.info("付款方 userId: " + fkUserId);

			//校验交易材料
			List<String> fileUrl = transferOrder.getFileUrl();
			if (JudgeUtils.isNull(fileUrl) || JudgeUtils.isEmpty(fileUrl)) {
				logger.error("数币链上转账,交易材料不能为空, skAddress: {}", skAddress);
				LemonException.throwBusinessException("TAM30009");
			}
			if (fileUrl.size() > 10) {
				logger.error("数币链上转账,交易材料数量超过最大限制(10), skAddress: {}, fileCount: {}", skAddress, fileUrl.size());
				LemonException.throwBusinessException("TAM30010");
			}
		}


		transferDO.setOrderCcy(transferOrder.getOrderCcy());
		transferDO.setCrossUserId(skUserId);

		transferDO.setGatherName(gatherName);

		GenericRspDTO<UserBasicInfDTO> queryPayUser = queryUser(fkUserId);
		UserBasicInfDTO queryPay = queryPayUser.getBody();

		transferDO.setPayerName(queryPay.getUsrNm());
		transferDO.setPayeMblNo(queryPay.getMblNo());
		transferDO.setFkAcNo(transferOrder.getUserMblNo());
		transferDO.setAmount(amount);
		transferDO.setTxType(TamConstants.TX_TYPE_DM_TRANSFER);
		transferDO.setBusType(busType);
		transferDO.setUserId(fkUserId);
		transferDO.setMblNo(transferOrder.getMblAddress() != null ? transferOrder.getMblAddress() : transferOrder.getMblNo());
		transferDO.setRemark(transferOrder.getRemark());
		transferDO.setOrderSts(TamConstants.ORD_STS_U);
		String ymd = DateTimeUtils.getCurrentDateStr();
		String orderNo = busType + ymd
				+ IdGenUtils.generateId(TamConstants.ORD_GEN_PRE + ymd, 12);
		transferDO.setOrderNo(orderNo);
		transferDO.setAcTm(dmOutTransferOrderDTO.getAccDate());
		transferDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());

//		String mblNo = transferDO.getMblNo();
//		if (mblNo.length() > 4) {
//			String mblNoStr = mblNo.substring(mblNo.length() - 4, mblNo.length());
//			String mblNoDesc = mblNo.substring(mblNo.length() - 2, mblNo.length());
//			String topMblNoStr = mblNo.substring(mblNo.indexOf("-") + 1, mblNo.length());
//			String topMblNo = topMblNoStr.substring(0, 3);
//			String mblNoHid = topMblNo + "****" + mblNoStr;
//			transferDO.setMblNoHid(mblNoHid);
//			transferDO.setLastMblNo(mblNoStr);
//			String goodesc=topMblNo+"***"+mblNoDesc;
//			Object[] args = new Object[] { goodesc };
//			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
//			transferDO.setGoodDesc(descStr);
//		} else {
//			Object[] args = new Object[] { mblNo };
//			String descStr = getViewOrderInfo(TamConstants.BUS_TYPE_TRANSFER_U, args);
//			transferDO.setGoodDesc(descStr);
//			transferDO.setLastMblNo(mblNo);
//		}
		// 填充查询手续费数据
		GenericDTO<TradeFeeReqDTO> TradeFeeReq = new GenericDTO<>();
		TradeFeeReqDTO tradeFeeReqDTO = new TradeFeeReqDTO();
		tradeFeeReqDTO.setBusOrderNo(transferDO.getOrderNo());
		tradeFeeReqDTO.setBusOrderTime(DateTimeUtils.getCurrentLocalDateTime());
		tradeFeeReqDTO.setBusType(busType);
		tradeFeeReqDTO.setCcy(transferDO.getOrderCcy());
		tradeFeeReqDTO.setTradeAmt(transferDO.getAmount());
		tradeFeeReqDTO.setUserId(transferDO.getUserId());
		TradeFeeReq.setBody(tradeFeeReqDTO);
		logger.info("开始调用计费===============" + transferDO.getOrderNo());
		// 调用tfm接口查询手续费
		GenericRspDTO<TradeFeeRspDTO> tradeGenericRspDTO = tfmServerClient.tradeFee(TradeFeeReq);

		if (JudgeUtils.isNotSuccess(tradeGenericRspDTO.getMsgCd())) {
			logger.error("调用计费失败");
			throw new LemonException(tradeGenericRspDTO.getMsgCd());
		}
		TradeFeeRspDTO feeRspDTO = tradeGenericRspDTO.getBody();
		// 手续费
		BigDecimal fee = feeRspDTO.getTradeFee();
		// 交易金额
		BigDecimal tradeAmt = feeRspDTO.getTradeAmt();
		if (reqFee.compareTo(fee) != 0) {
			logger.info("手续费不一致,请求的手续费:{},计算的手续费:{}", reqFee, fee);
			throw new LemonException("PWM30006");
		}
		tradeGenericRspDTO.getBody().getCalculateMode();
		transferDO.setFee(fee);
		transferDO.setActualTradeAmt(tradeAmt);
		// 生成转账订单
		this.transactionalService.createOrder(transferDO);

		//扣除手续费应大于0
		if (transferOrder.getAmount().subtract(fee).compareTo(BigDecimal.ZERO) <= 0) {
			logger.info("转账金额:{},手续费:{},扣除手续费金额:{}", transferOrder.getAmount(),
					fee, transferOrder.getAmount().subtract(fee));
			LemonException.throwBusinessException("PWM10020");
		}

		// 调用收银台 直付接口
		DirectPaymentDTO directPaymentDTO = new DirectPaymentDTO();
		directPaymentDTO.setExtOrderNo(transferDO.getOrderNo());
		directPaymentDTO.setOrderCcy(transferDO.getOrderCcy());
		directPaymentDTO.setOrderAmt(transferDO.getAmount());
		directPaymentDTO.setTxType(TamConstants.TX_TYPE_DM_TRANSFER);
		directPaymentDTO.setBusType(busType);
		directPaymentDTO.setSysChannel("OTHER");
		directPaymentDTO.setPayerId(transferDO.getUserId());
		directPaymentDTO.setPayerName(transferDO.getPayerName());
		directPaymentDTO.setPayeeId(transferDO.getCrossUserId());
		directPaymentDTO.setPayPassword(transferOrder.getPayPassword());
		directPaymentDTO.setAppCnl(LemonUtils.getApplicationName());
		directPaymentDTO.setBusPaytype(TamConstants.BUS_PAY_TYPE);
		directPaymentDTO.setGoodsDesc(transferDO.getGoodDesc());
		directPaymentDTO.sethCouponAmt(0);
		directPaymentDTO.setCashAmt(tradeAmt);
		directPaymentDTO.setFileUrl(transferOrder.getFileUrl());
		directPaymentDTO.setFkAcNo(transferOrder.getUserMblNo());
		directPaymentDTO.setRemark(JudgeUtils.isBlank(transferOrder.getRemark()) ? "" : transferOrder.getRemark());
		directPaymentDTO.setToAcNo(skAddress);
		logger.info("订单：{} 请求收银台", transferDO.getOrderNo());
		GenericDTO<DirectPaymentDTO> directPayment = new GenericDTO<DirectPaymentDTO>();
		directPayment.setBody(directPaymentDTO);
		GenericRspDTO<PaymentResultDTO> rspDTO = cshOrderClient.directDm(directPayment);
		if (!JudgeUtils.isSuccess(rspDTO.getMsgCd())) {
			logger.error("调用收银台后台直付接口失败");
			throw new LemonException(rspDTO.getMsgCd());
		}
		PaymentResultDTO paymentResultDTO = rspDTO.getBody();

		// 调用收银台接口成功 更新订单信息
		TransferOrderDO updateTransfer = new TransferOrderDO();
		updateTransfer.setBusOrderNo(paymentResultDTO.getOrderNo());
		updateTransfer.setOrderNo(paymentResultDTO.getBusOrderNo());
		// 更新订单信息
		transactionalService.updateTransferOrder(updateTransfer);

		String orderSts = null;
		if (JudgeUtils.equals(BussinessType.DM_TRANSFER.getValue(), busType) ||
				JudgeUtils.equals(BussinessType.DM_TRANSFER03.getValue(), busType)) {
			//站内或链上白名单转账,调用 Cregis 进行转账
			//组装cregis请求体
			PaymentReqDTO paymentReqDTO = new PaymentReqDTO();
			List<String> includes = new ArrayList<>();
			paymentReqDTO.setOrderId(paymentResultDTO.getOrderNo());
			paymentReqDTO.setCoinId(transferOrder.getOrderCcy());
			paymentReqDTO.setNetwork(fkAcBalRspDTO.getNetwork());
			paymentReqDTO.setLang(TamConstants.lang);
			paymentReqDTO.setFundFlowCode(fundFlowCode);
			includes.add(fkAcBalRspDTO.getAddress());
			paymentReqDTO.setIncludes(includes);
			PaymentPrepareReqDTO.PayTo[] payToList = new PaymentPrepareReqDTO.PayTo[2];
			payToList[0] = new PaymentPrepareReqDTO.PayTo();
			payToList[0].setTo(transferOrder.getMblAddress());
			payToList[0].setReadableAmount(amount.subtract(fee));

			GenericDTO<GetDmPlatAddrReqDTO> dtoGenericDTO = new GenericDTO<>();
			GetDmPlatAddrReqDTO getDmPlatAddrReqDTO = new GetDmPlatAddrReqDTO();
			getDmPlatAddrReqDTO.setNetwork(fkAcBalRspDTO.getNetwork());
			getDmPlatAddrReqDTO.setCcy(fkAcBalRspDTO.getCoinId());
			getDmPlatAddrReqDTO.setType(TamConstants.F_TYPE);
			dtoGenericDTO.setBody(getDmPlatAddrReqDTO);
			GenericRspDTO<GetDmPlatAddrRspDTO> dmPlatAddr = getDmPlatAddr(dtoGenericDTO);
			if (JudgeUtils.isNotSuccess(dmPlatAddr.getMsgCd()) || JudgeUtils.isNull(dmPlatAddr.getBody())) {
				LemonException.throwLemonException("CPI80003");
			}
			payToList[1] = new PaymentPrepareReqDTO.PayTo();
			payToList[1].setTo(dmPlatAddr.getBody().getAddress());
			payToList[1].setReadableAmount(fee);
			paymentReqDTO.setPayToList(payToList);

			CregisRsp<NoBody> transferAll = cregisClient.transferAll(paymentReqDTO);
			logger.info("订单：" + orderNo + " 调用Cregis提交转账结果：" + transferAll.getCode());
			if (JudgeUtils.equals("200", transferAll.getCode())) {
				logger.info("订单：" + orderNo + " 调用Cregis提交转账成功");
				//成功,更新订单状态,财务处理
//				orderSts = TamConstants.ORD_STS_S;
//				TransferOrderDO updateTransfer = new TransferOrderDO();
//				updateTransfer.setBusOrderNo(paymentResultDTO.getBusOrderNo());
//				updateTransfer.setOrderNo(orderNo);
//				updateTransfer.setOrderSts(TamConstants.ORD_STS_S);
//				transactionalService.updateTransferOrder(updateTransfer);
//				// 财务处理
//				GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
//				HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
//				// csh_order表中订单编号
//				handleFinanceDTO.setOrderNo(paymentResultDTO.getOrderNo());
//				// 业务类型
//				handleFinanceDTO.setBusType(TamConstants.BUS_TYPE_DM_TRANSFER);
//				handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
//				GenericRspDTO<NoBody> genericRspDTO = cshOrderClient.handleFinance(handleFinanceDTOGenericDTO);
//				if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
//					LemonException.throwBusinessException(genericRspDTO.getMsgCd());
//				}
			} else {
				logger.error("订单：" + orderNo + " 调用Cregis提交转账失败");
				orderSts = TamConstants.ORD_STS_F;
				//失败,账务冲正,更新订单状态
				GenericDTO<HandleFailTranDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
				HandleFailTranDTO handleFailTranDTO = new HandleFailTranDTO();
				// csh_order表中订单编号
				handleFailTranDTO.setOrderNo(paymentResultDTO.getOrderNo());
				// 业务类型
				handleFailTranDTO.setBusType(busType);
				handleFinanceDTOGenericDTO.setBody(handleFailTranDTO);
				GenericRspDTO<NoBody> genericRspDTO = cshOrderClient.handleFailTransfer(handleFinanceDTOGenericDTO);
				if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
					LemonException.throwBusinessException(genericRspDTO.getMsgCd());
				}
			}
		} else {
			//链上转账
			logger.info("订单：" + orderNo + " 链上转账");
		}

		// 组装对外传输数据
		DmTransferRspDTO userOut = new DmTransferRspDTO();
		userOut.setTransferType(busType);
		userOut.setAmount(paymentResultDTO.getOrderAmt());
		userOut.setFee(fee);
		userOut.setOrderSts(JudgeUtils.isBlank(orderSts) ? TamConstants.ORD_STS_P : orderSts);
		userOut.setOrderNo(paymentResultDTO.getOrderNo());
		// 消息推送
//		sendMessage(transferDO);
		return GenericRspDTO.newSuccessInstance(userOut);
	}

	/**
	 * 获取数币收款地址类型
	 *
	 * @param getDmSkAddrTypeReqDTO
	 * @return
	 */
	@Override
	public GenericRspDTO<String> getDmSkAddrType(GenericDTO<GetDmSkAddrTypeReqDTO> getDmSkAddrTypeReqDTO) {
		String address = getDmSkAddrTypeReqDTO.getBody().getAddress();
		logger.info("查询数币转账收款地址类型,地址:{}", address);
		String skAddrType = "";
		//判断收款方地址是否存在表中并且是可用状态
		GenericRspDTO<DmAccountAddressRspDTO> booleanGenericRspDTO = acmManageClient.getByAddress(address);
		DmAccountAddressRspDTO body = booleanGenericRspDTO.getBody();
		if (JudgeUtils.isNotSuccess(booleanGenericRspDTO.getMsgCd())) {
			LemonException.throwBusinessException(booleanGenericRspDTO.getMsgCd());
		}
		if (JudgeUtils.isNotNull(body)) {
			//站内转账
			skAddrType = DmSkAddrTypeEnum.PLATFORM_INTERNAL.getDescription();
		} else {
			//判断是否是链上白名单地址
			DmWhtAddrDO dmWhtAddrDO = new DmWhtAddrDO();
			dmWhtAddrDO.setAddress(address);
			dmWhtAddrDO.setEffFlg("Y");
			LocalDateTime now = LocalDateTime.now();
			dmWhtAddrDO.setBeginDt(now);
			dmWhtAddrDO.setEndDt(now);
			List<DmWhtAddrDO> dmWhtAddrDOS = dmWhtAddrDao.find(dmWhtAddrDO);
			if (!dmWhtAddrDOS.isEmpty()) {
				skAddrType = DmSkAddrTypeEnum.WHITELIST_ONCHAIN.getDescription();
			}else {
				skAddrType = DmSkAddrTypeEnum.EXTERNAL_ONCHAIN.getDescription();
			}
		}
		return GenericRspDTO.newSuccessInstance(skAddrType);
	}

	/**
	 * 获取数币平台地址
	 * @param getDmPlatAddrReqDTO
	 * @return
	 */
	@Override
    public GenericRspDTO<GetDmPlatAddrRspDTO> getDmPlatAddr(GenericDTO<GetDmPlatAddrReqDTO> getDmPlatAddrReqDTO) {
		GetDmPlatAddrReqDTO reqDTO = getDmPlatAddrReqDTO.getBody();
		logger.info("Received request to get platform address: network={}, type={}, ccy={}",
				reqDTO.getNetwork(), reqDTO.getType(), reqDTO.getCcy());

		// 获取 platformId
		String platformId = getPlatformIdByNetworkTypeCcy(
				reqDTO.getNetwork(), reqDTO.getType(), reqDTO.getCcy());

		// 查询数据库
		DmPlatFormDO platFormDO = dmPlatFormDao.getByPlatformNo(platformId);
		if (platFormDO == null || platFormDO.getAddress() == null) {
			logger.error("No address found for platformId={}", platformId);
			LemonException.throwBusinessException("CPI80003");
		}

		// 构造响应
		GetDmPlatAddrRspDTO rspDTO = new GetDmPlatAddrRspDTO();
		BeanUtils.copyProperties(rspDTO, platFormDO);
		logger.info("Successfully retrieved platform address: {}", platFormDO.getAddress());
		return GenericRspDTO.newSuccessInstance(rspDTO);
    }

	@Override
	public TransferOrderRspDTO getTransferOrder(String orderNo) {
		TransferOrderDO transferOrderDO = transactionalService.tansferInfo(orderNo);
		TransferOrderRspDTO transferOrderRspDTO = new TransferOrderRspDTO();
		BeanUtils.copyProperties(transferOrderRspDTO, transferOrderDO);
		return transferOrderRspDTO;
	}

	/**
	 * 根据网络、类型和币种获取平台ID
	 *
	 * @param network 区块链网络
	 * @param type    账户类型
	 * @param ccy     币种
	 * @return 平台ID
	 */
	private String getPlatformIdByNetworkTypeCcy(String network, String type, String ccy) {
		logger.debug("Querying platformId for network={}, type={}, ccy={}", network, type, ccy);
		for (DmPlatformEnum platform : DmPlatformEnum.values()) {
			if (platform.getNetwork().equals(network) &&
					platform.getType().equals(type) &&
					platform.getCcy().equals(ccy)) {
				logger.info("Found platformId: {}", platform.getPlatformId());
				return platform.getPlatformId();
			}
		}
		logger.error("No platform found for network={}, type={}, ccy={}", network, type, ccy);
		LemonException.throwBusinessException("CPI80003");
		return null;
	}

    private DmAccountDetailRspDTO getUserIdByFkAcNo(String userMblNo, BigDecimal amount) {
		// 付款方账户信息
		GenericRspDTO<DmAccountDetailRspDTO> fkRspDTO = acmManageClient.queryDmAccountDetail(userMblNo);
		if (JudgeUtils.isNotSuccess(fkRspDTO.getMsgCd())) {
			LemonException.throwLemonException("TAM10031");
		}
		DmAccountDetailRspDTO fkAcBalRspDTO = fkRspDTO.getBody();
		// 判断是否会余额不足
		if (amount.compareTo(fkAcBalRspDTO.getRealBal()) > 0) {
			logger.info("付款方余额: " + fkAcBalRspDTO.getRealBal());
			logger.info("转账金额: " + amount);
			LemonException.throwLemonException("TAM20007");
		}
		return fkAcBalRspDTO;
	}

	/**
	 * 校验双方数币账号是否为相同币种、网络等
	 *
	 * @param transferOrder
	 * @return
	 */
	private HashMap<String, DmAccountDetailRspDTO> checkDmAccount(DmOutTransferOrderDTO transferOrder) {

		HashMap<String, DmAccountDetailRspDTO> map = new HashMap<>();

		String userMblNo = transferOrder.getUserMblNo();
		String ccy = transferOrder.getOrderCcy();
		String mblNo = transferOrder.getMblNo();
		BigDecimal amount = transferOrder.getAmount();

		if (userMblNo.equals(mblNo)) {
			LemonException.throwLemonException("TAM20010");
		}
		if (BigDecimal.valueOf(0).compareTo(amount) >= 0) {
			throw new LemonException("TAM10021");
		}

		// 付款方账户信息
		GenericRspDTO<DmAccountDetailRspDTO> fkRspDTO = acmManageClient.queryDmAccountDetail(userMblNo);
		logger.info("付款方账户余额：{}", fkRspDTO.getBody().getRealBal());
		if (JudgeUtils.isNotSuccess(fkRspDTO.getMsgCd()) || fkRspDTO.getBody() == null) {
			LemonException.throwLemonException("TAM10031");
		}
		DmAccountDetailRspDTO fkAcBalRspDTO = fkRspDTO.getBody();
		// 如果调用Cregis查询到的账户余额为0,则去查表     生产这一个得去掉，只用于demo演示
		if (fkAcBalRspDTO.getRealBal().compareTo(BigDecimal.ZERO) == 0) {
			// 校验用户账户余额是否足够转账
			BigDecimal balance = new BigDecimal(0);
			//查询用户账户余额
			UserAccountDTO userAccountDTO = new UserAccountDTO();
			userAccountDTO.setUserId(fkAcBalRspDTO.getUserId());
			userAccountDTO.setCcy(ccy);
			//调用账户接口，查询账户余额
			GenericRspDTO<List<QueryAcBalRspDTO>> genericRspDTO = acmManageClient.queryAcBal(userAccountDTO);
			List<QueryAcBalRspDTO> queryAcBalRspDTO = genericRspDTO.getBody();
			if(JudgeUtils.isNull(queryAcBalRspDTO)){
				LemonException.throwBusinessException("PWM30009");
			}
			//判断资金类型为现金，则填充账户余额
			for(QueryAcBalRspDTO acBalRspDTO: queryAcBalRspDTO) {
				if(JudgeUtils.equals(CapTypEnum.CAP_TYP_CASH.getCapTyp(),acBalRspDTO.getCapTyp())) {
					logger.info("付款方账户acNo:{}, 余额:{} ", acBalRspDTO.getAcNo(), balance);
					balance = acBalRspDTO.getAcCurBal();
				}
			}
			//校验提现金额加手续费大于用户账户余额,则抛出异常
			if(amount.compareTo(balance) > 0){
				LemonException.throwBusinessException("PWM30002");
			}
		} else if (amount.compareTo(fkAcBalRspDTO.getRealBal()) > 0) {
			// 判断是否会余额不足
			logger.info("付款方余额: " + fkAcBalRspDTO.getRealBal());
			logger.info("转账金额: " + amount);
			LemonException.throwLemonException("TAM20007");
		}
		map.put("fkAcBalRspDTO", fkAcBalRspDTO);

		// 收款方账户信息
		GenericRspDTO<DmAccountDetailRspDTO> skRspDTO = acmManageClient.queryDmAccountDetail(mblNo);
		if (JudgeUtils.isNotSuccess(skRspDTO.getMsgCd())) {
			LemonException.throwLemonException("TAM10029");
		}
		DmAccountDetailRspDTO skAcBalRspDTO = skRspDTO.getBody();
		// 判断收款方是否为相同币种
		if (!JudgeUtils.equals(ccy, skAcBalRspDTO.getCoinId())) {
			logger.info("付款方币种: " + fkAcBalRspDTO.getCoinId());
			logger.info("收款方币种: " + skAcBalRspDTO.getCoinId());
			LemonException.throwLemonException("TAM10030");
		}
		// 判断双方网络是否一致
		if (!JudgeUtils.equals(fkAcBalRspDTO.getNetwork(), skAcBalRspDTO.getNetwork())) {
			logger.info("付款发网络: " + fkAcBalRspDTO.getNetwork());
			logger.info("收款方网络: " + skAcBalRspDTO.getNetwork());
			LemonException.throwLemonException("TAM30005");
		}
		map.put("skAcBalRspDTO", skAcBalRspDTO);
		return map;
	}

	/**
	 * 根据用户id检查用户状态
	 * @param userId
	 * @throws LemonException
	 */
	private void checkUserStatus(String userId) throws LemonException {
		RiskCheckUserStatusReqDTO reqDTO = new RiskCheckUserStatusReqDTO();
		reqDTO.setId(userId);
		reqDTO.setIdTyp("01"); // 证件类型 01用户号/商户号 02银行卡 03身份证
		reqDTO.setTxTyp("04"); // 交易类型
		GenericRspDTO<NoBody> rspDTO = riskCheckClient.checkUserStatus(reqDTO);
		if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
			// 用户状态检查失败
			logger.info("user :" + userId + " status check failure!");
			LemonException.throwBusinessException(rspDTO.getMsgCd());
		}
	}
}
