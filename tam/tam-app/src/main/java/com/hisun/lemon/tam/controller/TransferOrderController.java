package com.hisun.lemon.tam.controller;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.csh.dto.cashier.CashierViewDTO;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.tam.dao.GetDmSkAddrTypeReqDTO;
import com.hisun.lemon.tam.dto.*;
import com.hisun.lemon.tam.dto.QueryResultTransferOrderDTO.queryOrder;
import com.hisun.lemon.tam.service.ITransferOrderService;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "处理转账")
@RestController
@RequestMapping(value = "/tam/transfer")
public class TransferOrderController extends BaseController {
    @Resource(name = "transferService")
    ITransferOrderService transferService;

    /**
     * 查询历史转账记录
     *
     * @return
     */
    @ApiOperation(value = "历史转账记录", notes = "查询历史转账记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    })
    @ApiResponse(code = 200, message = "历史转账记录查询")
    @GetMapping(value = "/history/order")
    public GenericRspDTO<QueryResultTransferOrderDTO> historyTransferOrder(@Validated QueryTransferOrderDTO queryDTO) {
        QueryTransferOrderDTO transfer = new QueryTransferOrderDTO();
        transfer.setPageNum(queryDTO.getPageNum());
        transfer.setPageSize(queryDTO.getPageSize());
        List<queryOrder> queryList = this.transferService.historyTransferOrder(transfer);
        QueryResultTransferOrderDTO query = new QueryResultTransferOrderDTO();
        query.setQueryOrderList(queryList);
        return GenericRspDTO.newSuccessInstance(query);
    }

    @ApiOperation(value = "转账账户信息", notes = "转账账户信息")
    @ApiResponse(code = 200, message = "转账账户信息")
    @ApiImplicitParam(name = "mblNo", value = "账户号", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/user/{mblNo}")
    public GenericRspDTO<UserInfoRspDTO> userInfo(@PathVariable("mblNo") String mblNo) {
        UserInfoRspDTO userInfo = this.transferService.userInfo(mblNo);
        return GenericRspDTO.newSuccessInstance(userInfo);
    }

    /**
     * 面对面转账
     *
     * @param faceTransferOrderDTO 面对面转账订单
     * @return 订单信息
     */
    @ApiOperation(value = "面对面转账下单", notes = "生成转账订单，调用收银台")
    @ApiResponse(code = 200, message = "面对面数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    })
    @PostMapping(value = "/order/face")
    public GenericRspDTO<CashierViewDTO> createFaceOrder(@Validated @RequestBody GenericDTO<FaceTransferOrderDTO> faceTransferOrderDTO) {
        return this.transferService.createFaceOrder(faceTransferOrderDTO);

    }

    /**
     * 转账到账户
     *
     * @param userTransferOrderDTO 用户转账订单
     * @return 订单信息
     */
    @ApiOperation(value = "转账到账户下单", notes = "生成转账订单，调用收银台")
    @ApiResponse(code = 200, message = "转账到账户数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    })
    @PostMapping(value = "/order/user")
    public GenericRspDTO<CashierViewDTO> createUserOrder(@Validated @RequestBody GenericDTO<UserTransferOrderDTO> userTransferOrderDTO) {
        return this.transferService.createUserOrder(userTransferOrderDTO);

    }

    /**
     * 转账到账户(内部系统开放接口  免密)
     *
     * @param userOutTransferOrderDTO 用户外部转账订单
     */
    @ApiOperation(value = "转账到账户下单   免密", notes = "生成转账订单，调用收银台")
    @ApiResponse(code = 200, message = "转账到账户数据")
    @PostMapping(value = "/order/user/out")
    public GenericRspDTO<UserTransferRspDTO> createOutUserOrder(@Validated @RequestBody GenericDTO<UserOutTransferOrderDTO> userOutTransferOrderDTO) {
        return this.transferService.createUserOutOrder(userOutTransferOrderDTO);

    }

    /**
     * 转账到账户(内部系统开放接口   加密)
     *
     * @param userOutTransferOrderDTO 用户外部转账订单
     * @return 订单信息
     */
    @ApiOperation(value = "转账到账户下单  加密", notes = "生成转账订单，调用收银台")
    @ApiResponse(code = 200, message = "转账到账户数据")
    @PostMapping(value = "/order/user/encrypt/out/bak")
    public GenericRspDTO<UserTransferRspDTO> outUserOrderEncrypt(@Validated @RequestBody GenericDTO<UserOutTransferOrderDTO> userOutTransferOrderDTO) {
        UserOutTransferOrderDTO userOut = userOutTransferOrderDTO.getBody();
        if (JudgeUtils.isBlank(userOut.getPayPassword())) {
            throw new LemonException("TAM10024");
        }
        if (JudgeUtils.isBlank(userOut.getValidateRandom())) {
            throw new LemonException("TAM10025");
        }

        GenericRspDTO<UserTransferRspDTO> rspDTO = this.transferService.createUserOutOrder(userOutTransferOrderDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO;

    }


    /**
     * 转账到账户(内部系统开放接口   加密)  - 新版本
     *
     * @param userOutTransferOrderDTO 用户外部转账订单
     * @return 订单信息
     */
    @ApiOperation(value = "转账到账户下单", notes = "生成转账订单，调用收银台")
    @ApiResponse(code = 200, message = "转账到账户下单")
    @PostMapping(value = "/order/user/encrypt/out")
    public GenericRspDTO<UserTransferRspDTO> outUserOrder(@Validated @RequestBody GenericDTO<NewUserOutTransferOrderDTO> userOutTransferOrderDTO) {
        GenericRspDTO<UserTransferRspDTO> rspDTO = this.transferService.createUserOutOrderNew(userOutTransferOrderDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO;

    }

    /**
     * 数币转账
     *
     * @param dmOutTransferOrderDTO 数币转账订单请求DTO
     * @return 订单信息
     */
    @ApiOperation(value = "数币转账", notes = "数币转账")
    @ApiResponse(code = 200, message = "数币转账成功")
    @PostMapping(value = "/order/dm/encrypt/out")
    public GenericRspDTO<DmTransferRspDTO> outDmOrder(@Validated @RequestBody GenericDTO<DmOutTransferOrderDTO> dmOutTransferOrderDTO) {
        GenericRspDTO<DmTransferRspDTO> rspDTO = this.transferService.createDmOutOrder(dmOutTransferOrderDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO;
    }

    /**
     * 数币转账收款地址类型
     *
     * @param getDmSkAddrTypeReqDTO 数币转账收款地址类型请求DTO
     * @return 订单信息
     */
    @ApiOperation(value = "数币转账收款地址类型", notes = "数币转账收款地址类型")
    @ApiResponse(code = 200, message = "数币转账收款地址类型成功")
    @PostMapping(value = "/order/dm/skAddr/type")
    public GenericRspDTO<String> getDmSkAddrType(@Validated @RequestBody GenericDTO<GetDmSkAddrTypeReqDTO> getDmSkAddrTypeReqDTO) {
        GenericRspDTO<String> rspDTO = this.transferService.getDmSkAddrType(getDmSkAddrTypeReqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO;
    }

    /**
     * 获取数币平台地址
     *
     * @param getDmPlatAddrReqDTO 获取数币平台地址
     * @return 订单信息
     */
    @ApiOperation(value = "获取数币平台地址", notes = "获取数币平台地址")
    @ApiResponse(code = 200, message = "获取数币平台地址成功")
    @PostMapping(value = "/get/dm/plat/addr")
    public GenericRspDTO<GetDmPlatAddrRspDTO> getDmPlatAddr(@Validated @RequestBody GenericDTO<GetDmPlatAddrReqDTO> getDmPlatAddrReqDTO) {
        GenericRspDTO<GetDmPlatAddrRspDTO> rspDTO = this.transferService.getDmPlatAddr(getDmPlatAddrReqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "根据订单号查询转账订单", notes = "根据订单号查询转账订单")
    @ApiResponse(code = 200, message = "订单查询结果")
    @GetMapping(value = "/detail/{orderNo}")
    public GenericRspDTO<TransferOrderRspDTO> getTransferOrder(@PathVariable String orderNo) {
        TransferOrderRspDTO transferOrderRspDTO = this.transferService.getTransferOrder(orderNo);
        if (null == transferOrderRspDTO) {
            throw new LemonException("TAM20001");
        }
        return GenericRspDTO.newSuccessInstance(transferOrderRspDTO);
    }

    /**
     * 商户转账到账户
     *
     * @param genericMerchantTransferOrderDTO
     * @return
     */
    @ApiOperation(value = "商户转账到账户下单", notes = "生成转账订单，调用收银台")
    @ApiResponse(code = 200, message = "商户转账到账户数据")
    @PostMapping(value = "/order/merchant/out")
    public GenericRspDTO<MerchantTransferRspDTO> merchantTransferOrder(@Validated @RequestBody GenericDTO<MerchantTransferOrderDTO> genericMerchantTransferOrderDTO) {
        GenericRspDTO<MerchantTransferRspDTO> rspDTO = this.transferService.createMerchantOutOrder(genericMerchantTransferOrderDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO;

    }


    /**
     * 转账到银行卡
     *
     * @param cardTransferOrderDTO 银行卡转账订单
     * @return 订单信息
     */
    @ApiOperation(value = "转账到银行卡下单", notes = "生成转账到银行卡订单，调用收银台")
    @ApiResponse(code = 200, message = "转账到银行卡下单结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    })
    @PostMapping(value = "/order/card")
    public GenericRspDTO<CashierViewDTO> createCardOrder(@Validated @RequestBody GenericDTO<CardTransferOrderDTO> cardTransferOrderDTO) {
        return this.transferService.createCardOrder(cardTransferOrderDTO);
    }

    @ApiOperation(value = "转账通知", notes = "转账通知")
    @ApiResponse(code = 200, message = "转账通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    })
    @PatchMapping(value = "/result/order")
    public GenericRspDTO<NoBody> userTransferOrderResult(
            @Validated @RequestBody GenericDTO<ResultTransferOrderDTO> resultTransferOrderDTO) {
        GenericRspDTO<NoBody> rspDTO = transferService.userTransferOrderResult(resultTransferOrderDTO);
        return rspDTO.newSuccessInstance();
    }

    @ApiOperation(value = " 资金能力结果通知", notes = " 资金能力结果通知")
    @ApiResponse(code = 200, message = " 资金能力结果通知")
    @PatchMapping(value = "/result/order/bank")
    public GenericRspDTO<NoBody> transferOrderResult(
            @Validated @RequestBody GenericDTO<ResultTransferOrderDTO> resultTransferOrderDTO) {
        GenericRspDTO<NoBody> rspDTO = transferService.transferOrderResult(resultTransferOrderDTO);
        return rspDTO.newSuccessInstance();
    }


    /**
     * 补单  0601  与收银台对账
     *
     * @param killOrderRspDTO 补单信息
     * @return 订单信息
     */
    @ApiOperation(value = "补单  0601  与CSH对账", notes = "补单  0601  与CSH对账")
    @ApiResponse(code = 200, message = "补单  0601  与CSH对账")
    @PatchMapping(value = "/order/kill")
    public GenericRspDTO<NoBody> killOrder(@Validated @RequestBody GenericDTO<KillOrderRspDTO> killOrderRspDTO) {
        transferService.killOrder(killOrderRspDTO);
        return GenericRspDTO.newSuccessInstance();
    }


    /**
     * 补单 与CPO对账
     *
     * @param killOrderRspDTO 补单信息
     * @return 订单信息
     */
    @ApiOperation(value = "补单    与CPO对账", notes = "补单  与CPO对账")
    @ApiResponse(code = 200, message = "补单    与CPO对账")
    @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, paramType = "path", dataType = "String")
    @PatchMapping(value = "/order/cpo/kill")
    public GenericRspDTO<NoBody> killCpoOrder(@Validated @RequestBody GenericDTO<KillOrderRspDTO> killOrderRspDTO) {
        transferService.killCpoOrder(killOrderRspDTO);
        return GenericRspDTO.newSuccessInstance();
    }


    @ApiOperation(value = "转账到ACLEDA银行卡下单", notes = "生成转账到ACLEDA银行卡订单，调用收银台获取sessionId")
    @ApiResponse(code = 200, message = "转账到ACLEDA银行卡下单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    })
    @PostMapping(value = "/order/card/acleda")
    public GenericRspDTO<AcledaCardTransferOrderRspDTO> createAcledaCardOrder(@Validated @RequestBody GenericDTO<AcledaCardTransferOrderReqDTO> cardTransferOrderDTO) {
        return this.transferService.createAcledaCardOrder(cardTransferOrderDTO);
    }

    @ApiOperation(value = "转账到ACLEDA银行卡下单成功通知", notes = "转账到ACLEDA银行卡下单成功通知")
    @ApiResponse(code = 200, message = "转账到ACLEDA银行卡下单成功通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    })
    @PostMapping(value = "/order/card/acleda/notify")
    public GenericRspDTO<NoBody> acledaCardOrderSuccessNotify(@Validated @RequestBody GenericDTO<AcledaCardTransferNotifyDTO> cardTransferNotifyDTO) {
        return this.transferService.acledaCardSuccessNotify(cardTransferNotifyDTO);
    }
}
