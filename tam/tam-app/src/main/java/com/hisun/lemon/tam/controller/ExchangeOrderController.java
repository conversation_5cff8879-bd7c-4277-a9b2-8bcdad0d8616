package com.hisun.lemon.tam.controller;

import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.tam.dto.*;
import com.hisun.lemon.tam.service.ExchangeOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "处理兑换")
@RestController
@RequestMapping(value = "/tam/exchange")
public class ExchangeOrderController extends BaseController {

    @Resource
    ExchangeOrderService exchangeOrderService;


    @ApiOperation(value = "查询汇率", notes = "查询汇率")
    @ApiResponse(code = 200, message = "查询汇率")
    @PostMapping(value = "/rate")
    public GenericRspDTO<ExchangeRateRspDTO> queryExchangeRate(@Validated @RequestParam(name = "fromCoin") String fromCoin,
                                                               @Validated @RequestParam(name = "toCoin") String toCoin) {
        return GenericRspDTO.newSuccessInstance(exchangeOrderService.queryExchangeRate(fromCoin, toCoin));
    }

    @ApiOperation(value = "获取汇率列表", notes = "获取汇率列表")
    @ApiResponse(code = 200, message = "获取汇率列表")
    @PostMapping(value = "/rate/list")
    public GenericRspDTO<List<ExchangeRateRspDTO>> getExchangeRateList() {
        return GenericRspDTO.newSuccessInstance(exchangeOrderService.getExchangeRateList());
    }

    @ApiOperation(value = "数法币兑换", notes = "数法币兑换")
    @ApiResponse(code = 200, message = "数法币兑换")
    @PostMapping(value = "/order")
    public GenericRspDTO<UserExchangeRspDTO> exchangeOrder(@Validated @RequestBody GenericDTO<UserExchangeOrderDTO> req) {
        UserExchangeOrderDTO userExchangeOrderDTO = req.getBody();
        userExchangeOrderDTO.setUserId(LemonUtils.getUserId());
        return GenericRspDTO.newSuccessInstance(exchangeOrderService.exchangeOrder(userExchangeOrderDTO));
    }

    @ApiOperation(value = "数法币兑换执行", notes = "数法币兑换执行")
    @ApiResponse(code = 200, message = "数法币兑换执行")
    @PostMapping(value = "/order/execute")
    public GenericRspDTO<NoBody> executeExchangeOrder(@Validated @RequestBody GenericDTO<UserExchangeOrderDTO> req) {
        UserExchangeOrderDTO userExchangeOrderDTO = req.getBody();
        exchangeOrderService.executeExchangeOrder(userExchangeOrderDTO);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询兑换订单详情", notes = "查询兑换订单详情")
    @ApiResponse(code = 200, message = "查询兑换订单详情")
    @PostMapping(value = "/order/detail")
    public GenericRspDTO<ExchangeOrderDetailRspDTO> queryOrderDetail(@Validated @RequestParam(name = "orderNo") String orderNo) {
        return GenericRspDTO.newSuccessInstance(exchangeOrderService.queryExchangeOrderDetail(orderNo));
    }

    @ApiOperation(value = "计算兑换订单手续费", notes = "计算兑换订单手续费")
    @ApiResponse(code = 200, message = "计算兑换订单手续费")
    @PostMapping(value = "/calfee")
    public GenericRspDTO<UserExchangeRspDTO> calExchangeFee(@Validated @RequestBody GenericDTO<UserExchangeOrderDTO> req) {
        UserExchangeOrderDTO userExchangeOrderDTO = req.getBody();
        return GenericRspDTO.newSuccessInstance(exchangeOrderService.calExchangeFee(userExchangeOrderDTO));
    }

    @ApiOperation(value = "更新兑换订单表状态", notes = "更新兑换订单表状态")
    @ApiResponse(code = 200, message = "更新兑换订单表状态")
    @PostMapping(value = "/updateOrderSts")
    public GenericRspDTO<NoBody> updateOrderSts(@Validated @RequestParam(name = "orderNo") String orderNo,
                                                @Validated @RequestParam(name = "status") String status) {
        exchangeOrderService.updateOrderSts(orderNo,status);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "获取用户兑换订单列表", notes = "获取用户兑换订单列表")
    @ApiResponse(code = 200, message = "获取用户兑换订单列表")
    @PostMapping(value = "/order/list")
    public GenericRspDTO<List<ExchangeRateRspDTO>> queryExchangeOrderList(@Validated @RequestParam(name = "fromCoin") String fromCoin) {
        return GenericRspDTO.newSuccessInstance(exchangeOrderService.getExchangeRateListByFromCoin(fromCoin));
    }

}
