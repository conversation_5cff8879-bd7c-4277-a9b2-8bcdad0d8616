/*
 * @ClassName transferOrderDDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:44:54
 */
package com.hisun.lemon.tam.dao;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.tam.entity.TransferOrderDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ITransferOrderDao extends BaseDao<TransferOrderDO> {

    public List<TransferOrderDO> queryList(Map map);
    
    public List<TransferOrderDO> queryListByType(Map map);

    public List<TransferOrderDO> queryListByExtOrderNo(@Param("extOrderNo")String extOrderNo);
}