package com.hisun.lemon.tam.schedule;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import com.hisun.lemon.tam.service.chk.AbstractChkFileService;

/**
 * 定时任务调度器
 */
@Component
public class Scheduler {
	ExecutorService executorService = Executors.newFixedThreadPool(2);

	@Resource
	private List<AbstractChkFileService> scheduleService;

	
	@BatchScheduled(cron="0 0 0/1 * * ?")
	public void createChkFile(){
		for(AbstractChkFileService item:scheduleService){
			executorService.submit(item);
		}
	}
}
