package com.hisun.lemon.tam.dao;

import com.hisun.lemon.tam.entity.ExchangeRateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface ExchangeRateDao {

    ExchangeRateDO queryExchangeRate(@Param("fromCoin") String fromCoin, @Param("toCoin") String toCoin);

    List<ExchangeRateDO> get();

    List<ExchangeRateDO> getExchangeRateListByFromCoin(@Param("fromCoin") String fromCoin);
}
