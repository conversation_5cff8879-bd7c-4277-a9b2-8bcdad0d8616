<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:channel="http://www.hisun.com/schema/channel"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.hisun.com/schema/channel http://www.hisun.com/schema/channel/channel.xsd">
     <bean id="icbcSignature" class="com.hisun.lemon.cpi.icbc.fastpay.ICBCSecurity"/>
    
	<channel:service name="ICBC" charset="UTF-8" >
		<!-- 
		<channel:connector protocol="tcp_long" address="127.0.0.1:7778" timeout="60000" waitTimeout="100000" heartbeatCheck-ref="icbcHeartbeat" poolMaxConnections="5" />
		 -->
		<channel:connector id="http1" protocol="http" url="http://*************:21277" default="false"   dateTypeName="FORM" charset="UTF-8"/>
		<channel:container>  <!-- *************:8080  127.0.0.1:7777/mock/bytes/ICBC -->
			<!-- 如果签名验签都一样，建议在此处配置；如果不一样，建议在marshall-class 用注解@Signature(bean="beanName") -->
			<channel:signature ref="icbcSignature" />
			
			<channel:processor name="precontract" marshal-class="com.hisun.lemon.cpi.icbc.fastpay.req.PreContractReq" 
                unmarshal-class="com.hisun.lemon.cpi.icbc.fastpay.rsp.PreContractRsp" connector-id="http1" />
				
			<channel:processor name="contract" marshal-class="com.hisun.lemon.cpi.icbc.fastpay.req.ContractReq" 
                unmarshal-class="com.hisun.lemon.cpi.icbc.fastpay.rsp.ContractRsp" connector-id="http1" />
                
            <channel:processor name="payment" marshal-class="com.hisun.lemon.cpi.icbc.fastpay.req.PaymentReq" 
                unmarshal-class="com.hisun.lemon.cpi.icbc.fastpay.rsp.PaymentRsp" connector-id="http1" />
            
            <channel:processor name="refund" marshal-class="com.hisun.lemon.cpi.icbc.fastpay.req.RefundReq" 
                unmarshal-class="com.hisun.lemon.cpi.icbc.fastpay.rsp.RefundRsp" connector-id="http1" />
                
            <channel:processor name="release" marshal-class="com.hisun.lemon.cpi.icbc.fastpay.req.ReleaseReq" 
                unmarshal-class="com.hisun.lemon.cpi.icbc.fastpay.rsp.ReleaseRsp" connector-id="http1" />
                
			<!-- 此处的handler-filter对全部processor生效 -->
			<channel:handler-filter class="com.hisun.channel.service.child.filter.MonitorHandlerFilter" />
			<!--<channel:handler-filter class="com.hisun.channel.service.childpay.filter.MockHandlerFilter" /> -->
		</channel:container>
	</channel:service>
</beans>