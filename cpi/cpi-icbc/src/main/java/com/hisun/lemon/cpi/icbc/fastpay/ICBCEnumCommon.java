package com.hisun.lemon.cpi.icbc.fastpay;

import com.hisun.channel.common.utils.StringUtils;


/**
 * 枚举公共类
 * 
 * <AUTHOR>
 *
 */
public class ICBCEnumCommon {

	/**
	 * 接口类型
	 * <AUTHOR>
	 *
	 */
	public enum EnumSource{
		precontract,contract,payment,release,refund
	}
	
	/**
	 * 接口代码
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumIntFcode {
		PRECONTRACT("40010"),
		CONTRACT("40011"),
		RELEASE("40012"),
		PAYMENT("40021"),
		REFUND("40023");

		private String code;

		EnumIntFcode(String code) {
			this.code = code;
		}

		public static EnumIntFcode getEnumInFcode(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumIntFcode intFcode : values()) {
					if (StringUtils.equals(intFcode.getCode(), code)) {
						return intFcode;
					}
				}
			}
			return null;
		}

		public String getCode() {
			return code;
		}
	}

	/**
	 * 证件类型
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumIdType {

		IDENTITY_CARD("0"),
		PASSPORT("1");

		private String code;

		EnumIdType(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	/**
	 * 卡种
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumCardType {
		DEBIT_CARD("1"),
		CREDIT_CARD("2");

		private String code;

		EnumCardType(String code) {
			this.code = code;
		}

		public static EnumCardType getECardType(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumCardType cardType : values()) {
					if (StringUtils.equals(cardType.getCode(), code)) {
						return cardType;
					}
				}
			}
			return null;

		}

		public String getCode() {
			return code;
		}
	}

	/**
	 * 币种
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumCurrType {
		THAI_BAHT("084"),
		DOLLAR("014");

		private String code;

		EnumCurrType(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	/**
	 * 摘要
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumNote {
		CONSUME("01"),
		TRANSFER_ACCOUNTS("02"),
		PAY("03"),
		REPAYMENT("04"),
		AUTOMATIC_REPAYMENT("041"),
		FINANCING("05"),
		RECHARGE("06"),
		RED_PACKET("07");

		private String code;

		EnumNote(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	/**
	 * 交易状态
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumRetStatus {
		SUCCESS("2"),
		FAIL("3"),
		UNKNOWN("4");

		private String code;

		EnumRetStatus(String code) {
			this.code = code;
		}

		public static EnumRetStatus getERetStatus(String code) {
			if (code!=null) {
				for (EnumRetStatus retStatus : values()) {
                     if(StringUtils.equals(retStatus.getCode(),code)){
                    	 return retStatus;
                     }
				}
			}
			return null;
		}
		
		public String getCode() {
			return code;
		}
	}
}
