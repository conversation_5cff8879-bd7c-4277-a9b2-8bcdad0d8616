package com.hisun.lemon.cpi.icbc.fastpay.rsp;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.VerifyValue.ValueType;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumRetStatus;

/**
 * 解约响应报文
 * 
 * <AUTHOR>
 *
 */
@Validated
@Plain(root = "Root")
public class ReleaseRsp {

	@Verify(expr = "@Root")
	private String verify;

	@Item(expr = "INVOKE('icbcDecrypt',@Root)")
	@VerifyValue(type = ValueType.FIELD)
	private String encryMsg;

	@NotNull
	@Nest(expr = "$encryMsg")
	private ReleaseRspMsg releaseRspMsg;

	@Xml(root = "Msg")
	public static class ReleaseRspMsg {
		
		@Nest(alias="resp")
		private ReleaseTranRsp releaseTranRsp;

		public static class ReleaseTranRsp {

			/**
			 * 注销时间
			 */
			@Item
			private String signTime;

			/**
			 * 个人协议号
			 */
			@Item
			private String userId;

			/**
			 * 返回代码
			 */
			@Item
			private String retCode;

			/**
			 * 返回信息
			 */
			@Item
			private String retMsg;

			/**
			 * 交易状态
			 */
			@Item
			private String retStatus;

			/**
			 * 机构号
			 */
			@Item
			private String corpNo;

			/**
			 * 机构流水号
			 */
			@Item
			private String trxSerno;

			/**
			 * 注销时间
			 */
			public String getSignTime() {
				return signTime;
			}

			/**
			 * 注销时间
			 */
			public void setSignTime(String signTime) {
				this.signTime = signTime;
			}

			/**
			 * 个人协议号
			 */
			public String getUserId() {
				return userId;
			}

			/**
			 * 个人协议号
			 */
			public void setUserId(String userId) {
				this.userId = userId;
			}

			/**
			 * 返回代码
			 */
			public String getRetCode() {
				return retCode;
			}

			/**
			 * 返回代码
			 */
			public void setRetCode(String retCode) {
				this.retCode = retCode;
			}

			/**
			 * 返回信息
			 */
			public String getRetMsg() {
				return retMsg;
			}

			/**
			 * 返回信息
			 */
			public void setRetMsg(String retMsg) {
				this.retMsg = retMsg;
			}

			/**
			 * 交易状态
			 */
			public EnumRetStatus getRetStatus() {
				return EnumRetStatus.getERetStatus(retStatus);
			}

			/**
			 * 交易状态
			 */
			public void setRetStatus(String retStatus) {
				this.retStatus = retStatus;
			}

			/**
			 * 机构号
			 */
			public String getCorpNo() {
				return corpNo;
			}

			/**
			 * 机构号
			 */
			public void setCorpNo(String corpNo) {
				this.corpNo = corpNo;
			}

			/**
			 * 机构流水号
			 */
			public String getTrxSerno() {
				return trxSerno;
			}

			/**
			 * 机构流水号
			 */
			public void setTrxSerno(String trxSerno) {
				this.trxSerno = trxSerno;
			}

			@Override
			public String toString() {
				return "ReleaseTranRsp[signTime="+signTime+",userId="+userId+",retCode=" + retCode + ",retMsg=" + retMsg + ",retStatus=" + retStatus + ",corpNo=" + corpNo + ",trxSerno=" + trxSerno + "]";
			}
		}

		public ReleaseTranRsp getReleaseTranRsp() {
			return releaseTranRsp;
		}

		public void setReleaseTranRsp(ReleaseTranRsp releaseTranRsp) {
			this.releaseTranRsp = releaseTranRsp;
		}
		
		@Override
		public String toString() {
			return "ReleaseRspMsg[releaseTranRsp="+releaseTranRsp+"]";
		}
	}

	public String getVerify() {
		return verify;
	}

	public void setVerify(String verify) {
		this.verify = verify;
	}

	public String getEncryMsg() {
		return encryMsg;
	}

	public void setEncryMsg(String encryMsg) {
		this.encryMsg = encryMsg;
	}

	public ReleaseRspMsg getReleaseRspMsg() {
		return releaseRspMsg;
	}

	public void setReleaseRspMsg(ReleaseRspMsg releaseRspMsg) {
		this.releaseRspMsg = releaseRspMsg;
	}
	
	@Override
	public String toString() {
		return "ReleaseRsp[verify="+verify+",encryMsg="+encryMsg+",releaseRspMsg="+releaseRspMsg+"]";
	}

}
