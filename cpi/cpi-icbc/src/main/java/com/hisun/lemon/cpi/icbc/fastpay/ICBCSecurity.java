package com.hisun.lemon.cpi.icbc.fastpay;


import java.io.IOException;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.Provider;
import java.security.PublicKey;
import java.security.Security;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.channel.common.lifecycle.LifecycleBase;
import com.hisun.channel.common.lifecycle.LifecycleState;

//import sun.misc.BASE64Decoder;
//import sun.misc.BASE64Encoder;

@SuppressWarnings("restriction")
public class ICBCSecurity extends LifecycleBase implements com.hisun.channel.common.core.Signature {

	private ICBCSecurity() {
	}

	private static final Logger logger = LoggerFactory.getLogger(ICBCSecurity.class);

	//	private static final BASE64Encoder encoder = new BASE64Encoder();
//	private static final BASE64Decoder decoder = new BASE64Decoder();
	private static final Base64 base64=new Base64();

	/**
	 * 加密算法与填充方式
	 */
	public static final String AlGORITHM = "DESede/ECB/PKCS5Padding"; // 定义加密算法,可用
	// DES,DESede,Blowfish

	// 接口文档中附带的测试密钥
	private static final String test3DESCORPKey = "g8ezfA1MgA4gwnNAm5eJqENYC0kHmKdU";

	// 缓存provider
	private static Map<String, Provider> providerMap = new HashMap<String, Provider>();

	private final static String providerName = "BC";

	// 秘钥
	private final static String corp_private_key = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKlJRtH8GZx1dpmJEoxrdF6wGuWF10ylbX6a/5Mnf1lF4iouMrfz93m560d5M0A0aMjoNXVawTKIQNauHN9FAY29v+D9Y320WIzAF3bm6u4/ESvMYpbujKqepEzmCjTUS5q5fEDmMcpmNhxwY91PgO7mFTjxulwtfAzVeuWcxprZAgMBAAECgYB3Yaag5QEGtkuKIQFEp26sa9K79DQN6EecViBQHTWQtli1WFkKIvFuFW/XuqAq8grHTAKPHQ5L3YykGKthxJ8/uG+LxOdZCOe54aGbYmOXYT/6cBaxaPhMCVHwukHeyFrBcXFmNqsE0PJbxt6PQrDRrbtFJtKcZTBySpfOCQPnBQJBAOHJjjR/5wvv+jb6+QTcmW+SDuLryxmwfcKF0sBvjJfpj0Xn5QBLbnzB/tVEYD1WzaOi0PDRUgdXjUGWtyKeyEMCQQC/8EBb9lPcT6GOPN+GrYPOFm5z1z7efoDx/6KeskO467yKYRi8ei2PoA8K3HQsfc0j0lJeC7w7VJjdCNGyzdyzAkEAoStbu/P0vBuv8yERMjw5tl9/CtIfpxXJn1ohR0YZCLMql07hs2Uk2B8uRZPKUt6saXsmRFBWinEfnsrzrN3EuwJAHB5VP2Ox22erEkxmrrNCyPFOUxgodK3xtAIwT7Fr7G5Um60n04zOmejn6z5qoo33fJT2TiLH+6UkE9WT6u7vswJAc7AY/44GTIwNvV06sqRfaQxD9T/spQ9HREE4xh812eUzcKm2r2BE8PAgYf/GxHSk7Ml0vEK73Xl0esA7M5nZQg==";

	// 公钥
	private final static String icbc_public_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5+8FsILxu1vJrh0JeCviqmR7bBbnKiA3eU2kooSi+C+zCHLCg1tL1oxis5Ln6zD4fPAoIA+0FKmHgh0UNUYN+1UrKNCqHlnfjdF81snKihduiL7kDvwc8KX2imm8yVwqa1bdBJRD/pKQHzXa8wmqg2O/pomgqLcgi9NYxCtC+3wIDAQAB";

//	private static String read(InputStream inputStream) {
//		BufferedReader reader = null;
//		String laststr = "";
//		try {
//
//			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
//			reader = new BufferedReader(inputStreamReader);
//			String tempString = null;
//			while ((tempString = reader.readLine()) != null) {
//				laststr += tempString;
//			}
//		} catch (Exception e) {
//			logger.error("ICBCSecurity.read Exception:", e);
//		} finally {
//			if (reader != null) {
//				try {
//					reader.close();
//				} catch (IOException e) {
//					logger.error("ICBCSecurity.read IOException:", e);
//				}
//			}
//		}
//		return laststr;
//	}

	/**
	 * 工行签名算法
	 *
	 * @param respXml
	 *            报文
	 * @return
	 */
	public static String signature(String respXml) {
		try {
			System.out.println("加密报文:" + respXml);
//			byte[] privateKeyBytes = new BASE64Decoder().decodeBuffer(corp_private_key);
			byte[] privateKeyBytes = base64.decode(corp_private_key);
			PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

			// 计算散列值
			MessageDigest digest = MessageDigest.getInstance("MD5");
			digest.update(respXml.getBytes("UTF-8"));
			byte[] hash = digest.digest();

			// 签名
			Signature signature = Signature.getInstance("MD5withRSA");
			signature.initSign(privateKey);
			signature.update(hash);
//			return new BASE64Encoder().encode(signature.sign());
			return new String(base64.encode(signature.sign()));
		} catch (Exception e) {
			logger.error("ICBCSecurity.signature IOException:", e);
		}

		return null;

	}

	/**
	 * 工行签名校验
	 *
	 * @param reqXml
	 *            报文
	 * @param signStr
	 *            签名数据
	 * @return boolean true:校验成功 ; false:校验失败
	 */
	public static boolean icbcVerify(String reqXml, String signStr) {
		boolean result = false;
		try {
			// 设置公钥
//			byte[] publicKeyBytes = new BASE64Decoder().decodeBuffer(icbc_public_key);
			byte[] publicKeyBytes = base64.decode(icbc_public_key);
			X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
			KeyFactory keyFactory = KeyFactory.getInstance("RSA");
			PublicKey publicKey = keyFactory.generatePublic(keySpec);

			// 计算散列值
			MessageDigest digest = MessageDigest.getInstance("MD5");
			digest.update(reqXml.getBytes("UTF-8"));
			byte[] hash = digest.digest();

			// 验证签名
			Signature signature = Signature.getInstance("MD5withRSA");
			signature.initVerify(publicKey);
			signature.update(hash);
//			result = signature.verify(new BASE64Decoder().decodeBuffer(signStr));
			result = signature.verify(base64.decode(signStr));
		} catch (Exception e) {
			logger.error("ICBCSecurity.verify Exception:", e);
		}
		return result;

	}

	/**
	 * 使用3des加密明文
	 *
	 * @param key
	 *            : 密钥
	 * @param src
	 *            : 明文
	 *
	 */
	public static byte[] encrypt(byte[] key, byte[] src) {
		try {
			// 生成密钥
			SecretKey deskey = new SecretKeySpec(key, AlGORITHM);
			// 加密
			Cipher c1 = Cipher.getInstance(AlGORITHM);
			c1.init(Cipher.ENCRYPT_MODE, deskey);
			return c1.doFinal(src);// 在单一方面的加密或解密
		} catch (java.security.NoSuchAlgorithmException e1) {
			logger.error("ICBCSecurity.encrypt NoSuchAlgorithmException:", e1);
		} catch (javax.crypto.NoSuchPaddingException e2) {
			logger.error("ICBCSecurity.encrypt NoSuchPaddingException:", e2);
		} catch (java.lang.Exception e3) {
			logger.error("ICBCSecurity.encrypt Exception:", e3);
		}
		return null;
	}

	/**
	 * 使用3des解密密文
	 *
	 * @param keybyte
	 *            : 密钥
	 * @param src
	 *            : 密文
	 *
	 */
	public static byte[] decrypt(byte[] keybyte, byte[] src) {
		try {
			// 生成密钥
			SecretKey deskey = new SecretKeySpec(keybyte, AlGORITHM);
			// 解密
			Cipher c1 = Cipher.getInstance(AlGORITHM);
			c1.init(Cipher.DECRYPT_MODE, deskey);
			return c1.doFinal(src);
		} catch (java.security.NoSuchAlgorithmException e1) {
			logger.error("ICBCSecurity.decrypt NoSuchAlgorithmException:", e1);
		} catch (javax.crypto.NoSuchPaddingException e2) {
			logger.error("ICBCSecurity.decrypt NoSuchPaddingException:", e2);
		} catch (java.lang.Exception e3) {
			logger.error("ICBCSecurity.decrypt Exception:", e3);
		}

		return null;
	}

	private static byte[] getKey() {
		byte[] key = null;
		if (providerMap.get("provider") == null) {
			if (Security.getProvider(providerName) == null) {
				Security.addProvider(new BouncyCastleProvider());
			}
			providerMap.put("provider", Security.getProvider(providerName));
		} else {
			Security.addProvider(providerMap.get("provider"));
		}

		try {
			// 2.获取原始密钥[24byte]
//			key = decoder.decodeBuffer(test3DESCORPKey);
			key = base64.decode(test3DESCORPKey);
		} catch (Exception e) {
			logger.error("ICBCSecurity.getKey Exception:", e);
		}

		return key;
	}

	/**
	 * 加密字符串
	 *
	 * @param val
	 * @return
	 */
	public static String encrypt(String val) {
		return new String(base64.encode(ICBCSecurity.encrypt(getKey(), val.getBytes())));
	}

	/**
	 * 解密字符串
	 *
	 * @param val
	 * @return
	 */
	public static String decrypt(String val) {
		String deCORPTestString = null;
		try {
			deCORPTestString = new String(ICBCSecurity.decrypt(getKey(), base64.decode(val)));

		} catch (Exception e) {
			logger.error("ICBCSecurity.decrypt Exception:", e);
		}
		return deCORPTestString;
	}

	@Override
	public String sign(String signStr) {
		return ICBCSecurity.signature(signStr);
	}

	@Override
	public boolean verify(String verifyStr, String signStr) {
		return icbcVerify(verifyStr, signStr.split("</Msg>")[1]);
	}

	@Override
	protected void doInit() {

	}

	@Override
	protected void doStart() {
		this.setState(LifecycleState.STARTING);
	}

	@Override
	protected void doStop() {
		this.setState(LifecycleState.STOPPING);
	}

	@Override
	protected void doDestroy() {

	}
}
