package com.hisun.lemon.cpi.icbc.fastpay.req;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.icbc.fastpay.HeadReq;
import com.hisun.lemon.cpi.icbc.fastpay.LocationInfo;
import com.hisun.lemon.cpi.icbc.fastpay.LogisticsInfo;
import com.hisun.lemon.cpi.icbc.fastpay.OrderInfo;
import com.hisun.lemon.cpi.icbc.fastpay.PubReq;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumCurrType;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumIntFcode;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumNote;



/**
 * 支付请求报文
 * 
 * <AUTHOR>
 *
 */
@Validated
@Plain
public class PaymentReq {

	@Nest
	private HeadReq headReq;

	/**
	 * 请求报文区
	 */
	@Nest(ignore=true)
	@SignValue
	private PaymentReqMsg paymentReqMsg;
	
	@Item(before="&cosp=",expr="@paymentReqMsg")
	private String cosp;

	@Sign
	private String sign;
	
	@Xml(root = "Msg",removeLineSeparator=true)
	public static class PaymentReqMsg {

		/**
		 * 请求公共区
		 */
		@Nest(alias = "pub")
		private PubReq pubReq;
		
		/**
		 * 接口名称
		 */
		@Item(parent="pub")
		private String trxCode;

		/**
		 * 请求信息区
		 */
		@Nest(alias = "req")
		private PaymentTranReq paymentTranReq;

		@Validated
		public static class PaymentTranReq {
			/**
			 * 个人协议号
			 */
			@Item
			@NotNull
			private String userId;

			/**
			 * 币种014-美分
			 */
			@Item
			@NotNull
			private String currType;

			/**
			 * 交易金额,单位-美分
			 */
			@Item
			@NotNull
			private int amount;

			/**
			 * 摘要 01-消费; 02-转账; 03-缴费; 04-还款; 041-自动还款; 05-理财; 06-充值; 07红包
			 */
			@Item
			@NotNull
			private String note;

			/**
			 * 网络交易平台
			 */
			@Item
			@NotNull
			private String trxPlat;

			/**
			 * 第三方机构订单号
			 */
			@Item
			@NotNull
			private String orderNo;

			/**
			 * 订单信息
			 */
			@Item(cdata=true)
			@NotNull
			private String orderInfo;

			/**
			 * 位置信息
			 */
			@Item(cdata=true)
			private String locationInfo;

			/**
			 * 物流信息
			 */
			@Item(cdata=true)
			private String logisticsInfo;

			/**
			 * 个人协议号
			 */
			public String getUserId() {
				return userId;
			}

			/**
			 * 个人协议号
			 */
			public void setUserId(String userId) {
				this.userId = userId;
			}

			/**
			 * 币种 014-美分
			 */
			public String getCurrType() {
				return currType;
			}

			/**
			 * 币种 014-美分
			 */
			public void setCurrType(EnumCurrType enumCurrType) {
				this.currType = enumCurrType.getCode();
			}

			/**
			 * 交易金额,单位-美分
			 */
			public int getAmount() {
				return amount;
			}

			/**
			 * 交易金额,单位-美分
			 */
			public void setAmount(int amount) {
				this.amount = amount;
			}

			/**
			 * 摘要
			 */
			public String getNote() {
				return note;
			}

			/**
			 * 摘要 
			 */
			public void setNote(EnumNote enumNote) {
				this.note = enumNote.getCode();
			}

			/**
			 * 网络交易平台
			 */
			public String getTrxPlat() {
				return trxPlat;
			}

			/**
			 * 网络交易平台
			 */
			public void setTrxPlat(String trxPlat) {
				this.trxPlat = trxPlat;
			}

			/**
			 * 第三方机构订单号
			 */
			public String getOrderNo() {
				return orderNo;
			}

			/**
			 * 第三方机构订单号
			 */
			public void setOrderNo(String orderNo) {
				this.orderNo = orderNo;
			}

			/**
			 * 订单信息
			 */
			public String getOrderInfo() {
				return orderInfo;
			}

			/**
			 * 订单信息
			 */
			public void setOrderInfo(List<OrderInfo> orderInfoList) {
				if (orderInfoList != null && orderInfoList.size() > 0) {
					this.orderInfo = JSONArray.toJSONString(orderInfoList);
				}
			}

			/**
			 * 位置信息
			 */
			public String getLocationInfo() {
				return locationInfo;
			}

			/**
			 * 位置信息
			 */
			public void setLocationInfo(LocationInfo locationInfo) {
				if (locationInfo != null) {
					this.locationInfo = JSONObject.toJSONString(locationInfo);
				}
			}

			/**
			 * 物流信息
			 */
			public String getLogisticsInfo() {
				return logisticsInfo;
			}

			/**
			 * 物流信息
			 */
			public void setLogisticsInfo(LogisticsInfo logisticsInfo) {
				if (logisticsInfo != null) {
					this.logisticsInfo = JSONObject.toJSONString(logisticsInfo);
				}

			}
		}

		/**
		 * 请求公共区
		 */
		public PubReq getPubReq() {
			return pubReq;
		}

		/**
		 * 请求公共区
		 */
		public void setPubReq(PubReq pubReq) {
			this.pubReq = pubReq;
		}

		/**
		 * 接口名称
		 */
		public String getTrxCode() {
			return trxCode;
		}

		/**
		 * 接口名称
		 */
		public void setTrxCode(EnumIntFcode enumIntFcode) {
			this.trxCode = enumIntFcode.getCode();
		}
		
		/**
		 * 请求信息区
		 */
		public PaymentTranReq getPaymentTranReq() {
			return paymentTranReq;
		}

		/**
		 * 请求信息区
		 */
		public void setPaymentTranReq(PaymentTranReq paymentTranReq) {
			this.paymentTranReq = paymentTranReq;
		}
	}

	public HeadReq getHeadReq() {
		return headReq;
	}

	public void setHeadReq(HeadReq headReq) {
		this.headReq = headReq;
	}

	/**
	 * 请求报文区
	 */
	public PaymentReqMsg getPaymentReqMsg() {
		return paymentReqMsg;
	}

	/**
	 * 请求报文区
	 */
	public void setPaymentReqMsg(PaymentReqMsg paymentReqMsg) {
		this.paymentReqMsg = paymentReqMsg;
	}
	
	public String getCosp() {
		return cosp;
	}

	public void setCosp(String cosp) {
		this.cosp = cosp;
	}
	
	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}
}
