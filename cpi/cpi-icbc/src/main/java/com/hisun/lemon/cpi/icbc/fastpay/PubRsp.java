package com.hisun.lemon.cpi.icbc.fastpay;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumRetStatus;


/**
 * 响应报文公共参数
 * 
 * <AUTHOR>
 *
 */
public class PubRsp {

	/**
	 * 返回代码
	 */
	@Item
	private String retCode;

	/**
	 * 返回信息
	 */
	@Item
	private String retMsg;

	/**
	 * 交易状态
	 */
	@Item
	private String retStatus;

	/**
	 * 机构号
	 */
	@Item
	private String corpNo;

	/**
	 * 机构流水号
	 */
	@Item
	private String trxSerno;

	/**
	 * 返回代码
	 */
	public String getRetCode() {
		return retCode;
	}

	/**
	 * 返回代码
	 */
	public void setRetCode(String retCode) {
		this.retCode = retCode;
	}

	/**
	 * 返回信息
	 */
	public String getRetMsg() {
		return retMsg;
	}

	/**
	 * 返回信息
	 */
	public void setRetMsg(String retMsg) {
		this.retMsg = retMsg;
	}

	/**
	 * 交易状态
	 */
	public EnumRetStatus getRetStatus() {
		return EnumRetStatus.getERetStatus(retStatus);
	}

	/**
	 * 交易状态
	 */
	public void setRetStatus(String retStatus) {
		this.retStatus = retStatus;
	}

	/**
	 * 机构号
	 */
	public String getCorpNo() {
		return corpNo;
	}

	/**
	 * 机构号
	 */
	public void setCorpNo(String corpNo) {
		this.corpNo = corpNo;
	}

	/**
	 * 机构流水号
	 */
	public String getTrxSerno() {
		return trxSerno;
	}

	/**
	 * 机构流水号
	 */
	public void setTrxSerno(String trxSerno) {
		this.trxSerno = trxSerno;
	}

	@Override
    public String toString() {
		return "PubRsp[retCode=" + retCode + ",retMsg=" + retMsg + ",retStatus=" + retStatus + ",corpNo=" + corpNo + ",trxSerno=" + trxSerno + "]";
	}
}
