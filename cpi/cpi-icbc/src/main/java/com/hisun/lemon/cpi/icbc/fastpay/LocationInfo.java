package com.hisun.lemon.cpi.icbc.fastpay;
/**
 * 位置信息
 * 
 * <AUTHOR>
 *
 */
public class LocationInfo {

	/**
	 * 终端类型 PC-台式机或者笔记本; Mobile-手机; Other-其它渠道
	 */
	private String termType;

	/**
	 * 终端IP,客户支付时所在的ip
	 */
	private String clientIp;

	/**
	 * 所在城市,购物所在城市
	 */
	private String cityName;

	/**
	 * 终端类型 PC-台式机或者笔记本; Mobile-手机; Other-其它渠道
	 */
	public String getTermType() {
		return termType;
	}

	/**
	 * 终端类型 PC-台式机或者笔记本; Mobile-手机; Other-其它渠道
	 */
	public void setTermType(String termType) {
		this.termType = termType;
	}

	/**
	 * 终端IP,客户支付时所在的ip
	 */
	public String getClientIp() {
		return clientIp;
	}

	/**
	 * 终端IP,客户支付时所在的ip
	 */
	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	/**
	 * 所在城市,购物所在城市
	 */
	public String getCityName() {
		return cityName;
	}

	/**
	 * 所在城市,购物所在城市
	 */
	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

}
