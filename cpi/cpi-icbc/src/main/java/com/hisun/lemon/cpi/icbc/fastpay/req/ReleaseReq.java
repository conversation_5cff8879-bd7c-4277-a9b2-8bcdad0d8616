package com.hisun.lemon.cpi.icbc.fastpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.icbc.fastpay.HeadReq;
import com.hisun.lemon.cpi.icbc.fastpay.PubReq;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumIntFcode;



/**
 * 解约报文
 * 
 * <AUTHOR>
 *
 */
@Validated
@Plain
public class ReleaseReq {

	@Nest
	private HeadReq headReq;

	/**
	 * 请求报文区
	 */
	@Nest(ignore=true)
	@SignValue
	private ReleaseReqMsg releaseReqMsg;
	
	@Item(before="&cosp=",expr="@releaseReqMsg")
	private String cosp;
	
	public String getCosp() {
		return cosp;
	}

	public void setCosp(String cosp) {
		this.cosp = cosp;
	}

	/**
	 * 签名
	 */
	@Sign
	private String sign;

	@Xml(root = "Msg",removeLineSeparator=true)
	public static class ReleaseReqMsg {

		/**
		 * 请求公共区
		 */
		@Nest(alias = "pub")
		private PubReq pubReq;
		
		/**
		 * 接口名称
		 */
		@Item(parent="pub")
		private String trxCode;

		/**
		 * 请求信息区
		 */
		@Nest(alias = "req")
		private ReleaseTranReq releaseTranReq;

		@Validated
		public static class ReleaseTranReq {
			/**
			 * 个人协议号
			 */
			@Item
			@NotNull
			private String userId;

			/**
			 * 签约人账户辨识号
			 */
			@Item
			@NotNull
			private String accShtId;

			/**
			 * 短信模板
			 */
			@Item
			private String checkMsg;

			/**
			 * 个人协议号
			 */
			public String getUserId() {
				return userId;
			}

			/**
			 * 个人协议号
			 */
			public void setUserId(String userId) {
				this.userId = userId;
			}

			/**
			 * 签约人账户辨识号
			 */
			public String getAccShtId() {
				return accShtId;
			}

			/**
			 * 签约人账户辨识号
			 */
			public void setAccShtId(String accShtId) {
				this.accShtId = accShtId;
			}

			/**
			 * 短信模板
			 */
			public String getCheckMsg() {
				return checkMsg;
			}

			/**
			 * 短信模板
			 */
			public void setCheckMsg(String checkMsg) {
				this.checkMsg = checkMsg;
			}
		}

		/**
		 * 请求公共区
		 */
		public PubReq getPubReq() {
			return pubReq;
		}

		/**
		 * 请求公共区
		 */
		public void setPubReq(PubReq pubReq) {
			this.pubReq = pubReq;
		}
		
		/**
		 * 接口名称
		 */
		public String getTrxCode() {
			return trxCode;
		}

		/**
		 * 接口名称
		 */
		public void setTrxCode(EnumIntFcode enumIntFcode) {
			this.trxCode = enumIntFcode.getCode();
		}

		/**
		 * 请求信息区
		 */
		public ReleaseTranReq getReleaseTranReq() {
			return releaseTranReq;
		}

		/**
		 * 请求信息区
		 */
		public void setReleaseTranReq(ReleaseTranReq releaseTranReq) {
			this.releaseTranReq = releaseTranReq;
		}
	}

	public HeadReq getHeadReq() {
		return headReq;
	}

	public void setHeadReq(HeadReq headReq) {
		this.headReq = headReq;
	}

	/**
	 * 请求报文区
	 */
	public ReleaseReqMsg getReleaseReqMsg() {
		return releaseReqMsg;
	}

	/**
	 * 请求报文区
	 */
	public void setReleaseReqMsg(ReleaseReqMsg releaseReqMsg) {
		this.releaseReqMsg = releaseReqMsg;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}
}
