package com.hisun.lemon.cpi.icbc.fastpay;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumIntFcode;


@Plain
public class HeadReq {

	/**
	 * 应用代码，送F-ATP
	 */
	@Item(before="appcode=",separator="&")
	private String appcode;

	/**
	 * 机构代码，由工行提供
	 */
	@Item(before="corpcode=",separator="&")
	@NotNull
	private String corpcode;

	/**
	 * 交易代码，送99999
	 */
	@Item(before="trxcode=",separator="&")
	private String trxcode;

	/**
	 * 接口代码
	 */
	@Item(before="intfcode=",separator="&")
	@NotNull
	private String intfcode;

	/**
	 * 预签约和签约时上送签约卡号后2位，其他交易上送用户协议号后2位
	 */
	@Item(before="tailNumber=",separator="&")
	@NotNull
	private String tailNumber;
	
	/**
	 * 应用代码，送F-ATP
	 */
	public String getAppcode() {
		if(StringUtils.isEmpty(appcode)){
			appcode="F-ATP";
		}
		return appcode;
	}

	/**
	 * 应用代码，送F-ATP
	 */
	public void setAppcode(String appcode) {

		this.appcode = appcode;

	}

	/**
	 * 机构代码，由工行提供
	 */
	public String getCorpcode() {
		return corpcode;
	}

	/**
	 * 机构代码，由工行提供
	 */
	public void setCorpcode(String corpcode) {
		this.corpcode = corpcode;
	}

	/**
	 * 交易代码，送99999
	 */
	public String getTrxcode() {
		if (StringUtils.isEmpty(trxcode)) {
			this.trxcode = "99999";
		}
		return trxcode;
	}

	/**
	 * 交易代码，送99999
	 */
	public void setTrxcode(String trxcode) {

		this.trxcode = trxcode;

	}

	/**
	 * 接口代码
	 */
	public String getIntfcode() {
		return intfcode;
	}

	/**
	 * 接口代码
	 */
	public void setIntfcode(EnumIntFcode enumIntFcode) {
		this.intfcode = enumIntFcode.getCode();
	}

	/**
	 * 预签约和签约时上送签约卡号后2位，其他交易上送用户协议号后2位
	 */
	public String getTailNumber() {
		return tailNumber;
	}

	/**
	 * 预签约和签约时上送签约卡号后2位，其他交易上送用户协议号后2位
	 */
	public void setTailNumber(String tailNumber) {
		this.tailNumber = tailNumber;
	}
	
}
