package com.hisun.lemon.cpi.icbc.fastpay;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * User : Rui
 * Date : 2017/8/29
 * Time : 11:24
 **/
@Component(value = "icbcProperties")
@ConfigurationProperties(prefix = "icbc")
@PropertySource("classpath:config/fastpay-icbc.properties")
public class ICBCProperties {

    /**
     * 应用代码，送F-ATP
     */
    private String appcode;

    /**
     * 机构代码，由工行提供
     */
    private String corpcode;

    /**
     * 交易代码，送99999
     */
    private String trxcode;

    /**
     * 机构业务代码
     */
    private String bizCode;

    /**
     * 版本号
     */
    private String version;

    /**
     * 机构协议代码,借记卡
     */
    private String protocolNoDebit;

    /**
     * 机构协议代码,贷记卡
     */
    private String protocolNoCredit;

    /**
     * sftp服务器远程ip
     */
    private String remoteIp;

    /**
     * sftp服务器远程port
     */
    private String remotePort;

    /**
     * sftp服务器连接超时时间
     */
    private String remoteTimeOut;

    /**
     * sftp服务器远程文件所在目录
     */
    private String remoteFilePath;

    /**
     * sftp服务器登录用户
     */
    private String remoteUsername;

    /**
     * sftp服务器登录密码
     */
    private String remotePassword;

    /**
     * 下载文件存放路径
     */
    private String localFilePath;


    public String getAppcode() {
        return appcode;
    }

    public void setAppcode(String appcode) {
        this.appcode = appcode;
    }

    public String getCorpcode() {
        return corpcode;
    }

    public void setCorpcode(String corpcode) {
        this.corpcode = corpcode;
    }

    public String getTrxcode() {
        return trxcode;
    }

    public void setTrxcode(String trxcode) {
        this.trxcode = trxcode;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getProtocolNoDebit() {
        return protocolNoDebit;
    }

    public void setProtocolNoDebit(String protocolNoDebit) {
        this.protocolNoDebit = protocolNoDebit;
    }

    public String getProtocolNoCredit() {
        return protocolNoCredit;
    }

    public void setProtocolNoCredit(String protocolNoCredit) {
        this.protocolNoCredit = protocolNoCredit;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public String getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(String remotePort) {
        this.remotePort = remotePort;
    }

    public String getRemoteTimeOut() {
        return remoteTimeOut;
    }

    public void setRemoteTimeOut(String remoteTimeOut) {
        this.remoteTimeOut = remoteTimeOut;
    }

    public String getRemoteFilePath() {
        return remoteFilePath;
    }

    public void setRemoteFilePath(String remoteFilePath) {
        this.remoteFilePath = remoteFilePath;
    }

    public String getRemoteUsername() {
        return remoteUsername;
    }

    public void setRemoteUsername(String remoteUsername) {
        this.remoteUsername = remoteUsername;
    }

    public String getRemotePassword() {
        return remotePassword;
    }

    public void setRemotePassword(String remotePassword) {
        this.remotePassword = remotePassword;
    }

    public String getLocalFilePath() {
        return localFilePath;
    }

    public void setLocalFilePath(String localFilePath) {
        this.localFilePath = localFilePath;
    }
}
