package com.hisun.lemon.cpi.icbc.fastpay;

import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;




/**
 * 
 * <AUTHOR>
 *
 */
@Validated
public class PubReq {

	/**
	 * 机构号
	 */
	@Item
	private String corpNo;

	/**
	 * 机构协议代码,预签约和签约时上送：99 其他交易上送： 借记卡送98， 贷记卡送99
	 */
	@Item
	private String protocolNo;

	/**
	 * 机构业务代码
	 */
	@Item
	private String bizCode;

	/**
	 * 版本号
	 */
	@Item
	private String version;

	/**
	 * 机构交易日期
	 */
	@Item
	private String trxDate;

	/**
	 * 机构交易时间
	 */
	@Item
	private String trxTime;

	/**
	 * 机构交易流水号
	 */
	@Item
	private String trxSerno;

	/**
	 * 机构号
	 */
	public String getCorpNo() {
		return corpNo;
	}

	/**
	 * 机构号
	 */
	public void setCorpNo(String corpNo) {
		this.corpNo = corpNo;
	}

	/**
	 * 机构协议代码,预签约和签约时上送：99 其他交易上送： 借记卡送98， 贷记卡送99
	 */
	public String getProtocolNo() {
		return protocolNo;
	}

	/**
	 * 机构协议代码,预签约和签约时上送：99 其他交易上送： 借记卡送98， 贷记卡送99
	 */
	public void setProtocolNo(String protocolNo) {
		this.protocolNo = protocolNo;
	}

	/**
	 * 机构业务代码
	 */
	public String getBizCode() {
		if (StringUtils.isEmpty(bizCode)) {
			this.bizCode = "99";
		}
		return bizCode;
	}

	/**
	 * 机构业务代码
	 */
	public void setBizCode(String bizCode) {
		this.bizCode = bizCode;
	}

	/**
	 * 版本号
	 */
	public String getVersion() {
		if (StringUtils.isEmpty(version)) {
			this.version = "1.0.0";
		}
		return version;
	}

	/**
	 * 版本号
	 */
	public void setVersion(String version) {

		this.version = version;

	}

	/**
	 * 机构交易日期
	 */
	public String getTrxDate() {
		return trxDate;
	}

	/**
	 * 机构交易日期
	 */
	public void setTrxDate(String trxDate) {
		this.trxDate = trxDate;
	}

	/**
	 * 机构交易时间
	 */
	public String getTrxTime() {
		return trxTime;
	}

	/**
	 * 机构交易时间
	 */
	public void setTrxTime(String trxTime) {
		this.trxTime = trxTime;
	}

	/**
	 * 机构交易流水号
	 */
	public String getTrxSerno() {
		return trxSerno;
	}

	/**
	 * 机构交易流水号
	 */
	public void setTrxSerno(String trxSerno) {
		this.trxSerno = trxSerno;
	}
}
