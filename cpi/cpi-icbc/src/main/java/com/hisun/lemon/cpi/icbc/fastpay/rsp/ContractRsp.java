package com.hisun.lemon.cpi.icbc.fastpay.rsp;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.VerifyValue.ValueType;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumRetStatus;


/**
 * 签约响应报文
 * 
 * <AUTHOR>
 *
 */
@Validated
@Plain(root = "Root")
public class ContractRsp {

	@Verify(expr = "@Root")
	private String verify;

	@Item(expr = "INVOKE('icbcDecrypt',@Root)")
	@VerifyValue(type = ValueType.FIELD)
	private String encryMsg;

	@NotNull
	@Nest(expr = "$encryMsg")
	private ContractRspMsg contractRspMsg;
	

	@Xml(root = "Msg")
	public static class ContractRspMsg {

		@Nest(alias="resp")
		private ContractTranRsp contractTranRsp;

		public static class ContractTranRsp {

			/**
			 * 返回代码
			 */
			@Item
			private String retCode;

			/**
			 * 返回信息
			 */
			@Item
			private String retMsg;

			/**
			 * 交易状态
			 */
			@Item
			private String retStatus;

			/**
			 * 机构号
			 */
			@Item
			private String corpNo;

			/**
			 * 机构流水号
			 */
			@Item
			private String trxSerno;

			/**
			 * 签约时间
			 */
			@Item
			private String signTime;

			/**
			 * 个人协议号
			 */
			@Item
			private String userId;

			/**
			 * 卡种
			 */
			@Item
			private String cardType;

			/**
			 * 返回代码
			 */
			public String getRetCode() {
				return retCode;
			}

			/**
			 * 返回代码
			 */
			public void setRetCode(String retCode) {
				this.retCode = retCode;
			}

			/**
			 * 返回信息
			 */
			public String getRetMsg() {
				return retMsg;
			}

			/**
			 * 返回信息
			 */
			public void setRetMsg(String retMsg) {
				this.retMsg = retMsg;
			}

			/**
			 * 交易状态
			 */
			public EnumRetStatus getRetStatus() {
				return EnumRetStatus.getERetStatus(retStatus);
			}

			/**
			 * 交易状态
			 */
			public void setRetStatus(String retStatus) {
				this.retStatus = retStatus;
			}

			/**
			 * 机构号
			 */
			public String getCorpNo() {
				return corpNo;
			}

			/**
			 * 机构号
			 */
			public void setCorpNo(String corpNo) {
				this.corpNo = corpNo;
			}

			/**
			 * 机构流水号
			 */
			public String getTrxSerno() {
				return trxSerno;
			}

			/**
			 * 机构流水号
			 */
			public void setTrxSerno(String trxSerno) {
				this.trxSerno = trxSerno;
			}

			/**
			 * 签约时间
			 */
			public String getSignTime() {
				return signTime;
			}

			/**
			 * 签约时间
			 */
			public void setSignTime(String signTime) {
				this.signTime = signTime;
			}

			/**
			 * 个人协议号
			 */
			public String getUserId() {
				return userId;
			}

			/**
			 * 个人协议号
			 */
			public void setUserId(String userId) {
				this.userId = userId;
			}

			/**
			 * 卡种
			 */
			public String getCardType() {
				return cardType;
			}

			/**
			 * 卡种
			 */
			public void setCardType(String cardType) {
				this.cardType = cardType;
			}
			
			@Override
			public String toString() {
				return "ContractTranRsp[signTime="+signTime+",userId="+userId+",cardType="+cardType+",retCode=" + retCode + ",retMsg=" + retMsg + ",retStatus=" + retStatus + ",corpNo=" + corpNo + ",trxSerno=" + trxSerno + "]";
			}
		}
	 
		public ContractTranRsp getContractTranRsp() {
			return contractTranRsp;
		}

		public void setContractTranRsp(ContractTranRsp contractTranRsp) {
			this.contractTranRsp = contractTranRsp;
		}
		
		@Override
		public String toString(){
			return "ContractRspMsg[contractTranRsp="+contractTranRsp+"]";
		}
	
	}

	public String getVerify() {
		return verify;
	}

	public void setVerify(String verify) {
		this.verify = verify;
	}

	public String getEncryMsg() {
		return encryMsg;
	}

	public void setEncryMsg(String encryMsg) {
		this.encryMsg = encryMsg;
	}

	public ContractRspMsg getContractRspMsg() {
		return contractRspMsg;
	}

	public void setContractRspMsg(ContractRspMsg contractRspMsg) {
		this.contractRspMsg = contractRspMsg;
	}
	
	@Override
	public String toString(){
		return "ContractRsp[verify="+verify+",encryMsg="+encryMsg+",contractRspMsg="+contractRspMsg+"]";
	}

}
