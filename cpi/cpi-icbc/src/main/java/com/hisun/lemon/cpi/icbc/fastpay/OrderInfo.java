package com.hisun.lemon.cpi.icbc.fastpay;

/**
 * 订单信息
 * 
 * <AUTHOR>
 *
 */
public class OrderInfo {

	/**
	 * 末端商家名称,必输，只支持英文和泰文
	 */
	private String sellerName;

	/**
	 * 商户类别,采用银联MCC码,可为空
	 */
	private String sellerType;

	/**
	 * 物品名称,可为空
	 */
	private String goods;

	/**
	 * 末端商家名称,必输，只支持英文和泰文
	 */
	public String getSellerName() {
		return sellerName;
	}

	/**
	 * 末端商家名称,必输，只支持英文和泰文
	 */
	public void setSellerName(String sellerName) {
		this.sellerName = sellerName;
	}

	/**
	 * 商户类别,采用银联MCC码,可为空
	 */
	public String getSellerType() {
		return sellerType;
	}

	/**
	 * 商户类别,采用银联MCC码,可为空
	 */
	public void setSellerType(String sellerType) {
		this.sellerType = sellerType;
	}

	/**
	 * 物品名称,可为空
	 */
	public String getGoods() {
		return goods;
	}

	/**
	 * 物品名称,可为空
	 */
	public void setGoods(String goods) {
		this.goods = goods;
	}
}
