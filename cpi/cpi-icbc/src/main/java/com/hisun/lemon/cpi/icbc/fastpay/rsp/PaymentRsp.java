package com.hisun.lemon.cpi.icbc.fastpay.rsp;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.VerifyValue.ValueType;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumRetStatus;

/**
 * 支付响应报文
 * 
 * <AUTHOR>
 *
 */
@Validated
@Plain(root = "Root")
public class PaymentRsp {

	@Verify(expr = "@Root")
	private String verify;

	@Item(expr = "INVOKE('icbcDecrypt',@Root)")
	@VerifyValue(type = ValueType.FIELD)
	private String encryMsg;

	@NotNull
	@Nest(expr = "$encryMsg")
	private PaymentRspMsg paymentRspMsg;


	@Xml(root = "Msg")
	public static class PaymentRspMsg {

		@Nest(alias = "resp")
		private PaymentTranRsp paymentTranRsp;

		public static class PaymentTranRsp {

			/**
			 * 银行交易检索号
			 */
			@Item
			private String bankSerno;

			/**
			 * 银行交易日期
			 */
			@Item
			private String bankTrxDate;

			/**
			 * 返回代码
			 */
			@Item
			private String retCode;

			/**
			 * 返回信息
			 */
			@Item
			private String retMsg;

			/**
			 * 交易状态
			 */
			@Item
			private String retStatus;

			/**
			 * 机构号
			 */
			@Item
			private String corpNo;

			/**
			 * 机构流水号
			 */
			@Item
			private String trxSerno;

			/**
			 * 银行交易检索号
			 */
			public String getBankSerno() {
				return bankSerno;
			}

			/**
			 * 银行交易检索号
			 */
			public void setBankSerno(String bankSerno) {
				this.bankSerno = bankSerno;
			}

			/**
			 * 银行交易日期
			 */
			public String getBankTrxDate() {
				return bankTrxDate;
			}

			/**
			 * 银行交易日期
			 */
			public void setBankTrxDate(String bankTrxDate) {
				this.bankTrxDate = bankTrxDate;
			}

			/**
			 * 返回代码
			 */
			public String getRetCode() {
				return retCode;
			}

			/**
			 * 返回代码
			 */
			public void setRetCode(String retCode) {
				this.retCode = retCode;
			}

			/**
			 * 返回信息
			 */
			public String getRetMsg() {
				return retMsg;
			}

			/**
			 * 返回信息
			 */
			public void setRetMsg(String retMsg) {
				this.retMsg = retMsg;
			}

			/**
			 * 交易状态
			 */
			public EnumRetStatus getRetStatus() {
				return EnumRetStatus.getERetStatus(retStatus);
			}

			/**
			 * 交易状态
			 */
			public void setRetStatus(String retStatus) {
				this.retStatus = retStatus;
			}

			/**
			 * 机构号
			 */
			public String getCorpNo() {
				return corpNo;
			}

			/**
			 * 机构号
			 */
			public void setCorpNo(String corpNo) {
				this.corpNo = corpNo;
			}

			/**
			 * 机构流水号
			 */
			public String getTrxSerno() {
				return trxSerno;
			}

			/**
			 * 机构流水号
			 */
			public void setTrxSerno(String trxSerno) {
				this.trxSerno = trxSerno;
			}
			
			@Override
			public String toString() {
				return "PaymentTranRsp[bankSerno="+bankSerno+",bankTrxDate="+bankTrxDate+",retCode=" + retCode + ",retMsg=" + retMsg + ",retStatus=" + retStatus + ",corpNo=" + corpNo + ",trxSerno=" + trxSerno + "]";
			}
		}
	
		public PaymentTranRsp getPaymentTranRsp() {
			return paymentTranRsp;
		}

		public void setPaymentTranRsp(PaymentTranRsp paymentTranRsp) {
			this.paymentTranRsp = paymentTranRsp;
		}
		
		@Override
		public String toString(){
			return "PaymentRspMsg[paymentTranRsp="+paymentTranRsp+"]";
		}
	}

	public String getVerify() {
		return verify;
	}

	public void setVerify(String verify) {
		this.verify = verify;
	}

	public String getEncryMsg() {
		return encryMsg;
	}

	public void setEncryMsg(String encryMsg) {
		this.encryMsg = encryMsg;
	}

	public PaymentRspMsg getPaymentRspMsg() {
		return paymentRspMsg;
	}

	public void setPaymentRspMsg(PaymentRspMsg paymentRspMsg) {
		this.paymentRspMsg = paymentRspMsg;
	}
	
	@Override
	public String toString(){
		return "PaymentRsp[verify="+verify+",encryMsg="+encryMsg+",paymentRspMsg="+paymentRspMsg+"]";
	}

}
