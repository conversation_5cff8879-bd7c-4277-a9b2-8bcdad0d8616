package com.hisun.lemon.cpi.icbc.fastpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.icbc.fastpay.HeadReq;
import com.hisun.lemon.cpi.icbc.fastpay.PubReq;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumCurrType;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumIntFcode;


/**
 * 退款请求报文
 * 
 * <AUTHOR>
 *
 */
@Validated
@Plain
public class RefundReq {

	@Nest
	private HeadReq headReq;

	/**
	 * 请求报文区
	 */
	@Nest(ignore=true)
	@SignValue
	private RefundReqMsg refundReqMsg;
	
	@Item(before="&cosp=",expr="@refundReqMsg")
	private String cosp;
	
	public String getCosp() {
		return cosp;
	}

	public void setCosp(String cosp) {
		this.cosp = cosp;
	}

	/**
	 * 签名
	 */
	@Sign
	private String sign;

	@Xml(root="Msg",removeLineSeparator=true)
	public static class RefundReqMsg {

		/**
		 * 请求公共区
		 */
		@Nest(alias="pub")
		private PubReq pubReq;
		
		/**
		 * 接口名称
		 */
		@Item(parent="pub")
		private String trxCode;
		
		/**
		 * 请求信息区
		 */
		@Nest(alias="req")
		private RefundTranReq refundTranReq;

		@Validated
		public static class RefundTranReq{

			/**
			 * 个人协议号
			 */
			@Item
			@NotNull
			private String userId;

			/**
			 * 原银行交易日期
			 */
			@Item
			@NotNull
			private String origBankTrxDate;

			/**
			 * 原机构交易日期
			 */
			@Item
			@NotNull
			private String origTrxDate;

			/**
			 * 原机构交易流水号
			 */
			@Item
			@NotNull
			private String origTrxSerno;

			/**
			 * 交易币种014-美分
			 */
			@Item
			@NotNull
			private String currType;

			/**
			 * 退款金额美分
			 */
			@Item
			@NotNull
			private int amount;

			/**
			 * 摘要,描述退货原因
			 */
			@Item
			@NotNull
			private String note;

			/**
			 * 个人协议号
			 */
			public String getUserId() {
				return userId;
			}

			/**
			 * 个人协议号
			 */
			public void setUserId(String userId) {
				this.userId = userId;
			}

			/**
			 * 原银行交易日期
			 */
			public String getOrigBankTrxDate() {
				return origBankTrxDate;
			}

			/**
			 * 原银行交易日期
			 */
			public void setOrigBankTrxDate(String origBankTrxDate) {
				this.origBankTrxDate = origBankTrxDate;
			}

			/**
			 * 原机构交易日期
			 */
			public String getOrigTrxDate() {
				return origTrxDate;
			}

			/**
			 * 原机构交易日期
			 */
			public void setOrigTrxDate(String origTrxDate) {
				this.origTrxDate = origTrxDate;
			}

			/**
			 * 原机构交易流水号
			 */
			public String getOrigTrxSerno() {
				return origTrxSerno;
			}

			/**
			 * 原机构交易流水号
			 */
			public void setOrigTrxSerno(String origTrxSerno) {
				this.origTrxSerno = origTrxSerno;
			}

			/**
			 * 交易币种
			 */
			public String getCurrType() {
				return currType;
			}

			/**
			 * 交易币种
			 */
			public void setCurrType(EnumCurrType enumCurrType) {
				this.currType = enumCurrType.getCode();
			}

			/**
			 * 退款金额美分
			 */
			public int getAmount() {
				return amount;
			}

			/**
			 * 退款金额美分
			 */
			public void setAmount(int amount) {
				this.amount = amount;
			}

			/**
			 * 摘要,描述退货原因
			 */
			public String getNote() {
				return note;
			}

			/**
			 * 摘要,描述退货原因
			 */
			public void setNote(String note) {
				this.note = note;
			}
		}
		
		/**
		 * 请求公共区
		 */
		public PubReq getPubReq() {
			return pubReq;
		}

		/**
		 * 请求公共区
		 */
		public void setPubReq(PubReq pubReq) {
			this.pubReq = pubReq;
		}
		
		/**
		 * 接口名称
		 */
		public String getTrxCode() {
			return trxCode;
		}

		/**
		 * 接口名称
		 */
		public void setTrxCode(EnumIntFcode enumIntFcode) {
			this.trxCode = enumIntFcode.getCode();
		}
		
		/**
		 * 请求信息区
		 */
		public RefundTranReq getRefundTranReq() {
			return refundTranReq;
		}

		/**
		 * 请求信息区
		 */
		public void setRefundTranReq(RefundTranReq refundTranReq) {
			this.refundTranReq = refundTranReq;
		}
	}

	public HeadReq getHeadReq() {
		return headReq;
	}

	public void setHeadReq(HeadReq headReq) {
		this.headReq = headReq;
	}

	/**
	 * 请求报文区
	 * @return
	 */
	public RefundReqMsg getRefundReqMsg() {
		return refundReqMsg;
	}

	/**
	 * 请求报文区
	 * @return
	 */
	public void setRefundReqMsg(RefundReqMsg refundReqMsg) {
		this.refundReqMsg = refundReqMsg;
	}
	
	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}
}
