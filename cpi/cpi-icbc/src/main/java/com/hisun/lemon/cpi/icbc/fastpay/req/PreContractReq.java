package com.hisun.lemon.cpi.icbc.fastpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.icbc.fastpay.HeadReq;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCSecurity;
import com.hisun.lemon.cpi.icbc.fastpay.PubReq;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumIdType;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumIntFcode;


/**
 * 预签约请求报文
 * 
 * <AUTHOR>
 *
 */
@Validated
@Plain
public class PreContractReq {

	@Nest
	private HeadReq headReq;

	/**
	 * 请求报文区
	 */
	@Nest(ignore=true)
	@SignValue
	private PreContractReqMsg preContractReqMsg;
	
	@Item(before="&cosp=",expr="@preContractReqMsg")
	private String cosp;
	
	public String getCosp() {
		return cosp;
	}

	public void setCosp(String cosp) {
		this.cosp = cosp;
	}

	/**
	 * 签名
	 */
	@Sign
	private String sign;

	@Xml(root = "Msg",removeLineSeparator=true)
	public static class PreContractReqMsg {
		
		/**
		 * 请求公共区
		 */
		@Nest(alias="pub")
		private PubReq pubMsg;
		
		/**
		 * 接口名称
		 */
		@Item(parent="pub")
		private String trxCode;
		
		/**
		 * 请求信息区
		 */
		@Nest(alias="req")
		private PreContractTranReq preContractTranReq;

		@Validated
		public static class PreContractTranReq {

			/**
			 * 个人卡号
			 */
			@Item
			@NotNull
			private String accNo;

			/**
			 * 个人户名
			 */
			@Item
			@NotNull
			private String accName;

			/**
			 * 证件类型
			 */
			@Item
			@NotNull
			private String idType;

			/**
			 * 证件号码
			 */
			@Item
			@NotNull
			private String idNo;

			/**
			 * 手机号码
			 */
			@Item
			@NotNull
			private String mobile;

			/**
			 * 短信模板
			 */
			@Item
			private String checkMsg;

			/**
			 * 个人卡号
			 */
			public String getAccNo() {
				return accNo;
			}

			/**
			 * 个人卡号
			 */
			public void setAccNo(String accNo) {
				this.accNo = ICBCSecurity.encrypt(accNo);
			}

			/**
			 * 个人户名
			 */
			public String getAccName() {
				return accName;
			}

			/**
			 * 个人户名
			 */
			public void setAccName(String accName) {
				this.accName = accName;
			}

			/**
			 * 证件类型
			 */
			public String getIdType() {
				return idType;
			}

			/**
			 * 证件类型
			 */
			public void setIdType(EnumIdType enumIdType) {
				this.idType = enumIdType.getCode();
			}

			/**
			 * 证件号码
			 */
			public String getIdNo() {
				return idNo;
			}

			/**
			 * 证件号码
			 */
			public void setIdNo(String idNo) {
				this.idNo =ICBCSecurity.encrypt(idNo);
			}

			/**
			 * 手机号码
			 */
			public String getMobile() {
				return mobile;
			}

			/**
			 * 手机号码
			 */
			public void setMobile(String mobile) {
				this.mobile = ICBCSecurity.encrypt(mobile);
			}

			/**
			 * 短信模板
			 */
			public String getCheckMsg() {
				return checkMsg;
			}

			/**
			 * 短信模板
			 */
			public void setCheckMsg(String checkMsg) {
				this.checkMsg = checkMsg;
			}
		}

		/**
		 * 请求公共区
		 */
		public PubReq getPubMsg() {
			return pubMsg;
		}

		/**
		 * 请求公共区
		 */
		public void setPubMsg(PubReq pubMsg) {
			this.pubMsg = pubMsg;
		}

		/**
		 * 接口名称
		 */
		public String getTrxCode() {
			return trxCode;
		}

		/**
		 * 接口名称
		 */
		public void setTrxCode(EnumIntFcode enumIntFcode) {
			this.trxCode = enumIntFcode.getCode();
		}
		
		/**
		 * 请求信息区
		 */
		public PreContractTranReq getPreContractTranReq() {
			return preContractTranReq;
		}

		/**
		 * 请求信息区
		 */
		public void setPreContractTranReq(PreContractTranReq preContractTranReq) {
			this.preContractTranReq = preContractTranReq;
		}
	}

	public HeadReq getHeadReq() {
		return headReq;
	}

	public void setHeadReq(HeadReq headReq) {
		this.headReq = headReq;
	}

	/**
	 * 请求报文区
	 */
	public PreContractReqMsg getPreContractReqMsg() {
		return preContractReqMsg;
	}

	/**
	 * 请求报文区
	 */
	public void setPreContractReqMsg(PreContractReqMsg preContractReqMsg) {
		this.preContractReqMsg = preContractReqMsg;
	}
	
	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}
}
