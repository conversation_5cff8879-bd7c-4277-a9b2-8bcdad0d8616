package com.hisun.lemon.cpi;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.hisun.channel.common.core.Signature;
import com.hisun.channel.parse.Marshaller;
import com.hisun.channel.parse.MarshallerFactory;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCApi;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumCurrType;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumNote;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumSource;
import com.hisun.lemon.cpi.icbc.fastpay.HeadReq;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCSecurity;
import com.hisun.lemon.cpi.icbc.fastpay.OrderInfo;
import com.hisun.lemon.cpi.icbc.fastpay.PubReq;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumIdType;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCEnumCommon.EnumIntFcode;
import com.hisun.lemon.cpi.icbc.fastpay.req.ContractReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.ContractReq.ContractReqMsg;
import com.hisun.lemon.cpi.icbc.fastpay.req.ContractReq.ContractReqMsg.ContractTranReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.PaymentReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.PaymentReq.PaymentReqMsg;
import com.hisun.lemon.cpi.icbc.fastpay.req.PaymentReq.PaymentReqMsg.PaymentTranReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.PreContractReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.PreContractReq.PreContractReqMsg;
import com.hisun.lemon.cpi.icbc.fastpay.req.PreContractReq.PreContractReqMsg.PreContractTranReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.RefundReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.RefundReq.RefundReqMsg;
import com.hisun.lemon.cpi.icbc.fastpay.req.RefundReq.RefundReqMsg.RefundTranReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.ReleaseReq;
import com.hisun.lemon.cpi.icbc.fastpay.req.ReleaseReq.ReleaseReqMsg;
import com.hisun.lemon.cpi.icbc.fastpay.req.ReleaseReq.ReleaseReqMsg.ReleaseTranReq;
import com.hisun.lemon.cpi.icbc.fastpay.rsp.ContractRsp;
import com.hisun.lemon.cpi.icbc.fastpay.rsp.PaymentRsp;
import com.hisun.lemon.cpi.icbc.fastpay.rsp.PreContractRsp;
import com.hisun.lemon.cpi.icbc.fastpay.rsp.RefundRsp;
import com.hisun.lemon.cpi.icbc.fastpay.rsp.ReleaseRsp;
import com.hisun.channel.parse.AbstractMarshaller;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ObjectToPlainTest {

	@Resource
	private ICBCApi api;

	LocalDateTime localDate = LocalDateTime.now();
	DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyyMMdd");
	DateTimeFormatter formatterTime = DateTimeFormatter.ofPattern("HHmmss");

	/**
	 * 预签约
	 */
	@Test
	@Ignore
	public void preContractReqTest() {
		HeadReq headReq = new HeadReq();
		headReq.setCorpcode("1085");
		headReq.setIntfcode(EnumIntFcode.PRECONTRACT);
		headReq.setTailNumber("06");
		headReq.setAppcode("F-ATP");
		headReq.setTrxcode("99999");

		PubReq pubMsg = new PubReq();
		// pubMsg.setTrxCode("40010");
		pubMsg.setBizCode("99");
		pubMsg.setCorpNo("1085");
		pubMsg.setProtocolNo("99");
		pubMsg.setTrxDate(localDate.format(formatterDate));
		pubMsg.setTrxSerno("1");
		pubMsg.setTrxTime(localDate.format(formatterTime));

		PreContractReq.PreContractReqMsg.PreContractTranReq preContractTranReq = new PreContractTranReq();
		preContractTranReq.setAccName("XI XXEXU");
		preContractTranReq.setAccNo("6214342000030606");
		preContractTranReq.setCheckMsg("1");
		preContractTranReq.setIdNo("G84684302");
		preContractTranReq.setIdType(EnumIdType.PASSPORT);
		preContractTranReq.setMobile("855888580326");

		PreContractReq.PreContractReqMsg preContractReqMsg = new PreContractReqMsg();
		preContractReqMsg.setPubMsg(pubMsg);
		preContractReqMsg.setTrxCode(EnumIntFcode.PRECONTRACT);
		preContractReqMsg.setPreContractTranReq(preContractTranReq);

		PreContractReq preContractReq = new PreContractReq();
		preContractReq.setHeadReq(headReq);
		preContractReq.setPreContractReqMsg(preContractReqMsg);

//		Marshaller<PreContractReq> marshaller = MarshallerFactory.getMarshaller(PreContractReq.class, false);
//		((AbstractMarshaller<?>) marshaller).setSignature(new Signature() {
//
//			@Override
//			public String sign(String signStr) {
//				return ICBCSecurity.signature(signStr);
//			}
//
//			@Override
//			public boolean verify(String verifyStr, String signStr) {
//				return false;
//			}
//		});
//		marshaller.analyse();
//		byte[] bytes = marshaller.marshal(preContractReq);
//		System.out.println("==" + new String(bytes));

		 PreContractRsp rsp = api.doSend(preContractReq,EnumSource.precontract);
		 System.out.println(rsp);
	}

	/**
	 * 签约
	 */
	@Test
	@Ignore
	public void contractTest() {

		HeadReq headReq = new HeadReq();
		headReq.setCorpcode("1085");
		headReq.setIntfcode(EnumIntFcode.PRECONTRACT);
		headReq.setTailNumber("06");
		headReq.setAppcode("F-ATP");
		headReq.setTrxcode("99999");

		PubReq pubMsg = new PubReq();
		pubMsg.setBizCode("99");
		pubMsg.setCorpNo("1085");
		pubMsg.setProtocolNo("99");
		pubMsg.setTrxDate(localDate.format(formatterDate));
		pubMsg.setTrxSerno("223");
		pubMsg.setTrxTime(localDate.format(formatterTime));

		ContractTranReq contractTranReq = new ContractTranReq();
		contractTranReq.setAccName("XI XXEXU");
		contractTranReq.setAccNo("6214342000030606");
		contractTranReq.setIdType(EnumIdType.PASSPORT);
		contractTranReq.setMobile("855888580326");
		contractTranReq.setVerifyCode("639629");
		contractTranReq.setIdNo("G84684302");

		ContractReqMsg contractReqMsg = new ContractReqMsg();
		contractReqMsg.setPubMsg(pubMsg);
		contractReqMsg.setTrxCode(EnumIntFcode.CONTRACT);
		contractReqMsg.setContractTranReq(contractTranReq);

		ContractReq req = new ContractReq();
		req.setHeadReq(headReq);
		req.setContractReqMsg(contractReqMsg);

		// Marshaller<ContractReq> marshaller =
		// MarshallerFactory.getMarshaller(ContractReq.class, false);
		// ((AbstractMarshaller<?>) marshaller).setSignature(new Signature() {
		//
		// @Override
		// public String sign(String signStr) {
		// return ICBCSecurity.signature(signStr);
		// }
		//
		// @Override
		// public boolean verify(String verifyStr, String signStr) {
		// return false;
		// }
		// });
		// marshaller.analyse();
		// byte[] bytes = marshaller.marshal(req);
		// System.out.println("==" + new String(bytes));

		ContractRsp rsp = api.doSend(req, EnumSource.contract);
		System.out.println(rsp);
	}

	/**
	 * 支付
	 */
	@Test
	@Ignore
	public void paymentTest() {

		HeadReq headReq = new HeadReq();
		headReq.setCorpcode("1085");
		headReq.setIntfcode(EnumIntFcode.PAYMENT);
		headReq.setTailNumber("06");
		headReq.setAppcode("F-ATP");
		headReq.setTrxcode("99999");

		PubReq pubReq = new PubReq();
		pubReq.setBizCode("99");
		pubReq.setCorpNo("1085");
		pubReq.setProtocolNo("98");
		pubReq.setTrxDate(localDate.format(formatterDate));
		pubReq.setTrxSerno("1112");
		pubReq.setTrxTime(localDate.format(formatterTime));

		// LocationInfo locationInfo = new LocationInfo();
		// locationInfo.setCityName("asd");
		// locationInfo.setClientIp("dsa");
		// locationInfo.setTermType("gege");
		//
		// LogisticsInfo logisticsInfo = new LogisticsInfo();
		// logisticsInfo.setAddress("dsa");
		// logisticsInfo.setLogisticsCompany("ewegwg");
		// logisticsInfo.setMobile("fqfwqf");
		// logisticsInfo.setRecipient("2112");

		List<OrderInfo> orderInfoList = new ArrayList<OrderInfo>();
		OrderInfo orderInfo = new OrderInfo();
		// orderInfo.setGoods("fs");
		orderInfo.setSellerName("seatel");
		// orderInfo.setSellerType("yjtjyty");
		orderInfoList.add(orderInfo);

		PaymentReq.PaymentReqMsg.PaymentTranReq paymentTranReq = new PaymentTranReq();
		paymentTranReq.setAmount(10);
		paymentTranReq.setCurrType(EnumCurrType.DOLLAR);
		// paymentTranReq.setLocationInfo(locationInfo);
		// paymentTranReq.setLogisticsInfo(logisticsInfo);
		paymentTranReq.setNote(EnumNote.CONSUME);
		paymentTranReq.setOrderInfo(orderInfoList);
		paymentTranReq.setOrderNo("111113");
		paymentTranReq.setTrxPlat("seatel");
		paymentTranReq.setUserId("G903005002843401261606");

		PaymentReq.PaymentReqMsg paymentReqMsg = new PaymentReqMsg();
		paymentReqMsg.setPubReq(pubReq);
		paymentReqMsg.setTrxCode(EnumIntFcode.PAYMENT);
		paymentReqMsg.setPaymentTranReq(paymentTranReq);

		PaymentReq req = new PaymentReq();
		req.setHeadReq(headReq);
		req.setPaymentReqMsg(paymentReqMsg);

		// Marshaller<PaymentReq> marshaller =
		// MarshallerFactory.getMarshaller(PaymentReq.class, false);
		// ((AbstractMarshaller<?>) marshaller).setSignature(new Signature() {
		//
		// @Override
		// public String sign(String signStr) {
		// return signStr;
		// }
		//
		// @Override
		// public boolean verify(String verifyStr, String signStr) {
		// return false;
		// }
		// });
		// marshaller.analyse();
		// byte[] bytes = marshaller.marshal(paymentReq);
		// System.out.println("==" + new String(bytes));
		PaymentRsp rsp = api.doSend(req, EnumSource.payment);
	}

	@Test
	@Ignore
	public void refundTest() {
		HeadReq headReq = new HeadReq();
		headReq.setCorpcode("1085");
		headReq.setIntfcode(EnumIntFcode.REFUND);
		headReq.setTailNumber("06");
		headReq.setAppcode("F-ATP");
		headReq.setTrxcode("99999");

		PubReq pubReq = new PubReq();
		pubReq.setBizCode("99");
		pubReq.setCorpNo("1085");
		pubReq.setProtocolNo("98");
		pubReq.setTrxDate(localDate.format(formatterDate));
		pubReq.setTrxSerno("3333");
		pubReq.setTrxTime(localDate.format(formatterTime));

		RefundReq.RefundReqMsg.RefundTranReq refundTranReq = new RefundTranReq();
		refundTranReq.setAmount(10);
		refundTranReq.setCurrType(EnumCurrType.DOLLAR);
		refundTranReq.setNote("aaaa");
		refundTranReq.setOrigBankTrxDate("********");
		refundTranReq.setOrigTrxDate("********");
		refundTranReq.setOrigTrxSerno("1112");
		refundTranReq.setUserId("G903005002843401261606");

		RefundReq.RefundReqMsg refundReqMsg = new RefundReqMsg();
		refundReqMsg.setPubReq(pubReq);
		refundReqMsg.setTrxCode(EnumIntFcode.REFUND);
		refundReqMsg.setRefundTranReq(refundTranReq);

		RefundReq req = new RefundReq();
		req.setHeadReq(headReq);
		req.setRefundReqMsg(refundReqMsg);

		// Marshaller<RefundReq> marshaller =
		// MarshallerFactory.getMarshaller(RefundReq.class, false);
		// ((AbstractMarshaller<?>) marshaller).setSignature(new Signature() {
		//
		// @Override
		// public String sign(String signStr) {
		// return ICBCSecurity.signature(signStr);
		// }
		//
		// @Override
		// public boolean verify(String verifyStr, String signStr) {
		// return false;
		// }
		// });
		// marshaller.analyse();
		// byte[] bytes = marshaller.marshal(refundReq);
		// System.out.println("==" + new String(bytes));
		RefundRsp rsp = api.doSend(req, EnumSource.refund);
	}

	@Test
	@Ignore
	public void releaseTest() {

		HeadReq headReq = new HeadReq();
		headReq.setCorpcode("1085");
		headReq.setIntfcode(EnumIntFcode.RELEASE);
		headReq.setTailNumber("06");
		headReq.setAppcode("F-ATP");
		headReq.setTrxcode("99999");

		PubReq pubReq = new PubReq();
		pubReq.setBizCode("99");
		pubReq.setCorpNo("1085");
		pubReq.setProtocolNo("98");
		// pubMsg.setTrxCode("asdsad");
		pubReq.setTrxDate(localDate.format(formatterDate));
		pubReq.setTrxSerno("9996");
		pubReq.setTrxTime(localDate.format(formatterTime));

		ReleaseReq.ReleaseReqMsg.ReleaseTranReq releaseTranReq = new ReleaseTranReq();
		releaseTranReq.setAccShtId("0606");
		releaseTranReq.setCheckMsg("1");
		releaseTranReq.setUserId("G903005002843401261606");

		ReleaseReq.ReleaseReqMsg releaseReqMsg = new ReleaseReqMsg();
		releaseReqMsg.setPubReq(pubReq);
		releaseReqMsg.setTrxCode(EnumIntFcode.RELEASE);
		releaseReqMsg.setReleaseTranReq(releaseTranReq);

		ReleaseReq req = new ReleaseReq();
		req.setHeadReq(headReq);
		req.setReleaseReqMsg(releaseReqMsg);

		// Marshaller<ReleaseReq> marshaller =
		// MarshallerFactory.getMarshaller(ReleaseReq.class, false);
		//
		// ((AbstractMarshaller<?>) marshaller).setSignature(new Signature() {
		//
		// public String sign(String signStr) {
		// return signStr;
		// }
		//
		// public boolean verify(String verifyStr, String signStr) {
		// return false;
		// }
		// });
		// marshaller.analyse();
		// byte[] bytes = marshaller.marshal(releaseReq);
		// System.out.println("-------" + new String(bytes));

		ReleaseRsp rsp = api.doSend(req, EnumSource.release);
	}
}
