package com.hisun.lemon.cpi;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.hisun.channel.common.core.Signature;
import com.hisun.channel.parse.AbstractUnmarshaller;
import com.hisun.channel.parse.Unmarshaller;
import com.hisun.channel.parse.UnmarshallerFactory;
import com.hisun.lemon.cpi.icbc.fastpay.ICBCSecurity;
import com.hisun.lemon.cpi.icbc.fastpay.rsp.PreContractRsp;


@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class PlainToObjectTest {

	@Test
	@Ignore
	public void contractTest(){
		String rsp="<?xml version=\"1.0\" encoding=\"UTF-8\"?><Msg><resp><retCode>0</retCode><retMsg></retMsg><retStatus></retStatus><corpNo>1085</corpNo><trxSerno>1</trxSerno></resp></Msg>kWFJdz77echt/ibdGV4MqAz4Uvcj/yl1jCuapB7vbob5Rox/jekQ0+XgzFjFSK37kVcmSNHIV+bcCO3Wl2M6K4W4Q6JSCFHiaFngINlzWkrj+/weFU62JKWY4vNEOQg1BoPLoRno0VRvasPcrHAqe96Kk0zaWCz8bZZI8FbKv+4=";
	
		Unmarshaller<PreContractRsp> unmarshaller = UnmarshallerFactory.getUnmarshaller(PreContractRsp.class, false);
		((AbstractUnmarshaller<?>) unmarshaller).setSignature(new Signature(){

			@Override
			public String sign(String signStr) {
				return "~~~"+signStr+"~~~";
			}

			@Override
			public boolean verify(String verifyStr, String signStr) {
				return ICBCSecurity.icbcVerify(verifyStr, signStr.split("</Msg>")[1]);
			}});
		unmarshaller.analyse();
		PreContractRsp so = unmarshaller.unmarshal(rsp.getBytes());
		System.out.println("=="+so);
	}
}
