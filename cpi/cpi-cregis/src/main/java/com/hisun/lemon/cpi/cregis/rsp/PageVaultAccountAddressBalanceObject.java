package com.hisun.lemon.cpi.cregis.rsp;

import com.hisun.channel.parse.annotation.Item;

import java.util.List;

/**
 * 查询金库账户对应的地址余额信息相应类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14 11:52
 */
public class PageVaultAccountAddressBalanceObject {

    /**
     * 记录列表
     */
    @Item(alias = "records")
    private List<VaultAccountAddressBalanceObject> records;

    public List<VaultAccountAddressBalanceObject> getRecords() {
        return records;
    }

    public void setRecords(List<VaultAccountAddressBalanceObject> records) {
        this.records = records;
    }
}
