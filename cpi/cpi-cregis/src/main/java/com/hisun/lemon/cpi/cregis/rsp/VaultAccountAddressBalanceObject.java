package com.hisun.lemon.cpi.cregis.rsp;

import com.hisun.channel.parse.annotation.Item;

import java.time.LocalDateTime;

/**
 * 数币账户详情相应类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/11 17:30
 */
public class VaultAccountAddressBalanceObject {

    @Item
    private Long id; // ID

    @Item
    private Long accountId; // 账户 ID

    @Item
    private String coinId; // 币种 ID

    @Item
    private String address; // 地址

    @Item
    private String network; // 区块链网络

    @Item
    private Integer status; // 状态：0-可用，1-占用

    @Item
    private Integer balance; // 余额

    @Item
    private Object onChainState; // 地址链上状态

    @Item
    private Integer priority; // 使用优先级

    @Item
    private LocalDateTime createTime; // 创建时间

    @Item
    private LocalDateTime updateTime; // 更新时间

    // Getter 和 Setter 方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }
    public String getCoinId() { return coinId; }
    public void setCoinId(String coinId) { this.coinId = coinId; }
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    public String getNetwork() { return network; }
    public void setNetwork(String network) { this.network = network; }
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    public Integer getBalance() { return balance; }
    public void setBalance(Integer balance) { this.balance = balance; }
    public Object getOnChainState() { return onChainState; }
    public void setOnChainState(Object onChainState) { this.onChainState = onChainState; }
    public Integer getPriority() { return priority; }
    public void setPriority(Integer priority) { this.priority = priority; }
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
}
