package com.hisun.lemon.cpi.cregis;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.channel.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dto.CregisRsp;
import com.hisun.lemon.cpi.dto.SignRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.util.Map;

@Component
public class CregisApi {

    private static final Logger log = LoggerFactory.getLogger(CregisApi.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private CregisProperties cregisProperties;

//    @Autowired
//    private IChannelClient channelClient;

//    @SuppressWarnings("unchecked")
//    public <T> T doSend(Object obj, CregisEnumSource source) {
//        if (source == null) {
//            return null;
//        }
//        Request request = new Request();
//        request.setRoute("CREGIS");
//        request.setBusiType(source.name());
//        request.setSource(source.name());
//        request.setTarget(obj);
//        request.setRequestId("1");
//        Response response = channelClient.request(request);
//        Object t = response.getResult();
//        return (T) t;
//    }


    /**
     * 发送http请求
     *
     * @param url           请求地址
     * @param method        请求方法
     * @param req           请求参数
     * @param pathParams    路径参数
     * @param typeReference
     * @param <R>           请求类型
     * @param <T>           响应类型
     * @return
     */
    public <R, T> CregisRsp<T> doSend(String url, Method method, R req, Map<String, Object> pathParams, TypeReference<CregisRsp<T>> typeReference) {
        try {
            // 构建Url
            UrlBuilder urlBuilder = new UrlBuilder();
            // 如果是已经有url的就直接使用
            if(url.startsWith("http://")){
                urlBuilder = UrlBuilder.ofHttp(url);
            }else{
                urlBuilder = UrlBuilder.ofHttp(cregisProperties.getBaseUrl() + url);
            }

            // 处理路径参数
            if (pathParams != null && !pathParams.isEmpty()) {
                String rawUrl = cregisProperties.getBaseUrl() + url;
                log.info("Raw URL: {}", rawUrl);
                for (Map.Entry<String, Object> entry : pathParams.entrySet()) {
                    String placeholder = "{" + entry.getKey() + "}";
                    rawUrl = rawUrl.replace(placeholder, String.valueOf(entry.getValue()));
                }
                url = rawUrl;
            } else {
                url = urlBuilder.build();
            }

            // 构建http请求
            HttpRequest httpReq;
            if (Method.POST.equals(method)) {
                httpReq = HttpRequest.post(url);
                // json格式
//                String reqBody = JSONUtil.toJsonStr(req);
                // 使用 Jackson 序列化  由于ChainCmdBasic该类发送json时要为蛇形命名，所以使用Jackson
                String reqBody = objectMapper.writeValueAsString(req);
                httpReq.body(reqBody);
            } else {
                Map<String, Object> queryMap = BeanUtil.beanToMap(req, false, true);
                // Url参数形式: http://xxxx/xxx?key1=v1&key2=&key3=v3
                String queryString = HttpUtil.toParams(queryMap);
                httpReq = HttpRequest.get(url + (queryString.isEmpty() ? "" : "?" + queryString));
            }
//            httpReq.header("token", token);
            httpReq.timeout(cregisProperties.getTimeout());
            log.info("\r\n[Cregis]Http[{}]请求报文：{}", method, httpReq);

            HttpResponse httpRsp = httpReq.execute();

            log.info("\r\n[Cregis]Http[{}]响应报文：{}", method, httpRsp);

            // 获取响应体并尝试解析 JSON
            String rspbody = httpRsp.body();
            CregisRsp<T> resp;
            // 特殊处理签名服务响应
            if (isSignServiceCall(url, typeReference)) {
                resp = handleSignServiceResponse(rspbody);
            }
            // 其他API保持原逻辑
            else {
                try {
                    resp = JSONUtil.toBean(rspbody, typeReference, false);
                } catch (Exception e) {
                    log.error("[Cregis]JSON 解析失败：{}", e.getMessage(), e);
                    resp = new CregisRsp<>();
                    resp.setCode(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
                    resp.setMessage("JSON 解析失败: " + e.getMessage());
                    resp.setData(null);
                    return resp;
                }
            }

            // 判断 HTTP 响应是否成功，状态 200 表示成功
            if (httpRsp.isOk()) {
                // 判断业务是否成功
                if (JudgeUtils.equals("200", resp.getCode())) {
                    return resp;
                } else {
                    log.error("[Cregis]业务失败：{}-{}", resp.getData(), resp.getMessage());
                    return resp;
                }
            } else {
                log.error("[Cregis]响应失败：{}", httpRsp);
                return resp;
            }
        } catch (Exception e) {
            log.error("[Cregis]请求异常：{}", e.getMessage(), e);
            CregisRsp<T> errorResp = new CregisRsp<>();
            errorResp.setCode(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
            errorResp.setMessage("请求异常: " + e.getMessage());
            errorResp.setData(null);
            return errorResp;
        }
    }

    /**
     * 判断是否为签名服务调用
     * @param url
     * @param typeReference
     * @return
     * @param <T>
     */
    private <T> boolean isSignServiceCall(String url, TypeReference<CregisRsp<T>> typeReference) {
        // 1. URL特征判断
        boolean isSignServiceUrl = url.contains("/signTypedData");

        // 2. 类型特征判断
        boolean isSignResponseType = false;
        try {
            if (typeReference.getType() instanceof ParameterizedType) {
                ParameterizedType pt = (ParameterizedType) typeReference.getType();
                Class<?> actualType = (Class<?>) pt.getActualTypeArguments()[0];
                isSignResponseType = SignRspDTO.class.isAssignableFrom(actualType);
            }
        } catch (Exception e) {
            log.warn("类型判断异常", e);
        }

        return isSignServiceUrl && isSignResponseType;
    }

    /**
     * 处理签名服务响应
     * @param rspbody
     * @return
     * @param <T>
     */
    private <T> CregisRsp<T> handleSignServiceResponse(String rspbody) {
        try {
            // 解析为直接结构
            SignRspDTO signRsp = JSONUtil.toBean(rspbody, SignRspDTO.class);

            // 转换为统一响应格式
            CregisRsp<T> resp = new CregisRsp<>();
            resp.setCode(String.valueOf(200));
            resp.setMessage("SUCCESS");

            // 特殊类型转换
            if (signRsp != null) {
                SignRspDTO signResponse = new SignRspDTO();
                signResponse.setSignature(signRsp.getSignature());
                signResponse.setHash(signRsp.getHash());
                resp.setData((T) signResponse);
            }

            return resp;
        } catch (Exception e) {
            log.error("[Cregis]签名服务响应解析失败", e);
            CregisRsp<T> errorResp = new CregisRsp<>();
            errorResp.setCode(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
            errorResp.setMessage("请求异常: " + e.getMessage());
            errorResp.setData(null);
            return errorResp;
        }
    }

}
