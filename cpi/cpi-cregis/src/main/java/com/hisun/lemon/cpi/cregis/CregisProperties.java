package com.hisun.lemon.cpi.cregis;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * CregisProperties
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14 10:15
 */
@Component
@ConfigurationProperties(prefix = "cregis")
@PropertySource("classpath:config/cregis.properties")
public class CregisProperties {

    /**
     * cregis接口地址
     */
    private String BaseUrl;

    /**
     * 超时时间
     */
    private int timeout;

    /**
     * 获取金库账户对应的地址余额信息接口地址
     */
    private String accountDetailUrl;

    private String perPayment;
    private String signData;
    private String submitTransfer;

    public String getBaseUrl() {
        return BaseUrl;
    }

    public String getPerPayment() {
        return perPayment;
    }

    public void setPerPayment(String perPayment) {
        this.perPayment = perPayment;
    }

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public String getSubmitTransfer() {
        return submitTransfer;
    }

    public void setSubmitTransfer(String submitTransfer) {
        this.submitTransfer = submitTransfer;
    }

    public void setBaseUrl(String baseUrl) {
        BaseUrl = baseUrl;
    }

    public String getAccountDetailUrl() {
        return accountDetailUrl;
    }

    public void setAccountDetailUrl(String accountDetailUrl) {
        this.accountDetailUrl = accountDetailUrl;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
}
