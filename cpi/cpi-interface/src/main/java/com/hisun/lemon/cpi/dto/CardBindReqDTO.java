package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * Created by Rui on 2017/7/5.
 */
@ClientValidated
public class CardBindReqDTO {

    /**
     * 银行预留手机号
     */
    @ApiModelProperty(name = "mblNo", value = "银行预留手机号", required = true, dataType = "String")
    @NotEmpty(message="CPI10005")
    private String mblNo;
    /**
     * 卡资金机构
     */
    @ApiModelProperty(name = "crdCorpOrg", value = "资金机构", required = true, dataType = "String")
    @NotEmpty(message="CPI10006")
    private String crdCorpOrg;
    /**
     * 卡种，C贷记卡，D借记卡
     */
    @ApiModelProperty(name = "crdAcTyp", value = "银行卡类型，C贷记卡，D借记卡", required = true, dataType = "String")
    @NotEmpty(message="CPI10014")
    @Pattern(regexp="D|C",message="CPI10007")
    private String crdAcTyp;
    /**
     * 户名
     */
    @ApiModelProperty(name = "crdUsrNm", value = "银行卡户名", required = true, dataType = "String")
    @NotEmpty(message="CPI10008")
    private String crdUsrNm;
    /**
     * 预签约流水号
     */
    @ApiModelProperty(name = "jrnNo", value = "预签约流水号", required = true, dataType = "String")
    @NotEmpty(message="CPI10010")
    private String jrnNo;
    /**
     * 短信验证码
     */
    @ApiModelProperty(name = "chkNo", value = "短信验证码", required = false, dataType = "String")
    private String chkNo;
    /**
     * 短信token
     */
    @ApiModelProperty(name = "smsToken", value = "短信token", required = false, dataType = "String")
    private String smsToken;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getChkNo() {
        return chkNo;
    }

    public void setChkNo(String chkNo) {
        this.chkNo = chkNo;
    }

    public String getSmsToken() {
        return smsToken;
    }

    public void setSmsToken(String smsToken) {
        this.smsToken = smsToken;
    }
}
