package com.hisun.lemon.cpi.dto;

import java.math.BigDecimal;

/**
 * 支付准备请求数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 11:36
 */
public class PaymentPrepareReqDTO {
    /**
     * 找零地址，对于btc交易适用
     */
//    private String changeAddr;
    /**
     * 币种ID
     */
    private String coinId;
    /**
     * fundFlowCode
     */
    private String fundFlowCode;
    /**
     * 审批任务的语言
     */
    private String lang;
    /**
     * 链上备注信息，对于支持memo的链有效
     */
//    private String memo;
    /**
     * 所在网络
     */
    private String network;
    /**
     * 付款备注
     */
//    private String note;
    /**
     * 付出对象明细
     */
    private PayTo[] payToList;
//    /**
//     * 付款类型:单地址付款/聚合付款-按优先级/聚合付款-小额优先/聚合付款-大额优先/资金归集
//     */
//    private PayType payType;
    /**
     * 是否代付
     */
//    private Boolean sponsored;
    /**
     * 票据有效期，单位为分钟，默认为一天
     */
//    private Long validUntilTime;



    public String getCoinId() { return coinId; }
    public void setCoinId(String value) { this.coinId = value; }

    public String getFundFlowCode() { return fundFlowCode; }
    public void setFundFlowCode(String value) { this.fundFlowCode = value; }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }


    public String getNetwork() { return network; }
    public void setNetwork(String value) { this.network = value; }


    public PayTo[] getPayToList() { return payToList; }
    public void setPayToList(PayTo[] value) { this.payToList = value; }


    public static class PayTo {
        /**
         * 付款金额，如果传了该字段，readableAmount即失效，一般面向程序化调用使用
         */
        private String amount;
        /**
         * 可读金额，如果amount为空，该字段生效，用于接受页面直接传递过来的可读金额
         */
        private BigDecimal readableAmount;
        /**
         * 转出地址
         */
        private String to;

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public BigDecimal getReadableAmount() {
            return readableAmount;
        }

        public void setReadableAmount(BigDecimal readableAmount) {
            this.readableAmount = readableAmount;
        }

        public String getTo() {
            return to;
        }

        public void setTo(String to) {
            this.to = to;
        }
    }
}
