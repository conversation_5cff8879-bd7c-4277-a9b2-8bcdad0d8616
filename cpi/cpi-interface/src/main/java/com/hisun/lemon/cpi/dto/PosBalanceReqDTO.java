package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Description
 * @date 2017/10/16 16:52
 */
@ClientValidated
public class PosBalanceReqDTO {

    /**
     * 卡号
     */
    @ApiModelProperty(name = "crdNo", value = "卡号", required = true, dataType = "String")
    @NotEmpty(message="CPI10016")
    private String crdNo;

    /**
     * trmNo 终端号
     */
    @NotEmpty(message="CPI10033")
    @ApiModelProperty(name = "trmNo", value = "终端号", required = true, dataType = "String")
    private String trmNo;

    /**
     * 请求日期
     */
    @ApiModelProperty(name = "reqOrdDt", value = "请求订单日期", required = true, dataType = "LocalDate")
    @NotNull(message="CPI10022")
    private LocalDate reqOrdDt;

    /**
     * 请求时间
     */
    @ApiModelProperty(name = "reqOrdTm", value = "请求订单时间", required = true, dataType = "LocalTime")
    @NotNull(message="CPI10023")
    private LocalTime reqOrdTm;

    public String getCrdNo() {
        return crdNo;
    }

    public void setCrdNo(String crdNo) {
        this.crdNo = crdNo;
    }

    public String getTrmNo() {
        return trmNo;
    }

    public void setTrmNo(String trmNo) {
        this.trmNo = trmNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }
}
