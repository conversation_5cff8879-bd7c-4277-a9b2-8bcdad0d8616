package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2017/10/16 17:00
 */
public class PosBalanceRspDTO {

    /**
     * 卡号
     */
    @ApiModelProperty(name = "crdNo", value = "卡号", required = true, dataType = "String")
    private String crdNo;
    /**
     * trmNo 终端号
     */
    @ApiModelProperty(name = "trmNo", value = "终端号", required = true, dataType = "String")
    private String trmNo;
    /**
     * 余额
     */
    @ApiModelProperty(name = "balanceAmt", value = "余额", dataType = "BigDecimal")
    private BigDecimal balanceAmt;

    public String getCrdNo() {
        return crdNo;
    }

    public void setCrdNo(String crdNo) {
        this.crdNo = crdNo;
    }

    public String getTrmNo() {
        return trmNo;
    }

    public void setTrmNo(String trmNo) {
        this.trmNo = trmNo;
    }

    public BigDecimal getBalanceAmt() {
        return balanceAmt;
    }

    public void setBalanceAmt(BigDecimal balanceAmt) {
        this.balanceAmt = balanceAmt;
    }
}
