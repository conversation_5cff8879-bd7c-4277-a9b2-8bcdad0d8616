package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * Created by Rui on 2017/7/5.
 */
public class FastpayAcledaRspDTO {
    /**
     * 订单号
     */
    @ApiModelProperty(name = "sessionId", value = "sessionId", dataType = "String")
    private String sessionId;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "paymentTokenId", value = "paymentTokenId", dataType = "String")
    private String paymentTokenId;

    /**
     * 内部用户号
     */
    @ApiModelProperty(name = "userId", value = "内部用户号", dataType = "String")
    private String userId;
    /**
     * 金额
     */
    @ApiModelProperty(name = "ordAmt", value = "金额", dataType = "BigDecimal")
    private BigDecimal ordAmt;
    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", dataType = "CorpBusTyp")
    private CorpBusTyp corpBusTyp;
    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", dataType = "CorpBusSubTyp")
    private CorpBusSubTyp corpBusSubTyp;
    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", dataType = "String")
    private String reqOrdNo;
    /**
     * 订单状态
     */
    @ApiModelProperty(name = "ordSts", value = "订单状态", dataType = "String")
    private String ordSts;
    /**
     * 订单号
     */
    @ApiModelProperty(name = "ordNo", value = "订单号", dataType = "String")
    private String ordNo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getPaymentTokenId() {
        return paymentTokenId;
    }

    public void setPaymentTokenId(String paymentTokenId) {
        this.paymentTokenId = paymentTokenId;
    }
}
