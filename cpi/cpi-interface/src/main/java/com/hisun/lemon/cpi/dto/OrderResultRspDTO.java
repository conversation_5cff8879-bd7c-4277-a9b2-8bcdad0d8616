package com.hisun.lemon.cpi.dto;

import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by Rui on 2017/7/13.
 */
public class OrderResultRspDTO {

    /**
     * 订单号
     */
    @ApiModelProperty(name = "ordNo", value = "订单号", dataType = "String")
    private String ordNo;
    /**
     * 订单状态
     */
    @ApiModelProperty(name = "ordSts", value = "订单状态", dataType = "String")
    private String ordSts;
    /**
     * 内部用户号
     */
    @ApiModelProperty(name = "userId", value = "内部用户号", dataType = "String")
    private String userId;
    /**
     * 订单金额
     */
    @ApiModelProperty(name = "ordAmt", value = "金额", dataType = "BigDecimal")
    private String ordAmt;
    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", dataType = "String")
    private String reqOrdNo;
    /**
     * 订单日期
     */
    @ApiModelProperty(name = "userId", value = "订单日期", dataType = "LocalDate")
    private LocalDate ordDt;
    /**
     * 订单时间
     */
    @ApiModelProperty(name = "userId", value = "订单时间", dataType = "LocalTime")
    private LocalTime ordTm;

    /**
     * 图片url
     */
    @ApiModelProperty(name = "picUrl", value = "图片url", dataType = "String")
    private String picUrl;

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(String ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }
}
