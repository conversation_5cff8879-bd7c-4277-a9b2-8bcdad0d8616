package com.hisun.lemon.cpi.enums;

import java.io.IOException;

/**
 * 任务状态
 */
public enum State {
    ALL_BROADCAST, ALL_BROADCAST_FAILED, AUDITING, AUDIT_PASSED, BROADCAST_EXCEPTION, BROADCAST_FAILED, DELEGATE_SIGNED, EXECUTED, PARTIALLY_BROADCAST, REFUSED, SIGNED, SIGNING, SIGN_FAILED;

    public String toValue() {
        switch (this) {
            case ALL_BROADCAST: return "ALL_BROADCAST";
            case ALL_BROADCAST_FAILED: return "ALL_BROADCAST_FAILED";
            case AUDITING: return "AUDITING";
            case AUDIT_PASSED: return "AUDIT_PASSED";
            case BROADCAST_EXCEPTION: return "BROADCAST_EXCEPTION";
            case BROADCAST_FAILED: return "BROADCAST_FAILED";
            case DELEGATE_SIGNED: return "DELEGATE_SIGNED";
            case EXECUTED: return "EXECUTED";
            case PARTIALLY_BROADCAST: return "PARTIALLY_BROADCAST";
            case REFUSED: return "REFUSED";
            case SIGNED: return "SIGNED";
            case SIGNING: return "SIGNING";
            case SIGN_FAILED: return "SIGN_FAILED";
        }
        return null;
    }

    public static State forValue(String value) throws IOException {
        if (value.equals("ALL_BROADCAST")) return ALL_BROADCAST;
        if (value.equals("ALL_BROADCAST_FAILED")) return ALL_BROADCAST_FAILED;
        if (value.equals("AUDITING")) return AUDITING;
        if (value.equals("AUDIT_PASSED")) return AUDIT_PASSED;
        if (value.equals("BROADCAST_EXCEPTION")) return BROADCAST_EXCEPTION;
        if (value.equals("BROADCAST_FAILED")) return BROADCAST_FAILED;
        if (value.equals("DELEGATE_SIGNED")) return DELEGATE_SIGNED;
        if (value.equals("EXECUTED")) return EXECUTED;
        if (value.equals("PARTIALLY_BROADCAST")) return PARTIALLY_BROADCAST;
        if (value.equals("REFUSED")) return REFUSED;
        if (value.equals("SIGNED")) return SIGNED;
        if (value.equals("SIGNING")) return SIGNING;
        if (value.equals("SIGN_FAILED")) return SIGN_FAILED;
        throw new IOException("Cannot deserialize State");
    }
}
