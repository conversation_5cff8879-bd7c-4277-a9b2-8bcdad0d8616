package com.hisun.lemon.cpi.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * Created by Rui on 2017/7/7.
 */
public class AgrInfoRspDTO {

    private List<CardAgrInfo> list;

    public List<CardAgrInfo> getList() {
        return list;
    }

    public void setList(List<CardAgrInfo> list) {
        this.list = list;
    }

    public static class CardAgrInfo implements Serializable {
        /**
         * 内部协议号
         * @mbggenerated
         */
        @ApiModelProperty(name = "agrNo", value = "内部协议号", dataType = "String")
        private String agrNo;

        /**
         * 生效标志，W：初登记，Y：生效，N：失效
         * @mbggenerated
         */
        @ApiModelProperty(name = "agrEffFlg", value = "生效标志，W：初登记，Y：生效，N：失效", dataType = "String")
        private String agrEffFlg;

        /**
         * 签约日期
         * @mbggenerated
         */
        @ApiModelProperty(name = "signDt", value = "签约日期", dataType = "LocalDate")
        private LocalDate signDt;

        /**
         * 签约时间
         * @mbggenerated
         */
        @ApiModelProperty(name = "signTm", value = "签约时间", dataType = "LocalTime")
        private LocalTime signTm;

        /**
         * 解约日期
         * @mbggenerated
         */
        @ApiModelProperty(name = "unsignDt", value = "解约日期", dataType = "LocalDate")
        private LocalDate unsignDt;

        /**
         * 解约时间
         * @mbggenerated
         */
        @ApiModelProperty(name = "unsignTm", value = "解约时间", dataType = "LocalTime")
        private LocalTime unsignTm;

        /**
         * 用户号
         * @mbggenerated
         */
        @ApiModelProperty(name = "userId", value = "用户id", dataType = "String")
        private String userId;

        /**
         * 手机号
         * @mbggenerated
         */
        @ApiModelProperty(name = "mblNo", value = "手机号", dataType = "String")
        private String mblNo;

        /**
         * 个企标识
         * @mbggenerated
         */
        @ApiModelProperty(name = "bnkPsnFlg", value = "个企标识，B企业，C个人", dataType = "String")
        private String bnkPsnFlg;

        /**
         * 资金机构
         * @mbggenerated
         */
        @ApiModelProperty(name = "crdCorpOrg", value = "资金机构", dataType = "String")
        private String crdCorpOrg;

        /**
         * 路径机构
         * @mbggenerated
         */
        @ApiModelProperty(name = "rutCorpOrg", value = "路径机构", dataType = "String")
        private String rutCorpOrg;

        /**
         * 签约协议号
         * @mbggenerated
         */
        @ApiModelProperty(name = "signAgrno", value = "银行协议号", dataType = "String")
        private String signAgrno;

        /**
         * 协议方向，0：双向协议1：我方单侧协议
         * @mbggenerated
         */
        @ApiModelProperty(name = "agrDirect", value = "协议方向，0：双向协议1：我方单侧协议", dataType = "String")
        private String agrDirect;

        /**
         * 卡类型，D：借记卡，C：贷记卡
         * @mbggenerated
         */
        @ApiModelProperty(name = "agrDirect", value = "卡类型，D：借记卡，C：贷记卡", dataType = "String")
        private String crdAcTyp;

        /**
         * 加密银行卡号
         * @mbggenerated
         */
        @ApiModelProperty(name = "crdNoEnc", value = "加密银行卡号", dataType = "String")
        private String crdNoEnc;

        /**
         * 银行卡后四位
         * @mbggenerated
         */
        @ApiModelProperty(name = "crdNoLast", value = "银行卡后四位", dataType = "String")
        private String crdNoLast;

        /**
         * 账户名
         * @mbggenerated
         */
        @ApiModelProperty(name = "crdUsrNm", value = "账户名", dataType = "String")
        private String crdUsrNm;

        /**
         * 证件类型
         * @mbggenerated
         */
        @ApiModelProperty(name = "idTyp", value = "证件类型", dataType = "String")
        private String idTyp;

        /**
         * 加密证件号
         * @mbggenerated
         */
        @ApiModelProperty(name = "idNoEnc", value = "加密证件号", dataType = "String")
        private String idNoEnc;

        /**
         * 加密CVV2
         * @mbggenerated
         */
        @ApiModelProperty(name = "crdCvv2Enc", value = "加密CVV2", dataType = "String")
        private String crdCvv2Enc;

        /**
         * 加密有效期
         * @mbggenerated
         */
        @ApiModelProperty(name = "crdExpDtEnc", value = "加密有效期", dataType = "String")
        private String crdExpDtEnc;

        /**
         * 备注
         * @mbggenerated
         */
        @ApiModelProperty(name = "rmk", value = "备注", dataType = "String")
        private String rmk;

        public String getAgrNo() {
            return agrNo;
        }

        public void setAgrNo(String agrNo) {
            this.agrNo = agrNo;
        }

        public String getAgrEffFlg() {
            return agrEffFlg;
        }

        public void setAgrEffFlg(String agrEffFlg) {
            this.agrEffFlg = agrEffFlg;
        }

        public LocalDate getSignDt() {
            return signDt;
        }

        public void setSignDt(LocalDate signDt) {
            this.signDt = signDt;
        }

        public LocalTime getSignTm() {
            return signTm;
        }

        public void setSignTm(LocalTime signTm) {
            this.signTm = signTm;
        }

        public LocalDate getUnsignDt() {
            return unsignDt;
        }

        public void setUnsignDt(LocalDate unsignDt) {
            this.unsignDt = unsignDt;
        }

        public LocalTime getUnsignTm() {
            return unsignTm;
        }

        public void setUnsignTm(LocalTime unsignTm) {
            this.unsignTm = unsignTm;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getMblNo() {
            return mblNo;
        }

        public void setMblNo(String mblNo) {
            this.mblNo = mblNo;
        }

        public String getBnkPsnFlg() {
            return bnkPsnFlg;
        }

        public void setBnkPsnFlg(String bnkPsnFlg) {
            this.bnkPsnFlg = bnkPsnFlg;
        }

        public String getCrdCorpOrg() {
            return crdCorpOrg;
        }

        public void setCrdCorpOrg(String crdCorpOrg) {
            this.crdCorpOrg = crdCorpOrg;
        }

        public String getRutCorpOrg() {
            return rutCorpOrg;
        }

        public void setRutCorpOrg(String rutCorpOrg) {
            this.rutCorpOrg = rutCorpOrg;
        }

        public String getSignAgrno() {
            return signAgrno;
        }

        public void setSignAgrno(String signAgrno) {
            this.signAgrno = signAgrno;
        }

        public String getAgrDirect() {
            return agrDirect;
        }

        public void setAgrDirect(String agrDirect) {
            this.agrDirect = agrDirect;
        }

        public String getCrdAcTyp() {
            return crdAcTyp;
        }

        public void setCrdAcTyp(String crdAcTyp) {
            this.crdAcTyp = crdAcTyp;
        }

        public String getCrdNoEnc() {
            return crdNoEnc;
        }

        public void setCrdNoEnc(String crdNoEnc) {
            this.crdNoEnc = crdNoEnc;
        }

        public String getCrdNoLast() {
            return crdNoLast;
        }

        public void setCrdNoLast(String crdNoLast) {
            this.crdNoLast = crdNoLast;
        }

        public String getCrdUsrNm() {
            return crdUsrNm;
        }

        public void setCrdUsrNm(String crdUsrNm) {
            this.crdUsrNm = crdUsrNm;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getIdNoEnc() {
            return idNoEnc;
        }

        public void setIdNoEnc(String idNoEnc) {
            this.idNoEnc = idNoEnc;
        }

        public String getCrdCvv2Enc() {
            return crdCvv2Enc;
        }

        public void setCrdCvv2Enc(String crdCvv2Enc) {
            this.crdCvv2Enc = crdCvv2Enc;
        }

        public String getCrdExpDtEnc() {
            return crdExpDtEnc;
        }

        public void setCrdExpDtEnc(String crdExpDtEnc) {
            this.crdExpDtEnc = crdExpDtEnc;
        }

        public String getRmk() {
            return rmk;
        }

        public void setRmk(String rmk) {
            this.rmk = rmk;
        }
    }
}
