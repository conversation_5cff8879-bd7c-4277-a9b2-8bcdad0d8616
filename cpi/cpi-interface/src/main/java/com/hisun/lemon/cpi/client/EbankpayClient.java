package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient("CPI")
public interface EbankpayClient {
    /**
     * 网银下单
     */
    @PostMapping("/cpi/ebank/order")
    GenericRspDTO<EbankpayRspDTO> ebankpay(@RequestBody GenericDTO<EbankpayReqDTO> genericDTO);

    /**
     * 网银订单查询
     */
    @GetMapping("/cpi/ebank/result")
    GenericRspDTO<OrderResultRspDTO> orderQuery(@RequestParam(value = "ordNo") String ordNo);

    /**
     * 微信子商户号申请
     */
    @PostMapping("/cpi/ebank/submerc")
    GenericRspDTO<SubMercRspDTO> applySubMerc(@RequestBody GenericDTO<SubMercReqDTO> genericDTO);

    /**
     * 微信页面授权
     */
    @PostMapping("/cpi/ebank/token")
    GenericRspDTO<WeChatTokenRsp> applyToken(@RequestBody GenericDTO<WeChatTokenReq> genericDTO);

    /**
     * 撤单
     */
    @PostMapping("/cpi/ebank/closeOrder")
    GenericRspDTO<CloseOrderRspDTO> closeOrder(@RequestBody GenericDTO<CloseOrderReqDTO> genericDTO);

    /**
     * 平台在网银机构的结算明细查询
     */
    @PostMapping("/cpi/ebank/settlement")
    GenericRspDTO<NoBody> settlementQuery(@RequestParam(value = "stlFlg") int stlFlg,
                                          @RequestParam(value = "rutCorg") String rutCorg,
                                          @RequestParam(value = "startDate") String startDate,
                                          @RequestParam(value = "endDate") String endDate);

    /**
     * 查询微信子商户列表
     */
    @PostMapping("/cpi/ebank/submerclist")
    GenericRspDTO<SubMercListRspDTO> getSubMercList(@RequestBody GenericDTO<SubMercListReqDTO> genericDTO);

    /**
     * 解绑微信子商户
     */
    @DeleteMapping("/cpi/ebank/delete")
    GenericRspDTO unBundingSubMerc(@RequestBody GenericDTO<UnbundingSubMercReqDTO> genericDTO);

    /**
     * 查询支付宝二级商户列表
     */
    @PostMapping("/cpi/ebank/secmerclist")
    GenericRspDTO<SecMercListRspDTO> getSecMercList(@RequestBody GenericDTO<SecMercListReqDTO> genericDTO);

    /**
     * 添加支付宝二级商户
     */
    @PostMapping("/cpi/ebank/secmerc")
    GenericRspDTO addSecMerc(@RequestBody GenericDTO<SecMercReqDTO> genericDTO);
}
