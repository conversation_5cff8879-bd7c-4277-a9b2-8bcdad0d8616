package com.hisun.lemon.cpi.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class RemittanceConfirmDTO {

    /**
     * 订单号
     */
    @ApiModelProperty(name = "fndOrdNo", value = "充值订单号", required = true, dataType = "String")
    @NotNull(message="CPI10026")
    private String fndOrdNo;
    /**
     * 订单状态
     */
    @ApiModelProperty(name = "ordSts", value = "提现订单状态", required = true, dataType = "String")
    @NotNull(message="CPI10028")
    private String ordSts;

    /**
     * 处理理由
     */
    @ApiModelProperty(name = "reason", value = "处理理由", required = false, dataType = "String")
    private String reason;

    public String getFndOrdNo() {
        return fndOrdNo;
    }

    public void setFndOrdNo(String fndOrdNo) {
        this.fndOrdNo = fndOrdNo;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
