package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.RouteRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient("CPI")
public interface RouteClient {


    /**
     * 根据业务类型和子类型查询生效的机构
     * @param corpBusTyp
     * @param corpBusSubTyp
     * @return
     */
    @GetMapping("/cpi/route/result")
    GenericRspDTO<RouteRspDTO> queryEffOrgInfo(@Validated @RequestParam(value = "corpBusTyp")CorpBusTyp corpBusTyp,
                                                     @Validated @RequestParam(value = "corpBusSubTyp")CorpBusSubTyp corpBusSubTyp);
}
