package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiModelProperty;

/**
 * 平台商户申请微信子商户号，返回报文
 */
public class SubMercListReqDTO {
    /**
     * 微信返回子商户号
     */
    @ApiModelProperty(name = "subMchId", value = "微信返回子商户号", dataType = "String")
    private String subMchId;

    /**
     * 每页大小
     */
    @ApiModelProperty(name = "pageSize", value = "每页大小")
    private Integer pageSize;
    /**
     * 页数
     */
    @ApiModelProperty(name = "pageNo", value = " 页数")
    private Integer pageNo;

    public String getSubMchId() {
        return subMchId;
    }

    public void setSubMchId(String subMchId) {
        this.subMchId = subMchId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }
}
