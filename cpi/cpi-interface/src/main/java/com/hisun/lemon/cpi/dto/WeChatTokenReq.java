package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;

/**
 * 微信页面授权申请
 */
@ClientValidated
public class WeChatTokenReq {

    @ApiModelProperty(name = "appid", value = "微信公众号", required = false, dataType = "String")
    private String appid;

    @ApiModelProperty(name = "secret", value = "密钥", required = false, dataType = "String")
    private String secret;

    @ApiModelProperty(name = "code", value = "用户access_token的code", required = true, dataType = "String")
    private String code;

    @ApiModelProperty(name = "grant_type", value = "授权类型", required = false, dataType = "String")
    private String grant_type;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getGrant_type() {
        return grant_type;
    }

    public void setGrant_type(String grant_type) {
        this.grant_type = grant_type;
    }
}
