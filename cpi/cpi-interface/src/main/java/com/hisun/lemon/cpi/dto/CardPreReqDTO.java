package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;

/**
 * 用户银行卡预签约，银行(短信平台)下发短信验证码
 * Created by Rui on 2017/7/5.
 */
@ClientValidated
public class CardPreReqDTO {

    /**
     * 用户名
     */
    @ApiModelProperty(name = "userNm", value = "用户名", required = true, dataType = "String")
    @NotEmpty(message="CPI10004")
    private String userNm;

    /**
     * 银行预留手机号
     */
    @ApiModelProperty(name = "mblNo", value = "银行预留手机号", required = true, dataType = "String")
    @NotEmpty(message="CPI10005")
    private String mblNo;

    /**
     * 证件类型
     */
    @ApiModelProperty(name = "idTyp", value = "证件类型，0是身份证,1是护照", required = true, dataType = "String")
    @NotEmpty(message="CPI10011")
    private String idTyp;

    /**
     * 认证标识 Y-已认证(证件号加密)，N-未认证(证件号未加密)
     */
    @ApiModelProperty(name = "authFlag", value = "认证标识，Y-已认证(传递加密证件号)，N-未认证(传递非证件号)", required = true, dataType = "String")
    @NotEmpty(message="CPI30023")
    private String authFlag;

    /**
     * 证件号
     */
    @ApiModelProperty(name = "idNo", value = "证件号", required = true, dataType = "String")
    @NotEmpty(message="CPI10012")
    private String idNo;

    /**
     * 个企标识，B企业，C个人(默认)
     */
    @ApiModelProperty(name = "bnkPsnFlg", value = "个企标识，B企业，C个人(默认)", required = true, dataType = "String")
    @NotEmpty(message="CPI10015")
    @Pattern(regexp="B|C",message="CPI10013")
    private String bnkPsnFlg;

    /**
     * 卡资金机构
     */
    @ApiModelProperty(name = "crdCorpOrg", value = "资金机构", required = true, dataType = "String")
    @NotEmpty(message="CPI10006")
    private String crdCorpOrg;

    /**
     * 卡种，C贷记卡(信用卡)，D借记卡(默认)
     */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种，C贷记卡(信用卡)，D借记卡", required = true, dataType = "String")
    @NotEmpty(message="CPI10014")
    @Pattern(regexp="D|C",message="CPI10007")
    private String crdAcTyp;

    /**
     * 卡号
     */
    @ApiModelProperty(name = "crdNo", value = "卡号", required = true, dataType = "String")
    @NotEmpty(message="CPI10016")
    private String crdNo;

    /**
     * 银行卡预留户名
     */
    @ApiModelProperty(name = "crdUsrNm", value = "银行卡预留户名", required = true, dataType = "String")
    @NotEmpty(message="CPI10008")
    private String crdUsrNm;

    /**
     * 信用卡cvv2
     */
    @ApiModelProperty(name = "crdCvv2", value = "cvv2", required = false, dataType = "String")
    private String crdCvv2;

    /**
     * 信用卡有效期
     */
    @ApiModelProperty(name = "crdExpDt", value = "有效期", required = false, dataType = "String")
    private String crdExpDt;

    public String getUserNm() {
        return userNm;
    }

    public void setUserNm(String userNm) {
        this.userNm = userNm;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getBnkPsnFlg() {
        return bnkPsnFlg;
    }

    public void setBnkPsnFlg(String bnkPsnFlg) {
        this.bnkPsnFlg = bnkPsnFlg;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getAuthFlag() {
        return authFlag;
    }

    public void setAuthFlag(String authFlag) {
        this.authFlag = authFlag;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getCrdNo() {
        return crdNo;
    }

    public void setCrdNo(String crdNo) {
        this.crdNo = crdNo;
    }

    public String getCrdCvv2() {
        return crdCvv2;
    }

    public void setCrdCvv2(String crdCvv2) {
        this.crdCvv2 = crdCvv2;
    }

    public String getCrdExpDt() {
        return crdExpDt;
    }

    public void setCrdExpDt(String crdExpDt) {
        this.crdExpDt = crdExpDt;
    }
}
