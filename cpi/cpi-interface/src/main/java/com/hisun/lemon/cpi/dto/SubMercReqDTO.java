package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;

/**
 * 平台商户申请微信子商户号，请求报文
 */
@ClientValidated
public class SubMercReqDTO {

    /**
     * 商户编号
     */
    @ApiModelProperty(name = "merchantId", value = "商户编号", required = true, dataType = "String")
    private String merchantId;

    /**
     * 商户名称
     */
    @ApiModelProperty(name = "merchantName", value = "商户名称", required = true, dataType = "String")
    private String merchantName;

    /**
     * 商户简称
     */
    @ApiModelProperty(name = "merchantShortname", value = "商户简称", required = true, dataType = "String")
    private String merchantShortname;

    /**
     * 商户电话
     */
    @ApiModelProperty(name = "officePhone", value = "商户电话", required = true, dataType = "String")
    private String officePhone;

    /**
     * 备注，不能使用数字和中文
     */
    @ApiModelProperty(name = "merchantRemark", value = "备注", required = true, dataType = "String")
    private String merchantRemark;

    /**
     * 商户网址
     */
    @ApiModelProperty(name = "website", value = "商户网址", required = false, dataType = "String")
    private String website;

    /**
     * 联系人姓名，不能使用数字和中文
     */
    @ApiModelProperty(name = "contactName", value = "联系人姓名", required = false, dataType = "String")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(name = "contactPhone", value = "联系人电话", required = false, dataType = "String")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(name = "contactEmail", value = "联系人邮箱", required = false, dataType = "String")
    private String contactEmail;

    /**
     * 微信子商户号
     */
    @ApiModelProperty(name = "subMercId", value = "微信子商户号", required = false, dataType = "String")
    private String subMercId;

    /**
     * 简介
     */
    @ApiModelProperty(name = "mercIntroduction", value = "简介", required = false, dataType = "String")
    private String mercIntroduction;

    /**
     * 业务类别
     */
    @ApiModelProperty(name = "businessType", value = "业务类别", required = false, dataType = "String")
    private String businessType;

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantShortname() {
        return merchantShortname;
    }

    public void setMerchantShortname(String merchantShortname) {
        this.merchantShortname = merchantShortname;
    }

    public String getOfficePhone() {
        return officePhone;
    }

    public void setOfficePhone(String officePhone) {
        this.officePhone = officePhone;
    }

    public String getMerchantRemark() {
        return merchantRemark;
    }

    public void setMerchantRemark(String merchantRemark) {
        this.merchantRemark = merchantRemark;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getSubMercId() {
        return subMercId;
    }

    public void setSubMercId(String subMercId) {
        this.subMercId = subMercId;
    }

    public String getMercIntroduction() {
        return mercIntroduction;
    }

    public void setMercIntroduction(String mercIntroduction) {
        this.mercIntroduction = mercIntroduction;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}
