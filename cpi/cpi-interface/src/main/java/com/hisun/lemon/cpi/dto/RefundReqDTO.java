package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by Rui on 2017/7/5.
 */
@ClientValidated
public class RefundReqDTO {
    /**
     * 商户号
     */
    @ApiModelProperty(name = "userNo", value = "商户号", required = true, dataType = "String")
    @NotNull(message="CPI10001")
    private String userNo;
    /**
     * 原充值订单号
     */
    @ApiModelProperty(name = "ordNo", value = "原充值订单号", required = true, dataType = "String")
    @NotNull(message="CPI10026")
    private String ordNo;
    /**
     * 币种，默认为 USD美元
     */
    @ApiModelProperty(name = "ordCcy", value = "币种", required = true, dataType = "String")
    @NotNull(message="CPI10017")
    private String ordCcy;
    /**
     * 金额，单位默认为美元
     */
    @ApiModelProperty(name = "ordAmt", value = "金额", required = true, dataType = "String")
    @NotNull(message="CPI10018")
    @DecimalMax(value = "99999999999.99", message = "CPI30025")
    @DecimalMin(value = "0", message = "CPI30025")
    private BigDecimal ordAmt;
    /**
     * 协议付款日
     */
    @ApiModelProperty(name = "agrPayDt", value = "协议付款日", required = true, dataType = "LocalDate")
    @NotNull(message="CPI10027")
    private LocalDate agrPayDt;
    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", required = true, dataType = "CorpBusTyp")
    @NotNull(message="CPI10002")
    private CorpBusTyp corpBusTyp;
    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", required = true, dataType = "CorpBusSubTyp")
    @NotNull(message="CPI10003")
    private CorpBusSubTyp corpBusSubTyp;
    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", required = true, dataType = "String")
    @NotNull(message="CPI10021")
    private String reqOrdNo;
    /**
     * 请求日期
     */
    @ApiModelProperty(name = "reqOrdDt", value = "请求订单日期", required = true, dataType = "LocalDate")
    @NotNull(message="CPI10022")
    private LocalDate reqOrdDt;
    /**
     * 请求时间
     */
    @ApiModelProperty(name = "reqOrdTm", value = "请求订单时间", required = true, dataType = "LocalTime")
    @NotNull(message="CPI10023")
    private LocalTime reqOrdTm;

    @ApiModelProperty(name = "innerFlag", value = "内部模块标识", required = true, dataType = "String")
    private String innerFlag;

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public LocalDate getAgrPayDt() {
        return agrPayDt;
    }

    public void setAgrPayDt(LocalDate agrPayDt) {
        this.agrPayDt = agrPayDt;
    }

    public String getInnerFlag() {
        return innerFlag;
    }

    public void setInnerFlag(String innerFlag) {
        this.innerFlag = innerFlag;
    }
}
