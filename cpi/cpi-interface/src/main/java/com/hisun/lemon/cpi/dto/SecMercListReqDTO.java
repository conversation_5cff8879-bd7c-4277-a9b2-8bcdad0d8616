package com.hisun.lemon.cpi.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * 查询支付宝二级商户列表，请求报文
 */
public class SecMercListReqDTO {
    /**
     * 支付宝二级商户号
     */
    @ApiModelProperty(name = "secMercId", value = "支付宝二级商户号", dataType = "String")
    private String secMercId;

    /**
     * 每页大小
     */
    @ApiModelProperty(name = "pageSize", value = "每页大小")
    private Integer pageSize;
    /**
     * 页数
     */
    @ApiModelProperty(name = "pageNo", value = " 页数")
    private Integer pageNo;

    public String getSecMercId() {
        return secMercId;
    }

    public void setSecMercId(String secMercId) {
        this.secMercId = secMercId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }
}
