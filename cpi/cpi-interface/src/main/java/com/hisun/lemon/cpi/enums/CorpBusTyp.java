package com.hisun.lemon.cpi.enums;

/**
 * Created by Rui on 2017/7/5.
 */
public enum CorpBusTyp {
    /**
     * 签约类型
     */
    SIGN("01"),
    /**
     * 快捷
     */
    FASTPAY("02"),
    /**
     * 网银（支付宝，微信）
     */
    EBANKPAY("03"),
    /**
     * 汇款
     */
    REMITTANCE("04"),
    /**
     * 退款
     */
    REFUND("05"),
    /**
     * 提现
     */
    WITHDRAW("06"),
    /**
     * POS收单
     */
    POS("07"),
    /**
     * 撤销
     */
    CANCEL("08");

    CorpBusTyp(String type) {
        this.type = type;
    }

    // 成员变量
    private String type;

    public String getType() {
        return type;
    }
}
