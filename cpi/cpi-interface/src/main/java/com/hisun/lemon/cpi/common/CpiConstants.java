package com.hisun.lemon.cpi.common;

/**
 * Created by <PERSON><PERSON> on 2017/7/15.
 */
public class CpiConstants {

    /**
     * 短信下发方式
     * 0：平台下发平台校验
     * 1: 银行下发平台校验
     * 2: 银行下发银行校验
     */
    public static final String PLAT_SMS_SEND_PLAT_CHECK = "0";
    public static final String BANK_SMS_SEND_PLAT_CHECK = "1";
    public static final String BANK_SMS_SEND_BANK_CHECK = "2";

    /**
     * 订单状态
     * W1:等待充值或者等待退款
     * W2:充值银行处理中或者退款处理中
     * W3:提现申请 (待审核)
     * A1:待复核
     * W4:提现银行处理中
     * S1:订单处理成功
     * F1:订单处理失败
     * R1:部分退款
     * R2:全额退款
     * R3:订单已撤销
     */
    public static final String ORD_WATING_PAY = "W1";
    public static final String ORD_PAY_BANKING = "W2";
    public static final String ORD_WAITING_WITHDRAW = "W3";
    public static final String WAIT_REVIEW = "A1";
    public static final String ORD_WAITING_BANKING = "W4";
    public static final String ORD_SUCCESS = "S1";
    public static final String ORD_FAIL = "F1";
    public static final String ORD_WATING_REFUND = "W1";
    public static final String ORD_REFUND_BANKING = "W2";
    public static final String ORD_REFUND_PART = "R1";
    public static final String ORD_REFUND_ALL = "R2";
    public static final String ORD_REVOKE = "R3";

    /**
     * pos预授权状态
     * S:预授权
     * C:预授权撤销
     * S1:预授权成功
     * C1:预授权撤销成功
     */
    public static final String POS_PRE_AU = "S";
    public static final String POS_PRE_AU_CANCEL = "C";
    public static final String POS_PRE_AU_FINISH = "S1";
    public static final String POS_PRE_AU_FINISH_CANCEL = "C1";

    /**
     * 工行业务相关参数
     * ICBC: 工行
     * ICBC_CHECK_TX_TYP_SHORTCUT: 工行对账，交易类型为 消费(快捷充值)
     */
    public static final String ICBC = "ICBC";
    public static final String ICBC_CHECK_TX_TYP_SHORTCUT = "1";
    public static final String ICBC_CHECK_TX_TYP_REFUND = "3";

    /**
     * 翼支付相关参数
     * BESTPAY: 翼支付
     */
    public static final String BESTPAY = "BESTPAY";

    /**
     * 微信支付相关参数
     * WeChat: 微信支付
     */
    public static final String WECHAT = "WeChat";

    /**
     * 支付宝支付相关参数
     * ALIPAY: 支付宝支付
     */
    public static final String ALIPAY = "ALIPAY";

    /**
     * 银联国际相关参数
     * UPI: 银联国际
     */
    public static final String UPI = "UPI";

    /**
     * 营业厅相关参数
     * HALL: 营业厅
     */
    public static final String HALL = "HALL";

    /**
     * ACLEDA银行
     * ACLEDA
     */
    public static final String ACLEDA = "ACLEDA";

    /**
     * 银行返回结果
     * S：成功
     * W:等待，银行处理中
     * F:失败
     */
    public static final String BANK_RSP_SUC = "S";
    public static final String BANK_RSP_WAIT = "W";
    public static final String BANK_RSP_FAIL = "F";

    /**
     * 卡种类型
     * DEBIT_CARD：借记卡
     * CREDIT_CARD：贷记卡，信用卡
     * CARD_UNKNOWN: 未知
     */
    public static final String DEBIT_CARD = "D";
    public static final String CREDIT_CARD = "C";
    public static final String CARD_UNKNOWN = "U";

    /**
     * 对账批次锁状态，U-未加锁，L-已加锁
     */
    public static final String LOCK_STATUS_UNLOCK ="U";
    public static final String LOCK_STATUS_LOCKED ="L";

    /**
     * 对账锁前缀
     */
    public static final String CHK_LOCK_PREFIX = "CHK_";

    /**
     * 对账批次的对账状态
     * 0: 未对账
     * 1: 对账文件已下载
     * 2: 对账文件明细已入库
     * 3: 对账成功，待账务处理
     * 4: 对账结束
     * W: 写入对账文件
     * B：上传银行文件
     * L：上传本地文件
     */
    public static final String CHK_FIL_NOT_START = "0";
    public static final String CHK_FIL_DOWNLOAD = "1";
    public static final String CHK_FIL_IMPORT = "2";
    public static final String CHK_FIL_SUCCESS = "3";
    public static final String CHK_FIL_FINISHED = "4";
    public static final String CHK_FIL_WRITE = "W";
    public static final String CHK_FIL_BANK_UPLODAD = "B";
    public static final String CHK_FIL_LOCAL_UPLODAD = "L";

    /**
     * 子订单的对账状态
     * 0: 未对账
     * 1: 对账成功
     * 2: 我方有机构无
     * 3: 机构有我方无
     * 4: 金额错误
     * 5: 存疑
     */
    public static final String CHK_STS_NOT_START = "0";
    public static final String CHK_STS_SUCCESS = "1";
    public static final String CHK_STS_PLAT_EXIST = "2";
    public static final String CHK_STS_ORG_EXIST = "3";
    public static final String CHK_STS_AMT_ERROR = "4";
    public static final String CHK_STS_DOUBT = "5";

    /**
     * 差错类型
     * 2：短款差错
     * 3：长款差错
     * 4：金额错误
     */
    public static final String CHK_ERR_TYP_SHORT = "2";
    public static final String CHK_ERR_TYP_LONG = "3";
    public static final String CHK_ERR_TYP_AMTERR = "4";

    /**
     * 通知类型
     * W：等待通知
     * S：通知成功
     */
    public static final String NOTIFY_WAIT = "W";
    public static final String NOTIFY_SUCCESS = "S";
    public static final String NOTIFY_FAIL = "F";

    /**
     * 签约类型
     * P：预签约
     * S：绑定
     * C：解绑
     */
    public static final String SIGN_PRE = "P";
    public static final String SIGN_SUCCESS = "S";
    public static final String SIGN_CANCEL = "C";

    /**
     * 协议生效失效标识
     * Y：生效
     * N：失效
     * 1：生效
     * 0：失效
     */
    public static final String AGR_EFFECT_YES= "Y";
    public static final String AGR_EFFECT_NO = "N";
    public static final String EFFECT_YES= "1";
    public static final String EFFECT_NO = "0";

    /**
     * 银行返回状态
     * W：处理中
     * S: 处理成功
     * F: 处理失败
     */
    public static final String BANK_RESPONSE_WAIT = "W";
    public static final String BANK_RESPONSE_SUCCESS = "S";
    public static final String BANK_RESPONSE_FAIL = "F";
    public static final String BANK_RESPONSE_CANCEL_SUCCESS = "C";

    /**
     * 短信校验标识
     * Y：需要校验
     * N: 无需校验
     */
    public static final String SMS_CHECK_YES = "Y";
    public static final String SMS_CHECK_NO = "N";

    /**
     * 自动付款标识
     * Y：自动
     * N：手工
     */
    public static final String PAY_FLAG_AUTO = "Y";
    public static final String PAY_FLAG_MANUAL = "N";

    /**
     * 补单标识
     * Y：允许
     * N：不允许
     */
    public static final String SPL_ABLE_FLG_YES = "Y";
    public static final String SPL_ABLE_FLG_NO = "N";

    /**
     * 撤单标识
     * Y：允许
     * N：不允许
     */
    public static final String CAN_ABLE_FLG_YES = "Y";
    public static final String CAN_ABLE_FLG_NO = "N";

    /**
     * 差错处理标识
     * 0：待处理
     * 1：已补单
     * 2：已撤单
     * 3：人工取消
     */
    public static final String ERR_STS_INIT = "0";
    public static final String ERR_STS_ADDTION = "1";
    public static final String ERR_STS_CANCEL = "2";
    public static final String ERR_STS_MANUAL = "3";

    /**
     * 快捷认证标识
     * Y: 已认证
     * N: 未认证
     */
    public static final String AUTHENTICATE_YES = "Y";
    public static final String AUTHENTICATE_NO = "N";

    /**
     * 用户类型
     * U：用户
     * M：商户
     */
    public static final String USER_TYP_USER = "U";
    public static final String USER_TYP_MERC = "M";

    /**
     * 交易类型
     * N：对账模块对
     * Y：内部对
     */
    public static final String OWN_CHK_FLG_N = "N";
    public static final String OWN_CHK_FLG_Y = "Y";

    /**
     * 交易方式
     */
    public static final String TX_TYP_RECHARGE = "01";      //充值
    public static final String TX_TYP_CONSUME = "02";       //消费
    public static final String TX_TYP_TRANSFER = "03";      //转账
    public static final String TX_TYP_WITHDRAW = "04";      //提现
    public static final String TX_TYP_SEATEL = "05";        //充值海币
    public static final String TX_TYP_REFUNDS = "06";       //退款
    public static final String TX_TYP_INTEREST = "07";      //理财
    public static final String TX_TYP_PAYMENT = "08";       //缴费

    /**
     * 结算模式
     * 1：收支两条线
     * 2：轧差
     */
    public static final String IO_FLG_IN_OUT = "1";  //收支两条线
    public static final String IO_FLG_OFFSET = "2";  //轧差

    /**
     * 配置查询分页最大数值
     * MAX_PAGE_NUM: 分页查询最大页数（退款通知）
     * MAX_QUERY_NUM: 每页最大笔数（退款通知）
     * MAX_WRITE_NUM: 分页写入最大笔数（写对账文件用）
     * MAX_BATCH_NUM: 插入数据库最大笔数（批量入库用）
     */
    public final static int MAX_PAGE_NUM = 20;
    public final static int MAX_QUERY_NUM = 500;
    public final static int MAX_WRITE_NUM = 200;
    public final static int MAX_BATCH_NUM = 200;

    /**
     * 网银校验子类型
     * 1：校验网银充值和消费
     * 2：校验商户扫码收款
     * 3：僬侥用户扫码付款
     */
    public static final String VERIFY_EBANK = "1";  //校验网银充值和消费
    public static final String VERITY_MERC = "2";  //校验商户扫码收款
    public static final String VERITY_USER = "3";  //僬侥用户扫码付款

    /**
     * 身份类型
     * 0：身份证
     * 1: 护照
     */
    public static final String ID_TYP_ID = "0"; //身份类型
    public static final String ID_TYP_PASSPORT = "1"; //护照

    /**
     * 是否需要检查userId
     * Y：需要
     * N：不需要
     */
    public static final String CHK_USER_ID_YES = "Y";
    public static final String CHK_USER_ID_NO = "N";

    /**
     * 微信报文头返回码成功
     * PAYERROR：支付异常
     * CHANGE：退款异常
     * REVOKED：订单已撤销(刷卡支付)
     * USERPAYING：用户支付中，可能需要用户输入支付密码
     */
    public static final String WECHAT_HEAD_SUCCESS = "000000";
    public static final String WECHAT_SUCCESS = "SUCCESS";
    public static final String WECHAT_FAIL = "FAIL";
    public static final String WECHAT_PAYERROR = "PAYERROR";
    public static final String WECHAT_REVOKED = "REVOKED";
    public static final String WECHAT_CHANGE = "CHANGE";
    public static final String WECHAT_USERPAYING = "USERPAYING";

    /**
     * 翼支付处理成功返回码
     */
    public static final String BESTPAY_RESULT_SUCCESS = "CBP00000";

    /**
     * 支付宝报文头返回码成功
     * T：请求成功
     * F：请求异常
     * SUCCESS：支付成功
     * FAIL：支付失败
     */
    public static final String ALIPAY_T= "T";
    public static final String ALIPAY_F= "F";
    public static final String ALIPAY_SUCCESS = "SUCCESS";
    public static final String ALIPAY_FAIL = "FAIL";
    public static final String ALIPAY_TRADE_SUCCESS = "TRADE_SUCCESS";
    public static final String ALIPAY_UNKNOW = "UNKNOW";


    /**
     * ACLEDA报文头返回码成功
     * T：请求成功
     * F：请求异常
     * SUCCESS：支付成功
     * FAIL：支付失败
     */
    public static final String ACLEDA_REQ_SUCCESS= "SUCCESS";
    public static final String ACLEDA_SUCCESS_CODE= "0";
    public static final String ACLEDA_SYSTEM_ERROR= "-1";
    public static final String ACLEDA_INPUT_DATA_TAMPERED = "-2";
    public static final String ACLEDA_CHANNEL_NOT_FOUND = "-3";
    public static final String ACLEDA_MERCHANT_NOT_ACTIVE = "-5";
    public static final String ACLEDA_MERCHANT_SECRET_VALIDATE_FAIL = "-245";
    public static final String ACLEDA_PAYMENT_NOT_SELECTED = "-15";
    public static final String ACLEDA_GENER_TOKENID_FAIL = "-17";
    public static final String ACLEDA_OPEN_SESSION_FAIL = "-14";


    /**
     * 加解密类型 encrypt:加密  decrypt:解密
     */
    public static final String ENCRYPT = "encrypt";
    public static final String DECRYPT = "decrypt";

    /**
     * 结算类型 1-已结算；2-未结算
     */
    public static final int SETTLEMENT = 1;
    public static final int UNSETTLEMENT = 2;

    /**
     * 账户类型（PS:平台, 商户:MA）
     */
    public static final String ACC_TYP_PS = "PS";
    public static final String ACC_TYP_MA = "MA";

    /**
     * cregis回调成功状态码
     */
    public static final String CREGIS_SUCCESS = "CONFIRMED_SUCCESS";

    /**
     * 平台USDT-TRON本金地址
     */
    public static final String USDT_P_TRON = "TN2pKqCX4YCiBHvfzeNhpK95kskA62Mj1E";

    /**
     * 平台USDT-ETH本金地址
     */
    public static final String USDT_P_ETH = "******************************************";

}
