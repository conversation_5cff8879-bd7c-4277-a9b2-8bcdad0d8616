package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 平台商户申请微信子商户号，请求报文
 */
@ClientValidated
public class SubMercListRspDTO {

    private List<SubMercListDO> list;

    private Long total;

    public static class SubMercListDO {
        /**
         * 公众号
         */
        private String appId;
        /**
         * 商户编号
         */
        private String mchId;

        /**
         * 商户名称
         */
        private String mercNm;

        /**
         * 商户简称
         */
        private String mercSnm;

        /**
         * 商户电话
         */
        private String officeTel;

        /**
         * 备注，不能使用数字和中文
         */
        private String mercRmk;

        /**
         * 商户网址
         */
        private String mercWebsite;

        /**
         * 联系人姓名，不能使用数字和中文
         */
        private String contractNm;

        /**
         * 联系人电话
         */
        private String contractTel;

        /**
         * 联系人邮箱
         */
        private String contractWebsite;

        /**
         * 微信子商户号
         */
        private String subMchId;

        /**
         * 简介
         */
        private String mercIntroduction;

        /**
         * 业务类别
         */
        private String businessType;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getMchId() {
            return mchId;
        }

        public void setMchId(String mchId) {
            this.mchId = mchId;
        }

        public String getMercNm() {
            return mercNm;
        }

        public void setMercNm(String mercNm) {
            this.mercNm = mercNm;
        }

        public String getMercSnm() {
            return mercSnm;
        }

        public void setMercSnm(String mercSnm) {
            this.mercSnm = mercSnm;
        }

        public String getOfficeTel() {
            return officeTel;
        }

        public void setOfficeTel(String officeTel) {
            this.officeTel = officeTel;
        }

        public String getMercRmk() {
            return mercRmk;
        }

        public void setMercRmk(String mercRmk) {
            this.mercRmk = mercRmk;
        }

        public String getMercWebsite() {
            return mercWebsite;
        }

        public void setMercWebsite(String mercWebsite) {
            this.mercWebsite = mercWebsite;
        }

        public String getContractNm() {
            return contractNm;
        }

        public void setContractNm(String contractNm) {
            this.contractNm = contractNm;
        }

        public String getContractTel() {
            return contractTel;
        }

        public void setContractTel(String contractTel) {
            this.contractTel = contractTel;
        }

        public String getContractWebsite() {
            return contractWebsite;
        }

        public void setContractWebsite(String contractWebsite) {
            this.contractWebsite = contractWebsite;
        }

        public String getSubMchId() {
            return subMchId;
        }

        public void setSubMchId(String subMchId) {
            this.subMchId = subMchId;
        }

        public String getMercIntroduction() {
            return mercIntroduction;
        }

        public void setMercIntroduction(String mercIntroduction) {
            this.mercIntroduction = mercIntroduction;
        }

        public String getBusinessType() {
            return businessType;
        }

        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }
    }

    public List<SubMercListDO> getList() {
        return list;
    }

    public void setList(List<SubMercListDO> list) {
        this.list = list;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }
}
