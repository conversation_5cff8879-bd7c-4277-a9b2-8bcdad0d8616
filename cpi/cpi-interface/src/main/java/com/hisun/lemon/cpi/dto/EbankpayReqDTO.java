package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by Rui on 2017/7/5.
 */
@ClientValidated
public class EbankpayReqDTO {

    /**
     * 币种，USD美元
     */
    @ApiModelProperty(name = "ordCcy", value = "币种，USD美元", required = true, dataType = "String")
    @NotEmpty(message="CPI10017")
    private String ordCcy;

    /**
     * 金额，单位元
     */
    @ApiModelProperty(name = "ordAmt", value = "金额，单位元", required = true, dataType = "String")
    @NotNull(message="CPI10018")
    private BigDecimal ordAmt;

    /**
     * 资金机构
     */
    @ApiModelProperty(name = "crdCorpOrg", value = "资金机构", required = true, dataType = "String")
    @NotEmpty(message="CPI10006")
    private String crdCorpOrg;

    /**
     * 个企标识，B企业，C个人
     */
    @ApiModelProperty(name = "bnkPsnFlg", value = "个企标识，B企业，C个人", required = true, dataType = "String")
    @NotEmpty(message="CPI10015")
    @Pattern(regexp="B|C",message="CPI10013")
    private String bnkPsnFlg;

    /**
     * 用户类型，U：用户，M：商户
     */
    @ApiModelProperty(name = "userTyp", value = "用户类型，U：用户，M：商户", required = true, dataType = "String")
    @NotEmpty(message="CPI10015")
    @Pattern(regexp="U|M",message="CPI10013")
    private String userTyp;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userNo", value = "用户id", required = false, dataType = "String")
    private String userNo;

    /**
     * 卡种
     */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种，D借记卡，C贷记卡，U卡种未知", required = true, dataType = "String")
    @NotEmpty(message="CPI10014")
    @Pattern(regexp="D|C|U",message="CPI10007")
    private String crdAcTyp;

    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", required = true, dataType = "CorpBusTyp")
    @NotNull(message="CPI10002")
    private CorpBusTyp corpBusTyp;

    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", required = true, dataType = "CorpBusSubTyp")
    @NotNull(message="CPI10003")
    private CorpBusSubTyp corpBusSubTyp;

    /**
     * 是否注册标识
     */
    @ApiModelProperty(name = "registerFlag", value = "是否注册标识，Y已注册，N未注册", required = true, dataType = "String")
    @NotNull(message="CPI10031")
    @Pattern(regexp="Y|N",message="CPI10032")
    private String registerFlag;

    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", required = true, dataType = "String")
    @NotEmpty(message="CPI10021")
    private String reqOrdNo;

    /**
     * 请求日期
     */
    @ApiModelProperty(name = "reqOrdDt", value = "请求订单日期", required = true, dataType = "LocalDate")
    @NotNull(message="CPI10022")
    private LocalDate reqOrdDt;

    /**
     * 请求时间
     */
    @ApiModelProperty(name = "reqOrdTm", value = "请求订单时间", required = true, dataType = "LocalTime")
    @NotNull(message="CPI10023")
    private LocalTime reqOrdTm;

    /**
     * 二维码信息
     */
    @ApiModelProperty(name = "authCode", value = "二维码信息", required = false, dataType = "String")
    private String authCode;

    /**
     * 条形码信息
     */
    @ApiModelProperty(name = "barCode", value = "条形码信息", required = false, dataType = "String")
    private String barCode;

    /**
     * 用户翼支付登录手机号(可能适用微信、支付宝等)
     */
    @ApiModelProperty(name = "mblNo", value = "用户翼支付登录手机号", required = false, dataType = "String")
    private String mblNo;

    /**
     * 用户openId，微信支付时必输，由微信在页面授权时返回
     */
    @ApiModelProperty(name = "openId", value = "用户openId", required = false, dataType = "String")
    private String openId;

    /**
     * 企业在平台的商户编号，微信支付时必输
     */
    @ApiModelProperty(name = "merchantId", value = "收款方商户编号", required = false, dataType = "String")
    private String merchantId;

    /**
     * 网银交易类型
     */
    @ApiModelProperty(name = "tradeType", value = "网银交易类型", required = false, dataType = "String")
    private String tradeType;

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getBnkPsnFlg() {
        return bnkPsnFlg;
    }

    public void setBnkPsnFlg(String bnkPsnFlg) {
        this.bnkPsnFlg = bnkPsnFlg;
    }

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getRegisterFlag() {
        return registerFlag;
    }

    public void setRegisterFlag(String registerFlag) {
        this.registerFlag = registerFlag;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getUserTyp() {
        return userTyp;
    }

    public void setUserTyp(String userTyp) {
        this.userTyp = userTyp;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }
}
