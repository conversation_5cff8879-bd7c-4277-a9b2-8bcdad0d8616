package com.hisun.lemon.cpi.dto;



import java.util.List;
import java.util.Map;

/**
 * 提交转账的请求对象
 */
public class TransferSubmitReqDTO {
        private String taskId;
        private String vaultCode;
        private String groupCode;
        private Long accountId;
        private String submitter;
        private String cmdType;
        private ChainCmdBasicDTO cmdBasic;
        private Object cmdForm;
        private String state;
        private String businessType;
        private String signContent;
        private Integer threshold;
        private List<String> signers;
        private Integer signersLevel;


        private List<String> signatures;
        private String orderId;
        private String coinId;
        private List<String> includes;

        public TransferSubmitReqDTO() {
        }

        public TransferSubmitReqDTO(String taskId, String vaultCode, String groupCode, Long accountId, String submitter, String cmdType, ChainCmdBasicDTO cmdBasic, Object cmdForm, String state, String businessType, String signContent, Integer threshold, List<String> signers, Integer signersLevel, List<String> signatures, String orderId, String coinId, List<String> includes) {
                this.taskId = taskId;
                this.vaultCode = vaultCode;
                this.groupCode = groupCode;
                this.accountId = accountId;
                this.submitter = submitter;
                this.cmdType = cmdType;
                this.cmdBasic = cmdBasic;
                this.cmdForm = cmdForm;
                this.state = state;
                this.businessType = businessType;
                this.signContent = signContent;
                this.threshold = threshold;
                this.signers = signers;
                this.signersLevel = signersLevel;
                this.signatures = signatures;
                this.orderId = orderId;
                this.coinId = coinId;
                this.includes = includes;
        }

        public List<String> getIncludes() {
                return includes;
        }

        public void setIncludes(List<String> includes) {
                this.includes = includes;
        }

        public String getTaskId() {
                return taskId;
        }

        public void setTaskId(String taskId) {
                this.taskId = taskId;
        }

        public String getVaultCode() {
                return vaultCode;
        }

        public void setVaultCode(String vaultCode) {
                this.vaultCode = vaultCode;
        }

        public String getGroupCode() {
                return groupCode;
        }

        public void setGroupCode(String groupCode) {
                this.groupCode = groupCode;
        }

        public Long getAccountId() {
                return accountId;
        }

        public void setAccountId(Long accountId) {
                this.accountId = accountId;
        }

        public String getSubmitter() {
                return submitter;
        }

        public void setSubmitter(String submitter) {
                this.submitter = submitter;
        }

        public String getCmdType() {
                return cmdType;
        }

        public void setCmdType(String cmdType) {
                this.cmdType = cmdType;
        }

        public ChainCmdBasicDTO getCmdBasic() {
                return cmdBasic;
        }

        public void setCmdBasic(ChainCmdBasicDTO cmdBasic) {
                this.cmdBasic = cmdBasic;
        }

        public Object getCmdForm() {
                return cmdForm;
        }

        public void setCmdForm(Object cmdForm) {
                this.cmdForm = cmdForm;
        }

        public String getState() {
                return state;
        }

        public void setState(String state) {
                this.state = state;
        }

        public String getBusinessType() {
                return businessType;
        }

        public void setBusinessType(String businessType) {
                this.businessType = businessType;
        }

        public String getSignContent() {
                return signContent;
        }

        public void setSignContent(String signContent) {
                this.signContent = signContent;
        }

        public Integer getThreshold() {
                return threshold;
        }

        public void setThreshold(Integer threshold) {
                this.threshold = threshold;
        }

        public List<String> getSigners() {
                return signers;
        }

        public void setSigners(List<String> signers) {
                this.signers = signers;
        }

        public Integer getSignersLevel() {
                return signersLevel;
        }

        public void setSignersLevel(Integer signersLevel) {
                this.signersLevel = signersLevel;
        }

        public List<String> getSignatures() {
                return signatures;
        }

        public void setSignatures(List<String> signatures) {
                this.signatures = signatures;
        }

        public String getOrderId() {
                return orderId;
        }

        public void setOrderId(String orderId) {
                this.orderId = orderId;
        }

        public String getCoinId() {
                return coinId;
        }

        public void setCoinId(String coinId) {
                this.coinId = coinId;
        }
}