package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Cregis回调数据请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 10:12
 */
@ClientValidated
@ApiModel(value = "CregisCallbackDTO", description = "Cregis回调数据请求DTO")
public class CregisCallbackDTO {

    @ApiModelProperty(value = "EVM交易基础信息")
    private SolTxBase txBase;

    @ApiModelProperty(value = "所属金库编码")
    private String vaultCode;

    @ApiModelProperty(value = "关联账户ID")
    private Long accountId;

    /**
     * 账户类型（PS:平台, 商户:MA）
     */
    @ApiModelProperty(name = "accountType", value = "账户类型; 前端不需要传，由后端查询得出")
    private String accountType;

    @ApiModelProperty(value = "平台订单ID（内部唯一标识）", required = true)
    private String orderId;

    @ApiModelProperty(value = "币种标识（如SOL）")
    private String coinId;

    @ApiModelProperty(value = "网络标识（同tx_base_network，冗余存储）")
    private String network;

    @ApiModelProperty(value = "本平台地址", required = true)
    private String address;

    @ApiModelProperty(value = "对手方地址")
    private String cpAddress;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "交易方向（IN：入账；OUT：出账）")
    private String direction;

    public SolTxBase getTxBase() {
        return txBase;
    }

    public void setTxBase(SolTxBase txBase) {
        this.txBase = txBase;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCoinId() {
        return coinId;
    }

    public void setCoinId(String coinId) {
        this.coinId = coinId;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCpAddress() {
        return cpAddress;
    }

    public void setCpAddress(String cpAddress) {
        this.cpAddress = cpAddress;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    /**
     * EVM交易基础信息
     */
    public static class SolTxBase {
        @ApiModelProperty(value = "网络标识（如solana-testnet）")
        private String network;

        @ApiModelProperty(value = "区块ID")
        private Long blockId;

        @ApiModelProperty(value = "交易哈希（链上唯一标识）")
        private String txId;

        @ApiModelProperty(value = "错误码（成功时为空）")
        private String ecode;

        @ApiModelProperty(value = "交易组ID")
        private String groupId;

        @ApiModelProperty(value = "交易手续费")
        private BigDecimal fee;

        @ApiModelProperty(value = "交易状态（如ACCEPTED/SUCCESS/FAILED）")
        private String status;

        @ApiModelProperty(value = "交易创建时间（精确到微秒）")
        private String createTime;

        @ApiModelProperty(value = "区块哈希")
        private String blockHash;

        @ApiModelProperty(value = "合约程序ID")
        private String programId;

        @ApiModelProperty(value = "消耗的计算单元")
        private Long computeUnitsConsumed;

        @ApiModelProperty(value = "交易备注信息")
        private String memo;

        public String getNetwork() {
            return network;
        }

        public void setNetwork(String network) {
            this.network = network;
        }

        public Long getBlockId() {
            return blockId;
        }

        public void setBlockId(Long blockId) {
            this.blockId = blockId;
        }

        public String getTxId() {
            return txId;
        }

        public void setTxId(String txId) {
            this.txId = txId;
        }

        public String getEcode() {
            return ecode;
        }

        public void setEcode(String ecode) {
            this.ecode = ecode;
        }

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getBlockHash() {
            return blockHash;
        }

        public void setBlockHash(String blockHash) {
            this.blockHash = blockHash;
        }

        public String getProgramId() {
            return programId;
        }

        public void setProgramId(String programId) {
            this.programId = programId;
        }

        public Long getComputeUnitsConsumed() {
            return computeUnitsConsumed;
        }

        public void setComputeUnitsConsumed(Long computeUnitsConsumed) {
            this.computeUnitsConsumed = computeUnitsConsumed;
        }

        public String getMemo() {
            return memo;
        }

        public void setMemo(String memo) {
            this.memo = memo;
        }
    }
}