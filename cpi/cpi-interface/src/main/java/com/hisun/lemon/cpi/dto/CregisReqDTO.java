package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Cregis 账户详情查询请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18 14:36
 */
@ClientValidated
@ApiModel("Cregis 账户详情查询请求类")
public class CregisReqDTO {

    /**
     * 查询条件列表
     */
    private CregisAccDetailReqDTO cregisAccDetailReqDTO;

    @NotNull(message = "vaultCode不能为空")
    private String vaultCode;
    @NotNull(message = "groupCode不能为空")
    private String groupCode;
    @NotNull(message = "accountId不能为空")
    private String accountId;


    private String ccy;

    public CregisAccDetailReqDTO getCregisAccDetailReqDTO() {
        return cregisAccDetailReqDTO;
    }

    public void setCregisAccDetailReqDTO(CregisAccDetailReqDTO cregisAccDetailReqDTO) {
        this.cregisAccDetailReqDTO = cregisAccDetailReqDTO;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }
}
