package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by Rui on 2017/7/18.
 */
@FeignClient("CPI")
public interface FastpayClient {

    /**
     * 快捷预签约
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/fastpay/pre/cards")
    GenericRspDTO<CardPreRspDTO> preBindCard(@RequestBody GenericDTO<CardPreReqDTO> genericDTO);

    /**
     * 快捷绑卡签约
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/fastpay/cards")
    GenericRspDTO<CardBindRspDTO> bindCard(@RequestBody GenericDTO<CardBindReqDTO> genericDTO);

    /**
     * 快捷解绑银行卡
     * @param genericDTO
     * @return
     */
    @PutMapping("/cpi/fastpay/cards")
    GenericRspDTO<NoBody> unBindCard(@RequestBody GenericDTO<CardUnBindReqDTO> genericDTO);

    /**
     * 快捷支付
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/fastpay/payment")
    GenericRspDTO<FastpayRspDTO> fastpay(@RequestBody GenericDTO<FastpayReqDTO> genericDTO);

    /**
     * 快捷订单查询
     * @param ordNo
     * @return
     */
    @GetMapping("/cpi/fastpay/result")
    GenericRspDTO<OrderResultRspDTO> orderQuery(@RequestParam(value = "ordNo") String ordNo);

    /**
     * ACLEDA快捷支付
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/fastpay/payment/acleda")
    GenericRspDTO<FastpayAcledaRspDTO> fastpayAcleda( @RequestBody GenericDTO<FastpayAcledaReqDTO> genericDTO);

    /**
     * ACLEDA快捷订单查询
     * @param genericDTO
     * @return
     */
    @GetMapping("/cpi/fastpay/acleda/notify")
    GenericRspDTO acledaPayNotify(@RequestBody GenericDTO<FastpayAcledaNotifyReqDTO> genericDTO);
}
