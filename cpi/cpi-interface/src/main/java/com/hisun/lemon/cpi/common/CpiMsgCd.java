package com.hisun.lemon.cpi.common;


/**
 * 错误码
 * <AUTHOR>
 * @date 2017年6月14日
 * @time 下午4:40:05
 *
 */
public enum CpiMsgCd {
    SUCCESS("CPI00000", ""),

    USER_IS_NULL("CPI10001", "userId is null."),
    CORP_BUS_TYP_IS_NULL("CPI10002", "corpBusTyp is null."),
    CORP_BUS_SUB_TYP_IS_NULL("CPI10003", "corpBusSubTyp is null."),
    USER_NM_IS_NULL("CPI10004", "userNm is null."),
    MBL_NO_IS_NULL("CPI10005", "mblNo is null."),
    CARD_CORP_ORG_IS_NULL("CPI10006", "crdCorpOrg is null."),
    CARD_AC_TYPE_IS_NOT_EXPECTED("CPI10007", "crdAcTyp is not expected value."),
    CARD_USER_NAME_IS_NULL("CPI10008", "crdUsrNm is null."),
    RUT_CORP_ORG_IS_NULL("CPI10009", "rutCorpOrg is null."),
    PRE_SIGN_JRN_NO_IS_NULL("CPI10010", "pre sign jrnNo is null."),
    ID_TYPE_IS_NULL("CPI10011", "idTyp is null."),
    ID_NO_IS_NULL("CPI10012", "idNo is null."),
    BANK_PERSON_IS_NOT_EXPECTED("CPI10013", "bnkPsnFlg is not expected value."),
    CARD_AC_TYPE_IS_NULL("CPI10014", "crdAcTyp is null."),
    BANK_PERSON_IS_NULL("CPI10015", "bnkPsnFlg is null."),
    CARD_NO_IS_NULL("CPI10016", "crdNo is null."),
    ORD_CCY_IS_NULL("CPI10017", "ordCcy is null."),
    ORD_AMT_IS_NULL("CPI10018", "ordAmt is null."),
    USER_TYPE_IS_NOT_EXPECTED("CPI10019", "userTyp is not expected value."),
    USER_TYPE_PERSON_IS_NULL("CPI10020", "userTyp is null."),
    REQ_ORD_NO_IS_NULL("CPI10021", "reqOrdNo is null."),
    REQ_ORD_DT_IS_NULL("CPI10022", "reqOrdDt is null."),
    REQ_ORD_TM_IS_NULL("CPI10023", "reqOrdTm is null."),
    ARG_NO_IS_NULL("CPI10024", "argNo is null."),
    SMS_FLAG_IS_NULL("CPI10025", "smsFlag is null."),
    ORD_NO_IS_NULL("CPI10026", "ordNo is null."),
    AGR_PAY_DT_IS_NULL("CPI10027", "agrPayDt is null."),
    CHK_KEY_IS_NULL("CPI10028", "chkKey is null."),
    ORD_STS_IS_NULL("CPI10029", "ordSts is null."),
    PIC_URL_IS_NULL("CPI10030", "picUrl is null."),
    REGISTER_FLAG_IS_NULL("CPI10031", "register flag is null."),
    REGISTER_FLAG_IS_NOT_EXPECTED("CPI10032", "register flag is not expected value."),
    TRM_NO_IS_NOT_NULL("CPI10033", "trmNo is null."),

    CORP_BUS_TYP_IS_ERROR("CPI30001", "corpBusTyp is error"),
    CORP_BUS_SUB_TYP_IS_ERROR("CPI30002", "corpBusSubTyp is error"),
    AGR_IS_EMPTY("CPI30003", " agreement is empty"),
    CARD_BIN_IS_NOT_FUND("CPI30004", " card bin is not fund"),
    ROUTE_INFO_IS_NOT_FUND("CPI30005", "route info is not fund"),
    AGR_IS_NOT_FUND("CPI30006","agreement info is not fund"),
    ARG_LOSE_EFFECTIVENESS("CPI30007","agreement lose effectiveness"),
    REFUND_AMT_IS_ERROR("CPI30008","refund amt is error"),
    REFUND_PARAM_IS_NOT_FUND("CPI30009","refund param is not fund"),
    FORBID_REFUND_TODAY("CPI30010","forbid refund today"),
    FORBID_MULTI_REFUND("CPI30011","forbid multi refund"),
    FORBID_REFUND("CPI30012","forbid refund"),
    BIND_CARD_INFO_ERROR("CPI30013","bind card info error"),
    FAST_PAYMENT_FAIL("CPI30014","fast payment fail"),
    REFUND_TIME_IS_ERROR("CPI30015","refund time error"),
    ROUTE_NOT_FIND("CPI30016","route is not find"),
    CREATE_CHK_BAT_FAIL("CPI30017", "create chkbat fail"),
    CHECK_ACC_FAIL("CPI30018", "check acc fail"),
    DOWNLOAD_CHECK_FILE_FAIL("CPI30019", "download check file fail"),
    IMPORT_CHECK_FILE_FAIL("CPI30020", "import check file fail"),
    IMPORT_CHECK_FILE_REPEATED("CPI30021", "import check file repeated"),
    AMT_IS_NOT_EQUAL("CPI30022", "the amt of bank is not equal the amt of plat"),
    /**用户预签约认证标识错误*/
    AUTH_FLAG_IS_ERROR("CPI30023", "auth flag is error"),
    /**合作资金机构信息不存在*/
    ORG_INFO_IS_NOT_FOUND("CPI30024", "crdCorpOrg is not found"),
    /**金额超限*/
    ORD_AMT_IS_OUT_OF_RANGE("CPI30025", "ordAmt is out of range(0 ~~ ***********.99)"),
    /**充值订单不存在*/
    FUND_ORDER_IS_NOT_FOUND("CPI30026", "fund order is not found"),
    /**退款订单不存在*/
    REFUND_ORDER_IS_NOT_FOUND("CPI30027", "refund order is not found"),
    /**快捷参数未找到*/
    SHORT_PARAM_INFO_IS_NOT_FUND("CPI30028","short praram info is not fund"),
    REMIT_ORDER_IS_NOT_FUND("CPI30029","remit order is not fund"),
    CARD_VERIFY_FAILED("CPI30030","crad verify failed"),
    CARD_IS_BINDED("CPI30031","crad is binded"),
    SMS_TOKEN_IS_NULL("CPI30032","sms token is null"),
    ACC_NO_IS_NULL("CPI30033","acc no is null"),
    ID_TYP_IS_NOT_EXPECTED("CPI30034","acc no is not expected"),
    POS_RECORD_NOT_FOUND("CPI30035","pos record is not found"),

    /**微信接口相关错误码*/
    SUB_MCH_INFO_NOT_FOUND("CPI30036","sub_mch_info is not found"),

    FORBID_CLOSE_ORDER("CPI30037","forbid  order"),

    /**网银订单不存在*/
    EBANK_ORDER_IS_NOT_FOUND("CPI30038", "ebank order is not found"),

    /** 二级商户号已存在 */
    SECONDARY_MERCHANT_ALREADY_EXISTED("CPI30039","secondary merchant is already existed"),

    VERIFY_BANK_MESSAGE_ERROR("CPI30098","verify bank retrun message error"),
    BANK_COMMUNICATION_EXCEPTION("CPI30099","bank communication exception"),

    ORDER_NOT_ALLOW_ADD("CPI60001", "the order do not allow add"),
    ORDER_STATE_ERROR("CPI60002", "the order state is error"),
    ORIGINAL_ORDER_IS_NOT_FUND("CPI60003", "original order is not fund"),
    REFUND_ORDER_NOT_ALLOW_ADDITION("CPI60004", "refund order do not allow addition"),
    ACCOUT_BALANCE_IS_NOT_ENOUGH("CPI60005", "account balance is not enough"),
    ORIGINAL_SHORTCUT_ORDER_IS_NOT_FUND("CPI60006", "original shortcut order is not fund"),
    ORIGINAL_EBANK_ORDER_IS_NOT_FUND("CPI60007", "original ebank order is not fund"),
    ORIGINAL_SUB_ORDER_IS_NOT_FUND("CPI60008", "original subOrder order is not fund"),
    /**原充值订单状态已是最终态，不允许再次更改*/
    ORG_STS_IS_FINAL("CPI60009", "ordSts is final status, updated negative"),
    /**原充值订单不是处理中状态，不允许更改*/
    ORG_STS_IS_NOT_PROCESSING("CPI60010", "ordSts is not processing"),
    /**复核人和审核人不能是同一个用户*/
    REVIEWER_AND_THE_AUDITOR_CANNOT_BE_THE_SAME("CPI60011", "The reviewer and the auditor cannot be the same user"),

    BNK_70001("CPI70001","累计消费超限额"),
    BNK_70002("CPI70002","证件号码输入错误"),
    BNK_70003("CPI70003","帐户余额不足"),
    BNK_70004("CPI70004","卡号输入有误"),
    BNK_70005("CPI70005","手机号码有误"),
    BNK_70006("CPI70006","输入的户名有误"),
    BNK_70007("CPI70007","证件类型不符"),
    BNK_70008("CPI70008","输入的金额有误"),
    BNK_70009("CPI70009","证件号输入有误"),
    BNK_70010("CPI70010","签约异常，请重新签约"),
    BNK_70011("CPI70011","有效期输入有误"),
    BNK_70012("CPI70012","CVV2输入有误"),
    BNK_70013("CPI70013","绑卡数量已达最大值"),
    BNK_70014("CPI70014","已签约成功，请勿重复签约"),
    BNK_70015("CPI70015","已超过当日最大预签约次数,请明日重新签约"),
    BNK_70016("CPI70016","账户名输入有误"),
    BNK_70017("CPI70017","短信发送失败，请重新发起签约"),
    BNK_70018("CPI70018","验证码输入有误"),
    BNK_70019("CPI70019","验证码失效，请重新预签约"),
    BNK_70020("CPI70020","验证码输入有误"),
    BNK_70021("CPI70021","该卡已解约"),
    BNK_70022("CPI70022","验证码不合法"),
    BNK_70023("CPI70023","交易币种不合法"),
    BNK_70024("CPI70024","输入金额有误"),
    BNK_70025("CPI70025","银行金额超限"),
    BNK_70026("CPI70026","银行次数超限"),
    BNK_70027("CPI70027","用户状态异常，请咨询发卡行"),
    BNK_70099("CPI70099","银行系统异常，请稍后再试"),

    BNK_70101("CPI70101","当天交易只能撤销不能退款"),
    BNK_70102("CPI70102","交易处理中，请稍后查看"),
    BNK_70199("CPI70199","系统处理失败"),
    BNK_70200("CPI70200","订单处理失败"),

    //ACLEDA错误码
    BNK_70300("CPI70300","服务器异常或发生错误"),
    BNK_70301("CPI70301","请求数据被篡改"),
    BNK_70302("CPI70302","未找到该渠道路由"),
    BNK_70303("CPI70303","该商户未激活"),
    BNK_70304("CPI70304","商户密钥校验失败"),
    BNK_70305("CPI70305","未找到该商户"),
    BNK_70306("CPI70306","商户未选择支付选项"),
    BNK_70307("CPI70307","生成支付令牌ID失败"),
    BNK_70308("CPI70308","创建SESSION失败"),
    BNK_70309("CPI70309","普通异常或错误"),
    BNK_70310("CPI70310","未找到交易记录"),

    CREGIS_HTTP_ERROR("CPI80001","Cregis对接异常"),
    DM_ACCOUNT_NOT_EXIST("CPI80002","DM账户不存在"),

    CPI_SYS_EXCEPTION("CPI99999", "the system is exception")
    ;

    private String msgCd;
    private String msgInfo;
    private CpiMsgCd(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }
    public String getMsgCd() {
        return msgCd;
    }
    public String getMsgInfo() {
        return msgInfo;
    }
    
}
