package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.TransferAcledaReqDTO;
import com.hisun.lemon.cpi.dto.TransferAcledaRspDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by R<PERSON> on 2017/7/18.
 */
@FeignClient("CPI")
public interface BankClient {

    /**
     * acleda银行转账获取交易和支付ID
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/bank/transfer/acleda")
    public GenericRspDTO<TransferAcledaRspDTO> transferAcledaOpen(@Validated @RequestBody GenericDTO<TransferAcledaReqDTO> genericDTO) ;
}
