package com.hisun.lemon.cpi.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class FastpayAcledaNotifyReqDTO {

    @ApiModelProperty(name = "sessionId", value = "Acleda银行交易sessionId", required = true, dataType = "String")
    @NotNull(message="CPI10034")
    private String sessionId;

    @ApiModelProperty(name = "paymentTokenId", value = "Acleda银行支付令牌Id", required = true, dataType = "String")
    @NotNull(message="CPI10035")
    private String paymentTokenId;

    /**
     * 收银台订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", required = true, dataType = "String")
    @NotNull(message="CPI10021")
    private String reqOrdNo;


    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getPaymentTokenId() {
        return paymentTokenId;
    }

    public void setPaymentTokenId(String paymentTokenId) {
        this.paymentTokenId = paymentTokenId;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }
}
