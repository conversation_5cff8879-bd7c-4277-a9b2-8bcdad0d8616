package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;

import java.math.BigDecimal;
import java.util.List;

/**
 * 查询支付宝二级商户列表，返回报文
 */
@ClientValidated
public class SecMercListRspDTO {

    private List<SecMercListDO> list;

    private Long total;

    public static class SecMercListDO {
        /**
         * @Fields secondaryMerchantName
         */
        private String secondaryMerchantName;
        /**
         * @Fields storeName
         */
        private String storeName;
        /**
         * @Fields acquirerPartnerId
         */
        private String acquirerPartnerId;
        /**
         * @Fields secondaryMerchantId
         */
        private String secondaryMerchantId;
        /**
         * @Fields storeId
         */
        private String storeId;
        /**
         * @Fields mode
         */
        private String mode;
        /**
         * @Fields mcc
         */
        private String mcc;
        /**
         * @Fields countryArea
         */
        private String countryArea;
        /**
         * @Fields storeAddress
         */
        private String storeAddress;
        /**
         * @Fields brandName
         */
        private String brandName;
        /**
         * @Fields businessHours
         */
        private String businessHours;
        /**
         * @Fields averageSpending
         */
        private BigDecimal averageSpending;
        /**
         * @Fields ccy
         */
        private String ccy;
        /**
         * @Fields contactTel
         */
        private String contactTel;
        /**
         * @Fields service
         */
        private String service;
        /**
         * @Fields recommendations
         */
        private String recommendations;
        /**
         * @Fields storeNameCn
         */
        private String storeNameCn;
        /**
         * @Fields brandNameCn
         */
        private String brandNameCn;
        /**
         * @Fields storeDescCn
         */
        private String storeDescCn;
        /**
         * @Fields tagsCn
         */
        private String tagsCn;
        /**
         * @Fields addressCn
         */
        private String addressCn;
        /**
         * @Fields landmarkCn
         */
        private String landmarkCn;
        /**
         * @Fields commercialDistrict
         */
        private String commercialDistrict;
        /**
         * @Fields exceptCn
         */
        private String exceptCn;
        /**
         * @Fields googleMapsLatitude
         */
        private String googleMapsLatitude;
        /**
         * @Fields googleMapsLongitude
         */
        private String googleMapsLongitude;
        /**
         * @Fields storeSts
         */
        private String storeSts;

        public String getSecondaryMerchantName() {
            return secondaryMerchantName;
        }

        public void setSecondaryMerchantName(String secondaryMerchantName) {
            this.secondaryMerchantName = secondaryMerchantName;
        }

        public String getStoreName() {
            return storeName;
        }

        public void setStoreName(String storeName) {
            this.storeName = storeName;
        }

        public String getAcquirerPartnerId() {
            return acquirerPartnerId;
        }

        public void setAcquirerPartnerId(String acquirerPartnerId) {
            this.acquirerPartnerId = acquirerPartnerId;
        }

        public String getSecondaryMerchantId() {
            return secondaryMerchantId;
        }

        public void setSecondaryMerchantId(String secondaryMerchantId) {
            this.secondaryMerchantId = secondaryMerchantId;
        }

        public String getStoreId() {
            return storeId;
        }

        public void setStoreId(String storeId) {
            this.storeId = storeId;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public String getMcc() {
            return mcc;
        }

        public void setMcc(String mcc) {
            this.mcc = mcc;
        }

        public String getCountryArea() {
            return countryArea;
        }

        public void setCountryArea(String countryArea) {
            this.countryArea = countryArea;
        }

        public String getStoreAddress() {
            return storeAddress;
        }

        public void setStoreAddress(String storeAddress) {
            this.storeAddress = storeAddress;
        }

        public String getBrandName() {
            return brandName;
        }

        public void setBrandName(String brandName) {
            this.brandName = brandName;
        }

        public String getBusinessHours() {
            return businessHours;
        }

        public void setBusinessHours(String businessHours) {
            this.businessHours = businessHours;
        }

        public BigDecimal getAverageSpending() {
            return averageSpending;
        }

        public void setAverageSpending(BigDecimal averageSpending) {
            this.averageSpending = averageSpending;
        }

        public String getCcy() {
            return ccy;
        }

        public void setCcy(String ccy) {
            this.ccy = ccy;
        }

        public String getContactTel() {
            return contactTel;
        }

        public void setContactTel(String contactTel) {
            this.contactTel = contactTel;
        }

        public String getService() {
            return service;
        }

        public void setService(String service) {
            this.service = service;
        }

        public String getRecommendations() {
            return recommendations;
        }

        public void setRecommendations(String recommendations) {
            this.recommendations = recommendations;
        }

        public String getStoreNameCn() {
            return storeNameCn;
        }

        public void setStoreNameCn(String storeNameCn) {
            this.storeNameCn = storeNameCn;
        }

        public String getBrandNameCn() {
            return brandNameCn;
        }

        public void setBrandNameCn(String brandNameCn) {
            this.brandNameCn = brandNameCn;
        }

        public String getStoreDescCn() {
            return storeDescCn;
        }

        public void setStoreDescCn(String storeDescCn) {
            this.storeDescCn = storeDescCn;
        }

        public String getTagsCn() {
            return tagsCn;
        }

        public void setTagsCn(String tagsCn) {
            this.tagsCn = tagsCn;
        }

        public String getAddressCn() {
            return addressCn;
        }

        public void setAddressCn(String addressCn) {
            this.addressCn = addressCn;
        }

        public String getLandmarkCn() {
            return landmarkCn;
        }

        public void setLandmarkCn(String landmarkCn) {
            this.landmarkCn = landmarkCn;
        }

        public String getCommercialDistrict() {
            return commercialDistrict;
        }

        public void setCommercialDistrict(String commercialDistrict) {
            this.commercialDistrict = commercialDistrict;
        }

        public String getExceptCn() {
            return exceptCn;
        }

        public void setExceptCn(String exceptCn) {
            this.exceptCn = exceptCn;
        }

        public String getGoogleMapsLatitude() {
            return googleMapsLatitude;
        }

        public void setGoogleMapsLatitude(String googleMapsLatitude) {
            this.googleMapsLatitude = googleMapsLatitude;
        }

        public String getGoogleMapsLongitude() {
            return googleMapsLongitude;
        }

        public void setGoogleMapsLongitude(String googleMapsLongitude) {
            this.googleMapsLongitude = googleMapsLongitude;
        }

        public String getStoreSts() {
            return storeSts;
        }

        public void setStoreSts(String storeSts) {
            this.storeSts = storeSts;
        }
    }

    public List<SecMercListDO> getList() {
        return list;
    }

    public void setList(List<SecMercListDO> list) {
        this.list = list;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }
}
