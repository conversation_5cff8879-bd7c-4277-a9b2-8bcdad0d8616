package com.hisun.lemon.cpi.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * Created by Rui on 2017/7/24.
 */
public class CardUnBindReqDTO {

    /**
     * 内部协议号
     */
    @ApiModelProperty(name = "argNo", value = "内部协议号", required = true, dataType = "String")
    private String agrNo;

    /**
     * 是否需要检查userId
     * UI调用此接口则不需要检查userId
     */
    @ApiModelProperty(name = "chkUserId", value = "是否需要检查userId", required = false, dataType = "String")
    private String chkUserId;

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public String getChkUserId() {
        return chkUserId;
    }

    public void setChkUserId(String chkUserId) {
        this.chkUserId = chkUserId;
    }
}
