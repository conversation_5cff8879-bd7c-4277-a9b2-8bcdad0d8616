package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by Rui on 2017/7/5.
 */
@ClientValidated
public class FastpayReqDTO {

    /**
     * 协议号
     */
    @ApiModelProperty(name = "argNo", value = "协议号", required = true, dataType = "String")
    @NotNull(message="CPI10024")
    private String argNo;
    /**
     * 币种，USD美元
     */
    @ApiModelProperty(name = "ordCcy", value = "币种，USD美元", required = true, dataType = "String")
    @NotNull(message="CPI10017")
    private String ordCcy;
    /**
     * 金额，单位元
     */
    @ApiModelProperty(name = "ordAmt", value = "金额，单位元", required = true, dataType = "String")
    @NotNull(message="CPI10018")
    @DecimalMax(value = "99999999999.99", message = "CPI30025")
    @DecimalMin(value = "0", message = "CPI30025")
    private BigDecimal ordAmt;
    /**
     * 个企标识，B企业，C个人
     */
    @ApiModelProperty(name = "bnkPsnFlg", value = "个企标识，B企业，C个人", required = true, dataType = "String")
    @NotNull(message="CPI10015")
    @Pattern(regexp="B|C",message="CPI10013")
    private String bnkPsnFlg;
    /**
     * 用户类型，U：用户，M：商户
     */
    @ApiModelProperty(name = "userTyp", value = "用户类型，U：用户，M：商户", required = true, dataType = "String")
    @NotNull(message="CPI10015")
    @Pattern(regexp="U|M",message="CPI10013")
    private String userTyp;
    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", required = true, dataType = "CorpBusTyp")
    @NotNull(message="CPI10002")
    private CorpBusTyp corpBusTyp;
    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", required = true, dataType = "CorpBusSubTyp")
    @NotNull(message="CPI10003")
    private CorpBusSubTyp corpBusSubTyp;
    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", required = true, dataType = "String")
    @NotNull(message="CPI10021")
    private String reqOrdNo;
    /**
     * 请求日期
     */
    @ApiModelProperty(name = "reqOrdDt", value = "请求订单日期", required = true, dataType = "LocalDate")
    @NotNull(message="CPI10022")
    private LocalDate reqOrdDt;
    /**
     * 请求时间
     */
    @ApiModelProperty(name = "reqOrdTm", value = "请求订单时间", required = true, dataType = "LocalTime")
    @NotNull(message="CPI10023")
    private LocalTime reqOrdTm;
    /**
     * 短信下发标识
     */
    @ApiModelProperty(name = "smsFlag", value = "是否校验短信验证码 Y：是；N：否 ", required = true, dataType = "Boolean")
    @NotNull(message="CPI10025")
    @Pattern(regexp="Y|N",message="CPI10025")
    private String smsFlag;
    /**
     * 短信流水号
     */
    @ApiModelProperty(name = "smsJrnNo", value = "短信平台流水号", required = false, dataType = "String")
    private String smsJrnNo;


    public String getArgNo() {
        return argNo;
    }

    public void setArgNo(String argNo) {
        this.argNo = argNo;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getBnkPsnFlg() {
        return bnkPsnFlg;
    }

    public void setBnkPsnFlg(String bnkPsnFlg) {
        this.bnkPsnFlg = bnkPsnFlg;
    }


    public String getUserTyp() {
        return userTyp;
    }

    public void setUserTyp(String userTyp) {
        this.userTyp = userTyp;
    }

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public String getSmsFlag() {
        return smsFlag;
    }

    public void setSmsFlag(String smsFlag) {
        this.smsFlag = smsFlag;
    }

    public String getSmsJrnNo() {
        return smsJrnNo;
    }

    public void setSmsJrnNo(String smsJrnNo) {
        this.smsJrnNo = smsJrnNo;
    }
}
