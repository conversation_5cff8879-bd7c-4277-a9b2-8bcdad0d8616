package com.hisun.lemon.cpi.dto;

import java.util.List;

/**
 * WCCIP审批任务  准备付款数据的响应对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 11:34
 */
public class TransferSubmitRspDTO {

    /**
     * 金库的账户ID
     */
    private long accountId;
    /**
     * 任务业务类型，区分不同的form
     */
    private String businessType;

    private ChainCmdBasicDTO cmdBasic;
    /**
     * 命令参数
     */
    private Object cmdForm;
    /**
     * 命令类型
     */
    private String cmdType;
    /**
     * 账户分组code
     */
    private String groupCode;
    /**
     * id
     */
    private Long id;
    /**
     * 签名内容
     */
    private String signContent;
    /**
     * 任务参与者、审核人、审批人等
     */
    private List<String> signers;
    /**
     * 签名者的等级，默认是-1
     */
    private Integer signersLevel;
    /**
     * 任务状态
     */
    private String state;
    /**
     * 提交人:链上命令为fundFlowCode或者chainControlCode,系统命令为提交人
     */
    private String submitter;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 门限
     */
    private Integer threshold;
    /**
     * 总参与审批数量
     */
    private Integer total;
    /**
     * vault code
     */
    private String vaultCode;

    public long getAccountId() {
        return accountId;
    }

    public void setAccountId(long accountId) {
        this.accountId = accountId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public ChainCmdBasicDTO getCmdBasic() {
        return cmdBasic;
    }

    public void setCmdBasic(ChainCmdBasicDTO cmdBasic) {
        this.cmdBasic = cmdBasic;
    }

    public Object getCmdForm() {
        return cmdForm;
    }

    public void setCmdForm(Object cmdForm) {
        this.cmdForm = cmdForm;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSignContent() {
        return signContent;
    }

    public void setSignContent(String signContent) {
        this.signContent = signContent;
    }

    public List<String> getSigners() {
        return signers;
    }

    public void setSigners(List<String> signers) {
        this.signers = signers;
    }

    public Integer getSignersLevel() {
        return signersLevel;
    }

    public void setSignersLevel(Integer signersLevel) {
        this.signersLevel = signersLevel;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getSubmitter() {
        return submitter;
    }

    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getThreshold() {
        return threshold;
    }

    public void setThreshold(Integer threshold) {
        this.threshold = threshold;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }
}