package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class CloseOrderRspDTO {

    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现，07pos收单，08撤销
     */
    private CorpBusTyp corpBusTyp;

    /**
     * 业务子类型
     */
    private CorpBusSubTyp corpBusSubTyp;

    /**
     * 原充值订单号
     */
    private String fndOrdNo;

    /**
     * 撤销订单号
     */
    private String rfdOrdNo;

    /**
     * 撤单状态 C-已撤销
     */
    private String ordSts;

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getFndOrdNo() {
        return fndOrdNo;
    }

    public void setFndOrdNo(String fndOrdNo) {
        this.fndOrdNo = fndOrdNo;
    }

    public String getRfdOrdNo() {
        return rfdOrdNo;
    }

    public void setRfdOrdNo(String rfdOrdNo) {
        this.rfdOrdNo = rfdOrdNo;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }
}
