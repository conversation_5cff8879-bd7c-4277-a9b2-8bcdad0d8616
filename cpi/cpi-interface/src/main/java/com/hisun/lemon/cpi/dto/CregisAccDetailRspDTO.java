package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Cregis 账户详情响应类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18 14:34
 */
@ApiModel(" Cregis 账户详情响应类")
@ClientValidated
public class CregisAccDetailRspDTO {

    private Long id; // ID

    private Long accountId; // 账户 ID

    private String coinId; // 币种 ID

    private String address; // 地址

    private String network; // 区块链网络

    private Integer status; // 状态：0-可用，1-占用

    private BigDecimal balance; // 余额

    private Object onChainState; // 地址链上状态

    private Integer priority; // 使用优先级

    private LocalDateTime createTime; // 创建时间

    private LocalDateTime updateTime; // 更新时间

    // Getter 和 Setter 方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }
    public String getCoinId() { return coinId; }
    public void setCoinId(String coinId) { this.coinId = coinId; }
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    public String getNetwork() { return network; }
    public void setNetwork(String network) { this.network = network; }
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    public BigDecimal getBalance() { return balance; }
    public void setBalance(BigDecimal balance) { this.balance = balance; }
    public Object getOnChainState() { return onChainState; }
    public void setOnChainState(Object onChainState) { this.onChainState = onChainState; }
    public Integer getPriority() { return priority; }
    public void setPriority(Integer priority) { this.priority = priority; }
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
}
