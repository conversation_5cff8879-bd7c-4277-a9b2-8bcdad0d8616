package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import com.hisun.lemon.cpi.dto.CregisAccDetailReqDTO;
import com.hisun.lemon.cpi.dto.CregisAccDetailRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * CregisClient
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14 16:25
 */
@FeignClient("CPI")
public interface CregisClient {

    /**
     * 查询金库账户对应的地址余额信息
     * @param req
     * @return
     */
    @PostMapping("/cpi/address/list/balance")
    CregisAccDetailRspDTO getAccountDetail(@Validated @RequestBody CregisReqDTO req);


    @ApiOperation(value = "准备付款数据", notes = "准备付款数据")
    @ApiResponse(code = 200, message = "准备付款数据")
    @PostMapping("/cpi/address/fund-flow/prepare/payment")
    CregisRsp<TransferSubmitRspDTO> prePayment(
            @RequestBody PaymentPrepareReqDTO req
    );

    @ApiOperation(value = "付款数据签名", notes = "付款数据签名")
    @ApiResponse(code = 200, message = "付款数据签名")
    @PostMapping("/cpi/address/fund-flow/signTypedData")
    CregisRsp<SignRspDTO> signData(@RequestBody SignReqDTO signReqDTO);

    @ApiOperation(value = "提交转账", notes = "提交转账")
    @ApiResponse(code = 200, message = "提交转账")
    @PostMapping("/cpi/address/fund-flow/transfer")
    CregisRsp<NoBody> submitTransfer(@RequestBody TransferSubmitReqDTO request);

    @ApiOperation(value = "转账签名提交全流程", notes = "转账签名提交全流程")
    @ApiResponse(code = 200, message = "转账签名提交全流程")
    @PostMapping("/cpi/address/fund-flow/transfer/all")
    CregisRsp<NoBody> transferAll(@RequestBody PaymentReqDTO req);

    @ApiOperation(value = "处理Cregis回调数据", notes = "处理Cregis回调数据")
    @ApiResponse(code = 200, message = "处理Cregis回调数据成功")
    @PostMapping("/cpi/cregis/callback")
    GenericRspDTO<NoBody> cregisCallback(@RequestBody CregisCallbackBaseReqDTO req);
}
