package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericDTO;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Map;

/**
 * Created by Rui on 2017/7/5.
 */
public class EbankpayRspDTO {
    /**
     * 内部用户号
     */
    @ApiModelProperty(name = "userId", value = "内部用户号", dataType = "String")
    private String userId;
    /**
     * 金额
     */
    @ApiModelProperty(name = "ordAmt", value = "金额", dataType = "BigDecimal")
    private BigDecimal ordAmt;
    /**
     * 资金机构
     */
    @ApiModelProperty(name = "crdCorpOrg", value = "资金机构", dataType = "String")
    private String crdCorpOrg;
    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", dataType = "CorpBusTyp")
    private CorpBusTyp corpBusTyp;
    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", dataType = "CorpBusSubTyp")
    private CorpBusSubTyp corpBusSubTyp;
    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", dataType = "String")
    private String reqOrdNo;
    /**
     * 订单状态
     */
    @ApiModelProperty(name = "ordSts", value = "订单状态", dataType = "String")
    private String ordSts;
    /**
     * 订单号
     */
    @ApiModelProperty(name = "ordNo", value = "订单号", dataType = "String")
    private String ordNo;

    /**
     * 用户扫码下单所需的返回字段
     */
    private Map<String, String> resultMap;

    /**
     * 用户openId，由微信在页面授权时返回
     */
    @ApiModelProperty(name = "openId", value = "用户openId", dataType = "String")
    private String openId;

    /**
     * 企业在平台的商户编号，微信支付时必输
     */
    @ApiModelProperty(name = "merchantId", value = "商户编号", required = false, dataType = "String")
    private String merchantId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }

    public Map<String, String> getResultMap() {
        return resultMap;
    }

    public void setResultMap(Map<String, String> resultMap) {
        this.resultMap = resultMap;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
}
