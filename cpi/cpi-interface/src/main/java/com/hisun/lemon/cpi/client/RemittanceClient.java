package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON>ui on 2017/7/18.
 */
@FeignClient("CPI")
public interface RemittanceClient {

    /**
     * 汇款充值登记
     */
    @PostMapping("/cpi/remittance/order")
    GenericRspDTO<RemittanceRspDTO> remit(@RequestBody GenericDTO<RemittanceReqDTO> genericDTO);

    /**
     * 汇款订单查询
     */
    @GetMapping("/cpi/remittance/result")
    GenericRspDTO<OrderResultRspDTO> orderQuery(@RequestParam(value = "ordNo") String ordNo);

    /**
     * 汇款充值确认，或退回
     */
    @PostMapping("/cpi/remittance/payment")
    GenericRspDTO<NoBody> payment(@RequestBody GenericDTO<RemittanceConfirmDTO> genericDTO);

    /**
     * 营业厅充值登记
     */
    @PostMapping("/cpi/remittance/hallOrder")
    GenericRspDTO<RemittanceRspDTO> hallRemit(@RequestBody GenericDTO<RemittanceReqDTO> genericDTO);

}
