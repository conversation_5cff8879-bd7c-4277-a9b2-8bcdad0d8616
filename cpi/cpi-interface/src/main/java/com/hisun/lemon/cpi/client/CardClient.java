package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.AgrInfoReqDTO;
import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpi.dto.CardBinRspDTO;
import com.hisun.lemon.cpi.dto.CardBinReqDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Created by <PERSON><PERSON> on 2017/7/18.
 */
@FeignClient("CPI")
public interface CardClient {

    /**
     * 查询用户协议
     * @param corpBusTyp
     * @param corpBusSubTyp
     * @param crdAcTyp
     * @return
     */
    @GetMapping("/cpi/cards/userId")
    GenericRspDTO<AgrInfoRspDTO> queryCardsInfo(@Validated @RequestParam(value = "corpBusTyp") CorpBusTyp corpBusTyp,
                                                @Validated @RequestParam(value = "corpBusSubTyp") CorpBusSubTyp corpBusSubTyp,
                                                @RequestParam(value = "userNo", required = false) String userNo,
                                                @RequestParam(value = "crdAcTyp", required = false) String crdAcTyp);

    /**
     * 根据卡号查询卡bin信息
     * @param crdNo
     * @return
     */
    @GetMapping("/cpi/cards/head")
    GenericRspDTO<CardBinRspDTO> queryCardBin(@Validated @RequestParam(value = "crdNo") String crdNo);
}
