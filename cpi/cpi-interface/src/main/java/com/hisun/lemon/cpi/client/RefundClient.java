package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.OrderResultReqDTO;
import com.hisun.lemon.cpi.dto.OrderResultRspDTO;
import com.hisun.lemon.cpi.dto.RefundReqDTO;
import com.hisun.lemon.cpi.dto.RefundRspDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * Created by Rui on 2017/7/18.
 */
@FeignClient("CPI")
public interface RefundClient {

    /**
     * 退款登记订单
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/refund/order")
    GenericRspDTO<RefundRspDTO> createOrder(@RequestBody GenericDTO<RefundReqDTO> genericDTO);

    /**
     * 退款订单结果查询
     * @param ordNo
     * @return
     */
    @GetMapping("/cpi/refund/result")
    GenericRspDTO<OrderResultRspDTO> orderQuery(@RequestParam(value = "ordNo") String ordNo);
}
