package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by Rui on 2017/7/13.
 */
public class OrderResultReqDTO extends GenericDTO<NoBody>{

    /**
     * 订单号
     */
    @ApiModelProperty(name = "ordNo", value = "汇款订单号", required = true, dataType = "String")
    private String ordNo;

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }
}
