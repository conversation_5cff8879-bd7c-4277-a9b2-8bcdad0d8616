package com.hisun.lemon.cpi.enums;

import java.io.IOException;

/**
 * 任务业务类型，区分不同的form
 */
public enum BusinessType {
    CA_POOLING, DELEGATE_TRANSFER, DELEGATE_TRANSFER_EXEC, POOLING, SYSTEM, TOKEN_MINT, TOKEN_OPERATE, TRANSFER;

    public String toValue() {
        switch (this) {
            case CA_POOLING: return "CA_POOLING";
            case DELEGATE_TRANSFER: return "DELEGATE_TRANSFER";
            case DELEGATE_TRANSFER_EXEC: return "DELEGATE_TRANSFER_EXEC";
            case POOLING: return "POOLING";
            case SYSTEM: return "SYSTEM";
            case TOKEN_MINT: return "TOKEN_MINT";
            case TOKEN_OPERATE: return "TOKEN_OPERATE";
            case TRANSFER: return "TRANSFER";
        }
        return null;
    }

    public static BusinessType forValue(String value) throws IOException {
        if (value.equals("CA_POOLING")) return CA_POOLING;
        if (value.equals("DELEGATE_TRANSFER")) return DELEGATE_TRANSFER;
        if (value.equals("DELEGATE_TRANSFER_EXEC")) return DELEGATE_TRANSFER_EXEC;
        if (value.equals("POOLING")) return POOLING;
        if (value.equals("SYSTEM")) return SYSTEM;
        if (value.equals("TOKEN_MINT")) return TOKEN_MINT;
        if (value.equals("TOKEN_OPERATE")) return TOKEN_OPERATE;
        if (value.equals("TRANSFER")) return TRANSFER;
        throw new IOException("Cannot deserialize BusinessType");
    }
}