package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

@ClientValidated
public class RemittanceReqDTO {
    /**
     * 收款人id
     */
    @ApiModelProperty(name = "userNo", value = "用户id", required = false, dataType = "String")
    String userNo;
    /**
     * 币种，USD美元
     */
    @ApiModelProperty(name = "ordCcy", value = "币种，USD美元", required = true, dataType = "String")
    @NotEmpty(message="CPI10017")
    private String ordCcy;
    /**
     * 金额，单位元
     */
    @ApiModelProperty(name = "ordAmt", value = "金额，单位元", required = true, dataType = "String")
    @NotNull(message="CPI10018")
    private BigDecimal ordAmt;
    /**
     * 资金机构
     */
    @ApiModelProperty(name = "crdCorpOrg", value = "资金机构", required = false, dataType = "String")
    private String crdCorpOrg;
    /**
     * 银行预留手机号
     */
    @ApiModelProperty(name = "mblNo", value = "手机号", required = false, dataType = "String")
    private String mblNo;
    /**
     * 加密卡号
     */
    @ApiModelProperty(name = "crdNoEnc", value = "加密卡号", required = false, dataType = "String")
    private String crdNoEnc;
    /**
     * 户名
     */
    @ApiModelProperty(name = "crdUsrNm", value = "户名", required = false, dataType = "String")
    private String crdUsrNm;
    /**
     * 证件类型
     */
    @ApiModelProperty(name = "idTyp", value = "证件类型", required = false, dataType = "String")
    private String idTyp;
    /**
     * 加密证件号
     */
    @ApiModelProperty(name = "idNoEnc", value = "加密证件号", required = false, dataType = "String")
    private String idNoEnc;
    /**
     * 个企标识，B企业，C个人
     */
    @ApiModelProperty(name = "bnkPsnFlg", value = "个企标识，B企业，C个人", required = true, dataType = "String")
    @NotEmpty(message="CPI10015")
    @Pattern(regexp="B|C",message="CPI10013")
    private String bnkPsnFlg;
    /**
     * 用户类型，U：用户，M：商户
     */
    @ApiModelProperty(name = "userTyp", value = "用户类型，U：用户，M：商户", required = true, dataType = "String")
    @NotEmpty(message="CPI10015")
    @Pattern(regexp="U|M",message="CPI10013")
    private String userTyp;
    /**
     * 卡种
     */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种，D借记卡，C贷记卡，U未知", required = true, dataType = "String")
    @NotEmpty(message="CPI10014")
    @Pattern(regexp="D|C|U",message="CPI10007")
    private String crdAcTyp;
    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", required = true, dataType = "CorpBusTyp")
    @NotNull(message="CPI10002")
    private CorpBusTyp corpBusTyp;
    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", required = true, dataType = "CorpBusSubTyp")
    @NotNull(message="CPI10003")
    private CorpBusSubTyp corpBusSubTyp;
    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", required = true, dataType = "LocalDate")
    @NotEmpty(message="CPI10021")
    private String reqOrdNo;
    /**
     * 请求日期
     */
    @ApiModelProperty(name = "reqOrdDt", value = "请求订单日期", required = true, dataType = "LocalTime")
    @NotNull(message="CPI10022")
    private LocalDate reqOrdDt;
    /**
     * 请求时间
     */
    @ApiModelProperty(name = "reqOrdTm", value = "请求订单时间", required = true, dataType = "String")
    @NotNull(message="CPI10023")
    private LocalTime reqOrdTm;
    /**
     * 图片url
     */
    @ApiModelProperty(name = "picUrl", value = "图片url", required = false, dataType = "String")
    private String picUrl;
    /**
     * 备注
     */
    @ApiModelProperty(name = "rmk", value = "备注", required = false, dataType = "String")
    private String rmk;

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getBnkPsnFlg() {
        return bnkPsnFlg;
    }

    public void setBnkPsnFlg(String bnkPsnFlg) {
        this.bnkPsnFlg = bnkPsnFlg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNoEnc() {
        return idNoEnc;
    }

    public void setIdNoEnc(String idNoEnc) {
        this.idNoEnc = idNoEnc;
    }

    public String getUserTyp() {
        return userTyp;
    }

    public void setUserTyp(String userTyp) {
        this.userTyp = userTyp;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }
}
