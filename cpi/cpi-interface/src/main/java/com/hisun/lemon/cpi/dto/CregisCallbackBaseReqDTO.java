package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

/**
 * Cregis回调数据请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 10:12
 */
@ClientValidated
@ApiModel(value = "CregisCallbackBaseReqDTO", description = "Cregis回调数据请求DTO")
public class CregisCallbackBaseReqDTO {

    private String callbackType;
    private String subType;
    private String vaultCode;
    private String callbackData;


    public String getCallbackType() {
        return callbackType;
    }
    public void setCallbackType(String callbackType) {
        this.callbackType = callbackType;
    }
    public String getSubType() {
        return subType;
    }
    public void setSubType(String subType) {
        this.subType = subType;
    }
    public String getVaultCode() {
        return vaultCode;
    }
    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public String getCallbackData() {
        return callbackData;
    }
    public void setCallbackData(String callbackData) {
        this.callbackData = callbackData;
    }

}