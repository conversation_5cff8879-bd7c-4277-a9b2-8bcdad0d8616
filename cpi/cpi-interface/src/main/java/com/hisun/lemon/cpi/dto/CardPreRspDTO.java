package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户银行卡预签约，银行(短信平台)下发短信验证码
 * Created by Rui on 2017/7/5.
 */
public class CardPreRspDTO {

    /**
     * 内部用户号
     */
    @ApiModelProperty(name = "userId", value = "内部用户号", dataType = "String")
    private String userId;

    /**
     * 银行预留手机号
     */
    @ApiModelProperty(name = "mblNo", value = "银行预留手机号", dataType = "String")
    private String mblNo;

    /**
     * 卡资金机构
     */
    @ApiModelProperty(name = "crdCorpOrg", value = "卡资金机构", dataType = "String")
    private String crdCorpOrg;

    /**
     * 卡种，C贷记卡，D借记卡
     */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种，C贷记卡，D借记卡", dataType = "String")
    private String crdAcTyp;

    /**
     * 银行卡预留户名
     */
    @ApiModelProperty(name = "crdUsrNm", value = "银行卡预留户名", dataType = "String")
    private String crdUsrNm;

    /**
     * 预签约流水号
     */
    @ApiModelProperty(name = "jrnNo", value = "预签约流水号", dataType = "String")
    private String jrnNo;

    /**
     * 短信token
     */
    @ApiModelProperty(name = "smsToken", value = "短信token", dataType = "String")
    private String smsToken;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getJrnNo() {
        return jrnNo;
    }

    public void setJrnNo(String jrnNo) {
        this.jrnNo = jrnNo;
    }

    public String getSmsToken() {
        return smsToken;
    }

    public void setSmsToken(String smsToken) {
        this.smsToken = smsToken;
    }
}
