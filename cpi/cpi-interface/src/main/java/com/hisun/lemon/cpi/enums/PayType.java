package com.hisun.lemon.cpi.enums;

import java.io.IOException;

/**
 * 付款类型:单地址付款/聚合付款-按优先级/聚合付款-小额优先/聚合付款-大额优先/资金归集
 */
public enum PayType {
    AGGREGATE_PAYMENT_AMOUNT_LARGE_FIRST, AGGREGATE_PAYMENT_AMOUNT_SMALL_FIRST, AGGREGATE_PAYMENT_PRIORITY_LARGE_FIRST, AGGREGATE_PAYMENT_PRIORITY_SMALL_FIRST, FUND_CONSOLIDATION, SINGLE_ADDRESS_PAYMENT;

    public String toValue() {
        switch (this) {
            case AGGREGATE_PAYMENT_AMOUNT_LARGE_FIRST: return "AGGREGATE_PAYMENT_AMOUNT_LARGE_FIRST";
            case AGGREGATE_PAYMENT_AMOUNT_SMALL_FIRST: return "AGGREGATE_PAYMENT_AMOUNT_SMALL_FIRST";
            case AGGREGATE_PAYMENT_PRIORITY_LARGE_FIRST: return "AGGREGATE_PAYMENT_PRIORITY_LARGE_FIRST";
            case AGGREGATE_PAYMENT_PRIORITY_SMALL_FIRST: return "AGGREGATE_PAYMENT_PRIORITY_SMALL_FIRST";
            case FUND_CONSOLIDATION: return "FUND_CONSOLIDATION";
            case SINGLE_ADDRESS_PAYMENT: return "SINGLE_ADDRESS_PAYMENT";
        }
        return null;
    }

    public static PayType forValue(String value) throws IOException {
        if (value.equals("AGGREGATE_PAYMENT_AMOUNT_LARGE_FIRST")) return AGGREGATE_PAYMENT_AMOUNT_LARGE_FIRST;
        if (value.equals("AGGREGATE_PAYMENT_AMOUNT_SMALL_FIRST")) return AGGREGATE_PAYMENT_AMOUNT_SMALL_FIRST;
        if (value.equals("AGGREGATE_PAYMENT_PRIORITY_LARGE_FIRST")) return AGGREGATE_PAYMENT_PRIORITY_LARGE_FIRST;
        if (value.equals("AGGREGATE_PAYMENT_PRIORITY_SMALL_FIRST")) return AGGREGATE_PAYMENT_PRIORITY_SMALL_FIRST;
        if (value.equals("FUND_CONSOLIDATION")) return FUND_CONSOLIDATION;
        if (value.equals("SINGLE_ADDRESS_PAYMENT")) return SINGLE_ADDRESS_PAYMENT;
        throw new IOException("Cannot deserialize PayType");
    }
}
