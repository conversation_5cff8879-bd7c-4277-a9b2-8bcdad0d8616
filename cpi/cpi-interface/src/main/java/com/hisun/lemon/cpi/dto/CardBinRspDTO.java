package com.hisun.lemon.cpi.dto;

import io.swagger.annotations.ApiModelProperty;

public class CardBinRspDTO {
    /**
     * 主键id
     */
    @ApiModelProperty(name = "binId", value = "主键id", dataType = "String")
    private String binId;

    /**
     * 卡宾
     */
    @ApiModelProperty(name = "crdBin", value = "卡宾", dataType = "String")
    private String crdBin;

    /**
     * 资金机构
     */
    @ApiModelProperty(name = "capCorg", value = "资金机构", dataType = "String")
    private String capCorg;

    /**
     * 卡种
     */
    @ApiModelProperty(name = "crdAcTyp", value = "卡种", dataType = "String")
    private String crdAcTyp;

    /**
     * 卡长度
     */
    @ApiModelProperty(name = "crdLth", value = "卡长度", dataType = "String")
    private Integer crdLth;

    /**
     * 操作员id
     */
    @ApiModelProperty(name = "oprId", value = "操作员id", dataType = "String")
    private String oprId;

    public String getBinId() {
        return binId;
    }

    public void setBinId(String binId) {
        this.binId = binId == null ? null : binId.trim();
    }

    public String getCrdBin() {
        return crdBin;
    }

    public void setCrdBin(String crdBin) {
        this.crdBin = crdBin == null ? null : crdBin.trim();
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg == null ? null : capCorg.trim();
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp == null ? null : crdAcTyp.trim();
    }

    public Integer getCrdLth() {
        return crdLth;
    }

    public void setCrdLth(Integer crdLth) {
        this.crdLth = crdLth;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId == null ? null : oprId.trim();
    }
}