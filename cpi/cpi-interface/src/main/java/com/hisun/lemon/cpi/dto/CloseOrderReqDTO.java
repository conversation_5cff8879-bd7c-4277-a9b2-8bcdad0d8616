package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * Created by xzq on 2017/11/24
 */
@ClientValidated
public class CloseOrderReqDTO {

    /**
     * 商户号
     */
    @ApiModelProperty(name = "userNo", value = "发起撤销的商户", required = true, dataType = "String")
    @NotNull(message="CPI10001")
    private String userNo;

    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现，07pos收单，08撤销
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", required = true, dataType = "CorpBusTyp")
    @NotNull(message="CPI10002")
    private CorpBusTyp corpBusTyp;

    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", required = true, dataType = "CorpBusSubTyp")
    @NotNull(message="CPI10003")
    private CorpBusSubTyp corpBusSubTyp;

    /**
     * 原充值订单号
     */
    @ApiModelProperty(name = "fndOrdNo", value = "原充值订单号", required = true, dataType = "String")
    @NotNull(message="CPI10026")
    private String fndOrdNo;

    public String getFndOrdNo() {
        return fndOrdNo;
    }

    public void setFndOrdNo(String fndOrdNo) {
        this.fndOrdNo = fndOrdNo;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }
}
