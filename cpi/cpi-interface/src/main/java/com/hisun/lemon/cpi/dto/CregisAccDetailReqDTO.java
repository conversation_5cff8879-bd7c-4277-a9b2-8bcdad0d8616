package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * Cregis 账户详情查询请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18 14:36
 */
@ClientValidated
@ApiModel("Cregis 账户详情查询请求类")
public class CregisAccDetailReqDTO {

    /**
     * 查询条件列表
     */
    private List<QueryCondition> queryList;

    public List<QueryCondition> getQueryList() {
        return queryList;
    }

    public void setQueryList(List<QueryCondition> queryList) {
        this.queryList = queryList;
    }

    /**
     * 查询条件
     */
    public static class QueryCondition {
        /**
         * 查询键
         */
        private String key;

        /**
         * 查询值
         */
        private Object value;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public Object getValue() {
            return value;
        }

        public void setValue(Object value) {
            this.value = value;
        }
    }
}
