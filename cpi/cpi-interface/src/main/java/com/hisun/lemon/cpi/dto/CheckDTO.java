package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class CheckDTO {

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", required = true, dataType = "CorpBusTyp")
    @NotNull(message="CPI10002")
    private CorpBusTyp corpBusTyp;
    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", required = true, dataType = "CorpBusSubTyp")
    @NotNull(message="CPI10003")
    private CorpBusSubTyp corpBusSubTyp;
    /**
     * 对账key值
     */
    @ApiModelProperty(name = "chkKey", value = "对账key值", dataType = "String")
    @NotNull(message="CPI10027")
    private String chkKey;
    /**
     * 对账金额
     */
    @ApiModelProperty(name = "chkAmt", value = "对账金额", dataType = "String")
    private BigDecimal chkAmt;

    /**
     * 主方编码，即路径合作机构
     */
    @ApiModelProperty(name = "mainNo", value = "路径合作机构", dataType = "String")
    @NotNull(message="CPI10027")
    private String mainNo;

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getChkKey() {
        return chkKey;
    }

    public void setChkKey(String chkKey) {
        this.chkKey = chkKey;
    }

    public BigDecimal getChkAmt() {
        return chkAmt;
    }

    public void setChkAmt(BigDecimal chkAmt) {
        this.chkAmt = chkAmt;
    }

    public String getMainNo() {
        return mainNo;
    }

    public void setMainNo(String mainNo) {
        this.mainNo = mainNo;
    }

}
