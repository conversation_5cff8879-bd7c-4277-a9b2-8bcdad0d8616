package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by Rui on 2017/7/5.
 */
public class CardBindRspDTO {

    /**
     * 内部用户号
     */
    @ApiModelProperty(name = "userId", value = "内部用户号", dataType = "String")
    private String userId;

    /**
     * 签约内部协议号
     */
    @ApiModelProperty(name = "argNo", value = "签约内部协议号", dataType = "String")
    private String argNo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getArgNo() {
        return argNo;
    }

    public void setArgNo(String argNo) {
        this.argNo = argNo;
    }
}
