package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.CheckDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient("CPI")
public interface CheckClient {

    /**
     * 补单
     * @param genericDTO
     */
    @PostMapping("/cpi/chk/addition")
    GenericRspDTO<NoBody> additionalOrder(@Validated @RequestBody GenericDTO<CheckDTO> genericDTO);

    /**
     * 撤单
     * @param genericDTO
     */
    @PostMapping("/cpi/chk/canceled")
    GenericRspDTO<NoBody> cancelOrder(@Validated @RequestBody GenericDTO<CheckDTO> genericDTO);

    /**
     * 差错取消
     * @param genericDTO
     */
    @PostMapping("/cpi/chk/error")
    GenericRspDTO<NoBody> cancelError(@Validated @RequestBody GenericDTO<CheckDTO> genericDTO);

    /**
     * 对平处理
     * @param genericDTO
     */
    @PostMapping("/cpi/chk/match")
    GenericRspDTO<NoBody> matchHandle(@Validated @RequestBody GenericDTO<CheckDTO> genericDTO);
}
