package com.hisun.lemon.cpi.client;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Description
 * @date 2017/10/17 16:58
 */
@FeignClient("CPI")
public interface PosClient {

    /**
     * POS余额查询
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/pos/order")
    GenericRspDTO<PosBalanceRspDTO> balanceQuery(@RequestBody GenericDTO<PosBalanceReqDTO> genericDTO);

    /**
     * POS消费
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/pos/payment")
    GenericRspDTO<PosPayRspDTO> payment(@RequestBody GenericDTO<PosPayReqDTO> genericDTO);

    /**
     * POS预授权
     * @param genericDTO
     * @return
     */
    @PostMapping("/cpi/pos/authorization")
    GenericRspDTO<PosPreAuRspDTO> preAuthorization(@RequestBody GenericDTO<PosPreAuReqDTO> genericDTO);
}
