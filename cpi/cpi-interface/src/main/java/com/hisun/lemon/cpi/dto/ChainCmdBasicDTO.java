package com.hisun.lemon.cpi.dto;


public  class ChainCmdBasicDTO{
        private String vault_code;

        private String group_code;

        private String account_id;

        private String biz_code;

        private String command;

        private String applet_id;

        private String env;
        private String lang;

        private String valid_until_time;

        private String task_id;

        public String getVault_code() {
                return vault_code;
        }

        public void setVault_code(String vault_code) {
                this.vault_code = vault_code;
        }

        public String getGroup_code() {
                return group_code;
        }

        public void setGroup_code(String group_code) {
                this.group_code = group_code;
        }

        public String getAccount_id() {
                return account_id;
        }

        public void setAccount_id(String account_id) {
                this.account_id = account_id;
        }

        public String getBiz_code() {
                return biz_code;
        }

        public void setBiz_code(String biz_code) {
                this.biz_code = biz_code;
        }

        public String getCommand() {
                return command;
        }

        public void setCommand(String command) {
                this.command = command;
        }

        public String getApplet_id() {
                return applet_id;
        }

        public void setApplet_id(String applet_id) {
                this.applet_id = applet_id;
        }

        public String getEnv() {
                return env;
        }

        public void setEnv(String env) {
                this.env = env;
        }

        public String getLang() {
                return lang;
        }

        public void setLang(String lang) {
                this.lang = lang;
        }

        public String getValid_until_time() {
                return valid_until_time;
        }

        public void setValid_until_time(String valid_until_time) {
                this.valid_until_time = valid_until_time;
        }

        public String getTask_id() {
                return task_id;
        }

        public void setTask_id(String task_id) {
                this.task_id = task_id;
        }
}