package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

@ClientValidated
public class RemittanceQueryReqDTO extends GenericDTO<NoBody> {
    /**
     * 订单号
     */
    @ApiModelProperty(name = "ordNo", value = "订单号", required = true, dataType = "String")
    @NotEmpty(message="CPI10021")
    private String ordNo;

    public String getOrdNo() {
        return ordNo;
    }

    public void setOrdNo(String ordNo) {
        this.ordNo = ordNo;
    }
}
