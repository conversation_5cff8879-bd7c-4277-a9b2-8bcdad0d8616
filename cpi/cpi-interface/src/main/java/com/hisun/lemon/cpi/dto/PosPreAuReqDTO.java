package com.hisun.lemon.cpi.dto;

import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Description
 * @date 2017/10/17 9:32
 */
@ClientValidated
public class PosPreAuReqDTO {

    /**
     * 业务类型，01签约，02快捷支付，03网银支付，04汇款支付，05退款，06提现
     */
    @ApiModelProperty(name = "corpBusTyp", value = "业务类型", dataType = "CorpBusTyp")
    private CorpBusTyp corpBusTyp;
    /**
     * 业务子类型
     */
    @ApiModelProperty(name = "corpBusSubTyp", value = "业务子类型", dataType = "CorpBusSubTyp")
    private CorpBusSubTyp corpBusSubTyp;
    /**
     * 币种，USD美元
     */
    @ApiModelProperty(name = "ordCcy", value = "币种，USD美元", required = true, dataType = "String")
    @NotEmpty(message="CPI10017")
    private String ordCcy;

    /**
     * 金额，单位元
     */
    @ApiModelProperty(name = "ordAmt", value = "金额，单位元", required = true, dataType = "String")
    @NotNull(message="CPI10018")
    private BigDecimal ordAmt;

    /**
     * 加密卡号
     */
    @ApiModelProperty(name = "crdNo", value = "加密卡号", required = true, dataType = "String")
    @NotEmpty(message="CPI10016")
    private String crdNoEnc;

    /**
     * trmNo 终端号
     */
    @NotEmpty(message="CPI10033")
    @ApiModelProperty(name = "trmNo", value = "终端号", required = true, dataType = "String")
    private String trmNo;

    /**
     * 请求订单号
     */
    @ApiModelProperty(name = "reqOrdNo", value = "请求订单号", required = true, dataType = "String")
    @NotEmpty(message="CPI10021")
    private String reqOrdNo;

    /**
     * 请求日期
     */
    @ApiModelProperty(name = "reqOrdDt", value = "请求订单日期", required = true, dataType = "LocalDate")
    @NotNull(message="CPI10022")
    private LocalDate reqOrdDt;

    /**
     * 请求时间
     */
    @ApiModelProperty(name = "reqOrdTm", value = "请求订单时间", required = true, dataType = "LocalTime")
    @NotNull(message="CPI10023")
    private LocalTime reqOrdTm;

    public CorpBusTyp getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(CorpBusTyp corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public CorpBusSubTyp getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(CorpBusSubTyp corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getTrmNo() {
        return trmNo;
    }

    public void setTrmNo(String trmNo) {
        this.trmNo = trmNo;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }
}
