package com.hisun.lemon.cpi;
import com.hisun.lemon.cpi.alipay.ebankpay.*;
import com.hisun.lemon.cpi.alipay.ebankpay.req.*;
import com.hisun.lemon.cpi.alipay.ebankpay.rsp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLEncoder;

/**
 * Created by gonglei on 2017/7/6.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
//@Ignore
public class AlipayServiceTest {

    @Resource
    private AliApi2 api;
    @Resource
    private AlipayProperties alipayProperties;

    /**
     * 统一预下单，用户扫码
     */
    @Test
    public void preCreateUserTest(){
        String charset = null;
        String outTradeNo = "tradeprecreate" + System.currentTimeMillis()
                + (long) (Math.random() * 10000000L);

        // (必填) 订单标题，粗略描述用户的支付目的。如“xxx品牌xxx门店当面付扫码消费”
        String subject = "TianHe";

        // (必填) 订单总金额，单位为元，不能超过1亿元
        // 如果同时传入了【打折金额】,【不可打折金额】,【订单总金额】三者,则必须满足如下条件:【订单总金额】=【打折金额】+【不可打折金额】
        String totalAmount = "5.25";

        // (可选) 订单不可打折金额，可以配合商家平台配置折扣活动，如果酒水不参与打折，则将对应金额填写至此字段
        // 如果该值未传入,但传入了【订单总金额】,【打折金额】,则该值默认为【订单总金额】-【打折金额】
        String undiscountableAmount = "0";

        // 卖家支付宝账号ID，用于支持一个签约账号下支持打款到不同的收款账号，(打款到sellerId对应的支付宝账号)
        // 如果该字段为空，则默认为与支付宝签约的商户的PID，也就是appid对应的PID
        String sellerId = "2088621889514248";

        // 订单描述，可以对交易或商品进行一个详细地描述，比如填写"购买商品2件共15.00元"
        String body = "buy_two_total_20.00";

        // 商户操作员编号，添加此参数可以为商户操作员做销售统计
        String operatorId = "test_operator_id";

        // (必填) 商户门店编号，通过门店号和商家后台可以配置精准到门店的折扣信息，详询支付宝技术支持
        String storeId = "test_store_id";


        // 业务扩展参数，目前可添加由支付宝分配的系统商编号(通过setSysServiceProviderId方法)，详情请咨询支付宝技术支持
        ExtendParams extendParams = new ExtendParams();
        extendParams.setSecondary_merchant_id("2088621889514248");
        extendParams.setSecondary_merchant_industry("7011");
        extendParams.setSecondary_merchant_name("<EMAIL>");
        extendParams.setStore_id("BJ_ZZ_001");
        extendParams.setStore_name("Muku_in_the_Dreieichstrabe");
        extendParams.setSys_service_provider_id("R00998889911");

        // 支付超时，定义为120分钟
        String timeoutExpress = "120m";

        try{
            AliPlaceOrderReq aliPlaceOrderReq = new AliPlaceOrderReq();
            aliPlaceOrderReq.setService("alipay.acquire.precreate");
            aliPlaceOrderReq.setBody(body);
            aliPlaceOrderReq.setPartner(AlipayConstants.PARTNER);
            aliPlaceOrderReq.set_input_charset(AlipayConstants.CHARSET_UTF8);
            aliPlaceOrderReq.setSign_type(AlipayConstants.SIGN_TYPE_MD5);
            aliPlaceOrderReq.setSubject(subject);
            //aliPlaceOrderReq.setGoodsDetail(JSONObject.toJSONString(goodsDetailList));
            aliPlaceOrderReq.setTotal_fee(totalAmount);
            aliPlaceOrderReq.setTrans_currency("USD");
            aliPlaceOrderReq.setCurrency("USD");
            System.out.println(extendParams.toString());
            System.out.println(URLEncoder.encode(extendParams.toString()));
            aliPlaceOrderReq.setExtend_params(extendParams.toString());
            aliPlaceOrderReq.setOut_trade_no(outTradeNo);
            aliPlaceOrderReq.setPrice(totalAmount);
            aliPlaceOrderReq.setQuantity("1");
            aliPlaceOrderReq.setSeller_id(sellerId);
            aliPlaceOrderReq.setProduct_code("OVERSEAS_MBARCODE_PAY");
            aliPlaceOrderReq.setIt_b_pay("1d");
            aliPlaceOrderReq.setTimestamp("1456507704121");
            aliPlaceOrderReq.setNotify_url("http://bestpay.bestmpay.com/cpi/alipay/placeOrderNotify");
            AliPlaceOrderRsp response = api.doSend(aliPlaceOrderReq, AliPayEnumCommon.EnumSource.tradePreCreate);
            System.out.println("二维码地址>>"+response.getPreCreateRsp().getQrCode());
            System.out.println("预下单响应对象输出>>"+response);

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     * 统一下单，商户扫码
     */
    @Test
    public void preCreateMerchantTest(){

        int quantiy = 1;
        String trans_name = "iphone6s";
        String partner_trans_id = "Mtradeprecreate" + System.currentTimeMillis()
                + (long) (Math.random() * 10000000L);
        String currency = AliPayEnumCommon.EnumCurrency.USD.name();
        String trans_amount = "5.8";
        //用户收款二维码
        String buyer_identity_code = "285810578252881075";

        String identity_code_type = "barcode";

        String trans_create_time = "20180120153059";

        String memo = "iphone";

        String biz_product = "OVERSEAS_MBARCODE_PAY";

        ExtendParams extendParams = new ExtendParams();
        extendParams.setSecondary_merchant_id("2088621889514248");
        extendParams.setSecondary_merchant_industry("7011");
        extendParams.setSecondary_merchant_name("<EMAIL>");
        extendParams.setStore_id("BJ_ZZ_001");
        extendParams.setStore_name("Muku_in_the_Dreieichstrabe");
        extendParams.setSys_service_provider_id("R00998889911");

        AliMercPlaceOrderReq mercPlaceOrderReq = new AliMercPlaceOrderReq();
        mercPlaceOrderReq.setService(AliPayEnumCommon.EnumAlipayService.TRADE_MERC_PRECREATE.getCode());
        mercPlaceOrderReq.set_input_charset(AlipayConstants.CHARSET_UTF8);
        mercPlaceOrderReq.setSign_type(AlipayConstants.SIGN_TYPE_MD5);
        mercPlaceOrderReq.setPartner(AlipayConstants.PARTNER);
        mercPlaceOrderReq.setAlipay_seller_id(AlipayConstants.PARTNER);
        mercPlaceOrderReq.setQuantity(quantiy);
        mercPlaceOrderReq.setTrans_name(trans_name);
        mercPlaceOrderReq.setPartner_trans_id(partner_trans_id);
        mercPlaceOrderReq.setCurrency(currency);
        mercPlaceOrderReq.setTrans_amount(trans_amount);
        mercPlaceOrderReq.setBuyer_identity_code(buyer_identity_code);
        mercPlaceOrderReq.setIdentity_code_type(identity_code_type);
        mercPlaceOrderReq.setTrans_create_time(trans_create_time);
        mercPlaceOrderReq.setMemo(memo);
        mercPlaceOrderReq.setBiz_product(biz_product);
        mercPlaceOrderReq.setExtend_info(extendParams.toString());

        AliMercPlaceOrderRsp aliPlaceOrderRsp = api.doSend(mercPlaceOrderReq,AliPayEnumCommon.EnumSource.mercPlaceOrder);
        System.out.println("商户扫码结果：>>" + aliPlaceOrderRsp);

    }


    @Test
    public void orderQueryTest(){
        //tradeprecreate15148738940319095312
        AliOrderQueryReq aliOrderQueryReq = new AliOrderQueryReq();
        aliOrderQueryReq.set_input_charset(AlipayConstants.CHARSET_UTF8);
        aliOrderQueryReq.setPartner(AlipayConstants.PARTNER);
        //aliOrderQueryReq.setAlipay_trans_id("tradeprecreate15148753151063384718");
        //用户支付完成后，支付宝平台生成的订单号
//        aliOrderQueryReq.setAlipay_trans_id("2018010221001004920200389465");
        aliOrderQueryReq.setPartner_trans_id("tradeprecreate15149501985545795525");
//        aliOrderQueryReq.setPartner_trans_id(AlipayConstants.PARTNER);
        aliOrderQueryReq.setService(AliPayEnumCommon.EnumAlipayService.TRADE_QUERY.getCode());
        aliOrderQueryReq.setSign_type(AlipayConstants.SIGN_TYPE_MD5);

        AliOrderQueryRsp aliRsp = api.doSend(aliOrderQueryReq, AliPayEnumCommon.EnumSource.orderQuery);
        System.out.println("订单查询结果：>>" + aliRsp.toString());

    }

    @Test
    public void refundTest(){
        AliRefundOrderReq refundOrderReq = new AliRefundOrderReq();
//        refundOrderReq.set_input_charset(AlipayConstants.CHARSET_UTF8);
//        refundOrderReq.setCurrency(AliPayEnumCommon.EnumCurrency.USD.name());
//        refundOrderReq.setPartner(AlipayConstants.PARTNER);
//        //退款用的是平台生成的商户订单号
//        refundOrderReq.setPartner_refund_id("20180103195659204501");
//        refundOrderReq.setPartner_trans_id("20180103195659204501");
//        refundOrderReq.setRefund_amount(new BigDecimal("5.25"));
//        refundOrderReq.setService(AliPayEnumCommon.EnumAlipayService.TRADE_REFUND.getCode());
//        refundOrderReq.setSign_type(AlipayConstants.SIGN_TYPE_MD5);

        refundOrderReq.setCurrency(AliPayEnumCommon.EnumCurrency.USD.name());
        refundOrderReq.setPartner_refund_id("20180103195659204501");
        refundOrderReq.setPartner("2088621889514248");
        refundOrderReq.setPartner_trans_id("20180103195659204501");
        refundOrderReq.setRefund_amount(new BigDecimal("5.04"));
        refundOrderReq.setService("alipay.acquire.overseas.spot.refund");
//        aliRefundOrderReq.setNotify_url("");
        refundOrderReq.setSign_type("MD5");
        refundOrderReq.set_input_charset(AlipayConstants.CHARSET_UTF8);

        AliRefundRsp aliRsp = api.doSend(refundOrderReq,AliPayEnumCommon.EnumSource.refund);
        System.out.println("退款结果码:>>" + aliRsp.getRefundDetailRsp().getResultCode());
        System.out.println("订单退款结果：>>" + aliRsp.toString());
    }

    @Test
    public void cancelOrderTest(){

        AliCancelOrderReq cancelOrderReq = new AliCancelOrderReq();
        cancelOrderReq.set_input_charset(AlipayConstants.CHARSET_UTF8);
        //传入平台生成的商户订单号，用户未支付状态从"等待付款" 变成"交易关闭"
        cancelOrderReq.setOut_trade_no("tradeprecreate15148825005544404249");
        cancelOrderReq.setService(AliPayEnumCommon.EnumAlipayService.TRADE_CANCEL.getCode());
        cancelOrderReq.setPartner(AlipayConstants.PARTNER);
        cancelOrderReq.setSign_type(AlipayConstants.SIGN_TYPE_MD5);
        //cancelOrderReq.setTrade_no("");
        cancelOrderReq.setTimestamp("1456507704121");

        AliCloseOrderRsp aliRsp = api.doSend(cancelOrderReq,AliPayEnumCommon.EnumSource.closeOrder);
        System.out.println("订单订单号：>>" + aliRsp.getCancelDetailRsp().getTradeNo());
        System.out.println("订单响应信息：>>" + aliRsp.getCancelDetailRsp().getDisplayMessage());
        System.out.println("订单撤销结果：>>" + aliRsp.toString());

    }

    //alipay_trans_status: TRADE_CLOSED  WAIT_BUYER_PAY  TRADE_SUCCESS
    //result_code SUCCESS
    //is_success  T / F



}
