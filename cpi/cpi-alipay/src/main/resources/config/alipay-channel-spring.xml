<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:channel="http://www.hisun.com/schema/channel"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.hisun.com/schema/channel http://www.hisun.com/schema/channel/channel.xsd">

    <channel:service name="ALIPAY" charset="UTF-8" >
        
        <!-- 统一收单线下交易预创建，用户扫码，聚合支付 -->
        <channel:connector id="tradePreCreate" protocol="http" url="https://intlmapi.alipay.com/gateway.do" default="false" dateTypeName="QUERY_STRING" requestMode="GET" charset="UTF-8" />

        <!-- 统一收单线下交易预创建，商户扫码，聚合支付 -->
        <channel:connector id="mercPlaceOrder" protocol="http" url="https://intlmapi.alipay.com/gateway.do" default="false" dateTypeName="QUERY_STRING" requestMode="GET" charset="UTF-8" />

        <!-- 订单查询 -->
        <channel:connector id="orderQuery" protocol="http" url="https://intlmapi.alipay.com/gateway.do" default="false" dateTypeName="QUERY_STRING" requestMode="GET" charset="UTF-8" />

        <!-- 退款订单 -->
        <channel:connector id="refund" protocol="http" url="https://intlmapi.alipay.com/gateway.do" default="false" dateTypeName="QUERY_STRING" requestMode="GET" charset="UTF-8" />

        <!-- 撤销订单 -->
        <channel:connector id="closeOrder" protocol="http" url="https://intlmapi.alipay.com/gateway.do" default="false" dateTypeName="QUERY_STRING" requestMode="GET" charset="UTF-8" />

        <channel:container>

            <!-- 统一收单线下交易预创建，用户扫码，聚合支付 -->
            <channel:processor name="tradePreCreate" marshal-class="com.hisun.lemon.cpi.alipay.ebankpay.req.AliPlaceOrderReq"
                               unmarshal-class="com.hisun.lemon.cpi.alipay.ebankpay.rsp.AliPlaceOrderRsp" connector-id="tradePreCreate" />

            <!-- 统一收单线下交易预创建，商户扫码，聚合支付 -->
            <channel:processor name="mercPlaceOrder" marshal-class="com.hisun.lemon.cpi.alipay.ebankpay.req.AliMercPlaceOrderReq"
                               unmarshal-class="com.hisun.lemon.cpi.alipay.ebankpay.rsp.AliMercPlaceOrderRsp" connector-id="mercPlaceOrder" />


            <!-- 订单查询-->
            <channel:processor name="orderQuery" marshal-class="com.hisun.lemon.cpi.alipay.ebankpay.req.AliOrderQueryReq"
                               unmarshal-class="com.hisun.lemon.cpi.alipay.ebankpay.rsp.AliOrderQueryRsp" connector-id="orderQuery" />

            <!-- 订单退款-->
            <channel:processor name="refund" marshal-class="com.hisun.lemon.cpi.alipay.ebankpay.req.AliRefundOrderReq"
                               unmarshal-class="com.hisun.lemon.cpi.alipay.ebankpay.rsp.AliRefundRsp" connector-id="refund" />

            <!-- 撤销-->
            <channel:processor name="closeOrder" marshal-class="com.hisun.lemon.cpi.alipay.ebankpay.req.AliCancelOrderReq"
                               unmarshal-class="com.hisun.lemon.cpi.alipay.ebankpay.rsp.AliCloseOrderRsp" connector-id="closeOrder" />

            <!-- 此处的handler-filter对全部processor生效 -->
            <channel:handler-filter class="com.hisun.channel.service.child.filter.MonitorHandlerFilter" />
            <!-- <channel:handler-filter class="com.hisun.channel.service.child.filter.MessageContext" /> -->

        </channel:container>
    </channel:service>
</beans>