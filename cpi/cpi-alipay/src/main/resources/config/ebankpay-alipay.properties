
# ↓↓↓↓↓↓↓↓↓↓请在这里配置您的基本信息↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
# 合作身份者ID，以2088开头由16位纯数字组成的字符串
#	aliPay.PARTNER = "2088621887303624";
#	// 商户的私钥
#	aliPay.KEY = "vyptxdsf0xt2nbl5pruiu89p30vkaacc";

#aliPay.partner = 2088621889514248
aliPay.partner = 2088821976790652
# 商户的私钥
#aliPay.merKey = t5zdi9f8ld33ndw6alblg24ncph7v053
aliPay.merKey = q7o7txn4asotwti2rm0kjbw1i3lsvihp
# ↑↑↑↑↑↑↑↑↑↑请在这里配置您的基本信息↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑


# 字符编码格式 目前支持 gbk 或 utf-8
aliPay.inputCharset = UTF-8

# 签名方式 不需修改
aliPay.signType = MD5

#aliPay.apiUrl = https://openapi.alipaydev.com/gateway.do
aliPay.apiUrl = https://intlmapi.alipay.com/gateway.do
aliPay.fail = FAIL
aliPay.success = SUCCESS
aliPay.hmacsha256 = HMAC-SHA256

aliPay.fieldSign = sign
aliPay.fieldSignType = sign_type

### uat环境下单回调地址 ########
aliPay.notifyUrl = http://bestpay.bestmpay.com/cpi/alipay/placeOrderNotify

### 生产环境下单回调地址 ########
#aliPay.notifyUrl = https://business.bestmpay.com/cpi/alipay/placeOrderNotify

#####alipay service name############
aliPay.userPreCreateService=alipay.acquire.precreate
aliPay.mercPreCreateService=alipay.acquire.overseas.spot.pay
aliPay.tradeQueryService=alipay.acquire.overseas.query
aliPay.tradeCancelService=alipay.acquire.cancel
aliPay.tradeRefundService=alipay.acquire.overseas.spot.refund

# alipay sftp configuration
aliPay.sftpIp=*************
aliPay.sftpPort=22
aliPay.sftpTimeout=2000
aliPay.sftpFilePath=/download/
aliPay.userName=openviseSeatel
aliPay.passwd=C3C10T

### alipay settlement file download configuration
### uat
#aliPay.settleFilePath=/home/<USER>/data/cpi/settle/

### prd
aliPay.settleFilePath=/data/cpi/data/settle/