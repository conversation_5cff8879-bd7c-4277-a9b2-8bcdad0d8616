package com.hisun.lemon.cpi.alipay.ebankpay.req;
import java.net.URLEncoder;

import javax.validation.constraints.NotNull;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.lemon.cpi.alipay.ebankpay.AlipayConstants;
import com.hisun.lemon.cpi.alipay.ebankpay.util.StringUtils;

/**
 * 用户扫码，聚合支付，统一下单
 * api详情： https://global.alipay.com/service/external_QR_Code/49
 * <AUTHOR>
 *
 */
//@XmlRootElement(name = "request")
@Plain(form = Plain.Form.QUERY_STRING)
public class AliPlaceOrderReq {
	@Item
	@SignValue
	private String service ;

	@Item
	@SignValue
	private String partner;

	@Item
	@SignValue
	private String _input_charset;

	@Item
	@SignValue
	private String sign_type;
	//private String sign_type = AlipayConstants.SIGN_TYPE_RSA;

	/**
	 * 对一笔交易的具体描述信息。如果是多种商品，请将商品描述字符串累加传给body
	 */
	@Item
	@SignValue
	private String body;


	/**
	 * 订单金额币种。目前只支持传入156（人民币）。
	 如果为空，则默认设置为156
	 */
	@Item
	@SignValue
	private String currency;

	/**
	 * 公用业务扩展信息。用于商户的特定业务信息的传递，只有商户与支付宝约定了传递此参数且约定了参数含义，此参数才有效。
	 比如可传递二维码支付场景下的门店ID等信息，以json格式传输。
	 */
	@Item
	@SignValue
	private String extend_params;

	/**
	 * 描述商品明细信息，json格式。
	 */
	@Item
	@SignValue
	private String goodsDetail;

	@Item
	@SignValue
	private String product_code;


	/**
	 * 支付宝合作商户网站唯一订单号
	 */
	@Item
	@SignValue
	private String out_trade_no;

	/**
	 * 订单中商品的单价。
	 如果请求时传入本参数，则必须满足total_fee=price×quantity的条件
	 */
	@Item
	@SignValue
	private String price;

	/**
	 * 订单中商品的数量。
	 如果请求时传入本参数，则必须满足total_fee=price×quantity的条件
	 */
	@Item
	@SignValue
	private String quantity;

	/**
	 * 分账信息。
	 描述分账明细信息，json格式
	 */
	@Item
	@SignValue
	private String notify_url;

	/**
	 * 分账类型。卖家的分账类型，目前只支持传入ROYALTY（普通分账类型）
	 */
	@Item
	@SignValue
	private String timestamp;


	/**
	 * 卖家支付宝账号对应的支付宝唯一用户号，以2088开头的纯16位数字。如果和seller_email同时为空，则本参数默认填充partner的值
	 */
	@Item
	@SignValue
	private String seller_id;

	/**
	 * 收银台页面上，商品展示的超链接
	 */
	@Item
	@SignValue
	private String show_url;

	/**
	 * 商品购买
	 */
	@Item
	@SignValue
	private String subject;

	/**
	 * 订单金额。该笔订单的资金总额，取值范围[0.01,*********]，精确到小数点后2位。
	 */
	@Item
	@SignValue
	private String total_fee;

	@Item
	@SignValue
	private String trans_currency;

	/**
	 * 支付超时时间
	 */
	@Item
	@SignValue
	private String it_b_pay;

	/**
	 * 签名
	 */
	@Sign(alias = "sign",place= Sign.Place.FORM,signauteClass="com.hisun.lemon.cpi.alipay.ebankpay.util.AlipaySignature")
	@NotNull
	private String sign;

	public void setBody(String body) {
		this.body = body;
	}
	public String getBody() {
		return this.body;
	}


	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public String getCurrency() {
		return this.currency;
	}

	public void setGoodsDetail(String goodsDetail) {
		this.goodsDetail = goodsDetail;
	}
	public String getGoodsDetail() {
		return this.goodsDetail;
	}

	public String getOut_trade_no() {
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	public void setPrice(String price) {
		this.price = price;
	}
	public String getPrice() {
		return this.price;
	}

	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}
	public String getQuantity() {
		return this.quantity;
	}



	public void setSubject(String subject) {
		this.subject = subject;
	}
	public String getSubject() {
		try{
			if(subject != null && !subject.contains("%") && !subject.contains("+")){
/*				if(subject.contains("%")){
					return this.extend_params;
				}*/
				this.subject = URLEncoder.encode(subject,"utf-8");
				return this.subject;
			}
			return this.subject;

		}catch (Exception e){
			e.printStackTrace();
		}
		return null;
	}


	public String getApiMethodName() {
		return "alipay.acquire.overseas.spot.pay";
	}


	public String getTrans_currency() {
		return trans_currency;
	}

	public void setTrans_currency(String trans_currency) {
		this.trans_currency = trans_currency;
	}


	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getPartner() {
		return partner;
	}

	public void setPartner(String partner) {
		this.partner = partner;
	}

	public String get_input_charset() {
		return _input_charset == null ? AlipayConstants.CHARSET_UTF8 : _input_charset;
	}

	public void set_input_charset(String _input_charset) {
		this._input_charset = _input_charset;
	}

	public String getSign_type() {
		return sign_type;
	}

	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}


	public void setExtend_params(String extend_params) {
		this.extend_params = extend_params;
	}


	public void setProduct_code(String product_code) {
		this.product_code = product_code;
	}

	public String getNotify_url() {
		return notify_url;
	}

	public void setNotify_url(String notify_url) {
		this.notify_url = notify_url;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public String getSeller_id() {
		return seller_id;
	}

	public void setSeller_id(String seller_id) {
		this.seller_id = seller_id;
	}

	public String getShow_url() {
		return show_url;
	}

	public void setShow_url(String show_url) {
		this.show_url = show_url;
	}

	public String getTotal_fee() {
		return total_fee;
	}

	public void setTotal_fee(String total_fee) {
		this.total_fee = total_fee;
	}

	public String getExtend_params() {
		try{
			if(extend_params != null ){
				if(extend_params.contains("%")){
					return this.extend_params;
				}
				this.extend_params = URLEncoder.encode(extend_params,"utf-8");
				return this.extend_params;
			}
			return "";

		}catch (Exception e){
			e.printStackTrace();
		}
		return null;

	}

	public String getProduct_code() {
		return product_code;
	}

	public String getIt_b_pay() {
		return it_b_pay;
	}

	public void setIt_b_pay(String it_b_pay) {
		this.it_b_pay = it_b_pay;
	}
}
