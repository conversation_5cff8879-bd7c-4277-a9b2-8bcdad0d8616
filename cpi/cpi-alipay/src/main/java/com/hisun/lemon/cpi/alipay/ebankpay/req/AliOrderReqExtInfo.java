package com.hisun.lemon.cpi.alipay.ebankpay.req;

import javax.validation.constraints.NotNull;

public class AliOrderReqExtInfo {
	/**
	 * 二级商户id
	 */
	@NotNull
	private String secondary_merchant_id;
	/**
	 * 二级商户名
	 */
	@NotNull
	private String secondary_merchant_name;
	/**
	 * 二级商户商业类型
	 */
	@NotNull
	private String secondary_merchant_industry;
	/**
	 * 店铺名称
	 */
	@NotNull
	private String store_name;
	/**
	 * 店铺id
	 */
	@NotNull
	private String store_id;
	/**
	 * 终端编号id
	 */

	private String terminal_id;
	/**
	 * 技术提供商 id
	 */

	private String sys_service_provider_id;

	public String getSecondary_merchant_id() {
		return secondary_merchant_id;
	}

	public void setSecondary_merchant_id(String secondary_merchant_id) {
		this.secondary_merchant_id = secondary_merchant_id;
	}

	public String getSecondary_merchant_name() {
		return secondary_merchant_name;
	}

	public void setSecondary_merchant_name(String secondary_merchant_name) {
		this.secondary_merchant_name = secondary_merchant_name;
	}

	public String getSecondary_merchant_industry() {
		return secondary_merchant_industry;
	}

	public void setSecondary_merchant_industry(String secondary_merchant_industry) {
		this.secondary_merchant_industry = secondary_merchant_industry;
	}

	public String getStore_name() {
		return store_name;
	}

	public void setStore_name(String store_name) {
		this.store_name = store_name;
	}

	public String getStore_id() {
		return store_id;
	}

	public void setStore_id(String store_id) {
		this.store_id = store_id;
	}

	public String getTerminal_id() {
		return terminal_id;
	}

	public void setTerminal_id(String terminal_id) {
		this.terminal_id = terminal_id;
	}

	public String getSys_service_provider_id() {
		return sys_service_provider_id;
	}

	public void setSys_service_provider_id(String sys_service_provider_id) {
		this.sys_service_provider_id = sys_service_provider_id;
	}
}
