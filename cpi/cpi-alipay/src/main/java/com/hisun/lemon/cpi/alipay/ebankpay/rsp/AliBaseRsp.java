package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlTransient;

//@XmlRootElement(name="alipay")//(name = "response")
@XmlAccessorType(XmlAccessType.FIELD) 
public class AliBaseRsp implements Serializable{
	private static final long serialVersionUID = 1L;
	@XmlElement(name = "error")
	private String error;
	
	@XmlElement(name = "result_code")
	private String result_code;
	
	@XmlElement(name = "other")
	private String other;
	
	@XmlElement(name = "alipay_trans_id")	
	private String alipay_trans_id;	
	
	@XmlElement(name = "alipay_trans_status")
	private String alipay_trans_status;	
	
	@XmlElement(name = "qr_code")
	private String qr_code;	
		
	@XmlTransient
	public String getError() {
		return error;
	}
	public void setError(String error) {
		this.error = error;
	}
	@XmlTransient
	public String getResult_code() {
		return result_code;
	}
	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}
	public String getOther() {
		return other;
	}
	public void setOther(String other) {
		this.other = other;
	}
	
	public String getAlipay_trans_id() {
		return alipay_trans_id;
	}
	public void setAlipay_trans_id(String alipay_trans_id) {
		this.alipay_trans_id = alipay_trans_id;
	}
	
	public String getAlipay_trans_status() {
		return alipay_trans_status;
	}
	public void setAlipay_trans_status(String alipay_trans_status) {
		this.alipay_trans_status = alipay_trans_status;
	}
	@Override
	public String toString() {
		return "AliBaseRsp [error=" + error + ", result_code=" + result_code + ", other=" + other + "]";
	}
	public String getQr_code() {
		return qr_code;
	}
	public void setQr_code(String qr_code) {
		this.qr_code = qr_code;
	}	
}
