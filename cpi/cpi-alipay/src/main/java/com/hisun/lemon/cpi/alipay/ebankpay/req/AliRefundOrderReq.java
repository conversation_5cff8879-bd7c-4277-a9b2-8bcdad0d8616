package com.hisun.lemon.cpi.alipay.ebankpay.req;


import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.lemon.cpi.alipay.ebankpay.AlipayConstants;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * 退款订单请求报文
 * api: https://global.alipay.com/service/external_QR_Code/22
 * <AUTHOR>
 *
 */
@Plain(form = Plain.Form.QUERY_STRING)
public class AliRefundOrderReq {
	/**
	 * 服务名
	 */
	@Item
	@NotNull
	@SignValue
	private String service;

	/**
	 * 商务合作号
	 */
	@Item
	@NotNull
	@SignValue
	private String partner;

	/**
	 * 请求参数编码
	 */
	@Item
	@SignValue
	private String notify_url;

	@Item
	@SignValue
	private String _input_charset;


	/**
	 * 签名类型
	 */
	@Item
	@NotNull
	@SignValue
	private String sign_type;

	/**
	 * 签名
	 */
	@Sign(alias = "sign",place= Sign.Place.FORM,signauteClass="com.hisun.lemon.cpi.alipay.ebankpay.util.AlipaySignature")
	@NotNull
	private String sign;

	

	/**
	 *
	 */
	@Item
	@NotNull
	@SignValue
	private String partner_trans_id;

	/**
	 *
	 */
	@Item
	@NotNull
	@SignValue
	private String partner_refund_id;


	@Item
	@NotNull
	@SignValue
	private BigDecimal refund_amount;

	@Item
	@NotNull
	@SignValue
	private String currency;

	@Item
	@SignValue
	private String refund_reson;

	@Item
	@SignValue
	private String is_sync;


	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getPartner() {
		return partner;
	}

	public void setPartner(String partner) {
		this.partner = partner;
	}

	public String getNotify_url() {
		return notify_url;
	}

	public void setNotify_url(String notify_url) {
		this.notify_url = notify_url;
	}

	public String get_input_charset() {
		return _input_charset == null ? AlipayConstants.CHARSET_UTF8 : _input_charset;
	}

	public void set_input_charset(String _input_charset) {
		this._input_charset = _input_charset;
	}

	public String getSign_type() {
		return sign_type;
	}

	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getPartner_trans_id() {
		return partner_trans_id;
	}

	public void setPartner_trans_id(String partner_trans_id) {
		this.partner_trans_id = partner_trans_id;
	}

	public String getPartner_refund_id() {
		return partner_refund_id;
	}

	public void setPartner_refund_id(String partner_refund_id) {
		this.partner_refund_id = partner_refund_id;
	}

	public BigDecimal getRefund_amount() {
		return refund_amount;
	}

	public void setRefund_amount(BigDecimal refund_amount) {
		this.refund_amount = refund_amount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getRefund_reson() {
		return refund_reson;
	}

	public void setRefund_reson(String refund_reson) {
		this.refund_reson = refund_reson;
	}

	public String getIs_sync() {
		return is_sync;
	}

	public void setIs_sync(String is_sync) {
		this.is_sync = is_sync;
	}
}
