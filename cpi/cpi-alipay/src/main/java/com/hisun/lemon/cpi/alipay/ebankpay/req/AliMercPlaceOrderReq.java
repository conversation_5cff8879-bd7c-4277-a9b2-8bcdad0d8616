package com.hisun.lemon.cpi.alipay.ebankpay.req;

import com.hisun.channel.parse.annotation.*;
import com.hisun.lemon.cpi.alipay.ebankpay.AlipayConstants;
import com.hisun.lemon.cpi.alipay.ebankpay.util.StringUtils;

import java.math.BigDecimal;
import java.net.URLEncoder;

import javax.validation.constraints.NotNull;


/**
 * 商户扫码，支付宝刷卡支付
 * api: https://global.alipay.com/service/barcode/9
 * <AUTHOR>
 *
 */
@Plain(form = Plain.Form.QUERY_STRING)
public class AliMercPlaceOrderReq{
	/**
	 * 服务名
	 */
	@NotNull
	@Item
	@SignValue
	private String service;

	/**
	 * 商务合作号
	 */
	@NotNull
	@Item
	@SignValue
	private String partner;

	/**
	 * 请求参数编码
	 */
	@Item
	@SignValue
	private String _input_charset;

	/**
	 * 签名
	 */
	@NotNull
	@Sign(alias = "sign",place= Sign.Place.FORM,signauteClass="com.hisun.lemon.cpi.alipay.ebankpay.util.AlipaySignature")
	private String sign;

	/**
	 * 签名类型
	 */
	@NotNull
	@Item
	@SignValue
	private String sign_type;


	/**
	 * 和商务合作号partner一致
	 */
	@NotNull
	@Item
	@SignValue
	private String alipay_seller_id;

	/**
	 * 商品数量
	 */
	@Item
	@SignValue
	private int quantity;

	/**
	 * 商品名称
	 */
	@NotNull
	@Item
	@SignValue
	private String trans_name;

	/**
	 * 商户订单号
	 */
	@NotNull
	@Item
	@SignValue
	private String partner_trans_id;

	/**
	 * 标价金额
	 */
	@NotNull
	@Item
	@SignValue
	private String trans_amount;

	/**
	 * 标价币种
	 */
	@NotNull
	@Item
	@SignValue
	private String currency;

	/**
	 * 付款码
	 */
	@NotNull
	@Item
	@SignValue
	private String buyer_identity_code;

	/**
	 * 付款码种类  QRcode  /  barcode
	 */
	@NotNull
	@Item
	@SignValue
	private String identity_code_type;

	/**
	 * 支付订单创建时间
	 */
	@Item
	@SignValue
	private String trans_create_time;

	/**
	 * 备注
	 */
	@Item
	@SignValue
	private String memo;

	/**
	 * 产品名称，固定填写值 OVERSEAS_MBARCODE_PAY
	 */
	@Item
	@NotNull
	@SignValue
	private String biz_product;

	/**
	 * 附加数据 json结构
	 */
	@Item
	@NotNull
	@SignValue
	private String extend_info;


	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getPartner() {
		return partner;
	}

	public void setPartner(String partner) {
		this.partner = partner;
	}

	public String get_input_charset() {
		return _input_charset == null ? AlipayConstants.CHARSET_UTF8 : _input_charset;
	}

	public void set_input_charset(String _input_charset) {
		this._input_charset = _input_charset;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getSign_type() {
		return sign_type;
	}

	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	public String getAlipay_seller_id() {
		return alipay_seller_id;
	}

	public void setAlipay_seller_id(String alipay_seller_id) {
		this.alipay_seller_id = alipay_seller_id;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public String getTrans_name() {
		try {
			if(this.trans_name != null && !this.trans_name.contains("%") && !this.trans_name.contains("+")){
				this.trans_name = URLEncoder.encode(trans_name,"utf-8");
				return this.trans_name;
			}
		}catch (Exception ex){

		}
		return trans_name;
	}

	public void setTrans_name(String trans_name) {
		this.trans_name = trans_name;
	}

	public String getPartner_trans_id() {
		return partner_trans_id;
	}

	public void setPartner_trans_id(String partner_trans_id) {
		this.partner_trans_id = partner_trans_id;
	}

	public String getTrans_amount() {
		return trans_amount;
	}

	public void setTrans_amount(String trans_amount) {
		this.trans_amount = trans_amount;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getBuyer_identity_code() {
		return buyer_identity_code;
	}

	public void setBuyer_identity_code(String buyer_identity_code) {
		this.buyer_identity_code = buyer_identity_code;
	}

	public String getIdentity_code_type() {
		return identity_code_type;
	}

	public void setIdentity_code_type(String identity_code_type) {
		this.identity_code_type = identity_code_type;
	}

	public String getTrans_create_time() {
		return trans_create_time;
	}

	public void setTrans_create_time(String trans_create_time) {
		this.trans_create_time = trans_create_time;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getBiz_product() {
		return biz_product;
	}

	public void setBiz_product(String biz_product) {
		this.biz_product = biz_product;
	}

	public String getExtend_info() {
		try{
			if(extend_info != null ){
				if(extend_info.contains("%")){
					return this.extend_info;
				}
				this.extend_info = URLEncoder.encode(extend_info,"utf-8");
				return this.extend_info;
			}

		}catch (Exception e){
			e.printStackTrace();
		}
		return null;
	}

	public void setExtend_info(String extend_info) {
		this.extend_info = extend_info;
	}
}
