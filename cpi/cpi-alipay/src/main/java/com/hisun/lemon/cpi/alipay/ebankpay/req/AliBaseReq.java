package com.hisun.lemon.cpi.alipay.ebankpay.req;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.lemon.cpi.alipay.ebankpay.AlipayConstants;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlTransient;

/**
 * 支付宝基本请求参数
 * 
 * <AUTHOR>
 *
 */
@Plain(form = Plain.Form.QUERY_STRING)
public class AliBaseReq {

	/**
	 * 服务名
	 */
	@Item
	@NotNull
	@SignValue
	private String service;

	/**
	 * 商务合作号
	 */
	@Item
	@NotNull
	@SignValue
	private String partner;

	/**
	 * 请求参数编码
	 */
	@Item
	@SignValue
	private String _input_charset;

	/**
	 * 签名
	 */
	@Sign(alias = "sign",place= Sign.Place.FORM,signauteClass="com.hisun.lemon.cpi.alipay.ebankpay.util.AlipaySignature")
	@NotNull
	private String sign;

	/**
	 * 签名类型
	 */
	@Item
	@NotNull
	@SignValue
	private String sign_type;


	@XmlTransient
	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}
	@XmlTransient
	public String getPartner() {
		return partner;
	}

	public void setPartner(String partner) {
		this.partner = partner;
	}
	@XmlTransient
	public String get_input_charset() {
		return _input_charset == null ? AlipayConstants.CHARSET_UTF8 : _input_charset;
	}

	public void set_input_charset(String _input_charset) {
		this._input_charset = _input_charset;
	}
	@XmlTransient
	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}
	@XmlTransient
	public String getSign_type() {
		return sign_type;
	}

	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}
}
