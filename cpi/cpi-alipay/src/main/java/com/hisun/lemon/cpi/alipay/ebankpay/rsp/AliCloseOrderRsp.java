package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 关闭订单响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class AliCloseOrderRsp {

	@Item(alias="is_success")
	private String isSuccess;

	@Item(alias="sign_type")
	private String signType;

	@Item(alias="sign")
	private String sign;

	@Item(alias="error")
	private String error;

	@Nest(alias = "response")
	private CancelDetailRsp cancelDetailRsp;

	public static class CancelDetailRsp {

		@Item(alias = "action")
		private String action;

		@Item(alias = "out_trade_no")
		private String outTradeNo;

		@Item(alias = "result_code")
		private String resultCode;

		@Item(alias = "retry_flag")
		private String retryFlag;

		@Item(alias = "trade_no")
		private String tradeNo;

		@Item(alias = "display_message")
		private String displayMessage;

		public String getAction() {
			return action;
		}

		public void setAction(String action) {
			this.action = action;
		}

		public String getOutTradeNo() {
			return outTradeNo;
		}

		public void setOutTradeNo(String outTradeNo) {
			this.outTradeNo = outTradeNo;
		}

		public String getResultCode() {
			return resultCode;
		}

		public void setResultCode(String resultCode) {
			this.resultCode = resultCode;
		}

		public String getRetryFlag() {
			return retryFlag;
		}

		public void setRetryFlag(String retryFlag) {
			this.retryFlag = retryFlag;
		}

		public String getTradeNo() {
			return tradeNo;
		}

		public void setTradeNo(String tradeNo) {
			this.tradeNo = tradeNo;
		}

		public String getDisplayMessage() {
			return displayMessage;
		}

		public void setDisplayMessage(String displayMessage) {
			this.displayMessage = displayMessage;
		}

		@Override
		public String toString() {
			return "CancelDetailRsp{" +
					"action='" + action + '\'' +
					", outTradeNo='" + outTradeNo + '\'' +
					", resultCode='" + resultCode + '\'' +
					", retryFlag='" + retryFlag + '\'' +
					", tradeNo='" + tradeNo + '\'' +
					", displayMessage='" + displayMessage + '\'' +
					'}';
		}
	}

	public String getIsSuccess() {
		return isSuccess;
	}

	public void setIsSuccess(String isSuccess) {
		this.isSuccess = isSuccess;
	}

	public String getSignType() {
		return signType;
	}

	public void setSignType(String signType) {
		this.signType = signType;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public CancelDetailRsp getCancelDetailRsp() {
		return cancelDetailRsp;
	}

	public void setCancelDetailRsp(CancelDetailRsp cancelDetailRsp) {
		this.cancelDetailRsp = cancelDetailRsp;
	}

	public String getError() {
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	@Override
	public String toString() {
		return "AliCloseOrderRsp{" +
				"isSuccess='" + isSuccess + '\'' +
				", signType='" + signType + '\'' +
				", sign='" + sign + '\'' +
				", error='" + error + '\'' +
				", cancelDetailRsp=" + cancelDetailRsp +
				'}';
	}
}
