package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 离线支付响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "VSPAPI")
@VerifyValue
public class OfflinePaymentRsp {

}
