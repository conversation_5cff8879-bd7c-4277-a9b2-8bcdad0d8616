package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlTransient;

@XmlAccessorType(XmlAccessType.FIELD)
public class AliResponse  implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1103343945175477916L;
	@XmlElement(name = "alipay")
	private AliBaseRsp alipay;

	@XmlTransient
	public AliBaseRsp getAlipay() {
		return alipay;
	}

	public void setAlipay(AliBaseRsp alipay) {
		this.alipay = alipay;
	}

	@Override
	public String toString() {
		return "AliResponse [alipay=" + alipay + "]";
	}
}
