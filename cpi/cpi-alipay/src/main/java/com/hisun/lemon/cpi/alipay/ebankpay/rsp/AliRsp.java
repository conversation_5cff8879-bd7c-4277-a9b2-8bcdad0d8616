package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlTransient;

/**
 * 商户扫码，支付宝刷卡支付响应报文
 * 
 * <AUTHOR>
 *
 */
//@XmlRootElement(name = "alipay")
@Xml(root = "xml", removeHead = true)
public class AliRsp implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 返回状态吗
	 */

	@Item(cdata=true)
	private String is_success;

	/**
	 * 返回信息
	 */
//	@XmlElement(name = "response")
	@Item(cdata=true)
	private AliResponse response;

	/**
	 * 公共账号id
	 */
//	@XmlElementWrapper(name = "request")
////	@XmlElement(name = "param")
//	private List<AliMercPlaceOrderReq> param;

	/**
	 * 商户号
	 */
	@Item(cdata=true)
	private String sign;

	/**
	 * 设备号
	 */
	@Item(cdata=true)
	private String sign_type;

	public String getIs_success() {
		return is_success;
	}

	public void setIs_success(String is_success) {
		this.is_success = is_success;
	}





//	@XmlTransient
//	public List<AliMercPlaceOrderReq> getParam() {
//		return param;
//	}
//
//	public void setParam(List<AliMercPlaceOrderReq> param) {
//		this.param = param;
//	}

	public String getSign() {
		return sign;
	}

	@XmlTransient
	public AliResponse getResponse() {
		return response;
	}

	public void setResponse(AliResponse response) {
		this.response = response;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getSign_type() {
		return sign_type;
	}

	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	@Override
	public String toString() {
		return "AliRsp [is_success=" + is_success + ", response=" + response + ", sign=" + sign + ", sign_type="
				+ sign_type + "]";
	}
}
