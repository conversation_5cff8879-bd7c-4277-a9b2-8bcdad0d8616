package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 订单查询响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class AliOrderQueryRsp {

	@Item(alias="is_success")
	private String isSuccess;

	@Item(alias="sign_type")
	private String signType;

	@Item(alias="sign")
	private String sign;

	@Item(alias="error")
	private String error;


	@Nest(alias = "response")
	private OrderDetailRsp orderDetailRsp;

	public static class OrderDetailRsp {
		@Item(alias = "alipay_buyer_login_id")
		private String alipayBuyerLoginId;

		@Item(alias = "alipay_buyer_user_id")
		private String alipayBuyerUserId;

		@Item(alias = "alipay_pay_time")
		private String alipayPayTime;

		@Item(alias = "alipay_trans_id")
		private String alipayTransId;

		@Item(alias = "alipay_trans_status")
		private String alipayTransStatus;

		@Item(alias = "currency")
		private String currency;

		@Item(alias = "exchange_rate")
		private String exchangeRate;

		@Item(alias = "forex_total_fee")
		private String forexTotalFee;


		@Item(alias = "partner_trans_id")
		private String partnerTransId;

		@Item(alias = "out_trade_no")
		private String outTradeNo;

		@Item(alias = "result_code")
		private String resultCode;

		@Item(alias = "trans_amount")
		private String transAmount;

		@Item(alias = "trans_amount_cny")
		private String transAmountCny;

		@Item(alias = "trans_forex_rate")
		private String transForexRate;

		@Item(alias = "detail_error_code")
		private String detailErrorCode;

		@Item(alias = "detail_error_des")
		private String detailErrorDes;

		public String getAlipayBuyerLoginId() {
			return alipayBuyerLoginId;
		}

		public void setAlipayBuyerLoginId(String alipayBuyerLoginId) {
			this.alipayBuyerLoginId = alipayBuyerLoginId;
		}

		public String getAlipayBuyerUserId() {
			return alipayBuyerUserId;
		}

		public void setAlipayBuyerUserId(String alipayBuyerUserId) {
			this.alipayBuyerUserId = alipayBuyerUserId;
		}

		public String getAlipayPayTime() {
			return alipayPayTime;
		}

		public void setAlipayPayTime(String alipayPayTime) {
			this.alipayPayTime = alipayPayTime;
		}

		public String getAlipayTransId() {
			return alipayTransId;
		}

		public void setAlipayTransId(String alipayTransId) {
			this.alipayTransId = alipayTransId;
		}

		public String getAlipayTransStatus() {
			return alipayTransStatus;
		}

		public void setAlipayTransStatus(String alipayTransStatus) {
			this.alipayTransStatus = alipayTransStatus;
		}

		public String getCurrency() {
			return currency;
		}

		public void setCurrency(String currency) {
			this.currency = currency;
		}

		public String getExchangeRate() {
			return exchangeRate;
		}

		public void setExchangeRate(String exchangeRate) {
			this.exchangeRate = exchangeRate;
		}

		public String getForexTotalFee() {
			return forexTotalFee;
		}

		public void setForexTotalFee(String forexTotalFee) {
			this.forexTotalFee = forexTotalFee;
		}

		public String getPartnerTransId() {
			return partnerTransId;
		}

		public void setPartnerTransId(String partnerTransId) {
			this.partnerTransId = partnerTransId;
		}

		public String getOutTradeNo() {
			return outTradeNo;
		}

		public void setOutTradeNo(String outTradeNo) {
			this.outTradeNo = outTradeNo;
		}

		public String getResultCode() {
			return resultCode;
		}

		public void setResultCode(String resultCode) {
			this.resultCode = resultCode;
		}

		public String getTransAmount() {
			return transAmount;
		}

		public void setTransAmount(String transAmount) {
			this.transAmount = transAmount;
		}

		public String getTransAmountCny() {
			return transAmountCny;
		}

		public void setTransAmountCny(String transAmountCny) {
			this.transAmountCny = transAmountCny;
		}

		public String getTransForexRate() {
			return transForexRate;
		}

		public void setTransForexRate(String transForexRate) {
			this.transForexRate = transForexRate;
		}

		public String getDetailErrorCode() {
			return detailErrorCode;
		}

		public void setDetailErrorCode(String detailErrorCode) {
			this.detailErrorCode = detailErrorCode;
		}

		public String getDetailErrorDes() {
			return detailErrorDes;
		}

		public void setDetailErrorDes(String detailErrorDes) {
			this.detailErrorDes = detailErrorDes;
		}

		@Override
		public String toString() {
			return "OrderDetailRsp{" +
					"alipayBuyerLoginId='" + alipayBuyerLoginId + '\'' +
					", alipayBuyerUserId='" + alipayBuyerUserId + '\'' +
					", alipayPayTime='" + alipayPayTime + '\'' +
					", alipayTransId='" + alipayTransId + '\'' +
					", alipayTransStatus='" + alipayTransStatus + '\'' +
					", currency='" + currency + '\'' +
					", exchangeRate='" + exchangeRate + '\'' +
					", forexTotalFee='" + forexTotalFee + '\'' +
					", partnerTransId='" + partnerTransId + '\'' +
					", outTradeNo='" + outTradeNo + '\'' +
					", resultCode='" + resultCode + '\'' +
					", transAmount='" + transAmount + '\'' +
					", transAmountCny='" + transAmountCny + '\'' +
					", transForexRate='" + transForexRate + '\'' +
					", detailErrorCode='" + detailErrorCode + '\'' +
					", detailErrorDes='" + detailErrorDes + '\'' +
					'}';
		}
	}

	public String getIsSuccess() {
		return isSuccess;
	}

	public void setIsSuccess(String isSuccess) {
		this.isSuccess = isSuccess;
	}

	public String getSignType() {
		return signType;
	}

	public void setSignType(String signType) {
		this.signType = signType;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public OrderDetailRsp getOrderDetailRsp() {
		return orderDetailRsp;
	}

	public void setOrderDetailRsp(OrderDetailRsp orderDetailRsp) {
		this.orderDetailRsp = orderDetailRsp;
	}

	public String getError() {
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	@Override
	public String toString() {
		return "OrderQueryRsp{" +
				"isSuccess='" + isSuccess + '\'' +
				", signType='" + signType + '\'' +
				", sign='" + sign + '\'' +
				", error='" + error + '\'' +
				", orderDetailRsp=" + orderDetailRsp +
				'}';
	}
}
