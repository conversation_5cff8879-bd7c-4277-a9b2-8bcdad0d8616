package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Xml;

/**
 * Created by XianSky on 2017/12/27.
 */
@Xml(root = "xml", removeHead = true)
public class AliPlaceOrderRsp extends AlipayResponse {
    private static final long serialVersionUID = 2456278367459739544L;


    @Item(alias="is_success")
    private String isSuccess;

    @Item(alias="sign_type")
    private String signType;

    @Item(alias="sign")
    private String sign;

    @Item(alias="error")
    private String error;

    @Nest(alias = "response")
    private PreCreateRsp preCreateRsp;

    public static class PreCreateRsp {

        @Item(alias = "big_pic_url")
        private String bigPicUrl;

        @Item(alias = "out_trade_no")
        private String outTradeNo;

        @Item(alias = "pic_url")
        private String picUrl;

        @Item(alias = "qr_code")
        private String qrCode;

        @Item(alias = "small_pic_url")
        private String smallPicUrl;

        @Item(alias = "voucher_type")
        private String voucherType;

        public String getResultCode() {
            return resultCode;
        }

        public void setResultCode(String resultCode) {
            this.resultCode = resultCode;
        }

        @Item(alias = "result_code")
        private String resultCode;

        public String getBigPicUrl() {
            return bigPicUrl;
        }

        public void setBigPicUrl(String bigPicUrl) {
            this.bigPicUrl = bigPicUrl;
        }

        public String getOutTradeNo() {
            return outTradeNo;
        }

        public void setOutTradeNo(String outTradeNo) {
            this.outTradeNo = outTradeNo;
        }

        public String getPicUrl() {
            return picUrl;
        }

        public void setPicUrl(String picUrl) {
            this.picUrl = picUrl;
        }

        public String getQrCode() {
            return qrCode;
        }

        public void setQrCode(String qrCode) {
            this.qrCode = qrCode;
        }

        public String getSmallPicUrl() {
            return smallPicUrl;
        }

        public void setSmallPicUrl(String smallPicUrl) {
            this.smallPicUrl = smallPicUrl;
        }

        public String getVoucherType() {
            return voucherType;
        }

        public void setVoucherType(String voucherType) {
            this.voucherType = voucherType;
        }

        @Override
        public String toString() {
            return "PreCreateRsp{" +
                    "bigPicUrl='" + bigPicUrl + '\'' +
                    ", outTradeNo='" + outTradeNo + '\'' +
                    ", picUrl='" + picUrl + '\'' +
                    ", qrCode='" + qrCode + '\'' +
                    ", smallPicUrl='" + smallPicUrl + '\'' +
                    ", voucherType='" + voucherType + '\'' +
                    '}';
        }
    }

    public AliPlaceOrderRsp() {
    }


    public String getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(String isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public PreCreateRsp getPreCreateRsp() {
        return preCreateRsp;
    }

    public void setPreCreateRsp(PreCreateRsp preCreateRsp) {
        this.preCreateRsp = preCreateRsp;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    @Override
    public String toString() {
        return "PlaceOrderRsp{" +
                "isSuccess='" + isSuccess + '\'' +
                ", signType='" + signType + '\'' +
                ", sign='" + sign + '\'' +
                ", error='" + error + '\'' +
                ", preCreateRsp=" + preCreateRsp +
                '}';
    }
}
