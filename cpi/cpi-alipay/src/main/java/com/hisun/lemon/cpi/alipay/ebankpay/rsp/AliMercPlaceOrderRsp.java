package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.*;



/**
 * 商户扫码，支付宝刷卡支付
 * api: https://global.alipay.com/service/barcode/9
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class AliMercPlaceOrderRsp extends AlipayResponse{
	@Item(alias="is_success")
	private String isSuccess;

	@Item(alias="sign_type")
	private String signType;

	@Item(alias="sign")
	private String sign;

	@Item(alias="error")
	private String error;

	@Nest(alias = "response")
	private MercPlaceOrderDetail mercPlaceOrderDetail;

	public static class MercPlaceOrderDetail{

		@Item(alias = "error")
		private String error;

		@Item(alias = "alipay_buyer_login_id")
		private String alipayBuyerLoginId;

		@Item(alias = "alipay_buyer_user_id")
		private String alipayBuyerUserId;

		@Item(alias = "alipay_pay_time")
		private String alipayPayTime;

		@Item(alias = "alipay_trans_id")
		private String alipayTransId;

		@Item(alias = "currency")
		private String currency;

		@Item(alias = "exchange_rate")
		private String exchangeRate;

		@Item(alias = "partner_trans_id")
		private String partnerTransId;

		@Item(alias = "result_code")
		private String resultCode;

		@Item(alias = "trans_amount")
		private String transAmount;

		@Item(alias = "trans_amount_cny")
		private String transAmountCny;

		public String getError() {
			return error;
		}

		public void setError(String error) {
			this.error = error;
		}

		public String getAlipayBuyerLoginId() {
			return alipayBuyerLoginId;
		}

		public void setAlipayBuyerLoginId(String alipayBuyerLoginId) {
			this.alipayBuyerLoginId = alipayBuyerLoginId;
		}

		public String getAlipayBuyerUserId() {
			return alipayBuyerUserId;
		}

		public void setAlipayBuyerUserId(String alipayBuyerUserId) {
			this.alipayBuyerUserId = alipayBuyerUserId;
		}

		public String getAlipayPayTime() {
			return alipayPayTime;
		}

		public void setAlipayPayTime(String alipayPayTime) {
			this.alipayPayTime = alipayPayTime;
		}

		public String getAlipayTransId() {
			return alipayTransId;
		}

		public void setAlipayTransId(String alipayTransId) {
			this.alipayTransId = alipayTransId;
		}

		public String getCurrency() {
			return currency;
		}

		public void setCurrency(String currency) {
			this.currency = currency;
		}

		public String getExchangeRate() {
			return exchangeRate;
		}

		public void setExchangeRate(String exchangeRate) {
			this.exchangeRate = exchangeRate;
		}

		public String getPartnerTransId() {
			return partnerTransId;
		}

		public void setPartnerTransId(String partnerTransId) {
			this.partnerTransId = partnerTransId;
		}

		public String getResultCode() {
			return resultCode;
		}

		public void setResultCode(String resultCode) {
			this.resultCode = resultCode;
		}

		public String getTransAmount() {
			return transAmount;
		}

		public void setTransAmount(String transAmount) {
			this.transAmount = transAmount;
		}

		public String getTransAmountCny() {
			return transAmountCny;
		}

		public void setTransAmountCny(String transAmountCny) {
			this.transAmountCny = transAmountCny;
		}

		@Override
		public String toString() {
			return "MercPlaceOrderDetail{" +
					"error='" + error + '\'' +
					", alipayBuyerLoginId='" + alipayBuyerLoginId + '\'' +
					", alipayBuyerUserId='" + alipayBuyerUserId + '\'' +
					", alipayPayTime='" + alipayPayTime + '\'' +
					", alipayTransId='" + alipayTransId + '\'' +
					", currency='" + currency + '\'' +
					", exchangeRate='" + exchangeRate + '\'' +
					", partnerTransId='" + partnerTransId + '\'' +
					", resultCode='" + resultCode + '\'' +
					", transAmount='" + transAmount + '\'' +
					", transAmountCny='" + transAmountCny + '\'' +
					'}';
		}
	}

	public String getIsSuccess() {
		return isSuccess;
	}

	public void setIsSuccess(String isSuccess) {
		this.isSuccess = isSuccess;
	}

	public String getSignType() {
		return signType;
	}

	public void setSignType(String signType) {
		this.signType = signType;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getError() {
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	public MercPlaceOrderDetail getMercPlaceOrderDetail() {
		return mercPlaceOrderDetail;
	}

	public void setMercPlaceOrderDetail(MercPlaceOrderDetail mercPlaceOrderDetail) {
		this.mercPlaceOrderDetail = mercPlaceOrderDetail;
	}

	@Override
	public String toString() {
		return "AliMercPlaceOrderRsp{" +
				"isSuccess='" + isSuccess + '\'' +
				", signType='" + signType + '\'' +
				", sign='" + sign + '\'' +
				", error='" + error + '\'' +
				", mercPlaceOrderDetail=" + mercPlaceOrderDetail +
				'}';
	}
}
