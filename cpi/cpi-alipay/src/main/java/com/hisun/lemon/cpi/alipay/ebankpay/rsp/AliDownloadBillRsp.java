package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Verify;

/**
 * 下载对账单响应报文
 * 
 * <AUTHOR>
 *
 */
@Plain
public class AliDownloadBillRsp {

	/**
	 * 返回信息
	 */
	@Item
	private String return_result;

	@Verify(signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
	private String return_sign;

	public String getReturn_result() {
		return return_result;
	}

	public void setReturn_result(String return_result) {
		this.return_result = return_result;
	}

	public String getReturn_sign() {
		return return_sign;
	}

	public void setReturn_sign(String return_sign) {
		this.return_sign = return_sign;
	}
}
