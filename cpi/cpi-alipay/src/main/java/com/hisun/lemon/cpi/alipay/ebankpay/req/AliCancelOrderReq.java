package com.hisun.lemon.cpi.alipay.ebankpay.req;


import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.lemon.cpi.alipay.ebankpay.AlipayConstants;

import javax.validation.constraints.NotNull;


/**
 * 关闭订单请求报文
 * <AUTHOR>
 *
 */
@Plain(form = Plain.Form.QUERY_STRING)
public class AliCancelOrderReq {
	/**
	 * 服务名
	 */
	@Item
	@NotNull
	@SignValue
	private String service;

	/**
	 * 商务合作号
	 */
	@Item
	@NotNull
	@SignValue
	private String partner;

	/**
	 * 请求参数编码
	 */
	@Item
	@NotNull
	@SignValue
	private String _input_charset;

	/**
	 * 签名
	 */
	@Sign(alias = "sign",place= Sign.Place.FORM,signauteClass="com.hisun.lemon.cpi.alipay.ebankpay.util.AlipaySignature")
	@NotNull
	private String sign;

	/**
	 * 签名类型
	 */
	@Item
	@NotNull
	@SignValue
	private String sign_type;



	/**
	 * 当前时间戳
	 */
	@Item
	@NotNull
	@SignValue
	private String timestamp;

	@Item
	@SignValue
	private String terminal_timestamp;


	/**
	 * 商户订单号
	 */
	@Item
	@NotNull
	@SignValue
	private String out_trade_no;

	@Item
	@SignValue
	private String trade_no;


	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getPartner() {
		return partner;
	}

	public void setPartner(String partner) {
		this.partner = partner;
	}

	public String get_input_charset() {
		return _input_charset == null ? AlipayConstants.CHARSET_UTF8 : _input_charset;
	}

	public void set_input_charset(String _input_charset) {
		this._input_charset = _input_charset;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getSign_type() {
		return sign_type;
	}

	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public String getTerminal_timestamp() {
		return terminal_timestamp;
	}

	public void setTerminal_timestamp(String terminal_timestamp) {
		this.terminal_timestamp = terminal_timestamp;
	}

	public String getOut_trade_no() {
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	public String getTrade_no() {
		return trade_no;
	}

	public void setTrade_no(String trade_no) {
		this.trade_no = trade_no;
	}
}
