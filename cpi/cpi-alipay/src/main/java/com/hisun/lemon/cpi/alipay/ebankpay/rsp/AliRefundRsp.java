package com.hisun.lemon.cpi.alipay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 申请退款请求报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class AliRefundRsp {

	@Item(alias="is_success")
	private String isSuccess;

	@Item(alias="sign_type")
	private String signType;

	@Item(alias="sign")
	private String sign;

	@Item(alias="error")
	private String error;


	@Nest(alias = "response")
	private RefundDetailRsp refundDetailRsp;

	public static class RefundDetailRsp{

		@Item(alias = "alipay_trans_id")
		private String alipayTransId;

		@Item(alias = "currency")
		private String currency;

		@Item(alias = "exchange_rate")
		private String exchangeRate;

		@Item(alias = "partner_refund_id")
		private String partnerRefundId;

		@Item(alias = "partner_trans_id")
		private String partnerTransId;

		@Item(alias = "refund_amount")
		private String refundAmount;

		@Item(alias = "refund_amount_cny")
		private String refundAmountCny;

		@Item(alias = "result_code")
		private String resultCode;

		public String getAlipayTransId() {
			return alipayTransId;
		}

		public void setAlipayTransId(String alipayTransId) {
			this.alipayTransId = alipayTransId;
		}

		public String getCurrency() {
			return currency;
		}

		public void setCurrency(String currency) {
			this.currency = currency;
		}

		public String getExchangeRate() {
			return exchangeRate;
		}

		public void setExchangeRate(String exchangeRate) {
			this.exchangeRate = exchangeRate;
		}

		public String getPartnerRefundId() {
			return partnerRefundId;
		}

		public void setPartnerRefundId(String partnerRefundId) {
			this.partnerRefundId = partnerRefundId;
		}

		public String getPartnerTransId() {
			return partnerTransId;
		}

		public void setPartnerTransId(String partnerTransId) {
			this.partnerTransId = partnerTransId;
		}

		public String getRefundAmount() {
			return refundAmount;
		}

		public void setRefundAmount(String refundAmount) {
			this.refundAmount = refundAmount;
		}

		public String getRefundAmountCny() {
			return refundAmountCny;
		}

		public void setRefundAmountCny(String refundAmountCny) {
			this.refundAmountCny = refundAmountCny;
		}

		public String getResultCode() {
			return resultCode;
		}

		public void setResultCode(String resultCode) {
			this.resultCode = resultCode;
		}

		@Override
		public String toString() {
			return "RefundDetailRsp{" +
					"alipayTransId='" + alipayTransId + '\'' +
					", currency='" + currency + '\'' +
					", exchangeRate='" + exchangeRate + '\'' +
					", partnerRefundId='" + partnerRefundId + '\'' +
					", partnerTransId='" + partnerTransId + '\'' +
					", refundAmount='" + refundAmount + '\'' +
					", refundAmountCny='" + refundAmountCny + '\'' +
					", resultCode='" + resultCode + '\'' +
					'}';
		}
	}

	public String getError() {
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	public String getIsSuccess() {
		return isSuccess;
	}

	public void setIsSuccess(String isSuccess) {
		this.isSuccess = isSuccess;
	}

	public String getSignType() {
		return signType;
	}

	public void setSignType(String signType) {
		this.signType = signType;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public RefundDetailRsp getRefundDetailRsp() {
		return refundDetailRsp;
	}

	public void setRefundDetailRsp(RefundDetailRsp refundDetailRsp) {
		this.refundDetailRsp = refundDetailRsp;
	}

	@Override
	public String toString() {
		return "ApplyRefundRsp{" +
				"isSuccess='" + isSuccess + '\'' +
				", signType='" + signType + '\'' +
				", sign='" + sign + '\'' +
				", error='" + error + '\'' +
				", refundDetailRsp=" + refundDetailRsp +
				'}';
	}
}
