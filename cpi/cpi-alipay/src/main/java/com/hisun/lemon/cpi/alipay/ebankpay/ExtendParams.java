package com.hisun.lemon.cpi.alipay.ebankpay;


import com.alibaba.fastjson.JSONObject;
import com.hisun.lemon.cpi.alipay.ebankpay.util.StringUtils;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 18/01/26.
 * 扩展信息
 */
public class ExtendParams {
    private String secondary_merchant_id ;

    private String secondary_merchant_name;

    private String secondary_merchant_industry;

    private String store_name;

    private String store_id;
    // 系统商编号
    private String terminal_id;

    private String sys_service_provider_id;

    @Override
    public String toString() {
        Map<String,String> contentMap = new HashMap<>();
        JSONObject object = new JSONObject();
        if(this.getSecondary_merchant_id() != null && this.getSecondary_merchant_id() !=""){
            object.put("secondary_merchant_id",this.getSecondary_merchant_id());
        }
        if(this.getSecondary_merchant_name() != null && this.getSecondary_merchant_name() != ""){
            object.put("secondary_merchant_name", this.getSecondary_merchant_name());
        }
        if(this.getSecondary_merchant_industry() != null && this.getSecondary_merchant_industry() != ""){
            object.put("secondary_merchant_industry",this.getSecondary_merchant_industry());
        }

        if(this.getStore_name() != null && this.getStore_name() != ""){
            object.put("store_name",this.getStore_name());
        }

        if(this.getStore_id() != null && this.getStore_id() != ""){
            object.put("store_id",this.getStore_id());
        }

        if(this.getTerminal_id() != null && this.getTerminal_id() != ""){
            object.put("terminal_id",this.getTerminal_id());
        }

        if(this.getSys_service_provider_id() != null && this.getSys_service_provider_id() != ""){
            object.put("sys_service_provider_id",this.getSys_service_provider_id());
        }

        List<String> keys = new ArrayList(contentMap.keySet());
        Collections.sort(keys);
        for(String key : keys){
            object.put(key,contentMap.get(key));
        }
        return object.toJSONString();
    }

    public String getSecondary_merchant_id() {
        return secondary_merchant_id;
    }

    public void setSecondary_merchant_id(String secondary_merchant_id) {
        this.secondary_merchant_id = secondary_merchant_id;
    }

    public String getSecondary_merchant_name() {
        return secondary_merchant_name;
    }

    public void setSecondary_merchant_name(String secondary_merchant_name) {
        this.secondary_merchant_name = secondary_merchant_name;
    }

    public String getSecondary_merchant_industry() {
        return secondary_merchant_industry;
    }

    public void setSecondary_merchant_industry(String secondary_merchant_industry) {
        this.secondary_merchant_industry = secondary_merchant_industry;
    }

    public String getStore_name() {
        return store_name;
    }

    public void setStore_name(String store_name) {
        this.store_name = store_name;
    }

    public String getStore_id() {
        return store_id;
    }

    public void setStore_id(String store_id) {
        this.store_id = store_id;
    }

    public String getSys_service_provider_id() {
        return sys_service_provider_id;
    }

    public void setSys_service_provider_id(String sys_service_provider_id) {
        this.sys_service_provider_id = sys_service_provider_id;
    }

    public String getTerminal_id() {
        return terminal_id;
    }

    public void setTerminal_id(String terminal_id) {
        this.terminal_id = terminal_id;
    }
}
