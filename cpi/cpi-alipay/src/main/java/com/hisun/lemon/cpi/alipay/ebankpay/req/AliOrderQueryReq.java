package com.hisun.lemon.cpi.alipay.ebankpay.req;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;

/**
 * 订单查询请求报文
 * 
 * <AUTHOR>
 *
 */
//@XmlRootElement(name = "request")
//@XmlAccessorType(XmlAccessType.FIELD)
@Plain(form = Plain.Form.QUERY_STRING)
public class AliOrderQueryReq{
	/**
	 * 服务名
	 */
	@Item
	@NotNull
	@SignValue
	private String service;

	/**
	 * 商务合作号
	 */
	@Item
	@NotNull
	@SignValue
	private String partner;

	/**
	 * 请求参数编码
	 */
	@Item
	@SignValue
	private String _input_charset;

	/**
	 * 签名
	 */
	@Sign(alias = "sign",place= Sign.Place.FORM,signauteClass="com.hisun.lemon.cpi.alipay.ebankpay.util.AlipaySignature")
	@NotNull
	private String sign;

	/**
	 * 签名类型
	 */
	@Item
	@NotNull
	@SignValue
	private String sign_type;
	
	/**
	 * 合作伙伴流水号
	 */
	@Item(alias="partner_trans_id")
	@NotNull
	private String partner_trans_id;

	/**
	 * 支付宝流水号
	 */
	@Item(alias="alipay_trans_id")
	private String alipay_trans_id;


	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getPartner() {
		return partner;
	}

	public void setPartner(String partner) {
		this.partner = partner;
	}

	public String get_input_charset() {
		return _input_charset;
	}

	public void set_input_charset(String _input_charset) {
		this._input_charset = _input_charset;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getSign_type() {
		return sign_type;
	}

	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	public String getPartner_trans_id() {
		return partner_trans_id;
	}

	public void setPartner_trans_id(String partner_trans_id) {
		this.partner_trans_id = partner_trans_id;
	}

	public String getAlipay_trans_id() {
		return alipay_trans_id;
	}

	public void setAlipay_trans_id(String alipay_trans_id) {
		this.alipay_trans_id = alipay_trans_id;
	}
}
