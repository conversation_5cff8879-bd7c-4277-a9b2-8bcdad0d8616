package com.hisun.lemon.cpi.alipay.ebankpay.req;

/**
 * 下单回调通知报文
 */
public class AlipayOrderNotifyReq {

	/**
	 * 通知时间
	 */
	private String notify_time;

	/**
	 * 通知类型
	 */
	private String notify_type;

	/**
	 * 通知id
	 */
	private String notify_id;

	/**
	 * 签名类型
	 */
	private String sign_type;

	/**
	 * 签名
	 */
	private String sign;

	/**
	 * 通知操作类型
	 */
	private String notify_action_type;
	private String out_trade_no;
	private String subject;
	private String trade_no;
	private String trade_status;
	private String gmt_create;
	private String gmt_payment;
	private String seller_email;
	private String buyer_email;
	private String seller_id;
	private String buyer_id;
	private String price;
	private String quantity;
	private String total_fee;
	private String body;
	private String refund_fee;
	private String out_biz_no;
	private String paytools_pay_amount;
	private String extra_common_param;
	private String m_discount_forex_amount;
	
	public String getNotify_time() {
		return notify_time;
	}

	public void setNotify_time(String notify_time) {
		this.notify_time = notify_time;
	}

	public String getNotify_type() {
		return notify_type;
	}

	public void setNotify_type(String notify_type) {
		this.notify_type = notify_type;
	}

	public String getNotify_id() {
		return notify_id;
	}

	public void setNotify_id(String notify_id) {
		this.notify_id = notify_id;
	}

	public String getSign_type() {
		return sign_type;
	}

	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getNotify_action_type() {
		return notify_action_type;
	}

	public void setNotify_action_type(String notify_action_type) {
		this.notify_action_type = notify_action_type;
	}

	public String getOut_trade_no() {
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getTrade_no() {
		return trade_no;
	}

	public void setTrade_no(String trade_no) {
		this.trade_no = trade_no;
	}

	public String getTrade_status() {
		return trade_status;
	}

	public void setTrade_status(String trade_status) {
		this.trade_status = trade_status;
	}

	public String getGmt_create() {
		return gmt_create;
	}

	public void setGmt_create(String gmt_create) {
		this.gmt_create = gmt_create;
	}

	public String getGmt_payment() {
		return gmt_payment;
	}

	public void setGmt_payment(String gmt_payment) {
		this.gmt_payment = gmt_payment;
	}

	public String getSeller_email() {
		return seller_email;
	}

	public void setSeller_email(String seller_email) {
		this.seller_email = seller_email;
	}

	public String getBuyer_email() {
		return buyer_email;
	}

	public void setBuyer_email(String buyer_email) {
		this.buyer_email = buyer_email;
	}

	public String getSeller_id() {
		return seller_id;
	}

	public void setSeller_id(String seller_id) {
		this.seller_id = seller_id;
	}

	public String getBuyer_id() {
		return buyer_id;
	}

	public void setBuyer_id(String buyer_id) {
		this.buyer_id = buyer_id;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getQuantity() {
		return quantity;
	}

	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}

	public String getTotal_fee() {
		return total_fee;
	}

	public void setTotal_fee(String total_fee) {
		this.total_fee = total_fee;
	}

	public String getBody() {
		return body;
	}

	public void setBody(String body) {
		this.body = body;
	}

	public String getRefund_fee() {
		return refund_fee;
	}

	public void setRefund_fee(String refund_fee) {
		this.refund_fee = refund_fee;
	}

	public String getOut_biz_no() {
		return out_biz_no;
	}

	public void setOut_biz_no(String out_biz_no) {
		this.out_biz_no = out_biz_no;
	}

	public String getPaytools_pay_amount() {
		return paytools_pay_amount;
	}

	public void setPaytools_pay_amount(String paytools_pay_amount) {
		this.paytools_pay_amount = paytools_pay_amount;
	}

	public String getExtra_common_param() {
		return extra_common_param;
	}

	public void setExtra_common_param(String extra_common_param) {
		this.extra_common_param = extra_common_param;
	}

	public String getM_discount_forex_amount() {
		return m_discount_forex_amount;
	}

	public void setM_discount_forex_amount(String m_discount_forex_amount) {
		this.m_discount_forex_amount = m_discount_forex_amount;
	}

	@Override
	public String toString() {
		return "AlipayOrderNotifyReq [notify_time=" + notify_time + ", notify_type=" + notify_type + ", notify_id="
				+ notify_id + ", sign_type=" + sign_type + ", sign=" + sign + ", notify_action_type="
				+ notify_action_type + ", out_trade_no=" + out_trade_no + ", subject=" + subject + ", trade_no="
				+ trade_no + ", trade_status=" + trade_status + ", gmt_create=" + gmt_create + ", gmt_payment="
				+ gmt_payment + ", seller_email=" + seller_email + ", buyer_email=" + buyer_email + ", seller_id="
				+ seller_id + ", buyer_id=" + buyer_id + ", price=" + price + ", quantity=" + quantity + ", total_fee="
				+ total_fee + ", body=" + body + ", refund_fee=" + refund_fee + ", out_biz_no=" + out_biz_no
				+ ", paytools_pay_amount=" + paytools_pay_amount + ", extra_common_param=" + extra_common_param
				+ ", m_discount_forex_amount=" + m_discount_forex_amount + ", getNotify_time()=" + getNotify_time()
				+ ", getNotify_type()=" + getNotify_type() + ", getNotify_id()=" + getNotify_id() + ", getSign_type()="
				+ getSign_type() + ", getSign()=" + getSign() + ", getNotify_action_type()=" + getNotify_action_type()
				+ ", getOut_trade_no()=" + getOut_trade_no() + ", getSubject()=" + getSubject() + ", getTrade_no()="
				+ getTrade_no() + ", getTrade_status()=" + getTrade_status() + ", getGmt_create()=" + getGmt_create()
				+ ", getGmt_payment()=" + getGmt_payment() + ", getSeller_email()=" + getSeller_email()
				+ ", getBuyer_email()=" + getBuyer_email() + ", getSeller_id()=" + getSeller_id() + ", getBuyer_id()="
				+ getBuyer_id() + ", getPrice()=" + getPrice() + ", getQuantity()=" + getQuantity()
				+ ", getTotal_fee()=" + getTotal_fee() + ", getBody()=" + getBody() + ", getRefund_fee()="
				+ getRefund_fee() + ", getOut_biz_no()=" + getOut_biz_no() + ", getPaytools_pay_amount()="
				+ getPaytools_pay_amount() + ", getExtra_common_param()=" + getExtra_common_param()
				+ ", getM_discount_forex_amount()=" + getM_discount_forex_amount() + ", getClass()=" + getClass()
				+ ", hashCode()=" + hashCode() + ", toString()=" + super.toString() + "]";
	}
}
