package com.hisun.lemon.cpi.alipay.ebankpay;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hisun.channel.common.core.Signature;
import com.hisun.channel.common.lifecycle.LifecycleBase;
import com.hisun.channel.common.lifecycle.LifecycleState;
import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.core.MessageContext;

public class AlipaySecurity extends LifecycleBase implements Signature {
	private final static String appKey = "Seatel0920Mpay9872Seatel2017Mpay";

	@Override
	public String sign(String signData) {
		
		String md5String = "";
		try {
			Object object = MessageContext.getCurrentContext().getRequest().getTarget();
			md5String=pubSign(object);
		} catch (Exception e) {
			logger.error("WEIXINSecurity.sign 加密异常：", e);
		}
		return md5String;
	}

	/**
	 * 验签
	 * @param obj 验签对象
	 * @param sign 签名
	 * @return
	 */
	public static boolean isVerify(Object obj, String sign) {
		boolean bool = false;
		try {
			bool = StringUtils.contains(pubSign(obj), sign);
		} catch (Exception e) {
			logger.error("WEIXINSecurity.isVerify 验签异常：", e);
		}
		return bool;
	}

	/**
	 * 加签公共方法
	 * 
	 * @return
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws UnsupportedEncodingException
	 */
	private static String pubSign(Object object) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		List<String> fieldNames = new ArrayList<String>();
		Field[] fields = object.getClass().getDeclaredFields();
		for (Field field : fields) {
			String fieName = field.getName();
			boolean accessFlag = field.isAccessible();
			field.setAccessible(true);
			Object obj = field.get(object);
			if (obj != null) {
				if (fieName != "sign") {
					map.put(fieName, obj.toString());
					fieldNames.add(fieName);
				} 
			}
			field.setAccessible(accessFlag);
		}

		StringBuilder paramStringBuffer = new StringBuilder();
		Collections.sort(fieldNames);
		for (int i = 0; i < fieldNames.size(); i++) {
			String key = fieldNames.get(i);
			String value = map.get(key);
			paramStringBuffer.append("&").append(key).append("=").append(value);
		}
		// 去掉请求字符串末尾的最后一个&号
		if (paramStringBuffer.indexOf("&", 0) == 0) {
			paramStringBuffer.deleteCharAt(0);
		}
		String signString=paramStringBuffer.append("&key=") + appKey;
		System.out.println("加密字符串:"+signString);
		
		return getMD5ofByte(signString.getBytes("UTF-8")).toUpperCase();
	}

	@Override
	public boolean verify(String verifyData, String signedData) {

		return true;
	}

	@Override
	protected void doInit() {
		// TODO Auto-generated method stub

	}

	@Override
	protected void doStart() {
		this.setState(LifecycleState.STARTING);
	}

	@Override
	protected void doStop() {
		this.setState(LifecycleState.STOPPING);
	}

	@Override
	protected void doDestroy() {
		// TODO Auto-generated method stub

	}

	/**
	 * 
	 * 获得MD5加密密码的方法
	 */
	private static String getMD5ofByte(byte[] data) {
		String origMD5 = null;
		try {
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			byte[] result = md5.digest(data);
			origMD5 = byteArray2HexStr(result);
		} catch (Exception e) {
			logger.error("WEIXINSecurity.getMD5ofByte md5加密异常：", e);
		}
		return origMD5;
	}

	/**
	 * 
	 * 处理字节数组得到MD5密码的方法
	 */
	private static String byteArray2HexStr(byte[] bs) {
		StringBuffer sb = new StringBuffer();
		for (byte b : bs) {
			sb.append(byte2HexStr(b));
		}
		return sb.toString();
	}

	/**
	 * 
	 * 字节标准移位转十六进制方法
	 */
	private static String byte2HexStr(byte b) {
		String hexStr = null;
		int n = b;
		if (n < 0) {
			// 定义移位算法
			n = b & 0x7F + 128;
		}
		hexStr = Integer.toHexString(n / 16) + Integer.toHexString(n % 16);
		return hexStr.toUpperCase();
	}

}
