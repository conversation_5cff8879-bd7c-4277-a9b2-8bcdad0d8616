package com.hisun.lemon.cpi.alipay.ebankpay;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * User : Rui
 * Date : 2017/8/29
 * Time : 11:24
 **/
@Component
@ConfigurationProperties(prefix = "aliPay")
@PropertySource("classpath:config/ebankpay-alipay.properties")
public class AlipayProperties {
    private String partner;
    // 商户的私钥
    private String merKey;
    // 字符编码格式 目前支持 gbk 或 utf-8
    private String inputCharset;

    // 签名方式 不需修改
    private String signType;

    private String apiUrl;
    private String fail;
    private String success;
    private String hmacsha256;

    private String fieldSign;
    private String fieldSignType;
    private String notifyUrl;

    private String userPreCreateService;
    private String mercPreCreateService;
    private String tradeQueryService;
    private String tradeCancelService;
    private String tradeRefundService;

    private String sftpIp;
    private int sftpPort;
    private int sftpTimeout;
    private String sftpFilePath;
    private String userName;
    private String passwd;

    private String settleFilePath;


    public String getPartner() {
        return partner;
    }

    public void setPartner(String partner) {
        this.partner = partner;
    }

    public String getMerKey() {
        return merKey;
    }

    public void setMerKey(String merKey) {
        this.merKey = merKey;
    }

    public String getInputCharset() {
        return inputCharset;
    }

    public void setInputCharset(String inputCharset) {
        this.inputCharset = inputCharset;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getFail() {
        return fail;
    }

    public void setFail(String fail) {
        this.fail = fail;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getHmacsha256() {
        return hmacsha256;
    }

    public void setHmacsha256(String hmacsha256) {
        this.hmacsha256 = hmacsha256;
    }

    public String getFieldSign() {
        return fieldSign;
    }

    public void setFieldSign(String fieldSign) {
        this.fieldSign = fieldSign;
    }

    public String getFieldSignType() {
        return fieldSignType;
    }

    public void setFieldSignType(String fieldSignType) {
        this.fieldSignType = fieldSignType;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getUserPreCreateService() {
        return userPreCreateService;
    }

    public void setUserPreCreateService(String userPreCreateService) {
        this.userPreCreateService = userPreCreateService;
    }

    public String getMercPreCreateService() {
        return mercPreCreateService;
    }

    public void setMercPreCreateService(String mercPreCreateService) {
        this.mercPreCreateService = mercPreCreateService;
    }

    public String getTradeQueryService() {
        return tradeQueryService;
    }

    public void setTradeQueryService(String tradeQueryService) {
        this.tradeQueryService = tradeQueryService;
    }

    public String getTradeCancelService() {
        return tradeCancelService;
    }

    public void setTradeCancelService(String tradeCancelService) {
        this.tradeCancelService = tradeCancelService;
    }

    public String getTradeRefundService() {
        return tradeRefundService;
    }

    public void setTradeRefundService(String tradeRefundService) {
        this.tradeRefundService = tradeRefundService;
    }

    public String getSftpIp() {
        return sftpIp;
    }

    public void setSftpIp(String sftpIp) {
        this.sftpIp = sftpIp;
    }

    public int getSftpPort() {
        return sftpPort;
    }

    public void setSftpPort(int sftpPort) {
        this.sftpPort = sftpPort;
    }

    public int getSftpTimeout() {
        return sftpTimeout;
    }

    public void setSftpTimeout(int sftpTimeout) {
        this.sftpTimeout = sftpTimeout;
    }

    public String getSftpFilePath() {
        return sftpFilePath;
    }

    public void setSftpFilePath(String sftpFilePath) {
        this.sftpFilePath = sftpFilePath;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    public String getSettleFilePath() {
        return settleFilePath;
    }

    public void setSettleFilePath(String settleFilePath) {
        this.settleFilePath = settleFilePath;
    }
}
