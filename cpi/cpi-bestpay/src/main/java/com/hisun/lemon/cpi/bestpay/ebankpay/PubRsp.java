package com.hisun.lemon.cpi.bestpay.ebankpay;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;


/**
 * 公共响应报文
 * <AUTHOR>
 *
 */
@Plain
public class PubRsp {
	
	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	@Item(before="merchantId=",separator="&")
	private String merchantId;
	
	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	@Item(before="resultCode=",separator="&")
	private String resultCode;
	
	/**
	 * 返回码信息提示
	 */
	@Item(before="resultMessage=",separator="&")
	private String resultMessage;

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	public String getMerchantId() {
		return merchantId;
	}

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}	

	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	public String getResultCode() {
		return resultCode;
	}

	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	/**
	 * 返回码信息提示
	 */
	public String getResultMessage() {
		return resultMessage;
	}

	/**
	 * 返回码信息提示
	 */
	public void setResultMessage(String resultMessage) {
		this.resultMessage = resultMessage;
	}

}
