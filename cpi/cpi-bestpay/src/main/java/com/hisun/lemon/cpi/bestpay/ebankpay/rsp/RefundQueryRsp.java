package com.hisun.lemon.cpi.bestpay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.OrderByLetter;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.Plain.Form;
import com.hisun.lemon.cpi.bestpay.ebankpay.PubRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderCcy;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderSts;

/**
 * 退款订单查询请求报文
 * <AUTHOR>
 *
 */
@Plain(form=Form.STANDARD) 
@OrderByLetter
public class RefundQueryRsp{

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	@Item
	@VerifyValue
	private String merchantId;
	
	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	@Item
	@VerifyValue
	private String resultCode;
	
	/**
	 * 返回码信息提示
	 */
	@Item
	@VerifyValue
	private String resultMessage;

	/**
     * 签名方式:固定值：MD5
     */
	@Item(alias="ReqSignTyp")
	@VerifyValue
	private String reqSignTyp;

	/**
	 * 服务器签名:存放MD5加签后数据
	 */
	@Verify
	private String serverSign;

	/**
	 * 退款结果:
	 * U - 预登记
	 * P – 下单成功，处理中
	 * S - 成功
	 * F - 失败
	 * R - 全额退款
	 */
	@Item
	@VerifyValue
	private String refundSts;
	
	/**
	 * 退款流水号:由商户平台提供，原值返回
	 */
	@Item
	@VerifyValue
	private String refundReqNo;
	
	/**
	 * 原商户订单号
	 */
	@Item
	@VerifyValue
	private String oldOrderId;
	
	/**
	 * 退款币种
	 */
	@Item
	@VerifyValue
	private String refundCcy;
	
	/**
	 * 退款金额
	 */
	@Item
	@VerifyValue
	private String refundAmt;
	
	/**
	 * 退款本币金额
	 */
	@Item
	@VerifyValue
	private String refundCcyAmt;
	
	/**
	 * 汇率
	 */
	@Item
	@VerifyValue
	private String refundRat;

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	public String getMerchantId() {
		return merchantId;
	}

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}	

	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	public String getResultCode() {
		return resultCode;
	}

	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	/**
	 * 返回码信息提示
	 */
	public String getResultMessage() {
		return resultMessage;
	}

	/**
	 * 返回码信息提示
	 */
	public void setResultMessage(String resultMessage) {
		this.resultMessage = resultMessage;
	}
	
	/**
     * 签名方式:固定值：MD5
     */
	public String getReqSignTyp() {
		return reqSignTyp;
	}

	/**
     * 签名方式:固定值：MD5
     */
	public void setReqSignTyp(String reqSignTyp) {
		this.reqSignTyp = reqSignTyp;
	}
	
	public String getServerSign() {
		return serverSign;
	}

	public void setServerSign(String serverSign) {
		this.serverSign = serverSign;
	}
	
	/**
	 * 退款结果:
	 * U - 预登记
	 * P – 下单成功，处理中
	 * S - 成功
	 * F - 失败
	 * R - 全额退款
	 */
	public EnumOrderSts getRefundSts() {
		return EnumOrderSts.getEnum(refundSts);
	}

	/**
	 * 退款结果:
	 * U - 预登记
	 * P – 下单成功，处理中
	 * S - 成功
	 * F - 失败
	 * R - 全额退款
	 */
	public void setRefundSts(String refundSts) {
		this.refundSts = refundSts;
	}

	/**
	 * 退款流水号:由商户平台提供，原值返回
	 */
	public String getRefundReqNo() {
		return refundReqNo;
	}

	/**
	 * 退款流水号:由商户平台提供，原值返回
	 */
	public void setRefundReqNo(String refundReqNo) {
		this.refundReqNo = refundReqNo;
	}

	/**
	 * 原商户订单号
	 */
	public String getOldOrderId() {
		return oldOrderId;
	}

	/**
	 * 原商户订单号
	 */
	public void setOldOrderId(String oldOrderId) {
		this.oldOrderId = oldOrderId;
	}

	/**
	 * 退款币种
	 */
	public EnumOrderCcy getRefundCcy() {
		return EnumOrderCcy.getEnum(refundCcy);
	}

	/**
	 * 退款币种
	 */
	public void setRefundCcy(String refundCcy) {
		this.refundCcy = refundCcy;
	}

	/**
	 * 退款金额
	 */
	public String getRefundAmt() {
		return refundAmt;
	}

	/**
	 * 退款金额
	 */
	public void setRefundAmt(String refundAmt) {
		this.refundAmt = refundAmt;
	}

	/**
	 * 退款本币金额
	 */
	public String getRefundCcyAmt() {
		return refundCcyAmt;
	}

	/**
	 * 退款本币金额
	 */
	public void setRefundCcyAmt(String refundCcyAmt) {
		this.refundCcyAmt = refundCcyAmt;
	}

	/**
	 * 汇率
	 */
	public String getRefundRat() {
		return refundRat;
	}

	/**
	 * 汇率
	 */
	public void setRefundRat(String refundRat) {
		this.refundRat = refundRat;
	}
}
