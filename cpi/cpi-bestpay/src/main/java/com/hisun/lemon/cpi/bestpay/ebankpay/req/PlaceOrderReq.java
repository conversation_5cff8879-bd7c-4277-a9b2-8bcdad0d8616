package com.hisun.lemon.cpi.bestpay.ebankpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.OrderByLetter;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Sign.Place;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderCcy;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumService;


/**
 * 下单请求报文
 * <AUTHOR>
 *
 */ 
@Validated
@Plain
@OrderByLetter
public class PlaceOrderReq {
	
	/**
	 * 商户证书
	 */
	@Item(before="merchantCert=",separator="&",expr="@sign")
	private String merchantCert;

	/**
	 * 商户签名
	 */
	@Item(before="merchantSign=",separator="&",expr="@sign")
	private String merchantSign;
	
	@Sign(place=Place.IGNORE)
	private String sign;

	/**
	 * 字符集 默认00-GBK
	 */
	@Item(before="charset=",separator="&")
	@NotNull
	@SignValue
	private String charset;

	/**
	 * 接口版本 固定值:1.0
	 */
	@Item(before="version=",separator="&")
	@NotNull
	@SignValue
	private String version;

	/**
	 * 签名方式 固定值:MD5
	 */
	@Item(before="signType=",separator="&")
	@NotNull
	@SignValue
	private String signType;

	/**
	 * 交易码 固定值:forAppRefund
	 */
	@Item(before="service=",separator="&")
	@NotNull
	@SignValue
	private String service;

	/**
	 * 商户代码
	 */
	@Item(before="merchantId=",separator="&")
	@NotNull
	@SignValue
	private String merchantId;

	/**
	 * 请求时间 格式:YYYYMMDDHHmmss
	 */
	@Item(before="requestId=",separator="&")
	@NotNull
	@SignValue
	private String requestId;

	/**
	 * 商户的订单号
	 */
	@Item(before="orderId=",separator="&")
	@NotNull
	@SignValue
	private String orderId;
	
	/**
	 * 订单请求流水号
	 */
	@Item(before="jrnNo=",separator="&")
	@NotNull
	@SignValue
	private String jrnNo;
	
	/**
	 * 订单金额
	 */
	@Item(before="orderAmt=",separator="&")
	@NotNull
	@SignValue
	private String orderAmt;
	
	/**
	 * 订单币种
	 */
	@Item(before="orderCcy=",separator="&")
	@NotNull
	@SignValue
	private String orderCcy;
	
	/**
	 * 下单商户门店号
	 */
	@Item(before="storeId=",separator="&")
	@SignValue
	private String storeId;
	
	/**
	 * 付款条形码信息
	 */
	@Item(before="barcode=",separator="&")
	@SignValue
	private String barcode;
	
	/**
	 * 商品代码
	 */
	@Item(before="productId=",separator="&")
	@NotNull
	@SignValue
	private String productId;
	
	/**
	 * 商品描述
	 */
	@Item(before="productDesc=",separator="&")
	@NotNull
	@SignValue
	private String productDesc;
	
	/**
	 * 翼支付登录手机号
	 */
	@Item(before="loginNo=",separator="&")
	@NotNull
	@SignValue
	private String loginNo;
	
	/**
	 * 省市代码
	 */
	@Item(before="proviceCode=",separator="&")
	@SignValue
	private String proviceCode;
	
	/**
	 * 城市代码
	 */
	@Item(before="cityCode=",separator="&")
	@SignValue
	private String cityCode;
	
	/**
	 * 商户后台通知URL
	 */
	@Item(before="bankUrl=",separator="&")
	@SignValue
	@NotNull
	private String bankUrl;
	
	/**
	 * 接口编码
	 */
	@Item(before="serviceCode=",separator="&")
	@SignValue
	private String serviceCode;
	
	/**
	 * 附加信息
	 */
	@Item(before="attach=",separator="&")
	@SignValue
	private String attach;

	/**
	 * 接口编码
	 */
	public String getServiceCode() {
		if(StringUtils.isEmpty(serviceCode)){
			serviceCode="APP";
		}
		return serviceCode;
	}

	/**
	 * 接口编码
	 */
	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	/**
	 * 商户证书
	 */
	public String getMerchantCert() {
		return merchantCert;
	}

	/**
	 * 商户证书
	 */
	public void setMerchantCert(String merchantCert) {
		this.merchantCert = merchantCert;
	}

	/**
	 * 商户签名
	 */
	public String getMerchantSign() {
		return merchantSign;
	}

	/**
	 * 商户签名
	 */
	public void setMerchantSign(String merchantSign) {
		this.merchantSign = merchantSign;
	}
	
	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}
	
	/**
	 * 字符集 默认00-GBK
	 */
	public String getCharset() {
		if(StringUtils.isEmpty(charset)){
			charset="02";
		}
		return charset;
	}

	/**
	 * 字符集 默认00-GBK
	 */
	public void setCharset(String charset) {
		this.charset = charset;
	}

	/**
	 * 接口版本 固定值:1.0
	 */
	public String getVersion() {
		if(StringUtils.isEmpty(version)){
			version="1.0";
		}
		return version;
	}

	/**
	 * 接口版本 固定值:1.0
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * 签名方式 固定值:MD5
	 */
	public String getSignType() {
		if(StringUtils.isEmpty(signType)){
			signType="MD5";
		}
		return signType;
	}

	/**
	 * 签名方式 固定值:MD5
	 */
	public void setSignType(String signType) {
		this.signType = signType;
	}

	/**
	 * 交易码 固定值:forAppRefund
	 */
	public String getService() {
		return service;
	}

	/**
	 * 交易码 固定值:forAppRefund
	 */
	public void setService(EnumService enumService) {
		this.service = enumService.getCode();
	}

	/**
	 * 商户代码
	 */
	public String getMerchantId() {
		return merchantId;
	}

	/**
	 * 商户代码
	 */
	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	/**
	 * 请求时间 格式:YYYYMMDD
	 */
	public String getRequestId() {
		return requestId;
	}

	/**
	 * 请求时间 格式:YYYYMMDD
	 */
	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}
	
	/**
	 * 商户的订单号
	 */
	public String getOrderId() {
		return orderId;
	}

	/**
	 * 商户的订单号
	 */
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	/**
	 * 订单请求流水号
	 */
	public String getJrnNo() {
		return jrnNo;
	}

	/**
	 * 订单请求流水号
	 */
	public void setJrnNo(String jrnNo) {
		this.jrnNo = jrnNo;
	}

	/**
	 * 订单金额
	 */
	public String getOrderAmt() {
		return orderAmt;
	}

	/**
	 * 订单金额
	 */
	public void setOrderAmt(String orderAmt) {
		this.orderAmt = orderAmt;
	}

	/**
	 * 订单币种
	 */
	public String getOrderCcy() {
		return orderCcy;
	}

	/**
	 * 订单币种
	 */
	public void setOrderCcy(EnumOrderCcy enumOrderCcy) {
		this.orderCcy = enumOrderCcy.name();
	}

	/**
	 * 下单商户门店号
	 */
	public String getStoreId() {
		return storeId;
	}

	/**
	 * 下单商户门店号
	 */
	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	/**
	 * 付款条形码信息
	 */
	public String getBarcode() {
		return barcode;
	}

	/**
	 * 付款条形码信息
	 */
	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	/**
	 * 商品代码 固定:04
	 */
	public String getProductId() {
		if(StringUtils.isEmpty(productId)){
			productId="04";
		}
		return productId;
	}

	/**
	 * 商品代码
	 */
	public void setProductId(String productId) {
		this.productId = productId;
	}

	/**
	 * 商品描述
	 */
	public String getProductDesc() {
		return productDesc;
	}

	/**
	 * 商品描述
	 */
	public void setProductDesc(String productDesc) {
		this.productDesc = productDesc;
	}

	/**
	 * 翼支付登录手机号
	 */
	public String getLoginNo() {
		return loginNo;
	}

	/**
	 * 翼支付登录手机号
	 */
	public void setLoginNo(String loginNo) {
		this.loginNo = loginNo;
	}

	/**
	 * 省市代码
	 */
	public String getProviceCode() {
		return proviceCode;
	}

	/**
	 * 省市代码
	 */
	public void setProviceCode(String proviceCode) {
		this.proviceCode = proviceCode;
	}

	/**
	 * 城市代码
	 */
	public String getCityCode() {
		return cityCode;
	}

	/**
	 * 城市代码
	 */
	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	/**
	 * 商户后台通知URL
	 */
	public String getBankUrl() {
		return bankUrl;
	}

	/**
	 * 商户后台通知URL
	 */
	public void setBankUrl(String bankUrl) {
		this.bankUrl = bankUrl;
	}

	/**
	 * 附加信息
	 */
	public String getAttach() {
		return attach;
	}

	/**
	 * 附加信息
	 */
	public void setAttach(String attach) {
		this.attach = attach;
	}
	
	

}
