package com.hisun.lemon.cpi.bestpay.ebankpay;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;


@Component(value = "bestpayProperties")
@ConfigurationProperties(prefix = "bestpay")
@PropertySource("classpath:config/ebankpay-bestpay.properties")
public class BestpayProperties {
    /**
     * sftp服务器远程ip
     */
    private String remoteIp;

    /**
     * sftp服务器远程port
     */
    private String remotePort;

    /**
     * sftp服务器连接超时时间
     */
    private String remoteTimeOut;

    /**
     * sftp服务器远程文件所在目录
     */
    private String remoteFilePath;

    /**
     * sftp服务器登录用户
     */
    private String remoteUsername;

    /**
     * sftp服务器登录密码
     */
    private String remotePassword;

    /**
     * 下载文件存放路径
     */
    private String localFilePath;

    /**
     * 字符集
     */
    private String charset;

    /**
     * 版本号
     */
    private String version;

    /**
     * 签名方式
     */
    private String signType;

    /**
     * 商户代码，翼支付分配给支付平台唯一编号
     */
    private String merchantId;

    /**
     * 下单后台通知URL
     */
    private String fundBankUrl;

    /**
     * 退款后台通知URL
     */
    private String refundBankUrl;

    private String appKey;

    private String productId;

    private String productDesc;

    private String proviceCode;

    private String cityCode;

    private String subject;

    private String merchantPwd;

    private String busiType;

    private String otherFlow;

    private String isShowExchangeRate;

    private String service;

    private String storeId;

    private String mblNo;

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public String getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(String remotePort) {
        this.remotePort = remotePort;
    }

    public String getRemoteTimeOut() {
        return remoteTimeOut;
    }

    public void setRemoteTimeOut(String remoteTimeOut) {
        this.remoteTimeOut = remoteTimeOut;
    }

    public String getRemoteFilePath() {
        return remoteFilePath;
    }

    public void setRemoteFilePath(String remoteFilePath) {
        this.remoteFilePath = remoteFilePath;
    }

    public String getRemoteUsername() {
        return remoteUsername;
    }

    public void setRemoteUsername(String remoteUsername) {
        this.remoteUsername = remoteUsername;
    }

    public String getRemotePassword() {
        return remotePassword;
    }

    public void setRemotePassword(String remotePassword) {
        this.remotePassword = remotePassword;
    }

    public String getLocalFilePath() {
        return localFilePath;
    }

    public void setLocalFilePath(String localFilePath) {
        this.localFilePath = localFilePath;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getFundBankUrl() {
        return fundBankUrl;
    }

    public void setFundBankUrl(String fundBankUrl) {
        this.fundBankUrl = fundBankUrl;
    }

    public String getRefundBankUrl() {
        return refundBankUrl;
    }

    public void setRefundBankUrl(String refundBankUrl) {
        this.refundBankUrl = refundBankUrl;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductDesc() {
        return productDesc;
    }

    public void setProductDesc(String productDesc) {
        this.productDesc = productDesc;
    }

    public String getProviceCode() {
        return proviceCode;
    }

    public void setProviceCode(String proviceCode) {
        this.proviceCode = proviceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMerchantPwd() {
        return merchantPwd;
    }

    public void setMerchantPwd(String merchantPwd) {
        this.merchantPwd = merchantPwd;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getOtherFlow() {
        return otherFlow;
    }

    public void setOtherFlow(String otherFlow) {
        this.otherFlow = otherFlow;
    }

    public String getIsShowExchangeRate() {
        return isShowExchangeRate;
    }

    public void setIsShowExchangeRate(String isShowExchangeRate) {
        this.isShowExchangeRate = isShowExchangeRate;
    }
}
