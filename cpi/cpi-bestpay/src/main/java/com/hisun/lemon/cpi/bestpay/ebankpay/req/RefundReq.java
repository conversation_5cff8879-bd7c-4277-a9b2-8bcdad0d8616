package com.hisun.lemon.cpi.bestpay.ebankpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.OrderByLetter;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Sign.Place;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderCcy;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumService;

/**
 * 退款请求报文
 * <AUTHOR>
 *
 */
@Validated
@Plain
@OrderByLetter 
public class RefundReq{

	/**
	 * 商户证书
	 */
	@Item(before="merchantCert=",separator="&",expr="@sign")
	private String merchantCert;

	/**
	 * 商户签名
	 */
	@Item(before="merchantSign=",separator="&",expr="@sign")
	private String merchantSign;
	
	@Sign(place=Place.IGNORE)
	private String sign;

	/**
	 * 字符集 默认00-GBK
	 */
	@Item(before="charset=",separator="&")
	@NotNull
	@SignValue
	private String charset;

	/**
	 * 接口版本 固定值:1.0
	 */
	@Item(before="version=",separator="&")
	@NotNull
	@SignValue
	private String version;

	/**
	 * 签名方式 固定值:MD5
	 */
	@Item(before="signType=",separator="&")
	@NotNull
	@SignValue
	private String signType;

	/**
	 * 交易码 固定值:forAppRefund
	 */
	@Item(before="service=",separator="&")
	@NotNull
	@SignValue
	private String service;

	/**
	 * 商户代码
	 */
	@Item(before="merchantId=",separator="&")
	@NotNull
	@SignValue
	private String merchantId;

	/**
	 * 请求时间 格式:YYYYMMDDHHmmss
	 */
	@Item(before="requestId=",separator="&")
	@NotNull
	@SignValue
	private String requestId;

	/**
	 * 商户的退款流水号，商户系统保证唯一
	 */
	@Item(before="refundReqNo=",separator="&")
	@NotNull
	@SignValue
	private String refundReqNo;
	
	/**
	 * 商户原订单请求流水号，与原流水号一致
	 */
	@Item(before="oldJrnNo=",separator="&")
	@NotNull
	@SignValue
	private String oldJrnNo;
	
	/**
	 * 原扣款订单号，与原订单号一致
	 */
	@Item(before="oldOrderNo=",separator="&")
	@NotNull
	@SignValue
	private String oldOrderNo;
	
	/**
	 * 订单退款币种
	 */
	@Item(before="refundCcy=",separator="&")
	@NotNull
	@SignValue
	private String refundCcy;
	
	/**
	 * 退款外币金额，以元为单位
	 */
	@Item(before="refundAmt=",separator="&")
	@SignValue
	private String refundAmt;
	
	/**
	 * 退款渠道，固定值：APP
	 */
	@Item(before="serviceCode=",separator="&")
	@SignValue
	private String serviceCode;
	
	/**
	 * 后台通知地址
	 */
	@Item(before="bgUrl=",separator="&")
	@SignValue
	private String bgUrl;
	
	/**
	 * 商户证书
	 */
	public String getMerchantCert() {
		return merchantCert;
	}

	/**
	 * 商户证书
	 */
	public void setMerchantCert(String merchantCert) {
		this.merchantCert = merchantCert;
	}

	/**
	 * 商户签名
	 */
	public String getMerchantSign() {
		return merchantSign;
	}

	/**
	 * 商户签名
	 */
	public void setMerchantSign(String merchantSign) {
		this.merchantSign = merchantSign;
	}
	
	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 字符集 默认00-GBK
	 */
	public String getCharset() {
		if(StringUtils.isEmpty(charset)){
			charset="02";
		}
		return charset;
	}

	/**
	 * 字符集 默认00-GBK
	 */
	public void setCharset(String charset) {
		this.charset = charset;
	}

	/**
	 * 接口版本 固定值:1.0
	 */
	public String getVersion() {
		if(StringUtils.isEmpty(version)){
			version="1.0";
		}
		return version;
	}

	/**
	 * 接口版本 固定值:1.0
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * 签名方式 固定值:MD5
	 */
	public String getSignType() {
		if(StringUtils.isEmpty(signType)){
			signType="MD5";
		}
		return signType;
	}

	/**
	 * 签名方式 固定值:MD5
	 */
	public void setSignType(String signType) {
		this.signType = signType;
	}

	/**
	 * 交易码 固定值:forAppRefund
	 */
	public String getService() {
		return service;
	}

	/**
	 * 交易码 固定值:forAppRefund
	 */
	public void setService(EnumService enumService) {
		this.service = enumService.getCode();
	}

	/**
	 * 商户代码
	 */
	public String getMerchantId() {
		return merchantId;
	}

	/**
	 * 商户代码
	 */
	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	/**
	 * 请求时间 格式:YYYYMMDD
	 */
	public String getRequestId() {
		return requestId;
	}

	/**
	 * 请求时间 格式:YYYYMMDD
	 */
	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}
	
	/**
	 * 商户的退款流水号，商户系统保证唯一
	 */
	public String getRefundReqNo() {
		return refundReqNo;
	}

	/**
	 * 商户的退款流水号，商户系统保证唯一
	 */
	public void setRefundReqNo(String refundReqNo) {
		this.refundReqNo = refundReqNo;
	}

	/**
	 * 商户原订单请求流水号，与原流水号一致
	 */
	public String getOldJrnNo() {
		return oldJrnNo;
	}

	/**
	 * 商户原订单请求流水号，与原流水号一致
	 */
	public void setOldJrnNo(String oldJrnNo) {
		this.oldJrnNo = oldJrnNo;
	}

	/**
	 * 原扣款订单号，与原订单号一致
	 */
	public String getOldOrderNo() {
		return oldOrderNo;
	}

	/**
	 * 原扣款订单号，与原订单号一致
	 */
	public void setOldOrderNo(String oldOrderNo) {
		this.oldOrderNo = oldOrderNo;
	}

	/**
	 * 订单退款币种
	 */
	public String getRefundCcy() {
		return refundCcy;
	}

	/**
	 * 订单退款币种
	 */
	public void setRefundCcy(EnumOrderCcy enumOrderCcy) {
		this.refundCcy = enumOrderCcy.name();
	}

	/**
	 * 退款外币金额，以元为单位
	 */
	public String getRefundAmt() {
		return refundAmt;
	}

	/**
	 * 退款外币金额，以元为单位
	 */
	public void setRefundAmt(String refundAmt) {
		this.refundAmt = refundAmt;
	}

	/**
	 * 退款渠道，固定值：APP
	 */
	public String getServiceCode() {
		if(StringUtils.isEmpty(serviceCode)){
			serviceCode="APP";
		}
		return serviceCode;
	}

	/**
	 * 退款渠道，固定值：APP
	 */
	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	/**
	 * 后台通知地址
	 */
	public String getBgUrl() {
		return bgUrl;
	}

	/**
	 * 后台通知地址
	 */
	public void setBgUrl(String bgUrl) {
		this.bgUrl = bgUrl;
	}
	
}
