package com.hisun.lemon.cpi.bestpay.ebankpay.req;

/**
 * 退款通知对象
 * 
 * <AUTHOR>
 *
 */
public class RefundNotifyReq {

	/**
	 * 翼支付系统流水号
	 */
	private String upTranSeq;

	/**
	 * 商户号
	 */
	private String mercId;

	/**
	 * 处理结果码
	 */
	private String resultCode;

	/**
	 * 处理结果解释码
	 */
	private String resultMessage;

	/**
	 * 原订单请求流水号
	 */
	private String jrnNo;

	/**
	 * 商户原订单号
	 */
	private String orderId;

	/**
	 * 退款流水号
	 */
	private String rfdAplJrn;

	/**
	 * 退款币种
	 */
	private String rfdCcy;

	/**
	 * 退款本币金额
	 */
	private String rfdCcyAmt;

	/**
	 * 退款外币金额
	 */
	private String rfdAmt;

	/**
	 * 验签数据
	 */
	private String sign;

	/**
	 * 签名秘钥
	 */
	private String KEY;

	/**
	 * 翼支付系统流水号
	 */
	public String getUpTranSeq() {
		return upTranSeq;
	}

	/**
	 * 翼支付系统流水号
	 */
	public void setUpTranSeq(String upTranSeq) {
		this.upTranSeq = upTranSeq;
	}

	/**
	 * 商户号
	 */
	public String getMercId() {
		return mercId;
	}

	/**
	 * 商户号
	 */
	public void setMercId(String mercId) {
		this.mercId = mercId;
	}

	/**
	 * 处理结果码
	 */
	public String getResultCode() {
		return resultCode;
	}

	/**
	 * 处理结果码
	 */
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	/**
	 * 处理结果解释码
	 */
	public String getResultMessage() {
		return resultMessage;
	}

	/**
	 * 处理结果解释码
	 */
	public void setResultMessage(String resultMessage) {
		this.resultMessage = resultMessage;
	}

	/**
	 * 原订单请求流水号
	 */
	public String getJrnNo() {
		return jrnNo;
	}

	/**
	 * 原订单请求流水号
	 */
	public void setJrnNo(String jrnNo) {
		this.jrnNo = jrnNo;
	}

	/**
	 * 商户原订单号
	 */
	public String getOrderId() {
		return orderId;
	}

	/**
	 * 商户原订单号
	 */
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	/**
	 * 退款流水号
	 */
	public String getRfdAplJrn() {
		return rfdAplJrn;
	}

	/**
	 * 退款流水号
	 */
	public void setRfdAplJrn(String rfdAplJrn) {
		this.rfdAplJrn = rfdAplJrn;
	}

	/**
	 * 退款币种
	 */
	public String getRfdCcy() {
		return rfdCcy;
	}

	/**
	 * 退款币种
	 */
	public void setRfdCcy(String rfdCcy) {
		this.rfdCcy = rfdCcy;
	}

	/**
	 * 退款本币金额
	 */
	public String getRfdCcyAmt() {
		return rfdCcyAmt;
	}

	/**
	 * 退款本币金额
	 */
	public void setRfdCcyAmt(String rfdCcyAmt) {
		this.rfdCcyAmt = rfdCcyAmt;
	}

	/**
	 * 退款外币金额
	 */
	public String getRfdAmt() {
		return rfdAmt;
	}

	/**
	 * 退款外币金额
	 */
	public void setRfdAmt(String rfdAmt) {
		this.rfdAmt = rfdAmt;
	}

	/**
	 * 验签数据
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 验签数据
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 签名秘钥
	 */
	public String getKEY() {
		return KEY;
	}

	/**
	 * 签名秘钥
	 */
	public void setKEY(String kEY) {
		KEY = kEY;
	}

	@Override
    public String toString() {
		return "RefundNotifyReq[upTranSeq=" + upTranSeq + ",mercId=" + mercId + ",resultCode=" + resultCode + ",resultMessage=" + resultMessage
				+ ",jrnNo=" + jrnNo + ",orderId=" + orderId + ",rfdAplJrn=" + rfdAplJrn + ",rfdCcy=" + rfdCcy + ",rfdCcyAmt=" + rfdCcyAmt
				+ ",rfdAmt=" + rfdAmt + ",sign=" + sign + ",KEY=" + KEY + "]";
	}
}
