package com.hisun.lemon.cpi.bestpay.ebankpay;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.security.MessageDigest;

import org.apache.log4j.Logger;

import com.hisun.channel.common.core.Signature;
import com.hisun.channel.common.lifecycle.LifecycleBase;
import com.hisun.channel.common.lifecycle.LifecycleState;
import com.hisun.channel.common.utils.StringUtils;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumEncrypt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 签名工具类
 * 
 * <AUTHOR>
 *
 */
@Component
public class BestPaySecurity extends LifecycleBase implements Signature {

	private static Logger logger = Logger.getLogger(BestPaySecurity.class);

	@Autowired
	private BestpayProperties bestpayProperties;

	@Override
	public String sign(String signStr) {
		return getMD5ofByte(signStr, EnumEncrypt.encrypt);
	}

	@Override
	public boolean verify(String verifyStr, String signStr) {
		// System.out.println(StringUtils.contains(getMD5ofByte(verifyStr),signStr));
		return true;
	}

	@Override
	protected void doInit() {
	}

	@Override
	protected void doStart() {
		this.setState(LifecycleState.STARTING);
	}

	@Override
	protected void doStop() {
		this.setState(LifecycleState.STOPPING);
	}

	@Override
	protected void doDestroy() {
	}

	/**
	 * 
	 * 获得MD5加密密码的方法
	 */
	public String getMD5ofByte(String strData, EnumEncrypt encrypt) {
		String origMD5 = null;
		String c = "";
		String signVal = "";
		String keyName = "";
		try {
			if (encrypt != null) {
				switch (encrypt) {
					case encrypt:
						keyName = "&key=";
						break;
					case decrypt:
						keyName = "&KEY=";
						break;
				}
				signVal = strData + keyName + bestpayProperties.getAppKey();
			}
			System.out.println("加密内容:" + signVal);
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			byte[] result = md5.digest(signVal.getBytes("UTF-8"));
			origMD5 = byteArray2HexStr(result);
			if (origMD5 != null) {
				c = origMD5.toUpperCase();
			}
		} catch (Exception e) {
			logger.error("翼支付getMD5ofByte获得MD5加密密码的方法异常:" + e.getMessage());
		}
		return c;
	}

	/**
	 * 验签
	 * 
	 * @param obj
	 * @return
	 */
	public boolean isVerify(Object obj) {
		boolean bool = false;

		// 验签字符串
		StringBuilder paramStringBuffer = new StringBuilder();
		// 反射对象
		Field[] fields = obj.getClass().getDeclaredFields();
		String sign = "";
		try {
			for (Field field : fields) {
				// 属性名称
				String name = field.getName();
				// 将属性名称头字母改成大写
				String firstLetter = name.substring(0, 1).toUpperCase();
				// 获取get属性名
				String getter = "get" + firstLetter + name.substring(1);
				// 获取属性get方法
				Method method = obj.getClass().getMethod(getter, new Class[] {});
				// 获取属性值
				Object value = method.invoke(obj, new Object[] {});
				if (value != null) {
					if (name != "sign") {
						paramStringBuffer.append("&").append(name).append("=").append(value);

					} else {
						// 获取签名
						sign = value.toString();
					}
				}
			}
			// 删除头$符号
			if (paramStringBuffer.indexOf("&", 0) == 0) {
				paramStringBuffer.deleteCharAt(0);
			}

			// 价签并与sign签名匹配验签
			bool = StringUtils.contains(getMD5ofByte(paramStringBuffer.toString(), EnumEncrypt.decrypt), sign);

					} catch (Exception e) {
					logger.error("翼支付isVerify验签方法异常:" + e.getMessage());
					return false;
					}
					return bool;
					}

/**
 *
 * 处理字节数组得到MD5密码的方法
 */
private String byteArray2HexStr(byte[] bs) {
		StringBuffer sb = new StringBuffer();
		for (byte b : bs) {
		sb.append(byte2HexStr(b));
		}
		return sb.toString();
		}

/**
 *
 * 字节标准移位转十六进制方法
 */
private String byte2HexStr(byte b) {
		String hexStr = null;
		int n = b;
		if (n < 0) {
		// 定义移位算法
		n = b & 0x7F + 128;
		}
		hexStr = Integer.toHexString(n / 16) + Integer.toHexString(n % 16);
		return hexStr.toUpperCase();
		}
		}
