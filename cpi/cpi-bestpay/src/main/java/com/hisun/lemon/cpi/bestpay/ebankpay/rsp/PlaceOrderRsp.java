package com.hisun.lemon.cpi.bestpay.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.OrderByLetter;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.Plain.Form;
import com.hisun.lemon.cpi.bestpay.ebankpay.PubRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderCcy;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderSts;


/**
 * 下单响应报文
 * 
 * <AUTHOR>
 *
 */
@Plain(form=Form.STANDARD) 
@OrderByLetter
public class PlaceOrderRsp {

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	//@Item(before="merchantId=",separator="&")
	@Item
	@VerifyValue
	private String merchantId;
	
	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	//@Item(before="resultCode=",separator="&")
	@Item
	@VerifyValue
	private String resultCode;
	
	/**
	 * 返回码信息提示
	 */
	//@Item(before="resultMessage=",separator="&")
	@Item
	private String resultMessage;
	
	/**
     * 签名方式:固定值：MD5
     */
	//@Item(before="ReqSignTyp=",separator="&")
	@Item(alias="ReqSignTyp")
	private String reqSignTyp;

	/**
	 * 订单状态: U - 预登记 P –下单成功，处理中 S - 成功 F - 失败 R - 全额退款
	 */
	//@Item(before="orderSts=",separator="&")
	@Item
	@VerifyValue
	private String orderSts;

	/**
	 * 币种
	 */
	//@Item(before="orderCcy=",separator="&")
	@Item
	@VerifyValue
	private String orderCcy;

	/**
	 * 下单商户门店号
	 */
	//@Item(before="storeId=",separator="&")
	@Item
	@VerifyValue
	private String storeId;

	/**
	 * 付款码信息
	 */
	//@Item(before="barcode=",separator="&")
	@Item
	@VerifyValue
	private String barcode;

	/**
	 * 订单的外币金额
	 */
	//@Item(before="orderAmt=",separator="&")
	@Item
	@VerifyValue
	private String orderAmt;

	/**
	 * 订单本币金额
	 */
	//@Item(before="orderCcyAmt=",separator="&")
	@Item
	@VerifyValue
	private String orderCcyAmt;

	/**
	 * 交易汇率
	 */
	//@Item(before="orderRat=",separator="&")
	@Item
	@VerifyValue
	private String orderRat;

	/**
	 * 订单请求流水号
	 */
	//@Item(before="jrnNo=",separator="&")
	@Item
	@VerifyValue
	private String jrnNo;

	/**
	 * 订单号
	 */
	//@Item(before="orderId=",separator="&")
	@Item
	@VerifyValue
	private String orderId;

	/**
	 * 商户加密key
	 */
	//@Item(before="mercKey=",separator="&")
	@Item
	private String mercKey;

	/**
	 * 支付成功后台通知跨境地址（商户按照原值传给收银台）
	 */
	//@Item(before="backUrl=",separator="&")
	@Item
	@VerifyValue
	private String backUrl;
	
	/**
	 * 跨境商户号
	 */
	@Item
	@VerifyValue
	private String cbpMercId;

	/**
	 * 服务器签名:存放MD5加签后数据
	 */
	@Item
	@Verify
	private String serverSign;

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	public String getMerchantId() {
		return merchantId;
	}

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}	

	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	public String getResultCode() {
		return resultCode;
	}

	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	/**
	 * 返回码信息提示
	 */
	public String getResultMessage() {
		return resultMessage;
	}

	/**
	 * 返回码信息提示
	 */
	public void setResultMessage(String resultMessage) {
		this.resultMessage = resultMessage;
	}
	
	/**
     * 签名方式:固定值：MD5
     */
	public String getReqSignTyp() {
		return reqSignTyp;
	}

	/**
	 * 跨境商户号
	 */
	public String getCbpMercId() {
		return cbpMercId;
	}

	/**
	 * 跨境商户号
	 */
	public void setCbpMercId(String cbpMercId) {
		this.cbpMercId = cbpMercId;
	}
	
	/**
     * 签名方式:固定值：MD5
     */
	public void setReqSignTyp(String reqSignTyp) {
		this.reqSignTyp = reqSignTyp;
	}
	
	public String getServerSign() {
		return serverSign;
	}

	public void setServerSign(String serverSign) {
		this.serverSign = serverSign;
	}

	/**
	 * 订单状态: U - 预登记 P –下单成功，处理中 S - 成功 F - 失败 R - 全额退款
	 */
	public EnumOrderSts getOrderSts() {
		return EnumOrderSts.getEnum(orderSts);
	}

	/**
	 * 订单状态: U - 预登记 P –下单成功，处理中 S - 成功 F - 失败 R - 全额退款
	 */
	public void setOrderSts(String orderSts) {
		this.orderSts = orderSts;
	}

	/**
	 * 币种
	 */
	public EnumOrderCcy getOrderCcy() {
		return EnumOrderCcy.getEnum(orderCcy);
	}

	/**
	 * 币种
	 */
	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	/**
	 * 下单商户门店号
	 */
	public String getStoreId() {
		return storeId;
	}

	/**
	 * 下单商户门店号
	 */
	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	/**
	 * 付款码信息
	 */
	public String getBarcode() {
		return barcode;
	}

	/**
	 * 付款码信息
	 */
	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	/**
	 * 订单的外币金额
	 */
	public String getOrderAmt() {
		return orderAmt;
	}

	/**
	 * 订单的外币金额
	 */
	public void setOrderAmt(String orderAmt) {
		this.orderAmt = orderAmt;
	}

	/**
	 * 订单本币金额
	 */
	public String getOrderCcyAmt() {
		return orderCcyAmt;
	}

	/**
	 * 订单本币金额
	 */
	public void setOrderCcyAmt(String orderCcyAmt) {
		this.orderCcyAmt = orderCcyAmt;
	}

	/**
	 * 交易汇率
	 */
	public String getOrderRat() {
		return orderRat;
	}

	/**
	 * 交易汇率
	 */
	public void setOrderRat(String orderRat) {
		this.orderRat = orderRat;
	}

	/**
	 * 订单请求流水号
	 */
	public String getJrnNo() {
		return jrnNo;
	}

	/**
	 * 订单请求流水号
	 */
	public void setJrnNo(String jrnNo) {
		this.jrnNo = jrnNo;
	}

	/**
	 * 订单号
	 */
	public String getOrderId() {
		return orderId;
	}

	/**
	 * 订单号
	 */
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	/**
	 * 商户加密key
	 */
	public String getMercKey() {
		return mercKey;
	}

	/**
	 * 商户加密key
	 */
	public void setMercKey(String mercKey) {
		this.mercKey = mercKey;
	}

	/**
	 * 支付成功后台通知跨境地址（商户按照原值传给收银台）
	 */
	public String getBackUrl() {
		return backUrl;
	}

	/**
	 * 支付成功后台通知跨境地址（商户按照原值传给收银台）
	 */
	public void setBackUrl(String BackUrl) {
		this.backUrl = BackUrl;
	}
}
