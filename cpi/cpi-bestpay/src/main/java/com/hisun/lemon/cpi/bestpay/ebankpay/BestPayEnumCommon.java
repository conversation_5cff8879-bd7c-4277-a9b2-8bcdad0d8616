package com.hisun.lemon.cpi.bestpay.ebankpay;

import org.apache.commons.lang3.StringUtils;

/**
 * 枚举公共类
 * 
 * <AUTHOR>
 *
 */
public class BestPayEnumCommon {

	/**
	 * 加密类型
	 * <AUTHOR>
	 *
	 */
	public enum EnumEncrypt{
		encrypt,decrypt
	}
	
	
	/**
	 * 接口类型
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumSource {
		placeOrder,
		paymentQuery,
		refund,
		refundQuery
	}

	/**
	 * 交易码
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumService {
		APP_BARCODE_PAY("forAppBarcodePay"),
		APP_ORDER_QUERY("forAppOrderQuery"),
		APP_REFUND_QUERY("forAppRefundQuery"),
		APP_REFUND("forWebRefund"),
		APP_PAY("forAppPay");
        
		private String code;

		EnumService(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	/**
	 * 交易币种
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumOrderCcy {
		/**
		 * 美元
		 */
		USD;

		public static EnumOrderCcy getEnum(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumOrderCcy orderCcy : values()) {
					if (StringUtils.contains(orderCcy.name(), code)) {
						return orderCcy;
					}
				}
			}
			return null;
		}
	}

	/**
	 * 订单状态
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumOrderSts {
		PRE_REGISTRATION("U"),
		HANDLING("P"),
		SUCCESS("S"),
		FAIL("F"),
		FULL_REFUND("R");

		private String code;

		EnumOrderSts(String code) {
			this.code = code;
		}

		public static EnumOrderSts getEnum(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumOrderSts orderSts : values()) {
					if (StringUtils.contains(orderSts.getCode(), code)) {
						return orderSts;
					}
				}
			}
			return null;
		}

		public static EnumOrderSts getEnumByName(String name) {
			if (StringUtils.isNotEmpty(name)) {
				for (EnumOrderSts orderSts : values()) {
					if (StringUtils.contains(orderSts.name(),name)) {
						return orderSts;
					}
				}
			}
			return null;
		}

		public String getCode() {
			return code;
		}

	}
}
