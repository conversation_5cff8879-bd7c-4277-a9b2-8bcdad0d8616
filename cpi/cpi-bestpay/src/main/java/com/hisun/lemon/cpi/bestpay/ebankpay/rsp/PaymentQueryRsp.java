package com.hisun.lemon.cpi.bestpay.ebankpay.rsp;

import org.apache.commons.lang3.StringUtils;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.OrderByLetter;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.Plain.Form;
import com.hisun.lemon.cpi.bestpay.ebankpay.PubRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderCcy;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderSts;


/** 
 * 支付订单查询响应报文
 * 
 * <AUTHOR>
 *
 */
@Plain(form=Form.STANDARD) 
@OrderByLetter
public class PaymentQueryRsp {

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	//@Item(before="merchantId=",separator="&")
	@Item
	@VerifyValue
	private String merchantId;
	
	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	//@Item(before="resultCode=",separator="&")
	@Item
	@VerifyValue
	private String resultCode;
	
	/**
	 * 返回码信息提示
	 */
	//@Item(before="resultMessage=",separator="&")
	@Item
	@VerifyValue
	private String resultMessage;
	
	/**
     * 签名方式:固定值：MD5
     */
	//@Item(before="ReqSignTyp=",separator="&")
	@Item(alias="ReqSignTyp")
	@VerifyValue
	private String reqSignTyp;

	/**
	 * 服务器签名:存放MD5加签后数据
	 */
	@Verify
	private String serverSign;

	/**
	 * 支付结果: U - 预登记 P – 下单成功，处理中 F - 失败 S – 成功 R- 全额退款
	 */
	@Item
	@VerifyValue
	private String orderSts;

	/**
	 * 商户订单号
	 */
	@Item
	@VerifyValue
	private String orderId;

	/**
	 * 商户下单的日期:YYYYMMDD
	 */
	@Item
	@VerifyValue
	private String orderDate;

	/**
	 * 币种
	 */
	@Item
	@VerifyValue
	private String orderCcy;

	/**
	 * 订单金额
	 */
	@Item
	@VerifyValue
	private String orderAmt;

	/**
	 * 订单本币金额
	 */
	@Item
	@VerifyValue
	private String orderCcyAmt;

	/**
	 * 汇率
	 */
	@Item
	@VerifyValue
	private String orderRat;

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	public String getMerchantId() {
		return merchantId;
	}

	/**
	 * 商户代码:翼支付给合作商户分配的唯一标识
	 */
	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}	

	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	public String getResultCode() {
		return resultCode;
	}

	/**
	 * 返回交易码:CBP00000-交易成功,其他交易失败
	 */
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	/**
	 * 返回码信息提示
	 */
	public String getResultMessage() {
		return resultMessage;
	}

	/**
	 * 返回码信息提示
	 */
	public void setResultMessage(String resultMessage) {
		this.resultMessage = resultMessage;
	}

	/**
	 * 签名方式:固定值：MD5
	 */
	public String getReqSignTyp() {
		return reqSignTyp;
	}

	/**
	 * 签名方式:固定值：MD5
	 */
	public void setReqSignTyp(String reqSignTyp) {
		this.reqSignTyp = reqSignTyp;
	}

	public String getServerSign() {
		return serverSign;
	}

	public void setServerSign(String serverSign) {
		this.serverSign = serverSign;
	}

	/**
	 * 支付结果: U - 预登记 P – 下单成功，处理中 F - 失败 S – 成功 R- 全额退款
	 */
	public EnumOrderSts getOrderSts() {
		return EnumOrderSts.getEnum(orderSts);
	}

	/**
	 * 支付结果: U - 预登记 P – 下单成功，处理中 F - 失败 S – 成功 R- 全额退款
	 */
	public void setOrderSts(String orderSts) {
		this.orderSts = orderSts;
	}

	/**
	 * 商户订单号
	 */
	public String getOrderId() {
		return orderId;
	}

	/**
	 * 商户订单号
	 */
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	/**
	 * 商户下单的日期:YYYYMMDD
	 */
	public String getOrderDate() {
		return orderDate;
	}

	/**
	 * 商户下单的日期:YYYYMMDD
	 */
	public void setOrderDate(String orderDate) {
		this.orderDate = orderDate;
	}

	/**
	 * 币种
	 */
	public EnumOrderCcy getOrderCcy() {
		return EnumOrderCcy.getEnum(orderCcy);
	}

	/**
	 * 币种
	 */
	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	/**
	 * 订单金额
	 */
	public String getOrderAmt() {
		return orderAmt;
	}

	/**
	 * 订单金额
	 */
	public void setOrderAmt(String orderAmt) {
		this.orderAmt = orderAmt;
	}

	/**
	 * 订单本币金额
	 */
	public String getOrderCcyAmt() {
		return orderCcyAmt;
	}

	/**
	 * 订单本币金额
	 */
	public void setOrderCcyAmt(String orderCcyAmt) {
		this.orderCcyAmt = orderCcyAmt;
	}

	/**
	 * 汇率
	 */
	public String getOrderRat() {
		return orderRat;
	}

	/**
	 * 汇率
	 */
	public void setOrderRat(String orderRat) {
		this.orderRat = orderRat;
	}
}
