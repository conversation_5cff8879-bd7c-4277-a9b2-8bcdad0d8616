package com.hisun.lemon.cpi.bestpay.ebankpay.req;

/**
 * 下单通知对象
 * 
 * <AUTHOR>
 *
 */
public class PlaceOrderNotifyReq {

	/**
	 * 翼支付系统流水号
	 */
	private String upTranSeq;

	/**
	 * 翼支付系统交易日期
	 */
	private String tranDate;

	/**
	 * 处理结果码
	 */
	private String resultCode;

	/**
	 * 处理结果解释码
	 */
	private String resultMessage;

	/**
	 * 订单请求流水号
	 */
	private String jrnNo;

	/**
	 * 订单号
	 */
	private String orderId;
	
	/**
	 * 订单本币金额
	 */
	private String orderCcyAmt;
	
	/**
	 * 订单金额
	 */
	private String orderAmt;
	
	/**
	 * 汇率
	 */
	private String orderRat;

	/**
	 * 订单币种
	 */
	private String orderCcy;

	/**
	 * 签名方式
	 */
	private String signType;

	/**
	 * 银行编码
	 */
	private String bankId;

	/**
	 * 商户附加信息
	 */
	private String attach;

	/**
	 * 翼支付平台请求银行流水号
	 */
	private String upReqTranSeq;

	/**
	 * 银行流水号
	 */
	private String upBankTranSeq;

	/**
	 * 数字签名
	 */
	private String sign;

	/**
	 * 翼支付系统流水号
	 */
	public String getUpTranSeq() {
		return upTranSeq;
	}

	/**
	 * 翼支付系统流水号
	 */
	public void setUpTranSeq(String upTranSeq) {
		this.upTranSeq = upTranSeq;
	}

	/**
	 * 翼支付系统交易日期
	 */
	public String getTranDate() {
		return tranDate;
	}

	/**
	 * 翼支付系统交易日期
	 */
	public void setTranDate(String tranDate) {
		this.tranDate = tranDate;
	}

	/**
	 * 处理结果码
	 */
	public String getResultCode() {
		return resultCode;
	}

	/**
	 * 处理结果码
	 */
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	/**
	 * 处理结果解释码
	 */
	public String getResultMessage() {
		return resultMessage;
	}

	/**
	 * 处理结果解释码
	 */
	public void setResultMessage(String resultMessage) {
		this.resultMessage = resultMessage;
	}

	/**
	 * 订单请求流水号
	 */
	public String getJrnNo() {
		return jrnNo;
	}

	/**
	 * 订单请求流水号
	 */
	public void setJrnNo(String jrnNo) {
		this.jrnNo = jrnNo;
	}

	/**
	 * 订单号
	 */
	public String getOrderId() {
		return orderId;
	}

	/**
	 * 订单号
	 */
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	/**
	 * 订单币种
	 */
	public String getOrderCcy() {
		return orderCcy;
	}

	/**
	 * 订单币种
	 */
	public void setOrderCcy(String orderCcy) {
		this.orderCcy = orderCcy;
	}

	/**
	 * 订单金额
	 */
	public String getOrderAmt() {
		return orderAmt;
	}

	/**
	 * 订单金额
	 */
	public void setOrderAmt(String orderAmt) {
		this.orderAmt = orderAmt;
	}

	/**
	 * 订单本币金额
	 */
	public String getOrderCcyAmt() {
		return orderCcyAmt;
	}

	/**
	 * 订单本币金额
	 */
	public void setOrderCcyAmt(String orderCcyAmt) {
		this.orderCcyAmt = orderCcyAmt;
	}

	/**
	 * 汇率
	 */
	public String getOrderRat() {
		return orderRat;
	}

	/**
	 * 汇率
	 */
	public void setOrderRat(String orderRat) {
		this.orderRat = orderRat;
	}

	/**
	 * 签名方式
	 */
	public String getSignType() {
		return signType;
	}

	/**
	 * 签名方式
	 */
	public void setSignType(String signType) {
		this.signType = signType;
	}

	/**
	 * 银行编码
	 */
	public String getBankId() {
		return bankId;
	}

	/**
	 * 银行编码
	 */
	public void setBankId(String bankId) {
		this.bankId = bankId;
	}

	/**
	 * 商户附加信息
	 */
	public String getAttach() {
		return attach;
	}

	/**
	 * 商户附加信息
	 */
	public void setAttach(String attach) {
		this.attach = attach;
	}

	/**
	 * 翼支付平台请求银行流水号
	 */
	public String getUpReqTranSeq() {
		return upReqTranSeq;
	}

	/**
	 * 翼支付平台请求银行流水号
	 */
	public void setUpReqTranSeq(String upReqTranSeq) {
		this.upReqTranSeq = upReqTranSeq;
	}

	/**
	 * 银行流水号
	 */
	public String getUpBankTranSeq() {
		return upBankTranSeq;
	}

	/**
	 * 银行流水号
	 */
	public void setUpBankTranSeq(String upBankTranSeq) {
		this.upBankTranSeq = upBankTranSeq;
	}

	/**
	 * 数字签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 数字签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	@Override
    public String toString() {
		return "PlaceOrderNotifyReq[upTranSeq=" + upTranSeq + ",tranDate=" + tranDate + ",resultCode=" + resultCode + ",resultMessage="
				+ resultMessage + ",jrnNo=" + jrnNo + ",orderId=" + orderId + ",orderCcy=" + orderCcy + ",orderAmt=" + orderAmt + ",orderCcyAmt="
				+ orderCcyAmt + ",orderRat=" + orderRat + ",signType=" + signType + ",bankId=" + bankId + ",attach=" + attach + ",upReqTranSeq="
				+ upReqTranSeq + ",upBankTranSeq=" + upBankTranSeq + ",sign=" + sign + "]";
	}
}
