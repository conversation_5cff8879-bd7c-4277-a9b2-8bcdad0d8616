<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:channel="http://www.hisun.com/schema/channel"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.hisun.com/schema/channel http://www.hisun.com/schema/channel/channel.xsd">
        
    <bean id="bestPaySignature" class="com.hisun.lemon.cpi.bestpay.ebankpay.BestPaySecurity"/>
    
    <channel:service name="BESTPAY" charset="UTF-8" >
        <!-- 
        <channel:connector protocol="tcp_long" address="127.0.0.1:7778" timeout="60000" waitTimeout="100000" heartbeatCheck-ref="icbcHeartbeat" poolMaxConnections="5" />
         -->
        <channel:connector id="http1" protocol="http" url="https://cbp.bestpay.com.cn/cashier" default="false"   dateTypeName="FORM" charset="UTF-8"/>
        <channel:container> <!-- http://127.0.0.1:7777/mock/bytes/WING    http://***************:48092/cashier -->
            <!-- 如果签名验签都一样，建议在此处配置；如果不一样，建议在marshall-class 用注解@Signature(bean="beanName") -->
            <channel:signature ref="bestPaySignature" />   
            <channel:processor name="placeOrder" marshal-class="com.hisun.lemon.cpi.bestpay.ebankpay.req.PlaceOrderReq" 
                unmarshal-class="com.hisun.lemon.cpi.bestpay.ebankpay.rsp.PlaceOrderRsp" connector-id="http1" />
                
             <channel:processor name="paymentQuery" marshal-class="com.hisun.lemon.cpi.bestpay.ebankpay.req.PaymentQueryReq" 
                unmarshal-class="com.hisun.lemon.cpi.bestpay.ebankpay.rsp.PaymentQueryRsp" connector-id="http1" />
                
             <channel:processor name="refundQuery" marshal-class="com.hisun.lemon.cpi.bestpay.ebankpay.req.RefundQueryReq" 
                unmarshal-class="com.hisun.lemon.cpi.bestpay.ebankpay.rsp.RefundQueryRsp" connector-id="http1" />
                
             <channel:processor name="refund" marshal-class="com.hisun.lemon.cpi.bestpay.ebankpay.req.RefundReq" 
                unmarshal-class="com.hisun.lemon.cpi.bestpay.ebankpay.rsp.RefundRsp" connector-id="http1" />
            <!-- 此处的handler-filter对全部processor生效 -->
            <channel:handler-filter class="com.hisun.channel.service.child.filter.MonitorHandlerFilter" />
            <!-- <channel:handler-filter class="com.hisun.channel.service.child.filter.MockHandlerFilter" /> -->
        </channel:container>
    </channel:service>
</beans>