package com.hisun.lemon.cpi;

import javax.annotation.Resource;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayApi;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumOrderCcy;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumService;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon.EnumSource;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.PaymentQueryReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.PlaceOrderReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.RefundQueryReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.RefundReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.PaymentQueryRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.PlaceOrderRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.RefundQueryRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.RefundRsp;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ObjectToBestPayTest {

	@Resource
	BestPayApi api;

	/**
	 * app
	 */
	@Test
	@Ignore
	public void placeOrderTest() {
		PlaceOrderReq placeOrderReq = new PlaceOrderReq();
		placeOrderReq.setMerchantId("*****************");
		placeOrderReq.setRequestId("********");
		placeOrderReq.setCharset("02");
		placeOrderReq.setService(EnumService.APP_BARCODE_PAY);
		placeOrderReq.setSignType("MD5");
		placeOrderReq.setVersion("1.0");
		placeOrderReq.setOrderId("************");
		placeOrderReq.setJrnNo("************");
		placeOrderReq.setOrderAmt("2");
		placeOrderReq.setOrderCcy(EnumOrderCcy.USD);
		placeOrderReq.setStoreId("201231");
		placeOrderReq.setBarcode("510188859936336153");
		placeOrderReq.setProductId("04");
		placeOrderReq.setProductDesc("testshopping");
		placeOrderReq.setLoginNo("***********");
		placeOrderReq.setProviceCode("shanghai");
		placeOrderReq.setCityCode("hongkou");
		placeOrderReq.setBankUrl("http://219.135.153.39:8310/cpi/bestpay/placeOrder");
		placeOrderReq.setAttach("test");
		PlaceOrderRsp placeOrderRsp = api.doSend(placeOrderReq, EnumSource.placeOrder);
		String resultCode = placeOrderRsp.getResultCode();
//		String resultMessage = placeOrderRsp.getResultMessage();
//		String retOrdSts = placeOrderRsp.getOrderSts().getCode();
//		System.out.println("商户扫码下单返回报文 placeOrderRsp = " + placeOrderRsp);
	}

	/**
	 * app
	 */
	@Test
	@Ignore
	public void paymentQueryTest() {

		PaymentQueryReq paymentQueryReq = new PaymentQueryReq();
		paymentQueryReq.setOrderId("1171");
		paymentQueryReq.setMerchantId("*****************");
		paymentQueryReq.setRequestId("********");
		paymentQueryReq.setCharset("02");
		paymentQueryReq.setService(EnumService.APP_ORDER_QUERY);
		paymentQueryReq.setSignType("MD5");
		paymentQueryReq.setVersion("1.0");
		
		PaymentQueryRsp rsp = api.doSend(paymentQueryReq, EnumSource.paymentQuery);
		System.out.println(rsp);
	}

	/**
	 * app
	 */
	@Test
	@Ignore
	public void refundTest() {

		RefundReq refundReq = new RefundReq();
		refundReq.setBgUrl("http://219.135.153.39:8310/cpi/bestpay/refund");
		refundReq.setOldJrnNo("********0010");
		refundReq.setOldOrderNo("********0010");
		refundReq.setRefundAmt("0.5");
		refundReq.setRefundCcy(EnumOrderCcy.USD);
		refundReq.setRefundReqNo("201709130006");
		refundReq.setMerchantId("*****************");
		refundReq.setRequestId("20170913");
		refundReq.setCharset("02");
		refundReq.setService(EnumService.APP_REFUND);
		refundReq.setSignType("MD5");
		refundReq.setVersion("1.0");

		RefundRsp rsp = api.doSend(refundReq, EnumSource.refund);
		System.out.println(rsp);
	}

	/**
	 * app
	 */
	@Test
	@Ignore
	public void refundQueryTest() {
		RefundQueryReq refundQueryRsq = new RefundQueryReq();
		refundQueryRsq.setOldOrderId("1171");
		refundQueryRsq.setRefundReqNo("555575");
		refundQueryRsq.setMerchantId("*****************");
		refundQueryRsq.setRequestId("********");
		refundQueryRsq.setCharset("02");
		refundQueryRsq.setService(EnumService.APP_REFUND_QUERY);
		refundQueryRsq.setSignType("MD5");
		refundQueryRsq.setVersion("1.0");
		
		RefundQueryRsp rsp = api.doSend(refundQueryRsq, EnumSource.refundQuery);
		System.out.println(rsp);
	}

	/**
	 * 聚合支付
	 */
	@Test
	@Ignore
	public void placeOrderMAPITest(){
		PlaceOrderReq placeOrderReq = new PlaceOrderReq();
		placeOrderReq.setCharset("02");
		placeOrderReq.setVersion("1.0");
		placeOrderReq.setSignType("MD5");
		placeOrderReq.setService(EnumService.APP_PAY);
		placeOrderReq.setMerchantId("*****************");
		placeOrderReq.setRequestId("********");
		placeOrderReq.setOrderId("********0947000016");
		placeOrderReq.setJrnNo("********0947000016");
		placeOrderReq.setOrderAmt("1");
		placeOrderReq.setOrderCcy(EnumOrderCcy.USD);
		placeOrderReq.setProductId("04");
		placeOrderReq.setProductDesc("testshopping");
		placeOrderReq.setLoginNo("***********");
		placeOrderReq.setProviceCode("shanghai");
		placeOrderReq.setCityCode("hongkou");
		placeOrderReq.setBankUrl("http://219.135.153.39:8310/cpi/bestpay/placeOrder");
		placeOrderReq.setAttach("attch");
		
		PlaceOrderRsp rsp = api.doSend(placeOrderReq, EnumSource.placeOrder);
		System.out.println(rsp);
	}
	
	/**
	 * 聚合支付查询
	 */
	@Test
	@Ignore
	public void paymentQueryMAPITest(){
		PaymentQueryReq paymentQueryReq = new PaymentQueryReq();
		paymentQueryReq.setCharset("02");
		paymentQueryReq.setVersion("1.0");
		paymentQueryReq.setSignType("MD5");
		paymentQueryReq.setService(EnumService.APP_ORDER_QUERY);
		paymentQueryReq.setMerchantId("*****************");
		paymentQueryReq.setOrderId("********0947000014");
		paymentQueryReq.setRequestId("********");
		
		PaymentQueryRsp rsp = api.doSend(paymentQueryReq, EnumSource.paymentQuery);
		System.out.println(rsp);
	}
	
	/**
	 * 聚合退款
	 */
	@Test
	@Ignore
	public void refundMAPITest(){
		RefundReq refundReq = new RefundReq();
		refundReq.setCharset("02");
		refundReq.setVersion("1.0");
		refundReq.setSignType("MD5");
		refundReq.setService(EnumService.APP_REFUND);
		refundReq.setMerchantId("*****************");
		refundReq.setRequestId("********");
		refundReq.setRefundReqNo("77778");
		refundReq.setOldJrnNo("********0947000015");
		refundReq.setOldOrderNo("********0947000015");
		refundReq.setRefundCcy(EnumOrderCcy.USD);
		refundReq.setBgUrl("http://219.135.153.39:8310/cpi/bestpay/refund");
		refundReq.setRefundAmt("1");
		
		RefundRsp rsp = api.doSend(refundReq, EnumSource.refund);
		System.out.println(rsp);
	}
	
	/**
	 * 聚合退款查询
	 */
	@Test
	@Ignore
	public void refundQueryMAPITest(){
		RefundQueryReq refundQueryRsq = new RefundQueryReq();
		refundQueryRsq.setCharset("02");
		refundQueryRsq.setVersion("1.0");
		refundQueryRsq.setSignType("MD5");
		refundQueryRsq.setService(EnumService.APP_REFUND_QUERY);
		refundQueryRsq.setMerchantId("*****************");
		refundQueryRsq.setRequestId("********");
		refundQueryRsq.setRefundReqNo("555575");
		refundQueryRsq.setOldOrderId("1171");
		RefundQueryRsp rsp = api.doSend(refundQueryRsq, EnumSource.refundQuery);
		
	}
}
