
package com.hisun.lemon.cpi;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.hisun.channel.common.core.Signature;
import com.hisun.channel.parse.AbstractUnmarshaller;
import com.hisun.channel.parse.Unmarshaller;
import com.hisun.channel.parse.UnmarshallerFactory;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPaySecurity;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.PaymentNotifyReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.PlaceOrderRsp;


@RunWith(SpringRunner.class)
@SpringBootTest
public class BestPayTestToObject {

	@Test
	@Ignore
	public void placeOrderTest() {

		String str="merchantId=*****************&resultCode=CBP00009&resultMessage=商户订单号已经存在&orderSts=F&orderCcy=USD&storeId=201231&barcode=&orderAmt=10&orderCcyAmt=68.60&orderRat=686&orderId=1155&jrnNo=2265&ReqSignTyp=MD5&serverSign=E2D48C7A3749CAE2FFBA95EDECAD433F";
		Unmarshaller<PlaceOrderRsp> unmarshaller = UnmarshallerFactory.getUnmarshaller(PlaceOrderRsp.class, false);
		((AbstractUnmarshaller<?>) unmarshaller).setSignature(new Signature(){

			@Override
			public String sign(String signStr) {
				return "~~~"+signStr+"~~~";
			}

			@Override
			public boolean verify(String verifyStr, String signStr) {
				System.out.println("verifyStr:"+verifyStr);
				System.out.println("signStr:"+signStr);
				return true;
			}});
		unmarshaller.analyse();
		PlaceOrderRsp so = unmarshaller.unmarshal(str.getBytes());
		System.out.println("=="+so);
	}
	
	@Test
	@Ignore
	public void isVerify(){
		PaymentNotifyReq req=new PaymentNotifyReq();
		req.setUpTranSeq("********00000004125363");
		req.setTranDate("********");
		req.setResultCode("CBP00000");
		req.setResultMessage("0000");
		req.setJrnNo("********152706096536");
		req.setOrderId("********152706096536");
		req.setOrderCcyAmt("42532");
		req.setOrderAmt("62");
		req.setOrderRat("686");
		req.setOrderCcy("USD");
		req.setSignType("");
		req.setBankId("EPAYACC");
		req.setAttach("orderAmt=62USD&orderCcy=USD&orderRate=1USD=6.8600CNY&bizType=crossBoardPayment");
		req.setUpReqTranSeq("170918611002482849");
		req.setUpBankTranSeq("2001101020000918152708009992533");
		req.setSign("4EC03C1AD2F015B6C0CDCBC34A535382");
//		boolean bool=bestPaySecurity.isVerify(req);
//		System.out.println(bool);
	}
}

