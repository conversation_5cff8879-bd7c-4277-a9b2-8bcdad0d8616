package com.hisun.lemon.cpi;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.hisun.channel.common.utils.StringUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayApi;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.PaymentQueryReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.PaymentQueryRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.RefundRsp;
import com.hisun.lemon.cpi.bnkapi.bestpay.BestPayRetCdConvert;
import com.hisun.lemon.cpi.bnkapi.wechat.WeChatPayNewApi;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.dao.*;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.*;
import com.hisun.lemon.cpi.utils.XmlParseUtils;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon;
import com.hisun.lemon.cpi.wechat.ebankpay.WeChatApi;
import com.hisun.lemon.cpi.wechat.ebankpay.req.RefundQueryReq;
import com.hisun.lemon.cpi.wechat.ebankpay.req.SubMerchantEntryReq;
import com.hisun.lemon.cpi.wechat.ebankpay.req.WeChatOrderNotifyReq;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.RefundQueryRsp;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.SubMerchantEntryRsp;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.WeChatOrderNotifyRsp;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by Rui on 2017/7/5.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class ApplicationTest {
    @Autowired
    IRouteDao routeDao;
    @Autowired
    IRefundService refundService;
    @Resource
    IAccParamDao iAccParamDao;
    @Resource
    IAccControlDao iAccControlDao;
    @Resource
    ICheckMgrService iCheckMgrService;
    @Resource
    IAccRefundCfgDao accRefundCfgDao;
    @Resource
    ICheckMgrService checkMgrService;
    @Resource
    ICheckService checkService;
    @Resource
    IEbankpayService ebankpayService;
    @Autowired
    private WebApplicationContext context;
    @Resource
    private WeChatApi weChatApi;
    @Resource
    private BestPayApi bestPayApi;

    @Resource
    private WeChatPayNewApi weChatPayNewApi;
    @Resource
    private ISettlementService settlementService;

    @Resource
    private BestPayRetCdConvert bestPayRetCdConvert;


    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    @Test
    public void queryEffOrgInfo() throws Exception {
        RequestBuilder request = MockMvcRequestBuilders.get("/cpi/route/result?corpBusTyp=SIGN&corpBusSubTyp=PREFAST_SIGN");
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();
        System.out.println("status ============= " + status);
        System.out.println("content ============= " + content);
    }

    @Test
    public void queryCardBin() throws Exception {
        RequestBuilder request = MockMvcRequestBuilders.get("/cpi/cards/head/1234567899");
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();
        System.out.println("================end=============");
    }

    @Test
    public void queryCardsInfo() throws Exception {
        GenericDTO<AgrInfoReqDTO> genericDTO = new GenericDTO<>();
        AgrInfoReqDTO agrInfoReqDTO = new AgrInfoReqDTO();
        agrInfoReqDTO.setCorpBusTyp(CorpBusTyp.SIGN);
        agrInfoReqDTO.setCorpBusSubTyp(CorpBusSubTyp.FAST_SIGN);

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(agrInfoReqDTO);
        RequestBuilder request = MockMvcRequestBuilders.get("/cpi/cards/userId")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();
        System.out.println("================end=============");
    }

    @Test
    public void contextLoads() throws Exception {
        RouteDO routeDO = new RouteDO();
        routeDO.setCorpBusTyp("01");
        routeDO.setCorpBusSubTyp("0101");
        routeDO.setCrdAcTyp("D");
        routeDO.setCrdCorpOrg("ICBC");
        RouteDO rspRouteDO = routeDao.queryRouteInfo(routeDO,new BigDecimal("50"));
        List<RouteDO> routeDOs =  routeDao.queryRouteList("01","0101",null);
    }

    @Test
    public void preBindCard() throws Exception{
        CardPreReqDTO cardPreReqDTO = new CardPreReqDTO();
        cardPreReqDTO.setCrdAcTyp("D");
        cardPreReqDTO.setBnkPsnFlg("C");
        cardPreReqDTO.setCrdCorpOrg("ICBC");
        cardPreReqDTO.setCrdCvv2("13dfadsfa");
        cardPreReqDTO.setCrdExpDt("23dfa");
        cardPreReqDTO.setIdTyp("01");
        cardPreReqDTO.setCrdNo("1234567899");
        cardPreReqDTO.setIdNo("3432434234234");
        cardPreReqDTO.setMblNo("131313131313");
        cardPreReqDTO.setCrdUsrNm("李四");

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(cardPreReqDTO);
        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/fastpay/pre/cards")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

    @Test
    public void bindCard() throws Exception{
        CardBindReqDTO cardBindReqDTO = new CardBindReqDTO();
        cardBindReqDTO.setCrdAcTyp("D");
        cardBindReqDTO.setCrdCorpOrg("ICBC");
        cardBindReqDTO.setMblNo("131313131313");
        cardBindReqDTO.setJrnNo("CP201707160000011001");
        cardBindReqDTO.setCrdUsrNm("李四");

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(cardBindReqDTO);
        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/fastpay/cards")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

    @Test
    public void unbindCard() throws Exception{
        RequestBuilder request = MockMvcRequestBuilders.put("/cpi/fastpay/cards?userId=1&argNo=CP1707160000002502");
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

    @Test
    public void fastpay() throws Exception{
        FastpayReqDTO fastpayDTO = new FastpayReqDTO();
        fastpayDTO.setCorpBusTyp(CorpBusTyp.FASTPAY);
        fastpayDTO.setCorpBusSubTyp(CorpBusSubTyp.FASTPAY);
        fastpayDTO.setArgNo("122");
        fastpayDTO.setBnkPsnFlg("C");
        fastpayDTO.setOrdAmt(new BigDecimal("2.00"));
        fastpayDTO.setOrdCcy("01");
        fastpayDTO.setSmsFlag("0");
//        fastpayDTO.setReqOrdDt(LocalDate.now());
//        fastpayDTO.setReqOrdTm(LocalTime.now());
        fastpayDTO.setReqOrdNo("*********");

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(fastpayDTO);
        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/fastpay/payment")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

    @Test
    public void ebankpay() throws Exception{
        EbankpayReqDTO ebankpayDTO = new EbankpayReqDTO();
//        ebankpayDTO.setReqOrdDt(LocalDate.now());
//        ebankpayDTO.setReqOrdTm(LocalTime.now());
        ebankpayDTO.setReqOrdNo("*************");
        ebankpayDTO.setCorpBusTyp(CorpBusTyp.EBANKPAY);
        ebankpayDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANKPAY);
        ebankpayDTO.setCrdAcTyp("D");
        ebankpayDTO.setCrdCorpOrg("ICBC");
        ebankpayDTO.setOrdAmt(new BigDecimal("0.02"));
        ebankpayDTO.setOrdCcy("01");
//        ebankpayDTO.setUserId("1");
        ebankpayDTO.setBnkPsnFlg("C");
        ebankpayDTO.setUserTyp("M");
//        ebankPayService.ebankpay(ebankpayDTO);
        GenericDTO<EbankpayReqDTO> genericDTO = new GenericDTO<>();
        genericDTO.setBody(ebankpayDTO);

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericDTO);
        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/ebank/order")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

//    @Test
//    public void mercScanPay() throws Exception{
//        MercScanReqDTO mercScanReqDTO = new MercScanReqDTO();
////        ebankpayDTO.setReqOrdDt(LocalDate.now());
////        ebankpayDTO.setReqOrdTm(LocalTime.now());
//        mercScanReqDTO.setReqOrdNo("************");
//        mercScanReqDTO.setCorpBusTyp(CorpBusTyp.EBANKPAY);
//        mercScanReqDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANKPAY_MERCSCAN);
//        mercScanReqDTO.setCrdAcTyp("D");
//        mercScanReqDTO.setCrdCorpOrg("BESTPAY");
//        mercScanReqDTO.setOrdAmt(new BigDecimal("0.02"));
//        mercScanReqDTO.setOrdCcy("01");
////        mercScanReqDTO.setUserId("1");
//        mercScanReqDTO.setBnkPsnFlg("C");
//        mercScanReqDTO.setUserTyp("M");
////        ebankPayService.ebankpay(ebankpayDTO);
//        GenericDTO<MercScanReqDTO> genericDTO = new GenericDTO<>();
//        genericDTO.setBody(mercScanReqDTO);
//
//        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//        java.lang.String requestJson = ow.writeValueAsString(genericDTO);
//        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/ebank/code/merc")
//                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
//        MvcResult mvcResult = mockMvc.perform(request).andReturn();
//        int status = mvcResult.getResponse().getStatus();
//        String content = mvcResult.getResponse().getContentAsString();
//
//        System.out.println("================end=============");
//    }

//    @Test
//    public void userScanPay() throws Exception{
//        UserScanReqDTO userScanReqDTO = new UserScanReqDTO();
////        ebankpayDTO.setReqOrdDt(LocalDate.now());
////        ebankpayDTO.setReqOrdTm(LocalTime.now());
//        userScanReqDTO.setReqOrdNo("************");
//        userScanReqDTO.setCorpBusTyp(CorpBusTyp.EBANKPAY);
//        userScanReqDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANKPAY_USERSCAN);
//        userScanReqDTO.setCrdAcTyp("D");
//        userScanReqDTO.setCrdCorpOrg("BESTPAY");
//        userScanReqDTO.setOrdAmt(new BigDecimal("0.02"));
//        userScanReqDTO.setOrdCcy("01");
////        userScanReqDTO.setUserId("1");
//        userScanReqDTO.setBnkPsnFlg("C");
//        userScanReqDTO.setUserTyp("M");
////        ebankPayService.ebankpay(ebankpayDTO);
//        GenericDTO<UserScanReqDTO> genericDTO = new GenericDTO<>();
//        genericDTO.setBody(userScanReqDTO);
//
//        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
//        java.lang.String requestJson = ow.writeValueAsString(genericDTO);
//        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/ebank/code/user")
//                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
//        MvcResult mvcResult = mockMvc.perform(request).andReturn();
//        int status = mvcResult.getResponse().getStatus();
//        String content = mvcResult.getResponse().getContentAsString();
//
//        System.out.println("================end=============");
//    }

    @Test
    public void fastRefund() throws Exception {
        RefundReqDTO refundDTO = new RefundReqDTO();
//        refundDTO.setReqOrdDt(LocalDate.now());
//        refundDTO.setReqOrdTm(LocalTime.now());
        refundDTO.setReqOrdNo("*********");
        refundDTO.setCorpBusTyp(CorpBusTyp.REFUND);
        refundDTO.setCorpBusSubTyp(CorpBusSubTyp.FAST_REFUND);
        refundDTO.setOrdAmt(new BigDecimal("0.02"));
        refundDTO.setOrdCcy("01");
        refundDTO.setOrdNo("CP000000000000025501");
//        refundDTO.setUserId("1");
//        refundDTO.setAgrPayDt(LocalDate.now());
//        refundService.refund(refundDTO);
        GenericDTO<RefundReqDTO> genericDTO = new GenericDTO<>();
        genericDTO.setBody(refundDTO);

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericDTO);
        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/refund/order")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();
        System.out.println("================end=============");
    }

    @Test
    public void ebankRefund() throws Exception {
        RefundReqDTO refundDTO = new RefundReqDTO();
//        refundDTO.setReqOrdDt(LocalDate.now());
//        refundDTO.setReqOrdTm(LocalTime.now());
        refundDTO.setReqOrdNo("*********");
        refundDTO.setCorpBusTyp(CorpBusTyp.REFUND);
        refundDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANK_REFUND);
        refundDTO.setOrdAmt(new BigDecimal("0.02"));
        refundDTO.setOrdCcy("01");
        refundDTO.setOrdNo("CP201708010000031001");
//        refundDTO.setUserId("1");
//        refundDTO.setAgrPayDt(LocalDate.now());
//        refundService.refund(refundDTO);
        GenericDTO<RefundReqDTO> genericDTO = new GenericDTO<>();
        genericDTO.setBody(refundDTO);

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericDTO);
        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/refund/order")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

    @Test
    public void remit() throws Exception {
        RemittanceReqDTO remittanceDTO = new RemittanceReqDTO();
//        remittanceDTO.setReqOrdDt(LocalDate.now());
//        remittanceDTO.setReqOrdTm(LocalTime.now());
        remittanceDTO.setReqOrdNo("*********");
        remittanceDTO.setCorpBusTyp(CorpBusTyp.REMITTANCE);
        remittanceDTO.setCorpBusSubTyp(CorpBusSubTyp.REMITTANCE);
        remittanceDTO.setCrdAcTyp("D");
        remittanceDTO.setCrdCorpOrg("ICBC");
        remittanceDTO.setOrdAmt(new BigDecimal("0.02"));
        remittanceDTO.setOrdCcy("01");
//        remittanceDTO.setUserId("1");
        remittanceDTO.setBnkPsnFlg("C");
        remittanceDTO.setMblNo("135654432342");
        remittanceDTO.setIdTyp("01");
        remittanceDTO.setCrdNoEnc("4343423423");
        remittanceDTO.setIdNoEnc("4342342342");
        remittanceDTO.setCrdUsrNm("李四");
//        remitService.remit(remittanceDTO);

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(remittanceDTO);
        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/remittance/order")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

    @Test
    public void orderResultQuery() throws Exception {
        RequestBuilder request = MockMvcRequestBuilders.get("/cpi/refund/orders/CP000000000000004001");
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

    @Test
    @SuppressWarnings("unchecked")
    public void reflect() throws Exception {
        Class clazz = Class.forName("com.hisun.lemon.cpi.bnkapi.icbc.FastpayICBCApi");
        Method method = clazz.getMethod("preBindCard",CardProtJrnDO.class);
        CardProtJrnDO cardProtJrnDO = new CardProtJrnDO();
        Object object = clazz.newInstance();
        method.invoke(object,cardProtJrnDO);
    }

    @Test
    public void addAccParam() {
        AccParamDO accParamDO = new AccParamDO();
        accParamDO.preInsert();
        accParamDO.preUpdate();
        accParamDO.setChkPmId(IdGenUtils.generateIdWithDateTime("CHK", "CHK", 3));
        accParamDO.setRutCorg("ICBC");
        accParamDO.setCorpBusTyp("05");
        accParamDO.setCorpBusSubTyp("0501");
        accParamDO.setEffFlg("Y");
        accParamDO.setChkDoFlg("Y");
        accParamDO.setChkBegTm(LocalTime.of(9, 30, 0));
        accParamDO.setChkEndTm(LocalTime.of(23, 30,0));
        accParamDO.setOprId("SYS001");
        iAccParamDao.insert(accParamDO);
    }

    /**
     * 生成对账批次
     */
    @Test
    public void registerCheckBat() {
        // 取上一个日期，表示哪天的数据参与对账
        LocalDate checkDt = DateTimeUtils.getCurrentLocalDate().minusDays(1L);
        System.out.println("==================取上一个日期: " + DateTimeUtils.formatLocalDate(checkDt));
        iCheckMgrService.registerChkBatNo(checkDt);
        System.out.println("==================END==================");
    }

    /**
     * 执行所有对账批次
     */
    @Test
    public void startCheckBat() {
        List<AccParamDO> AccParamList = iAccParamDao.queryEffAccParamList();
        if(CollectionUtils.isNotEmpty(AccParamList)){
            for (AccParamDO accParamDO : AccParamList) {
                AccControlDO accControlDO = iAccControlDao.queryUnfinishedAccControl(accParamDO);
                if (JudgeUtils.isNotNull(accControlDO)) {
                    iCheckMgrService.beginCheckAcc(accControlDO);
                }
            }
        }
    }

    /**
     * 执行指定的对账批次
     */
    @Test
    public void startOneCheckBat() {
        AccControlDO accControlDO = iAccControlDao.get("CHK2017113009583433005");
        iCheckMgrService.beginCheckAcc(accControlDO);
        System.out.println("==================END==================");
    }

    @Test

    public void addAccCfgInfo() {
        AccRefundCfgDO accCfgDO = new AccRefundCfgDO();
        accCfgDO.preInsert();
        accCfgDO.preUpdate();
        accCfgDO.setAccCfgId(IdGenUtils.generateIdWithDateTime("CPI", 6));
        accCfgDO.setRutCorg("ICBC");
        accCfgDO.setCorpBusTyp("05");
        accCfgDO.setCorpBusSubTyp("0501");
        accCfgDO.setChkClazz("com.hisun.lemon.cpi.bnkapi.icbc.CheckAccICBCRefundApi");
        accCfgDO.setGetFileMethod("getCheckFile");
        accCfgDO.setImportFields("companyDate|companySerino|trxType|bankSerno|settleDate|cardNo|payAmt|currType|status");
        accCfgDO.setImportClass("com.hisun.lemon.cpi.entity.icbc.AccIcbcShortcutDO");
        accCfgDO.setImportMethod("batchInsertCheckDO");
        accCfgDO.setSplitSign("\\|");
        accCfgDO.setChkFilePath("E:\\chkfile");
        accCfgDO.setContinueNum(1);
        accCfgDO.setSelectDetailMethod("getAccDOList");
        accCfgDO.setSuccessFlag("2");
        accCfgDO.setCheckKeyFiled("companySerino");
        accCfgDO.setCheckKeyBakFiled("");
        accCfgDO.setCheckAmtFiled("payAmt");
        accCfgDO.setTxStsFiled("status");
        accCfgDO.setQueryNum(200);
        accCfgDO.setUpdateMethod("updateAccDOChkSts");
        accRefundCfgDao.insert(accCfgDO);
    }

    @Test
    public void addtionalOrderChk() {
        GenericDTO<CheckDTO> genericDTO = new GenericDTO<>();
        CheckDTO checkDTO = new CheckDTO();
        checkDTO.setChkKey("20170905170522035001");
        checkDTO.setChkAmt(BigDecimal.valueOf(10.00));
        checkDTO.setCorpBusTyp(CorpBusTyp.FASTPAY);
        checkDTO.setCorpBusSubTyp(CorpBusSubTyp.FASTPAY);
        checkDTO.setMainNo("ICBC");
        genericDTO.setBody(checkDTO);
        GenericRspDTO genericRspDTO = checkService.additionalOrder(genericDTO);
        System.out.println("=================================");
    }

    @Test
    public void writeCheckFileTest() throws Exception{
//        checkMgrService.writeCheckFile(LocalDate.now().minusDays(1),"CPI_FUND.dat","FUND");
        checkMgrService.writeCheckFile(LocalDate.now().minusDays(1),"CPI_REFUND_20170819.dat","CPI","REFUND");

    }

    /**
     * 用户扫码或商户扫码订单银行查询
     */
    @Test
    public void queryOrder() {
        FundPollParamDO fundPollParamDO = new FundPollParamDO();
        fundPollParamDO.setQryCycId("17");
        fundPollParamDO.setRutCorg(CpiConstants.WECHAT);
        fundPollParamDO.setCorpBusTyp("03");
        fundPollParamDO.setCorpBusSubTyp("0304");
        fundPollParamDO.setEffFlg("1");
        fundPollParamDO.setOrdPriLev(99);
        fundPollParamDO.setCycOrdCnt(10);
        fundPollParamDO.setBeforeTime(10);
        ebankpayService.additionalOrder(fundPollParamDO);
        System.out.println("=================================");
    }

    /**
     * 用户扫码或商户扫码退款订单发送到银行退款接口
     * @throws Exception
     */
    @Test
    public void refund() throws Exception {
        refundService.refundByTime();
    }

    /**
     * 用户扫码或商户扫码退款订单发送银行退款查询接口
     * @throws Exception
     */
    @Test
    public void refundAdditionalOrder() throws Exception {
        FundPollParamDO fundPollParamDO = new FundPollParamDO();
        fundPollParamDO.setQryCycId("16");
        fundPollParamDO.setRutCorg("WeChat");
        fundPollParamDO.setCorpBusTyp("05");
        fundPollParamDO.setCorpBusSubTyp("0502");
        fundPollParamDO.setEffFlg("1");
        fundPollParamDO.setOrdPriLev(99);
        fundPollParamDO.setCycOrdCnt(10);
        fundPollParamDO.setBeforeTime(10);
        refundService.refundAdditionalOrder(fundPollParamDO);
    }

    @Test
    public void testPair(){
        String str = "attach=product&bankUrl=http://219.135.153.39:9005/cpi/bestpay/placeOrder&barcode=510074665177486785&charset=02&cityCode=hongkou&jrnNo=********161415091502&loginNo=&merchantCert=4C428433C4560261A4FCA364755786E7&merchantId=*****************&merchantSign=4C428433C4560261A4FCA364755786E7&orderAmt=48&orderCcy=USD&orderId=********161415091502&productDesc=product&productId=04&proviceCode=shanghai&requestId=********&service=forAppBarcodePay&serviceCode=APP&signType=MD5&storeId=11&version=1.0";
        List<NameValuePair> nameValuePairs = resolveNameValuePair(str);
    }
    public List<NameValuePair> resolveNameValuePair(String str){
        if(StringUtils.isBlank(str)){
            return null;
        }
        return Stream.of(str.split("&")).map(nameValueStr -> {
            String[] nameValueArray = StringUtils.split(nameValueStr, "=", 2);
            //String[] nameValueArray = nameValueStr.split("=");
            System.out.println(nameValueArray[0] + " = " + nameValueArray[1]);
            return new BasicNameValuePair(nameValueArray[0], nameValueArray[1]);
        }).collect(Collectors.toList());
    }

    /**
     * 翼支付订单结果查询
     */
    @Test
    public void paymentQueryTest() {
        PaymentQueryReq paymentQueryReq = new PaymentQueryReq();
        paymentQueryReq.setOrderId("1171");
        paymentQueryReq.setMerchantId("*****************");
        paymentQueryReq.setRequestId("20170909");
        paymentQueryReq.setCharset("02");
        paymentQueryReq.setService(BestPayEnumCommon.EnumService.APP_ORDER_QUERY);
        paymentQueryReq.setSignType("MD5");
        paymentQueryReq.setVersion("1.0");
        PaymentQueryRsp rsp = bestPayApi.doSend(paymentQueryReq, BestPayEnumCommon.EnumSource.paymentQuery);
        System.out.println(rsp);
    }

    /**
     * 微信子商户申请
     */
    @Test
    public void subMerchantEntry() {
        SubMerchantEntryReq req = new SubMerchantEntryReq();
        req.setApp_id("wx9955355c79974cc6");
        req.setBusiness_category(WEIXINEnumCommon.EnumBusinessCategory.Other);
        req.setContact_email("<EMAIL>");
        req.setContact_name("张三");
        req.setContact_phone("***********");
        req.setMch_id("**********");
        req.setMerchant_introduction("当当网上商城");
        req.setMerchant_name("当当网上商城");
        req.setMerchant_shortname("当当网上商城");
        req.setOffice_phone("***********");
        req.setMerchant_remark("当当网上商城");
        SubMerchantEntryRsp rsp = weChatApi.doSend(req, WEIXINEnumCommon.EnumSource.submerchantEntry);
        System.out.println(rsp);
    }

    /**
     * 微信获取充值对账单接口
     */
    @Test
    public void weChatDownloadFundBill() {
        String chkFilPath = "E:\\chkfile";
        LocalDate checkDate = LocalDate.now().minusDays(1);
        weChatPayNewApi.getWeChatFundCheckFile(chkFilPath, checkDate);
        System.out.println("==================end==================");
    }

    /**
     * 微信获取退款对账单接口
     */
    @Test
    public void weChatDownloadRefundBill() {
        String chkFilPath = "E:\\chkfile";
        LocalDate checkDate = LocalDate.now().minusDays(7);
        weChatPayNewApi.getWeChatRefundCheckFile(chkFilPath, checkDate);
        System.out.println("==================end==================");
    }

    /**
     * 微信退款订单结果查询
     */
    @Test
    public void weChatRefundQuery() {
        RefundQueryReq req=new RefundQueryReq();
        req.setAppid("wx9955355c79974cc6");
        req.setMch_id("**********");
        req.setSub_mch_id("69269539");
        req.setOut_refund_no("20171129101649142503");
        req.setOut_trade_no("CPI20171129101601113501");
        RefundQueryRsp rsp = weChatApi.doSend(req, WEIXINEnumCommon.EnumSource.refundQuery);
        System.out.println("==================end==================");
    }

    /**
     * 微信下单支付结果通知
     */
    @Test
    public void placeOrderNotify() {
        String date_settle = "2017-11-23 00:00:00";
        LocalDateTime stlDateTime= DateTimeUtils.parseLocalDateTime(date_settle, "yyyy-MM-dd HH:mm:ss");
        LocalDate stlDate = stlDateTime.toLocalDate();
        System.out.println("==================end==================");
        String resultData = "<xml><appid><![CDATA[wx9955355c79974cc6]]></appid>\n" +
                "<bank_type><![CDATA[CFT]]></bank_type>\n" +
                "<cash_fee><![CDATA[6]]></cash_fee>\n" +
                "<cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n" +
                "<device_info><![CDATA[WEB]]></device_info>\n" +
                "<fee_type><![CDATA[USD]]></fee_type>\n" +
                "<is_subscribe><![CDATA[N]]></is_subscribe>\n" +
                "<mch_id><![CDATA[**********]]></mch_id>\n" +
                "<nonce_str><![CDATA[d749ae64cd114ffb831d4ef39d979056]]></nonce_str>\n" +
                "<openid><![CDATA[oiXyawjn7PT9unh7Yhz4teJ--UyE]]></openid>\n" +
                "<out_trade_no><![CDATA[20171130142555009008]]></out_trade_no>\n" +
                "<rate_value><![CDATA[*********]]></rate_value>\n" +
                "<result_code><![CDATA[SUCCESS]]></result_code>\n" +
                "<return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "<sign><![CDATA[A4555F36C0338D259C38BD2C15ADE7C1]]></sign>\n" +
                "<sub_mch_id><![CDATA[********]]></sub_mch_id>\n" +
                "<time_end><![CDATA[20171130152607]]></time_end>\n" +
                "<total_fee>1</total_fee>\n" +
                "<trade_type><![CDATA[JSAPI]]></trade_type>\n" +
                "<transaction_id><![CDATA[4200000043201711308040633197]]></transaction_id>\n" +
                "</xml>";
        WeChatOrderNotifyReq weChatOrderNotifyReq = XmlParseUtils.unmarshallerXmlToObject(resultData, WeChatOrderNotifyReq.class);
        System.out.println("==================end==================");

        WeChatOrderNotifyRsp weChatOrderNotifyRsp = new WeChatOrderNotifyRsp();
        weChatOrderNotifyRsp.setReturn_code(CpiConstants.WECHAT_SUCCESS);
        String responseData = XmlParseUtils.marshallerObjectToXmlStr(weChatOrderNotifyRsp, WeChatOrderNotifyRsp.class);
        System.out.println("==================end==================" + responseData);
    }

    /**
     * 获取平台在微信的结算明细数据
     */
    @Test
    public void ISettlementService() {
        settlementService.settlementQuery(CpiConstants.UNSETTLEMENT, CpiConstants.WECHAT, null, null);
        settlementService.settlementQuery(CpiConstants.SETTLEMENT, CpiConstants.WECHAT, null, null);
        System.out.println("==================end==================");
    }


    @Test
    public void refundOrderStatTest(){
        RefundRsp refundRsp = new RefundRsp();
        refundRsp.setMerchantId("5454");
        refundRsp.setOldJrnNo("");
        refundRsp.setOldOrderId("");
        refundRsp.setRefundCcy("USD");
        refundRsp.setRefundCcyAmt("20");
        refundRsp.setRefundSts("HANDLING");

        String retOrdSts = null;
        if(JudgeUtils.isNotNull(refundRsp.getRefundSts())) {
            retOrdSts = refundRsp.getRefundSts().getCode();
        }
        String txFlg = bestPayRetCdConvert.refundOrderResultConvert("", retOrdSts);
        System.out.print(txFlg);
    }

}