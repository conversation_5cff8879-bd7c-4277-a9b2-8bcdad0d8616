package com.hisun.lemon.cpi;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.service.IEbankpayService;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Created by gongle<PERSON> on 2017/7/6.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class EbankPayServiceTest {

    @Autowired
    private IEbankpayService ebankpayService;

    @Test
    public void contextLoads() throws Exception {
//        addFundPollParamSelective();
        updateFundPollParamSelective();
    }

    /**
     * 增加资金流入轮询查询结果补单参数
     * @param
     * @return
     */
    @Test
    public void addFundPollParam() {
        System.out.println("========= addFundPollParam() start =========");
        FundPollParamDO fundPollParamDO = new FundPollParamDO();
        fundPollParamDO.preInsert();
        fundPollParamDO.preUpdate();
        fundPollParamDO.setQryCycId(DateTimeUtils.getCurrentDateTimeStr());
        fundPollParamDO.setRutCorg("ICBC");
        fundPollParamDO.setCorpBusTyp("01");
        fundPollParamDO.setCorpBusSubTyp("0101");
        fundPollParamDO.setOrdPriLev(6);
        fundPollParamDO.setCycOrdCnt(10);
        fundPollParamDO.setBeforeTime(600);
        fundPollParamDO.setOprId("SYS00001");
        fundPollParamDO.setRmk("测试专用");
//        boolean isSuccess = ebankpayService.addFundPollParam(fundPollParamDO);
//        System.out.println("========= addFundPollParam() end isSuccess = " + isSuccess);
    }

    /**
     * 增加资金流入轮询查询结果补单参数,取不为null的字段值
     * @param
     * @return
     */
    @Test
    public void addFundPollParamSelective() {
        System.out.println("========= addFundPollParamSelective() start =========");
        FundPollParamDO fundPollParamDO = new FundPollParamDO();
        fundPollParamDO.preInsert();
        fundPollParamDO.preUpdate();
        fundPollParamDO.setQryCycId(DateTimeUtils.getCurrentDateTimeStr());
        fundPollParamDO.setRutCorg("CMB");
        fundPollParamDO.setCorpBusTyp("01");
        fundPollParamDO.setCorpBusSubTyp("0101");
        fundPollParamDO.setOprId("SYS00002");
        fundPollParamDO.setRmk("测试addFundPollParamSelective");
//        boolean isSuccess = ebankpayService.addFundPollParamSelective(fundPollParamDO);
//        System.out.println("========= addFundPollParamSelective() end isSuccess = " + isSuccess);
    }

    /**
     * 根据主键查询资金流入轮询查询结果补单参数
     * @param
     * @return
     */
    @Test
    public void getFundPollParamByPrimaryKey() {
        System.out.println("========= getFundPollParamByPrimaryKey() start =========");
        String qryCycId = "**************";
//        FundPollParamDO fundPollParamDO = ebankpayService.getFundPollParamByPrimaryKey(qryCycId);
//        System.out.println("========= getFundPollParamByPrimaryKey() end isSuccess = " + (null != fundPollParamDO));
    }

    /**
     * 根据唯一索引查询资金流入轮询查询结果补单参数
     * @param
     * @return
     */
    @Test
    public void getFundPollParam() {
        System.out.println("========= getFundPollParam() start =========");
        FundPollParamDO fundPollParamDO = new FundPollParamDO();
        fundPollParamDO.setRutCorg("CMBC");
        fundPollParamDO.setCorpBusTyp("01");
        fundPollParamDO.setCorpBusSubTyp("0101");
//        FundPollParamDO fundPollParamDO1 = ebankpayService.getFundPollParam(fundPollParamDO);
//        System.out.println("========= getFundPollParam() end isSuccess = " + (null != fundPollParamDO1));
    }

    /**
     * 根据主键更新资金流入轮询查询结果补单参数
     * @param
     * @return
     */
    @Test
    public void updateFundPollParamByPrimaryKey() {
        System.out.println("========= updateFundPollParamByPrimaryKey() start =========");
        FundPollParamDO fundPollParamDO = new FundPollParamDO();
        fundPollParamDO.preInsert();
        fundPollParamDO.preUpdate();
        fundPollParamDO.setQryCycId("**************");
        fundPollParamDO.setRutCorg("CMBC");
        fundPollParamDO.setCorpBusTyp("01");
        fundPollParamDO.setCorpBusSubTyp("0101");
        fundPollParamDO.setOprId("SYS00003");
        fundPollParamDO.setOrdPriLev(9);
        fundPollParamDO.setCycOrdCnt(100);
        fundPollParamDO.setBeforeTime(1800);
        fundPollParamDO.setRmk("test updateFundPollParamByPrimaryKey");
//        boolean isSuccess = ebankpayService.updateFundPollParamByPrimaryKey(fundPollParamDO);
//        System.out.println("========= updateFundPollParamByPrimaryKey() end isSuccess = " + isSuccess);
    }

    /**
     * 根据主键更新资金流入轮询查询结果补单参数,取不为null的字段值
     * @param
     * @return
     */
    @Test
    public void updateFundPollParamSelective() {
        System.out.println("========= updateFundPollParamSelective() start =========");
        FundPollParamDO fundPollParamDO = new FundPollParamDO();
        fundPollParamDO.preInsert();
        fundPollParamDO.preUpdate();
        fundPollParamDO.setQryCycId("**************");
        fundPollParamDO.setRutCorg("CMBC");
        fundPollParamDO.setCorpBusTyp("01");
        fundPollParamDO.setCorpBusSubTyp("0101");
        fundPollParamDO.setOprId("SYS00005");
        fundPollParamDO.setOrdPriLev(8);
        fundPollParamDO.setCycOrdCnt(500);
        fundPollParamDO.setBeforeTime(3700);
        fundPollParamDO.setRmk("test updateFundPollParamSelective");
//        boolean isSuccess = ebankpayService.updateFundPollParamSelective(fundPollParamDO);
//        System.out.println("========= updateFundPollParamSelective() end isSuccess = " + isSuccess);
    }

    /**
     * 删除资金流入轮询查询结果补单参数
     * @param
     * @return
     */
    @Test
    public void removeFundPollParamByPrimaryKey() {
        System.out.println("========= removeFundPollParamByPrimaryKey() start =========");
        String qryCycId = "**************";
//        boolean isSuccess = ebankpayService.removeFundPollParamByPrimaryKey(qryCycId);
//        System.out.println("========= removeFundPollParamByPrimaryKey() end isSuccess = " + isSuccess);
    }

    @Test
    public void replaceCharTest() {
        String content = "`2014-11-1016：33：45,`wx2421b1c4370ec43b,`********,`0,`1000,`1001690740201411100005734289,`**********";
        String[] contentArray = content.split(",");
        System.out.println(contentArray.length);

        String contentNew = content.replace("`", "");
        System.out.println(contentNew);
    }

}
