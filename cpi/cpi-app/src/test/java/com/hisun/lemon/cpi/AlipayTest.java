package com.hisun.lemon.cpi;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpi.bnkapi.alipay.AlipayApi;
import com.hisun.lemon.cpi.dao.IAccControlDao;
import com.hisun.lemon.cpi.dto.EbankpayReqDTO;
import com.hisun.lemon.cpi.entity.AccControlDO;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.ICheckMgrService;
import com.hisun.lemon.cpi.service.IEbankpayService;
import com.hisun.lemon.framework.data.GenericDTO;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Rui on 2017/7/5.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class AlipayTest {

    @Autowired
    private WebApplicationContext context;

    @Resource
    private AlipayApi alipayApi;

    @Resource
    ICheckMgrService iCheckMgrService;

    @Resource
    IAccControlDao iAccControlDao;

    private IEbankpayService ebankpayService;

    private FundPollParamDO fundPollParamDO;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }


    /**
     * 支付宝获取充值对账单接口
     */
    @Test
    public void alipayDownloadFundBill() {
/*        String chkFilPath = "E:\\chkfile";
        LocalDate checkDate = LocalDate.now().minusDays(3);
        alipayApi.getAlipayFundCheckFile(chkFilPath,checkDate);
        System.out.println("==================end==================");*/

        Map queryMap=new HashMap<>();
		queryMap.put("nowTm", DateTimeUtils.getCurrentLocalDateTime().minusDays(1));
        System.out.println("**********"+queryMap.get("nowTm"));
    }


    /**
     * 生成对账批次
     */
    @Test
    public void registerCheckBat() {
        // 取上一个日期，表示哪天的数据参与对账
        LocalDate checkDt = DateTimeUtils.getCurrentLocalDate().minusDays(-1L);
        System.out.println("==================取上一个日期: " + DateTimeUtils.formatLocalDate(checkDt));
        iCheckMgrService.registerChkBatNo(checkDt);
        System.out.println("==================END==================");
    }


    /**
     * 执行指定的对账批次： 指定批次列中有资金机构和业务类型(03:充值  05：退款)来决定是生成充值文件还是退款对账文件
     */
    @Test
    public void startOneCheckBat() {
        AccControlDO accControlDO = iAccControlDao.get("CHK201801101301338008");
        iCheckMgrService.beginCheckAcc(accControlDO);
        System.out.println("==================END==================");
    }


    @Test
    public void startOneadditionalOrderCheckTest() {
        FundPollParamDO fundPollParamDO = new FundPollParamDO();
        fundPollParamDO.setCorpBusTyp("03");
        fundPollParamDO.setCorpBusSubTyp("0304");
        fundPollParamDO.setRutCorg("ALIPAY");
        fundPollParamDO.setBeforeTime(10);
        fundPollParamDO.setCycOrdCnt(10);
        fundPollParamDO.setEffFlg("1");
        ebankpayService.additionalOrder(fundPollParamDO);
        System.out.println(">>>>>>>>>>>>>>>>>");
    }

    @Test
    public void ebankpay() throws Exception{
        EbankpayReqDTO ebankpayDTO = new EbankpayReqDTO();
        ebankpayDTO.setReqOrdDt(LocalDate.now());
        ebankpayDTO.setReqOrdTm(LocalTime.now());
        ebankpayDTO.setReqOrdNo("****************");
        ebankpayDTO.setCorpBusTyp(CorpBusTyp.EBANKPAY);
        ebankpayDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANKPAY);
        ebankpayDTO.setCrdAcTyp("U");
        ebankpayDTO.setCrdCorpOrg("ALIPAY");
        ebankpayDTO.setOrdAmt(new BigDecimal("0.01"));
        ebankpayDTO.setOrdCcy("USD");
        //用户二维码
        ebankpayDTO.setAuthCode("287523257080542312");
//        ebankpayDTO.setUserId("1");
        ebankpayDTO.setBnkPsnFlg("C");
        ebankpayDTO.setUserTyp("M");
//        ebankPayService.ebankpay(ebankpayDTO);
        GenericDTO<EbankpayReqDTO> genericDTO = new GenericDTO<>();
        genericDTO.setBody(ebankpayDTO);

        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        java.lang.String requestJson = ow.writeValueAsString(genericDTO);
        RequestBuilder request = MockMvcRequestBuilders.post("/cpi/ebank/order")
                .contentType(MediaType.APPLICATION_JSON_UTF8).content(requestJson);
        MvcResult mvcResult = mockMvc.perform(request).andReturn();
        int status = mvcResult.getResponse().getStatus();
        String content = mvcResult.getResponse().getContentAsString();

        System.out.println("================end=============");
    }

    @Test
    public void alipaySettle(){
        alipayApi.alipaySettlementQuery(1,"********");
    }

    @Test
    public void dateStr(){
        String dateStr = "********";
        StringBuilder sb = new StringBuilder();
        sb.append(dateStr.substring(0,4)).append("-").append(dateStr.substring(6,7)).append("-").append(dateStr.substring(9));
        System.out.println(sb.toString());
    }

}