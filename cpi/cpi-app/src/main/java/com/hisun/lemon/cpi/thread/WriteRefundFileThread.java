package com.hisun.lemon.cpi.thread;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpi.service.ICheckMgrService;
import com.hisun.lemon.framework.lock.DistributedLocker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;

/**
 * 对账服务线程
 */
public class WriteRefundFileThread implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(WriteFundFileThread.class);

    /**
     * redis锁
     */
    private DistributedLocker distributedLocker;
    /**
     * 对账服务
     */
    private ICheckMgrService checkMgrService;

    /**
     * 线程中不能用注解来注入service服务等
     */
    public WriteRefundFileThread() {
        super();
    }

    public WriteRefundFileThread(ICheckMgrService checkMgrService, DistributedLocker distributedLocker) {
        this.checkMgrService = checkMgrService;
        this.distributedLocker = distributedLocker;
    }

    /**
     * 执行对账服务，传入对象参数DO
     */
    @Override
    public void run() {
        try {
            //锁名
            String lockName = "CPI_WRITE_REFUND_FILE";
            //释放锁的时间（异常情况下最长的释放锁的时间，单位秒）
            int leaseTime = 40*60;
            //获取应用锁的时间，时间设置必须短，否则影响后面的线程获取锁（单位秒）
            int waitTime = 30;
            //对账日期
            LocalDate chkFilDt = DateTimeUtils.getCurrentLocalDate().minusDays(1L);
            //对账文件名
            String chkFilNm = "CPI_REFUND_"+DateTimeUtils.formatLocalDate(chkFilDt)+".dat";
            distributedLocker.lock(lockName, leaseTime, waitTime, () -> {
                checkMgrService.writeCheckFile(chkFilDt,chkFilNm,"CPI","REFUND");
                return null;
            });
        } catch (Exception e) {
            logger.error("写入退款对账文件定时任务执行失败，异常为 ", e);
            return ;
        }
    }
}
