package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * Created by Rui on 2017/7/12.
 */
public interface IRemitService {

    /**
     * 汇款充值登记
     */
    GenericRspDTO<RemittanceRspDTO> remit(GenericDTO<RemittanceReqDTO> genericDTO);

    /**
     * 汇款订单查询
     */
    GenericRspDTO<OrderResultRspDTO> queryOrder(String ordNo);

    /**
     * 汇款订单确认
     */
    GenericRspDTO<NoBody> payOrder(GenericDTO<RemittanceConfirmDTO> genericDTO);

    /**
     * 营业厅充值登记
     */
    GenericRspDTO<RemittanceRspDTO> hallRemit(GenericDTO<RemittanceReqDTO> genericDTO);
}
