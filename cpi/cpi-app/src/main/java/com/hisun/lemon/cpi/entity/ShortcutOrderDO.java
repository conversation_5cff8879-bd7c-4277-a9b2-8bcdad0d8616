/*
 * @ClassName ShortcutOrderDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-17 19:05:34
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class ShortcutOrderDO extends BaseDO {
    /**
     * @Fields subOrdNo 内部子订单号
     */
    private String subOrdNo;
    /**
     * @Fields fndOrdNo 内部订单号
     */
    private String fndOrdNo;
    /**
     * @Fields ordDt 订单日期
     */
    private LocalDate ordDt;
    /**
     * @Fields ordTm 订单时间
     */
    private LocalTime ordTm;
    /**
     * @Fields acDt 记账日期
     */
    private LocalDate acDt;
    /**
     * @Fields agrFlg 是否签约支付，Y：签约支付，N：免签约支付
     */
    private String agrFlg;
    /**
     * @Fields agrNo 内部协议号
     */
    private String agrNo;
    /**
     * @Fields crdCorpOrg 资金合作机构
     */
    private String crdCorpOrg;
    /**
     * @Fields rutCorpOrg 路径合作机构
     */
    private String rutCorpOrg;
    /**
     * @Fields corpBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields crdAcTyp 卡种，D贷记卡，C借记卡
     */
    private String crdAcTyp;
    /**
     * @Fields ordAmt 订单金额
     */
    private BigDecimal ordAmt;
    /**
     * @Fields ordSts 订单状态
     */
    private String ordSts;
    /**
     * @Fields crdCvv2Enc CVV2
     */
    private String crdCvv2Enc;
    /**
     * @Fields crdExpDtEnc 有效期
     */
    private String crdExpDtEnc;
    /**
     * @Fields smsChkFlg 是否校验短信验证码，Y：是，N：否
     */
    private String smsChkFlg;
    /**
     * @Fields smsJrnNo 短信校验成功流水号
     */
    private String smsJrnNo;
    /**
     * @Fields orgOrdNo 合作机构外部订单号（系统生成）
     */
    private String orgOrdNo;
    /**
     * @Fields orgJrnNo 合作机构返回的流水号
     */
    private String orgJrnNo;
    /**
     * @Fields orgOrdDt 合作机构交易日期
     */
    private LocalDate orgOrdDt;
    /**
     * @Fields orgOrdTm 合作机构交易时间
     */
    private LocalTime orgOrdTm;
    /**
     * @Fields orgRspCd 合作机构返回码
     */
    private String orgRspCd;
    /**
     * @Fields orgRspMsg 合作机构返回信息
     */
    private String orgRspMsg;
    /**
     * @Fields chkKey 对账键值
     */
    private String chkKey;
    /**
     * @Fields chkFlg 对账标志，0：需要对账，1：不需要对账
     */
    private String chkFlg;
    /**
     * @Fields chkSts 对账状态，0：未对账，1：对账成功，2：我方有对方无，3：对方有我方无，4：金额错，5：存疑
     */
    private String chkSts;
    /**
     * @Fields chkDt 对账日期
     */
    private LocalDate chkDt;
    /**
     * @Fields chkTm 对账时间
     */
    private LocalTime chkTm;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getSubOrdNo() {
        return subOrdNo;
    }

    public void setSubOrdNo(String subOrdNo) {
        this.subOrdNo = subOrdNo;
    }

    public String getFndOrdNo() {
        return fndOrdNo;
    }

    public void setFndOrdNo(String fndOrdNo) {
        this.fndOrdNo = fndOrdNo;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getAgrFlg() {
        return agrFlg;
    }

    public void setAgrFlg(String agrFlg) {
        this.agrFlg = agrFlg;
    }

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getCrdCvv2Enc() {
        return crdCvv2Enc;
    }

    public void setCrdCvv2Enc(String crdCvv2Enc) {
        this.crdCvv2Enc = crdCvv2Enc;
    }

    public String getCrdExpDtEnc() {
        return crdExpDtEnc;
    }

    public void setCrdExpDtEnc(String crdExpDtEnc) {
        this.crdExpDtEnc = crdExpDtEnc;
    }

    public String getSmsChkFlg() {
        return smsChkFlg;
    }

    public void setSmsChkFlg(String smsChkFlg) {
        this.smsChkFlg = smsChkFlg;
    }

    public String getSmsJrnNo() {
        return smsJrnNo;
    }

    public void setSmsJrnNo(String smsJrnNo) {
        this.smsJrnNo = smsJrnNo;
    }

    public String getOrgOrdNo() {
        return orgOrdNo;
    }

    public void setOrgOrdNo(String orgOrdNo) {
        this.orgOrdNo = orgOrdNo;
    }

    public String getOrgJrnNo() {
        return orgJrnNo;
    }

    public void setOrgJrnNo(String orgJrnNo) {
        this.orgJrnNo = orgJrnNo;
    }

    public LocalDate getOrgOrdDt() {
        return orgOrdDt;
    }

    public void setOrgOrdDt(LocalDate orgOrdDt) {
        this.orgOrdDt = orgOrdDt;
    }

    public LocalTime getOrgOrdTm() {
        return orgOrdTm;
    }

    public void setOrgOrdTm(LocalTime orgOrdTm) {
        this.orgOrdTm = orgOrdTm;
    }

    public String getOrgRspCd() {
        return orgRspCd;
    }

    public void setOrgRspCd(String orgRspCd) {
        this.orgRspCd = orgRspCd;
    }

    public String getOrgRspMsg() {
        return orgRspMsg;
    }

    public void setOrgRspMsg(String orgRspMsg) {
        this.orgRspMsg = orgRspMsg;
    }

    public String getChkKey() {
        return chkKey;
    }

    public void setChkKey(String chkKey) {
        this.chkKey = chkKey;
    }

    public String getChkFlg() {
        return chkFlg;
    }

    public void setChkFlg(String chkFlg) {
        this.chkFlg = chkFlg;
    }

    public String getChkSts() {
        return chkSts;
    }

    public void setChkSts(String chkSts) {
        this.chkSts = chkSts;
    }

    public LocalDate getChkDt() {
        return chkDt;
    }

    public void setChkDt(LocalDate chkDt) {
        this.chkDt = chkDt;
    }

    public LocalTime getChkTm() {
        return chkTm;
    }

    public void setChkTm(LocalTime chkTm) {
        this.chkTm = chkTm;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}