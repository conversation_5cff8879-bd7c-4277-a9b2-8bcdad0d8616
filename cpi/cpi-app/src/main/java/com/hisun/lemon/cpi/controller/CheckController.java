package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.dto.CheckDTO;
import com.hisun.lemon.cpi.service.ICheckService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/cpi/chk")
@Api(tags="CheckController", description="提供给对账通知服务")
public class CheckController extends BaseController {

    @Resource
    ICheckService checkService;

    @ApiOperation(value="补单", notes="补单")
    @ApiResponse(code = 200, message = "补单结果")
    @PostMapping("/addition")
    GenericRspDTO<NoBody> additionalOrder(@Validated @RequestBody GenericDTO<CheckDTO> genericDTO){
        return checkService.additionalOrder(genericDTO);
    }

    /**
     * 撤单
     * @param genericDTO
     */
    @ApiOperation(value="撤单", notes="撤单")
    @ApiResponse(code = 200, message = "撤单结果")
    @PostMapping("/canceled")
    GenericRspDTO<NoBody> cancelOrder(@Validated @RequestBody GenericDTO<CheckDTO> genericDTO){
        return checkService.cancelOrder(genericDTO);
    }

    /**
     * 差错取消
     * @param genericDTO
     */
    @ApiOperation(value="差错取消", notes="差错取消")
    @ApiResponse(code = 200, message = "差错取消结果")
    @PostMapping("/error")
    GenericRspDTO<NoBody> cancelError(@Validated @RequestBody GenericDTO<CheckDTO> genericDTO){
        return checkService.cancelError(genericDTO);
    }

    /**
     * 对平处理
     * @param genericDTO
     */
    @ApiOperation(value="对平处理", notes="对平处理")
    @ApiResponse(code = 200, message = "对平处理结果")
    @PostMapping("/match")
    GenericRspDTO<NoBody> matchHandle(@Validated @RequestBody GenericDTO<CheckDTO> genericDTO){
        return checkService.matchHandle(genericDTO);
    }
}
