package com.hisun.lemon.cpi.bnkapi.wechat;

import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon;
import org.springframework.stereotype.Component;

/**
 * User : Rui
 * Date : 2017/9/6
 * Time : 14:49
 **/
@Component("weChatRetCdConvert")
public class WeChatRetCdConvert {

    public CpiMsgCd Convert(String retCode){
        switch (retCode){
            case "0000":
            case "000000":
            case "31":
                return CpiMsgCd.SUCCESS;
            case "2007":
                return CpiMsgCd.BNK_70101;
            case "22":
                return CpiMsgCd.BNK_70102;
            case "32":
                return CpiMsgCd.BNK_70200;
            default:
                return CpiMsgCd.BNK_70200;
        }
    }

    /**
     * 扫码下单返回码结果转义
     * @param txFlg
     * @return
     */
    public CpiMsgCd txFlgConvert(String txFlg){
        switch (txFlg) {
            case "S":
                return CpiMsgCd.SUCCESS;
            case "W":
                return CpiMsgCd.BNK_70102;
            case "F":
                return CpiMsgCd.BNK_70200;
            default:
                return CpiMsgCd.BNK_70102;
        }
    }

    public WEIXINEnumCommon.EnumBusinessCategory busCategoryConvert(String busCategory){
        switch (busCategory){
            case "343":
                return WEIXINEnumCommon.EnumBusinessCategory.Shoes_Garments;
            case "493":
                return WEIXINEnumCommon.EnumBusinessCategory.Air_Ticket;
            case "492":
                return WEIXINEnumCommon.EnumBusinessCategory.Office_Supplies;
            case "491":
                return WEIXINEnumCommon.EnumBusinessCategory.Hotel_Industry;
            case "490":
                return WEIXINEnumCommon.EnumBusinessCategory.Education_Industry;
            case "489":
                return WEIXINEnumCommon.EnumBusinessCategory.Logistics;
            case "488":
                return WEIXINEnumCommon.EnumBusinessCategory.Digital_Appliance;
            case "487":
                return WEIXINEnumCommon.EnumBusinessCategory.Maternal_Infant;
            case "486":
                return WEIXINEnumCommon.EnumBusinessCategory.Cosmetics;
            case "485":
                return WEIXINEnumCommon.EnumBusinessCategory.Food;
            case "484":
                return WEIXINEnumCommon.EnumBusinessCategory.Comprehensive_mall;
            case "494":
                return WEIXINEnumCommon.EnumBusinessCategory.Other;
            default:
                return WEIXINEnumCommon.EnumBusinessCategory.Other;
        }
    }
}
