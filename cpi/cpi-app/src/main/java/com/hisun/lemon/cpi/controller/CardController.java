package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpi.dto.CardBinRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.ICardService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Created by Rui on 2017/7/5.
 */
@RestController
@RequestMapping("/cpi/cards")
@Api(tags="CardController", description="卡协议信息服务")
public class CardController extends BaseController {

    /**
     * 卡服务
     */
    @Resource
    private ICardService cardService;

    @ApiOperation(value="根据用户的内部用户号查找该用户的绑卡信息", notes="根据用户的内部用户号查找该用户的绑卡信息")
    @ApiResponse(code = 200, message = "绑卡信息")
    @GetMapping("/userId")
    public GenericRspDTO<AgrInfoRspDTO> queryCardsInfo(@Validated @ApiParam(name = "corpBusTyp", value = "业务类型", required = true) @RequestParam(value = "corpBusTyp") CorpBusTyp corpBusTyp,
                                                       @Validated @ApiParam(name = "corpBusSubTyp", value = "业务子类型", required = true) @RequestParam(value = "corpBusSubTyp") CorpBusSubTyp corpBusSubTyp,
                                                       @ApiParam(name = "userNo", value = "用户id,如果userId取不到的情况取这个值", required = false) @RequestParam(value = "userNo", required = false) String userNo,
                                                       @ApiParam(name = "crdAcTyp", value = "卡种,D借记卡，C贷记卡，非必输，不输查询所有", required = false) @RequestParam(value = "crdAcTyp", required = false) String crdAcTyp) {
        return cardService.queryCardsInfo(corpBusTyp,corpBusSubTyp,userNo,crdAcTyp);
    }

    @ApiOperation(value="根据卡号查找所属银行", notes="根据卡号查找所属银行")
    @ApiResponse(code = 200, message = "所属卡归属行信息")
    @GetMapping("/head")
    public GenericRspDTO<CardBinRspDTO> queryCardBin(@Validated @ApiParam(name = "crdNo", value = "明文卡号", required = true, type = "String") @RequestParam(value = "crdNo") String crdNo) {
        return cardService.queryCardBin(crdNo);
    }

    @ApiOperation(value="校验卡号", notes="校验卡号")
    @ApiResponse(code = 200, message = "校验结果")
    @GetMapping("/verification")
    public GenericRspDTO<NoBody> verifyCard(@Validated @ApiParam(name = "agrNo", value = "内部协议号", required = true) @RequestParam(value = "agrNo") String agrNo,
                                            @Validated @ApiParam(name = "crdNo", value = "明文卡号", required = true, type = "String") @RequestParam(value = "crdNo") String crdNo) {

        return cardService.verifyCard(agrNo,crdNo);
    }

}
