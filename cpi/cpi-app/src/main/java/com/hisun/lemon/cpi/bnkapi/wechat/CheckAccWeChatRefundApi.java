package com.hisun.lemon.cpi.bnkapi.wechat;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.wechat.IAccWeChatEbankpayRefundDao;
import com.hisun.lemon.cpi.entity.AccControlDO;
import com.hisun.lemon.cpi.entity.wechat.AccWeChatEbankpayRefundDO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 微信网银退款业务对账操作 API
 */
@Component
public class CheckAccWeChatRefundApi {

    private static final Logger logger = LoggerFactory.getLogger(CheckAccWeChatRefundApi.class);

    @Resource
    private WeChatPayNewApi weChatPayNewApi;

    @Resource
    private IAccWeChatEbankpayRefundDao accWeChatEbankpayRefundDao;

    /**
     * 1.1 微信扫码支付退款对账：获取退款对账文件
     */
    public String getRefundCheckFile(String chkFilPath, LocalDate chkDt) throws Exception{
        return weChatPayNewApi.getWeChatRefundCheckFile(chkFilPath, chkDt);
    }

    /**
     * 1.2 微信扫码支付退款对账：解析对账文件，对账明细批量入库
     */
    public void batchInsertCheckDO(List<Object> checkDOList, AccControlDO accControlDO) {
        logger.debug("==================微信扫码支付退款对账，解析对账文件==================");

        //step1：根据对账批次号查询一条记录，判断该批次是否已插入对账明细数据
        String chkBatNo = accControlDO.getChkBatNo();
        int totalNum = accWeChatEbankpayRefundDao.getAccRefundDetailCount(chkBatNo);
        if (totalNum > 0) {
            logger.error("该批次已录入银行对账明细，请重新确认，对账批次号: " + chkBatNo);
            throw new LemonException(CpiMsgCd.IMPORT_CHECK_FILE_REPEATED.getMsgCd());
        }

        //step2：批次插入银行对账明细
        AccWeChatEbankpayRefundDO accRefundDO;
        List<AccWeChatEbankpayRefundDO> refundDOList = new ArrayList<>();
        Integer filTotCnt = 0;
        BigDecimal filTotAmt = BigDecimal.ZERO;
        BigDecimal amtFormat = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(checkDOList)) {
            //循环获取文件明细的金额，并统计对账总金额和总笔数
            String chkId;
            for (Object checkAccDO : checkDOList) {
                chkId = IdGenUtils.generateIdWithDateTime("CPI", 6);
                accRefundDO = (AccWeChatEbankpayRefundDO)checkAccDO;
                accRefundDO.setChkId(chkId);
                amtFormat = accRefundDO.getRefundFee();
                refundDOList.add(accRefundDO);
                filTotAmt = filTotAmt.add(amtFormat);
                filTotCnt++;
            }
            //记录数大于200，分批量进行插入
            if (refundDOList.size() > CpiConstants.MAX_BATCH_NUM) {
                List<AccWeChatEbankpayRefundDO> tmpList = new ArrayList<>();
                for (AccWeChatEbankpayRefundDO checkAccDO : refundDOList) {
                    if (tmpList.size() == CpiConstants.MAX_BATCH_NUM) {
                        accWeChatEbankpayRefundDao.batchInsertCheckDO(tmpList);
                        tmpList.clear();
                    }
                    tmpList.add(checkAccDO);
                }
                if (tmpList.size() > 0) {
                    accWeChatEbankpayRefundDao.batchInsertCheckDO(tmpList);
                }
            } else {
                //记录数小于200，直接批量插入
                if(CollectionUtils.isNotEmpty(refundDOList)) {
                    accWeChatEbankpayRefundDao.batchInsertCheckDO(refundDOList);
                }
            }
        }
        //更新文件总笔数和总金额
        accControlDO.setFilTotAmt(filTotAmt);
        accControlDO.setFilTotCnt(filTotCnt);
    }

    /**
     * 1.3 微信扫码支付退款对账：获取银行对账明细 List
     * @param chkBatNo 对账批次号
     * @param txSts 银行明细交易状态
     * @param beginNum 从表中第几行开始取值
     * @param countNum 最大读取多少行记录
     */
    public List<AccWeChatEbankpayRefundDO> getAccDOList(String chkBatNo, String txSts, Integer beginNum, Integer countNum) {
        return accWeChatEbankpayRefundDao.getAccRefundDetailList(chkBatNo, txSts, beginNum, countNum);
    }

    /**
     * 1.4 微信扫码支付退款对账：更新银行明细的对账状态
     * @param checkKey 银行明细的主键，取机构的交易流水号
     * @param txSts 银行明细的交易状态
     * @param chkSts 银行明细的对账状态
     */
    public void updateAccDOChkSts(String checkKey, String txSts, String chkSts) {
        accWeChatEbankpayRefundDao.updateAccRefundChkSts(checkKey, txSts, chkSts);
    }
}
