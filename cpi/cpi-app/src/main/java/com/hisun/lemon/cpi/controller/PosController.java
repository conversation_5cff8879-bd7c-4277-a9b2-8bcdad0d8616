package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.service.IPosService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * User : Rui
 * Date : 2017/10/16
 * Time : 15:55
 **/
@RestController
@RequestMapping("/cpi/pos")
@Api(tags="PosController", description="POS服务")
public class PosController extends BaseController {

    @Resource
    IPosService posService;

    @ApiOperation(value="余额查询", notes="余额查询")
    @ApiResponse(code = 200, message = "余额查询结果")
    @PostMapping("/balance")
    public GenericRspDTO<PosBalanceRspDTO> balanceQuery(@Validated @RequestBody GenericDTO<PosBalanceReqDTO> genericDTO) {
        return posService.balanceQuery(genericDTO);
    }

    @ApiOperation(value="消费", notes="消费")
    @ApiResponse(code = 200, message = "消费返回结果")
    @PostMapping("/payment")
    public GenericRspDTO<PosPayRspDTO> payment(@Validated @RequestBody GenericDTO<PosPayReqDTO> genericDTO) {
        return posService.payment(genericDTO);
    }

    @ApiOperation(value="预授权交易", notes="预授权交易")
    @ApiResponse(code = 200, message = "预授权返回结果")
    @PostMapping("/authorization")
    public GenericRspDTO<PosPreAuRspDTO> preAuthorization(@Validated @RequestBody GenericDTO<PosPreAuReqDTO> genericDTO) {
        return posService.preAuthorization(genericDTO);
    }
}
