package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;

/**
 * 资金流入业务Service实现
 * 网银下单及查询
 */
public interface IEbankpayService {

    /**
     * 网银下单
     */
    GenericRspDTO<EbankpayRspDTO> ebankpay(GenericDTO<EbankpayReqDTO> genericDTO);

    /**
     * 网银订单查询
     */
    GenericRspDTO<OrderResultRspDTO> queryOrder(String ordNo);

    /**
     * 查询网银支付订单银行处理结果
     */
    void additionalOrder(FundPollParamDO fundPollParamDO);

    /**
     * 微信子商户号申请
     */
    GenericRspDTO<SubMercRspDTO> applySubMerc(GenericDTO<SubMercReqDTO> genericDTO);

    /**
     * 微信页面授权申请
     */
    GenericRspDTO<WeChatTokenRsp> applyToken(GenericDTO<WeChatTokenReq> genericDTO);

    /**
     *  撤单
     */
    GenericRspDTO<CloseOrderRspDTO> closeOrder(GenericDTO<CloseOrderReqDTO> genericDTO);

    /**
     * 查询微信子商户列表
     * @param subMercListReqDTO
     * @return
     */
    SubMercListRspDTO getSubMercList(SubMercListReqDTO subMercListReqDTO);

    /**
     * 解绑微信子商户号
     */
    GenericRspDTO unbundingSubMerc(UnbundingSubMercReqDTO unbundingSubMercReqDTO);

    /**
     * 查询支付宝二级商户列表
     * @param secMercListReqDTO
     * @return
     */
    SecMercListRspDTO getSecMercList(SecMercListReqDTO secMercListReqDTO);

    /**
     * 添加支付宝二级商户
     * @param genericDTO
     * @return
     */
    GenericRspDTO addSecMerc(GenericDTO<SecMercReqDTO> genericDTO);
}
