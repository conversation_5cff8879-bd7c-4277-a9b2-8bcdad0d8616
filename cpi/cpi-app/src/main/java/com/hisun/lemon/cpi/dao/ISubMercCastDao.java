package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.SubMercCastDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 平台商户号与微信子账户号映射表
 */
@Mapper
public interface ISubMercCastDao extends BaseDao<SubMercCastDO> {
    /**
     * 根据商户号，查询生效的微信子商户信息
     */
    SubMercCastDO getSubMercCastDO(@Param("appId")String appId, @Param("merchantId")String merchantId, @Param("effFlg")String effFlg);

    /**
     * 根据平台商户号，查出一条生效的微信子商户号
     */
    SubMercCastDO getOneSubMercCastDO(@Param("appId")String appId);
}
