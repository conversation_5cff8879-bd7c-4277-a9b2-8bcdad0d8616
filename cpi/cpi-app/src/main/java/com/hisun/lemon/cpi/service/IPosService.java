package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;

/**
 * <AUTHOR>
 * @date 2017/10/16
 */
public interface IPosService {

    /**
     * 余额查询
     * @param genericDTO 余额查询DTO
     * @return
     */
    GenericRspDTO<PosBalanceRspDTO> balanceQuery(GenericDTO<PosBalanceReqDTO> genericDTO);

    /**
     * POS消费
     * @param genericDTO 支付DTO
     * @return
     */
    GenericRspDTO<PosPayRspDTO> payment(GenericDTO<PosPayReqDTO> genericDTO);

    /**
     * POS预授权交易
     * @param genericDTO 预授权交易DTO
     * @return
     */
    GenericRspDTO<PosPreAuRspDTO> preAuthorization(GenericDTO<PosPreAuReqDTO> genericDTO);
}
