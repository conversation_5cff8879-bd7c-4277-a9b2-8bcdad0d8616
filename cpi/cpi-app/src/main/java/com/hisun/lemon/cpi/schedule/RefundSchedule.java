package com.hisun.lemon.cpi.schedule;

import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.service.IFundPollParamService;
import com.hisun.lemon.cpi.service.IRefundService;
import com.hisun.lemon.cpi.thread.EbankRefundQueryThread;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.lock.Locked;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by Rui on 2017/7/14.
 */
@Component
public class RefundSchedule {

    @Resource
    IRefundService refundService;

    @Resource
    private IFundPollParamService fundPollParamService;

    @Resource
    private TaskExecutor taskExecutor;

    @Resource
    private DistributedLocker distributedLocker;

    private static final Logger logger = LoggerFactory.getLogger(RefundSchedule.class);

    /**
     * 定时任务-向银行接口发起退款申请，每30分钟执行一次
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 0/10 9-23 * * ?")
    @Locked(waitTime = 75*60,leaseTime = 30,lockName = "RefundPay")
    public void refundByTime() {
        try {
            refundService.refundByTime();
        } catch (Exception e) {
            logger.error("refund Exception : ", e);
            return;
        }
    }

    /**
     * 定时任务-通知退款订单处理结果，每30分钟执行一次
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 0/10 9-23 * * ?")
    @Locked(waitTime = 75*60,leaseTime = 30,lockName = "RefundNotify")
    public void notifyOrderByTime() {
        try {
            refundService.notifyOrderByTime();
        } catch (Exception e) {
            logger.error("refund Exception : " , e);
            return;
        }
    }

    /**
     * 定时任务-退款订单异步调起银行接口，查询处理结果
     * 每5分钟执行一次
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 5 * * * ?")
    public void refundAdditionalOrder() {
        List<FundPollParamDO> list = fundPollParamService.queryAllRefund();
        if(CollectionUtils.isNotEmpty(list)) {
            for (FundPollParamDO fundPollParamDO : list) {
                //异步调起银行查询接口
                taskExecutor.execute(new EbankRefundQueryThread(refundService, distributedLocker,fundPollParamDO));
            }
        }
    }

}
