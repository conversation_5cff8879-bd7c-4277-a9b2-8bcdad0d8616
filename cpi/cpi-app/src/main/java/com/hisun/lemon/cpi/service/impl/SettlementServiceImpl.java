package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.bnkapi.alipay.AlipayApi;
import com.hisun.lemon.cpi.bnkapi.wechat.WeChatPayNewApi;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.ISettleDetailDao;
import com.hisun.lemon.cpi.dao.ISubMercCastDao;
import com.hisun.lemon.cpi.dao.alipay.IAlipaySettleDetailDao;
import com.hisun.lemon.cpi.entity.SettleDetailDO;
import com.hisun.lemon.cpi.entity.SubMercCastDO;
import com.hisun.lemon.cpi.entity.alipay.AlipaySettleDetailDO;
import com.hisun.lemon.cpi.entity.alipay.AlipaySettleStatisDO;
import com.hisun.lemon.cpi.service.ISettlementService;
import com.hisun.lemon.cpi.wechat.ebankpay.WeChatProperties;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.SettlementQueryRsp;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.IdGenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 平台结算明细数据管理
 */
@Transactional
@Service
public class SettlementServiceImpl implements ISettlementService {
    private static final Logger logger = LoggerFactory.getLogger(SettlementServiceImpl.class);

    @Resource
    private WeChatProperties weChatProperties;

    @Resource
    private WeChatPayNewApi weChatPayNewApi;

    @Resource
    private AlipayApi alipayApi;

    @Resource
    private ISubMercCastDao subMercCastDao;

    @Resource
    private ISettleDetailDao settleDetailDao;

    @Resource
    private IAlipaySettleDetailDao alipaySettleDetailDao;


    /**
     * 根据路由，选择不同接口查询平台在网银机构的结算明细信息
     */
    @Override
    public GenericRspDTO<NoBody> settlementQuery(int useTag, String rutCorg, String startDate, String endDate) {
        logger.info("EbankpayServiceImpl.weChatUnsettlementQuery() 进入结算查询");
        LocalDate dateStar = null;
        LocalDate dateEnd = null;
        if(StringUtils.isBlank(endDate)){
            dateEnd = DateTimeUtils.getCurrentLocalDate();
        }else{
            dateEnd = DateTimeUtils.parseLocalDate(endDate, "yyyy-MM-dd");
        }
        if(StringUtils.isBlank(startDate)){
            dateStar = dateEnd.minusDays(1L);
        }else{
            dateStar = DateTimeUtils.parseLocalDate(startDate, "yyyy-MM-dd");
        }
        switch (rutCorg) {
            case CpiConstants.WECHAT:
                weChatSettlement(useTag, dateStar, dateEnd);
                break;
            case CpiConstants.ALIPAY:
                alipaySettlment(useTag,dateStar,dateEnd);
            default:
                logger.error("EbankpayServiceImpl.settlementQuery() 执行失败，路由机构不正确");
                return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
        return null;
    }

    /**
     * 根据结算类型查询平台在微信的结算明细信息
     * @param useTag 结算类型，1 - 已结算查询；2 - 未结算查询
     */
    private GenericRspDTO<NoBody> weChatSettlement(int useTag, LocalDate startDate, LocalDate endDate) {
        switch (useTag) {
            case 1:
                weChatSettlementQuery(1, startDate, endDate);
                break;
            case 2:
                weChatUnsettlementQuery(2, startDate, endDate);
                break;
            default:
                logger.error("EbankpayServiceImpl.weChatUnsettlementQuery() 执行失败，结算类型不正确");
                return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 微信商户未结算明细数据查询
     */
    private void weChatUnsettlementQuery(int useTag, LocalDate startDate, LocalDate endDate) {
        //默认取当前日期和时间
        if(JudgeUtils.isNull(endDate)) {
            endDate = DateTimeUtils.getCurrentLocalDate();
        }
        if(JudgeUtils.isNull(startDate)) {
            startDate = endDate.minusDays(1L);
        }

        LocalDate currentDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime currentTime = DateTimeUtils.getCurrentLocalTime();
        //查询开始日期
        String dateStart = DateTimeUtils.formatLocalDate(startDate);
        //查询结束日期
        String dateEnd = DateTimeUtils.formatLocalDate(endDate);
        //偏移量，从结果集中第几条开始取数据
        int offset = 0;
        //每次最多查询数目
        int limit = 10;
        //循环次数
        int pollNum = 0;

        //查出一条生效的微信子商户信息
        SubMercCastDO subMercCastDO = subMercCastDao.getOneSubMercCastDO(weChatProperties.getAppId());
        if(JudgeUtils.isNull(subMercCastDO)) {
            logger.error("EbankpayServiceImpl.weChatUnsettlementQuery() 执行失败，不存在生效的微信子商户信息");
            return;
        }

        //循环获取微信商户的未结算明细数据，返回结果为null则跳出循环
        String subMchId = subMercCastDO.getSubMchId();
        List<SettlementQueryRsp.WeChatStlDetail> weChatStlDetailList;
        List<SettlementQueryRsp.WeChatStlDetail> weChatStlDetailListTotal = new ArrayList<>();
        while(true) {
            weChatStlDetailList = weChatPayNewApi.weChatSettlementQuery(subMchId, useTag, offset, limit, dateStart, dateEnd);
            if(JudgeUtils.isEmpty(weChatStlDetailList)) {
                break;
            } else {
                //保存到另一个集合中，后续统一处理
                weChatStlDetailListTotal.addAll(weChatStlDetailList);
                weChatStlDetailList.clear();
                pollNum++;
                offset = pollNum * limit;
            }
        }

        //若最终结果为空，则直接退出
        if(JudgeUtils.isEmpty(weChatStlDetailListTotal)) {
            logger.info("EbankpayServiceImpl.weChatUnsettlementQuery() 微信商户结算明细查询数目为0");
            return;
        }

        //微信商户未结算明细集合不为空，则遍历数据，保存到表中
        logger.info("EbankpayServiceImpl.weChatUnsettlementQuery() 微信商户结算明细数目为 " + weChatStlDetailListTotal.size());
        for(SettlementQueryRsp.WeChatStlDetail weChatStlDetail : weChatStlDetailListTotal) {
            //将微信商户未结算明细数据，保存到结算信息do对象中
            SettleDetailDO settleDetailDO = getWeChatSettleDetail(weChatStlDetail, currentDate, currentTime, subMchId, useTag);
            SettleDetailDO settleDetail = settleDetailDao.getSettleByDate(settleDetailDO.getSettleDate(),"1");
            BigDecimal lastTotalUnsettleFee = BigDecimal.ZERO;
            if(JudgeUtils.isNotNull(settleDetail)){
                lastTotalUnsettleFee = settleDetailDao.getUnSettleFee(settleDetail.getSettleDate(), settleDetailDO.getSettleDate(), settleDetailDO.getStlFlg());
            }else{
                settleDetail = settleDetailDao.getSettleByDate(settleDetail.getSettleDate(), settleDetailDO.getStlFlg());
                if(JudgeUtils.isNotNull(settleDetail)){
                    lastTotalUnsettleFee = settleDetail.getTotUnsettleFee();
                }
            }
            //汇总表中settleDetailDO结算日期之前未结算的总金额
            if(JudgeUtils.isNull(lastTotalUnsettleFee)) {
                //settleDetailDO结算日期之前未结算总金额为0，则从该结算日期开始累计
                settleDetailDO.setTotUnsettleFee(settleDetailDO.getUnsettleFee());
            } else {
                //设置settleDetailDO结算日期当天未结算的总金额
                BigDecimal currTotalUnsettleFee = settleDetailDO.getUnsettleFee().add(lastTotalUnsettleFee);
                settleDetailDO.setTotUnsettleFee(currTotalUnsettleFee);
            }
            BigDecimal totalSettleFee = settleDetailDao.getSettleFee(settleDetailDO.getSettleDate(), "1");
            if(JudgeUtils.isNull(totalSettleFee)) {
                settleDetailDO.setTotSettleFee(BigDecimal.ZERO);
            }else{
                settleDetailDO.setTotSettleFee(totalSettleFee);
            }

            //根据结算机构、结算日期、结算标志，查询表中是否已存有数据
            SettleDetailDO settleDetailDO1 = settleDetailDao.getSettleDetail(settleDetailDO.getRutCorg(), settleDetailDO.getSettleDate(), settleDetailDO.getStlFlg());
            if(JudgeUtils.isNull(settleDetailDO1)) {
                //表中没有数据，则直接插入结算明细
                settleDetailDao.insert(settleDetailDO);
            } else {
                //表中有数据，则根据主键更新该结算明细
                settleDetailDO.setJrnNo(settleDetailDO1.getJrnNo());
                settleDetailDao.update(settleDetailDO);
            }
        }
    }

    /**
     * 微信商户已结算明细数据查询
     */
    private void weChatSettlementQuery(int useTag, LocalDate startDate, LocalDate endDate) {
        if(JudgeUtils.isNull(endDate)) {
            endDate = DateTimeUtils.getCurrentLocalDate();
        }
        if(JudgeUtils.isNull(startDate)) {
            startDate = endDate.minusDays(1L);
        }

        LocalDate currentDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime currentTime = DateTimeUtils.getCurrentLocalTime();
        //查询开始日期
        String dateStart = DateTimeUtils.formatLocalDate(startDate);
        //查询结束日期
        String dateEnd = DateTimeUtils.formatLocalDate(endDate);
        //偏移量，从结果集中第几条开始取数据
        int offset = 0;
        //每次最多查询数目
        int limit = 10;
        //循环次数
        int pollNum = 0;

        //查出一条生效的微信子商户信息
        SubMercCastDO subMercCastDO = subMercCastDao.getOneSubMercCastDO(weChatProperties.getAppId());
        if(JudgeUtils.isNull(subMercCastDO)) {
            logger.error("EbankpayServiceImpl.weChatSettlementQuery() 执行失败，不存在生效的微信子商户信息");
            return;
        }

        //循环获取微信商户的未结算明细数据，返回结果为null则跳出循环
        String subMchId = subMercCastDO.getSubMchId();
        List<SettlementQueryRsp.WeChatStlDetail> weChatStlDetailList;
        List<SettlementQueryRsp.WeChatStlDetail> weChatStlDetailListTotal = new ArrayList<>();
        while(true) {
            weChatStlDetailList = weChatPayNewApi.weChatSettlementQuery(subMchId, useTag, offset, limit, dateStart, dateEnd);
            if(JudgeUtils.isEmpty(weChatStlDetailList)) {
                break;
            } else {
                //保存到另一个集合中，后续统一处理
                weChatStlDetailListTotal.addAll(weChatStlDetailList);
                weChatStlDetailList.clear();
                pollNum++;
                offset = pollNum * limit;
            }
        }

        //若最终结果为空，则直接退出
        if(JudgeUtils.isEmpty(weChatStlDetailListTotal)) {
            logger.info("EbankpayServiceImpl.weChatSettlementQuery() 微信商户结算明细查询数目为0");
            return;
        }

        //微信商户未结算明细集合不为空，则遍历数据，保存到表中
        logger.info("EbankpayServiceImpl.weChatSettlementQuery() 微信商户结算明细数目为 " + weChatStlDetailListTotal.size());
        for(SettlementQueryRsp.WeChatStlDetail weChatStlDetail : weChatStlDetailListTotal) {
            //将微信商户未结算明细数据，保存到结算信息do对象中
            SettleDetailDO settleDetailDO = getWeChatSettleDetail(weChatStlDetail, currentDate, currentTime, subMchId, useTag);
            //汇总表中settleDetailDO结算日期之前已结算的总金额
            BigDecimal lastTotalSettleFee = settleDetailDao.getSettleFee(settleDetailDO.getSettleDate(), settleDetailDO.getStlFlg());
            if(JudgeUtils.isNull(lastTotalSettleFee)) {
                //settleDetailDO结算日期之前已结算总金额为0，则从该结算日期开始累计
                settleDetailDO.setTotSettleFee(settleDetailDO.getSettleFee());
            } else {
                //设置settleDetailDO结算日期当天已结算的总金额
                BigDecimal currTotalSettleFee = settleDetailDO.getSettleFee().add(lastTotalSettleFee);
                settleDetailDO.setTotSettleFee(currTotalSettleFee);
            }
            settleDetailDO.setTotUnsettleFee(BigDecimal.ZERO);
            //根据结算机构、结算日期、结算标志，查询表中是否已存有数据
            SettleDetailDO settleDetailDO1 = settleDetailDao.getSettleDetail(settleDetailDO.getRutCorg(), settleDetailDO.getSettleDate(), settleDetailDO.getStlFlg());
            if(JudgeUtils.isNull(settleDetailDO1)) {
                //表中没有数据，则直接插入结算明细
                settleDetailDao.insert(settleDetailDO);
            } else {
                //表中有数据，则根据主键更新该结算明细
                settleDetailDO.setJrnNo(settleDetailDO1.getJrnNo());
                settleDetailDao.update(settleDetailDO);
            }
        }
    }

    /**
     * 微信返回的结算明细数据，转换为平台的标准结算信息
     */
    private SettleDetailDO getWeChatSettleDetail(SettlementQueryRsp.WeChatStlDetail weChatStlDetail, LocalDate currentDate, LocalTime currentTime, String subMchId, int stlFlg) {
        BigDecimal oneHundred = new BigDecimal(100);
        LocalDateTime settleDateTime;
        if(weChatStlDetail.getDate_settlement().length() == 10){
            String dateSettle  = weChatStlDetail.getDate_settlement() + " 00:00:00";
            settleDateTime = DateTimeUtils.parseLocalDateTime(dateSettle, "yyyy-MM-dd HH:mm:ss");
        }else{
            settleDateTime = DateTimeUtils.parseLocalDateTime(weChatStlDetail.getDate_settlement(), "yyyy-MM-dd HH:mm:ss");
        }
        SettleDetailDO settleDetailDO = new SettleDetailDO();
        settleDetailDO.setJrnNo(IdGenUtils.generateIdWithDateTime("CPI", 10));
        settleDetailDO.setUpdateDate(currentDate);
        settleDetailDO.setUpdateTime(currentTime);
        settleDetailDO.setPayBatchNo(weChatStlDetail.getFbatchno());
        settleDetailDO.setSettleDate(settleDateTime.toLocalDate());
        settleDetailDO.setSettleDatetime(settleDateTime);
        //settleDetailDO.setStartDate(weChatStlDetail.getDate_start() == null ? null : DateTimeUtils.parseLocalDate(weChatStlDetail.getDate_start()));
        //settleDetailDO.setEndDate(weChatStlDetail.getDate_end() == null ? null : DateTimeUtils.parseLocalDate(weChatStlDetail.getDate_end()));
        settleDetailDO.setSettleFee(new BigDecimal(weChatStlDetail.getSettlement_fee()).divide(oneHundred));
        settleDetailDO.setUnsettleFee(new BigDecimal(weChatStlDetail.getUnsettlement_fee()).divide(oneHundred));
        settleDetailDO.setSettleFeeType(weChatStlDetail.getSettlementfee_type());
        settleDetailDO.setPayFee(new BigDecimal(weChatStlDetail.getPay_fee()).divide(oneHundred));
        settleDetailDO.setRefundFee(new BigDecimal(weChatStlDetail.getRefund_fee()).divide(oneHundred));
        settleDetailDO.setPayNetFee(new BigDecimal(weChatStlDetail.getPay_net_fee()).divide(oneHundred));
        settleDetailDO.setPoundageFee(new BigDecimal(weChatStlDetail.getPoundage_fee()).divide(oneHundred));
        settleDetailDO.setAppid(weChatProperties.getAppId());
        settleDetailDO.setMchId(weChatProperties.getPlatMercId());
        settleDetailDO.setSubMchId(subMchId);
        settleDetailDO.setRutCorg(CpiConstants.WECHAT);
        settleDetailDO.setStlFlg(String.valueOf(stlFlg));
        settleDetailDO.setTotUnsettleFee(BigDecimal.ZERO);
        settleDetailDO.setTotSettleFee(BigDecimal.ZERO);
        return settleDetailDO;
    }

    /**
     * 支付宝结算信息查询
     * @param useTag  1 - 已结算查询；2 - 未结算查询
     * @param startDate 结算起始时间
     * @param endDate  结算结算时间
     * @return
     */
    private GenericRspDTO<NoBody> alipaySettlment(int useTag, LocalDate startDate, LocalDate endDate) {
        switch (useTag) {
            case 1:
                alipaySettlementQuery(1, startDate, endDate);
                break;
            case 2:
                //支付宝未结算信息
                break;
            default:
                logger.error("EbankpayServiceImpl.alipaySettlment() 执行失败，结算类型不正确");
                return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 支付宝商户已结算明细数据查询
     */
    private void alipaySettlementQuery(int useTag, LocalDate startDate, LocalDate endDate) {
        //结算信息获取前一天文件进行处理

        if(JudgeUtils.isNull(endDate)) {
            endDate = DateTimeUtils.getCurrentLocalDate();
        }
        if(JudgeUtils.isNull(startDate)){
            startDate = endDate.minusDays(1);
        }
        //查询结束日期
        String dateEnd = DateTimeUtils.formatLocalDate(endDate);
        String datestart = DateTimeUtils.formatLocalDate(startDate);
        logger.info("开始支付宝获取日期：" + datestart +",结算文件数据.." );
        //开始将支付宝结算信息入库和统计
        alipayApi.alipaySettlementQuery(useTag,datestart);
        logger.info("支付宝结算日期：" + startDate.toString() +",结算数据处理完成");

    }

}
