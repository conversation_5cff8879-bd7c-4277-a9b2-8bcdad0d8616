package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.entity.AccControlDO;
import com.hisun.lemon.cpi.entity.AccFundCfgDO;
import com.hisun.lemon.cpi.entity.AccRefundCfgDO;

/**
 * 资金流入模块，对账主控服务扩展 service
 */
public interface ICheckMgrExtendService {

    /**
     * 对账第一步：获取银行机构对账文件
     */
    String getRefundChkFile(AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO);

    /**
     * 对账第二步：解析银行对账文件，批量插入对账明细至数据库
     */
    String importRefundChkFile(AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO);

    /**
     * 对账第三步：对账，以银行成功的对账明细为准
     */
    String checkRefundFile(AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO) throws Exception;

    /**
     * 对账第四步:对账成功，等待账务处理，
     * 根据业务类型进行分类，分别调用不同业务的service进行账务处理
     */
    String accountTreatment(AccControlDO accControlDO);

    /**
     * 获取银行充值对账文件
     * @return
     */
    String getFundChkFile(AccControlDO accControlDO, AccFundCfgDO accFundCfgDO) throws Exception;

    /**
     * 写入对账文件
     * @return
     */
    String writeFundChkFile(AccControlDO accControlDO, AccFundCfgDO accFundCfgDO) throws Exception;

    /**
     * 上传对账文件
     * @return
     */
    String uploadFundChkFile(String chkBatNo,String chkFilPath,String chkFilSts,AccFundCfgDO accFundCfgDO) throws Exception;

}
