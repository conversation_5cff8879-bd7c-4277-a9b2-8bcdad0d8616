package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * Created by Rui on 2017/7/7.
 */
public interface IFastpayService {

    /**
     * 快捷预签约
     */
    GenericRspDTO<CardPreRspDTO> preBindCard(GenericDTO<CardPreReqDTO> genericDTO);

    /**
     * 快捷绑卡
     */
    GenericRspDTO<CardBindRspDTO> bindCard(GenericDTO<CardBindReqDTO> genericDTO);

    /**
     * 解绑快捷卡
     */
    GenericRspDTO<NoBody> unBindCard(GenericDTO<CardUnBindReqDTO> genericDTO);

    /**
     * 快捷支付
     */
    GenericRspDTO<FastpayRspDTO> fastpay(GenericDTO<FastpayReqDTO> genericDTO);

    /**
     * 快捷支付订单查询
     */
    GenericRspDTO<OrderResultRspDTO> queryOrder(String ordNo);

    /**
     * 快捷支付Acleda
     */
    GenericRspDTO<FastpayAcledaRspDTO> fastpayAcleda(GenericDTO<FastpayAcledaReqDTO> genericDTO);

    /**
     * 快捷支付Acleda结果通知
     */
    GenericRspDTO acledaPayNotify(GenericDTO<FastpayAcledaNotifyReqDTO> genericDTO);

}
