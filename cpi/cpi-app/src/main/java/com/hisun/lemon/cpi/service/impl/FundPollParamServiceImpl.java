package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.cpi.dao.IFundPollParamDao;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.service.IFundPollParamService;
import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by Rui on 2017/7/22.
 */
@Transactional
@Service
public class FundPollParamServiceImpl extends BaseService implements IFundPollParamService {

    @Resource
    IFundPollParamDao fundPollParamDao;

    /**
     * 查询所有生效的支付订单轮询参数
     * @return
     */
    @Override
    public List<FundPollParamDO> queryAllFund() {
        return fundPollParamDao.queryAllFund();
    }

    /**
     * 查询所有生效的退款订单轮询参数
     * @return
     */
    @Override
    public List<FundPollParamDO> queryAllRefund() {
        return fundPollParamDao.queryAllRefund();
    }
}
