package com.hisun.lemon.cpi.bnkapi.bestpay;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import org.springframework.stereotype.Component;

/**
 * Created by gongle<PERSON> on 2017/9/12.
 * 翼支付接口返回码和订单状态判断
 */
@Component("bestPayRetCdConvert")
public class BestPayRetCdConvert {

    /**
     * 扫码下单返回码结果转义
     * @param txFlg
     * @return
     */
    public CpiMsgCd Convert(String txFlg){
        switch (txFlg){
            case "S":
                return CpiMsgCd.SUCCESS;
            case "W":
                return CpiMsgCd.BNK_70102;
            case "F":
                return CpiMsgCd.BNK_70200;
            default:
                return CpiMsgCd.BNK_70102;
        }
    }

    /**
     * 翼支付用户扫码或商户扫码下单接口返回码判断
     */
    public String payOrderResultConvert(String resultCode, String retOrdSts) {
        String txFlg = null;
        if(JudgeUtils.equals(resultCode, CpiConstants.BESTPAY_RESULT_SUCCESS)) {
            switch(retOrdSts) {
                case "U":
                case "P":
                case "S":
                case "R":
                    txFlg = "W";//银行处理中
                    break;
                case "F":
                    txFlg = "F";//订单交易失败
                    break;
                default:
                    txFlg = "W";
                    break;
            }
        } else {
            if(JudgeUtils.isNotEmpty(retOrdSts)) {
                if(JudgeUtils.equals("F", retOrdSts)) {
                    txFlg = "F";//订单交易失败
                } else {
                    txFlg = "W";//银行处理中
                }
            } else {
                txFlg = "F";//订单交易失败
            }
        }
        return txFlg;
    }

    /**
     * 翼支付用户扫码或商户扫码订单查询接口返回码判断
     */
    public String queryOrderResultConvert(String resultCode, String retOrdSts) {
        String txFlg = null;
        if(JudgeUtils.equals(resultCode, CpiConstants.BESTPAY_RESULT_SUCCESS)) {
            switch(retOrdSts) {
                case "U":
                case "P":
                    txFlg = "W";//翼支付处理中
                    break;
                case "S":
                    txFlg = "S";//订单交易成功
                    break;
                case "F":
                    txFlg = "F";//订单交易失败
                    break;
                case "R":
                    txFlg = "R";//全额退款
                    break;
                default:
                    txFlg = "W";
                    break;
            }
        } else {
            txFlg = "F";//订单交易失败
        }
        return txFlg;
    }

    /**
     * 翼支付商户扫码退款下单接口返回码判断
     */
    public String refundOrderResultConvert(String resultCode, String retOrdSts) {
        String txFlg = null;
        if(JudgeUtils.equals(resultCode, CpiConstants.BESTPAY_RESULT_SUCCESS)) {
            switch(retOrdSts) {
                case "U":
                    txFlg = "W";//订单预登记
                    break;
                case "P":
                case "S":
                    txFlg = "S";//订单受理成功
                    break;
                case "F":
                    txFlg = "F";//订单交易失败
                    break;
                default:
                    txFlg = "W";//订单预登记
                    break;
            }
        } else {
            if(JudgeUtils.isNotEmpty(retOrdSts)) {
                if(JudgeUtils.equals("F", retOrdSts)) {
                    txFlg = "F";//订单交易失败
                } else if(JudgeUtils.equals("U", retOrdSts)) {
                    txFlg = "W";//银行预登记
                }  else if(JudgeUtils.equals("P", retOrdSts)) {
                    txFlg = "W";//银行预登记
                }  else {
                    txFlg = "S";//银行受理成功
                }
            } else {
                txFlg = "F";//订单交易失败
            }
        }
        return txFlg;
    }

}
