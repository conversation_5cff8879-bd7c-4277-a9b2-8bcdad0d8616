package com.hisun.lemon.cpi.service.impl;

import cn.hutool.json.JSONUtil;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.IFundOrderDao;
import com.hisun.lemon.cpi.dao.IRemitOrderDao;
import com.hisun.lemon.cpi.dao.IRouteDao;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.entity.FundOrderDO;
import com.hisun.lemon.cpi.entity.RemitOrderDO;
import com.hisun.lemon.cpi.entity.RouteDO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.mq.FundNotifyProduce;
import com.hisun.lemon.cpi.service.IRemitService;
import com.hisun.lemon.csh.client.CshOrderClient;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dto.order.NotifyResultDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by Rui on 2017/7/12.
 */
@Transactional
@Service
public class RemitServiceImpl extends BaseService implements IRemitService {

    @Resource
    IRouteDao routeDao;
    @Resource
    IFundOrderDao fundOrderDao;
    @Resource
    IRemitOrderDao remitOrderDao;
    @Resource
    RiskCheckClient riskCheckClient;
    @Resource
    CshOrderClient cshOrderClient;
    @Resource
    FundNotifyProduce fundNotifyProduce;

    @Resource
    private TaskExecutor taskExecutor;

    private static final Logger logger = LoggerFactory.getLogger(RemitServiceImpl.class);

    /**
     * 汇款充值登记
     */
    @Override
    public GenericRspDTO<RemittanceRspDTO> remit(GenericDTO<RemittanceReqDTO> genericDTO) {
        //请求对象
        RemittanceReqDTO remittanceReqDTO = genericDTO.getBody();
        //返回值
        RemittanceRspDTO remittanceRspDTO = new RemittanceRspDTO();

        String userId = null;

        try {
            //Step1:判断业务类型和业务子类型
            if (!CorpBusTyp.REMITTANCE.equals(remittanceReqDTO.getCorpBusTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd(), remittanceRspDTO);
            }
            if (!CorpBusSubTyp.REMITTANCE.equals(remittanceReqDTO.getCorpBusSubTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), remittanceRspDTO);
            }

            if (JudgeUtils.isBlank(LemonUtils.getUserId())) {
                if(JudgeUtils.isBlank(remittanceReqDTO.getUserNo())) {
                    return GenericRspDTO.newInstance(CpiMsgCd.USER_IS_NULL.getMsgCd(),remittanceRspDTO);
                }else {
                    userId = remittanceReqDTO.getUserNo();
                }
            }else {
                userId = LemonUtils.getUserId();
            }
            String funOrdNo = IdGenUtils.generateIdWithDateTime("FUD_ORD_NO", "CPI", 6);
            //Step2:风控，黑名单检查
            JrnReqDTO jrnReqDTO = new JrnReqDTO();
            //收方ID
            jrnReqDTO.setPayUserId(userId);
            //收方类型
            jrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
            //交易类型
            jrnReqDTO.setTxTyp(Constants.TX_TYP_RECHARGE);
            //交易状态
            jrnReqDTO.setTxSts("0");
            //交易渠道
            jrnReqDTO.setTxCnl("APP");
            //交易金额
            jrnReqDTO.setTxAmt(remittanceReqDTO.getOrdAmt());
            //交易币种
            jrnReqDTO.setCcy(remittanceReqDTO.getOrdCcy());
            //交易日期
            jrnReqDTO.setTxDate(DateTimeUtils.getCurrentLocalDate());
            //交易时间
            jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
            //原交易流水号
            jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
            //原交易订单号
            jrnReqDTO.setTxOrdNo(funOrdNo);
            GenericRspDTO<NoBody> riskRsp = riskCheckClient.riskControl(jrnReqDTO);
            if(JudgeUtils.isNotSuccess(riskRsp.getMsgCd())){
                throw new LemonException(riskRsp.getMsgCd());
            }

            //Step3:路由选择
            RouteDO routeDO = new RouteDO();
            routeDO.setCorpBusTyp(remittanceReqDTO.getCorpBusTyp().getType());
            routeDO.setCorpBusSubTyp(remittanceReqDTO.getCorpBusSubTyp().getType());
            routeDO.setCrdAcTyp(remittanceReqDTO.getCrdAcTyp());
            routeDO.setCrdCorpOrg(remittanceReqDTO.getCrdCorpOrg());
            RouteDO rspRouteInfo = routeDao.queryRouteInfo(routeDO, remittanceReqDTO.getOrdAmt());
            if (null == rspRouteInfo || StringUtils.isBlank(rspRouteInfo.getRutCorpOrg())) {
                return GenericRspDTO.newInstance(CpiMsgCd.ROUTE_INFO_IS_NOT_FUND.getMsgCd(), remittanceRspDTO);
            }

            //Step4:登记充值订单表和汇款订单表
            FundOrderDO fundOrderDO = new FundOrderDO();
            BeanUtils.copyProperties(fundOrderDO,remittanceReqDTO);
            BeanUtils.copyProperties(fundOrderDO,rspRouteInfo);
            fundOrderDO.setCorpBusTyp(remittanceReqDTO.getCorpBusTyp().getType());
            fundOrderDO.setCorpBusSubTyp(remittanceReqDTO.getCorpBusSubTyp().getType());
            fundOrderDO.setFudOrdNo(funOrdNo);
            fundOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY);
            fundOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
            fundOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
            fundOrderDO.setNtfSts(CpiConstants.NOTIFY_WAIT);
            fundOrderDO.setUserId(userId);
            fundOrderDao.insert(fundOrderDO);

            //登記汇款订单表
            RemitOrderDO remitOrderDO = new RemitOrderDO();
            String subOrdNo = IdGenUtils.generateIdWithDateTime("SUB_ORD_NO","CPI",6);
            BeanUtils.copyProperties(remitOrderDO,remittanceReqDTO);
            remitOrderDO.setSubOrdNo(subOrdNo);
            remitOrderDO.setCorpBusTyp(remittanceReqDTO.getCorpBusTyp().getType());
            remitOrderDO.setCorpBusSubTyp(remittanceReqDTO.getCorpBusSubTyp().getType());
            remitOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY);
            remitOrderDO.setFndOrdNo(funOrdNo);
            remitOrderDO.setCrdCorpOrg(rspRouteInfo.getCrdCorpOrg());
            remitOrderDO.setRutCorpOrg(rspRouteInfo.getRutCorpOrg());
            remitOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
            remitOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
            remitOrderDao.insert(remitOrderDO);

            BeanUtils.copyProperties(remittanceRspDTO,remittanceReqDTO);
            remittanceRspDTO.setOrdNo(funOrdNo);
            remittanceRspDTO.setOrdSts(CpiConstants.ORD_WATING_PAY);
            return GenericRspDTO.newSuccessInstance(remittanceRspDTO);
        } catch (LemonException e){
            return GenericRspDTO.newInstance(e.getMsgCd(), remittanceRspDTO);
        } catch (Exception e){
            logger.error("RemitServiceImpl.remit exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), remittanceRspDTO);
        }

    }

    @Override
    public GenericRspDTO<OrderResultRspDTO> queryOrder(String ordNo) {
        //返回值
        OrderResultRspDTO orderResultDTO = new OrderResultRspDTO();

        try {
            FundOrderDO fundOrderDO = fundOrderDao.get(ordNo);
            if(JudgeUtils.isNull(fundOrderDO)){
                return GenericRspDTO.newInstance(CpiMsgCd.FUND_ORDER_IS_NOT_FOUND.getMsgCd(), orderResultDTO);
            }
            RemitOrderDO remitOrderDO = remitOrderDao.selectByFndOrdNo(fundOrderDO.getFudOrdNo());
            if(JudgeUtils.isNotNull(remitOrderDO)){
                orderResultDTO.setPicUrl(remitOrderDO.getPicUrl());
            }
            BeanUtils.copyProperties(orderResultDTO,fundOrderDO);
            orderResultDTO.setOrdNo(ordNo);
            return GenericRspDTO.newSuccessInstance(orderResultDTO);
        }catch (LemonException e){
            return GenericRspDTO.newInstance(e.getMsgCd(), orderResultDTO);
        } catch (Exception e){
            logger.error("RemitServiceImpl.queryOrder exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), orderResultDTO);
        }
    }

    /**
     * 完成 付款订单  
     *  ordSts='A1'(审核通过)、ordSts='S1'(复核通过)、ordSts='F1'(复核拒绝)、ordSts='F2'(审核拒绝)
     * @param genericDTO 订单编号
     */
    @Override
    public GenericRspDTO<NoBody> payOrder(GenericDTO<RemittanceConfirmDTO> genericDTO) {
        //请求对象
        RemittanceConfirmDTO remittanceConfirmDTO = genericDTO.getBody();
        String ordSts = remittanceConfirmDTO.getOrdSts();
        String reason = remittanceConfirmDTO.getReason();
        boolean ordStsChangeFlag = false;
        try {
            // 根据主键查询充值订单信息
            FundOrderDO fundOrderDO = fundOrderDao.get(remittanceConfirmDTO.getFndOrdNo());
            if (JudgeUtils.isNull(fundOrderDO)) {
                logger.error("RemitServiceImpl.payOrder() 根据订单号没有找到相应的充值订单");
                throw new LemonException(CpiMsgCd.FUND_ORDER_IS_NOT_FOUND.getMsgCd());
            }
            RemitOrderDO remitOrderDO = remitOrderDao.selectByFndOrdNo(fundOrderDO.getFudOrdNo());
            if (JudgeUtils.isNull(remitOrderDO)) {
                logger.error("RemitServiceImpl.payOrder() 根据订单号没有找到相应的汇款订单");
                throw new LemonException(CpiMsgCd.FUND_ORDER_IS_NOT_FOUND.getMsgCd());
            }

            // 检验原订单状态
            if(CpiConstants.ORD_SUCCESS.equals(remitOrderDO.getOrdSts())||CpiConstants.ORD_FAIL.equals(remitOrderDO.getChkSts())){
                return GenericRspDTO.newInstance(CpiMsgCd.ORDER_STATE_ERROR.getMsgCd());
            }

            if (!JudgeUtils.equals(remitOrderDO.getOrdSts(), CpiConstants.ORD_WATING_PAY) && !JudgeUtils.equals(remitOrderDO.getOrdSts(), CpiConstants.WAIT_REVIEW)) {
                logger.error("WithdrawServiceImpl.payOrder() 原提现订单状态非处理中，不允许更改");
                throw new LemonException(CpiMsgCd.ORG_STS_IS_NOT_PROCESSING.getMsgCd());
            }

            // 如果请求状态是复核通过或复核拒绝，需要判断充值订单状态是否是待复核
            if (JudgeUtils.equals(ordSts, CpiConstants.ORD_SUCCESS) || JudgeUtils.equals(ordSts, CpiConstants.ORD_FAIL)) {
                if (!JudgeUtils.equals(fundOrderDO.getOrdSts(), CpiConstants.WAIT_REVIEW)) {
                    logger.error("充值订单状态非待复核，不允许更改");
                    throw new LemonException(CpiMsgCd.ORG_STS_IS_NOT_PROCESSING.getMsgCd());
                }
                if (JudgeUtils.isNotBlank(fundOrderDO.getFirstAuditUser()) && JudgeUtils.equals(fundOrderDO.getFirstAuditUser(), LemonUtils.getUserId())) {
                    logger.error("复核人和审核人不能是同一个用户");
                    throw new LemonException(CpiMsgCd.REVIEWER_AND_THE_AUDITOR_CANNOT_BE_THE_SAME.getMsgCd());
                }
            }

            if (JudgeUtils.equals(ordSts, CpiConstants.WAIT_REVIEW)) {
                logger.info("审核通过，更新充值订单：{}审核信息", fundOrderDO.getFudOrdNo());
                updateCpiFundOrderAuditInfo(fundOrderDO,"APPROVED",reason);
                return GenericRspDTO.newSuccessInstance();
            }
            if (JudgeUtils.equals(ordSts, "F2")) {
                logger.info("更新充值订单：{}审核信息", fundOrderDO.getFudOrdNo());
                updateCpiFundOrderAuditInfo(fundOrderDO,"REJECTED",reason);
                ordSts = CpiConstants.ORD_FAIL;
                ordStsChangeFlag = true;
            }
            //更新汇款充值订单状态
            RemitOrderDO updateSubOrderDo = new RemitOrderDO();
            updateSubOrderDo.setSubOrdNo(remitOrderDO.getSubOrdNo());
            updateSubOrderDo.setAcDt(LemonUtils.getAccDate());
            updateSubOrderDo.setOrdSts(ordSts);
            updateSubOrderDo.setReason(reason);
            remitOrderDao.update(updateSubOrderDo);

            //更新资金流入订单状态
            FundOrderDO updateOrderDO = new FundOrderDO();
            updateOrderDO.setFudOrdNo(fundOrderDO.getFudOrdNo());
            updateOrderDO.setOrdSts(ordSts);
            updateOrderDO.setAcDt(LemonUtils.getAccDate());
            updateOrderDO.setRmk(reason);
            if (CpiConstants.ORD_SUCCESS.equals(ordSts)) {
                updateOrderDO.setOrdSuccDt(DateTimeUtils.getCurrentLocalDate());
                updateOrderDO.setOrdSuccTm(DateTimeUtils.getCurrentLocalTime());
                logger.info("复核通过，更新充值订单：{}审核信息", fundOrderDO.getFudOrdNo());
                updateOrderDO.setSecondAuditUser(LemonUtils.getUserId());
                updateOrderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
                updateOrderDO.setSecondAuditResult("APPROVED");
                updateOrderDO.setSecondAuditOpinion(reason);
                updateOrderDO.setExecuteTime(DateTimeUtils.getCurrentLocalDateTime());
            } else if (ordSts.equals(CpiConstants.ORD_FAIL) && !ordStsChangeFlag){
                updateOrderDO.setSecondAuditUser(LemonUtils.getUserId());
                updateOrderDO.setSecondAuditTime(DateTimeUtils.getCurrentLocalDateTime());
                updateOrderDO.setSecondAuditResult("REJECTED");
                updateOrderDO.setSecondAuditOpinion(reason);
                updateOrderDO.setRejectReason(reason);
            }
            fundOrderDao.update(updateOrderDO);

            //通知收银台模块
            fundOrderDO.setOrdSts(ordSts);
//            fundNotifyProduce.fundOrderNotify(fundOrderDO,reason);
            // 同步处理结果
            processingResult(fundOrderDO,reason);
            return GenericRspDTO.newSuccessInstance();
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd());
        } catch(Exception e) {
            logger.error("RemitServiceImpl.payOrder exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
    }

    /**
     * 订单处理结果
     * @param fundOrderDO 充值订单
     * @param rmk 描述
     */
    private void processingResult(FundOrderDO fundOrderDO, String rmk) {
        //充值订单信息
        NotifyResultDTO notifyResultDTO = new NotifyResultDTO();
        notifyResultDTO.setCshOrderNo(fundOrderDO.getReqOrdNo());
        notifyResultDTO.setFndOrderNo(fundOrderDO.getFudOrdNo());
        notifyResultDTO.setCrdPayAmt(fundOrderDO.getOrdAmt());
        notifyResultDTO.setRemark(rmk);

        String ordSts = fundOrderDO.getOrdSts();
        if(CpiConstants.ORD_FAIL.equals(ordSts)){
            notifyResultDTO.setOrderStatus(CshConstants.JRN_STS_F);
        } else {
            notifyResultDTO.setOrderStatus(CshConstants.JRN_STS_S);
        }
        logger.info("===========MQ通知收银台对象{ "+ JSONUtil.toJsonStr(notifyResultDTO) +" }==========================");

        cshOrderClient.onMessageReceive(GenericDTO.newInstance(notifyResultDTO));
    }

    /**
     * 营业厅充值登记
     */
    @Override
    public GenericRspDTO<RemittanceRspDTO> hallRemit(GenericDTO<RemittanceReqDTO> genericDTO) {
        //请求对象
        RemittanceReqDTO remittanceReqDTO = genericDTO.getBody();
        //返回对象
        RemittanceRspDTO remittanceRspDTO = new RemittanceRspDTO();
        try {
            //判断业务类型和业务子类型
            if (!CorpBusTyp.REMITTANCE.equals(remittanceReqDTO.getCorpBusTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd(), remittanceRspDTO);
            }
            if (!CorpBusSubTyp.BUSSINESS_REMITTANCE.equals(remittanceReqDTO.getCorpBusSubTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), remittanceRspDTO);
            }

            //判断收款用户id
            String userId = LemonUtils.getUserId();
            if (JudgeUtils.isBlank(userId)) {
                userId = remittanceReqDTO.getUserNo();
                if (JudgeUtils.isBlank(userId)) {
                    return GenericRspDTO.newInstance(CpiMsgCd.USER_IS_NULL.getMsgCd(), remittanceRspDTO);
                }
            }

            //充值订单号及子订单号
            String fndOrdNo = IdGenUtils.generateIdWithDateTime("FUD_ORD_NO", "CPI", 6);
            String subOrdNo = IdGenUtils.generateIdWithDateTime("SUB_ORD_NO","CPI",6);

            //登记充值订单表
            FundOrderDO fundOrderDO = new FundOrderDO();
            BeanUtils.copyProperties(fundOrderDO,remittanceReqDTO);
            fundOrderDO.setCorpBusTyp(remittanceReqDTO.getCorpBusTyp().getType());
            fundOrderDO.setCorpBusSubTyp(remittanceReqDTO.getCorpBusSubTyp().getType());
            fundOrderDO.setFudOrdNo(fndOrdNo);
            fundOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
            fundOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
            fundOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
            fundOrderDO.setNtfSts(CpiConstants.NOTIFY_WAIT);
            fundOrderDO.setUserId(userId);
            fundOrderDO.setRutCorpOrg(CpiConstants.HALL);
            fundOrderDao.insert(fundOrderDO);

            //登记汇款订单表
            RemitOrderDO remitOrderDO = new RemitOrderDO();
            BeanUtils.copyProperties(remitOrderDO,remittanceReqDTO);
            remitOrderDO.setSubOrdNo(subOrdNo);
            remitOrderDO.setCorpBusTyp(remittanceReqDTO.getCorpBusTyp().getType());
            remitOrderDO.setCorpBusSubTyp(remittanceReqDTO.getCorpBusSubTyp().getType());
            remitOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
            remitOrderDO.setFndOrdNo(fndOrdNo);
            remitOrderDO.setCrdCorpOrg(remittanceReqDTO.getCrdCorpOrg());
            remitOrderDO.setRutCorpOrg(CpiConstants.HALL);
            remitOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
            remitOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
            remitOrderDO.setAcDt(DateTimeUtils.getCurrentLocalDate());
            remitOrderDO.setChkKey(fndOrdNo);
            remitOrderDao.insert(remitOrderDO);

            //返回结果
            BeanUtils.copyProperties(remittanceRspDTO,remittanceReqDTO);
            remittanceRspDTO.setOrdNo(fndOrdNo);
            remittanceRspDTO.setOrdSts(CpiConstants.ORD_SUCCESS);
            return GenericRspDTO.newSuccessInstance(remittanceRspDTO);
        } catch (LemonException e){
            return GenericRspDTO.newInstance(e.getMsgCd(), remittanceRspDTO);
        } catch (Exception e){
            logger.error("RemitServiceImpl.remit exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), remittanceRspDTO);
        }
    }



    /**
     * 更新充值订单审核信息
     * @param fundOrderDO    充值订单信息
     * @param auditResult    审核结果
     * @param reason    审核原因
     */
    private void updateCpiFundOrderAuditInfo(FundOrderDO fundOrderDO,String auditResult,String reason) {
        FundOrderDO orderDO = new FundOrderDO();
        orderDO.setFudOrdNo(fundOrderDO.getFudOrdNo());
        orderDO.setOrdSts(CpiConstants.WAIT_REVIEW);
        orderDO.setFirstAuditUser(LemonUtils.getUserId());
        orderDO.setFirstAuditTime(DateTimeUtils.getCurrentLocalDateTime());
        orderDO.setFirstAuditOpinion(reason);
        orderDO.setFirstAuditResult(auditResult);
        orderDO.setRmk(reason);
        fundOrderDao.update(orderDO);
    }


}
