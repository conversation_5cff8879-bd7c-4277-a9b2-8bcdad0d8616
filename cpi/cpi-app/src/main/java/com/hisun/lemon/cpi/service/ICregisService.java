package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.cregis.rsp.VaultAccountAddressBalanceObject;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.framework.data.NoBody;

import java.math.BigDecimal;

/**
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 17:29
 */
public interface ICregisService {

    /**
     * 准备付款数据
     *
     * @param req 请求参数
     * @return
     */
    CregisRsp<TransferSubmitRspDTO> prePayment(PaymentPrepareReqDTO req);

    /**
     * 签名数据
     *
     * @return
     */
    CregisRsp<SignRspDTO> signData(SignReqDTO  req);

    /**
     * 提交转账
     * @param request
     * @return
     */
    CregisRsp<NoBody> submitTransfer(TransferSubmitReqDTO request);

    /**
     * 转账签名全流程
     * @param req
     * @return
     */
    CregisRsp<NoBody> transferAll(PaymentReqDTO req);

    /**
     * 处理Cregis回调数据
     *
     * @param req
     */
    void handleCallback(CregisCallbackDTO req);

    BigDecimal decimalChange(String ccy, Integer balanceObject);
}
