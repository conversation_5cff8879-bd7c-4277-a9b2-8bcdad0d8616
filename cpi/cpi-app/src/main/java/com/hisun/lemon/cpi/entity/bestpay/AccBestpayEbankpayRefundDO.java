/*
 * @ClassName AccBestpayEbankpayRefundDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-03 16:58:30
 */
package com.hisun.lemon.cpi.entity.bestpay;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class AccBestpayEbankpayRefundDO extends BaseDO {
    /**
     * @Fields mercId 商户号
     */
    private String mercId;
    /**
     * @Fields bnkChkKey 银行对账外键
     */
    private String bnkChkKey;
    /**
     * @Fields platJrnNo 平台流水号
     */
    private String platJrnNo;
    /**
     * @Fields platOrdNo 平台订单号
     */
    private String platOrdNo;
    /**
     * @Fields ordCyy 币种
     */
    private String ordCyy;
    /**
     * @Fields ordForeignAmt 外币
     */
    private BigDecimal ordForeignAmt;
    /**
     * @Fields ordAmt 人民币
     */
    private BigDecimal ordAmt;
    /**
     * @Fields exchangeRate 汇率
     */
    private BigDecimal exchangeRate;
    /**
     * @Fields chkBatNo 对账批次号
     */
    private String chkBatNo;
    /**
     * @Fields rutCorg 路径合作机构
     */
    private String rutCorg;
    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields chkFilDt 对账文件日期
     */
    private LocalDate chkFilDt;
    /**
     * @Fields chkFilNm 对账文件名称
     */
    private String chkFilNm;
    /**
     * @Fields chkId 对账明细序号
     */
    private String chkId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields chkSts 对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑
     */
    private String chkSts;

    public String getMercId() {
        return mercId;
    }

    public void setMercId(String mercId) {
        this.mercId = mercId;
    }

    public String getBnkChkKey() {
        return bnkChkKey;
    }

    public void setBnkChkKey(String bnkChkKey) {
        this.bnkChkKey = bnkChkKey;
    }

    public String getPlatJrnNo() {
        return platJrnNo;
    }

    public void setPlatJrnNo(String platJrnNo) {
        this.platJrnNo = platJrnNo;
    }

    public String getPlatOrdNo() {
        return platOrdNo;
    }

    public void setPlatOrdNo(String platOrdNo) {
        this.platOrdNo = platOrdNo;
    }

    public String getOrdCyy() {
        return ordCyy;
    }

    public void setOrdCyy(String ordCyy) {
        this.ordCyy = ordCyy;
    }

    public BigDecimal getOrdForeignAmt() {
        return ordForeignAmt;
    }

    public void setOrdForeignAmt(BigDecimal ordForeignAmt) {
        this.ordForeignAmt = ordForeignAmt;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public LocalDate getChkFilDt() {
        return chkFilDt;
    }

    public void setChkFilDt(LocalDate chkFilDt) {
        this.chkFilDt = chkFilDt;
    }

    public String getChkFilNm() {
        return chkFilNm;
    }

    public void setChkFilNm(String chkFilNm) {
        this.chkFilNm = chkFilNm;
    }

    public String getChkId() {
        return chkId;
    }

    public void setChkId(String chkId) {
        this.chkId = chkId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getChkSts() {
        return chkSts;
    }

    public void setChkSts(String chkSts) {
        this.chkSts = chkSts;
    }
}