package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.PlaceOrderNotifyReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.RefundNotifyReq;
import com.hisun.lemon.cpi.bnkapi.alipay.AlipayApi;
import com.hisun.lemon.cpi.bnkapi.alipay.AlipayRetCdConvert;
import com.hisun.lemon.cpi.bnkapi.bestpay.BestPayRetCdConvert;
import com.hisun.lemon.cpi.bnkapi.bestpay.ebankpay.BestPayScanPayApi;
import com.hisun.lemon.cpi.bnkapi.wechat.WeChatPayNewApi;
import com.hisun.lemon.cpi.bnkapi.wechat.WeChatRetCdConvert;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.IFundOrderDao;
import com.hisun.lemon.cpi.dao.IRefundOrderDao;
import com.hisun.lemon.cpi.dao.IRefundSuborderDao;
import com.hisun.lemon.cpi.entity.EbankOrderDO;
import com.hisun.lemon.cpi.entity.FundOrderDO;
import com.hisun.lemon.cpi.entity.RefundOrderDO;
import com.hisun.lemon.cpi.entity.RefundSuborderDO;
import com.hisun.lemon.cpi.mq.FundNotifyProduce;
import com.hisun.lemon.cpi.service.IEbankNotifyService;
import com.hisun.lemon.cpi.service.IEbankpayTransaction;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.WeChatOrderNotifyRsp;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.cpi.alipay.ebankpay.req.AlipayOrderNotifyReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Created by Rui on 2017/7/26.
 */
@Service
@Transactional
public class EbankNotifyServiceImpl extends BaseService implements IEbankNotifyService{

    private static final Logger logger = LoggerFactory.getLogger(EbankNotifyServiceImpl.class);

    @Resource
    private IFundOrderDao fundOrderDao;
    @Resource
    private IRefundOrderDao refundOrderDao;
    @Resource
    private IRefundSuborderDao refundSuborderDao;
    @Resource
    private FundNotifyProduce fundNotifyProduce;
    @Resource
    private BestPayScanPayApi bestPayScanPayApi;
    @Resource
    private BestPayRetCdConvert bestPayRetCdConvert;
    @Resource
    private IEbankpayTransaction ebankpayTransaction;
    @Resource
    private WeChatPayNewApi weChatPayNewApi;
    @Resource
    private WeChatRetCdConvert weChatRetCdConvert;
    @Resource
    private AlipayApi aliPayApi;
    @Resource
    private AlipayRetCdConvert alipayRetCdConvert;

    /**
     * 用户扫码或商户扫码支付，翼支付结果通知
     */
    @Override
    public void bestPayFundNotify(PlaceOrderNotifyReq placeOrderNotifyReq) {
        //解析报文
        BaseBnkRspBO baseBnkRspBO = bestPayScanPayApi.bestPayFundNotify(placeOrderNotifyReq);

        //更新充值订单表和网银订单表的状态
        CpiMsgCd msgCd = bestPayRetCdConvert.Convert(baseBnkRspBO.getTxFlg());
        EbankOrderDO ebankOrderDO = ebankpayTransaction.selectEbankpayOrderByChkKey(baseBnkRspBO.getChkKey());

        //若网银订单的状态不等于W2,则等待2秒再更新订单状态
        if(!CpiConstants.ORD_PAY_BANKING.equals(ebankOrderDO.getOrdSts())) {
            logger.info("=========EbankNotifyServiceImpl.bestPayFundNotify 翼支付结果通知，等待2秒=========");
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                logger.error("=========EbankNotifyServiceImpl.bestPayFundNotify 翼支付结果通知，等待2秒异常中断，异常为", e);
                Thread.currentThread().interrupt();
            }
        }
        ebankpayTransaction.updateOrderStateByBank(ebankOrderDO, baseBnkRspBO, msgCd,null);

        //结果通知收银台
        FundOrderDO fundOrderDO = fundOrderDao.get(ebankOrderDO.getFndOrdNo());
        String resultCode = placeOrderNotifyReq.getResultCode();//返回码
        if(JudgeUtils.isSuccess(resultCode)) {
            fundOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
        } else {
            fundOrderDO.setOrdSts(CpiConstants.ORD_FAIL);
        }
        fundNotifyProduce.fundOrderNotify(fundOrderDO,"");
    }

    /**
     * 用户扫码退款或商户扫码退款，翼支付结果通知
     */
    @Override
    public void bestPayRefundNotify(RefundNotifyReq refundNotifyReq) {
        //解析报文
        BaseBnkRspBO baseBnkRspBO = bestPayScanPayApi.bestPayRefundNotify(refundNotifyReq);

        //更新退款订单表和子订单表状态
        RefundSuborderDO refundSuborderDO = refundSuborderDao.selectRefundSuborderByChkKey(baseBnkRspBO.getChkKey());
        RefundOrderDO updateOrderDO = new RefundOrderDO();
        RefundSuborderDO updateSuborderDO = new RefundSuborderDO();
        if(CpiConstants.BANK_RESPONSE_SUCCESS.equals(baseBnkRspBO.getTxFlg())) {
            updateOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateOrderDO.setOrdSuccDt(baseBnkRspBO.getOrgOrdDt());
            updateOrderDO.setOrdSuccTm(baseBnkRspBO.getOrgOrdTm());
            updateSuborderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateSuborderDO.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
            updateSuborderDO.setOrgOrdDt(baseBnkRspBO.getOrgOrdDt());
            updateSuborderDO.setOrgOrdTm(baseBnkRspBO.getOrgOrdTm());
        } else {
            updateOrderDO.setOrdSts(CpiConstants.ORD_FAIL);
            updateSuborderDO.setOrdSts(CpiConstants.ORD_FAIL);
            updateSuborderDO.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
            updateSuborderDO.setOrgOrdDt(baseBnkRspBO.getOrgOrdDt());
            updateSuborderDO.setOrgOrdTm(baseBnkRspBO.getOrgOrdTm());
        }
        updateOrderDO.setRfdOrdNo(refundSuborderDO.getRfdOrdNo());
        updateSuborderDO.setRfdOrdNo(refundSuborderDO.getRfdOrdNo());
        updateSuborderDO.setSubOrdNo(refundSuborderDO.getSubOrdNo());
        refundOrderDao.update(updateOrderDO);
        refundSuborderDao.update(updateSuborderDO);
    }

    /**
     * 用户扫码或商户扫码支付，微信下单结果通知
     */
    @Override
    public WeChatOrderNotifyRsp weChatFundNotify(String resultData) {
        //解析报文
        BaseBnkRspBO baseBnkRspBO = weChatPayNewApi.wechatFundNotify(resultData);
        WeChatOrderNotifyRsp weChatOrderNotifyRsp = new WeChatOrderNotifyRsp();

        //更新充值订单和网银订单状态
        if(JudgeUtils.isNull(baseBnkRspBO)) {
            logger.info("EbankNotifyServiceImpl.wechatFundNotify() 微信下单通知结果为空");
            weChatOrderNotifyRsp.setReturn_code(CpiConstants.WECHAT_FAIL);
            return weChatOrderNotifyRsp;
        }
        CpiMsgCd msgCd = weChatRetCdConvert.txFlgConvert(baseBnkRspBO.getTxFlg());
        EbankOrderDO ebankOrderDO = ebankpayTransaction.selectEbankpayOrderByChkKey(baseBnkRspBO.getChkKey());
        ebankpayTransaction.updateOrderStateByBank(ebankOrderDO, baseBnkRspBO, msgCd,null);

        //最终结果通知收银台
        FundOrderDO fundOrderDO = fundOrderDao.get(ebankOrderDO.getFndOrdNo());
        if(JudgeUtils.isSuccess(msgCd.getMsgCd())) {
            fundOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
        } else {
            fundOrderDO.setOrdSts(CpiConstants.ORD_FAIL);
        }
        fundNotifyProduce.fundOrderNotify(fundOrderDO,"");

        //处理结果返回给微信
        weChatOrderNotifyRsp.setReturn_code(CpiConstants.WECHAT_SUCCESS);
        return weChatOrderNotifyRsp;
    }

    /*
     * 用户扫码或商户扫码支付，支付宝下单结果通知
     */
    @Override
    public void alipayFundNotify(AlipayOrderNotifyReq alipayOrderNotifyReq) {
        //解析报文
        BaseBnkRspBO baseBnkRspBO = aliPayApi.alipayFundNotify(alipayOrderNotifyReq);

        //更新充值订单和网银订单状态
        if(JudgeUtils.isNull(baseBnkRspBO)) {
            logger.info("EbankNotifyServiceImpl.wechatFundNotify() 支付宝下单通知结果为空");
            return;
        }
        CpiMsgCd msgCd = alipayRetCdConvert.txFlgConvert(baseBnkRspBO.getTxFlg());
        EbankOrderDO ebankOrderDO = ebankpayTransaction.selectEbankpayOrderByChkKey(baseBnkRspBO.getChkKey());
        if(JudgeUtils.isNull(ebankOrderDO)){
            logger.info("支付宝预下单回调通知>>chkkey为：" + baseBnkRspBO.getChkKey() +",在网银订单表中不存在!");
            return;
        }
        //若是原网银订单状态是成功，不进行处理，支付宝会通知多次
        if(CpiConstants.ORD_SUCCESS.equals(ebankOrderDO.getOrdSts())){
            logger.info(ebankOrderDO.getChkKey() + ", 对应的网银订单状态为成功！");
            return;
        }
        ebankpayTransaction.updateOrderStateByBank(ebankOrderDO, baseBnkRspBO, msgCd,null);

        //最终结果通知收银台
        FundOrderDO fundOrderDO = fundOrderDao.get(ebankOrderDO.getFndOrdNo());
        if(JudgeUtils.isSuccess(msgCd.getMsgCd())) {
            fundOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
        } else {
            fundOrderDO.setOrdSts(CpiConstants.ORD_FAIL);
        }
        fundNotifyProduce.fundOrderNotify(fundOrderDO,"支付宝下单成功回调通知");
        logger.info("支付宝下单异步通知处理完成");
    }

}
