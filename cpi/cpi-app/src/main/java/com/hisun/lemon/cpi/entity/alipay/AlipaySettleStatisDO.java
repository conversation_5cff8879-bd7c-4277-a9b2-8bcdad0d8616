/*
 * @ClassName AlipaySettleStatisDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-17 18:33:02
 */
package com.hisun.lemon.cpi.entity.alipay;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class AlipaySettleStatisDO extends BaseDO {
    /**
     * @Fields settleBatchNo 结算批次号
     */
    private String settleBatchNo;
    /**
     * @Fields updateDate 更新日期
     */
    private LocalDate updateDate;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalTime updateTime;
    /**
     * @Fields settleDate 外部机构结算日期
     */
    private String settleDate;
    /**
     * @Fields settleDatetime 结算日期时间
     */
    private String settleDatetime;
    /**
     * @Fields settleAmount 结算金额,外币
     */
    private BigDecimal settleAmount;
    /**
     * @Fields rmbSettleAmount 结算金额,人民币，单位元
     */
    private BigDecimal rmbSettleAmount;
    /**
     * @Fields unsettleFee 未结算金额，以元为单位
     */
    private BigDecimal unsettleFee;
    /**
     * @Fields currencyType 结算币种
     */
    private String currencyType;
    /**
     * @Fields payFee 支付金额，以元为单位
     */
    private BigDecimal payFee;
    /**
     * @Fields refundFee 退款金额，以元为单位
     */
    private BigDecimal refundFee;
    /**
     * @Fields payNetFee 支付净额，以元为单位
     */
    private BigDecimal payNetFee;
    /**
     * @Fields poundageFee 手续费金额，以元为单位
     */
    private BigDecimal poundageFee;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields partner partner id
     */
    private String partner;
    /**
     * @Fields mchName 平台在外部机构的商户名称
     */
    private String mchName;
    /**
     * @Fields stlFlg 结算标志1-已结算；2-未结算
     */
    private String stlFlg;
    /**
     * @Fields rutCorg 外部机构（路由机构）
     */
    private String rutCorg;

    public String getSettleBatchNo() {
        return settleBatchNo;
    }

    public void setSettleBatchNo(String settleBatchNo) {
        this.settleBatchNo = settleBatchNo;
    }

    public LocalDate getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDate updateDate) {
        this.updateDate = updateDate;
    }

    public LocalTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    public String getSettleDatetime() {
        return settleDatetime;
    }

    public void setSettleDatetime(String settleDatetime) {
        this.settleDatetime = settleDatetime;
    }

    public BigDecimal getSettleAmount() {
        return settleAmount;
    }

    public void setSettleAmount(BigDecimal settleAmount) {
        this.settleAmount = settleAmount;
    }

    public BigDecimal getRmbSettleAmount() {
        return rmbSettleAmount;
    }

    public void setRmbSettleAmount(BigDecimal rmbSettleAmount) {
        this.rmbSettleAmount = rmbSettleAmount;
    }

    public BigDecimal getUnsettleFee() {
        return unsettleFee;
    }

    public void setUnsettleFee(BigDecimal unsettleFee) {
        this.unsettleFee = unsettleFee;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public BigDecimal getPayFee() {
        return payFee;
    }

    public void setPayFee(BigDecimal payFee) {
        this.payFee = payFee;
    }

    public BigDecimal getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    public BigDecimal getPayNetFee() {
        return payNetFee;
    }

    public void setPayNetFee(BigDecimal payNetFee) {
        this.payNetFee = payNetFee;
    }

    public BigDecimal getPoundageFee() {
        return poundageFee;
    }

    public void setPoundageFee(BigDecimal poundageFee) {
        this.poundageFee = poundageFee;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getPartner() {
        return partner;
    }

    public void setPartner(String partner) {
        this.partner = partner;
    }

    public String getMchName() {
        return mchName;
    }

    public void setMchName(String mchName) {
        this.mchName = mchName;
    }

    public String getStlFlg() {
        return stlFlg;
    }

    public void setStlFlg(String stlFlg) {
        this.stlFlg = stlFlg;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }
}