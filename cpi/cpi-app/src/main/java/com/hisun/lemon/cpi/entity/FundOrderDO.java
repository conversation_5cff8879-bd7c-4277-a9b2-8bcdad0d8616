/*
 * @ClassName FundOrderDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class FundOrderDO extends BaseDO {
    /**
     * @Fields fudOrdNo 内部订单号
     */
    private String fudOrdNo;
    /**
     * @Fields ordDt 订单日期
     */
    private LocalDate ordDt;
    /**
     * @Fields ordTm 订单时间
     */
    private LocalTime ordTm;
    /**
     * @Fields acDt 会计日期
     */
    private LocalDate acDt;
    /**
     * @Fields ordCcy 币种
     */
    private String ordCcy;
    /**
     * @Fields ordAmt 订单金额
     */
    private BigDecimal ordAmt;
    /**
     * @Fields ordRfdAmt 已退款金额
     */
    private BigDecimal ordRfdAmt;
    /**
     * @Fields ordSts 订单状态，W1：等待充值，S1：交易成功，E1：交易逾期，F1：交易失败
     */
    private String ordSts;
    /**
     * @Fields ordSuccDt 订单成功日期
     */
    private LocalDate ordSuccDt;
    /**
     * @Fields ordSuccTm 订单成功时间
     */
    private LocalTime ordSuccTm;
    /**
     * @Fields userTyp 用户类型，U：用户，M：商户
     */
    private String userTyp;
    /**
     * @Fields userId 用户ID
     */
    private String userId;
    /**
     * @Fields mblNo 手机号
     */
    private String mblNo;
    /**
     * @Fields crdCorpOrg 资金合作机构号
     */
    private String crdCorpOrg;
    /**
     * @Fields crdAcTyp 银行卡类型，C贷记卡，D借记卡
     */
    private String crdAcTyp;
    /**
     * @Fields crdNoEnc 加密银行卡号
     */
    private String crdNoEnc;
    /**
     * @Fields crdUsrNm 用户姓名
     */
    private String crdUsrNm;
    /**
     * @Fields crdNoLast 银行卡后4位
     */
    private String crdNoLast;
    /**
     * @Fields idTyp 证件类型
     */
    private String idTyp;
    /**
     * @Fields idNoEnc 加密证件号码
     */
    private String idNoEnc;
    /**
     * @Fields bnkPsnFlg 对公对私标志，B对公，C对私
     */
    private String bnkPsnFlg;
    /**
     * @Fields rutCorpOrg 路径合作机构号
     */
    private String rutCorpOrg;
    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields reqOrdNo 请求方订单号
     */
    private String reqOrdNo;
    /**
     * @Fields reqOrdDt 请求方订单日期
     */
    private LocalDate reqOrdDt;
    /**
     * @Fields reqOrdTm 请求方订单时间
     */
    private LocalTime reqOrdTm;
    /**
     * @Fields ntfSts 通知状态，W：待通知，S：通知成功，F：通知失败（可重发），E：通知失败（不可重发）
     */
    private String ntfSts;
    /**
     * @Fields ntfDt 通知日期
     */
    private LocalDate ntfDt;
    /**
     * @Fields ntfTm 通知时间
     */
    private LocalTime ntfTm;
    /**
     * @Fields ntfRspCd 通知返回码
     */
    private String ntfRspCd;
    /**
     * @Fields ntfRspMsg 通知返回信息
     */
    private String ntfRspMsg;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    /**
     * 初审人
     */
    private String firstAuditUser;

    /**
     * 初审时间
     */
    private LocalDateTime firstAuditTime;

    /**
     * 初审结果：APPROVED/REJECTED
     */
    private String firstAuditResult;

    /**
     * 初审意见
     */
    private String firstAuditOpinion;

    /**
     * 复核人
     */
    private String secondAuditUser;

    /**
     * 复核时间
     */
    private LocalDateTime secondAuditTime;

    /**
     * 复核结果：APPROVED/REJECTED
     */
    private String secondAuditResult;


    /**
     * 复核意见
     */
    private String secondAuditOpinion;


    /**
     * 执行时间（成功时）
     */
    private LocalDateTime executeTime;


    /**
     * 拒绝原因（最终拒绝原因）
     */
    private String rejectReason;

    public String getFudOrdNo() {
        return fudOrdNo;
    }

    public void setFudOrdNo(String fudOrdNo) {
        this.fudOrdNo = fudOrdNo;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public BigDecimal getOrdRfdAmt() {
        return ordRfdAmt;
    }

    public void setOrdRfdAmt(BigDecimal ordRfdAmt) {
        this.ordRfdAmt = ordRfdAmt;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public LocalDate getOrdSuccDt() {
        return ordSuccDt;
    }

    public void setOrdSuccDt(LocalDate ordSuccDt) {
        this.ordSuccDt = ordSuccDt;
    }

    public LocalTime getOrdSuccTm() {
        return ordSuccTm;
    }

    public void setOrdSuccTm(LocalTime ordSuccTm) {
        this.ordSuccTm = ordSuccTm;
    }

    public String getUserTyp() {
        return userTyp;
    }

    public void setUserTyp(String userTyp) {
        this.userTyp = userTyp;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getCrdNoLast() {
        return crdNoLast;
    }

    public void setCrdNoLast(String crdNoLast) {
        this.crdNoLast = crdNoLast;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNoEnc() {
        return idNoEnc;
    }

    public void setIdNoEnc(String idNoEnc) {
        this.idNoEnc = idNoEnc;
    }

    public String getBnkPsnFlg() {
        return bnkPsnFlg;
    }

    public void setBnkPsnFlg(String bnkPsnFlg) {
        this.bnkPsnFlg = bnkPsnFlg;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public String getNtfSts() {
        return ntfSts;
    }

    public void setNtfSts(String ntfSts) {
        this.ntfSts = ntfSts;
    }

    public LocalDate getNtfDt() {
        return ntfDt;
    }

    public void setNtfDt(LocalDate ntfDt) {
        this.ntfDt = ntfDt;
    }

    public LocalTime getNtfTm() {
        return ntfTm;
    }

    public void setNtfTm(LocalTime ntfTm) {
        this.ntfTm = ntfTm;
    }

    public String getNtfRspCd() {
        return ntfRspCd;
    }

    public void setNtfRspCd(String ntfRspCd) {
        this.ntfRspCd = ntfRspCd;
    }

    public String getNtfRspMsg() {
        return ntfRspMsg;
    }

    public void setNtfRspMsg(String ntfRspMsg) {
        this.ntfRspMsg = ntfRspMsg;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getFirstAuditUser() {
        return firstAuditUser;
    }

    public void setFirstAuditUser(String firstAuditUser) {
        this.firstAuditUser = firstAuditUser;
    }

    public LocalDateTime getFirstAuditTime() {
        return firstAuditTime;
    }

    public void setFirstAuditTime(LocalDateTime firstAuditTime) {
        this.firstAuditTime = firstAuditTime;
    }

    public String getFirstAuditResult() {
        return firstAuditResult;
    }

    public void setFirstAuditResult(String firstAuditResult) {
        this.firstAuditResult = firstAuditResult;
    }

    public String getFirstAuditOpinion() {
        return firstAuditOpinion;
    }

    public void setFirstAuditOpinion(String firstAuditOpinion) {
        this.firstAuditOpinion = firstAuditOpinion;
    }

    public String getSecondAuditUser() {
        return secondAuditUser;
    }

    public void setSecondAuditUser(String secondAuditUser) {
        this.secondAuditUser = secondAuditUser;
    }

    public LocalDateTime getSecondAuditTime() {
        return secondAuditTime;
    }

    public void setSecondAuditTime(LocalDateTime secondAuditTime) {
        this.secondAuditTime = secondAuditTime;
    }

    public String getSecondAuditResult() {
        return secondAuditResult;
    }

    public void setSecondAuditResult(String secondAuditResult) {
        this.secondAuditResult = secondAuditResult;
    }

    public String getSecondAuditOpinion() {
        return secondAuditOpinion;
    }

    public void setSecondAuditOpinion(String secondAuditOpinion) {
        this.secondAuditOpinion = secondAuditOpinion;
    }

    public LocalDateTime getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(LocalDateTime executeTime) {
        this.executeTime = executeTime;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }
}