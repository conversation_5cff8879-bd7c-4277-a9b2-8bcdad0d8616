/*
 * @ClassName ShortcutParamDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class ShortcutParamDO extends BaseDO {
    /**
     * @Fields shtParId 主键
     */
    private String shtParId;
    /**
     * @Fields rutCorpOrg 路径合作机构
     */
    private String rutCorpOrg;
    /**
     * @Fields crdAcTyp 卡种，D借记卡，C贷记卡
     */
    private String crdAcTyp;
    /**
     * @Fields smsTyp 短信类型，0平台下发平台校验，1银行下发平台校验，2银行下发银行校验
     */
    private String smsTyp;
    /**
     * @Fields expDtTyp 是否需要有效期，0不需要，1需要
     */
    private String expDtTyp;
    /**
     * @Fields cvv2Typ 是否需要CVV2，0不需要，1需要
     */
    private String cvv2Typ;
    /**
     * @Fields singPtl 单双协议类型，0单侧，1双侧
     */
    private String singPtl;
    /**
     * @Fields bnkMblTyp 是否需要银行预留手机号，0不需要，1需要
     */
    private String bnkMblTyp;
    /**
     * @Fields amtDLmt 日限额
     */
    private BigDecimal amtDLmt;
    /**
     * @Fields unsignFlg 解约标识，0单侧，1双侧
     */
    private String unsignFlg;
    /**
     * @Fields sucSmsFlg 成功短信标识，0不下发，1下发
     */
    private String sucSmsFlg;
    /**
     * @Fields creOprId 创建人ID
     */
    private String creOprId;
    /**
     * @Fields updOprId 修改人ID
     */
    private String updOprId;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getShtParId() {
        return shtParId;
    }

    public void setShtParId(String shtParId) {
        this.shtParId = shtParId;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getSmsTyp() {
        return smsTyp;
    }

    public void setSmsTyp(String smsTyp) {
        this.smsTyp = smsTyp;
    }

    public String getExpDtTyp() {
        return expDtTyp;
    }

    public void setExpDtTyp(String expDtTyp) {
        this.expDtTyp = expDtTyp;
    }

    public String getCvv2Typ() {
        return cvv2Typ;
    }

    public void setCvv2Typ(String cvv2Typ) {
        this.cvv2Typ = cvv2Typ;
    }

    public String getSingPtl() {
        return singPtl;
    }

    public void setSingPtl(String singPtl) {
        this.singPtl = singPtl;
    }

    public String getBnkMblTyp() {
        return bnkMblTyp;
    }

    public void setBnkMblTyp(String bnkMblTyp) {
        this.bnkMblTyp = bnkMblTyp;
    }

    public BigDecimal getAmtDLmt() {
        return amtDLmt;
    }

    public void setAmtDLmt(BigDecimal amtDLmt) {
        this.amtDLmt = amtDLmt;
    }

    public String getUnsignFlg() {
        return unsignFlg;
    }

    public void setUnsignFlg(String unsignFlg) {
        this.unsignFlg = unsignFlg;
    }

    public String getSucSmsFlg() {
        return sucSmsFlg;
    }

    public void setSucSmsFlg(String sucSmsFlg) {
        this.sucSmsFlg = sucSmsFlg;
    }

    public String getCreOprId() {
        return creOprId;
    }

    public void setCreOprId(String creOprId) {
        this.creOprId = creOprId;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}