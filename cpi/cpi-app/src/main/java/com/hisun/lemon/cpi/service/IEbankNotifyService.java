package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.bestpay.ebankpay.req.PlaceOrderNotifyReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.RefundNotifyReq;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.WeChatOrderNotifyRsp;
import com.hisun.lemon.cpi.alipay.ebankpay.req.AlipayOrderNotifyReq;

/**
 * 扫码下单结果回调通知
 */
public interface IEbankNotifyService {

    /**
     * 用户扫码或商户扫码支付，翼支付结果通知
     */
    void bestPayFundNotify(PlaceOrderNotifyReq placeOrderNotifyReq);

    /**
     * 用户扫码退款或商户扫码退款，翼支付结果通知
     */
    void bestPayRefundNotify(RefundNotifyReq refundNotifyReq);

    /**
     * 用户扫码或商户扫码支付，微信下单结果通知
     */
    WeChatOrderNotifyRsp weChatFundNotify(String resultData);
    /**
     * 用户扫码或商户扫码支付，支付宝下单结果通知
     */
    void alipayFundNotify(AlipayOrderNotifyReq alipayOrderNotifyReq);
}
