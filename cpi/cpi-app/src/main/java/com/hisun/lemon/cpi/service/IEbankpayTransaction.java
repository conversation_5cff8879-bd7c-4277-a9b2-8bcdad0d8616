package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dto.EbankpayReqDTO;
import com.hisun.lemon.cpi.entity.EbankOrderDO;
import com.hisun.lemon.cpi.entity.RouteDO;

/**
 * Created by Rui on 2017/8/1.
 */
public interface IEbankpayTransaction {

    /**
     * 登记网银订单和网银订单流水
     */
    EbankOrderDO createEbankOrder(EbankpayReqDTO ebankpayReqDTO, RouteDO rspRouteInfo,String funOrdNo);

    /**
     * 更新网银订单
     */
    void updateEbankOrder(EbankOrderDO ebankOrderDO);

    /**
     * 根据银行返回更新网易以及充值订单
     */
    void updateOrderStateByBank(EbankOrderDO ebankOrderDO, BaseBnkRspBO baseBnkRspBO, CpiMsgCd cpiMsgCd, String SplOrdFlg);

    /**
     * 根据对账外键查询网银订单
     */
    EbankOrderDO selectEbankpayOrderByChkKey(String chkKey);
}
