package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpi.dto.CardBinRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * Created by Rui on 2017/7/7.
 */
public interface ICardService {

    /**
     * 查询快捷银行卡协议列表
     * @param corpBusTyp
     * @param corpBusSubTyp
     * @param userNo
     * @param crdAcTyp
     * @return
     */
    GenericRspDTO<AgrInfoRspDTO> queryCardsInfo(CorpBusTyp corpBusTyp,
                                                CorpBusSubTyp corpBusSubTyp,
                                                String userNo,
                                                String crdAcTyp);

    /**
     * 根据卡号查询卡Bin信息
     * @param crdNo
     * @return
     */
    GenericRspDTO<CardBinRspDTO> queryCardBin(String crdNo);

    /**
     * 校验卡号
     * @param agrNo
     * @param crdNo
     * @return
     */
    GenericRspDTO<NoBody> verifyCard(String agrNo, String crdNo);
}
