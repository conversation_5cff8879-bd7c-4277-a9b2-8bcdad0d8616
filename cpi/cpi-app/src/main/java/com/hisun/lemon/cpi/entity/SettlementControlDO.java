/*
 * @ClassName SettlementControlDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-17 18:33:02
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class SettlementControlDO extends BaseDO {
    /**
     * @Fields settleBatchNo 结算批次号
     */
    private String settleBatchNo;
    /**
     * @Fields settleDate 结算批次日期
     */
    private LocalDate settleDate;
    /**
     * @Fields settleDate 结算批次日期
     */
    private String settleDateTime;
    /**
     * @Fields rutCorg 资金机构
     */
    private String rutCorg;

    /**
     * @Fields 结算批次状态
     */
    private String settleSts;
    /**
     * @Fields settleFlag 结算入库是否完成 Y:是 N:否
     */
    private String settleFlag;
    /**
     * @Fields settleFileExist 是否有结算文件 Y:是 N:否
     */
    private String settleFileExist;
    /**
     * @Fields settleFileName 结算文件名
     */
    private String settleFileName;
    /**
     * @Fields fileRcvFlag 结算文件是否下载 0：未下载 1：已下载
     */
    private String fileRcvFlag;
    /**
     * @Fields fileRcvDt 文件下载日期
     */
    private LocalDate fileRcvDt;
    /**
     * @Fields rmk 
     */
    private String rmk;

    public String getSettleBatchNo() {
        return settleBatchNo;
    }

    public void setSettleBatchNo(String settleBatchNo) {
        this.settleBatchNo = settleBatchNo;
    }

    public LocalDate getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(LocalDate settleDate) {
        this.settleDate = settleDate;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getSettleFlag() {
        return settleFlag;
    }

    public void setSettleFlag(String settleFlag) {
        this.settleFlag = settleFlag;
    }

    public String getSettleFileExist() {
        return settleFileExist;
    }

    public void setSettleFileExist(String settleFileExist) {
        this.settleFileExist = settleFileExist;
    }

    public String getSettleFileName() {
        return settleFileName;
    }

    public void setSettleFileName(String settleFileName) {
        this.settleFileName = settleFileName;
    }

    public String getFileRcvFlag() {
        return fileRcvFlag;
    }

    public void setFileRcvFlag(String fileRcvFlag) {
        this.fileRcvFlag = fileRcvFlag;
    }

    public LocalDate getFileRcvDt() {
        return fileRcvDt;
    }

    public void setFileRcvDt(LocalDate fileRcvDt) {
        this.fileRcvDt = fileRcvDt;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getSettleDateTime() {
        return settleDateTime;
    }

    public void setSettleDateTime(String settleDateTime) {
        this.settleDateTime = settleDateTime;
    }

    public String getSettleSts() {
        return settleSts;
    }

    public void setSettleSts(String settleSts) {
        this.settleSts = settleSts;
    }
}