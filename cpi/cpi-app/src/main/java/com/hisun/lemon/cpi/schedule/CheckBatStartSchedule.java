package com.hisun.lemon.cpi.schedule;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.entity.AccControlDO;
import com.hisun.lemon.cpi.entity.AccParamDO;
import com.hisun.lemon.cpi.service.ICheckMgrService;
import com.hisun.lemon.cpi.thread.CheckHandleThread;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 定时任务-发起对账服务
 */
@Component
public class CheckBatStartSchedule {
    private static final Logger logger = LoggerFactory.getLogger(CheckBatStartSchedule.class);

    /**
     * 注入对账主控服务
     */
    @Resource
    private ICheckMgrService checkMgrService;

    @Resource
    private TaskExecutor taskExecutor;

    @Resource
    private DistributedLocker distributedLocker;

    /**
     * 定时任务执行方法
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 0/30 3-22 * * ?")
    public void execute() {
        logger.debug("==================对账定时任务 开始时间：" + DateTimeUtils.getCurrentDateTimeStr());
        List<AccParamDO> AccParamList = checkMgrService.queryEffAccParamList();
        AccControlDO accControlDO = null;
        try {
            if(CollectionUtils.isNotEmpty(AccParamList)){
                for (AccParamDO accParamDO : AccParamList) {
                    // 根据生效的对账参数，查询未完成的、最小文件日期的对账批次
                    accControlDO = checkMgrService.queryUnfinishedAccControl(accParamDO);
                    if (JudgeUtils.isNotNull(accControlDO)
                            && accControlDO.getChkBegTm().compareTo(DateTimeUtils.getCurrentLocalTime()) <= 0
                            && accControlDO.getChkEndTm().compareTo(DateTimeUtils.getCurrentLocalTime()) >= 0) {
                        taskExecutor.execute(new CheckHandleThread(accControlDO, checkMgrService, distributedLocker));
                        logger.debug("==================异步调起对账，对账批次号: " + accControlDO.getChkBatNo());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("对账定时任务执行失败，异常为 " + e);
            return ;
        }
        logger.debug("=================对账定时任务 结束时间：" + DateTimeUtils.getCurrentDateTimeStr());
    }

}
