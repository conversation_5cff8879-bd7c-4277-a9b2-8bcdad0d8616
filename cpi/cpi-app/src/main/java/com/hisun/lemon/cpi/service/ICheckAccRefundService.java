package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.entity.AccControlDO;
import com.hisun.lemon.cpi.entity.AccRefundCfgDO;

/**
 * 对账服务扩展 service
 * 业务类型：退款(快捷退款、网银退款、汇款退款)
 */
public interface ICheckAccRefundService {
    /**
     * 根据银行成功的对账明细，和我方订单进行对账
     * @param accControlDO 对账批次信息
     * @param accRefundCfgDO 对账配置信息
     */
    String checkAccountRefund(AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO) throws Exception;

    /**
     * 退款(快捷退款、网银退款、汇款退款)业务对账成功，将对平金额汇总进行记账
     */
    boolean accountTreatmentRefund(AccControlDO accControlDO);
}
