package com.hisun.lemon.cpi.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.service.IEbankNotifyService;
import com.hisun.lemon.cpi.utils.XmlParseUtils;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.WeChatOrderNotifyRsp;
import com.hisun.lemon.framework.controller.BaseController;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 微信统一下单接口返回结果回调地址
 */
@RestController
@RequestMapping("/cpi/wechat")
@Api(tags="WeChatNotifyController", description="微信下单结果通知服务")
public class WeChatNotifyController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(WeChatNotifyController.class);

    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private IEbankNotifyService ebankNotifyService;

    /**
     * 微信统一下单结果回调方法
     */
    @PostMapping("/placeOrderNotify")
    public String placeOrderNotify(@RequestBody String resultData) {
        //平台返回给微信的结果
        WeChatOrderNotifyRsp weChatOrderNotifyRsp = new WeChatOrderNotifyRsp();
        logger.info("WechatNotifyController.placeOrderNotify() 微信下单结果回调通知：" + resultData);
        if(JudgeUtils.isNotEmpty(resultData)) {
            //扫码订单处理
            weChatOrderNotifyRsp = ebankNotifyService.weChatFundNotify(resultData);
        } else {
            weChatOrderNotifyRsp.setReturn_code(CpiConstants.WECHAT_FAIL);
        }
        return XmlParseUtils.marshallerObjectToXmlStr(weChatOrderNotifyRsp, WeChatOrderNotifyRsp.class);
    }

}
