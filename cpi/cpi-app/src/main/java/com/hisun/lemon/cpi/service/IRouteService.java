package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.RouteRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericRspDTO;

public interface IRouteService {

    /**
     * 根据业务类型和子类型查询生效的机构
     * @param corpBusTyp
     * @param corpBusSubTyp
     * @return
     */
    GenericRspDTO<RouteRspDTO> queryEffOrgInfo(CorpBusTyp corpBusTyp, CorpBusSubTyp corpBusSubTyp);
}
