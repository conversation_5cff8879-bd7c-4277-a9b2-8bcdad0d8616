/*
 * @ClassName PosPreAuJrnDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-18 15:01:57
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class PosPreAuJrnDO extends BaseDO {
    /**
     * @Fields preJrnNo 内部流水号
     */
    private String preJrnNo;
    /**
     * @Fields crdCorpOrg 资金合作机构
     */
    private String crdCorpOrg;
    /**
     * @Fields rutCorpOrg 路径合作机构
     */
    private String rutCorpOrg;
    /**
     * @Fields corpBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields crdAcTyp 银行卡类型，C贷记卡，D借记卡
     */
    private String crdAcTyp;
    /**
     * @Fields crdNoEnc 加密银行卡号
     */
    private String crdNoEnc;
    /**
     * @Fields ordCcy 币种
     */
    private String ordCcy;
    /**
     * @Fields ordAmt 订单金额
     */
    private BigDecimal ordAmt;
    /**
     * @Fields trmNo 终端号
     */
    private String trmNo;
    /**
     * @Fields reqOrdNo 请求方订单号
     */
    private String reqOrdNo;
    /**
     * @Fields reqOrdDt 请求方订单日期
     */
    private LocalDate reqOrdDt;
    /**
     * @Fields reqOrdTm 请求方订单时间
     */
    private LocalTime reqOrdTm;
    /**
     * @Fields orgOrdNo 合作机构外部订单号（系统生成）
     */
    private String orgOrdNo;
    /**
     * @Fields orgJrnNo 合作机构返回的流水号
     */
    private String orgJrnNo;
    /**
     * @Fields orgOrdDt 合作机构交易日期
     */
    private LocalDate orgOrdDt;
    /**
     * @Fields orgOrdTm 合作机构交易时间
     */
    private LocalTime orgOrdTm;
    /**
     * @Fields orgRspCd 合作机构返回码
     */
    private String orgRspCd;
    /**
     * @Fields orgRspMsg 合作机构返回信息
     */
    private String orgRspMsg;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getPreJrnNo() {
        return preJrnNo;
    }

    public void setPreJrnNo(String preJrnNo) {
        this.preJrnNo = preJrnNo;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getTrmNo() {
        return trmNo;
    }

    public void setTrmNo(String trmNo) {
        this.trmNo = trmNo;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public String getOrgOrdNo() {
        return orgOrdNo;
    }

    public void setOrgOrdNo(String orgOrdNo) {
        this.orgOrdNo = orgOrdNo;
    }

    public String getOrgJrnNo() {
        return orgJrnNo;
    }

    public void setOrgJrnNo(String orgJrnNo) {
        this.orgJrnNo = orgJrnNo;
    }

    public LocalDate getOrgOrdDt() {
        return orgOrdDt;
    }

    public void setOrgOrdDt(LocalDate orgOrdDt) {
        this.orgOrdDt = orgOrdDt;
    }

    public LocalTime getOrgOrdTm() {
        return orgOrdTm;
    }

    public void setOrgOrdTm(LocalTime orgOrdTm) {
        this.orgOrdTm = orgOrdTm;
    }

    public String getOrgRspCd() {
        return orgRspCd;
    }

    public void setOrgRspCd(String orgRspCd) {
        this.orgRspCd = orgRspCd;
    }

    public String getOrgRspMsg() {
        return orgRspMsg;
    }

    public void setOrgRspMsg(String orgRspMsg) {
        this.orgRspMsg = orgRspMsg;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}