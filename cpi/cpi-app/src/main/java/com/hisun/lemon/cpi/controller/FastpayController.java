package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.service.IFastpayService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Created by Rui on 2017/7/5.
 */
@RestController
@RequestMapping("/cpi/fastpay")
@Api(tags="FastpayController", description="快捷服务")
public class FastpayController extends BaseController {

    @Resource
    IFastpayService fastpayService;

    @ApiOperation(value="用户银行卡预签约", notes="用户银行卡预签约，银行(短信平台)下发短信验证码")
    @ApiResponse(code = 200, message = "预签约结果")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @PostMapping("/pre/cards")
    public GenericRspDTO<CardPreRspDTO> preBindCard(@Validated @RequestBody GenericDTO<CardPreReqDTO> genericDTO) {
        //主要是短信的校验
        return fastpayService.preBindCard(genericDTO);
    }

    @ApiOperation(value="绑定用户银行卡信息", notes="绑定用户银行卡信息")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "签约结果")
    @PostMapping("/cards")
    public GenericRspDTO<CardBindRspDTO> bindCard(@Validated @RequestBody GenericDTO<CardBindReqDTO> genericDTO) {
        return fastpayService.bindCard(genericDTO);
    }

    @ApiOperation(value="用户银行卡解绑", notes="用户银行卡解绑")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "解绑结果")
    @PutMapping("/cards")
    public GenericRspDTO<NoBody> unBindCard(@Validated @RequestBody GenericDTO<CardUnBindReqDTO> genericDTO) {
        return fastpayService.unBindCard(genericDTO);
    }

    @ApiOperation(value="快捷支付", notes="快捷支付")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "付款结果")
    @PostMapping("/payment")
    public GenericRspDTO<FastpayRspDTO> fastpay(@Validated @RequestBody GenericDTO<FastpayReqDTO> genericDTO) {
        return fastpayService.fastpay(genericDTO);
    }

    @ApiOperation(value="快捷订单结果查询", notes="快捷订单结果查询")
    @ApiResponse(code = 200, message = "订单结果")
    @GetMapping("/result")
    public  GenericRspDTO<OrderResultRspDTO> orderQuery(@Validated @ApiParam(name = "ordNo", value = "订单号", required = true) @RequestParam(value = "ordNo") String ordNo) {
        return fastpayService.queryOrder(ordNo);
    }


    @ApiOperation(value="ACLEDA快捷支付", notes="ACLEDA快捷支付")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "ACLEDA快捷支付sessionId,paymentTokenId")
    @PostMapping("/payment/acleda")
    public GenericRspDTO<FastpayAcledaRspDTO> fastpayAcleda(@Validated @RequestBody GenericDTO<FastpayAcledaReqDTO> genericDTO) {
        return fastpayService.fastpayAcleda(genericDTO);
    }

    @ApiOperation(value="ACLEDA快捷支付结果通知", notes="ACLEDA快捷支付结果通知")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "ACLEDA快捷支付结果通知")
    @PostMapping("/acleda/notify")
    public GenericRspDTO acledaPayNotify(@Validated @RequestBody GenericDTO<FastpayAcledaNotifyReqDTO> genericDTO) {
        fastpayService.acledaPayNotify(genericDTO);
        return GenericRspDTO.newInstance();
    }

}
