package com.hisun.lemon.cpi.bnkapi.alipay;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.collect.Lists;
import com.hisun.channel.common.lifecycle.LifecycleBase;

public class AlipayUtil {
	protected static final Logger logger = LoggerFactory.getLogger(LifecycleBase.class);
	/**
	 * 加签公共方法
	 * 
	 * @return
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws UnsupportedEncodingException
	 */
	public static List<NameValuePair> pubSign(Object object) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		List<String> fieldNames = new ArrayList<String>();
		List<NameValuePair> params = Lists.newArrayList(); //返回参数 
		Field[] fields = object.getClass().getDeclaredFields();
		for (Field field : fields) {
			String fieName = field.getName();
			boolean accessFlag = field.isAccessible();
			field.setAccessible(true);
			Object obj = field.get(object);
			if (obj != null) {//过滤空值和sign、sing_type
				if (fieName != "sign"&&fieName != "sing_type") {
					map.put(fieName, obj.toString());
					fieldNames.add(fieName);
					params.add(new BasicNameValuePair(fieName, obj.toString()));//添加参数 
				}
			}
			field.setAccessible(accessFlag);
		}

		StringBuilder paramStringBuffer = new StringBuilder();
		Collections.sort(fieldNames);
		for (int i = 0; i < fieldNames.size(); i++) {
			String key = fieldNames.get(i);
			String value = map.get(key);
			paramStringBuffer.append("&").append(key).append("=").append(value);
		}
		// 去掉请求字符串末尾的最后一个&号
		if (paramStringBuffer.indexOf("&", 0) == 0) {
			paramStringBuffer.deleteCharAt(0);
		}
		String signString = paramStringBuffer/*.append("&key=")*/ + AlipayConstants.KEY;
		String result = DigestUtils.md5Hex(signString.getBytes("UTF-8"));
		
		// 返回的请求参数字符串
		params.add(new BasicNameValuePair("sign", result));//添加参数 
		params.add(new BasicNameValuePair("sign_type", AlipayConstants.SIGN_TYPE));//添加参数 
		return params;
	}
}
