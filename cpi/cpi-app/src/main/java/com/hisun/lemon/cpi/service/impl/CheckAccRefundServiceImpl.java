package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.chk.client.ChkClient;
import com.hisun.lemon.chk.common.ChkConstants;
import com.hisun.lemon.chk.dto.ErrorDTO;
import com.hisun.lemon.common.extension.ExtensionLoader;
import com.hisun.lemon.common.utils.*;
import com.hisun.lemon.cpi.bo.BaseAccRefundBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.dao.IAccErrorDao;
import com.hisun.lemon.cpi.dao.IRefundOrderDao;
import com.hisun.lemon.cpi.dao.IRefundParamDao;
import com.hisun.lemon.cpi.dao.IRefundSuborderDao;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.service.ICheckAccRefundService;
import com.hisun.lemon.cpi.utils.AccountUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 对账服务扩展 service
 * 业务类型：退款(快捷退款、网银退款)
 */
@Service
public class CheckAccRefundServiceImpl extends BaseService implements ICheckAccRefundService {

    private static final Logger logger = LoggerFactory.getLogger(CheckAccRefundServiceImpl.class);

    @Resource
    private IAccErrorDao accErrorDao;

    @Resource
    private IRefundSuborderDao refundSuborderDao;

    @Resource
    private ChkClient chkClient;

    @Resource
    private IRefundParamDao refundParamDao;

    @Resource
    private IRefundOrderDao refundOrderDao;

    /**
     * 账务处理接口
     */
    @Resource
    private AccountingTreatmentClient accTreatClient;

    /**
     * 根据银行成功的对账明细，和我方订单进行对账
     * @param accControlDO 对账批次信息
     * @param accRefundCfgDO 对账配置信息
     */
    @Override
    @SuppressWarnings("unchecked")
    public String checkAccountRefund(AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO) throws Exception {
        //step1: 对账批次信息
        String chkFilSts = accControlDO.getChkFilSts();
        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusTyp = accControlDO.getCorpBusTyp();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        logger.debug("开始对账，机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo);

        //step2: 分页查询银行对账明细，查询结果进行排序后，每次取最大记录为queryNum
        int i = 0;//第i次查询银行明细
        int beginNum = 0;//从第多少条开始取记录
        int countNum = 0;//每次取多少条记录
        int queryNum = accRefundCfgDO.getQueryNum();//每次查询大最大笔数

        List<Object> checkDOList = null;//银行API分页查询返回的明细 List
        String selectDetailMethod = accRefundCfgDO.getSelectDetailMethod();//查询对账明细需执行的方法
        String chkClazzName = accRefundCfgDO.getChkClazz();//银行API类名
        String txSts = accRefundCfgDO.getSuccessFlag();//银行对账明细为交易成功对应的值

        //反射得到银行API类对账实例和执行方法
        Class chkClazz = Class.forName(chkClazzName);
        Method method = chkClazz.getMethod(selectDetailMethod, String.class, String.class, Integer.class, Integer.class);

        //将首字母转成小写，获取银行API类的bean
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(Character.toLowerCase(chkClazz.getSimpleName().charAt(0)));
        stringBuilder.append(chkClazz.getSimpleName().substring(1));
        String beanName = stringBuilder.toString();

        //获取到Spring容器中的对象
        Object instance = ExtensionLoader.getSpringBean(beanName, chkClazz);

        //step3: 分页查询，进行对账
        BaseAccRefundBO baseAccRefundBO = new BaseAccRefundBO();
        while (true) {
            beginNum = queryNum * i;
            countNum = queryNum;

            //通过反射，调用查询银行对账明细的方法
            checkDOList = (List<Object>)method.invoke(instance, chkBatNo, txSts, beginNum, countNum);

            //银行对账明细不为空，开始对账
            if (CollectionUtils.isNotEmpty(checkDOList)) {
                checkAccount(checkDOList, baseAccRefundBO, accControlDO, accRefundCfgDO);
            } else {
                //查无对账明细数据，退出循环
                break;
            }
            i++;
        }

        //step4: 将我方ORD_STS='S'、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'(我方有机构无)，并录入差错
        updateChkSts5To2(baseAccRefundBO, accControlDO);

        //step5: 将我方ORD_STS='S'、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)
        updateChkSts0To5(accControlDO);

        //step6: 录入差错表
        insertChkAccError(baseAccRefundBO, accControlDO, accRefundCfgDO);

        //step7: 3: 对账成功，待账务处理
        chkFilSts = CpiConstants.CHK_FIL_SUCCESS;
        return chkFilSts;
    }


    /**
     * 分页查询后，进行明细对账
     * @param checkDOList 分页查询获取对账明细 List
     * @param baseAccRefundBO 对账主控服务差异记录扩展业务对象
     * @param accControlDO 对账批次信息
     * @param accRefundCfgDO 对账配置信息
     * @throws Exception 抛出异常
     */
    @SuppressWarnings("unchecked")
    private void checkAccount(List<Object> checkDOList, BaseAccRefundBO baseAccRefundBO, AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO)  throws Exception {
        //step1：初始化参数
        String ordNo = null;//银行明细中的公司方交易流水号
        String txSts = null;//银行交易状态
        String successFlg = null;//对账配置参数中的成功标志
        BigDecimal checkAmt = null;//银行订单金额
        BigDecimal totMchAmt = new BigDecimal(0);//对平总金额
        Integer totMchCnt = 0;//对平总笔数

        LocalDate chkDt = accControlDO.getChkFilDt();//对账日期
        LocalTime chkTm = DateTimeUtils.getCurrentLocalTime();//对账时间
        List<Object> orgExistList = new ArrayList<>();//机构有我方无的差异记录，保存银行对账明细
        List<Object> amtErrorList = new ArrayList<>();//金额不相等的差异记录，保存银行对账明细

        RefundSuborderDO myRefundSuborderDO = null;//我方快捷订单信息
        RefundSuborderDO updateRefundSuborderDO = null;//用于更新我方快捷订单信息

        //获取更新的方法
        String chkClazz = accRefundCfgDO.getChkClazz();
        String updateMethod = accRefundCfgDO.getUpdateMethod();
        Class bankApi = Class.forName(chkClazz);
        Method method = bankApi.getMethod(updateMethod, String.class, String.class, String.class);

        //将首字母转成小写
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(Character.toLowerCase(bankApi.getSimpleName().charAt(0)));
        stringBuilder.append(bankApi.getSimpleName().substring(1));
        String beanName = stringBuilder.toString();

        //获取到Spring容器中的对象
        Object instance = ExtensionLoader.getSpringBean(beanName, bankApi);

        //取出每条银行对账明细，根据订单号，查询我方订单信息
        for (Object orgCheckDO : checkDOList) {
            //1.获取银行对账明细类DO
            Class clazz = orgCheckDO.getClass();

            //获取银行明细中的机构流水号
            Field checkKey = clazz.getDeclaredField(accRefundCfgDO.getCheckKeyFiled());
            ordNo = ReflectionUtils.getField(checkKey,orgCheckDO).toString();

            //获取银行明细的交易状态
            if(JudgeUtils.isNotEmpty(accRefundCfgDO.getTxStsFiled())) {
                Field txStsField = clazz.getDeclaredField(accRefundCfgDO.getTxStsFiled());
                txSts = ReflectionUtils.getField(txStsField, orgCheckDO).toString();
                successFlg = accRefundCfgDO.getSuccessFlag();
            } else {
                //若没有配置订单成功域或成功标志，则默认银行对账明细中都是成功订单
                txSts = CpiConstants.ORD_SUCCESS;
                successFlg = CpiConstants.ORD_SUCCESS;
            }

            //获取银行明细的交易金额，以元为单位
            Field checkAmtField = clazz.getDeclaredField(accRefundCfgDO.getCheckAmtFiled());
            checkAmt = new BigDecimal(ReflectionUtils.getField(checkAmtField,orgCheckDO).toString());

            //2.根据订单号查询我方的快捷订单信息
            myRefundSuborderDO = refundSuborderDao.selectRefundSuborderByChkKey(ordNo);

            //3.若我方没有该订单信息，则存入【机构有我方无】差异记录
            if (JudgeUtils.isNull(myRefundSuborderDO)) {
                orgExistList.add(orgCheckDO);

                //更新银行对账明细的对账状态
                method.invoke(instance, ordNo, txSts, CpiConstants.CHK_STS_ORG_EXIST);

            } else if (checkAmt.compareTo(myRefundSuborderDO.getOrdAmt()) != 0) {
                //4.若金额不相等，则存入【金额不相等】差异记录
                amtErrorList.add(orgCheckDO);

                //更新快捷订单对账状态
                updateRefundSuborderDO = new RefundSuborderDO();
                updateRefundSuborderDO.setChkKey(ordNo);
                updateRefundSuborderDO.setChkDt(chkDt);
                updateRefundSuborderDO.setChkTm(chkTm);
                updateRefundSuborderDO.setChkSts(CpiConstants.CHK_STS_AMT_ERROR);
                refundSuborderDao.updateRefundSuborderByChkKey(updateRefundSuborderDO);

                //更新银行对账明细的对账状态
                method.invoke(instance, ordNo, txSts, CpiConstants.CHK_STS_AMT_ERROR);

//            } else if (StringUtils.equals(txSts, accRefundCfgDO.getSuccessFlag())
//                    && !StringUtils.equals(myRefundSuborderDO.getOrdSts(), CpiConstants.ORD_SUCCESS)
//                    && !StringUtils.equals(myRefundSuborderDO.getOrdSts(), CpiConstants.ORD_PAY_BANKING) ) {
//                //5.若金额相等，我方为F1-不成功(或者非W2-处理中)，银行成功，则存入【机构有我方无】差异记录
//                orgExistList.add(orgCheckDO);
//
//                //更新子订单对账状态
//                updateRefundSuborderDO = new RefundSuborderDO();
//                updateRefundSuborderDO.setChkKey(ordNo);
//                updateRefundSuborderDO.setChkDt(chkDt);
//                updateRefundSuborderDO.setChkTm(chkTm);
//                updateRefundSuborderDO.setChkSts(CpiConstants.CHK_STS_ORG_EXIST);
//                refundSuborderDao.updateRefundSuborderByChkKey(updateRefundSuborderDO);
//
//                //更新银行对账明细的对账状态
//                method.invoke(instance, ordNo, txSts, CpiConstants.CHK_STS_ORG_EXIST);
//
            } else if (StringUtils.equals(txSts, successFlg)
                    && (StringUtils.equals(myRefundSuborderDO.getOrdSts(), CpiConstants.ORD_WATING_PAY)
                        || StringUtils.equals(myRefundSuborderDO.getOrdSts(), CpiConstants.ORD_PAY_BANKING)
                        || StringUtils.equals(myRefundSuborderDO.getOrdSts(), CpiConstants.ORD_SUCCESS)) ) {
                //6.银行退款订单为成功，我方退款订单为W1-等待退款、W2-银行处理中，则视为对平、对账成功
                //更新子订单对账状态
                RefundSuborderDO refundSuborderDO = refundSuborderDao.selectRefundSuborderByChkKey(ordNo);
                RefundOrderDO refundOrderDO = refundOrderDao.get(refundSuborderDO.getRfdOrdNo());

                updateRefundSuborderDO = new RefundSuborderDO();
                updateRefundSuborderDO.setSubOrdNo(refundSuborderDO.getSubOrdNo());
                updateRefundSuborderDO.setChkDt(chkDt);
                updateRefundSuborderDO.setChkTm(chkTm);
                updateRefundSuborderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
                updateRefundSuborderDO.setChkSts(CpiConstants.CHK_STS_SUCCESS);
                updateRefundSuborderDO.setAcDt(LemonUtils.getAccDate());
                refundSuborderDao.update(updateRefundSuborderDO);

                RefundOrderDO updateRefundOrderDo = new RefundOrderDO();
                updateRefundOrderDo.setRfdOrdNo(refundOrderDO.getRfdOrdNo());
                updateRefundOrderDo.setOrdSts(CpiConstants.ORD_SUCCESS);
                refundOrderDao.update(updateRefundOrderDo);

                //更新银行对账明细的对账状态
                method.invoke(instance, ordNo, txSts, CpiConstants.CHK_STS_SUCCESS);

                //对平金额和笔数累加
                totMchAmt = totMchAmt.add(checkAmt);
                totMchCnt++;
            }
        }

        //更新累计结果
        accControlDO.setTotMchAmt(totMchAmt);
        accControlDO.setTotMchCnt(totMchCnt);

        //更新对账差错订单
        if(CollectionUtils.isNotEmpty(orgExistList)) {
            baseAccRefundBO.addAllOrgExistList(orgExistList);
        }
        if(CollectionUtils.isNotEmpty(amtErrorList)) {
            baseAccRefundBO.addAllAmtErrorList(amtErrorList);
        }
    }

    /**
     * 将我方ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'(我方有机构无)，并录入差错
     */
    private void updateChkSts5To2(BaseAccRefundBO baseAccRefundBO, AccControlDO accControlDO) {
        //订单状态为成功的对应值
        String oldChkSts = CpiConstants.CHK_STS_DOUBT;
        String newChkSts = CpiConstants.CHK_STS_PLAT_EXIST;

        //对账批次信息
        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusTyp = accControlDO.getCorpBusTyp();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        LocalDate chkDt = accControlDO.getChkFilDt();
        LocalTime chkTm = DateTimeUtils.getCurrentLocalTime();
        logger.debug("对账服务-我方ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'(我方有机构无)，并录入差错，机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 存疑订单(5)更新为(2)我方有机构无");

        //累计存疑转差错总金额和笔数
        BigDecimal dbtErrAmt = new BigDecimal(0);
        Integer dbtErrCnt = 0;

        //登记我方有机构无的订单
        List<RefundSuborderDO> platExistList = new ArrayList<>();

        //1.查询我方ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单，(ORD_DT)订单日期小于对账日期
        List<RefundSuborderDO> refundSuborderDOList = refundSuborderDao.getRefundSuborderListByChkStsDoubt(oldChkSts, chkDt);

        //2.判断我方存疑订单集合是否为空
        if (CollectionUtils.isNotEmpty(refundSuborderDOList)) {
            for (RefundSuborderDO refundSuborderDO : refundSuborderDOList) {
                //3.录入差错
                refundSuborderDO.setChkSts(CpiConstants.CHK_STS_PLAT_EXIST);
                platExistList.add(refundSuborderDO);

                //存疑转差错金额和笔数累加
                dbtErrAmt = dbtErrAmt.add(refundSuborderDO.getOrdAmt());
                dbtErrCnt++;
            }

            //4.将ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'，(ORD_DT)订单日期小于对账日期
            refundSuborderDao.updateRefundSuborderByChkStsDoubt(oldChkSts, newChkSts, chkDt, chkTm);

            //5.更新对账差错订单 和 累计结果
            accControlDO.setDbtErrAmt(dbtErrAmt);
            accControlDO.setDbtErrCnt(dbtErrCnt);

            if (CollectionUtils.isNotEmpty(platExistList)) {
                baseAccRefundBO.addAllPlatExistList(platExistList);
            }
        }
    }

    /**
     * 将我方ORD_STS='S'、CHK_STS='0'(未对账)的订单对账状态更新为CHK_STS='5'(存疑)
     */
    private void updateChkSts0To5(AccControlDO accControlDO) {
        //订单状态为成功的对应值
        String ordSts = CpiConstants.ORD_SUCCESS;
        String oldChkSts = CpiConstants.CHK_STS_NOT_START;
        String newChkSts = CpiConstants.CHK_STS_DOUBT;

        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        LocalDate chkDt = accControlDO.getChkFilDt();
        LocalTime chkTm = DateTimeUtils.getCurrentLocalTime();
        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 未对账订单(0)更新为(5)存疑");

        //累计存疑订单金额和笔数
        BigDecimal doubtAmt = new BigDecimal(0);
        Integer doubtCnt = 0;

        //1.查询我方ORD_STS='S'、CHK_STS='0'(未对账)的订单，订单日期等于对账日期
        List<RefundSuborderDO> refundSuborderDOList = refundSuborderDao.getRefundSuborderListByChkStsNotstart(oldChkSts, chkDt);

        //2.判断我方未对账订单集合是否为空
        if (CollectionUtils.isNotEmpty(refundSuborderDOList)) {

            for (RefundSuborderDO refundSuborderDO : refundSuborderDOList) {

                //存疑金额和笔数累加
                doubtAmt = doubtAmt.add(refundSuborderDO.getOrdAmt());
                doubtCnt++;
            }

            //3.将ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)
            refundSuborderDao.updateRefundSuborderByChkStsNotstart(oldChkSts, newChkSts, chkDt, chkTm);

            //4.更新存疑总金额和笔数
            accControlDO.setDoubtAmt(doubtAmt);
            accControlDO.setDoubtCnt(doubtCnt);
        }
    }

    /**
     * 录入对账差错信息表，并进行差错账务处理
     */
    private void insertChkAccError(BaseAccRefundBO baseAccRefundBO, AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO) throws Exception {
        //1.获取所有的对账差错数据
        List<Object> orgExistList = baseAccRefundBO.getOrgExistList();//机构有我方无的差异记录，保存银行对账明细
        List<Object> amtErrorList = baseAccRefundBO.getAmtErrorList();//金额不相等的差异记录，保存银行对账明细
        List<RefundSuborderDO> platExistList = baseAccRefundBO.getPlatExistList();//我方有机构无的差异记录，保存我方订单信息

        //对账批次信息
        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        LocalDate chkDt = accControlDO.getChkFilDt();
        LocalTime chkTm = DateTimeUtils.getCurrentLocalTime();
        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 开始录入对账差错记录");

        //累计金额和笔数
        BigDecimal shortAmt = new BigDecimal(0);//短款总金额
        Integer shortCnt = 0;//短款总笔数
        BigDecimal longAmt = new BigDecimal(0);//长款总金额
        Integer longCnt = 0;//长款总笔数
        BigDecimal errTotAmt = new BigDecimal(0);//金额错误类型总金额
        Integer errTotCnt = 0;//金额错误类型总笔数

        BigDecimal txAmt = BigDecimal.ZERO;//交易金额

        //2.录入差异记录(我方有机构无)，T-1日对账已更新状态为存疑，T日将存疑的订单更新为我方有机构无
        if (CollectionUtils.isNotEmpty(platExistList)) {
            AccErrorDO accErrorDO = null;//差异信息对象，录入差错使用
            for (RefundSuborderDO refundSuborderDO : platExistList) {
                accErrorDO = new AccErrorDO();
                accErrorDO.setChkErId(IdGenUtils.generateIdWithDateTime("CHK", 6));
                accErrorDO.setRutCorg(refundSuborderDO.getRutCorpOrg());
                accErrorDO.setCorpBusTyp(refundSuborderDO.getCorpBusTyp());
                accErrorDO.setCorpBusSubTyp(refundSuborderDO.getCorpBusSubTyp());
                accErrorDO.setErrKeyId(refundSuborderDO.getChkKey());
                accErrorDO.setChkErrDt(chkDt);
                accErrorDO.setChkErrTm(chkTm);
                accErrorDO.setChkBatNo(chkBatNo);
                accErrorDO.setMyTxAmt(refundSuborderDO.getOrdAmt());
                accErrorDO.setOldCorgKey(refundSuborderDO.getChkKey());
                accErrorDO.setSplAbleFlg(CpiConstants.SPL_ABLE_FLG_NO);//不允许补单

                //业务类型为 05-退款(快捷退款、网银退款、汇款退款)，对账状态为 2-我方有银行无，则差错类型为 3-长款差错
                txAmt = accErrorDO.getMyTxAmt();
                longAmt = longAmt.add(txAmt);
                longCnt++;

                //插入差错信息表
                accErrorDO.setChkErrTyp(CpiConstants.CHK_ERR_TYP_LONG);
                accErrorDao.insert(accErrorDO);

                //通知对账模块
                //通知对账模块
                ErrorDTO errorDTO = new ErrorDTO();
                errorDTO.setOppoTxAmt(txAmt);
                BeanUtils.copyProperties(errorDTO,accErrorDO);
                this.notifyChkModuleInsertError(accControlDO,errorDTO);
            }
        }

        //3.添加差异记录(机构有我方无)
        if (CollectionUtils.isNotEmpty(orgExistList)) {
            for (Object baseCheckAccDO : orgExistList) {
                //获取银行对账明细表对应的DO类型
                Class clazz = baseCheckAccDO.getClass();

                //获取银行对账明细的对账外键
                Field checkKeyField = clazz.getDeclaredField(accRefundCfgDO.getCheckKeyFiled());
                String checkKey = ReflectionUtils.getField(checkKeyField,baseCheckAccDO).toString();

                //获取银行对账明细的交易金额
                Field checkAmtField = clazz.getDeclaredField(accRefundCfgDO.getCheckAmtFiled());
                BigDecimal checkAmt = new BigDecimal(ReflectionUtils.getField(checkAmtField,baseCheckAccDO).toString());

                //获取银行对账明细表对应的DO类的所有属性域
                AccErrorDO accErrorDO = new AccErrorDO();
                Field[] declaredFields = clazz.getDeclaredFields();
                for(Field field : declaredFields){
                    if ("corpBusTyp".equals(field.getName())) {
                        Object tmpCorpBustyp = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpCorpBustyp)){
                            accErrorDO.setCorpBusTyp(tmpCorpBustyp.toString());
                        }
                    } else if ("corpBusSubTyp".equals(field.getName())) {
                        Object tmpCorpBusSubtyp = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpCorpBusSubtyp)){
                            accErrorDO.setCorpBusSubTyp(tmpCorpBusSubtyp.toString());
                        }
                    } else if ("rutCorg".equals(field.getName())) {
                        Object tmpRutCorg = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpRutCorg)){
                            accErrorDO.setRutCorg(tmpRutCorg.toString());
                        }
                    }
                }
                accErrorDO.setChkErId(IdGenUtils.generateIdWithDateTime("CHK", 6));
                accErrorDO.setErrKeyId(checkKey);
                accErrorDO.setChkErrDt(chkDt);
                accErrorDO.setChkErrTm(chkTm);
                accErrorDO.setChkBatNo(chkBatNo);
                accErrorDO.setOthTxAmt(checkAmt);
                accErrorDO.setOldOrdNo(checkKey);
                accErrorDO.setOldCorgKey(checkKey);
                accErrorDO.setSplAbleFlg("N");//不允许补单

                //业务类型为 05-退款(快捷退款、网银退款、汇款退款)，对账状态为 3-银行有我方无，差错类型为 2-短款差错
                txAmt = accErrorDO.getOthTxAmt();
                shortAmt = shortAmt.add(txAmt);//短款差错总金额
                shortCnt++;//短款差错总笔数

                //插入差错信息表
                accErrorDO.setChkErrTyp(CpiConstants.CHK_ERR_TYP_SHORT);
                accErrorDao.insert(accErrorDO);

                //通知对账模块
                //通知对账模块
                ErrorDTO errorDTO = new ErrorDTO();
                errorDTO.setMainTxAmt(txAmt);
                BeanUtils.copyProperties(errorDTO,accErrorDO);
                this.notifyChkModuleInsertError(accControlDO,errorDTO);
            }
        }

        //4.添加差异记录(金额不相等)
        if (CollectionUtils.isNotEmpty(amtErrorList)) {
            for (Object baseCheckAccDO : amtErrorList) {
                //获取银行对账明细表对应的DO类型
                Class clazz = baseCheckAccDO.getClass();

                //获取银行对账明细的对账外键
                Field checkKeyField = clazz.getDeclaredField(accRefundCfgDO.getCheckKeyFiled());
                String checkKey = ReflectionUtils.getField(checkKeyField,baseCheckAccDO).toString();

                //获取银行对账明细的交易金额
                Field checkAmtField = clazz.getDeclaredField(accRefundCfgDO.getCheckAmtFiled());
                BigDecimal checkAmt = new BigDecimal(ReflectionUtils.getField(checkAmtField,baseCheckAccDO).toString());

                RefundSuborderDO myRefundSuborderDO = refundSuborderDao.selectRefundSuborderByChkKey(checkKey);

                //获取银行对账明细表对应的DO类的所有属性域
                AccErrorDO accErrorDO = new AccErrorDO();
                Field[] declaredFields = clazz.getDeclaredFields();
                for(Field field : declaredFields){
                    if ("corpBusTyp".equals(field.getName())) {
                        Object tmpCorpBustyp = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpCorpBustyp)){
                            accErrorDO.setCorpBusTyp(tmpCorpBustyp.toString());
                        }
                    } else if ("corpBusSubTyp".equals(field.getName())) {
                        Object tmpCorpBusSubtyp = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpCorpBusSubtyp)){
                            accErrorDO.setCorpBusSubTyp(tmpCorpBusSubtyp.toString());
                        }
                    } else if ("rutCorg".equals(field.getName())) {
                        Object tmpRutCorg = ReflectionUtils.getField(field, baseCheckAccDO);
                        if(JudgeUtils.isNotNull(tmpRutCorg)){
                            accErrorDO.setRutCorg(tmpRutCorg.toString());
                        }
                    }
                }
                accErrorDO.setChkErId(IdGenUtils.generateIdWithDateTime("CHK", 6));
                accErrorDO.setErrKeyId(checkKey);
                accErrorDO.setChkErrDt(chkDt);
                accErrorDO.setChkErrTm(chkTm);
                accErrorDO.setChkBatNo(chkBatNo);
                accErrorDO.setOthTxAmt(checkAmt);
                accErrorDO.setOldOrdNo(checkKey);
                accErrorDO.setOldCorgKey(checkKey);
                accErrorDO.setSplAbleFlg("N");//不允许补单
                accErrorDO.setMyTxAmt(myRefundSuborderDO.getOrdAmt());

                //根据业务子类型，判断差错类型
                txAmt = accErrorDO.getOthTxAmt();
                errTotAmt = errTotAmt.add(txAmt);//金额不符
                errTotCnt++;//金额不符

                //插入差错信息表
                accErrorDO.setChkErrTyp(CpiConstants.CHK_ERR_TYP_AMTERR);
                accErrorDao.insert(accErrorDO);

                //通知对账模块
                ErrorDTO errorDTO = new ErrorDTO();
                errorDTO.setMainTxAmt(txAmt);
                errorDTO.setOppoTxAmt(myRefundSuborderDO.getOrdAmt());
                BeanUtils.copyProperties(errorDTO,accErrorDO);
                this.notifyChkModuleInsertError(accControlDO,errorDTO);
            }
        }

        //更新差错金额和笔数
        accControlDO.setShortAmt(shortAmt);
        accControlDO.setShortCnt(shortCnt);
        accControlDO.setLongAmt(longAmt);
        accControlDO.setLongCnt(longCnt);
        accControlDO.setErrTotAmt(errTotAmt);
        accControlDO.setErrTotCnt(errTotCnt);
    }

    /**
     * 退款(快捷退款、网银退款、汇款退款)业务对账成功，将对平金额汇总进行记账
     */
    @Override
    public boolean accountTreatmentRefund(AccControlDO accControlDO) {

        //对平总金额为0，则直接退出
        BigDecimal txAmt = accControlDO.getTotMchAmt();
        if (txAmt.compareTo(BigDecimal.ZERO) == 0) {
            return true;
        }

        //查询结算模式
        RefundParamDO refundParamDO = refundParamDao.selectByUniqueKey(accControlDO.getRutCorg(),accControlDO.getCorpBusTyp(),accControlDO.getCorpBusSubTyp());

        //调用账务处理
        GenericDTO<List<AccountingReqDTO>> genericDTO = new GenericDTO<>();
        List<AccountingReqDTO> list = new ArrayList<>();
        AccountingReqDTO accReqInf1 = null;
        AccountingReqDTO accReqInf2 = null;
        String rutCorpOrg = accControlDO.getRutCorg();
        String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPI", 8);

        //借：应付账款-渠道退款-XX银行
        accReqInf1 = AccountUtils.getAccountingReqDTO("N","I","","1",AccountUtils.getPayItemNo1(rutCorpOrg),CpiConstants.TX_TYP_REFUNDS,
                accControlDO.getTotMchAmt(),"D",txJrnNo, accControlDO.getChkBatNo(),"","","","","");

        if(CpiConstants.IO_FLG_IN_OUT.equals(refundParamDO.getIoFlg())){
            //贷：银行存款-备付金账户-XX银行
            accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1",AccountUtils.getBankDepositItemNo1(rutCorpOrg),CpiConstants.TX_TYP_REFUNDS,
                    accControlDO.getTotMchAmt(),"C",txJrnNo, accControlDO.getChkBatNo(),"","","","","");

        }else {
            //贷：应收账款-待结算款-XX银行
            accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1",AccountUtils.getReceiveItemNo1(rutCorpOrg),CpiConstants.TX_TYP_REFUNDS,
                    accControlDO.getTotMchAmt(),"C",txJrnNo, accControlDO.getChkBatNo(),"","","","","");

        }

        list.add(accReqInf1);
        list.add(accReqInf2);
        genericDTO.setBody(list);
        GenericRspDTO<NoBody> rspDTO = accTreatClient.accountingTreatment(genericDTO);
        //判断账务处理是否成功，不成功退出
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            logger.error("机构: " + accControlDO.getRutCorg() + "; 业务: " + accControlDO.getCorpBusTyp() + "; 子业务: "
                    + accControlDO.getCorpBusSubTyp() + "; 对账批次号: " + accControlDO.getChkBatNo() + ";对账状态"
                    +accControlDO.getChkFilSts()+" 做账失败");
            return false;
        }else {
            return true;
        }
    }

    /**
     * 通知对账模块同步差错记录
     * @param accControlDO
     * @param errorDTO
     * @return
     */
    private void notifyChkModuleInsertError(AccControlDO accControlDO, ErrorDTO errorDTO){
        //通知修改结果
        GenericDTO<ErrorDTO> genericDTO = new GenericDTO<>();
        errorDTO.setChkFilDt(accControlDO.getChkFilDt());
        errorDTO.setChkBusTyp(ChkConstants.REFUND);
        errorDTO.setMainNo(accControlDO.getRutCorg());
        errorDTO.setOppoNo("CPI");
        //根据路由，选择对应的对账业务类型
        if(CpiConstants.ICBC.equals(accControlDO.getRutCorg())) {
            errorDTO.setChkBusSubTyp(ChkConstants.REFUND_ICBC_CPI);
        }
        else if (CpiConstants.BESTPAY.equals(accControlDO.getRutCorg())) {
            errorDTO.setChkBusSubTyp(ChkConstants.REFUND_BESTPAY_CPI);
        }
        else if (CpiConstants.WECHAT.equals(accControlDO.getRutCorg())) {
            errorDTO.setChkBusSubTyp(ChkConstants.REFUND_WECHAT_CPI);
        }else if(CpiConstants.ALIPAY.equals(accControlDO.getRutCorg())){
            errorDTO.setChkBusSubTyp(ChkConstants.REFUND_ALIPAY_CPI);
        }
        else {
            logger.error("机构: " + accControlDO.getRutCorg() + "; 业务: " + accControlDO.getCorpBusTyp() + "; 子业务: "
                    + accControlDO.getCorpBusSubTyp() + "; 对账批次号: " + accControlDO.getChkBatNo() + ";对账状态"
                    +accControlDO.getChkFilSts()+" 对账模块不支持该银行");
        }
        genericDTO.setBody(errorDTO);
        try {
            GenericRspDTO<NoBody> rspGeneric = chkClient.insertError(genericDTO);
            if (JudgeUtils.isNotSuccess(rspGeneric.getMsgCd())) {
                logger.error("机构: " + accControlDO.getRutCorg() + "; 业务: " + accControlDO.getCorpBusTyp() + "; 子业务: "
                        + accControlDO.getCorpBusSubTyp() + "; 对账批次号: " + accControlDO.getChkBatNo() + ";对账状态"
                        + accControlDO.getChkFilSts() + " 对账模块同步差错出错");
            }
        } catch (Exception e) {
            logger.error("机构: " + accControlDO.getRutCorg() + "; 业务: " + accControlDO.getCorpBusTyp() + "; 子业务: "
                    + accControlDO.getCorpBusSubTyp() + "; 对账批次号: " + accControlDO.getChkBatNo() + ";对账状态"
                    + accControlDO.getChkFilSts() + " 对账模块同步差错异常：", e);
        }
    }
}
