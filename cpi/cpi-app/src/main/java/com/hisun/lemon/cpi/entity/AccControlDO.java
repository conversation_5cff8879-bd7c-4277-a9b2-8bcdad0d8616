package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 资金流入模块对账总控表
 * 表名：cpi_acc_control
 */
public class AccControlDO extends BaseDO {
    /**
     * chkBatNo 批次号
     */
    private String chkBatNo;
    /**
     * rutCorg 路径合作机构
     */
    private String rutCorg;
    /**
     * corpBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * corpBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * chkFilDt 对账文件日期
     */
    private LocalDate chkFilDt;
    /**
     * chkFilNm 对账文件名
     */
    private String chkFilNm;
    /**
     * chkFilSts 对账状态,0未对账，1文件已取，2文件入库，3已对账，4对账完成
     */
    private String chkFilSts;
    /**
     * fileRcvDt 获取对账文件日期
     */
    private LocalDate fileRcvDt;
    /**
     * ownChkFlg 本地对账标识，Y本模块对，N单独对账模块对
     */
    private String ownChkFlg;
    /**
     * chkBegTm 对账开始时间
     */
    private LocalTime chkBegTm;
    /**
     * chkEndTm 对账结束时间
     */
    private LocalTime chkEndTm;
    /**
     * chkDt 对账日期
     */
    private LocalDate chkDt;
    /**
     * filTotAmt 对账总金额
     */
    private BigDecimal filTotAmt;
    /**
     * filTotCnt 对账总笔数
     */
    private Integer filTotCnt;
    /**
     * totMchAmt 对平总金额
     */
    private BigDecimal totMchAmt;
    /**
     * totMchCnt 对平总笔数
     */
    private Integer totMchCnt;
    /**
     * errTotCnt 差错笔数
     */
    private Integer errTotCnt;
    /**
     * errTotAmt 差错金额
     */
    private BigDecimal errTotAmt;
    /**
     * longAmt 长款金额
     */
    private BigDecimal longAmt;
    /**
     * longCnt 长款笔数
     */
    private Integer longCnt;
    /**
     * shortAmt 短款金额
     */
    private BigDecimal shortAmt;
    /**
     * shortCnt 短款笔数
     */
    private Integer shortCnt;
    /**
     * doubtAmt 存疑金额
     */
    private BigDecimal doubtAmt;
    /**
     * doubtCnt 存疑笔数
     */
    private Integer doubtCnt;
    /**
     * dbtErrAmt 存疑转差错金额
     */
    private BigDecimal dbtErrAmt;
    /**
     * dbtErrCnt 存疑转差错笔数
     */
    private Integer dbtErrCnt;
    /**
     * totDrAmt 借方总金额
     */
    private BigDecimal totDrAmt;
    /**
     * totDrNum 借方总笔数
     */
    private Integer totDrNum;
    /**
     * totCrAmt 贷方总金额
     */
    private BigDecimal totCrAmt;
    /**
     * totCrNum 贷方总笔数
     */
    private Integer totCrNum;
    /**
     * payTotAmt 应付总金额
     */
    private BigDecimal payTotAmt;
    /**
     * rcvTotAmt 应收总金额
     */
    private BigDecimal rcvTotAmt;
    /**
     * rmk 备注
     */
    private String rmk;
    /**
     * tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public LocalDate getChkFilDt() {
        return chkFilDt;
    }

    public void setChkFilDt(LocalDate chkFilDt) {
        this.chkFilDt = chkFilDt;
    }

    public String getChkFilNm() {
        return chkFilNm;
    }

    public void setChkFilNm(String chkFilNm) {
        this.chkFilNm = chkFilNm;
    }

    public String getChkFilSts() {
        return chkFilSts;
    }

    public void setChkFilSts(String chkFilSts) {
        this.chkFilSts = chkFilSts;
    }

    public LocalDate getFileRcvDt() {
        return fileRcvDt;
    }

    public void setFileRcvDt(LocalDate fileRcvDt) {
        this.fileRcvDt = fileRcvDt;
    }

    public String getOwnChkFlg() {
        return ownChkFlg;
    }

    public void setOwnChkFlg(String ownChkFlg) {
        this.ownChkFlg = ownChkFlg;
    }

    public LocalTime getChkBegTm() {
        return chkBegTm;
    }

    public void setChkBegTm(LocalTime chkBegTm) {
        this.chkBegTm = chkBegTm;
    }

    public LocalTime getChkEndTm() {
        return chkEndTm;
    }

    public void setChkEndTm(LocalTime chkEndTm) {
        this.chkEndTm = chkEndTm;
    }

    public LocalDate getChkDt() {
        return chkDt;
    }

    public void setChkDt(LocalDate chkDt) {
        this.chkDt = chkDt;
    }

    public BigDecimal getFilTotAmt() {
        return filTotAmt;
    }

    public void setFilTotAmt(BigDecimal filTotAmt) {
        this.filTotAmt = filTotAmt;
    }

    public Integer getFilTotCnt() {
        return filTotCnt;
    }

    public void setFilTotCnt(Integer filTotCnt) {
        this.filTotCnt = filTotCnt;
    }

    public BigDecimal getTotMchAmt() {
        return totMchAmt;
    }

    public void setTotMchAmt(BigDecimal totMchAmt) {
        this.totMchAmt = totMchAmt;
    }

    public Integer getTotMchCnt() {
        return totMchCnt;
    }

    public void setTotMchCnt(Integer totMchCnt) {
        this.totMchCnt = totMchCnt;
    }

    public Integer getErrTotCnt() {
        return errTotCnt;
    }

    public void setErrTotCnt(Integer errTotCnt) {
        this.errTotCnt = errTotCnt;
    }

    public BigDecimal getErrTotAmt() {
        return errTotAmt;
    }

    public void setErrTotAmt(BigDecimal errTotAmt) {
        this.errTotAmt = errTotAmt;
    }

    public BigDecimal getLongAmt() {
        return longAmt;
    }

    public void setLongAmt(BigDecimal longAmt) {
        this.longAmt = longAmt;
    }

    public Integer getLongCnt() {
        return longCnt;
    }

    public void setLongCnt(Integer longCnt) {
        this.longCnt = longCnt;
    }

    public BigDecimal getShortAmt() {
        return shortAmt;
    }

    public void setShortAmt(BigDecimal shortAmt) {
        this.shortAmt = shortAmt;
    }

    public Integer getShortCnt() {
        return shortCnt;
    }

    public void setShortCnt(Integer shortCnt) {
        this.shortCnt = shortCnt;
    }

    public BigDecimal getDoubtAmt() {
        return doubtAmt;
    }

    public void setDoubtAmt(BigDecimal doubtAmt) {
        this.doubtAmt = doubtAmt;
    }

    public Integer getDoubtCnt() {
        return doubtCnt;
    }

    public void setDoubtCnt(Integer doubtCnt) {
        this.doubtCnt = doubtCnt;
    }

    public BigDecimal getDbtErrAmt() {
        return dbtErrAmt;
    }

    public void setDbtErrAmt(BigDecimal dbtErrAmt) {
        this.dbtErrAmt = dbtErrAmt;
    }

    public Integer getDbtErrCnt() {
        return dbtErrCnt;
    }

    public void setDbtErrCnt(Integer dbtErrCnt) {
        this.dbtErrCnt = dbtErrCnt;
    }

    public BigDecimal getTotDrAmt() {
        return totDrAmt;
    }

    public void setTotDrAmt(BigDecimal totDrAmt) {
        this.totDrAmt = totDrAmt;
    }

    public Integer getTotDrNum() {
        return totDrNum;
    }

    public void setTotDrNum(Integer totDrNum) {
        this.totDrNum = totDrNum;
    }

    public BigDecimal getTotCrAmt() {
        return totCrAmt;
    }

    public void setTotCrAmt(BigDecimal totCrAmt) {
        this.totCrAmt = totCrAmt;
    }

    public Integer getTotCrNum() {
        return totCrNum;
    }

    public void setTotCrNum(Integer totCrNum) {
        this.totCrNum = totCrNum;
    }

    public BigDecimal getPayTotAmt() {
        return payTotAmt;
    }

    public void setPayTotAmt(BigDecimal payTotAmt) {
        this.payTotAmt = payTotAmt;
    }

    public BigDecimal getRcvTotAmt() {
        return rcvTotAmt;
    }

    public void setRcvTotAmt(BigDecimal rcvTotAmt) {
        this.rcvTotAmt = rcvTotAmt;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}