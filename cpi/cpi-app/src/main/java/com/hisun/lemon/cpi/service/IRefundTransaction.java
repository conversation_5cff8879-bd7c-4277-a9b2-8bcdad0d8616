package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.entity.RefundOrderDO;
import com.hisun.lemon.cpi.entity.RefundSuborderDO;

/**
 * 根据银行处理结果，更新退款订单状态
 */
public interface IRefundTransaction {

    /**
     * 更新退款订单状态
     */
    void updateOrdSts(RefundOrderDO refundOrderDO, String subOrdNo, int failCount, BaseBnkRspBO baseBnkRspBO);

    /**
     * 更新退款订单子订单的轮询状态
     */
    void updateRefundOrder(RefundSuborderDO refundSuborderDO);
}
