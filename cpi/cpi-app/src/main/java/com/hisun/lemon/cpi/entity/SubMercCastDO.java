package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;

/**
 * 平台商户号与微信子账户号映射表
 */
public class SubMercCastDO extends BaseDO {
    /**
     * 公众号ID，平台企业号id
     */
    private String appId;

    /**
     * 微信为平台分配的商户号
     */
    private String mchId;

    /**
     * 平台商户号
     */
    private String userId;

    /**
     * 微信子商户号
     */
    private String subMchId;

    /**
     * 生效标志：0-失效；1-生效
     */
    private String effFlg;

    /**
     * 商户名称
     */
    private String mercNm;

    /**
     * 商户简称
     */
    private String mercSnm;

    /**
     * 商户电话
     */
    private String officeTel;

    /**
     * 商户备注
     */
    private String mercRmk;

    /**
     * 商户网址
     */
    private String mercWebsite;

    /**
     * 联系人姓名
     */
    private String contractNm;

    /**
     * 联系人电话
     */
    private String contractTel;

    /**
     * 联系人邮箱
     */
    private String contractWebsite;

    /**
     * 简介
     */
    private String mercIntroduction;

    /**
     * 业务类别
     */
    private String businessType;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSubMchId() {
        return subMchId;
    }

    public void setSubMchId(String subMchId) {
        this.subMchId = subMchId;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getMercNm() {
        return mercNm;
    }

    public void setMercNm(String mercNm) {
        this.mercNm = mercNm;
    }

    public String getMercSnm() {
        return mercSnm;
    }

    public void setMercSnm(String mercSnm) {
        this.mercSnm = mercSnm;
    }

    public String getOfficeTel() {
        return officeTel;
    }

    public void setOfficeTel(String officeTel) {
        this.officeTel = officeTel;
    }

    public String getMercRmk() {
        return mercRmk;
    }

    public void setMercRmk(String mercRmk) {
        this.mercRmk = mercRmk;
    }

    public String getMercWebsite() {
        return mercWebsite;
    }

    public void setMercWebsite(String mercWebsite) {
        this.mercWebsite = mercWebsite;
    }

    public String getContractNm() {
        return contractNm;
    }

    public void setContractNm(String contractNm) {
        this.contractNm = contractNm;
    }

    public String getContractTel() {
        return contractTel;
    }

    public void setContractTel(String contractTel) {
        this.contractTel = contractTel;
    }

    public String getContractWebsite() {
        return contractWebsite;
    }

    public void setContractWebsite(String contractWebsite) {
        this.contractWebsite = contractWebsite;
    }

    public String getMercIntroduction() {
        return mercIntroduction;
    }

    public void setMercIntroduction(String mercIntroduction) {
        this.mercIntroduction = mercIntroduction;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}
