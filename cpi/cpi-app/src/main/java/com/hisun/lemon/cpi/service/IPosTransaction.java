package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.PosPayReqDTO;
import com.hisun.lemon.cpi.entity.PosOrderDO;
import com.hisun.lemon.cpi.entity.PosPreAuJrnDO;
import com.hisun.lemon.cpi.entity.RouteDO;

/**
 * <AUTHOR>
 * @Description
 * @date 2017/10/16 19:25
 */
public interface IPosTransaction {

    /**
     * 登记网银订单和网银订单流水
     * @param posPayReqDTO pos支付请求对象
     * @param rspRouteInfo 路由信息
     * @param fndOrdNo 订单号
     * @return
     */
    PosOrderDO createPosOrder(PosPayReqDTO posPayReqDTO, RouteDO rspRouteInfo, String fndOrdNo);

    /**
     * 插入预授权流水
     * @param posPreAuJrnDO 预授权流水信息
     */
    void insertCardProtJrnDO(PosPreAuJrnDO posPreAuJrnDO);
}
