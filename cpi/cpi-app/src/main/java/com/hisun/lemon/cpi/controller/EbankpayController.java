package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.service.IEbankpayService;
import com.hisun.lemon.cpi.service.IFundPollParamService;
import com.hisun.lemon.cpi.service.ISettlementService;
import com.hisun.lemon.cpi.thread.EbankFundQueryThread;
import com.hisun.lemon.cpi.thread.EbankRefundQueryThread;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.lock.DistributedLocker;
import io.swagger.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by Rui on 2017/7/5.
 */
@RestController
@RequestMapping("/cpi/ebank")
@Api(tags="EbankpayController", description="网银（支付宝，微信）服务")
public class EbankpayController extends BaseController {
    @Resource
    private IEbankpayService ebankpayService;
    @Resource
    private ISettlementService settlementService;
    @Resource
    private IFundPollParamService fundPollParamService;
    @Resource
    private DistributedLocker distributedLocker;

    @Resource
    private TaskExecutor taskExecutor;

    @ApiOperation(value="网银支付(支付宝，微信)", notes="网银支付(支付宝，微信)")
    @ApiResponse(code = 200, message = "付款结果")
    @PostMapping("/order")
    public GenericRspDTO<EbankpayRspDTO> ebankpay(@Validated @RequestBody GenericDTO<EbankpayReqDTO> genericDTO) {
        return ebankpayService.ebankpay(genericDTO);
    }

    @ApiOperation(value="支付结果查询", notes="支付结果查询")
    @ApiResponse(code = 200, message = "汇款结果")
    @GetMapping("/result")
    public GenericRspDTO<OrderResultRspDTO> orderQuery(@Validated @ApiParam(name = "ordNo", value = "充值订单号", required = true) @RequestParam(value = "ordNo") String ordNo) {
        return ebankpayService.queryOrder(ordNo);
    }

    @ApiOperation(value="微信子商户申请", notes="微信子商户申请")
    @ApiResponse(code = 200, message = "微信子商户结果")
    @PostMapping("/submerc")
    public GenericRspDTO<SubMercRspDTO> applySubMerc(@Validated @RequestBody GenericDTO<SubMercReqDTO> genericDTO) {
        return ebankpayService.applySubMerc(genericDTO);
    }

    @ApiOperation(value="微信页面授权", notes="微信页面授权")
    @ApiResponse(code = 200, message = "微信页面授权结果")
    @PostMapping("/token")
    public GenericRspDTO<WeChatTokenRsp> applyToken(@Validated @RequestBody GenericDTO<WeChatTokenReq> genericDTO) {
        return ebankpayService.applyToken(genericDTO);
    }

    @ApiOperation(value="撤单(支付宝，微信)", notes="撤单(支付宝，微信)")
    @ApiResponse(code = 200, message = "撤单结果")
    @PostMapping("/closeOrder")
    public GenericRspDTO<CloseOrderRspDTO> closeOrder(@Validated @RequestBody GenericDTO<CloseOrderReqDTO> genericDTO) {
        return ebankpayService.closeOrder(genericDTO);
    }

    @ApiOperation(value="平台在网银机构的结算明细查询", notes="平台在网银机构的结算明细查询")
    @ApiResponse(code = 200, message = "平台在网银机构的结算明细查询结果")
    @PostMapping("/settlement")
    public GenericRspDTO<NoBody> settlementQuery(@Validated @ApiParam(name = "stlFlg", value = "结算类型 1-已结算；2-未结算", required = true) @RequestParam(value = "stlFlg") int stlFlg,
                                                 @Validated @ApiParam(name = "rutCorg", value = "路由机构 WeChat-微信 等", required = true) @RequestParam(value = "rutCorg") String rutCorg,
                                                 @ApiParam(name = "startDate", value = "查询开始日期", required = false) @RequestParam(value = "startDate", required = false) String startDate,
                                                 @ApiParam(name = "endDate", value = "查询结束日期", required = false) @RequestParam(value = "endDate", required = false) String endDate) {
        return settlementService.settlementQuery(stlFlg, rutCorg, startDate, endDate);
    }

    @ApiOperation(value="查询网银支付订单银行处理结果", notes="查询网银支付订单银行处理结果")
    @ApiResponse(code = 200, message = "查询网银支付订单银行处理结果")
    @PostMapping("/additionalOrder")
    public void additionalOrder() {
        List<FundPollParamDO> list = fundPollParamService.queryAllFund();
        if(CollectionUtils.isNotEmpty(list)) {
            for (FundPollParamDO fundPollParamDO : list) {
                //异步调起银行查询接口
                taskExecutor.execute(new EbankFundQueryThread(ebankpayService,distributedLocker,fundPollParamDO));
            }
        }
    }

    @ApiOperation(value="微信子商户列表查询", notes="微信子商户列表查询")
    @ApiResponse(code = 200, message = "微信子商户列表查询结果")
    @PostMapping("/submerclist")
    public GenericRspDTO<SubMercListRspDTO> getSubMercList(@Validated @RequestBody GenericDTO<SubMercListReqDTO> genericDTO) {
        return GenericRspDTO.newSuccessInstance(ebankpayService.getSubMercList(genericDTO.getBody()));
    }

    @ApiOperation(value="微信子商户解除绑定", notes="微信子商户解除绑定")
    @ApiResponse(code = 200, message = "微信子商户解除绑定结果")
    @PostMapping("/delete")
    public GenericRspDTO unBundingSubMerc(@Validated @RequestBody GenericDTO<UnbundingSubMercReqDTO> genericDTO) {
        return ebankpayService.unbundingSubMerc(genericDTO.getBody());
    }

    @ApiOperation(value="支付宝二级商户列表查询", notes="支付宝二级商户列表查询")
    @ApiResponse(code = 200, message = "支付宝二级商户列表查询结果")
    @PostMapping("/secmerclist")
    public GenericRspDTO<SecMercListRspDTO> getSecMercList(@Validated @RequestBody GenericDTO<SecMercListReqDTO> genericDTO) {
        return GenericRspDTO.newSuccessInstance(ebankpayService.getSecMercList(genericDTO.getBody()));
    }

    @ApiOperation(value="添加支付宝二级商户", notes="添加支付宝二级商户")
    @ApiResponse(code = 200, message = "添加支付宝二级商户结果")
    @PostMapping("/secmerc")
    public GenericRspDTO addSecMerc(@Validated @RequestBody GenericDTO<SecMercReqDTO> genericDTO) {
        return ebankpayService.addSecMerc(genericDTO);
    }
}
