package com.hisun.lemon.cpi.bnkapi.common;

import java.util.List;

import javax.annotation.Resource;

import com.hisun.lemon.cpi.bnkapi.alipay.AlipayUtil;
import com.hisun.lemon.cpi.alipay.ebankpay.AliPayEnumCommon;
import com.hisun.lemon.cpi.alipay.ebankpay.rsp.AliRsp;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.hisun.channel.connector.http.AbstractHttpConnector;
import com.hisun.channel.data.Request;
import com.hisun.channel.data.Response;
import com.hisun.channel.service.exception.ErrorCode;


/**
 * SEATEL通道调用
 * 
 * <AUTHOR>
 * @date 2017年12月15日
 *
 */
@Component
public class SeatelChannelClient {
	protected static final Logger logger = LoggerFactory.getLogger(AbstractHttpConnector.class);

	@Resource
	private SeatelHttpSyncConnector seatelHttpSyncConnector;

	protected Response doRequest(Request request) {
		Response response = new Response();
		response.setRequestId(request.getRequestId());
		if (!validate(request, response)) {
			return response;
		}

		// 确定请求地址和返回实体
		AliRsp dto = null;
		Object object = request.getTarget();
		try {
			// 签名并添加到请求参数
			List<NameValuePair> params = AlipayUtil.pubSign(object);
			logger.info("SeatelChannelClient.doRequest parmStr is:" + params.toString());
			switch (AliPayEnumCommon.EnumSource.valueOf(request.getSource())) {
			case mercPlaceOrder:
				dto = seatelHttpSyncConnector.httpHandle(params, object, AliRsp.class);
				break;
			case orderQuery:
				dto = seatelHttpSyncConnector.httpHandle(params, object, AliRsp.class);
				break;				
			default:
				dto = seatelHttpSyncConnector.httpHandle(params, object, AliRsp.class);
				break;
			}
			if (dto != null) {
				response.setObject(dto);
				response.setMsgInfo(ErrorCode.SUCCESS.getMsgInfo());
				response.setMsgCode(ErrorCode.SUCCESS.getMsgCode());
			} else {
				response.setObject(null);
				response.setMsgInfo(ErrorCode.RECEIVE_MSG_IS_NULL.getMsgInfo());
				response.setMsgCode(ErrorCode.RECEIVE_MSG_IS_NULL.getMsgCode());
			}
		} catch (Exception e) {
			// TODO: handle exception
            logger.error("SeatelChannelClient.doRequest() Exception : ", e);
		}

		return response;

	}

	public Response request(Request request) {
		if (request.getRequestId() == null || request.getRequestId().length() == 0) {
			throw new IllegalArgumentException("Request id can not be null.");
		}
		return doRequest(request);
	}

	/**
	 * 输入参数验证
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	private boolean validate(Request request, Response response) {
		// requestId为空
		if (StringUtils.isBlank(request.getRequestId())) {
			response.setMsgCode(ErrorCode.REQUEST_ID_IS_NULL.getMsgCode());
			response.setMsgInfo(ErrorCode.REQUEST_ID_IS_NULL.getMsgInfo());
			return false;
		}
		// 请求业务类型为空
		if (StringUtils.isBlank(request.getBusiType())) {
			response.setMsgCode(ErrorCode.BUSI_TYPE_IS_NULL.getMsgCode());
			response.setMsgInfo(ErrorCode.BUSI_TYPE_IS_NULL.getMsgInfo());
			return false;
		}
		// 请求来源为空
		if (StringUtils.isBlank(request.getSource())) {
			response.setMsgCode(ErrorCode.SOURCE_IS_NULL.getMsgCode());
			response.setMsgInfo(ErrorCode.SOURCE_IS_NULL.getMsgInfo());
			return false;
		}
		// 请求数据为空
		if (null == request.getTarget()) {
			response.setMsgCode(ErrorCode.REQUEST_DATA_IS_NULL.getMsgCode());
			response.setMsgInfo(ErrorCode.REQUEST_DATA_IS_NULL.getMsgInfo());
			return false;
		}
		return true;
	}
}
