package com.hisun.lemon.cpi.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.entity.FundOrderDO;
import com.hisun.lemon.cpi.schedule.CheckBatRegisterSchedule;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.dto.order.NotifyResultDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.stream.MultiOutput;
import com.hisun.lemon.framework.stream.producer.Producer;
import com.hisun.lemon.framework.stream.producer.Producers;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * User : Rui
 * Date : 2017/8/28
 * Time : 11:31
 **/
@Component
public class FundNotifyProduce {

    private static final Logger logger = LoggerFactory.getLogger(FundNotifyProduce.class);

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 汇款充值订单结果异步通知
     */
    @Producers({
        @Producer(beanName="noticeHandler", channelName= MultiOutput.OUTPUT_DEFAULT)
    })
    public NotifyResultDTO fundOrderNotify(FundOrderDO fundOrderDO,String rmk) {

        //充值订单信息
        NotifyResultDTO notifyResultDTO = new NotifyResultDTO();
        notifyResultDTO.setCshOrderNo(fundOrderDO.getReqOrdNo());
        notifyResultDTO.setFndOrderNo(fundOrderDO.getFudOrdNo());
        notifyResultDTO.setCrdPayAmt(fundOrderDO.getOrdAmt());
        notifyResultDTO.setRemark(rmk);

        String ordSts = fundOrderDO.getOrdSts();
        if(CpiConstants.ORD_FAIL.equals(ordSts)){
            notifyResultDTO.setOrderStatus(CshConstants.JRN_STS_F);
        } else {
            notifyResultDTO.setOrderStatus(CshConstants.JRN_STS_S);
        }

        String data = ObjectMapperHelper.writeValueAsString(objectMapper, notifyResultDTO, true);
        logger.info("===========MQ通知收银台对象{ "+data+" }==========================");
        return notifyResultDTO;
    }
}
