/*
 * @ClassName IShortcutOrderDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-17 10:48:21
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.ShortcutOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Mapper
public interface IShortcutOrderDao extends BaseDao<ShortcutOrderDO> {

    /**
     * 根据内部订单号，查询快捷订单信息
     */
    ShortcutOrderDO selectByFndOrdNo(String fndOrdNo);

    /**
     * 根据对账键值，查询快捷订单信息
     */
    ShortcutOrderDO selectShortcutOrderByChkKey(String chkKey);

    /**
     * 根据对账键值，更新快捷订单的对账状态、对账时间等
     */
    void updateShortcutOrderByChkKey(ShortcutOrderDO shortcutOrderDO);

    /**
     * 查询我方ORD_STS='S1'(交易成功)、CHK_STS='5'(存疑)的订单，(ORD_DT)订单日期小于对账日期
     */
    List<ShortcutOrderDO> getShortcutOrderListByChkStsDoubt(@Param("ordSts")String ordSts, @Param("chkSts")String chkSts, @Param("chkDt")LocalDate chkDt);

    /**
     * 将我方ORD_STS='S1'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'，(ORD_DT)订单日期小于对账日期
     */
    void updateShortcutOrderByChkStsDoubt(@Param("ordSts")String ordSts, @Param("oldChkSts")String oldChkSts, @Param("newChkSts")String newChkSts, @Param("chkDt")LocalDate chkDt, @Param("chkTm")LocalTime chkTm);

    /**
     * 查询我方ORD_STS='S'、CHK_STS='0'(未对账)的订单，订单日期等于对账日期
     */
    List<ShortcutOrderDO> getShortcutOrderListByChkStsNotstart(@Param("ordSts")String ordSts, @Param("chkSts")String chkSts, @Param("chkDt")LocalDate chkDt);

    /**
     * 将我方ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)，订单日期等于对账日期
     */
    void updateShortcutOrderByChkStsNotstart(@Param("ordSts")String ordSts, @Param("oldChkSts")String oldChkSts, @Param("newChkSts")String newChkSts, @Param("chkDt")LocalDate chkDt, @Param("chkTm")LocalTime chkTm);

    /**
     * 根据机构将我方ORD_STS='S'(交易成功)、订单日期等于对账日期的数据查询出来
     */
    List<ShortcutOrderDO> getShotcutOrderListByChkFilDt(@Param("rutCorpOrg")String rutCorpOrg, @Param("chkFilDt")LocalDate chkFilDt, @Param("beginNum")Integer beginNum, @Param("countNum")Integer countNum);

}