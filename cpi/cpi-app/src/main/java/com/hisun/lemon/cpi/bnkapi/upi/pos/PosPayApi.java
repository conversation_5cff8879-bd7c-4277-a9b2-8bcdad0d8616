package com.hisun.lemon.cpi.bnkapi.upi.pos;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.entity.PosOrderDO;
import com.hisun.lemon.cpi.entity.PosPreAuJrnDO;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @date 2017/10/17 16:09
 */
@Component
public class PosPayApi {

    /**
     * pos支付
     * @param posOrderDO
     * @return
     */
    public BaseBnkRspBO posPay(PosOrderDO posOrderDO){
        //模拟码
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
        baseBnkRspBO.setOrgRspCd("0");
        baseBnkRspBO.setOrgRspMsg("成功");

        return baseBnkRspBO;
    }

    /**
     * 预授权交易
     * @param posPreAuJrnDO
     * @return
     */
    public BaseBnkRspBO preAuthorizationPay(PosPreAuJrnDO posPreAuJrnDO){
        //模拟码
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
        baseBnkRspBO.setOrgRspCd("0");
        baseBnkRspBO.setOrgRspMsg("成功");

        return baseBnkRspBO;
    }

    /**
     * 预授权撤销交易
     * @param posPreAuJrnDO
     * @return
     */
    public BaseBnkRspBO preAuthorizationCancel(PosPreAuJrnDO posPreAuJrnDO,String AuCode){
        //模拟码
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
        baseBnkRspBO.setOrgRspCd("0");
        baseBnkRspBO.setOrgRspMsg("成功");

        return baseBnkRspBO;
    }

    /**
     * 预授权完成交易
     * @param posPreAuJrnDO
     * @return
     */
    public BaseBnkRspBO preAuthorizationFinish(PosPreAuJrnDO posPreAuJrnDO,String AuCode){
        //模拟码
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
        baseBnkRspBO.setOrgRspCd("0");
        baseBnkRspBO.setOrgRspMsg("成功");

        return baseBnkRspBO;
    }

    /**
     * 预授权撤销完成交易
     * @param posPreAuJrnDO
     * @return
     */
    public BaseBnkRspBO preAuthorizationFinishCancel(PosPreAuJrnDO posPreAuJrnDO,String AuCode){
        //模拟码
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
        baseBnkRspBO.setOrgRspCd("0");
        baseBnkRspBO.setOrgRspMsg("成功");

        return baseBnkRspBO;
    }
}
