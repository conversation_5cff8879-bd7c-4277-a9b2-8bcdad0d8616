package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.IEbankOrderDao;
import com.hisun.lemon.cpi.dao.IFundOrderDao;
import com.hisun.lemon.cpi.dto.EbankpayReqDTO;
import com.hisun.lemon.cpi.entity.EbankOrderDO;
import com.hisun.lemon.cpi.entity.FundOrderDO;
import com.hisun.lemon.cpi.entity.RouteDO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.mq.FundNotifyProduce;
import com.hisun.lemon.cpi.service.IEbankpayTransaction;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Created by Rui on 2017/8/1.
 */
@Service
public class EbankpayTransactionImpl extends BaseService implements IEbankpayTransaction {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(EbankpayTransactionImpl.class);
    @Resource
    IFundOrderDao fundOrderDao;
    @Resource
    IEbankOrderDao ebankOrderDao;
    @Resource
    private FundNotifyProduce fundNotifyProduce;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public EbankOrderDO createEbankOrder(EbankpayReqDTO ebankpayReqDTO, RouteDO rspRouteInfo,String funOrdNo) {
        String userId = null;
        if(JudgeUtils.isNotEmpty(LemonUtils.getUserId())) {
            userId = LemonUtils.getUserId();
            logger.info(ebankpayReqDTO +"：LemonUtils.getUserId:>> " + userId);
        } else {
            userId = ebankpayReqDTO.getUserNo();
            boolean flag = JudgeUtils.isBlank(userId)
                    && JudgeUtils.equals(CorpBusSubTyp.EBANKPAY_USERSCAN.getType(),ebankpayReqDTO.getCorpBusSubTyp().getType());
            //用户扫码设置为商户收款用户号
            if(flag){
                userId = ebankpayReqDTO.getMerchantId();
            }
        }

        //创建充值订单
        FundOrderDO fundOrderDO = new FundOrderDO();
        BeanUtils.copyProperties(fundOrderDO, ebankpayReqDTO);
        BeanUtils.copyProperties(fundOrderDO, rspRouteInfo);
        fundOrderDO.setUserId(userId);
        fundOrderDO.setCorpBusTyp(ebankpayReqDTO.getCorpBusTyp().getType());
        fundOrderDO.setCorpBusSubTyp(ebankpayReqDTO.getCorpBusSubTyp().getType());
        fundOrderDO.setFudOrdNo(funOrdNo);
        fundOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY); //等待充值
        fundOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        fundOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
        fundOrderDO.setNtfSts(CpiConstants.NOTIFY_WAIT); //待通知
        fundOrderDao.insert(fundOrderDO);

        //创建网银订单
        EbankOrderDO ebankOrderDO = new EbankOrderDO();
        String subOrdNo = IdGenUtils.generateIdWithDateTime("SUB_ORD_NO", "CPI", 6);
        //String chkKey = IdGenUtils.generateIdWithDateTime("OUT_ORD_NO",6);
        //chkkey使用收银台订单号
        String chkKey = ebankpayReqDTO.getReqOrdNo();
        BeanUtils.copyProperties(ebankOrderDO, ebankpayReqDTO);
        ebankOrderDO.setSubOrdNo(subOrdNo);
        ebankOrderDO.setCorpBusTyp(ebankpayReqDTO.getCorpBusTyp().getType());
        ebankOrderDO.setCorpBusSubTyp(ebankpayReqDTO.getCorpBusSubTyp().getType());
        ebankOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY); //等待充值
        ebankOrderDO.setFndOrdNo(funOrdNo);
        ebankOrderDO.setChkKey(chkKey);
        ebankOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        ebankOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
        ebankOrderDO.setCrdCorpOrg(rspRouteInfo.getCrdCorpOrg());
        ebankOrderDO.setRutCorpOrg(rspRouteInfo.getRutCorpOrg());
        ebankOrderDO.setRcvUserId(ebankpayReqDTO.getMerchantId());
        ebankOrderDao.insert(ebankOrderDO);
        return ebankOrderDO;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public void updateEbankOrder(EbankOrderDO ebankOrderDO) {
        ebankOrderDao.update(ebankOrderDO);
    }

    /**
     * 根据银行返回信息更新网银子订单表，以及充值订单表
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public void  updateOrderStateByBank(EbankOrderDO ebankOrderDO, BaseBnkRspBO baseBnkRspBO, CpiMsgCd cpiMsgCd, String splOrdFlg){
        //更新网银订单状态
        EbankOrderDO updateEbankOrderDO = new EbankOrderDO();
        updateEbankOrderDO.setSubOrdNo(ebankOrderDO.getSubOrdNo());

        //更新充值订单状态
        FundOrderDO updateFundOrder = new FundOrderDO();
        updateFundOrder.setFudOrdNo(ebankOrderDO.getFndOrdNo());

        //判断交易状态
        if (JudgeUtils.isSuccess(cpiMsgCd.getMsgCd())) {
            //交易成功
            updateEbankOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateEbankOrderDO.setAcDt(LemonUtils.getAccDate());
            //设置网银机构订单号
            updateEbankOrderDO.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
            updateFundOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateFundOrder.setAcDt(LemonUtils.getAccDate());
            updateFundOrder.setOrdSuccDt(DateTimeUtils.getCurrentLocalDate());
            updateFundOrder.setOrdSuccTm(DateTimeUtils.getCurrentLocalTime());
        } else if(CpiMsgCd.BNK_70102.equals(cpiMsgCd)) {
            //银行处理中
            updateEbankOrderDO.setOrdSts(CpiConstants.ORD_PAY_BANKING);
            updateEbankOrderDO.setOrgRspCd(baseBnkRspBO.getOrgRspCd());
            updateEbankOrderDO.setOrgRspMsg(baseBnkRspBO.getOrgRspMsg());
            updateEbankOrderDO.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
            updateFundOrder.setOrdSts(CpiConstants.ORD_PAY_BANKING);
        } else {
            //交易失败
            updateEbankOrderDO.setOrdSts(CpiConstants.ORD_FAIL);
            updateFundOrder.setOrdSts(CpiConstants.ORD_FAIL);
        }

        //补单标识
        if(JudgeUtils.isNotEmpty(splOrdFlg)) {
            updateEbankOrderDO.setSplOrdFlg(splOrdFlg);
        }

        //更新订单
        BeanUtils.copyProperties(updateEbankOrderDO, baseBnkRspBO);
        ebankOrderDao.update(updateEbankOrderDO);
        fundOrderDao.update(updateFundOrder);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true, rollbackFor = Exception.class)
    public EbankOrderDO selectEbankpayOrderByChkKey(String chkKey) {
        return ebankOrderDao.selectEbankpayOrderByChkKey(chkKey);
    }


}
