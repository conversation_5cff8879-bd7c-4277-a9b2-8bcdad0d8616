package com.hisun.lemon.cpi.schedule;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpi.service.ICheckMgrService;
import com.hisun.lemon.cpi.thread.WriteFundFileThread;
import com.hisun.lemon.cpi.thread.WriteRefundFileThread;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class WriteCheckFileSchedule {

    private static final Logger logger = LoggerFactory.getLogger(CheckBatStartSchedule.class);

    /**
     * 注入对账主控服务
     */
    @Resource
    private ICheckMgrService checkMgrService;

    @Resource
    private TaskExecutor taskExecutor;

    @Resource
    private DistributedLocker distributedLocker;

    /**
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 0/30 2-12 * * ?")
    public void writeCheckFile(){
        logger.debug("==================写入对账文件定时 开始时间：" + DateTimeUtils.getCurrentDateTimeStr());
        try {
            taskExecutor.execute(new WriteFundFileThread(checkMgrService, distributedLocker));
            logger.debug("==================异步调起充值写对账文件");
            taskExecutor.execute(new WriteRefundFileThread(checkMgrService, distributedLocker));
            logger.debug("==================异步调起充值写对账文件");
        } catch (Exception e) {
            logger.error("写入对账文件定时任务执行失败，异常为 ",e);
            return ;
        }
        logger.debug("=================写入对账文件定时 结束时间：" + DateTimeUtils.getCurrentDateTimeStr());
    }
}
