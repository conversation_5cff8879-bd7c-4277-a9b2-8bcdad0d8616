package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.FastpayAcledaReqDTO;
import com.hisun.lemon.cpi.dto.FastpayReqDTO;
import com.hisun.lemon.cpi.entity.CardProtDO;
import com.hisun.lemon.cpi.entity.CardProtJrnDO;
import com.hisun.lemon.cpi.entity.ShortcutOrderDO;

/**
 * Created by Rui on 2017/7/10.
 */
public interface IFastpayTransaction {

    /**
     * 插入预签约流水
     */
    void insertCardProtJrnDO(CardProtJrnDO cardProtJrnDO);

    /**
     * 登记资金流出订单表和快捷订单表
     * @param fastpayDTO
     * @param cardProtDO
     * @return
     */
    ShortcutOrderDO createFundOrder(FastpayReqDTO fastpayDTO, CardProtDO cardProtDO,String FunOrdNo);

    /**
     * 登记ACLEDA银行资金流出订单表和快捷订单表
     * @param fastpayAcledaReqDTO
     * @param FunOrdNo
     * @return
     */
    ShortcutOrderDO createAcledaFundOrder(FastpayAcledaReqDTO fastpayAcledaReqDTO, String FunOrdNo);
}
