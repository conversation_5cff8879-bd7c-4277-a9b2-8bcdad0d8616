package com.hisun.lemon.cpi.bnkapi.bestpay.ebankpay;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayApi;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPayEnumCommon;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestpayProperties;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.*;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.PaymentQueryRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.PlaceOrderRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.RefundQueryRsp;
import com.hisun.lemon.cpi.bestpay.ebankpay.rsp.RefundRsp;
import com.hisun.lemon.cpi.bnkapi.bestpay.BestPayRetCdConvert;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.entity.EbankOrderDO;
import com.hisun.lemon.cpi.entity.RefundOrderDO;
import com.hisun.lemon.cpi.entity.RefundSuborderDO;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.jcommon.file.FileFtpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.Map;

/**
 * Created by Rui on 2017/8/2.
 */
@Component
public class BestPayScanPayApi {

    private static final Logger logger = LoggerFactory.getLogger(BestPayScanPayApi.class);

    @Resource
    private BestPayApi bestPayApi;

    @Resource
    private BestpayProperties bestpayProperties;

    @Resource
    private BestPayRetCdConvert bestPayRetCdConvert;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 用户扫码下单，聚合支付
     */
    public BaseBnkRspBO userCreateBankOrder(EbankOrderDO ebankOrderDO, Map<String, String> resultMap){
        BaseBnkRspBO baseBnkRspBO = null;
        String outOrdNo = ebankOrderDO.getChkKey();//商户外发请求订单号
        try {
            //调用翼支付聚合支付下单接口
            PlaceOrderReq placeOrderReq = new PlaceOrderReq();
            placeOrderReq.setCharset(bestpayProperties.getCharset());
            placeOrderReq.setVersion(bestpayProperties.getVersion());
            placeOrderReq.setSignType(bestpayProperties.getSignType());
            placeOrderReq.setService(BestPayEnumCommon.EnumService.APP_PAY);
            placeOrderReq.setMerchantId(bestpayProperties.getMerchantId());
            placeOrderReq.setRequestId(DateTimeUtils.getCurrentDateStr());
            placeOrderReq.setOrderId(outOrdNo);
            placeOrderReq.setJrnNo(outOrdNo);
            placeOrderReq.setOrderAmt(ebankOrderDO.getOrdAmt().toString());
            placeOrderReq.setOrderCcy(BestPayEnumCommon.EnumOrderCcy.USD);
            placeOrderReq.setProductId(bestpayProperties.getProductId());
            placeOrderReq.setProductDesc(bestpayProperties.getProductDesc());
            placeOrderReq.setLoginNo(resultMap.get("mblNo"));
            placeOrderReq.setProviceCode(bestpayProperties.getProviceCode());
            placeOrderReq.setCityCode(bestpayProperties.getCityCode());
            placeOrderReq.setBankUrl(bestpayProperties.getFundBankUrl());
            placeOrderReq.setAttach(bestpayProperties.getSubject());
            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, placeOrderReq, true);
            logger.info("BestPayScanPayApi.userCreateBankOrder() 翼支付用户扫码下单请求参数 " + reqData);

            //返回报文
            PlaceOrderRsp placeOrderRsp = bestPayApi.doSend(placeOrderReq, BestPayEnumCommon.EnumSource.placeOrder);
            if(JudgeUtils.isNull(placeOrderRsp)) {
                logger.error("BestPayScanPayApi.userCreateBankOrder() 银行接口返回报文为空");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, placeOrderRsp, true);
            logger.info("BestPayScanPayApi.userCreateBankOrder() 银行接口返回报文：" + rspData);

            //检查翼支付返回的流水号和订单号，出现不一致的报错
            String retJrnNo = placeOrderRsp.getJrnNo();
            String retOrdNo = placeOrderRsp.getOrderId();
            //流水号不一致
            if(!JudgeUtils.equals(retJrnNo, outOrdNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }
            //订单号不一致
            if(!JudgeUtils.equals(retOrdNo, outOrdNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }

            //翼支付处理结果
            String resultCode = placeOrderRsp.getResultCode();
            String resultMessage = placeOrderRsp.getResultMessage();
            String retOrdSts = null;
            if(JudgeUtils.isNotNull(placeOrderRsp.getOrderSts())) {
                retOrdSts = placeOrderRsp.getOrderSts().getCode();
            }
            String txFlg = bestPayRetCdConvert.payOrderResultConvert(resultCode, retOrdSts);

            //返回信息
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(outOrdNo);
            baseBnkRspBO.setChkKey(outOrdNo);
            baseBnkRspBO.setOrgJrnNo("");
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + retOrdSts);
            baseBnkRspBO.setOrgRspMsg(resultMessage);
            baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
            baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
            baseBnkRspBO.setTxFlg(txFlg);

            //若下单成功，则返回map数据给前端
            if(CpiConstants.BANK_RESPONSE_WAIT.equals(txFlg)) {
                resultMap.put("service", bestpayProperties.getService());
                resultMap.put("merchantId", placeOrderRsp.getCbpMercId());
                resultMap.put("merchantPwd", placeOrderRsp.getMercKey());
                resultMap.put("subMerchantId", placeOrderRsp.getCbpMercId());
                resultMap.put("backMerchantUrl", placeOrderRsp.getBackUrl());
                resultMap.put("signType", bestpayProperties.getSignType());
                resultMap.put("sign", placeOrderRsp.getServerSign());
                resultMap.put("orderSeq", outOrdNo);
                resultMap.put("orderReqTranSeq", outOrdNo);
                resultMap.put("orderTime", DateTimeUtils.getCurrentDateTimeStr());
                resultMap.put("orderValidityTime", "");
                resultMap.put("orderAmount", placeOrderRsp.getOrderCcyAmt());
                resultMap.put("curType", "RMB");
                resultMap.put("productId", bestpayProperties.getProductId());
                resultMap.put("productDesc", bestpayProperties.getProductDesc());
                resultMap.put("productAmount", placeOrderRsp.getOrderCcyAmt());
                resultMap.put("attachAmount", "0");
                resultMap.put("attach", "");
                resultMap.put("divDetails", "");
                resultMap.put("accountId", resultMap.get("mblNo"));
                resultMap.put("customerId", resultMap.get("mblNo"));
                resultMap.put("userIp", "");
                resultMap.put("busiType", bestpayProperties.getBusiType());
                resultMap.put("otherFlow", bestpayProperties.getOtherFlow());
                resultMap.put("accessToken", "");
                resultMap.put("switchAcc", "true");
                resultMap.put("subject", bestpayProperties.getSubject());
                resultMap.put("isShowExchangeRate", bestpayProperties.getIsShowExchangeRate());
                BigDecimal orderRate = new BigDecimal(placeOrderRsp.getOrderRat());
                resultMap.put("orderRat", orderRate.divide(new BigDecimal(100)).toString());
                resultMap.put("orderCcyAmt", placeOrderRsp.getOrderAmt());
            }
        } catch (Exception e) {
            //异常处理
            logger.error("BestPayScanPayApi.userCreateBankOrder() Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 商户扫码下单
     */
    public BaseBnkRspBO mercCreateBankOrder(EbankOrderDO ebankOrderDO,String authCode, String barCode, Map<String, String> resultMap){
        BaseBnkRspBO baseBnkRspBO = null;
        String outOrdNo = ebankOrderDO.getChkKey();//商户外发请求订单号
        try {
            //调用翼支付商户扫码下单接口
            PlaceOrderReq placeOrderReq = new PlaceOrderReq();
            placeOrderReq.setMerchantId(bestpayProperties.getMerchantId());
            placeOrderReq.setRequestId(DateTimeUtils.getCurrentDateStr());
            placeOrderReq.setCharset(bestpayProperties.getCharset());
            placeOrderReq.setService(BestPayEnumCommon.EnumService.APP_BARCODE_PAY);
            placeOrderReq.setSignType(bestpayProperties.getSignType());
            placeOrderReq.setVersion(bestpayProperties.getVersion());
            placeOrderReq.setOrderId(outOrdNo);
            placeOrderReq.setJrnNo(outOrdNo);
            placeOrderReq.setOrderAmt(ebankOrderDO.getOrdAmt().toString());
            placeOrderReq.setOrderCcy(BestPayEnumCommon.EnumOrderCcy.USD);
            placeOrderReq.setStoreId(bestpayProperties.getStoreId());
            if(JudgeUtils.isNotEmpty(authCode)) {
                placeOrderReq.setBarcode(authCode);
            }
            if(JudgeUtils.isNotEmpty(barCode)) {
                placeOrderReq.setBarcode(barCode);
            }
            placeOrderReq.setProductId(bestpayProperties.getProductId());
            placeOrderReq.setProductDesc(bestpayProperties.getProductDesc());
            if(JudgeUtils.isNotEmpty(resultMap.get("mblNo"))) {
                placeOrderReq.setLoginNo(resultMap.get("mblNo"));
            } else {
                placeOrderReq.setLoginNo(bestpayProperties.getMblNo());
            }
            placeOrderReq.setProviceCode(bestpayProperties.getProviceCode());
            placeOrderReq.setCityCode(bestpayProperties.getCityCode());
            placeOrderReq.setBankUrl(bestpayProperties.getFundBankUrl());
            placeOrderReq.setAttach(bestpayProperties.getSubject());

            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, placeOrderReq, true);
            logger.info("BestPayScanPayApi.mercCreateBankOrder() 翼支付商户扫码下单请求参数 " + reqData);

            //返回报文
            PlaceOrderRsp placeOrderRsp = bestPayApi.doSend(placeOrderReq, BestPayEnumCommon.EnumSource.placeOrder);
            if(JudgeUtils.isNull(placeOrderRsp)) {
                logger.error("BestPayScanPayApi.mercCreateBankOrder() 返回报文为空");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, placeOrderRsp, true);
            logger.info("BestPayScanPayApi.mercCreateBankOrder()银行接口返回报文：" + rspData);

            //检查翼支付返回的流水号和订单号，出现不一致的报错
            String retJrnNo = placeOrderRsp.getJrnNo();
            String retOrdNo = placeOrderRsp.getOrderId();
            //流水号不一致
            if(!JudgeUtils.equals(retJrnNo, outOrdNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }
            //订单号不一致
            if(!JudgeUtils.equals(retOrdNo, outOrdNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }

            //翼支付处理结果
            String resultCode = placeOrderRsp.getResultCode();
            String resultMessage = placeOrderRsp.getResultMessage();
            String retOrdSts = null;
            if(JudgeUtils.isNotNull(placeOrderRsp.getOrderSts())) {
                retOrdSts = placeOrderRsp.getOrderSts().getCode();
            }
            String txFlg = bestPayRetCdConvert.payOrderResultConvert(resultCode, retOrdSts);

            //返回信息
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(outOrdNo);
            baseBnkRspBO.setChkKey(outOrdNo);
            baseBnkRspBO.setOrgJrnNo("");
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + retOrdSts);
            baseBnkRspBO.setOrgRspMsg(resultMessage);
            baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
            baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
            baseBnkRspBO.setTxFlg(txFlg);
        } catch(Exception e) {
            //异常处理
            logger.error("BestPayScanPayApi.mercCreateBankOrder() Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 查询扫码订单详情（用户扫码付款和商户扫码收款通用）
     */
    public BaseBnkRspBO queryPayOrder(EbankOrderDO ebankOrderDO){
        BaseBnkRspBO baseBnkRspBO = null;
        String outOrdNo = ebankOrderDO.getChkKey();//商户外发请求订单号
        try {
            //调用翼支付查询接口
            PaymentQueryReq paymentQueryReq = new PaymentQueryReq();
            paymentQueryReq.setOrderId(outOrdNo);
            paymentQueryReq.setMerchantId(bestpayProperties.getMerchantId());
            paymentQueryReq.setRequestId(DateTimeUtils.getCurrentDateStr());
            paymentQueryReq.setCharset(bestpayProperties.getCharset());
            paymentQueryReq.setService(BestPayEnumCommon.EnumService.APP_ORDER_QUERY);
            paymentQueryReq.setSignType(bestpayProperties.getSignType());
            paymentQueryReq.setVersion(bestpayProperties.getVersion());

            //返回报文
            PaymentQueryRsp paymentQueryRsp = bestPayApi.doSend(paymentQueryReq, BestPayEnumCommon.EnumSource.paymentQuery);
            if(JudgeUtils.isNull(paymentQueryRsp)) {
                logger.error("BestPayScanPayApi.queryPayOrder() 银行接口返回报文为空");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, paymentQueryRsp, true);
            logger.info("BestPayScanPayApi.queryPayOrder() 银行接口返回报文：" + rspData);

            //检查翼支付返回的订单号，出现不一致的报错
            String retOrdNo = paymentQueryRsp.getOrderId();
            if(!JudgeUtils.equals(retOrdNo, outOrdNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }

            //翼支付处理结果
            String resultCode = paymentQueryRsp.getResultCode();
            String resultMessage = paymentQueryRsp.getResultMessage();
            String retOrdSts = null;
            if(JudgeUtils.isNotNull(paymentQueryRsp.getOrderSts())) {
                retOrdSts = paymentQueryRsp.getOrderSts().getCode();
            }
            String txFlg = bestPayRetCdConvert.queryOrderResultConvert(resultCode, retOrdSts);

            //返回信息
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(outOrdNo);
            baseBnkRspBO.setChkKey(outOrdNo);
            baseBnkRspBO.setOrgJrnNo("");
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + retOrdSts);
            baseBnkRspBO.setOrgRspMsg(resultMessage);
            baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
            baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
            baseBnkRspBO.setTxFlg(txFlg);
        } catch(Exception e) {
            //异常处理
            logger.error("BestPayScanPayApi.queryPayOrder() Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 商户扫码后台退款
     */
    public BaseBnkRspBO mercRefundBankOrder(RefundOrderDO refundOrderDO, EbankOrderDO ebankOrderDO, String outOrdNo){
        BaseBnkRspBO baseBnkRspBO = null;
        String oldOrderNo = ebankOrderDO.getChkKey();
        try {
            //调用银行接口
            RefundReq refundReq = new RefundReq();
            refundReq.setBgUrl(bestpayProperties.getFundBankUrl());
            refundReq.setOldJrnNo(oldOrderNo);
            refundReq.setOldOrderNo(oldOrderNo);
            refundReq.setRefundAmt(refundOrderDO.getOrdAmt().toString());
            refundReq.setRefundCcy(BestPayEnumCommon.EnumOrderCcy.USD);
            refundReq.setRefundReqNo(outOrdNo);
            refundReq.setMerchantId(bestpayProperties.getMerchantId());
            refundReq.setRequestId(DateTimeUtils.getCurrentDateStr());
            refundReq.setCharset(bestpayProperties.getCharset());
            refundReq.setService(BestPayEnumCommon.EnumService.APP_REFUND);
            refundReq.setSignType(bestpayProperties.getSignType());
            refundReq.setVersion(bestpayProperties.getVersion());

            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, refundReq, true);
            logger.info("BestPayScanPayApi.mercRefundBankOrder() 翼支付商户扫码后台退款请求参数 " + reqData);

            //返回报文
            RefundRsp refundRsp = bestPayApi.doSend(refundReq, BestPayEnumCommon.EnumSource.refund);
            if(JudgeUtils.isNull(refundRsp)) {
                logger.error("BestPayScanPayApi.mercRefundBankOrder() 银行接口返回报文为空");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, refundRsp, true);
            logger.info("BestPayScanPayApi.mercRefundBankOrder() 银行接口返回报文：" + rspData);

            //检查翼支付返回的流水号和订单号，出现不一致的报错
            String retRefundReqNo = refundRsp.getRefundReqNo();
            if(!JudgeUtils.equals(outOrdNo, retRefundReqNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }
            String retOldOrderNo = refundRsp.getOldOrderId();
            if(!JudgeUtils.equals(oldOrderNo, retOldOrderNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }
            String retOldJrnNo = refundRsp.getOldJrnNo();
            if(!JudgeUtils.equals(oldOrderNo, retOldJrnNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }

            //翼支付处理结果
            String resultCode = refundRsp.getResultCode();
            String resultMessage = refundRsp.getResultMessage();
            String retOrdSts = null;
            if(JudgeUtils.isNotNull(refundRsp.getRefundSts())) {
                retOrdSts = refundRsp.getRefundSts().getCode();
            }
            String txFlg = bestPayRetCdConvert.refundOrderResultConvert(resultCode, retOrdSts);

            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(outOrdNo);
            baseBnkRspBO.setChkKey(outOrdNo);
            baseBnkRspBO.setOrgJrnNo("");
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + retOrdSts);
            baseBnkRspBO.setOrgRspMsg(resultMessage);
            baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
            baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
            baseBnkRspBO.setTxFlg(txFlg);
        } catch(Exception e) {
            //异常处理
            logger.error("BestPayScanPayApi.mercRefundBankOrder() Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 用户主动退款
     */
    public BaseBnkRspBO userRefundBankOrder(RefundOrderDO refundOrderDO, EbankOrderDO ebankOrderDO, String outOrdNo){
        BaseBnkRspBO baseBnkRspBO = null;
        String oldOrderNo = ebankOrderDO.getChkKey();
        try {
            //调用翼支付用户扫码退款接口
            RefundReq refundReq = new RefundReq();
            refundReq.setCharset(bestpayProperties.getCharset());
            refundReq.setVersion(bestpayProperties.getVersion());
            refundReq.setSignType(bestpayProperties.getSignType());
            refundReq.setService(BestPayEnumCommon.EnumService.APP_REFUND);
            refundReq.setMerchantId(bestpayProperties.getMerchantId());
            refundReq.setRequestId(DateTimeUtils.getCurrentDateStr());
            refundReq.setRefundReqNo(outOrdNo);
            refundReq.setOldJrnNo(oldOrderNo);
            refundReq.setOldOrderNo(oldOrderNo);
            refundReq.setRefundCcy(BestPayEnumCommon.EnumOrderCcy.USD);
            refundReq.setBgUrl(bestpayProperties.getRefundBankUrl());
            refundReq.setRefundAmt(refundOrderDO.getOrdAmt().toString());
            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, refundReq, true);
            logger.info("BestPayScanPayApi.userRefundBankOrder() 翼支付用户主动退款请求参数 " + reqData);

            //返回报文
            RefundRsp refundRsp = bestPayApi.doSend(refundReq, BestPayEnumCommon.EnumSource.refund);
            if(JudgeUtils.isNull(refundRsp)) {
                logger.error("BestPayScanPayApi.userRefundBankOrder() 银行接口返回报文为空");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, refundRsp, true);
            logger.info("BestPayScanPayApi.userRefundBankOrder() 银行接口返回报文：" + rspData);

            //检查翼支付返回的流水号和订单号，出现不一致的报错
            String retRefundReqNo = refundRsp.getRefundReqNo();
            if(!JudgeUtils.equals(outOrdNo, retRefundReqNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }
            String retOldOrderNo = refundRsp.getOldOrderId();
            if(!JudgeUtils.equals(oldOrderNo, retOldOrderNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }
            String retOldJrnNo = refundRsp.getOldJrnNo();
            if(!JudgeUtils.equals(oldOrderNo, retOldJrnNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }

            //翼支付处理结果
            String resultCode = refundRsp.getResultCode();
            String resultMessage = refundRsp.getResultMessage();
            String retOrdSts = null;
            if(JudgeUtils.isNotNull(refundRsp.getRefundSts())) {
                retOrdSts = refundRsp.getRefundSts().getCode();
            }else{
                retOrdSts = "";
            }
            String txFlg = bestPayRetCdConvert.refundOrderResultConvert(resultCode, retOrdSts);

            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(outOrdNo);
            baseBnkRspBO.setChkKey(outOrdNo);
            baseBnkRspBO.setOrgJrnNo("");
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + retOrdSts);
            baseBnkRspBO.setOrgRspMsg(resultMessage);
            baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
            baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
            baseBnkRspBO.setTxFlg(txFlg);
        } catch(Exception e) {
            //异常处理
            logger.error("BestPayScanPayApi.userRefundBankOrder() Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 查询退款订单详情（用户扫码退款和商户扫码退款通用）
     */
    public BaseBnkRspBO queryRefundOrder(RefundSuborderDO refundSuborderDO, EbankOrderDO ebankOrderDO){
        BaseBnkRspBO baseBnkRspBO = null;
        String outOrdNo = refundSuborderDO.getChkKey();//商户退款外发请求订单号
        String oldOrdNo = ebankOrderDO.getChkKey();//原商户下单支付外发请求订单号
        try {
            //调用翼支付退款查询接口
            RefundQueryReq refundQueryRsq = new RefundQueryReq();
            refundQueryRsq.setOldOrderId(oldOrdNo);
            refundQueryRsq.setRefundReqNo(outOrdNo);
            refundQueryRsq.setMerchantId(bestpayProperties.getMerchantId());
            refundQueryRsq.setRequestId(DateTimeUtils.getCurrentDateStr());
            refundQueryRsq.setCharset(bestpayProperties.getCharset());
            refundQueryRsq.setService(BestPayEnumCommon.EnumService.APP_REFUND_QUERY);
            refundQueryRsq.setSignType(bestpayProperties.getSignType());
            refundQueryRsq.setVersion(bestpayProperties.getVersion());

            //返回报文
            RefundQueryRsp refundQueryRsp = bestPayApi.doSend(refundQueryRsq, BestPayEnumCommon.EnumSource.refundQuery);
            if(JudgeUtils.isNull(refundQueryRsp)) {
                logger.error("BestPayScanPayApi.queryPayOrder() 银行接口返回报文为空");
                //LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
                return baseBnkRspBO;
            }
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, refundQueryRsp, true);
            logger.info("BestPayScanPayApi.queryPayOrder() 银行接口返回报文：" + rspData);

            //检查商户退款请求订单号是否一致
            String retReqOrdNo = refundQueryRsp.getRefundReqNo();
            if(!JudgeUtils.equals(retReqOrdNo, outOrdNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }

            //检查原商户下单支付请求订单号是否一致
            String retOldOrdNo = refundQueryRsp.getOldOrderId();
            if(!JudgeUtils.equals(retOldOrdNo, oldOrdNo)) {
                LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
            }

            //翼支付处理结果
            String resultCode = refundQueryRsp.getResultCode();
            String resultMessage = refundQueryRsp.getResultMessage();
            String retOrdSts = null;
            if(JudgeUtils.isNotNull(refundQueryRsp.getRefundSts())) {
                retOrdSts = refundQueryRsp.getRefundSts().getCode();
            }
            String txFlg = bestPayRetCdConvert.queryOrderResultConvert(resultCode, retOrdSts);

            //返回信息
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(outOrdNo);
            baseBnkRspBO.setChkKey(outOrdNo);
            baseBnkRspBO.setOrgJrnNo("");
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + retOrdSts);
            baseBnkRspBO.setOrgRspMsg(resultMessage);
            baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
            baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
            baseBnkRspBO.setTxFlg(txFlg);
        } catch(Exception e) {
            //异常处理
            logger.error("BestPayScanPayApi.queryPayOrder() Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 翼支付结果通知，用户扫码或商户扫码支付
     */
    public BaseBnkRspBO bestPayFundNotify(PlaceOrderNotifyReq placeOrderNotifyReq) {
        //翼支付处理结果
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        baseBnkRspBO.setOutOrdNo(placeOrderNotifyReq.getOrderId());//商户请求订单号
        baseBnkRspBO.setChkKey(placeOrderNotifyReq.getOrderId());//商户请求订单号
        baseBnkRspBO.setOrgJrnNo(placeOrderNotifyReq.getUpTranSeq());//翼支付系统流水号
        baseBnkRspBO.setOrgRspMsg(placeOrderNotifyReq.getResultMessage());//返回信息
        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());

        //返回码为 CBP00000-支付成功，其他均为支付失败
        String resultCode = placeOrderNotifyReq.getResultCode();//返回码
        logger.info("BestPayScanPayApi.bestPayFundNotify() 翼支付扫码订单结果通知 resultCode: " + resultCode);
        if(JudgeUtils.isSuccess(resultCode)) {
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + CpiConstants.BANK_RESPONSE_SUCCESS);
            baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
        } else {
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + CpiConstants.BANK_RESPONSE_FAIL);
            baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
        }
        return baseBnkRspBO;
    }

    /**
     * 翼支付结果通知，用户退款或商户退款
     */
    public BaseBnkRspBO bestPayRefundNotify(RefundNotifyReq refundNotifyReq) {
        //翼支付处理结果
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        baseBnkRspBO.setOutOrdNo(refundNotifyReq.getOrderId());//商户请求订单号
        baseBnkRspBO.setChkKey(refundNotifyReq.getRfdAplJrn());//商户请求订单号
        baseBnkRspBO.setOrgJrnNo(refundNotifyReq.getUpTranSeq());//翼支付系统流水号
        baseBnkRspBO.setOrgRspMsg(refundNotifyReq.getResultMessage());//返回信息
        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());

        //返回码为 CBP00000-退款成功，其他均为退款失败
        String resultCode = refundNotifyReq.getResultCode();//返回码
        logger.info("BestPayScanPayApi.bestPayRefundNotify() 翼支付退款订单结果 resultCode: " + resultCode);
        if(JudgeUtils.isSuccess(resultCode)) {
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + CpiConstants.BANK_RESPONSE_SUCCESS);
            baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
        } else {
            baseBnkRspBO.setOrgRspCd(resultCode + "|" + CpiConstants.BANK_RESPONSE_FAIL);
            baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
        }
        return baseBnkRspBO;
    }

    /**
     * ftp服务器下载翼支付提供的充值对账文件
     */
    public String getFundCheckFile(String chkFilPath, LocalDate chkDt) throws Exception {
        //对账文件本地系统存放路径
        String chkDtStr = DateTimeUtils.formatLocalDate(chkDt);
        String chkFilNm = bestpayProperties.getMerchantId() + "_" + chkDtStr + "_fund.txt";
        String chkfileFullPath = chkFilPath + File.separator + chkFilNm;
        logger.info("BestPayScanPayApi.getFundCheckFile() 银行充值对账文件全路径: " + chkfileFullPath);

        //step1：获取银行对账文件，FTP下载、请求接口下载等
        String remoteIp = bestpayProperties.getRemoteIp();
        int remotePort = Integer.valueOf(bestpayProperties.getRemotePort());
        String remotePath = bestpayProperties.getRemoteFilePath();
        String name = bestpayProperties.getRemoteUsername();
        String pwd = bestpayProperties.getRemotePassword();
        FileFtpUtils fileFtpUtils = new FileFtpUtils(remoteIp,remotePort,name,pwd);

        boolean getFileFlg = fileFtpUtils.get(remotePath, chkFilNm, chkFilPath);
        if(!getFileFlg){
            logger.error("BestPayScanPayApi.getFundCheckFile() 银行对账文件FTP下载失败，对账日期：" + chkDtStr);
            return null;
        }

        //step2：若文件不存在或文件内容为空，则下载文件异常
        File file = new File(chkfileFullPath);
        if (!file.exists()) {
            logger.error("BestPayScanPayApi.getFundCheckFile() 银行对账文件不存在，对账日期：" + chkDtStr);
            return null;
        }
        return chkFilNm;
    }

    /**
     * ftp服务器下载翼支付提供的退款对账文件
     */
    public String getRefundCheckFile(String chkFilPath, LocalDate chkDt) throws Exception {
        //对账文件本地系统存放路径
        String chkDtStr = DateTimeUtils.formatLocalDate(chkDt);
        String chkFilNm = bestpayProperties.getMerchantId() + "_" + chkDtStr + "_refund.txt";
        String chkfileFullPath = chkFilPath + File.separator + chkFilNm;
        logger.info("BestPayScanPayApi.getRefundCheckFile() 银行退款对账文件全路径: " + chkfileFullPath);

        //step1：获取银行对账文件，FTP下载、请求接口下载等
        String remoteIp = bestpayProperties.getRemoteIp();
        int remotePort = Integer.valueOf(bestpayProperties.getRemotePort());
        String remotePath = bestpayProperties.getRemoteFilePath();
        String name = bestpayProperties.getRemoteUsername();
        String pwd = bestpayProperties.getRemotePassword();
        FileFtpUtils fileFtpUtils = new FileFtpUtils(remoteIp,remotePort,name,pwd);

        boolean getFileFlg = fileFtpUtils.get(remotePath, chkFilNm, chkFilPath);
        if(!getFileFlg) {
            logger.error("BestPayScanPayApi.getRefundCheckFile() 银行对账文件FTP下载失败，对账日期：" + chkDtStr);
            return null;
        }

        //step2：若文件不存在或文件内容为空，则下载文件异常
        File file = new File(chkfileFullPath);
        if(!file.exists()) {
            logger.error("BestPayScanPayApi.getRefundCheckFile() 银行对账文件不存在，对账日期：" + chkDtStr);
            return null;
        }
        return chkFilNm;
    }

}
