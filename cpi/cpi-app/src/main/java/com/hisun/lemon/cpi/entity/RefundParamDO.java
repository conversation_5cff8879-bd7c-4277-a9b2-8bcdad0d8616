/*
 * @ClassName RefundParamDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class RefundParamDO extends BaseDO {
    /**
     * @Fields rfdParId 主键
     */
    private String rfdParId;
    /**
     * @Fields rutCorg 路径合作机构
     */
    private String rutCorg;
    /**
     * @Fields copBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields copBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields ioFlg 结算模式，1：收支两条线，2：轧差
     */
    private String ioFlg;
    /**
     * @Fields sadRfdFlg 当日退款标识，Y支持，N不支持
     */
    private String sadRfdFlg;
    /**
     * @Fields mtsRfdFlg 多次退款标识，Y支持，N不支持
     */
    private String mtsRfdFlg;
    /**
     * @Fields autoRfdFlg 自动退款标识，Y支持，N不支持
     */
    private String autoRfdFlg;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getRfdParId() {
        return rfdParId;
    }

    public void setRfdParId(String rfdParId) {
        this.rfdParId = rfdParId;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getIoFlg() {
        return ioFlg;
    }

    public void setIoFlg(String ioFlg) {
        this.ioFlg = ioFlg;
    }

    public String getSadRfdFlg() {
        return sadRfdFlg;
    }

    public void setSadRfdFlg(String sadRfdFlg) {
        this.sadRfdFlg = sadRfdFlg;
    }

    public String getMtsRfdFlg() {
        return mtsRfdFlg;
    }

    public void setMtsRfdFlg(String mtsRfdFlg) {
        this.mtsRfdFlg = mtsRfdFlg;
    }

    public String getAutoRfdFlg() {
        return autoRfdFlg;
    }

    public void setAutoRfdFlg(String autoRfdFlg) {
        this.autoRfdFlg = autoRfdFlg;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}