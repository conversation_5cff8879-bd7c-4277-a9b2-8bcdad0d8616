/*
 * @ClassName PosPreAuDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-18 15:01:57
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class PosPreAuDO extends BaseDO {
    /**
     * @Fields preAuNo 预授权号
     */
    private String preAuNo;
    /**
     * @Fields acDt 记账日期
     */
    private LocalDate acDt;
    /**
     * @Fields crdCorpOrg 资金合作机构
     */
    private String crdCorpOrg;
    /**
     * @Fields rutCorpOrg 路径合作机构
     */
    private String rutCorpOrg;
    /**
     * @Fields crdAcTyp 银行卡类型，C贷记卡，D借记卡
     */
    private String crdAcTyp;
    /**
     * @Fields crdNoEnc 加密银行卡号
     */
    private String crdNoEnc;
    /**
     * @Fields ordCcy 币种
     */
    private String ordCcy;
    /**
     * @Fields ordAmt 订单金额
     */
    private BigDecimal ordAmt;
    /**
     * @Fields ordSts 订单状态，S：预授权，C：预授权撤销，S1：预授权完成，C1：预授权完成撤销成功
     */
    private String ordSts;
    /**
     * @Fields trmNo 终端号
     */
    private String trmNo;
    /**
     * @Fields autCd 授权码
     */
    private String autCd;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getPreAuNo() {
        return preAuNo;
    }

    public void setPreAuNo(String preAuNo) {
        this.preAuNo = preAuNo;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public String getTrmNo() {
        return trmNo;
    }

    public void setTrmNo(String trmNo) {
        this.trmNo = trmNo;
    }

    public String getAutCd() {
        return autCd;
    }

    public void setAutCd(String autCd) {
        this.autCd = autCd;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}