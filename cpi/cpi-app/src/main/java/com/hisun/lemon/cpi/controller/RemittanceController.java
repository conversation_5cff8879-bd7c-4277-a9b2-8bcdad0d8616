package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.service.IRemitService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Created by Rui on 2017/7/5.
 */
@RestController
@RequestMapping("/cpi/remittance")
@Api(tags="RemittanceController", description="汇款及营业厅服务")
public class RemittanceController extends BaseController {

    @Resource
    IRemitService remitService;

    @ApiOperation(value="汇款登记", notes="汇款登记")
    @ApiResponse(code = 200, message = "登记结果")
    @PostMapping("/order")
    public GenericRspDTO<RemittanceRspDTO> remit(@Validated @RequestBody GenericDTO<RemittanceReqDTO> genericDTO) {
        return remitService.remit(genericDTO);
    }

    @ApiOperation(value="汇款结果查询", notes="汇款结果查询")
    @ApiResponse(code = 200, message = "汇款结果")
    @GetMapping("/result")
    public GenericRspDTO<OrderResultRspDTO> orderQuery(@Validated @ApiParam(name = "ordNo", value = "订单号", required = true) @RequestParam(value = "ordNo") String ordNo) {
        return remitService.queryOrder(ordNo);
    }

    @ApiOperation(value="汇款结果查询", notes="汇款结果查询")
    @ApiResponse(code = 200, message = "汇款结果")
    @GetMapping(value = "/result/query")
    public GenericRspDTO<OrderResultRspDTO> orderQueryByOrdNo(@Validated RemittanceQueryReqDTO remittanceQueryReqDTO) {
        return remitService.queryOrder(remittanceQueryReqDTO.getOrdNo());
    }

    @ApiOperation(value="确认处理", notes="确认处理")
    @ApiResponse(code = 200, message = "确认处理")
    @PostMapping("/payment")
    public GenericRspDTO<NoBody> payOrder(@Validated @RequestBody GenericDTO<RemittanceConfirmDTO> genericDTO) {
        return remitService.payOrder(genericDTO);
    }

    @ApiOperation(value="营业厅充值登记", notes="营业厅充值登记")
    @ApiResponse(code = 200, message = "营业厅充值登记结果")
    @PostMapping("/hallOrder")
    public GenericRspDTO<RemittanceRspDTO> hallRemit(@Validated @RequestBody GenericDTO<RemittanceReqDTO> genericDTO) {
        return remitService.hallRemit(genericDTO);
    }
}
