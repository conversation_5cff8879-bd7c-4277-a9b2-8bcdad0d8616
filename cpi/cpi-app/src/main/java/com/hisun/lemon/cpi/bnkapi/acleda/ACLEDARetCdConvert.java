package com.hisun.lemon.cpi.bnkapi.acleda;

import com.hisun.lemon.cpi.common.CpiMsgCd;
import org.springframework.stereotype.Component;

/**
 * User : xian
 * Date : 2018/2/27
 * Time : 12:00
 **/
@Component("acledaRetCdConvert")
public class ACLEDARetCdConvert {

    public CpiMsgCd Convert(String retCode,int method){
        //opensessionv2
        if(method == 0){
            switch (retCode){
                case "0":
                    return CpiMsgCd.SUCCESS;
                case "-1" :
                    return CpiMsgCd.BNK_70300;
                case "-2":
                    return CpiMsgCd.BNK_70301;
                case "-3" :
                    return CpiMsgCd.BNK_70302;
                case "-4" :
                case "-5" :
                    return CpiMsgCd.BNK_70303;
                case "-245" :
                    return CpiMsgCd.BNK_70304;
                case "-11" :
                    return CpiMsgCd.BNK_70305;
                case "-15":
                    return CpiMsgCd.BNK_70306;
                case "-17":
                    return CpiMsgCd.BNK_70307;
                case "-14":
                    return CpiMsgCd.BNK_70308;
                default:
                    return CpiMsgCd.BNK_70099;
            }
        }

        //setdirection
        if(method == 1){
            switch (retCode){
                case "0":
                    return CpiMsgCd.SUCCESS;
                case "-1" :
                    return CpiMsgCd.BNK_70300;
                case "-2":
                default:
                    return CpiMsgCd.BNK_70099;
            }
        }

        //getTransactionstatus
        if(method == 2){
            switch (retCode){
                case "0":
                    return CpiMsgCd.SUCCESS;
                case "-1" :
                    return CpiMsgCd.BNK_70300;
                case "-2":
                    return CpiMsgCd.BNK_70309;
                case "-7" :
                    return CpiMsgCd.BNK_70305;
                case "-4" :
                case "-99" :
                    return CpiMsgCd.BNK_70309;
                case "-76" :
                    return CpiMsgCd.BNK_70301;
                case "-16" :
                    return CpiMsgCd.BNK_70310;
                default:
                    return CpiMsgCd.BNK_70099;
            }
        }
        return CpiMsgCd.BNK_70099;
    }

}
