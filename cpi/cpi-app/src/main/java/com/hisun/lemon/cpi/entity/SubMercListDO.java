/*
 * @ClassName CpiSubMercListDO
 * @Description 
 * @version 1.0
 * @Date 2018-03-22 14:57:59
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class SubMercListDO extends BaseDO {
    /**
     * @Fields subMchId 
     */
    private String subMchId;
    /**
     * @Fields appId 
     */
    private String appId;
    /**
     * @Fields mchId 
     */
    private String mchId;
    /**
     * @Fields mercNm 
     */
    private String mercNm;
    /**
     * @Fields mercSnm 
     */
    private String mercSnm;
    /**
     * @Fields officeTel 
     */
    private String officeTel;
    /**
     * @Fields mercRmk 
     */
    private String mercRmk;
    /**
     * @Fields mercWebsite 
     */
    private String mercWebsite;
    /**
     * @Fields contractNm 
     */
    private String contractNm;
    /**
     * @Fields contractTel 
     */
    private String contractTel;
    /**
     * @Fields contractWebsite 
     */
    private String contractWebsite;

    /**
     * 简介
     */
    private String mercIntroduction;

    /**
     * 业务类别
     */
    private String businessType;

    public String getSubMchId() {
        return subMchId;
    }

    public void setSubMchId(String subMchId) {
        this.subMchId = subMchId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getMercNm() {
        return mercNm;
    }

    public void setMercNm(String mercNm) {
        this.mercNm = mercNm;
    }

    public String getMercSnm() {
        return mercSnm;
    }

    public void setMercSnm(String mercSnm) {
        this.mercSnm = mercSnm;
    }

    public String getOfficeTel() {
        return officeTel;
    }

    public void setOfficeTel(String officeTel) {
        this.officeTel = officeTel;
    }

    public String getMercRmk() {
        return mercRmk;
    }

    public void setMercRmk(String mercRmk) {
        this.mercRmk = mercRmk;
    }

    public String getMercWebsite() {
        return mercWebsite;
    }

    public void setMercWebsite(String mercWebsite) {
        this.mercWebsite = mercWebsite;
    }

    public String getContractNm() {
        return contractNm;
    }

    public void setContractNm(String contractNm) {
        this.contractNm = contractNm;
    }

    public String getContractTel() {
        return contractTel;
    }

    public void setContractTel(String contractTel) {
        this.contractTel = contractTel;
    }

    public String getContractWebsite() {
        return contractWebsite;
    }

    public void setContractWebsite(String contractWebsite) {
        this.contractWebsite = contractWebsite;
    }

    public String getMercIntroduction() {
        return mercIntroduction;
    }

    public void setMercIntroduction(String mercIntroduction) {
        this.mercIntroduction = mercIntroduction;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}