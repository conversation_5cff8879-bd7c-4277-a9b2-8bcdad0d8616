/*
 * @ClassName IAlipaySettleDetailDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-17 11:04:50
 */
package com.hisun.lemon.cpi.dao.alipay;

import com.hisun.lemon.cpi.entity.alipay.AlipaySettleDetailDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IAlipaySettleDetailDao extends BaseDao<AlipaySettleDetailDO> {
    List<AlipaySettleDetailDO> findAllBySettleDate(@Param("settleDateStr") String settleDateStr);
}