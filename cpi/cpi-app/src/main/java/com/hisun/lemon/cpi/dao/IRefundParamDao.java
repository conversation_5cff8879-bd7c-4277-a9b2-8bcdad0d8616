/*
 * @ClassName IRefundParamDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.RefundParamDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IRefundParamDao extends BaseDao<RefundParamDO> {

    RefundParamDO selectByUniqueKey(@Param("rutCorg") String rutCorg,@Param("corpBusTyp") String copBusTyp, @Param("corpBusSubTyp") String corpBusSubTyp);
}