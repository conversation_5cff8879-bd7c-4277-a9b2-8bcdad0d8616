/*
 * @ClassName IPosPreAuDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-17 15:34:57
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.PosPreAuDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IPosPreAuDao extends BaseDao<PosPreAuDO> {

    /**
     * 根据条件查询唯一一条数据
     * @param posPreAuDO
     * @return
     */
    PosPreAuDO selectByCondition(@Param("posPreAuDO") PosPreAuDO posPreAuDO);
}