/*
 * @ClassName ICpiSubMercListDao
 * @Description 
 * @version 1.0
 * @Date 2018-03-22 14:57:59
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.SubMercListDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ISubMercListDao extends BaseDao<SubMercListDO> {
    List<SubMercListDO> getSubMercList(@Param("subMchId")String subMchId);
}