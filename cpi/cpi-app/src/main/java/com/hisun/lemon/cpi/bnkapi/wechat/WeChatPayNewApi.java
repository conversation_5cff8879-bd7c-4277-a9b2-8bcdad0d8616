package com.hisun.lemon.cpi.bnkapi.wechat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.IEbankOrderDao;
import com.hisun.lemon.cpi.dao.IRefundSuborderDao;
import com.hisun.lemon.cpi.dto.SubMercReqDTO;
import com.hisun.lemon.cpi.dto.WeChatTokenReq;
import com.hisun.lemon.cpi.entity.*;
//import com.hisun.lemon.cpi.utils.SignUtils;
import com.hisun.lemon.cpi.utils.XmlParseUtils;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.*;
import com.hisun.lemon.cpi.wechat.ebankpay.WeChatApi;
import com.hisun.lemon.cpi.wechat.ebankpay.WeChatProperties;
import com.hisun.lemon.cpi.wechat.ebankpay.req.*;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.*;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * 微信支付接口，新接口
 */
@Component
public class WeChatPayNewApi {
    @Resource
    private WeChatApi weChatApi;
    @Resource
    private WeChatProperties weChatProperties;
    @Resource
    private IEbankOrderDao ebankOrderDao;
    @Resource
    private IRefundSuborderDao refundSuborderDao;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private WeChatRetCdConvert weChatRetCdConvert;

    private static final Logger logger = LoggerFactory.getLogger(WeChatPayNewApi.class);

    /**
     * 微信子商户号申请
     */
    public String subMerchantApply(SubMercReqDTO subMercReqDTO) {
        //初始化请求报文
        SubMerchantEntryReq subMerchantEntryReq = new SubMerchantEntryReq();
        subMerchantEntryReq.setApp_id(weChatProperties.getAppId());
        subMerchantEntryReq.setMch_id(weChatProperties.getPlatMercId());
        subMerchantEntryReq.setMerchant_name(subMercReqDTO.getMerchantName());
        subMerchantEntryReq.setMerchant_shortname(subMercReqDTO.getMerchantShortname());
        subMerchantEntryReq.setOffice_phone(subMercReqDTO.getOfficePhone());
        subMerchantEntryReq.setContact_name(subMercReqDTO.getContactName());
        subMerchantEntryReq.setContact_phone(subMercReqDTO.getContactPhone());
        subMerchantEntryReq.setContact_email(subMercReqDTO.getContactEmail());
        subMerchantEntryReq.setBusiness_category(weChatRetCdConvert.busCategoryConvert(subMercReqDTO.getBusinessType()));
        subMerchantEntryReq.setMerchant_remark(subMercReqDTO.getMerchantRemark());
        subMerchantEntryReq.setWebsite(subMercReqDTO.getWebsite());
        subMerchantEntryReq.setMerchant_introduction(subMercReqDTO.getMercIntroduction());
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, subMerchantEntryReq, true);
        logger.info("WeChatPayNewApi.subMerchantApply() 微信子商户号申请接口请求参数 " + reqData);

        //调用微信接口，获取返回报文
        String subMchId = null;
        try {
            SubMerchantEntryRsp subMerchantEntryRsp = weChatApi.doSend(subMerchantEntryReq, EnumSource.submerchantEntry);
            if (JudgeUtils.isNull(subMerchantEntryRsp)) {
                logger.info("WeChatPayNewApi.subMerchantApply() 微信子商户号申请接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, subMerchantEntryRsp, true);
            logger.info("WeChatPayNewApi.subMerchantApply() 微信子商户号申请接口返回结果 " + reqData);
            //交易返回码
            String returnCode = subMerchantEntryRsp.getReturn_code();
            if(JudgeUtils.equals(returnCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                subMchId = subMerchantEntryRsp.getSub_mch_id();
            } else {
                logger.info("WeChatPayNewApi.subMerchantApply() 申请微信子商户号失败，原因为：" + subMerchantEntryRsp.getErr_code_des());
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.subMerchantApply() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return subMchId;
    }

    /**
     * 微信子商户号查询
     */
    public SubMercCastDO subMerchantQuery(SubMercCastDO subMercCastDO) {
        //初始化请求报文
        SubMerchantQueryReq subMerchantQueryReq = new SubMerchantQueryReq();
        subMerchantQueryReq.setApp_id(subMercCastDO.getAppId());
        subMerchantQueryReq.setMch_id(subMercCastDO.getUserId());
        subMerchantQueryReq.setSub_mch_id(subMercCastDO.getSubMchId());
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, subMerchantQueryReq, true);
        logger.info("WeChatPayNewApi.subMerchantQuery() 微信子商户号查询接口请求参数 " + reqData);

        //调用微信接口，获取返回报文
        try {
            SubMerchantQueryRsp subMerchantQueryRsp = weChatApi.doSend(subMerchantQueryReq, EnumSource.submerchantQuery);
            if (JudgeUtils.isNull(subMerchantQueryRsp)) {
                logger.info("WeChatPayNewApi.subMerchantQuery() 微信子商户号查询接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, subMerchantQueryRsp, true);
            logger.info("WeChatPayNewApi.subMerchantQuery() 微信子商户号查询接口返回结果 " + reqData);
            //返回码
            String returnCode = subMerchantQueryRsp.getReturn_code();
            //处理成功
            if(JudgeUtils.equals(returnCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                logger.info("WeChatPayNewApi.subMerchantQuery() 微信子商户号查询成功");
            } else {
                logger.info("WeChatPayNewApi.subMerchantQuery() 微信子商户号查询失败，原因为：" + subMerchantQueryRsp.getErr_code_des());
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.subMerchantQuery() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return subMercCastDO;
    }

    /**
     * 页面授权
     */
    public TokenRsp tokenApply(WeChatTokenReq weChatTokenReq) {
        //初始化请求报文
        TokenReq tokenReq = new TokenReq();
        tokenReq.setAppid(weChatProperties.getAppId());
        tokenReq.setCode(weChatTokenReq.getCode());
        tokenReq.setSecret(weChatProperties.getSecret());
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, tokenReq, true);
        logger.info("WeChatPayNewApi.tokenApply() 微信页面授权接口请求参数 " + reqData);

        //调用微信接口
        try {
            TokenRsp tokenRsp = weChatApi.doSend(tokenReq, EnumSource.Token);
            if (JudgeUtils.isNull(tokenRsp)) {
                logger.info("WeChatPayNewApi.tokenApply() 微信页面授权接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, tokenRsp, true);
            logger.info("WeChatPayNewApi.subMerchantQuery() 微信页面授权接口返回结果 " + reqData);
            String openId = tokenRsp.getOpenid();
            if (JudgeUtils.isNotNull(openId)) {
                return tokenRsp;
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.tokenApply() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return null;
    }

    /**
     * 用户扫码，聚合支付
     */
    public BaseBnkRspBO userPlaceOrder(SubMercCastDO subMercCastDO, EbankOrderDO ebankOrderDO, String openId, String ordCcy, String tradeType, Map<String, String> resultMap) {
        //订单金额由元转为分
        int totalFee = ebankOrderDO.getOrdAmt().multiply(new BigDecimal(100)).intValue();

        //初始化请求报文
        PlaceOrderReq placeOrderReq = new PlaceOrderReq();
        placeOrderReq.setAppid(weChatProperties.getAppId());
        placeOrderReq.setMch_id(weChatProperties.getPlatMercId());
        placeOrderReq.setSub_mch_id(subMercCastDO.getSubMchId());
        placeOrderReq.setOut_trade_no(ebankOrderDO.getChkKey());

        if(JudgeUtils.isNull(tradeType)){
            tradeType = EnumTradeType.JSAPI.name();
        }
        //H5支付
        if(JudgeUtils.equalsIgnoreCase(tradeType,EnumTradeType.JSAPI.name())){
            placeOrderReq.setBody("商品");
            //placeOrderReq.setDetail(info);
            placeOrderReq.setFee_type(EnumCurrency.valueOf(ordCcy));
            placeOrderReq.setTotal_fee(totalFee);
            placeOrderReq.setSpbill_create_ip("***************");
            placeOrderReq.setNotify_url(weChatProperties.getNotifyUrl());
            placeOrderReq.setTrade_type(EnumTradeType.JSAPI);
            placeOrderReq.setOpenid(openId);
        }
        //扫码支付
        if(JudgeUtils.equalsIgnoreCase(tradeType,EnumTradeType.NATIVE.name())){
            placeOrderReq.setBody(subMercCastDO.getMercNm());
            //此id为二维码中包含的商品ID
            placeOrderReq.setProduct_id(subMercCastDO.getSubMchId());
            placeOrderReq.setFee_type(EnumCurrency.valueOf(ordCcy));
            placeOrderReq.setTotal_fee(totalFee);
            placeOrderReq.setSpbill_create_ip("*************");
            placeOrderReq.setNotify_url(weChatProperties.getNotifyUrl());
            placeOrderReq.setTrade_type(EnumTradeType.NATIVE);
        }
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, placeOrderReq, true);
        logger.info("WeChatPayNewApi.userPlaceOrder() 微信用户扫码接口请求参数 " + reqData);
        //调用微信接口
        BaseBnkRspBO baseBnkRspBO = null;
        try {
            //发送报文
            PlaceOrderRsp placeOrderRsp = weChatApi.doSend(placeOrderReq, EnumSource.placeOrder);
            if (JudgeUtils.isNull(placeOrderRsp)) {
                logger.info("WeChatPayNewApi.userPlaceOrder() 微信用户扫码接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, placeOrderRsp, true);
            logger.info("WeChatPayNewApi.userPlaceOrder() 微信用户扫码接口返回结果 " + reqData);
            //接口返回信息
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(ebankOrderDO.getChkKey());
            baseBnkRspBO.setChkKey(ebankOrderDO.getChkKey());
            //交易处理码
            String returnCode = placeOrderRsp.getReturn_code();
            if(JudgeUtils.equals(returnCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                //业务处理码
                String resultCode = placeOrderRsp.getResult_code();
                if(JudgeUtils.equals(resultCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                    baseBnkRspBO.setOrgRspCd(resultCode);
                    baseBnkRspBO.setOrgRspMsg(placeOrderRsp.getReturn_msg());
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_WAIT);
                    logger.info("WeChatPayNewApi.userPlaceOrder() 用户扫码成功");

                    //返回微信app页面的参数
                    String timeStamp = Long.toString(System.currentTimeMillis() / 1000);
                    SortedMap<String, String> params = new TreeMap<>();

                    if(JudgeUtils.equalsIgnoreCase(tradeType,EnumTradeType.JSAPI.name())){
                        params.put("appId", weChatProperties.getAppId());
                        params.put("timeStamp", timeStamp);
                        params.put("nonceStr", placeOrderRsp.getNonce_str());
                        params.put("package", "prepay_id=" + placeOrderRsp.getPrepay_id());
                        params.put("signType", "MD5");
                        resultMap.put("timeStamp", timeStamp);
                        resultMap.put("nonceStr", placeOrderRsp.getNonce_str());
                        resultMap.put("package", "prepay_id=" + placeOrderRsp.getPrepay_id());
             //           resultMap.put("paySign", SignUtils.getSign(params, weChatProperties.getPlatMercPayId()));
                    }

                    if(JudgeUtils.equalsIgnoreCase(tradeType,EnumTradeType.NATIVE.name())){
                        resultMap.put("timeStamp", timeStamp);
                        resultMap.put("nonceStr", placeOrderRsp.getNonce_str());
                        resultMap.put("prepay_id",placeOrderRsp.getPrepay_id());
                        resultMap.put("signType", "MD5");
                        resultMap.put("code_url", placeOrderRsp.getCode_url());
                        resultMap.put("trade_type", placeOrderRsp.getTrade_type());
                        resultMap.put("sign", placeOrderRsp.getSign());
                    }

                } else {
                    //业务处理失败
                    baseBnkRspBO.setOrgRspCd(resultCode);
                    baseBnkRspBO.setOrgRspMsg(placeOrderRsp.getReturn_msg());
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                    logger.info("WeChatPayNewApi.userPlaceOrder() 用户扫码失败，原因为：" + placeOrderRsp.getErr_code_des());
                }
            } else {
                //交易处理失败
                baseBnkRspBO.setOrgRspCd(returnCode);
                baseBnkRspBO.setOrgRspMsg(placeOrderRsp.getReturn_msg());
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                logger.info("WeChatPayNewApi.userPlaceOrder() 用户扫码失败，原因为：" + placeOrderRsp.getErr_code_des());
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.userPlaceOrder() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 商户扫码，微信刷卡支付
     */
    public BaseBnkRspBO mercPlaceOrder(SubMercCastDO subMercCastDO, EbankOrderDO ebankOrderDO, String authCode, String ordCcy) {
        //订单金额由元转为分
        int totalFee = ebankOrderDO.getOrdAmt().multiply(new BigDecimal(100)).intValue();

        //初始化请求报文
        MercPlaceOrderReq mercPlaceOrderReq = new MercPlaceOrderReq();
        mercPlaceOrderReq.setAppid(weChatProperties.getAppId());
        mercPlaceOrderReq.setMch_id(weChatProperties.getPlatMercId());
        mercPlaceOrderReq.setSub_mch_id(subMercCastDO.getSubMchId());
        mercPlaceOrderReq.setBody("商品");
        mercPlaceOrderReq.setOut_trade_no(ebankOrderDO.getChkKey());
        mercPlaceOrderReq.setDevice_info(subMercCastDO.getUserId());
        mercPlaceOrderReq.setTotal_fee(totalFee);
        mercPlaceOrderReq.setFee_type(EnumCurrency.valueOf(ordCcy));
        mercPlaceOrderReq.setSpbill_create_ip("***************");
        mercPlaceOrderReq.setAuth_code(authCode);
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, mercPlaceOrderReq, true);
        logger.info("WeChatPayNewApi.mercPlaceOrder() 微信商户扫码接口请求参数 " + reqData);

        //调用微信接口
        BaseBnkRspBO baseBnkRspBO = null;
        try {
            //发送报文
            MercPlaceOrderRsp mercPlaceOrderRsp = weChatApi.doSend(mercPlaceOrderReq, EnumSource.mercPlaceOrder);
            if (JudgeUtils.isNull(mercPlaceOrderRsp)) {
                logger.info("WeChatPayNewApi.mercPlaceOrder() 微信商户扫码接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, mercPlaceOrderRsp, true);
            logger.info("WeChatPayNewApi.mercPlaceOrder() 微信商户扫码接口请求参数 " + reqData);
            //接口返回信息
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(ebankOrderDO.getChkKey());
            baseBnkRspBO.setChkKey(ebankOrderDO.getChkKey());
            //交易处理码
            String returnCode = mercPlaceOrderRsp.getReturn_code();
            if(JudgeUtils.equals(returnCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                //业务处理码
                String resultCode = mercPlaceOrderRsp.getResult_code();
                String errCode = mercPlaceOrderRsp.getErr_code();
                if(JudgeUtils.equals(resultCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                    baseBnkRspBO.setOrgRspCd(resultCode);
                    baseBnkRspBO.setOrgRspMsg(mercPlaceOrderRsp.getReturn_msg());
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_WAIT);
                    baseBnkRspBO.setOrgJrnNo(mercPlaceOrderRsp.getTransaction_id());
                    logger.info("WeChatPayNewApi.mercPlaceOrder() 商户扫码下单成功");
                }
                else if(JudgeUtils.equals(errCode.toUpperCase(), CpiConstants.WECHAT_USERPAYING)) {
                    //等待用户输入支付密码
                    baseBnkRspBO.setOrgRspCd(errCode);
                    baseBnkRspBO.setOrgRspMsg(mercPlaceOrderRsp.getErr_code_des());
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_WAIT);
                    logger.info("WeChatPayNewApi.mercPlaceOrder() 商户扫码下单成功，等待用户输入支付密码");
                }
                else {
                    //业务处理失败
                    baseBnkRspBO.setOrgRspCd(errCode);
                    baseBnkRspBO.setOrgRspMsg(mercPlaceOrderRsp.getErr_code_des());
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                    logger.info("WeChatPayNewApi.mercPlaceOrder() 商户扫码下单失败，原因为：" + mercPlaceOrderRsp.getErr_code_des());
                }
            } else {
                //交易处理失败
                baseBnkRspBO.setOrgRspCd(returnCode);
                baseBnkRspBO.setOrgRspMsg(mercPlaceOrderRsp.getReturn_msg());
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                logger.info("WeChatPayNewApi.mercPlaceOrder() 商户扫码下单失败，原因为：" + mercPlaceOrderRsp.getErr_code_des());
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.mercPlaceOrder() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 下单结果查询
     */
    public BaseBnkRspBO orderQuery(SubMercCastDO subMercCastDO, EbankOrderDO ebankOrderDO) {
        //初始化请求报文
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setAppid(weChatProperties.getAppId());
        orderQueryReq.setMch_id(weChatProperties.getPlatMercId());
        //orderQueryReq.setTransaction_id(ebankOrderDO.getOrgJrnNo());
        orderQueryReq.setOut_trade_no(ebankOrderDO.getChkKey());
        orderQueryReq.setSub_mch_id(subMercCastDO.getSubMchId());
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, orderQueryReq, true);
        logger.info("WeChatPayNewApi.orderQuery() 微信下单结果查询接口请求参数 " + reqData);

        //调用微信接口
        BaseBnkRspBO baseBnkRspBO = null;
        try {
            //发送报文
            OrderQueryRsp orderQueryRsp = weChatApi.doSend(orderQueryReq, EnumSource.orderQuery);
            if (JudgeUtils.isNull(orderQueryRsp)) {
                logger.info("WeChatPayNewApi.orderQuery() 微信下单结果查询接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, orderQueryRsp, true);
            logger.info("WeChatPayNewApi.orderQuery() 微信下单结果查询接口请求参数 " + reqData);
            //接口返回结果不为空
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(ebankOrderDO.getChkKey());
            baseBnkRspBO.setChkKey(ebankOrderDO.getChkKey());
            //交易处理返回码
            String returnCode = orderQueryRsp.getReturn_code();
            if(JudgeUtils.equals(returnCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                //业务处理返回码
                String resultCode = orderQueryRsp.getResult_code();
                if(JudgeUtils.equals(resultCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                    //订单状态
                    String tradeState = orderQueryRsp.getTrade_state();
                    if(JudgeUtils.equals(tradeState.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                        //订单状态成功
                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
                        baseBnkRspBO.setOrgRspCd(orderQueryRsp.getResult_code());
                        baseBnkRspBO.setOrgRspMsg(orderQueryRsp.getReturn_msg());
                    }
                    else if(JudgeUtils.equals(tradeState.toUpperCase(), CpiConstants.WECHAT_PAYERROR)
                            || JudgeUtils.equals(tradeState.toUpperCase(), CpiConstants.WECHAT_REVOKED)) {
                        //订单状态失败
                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                        baseBnkRspBO.setOrgRspCd(orderQueryRsp.getResult_code());
                        baseBnkRspBO.setOrgRspMsg(orderQueryRsp.getReturn_msg());
                    }
                    else {
                        //订单状态处理中
                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_WAIT);
                        baseBnkRspBO.setOrgRspCd(orderQueryRsp.getResult_code());
                        baseBnkRspBO.setOrgRspMsg(tradeState);
                    }
                } else {
                    //订单状态失败
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                    baseBnkRspBO.setOrgRspCd(orderQueryRsp.getErr_code());
                    baseBnkRspBO.setOrgRspMsg(orderQueryRsp.getErr_code_des());
                }
            } else {
                //交易处理失败
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                baseBnkRspBO.setOrgRspCd(orderQueryRsp.getResult_code());
                baseBnkRspBO.setOrgRspMsg(orderQueryRsp.getReturn_msg());
            }
        } catch (Exception e) {
            logger.error("WeChatPayNewApi.orderQuery Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 关闭订单(下单至少5分钟后才能申请该接口)
     */
    public BaseBnkRspBO closeOrder(SubMercCastDO subMercCastDO, EbankOrderDO ebankOrderDO) {
        //初始化请求报文
        CloseOrderReq closeOrderReq = new CloseOrderReq();
        closeOrderReq.setAppid(subMercCastDO.getAppId());
        closeOrderReq.setMch_id(subMercCastDO.getUserId());
        closeOrderReq.setOut_trade_no(ebankOrderDO.getChkKey());
        closeOrderReq.setSub_mch_id(subMercCastDO.getSubMchId());

        //调用微信接口
        BaseBnkRspBO baseBnkRspBO = null;
        try {
            //发送报文
            CloseOrderRsp closeOrderRsp = weChatApi.doSend(closeOrderReq, EnumSource.closeOrder);
            if(JudgeUtils.isNull(closeOrderRsp)) {
                //通讯异常处理
                logger.info("WeChatPayNewApi.closeOrder() 微信关闭订单接口返回空报文");
                return null;
            }
            //返回报文不为空
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(ebankOrderDO.getChkKey());
            baseBnkRspBO.setChkKey(ebankOrderDO.getChkKey());
            //交易处理返回码
            String returnCode = closeOrderRsp.getReturn_code();
            if(JudgeUtils.equals(returnCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                //交易处理成功
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
                baseBnkRspBO.setOrgRspCd(closeOrderRsp.getReturn_code());
                baseBnkRspBO.setOrgRspMsg(closeOrderRsp.getReturn_msg());
            } else {
                //交易处理失败
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                baseBnkRspBO.setOrgRspCd(closeOrderRsp.getErr_code());
                baseBnkRspBO.setOrgRspMsg(closeOrderRsp.getErr_code_des());
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.placeOrder Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 退款申请
     */
    public BaseBnkRspBO refundOrder(SubMercCastDO subMercCastDO, RefundOrderDO refundOrderDO, RefundSuborderDO refundSuborderDO, EbankOrderDO ebankOrderDO) {
        //原订单金额和退款金额，由元转为分
        int totalFee = ebankOrderDO.getOrdAmt().multiply(new BigDecimal(100)).intValue();
        int refundFee = refundSuborderDO.getOrdAmt().multiply(new BigDecimal(100)).intValue();

        //初始化请求报文
        ApplyRefundReq applyRefundReq = new ApplyRefundReq();
        applyRefundReq.setAppid(subMercCastDO.getAppId());
        applyRefundReq.setMch_id(subMercCastDO.getMchId());
        applyRefundReq.setSub_mch_id(subMercCastDO.getSubMchId());
        //商户系统内部的退款单号
        applyRefundReq.setOut_refund_no(refundSuborderDO.getChkKey());
        //商户系统内部订单号，原商户支付订单号
        applyRefundReq.setOut_trade_no(ebankOrderDO.getChkKey());
        //applyRefundReq.setRefund_account(refund_account);
        //applyRefundReq.setRefund_desc(refund_desc);
        //原交易金额
        applyRefundReq.setTotal_fee(totalFee);
        //申请退款金额
        applyRefundReq.setRefund_fee(refundFee);
        //退款币种
        applyRefundReq.setRefund_fee_type(EnumCurrency.valueOf(refundOrderDO.getOrdCcy()));
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, applyRefundReq, true);
        logger.info("WeChatPayNewApi.refundOrder() 微信退款申请接口请求参数 " + reqData);

        //调用微信接口
        BaseBnkRspBO baseBnkRspBO = null;
        try {
            ApplyRefundRsp applyRefundRsp = weChatApi.doSend(applyRefundReq, EnumSource.applyRefund);
            if(JudgeUtils.isNull(applyRefundRsp)) {
                //通讯异常处理
                logger.info("WeChatPayNewApi.refundOrder() 微信退款申请接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, applyRefundRsp, true);
            logger.info("WeChatPayNewApi.refundOrder() 微信退款申请接口接口返回结果 " + reqData);
            //返回报文不为空
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(refundSuborderDO.getChkKey());
            baseBnkRspBO.setChkKey(refundSuborderDO.getChkKey());
            //交易处理返回码
            String returnCode = applyRefundRsp.getReturn_code();
            if(returnCode.toUpperCase().equals(CpiConstants.WECHAT_SUCCESS)) {
                //交易处理成功
                String resultCode = applyRefundRsp.getResult_code();
                if(resultCode.toUpperCase().equals(CpiConstants.WECHAT_SUCCESS)) {
                    //退款申请受理成功
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
                    baseBnkRspBO.setOrgRspCd(applyRefundRsp.getReturn_code());
                    baseBnkRspBO.setOrgRspMsg(applyRefundRsp.getReturn_msg());
                } else {
                    //退款申请失败
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                    baseBnkRspBO.setOrgRspCd(applyRefundRsp.getErr_code());
                    baseBnkRspBO.setOrgRspMsg(applyRefundRsp.getErr_code_des());
                }
            } else {
                //交易处理失败
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                baseBnkRspBO.setOrgRspCd(applyRefundRsp.getReturn_code());
                baseBnkRspBO.setOrgRspMsg(applyRefundRsp.getReturn_msg());
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.applyRefundRsp() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 退款结果查询
     */
    public BaseBnkRspBO refundQuery(SubMercCastDO subMercCastDO, RefundSuborderDO refundSuborderDO) {
        //初始化请求报文
        RefundQueryReq refundQueryReq = new RefundQueryReq();
        refundQueryReq.setAppid(subMercCastDO.getAppId());
        refundQueryReq.setMch_id(subMercCastDO.getMchId());
        refundQueryReq.setSub_mch_id(subMercCastDO.getSubMchId());
        refundQueryReq.setOut_refund_no(refundSuborderDO.getChkKey());
        refundQueryReq.setOut_trade_no(refundSuborderDO.getFndOrdNo());
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, refundQueryReq, true);
        logger.info("WeChatPayNewApi.refundQuery() 微信退款结果查询接口请求参数 " + reqData);

        //调用微信接口
        BaseBnkRspBO baseBnkRspBO = null;
        try {
            //发送报文
            RefundQueryRsp refundQueryRsp = weChatApi.doSend(refundQueryReq, EnumSource.refundQuery);
            if(JudgeUtils.isNull(refundQueryRsp)) {
                //返回报文为空
                logger.info("WeChatPayNewApi.refundQuery() 微信退款结果查询接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, refundQueryRsp, true);
            logger.info("WeChatPayNewApi.refundQuery() 微信退款结果查询接口请求参数 " + reqData);
            //返回报文不为空
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(refundSuborderDO.getChkKey());
            baseBnkRspBO.setChkKey(refundSuborderDO.getChkKey());
            //交易处理返回码
            String returnCode = refundQueryRsp.getReturn_code();
            if(JudgeUtils.equals(returnCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                String resultCode = refundQueryRsp.getResult_code();
                if(JudgeUtils.equals(resultCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                    //业务处理返回码
                    String refund_status_0 = refundQueryRsp.getRefund_status_0();
                    //退款成功
                    if(JudgeUtils.equals(refund_status_0.toUpperCase(), CpiConstants.WECHAT_SUCCESS)) {
                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
                        baseBnkRspBO.setOrgRspCd(refundQueryRsp.getReturn_code());
                        baseBnkRspBO.setOrgRspMsg(refundQueryRsp.getReturn_msg());
                    }
                    //退款失败
                    else if(JudgeUtils.equals(refund_status_0.toUpperCase(), CpiConstants.WECHAT_CHANGE)) {
                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                        baseBnkRspBO.setOrgRspCd(refundQueryRsp.getErr_code());
                        baseBnkRspBO.setOrgRspMsg(refundQueryRsp.getErr_code_des());
                    }
                    //退款处理中
                    else {
                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_WAIT);
                        baseBnkRspBO.setOrgRspCd(refundQueryRsp.getReturn_code());
                        baseBnkRspBO.setOrgRspMsg(refundQueryRsp.getReturn_msg());
                    }
                } else {
                    //交易处理失败，等待下次查询
                }
            } else {
                //交易处理失败，等待下次查询
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.refundQuery() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 解析微信回调结果
     */
    public BaseBnkRspBO wechatFundNotify(String resultData) {
        //微信通知结果
        WeChatOrderNotifyReq weChatOrderNotifyReq = XmlParseUtils.unmarshallerXmlToObject(resultData, WeChatOrderNotifyReq.class);
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, weChatOrderNotifyReq, true);
        logger.info("WeChatPayNewApi.refundQuery() 解析微信回调结果 " + reqData);
        //返回结果
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        //交易处理返回码
        String returnCode = weChatOrderNotifyReq.getReturn_code();
        if(JudgeUtils.equals(returnCode, CpiConstants.WECHAT_SUCCESS)) {
            //业务处理返回码
            String resultCode = weChatOrderNotifyReq.getResult_code();
            if(JudgeUtils.equals(resultCode, CpiConstants.WECHAT_SUCCESS)) {
                //业务处理成功
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
                baseBnkRspBO.setOrgRspCd(resultCode);
                baseBnkRspBO.setChkKey(weChatOrderNotifyReq.getOut_trade_no());
                baseBnkRspBO.setOutOrdNo(weChatOrderNotifyReq.getOut_trade_no());
                baseBnkRspBO.setOrgJrnNo(weChatOrderNotifyReq.getTransaction_id());
            } else {
                //业务处理失败
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                baseBnkRspBO.setOrgRspCd(weChatOrderNotifyReq.getErr_code());
                baseBnkRspBO.setOrgRspMsg(weChatOrderNotifyReq.getErr_code_des());
                baseBnkRspBO.setChkKey(weChatOrderNotifyReq.getOut_trade_no());
                baseBnkRspBO.setOutOrdNo(weChatOrderNotifyReq.getOut_trade_no());
                baseBnkRspBO.setOrgJrnNo(weChatOrderNotifyReq.getTransaction_id());
            }
        } else {
            //交易处理失败
            baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
            baseBnkRspBO.setOrgRspCd(weChatOrderNotifyReq.getReturn_code());
            baseBnkRspBO.setOrgRspMsg(weChatOrderNotifyReq.getReturn_msg());
        }
        return baseBnkRspBO;
    }

    /**
     * 微信充值订单对账文件下载
     */
    public String getWeChatFundCheckFile(String chkFilPath, LocalDate checkDate) {
        //检查在指定日期内是否有成功的微信交易订单
        int weChatSuccOrderNum = ebankOrderDao.getSuccessfulEbankpayOrderNum(CpiConstants.WECHAT, checkDate);
        if(weChatSuccOrderNum <= 0) {
            logger.info("WeChatPayNewApi.getWeChatFundCheckFile() 在指定日期内没有成功的微信交易订单");
            return null;
        }

        //下载对账文件参数
        DownloadBillReq downloadBillReq = new DownloadBillReq();
        //平台appid
        downloadBillReq.setAppid(weChatProperties.getAppId());
        //对账文件日期
        String checkDateStr = DateTimeUtils.formatLocalDate(checkDate);
        downloadBillReq.setBill_date(checkDateStr);
        //账单类型
        //ALL，返回当日所有订单信息
        //SUCCESS，返回当日成功支付的订单
        //REFUND，返回当日退款订单
        //RECHARGE_REFUND，返回当日充值退款订单
        downloadBillReq.setBill_type(EnumBillType.SUCCESS);
        //平台在微信的商户号
        downloadBillReq.setMch_id(weChatProperties.getPlatMercId());
        //对账文件名
        String checkFileName = null;

        //调用微信接口
        try {
            DownloadBillRsp downloadBillRsp = weChatApi.doSend(downloadBillReq, EnumSource.downloadBill);
            if(JudgeUtils.isNull(downloadBillRsp)) {
                logger.info("WeChatPayNewApi.getWeChatFundCheckFile() 微信下载充值对账单接口返回空报文");
                return null;
            }

            //接口处理失败，会返回 return_code
            String resResult = downloadBillRsp.getReturn_result();
            if(resResult.contains("xml") && resResult.contains("return_code")) {
                Map<String, String> resMap = XmlParseUtils.parseXmlStringToMap(resResult);
                if(JudgeUtils.isNull(resMap)) {
                    logger.info("WeChatPayNewApi.getWeChatFundCheckFile() 下载充值对账文件返回报文解析失败");
                } else {
                    logger.info("WeChatPayNewApi.getWeChatFundCheckFile() 下载充值对账文件失败，原因为" + resMap.get("return_msg"));
                }
                return null;
            }

            //接口处理成功，返回的结果就是对账文件内容的字符串，对账文件为文本表格形式
            String content = downloadBillRsp.getReturn_result();
            if(JudgeUtils.isNull(content)) {
                logger.info("WeChatPayNewApi.getWeChatFundCheckFile() 微信下载充值对账单接口返回文件内容为空");
                return null;
            }

            //创建本地对账临时文件 WeChat_20171117_fund_tmp.txt
            String checkFileTmp = CpiConstants.WECHAT + "_" + checkDateStr + "_fund_tmp.txt";
            File tmpFile = new File(chkFilPath + File.separator + checkFileTmp);
            if(!tmpFile.exists()) {
                tmpFile.createNewFile();
            }

            //将数据写入到临时文件中，临时文件中的数据可以覆盖刷新
            FileWriter fileWriter1 = new FileWriter(tmpFile, false);
            BufferedWriter bufferedWriter1 = new BufferedWriter(fileWriter1);
            bufferedWriter1.write(content);
            bufferedWriter1.flush();
            bufferedWriter1.close();
            fileWriter1.close();

            //创建本地对账正式文件 WeChat_20171117_fund.txt
            checkFileName = CpiConstants.WECHAT + "_" + checkDateStr + "_fund.txt";
            File checkFile = new File(chkFilPath + File.separator + checkFileName);
            if(!checkFile.exists()) {
                checkFile.createNewFile();
            }

            //从临时文件的第一行开始读，文件最后两行去掉
            FileReader fileReader = new FileReader(tmpFile);
            BufferedReader bufferedReader = new BufferedReader(fileReader);

            //将处理后的数据写入正式对账文件
            FileWriter fileWriter = new FileWriter(checkFile);
            BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);

            //读取文件第一行列标题，根据逗号分割，获取长度
            String firstTitle = bufferedReader.readLine();
            int titleLength = firstTitle.split(",").length;
            bufferedWriter.write(firstTitle + "\n");

            //从第二行开始读，若不为空，则写入正式对账文件中
            String detailContent;
            int detailLength;
            while (JudgeUtils.isNotEmpty(detailContent = bufferedReader.readLine())) {
                detailLength = detailContent.split(",").length;
                if(detailLength == titleLength) {
                    if(detailContent.contains("`")) {
                        detailContent = detailContent.replace("`", "");
                    }
                    bufferedWriter.write(detailContent + "\n");
                }
            }

            //关闭输入输出流
            bufferedWriter.flush();
            bufferedWriter.close();
            fileWriter.close();
            bufferedReader.close();
            fileReader.close();
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.getWeChatFundCheckFile() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return checkFileName;
    }


    /**
     * 微信退款订单对账文件下载
     */
    public String getWeChatRefundCheckFile(String chkFilPath, LocalDate checkDate) {
        //检查在指定日期内是否有成功的微信退款订单
        int weChatSuccOrderNum = refundSuborderDao.getSuccessfulRefundOrderNum(CpiConstants.WECHAT, checkDate);
        if(weChatSuccOrderNum <= 0) {
            logger.info("WeChatPayNewApi.getWeChatRefundCheckFile() 在指定日期内没有申请成功的微信退款订单");
            return null;
        }

        //下载对账文件参数
        DownloadBillReq downloadBillReq = new DownloadBillReq();
        //平台appid
        downloadBillReq.setAppid(weChatProperties.getAppId());
        //对账文件日期
        String checkDateStr = DateTimeUtils.formatLocalDate(checkDate);
        downloadBillReq.setBill_date(checkDateStr);
        //账单类型
        //ALL，返回当日所有订单信息
        //SUCCESS，返回当日成功支付的订单
        //REFUND，返回当日退款订单
        //RECHARGE_REFUND，返回当日充值退款订单
        downloadBillReq.setBill_type(EnumBillType.REFUND);
        //平台在微信的商户号
        downloadBillReq.setMch_id(weChatProperties.getPlatMercId());
        //对账文件名
        String checkFileName = null;

        //调用微信接口，下载普通退款对账文件(暂时不下载充值退款对账文件，需求不支持商户充值退款)
        try {
            DownloadBillRsp downloadBillRsp = weChatApi.doSend(downloadBillReq, EnumSource.downloadBill);
            if(JudgeUtils.isNull(downloadBillRsp)) {
                logger.info("WeChatPayNewApi.getWeChatRefundCheckFile() 微信下载退款对账单接口返回空报文");
                return null;
            }

            //接口处理失败，会返回 return_code
            String resResult = downloadBillRsp.getReturn_result();
            if(resResult.contains("xml") && resResult.contains("return_code")) {
                Map<String, String> resMap = XmlParseUtils.parseXmlStringToMap(resResult);
                if(JudgeUtils.isNull(resMap)) {
                    logger.info("WeChatPayNewApi.getWeChatRefundCheckFile() 下载退款对账文件返回报文解析失败");
                } else {
                    logger.info("WeChatPayNewApi.getWeChatRefundCheckFile() 下载退款对账文件失败，原因为" + resMap.get("return_msg"));
                }
                return null;
            }

            //接口处理成功，返回的结果就是对账文件内容的字符串，对账文件为文本表格形式
            String content = downloadBillRsp.getReturn_result();
            if(JudgeUtils.isNull(content)) {
                logger.info("WeChatPayNewApi.getWeChatRefundCheckFile() 微信下载退款对账单接口返回文件内容为空");
                return null;
            }

            //创建本地对账临时文件 WeChat_20171117_fund_tmp.txt
            String checkFileTmp = CpiConstants.WECHAT + "_" + checkDateStr + "_refund_tmp.txt";
            File tmpFile = new File(chkFilPath + File.separator + checkFileTmp);
            if(!tmpFile.exists()) {
                tmpFile.createNewFile();
            }

            //将数据写入到临时文件中，允许覆盖刷新
            FileWriter fileWriter1 = new FileWriter(tmpFile, false);
            BufferedWriter bufferedWriter1 = new BufferedWriter(fileWriter1);
            bufferedWriter1.write(content);
            bufferedWriter1.flush();
            bufferedWriter1.close();
            fileWriter1.close();

            //创建本地对账正式文件 WeChat_20171117_fund.txt
            checkFileName = CpiConstants.WECHAT + "_" + checkDateStr + "_refund.txt";
            File checkFile = new File(chkFilPath + File.separator + checkFileName);
            if(!checkFile.exists()) {
                checkFile.createNewFile();
            }

            //从临时文件的第一行开始读，文件最后两行去掉
            FileReader fileReader = new FileReader(tmpFile);
            BufferedReader bufferedReader = new BufferedReader(fileReader);

            //将处理后的数据写入正式对账文件
            FileWriter fileWriter = new FileWriter(checkFile);
            BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);

            //读取文件第一行列标题，根据逗号分割，获取长度
            String firstTitle = bufferedReader.readLine();
            int titleLength = firstTitle.split(",").length;
            bufferedWriter.write(firstTitle + "\n");

            //从第二行开始读，若不为空，则写入正式对账文件中
            String detailContent = null;
            while (JudgeUtils.isNotEmpty(detailContent = bufferedReader.readLine())) {
                int detailLength = detailContent.split(",").length;
                if(detailLength == titleLength) {
                    if(detailContent.contains("`")) {
                        detailContent = detailContent.replace("`", "");
                    }
                    bufferedWriter.write(detailContent + "\n");
                }
            }

            //关闭输入输出流
            bufferedWriter.flush();
            bufferedWriter.close();
            fileWriter.close();
            bufferedReader.close();
            fileReader.close();
        } catch (Exception e) {
            logger.error("WeChatPayNewApi.getWeChatRefundCheckFile() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return checkFileName;
    }

    /**
     * 微信商户结算明细查询
     * @param subMchId 平台商家在微信的子商户号
     * @param useTag 结算状态 1-已结算； 2-未结算
     * @param offset 偏移量，从第几条开始取数据
     * @param limit 最大记录条数，一般不超过10条
     * @param dateStart 格式为yyyyMMdd，查询未结算记录时，该字段可不传
     * @param dateEnd 格式为yyyyMMdd，查询未结算记录时，该字段可不传
     * @return
     */
    public List<SettlementQueryRsp.WeChatStlDetail> weChatSettlementQuery(String subMchId, int useTag, int offset, int limit, String dateStart, String dateEnd) {
        //请求参数
        SettlementQueryReq settlementQueryReq = new SettlementQueryReq();
        settlementQueryReq.setAppid(weChatProperties.getAppId());
        settlementQueryReq.setMch_id(weChatProperties.getPlatMercId());
        settlementQueryReq.setSub_mch_id(subMchId);
        settlementQueryReq.setUsetag(useTag);
        settlementQueryReq.setOffset(offset);
        settlementQueryReq.setLimit(limit);
        settlementQueryReq.setDate_start(dateStart);
        settlementQueryReq.setDate_end(dateEnd);
        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, settlementQueryReq, true);
        logger.info("WeChatPayNewApi.settlementQuery() 微信查询商户结算明细接口接口请求参数 " + reqData);

        //调用微信接口，查询平台在微信的结算信息明细数据
        try {
            SettlementQueryRsp settlementQueryRsp = weChatApi.doSend(settlementQueryReq, EnumSource.settlementQuery);
            if(JudgeUtils.isNull(settlementQueryRsp)) {
                logger.info("WeChatPayNewApi.settlementQuery() 微信查询商户结算明细接口返回空报文");
                return null;
            }
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, settlementQueryRsp, true);
            logger.info("WeChatPayNewApi.settlementQuery() 微信查询商户结算明细接口接口返回结果 " + reqData);

            //接口返回结果
            String returnData = settlementQueryRsp.getReturn_data();
            Document document = DocumentHelper.parseText(returnData);
            Element root = document.getRootElement();
            String return_code = root.element("return_code").getStringValue();
            if(!("SUCCESS".equals(return_code))){
                logger.info("WeChatPayNewApi.settlementQuery() 微信查询商户结算信息接口处理失败");
                return null;
            }
            String result_code = root.element("result_code").getStringValue();
            Integer record_num = Integer.valueOf(root.element("record_num").getStringValue());
            if(!("SUCCESS".equals(return_code) && "SUCCESS".equals(result_code))) {
                logger.info("WeChatPayNewApi.settlementQuery() 微信查询商户结算信息接口处理失败");
                return null;
            }
            if(record_num == null || record_num == 0) {
                logger.info("WeChatPayNewApi.settlementQuery() 微信查询商户结算信息接口返回结算明细数目为0");
                return null;
            }

            //解析商户结算明细数据
            List<SettlementQueryRsp.WeChatStlDetail> settleDetailList = new ArrayList<>();
            for(int n = 0; n < record_num; n++) {
                Element setteinfo = root.element("setteinfo_" + n);
                SettlementQueryRsp.WeChatStlDetail settleDetailDO = (SettlementQueryRsp.WeChatStlDetail)XmlParseUtils.parseXmlDataToObject(setteinfo, SettlementQueryRsp.WeChatStlDetail.class);
                settleDetailList.add(settleDetailDO);
            }
            return settleDetailList;
        } catch (Exception e) {
            logger.error("WeChatPayNewApi.settlementQuery() Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return null;
    }

    /**
     * 刷卡支付 撤销订单
     * @param subMercCastDO
     * @param ebankOrderDO
     * @return
     */
    public BaseBnkRspBO reverseOrder(SubMercCastDO subMercCastDO, EbankOrderDO ebankOrderDO) {
        //初始化请求报文
        ReverseQrderReq reverseOrderReq = new ReverseQrderReq();
        reverseOrderReq.setAppid(subMercCastDO.getAppId());
        reverseOrderReq.setMch_id(subMercCastDO.getMchId());
        reverseOrderReq.setOut_trade_no(ebankOrderDO.getChkKey());
        reverseOrderReq.setSub_mch_id(subMercCastDO.getSubMchId());

        //调用微信接口
        BaseBnkRspBO baseBnkRspBO = null;
        try {
            //发送报文
            ReverseQrderRsp reverseOrderRsp = weChatApi.doSend(reverseOrderReq, EnumSource.reverseOrder);
            if(JudgeUtils.isNull(reverseOrderRsp)) {
                //通讯异常处理
                logger.info("WeChatPayNewApi.closeOrder() 微信刷卡支付，撤销订单接口返回空报文");
                return null;
            }
            //返回报文不为空
            baseBnkRspBO = new BaseBnkRspBO();
            baseBnkRspBO.setOutOrdNo(ebankOrderDO.getChkKey());
            baseBnkRspBO.setChkKey(ebankOrderDO.getChkKey());
            //交易处理返回码
            String returnCode = reverseOrderRsp.getReturn_code();
            String recall = reverseOrderRsp.getRecall();
            if(JudgeUtils.equals(returnCode.toUpperCase(), CpiConstants.WECHAT_SUCCESS) && JudgeUtils.equals(recall,"N")) {
                //交易处理成功
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
                baseBnkRspBO.setOrgRspCd(reverseOrderRsp.getReturn_code());
                baseBnkRspBO.setOrgRspMsg(reverseOrderRsp.getReturn_msg());
            } else {
                //交易处理失败
                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
                baseBnkRspBO.setOrgRspCd(reverseOrderRsp.getErr_code());
                baseBnkRspBO.setOrgRspMsg(reverseOrderRsp.getErr_code_des());
            }
        } catch (Exception e) {
            //通讯异常处理
            logger.error("WeChatPayNewApi.reverseOrder Exception : ", e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

}
