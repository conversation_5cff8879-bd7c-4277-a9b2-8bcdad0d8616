package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.dto.OrderResultRspDTO;
import com.hisun.lemon.cpi.dto.RefundReqDTO;
import com.hisun.lemon.cpi.dto.RefundRspDTO;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.service.IFundPollParamService;
import com.hisun.lemon.cpi.service.IRefundService;
import com.hisun.lemon.cpi.thread.EbankRefundQueryThread;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.lock.DistributedLocker;
import io.swagger.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退款服务
 */
@RestController
@RequestMapping("/cpi/refund")
@Api(tags="RefundController", description="退款服务")
public class RefundController extends BaseController {
    @Resource
    IRefundService refundService;
    @Resource
    private IFundPollParamService fundPollParamService;
    @Resource
    private DistributedLocker distributedLocker;

    @Resource
    private TaskExecutor taskExecutor;

    @ApiOperation(value="退款申请", notes="退款申请")
    @ApiResponse(code = 200, message = "退款申请结果")
    @PostMapping("/order")
    public GenericRspDTO<RefundRspDTO> createOrder(@Validated @RequestBody GenericDTO<RefundReqDTO> genericDTO) {
        return refundService.createOrder(genericDTO);
    }

    @ApiOperation(value="退款结果查询", notes="退款结果查询")
    @ApiResponse(code = 200, message = "退款结果")
    @GetMapping("/result")
    public GenericRspDTO<OrderResultRspDTO> orderQuery(@Validated @ApiParam(name = "ordNo", value = "订单号", required = true) @RequestParam(value = "ordNo") String ordNo) {
        return refundService.queryOrder(ordNo);
    }

    @ApiOperation(value="未支付订单撤销申请", notes="未支付订单撤销申请")
    @ApiResponse(code = 200, message = "未支付订单撤销申请结果")
    @PostMapping("/closeOrder")
    public GenericRspDTO<RefundRspDTO> closeOrder(@Validated @RequestBody GenericDTO<RefundReqDTO> genericDTO) {
        return refundService.closeOrder(genericDTO);
    }

    @ApiOperation(value="向银行发起退款请求", notes="向银行发起退款请求")
    @ApiResponse(code = 200, message = "向银行发起退款请求")
    @PostMapping("/refundByTime")
    public void refundByTime() {
        refundService.refundByTime();
    }

    @ApiOperation(value="退款订单异步调起银行接口，查询处理结果", notes="退款订单异步调起银行接口，查询处理结果")
    @ApiResponse(code = 200, message = "退款订单异步调起银行接口，查询处理结果")
    @PostMapping("/refundAdditionalOrder")
    public void refundAdditionalOrder() {
        List<FundPollParamDO> list = fundPollParamService.queryAllRefund();
        if(CollectionUtils.isNotEmpty(list)) {
            for (FundPollParamDO fundPollParamDO : list) {
                //异步调起银行查询接口
                taskExecutor.execute(new EbankRefundQueryThread(refundService, distributedLocker,fundPollParamDO));
            }
        }
    }
}
