/*
 * @ClassName IAccRefundCfgDao
 * @Description 
 * @version 1.0
 * @Date 2017-09-11 11:32:56
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.AccRefundCfgDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAccRefundCfgDao extends BaseDao<AccRefundCfgDO> {

    /**
     * 查询银行对账配置信息
     * @param rutCorg 路径合作机构
     * @param corpBusTyp 合作业务类型
     * @param corpBusSubTyp 合作业务子类型
     */
    AccRefundCfgDO getAccCfgDO(@Param("rutCorg") String rutCorg, @Param("corpBusTyp") String corpBusTyp, @Param("corpBusSubTyp") String corpBusSubTyp);
}