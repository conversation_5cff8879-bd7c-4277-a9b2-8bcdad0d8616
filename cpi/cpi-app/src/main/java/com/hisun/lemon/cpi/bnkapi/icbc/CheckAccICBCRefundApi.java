package com.hisun.lemon.cpi.bnkapi.icbc;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.bnkapi.icbc.fastpay.FastpayICBCApi;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.icbc.IAccIcbcRefundDao;
import com.hisun.lemon.cpi.entity.AccControlDO;
import com.hisun.lemon.cpi.entity.icbc.AccIcbcRefundDO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 工行快捷退款业务对账操作 API
 */
@Component
public class CheckAccICBCRefundApi {

    private static final Logger logger = LoggerFactory.getLogger(CheckAccICBCRefundApi.class);

    @Resource
    private IAccIcbcRefundDao accIcbcRefundDao;

    @Resource
    private FastpayICBCApi fastpayICBCApi;

    /**
     * 1.1 工行快捷退款对账：获取对账文件
     */
    public String getCheckFile(String chkFilPath, LocalDate chkDt) throws Exception{

        return fastpayICBCApi.getCheckFile(chkFilPath,chkDt);
    }

    /**
     * 1.2 工行快捷退款对账：解析对账文件，对账明细批量入库
     */
    public void batchInsertCheckDO(List<Object> checkDOList, AccControlDO accControlDO) {
        logger.debug("==================工行快捷退款对账，解析对账文件==================");

        //step1：根据对账批次号查询一条记录，判断该批次是否已插入对账明细数据
        String chkBatNo = accControlDO.getChkBatNo();
        int totalNum = accIcbcRefundDao.getAccIcbcRefundDOCount(chkBatNo);
        if (totalNum > 0) {
            logger.error("该批次已录入银行对账明细，请重新确认，对账批次号: " + chkBatNo);
            throw new LemonException(CpiMsgCd.IMPORT_CHECK_FILE_REPEATED.getMsgCd());
        }

        //step2：批次插入银行对账明细
        AccIcbcRefundDO accIcbcRefundDO = null;
        List<AccIcbcRefundDO> refundDOList = new ArrayList<>();
        Integer filTotCnt = 0;
        BigDecimal filTotAmt = BigDecimal.ZERO;
        BigDecimal amtFormat = BigDecimal.ZERO;
        String chkId = null;

        if (CollectionUtils.isNotEmpty(checkDOList)) {
            //循环获取文件中，交易类型为 3-退款(快捷退款) 的对账明细，并统计对账总金额和总笔数
            for (Object checkAccDO : checkDOList) {
                accIcbcRefundDO = (AccIcbcRefundDO)checkAccDO;
                if(StringUtils.equals(CpiConstants.ICBC_CHECK_TX_TYP_REFUND, accIcbcRefundDO.getTrxType())) {
                    chkId = IdGenUtils.generateIdWithDateTime("CPI", 6);
                    amtFormat = accIcbcRefundDO.getPayAmt().divide(new BigDecimal(100));
                    accIcbcRefundDO.setPayAmt(amtFormat);
                    accIcbcRefundDO.setChkId(chkId);
                    refundDOList.add(accIcbcRefundDO);
                    filTotAmt = filTotAmt.add(amtFormat);
                    filTotCnt++;
                }
            }
            //记录数大于200，分批量进行插入
            if (refundDOList.size() > CpiConstants.MAX_BATCH_NUM) {
                List<AccIcbcRefundDO> tmpList = new ArrayList<>();
                for (AccIcbcRefundDO checkAccDO : refundDOList) {
                    if (tmpList.size() == CpiConstants.MAX_BATCH_NUM) {
                        accIcbcRefundDao.batchInsertCheckDO(tmpList);
                        tmpList.clear();
                    }
                    tmpList.add(checkAccDO);
                }
                if (tmpList.size() > 0) {
                    accIcbcRefundDao.batchInsertCheckDO(tmpList);
                }
            } else {
                //记录数小于200，直接批量插入
                if(CollectionUtils.isNotEmpty(refundDOList)) {
                    accIcbcRefundDao.batchInsertCheckDO(refundDOList);
                }
            }
        }
        //更新文件总笔数和总金额
        accControlDO.setFilTotAmt(filTotAmt);
        accControlDO.setFilTotCnt(filTotCnt);
    }

    /**
     * 1.3 工行快捷退款对账：获取银行对账明细 List
     * @param chkBatNo 对账批次号
     * @param txSts 银行明细交易状态
     * @param beginNum 从表中第几行开始取值
     * @param countNum 最大读取多少行记录
     */
    public List<AccIcbcRefundDO> getAccDOList(String chkBatNo, String txSts, Integer beginNum, Integer countNum) {
        return accIcbcRefundDao.getAccIcbcRefundDOList(chkBatNo, txSts, beginNum, countNum);
    }

    /**
     * 1.4 工行快捷退款对账：更新银行明细的对账状态
     * @param checkKey 银行明细的主键，取机构的交易流水号
     * @param txSts 银行明细的交易状态
     * @param chkSts 银行明细的对账状态
     */
    public void updateAccDOChkSts(String checkKey, String txSts, String chkSts) {
        accIcbcRefundDao.updateAccIcbcRefundDOChkSts(checkKey, txSts, chkSts);
    }

}
