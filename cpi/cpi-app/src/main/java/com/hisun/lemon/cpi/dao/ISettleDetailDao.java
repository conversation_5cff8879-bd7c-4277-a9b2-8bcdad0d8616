package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.SettleDetailDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 平台在外部机构的结算明细数据dao类
 */
@Mapper
public interface ISettleDetailDao extends BaseDao<SettleDetailDO> {
    /**
     * 根据结算机构、结算日期、结算标志，查询表中是否已存有数据
     */
    SettleDetailDO getSettleDetail(@Param("rutCorg")String rutCorg, @Param("settleDate")LocalDate settleDate, @Param("stlFlg")String stlFlg);

    /**
     * 查询截止指定结算日期已结算总金额
     */
    BigDecimal getSettleFee(@Param("settleDate")LocalDate settleDate, @Param("stlFlg")String stlFlg);

    /**
     * 查询截止指定结算日期未结算总金额
     */
    BigDecimal getUnSettleFee(@Param("settleDate")LocalDate settleDate, @Param("updateDate")LocalDate updateDate, @Param("stlFlg")String stlFlg);

    /**
     * 根据结算日期查询最近一条已结算数据
     */
    SettleDetailDO getSettleByDate(@Param("settleDate")LocalDate settleDate, @Param("stlFlg")String stlFlg);
}