/*
 * @ClassName FundPollParamDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-17 17:12:13
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import com.hisun.lemon.framework.idgenerate.auto.AutoIdGen;

import java.time.LocalDateTime;

public class FundPollParamDO extends BaseDO {
    /**
     * @Fields qryCycId 主键
     */
    @AutoIdGen(key="QRY_CYC_ID", prefix="CP")
    private String qryCycId;
    /**
     * @Fields rutCorg 路径合作机构
     */
    private String rutCorg;
    /**
     * @Fields copBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields copBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields effFlg 生效失效标识，0失效，1生效
     */
    private String effFlg;
    /**
     * @Fields ordPriLev 订单优先级
     */
    private Integer ordPriLev;
    /**
     * @Fields cycOrdCnt 调起笔数
     */
    private Integer cycOrdCnt;
    /**
     * @Fields beforeTime 查找多长时间之前的订单(秒)
     */
    private Integer beforeTime;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getQryCycId() {
        return qryCycId;
    }

    public void setQryCycId(String qryCycId) {
        this.qryCycId = qryCycId;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getEffFlg() {
        return effFlg;
    }

    public void setEffFlg(String effFlg) {
        this.effFlg = effFlg;
    }

    public Integer getOrdPriLev() {
        return ordPriLev;
    }

    public void setOrdPriLev(Integer ordPriLev) {
        this.ordPriLev = ordPriLev;
    }

    public Integer getCycOrdCnt() {
        return cycOrdCnt;
    }

    public void setCycOrdCnt(Integer cycOrdCnt) {
        this.cycOrdCnt = cycOrdCnt;
    }

    public Integer getBeforeTime() {
        return beforeTime;
    }

    public void setBeforeTime(Integer beforeTime) {
        this.beforeTime = beforeTime;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}