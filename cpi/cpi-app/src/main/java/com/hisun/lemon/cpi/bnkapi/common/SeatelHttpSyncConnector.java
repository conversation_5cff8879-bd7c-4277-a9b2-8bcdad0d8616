package com.hisun.lemon.cpi.bnkapi.common;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import com.hisun.lemon.cpi.bnkapi.alipay.AlipayConstants;
import org.apache.commons.io.IOUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hisun.channel.connector.http.AbstractHttpConnector;

@Component
public class SeatelHttpSyncConnector {

	protected static final Logger logger = LoggerFactory.getLogger(AbstractHttpConnector.class);
	@Autowired
	HttpConnectionManager connManager;

	/**
	 * httpGet
	 * 
	 * @param url
	 *            路径
	 * @param jsonParam
	 *            参数
	 * @return XML格式数据
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public <T> T httpHandle(List<NameValuePair> params, Object bean, Class<T> rspType) {
		return httpHandle(params, bean, false, rspType);
	}

	/**
	 * get请求
	 * 
	 * @param url
	 *            URL地址
	 * @param jsonParam
	 *            参数
	 * @param noNeedResponse
	 *            不需要返回结果
	 * @return XML格式数据
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public <T> T httpHandle(List<NameValuePair> params, Object bean, boolean noNeedResponse, Class<T> rspType) {
		try {
			CloseableHttpClient httpClient = connManager.getHttpClient();
			// //创建Get请求
			// //转换为键值对
			// String str = EntityUtils.toString(new
			// UrlEncodedFormEntity(params, Consts.UTF_8));
			// String url = URLDecoder.decode(AlipayConfig.URL, "UTF-8");
			// HttpGet httpGet = new HttpGet(url+"?"+str);
			// CloseableHttpResponse resultResponse =
			// httpClient.execute(httpGet);

			// 创建Post请求
			HttpPost httpPost = new HttpPost(AlipayConstants.API_URL);
			httpPost.setHeader("content-type",
					"application/x-www-form-urlencoded; text/html; charset=" + AlipayConstants.INPUT_CHARSET);
			httpPost.setEntity(new UrlEncodedFormEntity(params));
			CloseableHttpResponse resultResponse = httpClient.execute(httpPost);

			/** 请求发送成功，并得到响应 **/
			if (resultResponse.getStatusLine().getStatusCode() == 200) {
				try {
					/** 读取服务器返回过来的XML字符串数据 **/
					if (noNeedResponse) {
						return null;
					}
					InputStream in = resultResponse.getEntity().getContent();
					String rspStr = IOUtils.toString(in, "GBK");
					logger.info("服务器返回数据"+rspStr);
					in.close();
					// 先转换成map
					Map<String, String> rspMap = XmlUtil.xmlToMap(rspStr);
					if ("F".equals(rspMap.get("is_success"))) {
						logger.error("请检查请求数据，网络请求返回错误信息The server response is" + rspMap.get("error"));
					}
					/** 把XML字符串转换成对象 **/
					if ("T".equals(rspMap.get("is_success"))) {
						T result = (T) XmlUtil.toBean(rspStr, rspType);
						return (T) result;
					}
				} catch (Exception e) {
					logger.error("网络请求数据解析失败:", e);
				} finally {
					resultResponse.close();
				}
			} else {
				logger.error("网络请求数据解析失败:", resultResponse.getStatusLine().getStatusCode());				
			}
		} catch (Exception e) {
            logger.error("SeatelHttpSyncConnector.httpHandle() Exception : ", e);
		}
		return null;
	}
}
