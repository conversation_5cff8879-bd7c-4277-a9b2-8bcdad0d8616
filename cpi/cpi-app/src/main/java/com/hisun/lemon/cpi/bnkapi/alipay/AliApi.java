package com.hisun.lemon.cpi.bnkapi.alipay;

import javax.annotation.Resource;

import com.hisun.channel.client.IChannelClient;
import com.hisun.lemon.cpi.alipay.ebankpay.AliPayEnumCommon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hisun.channel.data.Request;
import com.hisun.channel.data.Response;
import com.hisun.lemon.cpi.bnkapi.common.SeatelChannelClient;

@Component
public class AliApi {

	@Resource
	private SeatelChannelClient seatelChannelClient;

	@Autowired
	private IChannelClient channelClient;
	@SuppressWarnings("unchecked")
	public <T> T doSend(Object obj, AliPayEnumCommon.EnumSource source) {
		if (source == null) {
			return null;
		}
		Request request = new Request();
		request.setRoute("ALIPAY");
		request.setBusiType(source.name());
		request.setSource(source.name());
		request.setTarget(obj);
		request.setRequestId("1");
		Response response = channelClient.request(request);
		Object t =  response.getResult();
		return (T)t;
	}
}
