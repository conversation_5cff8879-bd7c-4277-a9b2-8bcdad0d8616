package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.EbankOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Mapper
public interface IEbankOrderDao extends BaseDao<EbankOrderDO> {

    /**
     * 查询充值订单
     */
    EbankOrderDO selectByFndOrdNo(String fndOrdNo);

    /**
     * 查询符合补单条件的订单
     */
    List<EbankOrderDO> selectAddtionalOrder(@Param("ebankOrderDO") EbankOrderDO ebankOrderDO,@Param("num") int num,@Param("maxFundQueryCnt") int maxFundQueryCnt);

    /**
     * 根据对账键值，查询快捷订单信息
     */
    EbankOrderDO selectEbankpayOrderByChkKey(String chkKey);

    /**
     * 根据对账键值，更新快捷订单的对账状态、对账时间等
     */
    void updateEbankpayOrderByChkKey(EbankOrderDO ebankOrderDO);

    /**
     * 查询我方ORD_STS='S1'(交易成功)、CHK_STS='5'(存疑)的订单，(ORD_DT)订单日期小于对账日期
     */
    List<EbankOrderDO> getEbankpayOrderListByChkStsDoubt(@Param("ordSts")String ordSts, @Param("chkSts")String chkSts, @Param("chkDt")LocalDate chkDt);

    /**
     * 将我方ORD_STS='S1'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'，(ORD_DT)订单日期小于对账日期
     */
    void updateEbankpayOrderByChkStsDoubt(@Param("ordSts")String ordSts, @Param("oldChkSts")String oldChkSts, @Param("newChkSts")String newChkSts, @Param("chkDt")LocalDate chkDt, @Param("chkTm")LocalTime chkTm);

    /**
     * 查询我方ORD_STS='S'、CHK_STS='0'(未对账)的订单，订单日期等于对账日期
     */
    List<EbankOrderDO> getEbankpayOrderListByChkStsNotstart(@Param("ordSts")String ordSts, @Param("chkSts")String chkSts, @Param("chkDt")LocalDate chkDt);

    /**
     * 将我方ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)，订单日期等于对账日期
     */
    void updateEbankpayOrderByChkStsNotstart(@Param("ordSts")String ordSts, @Param("oldChkSts")String oldChkSts, @Param("newChkSts")String newChkSts, @Param("chkDt")LocalDate chkDt, @Param("chkTm")LocalTime chkTm);

    /**
     * 根据机构将我方ORD_STS='S'(交易成功)、订单日期等于对账日期的数据查询出来
     */
    List<EbankOrderDO> getEbankpayOrderListByChkFilDt(@Param("rutCorpOrg")String rutCorpOrg, @Param("chkFilDt")LocalDate chkFilDt, @Param("beginNum")Integer beginNum, @Param("countNum")Integer countNum);

    /**
     * 根据路由机构查询指定日期内是否有成功的网银订单
     */
    Integer getSuccessfulEbankpayOrderNum(@Param("rutCorpOrg")String rutCorpOrg, @Param("chkFilDt")LocalDate chkFilDt);
}