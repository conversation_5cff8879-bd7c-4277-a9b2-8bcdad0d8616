package com.hisun.lemon.cpi.entity.alipay;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class AccAlipayEbankpayRefundDO extends BaseDO {

    /**
     * @Fields PartnerTransactionId 平台请求支付宝生成的订单号
     */
    private String partnerTransactionId;
    /**
     * @Fields TransactionId 支付宝平台的唯一订单号(支付宝退款单号)
     */
    private String transactionId;
    /**
     * @Fields TransactionAmount 交易金额
     */
    private BigDecimal transactionAmount;
    /**
     * @Fields ChargeAmount 手续费
     */
    private BigDecimal chargeAmount;
    /**
     * @Fields Currency 货币
     */
    private String currency;
    /**
     * @Fields paymentTime 支付时间
     */
    private String paymentTime;
    /**
     * @Fields TransactionType 交易类型：PAYMENT REVERSE REFUND
     */
    private String transactionType;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields secondaryMerchantIndustry 行业分类标识符
     */
    private String secondaryMerchantIndustry;
    /**
     * @Fields secondaryMerchantName 二级商户名称
     */
    private String secondaryMerchantName;
    /**
     * @Fields operatorName 操作员名字
     */
    private String operatorName;
    /**
     * @Fields orderScene 订单场景交易场景 Barcode mode | shopQrCode
     */
    private String orderScene;
    /**
     * @Fields TransCurrency  货币列表
     */
    private String transCurrency;
    /**
     * @Fields transForexRate 交易费率
     */
    private String transForexRate;

    /**
     * @Fields transAmount
     */
    private String transAmount;

    /**
     * @Fields chkBatNo 对账批次号
     */
    private String chkBatNo;
    /**
     * @Fields chkFilDt 对账文件日期
     */
    private LocalDate chkFilDt;
    /**
     * @Fields chkFilNm 对账文件名称
     */
    private String chkFilNm;
    /**
     * @Fields chkSts 对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑
     */
    private String chkSts;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields rutCorg 路径合作机构
     */
    private String rutCorg;
    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields chkId 对账明细序号
     */
    private String chkId;


    public String getPartnerTransactionId() {
        return partnerTransactionId;
    }

    public void setPartnerTransactionId(String partnerTransactionId) {
        this.partnerTransactionId = partnerTransactionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(String paymentTime) {
        this.paymentTime = paymentTime;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


    public String getSecondaryMerchantIndustry() {
        return secondaryMerchantIndustry;
    }

    public void setSecondaryMerchantIndustry(String secondaryMerchantIndustry) {
        this.secondaryMerchantIndustry = secondaryMerchantIndustry;
    }

    public String getSecondaryMerchantName() {
        return secondaryMerchantName;
    }

    public void setSecondaryMerchantName(String secondaryMerchantName) {
        this.secondaryMerchantName = secondaryMerchantName;
    }

    public String getTransForexRate() {
        return transForexRate;
    }

    public void setTransForexRate(String transForexRate) {
        this.transForexRate = transForexRate;
    }

    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    public LocalDate getChkFilDt() {
        return chkFilDt;
    }

    public void setChkFilDt(LocalDate chkFilDt) {
        this.chkFilDt = chkFilDt;
    }

    public String getChkFilNm() {
        return chkFilNm;
    }

    public void setChkFilNm(String chkFilNm) {
        this.chkFilNm = chkFilNm;
    }

    public String getChkSts() {
        return chkSts;
    }

    public void setChkSts(String chkSts) {
        this.chkSts = chkSts;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getChkId() {
        return chkId;
    }

    public void setChkId(String chkId) {
        this.chkId = chkId;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public BigDecimal getChargeAmount() {
        return chargeAmount;
    }

    public void setChargeAmount(BigDecimal chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOrderScene() {
        return orderScene;
    }

    public void setOrderScene(String orderScene) {
        this.orderScene = orderScene;
    }

    public String getTransCurrency() {
        return transCurrency;
    }

    public void setTransCurrency(String transCurrency) {
        this.transCurrency = transCurrency;
    }

    public String getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(String transAmount) {
        this.transAmount = transAmount;
    }
}