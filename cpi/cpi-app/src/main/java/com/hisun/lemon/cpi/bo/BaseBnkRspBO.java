package com.hisun.lemon.cpi.bo;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by <PERSON>ui on 2017/7/10.
 */
public class BaseBnkRspBO {
    /**
     * 合作机构交易日期
     * @mbggenerated
     */
    private LocalDate orgOrdDt;

    /**
     * 合作机构交易时间
     * @mbggenerated
     */
    private LocalTime orgOrdTm;
    /**
     * 与机构的订单号
     */
    private String outOrdNo;
    /**
     * 机构流水号
     */
    private String orgJrnNo;
    /**
     * 机构返回值
     */
    private String orgRspCd;
    /**
     * 机构返回信息
     */
    private String orgRspMsg;
    /**
     * 交易结果描述，S成功，F失败
     */
    private String txFlg;
    /**
     * 对账键值
     */
    private String chkKey;

    public LocalDate getOrgOrdDt() {
        return orgOrdDt;
    }

    public void setOrgOrdDt(LocalDate orgOrdDt) {
        this.orgOrdDt = orgOrdDt;
    }

    public LocalTime getOrgOrdTm() {
        return orgOrdTm;
    }

    public void setOrgOrdTm(LocalTime orgOrdTm) {
        this.orgOrdTm = orgOrdTm;
    }

    public String getOutOrdNo() {
        return outOrdNo;
    }

    public void setOutOrdNo(String outOrdNo) {
        this.outOrdNo = outOrdNo;
    }

    public String getOrgJrnNo() {
        return orgJrnNo;
    }

    public void setOrgJrnNo(String orgJrnNo) {
        this.orgJrnNo = orgJrnNo;
    }

    public String getOrgRspCd() {
        return orgRspCd;
    }

    public void setOrgRspCd(String orgRspCd) {
        this.orgRspCd = orgRspCd;
    }

    public String getOrgRspMsg() {
        return orgRspMsg;
    }

    public void setOrgRspMsg(String orgRspMsg) {
        this.orgRspMsg = orgRspMsg;
    }

    public String getChkKey() {
        return chkKey;
    }

    public void setChkKey(String chkKey) {
        this.chkKey = chkKey;
    }

    public String getTxFlg() {
        return txFlg;
    }

    public void setTxFlg(String txFlg) {
        this.txFlg = txFlg;
    }
}
