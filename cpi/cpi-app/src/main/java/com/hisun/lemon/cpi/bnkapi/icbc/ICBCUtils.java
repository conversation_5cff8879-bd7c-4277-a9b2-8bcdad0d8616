package com.hisun.lemon.cpi.bnkapi.icbc;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.external.biz.base64Coder.Base64Coder;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.security.MessageDigest;

@Component
public class ICBCUtils {

    private static final Logger logger = LoggerFactory.getLogger(ICBCUtils.class);

    /**
     * 读取文件内容
     * @param fileName
     * @return
     */
    public static String readMD5File(String fileName) {
        String data = "";
        InputStreamReader fr = null;
        BufferedReader br = null;
        try {
            fr = new InputStreamReader(new FileInputStream(fileName), "GBK");
            br = new BufferedReader(fr);
            String buff = "";
            while ((buff = br.readLine()) != null) {
                data = data + buff;
            }
        } catch (Exception e) {
            logger.error("ICBCUtils.readMD5File Exception:" , e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    logger.error("ICBCUtils.readMD5File Exception:" , e);
                }
            }
            if (fr != null) {
                try {
                    fr.close();
                } catch (Exception e) {
                    logger.error("ICBCUtils.readMD5File Exception:" , e);
                }
            }
        }

        return data;
    }

    /**
     * MD5加密文件内容
     * @param fileName
     * @return
     */
    public static String getFileMD5(String fileName) {
        MessageDigest MD5 = null;
        BufferedInputStream bis = null;
        String result = null;
        try {
            MD5 = MessageDigest.getInstance("MD5");
            byte[] md5Result = null;
            byte[] tmpBuff = new byte[2048];

            bis = new BufferedInputStream(new FileInputStream(fileName));
            int len = 0;
            while ((len = bis.read(tmpBuff)) != -1) {
                MD5.update(tmpBuff, 0, len);
            }
            md5Result = MD5.digest();
            result = new String(Base64Coder.encode(md5Result));
        } catch (Exception e) {
            logger.error("ICBCUtils.getFileMD5 Exception:" , e);
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (Exception e) {
                    logger.error("ICBCUtils.getFileMD5 Exception:" , e);
                }
            }
        }
        return result;
    }
}
