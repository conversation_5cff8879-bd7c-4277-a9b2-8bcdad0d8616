package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.bnkapi.upi.pos.PosPayApi;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.*;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.IPosService;
import com.hisun.lemon.cpi.service.IPosTransaction;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2017/10/16 16:44
 */
@Transactional
@Service
public class PosServiceImpl implements IPosService {

    @Resource
    IRouteDao routeDao;
    @Resource
    IPosTransaction posTransaction;
    @Resource
    IPosPreAuDao posPreAuDao;
    @Resource
    IPosPreAuJrnDao posPreAuJrnDao;
    @Resource
    PosPayApi posPayApi;
    @Resource
    IPosOrderDao posOrderDao;
    @Resource
    IFundOrderDao fundOrderDao;

    private static final Logger logger = LoggerFactory.getLogger(PosServiceImpl.class);

    @Override
    public GenericRspDTO<PosBalanceRspDTO> balanceQuery(GenericDTO<PosBalanceReqDTO> genericDTO) {
        PosBalanceReqDTO posBalanceReqDTO = genericDTO.getBody();

        PosBalanceRspDTO posBalanceRspDTO = new PosBalanceRspDTO();

        //模拟返回
        posBalanceRspDTO.setCrdNo(posBalanceReqDTO.getCrdNo());
        posBalanceRspDTO.setTrmNo(posBalanceRspDTO.getTrmNo());
        posBalanceRspDTO.setBalanceAmt(BigDecimal.valueOf(200));
        return GenericRspDTO.newInstance(CpiMsgCd.SUCCESS.getMsgCd(), posBalanceRspDTO);
    }

    @Override
    public GenericRspDTO<PosPayRspDTO> payment(GenericDTO<PosPayReqDTO> genericDTO) {
        //获取请求对象
        PosPayReqDTO posPayReqDTO = genericDTO.getBody();

        //返回值
        PosPayRspDTO posPayRspDTO = new PosPayRspDTO();

        //充值订单号
        String funOrdNo = IdGenUtils.generateIdWithDateTime("FUD_ORD_NO", "CPI", 6);
        try {
            //Step1:预处理
            if (!CorpBusTyp.POS.equals(posPayReqDTO.getCorpBusTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd(), posPayRspDTO);
            }
            if (!CorpBusSubTyp.POS_PAY.equals(posPayReqDTO.getCorpBusSubTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), posPayRspDTO);
            }

            //Step2:路由选择
            RouteDO routeDO = new RouteDO();
            routeDO.setCorpBusTyp(posPayReqDTO.getCorpBusTyp().getType());
            routeDO.setCorpBusSubTyp(posPayReqDTO.getCorpBusSubTyp().getType());
            routeDO.setCrdAcTyp(CpiConstants.CARD_UNKNOWN);
            routeDO.setCrdCorpOrg("UPI");
            RouteDO rspRouteInfo = routeDao.queryRouteInfo(routeDO, posPayReqDTO.getOrdAmt());
            if (null == rspRouteInfo || StringUtils.isBlank(rspRouteInfo.getRutCorpOrg())) {
                return GenericRspDTO.newInstance(CpiMsgCd.ROUTE_INFO_IS_NOT_FUND.getMsgCd(), posPayRspDTO);
            }

            //Step3:登记充值订单表和网银订单表
            PosOrderDO posOrderDO = posTransaction.createPosOrder(posPayReqDTO, rspRouteInfo, funOrdNo);

            //Step4:调用银行下单接口
            CpiMsgCd bnkMsgCd = CpiMsgCd.SUCCESS;
            bnkMsgCd = this.posPay(posOrderDO);
            BeanUtils.copyProperties(posPayRspDTO, posPayReqDTO);
            posPayRspDTO.setOrdNo(funOrdNo);

            return GenericRspDTO.newInstance(bnkMsgCd.getMsgCd(), posPayRspDTO);
        } catch (LemonException e) {
            logger.error("PosServiceImpl.payment LemonException : ", e);
            return GenericRspDTO.newInstance(e.getMsgCd(), posPayRspDTO);
        } catch (Exception e) {
            logger.error("PosServiceImpl.payment Exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), posPayRspDTO);
        }
    }

    @Override
    public GenericRspDTO<PosPreAuRspDTO> preAuthorization(GenericDTO<PosPreAuReqDTO> genericDTO) {
        //获取请求对象
        PosPreAuReqDTO posPreAuReqDTO = genericDTO.getBody();

        //返回值
        PosPreAuRspDTO posPreAuRspDTO = new PosPreAuRspDTO();

        //充值订单号
        String preJrnNo = IdGenUtils.generateIdWithDateTime("PRE_JRN_NO", "CPI", 6);
        try {
            //Step1:预处理
            if (!CorpBusTyp.POS.equals(posPreAuReqDTO.getCorpBusTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd(), posPreAuRspDTO);
            }
            //判断是否为子类型
            boolean isSubTyp = CorpBusSubTyp.POS_PRE_AU.equals(posPreAuReqDTO.getCorpBusSubTyp())||CorpBusSubTyp.POS_PRE_AU_CANCEL.equals(posPreAuReqDTO.getCorpBusSubTyp())
                    ||CorpBusSubTyp.POS_PRE_AU_FINISH.equals(posPreAuReqDTO.getCorpBusSubTyp())||CorpBusSubTyp.POS_PRE_AU_FINISH_CANCEL.equals(posPreAuReqDTO.getCorpBusSubTyp());

            if (!isSubTyp) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), posPreAuRspDTO);
            }

            //Step2:路由选择
            RouteDO routeDO = new RouteDO();
            routeDO.setCorpBusTyp(posPreAuReqDTO.getCorpBusTyp().getType());
            routeDO.setCorpBusSubTyp(posPreAuReqDTO.getCorpBusSubTyp().getType());
            routeDO.setCrdAcTyp(CpiConstants.CARD_UNKNOWN);
            routeDO.setCrdCorpOrg("UPI");
            RouteDO rspRouteInfo = routeDao.queryRouteInfo(routeDO, posPreAuRspDTO.getOrdAmt());
            if (null == rspRouteInfo || StringUtils.isBlank(rspRouteInfo.getRutCorpOrg())) {
                return GenericRspDTO.newInstance(CpiMsgCd.ROUTE_INFO_IS_NOT_FUND.getMsgCd(), posPreAuRspDTO);
            }

            //Step3:登记预授权流水表
            PosPreAuJrnDO posPreAuJrnDO = new PosPreAuJrnDO();
            posPreAuJrnDO.setPreJrnNo(preJrnNo);
            posPreAuJrnDO.setCrdCorpOrg(rspRouteInfo.getCrdCorpOrg());
            posPreAuJrnDO.setRutCorpOrg(rspRouteInfo.getRutCorpOrg());
            posPreAuJrnDO.setCrdAcTyp(CpiConstants.CARD_UNKNOWN);
            BeanUtils.copyProperties(posPreAuJrnDO,posPreAuReqDTO);
            posPreAuJrnDO.setCorpBusTyp(posPreAuReqDTO.getCorpBusTyp().getType());
            posPreAuJrnDO.setCorpBusSubTyp(posPreAuReqDTO.getCorpBusSubTyp().getType());
            posTransaction.insertCardProtJrnDO(posPreAuJrnDO);

            //Step4:调用银行下单接口
            CpiMsgCd bnkMsgCd = CpiMsgCd.SUCCESS;
            switch (posPreAuReqDTO.getCorpBusSubTyp()){
                case POS_PRE_AU:
                    bnkMsgCd = this.preAuthorizationPay(posPreAuJrnDO);
                    break;
                case POS_PRE_AU_CANCEL:
                case POS_PRE_AU_FINISH:
                case POS_PRE_AU_FINISH_CANCEL:
                    bnkMsgCd = this.preAuthorizationHandle(posPreAuJrnDO);
                    break;
                default:
                    return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), posPreAuRspDTO);
            }
            BeanUtils.copyProperties(posPreAuRspDTO, posPreAuReqDTO);
            posPreAuRspDTO.setJrnNo(preJrnNo);

            return GenericRspDTO.newInstance(bnkMsgCd.getMsgCd(), posPreAuRspDTO);
        } catch (LemonException e) {
            logger.error("PosServiceImpl.payment LemonException : ", e);
            return GenericRspDTO.newInstance(e.getMsgCd(), posPreAuRspDTO);
        } catch (Exception e) {
            logger.error("PosServiceImpl.payment Exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), posPreAuRspDTO);
        }
    }


    /**
     * 银行快捷支付内部处理
     */
    private CpiMsgCd posPay(PosOrderDO posOrderDO) {
        CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
        BaseBnkRspBO baseBnkRspBO = null;
        switch (posOrderDO.getRutCorpOrg()) {
            case CpiConstants.UPI:
                baseBnkRspBO = posPayApi.posPay(posOrderDO);
                if (JudgeUtils.isNull(baseBnkRspBO)) {
                    //银行错
                    return CpiMsgCd.BANK_COMMUNICATION_EXCEPTION;
                }else {
                    //TODO:错误码转义
                }
                break;
            default:
                //不支持银行
                return CpiMsgCd.ROUTE_NOT_FIND;
        }

        //更新pos订单表表和订单表的状态
        PosOrderDO updatePosOrderDO = new PosOrderDO();
        updatePosOrderDO.setSubOrdNo(posOrderDO.getSubOrdNo());
        updatePosOrderDO.setAcDt(LemonUtils.getAccDate());

        //更新订单表状态
        FundOrderDO updateFundOrder = new FundOrderDO();
        updateFundOrder.setFudOrdNo(posOrderDO.getFndOrdNo());
        updateFundOrder.setAcDt(LemonUtils.getAccDate());
        posOrderDO.setAcDt(LemonUtils.getAccDate());
        posOrderDO.setOrdSts(CpiConstants.ORD_FAIL);
        updateFundOrder.setOrdSts(CpiConstants.ORD_FAIL);
        if (JudgeUtils.isSuccess(msgCd.getMsgCd())) {
            posOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateFundOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateFundOrder.setOrdSuccDt(DateTimeUtils.getCurrentLocalDate());
            updateFundOrder.setOrdSuccTm(DateTimeUtils.getCurrentLocalTime());
        }
        //更新订单表和快捷表
        BeanUtils.copyProperties(posOrderDO, baseBnkRspBO);
        posOrderDO.setOrgOrdNo(baseBnkRspBO.getOutOrdNo());
        posOrderDao.update(posOrderDO);
        fundOrderDao.update(updateFundOrder);
        return msgCd;
    }

    /**
     * 预授权交易
     * @param posPreAuJrnDO 预授权流水信息
     * @return
     */
    private CpiMsgCd preAuthorizationPay(PosPreAuJrnDO posPreAuJrnDO){
        BaseBnkRspBO baseBnkRspBO = null;
        CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
        switch (posPreAuJrnDO.getRutCorpOrg()) {
            case CpiConstants.UPI:
                baseBnkRspBO = posPayApi.preAuthorizationPay(posPreAuJrnDO);
                if (JudgeUtils.isNull(baseBnkRspBO)) {
                    //银行错
                    return CpiMsgCd.BANK_COMMUNICATION_EXCEPTION;
                } else {
                    //TODO:错误码转义
                }
                break;
            default:
                //不支持银行
                return CpiMsgCd.ROUTE_NOT_FIND;
        }
        //更新流水表
        PosPreAuJrnDO updatePreAuJrn = new PosPreAuJrnDO();
        updatePreAuJrn.setPreJrnNo(posPreAuJrnDO.getPreJrnNo());
        updatePreAuJrn.setOrgRspCd(baseBnkRspBO.getOrgRspCd());
        updatePreAuJrn.setOrgRspMsg(baseBnkRspBO.getOrgRspMsg());
        updatePreAuJrn.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
        posPreAuJrnDao.update(updatePreAuJrn);
        //插入预授权信息
        PosPreAuDO posPreAuDO = new PosPreAuDO();
        BeanUtils.copyProperties(posPreAuDO,posPreAuJrnDO);
        posPreAuDO.setOrdSts(CpiConstants.POS_PRE_AU);
        String preAuNo = IdGenUtils.generateIdWithDateTime("PRE_AU_NO", "CPI", 6);
        posPreAuDO.setPreAuNo(preAuNo);
        posPreAuDao.insert(posPreAuDO);
        return msgCd;
    }

    /**
     * 预授权后处理交易包括（预授权撤销，预授权完成，预授权完成撤销）
     * @param posPreAuJrnDO 预授权流水信息
     * @return
     */
    private CpiMsgCd preAuthorizationHandle(PosPreAuJrnDO posPreAuJrnDO){
        BaseBnkRspBO baseBnkRspBO = null;
        CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
        //查询最近的一笔预授权交易
        PosPreAuDO pCondition = new PosPreAuDO();
        pCondition.setCrdCorpOrg(posPreAuJrnDO.getCrdCorpOrg());
        pCondition.setRutCorpOrg(posPreAuJrnDO.getRutCorpOrg());
        pCondition.setCrdNoEnc(posPreAuJrnDO.getCrdNoEnc());
        pCondition.setOrdAmt(posPreAuJrnDO.getOrdAmt());
        //不同的类型，查询的状态值不一样
        if(CorpBusSubTyp.POS_PRE_AU_CANCEL.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
            pCondition.setOrdSts(CpiConstants.POS_PRE_AU);
        }else if(CorpBusSubTyp.POS_PRE_AU_FINISH.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
            pCondition.setOrdSts(CpiConstants.POS_PRE_AU);
        }else if(CorpBusSubTyp.POS_PRE_AU_FINISH_CANCEL.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
            pCondition.setOrdSts(CpiConstants.POS_PRE_AU_FINISH);
        }else {
            return CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR;
        }

        PosPreAuDO posPreAuDO = posPreAuDao.selectByCondition(pCondition);
        //判断是否查到预授权记录
        if(JudgeUtils.isNull(posPreAuDO)){
            msgCd = CpiMsgCd.POS_RECORD_NOT_FOUND;
            return msgCd;
        }
        switch (posPreAuJrnDO.getRutCorpOrg()) {
            case CpiConstants.UPI:
                //对不同的处理，调用不同银行组件
                if(CorpBusSubTyp.POS_PRE_AU_CANCEL.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
                    baseBnkRspBO = posPayApi.preAuthorizationCancel(posPreAuJrnDO,posPreAuDO.getAutCd());
                }else if(CorpBusSubTyp.POS_PRE_AU_FINISH.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
                    baseBnkRspBO = posPayApi.preAuthorizationFinish(posPreAuJrnDO,posPreAuDO.getAutCd());
                }else if(CorpBusSubTyp.POS_PRE_AU_FINISH_CANCEL.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
                    baseBnkRspBO = posPayApi.preAuthorizationFinishCancel(posPreAuJrnDO,posPreAuDO.getAutCd());
                }else {
                    return CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR;
                }
                if (JudgeUtils.isNull(baseBnkRspBO)) {
                    //银行错
                    return CpiMsgCd.BANK_COMMUNICATION_EXCEPTION;
                } else {
                    //TODO:错误码转义
                }
                break;
            default:
                //不支持银行
                return CpiMsgCd.ROUTE_NOT_FIND;
        }
        //更新流水表
        PosPreAuJrnDO updatePreAuJrn = new PosPreAuJrnDO();
        updatePreAuJrn.setPreJrnNo(posPreAuJrnDO.getPreJrnNo());
        updatePreAuJrn.setOrgRspCd(baseBnkRspBO.getOrgRspCd());
        updatePreAuJrn.setOrgRspMsg(baseBnkRspBO.getOrgRspMsg());
        updatePreAuJrn.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
        posPreAuJrnDao.update(updatePreAuJrn);
        //更新预授权信息
        PosPreAuDO updatePosPreAuDO = new PosPreAuDO();
        updatePosPreAuDO.setPreAuNo(posPreAuDO.getPreAuNo());
        //不同类型，更新的状态不一样
        if(CorpBusSubTyp.POS_PRE_AU_CANCEL.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
            updatePosPreAuDO.setOrdSts(CpiConstants.POS_PRE_AU_CANCEL);
        }else if(CorpBusSubTyp.POS_PRE_AU_FINISH.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
            updatePosPreAuDO.setOrdSts(CpiConstants.POS_PRE_AU_FINISH);
        }else if(CorpBusSubTyp.POS_PRE_AU_FINISH_CANCEL.getType().equals(posPreAuJrnDO.getCorpBusSubTyp())){
            updatePosPreAuDO.setOrdSts(CpiConstants.POS_PRE_AU_FINISH_CANCEL);
        }else {
            return CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR;
        }

        posPreAuDao.update(updatePosPreAuDO);
        return msgCd;
    }


}
