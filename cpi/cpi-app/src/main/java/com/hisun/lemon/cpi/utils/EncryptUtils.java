package com.hisun.lemon.cpi.utils;

import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.CommonEncryptReqDTO;
import com.hisun.lemon.cmm.dto.CommonEncryptRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 银行卡号、证件号加密和解密工具类
 */
@Component
public class EncryptUtils {

    @Resource
    private CmmServerClient cmmServerClient;

    /**
     * 加密和解密
     * 加解密类型 encrypt:加密  decrypt:解密
     */
    public String encrypt(String data, String type) {
        //发送请求
        GenericDTO<CommonEncryptReqDTO> genericDTO = new GenericDTO<>();
        CommonEncryptReqDTO commonEncryptReqDTO = new CommonEncryptReqDTO();
        commonEncryptReqDTO.setData(data);
        commonEncryptReqDTO.setType(type);
        genericDTO.setBody(commonEncryptReqDTO);

        //返回结果
        GenericRspDTO<CommonEncryptRspDTO> genericRspDTO = cmmServerClient.encrypt(genericDTO);
        CommonEncryptRspDTO commonEncryptRspDTO = genericRspDTO.getBody();
        if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
            LemonException.throwBusinessException(genericRspDTO.getMsgCd());
        }
        return commonEncryptRspDTO.getData();
    }
}
