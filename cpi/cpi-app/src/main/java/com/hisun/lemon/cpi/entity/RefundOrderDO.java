/*
 * @ClassName RefundOrderDO
 * @Description 
 * @version 1.0
 * @Date 2017-09-15 20:37:35
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class RefundOrderDO extends BaseDO {
    /**
     * @Fields rfdOrdNo 内部订单号
     */
    private String rfdOrdNo;
    /**
     * @Fields ordDt 订单日期
     */
    private LocalDate ordDt;
    /**
     * @Fields ordTm 订单时间
     */
    private LocalTime ordTm;
    /**
     * @Fields ordCcy 币种
     */
    private String ordCcy;
    /**
     * @Fields ordAmt 订单金额
     */
    private BigDecimal ordAmt;
    /**
     * @Fields ordSts 订单状态，W1：等待充值，S1：交易成功，E1：交易逾期，F1：交易失败
     */
    private String ordSts;
    /**
     * @Fields acDt 记账日期
     */
    private LocalDate acDt;
    /**
     * @Fields ordSuccDt 订单成功日期
     */
    private LocalDate ordSuccDt;
    /**
     * @Fields ordSuccTm 订单成功时间
     */
    private LocalTime ordSuccTm;
    /**
     * @Fields userTyp 用户类型，U：用户，B：商户
     */
    private String userTyp;
    /**
     * @Fields userId 用户ID
     */
    private String userId;
    /**
     * @Fields mblNo 手机号
     */
    private String mblNo;
    /**
     * @Fields crdCorpOrg 资金合作机构号
     */
    private String crdCorpOrg;
    /**
     * @Fields crdAcTyp 银行卡类型，C贷记卡，D借记卡
     */
    private String crdAcTyp;
    /**
     * @Fields crdNoEnc 加密银行卡号
     */
    private String crdNoEnc;
    /**
     * @Fields crdUsrNm 用户姓名
     */
    private String crdUsrNm;
    /**
     * @Fields crdNoLast 银行卡后4位
     */
    private String crdNoLast;
    /**
     * @Fields idTyp 证件类型
     */
    private String idTyp;
    /**
     * @Fields idNoEnc 加密证件号码
     */
    private String idNoEnc;
    /**
     * @Fields bnkPsnFlg 对公对私标志，B对公，C对私
     */
    private String bnkPsnFlg;
    /**
     * @Fields rutCorpOrg 路径合作机构号
     */
    private String rutCorpOrg;
    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields autoPayFlg 自动付款标志，A：自动，M：人工
     */
    private String autoPayFlg;
    /**
     * @Fields agrPayDt 协议付款日
     */
    private LocalDate agrPayDt;
    /**
     * @Fields rfdTime 退款时间
     */
    private LocalDateTime rfdTime;
    /**
     * @Fields rfdFailCount 退款失败次数
     */
    private Integer rfdFailCount;
    /**
     * @Fields orgRmk 银行摘要
     */
    private String orgRmk;
    /**
     * @Fields apprSts 审批状态，0：不需要审批，1：待审批，2：审批付款，3：审批拒绝
     */
    private String apprSts;
    /**
     * @Fields apprOprId 审批操作员
     */
    private String apprOprId;
    /**
     * @Fields apprDt 审批日期
     */
    private LocalDate apprDt;
    /**
     * @Fields apprTm 审批时间
     */
    private LocalTime apprTm;
    /**
     * @Fields apprRefuseRsn 审批拒绝原因
     */
    private String apprRefuseRsn;
    /**
     * @Fields ntfSts 通知状态，W：待通知，S：通知成功，F：通知失败（可重发），E：通知失败（不可重发）
     */
    private String ntfSts;
    /**
     * @Fields ntfDt 通知日期
     */
    private LocalDate ntfDt;
    /**
     * @Fields ntfTm 通知时间
     */
    private LocalTime ntfTm;
    /**
     * @Fields ntfRspCd 通知返回码
     */
    private String ntfRspCd;
    /**
     * @Fields ntfRspMsg 通知返回信息
     */
    private String ntfRspMsg;
    /**
     * @Fields reqOrdNo 请求订单号
     */
    private String reqOrdNo;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields reqOrdDt 请求方订单日期
     */
    private LocalDate reqOrdDt;
    /**
     * @Fields reqOrdTm 请求方订单时间
     */
    private LocalTime reqOrdTm;
    /**
     * @Fields innerFlag 内部模块标识
     */
    private String innerFlag;

    public String getRfdOrdNo() {
        return rfdOrdNo;
    }

    public void setRfdOrdNo(String rfdOrdNo) {
        this.rfdOrdNo = rfdOrdNo;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public String getOrdCcy() {
        return ordCcy;
    }

    public void setOrdCcy(String ordCcy) {
        this.ordCcy = ordCcy;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public LocalDate getOrdSuccDt() {
        return ordSuccDt;
    }

    public void setOrdSuccDt(LocalDate ordSuccDt) {
        this.ordSuccDt = ordSuccDt;
    }

    public LocalTime getOrdSuccTm() {
        return ordSuccTm;
    }

    public void setOrdSuccTm(LocalTime ordSuccTm) {
        this.ordSuccTm = ordSuccTm;
    }

    public String getUserTyp() {
        return userTyp;
    }

    public void setUserTyp(String userTyp) {
        this.userTyp = userTyp;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getCrdNoLast() {
        return crdNoLast;
    }

    public void setCrdNoLast(String crdNoLast) {
        this.crdNoLast = crdNoLast;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNoEnc() {
        return idNoEnc;
    }

    public void setIdNoEnc(String idNoEnc) {
        this.idNoEnc = idNoEnc;
    }

    public String getBnkPsnFlg() {
        return bnkPsnFlg;
    }

    public void setBnkPsnFlg(String bnkPsnFlg) {
        this.bnkPsnFlg = bnkPsnFlg;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getAutoPayFlg() {
        return autoPayFlg;
    }

    public void setAutoPayFlg(String autoPayFlg) {
        this.autoPayFlg = autoPayFlg;
    }

    public LocalDate getAgrPayDt() {
        return agrPayDt;
    }

    public void setAgrPayDt(LocalDate agrPayDt) {
        this.agrPayDt = agrPayDt;
    }

    public LocalDateTime getRfdTime() {
        return rfdTime;
    }

    public void setRfdTime(LocalDateTime rfdTime) {
        this.rfdTime = rfdTime;
    }

    public Integer getRfdFailCount() {
        return rfdFailCount;
    }

    public void setRfdFailCount(Integer rfdFailCount) {
        this.rfdFailCount = rfdFailCount;
    }

    public String getOrgRmk() {
        return orgRmk;
    }

    public void setOrgRmk(String orgRmk) {
        this.orgRmk = orgRmk;
    }

    public String getApprSts() {
        return apprSts;
    }

    public void setApprSts(String apprSts) {
        this.apprSts = apprSts;
    }

    public String getApprOprId() {
        return apprOprId;
    }

    public void setApprOprId(String apprOprId) {
        this.apprOprId = apprOprId;
    }

    public LocalDate getApprDt() {
        return apprDt;
    }

    public void setApprDt(LocalDate apprDt) {
        this.apprDt = apprDt;
    }

    public LocalTime getApprTm() {
        return apprTm;
    }

    public void setApprTm(LocalTime apprTm) {
        this.apprTm = apprTm;
    }

    public String getApprRefuseRsn() {
        return apprRefuseRsn;
    }

    public void setApprRefuseRsn(String apprRefuseRsn) {
        this.apprRefuseRsn = apprRefuseRsn;
    }

    public String getNtfSts() {
        return ntfSts;
    }

    public void setNtfSts(String ntfSts) {
        this.ntfSts = ntfSts;
    }

    public LocalDate getNtfDt() {
        return ntfDt;
    }

    public void setNtfDt(LocalDate ntfDt) {
        this.ntfDt = ntfDt;
    }

    public LocalTime getNtfTm() {
        return ntfTm;
    }

    public void setNtfTm(LocalTime ntfTm) {
        this.ntfTm = ntfTm;
    }

    public String getNtfRspCd() {
        return ntfRspCd;
    }

    public void setNtfRspCd(String ntfRspCd) {
        this.ntfRspCd = ntfRspCd;
    }

    public String getNtfRspMsg() {
        return ntfRspMsg;
    }

    public void setNtfRspMsg(String ntfRspMsg) {
        this.ntfRspMsg = ntfRspMsg;
    }

    public String getReqOrdNo() {
        return reqOrdNo;
    }

    public void setReqOrdNo(String reqOrdNo) {
        this.reqOrdNo = reqOrdNo;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public LocalDate getReqOrdDt() {
        return reqOrdDt;
    }

    public void setReqOrdDt(LocalDate reqOrdDt) {
        this.reqOrdDt = reqOrdDt;
    }

    public LocalTime getReqOrdTm() {
        return reqOrdTm;
    }

    public void setReqOrdTm(LocalTime reqOrdTm) {
        this.reqOrdTm = reqOrdTm;
    }

    public String getInnerFlag() {
        return innerFlag;
    }

    public void setInnerFlag(String innerFlag) {
        this.innerFlag = innerFlag;
    }
}