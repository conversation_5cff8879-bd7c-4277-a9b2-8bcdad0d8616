/*
 * @ClassName CopAgcyBizDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class CopAgcyBizDO extends BaseDO {
    /**
     * @Fields orgBusId 主键
     */
    private String orgBusId;
    /**
     * @Fields corpOrgId 合作机构编号
     */
    private String corpOrgId;
    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields busEffFlg 业务生效标志，0失效，1生效
     */
    private String busEffFlg;
    /**
     * @Fields creOprId 创建人ID
     */
    private String creOprId;
    /**
     * @Fields updOprId 修改人ID
     */
    private String updOprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getOrgBusId() {
        return orgBusId;
    }

    public void setOrgBusId(String orgBusId) {
        this.orgBusId = orgBusId;
    }

    public String getCorpOrgId() {
        return corpOrgId;
    }

    public void setCorpOrgId(String corpOrgId) {
        this.corpOrgId = corpOrgId;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getBusEffFlg() {
        return busEffFlg;
    }

    public void setBusEffFlg(String busEffFlg) {
        this.busEffFlg = busEffFlg;
    }

    public String getCreOprId() {
        return creOprId;
    }

    public void setCreOprId(String creOprId) {
        this.creOprId = creOprId;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}