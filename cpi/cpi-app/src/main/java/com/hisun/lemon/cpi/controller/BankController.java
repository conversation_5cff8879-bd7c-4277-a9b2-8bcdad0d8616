package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.dto.TransferAcledaReqDTO;
import com.hisun.lemon.cpi.dto.TransferAcledaRspDTO;
import com.hisun.lemon.cpi.service.IBankService;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/cpi/bank")
@Api(tags="BankController", description="银行服务")
public class BankController {

    @Resource
    IBankService iBankService;

    @ApiOperation(value="ACLEDA银行转账请求处理", notes="ACLEDA银行转账请求处理")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @ApiResponse(code = 200, message = "ACLEDA转账sessionId,paymentTokenId")
    @PostMapping("/transfer/acleda")
    public GenericRspDTO<TransferAcledaRspDTO> transferAcledaOpen(@Validated @RequestBody GenericDTO<TransferAcledaReqDTO> genericDTO) {
        return iBankService.transferAcledaOpen(genericDTO);
    }

}
