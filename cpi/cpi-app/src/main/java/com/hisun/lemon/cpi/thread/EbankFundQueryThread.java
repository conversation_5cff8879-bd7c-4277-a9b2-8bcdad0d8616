package com.hisun.lemon.cpi.thread;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.service.IEbankpayService;
import com.hisun.lemon.framework.lock.DistributedLocker;
import org.springframework.stereotype.Component;

/**
 * Created by Rui on 2017/7/17.
 */
@Component
public class EbankFundQueryThread implements Runnable {

    private IEbankpayService ebankpayService;

    private FundPollParamDO fundPollParamDO;

    private DistributedLocker distributedLocker;

    public EbankFundQueryThread(){
        super();
    }

    public EbankFundQueryThread(IEbankpayService ebankPayService, DistributedLocker distributedLocker, FundPollParamDO fundPollParamDO){
        this.ebankpayService = ebankPayService;
        this.fundPollParamDO = fundPollParamDO;
        this.distributedLocker = distributedLocker;
    }

    @Override
    public void run() {
        try {
            //锁名
            String lockName = "QRY_"+fundPollParamDO.getRutCorg()+"_"+fundPollParamDO.getCorpBusSubTyp();
            //释放锁的时间（异常情况下最长的释放锁的时间，单位秒）
            int leaseTime = 5*60;
            //获取应用锁的时间，时间设置必须短，否则影响后面的线程获取锁（单位秒）
            int waitTime = 10;
            distributedLocker.lock(lockName, leaseTime, waitTime, () -> {
                ebankpayService.additionalOrder(fundPollParamDO);
                return null;
            });
        } catch (Exception e) {
            throw new LemonException();
        }
    }
}
