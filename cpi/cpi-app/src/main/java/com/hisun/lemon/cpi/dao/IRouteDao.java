package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.CopAgcyInfoDO;
import com.hisun.lemon.cpi.entity.RouteDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by Rui on 2017/7/8.
 */
@Mapper
public interface IRouteDao {

    /**
     * 查询某种业务类型的路由列表
     * @param corpBusTyp
     * @param corpBusSubTyp
     * @return
     */
    List<RouteDO> queryRouteList(@Param("corpBusTyp")String corpBusTyp,@Param("corpBusSubTyp")String corpBusSubTyp, @Param("crdAcTyp")String crdAcTyp);

    /**
     * 查询匹配的路由信息
     * @param routeDO
     * @return
     */
    RouteDO queryRouteInfo(@Param("routeDO") RouteDO routeDO, @Param("ordAmt")BigDecimal ordAmt);

    /**
     * 查询生效的合作资金机构信息
     */
    public List<RouteDO> queryEffOrgInfo(@Param("corpBusTyp")String corpBusTyp, @Param("corpBusSubTyp")String corpBusSubTyp);
}
