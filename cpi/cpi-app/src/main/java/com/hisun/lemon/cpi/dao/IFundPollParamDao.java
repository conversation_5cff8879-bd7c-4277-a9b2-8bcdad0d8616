/*
 * @ClassName IFundPollParamDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IFundPollParamDao extends BaseDao<FundPollParamDO> {

    /**
     * 查询参数
     * @param fundPollParamDO
     * @return
     */
    FundPollParamDO selectFundPollParam(FundPollParamDO fundPollParamDO);

    /**
     * 查询所有生效的支付订单轮询参数
     * @return
     */
    List<FundPollParamDO> queryAllFund();

    /**
     * 查询所有生效的退款订单轮询参数
     * @return
     */
    List<FundPollParamDO> queryAllRefund();

}