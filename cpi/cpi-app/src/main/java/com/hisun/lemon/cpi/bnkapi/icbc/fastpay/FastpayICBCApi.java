package com.hisun.lemon.cpi.bnkapi.icbc.fastpay;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.bnkapi.icbc.ICBCUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.icbc.fastpay.*;
import com.hisun.lemon.cpi.icbc.fastpay.req.*;
import com.hisun.lemon.cpi.icbc.fastpay.rsp.*;
import com.hisun.lemon.cpi.utils.EncryptUtils;
import com.hisun.lemon.cpi.utils.MobileUtils;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Rui on 2017/7/10.
 */
@Component
public class FastpayICBCApi {

    @Resource
    ICBCProperties icbcProperties;

    @Resource
    ICBCApi icbcApi;

    @Resource
    private EncryptUtils encryptUtils;

    @Resource
    private ObjectMapper objectMapper;

    private static final Logger logger = LoggerFactory.getLogger(FastpayICBCApi.class);

    /**
     * 银行预签约发送短信
     * @param cardProtJrnDO
     * @return
     */
    public BaseBnkRspBO preBindCard(CardProtJrnDO cardProtJrnDO){

        String outOrdNo = IdGenUtils.generateIdWithDateTime("OUT_ORD_NO",6);
        String accNo = null;
        String idNo = null;
        //卡号身份证解密
        accNo = encryptUtils.encrypt(cardProtJrnDO.getCrdNoEnc(), CpiConstants.DECRYPT);
        idNo = encryptUtils.encrypt(cardProtJrnDO.getIdNoEnc(), CpiConstants.DECRYPT);
        String mobile = MobileUtils.getMobile(cardProtJrnDO.getMblNo());

        //报文头
        HeadReq headReq = new HeadReq();
        headReq.setCorpcode(icbcProperties.getCorpcode());
        headReq.setIntfcode(ICBCEnumCommon.EnumIntFcode.PRECONTRACT);
        if(JudgeUtils.isEmpty(accNo)){
            LemonException.throwBusinessException(CpiMsgCd.ACC_NO_IS_NULL.getMsgCd());
        }
        //卡号后两位
        headReq.setTailNumber(accNo.substring(accNo.length()-2));
        headReq.setAppcode(icbcProperties.getAppcode());
        headReq.setTrxcode(icbcProperties.getTrxcode());

        //公共字段
        PubReq pubMsg = new PubReq();
        pubMsg.setBizCode(icbcProperties.getBizCode());
        pubMsg.setCorpNo(icbcProperties.getCorpcode());
        pubMsg.setProtocolNo(icbcProperties.getProtocolNoCredit());//预签约和签约时上送：99
        pubMsg.setTrxDate(DateTimeUtils.getCurrentDateStr());//交易日期
        pubMsg.setTrxTime(DateTimeUtils.getCurrentTimeStr());//交易时间
        pubMsg.setTrxSerno(outOrdNo);//流水号
        pubMsg.setVersion(icbcProperties.getVersion());//版本号

        //预签约请求报文
        PreContractReq.PreContractReqMsg.PreContractTranReq preContractTranReq = new PreContractReq.PreContractReqMsg.PreContractTranReq();
        preContractTranReq.setAccName(cardProtJrnDO.getCrdUsrNm());
        preContractTranReq.setAccNo(accNo);

        //短信模板
//        preContractTranReq.setCheckMsg("1");
//        preContractTranReq.setIdNo(idNo);
        if(CpiConstants.ID_TYP_ID.equals(cardProtJrnDO.getIdTyp())){
            preContractTranReq.setIdType(ICBCEnumCommon.EnumIdType.IDENTITY_CARD);//身份证
        } else if(CpiConstants.ID_TYP_PASSPORT.equals(cardProtJrnDO.getIdTyp())) {
            preContractTranReq.setIdType(ICBCEnumCommon.EnumIdType.PASSPORT);//护照
        } else {
            LemonException.throwBusinessException(CpiMsgCd.ID_TYP_IS_NOT_EXPECTED.getMsgCd());
        }
        preContractTranReq.setMobile(mobile);
        preContractTranReq.setIdNo(idNo);

        PreContractReq.PreContractReqMsg preContractReqMsg = new PreContractReq.PreContractReqMsg();
        preContractReqMsg.setPubMsg(pubMsg);
        preContractReqMsg.setTrxCode(ICBCEnumCommon.EnumIntFcode.PRECONTRACT);//预签约
        preContractReqMsg.setPreContractTranReq(preContractTranReq);

        PreContractReq preContractReq = new PreContractReq();
        preContractReq.setHeadReq(headReq);
        preContractReq.setPreContractReqMsg(preContractReqMsg);

        //发送请求报文
        BaseBnkRspBO baseBnkRspBO = null;
        try{
            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, preContractReq, true);
            logger.info("FastpayICBCApi.preBindCard() 银行预签约接口请求参数 " + reqData);
            PreContractRsp rsp = icbcApi.doSend(preContractReq, ICBCEnumCommon.EnumSource.precontract);
            if(JudgeUtils.isNotNull(rsp)) {
                baseBnkRspBO = new BaseBnkRspBO();
                PreContractRsp.PreContractRspMsg.PreContractTranRsp preRsp = rsp.getPreContractRspMsg().getPreContractTranRsp();
                reqData = ObjectMapperHelper.writeValueAsString(objectMapper, preRsp, true);
                logger.info("FastpayICBCApi.preBindCard() 银行预签约接口返回结果 " + reqData);
                if (!icbcProperties.getCorpcode().equals(preRsp.getCorpNo()) && !outOrdNo.equals(preRsp.getTrxSerno())) {
//                    LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    baseBnkRspBO.setOrgRspCd(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    return baseBnkRspBO;
                }
                baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
                baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
                baseBnkRspBO.setOrgRspCd(preRsp.getRetCode());
                baseBnkRspBO.setOrgRspMsg(preRsp.getRetMsg());
            } else {
                logger.error("FastpayICBCApi.preBindCard() bank communication exception");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
        }catch (Exception e){
            //异常处理
            logger.error("FastpayICBCApi.preBindCard Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }

        //模拟码
//        baseBnkRspBO = new BaseBnkRspBO();
//        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//        baseBnkRspBO.setOutOrdNo(outOrdNo);
//        baseBnkRspBO.setOrgRspCd("0");
//        baseBnkRspBO.setOrgRspMsg("成功");

        return baseBnkRspBO;
    }

    /**
     * 银行签约
     * @param cardProtJrnDO
     * @return
     */
    public BaseBnkRspBO bindCard(CardProtJrnDO cardProtJrnDO, String chkNo){
        //外发流水号
        String outOrdNo = IdGenUtils.generateIdWithDateTime("OUT_ORD_NO",6);
        String accNo = null;
        String idNo = null;

        //卡号身份证解密
        try {
            accNo = encryptUtils.encrypt(cardProtJrnDO.getCrdNoEnc(), CpiConstants.DECRYPT);
            idNo = encryptUtils.encrypt(cardProtJrnDO.getIdNoEnc(), CpiConstants.DECRYPT);
        } catch (LemonException e) {
            //异常处理
            logger.error("FastpayICBCApi.bindCard Exception : ",e);
            LemonException.throwBusinessException(e.getMsgCd());
        }
//        String mobile = PhoneNumberUtils.getPhoneNumber(cardProtJrnDO.getMblNo());
        String mobile = MobileUtils.getMobile(cardProtJrnDO.getMblNo());

        HeadReq headReq = new HeadReq();
        headReq.setCorpcode(icbcProperties.getCorpcode());
        headReq.setIntfcode(ICBCEnumCommon.EnumIntFcode.CONTRACT);

        //卡号后两位
        headReq.setTailNumber(accNo.substring(accNo.length()-2));
        headReq.setAppcode(icbcProperties.getAppcode());
        headReq.setTrxcode(icbcProperties.getTrxcode());

        PubReq pubMsg = new PubReq();
        pubMsg.setBizCode(icbcProperties.getBizCode());
        pubMsg.setCorpNo(icbcProperties.getCorpcode());
        pubMsg.setProtocolNo(icbcProperties.getProtocolNoCredit());//预签约和签约时上送：99
        pubMsg.setTrxDate(DateTimeUtils.getCurrentDateStr());//交易日期
        pubMsg.setTrxTime(DateTimeUtils.getCurrentTimeStr());//交易时间
        pubMsg.setTrxSerno(outOrdNo);//流水号
        pubMsg.setVersion(icbcProperties.getVersion());//版本号

        ContractReq.ContractReqMsg.ContractTranReq contractTranReq = new ContractReq.ContractReqMsg.ContractTranReq();
        contractTranReq.setAccName(cardProtJrnDO.getCrdUsrNm());
        contractTranReq.setAccNo(accNo);

        if(CpiConstants.ID_TYP_ID.equals(cardProtJrnDO.getIdTyp())){
            contractTranReq.setIdType(ICBCEnumCommon.EnumIdType.IDENTITY_CARD);//身份证
        }else if(CpiConstants.ID_TYP_PASSPORT.equals(cardProtJrnDO.getIdTyp())){
            contractTranReq.setIdType(ICBCEnumCommon.EnumIdType.PASSPORT);//护照
        }else {
            LemonException.throwBusinessException(CpiMsgCd.ID_TYP_IS_NOT_EXPECTED.getMsgCd());
        }
        contractTranReq.setMobile(mobile);
        contractTranReq.setVerifyCode(chkNo);
        contractTranReq.setIdNo(idNo);

        ContractReq.ContractReqMsg contractReqMsg = new ContractReq.ContractReqMsg();
        contractReqMsg.setPubMsg(pubMsg);
        contractReqMsg.setTrxCode(ICBCEnumCommon.EnumIntFcode.CONTRACT);
        contractReqMsg.setContractTranReq(contractTranReq);

        ContractReq req = new ContractReq();
        req.setHeadReq(headReq);
        req.setContractReqMsg(contractReqMsg);

        BaseBnkRspBO baseBnkRspBO = null;
        try{
            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, req, true);
            logger.info("FastpayICBCApi.bindCard() 银行绑卡请求参数 " + reqData);
            ContractRsp rsp = icbcApi.doSend(req, ICBCEnumCommon.EnumSource.contract);
            if(JudgeUtils.isNotNull(rsp)){
                baseBnkRspBO = new BaseBnkRspBO();
                ContractRsp.ContractRspMsg.ContractTranRsp bnkRsp = rsp.getContractRspMsg().getContractTranRsp();
                reqData = ObjectMapperHelper.writeValueAsString(objectMapper, bnkRsp, true);
                logger.info("FastpayICBCApi.bindCard() 银行绑卡请求接口返回结果 " + reqData);
                if (!icbcProperties.getCorpcode().equals(bnkRsp.getCorpNo()) && !outOrdNo.equals(bnkRsp.getTrxSerno())) {
//                    LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    baseBnkRspBO.setOrgRspCd(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    return baseBnkRspBO;
                }

                String signTime = bnkRsp.getSignTime();
                if(JudgeUtils.isNotBlank(signTime)) {
                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.parseLocalDate(bnkRsp.getSignTime(),"yyyyMMddHHmmss"));
                    baseBnkRspBO.setOrgOrdTm(LocalTime.parse(bnkRsp.getSignTime(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
                } else {
                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
                    baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
                }
                baseBnkRspBO.setOutOrdNo(bnkRsp.getUserId());
                baseBnkRspBO.setOrgRspCd(bnkRsp.getRetCode());
                baseBnkRspBO.setOrgRspMsg(bnkRsp.getRetMsg());
                baseBnkRspBO.setOrgJrnNo(bnkRsp.getTrxSerno());
            } else {
                logger.error("FastpayICBCApi.bindCard() bank communication exception");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
        }catch (Exception e){
            //异常处理
            logger.error("FastpayICBCApi.bindCard Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }

        //模拟报文
//        String signTime = "**************";//YYYYMMDDHH24MISS
//        String userId = "**********";
//        baseBnkRspBO = new BaseBnkRspBO();
//        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.parseLocalDate(signTime,"yyyyMMddHHmmss"));
//        baseBnkRspBO.setOrgOrdTm(LocalTime.parse(signTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
//        baseBnkRspBO.setOutOrdNo(userId);
//        baseBnkRspBO.setOrgRspCd("0");
//        baseBnkRspBO.setOrgRspMsg("成功");

        return baseBnkRspBO;
    }

    /**
     * 银行解约
     * @param cardProtDO
     * @return
     */
    public BaseBnkRspBO unBindCard(CardProtDO cardProtDO){
        //外发流水号
        String outOrdNo = IdGenUtils.generateIdWithDateTime("OUT_ORD_NO",6);
        String userId = cardProtDO.getSignAgrno();
        String crdNo = null;
        try {
            crdNo = encryptUtils.encrypt(cardProtDO.getCrdNoEnc(), CpiConstants.DECRYPT);
        } catch (LemonException e) {
            //异常处理
            logger.error("FastpayICBCApi.bindCard Exception : ",e);
            LemonException.throwBusinessException(e.getMsgCd());
        }

        HeadReq headReq = new HeadReq();
        headReq.setCorpcode(icbcProperties.getCorpcode());
        headReq.setIntfcode(ICBCEnumCommon.EnumIntFcode.RELEASE);

        //协议号后两位
        headReq.setTailNumber(userId.substring(userId.length()-2));
        headReq.setAppcode(icbcProperties.getAppcode());
        headReq.setTrxcode(icbcProperties.getTrxcode());

        PubReq pubReq = new PubReq();
        pubReq.setBizCode(icbcProperties.getBizCode());
        pubReq.setCorpNo(icbcProperties.getCorpcode());
        if(CpiConstants.DEBIT_CARD.equals(cardProtDO.getCrdAcTyp())) {
            pubReq.setProtocolNo(icbcProperties.getProtocolNoDebit());
        } else {
            pubReq.setProtocolNo(icbcProperties.getProtocolNoCredit());
        }
        pubReq.setTrxDate(DateTimeUtils.getCurrentDateStr());//交易日期
        pubReq.setTrxTime(DateTimeUtils.getCurrentTimeStr());//交易时间
        pubReq.setTrxSerno(outOrdNo);//流水号
        pubReq.setVersion(icbcProperties.getVersion());//版本号

        ReleaseReq.ReleaseReqMsg.ReleaseTranReq releaseTranReq = new ReleaseReq.ReleaseReqMsg.ReleaseTranReq();
        releaseTranReq.setAccShtId(crdNo.substring(crdNo.length()-4));
//        releaseTranReq.setCheckMsg("1");
        releaseTranReq.setUserId(userId);

        ReleaseReq.ReleaseReqMsg releaseReqMsg = new ReleaseReq.ReleaseReqMsg();
        releaseReqMsg.setPubReq(pubReq);
        releaseReqMsg.setTrxCode(ICBCEnumCommon.EnumIntFcode.RELEASE);
        releaseReqMsg.setReleaseTranReq(releaseTranReq);

        ReleaseReq req = new ReleaseReq();
        req.setHeadReq(headReq);
        req.setReleaseReqMsg(releaseReqMsg);

        BaseBnkRspBO baseBnkRspBO = null;
        try{
            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, req, true);
            logger.info("FastpayICBCApi.unBindCard() 银行解约接口请求参数 " + reqData);
            ReleaseRsp rsp = icbcApi.doSend(req, ICBCEnumCommon.EnumSource.release);
            if(JudgeUtils.isNotNull(rsp)){
                baseBnkRspBO = new BaseBnkRspBO();
                ReleaseRsp.ReleaseRspMsg.ReleaseTranRsp bnkRsp = rsp.getReleaseRspMsg().getReleaseTranRsp();
                reqData = ObjectMapperHelper.writeValueAsString(objectMapper, bnkRsp, true);
                logger.info("FastpayICBCApi.unBindCard() 银行解约接口返回参数 " + reqData);
                if (!icbcProperties.getCorpcode().equals(bnkRsp.getCorpNo()) && !outOrdNo.equals(bnkRsp.getTrxSerno())) {
//                    LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    baseBnkRspBO.setOrgRspCd(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    return baseBnkRspBO;
                }
                String signTime = bnkRsp.getSignTime();
                if(JudgeUtils.isNotBlank(signTime)) {
                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.parseLocalDate(bnkRsp.getSignTime(),"yyyyMMddHHmmss"));
                    baseBnkRspBO.setOrgOrdTm(LocalTime.parse(bnkRsp.getSignTime(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
                } else {
                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
                    baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
                }
                baseBnkRspBO.setOutOrdNo(bnkRsp.getUserId());
                baseBnkRspBO.setOrgRspCd(bnkRsp.getRetCode());
                baseBnkRspBO.setOrgRspMsg(bnkRsp.getRetMsg());
            } else {
                logger.error("FastpayICBCApi.unBindCard() bank communication exception");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
        }catch (Exception e){
            //异常处理
            logger.error("FastpayICBCApi.unBindCard Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }

        //模拟报文
//        baseBnkRspBO = new BaseBnkRspBO();
//        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//        baseBnkRspBO.setOutOrdNo(userId);
//        baseBnkRspBO.setOrgRspCd("0");
//        baseBnkRspBO.setOrgRspMsg("成功");

        return baseBnkRspBO;
    }

    /**
     * 快捷支付接口
     * @param shortcutOrderDO
     * @return
     */
    public BaseBnkRspBO fastpay(ShortcutOrderDO shortcutOrderDO, String signAgrno){
        String outOrdNo = IdGenUtils.generateIdWithDateTime("OUT_ORD_NO",6);
        String userId = signAgrno;

        HeadReq headReq = new HeadReq();
        headReq.setCorpcode(icbcProperties.getCorpcode());
        headReq.setIntfcode(ICBCEnumCommon.EnumIntFcode.PAYMENT);
        headReq.setTailNumber(userId.substring(userId.length()-2));
        headReq.setAppcode(icbcProperties.getAppcode());
        headReq.setTrxcode(icbcProperties.getTrxcode());

        PubReq pubReq = new PubReq();
        pubReq.setBizCode(icbcProperties.getBizCode());
        pubReq.setCorpNo(icbcProperties.getCorpcode());
        if(CpiConstants.DEBIT_CARD.equals(shortcutOrderDO.getCrdAcTyp())) {
            pubReq.setProtocolNo(icbcProperties.getProtocolNoDebit());
        }else {
            pubReq.setProtocolNo(icbcProperties.getProtocolNoCredit());
        }

        pubReq.setTrxDate(DateTimeUtils.getCurrentDateStr());//交易日期
        pubReq.setTrxTime(DateTimeUtils.getCurrentTimeStr());//交易时间
        pubReq.setTrxSerno(outOrdNo);//流水号
        pubReq.setVersion(icbcProperties.getVersion());//版本号

        List<OrderInfo> orderInfoList = new ArrayList<OrderInfo>();
        OrderInfo orderInfo = new OrderInfo();
        // orderInfo.setGoods("fs");
        orderInfo.setSellerName("seatel");
        // orderInfo.setSellerType("yjtjyty");
        orderInfoList.add(orderInfo);

        PaymentReq.PaymentReqMsg.PaymentTranReq paymentTranReq = new PaymentReq.PaymentReqMsg.PaymentTranReq();
        paymentTranReq.setAmount(shortcutOrderDO.getOrdAmt().multiply(BigDecimal.valueOf(100)).intValue());
        paymentTranReq.setCurrType(ICBCEnumCommon.EnumCurrType.DOLLAR);
        // paymentTranReq.setLocationInfo(locationInfo);
        // paymentTranReq.setLogisticsInfo(logisticsInfo);
        paymentTranReq.setNote(ICBCEnumCommon.EnumNote.CONSUME);
        paymentTranReq.setOrderInfo(orderInfoList);
        paymentTranReq.setOrderNo(outOrdNo);
        paymentTranReq.setTrxPlat("seatel");
        paymentTranReq.setUserId(userId);

        PaymentReq.PaymentReqMsg paymentReqMsg = new PaymentReq.PaymentReqMsg();
        paymentReqMsg.setPubReq(pubReq);
        paymentReqMsg.setTrxCode(ICBCEnumCommon.EnumIntFcode.PAYMENT);
        paymentReqMsg.setPaymentTranReq(paymentTranReq);

        PaymentReq req = new PaymentReq();
        req.setHeadReq(headReq);
        req.setPaymentReqMsg(paymentReqMsg);

        BaseBnkRspBO baseBnkRspBO = null;
        try{
            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, req, true);
            logger.info("FastpayICBCApi.fastpay() 银行支付接口请求参数 " + reqData);
            PaymentRsp rsp = icbcApi.doSend(req, ICBCEnumCommon.EnumSource.payment);
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, rsp, true);
            logger.info("FastpayICBCApi.fastpay() 银行支付接口请求返回参数 " + reqData);
            if(JudgeUtils.isNotNull(rsp)) {
                baseBnkRspBO = new BaseBnkRspBO();
                PaymentRsp.PaymentRspMsg.PaymentTranRsp bnkRsp = rsp.getPaymentRspMsg().getPaymentTranRsp();
                if (!icbcProperties.getCorpcode().equals(bnkRsp.getCorpNo()) && !outOrdNo.equals(bnkRsp.getTrxSerno())) {
//                    LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    baseBnkRspBO.setOrgRspCd(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    return baseBnkRspBO;
                }

                if(JudgeUtils.isNotBlank(bnkRsp.getBankTrxDate())) {
                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.parseLocalDate(bnkRsp.getBankTrxDate(),"yyyyMMdd"));
                } else {
                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
                }
                baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
                baseBnkRspBO.setOrgJrnNo(bnkRsp.getBankSerno());
                baseBnkRspBO.setOutOrdNo(outOrdNo);
                baseBnkRspBO.setOrgRspCd(bnkRsp.getRetCode());
                baseBnkRspBO.setChkKey(outOrdNo);
                baseBnkRspBO.setOrgRspMsg(bnkRsp.getRetMsg());
                if(ICBCEnumCommon.EnumRetStatus.SUCCESS.equals(bnkRsp.getRetStatus())) {
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_SUC);
                } else {
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_FAIL);
                }
            } else {
                logger.error("FastpayICBCApi.fastpay() bank communication exception");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }

            //模拟返回码
//            baseBnkRspBO = new BaseBnkRspBO();
//            String orgJrnNo = "111111";
//            baseBnkRspBO.setOutOrdNo(outOrdNo);
//            baseBnkRspBO.setChkKey(outOrdNo);
//            baseBnkRspBO.setOrgJrnNo(orgJrnNo);
//            baseBnkRspBO.setOrgRspCd("0");
//            baseBnkRspBO.setOrgRspMsg("成功");
//            baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//            baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//            baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_SUC);
//            if(shortcutOrderDO.getOrdAmt().compareTo(BigDecimal.valueOf(9000)) > 0){
//                baseBnkRspBO.setOrgRspCd("4101");
//                baseBnkRspBO.setOrgRspMsg("账户余额不足");
//            }
        } catch(Exception e) {
            //异常处理
            logger.error("FastpayICBCApi.fastpay Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        return baseBnkRspBO;
    }

    /**
     * 快捷退款接口
     * @param refundOrderDO
     * @return
     */
    public BaseBnkRspBO refund(RefundOrderDO refundOrderDO, ShortcutOrderDO shortcutOrderDO, String signAgrno){
        String outOrdNo = IdGenUtils.generateIdWithDateTime("OUT_ORD_NO",6);

        HeadReq headReq = new HeadReq();
        headReq.setCorpcode(icbcProperties.getCorpcode());
        headReq.setIntfcode(ICBCEnumCommon.EnumIntFcode.REFUND);
        headReq.setTailNumber(signAgrno.substring(signAgrno.length()-2));
        headReq.setAppcode(icbcProperties.getAppcode());
        headReq.setTrxcode(icbcProperties.getTrxcode());

        PubReq pubReq = new PubReq();
        pubReq.setBizCode(icbcProperties.getBizCode());
        pubReq.setCorpNo(icbcProperties.getCorpcode());
        if(CpiConstants.DEBIT_CARD.equals(shortcutOrderDO.getCrdAcTyp())) {
            pubReq.setProtocolNo(icbcProperties.getProtocolNoDebit());
        }else {
            pubReq.setProtocolNo(icbcProperties.getProtocolNoCredit());
        }
        pubReq.setTrxDate(DateTimeUtils.getCurrentDateStr());
        pubReq.setTrxTime(DateTimeUtils.getCurrentTimeStr());
        pubReq.setTrxSerno(outOrdNo);
        pubReq.setVersion(icbcProperties.getVersion());

        RefundReq.RefundReqMsg.RefundTranReq refundTranReq = new RefundReq.RefundReqMsg.RefundTranReq();
        refundTranReq.setAmount(refundOrderDO.getOrdAmt().multiply(BigDecimal.valueOf(100)).intValue());
        refundTranReq.setCurrType(ICBCEnumCommon.EnumCurrType.DOLLAR);
        refundTranReq.setNote("refund");
        refundTranReq.setOrigBankTrxDate(DateTimeUtils.formatLocalDate(shortcutOrderDO.getOrgOrdDt()));
        refundTranReq.setOrigTrxDate(DateTimeUtils.formatLocalDate(shortcutOrderDO.getOrdDt()));
//        refundTranReq.setOrigTrxSerno(shortcutOrderDO.getOrgJrnNo());
        refundTranReq.setOrigTrxSerno(shortcutOrderDO.getOrgOrdNo());
        refundTranReq.setUserId(signAgrno);

        RefundReq.RefundReqMsg refundReqMsg = new RefundReq.RefundReqMsg();
        refundReqMsg.setPubReq(pubReq);
        refundReqMsg.setTrxCode(ICBCEnumCommon.EnumIntFcode.REFUND);
        refundReqMsg.setRefundTranReq(refundTranReq);

        RefundReq req = new RefundReq();
        req.setHeadReq(headReq);
        req.setRefundReqMsg(refundReqMsg);

        BaseBnkRspBO baseBnkRspBO = null;
        try{
            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, req, true);
            logger.info("FastpayICBCApi.refund() 银行退款接口请求参数 " + reqData);
            RefundRsp rsp = icbcApi.doSend(req, ICBCEnumCommon.EnumSource.refund);
            reqData = ObjectMapperHelper.writeValueAsString(objectMapper, rsp, true);
            logger.info("FastpayICBCApi.refund() 银行退款接口请求返回参数 " + reqData);
            if(JudgeUtils.isNotNull(rsp)) {
                baseBnkRspBO = new BaseBnkRspBO();
                RefundRsp.RefundRspMsg.RefundTranRsp bnkRsp = rsp.getRefundRspMsg().getRefundTranRsp();
                if (!icbcProperties.getCorpcode().equals(bnkRsp.getCorpNo()) && !outOrdNo.equals(bnkRsp.getTrxSerno())) {
//                    LemonException.throwBusinessException(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    baseBnkRspBO.setOrgRspCd(CpiMsgCd.VERIFY_BANK_MESSAGE_ERROR.getMsgCd());
                    return baseBnkRspBO;
                }

                if(JudgeUtils.isNotBlank(bnkRsp.getBankTrxDate())) {
                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.parseLocalDate(bnkRsp.getBankTrxDate(),"yyyyMMdd"));
                } else {
                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
                }
                baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
                baseBnkRspBO.setOrgJrnNo(bnkRsp.getBankSerno());
                baseBnkRspBO.setOutOrdNo(outOrdNo);
                baseBnkRspBO.setOrgRspCd(bnkRsp.getRetCode());
                baseBnkRspBO.setChkKey(outOrdNo);
                baseBnkRspBO.setOrgRspMsg(bnkRsp.getRetMsg());
                if(ICBCEnumCommon.EnumRetStatus.SUCCESS.equals(bnkRsp.getRetStatus())) {
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_SUC);
                } else {
                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_FAIL);
                }
            } else {
                logger.error("FastpayICBCApi.refund() bank communication exception");
                LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
            }
        }catch (Exception e){
            //异常处理
            logger.error("FastpayICBCApi.refund Exception : ",e);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }

        //模拟码
//        baseBnkRspBO = new BaseBnkRspBO();
//        String orgJrnNo = "111111";
//        baseBnkRspBO.setOutOrdNo(outOrdNo);
//        baseBnkRspBO.setChkKey(outOrdNo);
//        baseBnkRspBO.setOrgJrnNo(orgJrnNo);
//        baseBnkRspBO.setOrgRspCd("0");
//        baseBnkRspBO.setOrgRspMsg("成功");
//        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_SUC);

        return baseBnkRspBO;
    }

    /**
      * 工行快捷(充值、消费、退款)对账：获取对账文件
      */
    public String getCheckFile(String chkFilPath, LocalDate chkDt) throws Exception{
        //对账文件本地系统存放路径
        String chkDtStr = DateTimeUtils.formatLocalDate(chkDt);
        String md5FilNm = "ICBC_EXPRESSPAY_CHECKRESULT_"+icbcProperties.getCorpcode()+"_" + chkDtStr + ".MD5";
        String md5FilNmPath = chkFilPath + File.separator + md5FilNm;
        String chkFilNm = "ICBC_EXPRESSPAY_CHECKRESULT_"+icbcProperties.getCorpcode()+"_" + chkDtStr + ".TXT";
        String chkfileFullPath = chkFilPath + File.separator + chkFilNm;
        logger.debug("==================银行对账文件全路径: " + chkfileFullPath);

        //step1：获取银行对账文件，SFTP下载、请求接口下载等
        String remoteIp = icbcProperties.getRemoteIp();
        int remotePort = Integer.valueOf(icbcProperties.getRemotePort());
        int connectTimeout = Integer.valueOf(icbcProperties.getRemoteTimeOut());
        String remotePath = icbcProperties.getRemoteFilePath();
        String name = icbcProperties.getRemoteUsername();
        String pwd = icbcProperties.getRemotePassword();
        FileSftpUtils.download(remoteIp,remotePort,connectTimeout,remotePath,md5FilNm,chkFilPath,name,pwd);

        //判断文件是否下载成功，如不成功则报错
        File file = new File(md5FilNmPath);
        if(!file.exists()){
            logger.error("FastpayICBCApi.getCheckFile()，银行对账MD5文件不存在，对账日期：" + chkDtStr);
            return null;
        }

        FileSftpUtils.download(remoteIp,remotePort,connectTimeout,remotePath,chkFilNm,chkFilPath,name,pwd);
        //判断文件是否下载成功，如不成功则报错
        file = new File(chkfileFullPath);
        if(!file.exists()){
            logger.error("FastpayICBCApi.getCheckFile()，银行对账文件不存在，对账日期：" + chkDtStr);
            return null;
        }

        String md5Content = ICBCUtils.readMD5File(md5FilNmPath);
        String[] splitArray =md5Content.split("\\|");
        String md5Value = ICBCUtils.getFileMD5(chkfileFullPath);
        if(!splitArray[1].equals(md5Value)){
            logger.error("FastpayICBCApi.getCheckFile()，银行对账文件MD5校验失败，对账日期：" + chkDtStr);
            return null;
        }

        return chkFilNm;
    }

}
