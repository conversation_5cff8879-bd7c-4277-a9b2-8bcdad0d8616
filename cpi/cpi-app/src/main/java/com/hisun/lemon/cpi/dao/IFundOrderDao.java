package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.FundOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface IFundOrderDao extends BaseDao<FundOrderDO> {

    /**
     * 获取充值明细 List
     * @param ordDt 订单日期
     * @param beginNum 从表中第几行开始取值
     * @param countNum 最大读取多少行记录
     */
    List<FundOrderDO> getFundOrderList(@Param("ordDt") LocalDate ordDt, @Param("beginNum")Integer beginNum, @Param("countNum")Integer countNum);
}