package com.hisun.lemon.cpi.entity.wechat;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class AccWeChatEbankpayRefundDO extends BaseDO {
    /**
     * @Fields tranTime 交易时间
     */
    private String tranTime;
    /**
     * @Fields appid 公众账号ID
     */
    private String appid;
    /**
     * @Fields mchId 平台在微信的商户号
     */
    private String mchId;
    /**
     * @Fields subMchId 平台的商家在微信的子商户号
     */
    private String subMchId;
    /**
     * @Fields deviceInfo 设备号
     */
    private String deviceInfo;
    /**
     * @Fields transactionId 微信订单号
     */
    private String transactionId;
    /**
     * @Fields outTransactionId 平台发往微信的商户订单号
     */
    private String outTransactionId;
    /**
     * @Fields openid 用户标识
     */
    private String openid;
    /**
     * @Fields tradeType 交易类型
     */
    private String tradeType;
    /**
     * @Fields tradeState 交易状态
     */
    private String tradeState;
    /**
     * @Fields bankType 付款银行
     */
    private String bankType;
    /**
     * @Fields feeType 货币种类
     */
    private String feeType;
    /**
     * @Fields totalFee 总金额
     */
    private BigDecimal totalFee;
    /**
     * @Fields couponAmount 代金券或立减优惠金额
     */
    private BigDecimal couponAmount;
    /**
     * @Fields rfdApplyTime 退款申请时间
     */
    private String rfdApplyTime;
    /**
     * @Fields rfdSuccTime 退款成功时间
     */
    private String rfdSuccTime;
    /**
     * @Fields refundId 微信退款单号
     */
    private String refundId;
    /**
     * @Fields outRefundNo 平台发往微信的商户退款单号
     */
    private String outRefundNo;
    /**
     * @Fields refundFee 退款金额
     */
    private BigDecimal refundFee;
    /**
     * @Fields couponRfdAmount 代金券或立减优惠退款金额
     */
    private BigDecimal couponRfdAmount;
    /**
     * @Fields rfdType 退款类型
     */
    private String rfdType;
    /**
     * @Fields rfdStatus 退款状态
     */
    private String rfdStatus;
    /**
     * @Fields productNm 商品名称
     */
    private String productNm;
    /**
     * @Fields attach 商户数据包
     */
    private String attach;
    /**
     * @Fields fee 手续费
     */
    private BigDecimal fee;
    /**
     * @Fields rate 费率
     */
    private String rate;
    /**
     * @Fields cashFeeType 支付金额币种
     */
    private String cashFeeType;
    /**
     * @Fields cashFee 支付金额
     */
    private BigDecimal cashFee;
    /**
     * @Fields stlCurrType 结算金额币种
     */
    private String stlCurrType;
    /**
     * @Fields stlCurrAmount 结算金额
     */
    private BigDecimal stlCurrAmount;
    /**
     * @Fields exchangeRate 外币汇率
     */
    private String exchangeRate;
    /**
     * @Fields rfdExchangeRate 退款外币汇率
     */
    private String rfdExchangeRate;
    /**
     * @Fields payerRfdAmount 支付方退款金额
     */
    private BigDecimal payerRfdAmount;
    /**
     * @Fields payerRfdCurrType 支付方退款币种
     */
    private String payerRfdCurrType;
    /**
     * @Fields rfdCurrType 退款币种
     */
    private String rfdCurrType;
    /**
     * @Fields rfdStlCurrType 退款结算币种
     */
    private String rfdStlCurrType;
    /**
     * @Fields rfdStlAmount 退款结算金额
     */
    private BigDecimal rfdStlAmount;
    /**
     * @Fields chkBatNo 对账批次号
     */
    private String chkBatNo;
    /**
     * @Fields chkFilDt 对账文件日期
     */
    private LocalDate chkFilDt;
    /**
     * @Fields chkFilNm 对账文件名称
     */
    private String chkFilNm;
    /**
     * @Fields chkSts 对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑
     */
    private String chkSts;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields rutCorg 路径合作机构
     */
    private String rutCorg;
    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields chkId 对账明细序号
     */
    private String chkId;

    public String getTranTime() {
        return tranTime;
    }

    public void setTranTime(String tranTime) {
        this.tranTime = tranTime;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getSubMchId() {
        return subMchId;
    }

    public void setSubMchId(String subMchId) {
        this.subMchId = subMchId;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTransactionId() {
        return outTransactionId;
    }

    public void setOutTransactionId(String outTransactionId) {
        this.outTransactionId = outTransactionId;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTradeState() {
        return tradeState;
    }

    public void setTradeState(String tradeState) {
        this.tradeState = tradeState;
    }

    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public String getRfdApplyTime() {
        return rfdApplyTime;
    }

    public void setRfdApplyTime(String rfdApplyTime) {
        this.rfdApplyTime = rfdApplyTime;
    }

    public String getRfdSuccTime() {
        return rfdSuccTime;
    }

    public void setRfdSuccTime(String rfdSuccTime) {
        this.rfdSuccTime = rfdSuccTime;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public BigDecimal getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    public BigDecimal getCouponRfdAmount() {
        return couponRfdAmount;
    }

    public void setCouponRfdAmount(BigDecimal couponRfdAmount) {
        this.couponRfdAmount = couponRfdAmount;
    }

    public String getRfdType() {
        return rfdType;
    }

    public void setRfdType(String rfdType) {
        this.rfdType = rfdType;
    }

    public String getRfdStatus() {
        return rfdStatus;
    }

    public void setRfdStatus(String rfdStatus) {
        this.rfdStatus = rfdStatus;
    }

    public String getProductNm() {
        return productNm;
    }

    public void setProductNm(String productNm) {
        this.productNm = productNm;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getCashFeeType() {
        return cashFeeType;
    }

    public void setCashFeeType(String cashFeeType) {
        this.cashFeeType = cashFeeType;
    }

    public BigDecimal getCashFee() {
        return cashFee;
    }

    public void setCashFee(BigDecimal cashFee) {
        this.cashFee = cashFee;
    }

    public String getStlCurrType() {
        return stlCurrType;
    }

    public void setStlCurrType(String stlCurrType) {
        this.stlCurrType = stlCurrType;
    }

    public BigDecimal getStlCurrAmount() {
        return stlCurrAmount;
    }

    public void setStlCurrAmount(BigDecimal stlCurrAmount) {
        this.stlCurrAmount = stlCurrAmount;
    }

    public String getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(String exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getRfdExchangeRate() {
        return rfdExchangeRate;
    }

    public void setRfdExchangeRate(String rfdExchangeRate) {
        this.rfdExchangeRate = rfdExchangeRate;
    }

    public BigDecimal getPayerRfdAmount() {
        return payerRfdAmount;
    }

    public void setPayerRfdAmount(BigDecimal payerRfdAmount) {
        this.payerRfdAmount = payerRfdAmount;
    }

    public String getPayerRfdCurrType() {
        return payerRfdCurrType;
    }

    public void setPayerRfdCurrType(String payerRfdCurrType) {
        this.payerRfdCurrType = payerRfdCurrType;
    }

    public String getRfdCurrType() {
        return rfdCurrType;
    }

    public void setRfdCurrType(String rfdCurrType) {
        this.rfdCurrType = rfdCurrType;
    }

    public String getRfdStlCurrType() {
        return rfdStlCurrType;
    }

    public void setRfdStlCurrType(String rfdStlCurrType) {
        this.rfdStlCurrType = rfdStlCurrType;
    }

    public BigDecimal getRfdStlAmount() {
        return rfdStlAmount;
    }

    public void setRfdStlAmount(BigDecimal rfdStlAmount) {
        this.rfdStlAmount = rfdStlAmount;
    }

    public String getChkBatNo() {
        return chkBatNo;
    }

    public void setChkBatNo(String chkBatNo) {
        this.chkBatNo = chkBatNo;
    }

    public LocalDate getChkFilDt() {
        return chkFilDt;
    }

    public void setChkFilDt(LocalDate chkFilDt) {
        this.chkFilDt = chkFilDt;
    }

    public String getChkFilNm() {
        return chkFilNm;
    }

    public void setChkFilNm(String chkFilNm) {
        this.chkFilNm = chkFilNm;
    }

    public String getChkSts() {
        return chkSts;
    }

    public void setChkSts(String chkSts) {
        this.chkSts = chkSts;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getChkId() {
        return chkId;
    }

    public void setChkId(String chkId) {
        this.chkId = chkId;
    }

}