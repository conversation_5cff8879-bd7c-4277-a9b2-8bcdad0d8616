package com.hisun.lemon.cpi.schedule;

import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.service.ISettlementService;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时任务-商户结算明细数据查询
 */
@Component
public class SettlementQuerySchedule {

    private static final Logger logger = LoggerFactory.getLogger(SettlementQuerySchedule.class);

    @Resource
    private ISettlementService settlementService;

    /**
     * 定时任务-向银行接口发起查询，查询微信商户的未结算明细数据
     * 每天上午十点、下午三点执行一次
     * 最多只能查询31天以内的数据
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 0 10,15,17 * * ?")
    public void WeChatUnsettlementQuery() {
        try {
            settlementService.settlementQuery(CpiConstants.UNSETTLEMENT, CpiConstants.WECHAT, null, null);
        } catch (Exception e) {
            logger.error("SettlementQuerySchedule.WeChatSettlementQuery() 执行失败，异常为", e);
            return;
        }
    }

    /**
     * 定时任务-向银行接口发起查询，查询微信商户的已结算明细数据
     * 每天上午十点半、下午三点半执行一次
     * 最多只能查询31天以内的数据
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0 10 10,15,17 * * ?")
    public void WeChatSettlementQuery() {
        try {
            settlementService.settlementQuery(CpiConstants.SETTLEMENT, CpiConstants.WECHAT, null, null);
        } catch (Exception e) {
            logger.error("SettlementQuerySchedule.WeChatSettlementQuery() 执行失败，异常为", e);
            return;
        }
    }


    //    @Scheduled(cron = "0 0/2 * * * ?")
    @BatchScheduled(cron = "0 10 10,15,17 * * ?")
    public void alipaySettlementQuery() {
        logger.info("支付宝结算日期开始处理");
        try {
            settlementService.settlementQuery(CpiConstants.SETTLEMENT, CpiConstants.ALIPAY, null, null);
        } catch (Exception e) {
            logger.error("SettlementQuerySchedule.AlipaySettlementQuery() 执行失败，异常为", e);
            return;
        }
    }

}
