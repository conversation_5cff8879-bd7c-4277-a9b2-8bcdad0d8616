/*
 * @ClassName ICardBinDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.CardBinDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ICardBinDao extends BaseDao<CardBinDO> {

    CardBinDO selectByCardBin(@Param("crdBin")String crdBin, @Param("crdLth")Integer crdLth);


}