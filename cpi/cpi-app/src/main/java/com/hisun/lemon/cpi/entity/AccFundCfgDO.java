/*
 * @ClassName AccFundCfgDO
 * @Description 
 * @version 1.0
 * @Date 2017-09-11 11:32:56
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AccFundCfgDO extends BaseDO {
    /**
     * @Fields accCfgId id
     */
    private String accCfgId;
    /**
     * @Fields rutCorg 路径机构
     */
    private String rutCorg;
    /**
     * @Fields corpBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields chkClazz 银行API类
     */
    private String chkClazz;
    /**
     * @Fields getFileMethod 获取对账文件的方法
     */
    private String getFileMethod;
    /**
     * @Fields chkFilePath 对账文件本地路径
     */
    private String chkFilePath;
    /**
     * @Fields uploadIp 上传ip
     */
    private String uploadIp;
    /**
     * @Fields uploadPort 上传端口
     */
    private String uploadPort;
    /**
     * @Fields uploadPath 上传路径
     */
    private String uploadPath;
    /**
     * @Fields uploadName 登录用户名
     */
    private String uploadName;
    /**
     * @Fields uploadPwd 登录密码
     */
    private String uploadPwd;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getAccCfgId() {
        return accCfgId;
    }

    public void setAccCfgId(String accCfgId) {
        this.accCfgId = accCfgId;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getChkClazz() {
        return chkClazz;
    }

    public void setChkClazz(String chkClazz) {
        this.chkClazz = chkClazz;
    }

    public String getGetFileMethod() {
        return getFileMethod;
    }

    public void setGetFileMethod(String getFileMethod) {
        this.getFileMethod = getFileMethod;
    }

    public String getChkFilePath() {
        return chkFilePath;
    }

    public void setChkFilePath(String chkFilePath) {
        this.chkFilePath = chkFilePath;
    }

    public String getUploadIp() {
        return uploadIp;
    }

    public void setUploadIp(String uploadIp) {
        this.uploadIp = uploadIp;
    }

    public String getUploadPort() {
        return uploadPort;
    }

    public void setUploadPort(String uploadPort) {
        this.uploadPort = uploadPort;
    }

    public String getUploadPath() {
        return uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public String getUploadName() {
        return uploadName;
    }

    public void setUploadName(String uploadName) {
        this.uploadName = uploadName;
    }

    public String getUploadPwd() {
        return uploadPwd;
    }

    public void setUploadPwd(String uploadPwd) {
        this.uploadPwd = uploadPwd;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}