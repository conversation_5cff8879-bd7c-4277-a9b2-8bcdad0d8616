package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.bnkapi.alipay.AlipayApi;
import com.hisun.lemon.cpi.bnkapi.bestpay.ebankpay.BestPayScanPayApi;
import com.hisun.lemon.cpi.bnkapi.icbc.fastpay.FastpayICBCApi;
import com.hisun.lemon.cpi.bnkapi.wechat.WeChatPayNewApi;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.*;
import com.hisun.lemon.cpi.dto.OrderResultRspDTO;
import com.hisun.lemon.cpi.dto.RefundReqDTO;
import com.hisun.lemon.cpi.dto.RefundRspDTO;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.IRefundService;
import com.hisun.lemon.cpi.service.IRefundTransaction;
import com.hisun.lemon.cpi.utils.AccountUtils;
import com.hisun.lemon.cpi.wechat.ebankpay.WeChatProperties;
import com.hisun.lemon.csh.client.CshRefundClient;
import com.hisun.lemon.csh.dto.refund.RefundResultOrderDTO;
import com.hisun.lemon.framework.core.InitLemonData;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Rui on 2017/7/11.
 */
@Transactional
@Service
public class RefundServiceImpl extends BaseService implements IRefundService {

    @Resource
    private IFundOrderDao fundOrderDao;
    @Resource
    private FastpayICBCApi fastpayICBCApi;
    @Resource
    private IRefundOrderDao refundOrderDao;
    @Resource
    private IRefundSuborderDao refundSuborderDao;
    @Resource
    private IRefundParamDao refundParamDao;
    @Resource
    private IShortcutOrderDao shortcutOrderDao;
    @Resource
    private IEbankOrderDao ebankOrderDao;
    @Resource
    private IRefundTransaction refundTransaction;
    @Resource
    private BestPayScanPayApi bestPayScanPayApi;
    @Resource
    private RiskCheckClient riskCheckClient;
    @Resource
    private CshRefundClient cshRefundClient;
    @Resource
    private ICardProtDao cardProtDao;
    @Resource
    private AccountingTreatmentClient accTreatClient;
    @Resource
    private WeChatPayNewApi weChatPayNewApi;
    @Resource
    private ISubMercCastDao subMercCastDao;
    @Resource
    private WeChatProperties weChatProperties;
    @Resource
    private AlipayApi alipayApi;

    private static final Logger logger = LoggerFactory.getLogger(RefundServiceImpl.class);

    /**
     * 退款申请
     */
    @Override
    public GenericRspDTO<RefundRspDTO> createOrder(GenericDTO<RefundReqDTO> genericDTO) {
        //请求参数
        RefundReqDTO refundReqDTO = genericDTO.getBody();
        String corpBusTyp = refundReqDTO.getCorpBusTyp().getType();
        String corpSubBusTyp = refundReqDTO.getCorpBusSubTyp().getType();
        //返回参数
        RefundRspDTO refundRspDTO = new RefundRspDTO();
        try {
            //Step1:业务合作类型检查
            if (!CorpBusTyp.REFUND.getType().equals(corpBusTyp)) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd(), refundRspDTO);
            }
            if (!CorpBusSubTyp.FAST_REFUND.getType().equals(corpSubBusTyp)
                    && !CorpBusSubTyp.EBANK_REFUND.getType().equals(corpSubBusTyp)) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), refundRspDTO);
            }

            //Step2:金额检查，原订单状态检查
            FundOrderDO fundOrderDO = fundOrderDao.get(refundReqDTO.getOrdNo());
            if (JudgeUtils.isNull(fundOrderDO)) {
                logger.error("RefundServiceImpl.createOrder() 退款申请，不存在原充值订单，原充值订单号为：" + refundReqDTO.getOrdNo());
                return GenericRspDTO.newInstance(CpiMsgCd.FUND_ORDER_IS_NOT_FOUND.getMsgCd(), refundRspDTO);
            }

            //若原充值订单状态为失败或已全部退款，则不允许再次退款下单
            if (CpiConstants.ORD_FAIL.equals(fundOrderDO.getOrdSts()) || CpiConstants.ORD_REFUND_ALL.equals(fundOrderDO.getOrdSts())) {
                logger.error("RefundServiceImpl.createOrder() 退款申请，原订单状态不满足再次退款申请，原充值订单号为：" + refundReqDTO.getOrdNo());
                return GenericRspDTO.newInstance(CpiMsgCd.FORBID_REFUND.getMsgCd(), refundRspDTO);
            }

            //退款申请金额
            BigDecimal ordAmt = fundOrderDO.getOrdAmt();
            //退款订单表中非失败的已申请退款金额
            BigDecimal ordRfdAmt = refundSuborderDao.selectSumRfdAmt(refundReqDTO.getOrdNo());
            //若退款订单表中没有该订单
            if (JudgeUtils.isNull(ordRfdAmt)) {
                //设置已退款金额为 0
                ordRfdAmt = BigDecimal.ZERO;
            }
            //可退款金额
            BigDecimal ableRfdAmt = ordAmt.subtract(ordRfdAmt);
            //可退金额小于退款金额
            if (ableRfdAmt.compareTo(refundReqDTO.getOrdAmt()) < 0) {
                logger.error("RefundServiceImpl.createOrder() 退款申请，可退款金额小于申请退款金额");
                return GenericRspDTO.newInstance(CpiMsgCd.REFUND_AMT_IS_ERROR.getMsgCd(), refundRspDTO);
            }

            //Step3:查询退款参数
            RefundParamDO refundParamDO = refundParamDao.selectByUniqueKey(fundOrderDO.getRutCorpOrg(), corpBusTyp, corpSubBusTyp);
            if (JudgeUtils.isNull(refundParamDO)) {
                logger.error("RefundServiceImpl.createOrder() 退款申请，退款参数不存在");
                return GenericRspDTO.newInstance(CpiMsgCd.REFUND_PARAM_IS_NOT_FUND.getMsgCd(), refundRspDTO);
            }

            //不支持当日退款
            if (("N").equals(refundParamDO.getSadRfdFlg())) {
                if (fundOrderDO.getOrdDt().compareTo(DateTimeUtils.getCurrentLocalDate()) >= 0) {
                    logger.error("RefundServiceImpl.createOrder() 退款申请，不支持当日退款");
                    return GenericRspDTO.newInstance(CpiMsgCd.FORBID_REFUND_TODAY.getMsgCd(), refundRspDTO);
                }
            }

            //不支持多次退款
            if (("N").equals(refundParamDO.getMtsRfdFlg())) {
                if (refundReqDTO.getOrdAmt().compareTo(fundOrderDO.getOrdAmt()) != 0) {
                    logger.error("RefundServiceImpl.createOrder() 退款申请，不支持多次退款");
                    return GenericRspDTO.newInstance(CpiMsgCd.FORBID_MULTI_REFUND.getMsgCd(), refundRspDTO);
                }
            }

            //生成退款订单号
            String rfdOrdNo = IdGenUtils.generateIdWithDateTime("RFD_ORD_NO", "CPI", 4);

            //Step4:风控检查，黑名单检查
            JrnReqDTO jrnReqDTO = new JrnReqDTO();
            //收方ID
            jrnReqDTO.setStlUserId(fundOrderDO.getUserId());
            //收方类型
            jrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
            //付方ID
            jrnReqDTO.setPayUserId(refundReqDTO.getUserNo());
            //付方类型
            jrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
            //交易类型
            jrnReqDTO.setTxTyp(Constants.TX_TYP_REFUNDS);
            //交易状态
            jrnReqDTO.setTxSts("0");
            //交易渠道
            jrnReqDTO.setTxCnl("APP");
            //交易金额
            jrnReqDTO.setTxAmt(refundReqDTO.getOrdAmt());
            //交易币种
            jrnReqDTO.setCcy(refundReqDTO.getOrdCcy());
            //交易日期
            jrnReqDTO.setTxDate(DateTimeUtils.getCurrentLocalDate());
            //交易时间
            jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
            //原交易流水号
            jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
            //原交易订单号
            jrnReqDTO.setTxOrdNo(rfdOrdNo);
            GenericRspDTO<NoBody> riskRsp = riskCheckClient.riskControl(jrnReqDTO);
            if (JudgeUtils.isNotSuccess(riskRsp.getMsgCd())) {
                logger.error("RefundServiceImpl.createOrder() 退款申请，风控检查不通过");
                throw new LemonException(riskRsp.getMsgCd());
            }

            //Step5:登记退款订单表
            RefundOrderDO refundOrderDO = new RefundOrderDO();
            refundOrderDO.setUserId(LemonUtils.getUserId());
            BeanUtils.copyProperties(refundOrderDO, fundOrderDO);
            BeanUtils.copyProperties(refundOrderDO, refundReqDTO);
            //协议付款日大于当天
            LocalDate localDate = DateTimeUtils.getCurrentLocalDate();
            if (refundReqDTO.getAgrPayDt().compareTo(localDate) > 0) {
                String agrPayDt = DateTimeUtils.formatLocalDate(refundReqDTO.getAgrPayDt());
                refundOrderDO.setRfdTime(DateTimeUtils.parseLocalDateTime(agrPayDt + "000000"));
            }
            //协议付款日等于当天
            else if (refundReqDTO.getAgrPayDt().compareTo(localDate) == 0) {
                refundOrderDO.setRfdTime(LocalDateTime.now());
            }
            //协议付款日小于当天
            else {
                logger.error("RefundServiceImpl.createOrder() 退款申请，协议付款日小于当天，不允许退款");
                return GenericRspDTO.newInstance(CpiMsgCd.REFUND_TIME_IS_ERROR.getMsgCd(), refundRspDTO);
            }
            refundOrderDO.setRfdOrdNo(rfdOrdNo);
            refundOrderDO.setOrdDt(localDate);
            refundOrderDO.setOrdTm(LocalTime.now());
            refundOrderDO.setCorpBusTyp(corpBusTyp);
            refundOrderDO.setCorpBusSubTyp(corpSubBusTyp);
            //退款订单初始状态，等待退款
            refundOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY);
            refundOrderDO.setCreateTime(null);
            refundOrderDO.setModifyTime(null);
            refundOrderDO.setTmSmp(null);
            //自动付款
            if (CpiConstants.PAY_FLAG_AUTO.equals(refundParamDO.getAutoRfdFlg())) {
                refundOrderDO.setAutoPayFlg(CpiConstants.PAY_FLAG_AUTO);
            } else {
                //手工付款
                refundOrderDO.setAutoPayFlg(CpiConstants.PAY_FLAG_MANUAL);
            }

            //Step6:更新原充值订单表状态
            FundOrderDO updateFundOrder = new FundOrderDO();
            updateFundOrder.setFudOrdNo(refundReqDTO.getOrdNo());
            //部分退款
            if (refundReqDTO.getOrdAmt().compareTo(fundOrderDO.getOrdAmt()) < 0) {
                updateFundOrder.setOrdSts(CpiConstants.ORD_REFUND_PART);
            }
            //全额退款
            else if (refundReqDTO.getOrdAmt().compareTo(fundOrderDO.getOrdAmt()) == 0) {
                updateFundOrder.setOrdSts(CpiConstants.ORD_REFUND_ALL);
            }
            //退款金额有误
            else {
                return GenericRspDTO.newInstance(CpiMsgCd.REFUND_TIME_IS_ERROR.getMsgCd(), refundRspDTO);
            }

            //Step7:根据不同的业务合作类型调用不同的登记子表方法
            RefundSuborderDO refundSuborderDO = new RefundSuborderDO();
            String chkKey = IdGenUtils.generateIdWithDateTime("OUT_ORD_NO",6);
            switch (corpSubBusTyp) {
                //原订单为快捷订单才可以快捷退款
                case "0501":
                    if (fundOrderDO.getCorpBusTyp().equals(CorpBusTyp.FASTPAY.getType())) {
                        ShortcutOrderDO shortcutOrderDO = shortcutOrderDao.selectByFndOrdNo(fundOrderDO.getFudOrdNo());
                        BeanUtils.copyProperties(refundSuborderDO, refundOrderDO);
                        String subOrdNo = IdGenUtils.generateIdWithDateTime("RFD_SUB_ORD_NO", "CPI", 4);
                        refundOrderDO.setAcDt(null);
                        refundSuborderDO.setAcDt(null);
                        refundSuborderDO.setSubOrdNo(subOrdNo);
                        refundSuborderDO.setCorpBusTyp(corpBusTyp);
                        refundSuborderDO.setCorpBusSubTyp(corpSubBusTyp);
                        refundSuborderDO.setFndOrdNo(fundOrderDO.getFudOrdNo());
                        refundSuborderDO.setFndSubOrdNo(shortcutOrderDO.getSubOrdNo());
                        refundSuborderDO.setChkKey(chkKey);
                    }
                    break;
                //原订单为网银订单才可以网银退款
                case "0502":
                    if (fundOrderDO.getCorpBusTyp().equals(CorpBusTyp.EBANKPAY.getType())) {
                        EbankOrderDO ebankOrderDO = ebankOrderDao.selectByFndOrdNo(fundOrderDO.getFudOrdNo());
                        BeanUtils.copyProperties(refundSuborderDO, refundOrderDO);
                        String subOrdNo = IdGenUtils.generateIdWithDateTime("RFD_SUB_ORD_NO", "CPI", 4);
                        refundOrderDO.setAcDt(null);
                        refundSuborderDO.setAcDt(null);
                        refundSuborderDO.setSubOrdNo(subOrdNo);
                        refundSuborderDO.setCorpBusTyp(corpBusTyp);
                        refundSuborderDO.setCorpBusSubTyp(corpSubBusTyp);
                        refundSuborderDO.setFndOrdNo(fundOrderDO.getFudOrdNo());
                        refundSuborderDO.setFndSubOrdNo(ebankOrderDO.getSubOrdNo());
                        refundSuborderDO.setChkKey(chkKey);
                    }
                    break;
                default:
                    //业务类型不正确
                    return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), refundRspDTO);
            }

            //Step8:退款申请账务处理
            GenericDTO<List<AccountingReqDTO>> accountingReqDTO = new GenericDTO<>();
            List<AccountingReqDTO> accountingReqList = new ArrayList<>();
            String rutCorgOrg = fundOrderDO.getRutCorpOrg();
            String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPI", 8);

            //借：其他应付款-渠道退款-XX银行
            AccountingReqDTO accReqInf1 = AccountUtils.getAccountingReqDTO("N", "I", "", "1", AccountUtils.getOtherPayItemNo1(rutCorgOrg), CpiConstants.TX_TYP_REFUNDS,
                    refundReqDTO.getOrdAmt(), "D", txJrnNo, refundSuborderDO.getSubOrdNo(), "", "", "", "", "");

            //贷：应付账款-渠道退款-XX银行
            AccountingReqDTO accReqInf2 = AccountUtils.getAccountingReqDTO("N", "I", "", "1", AccountUtils.getPayItemNo1(rutCorgOrg), CpiConstants.TX_TYP_REFUNDS,
                    refundReqDTO.getOrdAmt(), "C", txJrnNo, refundSuborderDO.getSubOrdNo(), "", "", "", "", "");

            //调用账务模块接口进行账务处理
            accountingReqList.add(accReqInf1);
            accountingReqList.add(accReqInf2);
            accountingReqDTO.setBody(accountingReqList);
            GenericRspDTO<NoBody> rspDTO = accTreatClient.accountingTreatment(accountingReqDTO);
            if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
                logger.info("RefundServiceImpl.createOrder() 退款申请账务处理失败，错误码为 " + rspDTO.getMsgCd());
                throw new LemonException(rspDTO.getMsgCd());
            }
            //更新原充值订单信息
            fundOrderDao.update(updateFundOrder);
            //新增退款订单信息
            refundOrderDao.insert(refundOrderDO);
            refundSuborderDao.insert(refundSuborderDO);
            //账务处理成功
            BeanUtils.copyProperties(refundRspDTO, refundReqDTO);
            refundRspDTO.setRfdOrdNo(rfdOrdNo);
            refundRspDTO.setOrdSts(CpiConstants.ORD_WATING_PAY);
            return GenericRspDTO.newSuccessInstance(refundRspDTO);
        } catch (LemonException e) {
            logger.error("RefundServiceImpl.createOrder() exception : ", e);
            return GenericRspDTO.newInstance(e.getMsgCd(), refundRspDTO);
        } catch (Exception e) {
            logger.error("RefundServiceImpl.createOrder() exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), refundRspDTO);
        }
    }

    /**
     * 退款订单结果查询
     */
    @Override
    public GenericRspDTO<OrderResultRspDTO> queryOrder(String ordNo) {
        //返回结果
        OrderResultRspDTO orderResultDTO = new OrderResultRspDTO();
        try {
            RefundOrderDO refundOrderDO = refundOrderDao.get(ordNo);
            if (JudgeUtils.isNull(refundOrderDO)) {
                return GenericRspDTO.newInstance(CpiMsgCd.REFUND_ORDER_IS_NOT_FOUND.getMsgCd(), orderResultDTO);
            }
            BeanUtils.copyProperties(orderResultDTO, refundOrderDO);
            orderResultDTO.setOrdNo(ordNo);
            return GenericRspDTO.newSuccessInstance(orderResultDTO);
        } catch (LemonException e) {
            logger.error("RefundServiceImpl.queryOrder() exception : ", e);
            return GenericRspDTO.newInstance(e.getMsgCd(), orderResultDTO);
        } catch (Exception e) {
            logger.error("RefundServiceImpl.queryOrder() exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), orderResultDTO);
        }
    }

    /**
     * 定时任务，退款结果通知
     */
    @Override
    public void notifyOrderByTime() throws Exception {
        //查询订单状态为成功、结果通知标识为W-未通知的退款订单
        int maxPageNum = CpiConstants.MAX_PAGE_NUM;//分页查询最大页数
        int maxQueryNum = CpiConstants.MAX_QUERY_NUM;//每页最大笔数
        int i = 0;
        while (i < maxPageNum) {
            int beginNum = maxPageNum * i;//从表中第几行开始获取记录
            List<RefundOrderDO> refundOrderDOList = refundOrderDao.getRefundOrderToNotify(beginNum, maxQueryNum);
            logger.debug("RefundNotifySchedule.execute() beginNum = " + beginNum + "; maxQueryNum = " + maxQueryNum + "; 查询笔数：" + refundOrderDOList.size());
            if (CollectionUtils.isEmpty(refundOrderDOList)) {
                break;
            }
            //循环发送
            for (RefundOrderDO refundOrderDO : refundOrderDOList) {
                GenericDTO<RefundResultOrderDTO> genericDTO = new GenericDTO<>();
                RefundResultOrderDTO refundResultOrderDTO = new RefundResultOrderDTO();
                refundResultOrderDTO.setFndRfdOrderNo(refundOrderDO.getRfdOrdNo());
                refundResultOrderDTO.setRfdOrdNo(refundOrderDO.getReqOrdNo());
                refundResultOrderDTO.setRfdSumAmt(refundOrderDO.getOrdAmt());
                refundResultOrderDTO.setOrderSataus("R3");//退款成功
                genericDTO.setBody(refundResultOrderDTO);
                GenericRspDTO genericRspDTO = cshRefundClient.completeBill(genericDTO);

                RefundOrderDO updateOrderDo = new RefundOrderDO();
                updateOrderDo.setRfdOrdNo(refundOrderDO.getRfdOrdNo());
                if (JudgeUtils.isSuccess(genericRspDTO.getMsgCd())) {
                    updateOrderDo.setNtfSts(CpiConstants.NOTIFY_SUCCESS);
                } else {
                    updateOrderDo.setNtfSts(CpiConstants.NOTIFY_FAIL);
                }
                updateOrderDo.setAcDt(LemonUtils.getAccDate());
                updateOrderDo.setNtfDt(DateTimeUtils.getCurrentLocalDate());
                updateOrderDo.setNtfRspCd(genericRspDTO.getMsgCd());
                updateOrderDo.setNtfRspMsg(genericRspDTO.getMsgInfo());
                updateOrderDo.setNtfTm(LocalTime.now());
                refundOrderDao.update(updateOrderDo);
            }
            i++;
        }
    }

    /**
     * 定时任务，向银行发起退款请求
     */
    @Override
    public void refundByTime() {
        //查询次数
        int offset = 0;
        //每次最多查询100笔订单
        int number = 100;
        logger.info("=================退款开始==============");
        while (true) {
            RefundOrderDO refundOrderDO = new RefundOrderDO();
            refundOrderDO.setAgrPayDt(DateTimeUtils.getCurrentLocalDate());
            refundOrderDO.setRfdTime(DateTimeUtils.getCurrentLocalDateTime());
            refundOrderDO.setRfdFailCount(3);

            //Step1:查询出需要退款的数据
            List<RefundOrderDO> refundOrderDOList = refundOrderDao.queryAwaitingPayment(refundOrderDO, offset * number, number);
            logger.info("第" + offset + "页获取到" + refundOrderDOList.size() + "条数据");

            //超过50次，即已查出5000条退款数据，这次退款批次就结束，防止过多操作或者while(true)的死循环
            if (offset >= 50) {
                return;
            }
            //查询结果为空，则直接退出当前方法
            if (JudgeUtils.isNull(refundOrderDOList) || refundOrderDOList.size() == 0) {
                return;
            }

            //Step2:循环调用银行接口进行退款；退款都是单笔发送，可能会存在报错，报错需要捕获处理
            for (RefundOrderDO refundOrder : refundOrderDOList) {
                RefundSuborderDO refundSuborderDO = refundSuborderDao.selectByUniqueKey(refundOrder.getRfdOrdNo());
                String outOrdNo = refundSuborderDO.getChkKey();
                try {
                    BaseBnkRspBO baseBnkRspBO = null;
                    //快捷退款
                    if (CorpBusSubTyp.FAST_REFUND.getType().equals(refundOrder.getCorpBusSubTyp())) {
                        ShortcutOrderDO shortcutOrderDO = shortcutOrderDao.get(refundSuborderDO.getFndSubOrdNo());
                        baseBnkRspBO = this.fastpayRefund(refundOrder, shortcutOrderDO);
                    }
                    //网银退款
                    else if (CorpBusSubTyp.EBANK_REFUND.getType().equals(refundOrder.getCorpBusSubTyp())) {
                        EbankOrderDO ebankOrderDO = ebankOrderDao.get(refundSuborderDO.getFndSubOrdNo());
                        //若路由是微信，则根据商户号查询对应的子商户信息
                        SubMercCastDO subMercCastDO = null;
                        if(JudgeUtils.equals(CpiConstants.WECHAT, ebankOrderDO.getRutCorpOrg())) {
                            subMercCastDO = subMercCastDao.getSubMercCastDO(weChatProperties.getAppId(), ebankOrderDO.getRcvUserId(), CpiConstants.EFFECT_YES);
                            if(JudgeUtils.isNull(subMercCastDO)) {
                                logger.info("EbankpayServiceImpl.refundByTime() 路由为微信时，商户对应的微信子商户信息不存在");
                                continue;
                            }
                        }
                        baseBnkRspBO = this.ebankRefund(refundOrder, refundSuborderDO, ebankOrderDO, subMercCastDO);
                    }
                    //不支持的退款业务类型
                    else {
                        logger.info("退款业务类型有误，退款订单号为+" + refundOrder.getRfdOrdNo());
                        return;
                    }
                    //根据银行结果更新订单状态
                    refundTransaction.updateOrdSts(refundOrder, refundSuborderDO.getSubOrdNo(), refundOrder.getRfdFailCount(), baseBnkRspBO);
                } catch (Exception e) {
                    //更新为失败状态
                    refundTransaction.updateOrdSts(refundOrder, refundSuborderDO.getSubOrdNo(), refundOrder.getRfdFailCount(), null);
                    logger.error("向银行发起退款异常，退款订单号为 " + refundOrder.getRfdOrdNo() + "，异常为:", e);
                }
            }
            offset++;
        }
    }

    /**
     * 快捷退款
     */
    private BaseBnkRspBO fastpayRefund(RefundOrderDO refundOrderDO, ShortcutOrderDO shortcutOrderDO) {
        switch (refundOrderDO.getRutCorpOrg()) {
            case CpiConstants.ICBC:
                CardProtDO cardProtDO = cardProtDao.get(shortcutOrderDO.getAgrNo());
                BaseBnkRspBO baseBnkRspBO = fastpayICBCApi.refund(refundOrderDO, shortcutOrderDO,cardProtDO.getSignAgrno());
                return baseBnkRspBO;
            default:
                return null;
        }
    }

    /**
     * 网银(微信，支付宝）退款申请
     */
    private BaseBnkRspBO ebankRefund(RefundOrderDO refundOrderDO, RefundSuborderDO refundSuborderDO, EbankOrderDO ebankOrderDO, SubMercCastDO subMercCastDO) {
        BaseBnkRspBO baseBnkRspBO = null;
        switch (refundOrderDO.getRutCorpOrg()) {
            //翼支付退款
            case CpiConstants.BESTPAY:
                //商户扫码退款
                if (ebankOrderDO.getCorpBusSubTyp().equals(CorpBusSubTyp.EBANKPAY_MERCSCAN.getType())) {
                    baseBnkRspBO = bestPayScanPayApi.mercRefundBankOrder(refundOrderDO, ebankOrderDO, refundSuborderDO.getChkKey());
                }
                //用户扫码退款
                else if (ebankOrderDO.getCorpBusSubTyp().equals(CorpBusSubTyp.EBANKPAY_USERSCAN.getType())) {
                    baseBnkRspBO = bestPayScanPayApi.userRefundBankOrder(refundOrderDO, ebankOrderDO, refundSuborderDO.getChkKey());
                }
                break;
            //微信退款
            case CpiConstants.WECHAT:
                if(JudgeUtils.isNotNull(refundSuborderDO) && JudgeUtils.isNotNull(subMercCastDO)) {
                    baseBnkRspBO = weChatPayNewApi.refundOrder(subMercCastDO, refundOrderDO, refundSuborderDO, ebankOrderDO);
                }
                break;
            //支付宝退款
            case CpiConstants.ALIPAY:
                if(JudgeUtils.isNotNull(refundSuborderDO) ) {
                    if(CpiConstants.ORD_WATING_PAY.equals(ebankOrderDO.getOrdSts()) ||
                            CpiConstants.ORD_PAY_BANKING.equals(ebankOrderDO.getOrdSts())){
                        baseBnkRspBO = alipayApi.closeOrder(ebankOrderDO);
                    }else{
                        baseBnkRspBO = alipayApi.refundOrder(refundOrderDO, ebankOrderDO, refundSuborderDO.getChkKey());
                    }
                }
                break;
            default:
                break;
        }
        return baseBnkRspBO;
    }

    /**
     * 定时任务，调用银行接口，查询退款订单处理结果
     */
    @Override
    @InitLemonData
    public void refundAdditionalOrder(FundPollParamDO fundPollParamDO) {
        logger.debug("根据路由查询退款订单的最终处理结果 START");
        RefundSuborderDO refundSuborderQry = new RefundSuborderDO();
        refundSuborderQry.setCorpBusTyp(fundPollParamDO.getCorpBusTyp());
        refundSuborderQry.setCorpBusSubTyp(fundPollParamDO.getCorpBusSubTyp());
        refundSuborderQry.setRutCorpOrg(fundPollParamDO.getRutCorg());
        refundSuborderQry.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        refundSuborderQry.setOrdTm(DateTimeUtils.getCurrentLocalTime().minusSeconds(fundPollParamDO.getBeforeTime()));
        if(JudgeUtils.equals(fundPollParamDO.getRutCorg(),CpiConstants.ALIPAY)){
            logger.debug("资金机构为支付宝，不进行退款处理结果查询.");
            return;
        }
        //查询退款受理成功、银行处理中、一个小时之前的网银退款订单
        List<RefundSuborderDO> refundSuborderDOList = refundSuborderDao.selectRefundAddtionalOrder(refundSuborderQry, fundPollParamDO.getCycOrdCnt());
        BaseBnkRspBO baseBnkRspBO = null;
        boolean supportBank = true;
        for (RefundSuborderDO refundSuborderDO : refundSuborderDOList) {
            //更新退款子订单轮询状态
            RefundSuborderDO updateRefundSuborderDO = new RefundSuborderDO();
            updateRefundSuborderDO.setSubOrdNo(refundSuborderDO.getSubOrdNo());
            //设置为已轮询
            updateRefundSuborderDO.setSplOrdFlg("1");

            //更新退款订单主表
            RefundOrderDO updateRefundOrderDO = new RefundOrderDO();
            updateRefundOrderDO.setRfdOrdNo(refundSuborderDO.getRfdOrdNo());
            String rutCorpOrg = refundSuborderDO.getRutCorpOrg();
            if(JudgeUtils.isEmpty(rutCorpOrg)) {
                logger.error("RefundServiceImpl.refundAdditionalOrder() error: rutCorpOrg is null");
                //新开事务提交，不影响后面的订单
                refundTransaction.updateRefundOrder(updateRefundSuborderDO);
            }

            //若路由是微信，则根据商户号查询对应的子商户信息
            SubMercCastDO subMercCastDO = null;
            EbankOrderDO ebankOrderDO = ebankOrderDao.get(refundSuborderDO.getFndSubOrdNo());
            if(JudgeUtils.equals(CpiConstants.WECHAT, rutCorpOrg)) {
                subMercCastDO = subMercCastDao.getSubMercCastDO(weChatProperties.getAppId(), ebankOrderDO.getRcvUserId(), CpiConstants.EFFECT_YES);
                if(JudgeUtils.isNull(subMercCastDO)) {
                    logger.info("EbankpayServiceImpl.refundByTime() 路由为微信时，商户对应的微信子商户信息不存在");
                    continue;
                }
            }

            //调用银行退款订单查询接口
            switch (rutCorpOrg) {
                //翼支付
                case CpiConstants.BESTPAY:
                    baseBnkRspBO = bestPayScanPayApi.queryRefundOrder(refundSuborderDO, ebankOrderDO);
                    break;
                //微信
                case CpiConstants.WECHAT:
                    baseBnkRspBO = weChatPayNewApi.refundQuery(subMercCastDO, refundSuborderDO);
                    break;
                //支付宝
                case CpiConstants.ALIPAY:
                    baseBnkRspBO = alipayApi.refundQuery(ebankOrderDO, refundSuborderDO);
                    break;
                //不支持的路由机构
                default:
                    supportBank = false;
                    break;
            }
            //若银行返回结果为空
            if (JudgeUtils.isNull(baseBnkRspBO)) {
                if (!supportBank) {
                    logger.error("RefundServiceImpl.refundAdditionalOrder() error: 不支持该路由机构");
                } else {
                    logger.error("RefundServiceImpl.refundAdditionalOrder() error: 银行通讯错误，返回报文为空");
                }
                //新开事务提交，不影响后面的订单
                refundTransaction.updateRefundOrder(updateRefundSuborderDO);
                continue;
            }
            RefundOrderDO refundOrderDO = refundOrderDao.get(refundSuborderDO.getRfdOrdNo());
            //更新退款子订单为已轮询
            refundTransaction.updateRefundOrder(updateRefundSuborderDO);
            //根据银行结果，更新退款订单和子订单的处理状态
            refundTransaction.updateOrdSts(refundOrderDO, refundSuborderDO.getSubOrdNo(), refundOrderDO.getRfdFailCount(), baseBnkRspBO);
        }
    }

    /**
     * 未支付订单撤销申请
     */
    @Override
    public GenericRspDTO<RefundRspDTO> closeOrder(GenericDTO<RefundReqDTO> genericDTO) {
        //请求参数
        RefundReqDTO refundReqDTO = genericDTO.getBody();
        String corpBusTyp = refundReqDTO.getCorpBusTyp().getType();
        String corpSubBusTyp = refundReqDTO.getCorpBusSubTyp().getType();

        //返回参数
        RefundRspDTO refundRspDTO = new RefundRspDTO();


        //业务合作类型检查
        if (!CorpBusTyp.REFUND.getType().equals(corpBusTyp)) {
            return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd(), refundRspDTO);
        }
        if (!CorpBusSubTyp.FAST_REFUND.getType().equals(corpSubBusTyp) && !CorpBusSubTyp.EBANK_REFUND.getType().equals(corpSubBusTyp)) {
            return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), refundRspDTO);
        }

        //原订单状态检查
        FundOrderDO fundOrderDO = fundOrderDao.get(refundReqDTO.getOrdNo());
        if (JudgeUtils.isNull(fundOrderDO)) {
            logger.error("RefundServiceImpl.closeOrder() 撤销申请，不存在原充值订单，原充值订单号为：" + refundReqDTO.getOrdNo());
            return GenericRspDTO.newInstance(CpiMsgCd.FUND_ORDER_IS_NOT_FOUND.getMsgCd(), refundRspDTO);
        }

//        //原充值订单状态不是等待付款，则不允许撤销
//        if (CpiConstants.ORD_WATING_PAY.equals(fundOrderDO.getOrdSts())) {
//            logger.error("RefundServiceImpl.closeOrder() 撤销申请，原充值订单不是等待付款状态，不允许撤销，原充值订单号为：" + refundReqDTO.getOrdNo());
//            return GenericRspDTO.newInstance(CpiMsgCd.FORBID_REFUND.getMsgCd(), refundRspDTO);
//        }

        //根据原充值订单路由机构，执行不同的撤销方法
        String rutCorpOrg = fundOrderDO.getRutCorpOrg();
        if(JudgeUtils.isEmpty(rutCorpOrg)) {
            logger.error("RefundServiceImpl.closeOrder() 撤销申请，原充值订单路由机构不存在，不允许撤销，原充值订单号为：" + refundReqDTO.getOrdNo());
            return GenericRspDTO.newInstance(CpiMsgCd.FORBID_REFUND.getMsgCd(), refundRspDTO);
        }

        //若路由是微信，则根据商户号查询对应的子商户信息
        SubMercCastDO subMercCastDO = null;
        EbankOrderDO ebankOrderDO = ebankOrderDao.get(fundOrderDO.getFudOrdNo());
        if(JudgeUtils.equals(CpiConstants.WECHAT, rutCorpOrg)) {
            subMercCastDO = subMercCastDao.getSubMercCastDO(weChatProperties.getAppId(), ebankOrderDO.getRcvUserId(), CpiConstants.EFFECT_YES);
            if(JudgeUtils.isNull(subMercCastDO)) {
                logger.error("EbankpayServiceImpl.closeOrder() 路由为微信，商户对应的微信子商户信息不存在");
                return GenericRspDTO.newInstance(CpiMsgCd.FORBID_REFUND.getMsgCd(), refundRspDTO);
            }
        }

        //调用银行订单撤销接口
        BaseBnkRspBO baseBnkRspBO = null;
        boolean supportBank = true;
        switch (rutCorpOrg) {
            //微信
            case CpiConstants.WECHAT:
                baseBnkRspBO = weChatPayNewApi.closeOrder(subMercCastDO, ebankOrderDO);
                break;
            //支付宝
            case CpiConstants.ALIPAY:
                baseBnkRspBO = alipayApi.closeOrder(ebankOrderDO);
                break;
            //不支持的路由机构
            default:
                supportBank = false;
                break;
        }

        //若银行返回结果为空
        if (JudgeUtils.isNull(baseBnkRspBO)) {
            if (!supportBank) {
                logger.error("RefundServiceImpl.refundAdditionalOrder() error: 不支持该路由机构");
            } else {
                logger.error("RefundServiceImpl.refundAdditionalOrder() error: 银行通讯错误，返回报文为空");
            }
            return GenericRspDTO.newInstance(CpiMsgCd.FORBID_REFUND.getMsgCd(), refundRspDTO);
        }

        //若银行返回关闭订单成功
        BeanUtils.copyProperties(refundRspDTO, refundReqDTO);
        GenericRspDTO<RefundRspDTO> genericRspDTO = new GenericRspDTO<>();
        if(JudgeUtils.equals(CpiConstants.BANK_RESPONSE_SUCCESS, baseBnkRspBO.getTxFlg())) {
            //更新原充值订单为已撤销
            FundOrderDO updateFundOrderDO = new FundOrderDO();
            updateFundOrderDO.setFudOrdNo(fundOrderDO.getFudOrdNo());
            updateFundOrderDO.setOrdSts(CpiConstants.ORD_REVOKE);
            fundOrderDao.update(updateFundOrderDO);

            //更新原网银订单为已撤销
            EbankOrderDO updateEbankOrderDO = new EbankOrderDO();
            updateEbankOrderDO.setFndOrdNo(fundOrderDO.getFudOrdNo());
            updateEbankOrderDO.setOrdSts(CpiConstants.ORD_REVOKE);
            ebankOrderDao.update(updateEbankOrderDO);

            //返回结果
            refundRspDTO.setOrdSts(CpiConstants.ORD_REVOKE);
            genericRspDTO.setBody(refundRspDTO);
            genericRspDTO.setMsgCd(CpiMsgCd.SUCCESS.getMsgCd());
        } else {
            genericRspDTO.setMsgCd(CpiMsgCd.FORBID_REFUND.getMsgCd());
        }
        return genericRspDTO;
    }

}
