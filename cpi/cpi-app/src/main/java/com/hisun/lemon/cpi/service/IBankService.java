package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.TransferAcledaReqDTO;
import com.hisun.lemon.cpi.dto.TransferAcledaRspDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;

/**
 * Created by Rui on 2017/7/7.
 */
public interface IBankService {

    /**
     * acleda银行转账，获取交易ID和支付ID
     */
    public GenericRspDTO<TransferAcledaRspDTO> transferAcledaOpen(GenericDTO<TransferAcledaReqDTO> genericDTO);


}
