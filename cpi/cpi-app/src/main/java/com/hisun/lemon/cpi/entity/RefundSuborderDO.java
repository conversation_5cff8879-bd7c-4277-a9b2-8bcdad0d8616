/*
 * @ClassName RefundSuborderDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-17 19:05:34
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class RefundSuborderDO extends BaseDO {
    /**
     * @Fields subOrdNo 内部子订单号
     */
    private String subOrdNo;
    /**
     * @Fields rfdOrdNo 内部订单号
     */
    private String rfdOrdNo;
    /**
     * @Fields fndOrdNo 充值订单号
     */
    private String fndOrdNo;
    /**
     * @Fields fndSubOrdNo 充值子订单号
     */
    private String fndSubOrdNo;
    /**
     * @Fields ordDt 订单日期
     */
    private LocalDate ordDt;
    /**
     * @Fields ordTm 订单时间
     */
    private LocalTime ordTm;
    /**
     * @Fields crdCorpOrg 资金合作机构号
     */
    private String crdCorpOrg;
    /**
     * @Fields rutCorpOrg 路径合作机构号
     */
    private String rutCorpOrg;
    /**
     * @Fields copBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields copBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields ordAmt 订单金额
     */
    private BigDecimal ordAmt;
    /**
     * @Fields ordSts 订单状态，W1：等待充值，S1：交易成功，E1：交易逾期，F1：交易失败
     */
    private String ordSts;
    /**
     * @Fields acDt 记账日期
     */
    private LocalDate acDt;
    /**
     * @Fields orgOrdNo 合作机构外部订单号（系统生成）
     */
    private String orgOrdNo;
    /**
     * @Fields orgJrnNo 合作机构返回的流水号
     */
    private String orgJrnNo;
    /**
     * @Fields orgOrdDt 合作机构交易日期
     */
    private LocalDate orgOrdDt;
    /**
     * @Fields orgOrdTm 合作机构交易时间
     */
    private LocalTime orgOrdTm;
    /**
     * @Fields qrySts 结果查询状态，W：未查询，U：未明，S：成功，F：失败
     */
    private String qrySts;
    /**
     * @Fields qryRspCd 结果查询返回码
     */
    private String qryRspCd;
    /**
     * @Fields qryRspMsg 结果查询返回信息
     */
    private String qryRspMsg;
    /**
     * @Fields orgRspCd 合作机构返回码
     */
    private String orgRspCd;
    /**
     * @Fields orgRspMsg 合作机构返回信息
     */
    private String orgRspMsg;
    /**
     * @Fields ntfSts 合作机构通知状态，U：未明，S：成功，F：失败
     */
    private String ntfSts;
    /**
     * @Fields chkKey 对账键值
     */
    private String chkKey;
    /**
     * @Fields chkFlg 对账标志，0：需要对账，1：不需要对账
     */
    private String chkFlg;
    /**
     * @Fields chkSts 对账状态，0：未对账，1：对账成功，2：我方有对方无，3：对方有我方无，4：金额错，5：存疑
     */
    private String chkSts;
    /**
     * @Fields chkDt 对账日期
     */
    private LocalDate chkDt;
    /**
     * @Fields chkTm 对账时间
     */
    private LocalTime chkTm;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    /**
     * 订单查询银行接口标志 0-未查询；1-已查询
     */
    private String splOrdFlg;

    public String getSubOrdNo() {
        return subOrdNo;
    }

    public void setSubOrdNo(String subOrdNo) {
        this.subOrdNo = subOrdNo;
    }

    public String getRfdOrdNo() {
        return rfdOrdNo;
    }

    public void setRfdOrdNo(String rfdOrdNo) {
        this.rfdOrdNo = rfdOrdNo;
    }

    public String getFndOrdNo() {
        return fndOrdNo;
    }

    public void setFndOrdNo(String fndOrdNo) {
        this.fndOrdNo = fndOrdNo;
    }

    public String getFndSubOrdNo() {
        return fndSubOrdNo;
    }

    public void setFndSubOrdNo(String fndSubOrdNo) {
        this.fndSubOrdNo = fndSubOrdNo;
    }

    public LocalDate getOrdDt() {
        return ordDt;
    }

    public void setOrdDt(LocalDate ordDt) {
        this.ordDt = ordDt;
    }

    public LocalTime getOrdTm() {
        return ordTm;
    }

    public void setOrdTm(LocalTime ordTm) {
        this.ordTm = ordTm;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public BigDecimal getOrdAmt() {
        return ordAmt;
    }

    public void setOrdAmt(BigDecimal ordAmt) {
        this.ordAmt = ordAmt;
    }

    public String getOrdSts() {
        return ordSts;
    }

    public void setOrdSts(String ordSts) {
        this.ordSts = ordSts;
    }

    public LocalDate getAcDt() {
        return acDt;
    }

    public void setAcDt(LocalDate acDt) {
        this.acDt = acDt;
    }

    public String getOrgOrdNo() {
        return orgOrdNo;
    }

    public void setOrgOrdNo(String orgOrdNo) {
        this.orgOrdNo = orgOrdNo;
    }

    public String getOrgJrnNo() {
        return orgJrnNo;
    }

    public void setOrgJrnNo(String orgJrnNo) {
        this.orgJrnNo = orgJrnNo;
    }

    public LocalDate getOrgOrdDt() {
        return orgOrdDt;
    }

    public void setOrgOrdDt(LocalDate orgOrdDt) {
        this.orgOrdDt = orgOrdDt;
    }

    public LocalTime getOrgOrdTm() {
        return orgOrdTm;
    }

    public void setOrgOrdTm(LocalTime orgOrdTm) {
        this.orgOrdTm = orgOrdTm;
    }

    public String getQrySts() {
        return qrySts;
    }

    public void setQrySts(String qrySts) {
        this.qrySts = qrySts;
    }

    public String getQryRspCd() {
        return qryRspCd;
    }

    public void setQryRspCd(String qryRspCd) {
        this.qryRspCd = qryRspCd;
    }

    public String getQryRspMsg() {
        return qryRspMsg;
    }

    public void setQryRspMsg(String qryRspMsg) {
        this.qryRspMsg = qryRspMsg;
    }

    public String getOrgRspCd() {
        return orgRspCd;
    }

    public void setOrgRspCd(String orgRspCd) {
        this.orgRspCd = orgRspCd;
    }

    public String getOrgRspMsg() {
        return orgRspMsg;
    }

    public void setOrgRspMsg(String orgRspMsg) {
        this.orgRspMsg = orgRspMsg;
    }

    public String getNtfSts() {
        return ntfSts;
    }

    public void setNtfSts(String ntfSts) {
        this.ntfSts = ntfSts;
    }

    public String getChkKey() {
        return chkKey;
    }

    public void setChkKey(String chkKey) {
        this.chkKey = chkKey;
    }

    public String getChkFlg() {
        return chkFlg;
    }

    public void setChkFlg(String chkFlg) {
        this.chkFlg = chkFlg;
    }

    public String getChkSts() {
        return chkSts;
    }

    public void setChkSts(String chkSts) {
        this.chkSts = chkSts;
    }

    public LocalDate getChkDt() {
        return chkDt;
    }

    public void setChkDt(LocalDate chkDt) {
        this.chkDt = chkDt;
    }

    public LocalTime getChkTm() {
        return chkTm;
    }

    public void setChkTm(LocalTime chkTm) {
        this.chkTm = chkTm;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getSplOrdFlg() {
        return splOrdFlg;
    }

    public void setSplOrdFlg(String splOrdFlg) {
        this.splOrdFlg = splOrdFlg;
    }
}