package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.RefundOrderDO;
import com.hisun.lemon.cpi.entity.RefundSuborderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
/*
 * @ClassName IRefundSuborderDao
 * @Description
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
@Mapper
public interface IRefundSuborderDao extends BaseDao<RefundSuborderDO> {

    /**
     * 根据唯一索引查询退款子表信息
     * @param rfdOrdNo
     * @return
     */
    RefundSuborderDO selectByUniqueKey(@Param("rfdOrdNo") String rfdOrdNo);

    /**
     * 查询原充值订单已退款
     * @param fndOrdNo
     * @return
     */
    BigDecimal selectSumRfdAmt(@Param("fndOrdNo") String fndOrdNo);

    /**
     * 根据对账键值，查询退款子订单信息
     */
    RefundSuborderDO selectRefundSuborderByChkKey(String chkKey);

    /**
     * 根据对账键值，更新退款子订单的对账状态、对账时间等
     */
    void updateRefundSuborderByChkKey(RefundSuborderDO refundSuborderDO);

    /**
     * 查询我方ORD_STS='S1'(交易成功)、CHK_STS='5'(存疑)的退款子订单订单，(ORD_DT)订单日期小于对账日期
     */
    List<RefundSuborderDO> getRefundSuborderListByChkStsDoubt(@Param("chkSts")String chkSts, @Param("chkDt")LocalDate chkDt);

    /**
     * 将我方ORD_STS='S1'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'，(ORD_DT)订单日期小于对账日期
     */
    void updateRefundSuborderByChkStsDoubt(@Param("oldChkSts")String oldChkSts, @Param("newChkSts")String newChkSts, @Param("chkDt")LocalDate chkDt, @Param("chkTm")LocalTime chkTm);

    /**
     * 查询我方ORD_STS='S'、CHK_STS='0'(未对账)的订单，订单日期等于对账日期
     */
    List<RefundSuborderDO> getRefundSuborderListByChkStsNotstart(@Param("chkSts")String chkSts, @Param("chkDt")LocalDate chkDt);

    /**
     * 将我方ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)，订单日期等于对账日期
     */
    void updateRefundSuborderByChkStsNotstart(@Param("oldChkSts")String oldChkSts, @Param("newChkSts")String newChkSts, @Param("chkDt")LocalDate chkDt, @Param("chkTm")LocalTime chkTm);

    /**
     * 查询退款受理成功、银行处理中、一个小时之前的网银退款订单
     */
    List<RefundSuborderDO> selectRefundAddtionalOrder(@Param("refundSuborderDO")RefundSuborderDO refundSuborderDO, @Param("num")int num);

    /**
     * 根据路由机构查询指定日期是否有申请成功的退款订单
     */
    Integer getSuccessfulRefundOrderNum(@Param("rutCorpOrg")String rutCorpOrg, @Param("chkFilDt")LocalDate chkFilDt);

    /**
     * 根据订单日期查询状态为银行处理中，并且为对账状态也为未对账或存疑的退款订单
     */
    List<RefundSuborderDO> getRefundOrderByDate(@Param("ordDt") LocalDate ordDt);

    /**
     * 根据订单日期和指定的资金机构查询状态为银行处理中，并且为对账状态也为未对账或存疑的退款订单
     */
    List<RefundSuborderDO> getRefundOrderByDateAndRutCorg(@Param("ordDt") LocalDate ordDt,@Param("rutCorpOrg")String rutCorpOrg);
}