package com.hisun.lemon.cpi.utils;


import java.util.Locale;
import java.util.regex.Pattern;

public class MobileUtils {
    /** 手机号码基础校验工具类 */
 //   private static PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
    /** 手机号码运营商扩展工具类 */
//    private static PhoneNumberToCarrierMapper carrierMapper = PhoneNumberToCarrierMapper.getInstance();
    /** 手机号码归属地扩展工具类 */
//    private static PhoneNumberOfflineGeocoder geocoder = PhoneNumberOfflineGeocoder.getInstance();
    /** 手机号格式校验正则匹配规则 */
    private static final String FORMATREGEX = "^\\+\\d+\\-\\d+";
    /** 加号 */
    private static final String PLUS = "+";
    /** 中划线 */
    private static final String STRIKE = "-";
    /** 错误返回 */
    private static final String ERROR = "-1";


    /**
     * @Description 获取手机号码，去除+,-,去除区号后首位带0
     * <AUTHOR>
     * @param phoneNumber
     * @return
     */
    public static String getMobile(String phoneNumber) {
        // 校验格式是否为+86-18684830734格式
        boolean isFormat = formatValid(phoneNumber);
        if (!isFormat) {
            return ERROR;
        }
        String[] temp = phoneNumber.split(STRIKE);
        String countryCode = temp[0].substring(temp[0].indexOf(PLUS) + 1);
        System.out.println(countryCode);
        temp = phoneNumber.split(STRIKE);
        String mobile = temp[1].substring(temp[1].indexOf(STRIKE) + 1);
        System.out.println(mobile);
        if(mobile.substring(0,1).equals("0")){
            mobile = mobile.substring(1,mobile.length());
        }
        return countryCode.concat(mobile);
    }

    /**
     * @Description 校验手机号格式
     * <AUTHOR>
     * @param phoneNumber
     * @return
     */
    public static boolean formatValid(String phoneNumber) {
        return Pattern.matches(FORMATREGEX, phoneNumber);
    }

}
