package com.hisun.lemon.cpi.bnkapi.alipay;

public class AlipayConstants {

	// ↓↓↓↓↓↓↓↓↓↓请在这里配置您的基本信息↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
	// 合作身份者ID，以2088开头由16位纯数字组成的字符串
//	public static String PARTNER = "2088621887303624";
//	// 商户的私钥
//	public static String KEY = "vyptxdsf0xt2nbl5pruiu89p30vkaacc";

	public static String PARTNER = "2088621889514248";
	// 商户的私钥
	public static String KEY = "t5zdi9f8ld33ndw6alblg24ncph7v053";
	// ↑↑↑↑↑↑↑↑↑↑请在这里配置您的基本信息↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑


	// 字符编码格式 目前支持 gbk 或 utf-8
	public static String INPUT_CHARSET = "UTF-8";

	// 签名方式 不需修改
	public static String SIGN_TYPE = "MD5";

	public static final String API_URL = "https://openapi.alipaydev.com/gateway.do";
	public static final String FAIL = "FAIL";
	public static final String SUCCESS = "SUCCESS";
	public static final String HMACSHA256 = "HMAC-SHA256";

	public static final String FIELD_SIGN = "sign";
	public static final String FIELD_SIGN_TYPE = "sign_type";
	public static final String NOTIFY_URL = "http://bestpay.bestmpay.com/cpi/alipay/placeOrderNotify";
}
