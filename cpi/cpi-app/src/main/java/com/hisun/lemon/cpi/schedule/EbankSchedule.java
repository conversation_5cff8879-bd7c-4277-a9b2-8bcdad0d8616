package com.hisun.lemon.cpi.schedule;

import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.service.IEbankpayService;
import com.hisun.lemon.cpi.service.IFundPollParamService;
import com.hisun.lemon.cpi.thread.EbankFundQueryThread;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.schedule.batch.BatchScheduled;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by Rui on 2017/7/17.
 */
@Component
public class EbankSchedule {

    @Resource
    private IFundPollParamService fundPollParamService;
    @Resource
    private IEbankpayService ebankpayService;
    @Resource
    private TaskExecutor taskExecutor;
    @Resource
    private DistributedLocker distributedLocker;

    /**
     * 每分钟执行一次
     * cronExpression表达式规则：
     * "秒(0-59) 分(0-59) 时(0-23) 日(1-31) 月(1-12 或者 JAN-DEC) 星期(1-7 或者 SUN-SAT) 年(可选:留空或1970-2099)"
     * "0 0 12 * * ?" 每天中午12点触发 “*”字符被用来指定所有的值。如：”*“在分钟的字段域里表示“每分钟”
     * “-”字符被用来指定一个范围。如：“10-12”在小时域意味着“10点、11点、12点”
     * “,”字符被用来指定另外的值。如：“MON,WED,FRI”在星期域里表示”星期一、星期三、星期五” “?”字符只在日期域和星期域中使用，用来占位
     */
    @BatchScheduled(cron = "0/5 * * * * ?")
    public void additionalOrder () {
        List<FundPollParamDO> list = fundPollParamService.queryAllFund();
        if(CollectionUtils.isNotEmpty(list)) {
            for (FundPollParamDO fundPollParamDO : list) {
                //异步调起银行查询接口
                taskExecutor.execute(new EbankFundQueryThread(ebankpayService,distributedLocker,fundPollParamDO));
            }
        }
    }
}
