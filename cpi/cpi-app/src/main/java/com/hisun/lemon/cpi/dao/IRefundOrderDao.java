/*
 * @ClassName IRefundOrderDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-14 11:17:09
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.RefundOrderDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface IRefundOrderDao extends BaseDao<RefundOrderDO> {

    /**
     * 查询待付款的数据
     * @param refundOrderDO
     * @param offset
     * @param number
     * @return
     */
    List<RefundOrderDO> queryAwaitingPayment(@Param("refundOrderDO") RefundOrderDO refundOrderDO, @Param("offset") int offset, @Param("number") int number);

    /**
     * 获取退款明细 List
     * @param ordDt 订单日期
     * @param beginNum 从表中第几行开始取值
     * @param countNum 最大读取多少行记录
     */
    List<RefundOrderDO> getRefundOrderList(@Param("ordDt") LocalDate ordDt, @Param("beginNum")Integer beginNum, @Param("countNum")Integer countNum);

    /**
     * 分页查询待通知的退款订单
     */
    List<RefundOrderDO> getRefundOrderToNotify(@Param("beginNum")int beginNum, @Param("maxQueryNum") int maxQueryNum);

}