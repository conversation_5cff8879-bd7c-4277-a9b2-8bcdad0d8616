package com.hisun.lemon.cpi.bnkapi.icbc;

import com.hisun.lemon.cpi.common.CpiMsgCd;
import org.springframework.stereotype.Component;

/**
 * User : Rui
 * Date : 2017/9/1
 * Time : 12:00
 **/
@Component("icbcRetCdConvert")
public class ICBCRetCdConvert {

    public CpiMsgCd Convert(String retCode){
        switch (retCode){
            case "0":
                return CpiMsgCd.SUCCESS;
            case "2301" :
            case "5919":
                return CpiMsgCd.BNK_70001;
            case "2037" :
                return CpiMsgCd.BNK_70002;
            case "4101" :
            case "4102" :
                return CpiMsgCd.BNK_70003;
            case "98000919" :
            case "98000920" :
            case "98000960":
                return CpiMsgCd.BNK_70004;
            case "2303":
            case "98000935":
            case "98000941":
            case "98000964":
                return CpiMsgCd.BNK_70005;
            case "2043" :
            case "98000961" :
                return CpiMsgCd.BNK_70006;
            case "2323" :
            case "2252" :
            case "98000938" :
            case "98000962" :
                return CpiMsgCd.BNK_70007;
            case "2003" :
                return CpiMsgCd.BNK_70008;
            case "2031" :
            case "98000918" :
            case "98000939" :
            case "98000963" :
                return CpiMsgCd.BNK_70009;
            case "98000925" :
            case "98000943" :
                return CpiMsgCd.BNK_70010;
            case "98000922":
            case "98000940":
                return CpiMsgCd.BNK_70011;
            case "98000923" :
                return CpiMsgCd.BNK_70012;
            case "98000928" :
                return CpiMsgCd.BNK_70013;
            case "98000930" :
                return CpiMsgCd.BNK_70014;
            case "98000931" :
                return CpiMsgCd.BNK_70015;
            case "98000937" :
                return CpiMsgCd.BNK_70016;
            case "98000942" :
                return CpiMsgCd.BNK_70017;
            case "98000924" :
                return CpiMsgCd.BNK_70018;
            case "98000944" :
                return CpiMsgCd.BNK_70019;
            case "98000945" :
                return CpiMsgCd.BNK_70020;
            case "98000949" :
                return CpiMsgCd.BNK_70021;
            case "98000966" :
                return CpiMsgCd.BNK_70022;
            case "98000967" :
                return CpiMsgCd.BNK_70023;
            case "98000979" :
                return CpiMsgCd.BNK_70024;
            case "98001669" :
            case "98001670" :
            case "98001672" :
            case "98001674" :
                return CpiMsgCd.BNK_70025;
            case "98001671" :
            case "98001673" :
            case "98001675" :
                return CpiMsgCd.BNK_70026;
            case "98000927" :
                return CpiMsgCd.BNK_70027;
            default:
                return CpiMsgCd.BNK_70099;
        }
    }
}
