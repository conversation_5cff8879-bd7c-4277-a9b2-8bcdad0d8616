/*
 * @ClassName ICardProtDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.CardProtDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ICardProtDao extends BaseDao<CardProtDO> {

    List<CardProtDO> selectByCorpBusTyp(@Param("userId") String userId, @Param("corpBusTyp") String corpBusTyp,
                                        @Param("corpBusSubTyp") String corpBusSubTyp, @Param("crdAcTyp")String crdAcTyp);

    List<CardProtDO> selectBindCard(@Param("userId")String userId,@Param("crdNoEnc")String crdNoEnc);
}