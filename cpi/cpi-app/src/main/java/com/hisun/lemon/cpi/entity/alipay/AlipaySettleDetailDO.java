/*
 * @ClassName AlipaySettleDetailDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-17 11:04:50
 */
package com.hisun.lemon.cpi.entity.alipay;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;

public class AlipaySettleDetailDO extends BaseDO {
    /**
     * @Fields partnerTransactionId 平台请求支付宝生成的订单号
     */
    private String partnerTransactionId;
    /**
     * @Fields transactionId 支付宝平台的唯一订单号
     */
    private String transactionId;
    /**
     * @Fields amount 充值交易外币金额
     */
    private BigDecimal amount;
    /**
     * @Fields rmbAmount 充值交易人民币金额
     */
    private BigDecimal rmbAmount;
    /**
     * @Fields fee 手续费
     */
    private BigDecimal fee;
    /**
     * @Fields settlement 外币结算金额
     */
    private BigDecimal settlement;
    /**
     * @Fields rmbSettlement 人民币结算金额
     */
    private BigDecimal rmbSettlement;
    /**
     * @Fields paymentTime 支付时间
     */
    private String paymentTime;
    /**
     * @Fields settleTime 结算时间
     */
    private String settleTime;
    /**
     * @Fields type 交易类型：正常：P, 退款：R
     */
    private String type;
    /**
     * @Fields status 状态：正常交易：L 已清算, 退款交易：L 已清算
     */
    private String status;
    /**
     * @Fields currency 币种
     */
    private String currency;
    /**
     * @Fields rate 币种汇率
     */
    private String rate;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields secondaryMerchantIndustry 行业分类标识符
     */
    private String secondaryMerchantIndustry;
    /**
     * @Fields secondaryMerchantName 二级商户名称
     */
    private String secondaryMerchantName;
    /**
     * @Fields operatorName 操作员名字
     */
    private String operatorName;
    /**
     * @Fields orderScene 订单场景交易场景
     */
    private String orderScene;
    /**
     * @Fields transCurrency 货币列表
     */
    private String transCurrency;
    /**
     * @Fields transForexRate 交易费率
     */
    private String transForexRate;
    /**
     * @Fields transAmount 货币列表总金额
     */
    private BigDecimal transAmount;

    public String getPartnerTransactionId() {
        return partnerTransactionId;
    }

    public void setPartnerTransactionId(String partnerTransactionId) {
        this.partnerTransactionId = partnerTransactionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getRmbAmount() {
        return rmbAmount;
    }

    public void setRmbAmount(BigDecimal rmbAmount) {
        this.rmbAmount = rmbAmount;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getSettlement() {
        return settlement;
    }

    public void setSettlement(BigDecimal settlement) {
        this.settlement = settlement;
    }

    public BigDecimal getRmbSettlement() {
        return rmbSettlement;
    }

    public void setRmbSettlement(BigDecimal rmbSettlement) {
        this.rmbSettlement = rmbSettlement;
    }

    public String getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(String paymentTime) {
        this.paymentTime = paymentTime;
    }

    public String getSettleTime() {
        return settleTime;
    }

    public void setSettleTime(String settleTime) {
        this.settleTime = settleTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSecondaryMerchantIndustry() {
        return secondaryMerchantIndustry;
    }

    public void setSecondaryMerchantIndustry(String secondaryMerchantIndustry) {
        this.secondaryMerchantIndustry = secondaryMerchantIndustry;
    }

    public String getSecondaryMerchantName() {
        return secondaryMerchantName;
    }

    public void setSecondaryMerchantName(String secondaryMerchantName) {
        this.secondaryMerchantName = secondaryMerchantName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOrderScene() {
        return orderScene;
    }

    public void setOrderScene(String orderScene) {
        this.orderScene = orderScene;
    }

    public String getTransCurrency() {
        return transCurrency;
    }

    public void setTransCurrency(String transCurrency) {
        this.transCurrency = transCurrency;
    }

    public String getTransForexRate() {
        return transForexRate;
    }

    public void setTransForexRate(String transForexRate) {
        this.transForexRate = transForexRate;
    }

    public BigDecimal getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(BigDecimal transAmount) {
        this.transAmount = transAmount;
    }

    @Override
    public String toString() {
        return "AlipaySettleDetailDO{" +
                "partnerTransactionId='" + partnerTransactionId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", amount=" + amount +
                ", rmbAmount=" + rmbAmount +
                ", fee=" + fee +
                ", settlement=" + settlement +
                ", rmbSettlement=" + rmbSettlement +
                ", paymentTime='" + paymentTime + '\'' +
                ", settleTime='" + settleTime + '\'' +
                ", type='" + type + '\'' +
                ", status='" + status + '\'' +
                ", currency='" + currency + '\'' +
                ", rate='" + rate + '\'' +
                ", remark='" + remark + '\'' +
                ", secondaryMerchantIndustry='" + secondaryMerchantIndustry + '\'' +
                ", secondaryMerchantName='" + secondaryMerchantName + '\'' +
                ", operatorName='" + operatorName + '\'' +
                ", orderScene='" + orderScene + '\'' +
                ", transCurrency='" + transCurrency + '\'' +
                ", transForexRate='" + transForexRate + '\'' +
                ", transAmount=" + transAmount +
                '}';
    }
}