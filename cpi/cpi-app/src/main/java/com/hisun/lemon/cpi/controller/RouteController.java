package com.hisun.lemon.cpi.controller;

import com.hisun.lemon.cpi.dto.RouteRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.IRouteService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/cpi/route")
@Api(tags="RouteController", description="路由服务")
public class RouteController extends BaseController {

    @Resource
    IRouteService routeService;

    @ApiOperation(value="根据类型查询生效的合作资金机构信息", notes="根据类型查询生效的合作资金机构信息")
    @ApiResponse(code = 200, message = "查询结果")
    @GetMapping("/result")
    public GenericRspDTO<RouteRspDTO> queryEffOrgInfo(@Validated @ApiParam(name = "corpBusTyp", value = "业务类型", required = true) @RequestParam(value = "corpBusTyp") CorpBusTyp corpBusTyp,
                                                      @Validated @ApiParam(name = "corpBusSubTyp", value = "业务子类型", required = true) @RequestParam(value = "corpBusSubTyp") CorpBusSubTyp corpBusSubTyp) {
        return routeService.queryEffOrgInfo(corpBusTyp,corpBusSubTyp);
    }
}
