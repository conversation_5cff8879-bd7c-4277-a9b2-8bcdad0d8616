package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.constants.ACMMessageCode;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.*;
import com.hisun.lemon.cpi.dto.CheckDTO;
import com.hisun.lemon.cpi.dto.RefundReqDTO;
import com.hisun.lemon.cpi.dto.RefundRspDTO;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.ICheckService;
import com.hisun.lemon.cpi.service.IRefundService;
import com.hisun.lemon.cpi.utils.AccountUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class CheckServiceImpl extends BaseService implements ICheckService {

    private static final Logger logger = LoggerFactory.getLogger(CheckServiceImpl.class);

    @Resource
    IFundOrderDao fundOrderDao;
    @Resource
    IShortcutOrderDao shortcutOrderDao;
    @Resource
    IEbankOrderDao ebankOrderDao;
    @Resource
    IRefundOrderDao refundOrderDao;
    @Resource
    IRefundSuborderDao refundSuborderDao;
    @Resource
    AccountingTreatmentClient accTreatClient;
    @Resource
    IRefundService refundService;
    @Resource
    AccountManagementClient accManageClint;

    @Override
    public GenericRspDTO<NoBody> additionalOrder(GenericDTO<CheckDTO> genericDTO) {
        //获取body
        CheckDTO checkDTO = genericDTO.getBody();

        try {
            String msgCd = CpiMsgCd.SUCCESS.getMsgCd();
            //根据类型去补单
            switch (checkDTO.getCorpBusTyp()) {
                case FASTPAY:
                    msgCd = this.fundAddtionalOrder(checkDTO,CorpBusTyp.FASTPAY);
                    break;
                case EBANKPAY:
                    msgCd = this.fundAddtionalOrder(checkDTO,CorpBusTyp.EBANKPAY);
                    break;
                case REFUND:
                    msgCd = CpiMsgCd.REFUND_ORDER_NOT_ALLOW_ADDITION.getMsgCd();
                    break;
                default:
                    msgCd = CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
            }
            return GenericRspDTO.newInstance(msgCd);
        } catch (Exception e) {
            logger.error("CheckServiceImpl.additionalOrder :", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
    }

    @Override
    public GenericRspDTO<NoBody> cancelOrder(GenericDTO<CheckDTO> genericDTO) {
        //获取body
        CheckDTO checkDTO = genericDTO.getBody();

        try {
            String msgCd = CpiMsgCd.SUCCESS.getMsgCd();
            //根据类型去撤单
            switch (checkDTO.getCorpBusTyp()) {
                case FASTPAY:
                    msgCd = this.fundCancelOrder(checkDTO,CorpBusTyp.FASTPAY);
                    break;
                case EBANKPAY:
                    msgCd = this.fundCancelOrder(checkDTO,CorpBusTyp.EBANKPAY);
                    break;
                case REFUND:
                    msgCd = this.refundCancelOrder(checkDTO);
                    break;
                default:
                    msgCd = CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
            }
            return GenericRspDTO.newInstance(msgCd);
        } catch (Exception e) {
            logger.error("CheckServiceImpl.cancelOrder :", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
    }

    @Override
    public GenericRspDTO<NoBody> matchHandle(GenericDTO<CheckDTO> genericDTO) {
        //获取body
        CheckDTO checkDTO = genericDTO.getBody();

        try {
            String msgCd = CpiMsgCd.SUCCESS.getMsgCd();
            //根据类型去差错取消
            switch (checkDTO.getCorpBusTyp()) {
                case FASTPAY:
                    msgCd = this.fundMatchHandle(checkDTO);
                    break;
                case EBANKPAY:
                    msgCd = this.fundMatchHandle(checkDTO);
                    break;
                default:
                    msgCd = CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
            }
            return GenericRspDTO.newInstance(msgCd);
        } catch (Exception e) {
            logger.error("CheckServiceImpl.matchHandle :", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
    }

    @Override
    public GenericRspDTO<NoBody> cancelError(GenericDTO<CheckDTO> genericDTO) {
        //获取body
        CheckDTO checkDTO = genericDTO.getBody();

        try {
            String msgCd = CpiMsgCd.SUCCESS.getMsgCd();
            //根据类型去差错取消
            switch (checkDTO.getCorpBusTyp()) {
                case FASTPAY:
                    msgCd = this.fundCancelError(checkDTO,CorpBusTyp.FASTPAY);
                    break;
                case EBANKPAY:
                    msgCd = this.fundCancelError(checkDTO,CorpBusTyp.EBANKPAY);
                    break;
                case REFUND:
                    msgCd = this.refundCancelError(checkDTO);
                    break;
                default:
                    msgCd = CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
            }
            return GenericRspDTO.newInstance(msgCd);
        } catch (Exception e) {
            logger.error("CheckUiSereviceImpl.cancelError :", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
    }

    /**
     * 充值订单补单
     * @param checkDTO
     * @param corpBusTyp
     * @return
     * @throws Exception
     */
    private String fundAddtionalOrder(CheckDTO checkDTO,CorpBusTyp corpBusTyp) throws Exception{
        //根据对账原订单流水查询订单
        ShortcutOrderDO shortcutOrderDO = null;
        EbankOrderDO ebankOrderDO = null;
        String fndOrdNo = null;
        String subOrdNo = null;
        if(CorpBusTyp.FASTPAY.equals(corpBusTyp)){
            shortcutOrderDO = shortcutOrderDao.selectShortcutOrderByChkKey(checkDTO.getChkKey());
            if (JudgeUtils.isNull(shortcutOrderDO)) {
                return CpiMsgCd.ORIGINAL_SHORTCUT_ORDER_IS_NOT_FUND.getMsgCd();
            }
            fndOrdNo = shortcutOrderDO.getFndOrdNo();
            subOrdNo = shortcutOrderDO.getSubOrdNo();
        }else if(CorpBusTyp.EBANKPAY.equals(corpBusTyp)){
            ebankOrderDO = ebankOrderDao.selectEbankpayOrderByChkKey(checkDTO.getChkKey());
            if (JudgeUtils.isNull(ebankOrderDO)) {
                return CpiMsgCd.ORIGINAL_EBANK_ORDER_IS_NOT_FUND.getMsgCd();
            }
            fndOrdNo = ebankOrderDO.getFndOrdNo();
            subOrdNo = ebankOrderDO.getSubOrdNo();
        }else {
            return CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
        }

        FundOrderDO fundOrderDO = fundOrderDao.get(fndOrdNo);
        if (JudgeUtils.isNull(fundOrderDO)) {
            return CpiMsgCd.ORIGINAL_ORDER_IS_NOT_FUND.getMsgCd();
        }

        //更新订单状态
        FundOrderDO updateFundOrder = new FundOrderDO();
        updateFundOrder.setFudOrdNo(fundOrderDO.getFudOrdNo());
        updateFundOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
        if(CorpBusTyp.FASTPAY.equals(corpBusTyp)) {
            ShortcutOrderDO updateShortcutOrder = new ShortcutOrderDO();
            updateShortcutOrder.setSubOrdNo(shortcutOrderDO.getSubOrdNo());
            updateShortcutOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
            shortcutOrderDao.update(updateShortcutOrder);
        }else if(CorpBusTyp.EBANKPAY.equals(corpBusTyp)){
            EbankOrderDO updateEbankOrder = new EbankOrderDO();
            updateEbankOrder.setSubOrdNo(ebankOrderDO.getSubOrdNo());
            updateEbankOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
            ebankOrderDao.update(updateEbankOrder);
        }else {
            return CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
        }
        fundOrderDao.update(updateFundOrder);


        //发起退款
        GenericDTO<RefundReqDTO> refundReqDTO = new GenericDTO<>();
        RefundReqDTO refundDTO = new RefundReqDTO();
        refundDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
        refundDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());
        refundDTO.setReqOrdNo(LemonUtils.getRequestId());
        refundDTO.setCorpBusTyp(CorpBusTyp.REFUND);
        if(CorpBusTyp.FASTPAY.equals(corpBusTyp)) {
            refundDTO.setCorpBusSubTyp(CorpBusSubTyp.FAST_REFUND);
        }else if(CorpBusTyp.EBANKPAY.equals(corpBusTyp)){
            refundDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANK_REFUND);
        }else{
            return CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
        }
        refundDTO.setInnerFlag("1");
        refundDTO.setOrdAmt(checkDTO.getChkAmt());
        refundDTO.setOrdCcy(fundOrderDO.getOrdCcy());
        refundDTO.setOrdNo(fundOrderDO.getFudOrdNo());
        refundDTO.setUserNo(fundOrderDO.getUserId());
        refundDTO.setAgrPayDt(DateTimeUtils.getCurrentLocalDate());
        refundReqDTO.setBody(refundDTO);
        GenericRspDTO<RefundRspDTO> refundRspDTO = refundService.createOrder(refundReqDTO);
        if(JudgeUtils.isNotSuccess(refundRspDTO.getMsgCd())){
            throw new LemonException(refundRspDTO.getMsgCd());
        }

        //调用账务处理
        GenericDTO<List<AccountingReqDTO>> genericDTO = new GenericDTO<>();
        List<AccountingReqDTO> list = new ArrayList<>();
        String rutCorpOrg = fundOrderDO.getRutCorpOrg();
        String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPI", 6);

        //借： 应收账款-渠道充值-XX银行
        AccountingReqDTO accReqInf1 = AccountUtils.getAccountingReqDTO("N","I","","1",AccountUtils.getReceiveItemNo2(rutCorpOrg),CpiConstants.TX_TYP_RECHARGE,
                checkDTO.getChkAmt(), "D",txJrnNo, subOrdNo,"","","","","");

        //贷：其他应付款-差异账-现金账户
        AccountingReqDTO accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1","**********",CpiConstants.TX_TYP_RECHARGE,
                checkDTO.getChkAmt(), "C",txJrnNo, subOrdNo,"","","","","");

        //借： 其他应付款-差异账-现金账户
        AccountingReqDTO accReqInf3 = AccountUtils.getAccountingReqDTO("N","I","","1","**********",CpiConstants.TX_TYP_REFUNDS,
                checkDTO.getChkAmt(), "D",txJrnNo, subOrdNo,"","","","","");

        //贷：其他应付款-渠道退款-XX银行
        AccountingReqDTO accReqInf4 = AccountUtils.getAccountingReqDTO("N","I","","1",AccountUtils.getOtherPayItemNo1(rutCorpOrg),CpiConstants.TX_TYP_REFUNDS,
                checkDTO.getChkAmt(), "C",txJrnNo, subOrdNo,"","","","","");

        list.add(accReqInf1);
        list.add(accReqInf2);
        list.add(accReqInf3);
        list.add(accReqInf4);
        genericDTO.setBody(list);
        GenericRspDTO<NoBody> rspDTO = accTreatClient.accountingTreatment(genericDTO);
        //判断账务处理是否成功，不成功退出
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            throw new LemonException(rspDTO.getMsgCd());
        }

        return CpiMsgCd.SUCCESS.getMsgCd();
    }

    /**
     * 充值订单撤单
     * @param checkDTO
     * @param corpBusTyp
     * @return
     * @throws Exception
     */
    private String fundCancelOrder(CheckDTO checkDTO,CorpBusTyp corpBusTyp) throws Exception{
        //根据对账原订单流水查询订单
        ShortcutOrderDO shortcutOrderDO = null;
        EbankOrderDO ebankOrderDO = null;
        String fndOrdNo = null;
        String subOrdNo = null;
        String userId = null;
        if(CorpBusTyp.FASTPAY.equals(corpBusTyp)){
            shortcutOrderDO = shortcutOrderDao.selectShortcutOrderByChkKey(checkDTO.getChkKey());
            if (JudgeUtils.isNull(shortcutOrderDO)) {
                return CpiMsgCd.ORIGINAL_SHORTCUT_ORDER_IS_NOT_FUND.getMsgCd();
            }
            fndOrdNo = shortcutOrderDO.getFndOrdNo();
            subOrdNo = shortcutOrderDO.getSubOrdNo();
        }else if(CorpBusTyp.EBANKPAY.equals(corpBusTyp)){
            ebankOrderDO = ebankOrderDao.selectEbankpayOrderByChkKey(checkDTO.getChkKey());
            if (JudgeUtils.isNull(ebankOrderDO)) {
                return CpiMsgCd.ORIGINAL_EBANK_ORDER_IS_NOT_FUND.getMsgCd();
            }
            fndOrdNo = ebankOrderDO.getFndOrdNo();
            subOrdNo = ebankOrderDO.getSubOrdNo();
        }else {
            return CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
        }

        FundOrderDO fundOrderDO = fundOrderDao.get(fndOrdNo);
        if (JudgeUtils.isNull(fundOrderDO)) {
            return CpiMsgCd.ORIGINAL_ORDER_IS_NOT_FUND.getMsgCd();
        }
        userId = fundOrderDO.getUserId();

        //查询账户余额是否可以撤单
        UserAccountDTO userAccountDTO = new UserAccountDTO();
        userAccountDTO.setUserId(userId);
        userAccountDTO.setCapTyp("1");
        GenericRspDTO<List<QueryAcBalRspDTO>> queryGenericDTO = accManageClint.queryAcBal(userAccountDTO);
        if (!ACMMessageCode.ACM_SUCC.equals(queryGenericDTO.getMsgCd())) {
            return queryGenericDTO.getMsgCd();
        }
        //获取用户余额
        BigDecimal acCurBal = queryGenericDTO.getBody().get(0).getAcCurBal();
        //可用余额小于订单金额
        if (acCurBal.compareTo(fundOrderDO.getOrdAmt()) < 0) {
            return CpiMsgCd.ACCOUT_BALANCE_IS_NOT_ENOUGH.getMsgCd();
        }

        //调用账务处理
        GenericDTO<List<AccountingReqDTO>> genericDTO = new GenericDTO<>();
        List<AccountingReqDTO> list = new ArrayList<>();
        String rutCorpOrg = fundOrderDO.getRutCorpOrg();
        String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPI", 6);

        //借：其他应付款-支付账户-现金账户
        AccountingReqDTO accReqInf1 = AccountUtils.getAccountingReqDTO("N","I","","1","**********",CpiConstants.TX_TYP_RECHARGE,
                fundOrderDO.getOrdAmt(),"D",txJrnNo, subOrdNo,"","","","","");

        //贷：应收账款-渠道充值-XX银行
        AccountingReqDTO accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1",AccountUtils.getReceiveItemNo2(rutCorpOrg),CpiConstants.TX_TYP_RECHARGE,
                fundOrderDO.getOrdAmt(),"C",txJrnNo, subOrdNo,"","","","","");

        list.add(accReqInf1);
        list.add(accReqInf2);
        genericDTO.setBody(list);
        GenericRspDTO<NoBody> rspDTO = accTreatClient.accountingTreatment(genericDTO);
        //判断账务处理是否成功，不成功退出
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            throw new LemonException(rspDTO.getMsgCd());
        }

        //更新订单状态
        FundOrderDO updateFundOrder = new FundOrderDO();
        updateFundOrder.setFudOrdNo(fundOrderDO.getFudOrdNo());
        updateFundOrder.setOrdSts(CpiConstants.ORD_FAIL);
        if(CorpBusTyp.FASTPAY.equals(corpBusTyp)) {
            ShortcutOrderDO updateShortcutOrder = new ShortcutOrderDO();
            updateShortcutOrder.setSubOrdNo(subOrdNo);
            updateShortcutOrder.setOrdSts(CpiConstants.ORD_FAIL);
            shortcutOrderDao.update(updateShortcutOrder);
        }else if(CorpBusTyp.EBANKPAY.equals(corpBusTyp)){
            EbankOrderDO updateEbankOrder = new EbankOrderDO();
            updateEbankOrder.setSubOrdNo(subOrdNo);
            updateEbankOrder.setOrdSts(CpiConstants.ORD_FAIL);
            ebankOrderDao.update(updateEbankOrder);
        }else {
            return CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
        }
        fundOrderDao.update(updateFundOrder);

        return CpiMsgCd.SUCCESS.getMsgCd();
    }

    /**
     * 退款撤单
     * @param checkDTO
     * @return
     * @throws Exception
     */
    private String refundCancelOrder(CheckDTO checkDTO) throws Exception{
        //根据对账原订单流水查询订单
        RefundSuborderDO refundSuborderDO = refundSuborderDao.selectRefundSuborderByChkKey(checkDTO.getChkKey());
        if (JudgeUtils.isNull(refundSuborderDO)) {
            return CpiMsgCd.ORIGINAL_SUB_ORDER_IS_NOT_FUND.getMsgCd();
        }

        RefundOrderDO refundOrderDO = refundOrderDao.get(refundSuborderDO.getRfdOrdNo());
        if (JudgeUtils.isNull(refundOrderDO)) {
            return CpiMsgCd.ORIGINAL_ORDER_IS_NOT_FUND.getMsgCd();
        }

        //调用账务处理
        GenericDTO<List<AccountingReqDTO>> genericDTO = new GenericDTO<>();
        List<AccountingReqDTO> list = new ArrayList<>();
        String rutCorpOrg = refundOrderDO.getRutCorpOrg();
        String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPI", 6);

        //借：应付账款-渠道退款-XX银行
        AccountingReqDTO accReqInf1 = AccountUtils.getAccountingReqDTO("N","I","","1",AccountUtils.getPayItemNo1(rutCorpOrg),CpiConstants.TX_TYP_REFUNDS
                ,refundOrderDO.getOrdAmt(),"D",txJrnNo, refundSuborderDO.getSubOrdNo(),"","","","","");

        //贷：其他应付款-支付账户-用户销户
        AccountingReqDTO accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1","**********",CpiConstants.TX_TYP_REFUNDS,
                refundOrderDO.getOrdAmt(),"C",txJrnNo ,refundSuborderDO.getSubOrdNo(),"","","","","");

        list.add(accReqInf1);
        list.add(accReqInf2);
        genericDTO.setBody(list);
        GenericRspDTO<NoBody> rspDTO = accTreatClient.accountingTreatment(genericDTO);
        //判断账务处理是否成功，不成功退出
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            throw new LemonException(rspDTO.getMsgCd());
        }

        //更新订单状态
        RefundSuborderDO updateSubOrder = new RefundSuborderDO();
        updateSubOrder.setSubOrdNo(refundSuborderDO.getSubOrdNo());
        updateSubOrder.setOrdSts(CpiConstants.ORD_FAIL);
        refundSuborderDao.update(updateSubOrder);

        RefundOrderDO updateRefundOrder = new RefundOrderDO();
        updateRefundOrder.setRfdOrdNo(refundSuborderDO.getRfdOrdNo());
        updateRefundOrder.setOrdSts(CpiConstants.ORD_FAIL);
        refundOrderDao.update(updateRefundOrder);

        return CpiMsgCd.SUCCESS.getMsgCd();
    }

    /**
     * 充值订单差错取消
     * @param checkDTO
     * @return
     * @throws Exception
     */
    private String fundCancelError(CheckDTO checkDTO,CorpBusTyp corpBusTyp) throws Exception{
        //根据对账原订单流水查询订单
        ShortcutOrderDO shortcutOrderDO = null;
        EbankOrderDO ebankOrderDO = null;
        String fndOrdNo = null;
        if(CorpBusTyp.FASTPAY.equals(corpBusTyp)){
            shortcutOrderDO = shortcutOrderDao.selectShortcutOrderByChkKey(checkDTO.getChkKey());
            if (JudgeUtils.isNull(shortcutOrderDO)) {
                return CpiMsgCd.ORIGINAL_SHORTCUT_ORDER_IS_NOT_FUND.getMsgCd();
            }
            fndOrdNo = shortcutOrderDO.getFndOrdNo();
        }else if(CorpBusTyp.EBANKPAY.equals(corpBusTyp)){
            ebankOrderDO = ebankOrderDao.selectEbankpayOrderByChkKey(checkDTO.getChkKey());
            if (JudgeUtils.isNull(ebankOrderDO)) {
                return CpiMsgCd.ORIGINAL_EBANK_ORDER_IS_NOT_FUND.getMsgCd();
            }
            fndOrdNo = ebankOrderDO.getFndOrdNo();
        }else {
            return CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
        }

        FundOrderDO fundOrderDO = fundOrderDao.get(fndOrdNo);
        if (JudgeUtils.isNull(fundOrderDO)) {
            return CpiMsgCd.ORIGINAL_ORDER_IS_NOT_FUND.getMsgCd();
        }

        //更新订单状态
        FundOrderDO updateFundOrder = new FundOrderDO();
        updateFundOrder.setFudOrdNo(fundOrderDO.getFudOrdNo());
        updateFundOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
        if(CorpBusTyp.FASTPAY.equals(corpBusTyp)) {
            ShortcutOrderDO updateShortcutOrder = new ShortcutOrderDO();
            updateShortcutOrder.setSubOrdNo(shortcutOrderDO.getSubOrdNo());
            updateShortcutOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
            shortcutOrderDao.update(updateShortcutOrder);
        }else if(CorpBusTyp.EBANKPAY.equals(corpBusTyp)){
            EbankOrderDO updateEbankOrder = new EbankOrderDO();
            updateEbankOrder.setSubOrdNo(ebankOrderDO.getSubOrdNo());
            updateEbankOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
            ebankOrderDao.update(updateEbankOrder);
        }else {
            return CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd();
        }
        fundOrderDao.update(updateFundOrder);

        return CpiMsgCd.SUCCESS.getMsgCd();
    }

    /**
     * 退款差错取消
     * @param checkDTO
     * @return
     * @throws Exception
     */
    private String refundCancelError(CheckDTO checkDTO) throws Exception{
        //根据对账原订单流水查询订单
        RefundSuborderDO refundSuborderDO = refundSuborderDao.selectRefundSuborderByChkKey(checkDTO.getChkKey());
        if (JudgeUtils.isNull(refundSuborderDO)) {
            return CpiMsgCd.ORIGINAL_SUB_ORDER_IS_NOT_FUND.getMsgCd();
        }

        RefundOrderDO refundOrderDO = refundOrderDao.get(refundSuborderDO.getRfdOrdNo());
        if (JudgeUtils.isNull(refundOrderDO)) {
            return CpiMsgCd.ORIGINAL_ORDER_IS_NOT_FUND.getMsgCd();
        }

        //调用账务处理
        GenericDTO<List<AccountingReqDTO>> genericDTO = new GenericDTO<>();
        List<AccountingReqDTO> list = new ArrayList<>();
        String rutCorpOrg = refundOrderDO.getRutCorpOrg();
        String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPI", 6);

        //借： 应收账款-渠道充值-XX银行
        AccountingReqDTO accReqInf1 = AccountUtils.getAccountingReqDTO("N","I","","1",AccountUtils.getReceiveItemNo2(rutCorpOrg),CpiConstants.TX_TYP_REFUNDS,
                refundOrderDO.getOrdAmt(),"D",txJrnNo, refundSuborderDO.getSubOrdNo(),"","","","","");

        //贷：其他应付款-差异账-现金账户
        AccountingReqDTO accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1","**********",CpiConstants.TX_TYP_REFUNDS,
                refundOrderDO.getOrdAmt(),"C",txJrnNo, refundSuborderDO.getSubOrdNo(),"","","","","");

        list.add(accReqInf1);
        list.add(accReqInf2);
        genericDTO.setBody(list);
        GenericRspDTO<NoBody> rspDTO = accTreatClient.accountingTreatment(genericDTO);
        //判断账务处理是否成功，不成功退出
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            throw new LemonException(rspDTO.getMsgCd());
        }

        //更新订单状态
        RefundSuborderDO updateSubOrder = new RefundSuborderDO();
        updateSubOrder.setSubOrdNo(refundSuborderDO.getSubOrdNo());
        updateSubOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
        refundSuborderDao.update(updateSubOrder);

        RefundOrderDO updateRefundOrder = new RefundOrderDO();
        updateRefundOrder.setRfdOrdNo(refundSuborderDO.getRfdOrdNo());
        updateRefundOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
        refundOrderDao.update(updateRefundOrder);

        return CpiMsgCd.SUCCESS.getMsgCd();
    }

    /**
     *
     */
    private String fundMatchHandle(CheckDTO checkDTO) throws Exception{

        //调用账务处理
        GenericDTO<List<AccountingReqDTO>> genericDTO = new GenericDTO<>();
        List<AccountingReqDTO> list = new ArrayList<>();
        String mainNo = checkDTO.getMainNo();
        String txJrnNo = IdGenUtils.generateIdWithDateTime("ACC_JRN_NO", "CPI", 6);

        //借：应收账款-待结算款-XX银行
        AccountingReqDTO accReqInf1 = AccountUtils.getAccountingReqDTO("N","I","","1", AccountUtils.getReceiveItemNo1(mainNo),CpiConstants.TX_TYP_RECHARGE,
                checkDTO.getChkAmt(),"D",txJrnNo, LemonUtils.getRequestId(),"","","","","");

        //贷：应收账款-渠道充值-XX银行
        AccountingReqDTO accReqInf2 = AccountUtils.getAccountingReqDTO("N","I","","1", AccountUtils.getReceiveItemNo2(mainNo),CpiConstants.TX_TYP_RECHARGE,
                checkDTO.getChkAmt(),"C",txJrnNo, LemonUtils.getRequestId(),"","","","","");

        list.add(accReqInf1);
        list.add(accReqInf2);
        genericDTO.setBody(list);
        GenericRspDTO<NoBody> rspDTO = accTreatClient.accountingTreatment(genericDTO);
        //判断账务处理是否成功，不成功退出
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            throw new LemonException(rspDTO.getMsgCd());
        }

        return CpiMsgCd.SUCCESS.getMsgCd();
    }
}

