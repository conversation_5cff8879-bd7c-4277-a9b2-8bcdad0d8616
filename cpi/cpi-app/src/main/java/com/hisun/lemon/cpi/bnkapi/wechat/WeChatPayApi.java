//package com.hisun.lemon.cpi.bnkapi.wechat;
//
//import com.hisun.lemon.common.exception.LemonException;
//import com.hisun.lemon.common.utils.DateTimeUtils;
//import com.hisun.lemon.common.utils.JudgeUtils;
//import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
//import com.hisun.lemon.cpi.common.CpiConstants;
//import com.hisun.lemon.cpi.common.CpiMsgCd;
//import com.hisun.lemon.cpi.entity.EbankOrderDO;
//import com.hisun.lemon.cpi.entity.RefundOrderDO;
//import com.hisun.lemon.cpi.entity.RefundSuborderDO;
//import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
//import com.hisun.lemon.cpi.wechat.ebankpay.*;
//import com.hisun.lemon.cpi.wechat.ebankpay.req.OfflinePaymentReq;
//import com.hisun.lemon.cpi.wechat.ebankpay.req.RefundReq;
//import com.hisun.lemon.cpi.wechat.ebankpay.req.RevokeReq;
//import com.hisun.lemon.cpi.wechat.ebankpay.req.TradeStatusQueryReq;
//import com.hisun.lemon.cpi.wechat.ebankpay.rsp.OfflinePaymentRsp;
//import com.hisun.lemon.cpi.wechat.ebankpay.rsp.RefundRsp;
//import com.hisun.lemon.cpi.wechat.ebankpay.rsp.RevokeRsp;
//import com.hisun.lemon.cpi.wechat.ebankpay.rsp.TradeStatusQueryRsp;
//import com.hisun.lemon.framework.utils.IdGenUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.time.LocalTime;
//import java.util.Date;
//
///**
// * User : Rui
// * Date : 2017/8/30
// * Time : 19:21
// **/
//@Component
//public class WeChatPayApi {
//
//    @Resource
//    WeChatApi weChatApi;
//
//    @Resource
//    WeChatProperties weChatProperties;
//
//    @Resource
//    WeChatRetCdConvert weChatRetCdConvert;
//
//    private static final Logger logger = LoggerFactory.getLogger(WeChatPayApi.class);
//
//    /**
//     * 商户扫码收款
//     *
//     * @param ebankOrderDO
//     * @return
//     */
//    public BaseBnkRspBO mercCreateBankOrder(EbankOrderDO ebankOrderDO, String authCode, String barCode) {
//
//        String outOrdNo = ebankOrderDO.getChkKey();
//
//        HeadReq headReq = new HeadReq();
//        headReq.setAesappId(weChatProperties.getAesappId());
//        headReq.setAescusId(weChatProperties.getAescusId());
//        headReq.setInfCode(WEIXINEnumCommon.EnumInfCode.OFFLINEPAYMENT);
//
//        OfflinePaymentReq.OfflinePaymentTranReq offlinePaymentTranReq = new OfflinePaymentReq.OfflinePaymentTranReq();
//        //扫描出来的用户微信二维码
//        offlinePaymentTranReq.setAuthCode(authCode);
//        offlinePaymentTranReq.setBody("商品");
//        offlinePaymentTranReq.setCurrency(WEIXINEnumCommon.EnumCurrency.USD);
//        offlinePaymentTranReq.setCusId(weChatProperties.getAescusId());
//        offlinePaymentTranReq.setReqsn(outOrdNo);
//        offlinePaymentTranReq.setTrxamt(ebankOrderDO.getOrdAmt().multiply(BigDecimal.valueOf(100)).intValue());
//
//        OfflinePaymentReq req = new OfflinePaymentReq();
//        req.setHeadReq(headReq);
//        req.setOfflinePaymentTranReq(offlinePaymentTranReq);
//
//        BaseBnkRspBO baseBnkRspBO = null;
//        try {
//            //调用银行接口
//            OfflinePaymentRsp offlinePaymentRsp = weChatApi.doSend(req, WEIXINEnumCommon.EnumSource.payment);
//            if (JudgeUtils.isNotNull(offlinePaymentRsp)) {
//                //获取返回值和返回头
//                HeadRsp headRsp = offlinePaymentRsp.getHeadRsp();
//                baseBnkRspBO = new BaseBnkRspBO();
//                baseBnkRspBO.setOutOrdNo(outOrdNo);
//                baseBnkRspBO.setChkKey(outOrdNo);
//
//                //判断报文头
//                if(CpiConstants.WECHAT_HEAD_SUCCESS.equals(headRsp.getRetCode())){
//                    OfflinePaymentRsp.OfflinePaymentTranRsp bnkRsp = offlinePaymentRsp.getOfflinePaymentTranRsp();
//                    baseBnkRspBO.setOrgJrnNo(bnkRsp.getTrxId());
//                    baseBnkRspBO.setOrgRspCd(bnkRsp.getTrxStatus());
//                    baseBnkRspBO.setOrgRspMsg(bnkRsp.getErrMsg());
//                } else {
//                    baseBnkRspBO.setOrgRspCd(headRsp.getRetCode());
//                    baseBnkRspBO.setOrgRspMsg(headRsp.getRetMsg());
//                }
//                baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//                baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//            }
//        } catch (Exception e) {
//            //异常处理
//            logger.error("WeChatPayApi.mercCreateBankOrder Exception : ", e);
//            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
//        }
//        return baseBnkRspBO;
//    }
//
//    /**
//     * 商户后台退款
//     */
//    public BaseBnkRspBO mercRefundBankOrder(RefundOrderDO refundOrderDO, EbankOrderDO ebankOrderDO){
//        String outOrdNo = ebankOrderDO.getChkKey();
//
//        LocalDate today = DateTimeUtils.getCurrentLocalDate();
//        HeadReq headReq = new HeadReq();
//        headReq.setAesappId(weChatProperties.getAesappId());
//        headReq.setAescusId(weChatProperties.getAescusId());
//        BaseBnkRspBO baseBnkRspBO = null;
//        //当日撤销，次日退款
//        try {
//            if (today.compareTo(ebankOrderDO.getOrdDt()) > 0) {
//                //退款
//                headReq.setInfCode(WEIXINEnumCommon.EnumInfCode.REFUND);
//                RefundReq.RefundTranReq refundTranReq = new RefundReq.RefundTranReq();
//                refundTranReq.setCusId(weChatProperties.getAescusId());
//                refundTranReq.setOldtrxId(ebankOrderDO.getOrgJrnNo());
//
//                RefundReq req = new RefundReq();
//                req.setHeadReq(headReq);
//                req.setRefundTranReq(refundTranReq);
//                RefundRsp rsp = weChatApi.doSend(req, WEIXINEnumCommon.EnumSource.refund);
//                if (JudgeUtils.isNotNull(rsp)) {
//                    //调用银行接口
//
//                    HeadRsp headRsp = rsp.getHeadRsp();
//                    baseBnkRspBO = new BaseBnkRspBO();
//                    baseBnkRspBO.setOutOrdNo(outOrdNo);
//                    baseBnkRspBO.setChkKey(outOrdNo);
//                    if(CpiConstants.WECHAT_HEAD_SUCCESS.equals(headRsp.getRetCode())){
//                        RefundRsp.RefundTranRsp bnkRsp = rsp.getRefundTranRsp();
//                        baseBnkRspBO.setOrgJrnNo(bnkRsp.getTrxId());
//                        baseBnkRspBO.setOrgRspCd(bnkRsp.getTrxStatus());
//                        baseBnkRspBO.setOrgRspMsg(headRsp.getRetMsg());
//                    } else {
//                        baseBnkRspBO.setOrgRspCd(headRsp.getRetCode());
//                        baseBnkRspBO.setOrgRspMsg(headRsp.getRetMsg());
//                    }
//                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//                    baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//                } else {
//                    return null;
//                }
//            } else {
//                //撤销
//                headReq.setInfCode(WEIXINEnumCommon.EnumInfCode.REVOKE);
//                RevokeReq.RevokeTranReq revokeTranReq = new RevokeReq.RevokeTranReq();
//                revokeTranReq.setCusId(weChatProperties.getAescusId());
//                revokeTranReq.setOldtrxId(ebankOrderDO.getOrgJrnNo());
//                RevokeReq req = new RevokeReq();
//                req.setHeadReq(headReq);
//                req.setRevokeTranReq(revokeTranReq);
//                RevokeRsp rsp = weChatApi.doSend(req, WEIXINEnumCommon.EnumSource.revoke);
//                if (JudgeUtils.isNotNull(rsp)) {
//                    //调用银行接口
//                    HeadRsp headRsp = rsp.getHeadRsp();
//                    baseBnkRspBO = new BaseBnkRspBO();
//                    baseBnkRspBO.setOutOrdNo(outOrdNo);
//                    baseBnkRspBO.setChkKey(outOrdNo);
//                    if(CpiConstants.WECHAT_HEAD_SUCCESS.equals(headRsp.getRetCode())){
//                        RevokeRsp.RevokeTranRsp bnkRsp = rsp.getRevokeTranRsp();
//                        baseBnkRspBO.setOrgJrnNo(bnkRsp.getTrxId());
//                        baseBnkRspBO.setOrgRspCd(bnkRsp.getTrxStatus());
//                        baseBnkRspBO.setOrgRspMsg(headRsp.getRetMsg());
//                    } else {
//                        baseBnkRspBO.setOrgRspCd(headRsp.getRetCode());
//                        baseBnkRspBO.setOrgRspMsg(headRsp.getRetMsg());
//                    }
//                    baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//                    baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//                } else {
//                    return null;
//                }
//            }
//            CpiMsgCd cpiMsgCd = weChatRetCdConvert.Convert(baseBnkRspBO.getOrgRspCd());
//            if(JudgeUtils.isSuccess(cpiMsgCd.getMsgCd())){
//                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_SUC);
//            } else {
//                baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_FAIL);
//            }
//        } catch (Exception e) {
//            //异常处理
//            logger.error("WeChatPayApi.mercRefundBankOrderd Exception : ", e);
//            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
//        }
//        return baseBnkRspBO;
//    }
//
//    /**
//     * 查询微信离线支付订单交易状态
//     */
//    public BaseBnkRspBO queryPayOrder(EbankOrderDO ebankOrderDO) {
//        String outOrdNo = ebankOrderDO.getChkKey();//商户外发请求订单号
//        String orgJrnNo = ebankOrderDO.getOrgJrnNo();//银行交易流水号
//
//        //报文头
//        HeadReq headReq = new HeadReq();
//        headReq.setAesappId(weChatProperties.getAesappId());
//        headReq.setAescusId(weChatProperties.getAescusId());
//        headReq.setInfCode(WEIXINEnumCommon.EnumInfCode.STATUSQUERY);
//
//        //报文体
//        TradeStatusQueryReq.TradeStatusQueryTranReq tradeStatusQueryTranReq=new TradeStatusQueryReq.TradeStatusQueryTranReq();
//        tradeStatusQueryTranReq.setCusId(weChatProperties.getAescusId());
//        tradeStatusQueryTranReq.setTrxCode(WEIXINEnumCommon.EnumTradeType.VSP503);
//        if(JudgeUtils.isNotBlank(outOrdNo)) {
//            tradeStatusQueryTranReq.setPayReqSn(outOrdNo);
//        }
//        if(JudgeUtils.isNotBlank(orgJrnNo)) {
//            tradeStatusQueryTranReq.setPayTrxId(orgJrnNo);
//        }
//
//        //查询请求报文
//        TradeStatusQueryReq req=new TradeStatusQueryReq();
//        req.setHeadReq(headReq);
//        req.setTradeStatusQueryTranReq(tradeStatusQueryTranReq);
//        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
//        try {
//            //调用银行查询接口
//            TradeStatusQueryRsp tradeStatusQueryRsp = weChatApi.doSend(req, WEIXINEnumCommon.EnumSource.tradestatusquery);
//            if (JudgeUtils.isNotNull(tradeStatusQueryRsp)) {
//                //获取返回报文头
//                HeadRsp headRsp = tradeStatusQueryRsp.getHeadRsp();
//                String retCode = headRsp.getRetCode();
//                String retMsg = headRsp.getRetMsg();
//                if(CpiConstants.WECHAT_HEAD_SUCCESS.equals(retCode)) {
//                    //获取返回值和返回头
//                    TradeStatusQueryRsp.TradeStatusQueryTranRsp tradeStatusQueryTranRsp = tradeStatusQueryRsp.getTradeStatusQueryTranRsp();
//                    baseBnkRspBO.setOutOrdNo(outOrdNo);
//                    baseBnkRspBO.setChkKey(outOrdNo);
//
//                    //返回订单状态
//                    String trxStatus = tradeStatusQueryTranRsp.getTrxStatus();
//                    String trsStatusName = tradeStatusQueryTranRsp.getTrxStatusName();
//                    String retPayTrxId = tradeStatusQueryTranRsp.getPayTrxId();
//
//                    //判断银行处理状态
//                    if(WEIXINEnumCommon.EnumTrxStatus.SUCCESS.getCode().equals(trxStatus)) {
//                        //银行处理成功
//                        baseBnkRspBO.setOrgJrnNo(retPayTrxId);
//                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
//
//                    } else if(WEIXINEnumCommon.EnumTrxStatus.FAIL.getCode().equals(trxStatus)) {
//                        //银行处理失败
//                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
//
//                    } else {
//                        //银行处理中
//                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_WAIT);
//                    }
//                    baseBnkRspBO.setOrgRspCd(trxStatus);
//                    baseBnkRspBO.setOrgRspMsg(trsStatusName);
//                } else {
//                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
//                    baseBnkRspBO.setOrgRspCd(retCode);
//                    baseBnkRspBO.setOrgRspMsg(retMsg);
//                }
//                baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//                baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//            }
//        } catch (Exception e) {
//            //异常处理
//            logger.error("WeChatPayApi.queryPayOrder Exception : ", e);
//            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
//        }
//        return baseBnkRspBO;
//    }
//
//    /**
//     * 查询微信离线支付、微信支付退款等交易状态
//     */
//    public BaseBnkRspBO queryRefundOrder(RefundSuborderDO refundSuborderDO, EbankOrderDO ebankOrderDO) {
//        String outOrdNo = refundSuborderDO.getChkKey();//商户外发请求订单号
//        String orgJrnNo = refundSuborderDO.getOrgJrnNo();//银行交易流水号
//        LocalDate today = DateTimeUtils.getCurrentLocalDate();
//        LocalDate refundOrdDt = refundSuborderDO.getOrdDt();
//
//        //请求报文头
//        HeadReq headReq = new HeadReq();
//        headReq.setAesappId(weChatProperties.getAesappId());
//        headReq.setAescusId(weChatProperties.getAescusId());
//        headReq.setInfCode(WEIXINEnumCommon.EnumInfCode.STATUSQUERY);
//
//        //请求报文体
//        TradeStatusQueryReq.TradeStatusQueryTranReq tradeStatusQueryTranReq=new TradeStatusQueryReq.TradeStatusQueryTranReq();
//        tradeStatusQueryTranReq.setCusId(weChatProperties.getAescusId());
//        if(JudgeUtils.isNotBlank(outOrdNo)) {
//            tradeStatusQueryTranReq.setPayReqSn(outOrdNo);
//        }
//        if(JudgeUtils.isNotBlank(orgJrnNo)) {
//            tradeStatusQueryTranReq.setPayTrxId(orgJrnNo);
//        }
//
//        //判断交易类型
//        WEIXINEnumCommon.EnumTradeType trxCode = null;
//        if (today.compareTo(refundOrdDt) == 0) {
//            //当日撤销订单查询
//            trxCode = WEIXINEnumCommon.EnumTradeType.VSP504;
//        } else {
//            //次日退款订单查询
//            trxCode = WEIXINEnumCommon.EnumTradeType.VSP505;
//        }
//        tradeStatusQueryTranReq.setTrxCode(trxCode);
//
//        //请求报文
//        TradeStatusQueryReq req = new TradeStatusQueryReq();
//        req.setHeadReq(headReq);
//        req.setTradeStatusQueryTranReq(tradeStatusQueryTranReq);
//        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
//        try {
//            //调用银行查询接口
//            TradeStatusQueryRsp tradeStatusQueryRsp = weChatApi.doSend(req, WEIXINEnumCommon.EnumSource.tradestatusquery);
//            if (JudgeUtils.isNotNull(tradeStatusQueryRsp)) {
//                //获取返回报文头
//                HeadRsp headRsp = tradeStatusQueryRsp.getHeadRsp();
//                String retCode = headRsp.getRetCode();
//                String retMsg = headRsp.getRetMsg();
//                if(CpiConstants.WECHAT_HEAD_SUCCESS.equals(retCode)) {
//                    //获取返回值和返回头
//                    TradeStatusQueryRsp.TradeStatusQueryTranRsp tradeStatusQueryTranRsp = tradeStatusQueryRsp.getTradeStatusQueryTranRsp();
//                    baseBnkRspBO.setOutOrdNo(outOrdNo);
//                    baseBnkRspBO.setChkKey(outOrdNo);
//
//                    //返回订单状态
//                    String trxStatus = tradeStatusQueryTranRsp.getTrxStatus();
//                    String trsStatusName = tradeStatusQueryTranRsp.getTrxStatusName();
//                    String retPayTrxId = tradeStatusQueryTranRsp.getPayTrxId();
//                    String retRefundTrxId = tradeStatusQueryTranRsp.getRefundTrxId();
//
//                    //判断银行处理状态
//                    if(WEIXINEnumCommon.EnumTrxStatus.SUCCESS.getCode().equals(trxStatus)) {
//                        //银行处理成功
//                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_SUCCESS);
//
//                    } else if(WEIXINEnumCommon.EnumTrxStatus.FAIL.getCode().equals(trxStatus)) {
//                        //银行处理失败
//                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
//                    } else {
//                        //银行处理中
//                        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_WAIT);
//                    }
//                    baseBnkRspBO.setOrgRspCd(trxStatus);
//                    baseBnkRspBO.setOrgRspMsg(trsStatusName);
//                    baseBnkRspBO.setOrgJrnNo(retRefundTrxId);
//                } else {
//                    baseBnkRspBO.setTxFlg(CpiConstants.BANK_RESPONSE_FAIL);
//                    baseBnkRspBO.setOrgRspCd(retCode);
//                    baseBnkRspBO.setOrgRspMsg(retMsg);
//                }
//                baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
//                baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
//            }
//        } catch (Exception e) {
//            //异常处理
//            logger.error("WeChatPayApi.queryPayOrder Exception : ", e);
//            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
//        }
//        return baseBnkRspBO;
//    }
//}
