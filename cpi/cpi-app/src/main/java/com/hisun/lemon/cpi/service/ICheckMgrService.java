package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.entity.AccControlDO;
import com.hisun.lemon.cpi.entity.AccParamDO;

import java.time.LocalDate;
import java.util.List;

/**
 * 对账参数和对账批次主控管理 service
 */
public interface ICheckMgrService {

    /**
     * 增加对账参数
     */
    boolean addAccParam(AccParamDO accParamDO);

    /**
     * 查询生效的对账参数
     */
    List<AccParamDO> queryEffAccParamList();

    /**
     * 查询对账批次是否已存在
     */
    AccControlDO queryUnfinishedAccControl(AccParamDO accParamDO);

    /**
     * 根据生效的对账参数，生成对账批次
     */
    void registerChkBatNo(LocalDate checkDt);

    /**
     * 开始执行对账服务
     */
    void beginCheckAcc(AccControlDO accControlDO);

    /**
     * 写入对账服务
     */
    void writeCheckFile(LocalDate chkFilDt, String chkFilNm,String rutCorg, String fileType);
}
