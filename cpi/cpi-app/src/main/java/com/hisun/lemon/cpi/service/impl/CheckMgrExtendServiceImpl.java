package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.chk.client.ChkClient;
import com.hisun.lemon.chk.common.ChkConstants;
import com.hisun.lemon.chk.dto.ControlDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.extension.ExtensionLoader;
import com.hisun.lemon.common.utils.*;
import com.hisun.lemon.cpi.bo.BaseAccChkFilBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.IAccControlDao;
import com.hisun.lemon.cpi.dao.IEbankOrderDao;
import com.hisun.lemon.cpi.dao.IShortcutOrderDao;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.ICheckAccRefundService;
import com.hisun.lemon.cpi.service.ICheckMgrExtendService;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.lemon.jcommon.file.FileUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 资金流入模块，对账主控服务扩展 service
 */
@Service
public class CheckMgrExtendServiceImpl extends BaseService implements ICheckMgrExtendService {
    private static final Logger logger = LoggerFactory.getLogger(CheckMgrExtendServiceImpl.class);

    @Resource
    private IAccControlDao accControlDao;

    @Resource
    private ICheckAccRefundService checkAccRefundService;

    @Resource
    private IShortcutOrderDao shortcutOrderDao;

    @Resource
    private IEbankOrderDao ebankOrderDao;

    @Resource
    private ChkClient chkClient;

    /**
     * 对账第一步:获取银行机构对账文件
     */
    @Override
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getRefundChkFile(AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO) {
        logger.debug("==================获取银行机构对账文件==================");
        String chkFilSts = accControlDO.getChkFilSts();
        try {
            //根据配置的反射类进行反射
            Class chkClazz = Class.forName(accRefundCfgDO.getChkClazz());
            Method method = chkClazz.getMethod(accRefundCfgDO.getGetFileMethod(), String.class,LocalDate.class);

            //将首字母转成小写
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.append(Character.toLowerCase(chkClazz.getSimpleName().charAt(0)));//首字母转为小写
            stringBuilder.append(chkClazz.getSimpleName().substring(1));//从第一位开始截取字符串
            String beanName = stringBuilder.toString();//所需bean的名称

            //获取到Spring容器中的对象
            Object instance = ExtensionLoader.getSpringBean(beanName, chkClazz);

            //调用getFile方法
            Object rspObject = method.invoke(instance, accRefundCfgDO.getChkFilePath(), accControlDO.getChkFilDt());
            if (JudgeUtils.isNotNull(rspObject)) {
                chkFilSts = CpiConstants.CHK_FIL_DOWNLOAD;
                accControlDO.setChkFilSts(chkFilSts);
                accControlDO.setChkFilNm((String)rspObject);
                AccControlDO updateAccControlDO = new AccControlDO();
                updateAccControlDO.setChkBatNo(accControlDO.getChkBatNo());
                updateAccControlDO.setFileRcvDt(DateTimeUtils.getCurrentLocalDate());
                updateAccControlDO.setChkFilSts(chkFilSts);
                updateAccControlDO.setChkFilNm((String)rspObject);
                accControlDao.update(updateAccControlDO);

                //通知对账模块同步状态,暂时不进行错误处理，防止对账之后出现问题，如果有问题先手工处理
                boolean isSuccess = this.notifyChkModule(accControlDO, ChkConstants.CHK_OPPO_FIL_DOWNLOAD);
//                if (isSuccess) {
//                    return CpiConstants.CHK_FIL_DOWNLOAD;
//                } else {
//                    return accControlDO.getChkFilSts();
//                }
                return CpiConstants.CHK_FIL_DOWNLOAD;
            }else {
                return accControlDO.getChkFilSts();
            }
        } catch (Exception e){
            logger.error("CheckMgrExtendServiceImpl.getOrgChkFile()，反射调用getCheckFile方法失败，异常为: ", e);
            return accControlDO.getChkFilSts();
        }
    }

    /**
     * 对账第二步:解析银行对账文件，批量插入对账明细至数据库
     */
    @Override
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String importRefundChkFile(AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO) {
        logger.debug("==================开始解析银行对账文件==================");
        List<Object> checkDOList = null;
        BufferedReader reader = null;
        String lineString = null;
        String[] valueArray = null;
        String chkFilSts = accControlDO.getChkFilSts();
        try {
            //step1: 获取对账文件
            File file = new File(accRefundCfgDO.getChkFilePath() + File.separator + accControlDO.getChkFilNm());

            //step2:若文件存在，则根据对账配置，调用银行API类的文件解析方法
            if(file.exists() && file.length() > 0) {
                checkDOList = new ArrayList<>();
                reader = new BufferedReader(new FileReader(file));//文件流
                String[] filedNames = accRefundCfgDO.getImportFields().split(accRefundCfgDO.getSplitSign());//文件格式字段
                String fileSplitter = accRefundCfgDO.getSplitSign();//文件中每行记录的字段分隔符
                int continueNum = accRefundCfgDO.getContinueNum();//需要跳过的行数

                //循环解析对账文件中的每行记录
                int lingNum = 0;
                while ((lineString = reader.readLine()) != null) {
                    //需要跳过的行数
                    if (lingNum < continueNum) {
                        lingNum++;
                        continue;
                    }

                    //初始化对账明细表对应的DO类
                    Class importClazz = Class.forName(accRefundCfgDO.getImportClass());
                    Object importObject = importClazz.newInstance();

                    //根据分割符，得到每行记录的字段值数组
                    valueArray = lineString.split(fileSplitter);
                    for (int i = 0; i < filedNames.length; i++) {
                        //获取DO类的属性域
                        Field field = importClazz.getDeclaredField(filedNames[i]);

                        //打破封装: 实际上setAccessible是启用和禁用访问安全检查的开关,并不是为true就能访问、为false就不能访问。
                        //由于JDK的安全检查耗时较多.所以通过setAccessible(true)的方式关闭安全检查就可以达到提升反射速度的目的。
                        field.setAccessible(true);

                        //属性赋值
                        ReflectionUtils.setField(field, importObject, valueArray[i]);
                    }

                    //获取DO类所有属性域
                    Field[] declaredFields = importClazz.getDeclaredFields();
                    for(Field field : declaredFields){
                        //关闭JDK的安全检查
                        field.setAccessible(true);
                        switch (field.getName()) {
                            //对账批次号
                            case "chkBatNo":
                                ReflectionUtils.setField(field, importObject, accControlDO.getChkBatNo());
                                break;
                            //对账文件日期
                            case "chkFilDt":
                                ReflectionUtils.setField(field, importObject, DateTimeUtils.formatLocalDate(accControlDO.getChkDt()));
                                break;
                            //对账文件名称
                            case "chkFilNm":
                                ReflectionUtils.setField(field, importObject, accControlDO.getChkFilNm());
                                break;
                            //合作业务类型
                            case "corpBusTyp":
                                ReflectionUtils.setField(field, importObject, accControlDO.getCorpBusTyp());
                                break;
                            //合作业务子类型
                            case "corpBusSubTyp":
                                ReflectionUtils.setField(field, importObject, accControlDO.getCorpBusSubTyp());
                                break;
                            //路径合作机构
                            case "rutCorg":
                                ReflectionUtils.setField(field, importObject, accControlDO.getRutCorg());
                                break;
                            default:
                                break;
                        }
                    }
                    checkDOList.add(importObject);
                }
            } else {
                return CpiConstants.CHK_FIL_IMPORT;
            }

            //反射获取银行API中批量插入对账明细的方法
            Class chkClazz = Class.forName(accRefundCfgDO.getChkClazz());
            Method method = chkClazz.getMethod(accRefundCfgDO.getImportMethod(), List.class, AccControlDO.class);

            //将首字母转成小写
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.append(Character.toLowerCase(chkClazz.getSimpleName().charAt(0)));//首字母转为小写
            stringBuilder.append(chkClazz.getSimpleName().substring(1));//从第一位开始截取字符串
            String beanName = stringBuilder.toString();//所需bean的名称

            //获取到Spring容器中的对象，银行API类的bean
            Object instance = ExtensionLoader.getSpringBean(beanName, chkClazz);

            //调用对账明细批量入库方法
            method.invoke(instance, checkDOList, accControlDO);

            //批量导入成功
            chkFilSts = CpiConstants.CHK_FIL_IMPORT;
            accControlDO.setChkFilSts(chkFilSts);
            AccControlDO updateAccControlDO = new AccControlDO();
            updateAccControlDO.setChkFilSts(chkFilSts);
            updateAccControlDO.setChkBatNo(accControlDO.getChkBatNo());
            updateAccControlDO.setFilTotCnt(accControlDO.getFilTotCnt());
            updateAccControlDO.setFilTotAmt(accControlDO.getFilTotAmt());
            accControlDao.update(updateAccControlDO);

            //通知对账模块同步状态
            boolean isSuccess = this.notifyChkModule(accControlDO,ChkConstants.CHK_OPPO_FIL_IMPORT);
//            if(isSuccess){
//                return CpiConstants.CHK_FIL_IMPORT;
//            }else {
//                return accControlDO.getChkFilSts();
//            }
            return CpiConstants.CHK_FIL_IMPORT;
        } catch (Exception e) {
            logger.error("CheckMgrExtendServiceImpl.importOrgChkFile()，解析银行对账文件失败，异常为: ", e);
            throw new LemonException(CpiMsgCd.IMPORT_CHECK_FILE_FAIL.getMsgCd());
        } finally {
            if (null != reader){
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error("CheckMgrExtendServiceImpl.importOrgChkFile()，关闭文件流异常，异常为: ", e);
                }
            }
        }
    }

    /**
     * 对账第三步:对账业务逻辑，以银行成功的对账明细为准
    */
   @Override
   @SuppressWarnings("unchecked")
   @Transactional(propagation = Propagation.REQUIRES_NEW)
   public String checkRefundFile(AccControlDO accControlDO, AccRefundCfgDO accRefundCfgDO) throws Exception {
       //step1: 对账批次信息
       String chkFilSts = accControlDO.getChkFilSts();
       String chkBatNo = accControlDO.getChkBatNo();
       String rutCorg = accControlDO.getRutCorg();
       String corpBusTyp = accControlDO.getCorpBusTyp();
       String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
       logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo);

       // step2: 检查上一对账日期之前，是否还有未完成的对账批次
       AccControlDO beforeAccControlDO = new AccControlDO();
       beforeAccControlDO.setChkFilDt(accControlDO.getChkFilDt().minusDays(1));
       beforeAccControlDO.setRutCorg(accControlDO.getRutCorg());
       beforeAccControlDO.setCorpBusTyp(accControlDO.getCorpBusTyp());
       beforeAccControlDO.setCorpBusSubTyp(accControlDO.getCorpBusSubTyp());

       AccControlDO accControlDOLast = accControlDao.getAccControlByLastChkDt(beforeAccControlDO);
       if (JudgeUtils.isNotNull(accControlDOLast)) {
           if (!CpiConstants.CHK_FIL_FINISHED.equals(accControlDOLast.getChkFilSts())) {
               logger.error("CheckMgrExtendServiceImpl.checkFile()，机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + " 上日对账仍有未完成的批次");
               return chkFilSts;
           }
       }

       //step3: 根据业务类型，分别调用不同的对账服务
       switch (corpBusTyp) {
           //退款(快捷退款、网银退款、汇款退款)
           case "05":
               chkFilSts = checkAccRefundService.checkAccountRefund(accControlDO, accRefundCfgDO);
               break;
           default:
               logger.error("CheckMgrExtendServiceImpl.checkFile()，机构: " + rutCorg + "; 对账批次号: " + chkBatNo + " 合作业务类型为空");
               throw new LemonException("机构: " + rutCorg + "; 合作业务类型为空");
       }

       //更新对账批次状态 3: 对账成功，待账务处理
       if (StringUtils.equals(chkFilSts, CpiConstants.CHK_FIL_SUCCESS)) {
           AccControlDO updateAcControl = new AccControlDO();
           updateAcControl.setChkFilSts(chkFilSts);
           updateAcControl.setChkBatNo(accControlDO.getChkBatNo());
           updateAcControl.setTotMchAmt(accControlDO.getTotMchAmt());
           updateAcControl.setTotMchCnt(accControlDO.getTotMchCnt());
           updateAcControl.setErrTotAmt(accControlDO.getErrTotAmt());
           updateAcControl.setErrTotCnt(accControlDO.getErrTotCnt());
           updateAcControl.setLongAmt(accControlDO.getLongAmt());
           updateAcControl.setLongCnt(accControlDO.getLongCnt());
           updateAcControl.setShortAmt(accControlDO.getShortAmt());
           updateAcControl.setShortCnt(accControlDO.getShortCnt());
           updateAcControl.setDoubtAmt(accControlDO.getDoubtAmt());
           updateAcControl.setDoubtCnt(accControlDO.getDoubtCnt());
           updateAcControl.setDbtErrAmt(accControlDO.getDbtErrAmt());
           updateAcControl.setDbtErrCnt(accControlDO.getDbtErrCnt());
           accControlDao.update(updateAcControl);

           //通知对账模块同步状态
           boolean isSuccess = this.notifyChkModule(accControlDO,ChkConstants.CHK_OPPO_FIL_IMPORT);
           if(isSuccess){
               return CpiConstants.CHK_FIL_SUCCESS;
           }else {
               return accControlDO.getChkFilSts();
           }

       }else {
           return accControlDO.getChkFilSts();
       }
   }

    /**
     * 对账第四步:对账成功，等待账务处理，
     * 根据业务类型进行分类，分别调用不同业务的service进行账务处理
     */
    @Override
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String accountTreatment(AccControlDO accControlDO) {
        //step1: 对账批次信息
        String chkFilSts = accControlDO.getChkFilSts();
        String chkBatNo = accControlDO.getChkBatNo();
        String rutCorg = accControlDO.getRutCorg();
        String corpBusTyp = accControlDO.getCorpBusTyp();
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 开始账务处理");

        //step2: 根据业务类型，分别调用不同的service进行账务处理
        Boolean isSuccess = false;
        switch (corpBusTyp) {
            //退款(快捷退款、网银退款、汇款退款)
            case "05":
                isSuccess = checkAccRefundService.accountTreatmentRefund(accControlDO);
                break;
            default:
                logger.error("CheckMgrExtendServiceImpl.checkFile()，机构: " + rutCorg + "; 对账批次号: " + chkBatNo + " 合作业务类型为空");
                throw new LemonException("机构: " + rutCorg + "; 合作业务类型为空");
        }

        //step3: 对账完成，更新对账批次状态为 4-对账结束
        if (isSuccess) {
            chkFilSts = CpiConstants.CHK_FIL_FINISHED;
            AccControlDO updateAcControl = new AccControlDO();
            updateAcControl.setChkFilSts(chkFilSts);
            updateAcControl.setChkBatNo(accControlDO.getChkBatNo());
            updateAcControl.setChkDt(DateTimeUtils.getCurrentLocalDate());
            accControlDao.update(updateAcControl);
            //通知对账模块同步状态
            accControlDO.setChkDt(DateTimeUtils.getCurrentLocalDate());
            boolean isSucc = this.notifyChkModule(accControlDO, ChkConstants.CHK_FIL_SUCCESS);
            if(isSucc){
                logger.debug("CheckMgrExtendServiceImpl.accountTreatment()，机构: " + rutCorg + "; 对账批次号: " + chkBatNo + " 通知对账模块同步状态成功");
            } else {
                logger.debug("CheckMgrExtendServiceImpl.accountTreatment()，机构: " + rutCorg + "; 对账批次号: " + chkBatNo + " 通知对账模块同步状态失败");
            }
            return CpiConstants.CHK_FIL_FINISHED;
        } else {
            return accControlDO.getChkFilSts();
        }

    }

    @Override
    @SuppressWarnings("unchecked")
    public String getFundChkFile(AccControlDO accControlDO, AccFundCfgDO accFundCfgDO) throws Exception {
        logger.debug("==================获取银行机构对账文件==================");
        String chkFilSts = accControlDO.getChkFilSts();
        try {

            //根据配置的反射类进行反射
            Class chkClazz = Class.forName(accFundCfgDO.getChkClazz());
            Method method = chkClazz.getMethod(accFundCfgDO.getGetFileMethod(), String.class,LocalDate.class);

            //将首字母转成小写
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.append(Character.toLowerCase(chkClazz.getSimpleName().charAt(0)));//首字母转为小写
            stringBuilder.append(chkClazz.getSimpleName().substring(1));//从第一位开始截取字符串
            String beanName = stringBuilder.toString();//所需bean的名称

            //获取到Spring容器中的对象
            Object instance = ExtensionLoader.getSpringBean(beanName, chkClazz);

            //调用银行API的获取对账文件方法
            Object rspObject = method.invoke(instance, accFundCfgDO.getChkFilePath(), accControlDO.getChkFilDt());
            if (JudgeUtils.isNotNull(rspObject)) {
                chkFilSts = CpiConstants.CHK_FIL_DOWNLOAD;
                accControlDO.setChkFilSts(chkFilSts);
                accControlDO.setChkFilNm((String)rspObject);
                //更新对账批次的对账状态
                AccControlDO updateAccControlDO = new AccControlDO();
                updateAccControlDO.setChkBatNo(accControlDO.getChkBatNo());
                updateAccControlDO.setFileRcvDt(DateTimeUtils.getCurrentLocalDate());
                updateAccControlDO.setChkFilSts(chkFilSts);
                updateAccControlDO.setChkFilNm((String)rspObject);
                accControlDao.update(updateAccControlDO);
            } else {
                return accControlDO.getChkFilSts();
            }
        } catch (Exception e){
            logger.error("CheckMgrExtendServiceImpl.getFundChkFile()，获取充值对账文件失败，异常为: ", e);
        }
        return accControlDO.getChkFilSts();
    }


    @Override
    public String writeFundChkFile(AccControlDO accControlDO, AccFundCfgDO accFundCfgDO) throws Exception{
        //文件名： 主方_对手方_对账文件日期.dat，文件中包括该对账文件日期内所有的充值订单(快捷、网银、汇款)
        String chkFilePath = accFundCfgDO.getChkFilePath() + File.separator + accControlDO.getRutCorg() + "_CPI_" + DateTimeUtils.formatLocalDate(accControlDO.getChkFilDt()) + ".dat";
        File file = new File(chkFilePath);
        int beginNum = 0;
        int countNum = CpiConstants.MAX_WRITE_NUM;
        int i = 0;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            //如果对账文件存在，先删除再进行写入
            if(file.exists()){
                file.delete();
            }
            while (true) {
                beginNum = countNum * i;
                List<BaseAccChkFilBO> chkFilBOList = new ArrayList<>();
                if (CorpBusTyp.FASTPAY.getType().equals(accControlDO.getCorpBusTyp())) {
                    //获取快捷成功订单
                    List<ShortcutOrderDO> shortcutOrderDOList = shortcutOrderDao.getShotcutOrderListByChkFilDt(accControlDO.getRutCorg(), accControlDO.getChkFilDt(), beginNum, countNum);
                    for (ShortcutOrderDO shortcutOrderDO : shortcutOrderDOList) {
                        BaseAccChkFilBO bo = new BaseAccChkFilBO();
                        bo.setChkKey(shortcutOrderDO.getChkKey());
                        bo.setChkAmt(shortcutOrderDO.getOrdAmt());
                        chkFilBOList.add(bo);
                    }
                } else if (CorpBusTyp.EBANKPAY.getType().equals(accControlDO.getCorpBusTyp())) {
                    //获取网银成功订单
                    List<EbankOrderDO> ebankOrderDOList = ebankOrderDao.getEbankpayOrderListByChkFilDt(accControlDO.getRutCorg(), accControlDO.getChkFilDt(), beginNum, countNum);
                    for (EbankOrderDO ebankOrderDO : ebankOrderDOList) {
                        BaseAccChkFilBO bo = new BaseAccChkFilBO();
                        bo.setChkKey(ebankOrderDO.getChkKey());
                        bo.setChkAmt(ebankOrderDO.getOrdAmt());
                        chkFilBOList.add(bo);
                    }
                } else {
                    return null;
                }
                FileUtils.writeAppend(stringBuilder.toString(), chkFilePath);
                if (CollectionUtils.isNotEmpty(chkFilBOList)) {
                    stringBuilder = new StringBuilder();
                    for (BaseAccChkFilBO bo : chkFilBOList) {
                        stringBuilder.append(bo.getChkKey()).append("|").append(bo.getChkAmt()).append("\n");
                    }

                } else {
                    //查无对账明细数据，退出循环
                    break;
                }
                i++;
            }
            AccControlDO updateDo = new AccControlDO();
            updateDo.setChkBatNo(accControlDO.getChkBatNo());
            updateDo.setChkFilSts(CpiConstants.CHK_FIL_WRITE);
            accControlDao.update(updateDo);
        }catch (Exception e){
            logger.error("写入对账文件失败：", e);
            file.delete();
            return null;
        }
        return chkFilePath;
    }

    @Override
    public String uploadFundChkFile(String chkBatNo,String chkFilPath,String chkFilSts,AccFundCfgDO accFundCfgDO) throws Exception{
        String uploadIp = accFundCfgDO.getUploadIp();
        int uploadPort = Integer.parseInt(accFundCfgDO.getUploadPort());
        int connectTimeout = 120000;
        String uploadPath = accFundCfgDO.getUploadPath();
        String name = accFundCfgDO.getUploadName();
        String pwd = accFundCfgDO.getUploadPwd();
        FileSftpUtils.upload(chkFilPath,uploadIp,uploadPort,connectTimeout,uploadPath,name,pwd);
        AccControlDO updateDo = new AccControlDO();
        updateDo.setChkBatNo(chkBatNo);
        updateDo.setChkFilSts(chkFilSts);
        accControlDao.update(updateDo);
        return chkFilSts;
    }

    /**
     * 通知对账模块同步状态(仅针对退款)
     * @param accControlDO
     * @param chkSts
     * @return
     */
    private boolean notifyChkModule(AccControlDO accControlDO,String chkSts){
        //通知修改结果
        GenericDTO<ControlDTO> genericDTO = new GenericDTO<>();
        ControlDTO controlDTO = new ControlDTO();
        controlDTO.setChkFilDt(accControlDO.getChkFilDt());
        controlDTO.setChkBusTyp(ChkConstants.REFUND);
        controlDTO.setMainNo(accControlDO.getRutCorg());
        controlDTO.setOppoNo("CPI");
        controlDTO.setMainTotCnt(accControlDO.getFilTotCnt());
        controlDTO.setMainTotAmt(accControlDO.getFilTotAmt());
        if(CpiConstants.ICBC.equals(accControlDO.getRutCorg())) {
            controlDTO.setChkBusSubTyp(ChkConstants.REFUND_ICBC_CPI);
        }
        else if(CpiConstants.BESTPAY.equals(accControlDO.getRutCorg())) {
            controlDTO.setChkBusSubTyp(ChkConstants.REFUND_BESTPAY_CPI);
        }
        else if (CpiConstants.WECHAT.equals(accControlDO.getRutCorg())) {
            controlDTO.setChkBusSubTyp(ChkConstants.REFUND_WECHAT_CPI);
        }else if(CpiConstants.ALIPAY.equals(accControlDO.getRutCorg())){
            controlDTO.setChkBusSubTyp(ChkConstants.REFUND_ALIPAY_CPI);
        }
        else {
            logger.error("机构: " + accControlDO.getRutCorg() + "; 业务: " + accControlDO.getCorpBusTyp() + "; 子业务: "
                    + accControlDO.getCorpBusSubTyp() + "; 对账批次号: " + accControlDO.getChkBatNo() + ";对账状态"
                    +accControlDO.getChkFilSts()+" 对账模块不支持该银行");
            return false;
        }
        BeanUtils.copyProperties(controlDTO,accControlDO);
        controlDTO.setChkFilSts(chkSts);
        genericDTO.setBody(controlDTO);
        try {
            GenericRspDTO<NoBody> rspGeneric = chkClient.updateControl(genericDTO);
            if (JudgeUtils.isNotSuccess(rspGeneric.getMsgCd())) {
                logger.error("机构: " + accControlDO.getRutCorg() + "; 业务: " + accControlDO.getCorpBusTyp() + "; 子业务: "
                        + accControlDO.getCorpBusSubTyp() + "; 对账批次号: " + accControlDO.getChkBatNo() + ";对账状态"
                        + accControlDO.getChkFilSts() + " 对账模块同步状态出错");
                return false;
            }
            return true;
        }catch (Exception e){
            logger.error("机构: " + accControlDO.getRutCorg() + "; 业务: " + accControlDO.getCorpBusTyp() + "; 子业务: "
                    + accControlDO.getCorpBusSubTyp() + "; 对账批次号: " + accControlDO.getChkBatNo() + ";对账状态"
                    + accControlDO.getChkFilSts() + " 对账模块同步状态异常：", e);
            return false;
        }
    }

}
