package com.hisun.lemon.cpi.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.cpi.bestpay.ebankpay.BestPaySecurity;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.PlaceOrderNotifyReq;
import com.hisun.lemon.cpi.bestpay.ebankpay.req.RefundNotifyReq;
import com.hisun.lemon.cpi.service.IEbankNotifyService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by R<PERSON> on 2017/7/26.
 */
@RestController
@RequestMapping("/cpi/bestpay")
@Api(tags="EbankNotifyController", description="网银结果通知服务")
public class EbankNotifyController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(EbankNotifyController.class);

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private IEbankNotifyService ebankNotifyService;

    @Resource
    private BestPaySecurity bestPaySecurity;

    /**
     * 用户扫码或商户扫码支付，翼支付结果通知
     */
    @PostMapping("/placeOrder")
    public void placeOrderNotify(PlaceOrderNotifyReq placeOrderNotifyReq) {
        logger.info("=========EbankNotifyController.placeOrderNotify()翼支付扫码订单结果通知=========");
        if (null != placeOrderNotifyReq) {
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, placeOrderNotifyReq, true);
            logger.info("EbankNotifyController.placeOrderNotify()翼支付扫码订单结果通知：" + rspData);
            //验签
            if (bestPaySecurity.isVerify(placeOrderNotifyReq)) {
                ebankNotifyService.bestPayFundNotify(placeOrderNotifyReq);
                logger.debug("EbankNotifyController.placeOrderNotify()翼支付扫码订单结果通知，验签通过");
            } else {
                logger.debug("EbankNotifyController.placeOrderNotify()翼支付扫码订单结果通知，验签不通过");
            }
        }
    }

    /**
     * 用户扫码退款或商户扫码退款，翼支付结果通知
     */
    @PostMapping("/refund")
    public void refund(RefundNotifyReq refundNotifyReq) {
        logger.debug("=========EbankNotifyController.refund()翼支付退款订单结果通知=========");
        if (null != refundNotifyReq) {
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, refundNotifyReq, true);
            logger.debug("BestPayScanPayApi.refund()翼支付退款订单结果通知：" + rspData);
            //验签
            if (bestPaySecurity.isVerify(refundNotifyReq)) {
                ebankNotifyService.bestPayRefundNotify(refundNotifyReq);
                logger.debug("EbankNotifyController.refund()翼支付退款订单结果通知，验签通过");
            } else {
                logger.debug("EbankNotifyController.refund()翼支付退款订单结果通知，验签不通过");
            }
        }
    }

}
