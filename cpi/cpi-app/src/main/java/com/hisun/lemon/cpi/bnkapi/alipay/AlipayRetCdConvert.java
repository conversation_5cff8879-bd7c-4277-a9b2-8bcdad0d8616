package com.hisun.lemon.cpi.bnkapi.alipay;

import com.hisun.lemon.cpi.common.CpiMsgCd;
import org.springframework.stereotype.Component;

/**
 * User : Rui
 * Date : 2017/9/6
 * Time : 14:49
 **/
@Component("alipayRetCdConvert")
public class AlipayRetCdConvert {

    public CpiMsgCd Convert(String retCode){
        switch (retCode){
            case "0000":
            case "000000":
            case "31":
                return CpiMsgCd.SUCCESS;
            case "2007":
                return CpiMsgCd.BNK_70101;
            case "22":
                return CpiMsgCd.BNK_70102;
            case "32":
                return CpiMsgCd.BNK_70200;
            default:
                return CpiMsgCd.BNK_70200;
        }
    }

    /**
     * 扫码下单返回码结果转义
     * @param txFlg
     * @return
     */
    public CpiMsgCd txFlgConvert(String txFlg){
        switch (txFlg) {
            case "S":
                return CpiMsgCd.SUCCESS;
            case "W":
                return CpiMsgCd.BNK_70102;
            case "F":
                return CpiMsgCd.BNK_70200;
            default:
                return CpiMsgCd.BNK_70102;
        }
    }

}
