package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.dao.IFundOrderDao;
import com.hisun.lemon.cpi.dao.IPosOrderDao;
import com.hisun.lemon.cpi.dao.IPosPreAuJrnDao;
import com.hisun.lemon.cpi.dto.PosPayReqDTO;
import com.hisun.lemon.cpi.entity.FundOrderDO;
import com.hisun.lemon.cpi.entity.PosOrderDO;
import com.hisun.lemon.cpi.entity.PosPreAuJrnDO;
import com.hisun.lemon.cpi.entity.RouteDO;
import com.hisun.lemon.cpi.service.IPosTransaction;
import com.hisun.lemon.framework.utils.IdGenUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Description
 * @date 2017/10/16 19:25
 */
@Service
public class PosTransactionImpl implements IPosTransaction{

    @Resource
    IPosOrderDao posOrderDao;
    @Resource
    IFundOrderDao fundOrderDao;
    @Resource
    IPosPreAuJrnDao posPreAuJrnDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public PosOrderDO createPosOrder(PosPayReqDTO posPayReqDTO, RouteDO rspRouteInfo, String fndOrdNo) {
        //创建充值订单
        FundOrderDO fundOrderDO = new FundOrderDO();
        BeanUtils.copyProperties(fundOrderDO, posPayReqDTO);
        BeanUtils.copyProperties(fundOrderDO, rspRouteInfo);
        fundOrderDO.setCorpBusTyp(posPayReqDTO.getCorpBusTyp().getType());
        fundOrderDO.setCorpBusSubTyp(posPayReqDTO.getCorpBusSubTyp().getType());
        fundOrderDO.setFudOrdNo(fndOrdNo);
        //等待充值
        fundOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY);
        fundOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        fundOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
        //等待通知
        fundOrderDO.setNtfSts(CpiConstants.NOTIFY_WAIT);
        fundOrderDao.insert(fundOrderDO);

        //创建网银订单
        PosOrderDO posOrderDO = new PosOrderDO();
        String subOrdNo = IdGenUtils.generateIdWithDateTime("SUB_ORD_NO", "CPI", 6);
        String chkKey = IdGenUtils.generateIdWithDateTime("OUT_ORD_NO",6);
        BeanUtils.copyProperties(posOrderDO, posPayReqDTO);
        posOrderDO.setSubOrdNo(subOrdNo);
        posOrderDO.setCorpBusTyp(posPayReqDTO.getCorpBusTyp().getType());
        posOrderDO.setCorpBusSubTyp(posPayReqDTO.getCorpBusSubTyp().getType());
        //等待充值
        posOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY);
        posOrderDO.setFndOrdNo(fndOrdNo);
        posOrderDO.setChkKey(chkKey);
        posOrderDO.setCrdCorpOrg(rspRouteInfo.getCrdCorpOrg());
        posOrderDO.setRutCorpOrg(rspRouteInfo.getRutCorpOrg());
        posOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        posOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
        posOrderDao.insert(posOrderDO);
        return posOrderDO;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public void insertCardProtJrnDO(PosPreAuJrnDO posPreAuJrnDO) {
        posPreAuJrnDao.insert(posPreAuJrnDO);
    }
}
