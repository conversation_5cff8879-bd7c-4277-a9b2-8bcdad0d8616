/*
 * @ClassName AccRefundCfgDO
 * @Description 
 * @version 1.0
 * @Date 2017-09-11 11:32:56
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AccRefundCfgDO extends BaseDO {
    /**
     * @Fields accCfgId id
     */
    private String accCfgId;
    /**
     * @Fields rutCorg 路径机构
     */
    private String rutCorg;
    /**
     * @Fields corpBusTyp 业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields chkClazz 银行API类
     */
    private String chkClazz;
    /**
     * @Fields getFileMethod 获取对账文件的方法
     */
    private String getFileMethod;
    /**
     * @Fields importFields 银行对账文件每行记录对应的字段
     */
    private String importFields;
    /**
     * @Fields importClass 对账文件入库对应的DO类
     */
    private String importClass;
    /**
     * @Fields importMethod 解析对账文件并入库的方法
     */
    private String importMethod;
    /**
     * @Fields splitSign 对账文件内容分隔符
     */
    private String splitSign;
    /**
     * @Fields chkFilePath 对账文件本地路径
     */
    private String chkFilePath;
    /**
     * @Fields continueNum 解析对账文件跳过多少行
     */
    private Integer continueNum;
    /**
     * @Fields selectDetailMethod 银行API查询对账明细的方法
     */
    private String selectDetailMethod;
    /**
     * @Fields successFlag 银行明细状态成功对应的值
     */
    private String successFlag;
    /**
     * @Fields checkKeyFiled 对账键值对应的域
     */
    private String checkKeyFiled;
    /**
     * @Fields checkKeyBakFiled 对账键值备份对应的域(银行摘要、附言)
     */
    private String checkKeyBakFiled;
    /**
     * @Fields checkAmtFiled 对账金额对应的域
     */
    private String checkAmtFiled;
    /**
     * @Fields txStsFiled 银行明细状态对应的域
     */
    private String txStsFiled;
    /**
     * @Fields queryNum 对账时每次查询的最大笔数
     */
    private Integer queryNum;
    /**
     * @Fields updateMethod 更新银行明细的对账状态方法
     */
    private String updateMethod;
    /**
     * @Fields uploadIp 上传ip
     */
    private String uploadIp;
    /**
     * @Fields uploadPort 上传端口
     */
    private String uploadPort;
    /**
     * @Fields uploadPath 上传路径
     */
    private String uploadPath;
    /**
     * @Fields uploadName 登录用户名
     */
    private String uploadName;
    /**
     * @Fields uploadPwd 登录密码
     */
    private String uploadPwd;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getAccCfgId() {
        return accCfgId;
    }

    public void setAccCfgId(String accCfgId) {
        this.accCfgId = accCfgId;
    }

    public String getRutCorg() {
        return rutCorg;
    }

    public void setRutCorg(String rutCorg) {
        this.rutCorg = rutCorg;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getChkClazz() {
        return chkClazz;
    }

    public void setChkClazz(String chkClazz) {
        this.chkClazz = chkClazz;
    }

    public String getGetFileMethod() {
        return getFileMethod;
    }

    public void setGetFileMethod(String getFileMethod) {
        this.getFileMethod = getFileMethod;
    }

    public String getImportFields() {
        return importFields;
    }

    public void setImportFields(String importFields) {
        this.importFields = importFields;
    }

    public String getImportClass() {
        return importClass;
    }

    public void setImportClass(String importClass) {
        this.importClass = importClass;
    }

    public String getImportMethod() {
        return importMethod;
    }

    public void setImportMethod(String importMethod) {
        this.importMethod = importMethod;
    }

    public String getSplitSign() {
        return splitSign;
    }

    public void setSplitSign(String splitSign) {
        this.splitSign = splitSign;
    }

    public String getChkFilePath() {
        return chkFilePath;
    }

    public void setChkFilePath(String chkFilePath) {
        this.chkFilePath = chkFilePath;
    }

    public Integer getContinueNum() {
        return continueNum;
    }

    public void setContinueNum(Integer continueNum) {
        this.continueNum = continueNum;
    }

    public String getSelectDetailMethod() {
        return selectDetailMethod;
    }

    public void setSelectDetailMethod(String selectDetailMethod) {
        this.selectDetailMethod = selectDetailMethod;
    }

    public String getSuccessFlag() {
        return successFlag;
    }

    public void setSuccessFlag(String successFlag) {
        this.successFlag = successFlag;
    }

    public String getCheckKeyFiled() {
        return checkKeyFiled;
    }

    public void setCheckKeyFiled(String checkKeyFiled) {
        this.checkKeyFiled = checkKeyFiled;
    }

    public String getCheckKeyBakFiled() {
        return checkKeyBakFiled;
    }

    public void setCheckKeyBakFiled(String checkKeyBakFiled) {
        this.checkKeyBakFiled = checkKeyBakFiled;
    }

    public String getCheckAmtFiled() {
        return checkAmtFiled;
    }

    public void setCheckAmtFiled(String checkAmtFiled) {
        this.checkAmtFiled = checkAmtFiled;
    }

    public String getTxStsFiled() {
        return txStsFiled;
    }

    public void setTxStsFiled(String txStsFiled) {
        this.txStsFiled = txStsFiled;
    }

    public Integer getQueryNum() {
        return queryNum;
    }

    public void setQueryNum(Integer queryNum) {
        this.queryNum = queryNum;
    }

    public String getUpdateMethod() {
        return updateMethod;
    }

    public void setUpdateMethod(String updateMethod) {
        this.updateMethod = updateMethod;
    }

    public String getUploadIp() {
        return uploadIp;
    }

    public void setUploadIp(String uploadIp) {
        this.uploadIp = uploadIp;
    }

    public String getUploadPort() {
        return uploadPort;
    }

    public void setUploadPort(String uploadPort) {
        this.uploadPort = uploadPort;
    }

    public String getUploadPath() {
        return uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public String getUploadName() {
        return uploadName;
    }

    public void setUploadName(String uploadName) {
        this.uploadName = uploadName;
    }

    public String getUploadPwd() {
        return uploadPwd;
    }

    public void setUploadPwd(String uploadPwd) {
        this.uploadPwd = uploadPwd;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}