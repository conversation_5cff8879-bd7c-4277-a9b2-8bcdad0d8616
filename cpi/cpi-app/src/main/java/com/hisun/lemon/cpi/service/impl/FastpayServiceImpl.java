package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.cmm.client.SmsServerClient;
import com.hisun.lemon.cmm.dto.SmsCheckReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.bnkapi.acleda.fastpay.FastpayACLEDAApi;
import com.hisun.lemon.cpi.bnkapi.icbc.ICBCRetCdConvert;
import com.hisun.lemon.cpi.bnkapi.icbc.fastpay.FastpayICBCApi;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.*;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.mq.FundNotifyProduce;
import com.hisun.lemon.cpi.service.IFastpayService;
import com.hisun.lemon.cpi.service.IFastpayTransaction;
import com.hisun.lemon.cpi.utils.EncryptUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
//import com.hisun.lemon.jcommon.phonenumber.PhoneNumberUtils;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import com.hisun.lemon.urm.dto.UserRealNameDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rui on 2017/7/7.
 */
@Transactional
@Service
public class FastpayServiceImpl extends BaseService implements IFastpayService {

    @Resource
    ICardProtJrnDao cardProtJrnDao;

    @Resource
    ICardBinDao cardBinDao;

    @Resource
    IRouteDao routeDao;

    @Resource
    IShortcutParamDao shortcutParamDao;

    @Resource
    ICardProtDao cardProtDao;

    @Resource
    IFundOrderDao fundOrderDao;

    @Resource
    IShortcutOrderDao shortcutOrderDao;

    @Resource
    FastpayICBCApi fastpayICBCApi;

    @Resource
    IFastpayTransaction fastpayTransaction;

    @Resource
    UserBasicInfClient userBasicInfClient;

    @Resource
    RiskCheckClient riskCheckClient;

    @Resource
    SmsServerClient smsServerClient;

    @Resource
    ICBCRetCdConvert icbcRetCdConvert;

    @Resource
    FastpayACLEDAApi fastpayACLEDAApi;

    @Resource
    private EncryptUtils encryptUtils;

    @Resource
    private FundNotifyProduce fundNotifyProduce;

    private static final Logger logger = LoggerFactory.getLogger(FastpayServiceImpl.class);

    /**
     * 用户银行卡预签约，银行(短信平台)下发短信验证码
     */
    @Override
    public GenericRspDTO<CardPreRspDTO> preBindCard(GenericDTO<CardPreReqDTO> genericDTO) {

        //请求对象
        CardPreReqDTO cardPreReqDTO = genericDTO.getBody();

        //返回对象
        CardPreRspDTO cardPreRspDTO = new CardPreRspDTO();

        try {
            //Step1:业务类型和业务子类型
            String corpBusTyp = CorpBusTyp.SIGN.getType();
            String corpBusSubTyp = CorpBusSubTyp.PREFAST_SIGN.getType();

            //若认证标识为N-未认证，则对证件号码进行加密
            String idNoEnc = null;
            if (CpiConstants.AUTHENTICATE_NO.equals(cardPreReqDTO.getAuthFlag())) {
                idNoEnc = encryptUtils.encrypt(cardPreReqDTO.getIdNo(), CpiConstants.ENCRYPT);
            } else {
                //查询用户接口获取身份证信息
                GenericRspDTO<UserBasicInfDTO> user = userBasicInfClient.queryUser(LemonUtils.getUserId());
                if (JudgeUtils.isNotSuccess(user.getMsgCd())) {
                    throw new LemonException(user.getMsgCd());
                }
                idNoEnc = user.getBody().getIdNo();
            }

            //Step2:查询卡BIN,判断资金机构
            String crdNoEnc = encryptUtils.encrypt(cardPreReqDTO.getCrdNo().trim(), CpiConstants.ENCRYPT);
            String crdNo = cardPreReqDTO.getCrdNo().trim();
            int crdLen = crdNo.length();
            CardBinDO cardBinDO = cardBinDao.selectByCardBin(crdNo, crdLen);
            if (JudgeUtils.isNull(cardBinDO) || JudgeUtils.isBlank(cardBinDO.getCapCorg())) {
                logger.debug("FastpayServiceImpl.preBindCard() 卡BIN检查不通过");
                return GenericRspDTO.newInstance(CpiMsgCd.CARD_BIN_IS_NOT_FUND.getMsgCd(), cardPreRspDTO);
            }

            //查询该卡是否已经绑定
            List<CardProtDO> list = cardProtDao.selectBindCard(LemonUtils.getUserId(), crdNoEnc);
            if (null != list && list.size() > 0) {
                return GenericRspDTO.newInstance(CpiMsgCd.CARD_IS_BINDED.getMsgCd(), cardPreRspDTO);
            }

            //Step3:查询路由
            RouteDO routeDO = new RouteDO();
            routeDO.setCorpBusTyp(corpBusTyp);
            routeDO.setCorpBusSubTyp(corpBusSubTyp);
            routeDO.setCrdAcTyp(cardPreReqDTO.getCrdAcTyp());
            routeDO.setCrdCorpOrg(cardPreReqDTO.getCrdCorpOrg());
            RouteDO rspRouteInfo = routeDao.queryRouteInfo(routeDO, null);
            if (JudgeUtils.isNull(rspRouteInfo) || JudgeUtils.isBlank(rspRouteInfo.getRutCorpOrg())) {
                logger.debug("FastpayServiceImpl.preBindCard() 没有可用路由");
                return GenericRspDTO.newInstance(CpiMsgCd.ROUTE_INFO_IS_NOT_FUND.getMsgCd(), cardPreRspDTO);
            }

            //Step4:查询快捷参数表，检查短信发送方式,是单侧还是双侧协议
            ShortcutParamDO shortcutParamDO = shortcutParamDao.selectByUniqueKey(rspRouteInfo.getRutCorpOrg(), rspRouteInfo.getCrdAcTyp());
            if (JudgeUtils.isNull(shortcutParamDO)) {
                logger.debug("FastpayServiceImpl.preBindCard() 没有可用的快捷参数信息");
                return GenericRspDTO.newInstance(CpiMsgCd.SHORT_PARAM_INFO_IS_NOT_FUND.getMsgCd(), cardPreRspDTO);
            }

            //Step5:用户状态检查和银行卡检查
            RiskCheckUserStatusReqDTO userStatusReqDTO = new RiskCheckUserStatusReqDTO();
            //用户状态检查
            userStatusReqDTO.setId(LemonUtils.getUserId());
            userStatusReqDTO.setIdTyp(Constants.ID_TYP_USER_NO);
            //交易类型
            userStatusReqDTO.setTxTyp(Constants.TX_TYP_RECHARGE);
            GenericRspDTO<NoBody> riskRsp = riskCheckClient.checkUserStatus(userStatusReqDTO);
            if (JudgeUtils.isNotSuccess(riskRsp.getMsgCd())) {
                throw new LemonException(riskRsp.getMsgCd());
            }
            //银行卡检查
            userStatusReqDTO.setId(cardPreReqDTO.getCrdNo());
            userStatusReqDTO.setIdTyp(Constants.ID_TYP_CARD);
            //交易类型
            userStatusReqDTO.setTxTyp(Constants.TX_TYP_RECHARGE);
            riskRsp = riskCheckClient.checkUserStatus(userStatusReqDTO);
            if (JudgeUtils.isNotSuccess(riskRsp.getMsgCd())) {
                throw new LemonException(riskRsp.getMsgCd());
            }

            //step6:插入流水表
            String jrnNo = IdGenUtils.generateIdWithDateTime("JRN_NO", "CPI", 6);
            CardProtJrnDO cardProtJrnDO = new CardProtJrnDO();
            BeanUtils.copyProperties(cardProtJrnDO, rspRouteInfo);
            BeanUtils.copyProperties(cardProtJrnDO, cardPreReqDTO);
            cardProtJrnDO.setUserId(LemonUtils.getUserId());
            cardProtJrnDO.setBndFlg(CpiConstants.SIGN_PRE);//预签约
            cardProtJrnDO.setTxDt(DateTimeUtils.getCurrentLocalDate());
            cardProtJrnDO.setTxTm(DateTimeUtils.getCurrentLocalTime());
            cardProtJrnDO.setJrnNo(jrnNo);
            cardProtJrnDO.setCorpBusTyp(corpBusTyp);
            cardProtJrnDO.setCorpBusSubTyp(corpBusSubTyp);
            cardProtJrnDO.setCrdNoEnc(crdNoEnc);
            cardProtJrnDO.setIdNoEnc(idNoEnc);
            if (!JudgeUtils.isEmpty(cardPreReqDTO.getCrdCvv2())) {
                //cvv2加密存储
                String crdCvv2Enc = encryptUtils.encrypt(cardPreReqDTO.getCrdCvv2(), CpiConstants.ENCRYPT);
                cardProtJrnDO.setCrdCvv2Enc(crdCvv2Enc);
            }
            if (!JudgeUtils.isEmpty(cardPreReqDTO.getCrdExpDt())) {
                //有效期加密存储
                String crdExpDtEnc = encryptUtils.encrypt(cardPreReqDTO.getCrdExpDt(), CpiConstants.ENCRYPT);
                cardProtJrnDO.setCrdExpDtEnc(crdExpDtEnc);
            }

            //这里用事务的insert，这样在与银行交互之前提交
            fastpayTransaction.insertCardProtJrnDO(cardProtJrnDO);
            CpiMsgCd msgCd = CpiMsgCd.SUCCESS;

            //Step7:调用发短信（银行或者我方发）
            switch (shortcutParamDO.getSmsTyp()) {
                case CpiConstants.BANK_SMS_SEND_BANK_CHECK:
                    //调用银行下发组件
                    msgCd = this.bankPreBindCard(cardProtJrnDO);
                    if(JudgeUtils.isNotSuccess(msgCd.getMsgCd())){
                        throw new LemonException(msgCd.getMsgCd());
                    }
                    break;
                case CpiConstants.PLAT_SMS_SEND_PLAT_CHECK:
                    GenericDTO<SmsCodeReqDTO> reqDTO = new GenericDTO<>();
                    SmsCodeReqDTO smsCodeReqDTO = new SmsCodeReqDTO();
                    //去掉+86-,前端默认传了国家代码
         //           smsCodeReqDTO.setMblNo(PhoneNumberUtils.getPhoneNumber(cardPreReqDTO.getMblNo()));
                    smsCodeReqDTO.setSmsCodeType("4");
                    reqDTO.setBody(smsCodeReqDTO);
                    GenericRspDTO<SmsCodeRspDTO> smsRspDTO = smsServerClient.smsCodeSend(reqDTO);
                    if(JudgeUtils.isNotSuccess(smsRspDTO.getMsgCd())){
                        throw new LemonException(smsRspDTO.getMsgCd());
                    }else {
                        cardPreRspDTO.setSmsToken(smsRspDTO.getBody().getToken());
                    }
                default:
                    break;
            }

            //Step8:返回信息
            BeanUtils.copyProperties(cardPreRspDTO, cardPreReqDTO);
            cardPreRspDTO.setJrnNo(jrnNo);
            return GenericRspDTO.newInstance(msgCd.getMsgCd(), cardPreRspDTO);
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), cardPreRspDTO);
        } catch (Exception e) {
            logger.error("FastpayServiceImpl.preBindCard exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), cardPreRspDTO);
        }

    }

    /**
     * 绑定用户银行卡信息
     */
    @Override
    public GenericRspDTO<CardBindRspDTO> bindCard(GenericDTO<CardBindReqDTO> genericDTO) {

        //请求对象
        CardBindReqDTO cardBindReqDTO = genericDTO.getBody();

        //返回对象
        CardBindRspDTO cardBindRspDTO = new CardBindRspDTO();

        try {
            //Step1:业务类型和业务子类型
            String corpBusTyp = CorpBusTyp.SIGN.getType();
            String corpBusSubTyp = CorpBusSubTyp.FAST_SIGN.getType();

            //Step2:查询预签约流水,校验一些基本签约信息是否匹配
            CardProtJrnDO cardProtJrnDO = cardProtJrnDao.get(cardBindReqDTO.getJrnNo());
            if (JudgeUtils.isNull(cardProtJrnDO)) {
                logger.debug("FastpayServiceImpl.bindCard() 预签约流水不存在");
                return GenericRspDTO.newInstance(CpiMsgCd.BIND_CARD_INFO_ERROR.getMsgCd(), cardBindRspDTO);
            }
            if (!cardProtJrnDO.getBndFlg().equals(CpiConstants.SIGN_PRE)
                    || !cardProtJrnDO.getCrdUsrNm().equals(cardBindReqDTO.getCrdUsrNm())
                    || !cardProtJrnDO.getCrdAcTyp().equals(cardBindReqDTO.getCrdAcTyp())
                    || !cardProtJrnDO.getCrdCorpOrg().equals(cardBindReqDTO.getCrdCorpOrg())) {
                return GenericRspDTO.newInstance(CpiMsgCd.BIND_CARD_INFO_ERROR.getMsgCd(), cardBindRspDTO);
            }

            //查询是否绑定，绑定过了则成功
            List<CardProtDO> list = cardProtDao.selectBindCard(cardProtJrnDO.getUserId(),cardProtJrnDO.getCrdNoEnc());
            if (null != list && list.size() > 0) {
                BeanUtils.copyProperties(cardBindRspDTO, cardBindReqDTO);
                cardBindRspDTO.setArgNo(list.get(0).getAgrNo());
                return GenericRspDTO.newSuccessInstance(cardBindRspDTO);
            }

            //Step3:用户状态检查
            RiskCheckUserStatusReqDTO userStatusReqDTO = new RiskCheckUserStatusReqDTO();
            //用户状态检查
            userStatusReqDTO.setId(LemonUtils.getUserId());
            userStatusReqDTO.setIdTyp(Constants.ID_TYP_USER_NO);
            //交易类型
            userStatusReqDTO.setTxTyp(Constants.TX_TYP_RECHARGE);
            GenericRspDTO<NoBody> riskRsp = riskCheckClient.checkUserStatus(userStatusReqDTO);
            if (JudgeUtils.isNotSuccess(riskRsp.getMsgCd())) {
                throw new LemonException(riskRsp.getMsgCd());
            }

            //Step3:插入流水表
            CardProtJrnDO bindCardJrnDo = new CardProtJrnDO();
            BeanUtils.copyProperties(bindCardJrnDo, cardProtJrnDO);
            BeanUtils.copyProperties(bindCardJrnDo, cardBindReqDTO);
            String jrnNo = IdGenUtils.generateIdWithDateTime("JRN_NO", "CPI", 6);
            bindCardJrnDo.setUserId(LemonUtils.getUserId());
            bindCardJrnDo.setBndFlg(CpiConstants.SIGN_SUCCESS);
            bindCardJrnDo.setCorpBusTyp(corpBusTyp);
            bindCardJrnDo.setCorpBusSubTyp(corpBusSubTyp);
            bindCardJrnDo.setTxDt(DateTimeUtils.getCurrentLocalDate());
            bindCardJrnDo.setTxTm(DateTimeUtils.getCurrentLocalTime());
            bindCardJrnDo.setJrnNo(jrnNo);
            bindCardJrnDo.setRutCorpOrg(cardProtJrnDO.getRutCorpOrg());

            //这里用事务的insert，这样在与银行交互之前提交
            fastpayTransaction.insertCardProtJrnDO(bindCardJrnDo);

            //Step4:查询快捷参数表，检查短信发送方式,是单侧还是双侧协议
            ShortcutParamDO shortcutParamDO = shortcutParamDao.selectByUniqueKey(cardProtJrnDO.getRutCorpOrg(), cardProtJrnDO.getCrdAcTyp());
            if (JudgeUtils.isNull(shortcutParamDO)) {
                return GenericRspDTO.newInstance(CpiMsgCd.SHORT_PARAM_INFO_IS_NOT_FUND.getMsgCd(), cardBindRspDTO);
            }

            //Step5:调用银行签约
            CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
            switch (shortcutParamDO.getSmsTyp()) {
                case CpiConstants.BANK_SMS_SEND_BANK_CHECK:
                    //调用银行下发组件
                    msgCd = this.bankBindCard(cardProtJrnDO, cardBindReqDTO.getChkNo());
                    if(JudgeUtils.isNotSuccess(msgCd.getMsgCd())){
                        throw new LemonException(msgCd.getMsgCd());
                    }
                    break;
                case CpiConstants.PLAT_SMS_SEND_PLAT_CHECK:
                    if(JudgeUtils.isEmpty(cardBindReqDTO.getSmsToken())){
                        return GenericRspDTO.newInstance(CpiMsgCd.SMS_TOKEN_IS_NULL.getMsgCd(), cardBindRspDTO);
                    }
                    GenericDTO<SmsCheckReqDTO> reqDTO = new GenericDTO<>();
                    SmsCheckReqDTO smsCheckReqDTO = new SmsCheckReqDTO();
                    smsCheckReqDTO.setType("4");//短信下发预签约
            //        smsCheckReqDTO.setMblNo(PhoneNumberUtils.getPhoneNumber(cardBindReqDTO.getMblNo()));
                    smsCheckReqDTO.setSmsCode(cardBindReqDTO.getChkNo());
                    smsCheckReqDTO.setToken(cardBindReqDTO.getSmsToken());
                    reqDTO.setBody(smsCheckReqDTO);
                    GenericRspDTO<NoBody> smsRspDTO = smsServerClient.smsCodeCheck(reqDTO);
                    if(JudgeUtils.isNotSuccess(smsRspDTO.getMsgCd())){
                        throw new LemonException(smsRspDTO.getMsgCd());
                    }
                default:
                    break;
            }

            //Step6:插入签约信息表
            CardProtDO cardProtDO = new CardProtDO();
            String agrNo = IdGenUtils.generateIdWithDateTime("AGR_NO", "CPI", 6);
            BeanUtils.copyProperties(cardProtDO, cardProtJrnDO);
            cardProtDO.setUserId(LemonUtils.getUserId());
            cardProtDO.setCorpBusTyp(corpBusTyp);
            cardProtDO.setCorpBusSubTyp(corpBusSubTyp);
            cardProtDO.setAgrEffFlg(CpiConstants.AGR_EFFECT_YES);//签约成功
            cardProtDO.setSignDt(DateTimeUtils.getCurrentLocalDate());
            cardProtDO.setSignTm(DateTimeUtils.getCurrentLocalTime());
            cardProtDO.setAgrNo(agrNo);
            String crdNo = encryptUtils.encrypt(cardProtJrnDO.getCrdNoEnc(), CpiConstants.DECRYPT);
            cardProtDO.setCrdNoLast(crdNo.substring(crdNo.length() - 4, crdNo.length()));
            cardProtDao.insert(cardProtDO);

            //实名认证，不判断返回，不处理问题
            if (CpiConstants.AUTHENTICATE_NO.equals(cardProtJrnDO.getAuthFlag())) {
                GenericDTO<UserRealNameDTO> reqDTO = new GenericDTO<>();
                UserRealNameDTO userRealNameDTO = new UserRealNameDTO();
                userRealNameDTO.setUserId(LemonUtils.getUserId());
                userRealNameDTO.setIdType(cardProtJrnDO.getIdTyp());
                userRealNameDTO.setIdNo(encryptUtils.encrypt(cardProtJrnDO.getIdNoEnc(), CpiConstants.DECRYPT));
                userRealNameDTO.setUsrNm(cardProtJrnDO.getCrdUsrNm());
                reqDTO.setBody(userRealNameDTO);
                userBasicInfClient.upgradeRealName(reqDTO);
            }

            BeanUtils.copyProperties(cardBindRspDTO, cardBindReqDTO);
            cardBindRspDTO.setArgNo(agrNo);
            return GenericRspDTO.newInstance(msgCd.getMsgCd(), cardBindRspDTO);
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), cardBindRspDTO);
        } catch (Exception e) {
            logger.error("FastpayServiceImpl.bindCard exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), cardBindRspDTO);
        }
    }

    /**
     * 用户银行卡解绑
     */
    @Override
    public GenericRspDTO<NoBody> unBindCard(GenericDTO<CardUnBindReqDTO> genericDTO) {
        //请求对象
        CardUnBindReqDTO cardUnBindReqDTO = genericDTO.getBody();
        String chkUserId = cardUnBindReqDTO.getChkUserId();
        if(StringUtils.isEmpty(chkUserId)) {
            chkUserId = CpiConstants.CHK_USER_ID_YES;
        }
        try {
            //step1:查询用户协议号
            CardProtDO cardProtDO = cardProtDao.get(cardUnBindReqDTO.getAgrNo());
            String userId = cardProtDO.getUserId();
            if (StringUtils.equals(chkUserId, CpiConstants.CHK_USER_ID_YES)) {
                if (JudgeUtils.isEmpty(LemonUtils.getUserId())) {
                    return GenericRspDTO.newInstance(CpiMsgCd.USER_IS_NULL.getMsgCd());
                }
                if (!cardProtDO.getUserId().trim().equals(LemonUtils.getUserId())) {
                    return GenericRspDTO.newInstance(CpiMsgCd.AGR_IS_NOT_FUND.getMsgCd());
                }
            }

            //判断生效标志是否为 Y：生效
            if (!cardProtDO.getAgrEffFlg().equals(CpiConstants.AGR_EFFECT_YES)) {
                return GenericRspDTO.newInstance(CpiMsgCd.ARG_LOSE_EFFECTIVENESS.getMsgCd());
            }

            //step2:插入流水表
            CardProtJrnDO unbindCardJrnDo = new CardProtJrnDO();
            BeanUtils.copyProperties(unbindCardJrnDo, cardProtDO);
            String jrnNo = IdGenUtils.generateIdWithDateTime("JRN_NO", "CPI", 6);
            unbindCardJrnDo.setUserId(userId);
            unbindCardJrnDo.setBndFlg(CpiConstants.SIGN_CANCEL);//解绑
            unbindCardJrnDo.setCorpBusTyp(CorpBusTyp.SIGN.getType());
            unbindCardJrnDo.setCorpBusSubTyp(CorpBusSubTyp.FAST_UNSIGN.getType());
            unbindCardJrnDo.setTxDt(DateTimeUtils.getCurrentLocalDate());
            unbindCardJrnDo.setTxTm(DateTimeUtils.getCurrentLocalTime());
            unbindCardJrnDo.setJrnNo(jrnNo);
            unbindCardJrnDo.setCreateTime(null);

            //这里用事务的insert，这样在与银行交互之前提交
            fastpayTransaction.insertCardProtJrnDO(unbindCardJrnDo);

            //step3:用户状态检查
            RiskCheckUserStatusReqDTO userStatusReqDTO = new RiskCheckUserStatusReqDTO();
            //用户状态检查
            userStatusReqDTO.setId(userId);
            userStatusReqDTO.setIdTyp(Constants.ID_TYP_USER_NO);
            //交易类型
            userStatusReqDTO.setTxTyp(Constants.TX_TYP_RECHARGE);
            GenericRspDTO<NoBody> riskRsp = riskCheckClient.checkUserStatus(userStatusReqDTO);
            if (JudgeUtils.isNotSuccess(riskRsp.getMsgCd())) {
                throw new LemonException(riskRsp.getMsgCd());
            }

            //step4:解约
            CardProtDO cardProtUpdate = new CardProtDO();
            cardProtUpdate.setAgrNo(cardUnBindReqDTO.getAgrNo());
            cardProtUpdate.setUnsignDt(DateTimeUtils.getCurrentLocalDate());
            cardProtUpdate.setUnsignTm(DateTimeUtils.getCurrentLocalTime());
            cardProtUpdate.setAgrEffFlg(CpiConstants.AGR_EFFECT_NO); //失效
            CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
            if ("0".equals(cardProtDO.getAgrDirect())) {
                //调用银行同步解约
                msgCd = this.bankUnbindCard(cardProtDO, unbindCardJrnDo);
            }
            if(JudgeUtils.isSuccess(msgCd.getMsgCd())) {
                cardProtDao.update(cardProtUpdate);
            }
            return GenericRspDTO.newInstance(msgCd.getMsgCd());
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd());
        } catch (Exception e) {
            logger.error("FastpayServiceImpl.unBindCard exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
    }

    /**
     * 快捷支付
     */
    @Override
    public GenericRspDTO<FastpayRspDTO> fastpay(GenericDTO<FastpayReqDTO> genericDTO) {
        //请求对象
        FastpayReqDTO fastpayReqDTO = genericDTO.getBody();

        //返回对象
        FastpayRspDTO fastpayRspDTO = new FastpayRspDTO();

        try {
            //Step1:检查协议和类型
            String argNo = fastpayReqDTO.getArgNo();
            CardProtDO cardProtDO = cardProtDao.get(argNo);
            if (!"Y".equals(cardProtDO.getAgrEffFlg())) {
                return GenericRspDTO.newInstance(CpiMsgCd.ARG_LOSE_EFFECTIVENESS.getMsgCd(), new FastpayRspDTO());
            }

            if (!CorpBusTyp.FASTPAY.equals(fastpayReqDTO.getCorpBusTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd(), new FastpayRspDTO());
            }
            if (!CorpBusSubTyp.FASTPAY.equals(fastpayReqDTO.getCorpBusSubTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), new FastpayRspDTO());
            }

            //Step2:验证短信验证码
            if (CpiConstants.SMS_CHECK_YES.equals(fastpayReqDTO.getSmsFlag())) {
                //根据短信流水号和传入的校验码进行校验
            }
            String funOrdNo = IdGenUtils.generateIdWithDateTime("FUD_ORD_NO", "CPI", 6);

            //Step3:风控，黑名单检查
            JrnReqDTO jrnReqDTO = new JrnReqDTO();
            //付方ID
            jrnReqDTO.setPayUserId(LemonUtils.getUserId());
            //付方类型
            jrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
            //交易类型
            jrnReqDTO.setTxTyp(Constants.TX_TYP_RECHARGE);
            //交易状态
            jrnReqDTO.setTxSts("0");
            //交易渠道
            jrnReqDTO.setTxCnl("APP");
            //交易金额
            jrnReqDTO.setTxAmt(fastpayReqDTO.getOrdAmt());
            //交易币种
            jrnReqDTO.setCcy(fastpayReqDTO.getOrdCcy());
            //交易日期
            jrnReqDTO.setTxDate(DateTimeUtils.getCurrentLocalDate());
            //交易时间
            jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
            //原交易流水号
            jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
            //原交易订单号
            jrnReqDTO.setTxOrdNo(funOrdNo);
            GenericRspDTO<NoBody> riskRsp = riskCheckClient.riskControl(jrnReqDTO);
            if (JudgeUtils.isNotSuccess(riskRsp.getMsgCd())) {
                throw new LemonException(riskRsp.getMsgCd());
            }

            //Step4:登记充值订单表和快捷表
            ShortcutOrderDO shortcutOrderDO = fastpayTransaction.createFundOrder(fastpayReqDTO, cardProtDO, funOrdNo);

            //Step5:调用银行处理
            CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
            msgCd = this.bankFastpay(shortcutOrderDO,cardProtDO.getSignAgrno());
            //返回值

            BeanUtils.copyProperties(fastpayRspDTO, fastpayReqDTO);
            fastpayRspDTO.setOrdNo(shortcutOrderDO.getFndOrdNo());
            return GenericRspDTO.newInstance(msgCd.getMsgCd(), fastpayRspDTO);
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), fastpayRspDTO);
        } catch (Exception e) {
            logger.error("FastpayServiceImpl.fastpay exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), fastpayRspDTO);
        }
    }

    /**
     * 快捷支付订单查询
     */
    @Override
    public GenericRspDTO<OrderResultRspDTO> queryOrder(String ordNo) {
        //返回值
        OrderResultRspDTO orderResultDTO = new OrderResultRspDTO();

        try {
            FundOrderDO fundOrderDO = fundOrderDao.get(ordNo);
            if (JudgeUtils.isNull(fundOrderDO)) {
                return GenericRspDTO.newInstance(CpiMsgCd.FUND_ORDER_IS_NOT_FOUND.getMsgCd(), orderResultDTO);
            }
            BeanUtils.copyProperties(orderResultDTO, fundOrderDO);
            orderResultDTO.setOrdNo(ordNo);
            return GenericRspDTO.newSuccessInstance(orderResultDTO);
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), orderResultDTO);
        } catch (Exception e) {
            logger.error("FastpayServiceImpl.queryOrder exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), orderResultDTO);
        }
    }

    @Override
    public GenericRspDTO<FastpayAcledaRspDTO> fastpayAcleda(GenericDTO<FastpayAcledaReqDTO> genericDTO){
        //请求对象
        FastpayAcledaReqDTO fastpayAcledaReqDTO = genericDTO.getBody();

        //返回对象
        FastpayAcledaRspDTO fastpayAcledaRspDTO = new FastpayAcledaRspDTO();

        try {
            //Step1:检查类型
            if (!CorpBusTyp.FASTPAY.equals(fastpayAcledaReqDTO.getCorpBusTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_TYP_IS_ERROR.getMsgCd(), new FastpayAcledaRspDTO());
            }
            if (!CorpBusSubTyp.FASTPAY.equals(fastpayAcledaReqDTO.getCorpBusSubTyp())) {
                return GenericRspDTO.newInstance(CpiMsgCd.CORP_BUS_SUB_TYP_IS_ERROR.getMsgCd(), new FastpayAcledaRspDTO());
            }

            String funOrdNo = IdGenUtils.generateIdWithDateTime("FUD_ORD_NO", "CPI", 6);

            //Step3:风控，黑名单检查
            JrnReqDTO jrnReqDTO = new JrnReqDTO();
            //付方ID
            jrnReqDTO.setPayUserId(LemonUtils.getUserId());
            //付方类型
            jrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
            //交易类型
            jrnReqDTO.setTxTyp(Constants.TX_TYP_RECHARGE);
            //交易状态
            jrnReqDTO.setTxSts("0");
            //交易渠道
            jrnReqDTO.setTxCnl("APP");
            //交易金额
            jrnReqDTO.setTxAmt(fastpayAcledaReqDTO.getOrdAmt());
            //交易币种
            jrnReqDTO.setCcy(fastpayAcledaReqDTO.getOrdCcy());
            //交易日期
            jrnReqDTO.setTxDate(DateTimeUtils.getCurrentLocalDate());
            //交易时间
            jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
            //原交易流水号
            jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
            //原交易订单号
            jrnReqDTO.setTxOrdNo(funOrdNo);
            GenericRspDTO<NoBody> riskRsp = riskCheckClient.riskControl(jrnReqDTO);
            if (JudgeUtils.isNotSuccess(riskRsp.getMsgCd())) {
                throw new LemonException(riskRsp.getMsgCd());
            }

            //Step4:登记充值订单表和快捷表
            ShortcutOrderDO shortcutOrderDO = fastpayTransaction.createAcledaFundOrder(fastpayAcledaReqDTO,funOrdNo);

            Map<String,String> resultMap = new HashMap<String,String>();
            //设置资金流方向
            resultMap.put("direction",String.valueOf(fastpayAcledaReqDTO.getDirection()));
            //Step5:调用银行处理
            CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
            //msgCd = this.bankFastpay(shortcutOrderDO,null,resultMap);
            msgCd = this.openAcledaPay(shortcutOrderDO,resultMap);
            //返回值

            BeanUtils.copyProperties(fastpayAcledaRspDTO, fastpayAcledaReqDTO);
            fastpayAcledaRspDTO.setOrdNo(shortcutOrderDO.getFndOrdNo());
            //设置sessionId , paymentTokenId
            fastpayAcledaRspDTO.setSessionId(resultMap.get("sessionId"));
            fastpayAcledaRspDTO.setPaymentTokenId(resultMap.get("paymentTokenId"));
            return GenericRspDTO.newInstance(msgCd.getMsgCd(), fastpayAcledaRspDTO);
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), fastpayAcledaRspDTO);
        } catch (Exception e) {
            logger.error("FastpayServiceImpl.fastpayAcleda exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), fastpayAcledaRspDTO);
        }
    }

    @Override
    public GenericRspDTO acledaPayNotify(GenericDTO<FastpayAcledaNotifyReqDTO> genericDTO){
        FastpayAcledaNotifyReqDTO fastpayAcledaNotifyReqDTO = genericDTO.getBody();
        //获取支付ID和交易ID
        String sessionId = fastpayAcledaNotifyReqDTO.getSessionId();
        String paymentTokenId = fastpayAcledaNotifyReqDTO.getPaymentTokenId();
        //查询ACLEDA交易订单状态
        BaseBnkRspBO baseBnkRspBO = fastpayACLEDAApi.getTransactionStatus(sessionId,paymentTokenId);
        FundOrderDO fundOrderDO = fundOrderDao.get(fastpayAcledaNotifyReqDTO.getReqOrdNo());

        //根据订单号查询快捷记录表
        ShortcutOrderDO shortcutOrderDO = shortcutOrderDao.selectShortcutOrderByChkKey(fundOrderDO.getReqOrdNo());

        //交易状态成功则更新充值和快捷订单状态
        ShortcutOrderDO updateShortcut = new ShortcutOrderDO();
        updateShortcut.setSubOrdNo(shortcutOrderDO.getSubOrdNo());
        updateShortcut.setAcDt(LemonUtils.getAccDate());

        //更新订单表状态
        FundOrderDO updateFundOrder = new FundOrderDO();
        updateFundOrder.setFudOrdNo(shortcutOrderDO.getFndOrdNo());
        updateFundOrder.setAcDt(LemonUtils.getAccDate());
        updateShortcut.setAcDt(LemonUtils.getAccDate());
        updateShortcut.setAgrNo(sessionId);
        updateShortcut.setOrdSts(CpiConstants.ORD_FAIL);
        updateFundOrder.setOrdSts(CpiConstants.ORD_FAIL);
        if (JudgeUtils.isSuccess(baseBnkRspBO.getOrgRspCd())) {
            updateShortcut.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateFundOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateFundOrder.setOrdSuccDt(DateTimeUtils.getCurrentLocalDate());
            updateFundOrder.setOrdSuccTm(DateTimeUtils.getCurrentLocalTime());
        }
        //更新订单表和快捷表
        BeanUtils.copyProperties(updateShortcut, baseBnkRspBO);
        updateShortcut.setOrgOrdNo(baseBnkRspBO.getOutOrdNo());
        shortcutOrderDao.update(updateShortcut);
        fundOrderDao.update(updateFundOrder);

        //异步通知收银台
        if(JudgeUtils.isSuccess(baseBnkRspBO.getOrgRspCd())) {
            fundOrderDO.setOrdSts(CpiConstants.ORD_SUCCESS);
        } else {
            fundOrderDO.setOrdSts(CpiConstants.ORD_FAIL);
        }
        fundNotifyProduce.fundOrderNotify(fundOrderDO,"ACLEDA银行订单处理成功,通知收银台成功");
        logger.info("ACLEDA下单成功异步通知处理完成");
        return GenericRspDTO.newInstance(baseBnkRspBO.getOrgRspCd());
    }

    private CpiMsgCd openAcledaPay(ShortcutOrderDO shortcutOrderDO,Map<String,String> resultMap){
        CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
        BaseBnkRspBO baseBnkRspBO = null;
        switch (shortcutOrderDO.getRutCorpOrg()) {
            case CpiConstants.ACLEDA:
                baseBnkRspBO = fastpayACLEDAApi.fastpay(shortcutOrderDO,resultMap);
                if (JudgeUtils.isNull(baseBnkRspBO)) {
                    //银行错
                    return CpiMsgCd.BANK_COMMUNICATION_EXCEPTION;
                }else{
                    msgCd = CpiMsgCd.valueOf(baseBnkRspBO.getOrgRspCd());
                }
                break;
            default:
                //不支持银行
                return CpiMsgCd.ROUTE_NOT_FIND;
        }
        return msgCd;
    }


    /**
     * 银行预签约内部处理
     */
    private CpiMsgCd bankPreBindCard(CardProtJrnDO cardProtJrnDO) {
        BaseBnkRspBO baseBnkRspBO = null;
        CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
        switch (cardProtJrnDO.getRutCorpOrg()) {
            case CpiConstants.ICBC:
                baseBnkRspBO = fastpayICBCApi.preBindCard(cardProtJrnDO);
                if (JudgeUtils.isNull(baseBnkRspBO)) {
                    //银行错
                    return CpiMsgCd.BANK_COMMUNICATION_EXCEPTION;
                } else {
                    msgCd = icbcRetCdConvert.Convert(baseBnkRspBO.getOrgRspCd());
                }
                break;
            default:
                //不支持银行
                return CpiMsgCd.ROUTE_NOT_FIND;
        }


        //更新流水表
        CardProtJrnDO updateCardProtJrn = new CardProtJrnDO();
        updateCardProtJrn.setJrnNo(cardProtJrnDO.getJrnNo());
        updateCardProtJrn.setSignAgrno(baseBnkRspBO.getOutOrdNo());
        updateCardProtJrn.setOrgRspCd(baseBnkRspBO.getOrgRspCd());
        updateCardProtJrn.setOrgRspMsg(baseBnkRspBO.getOrgRspMsg());
        updateCardProtJrn.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
        cardProtJrnDao.update(updateCardProtJrn);
        return msgCd;
    }

    /**
     * 银行签约内部处理
     */
    private CpiMsgCd bankBindCard(CardProtJrnDO cardProtJrnDO, String chkNo) {
        BaseBnkRspBO baseBnkRspBO = null;
        CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
        switch (cardProtJrnDO.getRutCorpOrg()) {
            case CpiConstants.ICBC:
                baseBnkRspBO = fastpayICBCApi.bindCard(cardProtJrnDO, chkNo);
                if (JudgeUtils.isNull(baseBnkRspBO)) {
                    //银行错
                    return CpiMsgCd.BANK_COMMUNICATION_EXCEPTION;
                } else {
                    msgCd = icbcRetCdConvert.Convert(baseBnkRspBO.getOrgRspCd());
                }
                break;
            default:
                //不支持银行
                return CpiMsgCd.ROUTE_NOT_FIND;
        }
        cardProtJrnDO.setSignAgrno(baseBnkRspBO.getOutOrdNo());
        //更新流水表
        CardProtJrnDO updateCardProtJrn = new CardProtJrnDO();
        updateCardProtJrn.setJrnNo(cardProtJrnDO.getJrnNo());
        updateCardProtJrn.setSignAgrno(baseBnkRspBO.getOutOrdNo());
        updateCardProtJrn.setOrgRspCd(baseBnkRspBO.getOrgRspCd());
        updateCardProtJrn.setOrgRspMsg(baseBnkRspBO.getOrgRspMsg());
        updateCardProtJrn.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
        cardProtJrnDao.update(updateCardProtJrn);
        return msgCd;
    }

    /**
     * 银行解约内部处理
     */
    private CpiMsgCd bankUnbindCard(CardProtDO cardProtDO, CardProtJrnDO cardProtJrnDO) {
        BaseBnkRspBO baseBnkRspBO = null;
        CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
        switch (cardProtJrnDO.getRutCorpOrg()) {
            case CpiConstants.ICBC:
                baseBnkRspBO = fastpayICBCApi.unBindCard(cardProtDO);
                if (JudgeUtils.isNull(baseBnkRspBO)) {
                    //银行错
                    return CpiMsgCd.BANK_COMMUNICATION_EXCEPTION;
                } else {
                    msgCd = icbcRetCdConvert.Convert(baseBnkRspBO.getOrgRspCd());
                }
                break;
            default:
                //不支持银行
                return CpiMsgCd.ROUTE_NOT_FIND;
        }
        //更新流水表
        CardProtJrnDO updateCardProtJrn = new CardProtJrnDO();
        updateCardProtJrn.setJrnNo(cardProtJrnDO.getJrnNo());
        updateCardProtJrn.setSignAgrno(baseBnkRspBO.getOutOrdNo());
        updateCardProtJrn.setOrgRspCd(baseBnkRspBO.getOrgRspCd());
        updateCardProtJrn.setOrgRspMsg(baseBnkRspBO.getOrgRspMsg());
        updateCardProtJrn.setOrgJrnNo(baseBnkRspBO.getOrgJrnNo());
        cardProtJrnDao.update(updateCardProtJrn);
        return msgCd;
    }

    /**
     * 银行快捷支付内部处理
     */
    private CpiMsgCd bankFastpay(ShortcutOrderDO shortcutOrderDO,String signAgrno) {
        CpiMsgCd msgCd = CpiMsgCd.SUCCESS;
        BaseBnkRspBO baseBnkRspBO = null;
        switch (shortcutOrderDO.getRutCorpOrg()) {
            case CpiConstants.ICBC:
                baseBnkRspBO = fastpayICBCApi.fastpay(shortcutOrderDO,signAgrno);
                if (JudgeUtils.isNull(baseBnkRspBO)) {
                    //银行错
                    return CpiMsgCd.BANK_COMMUNICATION_EXCEPTION;
                }else {
                    msgCd = icbcRetCdConvert.Convert(baseBnkRspBO.getOrgRspCd());
                }
                break;
            default:
                //不支持银行
                return CpiMsgCd.ROUTE_NOT_FIND;
        }

        //Step6:更新快捷表和订单表的状态
        ShortcutOrderDO updateShortcut = new ShortcutOrderDO();
        updateShortcut.setSubOrdNo(shortcutOrderDO.getSubOrdNo());
        updateShortcut.setAcDt(LemonUtils.getAccDate());

        //更新订单表状态
        FundOrderDO updateFundOrder = new FundOrderDO();
        updateFundOrder.setFudOrdNo(shortcutOrderDO.getFndOrdNo());
        updateFundOrder.setAcDt(LemonUtils.getAccDate());
        updateShortcut.setAcDt(LemonUtils.getAccDate());
        updateShortcut.setOrdSts(CpiConstants.ORD_FAIL);
        updateFundOrder.setOrdSts(CpiConstants.ORD_FAIL);
        if (JudgeUtils.isSuccess(msgCd.getMsgCd())) {
            updateShortcut.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateFundOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
            updateFundOrder.setOrdSuccDt(DateTimeUtils.getCurrentLocalDate());
            updateFundOrder.setOrdSuccTm(DateTimeUtils.getCurrentLocalTime());
        }
        //更新订单表和快捷表
        BeanUtils.copyProperties(updateShortcut, baseBnkRspBO);
        updateShortcut.setOrgOrdNo(baseBnkRspBO.getOutOrdNo());
        shortcutOrderDao.update(updateShortcut);
        fundOrderDao.update(updateFundOrder);
        return msgCd;
    }

}
