/*
 * @ClassName ISettlementControlDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-17 18:33:02
 */
package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.SettlementControlDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ISettlementControlDao extends BaseDao<SettlementControlDO> {

    List<SettlementControlDO> getUnSettlementDate(@Param("settleDateStr")String settleDateStr);

    SettlementControlDO getBySettleDate(@Param("settleDateStr")String settleDateStr);
}