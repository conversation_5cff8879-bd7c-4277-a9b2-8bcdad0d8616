package com.hisun.lemon.cpi.thread;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.cpi.service.IRefundService;
import com.hisun.lemon.framework.lock.DistributedLocker;
import org.springframework.stereotype.Component;

/**
 * 定时任务，退款订单调起银行查询接口
 */
@Component
public class EbankRefundQueryThread implements Runnable {

    private IRefundService refundService;

    private FundPollParamDO fundPollParamDO;

    private DistributedLocker distributedLocker;

    public EbankRefundQueryThread(){
        super();
    }

    public EbankRefundQueryThread(IRefundService refundService, DistributedLocker distributedLocker, FundPollParamDO fundPollParamDO){
        this.refundService = refundService;
        this.fundPollParamDO = fundPollParamDO;
        this.distributedLocker = distributedLocker;
    }

    @Override
    public void run() {
        try {
            //锁名
            String lockName = "QRY_" + fundPollParamDO.getRutCorg() + "_" + fundPollParamDO.getCorpBusSubTyp();
            //释放锁的时间（异常情况下最长的释放锁的时间，单位秒）
            int leaseTime = 5*60;
            //获取应用锁的时间，时间设置必须短，否则影响后面的线程获取锁（单位秒）
            int waitTime = 10;
            distributedLocker.lock(lockName, leaseTime, waitTime, () -> {
                refundService.refundAdditionalOrder(fundPollParamDO);
                return null;
            });
        } catch (Exception e) {
            throw new LemonException();
        }
    }
}
