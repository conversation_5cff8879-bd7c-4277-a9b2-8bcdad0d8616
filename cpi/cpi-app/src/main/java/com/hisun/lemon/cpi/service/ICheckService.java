package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.CheckDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

public interface ICheckService {

    /**
     * 补单
     * @param genericDTO
     */
    GenericRspDTO<NoBody> additionalOrder(GenericDTO<CheckDTO> genericDTO);

    /**
     * 撤单
     * @param genericDTO
     */
    GenericRspDTO<NoBody> cancelOrder(GenericDTO<CheckDTO> genericDTO);

    /**
     * 差错取消
     * @param genericDTO
     */
    GenericRspDTO<NoBody> cancelError(GenericDTO<CheckDTO> genericDTO);

    /**
     * 对平处理
     * @param genericDTO
     */
    GenericRspDTO<NoBody> matchHandle(GenericDTO<CheckDTO> genericDTO);
}
