package com.hisun.lemon.cpi.controller;

import javax.annotation.Resource;

import com.hisun.lemon.cpi.alipay.ebankpay.AlipaySecurity;
import com.hisun.lemon.cpi.alipay.ebankpay.req.AlipayOrderNotifyReq;
import com.hisun.lemon.cpi.alipay.ebankpay.util.AlipaySignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.service.IEbankNotifyService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;

import io.swagger.annotations.Api;

/**
 * 支付宝预下单接口返回结果回调地址
 */
@RestController
@RequestMapping("/cpi/alipay")
@Api(tags="AlipayNotifyController", description="支付宝预下单结果通知服务")
public class AlipayNotifyController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(AlipayNotifyController.class);

    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private IEbankNotifyService ebankNotifyService;

    /**
     * 支付宝预下单结果回调方法
     */
    @PostMapping("/placeOrderNotify")
    public void placeOrderNotify(AlipayOrderNotifyReq alipayOrderNotifyReq) {
        logger.info("==================支付宝下单结果回调通知==================");
        if(JudgeUtils.isNotNull(alipayOrderNotifyReq)) {
            String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, alipayOrderNotifyReq, true);
            logger.info("AlipayNotifyController.placeOrderNotify() 支付宝预下单回调结果：" + rspData);
            //验签
            if (AlipaySignature.isVerify(alipayOrderNotifyReq, alipayOrderNotifyReq.getSign())) {
                //验签通过
                logger.info("AlipayNotifyController.placeOrderNotify() 支付宝预下单结果回调通知，验签通过");
                //扫码订单处理
                ebankNotifyService.alipayFundNotify(alipayOrderNotifyReq);
            } else {
                logger.info("AlipayNotifyController.placeOrderNotify() 支付宝预下单结果回调通知，验签不通过");
            }
        }
    }

}
