package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.dao.*;
import com.hisun.lemon.cpi.dto.FastpayAcledaReqDTO;
import com.hisun.lemon.cpi.dto.FastpayReqDTO;
import com.hisun.lemon.cpi.entity.CardProtDO;
import com.hisun.lemon.cpi.entity.CardProtJrnDO;
import com.hisun.lemon.cpi.entity.FundOrderDO;
import com.hisun.lemon.cpi.entity.ShortcutOrderDO;
import com.hisun.lemon.cpi.service.IFastpayTransaction;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Created by Rui on 2017/7/10.
 */
@Service
public class FastpayTransactionImpl extends BaseService implements IFastpayTransaction {
    @Resource
    IFundOrderDao fundOrderDao;
    @Resource
    IShortcutOrderDao shortcutOrderDao;
    @Resource
    ICardProtJrnDao cardProtJrnDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public void insertCardProtJrnDO(CardProtJrnDO cardProtJrnDO) {
        cardProtJrnDao.insert(cardProtJrnDO);
    }

    /**
     * 登记资金流出订单表和快捷订单表
     * @param fastpayDTO
     * @param cardProtDO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public ShortcutOrderDO createFundOrder(FastpayReqDTO fastpayDTO, CardProtDO cardProtDO, String funOrdNo){
        //创建充值订单
        FundOrderDO fundOrderDO = new FundOrderDO();
        BeanUtils.copyProperties(fundOrderDO,fastpayDTO);
        BeanUtils.copyProperties(fundOrderDO,cardProtDO);
        fundOrderDO.setCorpBusTyp(fastpayDTO.getCorpBusTyp().getType());
        fundOrderDO.setCorpBusSubTyp(fastpayDTO.getCorpBusSubTyp().getType());
        fundOrderDO.setUserId(LemonUtils.getUserId());
        fundOrderDO.setFudOrdNo(funOrdNo);
        fundOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY); //等待充值
        fundOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        fundOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
        fundOrderDO.setNtfSts(CpiConstants.NOTIFY_WAIT); //等待通知
        fundOrderDao.insert(fundOrderDO);

        //创建快捷充值订单
        ShortcutOrderDO shortcutOrderDO = new ShortcutOrderDO();
        String subOrdNo = IdGenUtils.generateIdWithDateTime("SUB_ORD_NO","CPI",6);
        BeanUtils.copyProperties(shortcutOrderDO,fastpayDTO);
        BeanUtils.copyProperties(shortcutOrderDO,cardProtDO);
        shortcutOrderDO.setSubOrdNo(subOrdNo);
        shortcutOrderDO.setCorpBusTyp(fastpayDTO.getCorpBusTyp().getType());
        shortcutOrderDO.setCorpBusSubTyp(fastpayDTO.getCorpBusSubTyp().getType());
        shortcutOrderDO.setAgrFlg(CpiConstants.AGR_EFFECT_YES); //协议生效
        shortcutOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY); //等待充值
        shortcutOrderDO.setFndOrdNo(funOrdNo);
        shortcutOrderDO.setAgrNo(fastpayDTO.getArgNo());
        shortcutOrderDO.setCrdCorpOrg(cardProtDO.getCrdCorpOrg());
        shortcutOrderDO.setRutCorpOrg(cardProtDO.getRutCorpOrg());
        shortcutOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        shortcutOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
        if(CpiConstants.SMS_CHECK_YES.equals(fastpayDTO.getSmsFlag())) {
            shortcutOrderDO.setSmsChkFlg(CpiConstants.SMS_CHECK_YES);
        }else {
            shortcutOrderDO.setSmsChkFlg(CpiConstants.SMS_CHECK_NO);
        }
        shortcutOrderDao.insert(shortcutOrderDO);
        return shortcutOrderDO;

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public ShortcutOrderDO createAcledaFundOrder(FastpayAcledaReqDTO fastpayAcledaReqDTO,String funOrdNo){
        //创建充值订单
        FundOrderDO fundOrderDO = new FundOrderDO();
        BeanUtils.copyProperties(fundOrderDO,fastpayAcledaReqDTO);
        fundOrderDO.setCorpBusTyp(fastpayAcledaReqDTO.getCorpBusTyp().getType());
        fundOrderDO.setCorpBusSubTyp(fastpayAcledaReqDTO.getCorpBusSubTyp().getType());
        fundOrderDO.setUserId(LemonUtils.getUserId());
        fundOrderDO.setFudOrdNo(funOrdNo);
        fundOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY); //等待充值
        fundOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        fundOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
        fundOrderDO.setNtfSts(CpiConstants.NOTIFY_WAIT); //等待通知
        fundOrderDO.setReqOrdNo(fastpayAcledaReqDTO.getReqOrdNo());
        fundOrderDao.insert(fundOrderDO);

        //创建快捷充值订单
        ShortcutOrderDO shortcutOrderDO = new ShortcutOrderDO();
        String subOrdNo = IdGenUtils.generateIdWithDateTime("SUB_ORD_NO","CPI",6);
        BeanUtils.copyProperties(shortcutOrderDO,fastpayAcledaReqDTO);
        shortcutOrderDO.setSubOrdNo(subOrdNo);
        shortcutOrderDO.setCorpBusTyp(fastpayAcledaReqDTO.getCorpBusTyp().getType());
        shortcutOrderDO.setCorpBusSubTyp(fastpayAcledaReqDTO.getCorpBusSubTyp().getType());
        shortcutOrderDO.setAgrFlg(CpiConstants.AGR_EFFECT_YES); //协议生效
        shortcutOrderDO.setOrdSts(CpiConstants.ORD_WATING_PAY); //等待充值
        shortcutOrderDO.setFndOrdNo(funOrdNo);
        //对账值为收银台订单号
        shortcutOrderDO.setChkKey(fastpayAcledaReqDTO.getReqOrdNo());
        shortcutOrderDO.setAgrNo("");
        shortcutOrderDO.setRmk(fastpayAcledaReqDTO.getGoodsDesc());
        shortcutOrderDO.setOrdDt(DateTimeUtils.getCurrentLocalDate());
        shortcutOrderDO.setOrdTm(DateTimeUtils.getCurrentLocalTime());
        shortcutOrderDO.setSmsChkFlg(CpiConstants.SMS_CHECK_NO);
        shortcutOrderDao.insert(shortcutOrderDO);
        return shortcutOrderDO;

    }

}
