package com.hisun.lemon.cpi.bnkapi.bestpay;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.cpi.bnkapi.bestpay.ebankpay.BestPayScanPayApi;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.bestpay.IAccBestpayEbankpayRefundDao;
import com.hisun.lemon.cpi.entity.AccControlDO;
import com.hisun.lemon.cpi.entity.bestpay.AccBestpayEbankpayRefundDO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 翼支付扫码退款业务对账操作 API
 */
@Component
public class CheckAccEbankpayRefundApi {

    private static final Logger logger = LoggerFactory.getLogger(CheckAccEbankpayRefundApi.class);

    @Resource
    private IAccBestpayEbankpayRefundDao accBestpayEbankpayRefundDao;

    @Resource
    private BestPayScanPayApi bestPayScanPayApi;

    /**
     * 1.1 翼支付对账：获取退款对账文件
     */
    public String getRefundCheckFile(String chkFilPath, LocalDate chkDt) throws Exception{
        return bestPayScanPayApi.getRefundCheckFile(chkFilPath, chkDt);
    }

    /**
     * 1.2 翼支付扫码退款对账：解析对账文件，对账明细批量入库
     */
    public void batchInsertCheckDO(List<Object> checkDOList, AccControlDO accControlDO) {
        logger.debug("==================工行快捷退款对账，解析对账文件==================");

        //step1：根据对账批次号查询一条记录，判断该批次是否已插入对账明细数据
        String chkBatNo = accControlDO.getChkBatNo();
        int totalNum = accBestpayEbankpayRefundDao.getAccRefundDetailCount(chkBatNo);
        if (totalNum > 0) {
            logger.error("该批次已录入银行对账明细，请重新确认，对账批次号: " + chkBatNo);
            throw new LemonException(CpiMsgCd.IMPORT_CHECK_FILE_REPEATED.getMsgCd());
        }

        //step2：批次插入银行对账明细
        AccBestpayEbankpayRefundDO accRefundDO = null;
        List<AccBestpayEbankpayRefundDO> refundDOList = new ArrayList<>();
        Integer filTotCnt = 0;
        BigDecimal filTotAmt = BigDecimal.ZERO;
        BigDecimal amtFormat = BigDecimal.ZERO;
        String chkId = null;

        if (CollectionUtils.isNotEmpty(checkDOList)) {
            //循环获取文件中，交易类型为 3-退款(快捷退款) 的对账明细，并统计对账总金额和总笔数
            for (Object checkAccDO : checkDOList) {
                accRefundDO = (AccBestpayEbankpayRefundDO)checkAccDO;
                chkId = IdGenUtils.generateIdWithDateTime("CPI", 6);
                amtFormat = accRefundDO.getOrdForeignAmt();
                accRefundDO.setChkId(chkId);
                refundDOList.add(accRefundDO);
                filTotAmt = filTotAmt.add(amtFormat);
                filTotCnt++;
            }
            //记录数大于200，分批量进行插入
            if (refundDOList.size() > CpiConstants.MAX_BATCH_NUM) {
                List<AccBestpayEbankpayRefundDO> tmpList = new ArrayList<>();
                for (AccBestpayEbankpayRefundDO checkAccDO : refundDOList) {
                    if (tmpList.size() == CpiConstants.MAX_BATCH_NUM) {
                        accBestpayEbankpayRefundDao.batchInsertCheckDO(tmpList);
                        tmpList.clear();
                    }
                    tmpList.add(checkAccDO);
                }
                if (tmpList.size() > 0) {
                    accBestpayEbankpayRefundDao.batchInsertCheckDO(tmpList);
                }
            } else {
                //记录数小于200，直接批量插入
                if(CollectionUtils.isNotEmpty(refundDOList)) {
                    accBestpayEbankpayRefundDao.batchInsertCheckDO(refundDOList);
                }
            }
        }
        //更新文件总笔数和总金额
        accControlDO.setFilTotAmt(filTotAmt);
        accControlDO.setFilTotCnt(filTotCnt);
    }

    /**
     * 1.3 翼支付扫码退款对账：获取银行对账明细 List
     * @param chkBatNo 对账批次号
     * @param txSts 银行明细交易状态
     * @param beginNum 从表中第几行开始取值
     * @param countNum 最大读取多少行记录
     */
    public List<AccBestpayEbankpayRefundDO> getAccDOList(String chkBatNo, String txSts, Integer beginNum, Integer countNum) {
        return accBestpayEbankpayRefundDao.getAccRefundDetailList(chkBatNo, txSts, beginNum, countNum);
    }

    /**
     * 1.4 翼支付扫码退款对账：更新银行明细的对账状态
     * @param checkKey 银行明细的主键，取机构的交易流水号
     * @param txSts 银行明细的交易状态
     * @param chkSts 银行明细的对账状态
     */
    public void updateAccDOChkSts(String checkKey, String txSts, String chkSts) {
        accBestpayEbankpayRefundDao.updateAccRefundChkSts(checkKey, txSts, chkSts);
    }

}
