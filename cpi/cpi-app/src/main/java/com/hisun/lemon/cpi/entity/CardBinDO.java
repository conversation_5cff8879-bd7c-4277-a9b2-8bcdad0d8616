/*
 * @ClassName CardBinDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class CardBinDO extends BaseDO {
    /**
     * @Fields binId 主键
     */
    private String binId;
    /**
     * @Fields crdBin 卡BIN
     */
    private String crdBin;
    /**
     * @Fields capCorg 资金机构
     */
    private String capCorg;
    /**
     * @Fields crdAcTyp 卡类型，D借记卡，C贷记卡
     */
    private String crdAcTyp;
    /**
     * @Fields crdLth 卡长度
     */
    private Integer crdLth;
    /**
     * @Fields oprId 操作员
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getBinId() {
        return binId;
    }

    public void setBinId(String binId) {
        this.binId = binId;
    }

    public String getCrdBin() {
        return crdBin;
    }

    public void setCrdBin(String crdBin) {
        this.crdBin = crdBin;
    }

    public String getCapCorg() {
        return capCorg;
    }

    public void setCapCorg(String capCorg) {
        this.capCorg = capCorg;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public Integer getCrdLth() {
        return crdLth;
    }

    public void setCrdLth(Integer crdLth) {
        this.crdLth = crdLth;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}