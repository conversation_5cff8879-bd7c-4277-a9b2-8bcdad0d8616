package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.dao.IRefundOrderDao;
import com.hisun.lemon.cpi.dao.IRefundSuborderDao;
import com.hisun.lemon.cpi.entity.RefundOrderDO;
import com.hisun.lemon.cpi.entity.RefundSuborderDO;
import com.hisun.lemon.cpi.service.IRefundTransaction;
import com.hisun.lemon.framework.service.BaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * Created by Rui on 2017/7/14.
 */
@Service
public class RefundTransactionImpl extends BaseService implements IRefundTransaction{

    @Resource
    IRefundOrderDao refundOrderDao;

    @Resource
    IRefundSuborderDao refundSuborderDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public void updateOrdSts(RefundOrderDO refundOrderDO, String subOrdNo, int failCount, BaseBnkRspBO baseBnkRspBO) {
        //更新退款主订单
        RefundOrderDO updateRefundOrder =  new RefundOrderDO();
        updateRefundOrder.setRfdOrdNo(refundOrderDO.getRfdOrdNo());

        //更新退款子订单
        RefundSuborderDO updateRefundSuborder = new RefundSuborderDO();
        updateRefundSuborder.setSubOrdNo(subOrdNo);

        //银行返回结果不为空
        if(JudgeUtils.isNotNull(baseBnkRspBO)) {
            BeanUtils.copyProperties(updateRefundSuborder, baseBnkRspBO);
        }

        //银行返回结果为空，或者返回非成功状态
        if(JudgeUtils.isNull(baseBnkRspBO) || !CpiConstants.BANK_RSP_SUC.equals(baseBnkRspBO.getTxFlg())) {
            //失败的情况，失败次数不超过3次
            if (failCount < 2) {
                updateRefundOrder.setRfdFailCount(failCount + 1);
                updateRefundOrder.setRfdTime(LocalDateTime.now().plusMinutes(15));
            } else {
                //第3次退款失败
                updateRefundSuborder.setOrdSts(CpiConstants.ORD_FAIL);
                updateRefundOrder.setOrdSts(CpiConstants.ORD_FAIL);
            }
        } else {
            //微信特殊处理，退款受理中就是成功
//            if(CpiConstants.WECHAT.equals(refundOrderDO.getRutCorpOrg())) {
//                updateRefundSuborder.setOrdSts(CpiConstants.ORD_SUCCESS);
//                updateRefundOrder.setOrdSts(CpiConstants.ORD_SUCCESS);
//            } else {
                //银行返回处理成功，我方订单统一更新为退款处理中
                updateRefundSuborder.setOrdSts(CpiConstants.ORD_REFUND_BANKING);
                updateRefundOrder.setOrdSts(CpiConstants.ORD_REFUND_BANKING);
//            }
        }
        refundSuborderDao.update(updateRefundSuborder);
        refundOrderDao.update(updateRefundOrder);
    }

    /**
     * 更新退款订单主表状态
     * @param refundSuborderDO
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false, rollbackFor = Exception.class)
    public void updateRefundOrder(RefundSuborderDO refundSuborderDO) {
        refundSuborderDao.update(refundSuborderDO);
    }
}
