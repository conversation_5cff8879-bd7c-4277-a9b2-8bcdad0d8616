package com.hisun.lemon.cpi.dao.wechat;

import com.hisun.lemon.cpi.entity.wechat.AccWeChatEbankpayRefundDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IAccWeChatEbankpayRefundDao extends BaseDao<AccWeChatEbankpayRefundDO> {

    /**
     * 微信扫码支付退款对账：批量插入银行对账明细基本信息
     */
    void batchInsertCheckDO(@Param("checkDOList")List<AccWeChatEbankpayRefundDO> checkDOList);

    /**
     * 微信扫码支付退款对账：根据对账批次号，查询该批次是否已录入银行对账明细
     */
    int getAccRefundDetailCount(String chkBatNo);

    /**
     * 微信扫码支付退款对账：获取银行对账明细列表 List
     * @param chkBatNo 对账批次号
     * @param txSts 银行明细交易状态
     * @param beginNum 从表中第几行开始取值
     * @param countNum 最大读取多少行记录
     */
    List<AccWeChatEbankpayRefundDO> getAccRefundDetailList(@Param("chkBatNo")String chkBatNo,
                                                           @Param("txSts")String txSts,
                                                           @Param("beginNum")Integer beginNum,
                                                           @Param("countNum")Integer countNum);

    /**
     * 微信扫码支付退款对账：更新银行明细的对账状态
     * @param checkKey 银行明细的主键，取机构的交易流水号
     * @param txSts 银行明细的交易状态
     * @param chkSts 银行明细的对账状态
     */
    void updateAccRefundChkSts(@Param("checkKey")String checkKey,
                               @Param("txSts")String txSts,
                               @Param("chkSts")String chkSts);

}