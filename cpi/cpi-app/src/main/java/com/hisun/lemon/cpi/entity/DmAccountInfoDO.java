package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;

/**
 * 数币-账户信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 9:29
 */
public class DmAccountInfoDO extends BaseDO {
    /**
     * 内部自增ID
     */
    private Long id;

    /**
     * 账户唯一ID
     */
    private String accountId;

    /**
     * 所属金库编码
     */
    private String vaultCode;

    /**
     * 所属账户组编码
     */
    private String groupCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型（PS:平台, 商户:MA）
     */
    private String accountType;

    /**
     * 账户备注
     */
    private String remark;

    /**
     * 账户状态（ACTIVE:活跃, FROZEN:冻结, CLOSED:关闭）
     */
    private String status;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
