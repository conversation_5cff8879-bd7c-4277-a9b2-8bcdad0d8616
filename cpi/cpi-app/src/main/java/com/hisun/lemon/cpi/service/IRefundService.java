package com.hisun.lemon.cpi.service;

import com.hisun.lemon.cpi.dto.OrderResultRspDTO;
import com.hisun.lemon.cpi.dto.RefundReqDTO;
import com.hisun.lemon.cpi.dto.RefundRspDTO;
import com.hisun.lemon.cpi.entity.FundPollParamDO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;

/**
 * 退款服务
 */
public interface IRefundService {

    /**
     * 退款申请
     */
    GenericRspDTO<RefundRspDTO> createOrder(GenericDTO<RefundReqDTO> genericDTO);

    /**
     * 退款订单结果查询
     */
    GenericRspDTO<OrderResultRspDTO> queryOrder(String ordNo);

    /**
     * 定时任务，退款结果通知
     */
    void notifyOrderByTime() throws Exception;

    /**
     * 定时任务，向银行发起退款请求
     */
    void refundByTime();

    /**
     * 定时任务，查询网银退款订单银行处理结果
     */
    void refundAdditionalOrder(FundPollParamDO fundPollParamDO);

    /**
     * 未支付订单撤销申请
     */
    GenericRspDTO<RefundRspDTO> closeOrder(GenericDTO<RefundReqDTO> genericDTO);
}
