package com.hisun.lemon.cpi.utils;

import com.hisun.channel.common.core.Signature;
import com.hisun.channel.common.utils.ReflectionUtils;
import com.hisun.channel.parse.*;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 使用dom4j完成String和xml之间的转换
 */
public class XmlParseUtils {
    /**
     * 将string转换为xml，然后解析根节点及其子节点
     * 将子节点的name 和 value 存放在map中返回
     * string格式: <root><key1>value1</key1><key2>value2</key2><root/>
     */
    public static Map<String, String> parseXmlStringToMap(String source) {
        Map<String, String> resultMap = new HashMap<>();
        try {
            Document document = DocumentHelper.parseText(source);
            Element root = document.getRootElement();
            Iterator iterator = root.elementIterator();
            while(iterator.hasNext()) {
                Element item = (Element)iterator.next();
                resultMap.put(item.getName(), item.getStringValue());
            }
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return resultMap;
    }

    /**
     * 将xml字符串解析为指定类的对象
     */
    public static <T> T unmarshallerXmlToObject(String source, Class reqClazz) {
        Unmarshaller<T> unmarshaller = UnmarshallerFactory.getUnmarshaller(reqClazz, false);
        ((AbstractAnalyserAdaptee<?>)unmarshaller).setSignature(new Signature(){
            @Override
            public String sign(String signStr) {
                return "~~~"+signStr+"~~~";
            }
            @Override
            public boolean verify(String verifyStr, String signStr) {
                return true;

            }});
        unmarshaller.analyse();
        Object obj = unmarshaller.unmarshal(source.getBytes());
        return (T)obj;
    }

    /**
     * 将指定类的对象转为xml字符串
     */
    public static String marshallerObjectToXmlStr(Object source, Class sourceClass) {
        Marshaller<Object> marshaller = MarshallerFactory.getMarshaller(sourceClass,false);
        ((AbstractAnalyserAdaptee<?>)marshaller).setSignature(new Signature() {
            @Override
            public String sign(String signStr) {
                return "~~~"+signStr+"~~~";
            }
            @Override
            public boolean verify(String verifyStr, String signStr) {
                return false;
            }});
        marshaller.analyse();
        byte[] bytes = marshaller.marshal(source);
        return new String(bytes);
    }

    /**
     * 将xml报文转换为指定的对象
     * @param root xml报文的根节点
     * @param destClazz 目标类的class
     */
    public static Object parseXmlDataToObject(Element root, Class destClazz) throws Exception {
        //根据传入的Class，初始化类对象
        Object destObject = destClazz.newInstance();

        ////获取DO类所有属性域
        Field[] declaredFields = destClazz.getDeclaredFields();
        for(Field field : declaredFields) {
            field.setAccessible(true);
            Element element = root.element(field.getName());
            //目标对象设置相应属性
            if(null != element) {
                ReflectionUtils.setField(field, destObject, element.getStringValue());
            }
        }
        return destObject;
    }

}
