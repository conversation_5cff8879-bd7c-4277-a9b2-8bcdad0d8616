package com.hisun.lemon.cpi.bnkapi.acleda.fastpay;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.acleda.ebankpay.*;
import com.hisun.lemon.cpi.acleda.ebankpay.req.CloseSessionReq;
import com.hisun.lemon.cpi.acleda.ebankpay.req.DirectionReq;
import com.hisun.lemon.cpi.acleda.ebankpay.req.OpenSessionV2Req;
import com.hisun.lemon.cpi.acleda.ebankpay.req.TransactionQueryReq;
import com.hisun.lemon.cpi.acleda.ebankpay.rsp.CloseSessionRsp;
import com.hisun.lemon.cpi.acleda.ebankpay.rsp.DirectionRsp;
import com.hisun.lemon.cpi.acleda.ebankpay.rsp.OpenSessionV2Rsp;
import com.hisun.lemon.cpi.acleda.ebankpay.rsp.TransactionQueryRsp;
import com.hisun.lemon.cpi.acleda.ebankpay.util.StringUtils;
import com.hisun.lemon.cpi.bnkapi.acleda.ACLEDARetCdConvert;
import com.hisun.lemon.cpi.bo.BaseBnkRspBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.entity.ShortcutOrderDO;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
@Component
public class FastpayACLEDAApi {

    public static final int  OPENSESSIONV2 = 0;
    public static final int SETDIRECTION = 1;
    public static final int TRANSACTIONSTATUS = 2;

    @Resource
    AcledaProperties acledaProperties;

    @Resource
    AcledaApi acledaApi;

    @Resource
    ACLEDARetCdConvert acledaRetCdConvert;

    @Resource
    private ObjectMapper objectMapper;

    private static final Logger logger = LoggerFactory.getLogger(FastpayACLEDAApi.class);


    public OpenSessionV2Rsp openSessionV2(ShortcutOrderDO shortcutOrderDO){
        OpenSessionV2Req openSessionV2Req = new OpenSessionV2Req();
        TransactionDetails transactionDetails = new TransactionDetails();
        openSessionV2Req.setLoginId(acledaProperties.getLogin());
        openSessionV2Req.setPassword(acledaProperties.getPassword());
        openSessionV2Req.setMerchantId(acledaProperties.getMerchantId());
        openSessionV2Req.setTransactionDetails(transactionDetails);
        //设置ACLEDA创建session需要的属性值
        transactionDetails.setTxid(shortcutOrderDO.getChkKey());
        transactionDetails.setPurchaseAmount(shortcutOrderDO.getOrdAmt().doubleValue());
        //收银台订单号
        transactionDetails.setInvoiceid(shortcutOrderDO.getChkKey());
        //将信息设置到备注中
        transactionDetails.setItem(shortcutOrderDO.getRmk());
        transactionDetails.setPurchaseDate(DateTimeUtils.getCurrentLocalDate().toEpochDay());
        //过期时间，单位分
        transactionDetails.setExpiryTime(5);
        transactionDetails.setPurchaseCurrency("USD");
        transactionDetails.setQuantity(1);
        transactionDetails.setPurchaseDesc(shortcutOrderDO.getRmk());

        //创建oepnsessionv2接口请求参数的md5签名
        String strData = StringUtils.sort(openSessionV2Req);
        openSessionV2Req.setSignature(ACLEDASecurity.signature(strData));

        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, openSessionV2Req, true);
        logger.info("FastpayACLEDAApi.openSessionV2() acleda银行创建session请求参数 " + reqData);
        OpenSessionV2Rsp openSessionV2Rsp = null;
        try{
            openSessionV2Rsp = acledaApi.doSend(openSessionV2Req, AcledaEnumCommon.EnumSource.openSessionV2);
            logger.info("acleda银行创建session请求响应对象： " + openSessionV2Rsp);
            if(openSessionV2Rsp.getCode() == 0 && openSessionV2Rsp.getErrorDetails().equals(CpiConstants.ACLEDA_REQ_SUCCESS)){
                return openSessionV2Rsp;
            }

        }catch (Exception ex){
            logger.error("FastpayACLEDAApi.openSessionV2 Exception : ",ex);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        //转换ACLEDA返回的数字错误码
        LemonException.throwBusinessException(
                    acledaRetCdConvert.Convert(String.valueOf(openSessionV2Rsp.getCode()),FastpayACLEDAApi.OPENSESSIONV2).getMsgCd());
        return openSessionV2Rsp;

    }


    public BaseBnkRspBO setDirection(OpenSessionV2Rsp openSessionV2Rsp,int direction){
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        //设置资金流入流出方向
        DirectionReq directionReq = new DirectionReq();
        //Cash=1 int    Cash=2 out
        directionReq.setDirection(direction);
        //ACLEDA对接的商户平台帐号
        directionReq.setAccountnumber(Integer.valueOf(acledaProperties.getCashIn()));
        directionReq.setPtokenid(openSessionV2Rsp.getXtran().getPaymentTokenid());
        directionReq.setSessionid(openSessionV2Rsp.getSessionId());

        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, directionReq, true);
        logger.info("FastpayACLEDAApi.setDirection() acleda银行设置资金流方向请求参数 " + reqData);
        DirectionRsp directionRsp = null;
        try {
            directionRsp = acledaApi.doSend(directionReq, AcledaEnumCommon.EnumSource.setDirection);
            logger.info("ACLEDA.setDirection请求接口返回数据: " + directionRsp);
        }catch (Exception ex){
            logger.error("FastpayACLEDAApi.setDirection Exception : ",ex);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        baseBnkRspBO.setOrgRspCd(acledaRetCdConvert.Convert(String.valueOf(directionRsp.getResult()),FastpayACLEDAApi.SETDIRECTION).getMsgCd());
        baseBnkRspBO.setOrgRspMsg(directionRsp.getMessage());
        return baseBnkRspBO;
    }


    public BaseBnkRspBO getTransactionStatus(String sessionId,String paymentTokenId){
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();

        TransactionQueryReq transactionQueryReq = new TransactionQueryReq();
        transactionQueryReq.setPtokenId(sessionId);
        transactionQueryReq.setSessionid(paymentTokenId);

        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, transactionQueryReq, true);
        logger.info("FastpayACLEDAApi.getTransactionStatus() acleda银行查询交易状态请求参数 " + reqData);
        TransactionQueryRsp transactionQueryRsp = null;
        try {
            transactionQueryRsp = acledaApi.doSend(transactionQueryReq, AcledaEnumCommon.EnumSource.transactionQuery);
            logger.info("ACLEDA.getTransactionStatus接口请求返回数据: " + transactionQueryRsp);
            if(transactionQueryRsp.getResultCode() == 0 && JudgeUtils.equalsIgnoreCase(transactionQueryRsp.getResultMessage(),CpiConstants.ACLEDA_REQ_SUCCESS)){
                baseBnkRspBO.setOrgRspMsg(transactionQueryRsp.getResultMessage());
                baseBnkRspBO.setOrgRspCd(String.valueOf(transactionQueryRsp.getResultCode()));
                return baseBnkRspBO;
            }
        }catch (Exception ex){
            logger.error("FastpayACLEDAApi.getTransactionStatus Exception : ",ex);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
//            LemonException.throwBusinessException(
//                    acledaRetCdConvert.Convert(String.valueOf(transactionQueryRsp.getResultCode()),FastpayACLEDAApi.TRANSACTIONSTATUS).getMsgCd());
        baseBnkRspBO.setOrgRspMsg(transactionQueryRsp.getResultMessage());
        baseBnkRspBO.setOrgRspCd(acledaRetCdConvert.Convert(String.valueOf(transactionQueryRsp.getResultCode()),FastpayACLEDAApi.TRANSACTIONSTATUS).getMsgCd());
        return baseBnkRspBO;
    }


    public BaseBnkRspBO fastpay(ShortcutOrderDO shortcutOrderDO, Map<String,String> resultMap){
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();
        int direction = Integer.valueOf(resultMap.get("direction"));
        try{
            //创建银行资金交易session
            OpenSessionV2Rsp openSessionV2Rsp = openSessionV2(shortcutOrderDO);
            //设置银行资金流方向
            baseBnkRspBO = setDirection(openSessionV2Rsp,direction);
            //返回tokenId sessionId
            resultMap.put("sessionId",openSessionV2Rsp.getSessionId());
            resultMap.put("paymentTokenId",openSessionV2Rsp.getXtran().getPaymentTokenid());
        }catch (LemonException le){
            baseBnkRspBO.setOrgRspCd(le.getMsgCd());
            baseBnkRspBO.setOrgRspMsg("SESSION OPEN FAIL");
            baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
            baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
            baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_FAIL);
            return baseBnkRspBO;
        }
        //设置返回值
        baseBnkRspBO.setOutOrdNo(shortcutOrderDO.getFndOrdNo());
        baseBnkRspBO.setChkKey(shortcutOrderDO.getChkKey());
        baseBnkRspBO.setOrgJrnNo(shortcutOrderDO.getOrgJrnNo());
        baseBnkRspBO.setOrgRspCd(CpiMsgCd.SUCCESS.getMsgCd());
        baseBnkRspBO.setOrgRspMsg("SESSION OPEN SUCCESS");
        baseBnkRspBO.setOrgOrdDt(DateTimeUtils.getCurrentLocalDate());
        baseBnkRspBO.setOrgOrdTm(DateTimeUtils.getCurrentLocalTime());
        baseBnkRspBO.setTxFlg(CpiConstants.BANK_RSP_WAIT);
        return baseBnkRspBO;
    }


    public BaseBnkRspBO closeSession(String sessionId){
        BaseBnkRspBO baseBnkRspBO = new BaseBnkRspBO();

        CloseSessionReq closeSessionReq = new CloseSessionReq();
        closeSessionReq.setSessionId(sessionId);

        String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, closeSessionReq, true);
        logger.info("FastpayACLEDAApi.closeSession() acleda银行关闭交易请求参数 " + reqData);
        CloseSessionRsp closeSessionRsp = null;
        try {
            closeSessionRsp = acledaApi.doSend(closeSessionReq, AcledaEnumCommon.EnumSource.closeSession);
            logger.info("ACLEDA.closeSession接口请求返回数据: " + closeSessionRsp);
            if(closeSessionRsp.getErrorCode() == 0 && JudgeUtils.equalsIgnoreCase(closeSessionRsp.getErrorMessage(),CpiConstants.ACLEDA_REQ_SUCCESS)){
                baseBnkRspBO.setOrgRspMsg(closeSessionRsp.getErrorMessage());
                baseBnkRspBO.setOrgRspCd(String.valueOf(closeSessionRsp.getErrorCode()));
                return baseBnkRspBO;
            }
        }catch (Exception ex){
            logger.error("FastpayACLEDAApi.getTransactionStatus Exception : ",ex);
            LemonException.throwBusinessException(CpiMsgCd.BANK_COMMUNICATION_EXCEPTION.getMsgCd());
        }
        baseBnkRspBO.setOrgRspMsg(closeSessionRsp.getErrorMessage());
        baseBnkRspBO.setOrgRspCd(acledaRetCdConvert.Convert(String.valueOf(closeSessionRsp.getErrorCode()),FastpayACLEDAApi.TRANSACTIONSTATUS).getMsgCd());
        return baseBnkRspBO;
    }
}
