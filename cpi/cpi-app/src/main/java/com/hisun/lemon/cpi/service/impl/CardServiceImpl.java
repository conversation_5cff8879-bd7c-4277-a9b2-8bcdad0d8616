package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.ICardBinDao;
import com.hisun.lemon.cpi.dao.ICardProtDao;
import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpi.dto.CardBinRspDTO;
import com.hisun.lemon.cpi.entity.CardBinDO;
import com.hisun.lemon.cpi.entity.CardProtDO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.ICardService;
import com.hisun.lemon.cpi.utils.EncryptUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Rui on 2017/7/7.
 */
@Transactional(propagation = Propagation.NOT_SUPPORTED,readOnly = true)
@Service
public class CardServiceImpl extends BaseService implements ICardService {

    @Resource
    ICardBinDao cardBinDao;

    @Resource
    ICardProtDao cardProtDao;

    @Resource
    private EncryptUtils encryptUtils;

    private static final Logger logger = LoggerFactory.getLogger(CardServiceImpl.class);

    @Override
    public GenericRspDTO<AgrInfoRspDTO> queryCardsInfo(CorpBusTyp corpBusTyp, CorpBusSubTyp corpBusSubTyp, String userNo, String crdAcTyp) {
        //返回对象
        AgrInfoRspDTO agrInfoDTO = new AgrInfoRspDTO();

        try {
            String userId = null;
            if (JudgeUtils.isBlank(LemonUtils.getUserId())) {
                if(JudgeUtils.isBlank(userNo)) {
                    return GenericRspDTO.newInstance(CpiMsgCd.USER_IS_NULL.getMsgCd(), new AgrInfoRspDTO());
                }else {
                    userId = userNo;
                }
            }else {
                userId = LemonUtils.getUserId();
            }
            //根据业务类型去进行查找
            String corpBusTypStr = null;
            String corpBusSubTypStr = null;
            if (JudgeUtils.isNotNull(corpBusSubTyp)) {
                corpBusTypStr = corpBusTyp.getType();
            }
            if (JudgeUtils.isNotNull(corpBusSubTyp)) {
                corpBusSubTypStr = corpBusSubTyp.getType();
            }
            //查找卡协议
            List<CardProtDO> list = cardProtDao.selectByCorpBusTyp(userId, corpBusTypStr, corpBusSubTypStr, crdAcTyp);
//            if (JudgeUtils.isNull(list) || list.size() <= 0) {
//                return GenericRspDTO.newInstance(CpiMsgCd.AGR_IS_EMPTY.getMsgCd(), new AgrInfoRspDTO());
//            }
            //拼装返回的卡协议对象信息
            List<AgrInfoRspDTO.CardAgrInfo> cardAgrInfos = new ArrayList<>();
            for (CardProtDO cardProtDO : list) {
                AgrInfoRspDTO.CardAgrInfo cardAgrInfo = new AgrInfoRspDTO.CardAgrInfo();
                BeanUtils.copyProperties(cardAgrInfo, cardProtDO);
                cardAgrInfos.add(cardAgrInfo);
            }
            agrInfoDTO.setList(cardAgrInfos);
            GenericRspDTO<AgrInfoRspDTO> rspGenericDTO = GenericRspDTO.newSuccessInstance(agrInfoDTO);
            return rspGenericDTO;
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), agrInfoDTO);
        } catch (Exception e) {
            logger.error("CardServiceImpl.queryCardsInfo exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), agrInfoDTO);
        }
    }

    @Override
    public GenericRspDTO<CardBinRspDTO> queryCardBin(String crdNo) {
        //返回对象
        CardBinRspDTO cardBinDTO = new CardBinRspDTO();

        try {
//            String crdNo = cardBinReqDTO.getCrdNo();
            int crdLen = crdNo.length();
            //查询卡Bin
            CardBinDO cardBinDO = cardBinDao.selectByCardBin(crdNo, crdLen);
            //判断是否查询到卡Bin
            if (JudgeUtils.isNull(cardBinDO)) {
                return GenericRspDTO.newInstance(CpiMsgCd.CARD_BIN_IS_NOT_FUND.getMsgCd(), new CardBinRspDTO());
            }
            //属性拷贝
            BeanUtils.copyProperties(cardBinDTO, cardBinDO);
            return GenericRspDTO.newSuccessInstance(cardBinDTO);
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), cardBinDTO);
        } catch (Exception e) {
            logger.error("CardServiceImpl.queryCardBin exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd(), cardBinDTO);
        }

    }

    @Override
    public GenericRspDTO<NoBody> verifyCard(String agrNo, String crdNo) {

        try {
            //根据协议号查询加密卡号
            CardProtDO cardProtDO = cardProtDao.get(agrNo);
            if(JudgeUtils.isNull(cardProtDO)){
                return GenericRspDTO.newInstance(CpiMsgCd.AGR_IS_NOT_FUND.getMsgCd());
            }
            String crdNoEnc = cardProtDO.getCrdNoEnc();
            if(crdNoEnc.equals(encryptUtils.encrypt(crdNo, CpiConstants.ENCRYPT))){
                return GenericRspDTO.newSuccessInstance();
            }else {
                return GenericRspDTO.newInstance(CpiMsgCd.CARD_VERIFY_FAILED.getMsgCd());
            }
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd());
        } catch (Exception e) {
            logger.error("CardServiceImpl.queryCardsInfo exception : ", e);
            return GenericRspDTO.newInstance(CpiMsgCd.CPI_SYS_EXCEPTION.getMsgCd());
        }
    }
}
