package com.hisun.lemon.cpi.dao;

import com.hisun.lemon.cpi.entity.DmAccountInfoDO;
import com.hisun.lemon.framework.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 数币-账户信息DAO接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 9:31
 */
@Mapper
public interface IDmAccountInfoDao extends BaseDao<DmAccountInfoDO> {

    /**
     * 根据账户ID查询数币账户信息
     *
     * @param accountId 账户ID
     * @return 账户信息DO对象
     */
    DmAccountInfoDO queryByAccId(@Param("accountId") String accountId);
}
