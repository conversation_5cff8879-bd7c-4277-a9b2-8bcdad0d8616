package com.hisun.lemon.cpi.bnkapi.common;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpRequestUtil {

	private static Logger logger = LoggerFactory.getLogger(HttpRequestUtil.class);

	/**
	 * 发送get请求
	 * 
	 * @param url
	 *            路径
	 * @return json格式数据
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public static <T> T jsonGet(String url, Class<T> rspType) throws ClientProtocolException, IOException {
		CloseableHttpClient client = HttpClients.createDefault();
		// 发送get请求
		HttpGet request = new HttpGet(url);
		CloseableHttpResponse response = client.execute(request);
		try {
			/** 请求发送成功，并得到响应 **/
			if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				/** 读取服务器返回过来的json字符串数据 **/
				String strJson = EntityUtils.toString(response.getEntity());
				/** 把json字符串转换成json对象 **/
				T result = JsonUtil.toBean(strJson, rspType, false);
				url = URLDecoder.decode(url, "UTF-8");
				return result;
			} else {
				logger.error("get请求提交失败:" + url);
			}
		} catch (IOException e) {
			logger.error("get请求提交失败:" + url, e);
		} finally {
			response.close();
		}
		return null;
	}

	/**
	 * httpPost
	 * 
	 * @param url
	 *            路径
	 * @param jsonParam
	 *            参数
	 * @return json格式数据
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public static <T> T jsonPost(String url, Object bean, Class<T> rspType)
			throws ClientProtocolException, IOException {
		return jsonPost(url, bean, false, rspType);
	}

	/**
	 * post请求
	 * 
	 * @param url
	 *            url地址
	 * @param jsonParam
	 *            参数
	 * @param noNeedResponse
	 *            不需要返回结果
	 * @return json格式数据
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public static <T> T jsonPost(String url, Object bean, boolean noNeedResponse, Class<T> rspType)
			throws ClientProtocolException, IOException {

		// post请求返回结果
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost method = new HttpPost(url);

		if (null != bean) {
			// 解决中文乱码问题
			String jsonStr = null;
			jsonStr = JsonUtil.toJSon(bean);
			StringEntity entity = new StringEntity(jsonStr, "utf-8");
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/json");
			method.setEntity(entity);
		}
		CloseableHttpResponse resultResponse = httpClient.execute(method);
		url = URLDecoder.decode(url, "UTF-8");
		/** 请求发送成功，并得到响应 **/
		if (resultResponse.getStatusLine().getStatusCode() == 200) {
			try {
				/** 读取服务器返回过来的json字符串数据 **/
				String strJson = EntityUtils.toString(resultResponse.getEntity());
				if (noNeedResponse) {
					return null;
				}
				/** 把json字符串转换成json对象 **/
				T result = JsonUtil.toBean(strJson, rspType, false);
				return result;
			} catch (Exception e) {
				logger.error("post请求提交失败:" + url, e);
			} finally {
				resultResponse.close();
			}
		}

		return null;
	}

	/**
	 * 发送get请求
	 * 
	 * @param url
	 *            路径
	 * @return xml格式数据
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public static <T> T xmlGet(String url, Class<T> rspType) throws ClientProtocolException, IOException {
		CloseableHttpClient client = HttpClients.createDefault();
		// 发送get请求
		HttpGet request = new HttpGet(url);
		CloseableHttpResponse response = client.execute(request);
		try {
			/** 请求发送成功，并得到响应 **/
			if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				/** 读取服务器返回过来的xml字符串数据 **/
				String strXml = EntityUtils.toString(response.getEntity());
				/** 把xml字符串转换成对象 **/
				T result = XmlUtil.toBean(strXml, rspType);
				url = URLDecoder.decode(url, "UTF-8");
				return result;
			} else {
				logger.error("get请求提交失败:" + url);
			}
		} catch (IOException e) {
			logger.error("get请求提交失败:" + url, e);
		} finally {
			response.close();
		}
		return null;
	}

	/**
	 * httpPost
	 * 
	 * @param url
	 *            路径
	 * @param jsonParam
	 *            参数
	 * @return xml格式数据
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public static <T> T xmlPost(String url, Object bean, Class<T> rspType) throws ClientProtocolException, IOException {
		return xmlPost(url, bean, false, rspType);
	}

	/**
	 * post请求
	 * 
	 * @param url
	 *            url地址
	 * @param jsonParam
	 *            参数
	 * @param noNeedResponse
	 *            不需要返回结果
	 * @return xml格式数据
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public static <T> T xmlPost(String url, Object bean, boolean noNeedResponse, Class<T> rspType)
			throws ClientProtocolException, IOException {

		// post请求返回结果
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost method = new HttpPost(url);

		if (null != bean) {
			// 解决中文乱码问题
			String xmlStr = XmlUtil.toXml(bean);
			StringEntity entity = new StringEntity(xmlStr, "utf-8");
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/xml");
			method.setEntity(entity);
		}
		CloseableHttpResponse resultResponse = httpClient.execute(method);
		url = URLDecoder.decode(url, "UTF-8");
		/** 请求发送成功，并得到响应 **/
		if (resultResponse.getStatusLine().getStatusCode() == 200) {
			try {
				/** 读取服务器返回过来的xml字符串数据 **/
				String rspStr = EntityUtils.toString(resultResponse.getEntity());
				if (noNeedResponse) {
					return null;
				}
				/** 把xml字符串转换成对象 **/
				T result = XmlUtil.toBean(rspStr, rspType);
				return result;
			} catch (Exception e) {
				logger.error("post请求提交失败:" + url, e);
			} finally {
				resultResponse.close();
			}
		}
		return null;
	}
	/**
	 * httpPost
	 * 
	 * @param url
	 *            路径
	 * @param jsonParam
	 *            参数
	 * @return xml格式数据
	 * @throws Exception 
	 */
	public static <T> T doPost(String url,  Map<String, String> map, Class<T> rspType) throws Exception {
		return doPost(url, map, false, rspType);
	}

	/**
	 * post请求
	 * 
	 * @param url
	 *            url地址
	 * @param jsonParam
	 *            参数
	 * @param noNeedResponse
	 *            不需要返回结果
	 * @return xml格式数据
	 * @throws Exception 
	 */
	public static <T> T doPost(String url, Map<String, String> map, boolean noNeedResponse, Class<T> rspType)
			throws Exception {

		// post请求返回结果
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost method = new HttpPost(url);

		if (null != map) {
			// 解决中文乱码问题
			String xmlStr = null;
			xmlStr = XmlUtil.mapToXml(map);
            System.out.println("###########xmlStr###########"+xmlStr);   
			StringEntity entity = new StringEntity(xmlStr, "utf-8");
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/xml");
			method.setEntity(entity);
		}
		CloseableHttpResponse resultResponse = httpClient.execute(method);
		url = URLDecoder.decode(url, "UTF-8");
		/** 请求发送成功，并得到响应 **/
		if (resultResponse.getStatusLine().getStatusCode() == 200) {
			try {
				/** 读取服务器返回过来的xml字符串数据 **/
				String rspStr = EntityUtils.toString(resultResponse.getEntity());
				rspStr = new String(rspStr.getBytes("ISO-8859-1"), "UTF-8");
	            System.out.println("###########rspStr###########"+rspStr);   
				if (noNeedResponse) {
					return null;
				}
				/** 把xml字符串转换成对象 **/
				T result = XmlUtil.toBean(rspStr, rspType);
				return result;
			} catch (Exception e) {
				logger.error("post请求提交失败:" + url, e);
			} finally {
				resultResponse.close();
			}
		}
		return null;
	}	
}
