package com.hisun.lemon.cpi.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.bo.BaseAccChkFilBO;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.dao.*;
import com.hisun.lemon.cpi.entity.*;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.cpi.service.ICheckMgrExtendService;
import com.hisun.lemon.cpi.service.ICheckMgrService;
import com.hisun.lemon.framework.core.InitLemonData;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.jcommon.file.FileSftpUtils;
import com.hisun.lemon.jcommon.file.FileUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 对账参数和对账批次主控管理实现类 service
 */
@Service
public class CheckMgrServiceImpl extends BaseService implements ICheckMgrService {

    @Resource
    private IAccParamDao accParamDao;

    @Resource
    private IAccControlDao accControlDao;

    @Resource
    private ICheckMgrExtendService checkMgrExtendService;

    @Resource
    private IAccRefundCfgDao accRefundCfgDao;

    @Resource
    private IFundOrderDao fundOrderDao;
    @Resource
    private IRefundOrderDao refundOrderDao;
    @Resource
    private IAccUploadCfgDao accUploadCfgDao;
    @Resource
    private IAccFundCfgDao accFundCfgDao;
    @Resource
    private IRefundSuborderDao refundSuborderDao;

    private static final Logger logger = LoggerFactory.getLogger(CheckMgrServiceImpl.class);

    /**
     * 增加对账参数
     */
    @Override
    public boolean addAccParam(AccParamDO accParamDO) {
        return accParamDao.insert(accParamDO) > 0 ? true : false;
    }

    /**
     * 查询生效的对账参数
     */
    @Override
    public List<AccParamDO> queryEffAccParamList() {
        return accParamDao.queryEffAccParamList();
    }

    /**
     * 查询对账批次是否已存在
     */
    @Override
    public AccControlDO queryUnfinishedAccControl(AccParamDO accParamDO) {
        AccControlDO accControlDO = null;
        LocalDate chkFilDt = DateTimeUtils.getCurrentLocalDate().minusDays(1L);
        while(true){
            accControlDO = accControlDao.queryUnfinishedAccControl(accParamDO);
            if (JudgeUtils.isNotNull(accControlDO)
                    && accControlDO.getChkDt().compareTo(chkFilDt) < 0){
                if(CorpBusTyp.REFUND.getType().equals(accParamDO.getCorpBusTyp())){
                   // List<RefundSuborderDO> refundOrderDOList = refundSuborderDao.getRefundOrderByDate(accControlDO.getChkDt());
                    List<RefundSuborderDO> refundOrderDOList = refundSuborderDao.getRefundOrderByDateAndRutCorg(accControlDO.getChkDt(),accControlDO.getRutCorg());
                    if(refundOrderDOList !=null && !refundOrderDOList.isEmpty()){
                        return accControlDO;
                    }
                    accControlDO.setChkFilSts(CpiConstants.CHK_FIL_FINISHED);
                    accControlDao.update(accControlDO);
                }else{
                    List<FundOrderDO> fundOrderDOList = fundOrderDao.getFundOrderList(accControlDO.getChkDt(), 0, 10);
                    if(fundOrderDOList !=null && !fundOrderDOList.isEmpty()){
                        return accControlDO;
                    }
                    accControlDO.setChkFilSts(CpiConstants.CHK_FIL_FINISHED);
                    accControlDao.update(accControlDO);
                }
            }else{
                break;
            }
        }
        return accControlDO;
    }

    /**
     * 根据生效的对账参数，生成对账批次
     */
    @Override
    @Transactional
    public void registerChkBatNo(LocalDate checkDt) {
        try {
            String rutCorg = null;
            String corpBusTyp = null;
            String corpBusSubTyp = null;
            AccControlDO accControlDO = null;

            //查询有效的对账参数信息
            List<AccParamDO> AccParamList = accParamDao.queryEffAccParamList();

            //根据对账参数信息，创建对账批次信息
            if (CollectionUtils.isNotEmpty(AccParamList)) {
                for (AccParamDO accParamDO : AccParamList) {

                    //检查是否已生成当天的对账批次
                    rutCorg = accParamDO.getRutCorg();
                    corpBusTyp = accParamDO.getCorpBusTyp();
                    corpBusSubTyp = accParamDO.getCorpBusSubTyp();

                    accControlDO = accControlDao.getAccControl(checkDt, rutCorg, corpBusTyp, corpBusSubTyp);
                    if (JudgeUtils.isNotNull(accControlDO)) {
                        logger.debug("==================机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 对账日期: " + DateTimeUtils.formatLocalDate(checkDt) + " 对账批次已存在");
                        continue;
                    }

                    //生成对账批次号
                    String chkBatNo = IdGenUtils.generateIdWithDateTime("CHK", "CHK", 3);

                    //插入对账批次信息
                    accControlDO = new AccControlDO();
                    BeanUtils.copyProperties(accControlDO, accParamDO);
                    accControlDO.setChkBatNo(chkBatNo);
                    accControlDO.setChkDt(checkDt);
                    accControlDO.setChkFilDt(checkDt);
                    accControlDO.setChkFilSts(CpiConstants.CHK_FIL_NOT_START);
                    accControlDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
                    accControlDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                    accControlDO.setTmSmp(DateTimeUtils.getCurrentLocalDateTime());
                    accControlDao.insert(accControlDO);
                }
            }
        } catch (Exception e) {
            logger.error("CheckMgrServiceImpl.registerChkBatNo()，生成对账批次失败，异常为: ", e);
            throw new LemonException(CpiMsgCd.CREATE_CHK_BAT_FAIL.getMsgCd());
        }
    }

    /**
     * 开始执行对账服务
     */
    @Override
    @InitLemonData
    public void beginCheckAcc(AccControlDO accControlDO) {
        LocalDate accDt = LemonUtils.getAccDate();
        if(CpiConstants.OWN_CHK_FLG_N.equals(accControlDO.getOwnChkFlg())){
            //调用对账模块
            this.moduleCheck(accControlDO);
        } else{
            this.ownCheck(accControlDO);
        }

    }

    @Override
    public void writeCheckFile(LocalDate chkFilDt, String chkFilNm, String rutCorg, String fileType) {

        AccUploadCfgDO accUploadCfgDO = accUploadCfgDao.getByUnique(rutCorg);

        //step1:检测本地是否有文件，有则退出
        String  filePathAndName = accUploadCfgDO.getLocalFilePath()+File.separator+chkFilNm;
        File file = new File(filePathAndName);
        if(file.exists()){
            return;
        }

        //step2:查询出来ordDt等于chkFilDt的订单,写入对账文件
        int beginNum = 0;
        int countNum = CpiConstants.MAX_WRITE_NUM;
        int i = 0;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            while (true) {
                beginNum = countNum * i;
                List<BaseAccChkFilBO> chkFilBOList = new ArrayList<>();
                if ("FUND".equals(fileType)) {
                    List<FundOrderDO> fundOrderDOList = fundOrderDao.getFundOrderList(chkFilDt, beginNum, countNum);
                    for (FundOrderDO fundOrderDO : fundOrderDOList) {
                        BaseAccChkFilBO bo = new BaseAccChkFilBO();
                        bo.setChkKey(fundOrderDO.getFudOrdNo());
                        bo.setChkAmt(fundOrderDO.getOrdAmt());
                        bo.setChkSts(fundOrderDO.getOrdSts());
                        bo.setRequestId(fundOrderDO.getReqOrdNo());
                        chkFilBOList.add(bo);
                    }
                } else if ("REFUND".equals(fileType)) {
                    List<RefundOrderDO> refundOrderDOList = refundOrderDao.getRefundOrderList(chkFilDt, beginNum, countNum);
                    for (RefundOrderDO refundOrderDO : refundOrderDOList) {
                        BaseAccChkFilBO bo = new BaseAccChkFilBO();
                        bo.setChkKey(refundOrderDO.getRfdOrdNo());
                        bo.setChkAmt(refundOrderDO.getOrdAmt());
                        bo.setChkSts(refundOrderDO.getOrdSts());
                        bo.setRequestId(refundOrderDO.getReqOrdNo());
                        chkFilBOList.add(bo);
                    }
                } else {
                    return;
                }

                //写入对账文件，失败的话，删除文件
                FileUtils.writeAppend(stringBuilder.toString(), filePathAndName);

                if (CollectionUtils.isNotEmpty(chkFilBOList)) {
                    stringBuilder = new StringBuilder();
                    //准备数据
                    for (BaseAccChkFilBO bo : chkFilBOList) {
                        stringBuilder.append(bo.getChkKey()).append("|").append(bo.getChkAmt())
                                .append("|").append(bo.getChkSts()).append("|").append(bo.getRequestId()).append("\n");
                    }
                } else {
                    //查无对账明细数据，退出循环
                    break;
                }
                i++;
            }
            //step3：上传对账文件，失败删除本地对账文件
            FileSftpUtils.upload(filePathAndName,accUploadCfgDO.getUploadIp(),Integer.parseInt(accUploadCfgDO.getUploadPort()),
                    accUploadCfgDO.getConnectTime(),accUploadCfgDO.getUploadPath(),accUploadCfgDO.getUploadNm(),accUploadCfgDO.getUploadPwd());
        } catch (Exception e){
            logger.error("写入对账文件或者上传失败：",e);
            file.delete();
            return;
        }

    }

    /**
     * 内部自己对账
     * @param accControlDO
     */
    private void ownCheck(AccControlDO accControlDO){
        String chkBatNo = accControlDO.getChkBatNo();//对账批次号
        LocalDate chkDt = accControlDO.getChkFilDt();//当前对账日期
        String rutCorg = accControlDO.getRutCorg();//路径合作机构
        String corpBusTyp = accControlDO.getCorpBusTyp();//合作业务类型
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();//合作业务子类型

        //锁ID，前缀_路由合作机构_合作业务子类型：CHK_ICBC_02_0201
        String lockId = CpiConstants.CHK_LOCK_PREFIX + rutCorg + "_" + corpBusTyp + "_" + corpBusSubTyp;
        logger.debug("调起对账服务，对账锁ID： " + lockId + "; 对账日期：" + DateTimeUtils.formatLocalDate(chkDt));

        //对账流程
        try {

            //step1:查询出对账配置表中的配置信息
            AccRefundCfgDO accCfgDO = accRefundCfgDao.getAccCfgDO(rutCorg, corpBusTyp, corpBusSubTyp);
            if (JudgeUtils.isNull(accCfgDO)) {
                logger.error("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + " 没有添加银行对账配置信息");
                return ;
            }

            //step2:开始对账
            logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 开始对账");
            String chkFilSts = accControlDO.getChkFilSts();
            switch (chkFilSts) {
                //0：未对账，开始获取对账文件
                case CpiConstants.CHK_FIL_NOT_START:
                    chkFilSts = checkMgrExtendService.getRefundChkFile(accControlDO, accCfgDO);
                    if(!CpiConstants.CHK_FIL_DOWNLOAD.equals(chkFilSts)){
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 获取对账文件异常");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 获取对账文件完成");

                    //1：已获取对账文件，解析对账文件，批量导入数据库
                case CpiConstants.CHK_FIL_DOWNLOAD:
                    chkFilSts = checkMgrExtendService.importRefundChkFile(accControlDO, accCfgDO);
                    if(!CpiConstants.CHK_FIL_IMPORT.equals(chkFilSts)){
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账入库出错");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账明细导入数据库完成");

                    //2：文件已入库，开始对账
                case CpiConstants.CHK_FIL_IMPORT:
                    chkFilSts = checkMgrExtendService.checkRefundFile(accControlDO, accCfgDO);
                    if(!CpiConstants.CHK_FIL_SUCCESS.equals(chkFilSts)){
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 明细对账失败");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账成功，待账务处理");

                    //3: 对账成功，待账务处理
                case CpiConstants.CHK_FIL_SUCCESS:
                    chkFilSts = checkMgrExtendService.accountTreatment(accControlDO);
                    if(!CpiConstants.CHK_FIL_FINISHED.equals(chkFilSts)){
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 账务处理失败");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账结束");

                    //4：对账完成
                case CpiConstants.CHK_FIL_FINISHED:
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            logger.error("CheckMgrServiceImpl.ownCheck()，机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账失败! 异常为: ", e);
            return;
        }
    }

    /**
     * 调用对账模块对账
     * @param accControlDO
     */
    private void moduleCheck(AccControlDO accControlDO) {
        //对账批次号
        String chkBatNo = accControlDO.getChkBatNo();
        //当前对账日期
        LocalDate chkDt = accControlDO.getChkFilDt();
        //路径合作机构
        String rutCorg = accControlDO.getRutCorg();
        //合作业务类型
        String corpBusTyp = accControlDO.getCorpBusTyp();
        //合作业务子类型
        String corpBusSubTyp = accControlDO.getCorpBusSubTyp();
        String chkFilPath = null;

        //锁ID，前缀_路由合作机构_合作业务子类型：CHK_ICBC_02_0201
        logger.info("调起对账服务 对账日期：" + DateTimeUtils.formatLocalDate(chkDt));

        //对账流程
        try {
            //查询出对账配置表中的配置信息
            AccFundCfgDO accFundCfgDO = accFundCfgDao.getAccCfgDO(rutCorg, corpBusTyp, corpBusSubTyp);
            if (JudgeUtils.isNull(accFundCfgDO)) {
                logger.error("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + " 没有添加银行对账配置信息");
                return ;
            }

            //开始对账
            logger.info("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 开始对账");
            String chkFilSts = accControlDO.getChkFilSts();
            switch (chkFilSts) {
                //0：未对账，开始获取银行对账文件
                case CpiConstants.CHK_FIL_NOT_START:
                    chkFilSts = checkMgrExtendService.getFundChkFile(accControlDO, accFundCfgDO);
                    if(!CpiConstants.CHK_FIL_DOWNLOAD.equals(chkFilSts)) {
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 获取对账文件异常");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 获取对账文件完成");

                //1：已获取对账文件，上传银行对账文件
                case CpiConstants.CHK_FIL_DOWNLOAD:
                    String filPath = accFundCfgDO.getChkFilePath()+ File.separator+accControlDO.getChkFilNm();
                    chkFilSts = checkMgrExtendService.uploadFundChkFile(accControlDO.getChkBatNo(),filPath,CpiConstants.CHK_FIL_BANK_UPLODAD,accFundCfgDO);
                    if(!CpiConstants.CHK_FIL_BANK_UPLODAD.equals(chkFilSts)) {
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 上传银行对账文件异常");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 上传对账文件完成");

                //2：已获取对账文件，资金模块生成新的对账文件
                case CpiConstants.CHK_FIL_BANK_UPLODAD:
                    chkFilPath = checkMgrExtendService.writeFundChkFile(accControlDO, accFundCfgDO);
                    if(StringUtils.isEmpty(chkFilPath)) {
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 写入对账文件出错");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 写入对账文件完成");

                //3：上传资金模块对账文件到文件服务器
                case CpiConstants.CHK_FIL_WRITE:
                    chkFilSts = checkMgrExtendService.uploadFundChkFile(accControlDO.getChkBatNo(),chkFilPath,CpiConstants.CHK_FIL_FINISHED,accFundCfgDO);
                    if(!CpiConstants.CHK_FIL_FINISHED.equals(chkFilSts)) {
                        logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 上传写入的对账文件异常");
                        return;
                    }
                    logger.debug("机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 上传写入的对账文件成功");

                    //4：对账完成
                case CpiConstants.CHK_FIL_FINISHED:
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            logger.error("CheckMgrServiceImpl.moduleCheck()，机构: " + rutCorg + "; 业务: " + corpBusTyp + "; 子业务: " + corpBusSubTyp + "; 对账批次号: " + chkBatNo + " 对账失败! 异常为: ", e);
            return;
        }
    }
}
