/*
 * @ClassName CardProtDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-12 17:24:34
 */
package com.hisun.lemon.cpi.entity;

import com.hisun.lemon.framework.data.BaseDO;
import com.hisun.lemon.framework.idgenerate.auto.AutoIdGen;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class CardProtDO extends BaseDO {
    /**
     * @Fields agrNo 内部协议号
     */
    private String agrNo;
    /**
     * @Fields corpBusTyp 合作业务类型
     */
    private String corpBusTyp;
    /**
     * @Fields corpBusSubTyp 合作业务子类型
     */
    private String corpBusSubTyp;
    /**
     * @Fields agrEffFlg 生效标志，W：初登记，Y：生效，N：失效
     */
    private String agrEffFlg;
    /**
     * @Fields signDt 签约日期
     */
    private LocalDate signDt;
    /**
     * @Fields signTm 签约时间
     */
    private LocalTime signTm;
    /**
     * @Fields unsignDt 解约日期
     */
    private LocalDate unsignDt;
    /**
     * @Fields unsignTm 解约时间
     */
    private LocalTime unsignTm;
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields mblNo 手机号
     */
    private String mblNo;
    /**
     * @Fields bnkPsnFlg 对公对私标志，B：对公，C：对私
     */
    private String bnkPsnFlg;
    /**
     * @Fields crdCorpOrg 合作机构号
     */
    private String crdCorpOrg;
    /**
     * @Fields rutCorpOrg 路径机构号
     */
    private String rutCorpOrg;
    /**
     * @Fields signAgrno 机构协议号
     */
    private String signAgrno;
    /**
     * @Fields agrDirect 协议方向，0：双向协议1：我方单侧协议
     */
    private String agrDirect;
    /**
     * @Fields crdAcTyp 卡类型，D：借记卡，C：贷记卡
     */
    private String crdAcTyp;
    /**
     * @Fields crdNoEnc 加密银行卡号
     */
    private String crdNoEnc;
    /**
     * @Fields crdNoLast 银行卡后四位
     */
    private String crdNoLast;
    /**
     * @Fields crdUsrNm 账户名
     */
    private String crdUsrNm;
    /**
     * @Fields idTyp 证件类型
     */
    private String idTyp;
    /**
     * @Fields idNoEnc 加密证件号
     */
    private String idNoEnc;
    /**
     * @Fields crdCvv2Enc CVV2
     */
    private String crdCvv2Enc;
    /**
     * @Fields crdExpDtEnc 有效期
     */
    private String crdExpDtEnc;
    /**
     * @Fields rmk 备注
     */
    private String rmk;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public String getCorpBusTyp() {
        return corpBusTyp;
    }

    public void setCorpBusTyp(String corpBusTyp) {
        this.corpBusTyp = corpBusTyp;
    }

    public String getCorpBusSubTyp() {
        return corpBusSubTyp;
    }

    public void setCorpBusSubTyp(String corpBusSubTyp) {
        this.corpBusSubTyp = corpBusSubTyp;
    }

    public String getAgrEffFlg() {
        return agrEffFlg;
    }

    public void setAgrEffFlg(String agrEffFlg) {
        this.agrEffFlg = agrEffFlg;
    }

    public LocalDate getSignDt() {
        return signDt;
    }

    public void setSignDt(LocalDate signDt) {
        this.signDt = signDt;
    }

    public LocalTime getSignTm() {
        return signTm;
    }

    public void setSignTm(LocalTime signTm) {
        this.signTm = signTm;
    }

    public LocalDate getUnsignDt() {
        return unsignDt;
    }

    public void setUnsignDt(LocalDate unsignDt) {
        this.unsignDt = unsignDt;
    }

    public LocalTime getUnsignTm() {
        return unsignTm;
    }

    public void setUnsignTm(LocalTime unsignTm) {
        this.unsignTm = unsignTm;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getBnkPsnFlg() {
        return bnkPsnFlg;
    }

    public void setBnkPsnFlg(String bnkPsnFlg) {
        this.bnkPsnFlg = bnkPsnFlg;
    }

    public String getCrdCorpOrg() {
        return crdCorpOrg;
    }

    public void setCrdCorpOrg(String crdCorpOrg) {
        this.crdCorpOrg = crdCorpOrg;
    }

    public String getRutCorpOrg() {
        return rutCorpOrg;
    }

    public void setRutCorpOrg(String rutCorpOrg) {
        this.rutCorpOrg = rutCorpOrg;
    }

    public String getSignAgrno() {
        return signAgrno;
    }

    public void setSignAgrno(String signAgrno) {
        this.signAgrno = signAgrno;
    }

    public String getAgrDirect() {
        return agrDirect;
    }

    public void setAgrDirect(String agrDirect) {
        this.agrDirect = agrDirect;
    }

    public String getCrdAcTyp() {
        return crdAcTyp;
    }

    public void setCrdAcTyp(String crdAcTyp) {
        this.crdAcTyp = crdAcTyp;
    }

    public String getCrdNoEnc() {
        return crdNoEnc;
    }

    public void setCrdNoEnc(String crdNoEnc) {
        this.crdNoEnc = crdNoEnc;
    }

    public String getCrdNoLast() {
        return crdNoLast;
    }

    public void setCrdNoLast(String crdNoLast) {
        this.crdNoLast = crdNoLast;
    }

    public String getCrdUsrNm() {
        return crdUsrNm;
    }

    public void setCrdUsrNm(String crdUsrNm) {
        this.crdUsrNm = crdUsrNm;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNoEnc() {
        return idNoEnc;
    }

    public void setIdNoEnc(String idNoEnc) {
        this.idNoEnc = idNoEnc;
    }

    public String getCrdCvv2Enc() {
        return crdCvv2Enc;
    }

    public void setCrdCvv2Enc(String crdCvv2Enc) {
        this.crdCvv2Enc = crdCvv2Enc;
    }

    public String getCrdExpDtEnc() {
        return crdExpDtEnc;
    }

    public void setCrdExpDtEnc(String crdExpDtEnc) {
        this.crdExpDtEnc = crdExpDtEnc;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}