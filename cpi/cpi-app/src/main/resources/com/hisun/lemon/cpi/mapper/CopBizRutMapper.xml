<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ICopBizRutDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.CopBizRutDO" >
        <id column="RUT_INF_ID" property="rutInfId" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="RUT_EFF_FLG" property="rutEffFlg" jdbcType="CHAR" />
        <result column="PRI_LVL" property="priLvl" jdbcType="INTEGER" />
        <result column="LOW_AMT" property="lowAmt" jdbcType="DECIMAL" />
        <result column="HIGH_AMT" property="highAmt" jdbcType="DECIMAL" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        RUT_INF_ID, CRD_CORP_ORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, RUT_CORP_ORG, CRD_AC_TYP, 
        RUT_EFF_FLG, PRI_LVL, LOW_AMT, HIGH_AMT, OPR_ID, RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_cop_biz_rut
        where RUT_INF_ID = #{rutInfId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_cop_biz_rut
        where RUT_INF_ID = #{rutInfId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.CopBizRutDO" >
        insert into cpi_cop_biz_rut
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="rutInfId != null" >
                RUT_INF_ID,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="rutEffFlg != null" >
                RUT_EFF_FLG,
            </if>
            <if test="priLvl != null" >
                PRI_LVL,
            </if>
            <if test="lowAmt != null" >
                LOW_AMT,
            </if>
            <if test="highAmt != null" >
                HIGH_AMT,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="rutInfId != null" >
                #{rutInfId,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="rutEffFlg != null" >
                #{rutEffFlg,jdbcType=CHAR},
            </if>
            <if test="priLvl != null" >
                #{priLvl,jdbcType=INTEGER},
            </if>
            <if test="lowAmt != null" >
                #{lowAmt,jdbcType=DECIMAL},
            </if>
            <if test="highAmt != null" >
                #{highAmt,jdbcType=DECIMAL},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.CopBizRutDO" >
        update cpi_cop_biz_rut
        <set >
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="rutEffFlg != null" >
                RUT_EFF_FLG = #{rutEffFlg,jdbcType=CHAR},
            </if>
            <if test="priLvl != null" >
                PRI_LVL = #{priLvl,jdbcType=INTEGER},
            </if>
            <if test="lowAmt != null" >
                LOW_AMT = #{lowAmt,jdbcType=DECIMAL},
            </if>
            <if test="highAmt != null" >
                HIGH_AMT = #{highAmt,jdbcType=DECIMAL},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where RUT_INF_ID = #{rutInfId,jdbcType=VARCHAR}
    </update>
</mapper>