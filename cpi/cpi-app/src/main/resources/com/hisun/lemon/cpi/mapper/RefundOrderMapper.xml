<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IRefundOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.RefundOrderDO" >
        <id column="RFD_ORD_NO" property="rfdOrdNo" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="ORD_CCY" property="ordCcy" jdbcType="CHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
        <result column="ORD_STS" property="ordSts" jdbcType="CHAR" />
        <result column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="ORD_SUCC_DT" property="ordSuccDt" jdbcType="DATE" />
        <result column="ORD_SUCC_TM" property="ordSuccTm" jdbcType="TIME" />
        <result column="USER_TYP" property="userTyp" jdbcType="CHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_NO_ENC" property="crdNoEnc" jdbcType="VARCHAR" />
        <result column="CRD_USR_NM" property="crdUsrNm" jdbcType="VARCHAR" />
        <result column="CRD_NO_LAST" property="crdNoLast" jdbcType="CHAR" />
        <result column="ID_TYP" property="idTyp" jdbcType="CHAR" />
        <result column="ID_NO_ENC" property="idNoEnc" jdbcType="VARCHAR" />
        <result column="BNK_PSN_FLG" property="bnkPsnFlg" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="AUTO_PAY_FLG" property="autoPayFlg" jdbcType="CHAR" />
        <result column="AGR_PAY_DT" property="agrPayDt" jdbcType="DATE" />
        <result column="RFD_TIME" property="rfdTime" jdbcType="TIMESTAMP" />
        <result column="RFD_FAIL_COUNT" property="rfdFailCount" jdbcType="INTEGER" />
        <result column="ORG_RMK" property="orgRmk" jdbcType="VARCHAR" />
        <result column="APPR_STS" property="apprSts" jdbcType="CHAR" />
        <result column="APPR_OPR_ID" property="apprOprId" jdbcType="VARCHAR" />
        <result column="APPR_DT" property="apprDt" jdbcType="DATE" />
        <result column="APPR_TM" property="apprTm" jdbcType="TIME" />
        <result column="APPR_REFUSE_RSN" property="apprRefuseRsn" jdbcType="VARCHAR" />
        <result column="NTF_STS" property="ntfSts" jdbcType="CHAR" />
        <result column="NTF_DT" property="ntfDt" jdbcType="DATE" />
        <result column="NTF_TM" property="ntfTm" jdbcType="TIME" />
        <result column="NTF_RSP_CD" property="ntfRspCd" jdbcType="VARCHAR" />
        <result column="NTF_RSP_MSG" property="ntfRspMsg" jdbcType="VARCHAR" />
        <result column="REQ_ORD_NO" property="reqOrdNo" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="REQ_ORD_DT" property="reqOrdDt" jdbcType="DATE" />
        <result column="REQ_ORD_TM" property="reqOrdTm" jdbcType="TIME" />
        <result column="INNER_FLAG" property="innerFlag" jdbcType="CHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        RFD_ORD_NO, ORD_DT, ORD_TM, ORD_CCY, ORD_AMT, ORD_STS, AC_DT, ORD_SUCC_DT, ORD_SUCC_TM, 
        USER_TYP, USER_ID, MBL_NO, CRD_CORP_ORG, CRD_AC_TYP, CRD_NO_ENC, CRD_USR_NM, CRD_NO_LAST, 
        ID_TYP, ID_NO_ENC, BNK_PSN_FLG, RUT_CORP_ORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, AUTO_PAY_FLG, 
        AGR_PAY_DT, RFD_TIME, RFD_FAIL_COUNT, ORG_RMK, APPR_STS, APPR_OPR_ID, APPR_DT, APPR_TM, 
        APPR_REFUSE_RSN, NTF_STS, NTF_DT, NTF_TM, NTF_RSP_CD, NTF_RSP_MSG, REQ_ORD_NO, CREATE_TIME, 
        MODIFY_TIME, TM_SMP, REQ_ORD_DT, REQ_ORD_TM, INNER_FLAG
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_refund_order
        where RFD_ORD_NO = #{rfdOrdNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_refund_order
        where RFD_ORD_NO = #{rfdOrdNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.RefundOrderDO" >
        insert into cpi_refund_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="rfdOrdNo != null" >
                RFD_ORD_NO,
            </if>
            <if test="ordDt != null" >
                ORD_DT,
            </if>
            <if test="ordTm != null" >
                ORD_TM,
            </if>
            <if test="ordCcy != null" >
                ORD_CCY,
            </if>
            <if test="ordAmt != null" >
                ORD_AMT,
            </if>
            <if test="ordSts != null" >
                ORD_STS,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="ordSuccDt != null" >
                ORD_SUCC_DT,
            </if>
            <if test="ordSuccTm != null" >
                ORD_SUCC_TM,
            </if>
            <if test="userTyp != null" >
                USER_TYP,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC,
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM,
            </if>
            <if test="crdNoLast != null" >
                CRD_NO_LAST,
            </if>
            <if test="idTyp != null" >
                ID_TYP,
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC,
            </if>
            <if test="bnkPsnFlg != null" >
                BNK_PSN_FLG,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="autoPayFlg != null" >
                AUTO_PAY_FLG,
            </if>
            <if test="agrPayDt != null" >
                AGR_PAY_DT,
            </if>
            <if test="rfdTime != null" >
                RFD_TIME,
            </if>
            <if test="rfdFailCount != null" >
                RFD_FAIL_COUNT,
            </if>
            <if test="orgRmk != null" >
                ORG_RMK,
            </if>
            <if test="apprSts != null" >
                APPR_STS,
            </if>
            <if test="apprOprId != null" >
                APPR_OPR_ID,
            </if>
            <if test="apprDt != null" >
                APPR_DT,
            </if>
            <if test="apprTm != null" >
                APPR_TM,
            </if>
            <if test="apprRefuseRsn != null" >
                APPR_REFUSE_RSN,
            </if>
            <if test="ntfSts != null" >
                NTF_STS,
            </if>
            <if test="ntfDt != null" >
                NTF_DT,
            </if>
            <if test="ntfTm != null" >
                NTF_TM,
            </if>
            <if test="ntfRspCd != null" >
                NTF_RSP_CD,
            </if>
            <if test="ntfRspMsg != null" >
                NTF_RSP_MSG,
            </if>
            <if test="reqOrdNo != null" >
                REQ_ORD_NO,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="reqOrdDt != null" >
                REQ_ORD_DT,
            </if>
            <if test="reqOrdTm != null" >
                REQ_ORD_TM,
            </if>
            <if test="innerFlag != null" >
                INNER_FLAG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="rfdOrdNo != null" >
                #{rfdOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                #{ordTm,jdbcType=TIME},
            </if>
            <if test="ordCcy != null" >
                #{ordCcy,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                #{ordSts,jdbcType=CHAR},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="ordSuccDt != null" >
                #{ordSuccDt,jdbcType=DATE},
            </if>
            <if test="ordSuccTm != null" >
                #{ordSuccTm,jdbcType=TIME},
            </if>
            <if test="userTyp != null" >
                #{userTyp,jdbcType=CHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdUsrNm != null" >
                #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="crdNoLast != null" >
                #{crdNoLast,jdbcType=CHAR},
            </if>
            <if test="idTyp != null" >
                #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="bnkPsnFlg != null" >
                #{bnkPsnFlg,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="autoPayFlg != null" >
                #{autoPayFlg,jdbcType=CHAR},
            </if>
            <if test="agrPayDt != null" >
                #{agrPayDt,jdbcType=DATE},
            </if>
            <if test="rfdTime != null" >
                #{rfdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="rfdFailCount != null" >
                #{rfdFailCount,jdbcType=INTEGER},
            </if>
            <if test="orgRmk != null" >
                #{orgRmk,jdbcType=VARCHAR},
            </if>
            <if test="apprSts != null" >
                #{apprSts,jdbcType=CHAR},
            </if>
            <if test="apprOprId != null" >
                #{apprOprId,jdbcType=VARCHAR},
            </if>
            <if test="apprDt != null" >
                #{apprDt,jdbcType=DATE},
            </if>
            <if test="apprTm != null" >
                #{apprTm,jdbcType=TIME},
            </if>
            <if test="apprRefuseRsn != null" >
                #{apprRefuseRsn,jdbcType=VARCHAR},
            </if>
            <if test="ntfSts != null" >
                #{ntfSts,jdbcType=CHAR},
            </if>
            <if test="ntfDt != null" >
                #{ntfDt,jdbcType=DATE},
            </if>
            <if test="ntfTm != null" >
                #{ntfTm,jdbcType=TIME},
            </if>
            <if test="ntfRspCd != null" >
                #{ntfRspCd,jdbcType=VARCHAR},
            </if>
            <if test="ntfRspMsg != null" >
                #{ntfRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdNo != null" >
                #{reqOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="reqOrdDt != null" >
                #{reqOrdDt,jdbcType=DATE},
            </if>
            <if test="reqOrdTm != null" >
                #{reqOrdTm,jdbcType=TIME},
            </if>
            <if test="innerFlag != null" >
                #{innerFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.RefundOrderDO" >
        update cpi_refund_order
        <set >
            <if test="ordDt != null" >
                ORD_DT = #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                ORD_TM = #{ordTm,jdbcType=TIME},
            </if>
            <if test="ordCcy != null" >
                ORD_CCY = #{ordCcy,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                ORD_AMT = #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                ORD_STS = #{ordSts,jdbcType=CHAR},
            </if>
            <if test="acDt != null" >
                AC_DT = #{acDt,jdbcType=DATE},
            </if>
            <if test="ordSuccDt != null" >
                ORD_SUCC_DT = #{ordSuccDt,jdbcType=DATE},
            </if>
            <if test="ordSuccTm != null" >
                ORD_SUCC_TM = #{ordSuccTm,jdbcType=TIME},
            </if>
            <if test="userTyp != null" >
                USER_TYP = #{userTyp,jdbcType=CHAR},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                MBL_NO = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM = #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="crdNoLast != null" >
                CRD_NO_LAST = #{crdNoLast,jdbcType=CHAR},
            </if>
            <if test="idTyp != null" >
                ID_TYP = #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC = #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="bnkPsnFlg != null" >
                BNK_PSN_FLG = #{bnkPsnFlg,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="autoPayFlg != null" >
                AUTO_PAY_FLG = #{autoPayFlg,jdbcType=CHAR},
            </if>
            <if test="agrPayDt != null" >
                AGR_PAY_DT = #{agrPayDt,jdbcType=DATE},
            </if>
            <if test="rfdTime != null" >
                RFD_TIME = #{rfdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="rfdFailCount != null" >
                RFD_FAIL_COUNT = #{rfdFailCount,jdbcType=INTEGER},
            </if>
            <if test="orgRmk != null" >
                ORG_RMK = #{orgRmk,jdbcType=VARCHAR},
            </if>
            <if test="apprSts != null" >
                APPR_STS = #{apprSts,jdbcType=CHAR},
            </if>
            <if test="apprOprId != null" >
                APPR_OPR_ID = #{apprOprId,jdbcType=VARCHAR},
            </if>
            <if test="apprDt != null" >
                APPR_DT = #{apprDt,jdbcType=DATE},
            </if>
            <if test="apprTm != null" >
                APPR_TM = #{apprTm,jdbcType=TIME},
            </if>
            <if test="apprRefuseRsn != null" >
                APPR_REFUSE_RSN = #{apprRefuseRsn,jdbcType=VARCHAR},
            </if>
            <if test="ntfSts != null" >
                NTF_STS = #{ntfSts,jdbcType=CHAR},
            </if>
            <if test="ntfDt != null" >
                NTF_DT = #{ntfDt,jdbcType=DATE},
            </if>
            <if test="ntfTm != null" >
                NTF_TM = #{ntfTm,jdbcType=TIME},
            </if>
            <if test="ntfRspCd != null" >
                NTF_RSP_CD = #{ntfRspCd,jdbcType=VARCHAR},
            </if>
            <if test="ntfRspMsg != null" >
                NTF_RSP_MSG = #{ntfRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdNo != null" >
                REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="reqOrdDt != null" >
                REQ_ORD_DT = #{reqOrdDt,jdbcType=DATE},
            </if>
            <if test="reqOrdTm != null" >
                REQ_ORD_TM = #{reqOrdTm,jdbcType=TIME},
            </if>
            <if test="innerFlag != null" >
                INNER_FLAG = #{innerFlag,jdbcType=CHAR},
            </if>
        </set>
        where RFD_ORD_NO = #{rfdOrdNo,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.hisun.lemon.cpi.entity.RefundOrderDO" >
        select
        <include refid="Base_Column_List" />
        from cpi_refund_order
        <where>
            <if test="ordDt != null" >
                and ORD_DT = #{ordDt,jdbcType=DATE}
            </if>
            <if test="ordTm != null" >
                and ORD_TM = #{ordTm,jdbcType=TIME}
            </if>
            <if test="ordCcy != null" >
                and ORD_CCY = #{ordCcy,jdbcType=CHAR}
            </if>
            <if test="ordAmt != null" >
                and ORD_AMT = #{ordAmt,jdbcType=DECIMAL}
            </if>
            <if test="ordSts != null" >
                and ORD_STS = #{ordSts,jdbcType=CHAR}
            </if>
            <if test="ordSuccDt != null" >
                and ORD_SUCC_DT = #{ordSuccDt,jdbcType=DATE}
            </if>
            <if test="ordSuccTm != null" >
                and ORD_SUCC_TM = #{ordSuccTm,jdbcType=TIME}
            </if>
            <if test="userTyp != null" >
                and USER_TYP = #{userTyp,jdbcType=CHAR}
            </if>
            <if test="userId != null" >
                and USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="mblNo != null" >
                and MBL_NO = #{mblNo,jdbcType=VARCHAR}
            </if>
            <if test="crdCorpOrg != null" >
                and CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR}
            </if>
            <if test="crdAcTyp != null" >
                and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
            </if>
            <if test="crdNoEnc != null" >
                and CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR}
            </if>
            <if test="crdUsrNm != null" >
                and CRD_USR_NM = #{crdUsrNm,jdbcType=VARCHAR}
            </if>
            <if test="crdNoLast != null" >
                and CRD_NO_LAST = #{crdNoLast,jdbcType=CHAR}
            </if>
            <if test="idTyp != null" >
                and ID_TYP = #{idTyp,jdbcType=CHAR}
            </if>
            <if test="idNoEnc != null" >
                and ID_NO_ENC = #{idNoEnc,jdbcType=VARCHAR}
            </if>
            <if test="bnkPsnFlg != null" >
                and BNK_PSN_FLG = #{bnkPsnFlg,jdbcType=CHAR}
            </if>
            <if test="rutCorpOrg != null" >
                and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
            </if>
            <if test="corpBusTyp != null" >
                and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
            </if>
            <if test="corpBusSubTyp != null" >
                and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
            </if>
            <if test="autoPayFlg != null" >
                and AUTO_PAY_FLG = #{autoPayFlg,jdbcType=CHAR}
            </if>
            <if test="agrPayDt != null" >
                and AGR_PAY_DT = #{agrPayDt,jdbcType=DATE}
            </if>
            <if test="rfdTime != null" >
                and RFD_TIME = #{rfdTime,jdbcType=TIMESTAMP}
            </if>
            <if test="rfdFailCount != null" >
                and RFD_FAIL_COUNT = #{rfdFailCount,jdbcType=INTEGER}
            </if>
            <if test="orgRmk != null" >
                and ORG_RMK = #{orgRmk,jdbcType=VARCHAR}
            </if>
            <if test="apprSts != null" >
                and APPR_STS = #{apprSts,jdbcType=CHAR}
            </if>
            <if test="apprOprId != null" >
                and APPR_OPR_ID = #{apprOprId,jdbcType=VARCHAR}
            </if>
            <if test="apprDt != null" >
                and APPR_DT = #{apprDt,jdbcType=DATE}
            </if>
            <if test="apprTm != null" >
                and APPR_TM = #{apprTm,jdbcType=TIME}
            </if>
            <if test="apprRefuseRsn != null" >
                and APPR_REFUSE_RSN = #{apprRefuseRsn,jdbcType=VARCHAR}
            </if>
            <if test="ntfSts != null" >
                and NTF_STS = #{ntfSts,jdbcType=CHAR}
            </if>
            <if test="ntfDt != null" >
                and NTF_DT = #{ntfDt,jdbcType=DATE}
            </if>
            <if test="ntfTm != null" >
                and NTF_TM = #{ntfTm,jdbcType=TIME}
            </if>
            <if test="ntfRspCd != null" >
                and NTF_RSP_CD = #{ntfRspCd,jdbcType=VARCHAR}
            </if>
            <if test="ntfRspMsg != null" >
                and NTF_RSP_MSG = #{ntfRspMsg,jdbcType=VARCHAR}
            </if>
            <if test="reqOrdNo != null" >
                and REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryAwaitingPayment" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_refund_order
        where ORD_STS = 'W1'
          and AGR_PAY_DT &lt;= #{refundOrderDO.agrPayDt,jdbcType=DATE}
          and RFD_TIME &lt;= #{refundOrderDO.rfdTime,jdbcType=TIMESTAMP}
          and RFD_FAIL_COUNT &lt; #{refundOrderDO.rfdFailCount,jdbcType=INTEGER}
          limit #{offset,jdbcType=INTEGER},#{number,jdbcType=INTEGER}
    </select>

    <!--获取银行对账明细 List（根据会计日期查询有问题-->
    <select id="getRefundOrderList" resultMap="BaseResultMap" >
        select RFD_ORD_NO,ORD_AMT,ORD_STS,REQ_ORD_NO
        from cpi_refund_order
        where AC_DT = #{ordDt,jdbcType=DATE}
        and INNER_FLAG = '0'
        ORDER BY RFD_ORD_NO ASC
        limit #{beginNum,jdbcType=INTEGER}, #{countNum,jdbcType=INTEGER}
    </select>

    <!--分页查询待通知的订单 -->
    <select id="getRefundOrderToNotify" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_refund_order
        where NTF_STS = 'W'
        and ORD_STS = 'S1'
        order by CREATE_TIME
        limit #{beginNum,jdbcType=INTEGER}, #{maxQueryNum,jdbcType=INTEGER}
    </select>


</mapper>