<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.alipay.IAlipaySettleStatisDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.alipay.AlipaySettleStatisDO" >
        <id column="settle_batch_no" property="settleBatchNo" jdbcType="VARCHAR" />
        <result column="update_date" property="updateDate" jdbcType="DATE" />
        <result column="update_time" property="updateTime" jdbcType="TIME" />
        <result column="settle_date" property="settleDate" jdbcType="VARCHAR" />
        <result column="settle_datetime" property="settleDatetime" jdbcType="VARCHAR" />
        <result column="settle_amount" property="settleAmount" jdbcType="DECIMAL" />
        <result column="rmb_settle_amount" property="rmbSettleAmount" jdbcType="DECIMAL" />
        <result column="unsettle_fee" property="unsettleFee" jdbcType="DECIMAL" />
        <result column="currency_type" property="currencyType" jdbcType="VARCHAR" />
        <result column="pay_fee" property="payFee" jdbcType="DECIMAL" />
        <result column="refund_fee" property="refundFee" jdbcType="DECIMAL" />
        <result column="pay_net_fee" property="payNetFee" jdbcType="DECIMAL" />
        <result column="poundage_fee" property="poundageFee" jdbcType="DECIMAL" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="partner" property="partner" jdbcType="VARCHAR" />
        <result column="mch_name" property="mchName" jdbcType="VARCHAR" />
        <result column="stl_flg" property="stlFlg" jdbcType="VARCHAR" />
        <result column="rut_corg" property="rutCorg" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        settle_batch_no, update_date, update_time, settle_date, settle_datetime, settle_amount, 
        rmb_settle_amount, unsettle_fee, currency_type, pay_fee, refund_fee, pay_net_fee, 
        poundage_fee, create_time, modify_time, tm_smp, partner, mch_name, stl_flg, rut_corg
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_alipay_settle_statistics
        where settle_batch_no = #{settleBatchNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_alipay_settle_statistics
        where settle_batch_no = #{settleBatchNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.alipay.AlipaySettleStatisDO" >
        insert into cpi_alipay_settle_statistics
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="settleBatchNo != null" >
                settle_batch_no,
            </if>
            <if test="updateDate != null" >
                update_date,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="settleDate != null" >
                settle_date,
            </if>
            <if test="settleDatetime != null" >
                settle_datetime,
            </if>
            <if test="settleAmount != null" >
                settle_amount,
            </if>
            <if test="rmbSettleAmount != null" >
                rmb_settle_amount,
            </if>
            <if test="unsettleFee != null" >
                unsettle_fee,
            </if>
            <if test="currencyType != null" >
                currency_type,
            </if>
            <if test="payFee != null" >
                pay_fee,
            </if>
            <if test="refundFee != null" >
                refund_fee,
            </if>
            <if test="payNetFee != null" >
                pay_net_fee,
            </if>
            <if test="poundageFee != null" >
                poundage_fee,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
            <if test="partner != null" >
                partner,
            </if>
            <if test="mchName != null" >
                mch_name,
            </if>
            <if test="stlFlg != null" >
                stl_flg,
            </if>
            <if test="rutCorg != null" >
                rut_corg,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="settleBatchNo != null" >
                #{settleBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null" >
                #{updateDate,jdbcType=DATE},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIME},
            </if>
            <if test="settleDate != null" >
                #{settleDate,jdbcType=VARCHAR},
            </if>
            <if test="settleDatetime != null" >
                #{settleDatetime,jdbcType=VARCHAR},
            </if>
            <if test="settleAmount != null" >
                #{settleAmount,jdbcType=DECIMAL},
            </if>
            <if test="rmbSettleAmount != null" >
                #{rmbSettleAmount,jdbcType=DECIMAL},
            </if>
            <if test="unsettleFee != null" >
                #{unsettleFee,jdbcType=DECIMAL},
            </if>
            <if test="currencyType != null" >
                #{currencyType,jdbcType=VARCHAR},
            </if>
            <if test="payFee != null" >
                #{payFee,jdbcType=DECIMAL},
            </if>
            <if test="refundFee != null" >
                #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="payNetFee != null" >
                #{payNetFee,jdbcType=DECIMAL},
            </if>
            <if test="poundageFee != null" >
                #{poundageFee,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="partner != null" >
                #{partner,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null" >
                #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="stlFlg != null" >
                #{stlFlg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.alipay.AlipaySettleStatisDO" >
        update cpi_alipay_settle_statistics
        <set >
            <if test="updateDate != null" >
                update_date = #{updateDate,jdbcType=DATE},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIME},
            </if>
            <if test="settleDate != null" >
                settle_date = #{settleDate,jdbcType=VARCHAR},
            </if>
            <if test="settleDatetime != null" >
                settle_datetime = #{settleDatetime,jdbcType=VARCHAR},
            </if>
            <if test="settleAmount != null" >
                settle_amount = #{settleAmount,jdbcType=DECIMAL},
            </if>
            <if test="rmbSettleAmount != null" >
                rmb_settle_amount = #{rmbSettleAmount,jdbcType=DECIMAL},
            </if>
            <if test="unsettleFee != null" >
                unsettle_fee = #{unsettleFee,jdbcType=DECIMAL},
            </if>
            <if test="currencyType != null" >
                currency_type = #{currencyType,jdbcType=VARCHAR},
            </if>
            <if test="payFee != null" >
                pay_fee = #{payFee,jdbcType=DECIMAL},
            </if>
            <if test="refundFee != null" >
                refund_fee = #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="payNetFee != null" >
                pay_net_fee = #{payNetFee,jdbcType=DECIMAL},
            </if>
            <if test="poundageFee != null" >
                poundage_fee = #{poundageFee,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="partner != null" >
                partner = #{partner,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null" >
                mch_name = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="stlFlg != null" >
                stl_flg = #{stlFlg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                rut_corg = #{rutCorg,jdbcType=VARCHAR},
            </if>
        </set>
        where settle_batch_no = #{settleBatchNo,jdbcType=VARCHAR}
    </update>


    <select id="getBySettleControl" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cpi_alipay_settle_statistics
        WHERE settle_batch_no = #{settleBatchNo,jdbcType=VARCHAR}
    </select>
</mapper>