<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IRefundParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.RefundParamDO" >
        <id column="RFD_PAR_ID" property="rfdParId" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="IO_FLG" property="ioFlg" jdbcType="CHAR" />
        <result column="SAD_RFD_FLG" property="sadRfdFlg" jdbcType="CHAR" />
        <result column="MTS_RFD_FLG" property="mtsRfdFlg" jdbcType="CHAR" />
        <result column="AUTO_RFD_FLG" property="autoRfdFlg" jdbcType="CHAR" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        RFD_PAR_ID, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, IO_FLG, SAD_RFD_FLG, MTS_RFD_FLG, 
        AUTO_RFD_FLG, OPR_ID, RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_refund_param
        where RFD_PAR_ID = #{rfdParId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_refund_param
        where RFD_PAR_ID = #{rfdParId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.RefundParamDO" >
        insert into cpi_refund_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="rfdParId != null" >
                RFD_PAR_ID,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="ioFlg != null" >
                IO_FLG,
            </if>
            <if test="sadRfdFlg != null" >
                SAD_RFD_FLG,
            </if>
            <if test="mtsRfdFlg != null" >
                MTS_RFD_FLG,
            </if>
            <if test="autoRfdFlg != null" >
                AUTO_RFD_FLG,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="rfdParId != null" >
                #{rfdParId,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="ioFlg != null" >
                #{ioFlg,jdbcType=CHAR},
            </if>
            <if test="sadRfdFlg != null" >
                #{sadRfdFlg,jdbcType=CHAR},
            </if>
            <if test="mtsRfdFlg != null" >
                #{mtsRfdFlg,jdbcType=CHAR},
            </if>
            <if test="autoRfdFlg != null" >
                #{autoRfdFlg,jdbcType=CHAR},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.RefundParamDO" >
        update cpi_refund_param
        <set >
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="ioFlg != null" >
                IO_FLG = #{ioFlg,jdbcType=CHAR},
            </if>
            <if test="sadRfdFlg != null" >
                SAD_RFD_FLG = #{sadRfdFlg,jdbcType=CHAR},
            </if>
            <if test="mtsRfdFlg != null" >
                MTS_RFD_FLG = #{mtsRfdFlg,jdbcType=CHAR},
            </if>
            <if test="autoRfdFlg != null" >
                AUTO_RFD_FLG = #{autoRfdFlg,jdbcType=CHAR},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where RFD_PAR_ID = #{rfdParId,jdbcType=VARCHAR}
    </update>

    <select id="selectByUniqueKey" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_refund_param
        where RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
          and CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
          and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
    </select>
</mapper>