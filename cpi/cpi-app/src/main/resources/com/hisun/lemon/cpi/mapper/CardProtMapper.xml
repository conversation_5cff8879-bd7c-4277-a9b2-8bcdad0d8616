<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ICardProtDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.CardProtDO" >
        <id column="AGR_NO" property="agrNo" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="AGR_EFF_FLG" property="agrEffFlg" jdbcType="CHAR" />
        <result column="SIGN_DT" property="signDt" jdbcType="DATE" />
        <result column="SIGN_TM" property="signTm" jdbcType="TIME" />
        <result column="UNSIGN_DT" property="unsignDt" jdbcType="DATE" />
        <result column="UNSIGN_TM" property="unsignTm" jdbcType="TIME" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="BNK_PSN_FLG" property="bnkPsnFlg" jdbcType="CHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="SIGN_AGRNO" property="signAgrno" jdbcType="VARCHAR" />
        <result column="AGR_DIRECT" property="agrDirect" jdbcType="CHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_NO_ENC" property="crdNoEnc" jdbcType="VARCHAR" />
        <result column="CRD_NO_LAST" property="crdNoLast" jdbcType="VARCHAR" />
        <result column="CRD_USR_NM" property="crdUsrNm" jdbcType="VARCHAR" />
        <result column="ID_TYP" property="idTyp" jdbcType="CHAR" />
        <result column="ID_NO_ENC" property="idNoEnc" jdbcType="VARCHAR" />
        <result column="CRD_CVV2_ENC" property="crdCvv2Enc" jdbcType="VARCHAR" />
        <result column="CRD_EXP_DT_ENC" property="crdExpDtEnc" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        AGR_NO, CORP_BUS_TYP, CORP_BUS_SUB_TYP, AGR_EFF_FLG, SIGN_DT, SIGN_TM, UNSIGN_DT, 
        UNSIGN_TM, USER_ID, MBL_NO, BNK_PSN_FLG, CRD_CORP_ORG, RUT_CORP_ORG, SIGN_AGRNO, 
        AGR_DIRECT, CRD_AC_TYP, CRD_NO_ENC, CRD_NO_LAST, CRD_USR_NM, ID_TYP, ID_NO_ENC, CRD_CVV2_ENC, 
        CRD_EXP_DT_ENC, RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_card_prot
        where AGR_NO = #{agrNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_card_prot
        where AGR_NO = #{agrNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.CardProtDO" >
        insert into cpi_card_prot
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="agrNo != null" >
                AGR_NO,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="agrEffFlg != null" >
                AGR_EFF_FLG,
            </if>
            <if test="signDt != null" >
                SIGN_DT,
            </if>
            <if test="signTm != null" >
                SIGN_TM,
            </if>
            <if test="unsignDt != null" >
                UNSIGN_DT,
            </if>
            <if test="unsignTm != null" >
                UNSIGN_TM,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="bnkPsnFlg != null" >
                BNK_PSN_FLG,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="signAgrno != null" >
                SIGN_AGRNO,
            </if>
            <if test="agrDirect != null" >
                AGR_DIRECT,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC,
            </if>
            <if test="crdNoLast != null" >
                CRD_NO_LAST,
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM,
            </if>
            <if test="idTyp != null" >
                ID_TYP,
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC,
            </if>
            <if test="crdCvv2Enc != null" >
                CRD_CVV2_ENC,
            </if>
            <if test="crdExpDtEnc != null" >
                CRD_EXP_DT_ENC,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="agrNo != null" >
                #{agrNo,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="agrEffFlg != null" >
                #{agrEffFlg,jdbcType=CHAR},
            </if>
            <if test="signDt != null" >
                #{signDt,jdbcType=DATE},
            </if>
            <if test="signTm != null" >
                #{signTm,jdbcType=TIME},
            </if>
            <if test="unsignDt != null" >
                #{unsignDt,jdbcType=DATE},
            </if>
            <if test="unsignTm != null" >
                #{unsignTm,jdbcType=TIME},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="bnkPsnFlg != null" >
                #{bnkPsnFlg,jdbcType=CHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="signAgrno != null" >
                #{signAgrno,jdbcType=VARCHAR},
            </if>
            <if test="agrDirect != null" >
                #{agrDirect,jdbcType=CHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdNoLast != null" >
                #{crdNoLast,jdbcType=VARCHAR},
            </if>
            <if test="crdUsrNm != null" >
                #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="idTyp != null" >
                #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdCvv2Enc != null" >
                #{crdCvv2Enc,jdbcType=VARCHAR},
            </if>
            <if test="crdExpDtEnc != null" >
                #{crdExpDtEnc,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.CardProtDO" >
        update cpi_card_prot
        <set >
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="agrEffFlg != null" >
                AGR_EFF_FLG = #{agrEffFlg,jdbcType=CHAR},
            </if>
            <if test="signDt != null" >
                SIGN_DT = #{signDt,jdbcType=DATE},
            </if>
            <if test="signTm != null" >
                SIGN_TM = #{signTm,jdbcType=TIME},
            </if>
            <if test="unsignDt != null" >
                UNSIGN_DT = #{unsignDt,jdbcType=DATE},
            </if>
            <if test="unsignTm != null" >
                UNSIGN_TM = #{unsignTm,jdbcType=TIME},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                MBL_NO = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="bnkPsnFlg != null" >
                BNK_PSN_FLG = #{bnkPsnFlg,jdbcType=CHAR},
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="signAgrno != null" >
                SIGN_AGRNO = #{signAgrno,jdbcType=VARCHAR},
            </if>
            <if test="agrDirect != null" >
                AGR_DIRECT = #{agrDirect,jdbcType=CHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdNoLast != null" >
                CRD_NO_LAST = #{crdNoLast,jdbcType=VARCHAR},
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM = #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="idTyp != null" >
                ID_TYP = #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC = #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdCvv2Enc != null" >
                CRD_CVV2_ENC = #{crdCvv2Enc,jdbcType=VARCHAR},
            </if>
            <if test="crdExpDtEnc != null" >
                CRD_EXP_DT_ENC = #{crdExpDtEnc,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where AGR_NO = #{agrNo,jdbcType=VARCHAR}
    </update>

    <select id="selectByCorpBusTyp" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM cpi_card_prot
        WHERE AGR_EFF_FLG = 'Y'
        AND USER_ID = #{userId,jdbcType=VARCHAR}
        <if test="corpBusTyp != null" >
            and CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR}
        </if>
        <if test="corpBusSubTyp != null" >
            and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR}
        </if>
        <if test="crdAcTyp != null" >
            and CRD_AC_TYP = #{crdAcTyp,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectBindCard" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM cpi_card_prot
        WHERE AGR_EFF_FLG = 'Y'
          AND USER_ID = #{userId,jdbcType=VARCHAR}
          and CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR}
    </select>
</mapper>