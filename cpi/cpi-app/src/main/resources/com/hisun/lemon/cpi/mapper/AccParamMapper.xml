<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IAccParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.AccParamDO" >
        <id column="CHK_PM_ID" property="chkPmId" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="EFF_FLG" property="effFlg" jdbcType="CHAR" />
        <result column="CHK_DO_FLG" property="chkDoFlg" jdbcType="CHAR" />
        <result column="BAT_CHK_FLG" property="batChkFlg" jdbcType="CHAR" />
        <result column="OWN_CHK_FLG" property="ownChkFlg" jdbcType="CHAR" />
        <result column="CHK_BEG_TM" property="chkBegTm" jdbcType="TIME" />
        <result column="CHK_END_TM" property="chkEndTm" jdbcType="TIME" />
        <result column="SPL_ABLE_FLG" property="splAbleFlg" jdbcType="CHAR" />
        <result column="CAN_ABLE_FLG" property="canAbleFlg" jdbcType="CHAR" />
        <result column="AU_SK_FLG" property="auSkFlg" jdbcType="CHAR" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        CHK_PM_ID, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, EFF_FLG, CHK_DO_FLG, OWN_CHK_FLG, BAT_CHK_FLG,
        CHK_BEG_TM, CHK_END_TM, SPL_ABLE_FLG, CAN_ABLE_FLG, AU_SK_FLG, OPR_ID, CREATE_TIME, 
        MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_acc_param
        where CHK_PM_ID = #{chkPmId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_acc_param
        where CHK_PM_ID = #{chkPmId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.AccParamDO" >
        insert into cpi_acc_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="chkPmId != null" >
                CHK_PM_ID,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="effFlg != null" >
                EFF_FLG,
            </if>
            <if test="chkDoFlg != null" >
                CHK_DO_FLG,
            </if>
            <if test="batChkFlg != null" >
                BAT_CHK_FLG,
            </if>
            <if test="ownChkFlg != null" >
                OWN_CHK_FLG,
            </if>
            <if test="chkBegTm != null" >
                CHK_BEG_TM,
            </if>
            <if test="chkEndTm != null" >
                CHK_END_TM,
            </if>
            <if test="splAbleFlg != null" >
                SPL_ABLE_FLG,
            </if>
            <if test="canAbleFlg != null" >
                CAN_ABLE_FLG,
            </if>
            <if test="auSkFlg != null" >
                AU_SK_FLG,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="chkPmId != null" >
                #{chkPmId,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="effFlg != null" >
                #{effFlg,jdbcType=CHAR},
            </if>
            <if test="chkDoFlg != null" >
                #{chkDoFlg,jdbcType=CHAR},
            </if>
            <if test="batChkFlg != null" >
                #{batChkFlg,jdbcType=CHAR},
            </if>
            <if test="ownChkFlg != null" >
                #{ownChkFlg,jdbcType=CHAR},
            </if>
            <if test="chkBegTm != null" >
                #{chkBegTm,jdbcType=TIME},
            </if>
            <if test="chkEndTm != null" >
                #{chkEndTm,jdbcType=TIME},
            </if>
            <if test="splAbleFlg != null" >
                #{splAbleFlg,jdbcType=CHAR},
            </if>
            <if test="canAbleFlg != null" >
                #{canAbleFlg,jdbcType=CHAR},
            </if>
            <if test="auSkFlg != null" >
                #{auSkFlg,jdbcType=CHAR},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.AccParamDO" >
        update cpi_acc_param
        <set >
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="effFlg != null" >
                EFF_FLG = #{effFlg,jdbcType=CHAR},
            </if>
            <if test="chkDoFlg != null" >
                CHK_DO_FLG = #{chkDoFlg,jdbcType=CHAR},
            </if>
            <if test="batChkFlg != null" >
                BAT_CHK_FLG = #{batChkFlg,jdbcType=CHAR},
            </if>
            <if test="ownChkFlg != null" >
                OWN_CHK_FLG = #{ownChkFlg,jdbcType=CHAR},
            </if>
            <if test="chkBegTm != null" >
                CHK_BEG_TM = #{chkBegTm,jdbcType=TIME},
            </if>
            <if test="chkEndTm != null" >
                CHK_END_TM = #{chkEndTm,jdbcType=TIME},
            </if>
            <if test="splAbleFlg != null" >
                SPL_ABLE_FLG = #{splAbleFlg,jdbcType=CHAR},
            </if>
            <if test="canAbleFlg != null" >
                CAN_ABLE_FLG = #{canAbleFlg,jdbcType=CHAR},
            </if>
            <if test="auSkFlg != null" >
                AU_SK_FLG = #{auSkFlg,jdbcType=CHAR},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where CHK_PM_ID = #{chkPmId,jdbcType=VARCHAR}
    </update>

    <!-- 查询生效的对账参数信息 -->
    <select id="queryEffAccParamList" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_acc_param
        where EFF_FLG = 'Y'
    </select>

</mapper>