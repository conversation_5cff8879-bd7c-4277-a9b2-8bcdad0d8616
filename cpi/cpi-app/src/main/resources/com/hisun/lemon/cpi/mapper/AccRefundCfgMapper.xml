<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IAccRefundCfgDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.AccRefundCfgDO" >
        <id column="ACC_CFG_ID" property="accCfgId" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="CHK_CLAZZ" property="chkClazz" jdbcType="VARCHAR" />
        <result column="GET_FILE_METHOD" property="getFileMethod" jdbcType="VARCHAR" />
        <result column="IMPORT_FIELDS" property="importFields" jdbcType="VARCHAR" />
        <result column="IMPORT_CLASS" property="importClass" jdbcType="VARCHAR" />
        <result column="IMPORT_METHOD" property="importMethod" jdbcType="VARCHAR" />
        <result column="SPLIT_SIGN" property="splitSign" jdbcType="VARCHAR" />
        <result column="CHK_FILE_PATH" property="chkFilePath" jdbcType="VARCHAR" />
        <result column="CONTINUE_NUM" property="continueNum" jdbcType="INTEGER" />
        <result column="SELECT_DETAIL_METHOD" property="selectDetailMethod" jdbcType="VARCHAR" />
        <result column="SUCCESS_FLAG" property="successFlag" jdbcType="VARCHAR" />
        <result column="CHECK_KEY_FILED" property="checkKeyFiled" jdbcType="VARCHAR" />
        <result column="CHECK_KEY_BAK_FILED" property="checkKeyBakFiled" jdbcType="VARCHAR" />
        <result column="CHECK_AMT_FILED" property="checkAmtFiled" jdbcType="VARCHAR" />
        <result column="TX_STS_FILED" property="txStsFiled" jdbcType="VARCHAR" />
        <result column="QUERY_NUM" property="queryNum" jdbcType="INTEGER" />
        <result column="UPDATE_METHOD" property="updateMethod" jdbcType="VARCHAR" />
        <result column="UPLOAD_IP" property="uploadIp" jdbcType="VARCHAR" />
        <result column="UPLOAD_PORT" property="uploadPort" jdbcType="VARCHAR" />
        <result column="UPLOAD_PATH" property="uploadPath" jdbcType="VARCHAR" />
        <result column="UPLOAD_NAME" property="uploadName" jdbcType="VARCHAR" />
        <result column="UPLOAD_PWD" property="uploadPwd" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ACC_CFG_ID, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, CHK_CLAZZ, GET_FILE_METHOD, 
        IMPORT_FIELDS, IMPORT_CLASS, IMPORT_METHOD, SPLIT_SIGN, CHK_FILE_PATH, CONTINUE_NUM, 
        SELECT_DETAIL_METHOD, SUCCESS_FLAG, CHECK_KEY_FILED, CHECK_KEY_BAK_FILED, CHECK_AMT_FILED, 
        TX_STS_FILED, QUERY_NUM, UPDATE_METHOD, UPLOAD_IP, UPLOAD_PORT, UPLOAD_PATH, UPLOAD_NAME, 
        UPLOAD_PWD, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_acc_refund_cfg
        where ACC_CFG_ID = #{accCfgId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_acc_refund_cfg
        where ACC_CFG_ID = #{accCfgId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.AccRefundCfgDO" >
        insert into cpi_acc_refund_cfg
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="accCfgId != null" >
                ACC_CFG_ID,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="chkClazz != null" >
                CHK_CLAZZ,
            </if>
            <if test="getFileMethod != null" >
                GET_FILE_METHOD,
            </if>
            <if test="importFields != null" >
                IMPORT_FIELDS,
            </if>
            <if test="importClass != null" >
                IMPORT_CLASS,
            </if>
            <if test="importMethod != null" >
                IMPORT_METHOD,
            </if>
            <if test="splitSign != null" >
                SPLIT_SIGN,
            </if>
            <if test="chkFilePath != null" >
                CHK_FILE_PATH,
            </if>
            <if test="continueNum != null" >
                CONTINUE_NUM,
            </if>
            <if test="selectDetailMethod != null" >
                SELECT_DETAIL_METHOD,
            </if>
            <if test="successFlag != null" >
                SUCCESS_FLAG,
            </if>
            <if test="checkKeyFiled != null" >
                CHECK_KEY_FILED,
            </if>
            <if test="checkKeyBakFiled != null" >
                CHECK_KEY_BAK_FILED,
            </if>
            <if test="checkAmtFiled != null" >
                CHECK_AMT_FILED,
            </if>
            <if test="txStsFiled != null" >
                TX_STS_FILED,
            </if>
            <if test="queryNum != null" >
                QUERY_NUM,
            </if>
            <if test="updateMethod != null" >
                UPDATE_METHOD,
            </if>
            <if test="uploadIp != null" >
                UPLOAD_IP,
            </if>
            <if test="uploadPort != null" >
                UPLOAD_PORT,
            </if>
            <if test="uploadPath != null" >
                UPLOAD_PATH,
            </if>
            <if test="uploadName != null" >
                UPLOAD_NAME,
            </if>
            <if test="uploadPwd != null" >
                UPLOAD_PWD,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="accCfgId != null" >
                #{accCfgId,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="chkClazz != null" >
                #{chkClazz,jdbcType=VARCHAR},
            </if>
            <if test="getFileMethod != null" >
                #{getFileMethod,jdbcType=VARCHAR},
            </if>
            <if test="importFields != null" >
                #{importFields,jdbcType=VARCHAR},
            </if>
            <if test="importClass != null" >
                #{importClass,jdbcType=VARCHAR},
            </if>
            <if test="importMethod != null" >
                #{importMethod,jdbcType=VARCHAR},
            </if>
            <if test="splitSign != null" >
                #{splitSign,jdbcType=VARCHAR},
            </if>
            <if test="chkFilePath != null" >
                #{chkFilePath,jdbcType=VARCHAR},
            </if>
            <if test="continueNum != null" >
                #{continueNum,jdbcType=INTEGER},
            </if>
            <if test="selectDetailMethod != null" >
                #{selectDetailMethod,jdbcType=VARCHAR},
            </if>
            <if test="successFlag != null" >
                #{successFlag,jdbcType=VARCHAR},
            </if>
            <if test="checkKeyFiled != null" >
                #{checkKeyFiled,jdbcType=VARCHAR},
            </if>
            <if test="checkKeyBakFiled != null" >
                #{checkKeyBakFiled,jdbcType=VARCHAR},
            </if>
            <if test="checkAmtFiled != null" >
                #{checkAmtFiled,jdbcType=VARCHAR},
            </if>
            <if test="txStsFiled != null" >
                #{txStsFiled,jdbcType=VARCHAR},
            </if>
            <if test="queryNum != null" >
                #{queryNum,jdbcType=INTEGER},
            </if>
            <if test="updateMethod != null" >
                #{updateMethod,jdbcType=VARCHAR},
            </if>
            <if test="uploadIp != null" >
                #{uploadIp,jdbcType=VARCHAR},
            </if>
            <if test="uploadPort != null" >
                #{uploadPort,jdbcType=VARCHAR},
            </if>
            <if test="uploadPath != null" >
                #{uploadPath,jdbcType=VARCHAR},
            </if>
            <if test="uploadName != null" >
                #{uploadName,jdbcType=VARCHAR},
            </if>
            <if test="uploadPwd != null" >
                #{uploadPwd,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.AccRefundCfgDO" >
        update cpi_acc_refund_cfg
        <set >
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="chkClazz != null" >
                CHK_CLAZZ = #{chkClazz,jdbcType=VARCHAR},
            </if>
            <if test="getFileMethod != null" >
                GET_FILE_METHOD = #{getFileMethod,jdbcType=VARCHAR},
            </if>
            <if test="importFields != null" >
                IMPORT_FIELDS = #{importFields,jdbcType=VARCHAR},
            </if>
            <if test="importClass != null" >
                IMPORT_CLASS = #{importClass,jdbcType=VARCHAR},
            </if>
            <if test="importMethod != null" >
                IMPORT_METHOD = #{importMethod,jdbcType=VARCHAR},
            </if>
            <if test="splitSign != null" >
                SPLIT_SIGN = #{splitSign,jdbcType=VARCHAR},
            </if>
            <if test="chkFilePath != null" >
                CHK_FILE_PATH = #{chkFilePath,jdbcType=VARCHAR},
            </if>
            <if test="continueNum != null" >
                CONTINUE_NUM = #{continueNum,jdbcType=INTEGER},
            </if>
            <if test="selectDetailMethod != null" >
                SELECT_DETAIL_METHOD = #{selectDetailMethod,jdbcType=VARCHAR},
            </if>
            <if test="successFlag != null" >
                SUCCESS_FLAG = #{successFlag,jdbcType=VARCHAR},
            </if>
            <if test="checkKeyFiled != null" >
                CHECK_KEY_FILED = #{checkKeyFiled,jdbcType=VARCHAR},
            </if>
            <if test="checkKeyBakFiled != null" >
                CHECK_KEY_BAK_FILED = #{checkKeyBakFiled,jdbcType=VARCHAR},
            </if>
            <if test="checkAmtFiled != null" >
                CHECK_AMT_FILED = #{checkAmtFiled,jdbcType=VARCHAR},
            </if>
            <if test="txStsFiled != null" >
                TX_STS_FILED = #{txStsFiled,jdbcType=VARCHAR},
            </if>
            <if test="queryNum != null" >
                QUERY_NUM = #{queryNum,jdbcType=INTEGER},
            </if>
            <if test="updateMethod != null" >
                UPDATE_METHOD = #{updateMethod,jdbcType=VARCHAR},
            </if>
            <if test="uploadIp != null" >
                UPLOAD_IP = #{uploadIp,jdbcType=VARCHAR},
            </if>
            <if test="uploadPort != null" >
                UPLOAD_PORT = #{uploadPort,jdbcType=VARCHAR},
            </if>
            <if test="uploadPath != null" >
                UPLOAD_PATH = #{uploadPath,jdbcType=VARCHAR},
            </if>
            <if test="uploadName != null" >
                UPLOAD_NAME = #{uploadName,jdbcType=VARCHAR},
            </if>
            <if test="uploadPwd != null" >
                UPLOAD_PWD = #{uploadPwd,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ACC_CFG_ID = #{accCfgId,jdbcType=VARCHAR}
    </update>

    <!--查询银行对账配置信息-->
    <select id="getAccCfgDO" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_acc_refund_cfg
        where RUT_CORG = #{rutCorg,jdbcType=VARCHAR}
        and CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR}
        and CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR}
    </select>
</mapper>