<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ISubMercListDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.SubMercListDO" >
        <id column="sub_mch_id" property="subMchId" jdbcType="VARCHAR" />
        <result column="app_id" property="appId" jdbcType="VARCHAR" />
        <result column="mch_id" property="mchId" jdbcType="VARCHAR" />
        <result column="merc_nm" property="mercNm" jdbcType="VARCHAR" />
        <result column="merc_snm" property="mercSnm" jdbcType="VARCHAR" />
        <result column="office_tel" property="officeTel" jdbcType="VARCHAR" />
        <result column="merc_rmk" property="mercRmk" jdbcType="VARCHAR" />
        <result column="merc_website" property="mercWebsite" jdbcType="VARCHAR" />
        <result column="contract_nm" property="contractNm" jdbcType="VARCHAR" />
        <result column="contract_tel" property="contractTel" jdbcType="VARCHAR" />
        <result column="contract_website" property="contractWebsite" jdbcType="VARCHAR" />
        <result column="MERC_INTRODUCTION" property="mercIntroduction" jdbcType="VARCHAR" />
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        sub_mch_id, app_id, mch_id, merc_nm, merc_snm, office_tel, merc_rmk, merc_website, 
        contract_nm, contract_tel, contract_website, merc_introduction, business_type, create_time, modify_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_sub_merc_list
        where sub_mch_id = #{subMchId,jdbcType=VARCHAR}
    </select>

    <select id="getSubMercList" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpi_sub_merc_list
        where 1=1
        <if test="subMchId != null and subMchId != ''">
           and sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        </if>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_sub_merc_list
        where sub_mch_id = #{subMchId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.SubMercListDO" >
        insert into cpi_sub_merc_list
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="subMchId != null" >
                sub_mch_id,
            </if>
            <if test="appId != null" >
                app_id,
            </if>
            <if test="mchId != null" >
                mch_id,
            </if>
            <if test="mercNm != null" >
                merc_nm,
            </if>
            <if test="mercSnm != null" >
                merc_snm,
            </if>
            <if test="officeTel != null" >
                office_tel,
            </if>
            <if test="mercRmk != null" >
                merc_rmk,
            </if>
            <if test="mercWebsite != null" >
                merc_website,
            </if>
            <if test="contractNm != null" >
                contract_nm,
            </if>
            <if test="contractTel != null" >
                contract_tel,
            </if>
            <if test="contractWebsite != null" >
                contract_website,
            </if>
            <if test="mercIntroduction != null" >
                merc_introduction,
            </if>
            <if test="businessType != null" >
                business_type,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="subMchId != null" >
                #{subMchId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null" >
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="mchId != null" >
                #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="mercNm != null" >
                #{mercNm,jdbcType=VARCHAR},
            </if>
            <if test="mercSnm != null" >
                #{mercSnm,jdbcType=VARCHAR},
            </if>
            <if test="officeTel != null" >
                #{officeTel,jdbcType=VARCHAR},
            </if>
            <if test="mercRmk != null" >
                #{mercRmk,jdbcType=VARCHAR},
            </if>
            <if test="mercWebsite != null" >
                #{mercWebsite,jdbcType=VARCHAR},
            </if>
            <if test="contractNm != null" >
                #{contractNm,jdbcType=VARCHAR},
            </if>
            <if test="contractTel != null" >
                #{contractTel,jdbcType=VARCHAR},
            </if>
            <if test="contractWebsite != null" >
                #{contractWebsite,jdbcType=VARCHAR},
            </if>
            <if test="mercIntroduction != null" >
                #{mercIntroduction,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.SubMercListDO" >
        update cpi_sub_merc_list
        <set >
            <if test="appId != null" >
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="mchId != null" >
                mch_id = #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="mercNm != null" >
                merc_nm = #{mercNm,jdbcType=VARCHAR},
            </if>
            <if test="mercSnm != null" >
                merc_snm = #{mercSnm,jdbcType=VARCHAR},
            </if>
            <if test="officeTel != null" >
                office_tel = #{officeTel,jdbcType=VARCHAR},
            </if>
            <if test="mercRmk != null" >
                merc_rmk = #{mercRmk,jdbcType=VARCHAR},
            </if>
            <if test="mercWebsite != null" >
                merc_website = #{mercWebsite,jdbcType=VARCHAR},
            </if>
            <if test="contractNm != null" >
                contract_nm = #{contractNm,jdbcType=VARCHAR},
            </if>
            <if test="contractTel != null" >
                contract_tel = #{contractTel,jdbcType=VARCHAR},
            </if>
            <if test="contractWebsite != null" >
                contract_website = #{contractWebsite,jdbcType=VARCHAR},
            </if>
            <if test="mercIntroduction != null" >
                merc_introduction = #{mercIntroduction,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                business_type = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where sub_mch_id = #{subMchId,jdbcType=VARCHAR}
    </update>
</mapper>