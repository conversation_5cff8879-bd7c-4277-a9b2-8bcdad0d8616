<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IRouteDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.RouteDO" >
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="INTEGER" />
        <result column="CORP_ORG_ID" property="corpOrgId" jdbcType="VARCHAR" />
        <result column="CORP_ORG_NM" property="corpOrgNm" jdbcType="VARCHAR" />
        <result column="CORP_ORG_SNM" property="corpOrgSnm" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="VARCHAR" />
        <result column="CORP_ACC_NM" property="corpAccNm" jdbcType="VARCHAR" />
        <result column="CORP_ACC_NO" property="corpAccNo" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
    </resultMap>

    <!--根据业务类型、业务子类型，查询生效的合作资金机构信息-->
    <select id="queryRouteList" resultMap="BaseResultMap">
        SELECT
        a.CRD_CORP_ORG, a.CORP_BUS_TYP, a.CORP_BUS_SUB_TYP, a.RUT_CORP_ORG, a.CRD_AC_TYP
        FROM cpi_cop_biz_rut a, cpi_cop_agcy_biz b
        WHERE a.CRD_CORP_ORG =  b.CORP_ORG_ID
          and a.RUT_EFF_FLG = '1'
          and b.BUS_EFF_FLG = '1'
          and a.CORP_BUS_SUB_TYP = b.CORP_BUS_SUB_TYP
          and a.CORP_BUS_TYP = b.CORP_BUS_TYP
          and a.CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
          and a.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
          <if test="crdAcTyp != null" >
              and a.CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
          </if>
    </select>

    <!--根据金额、业务类型，查询匹配的路由信息-->
    <select id="queryRouteInfo" resultMap="BaseResultMap">
        SELECT
        a.CRD_CORP_ORG, a.CORP_BUS_TYP, a.CORP_BUS_SUB_TYP, a.RUT_CORP_ORG, a.CRD_AC_TYP
        FROM cpi_cop_biz_rut a, cpi_cop_agcy_biz b
        WHERE a.CRD_CORP_ORG =  b.CORP_ORG_ID
        and a.RUT_EFF_FLG = '1'
        and b.BUS_EFF_FLG = '1'
        and a.CORP_BUS_SUB_TYP = b.CORP_BUS_SUB_TYP
        and a.CORP_BUS_TYP = b.CORP_BUS_TYP
        and a.CORP_BUS_TYP = #{routeDO.corpBusTyp,jdbcType=CHAR}
        and a.CORP_BUS_SUB_TYP = #{routeDO.corpBusSubTyp,jdbcType=CHAR}
        <if test="routeDO.crdCorpOrg != null" >
            and a.CRD_CORP_ORG = #{routeDO.crdCorpOrg,jdbcType=VARCHAR}
        </if>
        <if test="routeDO.crdAcTyp != null" >
            and a.CRD_AC_TYP = #{routeDO.crdAcTyp,jdbcType=CHAR}
        </if>
        <if test="ordAmt != null" >
            and a.LOW_AMT &lt;= #{ordAmt,jdbcType=DECIMAL}
            and a.HIGH_AMT &gt; #{ordAmt,jdbcType=DECIMAL}
        </if>
        ORDER BY a.PRI_LVL DESC LIMIT 1
    </select>

    <!--查询生效的合作资金机构信息-->
    <select id="queryEffOrgInfo" resultMap="BaseResultMap">
        SELECT
          a.CRD_CORP_ORG, a.CORP_BUS_TYP, a.CORP_BUS_SUB_TYP, a.RUT_CORP_ORG, a.CRD_AC_TYP,
          c.CORP_ORG_ID, c.CORP_ORG_NM, c.CORP_ORG_SNM,c.CORP_ACC_NM,c.CORP_ACC_NO,c.RMK
          FROM cpi_cop_biz_rut a, cpi_cop_agcy_biz b, cpi_cop_agcy_info c
        WHERE a.CRD_CORP_ORG =  b.CORP_ORG_ID
          and a.CRD_CORP_ORG = c.CORP_ORG_ID
          and a.CORP_BUS_SUB_TYP = b.CORP_BUS_SUB_TYP
          and a.CORP_BUS_TYP = b.CORP_BUS_TYP
          and a.RUT_EFF_FLG = '1'
          and b.BUS_EFF_FLG = '1'
          and a.CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR}
          and a.CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR}
    </select>

</mapper>