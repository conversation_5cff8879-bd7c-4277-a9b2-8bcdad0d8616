<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ISecMercListCastDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.SecMercListCastDO" >
        <id column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="secondary_merchant_name" property="secondaryMerchantName" jdbcType="VARCHAR" />
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        <result column="acquirer_partner_ID" property="acquirerPartnerId" jdbcType="VARCHAR" />
        <result column="secondary_merchant_ID" property="secondaryMerchantId" jdbcType="VARCHAR" />
        <result column="store_ID" property="storeId" jdbcType="VARCHAR" />
        <result column="mode" property="mode" jdbcType="VARCHAR" />
        <result column="mcc" property="mcc" jdbcType="VARCHAR" />
        <result column="country_area" property="countryArea" jdbcType="VARCHAR" />
        <result column="store_address" property="storeAddress" jdbcType="VARCHAR" />
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
        <result column="business_hours" property="businessHours" jdbcType="VARCHAR" />
        <result column="average_spending" property="averageSpending" jdbcType="DECIMAL" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="contact_tel" property="contactTel" jdbcType="VARCHAR" />
        <result column="service" property="service" jdbcType="VARCHAR" />
        <result column="recommendations" property="recommendations" jdbcType="VARCHAR" />
        <result column="store_name_cn" property="storeNameCn" jdbcType="VARCHAR" />
        <result column="brand_name_cn" property="brandNameCn" jdbcType="VARCHAR" />
        <result column="store_desc_cn" property="storeDescCn" jdbcType="VARCHAR" />
        <result column="tags_cn" property="tagsCn" jdbcType="VARCHAR" />
        <result column="address_cn" property="addressCn" jdbcType="VARCHAR" />
        <result column="landmark_cn" property="landmarkCn" jdbcType="VARCHAR" />
        <result column="commercial_district" property="commercialDistrict" jdbcType="VARCHAR" />
        <result column="except_cn" property="exceptCn" jdbcType="VARCHAR" />
        <result column="Google_maps_latitude" property="googleMapsLatitude" jdbcType="VARCHAR" />
        <result column="Google_maps_longitude" property="googleMapsLongitude" jdbcType="VARCHAR" />
        <result column="store_sts" property="storeSts" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        user_id, secondary_merchant_name, store_name, acquirer_partner_ID, secondary_merchant_ID, 
        store_ID, mode, mcc, country_area, store_address, brand_name, business_hours, average_spending, 
        ccy, contact_tel, service, recommendations, store_name_cn, brand_name_cn, store_desc_cn, 
        tags_cn, address_cn, landmark_cn, commercial_district, except_cn, Google_maps_latitude, 
        Google_maps_longitude, store_sts, MODIFY_TIME, CREATE_TIME
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_sub_al_merc_list_cast
        where user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_sub_al_merc_list_cast
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.SecMercListCastDO" >
        insert into cpi_sub_al_merc_list_cast
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                user_id,
            </if>
            <if test="secondaryMerchantName != null" >
                secondary_merchant_name,
            </if>
            <if test="storeName != null" >
                store_name,
            </if>
            <if test="acquirerPartnerId != null" >
                acquirer_partner_ID,
            </if>
            <if test="secondaryMerchantId != null" >
                secondary_merchant_ID,
            </if>
            <if test="storeId != null" >
                store_ID,
            </if>
            <if test="mode != null" >
                mode,
            </if>
            <if test="mcc != null" >
                mcc,
            </if>
            <if test="countryArea != null" >
                country_area,
            </if>
            <if test="storeAddress != null" >
                store_address,
            </if>
            <if test="brandName != null" >
                brand_name,
            </if>
            <if test="businessHours != null" >
                business_hours,
            </if>
            <if test="averageSpending != null" >
                average_spending,
            </if>
            <if test="ccy != null" >
                ccy,
            </if>
            <if test="contactTel != null" >
                contact_tel,
            </if>
            <if test="service != null" >
                service,
            </if>
            <if test="recommendations != null" >
                recommendations,
            </if>
            <if test="storeNameCn != null" >
                store_name_cn,
            </if>
            <if test="brandNameCn != null" >
                brand_name_cn,
            </if>
            <if test="storeDescCn != null" >
                store_desc_cn,
            </if>
            <if test="tagsCn != null" >
                tags_cn,
            </if>
            <if test="addressCn != null" >
                address_cn,
            </if>
            <if test="landmarkCn != null" >
                landmark_cn,
            </if>
            <if test="commercialDistrict != null" >
                commercial_district,
            </if>
            <if test="exceptCn != null" >
                except_cn,
            </if>
            <if test="googleMapsLatitude != null" >
                Google_maps_latitude,
            </if>
            <if test="googleMapsLongitude != null" >
                Google_maps_longitude,
            </if>
            <if test="storeSts != null" >
                store_sts,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantName != null" >
                #{secondaryMerchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null" >
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="acquirerPartnerId != null" >
                #{acquirerPartnerId,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantId != null" >
                #{secondaryMerchantId,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null" >
                #{storeId,jdbcType=VARCHAR},
            </if>
            <if test="mode != null" >
                #{mode,jdbcType=VARCHAR},
            </if>
            <if test="mcc != null" >
                #{mcc,jdbcType=VARCHAR},
            </if>
            <if test="countryArea != null" >
                #{countryArea,jdbcType=VARCHAR},
            </if>
            <if test="storeAddress != null" >
                #{storeAddress,jdbcType=VARCHAR},
            </if>
            <if test="brandName != null" >
                #{brandName,jdbcType=VARCHAR},
            </if>
            <if test="businessHours != null" >
                #{businessHours,jdbcType=VARCHAR},
            </if>
            <if test="averageSpending != null" >
                #{averageSpending,jdbcType=DECIMAL},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="contactTel != null" >
                #{contactTel,jdbcType=VARCHAR},
            </if>
            <if test="service != null" >
                #{service,jdbcType=VARCHAR},
            </if>
            <if test="recommendations != null" >
                #{recommendations,jdbcType=VARCHAR},
            </if>
            <if test="storeNameCn != null" >
                #{storeNameCn,jdbcType=VARCHAR},
            </if>
            <if test="brandNameCn != null" >
                #{brandNameCn,jdbcType=VARCHAR},
            </if>
            <if test="storeDescCn != null" >
                #{storeDescCn,jdbcType=VARCHAR},
            </if>
            <if test="tagsCn != null" >
                #{tagsCn,jdbcType=VARCHAR},
            </if>
            <if test="addressCn != null" >
                #{addressCn,jdbcType=VARCHAR},
            </if>
            <if test="landmarkCn != null" >
                #{landmarkCn,jdbcType=VARCHAR},
            </if>
            <if test="commercialDistrict != null" >
                #{commercialDistrict,jdbcType=VARCHAR},
            </if>
            <if test="exceptCn != null" >
                #{exceptCn,jdbcType=VARCHAR},
            </if>
            <if test="googleMapsLatitude != null" >
                #{googleMapsLatitude,jdbcType=VARCHAR},
            </if>
            <if test="googleMapsLongitude != null" >
                #{googleMapsLongitude,jdbcType=VARCHAR},
            </if>
            <if test="storeSts != null" >
                #{storeSts,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.SecMercListCastDO" >
        update cpi_sub_al_merc_list_cast
        <set >
            <if test="secondaryMerchantName != null" >
                secondary_merchant_name = #{secondaryMerchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null" >
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="acquirerPartnerId != null" >
                acquirer_partner_ID = #{acquirerPartnerId,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantId != null" >
                secondary_merchant_ID = #{secondaryMerchantId,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null" >
                store_ID = #{storeId,jdbcType=VARCHAR},
            </if>
            <if test="mode != null" >
                mode = #{mode,jdbcType=VARCHAR},
            </if>
            <if test="mcc != null" >
                mcc = #{mcc,jdbcType=VARCHAR},
            </if>
            <if test="countryArea != null" >
                country_area = #{countryArea,jdbcType=VARCHAR},
            </if>
            <if test="storeAddress != null" >
                store_address = #{storeAddress,jdbcType=VARCHAR},
            </if>
            <if test="brandName != null" >
                brand_name = #{brandName,jdbcType=VARCHAR},
            </if>
            <if test="businessHours != null" >
                business_hours = #{businessHours,jdbcType=VARCHAR},
            </if>
            <if test="averageSpending != null" >
                average_spending = #{averageSpending,jdbcType=DECIMAL},
            </if>
            <if test="ccy != null" >
                ccy = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="contactTel != null" >
                contact_tel = #{contactTel,jdbcType=VARCHAR},
            </if>
            <if test="service != null" >
                service = #{service,jdbcType=VARCHAR},
            </if>
            <if test="recommendations != null" >
                recommendations = #{recommendations,jdbcType=VARCHAR},
            </if>
            <if test="storeNameCn != null" >
                store_name_cn = #{storeNameCn,jdbcType=VARCHAR},
            </if>
            <if test="brandNameCn != null" >
                brand_name_cn = #{brandNameCn,jdbcType=VARCHAR},
            </if>
            <if test="storeDescCn != null" >
                store_desc_cn = #{storeDescCn,jdbcType=VARCHAR},
            </if>
            <if test="tagsCn != null" >
                tags_cn = #{tagsCn,jdbcType=VARCHAR},
            </if>
            <if test="addressCn != null" >
                address_cn = #{addressCn,jdbcType=VARCHAR},
            </if>
            <if test="landmarkCn != null" >
                landmark_cn = #{landmarkCn,jdbcType=VARCHAR},
            </if>
            <if test="commercialDistrict != null" >
                commercial_district = #{commercialDistrict,jdbcType=VARCHAR},
            </if>
            <if test="exceptCn != null" >
                except_cn = #{exceptCn,jdbcType=VARCHAR},
            </if>
            <if test="googleMapsLatitude != null" >
                Google_maps_latitude = #{googleMapsLatitude,jdbcType=VARCHAR},
            </if>
            <if test="googleMapsLongitude != null" >
                Google_maps_longitude = #{googleMapsLongitude,jdbcType=VARCHAR},
            </if>
            <if test="storeSts != null" >
                store_sts = #{storeSts,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>
</mapper>