<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IPosPreAuJrnDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.PosPreAuJrnDO" >
        <id column="PRE_JRN_NO" property="preJrnNo" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_NO_ENC" property="crdNoEnc" jdbcType="VARCHAR" />
        <result column="ORD_CCY" property="ordCcy" jdbcType="CHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
        <result column="TRM_NO" property="trmNo" jdbcType="VARCHAR" />
        <result column="REQ_ORD_NO" property="reqOrdNo" jdbcType="VARCHAR" />
        <result column="REQ_ORD_DT" property="reqOrdDt" jdbcType="DATE" />
        <result column="REQ_ORD_TM" property="reqOrdTm" jdbcType="TIME" />
        <result column="ORG_ORD_NO" property="orgOrdNo" jdbcType="VARCHAR" />
        <result column="ORG_JRN_NO" property="orgJrnNo" jdbcType="VARCHAR" />
        <result column="ORG_ORD_DT" property="orgOrdDt" jdbcType="DATE" />
        <result column="ORG_ORD_TM" property="orgOrdTm" jdbcType="TIME" />
        <result column="ORG_RSP_CD" property="orgRspCd" jdbcType="VARCHAR" />
        <result column="ORG_RSP_MSG" property="orgRspMsg" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        PRE_JRN_NO, CRD_CORP_ORG, RUT_CORP_ORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, CRD_AC_TYP, 
        CRD_NO_ENC, ORD_CCY, ORD_AMT, TRM_NO, REQ_ORD_NO, REQ_ORD_DT, REQ_ORD_TM, ORG_ORD_NO,
        ORG_JRN_NO, ORG_ORD_DT, ORG_ORD_TM, ORG_RSP_CD, ORG_RSP_MSG, RMK, CREATE_TIME, MODIFY_TIME, 
        TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_pos_pre_au_jrn
        where PRE_JRN_NO = #{preJrnNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_pos_pre_au_jrn
        where PRE_JRN_NO = #{preJrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.PosPreAuJrnDO" >
        insert into cpi_pos_pre_au_jrn
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="preJrnNo != null" >
                PRE_JRN_NO,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC,
            </if>
            <if test="ordCcy != null" >
                ORD_CCY,
            </if>
            <if test="ordAmt != null" >
                ORD_AMT,
            </if>
            <if test="trmNo != null" >
                TRM_NO,
            </if>
            <if test="reqOrdNo != null" >
                REQ_ORD_NO,
            </if>
            <if test="reqOrdDt != null" >
                REQ_ORD_DT,
            </if>
            <if test="reqOrdTm != null" >
                REQ_ORD_TM,
            </if>
            <if test="orgOrdNo != null" >
                ORG_ORD_NO,
            </if>
            <if test="orgJrnNo != null" >
                ORG_JRN_NO,
            </if>
            <if test="orgOrdDt != null" >
                ORG_ORD_DT,
            </if>
            <if test="orgOrdTm != null" >
                ORG_ORD_TM,
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD,
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="preJrnNo != null" >
                #{preJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="ordCcy != null" >
                #{ordCcy,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="trmNo != null" >
                #{trmNo,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdNo != null" >
                #{reqOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdDt != null" >
                #{reqOrdDt,jdbcType=DATE},
            </if>
            <if test="reqOrdTm != null" >
                #{reqOrdTm,jdbcType=TIME},
            </if>
            <if test="orgOrdNo != null" >
                #{orgOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="orgJrnNo != null" >
                #{orgJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrdDt != null" >
                #{orgOrdDt,jdbcType=DATE},
            </if>
            <if test="orgOrdTm != null" >
                #{orgOrdTm,jdbcType=TIME},
            </if>
            <if test="orgRspCd != null" >
                #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.PosPreAuJrnDO" >
        update cpi_pos_pre_au_jrn
        <set >
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="ordCcy != null" >
                ORD_CCY = #{ordCcy,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                ORD_AMT = #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="trmNo != null" >
                TRM_NO = #{trmNo,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdNo != null" >
                REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdDt != null" >
                REQ_ORD_DT = #{reqOrdDt,jdbcType=DATE},
            </if>
            <if test="reqOrdTm != null" >
                REQ_ORD_TM = #{reqOrdTm,jdbcType=TIME},
            </if>
            <if test="orgOrdNo != null" >
                ORG_ORD_NO = #{orgOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="orgJrnNo != null" >
                ORG_JRN_NO = #{orgJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrdDt != null" >
                ORG_ORD_DT = #{orgOrdDt,jdbcType=DATE},
            </if>
            <if test="orgOrdTm != null" >
                ORG_ORD_TM = #{orgOrdTm,jdbcType=TIME},
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD = #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG = #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where PRE_JRN_NO = #{preJrnNo,jdbcType=VARCHAR}
    </update>
</mapper>