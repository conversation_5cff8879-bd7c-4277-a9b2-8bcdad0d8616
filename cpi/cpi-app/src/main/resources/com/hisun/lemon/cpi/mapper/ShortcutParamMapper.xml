<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IShortcutParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.ShortcutParamDO" >
        <id column="SHT_PAR_ID" property="shtParId" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="SMS_TYP" property="smsTyp" jdbcType="VARCHAR" />
        <result column="EXP_DT_TYP" property="expDtTyp" jdbcType="VARCHAR" />
        <result column="CVV2_TYP" property="cvv2Typ" jdbcType="CHAR" />
        <result column="SING_PTL" property="singPtl" jdbcType="CHAR" />
        <result column="BNK_MBL_TYP" property="bnkMblTyp" jdbcType="CHAR" />
        <result column="AMT_D_LMT" property="amtDLmt" jdbcType="DECIMAL" />
        <result column="UNSIGN_FLG" property="unsignFlg" jdbcType="CHAR" />
        <result column="SUC_SMS_FLG" property="sucSmsFlg" jdbcType="CHAR" />
        <result column="CRE_OPR_ID" property="creOprId" jdbcType="VARCHAR" />
        <result column="UPD_OPR_ID" property="updOprId" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        SHT_PAR_ID, RUT_CORP_ORG, CRD_AC_TYP, SMS_TYP, EXP_DT_TYP,
        CVV2_TYP, SING_PTL, BNK_MBL_TYP, AMT_D_LMT, UNSIGN_FLG, SUC_SMS_FLG, CRE_OPR_ID, 
        UPD_OPR_ID, RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_shortcut_param
        where SHT_PAR_ID = #{shtParId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_shortcut_param
        where SHT_PAR_ID = #{shtParId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.ShortcutParamDO" >
        insert into cpi_shortcut_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="shtParId != null" >
                SHT_PAR_ID,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="smsTyp != null" >
                SMS_TYP,
            </if>
            <if test="expDtTyp != null" >
                EXP_DT_TYP,
            </if>
            <if test="cvv2Typ != null" >
                CVV2_TYP,
            </if>
            <if test="singPtl != null" >
                SING_PTL,
            </if>
            <if test="bnkMblTyp != null" >
                BNK_MBL_TYP,
            </if>
            <if test="amtDLmt != null" >
                AMT_D_LMT,
            </if>
            <if test="unsignFlg != null" >
                UNSIGN_FLG,
            </if>
            <if test="sucSmsFlg != null" >
                SUC_SMS_FLG,
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID,
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="shtParId != null" >
                #{shtParId,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="smsTyp != null" >
                #{smsTyp,jdbcType=VARCHAR},
            </if>
            <if test="expDtTyp != null" >
                #{expDtTyp,jdbcType=VARCHAR},
            </if>
            <if test="cvv2Typ != null" >
                #{cvv2Typ,jdbcType=CHAR},
            </if>
            <if test="singPtl != null" >
                #{singPtl,jdbcType=CHAR},
            </if>
            <if test="bnkMblTyp != null" >
                #{bnkMblTyp,jdbcType=CHAR},
            </if>
            <if test="amtDLmt != null" >
                #{amtDLmt,jdbcType=DECIMAL},
            </if>
            <if test="unsignFlg != null" >
                #{unsignFlg,jdbcType=CHAR},
            </if>
            <if test="sucSmsFlg != null" >
                #{sucSmsFlg,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                #{creOprId,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null" >
                #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.ShortcutParamDO" >
        update cpi_shortcut_param
        <set >
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="smsTyp != null" >
                SMS_TYP = #{smsTyp,jdbcType=VARCHAR},
            </if>
            <if test="expDtTyp != null" >
                EXP_DT_TYP = #{expDtTyp,jdbcType=VARCHAR},
            </if>
            <if test="cvv2Typ != null" >
                CVV2_TYP = #{cvv2Typ,jdbcType=CHAR},
            </if>
            <if test="singPtl != null" >
                SING_PTL = #{singPtl,jdbcType=CHAR},
            </if>
            <if test="bnkMblTyp != null" >
                BNK_MBL_TYP = #{bnkMblTyp,jdbcType=CHAR},
            </if>
            <if test="amtDLmt != null" >
                AMT_D_LMT = #{amtDLmt,jdbcType=DECIMAL},
            </if>
            <if test="unsignFlg != null" >
                UNSIGN_FLG = #{unsignFlg,jdbcType=CHAR},
            </if>
            <if test="sucSmsFlg != null" >
                SUC_SMS_FLG = #{sucSmsFlg,jdbcType=CHAR},
            </if>
            <if test="creOprId != null" >
                CRE_OPR_ID = #{creOprId,jdbcType=VARCHAR},
            </if>
            <if test="updOprId != null" >
                UPD_OPR_ID = #{updOprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where SHT_PAR_ID = #{shtParId,jdbcType=VARCHAR}
    </update>

    <select id="selectByUniqueKey" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_shortcut_param
        where RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
          and CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR}
    </select>
</mapper>