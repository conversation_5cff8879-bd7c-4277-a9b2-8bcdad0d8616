<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IFundPollParamDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.FundPollParamDO" >
        <id column="QRY_CYC_ID" property="qryCycId" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="EFF_FLG" property="effFlg" jdbcType="CHAR" />
        <result column="ORD_PRI_LEV" property="ordPriLev" jdbcType="INTEGER" />
        <result column="CYC_ORD_CNT" property="cycOrdCnt" jdbcType="INTEGER" />
        <result column="BEFORE_TIME" property="beforeTime" jdbcType="INTEGER" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        QRY_CYC_ID, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, EFF_FLG, ORD_PRI_LEV, CYC_ORD_CNT, 
        BEFORE_TIME, OPR_ID, RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_fund_poll_param
        where QRY_CYC_ID = #{qryCycId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_fund_poll_param
        where QRY_CYC_ID = #{qryCycId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.FundPollParamDO" >
        insert into cpi_fund_poll_param
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="qryCycId != null" >
                QRY_CYC_ID,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="effFlg != null" >
                EFF_FLG,
            </if>
            <if test="ordPriLev != null" >
                ORD_PRI_LEV,
            </if>
            <if test="cycOrdCnt != null" >
                CYC_ORD_CNT,
            </if>
            <if test="beforeTime != null" >
                BEFORE_TIME,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="qryCycId != null" >
                #{qryCycId,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="effFlg != null" >
                #{effFlg,jdbcType=CHAR},
            </if>
            <if test="ordPriLev != null" >
                #{ordPriLev,jdbcType=INTEGER},
            </if>
            <if test="cycOrdCnt != null" >
                #{cycOrdCnt,jdbcType=INTEGER},
            </if>
            <if test="beforeTime != null" >
                #{beforeTime,jdbcType=INTEGER},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.FundPollParamDO" >
        update cpi_fund_poll_param
        <set >
            <if test="rutCorg != null" >
                RUT_CORG = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="effFlg != null" >
                EFF_FLG = #{effFlg,jdbcType=CHAR},
            </if>
            <if test="ordPriLev != null" >
                ORD_PRI_LEV = #{ordPriLev,jdbcType=INTEGER},
            </if>
            <if test="cycOrdCnt != null" >
                CYC_ORD_CNT = #{cycOrdCnt,jdbcType=INTEGER},
            </if>
            <if test="beforeTime != null" >
                BEFORE_TIME = #{beforeTime,jdbcType=INTEGER},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where QRY_CYC_ID = #{qryCycId,jdbcType=VARCHAR}
    </update>

    <select id="selectFundPollParam" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpi_fund_poll_param
        where RUT_CORG=#{rutCorg}
          AND CORP_BUS_TYP=#{corpBusTyp}
          AND CORP_BUS_SUB_TYP=#{corpBusSubTyp}
          AND EFF_FLG = '1'
    </select>

    <select id="queryAllFund" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_fund_poll_param
        where EFF_FLG = '1'
          and CORP_BUS_TYP in ('02','03','04')
    </select>

    <select id="queryAllRefund" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_fund_poll_param
        where EFF_FLG = '1'
        and CORP_BUS_TYP in ('05')
    </select>
</mapper>