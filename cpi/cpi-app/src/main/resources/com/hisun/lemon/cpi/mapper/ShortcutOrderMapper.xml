<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IShortcutOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.ShortcutOrderDO" >
        <id column="SUB_ORD_NO" property="subOrdNo" jdbcType="VARCHAR" />
        <result column="FND_ORD_NO" property="fndOrdNo" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="AGR_FLG" property="agrFlg" jdbcType="CHAR" />
        <result column="AGR_NO" property="agrNo" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
        <result column="ORD_STS" property="ordSts" jdbcType="CHAR" />
        <result column="CRD_CVV2_ENC" property="crdCvv2Enc" jdbcType="VARCHAR" />
        <result column="CRD_EXP_DT_ENC" property="crdExpDtEnc" jdbcType="VARCHAR" />
        <result column="SMS_CHK_FLG" property="smsChkFlg" jdbcType="CHAR" />
        <result column="SMS_JRN_NO" property="smsJrnNo" jdbcType="VARCHAR" />
        <result column="ORG_ORD_NO" property="orgOrdNo" jdbcType="VARCHAR" />
        <result column="ORG_JRN_NO" property="orgJrnNo" jdbcType="VARCHAR" />
        <result column="ORG_ORD_DT" property="orgOrdDt" jdbcType="DATE" />
        <result column="ORG_ORD_TM" property="orgOrdTm" jdbcType="TIME" />
        <result column="ORG_RSP_CD" property="orgRspCd" jdbcType="VARCHAR" />
        <result column="ORG_RSP_MSG" property="orgRspMsg" jdbcType="VARCHAR" />
        <result column="CHK_KEY" property="chkKey" jdbcType="VARCHAR" />
        <result column="CHK_FLG" property="chkFlg" jdbcType="CHAR" />
        <result column="CHK_STS" property="chkSts" jdbcType="CHAR" />
        <result column="CHK_DT" property="chkDt" jdbcType="DATE" />
        <result column="CHK_TM" property="chkTm" jdbcType="TIME" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        SUB_ORD_NO, FND_ORD_NO, ORD_DT, ORD_TM, AC_DT, AGR_FLG, AGR_NO, CRD_CORP_ORG, RUT_CORP_ORG, 
        CORP_BUS_TYP, CORP_BUS_SUB_TYP, CRD_AC_TYP, ORD_AMT, ORD_STS, CRD_CVV2_ENC, CRD_EXP_DT_ENC, 
        SMS_CHK_FLG, SMS_JRN_NO, ORG_ORD_NO, ORG_JRN_NO, ORG_ORD_DT, ORG_ORD_TM, ORG_RSP_CD, 
        ORG_RSP_MSG, CHK_KEY, CHK_FLG, CHK_STS, CHK_DT, CHK_TM, RMK, CREATE_TIME, MODIFY_TIME, 
        TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_shortcut_order
        where SUB_ORD_NO = #{subOrdNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_shortcut_order
        where SUB_ORD_NO = #{subOrdNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.ShortcutOrderDO" >
        insert into cpi_shortcut_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="subOrdNo != null" >
                SUB_ORD_NO,
            </if>
            <if test="fndOrdNo != null" >
                FND_ORD_NO,
            </if>
            <if test="ordDt != null" >
                ORD_DT,
            </if>
            <if test="ordTm != null" >
                ORD_TM,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="agrFlg != null" >
                AGR_FLG,
            </if>
            <if test="agrNo != null" >
                AGR_NO,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="ordAmt != null" >
                ORD_AMT,
            </if>
            <if test="ordSts != null" >
                ORD_STS,
            </if>
            <if test="crdCvv2Enc != null" >
                CRD_CVV2_ENC,
            </if>
            <if test="crdExpDtEnc != null" >
                CRD_EXP_DT_ENC,
            </if>
            <if test="smsChkFlg != null" >
                SMS_CHK_FLG,
            </if>
            <if test="smsJrnNo != null" >
                SMS_JRN_NO,
            </if>
            <if test="orgOrdNo != null" >
                ORG_ORD_NO,
            </if>
            <if test="orgJrnNo != null" >
                ORG_JRN_NO,
            </if>
            <if test="orgOrdDt != null" >
                ORG_ORD_DT,
            </if>
            <if test="orgOrdTm != null" >
                ORG_ORD_TM,
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD,
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG,
            </if>
            <if test="chkKey != null" >
                CHK_KEY,
            </if>
            <if test="chkFlg != null" >
                CHK_FLG,
            </if>
            <if test="chkSts != null" >
                CHK_STS,
            </if>
            <if test="chkDt != null" >
                CHK_DT,
            </if>
            <if test="chkTm != null" >
                CHK_TM,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="subOrdNo != null" >
                #{subOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="fndOrdNo != null" >
                #{fndOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                #{ordTm,jdbcType=TIME},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="agrFlg != null" >
                #{agrFlg,jdbcType=CHAR},
            </if>
            <if test="agrNo != null" >
                #{agrNo,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                #{ordSts,jdbcType=CHAR},
            </if>
            <if test="crdCvv2Enc != null" >
                #{crdCvv2Enc,jdbcType=VARCHAR},
            </if>
            <if test="crdExpDtEnc != null" >
                #{crdExpDtEnc,jdbcType=VARCHAR},
            </if>
            <if test="smsChkFlg != null" >
                #{smsChkFlg,jdbcType=CHAR},
            </if>
            <if test="smsJrnNo != null" >
                #{smsJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrdNo != null" >
                #{orgOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="orgJrnNo != null" >
                #{orgJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrdDt != null" >
                #{orgOrdDt,jdbcType=DATE},
            </if>
            <if test="orgOrdTm != null" >
                #{orgOrdTm,jdbcType=TIME},
            </if>
            <if test="orgRspCd != null" >
                #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="chkKey != null" >
                #{chkKey,jdbcType=VARCHAR},
            </if>
            <if test="chkFlg != null" >
                #{chkFlg,jdbcType=CHAR},
            </if>
            <if test="chkSts != null" >
                #{chkSts,jdbcType=CHAR},
            </if>
            <if test="chkDt != null" >
                #{chkDt,jdbcType=DATE},
            </if>
            <if test="chkTm != null" >
                #{chkTm,jdbcType=TIME},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.ShortcutOrderDO" >
        update cpi_shortcut_order
        <set >
            <if test="fndOrdNo != null" >
                FND_ORD_NO = #{fndOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                ORD_DT = #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                ORD_TM = #{ordTm,jdbcType=TIME},
            </if>
            <if test="acDt != null" >
                AC_DT = #{acDt,jdbcType=DATE},
            </if>
            <if test="agrFlg != null" >
                AGR_FLG = #{agrFlg,jdbcType=CHAR},
            </if>
            <if test="agrNo != null" >
                AGR_NO = #{agrNo,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                ORD_AMT = #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                ORD_STS = #{ordSts,jdbcType=CHAR},
            </if>
            <if test="crdCvv2Enc != null" >
                CRD_CVV2_ENC = #{crdCvv2Enc,jdbcType=VARCHAR},
            </if>
            <if test="crdExpDtEnc != null" >
                CRD_EXP_DT_ENC = #{crdExpDtEnc,jdbcType=VARCHAR},
            </if>
            <if test="smsChkFlg != null" >
                SMS_CHK_FLG = #{smsChkFlg,jdbcType=CHAR},
            </if>
            <if test="smsJrnNo != null" >
                SMS_JRN_NO = #{smsJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrdNo != null" >
                ORG_ORD_NO = #{orgOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="orgJrnNo != null" >
                ORG_JRN_NO = #{orgJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgOrdDt != null" >
                ORG_ORD_DT = #{orgOrdDt,jdbcType=DATE},
            </if>
            <if test="orgOrdTm != null" >
                ORG_ORD_TM = #{orgOrdTm,jdbcType=TIME},
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD = #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG = #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="chkKey != null" >
                CHK_KEY = #{chkKey,jdbcType=VARCHAR},
            </if>
            <if test="chkFlg != null" >
                CHK_FLG = #{chkFlg,jdbcType=CHAR},
            </if>
            <if test="chkSts != null" >
                CHK_STS = #{chkSts,jdbcType=CHAR},
            </if>
            <if test="chkDt != null" >
                CHK_DT = #{chkDt,jdbcType=DATE},
            </if>
            <if test="chkTm != null" >
                CHK_TM = #{chkTm,jdbcType=TIME},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where SUB_ORD_NO = #{subOrdNo,jdbcType=VARCHAR}
    </update>

    <!--根据内部订单号，查询快捷订单信息-->
    <select id="selectByFndOrdNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpi_shortcut_order
        where FND_ORD_NO = #{fndOrdNo,jdbcType=VARCHAR}
    </select>

    <!--根据对账键值，查询快捷订单信息-->
    <select id="selectShortcutOrderByChkKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpi_shortcut_order
        where CHK_KEY = #{chkKey,jdbcType=VARCHAR}
    </select>

    <!--根据对账键值，更新快捷订单的对账状态、对账时间等-->
    <update id="updateShortcutOrderByChkKey" parameterType="com.hisun.lemon.cpi.entity.ShortcutOrderDO" >
        update cpi_shortcut_order
          set CHK_STS = #{chkSts,jdbcType=CHAR},
              CHK_DT = #{chkDt,jdbcType=DATE},
              CHK_TM = #{chkTm,jdbcType=TIME}
        where CHK_KEY = #{chkKey,jdbcType=VARCHAR}
    </update>

    <!-- 查询我方ORD_STS='S1'(交易成功)、CHK_STS='5'(存疑)的订单，ORD_DT订单日期小于对账日期 -->
    <select id="getShortcutOrderListByChkStsDoubt" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_shortcut_order
        where ORD_STS = #{ordSts,jdbcType=CHAR}
          and CHK_STS = #{chkSts,jdbcType=CHAR}
          and ORD_DT &lt; #{chkDt,jdbcType=DATE}
    </select>

    <!--将ORD_STS='S'(交易成功)、CHK_STS='5'(存疑)的订单更新为CHK_STS='2'，(ORD_DT)订单日期小于对账日期-->
    <update id="updateShortcutOrderByChkStsDoubt" >
        update cpi_shortcut_order
          set CHK_DT = #{chkDt,jdbcType=DATE},
              CHK_TM = #{chkTm,jdbcType=TIME},
              CHK_STS = #{newChkSts,jdbcType=CHAR}
        where ORD_STS = #{ordSts,jdbcType=CHAR}
          and CHK_STS = #{oldChkSts,jdbcType=CHAR}
          and ORD_DT &lt; #{chkDt,jdbcType=DATE}
    </update>

    <!-- 查询我方ORD_STS='S'、CHK_STS='0'(未对账)的订单，订单日期等于对账日期 -->
    <select id="getShortcutOrderListByChkStsNotstart" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_shortcut_order
        where ORD_STS = #{ordSts,jdbcType=CHAR}
          and CHK_STS = #{chkSts,jdbcType=CHAR}
          and ORD_DT = #{chkDt,jdbcType=DATE}
    </select>

    <!-- 将我方ORD_STS='S'(交易成功)、CHK_STS='0'(未对账)的订单更新为CHK_STS='5'(存疑)，订单日期等于对账日期 -->
    <update id="updateShortcutOrderByChkStsNotstart" >
        update cpi_shortcut_order
        set CHK_DT = #{chkDt,jdbcType=DATE},
            CHK_TM = #{chkTm,jdbcType=TIME},
            CHK_STS = #{newChkSts,jdbcType=CHAR}
        where ORD_STS = #{ordSts,jdbcType=CHAR}
          and CHK_STS = #{oldChkSts,jdbcType=CHAR}
          and ORD_DT = #{chkDt,jdbcType=DATE}
    </update>

    <!-- 查询我方ORD_STS='S'、的订单，订单日期等于对账日期 -->
    <select id="getShotcutOrderListByChkFilDt" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_shortcut_order
        where ORD_STS = 'S1'
        and RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR}
        and AC_DT = #{chkFilDt,jdbcType=DATE}
        ORDER BY FND_ORD_NO ASC
        limit #{beginNum,jdbcType=INTEGER}, #{countNum,jdbcType=INTEGER}
    </select>

</mapper>