<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ISubMercCastDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.SubMercCastDO" >
        <id column="APP_ID" property="appId" jdbcType="VARCHAR" />
        <result column="MCH_ID" property="mchId" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="SUB_MCH_ID" property="subMchId" jdbcType="VARCHAR" />
        <result column="EFF_FLG" property="effFlg" jdbcType="VARCHAR" />
        <result column="MERC_NM" property="mercNm" jdbcType="VARCHAR" />
        <result column="MERC_SNM" property="mercSnm" jdbcType="VARCHAR" />
        <result column="OFFICE_TEL" property="officeTel" jdbcType="VARCHAR" />
        <result column="MERC_RMK" property="mercRmk" jdbcType="VARCHAR" />
        <result column="MERC_WEBSITE" property="mercWebsite" jdbcType="VARCHAR" />
        <result column="CONTRACT_NM" property="contractNm" jdbcType="VARCHAR" />
        <result column="CONTRACT_TEL" property="contractTel" jdbcType="VARCHAR" />
        <result column="CONTRACT_WEBSITE" property="contractWebsite" jdbcType="VARCHAR" />
        <result column="MERC_INTRODUCTION" property="mercIntroduction" jdbcType="VARCHAR" />
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        APP_ID, MCH_ID, USER_ID, SUB_MCH_ID, EFF_FLG, MERC_NM, MERC_SNM, OFFICE_TEL, MERC_RMK,
        MERC_WEBSITE, CONTRACT_NM, CONTRACT_TEL, CONTRACT_WEBSITE, MERC_INTRODUCTION, BUSINESS_TYPE, CREATE_TIME, MODIFY_TIME
    </sql>

    <!--根据平台商户号，查询对应的微信子商户号-->
    <select id="getSubMercCastDO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpi_sub_merc_cast
        where APP_ID = #{appId,jdbcType=VARCHAR}
        and USER_ID = #{merchantId,jdbcType=VARCHAR}
        <if test="effFlg != null and effFlg != ''" >
            and EFF_FLG = #{effFlg,jdbcType=VARCHAR}
        </if>
    </select>

    <!--根据平台商户号，查出一条生效的微信子商户号-->
    <select id="getOneSubMercCastDO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpi_sub_merc_cast
        where APP_ID = #{appId,jdbcType=VARCHAR}
        and EFF_FLG = '1'
        limit 0,1
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_sub_merc_cast
        where user_id = #{mercId,jdbcType=VARCHAR}
    </delete>

    <!--根据平台商户号，添加对应的微信子商户号-->
    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.SubMercCastDO" >
        insert into cpi_sub_merc_cast
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="appId != null" >
                APP_ID,
            </if>
            <if test="mchId != null" >
                MCH_ID,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="subMchId != null" >
                SUB_MCH_ID,
            </if>
            <if test="effFlg != null" >
                EFF_FLG,
            </if>
            <if test="mercNm != null" >
                MERC_NM,
            </if>
            <if test="mercSnm != null" >
                MERC_SNM,
            </if>
            <if test="officeTel != null" >
                OFFICE_TEL,
            </if>
            <if test="mercRmk != null" >
                MERC_RMK,
            </if>
            <if test="mercWebsite != null" >
                MERC_WEBSITE,
            </if>
            <if test="contractNm != null" >
                CONTRACT_NM,
            </if>
            <if test="contractTel != null" >
                CONTRACT_TEL,
            </if>
            <if test="contractWebsite != null" >
                CONTRACT_WEBSITE,
            </if>
            <if test="mercIntroduction != null" >
                MERC_INTRODUCTION,
            </if>
            <if test="businessType != null" >
                BUSINESS_TYPE,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="appId != null" >
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="mchId != null" >
                #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="subMchId != null" >
                #{subMchId,jdbcType=VARCHAR},
            </if>
            <if test="effFlg != null" >
                #{effFlg,jdbcType=VARCHAR},
            </if>
            <if test="mercNm != null" >
                #{mercNm,jdbcType=VARCHAR},
            </if>
            <if test="mercSnm != null" >
                #{mercSnm,jdbcType=VARCHAR},
            </if>
            <if test="officeTel != null" >
                #{officeTel,jdbcType=VARCHAR},
            </if>
            <if test="mercRmk != null" >
                #{mercRmk,jdbcType=VARCHAR},
            </if>
            <if test="mercWebsite != null" >
                #{mercWebsite,jdbcType=VARCHAR},
            </if>
            <if test="contractNm != null" >
                #{contractNm,jdbcType=VARCHAR},
            </if>
            <if test="contractTel != null" >
                #{contractTel,jdbcType=VARCHAR},
            </if>
            <if test="contractWebsite != null" >
                #{contractWebsite,jdbcType=VARCHAR},
            </if>
            <if test="mercIntroduction != null" >
                #{mercIntroduction,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!--根据平台商户号，更新对应的微信子商户号-->
    <update id="update" parameterType="com.hisun.lemon.cpi.entity.SubMercCastDO" >
        update cpi_sub_merc_cast
        <set>
            <if test="mchId != null" >
                MCH_ID = #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="subMchId != null" >
                SUB_MCH_ID = #{subMchId,jdbcType=VARCHAR},
            </if>
            <if test="effFlg != null" >
                EFF_FLG = #{effFlg,jdbcType=VARCHAR},
            </if>
            <if test="mercNm != null" >
                MERC_NM = #{mercNm,jdbcType=VARCHAR},
            </if>
            <if test="mercSnm != null" >
                MERC_SNM = #{mercSnm,jdbcType=VARCHAR},
            </if>
            <if test="officeTel != null" >
                OFFICE_TEL = #{officeTel,jdbcType=VARCHAR},
            </if>
            <if test="mercRmk != null" >
                MERC_RMK = #{mercRmk,jdbcType=VARCHAR},
            </if>
            <if test="mercWebsite != null" >
                MERC_WEBSITE = #{mercWebsite,jdbcType=VARCHAR},
            </if>
            <if test="contractNm != null" >
                CONTRACT_NM = #{contractNm,jdbcType=VARCHAR},
            </if>
            <if test="contractTel != null" >
                CONTRACT_TEL = #{contractTel,jdbcType=VARCHAR},
            </if>
            <if test="contractWebsite != null" >
                CONTRACT_WEBSITE = #{contractWebsite,jdbcType=VARCHAR},
            </if>
            <if test="mercIntroduction != null" >
                MERC_INTRODUCTION = #{mercIntroduction,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null" >
                BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where APP_ID = #{appId,jdbcType=VARCHAR}
        and USER_ID = #{userId,jdbcType=VARCHAR}
    </update>

</mapper>