<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.wechat.IAccWeChatEbankpayRefundDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.wechat.AccWeChatEbankpayRefundDO" >
        <result column="tran_time" property="tranTime" jdbcType="VARCHAR" />
        <result column="appid" property="appid" jdbcType="VARCHAR" />
        <result column="mch_id" property="mchId" jdbcType="VARCHAR" />
        <result column="sub_mch_id" property="subMchId" jdbcType="VARCHAR" />
        <result column="device_info" property="deviceInfo" jdbcType="VARCHAR" />
        <result column="transaction_id" property="transactionId" jdbcType="VARCHAR" />
        <result column="out_transaction_id" property="outTransactionId" jdbcType="VARCHAR" />
        <result column="openid" property="openid" jdbcType="VARCHAR" />
        <result column="trade_type" property="tradeType" jdbcType="VARCHAR" />
        <result column="trade_state" property="tradeState" jdbcType="VARCHAR" />
        <result column="bank_type" property="bankType" jdbcType="VARCHAR" />
        <result column="fee_type" property="feeType" jdbcType="VARCHAR" />
        <result column="total_fee" property="totalFee" jdbcType="DECIMAL" />
        <result column="coupon_amount" property="couponAmount" jdbcType="DECIMAL" />
        <result column="rfd_apply_time" property="rfdApplyTime" jdbcType="VARCHAR" />
        <result column="rfd_succ_time" property="rfdSuccTime" jdbcType="VARCHAR" />
        <result column="refund_id" property="refundId" jdbcType="VARCHAR" />
        <result column="out_refund_no" property="outRefundNo" jdbcType="VARCHAR" />
        <result column="refund_fee" property="refundFee" jdbcType="DECIMAL" />
        <result column="coupon_rfd_amount" property="couponRfdAmount" jdbcType="DECIMAL" />
        <result column="rfd_type" property="rfdType" jdbcType="VARCHAR" />
        <result column="rfd_status" property="rfdStatus" jdbcType="VARCHAR" />
        <result column="product_nm" property="productNm" jdbcType="VARCHAR" />
        <result column="attach" property="attach" jdbcType="VARCHAR" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="rate" property="rate" jdbcType="VARCHAR" />
        <result column="cash_fee_type" property="cashFeeType" jdbcType="VARCHAR" />
        <result column="cash_fee" property="cashFee" jdbcType="DECIMAL" />
        <result column="stl_curr_type" property="stlCurrType" jdbcType="VARCHAR" />
        <result column="stl_curr_amount" property="stlCurrAmount" jdbcType="DECIMAL" />
        <result column="exchange_rate" property="exchangeRate" jdbcType="VARCHAR" />
        <result column="rfd_exchange_rate" property="rfdExchangeRate" jdbcType="VARCHAR" />
        <result column="payer_rfd_amount" property="payerRfdAmount" jdbcType="DECIMAL" />
        <result column="payer_rfd_curr_type" property="payerRfdCurrType" jdbcType="VARCHAR" />
        <result column="rfd_curr_type" property="rfdCurrType" jdbcType="VARCHAR" />
        <result column="rfd_stl_curr_type" property="rfdStlCurrType" jdbcType="VARCHAR" />
        <result column="rfd_stl_amount" property="rfdStlAmount" jdbcType="DECIMAL" />
        <result column="CHK_BAT_NO" property="chkBatNo" jdbcType="VARCHAR" />
        <result column="CHK_FIL_DT" property="chkFilDt" jdbcType="DATE" />
        <result column="CHK_FIL_NM" property="chkFilNm" jdbcType="VARCHAR" />
        <result column="CHK_STS" property="chkSts" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="CHK_ID" property="chkId" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.wechat.AccWeChatEbankpayRefundDO" >
        insert into cpi_acc_wechat_ebankpay_refund
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="tranTime != null" >
                tran_time,
            </if>
            <if test="appid != null" >
                appid,
            </if>
            <if test="mchId != null" >
                mch_id,
            </if>
            <if test="subMchId != null" >
                sub_mch_id,
            </if>
            <if test="deviceInfo != null" >
                device_info,
            </if>
            <if test="transactionId != null" >
                transaction_id,
            </if>
            <if test="outTransactionId != null" >
                out_transaction_id,
            </if>
            <if test="openid != null" >
                openid,
            </if>
            <if test="tradeType != null" >
                trade_type,
            </if>
            <if test="tradeState != null" >
                trade_state,
            </if>
            <if test="bankType != null" >
                bank_type,
            </if>
            <if test="feeType != null" >
                fee_type,
            </if>
            <if test="totalFee != null" >
                total_fee,
            </if>
            <if test="couponAmount != null" >
                coupon_amount,
            </if>
            <if test="rfdApplyTime != null" >
                rfd_apply_time,
            </if>
            <if test="rfdSuccTime != null" >
                rfd_succ_time,
            </if>
            <if test="refundId != null" >
                refund_id,
            </if>
            <if test="outRefundNo != null" >
                out_refund_no,
            </if>
            <if test="refundFee != null" >
                refund_fee,
            </if>
            <if test="couponRfdAmount != null" >
                coupon_rfd_amount,
            </if>
            <if test="rfdType != null" >
                rfd_type,
            </if>
            <if test="rfdStatus != null" >
                rfd_status,
            </if>
            <if test="productNm != null" >
                product_nm,
            </if>
            <if test="attach != null" >
                attach,
            </if>
            <if test="fee != null" >
                fee,
            </if>
            <if test="rate != null" >
                rate,
            </if>
            <if test="cashFeeType != null" >
                cash_fee_type,
            </if>
            <if test="cashFee != null" >
                cash_fee,
            </if>
            <if test="stlCurrType != null" >
                stl_curr_type,
            </if>
            <if test="stlCurrAmount != null" >
                stl_curr_amount,
            </if>
            <if test="exchangeRate != null" >
                exchange_rate,
            </if>
            <if test="rfdExchangeRate != null" >
                rfd_exchange_rate,
            </if>
            <if test="payerRfdAmount != null" >
                payer_rfd_amount,
            </if>
            <if test="payerRfdCurrType != null" >
                payer_rfd_curr_type,
            </if>
            <if test="rfdCurrType != null" >
                rfd_curr_type,
            </if>
            <if test="rfdStlCurrType != null" >
                rfd_stl_curr_type,
            </if>
            <if test="rfdStlAmount != null" >
                rfd_stl_amount,
            </if>
            <if test="chkBatNo != null" >
                CHK_BAT_NO,
            </if>
            <if test="chkFilDt != null" >
                CHK_FIL_DT,
            </if>
            <if test="chkFilNm != null" >
                CHK_FIL_NM,
            </if>
            <if test="chkSts != null" >
                CHK_STS,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="chkId != null" >
                CHK_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="tranTime != null" >
                #{tranTime,jdbcType=VARCHAR},
            </if>
            <if test="appid != null" >
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="mchId != null" >
                #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="subMchId != null" >
                #{subMchId,jdbcType=VARCHAR},
            </if>
            <if test="deviceInfo != null" >
                #{deviceInfo,jdbcType=VARCHAR},
            </if>
            <if test="transactionId != null" >
                #{transactionId,jdbcType=VARCHAR},
            </if>
            <if test="outTransactionId != null" >
                #{outTransactionId,jdbcType=VARCHAR},
            </if>
            <if test="openid != null" >
                #{openid,jdbcType=VARCHAR},
            </if>
            <if test="tradeType != null" >
                #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="tradeState != null" >
                #{tradeState,jdbcType=VARCHAR},
            </if>
            <if test="bankType != null" >
                #{bankType,jdbcType=VARCHAR},
            </if>
            <if test="feeType != null" >
                #{feeType,jdbcType=VARCHAR},
            </if>
            <if test="totalFee != null" >
                #{totalFee,jdbcType=DECIMAL},
            </if>
            <if test="couponAmount != null" >
                #{couponAmount,jdbcType=DECIMAL},
            </if>
            <if test="rfdApplyTime != null" >
                #{rfdApplyTime,jdbcType=VARCHAR},
            </if>
            <if test="rfdSuccTime != null" >
                #{rfdSuccTime,jdbcType=VARCHAR},
            </if>
            <if test="refundId != null" >
                #{refundId,jdbcType=VARCHAR},
            </if>
            <if test="outRefundNo != null" >
                #{outRefundNo,jdbcType=VARCHAR},
            </if>
            <if test="refundFee != null" >
                #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="couponRfdAmount != null" >
                #{couponRfdAmount,jdbcType=DECIMAL},
            </if>
            <if test="rfdType != null" >
                #{rfdType,jdbcType=VARCHAR},
            </if>
            <if test="rfdStatus != null" >
                #{rfdStatus,jdbcType=VARCHAR},
            </if>
            <if test="productNm != null" >
                #{productNm,jdbcType=VARCHAR},
            </if>
            <if test="attach != null" >
                #{attach,jdbcType=VARCHAR},
            </if>
            <if test="fee != null" >
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="rate != null" >
                #{rate,jdbcType=VARCHAR},
            </if>
            <if test="cashFeeType != null" >
                #{cashFeeType,jdbcType=VARCHAR},
            </if>
            <if test="cashFee != null" >
                #{cashFee,jdbcType=DECIMAL},
            </if>
            <if test="stlCurrType != null" >
                #{stlCurrType,jdbcType=VARCHAR},
            </if>
            <if test="stlCurrAmount != null" >
                #{stlCurrAmount,jdbcType=DECIMAL},
            </if>
            <if test="exchangeRate != null" >
                #{exchangeRate,jdbcType=VARCHAR},
            </if>
            <if test="rfdExchangeRate != null" >
                #{rfdExchangeRate,jdbcType=VARCHAR},
            </if>
            <if test="payerRfdAmount != null" >
                #{payerRfdAmount,jdbcType=DECIMAL},
            </if>
            <if test="payerRfdCurrType != null" >
                #{payerRfdCurrType,jdbcType=VARCHAR},
            </if>
            <if test="rfdCurrType != null" >
                #{rfdCurrType,jdbcType=VARCHAR},
            </if>
            <if test="rfdStlCurrType != null" >
                #{rfdStlCurrType,jdbcType=VARCHAR},
            </if>
            <if test="rfdStlAmount != null" >
                #{rfdStlAmount,jdbcType=DECIMAL},
            </if>
            <if test="chkBatNo != null" >
                #{chkBatNo,jdbcType=VARCHAR},
            </if>
            <if test="chkFilDt != null" >
                #{chkFilDt,jdbcType=DATE},
            </if>
            <if test="chkFilNm != null" >
                #{chkFilNm,jdbcType=VARCHAR},
            </if>
            <if test="chkSts != null" >
                #{chkSts,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="chkId != null" >
                #{chkId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <!-- 批量插入银行对账明细 -->
    <insert id="batchInsertCheckDO" parameterType="java.util.List" >
        insert into cpi_acc_wechat_ebankpay_refund (
        tran_time,appid,mch_id,sub_mch_id,device_info,transaction_id,out_transaction_id,openid,trade_type,trade_state,bank_type,
        fee_type,total_fee,coupon_amount,rfd_apply_time,rfd_succ_time,refund_id,out_refund_no,refund_fee,coupon_rfd_amount,rfd_type,
        rfd_status,product_nm,attach,fee,rate,cash_fee_type,cash_fee,stl_curr_type,stl_curr_amount,exchange_rate,rfd_exchange_rate,
        payer_rfd_amount,payer_rfd_curr_type,rfd_curr_type,rfd_stl_curr_type,rfd_stl_amount,
        CHK_BAT_NO,CHK_FIL_DT,CHK_FIL_NM,RUT_CORG,CORP_BUS_TYP,CORP_BUS_SUB_TYP,CHK_ID)
        <foreach collection="checkDOList" item="checkDO" index="index" separator="union all" >
            select
                #{checkDO.tranTime,jdbcType=VARCHAR},
                #{checkDO.appid,jdbcType=VARCHAR},
                #{checkDO.mchId,jdbcType=VARCHAR},
                #{checkDO.subMchId,jdbcType=VARCHAR},
                #{checkDO.deviceInfo,jdbcType=VARCHAR},
                #{checkDO.transactionId,jdbcType=VARCHAR},
                #{checkDO.outTransactionId,jdbcType=VARCHAR},
                #{checkDO.openid,jdbcType=VARCHAR},
                #{checkDO.tradeType,jdbcType=VARCHAR},
                #{checkDO.tradeState,jdbcType=VARCHAR},
                #{checkDO.bankType,jdbcType=VARCHAR},
                #{checkDO.feeType,jdbcType=VARCHAR},
                #{checkDO.totalFee,jdbcType=DECIMAL},
                #{checkDO.couponAmount,jdbcType=DECIMAL},
                #{checkDO.rfdApplyTime,jdbcType=VARCHAR},
                #{checkDO.rfdSuccTime,jdbcType=VARCHAR},
                #{checkDO.refundId,jdbcType=VARCHAR},
                #{checkDO.outRefundNo,jdbcType=VARCHAR},
                #{checkDO.refundFee,jdbcType=DECIMAL},
                #{checkDO.couponRfdAmount,jdbcType=DECIMAL},
                #{checkDO.rfdType,jdbcType=VARCHAR},
                #{checkDO.rfdStatus,jdbcType=VARCHAR},
                #{checkDO.productNm,jdbcType=VARCHAR},
                #{checkDO.attach,jdbcType=VARCHAR},
                #{checkDO.fee,jdbcType=DECIMAL},
                #{checkDO.rate,jdbcType=VARCHAR},
                #{checkDO.cashFeeType,jdbcType=VARCHAR},
                #{checkDO.cashFee,jdbcType=DECIMAL},
                #{checkDO.stlCurrType,jdbcType=VARCHAR},
                #{checkDO.stlCurrAmount,jdbcType=DECIMAL},
                #{checkDO.exchangeRate,jdbcType=VARCHAR},
                #{checkDO.rfdExchangeRate,jdbcType=VARCHAR},
                #{checkDO.payerRfdAmount,jdbcType=DECIMAL},
                #{checkDO.payerRfdCurrType,jdbcType=VARCHAR},
                #{checkDO.rfdCurrType,jdbcType=VARCHAR},
                #{checkDO.rfdStlCurrType,jdbcType=VARCHAR},
                #{checkDO.rfdStlAmount,jdbcType=DECIMAL},
                #{checkDO.chkBatNo,jdbcType=VARCHAR},
                #{checkDO.chkFilDt,jdbcType=DATE},
                #{checkDO.chkFilNm,jdbcType=VARCHAR},
                #{checkDO.rutCorg,jdbcType=VARCHAR},
                #{checkDO.corpBusTyp,jdbcType=VARCHAR},
                #{checkDO.corpBusSubTyp,jdbcType=VARCHAR},
                #{checkDO.chkId,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <!--根据对账批次号查询一条记录，判断该批次是否已插入对账明细数据-->
    <select id="getAccRefundDetailCount" resultType="java.lang.Integer" >
        select count(1) from cpi_acc_wechat_ebankpay_refund
        where CHK_BAT_NO = #{chkBatNo, jdbcType=VARCHAR}
        limit 1
    </select>

    <!--获取银行对账明细 List-->
    <select id="getAccRefundDetailList" resultMap="BaseResultMap" >
        select out_refund_no, refund_fee, rfd_status, CHK_BAT_NO
        from cpi_acc_wechat_ebankpay_refund
        where CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR}
        ORDER BY out_refund_no ASC
        limit #{beginNum,jdbcType=INTEGER}, #{countNum,jdbcType=INTEGER}
    </select>

    <!--更新银行明细的对账状态-->
    <update id="updateAccRefundChkSts" >
        update cpi_acc_wechat_ebankpay_refund
        set CHK_STS = #{chkSts,jdbcType=VARCHAR}
        where out_refund_no = #{checkKey,jdbcType=VARCHAR}
        and CHK_STS = '0'
    </update>
</mapper>