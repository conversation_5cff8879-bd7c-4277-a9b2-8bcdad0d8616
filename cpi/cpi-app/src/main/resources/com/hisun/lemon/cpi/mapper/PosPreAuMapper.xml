<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IPosPreAuDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.PosPreAuDO" >
        <id column="PRE_AU_NO" property="preAuNo" jdbcType="VARCHAR" />
        <result column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_NO_ENC" property="crdNoEnc" jdbcType="VARCHAR" />
        <result column="ORD_CCY" property="ordCcy" jdbcType="CHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
        <result column="ORD_STS" property="ordSts" jdbcType="CHAR" />
        <result column="TRM_NO" property="trmNo" jdbcType="VARCHAR" />
        <result column="AUT_CD" property="autCd" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        PRE_AU_NO, AC_DT, CRD_CORP_ORG, RUT_CORP_ORG, CRD_AC_TYP, CRD_NO_ENC, ORD_CCY, ORD_AMT,
        ORD_STS, TRM_NO, AUT_CD, RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_pos_pre_au
        where PRE_AU_NO = #{preAuNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_pos_pre_au
        where PRE_AU_NO = #{preAuNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.PosPreAuDO" >
        insert into cpi_pos_pre_au
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="preAuNo != null" >
                PRE_AU_NO,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC,
            </if>
            <if test="ordCcy != null" >
                ORD_CCY,
            </if>
            <if test="ordAmt != null" >
                ORD_AMT,
            </if>
            <if test="ordSts != null" >
                ORD_STS,
            </if>
            <if test="trmNo != null" >
                TRM_NO,
            </if>
            <if test="autCd != null" >
                AUT_CD,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="preAuNo != null" >
                #{preAuNo,jdbcType=VARCHAR},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="ordCcy != null" >
                #{ordCcy,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                #{ordSts,jdbcType=CHAR},
            </if>
            <if test="trmNo != null" >
                #{trmNo,jdbcType=VARCHAR},
            </if>
            <if test="autCd != null" >
                #{autCd,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.PosPreAuDO" >
        update cpi_pos_pre_au
        <set >
            <if test="acDt != null" >
                AC_DT = #{acDt,jdbcType=DATE},
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="ordCcy != null" >
                ORD_CCY = #{ordCcy,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                ORD_AMT = #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                ORD_STS = #{ordSts,jdbcType=CHAR},
            </if>
            <if test="trmNo != null" >
                TRM_NO = #{trmNo,jdbcType=VARCHAR},
            </if>
            <if test="autCd != null" >
                AUT_CD = #{autCd,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where PRE_AU_NO = #{preAuNo,jdbcType=VARCHAR}
    </update>

    <!--根据条件查询出匹配的数据-->
    <select id="selectByCondition" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_pos_pre_au
        <where>
            <if test="posPreAuDO.crdCorpOrg != null" >
                and CRD_CORP_ORG = #{posPreAuDO.crdCorpOrg,jdbcType=VARCHAR}
            </if>
            <if test="posPreAuDO.rutCorpOrg != null" >
                and RUT_CORP_ORG = #{posPreAuDO.rutCorpOrg,jdbcType=VARCHAR}
            </if>
            <if test="posPreAuDO.crdAcTyp != null" >
                and CRD_AC_TYP = #{posPreAuDO.crdAcTyp,jdbcType=CHAR}
            </if>
            <if test="posPreAuDO.crdNoEnc != null" >
                and CRD_NO_ENC = #{posPreAuDO.crdNoEnc,jdbcType=VARCHAR}
            </if>
            <if test="posPreAuDO.ordSts != null" >
                and ORD_STS = #{posPreAuDO.ordSts,jdbcType=CHAR}
            </if>
            <if test="posPreAuDO.ordAmt != null" >
                and ORD_AMT = #{posPreAuDO.ordAmt,jdbcType=DECIMAL}
            </if>
        </where>
        order by CREATE_TIME desc limit 1
    </select>
</mapper>