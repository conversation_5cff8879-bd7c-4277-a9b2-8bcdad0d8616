<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ISettlementControlDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.SettlementControlDO" >
        <id column="settle_batch_no" property="settleBatchNo" jdbcType="VARCHAR" />
        <result column="settle_date" property="settleDate" jdbcType="DATE" />
        <result column="settle_date_time" property="settleDateTime" jdbcType="VARCHAR" />
        <result column="rut_corg" property="rutCorg" jdbcType="VARCHAR" />
        <result column="settle_sts" property="settleSts" jdbcType="VARCHAR" />
        <result column="settle_flag" property="settleFlag" jdbcType="CHAR" />
        <result column="settle_file_exist" property="settleFileExist" jdbcType="CHAR" />
        <result column="settle_file_name" property="settleFileName" jdbcType="CHAR" />
        <result column="file_rcv_flag" property="fileRcvFlag" jdbcType="CHAR" />
        <result column="file_rcv_dt" property="fileRcvDt" jdbcType="DATE" />
        <result column="rmk" property="rmk" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        settle_batch_no, settle_date,settle_date_time, rut_corg, settle_sts,settle_flag, settle_file_exist, settle_file_name,
        file_rcv_flag, file_rcv_dt, rmk, create_time, modify_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_settlement_control
        where settle_batch_no = #{settleBatchNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_settlement_control
        where settle_batch_no = #{settleBatchNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.SettlementControlDO" >
        insert into cpi_settlement_control
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="settleBatchNo != null" >
                settle_batch_no,
            </if>
            <if test="settleDate != null" >
                settle_date,
            </if>
            <if test="settleDateTime != null" >
                settle_date_time,
            </if>
            <if test="rutCorg != null" >
                rut_corg,
            </if>
            <if test="settleSts != null" >
                settle_sts,
            </if>
            <if test="settleFlag != null" >
                settle_flag,
            </if>
            <if test="settleFileExist != null" >
                settle_file_exist,
            </if>
            <if test="settleFileName != null" >
                settle_file_name,
            </if>
            <if test="fileRcvFlag != null" >
                file_rcv_flag,
            </if>
            <if test="fileRcvDt != null" >
                file_rcv_dt,
            </if>
            <if test="rmk != null" >
                rmk,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="settleBatchNo != null" >
                #{settleBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="settleDate != null" >
                #{settleDate,jdbcType=DATE},
            </if>
            <if test="settleDateTime != null" >
                #{settleDateTime,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="settleSts != null" >
                #{settleSts,jdbcType=VARCHAR},
            </if>
            <if test="settleFlag != null" >
                #{settleFlag,jdbcType=CHAR},
            </if>
            <if test="settleFileExist != null" >
                #{settleFileExist,jdbcType=CHAR},
            </if>
            <if test="settleFileName != null" >
                #{settleFileName,jdbcType=CHAR},
            </if>
            <if test="fileRcvFlag != null" >
                #{fileRcvFlag,jdbcType=CHAR},
            </if>
            <if test="fileRcvDt != null" >
                #{fileRcvDt,jdbcType=DATE},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.SettlementControlDO" >
        update cpi_settlement_control
        <set >
            <if test="settleDate != null" >
                settle_date = #{settleDate,jdbcType=DATE},
            </if>
            <if test="settleDateTime != null" >
                settle_date_time = #{settleDateTime,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                rut_corg = #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="settleSts != null" >
                settle_sts = #{settleSts,jdbcType=VARCHAR},
            </if>
            <if test="settleFlag != null" >
                settle_flag = #{settleFlag,jdbcType=CHAR},
            </if>
            <if test="settleFileExist != null" >
                settle_file_exist = #{settleFileExist,jdbcType=CHAR},
            </if>
            <if test="settleFileName != null" >
                settle_file_name = #{settleFileName,jdbcType=CHAR},
            </if>
            <if test="fileRcvFlag != null" >
                file_rcv_flag = #{fileRcvFlag,jdbcType=CHAR},
            </if>
            <if test="fileRcvDt != null" >
                file_rcv_dt = #{fileRcvDt,jdbcType=DATE},
            </if>
            <if test="rmk != null" >
                rmk = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where settle_batch_no = #{settleBatchNo,jdbcType=VARCHAR}
    </update>

    <select id="getUnSettlementDate" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from
        cpi_settlement_control
        where (settle_flag = 'N'
        OR
        settle_date_time is NULL)
        and
        str_to_date(#{settleDateStr,jdbcType=VARCHAR},"%Y%m%d") <![CDATA[ > ]]> settle_date
    </select>

    <select id="getBySettleDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        cpi_settlement_control
        where
        str_to_date(#{settleDateStr,jdbcType=VARCHAR},"%Y%m%d") = settle_date
    </select>
</mapper>