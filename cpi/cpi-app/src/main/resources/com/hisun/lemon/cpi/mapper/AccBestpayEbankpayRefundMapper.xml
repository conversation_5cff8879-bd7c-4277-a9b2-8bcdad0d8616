<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.bestpay.IAccBestpayEbankpayRefundDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.bestpay.AccBestpayEbankpayRefundDO" >
        <result column="merc_id" property="mercId" jdbcType="VARCHAR" />
        <result column="bnk_chk_key" property="bnkChkKey" jdbcType="VARCHAR" />
        <result column="plat_jrn_no" property="platJrnNo" jdbcType="VARCHAR" />
        <result column="plat_ord_no" property="platOrdNo" jdbcType="VARCHAR" />
        <result column="ord_cyy" property="ordCyy" jdbcType="VARCHAR" />
        <result column="ord_foreign_amt" property="ordForeignAmt" jdbcType="DECIMAL" />
        <result column="ord_amt" property="ordAmt" jdbcType="DECIMAL" />
        <result column="exchange_rate" property="exchangeRate" jdbcType="DECIMAL" />
        <result column="CHK_BAT_NO" property="chkBatNo" jdbcType="VARCHAR" />
        <result column="RUT_CORG" property="rutCorg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="CHK_FIL_DT" property="chkFilDt" jdbcType="DATE" />
        <result column="CHK_FIL_NM" property="chkFilNm" jdbcType="VARCHAR" />
        <result column="CHK_ID" property="chkId" jdbcType="VARCHAR" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="CHK_STS" property="chkSts" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.bestpay.AccBestpayEbankpayRefundDO" >
        insert into cpi_acc_bestpay_ebankpay_refund
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="mercId != null" >
                merc_id,
            </if>
            <if test="bnkChkKey != null" >
                bnk_chk_key,
            </if>
            <if test="platJrnNo != null" >
                plat_jrn_no,
            </if>
            <if test="platOrdNo != null" >
                plat_ord_no,
            </if>
            <if test="ordCyy != null" >
                ord_cyy,
            </if>
            <if test="ordForeignAmt != null" >
                ord_foreign_amt,
            </if>
            <if test="ordAmt != null" >
                ord_amt,
            </if>
            <if test="exchangeRate != null" >
                exchange_rate,
            </if>
            <if test="chkBatNo != null" >
                CHK_BAT_NO,
            </if>
            <if test="rutCorg != null" >
                RUT_CORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="chkFilDt != null" >
                CHK_FIL_DT,
            </if>
            <if test="chkFilNm != null" >
                CHK_FIL_NM,
            </if>
            <if test="chkId != null" >
                CHK_ID,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="chkSts != null" >
                CHK_STS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="mercId != null" >
                #{mercId,jdbcType=VARCHAR},
            </if>
            <if test="bnkChkKey != null" >
                #{bnkChkKey,jdbcType=VARCHAR},
            </if>
            <if test="platJrnNo != null" >
                #{platJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="platOrdNo != null" >
                #{platOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="ordCyy != null" >
                #{ordCyy,jdbcType=VARCHAR},
            </if>
            <if test="ordForeignAmt != null" >
                #{ordForeignAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordAmt != null" >
                #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="exchangeRate != null" >
                #{exchangeRate,jdbcType=DECIMAL},
            </if>
            <if test="chkBatNo != null" >
                #{chkBatNo,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="chkFilDt != null" >
                #{chkFilDt,jdbcType=DATE},
            </if>
            <if test="chkFilNm != null" >
                #{chkFilNm,jdbcType=VARCHAR},
            </if>
            <if test="chkId != null" >
                #{chkId,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="chkSts != null" >
                #{chkSts,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 批量插入银行对账明细 -->
    <insert id="batchInsertCheckDO" parameterType="java.util.List" >
        insert into cpi_acc_bestpay_ebankpay_refund (
        CHK_ID, merc_id, bnk_chk_key, plat_jrn_no, plat_ord_no, ord_cyy, ord_foreign_amt,
        ord_amt, exchange_rate, CHK_BAT_NO, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP,
        CHK_FIL_DT, CHK_FIL_NM)
        <foreach collection="checkDOList" item="checkDO" index="index" separator="union all" >
            select
            #{checkDO.chkId,jdbcType=VARCHAR},
            #{checkDO.mercId,jdbcType=VARCHAR},
            #{checkDO.bnkChkKey,jdbcType=VARCHAR},
            #{checkDO.platJrnNo,jdbcType=VARCHAR},
            #{checkDO.platOrdNo,jdbcType=VARCHAR},
            #{checkDO.ordCyy,jdbcType=VARCHAR},
            #{checkDO.ordForeignAmt,jdbcType=DECIMAL},
            #{checkDO.ordAmt,jdbcType=DECIMAL},
            #{checkDO.exchangeRate,jdbcType=VARCHAR},
            #{checkDO.chkBatNo,jdbcType=VARCHAR},
            #{checkDO.rutCorg,jdbcType=VARCHAR},
            #{checkDO.corpBusTyp,jdbcType=VARCHAR},
            #{checkDO.corpBusSubTyp,jdbcType=VARCHAR},
            #{checkDO.chkFilDt,jdbcType=DATE},
            #{checkDO.chkFilNm,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <!--根据对账批次号查询一条记录，判断该批次是否已插入对账明细数据-->
    <select id="getAccRefundDetailCount" resultType="java.lang.Integer" >
        select count(1) from cpi_acc_bestpay_ebankpay_refund
        where CHK_BAT_NO = #{chkBatNo, jdbcType=VARCHAR}
        limit 1
    </select>

    <!--获取银行对账明细 List-->
    <select id="getAccRefundDetailList" resultMap="BaseResultMap" >
        select plat_jrn_no, plat_ord_no, ord_foreign_amt, CHK_BAT_NO, RUT_CORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP
        from cpi_acc_bestpay_ebankpay_refund
        where CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR}
        ORDER BY plat_ord_no ASC
        limit #{beginNum,jdbcType=INTEGER}, #{countNum,jdbcType=INTEGER}
    </select>

    <!--更新银行明细的对账状态-->
    <update id="updateAccRefundChkSts" >
        update cpi_acc_bestpay_ebankpay_refund
        set CHK_STS = #{chkSts,jdbcType=VARCHAR}
        where plat_ord_no = #{checkKey,jdbcType=VARCHAR}
        and CHK_STS = '0'
    </update>
</mapper>