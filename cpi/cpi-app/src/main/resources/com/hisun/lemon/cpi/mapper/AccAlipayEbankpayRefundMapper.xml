<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.alipay.IAccAlipayEbankpayRefundDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.alipay.AccAlipayEbankpayRefundDO" >
        <result column="partner_transaction_id" property="partnerTransactionId" jdbcType="VARCHAR" />
        <result column="transaction_id" property="transactionId" jdbcType="VARCHAR" />
        <result column="transaction_amount" property="transactionAmount" jdbcType="DECIMAL" />
        <result column="charge_amount" property="chargeAmount" jdbcType="DECIMAL" />
        <result column="currency" property="currency" jdbcType="VARCHAR" />
        <result column="payment_time" property="paymentTime" jdbcType="VARCHAR" />
        <result column="transaction_type" property="transactionType" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="secondary_merchant_industry" property="secondaryMerchantIndustry" jdbcType="VARCHAR" />
        <result column="secondary_merchant_name" property="secondaryMerchantName" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="order_scene" property="orderScene" jdbcType="VARCHAR" />
        <result column="trans_currency" property="transCurrency" jdbcType="VARCHAR" />
        <result column="trans_forex_rate" property="transForexRate" jdbcType="VARCHAR" />
        <result column="trans_amount" property="transAmount" jdbcType="VARCHAR" />
        <result column="chk_bat_no" property="chkBatNo" jdbcType="VARCHAR" />
        <result column="chk_fil_dt" property="chkFilDt" jdbcType="DATE" />
        <result column="chk_fil_nm" property="chkFilNm" jdbcType="VARCHAR" />
        <result column="chk_sts" property="chkSts" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="rut_corg" property="rutCorg" jdbcType="VARCHAR" />
        <result column="corp_bus_typ" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="corp_bus_sub_typ" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="chk_id" property="chkId" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.alipay.AccAlipayEbankpayRefundDO" >
        insert into cpi_acc_alipay_ebankpay_refund
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="partnerTransactionId != null" >
                partner_transaction_id,
            </if>
            <if test="transactionId != null" >
                transaction_id,
            </if>
            <if test="transactionAmount != null" >
                transaction_amount,
            </if>
            <if test="chargeAmount != null" >
                charge_amount,
            </if>
            <if test="currency != null" >
                currency,
            </if>
            <if test="paymentTime != null" >
                payment_time,
            </if>
            <if test="transactionType != null" >
                transaction_type,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="secondaryMerchantIndustry != null" >
                secondary_merchant_industry,
            </if>
            <if test="secondaryMerchantName != null" >
                secondary_merchant_name,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="orderScene != null" >
                order_scene,
            </if>
            <if test="transCurrency != null" >
                trans_currency,
            </if>
            <if test="transForexRate != null" >
                trans_forex_rate,
            </if>
            <if test="transAmount != null" >
                trans_amount,
            </if>
            <if test="chkBatNo != null" >
                chk_bat_no,
            </if>
            <if test="chkFilDt != null" >
                chk_fil_dt,
            </if>
            <if test="chkFilNm != null" >
                chk_fil_nm,
            </if>
            <if test="chkSts != null" >
                chk_sts,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
            <if test="rutCorg != null" >
                rut_corg,
            </if>
            <if test="corpBusTyp != null" >
                corp_bus_typ,
            </if>
            <if test="corpBusSubTyp != null" >
                corp_bus_sub_typ,
            </if>
            <if test="chkId != null" >
                chk_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="partnerTransactionId != null" >
                #{partnerTransactionId,jdbcType=VARCHAR},
            </if>
            <if test="transactionId != null" >
                #{transactionId,jdbcType=VARCHAR},
            </if>
            <if test="transactionAmount != null" >
                #{transactionAmount,jdbcType=DECIMAL},
            </if>
            <if test="chargeAmount != null" >
                #{chargeAmount,jdbcType=DECIMAL},
            </if>
            <if test="currency != null" >
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="paymentTime != null" >
                #{paymentTime,jdbcType=VARCHAR},
            </if>
            <if test="transactionType != null" >
                #{transactionType,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantIndustry != null" >
                #{secondaryMerchantIndustry,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantName != null" >
                #{secondaryMerchantName,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="orderScene != null" >
                #{orderScene,jdbcType=VARCHAR},
            </if>
            <if test="transCurrency != null" >
                #{transCurrency,jdbcType=VARCHAR},
            </if>
            <if test="transForexRate != null" >
                #{transForexRate,jdbcType=VARCHAR},
            </if>
            <if test="transAmount != null" >
                #{transAmount,jdbcType=VARCHAR},
            </if>
            <if test="chkBatNo != null" >
                #{chkBatNo,jdbcType=VARCHAR},
            </if>
            <if test="chkFilDt != null" >
                #{chkFilDt,jdbcType=DATE},
            </if>
            <if test="chkFilNm != null" >
                #{chkFilNm,jdbcType=VARCHAR},
            </if>
            <if test="chkSts != null" >
                #{chkSts,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="chkId != null" >
                #{chkId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <!-- 批量插入银行对账明细 -->
    <insert id="batchInsertCheckDO" parameterType="java.util.List" >
        insert into cpi_acc_alipay_ebankpay_refund (
        partner_transaction_id,transaction_id,transaction_amount,charge_amount,currency,
        payment_time,transaction_type,remark,secondary_merchant_industry,secondary_merchant_name,
        operator_name,order_scene,trans_currency,trans_forex_rate,trans_amount,
        chk_bat_no,chk_fil_dt,chk_fil_nm,rut_corg,corp_bus_typ,corp_bus_sub_typ,chk_id)
        <foreach collection="checkDOList" item="checkDO" index="index" separator="union all" >
            select
                #{checkDO.partnerTransactionId,jdbcType=VARCHAR},
                #{checkDO.transactionId,jdbcType=VARCHAR},
                #{checkDO.transactionAmount,jdbcType=DECIMAL},
                #{checkDO.chargeAmount,jdbcType=DECIMAL},
                #{checkDO.currency,jdbcType=VARCHAR},
                #{checkDO.paymentTime,jdbcType=VARCHAR},
                #{checkDO.transactionType,jdbcType=VARCHAR},
                #{checkDO.remark,jdbcType=VARCHAR},
                #{checkDO.secondaryMerchantIndustry,jdbcType=VARCHAR},
                #{checkDO.secondaryMerchantName,jdbcType=VARCHAR},
                #{checkDO.operatorName,jdbcType=VARCHAR},
                #{checkDO.orderScene,jdbcType=VARCHAR},
                #{checkDO.transCurrency,jdbcType=VARCHAR},
                #{checkDO.transForexRate,jdbcType=VARCHAR},
                #{checkDO.transAmount,jdbcType=VARCHAR},
                #{checkDO.chkBatNo,jdbcType=VARCHAR},
                #{checkDO.chkFilDt,jdbcType=DATE},
                #{checkDO.chkFilNm,jdbcType=VARCHAR},
                #{checkDO.rutCorg,jdbcType=VARCHAR},
                #{checkDO.corpBusTyp,jdbcType=VARCHAR},
                #{checkDO.corpBusSubTyp,jdbcType=VARCHAR},
                #{checkDO.chkId,jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <!--根据对账批次号查询一条记录，判断该批次是否已插入对账明细数据-->
    <select id="getAccRefundDetailCount" resultType="java.lang.Integer" >
        select count(1) from cpi_acc_alipay_ebankpay_refund
        where CHK_BAT_NO = #{chkBatNo, jdbcType=VARCHAR}
        limit 1
    </select>

    <!--获取银行对账明细 List-->
    <select id="getAccRefundDetailList" resultMap="BaseResultMap" >
        select partner_transaction_id, transaction_amount, transaction_type, chk_bat_no
        from cpi_acc_alipay_ebankpay_refund
        where CHK_BAT_NO = #{chkBatNo,jdbcType=VARCHAR}
        ORDER BY partner_transaction_id ASC
        limit #{beginNum,jdbcType=INTEGER}, #{countNum,jdbcType=INTEGER}
    </select>

    <!--更新银行明细的对账状态-->
    <update id="updateAccRefundChkSts" >
        update cpi_acc_alipay_ebankpay_refund
        set chk_sts = #{chkSts,jdbcType=VARCHAR}
        where partner_transaction_id = #{checkKey,jdbcType=VARCHAR}
        and chk_sts = '0'
    </update>
</mapper>