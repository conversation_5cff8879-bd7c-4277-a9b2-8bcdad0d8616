<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IFundOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.FundOrderDO" >
        <id column="FUD_ORD_NO" property="fudOrdNo" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="ORD_CCY" property="ordCcy" jdbcType="CHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
        <result column="ORD_RFD_AMT" property="ordRfdAmt" jdbcType="DECIMAL" />
        <result column="ORD_STS" property="ordSts" jdbcType="CHAR" />
        <result column="ORD_SUCC_DT" property="ordSuccDt" jdbcType="DATE" />
        <result column="ORD_SUCC_TM" property="ordSuccTm" jdbcType="TIME" />
        <result column="USER_TYP" property="userTyp" jdbcType="CHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_NO_ENC" property="crdNoEnc" jdbcType="VARCHAR" />
        <result column="CRD_USR_NM" property="crdUsrNm" jdbcType="VARCHAR" />
        <result column="CRD_NO_LAST" property="crdNoLast" jdbcType="CHAR" />
        <result column="ID_TYP" property="idTyp" jdbcType="CHAR" />
        <result column="ID_NO_ENC" property="idNoEnc" jdbcType="VARCHAR" />
        <result column="BNK_PSN_FLG" property="bnkPsnFlg" jdbcType="CHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="VARCHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="VARCHAR" />
        <result column="REQ_ORD_NO" property="reqOrdNo" jdbcType="VARCHAR" />
        <result column="REQ_ORD_DT" property="reqOrdDt" jdbcType="DATE" />
        <result column="REQ_ORD_TM" property="reqOrdTm" jdbcType="TIME" />
        <result column="NTF_STS" property="ntfSts" jdbcType="CHAR" />
        <result column="NTF_DT" property="ntfDt" jdbcType="DATE" />
        <result column="NTF_TM" property="ntfTm" jdbcType="TIME" />
        <result column="NTF_RSP_CD" property="ntfRspCd" jdbcType="VARCHAR" />
        <result column="NTF_RSP_MSG" property="ntfRspMsg" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        FUD_ORD_NO, ORD_DT, AC_DT, ORD_TM, ORD_CCY, ORD_AMT, ORD_RFD_AMT, ORD_STS, ORD_SUCC_DT,
        ORD_SUCC_TM, USER_TYP, USER_ID, MBL_NO, CRD_CORP_ORG, CRD_AC_TYP, CRD_NO_ENC, CRD_USR_NM, 
        CRD_NO_LAST, ID_TYP, ID_NO_ENC, BNK_PSN_FLG, RUT_CORP_ORG, CORP_BUS_TYP, CORP_BUS_SUB_TYP, 
        REQ_ORD_NO, REQ_ORD_DT, REQ_ORD_TM, NTF_STS, NTF_DT, NTF_TM, NTF_RSP_CD, NTF_RSP_MSG, 
        RMK, CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_fund_order
        where FUD_ORD_NO = #{fudOrdNo,jdbcType=VARCHAR} OR REQ_ORD_NO = #{fudOrdNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_fund_order
        where FUD_ORD_NO = #{fudOrdNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.FundOrderDO" >
        insert into cpi_fund_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="fudOrdNo != null" >
                FUD_ORD_NO,
            </if>
            <if test="ordDt != null" >
                ORD_DT,
            </if>
            <if test="ordTm != null" >
                ORD_TM,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="ordCcy != null" >
                ORD_CCY,
            </if>
            <if test="ordAmt != null" >
                ORD_AMT,
            </if>
            <if test="ordRfdAmt != null" >
                ORD_RFD_AMT,
            </if>
            <if test="ordSts != null" >
                ORD_STS,
            </if>
            <if test="ordSuccDt != null" >
                ORD_SUCC_DT,
            </if>
            <if test="ordSuccTm != null" >
                ORD_SUCC_TM,
            </if>
            <if test="userTyp != null" >
                USER_TYP,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC,
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM,
            </if>
            <if test="crdNoLast != null" >
                CRD_NO_LAST,
            </if>
            <if test="idTyp != null" >
                ID_TYP,
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC,
            </if>
            <if test="bnkPsnFlg != null" >
                BNK_PSN_FLG,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="reqOrdNo != null" >
                REQ_ORD_NO,
            </if>
            <if test="reqOrdDt != null" >
                REQ_ORD_DT,
            </if>
            <if test="reqOrdTm != null" >
                REQ_ORD_TM,
            </if>
            <if test="ntfSts != null" >
                NTF_STS,
            </if>
            <if test="ntfDt != null" >
                NTF_DT,
            </if>
            <if test="ntfTm != null" >
                NTF_TM,
            </if>
            <if test="ntfRspCd != null" >
                NTF_RSP_CD,
            </if>
            <if test="ntfRspMsg != null" >
                NTF_RSP_MSG,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="firstAuditUser != null" >
                FIRST_AUDIT_USER,
            </if>
            <if test="firstAuditTime != null" >
                FIRST_AUDIT_TIME,
            </if>
            <if test="firstAuditResult != null" >
                FIRST_AUDIT_RESULT,
            </if>
            <if test="firstAuditOpinion != null" >
                FIRST_AUDIT_OPINION,
            </if>
            <if test="secondAuditUser != null" >
                SECOND_AUDIT_USER,
            </if>
            <if test="secondAuditTime != null" >
                SECOND_AUDIT_TIME,
            </if>
            <if test="secondAuditResult != null" >
                SECOND_AUDIT_RESULT,
            </if>
            <if test="secondAuditOpinion != null" >
                SECOND_AUDIT_OPINION,
            </if>
            <if test="executeTime != null" >
                EXECUTE_TIME,
            </if>
            <if test="rejectReason != null" >
                REJECT_REASON
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="fudOrdNo != null" >
                #{fudOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                #{ordTm,jdbcType=TIME},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="ordCcy != null" >
                #{ordCcy,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordRfdAmt != null" >
                #{ordRfdAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                #{ordSts,jdbcType=CHAR},
            </if>
            <if test="ordSuccDt != null" >
                #{ordSuccDt,jdbcType=DATE},
            </if>
            <if test="ordSuccTm != null" >
                #{ordSuccTm,jdbcType=TIME},
            </if>
            <if test="userTyp != null" >
                #{userTyp,jdbcType=CHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdUsrNm != null" >
                #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="crdNoLast != null" >
                #{crdNoLast,jdbcType=CHAR},
            </if>
            <if test="idTyp != null" >
                #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="bnkPsnFlg != null" >
                #{bnkPsnFlg,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdNo != null" >
                #{reqOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdDt != null" >
                #{reqOrdDt,jdbcType=DATE},
            </if>
            <if test="reqOrdTm != null" >
                #{reqOrdTm,jdbcType=TIME},
            </if>
            <if test="ntfSts != null" >
                #{ntfSts,jdbcType=CHAR},
            </if>
            <if test="ntfDt != null" >
                #{ntfDt,jdbcType=DATE},
            </if>
            <if test="ntfTm != null" >
                #{ntfTm,jdbcType=TIME},
            </if>
            <if test="ntfRspCd != null" >
                #{ntfRspCd,jdbcType=VARCHAR},
            </if>
            <if test="ntfRspMsg != null" >
                #{ntfRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.FundOrderDO" >
        update cpi_fund_order
        <set >
            <if test="ordDt != null" >
                ORD_DT = #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                ORD_TM = #{ordTm,jdbcType=TIME},
            </if>
            <if test="acDt != null" >
                AC_DT = #{acDt,jdbcType=DATE},
            </if>
            <if test="ordCcy != null" >
                ORD_CCY = #{ordCcy,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                ORD_AMT = #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordRfdAmt != null" >
                ORD_RFD_AMT = #{ordRfdAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                ORD_STS = #{ordSts,jdbcType=CHAR},
            </if>
            <if test="ordSuccDt != null" >
                ORD_SUCC_DT = #{ordSuccDt,jdbcType=DATE},
            </if>
            <if test="ordSuccTm != null" >
                ORD_SUCC_TM = #{ordSuccTm,jdbcType=TIME},
            </if>
            <if test="userTyp != null" >
                USER_TYP = #{userTyp,jdbcType=CHAR},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                MBL_NO = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM = #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="crdNoLast != null" >
                CRD_NO_LAST = #{crdNoLast,jdbcType=CHAR},
            </if>
            <if test="idTyp != null" >
                ID_TYP = #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC = #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="bnkPsnFlg != null" >
                BNK_PSN_FLG = #{bnkPsnFlg,jdbcType=CHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=VARCHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdNo != null" >
                REQ_ORD_NO = #{reqOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="reqOrdDt != null" >
                REQ_ORD_DT = #{reqOrdDt,jdbcType=DATE},
            </if>
            <if test="reqOrdTm != null" >
                REQ_ORD_TM = #{reqOrdTm,jdbcType=TIME},
            </if>
            <if test="ntfSts != null" >
                NTF_STS = #{ntfSts,jdbcType=CHAR},
            </if>
            <if test="ntfDt != null" >
                NTF_DT = #{ntfDt,jdbcType=DATE},
            </if>
            <if test="ntfTm != null" >
                NTF_TM = #{ntfTm,jdbcType=TIME},
            </if>
            <if test="ntfRspCd != null" >
                NTF_RSP_CD = #{ntfRspCd,jdbcType=VARCHAR},
            </if>
            <if test="ntfRspMsg != null" >
                NTF_RSP_MSG = #{ntfRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="firstAuditUser != null" >
                FIRST_AUDIT_USER = #{firstAuditUser,jdbcType=VARCHAR},
            </if>
            <if test="firstAuditTime != null" >
                FIRST_AUDIT_TIME = #{firstAuditTime,jdbcType=DATE},
            </if>
            <if test="firstAuditResult != null" >
                FIRST_AUDIT_RESULT = #{firstAuditResult,jdbcType=VARCHAR},
            </if>
            <if test="firstAuditOpinion != null" >
                FIRST_AUDIT_OPINION = #{firstAuditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="secondAuditUser != null" >
                SECOND_AUDIT_USER = #{secondAuditUser},
            </if>
            <if test="secondAuditTime != null" >
                SECOND_AUDIT_TIME = #{secondAuditTime,jdbcType=DATE},
            </if>
            <if test="secondAuditResult != null" >
                SECOND_AUDIT_RESULT = #{secondAuditResult,jdbcType=VARCHAR},
            </if>
            <if test="secondAuditOpinion != null" >
                SECOND_AUDIT_OPINION = #{secondAuditOpinion,jdbcType=VARCHAR},
            </if>
            <if test="executeTime != null" >
                EXECUTE_TIME = #{executeTime,jdbcType=DATE},
            </if>
            <if test="rejectReason != null" >
                REJECT_REASON = #{rejectReason,jdbcType=VARCHAR}
            </if>
        </set>
        where FUD_ORD_NO = #{fudOrdNo,jdbcType=VARCHAR}
    </update>

    <!--获取银行对账明细 List-->
    <select id="getFundOrderList" resultMap="BaseResultMap" >
        select FUD_ORD_NO,ORD_AMT,ORD_STS,REQ_ORD_NO
        from cpi_fund_order
        where AC_DT = #{ordDt,jdbcType=DATE}
        ORDER BY FUD_ORD_NO ASC
        limit #{beginNum,jdbcType=INTEGER}, #{countNum,jdbcType=INTEGER}
    </select>
</mapper>