<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ICardBinDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.CardBinDO" >
        <id column="BIN_ID" property="binId" jdbcType="VARCHAR" />
        <result column="CRD_BIN" property="crdBin" jdbcType="VARCHAR" />
        <result column="CAP_CORG" property="capCorg" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_LTH" property="crdLth" jdbcType="INTEGER" />
        <result column="OPR_ID" property="oprId" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        BIN_ID, CRD_BIN, CAP_CORG, CRD_AC_TYP, CRD_LTH, OPR_ID, CREATE_TIME, MODIFY_TIME, 
        TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_card_bin
        where BIN_ID = #{binId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_card_bin
        where BIN_ID = #{binId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.CardBinDO" >
        insert into cpi_card_bin
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="binId != null" >
                BIN_ID,
            </if>
            <if test="crdBin != null" >
                CRD_BIN,
            </if>
            <if test="capCorg != null" >
                CAP_CORG,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdLth != null" >
                CRD_LTH,
            </if>
            <if test="oprId != null" >
                OPR_ID,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="binId != null" >
                #{binId,jdbcType=VARCHAR},
            </if>
            <if test="crdBin != null" >
                #{crdBin,jdbcType=VARCHAR},
            </if>
            <if test="capCorg != null" >
                #{capCorg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdLth != null" >
                #{crdLth,jdbcType=INTEGER},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.CardBinDO" >
        update cpi_card_bin
        <set >
            <if test="crdBin != null" >
                CRD_BIN = #{crdBin,jdbcType=VARCHAR},
            </if>
            <if test="capCorg != null" >
                CAP_CORG = #{capCorg,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdLth != null" >
                CRD_LTH = #{crdLth,jdbcType=INTEGER},
            </if>
            <if test="oprId != null" >
                OPR_ID = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where BIN_ID = #{binId,jdbcType=VARCHAR}
    </update>

    <select id="selectByCardBin" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from cpi_card_bin
        where CRD_LTH = #{crdLth,jdbcType=INTEGER}
          and #{crdBin,jdbcType=VARCHAR}
          LIKE CONCAT(CRD_BIN, '%')
    </select>
</mapper>