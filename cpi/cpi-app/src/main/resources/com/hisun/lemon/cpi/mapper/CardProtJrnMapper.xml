<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ICardProtJrnDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.CardProtJrnDO" >
        <id column="JRN_NO" property="jrnNo" jdbcType="VARCHAR" />
        <result column="TX_DT" property="txDt" jdbcType="DATE" />
        <result column="TX_TM" property="txTm" jdbcType="TIME" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="USER_NM" property="userNm" jdbcType="VARCHAR" />
        <result column="ID_TYP" property="idTyp" jdbcType="CHAR" />
        <result column="ID_NO_ENC" property="idNoEnc" jdbcType="VARCHAR" />
        <result column="CRD_NO_ENC" property="crdNoEnc" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="BND_FLG" property="bndFlg" jdbcType="CHAR" />
        <result column="AUTH_FLAG" property="authFlag" jdbcType="CHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="CRD_AC_TYP" property="crdAcTyp" jdbcType="CHAR" />
        <result column="CRD_USR_NM" property="crdUsrNm" jdbcType="VARCHAR" />
        <result column="CRD_CVV2_ENC" property="crdCvv2Enc" jdbcType="VARCHAR" />
        <result column="CRD_EXP_DT_ENC" property="crdExpDtEnc" jdbcType="VARCHAR" />
        <result column="SIGN_AGRNO" property="signAgrno" jdbcType="VARCHAR" />
        <result column="ORG_JRN_NO" property="orgJrnNo" jdbcType="VARCHAR" />
        <result column="ORG_RSP_CD" property="orgRspCd" jdbcType="VARCHAR" />
        <result column="ORG_RSP_MSG" property="orgRspMsg" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        JRN_NO, TX_DT, TX_TM, CORP_BUS_TYP, CORP_BUS_SUB_TYP, CRD_CORP_ORG, USER_ID, USER_NM, 
        ID_TYP, ID_NO_ENC, CRD_NO_ENC, RUT_CORP_ORG, BND_FLG, AUTH_FLAG, MBL_NO, CRD_AC_TYP, CRD_USR_NM,
        CRD_CVV2_ENC, CRD_EXP_DT_ENC, SIGN_AGRNO, ORG_JRN_NO, ORG_RSP_CD, ORG_RSP_MSG, RMK, 
        CREATE_TIME, MODIFY_TIME, TM_SMP
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_card_prot_jrn
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_card_prot_jrn
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.CardProtJrnDO" >
        insert into cpi_card_prot_jrn
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                JRN_NO,
            </if>
            <if test="txDt != null" >
                TX_DT,
            </if>
            <if test="txTm != null" >
                TX_TM,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="userNm != null" >
                USER_NM,
            </if>
            <if test="idTyp != null" >
                ID_TYP,
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC,
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="bndFlg != null" >
                BND_FLG,
            </if>
            <if test="authFlag != null" >
                AUTH_FLAG,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP,
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM,
            </if>
            <if test="crdCvv2Enc != null" >
                CRD_CVV2_ENC,
            </if>
            <if test="crdExpDtEnc != null" >
                CRD_EXP_DT_ENC,
            </if>
            <if test="signAgrno != null" >
                SIGN_AGRNO,
            </if>
            <if test="orgJrnNo != null" >
                ORG_JRN_NO,
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD,
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="txDt != null" >
                #{txDt,jdbcType=DATE},
            </if>
            <if test="txTm != null" >
                #{txTm,jdbcType=TIME},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userNm != null" >
                #{userNm,jdbcType=VARCHAR},
            </if>
            <if test="idTyp != null" >
                #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdNoEnc != null" >
                #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="bndFlg != null" >
                #{bndFlg,jdbcType=CHAR},
            </if>
            <if test="authFlag != null" >
                #{authFlag,jdbcType=CHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdUsrNm != null" >
                #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="crdCvv2Enc != null" >
                #{crdCvv2Enc,jdbcType=VARCHAR},
            </if>
            <if test="crdExpDtEnc != null" >
                #{crdExpDtEnc,jdbcType=VARCHAR},
            </if>
            <if test="signAgrno != null" >
                #{signAgrno,jdbcType=VARCHAR},
            </if>
            <if test="orgJrnNo != null" >
                #{orgJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgRspCd != null" >
                #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.CardProtJrnDO" >
        update cpi_card_prot_jrn
        <set >
            <if test="txDt != null" >
                TX_DT = #{txDt,jdbcType=DATE},
            </if>
            <if test="txTm != null" >
                TX_TM = #{txTm,jdbcType=TIME},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userNm != null" >
                USER_NM = #{userNm,jdbcType=VARCHAR},
            </if>
            <if test="idTyp != null" >
                ID_TYP = #{idTyp,jdbcType=CHAR},
            </if>
            <if test="idNoEnc != null" >
                ID_NO_ENC = #{idNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="crdNoEnc != null" >
                CRD_NO_ENC = #{crdNoEnc,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="bndFlg != null" >
                BND_FLG = #{bndFlg,jdbcType=CHAR},
            </if>
            <if test="authFlag != null" >
                AUTH_FLAG = #{authFlag,jdbcType=CHAR},
            </if>
            <if test="mblNo != null" >
                MBL_NO = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="crdAcTyp != null" >
                CRD_AC_TYP = #{crdAcTyp,jdbcType=CHAR},
            </if>
            <if test="crdUsrNm != null" >
                CRD_USR_NM = #{crdUsrNm,jdbcType=VARCHAR},
            </if>
            <if test="crdCvv2Enc != null" >
                CRD_CVV2_ENC = #{crdCvv2Enc,jdbcType=VARCHAR},
            </if>
            <if test="crdExpDtEnc != null" >
                CRD_EXP_DT_ENC = #{crdExpDtEnc,jdbcType=VARCHAR},
            </if>
            <if test="signAgrno != null" >
                SIGN_AGRNO = #{signAgrno,jdbcType=VARCHAR},
            </if>
            <if test="orgJrnNo != null" >
                ORG_JRN_NO = #{orgJrnNo,jdbcType=VARCHAR},
            </if>
            <if test="orgRspCd != null" >
                ORG_RSP_CD = #{orgRspCd,jdbcType=VARCHAR},
            </if>
            <if test="orgRspMsg != null" >
                ORG_RSP_MSG = #{orgRspMsg,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where JRN_NO = #{jrnNo,jdbcType=VARCHAR}
    </update>
</mapper>