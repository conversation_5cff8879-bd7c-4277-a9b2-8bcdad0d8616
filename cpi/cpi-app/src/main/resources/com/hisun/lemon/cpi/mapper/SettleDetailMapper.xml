<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.ISettleDetailDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.SettleDetailDO" >
        <id column="jrn_no" property="jrnNo" jdbcType="VARCHAR" />
        <result column="update_date" property="updateDate" jdbcType="DATE" />
        <result column="update_time" property="updateTime" jdbcType="TIME" />
        <result column="pay_batch_no" property="payBatchNo" jdbcType="VARCHAR" />
        <result column="settle_date" property="settleDate" jdbcType="DATE" />
        <result column="settle_datetime" property="settleDatetime" jdbcType="TIMESTAMP" />
        <result column="start_date" property="startDate" jdbcType="DATE" />
        <result column="end_date" property="endDate" jdbcType="DATE" />
        <result column="settle_fee" property="settleFee" jdbcType="DECIMAL" />
        <result column="unsettle_fee" property="unsettleFee" jdbcType="DECIMAL" />
        <result column="settle_fee_type" property="settleFeeType" jdbcType="VARCHAR" />
        <result column="pay_fee" property="payFee" jdbcType="DECIMAL" />
        <result column="refund_fee" property="refundFee" jdbcType="DECIMAL" />
        <result column="pay_net_fee" property="payNetFee" jdbcType="DECIMAL" />
        <result column="poundage_fee" property="poundageFee" jdbcType="DECIMAL" />
        <result column="tot_settle_fee" property="totSettleFee" jdbcType="DECIMAL" />
        <result column="tot_unsettle_fee" property="totUnsettleFee" jdbcType="DECIMAL" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="appid" property="appid" jdbcType="VARCHAR" />
        <result column="mch_id" property="mchId" jdbcType="VARCHAR" />
        <result column="sub_mch_id" property="subMchId" jdbcType="VARCHAR" />
        <result column="stl_flg" property="stlFlg" jdbcType="VARCHAR" />
        <result column="rut_corg" property="rutCorg" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        jrn_no, update_date, update_time, pay_batch_no, settle_date, settle_datetime, start_date,
        end_date, settle_fee, unsettle_fee, settle_fee_type, pay_fee, refund_fee, pay_net_fee,
        poundage_fee, tot_settle_fee, tot_unsettle_fee, CREATE_TIME, MODIFY_TIME, TM_SMP,
        appid, mch_id, sub_mch_id, stl_flg, rut_corg
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpi_settle_detail
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_settle_detail
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.SettleDetailDO" >
        insert into cpi_settle_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                jrn_no,
            </if>
            <if test="updateDate != null" >
                update_date,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="payBatchNo != null" >
                pay_batch_no,
            </if>
            <if test="settleDate != null" >
                settle_date,
            </if>
            <if test="settleDatetime != null" >
                settle_datetime,
            </if>
            <if test="startDate != null" >
                start_date,
            </if>
            <if test="endDate != null" >
                end_date,
            </if>
            <if test="settleFee != null" >
                settle_fee,
            </if>
            <if test="unsettleFee != null" >
                unsettle_fee,
            </if>
            <if test="settleFeeType != null" >
                settle_fee_type,
            </if>
            <if test="payFee != null" >
                pay_fee,
            </if>
            <if test="refundFee != null" >
                refund_fee,
            </if>
            <if test="payNetFee != null" >
                pay_net_fee,
            </if>
            <if test="poundageFee != null" >
                poundage_fee,
            </if>
            <if test="totSettleFee != null" >
                tot_settle_fee,
            </if>
            <if test="totUnsettleFee != null" >
                tot_unsettle_fee,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="appid != null" >
                appid,
            </if>
            <if test="mchId != null" >
                mch_id,
            </if>
            <if test="subMchId != null" >
                sub_mch_id,
            </if>
            <if test="stlFlg != null" >
                stl_flg,
            </if>
            <if test="rutCorg != null" >
                rut_corg,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jrnNo != null" >
                #{jrnNo,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null" >
                #{updateDate,jdbcType=DATE},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIME},
            </if>
            <if test="payBatchNo != null" >
                #{payBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="settleDate != null" >
                #{settleDate,jdbcType=DATE},
            </if>
            <if test="settleDatetime != null" >
                #{settleDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="startDate != null" >
                #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null" >
                #{endDate,jdbcType=DATE},
            </if>
            <if test="settleFee != null" >
                #{settleFee,jdbcType=DECIMAL},
            </if>
            <if test="unsettleFee != null" >
                #{unsettleFee,jdbcType=DECIMAL},
            </if>
            <if test="settleFeeType != null" >
                #{settleFeeType,jdbcType=VARCHAR},
            </if>
            <if test="payFee != null" >
                #{payFee,jdbcType=DECIMAL},
            </if>
            <if test="refundFee != null" >
                #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="payNetFee != null" >
                #{payNetFee,jdbcType=DECIMAL},
            </if>
            <if test="poundageFee != null" >
                #{poundageFee,jdbcType=DECIMAL},
            </if>
            <if test="totSettleFee != null" >
                #{totSettleFee,jdbcType=DECIMAL},
            </if>
            <if test="totUnsettleFee != null" >
                #{totUnsettleFee,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="appid != null" >
                #{appid,jdbcType=VARCHAR},
            </if>
            <if test="mchId != null" >
                #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="subMchId != null" >
                #{subMchId,jdbcType=VARCHAR},
            </if>
            <if test="stlFlg != null" >
                #{stlFlg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                #{rutCorg,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.SettleDetailDO" >
        update cpi_settle_detail
        <set >
            <if test="updateDate != null" >
                update_date = #{updateDate,jdbcType=DATE},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIME},
            </if>
            <if test="payBatchNo != null" >
                pay_batch_no = #{payBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="settleDate != null" >
                settle_date = #{settleDate,jdbcType=DATE},
            </if>
            <if test="settleDatetime != null" >
                settle_datetime = #{settleDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="startDate != null" >
                start_date = #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null" >
                end_date = #{endDate,jdbcType=DATE},
            </if>
            <if test="settleFee != null" >
                settle_fee = #{settleFee,jdbcType=DECIMAL},
            </if>
            <if test="unsettleFee != null" >
                unsettle_fee = #{unsettleFee,jdbcType=DECIMAL},
            </if>
            <if test="settleFeeType != null" >
                settle_fee_type = #{settleFeeType,jdbcType=VARCHAR},
            </if>
            <if test="payFee != null" >
                pay_fee = #{payFee,jdbcType=DECIMAL},
            </if>
            <if test="refundFee != null" >
                refund_fee = #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="payNetFee != null" >
                pay_net_fee = #{payNetFee,jdbcType=DECIMAL},
            </if>
            <if test="poundageFee != null" >
                poundage_fee = #{poundageFee,jdbcType=DECIMAL},
            </if>
            <if test="totSettleFee != null" >
                tot_settle_fee = #{totSettleFee,jdbcType=DECIMAL},
            </if>
            <if test="totUnsettleFee != null" >
                tot_unsettle_fee = #{totUnsettleFee,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="appid != null" >
                appid = #{appid,jdbcType=VARCHAR},
            </if>
            <if test="mchId != null" >
                mch_id = #{mchId,jdbcType=VARCHAR},
            </if>
            <if test="subMchId != null" >
                sub_mch_id = #{subMchId,jdbcType=VARCHAR},
            </if>
            <if test="stlFlg != null" >
                stl_flg = #{stlFlg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorg != null" >
                rut_corg = #{rutCorg,jdbcType=VARCHAR},
            </if>
        </set>
        where jrn_no = #{jrnNo,jdbcType=VARCHAR}
    </update>

    <!--查询截止指定结算日期未结算或已结算总金额-->
    <select id="getUnSettleFee" resultType="java.math.BigDecimal">
        select sum(unsettle_fee) tot_unsettle_fee
        from cpi_settle_detail
        where settle_date > #{settleDate,jdbcType=DATE}
        and settle_date &lt; #{updateDate,jdbcType=DATE}
        and stl_flg = #{stlFlg,jdbcType=VARCHAR}
    </select>

    <!--查询截止指定结算日期未结算或已结算总金额-->
    <select id="getSettleFee" resultType="java.math.BigDecimal">
        select sum(settle_fee) tot_unsettle_fee
        from cpi_settle_detail
        where settle_date &lt; #{settleDate,jdbcType=DATE}
        and stl_flg = #{stlFlg,jdbcType=VARCHAR}
    </select>

    <!--根据结算机构、结算日期、结算标志，查询表中是否已存有数据-->
    <select id="getSettleDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cpi_settle_detail
        where rut_corg = #{rutCorg,jdbcType=VARCHAR}
        and settle_date = #{settleDate,jdbcType=DATE}
        and stl_flg = #{stlFlg,jdbcType=VARCHAR}
    </select>


    <!--根据结算日期结算类型查询最大日期数据-->
    <select id="getSettleByDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM cpi_settle_detail
        WHERE stl_flg = #{stlFlg,jdbcType=VARCHAR}
        AND settle_date &lt; #{settleDate,jdbcType=DATE}
        ORDER BY settle_date DESC LIMIT 1
    </select>

</mapper>