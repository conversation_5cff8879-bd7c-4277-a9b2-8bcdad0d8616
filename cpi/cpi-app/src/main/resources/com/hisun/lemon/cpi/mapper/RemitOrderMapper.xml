<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.IRemitOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.RemitOrderDO" >
        <id column="SUB_ORD_NO" property="subOrdNo" jdbcType="VARCHAR" />
        <result column="FND_ORD_NO" property="fndOrdNo" jdbcType="VARCHAR" />
        <result column="ORD_DT" property="ordDt" jdbcType="DATE" />
        <result column="ORD_TM" property="ordTm" jdbcType="TIME" />
        <result column="AC_DT" property="acDt" jdbcType="DATE" />
        <result column="CRD_CORP_ORG" property="crdCorpOrg" jdbcType="VARCHAR" />
        <result column="RUT_CORP_ORG" property="rutCorpOrg" jdbcType="VARCHAR" />
        <result column="CORP_BUS_TYP" property="corpBusTyp" jdbcType="CHAR" />
        <result column="CORP_BUS_SUB_TYP" property="corpBusSubTyp" jdbcType="CHAR" />
        <result column="ORD_AMT" property="ordAmt" jdbcType="DECIMAL" />
        <result column="ORD_STS" property="ordSts" jdbcType="CHAR" />
        <result column="CHK_KEY" property="chkKey" jdbcType="VARCHAR" />
        <result column="CHK_FLG" property="chkFlg" jdbcType="CHAR" />
        <result column="CHK_STS" property="chkSts" jdbcType="CHAR" />
        <result column="CHK_DT" property="chkDt" jdbcType="DATE" />
        <result column="CHK_TM" property="chkTm" jdbcType="TIME" />
        <result column="PIC_URL" property="picUrl" jdbcType="VARCHAR" />
        <result column="RMK" property="rmk" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="TM_SMP" property="tmSmp" jdbcType="TIMESTAMP" />
        <result column="REASON" property="reason" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        SUB_ORD_NO, FND_ORD_NO, ORD_DT, ORD_TM, AC_DT, CRD_CORP_ORG, RUT_CORP_ORG, CORP_BUS_TYP, 
        CORP_BUS_SUB_TYP, ORD_AMT, ORD_STS, CHK_KEY, CHK_FLG, CHK_STS, CHK_DT, CHK_TM, PIC_URL, RMK,
        CREATE_TIME, MODIFY_TIME, TM_SMP, REASON
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_remit_order
        where SUB_ORD_NO = #{subOrdNo,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_remit_order
        where SUB_ORD_NO = #{subOrdNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.RemitOrderDO" >
        insert into cpi_remit_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="subOrdNo != null" >
                SUB_ORD_NO,
            </if>
            <if test="fndOrdNo != null" >
                FND_ORD_NO,
            </if>
            <if test="ordDt != null" >
                ORD_DT,
            </if>
            <if test="ordTm != null" >
                ORD_TM,
            </if>
            <if test="acDt != null" >
                AC_DT,
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG,
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG,
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP,
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP,
            </if>
            <if test="ordAmt != null" >
                ORD_AMT,
            </if>
            <if test="ordSts != null" >
                ORD_STS,
            </if>
            <if test="chkKey != null" >
                CHK_KEY,
            </if>
            <if test="chkFlg != null" >
                CHK_FLG,
            </if>
            <if test="chkSts != null" >
                CHK_STS,
            </if>
            <if test="chkDt != null" >
                CHK_DT,
            </if>
            <if test="chkTm != null" >
                CHK_TM,
            </if>
            <if test="picUrl != null" >
                PIC_URL,
            </if>
            <if test="rmk != null" >
                RMK,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="tmSmp != null" >
                TM_SMP,
            </if>
            <if test="reason != null" >
                REASON
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="subOrdNo != null" >
                #{subOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="fndOrdNo != null" >
                #{fndOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                #{ordTm,jdbcType=TIME},
            </if>
            <if test="acDt != null" >
                #{acDt,jdbcType=DATE},
            </if>
            <if test="crdCorpOrg != null" >
                #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                #{ordSts,jdbcType=CHAR},
            </if>
            <if test="chkKey != null" >
                #{chkKey,jdbcType=VARCHAR},
            </if>
            <if test="chkFlg != null" >
                #{chkFlg,jdbcType=CHAR},
            </if>
            <if test="chkSts != null" >
                #{chkSts,jdbcType=CHAR},
            </if>
            <if test="chkDt != null" >
                #{chkDt,jdbcType=DATE},
            </if>
            <if test="chkTm != null" >
                #{chkTm,jdbcType=TIME},
            </if>
            <if test="picUrl != null" >
                #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="reason != null" >
                #{reason,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.RemitOrderDO" >
        update cpi_remit_order
        <set >
            <if test="fndOrdNo != null" >
                FND_ORD_NO = #{fndOrdNo,jdbcType=VARCHAR},
            </if>
            <if test="ordDt != null" >
                ORD_DT = #{ordDt,jdbcType=DATE},
            </if>
            <if test="ordTm != null" >
                ORD_TM = #{ordTm,jdbcType=TIME},
            </if>
            <if test="acDt != null" >
                AC_DT = #{acDt,jdbcType=DATE},
            </if>
            <if test="crdCorpOrg != null" >
                CRD_CORP_ORG = #{crdCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="rutCorpOrg != null" >
                RUT_CORP_ORG = #{rutCorpOrg,jdbcType=VARCHAR},
            </if>
            <if test="corpBusTyp != null" >
                CORP_BUS_TYP = #{corpBusTyp,jdbcType=CHAR},
            </if>
            <if test="corpBusSubTyp != null" >
                CORP_BUS_SUB_TYP = #{corpBusSubTyp,jdbcType=CHAR},
            </if>
            <if test="ordAmt != null" >
                ORD_AMT = #{ordAmt,jdbcType=DECIMAL},
            </if>
            <if test="ordSts != null" >
                ORD_STS = #{ordSts,jdbcType=CHAR},
            </if>
            <if test="chkKey != null" >
                CHK_KEY = #{chkKey,jdbcType=VARCHAR},
            </if>
            <if test="chkFlg != null" >
                CHK_FLG = #{chkFlg,jdbcType=CHAR},
            </if>
            <if test="chkSts != null" >
                CHK_STS = #{chkSts,jdbcType=CHAR},
            </if>
            <if test="chkDt != null" >
                CHK_DT = #{chkDt,jdbcType=DATE},
            </if>
            <if test="chkTm != null" >
                CHK_TM = #{chkTm,jdbcType=TIME},
            </if>
            <if test="picUrl != null" >
                PIC_URL = #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="rmk != null" >
                RMK = #{rmk,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                TM_SMP = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
            <if test="reason != null" >
                REASON = #{reason,jdbcType=VARCHAR}
            </if>
        </set>
        where SUB_ORD_NO = #{subOrdNo,jdbcType=VARCHAR}
    </update>

    <select id="selectByFndOrdNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from cpi_remit_order
        where FND_ORD_NO = #{fndOrdNo,jdbcType=VARCHAR}
    </select>
</mapper>