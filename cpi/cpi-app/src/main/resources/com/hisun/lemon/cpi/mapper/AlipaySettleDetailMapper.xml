<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.cpi.dao.alipay.IAlipaySettleDetailDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.cpi.entity.alipay.AlipaySettleDetailDO" >
        <id column="partner_transaction_id" property="partnerTransactionId" jdbcType="VARCHAR" />
        <result column="transaction_id" property="transactionId" jdbcType="VARCHAR" />
        <result column="amount" property="amount" jdbcType="DECIMAL" />
        <result column="rmb_amount" property="rmbAmount" jdbcType="DECIMAL" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="settlement" property="settlement" jdbcType="DECIMAL" />
        <result column="rmb_settlement" property="rmbSettlement" jdbcType="DECIMAL" />
        <result column="payment_time" property="paymentTime" jdbcType="VARCHAR" />
        <result column="settle_time" property="settleTime" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="currency" property="currency" jdbcType="VARCHAR" />
        <result column="rate" property="rate" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="secondary_merchant_industry" property="secondaryMerchantIndustry" jdbcType="VARCHAR" />
        <result column="secondary_merchant_name" property="secondaryMerchantName" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="order_scene" property="orderScene" jdbcType="VARCHAR" />
        <result column="trans_currency" property="transCurrency" jdbcType="VARCHAR" />
        <result column="trans_forex_rate" property="transForexRate" jdbcType="VARCHAR" />
        <result column="trans_amount" property="transAmount" jdbcType="DECIMAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        partner_transaction_id, transaction_id, amount, rmb_amount, fee, settlement, rmb_settlement, 
        payment_time, settle_time, type, status, currency, rate, remark, secondary_merchant_industry, 
        secondary_merchant_name, operator_name, order_scene, trans_currency, trans_forex_rate, 
        trans_amount
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from cpi_alipay_settle_detail
        where partner_transaction_id = #{partnerTransactionId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from cpi_alipay_settle_detail
        where partner_transaction_id = #{partnerTransactionId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.cpi.entity.alipay.AlipaySettleDetailDO" >
        insert into cpi_alipay_settle_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="partnerTransactionId != null" >
                partner_transaction_id,
            </if>
            <if test="transactionId != null" >
                transaction_id,
            </if>
            <if test="amount != null" >
                amount,
            </if>
            <if test="rmbAmount != null" >
                rmb_amount,
            </if>
            <if test="fee != null" >
                fee,
            </if>
            <if test="settlement != null" >
                settlement,
            </if>
            <if test="rmbSettlement != null" >
                rmb_settlement,
            </if>
            <if test="paymentTime != null" >
                payment_time,
            </if>
            <if test="settleTime != null" >
                settle_time,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="currency != null" >
                currency,
            </if>
            <if test="rate != null" >
                rate,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="secondaryMerchantIndustry != null" >
                secondary_merchant_industry,
            </if>
            <if test="secondaryMerchantName != null" >
                secondary_merchant_name,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="orderScene != null" >
                order_scene,
            </if>
            <if test="transCurrency != null" >
                trans_currency,
            </if>
            <if test="transForexRate != null" >
                trans_forex_rate,
            </if>
            <if test="transAmount != null" >
                trans_amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="partnerTransactionId != null" >
                #{partnerTransactionId,jdbcType=VARCHAR},
            </if>
            <if test="transactionId != null" >
                #{transactionId,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="rmbAmount != null" >
                #{rmbAmount,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="settlement != null" >
                #{settlement,jdbcType=DECIMAL},
            </if>
            <if test="rmbSettlement != null" >
                #{rmbSettlement,jdbcType=DECIMAL},
            </if>
            <if test="paymentTime != null" >
                #{paymentTime,jdbcType=VARCHAR},
            </if>
            <if test="settleTime != null" >
                #{settleTime,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="currency != null" >
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="rate != null" >
                #{rate,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantIndustry != null" >
                #{secondaryMerchantIndustry,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantName != null" >
                #{secondaryMerchantName,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="orderScene != null" >
                #{orderScene,jdbcType=VARCHAR},
            </if>
            <if test="transCurrency != null" >
                #{transCurrency,jdbcType=VARCHAR},
            </if>
            <if test="transForexRate != null" >
                #{transForexRate,jdbcType=VARCHAR},
            </if>
            <if test="transAmount != null" >
                #{transAmount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.cpi.entity.alipay.AlipaySettleDetailDO" >
        update cpi_alipay_settle_detail
        <set >
            <if test="transactionId != null" >
                transaction_id = #{transactionId,jdbcType=VARCHAR},
            </if>
            <if test="amount != null" >
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="rmbAmount != null" >
                rmb_amount = #{rmbAmount,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="settlement != null" >
                settlement = #{settlement,jdbcType=DECIMAL},
            </if>
            <if test="rmbSettlement != null" >
                rmb_settlement = #{rmbSettlement,jdbcType=DECIMAL},
            </if>
            <if test="paymentTime != null" >
                payment_time = #{paymentTime,jdbcType=VARCHAR},
            </if>
            <if test="settleTime != null" >
                settle_time = #{settleTime,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="currency != null" >
                currency = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="rate != null" >
                rate = #{rate,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantIndustry != null" >
                secondary_merchant_industry = #{secondaryMerchantIndustry,jdbcType=VARCHAR},
            </if>
            <if test="secondaryMerchantName != null" >
                secondary_merchant_name = #{secondaryMerchantName,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="orderScene != null" >
                order_scene = #{orderScene,jdbcType=VARCHAR},
            </if>
            <if test="transCurrency != null" >
                trans_currency = #{transCurrency,jdbcType=VARCHAR},
            </if>
            <if test="transForexRate != null" >
                trans_forex_rate = #{transForexRate,jdbcType=VARCHAR},
            </if>
            <if test="transAmount != null" >
                trans_amount = #{transAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where partner_transaction_id = #{partnerTransactionId,jdbcType=VARCHAR}
    </update>

    <select id="findAllBySettleDate" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List"/>
      from cpi_alipay_settle_detail
      WHERE  STR_TO_DATE(settle_time,"%Y-%m-%d") = STR_TO_DATE(#{settleDateStr,jdbcType=VARCHAR},"%Y%m%d")

    </select>
</mapper>