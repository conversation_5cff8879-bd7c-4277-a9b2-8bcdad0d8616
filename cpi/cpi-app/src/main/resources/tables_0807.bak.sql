/*
MySQL Backup
Source Server Version: 5.7.18
Source Database: seatelpay_cpi
Date: 2017/8/7 16:54:47
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
--  Table structure for `cpi_acc_bestpay_ebankpay`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_acc_bestpay_ebankpay`;
CREATE TABLE `cpi_acc_bestpay_ebankpay` (
  `merc_id` varchar(32) DEFAULT NULL COMMENT '商户号',
  `bnk_chk_key` varchar(32) DEFAULT NULL COMMENT '银行对账外键',
  `plat_jrn_no` varchar(32) DEFAULT NULL COMMENT '平台流水号',
  `plat_ord_no` varchar(32) DEFAULT NULL COMMENT '平台订单号',
  `ord_cyy` varchar(8) DEFAULT NULL COMMENT '币种',
  `ord_foreign_amt` decimal(13,2) DEFAULT NULL COMMENT '外币',
  `ord_amt` decimal(13,2) DEFAULT NULL COMMENT '人民币',
  `exchange_rate` decimal(13,2) DEFAULT NULL COMMENT '汇率',
  `CHK_BAT_NO` varchar(32) DEFAULT ' ' COMMENT '对账批次号',
  `RUT_CORG` varchar(16) DEFAULT ' ' COMMENT '路径合作机构',
  `CORP_BUS_TYP` varchar(2) DEFAULT ' ' COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) DEFAULT ' ' COMMENT '合作业务子类型',
  `CHK_FIL_DT` date DEFAULT NULL COMMENT '对账文件日期',
  `CHK_FIL_NM` varchar(64) DEFAULT ' ' COMMENT '对账文件名称',
  `CHK_ID` varchar(32) NOT NULL COMMENT '对账明细序号',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  `CHK_STS` varchar(1) DEFAULT '0' COMMENT '对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑',
  KEY `AK_Key_1` (`CHK_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='翼支付网银（扫码付）对账明细';

-- ----------------------------
--  Table structure for `cpi_acc_bestpay_ebankpay_refund`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_acc_bestpay_ebankpay_refund`;
CREATE TABLE `cpi_acc_bestpay_ebankpay_refund` (
  `merc_id` varchar(32) DEFAULT NULL COMMENT '商户号',
  `bnk_chk_key` varchar(32) DEFAULT NULL COMMENT '银行对账外键',
  `plat_jrn_no` varchar(32) DEFAULT NULL COMMENT '平台流水号',
  `plat_ord_no` varchar(32) DEFAULT NULL COMMENT '平台订单号',
  `ord_cyy` varchar(8) DEFAULT NULL COMMENT '币种',
  `ord_foreign_amt` decimal(13,2) DEFAULT NULL COMMENT '外币',
  `ord_amt` decimal(13,2) DEFAULT NULL COMMENT '人民币',
  `exchange_rate` decimal(13,2) DEFAULT NULL COMMENT '汇率',
  `CHK_BAT_NO` varchar(32) DEFAULT ' ' COMMENT '对账批次号',
  `RUT_CORG` varchar(16) DEFAULT ' ' COMMENT '路径合作机构',
  `CORP_BUS_TYP` varchar(2) DEFAULT ' ' COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) DEFAULT ' ' COMMENT '合作业务子类型',
  `CHK_FIL_DT` date DEFAULT NULL COMMENT '对账文件日期',
  `CHK_FIL_NM` varchar(64) DEFAULT ' ' COMMENT '对账文件名称',
  `CHK_ID` varchar(32) NOT NULL COMMENT '对账明细序号',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  `CHK_STS` varchar(1) DEFAULT '0' COMMENT '对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑',
  KEY `AK_Key_1` (`CHK_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='翼支付网银（扫码付）退款对账明细';

-- ----------------------------
--  Table structure for `cpi_acc_cfg`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_acc_cfg`;
CREATE TABLE `cpi_acc_cfg` (
  `ACC_CFG_ID` varchar(32) NOT NULL COMMENT 'id',
  `RUT_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径机构',
  `CORP_BUS_TYP` varchar(2) NOT NULL DEFAULT ' ' COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) NOT NULL DEFAULT ' ' COMMENT '业务子类型',
  `CHK_CLAZZ` varchar(128) NOT NULL DEFAULT ' ' COMMENT '银行API类',
  `GET_FILE_METHOD` varchar(128) NOT NULL DEFAULT ' ' COMMENT '获取对账文件的方法',
  `IMPORT_FIELDS` varchar(256) NOT NULL DEFAULT ' ' COMMENT '银行对账文件每行记录对应的字段',
  `IMPORT_CLASS` varchar(128) NOT NULL DEFAULT ' ' COMMENT '对账文件入库对应的DO类',
  `IMPORT_METHOD` varchar(64) NOT NULL DEFAULT ' ' COMMENT '解析对账文件并入库的方法',
  `SPLIT_SIGN` varchar(8) NOT NULL DEFAULT ' ' COMMENT '对账文件内容分隔符',
  `CHK_FILE_PATH` varchar(64) NOT NULL DEFAULT ' ' COMMENT '对账文件本地路径',
  `CONTINUE_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '解析对账文件跳过多少行',
  `SELECT_DETAIL_METHOD` varchar(64) NOT NULL DEFAULT ' ' COMMENT '银行API查询对账明细的方法',
  `SUCCESS_FLAG` varchar(8) NOT NULL DEFAULT ' ' COMMENT '银行明细状态成功对应的值',
  `CHECK_KEY_FILED` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账键值对应的域',
  `CHECK_KEY_BAK_FILED` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账键值备份对应的域(银行摘要、附言)',
  `CHECK_AMT_FILED` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账金额对应的域',
  `TX_STS_FILED` varchar(32) NOT NULL DEFAULT ' ' COMMENT '银行明细状态对应的域',
  `QUERY_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '对账时每次查询的最大笔数',
  `UPDATE_METHOD` varchar(32) NOT NULL DEFAULT ' ' COMMENT '更新银行明细的对账状态方法',
  `CREATE_TIME` date DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` time DEFAULT NULL COMMENT '更新时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`ACC_CFG_ID`),
  UNIQUE KEY `cpi_acc_cfg_ui1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对账配置表';

-- ----------------------------
--  Table structure for `cpi_acc_control`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_acc_control`;
CREATE TABLE `cpi_acc_control` (
  `CHK_BAT_NO` varchar(32) NOT NULL COMMENT '批次号',
  `RUT_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构',
  `CORP_BUS_TYP` char(2) NOT NULL DEFAULT '' COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL DEFAULT '' COMMENT '业务子类型',
  `CHK_FIL_DT` date DEFAULT NULL COMMENT '对账文件日期',
  `CHK_FIL_NM` varchar(64) NOT NULL DEFAULT ' ' COMMENT '对账文件名',
  `CHK_FIL_STS` char(1) NOT NULL DEFAULT '0' COMMENT '对账状态,0未对账，1文件已取，2文件入库，3已对账，4对账完成',
  `FILE_RCV_DT` date DEFAULT NULL COMMENT '获取对账文件日期',
  `CHK_BEG_TM` time DEFAULT NULL COMMENT '对账开始时间',
  `CHK_END_TM` time DEFAULT NULL COMMENT '对账结束时间',
  `CHK_DT` date DEFAULT NULL COMMENT '对账日期',
  `FIL_TOT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '对账总金额',
  `FIL_TOT_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '对账总笔数',
  `TOT_MCH_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '对平总金额',
  `TOT_MCH_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '对平总笔数',
  `ERR_TOT_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '差错笔数',
  `ERR_TOT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '差错金额',
  `LONG_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '长款金额',
  `LONG_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '长款笔数',
  `SHORT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '短款金额',
  `SHORT_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '短款笔数',
  `DOUBT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '存疑金额',
  `DOUBT_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '存疑笔数',
  `DBT_ERR_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '存疑转差错金额',
  `DBT_ERR_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '存疑转差错笔数',
  `TOT_DR_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '借方总金额',
  `TOT_DR_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '借方总笔数',
  `TOT_CR_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '贷方总金额',
  `TOT_CR_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '贷方总笔数',
  `PAY_TOT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '应付总金额',
  `RCV_TOT_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '应收总金额',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime DEFAULT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`CHK_BAT_NO`),
  UNIQUE KEY `Index_1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`,`CHK_FIL_DT`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金模块对账总控表';

-- ----------------------------
--  Table structure for `cpi_acc_error`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_acc_error`;
CREATE TABLE `cpi_acc_error` (
  `CHK_ER_ID` varchar(20) NOT NULL COMMENT '主键',
  `RUT_CORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构',
  `CORP_BUS_TYP` varchar(2) NOT NULL DEFAULT ' ' COMMENT '业务合作类型',
  `CORP_BUS_SUB_TYP` varchar(4) NOT NULL DEFAULT ' ' COMMENT '业务合作子类型',
  `ERR_KEY_ID` varchar(64) NOT NULL DEFAULT ' ' COMMENT '差错键值',
  `CHK_ERR_DT` date DEFAULT NULL COMMENT '差错创建日期',
  `CHK_ERR_TM` time DEFAULT NULL COMMENT '差错创建时间',
  `CHK_BAT_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '对账批次',
  `SPL_ABLE_FLG` char(1) NOT NULL DEFAULT 'Y' COMMENT '补单允许标识，Y允许，N不允许',
  `CAN_ABLE_FLG` char(1) NOT NULL DEFAULT 'Y' COMMENT '撤单允许标识，Y允许，N不允许',
  `CHK_ERR_TYP` char(1) NOT NULL DEFAULT '' COMMENT '差错类型，2：短款差错，3：长款差错，4：金额不符',
  `ERR_STS` char(1) NOT NULL DEFAULT '0' COMMENT '差错状态，0：待处理，1：已补单，2：已撤单，3：人工取消',
  `OTH_TX_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '对方交易金额',
  `MY_TX_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '我方交易金额',
  `PSN_CRP_FLG` char(1) NOT NULL DEFAULT 'C' COMMENT '个人/商户标识，B商户，C个人',
  `OLD_TX_DT` char(8) DEFAULT NULL COMMENT '原交易日期',
  `OLD_JRN_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '原交易流水号',
  `OLD_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '原交易订单号',
  `OLD_CORG_KEY` varchar(64) NOT NULL DEFAULT ' ' COMMENT '原路径合作机构对账主键',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime DEFAULT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`CHK_ER_ID`),
  UNIQUE KEY `Index_1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`,`ERR_KEY_ID`),
  KEY `Index_2` (`CHK_BAT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对账差错表';

-- ----------------------------
--  Table structure for `cpi_acc_icbc_refund`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_acc_icbc_refund`;
CREATE TABLE `cpi_acc_icbc_refund` (
  `company_date` varchar(8) DEFAULT ' ' COMMENT '公司方日期',
  `company_serino` varchar(40) DEFAULT ' ' COMMENT '公司方交易流水号(同机构交易流水号)',
  `trx_type` varchar(2) DEFAULT ' ' COMMENT '交易类型 1-消费; 3-退货',
  `bank_serno` varchar(32) DEFAULT ' ' COMMENT '银行流水号',
  `settle_date` varchar(8) DEFAULT ' ' COMMENT '清算日期，格式：YYYYMMDD',
  `card_no` varchar(40) DEFAULT ' ' COMMENT '银行卡号',
  `pay_amt` decimal(13,2) DEFAULT '0.00' COMMENT '银行订单金额(单位:元)',
  `curr_type` varchar(3) DEFAULT ' ' COMMENT '币种',
  `status` varchar(1) DEFAULT ' ' COMMENT '明细状态 2-成功; 3-失败',
  `CHK_BAT_NO` varchar(32) DEFAULT ' ' COMMENT '对账批次号',
  `RUT_CORG` varchar(16) DEFAULT ' ' COMMENT '路径合作机构',
  `CORP_BUS_TYP` varchar(2) DEFAULT ' ' COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) DEFAULT ' ' COMMENT '合作业务子类型',
  `CHK_FIL_DT` date DEFAULT NULL COMMENT '对账文件日期',
  `CHK_FIL_NM` varchar(64) DEFAULT ' ' COMMENT '对账文件名称',
  `CHK_ID` varchar(32) NOT NULL COMMENT '对账明细序号',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  `CHK_STS` varchar(1) DEFAULT '0' COMMENT '对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑',
  PRIMARY KEY (`CHK_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工行对账退款明细表(包括快捷退款、网银退款)';

-- ----------------------------
--  Table structure for `cpi_acc_icbc_shortcut`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_acc_icbc_shortcut`;
CREATE TABLE `cpi_acc_icbc_shortcut` (
  `company_date` varchar(8) DEFAULT ' ' COMMENT '公司方日期',
  `company_serino` varchar(40) DEFAULT ' ' COMMENT '公司方交易流水号(同机构交易流水号)',
  `trx_type` varchar(2) DEFAULT ' ' COMMENT '交易类型 1-消费; 3-退货',
  `bank_serno` varchar(32) DEFAULT ' ' COMMENT '银行流水号',
  `settle_date` varchar(8) DEFAULT ' ' COMMENT '清算日期，格式：YYYYMMDD',
  `card_no` varchar(40) DEFAULT ' ' COMMENT '银行卡号',
  `pay_amt` decimal(13,2) DEFAULT '0.00' COMMENT '银行订单金额(单位:元)',
  `curr_type` varchar(3) DEFAULT ' ' COMMENT '币种',
  `status` varchar(1) DEFAULT ' ' COMMENT '明细状态 2-成功; 3-失败',
  `CHK_BAT_NO` varchar(32) DEFAULT ' ' COMMENT '对账批次号',
  `RUT_CORG` varchar(16) DEFAULT ' ' COMMENT '路径合作机构',
  `CORP_BUS_TYP` varchar(2) DEFAULT ' ' COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) DEFAULT ' ' COMMENT '合作业务子类型',
  `CHK_FIL_DT` date DEFAULT NULL COMMENT '对账文件日期',
  `CHK_FIL_NM` varchar(64) DEFAULT ' ' COMMENT '对账文件名称',
  `CHK_ID` varchar(32) NOT NULL COMMENT '对账明细序号',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  `CHK_STS` varchar(1) DEFAULT '0' COMMENT '对账状态，0：未对账，1：对账成功，2：我方有银行无，3：银行有我方无，4：金额错误，5：存疑',
  PRIMARY KEY (`CHK_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工行对账快捷明细表(消费)';

-- ----------------------------
--  Table structure for `cpi_acc_param`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_acc_param`;
CREATE TABLE `cpi_acc_param` (
  `CHK_PM_ID` varchar(24) NOT NULL COMMENT 'id',
  `RUT_CORG` varchar(16) NOT NULL COMMENT '路径机构',
  `CORP_BUS_TYP` varchar(16) NOT NULL COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` varchar(16) NOT NULL COMMENT '业务子类型',
  `EFF_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '生效标识，Y生效，N不生效',
  `CHK_DO_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '是否对账标识，Y对账，N不对账',
  `BAT_CHK_FLG` char(1) NOT NULL DEFAULT 'B' COMMENT '批量对账标识，B批量,O:实时对账',
  `CHK_BEG_TM` time DEFAULT NULL COMMENT '对账开始时间',
  `CHK_END_TM` time DEFAULT NULL COMMENT '对账结束时间',
  `SPL_ABLE_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '补单标识，Y支持，N不支持',
  `CAN_ABLE_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '撤单标识，Y支持，N不支持',
  `AU_SK_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '自动跳账标识，Y支持，N不支持',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime DEFAULT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`CHK_PM_ID`),
  UNIQUE KEY `Index_1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金模块对账参数表';

-- ----------------------------
--  Table structure for `cpi_card_bin`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_card_bin`;
CREATE TABLE `cpi_card_bin` (
  `BIN_ID` varchar(20) NOT NULL COMMENT '主键',
  `CRD_BIN` varchar(12) NOT NULL COMMENT '卡BIN',
  `CAP_CORG` varchar(16) NOT NULL COMMENT '资金机构',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT 'D' COMMENT '卡类型，D借记卡，C贷记卡',
  `CRD_LTH` int(11) NOT NULL DEFAULT '0' COMMENT '卡长度',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`BIN_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金模块BIN表';

-- ----------------------------
--  Table structure for `cpi_card_prot`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_card_prot`;
CREATE TABLE `cpi_card_prot` (
  `AGR_NO` varchar(24) NOT NULL COMMENT '内部协议号',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '合作业务子类型',
  `AGR_EFF_FLG` char(1) NOT NULL DEFAULT 'W' COMMENT '生效标志，W：初登记，Y：生效，N：失效',
  `SIGN_DT` date DEFAULT NULL COMMENT '签约日期',
  `SIGN_TM` time DEFAULT NULL COMMENT '签约时间',
  `UNSIGN_DT` date DEFAULT NULL COMMENT '解约日期',
  `UNSIGN_TM` time DEFAULT NULL COMMENT '解约时间',
  `USER_ID` varchar(32) NOT NULL COMMENT '内部用户号',
  `MBL_NO` varchar(16) NOT NULL DEFAULT ' ' COMMENT '手机号',
  `BNK_PSN_FLG` char(1) NOT NULL DEFAULT 'C' COMMENT '对公对私标志，B：对公，C：对私',
  `CRD_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '合作机构号',
  `RUT_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径机构号',
  `SIGN_AGRNO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '机构协议号',
  `AGR_DIRECT` char(1) NOT NULL DEFAULT '0' COMMENT '协议方向，0：双向协议1：我方单侧协议',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT 'D' COMMENT '卡类型，D：借记卡，C：贷记卡',
  `CRD_NO_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT '加密银行卡号',
  `CRD_NO_LAST` varchar(4) NOT NULL DEFAULT ' ' COMMENT '银行卡后四位',
  `CRD_USR_NM` varchar(64) NOT NULL DEFAULT ' ' COMMENT '账户名',
  `ID_TYP` char(2) NOT NULL DEFAULT '' COMMENT '证件类型',
  `ID_NO_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT '加密证件号',
  `CRD_CVV2_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT 'CVV2',
  `CRD_EXP_DT_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT '有效期',
  `RMK` varchar(128) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`AGR_NO`),
  KEY `in1` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户银行卡主协议表';

-- ----------------------------
--  Table structure for `cpi_card_prot_jrn`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_card_prot_jrn`;
CREATE TABLE `cpi_card_prot_jrn` (
  `JRN_NO` varchar(32) NOT NULL COMMENT '流水号',
  `TX_DT` date NOT NULL COMMENT '签约日期',
  `TX_TM` time NOT NULL COMMENT '签约时间',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '合作业务子类型',
  `CRD_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '合作机构号',
  `USER_ID` varchar(32) NOT NULL DEFAULT ' ' COMMENT '内部用户号',
  `USER_NM` varchar(32) NOT NULL DEFAULT ' ' COMMENT '用户名',
  `ID_TYP` char(2) NOT NULL DEFAULT '' COMMENT '证件类型',
  `ID_NO_ENC` varchar(128) NOT NULL DEFAULT ' ' COMMENT '加密证件号',
  `CRD_NO_ENC` varchar(128) NOT NULL DEFAULT ' ' COMMENT '加密卡号',
  `RUT_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径机构号',
  `BND_FLG` char(1) NOT NULL DEFAULT '' COMMENT '绑定类型，P-预签约，S-绑定 C-解绑',
  `MBL_NO` varchar(16) NOT NULL DEFAULT ' ' COMMENT '手机号',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT 'D' COMMENT '卡类型，D：借记卡，C：贷记卡',
  `CRD_USR_NM` varchar(64) NOT NULL DEFAULT ' ' COMMENT '账户名',
  `CRD_CVV2_ENC` varchar(128) NOT NULL DEFAULT ' ' COMMENT '加密的cvv2',
  `CRD_EXP_DT_ENC` varchar(128) NOT NULL DEFAULT ' ' COMMENT '加密的有限期',
  `SIGN_AGRNO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '机构协议号',
  `ORG_JRN_NO` varchar(32) NOT NULL DEFAULT ' ' COMMENT '机构返回流水号',
  `ORG_RSP_CD` varchar(16) NOT NULL DEFAULT ' ' COMMENT '机构返回码',
  `ORG_RSP_MSG` varchar(128) NOT NULL DEFAULT ' ' COMMENT '机构返回信息',
  `RMK` varchar(128) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`JRN_NO`),
  KEY `in1` (`USER_ID`),
  KEY `in2` (`TX_DT`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户银行卡协议流水表';

-- ----------------------------
--  Table structure for `cpi_cop_agcy_biz`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_cop_agcy_biz`;
CREATE TABLE `cpi_cop_agcy_biz` (
  `ORG_BUS_ID` varchar(24) NOT NULL COMMENT '主键',
  `CORP_ORG_ID` varchar(8) NOT NULL COMMENT '合作机构编号',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '合作业务子类型',
  `BUS_EFF_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '业务生效标志，0失效，1生效',
  `CRE_OPR_ID` char(16) NOT NULL DEFAULT '' COMMENT '创建人ID',
  `UPD_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '修改人ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`ORG_BUS_ID`),
  UNIQUE KEY `in1` (`CORP_ORG_ID`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合作机构业务表';

-- ----------------------------
--  Table structure for `cpi_cop_agcy_info`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_cop_agcy_info`;
CREATE TABLE `cpi_cop_agcy_info` (
  `ORG_INF_ID` varchar(24) NOT NULL COMMENT '主键',
  `CORP_ORG_ID` varchar(16) NOT NULL COMMENT '合作机构编号',
  `CORP_ORG_NM` varchar(60) NOT NULL DEFAULT ' ' COMMENT '合作机构名称',
  `CORP_ORG_SNM` varchar(20) NOT NULL DEFAULT ' ' COMMENT '合作机构名简称',
  `CORP_ORG_TYP` char(1) NOT NULL DEFAULT '0' COMMENT '合作机构类型，0银行，1非银行',
  `CRE_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '创建柜员ID',
  `UPD_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '修改柜员ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`ORG_INF_ID`),
  UNIQUE KEY `in1` (`CORP_ORG_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合作机构基本信息表';

-- ----------------------------
--  Table structure for `cpi_cop_biz_rut`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_cop_biz_rut`;
CREATE TABLE `cpi_cop_biz_rut` (
  `RUT_INF_ID` varchar(24) NOT NULL COMMENT '主键',
  `CRD_CORP_ORG` varchar(16) NOT NULL COMMENT '资金合作机构编号',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '合作业务子类型',
  `RUT_CORP_ORG` varchar(16) NOT NULL COMMENT '路径合作机构号',
  `CRD_AC_TYP` char(1) NOT NULL COMMENT '银行卡类型，D借记卡，C贷记卡',
  `RUT_EFF_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '路由生效标志，0失效，1生效',
  `PRI_LVL` int(11) NOT NULL DEFAULT '99' COMMENT '0-99，数字越大级别越高',
  `LOW_AMT` decimal(13,2) NOT NULL COMMENT '最低金额',
  `HIGH_AMT` decimal(13,2) NOT NULL COMMENT '最高金额',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '创建柜员ID',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`RUT_INF_ID`),
  UNIQUE KEY `in1` (`CRD_CORP_ORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`,`RUT_CORP_ORG`,`CRD_AC_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='业务合作路由表';

-- ----------------------------
--  Table structure for `cpi_ebank_order`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_ebank_order`;
CREATE TABLE `cpi_ebank_order` (
  `SUB_ORD_NO` varchar(24) NOT NULL COMMENT '内部子订单号',
  `FND_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '内部订单号',
  `ORD_DT` date DEFAULT NULL COMMENT '订单日期',
  `ORD_TM` time DEFAULT NULL COMMENT '订单时间',
  `AC_DT` date DEFAULT NULL COMMENT '记账日期',
  `CRD_CORP_ORG` varchar(16) NOT NULL COMMENT '资金合作机构',
  `RUT_CORP_ORG` varchar(16) NOT NULL COMMENT '路径合作机构',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '业务子类型',
  `ORD_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `ORD_STS` char(2) NOT NULL DEFAULT '' COMMENT '订单状态，W1：等待充值，S1：交易成功，E1：交易逾期，F1：交易失败',
  `ORG_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '合作机构外部订单号（系统生成）',
  `ORG_JRN_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '合作机构返回的流水号',
  `ORG_ORD_DT` date DEFAULT NULL COMMENT '合作机构交易日期',
  `ORG_ORD_TM` time DEFAULT NULL COMMENT '合作机构交易时间',
  `SPL_ORD_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '0未轮询，1已轮询',
  `QRY_STS` char(1) NOT NULL DEFAULT 'W' COMMENT '结果查询状态，U：未明，S：成功，F：失败',
  `NTF_STS` char(1) NOT NULL DEFAULT '' COMMENT '合作机构通知状态，U：未明，S：成功，F：失败',
  `ORG_RSP_CD` varchar(8) NOT NULL DEFAULT ' ' COMMENT '合作机构返回码',
  `ORG_RSP_MSG` varchar(128) NOT NULL DEFAULT ' ' COMMENT '合作机构返回信息',
  `CHK_KEY` varchar(24) NOT NULL DEFAULT ' ' COMMENT '对账键值',
  `CHK_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '对账标志，0：需要对账，1：不需要对账',
  `CHK_STS` char(1) NOT NULL DEFAULT '0' COMMENT '对账状态，0：未对账，1：对账成功，2：我方有对方无，3：对方有我方无，4：金额错，5：存疑',
  `CHK_DT` date DEFAULT NULL COMMENT '对账日期',
  `CHK_TM` time DEFAULT NULL COMMENT '对账时间',
  `RMK` varchar(128) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`SUB_ORD_NO`),
  UNIQUE KEY `in1` (`FND_ORD_NO`),
  KEY `in2` (`CHK_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='网银订单表（支付宝、微信）';

-- ----------------------------
--  Table structure for `cpi_fund_order`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_fund_order`;
CREATE TABLE `cpi_fund_order` (
  `FUD_ORD_NO` varchar(24) NOT NULL COMMENT '内部订单号',
  `ORD_DT` date NOT NULL COMMENT '订单日期',
  `ORD_TM` time NOT NULL COMMENT '订单时间',
  `ORD_CCY` char(3) NOT NULL DEFAULT '' COMMENT '币种',
  `ORD_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `ORD_RFD_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '已退款金额',
  `ORD_STS` char(2) NOT NULL DEFAULT '' COMMENT '订单状态，W1：等待充值，S1：交易成功，E1：交易逾期，F1：交易失败',
  `ORD_SUCC_DT` date DEFAULT NULL COMMENT '订单成功日期',
  `ORD_SUCC_TM` time DEFAULT NULL COMMENT '订单成功时间',
  `USER_TYP` char(1) NOT NULL DEFAULT 'U' COMMENT '用户类型，U：用户，M：商户',
  `USER_ID` varchar(20) NOT NULL DEFAULT ' ' COMMENT '用户ID',
  `MBL_NO` varchar(16) NOT NULL DEFAULT ' ' COMMENT '手机号',
  `CRD_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '资金合作机构号',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT 'D' COMMENT '银行卡类型，C贷记卡，D借记卡',
  `CRD_NO_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT '加密银行卡号',
  `CRD_USR_NM` varchar(64) NOT NULL DEFAULT ' ' COMMENT '用户姓名',
  `CRD_NO_LAST` char(4) NOT NULL DEFAULT '' COMMENT '银行卡后4位',
  `ID_TYP` char(2) NOT NULL DEFAULT '' COMMENT '证件类型',
  `ID_NO_ENC` varchar(128) NOT NULL DEFAULT ' ' COMMENT '加密证件号码',
  `BNK_PSN_FLG` char(1) NOT NULL DEFAULT 'C' COMMENT '对公对私标志，B对公，C对私',
  `RUT_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构号',
  `CORP_BUS_TYP` varchar(2) NOT NULL DEFAULT ' ' COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) NOT NULL DEFAULT ' ' COMMENT '合作业务子类型',
  `REQ_ORD_NO` varchar(32) NOT NULL DEFAULT ' ' COMMENT '请求方订单号',
  `REQ_ORD_DT` date NOT NULL COMMENT '请求方订单日期',
  `REQ_ORD_TM` time NOT NULL COMMENT '请求方订单时间',
  `NTF_STS` char(1) NOT NULL COMMENT '通知状态，W：待通知，S：通知成功，F：通知失败（可重发），E：通知失败（不可重发）',
  `NTF_DT` date DEFAULT NULL COMMENT '通知日期',
  `NTF_TM` time DEFAULT NULL COMMENT '通知时间',
  `NTF_RSP_CD` varchar(8) DEFAULT NULL COMMENT '通知返回码',
  `NTF_RSP_MSG` varchar(256) DEFAULT NULL COMMENT '通知返回信息',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`FUD_ORD_NO`),
  KEY `in1` (`USER_ID`),
  KEY `in2` (`REQ_ORD_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金流入订单表';

-- ----------------------------
--  Table structure for `cpi_fund_poll_param`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_fund_poll_param`;
CREATE TABLE `cpi_fund_poll_param` (
  `QRY_CYC_ID` varchar(24) NOT NULL COMMENT '主键',
  `RUT_CORG` varchar(16) NOT NULL COMMENT '路径合作机构',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '业务子类型',
  `EFF_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '生效失效标识，0失效，1生效',
  `ORD_PRI_LEV` int(11) NOT NULL COMMENT '订单优先级',
  `CYC_ORD_CNT` int(11) NOT NULL DEFAULT '0' COMMENT '调起笔数',
  `BEFORE_TIME` int(11) NOT NULL DEFAULT '0' COMMENT '查找多长时间之前的订单(秒)',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员ID',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`QRY_CYC_ID`),
  UNIQUE KEY `in1` (`RUT_CORG`,`CORP_BUS_TYP`,`CORP_BUS_SUB_TYP`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资金流入轮询查询结果补单参数表';

-- ----------------------------
--  Table structure for `cpi_lock`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_lock`;
CREATE TABLE `cpi_lock` (
  `LOCK_ID` varchar(24) NOT NULL COMMENT '锁ID',
  `LOCK_NAME` varchar(128) NOT NULL DEFAULT ' ' COMMENT '锁名称',
  `LOCK_STS` varchar(2) NOT NULL DEFAULT ' ' COMMENT '锁状态，U：未加锁；L：已加锁',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '更新时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对账批次锁状态表';

-- ----------------------------
--  Table structure for `cpi_refund_order`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_refund_order`;
CREATE TABLE `cpi_refund_order` (
  `RFD_ORD_NO` varchar(24) NOT NULL COMMENT '内部订单号',
  `ORD_DT` date NOT NULL COMMENT '订单日期',
  `ORD_TM` time NOT NULL COMMENT '订单时间',
  `ORD_CCY` char(3) NOT NULL DEFAULT '' COMMENT '币种',
  `ORD_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `ORD_STS` char(2) NOT NULL DEFAULT '' COMMENT '订单状态，W1：等待充值，S1：交易成功，E1：交易逾期，F1：交易失败',
  `ORD_SUCC_DT` date DEFAULT NULL COMMENT '订单成功日期',
  `ORD_SUCC_TM` time DEFAULT NULL COMMENT '订单成功时间',
  `USER_TYP` char(1) NOT NULL DEFAULT 'U' COMMENT '用户类型，U：用户，B：商户',
  `USER_ID` varchar(20) NOT NULL DEFAULT ' ' COMMENT '用户ID',
  `MBL_NO` varchar(16) NOT NULL DEFAULT ' ' COMMENT '手机号',
  `CRD_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '资金合作机构号',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT 'D' COMMENT '银行卡类型，C贷记卡，D借记卡',
  `CRD_NO_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT '加密银行卡号',
  `CRD_USR_NM` varchar(32) NOT NULL DEFAULT ' ' COMMENT '用户姓名',
  `CRD_NO_LAST` char(4) NOT NULL DEFAULT '' COMMENT '银行卡后4位',
  `ID_TYP` char(2) NOT NULL DEFAULT '' COMMENT '证件类型',
  `ID_NO_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT '加密证件号码',
  `BNK_PSN_FLG` char(1) NOT NULL DEFAULT 'C' COMMENT '对公对私标志，B对公，C对私',
  `RUT_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构号',
  `CORP_BUS_TYP` char(2) NOT NULL DEFAULT '' COMMENT '合作业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL DEFAULT '' COMMENT '合作业务子类型',
  `AUTO_PAY_FLG` char(1) NOT NULL DEFAULT 'A' COMMENT '自动付款标志，A：自动，M：人工',
  `AGR_PAY_DT` date NOT NULL COMMENT '协议付款日',
  `RFD_TIME` datetime DEFAULT NULL COMMENT '退款时间',
  `RFD_FAIL_COUNT` int(11) NOT NULL DEFAULT '0' COMMENT '退款失败次数',
  `ORG_RMK` varchar(128) NOT NULL DEFAULT ' ' COMMENT '银行摘要',
  `APPR_STS` char(1) NOT NULL DEFAULT '0' COMMENT '审批状态，0：不需要审批，1：待审批，2：审批付款，3：审批拒绝',
  `APPR_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '审批操作员',
  `APPR_DT` date DEFAULT NULL COMMENT '审批日期',
  `APPR_TM` time DEFAULT NULL COMMENT '审批时间',
  `APPR_REFUSE_RSN` varchar(256) NOT NULL DEFAULT ' ' COMMENT '审批拒绝原因',
  `NTF_STS` char(1) NOT NULL DEFAULT '' COMMENT '通知状态，W：待通知，S：通知成功，F：通知失败（可重发），E：通知失败（不可重发）',
  `NTF_DT` date DEFAULT NULL COMMENT '通知日期',
  `NTF_TM` time DEFAULT NULL COMMENT '通知时间',
  `NTF_RSP_CD` varchar(10) NOT NULL DEFAULT ' ' COMMENT '通知返回码',
  `NTF_RSP_MSG` varchar(256) NOT NULL DEFAULT ' ' COMMENT '通知返回信息',
  `REQ_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '请求订单号',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`RFD_ORD_NO`),
  KEY `Index_1` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='退款订单表';

-- ----------------------------
--  Table structure for `cpi_refund_param`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_refund_param`;
CREATE TABLE `cpi_refund_param` (
  `RFD_PAR_ID` varchar(20) NOT NULL COMMENT '主键',
  `RUT_CORG` varchar(16) NOT NULL COMMENT '路径合作机构',
  `corp_bus_typ` char(2) DEFAULT NULL,
  `corp_bus_sub_typ` char(4) DEFAULT NULL,
  `IO_FLG` char(1) NOT NULL DEFAULT '1' COMMENT '结算模式，1：收支两条线，2：轧差',
  `SAD_RFD_FLG` char(1) NOT NULL DEFAULT 'Y' COMMENT '当日退款标识，Y支持，N不支持',
  `MTS_RFD_FLG` char(1) NOT NULL DEFAULT 'Y' COMMENT '多次退款标识，Y支持，N不支持',
  `AUTO_RFD_FLG` char(1) NOT NULL DEFAULT 'Y' COMMENT '自动退款标识，Y支持，N不支持',
  `OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '操作员ID',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`RFD_PAR_ID`),
  UNIQUE KEY `in1` (`RUT_CORG`,`corp_bus_typ`,`corp_bus_sub_typ`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='退款参数表';

-- ----------------------------
--  Table structure for `cpi_refund_suborder`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_refund_suborder`;
CREATE TABLE `cpi_refund_suborder` (
  `SUB_ORD_NO` varchar(24) NOT NULL COMMENT '内部子订单号',
  `RFD_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '内部订单号',
  `FND_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '充值订单号',
  `FND_SUB_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '充值子订单号',
  `ORD_DT` date DEFAULT NULL COMMENT '订单日期',
  `ORD_TM` time DEFAULT NULL COMMENT '订单时间',
  `CRD_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '资金合作机构号',
  `RUT_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构号',
  `CORP_BUS_TYP` char(2) NOT NULL DEFAULT '' COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL DEFAULT '' COMMENT '业务子类型',
  `ORD_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `ORD_STS` char(2) NOT NULL DEFAULT '' COMMENT '订单状态，W1：等待充值，S1：交易成功，E1：交易逾期，F1：交易失败',
  `AC_DT` date DEFAULT NULL COMMENT '记账日期',
  `ORG_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '合作机构外部订单号（系统生成）',
  `ORG_JRN_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '合作机构返回的流水号',
  `ORG_ORD_DT` date DEFAULT NULL COMMENT '合作机构交易日期',
  `ORG_ORD_TM` time DEFAULT NULL COMMENT '合作机构交易时间',
  `QRY_STS` char(1) NOT NULL DEFAULT 'W' COMMENT '结果查询状态，W：未查询，U：未明，S：成功，F：失败',
  `QRY_RSP_CD` varchar(16) NOT NULL DEFAULT ' ' COMMENT '结果查询返回码',
  `QRY_RSP_MSG` varchar(256) NOT NULL DEFAULT ' ' COMMENT '结果查询返回信息',
  `ORG_RSP_CD` varchar(10) NOT NULL DEFAULT ' ' COMMENT '合作机构返回码',
  `ORG_RSP_MSG` varchar(256) NOT NULL DEFAULT ' ' COMMENT '合作机构返回信息',
  `NTF_STS` char(1) NOT NULL DEFAULT '' COMMENT '合作机构通知状态，U：未明，S：成功，F：失败',
  `CHK_KEY` varchar(24) NOT NULL DEFAULT ' ' COMMENT '对账键值',
  `CHK_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '对账标志，0：需要对账，1：不需要对账',
  `CHK_STS` char(1) NOT NULL DEFAULT '0' COMMENT '对账状态，0：未对账，1：对账成功，2：我方有对方无，3：对方有我方无，4：金额错，5：存疑',
  `CHK_DT` date DEFAULT NULL COMMENT '对账日期',
  `CHK_TM` time DEFAULT NULL COMMENT '对账时间',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`SUB_ORD_NO`),
  UNIQUE KEY `in1` (`RFD_ORD_NO`),
  KEY `in2` (`FND_ORD_NO`),
  KEY `in3` (`CHK_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='退款订单子表';

-- ----------------------------
--  Table structure for `cpi_remit_order`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_remit_order`;
CREATE TABLE `cpi_remit_order` (
  `SUB_ORD_NO` varchar(20) NOT NULL COMMENT '内部子订单号',
  `FND_ORD_NO` varchar(20) NOT NULL DEFAULT ' ' COMMENT '内部订单号',
  `ORD_DT` date DEFAULT NULL COMMENT '订单日期',
  `ORD_TM` time DEFAULT NULL COMMENT '订单时间',
  `AC_DT` date DEFAULT NULL COMMENT '记账日期',
  `CRD_CORP_ORG` varchar(16) NOT NULL COMMENT '资金合作机构',
  `RUT_CORP_ORG` varchar(16) NOT NULL COMMENT '路径合作机构',
  `CORP_BUS_TYP` char(2) NOT NULL COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` char(4) NOT NULL COMMENT '业务子类型',
  `ORD_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `ORD_STS` char(2) NOT NULL DEFAULT '' COMMENT '订单状态，W1：等待充值，S1：交易成功，E1：交易逾期，F1：交易失败',
  `CHK_KEY` varchar(24) NOT NULL DEFAULT ' ' COMMENT '对账键值',
  `CHK_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '对账标志，0：需要对账，1：不需要对账',
  `CHK_STS` char(1) NOT NULL DEFAULT '0' COMMENT '对账状态，0：未对账，1：对账成功，2：我方有对方无，3：对方有我方无，4：金额错，5：存疑',
  `CHK_DT` date DEFAULT NULL COMMENT '对账日期',
  `CHK_TM` time DEFAULT NULL COMMENT '对账时间',
  `RMK` varchar(128) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`SUB_ORD_NO`),
  KEY `in1` (`FND_ORD_NO`),
  KEY `in2` (`CHK_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='汇款订单表';

-- ----------------------------
--  Table structure for `cpi_shortcut_order`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_shortcut_order`;
CREATE TABLE `cpi_shortcut_order` (
  `SUB_ORD_NO` varchar(24) NOT NULL COMMENT '内部子订单号',
  `FND_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '内部订单号',
  `ORD_DT` date DEFAULT NULL COMMENT '订单日期',
  `ORD_TM` time DEFAULT NULL COMMENT '订单时间',
  `AC_DT` date DEFAULT NULL COMMENT '记账日期',
  `AGR_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '是否签约支付，Y：签约支付，N：免签约支付',
  `AGR_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '内部协议号',
  `CRD_CORP_ORG` varchar(16) NOT NULL COMMENT '资金合作机构',
  `RUT_CORP_ORG` varchar(16) NOT NULL COMMENT '路径合作机构',
  `CORP_BUS_TYP` varchar(2) NOT NULL COMMENT '业务类型',
  `CORP_BUS_SUB_TYP` varchar(4) NOT NULL COMMENT '业务子类型',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT 'D' COMMENT '卡种，D贷记卡，C借记卡',
  `ORD_AMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `ORD_STS` char(2) NOT NULL DEFAULT '' COMMENT '订单状态',
  `CRD_CVV2_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT 'CVV2',
  `CRD_EXP_DT_ENC` varchar(64) NOT NULL DEFAULT ' ' COMMENT '有效期',
  `SMS_CHK_FLG` char(1) NOT NULL DEFAULT 'N' COMMENT '是否校验短信验证码，Y：是，N：否',
  `SMS_JRN_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '短信校验成功流水号',
  `ORG_ORD_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '合作机构外部订单号（系统生成）',
  `ORG_JRN_NO` varchar(24) NOT NULL DEFAULT ' ' COMMENT '合作机构返回的流水号',
  `ORG_ORD_DT` date DEFAULT NULL COMMENT '合作机构交易日期',
  `ORG_ORD_TM` time DEFAULT NULL COMMENT '合作机构交易时间',
  `ORG_RSP_CD` varchar(8) NOT NULL DEFAULT ' ' COMMENT '合作机构返回码',
  `ORG_RSP_MSG` varchar(256) NOT NULL DEFAULT ' ' COMMENT '合作机构返回信息',
  `CHK_KEY` varchar(32) NOT NULL DEFAULT ' ' COMMENT '对账键值',
  `CHK_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '对账标志，0：需要对账，1：不需要对账',
  `CHK_STS` char(1) NOT NULL DEFAULT '0' COMMENT '对账状态，0：未对账，1：对账成功，2：我方有对方无，3：对方有我方无，4：金额错，5：存疑',
  `CHK_DT` date DEFAULT NULL COMMENT '对账日期',
  `CHK_TM` time DEFAULT NULL COMMENT '对账时间',
  `RMK` varchar(256) NOT NULL DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`SUB_ORD_NO`),
  UNIQUE KEY `in1` (`FND_ORD_NO`),
  KEY `in2` (`CHK_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='快捷订单表';

-- ----------------------------
--  Table structure for `cpi_shortcut_param`
-- ----------------------------
DROP TABLE IF EXISTS `cpi_shortcut_param`;
CREATE TABLE `cpi_shortcut_param` (
  `SHT_PAR_ID` varchar(24) NOT NULL COMMENT '主键',
  `RUT_CORP_ORG` varchar(16) NOT NULL DEFAULT ' ' COMMENT '路径合作机构',
  `CRD_AC_TYP` char(1) NOT NULL DEFAULT '' COMMENT '卡种，D借记卡，C贷记卡',
  `SMS_TYP` varchar(32) NOT NULL DEFAULT '0' COMMENT '短信类型，0平台下发平台校验，1银行下发平台校验，2银行下发银行校验',
  `EXP_DT_TYP` varchar(64) NOT NULL DEFAULT '0' COMMENT '是否需要有效期，0不需要，1需要',
  `CVV2_TYP` char(1) NOT NULL DEFAULT '0' COMMENT '是否需要CVV2，0不需要，1需要',
  `SING_PTL` char(1) NOT NULL DEFAULT '0' COMMENT '单双协议类型，0单侧，1双侧',
  `BNK_MBL_TYP` char(1) NOT NULL DEFAULT '0' COMMENT '是否需要银行预留手机号，0不需要，1需要',
  `AMT_D_LMT` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '日限额',
  `UNSIGN_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '解约标识，0单侧，1双侧',
  `SUC_SMS_FLG` char(1) NOT NULL DEFAULT '0' COMMENT '成功短信标识，0不下发，1下发',
  `CRE_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '创建人ID',
  `UPD_OPR_ID` varchar(16) NOT NULL DEFAULT ' ' COMMENT '修改人ID',
  `RMK` varchar(256) DEFAULT ' ' COMMENT '备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `MODIFY_TIME` datetime NOT NULL COMMENT '修改时间',
  `TM_SMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`SHT_PAR_ID`),
  UNIQUE KEY `in1` (`RUT_CORP_ORG`,`CRD_AC_TYP`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='快捷银行参数表';

-- ----------------------------
--  Records 
-- ----------------------------
INSERT INTO `cpi_acc_cfg` VALUES ('20170722104837003501','ICBC','02','0201','com.hisun.lemon.cpi.bnkapi.icbc.CheckAccICBCShortcutApi','getCheckFile','companyDate|companySerino|trxType|bankSerno|settleDate|cardNo|payAmt|currType|status','com.hisun.lemon.cpi.entity.icbc.AccIcbcShortcutDO','batchInsertCheckDO','\\|','E:\\chkfile','1','getAccDOList','2','companySerino','','payAmt','status','200','updateAccDOChkSts','2017-07-22','10:48:37','2017-07-24 11:28:38'), ('20170724112547009001','ICBC','05','0501','com.hisun.lemon.cpi.bnkapi.icbc.CheckAccICBCRefundApi','getCheckFile','companyDate|companySerino|trxType|bankSerno|settleDate|cardNo|payAmt|currType|status','com.hisun.lemon.cpi.entity.icbc.AccIcbcRefundDO','batchInsertCheckDO','\\|','E:\\chkfile','1','getAccDOList','2','companySerino','','payAmt','status','200','updateAccDOChkSts','2017-07-24','11:25:47','2017-07-24 14:27:48'), ('20170724112547009002','BESTPAY','03','0302','com.hisun.lemon.cpi.bnkapi.bestpay.CheckAccEbankpayApi','getCheckFile','mercId|bnkChkKey|platJrnNo|platOrdNo|ordCyy|ordForeignAmt|ordAmt|exchangeRate','com.hisun.lemon.cpi.entity.bestpay.AccBestpayEbankpayDO','batchInsertCheckDO','\\|','E:\\chkfile','1','getAccDOList','','platOrdNo','','ordForeignAmt','','200','updateAccDOChkSts','2017-07-24','11:25:47','2017-08-03 20:19:28'), ('20170724112547009003','BESTPAY','05','0502','com.hisun.lemon.cpi.bnkapi.bestpay.CheckAccEbankpayRefundApi','getCheckFile','mercId|bnkChkKey|platJrnNo|platOrdNo|ordCyy|ordForeignAmt|ordAmt|exchangeRate','com.hisun.lemon.cpi.entity.bestpay.AccBestpayEbankpayRefundDO','batchInsertCheckDO','\\|','E:\\chkfile','1','getAccDOList','','platOrdNo','','ordForeignAmt','','200','updateAccDOChkSts','2017-07-24','11:25:47','2017-08-04 19:18:46');
INSERT INTO `cpi_acc_param` VALUES ('CHK20170720000001001','ICBC','02','0201','Y','Y','B','09:30:00','23:30:00','N','N','N','SYS666666','2017-07-20 10:43:12','2017-07-20 10:43:12','2017-07-20 10:43:07'), ('CHK201707241137089001','ICBC','05','0501','Y','Y','B','09:30:00','23:30:00','N','N','N','SYS001','2017-07-24 11:37:08','2017-07-24 11:37:08','2017-07-24 11:37:00'), ('CHK20170724113708902','BESTPAY','05','0502','Y','Y','B','09:30:00','23:30:00','N','N','N','SYS001','2017-07-24 11:37:08','2017-07-24 11:37:08','2017-07-24 11:37:00'), ('CHK20170724113708903','BESTPAY','03','0302','Y','Y','B','09:30:00','23:30:00','N','N','N','SYS001','2017-07-24 11:37:08','2017-07-24 11:37:08','2017-07-24 11:37:00');
INSERT INTO `cpi_card_bin` VALUES ('111','12345','ICBC','D','10',' ','2017-07-12 20:06:44','2017-07-12 20:06:47','2017-07-12 20:06:49');
INSERT INTO `cpi_cop_agcy_biz` VALUES ('1111','ICBC','03','0301','1','',' ','2017-07-12 10:24:48','2017-07-12 10:24:51','2017-07-12 10:24:51'), ('1112','ICBC','04','0401','1','',' ','2017-07-12 10:24:48','2017-07-12 10:24:51','2017-07-12 10:24:51'), ('1113','ICBC','01','0101','1','',' ','2017-07-12 20:10:41','2017-07-12 20:10:43','2017-07-12 20:10:45'), ('1114','BESTPAY','03','0302','1','',' ','2017-08-02 19:39:41','2017-08-02 19:39:38','2017-08-02 19:39:43'), ('1115','BESTPAY','03','0303','1','',' ','2017-08-02 19:40:00','2017-08-02 19:40:02','2017-08-02 19:40:04');
INSERT INTO `cpi_cop_agcy_info` VALUES ('111','ICBC',' 中国工商银行',' 工商银行','0',' ',' ','2017-07-12 10:24:21','2017-07-12 10:24:23','2017-07-12 10:24:23'), ('112','BESTPAY',' 翼支付','  翼支付','0',' ',' ','2017-08-02 19:40:20','2017-08-02 19:40:23','2017-08-02 19:40:25');
INSERT INTO `cpi_cop_biz_rut` VALUES ('111','ICBC','03','0301','ICBC','D','1','99','0.00','5000.00',' ',' ','2017-07-12 10:18:01','2017-07-12 10:18:04','2017-07-12 10:23:51'), ('112','ICBC','04','0401','ICBC','D','1','99','0.00','5000.00',' ',' ','2017-07-12 10:18:01','2017-07-12 10:18:04','2017-07-12 10:23:51'), ('113','ICBC','01','0101','ICBC','D','1','99','0.00','99999999.00',' ',' ','2017-07-12 20:10:07','2017-07-12 20:10:11','2017-07-12 20:10:19'), ('114','BESTPAY','03','0302','BESTPAY','D','1','99','0.00','99999999.00',' ',' ','2017-07-12 20:10:07','2017-07-12 20:10:11','2017-07-12 20:10:19'), ('115','BESTPAY','03','0303','BESTPAY','D','1','99','0.00','99999999.00',' ',' ','2017-07-12 20:10:07','2017-07-12 20:10:11','2017-07-12 20:10:19');
INSERT INTO `cpi_fund_poll_param` VALUES ('11','ICBC','03','0301','1','99','10','600',' ',' ','2017-07-17 19:34:44','2017-07-17 19:34:46','2017-07-17 19:37:28');
INSERT INTO `cpi_refund_param` VALUES ('1','BESTPAY','05','0501','1','Y','Y','Y',' ',' ','2017-07-11 17:16:45','2017-07-11 17:16:48','2017-07-11 17:48:39'), ('111111','ICBC','05','0501','1','Y','Y','Y',' ',' ','2017-07-11 17:16:45','2017-07-11 17:16:48','2017-07-11 17:48:39'), ('111112','ICBC','05','0502','1','Y','Y','Y',' ',' ','2017-07-12 11:03:57','2017-07-12 11:04:00','2017-07-12 11:04:13'), ('2','BESTPAY','05','0502','1','Y','Y','Y',' ',' ','2017-07-11 17:16:45','2017-07-11 17:16:48','2017-07-11 17:48:39');
INSERT INTO `cpi_shortcut_param` VALUES ('11','ICBC','D','2','0','0','0','0','100.00','0','0',' ',' ',' ','2017-07-12 20:13:47','2017-07-12 20:13:49','2017-07-16 11:04:39');
