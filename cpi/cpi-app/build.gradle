apply plugin: 'application'

dependencies {
    compile project(":cpi-interface")
    compile project(":cpi-icbc")
    compile project(":cpi-wechat")
    compile project(":cpi-bestpay")
    compile project(":cpi-alipay")
    compile project(":cpi-acleda")
    compile project(":cpi-cregis")
    compile("com.hisun:lemon-swagger")
    compile("com.hisun:lemon-framework")
    compile("com.hisun:jcommon")
    compile("com.hisun:acm-interface")
    compile("com.hisun:pwm-interface")
    compile("com.hisun:urm-interface")
    compile("com.hisun:rsm-interface")
    compile("com.hisun:chk-interface")
    compile("com.hisun:csh-interface")
    compile("com.hisun:cmm-interface")
    compile("com.hisun:bil-interface:1.0.0-SNAPSHOT")
    compile("com.hisun:lemon-framework-lock")
    compile("com.hisun:tam-interface")
    // 添加 Hutool 依赖
    implementation 'cn.hutool:hutool-all:5.8.16'
}

mainClassName = 'com.hisun.lemon.Application'

jar {
    manifest {
        attributes(
                "Implementation-Title": "Gradle",
                "Implementation-Version": "${version}",
                "Class-Path": '. config/'
        )
    }
    //exclude('config/')
}

bootRepackage {
    enabled = true
    mainClass = 'com.hisun.lemon.Application'
}

task clearTarget(type:Delete){
    delete 'build/target'
}

task release(type: Copy,dependsOn: [clearTarget,build]) {
    from('build/libs') {
        include '*.jar'
        exclude '*-sources.jar'
    }
    //from('src/main/resources') {
    //    include 'config/*'
    //}
    into ('build/target')

    into('bin') {
        from 'shell'
    }
}

task dist(type: Zip,dependsOn: [release]) {
    from ('build/target/') {
    }
}