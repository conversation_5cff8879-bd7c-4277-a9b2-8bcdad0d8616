<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:channel="http://www.hisun.com/schema/channel"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.hisun.com/schema/channel http://www.hisun.com/schema/channel/channel.xsd">
    <!-- acleda签名规则 -->
    <bean id="acledaSignature" class="com.hisun.lemon.cpi.acleda.ebankpay.ACLEDASecurity"/>

    <channel:service name="ACLEDA" charset="UTF-8" >

        <!--<channel:connector id="http1" protocol="http" url="http://123.172.98.30:21277" default="false"   dateTypeName="FORM" charset="UTF-8"/>-->
        
        <!-- OpenSessionV2 -->
        <channel:connector id="openSessionV2" protocol="http" url="https://epaymentuat.acledabank.com.kh:8443/SEATEL/XPAYMobilePayment/XPAYRestRS/openSessionv2" default="false" dateTypeName="STRING" charset="UTF-8" />

        <!--setDirection-->
        <channel:connector id="setDirection" protocol="http" url="https://epaymentuat.acledabank.com.kh:8443/SEATEL/XPAYMobilePayment/XPAYRestRS/SetDirection" default="false" dateTypeName="STRING" charset="UTF-8" />

        <!-- GetTransactionStatus-->
        <channel:connector id="getTransactionStatus" protocol="http" url="https://epaymentuat.acledabank.com.kh:8443/SEATEL/XPAYMobilePayment/XPAYRestRS/getTransactionStatus" default="false" dateTypeName="STRING" charset="UTF-8" />

        <!-- closeSession-->
        <channel:connector id="closeSession" protocol="http" url="https://epaymentuat.acledabank.com.kh:8443/SEATEL/XPAYMobilePayment/XPAYRestRS/CloseSession" default="false" dateTypeName="STRING" charset="UTF-8" />

        <channel:container>
            <channel:signature ref="acledaSignature" />

            <!--OpenSessionV2-->
            <channel:processor name="openSessionV2" marshal-class="com.hisun.lemon.cpi.acleda.ebankpay.req.OpenSessionV2Req"
                               unmarshal-class="com.hisun.lemon.cpi.acleda.ebankpay.rsp.OpenSessionV2Rsp" connector-id="openSessionV2" />

            <!--setDirection-->
            <channel:processor name="setDirection" marshal-class="com.hisun.lemon.cpi.acleda.ebankpay.req.DirectionReq"
                               unmarshal-class="com.hisun.lemon.cpi.acleda.ebankpay.rsp.DirectionRsp" connector-id="setDirection" />

            <!--GetTransactionStatus-->
            <channel:processor name="transactionQuery" marshal-class="com.hisun.lemon.cpi.acleda.ebankpay.req.TransactionQueryReq"
                               unmarshal-class="com.hisun.lemon.cpi.acleda.ebankpay.rsp.TransactionQueryRsp" connector-id="getTransactionStatus" />

            <!--closeSession-->
            <channel:processor name="closeSession" marshal-class="com.hisun.lemon.cpi.acleda.ebankpay.req.CloseSessionReq"
                               unmarshal-class="com.hisun.lemon.cpi.acleda.ebankpay.rsp.CloseSessionRsp" connector-id="closeSession" />

            <!-- 此处的handler-filter对全部processor生效 -->
            <channel:handler-filter class="com.hisun.channel.service.child.filter.MonitorHandlerFilter" />
            <!-- <channel:handler-filter class="com.hisun.channel.service.child.filter.MessageContext" /> -->

        </channel:container>
    </channel:service>
</beans>