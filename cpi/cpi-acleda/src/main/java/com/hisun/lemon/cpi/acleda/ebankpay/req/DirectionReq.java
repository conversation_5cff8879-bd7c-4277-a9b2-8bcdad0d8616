package com.hisun.lemon.cpi.acleda.ebankpay.req;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;

import javax.validation.constraints.NotNull;

@Plain(form = Plain.Form.HTTP_FORM)
public class DirectionReq {

    @Item
    @NotNull
    private String sessionid;

    @Item
    @NotNull
    private String ptokenid;

    @Item
    @NotNull
    private int direction;

    @Item
    @NotNull
    private int accountnumber;

    public String getSessionid() {
        return sessionid;
    }

    public void setSessionid(String sessionid) {
        this.sessionid = sessionid;
    }

    public String getPtokenid() {
        return ptokenid;
    }

    public void setPtokenid(String ptokenid) {
        this.ptokenid = ptokenid;
    }

    public int getDirection() {
        return direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }

    public int getAccountnumber() {
        return accountnumber;
    }

    public void setAccountnumber(int accountnumber) {
        this.accountnumber = accountnumber;
    }

    @Override
    public String toString() {
        return "DirectionReq{" +
                "sessionId='" + sessionid + '\'' +
                ", ptokenid='" + ptokenid + '\'' +
                ", direction=" + direction +
                ", accountnumber=" + accountnumber +
                '}';
    }
}
