package com.hisun.lemon.cpi.acleda.ebankpay;

import com.hisun.channel.client.IChannelClient;
import com.hisun.channel.data.Request;
import com.hisun.channel.data.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AcledaApi {

	@Autowired
	private IChannelClient channelClient;
	@SuppressWarnings("unchecked")
	public <T> T doSend(Object obj, AcledaEnumCommon.EnumSource source) {
		if (source == null) {
			return null;
		}
		Request request = new Request();
		request.setRoute("ACLEDA");
		request.setBusiType(source.name());
		request.setTarget(obj);
		request.setSource(source.name());
		request.setRequestId("1");
		Response response = channelClient.request(request);
		Object t =  response.getResult();
		return (T)t;
	}
}
