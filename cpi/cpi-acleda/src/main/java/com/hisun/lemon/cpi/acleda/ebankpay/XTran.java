package com.hisun.lemon.cpi.acleda.ebankpay;

public class XTran {

    private Double purchaseAmount;
    private Long   purchaseDate;
    private int quantity;
    private String paymentTokenid;
    private int expiryTime;


    public Double getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(Double purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public Long getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(Long purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getPaymentTokenid() {
        return paymentTokenid;
    }

    public void setPaymentTokenid(String paymentTokenid) {
        this.paymentTokenid = paymentTokenid;
    }

    public int getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(int expiryTime) {
        this.expiryTime = expiryTime;
    }

    @Override
    public String toString() {
        return "XTran{" +
                "purchaseAmount=" + purchaseAmount +
                ", purchaseDate=" + purchaseDate +
                ", quantity=" + quantity +
                ", paymentTokenid='" + paymentTokenid + '\'' +
                ", expiryTime=" + expiryTime +
                '}';
    }
}
