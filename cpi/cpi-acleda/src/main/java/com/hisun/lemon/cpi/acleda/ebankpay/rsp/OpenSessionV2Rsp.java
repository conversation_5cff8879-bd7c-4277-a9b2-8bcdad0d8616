package com.hisun.lemon.cpi.acleda.ebankpay.rsp;


import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Json;
import com.hisun.lemon.cpi.acleda.ebankpay.XTran;

@Json
public class OpenSessionV2Rsp {

    @Item
    private int Code;

    @Item
    private String errorDetails;

    @Item
    private String sessionId;

    @Item
    private XTran xtran;

    public int getCode() {
        return Code;
    }

    public void setCode(int code) {
        Code = code;
    }

    public String getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public XTran getXtran() {
        return xtran;
    }

    public void setXtran(XTran xtran) {
        this.xtran = xtran;
    }

    @Override
    public String toString() {
        return "OpenSessionV2Rsp{" +
                "Code=" + Code +
                ", errorDetails='" + errorDetails + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", xTran=" + xtran +
                '}';
    }
}
