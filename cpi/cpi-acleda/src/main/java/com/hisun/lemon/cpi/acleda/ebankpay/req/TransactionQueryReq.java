package com.hisun.lemon.cpi.acleda.ebankpay.req;


import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;

import javax.validation.constraints.NotNull;

@Plain(form = Plain.Form.HTTP_FORM)
public class TransactionQueryReq {

    @Item
    @NotNull
    private String sessionid;

    @Item
    @NotNull
    private String ptokenId;

    public String getSessionid() {
        return sessionid;
    }

    public void setSessionid(String sessionid) {
        this.sessionid = sessionid;
    }

    public String getPtokenId() {
        return ptokenId;
    }

    public void setPtokenId(String ptokenId) {
        this.ptokenId = ptokenId;
    }

    @Override
    public String toString() {
        return "TransactionQueryReq{" +
                "sessionid='" + sessionid + '\'' +
                ", ptokenId='" + ptokenId + '\'' +
                '}';
    }
}
