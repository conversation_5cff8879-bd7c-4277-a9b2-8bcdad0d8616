package com.hisun.lemon.cpi.acleda.ebankpay;


import com.hisun.channel.common.lifecycle.LifecycleBase;
import com.hisun.channel.common.lifecycle.LifecycleState;
import com.hisun.channel.common.utils.StringUtils;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;



public class ACLEDASecurity extends LifecycleBase implements com.hisun.channel.common.core.Signature {

	private ACLEDASecurity() {
	}

	private static final Logger logger = LoggerFactory.getLogger(ACLEDASecurity.class);

	private static final Base64 base64=new Base64();

	@Autowired
	AcledaProperties acledaProperties;

	// 接口文档中附带的测试密钥
	private static final String MERCHANT_SECRET = "g8ezfA1MgA4gwnNAm5eJqENYC0kHmKdU";

	// 缓存provider
	private static Map<String, Provider> providerMap = new HashMap<String, Provider>();

	/**
	 * ACLEDA签名算法
	 *
	 * @param strData
	 *            报文
	 * @return
	 */
	public static String signature(String strData) {
			try {
				logger.info("签名数据:" + strData + MERCHANT_SECRET);
				String md5String = getMD5ofByte((strData + MERCHANT_SECRET).getBytes("UTF-8"));
				if (StringUtils.isBlank(md5String)) {
					return null;
				} else {
					return md5String.toLowerCase();
				}
			} catch (UnsupportedEncodingException e) {
				logger.error("ACLEDASecurity.signature 异常：", e);
				return null;
			}

	}

	/**
	 *
	 * 获得MD5加密密码的方法
	 */
	private static String getMD5ofByte(byte[] data) {
		String origMD5 = null;
		try {
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			byte[] result = md5.digest(data);
			origMD5 = byteArray2HexStr(result);
		} catch (Exception e) {
			logger.error("WEIXINSecurity.getMD5ofByte md5加密异常：", e);
		}
		return origMD5;
	}

	/**
	 *
	 * 处理字节数组得到MD5密码的方法
	 */
	private static String byteArray2HexStr(byte[] bs) {
		StringBuffer sb = new StringBuffer();
		for (byte b : bs) {
			sb.append(byte2HexStr(b));
		}
		return sb.toString();
	}

	/**
	 *
	 * 字节标准移位转十六进制方法
	 */
	private static String byte2HexStr(byte b) {
		String hexStr = null;
		int n = b;
		if (n < 0) {
			// 定义移位算法
			n = b & 0x7F + 128;
		}
		hexStr = Integer.toHexString(n / 16) + Integer.toHexString(n % 16);
		return hexStr.toUpperCase();
	}

	@Override
	public String sign(String signStr) {
		return ACLEDASecurity.signature(signStr);
	}

	@Override
	public boolean verify(String verifyStr, String signStr) {
		return false;
	}

	@Override
	protected void doInit() {

	}

	@Override
	protected void doStart() {
		this.setState(LifecycleState.STARTING);
	}

	@Override
	protected void doStop() {
		this.setState(LifecycleState.STOPPING);
	}

	@Override
	protected void doDestroy() {

	}
}
