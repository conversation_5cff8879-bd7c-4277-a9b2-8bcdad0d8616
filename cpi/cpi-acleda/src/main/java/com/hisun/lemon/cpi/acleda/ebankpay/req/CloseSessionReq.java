package com.hisun.lemon.cpi.acleda.ebankpay.req;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;

import javax.validation.constraints.NotNull;

@Plain(form = Plain.Form.HTTP_FORM)
public class CloseSessionReq {

    @Item
    @NotNull
    private String sessionId;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
