package com.hisun.lemon.cpi.acleda.ebankpay;

import com.alibaba.fastjson.JSONObject;

import java.util.*;

public class TransactionDetails {

    private String txid;
    private Double purchaseAmount;
    private String purchaseCurrency;
    private Long   purchaseDate;
    private String purchaseDesc;
    private String invoiceid;
    private String item;
    private int quantity;
    private int expiryTime;

    public String getTxid() {
        return txid;
    }

    public void setTxid(String txid) {
        this.txid = txid;
    }

    public Double getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(Double purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public String getPurchaseCurrency() {
        return purchaseCurrency;
    }

    public void setPurchaseCurrency(String purchaseCurrency) {
        this.purchaseCurrency = purchaseCurrency;
    }

    public Long getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(Long purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public String getPurchaseDesc() {
        return purchaseDesc;
    }

    public void setPurchaseDesc(String purchaseDesc) {
        this.purchaseDesc = purchaseDesc;
    }

    public String getInvoiceid() {
        return invoiceid;
    }

    public void setInvoiceid(String invoiceid) {
        this.invoiceid = invoiceid;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public int getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(int expiryTime) {
        this.expiryTime = expiryTime;
    }

    @Override
    public String toString() {
        Map<String,Object> contentMap = new HashMap<>();
        JSONObject object = new JSONObject();
        if(this.getTxid() != null && this.getTxid() != ""){
            contentMap.put("txid",this.getTxid());
        }
        if(this.getPurchaseAmount() != null){
            contentMap.put("purchaseAmount",this.getPurchaseAmount());
        }
        if(this.getPurchaseCurrency() != null){
            contentMap.put("purchaseCurrency",this.getPurchaseCurrency());
        }

        if(this.getPurchaseDate() != null){
            contentMap.put("purchaseDate",this.getPurchaseDate());
        }

        if(this.getPurchaseDesc() != null && this.getPurchaseDesc() != ""){
            contentMap.put("purchaseDesc",this.getPurchaseDesc());
        }

        if(this.getInvoiceid() != null && this.getInvoiceid() != ""){
            contentMap.put("invoiceid",this.getInvoiceid());
        }

        if(this.getItem() != null && this.getItem() != ""){
            contentMap.put("item",this.getItem());
        }

        contentMap.put("quantity",this.getQuantity());
        contentMap.put("expiryTime",this.getExpiryTime());

        List<String> keys = new ArrayList(contentMap.keySet());
        Collections.sort(keys);
        for(String key : keys){
            object.put(key,contentMap.get(key));
        }
        return object.toJSONString();

    }
}
