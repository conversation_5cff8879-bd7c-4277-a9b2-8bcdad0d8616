package com.hisun.lemon.cpi.acleda.ebankpay;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * User : Rui
 * Date : 2017/8/29
 * Time : 11:24
 **/
@Component
@ConfigurationProperties(prefix = "aliPay")
@PropertySource("classpath:config/ebankpay-acleda.properties")
public class AcledaProperties {
    private String merchantId;
    private String secret;
    private String login;
    private String password;
    private String cashIn;
    private String cashOut;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCashIn() {
        return cashIn;
    }

    public void setCashIn(String cashIn) {
        this.cashIn = cashIn;
    }

    public String getCashOut() {
        return cashOut;
    }

    public void setCashOut(String cashOut) {
        this.cashOut = cashOut;
    }
}
