package com.hisun.lemon.cpi.acleda.ebankpay.req;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.lemon.cpi.acleda.ebankpay.TransactionDetails;

import javax.validation.constraints.NotNull;

@Plain(form = Plain.Form.HTTP_FORM)
public class OpenSessionV2Req {
    @Item
    @NotNull
    private String loginId;

    @Item
    @NotNull
    private String password;

    @Item
    @NotNull
    private String merchantId;

    @NotNull
    private String signature;

    @Item
    @NotNull
    private TransactionDetails transactionDetails;


    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public TransactionDetails getTransactionDetails() {
        return transactionDetails;
    }

    public void setTransactionDetails(TransactionDetails transactionDetails) {
        this.transactionDetails = transactionDetails;
    }

    @Override
    public String toString() {
        return "OpenSessionV2Req{" +
                "loginId='" + loginId + '\'' +
                ", password='" + password + '\'' +
                ", merchantId='" + merchantId + '\'' +
                ", signature='" + signature + '\'' +
                ", transactionDetails=" + transactionDetails +
                '}';
    }
}
