package com.hisun.lemon.cpi.acleda.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Json;

@Json
public class TransactionQueryRsp {

    @Item
    private int ResultCode;

    @Item
    private String ResultMessage;

    public int getResultCode() {
        return ResultCode;
    }

    public void setResultCode(int resultCode) {
        ResultCode = resultCode;
    }

    public String getResultMessage() {
        return ResultMessage;
    }

    public void setResultMessage(String resultMessage) {
        ResultMessage = resultMessage;
    }

    @Override
    public String toString() {
        return "TransactionQueryRsp{" +
                "ResultCode='" + ResultCode + '\'' +
                ", ResultMessage='" + ResultMessage + '\'' +
                '}';
    }
}
