package com.hisun.lemon.cpi.acleda.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Json;

@Json
public class CloseSessionRsp {

    @Item
    private int errorCode;

    @Item
    private String errorMessage;


    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
