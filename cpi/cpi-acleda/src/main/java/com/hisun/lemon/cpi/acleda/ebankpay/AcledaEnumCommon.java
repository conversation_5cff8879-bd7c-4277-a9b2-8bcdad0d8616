package com.hisun.lemon.cpi.acleda.ebankpay;



import org.springframework.stereotype.Controller;

import com.hisun.channel.common.utils.StringUtils;

/**
 * 枚举公共类
 * 
 * <AUTHOR>
 *
 */
@Controller
public class AcledaEnumCommon {

	private AcledaEnumCommon() {
	}

	/**
	 * 接口类型
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumSource {
		openSessionV2,
		setDirection,
		transactionQuery,
		closeSession
	}

	/**
	 * 账单类型
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumBillType {
		/**
		 * 返回当日所有订单信息
		 */
		ALL,
		/**
		 * 返回当日成功支付的订单
		 */
		SUCCESS,
		/**
		 * 返回当日退款订单
		 */
		REFUND,
		/**
		 * 返回当日充值退款订单（相比其他对账单多一栏“返还手续费”）
		 */
		RECHARGE_REFUND

	}

	/**
	 * 交易返回码
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumTrxStatus {
		HANDLING("22"),
		SUCCESS("31"),
		FAIL("32");

		private String code;

		public static EnumTrxStatus getEnum(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumTrxStatus trxStatus : values()) {
					if (StringUtils.equals(trxStatus.getCode(), code)) {
						return trxStatus;
					}
				}
			}
			return null;
		}

		EnumTrxStatus(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	/**
	 * 币种
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumCurrency {
		/**
		 * 人民币
		 */
		CNY,
		/**
		 * 港币
		 */
		HKD,
		/**
		 * 加币
		 */
		CAD,
		/**
		 * 美元
		 */
		USD,
		/**
		 * 欧元
		 */
		EUR,
		/**
		 * 英镑
		 */
		GBP,
		/**
		 * 澳元
		 */
		AUD,
		/**
		 * 新西兰元
		 */
		NZD,
		/**
		 * 日元
		 */
		JPY,
		/**
		 * 瑞士法郎
		 */
		CHF,
		/**
		 * 瑞典克朗
		 */
		SEK,
		/**
		 * 挪威克朗
		 */
		NOK,
		/**
		 * 新加坡元
		 */
		SGD,
		/**
		 * 泰国铢
		 */
		THB
	}

	/**
	 * 接口编号
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumInfCode {

		OFFLINEPAYMENT("VSPTRX_WEIXIN_CNP"),
		REVOKE("VSPTRX_WEIXIN_REFUND"),
		REFUND("VSPTRX_WEIXIN_REFUNDTN"),
		STATUSQUERY("VSPTRX_CUSTRXQUERY");

		private String code;

		public static EnumInfCode getEnum(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumInfCode infCode : values()) {
					if (StringUtils.equals(infCode.getCode(), code)) {
						return infCode;
					}
				}
			}
			return null;
		}

		EnumInfCode(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}



	/**
	 * 用户主扫支付宝订单状态
	 */
	public enum EnumTradeStatus{
		/**
		 * 等待付款
		 */
		WAIT_BUYER_PAY,
		/**
		 * 交易成功
		 */
		TRADE_SUCCESS,
		/**
		 * 交易关闭
		 */
		TRADE_CLOSED,
		/**
		 * 交易完成，不进行任何操作
		 */
		TRADE_FINISHED
	}

	/**
	 * 商户主扫支付宝订单状态
	 */
	public enum EnumMerTradeStatus{
		/**
		 * 付款成功
		 */
		SUCCESS ,
		/**
		 * 付款失败
		 */
		FAIL,
		/**
		 * 未知状态
		 */
		UNKNOW
	}

	/**
	 * 取消接口action值
	 */
	public enum EnumCloseStatus{
		/**
		 * 关闭
		 */
		CLOSE ,
		/**
		 * 退款
		 */
		REFUND,
	}

}
