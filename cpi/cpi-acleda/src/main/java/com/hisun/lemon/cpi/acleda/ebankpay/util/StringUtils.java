package com.hisun.lemon.cpi.acleda.ebankpay.util;

import com.hisun.lemon.cpi.acleda.ebankpay.TransactionDetails;
import com.hisun.lemon.cpi.acleda.ebankpay.req.OpenSessionV2Req;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Created by XianSky on 2017/12/27.
 */
public abstract class StringUtils {
    private StringUtils() {
    }


    public static String sort(OpenSessionV2Req openSessionV2Req){
        Field[] fields = openSessionV2Req.getClass().getDeclaredFields();
        //List<String> fieldList = Arrays.asList(fields).stream().map(Field::getName).collect(Collectors.toList());

        TransactionDetails transactionDetails = openSessionV2Req.getTransactionDetails();

        Map<String, String> map = new HashMap<String, String>();
        List<String> fieldNames = new ArrayList<String>();
        try{
            for (Field field : fields) {
                String fieName = field.getName();
                boolean accessFlag = field.isAccessible();
                field.setAccessible(true);
                Object obj = field.get(openSessionV2Req);
                if (obj != null) {
                    if (fieName != "signature") {
                        map.put(fieName, obj.toString());
                        fieldNames.add(fieName);
                    }
                }
                field.setAccessible(accessFlag);
            }

            StringBuilder paramStringBuffer = new StringBuilder();
            Collections.sort(fieldNames);
            for (int i = 0; i < fieldNames.size(); i++) {
                String key = fieldNames.get(i);
                String value = map.get(key);
                paramStringBuffer.append("&").append(key).append("=").append(value);
            }
            // 去掉请求字符串末尾的最后一个&号
            if (paramStringBuffer.indexOf("&", 0) == 0) {
                paramStringBuffer.deleteCharAt(0);
            }
            System.out.println("opensessionv2接口请求方法字符串:"+paramStringBuffer.toString());
            return paramStringBuffer.toString();
        }catch (Exception ex){

        }
        return null;

    }

}

