package com.hisun.lemon.cpi.acleda.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Json;

@Json
public class DirectionRsp {

    @Item
    private int result;

    @Item
    private String message;

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "DirectionRsp{" +
                "result=" + result +
                ", message='" + message + '\'' +
                '}';
    }
}
