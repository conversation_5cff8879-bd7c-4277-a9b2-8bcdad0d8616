package com.hisun.lemon.cpi;
import com.hisun.lemon.cpi.acleda.ebankpay.*;
import com.hisun.lemon.cpi.acleda.ebankpay.req.OpenSessionV2Req;
import com.hisun.lemon.cpi.acleda.ebankpay.rsp.OpenSessionV2Rsp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;


/**
 * Created by gongle<PERSON> on 2017/7/6.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
//@Ignore
public class AlipayServiceTest {

    @Resource
    private AcledaApi api;
    @Resource
    private AcledaProperties acledaProperties;

    @Test
    public void opensessionTest(){

        OpenSessionV2Req openSessionV2Req = new OpenSessionV2Req();
        //设置

        OpenSessionV2Rsp openSessionV2Rsp = api.doSend(openSessionV2Req,AcledaEnumCommon.EnumSource.openSessionV2);
        System.out.println("acleda opensession result：>>" + openSessionV2Rsp);

    }

}
