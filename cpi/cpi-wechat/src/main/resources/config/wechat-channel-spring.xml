<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:channel="http://www.hisun.com/schema/channel"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.hisun.com/schema/channel http://www.hisun.com/schema/channel/channel.xsd">
        
    <!-- <bean id="weixinSignature" class="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity"/> -->

    <channel:service name="WEIXIN" charset="UTF-8" >
        <!-- 
        <channel:connector protocol="tcp_long" address="127.0.0.1:7778" timeout="60000" waitTimeout="100000" heartbeatCheck-ref="icbcHeartbeat" poolMaxConnections="5" />
         -->
        <channel:connector id="http1" protocol="http" url="http://*************/vsapi/VspApiServlet" default="false"
        dateTypeName="FORM" charset="UTF-8" />
        <!-- http://*************/vsapi/VspApiServlet -->
        
        <!-- 页面授权  -->
        <channel:connector id="token" protocol="http" url="https://api.weixin.qq.com/sns/oauth2/access_token" default="false"
        dateTypeName="QUERY_STRING" requestMode="GET" charset="UTF-8" />
        
        <!-- 统一下单，用户扫码，聚合支付 -->
        <channel:connector id="placeorder" protocol="http" url="https://apihk.mch.weixin.qq.com/pay/unifiedorder" default="false"
        dateTypeName="STRING" charset="UTF-8" />

        <!-- 微信刷卡支付，商户扫码 -->
        <channel:connector id="mercplaceorder" protocol="http" url="https://apihk.mch.weixin.qq.com/pay/micropay" default="false"
        dateTypeName="STRING" charset="UTF-8" />
        
        <!-- 子商户申请 -->
        <channel:connector id="submerchantentry" protocol="http" url="https://api.mch.weixin.qq.com/secapi/mch/addInstitutionsub" default="false"
        dateTypeName="STRING" charset="UTF-8" trustFile="config/apiclient_cert.p12" keyStoreType="PKCS12" keyPassword="1488898692" storePassword="1488898692" hostnameVerifier="BROWSER" supportedProtocols="TLSv1"/>
        
        <!-- 子商户查询 -->
        <channel:connector id="submerchantquery" protocol="http" url="https://api.mch.weixin.qq.com/secapi/mch/queryInstitutionsub" default="false"
        dateTypeName="STRING" charset="UTF-8" trustFile="config/apiclient_cert.p12" keyStoreType="PKCS12" keyPassword="1488898692" storePassword="1488898692" hostnameVerifier="BROWSER" supportedProtocols="TLSv1"/>
        
        <!-- 订单查询 -->
        <channel:connector id="orderquery" protocol="http" url="https://apihk.mch.weixin.qq.com/pay/orderquery" default="false"
        dateTypeName="STRING" charset="UTF-8" />
        
        <!-- 订单关闭 -->
        <channel:connector id="closeorder" protocol="http" url="https://apihk.mch.weixin.qq.com/pay/closeorder" default="false"
        dateTypeName="STRING" charset="UTF-8" />
        
        <!-- 订单退款-->
        <channel:connector id="applyrefund" protocol="http" url="https://apihk.mch.weixin.qq.com/secapi/pay/refund" default="false"
        dateTypeName="STRING" charset="UTF-8" trustFile="config/apiclient_cert.p12" keyStoreType="PKCS12" keyPassword="1488898692" storePassword="1488898692" hostnameVerifier="BROWSER" supportedProtocols="TLSv1"/>

        <!-- 刷卡支付 撤销订单-->
        <channel:connector id="reverseorder" protocol="http" url="https://api.mch.weixin.qq.com/secapi/pay/reverse" default="false"
        dateTypeName="STRING" charset="UTF-8" trustFile="config/apiclient_cert.p12" keyStoreType="PKCS12" keyPassword="1488898692" storePassword="1488898692" hostnameVerifier="BROWSER" supportedProtocols="TLSv1"/>

        <!-- 退款查询 -->
        <channel:connector id="refundquery" protocol="http" url="https://apihk.mch.weixin.qq.com/pay/refundquery" default="false"
        dateTypeName="STRING" charset="UTF-8" />
        
        <!-- 下载对账文件 -->
        <channel:connector id="downloadbill" protocol="http" url="https://api.mch.weixin.qq.com/pay/downloadbill" default="false"
        dateTypeName="STRING" charset="UTF-8" />

        <!-- 查询商户结算资金 -->
        <channel:connector id="settlementquery" protocol="http" url="https://api.mch.weixin.qq.com/pay/settlementquery" default="false"
        dateTypeName="STRING" charset="UTF-8" />

        <channel:container>
           
            <!-- 离线支付 -->
            <channel:processor name="payment" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.OfflinePaymentReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.OfflinePaymentRsp" connector-id="http1" />
           
            <!-- 退款 -->
            <channel:processor name="refund" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.RefundReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.RefundRsp" connector-id="http1" />
            
            <!-- 撤销 -->
            <channel:processor name="revoke" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.RevokeReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.RevokeRsp" connector-id="http1" />
                
            <!-- 交易状态查询请求报文 -->
            <channel:processor name="tradestatusquery" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.TradeStatusQueryReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.TradeStatusQueryRsp" connector-id="http1" />

            <!-- 页面授权 -->
            <channel:processor name="Token" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.TokenReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.TokenRsp" connector-id="token" />
                
            <!-- 子商户申请 -->
            <channel:processor name="submerchantEntry" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.SubMerchantEntryReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.SubMerchantEntryRsp" connector-id="submerchantentry" />
                
            <!-- 子商户查询 -->
            <channel:processor name="submerchantQuery" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.SubMerchantQueryReq" 
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.SubMerchantQueryRsp" connector-id="submerchantquery" />
            
            <!-- 统一下单，用户扫码，聚合支付 -->
            <channel:processor name="placeOrder" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.PlaceOrderReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.PlaceOrderRsp" connector-id="placeorder" />

            <!-- 微信刷卡支付，商户扫码 -->
            <channel:processor name="mercPlaceOrder" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.MercPlaceOrderReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.MercPlaceOrderRsp" connector-id="mercplaceorder" />

            <!-- 订单查询 -->
            <channel:processor name="orderQuery" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.OrderQueryReq" 
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.OrderQueryRsp" connector-id="orderquery" />
                
            <!-- 订单关闭 -->
            <channel:processor name="closeOrder" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.CloseOrderReq" 
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.CloseOrderRsp" connector-id="closeorder" />
            
            <!-- 申请退款 -->
            <channel:processor name="applyRefund" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.ApplyRefundReq" 
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.ApplyRefundRsp" connector-id="applyrefund" />
            
            <!-- 退款查询 -->
            <channel:processor name="refundQuery" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.RefundQueryReq" 
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.RefundQueryRsp" connector-id="refundquery" />
                
            <!-- 下载对账文件-->
            <channel:processor name="downloadBill" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.DownloadBillReq" 
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.DownloadBillRsp" connector-id="downloadbill" />

            <!-- 结算资金明细查询-->
            <channel:processor name="settlementQuery" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.SettlementQueryReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.SettlementQueryRsp" connector-id="settlementquery" />

            <!-- 刷卡支付 撤销订单-->
            <channel:processor name="reverseOrder" marshal-class="com.hisun.lemon.cpi.wechat.ebankpay.req.ReverseQrderReq"
            unmarshal-class="com.hisun.lemon.cpi.wechat.ebankpay.rsp.ReverseQrderRsp" connector-id="reverseorder" />

            <!-- 此处的handler-filter对全部processor生效 -->
            <channel:handler-filter class="com.hisun.channel.service.child.filter.MonitorHandlerFilter" />
            <!-- <channel:handler-filter class="com.hisun.channel.service.child.filter.MessageContext" /> --> 

        </channel:container>
    </channel:service>
</beans>