package com.hisun.lemon.cpi.wechat.ebankpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumBusinessCategory;

/**
 * 子商户申请请求报文
 * <AUTHOR>
 *
 */
@Xml(root = "xml",removeHead=true)
public class SubMerchantEntryReq {

	/**
	 * 公众账号ID
	 */
	@Item(alias="app_id")
	@NotNull
	private String app_id;
	
	/**
	 * 商户号
	 */
	@Item(alias="mch_id")
	@NotNull
	private String mch_id;
	
	/**
	 * 签名
	 */
	@Sign(alias = "sign", xpath = "//xml",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
	@NotNull
	private String sign;
	
	/**
	 * 商户名称
	 */
	@Item(alias="merchant_name")
	@NotNull
	private String merchant_name;
	
	/**
	 * 商户简称
	 */
	@Item(alias="merchant_shortname")
	@NotNull
	private String merchant_shortname;
	
	/**
	 * 办公电话
	 */
	@Item(alias="office_phone")
	@NotNull
	private String office_phone;
	
	/**
	 * 联系人姓名
	 */
	@Item(alias="contact_name")
	private String contact_name;
	
	/**
	 * 联系人电话
	 */
	@Item(alias="contact_phone")
	private String contact_phone;
	
	/**
	 * 电子邮箱
	 */
	@Item(alias="contact_email")
	private String contact_email;
	
	/**
	 * 业务类别
	 */
	@Item(alias="business_category")
	@NotNull
	private String business_category;
	
	/**
	 * 商户备注
	 */
	@Item(alias="merchant_remark")
	@NotNull
	private String merchant_remark;
	
	/**
	 * 商户网站
	 */
	@Item(alias="website")
	private String website;
	
	/**
	 * 商户简介
	 */
	@Item(alias="merchant_introduction")
	@NotNull
	private String merchant_introduction;

	/**
	 * 公众账号ID
	 */
	public String getApp_id() {
		return app_id;
	}

	/**
	 * 公众账号ID
	 */
	public void setApp_id(String app_id) {
		this.app_id = app_id;
	}

	/**
	 * 商户号
	 */
	public String getMch_id() {
		return mch_id;
	}

	/**
	 * 商户号
	 */
	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 商户名称
	 */
	public String getMerchant_name() {
		return merchant_name;
	}

	/**
	 * 商户名称
	 */
	public void setMerchant_name(String merchant_name) {
		this.merchant_name = merchant_name;
	}

	/**
	 * 商户简称
	 */
	public String getMerchant_shortname() {
		return merchant_shortname;
	}

	/**
	 * 商户简称
	 */
	public void setMerchant_shortname(String merchant_shortname) {
		this.merchant_shortname = merchant_shortname;
	}

	/**
	 * 办公电话
	 */
	public String getOffice_phone() {
		return office_phone;
	}

	/**
	 * 办公电话
	 */
	public void setOffice_phone(String office_phone) {
		this.office_phone = office_phone;
	}

	/**
	 * 联系人姓名
	 */
	public String getContact_name() {
		return contact_name;
	}

	/**
	 * 联系人姓名
	 */
	public void setContact_name(String contact_name) {
		this.contact_name = contact_name;
	}

	/**
	 * 联系人电话
	 */
	public String getContact_phone() {
		return contact_phone;
	}

	/**
	 * 联系人电话
	 */
	public void setContact_phone(String contact_phone) {
		this.contact_phone = contact_phone;
	}

	/**
	 * 电子邮箱
	 */
	public String getContact_email() {
		return contact_email;
	}

	/**
	 * 电子邮箱
	 */
	public void setContact_email(String contact_email) {
		this.contact_email = contact_email;
	}

	/**
	 * 业务类别
	 */
	public String getBusiness_category() {
		return business_category;
	}

	/**
	 * 业务类别
	 */
	public void setBusiness_category(EnumBusinessCategory enumBusinessCategory) {
		this.business_category = enumBusinessCategory.getCode();
	}

	/**
	 * 商户备注
	 */
	public String getMerchant_remark() {
		return merchant_remark;
	}

	/**
	 * 商户备注
	 */
	public void setMerchant_remark(String merchant_remark) {
		this.merchant_remark = merchant_remark;
	}

	/**
	 * 商户网站
	 */
	public String getWebsite() {
		return website;
	}

	/**
	 * 商户网站
	 */
	public void setWebsite(String website) {
		this.website = website;
	}

	/**
	 * 商户简介
	 */
	public String getMerchant_introduction() {
		return merchant_introduction;
	}

	/**
	 * 商户简介
	 */
	public void setMerchant_introduction(String merchant_introduction) {
		this.merchant_introduction = merchant_introduction;
	}
	
}
