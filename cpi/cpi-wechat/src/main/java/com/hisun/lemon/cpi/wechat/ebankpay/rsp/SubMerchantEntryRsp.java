package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 子商户申请响应报文
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class SubMerchantEntryRsp {

	/**
	 * 返回状态码
	 */
	@Item(alias="return_code",cdata=true)
	private String return_code;
	
	/**
	 * 返回信息
	 */
	@Item(alias="return_msg",cdata=true)
	private String return_msg;
	
	/**
	 * 业务结果
	 */
	@Item(alias="result_code",cdata=true)
	private String result_code;
	
	/**
	 * 签名
	 */
	@Item(alias="sign",cdata=true)
	private String sign;
	
	/**
	 * 子商户标识
	 */
	@Item(alias="sub_mch_id",cdata=true)
	private String sub_mch_id;
	
	/**
	 * 错误码
	 */
	@Item(alias="err_code",cdata=true)
	private String err_code;
	
	/**
	 * 错误码内容
	 */
	@Item(alias="err_code_des",cdata=true)
	private String err_code_des;

	/**
	 * 返回状态码
	 */
	public String getReturn_code() {
		return return_code;
	}

	/**
	 * 返回状态码
	 */
	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	/**
	 * 返回信息
	 */
	public String getReturn_msg() {
		return return_msg;
	}

	/**
	 * 返回信息
	 */
	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	/**
	 * 业务结果
	 */
	public String getResult_code() {
		return result_code;
	}

	/**
	 * 业务结果
	 */
	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 子商户标识
	 */
	public String getSub_mch_id() {
		return sub_mch_id;
	}

	/**
	 * 子商户标识
	 */
	public void setSub_mch_id(String sub_mch_id) {
		this.sub_mch_id = sub_mch_id;
	}

	/**
	 * 错误码
	 */
	public String getErr_code() {
		return err_code;
	}

	/**
	 * 错误码
	 */
	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	/**
	 * 错误码内容
	 */
	public String getErr_code_des() {
		return err_code_des;
	}

	/**
	 * 错误码内容
	 */
	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}
}
