package com.hisun.lemon.cpi.wechat.ebankpay;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;

/**
 * 响应报文头
 * 
 * <AUTHOR>
 *
 */
@Validated
public class HeadRsp {

	/**
	 * 接口编号
	 */
	@Item(alias = "INFCODE")
	private String infCode;

	/** 接口版本 */
	@Item(alias = "VERSION")
	@NotNull
	private String version;

	/** 数据格式 */
	@Item(alias = "DATATYPE")
	private Integer dataType;

	/** 接口错误代码 */
	@Item(alias = "RETCODE")
	@NotNull
	private String retCode;

	/** 错误信息 */
	@Item(alias = "RETMSG")
	private String retMsg;

	/** 签名信息 */
	@Item(alias = "SIGNEDMSG")
	private String signeDmsg;

	/**
	 * 接口编号
	 */
	public String getInfCode() {
		return infCode;
	}

	/**
	 * 接口编号
	 */
	public void setInfCode(String infCode) {
		this.infCode = infCode;
	}

	/** 接口版本 */
	public String getVersion() {
		return version;
	}

	/** 接口版本 */
	public void setVersion(String version) {
		this.version = version;
	}

	/** 数据格式 */
	public Integer getDataType() {
		return dataType;
	}

	/** 数据格式 */
	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	/** 接口错误代码 */
	public String getRetCode() {
		return retCode;
	}

	/** 接口错误代码 */
	public void setRetCode(String retCode) {
		this.retCode = retCode;
	}

	/** 错误信息 */
	public String getRetMsg() {
		return retMsg;
	}

	/** 错误信息 */
	public void setRetMsg(String retMsg) {
		this.retMsg = retMsg;
	}

	/**
	 * 签名信息
	 */
	public String getSigneDmsg() {
		return signeDmsg;
	}

	/**
	 * 签名信息
	 */
	public void setSigneDmsg(String signeDmsg) {
		this.signeDmsg = signeDmsg;
	}

	@Override
	public String toString() {
		return "HeadRsp[infCode=" + infCode + ",version=" + version + ",dataType=" + dataType + ",retCode=" + retCode + ",retMsg=" + retMsg
				+ ",signeDmsg=" + signeDmsg + "]";

	}
}
