package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 申请退款请求报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class ApplyRefundRsp {

	/**
	 * 返回状态码
	 */
	@Item(cdata=true)
	private String return_code;

	/**
	 * 返回信息
	 */
	@Item(cdata=true)
	private String return_msg;

	/**
	 * 业务结果
	 */
	@Item(cdata=true)
	private String result_code;

	/**
	 * 错误代码
	 */
	@Item(cdata=true)
	private String err_code;

	/**
	 * 错误代码描述
	 */
	@Item(cdata=true)
	private String err_code_des;

	/**
	 * 公众账号id
	 */
	@Item(cdata=true)
	private String appid;

	/**
	 * 商户号
	 */
	@Item(cdata=true)
	private String mch_id;

	/**
	 * 随机字符串
	 */
	@Item(cdata=true)
	private String nonce_str;

	/**
	 * 签名
	 */
	@Item(cdata=true)
	private String sign;

	/**
	 * 微信订单号
	 */
	@Item(cdata=true)
	private String transaction_id;

	/**
	 * 商户订单号
	 */
	@Item(cdata=true)
	private String out_trade_no;

	/**
	 * 商户退款单号
	 */
	@Item(cdata=true)
	private String out_refund_no;

	/**
	 * 微信退款单号
	 */
	@Item(cdata=true)
	private String refund_id;

	/**
	 * 退款金额
	 */
	@Item(cdata=true)
	private int refund_fee;

	/**
	 * 退款币种
	 */
	@Item(cdata=true)
	private String refund_fee_type;

	/**
	 * 标价金额
	 */
	@Item(cdata=true)
	private int total_fee;

	/**
	 * 标价币种
	 */
	@Item(cdata=true)
	private String fee_type;

	/**
	 * 用户支付金额
	 */
	@Item(cdata=true)
	private int cash_fee;

	/**
	 * 用户支付金额币种
	 */
	@Item(cdata=true)
	private String cash_fee_type;

	/**
	 * 用户支付退款金额
	 */
	@Item(cdata=true)
	private int cash_refund_fee;

	/**
	 * 用户支付退款币种
	 */
	@Item(cdata=true)
	private String cash_refund_fee_type;

	/**
	 * 汇率
	 */
	@Item(cdata=true)
	private String rate;

	public String getReturn_code() {
		return return_code;
	}

	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	public String getReturn_msg() {
		return return_msg;
	}

	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	public String getResult_code() {
		return result_code;
	}

	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}

	public String getErr_code() {
		return err_code;
	}

	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	public String getErr_code_des() {
		return err_code_des;
	}

	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getMch_id() {
		return mch_id;
	}

	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	public String getNonce_str() {
		return nonce_str;
	}

	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getTransaction_id() {
		return transaction_id;
	}

	public void setTransaction_id(String transaction_id) {
		this.transaction_id = transaction_id;
	}

	public String getOut_trade_no() {
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	public String getOut_refund_no() {
		return out_refund_no;
	}

	public void setOut_refund_no(String out_refund_no) {
		this.out_refund_no = out_refund_no;
	}

	public String getRefund_id() {
		return refund_id;
	}

	public void setRefund_id(String refund_id) {
		this.refund_id = refund_id;
	}

	public int getRefund_fee() {
		return refund_fee;
	}

	public void setRefund_fee(int refund_fee) {
		this.refund_fee = refund_fee;
	}

	public String getRefund_fee_type() {
		return refund_fee_type;
	}

	public void setRefund_fee_type(String refund_fee_type) {
		this.refund_fee_type = refund_fee_type;
	}

	public int getTotal_fee() {
		return total_fee;
	}

	public void setTotal_fee(int total_fee) {
		this.total_fee = total_fee;
	}

	public String getFee_type() {
		return fee_type;
	}

	public void setFee_type(String fee_type) {
		this.fee_type = fee_type;
	}

	public int getCash_fee() {
		return cash_fee;
	}

	public void setCash_fee(int cash_fee) {
		this.cash_fee = cash_fee;
	}

	public String getCash_fee_type() {
		return cash_fee_type;
	}

	public void setCash_fee_type(String cash_fee_type) {
		this.cash_fee_type = cash_fee_type;
	}

	public int getCash_refund_fee() {
		return cash_refund_fee;
	}

	public void setCash_refund_fee(int cash_refund_fee) {
		this.cash_refund_fee = cash_refund_fee;
	}

	public String getCash_refund_fee_type() {
		return cash_refund_fee_type;
	}

	public void setCash_refund_fee_type(String cash_refund_fee_type) {
		this.cash_refund_fee_type = cash_refund_fee_type;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}

	@Override
	public String toString() {
		return "ApplyRefundRsp[return_code=" + return_code + ",return_msg=" + return_msg + ",result_code=" + result_code + ",err_code=" + err_code
				+ ",err_code_des=" + err_code_des + ",appid=" + appid + ",mch_id=" + mch_id + ",nonce_str=" + nonce_str + ",sign=" + sign
				+ ",transaction_id=" + transaction_id + ",out_trade_no=" + out_trade_no + ",out_refund_no=" + out_refund_no + ",refund_id="
				+ refund_id + ",refund_fee=" + refund_fee + ",refund_fee_type=" + refund_fee_type + ",total_fee=" + total_fee + ",fee_type="
				+ fee_type + ",cash_fee=" + cash_fee + ",cash_fee_type=" + cash_fee_type + ",cash_refund_fee=" + cash_refund_fee
				+ ",cash_refund_fee_type=" + cash_refund_fee_type + ",rate=" + rate + "]";
	}
}
