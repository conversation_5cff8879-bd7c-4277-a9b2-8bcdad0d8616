package com.hisun.lemon.cpi.wechat.ebankpay.req;

import com.alibaba.fastjson.JSONArray;
import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.GoodsInfo;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumCurrency;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumTradeType;

import javax.validation.constraints.NotNull;
import java.util.UUID;

/**
 * 商户扫码，微信刷卡支付
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class MercPlaceOrderReq {

	/**
	 * 公共账号ID
	 */
	@Item(alias = "appid")
	@NotNull
	private String appid;

	/**
	 * 商户号
	 */
	@Item(alias = "mch_id")
	@NotNull
	private String mch_id;

	/**
	 * 子商户编号
	 */
	@Item(alias="sub_mch_id")
	private String sub_mch_id;

	/**
	 * 设备号
	 */
	@Item(alias = "device_info")
	private String device_info;

	/**
	 * 随机字符串
	 */
	@Item(alias = "nonce_str")
	private String nonce_str;

	/**
	 * 签名
	 */
	@Sign(alias = "sign", xpath = "//xml",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
	@NotNull
	private String sign;

	/**
	 * 签名类型
	 */
	@Item(alias = "sign_type")
	private String sign_type;

	/**
	 * 商品描述
	 */
	@Item(alias = "body")
	@NotNull
	private String body;

	/**
	 * 商品详情
	 */
	@Item(alias = "detail", cdata = true)
	private String detail;

	/**
	 * 附加数据
	 */
	@Item(alias = "attach")
	private String attach;

	/**
	 * 商户订单号
	 */
	@Item(alias = "out_trade_no")
	private String out_trade_no;

	/**
	 * 标价金额
	 */
	@Item(alias = "total_fee")
	@NotNull
	private int total_fee;

	/**
	 * 标价币种
	 */
	@Item(alias = "fee_type")
	private String fee_type;

	/**
	 * 终端ip
	 */
	@Item(alias = "spbill_create_ip")
	private String spbill_create_ip;

	/**
	 * 订单优惠标记
	 */
	@Item(alias = "goods_tag")
	private String goods_tag;

	/**
	 * 授权码
	 */
	@Item(alias = "auth_code")
	private String auth_code;

	/**
	 * 场景信息
	 */
	@Item(alias = "scene_info")
	private String scene_info;

	/**
	 * 公共账号ID
	 */
	public String getAppid() {
		return appid;
	}

	/**
	 * 公共账号ID
	 */
	public void setAppid(String appid) {
		this.appid = appid;
	}

	/**
	 * 商户号
	 */
	public String getMch_id() {
		return mch_id;
	}

	/**
	 * 商户号
	 */
	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	/**
	 * 设备号
	 */
	public String getDevice_info() {
		if (StringUtils.isEmpty(device_info)) {
			device_info = "WEB";
		}
		return device_info;
	}

	/**
	 * 设备号
	 */
	public void setDevice_info(String device_info) {
		this.device_info = device_info;
	}

	/**
	 * 随机字符串
	 */
	public String getNonce_str() {
		 nonce_str=UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
		 return nonce_str;
	}

	/**
	 * 随机字符串
	 */
	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 签名类型
	 */
	public String getSign_type() {
		if (StringUtils.isEmpty(sign_type)) {
			sign_type = "MD5";
		}
		return sign_type;
	}

	/**
	 * 签名类型
	 */
	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	/**
	 * 商品描述
	 */
	public String getBody() {
		return body;
	}

	/**
	 * 商品描述
	 */
	public void setBody(String body) {
		this.body = body;
	}

	/**
	 * 商品详情
	 */
	public String getDetail() {
		return detail;
	}

	/**
	 * 商品详情
	 */
	public void setDetail(GoodsInfo goodsInfos) {
		if (goodsInfos!=null) {
			this.detail = JSONArray.toJSONString(goodsInfos);
		}
	}

	/**
	 * 附加数据
	 */
	public String getAttach() {
		return attach;
	}

	/**
	 * 附加数据
	 */
	public void setAttach(String attach) {
		this.attach = attach;
	}

	/**
	 * 商户订单号
	 */
	public String getOut_trade_no() {
		return out_trade_no;
	}

	/**
	 * 商户订单号
	 */
	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	/**
	 * 标价币种
	 */
	public String getFee_type() {
		return fee_type;
	}

	/**
	 * 标价币种
	 */
	public void setFee_type(EnumCurrency enumCurrency) {
		if (enumCurrency != null) {
			this.fee_type = enumCurrency.toString();
		}
	}

	/**
	 * 标价金额
	 */
	public int getTotal_fee() {
		return total_fee;
	}

	/**
	 * 标价金额
	 */
	public void setTotal_fee(int total_fee) {
		this.total_fee = total_fee;
	}

	/**
	 * 终端ip
	 */
	public String getSpbill_create_ip() {
		return spbill_create_ip;
	}

	/**
	 * 终端ip
	 */
	public void setSpbill_create_ip(String spbill_create_ip) {
		this.spbill_create_ip = spbill_create_ip;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	public void setFee_type(String fee_type) {
		this.fee_type = fee_type;
	}

	public String getGoods_tag() {
		return goods_tag;
	}

	public void setGoods_tag(String goods_tag) {
		this.goods_tag = goods_tag;
	}

	public String getAuth_code() {
		return auth_code;
	}

	public void setAuth_code(String auth_code) {
		this.auth_code = auth_code;
	}

	public String getScene_info() {
		return scene_info;
	}

	public void setScene_info(String scene_info) {
		this.scene_info = scene_info;
	}

	public String getSub_mch_id() {
		return sub_mch_id;
	}

	public void setSub_mch_id(String sub_mch_id) {
		this.sub_mch_id = sub_mch_id;
	}
}
