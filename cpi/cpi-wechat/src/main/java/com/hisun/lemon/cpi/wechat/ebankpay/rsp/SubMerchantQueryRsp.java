package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 子商户查询返回报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class SubMerchantQueryRsp {

	/**
	 * 返回状态码
	 */
	@Item(alias="return_code",cdata=true)
	private String return_code;

	/**
	 * 返回信息
	 */
	@Item(alias="return_msg",cdata=true)
	private String return_msg;

	/**
	 * 业务结果
	 */
	@Item(alias="result_code",cdata=true)
	private String result_code;

	/**
	 * 签名
	 */
	@Item(alias="sign",cdata=true)
	private String sign;

	/**
	 * 子商户号
	 */
	@Item(alias="sub_mch_id_0",cdata=true)
	private String sub_mch_id_0;

	/**
	 * 商户名称
	 */
	@Item(alias="merchant_name_0",cdata=true)
	private String merchant_name_0;

	/**
	 * 商户缩写
	 */
	@Item(alias="merchant_shortname_0",cdata=true)
	private String merchant_shortname_0;

	/**
	 * 办公电话
	 */
	@Item(alias="office_phone_0",cdata=true)
	private String office_phone_0;

	/**
	 * 联系人姓名
	 */
	@Item(alias="contact_name_0",cdata=true)
	private String contact_name_0;

	/**
	 * 联系人电话
	 */
	@Item(alias="contact_phone_0",cdata=true)
	private String contact_phone_0;

	/**
	 * 电子邮箱
	 */
	@Item(alias="contact_email_0",cdata=true)
	private String contact_email_0;

	/**
	 * 业务类型
	 */
	@Item(alias="business_category_0",cdata=true)
	private String business_category_0;

	/**
	 * 商户备注
	 */
	@Item(alias="merchant_remark_0",cdata=true)
	private String merchant_remark_0;

	/**
	 * 商户网页
	 */
	@Item(alias="website_0",cdata=true)
	private String website_0;

	/**
	 * 商户简介
	 */
	@Item(alias="merchant_introduction_0",cdata=true)
	private String merchant_introduction_0;

	/**
	 * 返回的子商户总数
	 */
	@Item(alias="total",cdata=true)
	private String total;

	/**
	 * 返回的错误码
	 */
	@Item(alias="err_code",cdata=true)
	private String err_code;

	/**
	 * 返回的错误内容
	 */
	@Item(alias="err_code_des",cdata=true)
	private String err_code_des;

	/**
	 * 返回状态码
	 */
	public String getReturn_code() {
		return return_code;
	}

	/**
	 * 返回状态码
	 */
	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	/**
	 * 返回信息
	 */
	public String getReturn_msg() {
		return return_msg;
	}

	/**
	 * 返回信息
	 */
	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	/**
	 * 业务结果
	 */
	public String getResult_code() {
		return result_code;
	}

	/**
	 * 业务结果
	 */
	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}
	
	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 子商户号
	 */
	public String getSub_mch_id_0() {
		return sub_mch_id_0;
	}

	/**
	 * 子商户号
	 */
	public void setSub_mch_id_0(String sub_mch_id_0) {
		this.sub_mch_id_0 = sub_mch_id_0;
	}

	/**
	 * 商户名称
	 */
	public String getMerchant_name_0() {
		return merchant_name_0;
	}

	/**
	 * 商户名称
	 */
	public void setMerchant_name_0(String merchant_name_0) {
		this.merchant_name_0 = merchant_name_0;
	}

	/**
	 * 商户缩写
	 */
	public String getMerchant_shortname_0() {
		return merchant_shortname_0;
	}

	/**
	 * 商户缩写
	 */
	public void setMerchant_shortname_0(String merchant_shortname_0) {
		this.merchant_shortname_0 = merchant_shortname_0;
	}

	/**
	 * 办公电话
	 */
	public String getOffice_phone_0() {
		return office_phone_0;
	}

	/**
	 * 办公电话
	 */
	public void setOffice_phone_0(String office_phone_0) {
		this.office_phone_0 = office_phone_0;
	}

	/**
	 * 联系人姓名
	 */
	public String getContact_name_0() {
		return contact_name_0;
	}

	/**
	 * 联系人姓名
	 */
	public void setContact_name_0(String contact_name_0) {
		this.contact_name_0 = contact_name_0;
	}

	/**
	 * 联系人电话
	 */
	public String getContact_phone_0() {
		return contact_phone_0;
	}

	/**
	 * 联系人电话
	 */
	public void setContact_phone_0(String contact_phone_0) {
		this.contact_phone_0 = contact_phone_0;
	}

	/**
	 * 电子邮箱
	 */
	public String getContact_email_0() {
		return contact_email_0;
	}

	/**
	 * 电子邮箱
	 */
	public void setContact_email_0(String contact_email_0) {
		this.contact_email_0 = contact_email_0;
	}

	/**
	 * 业务类型
	 */
	public String getBusiness_category_0() {
		return business_category_0;
	}

	/**
	 * 业务类型
	 */
	public void setBusiness_category_0(String business_category_0) {
		this.business_category_0 = business_category_0;
	}

	/**
	 * 商户备注
	 */
	public String getMerchant_remark_0() {
		return merchant_remark_0;
	}

	/**
	 * 商户备注
	 */
	public void setMerchant_remark_0(String merchant_remark_0) {
		this.merchant_remark_0 = merchant_remark_0;
	}

	/**
	 * 商户网页
	 */
	public String getWebsite_0() {
		return website_0;
	}

	/**
	 * 商户网页
	 */
	public void setWebsite_0(String website_0) {
		this.website_0 = website_0;
	}

	/**
	 * 商户简介
	 */
	public String getMerchant_introduction_0() {
		return merchant_introduction_0;
	}

	/**
	 * 商户简介
	 */
	public void setMerchant_introduction_0(String merchant_introduction_0) {
		this.merchant_introduction_0 = merchant_introduction_0;
	}

	/**
	 * 返回的子商户总数
	 */
	public String getTotal() {
		return total;
	}

	/**
	 * 返回的子商户总数
	 */
	public void setTotal(String total) {
		this.total = total;
	}

	/**
	 * 返回的错误码
	 */
	public String getErr_code() {
		return err_code;
	}

	/**
	 * 返回的错误码
	 */
	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	/**
	 * 返回的错误内容
	 */
	public String getErr_code_des() {
		return err_code_des;
	}

	/**
	 * 返回的错误内容
	 */
	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}
}
