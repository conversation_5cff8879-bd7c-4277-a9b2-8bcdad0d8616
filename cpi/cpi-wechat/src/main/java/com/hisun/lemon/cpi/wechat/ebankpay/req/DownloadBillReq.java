package com.hisun.lemon.cpi.wechat.ebankpay.req;

import java.util.UUID;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.OrderByLetter;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumBillType;

/**
 * 下载对账文件请求报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead=true)
@OrderByLetter
public class DownloadBillReq {

	/**
	 * 公众账号ID
	 */
	@Item(alias = "appid")
	private String appid;

	/**
	 * 商户号
	 */
	@Item(alias = "mch_id")
	private String mch_id;

	/**
	 * 设备号
	 */
	@Item(alias = "device_info")
	private String device_info;

	/**
	 * 随机字符串
	 */
	@Item(alias = "nonce_str")
	private String nonce_str;

	/**
	 * 签名
	 */
	@Sign(alias = "sign", xpath = "//xml",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
	private String sign;

	/**
	 * 签名类型
	 */
	@Item(alias = "sign_type")
	private String sign_type;

	/**
	 * 对账单日期
	 */
	@Item(alias = "bill_date")
	private String bill_date;

	/**
	 * 账单类型
	 */
	@Item(alias = "bill_type")
	private String bill_type;

	/**
	 * 压缩账单
	 */
	@Item(alias = "tar_type")
	private String tar_type;

	/**
	 * 子商户编号
	 */
	@Item(alias="sub_mch_id")
	private String sub_mch_id;
	
	/**
	 * 子商户编号
	 */
	public String getSub_mch_id() {
		return sub_mch_id;
	}

	/**
	 * 子商户编号
	 */
	public void setSub_mch_id(String sub_mch_id) {
		this.sub_mch_id = sub_mch_id;
	}
	
	/**
	 * 公众账号ID
	 */
	public String getAppid() {
		return appid;
	}

	/**
	 * 公众账号ID
	 */
	public void setAppid(String appid) {
		this.appid = appid;
	}

	/**
	 * 商户号
	 */
	public String getMch_id() {
		return mch_id;
	}

	/**
	 * 商户号
	 */
	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	/**
	 * 设备号
	 */
	public String getDevice_info() {
		return device_info;
	}

	/**
	 * 设备号
	 */
	public void setDevice_info(String device_info) {
		this.device_info = device_info;
	}

	/**
	 * 随机字符串
	 */
	public String getNonce_str() {
		nonce_str=UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
		return nonce_str;
	}

	/**
	 * 随机字符串
	 */
	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 签名类型
	 */
	public String getSign_type() {
		return sign_type;
	}

	/**
	 * 签名类型
	 */
	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	/**
	 * 对账单日期
	 */
	public String getBill_date() {
		return bill_date;
	}

	/**
	 * 对账单日期
	 */
	public void setBill_date(String bill_date) {
		this.bill_date = bill_date;
	}

	/**
	 * 账单类型
	 */
	public String getBill_type() {
		return bill_type;
	}

	/**
	 * 账单类型
	 */
	public void setBill_type(EnumBillType enumBillType) {
		this.bill_type = enumBillType.name();
	}

	/**
	 * 压缩账单
	 */
	public String getTar_type() {
		return tar_type;
	}

	/**
	 * 压缩账单
	 */
	public void setTar_type(String tar_type) {
		this.tar_type = tar_type;
	}

}
