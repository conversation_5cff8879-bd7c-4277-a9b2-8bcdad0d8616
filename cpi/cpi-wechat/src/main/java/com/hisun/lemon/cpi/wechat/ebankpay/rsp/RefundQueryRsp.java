package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 查询退款响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class RefundQueryRsp {

	/**
	 * 返回状态码
	 */
	@Item(cdata=true)
	private String return_code;

	/**
	 * 返回信息
	 */
	@Item(cdata=true)
	private String return_msg;

	/**
	 * 业务结果
	 */
	@Item(cdata=true)
	private String result_code;

	/**
	 * 错误码
	 */
	@Item(cdata=true)
	private String err_code;

	/**
	 * 错误描述
	 */
	@Item(cdata=true)
	private String err_code_des;

	/**
	 * 公众账号ID
	 */
	@Item(cdata=true)
	private String appid;

	/**
	 * 商户号
	 */
	@Item(cdata=true)
	private String mch_id;

	/**
	 * 随机字符串
	 */
	@Item(cdata=true)
	private String nonce_str;

	/**
	 * 签名
	 */
	@Item(cdata=true)
	private String sign;

	/**
	 * 微信订单号
	 */
	@Item(cdata=true)
	private String transaction_id;

	/**
	 * 商户订单号
	 */
	@Item(cdata=true)
	private String out_trade_no;

	/**
	 * 标价金额
	 */
	@Item(cdata=true)
	private int total_fee;

	/**
	 * 标价币种
	 */
	@Item(cdata=true)
	private String fee_type;

	/**
	 * 用户支付金额
	 */
	@Item(cdata=true)
	private int cash_fee;

	/**
	 * 用户支付金额币种
	 */
	@Item(cdata=true)
	private String cash_fee_type;

	/**
	 * 退款金额
	 */
	@Item(cdata=true)
	private int refund_fee_0;

	/**
	 * 退款笔数
	 */
	@Item(cdata=true)
	private int refund_count;

	/**
	 * 商户退款单号
	 */
	@Item(cdata=true)
	private String out_refund_no_0;

	/**
	 * 微信退款单号
	 */
	@Item(cdata=true)
	private String refund_id_0;

	/**
	 * 退款渠道
	 */
	@Item(cdata=true)
	private String refund_channel_0;

	/**
	 * 退款状态
	 */
	@Item(cdata=true)
	private String refund_status_0;

	/**
	 * 退款资金来源
	 */
	@Item(cdata=true)
	private String refund_account_0;

	/**
	 * 退款入账账户
	 */
	@Item(cdata=true)
	private String refund_recv_accout_0;

	/**
	 * 退款成功时间
	 */
	@Item(cdata=true)
	private String refund_success_time_0;

	/**
	 * 汇率
	 */
	@Item(cdata=true)
	private String rate;

	public String getReturn_code() {
		return return_code;
	}

	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	public String getReturn_msg() {
		return return_msg;
	}

	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	public String getResult_code() {
		return result_code;
	}

	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}

	public String getErr_code() {
		return err_code;
	}

	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	public String getErr_code_des() {
		return err_code_des;
	}

	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getMch_id() {
		return mch_id;
	}

	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	public String getNonce_str() {
		return nonce_str;
	}

	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getTransaction_id() {
		return transaction_id;
	}

	public void setTransaction_id(String transaction_id) {
		this.transaction_id = transaction_id;
	}

	public String getOut_trade_no() {
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	public int getTotal_fee() {
		return total_fee;
	}

	public void setTotal_fee(int total_fee) {
		this.total_fee = total_fee;
	}

	public String getFee_type() {
		return fee_type;
	}

	public void setFee_type(String fee_type) {
		this.fee_type = fee_type;
	}

	public int getCash_fee() {
		return cash_fee;
	}

	public void setCash_fee(int cash_fee) {
		this.cash_fee = cash_fee;
	}

	public String getCash_fee_type() {
		return cash_fee_type;
	}

	public void setCash_fee_type(String cash_fee_type) {
		this.cash_fee_type = cash_fee_type;
	}

	public int getRefund_fee_0() {
		return refund_fee_0;
	}

	public void setRefund_fee_0(int refund_fee_0) {
		this.refund_fee_0 = refund_fee_0;
	}

	public int getRefund_count() {
		return refund_count;
	}

	public void setRefund_count(int refund_count) {
		this.refund_count = refund_count;
	}

	public String getOut_refund_no_0() {
		return out_refund_no_0;
	}

	public void setOut_refund_no_0(String out_refund_no_0) {
		this.out_refund_no_0 = out_refund_no_0;
	}

	public String getRefund_id_0() {
		return refund_id_0;
	}

	public void setRefund_id_0(String refund_id_0) {
		this.refund_id_0 = refund_id_0;
	}

	public String getRefund_channel_0() {
		return refund_channel_0;
	}

	public void setRefund_channel_0(String refund_channel_0) {
		this.refund_channel_0 = refund_channel_0;
	}

	public String getRefund_status_0() {
		return refund_status_0;
	}

	public void setRefund_status_0(String refund_status_0) {
		this.refund_status_0 = refund_status_0;
	}

	public String getRefund_account_0() {
		return refund_account_0;
	}

	public void setRefund_account_0(String refund_account_0) {
		this.refund_account_0 = refund_account_0;
	}

	public String getRefund_recv_accout_0() {
		return refund_recv_accout_0;
	}

	public void setRefund_recv_accout_0(String refund_recv_accout_0) {
		this.refund_recv_accout_0 = refund_recv_accout_0;
	}

	public String getRefund_success_time_0() {
		return refund_success_time_0;
	}

	public void setRefund_success_time_0(String refund_success_time_0) {
		this.refund_success_time_0 = refund_success_time_0;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}

	@Override
	public String toString() {
		return "RefundQueryRsp[return_code=" + return_code + ",return_msg=" + return_msg + ",result_code=" + result_code + ",err_code=" + err_code
				+ ",err_code_des=" + err_code_des + ",appid=" + appid + ",mch_id=" + mch_id + ",nonce_str=" + nonce_str + ",sign=" + sign
				+ ",transaction_id=" + transaction_id + ",out_trade_no=" + out_trade_no + ",total_fee=" + total_fee + ",fee_type=" + fee_type
				+ ",cash_fee=" + cash_fee + ",cash_fee_type=" + cash_fee_type + ",refund_fee_0=" + refund_fee_0 + ",refund_count=" + refund_count
				+ ",out_refund_no_0=" + out_refund_no_0 + ",refund_id_0=" + refund_id_0 + ",refund_channel_0=" + refund_channel_0
				+ ",refund_status_0=" + refund_status_0 + ",refund_account_0=" + refund_account_0 + ",refund_recv_accout_0="
				+ refund_recv_accout_0 + ",refund_success_time_0=" + refund_success_time_0 + ",rate=" + rate + "]";
	}
}
