package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 用户扫码，聚合支付，统一下单响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class PlaceOrderRsp {

	/**
	 * 返回状态吗
	 */
	@Item(alias = "return_code",cdata=true)
	private String return_code;

	/**
	 * 返回信息
	 */
	@Item(alias = "return_msg",cdata=true)
	private String return_msg;

	/**
	 * 公共账号id
	 */
	@Item(alias = "appid",cdata=true)
	private String appid;

	/**
	 * 商户号
	 */
	@Item(alias = "mch_id",cdata=true)
	private String mch_id;

	/**
	 * 设备号
	 */
	@Item(alias = "device_info",cdata=true)
	private String device_info;

	/**
	 * 随机字符串
	 */
	@Item(alias = "nonce_str",cdata=true)
	private String nonce_str;

	/**
	 * 签名
	 */
	@Item(alias = "sign",cdata=true)
	private String sign;

	/**
	 * 业务结果
	 */
	@Item(alias = "result_code",cdata=true)
	private String result_code;

	/**
	 * 错误代码
	 */
	@Item(alias = "err_code",cdata=true)
	private String err_code;

	/**
	 * 错误代码描述
	 */
	@Item(alias = "err_code_des",cdata=true)
	private String err_code_des;

	/**
	 * 交易类型
	 */
	@Item(alias = "trade_type",cdata=true)
	private String trade_type;

	/**
	 * 预支付交易会话标识
	 */
	@Item(alias = "prepay_id",cdata=true)
	private String prepay_id;

	/**
	 * 二维码链接
	 */
	@Item(alias = "code_url",cdata=true)
	private String code_url;

	/**
	 * 返回状态吗
	 */
	public String getReturn_code() {
		return return_code;
	}

	/**
	 * 返回状态吗
	 */
	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	/**
	 * 返回信息
	 */
	public String getReturn_msg() {
		return return_msg;
	}

	/**
	 * 返回信息
	 */
	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	/**
	 * 公共账号id
	 */
	public String getAppid() {
		return appid;
	}

	/**
	 * 公共账号id
	 */
	public void setAppid(String appid) {
		this.appid = appid;
	}

	/**
	 * 商户号
	 */
	public String getMch_id() {
		return mch_id;
	}

	/**
	 * 商户号
	 */
	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	/**
	 * 设备号
	 */
	public String getDevice_info() {
		return device_info;
	}

	/**
	 * 设备号
	 */
	public void setDevice_info(String device_info) {
		this.device_info = device_info;
	}

	/**
	 * 随机字符串
	 */
	public String getNonce_str() {
		return nonce_str;
	}

	/**
	 * 随机字符串
	 */
	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 业务结果
	 */
	public String getResult_code() {
		return result_code;
	}

	/**
	 * 业务结果
	 */
	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}

	/**
	 * 错误代码
	 */
	public String getErr_code() {
		return err_code;
	}

	/**
	 * 错误代码
	 */
	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	/**
	 * 错误代码描述
	 */
	public String getErr_code_des() {
		return err_code_des;
	}

	/**
	 * 错误代码描述
	 */
	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}

	/**
	 * 交易类型
	 */
	public String getTrade_type() {
		return trade_type;
	}

	/**
	 * 交易类型
	 */
	public void setTrade_type(String trade_type) {
		this.trade_type = trade_type;
	}

	/**
	 * 预支付交易会话标识
	 */
	public String getPrepay_id() {
		return prepay_id;
	}

	/**
	 * 预支付交易会话标识
	 */
	public void setPrepay_id(String prepay_id) {
		this.prepay_id = prepay_id;
	}

	/**
	 * 二维码链接
	 */
	public String getCode_url() {
		return code_url;
	}

	/**
	 * 二维码链接
	 */
	public void setCode_url(String code_url) {
		this.code_url = code_url;
	}

	@Override
	public String toString() {
		return "PlaceOrderRsp[return_code=" + return_code + ",return_msg=" + return_msg + ",appid=" + appid + ",mch_id=" + mch_id + ",device_info="
				+ device_info + ",nonce_str=" + nonce_str + ",sign=" + sign + ",result_code=" + result_code + ",err_code=" + err_code
				+ ",err_code_des=" + err_code_des + ",trade_type=" + trade_type + ",prepay_id=" + prepay_id + ",code_url=" + code_url + "]";
	}
}
