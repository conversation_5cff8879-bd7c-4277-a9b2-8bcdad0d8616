package com.hisun.lemon.cpi.wechat.ebankpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.HeadReq;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumCurrency;


/**
 * 离线支付
 * 
 * <AUTHOR>
 *
 */
@Plain
@Xml(root = "VSPAPI",removeLineSeparator=true)
@SignValue
public class OfflinePaymentReq {
	
	@Nest(alias = "HEAD")
	private HeadReq headReq;

	@Sign(alias = "SIGNEDMSG", xpath = "//VSPAPI/HEAD",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WEIXINSecurity")
	private String signeDmsg;

	@Nest(alias = "BODY")
	private OfflinePaymentTranReq offlinePaymentTranReq;

	@Validated
	public static class OfflinePaymentTranReq {

		/**
		 * 交易金额
		 */
		@Item(alias = "TRXAMT")
		@NotNull
		private int trxamt;

		/** 交易流水号 */
		@Item(alias = "REQSN")
		@NotNull
		private String reqsn;

		/** 交易商户号 */
		@Item(alias = "CUSID")
		@NotNull
		private String cusId;

		/** 商品名称 */
		@Item(alias = "BODY")
		private String body;

		/**
		 * 二维码字符串
		 */
		@Item(alias = "AUTHCODE")
		@NotNull
		private String authCode;

		/**
		 * 币种
		 */
		@Item(alias = "CURRENCY")
		private String currency;

		/**
		 * 交易金额
		 */
		public int getTrxamt() {
			return trxamt;
		}

		/**
		 * 交易金额
		 */
		public void setTrxamt(int trxamt) {
			this.trxamt = trxamt;
		}

		/** 交易流水号 */
		public String getReqsn() {
			return reqsn;
		}

		/** 交易流水号 */
		public void setReqsn(String reqsn) {
			this.reqsn = reqsn;
		}

		/** 交易商户号 */
		public String getCusId() {
			return cusId;
		}

		/** 交易商户号 */
		public void setCusId(String cusId) {
			this.cusId = cusId;
		}

		/** 商品名称 */
		public String getBody() {
			return body;
		}

		/** 商品名称 */
		public void setBody(String body) {
			this.body = body;
		}

		/**
		 * 二维码字符串
		 */
		public String getAuthCode() {
			return authCode;
		}

		/**
		 * 二维码字符串
		 */
		public void setAuthCode(String authCode) {
			this.authCode = authCode;
		}

		/**
		 * 币种
		 */
		public String getCurrency() {
			return currency;
		}

		/**
		 * 币种
		 */
		public void setCurrency(EnumCurrency enumCurrency) {
			this.currency = enumCurrency.toString();
		}
	}

	/**
	 * 签名信息
	 */
	public String getSigneDmsg() {
		return signeDmsg;
	}

	/**
	 * 签名信息
	 */
	public void setSigneDmsg(String signeDmsg) {
		this.signeDmsg = signeDmsg;
	}

	public HeadReq getHeadReq() {
		return headReq;
	}

	public void setHeadReq(HeadReq headReq) {
		this.headReq = headReq;
	}

	public OfflinePaymentTranReq getOfflinePaymentTranReq() {
		return offlinePaymentTranReq;
	}

	public void setOfflinePaymentTranReq(OfflinePaymentTranReq offlinePaymentTranReq) {
		this.offlinePaymentTranReq = offlinePaymentTranReq;
	}
}
