package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 订单查询响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class OrderQueryRsp {

	/**
	 * 返回状态码
	 */
	@Item(cdata=true)
	private String return_code;

	/**
	 * 返回信息
	 */
	@Item(cdata=true)
	private String return_msg;

	/**
	 * 公共账号id
	 */
	@Item(cdata=true)
	private String appid;

	/**
	 * 商户号
	 */
	@Item(cdata=true)
	private String mch_id;

	/**
	 * 随机字符串
	 */
	@Item(cdata=true)
	private String nonce_str;

	/**
	 * 签名
	 */
	@Item(cdata=true)
	private String sign;

	/**
	 * 业务结果
	 */
	@Item(cdata=true)
	private String result_code;

	/**
	 * 错误代码
	 */
	@Item(cdata=true)
	private String err_code;

	/**
	 * 错误代码描述
	 */
	@Item(cdata=true)
	private String err_code_des;

	/**
	 * 设备号
	 */
	@Item(cdata=true)
	private String device_info;

	/**
	 * 用户标识
	 */
	@Item(cdata=true)
	private String openid;

	/**
	 * 是否关注公众账号
	 */
	@Item(cdata=true)
	private String is_subscribe;

	/**
	 * 交易类型
	 */
	@Item(cdata=true)
	private String trade_type;

	/**
	 * 交易状态
	 */
	@Item(cdata=true)
	private String trade_state;

	/**
	 * 付款银行
	 */
	@Item(cdata=true)
	private String bank_type;

	/**
	 * 标价金额
	 */
	@Item(cdata=true)
	private int total_fee;

	/**
	 * 标价币种
	 */
	@Item(cdata=true)
	private String fee_type;

	/**
	 * 用户支付金额
	 */
	@Item(cdata=true)
	private int cash_fee;

	/**
	 * 用户支付币种
	 */
	@Item(cdata=true)
	private String cash_fee_type;

	/**
	 * 微信支付订单号
	 */
	@Item(cdata=true)
	private String transaction_id;

	/**
	 * 商户订单号
	 */
	@Item(cdata=true)
	private String out_trade_no;

	/**
	 * 附加数据
	 */
	@Item(cdata=true)
	private String attach;

	/**
	 * 支付完成时间
	 */
	@Item(cdata=true)
	private String time_end;

	/**
	 * 汇率
	 */
	@Item(cdata=true)
	private String rate;

	public String getReturn_code() {
		return return_code;
	}

	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	public String getReturn_msg() {
		return return_msg;
	}

	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getMch_id() {
		return mch_id;
	}

	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	public String getNonce_str() {
		return nonce_str;
	}

	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getResult_code() {
		return result_code;
	}

	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}

	public String getErr_code() {
		return err_code;
	}

	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	public String getErr_code_des() {
		return err_code_des;
	}

	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}

	public String getDevice_info() {
		return device_info;
	}

	public void setDevice_info(String device_info) {
		this.device_info = device_info;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public String getIs_subscribe() {
		return is_subscribe;
	}

	public void setIs_subscribe(String is_subscribe) {
		this.is_subscribe = is_subscribe;
	}

	public String getTrade_type() {
		return trade_type;
	}

	public void setTrade_type(String trade_type) {
		this.trade_type = trade_type;
	}

	public String getTrade_state() {
		return trade_state;
	}

	public void setTrade_state(String trade_state) {
		this.trade_state = trade_state;
	}

	public String getBank_type() {
		return bank_type;
	}

	public void setBank_type(String bank_type) {
		this.bank_type = bank_type;
	}

	public int getTotal_fee() {
		return total_fee;
	}

	public void setTotal_fee(int total_fee) {
		this.total_fee = total_fee;
	}

	public String getFee_type() {
		return fee_type;
	}

	public void setFee_type(String fee_type) {
		this.fee_type = fee_type;
	}

	public int getCash_fee() {
		return cash_fee;
	}

	public void setCash_fee(int cash_fee) {
		this.cash_fee = cash_fee;
	}

	public String getCash_fee_type() {
		return cash_fee_type;
	}

	public void setCash_fee_type(String cash_fee_type) {
		this.cash_fee_type = cash_fee_type;
	}

	public String getTransaction_id() {
		return transaction_id;
	}

	public void setTransaction_id(String transaction_id) {
		this.transaction_id = transaction_id;
	}

	public String getOut_trade_no() {
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	public String getAttach() {
		return attach;
	}

	public void setAttach(String attach) {
		this.attach = attach;
	}

	public String getTime_end() {
		return time_end;
	}

	public void setTime_end(String time_end) {
		this.time_end = time_end;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}

	@Override
	public String toString() {
		return "OrderQueryRsp[return_code=" + return_code + ",return_msg =" + return_msg + ",appid =" + appid + ",mch_id=" + mch_id + ",nonce_str ="
				+ nonce_str + ",sign=" + sign + ",result_code=" + result_code + ",err_code=" + err_code + ",err_code_des =" + err_code_des
				+ ",device_info=" + device_info + ",openid =" + openid + ",is_subscribe =" + is_subscribe + ",trade_type =" + trade_type
				+ ",trade_state =" + trade_state + ",bank_type =" + bank_type + ",total_fee=" + total_fee + ",fee_type=" + fee_type + ",cash_fee="
				+ cash_fee + ",cash_fee_type=" + cash_fee_type + ",transaction_id =" + transaction_id + ",out_trade_no =" + out_trade_no + ",attach="
				+ attach + ",time_end =" + time_end + ",rate=" + rate + "]";
	}
}
