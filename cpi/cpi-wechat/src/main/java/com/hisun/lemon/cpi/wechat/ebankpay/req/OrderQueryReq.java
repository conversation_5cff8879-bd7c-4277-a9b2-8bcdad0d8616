package com.hisun.lemon.cpi.wechat.ebankpay.req;

import java.util.UUID;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 订单查询请求报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead=true)
public class OrderQueryReq {

	/**
	 * 公众账号id
	 */
	@Item(alias="appid")
	@NotNull
	private String appid;

	/**
	 * 商户号
	 */
	@Item(alias="mch_id")
	@NotNull
	private String mch_id;
	
	/**
	 * 微信订单号
	 */
	@Item
	private String transaction_id;
	
	/**
	 * 商户订单号
	 */
	@Item(alias="out_trade_no")
	private String out_trade_no ;
	
	/**
	 * 随机字符串
	 */
	@Item(alias="nonce_str")
	private String nonce_str ;
	
	/**
	 * 签名
	 */
	@Sign(alias = "sign", xpath = "//xml",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
	@NotNull
	private String sign;
	
	/**
	 * 签名类型
	 */
	@Item(alias="sign_type")
	private String sign_type;

	/**
	 * 子商户编号
	 */
	@Item(alias="sub_mch_id")
	private String sub_mch_id;
	
	public String getSub_mch_id() {
		return sub_mch_id;
	}

	public void setSub_mch_id(String sub_mch_id) {
		this.sub_mch_id = sub_mch_id;
	}

	/**
	 * 公众账号id
	 */
	public String getAppid() {
		return appid;
	}

	/**
	 * 公众账号id
	 */
	public void setAppid(String appid) {
		this.appid = appid;
	}

	/**
	 * 商户号
	 */
	public String getMch_id() {
		return mch_id;
	}

	/**
	 * 商户号
	 */
	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	/**
	 * 微信订单号
	 */
	public String getTransaction_id() {
		return transaction_id;
	}

	/**
	 * 微信订单号
	 */
	public void setTransaction_id(String transaction_id) {
		this.transaction_id = transaction_id;
	}

	/**
	 * 商户订单号
	 */
	public String getOut_trade_no() {
		return out_trade_no;
	}
	
	/**
	 * 商户订单号
	 */
	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	/**
	 * 随机字符串
	 */
	public String getNonce_str() {
		nonce_str=UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
		return nonce_str;
	}

	/**
	 * 随机字符串
	 */
	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 签名类型
	 */
	public String getSign_type() {
		if(StringUtils.isEmpty(sign_type))
		{
			sign_type="MD5";
		}
		return sign_type;
	}

	/**
	 * 签名类型
	 */
	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}
}
