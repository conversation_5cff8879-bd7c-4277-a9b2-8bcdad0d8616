package com.hisun.lemon.cpi.wechat.ebankpay;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumInfCode;


/**
 * 请求报文头
 * 
 * <AUTHOR>
 *
 */
@Validated
public class HeadReq {

	/**
	 * 接口编号
	 */
	@Item(alias = "INFCODE")
	@NotNull
	private String infCode;

	/**
	 * 接口版本
	 */
	@Item(alias = "VERSION")
	@NotNull
	private String version;

	/**
	 * 数据格式
	 */
	@Item(alias = "DATATYPE")
	private String dataType;

	/**
	 * 接入商户号
	 */
	@Item(alias = "AESCUSID")
	@NotNull
	private String aescusId;

	/**
	 * 接入APPID
	 */
	@Item(alias = "AESAPPID")
	private String aesappId;

	/**
	 * 报文上送途径
	 */
	@Item(alias = "RCVCHNL")
	@NotNull
	private String rcvchnl;
	
	/**
	 * 接口编号
	 */
	public String getInfCode() {
		return infCode;
	}

	/**
	 * 接口编号
	 */
	public void setInfCode(EnumInfCode enumInfCode) {
		this.infCode = enumInfCode.getCode();
	}

	/**
	 * 接口版本
	 */
	public String getVersion() {
		if(StringUtils.isEmpty(version)){
			version= "01";
		}
		return version;
	}

	/**
	 * 接口版本
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * 数据格式
	 */
	public String getDataType() {
		if(StringUtils.isEmpty(dataType)){
			dataType="1";
		}
		return dataType;
	}

	/**
	 * 数据格式
	 */
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	/**
	 * 接入商户号
	 */
	public String getAescusId() {
		return aescusId;
	}

	/**
	 * 接入商户号
	 */
	public void setAescusId(String aescusId) {
		this.aescusId = aescusId;
	}

	/**
	 * 接入APPID
	 */
	public String getAesappId() {
		return aesappId;
	}

	/**
	 * 接入APPID
	 */
	public void setAesappId(String aesappId) {
		this.aesappId = aesappId;
	}

	/**
	 * 报文上送途径
	 */
	public String getRcvchnl() {
		if(StringUtils.isEmpty(rcvchnl)){
			rcvchnl="00020001";
		}
		return rcvchnl;
	}

	/**
	 * 报文上送途径
	 */
	public void setRcvchnl(String rcvchnl) {
		this.rcvchnl = rcvchnl;
	}

//	/**
//	 * 签名信息
//	 */
//	public String getSigneDmsg() {
//		return signeDmsg;
//	}
//
//	/**
//	 * 签名信息
//	 */
//	public void setSigneDmsg(String signeDmsg) {
//		this.signeDmsg = signeDmsg;
//	}
	
}
