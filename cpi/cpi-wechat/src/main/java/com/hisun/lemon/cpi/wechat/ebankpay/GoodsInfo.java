package com.hisun.lemon.cpi.wechat.ebankpay;

import java.util.List;

/**
 * 商品信息
 * <AUTHOR>
 *
 */
public class GoodsInfo {

	private List<Goods_Detail> goods_detail;
	
	public List<Goods_Detail> getGoods_detail() {
		return goods_detail;
	}

	public void setGoods_detail(List<Goods_Detail> goods_detail) {
		this.goods_detail = goods_detail;
	}

	public static class Goods_Detail {
		/**
		 * 商品编号
		 */
		private String goods_id;

		/**
		 * 微信支付定义的统一商品编号
		 */
		private String wxpay_goods_id;

		/**
		 * 商品名称
		 */
		private String goods_name;

		/**
		 * 商品数量
		 */
		private int quantity;

		/**
		 * 商品单价，单位为分
		 */
		private int price;

		/**
		 * 商品编号
		 */
		public String getGoods_id() {
			return goods_id;
		}

		/**
		 * 商品编号
		 */
		public void setGoods_id(String goods_id) {
			this.goods_id = goods_id;
		}

		/**
		 * 微信支付定义的统一商品编号
		 */
		public String getWxpay_goods_id() {
			return wxpay_goods_id;
		}

		/**
		 * 微信支付定义的统一商品编号
		 */
		public void setWxpay_goods_id(String wxpay_goods_id) {
			this.wxpay_goods_id = wxpay_goods_id;
		}

		/**
		 * 商品名称
		 */
		public String getGoods_name() {
			return goods_name;
		}

		/**
		 * 商品名称
		 */
		public void setGoods_name(String goods_name) {
			this.goods_name = goods_name;
		}

		/**
		 * 商品数量
		 */
		public int getQuantity() {
			return quantity;
		}

		/**
		 * 商品数量
		 */
		public void setQuantity(int quantity) {
			this.quantity = quantity;
		}

		/**
		 * 商品单价，单位为分
		 */
		public int getPrice() {
			return price;
		}

		/**
		 * 商品单价，单位为分
		 */
		public void setPrice(int price) {
			this.price = price;
		}
	}

}
