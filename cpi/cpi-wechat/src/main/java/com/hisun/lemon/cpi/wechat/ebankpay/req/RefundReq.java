package com.hisun.lemon.cpi.wechat.ebankpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.HeadReq;


/**
 * 退款
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "VSPAPI",removeLineSeparator=true)
@SignValue
public class RefundReq {

	@Nest(alias = "HEAD")
	private HeadReq headReq;

	@Sign(alias = "SIGNEDMSG", xpath = "//VSPAPI/HEAD",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WEIXINSecurity")
	private String signeDmsg;

	@Nest(alias = "BODY")
	private RefundTranReq refundTranReq;

	@Validated
	public static class RefundTranReq {

		/**
		 * 原交易单号
		 */
		@Item(alias = "OLDTRXID")
		@NotNull
		private String oldtrxId;

		/**
		 * 交易商户号
		 */
		@Item(alias = "CUSID")
		@NotNull
		private String cusId;

		/**
		 * 原交易单号
		 */
		public String getOldtrxId() {
			return oldtrxId;
		}

		/**
		 * 原交易单号
		 */
		public void setOldtrxId(String oldtrxId) {
			this.oldtrxId = oldtrxId;
		}

		/**
		 * 交易商户号
		 */
		public String getCusId() {
			return cusId;
		}

		/**
		 * 交易商户号
		 */
		public void setCusId(String cusId) {
			this.cusId = cusId;
		}

	}

	/**
	 * 签名信息
	 */
	public String getSigneDmsg() {
		return signeDmsg;
	}

	/**
	 * 签名信息
	 */
	public void setSigneDmsg(String signeDmsg) {
		this.signeDmsg = signeDmsg;
	}

	public HeadReq getHeadReq() {
		return headReq;
	}

	public void setHeadReq(HeadReq headReq) {
		this.headReq = headReq;
	}

	public RefundTranReq getRefundTranReq() {
		return refundTranReq;
	}

	public void setRefundTranReq(RefundTranReq refundTranReq) {
		this.refundTranReq = refundTranReq;
	}
}
