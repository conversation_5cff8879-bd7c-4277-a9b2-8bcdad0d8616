package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Plain;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 结算资金查询返回结果
 */
@Plain
public class SettlementQueryRsp {

    /**
     * 返回信息
     */
    @Item
    private String return_data;

    @Verify(signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
    private String return_sign;

    /**
     * 结算明细信息对象
     */
    public static class WeChatStlDetail {
        /**
         * 以下字段在return_code 和result_code都为SUCCESS的时候有返回 ，
         * 如有多条记录，则以下数据会重复多组
         * 付汇批次号
         */
        @Item(alias = "fbatchno",cdata=true)
        private String fbatchno;

        /**
         * 结算日期
         */
        @Item(alias = "date_settlement",cdata=true)
        private String date_settlement;

        /**
         * 交易开始日期
         */
        @Item(alias = "date_start",cdata=true)
        private String date_start;

        /**
         * 交易结束日期
         */
        @Item(alias = "date_end",cdata=true)
        private String date_end;

        /**
         * 划账金额
         */
        @Item(alias = "settlement_fee",cdata=true)
        private String settlement_fee;

        /**
         * 未划账金额
         */
        @Item(alias = "unsettlement_fee",cdata=true)
        private String unsettlement_fee;

        /**
         * 结算币种
         */
        @Item(alias = "settlementfee_type",cdata=true)
        private String settlementfee_type;

        /**
         * 支付金额
         */
        @Item(alias = "pay_fee",cdata=true)
        private String pay_fee;

        /**
         * 退款金额
         */
        @Item(alias = "refund_fee",cdata=true)
        private String refund_fee;

        /**
         * 支付净额
         */
        @Item(alias = "pay_net_fee",cdata=true)
        private String pay_net_fee;

        /**
         * 手续费金额
         */
        @Item(alias = "poundage_fee",cdata=true)
        private String poundage_fee;

        public String getFbatchno() {
            return fbatchno;
        }

        public void setFbatchno(String fbatchno) {
            this.fbatchno = fbatchno;
        }

        public String getDate_settlement() {
            return date_settlement;
        }

        public void setDate_settlement(String date_settlement) {
            this.date_settlement = date_settlement;
        }

        public String getDate_start() {
            return date_start;
        }

        public void setDate_start(String date_start) {
            this.date_start = date_start;
        }

        public String getDate_end() {
            return date_end;
        }

        public void setDate_end(String date_end) {
            this.date_end = date_end;
        }

        public String getSettlement_fee() {
            return settlement_fee;
        }

        public void setSettlement_fee(String settlement_fee) {
            this.settlement_fee = settlement_fee;
        }

        public String getUnsettlement_fee() {
            return unsettlement_fee;
        }

        public void setUnsettlement_fee(String unsettlement_fee) {
            this.unsettlement_fee = unsettlement_fee;
        }

        public String getSettlementfee_type() {
            return settlementfee_type;
        }

        public void setSettlementfee_type(String settlementfee_type) {
            this.settlementfee_type = settlementfee_type;
        }

        public String getPay_fee() {
            return pay_fee;
        }

        public void setPay_fee(String pay_fee) {
            this.pay_fee = pay_fee;
        }

        public String getRefund_fee() {
            return refund_fee;
        }

        public void setRefund_fee(String refund_fee) {
            this.refund_fee = refund_fee;
        }

        public String getPay_net_fee() {
            return pay_net_fee;
        }

        public void setPay_net_fee(String pay_net_fee) {
            this.pay_net_fee = pay_net_fee;
        }

        public String getPoundage_fee() {
            return poundage_fee;
        }

        public void setPoundage_fee(String poundage_fee) {
            this.poundage_fee = poundage_fee;
        }
    }

    public String getReturn_data() {
        return return_data;
    }

    public void setReturn_data(String return_data) {
        this.return_data = return_data;
    }

    public String getReturn_sign() {
        return return_sign;
    }

    public void setReturn_sign(String return_sign) {
        this.return_sign = return_sign;
    }
}
