package com.hisun.lemon.cpi.wechat.ebankpay;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * User : Rui
 * Date : 2017/8/29
 * Time : 11:24
 **/
@Component
@ConfigurationProperties(prefix = "weChat")
@PropertySource("classpath:config/ebankpay-wechat.properties")
public class WeChatProperties {

    private String aesappId;

    private String aescusId;
    
    private String appId;
    
    private String secret;

    private String weiXinAppKey;

    private String notifyUrl;

    private String platMercId;

    private String paySignKey;

    private String platMercPayId;

	public String getPlatMercPayId() {
		return platMercPayId;
	}

	public void setPlatMercPayId(String platMercPayId) {
		this.platMercPayId = platMercPayId;
	}

	public String getPaySignKey() {
		return paySignKey;
	}

	public void setPaySignKey(String paySignKey) {
		this.paySignKey = paySignKey;
	}

	public String getWeiXinAppKey() {
		return weiXinAppKey;
	}

	public void setWeiXinAppKey(String weiXinAppKey) {
		this.weiXinAppKey = weiXinAppKey;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getSecret() {
		return secret;
	}

	public void setSecret(String secret) {
		this.secret = secret;
	}

	public String getAesappId() {
        return aesappId;
    }

    public void setAesappId(String aesappId) {
        this.aesappId = aesappId;
    }

    public String getAescusId() {
        return aescusId;
    }

    public void setAescusId(String aescusId) {
        this.aescusId = aescusId;
    }

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getPlatMercId() {
		return platMercId;
	}

	public void setPlatMercId(String platMercId) {
		this.platMercId = platMercId;
	}
}
