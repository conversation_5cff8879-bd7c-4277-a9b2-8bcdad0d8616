package com.hisun.lemon.cpi.wechat.ebankpay.req;

import javax.validation.constraints.NotNull;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 子商户查询请求报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml",removeHead=true)
public class SubMerchantQueryReq {

	/**
	 * 公众账号ID
	 */
	@Item(alias="app_id")
	@NotNull
	private String app_id;

	/**
	 * 商户号
	 */
	@Item(alias="mch_id")
	@NotNull
	private String mch_id;

	/**
	 * 签名
	 */
	@Sign(alias = "sign", xpath = "//xml",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
	@NotNull
	private String sign;

	/**
	 * 子商户号
	 */
	@Item(alias="sub_mch_id")
	@NotNull
	private String sub_mch_id;

	/**
	 * 公众账号ID
	 */
	public String getApp_id() {
		return app_id;
	}

	/**
	 * 公众账号ID
	 */
	public void setApp_id(String app_id) {
		this.app_id = app_id;
	}

	/**
	 * 商户号
	 */
	public String getMch_id() {
		return mch_id;
	}

	/**
	 * 商户号
	 */
	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 子商户号
	 */
	public String getSub_mch_id() {
		return sub_mch_id;
	}

	/**
	 * 子商户号
	 */
	public void setSub_mch_id(String sub_mch_id) {
		this.sub_mch_id = sub_mch_id;
	}
}
