package com.hisun.lemon.cpi.wechat.ebankpay.req;

import com.hisun.channel.common.validation.Validated;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.SignValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.HeadReq;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumTradeType;

/**
 * 交易状态查询请求报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "VSPAPI", removeLineSeparator = true)
@SignValue
public class TradeStatusQueryReq {
	@Nest(alias = "HEAD")
	private HeadReq headReq;

	@Sign(alias = "SIGNEDMSG", xpath = "//VSPAPI/HEAD",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WEIXINSecurity")
	private String signeDmsg;

	@Nest(alias = "BODY")
	private TradeStatusQueryTranReq tradeStatusQueryTranReq;

	@Validated
	public static class TradeStatusQueryTranReq {

		/**
		 * 支付交易流水
		 */
		@Item(alias="PAYREQSN")
		private String payReqSn;

		/**
		 * 支付交易单号
		 */
		@Item(alias="PAYTRXID")
		private String payTrxId;

		/**
		 * 交易类型码
		 */
		@Item(alias="TRXCODE")
		private String TrxCode;

		/**
		 * 交易商户号
		 */
		@Item(alias="CUSID")
		private String cusId;

		/**
		 * 支付交易流水
		 */
		public String getPayReqSn() {
			return payReqSn;
		}

		/**
		 * 支付交易流水
		 */
		public void setPayReqSn(String payReqSn) {
			this.payReqSn = payReqSn;
		}

		/**
		 * 支付交易单号
		 */
		public String getPayTrxId() {
			return payTrxId;
		}

		/**
		 * 支付交易单号
		 */
		public void setPayTrxId(String payTrxId) {
			this.payTrxId = payTrxId;
		}

		/**
		 * 交易类型码
		 */
		public String getTrxCode() {
			return TrxCode;
		}

		/**
		 * 交易类型码
		 */
		public void setTrxCode(EnumTradeType enumTradeType) {
			TrxCode = enumTradeType.name();
		}

		/**
		 * 交易商户号
		 */
		public String getCusId() {
			return cusId;
		}

		/**
		 * 交易商户号
		 */
		public void setCusId(String cusId) {
			this.cusId = cusId;
		}

	}

	public HeadReq getHeadReq() {
		return headReq;
	}

	public void setHeadReq(HeadReq headReq) {
		this.headReq = headReq;
	}

	public String getSigneDmsg() {
		return signeDmsg;
	}

	public void setSigneDmsg(String signeDmsg) {
		this.signeDmsg = signeDmsg;
	}

	public TradeStatusQueryTranReq getTradeStatusQueryTranReq() {
		return tradeStatusQueryTranReq;
	}

	public void setTradeStatusQueryTranReq(TradeStatusQueryTranReq tradeStatusQueryTranReq) {
		this.tradeStatusQueryTranReq = tradeStatusQueryTranReq;
	}
}
