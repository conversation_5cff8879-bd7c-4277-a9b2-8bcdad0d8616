package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 关闭订单响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class CloseOrderRsp {

	/**
	 * 返回状态码
	 */
	@Item(cdata=true)
	private String return_code;

	/**
	 * 返回信息
	 */
	@Item(cdata=true)
	private String return_msg;

	/**
	 * 公众账号id
	 */
	@Item(cdata=true)
	private String appid;

	/**
	 * 商户号
	 */
	@Item(cdata=true)
	private String mch_id;

	/**
	 * 随机字符串
	 */
	@Item(cdata=true)
	private String nonce_str;

	/**
	 * 签名
	 */
	@Item(cdata=true)
	private String sign;

	/**
	 * 错误代码
	 */
	@Item(cdata=true)
	private String err_code;

	/**
	 * 错误代码描述
	 */
	@Item(cdata=true)
	private String err_code_des;

	public String getReturn_code() {
		return return_code;
	}

	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	public String getReturn_msg() {
		return return_msg;
	}

	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getMch_id() {
		return mch_id;
	}

	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	public String getNonce_str() {
		return nonce_str;
	}

	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getErr_code() {
		return err_code;
	}

	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	public String getErr_code_des() {
		return err_code_des;
	}

	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}

	@Override
	public String toString() {
		return "CloseOrderRsp[return_code=" + return_code + ",return_msg=" + return_msg + ",appid=" + appid + ",mch_id=" + mch_id + ",nonce_str="
				+ nonce_str + ",sign=" + sign + ",err_code=" + err_code + ",err_code_des=" + err_code_des + "]";
	}
}
