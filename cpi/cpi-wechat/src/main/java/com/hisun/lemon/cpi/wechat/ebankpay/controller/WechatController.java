package com.hisun.lemon.cpi.wechat.ebankpay.controller;

import java.util.Optional;

import com.hisun.lemon.cpi.wechat.ebankpay.req.WeChatOrderNotifyReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.hisun.lemon.cpi.wechat.ebankpay.WeChatProperties;
import com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity;

//@RestController
public class WechatController {

	private static final Logger logger = LoggerFactory.getLogger(WechatController.class);

	@Autowired
	WeChatProperties weChatProperties;

	/**
	 * 支付回调接口
	 * 
	 * @param req
	 */
	//@PostMapping("/placeOrderNotify")
	public void placeOrderNotify(Optional<WeChatOrderNotifyReq> req) {
		logger.info("placeOrderNotify:" + req.toString());
		// 根据业务状态判断该通知是否处理过
		// 签名认证
		if (WechatSecurity.isVerify(req, req.get().getSign())) {

		}
		// 校验订单金额是否与商户的订单金额一致

	}

}
