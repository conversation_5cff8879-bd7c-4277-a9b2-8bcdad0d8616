package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.HeadRsp;

/**
 * 离线支付响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "VSPAPI")
@VerifyValue
public class OfflinePaymentRsp {

	@Nest(alias = "HEAD")
	private HeadRsp headRsp;

	@Verify(xpath = "//VSPAPI/HEAD/SIGNEDMSG",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WEIXINSecurity")
	private String verify;

	@Nest(alias = "BODY")
	private OfflinePaymentTranRsp offlinePaymentTranRsp;

	public static class OfflinePaymentTranRsp {

		/**
		 * 交易结果
		 */
		@Item(alias = "TRXSTATUS")
		private String trxStatus;

		/**
		 * 错误说明
		 */
		@Item(alias = "ERRMSG")
		private String errMsg;

		/**
		 * 交易单号
		 */
		@Item(alias = "TRXID")
		private String trxId;

		/**
		 * 交易结果
		 */
		public String getTrxStatus() {
			return trxStatus;
		}

		/**
		 * 交易结果
		 */
		public void setTrxStatus(String trxStatus) {
			this.trxStatus = trxStatus;
		}

		/**
		 * 错误说明
		 */
		public String getErrMsg() {
			return errMsg;
		}

		/**
		 * 错误说明
		 */
		public void setErrMsg(String errMsg) {
			this.errMsg = errMsg;
		}

		/**
		 * 交易单号
		 */
		public String getTrxId() {
			return trxId;
		}

		/**
		 * 交易单号
		 */
		public void setTrxId(String trxId) {
			this.trxId = trxId;
		}

		@Override
		public String toString() {
			return "OfflinePaymentTranRsp[trxStatus=" + trxStatus + ",errMsg=" + errMsg + ",trxId=" + trxId + "]";
		}
	}

	public String getVerify() {
		return verify;
	}

	public void setVerify(String verify) {
		this.verify = verify;
	}

	public HeadRsp getHeadRsp() {
		return headRsp;
	}

	public void setHeadRsp(HeadRsp headRsp) {
		this.headRsp = headRsp;
	}

	public OfflinePaymentTranRsp getOfflinePaymentTranRsp() {
		return offlinePaymentTranRsp;
	}

	public void setOfflinePaymentTranRsp(OfflinePaymentTranRsp offlinePaymentTranRsp) {
		this.offlinePaymentTranRsp = offlinePaymentTranRsp;
	}

	@Override
	public String toString() {

		return "OfflinePaymentRsp[headRsp=" + headRsp + ",verify=" + verify + ",offlinePaymentTranRsp=" + offlinePaymentTranRsp + "]";
	}
}
