package com.hisun.lemon.cpi.wechat.ebankpay;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.URL;

import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;
import com.hisun.channel.common.utils.StringUtils;

/**
 * 枚举公共类
 * 
 * <AUTHOR>
 *
 */
@Controller
public class WEIXINEnumCommon {

	private WEIXINEnumCommon() {
	}

	/**
	 * 接口类型
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumSource {
		Token,
		placeOrder,
		orderQuery,
		payment,
		refund,
		revoke,
		tradestatusquery,
		submerchantEntry,
		submerchantQuery,
		closeOrder,
		applyRefund,
		refundQuery,
		downloadBill,
		mercPlaceOrder,
		settlementQuery,
		reverseOrder
	}

	/**
	 * 账单类型
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumBillType {
		/**
		 * 返回当日所有订单信息
		 */
		ALL,
		/**
		 * 返回当日成功支付的订单
		 */
		SUCCESS,
		/**
		 * 返回当日退款订单
		 */
		REFUND,
		/**
		 * 返回当日充值退款订单（相比其他对账单多一栏“返还手续费”）
		 */
		RECHARGE_REFUND

	}

	/**
	 * 交易返回码
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumTrxStatus {
		HANDLING("22"),
		SUCCESS("31"),
		FAIL("32");

		private String code;

		public static EnumTrxStatus getEnum(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumTrxStatus trxStatus : values()) {
					if (StringUtils.equals(trxStatus.getCode(), code)) {
						return trxStatus;
					}
				}
			}
			return null;
		}

		EnumTrxStatus(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	/**
	 * 币种
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumCurrency {
		/**
		 * 人民币
		 */
		CNY,
		/**
		 * 港币
		 */
		HKD,
		/**
		 * 加币
		 */
		CAD,
		/**
		 * 美元
		 */
		USD,
		/**
		 * 欧元
		 */
		EUR,
		/**
		 * 英镑
		 */
		GBP,
		/**
		 * 澳元
		 */
		AUD,
		/**
		 * 新西兰元
		 */
		NZD,
		/**
		 * 日元
		 */
		JPY,
		/**
		 * 瑞士法郎
		 */
		CHF,
		/**
		 * 瑞典克朗
		 */
		SEK,
		/**
		 * 挪威克朗
		 */
		NOK,
		/**
		 * 新加坡元
		 */
		SGD,
		/**
		 * 泰国铢
		 */
		THB
	}

	/**
	 * 接口编号
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumInfCode {

		OFFLINEPAYMENT("VSPTRX_WEIXIN_CNP"),
		REVOKE("VSPTRX_WEIXIN_REFUND"),
		REFUND("VSPTRX_WEIXIN_REFUNDTN"),
		STATUSQUERY("VSPTRX_CUSTRXQUERY");

		private String code;

		public static EnumInfCode getEnum(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumInfCode infCode : values()) {
					if (StringUtils.equals(infCode.getCode(), code)) {
						return infCode;
					}
				}
			}
			return null;
		}

		EnumInfCode(String code) {
			this.code = code;
		}

		public String getCode() {
			return code;
		}

	}

	/**
	 * 交易类型码
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumTradeType {
		/**
		 * 跨境微信支付
		 */
		VSP503,
		/**
		 * 跨境微信支付撤销
		 */
		VSP504,
		/**
		 * 跨境微信支付退款
		 */
		VSP505,
		/**
		 * 公众号支付
		 */
		JSAPI,
		/**
		 * 扫码支付
		 */
		NATIVE,
		/**
		 * APP支付
		 */
		APP

	}

	/**
	 * 商户业务类型
	 * 
	 * <AUTHOR>
	 *
	 */
	public enum EnumBusinessCategory {
		Shoes_Garments("343"),
		Air_Ticket("493"),
		Office_Supplies("492"),
		Hotel_Industry("491"),
		Education_Industry("490"),
		Logistics("489"),
		Digital_Appliance("488"),
		Maternal_Infant("487"),
		Cosmetics("486"),
		Food("485"),
		Comprehensive_mall("484"),
		Other("494");

		private String code;

		EnumBusinessCategory(String code) {
			this.code = code;
		}

		public static EnumBusinessCategory getEnum(String code) {
			if (StringUtils.isNotEmpty(code)) {
				for (EnumBusinessCategory trxStatus : values()) {
					if (StringUtils.equals(trxStatus.getCode(), code)) {
						return trxStatus;
					}
				}
			}
			return null;
		}

		public String getCode() {
			return code;
		}

		public void setCode(String code) {
			this.code = code;
		}

	}

}
