package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.HeadRsp;

/**
 * 交易状态查询响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "VSPAPI")
@VerifyValue
public class TradeStatusQueryRsp {

	@Nest(alias = "HEAD")
	private HeadRsp headRsp;

	@Verify(xpath = "//VSPAPI/HEAD/SIGNEDMSG",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WEIXINSecurity")
	private String verify;

	@Nest(alias = "BODY")
	private TradeStatusQueryTranRsp tradeStatusQueryTranRsp;

	public static class TradeStatusQueryTranRsp {

		/**
		 * 交易状态
		 */
		@Item(alias = "TRXSTATUS")
		private String trxStatus;

		/**
		 * 状态说明
		 */
		@Item(alias = "TRXSTATUSNAME")
		private String trxStatusName;

		/**
		 * 支付交易单号
		 */
		@Item(alias = "PAYTRXID")
		private String payTrxId;

		/**
		 * 退款交易单号
		 */
		@Item(alias = "REFUNDTRXID")
		private String refundTrxId;

		/**
		 * 交易状态
		 */
		public String getTrxStatus() {
			return trxStatus;
		}

		/**
		 * 交易状态
		 */
		public void setTrxStatus(String trxStatus) {
			this.trxStatus = trxStatus;
		}

		/**
		 * 状态说明
		 */
		public String getTrxStatusName() {
			return trxStatusName;
		}

		/**
		 * 状态说明
		 */
		public void setTrxStatusName(String trxStatusName) {
			this.trxStatusName = trxStatusName;
		}

		/**
		 * 支付交易单号
		 */
		public String getPayTrxId() {
			return payTrxId;
		}

		/**
		 * 支付交易单号
		 */
		public void setPayTrxId(String payTrxId) {
			this.payTrxId = payTrxId;
		}

		/**
		 * 退款交易单号
		 */
		public String getRefundTrxId() {
			return refundTrxId;
		}

		/**
		 * 退款交易单号
		 */
		public void setRefundTrxId(String refundTrxId) {
			this.refundTrxId = refundTrxId;
		}

		@Override
		public String toString() {
			return "TradeStatusQueryTranRsp[trxStatus=" + trxStatus + ",trxStatusName=" + trxStatusName + ",payTrxId=" + payTrxId + ",refundTrxId="
					+ refundTrxId + "]";

		}
	}

	public HeadRsp getHeadRsp() {
		return headRsp;
	}

	public void setHeadRsp(HeadRsp headRsp) {
		this.headRsp = headRsp;
	}

	public String getVerify() {
		return verify;
	}

	public void setVerify(String verify) {
		this.verify = verify;
	}

	public TradeStatusQueryTranRsp getTradeStatusQueryTranRsp() {
		return tradeStatusQueryTranRsp;
	}

	public void setTradeStatusQueryTranRsp(TradeStatusQueryTranRsp tradeStatusQueryTranRsp) {
		this.tradeStatusQueryTranRsp = tradeStatusQueryTranRsp;
	}

	@Override
	public String toString() {
		return "TradeStatusQueryRsp[headRsp=" + headRsp + ",verify=" + verify + ",tradeStatusQueryTranRsp=" + tradeStatusQueryTranRsp + "]";
	}

}
