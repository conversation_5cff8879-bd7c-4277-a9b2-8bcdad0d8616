package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;
/**
 * 微信刷卡支付 撤销订单响应对象
 */
@Xml(root = "xml", removeHead = true)
public class ReverseQrderRsp {

    /**
     * 返回状态码
     */
    @Item(cdata=true)
    private String return_code;

    /**
     * 返回信息
     */
    @Item(cdata=true)
    private String return_msg;

    /**
     * 公众账号id
     */
    @Item(cdata=true)
    private String appid;

    /**
     * 商户号
     */
    @Item(cdata=true)
    private String mch_id;

    /**
     * 随机字符串
     */
    @Item(cdata=true)
    private String nonce_str;

    /**
     * 签名
     */
    @Item(cdata=true)
    private String sign;

    /**
     * 错误代码
     */
    @Item(cdata=true)
    private String err_code;

    /**
     * 错误代码描述
     */
    @Item(cdata=true)
    private String err_code_des;

    @Item(cdata=true)
    private String sub_mch_id;

    @Item(cdata=true)
    private String recall;

    @Item(cdata=true)
    private String rate;

    public String getReturn_code() {
        return return_code;
    }

    public void setReturn_code(String return_code) {
        this.return_code = return_code;
    }

    public String getReturn_msg() {
        return return_msg;
    }

    public void setReturn_msg(String return_msg) {
        this.return_msg = return_msg;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMch_id() {
        return mch_id;
    }

    public void setMch_id(String mch_id) {
        this.mch_id = mch_id;
    }

    public String getNonce_str() {
        return nonce_str;
    }

    public void setNonce_str(String nonce_str) {
        this.nonce_str = nonce_str;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getErr_code() {
        return err_code;
    }

    public void setErr_code(String err_code) {
        this.err_code = err_code;
    }

    public String getErr_code_des() {
        return err_code_des;
    }

    public void setErr_code_des(String err_code_des) {
        this.err_code_des = err_code_des;
    }

    public String getSub_mch_id() {
        return sub_mch_id;
    }

    public void setSub_mch_id(String sub_mch_id) {
        this.sub_mch_id = sub_mch_id;
    }

    public String getRecall() {
        return recall;
    }

    public void setRecall(String recall) {
        this.recall = recall;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    @Override
    public String toString() {
        return "ReverseQrderRsp{" +
                "return_code='" + return_code + '\'' +
                ", return_msg='" + return_msg + '\'' +
                ", appid='" + appid + '\'' +
                ", mch_id='" + mch_id + '\'' +
                ", nonce_str='" + nonce_str + '\'' +
                ", sign='" + sign + '\'' +
                ", err_code='" + err_code + '\'' +
                ", err_code_des='" + err_code_des + '\'' +
                ", sub_mch_id='" + sub_mch_id + '\'' +
                ", recall='" + recall + '\'' +
                ", rate='" + rate + '\'' +
                '}';
    }
}
