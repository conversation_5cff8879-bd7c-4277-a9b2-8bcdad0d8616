package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 平台成功接收下单回调通知报文，返回给微信的结果
 */
@Xml(root = "xml", removeHead = true)
public class WeChatOrderNotifyRsp {

    /**
     * 返回状态码
     */
    @Item(alias="return_code",cdata=true)
    private String return_code;

    /**
     * 返回信息
     */
    @Item(alias="return_msg",cdata=true)
    private String return_msg;

    public String getReturn_code() {
        return return_code;
    }

    public void setReturn_code(String return_code) {
        this.return_code = return_code;
    }

    public String getReturn_msg() {
        return return_msg;
    }

    public void setReturn_msg(String return_msg) {
        this.return_msg = return_msg;
    }
}
