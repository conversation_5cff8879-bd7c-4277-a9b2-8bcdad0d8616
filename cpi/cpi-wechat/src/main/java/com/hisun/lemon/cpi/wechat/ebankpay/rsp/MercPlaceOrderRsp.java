package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Xml;

/**
 * 商户扫码，微信刷卡支付响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class MercPlaceOrderRsp {

	/**
	 * 返回状态吗
	 */
	@Item(alias = "return_code",cdata=true)
	private String return_code;

	/**
	 * 返回信息
	 */
	@Item(alias = "return_msg",cdata=true)
	private String return_msg;

	/**
	 * 公共账号id
	 */
	@Item(alias = "appid",cdata=true)
	private String appid;

	/**
	 * 商户号
	 */
	@Item(alias = "mch_id",cdata=true)
	private String mch_id;

	/**
	 * 设备号
	 */
	@Item(alias = "device_info",cdata=true)
	private String device_info;

	/**
	 * 随机字符串
	 */
	@Item(alias = "nonce_str",cdata=true)
	private String nonce_str;

	/**
	 * 签名
	 */
	@Item(alias = "sign",cdata=true)
	private String sign;

	/**
	 * 业务结果
	 */
	@Item(alias = "result_code",cdata=true)
	private String result_code;

	/**
	 * 错误代码
	 */
	@Item(alias = "err_code",cdata=true)
	private String err_code;

	/**
	 * 错误代码描述
	 */
	@Item(alias = "err_code_des",cdata=true)
	private String err_code_des;

	/**
	 * 用户标识
	 */
	@Item(alias = "openid",cdata=true)
	private String openid;

	/**
	 * 是否关注公众账号
	 */
	@Item(alias = "is_subscribe",cdata=true)
	private String is_subscribe;

	/**
	 * 交易类型
	 */
	@Item(alias = "trade_type",cdata=true)
	private String trade_type;

	/**
	 * 付款银行
	 */
	@Item(alias = "bank_type",cdata=true)
	private String bank_type;

	/**
	 * 标价金额
	 */
	@Item(alias = "total_fee",cdata=true)
	private String total_fee;

	/**
	 * 标价币种
	 */
	@Item(alias = "fee_type",cdata=true)
	private String fee_type;

	/**
	 * 用户支付金额
	 */
	@Item(alias = "cash_fee",cdata=true)
	private String cash_fee;

	/**
	 * 用户支付金额币种
	 */
	@Item(alias = "cash_fee_type",cdata=true)
	private String cash_fee_type;

	/**
	 * 微信支付订单号
	 */
	@Item(alias = "transaction_id",cdata=true)
	private String transaction_id;
	/**
	 * 商户订单号
	 */
	@Item(alias = "out_trade_no",cdata=true)
	private String out_trade_no;
	/**
	 * 商家数据包
	 */
	@Item(alias = "attach",cdata=true)
	private String attach;
	/**
	 * 支付完成时间
	 */
	@Item(alias = "time_end",cdata=true)
	private String time_end;
	/**
	 * 汇率
	 */
	@Item(alias = "rate",cdata=true)
	private String rate;
	/**
	 * 返回状态吗
	 */
	public String getReturn_code() {
		return return_code;
	}

	/**
	 * 返回状态吗
	 */
	public void setReturn_code(String return_code) {
		this.return_code = return_code;
	}

	/**
	 * 返回信息
	 */
	public String getReturn_msg() {
		return return_msg;
	}

	/**
	 * 返回信息
	 */
	public void setReturn_msg(String return_msg) {
		this.return_msg = return_msg;
	}

	/**
	 * 公共账号id
	 */
	public String getAppid() {
		return appid;
	}

	/**
	 * 公共账号id
	 */
	public void setAppid(String appid) {
		this.appid = appid;
	}

	/**
	 * 商户号
	 */
	public String getMch_id() {
		return mch_id;
	}

	/**
	 * 商户号
	 */
	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	/**
	 * 设备号
	 */
	public String getDevice_info() {
		return device_info;
	}

	/**
	 * 设备号
	 */
	public void setDevice_info(String device_info) {
		this.device_info = device_info;
	}

	/**
	 * 随机字符串
	 */
	public String getNonce_str() {
		return nonce_str;
	}

	/**
	 * 随机字符串
	 */
	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 业务结果
	 */
	public String getResult_code() {
		return result_code;
	}

	/**
	 * 业务结果
	 */
	public void setResult_code(String result_code) {
		this.result_code = result_code;
	}

	/**
	 * 错误代码
	 */
	public String getErr_code() {
		return err_code;
	}

	/**
	 * 错误代码
	 */
	public void setErr_code(String err_code) {
		this.err_code = err_code;
	}

	/**
	 * 错误代码描述
	 */
	public String getErr_code_des() {
		return err_code_des;
	}

	/**
	 * 错误代码描述
	 */
	public void setErr_code_des(String err_code_des) {
		this.err_code_des = err_code_des;
	}

	/**
	 * 交易类型
	 */
	public String getTrade_type() {
		return trade_type;
	}

	/**
	 * 交易类型
	 */
	public void setTrade_type(String trade_type) {
		this.trade_type = trade_type;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public String getIs_subscribe() {
		return is_subscribe;
	}

	public void setIs_subscribe(String is_subscribe) {
		this.is_subscribe = is_subscribe;
	}

	public String getBank_type() {
		return bank_type;
	}

	public void setBank_type(String bank_type) {
		this.bank_type = bank_type;
	}

	public String getTotal_fee() {
		return total_fee;
	}

	public void setTotal_fee(String total_fee) {
		this.total_fee = total_fee;
	}

	public String getFee_type() {
		return fee_type;
	}

	public void setFee_type(String fee_type) {
		this.fee_type = fee_type;
	}

	public String getCash_fee() {
		return cash_fee;
	}

	public void setCash_fee(String cash_fee) {
		this.cash_fee = cash_fee;
	}

	public String getCash_fee_type() {
		return cash_fee_type;
	}

	public void setCash_fee_type(String cash_fee_type) {
		this.cash_fee_type = cash_fee_type;
	}

	public String getTransaction_id() {
		return transaction_id;
	}

	public void setTransaction_id(String transaction_id) {
		this.transaction_id = transaction_id;
	}

	public String getOut_trade_no() {
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	public String getAttach() {
		return attach;
	}

	public void setAttach(String attach) {
		this.attach = attach;
	}

	public String getTime_end() {
		return time_end;
	}

	public void setTime_end(String time_end) {
		this.time_end = time_end;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}
}
