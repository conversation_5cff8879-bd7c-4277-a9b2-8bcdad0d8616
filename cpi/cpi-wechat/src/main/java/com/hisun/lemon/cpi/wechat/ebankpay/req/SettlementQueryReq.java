package com.hisun.lemon.cpi.wechat.ebankpay.req;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.Xml;

import javax.validation.constraints.NotNull;
import java.util.UUID;

/**
 * 结算资金查询请求参数
 */
@Xml(root = "xml", removeHead = true)
public class SettlementQueryReq {
    /**
     * 公众账号ID
     */
    @Item(alias = "appid")
    @NotNull
    private String appid;

    /**
     * 商户号
     */
    @Item(alias = "mch_id")
    @NotNull
    private String mch_id;

    /**
     * 子商户号
     */
    @Item(alias="sub_mch_id")
    private String sub_mch_id;

    /**
     * 结算状态
     */
    @Item(alias="usetag")
    private int usetag;

    /**
     * 随机字符串
     */
    @Item(alias = "nonce_str")
    private String nonce_str;

    /**
     * 偏移量
     */
    @Item(alias = "offset")
    private int offset;

    /**
     * 最大记录条数
     */
    @Item(alias = "limit")
    private int limit;

    /**
     * 开始日期
     */
    @Item(alias = "date_start")
    private String date_start;

    /**
     * 结束日期
     */
    @Item(alias = "date_end")
    private String date_end;

    /**
     * 签名
     */
    @Sign(alias = "sign", xpath = "//xml",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
    @NotNull
    private String sign;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMch_id() {
        return mch_id;
    }

    public void setMch_id(String mch_id) {
        this.mch_id = mch_id;
    }

    public String getSub_mch_id() {
        return sub_mch_id;
    }

    public void setSub_mch_id(String sub_mch_id) {
        this.sub_mch_id = sub_mch_id;
    }

    public int getUsetag() {
        return usetag;
    }

    public void setUsetag(int usetag) {
        this.usetag = usetag;
    }

    /**
     * 随机字符串
     */
    public String getNonce_str() {
        nonce_str= UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
        return nonce_str;
    }


    public void setNonce_str(String nonce_str) {
        this.nonce_str = nonce_str;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public String getDate_start() {
        return date_start;
    }

    public void setDate_start(String date_start) {
        this.date_start = date_start;
    }

    public String getDate_end() {
        return date_end;
    }

    public void setDate_end(String date_end) {
        this.date_end = date_end;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
