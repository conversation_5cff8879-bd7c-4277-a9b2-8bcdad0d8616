package com.hisun.lemon.cpi.wechat.ebankpay.req;
import java.util.UUID;

import javax.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONArray;
import com.hisun.channel.common.utils.StringUtils;
import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Sign;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.GoodsInfo;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumCurrency;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumTradeType;

/**
 * 用户扫码，聚合支付，统一下单
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "xml", removeHead = true)
public class PlaceOrderReq {

	/**
	 * 公共账号ID
	 */
	@Item(alias = "appid")
	@NotNull
	private String appid;

	/**
	 * 商户号
	 */
	@Item(alias = "mch_id")
	@NotNull
	private String mch_id;

	/**
	 * 设备号
	 */
	@Item(alias = "device_info")
	private String device_info;

	/**
	 * 随机字符串
	 */
	@Item(alias = "nonce_str")
	private String nonce_str;

	/**
	 * 签名
	 */
	@Sign(alias = "sign", xpath = "//xml",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WechatSecurity")
	@NotNull
	private String sign;

	/**
	 * 签名类型
	 */
	@Item(alias = "sign_type")
	private String sign_type;

	/**
	 * 商品描述
	 */
	@Item(alias = "body")
	@NotNull
	private String body;

	/**
	 * 商品详情
	 */
	@Item(alias = "detail", cdata = true)
	private String detail;

	/**
	 * 附加数据
	 */
	@Item(alias = "attach")
	private String attach;

	/**
	 * 商户订单号
	 */
	@Item(alias = "out_trade_no")
	private String out_trade_no;

	/**
	 * 标价币种
	 */
	@Item(alias = "fee_type")
	private String fee_type;

	/**
	 * 标价金额
	 */
	@Item(alias = "total_fee")
	@NotNull
	private int total_fee;

	/**
	 * 终端ip
	 */
	@Item(alias = "spbill_create_ip")
	private String spbill_create_ip;

	/**
	 * 交易起始时间
	 */
	@Item(alias = "time_start")
	private String time_start;

	/**
	 * 交易结束时间,注意：最短失效时间间隔必须大于5分钟
	 */
	@Item(alias = "time_expire")
	private String time_expire;

	/**
	 * 通知地址
	 */
	@Item(alias = "notify_url")
	private String notify_url;

	/**
	 * 交易类型
	 */
	@Item(alias = "trade_type")
	private String trade_type;

	/**
	 * 商品id
	 */
	@Item(alias = "product_id")
	private String product_id;

	/**
	 * 指定支付方式
	 */
	@Item(alias = "limit_pay")
	private String limit_pay;

	/**
	 * 用户标识
	 */
	@Item(alias = "openid")
	private String openid;

	/**
	 * 子商户编号
	 */
	@Item(alias="sub_mch_id")
	private String sub_mch_id;
	
	/**
	 * 子商户编号
	 */
	public String getSub_mch_id() {
		return sub_mch_id;
	}

	/**
	 * 子商户编号
	 */
	public void setSub_mch_id(String sub_mch_id) {
		this.sub_mch_id = sub_mch_id;
	}

	/**
	 * 公共账号ID
	 */
	public String getAppid() {
		return appid;
	}

	/**
	 * 公共账号ID
	 */
	public void setAppid(String appid) {
		this.appid = appid;
	}

	/**
	 * 商户号
	 */
	public String getMch_id() {
		return mch_id;
	}

	/**
	 * 商户号
	 */
	public void setMch_id(String mch_id) {
		this.mch_id = mch_id;
	}

	/**
	 * 设备号
	 */
	public String getDevice_info() {
		if (StringUtils.isEmpty(device_info)) {
			device_info = "WEB";
		}
		return device_info;
	}

	/**
	 * 设备号
	 */
	public void setDevice_info(String device_info) {
		this.device_info = device_info;
	}

	/**
	 * 随机字符串
	 */
	public String getNonce_str() {
		 nonce_str=UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
		 return nonce_str;
	}

	/**
	 * 随机字符串
	 */
	public void setNonce_str(String nonce_str) {
		this.nonce_str = nonce_str;
	}

	/**
	 * 签名
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * 签名
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * 签名类型
	 */
	public String getSign_type() {
		if (StringUtils.isEmpty(sign_type)) {
			sign_type = "MD5";
		}
		return sign_type;
	}

	/**
	 * 签名类型
	 */
	public void setSign_type(String sign_type) {
		this.sign_type = sign_type;
	}

	/**
	 * 商品描述
	 */
	public String getBody() {
		return body;
	}

	/**
	 * 商品描述
	 */
	public void setBody(String body) {
		this.body = body;
	}

	/**
	 * 商品详情
	 */
	public String getDetail() {
		return detail;
	}

	/**
	 * 商品详情
	 */
	public void setDetail(GoodsInfo goodsInfos) {
		if (goodsInfos!=null) {
			this.detail = JSONArray.toJSONString(goodsInfos);
		}
	}

	/**
	 * 附加数据
	 */
	public String getAttach() {
		return attach;
	}

	/**
	 * 附加数据
	 */
	public void setAttach(String attach) {
		this.attach = attach;
	}

	/**
	 * 商户订单号
	 */
	public String getOut_trade_no() {
		return out_trade_no;
	}

	/**
	 * 商户订单号
	 */
	public void setOut_trade_no(String out_trade_no) {
		this.out_trade_no = out_trade_no;
	}

	/**
	 * 标价币种
	 */
	public String getFee_type() {
		return fee_type;
	}

	/**
	 * 标价币种
	 */
	public void setFee_type(EnumCurrency enumCurrency) {
		if (enumCurrency != null) {
			this.fee_type = enumCurrency.toString();
		}
	}

	/**
	 * 标价金额
	 */
	public int getTotal_fee() {
		return total_fee;
	}

	/**
	 * 标价金额
	 */
	public void setTotal_fee(int total_fee) {
		this.total_fee = total_fee;
	}

	/**
	 * 终端ip
	 */
	public String getSpbill_create_ip() {
		return spbill_create_ip;
	}

	/**
	 * 终端ip
	 */
	public void setSpbill_create_ip(String spbill_create_ip) {
		this.spbill_create_ip = spbill_create_ip;
	}

	/**
	 * 交易起始时间
	 */
	public String getTime_start() {
		return time_start;
	}

	/**
	 * 交易起始时间
	 */
	public void setTime_start(String time_start) {
		this.time_start = time_start;
	}

	/**
	 * 交易结束时间,注意：最短失效时间间隔必须大于5分钟
	 */
	public String getTime_expire() {
		return time_expire;
	}

	/**
	 * 交易结束时间,注意：最短失效时间间隔必须大于5分钟
	 */
	public void setTime_expire(String time_expire) {
		this.time_expire = time_expire;
	}

	/**
	 * 通知地址
	 */
	public String getNotify_url() {
		return notify_url;
	}

	/**
	 * 通知地址
	 */
	public void setNotify_url(String notify_url) {
		this.notify_url = notify_url;
	}

	/**
	 * 交易类型
	 */
	public String getTrade_type() {
		return trade_type;
	}

	/**
	 * 交易类型
	 */
	public void setTrade_type(EnumTradeType enumTradeType) {
		if (enumTradeType != null) {
			this.trade_type = enumTradeType.toString();
		}
	}

	/**
	 * 商品id
	 */
	public String getProduct_id() {
		return product_id;
	}

	/**
	 * 商品id
	 */
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}

	/**
	 * 指定支付方式
	 */
	public String getLimit_pay() {
		return limit_pay;
	}

	/**
	 * 指定支付方式
	 */
	public void setLimit_pay(String limit_pay) {
		this.limit_pay = limit_pay;
	}

	/**
	 * 用户标识
	 */
	public String getOpenid() {
		return openid;
	}

	/**
	 * 用户标识
	 */
	public void setOpenid(String openid) {
		this.openid = openid;
	}
}
