package com.hisun.lemon.cpi.wechat.ebankpay;

import com.hisun.channel.expression.ExpressionInvoker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class WEIXINDecrypt implements ExpressionInvoker{

	private static final Logger logger = LoggerFactory.getLogger(WEIXINDecrypt.class);

	@Override
	public String invoke(Object context, String... args) {
		String val=args[0].split("cosp=")[1].split("</Msg>")[0];
		logger.info("WEIXINDecrypt.invoke xml:"+val+"</Msg>");
		return val+"</Msg>";
	}

}
