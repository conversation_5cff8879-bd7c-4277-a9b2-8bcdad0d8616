package com.hisun.lemon.cpi.wechat.ebankpay.rsp;

import javax.validation.constraints.NotNull;

import com.hisun.channel.parse.annotation.Item;
import com.hisun.channel.parse.annotation.Nest;
import com.hisun.channel.parse.annotation.Verify;
import com.hisun.channel.parse.annotation.VerifyValue;
import com.hisun.channel.parse.annotation.Xml;
import com.hisun.lemon.cpi.wechat.ebankpay.HeadRsp;

/**
 * 撤销响应报文
 * 
 * <AUTHOR>
 *
 */
@Xml(root = "VSPAPI")
@VerifyValue
public class RevokeRsp {

	@Nest(alias = "HEAD")
	private HeadRsp headRsp;

	@Verify(xpath = "//VSPAPI/HEAD/SIGNEDMSG",signauteClass="com.hisun.lemon.cpi.wechat.ebankpay.WEIXINSecurity")
	private String verify;

	@Nest(alias = "BODY")
	private RevokeTranRsp revokeTranRsp;

	public static class RevokeTranRsp {

		/**
		 * 交易单号
		 */
		@Item(alias = "TRXID")
		@NotNull
		private String trxId;

		/**
		 * 原交易单号
		 */
		@Item(alias = "OLDTRXID")
		@NotNull
		private String oldtrxId;

		/**
		 * 撤销金额
		 */
		@Item(alias = "TRXAMT")
		@NotNull
		private Integer trxAmt;

		/**
		 * 退款时间
		 */
		@Item(alias = "REFUNDTIME")
		private String refundTime;

		/**
		 * 退款交易状态
		 */
		@Item(alias = "TRXSTATUS")
		@NotNull
		private String trxStatus;

		/**
		 * 交易单号
		 */
		public String getTrxId() {
			return trxId;
		}

		/**
		 * 交易单号
		 */
		public void setTrxId(String trxId) {
			this.trxId = trxId;
		}

		/**
		 * 原交易单号
		 */
		public String getOldtrxId() {
			return oldtrxId;
		}

		/**
		 * 原交易单号
		 */
		public void setOldtrxId(String oldtrxId) {
			this.oldtrxId = oldtrxId;
		}

		/**
		 * 撤销金额
		 */
		public Integer getTrxAmt() {
			return trxAmt;
		}

		/**
		 * 撤销金额
		 */
		public void setTrxAmt(Integer trxAmt) {
			this.trxAmt = trxAmt;
		}

		/**
		 * 退款时间
		 */
		public String getRefundTime() {
			return refundTime;
		}

		/**
		 * 退款时间
		 */
		public void setRefundTime(String refundTime) {
			this.refundTime = refundTime;
		}

		/**
		 * 退款交易状态
		 */
		public String getTrxStatus() {

			return trxStatus;
		}

		/**
		 * 退款交易状态
		 */
		public void setTrxStatus(String trxStatus) {
			this.trxStatus = trxStatus;
		}

		@Override
		public String toString() {

			return "RevokeTranRsp[trxId=" + trxId + ",oldtrxId=" + oldtrxId + ",trxAmt=" + trxAmt + ",refundTime=" + refundTime + ",trxStatus="
					+ trxStatus + "]";
		}
	}

	public HeadRsp getHeadRsp() {
		return headRsp;
	}

	public void setHeadRsp(HeadRsp headRsp) {
		this.headRsp = headRsp;
	}

	public String getVerify() {
		return verify;
	}

	public void setVerify(String verify) {
		this.verify = verify;
	}

	public RevokeTranRsp getRevokeTranRsp() {
		return revokeTranRsp;
	}

	public void setRevokeTranRsp(RevokeTranRsp revokeTranRsp) {
		this.revokeTranRsp = revokeTranRsp;
	}

	@Override
    public String toString() {
		return "RevokeRsp[headRsp=" + headRsp + ",verify=" + verify + ",revokeTranRsp=" + revokeTranRsp + "]";
	}
}
