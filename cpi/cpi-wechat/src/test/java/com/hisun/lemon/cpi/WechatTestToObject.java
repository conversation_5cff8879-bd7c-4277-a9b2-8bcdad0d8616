package com.hisun.lemon.cpi;

import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import com.hisun.channel.common.core.Signature;
import com.hisun.channel.parse.AbstractAnalyserAdaptee;
import com.hisun.channel.parse.Unmarshaller;
import com.hisun.channel.parse.UnmarshallerFactory;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.OfflinePaymentRsp;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.RefundRsp;


@RunWith(SpringRunner.class)
@SpringBootTest
public class WechatTestToObject {

	@Test
	@Ignore
	public void OfflinePaymentTest(){
		String str="<?xml version=\"1.0\" encoding=\"UTF-8\"?><VSPAPI><HEAD><INFCODE>VSPTRX_WEIXIN_CNP</INFCODE><VERSION>01</VERSION><DATATYPE>1</DATATYPE><RETCODE>000000</RETCODE><SIGNEDMSG>6eb877d0a2419b00d24869a5dd378b0d</SIGNEDMSG></HEAD><BODY><TRXID>********</TRXID><TRXSTATUS>0000</TRXSTATUS></BODY></VSPAPI>";
		
		Unmarshaller<OfflinePaymentRsp> unmarshaller = UnmarshallerFactory.getUnmarshaller(OfflinePaymentRsp.class, false);
		((AbstractAnalyserAdaptee<?>)unmarshaller).setSignature(new Signature(){

			@Override
			public String sign(String signStr) {
				return "~~~"+signStr+"~~~";
			}

			@Override
			public boolean verify(String verifyStr, String signStr) {
				return StringUtils.equals("~~~"+verifyStr+"~~~", signStr);
						
			}});
		unmarshaller.analyse();
		OfflinePaymentRsp rsp = unmarshaller.unmarshal(str.getBytes());
		System.out.println("==="+rsp);
	}
}
