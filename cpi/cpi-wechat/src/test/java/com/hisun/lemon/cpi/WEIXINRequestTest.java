package com.hisun.lemon.cpi;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.util.*;

import javax.annotation.Resource;

import com.hisun.channel.common.utils.ReflectionUtils;
import com.hisun.lemon.cpi.wechat.ebankpay.req.*;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.*;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.hisun.lemon.cpi.wechat.ebankpay.GoodsInfo;
import com.hisun.lemon.cpi.wechat.ebankpay.WeChatProperties;
import com.hisun.lemon.cpi.wechat.ebankpay.GoodsInfo.Goods_Detail;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumBillType;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumBusinessCategory;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumCurrency;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumTradeType;
import com.hisun.lemon.cpi.wechat.ebankpay.WeChatApi;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumSource;

@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class WEIXINRequestTest {

	@Resource
	private WeChatApi api;
	
	@Autowired
	private WeChatProperties weChatProperties;

	/**
	 * 页面授权
	 */
	@Test
	public void token(){
		TokenReq req=new TokenReq();
		req.setAppid(weChatProperties.getAppId());
		req.setCode("011hRsKV0DYpHX1kC1LV0nqwKV0hRsKW");
		req.setSecret(weChatProperties.getSecret());
		TokenRsp rsp = api.doSend(req, EnumSource.Token);
		System.out.println(rsp);
	}
	
	/**
	 * 刷新access_token
	 */
	public void refresh_token() {
        
	}
	
	/**
	 * 子商户申请
	 */
	@Test
	public void subMerchantEntry() {
		SubMerchantEntryReq req = new SubMerchantEntryReq();
		req.setApp_id("wx9955355c79974cc6");
		req.setBusiness_category(EnumBusinessCategory.Other);
		req.setContact_email("<EMAIL>");
		req.setContact_name("张三");
		req.setContact_phone("***********");
		req.setMch_id("**********");
		req.setMerchant_introduction("当当网上商城");
		req.setMerchant_name("当当网上商城");
		req.setMerchant_shortname("当当网上商城");
		req.setOffice_phone("***********");
		req.setMerchant_remark("当当网上商城");
		// req.setWebsite("http://bestpay.bestmpay.com/aggregation/placeOrderNotify");

		SubMerchantEntryRsp rsp = api.doSend(req, EnumSource.submerchantEntry);
		System.out.println(rsp);
	}
	
	/**
	 * 子商户查询
	 */
	@Test
	public void subMerchantQuery() {
		SubMerchantQueryReq req = new SubMerchantQueryReq();
		req.setApp_id("wx9955355c79974cc6");
		req.setMch_id("**********");
		req.setSub_mch_id("********");

		SubMerchantQueryRsp rsp = api.doSend(req, EnumSource.submerchantQuery);
		System.out.println(rsp);
	}
	
	/**
	 * 用户扫码下单，聚合支付
	 */
	@Test
	public void userPlacePay() {
		List<Goods_Detail> goods_detail_list = new ArrayList<>();
		GoodsInfo info = new GoodsInfo();
		GoodsInfo.Goods_Detail goods_detail = new GoodsInfo.Goods_Detail();
		goods_detail.setGoods_id("0001");
		goods_detail.setGoods_name("香烟");
		goods_detail.setWxpay_goods_id("0001");
		goods_detail.setQuantity(1);
		goods_detail.setPrice(10);
		goods_detail_list.add(goods_detail);

		goods_detail = new Goods_Detail();
		goods_detail.setGoods_id("0002");
		goods_detail.setGoods_name("香烟");
		goods_detail.setWxpay_goods_id("0002");
		goods_detail.setQuantity(1);
		goods_detail.setPrice(10);
		goods_detail_list.add(goods_detail);
		info.setGoods_detail(goods_detail_list);

		PlaceOrderReq req = new PlaceOrderReq();
		req.setAppid("wx9955355c79974cc6");
		req.setMch_id("**********");
		req.setBody("香烟");
		// req.setDetail(info);
		req.setOut_trade_no("10000005");
		req.setFee_type(EnumCurrency.CNY);
		req.setTotal_fee(1);
		req.setSpbill_create_ip("*************");
		req.setNotify_url("http://bestpay.bestmpay.com/aggregation/placeOrderNotify");
		req.setTrade_type(EnumTradeType.JSAPI);
		req.setOpenid("oiXyawhB-rcKXLsv67Y6FGfKHcJE");
		req.setSub_mch_id("********");

		PlaceOrderRsp rsp = api.doSend(req, EnumSource.placeOrder);
		System.out.println(rsp);
	}

	/**
	 * 商户扫码下单，微信刷卡支付
	 */
	@Test
	public void mercPlacePay() {
		MercPlaceOrderReq req = new MercPlaceOrderReq();
		req.setAppid(weChatProperties.getAppId());
		req.setMch_id(weChatProperties.getPlatMercId());
		req.setSub_mch_id("********");
		req.setBody("iphone 6s 32G");
		req.setOut_trade_no("20171120063010000010");
		req.setDevice_info("8888880000024505");
		req.setTotal_fee(1);
		req.setFee_type(EnumCurrency.valueOf("USD"));
		req.setSpbill_create_ip("***************");
		req.setAuth_code("134737827407761963");
		MercPlaceOrderRsp rsp = api.doSend(req, EnumSource.mercPlaceOrder);
		System.out.println(rsp);
	}

	/**
	 * 订单查询
	 */
	@Test
	public void orderQuery() {
		OrderQueryReq req = new OrderQueryReq();
		req.setAppid("wx9955355c79974cc6");
		req.setMch_id("**********");
		// req.setTransaction_id("2016090910595900000013");
		req.setOut_trade_no("20171120063010000010");
		req.setSub_mch_id("********");

		// Marshaller<OrderQueryReq> marshaller =
		// MarshallerFactory.getMarshaller(OrderQueryReq.class, false);
		// ((AbstractAnalyserAdaptee<?>) marshaller).setSignature(new
		// Signature() {
		//
		// @Override
		// public String sign(String signStr) {
		// return wechatSecurity.sign(signStr);
		// }
		//
		// @Override
		// public boolean verify(String verifyStr, String signStr) {
		// return false;
		// }
		// });
		// marshaller.analyse();
		// byte[] bytes = marshaller.marshal(req);
		// System.out.println("=========" + new String(bytes));

		OrderQueryRsp rsp = api.doSend(req, EnumSource.orderQuery);
		System.out.println(rsp);
	}

	/**
	 * 关闭订单
	 */
	@Test
	public void closeOrder(){
		CloseOrderReq req=new CloseOrderReq();
		req.setAppid("wx9955355c79974cc6");
		req.setMch_id("**********");
	    req.setOut_trade_no("20171120063010000010");
	    req.setSub_mch_id("********");
	    CloseOrderRsp rsp = api.doSend(req, EnumSource.closeOrder);
		System.out.println(rsp);
	}
	
	/**
	 * 申请退款
	 */
	@Test
	public void refundOrder(){
		ApplyRefundReq req=new ApplyRefundReq();
		req.setAppid("wx9955355c79974cc6");
		req.setMch_id("**********");
		req.setSub_mch_id("********");
		req.setOut_refund_no("*************");
		req.setOut_trade_no("20171120063010000010");
		//req.setRefund_account(refund_account);
		//req.setRefund_desc(refund_desc);
		req.setRefund_fee(1);
		req.setRefund_fee_type(EnumCurrency.valueOf("USD"));
		req.setTotal_fee(1);
		ApplyRefundRsp rsp = api.doSend(req, EnumSource.applyRefund);
		System.out.println(rsp);
	}
	
	/**
	 * 退款查询
	 */
	@Test
	public void refundQuery(){
		RefundQueryReq req=new RefundQueryReq();
		req.setAppid("wx9955355c79974cc6");
		req.setMch_id("**********");
		req.setOut_refund_no("*************");
		req.setOut_trade_no("20171120063010000010");
		req.setSub_mch_id("********");
		RefundQueryRsp rsp = api.doSend(req, EnumSource.refundQuery);
		System.out.println(rsp);
	}
	
	/**
	 * 下载对账文件
	 */
	@Test
	public void downLoadBill() throws Exception {
		DownloadBillReq req=new DownloadBillReq();
		req.setAppid("wx9955355c79974cc6");
		req.setBill_date("********");
		req.setBill_type(EnumBillType.ALL);
		req.setMch_id("**********");
		req.setSub_mch_id("********");
		DownloadBillRsp rsp = api.doSend(req, EnumSource.downloadBill);
		System.out.println(rsp);

		String resResult = rsp.getReturn_result();
		Map<String, String> resMap = new HashMap<String, String>();
		if(resResult.contains("xml") && resResult.contains("return_code")) {
			Document document = DocumentHelper.parseText(resResult);
			Element root = document.getRootElement();
			Iterator<Element> iterator = root.elementIterator();
			while(iterator.hasNext()) {
				Element item = iterator.next();
				resMap.put(item.getName(), item.getStringValue());
			}
		}
	}

	@Test
	public void cc() throws UnsupportedEncodingException {
		String url = "http://bestpay.bestmpay.com/aggregation/doGet?mch_id=64983650";
		System.out.println(java.net.URLEncoder.encode(url, "utf-8"));
	}

	/**
	 * 商户结算信息查询
	 */
	@Test
	public void settlementQuery() throws Exception {
		SettlementQueryReq settlementQueryReq = new SettlementQueryReq();
		settlementQueryReq.setAppid("wx9955355c79974cc6");
		settlementQueryReq.setMch_id("**********");
		settlementQueryReq.setSub_mch_id("77875930");
		settlementQueryReq.setUsetag(2);
		settlementQueryReq.setOffset(20);
		settlementQueryReq.setLimit(10);
		settlementQueryReq.setDate_start("20171207");
		settlementQueryReq.setDate_end("20171208");
		SettlementQueryRsp rsp = api.doSend(settlementQueryReq, EnumSource.settlementQuery);
		System.out.println(rsp);

		String returnData = rsp.getReturn_data();
		Document document = DocumentHelper.parseText(returnData);
		Element root = document.getRootElement();
		String return_code = root.element("return_code").getStringValue();
		String result_code = root.element("result_code").getStringValue();
		Integer record_num = Integer.valueOf(root.element("record_num").getStringValue());
		if(!("SUCCESS".equals(return_code) && "SUCCESS".equals(result_code))) {
			System.out.println("---------银行处理失败---------");
			return;
		}
		if(record_num == null || record_num == 0) {
			System.out.println("---------银行返回商户结算信息明细数目为0---------");
			return;
		}
		List<Object> settleDetailList = new ArrayList<>();
		for(int n = 0; n < record_num; n++) {
			Element setteinfo = root.element("setteinfo_" + n);
			Class destClazz = SettlementQueryRsp.WeChatStlDetail.class;
			Object destObject = destClazz.newInstance();
			Field[] declaredFields = destClazz.getDeclaredFields();
			for(Field field : declaredFields) {
				Element setteDetail = setteinfo.element(field.getName());
				ReflectionUtils.setField(field, destObject, setteDetail.getStringValue());
			}
			settleDetailList.add(destObject);
		}
		System.out.println("---------处理结果---------" + settleDetailList.size());
	}

}
