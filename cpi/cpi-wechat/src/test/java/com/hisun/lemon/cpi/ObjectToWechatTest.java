package com.hisun.lemon.cpi;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import javax.annotation.Resource;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.hisun.channel.common.core.Signature;
import com.hisun.channel.parse.AbstractAnalyserAdaptee;
import com.hisun.channel.parse.Marshaller;
import com.hisun.channel.parse.MarshallerFactory;
import com.hisun.lemon.cpi.wechat.ebankpay.HeadReq;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumCurrency;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumInfCode;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumSource;
import com.hisun.lemon.cpi.wechat.ebankpay.WEIXINEnumCommon.EnumTradeType;
import com.hisun.lemon.cpi.wechat.ebankpay.WeChatApi;
import com.hisun.lemon.cpi.wechat.ebankpay.req.OfflinePaymentReq;
import com.hisun.lemon.cpi.wechat.ebankpay.req.OfflinePaymentReq.OfflinePaymentTranReq;
import com.hisun.lemon.cpi.wechat.ebankpay.req.RefundReq;
import com.hisun.lemon.cpi.wechat.ebankpay.req.RevokeReq;
import com.hisun.lemon.cpi.wechat.ebankpay.req.RevokeReq.RevokeTranReq;
import com.hisun.lemon.cpi.wechat.ebankpay.req.TradeStatusQueryReq;
import com.hisun.lemon.cpi.wechat.ebankpay.req.TradeStatusQueryReq.TradeStatusQueryTranReq;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.OfflinePaymentRsp;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.RefundRsp;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.RevokeRsp;
import com.hisun.lemon.cpi.wechat.ebankpay.rsp.TradeStatusQueryRsp;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ObjectToWechatTest {
	LocalDateTime localDate = LocalDateTime.now();
	DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyyMMdd");
	DateTimeFormatter formatterTime = DateTimeFormatter.ofPattern("HHmmss");

	@Resource
	private WeChatApi api;
	
	@Test
	@Ignore
	public void offlinePaymentTest() {
		HeadReq headReq = new HeadReq();
		headReq.setAesappId("********");
		headReq.setAescusId("020J02348120001");
		headReq.setInfCode(EnumInfCode.OFFLINEPAYMENT);

		OfflinePaymentReq.OfflinePaymentTranReq offlinePaymentTranReq = new OfflinePaymentTranReq();
		offlinePaymentTranReq.setAuthCode("135171465074578830");
		offlinePaymentTranReq.setBody("商品1");
		offlinePaymentTranReq.setCurrency(EnumCurrency.USD);
		offlinePaymentTranReq.setCusId("020J02348120001");
		offlinePaymentTranReq.setReqsn("***********");
		offlinePaymentTranReq.setTrxamt(1);

		OfflinePaymentReq req = new OfflinePaymentReq();
		req.setHeadReq(headReq);
		req.setOfflinePaymentTranReq(offlinePaymentTranReq);

		// Marshaller<OfflinePaymentReq> marshaller =
		// MarshallerFactory.getMarshaller(OfflinePaymentReq.class, false);
		// ((AbstractAnalyserAdaptee<?>) marshaller).setSignature(new
		// Signature() {
		//
		// @Override
		// public String sign(String signStr) {
		// return WEIXINSecurity.signature(signStr);
		// }
		//
		// @Override
		// public boolean verify(String verifyStr, String signStr) {
		// return false;
		// }
		// });
		// marshaller.analyse();
		// byte[] bytes = marshaller.marshal(req);
		// System.out.println("=========" + new String(bytes));
		OfflinePaymentRsp offlinePaymentRsp = api.doSend(req, EnumSource.payment);

		System.out.println(offlinePaymentRsp);
	}

	@Test
	@Ignore
	public void revokeTest() {
		HeadReq headReq = new HeadReq();
		headReq.setAesappId("********");
		headReq.setAescusId("020J02348120001");
		headReq.setInfCode(EnumInfCode.REVOKE);

		RevokeReq.RevokeTranReq revokeTranReq = new RevokeTranReq();
		revokeTranReq.setCusId("020J02348120001");
		revokeTranReq.setOldtrxId("12399997");

		RevokeReq req = new RevokeReq();
		req.setHeadReq(headReq);
		req.setRevokeTranReq(revokeTranReq);

		Marshaller<RevokeReq> marshaller = MarshallerFactory.getMarshaller(RevokeReq.class, false);
		((AbstractAnalyserAdaptee<?>) marshaller).setSignature(new Signature() {

			@Override
			public String sign(String signStr) {
				return null;
			}

			@Override
			public boolean verify(String verifyStr, String signStr) {
				return false;
			}
		});
		marshaller.analyse();
		byte[] bytes = marshaller.marshal(req);
		System.out.println("=========" + new String(bytes));

		RevokeRsp rsp = api.doSend(req, EnumSource.revoke);
		System.out.println(rsp);
	}

	@Test
	@Ignore
	public void refundTest() {

		HeadReq headReq = new HeadReq();
		headReq.setAesappId("********");
		headReq.setAescusId("020J02348120001");
		headReq.setInfCode(EnumInfCode.REFUND);

		RefundReq.RefundTranReq refundTranReq = new RefundReq.RefundTranReq();
		refundTranReq.setCusId("020J02348120001");
		refundTranReq.setOldtrxId("20160906002");

		RefundReq req = new RefundReq();
		req.setHeadReq(headReq);
		req.setRefundTranReq(refundTranReq);

		Marshaller<RefundReq> marshaller = MarshallerFactory.getMarshaller(RefundReq.class, false);
		((AbstractAnalyserAdaptee<?>) marshaller).setSignature(new Signature() {

			@Override
			public String sign(String signStr) {
				return null;
			}

			@Override
			public boolean verify(String verifyStr, String signStr) {
				return false;
			}
		});
		marshaller.analyse();
		byte[] bytes = marshaller.marshal(req);
		System.out.println("=========" + new String(bytes));

		RefundRsp rsp = api.doSend(req, EnumSource.refund);
		System.out.println(rsp);
	}

	@Test
	@Ignore
	public void tradeStatusQuery(){
		HeadReq headReq = new HeadReq();
		headReq.setAesappId("********");
		headReq.setAescusId("020J02348120001");
		headReq.setInfCode(EnumInfCode.STATUSQUERY);
		
		TradeStatusQueryReq.TradeStatusQueryTranReq tradeStatusQueryTranReq=new TradeStatusQueryTranReq();
		tradeStatusQueryTranReq.setCusId("020J02348120001");
		tradeStatusQueryTranReq.setPayReqSn("***********");
		tradeStatusQueryTranReq.setPayTrxId("20160906002");
		tradeStatusQueryTranReq.setTrxCode(EnumTradeType.VSP503);
	
		TradeStatusQueryReq req=new TradeStatusQueryReq();
		req.setHeadReq(headReq);
		req.setTradeStatusQueryTranReq(tradeStatusQueryTranReq);
		
		Marshaller<TradeStatusQueryReq> marshaller = MarshallerFactory.getMarshaller(TradeStatusQueryReq.class, false);
		((AbstractAnalyserAdaptee<?>) marshaller).setSignature(new Signature() {

			@Override
			public String sign(String signStr) {
				return null;
			}

			@Override
			public boolean verify(String verifyStr, String signStr) {
				return false;
			}
		});
		marshaller.analyse();
		byte[] bytes = marshaller.marshal(req);
		System.out.println("=========" + new String(bytes));
		
		TradeStatusQueryRsp rsp = api.doSend(req, EnumSource.tradestatusquery);
		System.out.println(rsp);
	}
}
